// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart

// class id: 1049439, size: 0x8
class :: {
}

// class id: 3311, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb884ac, size: 0x64
    // 0xb884ac: EnterFrame
    //     0xb884ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb884b0: mov             fp, SP
    // 0xb884b4: AllocStack(0x18)
    //     0xb884b4: sub             SP, SP, #0x18
    // 0xb884b8: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb884b8: stur            x1, [fp, #-8]
    //     0xb884bc: stur            x2, [fp, #-0x10]
    // 0xb884c0: r1 = 2
    //     0xb884c0: movz            x1, #0x2
    // 0xb884c4: r0 = AllocateContext()
    //     0xb884c4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb884c8: mov             x1, x0
    // 0xb884cc: ldur            x0, [fp, #-8]
    // 0xb884d0: stur            x1, [fp, #-0x18]
    // 0xb884d4: StoreField: r1->field_f = r0
    //     0xb884d4: stur            w0, [x1, #0xf]
    // 0xb884d8: ldur            x0, [fp, #-0x10]
    // 0xb884dc: StoreField: r1->field_13 = r0
    //     0xb884dc: stur            w0, [x1, #0x13]
    // 0xb884e0: r0 = Obx()
    //     0xb884e0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb884e4: ldur            x2, [fp, #-0x18]
    // 0xb884e8: r1 = Function '<anonymous closure>':.
    //     0xb884e8: add             x1, PP, #0x55, lsl #12  ; [pp+0x554f0] AnonymousClosure: (0xb88530), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb884ac)
    //     0xb884ec: ldr             x1, [x1, #0x4f0]
    // 0xb884f0: stur            x0, [fp, #-8]
    // 0xb884f4: r0 = AllocateClosure()
    //     0xb884f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb884f8: mov             x1, x0
    // 0xb884fc: ldur            x0, [fp, #-8]
    // 0xb88500: StoreField: r0->field_b = r1
    //     0xb88500: stur            w1, [x0, #0xb]
    // 0xb88504: LeaveFrame
    //     0xb88504: mov             SP, fp
    //     0xb88508: ldp             fp, lr, [SP], #0x10
    // 0xb8850c: ret
    //     0xb8850c: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xb88530, size: 0x830
    // 0xb88530: EnterFrame
    //     0xb88530: stp             fp, lr, [SP, #-0x10]!
    //     0xb88534: mov             fp, SP
    // 0xb88538: AllocStack(0x78)
    //     0xb88538: sub             SP, SP, #0x78
    // 0xb8853c: SetupParameters()
    //     0xb8853c: ldr             x0, [fp, #0x10]
    //     0xb88540: ldur            w3, [x0, #0x17]
    //     0xb88544: add             x3, x3, HEAP, lsl #32
    //     0xb88548: stur            x3, [fp, #-0x10]
    // 0xb8854c: CheckStackOverflow
    //     0xb8854c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88550: cmp             SP, x16
    //     0xb88554: b.ls            #0xb88d24
    // 0xb88558: LoadField: r0 = r3->field_f
    //     0xb88558: ldur            w0, [x3, #0xf]
    // 0xb8855c: DecompressPointer r0
    //     0xb8855c: add             x0, x0, HEAP, lsl #32
    // 0xb88560: LoadField: r1 = r0->field_b
    //     0xb88560: ldur            w1, [x0, #0xb]
    // 0xb88564: DecompressPointer r1
    //     0xb88564: add             x1, x1, HEAP, lsl #32
    // 0xb88568: cmp             w1, NULL
    // 0xb8856c: b.eq            #0xb88d2c
    // 0xb88570: LoadField: r0 = r1->field_13
    //     0xb88570: ldur            w0, [x1, #0x13]
    // 0xb88574: DecompressPointer r0
    //     0xb88574: add             x0, x0, HEAP, lsl #32
    // 0xb88578: LoadField: r1 = r0->field_7
    //     0xb88578: ldur            w1, [x0, #7]
    // 0xb8857c: DecompressPointer r1
    //     0xb8857c: add             x1, x1, HEAP, lsl #32
    // 0xb88580: cmp             w1, NULL
    // 0xb88584: b.ne            #0xb88594
    // 0xb88588: r0 = Instance_TitleAlignment
    //     0xb88588: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb8858c: ldr             x0, [x0, #0x518]
    // 0xb88590: b               #0xb88598
    // 0xb88594: mov             x0, x1
    // 0xb88598: r16 = Instance_TitleAlignment
    //     0xb88598: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb8859c: ldr             x16, [x16, #0x520]
    // 0xb885a0: cmp             w0, w16
    // 0xb885a4: b.ne            #0xb885b4
    // 0xb885a8: r0 = Instance_CrossAxisAlignment
    //     0xb885a8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb885ac: ldr             x0, [x0, #0xc68]
    // 0xb885b0: b               #0xb885d8
    // 0xb885b4: r16 = Instance_TitleAlignment
    //     0xb885b4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb885b8: ldr             x16, [x16, #0x518]
    // 0xb885bc: cmp             w0, w16
    // 0xb885c0: b.ne            #0xb885d0
    // 0xb885c4: r0 = Instance_CrossAxisAlignment
    //     0xb885c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb885c8: ldr             x0, [x0, #0x890]
    // 0xb885cc: b               #0xb885d8
    // 0xb885d0: r0 = Instance_CrossAxisAlignment
    //     0xb885d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb885d4: ldr             x0, [x0, #0xa18]
    // 0xb885d8: stur            x0, [fp, #-8]
    // 0xb885dc: r1 = <Widget>
    //     0xb885dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb885e0: r2 = 0
    //     0xb885e0: movz            x2, #0
    // 0xb885e4: r0 = _GrowableList()
    //     0xb885e4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb885e8: ldur            x2, [fp, #-0x10]
    // 0xb885ec: stur            x0, [fp, #-0x18]
    // 0xb885f0: LoadField: r1 = r2->field_f
    //     0xb885f0: ldur            w1, [x2, #0xf]
    // 0xb885f4: DecompressPointer r1
    //     0xb885f4: add             x1, x1, HEAP, lsl #32
    // 0xb885f8: LoadField: r3 = r1->field_b
    //     0xb885f8: ldur            w3, [x1, #0xb]
    // 0xb885fc: DecompressPointer r3
    //     0xb885fc: add             x3, x3, HEAP, lsl #32
    // 0xb88600: cmp             w3, NULL
    // 0xb88604: b.eq            #0xb88d30
    // 0xb88608: LoadField: r1 = r3->field_f
    //     0xb88608: ldur            w1, [x3, #0xf]
    // 0xb8860c: DecompressPointer r1
    //     0xb8860c: add             x1, x1, HEAP, lsl #32
    // 0xb88610: LoadField: r3 = r1->field_7
    //     0xb88610: ldur            w3, [x1, #7]
    // 0xb88614: cbz             w3, #0xb887a0
    // 0xb88618: r0 = capitalizeFirstWord()
    //     0xb88618: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb8861c: ldur            x2, [fp, #-0x10]
    // 0xb88620: stur            x0, [fp, #-0x28]
    // 0xb88624: LoadField: r1 = r2->field_f
    //     0xb88624: ldur            w1, [x2, #0xf]
    // 0xb88628: DecompressPointer r1
    //     0xb88628: add             x1, x1, HEAP, lsl #32
    // 0xb8862c: LoadField: r3 = r1->field_b
    //     0xb8862c: ldur            w3, [x1, #0xb]
    // 0xb88630: DecompressPointer r3
    //     0xb88630: add             x3, x3, HEAP, lsl #32
    // 0xb88634: cmp             w3, NULL
    // 0xb88638: b.eq            #0xb88d34
    // 0xb8863c: LoadField: r1 = r3->field_13
    //     0xb8863c: ldur            w1, [x3, #0x13]
    // 0xb88640: DecompressPointer r1
    //     0xb88640: add             x1, x1, HEAP, lsl #32
    // 0xb88644: LoadField: r3 = r1->field_7
    //     0xb88644: ldur            w3, [x1, #7]
    // 0xb88648: DecompressPointer r3
    //     0xb88648: add             x3, x3, HEAP, lsl #32
    // 0xb8864c: cmp             w3, NULL
    // 0xb88650: b.ne            #0xb88660
    // 0xb88654: r1 = Instance_TitleAlignment
    //     0xb88654: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb88658: ldr             x1, [x1, #0x518]
    // 0xb8865c: b               #0xb88664
    // 0xb88660: mov             x1, x3
    // 0xb88664: r16 = Instance_TitleAlignment
    //     0xb88664: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb88668: ldr             x16, [x16, #0x520]
    // 0xb8866c: cmp             w1, w16
    // 0xb88670: b.ne            #0xb8867c
    // 0xb88674: r4 = Instance_TextAlign
    //     0xb88674: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb88678: b               #0xb88698
    // 0xb8867c: r16 = Instance_TitleAlignment
    //     0xb8867c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb88680: ldr             x16, [x16, #0x518]
    // 0xb88684: cmp             w1, w16
    // 0xb88688: b.ne            #0xb88694
    // 0xb8868c: r4 = Instance_TextAlign
    //     0xb8868c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb88690: b               #0xb88698
    // 0xb88694: r4 = Instance_TextAlign
    //     0xb88694: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb88698: ldur            x3, [fp, #-0x18]
    // 0xb8869c: stur            x4, [fp, #-0x20]
    // 0xb886a0: LoadField: r1 = r2->field_13
    //     0xb886a0: ldur            w1, [x2, #0x13]
    // 0xb886a4: DecompressPointer r1
    //     0xb886a4: add             x1, x1, HEAP, lsl #32
    // 0xb886a8: r0 = of()
    //     0xb886a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb886ac: LoadField: r1 = r0->field_87
    //     0xb886ac: ldur            w1, [x0, #0x87]
    // 0xb886b0: DecompressPointer r1
    //     0xb886b0: add             x1, x1, HEAP, lsl #32
    // 0xb886b4: LoadField: r0 = r1->field_7
    //     0xb886b4: ldur            w0, [x1, #7]
    // 0xb886b8: DecompressPointer r0
    //     0xb886b8: add             x0, x0, HEAP, lsl #32
    // 0xb886bc: r16 = Instance_Color
    //     0xb886bc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb886c0: r30 = 32.000000
    //     0xb886c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb886c4: ldr             lr, [lr, #0x848]
    // 0xb886c8: stp             lr, x16, [SP]
    // 0xb886cc: mov             x1, x0
    // 0xb886d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb886d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb886d4: ldr             x4, [x4, #0x9b8]
    // 0xb886d8: r0 = copyWith()
    //     0xb886d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb886dc: stur            x0, [fp, #-0x30]
    // 0xb886e0: r0 = Text()
    //     0xb886e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb886e4: mov             x1, x0
    // 0xb886e8: ldur            x0, [fp, #-0x28]
    // 0xb886ec: stur            x1, [fp, #-0x38]
    // 0xb886f0: StoreField: r1->field_b = r0
    //     0xb886f0: stur            w0, [x1, #0xb]
    // 0xb886f4: ldur            x0, [fp, #-0x30]
    // 0xb886f8: StoreField: r1->field_13 = r0
    //     0xb886f8: stur            w0, [x1, #0x13]
    // 0xb886fc: ldur            x0, [fp, #-0x20]
    // 0xb88700: StoreField: r1->field_1b = r0
    //     0xb88700: stur            w0, [x1, #0x1b]
    // 0xb88704: r0 = Padding()
    //     0xb88704: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb88708: mov             x2, x0
    // 0xb8870c: r0 = Instance_EdgeInsets
    //     0xb8870c: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb88710: ldr             x0, [x0, #0x78]
    // 0xb88714: stur            x2, [fp, #-0x20]
    // 0xb88718: StoreField: r2->field_f = r0
    //     0xb88718: stur            w0, [x2, #0xf]
    // 0xb8871c: ldur            x0, [fp, #-0x38]
    // 0xb88720: StoreField: r2->field_b = r0
    //     0xb88720: stur            w0, [x2, #0xb]
    // 0xb88724: ldur            x0, [fp, #-0x18]
    // 0xb88728: LoadField: r1 = r0->field_b
    //     0xb88728: ldur            w1, [x0, #0xb]
    // 0xb8872c: LoadField: r3 = r0->field_f
    //     0xb8872c: ldur            w3, [x0, #0xf]
    // 0xb88730: DecompressPointer r3
    //     0xb88730: add             x3, x3, HEAP, lsl #32
    // 0xb88734: LoadField: r4 = r3->field_b
    //     0xb88734: ldur            w4, [x3, #0xb]
    // 0xb88738: r3 = LoadInt32Instr(r1)
    //     0xb88738: sbfx            x3, x1, #1, #0x1f
    // 0xb8873c: stur            x3, [fp, #-0x40]
    // 0xb88740: r1 = LoadInt32Instr(r4)
    //     0xb88740: sbfx            x1, x4, #1, #0x1f
    // 0xb88744: cmp             x3, x1
    // 0xb88748: b.ne            #0xb88754
    // 0xb8874c: mov             x1, x0
    // 0xb88750: r0 = _growToNextCapacity()
    //     0xb88750: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb88754: ldur            x3, [fp, #-0x18]
    // 0xb88758: ldur            x2, [fp, #-0x40]
    // 0xb8875c: add             x0, x2, #1
    // 0xb88760: lsl             x1, x0, #1
    // 0xb88764: StoreField: r3->field_b = r1
    //     0xb88764: stur            w1, [x3, #0xb]
    // 0xb88768: LoadField: r1 = r3->field_f
    //     0xb88768: ldur            w1, [x3, #0xf]
    // 0xb8876c: DecompressPointer r1
    //     0xb8876c: add             x1, x1, HEAP, lsl #32
    // 0xb88770: ldur            x0, [fp, #-0x20]
    // 0xb88774: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb88774: add             x25, x1, x2, lsl #2
    //     0xb88778: add             x25, x25, #0xf
    //     0xb8877c: str             w0, [x25]
    //     0xb88780: tbz             w0, #0, #0xb8879c
    //     0xb88784: ldurb           w16, [x1, #-1]
    //     0xb88788: ldurb           w17, [x0, #-1]
    //     0xb8878c: and             x16, x17, x16, lsr #2
    //     0xb88790: tst             x16, HEAP, lsr #32
    //     0xb88794: b.eq            #0xb8879c
    //     0xb88798: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8879c: b               #0xb887a4
    // 0xb887a0: mov             x3, x0
    // 0xb887a4: ldur            x0, [fp, #-0x10]
    // 0xb887a8: LoadField: r1 = r0->field_f
    //     0xb887a8: ldur            w1, [x0, #0xf]
    // 0xb887ac: DecompressPointer r1
    //     0xb887ac: add             x1, x1, HEAP, lsl #32
    // 0xb887b0: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xb887b0: ldur            x2, [x1, #0x17]
    // 0xb887b4: r0 = _calculateCardHeight()
    //     0xb887b4: bl              #0xa87490  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xb887b8: ldur            x2, [fp, #-0x10]
    // 0xb887bc: stur            d0, [fp, #-0x58]
    // 0xb887c0: LoadField: r0 = r2->field_f
    //     0xb887c0: ldur            w0, [x2, #0xf]
    // 0xb887c4: DecompressPointer r0
    //     0xb887c4: add             x0, x0, HEAP, lsl #32
    // 0xb887c8: LoadField: r1 = r0->field_b
    //     0xb887c8: ldur            w1, [x0, #0xb]
    // 0xb887cc: DecompressPointer r1
    //     0xb887cc: add             x1, x1, HEAP, lsl #32
    // 0xb887d0: cmp             w1, NULL
    // 0xb887d4: b.eq            #0xb88d38
    // 0xb887d8: LoadField: r0 = r1->field_b
    //     0xb887d8: ldur            w0, [x1, #0xb]
    // 0xb887dc: DecompressPointer r0
    //     0xb887dc: add             x0, x0, HEAP, lsl #32
    // 0xb887e0: r1 = LoadClassIdInstr(r0)
    //     0xb887e0: ldur            x1, [x0, #-1]
    //     0xb887e4: ubfx            x1, x1, #0xc, #0x14
    // 0xb887e8: str             x0, [SP]
    // 0xb887ec: mov             x0, x1
    // 0xb887f0: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb887f0: movz            x17, #0xc898
    //     0xb887f4: add             lr, x0, x17
    //     0xb887f8: ldr             lr, [x21, lr, lsl #3]
    //     0xb887fc: blr             lr
    // 0xb88800: mov             x3, x0
    // 0xb88804: ldur            x0, [fp, #-0x10]
    // 0xb88808: stur            x3, [fp, #-0x28]
    // 0xb8880c: LoadField: r1 = r0->field_f
    //     0xb8880c: ldur            w1, [x0, #0xf]
    // 0xb88810: DecompressPointer r1
    //     0xb88810: add             x1, x1, HEAP, lsl #32
    // 0xb88814: LoadField: r4 = r1->field_13
    //     0xb88814: ldur            w4, [x1, #0x13]
    // 0xb88818: DecompressPointer r4
    //     0xb88818: add             x4, x4, HEAP, lsl #32
    // 0xb8881c: r16 = Sentinel
    //     0xb8881c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb88820: cmp             w4, w16
    // 0xb88824: b.eq            #0xb88d3c
    // 0xb88828: mov             x2, x0
    // 0xb8882c: stur            x4, [fp, #-0x20]
    // 0xb88830: r1 = Function '<anonymous closure>':.
    //     0xb88830: add             x1, PP, #0x55, lsl #12  ; [pp+0x554f8] AnonymousClosure: (0xb89e98), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb884ac)
    //     0xb88834: ldr             x1, [x1, #0x4f8]
    // 0xb88838: r0 = AllocateClosure()
    //     0xb88838: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8883c: ldur            x2, [fp, #-0x10]
    // 0xb88840: r1 = Function '<anonymous closure>':.
    //     0xb88840: add             x1, PP, #0x55, lsl #12  ; [pp+0x55500] AnonymousClosure: (0xb88f18), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb884ac)
    //     0xb88844: ldr             x1, [x1, #0x500]
    // 0xb88848: stur            x0, [fp, #-0x30]
    // 0xb8884c: r0 = AllocateClosure()
    //     0xb8884c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb88850: stur            x0, [fp, #-0x38]
    // 0xb88854: r0 = PageView()
    //     0xb88854: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb88858: stur            x0, [fp, #-0x48]
    // 0xb8885c: r16 = Instance_BouncingScrollPhysics
    //     0xb8885c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb88860: ldr             x16, [x16, #0x890]
    // 0xb88864: ldur            lr, [fp, #-0x20]
    // 0xb88868: stp             lr, x16, [SP]
    // 0xb8886c: mov             x1, x0
    // 0xb88870: ldur            x2, [fp, #-0x38]
    // 0xb88874: ldur            x3, [fp, #-0x28]
    // 0xb88878: ldur            x5, [fp, #-0x30]
    // 0xb8887c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb8887c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb88880: ldr             x4, [x4, #0xe40]
    // 0xb88884: r0 = PageView.builder()
    //     0xb88884: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb88888: ldur            d0, [fp, #-0x58]
    // 0xb8888c: r0 = inline_Allocate_Double()
    //     0xb8888c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb88890: add             x0, x0, #0x10
    //     0xb88894: cmp             x1, x0
    //     0xb88898: b.ls            #0xb88d48
    //     0xb8889c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb888a0: sub             x0, x0, #0xf
    //     0xb888a4: movz            x1, #0xe15c
    //     0xb888a8: movk            x1, #0x3, lsl #16
    //     0xb888ac: stur            x1, [x0, #-1]
    // 0xb888b0: StoreField: r0->field_7 = d0
    //     0xb888b0: stur            d0, [x0, #7]
    // 0xb888b4: stur            x0, [fp, #-0x20]
    // 0xb888b8: r0 = SizedBox()
    //     0xb888b8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb888bc: mov             x2, x0
    // 0xb888c0: ldur            x0, [fp, #-0x20]
    // 0xb888c4: stur            x2, [fp, #-0x28]
    // 0xb888c8: StoreField: r2->field_13 = r0
    //     0xb888c8: stur            w0, [x2, #0x13]
    // 0xb888cc: ldur            x0, [fp, #-0x48]
    // 0xb888d0: StoreField: r2->field_b = r0
    //     0xb888d0: stur            w0, [x2, #0xb]
    // 0xb888d4: ldur            x0, [fp, #-0x18]
    // 0xb888d8: LoadField: r1 = r0->field_b
    //     0xb888d8: ldur            w1, [x0, #0xb]
    // 0xb888dc: LoadField: r3 = r0->field_f
    //     0xb888dc: ldur            w3, [x0, #0xf]
    // 0xb888e0: DecompressPointer r3
    //     0xb888e0: add             x3, x3, HEAP, lsl #32
    // 0xb888e4: LoadField: r4 = r3->field_b
    //     0xb888e4: ldur            w4, [x3, #0xb]
    // 0xb888e8: r3 = LoadInt32Instr(r1)
    //     0xb888e8: sbfx            x3, x1, #1, #0x1f
    // 0xb888ec: stur            x3, [fp, #-0x40]
    // 0xb888f0: r1 = LoadInt32Instr(r4)
    //     0xb888f0: sbfx            x1, x4, #1, #0x1f
    // 0xb888f4: cmp             x3, x1
    // 0xb888f8: b.ne            #0xb88904
    // 0xb888fc: mov             x1, x0
    // 0xb88900: r0 = _growToNextCapacity()
    //     0xb88900: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb88904: ldur            x4, [fp, #-0x10]
    // 0xb88908: ldur            x2, [fp, #-0x18]
    // 0xb8890c: ldur            x3, [fp, #-0x40]
    // 0xb88910: add             x0, x3, #1
    // 0xb88914: lsl             x1, x0, #1
    // 0xb88918: StoreField: r2->field_b = r1
    //     0xb88918: stur            w1, [x2, #0xb]
    // 0xb8891c: LoadField: r1 = r2->field_f
    //     0xb8891c: ldur            w1, [x2, #0xf]
    // 0xb88920: DecompressPointer r1
    //     0xb88920: add             x1, x1, HEAP, lsl #32
    // 0xb88924: ldur            x0, [fp, #-0x28]
    // 0xb88928: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb88928: add             x25, x1, x3, lsl #2
    //     0xb8892c: add             x25, x25, #0xf
    //     0xb88930: str             w0, [x25]
    //     0xb88934: tbz             w0, #0, #0xb88950
    //     0xb88938: ldurb           w16, [x1, #-1]
    //     0xb8893c: ldurb           w17, [x0, #-1]
    //     0xb88940: and             x16, x17, x16, lsr #2
    //     0xb88944: tst             x16, HEAP, lsr #32
    //     0xb88948: b.eq            #0xb88950
    //     0xb8894c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb88950: LoadField: r0 = r4->field_f
    //     0xb88950: ldur            w0, [x4, #0xf]
    // 0xb88954: DecompressPointer r0
    //     0xb88954: add             x0, x0, HEAP, lsl #32
    // 0xb88958: LoadField: r1 = r0->field_b
    //     0xb88958: ldur            w1, [x0, #0xb]
    // 0xb8895c: DecompressPointer r1
    //     0xb8895c: add             x1, x1, HEAP, lsl #32
    // 0xb88960: cmp             w1, NULL
    // 0xb88964: b.eq            #0xb88d58
    // 0xb88968: LoadField: r0 = r1->field_b
    //     0xb88968: ldur            w0, [x1, #0xb]
    // 0xb8896c: DecompressPointer r0
    //     0xb8896c: add             x0, x0, HEAP, lsl #32
    // 0xb88970: r1 = LoadClassIdInstr(r0)
    //     0xb88970: ldur            x1, [x0, #-1]
    //     0xb88974: ubfx            x1, x1, #0xc, #0x14
    // 0xb88978: str             x0, [SP]
    // 0xb8897c: mov             x0, x1
    // 0xb88980: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb88980: movz            x17, #0xc898
    //     0xb88984: add             lr, x0, x17
    //     0xb88988: ldr             lr, [x21, lr, lsl #3]
    //     0xb8898c: blr             lr
    // 0xb88990: r1 = LoadInt32Instr(r0)
    //     0xb88990: sbfx            x1, x0, #1, #0x1f
    // 0xb88994: cmp             x1, #1
    // 0xb88998: b.le            #0xb88acc
    // 0xb8899c: ldur            x2, [fp, #-0x10]
    // 0xb889a0: ldur            x1, [fp, #-0x18]
    // 0xb889a4: LoadField: r0 = r2->field_f
    //     0xb889a4: ldur            w0, [x2, #0xf]
    // 0xb889a8: DecompressPointer r0
    //     0xb889a8: add             x0, x0, HEAP, lsl #32
    // 0xb889ac: LoadField: r3 = r0->field_b
    //     0xb889ac: ldur            w3, [x0, #0xb]
    // 0xb889b0: DecompressPointer r3
    //     0xb889b0: add             x3, x3, HEAP, lsl #32
    // 0xb889b4: cmp             w3, NULL
    // 0xb889b8: b.eq            #0xb88d5c
    // 0xb889bc: LoadField: r0 = r3->field_b
    //     0xb889bc: ldur            w0, [x3, #0xb]
    // 0xb889c0: DecompressPointer r0
    //     0xb889c0: add             x0, x0, HEAP, lsl #32
    // 0xb889c4: r3 = LoadClassIdInstr(r0)
    //     0xb889c4: ldur            x3, [x0, #-1]
    //     0xb889c8: ubfx            x3, x3, #0xc, #0x14
    // 0xb889cc: str             x0, [SP]
    // 0xb889d0: mov             x0, x3
    // 0xb889d4: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb889d4: movz            x17, #0xc898
    //     0xb889d8: add             lr, x0, x17
    //     0xb889dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb889e0: blr             lr
    // 0xb889e4: ldur            x2, [fp, #-0x10]
    // 0xb889e8: stur            x0, [fp, #-0x20]
    // 0xb889ec: LoadField: r1 = r2->field_f
    //     0xb889ec: ldur            w1, [x2, #0xf]
    // 0xb889f0: DecompressPointer r1
    //     0xb889f0: add             x1, x1, HEAP, lsl #32
    // 0xb889f4: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xb889f4: ldur            x3, [x1, #0x17]
    // 0xb889f8: stur            x3, [fp, #-0x40]
    // 0xb889fc: LoadField: r1 = r2->field_13
    //     0xb889fc: ldur            w1, [x2, #0x13]
    // 0xb88a00: DecompressPointer r1
    //     0xb88a00: add             x1, x1, HEAP, lsl #32
    // 0xb88a04: r0 = of()
    //     0xb88a04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb88a08: LoadField: r1 = r0->field_5b
    //     0xb88a08: ldur            w1, [x0, #0x5b]
    // 0xb88a0c: DecompressPointer r1
    //     0xb88a0c: add             x1, x1, HEAP, lsl #32
    // 0xb88a10: ldur            x0, [fp, #-0x20]
    // 0xb88a14: stur            x1, [fp, #-0x28]
    // 0xb88a18: r2 = LoadInt32Instr(r0)
    //     0xb88a18: sbfx            x2, x0, #1, #0x1f
    // 0xb88a1c: stur            x2, [fp, #-0x50]
    // 0xb88a20: r0 = CarouselIndicator()
    //     0xb88a20: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb88a24: mov             x2, x0
    // 0xb88a28: ldur            x0, [fp, #-0x50]
    // 0xb88a2c: stur            x2, [fp, #-0x20]
    // 0xb88a30: StoreField: r2->field_b = r0
    //     0xb88a30: stur            x0, [x2, #0xb]
    // 0xb88a34: ldur            x0, [fp, #-0x40]
    // 0xb88a38: StoreField: r2->field_13 = r0
    //     0xb88a38: stur            x0, [x2, #0x13]
    // 0xb88a3c: ldur            x0, [fp, #-0x28]
    // 0xb88a40: StoreField: r2->field_1b = r0
    //     0xb88a40: stur            w0, [x2, #0x1b]
    // 0xb88a44: r0 = Instance_Color
    //     0xb88a44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb88a48: ldr             x0, [x0, #0x90]
    // 0xb88a4c: StoreField: r2->field_1f = r0
    //     0xb88a4c: stur            w0, [x2, #0x1f]
    // 0xb88a50: ldur            x0, [fp, #-0x18]
    // 0xb88a54: LoadField: r1 = r0->field_b
    //     0xb88a54: ldur            w1, [x0, #0xb]
    // 0xb88a58: LoadField: r3 = r0->field_f
    //     0xb88a58: ldur            w3, [x0, #0xf]
    // 0xb88a5c: DecompressPointer r3
    //     0xb88a5c: add             x3, x3, HEAP, lsl #32
    // 0xb88a60: LoadField: r4 = r3->field_b
    //     0xb88a60: ldur            w4, [x3, #0xb]
    // 0xb88a64: r3 = LoadInt32Instr(r1)
    //     0xb88a64: sbfx            x3, x1, #1, #0x1f
    // 0xb88a68: stur            x3, [fp, #-0x40]
    // 0xb88a6c: r1 = LoadInt32Instr(r4)
    //     0xb88a6c: sbfx            x1, x4, #1, #0x1f
    // 0xb88a70: cmp             x3, x1
    // 0xb88a74: b.ne            #0xb88a80
    // 0xb88a78: mov             x1, x0
    // 0xb88a7c: r0 = _growToNextCapacity()
    //     0xb88a7c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb88a80: ldur            x2, [fp, #-0x18]
    // 0xb88a84: ldur            x3, [fp, #-0x40]
    // 0xb88a88: add             x0, x3, #1
    // 0xb88a8c: lsl             x1, x0, #1
    // 0xb88a90: StoreField: r2->field_b = r1
    //     0xb88a90: stur            w1, [x2, #0xb]
    // 0xb88a94: LoadField: r1 = r2->field_f
    //     0xb88a94: ldur            w1, [x2, #0xf]
    // 0xb88a98: DecompressPointer r1
    //     0xb88a98: add             x1, x1, HEAP, lsl #32
    // 0xb88a9c: ldur            x0, [fp, #-0x20]
    // 0xb88aa0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb88aa0: add             x25, x1, x3, lsl #2
    //     0xb88aa4: add             x25, x25, #0xf
    //     0xb88aa8: str             w0, [x25]
    //     0xb88aac: tbz             w0, #0, #0xb88ac8
    //     0xb88ab0: ldurb           w16, [x1, #-1]
    //     0xb88ab4: ldurb           w17, [x0, #-1]
    //     0xb88ab8: and             x16, x17, x16, lsr #2
    //     0xb88abc: tst             x16, HEAP, lsr #32
    //     0xb88ac0: b.eq            #0xb88ac8
    //     0xb88ac4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb88ac8: b               #0xb88ad0
    // 0xb88acc: ldur            x2, [fp, #-0x18]
    // 0xb88ad0: ldur            x0, [fp, #-0x10]
    // 0xb88ad4: LoadField: r1 = r0->field_13
    //     0xb88ad4: ldur            w1, [x0, #0x13]
    // 0xb88ad8: DecompressPointer r1
    //     0xb88ad8: add             x1, x1, HEAP, lsl #32
    // 0xb88adc: r0 = of()
    //     0xb88adc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb88ae0: LoadField: r1 = r0->field_5b
    //     0xb88ae0: ldur            w1, [x0, #0x5b]
    // 0xb88ae4: DecompressPointer r1
    //     0xb88ae4: add             x1, x1, HEAP, lsl #32
    // 0xb88ae8: stur            x1, [fp, #-0x20]
    // 0xb88aec: r0 = BoxDecoration()
    //     0xb88aec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb88af0: mov             x2, x0
    // 0xb88af4: ldur            x0, [fp, #-0x20]
    // 0xb88af8: stur            x2, [fp, #-0x28]
    // 0xb88afc: StoreField: r2->field_7 = r0
    //     0xb88afc: stur            w0, [x2, #7]
    // 0xb88b00: r0 = Instance_BorderRadius
    //     0xb88b00: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb88b04: ldr             x0, [x0, #0x460]
    // 0xb88b08: StoreField: r2->field_13 = r0
    //     0xb88b08: stur            w0, [x2, #0x13]
    // 0xb88b0c: r0 = Instance_BoxShape
    //     0xb88b0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb88b10: ldr             x0, [x0, #0x80]
    // 0xb88b14: StoreField: r2->field_23 = r0
    //     0xb88b14: stur            w0, [x2, #0x23]
    // 0xb88b18: ldur            x3, [fp, #-0x10]
    // 0xb88b1c: LoadField: r1 = r3->field_13
    //     0xb88b1c: ldur            w1, [x3, #0x13]
    // 0xb88b20: DecompressPointer r1
    //     0xb88b20: add             x1, x1, HEAP, lsl #32
    // 0xb88b24: r0 = of()
    //     0xb88b24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb88b28: LoadField: r1 = r0->field_87
    //     0xb88b28: ldur            w1, [x0, #0x87]
    // 0xb88b2c: DecompressPointer r1
    //     0xb88b2c: add             x1, x1, HEAP, lsl #32
    // 0xb88b30: LoadField: r0 = r1->field_2b
    //     0xb88b30: ldur            w0, [x1, #0x2b]
    // 0xb88b34: DecompressPointer r0
    //     0xb88b34: add             x0, x0, HEAP, lsl #32
    // 0xb88b38: r16 = 16.000000
    //     0xb88b38: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb88b3c: ldr             x16, [x16, #0x188]
    // 0xb88b40: r30 = Instance_Color
    //     0xb88b40: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb88b44: stp             lr, x16, [SP]
    // 0xb88b48: mov             x1, x0
    // 0xb88b4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb88b4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb88b50: ldr             x4, [x4, #0xaa0]
    // 0xb88b54: r0 = copyWith()
    //     0xb88b54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb88b58: stur            x0, [fp, #-0x20]
    // 0xb88b5c: r0 = Text()
    //     0xb88b5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb88b60: mov             x1, x0
    // 0xb88b64: r0 = "View All"
    //     0xb88b64: add             x0, PP, #0x55, lsl #12  ; [pp+0x55098] "View All"
    //     0xb88b68: ldr             x0, [x0, #0x98]
    // 0xb88b6c: stur            x1, [fp, #-0x30]
    // 0xb88b70: StoreField: r1->field_b = r0
    //     0xb88b70: stur            w0, [x1, #0xb]
    // 0xb88b74: ldur            x0, [fp, #-0x20]
    // 0xb88b78: StoreField: r1->field_13 = r0
    //     0xb88b78: stur            w0, [x1, #0x13]
    // 0xb88b7c: r0 = Center()
    //     0xb88b7c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb88b80: mov             x1, x0
    // 0xb88b84: r0 = Instance_Alignment
    //     0xb88b84: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb88b88: ldr             x0, [x0, #0xb10]
    // 0xb88b8c: stur            x1, [fp, #-0x20]
    // 0xb88b90: StoreField: r1->field_f = r0
    //     0xb88b90: stur            w0, [x1, #0xf]
    // 0xb88b94: ldur            x0, [fp, #-0x30]
    // 0xb88b98: StoreField: r1->field_b = r0
    //     0xb88b98: stur            w0, [x1, #0xb]
    // 0xb88b9c: r0 = Container()
    //     0xb88b9c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb88ba0: stur            x0, [fp, #-0x30]
    // 0xb88ba4: r16 = 40.000000
    //     0xb88ba4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb88ba8: ldr             x16, [x16, #8]
    // 0xb88bac: r30 = 110.000000
    //     0xb88bac: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb88bb0: ldr             lr, [lr, #0x770]
    // 0xb88bb4: stp             lr, x16, [SP, #0x10]
    // 0xb88bb8: ldur            x16, [fp, #-0x28]
    // 0xb88bbc: ldur            lr, [fp, #-0x20]
    // 0xb88bc0: stp             lr, x16, [SP]
    // 0xb88bc4: mov             x1, x0
    // 0xb88bc8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb88bc8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb88bcc: ldr             x4, [x4, #0x8c0]
    // 0xb88bd0: r0 = Container()
    //     0xb88bd0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb88bd4: r0 = InkWell()
    //     0xb88bd4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb88bd8: mov             x3, x0
    // 0xb88bdc: ldur            x0, [fp, #-0x30]
    // 0xb88be0: stur            x3, [fp, #-0x20]
    // 0xb88be4: StoreField: r3->field_b = r0
    //     0xb88be4: stur            w0, [x3, #0xb]
    // 0xb88be8: ldur            x2, [fp, #-0x10]
    // 0xb88bec: r1 = Function '<anonymous closure>':.
    //     0xb88bec: add             x1, PP, #0x55, lsl #12  ; [pp+0x55508] AnonymousClosure: (0xb88d60), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb884ac)
    //     0xb88bf0: ldr             x1, [x1, #0x508]
    // 0xb88bf4: r0 = AllocateClosure()
    //     0xb88bf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb88bf8: mov             x1, x0
    // 0xb88bfc: ldur            x0, [fp, #-0x20]
    // 0xb88c00: StoreField: r0->field_f = r1
    //     0xb88c00: stur            w1, [x0, #0xf]
    // 0xb88c04: r1 = true
    //     0xb88c04: add             x1, NULL, #0x20  ; true
    // 0xb88c08: StoreField: r0->field_43 = r1
    //     0xb88c08: stur            w1, [x0, #0x43]
    // 0xb88c0c: r2 = Instance_BoxShape
    //     0xb88c0c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb88c10: ldr             x2, [x2, #0x80]
    // 0xb88c14: StoreField: r0->field_47 = r2
    //     0xb88c14: stur            w2, [x0, #0x47]
    // 0xb88c18: StoreField: r0->field_6f = r1
    //     0xb88c18: stur            w1, [x0, #0x6f]
    // 0xb88c1c: r2 = false
    //     0xb88c1c: add             x2, NULL, #0x30  ; false
    // 0xb88c20: StoreField: r0->field_73 = r2
    //     0xb88c20: stur            w2, [x0, #0x73]
    // 0xb88c24: StoreField: r0->field_83 = r1
    //     0xb88c24: stur            w1, [x0, #0x83]
    // 0xb88c28: StoreField: r0->field_7b = r2
    //     0xb88c28: stur            w2, [x0, #0x7b]
    // 0xb88c2c: r0 = Padding()
    //     0xb88c2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb88c30: mov             x2, x0
    // 0xb88c34: r0 = Instance_EdgeInsets
    //     0xb88c34: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0xb88c38: ldr             x0, [x0, #0xc30]
    // 0xb88c3c: stur            x2, [fp, #-0x10]
    // 0xb88c40: StoreField: r2->field_f = r0
    //     0xb88c40: stur            w0, [x2, #0xf]
    // 0xb88c44: ldur            x0, [fp, #-0x20]
    // 0xb88c48: StoreField: r2->field_b = r0
    //     0xb88c48: stur            w0, [x2, #0xb]
    // 0xb88c4c: ldur            x0, [fp, #-0x18]
    // 0xb88c50: LoadField: r1 = r0->field_b
    //     0xb88c50: ldur            w1, [x0, #0xb]
    // 0xb88c54: LoadField: r3 = r0->field_f
    //     0xb88c54: ldur            w3, [x0, #0xf]
    // 0xb88c58: DecompressPointer r3
    //     0xb88c58: add             x3, x3, HEAP, lsl #32
    // 0xb88c5c: LoadField: r4 = r3->field_b
    //     0xb88c5c: ldur            w4, [x3, #0xb]
    // 0xb88c60: r3 = LoadInt32Instr(r1)
    //     0xb88c60: sbfx            x3, x1, #1, #0x1f
    // 0xb88c64: stur            x3, [fp, #-0x40]
    // 0xb88c68: r1 = LoadInt32Instr(r4)
    //     0xb88c68: sbfx            x1, x4, #1, #0x1f
    // 0xb88c6c: cmp             x3, x1
    // 0xb88c70: b.ne            #0xb88c7c
    // 0xb88c74: mov             x1, x0
    // 0xb88c78: r0 = _growToNextCapacity()
    //     0xb88c78: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb88c7c: ldur            x2, [fp, #-0x18]
    // 0xb88c80: ldur            x4, [fp, #-8]
    // 0xb88c84: ldur            x3, [fp, #-0x40]
    // 0xb88c88: add             x0, x3, #1
    // 0xb88c8c: lsl             x1, x0, #1
    // 0xb88c90: StoreField: r2->field_b = r1
    //     0xb88c90: stur            w1, [x2, #0xb]
    // 0xb88c94: LoadField: r1 = r2->field_f
    //     0xb88c94: ldur            w1, [x2, #0xf]
    // 0xb88c98: DecompressPointer r1
    //     0xb88c98: add             x1, x1, HEAP, lsl #32
    // 0xb88c9c: ldur            x0, [fp, #-0x10]
    // 0xb88ca0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb88ca0: add             x25, x1, x3, lsl #2
    //     0xb88ca4: add             x25, x25, #0xf
    //     0xb88ca8: str             w0, [x25]
    //     0xb88cac: tbz             w0, #0, #0xb88cc8
    //     0xb88cb0: ldurb           w16, [x1, #-1]
    //     0xb88cb4: ldurb           w17, [x0, #-1]
    //     0xb88cb8: and             x16, x17, x16, lsr #2
    //     0xb88cbc: tst             x16, HEAP, lsr #32
    //     0xb88cc0: b.eq            #0xb88cc8
    //     0xb88cc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb88cc8: r0 = Column()
    //     0xb88cc8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb88ccc: r1 = Instance_Axis
    //     0xb88ccc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb88cd0: StoreField: r0->field_f = r1
    //     0xb88cd0: stur            w1, [x0, #0xf]
    // 0xb88cd4: r1 = Instance_MainAxisAlignment
    //     0xb88cd4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb88cd8: ldr             x1, [x1, #0xa08]
    // 0xb88cdc: StoreField: r0->field_13 = r1
    //     0xb88cdc: stur            w1, [x0, #0x13]
    // 0xb88ce0: r1 = Instance_MainAxisSize
    //     0xb88ce0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb88ce4: ldr             x1, [x1, #0xa10]
    // 0xb88ce8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb88ce8: stur            w1, [x0, #0x17]
    // 0xb88cec: ldur            x1, [fp, #-8]
    // 0xb88cf0: StoreField: r0->field_1b = r1
    //     0xb88cf0: stur            w1, [x0, #0x1b]
    // 0xb88cf4: r1 = Instance_VerticalDirection
    //     0xb88cf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb88cf8: ldr             x1, [x1, #0xa20]
    // 0xb88cfc: StoreField: r0->field_23 = r1
    //     0xb88cfc: stur            w1, [x0, #0x23]
    // 0xb88d00: r1 = Instance_Clip
    //     0xb88d00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb88d04: ldr             x1, [x1, #0x38]
    // 0xb88d08: StoreField: r0->field_2b = r1
    //     0xb88d08: stur            w1, [x0, #0x2b]
    // 0xb88d0c: StoreField: r0->field_2f = rZR
    //     0xb88d0c: stur            xzr, [x0, #0x2f]
    // 0xb88d10: ldur            x1, [fp, #-0x18]
    // 0xb88d14: StoreField: r0->field_b = r1
    //     0xb88d14: stur            w1, [x0, #0xb]
    // 0xb88d18: LeaveFrame
    //     0xb88d18: mov             SP, fp
    //     0xb88d1c: ldp             fp, lr, [SP], #0x10
    // 0xb88d20: ret
    //     0xb88d20: ret             
    // 0xb88d24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb88d24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88d28: b               #0xb88558
    // 0xb88d2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88d2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88d30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88d30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88d34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88d34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88d38: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb88d38: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb88d3c: r9 = _pageController
    //     0xb88d3c: add             x9, PP, #0x55, lsl #12  ; [pp+0x554e8] Field <_ProductTestimonialCarouselState@1624278349._pageController@1624278349>: late (offset: 0x14)
    //     0xb88d40: ldr             x9, [x9, #0x4e8]
    // 0xb88d44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb88d44: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb88d48: SaveReg d0
    //     0xb88d48: str             q0, [SP, #-0x10]!
    // 0xb88d4c: r0 = AllocateDouble()
    //     0xb88d4c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb88d50: RestoreReg d0
    //     0xb88d50: ldr             q0, [SP], #0x10
    // 0xb88d54: b               #0xb888b0
    // 0xb88d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88d58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88d5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb88d60, size: 0x1b8
    // 0xb88d60: EnterFrame
    //     0xb88d60: stp             fp, lr, [SP, #-0x10]!
    //     0xb88d64: mov             fp, SP
    // 0xb88d68: AllocStack(0x48)
    //     0xb88d68: sub             SP, SP, #0x48
    // 0xb88d6c: SetupParameters()
    //     0xb88d6c: ldr             x0, [fp, #0x10]
    //     0xb88d70: ldur            w1, [x0, #0x17]
    //     0xb88d74: add             x1, x1, HEAP, lsl #32
    //     0xb88d78: stur            x1, [fp, #-0x28]
    // 0xb88d7c: CheckStackOverflow
    //     0xb88d7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88d80: cmp             SP, x16
    //     0xb88d84: b.ls            #0xb88f08
    // 0xb88d88: LoadField: r0 = r1->field_f
    //     0xb88d88: ldur            w0, [x1, #0xf]
    // 0xb88d8c: DecompressPointer r0
    //     0xb88d8c: add             x0, x0, HEAP, lsl #32
    // 0xb88d90: LoadField: r2 = r0->field_b
    //     0xb88d90: ldur            w2, [x0, #0xb]
    // 0xb88d94: DecompressPointer r2
    //     0xb88d94: add             x2, x2, HEAP, lsl #32
    // 0xb88d98: stur            x2, [fp, #-0x20]
    // 0xb88d9c: cmp             w2, NULL
    // 0xb88da0: b.eq            #0xb88f10
    // 0xb88da4: LoadField: r0 = r2->field_1b
    //     0xb88da4: ldur            w0, [x2, #0x1b]
    // 0xb88da8: DecompressPointer r0
    //     0xb88da8: add             x0, x0, HEAP, lsl #32
    // 0xb88dac: stur            x0, [fp, #-0x18]
    // 0xb88db0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb88db0: ldur            w3, [x2, #0x17]
    // 0xb88db4: DecompressPointer r3
    //     0xb88db4: add             x3, x3, HEAP, lsl #32
    // 0xb88db8: stur            x3, [fp, #-0x10]
    // 0xb88dbc: LoadField: r4 = r2->field_f
    //     0xb88dbc: ldur            w4, [x2, #0xf]
    // 0xb88dc0: DecompressPointer r4
    //     0xb88dc0: add             x4, x4, HEAP, lsl #32
    // 0xb88dc4: stur            x4, [fp, #-8]
    // 0xb88dc8: r0 = EventData()
    //     0xb88dc8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb88dcc: mov             x1, x0
    // 0xb88dd0: r0 = "product_page"
    //     0xb88dd0: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb88dd4: ldr             x0, [x0, #0x480]
    // 0xb88dd8: stur            x1, [fp, #-0x30]
    // 0xb88ddc: StoreField: r1->field_13 = r0
    //     0xb88ddc: stur            w0, [x1, #0x13]
    // 0xb88de0: ldur            x2, [fp, #-0x10]
    // 0xb88de4: StoreField: r1->field_53 = r2
    //     0xb88de4: stur            w2, [x1, #0x53]
    // 0xb88de8: ldur            x2, [fp, #-0x18]
    // 0xb88dec: StoreField: r1->field_57 = r2
    //     0xb88dec: stur            w2, [x1, #0x57]
    // 0xb88df0: ldur            x2, [fp, #-8]
    // 0xb88df4: StoreField: r1->field_ef = r2
    //     0xb88df4: stur            w2, [x1, #0xef]
    // 0xb88df8: StoreField: r1->field_f3 = r0
    //     0xb88df8: stur            w0, [x1, #0xf3]
    // 0xb88dfc: r0 = EventsRequest()
    //     0xb88dfc: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb88e00: mov             x1, x0
    // 0xb88e04: r0 = "widget_clicked"
    //     0xb88e04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edd8] "widget_clicked"
    //     0xb88e08: ldr             x0, [x0, #0xdd8]
    // 0xb88e0c: StoreField: r1->field_7 = r0
    //     0xb88e0c: stur            w0, [x1, #7]
    // 0xb88e10: ldur            x0, [fp, #-0x30]
    // 0xb88e14: StoreField: r1->field_b = r0
    //     0xb88e14: stur            w0, [x1, #0xb]
    // 0xb88e18: ldur            x0, [fp, #-0x20]
    // 0xb88e1c: LoadField: r2 = r0->field_27
    //     0xb88e1c: ldur            w2, [x0, #0x27]
    // 0xb88e20: DecompressPointer r2
    //     0xb88e20: add             x2, x2, HEAP, lsl #32
    // 0xb88e24: stp             x1, x2, [SP]
    // 0xb88e28: r4 = 0
    //     0xb88e28: movz            x4, #0
    // 0xb88e2c: ldr             x0, [SP, #8]
    // 0xb88e30: r16 = UnlinkedCall_0x613b5c
    //     0xb88e30: add             x16, PP, #0x55, lsl #12  ; [pp+0x55510] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb88e34: add             x16, x16, #0x510
    // 0xb88e38: ldp             x5, lr, [x16]
    // 0xb88e3c: blr             lr
    // 0xb88e40: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb88e40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb88e44: ldr             x0, [x0, #0x1c80]
    //     0xb88e48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb88e4c: cmp             w0, w16
    //     0xb88e50: b.ne            #0xb88e5c
    //     0xb88e54: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb88e58: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb88e5c: r1 = Null
    //     0xb88e5c: mov             x1, NULL
    // 0xb88e60: r2 = 12
    //     0xb88e60: movz            x2, #0xc
    // 0xb88e64: r0 = AllocateArray()
    //     0xb88e64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb88e68: r16 = "previousScreenSource"
    //     0xb88e68: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xb88e6c: ldr             x16, [x16, #0x448]
    // 0xb88e70: StoreField: r0->field_f = r16
    //     0xb88e70: stur            w16, [x0, #0xf]
    // 0xb88e74: ldur            x1, [fp, #-0x28]
    // 0xb88e78: LoadField: r2 = r1->field_f
    //     0xb88e78: ldur            w2, [x1, #0xf]
    // 0xb88e7c: DecompressPointer r2
    //     0xb88e7c: add             x2, x2, HEAP, lsl #32
    // 0xb88e80: LoadField: r1 = r2->field_b
    //     0xb88e80: ldur            w1, [x2, #0xb]
    // 0xb88e84: DecompressPointer r1
    //     0xb88e84: add             x1, x1, HEAP, lsl #32
    // 0xb88e88: cmp             w1, NULL
    // 0xb88e8c: b.eq            #0xb88f14
    // 0xb88e90: LoadField: r2 = r1->field_23
    //     0xb88e90: ldur            w2, [x1, #0x23]
    // 0xb88e94: DecompressPointer r2
    //     0xb88e94: add             x2, x2, HEAP, lsl #32
    // 0xb88e98: StoreField: r0->field_13 = r2
    //     0xb88e98: stur            w2, [x0, #0x13]
    // 0xb88e9c: r16 = "screenSource"
    //     0xb88e9c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xb88ea0: ldr             x16, [x16, #0x450]
    // 0xb88ea4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb88ea4: stur            w16, [x0, #0x17]
    // 0xb88ea8: LoadField: r2 = r1->field_1f
    //     0xb88ea8: ldur            w2, [x1, #0x1f]
    // 0xb88eac: DecompressPointer r2
    //     0xb88eac: add             x2, x2, HEAP, lsl #32
    // 0xb88eb0: StoreField: r0->field_1b = r2
    //     0xb88eb0: stur            w2, [x0, #0x1b]
    // 0xb88eb4: r16 = "widgetType"
    //     0xb88eb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xb88eb8: ldr             x16, [x16, #0x338]
    // 0xb88ebc: StoreField: r0->field_1f = r16
    //     0xb88ebc: stur            w16, [x0, #0x1f]
    // 0xb88ec0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb88ec0: ldur            w2, [x1, #0x17]
    // 0xb88ec4: DecompressPointer r2
    //     0xb88ec4: add             x2, x2, HEAP, lsl #32
    // 0xb88ec8: StoreField: r0->field_23 = r2
    //     0xb88ec8: stur            w2, [x0, #0x23]
    // 0xb88ecc: r16 = <String, String?>
    //     0xb88ecc: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xb88ed0: ldr             x16, [x16, #0x3c8]
    // 0xb88ed4: stp             x0, x16, [SP]
    // 0xb88ed8: r0 = Map._fromLiteral()
    //     0xb88ed8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb88edc: r16 = "/testimonials"
    //     0xb88edc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xb88ee0: ldr             x16, [x16, #0x898]
    // 0xb88ee4: stp             x16, NULL, [SP, #8]
    // 0xb88ee8: str             x0, [SP]
    // 0xb88eec: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb88eec: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb88ef0: ldr             x4, [x4, #0x438]
    // 0xb88ef4: r0 = GetNavigation.toNamed()
    //     0xb88ef4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb88ef8: r0 = Null
    //     0xb88ef8: mov             x0, NULL
    // 0xb88efc: LeaveFrame
    //     0xb88efc: mov             SP, fp
    //     0xb88f00: ldp             fp, lr, [SP], #0x10
    // 0xb88f04: ret
    //     0xb88f04: ret             
    // 0xb88f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb88f08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88f0c: b               #0xb88d88
    // 0xb88f10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88f10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88f14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88f14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb88f18, size: 0x70
    // 0xb88f18: EnterFrame
    //     0xb88f18: stp             fp, lr, [SP, #-0x10]!
    //     0xb88f1c: mov             fp, SP
    // 0xb88f20: ldr             x0, [fp, #0x20]
    // 0xb88f24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb88f24: ldur            w1, [x0, #0x17]
    // 0xb88f28: DecompressPointer r1
    //     0xb88f28: add             x1, x1, HEAP, lsl #32
    // 0xb88f2c: CheckStackOverflow
    //     0xb88f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88f30: cmp             SP, x16
    //     0xb88f34: b.ls            #0xb88f7c
    // 0xb88f38: LoadField: r0 = r1->field_f
    //     0xb88f38: ldur            w0, [x1, #0xf]
    // 0xb88f3c: DecompressPointer r0
    //     0xb88f3c: add             x0, x0, HEAP, lsl #32
    // 0xb88f40: LoadField: r1 = r0->field_b
    //     0xb88f40: ldur            w1, [x0, #0xb]
    // 0xb88f44: DecompressPointer r1
    //     0xb88f44: add             x1, x1, HEAP, lsl #32
    // 0xb88f48: cmp             w1, NULL
    // 0xb88f4c: b.eq            #0xb88f84
    // 0xb88f50: LoadField: r2 = r1->field_b
    //     0xb88f50: ldur            w2, [x1, #0xb]
    // 0xb88f54: DecompressPointer r2
    //     0xb88f54: add             x2, x2, HEAP, lsl #32
    // 0xb88f58: ldr             x1, [fp, #0x10]
    // 0xb88f5c: r3 = LoadInt32Instr(r1)
    //     0xb88f5c: sbfx            x3, x1, #1, #0x1f
    //     0xb88f60: tbz             w1, #0, #0xb88f68
    //     0xb88f64: ldur            x3, [x1, #7]
    // 0xb88f68: mov             x1, x0
    // 0xb88f6c: r0 = _testimonialCard()
    //     0xb88f6c: bl              #0xb88f88  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xb88f70: LeaveFrame
    //     0xb88f70: mov             SP, fp
    //     0xb88f74: ldp             fp, lr, [SP], #0x10
    // 0xb88f78: ret
    //     0xb88f78: ret             
    // 0xb88f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb88f7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88f80: b               #0xb88f38
    // 0xb88f84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88f84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xb88f88, size: 0xe84
    // 0xb88f88: EnterFrame
    //     0xb88f88: stp             fp, lr, [SP, #-0x10]!
    //     0xb88f8c: mov             fp, SP
    // 0xb88f90: AllocStack(0xa0)
    //     0xb88f90: sub             SP, SP, #0xa0
    // 0xb88f94: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb88f94: stur            x1, [fp, #-8]
    //     0xb88f98: stur            x2, [fp, #-0x10]
    //     0xb88f9c: stur            x3, [fp, #-0x18]
    // 0xb88fa0: CheckStackOverflow
    //     0xb88fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88fa4: cmp             SP, x16
    //     0xb88fa8: b.ls            #0xb89dd8
    // 0xb88fac: r1 = 2
    //     0xb88fac: movz            x1, #0x2
    // 0xb88fb0: r0 = AllocateContext()
    //     0xb88fb0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb88fb4: mov             x3, x0
    // 0xb88fb8: ldur            x2, [fp, #-8]
    // 0xb88fbc: stur            x3, [fp, #-0x28]
    // 0xb88fc0: StoreField: r3->field_f = r2
    //     0xb88fc0: stur            w2, [x3, #0xf]
    // 0xb88fc4: ldur            x4, [fp, #-0x18]
    // 0xb88fc8: r0 = BoxInt64Instr(r4)
    //     0xb88fc8: sbfiz           x0, x4, #1, #0x1f
    //     0xb88fcc: cmp             x4, x0, asr #1
    //     0xb88fd0: b.eq            #0xb88fdc
    //     0xb88fd4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb88fd8: stur            x4, [x0, #7]
    // 0xb88fdc: mov             x1, x0
    // 0xb88fe0: ldur            x0, [fp, #-0x10]
    // 0xb88fe4: stur            x1, [fp, #-0x20]
    // 0xb88fe8: r4 = LoadClassIdInstr(r0)
    //     0xb88fe8: ldur            x4, [x0, #-1]
    //     0xb88fec: ubfx            x4, x4, #0xc, #0x14
    // 0xb88ff0: stp             x1, x0, [SP]
    // 0xb88ff4: mov             x0, x4
    // 0xb88ff8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb88ff8: sub             lr, x0, #0xb7
    //     0xb88ffc: ldr             lr, [x21, lr, lsl #3]
    //     0xb89000: blr             lr
    // 0xb89004: stur            x0, [fp, #-0x38]
    // 0xb89008: cmp             w0, NULL
    // 0xb8900c: b.ne            #0xb89018
    // 0xb89010: r1 = Null
    //     0xb89010: mov             x1, NULL
    // 0xb89014: b               #0xb89044
    // 0xb89018: LoadField: r1 = r0->field_b7
    //     0xb89018: ldur            w1, [x0, #0xb7]
    // 0xb8901c: DecompressPointer r1
    //     0xb8901c: add             x1, x1, HEAP, lsl #32
    // 0xb89020: cmp             w1, NULL
    // 0xb89024: b.ne            #0xb89030
    // 0xb89028: r1 = Null
    //     0xb89028: mov             x1, NULL
    // 0xb8902c: b               #0xb89044
    // 0xb89030: LoadField: r2 = r1->field_b
    //     0xb89030: ldur            w2, [x1, #0xb]
    // 0xb89034: cbnz            w2, #0xb89040
    // 0xb89038: r1 = false
    //     0xb89038: add             x1, NULL, #0x30  ; false
    // 0xb8903c: b               #0xb89044
    // 0xb89040: r1 = true
    //     0xb89040: add             x1, NULL, #0x20  ; true
    // 0xb89044: cmp             w1, NULL
    // 0xb89048: b.ne            #0xb89054
    // 0xb8904c: r4 = false
    //     0xb8904c: add             x4, NULL, #0x30  ; false
    // 0xb89050: b               #0xb89058
    // 0xb89054: mov             x4, x1
    // 0xb89058: ldur            x3, [fp, #-8]
    // 0xb8905c: stur            x4, [fp, #-0x30]
    // 0xb89060: LoadField: r5 = r3->field_1f
    //     0xb89060: ldur            w5, [x3, #0x1f]
    // 0xb89064: DecompressPointer r5
    //     0xb89064: add             x5, x5, HEAP, lsl #32
    // 0xb89068: mov             x1, x5
    // 0xb8906c: ldur            x2, [fp, #-0x20]
    // 0xb89070: stur            x5, [fp, #-0x10]
    // 0xb89074: r0 = _getValueOrData()
    //     0xb89074: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb89078: mov             x1, x0
    // 0xb8907c: ldur            x0, [fp, #-0x10]
    // 0xb89080: LoadField: r2 = r0->field_f
    //     0xb89080: ldur            w2, [x0, #0xf]
    // 0xb89084: DecompressPointer r2
    //     0xb89084: add             x2, x2, HEAP, lsl #32
    // 0xb89088: cmp             w2, w1
    // 0xb8908c: b.ne            #0xb89098
    // 0xb89090: r0 = Null
    //     0xb89090: mov             x0, NULL
    // 0xb89094: b               #0xb8909c
    // 0xb89098: mov             x0, x1
    // 0xb8909c: cmp             w0, NULL
    // 0xb890a0: b.ne            #0xb890e0
    // 0xb890a4: r1 = <bool>
    //     0xb890a4: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xb890a8: r0 = RxBool()
    //     0xb890a8: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xb890ac: mov             x2, x0
    // 0xb890b0: r0 = Sentinel
    //     0xb890b0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb890b4: stur            x2, [fp, #-0x10]
    // 0xb890b8: StoreField: r2->field_13 = r0
    //     0xb890b8: stur            w0, [x2, #0x13]
    // 0xb890bc: r0 = true
    //     0xb890bc: add             x0, NULL, #0x20  ; true
    // 0xb890c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb890c0: stur            w0, [x2, #0x17]
    // 0xb890c4: mov             x1, x2
    // 0xb890c8: r0 = RxNotifier()
    //     0xb890c8: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xb890cc: ldur            x0, [fp, #-0x10]
    // 0xb890d0: r1 = false
    //     0xb890d0: add             x1, NULL, #0x30  ; false
    // 0xb890d4: StoreField: r0->field_13 = r1
    //     0xb890d4: stur            w1, [x0, #0x13]
    // 0xb890d8: mov             x5, x0
    // 0xb890dc: b               #0xb890e8
    // 0xb890e0: r1 = false
    //     0xb890e0: add             x1, NULL, #0x30  ; false
    // 0xb890e4: mov             x5, x0
    // 0xb890e8: ldur            x2, [fp, #-8]
    // 0xb890ec: ldur            x4, [fp, #-0x28]
    // 0xb890f0: ldur            x3, [fp, #-0x30]
    // 0xb890f4: mov             x0, x5
    // 0xb890f8: stur            x5, [fp, #-0x10]
    // 0xb890fc: StoreField: r4->field_13 = r0
    //     0xb890fc: stur            w0, [x4, #0x13]
    //     0xb89100: ldurb           w16, [x4, #-1]
    //     0xb89104: ldurb           w17, [x0, #-1]
    //     0xb89108: and             x16, x17, x16, lsr #2
    //     0xb8910c: tst             x16, HEAP, lsr #32
    //     0xb89110: b.eq            #0xb89118
    //     0xb89114: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb89118: r0 = Radius()
    //     0xb89118: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8911c: d0 = 12.000000
    //     0xb8911c: fmov            d0, #12.00000000
    // 0xb89120: stur            x0, [fp, #-0x20]
    // 0xb89124: StoreField: r0->field_7 = d0
    //     0xb89124: stur            d0, [x0, #7]
    // 0xb89128: StoreField: r0->field_f = d0
    //     0xb89128: stur            d0, [x0, #0xf]
    // 0xb8912c: r0 = BorderRadius()
    //     0xb8912c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb89130: mov             x1, x0
    // 0xb89134: ldur            x0, [fp, #-0x20]
    // 0xb89138: stur            x1, [fp, #-0x40]
    // 0xb8913c: StoreField: r1->field_7 = r0
    //     0xb8913c: stur            w0, [x1, #7]
    // 0xb89140: StoreField: r1->field_b = r0
    //     0xb89140: stur            w0, [x1, #0xb]
    // 0xb89144: StoreField: r1->field_f = r0
    //     0xb89144: stur            w0, [x1, #0xf]
    // 0xb89148: StoreField: r1->field_13 = r0
    //     0xb89148: stur            w0, [x1, #0x13]
    // 0xb8914c: r0 = RoundedRectangleBorder()
    //     0xb8914c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb89150: mov             x2, x0
    // 0xb89154: ldur            x0, [fp, #-0x40]
    // 0xb89158: stur            x2, [fp, #-0x20]
    // 0xb8915c: StoreField: r2->field_b = r0
    //     0xb8915c: stur            w0, [x2, #0xb]
    // 0xb89160: r0 = Instance_BorderSide
    //     0xb89160: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb89164: ldr             x0, [x0, #0xe20]
    // 0xb89168: StoreField: r2->field_7 = r0
    //     0xb89168: stur            w0, [x2, #7]
    // 0xb8916c: ldur            x0, [fp, #-8]
    // 0xb89170: LoadField: r1 = r0->field_f
    //     0xb89170: ldur            w1, [x0, #0xf]
    // 0xb89174: DecompressPointer r1
    //     0xb89174: add             x1, x1, HEAP, lsl #32
    // 0xb89178: cmp             w1, NULL
    // 0xb8917c: b.eq            #0xb89de0
    // 0xb89180: r0 = of()
    //     0xb89180: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb89184: LoadField: r1 = r0->field_5b
    //     0xb89184: ldur            w1, [x0, #0x5b]
    // 0xb89188: DecompressPointer r1
    //     0xb89188: add             x1, x1, HEAP, lsl #32
    // 0xb8918c: r0 = LoadClassIdInstr(r1)
    //     0xb8918c: ldur            x0, [x1, #-1]
    //     0xb89190: ubfx            x0, x0, #0xc, #0x14
    // 0xb89194: d0 = 0.030000
    //     0xb89194: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb89198: ldr             d0, [x17, #0x238]
    // 0xb8919c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb8919c: sub             lr, x0, #0xffa
    //     0xb891a0: ldr             lr, [x21, lr, lsl #3]
    //     0xb891a4: blr             lr
    // 0xb891a8: r1 = <Widget>
    //     0xb891a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb891ac: r2 = 0
    //     0xb891ac: movz            x2, #0
    // 0xb891b0: stur            x0, [fp, #-0x40]
    // 0xb891b4: r0 = _GrowableList()
    //     0xb891b4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb891b8: mov             x3, x0
    // 0xb891bc: ldur            x0, [fp, #-0x30]
    // 0xb891c0: stur            x3, [fp, #-0x48]
    // 0xb891c4: tbnz            w0, #4, #0xb89368
    // 0xb891c8: ldur            x4, [fp, #-0x38]
    // 0xb891cc: cmp             w4, NULL
    // 0xb891d0: b.ne            #0xb891dc
    // 0xb891d4: r0 = Null
    //     0xb891d4: mov             x0, NULL
    // 0xb891d8: b               #0xb89240
    // 0xb891dc: LoadField: r2 = r4->field_b7
    //     0xb891dc: ldur            w2, [x4, #0xb7]
    // 0xb891e0: DecompressPointer r2
    //     0xb891e0: add             x2, x2, HEAP, lsl #32
    // 0xb891e4: cmp             w2, NULL
    // 0xb891e8: b.ne            #0xb891f4
    // 0xb891ec: r0 = Null
    //     0xb891ec: mov             x0, NULL
    // 0xb891f0: b               #0xb89240
    // 0xb891f4: LoadField: r0 = r2->field_b
    //     0xb891f4: ldur            w0, [x2, #0xb]
    // 0xb891f8: r1 = LoadInt32Instr(r0)
    //     0xb891f8: sbfx            x1, x0, #1, #0x1f
    // 0xb891fc: mov             x0, x1
    // 0xb89200: r1 = 0
    //     0xb89200: movz            x1, #0
    // 0xb89204: cmp             x1, x0
    // 0xb89208: b.hs            #0xb89de4
    // 0xb8920c: LoadField: r0 = r2->field_f
    //     0xb8920c: ldur            w0, [x2, #0xf]
    // 0xb89210: DecompressPointer r0
    //     0xb89210: add             x0, x0, HEAP, lsl #32
    // 0xb89214: LoadField: r1 = r0->field_f
    //     0xb89214: ldur            w1, [x0, #0xf]
    // 0xb89218: DecompressPointer r1
    //     0xb89218: add             x1, x1, HEAP, lsl #32
    // 0xb8921c: LoadField: r0 = r1->field_7
    //     0xb8921c: ldur            w0, [x1, #7]
    // 0xb89220: DecompressPointer r0
    //     0xb89220: add             x0, x0, HEAP, lsl #32
    // 0xb89224: cmp             w0, NULL
    // 0xb89228: b.ne            #0xb89234
    // 0xb8922c: r0 = Null
    //     0xb8922c: mov             x0, NULL
    // 0xb89230: b               #0xb89240
    // 0xb89234: LoadField: r1 = r0->field_b
    //     0xb89234: ldur            w1, [x0, #0xb]
    // 0xb89238: DecompressPointer r1
    //     0xb89238: add             x1, x1, HEAP, lsl #32
    // 0xb8923c: mov             x0, x1
    // 0xb89240: cmp             w0, NULL
    // 0xb89244: b.ne            #0xb8924c
    // 0xb89248: r0 = ""
    //     0xb89248: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8924c: stur            x0, [fp, #-0x30]
    // 0xb89250: r1 = Function '<anonymous closure>':.
    //     0xb89250: add             x1, PP, #0x55, lsl #12  ; [pp+0x55520] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb89254: ldr             x1, [x1, #0x520]
    // 0xb89258: r2 = Null
    //     0xb89258: mov             x2, NULL
    // 0xb8925c: r0 = AllocateClosure()
    //     0xb8925c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89260: r1 = Function '<anonymous closure>':.
    //     0xb89260: add             x1, PP, #0x55, lsl #12  ; [pp+0x55528] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb89264: ldr             x1, [x1, #0x528]
    // 0xb89268: r2 = Null
    //     0xb89268: mov             x2, NULL
    // 0xb8926c: stur            x0, [fp, #-0x50]
    // 0xb89270: r0 = AllocateClosure()
    //     0xb89270: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89274: stur            x0, [fp, #-0x58]
    // 0xb89278: r0 = CachedNetworkImage()
    //     0xb89278: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb8927c: stur            x0, [fp, #-0x60]
    // 0xb89280: ldur            x16, [fp, #-0x50]
    // 0xb89284: ldur            lr, [fp, #-0x58]
    // 0xb89288: stp             lr, x16, [SP, #0x18]
    // 0xb8928c: r16 = inf
    //     0xb8928c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb89290: ldr             x16, [x16, #0x9f8]
    // 0xb89294: r30 = 336.000000
    //     0xb89294: add             lr, PP, #0x55, lsl #12  ; [pp+0x55530] 336
    //     0xb89298: ldr             lr, [lr, #0x530]
    // 0xb8929c: stp             lr, x16, [SP, #8]
    // 0xb892a0: r16 = Instance_BoxFit
    //     0xb892a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb892a4: ldr             x16, [x16, #0x118]
    // 0xb892a8: str             x16, [SP]
    // 0xb892ac: mov             x1, x0
    // 0xb892b0: ldur            x2, [fp, #-0x30]
    // 0xb892b4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x3, fit, 0x6, height, 0x5, progressIndicatorBuilder, 0x2, width, 0x4, null]
    //     0xb892b4: add             x4, PP, #0x55, lsl #12  ; [pp+0x55538] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x3, "fit", 0x6, "height", 0x5, "progressIndicatorBuilder", 0x2, "width", 0x4, Null]
    //     0xb892b8: ldr             x4, [x4, #0x538]
    // 0xb892bc: r0 = CachedNetworkImage()
    //     0xb892bc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb892c0: r0 = ClipRRect()
    //     0xb892c0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb892c4: mov             x2, x0
    // 0xb892c8: r0 = Instance_BorderRadius
    //     0xb892c8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb892cc: ldr             x0, [x0, #0xe10]
    // 0xb892d0: stur            x2, [fp, #-0x30]
    // 0xb892d4: StoreField: r2->field_f = r0
    //     0xb892d4: stur            w0, [x2, #0xf]
    // 0xb892d8: r0 = Instance_Clip
    //     0xb892d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb892dc: ldr             x0, [x0, #0x138]
    // 0xb892e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb892e0: stur            w0, [x2, #0x17]
    // 0xb892e4: ldur            x0, [fp, #-0x60]
    // 0xb892e8: StoreField: r2->field_b = r0
    //     0xb892e8: stur            w0, [x2, #0xb]
    // 0xb892ec: ldur            x0, [fp, #-0x48]
    // 0xb892f0: LoadField: r1 = r0->field_b
    //     0xb892f0: ldur            w1, [x0, #0xb]
    // 0xb892f4: LoadField: r3 = r0->field_f
    //     0xb892f4: ldur            w3, [x0, #0xf]
    // 0xb892f8: DecompressPointer r3
    //     0xb892f8: add             x3, x3, HEAP, lsl #32
    // 0xb892fc: LoadField: r4 = r3->field_b
    //     0xb892fc: ldur            w4, [x3, #0xb]
    // 0xb89300: r3 = LoadInt32Instr(r1)
    //     0xb89300: sbfx            x3, x1, #1, #0x1f
    // 0xb89304: stur            x3, [fp, #-0x18]
    // 0xb89308: r1 = LoadInt32Instr(r4)
    //     0xb89308: sbfx            x1, x4, #1, #0x1f
    // 0xb8930c: cmp             x3, x1
    // 0xb89310: b.ne            #0xb8931c
    // 0xb89314: mov             x1, x0
    // 0xb89318: r0 = _growToNextCapacity()
    //     0xb89318: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8931c: ldur            x2, [fp, #-0x48]
    // 0xb89320: ldur            x3, [fp, #-0x18]
    // 0xb89324: add             x0, x3, #1
    // 0xb89328: lsl             x1, x0, #1
    // 0xb8932c: StoreField: r2->field_b = r1
    //     0xb8932c: stur            w1, [x2, #0xb]
    // 0xb89330: LoadField: r1 = r2->field_f
    //     0xb89330: ldur            w1, [x2, #0xf]
    // 0xb89334: DecompressPointer r1
    //     0xb89334: add             x1, x1, HEAP, lsl #32
    // 0xb89338: ldur            x0, [fp, #-0x30]
    // 0xb8933c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8933c: add             x25, x1, x3, lsl #2
    //     0xb89340: add             x25, x25, #0xf
    //     0xb89344: str             w0, [x25]
    //     0xb89348: tbz             w0, #0, #0xb89364
    //     0xb8934c: ldurb           w16, [x1, #-1]
    //     0xb89350: ldurb           w17, [x0, #-1]
    //     0xb89354: and             x16, x17, x16, lsr #2
    //     0xb89358: tst             x16, HEAP, lsr #32
    //     0xb8935c: b.eq            #0xb89364
    //     0xb89360: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb89364: b               #0xb8936c
    // 0xb89368: mov             x2, x3
    // 0xb8936c: ldur            x0, [fp, #-0x38]
    // 0xb89370: cmp             w0, NULL
    // 0xb89374: b.ne            #0xb89380
    // 0xb89378: r1 = Null
    //     0xb89378: mov             x1, NULL
    // 0xb8937c: b               #0xb89388
    // 0xb89380: LoadField: r1 = r0->field_cb
    //     0xb89380: ldur            w1, [x0, #0xcb]
    // 0xb89384: DecompressPointer r1
    //     0xb89384: add             x1, x1, HEAP, lsl #32
    // 0xb89388: cmp             w1, NULL
    // 0xb8938c: b.ne            #0xb89394
    // 0xb89390: r1 = ""
    //     0xb89390: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb89394: ldur            x3, [fp, #-8]
    // 0xb89398: r0 = capitalizeFirstWord()
    //     0xb89398: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb8939c: mov             x2, x0
    // 0xb893a0: ldur            x0, [fp, #-8]
    // 0xb893a4: stur            x2, [fp, #-0x30]
    // 0xb893a8: LoadField: r1 = r0->field_f
    //     0xb893a8: ldur            w1, [x0, #0xf]
    // 0xb893ac: DecompressPointer r1
    //     0xb893ac: add             x1, x1, HEAP, lsl #32
    // 0xb893b0: cmp             w1, NULL
    // 0xb893b4: b.eq            #0xb89de8
    // 0xb893b8: r0 = of()
    //     0xb893b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb893bc: LoadField: r1 = r0->field_87
    //     0xb893bc: ldur            w1, [x0, #0x87]
    // 0xb893c0: DecompressPointer r1
    //     0xb893c0: add             x1, x1, HEAP, lsl #32
    // 0xb893c4: LoadField: r0 = r1->field_7
    //     0xb893c4: ldur            w0, [x1, #7]
    // 0xb893c8: DecompressPointer r0
    //     0xb893c8: add             x0, x0, HEAP, lsl #32
    // 0xb893cc: r16 = 14.000000
    //     0xb893cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb893d0: ldr             x16, [x16, #0x1d8]
    // 0xb893d4: r30 = Instance_Color
    //     0xb893d4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb893d8: stp             lr, x16, [SP]
    // 0xb893dc: mov             x1, x0
    // 0xb893e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb893e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb893e4: ldr             x4, [x4, #0xaa0]
    // 0xb893e8: r0 = copyWith()
    //     0xb893e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb893ec: stur            x0, [fp, #-0x50]
    // 0xb893f0: r0 = Text()
    //     0xb893f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb893f4: mov             x1, x0
    // 0xb893f8: ldur            x0, [fp, #-0x30]
    // 0xb893fc: stur            x1, [fp, #-0x58]
    // 0xb89400: StoreField: r1->field_b = r0
    //     0xb89400: stur            w0, [x1, #0xb]
    // 0xb89404: ldur            x0, [fp, #-0x50]
    // 0xb89408: StoreField: r1->field_13 = r0
    //     0xb89408: stur            w0, [x1, #0x13]
    // 0xb8940c: r0 = Padding()
    //     0xb8940c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb89410: mov             x2, x0
    // 0xb89414: r0 = Instance_EdgeInsets
    //     0xb89414: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb89418: ldr             x0, [x0, #0xa98]
    // 0xb8941c: stur            x2, [fp, #-0x30]
    // 0xb89420: StoreField: r2->field_f = r0
    //     0xb89420: stur            w0, [x2, #0xf]
    // 0xb89424: ldur            x0, [fp, #-0x58]
    // 0xb89428: StoreField: r2->field_b = r0
    //     0xb89428: stur            w0, [x2, #0xb]
    // 0xb8942c: ldur            x0, [fp, #-0x48]
    // 0xb89430: LoadField: r1 = r0->field_b
    //     0xb89430: ldur            w1, [x0, #0xb]
    // 0xb89434: LoadField: r3 = r0->field_f
    //     0xb89434: ldur            w3, [x0, #0xf]
    // 0xb89438: DecompressPointer r3
    //     0xb89438: add             x3, x3, HEAP, lsl #32
    // 0xb8943c: LoadField: r4 = r3->field_b
    //     0xb8943c: ldur            w4, [x3, #0xb]
    // 0xb89440: r3 = LoadInt32Instr(r1)
    //     0xb89440: sbfx            x3, x1, #1, #0x1f
    // 0xb89444: stur            x3, [fp, #-0x18]
    // 0xb89448: r1 = LoadInt32Instr(r4)
    //     0xb89448: sbfx            x1, x4, #1, #0x1f
    // 0xb8944c: cmp             x3, x1
    // 0xb89450: b.ne            #0xb8945c
    // 0xb89454: mov             x1, x0
    // 0xb89458: r0 = _growToNextCapacity()
    //     0xb89458: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8945c: ldur            x2, [fp, #-0x48]
    // 0xb89460: ldur            x3, [fp, #-0x18]
    // 0xb89464: add             x4, x3, #1
    // 0xb89468: stur            x4, [fp, #-0x68]
    // 0xb8946c: lsl             x0, x4, #1
    // 0xb89470: StoreField: r2->field_b = r0
    //     0xb89470: stur            w0, [x2, #0xb]
    // 0xb89474: LoadField: r5 = r2->field_f
    //     0xb89474: ldur            w5, [x2, #0xf]
    // 0xb89478: DecompressPointer r5
    //     0xb89478: add             x5, x5, HEAP, lsl #32
    // 0xb8947c: mov             x1, x5
    // 0xb89480: ldur            x0, [fp, #-0x30]
    // 0xb89484: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb89484: add             x25, x1, x3, lsl #2
    //     0xb89488: add             x25, x25, #0xf
    //     0xb8948c: str             w0, [x25]
    //     0xb89490: tbz             w0, #0, #0xb894ac
    //     0xb89494: ldurb           w16, [x1, #-1]
    //     0xb89498: ldurb           w17, [x0, #-1]
    //     0xb8949c: and             x16, x17, x16, lsr #2
    //     0xb894a0: tst             x16, HEAP, lsr #32
    //     0xb894a4: b.eq            #0xb894ac
    //     0xb894a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb894ac: LoadField: r0 = r5->field_b
    //     0xb894ac: ldur            w0, [x5, #0xb]
    // 0xb894b0: r1 = LoadInt32Instr(r0)
    //     0xb894b0: sbfx            x1, x0, #1, #0x1f
    // 0xb894b4: cmp             x4, x1
    // 0xb894b8: b.ne            #0xb894c4
    // 0xb894bc: mov             x1, x2
    // 0xb894c0: r0 = _growToNextCapacity()
    //     0xb894c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb894c4: ldur            x2, [fp, #-0x38]
    // 0xb894c8: ldur            x0, [fp, #-0x48]
    // 0xb894cc: ldur            x1, [fp, #-0x68]
    // 0xb894d0: add             x3, x1, #1
    // 0xb894d4: lsl             x4, x3, #1
    // 0xb894d8: StoreField: r0->field_b = r4
    //     0xb894d8: stur            w4, [x0, #0xb]
    // 0xb894dc: LoadField: r3 = r0->field_f
    //     0xb894dc: ldur            w3, [x0, #0xf]
    // 0xb894e0: DecompressPointer r3
    //     0xb894e0: add             x3, x3, HEAP, lsl #32
    // 0xb894e4: add             x4, x3, x1, lsl #2
    // 0xb894e8: r16 = Instance_SizedBox
    //     0xb894e8: add             x16, PP, #0x55, lsl #12  ; [pp+0x55540] Obj!SizedBox@d67fe1
    //     0xb894ec: ldr             x16, [x16, #0x540]
    // 0xb894f0: StoreField: r4->field_f = r16
    //     0xb894f0: stur            w16, [x4, #0xf]
    // 0xb894f4: cmp             w2, NULL
    // 0xb894f8: b.ne            #0xb89504
    // 0xb894fc: r1 = Null
    //     0xb894fc: mov             x1, NULL
    // 0xb89500: b               #0xb8950c
    // 0xb89504: LoadField: r1 = r2->field_bb
    //     0xb89504: ldur            w1, [x2, #0xbb]
    // 0xb89508: DecompressPointer r1
    //     0xb89508: add             x1, x1, HEAP, lsl #32
    // 0xb8950c: cmp             w1, NULL
    // 0xb89510: b.ne            #0xb89518
    // 0xb89514: r1 = "0"
    //     0xb89514: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xb89518: ldur            x3, [fp, #-8]
    // 0xb8951c: r0 = parse()
    //     0xb8951c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb89520: ldur            x0, [fp, #-8]
    // 0xb89524: stur            d0, [fp, #-0x78]
    // 0xb89528: LoadField: r1 = r0->field_f
    //     0xb89528: ldur            w1, [x0, #0xf]
    // 0xb8952c: DecompressPointer r1
    //     0xb8952c: add             x1, x1, HEAP, lsl #32
    // 0xb89530: cmp             w1, NULL
    // 0xb89534: b.eq            #0xb89dec
    // 0xb89538: r0 = of()
    //     0xb89538: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8953c: LoadField: r1 = r0->field_5b
    //     0xb8953c: ldur            w1, [x0, #0x5b]
    // 0xb89540: DecompressPointer r1
    //     0xb89540: add             x1, x1, HEAP, lsl #32
    // 0xb89544: stur            x1, [fp, #-0x30]
    // 0xb89548: r0 = Icon()
    //     0xb89548: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb8954c: mov             x2, x0
    // 0xb89550: r0 = Instance_IconData
    //     0xb89550: add             x0, PP, #0x55, lsl #12  ; [pp+0x55548] Obj!IconData@d55401
    //     0xb89554: ldr             x0, [x0, #0x548]
    // 0xb89558: stur            x2, [fp, #-0x50]
    // 0xb8955c: StoreField: r2->field_b = r0
    //     0xb8955c: stur            w0, [x2, #0xb]
    // 0xb89560: r0 = 8.000000
    //     0xb89560: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb89564: ldr             x0, [x0, #0x608]
    // 0xb89568: StoreField: r2->field_f = r0
    //     0xb89568: stur            w0, [x2, #0xf]
    // 0xb8956c: ldur            x1, [fp, #-0x30]
    // 0xb89570: StoreField: r2->field_23 = r1
    //     0xb89570: stur            w1, [x2, #0x23]
    // 0xb89574: ldur            x3, [fp, #-8]
    // 0xb89578: LoadField: r1 = r3->field_f
    //     0xb89578: ldur            w1, [x3, #0xf]
    // 0xb8957c: DecompressPointer r1
    //     0xb8957c: add             x1, x1, HEAP, lsl #32
    // 0xb89580: cmp             w1, NULL
    // 0xb89584: b.eq            #0xb89df0
    // 0xb89588: r0 = of()
    //     0xb89588: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8958c: LoadField: r1 = r0->field_5b
    //     0xb8958c: ldur            w1, [x0, #0x5b]
    // 0xb89590: DecompressPointer r1
    //     0xb89590: add             x1, x1, HEAP, lsl #32
    // 0xb89594: stur            x1, [fp, #-0x30]
    // 0xb89598: r0 = Icon()
    //     0xb89598: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb8959c: mov             x2, x0
    // 0xb895a0: r0 = Instance_IconData
    //     0xb895a0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55550] Obj!IconData@d553e1
    //     0xb895a4: ldr             x0, [x0, #0x550]
    // 0xb895a8: stur            x2, [fp, #-0x58]
    // 0xb895ac: StoreField: r2->field_b = r0
    //     0xb895ac: stur            w0, [x2, #0xb]
    // 0xb895b0: r0 = 8.000000
    //     0xb895b0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb895b4: ldr             x0, [x0, #0x608]
    // 0xb895b8: StoreField: r2->field_f = r0
    //     0xb895b8: stur            w0, [x2, #0xf]
    // 0xb895bc: ldur            x1, [fp, #-0x30]
    // 0xb895c0: StoreField: r2->field_23 = r1
    //     0xb895c0: stur            w1, [x2, #0x23]
    // 0xb895c4: ldur            x3, [fp, #-8]
    // 0xb895c8: LoadField: r1 = r3->field_f
    //     0xb895c8: ldur            w1, [x3, #0xf]
    // 0xb895cc: DecompressPointer r1
    //     0xb895cc: add             x1, x1, HEAP, lsl #32
    // 0xb895d0: cmp             w1, NULL
    // 0xb895d4: b.eq            #0xb89df4
    // 0xb895d8: r0 = of()
    //     0xb895d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb895dc: LoadField: r1 = r0->field_5b
    //     0xb895dc: ldur            w1, [x0, #0x5b]
    // 0xb895e0: DecompressPointer r1
    //     0xb895e0: add             x1, x1, HEAP, lsl #32
    // 0xb895e4: stur            x1, [fp, #-0x30]
    // 0xb895e8: r0 = Icon()
    //     0xb895e8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb895ec: mov             x1, x0
    // 0xb895f0: r0 = Instance_IconData
    //     0xb895f0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55558] Obj!IconData@d553c1
    //     0xb895f4: ldr             x0, [x0, #0x558]
    // 0xb895f8: stur            x1, [fp, #-0x60]
    // 0xb895fc: StoreField: r1->field_b = r0
    //     0xb895fc: stur            w0, [x1, #0xb]
    // 0xb89600: r0 = 8.000000
    //     0xb89600: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb89604: ldr             x0, [x0, #0x608]
    // 0xb89608: StoreField: r1->field_f = r0
    //     0xb89608: stur            w0, [x1, #0xf]
    // 0xb8960c: ldur            x0, [fp, #-0x30]
    // 0xb89610: StoreField: r1->field_23 = r0
    //     0xb89610: stur            w0, [x1, #0x23]
    // 0xb89614: r0 = RatingWidget()
    //     0xb89614: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb89618: mov             x3, x0
    // 0xb8961c: ldur            x0, [fp, #-0x50]
    // 0xb89620: stur            x3, [fp, #-0x30]
    // 0xb89624: StoreField: r3->field_7 = r0
    //     0xb89624: stur            w0, [x3, #7]
    // 0xb89628: ldur            x0, [fp, #-0x58]
    // 0xb8962c: StoreField: r3->field_b = r0
    //     0xb8962c: stur            w0, [x3, #0xb]
    // 0xb89630: ldur            x0, [fp, #-0x60]
    // 0xb89634: StoreField: r3->field_f = r0
    //     0xb89634: stur            w0, [x3, #0xf]
    // 0xb89638: r1 = Function '<anonymous closure>':.
    //     0xb89638: add             x1, PP, #0x55, lsl #12  ; [pp+0x55560] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb8963c: ldr             x1, [x1, #0x560]
    // 0xb89640: r2 = Null
    //     0xb89640: mov             x2, NULL
    // 0xb89644: r0 = AllocateClosure()
    //     0xb89644: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89648: stur            x0, [fp, #-0x50]
    // 0xb8964c: r0 = RatingBar()
    //     0xb8964c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb89650: mov             x2, x0
    // 0xb89654: ldur            x0, [fp, #-0x50]
    // 0xb89658: stur            x2, [fp, #-0x58]
    // 0xb8965c: StoreField: r2->field_b = r0
    //     0xb8965c: stur            w0, [x2, #0xb]
    // 0xb89660: r0 = true
    //     0xb89660: add             x0, NULL, #0x20  ; true
    // 0xb89664: StoreField: r2->field_1f = r0
    //     0xb89664: stur            w0, [x2, #0x1f]
    // 0xb89668: r3 = Instance_Axis
    //     0xb89668: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8966c: StoreField: r2->field_23 = r3
    //     0xb8966c: stur            w3, [x2, #0x23]
    // 0xb89670: StoreField: r2->field_27 = r0
    //     0xb89670: stur            w0, [x2, #0x27]
    // 0xb89674: d0 = 2.000000
    //     0xb89674: fmov            d0, #2.00000000
    // 0xb89678: StoreField: r2->field_2b = d0
    //     0xb89678: stur            d0, [x2, #0x2b]
    // 0xb8967c: StoreField: r2->field_33 = r0
    //     0xb8967c: stur            w0, [x2, #0x33]
    // 0xb89680: ldur            d0, [fp, #-0x78]
    // 0xb89684: StoreField: r2->field_37 = d0
    //     0xb89684: stur            d0, [x2, #0x37]
    // 0xb89688: r1 = 5
    //     0xb89688: movz            x1, #0x5
    // 0xb8968c: StoreField: r2->field_3f = r1
    //     0xb8968c: stur            x1, [x2, #0x3f]
    // 0xb89690: r1 = Instance_EdgeInsets
    //     0xb89690: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb89694: StoreField: r2->field_47 = r1
    //     0xb89694: stur            w1, [x2, #0x47]
    // 0xb89698: d0 = 18.000000
    //     0xb89698: fmov            d0, #18.00000000
    // 0xb8969c: StoreField: r2->field_4b = d0
    //     0xb8969c: stur            d0, [x2, #0x4b]
    // 0xb896a0: StoreField: r2->field_53 = rZR
    //     0xb896a0: stur            xzr, [x2, #0x53]
    // 0xb896a4: r1 = false
    //     0xb896a4: add             x1, NULL, #0x30  ; false
    // 0xb896a8: StoreField: r2->field_5b = r1
    //     0xb896a8: stur            w1, [x2, #0x5b]
    // 0xb896ac: StoreField: r2->field_5f = r1
    //     0xb896ac: stur            w1, [x2, #0x5f]
    // 0xb896b0: ldur            x1, [fp, #-0x30]
    // 0xb896b4: StoreField: r2->field_67 = r1
    //     0xb896b4: stur            w1, [x2, #0x67]
    // 0xb896b8: ldur            x4, [fp, #-0x38]
    // 0xb896bc: cmp             w4, NULL
    // 0xb896c0: b.ne            #0xb896cc
    // 0xb896c4: r1 = Null
    //     0xb896c4: mov             x1, NULL
    // 0xb896c8: b               #0xb896d4
    // 0xb896cc: LoadField: r1 = r4->field_c3
    //     0xb896cc: ldur            w1, [x4, #0xc3]
    // 0xb896d0: DecompressPointer r1
    //     0xb896d0: add             x1, x1, HEAP, lsl #32
    // 0xb896d4: cmp             w1, NULL
    // 0xb896d8: b.ne            #0xb896e4
    // 0xb896dc: r7 = ""
    //     0xb896dc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb896e0: b               #0xb896e8
    // 0xb896e4: mov             x7, x1
    // 0xb896e8: ldur            x5, [fp, #-8]
    // 0xb896ec: ldur            x6, [fp, #-0x48]
    // 0xb896f0: stur            x7, [fp, #-0x30]
    // 0xb896f4: LoadField: r1 = r5->field_f
    //     0xb896f4: ldur            w1, [x5, #0xf]
    // 0xb896f8: DecompressPointer r1
    //     0xb896f8: add             x1, x1, HEAP, lsl #32
    // 0xb896fc: cmp             w1, NULL
    // 0xb89700: b.eq            #0xb89df8
    // 0xb89704: r0 = of()
    //     0xb89704: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb89708: LoadField: r1 = r0->field_87
    //     0xb89708: ldur            w1, [x0, #0x87]
    // 0xb8970c: DecompressPointer r1
    //     0xb8970c: add             x1, x1, HEAP, lsl #32
    // 0xb89710: LoadField: r0 = r1->field_2b
    //     0xb89710: ldur            w0, [x1, #0x2b]
    // 0xb89714: DecompressPointer r0
    //     0xb89714: add             x0, x0, HEAP, lsl #32
    // 0xb89718: stur            x0, [fp, #-0x50]
    // 0xb8971c: r1 = Instance_Color
    //     0xb8971c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb89720: d0 = 0.700000
    //     0xb89720: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb89724: ldr             d0, [x17, #0xf48]
    // 0xb89728: r0 = withOpacity()
    //     0xb89728: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8972c: r16 = 14.000000
    //     0xb8972c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb89730: ldr             x16, [x16, #0x1d8]
    // 0xb89734: stp             x0, x16, [SP]
    // 0xb89738: ldur            x1, [fp, #-0x50]
    // 0xb8973c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8973c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb89740: ldr             x4, [x4, #0xaa0]
    // 0xb89744: r0 = copyWith()
    //     0xb89744: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb89748: stur            x0, [fp, #-0x50]
    // 0xb8974c: r0 = Text()
    //     0xb8974c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb89750: mov             x3, x0
    // 0xb89754: ldur            x0, [fp, #-0x30]
    // 0xb89758: stur            x3, [fp, #-0x60]
    // 0xb8975c: StoreField: r3->field_b = r0
    //     0xb8975c: stur            w0, [x3, #0xb]
    // 0xb89760: ldur            x0, [fp, #-0x50]
    // 0xb89764: StoreField: r3->field_13 = r0
    //     0xb89764: stur            w0, [x3, #0x13]
    // 0xb89768: r1 = Null
    //     0xb89768: mov             x1, NULL
    // 0xb8976c: r2 = 6
    //     0xb8976c: movz            x2, #0x6
    // 0xb89770: r0 = AllocateArray()
    //     0xb89770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb89774: mov             x2, x0
    // 0xb89778: ldur            x0, [fp, #-0x58]
    // 0xb8977c: stur            x2, [fp, #-0x30]
    // 0xb89780: StoreField: r2->field_f = r0
    //     0xb89780: stur            w0, [x2, #0xf]
    // 0xb89784: r16 = Instance_SizedBox
    //     0xb89784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xb89788: ldr             x16, [x16, #0x940]
    // 0xb8978c: StoreField: r2->field_13 = r16
    //     0xb8978c: stur            w16, [x2, #0x13]
    // 0xb89790: ldur            x0, [fp, #-0x60]
    // 0xb89794: ArrayStore: r2[0] = r0  ; List_4
    //     0xb89794: stur            w0, [x2, #0x17]
    // 0xb89798: r1 = <Widget>
    //     0xb89798: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8979c: r0 = AllocateGrowableArray()
    //     0xb8979c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb897a0: mov             x1, x0
    // 0xb897a4: ldur            x0, [fp, #-0x30]
    // 0xb897a8: stur            x1, [fp, #-0x50]
    // 0xb897ac: StoreField: r1->field_f = r0
    //     0xb897ac: stur            w0, [x1, #0xf]
    // 0xb897b0: r0 = 6
    //     0xb897b0: movz            x0, #0x6
    // 0xb897b4: StoreField: r1->field_b = r0
    //     0xb897b4: stur            w0, [x1, #0xb]
    // 0xb897b8: r0 = Row()
    //     0xb897b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb897bc: mov             x1, x0
    // 0xb897c0: r0 = Instance_Axis
    //     0xb897c0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb897c4: stur            x1, [fp, #-0x30]
    // 0xb897c8: StoreField: r1->field_f = r0
    //     0xb897c8: stur            w0, [x1, #0xf]
    // 0xb897cc: r0 = Instance_MainAxisAlignment
    //     0xb897cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb897d0: ldr             x0, [x0, #0xa08]
    // 0xb897d4: StoreField: r1->field_13 = r0
    //     0xb897d4: stur            w0, [x1, #0x13]
    // 0xb897d8: r2 = Instance_MainAxisSize
    //     0xb897d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb897dc: ldr             x2, [x2, #0xa10]
    // 0xb897e0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb897e0: stur            w2, [x1, #0x17]
    // 0xb897e4: r3 = Instance_CrossAxisAlignment
    //     0xb897e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb897e8: ldr             x3, [x3, #0xa18]
    // 0xb897ec: StoreField: r1->field_1b = r3
    //     0xb897ec: stur            w3, [x1, #0x1b]
    // 0xb897f0: r3 = Instance_VerticalDirection
    //     0xb897f0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb897f4: ldr             x3, [x3, #0xa20]
    // 0xb897f8: StoreField: r1->field_23 = r3
    //     0xb897f8: stur            w3, [x1, #0x23]
    // 0xb897fc: r4 = Instance_Clip
    //     0xb897fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb89800: ldr             x4, [x4, #0x38]
    // 0xb89804: StoreField: r1->field_2b = r4
    //     0xb89804: stur            w4, [x1, #0x2b]
    // 0xb89808: StoreField: r1->field_2f = rZR
    //     0xb89808: stur            xzr, [x1, #0x2f]
    // 0xb8980c: ldur            x5, [fp, #-0x50]
    // 0xb89810: StoreField: r1->field_b = r5
    //     0xb89810: stur            w5, [x1, #0xb]
    // 0xb89814: r0 = Padding()
    //     0xb89814: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb89818: mov             x2, x0
    // 0xb8981c: r0 = Instance_EdgeInsets
    //     0xb8981c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb89820: ldr             x0, [x0, #0x668]
    // 0xb89824: stur            x2, [fp, #-0x50]
    // 0xb89828: StoreField: r2->field_f = r0
    //     0xb89828: stur            w0, [x2, #0xf]
    // 0xb8982c: ldur            x0, [fp, #-0x30]
    // 0xb89830: StoreField: r2->field_b = r0
    //     0xb89830: stur            w0, [x2, #0xb]
    // 0xb89834: ldur            x0, [fp, #-0x48]
    // 0xb89838: LoadField: r1 = r0->field_b
    //     0xb89838: ldur            w1, [x0, #0xb]
    // 0xb8983c: LoadField: r3 = r0->field_f
    //     0xb8983c: ldur            w3, [x0, #0xf]
    // 0xb89840: DecompressPointer r3
    //     0xb89840: add             x3, x3, HEAP, lsl #32
    // 0xb89844: LoadField: r4 = r3->field_b
    //     0xb89844: ldur            w4, [x3, #0xb]
    // 0xb89848: r3 = LoadInt32Instr(r1)
    //     0xb89848: sbfx            x3, x1, #1, #0x1f
    // 0xb8984c: stur            x3, [fp, #-0x18]
    // 0xb89850: r1 = LoadInt32Instr(r4)
    //     0xb89850: sbfx            x1, x4, #1, #0x1f
    // 0xb89854: cmp             x3, x1
    // 0xb89858: b.ne            #0xb89864
    // 0xb8985c: mov             x1, x0
    // 0xb89860: r0 = _growToNextCapacity()
    //     0xb89860: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb89864: ldur            x4, [fp, #-0x38]
    // 0xb89868: ldur            x2, [fp, #-0x48]
    // 0xb8986c: ldur            x3, [fp, #-0x18]
    // 0xb89870: add             x0, x3, #1
    // 0xb89874: lsl             x1, x0, #1
    // 0xb89878: StoreField: r2->field_b = r1
    //     0xb89878: stur            w1, [x2, #0xb]
    // 0xb8987c: LoadField: r1 = r2->field_f
    //     0xb8987c: ldur            w1, [x2, #0xf]
    // 0xb89880: DecompressPointer r1
    //     0xb89880: add             x1, x1, HEAP, lsl #32
    // 0xb89884: ldur            x0, [fp, #-0x50]
    // 0xb89888: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb89888: add             x25, x1, x3, lsl #2
    //     0xb8988c: add             x25, x25, #0xf
    //     0xb89890: str             w0, [x25]
    //     0xb89894: tbz             w0, #0, #0xb898b0
    //     0xb89898: ldurb           w16, [x1, #-1]
    //     0xb8989c: ldurb           w17, [x0, #-1]
    //     0xb898a0: and             x16, x17, x16, lsr #2
    //     0xb898a4: tst             x16, HEAP, lsr #32
    //     0xb898a8: b.eq            #0xb898b0
    //     0xb898ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb898b0: cmp             w4, NULL
    // 0xb898b4: b.ne            #0xb898c0
    // 0xb898b8: r0 = Null
    //     0xb898b8: mov             x0, NULL
    // 0xb898bc: b               #0xb898dc
    // 0xb898c0: LoadField: r1 = r4->field_bf
    //     0xb898c0: ldur            w1, [x4, #0xbf]
    // 0xb898c4: DecompressPointer r1
    //     0xb898c4: add             x1, x1, HEAP, lsl #32
    // 0xb898c8: cmp             w1, NULL
    // 0xb898cc: b.ne            #0xb898d8
    // 0xb898d0: r0 = Null
    //     0xb898d0: mov             x0, NULL
    // 0xb898d4: b               #0xb898dc
    // 0xb898d8: r0 = trim()
    //     0xb898d8: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb898dc: cmp             w0, NULL
    // 0xb898e0: b.ne            #0xb898e8
    // 0xb898e4: r0 = ""
    //     0xb898e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb898e8: ldur            x1, [fp, #-0x10]
    // 0xb898ec: stur            x0, [fp, #-0x30]
    // 0xb898f0: r0 = value()
    //     0xb898f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb898f4: tbnz            w0, #4, #0xb89900
    // 0xb898f8: r0 = Null
    //     0xb898f8: mov             x0, NULL
    // 0xb898fc: b               #0xb89904
    // 0xb89900: r0 = 4
    //     0xb89900: movz            x0, #0x4
    // 0xb89904: ldur            x1, [fp, #-0x10]
    // 0xb89908: stur            x0, [fp, #-0x50]
    // 0xb8990c: r0 = value()
    //     0xb8990c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb89910: tbnz            w0, #4, #0xb89920
    // 0xb89914: r5 = Instance_TextOverflow
    //     0xb89914: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xb89918: ldr             x5, [x5, #0x3a8]
    // 0xb8991c: b               #0xb89928
    // 0xb89920: r5 = Instance_TextOverflow
    //     0xb89920: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb89924: ldr             x5, [x5, #0xe10]
    // 0xb89928: ldur            x4, [fp, #-8]
    // 0xb8992c: ldur            x3, [fp, #-0x38]
    // 0xb89930: ldur            x2, [fp, #-0x30]
    // 0xb89934: ldur            x0, [fp, #-0x50]
    // 0xb89938: stur            x5, [fp, #-0x58]
    // 0xb8993c: LoadField: r1 = r4->field_f
    //     0xb8993c: ldur            w1, [x4, #0xf]
    // 0xb89940: DecompressPointer r1
    //     0xb89940: add             x1, x1, HEAP, lsl #32
    // 0xb89944: cmp             w1, NULL
    // 0xb89948: b.eq            #0xb89dfc
    // 0xb8994c: r0 = of()
    //     0xb8994c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb89950: LoadField: r1 = r0->field_87
    //     0xb89950: ldur            w1, [x0, #0x87]
    // 0xb89954: DecompressPointer r1
    //     0xb89954: add             x1, x1, HEAP, lsl #32
    // 0xb89958: LoadField: r0 = r1->field_2b
    //     0xb89958: ldur            w0, [x1, #0x2b]
    // 0xb8995c: DecompressPointer r0
    //     0xb8995c: add             x0, x0, HEAP, lsl #32
    // 0xb89960: LoadField: r1 = r0->field_13
    //     0xb89960: ldur            w1, [x0, #0x13]
    // 0xb89964: DecompressPointer r1
    //     0xb89964: add             x1, x1, HEAP, lsl #32
    // 0xb89968: stur            x1, [fp, #-0x60]
    // 0xb8996c: r0 = TextStyle()
    //     0xb8996c: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb89970: mov             x1, x0
    // 0xb89974: r0 = true
    //     0xb89974: add             x0, NULL, #0x20  ; true
    // 0xb89978: stur            x1, [fp, #-0x70]
    // 0xb8997c: StoreField: r1->field_7 = r0
    //     0xb8997c: stur            w0, [x1, #7]
    // 0xb89980: r2 = Instance_Color
    //     0xb89980: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb89984: StoreField: r1->field_b = r2
    //     0xb89984: stur            w2, [x1, #0xb]
    // 0xb89988: r2 = 12.000000
    //     0xb89988: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8998c: ldr             x2, [x2, #0x9e8]
    // 0xb89990: StoreField: r1->field_1f = r2
    //     0xb89990: stur            w2, [x1, #0x1f]
    // 0xb89994: ldur            x2, [fp, #-0x60]
    // 0xb89998: StoreField: r1->field_13 = r2
    //     0xb89998: stur            w2, [x1, #0x13]
    // 0xb8999c: r0 = Text()
    //     0xb8999c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb899a0: mov             x3, x0
    // 0xb899a4: ldur            x0, [fp, #-0x30]
    // 0xb899a8: stur            x3, [fp, #-0x60]
    // 0xb899ac: StoreField: r3->field_b = r0
    //     0xb899ac: stur            w0, [x3, #0xb]
    // 0xb899b0: ldur            x0, [fp, #-0x70]
    // 0xb899b4: StoreField: r3->field_13 = r0
    //     0xb899b4: stur            w0, [x3, #0x13]
    // 0xb899b8: r0 = Instance_TextAlign
    //     0xb899b8: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb899bc: StoreField: r3->field_1b = r0
    //     0xb899bc: stur            w0, [x3, #0x1b]
    // 0xb899c0: ldur            x0, [fp, #-0x58]
    // 0xb899c4: StoreField: r3->field_2b = r0
    //     0xb899c4: stur            w0, [x3, #0x2b]
    // 0xb899c8: ldur            x0, [fp, #-0x50]
    // 0xb899cc: StoreField: r3->field_37 = r0
    //     0xb899cc: stur            w0, [x3, #0x37]
    // 0xb899d0: r1 = Null
    //     0xb899d0: mov             x1, NULL
    // 0xb899d4: r2 = 2
    //     0xb899d4: movz            x2, #0x2
    // 0xb899d8: r0 = AllocateArray()
    //     0xb899d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb899dc: mov             x2, x0
    // 0xb899e0: ldur            x0, [fp, #-0x60]
    // 0xb899e4: stur            x2, [fp, #-0x30]
    // 0xb899e8: StoreField: r2->field_f = r0
    //     0xb899e8: stur            w0, [x2, #0xf]
    // 0xb899ec: r1 = <Widget>
    //     0xb899ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb899f0: r0 = AllocateGrowableArray()
    //     0xb899f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb899f4: mov             x2, x0
    // 0xb899f8: ldur            x0, [fp, #-0x30]
    // 0xb899fc: stur            x2, [fp, #-0x50]
    // 0xb89a00: StoreField: r2->field_f = r0
    //     0xb89a00: stur            w0, [x2, #0xf]
    // 0xb89a04: r0 = 2
    //     0xb89a04: movz            x0, #0x2
    // 0xb89a08: StoreField: r2->field_b = r0
    //     0xb89a08: stur            w0, [x2, #0xb]
    // 0xb89a0c: ldur            x0, [fp, #-0x38]
    // 0xb89a10: cmp             w0, NULL
    // 0xb89a14: b.ne            #0xb89a20
    // 0xb89a18: r0 = Null
    //     0xb89a18: mov             x0, NULL
    // 0xb89a1c: b               #0xb89a3c
    // 0xb89a20: LoadField: r1 = r0->field_bf
    //     0xb89a20: ldur            w1, [x0, #0xbf]
    // 0xb89a24: DecompressPointer r1
    //     0xb89a24: add             x1, x1, HEAP, lsl #32
    // 0xb89a28: cmp             w1, NULL
    // 0xb89a2c: b.ne            #0xb89a38
    // 0xb89a30: r0 = Null
    //     0xb89a30: mov             x0, NULL
    // 0xb89a34: b               #0xb89a3c
    // 0xb89a38: r0 = trim()
    //     0xb89a38: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb89a3c: cmp             w0, NULL
    // 0xb89a40: b.ne            #0xb89a4c
    // 0xb89a44: r1 = ""
    //     0xb89a44: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb89a48: b               #0xb89a50
    // 0xb89a4c: mov             x1, x0
    // 0xb89a50: ldur            x0, [fp, #-8]
    // 0xb89a54: LoadField: r2 = r0->field_f
    //     0xb89a54: ldur            w2, [x0, #0xf]
    // 0xb89a58: DecompressPointer r2
    //     0xb89a58: add             x2, x2, HEAP, lsl #32
    // 0xb89a5c: cmp             w2, NULL
    // 0xb89a60: b.eq            #0xb89e00
    // 0xb89a64: r0 = TextExceeds.textExceedsLines()
    //     0xb89a64: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xb89a68: tbnz            w0, #4, #0xb89bf8
    // 0xb89a6c: ldur            x1, [fp, #-0x10]
    // 0xb89a70: r0 = value()
    //     0xb89a70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb89a74: tbnz            w0, #4, #0xb89a84
    // 0xb89a78: r3 = "Know Less"
    //     0xb89a78: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xb89a7c: ldr             x3, [x3, #0x1d0]
    // 0xb89a80: b               #0xb89a8c
    // 0xb89a84: r3 = "Know more"
    //     0xb89a84: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xb89a88: ldr             x3, [x3, #0x20]
    // 0xb89a8c: ldur            x0, [fp, #-8]
    // 0xb89a90: ldur            x2, [fp, #-0x50]
    // 0xb89a94: stur            x3, [fp, #-0x10]
    // 0xb89a98: LoadField: r1 = r0->field_f
    //     0xb89a98: ldur            w1, [x0, #0xf]
    // 0xb89a9c: DecompressPointer r1
    //     0xb89a9c: add             x1, x1, HEAP, lsl #32
    // 0xb89aa0: cmp             w1, NULL
    // 0xb89aa4: b.eq            #0xb89e04
    // 0xb89aa8: r0 = of()
    //     0xb89aa8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb89aac: LoadField: r1 = r0->field_87
    //     0xb89aac: ldur            w1, [x0, #0x87]
    // 0xb89ab0: DecompressPointer r1
    //     0xb89ab0: add             x1, x1, HEAP, lsl #32
    // 0xb89ab4: LoadField: r0 = r1->field_7
    //     0xb89ab4: ldur            w0, [x1, #7]
    // 0xb89ab8: DecompressPointer r0
    //     0xb89ab8: add             x0, x0, HEAP, lsl #32
    // 0xb89abc: ldur            x1, [fp, #-8]
    // 0xb89ac0: stur            x0, [fp, #-0x30]
    // 0xb89ac4: LoadField: r2 = r1->field_f
    //     0xb89ac4: ldur            w2, [x1, #0xf]
    // 0xb89ac8: DecompressPointer r2
    //     0xb89ac8: add             x2, x2, HEAP, lsl #32
    // 0xb89acc: cmp             w2, NULL
    // 0xb89ad0: b.eq            #0xb89e08
    // 0xb89ad4: mov             x1, x2
    // 0xb89ad8: r0 = of()
    //     0xb89ad8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb89adc: LoadField: r1 = r0->field_5b
    //     0xb89adc: ldur            w1, [x0, #0x5b]
    // 0xb89ae0: DecompressPointer r1
    //     0xb89ae0: add             x1, x1, HEAP, lsl #32
    // 0xb89ae4: r16 = 12.000000
    //     0xb89ae4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb89ae8: ldr             x16, [x16, #0x9e8]
    // 0xb89aec: stp             x1, x16, [SP, #8]
    // 0xb89af0: r16 = Instance_TextDecoration
    //     0xb89af0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb89af4: ldr             x16, [x16, #0x10]
    // 0xb89af8: str             x16, [SP]
    // 0xb89afc: ldur            x1, [fp, #-0x30]
    // 0xb89b00: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb89b00: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb89b04: ldr             x4, [x4, #0xe38]
    // 0xb89b08: r0 = copyWith()
    //     0xb89b08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb89b0c: stur            x0, [fp, #-8]
    // 0xb89b10: r0 = Text()
    //     0xb89b10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb89b14: mov             x1, x0
    // 0xb89b18: ldur            x0, [fp, #-0x10]
    // 0xb89b1c: stur            x1, [fp, #-0x30]
    // 0xb89b20: StoreField: r1->field_b = r0
    //     0xb89b20: stur            w0, [x1, #0xb]
    // 0xb89b24: ldur            x0, [fp, #-8]
    // 0xb89b28: StoreField: r1->field_13 = r0
    //     0xb89b28: stur            w0, [x1, #0x13]
    // 0xb89b2c: r0 = Padding()
    //     0xb89b2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb89b30: mov             x1, x0
    // 0xb89b34: r0 = Instance_EdgeInsets
    //     0xb89b34: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb89b38: ldr             x0, [x0, #0x668]
    // 0xb89b3c: stur            x1, [fp, #-8]
    // 0xb89b40: StoreField: r1->field_f = r0
    //     0xb89b40: stur            w0, [x1, #0xf]
    // 0xb89b44: ldur            x0, [fp, #-0x30]
    // 0xb89b48: StoreField: r1->field_b = r0
    //     0xb89b48: stur            w0, [x1, #0xb]
    // 0xb89b4c: r0 = GestureDetector()
    //     0xb89b4c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb89b50: ldur            x2, [fp, #-0x28]
    // 0xb89b54: r1 = Function '<anonymous closure>':.
    //     0xb89b54: add             x1, PP, #0x55, lsl #12  ; [pp+0x55568] AnonymousClosure: (0xb89e0c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xb88f88)
    //     0xb89b58: ldr             x1, [x1, #0x568]
    // 0xb89b5c: stur            x0, [fp, #-0x10]
    // 0xb89b60: r0 = AllocateClosure()
    //     0xb89b60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89b64: ldur            x16, [fp, #-8]
    // 0xb89b68: stp             x16, x0, [SP]
    // 0xb89b6c: ldur            x1, [fp, #-0x10]
    // 0xb89b70: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb89b70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb89b74: ldr             x4, [x4, #0xaf0]
    // 0xb89b78: r0 = GestureDetector()
    //     0xb89b78: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb89b7c: ldur            x0, [fp, #-0x50]
    // 0xb89b80: LoadField: r1 = r0->field_b
    //     0xb89b80: ldur            w1, [x0, #0xb]
    // 0xb89b84: LoadField: r2 = r0->field_f
    //     0xb89b84: ldur            w2, [x0, #0xf]
    // 0xb89b88: DecompressPointer r2
    //     0xb89b88: add             x2, x2, HEAP, lsl #32
    // 0xb89b8c: LoadField: r3 = r2->field_b
    //     0xb89b8c: ldur            w3, [x2, #0xb]
    // 0xb89b90: r2 = LoadInt32Instr(r1)
    //     0xb89b90: sbfx            x2, x1, #1, #0x1f
    // 0xb89b94: stur            x2, [fp, #-0x18]
    // 0xb89b98: r1 = LoadInt32Instr(r3)
    //     0xb89b98: sbfx            x1, x3, #1, #0x1f
    // 0xb89b9c: cmp             x2, x1
    // 0xb89ba0: b.ne            #0xb89bac
    // 0xb89ba4: mov             x1, x0
    // 0xb89ba8: r0 = _growToNextCapacity()
    //     0xb89ba8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb89bac: ldur            x2, [fp, #-0x50]
    // 0xb89bb0: ldur            x3, [fp, #-0x18]
    // 0xb89bb4: add             x0, x3, #1
    // 0xb89bb8: lsl             x1, x0, #1
    // 0xb89bbc: StoreField: r2->field_b = r1
    //     0xb89bbc: stur            w1, [x2, #0xb]
    // 0xb89bc0: LoadField: r1 = r2->field_f
    //     0xb89bc0: ldur            w1, [x2, #0xf]
    // 0xb89bc4: DecompressPointer r1
    //     0xb89bc4: add             x1, x1, HEAP, lsl #32
    // 0xb89bc8: ldur            x0, [fp, #-0x10]
    // 0xb89bcc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb89bcc: add             x25, x1, x3, lsl #2
    //     0xb89bd0: add             x25, x25, #0xf
    //     0xb89bd4: str             w0, [x25]
    //     0xb89bd8: tbz             w0, #0, #0xb89bf4
    //     0xb89bdc: ldurb           w16, [x1, #-1]
    //     0xb89be0: ldurb           w17, [x0, #-1]
    //     0xb89be4: and             x16, x17, x16, lsr #2
    //     0xb89be8: tst             x16, HEAP, lsr #32
    //     0xb89bec: b.eq            #0xb89bf4
    //     0xb89bf0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb89bf4: b               #0xb89bfc
    // 0xb89bf8: ldur            x2, [fp, #-0x50]
    // 0xb89bfc: ldur            x1, [fp, #-0x48]
    // 0xb89c00: r0 = Column()
    //     0xb89c00: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb89c04: mov             x1, x0
    // 0xb89c08: r0 = Instance_Axis
    //     0xb89c08: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb89c0c: stur            x1, [fp, #-8]
    // 0xb89c10: StoreField: r1->field_f = r0
    //     0xb89c10: stur            w0, [x1, #0xf]
    // 0xb89c14: r0 = Instance_MainAxisAlignment
    //     0xb89c14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb89c18: ldr             x0, [x0, #0xa08]
    // 0xb89c1c: StoreField: r1->field_13 = r0
    //     0xb89c1c: stur            w0, [x1, #0x13]
    // 0xb89c20: r0 = Instance_MainAxisSize
    //     0xb89c20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb89c24: ldr             x0, [x0, #0xa10]
    // 0xb89c28: ArrayStore: r1[0] = r0  ; List_4
    //     0xb89c28: stur            w0, [x1, #0x17]
    // 0xb89c2c: r0 = Instance_CrossAxisAlignment
    //     0xb89c2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb89c30: ldr             x0, [x0, #0x890]
    // 0xb89c34: StoreField: r1->field_1b = r0
    //     0xb89c34: stur            w0, [x1, #0x1b]
    // 0xb89c38: r0 = Instance_VerticalDirection
    //     0xb89c38: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb89c3c: ldr             x0, [x0, #0xa20]
    // 0xb89c40: StoreField: r1->field_23 = r0
    //     0xb89c40: stur            w0, [x1, #0x23]
    // 0xb89c44: r0 = Instance_Clip
    //     0xb89c44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb89c48: ldr             x0, [x0, #0x38]
    // 0xb89c4c: StoreField: r1->field_2b = r0
    //     0xb89c4c: stur            w0, [x1, #0x2b]
    // 0xb89c50: StoreField: r1->field_2f = rZR
    //     0xb89c50: stur            xzr, [x1, #0x2f]
    // 0xb89c54: ldur            x0, [fp, #-0x50]
    // 0xb89c58: StoreField: r1->field_b = r0
    //     0xb89c58: stur            w0, [x1, #0xb]
    // 0xb89c5c: r0 = Padding()
    //     0xb89c5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb89c60: mov             x2, x0
    // 0xb89c64: r0 = Instance_EdgeInsets
    //     0xb89c64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb89c68: ldr             x0, [x0, #0xd0]
    // 0xb89c6c: stur            x2, [fp, #-0x10]
    // 0xb89c70: StoreField: r2->field_f = r0
    //     0xb89c70: stur            w0, [x2, #0xf]
    // 0xb89c74: ldur            x0, [fp, #-8]
    // 0xb89c78: StoreField: r2->field_b = r0
    //     0xb89c78: stur            w0, [x2, #0xb]
    // 0xb89c7c: ldur            x0, [fp, #-0x48]
    // 0xb89c80: LoadField: r1 = r0->field_b
    //     0xb89c80: ldur            w1, [x0, #0xb]
    // 0xb89c84: LoadField: r3 = r0->field_f
    //     0xb89c84: ldur            w3, [x0, #0xf]
    // 0xb89c88: DecompressPointer r3
    //     0xb89c88: add             x3, x3, HEAP, lsl #32
    // 0xb89c8c: LoadField: r4 = r3->field_b
    //     0xb89c8c: ldur            w4, [x3, #0xb]
    // 0xb89c90: r3 = LoadInt32Instr(r1)
    //     0xb89c90: sbfx            x3, x1, #1, #0x1f
    // 0xb89c94: stur            x3, [fp, #-0x18]
    // 0xb89c98: r1 = LoadInt32Instr(r4)
    //     0xb89c98: sbfx            x1, x4, #1, #0x1f
    // 0xb89c9c: cmp             x3, x1
    // 0xb89ca0: b.ne            #0xb89cac
    // 0xb89ca4: mov             x1, x0
    // 0xb89ca8: r0 = _growToNextCapacity()
    //     0xb89ca8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb89cac: ldur            x5, [fp, #-0x20]
    // 0xb89cb0: ldur            x4, [fp, #-0x40]
    // 0xb89cb4: ldur            x2, [fp, #-0x48]
    // 0xb89cb8: ldur            x3, [fp, #-0x18]
    // 0xb89cbc: add             x0, x3, #1
    // 0xb89cc0: lsl             x1, x0, #1
    // 0xb89cc4: StoreField: r2->field_b = r1
    //     0xb89cc4: stur            w1, [x2, #0xb]
    // 0xb89cc8: LoadField: r1 = r2->field_f
    //     0xb89cc8: ldur            w1, [x2, #0xf]
    // 0xb89ccc: DecompressPointer r1
    //     0xb89ccc: add             x1, x1, HEAP, lsl #32
    // 0xb89cd0: ldur            x0, [fp, #-0x10]
    // 0xb89cd4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb89cd4: add             x25, x1, x3, lsl #2
    //     0xb89cd8: add             x25, x25, #0xf
    //     0xb89cdc: str             w0, [x25]
    //     0xb89ce0: tbz             w0, #0, #0xb89cfc
    //     0xb89ce4: ldurb           w16, [x1, #-1]
    //     0xb89ce8: ldurb           w17, [x0, #-1]
    //     0xb89cec: and             x16, x17, x16, lsr #2
    //     0xb89cf0: tst             x16, HEAP, lsr #32
    //     0xb89cf4: b.eq            #0xb89cfc
    //     0xb89cf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb89cfc: r0 = ListView()
    //     0xb89cfc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb89d00: stur            x0, [fp, #-8]
    // 0xb89d04: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb89d04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb89d08: ldr             x16, [x16, #0x1c8]
    // 0xb89d0c: r30 = true
    //     0xb89d0c: add             lr, NULL, #0x20  ; true
    // 0xb89d10: stp             lr, x16, [SP, #8]
    // 0xb89d14: r16 = Instance_EdgeInsets
    //     0xb89d14: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb89d18: str             x16, [SP]
    // 0xb89d1c: mov             x1, x0
    // 0xb89d20: ldur            x2, [fp, #-0x48]
    // 0xb89d24: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xb89d24: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xb89d28: ldr             x4, [x4, #0x6b8]
    // 0xb89d2c: r0 = ListView()
    //     0xb89d2c: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb89d30: r0 = Card()
    //     0xb89d30: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb89d34: mov             x1, x0
    // 0xb89d38: ldur            x0, [fp, #-0x40]
    // 0xb89d3c: stur            x1, [fp, #-0x10]
    // 0xb89d40: StoreField: r1->field_b = r0
    //     0xb89d40: stur            w0, [x1, #0xb]
    // 0xb89d44: r0 = 0.000000
    //     0xb89d44: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb89d48: ArrayStore: r1[0] = r0  ; List_4
    //     0xb89d48: stur            w0, [x1, #0x17]
    // 0xb89d4c: ldur            x0, [fp, #-0x20]
    // 0xb89d50: StoreField: r1->field_1b = r0
    //     0xb89d50: stur            w0, [x1, #0x1b]
    // 0xb89d54: r0 = true
    //     0xb89d54: add             x0, NULL, #0x20  ; true
    // 0xb89d58: StoreField: r1->field_1f = r0
    //     0xb89d58: stur            w0, [x1, #0x1f]
    // 0xb89d5c: ldur            x2, [fp, #-8]
    // 0xb89d60: StoreField: r1->field_2f = r2
    //     0xb89d60: stur            w2, [x1, #0x2f]
    // 0xb89d64: StoreField: r1->field_2b = r0
    //     0xb89d64: stur            w0, [x1, #0x2b]
    // 0xb89d68: r0 = Instance__CardVariant
    //     0xb89d68: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb89d6c: ldr             x0, [x0, #0xa68]
    // 0xb89d70: StoreField: r1->field_33 = r0
    //     0xb89d70: stur            w0, [x1, #0x33]
    // 0xb89d74: r0 = Container()
    //     0xb89d74: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb89d78: stur            x0, [fp, #-8]
    // 0xb89d7c: r16 = Instance_EdgeInsets
    //     0xb89d7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb89d80: ldr             x16, [x16, #0x980]
    // 0xb89d84: ldur            lr, [fp, #-0x10]
    // 0xb89d88: stp             lr, x16, [SP]
    // 0xb89d8c: mov             x1, x0
    // 0xb89d90: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb89d90: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb89d94: ldr             x4, [x4, #0x30]
    // 0xb89d98: r0 = Container()
    //     0xb89d98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb89d9c: r0 = AnimatedContainer()
    //     0xb89d9c: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb89da0: stur            x0, [fp, #-0x10]
    // 0xb89da4: r16 = Instance_Cubic
    //     0xb89da4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb89da8: ldr             x16, [x16, #0xaf8]
    // 0xb89dac: str             x16, [SP]
    // 0xb89db0: mov             x1, x0
    // 0xb89db4: ldur            x2, [fp, #-8]
    // 0xb89db8: r3 = Instance_Duration
    //     0xb89db8: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb89dbc: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb89dbc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb89dc0: ldr             x4, [x4, #0xbc8]
    // 0xb89dc4: r0 = AnimatedContainer()
    //     0xb89dc4: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb89dc8: ldur            x0, [fp, #-0x10]
    // 0xb89dcc: LeaveFrame
    //     0xb89dcc: mov             SP, fp
    //     0xb89dd0: ldp             fp, lr, [SP], #0x10
    // 0xb89dd4: ret
    //     0xb89dd4: ret             
    // 0xb89dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89dd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89ddc: b               #0xb88fac
    // 0xb89de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89de0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89de4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb89de4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb89de8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89de8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89dec: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb89dec: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb89df0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89df0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89df4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89df4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89df8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89dfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89dfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89e00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89e00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89e04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89e04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb89e08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb89e08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb89e0c, size: 0x8c
    // 0xb89e0c: EnterFrame
    //     0xb89e0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb89e10: mov             fp, SP
    // 0xb89e14: AllocStack(0x10)
    //     0xb89e14: sub             SP, SP, #0x10
    // 0xb89e18: SetupParameters()
    //     0xb89e18: ldr             x0, [fp, #0x10]
    //     0xb89e1c: ldur            w2, [x0, #0x17]
    //     0xb89e20: add             x2, x2, HEAP, lsl #32
    //     0xb89e24: stur            x2, [fp, #-0x10]
    // 0xb89e28: CheckStackOverflow
    //     0xb89e28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89e2c: cmp             SP, x16
    //     0xb89e30: b.ls            #0xb89e90
    // 0xb89e34: LoadField: r0 = r2->field_13
    //     0xb89e34: ldur            w0, [x2, #0x13]
    // 0xb89e38: DecompressPointer r0
    //     0xb89e38: add             x0, x0, HEAP, lsl #32
    // 0xb89e3c: mov             x1, x0
    // 0xb89e40: stur            x0, [fp, #-8]
    // 0xb89e44: r0 = value()
    //     0xb89e44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb89e48: eor             x2, x0, #0x10
    // 0xb89e4c: ldur            x1, [fp, #-8]
    // 0xb89e50: r0 = value=()
    //     0xb89e50: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb89e54: ldur            x0, [fp, #-0x10]
    // 0xb89e58: LoadField: r3 = r0->field_f
    //     0xb89e58: ldur            w3, [x0, #0xf]
    // 0xb89e5c: DecompressPointer r3
    //     0xb89e5c: add             x3, x3, HEAP, lsl #32
    // 0xb89e60: stur            x3, [fp, #-8]
    // 0xb89e64: r1 = Function '<anonymous closure>':.
    //     0xb89e64: add             x1, PP, #0x55, lsl #12  ; [pp+0x55570] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb89e68: ldr             x1, [x1, #0x570]
    // 0xb89e6c: r2 = Null
    //     0xb89e6c: mov             x2, NULL
    // 0xb89e70: r0 = AllocateClosure()
    //     0xb89e70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89e74: ldur            x1, [fp, #-8]
    // 0xb89e78: mov             x2, x0
    // 0xb89e7c: r0 = setState()
    //     0xb89e7c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb89e80: r0 = Null
    //     0xb89e80: mov             x0, NULL
    // 0xb89e84: LeaveFrame
    //     0xb89e84: mov             SP, fp
    //     0xb89e88: ldp             fp, lr, [SP], #0x10
    // 0xb89e8c: ret
    //     0xb89e8c: ret             
    // 0xb89e90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89e90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89e94: b               #0xb89e34
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb89e98, size: 0x84
    // 0xb89e98: EnterFrame
    //     0xb89e98: stp             fp, lr, [SP, #-0x10]!
    //     0xb89e9c: mov             fp, SP
    // 0xb89ea0: AllocStack(0x10)
    //     0xb89ea0: sub             SP, SP, #0x10
    // 0xb89ea4: SetupParameters()
    //     0xb89ea4: ldr             x0, [fp, #0x18]
    //     0xb89ea8: ldur            w1, [x0, #0x17]
    //     0xb89eac: add             x1, x1, HEAP, lsl #32
    //     0xb89eb0: stur            x1, [fp, #-8]
    // 0xb89eb4: CheckStackOverflow
    //     0xb89eb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89eb8: cmp             SP, x16
    //     0xb89ebc: b.ls            #0xb89f14
    // 0xb89ec0: r1 = 1
    //     0xb89ec0: movz            x1, #0x1
    // 0xb89ec4: r0 = AllocateContext()
    //     0xb89ec4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb89ec8: mov             x1, x0
    // 0xb89ecc: ldur            x0, [fp, #-8]
    // 0xb89ed0: StoreField: r1->field_b = r0
    //     0xb89ed0: stur            w0, [x1, #0xb]
    // 0xb89ed4: ldr             x2, [fp, #0x10]
    // 0xb89ed8: StoreField: r1->field_f = r2
    //     0xb89ed8: stur            w2, [x1, #0xf]
    // 0xb89edc: LoadField: r3 = r0->field_f
    //     0xb89edc: ldur            w3, [x0, #0xf]
    // 0xb89ee0: DecompressPointer r3
    //     0xb89ee0: add             x3, x3, HEAP, lsl #32
    // 0xb89ee4: mov             x2, x1
    // 0xb89ee8: stur            x3, [fp, #-0x10]
    // 0xb89eec: r1 = Function '<anonymous closure>':.
    //     0xb89eec: add             x1, PP, #0x55, lsl #12  ; [pp+0x55578] AnonymousClosure: (0xb89f1c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb884ac)
    //     0xb89ef0: ldr             x1, [x1, #0x578]
    // 0xb89ef4: r0 = AllocateClosure()
    //     0xb89ef4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89ef8: ldur            x1, [fp, #-0x10]
    // 0xb89efc: mov             x2, x0
    // 0xb89f00: r0 = setState()
    //     0xb89f00: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb89f04: r0 = Null
    //     0xb89f04: mov             x0, NULL
    // 0xb89f08: LeaveFrame
    //     0xb89f08: mov             SP, fp
    //     0xb89f0c: ldp             fp, lr, [SP], #0x10
    // 0xb89f10: ret
    //     0xb89f10: ret             
    // 0xb89f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89f14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89f18: b               #0xb89ec0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb89f1c, size: 0x8c
    // 0xb89f1c: EnterFrame
    //     0xb89f1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb89f20: mov             fp, SP
    // 0xb89f24: AllocStack(0x8)
    //     0xb89f24: sub             SP, SP, #8
    // 0xb89f28: SetupParameters()
    //     0xb89f28: ldr             x0, [fp, #0x10]
    //     0xb89f2c: ldur            w1, [x0, #0x17]
    //     0xb89f30: add             x1, x1, HEAP, lsl #32
    // 0xb89f34: CheckStackOverflow
    //     0xb89f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb89f38: cmp             SP, x16
    //     0xb89f3c: b.ls            #0xb89fa0
    // 0xb89f40: LoadField: r0 = r1->field_b
    //     0xb89f40: ldur            w0, [x1, #0xb]
    // 0xb89f44: DecompressPointer r0
    //     0xb89f44: add             x0, x0, HEAP, lsl #32
    // 0xb89f48: LoadField: r2 = r0->field_f
    //     0xb89f48: ldur            w2, [x0, #0xf]
    // 0xb89f4c: DecompressPointer r2
    //     0xb89f4c: add             x2, x2, HEAP, lsl #32
    // 0xb89f50: LoadField: r0 = r1->field_f
    //     0xb89f50: ldur            w0, [x1, #0xf]
    // 0xb89f54: DecompressPointer r0
    //     0xb89f54: add             x0, x0, HEAP, lsl #32
    // 0xb89f58: r1 = LoadInt32Instr(r0)
    //     0xb89f58: sbfx            x1, x0, #1, #0x1f
    //     0xb89f5c: tbz             w0, #0, #0xb89f64
    //     0xb89f60: ldur            x1, [x0, #7]
    // 0xb89f64: ArrayStore: r2[0] = r1  ; List_8
    //     0xb89f64: stur            x1, [x2, #0x17]
    // 0xb89f68: LoadField: r0 = r2->field_1f
    //     0xb89f68: ldur            w0, [x2, #0x1f]
    // 0xb89f6c: DecompressPointer r0
    //     0xb89f6c: add             x0, x0, HEAP, lsl #32
    // 0xb89f70: stur            x0, [fp, #-8]
    // 0xb89f74: r1 = Function '<anonymous closure>':.
    //     0xb89f74: add             x1, PP, #0x55, lsl #12  ; [pp+0x55580] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xb89f78: ldr             x1, [x1, #0x580]
    // 0xb89f7c: r2 = Null
    //     0xb89f7c: mov             x2, NULL
    // 0xb89f80: r0 = AllocateClosure()
    //     0xb89f80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb89f84: ldur            x1, [fp, #-8]
    // 0xb89f88: mov             x2, x0
    // 0xb89f8c: r0 = forEach()
    //     0xb89f8c: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xb89f90: r0 = Null
    //     0xb89f90: mov             x0, NULL
    // 0xb89f94: LeaveFrame
    //     0xb89f94: mov             SP, fp
    //     0xb89f98: ldp             fp, lr, [SP], #0x10
    // 0xb89f9c: ret
    //     0xb89f9c: ret             
    // 0xb89fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb89fa0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb89fa4: b               #0xb89f40
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87de4, size: 0x54
    // 0xc87de4: EnterFrame
    //     0xc87de4: stp             fp, lr, [SP, #-0x10]!
    //     0xc87de8: mov             fp, SP
    // 0xc87dec: CheckStackOverflow
    //     0xc87dec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87df0: cmp             SP, x16
    //     0xc87df4: b.ls            #0xc87e24
    // 0xc87df8: LoadField: r0 = r1->field_13
    //     0xc87df8: ldur            w0, [x1, #0x13]
    // 0xc87dfc: DecompressPointer r0
    //     0xc87dfc: add             x0, x0, HEAP, lsl #32
    // 0xc87e00: r16 = Sentinel
    //     0xc87e00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87e04: cmp             w0, w16
    // 0xc87e08: b.eq            #0xc87e2c
    // 0xc87e0c: mov             x1, x0
    // 0xc87e10: r0 = dispose()
    //     0xc87e10: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87e14: r0 = Null
    //     0xc87e14: mov             x0, NULL
    // 0xc87e18: LeaveFrame
    //     0xc87e18: mov             SP, fp
    //     0xc87e1c: ldp             fp, lr, [SP], #0x10
    // 0xc87e20: ret
    //     0xc87e20: ret             
    // 0xc87e24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87e24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87e28: b               #0xc87df8
    // 0xc87e2c: r9 = _pageController
    //     0xc87e2c: add             x9, PP, #0x55, lsl #12  ; [pp+0x554e8] Field <_ProductTestimonialCarouselState@1624278349._pageController@1624278349>: late (offset: 0x14)
    //     0xc87e30: ldr             x9, [x9, #0x4e8]
    // 0xc87e34: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87e34: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4054, size: 0x2c, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f8f8, size: 0x84
    // 0xc7f8f8: EnterFrame
    //     0xc7f8f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f8fc: mov             fp, SP
    // 0xc7f900: AllocStack(0x18)
    //     0xc7f900: sub             SP, SP, #0x18
    // 0xc7f904: CheckStackOverflow
    //     0xc7f904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f908: cmp             SP, x16
    //     0xc7f90c: b.ls            #0xc7f974
    // 0xc7f910: r1 = <ProductTestimonialCarousel>
    //     0xc7f910: add             x1, PP, #0x48, lsl #12  ; [pp+0x48748] TypeArguments: <ProductTestimonialCarousel>
    //     0xc7f914: ldr             x1, [x1, #0x748]
    // 0xc7f918: r0 = _ProductTestimonialCarouselState()
    //     0xc7f918: bl              #0xc7f97c  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc7f91c: mov             x1, x0
    // 0xc7f920: r0 = Sentinel
    //     0xc7f920: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f924: stur            x1, [fp, #-8]
    // 0xc7f928: StoreField: r1->field_13 = r0
    //     0xc7f928: stur            w0, [x1, #0x13]
    // 0xc7f92c: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7f92c: stur            xzr, [x1, #0x17]
    // 0xc7f930: r16 = <int, RxBool>
    //     0xc7f930: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7f934: ldr             x16, [x16, #0x298]
    // 0xc7f938: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7f93c: stp             lr, x16, [SP]
    // 0xc7f940: r0 = Map._fromLiteral()
    //     0xc7f940: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7f944: ldur            x1, [fp, #-8]
    // 0xc7f948: StoreField: r1->field_1f = r0
    //     0xc7f948: stur            w0, [x1, #0x1f]
    //     0xc7f94c: ldurb           w16, [x1, #-1]
    //     0xc7f950: ldurb           w17, [x0, #-1]
    //     0xc7f954: and             x16, x17, x16, lsr #2
    //     0xc7f958: tst             x16, HEAP, lsr #32
    //     0xc7f95c: b.eq            #0xc7f964
    //     0xc7f960: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7f964: mov             x0, x1
    // 0xc7f968: LeaveFrame
    //     0xc7f968: mov             SP, fp
    //     0xc7f96c: ldp             fp, lr, [SP], #0x10
    // 0xc7f970: ret
    //     0xc7f970: ret             
    // 0xc7f974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f974: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f978: b               #0xc7f910
  }
}
