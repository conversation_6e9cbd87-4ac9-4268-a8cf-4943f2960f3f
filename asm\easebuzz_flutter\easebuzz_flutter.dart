// lib: , url: package:easebuzz_flutter/easebuzz_flutter.dart

// class id: 1049621, size: 0x8
class :: {
}

// class id: 4969, size: 0x8, field offset: 0x8
class EasebuzzFlutter extends Object {

  static late final EasebuzzFlutter _instance; // offset: 0xddc

  _ payWithEasebuzz(/* No info */) async {
    // ** addr: 0x12d85e8, size: 0x94
    // 0x12d85e8: EnterFrame
    //     0x12d85e8: stp             fp, lr, [SP, #-0x10]!
    //     0x12d85ec: mov             fp, SP
    // 0x12d85f0: AllocStack(0x18)
    //     0x12d85f0: sub             SP, SP, #0x18
    // 0x12d85f4: SetupParameters(EasebuzzFlutter this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x12d85f4: stur            NULL, [fp, #-8]
    //     0x12d85f8: stur            x1, [fp, #-0x10]
    //     0x12d85fc: stur            x2, [fp, #-0x18]
    // 0x12d8600: CheckStackOverflow
    //     0x12d8600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12d8604: cmp             SP, x16
    //     0x12d8608: b.ls            #0x12d8674
    // 0x12d860c: InitAsync() -> Future<String?>
    //     0x12d860c: ldr             x0, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    //     0x12d8610: bl              #0x6326e0  ; InitAsyncStub
    // 0x12d8614: r0 = InitLateStaticField(0xed4) // [package:easebuzz_flutter/easebuzz_flutter_platform_interface.dart] EasebuzzFlutterPlatform::_instance
    //     0x12d8614: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12d8618: ldr             x0, [x0, #0x1da8]
    //     0x12d861c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12d8620: cmp             w0, w16
    //     0x12d8624: b.ne            #0x12d8634
    //     0x12d8628: add             x2, PP, #0x3a, lsl #12  ; [pp+0x3a728] Field <EasebuzzFlutterPlatform._instance@1789398441>: static late (offset: 0xed4)
    //     0x12d862c: ldr             x2, [x2, #0x728]
    //     0x12d8630: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x12d8634: mov             x1, x0
    // 0x12d8638: ldur            x2, [fp, #-0x18]
    // 0x12d863c: r3 = "production"
    //     0x12d863c: add             x3, PP, #0x3a, lsl #12  ; [pp+0x3a730] "production"
    //     0x12d8640: ldr             x3, [x3, #0x730]
    // 0x12d8644: r0 = payWithEasebuzz()
    //     0x12d8644: bl              #0x12d867c  ; [package:easebuzz_flutter/easebuzz_flutter_method_channel.dart] MethodChannelEasebuzzFlutter::payWithEasebuzz
    // 0x12d8648: mov             x1, x0
    // 0x12d864c: stur            x1, [fp, #-0x18]
    // 0x12d8650: r0 = Await()
    //     0x12d8650: bl              #0x63248c  ; AwaitStub
    // 0x12d8654: cmp             w0, NULL
    // 0x12d8658: b.eq            #0x12d866c
    // 0x12d865c: mov             x1, x0
    // 0x12d8660: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12d8660: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12d8664: r0 = jsonEncode()
    //     0x12d8664: bl              #0x882de8  ; [dart:convert] ::jsonEncode
    // 0x12d8668: r0 = ReturnAsyncNotFuture()
    //     0x12d8668: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x12d866c: r0 = Null
    //     0x12d866c: mov             x0, NULL
    // 0x12d8670: r0 = ReturnAsyncNotFuture()
    //     0x12d8670: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x12d8674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12d8674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12d8678: b               #0x12d860c
  }
  static EasebuzzFlutter _instance() {
    // ** addr: 0x15fb59c, size: 0x18
    // 0x15fb59c: EnterFrame
    //     0x15fb59c: stp             fp, lr, [SP, #-0x10]!
    //     0x15fb5a0: mov             fp, SP
    // 0x15fb5a4: r0 = EasebuzzFlutter()
    //     0x15fb5a4: bl              #0x15fb5b4  ; AllocateEasebuzzFlutterStub -> EasebuzzFlutter (size=0x8)
    // 0x15fb5a8: LeaveFrame
    //     0x15fb5a8: mov             SP, fp
    //     0x15fb5ac: ldp             fp, lr, [SP], #0x10
    // 0x15fb5b0: ret
    //     0x15fb5b0: ret             
  }
}
