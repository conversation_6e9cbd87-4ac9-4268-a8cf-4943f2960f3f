// lib: , url: package:dio/src/utils.dart

// class id: 1049619, size: 0x8
class :: {

  static Map<String, Y0> caseInsensitiveKeyMap<Y0>([Map<String, Y0>?]) {
    // ** addr: 0x8626d4, size: 0x12c
    // 0x8626d4: EnterFrame
    //     0x8626d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8626d8: mov             fp, SP
    // 0x8626dc: AllocStack(0x20)
    //     0x8626dc: sub             SP, SP, #0x20
    // 0x8626e0: SetupParameters([dynamic _ = Null /* r0, fp-0x10 */])
    //     0x8626e0: ldur            w0, [x4, #0x13]
    //     0x8626e4: cmp             w0, #2
    //     0x8626e8: b.lt            #0x8626fc
    //     0x8626ec: add             x1, fp, w0, sxtw #2
    //     0x8626f0: ldr             x1, [x1, #8]
    //     0x8626f4: mov             x0, x1
    //     0x8626f8: b               #0x862700
    //     0x8626fc: mov             x0, NULL
    //     0x862700: stur            x0, [fp, #-0x10]
    //     0x862704: ldur            w1, [x4, #0xf]
    //     0x862708: cbnz            w1, #0x862714
    //     0x86270c: mov             x3, NULL
    //     0x862710: b               #0x862724
    //     0x862714: ldur            w1, [x4, #0x17]
    //     0x862718: add             x2, fp, w1, sxtw #2
    //     0x86271c: ldr             x2, [x2, #0x10]
    //     0x862720: mov             x3, x2
    //     0x862724: stur            x3, [fp, #-8]
    // 0x862728: CheckStackOverflow
    //     0x862728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86272c: cmp             SP, x16
    //     0x862730: b.ls            #0x8627f8
    // 0x862734: r1 = Function '<anonymous closure>': static.
    //     0x862734: add             x1, PP, #8, lsl #12  ; [pp+0x8b38] AnonymousClosure: static (0x862ab4), in [package:dio/src/utils.dart] ::caseInsensitiveKeyMap (0x8626d4)
    //     0x862738: ldr             x1, [x1, #0xb38]
    // 0x86273c: r2 = Null
    //     0x86273c: mov             x2, NULL
    // 0x862740: r0 = AllocateClosure()
    //     0x862740: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x862744: mov             x3, x0
    // 0x862748: ldur            x0, [fp, #-8]
    // 0x86274c: stur            x3, [fp, #-0x18]
    // 0x862750: StoreField: r3->field_b = r0
    //     0x862750: stur            w0, [x3, #0xb]
    // 0x862754: r1 = Function '<anonymous closure>': static.
    //     0x862754: add             x1, PP, #8, lsl #12  ; [pp+0x8b40] AnonymousClosure: static (0x862a48), in [package:dio/src/utils.dart] ::caseInsensitiveKeyMap (0x8626d4)
    //     0x862758: ldr             x1, [x1, #0xb40]
    // 0x86275c: r2 = Null
    //     0x86275c: mov             x2, NULL
    // 0x862760: r0 = AllocateClosure()
    //     0x862760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x862764: ldur            x1, [fp, #-8]
    // 0x862768: stur            x0, [fp, #-0x20]
    // 0x86276c: StoreField: r0->field_b = r1
    //     0x86276c: stur            w1, [x0, #0xb]
    // 0x862770: r2 = Null
    //     0x862770: mov             x2, NULL
    // 0x862774: r3 = <String, Y0>
    //     0x862774: add             x3, PP, #8, lsl #12  ; [pp+0x8b48] TypeArguments: <String, Y0>
    //     0x862778: ldr             x3, [x3, #0xb48]
    // 0x86277c: r30 = InstantiateTypeArgumentsStub
    //     0x86277c: ldr             lr, [PP, #0x208]  ; [pp+0x208] Stub: InstantiateTypeArguments (0x600f4c)
    // 0x862780: LoadField: r30 = r30->field_7
    //     0x862780: ldur            lr, [lr, #7]
    // 0x862784: blr             lr
    // 0x862788: mov             x1, x0
    // 0x86278c: ldur            x2, [fp, #-0x18]
    // 0x862790: ldur            x3, [fp, #-0x20]
    // 0x862794: r0 = LinkedHashMap()
    //     0x862794: bl              #0x862800  ; [dart:collection] LinkedHashMap::LinkedHashMap
    // 0x862798: mov             x3, x0
    // 0x86279c: ldur            x2, [fp, #-0x10]
    // 0x8627a0: stur            x3, [fp, #-8]
    // 0x8627a4: cmp             w2, NULL
    // 0x8627a8: b.eq            #0x8627e8
    // 0x8627ac: r0 = LoadClassIdInstr(r2)
    //     0x8627ac: ldur            x0, [x2, #-1]
    //     0x8627b0: ubfx            x0, x0, #0xc, #0x14
    // 0x8627b4: mov             x1, x2
    // 0x8627b8: r0 = GDT[cid_x0 + 0x76e]()
    //     0x8627b8: add             lr, x0, #0x76e
    //     0x8627bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8627c0: blr             lr
    // 0x8627c4: tbnz            w0, #4, #0x8627e8
    // 0x8627c8: ldur            x3, [fp, #-8]
    // 0x8627cc: r0 = LoadClassIdInstr(r3)
    //     0x8627cc: ldur            x0, [x3, #-1]
    //     0x8627d0: ubfx            x0, x0, #0xc, #0x14
    // 0x8627d4: mov             x1, x3
    // 0x8627d8: ldur            x2, [fp, #-0x10]
    // 0x8627dc: r0 = GDT[cid_x0 + -0xdb3]()
    //     0x8627dc: sub             lr, x0, #0xdb3
    //     0x8627e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8627e4: blr             lr
    // 0x8627e8: ldur            x0, [fp, #-8]
    // 0x8627ec: LeaveFrame
    //     0x8627ec: mov             SP, fp
    //     0x8627f0: ldp             fp, lr, [SP], #0x10
    // 0x8627f4: ret
    //     0x8627f4: ret             
    // 0x8627f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8627f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8627fc: b               #0x862734
  }
  [closure] static int <anonymous closure>(dynamic, String) {
    // ** addr: 0x862a48, size: 0x6c
    // 0x862a48: EnterFrame
    //     0x862a48: stp             fp, lr, [SP, #-0x10]!
    //     0x862a4c: mov             fp, SP
    // 0x862a50: AllocStack(0x8)
    //     0x862a50: sub             SP, SP, #8
    // 0x862a54: CheckStackOverflow
    //     0x862a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862a58: cmp             SP, x16
    //     0x862a5c: b.ls            #0x862aac
    // 0x862a60: ldr             x0, [fp, #0x10]
    // 0x862a64: r1 = LoadClassIdInstr(r0)
    //     0x862a64: ldur            x1, [x0, #-1]
    //     0x862a68: ubfx            x1, x1, #0xc, #0x14
    // 0x862a6c: str             x0, [SP]
    // 0x862a70: mov             x0, x1
    // 0x862a74: r0 = GDT[cid_x0 + -0xffa]()
    //     0x862a74: sub             lr, x0, #0xffa
    //     0x862a78: ldr             lr, [x21, lr, lsl #3]
    //     0x862a7c: blr             lr
    // 0x862a80: r1 = LoadClassIdInstr(r0)
    //     0x862a80: ldur            x1, [x0, #-1]
    //     0x862a84: ubfx            x1, x1, #0xc, #0x14
    // 0x862a88: str             x0, [SP]
    // 0x862a8c: mov             x0, x1
    // 0x862a90: r0 = GDT[cid_x0 + 0x4310]()
    //     0x862a90: movz            x17, #0x4310
    //     0x862a94: add             lr, x0, x17
    //     0x862a98: ldr             lr, [x21, lr, lsl #3]
    //     0x862a9c: blr             lr
    // 0x862aa0: LeaveFrame
    //     0x862aa0: mov             SP, fp
    //     0x862aa4: ldp             fp, lr, [SP], #0x10
    // 0x862aa8: ret
    //     0x862aa8: ret             
    // 0x862aac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862aac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862ab0: b               #0x862a60
  }
  [closure] static bool <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x862ab4, size: 0x98
    // 0x862ab4: EnterFrame
    //     0x862ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x862ab8: mov             fp, SP
    // 0x862abc: AllocStack(0x18)
    //     0x862abc: sub             SP, SP, #0x18
    // 0x862ac0: CheckStackOverflow
    //     0x862ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862ac4: cmp             SP, x16
    //     0x862ac8: b.ls            #0x862b44
    // 0x862acc: ldr             x0, [fp, #0x18]
    // 0x862ad0: r1 = LoadClassIdInstr(r0)
    //     0x862ad0: ldur            x1, [x0, #-1]
    //     0x862ad4: ubfx            x1, x1, #0xc, #0x14
    // 0x862ad8: str             x0, [SP]
    // 0x862adc: mov             x0, x1
    // 0x862ae0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x862ae0: sub             lr, x0, #0xffa
    //     0x862ae4: ldr             lr, [x21, lr, lsl #3]
    //     0x862ae8: blr             lr
    // 0x862aec: mov             x1, x0
    // 0x862af0: ldr             x0, [fp, #0x10]
    // 0x862af4: stur            x1, [fp, #-8]
    // 0x862af8: r2 = LoadClassIdInstr(r0)
    //     0x862af8: ldur            x2, [x0, #-1]
    //     0x862afc: ubfx            x2, x2, #0xc, #0x14
    // 0x862b00: str             x0, [SP]
    // 0x862b04: mov             x0, x2
    // 0x862b08: r0 = GDT[cid_x0 + -0xffa]()
    //     0x862b08: sub             lr, x0, #0xffa
    //     0x862b0c: ldr             lr, [x21, lr, lsl #3]
    //     0x862b10: blr             lr
    // 0x862b14: mov             x1, x0
    // 0x862b18: ldur            x0, [fp, #-8]
    // 0x862b1c: r2 = LoadClassIdInstr(r0)
    //     0x862b1c: ldur            x2, [x0, #-1]
    //     0x862b20: ubfx            x2, x2, #0xc, #0x14
    // 0x862b24: stp             x1, x0, [SP]
    // 0x862b28: mov             x0, x2
    // 0x862b2c: mov             lr, x0
    // 0x862b30: ldr             lr, [x21, lr, lsl #3]
    // 0x862b34: blr             lr
    // 0x862b38: LeaveFrame
    //     0x862b38: mov             SP, fp
    //     0x862b3c: ldp             fp, lr, [SP], #0x10
    // 0x862b40: ret
    //     0x862b40: ret             
    // 0x862b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862b44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862b48: b               #0x862acc
  }
  static _ encodeMap(/* No info */) {
    // ** addr: 0x8803cc, size: 0x280
    // 0x8803cc: EnterFrame
    //     0x8803cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8803d0: mov             fp, SP
    // 0x8803d4: AllocStack(0x40)
    //     0x8803d4: sub             SP, SP, #0x40
    // 0x8803d8: SetupParameters(dynamic _ /* r1 => r1, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */, {dynamic encode = true /* r5, fp-0x10 */, dynamic isQuery = false /* r0, fp-0x8 */})
    //     0x8803d8: stur            x1, [fp, #-0x18]
    //     0x8803dc: stur            x2, [fp, #-0x20]
    //     0x8803e0: ldur            w0, [x4, #0x13]
    //     0x8803e4: ldur            w3, [x4, #0x1f]
    //     0x8803e8: add             x3, x3, HEAP, lsl #32
    //     0x8803ec: add             x16, PP, #0xa, lsl #12  ; [pp+0xa3f8] "encode"
    //     0x8803f0: ldr             x16, [x16, #0x3f8]
    //     0x8803f4: cmp             w3, w16
    //     0x8803f8: b.ne            #0x88041c
    //     0x8803fc: ldur            w3, [x4, #0x23]
    //     0x880400: add             x3, x3, HEAP, lsl #32
    //     0x880404: sub             w5, w0, w3
    //     0x880408: add             x3, fp, w5, sxtw #2
    //     0x88040c: ldr             x3, [x3, #8]
    //     0x880410: mov             x5, x3
    //     0x880414: movz            x3, #0x1
    //     0x880418: b               #0x880424
    //     0x88041c: add             x5, NULL, #0x20  ; true
    //     0x880420: movz            x3, #0
    //     0x880424: stur            x5, [fp, #-0x10]
    //     0x880428: lsl             x6, x3, #1
    //     0x88042c: lsl             w3, w6, #1
    //     0x880430: add             w6, w3, #8
    //     0x880434: add             x16, x4, w6, sxtw #1
    //     0x880438: ldur            w7, [x16, #0xf]
    //     0x88043c: add             x7, x7, HEAP, lsl #32
    //     0x880440: add             x16, PP, #0xa, lsl #12  ; [pp+0xa400] "isQuery"
    //     0x880444: ldr             x16, [x16, #0x400]
    //     0x880448: cmp             w7, w16
    //     0x88044c: b.ne            #0x880470
    //     0x880450: add             w6, w3, #0xa
    //     0x880454: add             x16, x4, w6, sxtw #1
    //     0x880458: ldur            w3, [x16, #0xf]
    //     0x88045c: add             x3, x3, HEAP, lsl #32
    //     0x880460: sub             w4, w0, w3
    //     0x880464: add             x0, fp, w4, sxtw #2
    //     0x880468: ldr             x0, [x0, #8]
    //     0x88046c: b               #0x880474
    //     0x880470: add             x0, NULL, #0x30  ; false
    //     0x880474: stur            x0, [fp, #-8]
    // 0x880478: CheckStackOverflow
    //     0x880478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88047c: cmp             SP, x16
    //     0x880480: b.ls            #0x880644
    // 0x880484: r1 = 9
    //     0x880484: movz            x1, #0x9
    // 0x880488: r0 = AllocateContext()
    //     0x880488: bl              #0x16f6108  ; AllocateContextStub
    // 0x88048c: mov             x1, x0
    // 0x880490: ldur            x0, [fp, #-0x20]
    // 0x880494: stur            x1, [fp, #-0x28]
    // 0x880498: StoreField: r1->field_f = r0
    //     0x880498: stur            w0, [x1, #0xf]
    // 0x88049c: ldur            x0, [fp, #-8]
    // 0x8804a0: StoreField: r1->field_13 = r0
    //     0x8804a0: stur            w0, [x1, #0x13]
    // 0x8804a4: r0 = StringBuffer()
    //     0x8804a4: bl              #0x620890  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x8804a8: stur            x0, [fp, #-8]
    // 0x8804ac: r16 = ""
    //     0x8804ac: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8804b0: str             x16, [SP]
    // 0x8804b4: mov             x1, x0
    // 0x8804b8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8804b8: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8804bc: r0 = StringBuffer()
    //     0x8804bc: bl              #0x6200bc  ; [dart:core] StringBuffer::StringBuffer
    // 0x8804c0: ldur            x0, [fp, #-8]
    // 0x8804c4: ldur            x3, [fp, #-0x28]
    // 0x8804c8: ArrayStore: r3[0] = r0  ; List_4
    //     0x8804c8: stur            w0, [x3, #0x17]
    //     0x8804cc: ldurb           w16, [x3, #-1]
    //     0x8804d0: ldurb           w17, [x0, #-1]
    //     0x8804d4: and             x16, x17, x16, lsr #2
    //     0x8804d8: tst             x16, HEAP, lsr #32
    //     0x8804dc: b.eq            #0x8804e4
    //     0x8804e0: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8804e4: r0 = true
    //     0x8804e4: add             x0, NULL, #0x20  ; true
    // 0x8804e8: StoreField: r3->field_1b = r0
    //     0x8804e8: stur            w0, [x3, #0x1b]
    // 0x8804ec: LoadField: r1 = r3->field_13
    //     0x8804ec: ldur            w1, [x3, #0x13]
    // 0x8804f0: DecompressPointer r1
    //     0x8804f0: add             x1, x1, HEAP, lsl #32
    // 0x8804f4: tbnz            w1, #4, #0x880500
    // 0x8804f8: ldur            x2, [fp, #-0x10]
    // 0x8804fc: b               #0x880508
    // 0x880500: ldur            x2, [fp, #-0x10]
    // 0x880504: tbz             w2, #4, #0x880510
    // 0x880508: r0 = "["
    //     0x880508: ldr             x0, [PP, #0x1230]  ; [pp+0x1230] "["
    // 0x88050c: b               #0x880518
    // 0x880510: r0 = "%5B"
    //     0x880510: add             x0, PP, #0xa, lsl #12  ; [pp+0xa408] "%5B"
    //     0x880514: ldr             x0, [x0, #0x408]
    // 0x880518: StoreField: r3->field_1f = r0
    //     0x880518: stur            w0, [x3, #0x1f]
    //     0x88051c: ldurb           w16, [x3, #-1]
    //     0x880520: ldurb           w17, [x0, #-1]
    //     0x880524: and             x16, x17, x16, lsr #2
    //     0x880528: tst             x16, HEAP, lsr #32
    //     0x88052c: b.eq            #0x880534
    //     0x880530: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x880534: tbz             w1, #4, #0x88053c
    // 0x880538: tbz             w2, #4, #0x880544
    // 0x88053c: r0 = "]"
    //     0x88053c: ldr             x0, [PP, #0x1238]  ; [pp+0x1238] "]"
    // 0x880540: b               #0x88054c
    // 0x880544: r0 = "%5D"
    //     0x880544: add             x0, PP, #0xa, lsl #12  ; [pp+0xa410] "%5D"
    //     0x880548: ldr             x0, [x0, #0x410]
    // 0x88054c: StoreField: r3->field_23 = r0
    //     0x88054c: stur            w0, [x3, #0x23]
    //     0x880550: ldurb           w16, [x3, #-1]
    //     0x880554: ldurb           w17, [x0, #-1]
    //     0x880558: and             x16, x17, x16, lsr #2
    //     0x88055c: tst             x16, HEAP, lsr #32
    //     0x880560: b.eq            #0x880568
    //     0x880564: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x880568: tbnz            w2, #4, #0x880578
    // 0x88056c: r0 = Closure: (String, {Encoding encoding}) => String from Function 'encodeQueryComponent': static.
    //     0x88056c: add             x0, PP, #0xa, lsl #12  ; [pp+0xa418] Closure: (String, {Encoding encoding}) => String from Function 'encodeQueryComponent': static. (0x7fa737623ba4)
    //     0x880570: ldr             x0, [x0, #0x418]
    // 0x880574: b               #0x88058c
    // 0x880578: r1 = Function '<anonymous closure>': static.
    //     0x880578: add             x1, PP, #0xa, lsl #12  ; [pp+0xa420] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x88057c: ldr             x1, [x1, #0x420]
    // 0x880580: r2 = Null
    //     0x880580: mov             x2, NULL
    // 0x880584: r0 = AllocateClosure()
    //     0x880584: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x880588: ldur            x3, [fp, #-0x28]
    // 0x88058c: StoreField: r3->field_27 = r0
    //     0x88058c: stur            w0, [x3, #0x27]
    //     0x880590: ldurb           w16, [x3, #-1]
    //     0x880594: ldurb           w17, [x0, #-1]
    //     0x880598: and             x16, x17, x16, lsr #2
    //     0x88059c: tst             x16, HEAP, lsr #32
    //     0x8805a0: b.eq            #0x8805a8
    //     0x8805a4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8805a8: mov             x2, x3
    // 0x8805ac: r1 = Function 'maybeEncode': static.
    //     0x8805ac: add             x1, PP, #0xa, lsl #12  ; [pp+0xa428] AnonymousClosure: static (0x88106c), in [package:dio/src/utils.dart] ::encodeMap (0x8803cc)
    //     0x8805b0: ldr             x1, [x1, #0x428]
    // 0x8805b4: r0 = AllocateClosure()
    //     0x8805b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8805b8: ldur            x3, [fp, #-0x28]
    // 0x8805bc: StoreField: r3->field_2b = r0
    //     0x8805bc: stur            w0, [x3, #0x2b]
    //     0x8805c0: ldurb           w16, [x3, #-1]
    //     0x8805c4: ldurb           w17, [x0, #-1]
    //     0x8805c8: and             x16, x17, x16, lsr #2
    //     0x8805cc: tst             x16, HEAP, lsr #32
    //     0x8805d0: b.eq            #0x8805d8
    //     0x8805d4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8805d8: mov             x2, x3
    // 0x8805dc: r1 = Function 'urlEncode': static.
    //     0x8805dc: add             x1, PP, #0xa, lsl #12  ; [pp+0xa430] AnonymousClosure: static (0x88064c), in [package:dio/src/utils.dart] ::encodeMap (0x8803cc)
    //     0x8805e0: ldr             x1, [x1, #0x430]
    // 0x8805e4: r0 = AllocateClosure()
    //     0x8805e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8805e8: mov             x2, x0
    // 0x8805ec: ldur            x1, [fp, #-0x28]
    // 0x8805f0: StoreField: r1->field_2f = r0
    //     0x8805f0: stur            w0, [x1, #0x2f]
    //     0x8805f4: ldurb           w16, [x1, #-1]
    //     0x8805f8: ldurb           w17, [x0, #-1]
    //     0x8805fc: and             x16, x17, x16, lsr #2
    //     0x880600: tst             x16, HEAP, lsr #32
    //     0x880604: b.eq            #0x88060c
    //     0x880608: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x88060c: ldur            x16, [fp, #-0x18]
    // 0x880610: stp             x16, x2, [SP, #8]
    // 0x880614: r16 = ""
    //     0x880614: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x880618: str             x16, [SP]
    // 0x88061c: mov             x0, x2
    // 0x880620: ClosureCall
    //     0x880620: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x880624: ldur            x2, [x0, #0x1f]
    //     0x880628: blr             x2
    // 0x88062c: ldur            x16, [fp, #-8]
    // 0x880630: str             x16, [SP]
    // 0x880634: r0 = toString()
    //     0x880634: bl              #0x1558aa8  ; [dart:core] StringBuffer::toString
    // 0x880638: LeaveFrame
    //     0x880638: mov             SP, fp
    //     0x88063c: ldp             fp, lr, [SP], #0x10
    // 0x880640: ret
    //     0x880640: ret             
    // 0x880644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x880644: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x880648: b               #0x880484
  }
  [closure] static void urlEncode(dynamic, Object?, String) {
    // ** addr: 0x88064c, size: 0x6d4
    // 0x88064c: EnterFrame
    //     0x88064c: stp             fp, lr, [SP, #-0x10]!
    //     0x880650: mov             fp, SP
    // 0x880654: AllocStack(0x80)
    //     0x880654: sub             SP, SP, #0x80
    // 0x880658: SetupParameters()
    //     0x880658: ldr             x0, [fp, #0x20]
    //     0x88065c: ldur            w1, [x0, #0x17]
    //     0x880660: add             x1, x1, HEAP, lsl #32
    //     0x880664: stur            x1, [fp, #-8]
    // 0x880668: CheckStackOverflow
    //     0x880668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88066c: cmp             SP, x16
    //     0x880670: b.ls            #0x880d10
    // 0x880674: r1 = 1
    //     0x880674: movz            x1, #0x1
    // 0x880678: r0 = AllocateContext()
    //     0x880678: bl              #0x16f6108  ; AllocateContextStub
    // 0x88067c: mov             x4, x0
    // 0x880680: ldur            x3, [fp, #-8]
    // 0x880684: stur            x4, [fp, #-0x10]
    // 0x880688: StoreField: r4->field_b = r3
    //     0x880688: stur            w3, [x4, #0xb]
    // 0x88068c: ldr             x5, [fp, #0x10]
    // 0x880690: StoreField: r4->field_f = r5
    //     0x880690: stur            w5, [x4, #0xf]
    // 0x880694: ldr             x0, [fp, #0x18]
    // 0x880698: r2 = Null
    //     0x880698: mov             x2, NULL
    // 0x88069c: r1 = Null
    //     0x88069c: mov             x1, NULL
    // 0x8806a0: cmp             w0, NULL
    // 0x8806a4: b.eq            #0x880748
    // 0x8806a8: branchIfSmi(r0, 0x880748)
    //     0x8806a8: tbz             w0, #0, #0x880748
    // 0x8806ac: r3 = LoadClassIdInstr(r0)
    //     0x8806ac: ldur            x3, [x0, #-1]
    //     0x8806b0: ubfx            x3, x3, #0xc, #0x14
    // 0x8806b4: r17 = 6676
    //     0x8806b4: movz            x17, #0x1a14
    // 0x8806b8: cmp             x3, x17
    // 0x8806bc: b.eq            #0x880750
    // 0x8806c0: sub             x3, x3, #0x5a
    // 0x8806c4: cmp             x3, #2
    // 0x8806c8: b.ls            #0x880750
    // 0x8806cc: r4 = LoadClassIdInstr(r0)
    //     0x8806cc: ldur            x4, [x0, #-1]
    //     0x8806d0: ubfx            x4, x4, #0xc, #0x14
    // 0x8806d4: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x8806d8: ldr             x3, [x3, #0x18]
    // 0x8806dc: ldr             x3, [x3, x4, lsl #3]
    // 0x8806e0: LoadField: r3 = r3->field_2b
    //     0x8806e0: ldur            w3, [x3, #0x2b]
    // 0x8806e4: DecompressPointer r3
    //     0x8806e4: add             x3, x3, HEAP, lsl #32
    // 0x8806e8: cmp             w3, NULL
    // 0x8806ec: b.eq            #0x880748
    // 0x8806f0: LoadField: r3 = r3->field_f
    //     0x8806f0: ldur            w3, [x3, #0xf]
    // 0x8806f4: lsr             x3, x3, #3
    // 0x8806f8: r17 = 6676
    //     0x8806f8: movz            x17, #0x1a14
    // 0x8806fc: cmp             x3, x17
    // 0x880700: b.eq            #0x880750
    // 0x880704: r3 = SubtypeTestCache
    //     0x880704: add             x3, PP, #0xa, lsl #12  ; [pp+0xa438] SubtypeTestCache
    //     0x880708: ldr             x3, [x3, #0x438]
    // 0x88070c: r30 = Subtype1TestCacheStub
    //     0x88070c: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x880710: LoadField: r30 = r30->field_7
    //     0x880710: ldur            lr, [lr, #7]
    // 0x880714: blr             lr
    // 0x880718: cmp             w7, NULL
    // 0x88071c: b.eq            #0x880728
    // 0x880720: tbnz            w7, #4, #0x880748
    // 0x880724: b               #0x880750
    // 0x880728: r8 = List
    //     0x880728: add             x8, PP, #0xa, lsl #12  ; [pp+0xa440] Type: List
    //     0x88072c: ldr             x8, [x8, #0x440]
    // 0x880730: r3 = SubtypeTestCache
    //     0x880730: add             x3, PP, #0xa, lsl #12  ; [pp+0xa448] SubtypeTestCache
    //     0x880734: ldr             x3, [x3, #0x448]
    // 0x880738: r30 = InstanceOfStub
    //     0x880738: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x88073c: LoadField: r30 = r30->field_7
    //     0x88073c: ldur            lr, [lr, #7]
    // 0x880740: blr             lr
    // 0x880744: b               #0x880754
    // 0x880748: r0 = false
    //     0x880748: add             x0, NULL, #0x30  ; false
    // 0x88074c: b               #0x880754
    // 0x880750: r0 = true
    //     0x880750: add             x0, NULL, #0x20  ; true
    // 0x880754: tbnz            w0, #4, #0x880b60
    // 0x880758: ldur            x3, [fp, #-8]
    // 0x88075c: LoadField: r1 = r3->field_2f
    //     0x88075c: ldur            w1, [x3, #0x2f]
    // 0x880760: DecompressPointer r1
    //     0x880760: add             x1, x1, HEAP, lsl #32
    // 0x880764: stur            x1, [fp, #-0x40]
    // 0x880768: LoadField: r0 = r3->field_2b
    //     0x880768: ldur            w0, [x3, #0x2b]
    // 0x88076c: DecompressPointer r0
    //     0x88076c: add             x0, x0, HEAP, lsl #32
    // 0x880770: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x880770: ldur            w2, [x0, #0x17]
    // 0x880774: DecompressPointer r2
    //     0x880774: add             x2, x2, HEAP, lsl #32
    // 0x880778: stur            x2, [fp, #-0x38]
    // 0x88077c: LoadField: r4 = r2->field_27
    //     0x88077c: ldur            w4, [x2, #0x27]
    // 0x880780: DecompressPointer r4
    //     0x880780: add             x4, x4, HEAP, lsl #32
    // 0x880784: stur            x4, [fp, #-0x30]
    // 0x880788: LoadField: r5 = r3->field_1f
    //     0x880788: ldur            w5, [x3, #0x1f]
    // 0x88078c: DecompressPointer r5
    //     0x88078c: add             x5, x5, HEAP, lsl #32
    // 0x880790: stur            x5, [fp, #-0x28]
    // 0x880794: LoadField: r6 = r3->field_23
    //     0x880794: ldur            w6, [x3, #0x23]
    // 0x880798: DecompressPointer r6
    //     0x880798: add             x6, x6, HEAP, lsl #32
    // 0x88079c: stur            x6, [fp, #-0x20]
    // 0x8807a0: r8 = 0
    //     0x8807a0: movz            x8, #0
    // 0x8807a4: ldr             x7, [fp, #0x18]
    // 0x8807a8: ldur            x3, [fp, #-0x10]
    // 0x8807ac: stur            x8, [fp, #-0x18]
    // 0x8807b0: CheckStackOverflow
    //     0x8807b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8807b4: cmp             SP, x16
    //     0x8807b8: b.ls            #0x880d18
    // 0x8807bc: r0 = LoadClassIdInstr(r7)
    //     0x8807bc: ldur            x0, [x7, #-1]
    //     0x8807c0: ubfx            x0, x0, #0xc, #0x14
    // 0x8807c4: str             x7, [SP]
    // 0x8807c8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x8807c8: movz            x17, #0xc898
    //     0x8807cc: add             lr, x0, x17
    //     0x8807d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8807d4: blr             lr
    // 0x8807d8: r1 = LoadInt32Instr(r0)
    //     0x8807d8: sbfx            x1, x0, #1, #0x1f
    //     0x8807dc: tbz             w0, #0, #0x8807e4
    //     0x8807e0: ldur            x1, [x0, #7]
    // 0x8807e4: ldur            x2, [fp, #-0x18]
    // 0x8807e8: cmp             x2, x1
    // 0x8807ec: b.ge            #0x880d00
    // 0x8807f0: ldr             x3, [fp, #0x18]
    // 0x8807f4: r0 = BoxInt64Instr(r2)
    //     0x8807f4: sbfiz           x0, x2, #1, #0x1f
    //     0x8807f8: cmp             x2, x0, asr #1
    //     0x8807fc: b.eq            #0x880808
    //     0x880800: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x880804: stur            x2, [x0, #7]
    // 0x880808: mov             x1, x0
    // 0x88080c: stur            x1, [fp, #-0x48]
    // 0x880810: r0 = LoadClassIdInstr(r3)
    //     0x880810: ldur            x0, [x3, #-1]
    //     0x880814: ubfx            x0, x0, #0xc, #0x14
    // 0x880818: stp             x1, x3, [SP]
    // 0x88081c: r0 = GDT[cid_x0 + -0xb7]()
    //     0x88081c: sub             lr, x0, #0xb7
    //     0x880820: ldr             lr, [x21, lr, lsl #3]
    //     0x880824: blr             lr
    // 0x880828: r2 = Null
    //     0x880828: mov             x2, NULL
    // 0x88082c: r1 = Null
    //     0x88082c: mov             x1, NULL
    // 0x880830: cmp             w0, NULL
    // 0x880834: b.eq            #0x8808cc
    // 0x880838: branchIfSmi(r0, 0x8808cc)
    //     0x880838: tbz             w0, #0, #0x8808cc
    // 0x88083c: r3 = LoadClassIdInstr(r0)
    //     0x88083c: ldur            x3, [x0, #-1]
    //     0x880840: ubfx            x3, x3, #0xc, #0x14
    // 0x880844: r17 = 6675
    //     0x880844: movz            x17, #0x1a13
    // 0x880848: cmp             x3, x17
    // 0x88084c: b.eq            #0x8808d4
    // 0x880850: r4 = LoadClassIdInstr(r0)
    //     0x880850: ldur            x4, [x0, #-1]
    //     0x880854: ubfx            x4, x4, #0xc, #0x14
    // 0x880858: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x88085c: ldr             x3, [x3, #0x18]
    // 0x880860: ldr             x3, [x3, x4, lsl #3]
    // 0x880864: LoadField: r3 = r3->field_2b
    //     0x880864: ldur            w3, [x3, #0x2b]
    // 0x880868: DecompressPointer r3
    //     0x880868: add             x3, x3, HEAP, lsl #32
    // 0x88086c: cmp             w3, NULL
    // 0x880870: b.eq            #0x8808cc
    // 0x880874: LoadField: r3 = r3->field_f
    //     0x880874: ldur            w3, [x3, #0xf]
    // 0x880878: lsr             x3, x3, #3
    // 0x88087c: r17 = 6675
    //     0x88087c: movz            x17, #0x1a13
    // 0x880880: cmp             x3, x17
    // 0x880884: b.eq            #0x8808d4
    // 0x880888: r3 = SubtypeTestCache
    //     0x880888: add             x3, PP, #0xa, lsl #12  ; [pp+0xa450] SubtypeTestCache
    //     0x88088c: ldr             x3, [x3, #0x450]
    // 0x880890: r30 = Subtype1TestCacheStub
    //     0x880890: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x880894: LoadField: r30 = r30->field_7
    //     0x880894: ldur            lr, [lr, #7]
    // 0x880898: blr             lr
    // 0x88089c: cmp             w7, NULL
    // 0x8808a0: b.eq            #0x8808ac
    // 0x8808a4: tbnz            w7, #4, #0x8808cc
    // 0x8808a8: b               #0x8808d4
    // 0x8808ac: r8 = Map
    //     0x8808ac: add             x8, PP, #0xa, lsl #12  ; [pp+0xa458] Type: Map
    //     0x8808b0: ldr             x8, [x8, #0x458]
    // 0x8808b4: r3 = SubtypeTestCache
    //     0x8808b4: add             x3, PP, #0xa, lsl #12  ; [pp+0xa460] SubtypeTestCache
    //     0x8808b8: ldr             x3, [x3, #0x460]
    // 0x8808bc: r30 = InstanceOfStub
    //     0x8808bc: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x8808c0: LoadField: r30 = r30->field_7
    //     0x8808c0: ldur            lr, [lr, #7]
    // 0x8808c4: blr             lr
    // 0x8808c8: b               #0x8808d8
    // 0x8808cc: r0 = false
    //     0x8808cc: add             x0, NULL, #0x30  ; false
    // 0x8808d0: b               #0x8808d8
    // 0x8808d4: r0 = true
    //     0x8808d4: add             x0, NULL, #0x20  ; true
    // 0x8808d8: tbz             w0, #4, #0x8809bc
    // 0x8808dc: ldr             x1, [fp, #0x18]
    // 0x8808e0: r0 = LoadClassIdInstr(r1)
    //     0x8808e0: ldur            x0, [x1, #-1]
    //     0x8808e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8808e8: ldur            x16, [fp, #-0x48]
    // 0x8808ec: stp             x16, x1, [SP]
    // 0x8808f0: r0 = GDT[cid_x0 + -0xb7]()
    //     0x8808f0: sub             lr, x0, #0xb7
    //     0x8808f4: ldr             lr, [x21, lr, lsl #3]
    //     0x8808f8: blr             lr
    // 0x8808fc: r2 = Null
    //     0x8808fc: mov             x2, NULL
    // 0x880900: r1 = Null
    //     0x880900: mov             x1, NULL
    // 0x880904: cmp             w0, NULL
    // 0x880908: b.eq            #0x8809ac
    // 0x88090c: branchIfSmi(r0, 0x8809ac)
    //     0x88090c: tbz             w0, #0, #0x8809ac
    // 0x880910: r3 = LoadClassIdInstr(r0)
    //     0x880910: ldur            x3, [x0, #-1]
    //     0x880914: ubfx            x3, x3, #0xc, #0x14
    // 0x880918: r17 = 6676
    //     0x880918: movz            x17, #0x1a14
    // 0x88091c: cmp             x3, x17
    // 0x880920: b.eq            #0x8809b4
    // 0x880924: sub             x3, x3, #0x5a
    // 0x880928: cmp             x3, #2
    // 0x88092c: b.ls            #0x8809b4
    // 0x880930: r4 = LoadClassIdInstr(r0)
    //     0x880930: ldur            x4, [x0, #-1]
    //     0x880934: ubfx            x4, x4, #0xc, #0x14
    // 0x880938: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x88093c: ldr             x3, [x3, #0x18]
    // 0x880940: ldr             x3, [x3, x4, lsl #3]
    // 0x880944: LoadField: r3 = r3->field_2b
    //     0x880944: ldur            w3, [x3, #0x2b]
    // 0x880948: DecompressPointer r3
    //     0x880948: add             x3, x3, HEAP, lsl #32
    // 0x88094c: cmp             w3, NULL
    // 0x880950: b.eq            #0x8809ac
    // 0x880954: LoadField: r3 = r3->field_f
    //     0x880954: ldur            w3, [x3, #0xf]
    // 0x880958: lsr             x3, x3, #3
    // 0x88095c: r17 = 6676
    //     0x88095c: movz            x17, #0x1a14
    // 0x880960: cmp             x3, x17
    // 0x880964: b.eq            #0x8809b4
    // 0x880968: r3 = SubtypeTestCache
    //     0x880968: add             x3, PP, #0xa, lsl #12  ; [pp+0xa468] SubtypeTestCache
    //     0x88096c: ldr             x3, [x3, #0x468]
    // 0x880970: r30 = Subtype1TestCacheStub
    //     0x880970: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x880974: LoadField: r30 = r30->field_7
    //     0x880974: ldur            lr, [lr, #7]
    // 0x880978: blr             lr
    // 0x88097c: cmp             w7, NULL
    // 0x880980: b.eq            #0x88098c
    // 0x880984: tbnz            w7, #4, #0x8809ac
    // 0x880988: b               #0x8809b4
    // 0x88098c: r8 = List
    //     0x88098c: add             x8, PP, #0xa, lsl #12  ; [pp+0xa470] Type: List
    //     0x880990: ldr             x8, [x8, #0x470]
    // 0x880994: r3 = SubtypeTestCache
    //     0x880994: add             x3, PP, #0xa, lsl #12  ; [pp+0xa478] SubtypeTestCache
    //     0x880998: ldr             x3, [x3, #0x478]
    // 0x88099c: r30 = InstanceOfStub
    //     0x88099c: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x8809a0: LoadField: r30 = r30->field_7
    //     0x8809a0: ldur            lr, [lr, #7]
    // 0x8809a4: blr             lr
    // 0x8809a8: b               #0x8809b8
    // 0x8809ac: r0 = false
    //     0x8809ac: add             x0, NULL, #0x30  ; false
    // 0x8809b0: b               #0x8809b8
    // 0x8809b4: r0 = true
    //     0x8809b4: add             x0, NULL, #0x20  ; true
    // 0x8809b8: tbnz            w0, #4, #0x8809c4
    // 0x8809bc: r3 = true
    //     0x8809bc: add             x3, NULL, #0x20  ; true
    // 0x8809c0: b               #0x8809e8
    // 0x8809c4: ldr             x1, [fp, #0x18]
    // 0x8809c8: r0 = LoadClassIdInstr(r1)
    //     0x8809c8: ldur            x0, [x1, #-1]
    //     0x8809cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8809d0: ldur            x16, [fp, #-0x48]
    // 0x8809d4: stp             x16, x1, [SP]
    // 0x8809d8: r0 = GDT[cid_x0 + -0xb7]()
    //     0x8809d8: sub             lr, x0, #0xb7
    //     0x8809dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8809e0: blr             lr
    // 0x8809e4: r3 = false
    //     0x8809e4: add             x3, NULL, #0x30  ; false
    // 0x8809e8: ldr             x1, [fp, #0x18]
    // 0x8809ec: ldur            x2, [fp, #-0x38]
    // 0x8809f0: stur            x3, [fp, #-0x50]
    // 0x8809f4: r0 = LoadClassIdInstr(r1)
    //     0x8809f4: ldur            x0, [x1, #-1]
    //     0x8809f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8809fc: ldur            x16, [fp, #-0x48]
    // 0x880a00: stp             x16, x1, [SP]
    // 0x880a04: r0 = GDT[cid_x0 + -0xb7]()
    //     0x880a04: sub             lr, x0, #0xb7
    //     0x880a08: ldr             lr, [x21, lr, lsl #3]
    //     0x880a0c: blr             lr
    // 0x880a10: ldur            x1, [fp, #-0x38]
    // 0x880a14: LoadField: r2 = r1->field_13
    //     0x880a14: ldur            w2, [x1, #0x13]
    // 0x880a18: DecompressPointer r2
    //     0x880a18: add             x2, x2, HEAP, lsl #32
    // 0x880a1c: tbnz            w2, #4, #0x880a44
    // 0x880a20: cmp             w0, NULL
    // 0x880a24: b.eq            #0x880a44
    // 0x880a28: r2 = 60
    //     0x880a28: movz            x2, #0x3c
    // 0x880a2c: branchIfSmi(r0, 0x880a38)
    //     0x880a2c: tbz             w0, #0, #0x880a38
    // 0x880a30: r2 = LoadClassIdInstr(r0)
    //     0x880a30: ldur            x2, [x0, #-1]
    //     0x880a34: ubfx            x2, x2, #0xc, #0x14
    // 0x880a38: sub             x16, x2, #0x5e
    // 0x880a3c: cmp             x16, #1
    // 0x880a40: b.ls            #0x880a4c
    // 0x880a44: mov             x4, x0
    // 0x880a48: b               #0x880a68
    // 0x880a4c: ldur            x16, [fp, #-0x30]
    // 0x880a50: stp             x0, x16, [SP]
    // 0x880a54: ldur            x0, [fp, #-0x30]
    // 0x880a58: ClosureCall
    //     0x880a58: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x880a5c: ldur            x2, [x0, #0x1f]
    //     0x880a60: blr             x2
    // 0x880a64: mov             x4, x0
    // 0x880a68: ldur            x3, [fp, #-0x10]
    // 0x880a6c: ldur            x0, [fp, #-0x50]
    // 0x880a70: stur            x4, [fp, #-0x60]
    // 0x880a74: LoadField: r5 = r3->field_f
    //     0x880a74: ldur            w5, [x3, #0xf]
    // 0x880a78: DecompressPointer r5
    //     0x880a78: add             x5, x5, HEAP, lsl #32
    // 0x880a7c: stur            x5, [fp, #-0x58]
    // 0x880a80: r1 = Null
    //     0x880a80: mov             x1, NULL
    // 0x880a84: r2 = 4
    //     0x880a84: movz            x2, #0x4
    // 0x880a88: r0 = AllocateArray()
    //     0x880a88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x880a8c: mov             x3, x0
    // 0x880a90: ldur            x0, [fp, #-0x58]
    // 0x880a94: stur            x3, [fp, #-0x68]
    // 0x880a98: StoreField: r3->field_f = r0
    //     0x880a98: stur            w0, [x3, #0xf]
    // 0x880a9c: ldur            x0, [fp, #-0x50]
    // 0x880aa0: tbnz            w0, #4, #0x880ae4
    // 0x880aa4: ldur            x4, [fp, #-0x28]
    // 0x880aa8: ldur            x5, [fp, #-0x20]
    // 0x880aac: ldur            x0, [fp, #-0x48]
    // 0x880ab0: r1 = Null
    //     0x880ab0: mov             x1, NULL
    // 0x880ab4: r2 = 6
    //     0x880ab4: movz            x2, #0x6
    // 0x880ab8: r0 = AllocateArray()
    //     0x880ab8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x880abc: mov             x1, x0
    // 0x880ac0: ldur            x0, [fp, #-0x28]
    // 0x880ac4: StoreField: r1->field_f = r0
    //     0x880ac4: stur            w0, [x1, #0xf]
    // 0x880ac8: ldur            x2, [fp, #-0x48]
    // 0x880acc: StoreField: r1->field_13 = r2
    //     0x880acc: stur            w2, [x1, #0x13]
    // 0x880ad0: ldur            x2, [fp, #-0x20]
    // 0x880ad4: ArrayStore: r1[0] = r2  ; List_4
    //     0x880ad4: stur            w2, [x1, #0x17]
    // 0x880ad8: str             x1, [SP]
    // 0x880adc: r0 = _interpolate()
    //     0x880adc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x880ae0: b               #0x880ae8
    // 0x880ae4: r0 = ""
    //     0x880ae4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x880ae8: ldur            x2, [fp, #-0x18]
    // 0x880aec: ldur            x1, [fp, #-0x68]
    // 0x880af0: ArrayStore: r1[1] = r0  ; List_4
    //     0x880af0: add             x25, x1, #0x13
    //     0x880af4: str             w0, [x25]
    //     0x880af8: tbz             w0, #0, #0x880b14
    //     0x880afc: ldurb           w16, [x1, #-1]
    //     0x880b00: ldurb           w17, [x0, #-1]
    //     0x880b04: and             x16, x17, x16, lsr #2
    //     0x880b08: tst             x16, HEAP, lsr #32
    //     0x880b0c: b.eq            #0x880b14
    //     0x880b10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x880b14: ldur            x16, [fp, #-0x68]
    // 0x880b18: str             x16, [SP]
    // 0x880b1c: r0 = _interpolate()
    //     0x880b1c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x880b20: ldur            x16, [fp, #-0x40]
    // 0x880b24: ldur            lr, [fp, #-0x60]
    // 0x880b28: stp             lr, x16, [SP, #8]
    // 0x880b2c: str             x0, [SP]
    // 0x880b30: ldur            x0, [fp, #-0x40]
    // 0x880b34: ClosureCall
    //     0x880b34: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x880b38: ldur            x2, [x0, #0x1f]
    //     0x880b3c: blr             x2
    // 0x880b40: ldur            x0, [fp, #-0x18]
    // 0x880b44: add             x8, x0, #1
    // 0x880b48: ldur            x1, [fp, #-0x40]
    // 0x880b4c: ldur            x5, [fp, #-0x28]
    // 0x880b50: ldur            x6, [fp, #-0x20]
    // 0x880b54: ldur            x2, [fp, #-0x38]
    // 0x880b58: ldur            x4, [fp, #-0x30]
    // 0x880b5c: b               #0x8807a4
    // 0x880b60: ldur            x3, [fp, #-8]
    // 0x880b64: ldr             x0, [fp, #0x18]
    // 0x880b68: r2 = Null
    //     0x880b68: mov             x2, NULL
    // 0x880b6c: r1 = Null
    //     0x880b6c: mov             x1, NULL
    // 0x880b70: cmp             w0, NULL
    // 0x880b74: b.eq            #0x880c0c
    // 0x880b78: branchIfSmi(r0, 0x880c0c)
    //     0x880b78: tbz             w0, #0, #0x880c0c
    // 0x880b7c: r3 = LoadClassIdInstr(r0)
    //     0x880b7c: ldur            x3, [x0, #-1]
    //     0x880b80: ubfx            x3, x3, #0xc, #0x14
    // 0x880b84: r17 = 6675
    //     0x880b84: movz            x17, #0x1a13
    // 0x880b88: cmp             x3, x17
    // 0x880b8c: b.eq            #0x880c14
    // 0x880b90: r4 = LoadClassIdInstr(r0)
    //     0x880b90: ldur            x4, [x0, #-1]
    //     0x880b94: ubfx            x4, x4, #0xc, #0x14
    // 0x880b98: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x880b9c: ldr             x3, [x3, #0x18]
    // 0x880ba0: ldr             x3, [x3, x4, lsl #3]
    // 0x880ba4: LoadField: r3 = r3->field_2b
    //     0x880ba4: ldur            w3, [x3, #0x2b]
    // 0x880ba8: DecompressPointer r3
    //     0x880ba8: add             x3, x3, HEAP, lsl #32
    // 0x880bac: cmp             w3, NULL
    // 0x880bb0: b.eq            #0x880c0c
    // 0x880bb4: LoadField: r3 = r3->field_f
    //     0x880bb4: ldur            w3, [x3, #0xf]
    // 0x880bb8: lsr             x3, x3, #3
    // 0x880bbc: r17 = 6675
    //     0x880bbc: movz            x17, #0x1a13
    // 0x880bc0: cmp             x3, x17
    // 0x880bc4: b.eq            #0x880c14
    // 0x880bc8: r3 = SubtypeTestCache
    //     0x880bc8: add             x3, PP, #0xa, lsl #12  ; [pp+0xa480] SubtypeTestCache
    //     0x880bcc: ldr             x3, [x3, #0x480]
    // 0x880bd0: r30 = Subtype1TestCacheStub
    //     0x880bd0: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x880bd4: LoadField: r30 = r30->field_7
    //     0x880bd4: ldur            lr, [lr, #7]
    // 0x880bd8: blr             lr
    // 0x880bdc: cmp             w7, NULL
    // 0x880be0: b.eq            #0x880bec
    // 0x880be4: tbnz            w7, #4, #0x880c0c
    // 0x880be8: b               #0x880c14
    // 0x880bec: r8 = Map
    //     0x880bec: add             x8, PP, #0xa, lsl #12  ; [pp+0xa488] Type: Map
    //     0x880bf0: ldr             x8, [x8, #0x488]
    // 0x880bf4: r3 = SubtypeTestCache
    //     0x880bf4: add             x3, PP, #0xa, lsl #12  ; [pp+0xa490] SubtypeTestCache
    //     0x880bf8: ldr             x3, [x3, #0x490]
    // 0x880bfc: r30 = InstanceOfStub
    //     0x880bfc: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x880c00: LoadField: r30 = r30->field_7
    //     0x880c00: ldur            lr, [lr, #7]
    // 0x880c04: blr             lr
    // 0x880c08: b               #0x880c18
    // 0x880c0c: r0 = false
    //     0x880c0c: add             x0, NULL, #0x30  ; false
    // 0x880c10: b               #0x880c18
    // 0x880c14: r0 = true
    //     0x880c14: add             x0, NULL, #0x20  ; true
    // 0x880c18: tbnz            w0, #4, #0x880c58
    // 0x880c1c: ldr             x0, [fp, #0x18]
    // 0x880c20: ldur            x2, [fp, #-0x10]
    // 0x880c24: r1 = Function '<anonymous closure>': static.
    //     0x880c24: add             x1, PP, #0xa, lsl #12  ; [pp+0xa498] AnonymousClosure: static (0x880d20), in [package:dio/src/utils.dart] ::encodeMap (0x8803cc)
    //     0x880c28: ldr             x1, [x1, #0x498]
    // 0x880c2c: r0 = AllocateClosure()
    //     0x880c2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x880c30: ldr             x1, [fp, #0x18]
    // 0x880c34: r2 = LoadClassIdInstr(r1)
    //     0x880c34: ldur            x2, [x1, #-1]
    //     0x880c38: ubfx            x2, x2, #0xc, #0x14
    // 0x880c3c: mov             x16, x0
    // 0x880c40: mov             x0, x2
    // 0x880c44: mov             x2, x16
    // 0x880c48: r0 = GDT[cid_x0 + 0x6d3]()
    //     0x880c48: add             lr, x0, #0x6d3
    //     0x880c4c: ldr             lr, [x21, lr, lsl #3]
    //     0x880c50: blr             lr
    // 0x880c54: b               #0x880d00
    // 0x880c58: ldr             x1, [fp, #0x18]
    // 0x880c5c: ldur            x2, [fp, #-8]
    // 0x880c60: LoadField: r0 = r2->field_f
    //     0x880c60: ldur            w0, [x2, #0xf]
    // 0x880c64: DecompressPointer r0
    //     0x880c64: add             x0, x0, HEAP, lsl #32
    // 0x880c68: ldr             x16, [fp, #0x10]
    // 0x880c6c: stp             x16, x0, [SP, #8]
    // 0x880c70: str             x1, [SP]
    // 0x880c74: ClosureCall
    //     0x880c74: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x880c78: ldur            x2, [x0, #0x1f]
    //     0x880c7c: blr             x2
    // 0x880c80: stur            x0, [fp, #-0x10]
    // 0x880c84: cmp             w0, NULL
    // 0x880c88: b.eq            #0x880cb0
    // 0x880c8c: mov             x1, x0
    // 0x880c90: r0 = trim()
    //     0x880c90: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x880c94: LoadField: r1 = r0->field_7
    //     0x880c94: ldur            w1, [x0, #7]
    // 0x880c98: cbnz            w1, #0x880ca4
    // 0x880c9c: r0 = false
    //     0x880c9c: add             x0, NULL, #0x30  ; false
    // 0x880ca0: b               #0x880ca8
    // 0x880ca4: r0 = true
    //     0x880ca4: add             x0, NULL, #0x20  ; true
    // 0x880ca8: mov             x3, x0
    // 0x880cac: b               #0x880cb4
    // 0x880cb0: r3 = false
    //     0x880cb0: add             x3, NULL, #0x30  ; false
    // 0x880cb4: ldur            x0, [fp, #-8]
    // 0x880cb8: stur            x3, [fp, #-0x20]
    // 0x880cbc: LoadField: r1 = r0->field_1b
    //     0x880cbc: ldur            w1, [x0, #0x1b]
    // 0x880cc0: DecompressPointer r1
    //     0x880cc0: add             x1, x1, HEAP, lsl #32
    // 0x880cc4: tbz             w1, #4, #0x880cdc
    // 0x880cc8: tbnz            w3, #4, #0x880cdc
    // 0x880ccc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x880ccc: ldur            w1, [x0, #0x17]
    // 0x880cd0: DecompressPointer r1
    //     0x880cd0: add             x1, x1, HEAP, lsl #32
    // 0x880cd4: r2 = "&"
    //     0x880cd4: ldr             x2, [PP, #0x1188]  ; [pp+0x1188] "&"
    // 0x880cd8: r0 = write()
    //     0x880cd8: bl              #0x165e2d8  ; [dart:core] StringBuffer::write
    // 0x880cdc: ldur            x0, [fp, #-8]
    // 0x880ce0: ldur            x1, [fp, #-0x20]
    // 0x880ce4: r2 = false
    //     0x880ce4: add             x2, NULL, #0x30  ; false
    // 0x880ce8: StoreField: r0->field_1b = r2
    //     0x880ce8: stur            w2, [x0, #0x1b]
    // 0x880cec: tbnz            w1, #4, #0x880d00
    // 0x880cf0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x880cf0: ldur            w1, [x0, #0x17]
    // 0x880cf4: DecompressPointer r1
    //     0x880cf4: add             x1, x1, HEAP, lsl #32
    // 0x880cf8: ldur            x2, [fp, #-0x10]
    // 0x880cfc: r0 = write()
    //     0x880cfc: bl              #0x165e2d8  ; [dart:core] StringBuffer::write
    // 0x880d00: r0 = Null
    //     0x880d00: mov             x0, NULL
    // 0x880d04: LeaveFrame
    //     0x880d04: mov             SP, fp
    //     0x880d08: ldp             fp, lr, [SP], #0x10
    // 0x880d0c: ret
    //     0x880d0c: ret             
    // 0x880d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x880d10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x880d14: b               #0x880674
    // 0x880d18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x880d18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x880d1c: b               #0x8807bc
  }
  [closure] static void <anonymous closure>(dynamic, dynamic, dynamic) {
    // ** addr: 0x880d20, size: 0x34c
    // 0x880d20: EnterFrame
    //     0x880d20: stp             fp, lr, [SP, #-0x10]!
    //     0x880d24: mov             fp, SP
    // 0x880d28: AllocStack(0x48)
    //     0x880d28: sub             SP, SP, #0x48
    // 0x880d2c: SetupParameters()
    //     0x880d2c: ldr             x0, [fp, #0x20]
    //     0x880d30: ldur            w1, [x0, #0x17]
    //     0x880d34: add             x1, x1, HEAP, lsl #32
    //     0x880d38: stur            x1, [fp, #-8]
    // 0x880d3c: CheckStackOverflow
    //     0x880d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x880d40: cmp             SP, x16
    //     0x880d44: b.ls            #0x881064
    // 0x880d48: LoadField: r0 = r1->field_f
    //     0x880d48: ldur            w0, [x1, #0xf]
    // 0x880d4c: DecompressPointer r0
    //     0x880d4c: add             x0, x0, HEAP, lsl #32
    // 0x880d50: r2 = LoadClassIdInstr(r0)
    //     0x880d50: ldur            x2, [x0, #-1]
    //     0x880d54: ubfx            x2, x2, #0xc, #0x14
    // 0x880d58: r16 = ""
    //     0x880d58: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x880d5c: stp             x16, x0, [SP]
    // 0x880d60: mov             x0, x2
    // 0x880d64: mov             lr, x0
    // 0x880d68: ldr             lr, [x21, lr, lsl #3]
    // 0x880d6c: blr             lr
    // 0x880d70: tbnz            w0, #4, #0x880e94
    // 0x880d74: ldur            x1, [fp, #-8]
    // 0x880d78: LoadField: r2 = r1->field_b
    //     0x880d78: ldur            w2, [x1, #0xb]
    // 0x880d7c: DecompressPointer r2
    //     0x880d7c: add             x2, x2, HEAP, lsl #32
    // 0x880d80: stur            x2, [fp, #-0x18]
    // 0x880d84: LoadField: r1 = r2->field_2f
    //     0x880d84: ldur            w1, [x2, #0x2f]
    // 0x880d88: DecompressPointer r1
    //     0x880d88: add             x1, x1, HEAP, lsl #32
    // 0x880d8c: stur            x1, [fp, #-0x10]
    // 0x880d90: LoadField: r0 = r2->field_2b
    //     0x880d90: ldur            w0, [x2, #0x2b]
    // 0x880d94: DecompressPointer r0
    //     0x880d94: add             x0, x0, HEAP, lsl #32
    // 0x880d98: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x880d98: ldur            w3, [x0, #0x17]
    // 0x880d9c: DecompressPointer r3
    //     0x880d9c: add             x3, x3, HEAP, lsl #32
    // 0x880da0: LoadField: r0 = r3->field_13
    //     0x880da0: ldur            w0, [x3, #0x13]
    // 0x880da4: DecompressPointer r0
    //     0x880da4: add             x0, x0, HEAP, lsl #32
    // 0x880da8: tbz             w0, #4, #0x880db4
    // 0x880dac: ldr             x0, [fp, #0x10]
    // 0x880db0: b               #0x880ddc
    // 0x880db4: ldr             x0, [fp, #0x10]
    // 0x880db8: cmp             w0, NULL
    // 0x880dbc: b.eq            #0x880ddc
    // 0x880dc0: r4 = 60
    //     0x880dc0: movz            x4, #0x3c
    // 0x880dc4: branchIfSmi(r0, 0x880dd0)
    //     0x880dc4: tbz             w0, #0, #0x880dd0
    // 0x880dc8: r4 = LoadClassIdInstr(r0)
    //     0x880dc8: ldur            x4, [x0, #-1]
    //     0x880dcc: ubfx            x4, x4, #0xc, #0x14
    // 0x880dd0: sub             x16, x4, #0x5e
    // 0x880dd4: cmp             x16, #1
    // 0x880dd8: b.ls            #0x880de8
    // 0x880ddc: mov             x3, x0
    // 0x880de0: mov             x0, x2
    // 0x880de4: b               #0x880e0c
    // 0x880de8: LoadField: r4 = r3->field_27
    //     0x880de8: ldur            w4, [x3, #0x27]
    // 0x880dec: DecompressPointer r4
    //     0x880dec: add             x4, x4, HEAP, lsl #32
    // 0x880df0: stp             x0, x4, [SP]
    // 0x880df4: mov             x0, x4
    // 0x880df8: ClosureCall
    //     0x880df8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x880dfc: ldur            x2, [x0, #0x1f]
    //     0x880e00: blr             x2
    // 0x880e04: mov             x3, x0
    // 0x880e08: ldur            x0, [fp, #-0x18]
    // 0x880e0c: stur            x3, [fp, #-0x28]
    // 0x880e10: LoadField: r4 = r0->field_27
    //     0x880e10: ldur            w4, [x0, #0x27]
    // 0x880e14: DecompressPointer r4
    //     0x880e14: add             x4, x4, HEAP, lsl #32
    // 0x880e18: ldr             x0, [fp, #0x18]
    // 0x880e1c: stur            x4, [fp, #-0x20]
    // 0x880e20: r2 = Null
    //     0x880e20: mov             x2, NULL
    // 0x880e24: r1 = Null
    //     0x880e24: mov             x1, NULL
    // 0x880e28: r4 = 60
    //     0x880e28: movz            x4, #0x3c
    // 0x880e2c: branchIfSmi(r0, 0x880e38)
    //     0x880e2c: tbz             w0, #0, #0x880e38
    // 0x880e30: r4 = LoadClassIdInstr(r0)
    //     0x880e30: ldur            x4, [x0, #-1]
    //     0x880e34: ubfx            x4, x4, #0xc, #0x14
    // 0x880e38: sub             x4, x4, #0x5e
    // 0x880e3c: cmp             x4, #1
    // 0x880e40: b.ls            #0x880e54
    // 0x880e44: r8 = String
    //     0x880e44: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x880e48: r3 = Null
    //     0x880e48: add             x3, PP, #0xa, lsl #12  ; [pp+0xa4a0] Null
    //     0x880e4c: ldr             x3, [x3, #0x4a0]
    // 0x880e50: r0 = String()
    //     0x880e50: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x880e54: ldur            x16, [fp, #-0x20]
    // 0x880e58: ldr             lr, [fp, #0x18]
    // 0x880e5c: stp             lr, x16, [SP]
    // 0x880e60: ldur            x0, [fp, #-0x20]
    // 0x880e64: ClosureCall
    //     0x880e64: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x880e68: ldur            x2, [x0, #0x1f]
    //     0x880e6c: blr             x2
    // 0x880e70: ldur            x16, [fp, #-0x10]
    // 0x880e74: ldur            lr, [fp, #-0x28]
    // 0x880e78: stp             lr, x16, [SP, #8]
    // 0x880e7c: str             x0, [SP]
    // 0x880e80: ldur            x0, [fp, #-0x10]
    // 0x880e84: ClosureCall
    //     0x880e84: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x880e88: ldur            x2, [x0, #0x1f]
    //     0x880e8c: blr             x2
    // 0x880e90: b               #0x881054
    // 0x880e94: ldr             x0, [fp, #0x10]
    // 0x880e98: ldur            x1, [fp, #-8]
    // 0x880e9c: LoadField: r2 = r1->field_b
    //     0x880e9c: ldur            w2, [x1, #0xb]
    // 0x880ea0: DecompressPointer r2
    //     0x880ea0: add             x2, x2, HEAP, lsl #32
    // 0x880ea4: stur            x2, [fp, #-0x18]
    // 0x880ea8: LoadField: r3 = r2->field_2f
    //     0x880ea8: ldur            w3, [x2, #0x2f]
    // 0x880eac: DecompressPointer r3
    //     0x880eac: add             x3, x3, HEAP, lsl #32
    // 0x880eb0: stur            x3, [fp, #-0x10]
    // 0x880eb4: LoadField: r4 = r2->field_2b
    //     0x880eb4: ldur            w4, [x2, #0x2b]
    // 0x880eb8: DecompressPointer r4
    //     0x880eb8: add             x4, x4, HEAP, lsl #32
    // 0x880ebc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x880ebc: ldur            w5, [x4, #0x17]
    // 0x880ec0: DecompressPointer r5
    //     0x880ec0: add             x5, x5, HEAP, lsl #32
    // 0x880ec4: LoadField: r4 = r5->field_13
    //     0x880ec4: ldur            w4, [x5, #0x13]
    // 0x880ec8: DecompressPointer r4
    //     0x880ec8: add             x4, x4, HEAP, lsl #32
    // 0x880ecc: tbnz            w4, #4, #0x880ef4
    // 0x880ed0: cmp             w0, NULL
    // 0x880ed4: b.eq            #0x880ef4
    // 0x880ed8: r4 = 60
    //     0x880ed8: movz            x4, #0x3c
    // 0x880edc: branchIfSmi(r0, 0x880ee8)
    //     0x880edc: tbz             w0, #0, #0x880ee8
    // 0x880ee0: r4 = LoadClassIdInstr(r0)
    //     0x880ee0: ldur            x4, [x0, #-1]
    //     0x880ee4: ubfx            x4, x4, #0xc, #0x14
    // 0x880ee8: sub             x16, x4, #0x5e
    // 0x880eec: cmp             x16, #1
    // 0x880ef0: b.ls            #0x880f04
    // 0x880ef4: mov             x4, x0
    // 0x880ef8: mov             x0, x1
    // 0x880efc: mov             x3, x2
    // 0x880f00: b               #0x880f2c
    // 0x880f04: LoadField: r4 = r5->field_27
    //     0x880f04: ldur            w4, [x5, #0x27]
    // 0x880f08: DecompressPointer r4
    //     0x880f08: add             x4, x4, HEAP, lsl #32
    // 0x880f0c: stp             x0, x4, [SP]
    // 0x880f10: mov             x0, x4
    // 0x880f14: ClosureCall
    //     0x880f14: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x880f18: ldur            x2, [x0, #0x1f]
    //     0x880f1c: blr             x2
    // 0x880f20: mov             x4, x0
    // 0x880f24: ldur            x0, [fp, #-8]
    // 0x880f28: ldur            x3, [fp, #-0x18]
    // 0x880f2c: stur            x4, [fp, #-0x28]
    // 0x880f30: LoadField: r5 = r0->field_f
    //     0x880f30: ldur            w5, [x0, #0xf]
    // 0x880f34: DecompressPointer r5
    //     0x880f34: add             x5, x5, HEAP, lsl #32
    // 0x880f38: stur            x5, [fp, #-0x20]
    // 0x880f3c: r1 = Null
    //     0x880f3c: mov             x1, NULL
    // 0x880f40: r2 = 8
    //     0x880f40: movz            x2, #0x8
    // 0x880f44: r0 = AllocateArray()
    //     0x880f44: bl              #0x16f7198  ; AllocateArrayStub
    // 0x880f48: mov             x3, x0
    // 0x880f4c: ldur            x0, [fp, #-0x20]
    // 0x880f50: stur            x3, [fp, #-0x30]
    // 0x880f54: StoreField: r3->field_f = r0
    //     0x880f54: stur            w0, [x3, #0xf]
    // 0x880f58: ldur            x4, [fp, #-0x18]
    // 0x880f5c: LoadField: r0 = r4->field_1f
    //     0x880f5c: ldur            w0, [x4, #0x1f]
    // 0x880f60: DecompressPointer r0
    //     0x880f60: add             x0, x0, HEAP, lsl #32
    // 0x880f64: StoreField: r3->field_13 = r0
    //     0x880f64: stur            w0, [x3, #0x13]
    // 0x880f68: LoadField: r5 = r4->field_27
    //     0x880f68: ldur            w5, [x4, #0x27]
    // 0x880f6c: DecompressPointer r5
    //     0x880f6c: add             x5, x5, HEAP, lsl #32
    // 0x880f70: ldr             x0, [fp, #0x18]
    // 0x880f74: stur            x5, [fp, #-8]
    // 0x880f78: r2 = Null
    //     0x880f78: mov             x2, NULL
    // 0x880f7c: r1 = Null
    //     0x880f7c: mov             x1, NULL
    // 0x880f80: r4 = 60
    //     0x880f80: movz            x4, #0x3c
    // 0x880f84: branchIfSmi(r0, 0x880f90)
    //     0x880f84: tbz             w0, #0, #0x880f90
    // 0x880f88: r4 = LoadClassIdInstr(r0)
    //     0x880f88: ldur            x4, [x0, #-1]
    //     0x880f8c: ubfx            x4, x4, #0xc, #0x14
    // 0x880f90: sub             x4, x4, #0x5e
    // 0x880f94: cmp             x4, #1
    // 0x880f98: b.ls            #0x880fac
    // 0x880f9c: r8 = String
    //     0x880f9c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x880fa0: r3 = Null
    //     0x880fa0: add             x3, PP, #0xa, lsl #12  ; [pp+0xa4b0] Null
    //     0x880fa4: ldr             x3, [x3, #0x4b0]
    // 0x880fa8: r0 = String()
    //     0x880fa8: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x880fac: ldur            x16, [fp, #-8]
    // 0x880fb0: ldr             lr, [fp, #0x18]
    // 0x880fb4: stp             lr, x16, [SP]
    // 0x880fb8: ldur            x0, [fp, #-8]
    // 0x880fbc: ClosureCall
    //     0x880fbc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x880fc0: ldur            x2, [x0, #0x1f]
    //     0x880fc4: blr             x2
    // 0x880fc8: ldur            x1, [fp, #-0x30]
    // 0x880fcc: ArrayStore: r1[2] = r0  ; List_4
    //     0x880fcc: add             x25, x1, #0x17
    //     0x880fd0: str             w0, [x25]
    //     0x880fd4: tbz             w0, #0, #0x880ff0
    //     0x880fd8: ldurb           w16, [x1, #-1]
    //     0x880fdc: ldurb           w17, [x0, #-1]
    //     0x880fe0: and             x16, x17, x16, lsr #2
    //     0x880fe4: tst             x16, HEAP, lsr #32
    //     0x880fe8: b.eq            #0x880ff0
    //     0x880fec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x880ff0: ldur            x0, [fp, #-0x18]
    // 0x880ff4: LoadField: r1 = r0->field_23
    //     0x880ff4: ldur            w1, [x0, #0x23]
    // 0x880ff8: DecompressPointer r1
    //     0x880ff8: add             x1, x1, HEAP, lsl #32
    // 0x880ffc: mov             x0, x1
    // 0x881000: ldur            x1, [fp, #-0x30]
    // 0x881004: ArrayStore: r1[3] = r0  ; List_4
    //     0x881004: add             x25, x1, #0x1b
    //     0x881008: str             w0, [x25]
    //     0x88100c: tbz             w0, #0, #0x881028
    //     0x881010: ldurb           w16, [x1, #-1]
    //     0x881014: ldurb           w17, [x0, #-1]
    //     0x881018: and             x16, x17, x16, lsr #2
    //     0x88101c: tst             x16, HEAP, lsr #32
    //     0x881020: b.eq            #0x881028
    //     0x881024: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x881028: ldur            x16, [fp, #-0x30]
    // 0x88102c: str             x16, [SP]
    // 0x881030: r0 = _interpolate()
    //     0x881030: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x881034: ldur            x16, [fp, #-0x10]
    // 0x881038: ldur            lr, [fp, #-0x28]
    // 0x88103c: stp             lr, x16, [SP, #8]
    // 0x881040: str             x0, [SP]
    // 0x881044: ldur            x0, [fp, #-0x10]
    // 0x881048: ClosureCall
    //     0x881048: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x88104c: ldur            x2, [x0, #0x1f]
    //     0x881050: blr             x2
    // 0x881054: r0 = Null
    //     0x881054: mov             x0, NULL
    // 0x881058: LeaveFrame
    //     0x881058: mov             SP, fp
    //     0x88105c: ldp             fp, lr, [SP], #0x10
    // 0x881060: ret
    //     0x881060: ret             
    // 0x881064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x881064: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x881068: b               #0x880d48
  }
  [closure] static Object? maybeEncode(dynamic, Object?) {
    // ** addr: 0x88106c, size: 0x9c
    // 0x88106c: EnterFrame
    //     0x88106c: stp             fp, lr, [SP, #-0x10]!
    //     0x881070: mov             fp, SP
    // 0x881074: AllocStack(0x10)
    //     0x881074: sub             SP, SP, #0x10
    // 0x881078: SetupParameters()
    //     0x881078: ldr             x0, [fp, #0x18]
    //     0x88107c: ldur            w1, [x0, #0x17]
    //     0x881080: add             x1, x1, HEAP, lsl #32
    // 0x881084: CheckStackOverflow
    //     0x881084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x881088: cmp             SP, x16
    //     0x88108c: b.ls            #0x881100
    // 0x881090: LoadField: r0 = r1->field_13
    //     0x881090: ldur            w0, [x1, #0x13]
    // 0x881094: DecompressPointer r0
    //     0x881094: add             x0, x0, HEAP, lsl #32
    // 0x881098: tbz             w0, #4, #0x8810a4
    // 0x88109c: ldr             x0, [fp, #0x10]
    // 0x8810a0: b               #0x8810cc
    // 0x8810a4: ldr             x0, [fp, #0x10]
    // 0x8810a8: cmp             w0, NULL
    // 0x8810ac: b.eq            #0x8810cc
    // 0x8810b0: r2 = 60
    //     0x8810b0: movz            x2, #0x3c
    // 0x8810b4: branchIfSmi(r0, 0x8810c0)
    //     0x8810b4: tbz             w0, #0, #0x8810c0
    // 0x8810b8: r2 = LoadClassIdInstr(r0)
    //     0x8810b8: ldur            x2, [x0, #-1]
    //     0x8810bc: ubfx            x2, x2, #0xc, #0x14
    // 0x8810c0: sub             x16, x2, #0x5e
    // 0x8810c4: cmp             x16, #1
    // 0x8810c8: b.ls            #0x8810d8
    // 0x8810cc: LeaveFrame
    //     0x8810cc: mov             SP, fp
    //     0x8810d0: ldp             fp, lr, [SP], #0x10
    // 0x8810d4: ret
    //     0x8810d4: ret             
    // 0x8810d8: LoadField: r2 = r1->field_27
    //     0x8810d8: ldur            w2, [x1, #0x27]
    // 0x8810dc: DecompressPointer r2
    //     0x8810dc: add             x2, x2, HEAP, lsl #32
    // 0x8810e0: stp             x0, x2, [SP]
    // 0x8810e4: mov             x0, x2
    // 0x8810e8: ClosureCall
    //     0x8810e8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x8810ec: ldur            x2, [x0, #0x1f]
    //     0x8810f0: blr             x2
    // 0x8810f4: LeaveFrame
    //     0x8810f4: mov             SP, fp
    //     0x8810f8: ldp             fp, lr, [SP], #0x10
    // 0x8810fc: ret
    //     0x8810fc: ret             
    // 0x881100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x881100: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x881104: b               #0x881090
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x883fc4, size: 0x4c
    // 0x883fc4: EnterFrame
    //     0x883fc4: stp             fp, lr, [SP, #-0x10]!
    //     0x883fc8: mov             fp, SP
    // 0x883fcc: ldr             x0, [fp, #0x10]
    // 0x883fd0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x883fd0: ldur            w1, [x0, #0x17]
    // 0x883fd4: DecompressPointer r1
    //     0x883fd4: add             x1, x1, HEAP, lsl #32
    // 0x883fd8: CheckStackOverflow
    //     0x883fd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883fdc: cmp             SP, x16
    //     0x883fe0: b.ls            #0x884008
    // 0x883fe4: LoadField: r0 = r1->field_f
    //     0x883fe4: ldur            w0, [x1, #0xf]
    // 0x883fe8: DecompressPointer r0
    //     0x883fe8: add             x0, x0, HEAP, lsl #32
    // 0x883fec: mov             x1, x0
    // 0x883ff0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x883ff0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x883ff4: r0 = complete()
    //     0x883ff4: bl              #0x16646a8  ; [dart:async] _AsyncCompleter::complete
    // 0x883ff8: r0 = Null
    //     0x883ff8: mov             x0, NULL
    // 0x883ffc: LeaveFrame
    //     0x883ffc: mov             SP, fp
    //     0x884000: ldp             fp, lr, [SP], #0x10
    // 0x884004: ret
    //     0x884004: ret             
    // 0x884008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x884008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88400c: b               #0x883fe4
  }
  static _ encodingForCharset(/* No info */) {
    // ** addr: 0x12c9c90, size: 0x50
    // 0x12c9c90: EnterFrame
    //     0x12c9c90: stp             fp, lr, [SP, #-0x10]!
    //     0x12c9c94: mov             fp, SP
    // 0x12c9c98: CheckStackOverflow
    //     0x12c9c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c9c9c: cmp             SP, x16
    //     0x12c9ca0: b.ls            #0x12c9cd8
    // 0x12c9ca4: cmp             w1, NULL
    // 0x12c9ca8: b.ne            #0x12c9cbc
    // 0x12c9cac: r0 = Instance_Utf8Codec
    //     0x12c9cac: ldr             x0, [PP, #0x11a0]  ; [pp+0x11a0] Obj!Utf8Codec@d6eda1
    // 0x12c9cb0: LeaveFrame
    //     0x12c9cb0: mov             SP, fp
    //     0x12c9cb4: ldp             fp, lr, [SP], #0x10
    // 0x12c9cb8: ret
    //     0x12c9cb8: ret             
    // 0x12c9cbc: r0 = getByName()
    //     0x12c9cbc: bl              #0x12c9ce0  ; [dart:convert] Encoding::getByName
    // 0x12c9cc0: cmp             w0, NULL
    // 0x12c9cc4: b.ne            #0x12c9ccc
    // 0x12c9cc8: r0 = Instance_Utf8Codec
    //     0x12c9cc8: ldr             x0, [PP, #0x11a0]  ; [pp+0x11a0] Obj!Utf8Codec@d6eda1
    // 0x12c9ccc: LeaveFrame
    //     0x12c9ccc: mov             SP, fp
    //     0x12c9cd0: ldp             fp, lr, [SP], #0x10
    // 0x12c9cd4: ret
    //     0x12c9cd4: ret             
    // 0x12c9cd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c9cd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c9cdc: b               #0x12c9ca4
  }
}
