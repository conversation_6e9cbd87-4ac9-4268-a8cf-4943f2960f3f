// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart

// class id: 1049341, size: 0x8
class :: {
}

// class id: 3383, size: 0x1c, field offset: 0x14
class _BagItemViewWidgetState extends State<dynamic> {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x8043d8, size: 0x140
    // 0x8043d8: EnterFrame
    //     0x8043d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8043dc: mov             fp, SP
    // 0x8043e0: AllocStack(0x10)
    //     0x8043e0: sub             SP, SP, #0x10
    // 0x8043e4: SetupParameters(_BagItemViewWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8043e4: mov             x4, x1
    //     0x8043e8: mov             x3, x2
    //     0x8043ec: stur            x1, [fp, #-8]
    //     0x8043f0: stur            x2, [fp, #-0x10]
    // 0x8043f4: CheckStackOverflow
    //     0x8043f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8043f8: cmp             SP, x16
    //     0x8043fc: b.ls            #0x80450c
    // 0x804400: mov             x0, x3
    // 0x804404: r2 = Null
    //     0x804404: mov             x2, NULL
    // 0x804408: r1 = Null
    //     0x804408: mov             x1, NULL
    // 0x80440c: r4 = 60
    //     0x80440c: movz            x4, #0x3c
    // 0x804410: branchIfSmi(r0, 0x80441c)
    //     0x804410: tbz             w0, #0, #0x80441c
    // 0x804414: r4 = LoadClassIdInstr(r0)
    //     0x804414: ldur            x4, [x0, #-1]
    //     0x804418: ubfx            x4, x4, #0xc, #0x14
    // 0x80441c: r17 = 4121
    //     0x80441c: movz            x17, #0x1019
    // 0x804420: cmp             x4, x17
    // 0x804424: b.eq            #0x80443c
    // 0x804428: r8 = BagItemViewWidget
    //     0x804428: add             x8, PP, #0x57, lsl #12  ; [pp+0x57318] Type: BagItemViewWidget
    //     0x80442c: ldr             x8, [x8, #0x318]
    // 0x804430: r3 = Null
    //     0x804430: add             x3, PP, #0x57, lsl #12  ; [pp+0x57320] Null
    //     0x804434: ldr             x3, [x3, #0x320]
    // 0x804438: r0 = BagItemViewWidget()
    //     0x804438: bl              #0x8048c4  ; IsType_BagItemViewWidget_Stub
    // 0x80443c: ldur            x3, [fp, #-8]
    // 0x804440: LoadField: r2 = r3->field_7
    //     0x804440: ldur            w2, [x3, #7]
    // 0x804444: DecompressPointer r2
    //     0x804444: add             x2, x2, HEAP, lsl #32
    // 0x804448: ldur            x0, [fp, #-0x10]
    // 0x80444c: r1 = Null
    //     0x80444c: mov             x1, NULL
    // 0x804450: cmp             w2, NULL
    // 0x804454: b.eq            #0x804478
    // 0x804458: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x804458: ldur            w4, [x2, #0x17]
    // 0x80445c: DecompressPointer r4
    //     0x80445c: add             x4, x4, HEAP, lsl #32
    // 0x804460: r8 = X0 bound StatefulWidget
    //     0x804460: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x804464: ldr             x8, [x8, #0x7a0]
    // 0x804468: LoadField: r9 = r4->field_7
    //     0x804468: ldur            x9, [x4, #7]
    // 0x80446c: r3 = Null
    //     0x80446c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57330] Null
    //     0x804470: ldr             x3, [x3, #0x330]
    // 0x804474: blr             x9
    // 0x804478: ldur            x0, [fp, #-0x10]
    // 0x80447c: LoadField: r1 = r0->field_b
    //     0x80447c: ldur            w1, [x0, #0xb]
    // 0x804480: DecompressPointer r1
    //     0x804480: add             x1, x1, HEAP, lsl #32
    // 0x804484: cmp             w1, NULL
    // 0x804488: b.ne            #0x804494
    // 0x80448c: r1 = Null
    //     0x80448c: mov             x1, NULL
    // 0x804490: b               #0x8044a0
    // 0x804494: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x804494: ldur            w0, [x1, #0x17]
    // 0x804498: DecompressPointer r0
    //     0x804498: add             x0, x0, HEAP, lsl #32
    // 0x80449c: mov             x1, x0
    // 0x8044a0: ldur            x0, [fp, #-8]
    // 0x8044a4: LoadField: r2 = r0->field_b
    //     0x8044a4: ldur            w2, [x0, #0xb]
    // 0x8044a8: DecompressPointer r2
    //     0x8044a8: add             x2, x2, HEAP, lsl #32
    // 0x8044ac: cmp             w2, NULL
    // 0x8044b0: b.eq            #0x804514
    // 0x8044b4: LoadField: r3 = r2->field_b
    //     0x8044b4: ldur            w3, [x2, #0xb]
    // 0x8044b8: DecompressPointer r3
    //     0x8044b8: add             x3, x3, HEAP, lsl #32
    // 0x8044bc: cmp             w3, NULL
    // 0x8044c0: b.ne            #0x8044cc
    // 0x8044c4: r3 = Null
    //     0x8044c4: mov             x3, NULL
    // 0x8044c8: b               #0x8044d8
    // 0x8044cc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x8044cc: ldur            w4, [x3, #0x17]
    // 0x8044d0: DecompressPointer r4
    //     0x8044d0: add             x4, x4, HEAP, lsl #32
    // 0x8044d4: mov             x3, x4
    // 0x8044d8: cmp             w1, w3
    // 0x8044dc: b.ne            #0x8044ec
    // 0x8044e0: LoadField: r1 = r2->field_3b
    //     0x8044e0: ldur            w1, [x2, #0x3b]
    // 0x8044e4: DecompressPointer r1
    //     0x8044e4: add             x1, x1, HEAP, lsl #32
    // 0x8044e8: tbnz            w1, #4, #0x8044fc
    // 0x8044ec: mov             x1, x0
    // 0x8044f0: r0 = _initializeCatalogueQuantities()
    //     0x8044f0: bl              #0x8046bc  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_initializeCatalogueQuantities
    // 0x8044f4: ldur            x1, [fp, #-8]
    // 0x8044f8: r0 = _checkAvailableQuantity()
    //     0x8044f8: bl              #0x804518  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_checkAvailableQuantity
    // 0x8044fc: r0 = Null
    //     0x8044fc: mov             x0, NULL
    // 0x804500: LeaveFrame
    //     0x804500: mov             SP, fp
    //     0x804504: ldp             fp, lr, [SP], #0x10
    // 0x804508: ret
    //     0x804508: ret             
    // 0x80450c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80450c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804510: b               #0x804400
    // 0x804514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804514: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _checkAvailableQuantity(/* No info */) {
    // ** addr: 0x804518, size: 0x1a4
    // 0x804518: EnterFrame
    //     0x804518: stp             fp, lr, [SP, #-0x10]!
    //     0x80451c: mov             fp, SP
    // 0x804520: AllocStack(0x30)
    //     0x804520: sub             SP, SP, #0x30
    // 0x804524: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x804524: mov             x0, x1
    //     0x804528: stur            x1, [fp, #-8]
    // 0x80452c: CheckStackOverflow
    //     0x80452c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804530: cmp             SP, x16
    //     0x804534: b.ls            #0x8046a8
    // 0x804538: LoadField: r1 = r0->field_b
    //     0x804538: ldur            w1, [x0, #0xb]
    // 0x80453c: DecompressPointer r1
    //     0x80453c: add             x1, x1, HEAP, lsl #32
    // 0x804540: cmp             w1, NULL
    // 0x804544: b.eq            #0x8046b0
    // 0x804548: LoadField: r2 = r1->field_b
    //     0x804548: ldur            w2, [x1, #0xb]
    // 0x80454c: DecompressPointer r2
    //     0x80454c: add             x2, x2, HEAP, lsl #32
    // 0x804550: cmp             w2, NULL
    // 0x804554: b.ne            #0x804560
    // 0x804558: r1 = Null
    //     0x804558: mov             x1, NULL
    // 0x80455c: b               #0x804568
    // 0x804560: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x804560: ldur            w1, [x2, #0x17]
    // 0x804564: DecompressPointer r1
    //     0x804564: add             x1, x1, HEAP, lsl #32
    // 0x804568: cmp             w1, NULL
    // 0x80456c: b.ne            #0x804584
    // 0x804570: r1 = <Catalogue>
    //     0x804570: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x804574: ldr             x1, [x1, #0x418]
    // 0x804578: r2 = 0
    //     0x804578: movz            x2, #0
    // 0x80457c: r0 = _GrowableList()
    //     0x80457c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x804580: mov             x1, x0
    // 0x804584: ldur            x3, [fp, #-8]
    // 0x804588: r0 = false
    //     0x804588: add             x0, NULL, #0x30  ; false
    // 0x80458c: ArrayStore: r3[0] = r0  ; List_4
    //     0x80458c: stur            w0, [x3, #0x17]
    // 0x804590: LoadField: r4 = r1->field_7
    //     0x804590: ldur            w4, [x1, #7]
    // 0x804594: DecompressPointer r4
    //     0x804594: add             x4, x4, HEAP, lsl #32
    // 0x804598: stur            x4, [fp, #-0x30]
    // 0x80459c: LoadField: r0 = r1->field_b
    //     0x80459c: ldur            w0, [x1, #0xb]
    // 0x8045a0: r5 = LoadInt32Instr(r0)
    //     0x8045a0: sbfx            x5, x0, #1, #0x1f
    // 0x8045a4: stur            x5, [fp, #-0x28]
    // 0x8045a8: LoadField: r6 = r1->field_f
    //     0x8045a8: ldur            w6, [x1, #0xf]
    // 0x8045ac: DecompressPointer r6
    //     0x8045ac: add             x6, x6, HEAP, lsl #32
    // 0x8045b0: stur            x6, [fp, #-0x20]
    // 0x8045b4: r0 = 0
    //     0x8045b4: movz            x0, #0
    // 0x8045b8: CheckStackOverflow
    //     0x8045b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8045bc: cmp             SP, x16
    //     0x8045c0: b.ls            #0x8046b4
    // 0x8045c4: cmp             x0, x5
    // 0x8045c8: b.ge            #0x804698
    // 0x8045cc: ArrayLoad: r7 = r6[r0]  ; Unknown_4
    //     0x8045cc: add             x16, x6, x0, lsl #2
    //     0x8045d0: ldur            w7, [x16, #0xf]
    // 0x8045d4: DecompressPointer r7
    //     0x8045d4: add             x7, x7, HEAP, lsl #32
    // 0x8045d8: stur            x7, [fp, #-0x18]
    // 0x8045dc: add             x8, x0, #1
    // 0x8045e0: stur            x8, [fp, #-0x10]
    // 0x8045e4: cmp             w7, NULL
    // 0x8045e8: b.ne            #0x80461c
    // 0x8045ec: mov             x0, x7
    // 0x8045f0: mov             x2, x4
    // 0x8045f4: r1 = Null
    //     0x8045f4: mov             x1, NULL
    // 0x8045f8: cmp             w2, NULL
    // 0x8045fc: b.eq            #0x80461c
    // 0x804600: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x804600: ldur            w4, [x2, #0x17]
    // 0x804604: DecompressPointer r4
    //     0x804604: add             x4, x4, HEAP, lsl #32
    // 0x804608: r8 = X0
    //     0x804608: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x80460c: LoadField: r9 = r4->field_7
    //     0x80460c: ldur            x9, [x4, #7]
    // 0x804610: r3 = Null
    //     0x804610: add             x3, PP, #0x57, lsl #12  ; [pp+0x57340] Null
    //     0x804614: ldr             x3, [x3, #0x340]
    // 0x804618: blr             x9
    // 0x80461c: ldur            x1, [fp, #-0x18]
    // 0x804620: LoadField: r2 = r1->field_63
    //     0x804620: ldur            w2, [x1, #0x63]
    // 0x804624: DecompressPointer r2
    //     0x804624: add             x2, x2, HEAP, lsl #32
    // 0x804628: cmp             w2, #2
    // 0x80462c: b.ne            #0x804678
    // 0x804630: LoadField: r2 = r1->field_5f
    //     0x804630: ldur            w2, [x1, #0x5f]
    // 0x804634: DecompressPointer r2
    //     0x804634: add             x2, x2, HEAP, lsl #32
    // 0x804638: cmp             w2, NULL
    // 0x80463c: b.ne            #0x804648
    // 0x804640: r1 = 0
    //     0x804640: movz            x1, #0
    // 0x804644: b               #0x804654
    // 0x804648: r1 = LoadInt32Instr(r2)
    //     0x804648: sbfx            x1, x2, #1, #0x1f
    //     0x80464c: tbz             w2, #0, #0x804654
    //     0x804650: ldur            x1, [x2, #7]
    // 0x804654: cmp             x1, #1
    // 0x804658: b.gt            #0x804668
    // 0x80465c: ldur            x1, [fp, #-8]
    // 0x804660: r2 = true
    //     0x804660: add             x2, NULL, #0x20  ; true
    // 0x804664: b               #0x804680
    // 0x804668: ldur            x1, [fp, #-8]
    // 0x80466c: r2 = true
    //     0x80466c: add             x2, NULL, #0x20  ; true
    // 0x804670: ArrayStore: r1[0] = r2  ; List_4
    //     0x804670: stur            w2, [x1, #0x17]
    // 0x804674: b               #0x804698
    // 0x804678: ldur            x1, [fp, #-8]
    // 0x80467c: r2 = true
    //     0x80467c: add             x2, NULL, #0x20  ; true
    // 0x804680: ldur            x0, [fp, #-0x10]
    // 0x804684: mov             x3, x1
    // 0x804688: ldur            x4, [fp, #-0x30]
    // 0x80468c: ldur            x6, [fp, #-0x20]
    // 0x804690: ldur            x5, [fp, #-0x28]
    // 0x804694: b               #0x8045b8
    // 0x804698: r0 = Null
    //     0x804698: mov             x0, NULL
    // 0x80469c: LeaveFrame
    //     0x80469c: mov             SP, fp
    //     0x8046a0: ldp             fp, lr, [SP], #0x10
    // 0x8046a4: ret
    //     0x8046a4: ret             
    // 0x8046a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8046a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8046ac: b               #0x804538
    // 0x8046b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8046b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8046b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8046b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8046b8: b               #0x8045c4
  }
  _ _initializeCatalogueQuantities(/* No info */) {
    // ** addr: 0x8046bc, size: 0x208
    // 0x8046bc: EnterFrame
    //     0x8046bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8046c0: mov             fp, SP
    // 0x8046c4: AllocStack(0x40)
    //     0x8046c4: sub             SP, SP, #0x40
    // 0x8046c8: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x8046c8: mov             x0, x1
    //     0x8046cc: stur            x1, [fp, #-8]
    // 0x8046d0: CheckStackOverflow
    //     0x8046d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8046d4: cmp             SP, x16
    //     0x8046d8: b.ls            #0x8048ac
    // 0x8046dc: LoadField: r1 = r0->field_b
    //     0x8046dc: ldur            w1, [x0, #0xb]
    // 0x8046e0: DecompressPointer r1
    //     0x8046e0: add             x1, x1, HEAP, lsl #32
    // 0x8046e4: cmp             w1, NULL
    // 0x8046e8: b.eq            #0x8048b4
    // 0x8046ec: LoadField: r2 = r1->field_b
    //     0x8046ec: ldur            w2, [x1, #0xb]
    // 0x8046f0: DecompressPointer r2
    //     0x8046f0: add             x2, x2, HEAP, lsl #32
    // 0x8046f4: cmp             w2, NULL
    // 0x8046f8: b.ne            #0x804704
    // 0x8046fc: r1 = Null
    //     0x8046fc: mov             x1, NULL
    // 0x804700: b               #0x80470c
    // 0x804704: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x804704: ldur            w1, [x2, #0x17]
    // 0x804708: DecompressPointer r1
    //     0x804708: add             x1, x1, HEAP, lsl #32
    // 0x80470c: cmp             w1, NULL
    // 0x804710: b.ne            #0x804728
    // 0x804714: r1 = <Catalogue>
    //     0x804714: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x804718: ldr             x1, [x1, #0x418]
    // 0x80471c: r2 = 0
    //     0x80471c: movz            x2, #0
    // 0x804720: r0 = _GrowableList()
    //     0x804720: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x804724: b               #0x80472c
    // 0x804728: mov             x0, x1
    // 0x80472c: stur            x0, [fp, #-0x10]
    // 0x804730: LoadField: r1 = r0->field_b
    //     0x804730: ldur            w1, [x0, #0xb]
    // 0x804734: r2 = LoadInt32Instr(r1)
    //     0x804734: sbfx            x2, x1, #1, #0x1f
    // 0x804738: r1 = <int>
    //     0x804738: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0x80473c: r0 = _GrowableList()
    //     0x80473c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x804740: mov             x3, x0
    // 0x804744: stur            x3, [fp, #-0x40]
    // 0x804748: LoadField: r0 = r3->field_b
    //     0x804748: ldur            w0, [x3, #0xb]
    // 0x80474c: r4 = LoadInt32Instr(r0)
    //     0x80474c: sbfx            x4, x0, #1, #0x1f
    // 0x804750: ldur            x0, [fp, #-0x10]
    // 0x804754: stur            x4, [fp, #-0x38]
    // 0x804758: LoadField: r1 = r0->field_b
    //     0x804758: ldur            w1, [x0, #0xb]
    // 0x80475c: r5 = LoadInt32Instr(r1)
    //     0x80475c: sbfx            x5, x1, #1, #0x1f
    // 0x804760: stur            x5, [fp, #-0x30]
    // 0x804764: LoadField: r6 = r0->field_f
    //     0x804764: ldur            w6, [x0, #0xf]
    // 0x804768: DecompressPointer r6
    //     0x804768: add             x6, x6, HEAP, lsl #32
    // 0x80476c: stur            x6, [fp, #-0x28]
    // 0x804770: LoadField: r7 = r3->field_f
    //     0x804770: ldur            w7, [x3, #0xf]
    // 0x804774: DecompressPointer r7
    //     0x804774: add             x7, x7, HEAP, lsl #32
    // 0x804778: stur            x7, [fp, #-0x20]
    // 0x80477c: r8 = 0
    //     0x80477c: movz            x8, #0
    // 0x804780: stur            x8, [fp, #-0x18]
    // 0x804784: CheckStackOverflow
    //     0x804784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804788: cmp             SP, x16
    //     0x80478c: b.ls            #0x8048b8
    // 0x804790: cmp             x8, x4
    // 0x804794: b.ge            #0x804878
    // 0x804798: mov             x0, x5
    // 0x80479c: mov             x1, x8
    // 0x8047a0: cmp             x1, x0
    // 0x8047a4: b.hs            #0x8048c0
    // 0x8047a8: ArrayLoad: r0 = r6[r8]  ; Unknown_4
    //     0x8047a8: add             x16, x6, x8, lsl #2
    //     0x8047ac: ldur            w0, [x16, #0xf]
    // 0x8047b0: DecompressPointer r0
    //     0x8047b0: add             x0, x0, HEAP, lsl #32
    // 0x8047b4: LoadField: r1 = r0->field_5f
    //     0x8047b4: ldur            w1, [x0, #0x5f]
    // 0x8047b8: DecompressPointer r1
    //     0x8047b8: add             x1, x1, HEAP, lsl #32
    // 0x8047bc: cmp             w1, NULL
    // 0x8047c0: b.ne            #0x8047cc
    // 0x8047c4: r2 = 0
    //     0x8047c4: movz            x2, #0
    // 0x8047c8: b               #0x8047dc
    // 0x8047cc: r0 = LoadInt32Instr(r1)
    //     0x8047cc: sbfx            x0, x1, #1, #0x1f
    //     0x8047d0: tbz             w1, #0, #0x8047d8
    //     0x8047d4: ldur            x0, [x1, #7]
    // 0x8047d8: mov             x2, x0
    // 0x8047dc: r0 = BoxInt64Instr(r2)
    //     0x8047dc: sbfiz           x0, x2, #1, #0x1f
    //     0x8047e0: cmp             x2, x0, asr #1
    //     0x8047e4: b.eq            #0x8047f0
    //     0x8047e8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8047ec: stur            x2, [x0, #7]
    // 0x8047f0: mov             x9, x0
    // 0x8047f4: r2 = Null
    //     0x8047f4: mov             x2, NULL
    // 0x8047f8: r1 = Null
    //     0x8047f8: mov             x1, NULL
    // 0x8047fc: stur            x9, [fp, #-0x10]
    // 0x804800: branchIfSmi(r0, 0x804828)
    //     0x804800: tbz             w0, #0, #0x804828
    // 0x804804: r4 = LoadClassIdInstr(r0)
    //     0x804804: ldur            x4, [x0, #-1]
    //     0x804808: ubfx            x4, x4, #0xc, #0x14
    // 0x80480c: sub             x4, x4, #0x3c
    // 0x804810: cmp             x4, #1
    // 0x804814: b.ls            #0x804828
    // 0x804818: r8 = int
    //     0x804818: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x80481c: r3 = Null
    //     0x80481c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57350] Null
    //     0x804820: ldr             x3, [x3, #0x350]
    // 0x804824: r0 = int()
    //     0x804824: bl              #0x16fc548  ; IsType_int_Stub
    // 0x804828: ldur            x1, [fp, #-0x20]
    // 0x80482c: ldur            x0, [fp, #-0x10]
    // 0x804830: ldur            x2, [fp, #-0x18]
    // 0x804834: ArrayStore: r1[r2] = r0  ; List_4
    //     0x804834: add             x25, x1, x2, lsl #2
    //     0x804838: add             x25, x25, #0xf
    //     0x80483c: str             w0, [x25]
    //     0x804840: tbz             w0, #0, #0x80485c
    //     0x804844: ldurb           w16, [x1, #-1]
    //     0x804848: ldurb           w17, [x0, #-1]
    //     0x80484c: and             x16, x17, x16, lsr #2
    //     0x804850: tst             x16, HEAP, lsr #32
    //     0x804854: b.eq            #0x80485c
    //     0x804858: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x80485c: add             x8, x2, #1
    // 0x804860: ldur            x3, [fp, #-0x40]
    // 0x804864: ldur            x7, [fp, #-0x20]
    // 0x804868: ldur            x6, [fp, #-0x28]
    // 0x80486c: ldur            x4, [fp, #-0x38]
    // 0x804870: ldur            x5, [fp, #-0x30]
    // 0x804874: b               #0x804780
    // 0x804878: ldur            x1, [fp, #-8]
    // 0x80487c: ldur            x0, [fp, #-0x40]
    // 0x804880: StoreField: r1->field_13 = r0
    //     0x804880: stur            w0, [x1, #0x13]
    //     0x804884: ldurb           w16, [x1, #-1]
    //     0x804888: ldurb           w17, [x0, #-1]
    //     0x80488c: and             x16, x17, x16, lsr #2
    //     0x804890: tst             x16, HEAP, lsr #32
    //     0x804894: b.eq            #0x80489c
    //     0x804898: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x80489c: r0 = Null
    //     0x80489c: mov             x0, NULL
    // 0x8048a0: LeaveFrame
    //     0x8048a0: mov             SP, fp
    //     0x8048a4: ldp             fp, lr, [SP], #0x10
    // 0x8048a8: ret
    //     0x8048a8: ret             
    // 0x8048ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8048ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8048b0: b               #0x8046dc
    // 0x8048b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8048b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8048b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8048b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8048bc: b               #0x804790
    // 0x8048c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8048c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x93da18, size: 0x48
    // 0x93da18: EnterFrame
    //     0x93da18: stp             fp, lr, [SP, #-0x10]!
    //     0x93da1c: mov             fp, SP
    // 0x93da20: AllocStack(0x8)
    //     0x93da20: sub             SP, SP, #8
    // 0x93da24: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x93da24: mov             x0, x1
    //     0x93da28: stur            x1, [fp, #-8]
    // 0x93da2c: CheckStackOverflow
    //     0x93da2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93da30: cmp             SP, x16
    //     0x93da34: b.ls            #0x93da58
    // 0x93da38: mov             x1, x0
    // 0x93da3c: r0 = _initializeCatalogueQuantities()
    //     0x93da3c: bl              #0x8046bc  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_initializeCatalogueQuantities
    // 0x93da40: ldur            x1, [fp, #-8]
    // 0x93da44: r0 = _checkAvailableQuantity()
    //     0x93da44: bl              #0x804518  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_checkAvailableQuantity
    // 0x93da48: r0 = Null
    //     0x93da48: mov             x0, NULL
    // 0x93da4c: LeaveFrame
    //     0x93da4c: mov             SP, fp
    //     0x93da50: ldp             fp, lr, [SP], #0x10
    // 0x93da54: ret
    //     0x93da54: ret             
    // 0x93da58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93da58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93da5c: b               #0x93da38
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9d5624, size: 0xd8
    // 0x9d5624: EnterFrame
    //     0x9d5624: stp             fp, lr, [SP, #-0x10]!
    //     0x9d5628: mov             fp, SP
    // 0x9d562c: ldr             x2, [fp, #0x10]
    // 0x9d5630: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9d5630: ldur            w3, [x2, #0x17]
    // 0x9d5634: DecompressPointer r3
    //     0x9d5634: add             x3, x3, HEAP, lsl #32
    // 0x9d5638: LoadField: r2 = r3->field_f
    //     0x9d5638: ldur            w2, [x3, #0xf]
    // 0x9d563c: DecompressPointer r2
    //     0x9d563c: add             x2, x2, HEAP, lsl #32
    // 0x9d5640: LoadField: r4 = r2->field_13
    //     0x9d5640: ldur            w4, [x2, #0x13]
    // 0x9d5644: DecompressPointer r4
    //     0x9d5644: add             x4, x4, HEAP, lsl #32
    // 0x9d5648: LoadField: r5 = r3->field_13
    //     0x9d5648: ldur            w5, [x3, #0x13]
    // 0x9d564c: DecompressPointer r5
    //     0x9d564c: add             x5, x5, HEAP, lsl #32
    // 0x9d5650: LoadField: r3 = r4->field_b
    //     0x9d5650: ldur            w3, [x4, #0xb]
    // 0x9d5654: r6 = LoadInt32Instr(r5)
    //     0x9d5654: sbfx            x6, x5, #1, #0x1f
    //     0x9d5658: tbz             w5, #0, #0x9d5660
    //     0x9d565c: ldur            x6, [x5, #7]
    // 0x9d5660: r0 = LoadInt32Instr(r3)
    //     0x9d5660: sbfx            x0, x3, #1, #0x1f
    // 0x9d5664: mov             x1, x6
    // 0x9d5668: cmp             x1, x0
    // 0x9d566c: b.hs            #0x9d56f8
    // 0x9d5670: LoadField: r3 = r4->field_f
    //     0x9d5670: ldur            w3, [x4, #0xf]
    // 0x9d5674: DecompressPointer r3
    //     0x9d5674: add             x3, x3, HEAP, lsl #32
    // 0x9d5678: ArrayLoad: r4 = r3[r6]  ; Unknown_4
    //     0x9d5678: add             x16, x3, x6, lsl #2
    //     0x9d567c: ldur            w4, [x16, #0xf]
    // 0x9d5680: DecompressPointer r4
    //     0x9d5680: add             x4, x4, HEAP, lsl #32
    // 0x9d5684: r5 = LoadInt32Instr(r4)
    //     0x9d5684: sbfx            x5, x4, #1, #0x1f
    //     0x9d5688: tbz             w4, #0, #0x9d5690
    //     0x9d568c: ldur            x5, [x4, #7]
    // 0x9d5690: add             x4, x5, #1
    // 0x9d5694: r0 = BoxInt64Instr(r4)
    //     0x9d5694: sbfiz           x0, x4, #1, #0x1f
    //     0x9d5698: cmp             x4, x0, asr #1
    //     0x9d569c: b.eq            #0x9d56a8
    //     0x9d56a0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9d56a4: stur            x4, [x0, #7]
    // 0x9d56a8: mov             x1, x3
    // 0x9d56ac: ArrayStore: r1[r6] = r0  ; List_4
    //     0x9d56ac: add             x25, x1, x6, lsl #2
    //     0x9d56b0: add             x25, x25, #0xf
    //     0x9d56b4: str             w0, [x25]
    //     0x9d56b8: tbz             w0, #0, #0x9d56d4
    //     0x9d56bc: ldurb           w16, [x1, #-1]
    //     0x9d56c0: ldurb           w17, [x0, #-1]
    //     0x9d56c4: and             x16, x17, x16, lsr #2
    //     0x9d56c8: tst             x16, HEAP, lsr #32
    //     0x9d56cc: b.eq            #0x9d56d4
    //     0x9d56d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9d56d4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9d56d4: ldur            w1, [x2, #0x17]
    // 0x9d56d8: DecompressPointer r1
    //     0x9d56d8: add             x1, x1, HEAP, lsl #32
    // 0x9d56dc: tbnz            w1, #4, #0x9d56e8
    // 0x9d56e0: r1 = false
    //     0x9d56e0: add             x1, NULL, #0x30  ; false
    // 0x9d56e4: ArrayStore: r2[0] = r1  ; List_4
    //     0x9d56e4: stur            w1, [x2, #0x17]
    // 0x9d56e8: r0 = Null
    //     0x9d56e8: mov             x0, NULL
    // 0x9d56ec: LeaveFrame
    //     0x9d56ec: mov             SP, fp
    //     0x9d56f0: ldp             fp, lr, [SP], #0x10
    // 0x9d56f4: ret
    //     0x9d56f4: ret             
    // 0x9d56f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d56f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _handleQuantityIncrease(/* No info */) {
    // ** addr: 0x9d56fc, size: 0x304
    // 0x9d56fc: EnterFrame
    //     0x9d56fc: stp             fp, lr, [SP, #-0x10]!
    //     0x9d5700: mov             fp, SP
    // 0x9d5704: AllocStack(0x38)
    //     0x9d5704: sub             SP, SP, #0x38
    // 0x9d5708: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x9d5708: mov             x0, x1
    //     0x9d570c: stur            x1, [fp, #-8]
    //     0x9d5710: mov             x1, x2
    //     0x9d5714: stur            x2, [fp, #-0x10]
    // 0x9d5718: CheckStackOverflow
    //     0x9d5718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d571c: cmp             SP, x16
    //     0x9d5720: b.ls            #0x9d59e0
    // 0x9d5724: r1 = 2
    //     0x9d5724: movz            x1, #0x2
    // 0x9d5728: r0 = AllocateContext()
    //     0x9d5728: bl              #0x16f6108  ; AllocateContextStub
    // 0x9d572c: mov             x2, x0
    // 0x9d5730: ldur            x3, [fp, #-8]
    // 0x9d5734: StoreField: r2->field_f = r3
    //     0x9d5734: stur            w3, [x2, #0xf]
    // 0x9d5738: ldur            x4, [fp, #-0x10]
    // 0x9d573c: r0 = BoxInt64Instr(r4)
    //     0x9d573c: sbfiz           x0, x4, #1, #0x1f
    //     0x9d5740: cmp             x4, x0, asr #1
    //     0x9d5744: b.eq            #0x9d5750
    //     0x9d5748: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9d574c: stur            x4, [x0, #7]
    // 0x9d5750: StoreField: r2->field_13 = r0
    //     0x9d5750: stur            w0, [x2, #0x13]
    // 0x9d5754: LoadField: r0 = r3->field_b
    //     0x9d5754: ldur            w0, [x3, #0xb]
    // 0x9d5758: DecompressPointer r0
    //     0x9d5758: add             x0, x0, HEAP, lsl #32
    // 0x9d575c: cmp             w0, NULL
    // 0x9d5760: b.eq            #0x9d59e8
    // 0x9d5764: LoadField: r1 = r0->field_b
    //     0x9d5764: ldur            w1, [x0, #0xb]
    // 0x9d5768: DecompressPointer r1
    //     0x9d5768: add             x1, x1, HEAP, lsl #32
    // 0x9d576c: cmp             w1, NULL
    // 0x9d5770: b.ne            #0x9d577c
    // 0x9d5774: r5 = Null
    //     0x9d5774: mov             x5, NULL
    // 0x9d5778: b               #0x9d57b4
    // 0x9d577c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x9d577c: ldur            w5, [x1, #0x17]
    // 0x9d5780: DecompressPointer r5
    //     0x9d5780: add             x5, x5, HEAP, lsl #32
    // 0x9d5784: LoadField: r0 = r5->field_b
    //     0x9d5784: ldur            w0, [x5, #0xb]
    // 0x9d5788: r1 = LoadInt32Instr(r0)
    //     0x9d5788: sbfx            x1, x0, #1, #0x1f
    // 0x9d578c: mov             x0, x1
    // 0x9d5790: mov             x1, x4
    // 0x9d5794: cmp             x1, x0
    // 0x9d5798: b.hs            #0x9d59ec
    // 0x9d579c: LoadField: r0 = r5->field_f
    //     0x9d579c: ldur            w0, [x5, #0xf]
    // 0x9d57a0: DecompressPointer r0
    //     0x9d57a0: add             x0, x0, HEAP, lsl #32
    // 0x9d57a4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d57a4: add             x16, x0, x4, lsl #2
    //     0x9d57a8: ldur            w1, [x16, #0xf]
    // 0x9d57ac: DecompressPointer r1
    //     0x9d57ac: add             x1, x1, HEAP, lsl #32
    // 0x9d57b0: mov             x5, x1
    // 0x9d57b4: stur            x5, [fp, #-0x18]
    // 0x9d57b8: cmp             w5, NULL
    // 0x9d57bc: b.ne            #0x9d57c8
    // 0x9d57c0: r0 = Null
    //     0x9d57c0: mov             x0, NULL
    // 0x9d57c4: b               #0x9d57d0
    // 0x9d57c8: LoadField: r0 = r5->field_63
    //     0x9d57c8: ldur            w0, [x5, #0x63]
    // 0x9d57cc: DecompressPointer r0
    //     0x9d57cc: add             x0, x0, HEAP, lsl #32
    // 0x9d57d0: cmp             w0, NULL
    // 0x9d57d4: b.ne            #0x9d57e0
    // 0x9d57d8: r6 = 0
    //     0x9d57d8: movz            x6, #0
    // 0x9d57dc: b               #0x9d57f0
    // 0x9d57e0: r1 = LoadInt32Instr(r0)
    //     0x9d57e0: sbfx            x1, x0, #1, #0x1f
    //     0x9d57e4: tbz             w0, #0, #0x9d57ec
    //     0x9d57e8: ldur            x1, [x0, #7]
    // 0x9d57ec: mov             x6, x1
    // 0x9d57f0: cmp             w5, NULL
    // 0x9d57f4: b.eq            #0x9d580c
    // 0x9d57f8: LoadField: r0 = r5->field_33
    //     0x9d57f8: ldur            w0, [x5, #0x33]
    // 0x9d57fc: DecompressPointer r0
    //     0x9d57fc: add             x0, x0, HEAP, lsl #32
    // 0x9d5800: r16 = true
    //     0x9d5800: add             x16, NULL, #0x20  ; true
    // 0x9d5804: cmp             w0, w16
    // 0x9d5808: b.eq            #0x9d59d0
    // 0x9d580c: cmp             w5, NULL
    // 0x9d5810: b.eq            #0x9d58c8
    // 0x9d5814: LoadField: r0 = r5->field_83
    //     0x9d5814: ldur            w0, [x5, #0x83]
    // 0x9d5818: DecompressPointer r0
    //     0x9d5818: add             x0, x0, HEAP, lsl #32
    // 0x9d581c: cmp             w0, NULL
    // 0x9d5820: b.eq            #0x9d58c8
    // 0x9d5824: LoadField: r7 = r3->field_13
    //     0x9d5824: ldur            w7, [x3, #0x13]
    // 0x9d5828: DecompressPointer r7
    //     0x9d5828: add             x7, x7, HEAP, lsl #32
    // 0x9d582c: LoadField: r0 = r7->field_b
    //     0x9d582c: ldur            w0, [x7, #0xb]
    // 0x9d5830: r1 = LoadInt32Instr(r0)
    //     0x9d5830: sbfx            x1, x0, #1, #0x1f
    // 0x9d5834: mov             x0, x1
    // 0x9d5838: mov             x1, x4
    // 0x9d583c: cmp             x1, x0
    // 0x9d5840: b.hs            #0x9d59f0
    // 0x9d5844: LoadField: r0 = r7->field_f
    //     0x9d5844: ldur            w0, [x7, #0xf]
    // 0x9d5848: DecompressPointer r0
    //     0x9d5848: add             x0, x0, HEAP, lsl #32
    // 0x9d584c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d584c: add             x16, x0, x4, lsl #2
    //     0x9d5850: ldur            w1, [x16, #0xf]
    // 0x9d5854: DecompressPointer r1
    //     0x9d5854: add             x1, x1, HEAP, lsl #32
    // 0x9d5858: r0 = LoadInt32Instr(r1)
    //     0x9d5858: sbfx            x0, x1, #1, #0x1f
    //     0x9d585c: tbz             w1, #0, #0x9d5864
    //     0x9d5860: ldur            x0, [x1, #7]
    // 0x9d5864: cmp             x0, x6
    // 0x9d5868: b.ge            #0x9d5884
    // 0x9d586c: r1 = Function '<anonymous closure>':.
    //     0x9d586c: add             x1, PP, #0x57, lsl #12  ; [pp+0x572a8] AnonymousClosure: (0x9d5a00), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_handleQuantityIncrease (0x9d56fc)
    //     0x9d5870: ldr             x1, [x1, #0x2a8]
    // 0x9d5874: r0 = AllocateClosure()
    //     0x9d5874: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9d5878: ldur            x1, [fp, #-8]
    // 0x9d587c: mov             x2, x0
    // 0x9d5880: r0 = setState()
    //     0x9d5880: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9d5884: ldur            x3, [fp, #-8]
    // 0x9d5888: ldur            x5, [fp, #-0x18]
    // 0x9d588c: LoadField: r0 = r3->field_b
    //     0x9d588c: ldur            w0, [x3, #0xb]
    // 0x9d5890: DecompressPointer r0
    //     0x9d5890: add             x0, x0, HEAP, lsl #32
    // 0x9d5894: cmp             w0, NULL
    // 0x9d5898: b.eq            #0x9d59f4
    // 0x9d589c: LoadField: r1 = r5->field_5f
    //     0x9d589c: ldur            w1, [x5, #0x5f]
    // 0x9d58a0: DecompressPointer r1
    //     0x9d58a0: add             x1, x1, HEAP, lsl #32
    // 0x9d58a4: LoadField: r2 = r0->field_1f
    //     0x9d58a4: ldur            w2, [x0, #0x1f]
    // 0x9d58a8: DecompressPointer r2
    //     0x9d58a8: add             x2, x2, HEAP, lsl #32
    // 0x9d58ac: stp             x1, x2, [SP, #8]
    // 0x9d58b0: str             x5, [SP]
    // 0x9d58b4: mov             x0, x2
    // 0x9d58b8: ClosureCall
    //     0x9d58b8: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x9d58bc: ldur            x2, [x0, #0x1f]
    //     0x9d58c0: blr             x2
    // 0x9d58c4: b               #0x9d59d0
    // 0x9d58c8: LoadField: r7 = r3->field_13
    //     0x9d58c8: ldur            w7, [x3, #0x13]
    // 0x9d58cc: DecompressPointer r7
    //     0x9d58cc: add             x7, x7, HEAP, lsl #32
    // 0x9d58d0: LoadField: r0 = r7->field_b
    //     0x9d58d0: ldur            w0, [x7, #0xb]
    // 0x9d58d4: r1 = LoadInt32Instr(r0)
    //     0x9d58d4: sbfx            x1, x0, #1, #0x1f
    // 0x9d58d8: mov             x0, x1
    // 0x9d58dc: mov             x1, x4
    // 0x9d58e0: cmp             x1, x0
    // 0x9d58e4: b.hs            #0x9d59f8
    // 0x9d58e8: LoadField: r0 = r7->field_f
    //     0x9d58e8: ldur            w0, [x7, #0xf]
    // 0x9d58ec: DecompressPointer r0
    //     0x9d58ec: add             x0, x0, HEAP, lsl #32
    // 0x9d58f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d58f0: add             x16, x0, x4, lsl #2
    //     0x9d58f4: ldur            w1, [x16, #0xf]
    // 0x9d58f8: DecompressPointer r1
    //     0x9d58f8: add             x1, x1, HEAP, lsl #32
    // 0x9d58fc: r0 = LoadInt32Instr(r1)
    //     0x9d58fc: sbfx            x0, x1, #1, #0x1f
    //     0x9d5900: tbz             w1, #0, #0x9d5908
    //     0x9d5904: ldur            x0, [x1, #7]
    // 0x9d5908: cmp             x0, x6
    // 0x9d590c: b.ge            #0x9d59d0
    // 0x9d5910: r1 = Function '<anonymous closure>':.
    //     0x9d5910: add             x1, PP, #0x57, lsl #12  ; [pp+0x572b0] AnonymousClosure: (0x9d5624), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_handleQuantityIncrease (0x9d56fc)
    //     0x9d5914: ldr             x1, [x1, #0x2b0]
    // 0x9d5918: r0 = AllocateClosure()
    //     0x9d5918: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9d591c: ldur            x1, [fp, #-8]
    // 0x9d5920: mov             x2, x0
    // 0x9d5924: r0 = setState()
    //     0x9d5924: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9d5928: ldur            x0, [fp, #-0x18]
    // 0x9d592c: cmp             w0, NULL
    // 0x9d5930: b.ne            #0x9d593c
    // 0x9d5934: r1 = Null
    //     0x9d5934: mov             x1, NULL
    // 0x9d5938: b               #0x9d5944
    // 0x9d593c: LoadField: r1 = r0->field_b
    //     0x9d593c: ldur            w1, [x0, #0xb]
    // 0x9d5940: DecompressPointer r1
    //     0x9d5940: add             x1, x1, HEAP, lsl #32
    // 0x9d5944: stur            x1, [fp, #-0x20]
    // 0x9d5948: cmp             w0, NULL
    // 0x9d594c: b.ne            #0x9d5958
    // 0x9d5950: r2 = Null
    //     0x9d5950: mov             x2, NULL
    // 0x9d5954: b               #0x9d597c
    // 0x9d5958: LoadField: r2 = r0->field_1f
    //     0x9d5958: ldur            w2, [x0, #0x1f]
    // 0x9d595c: DecompressPointer r2
    //     0x9d595c: add             x2, x2, HEAP, lsl #32
    // 0x9d5960: cmp             w2, NULL
    // 0x9d5964: b.ne            #0x9d5970
    // 0x9d5968: r0 = Null
    //     0x9d5968: mov             x0, NULL
    // 0x9d596c: b               #0x9d5978
    // 0x9d5970: LoadField: r0 = r2->field_b
    //     0x9d5970: ldur            w0, [x2, #0xb]
    // 0x9d5974: DecompressPointer r0
    //     0x9d5974: add             x0, x0, HEAP, lsl #32
    // 0x9d5978: mov             x2, x0
    // 0x9d597c: ldur            x0, [fp, #-8]
    // 0x9d5980: stur            x2, [fp, #-0x18]
    // 0x9d5984: r0 = AddToBagRequest()
    //     0x9d5984: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0x9d5988: mov             x1, x0
    // 0x9d598c: ldur            x0, [fp, #-0x20]
    // 0x9d5990: StoreField: r1->field_7 = r0
    //     0x9d5990: stur            w0, [x1, #7]
    // 0x9d5994: ldur            x0, [fp, #-0x18]
    // 0x9d5998: StoreField: r1->field_b = r0
    //     0x9d5998: stur            w0, [x1, #0xb]
    // 0x9d599c: r0 = 1
    //     0x9d599c: movz            x0, #0x1
    // 0x9d59a0: StoreField: r1->field_f = r0
    //     0x9d59a0: stur            x0, [x1, #0xf]
    // 0x9d59a4: ldur            x0, [fp, #-8]
    // 0x9d59a8: LoadField: r2 = r0->field_b
    //     0x9d59a8: ldur            w2, [x0, #0xb]
    // 0x9d59ac: DecompressPointer r2
    //     0x9d59ac: add             x2, x2, HEAP, lsl #32
    // 0x9d59b0: cmp             w2, NULL
    // 0x9d59b4: b.eq            #0x9d59fc
    // 0x9d59b8: LoadField: r0 = r2->field_23
    //     0x9d59b8: ldur            w0, [x2, #0x23]
    // 0x9d59bc: DecompressPointer r0
    //     0x9d59bc: add             x0, x0, HEAP, lsl #32
    // 0x9d59c0: stp             x1, x0, [SP]
    // 0x9d59c4: ClosureCall
    //     0x9d59c4: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9d59c8: ldur            x2, [x0, #0x1f]
    //     0x9d59cc: blr             x2
    // 0x9d59d0: r0 = Null
    //     0x9d59d0: mov             x0, NULL
    // 0x9d59d4: LeaveFrame
    //     0x9d59d4: mov             SP, fp
    //     0x9d59d8: ldp             fp, lr, [SP], #0x10
    // 0x9d59dc: ret
    //     0x9d59dc: ret             
    // 0x9d59e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d59e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d59e4: b               #0x9d5724
    // 0x9d59e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d59e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d59ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d59ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9d59f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d59f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9d59f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d59f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d59f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d59f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9d59fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d59fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9d5a00, size: 0x30
    // 0x9d5a00: ldr             x1, [SP]
    // 0x9d5a04: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9d5a04: ldur            w2, [x1, #0x17]
    // 0x9d5a08: DecompressPointer r2
    //     0x9d5a08: add             x2, x2, HEAP, lsl #32
    // 0x9d5a0c: LoadField: r1 = r2->field_f
    //     0x9d5a0c: ldur            w1, [x2, #0xf]
    // 0x9d5a10: DecompressPointer r1
    //     0x9d5a10: add             x1, x1, HEAP, lsl #32
    // 0x9d5a14: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9d5a14: ldur            w2, [x1, #0x17]
    // 0x9d5a18: DecompressPointer r2
    //     0x9d5a18: add             x2, x2, HEAP, lsl #32
    // 0x9d5a1c: tbnz            w2, #4, #0x9d5a28
    // 0x9d5a20: r2 = false
    //     0x9d5a20: add             x2, NULL, #0x30  ; false
    // 0x9d5a24: ArrayStore: r1[0] = r2  ; List_4
    //     0x9d5a24: stur            w2, [x1, #0x17]
    // 0x9d5a28: r0 = Null
    //     0x9d5a28: mov             x0, NULL
    // 0x9d5a2c: ret
    //     0x9d5a2c: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9d5e18, size: 0xd8
    // 0x9d5e18: EnterFrame
    //     0x9d5e18: stp             fp, lr, [SP, #-0x10]!
    //     0x9d5e1c: mov             fp, SP
    // 0x9d5e20: ldr             x2, [fp, #0x10]
    // 0x9d5e24: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9d5e24: ldur            w3, [x2, #0x17]
    // 0x9d5e28: DecompressPointer r3
    //     0x9d5e28: add             x3, x3, HEAP, lsl #32
    // 0x9d5e2c: LoadField: r2 = r3->field_f
    //     0x9d5e2c: ldur            w2, [x3, #0xf]
    // 0x9d5e30: DecompressPointer r2
    //     0x9d5e30: add             x2, x2, HEAP, lsl #32
    // 0x9d5e34: LoadField: r4 = r2->field_13
    //     0x9d5e34: ldur            w4, [x2, #0x13]
    // 0x9d5e38: DecompressPointer r4
    //     0x9d5e38: add             x4, x4, HEAP, lsl #32
    // 0x9d5e3c: LoadField: r5 = r3->field_13
    //     0x9d5e3c: ldur            w5, [x3, #0x13]
    // 0x9d5e40: DecompressPointer r5
    //     0x9d5e40: add             x5, x5, HEAP, lsl #32
    // 0x9d5e44: LoadField: r3 = r4->field_b
    //     0x9d5e44: ldur            w3, [x4, #0xb]
    // 0x9d5e48: r6 = LoadInt32Instr(r5)
    //     0x9d5e48: sbfx            x6, x5, #1, #0x1f
    //     0x9d5e4c: tbz             w5, #0, #0x9d5e54
    //     0x9d5e50: ldur            x6, [x5, #7]
    // 0x9d5e54: r0 = LoadInt32Instr(r3)
    //     0x9d5e54: sbfx            x0, x3, #1, #0x1f
    // 0x9d5e58: mov             x1, x6
    // 0x9d5e5c: cmp             x1, x0
    // 0x9d5e60: b.hs            #0x9d5eec
    // 0x9d5e64: LoadField: r3 = r4->field_f
    //     0x9d5e64: ldur            w3, [x4, #0xf]
    // 0x9d5e68: DecompressPointer r3
    //     0x9d5e68: add             x3, x3, HEAP, lsl #32
    // 0x9d5e6c: ArrayLoad: r4 = r3[r6]  ; Unknown_4
    //     0x9d5e6c: add             x16, x3, x6, lsl #2
    //     0x9d5e70: ldur            w4, [x16, #0xf]
    // 0x9d5e74: DecompressPointer r4
    //     0x9d5e74: add             x4, x4, HEAP, lsl #32
    // 0x9d5e78: r5 = LoadInt32Instr(r4)
    //     0x9d5e78: sbfx            x5, x4, #1, #0x1f
    //     0x9d5e7c: tbz             w4, #0, #0x9d5e84
    //     0x9d5e80: ldur            x5, [x4, #7]
    // 0x9d5e84: sub             x4, x5, #1
    // 0x9d5e88: r0 = BoxInt64Instr(r4)
    //     0x9d5e88: sbfiz           x0, x4, #1, #0x1f
    //     0x9d5e8c: cmp             x4, x0, asr #1
    //     0x9d5e90: b.eq            #0x9d5e9c
    //     0x9d5e94: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9d5e98: stur            x4, [x0, #7]
    // 0x9d5e9c: mov             x1, x3
    // 0x9d5ea0: ArrayStore: r1[r6] = r0  ; List_4
    //     0x9d5ea0: add             x25, x1, x6, lsl #2
    //     0x9d5ea4: add             x25, x25, #0xf
    //     0x9d5ea8: str             w0, [x25]
    //     0x9d5eac: tbz             w0, #0, #0x9d5ec8
    //     0x9d5eb0: ldurb           w16, [x1, #-1]
    //     0x9d5eb4: ldurb           w17, [x0, #-1]
    //     0x9d5eb8: and             x16, x17, x16, lsr #2
    //     0x9d5ebc: tst             x16, HEAP, lsr #32
    //     0x9d5ec0: b.eq            #0x9d5ec8
    //     0x9d5ec4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9d5ec8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9d5ec8: ldur            w1, [x2, #0x17]
    // 0x9d5ecc: DecompressPointer r1
    //     0x9d5ecc: add             x1, x1, HEAP, lsl #32
    // 0x9d5ed0: tbnz            w1, #4, #0x9d5edc
    // 0x9d5ed4: r1 = false
    //     0x9d5ed4: add             x1, NULL, #0x30  ; false
    // 0x9d5ed8: ArrayStore: r2[0] = r1  ; List_4
    //     0x9d5ed8: stur            w1, [x2, #0x17]
    // 0x9d5edc: r0 = Null
    //     0x9d5edc: mov             x0, NULL
    // 0x9d5ee0: LeaveFrame
    //     0x9d5ee0: mov             SP, fp
    //     0x9d5ee4: ldp             fp, lr, [SP], #0x10
    // 0x9d5ee8: ret
    //     0x9d5ee8: ret             
    // 0x9d5eec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d5eec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _handleQuantityDecrease(/* No info */) {
    // ** addr: 0x9d5ef0, size: 0x388
    // 0x9d5ef0: EnterFrame
    //     0x9d5ef0: stp             fp, lr, [SP, #-0x10]!
    //     0x9d5ef4: mov             fp, SP
    // 0x9d5ef8: AllocStack(0x58)
    //     0x9d5ef8: sub             SP, SP, #0x58
    // 0x9d5efc: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x9d5efc: mov             x0, x1
    //     0x9d5f00: stur            x1, [fp, #-8]
    //     0x9d5f04: mov             x1, x2
    //     0x9d5f08: stur            x2, [fp, #-0x10]
    // 0x9d5f0c: CheckStackOverflow
    //     0x9d5f0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9d5f10: cmp             SP, x16
    //     0x9d5f14: b.ls            #0x9d625c
    // 0x9d5f18: r1 = 2
    //     0x9d5f18: movz            x1, #0x2
    // 0x9d5f1c: r0 = AllocateContext()
    //     0x9d5f1c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9d5f20: mov             x2, x0
    // 0x9d5f24: ldur            x3, [fp, #-8]
    // 0x9d5f28: StoreField: r2->field_f = r3
    //     0x9d5f28: stur            w3, [x2, #0xf]
    // 0x9d5f2c: ldur            x4, [fp, #-0x10]
    // 0x9d5f30: r0 = BoxInt64Instr(r4)
    //     0x9d5f30: sbfiz           x0, x4, #1, #0x1f
    //     0x9d5f34: cmp             x4, x0, asr #1
    //     0x9d5f38: b.eq            #0x9d5f44
    //     0x9d5f3c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9d5f40: stur            x4, [x0, #7]
    // 0x9d5f44: StoreField: r2->field_13 = r0
    //     0x9d5f44: stur            w0, [x2, #0x13]
    // 0x9d5f48: LoadField: r5 = r3->field_b
    //     0x9d5f48: ldur            w5, [x3, #0xb]
    // 0x9d5f4c: DecompressPointer r5
    //     0x9d5f4c: add             x5, x5, HEAP, lsl #32
    // 0x9d5f50: cmp             w5, NULL
    // 0x9d5f54: b.eq            #0x9d6264
    // 0x9d5f58: LoadField: r0 = r5->field_b
    //     0x9d5f58: ldur            w0, [x5, #0xb]
    // 0x9d5f5c: DecompressPointer r0
    //     0x9d5f5c: add             x0, x0, HEAP, lsl #32
    // 0x9d5f60: cmp             w0, NULL
    // 0x9d5f64: b.ne            #0x9d5f70
    // 0x9d5f68: r6 = Null
    //     0x9d5f68: mov             x6, NULL
    // 0x9d5f6c: b               #0x9d5fa8
    // 0x9d5f70: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x9d5f70: ldur            w6, [x0, #0x17]
    // 0x9d5f74: DecompressPointer r6
    //     0x9d5f74: add             x6, x6, HEAP, lsl #32
    // 0x9d5f78: LoadField: r0 = r6->field_b
    //     0x9d5f78: ldur            w0, [x6, #0xb]
    // 0x9d5f7c: r1 = LoadInt32Instr(r0)
    //     0x9d5f7c: sbfx            x1, x0, #1, #0x1f
    // 0x9d5f80: mov             x0, x1
    // 0x9d5f84: mov             x1, x4
    // 0x9d5f88: cmp             x1, x0
    // 0x9d5f8c: b.hs            #0x9d6268
    // 0x9d5f90: LoadField: r0 = r6->field_f
    //     0x9d5f90: ldur            w0, [x6, #0xf]
    // 0x9d5f94: DecompressPointer r0
    //     0x9d5f94: add             x0, x0, HEAP, lsl #32
    // 0x9d5f98: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d5f98: add             x16, x0, x4, lsl #2
    //     0x9d5f9c: ldur            w1, [x16, #0xf]
    // 0x9d5fa0: DecompressPointer r1
    //     0x9d5fa0: add             x1, x1, HEAP, lsl #32
    // 0x9d5fa4: mov             x6, x1
    // 0x9d5fa8: stur            x6, [fp, #-0x18]
    // 0x9d5fac: cmp             w6, NULL
    // 0x9d5fb0: b.eq            #0x9d5fc8
    // 0x9d5fb4: LoadField: r0 = r6->field_33
    //     0x9d5fb4: ldur            w0, [x6, #0x33]
    // 0x9d5fb8: DecompressPointer r0
    //     0x9d5fb8: add             x0, x0, HEAP, lsl #32
    // 0x9d5fbc: r16 = true
    //     0x9d5fbc: add             x16, NULL, #0x20  ; true
    // 0x9d5fc0: cmp             w0, w16
    // 0x9d5fc4: b.eq            #0x9d6104
    // 0x9d5fc8: LoadField: r7 = r3->field_13
    //     0x9d5fc8: ldur            w7, [x3, #0x13]
    // 0x9d5fcc: DecompressPointer r7
    //     0x9d5fcc: add             x7, x7, HEAP, lsl #32
    // 0x9d5fd0: LoadField: r0 = r7->field_b
    //     0x9d5fd0: ldur            w0, [x7, #0xb]
    // 0x9d5fd4: r1 = LoadInt32Instr(r0)
    //     0x9d5fd4: sbfx            x1, x0, #1, #0x1f
    // 0x9d5fd8: mov             x0, x1
    // 0x9d5fdc: mov             x1, x4
    // 0x9d5fe0: cmp             x1, x0
    // 0x9d5fe4: b.hs            #0x9d626c
    // 0x9d5fe8: LoadField: r0 = r7->field_f
    //     0x9d5fe8: ldur            w0, [x7, #0xf]
    // 0x9d5fec: DecompressPointer r0
    //     0x9d5fec: add             x0, x0, HEAP, lsl #32
    // 0x9d5ff0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d5ff0: add             x16, x0, x4, lsl #2
    //     0x9d5ff4: ldur            w1, [x16, #0xf]
    // 0x9d5ff8: DecompressPointer r1
    //     0x9d5ff8: add             x1, x1, HEAP, lsl #32
    // 0x9d5ffc: r0 = LoadInt32Instr(r1)
    //     0x9d5ffc: sbfx            x0, x1, #1, #0x1f
    //     0x9d6000: tbz             w1, #0, #0x9d6008
    //     0x9d6004: ldur            x0, [x1, #7]
    // 0x9d6008: cmp             x0, #1
    // 0x9d600c: b.le            #0x9d60f8
    // 0x9d6010: r1 = Function '<anonymous closure>':.
    //     0x9d6010: add             x1, PP, #0x57, lsl #12  ; [pp+0x572b8] AnonymousClosure: (0x9d5e18), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_handleQuantityDecrease (0x9d5ef0)
    //     0x9d6014: ldr             x1, [x1, #0x2b8]
    // 0x9d6018: r0 = AllocateClosure()
    //     0x9d6018: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9d601c: ldur            x1, [fp, #-8]
    // 0x9d6020: mov             x2, x0
    // 0x9d6024: r0 = setState()
    //     0x9d6024: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9d6028: ldur            x2, [fp, #-0x18]
    // 0x9d602c: cmp             w2, NULL
    // 0x9d6030: b.ne            #0x9d603c
    // 0x9d6034: r0 = Null
    //     0x9d6034: mov             x0, NULL
    // 0x9d6038: b               #0x9d6044
    // 0x9d603c: LoadField: r0 = r2->field_b
    //     0x9d603c: ldur            w0, [x2, #0xb]
    // 0x9d6040: DecompressPointer r0
    //     0x9d6040: add             x0, x0, HEAP, lsl #32
    // 0x9d6044: stur            x0, [fp, #-0x30]
    // 0x9d6048: cmp             w2, NULL
    // 0x9d604c: b.ne            #0x9d6058
    // 0x9d6050: r1 = Null
    //     0x9d6050: mov             x1, NULL
    // 0x9d6054: b               #0x9d607c
    // 0x9d6058: LoadField: r1 = r2->field_1f
    //     0x9d6058: ldur            w1, [x2, #0x1f]
    // 0x9d605c: DecompressPointer r1
    //     0x9d605c: add             x1, x1, HEAP, lsl #32
    // 0x9d6060: cmp             w1, NULL
    // 0x9d6064: b.ne            #0x9d6070
    // 0x9d6068: r1 = Null
    //     0x9d6068: mov             x1, NULL
    // 0x9d606c: b               #0x9d607c
    // 0x9d6070: LoadField: r3 = r1->field_b
    //     0x9d6070: ldur            w3, [x1, #0xb]
    // 0x9d6074: DecompressPointer r3
    //     0x9d6074: add             x3, x3, HEAP, lsl #32
    // 0x9d6078: mov             x1, x3
    // 0x9d607c: stur            x1, [fp, #-0x28]
    // 0x9d6080: cmp             w2, NULL
    // 0x9d6084: b.ne            #0x9d6090
    // 0x9d6088: r3 = Null
    //     0x9d6088: mov             x3, NULL
    // 0x9d608c: b               #0x9d6098
    // 0x9d6090: LoadField: r3 = r2->field_83
    //     0x9d6090: ldur            w3, [x2, #0x83]
    // 0x9d6094: DecompressPointer r3
    //     0x9d6094: add             x3, x3, HEAP, lsl #32
    // 0x9d6098: ldur            x2, [fp, #-8]
    // 0x9d609c: stur            x3, [fp, #-0x20]
    // 0x9d60a0: r0 = AddToBagRequest()
    //     0x9d60a0: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0x9d60a4: mov             x1, x0
    // 0x9d60a8: ldur            x0, [fp, #-0x30]
    // 0x9d60ac: StoreField: r1->field_7 = r0
    //     0x9d60ac: stur            w0, [x1, #7]
    // 0x9d60b0: ldur            x0, [fp, #-0x28]
    // 0x9d60b4: StoreField: r1->field_b = r0
    //     0x9d60b4: stur            w0, [x1, #0xb]
    // 0x9d60b8: r0 = -1
    //     0x9d60b8: movn            x0, #0
    // 0x9d60bc: StoreField: r1->field_f = r0
    //     0x9d60bc: stur            x0, [x1, #0xf]
    // 0x9d60c0: ldur            x0, [fp, #-0x20]
    // 0x9d60c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x9d60c4: stur            w0, [x1, #0x17]
    // 0x9d60c8: ldur            x0, [fp, #-8]
    // 0x9d60cc: LoadField: r2 = r0->field_b
    //     0x9d60cc: ldur            w2, [x0, #0xb]
    // 0x9d60d0: DecompressPointer r2
    //     0x9d60d0: add             x2, x2, HEAP, lsl #32
    // 0x9d60d4: cmp             w2, NULL
    // 0x9d60d8: b.eq            #0x9d6270
    // 0x9d60dc: LoadField: r0 = r2->field_23
    //     0x9d60dc: ldur            w0, [x2, #0x23]
    // 0x9d60e0: DecompressPointer r0
    //     0x9d60e0: add             x0, x0, HEAP, lsl #32
    // 0x9d60e4: stp             x1, x0, [SP]
    // 0x9d60e8: ClosureCall
    //     0x9d60e8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9d60ec: ldur            x2, [x0, #0x1f]
    //     0x9d60f0: blr             x2
    // 0x9d60f4: b               #0x9d624c
    // 0x9d60f8: mov             x0, x3
    // 0x9d60fc: mov             x2, x6
    // 0x9d6100: b               #0x9d610c
    // 0x9d6104: mov             x0, x3
    // 0x9d6108: mov             x2, x6
    // 0x9d610c: LoadField: r3 = r0->field_13
    //     0x9d610c: ldur            w3, [x0, #0x13]
    // 0x9d6110: DecompressPointer r3
    //     0x9d6110: add             x3, x3, HEAP, lsl #32
    // 0x9d6114: LoadField: r0 = r3->field_b
    //     0x9d6114: ldur            w0, [x3, #0xb]
    // 0x9d6118: r1 = LoadInt32Instr(r0)
    //     0x9d6118: sbfx            x1, x0, #1, #0x1f
    // 0x9d611c: mov             x0, x1
    // 0x9d6120: mov             x1, x4
    // 0x9d6124: cmp             x1, x0
    // 0x9d6128: b.hs            #0x9d6274
    // 0x9d612c: LoadField: r0 = r3->field_f
    //     0x9d612c: ldur            w0, [x3, #0xf]
    // 0x9d6130: DecompressPointer r0
    //     0x9d6130: add             x0, x0, HEAP, lsl #32
    // 0x9d6134: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9d6134: add             x16, x0, x4, lsl #2
    //     0x9d6138: ldur            w1, [x16, #0xf]
    // 0x9d613c: DecompressPointer r1
    //     0x9d613c: add             x1, x1, HEAP, lsl #32
    // 0x9d6140: cmp             w1, #2
    // 0x9d6144: b.ne            #0x9d624c
    // 0x9d6148: cmp             w2, NULL
    // 0x9d614c: b.eq            #0x9d615c
    // 0x9d6150: LoadField: r0 = r2->field_63
    //     0x9d6150: ldur            w0, [x2, #0x63]
    // 0x9d6154: DecompressPointer r0
    //     0x9d6154: add             x0, x0, HEAP, lsl #32
    // 0x9d6158: cbz             w0, #0x9d624c
    // 0x9d615c: cmp             w2, NULL
    // 0x9d6160: b.ne            #0x9d616c
    // 0x9d6164: r0 = Null
    //     0x9d6164: mov             x0, NULL
    // 0x9d6168: b               #0x9d6174
    // 0x9d616c: LoadField: r0 = r2->field_b
    //     0x9d616c: ldur            w0, [x2, #0xb]
    // 0x9d6170: DecompressPointer r0
    //     0x9d6170: add             x0, x0, HEAP, lsl #32
    // 0x9d6174: cmp             w0, NULL
    // 0x9d6178: b.ne            #0x9d6180
    // 0x9d617c: r0 = ""
    //     0x9d617c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9d6180: cmp             w2, NULL
    // 0x9d6184: b.ne            #0x9d6190
    // 0x9d6188: r1 = Null
    //     0x9d6188: mov             x1, NULL
    // 0x9d618c: b               #0x9d61b4
    // 0x9d6190: LoadField: r1 = r2->field_1f
    //     0x9d6190: ldur            w1, [x2, #0x1f]
    // 0x9d6194: DecompressPointer r1
    //     0x9d6194: add             x1, x1, HEAP, lsl #32
    // 0x9d6198: cmp             w1, NULL
    // 0x9d619c: b.ne            #0x9d61a8
    // 0x9d61a0: r1 = Null
    //     0x9d61a0: mov             x1, NULL
    // 0x9d61a4: b               #0x9d61b4
    // 0x9d61a8: LoadField: r3 = r1->field_b
    //     0x9d61a8: ldur            w3, [x1, #0xb]
    // 0x9d61ac: DecompressPointer r3
    //     0x9d61ac: add             x3, x3, HEAP, lsl #32
    // 0x9d61b0: mov             x1, x3
    // 0x9d61b4: cmp             w1, NULL
    // 0x9d61b8: b.ne            #0x9d61c0
    // 0x9d61bc: r1 = ""
    //     0x9d61bc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9d61c0: cmp             w2, NULL
    // 0x9d61c4: b.ne            #0x9d61d0
    // 0x9d61c8: r3 = Null
    //     0x9d61c8: mov             x3, NULL
    // 0x9d61cc: b               #0x9d61f4
    // 0x9d61d0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9d61d0: ldur            w3, [x2, #0x17]
    // 0x9d61d4: DecompressPointer r3
    //     0x9d61d4: add             x3, x3, HEAP, lsl #32
    // 0x9d61d8: cmp             w3, NULL
    // 0x9d61dc: b.ne            #0x9d61e8
    // 0x9d61e0: r3 = Null
    //     0x9d61e0: mov             x3, NULL
    // 0x9d61e4: b               #0x9d61f4
    // 0x9d61e8: LoadField: r4 = r3->field_7
    //     0x9d61e8: ldur            w4, [x3, #7]
    // 0x9d61ec: DecompressPointer r4
    //     0x9d61ec: add             x4, x4, HEAP, lsl #32
    // 0x9d61f0: mov             x3, x4
    // 0x9d61f4: cmp             w3, NULL
    // 0x9d61f8: b.ne            #0x9d6200
    // 0x9d61fc: r3 = ""
    //     0x9d61fc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9d6200: cmp             w2, NULL
    // 0x9d6204: b.ne            #0x9d6210
    // 0x9d6208: r2 = Null
    //     0x9d6208: mov             x2, NULL
    // 0x9d620c: b               #0x9d621c
    // 0x9d6210: LoadField: r4 = r2->field_83
    //     0x9d6210: ldur            w4, [x2, #0x83]
    // 0x9d6214: DecompressPointer r4
    //     0x9d6214: add             x4, x4, HEAP, lsl #32
    // 0x9d6218: mov             x2, x4
    // 0x9d621c: cmp             w2, NULL
    // 0x9d6220: b.ne            #0x9d6228
    // 0x9d6224: r2 = ""
    //     0x9d6224: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9d6228: LoadField: r4 = r5->field_27
    //     0x9d6228: ldur            w4, [x5, #0x27]
    // 0x9d622c: DecompressPointer r4
    //     0x9d622c: add             x4, x4, HEAP, lsl #32
    // 0x9d6230: stp             x0, x4, [SP, #0x18]
    // 0x9d6234: stp             x3, x1, [SP, #8]
    // 0x9d6238: str             x2, [SP]
    // 0x9d623c: mov             x0, x4
    // 0x9d6240: ClosureCall
    //     0x9d6240: ldr             x4, [PP, #0x13d0]  ; [pp+0x13d0] List(5) [0, 0x5, 0x5, 0x5, Null]
    //     0x9d6244: ldur            x2, [x0, #0x1f]
    //     0x9d6248: blr             x2
    // 0x9d624c: r0 = Null
    //     0x9d624c: mov             x0, NULL
    // 0x9d6250: LeaveFrame
    //     0x9d6250: mov             SP, fp
    //     0x9d6254: ldp             fp, lr, [SP], #0x10
    // 0x9d6258: ret
    //     0x9d6258: ret             
    // 0x9d625c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9d625c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9d6260: b               #0x9d5f18
    // 0x9d6264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d6264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d6268: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d6268: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9d626c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d626c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9d6270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9d6270: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9d6274: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9d6274: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb2aee8, size: 0x2d0
    // 0xb2aee8: EnterFrame
    //     0xb2aee8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2aeec: mov             fp, SP
    // 0xb2aef0: AllocStack(0x38)
    //     0xb2aef0: sub             SP, SP, #0x38
    // 0xb2aef4: SetupParameters(_BagItemViewWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb2aef4: mov             x0, x2
    //     0xb2aef8: stur            x2, [fp, #-0x10]
    //     0xb2aefc: mov             x2, x1
    //     0xb2af00: stur            x1, [fp, #-8]
    // 0xb2af04: CheckStackOverflow
    //     0xb2af04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2af08: cmp             SP, x16
    //     0xb2af0c: b.ls            #0xb2b1b0
    // 0xb2af10: mov             x1, x0
    // 0xb2af14: r0 = of()
    //     0xb2af14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2af18: LoadField: r1 = r0->field_5b
    //     0xb2af18: ldur            w1, [x0, #0x5b]
    // 0xb2af1c: DecompressPointer r1
    //     0xb2af1c: add             x1, x1, HEAP, lsl #32
    // 0xb2af20: r0 = LoadClassIdInstr(r1)
    //     0xb2af20: ldur            x0, [x1, #-1]
    //     0xb2af24: ubfx            x0, x0, #0xc, #0x14
    // 0xb2af28: d0 = 0.030000
    //     0xb2af28: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb2af2c: ldr             d0, [x17, #0x238]
    // 0xb2af30: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb2af30: sub             lr, x0, #0xffa
    //     0xb2af34: ldr             lr, [x21, lr, lsl #3]
    //     0xb2af38: blr             lr
    // 0xb2af3c: r1 = <Widget>
    //     0xb2af3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2af40: r2 = 18
    //     0xb2af40: movz            x2, #0x12
    // 0xb2af44: stur            x0, [fp, #-0x18]
    // 0xb2af48: r0 = AllocateArray()
    //     0xb2af48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2af4c: stur            x0, [fp, #-0x20]
    // 0xb2af50: r16 = Instance_SizedBox
    //     0xb2af50: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb2af54: ldr             x16, [x16, #0x328]
    // 0xb2af58: StoreField: r0->field_f = r16
    //     0xb2af58: stur            w16, [x0, #0xf]
    // 0xb2af5c: ldur            x1, [fp, #-8]
    // 0xb2af60: ldur            x2, [fp, #-0x10]
    // 0xb2af64: r0 = _buildHeader()
    //     0xb2af64: bl              #0xb2ed98  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildHeader
    // 0xb2af68: ldur            x1, [fp, #-0x20]
    // 0xb2af6c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb2af6c: add             x25, x1, #0x13
    //     0xb2af70: str             w0, [x25]
    //     0xb2af74: tbz             w0, #0, #0xb2af90
    //     0xb2af78: ldurb           w16, [x1, #-1]
    //     0xb2af7c: ldurb           w17, [x0, #-1]
    //     0xb2af80: and             x16, x17, x16, lsr #2
    //     0xb2af84: tst             x16, HEAP, lsr #32
    //     0xb2af88: b.eq            #0xb2af90
    //     0xb2af8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2af90: ldur            x0, [fp, #-0x20]
    // 0xb2af94: r16 = Instance_SizedBox
    //     0xb2af94: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb2af98: ldr             x16, [x16, #0x328]
    // 0xb2af9c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb2af9c: stur            w16, [x0, #0x17]
    // 0xb2afa0: ldur            x1, [fp, #-8]
    // 0xb2afa4: ldur            x2, [fp, #-0x10]
    // 0xb2afa8: r0 = _buildItemsCount()
    //     0xb2afa8: bl              #0xb2ec70  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildItemsCount
    // 0xb2afac: ldur            x1, [fp, #-0x20]
    // 0xb2afb0: ArrayStore: r1[3] = r0  ; List_4
    //     0xb2afb0: add             x25, x1, #0x1b
    //     0xb2afb4: str             w0, [x25]
    //     0xb2afb8: tbz             w0, #0, #0xb2afd4
    //     0xb2afbc: ldurb           w16, [x1, #-1]
    //     0xb2afc0: ldurb           w17, [x0, #-1]
    //     0xb2afc4: and             x16, x17, x16, lsr #2
    //     0xb2afc8: tst             x16, HEAP, lsr #32
    //     0xb2afcc: b.eq            #0xb2afd4
    //     0xb2afd0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2afd4: ldur            x0, [fp, #-0x20]
    // 0xb2afd8: r16 = Instance_SizedBox
    //     0xb2afd8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57270] Obj!SizedBox@d680a1
    //     0xb2afdc: ldr             x16, [x16, #0x270]
    // 0xb2afe0: StoreField: r0->field_1f = r16
    //     0xb2afe0: stur            w16, [x0, #0x1f]
    // 0xb2afe4: ldur            x1, [fp, #-8]
    // 0xb2afe8: ldur            x2, [fp, #-0x10]
    // 0xb2afec: r0 = _buildWarningMessages()
    //     0xb2afec: bl              #0xb2e54c  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildWarningMessages
    // 0xb2aff0: ldur            x1, [fp, #-0x20]
    // 0xb2aff4: ArrayStore: r1[5] = r0  ; List_4
    //     0xb2aff4: add             x25, x1, #0x23
    //     0xb2aff8: str             w0, [x25]
    //     0xb2affc: tbz             w0, #0, #0xb2b018
    //     0xb2b000: ldurb           w16, [x1, #-1]
    //     0xb2b004: ldurb           w17, [x0, #-1]
    //     0xb2b008: and             x16, x17, x16, lsr #2
    //     0xb2b00c: tst             x16, HEAP, lsr #32
    //     0xb2b010: b.eq            #0xb2b018
    //     0xb2b014: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2b018: ldur            x1, [fp, #-8]
    // 0xb2b01c: ldur            x2, [fp, #-0x10]
    // 0xb2b020: r0 = _buildExchangeSection()
    //     0xb2b020: bl              #0xb2d624  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildExchangeSection
    // 0xb2b024: ldur            x1, [fp, #-0x20]
    // 0xb2b028: ArrayStore: r1[6] = r0  ; List_4
    //     0xb2b028: add             x25, x1, #0x27
    //     0xb2b02c: str             w0, [x25]
    //     0xb2b030: tbz             w0, #0, #0xb2b04c
    //     0xb2b034: ldurb           w16, [x1, #-1]
    //     0xb2b038: ldurb           w17, [x0, #-1]
    //     0xb2b03c: and             x16, x17, x16, lsr #2
    //     0xb2b040: tst             x16, HEAP, lsr #32
    //     0xb2b044: b.eq            #0xb2b04c
    //     0xb2b048: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2b04c: ldur            x1, [fp, #-8]
    // 0xb2b050: ldur            x2, [fp, #-0x10]
    // 0xb2b054: r0 = _buildFreeGiftSection()
    //     0xb2b054: bl              #0xb2c980  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildFreeGiftSection
    // 0xb2b058: ldur            x1, [fp, #-0x20]
    // 0xb2b05c: ArrayStore: r1[7] = r0  ; List_4
    //     0xb2b05c: add             x25, x1, #0x2b
    //     0xb2b060: str             w0, [x25]
    //     0xb2b064: tbz             w0, #0, #0xb2b080
    //     0xb2b068: ldurb           w16, [x1, #-1]
    //     0xb2b06c: ldurb           w17, [x0, #-1]
    //     0xb2b070: and             x16, x17, x16, lsr #2
    //     0xb2b074: tst             x16, HEAP, lsr #32
    //     0xb2b078: b.eq            #0xb2b080
    //     0xb2b07c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2b080: ldur            x1, [fp, #-8]
    // 0xb2b084: r0 = _buildCatalogueList()
    //     0xb2b084: bl              #0xb2b1b8  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueList
    // 0xb2b088: ldur            x1, [fp, #-0x20]
    // 0xb2b08c: ArrayStore: r1[8] = r0  ; List_4
    //     0xb2b08c: add             x25, x1, #0x2f
    //     0xb2b090: str             w0, [x25]
    //     0xb2b094: tbz             w0, #0, #0xb2b0b0
    //     0xb2b098: ldurb           w16, [x1, #-1]
    //     0xb2b09c: ldurb           w17, [x0, #-1]
    //     0xb2b0a0: and             x16, x17, x16, lsr #2
    //     0xb2b0a4: tst             x16, HEAP, lsr #32
    //     0xb2b0a8: b.eq            #0xb2b0b0
    //     0xb2b0ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2b0b0: r1 = <Widget>
    //     0xb2b0b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2b0b4: r0 = AllocateGrowableArray()
    //     0xb2b0b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2b0b8: mov             x1, x0
    // 0xb2b0bc: ldur            x0, [fp, #-0x20]
    // 0xb2b0c0: stur            x1, [fp, #-8]
    // 0xb2b0c4: StoreField: r1->field_f = r0
    //     0xb2b0c4: stur            w0, [x1, #0xf]
    // 0xb2b0c8: r0 = 18
    //     0xb2b0c8: movz            x0, #0x12
    // 0xb2b0cc: StoreField: r1->field_b = r0
    //     0xb2b0cc: stur            w0, [x1, #0xb]
    // 0xb2b0d0: r0 = Column()
    //     0xb2b0d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2b0d4: mov             x1, x0
    // 0xb2b0d8: r0 = Instance_Axis
    //     0xb2b0d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2b0dc: stur            x1, [fp, #-0x10]
    // 0xb2b0e0: StoreField: r1->field_f = r0
    //     0xb2b0e0: stur            w0, [x1, #0xf]
    // 0xb2b0e4: r2 = Instance_MainAxisAlignment
    //     0xb2b0e4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2b0e8: ldr             x2, [x2, #0xa08]
    // 0xb2b0ec: StoreField: r1->field_13 = r2
    //     0xb2b0ec: stur            w2, [x1, #0x13]
    // 0xb2b0f0: r2 = Instance_MainAxisSize
    //     0xb2b0f0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb2b0f4: ldr             x2, [x2, #0xdd0]
    // 0xb2b0f8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb2b0f8: stur            w2, [x1, #0x17]
    // 0xb2b0fc: r2 = Instance_CrossAxisAlignment
    //     0xb2b0fc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2b100: ldr             x2, [x2, #0x890]
    // 0xb2b104: StoreField: r1->field_1b = r2
    //     0xb2b104: stur            w2, [x1, #0x1b]
    // 0xb2b108: r2 = Instance_VerticalDirection
    //     0xb2b108: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2b10c: ldr             x2, [x2, #0xa20]
    // 0xb2b110: StoreField: r1->field_23 = r2
    //     0xb2b110: stur            w2, [x1, #0x23]
    // 0xb2b114: r2 = Instance_Clip
    //     0xb2b114: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2b118: ldr             x2, [x2, #0x38]
    // 0xb2b11c: StoreField: r1->field_2b = r2
    //     0xb2b11c: stur            w2, [x1, #0x2b]
    // 0xb2b120: StoreField: r1->field_2f = rZR
    //     0xb2b120: stur            xzr, [x1, #0x2f]
    // 0xb2b124: ldur            x2, [fp, #-8]
    // 0xb2b128: StoreField: r1->field_b = r2
    //     0xb2b128: stur            w2, [x1, #0xb]
    // 0xb2b12c: r0 = SingleChildScrollView()
    //     0xb2b12c: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb2b130: mov             x1, x0
    // 0xb2b134: r0 = Instance_Axis
    //     0xb2b134: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2b138: stur            x1, [fp, #-8]
    // 0xb2b13c: StoreField: r1->field_b = r0
    //     0xb2b13c: stur            w0, [x1, #0xb]
    // 0xb2b140: r0 = false
    //     0xb2b140: add             x0, NULL, #0x30  ; false
    // 0xb2b144: StoreField: r1->field_f = r0
    //     0xb2b144: stur            w0, [x1, #0xf]
    // 0xb2b148: ldur            x0, [fp, #-0x10]
    // 0xb2b14c: StoreField: r1->field_23 = r0
    //     0xb2b14c: stur            w0, [x1, #0x23]
    // 0xb2b150: r0 = Instance_DragStartBehavior
    //     0xb2b150: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb2b154: StoreField: r1->field_27 = r0
    //     0xb2b154: stur            w0, [x1, #0x27]
    // 0xb2b158: r0 = Instance_Clip
    //     0xb2b158: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb2b15c: ldr             x0, [x0, #0x7e0]
    // 0xb2b160: StoreField: r1->field_2b = r0
    //     0xb2b160: stur            w0, [x1, #0x2b]
    // 0xb2b164: r0 = Instance_HitTestBehavior
    //     0xb2b164: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb2b168: ldr             x0, [x0, #0x288]
    // 0xb2b16c: StoreField: r1->field_2f = r0
    //     0xb2b16c: stur            w0, [x1, #0x2f]
    // 0xb2b170: r0 = Container()
    //     0xb2b170: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2b174: stur            x0, [fp, #-0x10]
    // 0xb2b178: ldur            x16, [fp, #-0x18]
    // 0xb2b17c: r30 = inf
    //     0xb2b17c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb2b180: ldr             lr, [lr, #0x9f8]
    // 0xb2b184: stp             lr, x16, [SP, #8]
    // 0xb2b188: ldur            x16, [fp, #-8]
    // 0xb2b18c: str             x16, [SP]
    // 0xb2b190: mov             x1, x0
    // 0xb2b194: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, height, 0x2, null]
    //     0xb2b194: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f308] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "height", 0x2, Null]
    //     0xb2b198: ldr             x4, [x4, #0x308]
    // 0xb2b19c: r0 = Container()
    //     0xb2b19c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2b1a0: ldur            x0, [fp, #-0x10]
    // 0xb2b1a4: LeaveFrame
    //     0xb2b1a4: mov             SP, fp
    //     0xb2b1a8: ldp             fp, lr, [SP], #0x10
    // 0xb2b1ac: ret
    //     0xb2b1ac: ret             
    // 0xb2b1b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2b1b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2b1b4: b               #0xb2af10
  }
  _ _buildCatalogueList(/* No info */) {
    // ** addr: 0xb2b1b8, size: 0x140
    // 0xb2b1b8: EnterFrame
    //     0xb2b1b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2b1bc: mov             fp, SP
    // 0xb2b1c0: AllocStack(0x28)
    //     0xb2b1c0: sub             SP, SP, #0x28
    // 0xb2b1c4: SetupParameters(_BagItemViewWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xb2b1c4: stur            x1, [fp, #-8]
    // 0xb2b1c8: CheckStackOverflow
    //     0xb2b1c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2b1cc: cmp             SP, x16
    //     0xb2b1d0: b.ls            #0xb2b2ec
    // 0xb2b1d4: r1 = 1
    //     0xb2b1d4: movz            x1, #0x1
    // 0xb2b1d8: r0 = AllocateContext()
    //     0xb2b1d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2b1dc: mov             x3, x0
    // 0xb2b1e0: ldur            x0, [fp, #-8]
    // 0xb2b1e4: stur            x3, [fp, #-0x10]
    // 0xb2b1e8: StoreField: r3->field_f = r0
    //     0xb2b1e8: stur            w0, [x3, #0xf]
    // 0xb2b1ec: LoadField: r1 = r0->field_b
    //     0xb2b1ec: ldur            w1, [x0, #0xb]
    // 0xb2b1f0: DecompressPointer r1
    //     0xb2b1f0: add             x1, x1, HEAP, lsl #32
    // 0xb2b1f4: cmp             w1, NULL
    // 0xb2b1f8: b.eq            #0xb2b2f4
    // 0xb2b1fc: LoadField: r0 = r1->field_b
    //     0xb2b1fc: ldur            w0, [x1, #0xb]
    // 0xb2b200: DecompressPointer r0
    //     0xb2b200: add             x0, x0, HEAP, lsl #32
    // 0xb2b204: cmp             w0, NULL
    // 0xb2b208: b.ne            #0xb2b214
    // 0xb2b20c: r0 = Null
    //     0xb2b20c: mov             x0, NULL
    // 0xb2b210: b               #0xb2b220
    // 0xb2b214: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2b214: ldur            w1, [x0, #0x17]
    // 0xb2b218: DecompressPointer r1
    //     0xb2b218: add             x1, x1, HEAP, lsl #32
    // 0xb2b21c: mov             x0, x1
    // 0xb2b220: cmp             w0, NULL
    // 0xb2b224: b.ne            #0xb2b238
    // 0xb2b228: r1 = <Catalogue>
    //     0xb2b228: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0xb2b22c: ldr             x1, [x1, #0x418]
    // 0xb2b230: r2 = 0
    //     0xb2b230: movz            x2, #0
    // 0xb2b234: r0 = _GrowableList()
    //     0xb2b234: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb2b238: LoadField: r3 = r0->field_b
    //     0xb2b238: ldur            w3, [x0, #0xb]
    // 0xb2b23c: stur            x3, [fp, #-8]
    // 0xb2b240: cbnz            w3, #0xb2b254
    // 0xb2b244: r0 = Instance_SizedBox
    //     0xb2b244: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb2b248: LeaveFrame
    //     0xb2b248: mov             SP, fp
    //     0xb2b24c: ldp             fp, lr, [SP], #0x10
    // 0xb2b250: ret
    //     0xb2b250: ret             
    // 0xb2b254: ldur            x2, [fp, #-0x10]
    // 0xb2b258: r1 = Function '<anonymous closure>':.
    //     0xb2b258: add             x1, PP, #0x57, lsl #12  ; [pp+0x57278] AnonymousClosure: (0xb2b2f8), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueList (0xb2b1b8)
    //     0xb2b25c: ldr             x1, [x1, #0x278]
    // 0xb2b260: r0 = AllocateClosure()
    //     0xb2b260: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2b264: stur            x0, [fp, #-0x10]
    // 0xb2b268: r0 = ListView()
    //     0xb2b268: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb2b26c: stur            x0, [fp, #-0x18]
    // 0xb2b270: r16 = true
    //     0xb2b270: add             x16, NULL, #0x20  ; true
    // 0xb2b274: r30 = Instance_BouncingScrollPhysics
    //     0xb2b274: add             lr, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb2b278: ldr             lr, [lr, #0x890]
    // 0xb2b27c: stp             lr, x16, [SP]
    // 0xb2b280: mov             x1, x0
    // 0xb2b284: ldur            x2, [fp, #-0x10]
    // 0xb2b288: ldur            x3, [fp, #-8]
    // 0xb2b28c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb2b28c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb2b290: ldr             x4, [x4, #8]
    // 0xb2b294: r0 = ListView.builder()
    //     0xb2b294: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb2b298: r0 = Padding()
    //     0xb2b298: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2b29c: mov             x2, x0
    // 0xb2b2a0: r0 = Instance_EdgeInsets
    //     0xb2b2a0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xb2b2a4: ldr             x0, [x0, #0xf98]
    // 0xb2b2a8: stur            x2, [fp, #-8]
    // 0xb2b2ac: StoreField: r2->field_f = r0
    //     0xb2b2ac: stur            w0, [x2, #0xf]
    // 0xb2b2b0: ldur            x0, [fp, #-0x18]
    // 0xb2b2b4: StoreField: r2->field_b = r0
    //     0xb2b2b4: stur            w0, [x2, #0xb]
    // 0xb2b2b8: r1 = <FlexParentData>
    //     0xb2b2b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2b2bc: ldr             x1, [x1, #0xe00]
    // 0xb2b2c0: r0 = Flexible()
    //     0xb2b2c0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb2b2c4: r1 = 1
    //     0xb2b2c4: movz            x1, #0x1
    // 0xb2b2c8: StoreField: r0->field_13 = r1
    //     0xb2b2c8: stur            x1, [x0, #0x13]
    // 0xb2b2cc: r1 = Instance_FlexFit
    //     0xb2b2cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xb2b2d0: ldr             x1, [x1, #0xe20]
    // 0xb2b2d4: StoreField: r0->field_1b = r1
    //     0xb2b2d4: stur            w1, [x0, #0x1b]
    // 0xb2b2d8: ldur            x1, [fp, #-8]
    // 0xb2b2dc: StoreField: r0->field_b = r1
    //     0xb2b2dc: stur            w1, [x0, #0xb]
    // 0xb2b2e0: LeaveFrame
    //     0xb2b2e0: mov             SP, fp
    //     0xb2b2e4: ldp             fp, lr, [SP], #0x10
    // 0xb2b2e8: ret
    //     0xb2b2e8: ret             
    // 0xb2b2ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2b2ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2b2f0: b               #0xb2b1d4
    // 0xb2b2f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2b2f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb2b2f8, size: 0x58
    // 0xb2b2f8: EnterFrame
    //     0xb2b2f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2b2fc: mov             fp, SP
    // 0xb2b300: ldr             x0, [fp, #0x20]
    // 0xb2b304: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2b304: ldur            w1, [x0, #0x17]
    // 0xb2b308: DecompressPointer r1
    //     0xb2b308: add             x1, x1, HEAP, lsl #32
    // 0xb2b30c: CheckStackOverflow
    //     0xb2b30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2b310: cmp             SP, x16
    //     0xb2b314: b.ls            #0xb2b348
    // 0xb2b318: LoadField: r0 = r1->field_f
    //     0xb2b318: ldur            w0, [x1, #0xf]
    // 0xb2b31c: DecompressPointer r0
    //     0xb2b31c: add             x0, x0, HEAP, lsl #32
    // 0xb2b320: ldr             x1, [fp, #0x10]
    // 0xb2b324: r3 = LoadInt32Instr(r1)
    //     0xb2b324: sbfx            x3, x1, #1, #0x1f
    //     0xb2b328: tbz             w1, #0, #0xb2b330
    //     0xb2b32c: ldur            x3, [x1, #7]
    // 0xb2b330: mov             x1, x0
    // 0xb2b334: ldr             x2, [fp, #0x18]
    // 0xb2b338: r0 = _buildCatalogueItem()
    //     0xb2b338: bl              #0xb2b350  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueItem
    // 0xb2b33c: LeaveFrame
    //     0xb2b33c: mov             SP, fp
    //     0xb2b340: ldp             fp, lr, [SP], #0x10
    // 0xb2b344: ret
    //     0xb2b344: ret             
    // 0xb2b348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2b348: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2b34c: b               #0xb2b318
  }
  _ _buildCatalogueItem(/* No info */) {
    // ** addr: 0xb2b350, size: 0x410
    // 0xb2b350: EnterFrame
    //     0xb2b350: stp             fp, lr, [SP, #-0x10]!
    //     0xb2b354: mov             fp, SP
    // 0xb2b358: AllocStack(0x40)
    //     0xb2b358: sub             SP, SP, #0x40
    // 0xb2b35c: SetupParameters(_BagItemViewWidgetState this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xb2b35c: mov             x5, x1
    //     0xb2b360: mov             x4, x2
    //     0xb2b364: stur            x1, [fp, #-0x10]
    //     0xb2b368: stur            x2, [fp, #-0x18]
    //     0xb2b36c: stur            x3, [fp, #-0x20]
    // 0xb2b370: CheckStackOverflow
    //     0xb2b370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2b374: cmp             SP, x16
    //     0xb2b378: b.ls            #0xb2b74c
    // 0xb2b37c: LoadField: r0 = r5->field_b
    //     0xb2b37c: ldur            w0, [x5, #0xb]
    // 0xb2b380: DecompressPointer r0
    //     0xb2b380: add             x0, x0, HEAP, lsl #32
    // 0xb2b384: cmp             w0, NULL
    // 0xb2b388: b.eq            #0xb2b754
    // 0xb2b38c: LoadField: r1 = r0->field_b
    //     0xb2b38c: ldur            w1, [x0, #0xb]
    // 0xb2b390: DecompressPointer r1
    //     0xb2b390: add             x1, x1, HEAP, lsl #32
    // 0xb2b394: cmp             w1, NULL
    // 0xb2b398: b.eq            #0xb2b758
    // 0xb2b39c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2b39c: ldur            w2, [x1, #0x17]
    // 0xb2b3a0: DecompressPointer r2
    //     0xb2b3a0: add             x2, x2, HEAP, lsl #32
    // 0xb2b3a4: LoadField: r0 = r2->field_b
    //     0xb2b3a4: ldur            w0, [x2, #0xb]
    // 0xb2b3a8: r1 = LoadInt32Instr(r0)
    //     0xb2b3a8: sbfx            x1, x0, #1, #0x1f
    // 0xb2b3ac: mov             x0, x1
    // 0xb2b3b0: mov             x1, x3
    // 0xb2b3b4: cmp             x1, x0
    // 0xb2b3b8: b.hs            #0xb2b75c
    // 0xb2b3bc: LoadField: r0 = r2->field_f
    //     0xb2b3bc: ldur            w0, [x2, #0xf]
    // 0xb2b3c0: DecompressPointer r0
    //     0xb2b3c0: add             x0, x0, HEAP, lsl #32
    // 0xb2b3c4: ArrayLoad: r6 = r0[r3]  ; Unknown_4
    //     0xb2b3c4: add             x16, x0, x3, lsl #2
    //     0xb2b3c8: ldur            w6, [x16, #0xf]
    // 0xb2b3cc: DecompressPointer r6
    //     0xb2b3cc: add             x6, x6, HEAP, lsl #32
    // 0xb2b3d0: stur            x6, [fp, #-8]
    // 0xb2b3d4: LoadField: r0 = r6->field_1b
    //     0xb2b3d4: ldur            w0, [x6, #0x1b]
    // 0xb2b3d8: DecompressPointer r0
    //     0xb2b3d8: add             x0, x0, HEAP, lsl #32
    // 0xb2b3dc: cmp             w0, NULL
    // 0xb2b3e0: b.ne            #0xb2b3fc
    // 0xb2b3e4: r1 = <BagImage>
    //     0xb2b3e4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25528] TypeArguments: <BagImage>
    //     0xb2b3e8: ldr             x1, [x1, #0x528]
    // 0xb2b3ec: r2 = 0
    //     0xb2b3ec: movz            x2, #0
    // 0xb2b3f0: r0 = _GrowableList()
    //     0xb2b3f0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb2b3f4: mov             x1, x0
    // 0xb2b3f8: b               #0xb2b400
    // 0xb2b3fc: mov             x1, x0
    // 0xb2b400: stur            x1, [fp, #-0x28]
    // 0xb2b404: r1 = 1
    //     0xb2b404: movz            x1, #0x1
    // 0xb2b408: r0 = AllocateContext()
    //     0xb2b408: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2b40c: mov             x3, x0
    // 0xb2b410: ldur            x0, [fp, #-0x28]
    // 0xb2b414: stur            x3, [fp, #-0x30]
    // 0xb2b418: StoreField: r3->field_f = r0
    //     0xb2b418: stur            w0, [x3, #0xf]
    // 0xb2b41c: LoadField: r1 = r0->field_b
    //     0xb2b41c: ldur            w1, [x0, #0xb]
    // 0xb2b420: cbz             w1, #0xb2b464
    // 0xb2b424: r1 = Function '<anonymous closure>':.
    //     0xb2b424: add             x1, PP, #0x57, lsl #12  ; [pp+0x57280] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0xb2b428: ldr             x1, [x1, #0x280]
    // 0xb2b42c: r2 = Null
    //     0xb2b42c: mov             x2, NULL
    // 0xb2b430: r0 = AllocateClosure()
    //     0xb2b430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2b434: ldur            x2, [fp, #-0x30]
    // 0xb2b438: r1 = Function '<anonymous closure>':.
    //     0xb2b438: add             x1, PP, #0x57, lsl #12  ; [pp+0x57288] AnonymousClosure: (0x9d6bc4), in [package:customer_app/app/presentation/views/line/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueItem (0x9d6c08)
    //     0xb2b43c: ldr             x1, [x1, #0x288]
    // 0xb2b440: stur            x0, [fp, #-0x30]
    // 0xb2b444: r0 = AllocateClosure()
    //     0xb2b444: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2b448: str             x0, [SP]
    // 0xb2b44c: ldur            x1, [fp, #-0x28]
    // 0xb2b450: ldur            x2, [fp, #-0x30]
    // 0xb2b454: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb2b454: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb2b458: ldr             x4, [x4, #0xb48]
    // 0xb2b45c: r0 = firstWhere()
    //     0xb2b45c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb2b460: b               #0xb2b468
    // 0xb2b464: r0 = Null
    //     0xb2b464: mov             x0, NULL
    // 0xb2b468: cmp             w0, NULL
    // 0xb2b46c: b.ne            #0xb2b478
    // 0xb2b470: r0 = Null
    //     0xb2b470: mov             x0, NULL
    // 0xb2b474: b               #0xb2b484
    // 0xb2b478: LoadField: r1 = r0->field_b
    //     0xb2b478: ldur            w1, [x0, #0xb]
    // 0xb2b47c: DecompressPointer r1
    //     0xb2b47c: add             x1, x1, HEAP, lsl #32
    // 0xb2b480: mov             x0, x1
    // 0xb2b484: cmp             w0, NULL
    // 0xb2b488: b.ne            #0xb2b494
    // 0xb2b48c: r3 = ""
    //     0xb2b48c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2b490: b               #0xb2b498
    // 0xb2b494: mov             x3, x0
    // 0xb2b498: ldur            x0, [fp, #-8]
    // 0xb2b49c: ldur            x1, [fp, #-0x10]
    // 0xb2b4a0: ldur            x2, [fp, #-0x20]
    // 0xb2b4a4: r0 = _buildCatalogueImage()
    //     0xb2b4a4: bl              #0xb2c3a4  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueImage
    // 0xb2b4a8: ldur            x1, [fp, #-0x10]
    // 0xb2b4ac: ldur            x2, [fp, #-0x18]
    // 0xb2b4b0: ldur            x3, [fp, #-8]
    // 0xb2b4b4: stur            x0, [fp, #-0x28]
    // 0xb2b4b8: r0 = _buildCatalogueDetails()
    //     0xb2b4b8: bl              #0xb2bcf4  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueDetails
    // 0xb2b4bc: stur            x0, [fp, #-0x30]
    // 0xb2b4c0: r0 = Padding()
    //     0xb2b4c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2b4c4: mov             x2, x0
    // 0xb2b4c8: r0 = Instance_EdgeInsets
    //     0xb2b4c8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb2b4cc: ldr             x0, [x0, #0xa78]
    // 0xb2b4d0: stur            x2, [fp, #-0x38]
    // 0xb2b4d4: StoreField: r2->field_f = r0
    //     0xb2b4d4: stur            w0, [x2, #0xf]
    // 0xb2b4d8: ldur            x0, [fp, #-0x30]
    // 0xb2b4dc: StoreField: r2->field_b = r0
    //     0xb2b4dc: stur            w0, [x2, #0xb]
    // 0xb2b4e0: r1 = <FlexParentData>
    //     0xb2b4e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2b4e4: ldr             x1, [x1, #0xe00]
    // 0xb2b4e8: r0 = Expanded()
    //     0xb2b4e8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2b4ec: mov             x4, x0
    // 0xb2b4f0: r0 = 1
    //     0xb2b4f0: movz            x0, #0x1
    // 0xb2b4f4: stur            x4, [fp, #-0x30]
    // 0xb2b4f8: StoreField: r4->field_13 = r0
    //     0xb2b4f8: stur            x0, [x4, #0x13]
    // 0xb2b4fc: r0 = Instance_FlexFit
    //     0xb2b4fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2b500: ldr             x0, [x0, #0xe08]
    // 0xb2b504: StoreField: r4->field_1b = r0
    //     0xb2b504: stur            w0, [x4, #0x1b]
    // 0xb2b508: ldur            x0, [fp, #-0x38]
    // 0xb2b50c: StoreField: r4->field_b = r0
    //     0xb2b50c: stur            w0, [x4, #0xb]
    // 0xb2b510: ldur            x1, [fp, #-0x10]
    // 0xb2b514: ldur            x2, [fp, #-0x18]
    // 0xb2b518: ldur            x3, [fp, #-0x20]
    // 0xb2b51c: ldur            x5, [fp, #-8]
    // 0xb2b520: r0 = _buildQuantityControls()
    //     0xb2b520: bl              #0xb2b760  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildQuantityControls
    // 0xb2b524: r1 = Null
    //     0xb2b524: mov             x1, NULL
    // 0xb2b528: r2 = 6
    //     0xb2b528: movz            x2, #0x6
    // 0xb2b52c: stur            x0, [fp, #-0x10]
    // 0xb2b530: r0 = AllocateArray()
    //     0xb2b530: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2b534: mov             x2, x0
    // 0xb2b538: ldur            x0, [fp, #-0x28]
    // 0xb2b53c: stur            x2, [fp, #-0x18]
    // 0xb2b540: StoreField: r2->field_f = r0
    //     0xb2b540: stur            w0, [x2, #0xf]
    // 0xb2b544: ldur            x0, [fp, #-0x30]
    // 0xb2b548: StoreField: r2->field_13 = r0
    //     0xb2b548: stur            w0, [x2, #0x13]
    // 0xb2b54c: ldur            x0, [fp, #-0x10]
    // 0xb2b550: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2b550: stur            w0, [x2, #0x17]
    // 0xb2b554: r1 = <Widget>
    //     0xb2b554: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2b558: r0 = AllocateGrowableArray()
    //     0xb2b558: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2b55c: mov             x1, x0
    // 0xb2b560: ldur            x0, [fp, #-0x18]
    // 0xb2b564: stur            x1, [fp, #-0x10]
    // 0xb2b568: StoreField: r1->field_f = r0
    //     0xb2b568: stur            w0, [x1, #0xf]
    // 0xb2b56c: r0 = 6
    //     0xb2b56c: movz            x0, #0x6
    // 0xb2b570: StoreField: r1->field_b = r0
    //     0xb2b570: stur            w0, [x1, #0xb]
    // 0xb2b574: r0 = Row()
    //     0xb2b574: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2b578: mov             x3, x0
    // 0xb2b57c: r0 = Instance_Axis
    //     0xb2b57c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2b580: stur            x3, [fp, #-0x18]
    // 0xb2b584: StoreField: r3->field_f = r0
    //     0xb2b584: stur            w0, [x3, #0xf]
    // 0xb2b588: r0 = Instance_MainAxisAlignment
    //     0xb2b588: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2b58c: ldr             x0, [x0, #0xa08]
    // 0xb2b590: StoreField: r3->field_13 = r0
    //     0xb2b590: stur            w0, [x3, #0x13]
    // 0xb2b594: r4 = Instance_MainAxisSize
    //     0xb2b594: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2b598: ldr             x4, [x4, #0xa10]
    // 0xb2b59c: ArrayStore: r3[0] = r4  ; List_4
    //     0xb2b59c: stur            w4, [x3, #0x17]
    // 0xb2b5a0: r1 = Instance_CrossAxisAlignment
    //     0xb2b5a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2b5a4: ldr             x1, [x1, #0xa18]
    // 0xb2b5a8: StoreField: r3->field_1b = r1
    //     0xb2b5a8: stur            w1, [x3, #0x1b]
    // 0xb2b5ac: r5 = Instance_VerticalDirection
    //     0xb2b5ac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2b5b0: ldr             x5, [x5, #0xa20]
    // 0xb2b5b4: StoreField: r3->field_23 = r5
    //     0xb2b5b4: stur            w5, [x3, #0x23]
    // 0xb2b5b8: r6 = Instance_Clip
    //     0xb2b5b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2b5bc: ldr             x6, [x6, #0x38]
    // 0xb2b5c0: StoreField: r3->field_2b = r6
    //     0xb2b5c0: stur            w6, [x3, #0x2b]
    // 0xb2b5c4: StoreField: r3->field_2f = rZR
    //     0xb2b5c4: stur            xzr, [x3, #0x2f]
    // 0xb2b5c8: ldur            x1, [fp, #-0x10]
    // 0xb2b5cc: StoreField: r3->field_b = r1
    //     0xb2b5cc: stur            w1, [x3, #0xb]
    // 0xb2b5d0: r1 = Null
    //     0xb2b5d0: mov             x1, NULL
    // 0xb2b5d4: r2 = 2
    //     0xb2b5d4: movz            x2, #0x2
    // 0xb2b5d8: r0 = AllocateArray()
    //     0xb2b5d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2b5dc: mov             x2, x0
    // 0xb2b5e0: ldur            x0, [fp, #-0x18]
    // 0xb2b5e4: stur            x2, [fp, #-0x10]
    // 0xb2b5e8: StoreField: r2->field_f = r0
    //     0xb2b5e8: stur            w0, [x2, #0xf]
    // 0xb2b5ec: r1 = <Widget>
    //     0xb2b5ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2b5f0: r0 = AllocateGrowableArray()
    //     0xb2b5f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2b5f4: mov             x1, x0
    // 0xb2b5f8: ldur            x0, [fp, #-0x10]
    // 0xb2b5fc: stur            x1, [fp, #-0x28]
    // 0xb2b600: StoreField: r1->field_f = r0
    //     0xb2b600: stur            w0, [x1, #0xf]
    // 0xb2b604: r0 = 2
    //     0xb2b604: movz            x0, #0x2
    // 0xb2b608: StoreField: r1->field_b = r0
    //     0xb2b608: stur            w0, [x1, #0xb]
    // 0xb2b60c: ldur            x0, [fp, #-8]
    // 0xb2b610: LoadField: r2 = r0->field_6b
    //     0xb2b610: ldur            w2, [x0, #0x6b]
    // 0xb2b614: DecompressPointer r2
    //     0xb2b614: add             x2, x2, HEAP, lsl #32
    // 0xb2b618: stur            x2, [fp, #-0x18]
    // 0xb2b61c: cmp             w2, NULL
    // 0xb2b620: b.ne            #0xb2b62c
    // 0xb2b624: r3 = Null
    //     0xb2b624: mov             x3, NULL
    // 0xb2b628: b               #0xb2b644
    // 0xb2b62c: LoadField: r3 = r2->field_b
    //     0xb2b62c: ldur            w3, [x2, #0xb]
    // 0xb2b630: cbnz            w3, #0xb2b63c
    // 0xb2b634: r4 = false
    //     0xb2b634: add             x4, NULL, #0x30  ; false
    // 0xb2b638: b               #0xb2b640
    // 0xb2b63c: r4 = true
    //     0xb2b63c: add             x4, NULL, #0x20  ; true
    // 0xb2b640: mov             x3, x4
    // 0xb2b644: cmp             w3, NULL
    // 0xb2b648: b.ne            #0xb2b654
    // 0xb2b64c: mov             x2, x1
    // 0xb2b650: b               #0xb2b6cc
    // 0xb2b654: tbnz            w3, #4, #0xb2b6c8
    // 0xb2b658: LoadField: r3 = r0->field_73
    //     0xb2b658: ldur            w3, [x0, #0x73]
    // 0xb2b65c: DecompressPointer r3
    //     0xb2b65c: add             x3, x3, HEAP, lsl #32
    // 0xb2b660: stur            x3, [fp, #-0x10]
    // 0xb2b664: r0 = CustomisedStrip()
    //     0xb2b664: bl              #0xa1c2f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xb2b668: mov             x2, x0
    // 0xb2b66c: ldur            x0, [fp, #-0x18]
    // 0xb2b670: stur            x2, [fp, #-8]
    // 0xb2b674: StoreField: r2->field_b = r0
    //     0xb2b674: stur            w0, [x2, #0xb]
    // 0xb2b678: ldur            x0, [fp, #-0x10]
    // 0xb2b67c: StoreField: r2->field_13 = r0
    //     0xb2b67c: stur            w0, [x2, #0x13]
    // 0xb2b680: ldur            x1, [fp, #-0x28]
    // 0xb2b684: r0 = _growToNextCapacity()
    //     0xb2b684: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2b688: ldur            x2, [fp, #-0x28]
    // 0xb2b68c: r0 = 4
    //     0xb2b68c: movz            x0, #0x4
    // 0xb2b690: StoreField: r2->field_b = r0
    //     0xb2b690: stur            w0, [x2, #0xb]
    // 0xb2b694: LoadField: r1 = r2->field_f
    //     0xb2b694: ldur            w1, [x2, #0xf]
    // 0xb2b698: DecompressPointer r1
    //     0xb2b698: add             x1, x1, HEAP, lsl #32
    // 0xb2b69c: ldur            x0, [fp, #-8]
    // 0xb2b6a0: ArrayStore: r1[1] = r0  ; List_4
    //     0xb2b6a0: add             x25, x1, #0x13
    //     0xb2b6a4: str             w0, [x25]
    //     0xb2b6a8: tbz             w0, #0, #0xb2b6c4
    //     0xb2b6ac: ldurb           w16, [x1, #-1]
    //     0xb2b6b0: ldurb           w17, [x0, #-1]
    //     0xb2b6b4: and             x16, x17, x16, lsr #2
    //     0xb2b6b8: tst             x16, HEAP, lsr #32
    //     0xb2b6bc: b.eq            #0xb2b6c4
    //     0xb2b6c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2b6c4: b               #0xb2b6cc
    // 0xb2b6c8: mov             x2, x1
    // 0xb2b6cc: r0 = Column()
    //     0xb2b6cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2b6d0: mov             x1, x0
    // 0xb2b6d4: r0 = Instance_Axis
    //     0xb2b6d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2b6d8: stur            x1, [fp, #-8]
    // 0xb2b6dc: StoreField: r1->field_f = r0
    //     0xb2b6dc: stur            w0, [x1, #0xf]
    // 0xb2b6e0: r0 = Instance_MainAxisAlignment
    //     0xb2b6e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2b6e4: ldr             x0, [x0, #0xa08]
    // 0xb2b6e8: StoreField: r1->field_13 = r0
    //     0xb2b6e8: stur            w0, [x1, #0x13]
    // 0xb2b6ec: r0 = Instance_MainAxisSize
    //     0xb2b6ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2b6f0: ldr             x0, [x0, #0xa10]
    // 0xb2b6f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2b6f4: stur            w0, [x1, #0x17]
    // 0xb2b6f8: r0 = Instance_CrossAxisAlignment
    //     0xb2b6f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2b6fc: ldr             x0, [x0, #0x890]
    // 0xb2b700: StoreField: r1->field_1b = r0
    //     0xb2b700: stur            w0, [x1, #0x1b]
    // 0xb2b704: r0 = Instance_VerticalDirection
    //     0xb2b704: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2b708: ldr             x0, [x0, #0xa20]
    // 0xb2b70c: StoreField: r1->field_23 = r0
    //     0xb2b70c: stur            w0, [x1, #0x23]
    // 0xb2b710: r0 = Instance_Clip
    //     0xb2b710: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2b714: ldr             x0, [x0, #0x38]
    // 0xb2b718: StoreField: r1->field_2b = r0
    //     0xb2b718: stur            w0, [x1, #0x2b]
    // 0xb2b71c: StoreField: r1->field_2f = rZR
    //     0xb2b71c: stur            xzr, [x1, #0x2f]
    // 0xb2b720: ldur            x0, [fp, #-0x28]
    // 0xb2b724: StoreField: r1->field_b = r0
    //     0xb2b724: stur            w0, [x1, #0xb]
    // 0xb2b728: r0 = Padding()
    //     0xb2b728: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2b72c: r1 = Instance_EdgeInsets
    //     0xb2b72c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57290] Obj!EdgeInsets@d59211
    //     0xb2b730: ldr             x1, [x1, #0x290]
    // 0xb2b734: StoreField: r0->field_f = r1
    //     0xb2b734: stur            w1, [x0, #0xf]
    // 0xb2b738: ldur            x1, [fp, #-8]
    // 0xb2b73c: StoreField: r0->field_b = r1
    //     0xb2b73c: stur            w1, [x0, #0xb]
    // 0xb2b740: LeaveFrame
    //     0xb2b740: mov             SP, fp
    //     0xb2b744: ldp             fp, lr, [SP], #0x10
    // 0xb2b748: ret
    //     0xb2b748: ret             
    // 0xb2b74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2b74c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2b750: b               #0xb2b37c
    // 0xb2b754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2b754: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2b758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2b758: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2b75c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2b75c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildQuantityControls(/* No info */) {
    // ** addr: 0xb2b760, size: 0x4d4
    // 0xb2b760: EnterFrame
    //     0xb2b760: stp             fp, lr, [SP, #-0x10]!
    //     0xb2b764: mov             fp, SP
    // 0xb2b768: AllocStack(0x50)
    //     0xb2b768: sub             SP, SP, #0x50
    // 0xb2b76c: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xb2b76c: mov             x0, x1
    //     0xb2b770: stur            x1, [fp, #-8]
    //     0xb2b774: mov             x1, x2
    //     0xb2b778: stur            x2, [fp, #-0x10]
    //     0xb2b77c: stur            x3, [fp, #-0x18]
    //     0xb2b780: stur            x5, [fp, #-0x20]
    // 0xb2b784: CheckStackOverflow
    //     0xb2b784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2b788: cmp             SP, x16
    //     0xb2b78c: b.ls            #0xb2bc20
    // 0xb2b790: r1 = 2
    //     0xb2b790: movz            x1, #0x2
    // 0xb2b794: r0 = AllocateContext()
    //     0xb2b794: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2b798: mov             x3, x0
    // 0xb2b79c: ldur            x2, [fp, #-8]
    // 0xb2b7a0: stur            x3, [fp, #-0x28]
    // 0xb2b7a4: StoreField: r3->field_f = r2
    //     0xb2b7a4: stur            w2, [x3, #0xf]
    // 0xb2b7a8: ldur            x4, [fp, #-0x18]
    // 0xb2b7ac: r0 = BoxInt64Instr(r4)
    //     0xb2b7ac: sbfiz           x0, x4, #1, #0x1f
    //     0xb2b7b0: cmp             x4, x0, asr #1
    //     0xb2b7b4: b.eq            #0xb2b7c0
    //     0xb2b7b8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2b7bc: stur            x4, [x0, #7]
    // 0xb2b7c0: StoreField: r3->field_13 = r0
    //     0xb2b7c0: stur            w0, [x3, #0x13]
    // 0xb2b7c4: LoadField: r0 = r2->field_b
    //     0xb2b7c4: ldur            w0, [x2, #0xb]
    // 0xb2b7c8: DecompressPointer r0
    //     0xb2b7c8: add             x0, x0, HEAP, lsl #32
    // 0xb2b7cc: cmp             w0, NULL
    // 0xb2b7d0: b.eq            #0xb2bc28
    // 0xb2b7d4: LoadField: r1 = r0->field_2f
    //     0xb2b7d4: ldur            w1, [x0, #0x2f]
    // 0xb2b7d8: DecompressPointer r1
    //     0xb2b7d8: add             x1, x1, HEAP, lsl #32
    // 0xb2b7dc: LoadField: r4 = r1->field_7
    //     0xb2b7dc: ldur            w4, [x1, #7]
    // 0xb2b7e0: cbz             w4, #0xb2b870
    // 0xb2b7e4: LoadField: r1 = r0->field_33
    //     0xb2b7e4: ldur            w1, [x0, #0x33]
    // 0xb2b7e8: DecompressPointer r1
    //     0xb2b7e8: add             x1, x1, HEAP, lsl #32
    // 0xb2b7ec: LoadField: r4 = r1->field_7
    //     0xb2b7ec: ldur            w4, [x1, #7]
    // 0xb2b7f0: cbz             w4, #0xb2b868
    // 0xb2b7f4: ldur            x1, [fp, #-0x20]
    // 0xb2b7f8: LoadField: r4 = r0->field_37
    //     0xb2b7f8: ldur            w4, [x0, #0x37]
    // 0xb2b7fc: DecompressPointer r4
    //     0xb2b7fc: add             x4, x4, HEAP, lsl #32
    // 0xb2b800: LoadField: r0 = r1->field_7
    //     0xb2b800: ldur            w0, [x1, #7]
    // 0xb2b804: DecompressPointer r0
    //     0xb2b804: add             x0, x0, HEAP, lsl #32
    // 0xb2b808: r5 = LoadClassIdInstr(r4)
    //     0xb2b808: ldur            x5, [x4, #-1]
    //     0xb2b80c: ubfx            x5, x5, #0xc, #0x14
    // 0xb2b810: stp             x0, x4, [SP]
    // 0xb2b814: mov             x0, x5
    // 0xb2b818: mov             lr, x0
    // 0xb2b81c: ldr             lr, [x21, lr, lsl #3]
    // 0xb2b820: blr             lr
    // 0xb2b824: tbz             w0, #4, #0xb2b860
    // 0xb2b828: ldur            x0, [fp, #-8]
    // 0xb2b82c: LoadField: r1 = r0->field_b
    //     0xb2b82c: ldur            w1, [x0, #0xb]
    // 0xb2b830: DecompressPointer r1
    //     0xb2b830: add             x1, x1, HEAP, lsl #32
    // 0xb2b834: cmp             w1, NULL
    // 0xb2b838: b.eq            #0xb2bc2c
    // 0xb2b83c: LoadField: r2 = r1->field_37
    //     0xb2b83c: ldur            w2, [x1, #0x37]
    // 0xb2b840: DecompressPointer r2
    //     0xb2b840: add             x2, x2, HEAP, lsl #32
    // 0xb2b844: LoadField: r1 = r2->field_7
    //     0xb2b844: ldur            w1, [x2, #7]
    // 0xb2b848: cbnz            w1, #0xb2b854
    // 0xb2b84c: r2 = false
    //     0xb2b84c: add             x2, NULL, #0x30  ; false
    // 0xb2b850: b               #0xb2b858
    // 0xb2b854: r2 = true
    //     0xb2b854: add             x2, NULL, #0x20  ; true
    // 0xb2b858: mov             x3, x2
    // 0xb2b85c: b               #0xb2b878
    // 0xb2b860: ldur            x0, [fp, #-8]
    // 0xb2b864: b               #0xb2b874
    // 0xb2b868: mov             x0, x2
    // 0xb2b86c: b               #0xb2b874
    // 0xb2b870: mov             x0, x2
    // 0xb2b874: r3 = false
    //     0xb2b874: add             x3, NULL, #0x30  ; false
    // 0xb2b878: ldur            x2, [fp, #-0x28]
    // 0xb2b87c: stur            x3, [fp, #-0x30]
    // 0xb2b880: LoadField: r1 = r2->field_13
    //     0xb2b880: ldur            w1, [x2, #0x13]
    // 0xb2b884: DecompressPointer r1
    //     0xb2b884: add             x1, x1, HEAP, lsl #32
    // 0xb2b888: LoadField: r4 = r0->field_13
    //     0xb2b888: ldur            w4, [x0, #0x13]
    // 0xb2b88c: DecompressPointer r4
    //     0xb2b88c: add             x4, x4, HEAP, lsl #32
    // 0xb2b890: LoadField: r0 = r4->field_b
    //     0xb2b890: ldur            w0, [x4, #0xb]
    // 0xb2b894: r5 = LoadInt32Instr(r1)
    //     0xb2b894: sbfx            x5, x1, #1, #0x1f
    //     0xb2b898: tbz             w1, #0, #0xb2b8a0
    //     0xb2b89c: ldur            x5, [x1, #7]
    // 0xb2b8a0: r1 = LoadInt32Instr(r0)
    //     0xb2b8a0: sbfx            x1, x0, #1, #0x1f
    // 0xb2b8a4: cmp             x5, x1
    // 0xb2b8a8: b.ge            #0xb2b8e0
    // 0xb2b8ac: mov             x0, x1
    // 0xb2b8b0: mov             x1, x5
    // 0xb2b8b4: cmp             x1, x0
    // 0xb2b8b8: b.hs            #0xb2bc30
    // 0xb2b8bc: LoadField: r0 = r4->field_f
    //     0xb2b8bc: ldur            w0, [x4, #0xf]
    // 0xb2b8c0: DecompressPointer r0
    //     0xb2b8c0: add             x0, x0, HEAP, lsl #32
    // 0xb2b8c4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb2b8c4: add             x16, x0, x5, lsl #2
    //     0xb2b8c8: ldur            w1, [x16, #0xf]
    // 0xb2b8cc: DecompressPointer r1
    //     0xb2b8cc: add             x1, x1, HEAP, lsl #32
    // 0xb2b8d0: r0 = LoadInt32Instr(r1)
    //     0xb2b8d0: sbfx            x0, x1, #1, #0x1f
    //     0xb2b8d4: tbz             w1, #0, #0xb2b8dc
    //     0xb2b8d8: ldur            x0, [x1, #7]
    // 0xb2b8dc: b               #0xb2b908
    // 0xb2b8e0: ldur            x0, [fp, #-0x20]
    // 0xb2b8e4: LoadField: r1 = r0->field_5f
    //     0xb2b8e4: ldur            w1, [x0, #0x5f]
    // 0xb2b8e8: DecompressPointer r1
    //     0xb2b8e8: add             x1, x1, HEAP, lsl #32
    // 0xb2b8ec: cmp             w1, NULL
    // 0xb2b8f0: b.ne            #0xb2b8fc
    // 0xb2b8f4: r0 = 0
    //     0xb2b8f4: movz            x0, #0
    // 0xb2b8f8: b               #0xb2b908
    // 0xb2b8fc: r0 = LoadInt32Instr(r1)
    //     0xb2b8fc: sbfx            x0, x1, #1, #0x1f
    //     0xb2b900: tbz             w1, #0, #0xb2b908
    //     0xb2b904: ldur            x0, [x1, #7]
    // 0xb2b908: ldur            x1, [fp, #-0x10]
    // 0xb2b90c: stur            x0, [fp, #-0x18]
    // 0xb2b910: r0 = of()
    //     0xb2b910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2b914: LoadField: r1 = r0->field_5b
    //     0xb2b914: ldur            w1, [x0, #0x5b]
    // 0xb2b918: DecompressPointer r1
    //     0xb2b918: add             x1, x1, HEAP, lsl #32
    // 0xb2b91c: stur            x1, [fp, #-8]
    // 0xb2b920: r0 = ColorFilter()
    //     0xb2b920: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb2b924: mov             x1, x0
    // 0xb2b928: ldur            x0, [fp, #-8]
    // 0xb2b92c: stur            x1, [fp, #-0x20]
    // 0xb2b930: StoreField: r1->field_7 = r0
    //     0xb2b930: stur            w0, [x1, #7]
    // 0xb2b934: r0 = Instance_BlendMode
    //     0xb2b934: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb2b938: ldr             x0, [x0, #0xb30]
    // 0xb2b93c: StoreField: r1->field_b = r0
    //     0xb2b93c: stur            w0, [x1, #0xb]
    // 0xb2b940: r2 = 1
    //     0xb2b940: movz            x2, #0x1
    // 0xb2b944: StoreField: r1->field_13 = r2
    //     0xb2b944: stur            x2, [x1, #0x13]
    // 0xb2b948: r0 = SvgPicture()
    //     0xb2b948: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb2b94c: stur            x0, [fp, #-8]
    // 0xb2b950: ldur            x16, [fp, #-0x20]
    // 0xb2b954: str             x16, [SP]
    // 0xb2b958: mov             x1, x0
    // 0xb2b95c: r2 = "assets/images/remove_icon_glass.svg"
    //     0xb2b95c: add             x2, PP, #0x56, lsl #12  ; [pp+0x56cf0] "assets/images/remove_icon_glass.svg"
    //     0xb2b960: ldr             x2, [x2, #0xcf0]
    // 0xb2b964: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb2b964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb2b968: ldr             x4, [x4, #0xa38]
    // 0xb2b96c: r0 = SvgPicture.asset()
    //     0xb2b96c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb2b970: r0 = InkWell()
    //     0xb2b970: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2b974: mov             x3, x0
    // 0xb2b978: ldur            x0, [fp, #-8]
    // 0xb2b97c: stur            x3, [fp, #-0x20]
    // 0xb2b980: StoreField: r3->field_b = r0
    //     0xb2b980: stur            w0, [x3, #0xb]
    // 0xb2b984: ldur            x2, [fp, #-0x28]
    // 0xb2b988: r1 = Function '<anonymous closure>':.
    //     0xb2b988: add             x1, PP, #0x57, lsl #12  ; [pp+0x57298] AnonymousClosure: (0xb2bc94), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildQuantityControls (0xb2b760)
    //     0xb2b98c: ldr             x1, [x1, #0x298]
    // 0xb2b990: r0 = AllocateClosure()
    //     0xb2b990: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2b994: mov             x1, x0
    // 0xb2b998: ldur            x0, [fp, #-0x20]
    // 0xb2b99c: StoreField: r0->field_f = r1
    //     0xb2b99c: stur            w1, [x0, #0xf]
    // 0xb2b9a0: r1 = true
    //     0xb2b9a0: add             x1, NULL, #0x20  ; true
    // 0xb2b9a4: StoreField: r0->field_43 = r1
    //     0xb2b9a4: stur            w1, [x0, #0x43]
    // 0xb2b9a8: r2 = Instance_BoxShape
    //     0xb2b9a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2b9ac: ldr             x2, [x2, #0x80]
    // 0xb2b9b0: StoreField: r0->field_47 = r2
    //     0xb2b9b0: stur            w2, [x0, #0x47]
    // 0xb2b9b4: StoreField: r0->field_6f = r1
    //     0xb2b9b4: stur            w1, [x0, #0x6f]
    // 0xb2b9b8: r3 = false
    //     0xb2b9b8: add             x3, NULL, #0x30  ; false
    // 0xb2b9bc: StoreField: r0->field_73 = r3
    //     0xb2b9bc: stur            w3, [x0, #0x73]
    // 0xb2b9c0: StoreField: r0->field_83 = r1
    //     0xb2b9c0: stur            w1, [x0, #0x83]
    // 0xb2b9c4: StoreField: r0->field_7b = r3
    //     0xb2b9c4: stur            w3, [x0, #0x7b]
    // 0xb2b9c8: r0 = IgnorePointer()
    //     0xb2b9c8: bl              #0x9d52b4  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xb2b9cc: mov             x3, x0
    // 0xb2b9d0: ldur            x2, [fp, #-0x30]
    // 0xb2b9d4: stur            x3, [fp, #-8]
    // 0xb2b9d8: StoreField: r3->field_f = r2
    //     0xb2b9d8: stur            w2, [x3, #0xf]
    // 0xb2b9dc: ldur            x0, [fp, #-0x20]
    // 0xb2b9e0: StoreField: r3->field_b = r0
    //     0xb2b9e0: stur            w0, [x3, #0xb]
    // 0xb2b9e4: ldur            x4, [fp, #-0x18]
    // 0xb2b9e8: r0 = BoxInt64Instr(r4)
    //     0xb2b9e8: sbfiz           x0, x4, #1, #0x1f
    //     0xb2b9ec: cmp             x4, x0, asr #1
    //     0xb2b9f0: b.eq            #0xb2b9fc
    //     0xb2b9f4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb2b9f8: stur            x4, [x0, #7]
    // 0xb2b9fc: r1 = 60
    //     0xb2b9fc: movz            x1, #0x3c
    // 0xb2ba00: branchIfSmi(r0, 0xb2ba0c)
    //     0xb2ba00: tbz             w0, #0, #0xb2ba0c
    // 0xb2ba04: r1 = LoadClassIdInstr(r0)
    //     0xb2ba04: ldur            x1, [x0, #-1]
    //     0xb2ba08: ubfx            x1, x1, #0xc, #0x14
    // 0xb2ba0c: str             x0, [SP]
    // 0xb2ba10: mov             x0, x1
    // 0xb2ba14: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2ba14: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2ba18: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2ba18: movz            x17, #0x2700
    //     0xb2ba1c: add             lr, x0, x17
    //     0xb2ba20: ldr             lr, [x21, lr, lsl #3]
    //     0xb2ba24: blr             lr
    // 0xb2ba28: ldur            x1, [fp, #-0x10]
    // 0xb2ba2c: stur            x0, [fp, #-0x20]
    // 0xb2ba30: r0 = of()
    //     0xb2ba30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ba34: LoadField: r1 = r0->field_87
    //     0xb2ba34: ldur            w1, [x0, #0x87]
    // 0xb2ba38: DecompressPointer r1
    //     0xb2ba38: add             x1, x1, HEAP, lsl #32
    // 0xb2ba3c: LoadField: r0 = r1->field_27
    //     0xb2ba3c: ldur            w0, [x1, #0x27]
    // 0xb2ba40: DecompressPointer r0
    //     0xb2ba40: add             x0, x0, HEAP, lsl #32
    // 0xb2ba44: r16 = Instance_Color
    //     0xb2ba44: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2ba48: str             x16, [SP]
    // 0xb2ba4c: mov             x1, x0
    // 0xb2ba50: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2ba50: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2ba54: ldr             x4, [x4, #0xf40]
    // 0xb2ba58: r0 = copyWith()
    //     0xb2ba58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2ba5c: stur            x0, [fp, #-0x38]
    // 0xb2ba60: r0 = Text()
    //     0xb2ba60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2ba64: mov             x1, x0
    // 0xb2ba68: ldur            x0, [fp, #-0x20]
    // 0xb2ba6c: stur            x1, [fp, #-0x40]
    // 0xb2ba70: StoreField: r1->field_b = r0
    //     0xb2ba70: stur            w0, [x1, #0xb]
    // 0xb2ba74: ldur            x0, [fp, #-0x38]
    // 0xb2ba78: StoreField: r1->field_13 = r0
    //     0xb2ba78: stur            w0, [x1, #0x13]
    // 0xb2ba7c: r0 = Padding()
    //     0xb2ba7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2ba80: mov             x2, x0
    // 0xb2ba84: r0 = Instance_EdgeInsets
    //     0xb2ba84: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb2ba88: ldr             x0, [x0, #0xf30]
    // 0xb2ba8c: stur            x2, [fp, #-0x20]
    // 0xb2ba90: StoreField: r2->field_f = r0
    //     0xb2ba90: stur            w0, [x2, #0xf]
    // 0xb2ba94: ldur            x0, [fp, #-0x40]
    // 0xb2ba98: StoreField: r2->field_b = r0
    //     0xb2ba98: stur            w0, [x2, #0xb]
    // 0xb2ba9c: ldur            x1, [fp, #-0x10]
    // 0xb2baa0: r0 = of()
    //     0xb2baa0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2baa4: LoadField: r1 = r0->field_5b
    //     0xb2baa4: ldur            w1, [x0, #0x5b]
    // 0xb2baa8: DecompressPointer r1
    //     0xb2baa8: add             x1, x1, HEAP, lsl #32
    // 0xb2baac: stur            x1, [fp, #-0x10]
    // 0xb2bab0: r0 = ColorFilter()
    //     0xb2bab0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb2bab4: mov             x1, x0
    // 0xb2bab8: ldur            x0, [fp, #-0x10]
    // 0xb2babc: stur            x1, [fp, #-0x38]
    // 0xb2bac0: StoreField: r1->field_7 = r0
    //     0xb2bac0: stur            w0, [x1, #7]
    // 0xb2bac4: r0 = Instance_BlendMode
    //     0xb2bac4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb2bac8: ldr             x0, [x0, #0xb30]
    // 0xb2bacc: StoreField: r1->field_b = r0
    //     0xb2bacc: stur            w0, [x1, #0xb]
    // 0xb2bad0: r0 = 1
    //     0xb2bad0: movz            x0, #0x1
    // 0xb2bad4: StoreField: r1->field_13 = r0
    //     0xb2bad4: stur            x0, [x1, #0x13]
    // 0xb2bad8: r0 = SvgPicture()
    //     0xb2bad8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb2badc: stur            x0, [fp, #-0x10]
    // 0xb2bae0: ldur            x16, [fp, #-0x38]
    // 0xb2bae4: str             x16, [SP]
    // 0xb2bae8: mov             x1, x0
    // 0xb2baec: r2 = "assets/images/add_icon_glass.svg"
    //     0xb2baec: add             x2, PP, #0x56, lsl #12  ; [pp+0x56d00] "assets/images/add_icon_glass.svg"
    //     0xb2baf0: ldr             x2, [x2, #0xd00]
    // 0xb2baf4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb2baf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb2baf8: ldr             x4, [x4, #0xa38]
    // 0xb2bafc: r0 = SvgPicture.asset()
    //     0xb2bafc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb2bb00: r0 = InkWell()
    //     0xb2bb00: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2bb04: mov             x3, x0
    // 0xb2bb08: ldur            x0, [fp, #-0x10]
    // 0xb2bb0c: stur            x3, [fp, #-0x38]
    // 0xb2bb10: StoreField: r3->field_b = r0
    //     0xb2bb10: stur            w0, [x3, #0xb]
    // 0xb2bb14: ldur            x2, [fp, #-0x28]
    // 0xb2bb18: r1 = Function '<anonymous closure>':.
    //     0xb2bb18: add             x1, PP, #0x57, lsl #12  ; [pp+0x572a0] AnonymousClosure: (0xb2bc34), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildQuantityControls (0xb2b760)
    //     0xb2bb1c: ldr             x1, [x1, #0x2a0]
    // 0xb2bb20: r0 = AllocateClosure()
    //     0xb2bb20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2bb24: mov             x1, x0
    // 0xb2bb28: ldur            x0, [fp, #-0x38]
    // 0xb2bb2c: StoreField: r0->field_f = r1
    //     0xb2bb2c: stur            w1, [x0, #0xf]
    // 0xb2bb30: r1 = true
    //     0xb2bb30: add             x1, NULL, #0x20  ; true
    // 0xb2bb34: StoreField: r0->field_43 = r1
    //     0xb2bb34: stur            w1, [x0, #0x43]
    // 0xb2bb38: r2 = Instance_BoxShape
    //     0xb2bb38: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2bb3c: ldr             x2, [x2, #0x80]
    // 0xb2bb40: StoreField: r0->field_47 = r2
    //     0xb2bb40: stur            w2, [x0, #0x47]
    // 0xb2bb44: StoreField: r0->field_6f = r1
    //     0xb2bb44: stur            w1, [x0, #0x6f]
    // 0xb2bb48: r2 = false
    //     0xb2bb48: add             x2, NULL, #0x30  ; false
    // 0xb2bb4c: StoreField: r0->field_73 = r2
    //     0xb2bb4c: stur            w2, [x0, #0x73]
    // 0xb2bb50: StoreField: r0->field_83 = r1
    //     0xb2bb50: stur            w1, [x0, #0x83]
    // 0xb2bb54: StoreField: r0->field_7b = r2
    //     0xb2bb54: stur            w2, [x0, #0x7b]
    // 0xb2bb58: r0 = IgnorePointer()
    //     0xb2bb58: bl              #0x9d52b4  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xb2bb5c: mov             x3, x0
    // 0xb2bb60: ldur            x0, [fp, #-0x30]
    // 0xb2bb64: stur            x3, [fp, #-0x10]
    // 0xb2bb68: StoreField: r3->field_f = r0
    //     0xb2bb68: stur            w0, [x3, #0xf]
    // 0xb2bb6c: ldur            x0, [fp, #-0x38]
    // 0xb2bb70: StoreField: r3->field_b = r0
    //     0xb2bb70: stur            w0, [x3, #0xb]
    // 0xb2bb74: r1 = Null
    //     0xb2bb74: mov             x1, NULL
    // 0xb2bb78: r2 = 6
    //     0xb2bb78: movz            x2, #0x6
    // 0xb2bb7c: r0 = AllocateArray()
    //     0xb2bb7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2bb80: mov             x2, x0
    // 0xb2bb84: ldur            x0, [fp, #-8]
    // 0xb2bb88: stur            x2, [fp, #-0x28]
    // 0xb2bb8c: StoreField: r2->field_f = r0
    //     0xb2bb8c: stur            w0, [x2, #0xf]
    // 0xb2bb90: ldur            x0, [fp, #-0x20]
    // 0xb2bb94: StoreField: r2->field_13 = r0
    //     0xb2bb94: stur            w0, [x2, #0x13]
    // 0xb2bb98: ldur            x0, [fp, #-0x10]
    // 0xb2bb9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2bb9c: stur            w0, [x2, #0x17]
    // 0xb2bba0: r1 = <Widget>
    //     0xb2bba0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2bba4: r0 = AllocateGrowableArray()
    //     0xb2bba4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2bba8: mov             x1, x0
    // 0xb2bbac: ldur            x0, [fp, #-0x28]
    // 0xb2bbb0: stur            x1, [fp, #-8]
    // 0xb2bbb4: StoreField: r1->field_f = r0
    //     0xb2bbb4: stur            w0, [x1, #0xf]
    // 0xb2bbb8: r0 = 6
    //     0xb2bbb8: movz            x0, #0x6
    // 0xb2bbbc: StoreField: r1->field_b = r0
    //     0xb2bbbc: stur            w0, [x1, #0xb]
    // 0xb2bbc0: r0 = Row()
    //     0xb2bbc0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2bbc4: r1 = Instance_Axis
    //     0xb2bbc4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2bbc8: StoreField: r0->field_f = r1
    //     0xb2bbc8: stur            w1, [x0, #0xf]
    // 0xb2bbcc: r1 = Instance_MainAxisAlignment
    //     0xb2bbcc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2bbd0: ldr             x1, [x1, #0xa08]
    // 0xb2bbd4: StoreField: r0->field_13 = r1
    //     0xb2bbd4: stur            w1, [x0, #0x13]
    // 0xb2bbd8: r1 = Instance_MainAxisSize
    //     0xb2bbd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2bbdc: ldr             x1, [x1, #0xa10]
    // 0xb2bbe0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2bbe0: stur            w1, [x0, #0x17]
    // 0xb2bbe4: r1 = Instance_CrossAxisAlignment
    //     0xb2bbe4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2bbe8: ldr             x1, [x1, #0xa18]
    // 0xb2bbec: StoreField: r0->field_1b = r1
    //     0xb2bbec: stur            w1, [x0, #0x1b]
    // 0xb2bbf0: r1 = Instance_VerticalDirection
    //     0xb2bbf0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2bbf4: ldr             x1, [x1, #0xa20]
    // 0xb2bbf8: StoreField: r0->field_23 = r1
    //     0xb2bbf8: stur            w1, [x0, #0x23]
    // 0xb2bbfc: r1 = Instance_Clip
    //     0xb2bbfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2bc00: ldr             x1, [x1, #0x38]
    // 0xb2bc04: StoreField: r0->field_2b = r1
    //     0xb2bc04: stur            w1, [x0, #0x2b]
    // 0xb2bc08: StoreField: r0->field_2f = rZR
    //     0xb2bc08: stur            xzr, [x0, #0x2f]
    // 0xb2bc0c: ldur            x1, [fp, #-8]
    // 0xb2bc10: StoreField: r0->field_b = r1
    //     0xb2bc10: stur            w1, [x0, #0xb]
    // 0xb2bc14: LeaveFrame
    //     0xb2bc14: mov             SP, fp
    //     0xb2bc18: ldp             fp, lr, [SP], #0x10
    // 0xb2bc1c: ret
    //     0xb2bc1c: ret             
    // 0xb2bc20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2bc20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2bc24: b               #0xb2b790
    // 0xb2bc28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2bc28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2bc2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2bc2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2bc30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2bc30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2bc34, size: 0x60
    // 0xb2bc34: EnterFrame
    //     0xb2bc34: stp             fp, lr, [SP, #-0x10]!
    //     0xb2bc38: mov             fp, SP
    // 0xb2bc3c: ldr             x0, [fp, #0x10]
    // 0xb2bc40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2bc40: ldur            w1, [x0, #0x17]
    // 0xb2bc44: DecompressPointer r1
    //     0xb2bc44: add             x1, x1, HEAP, lsl #32
    // 0xb2bc48: CheckStackOverflow
    //     0xb2bc48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2bc4c: cmp             SP, x16
    //     0xb2bc50: b.ls            #0xb2bc8c
    // 0xb2bc54: LoadField: r0 = r1->field_f
    //     0xb2bc54: ldur            w0, [x1, #0xf]
    // 0xb2bc58: DecompressPointer r0
    //     0xb2bc58: add             x0, x0, HEAP, lsl #32
    // 0xb2bc5c: LoadField: r2 = r1->field_13
    //     0xb2bc5c: ldur            w2, [x1, #0x13]
    // 0xb2bc60: DecompressPointer r2
    //     0xb2bc60: add             x2, x2, HEAP, lsl #32
    // 0xb2bc64: r1 = LoadInt32Instr(r2)
    //     0xb2bc64: sbfx            x1, x2, #1, #0x1f
    //     0xb2bc68: tbz             w2, #0, #0xb2bc70
    //     0xb2bc6c: ldur            x1, [x2, #7]
    // 0xb2bc70: mov             x2, x1
    // 0xb2bc74: mov             x1, x0
    // 0xb2bc78: r0 = _handleQuantityIncrease()
    //     0xb2bc78: bl              #0x9d56fc  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_handleQuantityIncrease
    // 0xb2bc7c: r0 = Null
    //     0xb2bc7c: mov             x0, NULL
    // 0xb2bc80: LeaveFrame
    //     0xb2bc80: mov             SP, fp
    //     0xb2bc84: ldp             fp, lr, [SP], #0x10
    // 0xb2bc88: ret
    //     0xb2bc88: ret             
    // 0xb2bc8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2bc8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2bc90: b               #0xb2bc54
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2bc94, size: 0x60
    // 0xb2bc94: EnterFrame
    //     0xb2bc94: stp             fp, lr, [SP, #-0x10]!
    //     0xb2bc98: mov             fp, SP
    // 0xb2bc9c: ldr             x0, [fp, #0x10]
    // 0xb2bca0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2bca0: ldur            w1, [x0, #0x17]
    // 0xb2bca4: DecompressPointer r1
    //     0xb2bca4: add             x1, x1, HEAP, lsl #32
    // 0xb2bca8: CheckStackOverflow
    //     0xb2bca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2bcac: cmp             SP, x16
    //     0xb2bcb0: b.ls            #0xb2bcec
    // 0xb2bcb4: LoadField: r0 = r1->field_f
    //     0xb2bcb4: ldur            w0, [x1, #0xf]
    // 0xb2bcb8: DecompressPointer r0
    //     0xb2bcb8: add             x0, x0, HEAP, lsl #32
    // 0xb2bcbc: LoadField: r2 = r1->field_13
    //     0xb2bcbc: ldur            w2, [x1, #0x13]
    // 0xb2bcc0: DecompressPointer r2
    //     0xb2bcc0: add             x2, x2, HEAP, lsl #32
    // 0xb2bcc4: r1 = LoadInt32Instr(r2)
    //     0xb2bcc4: sbfx            x1, x2, #1, #0x1f
    //     0xb2bcc8: tbz             w2, #0, #0xb2bcd0
    //     0xb2bccc: ldur            x1, [x2, #7]
    // 0xb2bcd0: mov             x2, x1
    // 0xb2bcd4: mov             x1, x0
    // 0xb2bcd8: r0 = _handleQuantityDecrease()
    //     0xb2bcd8: bl              #0x9d5ef0  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_handleQuantityDecrease
    // 0xb2bcdc: r0 = Null
    //     0xb2bcdc: mov             x0, NULL
    // 0xb2bce0: LeaveFrame
    //     0xb2bce0: mov             SP, fp
    //     0xb2bce4: ldp             fp, lr, [SP], #0x10
    // 0xb2bce8: ret
    //     0xb2bce8: ret             
    // 0xb2bcec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2bcec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2bcf0: b               #0xb2bcb4
  }
  _ _buildCatalogueDetails(/* No info */) {
    // ** addr: 0xb2bcf4, size: 0x3c8
    // 0xb2bcf4: EnterFrame
    //     0xb2bcf4: stp             fp, lr, [SP, #-0x10]!
    //     0xb2bcf8: mov             fp, SP
    // 0xb2bcfc: AllocStack(0x58)
    //     0xb2bcfc: sub             SP, SP, #0x58
    // 0xb2bd00: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb2bd00: mov             x0, x1
    //     0xb2bd04: stur            x1, [fp, #-8]
    //     0xb2bd08: mov             x1, x2
    //     0xb2bd0c: stur            x2, [fp, #-0x10]
    //     0xb2bd10: stur            x3, [fp, #-0x18]
    // 0xb2bd14: CheckStackOverflow
    //     0xb2bd14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2bd18: cmp             SP, x16
    //     0xb2bd1c: b.ls            #0xb2c09c
    // 0xb2bd20: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2bd20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2bd24: ldr             x0, [x0, #0x1c80]
    //     0xb2bd28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2bd2c: cmp             w0, w16
    //     0xb2bd30: b.ne            #0xb2bd3c
    //     0xb2bd34: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2bd38: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2bd3c: r0 = GetNavigation.size()
    //     0xb2bd3c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2bd40: LoadField: d0 = r0->field_7
    //     0xb2bd40: ldur            d0, [x0, #7]
    // 0xb2bd44: d1 = 0.450000
    //     0xb2bd44: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xb2bd48: ldr             d1, [x17, #0xd08]
    // 0xb2bd4c: fmul            d2, d0, d1
    // 0xb2bd50: ldur            x3, [fp, #-0x18]
    // 0xb2bd54: stur            d2, [fp, #-0x48]
    // 0xb2bd58: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb2bd58: ldur            w0, [x3, #0x17]
    // 0xb2bd5c: DecompressPointer r0
    //     0xb2bd5c: add             x0, x0, HEAP, lsl #32
    // 0xb2bd60: cmp             w0, NULL
    // 0xb2bd64: b.ne            #0xb2bd70
    // 0xb2bd68: r0 = Null
    //     0xb2bd68: mov             x0, NULL
    // 0xb2bd6c: b               #0xb2bd7c
    // 0xb2bd70: LoadField: r1 = r0->field_7
    //     0xb2bd70: ldur            w1, [x0, #7]
    // 0xb2bd74: DecompressPointer r1
    //     0xb2bd74: add             x1, x1, HEAP, lsl #32
    // 0xb2bd78: mov             x0, x1
    // 0xb2bd7c: cmp             w0, NULL
    // 0xb2bd80: b.ne            #0xb2bd8c
    // 0xb2bd84: r1 = ""
    //     0xb2bd84: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2bd88: b               #0xb2bd90
    // 0xb2bd8c: mov             x1, x0
    // 0xb2bd90: r0 = capitalizeFirstWord()
    //     0xb2bd90: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb2bd94: ldur            x1, [fp, #-0x10]
    // 0xb2bd98: stur            x0, [fp, #-0x20]
    // 0xb2bd9c: r0 = of()
    //     0xb2bd9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2bda0: LoadField: r1 = r0->field_87
    //     0xb2bda0: ldur            w1, [x0, #0x87]
    // 0xb2bda4: DecompressPointer r1
    //     0xb2bda4: add             x1, x1, HEAP, lsl #32
    // 0xb2bda8: LoadField: r0 = r1->field_2f
    //     0xb2bda8: ldur            w0, [x1, #0x2f]
    // 0xb2bdac: DecompressPointer r0
    //     0xb2bdac: add             x0, x0, HEAP, lsl #32
    // 0xb2bdb0: stur            x0, [fp, #-0x28]
    // 0xb2bdb4: cmp             w0, NULL
    // 0xb2bdb8: b.ne            #0xb2bdc4
    // 0xb2bdbc: r1 = Null
    //     0xb2bdbc: mov             x1, NULL
    // 0xb2bdc0: b               #0xb2bdf4
    // 0xb2bdc4: r1 = Instance_Color
    //     0xb2bdc4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2bdc8: d0 = 0.700000
    //     0xb2bdc8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2bdcc: ldr             d0, [x17, #0xf48]
    // 0xb2bdd0: r0 = withOpacity()
    //     0xb2bdd0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2bdd4: r16 = 12.000000
    //     0xb2bdd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2bdd8: ldr             x16, [x16, #0x9e8]
    // 0xb2bddc: stp             x0, x16, [SP]
    // 0xb2bde0: ldur            x1, [fp, #-0x28]
    // 0xb2bde4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2bde4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2bde8: ldr             x4, [x4, #0xaa0]
    // 0xb2bdec: r0 = copyWith()
    //     0xb2bdec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2bdf0: mov             x1, x0
    // 0xb2bdf4: ldur            x3, [fp, #-0x18]
    // 0xb2bdf8: ldur            d0, [fp, #-0x48]
    // 0xb2bdfc: ldur            x0, [fp, #-0x20]
    // 0xb2be00: stur            x1, [fp, #-0x28]
    // 0xb2be04: r0 = Text()
    //     0xb2be04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2be08: mov             x1, x0
    // 0xb2be0c: ldur            x0, [fp, #-0x20]
    // 0xb2be10: stur            x1, [fp, #-0x30]
    // 0xb2be14: StoreField: r1->field_b = r0
    //     0xb2be14: stur            w0, [x1, #0xb]
    // 0xb2be18: ldur            x0, [fp, #-0x28]
    // 0xb2be1c: StoreField: r1->field_13 = r0
    //     0xb2be1c: stur            w0, [x1, #0x13]
    // 0xb2be20: r0 = Instance_TextOverflow
    //     0xb2be20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb2be24: ldr             x0, [x0, #0xe10]
    // 0xb2be28: StoreField: r1->field_2b = r0
    //     0xb2be28: stur            w0, [x1, #0x2b]
    // 0xb2be2c: r0 = 2
    //     0xb2be2c: movz            x0, #0x2
    // 0xb2be30: StoreField: r1->field_37 = r0
    //     0xb2be30: stur            w0, [x1, #0x37]
    // 0xb2be34: ldur            d0, [fp, #-0x48]
    // 0xb2be38: r0 = inline_Allocate_Double()
    //     0xb2be38: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb2be3c: add             x0, x0, #0x10
    //     0xb2be40: cmp             x2, x0
    //     0xb2be44: b.ls            #0xb2c0a4
    //     0xb2be48: str             x0, [THR, #0x50]  ; THR::top
    //     0xb2be4c: sub             x0, x0, #0xf
    //     0xb2be50: movz            x2, #0xe15c
    //     0xb2be54: movk            x2, #0x3, lsl #16
    //     0xb2be58: stur            x2, [x0, #-1]
    // 0xb2be5c: StoreField: r0->field_7 = d0
    //     0xb2be5c: stur            d0, [x0, #7]
    // 0xb2be60: stur            x0, [fp, #-0x20]
    // 0xb2be64: r0 = SizedBox()
    //     0xb2be64: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb2be68: mov             x3, x0
    // 0xb2be6c: ldur            x0, [fp, #-0x20]
    // 0xb2be70: stur            x3, [fp, #-0x28]
    // 0xb2be74: StoreField: r3->field_f = r0
    //     0xb2be74: stur            w0, [x3, #0xf]
    // 0xb2be78: ldur            x0, [fp, #-0x30]
    // 0xb2be7c: StoreField: r3->field_b = r0
    //     0xb2be7c: stur            w0, [x3, #0xb]
    // 0xb2be80: r1 = Null
    //     0xb2be80: mov             x1, NULL
    // 0xb2be84: r2 = 4
    //     0xb2be84: movz            x2, #0x4
    // 0xb2be88: r0 = AllocateArray()
    //     0xb2be88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2be8c: r16 = "Size: "
    //     0xb2be8c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb2be90: ldr             x16, [x16, #0xf00]
    // 0xb2be94: StoreField: r0->field_f = r16
    //     0xb2be94: stur            w16, [x0, #0xf]
    // 0xb2be98: ldur            x3, [fp, #-0x18]
    // 0xb2be9c: LoadField: r1 = r3->field_53
    //     0xb2be9c: ldur            w1, [x3, #0x53]
    // 0xb2bea0: DecompressPointer r1
    //     0xb2bea0: add             x1, x1, HEAP, lsl #32
    // 0xb2bea4: cmp             w1, NULL
    // 0xb2bea8: b.ne            #0xb2beb0
    // 0xb2beac: r1 = ""
    //     0xb2beac: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2beb0: StoreField: r0->field_13 = r1
    //     0xb2beb0: stur            w1, [x0, #0x13]
    // 0xb2beb4: str             x0, [SP]
    // 0xb2beb8: r0 = _interpolate()
    //     0xb2beb8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2bebc: ldur            x1, [fp, #-0x10]
    // 0xb2bec0: stur            x0, [fp, #-0x20]
    // 0xb2bec4: r0 = of()
    //     0xb2bec4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2bec8: LoadField: r1 = r0->field_87
    //     0xb2bec8: ldur            w1, [x0, #0x87]
    // 0xb2becc: DecompressPointer r1
    //     0xb2becc: add             x1, x1, HEAP, lsl #32
    // 0xb2bed0: LoadField: r0 = r1->field_7
    //     0xb2bed0: ldur            w0, [x1, #7]
    // 0xb2bed4: DecompressPointer r0
    //     0xb2bed4: add             x0, x0, HEAP, lsl #32
    // 0xb2bed8: stur            x0, [fp, #-0x30]
    // 0xb2bedc: r1 = Instance_Color
    //     0xb2bedc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2bee0: d0 = 0.700000
    //     0xb2bee0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2bee4: ldr             d0, [x17, #0xf48]
    // 0xb2bee8: r0 = withOpacity()
    //     0xb2bee8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2beec: r16 = 12.000000
    //     0xb2beec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2bef0: ldr             x16, [x16, #0x9e8]
    // 0xb2bef4: stp             x0, x16, [SP]
    // 0xb2bef8: ldur            x1, [fp, #-0x30]
    // 0xb2befc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2befc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2bf00: ldr             x4, [x4, #0xaa0]
    // 0xb2bf04: r0 = copyWith()
    //     0xb2bf04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2bf08: stur            x0, [fp, #-0x30]
    // 0xb2bf0c: r0 = Text()
    //     0xb2bf0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2bf10: mov             x1, x0
    // 0xb2bf14: ldur            x0, [fp, #-0x20]
    // 0xb2bf18: stur            x1, [fp, #-0x38]
    // 0xb2bf1c: StoreField: r1->field_b = r0
    //     0xb2bf1c: stur            w0, [x1, #0xb]
    // 0xb2bf20: ldur            x0, [fp, #-0x30]
    // 0xb2bf24: StoreField: r1->field_13 = r0
    //     0xb2bf24: stur            w0, [x1, #0x13]
    // 0xb2bf28: r0 = Padding()
    //     0xb2bf28: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2bf2c: mov             x2, x0
    // 0xb2bf30: r0 = Instance_EdgeInsets
    //     0xb2bf30: add             x0, PP, #0x42, lsl #12  ; [pp+0x42638] Obj!EdgeInsets@d591e1
    //     0xb2bf34: ldr             x0, [x0, #0x638]
    // 0xb2bf38: stur            x2, [fp, #-0x30]
    // 0xb2bf3c: StoreField: r2->field_f = r0
    //     0xb2bf3c: stur            w0, [x2, #0xf]
    // 0xb2bf40: ldur            x0, [fp, #-0x38]
    // 0xb2bf44: StoreField: r2->field_b = r0
    //     0xb2bf44: stur            w0, [x2, #0xb]
    // 0xb2bf48: ldur            x3, [fp, #-0x18]
    // 0xb2bf4c: LoadField: r0 = r3->field_27
    //     0xb2bf4c: ldur            w0, [x3, #0x27]
    // 0xb2bf50: DecompressPointer r0
    //     0xb2bf50: add             x0, x0, HEAP, lsl #32
    // 0xb2bf54: cmp             w0, NULL
    // 0xb2bf58: b.ne            #0xb2bf64
    // 0xb2bf5c: r4 = ""
    //     0xb2bf5c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2bf60: b               #0xb2bf68
    // 0xb2bf64: mov             x4, x0
    // 0xb2bf68: ldur            x0, [fp, #-0x28]
    // 0xb2bf6c: ldur            x1, [fp, #-0x10]
    // 0xb2bf70: stur            x4, [fp, #-0x20]
    // 0xb2bf74: r0 = of()
    //     0xb2bf74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2bf78: LoadField: r1 = r0->field_87
    //     0xb2bf78: ldur            w1, [x0, #0x87]
    // 0xb2bf7c: DecompressPointer r1
    //     0xb2bf7c: add             x1, x1, HEAP, lsl #32
    // 0xb2bf80: LoadField: r0 = r1->field_7
    //     0xb2bf80: ldur            w0, [x1, #7]
    // 0xb2bf84: DecompressPointer r0
    //     0xb2bf84: add             x0, x0, HEAP, lsl #32
    // 0xb2bf88: r16 = Instance_Color
    //     0xb2bf88: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2bf8c: r30 = 16.000000
    //     0xb2bf8c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2bf90: ldr             lr, [lr, #0x188]
    // 0xb2bf94: stp             lr, x16, [SP]
    // 0xb2bf98: mov             x1, x0
    // 0xb2bf9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb2bf9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb2bfa0: ldr             x4, [x4, #0x9b8]
    // 0xb2bfa4: r0 = copyWith()
    //     0xb2bfa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2bfa8: stur            x0, [fp, #-0x38]
    // 0xb2bfac: r0 = Text()
    //     0xb2bfac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2bfb0: mov             x4, x0
    // 0xb2bfb4: ldur            x0, [fp, #-0x20]
    // 0xb2bfb8: stur            x4, [fp, #-0x40]
    // 0xb2bfbc: StoreField: r4->field_b = r0
    //     0xb2bfbc: stur            w0, [x4, #0xb]
    // 0xb2bfc0: ldur            x0, [fp, #-0x38]
    // 0xb2bfc4: StoreField: r4->field_13 = r0
    //     0xb2bfc4: stur            w0, [x4, #0x13]
    // 0xb2bfc8: r0 = Instance_TextOverflow
    //     0xb2bfc8: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xb2bfcc: ldr             x0, [x0, #0x3a8]
    // 0xb2bfd0: StoreField: r4->field_2b = r0
    //     0xb2bfd0: stur            w0, [x4, #0x2b]
    // 0xb2bfd4: ldur            x1, [fp, #-8]
    // 0xb2bfd8: ldur            x2, [fp, #-0x10]
    // 0xb2bfdc: ldur            x3, [fp, #-0x18]
    // 0xb2bfe0: r0 = _buildStatusMessages()
    //     0xb2bfe0: bl              #0xb2c0bc  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildStatusMessages
    // 0xb2bfe4: r1 = Null
    //     0xb2bfe4: mov             x1, NULL
    // 0xb2bfe8: r2 = 8
    //     0xb2bfe8: movz            x2, #0x8
    // 0xb2bfec: stur            x0, [fp, #-8]
    // 0xb2bff0: r0 = AllocateArray()
    //     0xb2bff0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2bff4: mov             x2, x0
    // 0xb2bff8: ldur            x0, [fp, #-0x28]
    // 0xb2bffc: stur            x2, [fp, #-0x10]
    // 0xb2c000: StoreField: r2->field_f = r0
    //     0xb2c000: stur            w0, [x2, #0xf]
    // 0xb2c004: ldur            x0, [fp, #-0x30]
    // 0xb2c008: StoreField: r2->field_13 = r0
    //     0xb2c008: stur            w0, [x2, #0x13]
    // 0xb2c00c: ldur            x0, [fp, #-0x40]
    // 0xb2c010: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2c010: stur            w0, [x2, #0x17]
    // 0xb2c014: ldur            x0, [fp, #-8]
    // 0xb2c018: StoreField: r2->field_1b = r0
    //     0xb2c018: stur            w0, [x2, #0x1b]
    // 0xb2c01c: r1 = <Widget>
    //     0xb2c01c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2c020: r0 = AllocateGrowableArray()
    //     0xb2c020: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2c024: mov             x1, x0
    // 0xb2c028: ldur            x0, [fp, #-0x10]
    // 0xb2c02c: stur            x1, [fp, #-8]
    // 0xb2c030: StoreField: r1->field_f = r0
    //     0xb2c030: stur            w0, [x1, #0xf]
    // 0xb2c034: r0 = 8
    //     0xb2c034: movz            x0, #0x8
    // 0xb2c038: StoreField: r1->field_b = r0
    //     0xb2c038: stur            w0, [x1, #0xb]
    // 0xb2c03c: r0 = Column()
    //     0xb2c03c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2c040: r1 = Instance_Axis
    //     0xb2c040: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2c044: StoreField: r0->field_f = r1
    //     0xb2c044: stur            w1, [x0, #0xf]
    // 0xb2c048: r1 = Instance_MainAxisAlignment
    //     0xb2c048: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2c04c: ldr             x1, [x1, #0xa08]
    // 0xb2c050: StoreField: r0->field_13 = r1
    //     0xb2c050: stur            w1, [x0, #0x13]
    // 0xb2c054: r1 = Instance_MainAxisSize
    //     0xb2c054: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2c058: ldr             x1, [x1, #0xa10]
    // 0xb2c05c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2c05c: stur            w1, [x0, #0x17]
    // 0xb2c060: r1 = Instance_CrossAxisAlignment
    //     0xb2c060: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2c064: ldr             x1, [x1, #0x890]
    // 0xb2c068: StoreField: r0->field_1b = r1
    //     0xb2c068: stur            w1, [x0, #0x1b]
    // 0xb2c06c: r1 = Instance_VerticalDirection
    //     0xb2c06c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2c070: ldr             x1, [x1, #0xa20]
    // 0xb2c074: StoreField: r0->field_23 = r1
    //     0xb2c074: stur            w1, [x0, #0x23]
    // 0xb2c078: r1 = Instance_Clip
    //     0xb2c078: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2c07c: ldr             x1, [x1, #0x38]
    // 0xb2c080: StoreField: r0->field_2b = r1
    //     0xb2c080: stur            w1, [x0, #0x2b]
    // 0xb2c084: StoreField: r0->field_2f = rZR
    //     0xb2c084: stur            xzr, [x0, #0x2f]
    // 0xb2c088: ldur            x1, [fp, #-8]
    // 0xb2c08c: StoreField: r0->field_b = r1
    //     0xb2c08c: stur            w1, [x0, #0xb]
    // 0xb2c090: LeaveFrame
    //     0xb2c090: mov             SP, fp
    //     0xb2c094: ldp             fp, lr, [SP], #0x10
    // 0xb2c098: ret
    //     0xb2c098: ret             
    // 0xb2c09c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2c09c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2c0a0: b               #0xb2bd20
    // 0xb2c0a4: SaveReg d0
    //     0xb2c0a4: str             q0, [SP, #-0x10]!
    // 0xb2c0a8: SaveReg r1
    //     0xb2c0a8: str             x1, [SP, #-8]!
    // 0xb2c0ac: r0 = AllocateDouble()
    //     0xb2c0ac: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb2c0b0: RestoreReg r1
    //     0xb2c0b0: ldr             x1, [SP], #8
    // 0xb2c0b4: RestoreReg d0
    //     0xb2c0b4: ldr             q0, [SP], #0x10
    // 0xb2c0b8: b               #0xb2be5c
  }
  _ _buildStatusMessages(/* No info */) {
    // ** addr: 0xb2c0bc, size: 0x2e8
    // 0xb2c0bc: EnterFrame
    //     0xb2c0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb2c0c0: mov             fp, SP
    // 0xb2c0c4: AllocStack(0x40)
    //     0xb2c0c4: sub             SP, SP, #0x40
    // 0xb2c0c8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xb2c0c8: mov             x0, x2
    //     0xb2c0cc: stur            x2, [fp, #-8]
    //     0xb2c0d0: stur            x3, [fp, #-0x10]
    // 0xb2c0d4: CheckStackOverflow
    //     0xb2c0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2c0d8: cmp             SP, x16
    //     0xb2c0dc: b.ls            #0xb2c39c
    // 0xb2c0e0: r1 = <Widget>
    //     0xb2c0e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2c0e4: r2 = 0
    //     0xb2c0e4: movz            x2, #0
    // 0xb2c0e8: r0 = _GrowableList()
    //     0xb2c0e8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb2c0ec: mov             x2, x0
    // 0xb2c0f0: ldur            x0, [fp, #-0x10]
    // 0xb2c0f4: stur            x2, [fp, #-0x18]
    // 0xb2c0f8: LoadField: r1 = r0->field_33
    //     0xb2c0f8: ldur            w1, [x0, #0x33]
    // 0xb2c0fc: DecompressPointer r1
    //     0xb2c0fc: add             x1, x1, HEAP, lsl #32
    // 0xb2c100: cmp             w1, NULL
    // 0xb2c104: b.eq            #0xb2c204
    // 0xb2c108: tbnz            w1, #4, #0xb2c204
    // 0xb2c10c: ldur            x1, [fp, #-8]
    // 0xb2c110: r0 = of()
    //     0xb2c110: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2c114: LoadField: r1 = r0->field_87
    //     0xb2c114: ldur            w1, [x0, #0x87]
    // 0xb2c118: DecompressPointer r1
    //     0xb2c118: add             x1, x1, HEAP, lsl #32
    // 0xb2c11c: LoadField: r0 = r1->field_7
    //     0xb2c11c: ldur            w0, [x1, #7]
    // 0xb2c120: DecompressPointer r0
    //     0xb2c120: add             x0, x0, HEAP, lsl #32
    // 0xb2c124: r16 = Instance_MaterialColor
    //     0xb2c124: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb2c128: ldr             x16, [x16, #0x180]
    // 0xb2c12c: r30 = 12.000000
    //     0xb2c12c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2c130: ldr             lr, [lr, #0x9e8]
    // 0xb2c134: stp             lr, x16, [SP]
    // 0xb2c138: mov             x1, x0
    // 0xb2c13c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb2c13c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb2c140: ldr             x4, [x4, #0x9b8]
    // 0xb2c144: r0 = copyWith()
    //     0xb2c144: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2c148: stur            x0, [fp, #-0x20]
    // 0xb2c14c: r0 = Text()
    //     0xb2c14c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2c150: mov             x1, x0
    // 0xb2c154: r0 = "SOLD OUT"
    //     0xb2c154: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc8] "SOLD OUT"
    //     0xb2c158: ldr             x0, [x0, #0xdc8]
    // 0xb2c15c: stur            x1, [fp, #-0x28]
    // 0xb2c160: StoreField: r1->field_b = r0
    //     0xb2c160: stur            w0, [x1, #0xb]
    // 0xb2c164: ldur            x0, [fp, #-0x20]
    // 0xb2c168: StoreField: r1->field_13 = r0
    //     0xb2c168: stur            w0, [x1, #0x13]
    // 0xb2c16c: r0 = Padding()
    //     0xb2c16c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2c170: mov             x2, x0
    // 0xb2c174: r0 = Instance_EdgeInsets
    //     0xb2c174: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb2c178: ldr             x0, [x0, #0x770]
    // 0xb2c17c: stur            x2, [fp, #-0x20]
    // 0xb2c180: StoreField: r2->field_f = r0
    //     0xb2c180: stur            w0, [x2, #0xf]
    // 0xb2c184: ldur            x1, [fp, #-0x28]
    // 0xb2c188: StoreField: r2->field_b = r1
    //     0xb2c188: stur            w1, [x2, #0xb]
    // 0xb2c18c: ldur            x3, [fp, #-0x18]
    // 0xb2c190: LoadField: r1 = r3->field_b
    //     0xb2c190: ldur            w1, [x3, #0xb]
    // 0xb2c194: LoadField: r4 = r3->field_f
    //     0xb2c194: ldur            w4, [x3, #0xf]
    // 0xb2c198: DecompressPointer r4
    //     0xb2c198: add             x4, x4, HEAP, lsl #32
    // 0xb2c19c: LoadField: r5 = r4->field_b
    //     0xb2c19c: ldur            w5, [x4, #0xb]
    // 0xb2c1a0: r4 = LoadInt32Instr(r1)
    //     0xb2c1a0: sbfx            x4, x1, #1, #0x1f
    // 0xb2c1a4: stur            x4, [fp, #-0x30]
    // 0xb2c1a8: r1 = LoadInt32Instr(r5)
    //     0xb2c1a8: sbfx            x1, x5, #1, #0x1f
    // 0xb2c1ac: cmp             x4, x1
    // 0xb2c1b0: b.ne            #0xb2c1bc
    // 0xb2c1b4: mov             x1, x3
    // 0xb2c1b8: r0 = _growToNextCapacity()
    //     0xb2c1b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2c1bc: ldur            x2, [fp, #-0x18]
    // 0xb2c1c0: ldur            x3, [fp, #-0x30]
    // 0xb2c1c4: add             x0, x3, #1
    // 0xb2c1c8: lsl             x1, x0, #1
    // 0xb2c1cc: StoreField: r2->field_b = r1
    //     0xb2c1cc: stur            w1, [x2, #0xb]
    // 0xb2c1d0: LoadField: r1 = r2->field_f
    //     0xb2c1d0: ldur            w1, [x2, #0xf]
    // 0xb2c1d4: DecompressPointer r1
    //     0xb2c1d4: add             x1, x1, HEAP, lsl #32
    // 0xb2c1d8: ldur            x0, [fp, #-0x20]
    // 0xb2c1dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2c1dc: add             x25, x1, x3, lsl #2
    //     0xb2c1e0: add             x25, x25, #0xf
    //     0xb2c1e4: str             w0, [x25]
    //     0xb2c1e8: tbz             w0, #0, #0xb2c204
    //     0xb2c1ec: ldurb           w16, [x1, #-1]
    //     0xb2c1f0: ldurb           w17, [x0, #-1]
    //     0xb2c1f4: and             x16, x17, x16, lsr #2
    //     0xb2c1f8: tst             x16, HEAP, lsr #32
    //     0xb2c1fc: b.eq            #0xb2c204
    //     0xb2c200: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2c204: ldur            x0, [fp, #-0x10]
    // 0xb2c208: LoadField: r1 = r0->field_63
    //     0xb2c208: ldur            w1, [x0, #0x63]
    // 0xb2c20c: DecompressPointer r1
    //     0xb2c20c: add             x1, x1, HEAP, lsl #32
    // 0xb2c210: cmp             w1, #2
    // 0xb2c214: b.ne            #0xb2c33c
    // 0xb2c218: LoadField: r1 = r0->field_5f
    //     0xb2c218: ldur            w1, [x0, #0x5f]
    // 0xb2c21c: DecompressPointer r1
    //     0xb2c21c: add             x1, x1, HEAP, lsl #32
    // 0xb2c220: cmp             w1, NULL
    // 0xb2c224: b.ne            #0xb2c230
    // 0xb2c228: r0 = 0
    //     0xb2c228: movz            x0, #0
    // 0xb2c22c: b               #0xb2c23c
    // 0xb2c230: r0 = LoadInt32Instr(r1)
    //     0xb2c230: sbfx            x0, x1, #1, #0x1f
    //     0xb2c234: tbz             w1, #0, #0xb2c23c
    //     0xb2c238: ldur            x0, [x1, #7]
    // 0xb2c23c: cmp             x0, #1
    // 0xb2c240: b.le            #0xb2c33c
    // 0xb2c244: ldur            x1, [fp, #-8]
    // 0xb2c248: r0 = of()
    //     0xb2c248: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2c24c: LoadField: r1 = r0->field_87
    //     0xb2c24c: ldur            w1, [x0, #0x87]
    // 0xb2c250: DecompressPointer r1
    //     0xb2c250: add             x1, x1, HEAP, lsl #32
    // 0xb2c254: LoadField: r0 = r1->field_7
    //     0xb2c254: ldur            w0, [x1, #7]
    // 0xb2c258: DecompressPointer r0
    //     0xb2c258: add             x0, x0, HEAP, lsl #32
    // 0xb2c25c: r16 = Instance_MaterialColor
    //     0xb2c25c: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb2c260: ldr             x16, [x16, #0x180]
    // 0xb2c264: r30 = 14.000000
    //     0xb2c264: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2c268: ldr             lr, [lr, #0x1d8]
    // 0xb2c26c: stp             lr, x16, [SP]
    // 0xb2c270: mov             x1, x0
    // 0xb2c274: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb2c274: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb2c278: ldr             x4, [x4, #0x9b8]
    // 0xb2c27c: r0 = copyWith()
    //     0xb2c27c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2c280: stur            x0, [fp, #-8]
    // 0xb2c284: r0 = Text()
    //     0xb2c284: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2c288: mov             x1, x0
    // 0xb2c28c: r0 = "Only 1 left!"
    //     0xb2c28c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54c00] "Only 1 left!"
    //     0xb2c290: ldr             x0, [x0, #0xc00]
    // 0xb2c294: stur            x1, [fp, #-0x10]
    // 0xb2c298: StoreField: r1->field_b = r0
    //     0xb2c298: stur            w0, [x1, #0xb]
    // 0xb2c29c: ldur            x0, [fp, #-8]
    // 0xb2c2a0: StoreField: r1->field_13 = r0
    //     0xb2c2a0: stur            w0, [x1, #0x13]
    // 0xb2c2a4: r0 = Padding()
    //     0xb2c2a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2c2a8: mov             x2, x0
    // 0xb2c2ac: r0 = Instance_EdgeInsets
    //     0xb2c2ac: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb2c2b0: ldr             x0, [x0, #0x770]
    // 0xb2c2b4: stur            x2, [fp, #-8]
    // 0xb2c2b8: StoreField: r2->field_f = r0
    //     0xb2c2b8: stur            w0, [x2, #0xf]
    // 0xb2c2bc: ldur            x0, [fp, #-0x10]
    // 0xb2c2c0: StoreField: r2->field_b = r0
    //     0xb2c2c0: stur            w0, [x2, #0xb]
    // 0xb2c2c4: ldur            x0, [fp, #-0x18]
    // 0xb2c2c8: LoadField: r1 = r0->field_b
    //     0xb2c2c8: ldur            w1, [x0, #0xb]
    // 0xb2c2cc: LoadField: r3 = r0->field_f
    //     0xb2c2cc: ldur            w3, [x0, #0xf]
    // 0xb2c2d0: DecompressPointer r3
    //     0xb2c2d0: add             x3, x3, HEAP, lsl #32
    // 0xb2c2d4: LoadField: r4 = r3->field_b
    //     0xb2c2d4: ldur            w4, [x3, #0xb]
    // 0xb2c2d8: r3 = LoadInt32Instr(r1)
    //     0xb2c2d8: sbfx            x3, x1, #1, #0x1f
    // 0xb2c2dc: stur            x3, [fp, #-0x30]
    // 0xb2c2e0: r1 = LoadInt32Instr(r4)
    //     0xb2c2e0: sbfx            x1, x4, #1, #0x1f
    // 0xb2c2e4: cmp             x3, x1
    // 0xb2c2e8: b.ne            #0xb2c2f4
    // 0xb2c2ec: mov             x1, x0
    // 0xb2c2f0: r0 = _growToNextCapacity()
    //     0xb2c2f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2c2f4: ldur            x2, [fp, #-0x18]
    // 0xb2c2f8: ldur            x3, [fp, #-0x30]
    // 0xb2c2fc: add             x0, x3, #1
    // 0xb2c300: lsl             x1, x0, #1
    // 0xb2c304: StoreField: r2->field_b = r1
    //     0xb2c304: stur            w1, [x2, #0xb]
    // 0xb2c308: LoadField: r1 = r2->field_f
    //     0xb2c308: ldur            w1, [x2, #0xf]
    // 0xb2c30c: DecompressPointer r1
    //     0xb2c30c: add             x1, x1, HEAP, lsl #32
    // 0xb2c310: ldur            x0, [fp, #-8]
    // 0xb2c314: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2c314: add             x25, x1, x3, lsl #2
    //     0xb2c318: add             x25, x25, #0xf
    //     0xb2c31c: str             w0, [x25]
    //     0xb2c320: tbz             w0, #0, #0xb2c33c
    //     0xb2c324: ldurb           w16, [x1, #-1]
    //     0xb2c328: ldurb           w17, [x0, #-1]
    //     0xb2c32c: and             x16, x17, x16, lsr #2
    //     0xb2c330: tst             x16, HEAP, lsr #32
    //     0xb2c334: b.eq            #0xb2c33c
    //     0xb2c338: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2c33c: r0 = Column()
    //     0xb2c33c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2c340: r1 = Instance_Axis
    //     0xb2c340: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2c344: StoreField: r0->field_f = r1
    //     0xb2c344: stur            w1, [x0, #0xf]
    // 0xb2c348: r1 = Instance_MainAxisAlignment
    //     0xb2c348: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2c34c: ldr             x1, [x1, #0xa08]
    // 0xb2c350: StoreField: r0->field_13 = r1
    //     0xb2c350: stur            w1, [x0, #0x13]
    // 0xb2c354: r1 = Instance_MainAxisSize
    //     0xb2c354: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2c358: ldr             x1, [x1, #0xa10]
    // 0xb2c35c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2c35c: stur            w1, [x0, #0x17]
    // 0xb2c360: r1 = Instance_CrossAxisAlignment
    //     0xb2c360: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2c364: ldr             x1, [x1, #0x890]
    // 0xb2c368: StoreField: r0->field_1b = r1
    //     0xb2c368: stur            w1, [x0, #0x1b]
    // 0xb2c36c: r1 = Instance_VerticalDirection
    //     0xb2c36c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2c370: ldr             x1, [x1, #0xa20]
    // 0xb2c374: StoreField: r0->field_23 = r1
    //     0xb2c374: stur            w1, [x0, #0x23]
    // 0xb2c378: r1 = Instance_Clip
    //     0xb2c378: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2c37c: ldr             x1, [x1, #0x38]
    // 0xb2c380: StoreField: r0->field_2b = r1
    //     0xb2c380: stur            w1, [x0, #0x2b]
    // 0xb2c384: StoreField: r0->field_2f = rZR
    //     0xb2c384: stur            xzr, [x0, #0x2f]
    // 0xb2c388: ldur            x1, [fp, #-0x18]
    // 0xb2c38c: StoreField: r0->field_b = r1
    //     0xb2c38c: stur            w1, [x0, #0xb]
    // 0xb2c390: LeaveFrame
    //     0xb2c390: mov             SP, fp
    //     0xb2c394: ldp             fp, lr, [SP], #0x10
    // 0xb2c398: ret
    //     0xb2c398: ret             
    // 0xb2c39c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2c39c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2c3a0: b               #0xb2c0e0
  }
  _ _buildCatalogueImage(/* No info */) {
    // ** addr: 0xb2c3a4, size: 0x418
    // 0xb2c3a4: EnterFrame
    //     0xb2c3a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb2c3a8: mov             fp, SP
    // 0xb2c3ac: AllocStack(0x60)
    //     0xb2c3ac: sub             SP, SP, #0x60
    // 0xb2c3b0: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xb2c3b0: mov             x0, x1
    //     0xb2c3b4: stur            x1, [fp, #-8]
    //     0xb2c3b8: mov             x1, x2
    //     0xb2c3bc: stur            x2, [fp, #-0x10]
    //     0xb2c3c0: mov             x2, x3
    //     0xb2c3c4: stur            x3, [fp, #-0x18]
    // 0xb2c3c8: CheckStackOverflow
    //     0xb2c3c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2c3cc: cmp             SP, x16
    //     0xb2c3d0: b.ls            #0xb2c7a4
    // 0xb2c3d4: r1 = 2
    //     0xb2c3d4: movz            x1, #0x2
    // 0xb2c3d8: r0 = AllocateContext()
    //     0xb2c3d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2c3dc: mov             x3, x0
    // 0xb2c3e0: ldur            x2, [fp, #-8]
    // 0xb2c3e4: stur            x3, [fp, #-0x30]
    // 0xb2c3e8: StoreField: r3->field_f = r2
    //     0xb2c3e8: stur            w2, [x3, #0xf]
    // 0xb2c3ec: LoadField: r4 = r2->field_b
    //     0xb2c3ec: ldur            w4, [x2, #0xb]
    // 0xb2c3f0: DecompressPointer r4
    //     0xb2c3f0: add             x4, x4, HEAP, lsl #32
    // 0xb2c3f4: cmp             w4, NULL
    // 0xb2c3f8: b.eq            #0xb2c7ac
    // 0xb2c3fc: LoadField: r0 = r4->field_b
    //     0xb2c3fc: ldur            w0, [x4, #0xb]
    // 0xb2c400: DecompressPointer r0
    //     0xb2c400: add             x0, x0, HEAP, lsl #32
    // 0xb2c404: cmp             w0, NULL
    // 0xb2c408: b.eq            #0xb2c7b0
    // 0xb2c40c: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xb2c40c: ldur            w5, [x0, #0x17]
    // 0xb2c410: DecompressPointer r5
    //     0xb2c410: add             x5, x5, HEAP, lsl #32
    // 0xb2c414: LoadField: r0 = r5->field_b
    //     0xb2c414: ldur            w0, [x5, #0xb]
    // 0xb2c418: r1 = LoadInt32Instr(r0)
    //     0xb2c418: sbfx            x1, x0, #1, #0x1f
    // 0xb2c41c: mov             x0, x1
    // 0xb2c420: ldur            x1, [fp, #-0x10]
    // 0xb2c424: cmp             x1, x0
    // 0xb2c428: b.hs            #0xb2c7b4
    // 0xb2c42c: LoadField: r0 = r5->field_f
    //     0xb2c42c: ldur            w0, [x5, #0xf]
    // 0xb2c430: DecompressPointer r0
    //     0xb2c430: add             x0, x0, HEAP, lsl #32
    // 0xb2c434: ldur            x1, [fp, #-0x10]
    // 0xb2c438: ArrayLoad: r5 = r0[r1]  ; Unknown_4
    //     0xb2c438: add             x16, x0, x1, lsl #2
    //     0xb2c43c: ldur            w5, [x16, #0xf]
    // 0xb2c440: DecompressPointer r5
    //     0xb2c440: add             x5, x5, HEAP, lsl #32
    // 0xb2c444: stur            x5, [fp, #-0x28]
    // 0xb2c448: StoreField: r3->field_13 = r5
    //     0xb2c448: stur            w5, [x3, #0x13]
    // 0xb2c44c: LoadField: r0 = r4->field_2f
    //     0xb2c44c: ldur            w0, [x4, #0x2f]
    // 0xb2c450: DecompressPointer r0
    //     0xb2c450: add             x0, x0, HEAP, lsl #32
    // 0xb2c454: LoadField: r1 = r0->field_7
    //     0xb2c454: ldur            w1, [x0, #7]
    // 0xb2c458: cbz             w1, #0xb2c47c
    // 0xb2c45c: LoadField: r0 = r4->field_33
    //     0xb2c45c: ldur            w0, [x4, #0x33]
    // 0xb2c460: DecompressPointer r0
    //     0xb2c460: add             x0, x0, HEAP, lsl #32
    // 0xb2c464: LoadField: r1 = r0->field_7
    //     0xb2c464: ldur            w1, [x0, #7]
    // 0xb2c468: cbnz            w1, #0xb2c474
    // 0xb2c46c: r0 = false
    //     0xb2c46c: add             x0, NULL, #0x30  ; false
    // 0xb2c470: b               #0xb2c478
    // 0xb2c474: r0 = true
    //     0xb2c474: add             x0, NULL, #0x20  ; true
    // 0xb2c478: b               #0xb2c480
    // 0xb2c47c: r0 = false
    //     0xb2c47c: add             x0, NULL, #0x30  ; false
    // 0xb2c480: stur            x0, [fp, #-0x20]
    // 0xb2c484: r0 = CachedNetworkImage()
    //     0xb2c484: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb2c488: r1 = Function '<anonymous closure>':.
    //     0xb2c488: add             x1, PP, #0x57, lsl #12  ; [pp+0x572c0] AnonymousClosure: (0xb2c868), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueImage (0xb2c3a4)
    //     0xb2c48c: ldr             x1, [x1, #0x2c0]
    // 0xb2c490: r2 = Null
    //     0xb2c490: mov             x2, NULL
    // 0xb2c494: stur            x0, [fp, #-0x38]
    // 0xb2c498: r0 = AllocateClosure()
    //     0xb2c498: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2c49c: r1 = Function '<anonymous closure>':.
    //     0xb2c49c: add             x1, PP, #0x57, lsl #12  ; [pp+0x572c8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb2c4a0: ldr             x1, [x1, #0x2c8]
    // 0xb2c4a4: r2 = Null
    //     0xb2c4a4: mov             x2, NULL
    // 0xb2c4a8: stur            x0, [fp, #-0x40]
    // 0xb2c4ac: r0 = AllocateClosure()
    //     0xb2c4ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2c4b0: r1 = Function '<anonymous closure>':.
    //     0xb2c4b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x572d0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb2c4b4: ldr             x1, [x1, #0x2d0]
    // 0xb2c4b8: r2 = Null
    //     0xb2c4b8: mov             x2, NULL
    // 0xb2c4bc: stur            x0, [fp, #-0x48]
    // 0xb2c4c0: r0 = AllocateClosure()
    //     0xb2c4c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2c4c4: ldur            x16, [fp, #-0x40]
    // 0xb2c4c8: ldur            lr, [fp, #-0x48]
    // 0xb2c4cc: stp             lr, x16, [SP, #8]
    // 0xb2c4d0: str             x0, [SP]
    // 0xb2c4d4: ldur            x1, [fp, #-0x38]
    // 0xb2c4d8: ldur            x2, [fp, #-0x18]
    // 0xb2c4dc: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, imageBuilder, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb2c4dc: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f428] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "imageBuilder", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb2c4e0: ldr             x4, [x4, #0x428]
    // 0xb2c4e4: r0 = CachedNetworkImage()
    //     0xb2c4e4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb2c4e8: r1 = Null
    //     0xb2c4e8: mov             x1, NULL
    // 0xb2c4ec: r2 = 2
    //     0xb2c4ec: movz            x2, #0x2
    // 0xb2c4f0: r0 = AllocateArray()
    //     0xb2c4f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2c4f4: mov             x2, x0
    // 0xb2c4f8: ldur            x0, [fp, #-0x38]
    // 0xb2c4fc: stur            x2, [fp, #-0x18]
    // 0xb2c500: StoreField: r2->field_f = r0
    //     0xb2c500: stur            w0, [x2, #0xf]
    // 0xb2c504: r1 = <Widget>
    //     0xb2c504: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2c508: r0 = AllocateGrowableArray()
    //     0xb2c508: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2c50c: mov             x2, x0
    // 0xb2c510: ldur            x0, [fp, #-0x18]
    // 0xb2c514: stur            x2, [fp, #-0x38]
    // 0xb2c518: StoreField: r2->field_f = r0
    //     0xb2c518: stur            w0, [x2, #0xf]
    // 0xb2c51c: r0 = 2
    //     0xb2c51c: movz            x0, #0x2
    // 0xb2c520: StoreField: r2->field_b = r0
    //     0xb2c520: stur            w0, [x2, #0xb]
    // 0xb2c524: ldur            x0, [fp, #-0x20]
    // 0xb2c528: tbnz            w0, #4, #0xb2c768
    // 0xb2c52c: ldur            x0, [fp, #-0x28]
    // 0xb2c530: LoadField: r1 = r0->field_33
    //     0xb2c530: ldur            w1, [x0, #0x33]
    // 0xb2c534: DecompressPointer r1
    //     0xb2c534: add             x1, x1, HEAP, lsl #32
    // 0xb2c538: cmp             w1, NULL
    // 0xb2c53c: b.eq            #0xb2c608
    // 0xb2c540: tbnz            w1, #4, #0xb2c608
    // 0xb2c544: r1 = Instance_Color
    //     0xb2c544: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2c548: d0 = 0.200000
    //     0xb2c548: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb2c54c: r0 = withOpacity()
    //     0xb2c54c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2c550: stur            x0, [fp, #-0x18]
    // 0xb2c554: r0 = Radius()
    //     0xb2c554: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2c558: d0 = 2.000000
    //     0xb2c558: fmov            d0, #2.00000000
    // 0xb2c55c: stur            x0, [fp, #-0x20]
    // 0xb2c560: StoreField: r0->field_7 = d0
    //     0xb2c560: stur            d0, [x0, #7]
    // 0xb2c564: StoreField: r0->field_f = d0
    //     0xb2c564: stur            d0, [x0, #0xf]
    // 0xb2c568: r0 = BorderRadius()
    //     0xb2c568: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2c56c: mov             x1, x0
    // 0xb2c570: ldur            x0, [fp, #-0x20]
    // 0xb2c574: stur            x1, [fp, #-0x40]
    // 0xb2c578: StoreField: r1->field_7 = r0
    //     0xb2c578: stur            w0, [x1, #7]
    // 0xb2c57c: StoreField: r1->field_b = r0
    //     0xb2c57c: stur            w0, [x1, #0xb]
    // 0xb2c580: StoreField: r1->field_f = r0
    //     0xb2c580: stur            w0, [x1, #0xf]
    // 0xb2c584: StoreField: r1->field_13 = r0
    //     0xb2c584: stur            w0, [x1, #0x13]
    // 0xb2c588: r0 = BoxDecoration()
    //     0xb2c588: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2c58c: mov             x1, x0
    // 0xb2c590: ldur            x0, [fp, #-0x18]
    // 0xb2c594: stur            x1, [fp, #-0x20]
    // 0xb2c598: StoreField: r1->field_7 = r0
    //     0xb2c598: stur            w0, [x1, #7]
    // 0xb2c59c: ldur            x0, [fp, #-0x40]
    // 0xb2c5a0: StoreField: r1->field_13 = r0
    //     0xb2c5a0: stur            w0, [x1, #0x13]
    // 0xb2c5a4: r0 = Instance_BoxShape
    //     0xb2c5a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2c5a8: ldr             x0, [x0, #0x80]
    // 0xb2c5ac: StoreField: r1->field_23 = r0
    //     0xb2c5ac: stur            w0, [x1, #0x23]
    // 0xb2c5b0: r0 = Container()
    //     0xb2c5b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2c5b4: stur            x0, [fp, #-0x18]
    // 0xb2c5b8: r16 = 20.000000
    //     0xb2c5b8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb2c5bc: ldr             x16, [x16, #0xac8]
    // 0xb2c5c0: r30 = 20.000000
    //     0xb2c5c0: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb2c5c4: ldr             lr, [lr, #0xac8]
    // 0xb2c5c8: stp             lr, x16, [SP, #8]
    // 0xb2c5cc: ldur            x16, [fp, #-0x20]
    // 0xb2c5d0: str             x16, [SP]
    // 0xb2c5d4: mov             x1, x0
    // 0xb2c5d8: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb2c5d8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb2c5dc: ldr             x4, [x4, #0x468]
    // 0xb2c5e0: r0 = Container()
    //     0xb2c5e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2c5e4: r0 = Padding()
    //     0xb2c5e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2c5e8: mov             x1, x0
    // 0xb2c5ec: r0 = Instance_EdgeInsets
    //     0xb2c5ec: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb2c5f0: ldr             x0, [x0, #0x850]
    // 0xb2c5f4: StoreField: r1->field_f = r0
    //     0xb2c5f4: stur            w0, [x1, #0xf]
    // 0xb2c5f8: ldur            x0, [fp, #-0x18]
    // 0xb2c5fc: StoreField: r1->field_b = r0
    //     0xb2c5fc: stur            w0, [x1, #0xb]
    // 0xb2c600: mov             x0, x1
    // 0xb2c604: b               #0xb2c674
    // 0xb2c608: ldur            x1, [fp, #-8]
    // 0xb2c60c: LoadField: r2 = r1->field_b
    //     0xb2c60c: ldur            w2, [x1, #0xb]
    // 0xb2c610: DecompressPointer r2
    //     0xb2c610: add             x2, x2, HEAP, lsl #32
    // 0xb2c614: cmp             w2, NULL
    // 0xb2c618: b.eq            #0xb2c7b8
    // 0xb2c61c: LoadField: r1 = r2->field_37
    //     0xb2c61c: ldur            w1, [x2, #0x37]
    // 0xb2c620: DecompressPointer r1
    //     0xb2c620: add             x1, x1, HEAP, lsl #32
    // 0xb2c624: LoadField: r2 = r0->field_7
    //     0xb2c624: ldur            w2, [x0, #7]
    // 0xb2c628: DecompressPointer r2
    //     0xb2c628: add             x2, x2, HEAP, lsl #32
    // 0xb2c62c: r0 = LoadClassIdInstr(r1)
    //     0xb2c62c: ldur            x0, [x1, #-1]
    //     0xb2c630: ubfx            x0, x0, #0xc, #0x14
    // 0xb2c634: stp             x2, x1, [SP]
    // 0xb2c638: mov             lr, x0
    // 0xb2c63c: ldr             lr, [x21, lr, lsl #3]
    // 0xb2c640: blr             lr
    // 0xb2c644: tbnz            w0, #4, #0xb2c654
    // 0xb2c648: r0 = Instance_IconData
    //     0xb2c648: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0xb2c64c: ldr             x0, [x0, #0xc38]
    // 0xb2c650: b               #0xb2c65c
    // 0xb2c654: r0 = Instance_IconData
    //     0xb2c654: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0xb2c658: ldr             x0, [x0, #0xc30]
    // 0xb2c65c: stur            x0, [fp, #-8]
    // 0xb2c660: r0 = Icon()
    //     0xb2c660: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb2c664: mov             x1, x0
    // 0xb2c668: ldur            x0, [fp, #-8]
    // 0xb2c66c: StoreField: r1->field_b = r0
    //     0xb2c66c: stur            w0, [x1, #0xb]
    // 0xb2c670: mov             x0, x1
    // 0xb2c674: ldur            x1, [fp, #-0x38]
    // 0xb2c678: stur            x0, [fp, #-8]
    // 0xb2c67c: r0 = InkWell()
    //     0xb2c67c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2c680: mov             x3, x0
    // 0xb2c684: ldur            x0, [fp, #-8]
    // 0xb2c688: stur            x3, [fp, #-0x18]
    // 0xb2c68c: StoreField: r3->field_b = r0
    //     0xb2c68c: stur            w0, [x3, #0xb]
    // 0xb2c690: ldur            x2, [fp, #-0x30]
    // 0xb2c694: r1 = Function '<anonymous closure>':.
    //     0xb2c694: add             x1, PP, #0x57, lsl #12  ; [pp+0x572d8] AnonymousClosure: (0xb2c7bc), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildCatalogueImage (0xb2c3a4)
    //     0xb2c698: ldr             x1, [x1, #0x2d8]
    // 0xb2c69c: r0 = AllocateClosure()
    //     0xb2c69c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2c6a0: mov             x1, x0
    // 0xb2c6a4: ldur            x0, [fp, #-0x18]
    // 0xb2c6a8: StoreField: r0->field_f = r1
    //     0xb2c6a8: stur            w1, [x0, #0xf]
    // 0xb2c6ac: r1 = true
    //     0xb2c6ac: add             x1, NULL, #0x20  ; true
    // 0xb2c6b0: StoreField: r0->field_43 = r1
    //     0xb2c6b0: stur            w1, [x0, #0x43]
    // 0xb2c6b4: r2 = Instance_BoxShape
    //     0xb2c6b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2c6b8: ldr             x2, [x2, #0x80]
    // 0xb2c6bc: StoreField: r0->field_47 = r2
    //     0xb2c6bc: stur            w2, [x0, #0x47]
    // 0xb2c6c0: StoreField: r0->field_6f = r1
    //     0xb2c6c0: stur            w1, [x0, #0x6f]
    // 0xb2c6c4: r2 = false
    //     0xb2c6c4: add             x2, NULL, #0x30  ; false
    // 0xb2c6c8: StoreField: r0->field_73 = r2
    //     0xb2c6c8: stur            w2, [x0, #0x73]
    // 0xb2c6cc: StoreField: r0->field_83 = r1
    //     0xb2c6cc: stur            w1, [x0, #0x83]
    // 0xb2c6d0: StoreField: r0->field_7b = r2
    //     0xb2c6d0: stur            w2, [x0, #0x7b]
    // 0xb2c6d4: r1 = <StackParentData>
    //     0xb2c6d4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb2c6d8: ldr             x1, [x1, #0x8e0]
    // 0xb2c6dc: r0 = Positioned()
    //     0xb2c6dc: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb2c6e0: mov             x2, x0
    // 0xb2c6e4: ldur            x0, [fp, #-0x18]
    // 0xb2c6e8: stur            x2, [fp, #-8]
    // 0xb2c6ec: StoreField: r2->field_b = r0
    //     0xb2c6ec: stur            w0, [x2, #0xb]
    // 0xb2c6f0: ldur            x0, [fp, #-0x38]
    // 0xb2c6f4: LoadField: r1 = r0->field_b
    //     0xb2c6f4: ldur            w1, [x0, #0xb]
    // 0xb2c6f8: LoadField: r3 = r0->field_f
    //     0xb2c6f8: ldur            w3, [x0, #0xf]
    // 0xb2c6fc: DecompressPointer r3
    //     0xb2c6fc: add             x3, x3, HEAP, lsl #32
    // 0xb2c700: LoadField: r4 = r3->field_b
    //     0xb2c700: ldur            w4, [x3, #0xb]
    // 0xb2c704: r3 = LoadInt32Instr(r1)
    //     0xb2c704: sbfx            x3, x1, #1, #0x1f
    // 0xb2c708: stur            x3, [fp, #-0x10]
    // 0xb2c70c: r1 = LoadInt32Instr(r4)
    //     0xb2c70c: sbfx            x1, x4, #1, #0x1f
    // 0xb2c710: cmp             x3, x1
    // 0xb2c714: b.ne            #0xb2c720
    // 0xb2c718: mov             x1, x0
    // 0xb2c71c: r0 = _growToNextCapacity()
    //     0xb2c71c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2c720: ldur            x2, [fp, #-0x38]
    // 0xb2c724: ldur            x3, [fp, #-0x10]
    // 0xb2c728: add             x0, x3, #1
    // 0xb2c72c: lsl             x1, x0, #1
    // 0xb2c730: StoreField: r2->field_b = r1
    //     0xb2c730: stur            w1, [x2, #0xb]
    // 0xb2c734: LoadField: r1 = r2->field_f
    //     0xb2c734: ldur            w1, [x2, #0xf]
    // 0xb2c738: DecompressPointer r1
    //     0xb2c738: add             x1, x1, HEAP, lsl #32
    // 0xb2c73c: ldur            x0, [fp, #-8]
    // 0xb2c740: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2c740: add             x25, x1, x3, lsl #2
    //     0xb2c744: add             x25, x25, #0xf
    //     0xb2c748: str             w0, [x25]
    //     0xb2c74c: tbz             w0, #0, #0xb2c768
    //     0xb2c750: ldurb           w16, [x1, #-1]
    //     0xb2c754: ldurb           w17, [x0, #-1]
    //     0xb2c758: and             x16, x17, x16, lsr #2
    //     0xb2c75c: tst             x16, HEAP, lsr #32
    //     0xb2c760: b.eq            #0xb2c768
    //     0xb2c764: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2c768: r0 = Stack()
    //     0xb2c768: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb2c76c: r1 = Instance_AlignmentDirectional
    //     0xb2c76c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb2c770: ldr             x1, [x1, #0xd08]
    // 0xb2c774: StoreField: r0->field_f = r1
    //     0xb2c774: stur            w1, [x0, #0xf]
    // 0xb2c778: r1 = Instance_StackFit
    //     0xb2c778: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb2c77c: ldr             x1, [x1, #0xfa8]
    // 0xb2c780: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2c780: stur            w1, [x0, #0x17]
    // 0xb2c784: r1 = Instance_Clip
    //     0xb2c784: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb2c788: ldr             x1, [x1, #0x7e0]
    // 0xb2c78c: StoreField: r0->field_1b = r1
    //     0xb2c78c: stur            w1, [x0, #0x1b]
    // 0xb2c790: ldur            x1, [fp, #-0x38]
    // 0xb2c794: StoreField: r0->field_b = r1
    //     0xb2c794: stur            w1, [x0, #0xb]
    // 0xb2c798: LeaveFrame
    //     0xb2c798: mov             SP, fp
    //     0xb2c79c: ldp             fp, lr, [SP], #0x10
    // 0xb2c7a0: ret
    //     0xb2c7a0: ret             
    // 0xb2c7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2c7a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2c7a8: b               #0xb2c3d4
    // 0xb2c7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2c7ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2c7b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2c7b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2c7b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2c7b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2c7b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2c7b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2c7bc, size: 0xac
    // 0xb2c7bc: EnterFrame
    //     0xb2c7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb2c7c0: mov             fp, SP
    // 0xb2c7c4: AllocStack(0x10)
    //     0xb2c7c4: sub             SP, SP, #0x10
    // 0xb2c7c8: SetupParameters()
    //     0xb2c7c8: ldr             x0, [fp, #0x10]
    //     0xb2c7cc: ldur            w1, [x0, #0x17]
    //     0xb2c7d0: add             x1, x1, HEAP, lsl #32
    // 0xb2c7d4: CheckStackOverflow
    //     0xb2c7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2c7d8: cmp             SP, x16
    //     0xb2c7dc: b.ls            #0xb2c85c
    // 0xb2c7e0: LoadField: r0 = r1->field_13
    //     0xb2c7e0: ldur            w0, [x1, #0x13]
    // 0xb2c7e4: DecompressPointer r0
    //     0xb2c7e4: add             x0, x0, HEAP, lsl #32
    // 0xb2c7e8: LoadField: r2 = r0->field_33
    //     0xb2c7e8: ldur            w2, [x0, #0x33]
    // 0xb2c7ec: DecompressPointer r2
    //     0xb2c7ec: add             x2, x2, HEAP, lsl #32
    // 0xb2c7f0: cmp             w2, NULL
    // 0xb2c7f4: b.eq            #0xb2c7fc
    // 0xb2c7f8: tbz             w2, #4, #0xb2c84c
    // 0xb2c7fc: LoadField: r2 = r1->field_f
    //     0xb2c7fc: ldur            w2, [x1, #0xf]
    // 0xb2c800: DecompressPointer r2
    //     0xb2c800: add             x2, x2, HEAP, lsl #32
    // 0xb2c804: LoadField: r1 = r2->field_b
    //     0xb2c804: ldur            w1, [x2, #0xb]
    // 0xb2c808: DecompressPointer r1
    //     0xb2c808: add             x1, x1, HEAP, lsl #32
    // 0xb2c80c: cmp             w1, NULL
    // 0xb2c810: b.eq            #0xb2c864
    // 0xb2c814: LoadField: r2 = r0->field_7
    //     0xb2c814: ldur            w2, [x0, #7]
    // 0xb2c818: DecompressPointer r2
    //     0xb2c818: add             x2, x2, HEAP, lsl #32
    // 0xb2c81c: cmp             w2, NULL
    // 0xb2c820: b.ne            #0xb2c82c
    // 0xb2c824: r0 = ""
    //     0xb2c824: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2c828: b               #0xb2c830
    // 0xb2c82c: mov             x0, x2
    // 0xb2c830: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2c830: ldur            w2, [x1, #0x17]
    // 0xb2c834: DecompressPointer r2
    //     0xb2c834: add             x2, x2, HEAP, lsl #32
    // 0xb2c838: stp             x0, x2, [SP]
    // 0xb2c83c: mov             x0, x2
    // 0xb2c840: ClosureCall
    //     0xb2c840: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb2c844: ldur            x2, [x0, #0x1f]
    //     0xb2c848: blr             x2
    // 0xb2c84c: r0 = Null
    //     0xb2c84c: mov             x0, NULL
    // 0xb2c850: LeaveFrame
    //     0xb2c850: mov             SP, fp
    //     0xb2c854: ldp             fp, lr, [SP], #0x10
    // 0xb2c858: ret
    //     0xb2c858: ret             
    // 0xb2c85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2c85c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2c860: b               #0xb2c7e0
    // 0xb2c864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2c864: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, ImageProvider<Object>) {
    // ** addr: 0xb2c868, size: 0xf0
    // 0xb2c868: EnterFrame
    //     0xb2c868: stp             fp, lr, [SP, #-0x10]!
    //     0xb2c86c: mov             fp, SP
    // 0xb2c870: AllocStack(0x28)
    //     0xb2c870: sub             SP, SP, #0x28
    // 0xb2c874: CheckStackOverflow
    //     0xb2c874: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2c878: cmp             SP, x16
    //     0xb2c87c: b.ls            #0xb2c950
    // 0xb2c880: r0 = DecorationImage()
    //     0xb2c880: bl              #0x83fce0  ; AllocateDecorationImageStub -> DecorationImage (size=0x44)
    // 0xb2c884: mov             x1, x0
    // 0xb2c888: ldr             x0, [fp, #0x10]
    // 0xb2c88c: stur            x1, [fp, #-8]
    // 0xb2c890: StoreField: r1->field_7 = r0
    //     0xb2c890: stur            w0, [x1, #7]
    // 0xb2c894: r0 = Instance_BoxFit
    //     0xb2c894: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb2c898: ldr             x0, [x0, #0x118]
    // 0xb2c89c: StoreField: r1->field_13 = r0
    //     0xb2c89c: stur            w0, [x1, #0x13]
    // 0xb2c8a0: r0 = Instance_Alignment
    //     0xb2c8a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb2c8a4: ldr             x0, [x0, #0xb10]
    // 0xb2c8a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2c8a8: stur            w0, [x1, #0x17]
    // 0xb2c8ac: r0 = Instance_ImageRepeat
    //     0xb2c8ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb00] Obj!ImageRepeat@d73821
    //     0xb2c8b0: ldr             x0, [x0, #0xb00]
    // 0xb2c8b4: StoreField: r1->field_1f = r0
    //     0xb2c8b4: stur            w0, [x1, #0x1f]
    // 0xb2c8b8: r0 = false
    //     0xb2c8b8: add             x0, NULL, #0x30  ; false
    // 0xb2c8bc: StoreField: r1->field_23 = r0
    //     0xb2c8bc: stur            w0, [x1, #0x23]
    // 0xb2c8c0: d0 = 1.000000
    //     0xb2c8c0: fmov            d0, #1.00000000
    // 0xb2c8c4: StoreField: r1->field_27 = d0
    //     0xb2c8c4: stur            d0, [x1, #0x27]
    // 0xb2c8c8: StoreField: r1->field_2f = d0
    //     0xb2c8c8: stur            d0, [x1, #0x2f]
    // 0xb2c8cc: r2 = Instance_FilterQuality
    //     0xb2c8cc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33a98] Obj!FilterQuality@d77121
    //     0xb2c8d0: ldr             x2, [x2, #0xa98]
    // 0xb2c8d4: StoreField: r1->field_37 = r2
    //     0xb2c8d4: stur            w2, [x1, #0x37]
    // 0xb2c8d8: StoreField: r1->field_3b = r0
    //     0xb2c8d8: stur            w0, [x1, #0x3b]
    // 0xb2c8dc: StoreField: r1->field_3f = r0
    //     0xb2c8dc: stur            w0, [x1, #0x3f]
    // 0xb2c8e0: r0 = BoxDecoration()
    //     0xb2c8e0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2c8e4: mov             x1, x0
    // 0xb2c8e8: ldur            x0, [fp, #-8]
    // 0xb2c8ec: stur            x1, [fp, #-0x10]
    // 0xb2c8f0: StoreField: r1->field_b = r0
    //     0xb2c8f0: stur            w0, [x1, #0xb]
    // 0xb2c8f4: r0 = Instance_BorderRadius
    //     0xb2c8f4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb2c8f8: ldr             x0, [x0, #0x460]
    // 0xb2c8fc: StoreField: r1->field_13 = r0
    //     0xb2c8fc: stur            w0, [x1, #0x13]
    // 0xb2c900: r0 = Instance_BoxShape
    //     0xb2c900: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2c904: ldr             x0, [x0, #0x80]
    // 0xb2c908: StoreField: r1->field_23 = r0
    //     0xb2c908: stur            w0, [x1, #0x23]
    // 0xb2c90c: r0 = Container()
    //     0xb2c90c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2c910: stur            x0, [fp, #-8]
    // 0xb2c914: r16 = 75.000000
    //     0xb2c914: add             x16, PP, #0x57, lsl #12  ; [pp+0x572e0] 75
    //     0xb2c918: ldr             x16, [x16, #0x2e0]
    // 0xb2c91c: r30 = 75.000000
    //     0xb2c91c: add             lr, PP, #0x57, lsl #12  ; [pp+0x572e0] 75
    //     0xb2c920: ldr             lr, [lr, #0x2e0]
    // 0xb2c924: stp             lr, x16, [SP, #8]
    // 0xb2c928: ldur            x16, [fp, #-0x10]
    // 0xb2c92c: str             x16, [SP]
    // 0xb2c930: mov             x1, x0
    // 0xb2c934: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb2c934: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb2c938: ldr             x4, [x4, #0x468]
    // 0xb2c93c: r0 = Container()
    //     0xb2c93c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2c940: ldur            x0, [fp, #-8]
    // 0xb2c944: LeaveFrame
    //     0xb2c944: mov             SP, fp
    //     0xb2c948: ldp             fp, lr, [SP], #0x10
    // 0xb2c94c: ret
    //     0xb2c94c: ret             
    // 0xb2c950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2c950: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2c954: b               #0xb2c880
  }
  _ _buildFreeGiftSection(/* No info */) {
    // ** addr: 0xb2c980, size: 0x1f0
    // 0xb2c980: EnterFrame
    //     0xb2c980: stp             fp, lr, [SP, #-0x10]!
    //     0xb2c984: mov             fp, SP
    // 0xb2c988: AllocStack(0x50)
    //     0xb2c988: sub             SP, SP, #0x50
    // 0xb2c98c: SetupParameters(_BagItemViewWidgetState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xb2c98c: mov             x0, x2
    //     0xb2c990: stur            x2, [fp, #-0x18]
    //     0xb2c994: mov             x2, x1
    //     0xb2c998: stur            x1, [fp, #-0x10]
    // 0xb2c99c: CheckStackOverflow
    //     0xb2c99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2c9a0: cmp             SP, x16
    //     0xb2c9a4: b.ls            #0xb2cb64
    // 0xb2c9a8: LoadField: r1 = r2->field_b
    //     0xb2c9a8: ldur            w1, [x2, #0xb]
    // 0xb2c9ac: DecompressPointer r1
    //     0xb2c9ac: add             x1, x1, HEAP, lsl #32
    // 0xb2c9b0: cmp             w1, NULL
    // 0xb2c9b4: b.eq            #0xb2cb6c
    // 0xb2c9b8: LoadField: r3 = r1->field_b
    //     0xb2c9b8: ldur            w3, [x1, #0xb]
    // 0xb2c9bc: DecompressPointer r3
    //     0xb2c9bc: add             x3, x3, HEAP, lsl #32
    // 0xb2c9c0: cmp             w3, NULL
    // 0xb2c9c4: b.eq            #0xb2c9d8
    // 0xb2c9c8: LoadField: r1 = r3->field_2f
    //     0xb2c9c8: ldur            w1, [x3, #0x2f]
    // 0xb2c9cc: DecompressPointer r1
    //     0xb2c9cc: add             x1, x1, HEAP, lsl #32
    // 0xb2c9d0: cmp             w1, NULL
    // 0xb2c9d4: b.ne            #0xb2c9e8
    // 0xb2c9d8: r0 = Instance_SizedBox
    //     0xb2c9d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb2c9dc: LeaveFrame
    //     0xb2c9dc: mov             SP, fp
    //     0xb2c9e0: ldp             fp, lr, [SP], #0x10
    // 0xb2c9e4: ret
    //     0xb2c9e4: ret             
    // 0xb2c9e8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb2c9e8: ldur            w3, [x1, #0x17]
    // 0xb2c9ec: DecompressPointer r3
    //     0xb2c9ec: add             x3, x3, HEAP, lsl #32
    // 0xb2c9f0: cmp             w3, NULL
    // 0xb2c9f4: b.ne            #0xb2c9fc
    // 0xb2c9f8: r3 = false
    //     0xb2c9f8: add             x3, NULL, #0x30  ; false
    // 0xb2c9fc: stur            x3, [fp, #-8]
    // 0xb2ca00: tbnz            w3, #4, #0xb2ca1c
    // 0xb2ca04: r1 = Instance_Color
    //     0xb2ca04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2ca08: d0 = 0.070000
    //     0xb2ca08: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb2ca0c: ldr             d0, [x17, #0x5f8]
    // 0xb2ca10: r0 = withOpacity()
    //     0xb2ca10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2ca14: mov             x2, x0
    // 0xb2ca18: b               #0xb2ca4c
    // 0xb2ca1c: ldur            x1, [fp, #-0x18]
    // 0xb2ca20: r0 = of()
    //     0xb2ca20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ca24: LoadField: r1 = r0->field_5b
    //     0xb2ca24: ldur            w1, [x0, #0x5b]
    // 0xb2ca28: DecompressPointer r1
    //     0xb2ca28: add             x1, x1, HEAP, lsl #32
    // 0xb2ca2c: r0 = LoadClassIdInstr(r1)
    //     0xb2ca2c: ldur            x0, [x1, #-1]
    //     0xb2ca30: ubfx            x0, x0, #0xc, #0x14
    // 0xb2ca34: d0 = 0.070000
    //     0xb2ca34: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb2ca38: ldr             d0, [x17, #0x5f8]
    // 0xb2ca3c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb2ca3c: sub             lr, x0, #0xffa
    //     0xb2ca40: ldr             lr, [x21, lr, lsl #3]
    //     0xb2ca44: blr             lr
    // 0xb2ca48: mov             x2, x0
    // 0xb2ca4c: ldur            x3, [fp, #-8]
    // 0xb2ca50: r16 = 1.000000
    //     0xb2ca50: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb2ca54: str             x16, [SP]
    // 0xb2ca58: r1 = Null
    //     0xb2ca58: mov             x1, NULL
    // 0xb2ca5c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb2ca5c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb2ca60: ldr             x4, [x4, #0x108]
    // 0xb2ca64: r0 = Border.all()
    //     0xb2ca64: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb2ca68: ldur            x3, [fp, #-8]
    // 0xb2ca6c: stur            x0, [fp, #-0x20]
    // 0xb2ca70: tbnz            w3, #4, #0xb2ca7c
    // 0xb2ca74: d0 = 15.000000
    //     0xb2ca74: fmov            d0, #15.00000000
    // 0xb2ca78: b               #0xb2ca80
    // 0xb2ca7c: d0 = 12.000000
    //     0xb2ca7c: fmov            d0, #12.00000000
    // 0xb2ca80: stur            d0, [fp, #-0x40]
    // 0xb2ca84: r0 = Radius()
    //     0xb2ca84: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2ca88: ldur            d0, [fp, #-0x40]
    // 0xb2ca8c: stur            x0, [fp, #-0x28]
    // 0xb2ca90: StoreField: r0->field_7 = d0
    //     0xb2ca90: stur            d0, [x0, #7]
    // 0xb2ca94: StoreField: r0->field_f = d0
    //     0xb2ca94: stur            d0, [x0, #0xf]
    // 0xb2ca98: r0 = BorderRadius()
    //     0xb2ca98: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2ca9c: mov             x1, x0
    // 0xb2caa0: ldur            x0, [fp, #-0x28]
    // 0xb2caa4: stur            x1, [fp, #-0x30]
    // 0xb2caa8: StoreField: r1->field_7 = r0
    //     0xb2caa8: stur            w0, [x1, #7]
    // 0xb2caac: StoreField: r1->field_b = r0
    //     0xb2caac: stur            w0, [x1, #0xb]
    // 0xb2cab0: StoreField: r1->field_f = r0
    //     0xb2cab0: stur            w0, [x1, #0xf]
    // 0xb2cab4: StoreField: r1->field_13 = r0
    //     0xb2cab4: stur            w0, [x1, #0x13]
    // 0xb2cab8: ldur            x3, [fp, #-8]
    // 0xb2cabc: tbnz            w3, #4, #0xb2cacc
    // 0xb2cac0: r2 = Instance_LinearGradient
    //     0xb2cac0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xb2cac4: ldr             x2, [x2, #0x660]
    // 0xb2cac8: b               #0xb2cad0
    // 0xb2cacc: r2 = Null
    //     0xb2cacc: mov             x2, NULL
    // 0xb2cad0: ldur            x0, [fp, #-0x20]
    // 0xb2cad4: stur            x2, [fp, #-0x28]
    // 0xb2cad8: r0 = BoxDecoration()
    //     0xb2cad8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2cadc: mov             x4, x0
    // 0xb2cae0: ldur            x0, [fp, #-0x20]
    // 0xb2cae4: stur            x4, [fp, #-0x38]
    // 0xb2cae8: StoreField: r4->field_f = r0
    //     0xb2cae8: stur            w0, [x4, #0xf]
    // 0xb2caec: ldur            x0, [fp, #-0x30]
    // 0xb2caf0: StoreField: r4->field_13 = r0
    //     0xb2caf0: stur            w0, [x4, #0x13]
    // 0xb2caf4: ldur            x0, [fp, #-0x28]
    // 0xb2caf8: StoreField: r4->field_1b = r0
    //     0xb2caf8: stur            w0, [x4, #0x1b]
    // 0xb2cafc: r0 = Instance_BoxShape
    //     0xb2cafc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2cb00: ldr             x0, [x0, #0x80]
    // 0xb2cb04: StoreField: r4->field_23 = r0
    //     0xb2cb04: stur            w0, [x4, #0x23]
    // 0xb2cb08: ldur            x1, [fp, #-0x10]
    // 0xb2cb0c: ldur            x2, [fp, #-0x18]
    // 0xb2cb10: ldur            x3, [fp, #-8]
    // 0xb2cb14: r0 = _buildFreeGiftContent()
    //     0xb2cb14: bl              #0xb2cb70  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildFreeGiftContent
    // 0xb2cb18: stur            x0, [fp, #-8]
    // 0xb2cb1c: r0 = Container()
    //     0xb2cb1c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2cb20: stur            x0, [fp, #-0x10]
    // 0xb2cb24: ldur            x16, [fp, #-0x38]
    // 0xb2cb28: ldur            lr, [fp, #-8]
    // 0xb2cb2c: stp             lr, x16, [SP]
    // 0xb2cb30: mov             x1, x0
    // 0xb2cb34: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb2cb34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb2cb38: ldr             x4, [x4, #0x88]
    // 0xb2cb3c: r0 = Container()
    //     0xb2cb3c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2cb40: r0 = Padding()
    //     0xb2cb40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2cb44: r1 = Instance_EdgeInsets
    //     0xb2cb44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb2cb48: ldr             x1, [x1, #0x1f0]
    // 0xb2cb4c: StoreField: r0->field_f = r1
    //     0xb2cb4c: stur            w1, [x0, #0xf]
    // 0xb2cb50: ldur            x1, [fp, #-0x10]
    // 0xb2cb54: StoreField: r0->field_b = r1
    //     0xb2cb54: stur            w1, [x0, #0xb]
    // 0xb2cb58: LeaveFrame
    //     0xb2cb58: mov             SP, fp
    //     0xb2cb5c: ldp             fp, lr, [SP], #0x10
    // 0xb2cb60: ret
    //     0xb2cb60: ret             
    // 0xb2cb64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2cb64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2cb68: b               #0xb2c9a8
    // 0xb2cb6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2cb6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildFreeGiftContent(/* No info */) {
    // ** addr: 0xb2cb70, size: 0x92c
    // 0xb2cb70: EnterFrame
    //     0xb2cb70: stp             fp, lr, [SP, #-0x10]!
    //     0xb2cb74: mov             fp, SP
    // 0xb2cb78: AllocStack(0x78)
    //     0xb2cb78: sub             SP, SP, #0x78
    // 0xb2cb7c: SetupParameters(_BagItemViewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb2cb7c: mov             x0, x1
    //     0xb2cb80: stur            x1, [fp, #-8]
    //     0xb2cb84: mov             x1, x2
    //     0xb2cb88: stur            x2, [fp, #-0x10]
    //     0xb2cb8c: stur            x3, [fp, #-0x18]
    // 0xb2cb90: CheckStackOverflow
    //     0xb2cb90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2cb94: cmp             SP, x16
    //     0xb2cb98: b.ls            #0xb2d480
    // 0xb2cb9c: r1 = 2
    //     0xb2cb9c: movz            x1, #0x2
    // 0xb2cba0: r0 = AllocateContext()
    //     0xb2cba0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2cba4: mov             x2, x0
    // 0xb2cba8: ldur            x0, [fp, #-8]
    // 0xb2cbac: stur            x2, [fp, #-0x20]
    // 0xb2cbb0: StoreField: r2->field_f = r0
    //     0xb2cbb0: stur            w0, [x2, #0xf]
    // 0xb2cbb4: ldur            x1, [fp, #-0x10]
    // 0xb2cbb8: StoreField: r2->field_13 = r1
    //     0xb2cbb8: stur            w1, [x2, #0x13]
    // 0xb2cbbc: ldur            x3, [fp, #-0x18]
    // 0xb2cbc0: tbnz            w3, #4, #0xb2cbd4
    // 0xb2cbc4: mov             x0, x3
    // 0xb2cbc8: r1 = Instance_Color
    //     0xb2cbc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb2cbcc: ldr             x1, [x1, #0x858]
    // 0xb2cbd0: b               #0xb2cc00
    // 0xb2cbd4: r0 = of()
    //     0xb2cbd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2cbd8: LoadField: r1 = r0->field_5b
    //     0xb2cbd8: ldur            w1, [x0, #0x5b]
    // 0xb2cbdc: DecompressPointer r1
    //     0xb2cbdc: add             x1, x1, HEAP, lsl #32
    // 0xb2cbe0: r0 = LoadClassIdInstr(r1)
    //     0xb2cbe0: ldur            x0, [x1, #-1]
    //     0xb2cbe4: ubfx            x0, x0, #0xc, #0x14
    // 0xb2cbe8: d0 = 0.400000
    //     0xb2cbe8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb2cbec: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb2cbec: sub             lr, x0, #0xffa
    //     0xb2cbf0: ldr             lr, [x21, lr, lsl #3]
    //     0xb2cbf4: blr             lr
    // 0xb2cbf8: mov             x1, x0
    // 0xb2cbfc: ldur            x0, [fp, #-0x18]
    // 0xb2cc00: stur            x1, [fp, #-0x10]
    // 0xb2cc04: tbnz            w0, #4, #0xb2cc10
    // 0xb2cc08: d0 = 15.000000
    //     0xb2cc08: fmov            d0, #15.00000000
    // 0xb2cc0c: b               #0xb2cc14
    // 0xb2cc10: d0 = 12.000000
    //     0xb2cc10: fmov            d0, #12.00000000
    // 0xb2cc14: stur            d0, [fp, #-0x58]
    // 0xb2cc18: r0 = Radius()
    //     0xb2cc18: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2cc1c: ldur            d0, [fp, #-0x58]
    // 0xb2cc20: stur            x0, [fp, #-0x28]
    // 0xb2cc24: StoreField: r0->field_7 = d0
    //     0xb2cc24: stur            d0, [x0, #7]
    // 0xb2cc28: StoreField: r0->field_f = d0
    //     0xb2cc28: stur            d0, [x0, #0xf]
    // 0xb2cc2c: ldur            x1, [fp, #-0x18]
    // 0xb2cc30: tbnz            w1, #4, #0xb2cc3c
    // 0xb2cc34: d0 = 15.000000
    //     0xb2cc34: fmov            d0, #15.00000000
    // 0xb2cc38: b               #0xb2cc40
    // 0xb2cc3c: d0 = 12.000000
    //     0xb2cc3c: fmov            d0, #12.00000000
    // 0xb2cc40: ldur            x3, [fp, #-8]
    // 0xb2cc44: ldur            x2, [fp, #-0x10]
    // 0xb2cc48: stur            d0, [fp, #-0x58]
    // 0xb2cc4c: r0 = Radius()
    //     0xb2cc4c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2cc50: ldur            d0, [fp, #-0x58]
    // 0xb2cc54: stur            x0, [fp, #-0x30]
    // 0xb2cc58: StoreField: r0->field_7 = d0
    //     0xb2cc58: stur            d0, [x0, #7]
    // 0xb2cc5c: StoreField: r0->field_f = d0
    //     0xb2cc5c: stur            d0, [x0, #0xf]
    // 0xb2cc60: r0 = BorderRadius()
    //     0xb2cc60: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2cc64: mov             x1, x0
    // 0xb2cc68: ldur            x0, [fp, #-0x28]
    // 0xb2cc6c: stur            x1, [fp, #-0x38]
    // 0xb2cc70: StoreField: r1->field_7 = r0
    //     0xb2cc70: stur            w0, [x1, #7]
    // 0xb2cc74: r0 = Instance_Radius
    //     0xb2cc74: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0xb2cc78: ldr             x0, [x0, #0xb48]
    // 0xb2cc7c: StoreField: r1->field_b = r0
    //     0xb2cc7c: stur            w0, [x1, #0xb]
    // 0xb2cc80: ldur            x2, [fp, #-0x30]
    // 0xb2cc84: StoreField: r1->field_f = r2
    //     0xb2cc84: stur            w2, [x1, #0xf]
    // 0xb2cc88: StoreField: r1->field_13 = r0
    //     0xb2cc88: stur            w0, [x1, #0x13]
    // 0xb2cc8c: r0 = BoxDecoration()
    //     0xb2cc8c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2cc90: mov             x1, x0
    // 0xb2cc94: ldur            x0, [fp, #-0x10]
    // 0xb2cc98: stur            x1, [fp, #-0x28]
    // 0xb2cc9c: StoreField: r1->field_7 = r0
    //     0xb2cc9c: stur            w0, [x1, #7]
    // 0xb2cca0: ldur            x0, [fp, #-0x38]
    // 0xb2cca4: StoreField: r1->field_13 = r0
    //     0xb2cca4: stur            w0, [x1, #0x13]
    // 0xb2cca8: r0 = Instance_BoxShape
    //     0xb2cca8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2ccac: ldr             x0, [x0, #0x80]
    // 0xb2ccb0: StoreField: r1->field_23 = r0
    //     0xb2ccb0: stur            w0, [x1, #0x23]
    // 0xb2ccb4: r0 = Container()
    //     0xb2ccb4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2ccb8: stur            x0, [fp, #-0x10]
    // 0xb2ccbc: r16 = 24.000000
    //     0xb2ccbc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb2ccc0: ldr             x16, [x16, #0xba8]
    // 0xb2ccc4: r30 = 56.000000
    //     0xb2ccc4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb2ccc8: ldr             lr, [lr, #0xb78]
    // 0xb2cccc: stp             lr, x16, [SP, #0x10]
    // 0xb2ccd0: ldur            x16, [fp, #-0x28]
    // 0xb2ccd4: r30 = Instance_RotatedBox
    //     0xb2ccd4: add             lr, PP, #0x48, lsl #12  ; [pp+0x48cf8] Obj!RotatedBox@d685a1
    //     0xb2ccd8: ldr             lr, [lr, #0xcf8]
    // 0xb2ccdc: stp             lr, x16, [SP]
    // 0xb2cce0: mov             x1, x0
    // 0xb2cce4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb2cce4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb2cce8: ldr             x4, [x4, #0x870]
    // 0xb2ccec: r0 = Container()
    //     0xb2ccec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2ccf0: ldur            x0, [fp, #-8]
    // 0xb2ccf4: LoadField: r1 = r0->field_b
    //     0xb2ccf4: ldur            w1, [x0, #0xb]
    // 0xb2ccf8: DecompressPointer r1
    //     0xb2ccf8: add             x1, x1, HEAP, lsl #32
    // 0xb2ccfc: cmp             w1, NULL
    // 0xb2cd00: b.eq            #0xb2d488
    // 0xb2cd04: LoadField: r2 = r1->field_b
    //     0xb2cd04: ldur            w2, [x1, #0xb]
    // 0xb2cd08: DecompressPointer r2
    //     0xb2cd08: add             x2, x2, HEAP, lsl #32
    // 0xb2cd0c: cmp             w2, NULL
    // 0xb2cd10: b.ne            #0xb2cd1c
    // 0xb2cd14: r1 = Null
    //     0xb2cd14: mov             x1, NULL
    // 0xb2cd18: b               #0xb2cd40
    // 0xb2cd1c: LoadField: r1 = r2->field_2f
    //     0xb2cd1c: ldur            w1, [x2, #0x2f]
    // 0xb2cd20: DecompressPointer r1
    //     0xb2cd20: add             x1, x1, HEAP, lsl #32
    // 0xb2cd24: cmp             w1, NULL
    // 0xb2cd28: b.ne            #0xb2cd34
    // 0xb2cd2c: r1 = Null
    //     0xb2cd2c: mov             x1, NULL
    // 0xb2cd30: b               #0xb2cd40
    // 0xb2cd34: LoadField: r2 = r1->field_7
    //     0xb2cd34: ldur            w2, [x1, #7]
    // 0xb2cd38: DecompressPointer r2
    //     0xb2cd38: add             x2, x2, HEAP, lsl #32
    // 0xb2cd3c: mov             x1, x2
    // 0xb2cd40: cmp             w1, NULL
    // 0xb2cd44: b.ne            #0xb2cd50
    // 0xb2cd48: r2 = ""
    //     0xb2cd48: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2cd4c: b               #0xb2cd54
    // 0xb2cd50: mov             x2, x1
    // 0xb2cd54: stur            x2, [fp, #-0x28]
    // 0xb2cd58: r0 = CachedNetworkImage()
    //     0xb2cd58: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb2cd5c: stur            x0, [fp, #-0x30]
    // 0xb2cd60: r16 = 56.000000
    //     0xb2cd60: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb2cd64: ldr             x16, [x16, #0xb78]
    // 0xb2cd68: r30 = 56.000000
    //     0xb2cd68: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb2cd6c: ldr             lr, [lr, #0xb78]
    // 0xb2cd70: stp             lr, x16, [SP, #8]
    // 0xb2cd74: r16 = Instance_BoxFit
    //     0xb2cd74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb2cd78: ldr             x16, [x16, #0x118]
    // 0xb2cd7c: str             x16, [SP]
    // 0xb2cd80: mov             x1, x0
    // 0xb2cd84: ldur            x2, [fp, #-0x28]
    // 0xb2cd88: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb2cd88: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb2cd8c: ldr             x4, [x4, #0xb40]
    // 0xb2cd90: r0 = CachedNetworkImage()
    //     0xb2cd90: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb2cd94: r0 = InkWell()
    //     0xb2cd94: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2cd98: mov             x3, x0
    // 0xb2cd9c: ldur            x0, [fp, #-0x30]
    // 0xb2cda0: stur            x3, [fp, #-0x28]
    // 0xb2cda4: StoreField: r3->field_b = r0
    //     0xb2cda4: stur            w0, [x3, #0xb]
    // 0xb2cda8: ldur            x2, [fp, #-0x20]
    // 0xb2cdac: r1 = Function '<anonymous closure>':.
    //     0xb2cdac: add             x1, PP, #0x57, lsl #12  ; [pp+0x572e8] AnonymousClosure: (0xb2d49c), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildFreeGiftContent (0xb2cb70)
    //     0xb2cdb0: ldr             x1, [x1, #0x2e8]
    // 0xb2cdb4: r0 = AllocateClosure()
    //     0xb2cdb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2cdb8: mov             x1, x0
    // 0xb2cdbc: ldur            x0, [fp, #-0x28]
    // 0xb2cdc0: StoreField: r0->field_f = r1
    //     0xb2cdc0: stur            w1, [x0, #0xf]
    // 0xb2cdc4: r2 = true
    //     0xb2cdc4: add             x2, NULL, #0x20  ; true
    // 0xb2cdc8: StoreField: r0->field_43 = r2
    //     0xb2cdc8: stur            w2, [x0, #0x43]
    // 0xb2cdcc: r3 = Instance_BoxShape
    //     0xb2cdcc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2cdd0: ldr             x3, [x3, #0x80]
    // 0xb2cdd4: StoreField: r0->field_47 = r3
    //     0xb2cdd4: stur            w3, [x0, #0x47]
    // 0xb2cdd8: StoreField: r0->field_6f = r2
    //     0xb2cdd8: stur            w2, [x0, #0x6f]
    // 0xb2cddc: r4 = false
    //     0xb2cddc: add             x4, NULL, #0x30  ; false
    // 0xb2cde0: StoreField: r0->field_73 = r4
    //     0xb2cde0: stur            w4, [x0, #0x73]
    // 0xb2cde4: StoreField: r0->field_83 = r2
    //     0xb2cde4: stur            w2, [x0, #0x83]
    // 0xb2cde8: StoreField: r0->field_7b = r4
    //     0xb2cde8: stur            w4, [x0, #0x7b]
    // 0xb2cdec: ldur            x5, [fp, #-8]
    // 0xb2cdf0: LoadField: r1 = r5->field_b
    //     0xb2cdf0: ldur            w1, [x5, #0xb]
    // 0xb2cdf4: DecompressPointer r1
    //     0xb2cdf4: add             x1, x1, HEAP, lsl #32
    // 0xb2cdf8: cmp             w1, NULL
    // 0xb2cdfc: b.eq            #0xb2d48c
    // 0xb2ce00: LoadField: r6 = r1->field_b
    //     0xb2ce00: ldur            w6, [x1, #0xb]
    // 0xb2ce04: DecompressPointer r6
    //     0xb2ce04: add             x6, x6, HEAP, lsl #32
    // 0xb2ce08: cmp             w6, NULL
    // 0xb2ce0c: b.ne            #0xb2ce18
    // 0xb2ce10: r1 = Null
    //     0xb2ce10: mov             x1, NULL
    // 0xb2ce14: b               #0xb2ce3c
    // 0xb2ce18: LoadField: r1 = r6->field_2f
    //     0xb2ce18: ldur            w1, [x6, #0x2f]
    // 0xb2ce1c: DecompressPointer r1
    //     0xb2ce1c: add             x1, x1, HEAP, lsl #32
    // 0xb2ce20: cmp             w1, NULL
    // 0xb2ce24: b.ne            #0xb2ce30
    // 0xb2ce28: r1 = Null
    //     0xb2ce28: mov             x1, NULL
    // 0xb2ce2c: b               #0xb2ce3c
    // 0xb2ce30: LoadField: r6 = r1->field_b
    //     0xb2ce30: ldur            w6, [x1, #0xb]
    // 0xb2ce34: DecompressPointer r6
    //     0xb2ce34: add             x6, x6, HEAP, lsl #32
    // 0xb2ce38: mov             x1, x6
    // 0xb2ce3c: cmp             w1, NULL
    // 0xb2ce40: b.ne            #0xb2ce4c
    // 0xb2ce44: r7 = ""
    //     0xb2ce44: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2ce48: b               #0xb2ce50
    // 0xb2ce4c: mov             x7, x1
    // 0xb2ce50: ldur            x6, [fp, #-0x20]
    // 0xb2ce54: stur            x7, [fp, #-0x30]
    // 0xb2ce58: LoadField: r1 = r6->field_13
    //     0xb2ce58: ldur            w1, [x6, #0x13]
    // 0xb2ce5c: DecompressPointer r1
    //     0xb2ce5c: add             x1, x1, HEAP, lsl #32
    // 0xb2ce60: r0 = of()
    //     0xb2ce60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ce64: LoadField: r1 = r0->field_87
    //     0xb2ce64: ldur            w1, [x0, #0x87]
    // 0xb2ce68: DecompressPointer r1
    //     0xb2ce68: add             x1, x1, HEAP, lsl #32
    // 0xb2ce6c: LoadField: r0 = r1->field_7
    //     0xb2ce6c: ldur            w0, [x1, #7]
    // 0xb2ce70: DecompressPointer r0
    //     0xb2ce70: add             x0, x0, HEAP, lsl #32
    // 0xb2ce74: r16 = 12.000000
    //     0xb2ce74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2ce78: ldr             x16, [x16, #0x9e8]
    // 0xb2ce7c: r30 = Instance_Color
    //     0xb2ce7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2ce80: stp             lr, x16, [SP]
    // 0xb2ce84: mov             x1, x0
    // 0xb2ce88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2ce88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2ce8c: ldr             x4, [x4, #0xaa0]
    // 0xb2ce90: r0 = copyWith()
    //     0xb2ce90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2ce94: stur            x0, [fp, #-0x38]
    // 0xb2ce98: r0 = Text()
    //     0xb2ce98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2ce9c: mov             x1, x0
    // 0xb2cea0: ldur            x0, [fp, #-0x30]
    // 0xb2cea4: stur            x1, [fp, #-0x40]
    // 0xb2cea8: StoreField: r1->field_b = r0
    //     0xb2cea8: stur            w0, [x1, #0xb]
    // 0xb2ceac: ldur            x0, [fp, #-0x38]
    // 0xb2ceb0: StoreField: r1->field_13 = r0
    //     0xb2ceb0: stur            w0, [x1, #0x13]
    // 0xb2ceb4: r0 = Instance_TextOverflow
    //     0xb2ceb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb2ceb8: ldr             x0, [x0, #0xe10]
    // 0xb2cebc: StoreField: r1->field_2b = r0
    //     0xb2cebc: stur            w0, [x1, #0x2b]
    // 0xb2cec0: r0 = 2
    //     0xb2cec0: movz            x0, #0x2
    // 0xb2cec4: StoreField: r1->field_37 = r0
    //     0xb2cec4: stur            w0, [x1, #0x37]
    // 0xb2cec8: r0 = SizedBox()
    //     0xb2cec8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb2cecc: mov             x2, x0
    // 0xb2ced0: r0 = 150.000000
    //     0xb2ced0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb2ced4: ldr             x0, [x0, #0x690]
    // 0xb2ced8: stur            x2, [fp, #-0x30]
    // 0xb2cedc: StoreField: r2->field_f = r0
    //     0xb2cedc: stur            w0, [x2, #0xf]
    // 0xb2cee0: ldur            x0, [fp, #-0x40]
    // 0xb2cee4: StoreField: r2->field_b = r0
    //     0xb2cee4: stur            w0, [x2, #0xb]
    // 0xb2cee8: ldur            x0, [fp, #-0x20]
    // 0xb2ceec: LoadField: r1 = r0->field_13
    //     0xb2ceec: ldur            w1, [x0, #0x13]
    // 0xb2cef0: DecompressPointer r1
    //     0xb2cef0: add             x1, x1, HEAP, lsl #32
    // 0xb2cef4: r0 = of()
    //     0xb2cef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2cef8: LoadField: r1 = r0->field_87
    //     0xb2cef8: ldur            w1, [x0, #0x87]
    // 0xb2cefc: DecompressPointer r1
    //     0xb2cefc: add             x1, x1, HEAP, lsl #32
    // 0xb2cf00: LoadField: r0 = r1->field_2b
    //     0xb2cf00: ldur            w0, [x1, #0x2b]
    // 0xb2cf04: DecompressPointer r0
    //     0xb2cf04: add             x0, x0, HEAP, lsl #32
    // 0xb2cf08: r16 = 12.000000
    //     0xb2cf08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2cf0c: ldr             x16, [x16, #0x9e8]
    // 0xb2cf10: r30 = Instance_Color
    //     0xb2cf10: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2cf14: stp             lr, x16, [SP]
    // 0xb2cf18: mov             x1, x0
    // 0xb2cf1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2cf1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2cf20: ldr             x4, [x4, #0xaa0]
    // 0xb2cf24: r0 = copyWith()
    //     0xb2cf24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2cf28: stur            x0, [fp, #-0x38]
    // 0xb2cf2c: r0 = Text()
    //     0xb2cf2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2cf30: mov             x2, x0
    // 0xb2cf34: r0 = "Free"
    //     0xb2cf34: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb2cf38: ldr             x0, [x0, #0x668]
    // 0xb2cf3c: stur            x2, [fp, #-0x40]
    // 0xb2cf40: StoreField: r2->field_b = r0
    //     0xb2cf40: stur            w0, [x2, #0xb]
    // 0xb2cf44: ldur            x0, [fp, #-0x38]
    // 0xb2cf48: StoreField: r2->field_13 = r0
    //     0xb2cf48: stur            w0, [x2, #0x13]
    // 0xb2cf4c: ldur            x0, [fp, #-8]
    // 0xb2cf50: LoadField: r1 = r0->field_b
    //     0xb2cf50: ldur            w1, [x0, #0xb]
    // 0xb2cf54: DecompressPointer r1
    //     0xb2cf54: add             x1, x1, HEAP, lsl #32
    // 0xb2cf58: cmp             w1, NULL
    // 0xb2cf5c: b.eq            #0xb2d490
    // 0xb2cf60: LoadField: r3 = r1->field_b
    //     0xb2cf60: ldur            w3, [x1, #0xb]
    // 0xb2cf64: DecompressPointer r3
    //     0xb2cf64: add             x3, x3, HEAP, lsl #32
    // 0xb2cf68: cmp             w3, NULL
    // 0xb2cf6c: b.ne            #0xb2cf78
    // 0xb2cf70: r1 = Null
    //     0xb2cf70: mov             x1, NULL
    // 0xb2cf74: b               #0xb2cf9c
    // 0xb2cf78: LoadField: r1 = r3->field_2f
    //     0xb2cf78: ldur            w1, [x3, #0x2f]
    // 0xb2cf7c: DecompressPointer r1
    //     0xb2cf7c: add             x1, x1, HEAP, lsl #32
    // 0xb2cf80: cmp             w1, NULL
    // 0xb2cf84: b.ne            #0xb2cf90
    // 0xb2cf88: r1 = Null
    //     0xb2cf88: mov             x1, NULL
    // 0xb2cf8c: b               #0xb2cf9c
    // 0xb2cf90: LoadField: r3 = r1->field_13
    //     0xb2cf90: ldur            w3, [x1, #0x13]
    // 0xb2cf94: DecompressPointer r3
    //     0xb2cf94: add             x3, x3, HEAP, lsl #32
    // 0xb2cf98: mov             x1, x3
    // 0xb2cf9c: cmp             w1, NULL
    // 0xb2cfa0: b.ne            #0xb2cfac
    // 0xb2cfa4: r8 = ""
    //     0xb2cfa4: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2cfa8: b               #0xb2cfb0
    // 0xb2cfac: mov             x8, x1
    // 0xb2cfb0: ldur            x7, [fp, #-0x18]
    // 0xb2cfb4: ldur            x4, [fp, #-0x20]
    // 0xb2cfb8: ldur            x6, [fp, #-0x10]
    // 0xb2cfbc: ldur            x5, [fp, #-0x28]
    // 0xb2cfc0: ldur            x3, [fp, #-0x30]
    // 0xb2cfc4: stur            x8, [fp, #-0x38]
    // 0xb2cfc8: LoadField: r1 = r4->field_13
    //     0xb2cfc8: ldur            w1, [x4, #0x13]
    // 0xb2cfcc: DecompressPointer r1
    //     0xb2cfcc: add             x1, x1, HEAP, lsl #32
    // 0xb2cfd0: r0 = of()
    //     0xb2cfd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2cfd4: LoadField: r1 = r0->field_87
    //     0xb2cfd4: ldur            w1, [x0, #0x87]
    // 0xb2cfd8: DecompressPointer r1
    //     0xb2cfd8: add             x1, x1, HEAP, lsl #32
    // 0xb2cfdc: LoadField: r0 = r1->field_2b
    //     0xb2cfdc: ldur            w0, [x1, #0x2b]
    // 0xb2cfe0: DecompressPointer r0
    //     0xb2cfe0: add             x0, x0, HEAP, lsl #32
    // 0xb2cfe4: r16 = 12.000000
    //     0xb2cfe4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2cfe8: ldr             x16, [x16, #0x9e8]
    // 0xb2cfec: r30 = Instance_TextDecoration
    //     0xb2cfec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb2cff0: ldr             lr, [lr, #0xe30]
    // 0xb2cff4: stp             lr, x16, [SP]
    // 0xb2cff8: mov             x1, x0
    // 0xb2cffc: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb2cffc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb2d000: ldr             x4, [x4, #0x698]
    // 0xb2d004: r0 = copyWith()
    //     0xb2d004: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2d008: stur            x0, [fp, #-0x48]
    // 0xb2d00c: r0 = Text()
    //     0xb2d00c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2d010: mov             x3, x0
    // 0xb2d014: ldur            x0, [fp, #-0x38]
    // 0xb2d018: stur            x3, [fp, #-0x50]
    // 0xb2d01c: StoreField: r3->field_b = r0
    //     0xb2d01c: stur            w0, [x3, #0xb]
    // 0xb2d020: ldur            x0, [fp, #-0x48]
    // 0xb2d024: StoreField: r3->field_13 = r0
    //     0xb2d024: stur            w0, [x3, #0x13]
    // 0xb2d028: r1 = Null
    //     0xb2d028: mov             x1, NULL
    // 0xb2d02c: r2 = 6
    //     0xb2d02c: movz            x2, #0x6
    // 0xb2d030: r0 = AllocateArray()
    //     0xb2d030: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d034: mov             x2, x0
    // 0xb2d038: ldur            x0, [fp, #-0x40]
    // 0xb2d03c: stur            x2, [fp, #-0x38]
    // 0xb2d040: StoreField: r2->field_f = r0
    //     0xb2d040: stur            w0, [x2, #0xf]
    // 0xb2d044: r16 = Instance_SizedBox
    //     0xb2d044: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb2d048: ldr             x16, [x16, #0xa50]
    // 0xb2d04c: StoreField: r2->field_13 = r16
    //     0xb2d04c: stur            w16, [x2, #0x13]
    // 0xb2d050: ldur            x0, [fp, #-0x50]
    // 0xb2d054: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2d054: stur            w0, [x2, #0x17]
    // 0xb2d058: r1 = <Widget>
    //     0xb2d058: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d05c: r0 = AllocateGrowableArray()
    //     0xb2d05c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d060: mov             x1, x0
    // 0xb2d064: ldur            x0, [fp, #-0x38]
    // 0xb2d068: stur            x1, [fp, #-0x40]
    // 0xb2d06c: StoreField: r1->field_f = r0
    //     0xb2d06c: stur            w0, [x1, #0xf]
    // 0xb2d070: r2 = 6
    //     0xb2d070: movz            x2, #0x6
    // 0xb2d074: StoreField: r1->field_b = r2
    //     0xb2d074: stur            w2, [x1, #0xb]
    // 0xb2d078: r0 = Row()
    //     0xb2d078: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2d07c: mov             x3, x0
    // 0xb2d080: r0 = Instance_Axis
    //     0xb2d080: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2d084: stur            x3, [fp, #-0x38]
    // 0xb2d088: StoreField: r3->field_f = r0
    //     0xb2d088: stur            w0, [x3, #0xf]
    // 0xb2d08c: r4 = Instance_MainAxisAlignment
    //     0xb2d08c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2d090: ldr             x4, [x4, #0xa08]
    // 0xb2d094: StoreField: r3->field_13 = r4
    //     0xb2d094: stur            w4, [x3, #0x13]
    // 0xb2d098: r5 = Instance_MainAxisSize
    //     0xb2d098: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d09c: ldr             x5, [x5, #0xa10]
    // 0xb2d0a0: ArrayStore: r3[0] = r5  ; List_4
    //     0xb2d0a0: stur            w5, [x3, #0x17]
    // 0xb2d0a4: r6 = Instance_CrossAxisAlignment
    //     0xb2d0a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2d0a8: ldr             x6, [x6, #0xa18]
    // 0xb2d0ac: StoreField: r3->field_1b = r6
    //     0xb2d0ac: stur            w6, [x3, #0x1b]
    // 0xb2d0b0: r7 = Instance_VerticalDirection
    //     0xb2d0b0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d0b4: ldr             x7, [x7, #0xa20]
    // 0xb2d0b8: StoreField: r3->field_23 = r7
    //     0xb2d0b8: stur            w7, [x3, #0x23]
    // 0xb2d0bc: r8 = Instance_Clip
    //     0xb2d0bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d0c0: ldr             x8, [x8, #0x38]
    // 0xb2d0c4: StoreField: r3->field_2b = r8
    //     0xb2d0c4: stur            w8, [x3, #0x2b]
    // 0xb2d0c8: StoreField: r3->field_2f = rZR
    //     0xb2d0c8: stur            xzr, [x3, #0x2f]
    // 0xb2d0cc: ldur            x1, [fp, #-0x40]
    // 0xb2d0d0: StoreField: r3->field_b = r1
    //     0xb2d0d0: stur            w1, [x3, #0xb]
    // 0xb2d0d4: r1 = Null
    //     0xb2d0d4: mov             x1, NULL
    // 0xb2d0d8: r2 = 6
    //     0xb2d0d8: movz            x2, #0x6
    // 0xb2d0dc: r0 = AllocateArray()
    //     0xb2d0dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d0e0: mov             x2, x0
    // 0xb2d0e4: ldur            x0, [fp, #-0x30]
    // 0xb2d0e8: stur            x2, [fp, #-0x40]
    // 0xb2d0ec: StoreField: r2->field_f = r0
    //     0xb2d0ec: stur            w0, [x2, #0xf]
    // 0xb2d0f0: r16 = Instance_SizedBox
    //     0xb2d0f0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb2d0f4: ldr             x16, [x16, #0xc70]
    // 0xb2d0f8: StoreField: r2->field_13 = r16
    //     0xb2d0f8: stur            w16, [x2, #0x13]
    // 0xb2d0fc: ldur            x0, [fp, #-0x38]
    // 0xb2d100: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2d100: stur            w0, [x2, #0x17]
    // 0xb2d104: r1 = <Widget>
    //     0xb2d104: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d108: r0 = AllocateGrowableArray()
    //     0xb2d108: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d10c: mov             x1, x0
    // 0xb2d110: ldur            x0, [fp, #-0x40]
    // 0xb2d114: stur            x1, [fp, #-0x30]
    // 0xb2d118: StoreField: r1->field_f = r0
    //     0xb2d118: stur            w0, [x1, #0xf]
    // 0xb2d11c: r2 = 6
    //     0xb2d11c: movz            x2, #0x6
    // 0xb2d120: StoreField: r1->field_b = r2
    //     0xb2d120: stur            w2, [x1, #0xb]
    // 0xb2d124: r0 = Column()
    //     0xb2d124: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2d128: mov             x1, x0
    // 0xb2d12c: r0 = Instance_Axis
    //     0xb2d12c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2d130: stur            x1, [fp, #-0x38]
    // 0xb2d134: StoreField: r1->field_f = r0
    //     0xb2d134: stur            w0, [x1, #0xf]
    // 0xb2d138: r0 = Instance_MainAxisAlignment
    //     0xb2d138: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2d13c: ldr             x0, [x0, #0xa08]
    // 0xb2d140: StoreField: r1->field_13 = r0
    //     0xb2d140: stur            w0, [x1, #0x13]
    // 0xb2d144: r2 = Instance_MainAxisSize
    //     0xb2d144: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d148: ldr             x2, [x2, #0xa10]
    // 0xb2d14c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb2d14c: stur            w2, [x1, #0x17]
    // 0xb2d150: r3 = Instance_CrossAxisAlignment
    //     0xb2d150: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2d154: ldr             x3, [x3, #0x890]
    // 0xb2d158: StoreField: r1->field_1b = r3
    //     0xb2d158: stur            w3, [x1, #0x1b]
    // 0xb2d15c: r4 = Instance_VerticalDirection
    //     0xb2d15c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d160: ldr             x4, [x4, #0xa20]
    // 0xb2d164: StoreField: r1->field_23 = r4
    //     0xb2d164: stur            w4, [x1, #0x23]
    // 0xb2d168: r5 = Instance_Clip
    //     0xb2d168: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d16c: ldr             x5, [x5, #0x38]
    // 0xb2d170: StoreField: r1->field_2b = r5
    //     0xb2d170: stur            w5, [x1, #0x2b]
    // 0xb2d174: StoreField: r1->field_2f = rZR
    //     0xb2d174: stur            xzr, [x1, #0x2f]
    // 0xb2d178: ldur            x6, [fp, #-0x30]
    // 0xb2d17c: StoreField: r1->field_b = r6
    //     0xb2d17c: stur            w6, [x1, #0xb]
    // 0xb2d180: r0 = Padding()
    //     0xb2d180: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2d184: mov             x2, x0
    // 0xb2d188: r0 = Instance_EdgeInsets
    //     0xb2d188: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb2d18c: ldr             x0, [x0, #0xa78]
    // 0xb2d190: stur            x2, [fp, #-0x30]
    // 0xb2d194: StoreField: r2->field_f = r0
    //     0xb2d194: stur            w0, [x2, #0xf]
    // 0xb2d198: ldur            x0, [fp, #-0x38]
    // 0xb2d19c: StoreField: r2->field_b = r0
    //     0xb2d19c: stur            w0, [x2, #0xb]
    // 0xb2d1a0: r1 = <FlexParentData>
    //     0xb2d1a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2d1a4: ldr             x1, [x1, #0xe00]
    // 0xb2d1a8: r0 = Expanded()
    //     0xb2d1a8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2d1ac: mov             x3, x0
    // 0xb2d1b0: r0 = 1
    //     0xb2d1b0: movz            x0, #0x1
    // 0xb2d1b4: stur            x3, [fp, #-0x38]
    // 0xb2d1b8: StoreField: r3->field_13 = r0
    //     0xb2d1b8: stur            x0, [x3, #0x13]
    // 0xb2d1bc: r4 = Instance_FlexFit
    //     0xb2d1bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2d1c0: ldr             x4, [x4, #0xe08]
    // 0xb2d1c4: StoreField: r3->field_1b = r4
    //     0xb2d1c4: stur            w4, [x3, #0x1b]
    // 0xb2d1c8: ldur            x1, [fp, #-0x30]
    // 0xb2d1cc: StoreField: r3->field_b = r1
    //     0xb2d1cc: stur            w1, [x3, #0xb]
    // 0xb2d1d0: r1 = Null
    //     0xb2d1d0: mov             x1, NULL
    // 0xb2d1d4: r2 = 6
    //     0xb2d1d4: movz            x2, #0x6
    // 0xb2d1d8: r0 = AllocateArray()
    //     0xb2d1d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d1dc: mov             x2, x0
    // 0xb2d1e0: ldur            x0, [fp, #-0x10]
    // 0xb2d1e4: stur            x2, [fp, #-0x30]
    // 0xb2d1e8: StoreField: r2->field_f = r0
    //     0xb2d1e8: stur            w0, [x2, #0xf]
    // 0xb2d1ec: ldur            x0, [fp, #-0x28]
    // 0xb2d1f0: StoreField: r2->field_13 = r0
    //     0xb2d1f0: stur            w0, [x2, #0x13]
    // 0xb2d1f4: ldur            x0, [fp, #-0x38]
    // 0xb2d1f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2d1f8: stur            w0, [x2, #0x17]
    // 0xb2d1fc: r1 = <Widget>
    //     0xb2d1fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d200: r0 = AllocateGrowableArray()
    //     0xb2d200: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d204: mov             x1, x0
    // 0xb2d208: ldur            x0, [fp, #-0x30]
    // 0xb2d20c: stur            x1, [fp, #-0x10]
    // 0xb2d210: StoreField: r1->field_f = r0
    //     0xb2d210: stur            w0, [x1, #0xf]
    // 0xb2d214: r0 = 6
    //     0xb2d214: movz            x0, #0x6
    // 0xb2d218: StoreField: r1->field_b = r0
    //     0xb2d218: stur            w0, [x1, #0xb]
    // 0xb2d21c: r0 = Row()
    //     0xb2d21c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2d220: mov             x2, x0
    // 0xb2d224: r0 = Instance_Axis
    //     0xb2d224: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2d228: stur            x2, [fp, #-0x28]
    // 0xb2d22c: StoreField: r2->field_f = r0
    //     0xb2d22c: stur            w0, [x2, #0xf]
    // 0xb2d230: r1 = Instance_MainAxisAlignment
    //     0xb2d230: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2d234: ldr             x1, [x1, #0xa08]
    // 0xb2d238: StoreField: r2->field_13 = r1
    //     0xb2d238: stur            w1, [x2, #0x13]
    // 0xb2d23c: r3 = Instance_MainAxisSize
    //     0xb2d23c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d240: ldr             x3, [x3, #0xa10]
    // 0xb2d244: ArrayStore: r2[0] = r3  ; List_4
    //     0xb2d244: stur            w3, [x2, #0x17]
    // 0xb2d248: r1 = Instance_CrossAxisAlignment
    //     0xb2d248: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2d24c: ldr             x1, [x1, #0xa18]
    // 0xb2d250: StoreField: r2->field_1b = r1
    //     0xb2d250: stur            w1, [x2, #0x1b]
    // 0xb2d254: r4 = Instance_VerticalDirection
    //     0xb2d254: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d258: ldr             x4, [x4, #0xa20]
    // 0xb2d25c: StoreField: r2->field_23 = r4
    //     0xb2d25c: stur            w4, [x2, #0x23]
    // 0xb2d260: r5 = Instance_Clip
    //     0xb2d260: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d264: ldr             x5, [x5, #0x38]
    // 0xb2d268: StoreField: r2->field_2b = r5
    //     0xb2d268: stur            w5, [x2, #0x2b]
    // 0xb2d26c: StoreField: r2->field_2f = rZR
    //     0xb2d26c: stur            xzr, [x2, #0x2f]
    // 0xb2d270: ldur            x1, [fp, #-0x10]
    // 0xb2d274: StoreField: r2->field_b = r1
    //     0xb2d274: stur            w1, [x2, #0xb]
    // 0xb2d278: r1 = <FlexParentData>
    //     0xb2d278: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2d27c: ldr             x1, [x1, #0xe00]
    // 0xb2d280: r0 = Expanded()
    //     0xb2d280: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2d284: mov             x2, x0
    // 0xb2d288: r0 = 1
    //     0xb2d288: movz            x0, #0x1
    // 0xb2d28c: stur            x2, [fp, #-0x30]
    // 0xb2d290: StoreField: r2->field_13 = r0
    //     0xb2d290: stur            x0, [x2, #0x13]
    // 0xb2d294: r0 = Instance_FlexFit
    //     0xb2d294: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2d298: ldr             x0, [x0, #0xe08]
    // 0xb2d29c: StoreField: r2->field_1b = r0
    //     0xb2d29c: stur            w0, [x2, #0x1b]
    // 0xb2d2a0: ldur            x0, [fp, #-0x28]
    // 0xb2d2a4: StoreField: r2->field_b = r0
    //     0xb2d2a4: stur            w0, [x2, #0xb]
    // 0xb2d2a8: ldur            x0, [fp, #-0x18]
    // 0xb2d2ac: tbnz            w0, #4, #0xb2d2d4
    // 0xb2d2b0: ldur            x1, [fp, #-8]
    // 0xb2d2b4: LoadField: r3 = r1->field_b
    //     0xb2d2b4: ldur            w3, [x1, #0xb]
    // 0xb2d2b8: DecompressPointer r3
    //     0xb2d2b8: add             x3, x3, HEAP, lsl #32
    // 0xb2d2bc: cmp             w3, NULL
    // 0xb2d2c0: b.eq            #0xb2d494
    // 0xb2d2c4: LoadField: r1 = r3->field_13
    //     0xb2d2c4: ldur            w1, [x3, #0x13]
    // 0xb2d2c8: DecompressPointer r1
    //     0xb2d2c8: add             x1, x1, HEAP, lsl #32
    // 0xb2d2cc: mov             x3, x1
    // 0xb2d2d0: b               #0xb2d2f4
    // 0xb2d2d4: ldur            x1, [fp, #-8]
    // 0xb2d2d8: LoadField: r3 = r1->field_b
    //     0xb2d2d8: ldur            w3, [x1, #0xb]
    // 0xb2d2dc: DecompressPointer r3
    //     0xb2d2dc: add             x3, x3, HEAP, lsl #32
    // 0xb2d2e0: cmp             w3, NULL
    // 0xb2d2e4: b.eq            #0xb2d498
    // 0xb2d2e8: LoadField: r1 = r3->field_f
    //     0xb2d2e8: ldur            w1, [x3, #0xf]
    // 0xb2d2ec: DecompressPointer r1
    //     0xb2d2ec: add             x1, x1, HEAP, lsl #32
    // 0xb2d2f0: mov             x3, x1
    // 0xb2d2f4: stur            x3, [fp, #-0x10]
    // 0xb2d2f8: tbnz            w0, #4, #0xb2d308
    // 0xb2d2fc: r4 = "Remove"
    //     0xb2d2fc: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0xb2d300: ldr             x4, [x4, #0x7d0]
    // 0xb2d304: b               #0xb2d310
    // 0xb2d308: r4 = "Add"
    //     0xb2d308: add             x4, PP, #0x54, lsl #12  ; [pp+0x54a70] "Add"
    //     0xb2d30c: ldr             x4, [x4, #0xa70]
    // 0xb2d310: ldur            x0, [fp, #-0x20]
    // 0xb2d314: stur            x4, [fp, #-8]
    // 0xb2d318: LoadField: r1 = r0->field_13
    //     0xb2d318: ldur            w1, [x0, #0x13]
    // 0xb2d31c: DecompressPointer r1
    //     0xb2d31c: add             x1, x1, HEAP, lsl #32
    // 0xb2d320: r0 = of()
    //     0xb2d320: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2d324: LoadField: r1 = r0->field_87
    //     0xb2d324: ldur            w1, [x0, #0x87]
    // 0xb2d328: DecompressPointer r1
    //     0xb2d328: add             x1, x1, HEAP, lsl #32
    // 0xb2d32c: LoadField: r0 = r1->field_7
    //     0xb2d32c: ldur            w0, [x1, #7]
    // 0xb2d330: DecompressPointer r0
    //     0xb2d330: add             x0, x0, HEAP, lsl #32
    // 0xb2d334: r16 = 12.000000
    //     0xb2d334: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2d338: ldr             x16, [x16, #0x9e8]
    // 0xb2d33c: r30 = Instance_Color
    //     0xb2d33c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb2d340: ldr             lr, [lr, #0x858]
    // 0xb2d344: stp             lr, x16, [SP]
    // 0xb2d348: mov             x1, x0
    // 0xb2d34c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2d34c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2d350: ldr             x4, [x4, #0xaa0]
    // 0xb2d354: r0 = copyWith()
    //     0xb2d354: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2d358: stur            x0, [fp, #-0x18]
    // 0xb2d35c: r0 = Text()
    //     0xb2d35c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2d360: mov             x1, x0
    // 0xb2d364: ldur            x0, [fp, #-8]
    // 0xb2d368: stur            x1, [fp, #-0x20]
    // 0xb2d36c: StoreField: r1->field_b = r0
    //     0xb2d36c: stur            w0, [x1, #0xb]
    // 0xb2d370: ldur            x0, [fp, #-0x18]
    // 0xb2d374: StoreField: r1->field_13 = r0
    //     0xb2d374: stur            w0, [x1, #0x13]
    // 0xb2d378: r0 = InkWell()
    //     0xb2d378: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2d37c: mov             x1, x0
    // 0xb2d380: ldur            x0, [fp, #-0x20]
    // 0xb2d384: stur            x1, [fp, #-8]
    // 0xb2d388: StoreField: r1->field_b = r0
    //     0xb2d388: stur            w0, [x1, #0xb]
    // 0xb2d38c: ldur            x0, [fp, #-0x10]
    // 0xb2d390: StoreField: r1->field_f = r0
    //     0xb2d390: stur            w0, [x1, #0xf]
    // 0xb2d394: r0 = true
    //     0xb2d394: add             x0, NULL, #0x20  ; true
    // 0xb2d398: StoreField: r1->field_43 = r0
    //     0xb2d398: stur            w0, [x1, #0x43]
    // 0xb2d39c: r2 = Instance_BoxShape
    //     0xb2d39c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2d3a0: ldr             x2, [x2, #0x80]
    // 0xb2d3a4: StoreField: r1->field_47 = r2
    //     0xb2d3a4: stur            w2, [x1, #0x47]
    // 0xb2d3a8: StoreField: r1->field_6f = r0
    //     0xb2d3a8: stur            w0, [x1, #0x6f]
    // 0xb2d3ac: r2 = false
    //     0xb2d3ac: add             x2, NULL, #0x30  ; false
    // 0xb2d3b0: StoreField: r1->field_73 = r2
    //     0xb2d3b0: stur            w2, [x1, #0x73]
    // 0xb2d3b4: StoreField: r1->field_83 = r0
    //     0xb2d3b4: stur            w0, [x1, #0x83]
    // 0xb2d3b8: StoreField: r1->field_7b = r2
    //     0xb2d3b8: stur            w2, [x1, #0x7b]
    // 0xb2d3bc: r0 = Padding()
    //     0xb2d3bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2d3c0: mov             x3, x0
    // 0xb2d3c4: r0 = Instance_EdgeInsets
    //     0xb2d3c4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xb2d3c8: ldr             x0, [x0, #0xa40]
    // 0xb2d3cc: stur            x3, [fp, #-0x10]
    // 0xb2d3d0: StoreField: r3->field_f = r0
    //     0xb2d3d0: stur            w0, [x3, #0xf]
    // 0xb2d3d4: ldur            x0, [fp, #-8]
    // 0xb2d3d8: StoreField: r3->field_b = r0
    //     0xb2d3d8: stur            w0, [x3, #0xb]
    // 0xb2d3dc: r1 = Null
    //     0xb2d3dc: mov             x1, NULL
    // 0xb2d3e0: r2 = 4
    //     0xb2d3e0: movz            x2, #0x4
    // 0xb2d3e4: r0 = AllocateArray()
    //     0xb2d3e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d3e8: mov             x2, x0
    // 0xb2d3ec: ldur            x0, [fp, #-0x30]
    // 0xb2d3f0: stur            x2, [fp, #-8]
    // 0xb2d3f4: StoreField: r2->field_f = r0
    //     0xb2d3f4: stur            w0, [x2, #0xf]
    // 0xb2d3f8: ldur            x0, [fp, #-0x10]
    // 0xb2d3fc: StoreField: r2->field_13 = r0
    //     0xb2d3fc: stur            w0, [x2, #0x13]
    // 0xb2d400: r1 = <Widget>
    //     0xb2d400: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d404: r0 = AllocateGrowableArray()
    //     0xb2d404: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d408: mov             x1, x0
    // 0xb2d40c: ldur            x0, [fp, #-8]
    // 0xb2d410: stur            x1, [fp, #-0x10]
    // 0xb2d414: StoreField: r1->field_f = r0
    //     0xb2d414: stur            w0, [x1, #0xf]
    // 0xb2d418: r0 = 4
    //     0xb2d418: movz            x0, #0x4
    // 0xb2d41c: StoreField: r1->field_b = r0
    //     0xb2d41c: stur            w0, [x1, #0xb]
    // 0xb2d420: r0 = Row()
    //     0xb2d420: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2d424: r1 = Instance_Axis
    //     0xb2d424: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2d428: StoreField: r0->field_f = r1
    //     0xb2d428: stur            w1, [x0, #0xf]
    // 0xb2d42c: r1 = Instance_MainAxisAlignment
    //     0xb2d42c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb2d430: ldr             x1, [x1, #0xa8]
    // 0xb2d434: StoreField: r0->field_13 = r1
    //     0xb2d434: stur            w1, [x0, #0x13]
    // 0xb2d438: r1 = Instance_MainAxisSize
    //     0xb2d438: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d43c: ldr             x1, [x1, #0xa10]
    // 0xb2d440: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2d440: stur            w1, [x0, #0x17]
    // 0xb2d444: r1 = Instance_CrossAxisAlignment
    //     0xb2d444: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2d448: ldr             x1, [x1, #0x890]
    // 0xb2d44c: StoreField: r0->field_1b = r1
    //     0xb2d44c: stur            w1, [x0, #0x1b]
    // 0xb2d450: r1 = Instance_VerticalDirection
    //     0xb2d450: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d454: ldr             x1, [x1, #0xa20]
    // 0xb2d458: StoreField: r0->field_23 = r1
    //     0xb2d458: stur            w1, [x0, #0x23]
    // 0xb2d45c: r1 = Instance_Clip
    //     0xb2d45c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d460: ldr             x1, [x1, #0x38]
    // 0xb2d464: StoreField: r0->field_2b = r1
    //     0xb2d464: stur            w1, [x0, #0x2b]
    // 0xb2d468: StoreField: r0->field_2f = rZR
    //     0xb2d468: stur            xzr, [x0, #0x2f]
    // 0xb2d46c: ldur            x1, [fp, #-0x10]
    // 0xb2d470: StoreField: r0->field_b = r1
    //     0xb2d470: stur            w1, [x0, #0xb]
    // 0xb2d474: LeaveFrame
    //     0xb2d474: mov             SP, fp
    //     0xb2d478: ldp             fp, lr, [SP], #0x10
    // 0xb2d47c: ret
    //     0xb2d47c: ret             
    // 0xb2d480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2d480: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2d484: b               #0xb2cb9c
    // 0xb2d488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d488: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2d48c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d48c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2d490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d490: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2d494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d494: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2d498: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d498: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2d49c, size: 0x50
    // 0xb2d49c: EnterFrame
    //     0xb2d49c: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d4a0: mov             fp, SP
    // 0xb2d4a4: ldr             x0, [fp, #0x10]
    // 0xb2d4a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2d4a8: ldur            w1, [x0, #0x17]
    // 0xb2d4ac: DecompressPointer r1
    //     0xb2d4ac: add             x1, x1, HEAP, lsl #32
    // 0xb2d4b0: CheckStackOverflow
    //     0xb2d4b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d4b4: cmp             SP, x16
    //     0xb2d4b8: b.ls            #0xb2d4e4
    // 0xb2d4bc: LoadField: r0 = r1->field_f
    //     0xb2d4bc: ldur            w0, [x1, #0xf]
    // 0xb2d4c0: DecompressPointer r0
    //     0xb2d4c0: add             x0, x0, HEAP, lsl #32
    // 0xb2d4c4: LoadField: r2 = r1->field_13
    //     0xb2d4c4: ldur            w2, [x1, #0x13]
    // 0xb2d4c8: DecompressPointer r2
    //     0xb2d4c8: add             x2, x2, HEAP, lsl #32
    // 0xb2d4cc: mov             x1, x0
    // 0xb2d4d0: r0 = _showFreeGiftDialog()
    //     0xb2d4d0: bl              #0xb2d4ec  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_showFreeGiftDialog
    // 0xb2d4d4: r0 = Null
    //     0xb2d4d4: mov             x0, NULL
    // 0xb2d4d8: LeaveFrame
    //     0xb2d4d8: mov             SP, fp
    //     0xb2d4dc: ldp             fp, lr, [SP], #0x10
    // 0xb2d4e0: ret
    //     0xb2d4e0: ret             
    // 0xb2d4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2d4e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2d4e8: b               #0xb2d4bc
  }
  _ _showFreeGiftDialog(/* No info */) {
    // ** addr: 0xb2d4ec, size: 0xdc
    // 0xb2d4ec: EnterFrame
    //     0xb2d4ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d4f0: mov             fp, SP
    // 0xb2d4f4: AllocStack(0x30)
    //     0xb2d4f4: sub             SP, SP, #0x30
    // 0xb2d4f8: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb2d4f8: stur            x2, [fp, #-0x10]
    // 0xb2d4fc: CheckStackOverflow
    //     0xb2d4fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d500: cmp             SP, x16
    //     0xb2d504: b.ls            #0xb2d5bc
    // 0xb2d508: LoadField: r0 = r1->field_b
    //     0xb2d508: ldur            w0, [x1, #0xb]
    // 0xb2d50c: DecompressPointer r0
    //     0xb2d50c: add             x0, x0, HEAP, lsl #32
    // 0xb2d510: cmp             w0, NULL
    // 0xb2d514: b.eq            #0xb2d5c4
    // 0xb2d518: LoadField: r1 = r0->field_2b
    //     0xb2d518: ldur            w1, [x0, #0x2b]
    // 0xb2d51c: DecompressPointer r1
    //     0xb2d51c: add             x1, x1, HEAP, lsl #32
    // 0xb2d520: LoadField: r0 = r1->field_b
    //     0xb2d520: ldur            w0, [x1, #0xb]
    // 0xb2d524: DecompressPointer r0
    //     0xb2d524: add             x0, x0, HEAP, lsl #32
    // 0xb2d528: stur            x0, [fp, #-8]
    // 0xb2d52c: r1 = 2
    //     0xb2d52c: movz            x1, #0x2
    // 0xb2d530: r0 = AllocateContext()
    //     0xb2d530: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2d534: mov             x1, x0
    // 0xb2d538: ldur            x0, [fp, #-8]
    // 0xb2d53c: StoreField: r1->field_f = r0
    //     0xb2d53c: stur            w0, [x1, #0xf]
    // 0xb2d540: cmp             w0, NULL
    // 0xb2d544: b.ne            #0xb2d550
    // 0xb2d548: r0 = Null
    //     0xb2d548: mov             x0, NULL
    // 0xb2d54c: b               #0xb2d55c
    // 0xb2d550: LoadField: r2 = r0->field_7
    //     0xb2d550: ldur            w2, [x0, #7]
    // 0xb2d554: DecompressPointer r2
    //     0xb2d554: add             x2, x2, HEAP, lsl #32
    // 0xb2d558: LoadField: r0 = r2->field_b
    //     0xb2d558: ldur            w0, [x2, #0xb]
    // 0xb2d55c: cmp             w0, NULL
    // 0xb2d560: b.ne            #0xb2d56c
    // 0xb2d564: r0 = 0
    //     0xb2d564: movz            x0, #0
    // 0xb2d568: b               #0xb2d574
    // 0xb2d56c: r2 = LoadInt32Instr(r0)
    //     0xb2d56c: sbfx            x2, x0, #1, #0x1f
    // 0xb2d570: mov             x0, x2
    // 0xb2d574: lsl             x2, x0, #1
    // 0xb2d578: StoreField: r1->field_13 = r2
    //     0xb2d578: stur            w2, [x1, #0x13]
    // 0xb2d57c: mov             x2, x1
    // 0xb2d580: r1 = Function '<anonymous closure>':.
    //     0xb2d580: add             x1, PP, #0x57, lsl #12  ; [pp+0x572f0] AnonymousClosure: (0xb2d5c8), in [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_showFreeGiftDialog (0xb2d4ec)
    //     0xb2d584: ldr             x1, [x1, #0x2f0]
    // 0xb2d588: r0 = AllocateClosure()
    //     0xb2d588: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2d58c: r16 = <void?>
    //     0xb2d58c: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0xb2d590: stp             x0, x16, [SP, #0x10]
    // 0xb2d594: ldur            x16, [fp, #-0x10]
    // 0xb2d598: r30 = false
    //     0xb2d598: add             lr, NULL, #0x30  ; false
    // 0xb2d59c: stp             lr, x16, [SP]
    // 0xb2d5a0: r4 = const [0x1, 0x3, 0x3, 0x2, barrierDismissible, 0x2, null]
    //     0xb2d5a0: add             x4, PP, #0x52, lsl #12  ; [pp+0x528d8] List(7) [0x1, 0x3, 0x3, 0x2, "barrierDismissible", 0x2, Null]
    //     0xb2d5a4: ldr             x4, [x4, #0x8d8]
    // 0xb2d5a8: r0 = showDialog()
    //     0xb2d5a8: bl              #0x99c870  ; [package:flutter/src/material/dialog.dart] ::showDialog
    // 0xb2d5ac: r0 = Null
    //     0xb2d5ac: mov             x0, NULL
    // 0xb2d5b0: LeaveFrame
    //     0xb2d5b0: mov             SP, fp
    //     0xb2d5b4: ldp             fp, lr, [SP], #0x10
    // 0xb2d5b8: ret
    //     0xb2d5b8: ret             
    // 0xb2d5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2d5bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2d5c0: b               #0xb2d508
    // 0xb2d5c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d5c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] _FreeGiftDialog <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb2d5c8, size: 0x50
    // 0xb2d5c8: EnterFrame
    //     0xb2d5c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d5cc: mov             fp, SP
    // 0xb2d5d0: AllocStack(0x10)
    //     0xb2d5d0: sub             SP, SP, #0x10
    // 0xb2d5d4: SetupParameters()
    //     0xb2d5d4: ldr             x0, [fp, #0x18]
    //     0xb2d5d8: ldur            w1, [x0, #0x17]
    //     0xb2d5dc: add             x1, x1, HEAP, lsl #32
    // 0xb2d5e0: LoadField: r0 = r1->field_f
    //     0xb2d5e0: ldur            w0, [x1, #0xf]
    // 0xb2d5e4: DecompressPointer r0
    //     0xb2d5e4: add             x0, x0, HEAP, lsl #32
    // 0xb2d5e8: stur            x0, [fp, #-0x10]
    // 0xb2d5ec: LoadField: r2 = r1->field_13
    //     0xb2d5ec: ldur            w2, [x1, #0x13]
    // 0xb2d5f0: stur            x2, [fp, #-8]
    // 0xb2d5f4: r0 = _FreeGiftDialog()
    //     0xb2d5f4: bl              #0xb2d618  ; Allocate_FreeGiftDialogStub -> _FreeGiftDialog (size=0x18)
    // 0xb2d5f8: ldur            x1, [fp, #-0x10]
    // 0xb2d5fc: StoreField: r0->field_b = r1
    //     0xb2d5fc: stur            w1, [x0, #0xb]
    // 0xb2d600: ldur            x1, [fp, #-8]
    // 0xb2d604: r2 = LoadInt32Instr(r1)
    //     0xb2d604: sbfx            x2, x1, #1, #0x1f
    // 0xb2d608: StoreField: r0->field_f = r2
    //     0xb2d608: stur            x2, [x0, #0xf]
    // 0xb2d60c: LeaveFrame
    //     0xb2d60c: mov             SP, fp
    //     0xb2d610: ldp             fp, lr, [SP], #0x10
    // 0xb2d614: ret
    //     0xb2d614: ret             
  }
  _ _buildExchangeSection(/* No info */) {
    // ** addr: 0xb2d624, size: 0x138
    // 0xb2d624: EnterFrame
    //     0xb2d624: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d628: mov             fp, SP
    // 0xb2d62c: AllocStack(0x18)
    //     0xb2d62c: sub             SP, SP, #0x18
    // 0xb2d630: SetupParameters(_BagItemViewWidgetState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb2d630: mov             x3, x1
    //     0xb2d634: mov             x0, x2
    //     0xb2d638: stur            x1, [fp, #-8]
    //     0xb2d63c: stur            x2, [fp, #-0x10]
    // 0xb2d640: CheckStackOverflow
    //     0xb2d640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d644: cmp             SP, x16
    //     0xb2d648: b.ls            #0xb2d750
    // 0xb2d64c: LoadField: r1 = r3->field_b
    //     0xb2d64c: ldur            w1, [x3, #0xb]
    // 0xb2d650: DecompressPointer r1
    //     0xb2d650: add             x1, x1, HEAP, lsl #32
    // 0xb2d654: cmp             w1, NULL
    // 0xb2d658: b.eq            #0xb2d758
    // 0xb2d65c: LoadField: r2 = r1->field_2f
    //     0xb2d65c: ldur            w2, [x1, #0x2f]
    // 0xb2d660: DecompressPointer r2
    //     0xb2d660: add             x2, x2, HEAP, lsl #32
    // 0xb2d664: LoadField: r4 = r2->field_7
    //     0xb2d664: ldur            w4, [x2, #7]
    // 0xb2d668: cbz             w4, #0xb2d740
    // 0xb2d66c: LoadField: r2 = r1->field_33
    //     0xb2d66c: ldur            w2, [x1, #0x33]
    // 0xb2d670: DecompressPointer r2
    //     0xb2d670: add             x2, x2, HEAP, lsl #32
    // 0xb2d674: LoadField: r1 = r2->field_7
    //     0xb2d674: ldur            w1, [x2, #7]
    // 0xb2d678: cbz             w1, #0xb2d740
    // 0xb2d67c: mov             x1, x3
    // 0xb2d680: mov             x2, x0
    // 0xb2d684: r0 = _buildExchangeCard()
    //     0xb2d684: bl              #0xb2d8ec  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildExchangeCard
    // 0xb2d688: ldur            x1, [fp, #-8]
    // 0xb2d68c: ldur            x2, [fp, #-0x10]
    // 0xb2d690: stur            x0, [fp, #-8]
    // 0xb2d694: r0 = _buildExchangeInfo()
    //     0xb2d694: bl              #0xb2d75c  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildExchangeInfo
    // 0xb2d698: r1 = Null
    //     0xb2d698: mov             x1, NULL
    // 0xb2d69c: r2 = 4
    //     0xb2d69c: movz            x2, #0x4
    // 0xb2d6a0: stur            x0, [fp, #-0x10]
    // 0xb2d6a4: r0 = AllocateArray()
    //     0xb2d6a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d6a8: mov             x2, x0
    // 0xb2d6ac: ldur            x0, [fp, #-8]
    // 0xb2d6b0: stur            x2, [fp, #-0x18]
    // 0xb2d6b4: StoreField: r2->field_f = r0
    //     0xb2d6b4: stur            w0, [x2, #0xf]
    // 0xb2d6b8: ldur            x0, [fp, #-0x10]
    // 0xb2d6bc: StoreField: r2->field_13 = r0
    //     0xb2d6bc: stur            w0, [x2, #0x13]
    // 0xb2d6c0: r1 = <Widget>
    //     0xb2d6c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d6c4: r0 = AllocateGrowableArray()
    //     0xb2d6c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d6c8: mov             x1, x0
    // 0xb2d6cc: ldur            x0, [fp, #-0x18]
    // 0xb2d6d0: stur            x1, [fp, #-8]
    // 0xb2d6d4: StoreField: r1->field_f = r0
    //     0xb2d6d4: stur            w0, [x1, #0xf]
    // 0xb2d6d8: r0 = 4
    //     0xb2d6d8: movz            x0, #0x4
    // 0xb2d6dc: StoreField: r1->field_b = r0
    //     0xb2d6dc: stur            w0, [x1, #0xb]
    // 0xb2d6e0: r0 = Column()
    //     0xb2d6e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2d6e4: r1 = Instance_Axis
    //     0xb2d6e4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2d6e8: StoreField: r0->field_f = r1
    //     0xb2d6e8: stur            w1, [x0, #0xf]
    // 0xb2d6ec: r1 = Instance_MainAxisAlignment
    //     0xb2d6ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2d6f0: ldr             x1, [x1, #0xa08]
    // 0xb2d6f4: StoreField: r0->field_13 = r1
    //     0xb2d6f4: stur            w1, [x0, #0x13]
    // 0xb2d6f8: r1 = Instance_MainAxisSize
    //     0xb2d6f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d6fc: ldr             x1, [x1, #0xa10]
    // 0xb2d700: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2d700: stur            w1, [x0, #0x17]
    // 0xb2d704: r1 = Instance_CrossAxisAlignment
    //     0xb2d704: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2d708: ldr             x1, [x1, #0xa18]
    // 0xb2d70c: StoreField: r0->field_1b = r1
    //     0xb2d70c: stur            w1, [x0, #0x1b]
    // 0xb2d710: r1 = Instance_VerticalDirection
    //     0xb2d710: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d714: ldr             x1, [x1, #0xa20]
    // 0xb2d718: StoreField: r0->field_23 = r1
    //     0xb2d718: stur            w1, [x0, #0x23]
    // 0xb2d71c: r1 = Instance_Clip
    //     0xb2d71c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d720: ldr             x1, [x1, #0x38]
    // 0xb2d724: StoreField: r0->field_2b = r1
    //     0xb2d724: stur            w1, [x0, #0x2b]
    // 0xb2d728: StoreField: r0->field_2f = rZR
    //     0xb2d728: stur            xzr, [x0, #0x2f]
    // 0xb2d72c: ldur            x1, [fp, #-8]
    // 0xb2d730: StoreField: r0->field_b = r1
    //     0xb2d730: stur            w1, [x0, #0xb]
    // 0xb2d734: LeaveFrame
    //     0xb2d734: mov             SP, fp
    //     0xb2d738: ldp             fp, lr, [SP], #0x10
    // 0xb2d73c: ret
    //     0xb2d73c: ret             
    // 0xb2d740: r0 = Instance_SizedBox
    //     0xb2d740: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb2d744: LeaveFrame
    //     0xb2d744: mov             SP, fp
    //     0xb2d748: ldp             fp, lr, [SP], #0x10
    // 0xb2d74c: ret
    //     0xb2d74c: ret             
    // 0xb2d750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2d750: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2d754: b               #0xb2d64c
    // 0xb2d758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2d758: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildExchangeInfo(/* No info */) {
    // ** addr: 0xb2d75c, size: 0x190
    // 0xb2d75c: EnterFrame
    //     0xb2d75c: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d760: mov             fp, SP
    // 0xb2d764: AllocStack(0x20)
    //     0xb2d764: sub             SP, SP, #0x20
    // 0xb2d768: SetupParameters(_BagItemViewWidgetState this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xb2d768: mov             x0, x1
    //     0xb2d76c: mov             x1, x2
    // 0xb2d770: CheckStackOverflow
    //     0xb2d770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d774: cmp             SP, x16
    //     0xb2d778: b.ls            #0xb2d8e4
    // 0xb2d77c: r0 = of()
    //     0xb2d77c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2d780: LoadField: r1 = r0->field_87
    //     0xb2d780: ldur            w1, [x0, #0x87]
    // 0xb2d784: DecompressPointer r1
    //     0xb2d784: add             x1, x1, HEAP, lsl #32
    // 0xb2d788: LoadField: r0 = r1->field_27
    //     0xb2d788: ldur            w0, [x1, #0x27]
    // 0xb2d78c: DecompressPointer r0
    //     0xb2d78c: add             x0, x0, HEAP, lsl #32
    // 0xb2d790: stur            x0, [fp, #-8]
    // 0xb2d794: r1 = Instance_Color
    //     0xb2d794: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2d798: d0 = 0.700000
    //     0xb2d798: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2d79c: ldr             d0, [x17, #0xf48]
    // 0xb2d7a0: r0 = withOpacity()
    //     0xb2d7a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2d7a4: r16 = 12.000000
    //     0xb2d7a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2d7a8: ldr             x16, [x16, #0x9e8]
    // 0xb2d7ac: stp             x16, x0, [SP]
    // 0xb2d7b0: ldur            x1, [fp, #-8]
    // 0xb2d7b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb2d7b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb2d7b8: ldr             x4, [x4, #0x9b8]
    // 0xb2d7bc: r0 = copyWith()
    //     0xb2d7bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2d7c0: stur            x0, [fp, #-8]
    // 0xb2d7c4: r0 = Text()
    //     0xb2d7c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2d7c8: mov             x2, x0
    // 0xb2d7cc: r0 = "You can only select one product for exchange"
    //     0xb2d7cc: add             x0, PP, #0x54, lsl #12  ; [pp+0x54c70] "You can only select one product for exchange"
    //     0xb2d7d0: ldr             x0, [x0, #0xc70]
    // 0xb2d7d4: stur            x2, [fp, #-0x10]
    // 0xb2d7d8: StoreField: r2->field_b = r0
    //     0xb2d7d8: stur            w0, [x2, #0xb]
    // 0xb2d7dc: ldur            x0, [fp, #-8]
    // 0xb2d7e0: StoreField: r2->field_13 = r0
    //     0xb2d7e0: stur            w0, [x2, #0x13]
    // 0xb2d7e4: r1 = <FlexParentData>
    //     0xb2d7e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2d7e8: ldr             x1, [x1, #0xe00]
    // 0xb2d7ec: r0 = Flexible()
    //     0xb2d7ec: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb2d7f0: mov             x3, x0
    // 0xb2d7f4: r0 = 1
    //     0xb2d7f4: movz            x0, #0x1
    // 0xb2d7f8: stur            x3, [fp, #-8]
    // 0xb2d7fc: StoreField: r3->field_13 = r0
    //     0xb2d7fc: stur            x0, [x3, #0x13]
    // 0xb2d800: r0 = Instance_FlexFit
    //     0xb2d800: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xb2d804: ldr             x0, [x0, #0xe20]
    // 0xb2d808: StoreField: r3->field_1b = r0
    //     0xb2d808: stur            w0, [x3, #0x1b]
    // 0xb2d80c: ldur            x0, [fp, #-0x10]
    // 0xb2d810: StoreField: r3->field_b = r0
    //     0xb2d810: stur            w0, [x3, #0xb]
    // 0xb2d814: r1 = Null
    //     0xb2d814: mov             x1, NULL
    // 0xb2d818: r2 = 6
    //     0xb2d818: movz            x2, #0x6
    // 0xb2d81c: r0 = AllocateArray()
    //     0xb2d81c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2d820: stur            x0, [fp, #-0x10]
    // 0xb2d824: r16 = Instance_Icon
    //     0xb2d824: add             x16, PP, #0x57, lsl #12  ; [pp+0x572f8] Obj!Icon@d66071
    //     0xb2d828: ldr             x16, [x16, #0x2f8]
    // 0xb2d82c: StoreField: r0->field_f = r16
    //     0xb2d82c: stur            w16, [x0, #0xf]
    // 0xb2d830: r16 = Instance_SizedBox
    //     0xb2d830: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb2d834: ldr             x16, [x16, #0x998]
    // 0xb2d838: StoreField: r0->field_13 = r16
    //     0xb2d838: stur            w16, [x0, #0x13]
    // 0xb2d83c: ldur            x1, [fp, #-8]
    // 0xb2d840: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2d840: stur            w1, [x0, #0x17]
    // 0xb2d844: r1 = <Widget>
    //     0xb2d844: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2d848: r0 = AllocateGrowableArray()
    //     0xb2d848: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2d84c: mov             x1, x0
    // 0xb2d850: ldur            x0, [fp, #-0x10]
    // 0xb2d854: stur            x1, [fp, #-8]
    // 0xb2d858: StoreField: r1->field_f = r0
    //     0xb2d858: stur            w0, [x1, #0xf]
    // 0xb2d85c: r0 = 6
    //     0xb2d85c: movz            x0, #0x6
    // 0xb2d860: StoreField: r1->field_b = r0
    //     0xb2d860: stur            w0, [x1, #0xb]
    // 0xb2d864: r0 = Row()
    //     0xb2d864: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2d868: mov             x1, x0
    // 0xb2d86c: r0 = Instance_Axis
    //     0xb2d86c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2d870: stur            x1, [fp, #-0x10]
    // 0xb2d874: StoreField: r1->field_f = r0
    //     0xb2d874: stur            w0, [x1, #0xf]
    // 0xb2d878: r0 = Instance_MainAxisAlignment
    //     0xb2d878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2d87c: ldr             x0, [x0, #0xa08]
    // 0xb2d880: StoreField: r1->field_13 = r0
    //     0xb2d880: stur            w0, [x1, #0x13]
    // 0xb2d884: r0 = Instance_MainAxisSize
    //     0xb2d884: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2d888: ldr             x0, [x0, #0xa10]
    // 0xb2d88c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2d88c: stur            w0, [x1, #0x17]
    // 0xb2d890: r0 = Instance_CrossAxisAlignment
    //     0xb2d890: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2d894: ldr             x0, [x0, #0xa18]
    // 0xb2d898: StoreField: r1->field_1b = r0
    //     0xb2d898: stur            w0, [x1, #0x1b]
    // 0xb2d89c: r0 = Instance_VerticalDirection
    //     0xb2d89c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2d8a0: ldr             x0, [x0, #0xa20]
    // 0xb2d8a4: StoreField: r1->field_23 = r0
    //     0xb2d8a4: stur            w0, [x1, #0x23]
    // 0xb2d8a8: r0 = Instance_Clip
    //     0xb2d8a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2d8ac: ldr             x0, [x0, #0x38]
    // 0xb2d8b0: StoreField: r1->field_2b = r0
    //     0xb2d8b0: stur            w0, [x1, #0x2b]
    // 0xb2d8b4: StoreField: r1->field_2f = rZR
    //     0xb2d8b4: stur            xzr, [x1, #0x2f]
    // 0xb2d8b8: ldur            x0, [fp, #-8]
    // 0xb2d8bc: StoreField: r1->field_b = r0
    //     0xb2d8bc: stur            w0, [x1, #0xb]
    // 0xb2d8c0: r0 = Padding()
    //     0xb2d8c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2d8c4: r1 = Instance_EdgeInsets
    //     0xb2d8c4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d70] Obj!EdgeInsets@d56e11
    //     0xb2d8c8: ldr             x1, [x1, #0xd70]
    // 0xb2d8cc: StoreField: r0->field_f = r1
    //     0xb2d8cc: stur            w1, [x0, #0xf]
    // 0xb2d8d0: ldur            x1, [fp, #-0x10]
    // 0xb2d8d4: StoreField: r0->field_b = r1
    //     0xb2d8d4: stur            w1, [x0, #0xb]
    // 0xb2d8d8: LeaveFrame
    //     0xb2d8d8: mov             SP, fp
    //     0xb2d8dc: ldp             fp, lr, [SP], #0x10
    // 0xb2d8e0: ret
    //     0xb2d8e0: ret             
    // 0xb2d8e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2d8e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2d8e8: b               #0xb2d77c
  }
  _ _buildExchangeCard(/* No info */) {
    // ** addr: 0xb2d8ec, size: 0xc60
    // 0xb2d8ec: EnterFrame
    //     0xb2d8ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb2d8f0: mov             fp, SP
    // 0xb2d8f4: AllocStack(0x78)
    //     0xb2d8f4: sub             SP, SP, #0x78
    // 0xb2d8f8: SetupParameters(_BagItemViewWidgetState this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r0, fp-0x20 */)
    //     0xb2d8f8: mov             x0, x2
    //     0xb2d8fc: stur            x2, [fp, #-0x20]
    //     0xb2d900: mov             x2, x1
    //     0xb2d904: stur            x1, [fp, #-0x18]
    // 0xb2d908: CheckStackOverflow
    //     0xb2d908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2d90c: cmp             SP, x16
    //     0xb2d910: b.ls            #0xb2e53c
    // 0xb2d914: LoadField: r1 = r2->field_b
    //     0xb2d914: ldur            w1, [x2, #0xb]
    // 0xb2d918: DecompressPointer r1
    //     0xb2d918: add             x1, x1, HEAP, lsl #32
    // 0xb2d91c: cmp             w1, NULL
    // 0xb2d920: b.eq            #0xb2e544
    // 0xb2d924: LoadField: r3 = r1->field_b
    //     0xb2d924: ldur            w3, [x1, #0xb]
    // 0xb2d928: DecompressPointer r3
    //     0xb2d928: add             x3, x3, HEAP, lsl #32
    // 0xb2d92c: cmp             w3, NULL
    // 0xb2d930: b.ne            #0xb2d93c
    // 0xb2d934: r3 = Null
    //     0xb2d934: mov             x3, NULL
    // 0xb2d938: b               #0xb2d948
    // 0xb2d93c: LoadField: r1 = r3->field_1f
    //     0xb2d93c: ldur            w1, [x3, #0x1f]
    // 0xb2d940: DecompressPointer r1
    //     0xb2d940: add             x1, x1, HEAP, lsl #32
    // 0xb2d944: mov             x3, x1
    // 0xb2d948: stur            x3, [fp, #-0x10]
    // 0xb2d94c: cmp             w3, NULL
    // 0xb2d950: b.ne            #0xb2d95c
    // 0xb2d954: r4 = Null
    //     0xb2d954: mov             x4, NULL
    // 0xb2d958: b               #0xb2d968
    // 0xb2d95c: LoadField: r1 = r3->field_7
    //     0xb2d95c: ldur            w1, [x3, #7]
    // 0xb2d960: DecompressPointer r1
    //     0xb2d960: add             x1, x1, HEAP, lsl #32
    // 0xb2d964: mov             x4, x1
    // 0xb2d968: mov             x1, x0
    // 0xb2d96c: stur            x4, [fp, #-8]
    // 0xb2d970: r0 = of()
    //     0xb2d970: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2d974: LoadField: r1 = r0->field_5b
    //     0xb2d974: ldur            w1, [x0, #0x5b]
    // 0xb2d978: DecompressPointer r1
    //     0xb2d978: add             x1, x1, HEAP, lsl #32
    // 0xb2d97c: stur            x1, [fp, #-0x28]
    // 0xb2d980: r0 = BoxDecoration()
    //     0xb2d980: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2d984: mov             x1, x0
    // 0xb2d988: ldur            x0, [fp, #-0x28]
    // 0xb2d98c: stur            x1, [fp, #-0x30]
    // 0xb2d990: StoreField: r1->field_7 = r0
    //     0xb2d990: stur            w0, [x1, #7]
    // 0xb2d994: r0 = Instance_BorderRadius
    //     0xb2d994: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb2d998: ldr             x0, [x0, #0xe10]
    // 0xb2d99c: StoreField: r1->field_13 = r0
    //     0xb2d99c: stur            w0, [x1, #0x13]
    // 0xb2d9a0: r0 = Instance_BoxShape
    //     0xb2d9a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2d9a4: ldr             x0, [x0, #0x80]
    // 0xb2d9a8: StoreField: r1->field_23 = r0
    //     0xb2d9a8: stur            w0, [x1, #0x23]
    // 0xb2d9ac: r0 = Radius()
    //     0xb2d9ac: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2d9b0: d0 = 12.000000
    //     0xb2d9b0: fmov            d0, #12.00000000
    // 0xb2d9b4: stur            x0, [fp, #-0x28]
    // 0xb2d9b8: StoreField: r0->field_7 = d0
    //     0xb2d9b8: stur            d0, [x0, #7]
    // 0xb2d9bc: StoreField: r0->field_f = d0
    //     0xb2d9bc: stur            d0, [x0, #0xf]
    // 0xb2d9c0: r0 = BorderRadius()
    //     0xb2d9c0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2d9c4: mov             x3, x0
    // 0xb2d9c8: ldur            x0, [fp, #-0x28]
    // 0xb2d9cc: stur            x3, [fp, #-0x38]
    // 0xb2d9d0: StoreField: r3->field_7 = r0
    //     0xb2d9d0: stur            w0, [x3, #7]
    // 0xb2d9d4: StoreField: r3->field_b = r0
    //     0xb2d9d4: stur            w0, [x3, #0xb]
    // 0xb2d9d8: StoreField: r3->field_f = r0
    //     0xb2d9d8: stur            w0, [x3, #0xf]
    // 0xb2d9dc: StoreField: r3->field_13 = r0
    //     0xb2d9dc: stur            w0, [x3, #0x13]
    // 0xb2d9e0: ldur            x0, [fp, #-8]
    // 0xb2d9e4: cmp             w0, NULL
    // 0xb2d9e8: b.ne            #0xb2d9f4
    // 0xb2d9ec: r1 = Null
    //     0xb2d9ec: mov             x1, NULL
    // 0xb2d9f0: b               #0xb2d9fc
    // 0xb2d9f4: LoadField: r1 = r0->field_7
    //     0xb2d9f4: ldur            w1, [x0, #7]
    // 0xb2d9f8: DecompressPointer r1
    //     0xb2d9f8: add             x1, x1, HEAP, lsl #32
    // 0xb2d9fc: cmp             w1, NULL
    // 0xb2da00: b.ne            #0xb2da0c
    // 0xb2da04: r4 = ""
    //     0xb2da04: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2da08: b               #0xb2da10
    // 0xb2da0c: mov             x4, x1
    // 0xb2da10: stur            x4, [fp, #-0x28]
    // 0xb2da14: r1 = Function '<anonymous closure>':.
    //     0xb2da14: add             x1, PP, #0x57, lsl #12  ; [pp+0x57300] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0xb2da18: ldr             x1, [x1, #0x300]
    // 0xb2da1c: r2 = Null
    //     0xb2da1c: mov             x2, NULL
    // 0xb2da20: r0 = AllocateClosure()
    //     0xb2da20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2da24: stur            x0, [fp, #-0x40]
    // 0xb2da28: r0 = CachedNetworkImage()
    //     0xb2da28: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb2da2c: stur            x0, [fp, #-0x48]
    // 0xb2da30: r16 = 84.000000
    //     0xb2da30: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb2da34: ldr             x16, [x16, #0xf90]
    // 0xb2da38: r30 = 84.000000
    //     0xb2da38: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb2da3c: ldr             lr, [lr, #0xf90]
    // 0xb2da40: stp             lr, x16, [SP, #0x10]
    // 0xb2da44: ldur            x16, [fp, #-0x40]
    // 0xb2da48: r30 = Instance_BoxFit
    //     0xb2da48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb2da4c: ldr             lr, [lr, #0x118]
    // 0xb2da50: stp             lr, x16, [SP]
    // 0xb2da54: mov             x1, x0
    // 0xb2da58: ldur            x2, [fp, #-0x28]
    // 0xb2da5c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, height, 0x2, width, 0x3, null]
    //     0xb2da5c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40060] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "height", 0x2, "width", 0x3, Null]
    //     0xb2da60: ldr             x4, [x4, #0x60]
    // 0xb2da64: r0 = CachedNetworkImage()
    //     0xb2da64: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb2da68: r0 = ClipRRect()
    //     0xb2da68: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb2da6c: mov             x2, x0
    // 0xb2da70: ldur            x0, [fp, #-0x38]
    // 0xb2da74: stur            x2, [fp, #-0x28]
    // 0xb2da78: StoreField: r2->field_f = r0
    //     0xb2da78: stur            w0, [x2, #0xf]
    // 0xb2da7c: r0 = Instance_Clip
    //     0xb2da7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb2da80: ldr             x0, [x0, #0x138]
    // 0xb2da84: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2da84: stur            w0, [x2, #0x17]
    // 0xb2da88: ldur            x0, [fp, #-0x48]
    // 0xb2da8c: StoreField: r2->field_b = r0
    //     0xb2da8c: stur            w0, [x2, #0xb]
    // 0xb2da90: ldur            x1, [fp, #-0x20]
    // 0xb2da94: r0 = of()
    //     0xb2da94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2da98: LoadField: r1 = r0->field_87
    //     0xb2da98: ldur            w1, [x0, #0x87]
    // 0xb2da9c: DecompressPointer r1
    //     0xb2da9c: add             x1, x1, HEAP, lsl #32
    // 0xb2daa0: LoadField: r0 = r1->field_7
    //     0xb2daa0: ldur            w0, [x1, #7]
    // 0xb2daa4: DecompressPointer r0
    //     0xb2daa4: add             x0, x0, HEAP, lsl #32
    // 0xb2daa8: r16 = 12.000000
    //     0xb2daa8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2daac: ldr             x16, [x16, #0x9e8]
    // 0xb2dab0: r30 = Instance_Color
    //     0xb2dab0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2dab4: stp             lr, x16, [SP]
    // 0xb2dab8: mov             x1, x0
    // 0xb2dabc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2dabc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2dac0: ldr             x4, [x4, #0xaa0]
    // 0xb2dac4: r0 = copyWith()
    //     0xb2dac4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2dac8: stur            x0, [fp, #-0x38]
    // 0xb2dacc: r0 = Text()
    //     0xb2dacc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2dad0: mov             x2, x0
    // 0xb2dad4: r0 = "Ready for exchange !"
    //     0xb2dad4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54c88] "Ready for exchange !"
    //     0xb2dad8: ldr             x0, [x0, #0xc88]
    // 0xb2dadc: stur            x2, [fp, #-0x40]
    // 0xb2dae0: StoreField: r2->field_b = r0
    //     0xb2dae0: stur            w0, [x2, #0xb]
    // 0xb2dae4: ldur            x0, [fp, #-0x38]
    // 0xb2dae8: StoreField: r2->field_13 = r0
    //     0xb2dae8: stur            w0, [x2, #0x13]
    // 0xb2daec: ldur            x0, [fp, #-8]
    // 0xb2daf0: cmp             w0, NULL
    // 0xb2daf4: b.ne            #0xb2db00
    // 0xb2daf8: r1 = Null
    //     0xb2daf8: mov             x1, NULL
    // 0xb2dafc: b               #0xb2db08
    // 0xb2db00: LoadField: r1 = r0->field_b
    //     0xb2db00: ldur            w1, [x0, #0xb]
    // 0xb2db04: DecompressPointer r1
    //     0xb2db04: add             x1, x1, HEAP, lsl #32
    // 0xb2db08: cmp             w1, NULL
    // 0xb2db0c: b.ne            #0xb2db18
    // 0xb2db10: r3 = ""
    //     0xb2db10: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2db14: b               #0xb2db1c
    // 0xb2db18: mov             x3, x1
    // 0xb2db1c: ldur            x1, [fp, #-0x20]
    // 0xb2db20: stur            x3, [fp, #-0x38]
    // 0xb2db24: r0 = of()
    //     0xb2db24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2db28: LoadField: r1 = r0->field_87
    //     0xb2db28: ldur            w1, [x0, #0x87]
    // 0xb2db2c: DecompressPointer r1
    //     0xb2db2c: add             x1, x1, HEAP, lsl #32
    // 0xb2db30: LoadField: r0 = r1->field_7
    //     0xb2db30: ldur            w0, [x1, #7]
    // 0xb2db34: DecompressPointer r0
    //     0xb2db34: add             x0, x0, HEAP, lsl #32
    // 0xb2db38: r16 = 12.000000
    //     0xb2db38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2db3c: ldr             x16, [x16, #0x9e8]
    // 0xb2db40: r30 = Instance_Color
    //     0xb2db40: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2db44: stp             lr, x16, [SP]
    // 0xb2db48: mov             x1, x0
    // 0xb2db4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2db4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2db50: ldr             x4, [x4, #0xaa0]
    // 0xb2db54: r0 = copyWith()
    //     0xb2db54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2db58: stur            x0, [fp, #-0x48]
    // 0xb2db5c: r0 = Text()
    //     0xb2db5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2db60: mov             x3, x0
    // 0xb2db64: ldur            x0, [fp, #-0x38]
    // 0xb2db68: stur            x3, [fp, #-0x50]
    // 0xb2db6c: StoreField: r3->field_b = r0
    //     0xb2db6c: stur            w0, [x3, #0xb]
    // 0xb2db70: ldur            x0, [fp, #-0x48]
    // 0xb2db74: StoreField: r3->field_13 = r0
    //     0xb2db74: stur            w0, [x3, #0x13]
    // 0xb2db78: r0 = 4
    //     0xb2db78: movz            x0, #0x4
    // 0xb2db7c: StoreField: r3->field_37 = r0
    //     0xb2db7c: stur            w0, [x3, #0x37]
    // 0xb2db80: r1 = Null
    //     0xb2db80: mov             x1, NULL
    // 0xb2db84: r2 = 8
    //     0xb2db84: movz            x2, #0x8
    // 0xb2db88: r0 = AllocateArray()
    //     0xb2db88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2db8c: r16 = "Size: "
    //     0xb2db8c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb2db90: ldr             x16, [x16, #0xf00]
    // 0xb2db94: StoreField: r0->field_f = r16
    //     0xb2db94: stur            w16, [x0, #0xf]
    // 0xb2db98: ldur            x1, [fp, #-8]
    // 0xb2db9c: cmp             w1, NULL
    // 0xb2dba0: b.ne            #0xb2dbac
    // 0xb2dba4: r2 = Null
    //     0xb2dba4: mov             x2, NULL
    // 0xb2dba8: b               #0xb2dbb4
    // 0xb2dbac: LoadField: r2 = r1->field_f
    //     0xb2dbac: ldur            w2, [x1, #0xf]
    // 0xb2dbb0: DecompressPointer r2
    //     0xb2dbb0: add             x2, x2, HEAP, lsl #32
    // 0xb2dbb4: StoreField: r0->field_13 = r2
    //     0xb2dbb4: stur            w2, [x0, #0x13]
    // 0xb2dbb8: r16 = " / Qty: "
    //     0xb2dbb8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb2dbbc: ldr             x16, [x16, #0x760]
    // 0xb2dbc0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb2dbc0: stur            w16, [x0, #0x17]
    // 0xb2dbc4: cmp             w1, NULL
    // 0xb2dbc8: b.ne            #0xb2dbd4
    // 0xb2dbcc: r2 = Null
    //     0xb2dbcc: mov             x2, NULL
    // 0xb2dbd0: b               #0xb2dbdc
    // 0xb2dbd4: LoadField: r2 = r1->field_13
    //     0xb2dbd4: ldur            w2, [x1, #0x13]
    // 0xb2dbd8: DecompressPointer r2
    //     0xb2dbd8: add             x2, x2, HEAP, lsl #32
    // 0xb2dbdc: StoreField: r0->field_1b = r2
    //     0xb2dbdc: stur            w2, [x0, #0x1b]
    // 0xb2dbe0: str             x0, [SP]
    // 0xb2dbe4: r0 = _interpolate()
    //     0xb2dbe4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2dbe8: ldur            x1, [fp, #-0x20]
    // 0xb2dbec: stur            x0, [fp, #-0x38]
    // 0xb2dbf0: r0 = of()
    //     0xb2dbf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2dbf4: LoadField: r1 = r0->field_87
    //     0xb2dbf4: ldur            w1, [x0, #0x87]
    // 0xb2dbf8: DecompressPointer r1
    //     0xb2dbf8: add             x1, x1, HEAP, lsl #32
    // 0xb2dbfc: LoadField: r0 = r1->field_2b
    //     0xb2dbfc: ldur            w0, [x1, #0x2b]
    // 0xb2dc00: DecompressPointer r0
    //     0xb2dc00: add             x0, x0, HEAP, lsl #32
    // 0xb2dc04: r16 = 12.000000
    //     0xb2dc04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2dc08: ldr             x16, [x16, #0x9e8]
    // 0xb2dc0c: r30 = Instance_Color
    //     0xb2dc0c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2dc10: stp             lr, x16, [SP]
    // 0xb2dc14: mov             x1, x0
    // 0xb2dc18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2dc18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2dc1c: ldr             x4, [x4, #0xaa0]
    // 0xb2dc20: r0 = copyWith()
    //     0xb2dc20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2dc24: stur            x0, [fp, #-0x48]
    // 0xb2dc28: r0 = Text()
    //     0xb2dc28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2dc2c: mov             x2, x0
    // 0xb2dc30: ldur            x0, [fp, #-0x38]
    // 0xb2dc34: stur            x2, [fp, #-0x58]
    // 0xb2dc38: StoreField: r2->field_b = r0
    //     0xb2dc38: stur            w0, [x2, #0xb]
    // 0xb2dc3c: ldur            x0, [fp, #-0x48]
    // 0xb2dc40: StoreField: r2->field_13 = r0
    //     0xb2dc40: stur            w0, [x2, #0x13]
    // 0xb2dc44: ldur            x0, [fp, #-8]
    // 0xb2dc48: cmp             w0, NULL
    // 0xb2dc4c: b.ne            #0xb2dc58
    // 0xb2dc50: r0 = Null
    //     0xb2dc50: mov             x0, NULL
    // 0xb2dc54: b               #0xb2dc78
    // 0xb2dc58: LoadField: r1 = r0->field_1b
    //     0xb2dc58: ldur            w1, [x0, #0x1b]
    // 0xb2dc5c: DecompressPointer r1
    //     0xb2dc5c: add             x1, x1, HEAP, lsl #32
    // 0xb2dc60: cmp             w1, NULL
    // 0xb2dc64: b.ne            #0xb2dc70
    // 0xb2dc68: r0 = Null
    //     0xb2dc68: mov             x0, NULL
    // 0xb2dc6c: b               #0xb2dc78
    // 0xb2dc70: LoadField: r0 = r1->field_7
    //     0xb2dc70: ldur            w0, [x1, #7]
    // 0xb2dc74: DecompressPointer r0
    //     0xb2dc74: add             x0, x0, HEAP, lsl #32
    // 0xb2dc78: cmp             w0, NULL
    // 0xb2dc7c: b.ne            #0xb2dc88
    // 0xb2dc80: r7 = ""
    //     0xb2dc80: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2dc84: b               #0xb2dc8c
    // 0xb2dc88: mov             x7, x0
    // 0xb2dc8c: ldur            x5, [fp, #-0x18]
    // 0xb2dc90: ldur            x6, [fp, #-0x10]
    // 0xb2dc94: ldur            x4, [fp, #-0x28]
    // 0xb2dc98: ldur            x3, [fp, #-0x40]
    // 0xb2dc9c: ldur            x0, [fp, #-0x50]
    // 0xb2dca0: ldur            x1, [fp, #-0x20]
    // 0xb2dca4: stur            x7, [fp, #-8]
    // 0xb2dca8: r0 = of()
    //     0xb2dca8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2dcac: LoadField: r1 = r0->field_87
    //     0xb2dcac: ldur            w1, [x0, #0x87]
    // 0xb2dcb0: DecompressPointer r1
    //     0xb2dcb0: add             x1, x1, HEAP, lsl #32
    // 0xb2dcb4: LoadField: r0 = r1->field_7
    //     0xb2dcb4: ldur            w0, [x1, #7]
    // 0xb2dcb8: DecompressPointer r0
    //     0xb2dcb8: add             x0, x0, HEAP, lsl #32
    // 0xb2dcbc: r16 = 12.000000
    //     0xb2dcbc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2dcc0: ldr             x16, [x16, #0x9e8]
    // 0xb2dcc4: r30 = Instance_Color
    //     0xb2dcc4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2dcc8: stp             lr, x16, [SP]
    // 0xb2dccc: mov             x1, x0
    // 0xb2dcd0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2dcd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2dcd4: ldr             x4, [x4, #0xaa0]
    // 0xb2dcd8: r0 = copyWith()
    //     0xb2dcd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2dcdc: stur            x0, [fp, #-0x38]
    // 0xb2dce0: r0 = Text()
    //     0xb2dce0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2dce4: mov             x3, x0
    // 0xb2dce8: ldur            x0, [fp, #-8]
    // 0xb2dcec: stur            x3, [fp, #-0x48]
    // 0xb2dcf0: StoreField: r3->field_b = r0
    //     0xb2dcf0: stur            w0, [x3, #0xb]
    // 0xb2dcf4: ldur            x0, [fp, #-0x38]
    // 0xb2dcf8: StoreField: r3->field_13 = r0
    //     0xb2dcf8: stur            w0, [x3, #0x13]
    // 0xb2dcfc: r1 = Null
    //     0xb2dcfc: mov             x1, NULL
    // 0xb2dd00: r2 = 10
    //     0xb2dd00: movz            x2, #0xa
    // 0xb2dd04: r0 = AllocateArray()
    //     0xb2dd04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2dd08: mov             x2, x0
    // 0xb2dd0c: ldur            x0, [fp, #-0x40]
    // 0xb2dd10: stur            x2, [fp, #-8]
    // 0xb2dd14: StoreField: r2->field_f = r0
    //     0xb2dd14: stur            w0, [x2, #0xf]
    // 0xb2dd18: r16 = Instance_SizedBox
    //     0xb2dd18: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb2dd1c: ldr             x16, [x16, #0xc70]
    // 0xb2dd20: StoreField: r2->field_13 = r16
    //     0xb2dd20: stur            w16, [x2, #0x13]
    // 0xb2dd24: ldur            x0, [fp, #-0x50]
    // 0xb2dd28: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2dd28: stur            w0, [x2, #0x17]
    // 0xb2dd2c: ldur            x0, [fp, #-0x58]
    // 0xb2dd30: StoreField: r2->field_1b = r0
    //     0xb2dd30: stur            w0, [x2, #0x1b]
    // 0xb2dd34: ldur            x0, [fp, #-0x48]
    // 0xb2dd38: StoreField: r2->field_1f = r0
    //     0xb2dd38: stur            w0, [x2, #0x1f]
    // 0xb2dd3c: r1 = <Widget>
    //     0xb2dd3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2dd40: r0 = AllocateGrowableArray()
    //     0xb2dd40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2dd44: mov             x1, x0
    // 0xb2dd48: ldur            x0, [fp, #-8]
    // 0xb2dd4c: stur            x1, [fp, #-0x38]
    // 0xb2dd50: StoreField: r1->field_f = r0
    //     0xb2dd50: stur            w0, [x1, #0xf]
    // 0xb2dd54: r0 = 10
    //     0xb2dd54: movz            x0, #0xa
    // 0xb2dd58: StoreField: r1->field_b = r0
    //     0xb2dd58: stur            w0, [x1, #0xb]
    // 0xb2dd5c: r0 = Column()
    //     0xb2dd5c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2dd60: mov             x2, x0
    // 0xb2dd64: r0 = Instance_Axis
    //     0xb2dd64: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2dd68: stur            x2, [fp, #-8]
    // 0xb2dd6c: StoreField: r2->field_f = r0
    //     0xb2dd6c: stur            w0, [x2, #0xf]
    // 0xb2dd70: r3 = Instance_MainAxisAlignment
    //     0xb2dd70: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2dd74: ldr             x3, [x3, #0xa08]
    // 0xb2dd78: StoreField: r2->field_13 = r3
    //     0xb2dd78: stur            w3, [x2, #0x13]
    // 0xb2dd7c: r4 = Instance_MainAxisSize
    //     0xb2dd7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2dd80: ldr             x4, [x4, #0xa10]
    // 0xb2dd84: ArrayStore: r2[0] = r4  ; List_4
    //     0xb2dd84: stur            w4, [x2, #0x17]
    // 0xb2dd88: r5 = Instance_CrossAxisAlignment
    //     0xb2dd88: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2dd8c: ldr             x5, [x5, #0x890]
    // 0xb2dd90: StoreField: r2->field_1b = r5
    //     0xb2dd90: stur            w5, [x2, #0x1b]
    // 0xb2dd94: r6 = Instance_VerticalDirection
    //     0xb2dd94: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2dd98: ldr             x6, [x6, #0xa20]
    // 0xb2dd9c: StoreField: r2->field_23 = r6
    //     0xb2dd9c: stur            w6, [x2, #0x23]
    // 0xb2dda0: r7 = Instance_Clip
    //     0xb2dda0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2dda4: ldr             x7, [x7, #0x38]
    // 0xb2dda8: StoreField: r2->field_2b = r7
    //     0xb2dda8: stur            w7, [x2, #0x2b]
    // 0xb2ddac: StoreField: r2->field_2f = rZR
    //     0xb2ddac: stur            xzr, [x2, #0x2f]
    // 0xb2ddb0: ldur            x1, [fp, #-0x38]
    // 0xb2ddb4: StoreField: r2->field_b = r1
    //     0xb2ddb4: stur            w1, [x2, #0xb]
    // 0xb2ddb8: r1 = <FlexParentData>
    //     0xb2ddb8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2ddbc: ldr             x1, [x1, #0xe00]
    // 0xb2ddc0: r0 = Expanded()
    //     0xb2ddc0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2ddc4: mov             x3, x0
    // 0xb2ddc8: r0 = 1
    //     0xb2ddc8: movz            x0, #0x1
    // 0xb2ddcc: stur            x3, [fp, #-0x38]
    // 0xb2ddd0: StoreField: r3->field_13 = r0
    //     0xb2ddd0: stur            x0, [x3, #0x13]
    // 0xb2ddd4: r0 = Instance_FlexFit
    //     0xb2ddd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2ddd8: ldr             x0, [x0, #0xe08]
    // 0xb2dddc: StoreField: r3->field_1b = r0
    //     0xb2dddc: stur            w0, [x3, #0x1b]
    // 0xb2dde0: ldur            x0, [fp, #-8]
    // 0xb2dde4: StoreField: r3->field_b = r0
    //     0xb2dde4: stur            w0, [x3, #0xb]
    // 0xb2dde8: r1 = Null
    //     0xb2dde8: mov             x1, NULL
    // 0xb2ddec: r2 = 6
    //     0xb2ddec: movz            x2, #0x6
    // 0xb2ddf0: r0 = AllocateArray()
    //     0xb2ddf0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2ddf4: mov             x2, x0
    // 0xb2ddf8: ldur            x0, [fp, #-0x28]
    // 0xb2ddfc: stur            x2, [fp, #-8]
    // 0xb2de00: StoreField: r2->field_f = r0
    //     0xb2de00: stur            w0, [x2, #0xf]
    // 0xb2de04: r16 = Instance_SizedBox
    //     0xb2de04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb2de08: ldr             x16, [x16, #0xb20]
    // 0xb2de0c: StoreField: r2->field_13 = r16
    //     0xb2de0c: stur            w16, [x2, #0x13]
    // 0xb2de10: ldur            x0, [fp, #-0x38]
    // 0xb2de14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2de14: stur            w0, [x2, #0x17]
    // 0xb2de18: r1 = <Widget>
    //     0xb2de18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2de1c: r0 = AllocateGrowableArray()
    //     0xb2de1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2de20: mov             x1, x0
    // 0xb2de24: ldur            x0, [fp, #-8]
    // 0xb2de28: stur            x1, [fp, #-0x28]
    // 0xb2de2c: StoreField: r1->field_f = r0
    //     0xb2de2c: stur            w0, [x1, #0xf]
    // 0xb2de30: r0 = 6
    //     0xb2de30: movz            x0, #0x6
    // 0xb2de34: StoreField: r1->field_b = r0
    //     0xb2de34: stur            w0, [x1, #0xb]
    // 0xb2de38: r0 = Row()
    //     0xb2de38: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2de3c: mov             x2, x0
    // 0xb2de40: r0 = Instance_Axis
    //     0xb2de40: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2de44: stur            x2, [fp, #-0x38]
    // 0xb2de48: StoreField: r2->field_f = r0
    //     0xb2de48: stur            w0, [x2, #0xf]
    // 0xb2de4c: r0 = Instance_MainAxisAlignment
    //     0xb2de4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2de50: ldr             x0, [x0, #0xa08]
    // 0xb2de54: StoreField: r2->field_13 = r0
    //     0xb2de54: stur            w0, [x2, #0x13]
    // 0xb2de58: r3 = Instance_MainAxisSize
    //     0xb2de58: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2de5c: ldr             x3, [x3, #0xa10]
    // 0xb2de60: ArrayStore: r2[0] = r3  ; List_4
    //     0xb2de60: stur            w3, [x2, #0x17]
    // 0xb2de64: r4 = Instance_CrossAxisAlignment
    //     0xb2de64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2de68: ldr             x4, [x4, #0xa18]
    // 0xb2de6c: StoreField: r2->field_1b = r4
    //     0xb2de6c: stur            w4, [x2, #0x1b]
    // 0xb2de70: r5 = Instance_VerticalDirection
    //     0xb2de70: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2de74: ldr             x5, [x5, #0xa20]
    // 0xb2de78: StoreField: r2->field_23 = r5
    //     0xb2de78: stur            w5, [x2, #0x23]
    // 0xb2de7c: r6 = Instance_Clip
    //     0xb2de7c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2de80: ldr             x6, [x6, #0x38]
    // 0xb2de84: StoreField: r2->field_2b = r6
    //     0xb2de84: stur            w6, [x2, #0x2b]
    // 0xb2de88: StoreField: r2->field_2f = rZR
    //     0xb2de88: stur            xzr, [x2, #0x2f]
    // 0xb2de8c: ldur            x1, [fp, #-0x28]
    // 0xb2de90: StoreField: r2->field_b = r1
    //     0xb2de90: stur            w1, [x2, #0xb]
    // 0xb2de94: ldur            x1, [fp, #-0x18]
    // 0xb2de98: LoadField: r7 = r1->field_b
    //     0xb2de98: ldur            w7, [x1, #0xb]
    // 0xb2de9c: DecompressPointer r7
    //     0xb2de9c: add             x7, x7, HEAP, lsl #32
    // 0xb2dea0: cmp             w7, NULL
    // 0xb2dea4: b.eq            #0xb2e548
    // 0xb2dea8: LoadField: r8 = r7->field_1b
    //     0xb2dea8: ldur            w8, [x7, #0x1b]
    // 0xb2deac: DecompressPointer r8
    //     0xb2deac: add             x8, x8, HEAP, lsl #32
    // 0xb2deb0: ldur            x1, [fp, #-0x20]
    // 0xb2deb4: stur            x8, [fp, #-8]
    // 0xb2deb8: r0 = of()
    //     0xb2deb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2debc: LoadField: r1 = r0->field_87
    //     0xb2debc: ldur            w1, [x0, #0x87]
    // 0xb2dec0: DecompressPointer r1
    //     0xb2dec0: add             x1, x1, HEAP, lsl #32
    // 0xb2dec4: LoadField: r0 = r1->field_7
    //     0xb2dec4: ldur            w0, [x1, #7]
    // 0xb2dec8: DecompressPointer r0
    //     0xb2dec8: add             x0, x0, HEAP, lsl #32
    // 0xb2decc: r16 = Instance_TextDecoration
    //     0xb2decc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb2ded0: ldr             x16, [x16, #0x10]
    // 0xb2ded4: r30 = 12.000000
    //     0xb2ded4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2ded8: ldr             lr, [lr, #0x9e8]
    // 0xb2dedc: stp             lr, x16, [SP, #8]
    // 0xb2dee0: r16 = Instance_Color
    //     0xb2dee0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb2dee4: str             x16, [SP]
    // 0xb2dee8: mov             x1, x0
    // 0xb2deec: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb2deec: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb2def0: ldr             x4, [x4, #0xb60]
    // 0xb2def4: r0 = copyWith()
    //     0xb2def4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2def8: stur            x0, [fp, #-0x18]
    // 0xb2defc: r0 = Text()
    //     0xb2defc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2df00: mov             x1, x0
    // 0xb2df04: r0 = "Remove Exchange"
    //     0xb2df04: add             x0, PP, #0x57, lsl #12  ; [pp+0x57308] "Remove Exchange"
    //     0xb2df08: ldr             x0, [x0, #0x308]
    // 0xb2df0c: stur            x1, [fp, #-0x28]
    // 0xb2df10: StoreField: r1->field_b = r0
    //     0xb2df10: stur            w0, [x1, #0xb]
    // 0xb2df14: ldur            x0, [fp, #-0x18]
    // 0xb2df18: StoreField: r1->field_13 = r0
    //     0xb2df18: stur            w0, [x1, #0x13]
    // 0xb2df1c: r0 = Padding()
    //     0xb2df1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2df20: mov             x1, x0
    // 0xb2df24: r0 = Instance_EdgeInsets
    //     0xb2df24: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xb2df28: ldr             x0, [x0, #0x100]
    // 0xb2df2c: stur            x1, [fp, #-0x18]
    // 0xb2df30: StoreField: r1->field_f = r0
    //     0xb2df30: stur            w0, [x1, #0xf]
    // 0xb2df34: ldur            x0, [fp, #-0x28]
    // 0xb2df38: StoreField: r1->field_b = r0
    //     0xb2df38: stur            w0, [x1, #0xb]
    // 0xb2df3c: r0 = InkWell()
    //     0xb2df3c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2df40: mov             x3, x0
    // 0xb2df44: ldur            x0, [fp, #-0x18]
    // 0xb2df48: stur            x3, [fp, #-0x28]
    // 0xb2df4c: StoreField: r3->field_b = r0
    //     0xb2df4c: stur            w0, [x3, #0xb]
    // 0xb2df50: ldur            x0, [fp, #-8]
    // 0xb2df54: StoreField: r3->field_f = r0
    //     0xb2df54: stur            w0, [x3, #0xf]
    // 0xb2df58: r0 = true
    //     0xb2df58: add             x0, NULL, #0x20  ; true
    // 0xb2df5c: StoreField: r3->field_43 = r0
    //     0xb2df5c: stur            w0, [x3, #0x43]
    // 0xb2df60: r1 = Instance_BoxShape
    //     0xb2df60: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2df64: ldr             x1, [x1, #0x80]
    // 0xb2df68: StoreField: r3->field_47 = r1
    //     0xb2df68: stur            w1, [x3, #0x47]
    // 0xb2df6c: StoreField: r3->field_6f = r0
    //     0xb2df6c: stur            w0, [x3, #0x6f]
    // 0xb2df70: r1 = false
    //     0xb2df70: add             x1, NULL, #0x30  ; false
    // 0xb2df74: StoreField: r3->field_73 = r1
    //     0xb2df74: stur            w1, [x3, #0x73]
    // 0xb2df78: StoreField: r3->field_83 = r0
    //     0xb2df78: stur            w0, [x3, #0x83]
    // 0xb2df7c: StoreField: r3->field_7b = r1
    //     0xb2df7c: stur            w1, [x3, #0x7b]
    // 0xb2df80: r1 = Null
    //     0xb2df80: mov             x1, NULL
    // 0xb2df84: r2 = 4
    //     0xb2df84: movz            x2, #0x4
    // 0xb2df88: r0 = AllocateArray()
    //     0xb2df88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2df8c: mov             x2, x0
    // 0xb2df90: ldur            x0, [fp, #-0x38]
    // 0xb2df94: stur            x2, [fp, #-8]
    // 0xb2df98: StoreField: r2->field_f = r0
    //     0xb2df98: stur            w0, [x2, #0xf]
    // 0xb2df9c: ldur            x0, [fp, #-0x28]
    // 0xb2dfa0: StoreField: r2->field_13 = r0
    //     0xb2dfa0: stur            w0, [x2, #0x13]
    // 0xb2dfa4: r1 = <Widget>
    //     0xb2dfa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2dfa8: r0 = AllocateGrowableArray()
    //     0xb2dfa8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2dfac: mov             x1, x0
    // 0xb2dfb0: ldur            x0, [fp, #-8]
    // 0xb2dfb4: stur            x1, [fp, #-0x18]
    // 0xb2dfb8: StoreField: r1->field_f = r0
    //     0xb2dfb8: stur            w0, [x1, #0xf]
    // 0xb2dfbc: r2 = 4
    //     0xb2dfbc: movz            x2, #0x4
    // 0xb2dfc0: StoreField: r1->field_b = r2
    //     0xb2dfc0: stur            w2, [x1, #0xb]
    // 0xb2dfc4: r0 = Column()
    //     0xb2dfc4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2dfc8: mov             x1, x0
    // 0xb2dfcc: r0 = Instance_Axis
    //     0xb2dfcc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2dfd0: stur            x1, [fp, #-8]
    // 0xb2dfd4: StoreField: r1->field_f = r0
    //     0xb2dfd4: stur            w0, [x1, #0xf]
    // 0xb2dfd8: r2 = Instance_MainAxisAlignment
    //     0xb2dfd8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2dfdc: ldr             x2, [x2, #0xa08]
    // 0xb2dfe0: StoreField: r1->field_13 = r2
    //     0xb2dfe0: stur            w2, [x1, #0x13]
    // 0xb2dfe4: r3 = Instance_MainAxisSize
    //     0xb2dfe4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2dfe8: ldr             x3, [x3, #0xa10]
    // 0xb2dfec: ArrayStore: r1[0] = r3  ; List_4
    //     0xb2dfec: stur            w3, [x1, #0x17]
    // 0xb2dff0: r4 = Instance_CrossAxisAlignment
    //     0xb2dff0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2dff4: ldr             x4, [x4, #0xa18]
    // 0xb2dff8: StoreField: r1->field_1b = r4
    //     0xb2dff8: stur            w4, [x1, #0x1b]
    // 0xb2dffc: r4 = Instance_VerticalDirection
    //     0xb2dffc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2e000: ldr             x4, [x4, #0xa20]
    // 0xb2e004: StoreField: r1->field_23 = r4
    //     0xb2e004: stur            w4, [x1, #0x23]
    // 0xb2e008: r5 = Instance_Clip
    //     0xb2e008: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2e00c: ldr             x5, [x5, #0x38]
    // 0xb2e010: StoreField: r1->field_2b = r5
    //     0xb2e010: stur            w5, [x1, #0x2b]
    // 0xb2e014: StoreField: r1->field_2f = rZR
    //     0xb2e014: stur            xzr, [x1, #0x2f]
    // 0xb2e018: ldur            x6, [fp, #-0x18]
    // 0xb2e01c: StoreField: r1->field_b = r6
    //     0xb2e01c: stur            w6, [x1, #0xb]
    // 0xb2e020: r0 = Padding()
    //     0xb2e020: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2e024: mov             x1, x0
    // 0xb2e028: r0 = Instance_EdgeInsets
    //     0xb2e028: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xb2e02c: ldr             x0, [x0, #0xf98]
    // 0xb2e030: stur            x1, [fp, #-0x18]
    // 0xb2e034: StoreField: r1->field_f = r0
    //     0xb2e034: stur            w0, [x1, #0xf]
    // 0xb2e038: ldur            x0, [fp, #-8]
    // 0xb2e03c: StoreField: r1->field_b = r0
    //     0xb2e03c: stur            w0, [x1, #0xb]
    // 0xb2e040: r0 = Container()
    //     0xb2e040: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2e044: stur            x0, [fp, #-8]
    // 0xb2e048: ldur            x16, [fp, #-0x30]
    // 0xb2e04c: ldur            lr, [fp, #-0x18]
    // 0xb2e050: stp             lr, x16, [SP]
    // 0xb2e054: mov             x1, x0
    // 0xb2e058: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb2e058: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb2e05c: ldr             x4, [x4, #0x88]
    // 0xb2e060: r0 = Container()
    //     0xb2e060: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2e064: ldur            x1, [fp, #-0x20]
    // 0xb2e068: r0 = of()
    //     0xb2e068: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e06c: LoadField: r1 = r0->field_87
    //     0xb2e06c: ldur            w1, [x0, #0x87]
    // 0xb2e070: DecompressPointer r1
    //     0xb2e070: add             x1, x1, HEAP, lsl #32
    // 0xb2e074: LoadField: r0 = r1->field_27
    //     0xb2e074: ldur            w0, [x1, #0x27]
    // 0xb2e078: DecompressPointer r0
    //     0xb2e078: add             x0, x0, HEAP, lsl #32
    // 0xb2e07c: stur            x0, [fp, #-0x18]
    // 0xb2e080: r1 = Instance_Color
    //     0xb2e080: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2e084: d0 = 0.700000
    //     0xb2e084: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2e088: ldr             d0, [x17, #0xf48]
    // 0xb2e08c: r0 = withOpacity()
    //     0xb2e08c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2e090: str             x0, [SP]
    // 0xb2e094: ldur            x1, [fp, #-0x18]
    // 0xb2e098: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2e098: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2e09c: ldr             x4, [x4, #0xf40]
    // 0xb2e0a0: r0 = copyWith()
    //     0xb2e0a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2e0a4: stur            x0, [fp, #-0x18]
    // 0xb2e0a8: r0 = TextSpan()
    //     0xb2e0a8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e0ac: mov             x3, x0
    // 0xb2e0b0: r0 = "• Exchange credit will be added "
    //     0xb2e0b0: add             x0, PP, #0x53, lsl #12  ; [pp+0x539c8] "• Exchange credit will be added "
    //     0xb2e0b4: ldr             x0, [x0, #0x9c8]
    // 0xb2e0b8: stur            x3, [fp, #-0x28]
    // 0xb2e0bc: StoreField: r3->field_b = r0
    //     0xb2e0bc: stur            w0, [x3, #0xb]
    // 0xb2e0c0: r0 = Instance__DeferringMouseCursor
    //     0xb2e0c0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e0c4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2e0c4: stur            w0, [x3, #0x17]
    // 0xb2e0c8: ldur            x1, [fp, #-0x18]
    // 0xb2e0cc: StoreField: r3->field_7 = r1
    //     0xb2e0cc: stur            w1, [x3, #7]
    // 0xb2e0d0: r1 = Null
    //     0xb2e0d0: mov             x1, NULL
    // 0xb2e0d4: r2 = 4
    //     0xb2e0d4: movz            x2, #0x4
    // 0xb2e0d8: r0 = AllocateArray()
    //     0xb2e0d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e0dc: r16 = "worth "
    //     0xb2e0dc: add             x16, PP, #0x53, lsl #12  ; [pp+0x539d0] "worth "
    //     0xb2e0e0: ldr             x16, [x16, #0x9d0]
    // 0xb2e0e4: StoreField: r0->field_f = r16
    //     0xb2e0e4: stur            w16, [x0, #0xf]
    // 0xb2e0e8: ldur            x1, [fp, #-0x10]
    // 0xb2e0ec: cmp             w1, NULL
    // 0xb2e0f0: b.ne            #0xb2e0fc
    // 0xb2e0f4: r2 = Null
    //     0xb2e0f4: mov             x2, NULL
    // 0xb2e0f8: b               #0xb2e120
    // 0xb2e0fc: LoadField: r2 = r1->field_b
    //     0xb2e0fc: ldur            w2, [x1, #0xb]
    // 0xb2e100: DecompressPointer r2
    //     0xb2e100: add             x2, x2, HEAP, lsl #32
    // 0xb2e104: cmp             w2, NULL
    // 0xb2e108: b.ne            #0xb2e114
    // 0xb2e10c: r2 = Null
    //     0xb2e10c: mov             x2, NULL
    // 0xb2e110: b               #0xb2e120
    // 0xb2e114: LoadField: r3 = r2->field_7
    //     0xb2e114: ldur            w3, [x2, #7]
    // 0xb2e118: DecompressPointer r3
    //     0xb2e118: add             x3, x3, HEAP, lsl #32
    // 0xb2e11c: mov             x2, x3
    // 0xb2e120: cmp             w2, NULL
    // 0xb2e124: b.ne            #0xb2e130
    // 0xb2e128: r3 = ""
    //     0xb2e128: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2e12c: b               #0xb2e134
    // 0xb2e130: mov             x3, x2
    // 0xb2e134: ldur            x2, [fp, #-0x28]
    // 0xb2e138: StoreField: r0->field_13 = r3
    //     0xb2e138: stur            w3, [x0, #0x13]
    // 0xb2e13c: str             x0, [SP]
    // 0xb2e140: r0 = _interpolate()
    //     0xb2e140: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2e144: ldur            x1, [fp, #-0x20]
    // 0xb2e148: stur            x0, [fp, #-0x18]
    // 0xb2e14c: r0 = of()
    //     0xb2e14c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e150: LoadField: r1 = r0->field_87
    //     0xb2e150: ldur            w1, [x0, #0x87]
    // 0xb2e154: DecompressPointer r1
    //     0xb2e154: add             x1, x1, HEAP, lsl #32
    // 0xb2e158: LoadField: r0 = r1->field_27
    //     0xb2e158: ldur            w0, [x1, #0x27]
    // 0xb2e15c: DecompressPointer r0
    //     0xb2e15c: add             x0, x0, HEAP, lsl #32
    // 0xb2e160: r16 = Instance_Color
    //     0xb2e160: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2e164: str             x16, [SP]
    // 0xb2e168: mov             x1, x0
    // 0xb2e16c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2e16c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2e170: ldr             x4, [x4, #0xf40]
    // 0xb2e174: r0 = copyWith()
    //     0xb2e174: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2e178: stur            x0, [fp, #-0x30]
    // 0xb2e17c: r0 = TextSpan()
    //     0xb2e17c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e180: mov             x3, x0
    // 0xb2e184: ldur            x0, [fp, #-0x18]
    // 0xb2e188: stur            x3, [fp, #-0x38]
    // 0xb2e18c: StoreField: r3->field_b = r0
    //     0xb2e18c: stur            w0, [x3, #0xb]
    // 0xb2e190: r0 = Instance__DeferringMouseCursor
    //     0xb2e190: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e194: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2e194: stur            w0, [x3, #0x17]
    // 0xb2e198: ldur            x1, [fp, #-0x30]
    // 0xb2e19c: StoreField: r3->field_7 = r1
    //     0xb2e19c: stur            w1, [x3, #7]
    // 0xb2e1a0: r1 = Null
    //     0xb2e1a0: mov             x1, NULL
    // 0xb2e1a4: r2 = 4
    //     0xb2e1a4: movz            x2, #0x4
    // 0xb2e1a8: r0 = AllocateArray()
    //     0xb2e1a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e1ac: mov             x2, x0
    // 0xb2e1b0: ldur            x0, [fp, #-0x28]
    // 0xb2e1b4: stur            x2, [fp, #-0x18]
    // 0xb2e1b8: StoreField: r2->field_f = r0
    //     0xb2e1b8: stur            w0, [x2, #0xf]
    // 0xb2e1bc: ldur            x0, [fp, #-0x38]
    // 0xb2e1c0: StoreField: r2->field_13 = r0
    //     0xb2e1c0: stur            w0, [x2, #0x13]
    // 0xb2e1c4: r1 = <InlineSpan>
    //     0xb2e1c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2e1c8: ldr             x1, [x1, #0xe40]
    // 0xb2e1cc: r0 = AllocateGrowableArray()
    //     0xb2e1cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e1d0: mov             x1, x0
    // 0xb2e1d4: ldur            x0, [fp, #-0x18]
    // 0xb2e1d8: stur            x1, [fp, #-0x28]
    // 0xb2e1dc: StoreField: r1->field_f = r0
    //     0xb2e1dc: stur            w0, [x1, #0xf]
    // 0xb2e1e0: r2 = 4
    //     0xb2e1e0: movz            x2, #0x4
    // 0xb2e1e4: StoreField: r1->field_b = r2
    //     0xb2e1e4: stur            w2, [x1, #0xb]
    // 0xb2e1e8: r0 = TextSpan()
    //     0xb2e1e8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e1ec: mov             x1, x0
    // 0xb2e1f0: ldur            x0, [fp, #-0x28]
    // 0xb2e1f4: stur            x1, [fp, #-0x18]
    // 0xb2e1f8: StoreField: r1->field_f = r0
    //     0xb2e1f8: stur            w0, [x1, #0xf]
    // 0xb2e1fc: r0 = Instance__DeferringMouseCursor
    //     0xb2e1fc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e200: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2e200: stur            w0, [x1, #0x17]
    // 0xb2e204: r0 = RichText()
    //     0xb2e204: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2e208: mov             x1, x0
    // 0xb2e20c: ldur            x2, [fp, #-0x18]
    // 0xb2e210: stur            x0, [fp, #-0x18]
    // 0xb2e214: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2e214: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2e218: r0 = RichText()
    //     0xb2e218: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2e21c: ldur            x1, [fp, #-0x20]
    // 0xb2e220: r0 = of()
    //     0xb2e220: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e224: LoadField: r1 = r0->field_87
    //     0xb2e224: ldur            w1, [x0, #0x87]
    // 0xb2e228: DecompressPointer r1
    //     0xb2e228: add             x1, x1, HEAP, lsl #32
    // 0xb2e22c: LoadField: r0 = r1->field_27
    //     0xb2e22c: ldur            w0, [x1, #0x27]
    // 0xb2e230: DecompressPointer r0
    //     0xb2e230: add             x0, x0, HEAP, lsl #32
    // 0xb2e234: stur            x0, [fp, #-0x28]
    // 0xb2e238: r1 = Instance_Color
    //     0xb2e238: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2e23c: d0 = 0.700000
    //     0xb2e23c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2e240: ldr             d0, [x17, #0xf48]
    // 0xb2e244: r0 = withOpacity()
    //     0xb2e244: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2e248: str             x0, [SP]
    // 0xb2e24c: ldur            x1, [fp, #-0x28]
    // 0xb2e250: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2e250: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2e254: ldr             x4, [x4, #0xf40]
    // 0xb2e258: r0 = copyWith()
    //     0xb2e258: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2e25c: stur            x0, [fp, #-0x28]
    // 0xb2e260: r0 = TextSpan()
    //     0xb2e260: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e264: mov             x2, x0
    // 0xb2e268: r0 = "• Exchange any new item by "
    //     0xb2e268: add             x0, PP, #0x53, lsl #12  ; [pp+0x539e0] "• Exchange any new item by "
    //     0xb2e26c: ldr             x0, [x0, #0x9e0]
    // 0xb2e270: stur            x2, [fp, #-0x30]
    // 0xb2e274: StoreField: r2->field_b = r0
    //     0xb2e274: stur            w0, [x2, #0xb]
    // 0xb2e278: r0 = Instance__DeferringMouseCursor
    //     0xb2e278: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e27c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2e27c: stur            w0, [x2, #0x17]
    // 0xb2e280: ldur            x1, [fp, #-0x28]
    // 0xb2e284: StoreField: r2->field_7 = r1
    //     0xb2e284: stur            w1, [x2, #7]
    // 0xb2e288: ldur            x1, [fp, #-0x10]
    // 0xb2e28c: cmp             w1, NULL
    // 0xb2e290: b.ne            #0xb2e29c
    // 0xb2e294: r1 = Null
    //     0xb2e294: mov             x1, NULL
    // 0xb2e298: b               #0xb2e2a8
    // 0xb2e29c: LoadField: r3 = r1->field_13
    //     0xb2e29c: ldur            w3, [x1, #0x13]
    // 0xb2e2a0: DecompressPointer r3
    //     0xb2e2a0: add             x3, x3, HEAP, lsl #32
    // 0xb2e2a4: mov             x1, x3
    // 0xb2e2a8: cmp             w1, NULL
    // 0xb2e2ac: b.ne            #0xb2e2b8
    // 0xb2e2b0: r5 = ""
    //     0xb2e2b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2e2b4: b               #0xb2e2bc
    // 0xb2e2b8: mov             x5, x1
    // 0xb2e2bc: ldur            x4, [fp, #-8]
    // 0xb2e2c0: ldur            x3, [fp, #-0x18]
    // 0xb2e2c4: ldur            x1, [fp, #-0x20]
    // 0xb2e2c8: stur            x5, [fp, #-0x10]
    // 0xb2e2cc: r0 = of()
    //     0xb2e2cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e2d0: LoadField: r1 = r0->field_87
    //     0xb2e2d0: ldur            w1, [x0, #0x87]
    // 0xb2e2d4: DecompressPointer r1
    //     0xb2e2d4: add             x1, x1, HEAP, lsl #32
    // 0xb2e2d8: LoadField: r0 = r1->field_27
    //     0xb2e2d8: ldur            w0, [x1, #0x27]
    // 0xb2e2dc: DecompressPointer r0
    //     0xb2e2dc: add             x0, x0, HEAP, lsl #32
    // 0xb2e2e0: r16 = Instance_Color
    //     0xb2e2e0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2e2e4: str             x16, [SP]
    // 0xb2e2e8: mov             x1, x0
    // 0xb2e2ec: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb2e2ec: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2e2f0: ldr             x4, [x4, #0xf40]
    // 0xb2e2f4: r0 = copyWith()
    //     0xb2e2f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2e2f8: stur            x0, [fp, #-0x20]
    // 0xb2e2fc: r0 = TextSpan()
    //     0xb2e2fc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e300: mov             x3, x0
    // 0xb2e304: ldur            x0, [fp, #-0x10]
    // 0xb2e308: stur            x3, [fp, #-0x28]
    // 0xb2e30c: StoreField: r3->field_b = r0
    //     0xb2e30c: stur            w0, [x3, #0xb]
    // 0xb2e310: r0 = Instance__DeferringMouseCursor
    //     0xb2e310: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e314: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2e314: stur            w0, [x3, #0x17]
    // 0xb2e318: ldur            x1, [fp, #-0x20]
    // 0xb2e31c: StoreField: r3->field_7 = r1
    //     0xb2e31c: stur            w1, [x3, #7]
    // 0xb2e320: r1 = Null
    //     0xb2e320: mov             x1, NULL
    // 0xb2e324: r2 = 4
    //     0xb2e324: movz            x2, #0x4
    // 0xb2e328: r0 = AllocateArray()
    //     0xb2e328: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e32c: mov             x2, x0
    // 0xb2e330: ldur            x0, [fp, #-0x30]
    // 0xb2e334: stur            x2, [fp, #-0x10]
    // 0xb2e338: StoreField: r2->field_f = r0
    //     0xb2e338: stur            w0, [x2, #0xf]
    // 0xb2e33c: ldur            x0, [fp, #-0x28]
    // 0xb2e340: StoreField: r2->field_13 = r0
    //     0xb2e340: stur            w0, [x2, #0x13]
    // 0xb2e344: r1 = <InlineSpan>
    //     0xb2e344: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2e348: ldr             x1, [x1, #0xe40]
    // 0xb2e34c: r0 = AllocateGrowableArray()
    //     0xb2e34c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e350: mov             x1, x0
    // 0xb2e354: ldur            x0, [fp, #-0x10]
    // 0xb2e358: stur            x1, [fp, #-0x20]
    // 0xb2e35c: StoreField: r1->field_f = r0
    //     0xb2e35c: stur            w0, [x1, #0xf]
    // 0xb2e360: r2 = 4
    //     0xb2e360: movz            x2, #0x4
    // 0xb2e364: StoreField: r1->field_b = r2
    //     0xb2e364: stur            w2, [x1, #0xb]
    // 0xb2e368: r0 = TextSpan()
    //     0xb2e368: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2e36c: mov             x1, x0
    // 0xb2e370: ldur            x0, [fp, #-0x20]
    // 0xb2e374: stur            x1, [fp, #-0x10]
    // 0xb2e378: StoreField: r1->field_f = r0
    //     0xb2e378: stur            w0, [x1, #0xf]
    // 0xb2e37c: r0 = Instance__DeferringMouseCursor
    //     0xb2e37c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2e380: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2e380: stur            w0, [x1, #0x17]
    // 0xb2e384: r0 = RichText()
    //     0xb2e384: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2e388: mov             x1, x0
    // 0xb2e38c: ldur            x2, [fp, #-0x10]
    // 0xb2e390: stur            x0, [fp, #-0x10]
    // 0xb2e394: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2e394: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2e398: r0 = RichText()
    //     0xb2e398: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2e39c: r1 = Null
    //     0xb2e39c: mov             x1, NULL
    // 0xb2e3a0: r2 = 4
    //     0xb2e3a0: movz            x2, #0x4
    // 0xb2e3a4: r0 = AllocateArray()
    //     0xb2e3a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e3a8: mov             x2, x0
    // 0xb2e3ac: ldur            x0, [fp, #-0x18]
    // 0xb2e3b0: stur            x2, [fp, #-0x20]
    // 0xb2e3b4: StoreField: r2->field_f = r0
    //     0xb2e3b4: stur            w0, [x2, #0xf]
    // 0xb2e3b8: ldur            x0, [fp, #-0x10]
    // 0xb2e3bc: StoreField: r2->field_13 = r0
    //     0xb2e3bc: stur            w0, [x2, #0x13]
    // 0xb2e3c0: r1 = <Widget>
    //     0xb2e3c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2e3c4: r0 = AllocateGrowableArray()
    //     0xb2e3c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e3c8: mov             x1, x0
    // 0xb2e3cc: ldur            x0, [fp, #-0x20]
    // 0xb2e3d0: stur            x1, [fp, #-0x10]
    // 0xb2e3d4: StoreField: r1->field_f = r0
    //     0xb2e3d4: stur            w0, [x1, #0xf]
    // 0xb2e3d8: r2 = 4
    //     0xb2e3d8: movz            x2, #0x4
    // 0xb2e3dc: StoreField: r1->field_b = r2
    //     0xb2e3dc: stur            w2, [x1, #0xb]
    // 0xb2e3e0: r0 = Column()
    //     0xb2e3e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2e3e4: mov             x1, x0
    // 0xb2e3e8: r0 = Instance_Axis
    //     0xb2e3e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2e3ec: stur            x1, [fp, #-0x18]
    // 0xb2e3f0: StoreField: r1->field_f = r0
    //     0xb2e3f0: stur            w0, [x1, #0xf]
    // 0xb2e3f4: r2 = Instance_MainAxisAlignment
    //     0xb2e3f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2e3f8: ldr             x2, [x2, #0xa08]
    // 0xb2e3fc: StoreField: r1->field_13 = r2
    //     0xb2e3fc: stur            w2, [x1, #0x13]
    // 0xb2e400: r3 = Instance_MainAxisSize
    //     0xb2e400: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2e404: ldr             x3, [x3, #0xa10]
    // 0xb2e408: ArrayStore: r1[0] = r3  ; List_4
    //     0xb2e408: stur            w3, [x1, #0x17]
    // 0xb2e40c: r4 = Instance_CrossAxisAlignment
    //     0xb2e40c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2e410: ldr             x4, [x4, #0x890]
    // 0xb2e414: StoreField: r1->field_1b = r4
    //     0xb2e414: stur            w4, [x1, #0x1b]
    // 0xb2e418: r5 = Instance_VerticalDirection
    //     0xb2e418: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2e41c: ldr             x5, [x5, #0xa20]
    // 0xb2e420: StoreField: r1->field_23 = r5
    //     0xb2e420: stur            w5, [x1, #0x23]
    // 0xb2e424: r6 = Instance_Clip
    //     0xb2e424: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2e428: ldr             x6, [x6, #0x38]
    // 0xb2e42c: StoreField: r1->field_2b = r6
    //     0xb2e42c: stur            w6, [x1, #0x2b]
    // 0xb2e430: StoreField: r1->field_2f = rZR
    //     0xb2e430: stur            xzr, [x1, #0x2f]
    // 0xb2e434: ldur            x7, [fp, #-0x10]
    // 0xb2e438: StoreField: r1->field_b = r7
    //     0xb2e438: stur            w7, [x1, #0xb]
    // 0xb2e43c: r0 = Container()
    //     0xb2e43c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2e440: stur            x0, [fp, #-0x10]
    // 0xb2e444: r16 = inf
    //     0xb2e444: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb2e448: ldr             x16, [x16, #0x9f8]
    // 0xb2e44c: r30 = Instance_EdgeInsets
    //     0xb2e44c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb2e450: ldr             lr, [lr, #0x1f0]
    // 0xb2e454: stp             lr, x16, [SP, #0x10]
    // 0xb2e458: r16 = Instance_BoxDecoration
    //     0xb2e458: add             x16, PP, #0x42, lsl #12  ; [pp+0x42e60] Obj!BoxDecoration@d64891
    //     0xb2e45c: ldr             x16, [x16, #0xe60]
    // 0xb2e460: ldur            lr, [fp, #-0x18]
    // 0xb2e464: stp             lr, x16, [SP]
    // 0xb2e468: mov             x1, x0
    // 0xb2e46c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb2e46c: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f68] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb2e470: ldr             x4, [x4, #0xf68]
    // 0xb2e474: r0 = Container()
    //     0xb2e474: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2e478: r1 = Null
    //     0xb2e478: mov             x1, NULL
    // 0xb2e47c: r2 = 4
    //     0xb2e47c: movz            x2, #0x4
    // 0xb2e480: r0 = AllocateArray()
    //     0xb2e480: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e484: mov             x2, x0
    // 0xb2e488: ldur            x0, [fp, #-8]
    // 0xb2e48c: stur            x2, [fp, #-0x18]
    // 0xb2e490: StoreField: r2->field_f = r0
    //     0xb2e490: stur            w0, [x2, #0xf]
    // 0xb2e494: ldur            x0, [fp, #-0x10]
    // 0xb2e498: StoreField: r2->field_13 = r0
    //     0xb2e498: stur            w0, [x2, #0x13]
    // 0xb2e49c: r1 = <Widget>
    //     0xb2e49c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2e4a0: r0 = AllocateGrowableArray()
    //     0xb2e4a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e4a4: mov             x1, x0
    // 0xb2e4a8: ldur            x0, [fp, #-0x18]
    // 0xb2e4ac: stur            x1, [fp, #-8]
    // 0xb2e4b0: StoreField: r1->field_f = r0
    //     0xb2e4b0: stur            w0, [x1, #0xf]
    // 0xb2e4b4: r0 = 4
    //     0xb2e4b4: movz            x0, #0x4
    // 0xb2e4b8: StoreField: r1->field_b = r0
    //     0xb2e4b8: stur            w0, [x1, #0xb]
    // 0xb2e4bc: r0 = Column()
    //     0xb2e4bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2e4c0: mov             x1, x0
    // 0xb2e4c4: r0 = Instance_Axis
    //     0xb2e4c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2e4c8: stur            x1, [fp, #-0x10]
    // 0xb2e4cc: StoreField: r1->field_f = r0
    //     0xb2e4cc: stur            w0, [x1, #0xf]
    // 0xb2e4d0: r0 = Instance_MainAxisAlignment
    //     0xb2e4d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2e4d4: ldr             x0, [x0, #0xa08]
    // 0xb2e4d8: StoreField: r1->field_13 = r0
    //     0xb2e4d8: stur            w0, [x1, #0x13]
    // 0xb2e4dc: r0 = Instance_MainAxisSize
    //     0xb2e4dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2e4e0: ldr             x0, [x0, #0xa10]
    // 0xb2e4e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2e4e4: stur            w0, [x1, #0x17]
    // 0xb2e4e8: r0 = Instance_CrossAxisAlignment
    //     0xb2e4e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb2e4ec: ldr             x0, [x0, #0x890]
    // 0xb2e4f0: StoreField: r1->field_1b = r0
    //     0xb2e4f0: stur            w0, [x1, #0x1b]
    // 0xb2e4f4: r0 = Instance_VerticalDirection
    //     0xb2e4f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2e4f8: ldr             x0, [x0, #0xa20]
    // 0xb2e4fc: StoreField: r1->field_23 = r0
    //     0xb2e4fc: stur            w0, [x1, #0x23]
    // 0xb2e500: r0 = Instance_Clip
    //     0xb2e500: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2e504: ldr             x0, [x0, #0x38]
    // 0xb2e508: StoreField: r1->field_2b = r0
    //     0xb2e508: stur            w0, [x1, #0x2b]
    // 0xb2e50c: StoreField: r1->field_2f = rZR
    //     0xb2e50c: stur            xzr, [x1, #0x2f]
    // 0xb2e510: ldur            x0, [fp, #-8]
    // 0xb2e514: StoreField: r1->field_b = r0
    //     0xb2e514: stur            w0, [x1, #0xb]
    // 0xb2e518: r0 = Padding()
    //     0xb2e518: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2e51c: r1 = Instance_EdgeInsets
    //     0xb2e51c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb2e520: ldr             x1, [x1, #0x668]
    // 0xb2e524: StoreField: r0->field_f = r1
    //     0xb2e524: stur            w1, [x0, #0xf]
    // 0xb2e528: ldur            x1, [fp, #-0x10]
    // 0xb2e52c: StoreField: r0->field_b = r1
    //     0xb2e52c: stur            w1, [x0, #0xb]
    // 0xb2e530: LeaveFrame
    //     0xb2e530: mov             SP, fp
    //     0xb2e534: ldp             fp, lr, [SP], #0x10
    // 0xb2e538: ret
    //     0xb2e538: ret             
    // 0xb2e53c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2e53c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2e540: b               #0xb2d914
    // 0xb2e544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2e544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2e548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2e548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildWarningMessages(/* No info */) {
    // ** addr: 0xb2e54c, size: 0xf4
    // 0xb2e54c: EnterFrame
    //     0xb2e54c: stp             fp, lr, [SP, #-0x10]!
    //     0xb2e550: mov             fp, SP
    // 0xb2e554: AllocStack(0x18)
    //     0xb2e554: sub             SP, SP, #0x18
    // 0xb2e558: SetupParameters(_BagItemViewWidgetState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb2e558: mov             x3, x1
    //     0xb2e55c: mov             x0, x2
    //     0xb2e560: stur            x1, [fp, #-8]
    //     0xb2e564: stur            x2, [fp, #-0x10]
    // 0xb2e568: CheckStackOverflow
    //     0xb2e568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2e56c: cmp             SP, x16
    //     0xb2e570: b.ls            #0xb2e638
    // 0xb2e574: mov             x1, x3
    // 0xb2e578: mov             x2, x0
    // 0xb2e57c: r0 = _buildSoldOutWarning()
    //     0xb2e57c: bl              #0xb2e924  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildSoldOutWarning
    // 0xb2e580: ldur            x1, [fp, #-8]
    // 0xb2e584: ldur            x2, [fp, #-0x10]
    // 0xb2e588: stur            x0, [fp, #-8]
    // 0xb2e58c: r0 = _buildQuantityWarning()
    //     0xb2e58c: bl              #0xb2e640  ; [package:customer_app/app/presentation/views/glass/bag/bag_item_view_widget.dart] _BagItemViewWidgetState::_buildQuantityWarning
    // 0xb2e590: r1 = Null
    //     0xb2e590: mov             x1, NULL
    // 0xb2e594: r2 = 4
    //     0xb2e594: movz            x2, #0x4
    // 0xb2e598: stur            x0, [fp, #-0x10]
    // 0xb2e59c: r0 = AllocateArray()
    //     0xb2e59c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e5a0: mov             x2, x0
    // 0xb2e5a4: ldur            x0, [fp, #-8]
    // 0xb2e5a8: stur            x2, [fp, #-0x18]
    // 0xb2e5ac: StoreField: r2->field_f = r0
    //     0xb2e5ac: stur            w0, [x2, #0xf]
    // 0xb2e5b0: ldur            x0, [fp, #-0x10]
    // 0xb2e5b4: StoreField: r2->field_13 = r0
    //     0xb2e5b4: stur            w0, [x2, #0x13]
    // 0xb2e5b8: r1 = <Widget>
    //     0xb2e5b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2e5bc: r0 = AllocateGrowableArray()
    //     0xb2e5bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e5c0: mov             x1, x0
    // 0xb2e5c4: ldur            x0, [fp, #-0x18]
    // 0xb2e5c8: stur            x1, [fp, #-8]
    // 0xb2e5cc: StoreField: r1->field_f = r0
    //     0xb2e5cc: stur            w0, [x1, #0xf]
    // 0xb2e5d0: r0 = 4
    //     0xb2e5d0: movz            x0, #0x4
    // 0xb2e5d4: StoreField: r1->field_b = r0
    //     0xb2e5d4: stur            w0, [x1, #0xb]
    // 0xb2e5d8: r0 = Column()
    //     0xb2e5d8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb2e5dc: r1 = Instance_Axis
    //     0xb2e5dc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2e5e0: StoreField: r0->field_f = r1
    //     0xb2e5e0: stur            w1, [x0, #0xf]
    // 0xb2e5e4: r1 = Instance_MainAxisAlignment
    //     0xb2e5e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2e5e8: ldr             x1, [x1, #0xa08]
    // 0xb2e5ec: StoreField: r0->field_13 = r1
    //     0xb2e5ec: stur            w1, [x0, #0x13]
    // 0xb2e5f0: r1 = Instance_MainAxisSize
    //     0xb2e5f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2e5f4: ldr             x1, [x1, #0xa10]
    // 0xb2e5f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2e5f8: stur            w1, [x0, #0x17]
    // 0xb2e5fc: r1 = Instance_CrossAxisAlignment
    //     0xb2e5fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2e600: ldr             x1, [x1, #0xa18]
    // 0xb2e604: StoreField: r0->field_1b = r1
    //     0xb2e604: stur            w1, [x0, #0x1b]
    // 0xb2e608: r1 = Instance_VerticalDirection
    //     0xb2e608: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2e60c: ldr             x1, [x1, #0xa20]
    // 0xb2e610: StoreField: r0->field_23 = r1
    //     0xb2e610: stur            w1, [x0, #0x23]
    // 0xb2e614: r1 = Instance_Clip
    //     0xb2e614: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2e618: ldr             x1, [x1, #0x38]
    // 0xb2e61c: StoreField: r0->field_2b = r1
    //     0xb2e61c: stur            w1, [x0, #0x2b]
    // 0xb2e620: StoreField: r0->field_2f = rZR
    //     0xb2e620: stur            xzr, [x0, #0x2f]
    // 0xb2e624: ldur            x1, [fp, #-8]
    // 0xb2e628: StoreField: r0->field_b = r1
    //     0xb2e628: stur            w1, [x0, #0xb]
    // 0xb2e62c: LeaveFrame
    //     0xb2e62c: mov             SP, fp
    //     0xb2e630: ldp             fp, lr, [SP], #0x10
    // 0xb2e634: ret
    //     0xb2e634: ret             
    // 0xb2e638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2e638: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2e63c: b               #0xb2e574
  }
  _ _buildQuantityWarning(/* No info */) {
    // ** addr: 0xb2e640, size: 0x2e4
    // 0xb2e640: EnterFrame
    //     0xb2e640: stp             fp, lr, [SP, #-0x10]!
    //     0xb2e644: mov             fp, SP
    // 0xb2e648: AllocStack(0x40)
    //     0xb2e648: sub             SP, SP, #0x40
    // 0xb2e64c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xb2e64c: mov             x0, x2
    //     0xb2e650: stur            x2, [fp, #-8]
    // 0xb2e654: CheckStackOverflow
    //     0xb2e654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2e658: cmp             SP, x16
    //     0xb2e65c: b.ls            #0xb2e904
    // 0xb2e660: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2e660: ldur            w2, [x1, #0x17]
    // 0xb2e664: DecompressPointer r2
    //     0xb2e664: add             x2, x2, HEAP, lsl #32
    // 0xb2e668: tbz             w2, #4, #0xb2e67c
    // 0xb2e66c: r0 = Instance_SizedBox
    //     0xb2e66c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb2e670: LeaveFrame
    //     0xb2e670: mov             SP, fp
    //     0xb2e674: ldp             fp, lr, [SP], #0x10
    // 0xb2e678: ret
    //     0xb2e678: ret             
    // 0xb2e67c: mov             x1, x0
    // 0xb2e680: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb2e680: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb2e684: r0 = _of()
    //     0xb2e684: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb2e688: LoadField: r1 = r0->field_7
    //     0xb2e688: ldur            w1, [x0, #7]
    // 0xb2e68c: DecompressPointer r1
    //     0xb2e68c: add             x1, x1, HEAP, lsl #32
    // 0xb2e690: LoadField: d0 = r1->field_7
    //     0xb2e690: ldur            d0, [x1, #7]
    // 0xb2e694: ldur            x1, [fp, #-8]
    // 0xb2e698: stur            d0, [fp, #-0x28]
    // 0xb2e69c: r0 = of()
    //     0xb2e69c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e6a0: LoadField: r1 = r0->field_5b
    //     0xb2e6a0: ldur            w1, [x0, #0x5b]
    // 0xb2e6a4: DecompressPointer r1
    //     0xb2e6a4: add             x1, x1, HEAP, lsl #32
    // 0xb2e6a8: r0 = LoadClassIdInstr(r1)
    //     0xb2e6a8: ldur            x0, [x1, #-1]
    //     0xb2e6ac: ubfx            x0, x0, #0xc, #0x14
    // 0xb2e6b0: d0 = 0.030000
    //     0xb2e6b0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb2e6b4: ldr             d0, [x17, #0x238]
    // 0xb2e6b8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb2e6b8: sub             lr, x0, #0xffa
    //     0xb2e6bc: ldr             lr, [x21, lr, lsl #3]
    //     0xb2e6c0: blr             lr
    // 0xb2e6c4: stur            x0, [fp, #-0x10]
    // 0xb2e6c8: r0 = Radius()
    //     0xb2e6c8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2e6cc: d0 = 15.000000
    //     0xb2e6cc: fmov            d0, #15.00000000
    // 0xb2e6d0: stur            x0, [fp, #-0x18]
    // 0xb2e6d4: StoreField: r0->field_7 = d0
    //     0xb2e6d4: stur            d0, [x0, #7]
    // 0xb2e6d8: StoreField: r0->field_f = d0
    //     0xb2e6d8: stur            d0, [x0, #0xf]
    // 0xb2e6dc: r0 = BorderRadius()
    //     0xb2e6dc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2e6e0: mov             x1, x0
    // 0xb2e6e4: ldur            x0, [fp, #-0x18]
    // 0xb2e6e8: stur            x1, [fp, #-0x20]
    // 0xb2e6ec: StoreField: r1->field_7 = r0
    //     0xb2e6ec: stur            w0, [x1, #7]
    // 0xb2e6f0: StoreField: r1->field_b = r0
    //     0xb2e6f0: stur            w0, [x1, #0xb]
    // 0xb2e6f4: StoreField: r1->field_f = r0
    //     0xb2e6f4: stur            w0, [x1, #0xf]
    // 0xb2e6f8: StoreField: r1->field_13 = r0
    //     0xb2e6f8: stur            w0, [x1, #0x13]
    // 0xb2e6fc: r0 = BoxDecoration()
    //     0xb2e6fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2e700: mov             x2, x0
    // 0xb2e704: ldur            x0, [fp, #-0x10]
    // 0xb2e708: stur            x2, [fp, #-0x18]
    // 0xb2e70c: StoreField: r2->field_7 = r0
    //     0xb2e70c: stur            w0, [x2, #7]
    // 0xb2e710: ldur            x0, [fp, #-0x20]
    // 0xb2e714: StoreField: r2->field_13 = r0
    //     0xb2e714: stur            w0, [x2, #0x13]
    // 0xb2e718: r0 = Instance_BoxShape
    //     0xb2e718: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2e71c: ldr             x0, [x0, #0x80]
    // 0xb2e720: StoreField: r2->field_23 = r0
    //     0xb2e720: stur            w0, [x2, #0x23]
    // 0xb2e724: ldur            x1, [fp, #-8]
    // 0xb2e728: r0 = of()
    //     0xb2e728: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2e72c: LoadField: r1 = r0->field_87
    //     0xb2e72c: ldur            w1, [x0, #0x87]
    // 0xb2e730: DecompressPointer r1
    //     0xb2e730: add             x1, x1, HEAP, lsl #32
    // 0xb2e734: LoadField: r0 = r1->field_7
    //     0xb2e734: ldur            w0, [x1, #7]
    // 0xb2e738: DecompressPointer r0
    //     0xb2e738: add             x0, x0, HEAP, lsl #32
    // 0xb2e73c: r16 = 14.000000
    //     0xb2e73c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2e740: ldr             x16, [x16, #0x1d8]
    // 0xb2e744: r30 = Instance_MaterialColor
    //     0xb2e744: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb2e748: ldr             lr, [lr, #0x180]
    // 0xb2e74c: stp             lr, x16, [SP]
    // 0xb2e750: mov             x1, x0
    // 0xb2e754: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2e754: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2e758: ldr             x4, [x4, #0xaa0]
    // 0xb2e75c: r0 = copyWith()
    //     0xb2e75c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2e760: stur            x0, [fp, #-8]
    // 0xb2e764: r0 = Text()
    //     0xb2e764: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2e768: mov             x2, x0
    // 0xb2e76c: r0 = "Some items in your bag has updated their quantity. Please remove excess quantity to proceed"
    //     0xb2e76c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54ca0] "Some items in your bag has updated their quantity. Please remove excess quantity to proceed"
    //     0xb2e770: ldr             x0, [x0, #0xca0]
    // 0xb2e774: stur            x2, [fp, #-0x10]
    // 0xb2e778: StoreField: r2->field_b = r0
    //     0xb2e778: stur            w0, [x2, #0xb]
    // 0xb2e77c: ldur            x0, [fp, #-8]
    // 0xb2e780: StoreField: r2->field_13 = r0
    //     0xb2e780: stur            w0, [x2, #0x13]
    // 0xb2e784: r1 = <FlexParentData>
    //     0xb2e784: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb2e788: ldr             x1, [x1, #0xe00]
    // 0xb2e78c: r0 = Expanded()
    //     0xb2e78c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2e790: mov             x3, x0
    // 0xb2e794: r0 = 1
    //     0xb2e794: movz            x0, #0x1
    // 0xb2e798: stur            x3, [fp, #-8]
    // 0xb2e79c: StoreField: r3->field_13 = r0
    //     0xb2e79c: stur            x0, [x3, #0x13]
    // 0xb2e7a0: r0 = Instance_FlexFit
    //     0xb2e7a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb2e7a4: ldr             x0, [x0, #0xe08]
    // 0xb2e7a8: StoreField: r3->field_1b = r0
    //     0xb2e7a8: stur            w0, [x3, #0x1b]
    // 0xb2e7ac: ldur            x0, [fp, #-0x10]
    // 0xb2e7b0: StoreField: r3->field_b = r0
    //     0xb2e7b0: stur            w0, [x3, #0xb]
    // 0xb2e7b4: r1 = Null
    //     0xb2e7b4: mov             x1, NULL
    // 0xb2e7b8: r2 = 6
    //     0xb2e7b8: movz            x2, #0x6
    // 0xb2e7bc: r0 = AllocateArray()
    //     0xb2e7bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2e7c0: mov             x2, x0
    // 0xb2e7c4: ldur            x0, [fp, #-8]
    // 0xb2e7c8: stur            x2, [fp, #-0x10]
    // 0xb2e7cc: StoreField: r2->field_f = r0
    //     0xb2e7cc: stur            w0, [x2, #0xf]
    // 0xb2e7d0: r16 = Instance_SizedBox
    //     0xb2e7d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb2e7d4: ldr             x16, [x16, #0xb20]
    // 0xb2e7d8: StoreField: r2->field_13 = r16
    //     0xb2e7d8: stur            w16, [x2, #0x13]
    // 0xb2e7dc: r16 = Instance_Icon
    //     0xb2e7dc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ca8] Obj!Icon@d661b1
    //     0xb2e7e0: ldr             x16, [x16, #0xca8]
    // 0xb2e7e4: ArrayStore: r2[0] = r16  ; List_4
    //     0xb2e7e4: stur            w16, [x2, #0x17]
    // 0xb2e7e8: r1 = <Widget>
    //     0xb2e7e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2e7ec: r0 = AllocateGrowableArray()
    //     0xb2e7ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2e7f0: mov             x1, x0
    // 0xb2e7f4: ldur            x0, [fp, #-0x10]
    // 0xb2e7f8: stur            x1, [fp, #-8]
    // 0xb2e7fc: StoreField: r1->field_f = r0
    //     0xb2e7fc: stur            w0, [x1, #0xf]
    // 0xb2e800: r0 = 6
    //     0xb2e800: movz            x0, #0x6
    // 0xb2e804: StoreField: r1->field_b = r0
    //     0xb2e804: stur            w0, [x1, #0xb]
    // 0xb2e808: r0 = Row()
    //     0xb2e808: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2e80c: mov             x1, x0
    // 0xb2e810: r0 = Instance_Axis
    //     0xb2e810: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2e814: stur            x1, [fp, #-0x10]
    // 0xb2e818: StoreField: r1->field_f = r0
    //     0xb2e818: stur            w0, [x1, #0xf]
    // 0xb2e81c: r0 = Instance_MainAxisAlignment
    //     0xb2e81c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb2e820: ldr             x0, [x0, #0xd10]
    // 0xb2e824: StoreField: r1->field_13 = r0
    //     0xb2e824: stur            w0, [x1, #0x13]
    // 0xb2e828: r0 = Instance_MainAxisSize
    //     0xb2e828: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2e82c: ldr             x0, [x0, #0xa10]
    // 0xb2e830: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2e830: stur            w0, [x1, #0x17]
    // 0xb2e834: r0 = Instance_CrossAxisAlignment
    //     0xb2e834: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2e838: ldr             x0, [x0, #0xa18]
    // 0xb2e83c: StoreField: r1->field_1b = r0
    //     0xb2e83c: stur            w0, [x1, #0x1b]
    // 0xb2e840: r0 = Instance_VerticalDirection
    //     0xb2e840: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2e844: ldr             x0, [x0, #0xa20]
    // 0xb2e848: StoreField: r1->field_23 = r0
    //     0xb2e848: stur            w0, [x1, #0x23]
    // 0xb2e84c: r0 = Instance_Clip
    //     0xb2e84c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2e850: ldr             x0, [x0, #0x38]
    // 0xb2e854: StoreField: r1->field_2b = r0
    //     0xb2e854: stur            w0, [x1, #0x2b]
    // 0xb2e858: StoreField: r1->field_2f = rZR
    //     0xb2e858: stur            xzr, [x1, #0x2f]
    // 0xb2e85c: ldur            x0, [fp, #-8]
    // 0xb2e860: StoreField: r1->field_b = r0
    //     0xb2e860: stur            w0, [x1, #0xb]
    // 0xb2e864: r0 = Padding()
    //     0xb2e864: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2e868: mov             x1, x0
    // 0xb2e86c: r0 = Instance_EdgeInsets
    //     0xb2e86c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb2e870: ldr             x0, [x0, #0x980]
    // 0xb2e874: stur            x1, [fp, #-0x20]
    // 0xb2e878: StoreField: r1->field_f = r0
    //     0xb2e878: stur            w0, [x1, #0xf]
    // 0xb2e87c: ldur            x0, [fp, #-0x10]
    // 0xb2e880: StoreField: r1->field_b = r0
    //     0xb2e880: stur            w0, [x1, #0xb]
    // 0xb2e884: ldur            d0, [fp, #-0x28]
    // 0xb2e888: r0 = inline_Allocate_Double()
    //     0xb2e888: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb2e88c: add             x0, x0, #0x10
    //     0xb2e890: cmp             x2, x0
    //     0xb2e894: b.ls            #0xb2e90c
    //     0xb2e898: str             x0, [THR, #0x50]  ; THR::top
    //     0xb2e89c: sub             x0, x0, #0xf
    //     0xb2e8a0: movz            x2, #0xe15c
    //     0xb2e8a4: movk            x2, #0x3, lsl #16
    //     0xb2e8a8: stur            x2, [x0, #-1]
    // 0xb2e8ac: StoreField: r0->field_7 = d0
    //     0xb2e8ac: stur            d0, [x0, #7]
    // 0xb2e8b0: stur            x0, [fp, #-8]
    // 0xb2e8b4: r0 = Container()
    //     0xb2e8b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2e8b8: stur            x0, [fp, #-0x10]
    // 0xb2e8bc: ldur            x16, [fp, #-8]
    // 0xb2e8c0: ldur            lr, [fp, #-0x18]
    // 0xb2e8c4: stp             lr, x16, [SP, #8]
    // 0xb2e8c8: ldur            x16, [fp, #-0x20]
    // 0xb2e8cc: str             x16, [SP]
    // 0xb2e8d0: mov             x1, x0
    // 0xb2e8d4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xb2e8d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xb2e8d8: ldr             x4, [x4, #0x830]
    // 0xb2e8dc: r0 = Container()
    //     0xb2e8dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2e8e0: r0 = Padding()
    //     0xb2e8e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2e8e4: r1 = Instance_EdgeInsets
    //     0xb2e8e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb2e8e8: ldr             x1, [x1, #0x1f0]
    // 0xb2e8ec: StoreField: r0->field_f = r1
    //     0xb2e8ec: stur            w1, [x0, #0xf]
    // 0xb2e8f0: ldur            x1, [fp, #-0x10]
    // 0xb2e8f4: StoreField: r0->field_b = r1
    //     0xb2e8f4: stur            w1, [x0, #0xb]
    // 0xb2e8f8: LeaveFrame
    //     0xb2e8f8: mov             SP, fp
    //     0xb2e8fc: ldp             fp, lr, [SP], #0x10
    // 0xb2e900: ret
    //     0xb2e900: ret             
    // 0xb2e904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2e904: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2e908: b               #0xb2e660
    // 0xb2e90c: SaveReg d0
    //     0xb2e90c: str             q0, [SP, #-0x10]!
    // 0xb2e910: SaveReg r1
    //     0xb2e910: str             x1, [SP, #-8]!
    // 0xb2e914: r0 = AllocateDouble()
    //     0xb2e914: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb2e918: RestoreReg r1
    //     0xb2e918: ldr             x1, [SP], #8
    // 0xb2e91c: RestoreReg d0
    //     0xb2e91c: ldr             q0, [SP], #0x10
    // 0xb2e920: b               #0xb2e8ac
  }
  _ _buildSoldOutWarning(/* No info */) {
    // ** addr: 0xb2e924, size: 0x34c
    // 0xb2e924: EnterFrame
    //     0xb2e924: stp             fp, lr, [SP, #-0x10]!
    //     0xb2e928: mov             fp, SP
    // 0xb2e92c: AllocStack(0x40)
    //     0xb2e92c: sub             SP, SP, #0x40
    // 0xb2e930: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xb2e930: mov             x0, x2
    //     0xb2e934: stur            x2, [fp, #-8]
    // 0xb2e938: CheckStackOverflow
    //     0xb2e938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2e93c: cmp             SP, x16
    //     0xb2e940: b.ls            #0xb2ec48
    // 0xb2e944: LoadField: r2 = r1->field_b
    //     0xb2e944: ldur            w2, [x1, #0xb]
    // 0xb2e948: DecompressPointer r2
    //     0xb2e948: add             x2, x2, HEAP, lsl #32
    // 0xb2e94c: cmp             w2, NULL
    // 0xb2e950: b.eq            #0xb2ec50
    // 0xb2e954: LoadField: r1 = r2->field_b
    //     0xb2e954: ldur            w1, [x2, #0xb]
    // 0xb2e958: DecompressPointer r1
    //     0xb2e958: add             x1, x1, HEAP, lsl #32
    // 0xb2e95c: cmp             w1, NULL
    // 0xb2e960: b.ne            #0xb2e96c
    // 0xb2e964: r1 = Null
    //     0xb2e964: mov             x1, NULL
    // 0xb2e968: b               #0xb2e978
    // 0xb2e96c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2e96c: ldur            w2, [x1, #0x17]
    // 0xb2e970: DecompressPointer r2
    //     0xb2e970: add             x2, x2, HEAP, lsl #32
    // 0xb2e974: mov             x1, x2
    // 0xb2e978: cmp             w1, NULL
    // 0xb2e97c: b.ne            #0xb2e998
    // 0xb2e980: r1 = <Catalogue>
    //     0xb2e980: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0xb2e984: ldr             x1, [x1, #0x418]
    // 0xb2e988: r2 = 0
    //     0xb2e988: movz            x2, #0
    // 0xb2e98c: r0 = _GrowableList()
    //     0xb2e98c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb2e990: mov             x2, x0
    // 0xb2e994: b               #0xb2e99c
    // 0xb2e998: mov             x2, x1
    // 0xb2e99c: LoadField: r0 = r2->field_b
    //     0xb2e99c: ldur            w0, [x2, #0xb]
    // 0xb2e9a0: r1 = LoadInt32Instr(r0)
    //     0xb2e9a0: sbfx            x1, x0, #1, #0x1f
    // 0xb2e9a4: cmp             x1, #1
    // 0xb2e9a8: b.ne            #0xb2ec38
    // 0xb2e9ac: mov             x0, x1
    // 0xb2e9b0: r1 = 0
    //     0xb2e9b0: movz            x1, #0
    // 0xb2e9b4: cmp             x1, x0
    // 0xb2e9b8: b.hs            #0xb2ec54
    // 0xb2e9bc: LoadField: r0 = r2->field_f
    //     0xb2e9bc: ldur            w0, [x2, #0xf]
    // 0xb2e9c0: DecompressPointer r0
    //     0xb2e9c0: add             x0, x0, HEAP, lsl #32
    // 0xb2e9c4: LoadField: r1 = r0->field_f
    //     0xb2e9c4: ldur            w1, [x0, #0xf]
    // 0xb2e9c8: DecompressPointer r1
    //     0xb2e9c8: add             x1, x1, HEAP, lsl #32
    // 0xb2e9cc: LoadField: r0 = r1->field_33
    //     0xb2e9cc: ldur            w0, [x1, #0x33]
    // 0xb2e9d0: DecompressPointer r0
    //     0xb2e9d0: add             x0, x0, HEAP, lsl #32
    // 0xb2e9d4: cmp             w0, NULL
    // 0xb2e9d8: b.eq            #0xb2ec38
    // 0xb2e9dc: tbnz            w0, #4, #0xb2ec38
    // 0xb2e9e0: ldur            x1, [fp, #-8]
    // 0xb2e9e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb2e9e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb2e9e8: r0 = _of()
    //     0xb2e9e8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb2e9ec: LoadField: r1 = r0->field_7
    //     0xb2e9ec: ldur            w1, [x0, #7]
    // 0xb2e9f0: DecompressPointer r1
    //     0xb2e9f0: add             x1, x1, HEAP, lsl #32
    // 0xb2e9f4: LoadField: d0 = r1->field_7
    //     0xb2e9f4: ldur            d0, [x1, #7]
    // 0xb2e9f8: ldur            x1, [fp, #-8]
    // 0xb2e9fc: stur            d0, [fp, #-0x28]
    // 0xb2ea00: r0 = of()
    //     0xb2ea00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ea04: LoadField: r1 = r0->field_5b
    //     0xb2ea04: ldur            w1, [x0, #0x5b]
    // 0xb2ea08: DecompressPointer r1
    //     0xb2ea08: add             x1, x1, HEAP, lsl #32
    // 0xb2ea0c: r0 = LoadClassIdInstr(r1)
    //     0xb2ea0c: ldur            x0, [x1, #-1]
    //     0xb2ea10: ubfx            x0, x0, #0xc, #0x14
    // 0xb2ea14: d0 = 0.030000
    //     0xb2ea14: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb2ea18: ldr             d0, [x17, #0x238]
    // 0xb2ea1c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb2ea1c: sub             lr, x0, #0xffa
    //     0xb2ea20: ldr             lr, [x21, lr, lsl #3]
    //     0xb2ea24: blr             lr
    // 0xb2ea28: stur            x0, [fp, #-0x10]
    // 0xb2ea2c: r0 = Radius()
    //     0xb2ea2c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb2ea30: d0 = 15.000000
    //     0xb2ea30: fmov            d0, #15.00000000
    // 0xb2ea34: stur            x0, [fp, #-0x18]
    // 0xb2ea38: StoreField: r0->field_7 = d0
    //     0xb2ea38: stur            d0, [x0, #7]
    // 0xb2ea3c: StoreField: r0->field_f = d0
    //     0xb2ea3c: stur            d0, [x0, #0xf]
    // 0xb2ea40: r0 = BorderRadius()
    //     0xb2ea40: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb2ea44: mov             x1, x0
    // 0xb2ea48: ldur            x0, [fp, #-0x18]
    // 0xb2ea4c: stur            x1, [fp, #-0x20]
    // 0xb2ea50: StoreField: r1->field_7 = r0
    //     0xb2ea50: stur            w0, [x1, #7]
    // 0xb2ea54: StoreField: r1->field_b = r0
    //     0xb2ea54: stur            w0, [x1, #0xb]
    // 0xb2ea58: StoreField: r1->field_f = r0
    //     0xb2ea58: stur            w0, [x1, #0xf]
    // 0xb2ea5c: StoreField: r1->field_13 = r0
    //     0xb2ea5c: stur            w0, [x1, #0x13]
    // 0xb2ea60: r0 = BoxDecoration()
    //     0xb2ea60: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2ea64: mov             x2, x0
    // 0xb2ea68: ldur            x0, [fp, #-0x10]
    // 0xb2ea6c: stur            x2, [fp, #-0x18]
    // 0xb2ea70: StoreField: r2->field_7 = r0
    //     0xb2ea70: stur            w0, [x2, #7]
    // 0xb2ea74: ldur            x0, [fp, #-0x20]
    // 0xb2ea78: StoreField: r2->field_13 = r0
    //     0xb2ea78: stur            w0, [x2, #0x13]
    // 0xb2ea7c: r0 = Instance_BoxShape
    //     0xb2ea7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2ea80: ldr             x0, [x0, #0x80]
    // 0xb2ea84: StoreField: r2->field_23 = r0
    //     0xb2ea84: stur            w0, [x2, #0x23]
    // 0xb2ea88: ldur            x1, [fp, #-8]
    // 0xb2ea8c: r0 = of()
    //     0xb2ea8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ea90: LoadField: r1 = r0->field_87
    //     0xb2ea90: ldur            w1, [x0, #0x87]
    // 0xb2ea94: DecompressPointer r1
    //     0xb2ea94: add             x1, x1, HEAP, lsl #32
    // 0xb2ea98: LoadField: r0 = r1->field_7
    //     0xb2ea98: ldur            w0, [x1, #7]
    // 0xb2ea9c: DecompressPointer r0
    //     0xb2ea9c: add             x0, x0, HEAP, lsl #32
    // 0xb2eaa0: r16 = 14.000000
    //     0xb2eaa0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2eaa4: ldr             x16, [x16, #0x1d8]
    // 0xb2eaa8: r30 = Instance_MaterialColor
    //     0xb2eaa8: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb2eaac: ldr             lr, [lr, #0x180]
    // 0xb2eab0: stp             lr, x16, [SP]
    // 0xb2eab4: mov             x1, x0
    // 0xb2eab8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2eab8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2eabc: ldr             x4, [x4, #0xaa0]
    // 0xb2eac0: r0 = copyWith()
    //     0xb2eac0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2eac4: stur            x0, [fp, #-8]
    // 0xb2eac8: r0 = Text()
    //     0xb2eac8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2eacc: mov             x3, x0
    // 0xb2ead0: r0 = "All products of your bag are sold out!"
    //     0xb2ead0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54cb0] "All products of your bag are sold out!"
    //     0xb2ead4: ldr             x0, [x0, #0xcb0]
    // 0xb2ead8: stur            x3, [fp, #-0x10]
    // 0xb2eadc: StoreField: r3->field_b = r0
    //     0xb2eadc: stur            w0, [x3, #0xb]
    // 0xb2eae0: ldur            x0, [fp, #-8]
    // 0xb2eae4: StoreField: r3->field_13 = r0
    //     0xb2eae4: stur            w0, [x3, #0x13]
    // 0xb2eae8: r1 = Null
    //     0xb2eae8: mov             x1, NULL
    // 0xb2eaec: r2 = 6
    //     0xb2eaec: movz            x2, #0x6
    // 0xb2eaf0: r0 = AllocateArray()
    //     0xb2eaf0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2eaf4: mov             x2, x0
    // 0xb2eaf8: ldur            x0, [fp, #-0x10]
    // 0xb2eafc: stur            x2, [fp, #-8]
    // 0xb2eb00: StoreField: r2->field_f = r0
    //     0xb2eb00: stur            w0, [x2, #0xf]
    // 0xb2eb04: r16 = Instance_Spacer
    //     0xb2eb04: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2eb08: ldr             x16, [x16, #0xf0]
    // 0xb2eb0c: StoreField: r2->field_13 = r16
    //     0xb2eb0c: stur            w16, [x2, #0x13]
    // 0xb2eb10: r16 = Instance_Icon
    //     0xb2eb10: add             x16, PP, #0x54, lsl #12  ; [pp+0x54cb8] Obj!Icon@d661f1
    //     0xb2eb14: ldr             x16, [x16, #0xcb8]
    // 0xb2eb18: ArrayStore: r2[0] = r16  ; List_4
    //     0xb2eb18: stur            w16, [x2, #0x17]
    // 0xb2eb1c: r1 = <Widget>
    //     0xb2eb1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2eb20: r0 = AllocateGrowableArray()
    //     0xb2eb20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2eb24: mov             x1, x0
    // 0xb2eb28: ldur            x0, [fp, #-8]
    // 0xb2eb2c: stur            x1, [fp, #-0x10]
    // 0xb2eb30: StoreField: r1->field_f = r0
    //     0xb2eb30: stur            w0, [x1, #0xf]
    // 0xb2eb34: r0 = 6
    //     0xb2eb34: movz            x0, #0x6
    // 0xb2eb38: StoreField: r1->field_b = r0
    //     0xb2eb38: stur            w0, [x1, #0xb]
    // 0xb2eb3c: r0 = Row()
    //     0xb2eb3c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2eb40: mov             x1, x0
    // 0xb2eb44: r0 = Instance_Axis
    //     0xb2eb44: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2eb48: stur            x1, [fp, #-8]
    // 0xb2eb4c: StoreField: r1->field_f = r0
    //     0xb2eb4c: stur            w0, [x1, #0xf]
    // 0xb2eb50: r0 = Instance_MainAxisAlignment
    //     0xb2eb50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb2eb54: ldr             x0, [x0, #0xd10]
    // 0xb2eb58: StoreField: r1->field_13 = r0
    //     0xb2eb58: stur            w0, [x1, #0x13]
    // 0xb2eb5c: r0 = Instance_MainAxisSize
    //     0xb2eb5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2eb60: ldr             x0, [x0, #0xa10]
    // 0xb2eb64: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2eb64: stur            w0, [x1, #0x17]
    // 0xb2eb68: r0 = Instance_CrossAxisAlignment
    //     0xb2eb68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2eb6c: ldr             x0, [x0, #0xa18]
    // 0xb2eb70: StoreField: r1->field_1b = r0
    //     0xb2eb70: stur            w0, [x1, #0x1b]
    // 0xb2eb74: r0 = Instance_VerticalDirection
    //     0xb2eb74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2eb78: ldr             x0, [x0, #0xa20]
    // 0xb2eb7c: StoreField: r1->field_23 = r0
    //     0xb2eb7c: stur            w0, [x1, #0x23]
    // 0xb2eb80: r0 = Instance_Clip
    //     0xb2eb80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2eb84: ldr             x0, [x0, #0x38]
    // 0xb2eb88: StoreField: r1->field_2b = r0
    //     0xb2eb88: stur            w0, [x1, #0x2b]
    // 0xb2eb8c: StoreField: r1->field_2f = rZR
    //     0xb2eb8c: stur            xzr, [x1, #0x2f]
    // 0xb2eb90: ldur            x0, [fp, #-0x10]
    // 0xb2eb94: StoreField: r1->field_b = r0
    //     0xb2eb94: stur            w0, [x1, #0xb]
    // 0xb2eb98: r0 = Padding()
    //     0xb2eb98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2eb9c: mov             x1, x0
    // 0xb2eba0: r0 = Instance_EdgeInsets
    //     0xb2eba0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb2eba4: ldr             x0, [x0, #0x1f0]
    // 0xb2eba8: stur            x1, [fp, #-0x10]
    // 0xb2ebac: StoreField: r1->field_f = r0
    //     0xb2ebac: stur            w0, [x1, #0xf]
    // 0xb2ebb0: ldur            x0, [fp, #-8]
    // 0xb2ebb4: StoreField: r1->field_b = r0
    //     0xb2ebb4: stur            w0, [x1, #0xb]
    // 0xb2ebb8: ldur            d0, [fp, #-0x28]
    // 0xb2ebbc: r0 = inline_Allocate_Double()
    //     0xb2ebbc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb2ebc0: add             x0, x0, #0x10
    //     0xb2ebc4: cmp             x2, x0
    //     0xb2ebc8: b.ls            #0xb2ec58
    //     0xb2ebcc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb2ebd0: sub             x0, x0, #0xf
    //     0xb2ebd4: movz            x2, #0xe15c
    //     0xb2ebd8: movk            x2, #0x3, lsl #16
    //     0xb2ebdc: stur            x2, [x0, #-1]
    // 0xb2ebe0: StoreField: r0->field_7 = d0
    //     0xb2ebe0: stur            d0, [x0, #7]
    // 0xb2ebe4: stur            x0, [fp, #-8]
    // 0xb2ebe8: r0 = Container()
    //     0xb2ebe8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2ebec: stur            x0, [fp, #-0x20]
    // 0xb2ebf0: ldur            x16, [fp, #-8]
    // 0xb2ebf4: ldur            lr, [fp, #-0x18]
    // 0xb2ebf8: stp             lr, x16, [SP, #8]
    // 0xb2ebfc: ldur            x16, [fp, #-0x10]
    // 0xb2ec00: str             x16, [SP]
    // 0xb2ec04: mov             x1, x0
    // 0xb2ec08: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xb2ec08: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xb2ec0c: ldr             x4, [x4, #0x830]
    // 0xb2ec10: r0 = Container()
    //     0xb2ec10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2ec14: r0 = Padding()
    //     0xb2ec14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2ec18: r1 = Instance_EdgeInsets
    //     0xb2ec18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb2ec1c: ldr             x1, [x1, #0x980]
    // 0xb2ec20: StoreField: r0->field_f = r1
    //     0xb2ec20: stur            w1, [x0, #0xf]
    // 0xb2ec24: ldur            x1, [fp, #-0x20]
    // 0xb2ec28: StoreField: r0->field_b = r1
    //     0xb2ec28: stur            w1, [x0, #0xb]
    // 0xb2ec2c: LeaveFrame
    //     0xb2ec2c: mov             SP, fp
    //     0xb2ec30: ldp             fp, lr, [SP], #0x10
    // 0xb2ec34: ret
    //     0xb2ec34: ret             
    // 0xb2ec38: r0 = Instance_SizedBox
    //     0xb2ec38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb2ec3c: LeaveFrame
    //     0xb2ec3c: mov             SP, fp
    //     0xb2ec40: ldp             fp, lr, [SP], #0x10
    // 0xb2ec44: ret
    //     0xb2ec44: ret             
    // 0xb2ec48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2ec48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2ec4c: b               #0xb2e944
    // 0xb2ec50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ec50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2ec54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2ec54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2ec58: SaveReg d0
    //     0xb2ec58: str             q0, [SP, #-0x10]!
    // 0xb2ec5c: SaveReg r1
    //     0xb2ec5c: str             x1, [SP, #-8]!
    // 0xb2ec60: r0 = AllocateDouble()
    //     0xb2ec60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb2ec64: RestoreReg r1
    //     0xb2ec64: ldr             x1, [SP], #8
    // 0xb2ec68: RestoreReg d0
    //     0xb2ec68: ldr             q0, [SP], #0x10
    // 0xb2ec6c: b               #0xb2ebe0
  }
  _ _buildItemsCount(/* No info */) {
    // ** addr: 0xb2ec70, size: 0x128
    // 0xb2ec70: EnterFrame
    //     0xb2ec70: stp             fp, lr, [SP, #-0x10]!
    //     0xb2ec74: mov             fp, SP
    // 0xb2ec78: AllocStack(0x28)
    //     0xb2ec78: sub             SP, SP, #0x28
    // 0xb2ec7c: SetupParameters(_BagItemViewWidgetState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb2ec7c: mov             x3, x1
    //     0xb2ec80: mov             x0, x2
    //     0xb2ec84: stur            x1, [fp, #-8]
    //     0xb2ec88: stur            x2, [fp, #-0x10]
    // 0xb2ec8c: CheckStackOverflow
    //     0xb2ec8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2ec90: cmp             SP, x16
    //     0xb2ec94: b.ls            #0xb2ed8c
    // 0xb2ec98: r1 = Null
    //     0xb2ec98: mov             x1, NULL
    // 0xb2ec9c: r2 = 6
    //     0xb2ec9c: movz            x2, #0x6
    // 0xb2eca0: r0 = AllocateArray()
    //     0xb2eca0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2eca4: r16 = "Items ("
    //     0xb2eca4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57310] "Items ("
    //     0xb2eca8: ldr             x16, [x16, #0x310]
    // 0xb2ecac: StoreField: r0->field_f = r16
    //     0xb2ecac: stur            w16, [x0, #0xf]
    // 0xb2ecb0: ldur            x1, [fp, #-8]
    // 0xb2ecb4: LoadField: r2 = r1->field_b
    //     0xb2ecb4: ldur            w2, [x1, #0xb]
    // 0xb2ecb8: DecompressPointer r2
    //     0xb2ecb8: add             x2, x2, HEAP, lsl #32
    // 0xb2ecbc: cmp             w2, NULL
    // 0xb2ecc0: b.eq            #0xb2ed94
    // 0xb2ecc4: LoadField: r1 = r2->field_b
    //     0xb2ecc4: ldur            w1, [x2, #0xb]
    // 0xb2ecc8: DecompressPointer r1
    //     0xb2ecc8: add             x1, x1, HEAP, lsl #32
    // 0xb2eccc: cmp             w1, NULL
    // 0xb2ecd0: b.ne            #0xb2ecdc
    // 0xb2ecd4: r1 = Null
    //     0xb2ecd4: mov             x1, NULL
    // 0xb2ecd8: b               #0xb2ece8
    // 0xb2ecdc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2ecdc: ldur            w2, [x1, #0x17]
    // 0xb2ece0: DecompressPointer r2
    //     0xb2ece0: add             x2, x2, HEAP, lsl #32
    // 0xb2ece4: LoadField: r1 = r2->field_b
    //     0xb2ece4: ldur            w1, [x2, #0xb]
    // 0xb2ece8: StoreField: r0->field_13 = r1
    //     0xb2ece8: stur            w1, [x0, #0x13]
    // 0xb2ecec: r16 = ")"
    //     0xb2ecec: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb2ecf0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb2ecf0: stur            w16, [x0, #0x17]
    // 0xb2ecf4: str             x0, [SP]
    // 0xb2ecf8: r0 = _interpolate()
    //     0xb2ecf8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2ecfc: ldur            x1, [fp, #-0x10]
    // 0xb2ed00: stur            x0, [fp, #-8]
    // 0xb2ed04: r0 = of()
    //     0xb2ed04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2ed08: LoadField: r1 = r0->field_87
    //     0xb2ed08: ldur            w1, [x0, #0x87]
    // 0xb2ed0c: DecompressPointer r1
    //     0xb2ed0c: add             x1, x1, HEAP, lsl #32
    // 0xb2ed10: LoadField: r0 = r1->field_7
    //     0xb2ed10: ldur            w0, [x1, #7]
    // 0xb2ed14: DecompressPointer r0
    //     0xb2ed14: add             x0, x0, HEAP, lsl #32
    // 0xb2ed18: stur            x0, [fp, #-0x10]
    // 0xb2ed1c: r1 = Instance_Color
    //     0xb2ed1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2ed20: d0 = 0.700000
    //     0xb2ed20: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb2ed24: ldr             d0, [x17, #0xf48]
    // 0xb2ed28: r0 = withOpacity()
    //     0xb2ed28: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2ed2c: r16 = 14.000000
    //     0xb2ed2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2ed30: ldr             x16, [x16, #0x1d8]
    // 0xb2ed34: stp             x0, x16, [SP]
    // 0xb2ed38: ldur            x1, [fp, #-0x10]
    // 0xb2ed3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2ed3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2ed40: ldr             x4, [x4, #0xaa0]
    // 0xb2ed44: r0 = copyWith()
    //     0xb2ed44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2ed48: stur            x0, [fp, #-0x10]
    // 0xb2ed4c: r0 = Text()
    //     0xb2ed4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2ed50: mov             x1, x0
    // 0xb2ed54: ldur            x0, [fp, #-8]
    // 0xb2ed58: stur            x1, [fp, #-0x18]
    // 0xb2ed5c: StoreField: r1->field_b = r0
    //     0xb2ed5c: stur            w0, [x1, #0xb]
    // 0xb2ed60: ldur            x0, [fp, #-0x10]
    // 0xb2ed64: StoreField: r1->field_13 = r0
    //     0xb2ed64: stur            w0, [x1, #0x13]
    // 0xb2ed68: r0 = Padding()
    //     0xb2ed68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2ed6c: r1 = Instance_EdgeInsets
    //     0xb2ed6c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb2ed70: ldr             x1, [x1, #0x980]
    // 0xb2ed74: StoreField: r0->field_f = r1
    //     0xb2ed74: stur            w1, [x0, #0xf]
    // 0xb2ed78: ldur            x1, [fp, #-0x18]
    // 0xb2ed7c: StoreField: r0->field_b = r1
    //     0xb2ed7c: stur            w1, [x0, #0xb]
    // 0xb2ed80: LeaveFrame
    //     0xb2ed80: mov             SP, fp
    //     0xb2ed84: ldp             fp, lr, [SP], #0x10
    // 0xb2ed88: ret
    //     0xb2ed88: ret             
    // 0xb2ed8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2ed8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2ed90: b               #0xb2ec98
    // 0xb2ed94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ed94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0xb2ed98, size: 0xa4
    // 0xb2ed98: EnterFrame
    //     0xb2ed98: stp             fp, lr, [SP, #-0x10]!
    //     0xb2ed9c: mov             fp, SP
    // 0xb2eda0: AllocStack(0x20)
    //     0xb2eda0: sub             SP, SP, #0x20
    // 0xb2eda4: SetupParameters(_BagItemViewWidgetState this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xb2eda4: mov             x0, x1
    //     0xb2eda8: mov             x1, x2
    // 0xb2edac: CheckStackOverflow
    //     0xb2edac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2edb0: cmp             SP, x16
    //     0xb2edb4: b.ls            #0xb2ee34
    // 0xb2edb8: r0 = of()
    //     0xb2edb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2edbc: LoadField: r1 = r0->field_87
    //     0xb2edbc: ldur            w1, [x0, #0x87]
    // 0xb2edc0: DecompressPointer r1
    //     0xb2edc0: add             x1, x1, HEAP, lsl #32
    // 0xb2edc4: LoadField: r0 = r1->field_7
    //     0xb2edc4: ldur            w0, [x1, #7]
    // 0xb2edc8: DecompressPointer r0
    //     0xb2edc8: add             x0, x0, HEAP, lsl #32
    // 0xb2edcc: r16 = 16.000000
    //     0xb2edcc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2edd0: ldr             x16, [x16, #0x188]
    // 0xb2edd4: r30 = Instance_Color
    //     0xb2edd4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2edd8: stp             lr, x16, [SP]
    // 0xb2eddc: mov             x1, x0
    // 0xb2ede0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2ede0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2ede4: ldr             x4, [x4, #0xaa0]
    // 0xb2ede8: r0 = copyWith()
    //     0xb2ede8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2edec: stur            x0, [fp, #-8]
    // 0xb2edf0: r0 = Text()
    //     0xb2edf0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2edf4: mov             x1, x0
    // 0xb2edf8: r0 = "My Bag"
    //     0xb2edf8: add             x0, PP, #0x48, lsl #12  ; [pp+0x489b8] "My Bag"
    //     0xb2edfc: ldr             x0, [x0, #0x9b8]
    // 0xb2ee00: stur            x1, [fp, #-0x10]
    // 0xb2ee04: StoreField: r1->field_b = r0
    //     0xb2ee04: stur            w0, [x1, #0xb]
    // 0xb2ee08: ldur            x0, [fp, #-8]
    // 0xb2ee0c: StoreField: r1->field_13 = r0
    //     0xb2ee0c: stur            w0, [x1, #0x13]
    // 0xb2ee10: r0 = Padding()
    //     0xb2ee10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2ee14: r1 = Instance_EdgeInsets
    //     0xb2ee14: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb2ee18: ldr             x1, [x1, #0x980]
    // 0xb2ee1c: StoreField: r0->field_f = r1
    //     0xb2ee1c: stur            w1, [x0, #0xf]
    // 0xb2ee20: ldur            x1, [fp, #-0x10]
    // 0xb2ee24: StoreField: r0->field_b = r1
    //     0xb2ee24: stur            w1, [x0, #0xb]
    // 0xb2ee28: LeaveFrame
    //     0xb2ee28: mov             SP, fp
    //     0xb2ee2c: ldp             fp, lr, [SP], #0x10
    // 0xb2ee30: ret
    //     0xb2ee30: ret             
    // 0xb2ee34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2ee34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2ee38: b               #0xb2edb8
  }
}

// class id: 4121, size: 0x40, field offset: 0xc
//   const constructor, 
class BagItemViewWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e594, size: 0x78
    // 0xc7e594: EnterFrame
    //     0xc7e594: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e598: mov             fp, SP
    // 0xc7e59c: AllocStack(0x8)
    //     0xc7e59c: sub             SP, SP, #8
    // 0xc7e5a0: CheckStackOverflow
    //     0xc7e5a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e5a4: cmp             SP, x16
    //     0xc7e5a8: b.ls            #0xc7e604
    // 0xc7e5ac: r1 = <BagItemViewWidget>
    //     0xc7e5ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x48aa8] TypeArguments: <BagItemViewWidget>
    //     0xc7e5b0: ldr             x1, [x1, #0xaa8]
    // 0xc7e5b4: r0 = _BagItemViewWidgetState()
    //     0xc7e5b4: bl              #0xc7e60c  ; Allocate_BagItemViewWidgetStateStub -> _BagItemViewWidgetState (size=0x1c)
    // 0xc7e5b8: mov             x3, x0
    // 0xc7e5bc: r0 = false
    //     0xc7e5bc: add             x0, NULL, #0x30  ; false
    // 0xc7e5c0: stur            x3, [fp, #-8]
    // 0xc7e5c4: ArrayStore: r3[0] = r0  ; List_4
    //     0xc7e5c4: stur            w0, [x3, #0x17]
    // 0xc7e5c8: r1 = <int>
    //     0xc7e5c8: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc7e5cc: r2 = 0
    //     0xc7e5cc: movz            x2, #0
    // 0xc7e5d0: r0 = _GrowableList()
    //     0xc7e5d0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc7e5d4: ldur            x1, [fp, #-8]
    // 0xc7e5d8: StoreField: r1->field_13 = r0
    //     0xc7e5d8: stur            w0, [x1, #0x13]
    //     0xc7e5dc: ldurb           w16, [x1, #-1]
    //     0xc7e5e0: ldurb           w17, [x0, #-1]
    //     0xc7e5e4: and             x16, x17, x16, lsr #2
    //     0xc7e5e8: tst             x16, HEAP, lsr #32
    //     0xc7e5ec: b.eq            #0xc7e5f4
    //     0xc7e5f0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7e5f4: mov             x0, x1
    // 0xc7e5f8: LeaveFrame
    //     0xc7e5f8: mov             SP, fp
    //     0xc7e5fc: ldp             fp, lr, [SP], #0x10
    // 0xc7e600: ret
    //     0xc7e600: ret             
    // 0xc7e604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e604: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e608: b               #0xc7e5ac
  }
}

// class id: 4495, size: 0x18, field offset: 0xc
//   const constructor, 
class _FreeGiftDialog extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x12906c0, size: 0xc18
    // 0x12906c0: EnterFrame
    //     0x12906c0: stp             fp, lr, [SP, #-0x10]!
    //     0x12906c4: mov             fp, SP
    // 0x12906c8: AllocStack(0x70)
    //     0x12906c8: sub             SP, SP, #0x70
    // 0x12906cc: SetupParameters(_FreeGiftDialog this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x12906cc: mov             x0, x1
    //     0x12906d0: stur            x1, [fp, #-8]
    //     0x12906d4: mov             x1, x2
    //     0x12906d8: stur            x2, [fp, #-0x10]
    // 0x12906dc: CheckStackOverflow
    //     0x12906dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12906e0: cmp             SP, x16
    //     0x12906e4: b.ls            #0x1291238
    // 0x12906e8: r1 = 2
    //     0x12906e8: movz            x1, #0x2
    // 0x12906ec: r0 = AllocateContext()
    //     0x12906ec: bl              #0x16f6108  ; AllocateContextStub
    // 0x12906f0: mov             x2, x0
    // 0x12906f4: ldur            x0, [fp, #-8]
    // 0x12906f8: stur            x2, [fp, #-0x18]
    // 0x12906fc: StoreField: r2->field_f = r0
    //     0x12906fc: stur            w0, [x2, #0xf]
    // 0x1290700: ldur            x1, [fp, #-0x10]
    // 0x1290704: StoreField: r2->field_13 = r1
    //     0x1290704: stur            w1, [x2, #0x13]
    // 0x1290708: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290708: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x129070c: r0 = _of()
    //     0x129070c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290710: LoadField: r1 = r0->field_7
    //     0x1290710: ldur            w1, [x0, #7]
    // 0x1290714: DecompressPointer r1
    //     0x1290714: add             x1, x1, HEAP, lsl #32
    // 0x1290718: LoadField: d0 = r1->field_7
    //     0x1290718: ldur            d0, [x1, #7]
    // 0x129071c: d1 = 0.880000
    //     0x129071c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0x1290720: ldr             d1, [x17, #0x8e0]
    // 0x1290724: fmul            d2, d0, d1
    // 0x1290728: ldur            x2, [fp, #-0x18]
    // 0x129072c: stur            d2, [fp, #-0x48]
    // 0x1290730: LoadField: r1 = r2->field_13
    //     0x1290730: ldur            w1, [x2, #0x13]
    // 0x1290734: DecompressPointer r1
    //     0x1290734: add             x1, x1, HEAP, lsl #32
    // 0x1290738: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290738: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x129073c: r0 = _of()
    //     0x129073c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290740: LoadField: r1 = r0->field_7
    //     0x1290740: ldur            w1, [x0, #7]
    // 0x1290744: DecompressPointer r1
    //     0x1290744: add             x1, x1, HEAP, lsl #32
    // 0x1290748: LoadField: d0 = r1->field_7
    //     0x1290748: ldur            d0, [x1, #7]
    // 0x129074c: d1 = 0.880000
    //     0x129074c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0x1290750: ldr             d1, [x17, #0x8e0]
    // 0x1290754: fmul            d2, d0, d1
    // 0x1290758: stur            d2, [fp, #-0x50]
    // 0x129075c: r0 = Radius()
    //     0x129075c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1290760: d0 = 12.000000
    //     0x1290760: fmov            d0, #12.00000000
    // 0x1290764: stur            x0, [fp, #-0x10]
    // 0x1290768: StoreField: r0->field_7 = d0
    //     0x1290768: stur            d0, [x0, #7]
    // 0x129076c: StoreField: r0->field_f = d0
    //     0x129076c: stur            d0, [x0, #0xf]
    // 0x1290770: r0 = BorderRadius()
    //     0x1290770: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1290774: mov             x1, x0
    // 0x1290778: ldur            x0, [fp, #-0x10]
    // 0x129077c: stur            x1, [fp, #-0x20]
    // 0x1290780: StoreField: r1->field_7 = r0
    //     0x1290780: stur            w0, [x1, #7]
    // 0x1290784: StoreField: r1->field_b = r0
    //     0x1290784: stur            w0, [x1, #0xb]
    // 0x1290788: StoreField: r1->field_f = r0
    //     0x1290788: stur            w0, [x1, #0xf]
    // 0x129078c: StoreField: r1->field_13 = r0
    //     0x129078c: stur            w0, [x1, #0x13]
    // 0x1290790: r0 = RoundedRectangleBorder()
    //     0x1290790: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1290794: mov             x3, x0
    // 0x1290798: ldur            x0, [fp, #-0x20]
    // 0x129079c: stur            x3, [fp, #-0x10]
    // 0x12907a0: StoreField: r3->field_b = r0
    //     0x12907a0: stur            w0, [x3, #0xb]
    // 0x12907a4: r0 = Instance_BorderSide
    //     0x12907a4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x12907a8: ldr             x0, [x0, #0xe20]
    // 0x12907ac: StoreField: r3->field_7 = r0
    //     0x12907ac: stur            w0, [x3, #7]
    // 0x12907b0: r1 = <Widget>
    //     0x12907b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12907b4: r2 = 20
    //     0x12907b4: movz            x2, #0x14
    // 0x12907b8: r0 = AllocateArray()
    //     0x12907b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12907bc: stur            x0, [fp, #-0x20]
    // 0x12907c0: r16 = Instance_SizedBox
    //     0x12907c0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x12907c4: ldr             x16, [x16, #0xd68]
    // 0x12907c8: StoreField: r0->field_f = r16
    //     0x12907c8: stur            w16, [x0, #0xf]
    // 0x12907cc: ldur            x2, [fp, #-0x18]
    // 0x12907d0: LoadField: r1 = r2->field_13
    //     0x12907d0: ldur            w1, [x2, #0x13]
    // 0x12907d4: DecompressPointer r1
    //     0x12907d4: add             x1, x1, HEAP, lsl #32
    // 0x12907d8: r0 = of()
    //     0x12907d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12907dc: LoadField: r1 = r0->field_87
    //     0x12907dc: ldur            w1, [x0, #0x87]
    // 0x12907e0: DecompressPointer r1
    //     0x12907e0: add             x1, x1, HEAP, lsl #32
    // 0x12907e4: LoadField: r0 = r1->field_7
    //     0x12907e4: ldur            w0, [x1, #7]
    // 0x12907e8: DecompressPointer r0
    //     0x12907e8: add             x0, x0, HEAP, lsl #32
    // 0x12907ec: r16 = 12.000000
    //     0x12907ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x12907f0: ldr             x16, [x16, #0x9e8]
    // 0x12907f4: str             x16, [SP]
    // 0x12907f8: mov             x1, x0
    // 0x12907fc: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x12907fc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1290800: ldr             x4, [x4, #0x798]
    // 0x1290804: r0 = copyWith()
    //     0x1290804: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290808: stur            x0, [fp, #-0x28]
    // 0x129080c: r0 = Text()
    //     0x129080c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1290810: mov             x1, x0
    // 0x1290814: r0 = "Get a Free Gift with this product!"
    //     0x1290814: add             x0, PP, #0x52, lsl #12  ; [pp+0x52910] "Get a Free Gift with this product!"
    //     0x1290818: ldr             x0, [x0, #0x910]
    // 0x129081c: StoreField: r1->field_b = r0
    //     0x129081c: stur            w0, [x1, #0xb]
    // 0x1290820: ldur            x0, [fp, #-0x28]
    // 0x1290824: StoreField: r1->field_13 = r0
    //     0x1290824: stur            w0, [x1, #0x13]
    // 0x1290828: mov             x0, x1
    // 0x129082c: ldur            x1, [fp, #-0x20]
    // 0x1290830: ArrayStore: r1[1] = r0  ; List_4
    //     0x1290830: add             x25, x1, #0x13
    //     0x1290834: str             w0, [x25]
    //     0x1290838: tbz             w0, #0, #0x1290854
    //     0x129083c: ldurb           w16, [x1, #-1]
    //     0x1290840: ldurb           w17, [x0, #-1]
    //     0x1290844: and             x16, x17, x16, lsr #2
    //     0x1290848: tst             x16, HEAP, lsr #32
    //     0x129084c: b.eq            #0x1290854
    //     0x1290850: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290854: ldur            x0, [fp, #-0x20]
    // 0x1290858: r16 = Instance_SizedBox
    //     0x1290858: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x129085c: ldr             x16, [x16, #0x578]
    // 0x1290860: ArrayStore: r0[0] = r16  ; List_4
    //     0x1290860: stur            w16, [x0, #0x17]
    // 0x1290864: ldur            x1, [fp, #-8]
    // 0x1290868: LoadField: r2 = r1->field_b
    //     0x1290868: ldur            w2, [x1, #0xb]
    // 0x129086c: DecompressPointer r2
    //     0x129086c: add             x2, x2, HEAP, lsl #32
    // 0x1290870: stur            x2, [fp, #-0x28]
    // 0x1290874: cmp             w2, NULL
    // 0x1290878: b.ne            #0x1290884
    // 0x129087c: r1 = Null
    //     0x129087c: mov             x1, NULL
    // 0x1290880: b               #0x129088c
    // 0x1290884: LoadField: r1 = r2->field_f
    //     0x1290884: ldur            w1, [x2, #0xf]
    // 0x1290888: DecompressPointer r1
    //     0x1290888: add             x1, x1, HEAP, lsl #32
    // 0x129088c: cmp             w1, NULL
    // 0x1290890: b.ne            #0x129089c
    // 0x1290894: r4 = ""
    //     0x1290894: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1290898: b               #0x12908a0
    // 0x129089c: mov             x4, x1
    // 0x12908a0: ldur            x3, [fp, #-0x18]
    // 0x12908a4: stur            x4, [fp, #-8]
    // 0x12908a8: LoadField: r1 = r3->field_13
    //     0x12908a8: ldur            w1, [x3, #0x13]
    // 0x12908ac: DecompressPointer r1
    //     0x12908ac: add             x1, x1, HEAP, lsl #32
    // 0x12908b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12908b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12908b4: r0 = _of()
    //     0x12908b4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x12908b8: LoadField: r1 = r0->field_7
    //     0x12908b8: ldur            w1, [x0, #7]
    // 0x12908bc: DecompressPointer r1
    //     0x12908bc: add             x1, x1, HEAP, lsl #32
    // 0x12908c0: LoadField: d0 = r1->field_f
    //     0x12908c0: ldur            d0, [x1, #0xf]
    // 0x12908c4: d1 = 0.200000
    //     0x12908c4: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x12908c8: fmul            d2, d0, d1
    // 0x12908cc: ldur            x2, [fp, #-0x18]
    // 0x12908d0: stur            d2, [fp, #-0x58]
    // 0x12908d4: LoadField: r1 = r2->field_13
    //     0x12908d4: ldur            w1, [x2, #0x13]
    // 0x12908d8: DecompressPointer r1
    //     0x12908d8: add             x1, x1, HEAP, lsl #32
    // 0x12908dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12908dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12908e0: r0 = _of()
    //     0x12908e0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x12908e4: LoadField: r1 = r0->field_7
    //     0x12908e4: ldur            w1, [x0, #7]
    // 0x12908e8: DecompressPointer r1
    //     0x12908e8: add             x1, x1, HEAP, lsl #32
    // 0x12908ec: LoadField: d0 = r1->field_7
    //     0x12908ec: ldur            d0, [x1, #7]
    // 0x12908f0: d1 = 0.770000
    //     0x12908f0: add             x17, PP, #0x52, lsl #12  ; [pp+0x52918] IMM: double(0.77) from 0x3fe8a3d70a3d70a4
    //     0x12908f4: ldr             d1, [x17, #0x918]
    // 0x12908f8: fmul            d2, d0, d1
    // 0x12908fc: ldur            d0, [fp, #-0x58]
    // 0x1290900: r0 = inline_Allocate_Double()
    //     0x1290900: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1290904: add             x0, x0, #0x10
    //     0x1290908: cmp             x1, x0
    //     0x129090c: b.ls            #0x1291240
    //     0x1290910: str             x0, [THR, #0x50]  ; THR::top
    //     0x1290914: sub             x0, x0, #0xf
    //     0x1290918: movz            x1, #0xe15c
    //     0x129091c: movk            x1, #0x3, lsl #16
    //     0x1290920: stur            x1, [x0, #-1]
    // 0x1290924: StoreField: r0->field_7 = d0
    //     0x1290924: stur            d0, [x0, #7]
    // 0x1290928: stur            x0, [fp, #-0x38]
    // 0x129092c: r1 = inline_Allocate_Double()
    //     0x129092c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x1290930: add             x1, x1, #0x10
    //     0x1290934: cmp             x2, x1
    //     0x1290938: b.ls            #0x1291250
    //     0x129093c: str             x1, [THR, #0x50]  ; THR::top
    //     0x1290940: sub             x1, x1, #0xf
    //     0x1290944: movz            x2, #0xe15c
    //     0x1290948: movk            x2, #0x3, lsl #16
    //     0x129094c: stur            x2, [x1, #-1]
    // 0x1290950: StoreField: r1->field_7 = d2
    //     0x1290950: stur            d2, [x1, #7]
    // 0x1290954: stur            x1, [fp, #-0x30]
    // 0x1290958: r0 = CachedNetworkImage()
    //     0x1290958: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x129095c: stur            x0, [fp, #-0x40]
    // 0x1290960: r16 = Instance_BoxFit
    //     0x1290960: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x1290964: ldr             x16, [x16, #0x118]
    // 0x1290968: ldur            lr, [fp, #-0x38]
    // 0x129096c: stp             lr, x16, [SP, #8]
    // 0x1290970: ldur            x16, [fp, #-0x30]
    // 0x1290974: str             x16, [SP]
    // 0x1290978: mov             x1, x0
    // 0x129097c: ldur            x2, [fp, #-8]
    // 0x1290980: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x1290980: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x1290984: ldr             x4, [x4, #0x8e0]
    // 0x1290988: r0 = CachedNetworkImage()
    //     0x1290988: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x129098c: ldur            x1, [fp, #-0x20]
    // 0x1290990: ldur            x0, [fp, #-0x40]
    // 0x1290994: ArrayStore: r1[3] = r0  ; List_4
    //     0x1290994: add             x25, x1, #0x1b
    //     0x1290998: str             w0, [x25]
    //     0x129099c: tbz             w0, #0, #0x12909b8
    //     0x12909a0: ldurb           w16, [x1, #-1]
    //     0x12909a4: ldurb           w17, [x0, #-1]
    //     0x12909a8: and             x16, x17, x16, lsr #2
    //     0x12909ac: tst             x16, HEAP, lsr #32
    //     0x12909b0: b.eq            #0x12909b8
    //     0x12909b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12909b8: ldur            x0, [fp, #-0x20]
    // 0x12909bc: r16 = Instance_SizedBox
    //     0x12909bc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x12909c0: ldr             x16, [x16, #0x578]
    // 0x12909c4: StoreField: r0->field_1f = r16
    //     0x12909c4: stur            w16, [x0, #0x1f]
    // 0x12909c8: ldur            x2, [fp, #-0x28]
    // 0x12909cc: cmp             w2, NULL
    // 0x12909d0: b.ne            #0x12909dc
    // 0x12909d4: r1 = Null
    //     0x12909d4: mov             x1, NULL
    // 0x12909d8: b               #0x12909e4
    // 0x12909dc: LoadField: r1 = r2->field_b
    //     0x12909dc: ldur            w1, [x2, #0xb]
    // 0x12909e0: DecompressPointer r1
    //     0x12909e0: add             x1, x1, HEAP, lsl #32
    // 0x12909e4: cmp             w1, NULL
    // 0x12909e8: b.ne            #0x12909f4
    // 0x12909ec: r4 = ""
    //     0x12909ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12909f0: b               #0x12909f8
    // 0x12909f4: mov             x4, x1
    // 0x12909f8: ldur            x3, [fp, #-0x18]
    // 0x12909fc: stur            x4, [fp, #-8]
    // 0x1290a00: LoadField: r1 = r3->field_13
    //     0x1290a00: ldur            w1, [x3, #0x13]
    // 0x1290a04: DecompressPointer r1
    //     0x1290a04: add             x1, x1, HEAP, lsl #32
    // 0x1290a08: r0 = of()
    //     0x1290a08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1290a0c: LoadField: r1 = r0->field_87
    //     0x1290a0c: ldur            w1, [x0, #0x87]
    // 0x1290a10: DecompressPointer r1
    //     0x1290a10: add             x1, x1, HEAP, lsl #32
    // 0x1290a14: LoadField: r0 = r1->field_7
    //     0x1290a14: ldur            w0, [x1, #7]
    // 0x1290a18: DecompressPointer r0
    //     0x1290a18: add             x0, x0, HEAP, lsl #32
    // 0x1290a1c: r16 = 12.000000
    //     0x1290a1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1290a20: ldr             x16, [x16, #0x9e8]
    // 0x1290a24: str             x16, [SP]
    // 0x1290a28: mov             x1, x0
    // 0x1290a2c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1290a2c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1290a30: ldr             x4, [x4, #0x798]
    // 0x1290a34: r0 = copyWith()
    //     0x1290a34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290a38: stur            x0, [fp, #-0x30]
    // 0x1290a3c: r0 = Text()
    //     0x1290a3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1290a40: mov             x1, x0
    // 0x1290a44: ldur            x0, [fp, #-8]
    // 0x1290a48: StoreField: r1->field_b = r0
    //     0x1290a48: stur            w0, [x1, #0xb]
    // 0x1290a4c: ldur            x0, [fp, #-0x30]
    // 0x1290a50: StoreField: r1->field_13 = r0
    //     0x1290a50: stur            w0, [x1, #0x13]
    // 0x1290a54: mov             x0, x1
    // 0x1290a58: ldur            x1, [fp, #-0x20]
    // 0x1290a5c: ArrayStore: r1[5] = r0  ; List_4
    //     0x1290a5c: add             x25, x1, #0x23
    //     0x1290a60: str             w0, [x25]
    //     0x1290a64: tbz             w0, #0, #0x1290a80
    //     0x1290a68: ldurb           w16, [x1, #-1]
    //     0x1290a6c: ldurb           w17, [x0, #-1]
    //     0x1290a70: and             x16, x17, x16, lsr #2
    //     0x1290a74: tst             x16, HEAP, lsr #32
    //     0x1290a78: b.eq            #0x1290a80
    //     0x1290a7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290a80: ldur            x0, [fp, #-0x20]
    // 0x1290a84: r16 = Instance_SizedBox
    //     0x1290a84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x1290a88: ldr             x16, [x16, #0x8f0]
    // 0x1290a8c: StoreField: r0->field_27 = r16
    //     0x1290a8c: stur            w16, [x0, #0x27]
    // 0x1290a90: ldur            x2, [fp, #-0x18]
    // 0x1290a94: LoadField: r1 = r2->field_13
    //     0x1290a94: ldur            w1, [x2, #0x13]
    // 0x1290a98: DecompressPointer r1
    //     0x1290a98: add             x1, x1, HEAP, lsl #32
    // 0x1290a9c: r0 = of()
    //     0x1290a9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1290aa0: LoadField: r1 = r0->field_87
    //     0x1290aa0: ldur            w1, [x0, #0x87]
    // 0x1290aa4: DecompressPointer r1
    //     0x1290aa4: add             x1, x1, HEAP, lsl #32
    // 0x1290aa8: LoadField: r0 = r1->field_2b
    //     0x1290aa8: ldur            w0, [x1, #0x2b]
    // 0x1290aac: DecompressPointer r0
    //     0x1290aac: add             x0, x0, HEAP, lsl #32
    // 0x1290ab0: r16 = 12.000000
    //     0x1290ab0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1290ab4: ldr             x16, [x16, #0x9e8]
    // 0x1290ab8: r30 = Instance_Color
    //     0x1290ab8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x1290abc: ldr             lr, [lr, #0x858]
    // 0x1290ac0: stp             lr, x16, [SP]
    // 0x1290ac4: mov             x1, x0
    // 0x1290ac8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1290ac8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1290acc: ldr             x4, [x4, #0xaa0]
    // 0x1290ad0: r0 = copyWith()
    //     0x1290ad0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290ad4: stur            x0, [fp, #-8]
    // 0x1290ad8: r0 = Text()
    //     0x1290ad8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1290adc: mov             x2, x0
    // 0x1290ae0: r0 = "Free"
    //     0x1290ae0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x1290ae4: ldr             x0, [x0, #0x668]
    // 0x1290ae8: stur            x2, [fp, #-0x30]
    // 0x1290aec: StoreField: r2->field_b = r0
    //     0x1290aec: stur            w0, [x2, #0xb]
    // 0x1290af0: ldur            x0, [fp, #-8]
    // 0x1290af4: StoreField: r2->field_13 = r0
    //     0x1290af4: stur            w0, [x2, #0x13]
    // 0x1290af8: ldur            x0, [fp, #-0x28]
    // 0x1290afc: cmp             w0, NULL
    // 0x1290b00: b.ne            #0x1290b0c
    // 0x1290b04: r1 = Null
    //     0x1290b04: mov             x1, NULL
    // 0x1290b08: b               #0x1290b14
    // 0x1290b0c: LoadField: r1 = r0->field_13
    //     0x1290b0c: ldur            w1, [x0, #0x13]
    // 0x1290b10: DecompressPointer r1
    //     0x1290b10: add             x1, x1, HEAP, lsl #32
    // 0x1290b14: cmp             w1, NULL
    // 0x1290b18: b.ne            #0x1290b24
    // 0x1290b1c: r5 = ""
    //     0x1290b1c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1290b20: b               #0x1290b28
    // 0x1290b24: mov             x5, x1
    // 0x1290b28: ldur            x4, [fp, #-0x18]
    // 0x1290b2c: ldur            x3, [fp, #-0x20]
    // 0x1290b30: stur            x5, [fp, #-8]
    // 0x1290b34: LoadField: r1 = r4->field_13
    //     0x1290b34: ldur            w1, [x4, #0x13]
    // 0x1290b38: DecompressPointer r1
    //     0x1290b38: add             x1, x1, HEAP, lsl #32
    // 0x1290b3c: r0 = of()
    //     0x1290b3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1290b40: LoadField: r1 = r0->field_87
    //     0x1290b40: ldur            w1, [x0, #0x87]
    // 0x1290b44: DecompressPointer r1
    //     0x1290b44: add             x1, x1, HEAP, lsl #32
    // 0x1290b48: LoadField: r0 = r1->field_2b
    //     0x1290b48: ldur            w0, [x1, #0x2b]
    // 0x1290b4c: DecompressPointer r0
    //     0x1290b4c: add             x0, x0, HEAP, lsl #32
    // 0x1290b50: r16 = 12.000000
    //     0x1290b50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1290b54: ldr             x16, [x16, #0x9e8]
    // 0x1290b58: r30 = Instance_TextDecoration
    //     0x1290b58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x1290b5c: ldr             lr, [lr, #0xe30]
    // 0x1290b60: stp             lr, x16, [SP]
    // 0x1290b64: mov             x1, x0
    // 0x1290b68: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x1290b68: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x1290b6c: ldr             x4, [x4, #0x698]
    // 0x1290b70: r0 = copyWith()
    //     0x1290b70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1290b74: stur            x0, [fp, #-0x38]
    // 0x1290b78: r0 = Text()
    //     0x1290b78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1290b7c: mov             x3, x0
    // 0x1290b80: ldur            x0, [fp, #-8]
    // 0x1290b84: stur            x3, [fp, #-0x40]
    // 0x1290b88: StoreField: r3->field_b = r0
    //     0x1290b88: stur            w0, [x3, #0xb]
    // 0x1290b8c: ldur            x0, [fp, #-0x38]
    // 0x1290b90: StoreField: r3->field_13 = r0
    //     0x1290b90: stur            w0, [x3, #0x13]
    // 0x1290b94: r1 = Null
    //     0x1290b94: mov             x1, NULL
    // 0x1290b98: r2 = 6
    //     0x1290b98: movz            x2, #0x6
    // 0x1290b9c: r0 = AllocateArray()
    //     0x1290b9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1290ba0: mov             x2, x0
    // 0x1290ba4: ldur            x0, [fp, #-0x30]
    // 0x1290ba8: stur            x2, [fp, #-8]
    // 0x1290bac: StoreField: r2->field_f = r0
    //     0x1290bac: stur            w0, [x2, #0xf]
    // 0x1290bb0: r16 = Instance_SizedBox
    //     0x1290bb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x1290bb4: ldr             x16, [x16, #0xa50]
    // 0x1290bb8: StoreField: r2->field_13 = r16
    //     0x1290bb8: stur            w16, [x2, #0x13]
    // 0x1290bbc: ldur            x0, [fp, #-0x40]
    // 0x1290bc0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1290bc0: stur            w0, [x2, #0x17]
    // 0x1290bc4: r1 = <Widget>
    //     0x1290bc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1290bc8: r0 = AllocateGrowableArray()
    //     0x1290bc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1290bcc: mov             x1, x0
    // 0x1290bd0: ldur            x0, [fp, #-8]
    // 0x1290bd4: stur            x1, [fp, #-0x30]
    // 0x1290bd8: StoreField: r1->field_f = r0
    //     0x1290bd8: stur            w0, [x1, #0xf]
    // 0x1290bdc: r2 = 6
    //     0x1290bdc: movz            x2, #0x6
    // 0x1290be0: StoreField: r1->field_b = r2
    //     0x1290be0: stur            w2, [x1, #0xb]
    // 0x1290be4: r0 = Row()
    //     0x1290be4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1290be8: mov             x1, x0
    // 0x1290bec: r0 = Instance_Axis
    //     0x1290bec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1290bf0: StoreField: r1->field_f = r0
    //     0x1290bf0: stur            w0, [x1, #0xf]
    // 0x1290bf4: r2 = Instance_MainAxisAlignment
    //     0x1290bf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1290bf8: ldr             x2, [x2, #0xa08]
    // 0x1290bfc: StoreField: r1->field_13 = r2
    //     0x1290bfc: stur            w2, [x1, #0x13]
    // 0x1290c00: r0 = Instance_MainAxisSize
    //     0x1290c00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1290c04: ldr             x0, [x0, #0xa10]
    // 0x1290c08: ArrayStore: r1[0] = r0  ; List_4
    //     0x1290c08: stur            w0, [x1, #0x17]
    // 0x1290c0c: r0 = Instance_CrossAxisAlignment
    //     0x1290c0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1290c10: ldr             x0, [x0, #0xa18]
    // 0x1290c14: StoreField: r1->field_1b = r0
    //     0x1290c14: stur            w0, [x1, #0x1b]
    // 0x1290c18: r3 = Instance_VerticalDirection
    //     0x1290c18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1290c1c: ldr             x3, [x3, #0xa20]
    // 0x1290c20: StoreField: r1->field_23 = r3
    //     0x1290c20: stur            w3, [x1, #0x23]
    // 0x1290c24: r4 = Instance_Clip
    //     0x1290c24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1290c28: ldr             x4, [x4, #0x38]
    // 0x1290c2c: StoreField: r1->field_2b = r4
    //     0x1290c2c: stur            w4, [x1, #0x2b]
    // 0x1290c30: StoreField: r1->field_2f = rZR
    //     0x1290c30: stur            xzr, [x1, #0x2f]
    // 0x1290c34: ldur            x0, [fp, #-0x30]
    // 0x1290c38: StoreField: r1->field_b = r0
    //     0x1290c38: stur            w0, [x1, #0xb]
    // 0x1290c3c: mov             x0, x1
    // 0x1290c40: ldur            x1, [fp, #-0x20]
    // 0x1290c44: ArrayStore: r1[7] = r0  ; List_4
    //     0x1290c44: add             x25, x1, #0x2b
    //     0x1290c48: str             w0, [x25]
    //     0x1290c4c: tbz             w0, #0, #0x1290c68
    //     0x1290c50: ldurb           w16, [x1, #-1]
    //     0x1290c54: ldurb           w17, [x0, #-1]
    //     0x1290c58: and             x16, x17, x16, lsr #2
    //     0x1290c5c: tst             x16, HEAP, lsr #32
    //     0x1290c60: b.eq            #0x1290c68
    //     0x1290c64: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290c68: ldur            x0, [fp, #-0x20]
    // 0x1290c6c: r16 = Instance_SizedBox
    //     0x1290c6c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x1290c70: ldr             x16, [x16, #0x8f0]
    // 0x1290c74: StoreField: r0->field_2f = r16
    //     0x1290c74: stur            w16, [x0, #0x2f]
    // 0x1290c78: ldur            x5, [fp, #-0x18]
    // 0x1290c7c: LoadField: r1 = r5->field_f
    //     0x1290c7c: ldur            w1, [x5, #0xf]
    // 0x1290c80: DecompressPointer r1
    //     0x1290c80: add             x1, x1, HEAP, lsl #32
    // 0x1290c84: LoadField: r6 = r1->field_f
    //     0x1290c84: ldur            x6, [x1, #0xf]
    // 0x1290c88: cbnz            x6, #0x1290c9c
    // 0x1290c8c: d0 = 0.000000
    //     0x1290c8c: eor             v0.16b, v0.16b, v0.16b
    // 0x1290c90: d1 = 0.090000
    //     0x1290c90: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1290c94: ldr             d1, [x17, #0x920]
    // 0x1290c98: b               #0x1290d0c
    // 0x1290c9c: cmp             x6, #1
    // 0x1290ca0: b.ne            #0x1290cd8
    // 0x1290ca4: LoadField: r1 = r5->field_13
    //     0x1290ca4: ldur            w1, [x5, #0x13]
    // 0x1290ca8: DecompressPointer r1
    //     0x1290ca8: add             x1, x1, HEAP, lsl #32
    // 0x1290cac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290cac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1290cb0: r0 = _of()
    //     0x1290cb0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290cb4: LoadField: r1 = r0->field_7
    //     0x1290cb4: ldur            w1, [x0, #7]
    // 0x1290cb8: DecompressPointer r1
    //     0x1290cb8: add             x1, x1, HEAP, lsl #32
    // 0x1290cbc: LoadField: d0 = r1->field_f
    //     0x1290cbc: ldur            d0, [x1, #0xf]
    // 0x1290cc0: d1 = 0.040000
    //     0x1290cc0: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x1290cc4: fmul            d2, d0, d1
    // 0x1290cc8: mov             v0.16b, v2.16b
    // 0x1290ccc: d1 = 0.090000
    //     0x1290ccc: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1290cd0: ldr             d1, [x17, #0x920]
    // 0x1290cd4: b               #0x1290d0c
    // 0x1290cd8: mov             x2, x5
    // 0x1290cdc: d1 = 0.040000
    //     0x1290cdc: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x1290ce0: LoadField: r1 = r2->field_13
    //     0x1290ce0: ldur            w1, [x2, #0x13]
    // 0x1290ce4: DecompressPointer r1
    //     0x1290ce4: add             x1, x1, HEAP, lsl #32
    // 0x1290ce8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290ce8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1290cec: r0 = _of()
    //     0x1290cec: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290cf0: LoadField: r1 = r0->field_7
    //     0x1290cf0: ldur            w1, [x0, #7]
    // 0x1290cf4: DecompressPointer r1
    //     0x1290cf4: add             x1, x1, HEAP, lsl #32
    // 0x1290cf8: LoadField: d0 = r1->field_f
    //     0x1290cf8: ldur            d0, [x1, #0xf]
    // 0x1290cfc: d1 = 0.090000
    //     0x1290cfc: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1290d00: ldr             d1, [x17, #0x920]
    // 0x1290d04: fmul            d2, d0, d1
    // 0x1290d08: mov             v0.16b, v2.16b
    // 0x1290d0c: ldur            x0, [fp, #-0x28]
    // 0x1290d10: stur            d0, [fp, #-0x58]
    // 0x1290d14: cmp             w0, NULL
    // 0x1290d18: b.ne            #0x1290d24
    // 0x1290d1c: r0 = Null
    //     0x1290d1c: mov             x0, NULL
    // 0x1290d20: b               #0x1290d30
    // 0x1290d24: LoadField: r1 = r0->field_7
    //     0x1290d24: ldur            w1, [x0, #7]
    // 0x1290d28: DecompressPointer r1
    //     0x1290d28: add             x1, x1, HEAP, lsl #32
    // 0x1290d2c: LoadField: r0 = r1->field_b
    //     0x1290d2c: ldur            w0, [x1, #0xb]
    // 0x1290d30: cmp             w0, NULL
    // 0x1290d34: b.ne            #0x1290d40
    // 0x1290d38: r1 = 0
    //     0x1290d38: movz            x1, #0
    // 0x1290d3c: b               #0x1290d44
    // 0x1290d40: r1 = LoadInt32Instr(r0)
    //     0x1290d40: sbfx            x1, x0, #1, #0x1f
    // 0x1290d44: ldur            x0, [fp, #-0x18]
    // 0x1290d48: ldur            d2, [fp, #-0x50]
    // 0x1290d4c: ldur            x4, [fp, #-0x10]
    // 0x1290d50: ldur            x3, [fp, #-0x20]
    // 0x1290d54: lsl             x5, x1, #1
    // 0x1290d58: mov             x2, x0
    // 0x1290d5c: stur            x5, [fp, #-8]
    // 0x1290d60: r1 = Function '<anonymous closure>':.
    //     0x1290d60: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e60] AnonymousClosure: (0x1286804), in [package:customer_app/app/presentation/views/line/bag/bag_item_view_widget.dart] _FreeGiftDialog::build (0x12944b0)
    //     0x1290d64: ldr             x1, [x1, #0xe60]
    // 0x1290d68: r0 = AllocateClosure()
    //     0x1290d68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1290d6c: stur            x0, [fp, #-0x28]
    // 0x1290d70: r0 = ListView()
    //     0x1290d70: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1290d74: stur            x0, [fp, #-0x30]
    // 0x1290d78: r16 = true
    //     0x1290d78: add             x16, NULL, #0x20  ; true
    // 0x1290d7c: r30 = Instance_NeverScrollableScrollPhysics
    //     0x1290d7c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x1290d80: ldr             lr, [lr, #0x1c8]
    // 0x1290d84: stp             lr, x16, [SP]
    // 0x1290d88: mov             x1, x0
    // 0x1290d8c: ldur            x2, [fp, #-0x28]
    // 0x1290d90: ldur            x3, [fp, #-8]
    // 0x1290d94: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x1290d94: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1290d98: ldr             x4, [x4, #8]
    // 0x1290d9c: r0 = ListView.builder()
    //     0x1290d9c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1290da0: ldur            d0, [fp, #-0x58]
    // 0x1290da4: r0 = inline_Allocate_Double()
    //     0x1290da4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1290da8: add             x0, x0, #0x10
    //     0x1290dac: cmp             x1, x0
    //     0x1290db0: b.ls            #0x129126c
    //     0x1290db4: str             x0, [THR, #0x50]  ; THR::top
    //     0x1290db8: sub             x0, x0, #0xf
    //     0x1290dbc: movz            x1, #0xe15c
    //     0x1290dc0: movk            x1, #0x3, lsl #16
    //     0x1290dc4: stur            x1, [x0, #-1]
    // 0x1290dc8: StoreField: r0->field_7 = d0
    //     0x1290dc8: stur            d0, [x0, #7]
    // 0x1290dcc: stur            x0, [fp, #-8]
    // 0x1290dd0: r0 = SizedBox()
    //     0x1290dd0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1290dd4: mov             x1, x0
    // 0x1290dd8: ldur            x0, [fp, #-8]
    // 0x1290ddc: StoreField: r1->field_13 = r0
    //     0x1290ddc: stur            w0, [x1, #0x13]
    // 0x1290de0: ldur            x0, [fp, #-0x30]
    // 0x1290de4: StoreField: r1->field_b = r0
    //     0x1290de4: stur            w0, [x1, #0xb]
    // 0x1290de8: mov             x0, x1
    // 0x1290dec: ldur            x1, [fp, #-0x20]
    // 0x1290df0: ArrayStore: r1[9] = r0  ; List_4
    //     0x1290df0: add             x25, x1, #0x33
    //     0x1290df4: str             w0, [x25]
    //     0x1290df8: tbz             w0, #0, #0x1290e14
    //     0x1290dfc: ldurb           w16, [x1, #-1]
    //     0x1290e00: ldurb           w17, [x0, #-1]
    //     0x1290e04: and             x16, x17, x16, lsr #2
    //     0x1290e08: tst             x16, HEAP, lsr #32
    //     0x1290e0c: b.eq            #0x1290e14
    //     0x1290e10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1290e14: r1 = <Widget>
    //     0x1290e14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1290e18: r0 = AllocateGrowableArray()
    //     0x1290e18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1290e1c: mov             x1, x0
    // 0x1290e20: ldur            x0, [fp, #-0x20]
    // 0x1290e24: stur            x1, [fp, #-8]
    // 0x1290e28: StoreField: r1->field_f = r0
    //     0x1290e28: stur            w0, [x1, #0xf]
    // 0x1290e2c: r0 = 20
    //     0x1290e2c: movz            x0, #0x14
    // 0x1290e30: StoreField: r1->field_b = r0
    //     0x1290e30: stur            w0, [x1, #0xb]
    // 0x1290e34: r0 = Column()
    //     0x1290e34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1290e38: mov             x1, x0
    // 0x1290e3c: r0 = Instance_Axis
    //     0x1290e3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1290e40: stur            x1, [fp, #-0x20]
    // 0x1290e44: StoreField: r1->field_f = r0
    //     0x1290e44: stur            w0, [x1, #0xf]
    // 0x1290e48: r0 = Instance_MainAxisAlignment
    //     0x1290e48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1290e4c: ldr             x0, [x0, #0xa08]
    // 0x1290e50: StoreField: r1->field_13 = r0
    //     0x1290e50: stur            w0, [x1, #0x13]
    // 0x1290e54: r0 = Instance_MainAxisSize
    //     0x1290e54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1290e58: ldr             x0, [x0, #0xdd0]
    // 0x1290e5c: ArrayStore: r1[0] = r0  ; List_4
    //     0x1290e5c: stur            w0, [x1, #0x17]
    // 0x1290e60: r0 = Instance_CrossAxisAlignment
    //     0x1290e60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1290e64: ldr             x0, [x0, #0x890]
    // 0x1290e68: StoreField: r1->field_1b = r0
    //     0x1290e68: stur            w0, [x1, #0x1b]
    // 0x1290e6c: r0 = Instance_VerticalDirection
    //     0x1290e6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1290e70: ldr             x0, [x0, #0xa20]
    // 0x1290e74: StoreField: r1->field_23 = r0
    //     0x1290e74: stur            w0, [x1, #0x23]
    // 0x1290e78: r0 = Instance_Clip
    //     0x1290e78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1290e7c: ldr             x0, [x0, #0x38]
    // 0x1290e80: StoreField: r1->field_2b = r0
    //     0x1290e80: stur            w0, [x1, #0x2b]
    // 0x1290e84: StoreField: r1->field_2f = rZR
    //     0x1290e84: stur            xzr, [x1, #0x2f]
    // 0x1290e88: ldur            x0, [fp, #-8]
    // 0x1290e8c: StoreField: r1->field_b = r0
    //     0x1290e8c: stur            w0, [x1, #0xb]
    // 0x1290e90: r0 = AlertDialog()
    //     0x1290e90: bl              #0x99db14  ; AllocateAlertDialogStub -> AlertDialog (size=0x50)
    // 0x1290e94: mov             x1, x0
    // 0x1290e98: ldur            x0, [fp, #-0x20]
    // 0x1290e9c: stur            x1, [fp, #-0x28]
    // 0x1290ea0: StoreField: r1->field_f = r0
    //     0x1290ea0: stur            w0, [x1, #0xf]
    // 0x1290ea4: ldur            x0, [fp, #-0x10]
    // 0x1290ea8: StoreField: r1->field_3f = r0
    //     0x1290ea8: stur            w0, [x1, #0x3f]
    // 0x1290eac: r0 = false
    //     0x1290eac: add             x0, NULL, #0x30  ; false
    // 0x1290eb0: StoreField: r1->field_4b = r0
    //     0x1290eb0: stur            w0, [x1, #0x4b]
    // 0x1290eb4: ldur            d0, [fp, #-0x50]
    // 0x1290eb8: r2 = inline_Allocate_Double()
    //     0x1290eb8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x1290ebc: add             x2, x2, #0x10
    //     0x1290ec0: cmp             x3, x2
    //     0x1290ec4: b.ls            #0x129127c
    //     0x1290ec8: str             x2, [THR, #0x50]  ; THR::top
    //     0x1290ecc: sub             x2, x2, #0xf
    //     0x1290ed0: movz            x3, #0xe15c
    //     0x1290ed4: movk            x3, #0x3, lsl #16
    //     0x1290ed8: stur            x3, [x2, #-1]
    // 0x1290edc: StoreField: r2->field_7 = d0
    //     0x1290edc: stur            d0, [x2, #7]
    // 0x1290ee0: stur            x2, [fp, #-8]
    // 0x1290ee4: r0 = SizedBox()
    //     0x1290ee4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1290ee8: mov             x1, x0
    // 0x1290eec: ldur            x0, [fp, #-8]
    // 0x1290ef0: stur            x1, [fp, #-0x10]
    // 0x1290ef4: StoreField: r1->field_f = r0
    //     0x1290ef4: stur            w0, [x1, #0xf]
    // 0x1290ef8: ldur            x0, [fp, #-0x28]
    // 0x1290efc: StoreField: r1->field_b = r0
    //     0x1290efc: stur            w0, [x1, #0xb]
    // 0x1290f00: r0 = Center()
    //     0x1290f00: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1290f04: mov             x2, x0
    // 0x1290f08: r0 = Instance_Alignment
    //     0x1290f08: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1290f0c: ldr             x0, [x0, #0xb10]
    // 0x1290f10: stur            x2, [fp, #-8]
    // 0x1290f14: StoreField: r2->field_f = r0
    //     0x1290f14: stur            w0, [x2, #0xf]
    // 0x1290f18: ldur            x0, [fp, #-0x10]
    // 0x1290f1c: StoreField: r2->field_b = r0
    //     0x1290f1c: stur            w0, [x2, #0xb]
    // 0x1290f20: ldur            x0, [fp, #-0x18]
    // 0x1290f24: LoadField: r1 = r0->field_f
    //     0x1290f24: ldur            w1, [x0, #0xf]
    // 0x1290f28: DecompressPointer r1
    //     0x1290f28: add             x1, x1, HEAP, lsl #32
    // 0x1290f2c: LoadField: r3 = r1->field_f
    //     0x1290f2c: ldur            x3, [x1, #0xf]
    // 0x1290f30: cmp             x3, #1
    // 0x1290f34: b.ne            #0x1290f6c
    // 0x1290f38: LoadField: r1 = r0->field_13
    //     0x1290f38: ldur            w1, [x0, #0x13]
    // 0x1290f3c: DecompressPointer r1
    //     0x1290f3c: add             x1, x1, HEAP, lsl #32
    // 0x1290f40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290f40: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1290f44: r0 = _of()
    //     0x1290f44: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290f48: LoadField: r1 = r0->field_7
    //     0x1290f48: ldur            w1, [x0, #7]
    // 0x1290f4c: DecompressPointer r1
    //     0x1290f4c: add             x1, x1, HEAP, lsl #32
    // 0x1290f50: LoadField: d0 = r1->field_f
    //     0x1290f50: ldur            d0, [x1, #0xf]
    // 0x1290f54: d1 = 0.040000
    //     0x1290f54: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x1290f58: fmul            d2, d0, d1
    // 0x1290f5c: mov             v0.16b, v2.16b
    // 0x1290f60: d1 = 0.090000
    //     0x1290f60: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1290f64: ldr             d1, [x17, #0x920]
    // 0x1290f68: b               #0x1290fa0
    // 0x1290f6c: mov             x2, x0
    // 0x1290f70: d1 = 0.040000
    //     0x1290f70: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x1290f74: LoadField: r1 = r2->field_13
    //     0x1290f74: ldur            w1, [x2, #0x13]
    // 0x1290f78: DecompressPointer r1
    //     0x1290f78: add             x1, x1, HEAP, lsl #32
    // 0x1290f7c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1290f7c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1290f80: r0 = _of()
    //     0x1290f80: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1290f84: LoadField: r1 = r0->field_7
    //     0x1290f84: ldur            w1, [x0, #7]
    // 0x1290f88: DecompressPointer r1
    //     0x1290f88: add             x1, x1, HEAP, lsl #32
    // 0x1290f8c: LoadField: d0 = r1->field_f
    //     0x1290f8c: ldur            d0, [x1, #0xf]
    // 0x1290f90: d1 = 0.090000
    //     0x1290f90: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1290f94: ldr             d1, [x17, #0x920]
    // 0x1290f98: fmul            d2, d0, d1
    // 0x1290f9c: mov             v0.16b, v2.16b
    // 0x1290fa0: ldur            x2, [fp, #-0x18]
    // 0x1290fa4: stur            d0, [fp, #-0x50]
    // 0x1290fa8: r0 = SvgPicture()
    //     0x1290fa8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1290fac: mov             x1, x0
    // 0x1290fb0: r2 = "assets/images/gift-icon-popup.svg"
    //     0x1290fb0: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0x1290fb4: ldr             x2, [x2, #0x8e8]
    // 0x1290fb8: stur            x0, [fp, #-0x10]
    // 0x1290fbc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1290fbc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1290fc0: r0 = SvgPicture.asset()
    //     0x1290fc0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1290fc4: ldur            d0, [fp, #-0x50]
    // 0x1290fc8: r0 = inline_Allocate_Double()
    //     0x1290fc8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1290fcc: add             x0, x0, #0x10
    //     0x1290fd0: cmp             x1, x0
    //     0x1290fd4: b.ls            #0x1291298
    //     0x1290fd8: str             x0, [THR, #0x50]  ; THR::top
    //     0x1290fdc: sub             x0, x0, #0xf
    //     0x1290fe0: movz            x1, #0xe15c
    //     0x1290fe4: movk            x1, #0x3, lsl #16
    //     0x1290fe8: stur            x1, [x0, #-1]
    // 0x1290fec: StoreField: r0->field_7 = d0
    //     0x1290fec: stur            d0, [x0, #7]
    // 0x1290ff0: stur            x0, [fp, #-0x20]
    // 0x1290ff4: r1 = <StackParentData>
    //     0x1290ff4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x1290ff8: ldr             x1, [x1, #0x8e0]
    // 0x1290ffc: r0 = Positioned()
    //     0x1290ffc: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x1291000: mov             x2, x0
    // 0x1291004: ldur            x0, [fp, #-0x20]
    // 0x1291008: stur            x2, [fp, #-0x28]
    // 0x129100c: ArrayStore: r2[0] = r0  ; List_4
    //     0x129100c: stur            w0, [x2, #0x17]
    // 0x1291010: ldur            x0, [fp, #-0x10]
    // 0x1291014: StoreField: r2->field_b = r0
    //     0x1291014: stur            w0, [x2, #0xb]
    // 0x1291018: ldur            x0, [fp, #-0x18]
    // 0x129101c: LoadField: r1 = r0->field_f
    //     0x129101c: ldur            w1, [x0, #0xf]
    // 0x1291020: DecompressPointer r1
    //     0x1291020: add             x1, x1, HEAP, lsl #32
    // 0x1291024: LoadField: r3 = r1->field_f
    //     0x1291024: ldur            x3, [x1, #0xf]
    // 0x1291028: cmp             x3, #1
    // 0x129102c: b.ne            #0x129105c
    // 0x1291030: LoadField: r1 = r0->field_13
    //     0x1291030: ldur            w1, [x0, #0x13]
    // 0x1291034: DecompressPointer r1
    //     0x1291034: add             x1, x1, HEAP, lsl #32
    // 0x1291038: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1291038: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x129103c: r0 = _of()
    //     0x129103c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1291040: LoadField: r1 = r0->field_7
    //     0x1291040: ldur            w1, [x0, #7]
    // 0x1291044: DecompressPointer r1
    //     0x1291044: add             x1, x1, HEAP, lsl #32
    // 0x1291048: LoadField: d0 = r1->field_f
    //     0x1291048: ldur            d0, [x1, #0xf]
    // 0x129104c: d1 = 0.040000
    //     0x129104c: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x1291050: fmul            d2, d0, d1
    // 0x1291054: mov             v1.16b, v2.16b
    // 0x1291058: b               #0x129108c
    // 0x129105c: mov             x2, x0
    // 0x1291060: LoadField: r1 = r2->field_13
    //     0x1291060: ldur            w1, [x2, #0x13]
    // 0x1291064: DecompressPointer r1
    //     0x1291064: add             x1, x1, HEAP, lsl #32
    // 0x1291068: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1291068: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x129106c: r0 = _of()
    //     0x129106c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1291070: LoadField: r1 = r0->field_7
    //     0x1291070: ldur            w1, [x0, #7]
    // 0x1291074: DecompressPointer r1
    //     0x1291074: add             x1, x1, HEAP, lsl #32
    // 0x1291078: LoadField: d0 = r1->field_f
    //     0x1291078: ldur            d0, [x1, #0xf]
    // 0x129107c: d1 = 0.090000
    //     0x129107c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x1291080: ldr             d1, [x17, #0x920]
    // 0x1291084: fmul            d2, d0, d1
    // 0x1291088: mov             v1.16b, v2.16b
    // 0x129108c: ldur            d0, [fp, #-0x48]
    // 0x1291090: ldur            x3, [fp, #-8]
    // 0x1291094: ldur            x0, [fp, #-0x28]
    // 0x1291098: ldur            x2, [fp, #-0x18]
    // 0x129109c: stur            d1, [fp, #-0x50]
    // 0x12910a0: r1 = Function '<anonymous closure>':.
    //     0x12910a0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e68] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0x12910a4: ldr             x1, [x1, #0xe68]
    // 0x12910a8: r0 = AllocateClosure()
    //     0x12910a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12910ac: stur            x0, [fp, #-0x10]
    // 0x12910b0: r0 = IconButton()
    //     0x12910b0: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0x12910b4: mov             x2, x0
    // 0x12910b8: ldur            x0, [fp, #-0x10]
    // 0x12910bc: stur            x2, [fp, #-0x18]
    // 0x12910c0: StoreField: r2->field_3b = r0
    //     0x12910c0: stur            w0, [x2, #0x3b]
    // 0x12910c4: r0 = false
    //     0x12910c4: add             x0, NULL, #0x30  ; false
    // 0x12910c8: StoreField: r2->field_4f = r0
    //     0x12910c8: stur            w0, [x2, #0x4f]
    // 0x12910cc: r0 = Instance_Icon
    //     0x12910cc: add             x0, PP, #0x52, lsl #12  ; [pp+0x528f8] Obj!Icon@d65d71
    //     0x12910d0: ldr             x0, [x0, #0x8f8]
    // 0x12910d4: StoreField: r2->field_1f = r0
    //     0x12910d4: stur            w0, [x2, #0x1f]
    // 0x12910d8: r0 = Instance__IconButtonVariant
    //     0x12910d8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0x12910dc: ldr             x0, [x0, #0x900]
    // 0x12910e0: StoreField: r2->field_6b = r0
    //     0x12910e0: stur            w0, [x2, #0x6b]
    // 0x12910e4: ldur            d0, [fp, #-0x50]
    // 0x12910e8: r0 = inline_Allocate_Double()
    //     0x12910e8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x12910ec: add             x0, x0, #0x10
    //     0x12910f0: cmp             x1, x0
    //     0x12910f4: b.ls            #0x12912a8
    //     0x12910f8: str             x0, [THR, #0x50]  ; THR::top
    //     0x12910fc: sub             x0, x0, #0xf
    //     0x1291100: movz            x1, #0xe15c
    //     0x1291104: movk            x1, #0x3, lsl #16
    //     0x1291108: stur            x1, [x0, #-1]
    // 0x129110c: StoreField: r0->field_7 = d0
    //     0x129110c: stur            d0, [x0, #7]
    // 0x1291110: stur            x0, [fp, #-0x10]
    // 0x1291114: r1 = <StackParentData>
    //     0x1291114: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x1291118: ldr             x1, [x1, #0x8e0]
    // 0x129111c: r0 = Positioned()
    //     0x129111c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x1291120: mov             x3, x0
    // 0x1291124: ldur            x0, [fp, #-0x10]
    // 0x1291128: stur            x3, [fp, #-0x20]
    // 0x129112c: ArrayStore: r3[0] = r0  ; List_4
    //     0x129112c: stur            w0, [x3, #0x17]
    // 0x1291130: r0 = 50.000000
    //     0x1291130: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x1291134: ldr             x0, [x0, #0xa90]
    // 0x1291138: StoreField: r3->field_1b = r0
    //     0x1291138: stur            w0, [x3, #0x1b]
    // 0x129113c: ldur            x0, [fp, #-0x18]
    // 0x1291140: StoreField: r3->field_b = r0
    //     0x1291140: stur            w0, [x3, #0xb]
    // 0x1291144: r1 = Null
    //     0x1291144: mov             x1, NULL
    // 0x1291148: r2 = 6
    //     0x1291148: movz            x2, #0x6
    // 0x129114c: r0 = AllocateArray()
    //     0x129114c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1291150: mov             x2, x0
    // 0x1291154: ldur            x0, [fp, #-8]
    // 0x1291158: stur            x2, [fp, #-0x10]
    // 0x129115c: StoreField: r2->field_f = r0
    //     0x129115c: stur            w0, [x2, #0xf]
    // 0x1291160: ldur            x0, [fp, #-0x28]
    // 0x1291164: StoreField: r2->field_13 = r0
    //     0x1291164: stur            w0, [x2, #0x13]
    // 0x1291168: ldur            x0, [fp, #-0x20]
    // 0x129116c: ArrayStore: r2[0] = r0  ; List_4
    //     0x129116c: stur            w0, [x2, #0x17]
    // 0x1291170: r1 = <Widget>
    //     0x1291170: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1291174: r0 = AllocateGrowableArray()
    //     0x1291174: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1291178: mov             x1, x0
    // 0x129117c: ldur            x0, [fp, #-0x10]
    // 0x1291180: stur            x1, [fp, #-8]
    // 0x1291184: StoreField: r1->field_f = r0
    //     0x1291184: stur            w0, [x1, #0xf]
    // 0x1291188: r0 = 6
    //     0x1291188: movz            x0, #0x6
    // 0x129118c: StoreField: r1->field_b = r0
    //     0x129118c: stur            w0, [x1, #0xb]
    // 0x1291190: r0 = Stack()
    //     0x1291190: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x1291194: mov             x1, x0
    // 0x1291198: r0 = Instance_Alignment
    //     0x1291198: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x129119c: ldr             x0, [x0, #0xce0]
    // 0x12911a0: stur            x1, [fp, #-0x10]
    // 0x12911a4: StoreField: r1->field_f = r0
    //     0x12911a4: stur            w0, [x1, #0xf]
    // 0x12911a8: r0 = Instance_StackFit
    //     0x12911a8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x12911ac: ldr             x0, [x0, #0xfa8]
    // 0x12911b0: ArrayStore: r1[0] = r0  ; List_4
    //     0x12911b0: stur            w0, [x1, #0x17]
    // 0x12911b4: r0 = Instance_Clip
    //     0x12911b4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x12911b8: ldr             x0, [x0, #0x7e0]
    // 0x12911bc: StoreField: r1->field_1b = r0
    //     0x12911bc: stur            w0, [x1, #0x1b]
    // 0x12911c0: ldur            x0, [fp, #-8]
    // 0x12911c4: StoreField: r1->field_b = r0
    //     0x12911c4: stur            w0, [x1, #0xb]
    // 0x12911c8: ldur            d0, [fp, #-0x48]
    // 0x12911cc: r0 = inline_Allocate_Double()
    //     0x12911cc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x12911d0: add             x0, x0, #0x10
    //     0x12911d4: cmp             x2, x0
    //     0x12911d8: b.ls            #0x12912c0
    //     0x12911dc: str             x0, [THR, #0x50]  ; THR::top
    //     0x12911e0: sub             x0, x0, #0xf
    //     0x12911e4: movz            x2, #0xe15c
    //     0x12911e8: movk            x2, #0x3, lsl #16
    //     0x12911ec: stur            x2, [x0, #-1]
    // 0x12911f0: StoreField: r0->field_7 = d0
    //     0x12911f0: stur            d0, [x0, #7]
    // 0x12911f4: stur            x0, [fp, #-8]
    // 0x12911f8: r0 = Container()
    //     0x12911f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12911fc: stur            x0, [fp, #-0x18]
    // 0x1291200: r16 = Instance_Color
    //     0x1291200: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1291204: ldr             x16, [x16, #0xf88]
    // 0x1291208: ldur            lr, [fp, #-8]
    // 0x129120c: stp             lr, x16, [SP, #8]
    // 0x1291210: ldur            x16, [fp, #-0x10]
    // 0x1291214: str             x16, [SP]
    // 0x1291218: mov             x1, x0
    // 0x129121c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, width, 0x2, null]
    //     0x129121c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52908] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "width", 0x2, Null]
    //     0x1291220: ldr             x4, [x4, #0x908]
    // 0x1291224: r0 = Container()
    //     0x1291224: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1291228: ldur            x0, [fp, #-0x18]
    // 0x129122c: LeaveFrame
    //     0x129122c: mov             SP, fp
    //     0x1291230: ldp             fp, lr, [SP], #0x10
    // 0x1291234: ret
    //     0x1291234: ret             
    // 0x1291238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1291238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129123c: b               #0x12906e8
    // 0x1291240: stp             q0, q2, [SP, #-0x20]!
    // 0x1291244: r0 = AllocateDouble()
    //     0x1291244: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1291248: ldp             q0, q2, [SP], #0x20
    // 0x129124c: b               #0x1290924
    // 0x1291250: SaveReg d2
    //     0x1291250: str             q2, [SP, #-0x10]!
    // 0x1291254: SaveReg r0
    //     0x1291254: str             x0, [SP, #-8]!
    // 0x1291258: r0 = AllocateDouble()
    //     0x1291258: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129125c: mov             x1, x0
    // 0x1291260: RestoreReg r0
    //     0x1291260: ldr             x0, [SP], #8
    // 0x1291264: RestoreReg d2
    //     0x1291264: ldr             q2, [SP], #0x10
    // 0x1291268: b               #0x1290950
    // 0x129126c: SaveReg d0
    //     0x129126c: str             q0, [SP, #-0x10]!
    // 0x1291270: r0 = AllocateDouble()
    //     0x1291270: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1291274: RestoreReg d0
    //     0x1291274: ldr             q0, [SP], #0x10
    // 0x1291278: b               #0x1290dc8
    // 0x129127c: SaveReg d0
    //     0x129127c: str             q0, [SP, #-0x10]!
    // 0x1291280: stp             x0, x1, [SP, #-0x10]!
    // 0x1291284: r0 = AllocateDouble()
    //     0x1291284: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1291288: mov             x2, x0
    // 0x129128c: ldp             x0, x1, [SP], #0x10
    // 0x1291290: RestoreReg d0
    //     0x1291290: ldr             q0, [SP], #0x10
    // 0x1291294: b               #0x1290edc
    // 0x1291298: SaveReg d0
    //     0x1291298: str             q0, [SP, #-0x10]!
    // 0x129129c: r0 = AllocateDouble()
    //     0x129129c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12912a0: RestoreReg d0
    //     0x12912a0: ldr             q0, [SP], #0x10
    // 0x12912a4: b               #0x1290fec
    // 0x12912a8: SaveReg d0
    //     0x12912a8: str             q0, [SP, #-0x10]!
    // 0x12912ac: SaveReg r2
    //     0x12912ac: str             x2, [SP, #-8]!
    // 0x12912b0: r0 = AllocateDouble()
    //     0x12912b0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12912b4: RestoreReg r2
    //     0x12912b4: ldr             x2, [SP], #8
    // 0x12912b8: RestoreReg d0
    //     0x12912b8: ldr             q0, [SP], #0x10
    // 0x12912bc: b               #0x129110c
    // 0x12912c0: SaveReg d0
    //     0x12912c0: str             q0, [SP, #-0x10]!
    // 0x12912c4: SaveReg r1
    //     0x12912c4: str             x1, [SP, #-8]!
    // 0x12912c8: r0 = AllocateDouble()
    //     0x12912c8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12912cc: RestoreReg r1
    //     0x12912cc: ldr             x1, [SP], #8
    // 0x12912d0: RestoreReg d0
    //     0x12912d0: ldr             q0, [SP], #0x10
    // 0x12912d4: b               #0x12911f0
  }
}
