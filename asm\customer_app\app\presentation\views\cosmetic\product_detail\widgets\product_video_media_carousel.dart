// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_video_media_carousel.dart

// class id: 1049319, size: 0x8
class :: {
}

// class id: 3398, size: 0x20, field offset: 0x14
class _ProductVideoMediaCarouselState extends State<dynamic> {

  late Future<void> _future; // offset: 0x1c
  late VideoPlayerController _controller; // offset: 0x14
  late ChewieController? _chewieController; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x93cc08, size: 0xf8
    // 0x93cc08: EnterFrame
    //     0x93cc08: stp             fp, lr, [SP, #-0x10]!
    //     0x93cc0c: mov             fp, SP
    // 0x93cc10: AllocStack(0x10)
    //     0x93cc10: sub             SP, SP, #0x10
    // 0x93cc14: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x93cc14: mov             x0, x1
    //     0x93cc18: stur            x1, [fp, #-8]
    // 0x93cc1c: CheckStackOverflow
    //     0x93cc1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cc20: cmp             SP, x16
    //     0x93cc24: b.ls            #0x93ccf4
    // 0x93cc28: LoadField: r1 = r0->field_b
    //     0x93cc28: ldur            w1, [x0, #0xb]
    // 0x93cc2c: DecompressPointer r1
    //     0x93cc2c: add             x1, x1, HEAP, lsl #32
    // 0x93cc30: cmp             w1, NULL
    // 0x93cc34: b.eq            #0x93ccfc
    // 0x93cc38: LoadField: r2 = r1->field_b
    //     0x93cc38: ldur            w2, [x1, #0xb]
    // 0x93cc3c: DecompressPointer r2
    //     0x93cc3c: add             x2, x2, HEAP, lsl #32
    // 0x93cc40: LoadField: r1 = r2->field_2f
    //     0x93cc40: ldur            w1, [x2, #0x2f]
    // 0x93cc44: DecompressPointer r1
    //     0x93cc44: add             x1, x1, HEAP, lsl #32
    // 0x93cc48: cmp             w1, NULL
    // 0x93cc4c: b.ne            #0x93cc58
    // 0x93cc50: r1 = Null
    //     0x93cc50: mov             x1, NULL
    // 0x93cc54: b               #0x93cc64
    // 0x93cc58: LoadField: r2 = r1->field_b
    //     0x93cc58: ldur            w2, [x1, #0xb]
    // 0x93cc5c: DecompressPointer r2
    //     0x93cc5c: add             x2, x2, HEAP, lsl #32
    // 0x93cc60: mov             x1, x2
    // 0x93cc64: cmp             w1, NULL
    // 0x93cc68: b.ne            #0x93cc70
    // 0x93cc6c: r1 = ""
    //     0x93cc6c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93cc70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x93cc70: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x93cc74: r0 = parse()
    //     0x93cc74: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0x93cc78: r1 = <VideoPlayerValue>
    //     0x93cc78: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x93cc7c: ldr             x1, [x1, #0xf90]
    // 0x93cc80: stur            x0, [fp, #-0x10]
    // 0x93cc84: r0 = VideoPlayerController()
    //     0x93cc84: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x93cc88: mov             x1, x0
    // 0x93cc8c: ldur            x2, [fp, #-0x10]
    // 0x93cc90: stur            x0, [fp, #-0x10]
    // 0x93cc94: r0 = VideoPlayerController.networkUrl()
    //     0x93cc94: bl              #0x8f9ba4  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.networkUrl
    // 0x93cc98: ldur            x0, [fp, #-0x10]
    // 0x93cc9c: ldur            x2, [fp, #-8]
    // 0x93cca0: StoreField: r2->field_13 = r0
    //     0x93cca0: stur            w0, [x2, #0x13]
    //     0x93cca4: ldurb           w16, [x2, #-1]
    //     0x93cca8: ldurb           w17, [x0, #-1]
    //     0x93ccac: and             x16, x17, x16, lsr #2
    //     0x93ccb0: tst             x16, HEAP, lsr #32
    //     0x93ccb4: b.eq            #0x93ccbc
    //     0x93ccb8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93ccbc: mov             x1, x2
    // 0x93ccc0: r0 = initVideoPlayer()
    //     0x93ccc0: bl              #0x93cd24  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer
    // 0x93ccc4: ldur            x1, [fp, #-8]
    // 0x93ccc8: StoreField: r1->field_1b = r0
    //     0x93ccc8: stur            w0, [x1, #0x1b]
    //     0x93cccc: ldurb           w16, [x1, #-1]
    //     0x93ccd0: ldurb           w17, [x0, #-1]
    //     0x93ccd4: and             x16, x17, x16, lsr #2
    //     0x93ccd8: tst             x16, HEAP, lsr #32
    //     0x93ccdc: b.eq            #0x93cce4
    //     0x93cce0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93cce4: r0 = Null
    //     0x93cce4: mov             x0, NULL
    // 0x93cce8: LeaveFrame
    //     0x93cce8: mov             SP, fp
    //     0x93ccec: ldp             fp, lr, [SP], #0x10
    // 0x93ccf0: ret
    //     0x93ccf0: ret             
    // 0x93ccf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ccf4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ccf8: b               #0x93cc28
    // 0x93ccfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ccfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initVideoPlayer(/* No info */) async {
    // ** addr: 0x93cd24, size: 0xa0
    // 0x93cd24: EnterFrame
    //     0x93cd24: stp             fp, lr, [SP, #-0x10]!
    //     0x93cd28: mov             fp, SP
    // 0x93cd2c: AllocStack(0x20)
    //     0x93cd2c: sub             SP, SP, #0x20
    // 0x93cd30: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r1, fp-0x10 */)
    //     0x93cd30: stur            NULL, [fp, #-8]
    //     0x93cd34: stur            x1, [fp, #-0x10]
    // 0x93cd38: CheckStackOverflow
    //     0x93cd38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cd3c: cmp             SP, x16
    //     0x93cd40: b.ls            #0x93cdb0
    // 0x93cd44: r1 = 1
    //     0x93cd44: movz            x1, #0x1
    // 0x93cd48: r0 = AllocateContext()
    //     0x93cd48: bl              #0x16f6108  ; AllocateContextStub
    // 0x93cd4c: mov             x2, x0
    // 0x93cd50: ldur            x1, [fp, #-0x10]
    // 0x93cd54: stur            x2, [fp, #-0x18]
    // 0x93cd58: StoreField: r2->field_f = r1
    //     0x93cd58: stur            w1, [x2, #0xf]
    // 0x93cd5c: InitAsync() -> Future<void?>
    //     0x93cd5c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x93cd60: bl              #0x6326e0  ; InitAsyncStub
    // 0x93cd64: ldur            x0, [fp, #-0x10]
    // 0x93cd68: LoadField: r1 = r0->field_13
    //     0x93cd68: ldur            w1, [x0, #0x13]
    // 0x93cd6c: DecompressPointer r1
    //     0x93cd6c: add             x1, x1, HEAP, lsl #32
    // 0x93cd70: r16 = Sentinel
    //     0x93cd70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93cd74: cmp             w1, w16
    // 0x93cd78: b.eq            #0x93cdb8
    // 0x93cd7c: r0 = initialize()
    //     0x93cd7c: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x93cd80: mov             x1, x0
    // 0x93cd84: stur            x1, [fp, #-0x20]
    // 0x93cd88: r0 = Await()
    //     0x93cd88: bl              #0x63248c  ; AwaitStub
    // 0x93cd8c: ldur            x2, [fp, #-0x18]
    // 0x93cd90: r1 = Function '<anonymous closure>':.
    //     0x93cd90: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac80] AnonymousClosure: (0x93cdc4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x93cd24)
    //     0x93cd94: ldr             x1, [x1, #0xc80]
    // 0x93cd98: r0 = AllocateClosure()
    //     0x93cd98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93cd9c: ldur            x1, [fp, #-0x10]
    // 0x93cda0: mov             x2, x0
    // 0x93cda4: r0 = setState()
    //     0x93cda4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93cda8: r0 = Null
    //     0x93cda8: mov             x0, NULL
    // 0x93cdac: r0 = ReturnAsyncNotFuture()
    //     0x93cdac: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93cdb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93cdb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93cdb4: b               #0x93cd44
    // 0x93cdb8: r9 = _controller
    //     0x93cdb8: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac70] Field <_ProductVideoMediaCarouselState@1509081183._controller@1509081183>: late (offset: 0x14)
    //     0x93cdbc: ldr             x9, [x9, #0xc70]
    // 0x93cdc0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93cdc0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x93cdc4, size: 0x168
    // 0x93cdc4: EnterFrame
    //     0x93cdc4: stp             fp, lr, [SP, #-0x10]!
    //     0x93cdc8: mov             fp, SP
    // 0x93cdcc: AllocStack(0x58)
    //     0x93cdcc: sub             SP, SP, #0x58
    // 0x93cdd0: SetupParameters()
    //     0x93cdd0: ldr             x0, [fp, #0x10]
    //     0x93cdd4: ldur            w2, [x0, #0x17]
    //     0x93cdd8: add             x2, x2, HEAP, lsl #32
    //     0x93cddc: stur            x2, [fp, #-0x18]
    // 0x93cde0: CheckStackOverflow
    //     0x93cde0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cde4: cmp             SP, x16
    //     0x93cde8: b.ls            #0x93cefc
    // 0x93cdec: LoadField: r0 = r2->field_f
    //     0x93cdec: ldur            w0, [x2, #0xf]
    // 0x93cdf0: DecompressPointer r0
    //     0x93cdf0: add             x0, x0, HEAP, lsl #32
    // 0x93cdf4: stur            x0, [fp, #-0x10]
    // 0x93cdf8: LoadField: r5 = r0->field_13
    //     0x93cdf8: ldur            w5, [x0, #0x13]
    // 0x93cdfc: DecompressPointer r5
    //     0x93cdfc: add             x5, x5, HEAP, lsl #32
    // 0x93ce00: r16 = Sentinel
    //     0x93ce00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93ce04: cmp             w5, w16
    // 0x93ce08: b.eq            #0x93cf04
    // 0x93ce0c: stur            x5, [fp, #-8]
    // 0x93ce10: LoadField: r1 = r5->field_27
    //     0x93ce10: ldur            w1, [x5, #0x27]
    // 0x93ce14: DecompressPointer r1
    //     0x93ce14: add             x1, x1, HEAP, lsl #32
    // 0x93ce18: r0 = aspectRatio()
    //     0x93ce18: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0x93ce1c: r0 = inline_Allocate_Double()
    //     0x93ce1c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x93ce20: add             x0, x0, #0x10
    //     0x93ce24: cmp             x1, x0
    //     0x93ce28: b.ls            #0x93cf10
    //     0x93ce2c: str             x0, [THR, #0x50]  ; THR::top
    //     0x93ce30: sub             x0, x0, #0xf
    //     0x93ce34: movz            x1, #0xe15c
    //     0x93ce38: movk            x1, #0x3, lsl #16
    //     0x93ce3c: stur            x1, [x0, #-1]
    // 0x93ce40: StoreField: r0->field_7 = d0
    //     0x93ce40: stur            d0, [x0, #7]
    // 0x93ce44: stur            x0, [fp, #-0x20]
    // 0x93ce48: r1 = Function '<anonymous closure>':.
    //     0x93ce48: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac88] AnonymousClosure: (0x934ed0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x9350dc)
    //     0x93ce4c: ldr             x1, [x1, #0xc88]
    // 0x93ce50: r2 = Null
    //     0x93ce50: mov             x2, NULL
    // 0x93ce54: r0 = AllocateClosure()
    //     0x93ce54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ce58: stur            x0, [fp, #-0x28]
    // 0x93ce5c: r0 = ChewieController()
    //     0x93ce5c: bl              #0x8faaa0  ; AllocateChewieControllerStub -> ChewieController (size=0xdc)
    // 0x93ce60: stur            x0, [fp, #-0x30]
    // 0x93ce64: ldur            x16, [fp, #-0x20]
    // 0x93ce68: r30 = true
    //     0x93ce68: add             lr, NULL, #0x20  ; true
    // 0x93ce6c: stp             lr, x16, [SP, #0x18]
    // 0x93ce70: r16 = true
    //     0x93ce70: add             x16, NULL, #0x20  ; true
    // 0x93ce74: r30 = false
    //     0x93ce74: add             lr, NULL, #0x30  ; false
    // 0x93ce78: stp             lr, x16, [SP, #8]
    // 0x93ce7c: ldur            x16, [fp, #-0x28]
    // 0x93ce80: str             x16, [SP]
    // 0x93ce84: mov             x1, x0
    // 0x93ce88: ldur            x5, [fp, #-8]
    // 0x93ce8c: r2 = true
    //     0x93ce8c: add             x2, NULL, #0x20  ; true
    // 0x93ce90: r3 = true
    //     0x93ce90: add             x3, NULL, #0x20  ; true
    // 0x93ce94: r4 = const [0, 0x9, 0x5, 0x4, allowFullScreen, 0x7, aspectRatio, 0x4, autoInitialize, 0x5, errorBuilder, 0x8, showOptions, 0x6, null]
    //     0x93ce94: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a1c0] List(15) [0, 0x9, 0x5, 0x4, "allowFullScreen", 0x7, "aspectRatio", 0x4, "autoInitialize", 0x5, "errorBuilder", 0x8, "showOptions", 0x6, Null]
    //     0x93ce98: ldr             x4, [x4, #0x1c0]
    // 0x93ce9c: r0 = ChewieController()
    //     0x93ce9c: bl              #0x8fa550  ; [package:chewie/src/chewie_player.dart] ChewieController::ChewieController
    // 0x93cea0: ldur            x0, [fp, #-0x30]
    // 0x93cea4: ldur            x1, [fp, #-0x10]
    // 0x93cea8: ArrayStore: r1[0] = r0  ; List_4
    //     0x93cea8: stur            w0, [x1, #0x17]
    //     0x93ceac: ldurb           w16, [x1, #-1]
    //     0x93ceb0: ldurb           w17, [x0, #-1]
    //     0x93ceb4: and             x16, x17, x16, lsr #2
    //     0x93ceb8: tst             x16, HEAP, lsr #32
    //     0x93cebc: b.eq            #0x93cec4
    //     0x93cec0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93cec4: ldur            x0, [fp, #-0x18]
    // 0x93cec8: LoadField: r1 = r0->field_f
    //     0x93cec8: ldur            w1, [x0, #0xf]
    // 0x93cecc: DecompressPointer r1
    //     0x93cecc: add             x1, x1, HEAP, lsl #32
    // 0x93ced0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x93ced0: ldur            w0, [x1, #0x17]
    // 0x93ced4: DecompressPointer r0
    //     0x93ced4: add             x0, x0, HEAP, lsl #32
    // 0x93ced8: r16 = Sentinel
    //     0x93ced8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x93cedc: cmp             w0, w16
    // 0x93cee0: b.eq            #0x93cf20
    // 0x93cee4: mov             x1, x0
    // 0x93cee8: r0 = setVolume()
    //     0x93cee8: bl              #0x8fa424  ; [package:chewie/src/chewie_player.dart] ChewieController::setVolume
    // 0x93ceec: r0 = Null
    //     0x93ceec: mov             x0, NULL
    // 0x93cef0: LeaveFrame
    //     0x93cef0: mov             SP, fp
    //     0x93cef4: ldp             fp, lr, [SP], #0x10
    // 0x93cef8: ret
    //     0x93cef8: ret             
    // 0x93cefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93cefc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93cf00: b               #0x93cdec
    // 0x93cf04: r9 = _controller
    //     0x93cf04: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac70] Field <_ProductVideoMediaCarouselState@1509081183._controller@1509081183>: late (offset: 0x14)
    //     0x93cf08: ldr             x9, [x9, #0xc70]
    // 0x93cf0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93cf0c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x93cf10: SaveReg d0
    //     0x93cf10: str             q0, [SP, #-0x10]!
    // 0x93cf14: r0 = AllocateDouble()
    //     0x93cf14: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x93cf18: RestoreReg d0
    //     0x93cf18: ldr             q0, [SP], #0x10
    // 0x93cf1c: b               #0x93ce40
    // 0x93cf20: r9 = _chewieController
    //     0x93cf20: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac78] Field <_ProductVideoMediaCarouselState@1509081183._chewieController@1509081183>: late (offset: 0x18)
    //     0x93cf24: ldr             x9, [x9, #0xc78]
    // 0x93cf28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x93cf28: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb124d4, size: 0xa4
    // 0xb124d4: EnterFrame
    //     0xb124d4: stp             fp, lr, [SP, #-0x10]!
    //     0xb124d8: mov             fp, SP
    // 0xb124dc: AllocStack(0x18)
    //     0xb124dc: sub             SP, SP, #0x18
    // 0xb124e0: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0xb124e0: stur            x1, [fp, #-8]
    // 0xb124e4: r1 = 1
    //     0xb124e4: movz            x1, #0x1
    // 0xb124e8: r0 = AllocateContext()
    //     0xb124e8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb124ec: mov             x2, x0
    // 0xb124f0: ldur            x0, [fp, #-8]
    // 0xb124f4: stur            x2, [fp, #-0x18]
    // 0xb124f8: StoreField: r2->field_f = r0
    //     0xb124f8: stur            w0, [x2, #0xf]
    // 0xb124fc: LoadField: r3 = r0->field_1b
    //     0xb124fc: ldur            w3, [x0, #0x1b]
    // 0xb12500: DecompressPointer r3
    //     0xb12500: add             x3, x3, HEAP, lsl #32
    // 0xb12504: r16 = Sentinel
    //     0xb12504: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb12508: cmp             w3, w16
    // 0xb1250c: b.eq            #0xb1256c
    // 0xb12510: stur            x3, [fp, #-0x10]
    // 0xb12514: r1 = <void?>
    //     0xb12514: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0xb12518: r0 = FutureBuilder()
    //     0xb12518: bl              #0xa89a78  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xb1251c: mov             x3, x0
    // 0xb12520: ldur            x0, [fp, #-0x10]
    // 0xb12524: stur            x3, [fp, #-8]
    // 0xb12528: StoreField: r3->field_f = r0
    //     0xb12528: stur            w0, [x3, #0xf]
    // 0xb1252c: ldur            x2, [fp, #-0x18]
    // 0xb12530: r1 = Function '<anonymous closure>':.
    //     0xb12530: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac60] AnonymousClosure: (0xb12578), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::build (0xb124d4)
    //     0xb12534: ldr             x1, [x1, #0xc60]
    // 0xb12538: r0 = AllocateClosure()
    //     0xb12538: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1253c: mov             x1, x0
    // 0xb12540: ldur            x0, [fp, #-8]
    // 0xb12544: StoreField: r0->field_13 = r1
    //     0xb12544: stur            w1, [x0, #0x13]
    // 0xb12548: r0 = Padding()
    //     0xb12548: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1254c: r1 = Instance_EdgeInsets
    //     0xb1254c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb12550: ldr             x1, [x1, #0x980]
    // 0xb12554: StoreField: r0->field_f = r1
    //     0xb12554: stur            w1, [x0, #0xf]
    // 0xb12558: ldur            x1, [fp, #-8]
    // 0xb1255c: StoreField: r0->field_b = r1
    //     0xb1255c: stur            w1, [x0, #0xb]
    // 0xb12560: LeaveFrame
    //     0xb12560: mov             SP, fp
    //     0xb12564: ldp             fp, lr, [SP], #0x10
    // 0xb12568: ret
    //     0xb12568: ret             
    // 0xb1256c: r9 = _future
    //     0xb1256c: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac68] Field <_ProductVideoMediaCarouselState@1509081183._future@1509081183>: late (offset: 0x1c)
    //     0xb12570: ldr             x9, [x9, #0xc68]
    // 0xb12574: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb12574: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<Object?>) {
    // ** addr: 0xb12578, size: 0x104
    // 0xb12578: EnterFrame
    //     0xb12578: stp             fp, lr, [SP, #-0x10]!
    //     0xb1257c: mov             fp, SP
    // 0xb12580: AllocStack(0x18)
    //     0xb12580: sub             SP, SP, #0x18
    // 0xb12584: SetupParameters()
    //     0xb12584: ldr             x0, [fp, #0x20]
    //     0xb12588: ldur            w2, [x0, #0x17]
    //     0xb1258c: add             x2, x2, HEAP, lsl #32
    //     0xb12590: stur            x2, [fp, #-8]
    // 0xb12594: CheckStackOverflow
    //     0xb12594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb12598: cmp             SP, x16
    //     0xb1259c: b.ls            #0xb1265c
    // 0xb125a0: LoadField: r0 = r2->field_f
    //     0xb125a0: ldur            w0, [x2, #0xf]
    // 0xb125a4: DecompressPointer r0
    //     0xb125a4: add             x0, x0, HEAP, lsl #32
    // 0xb125a8: LoadField: r1 = r0->field_13
    //     0xb125a8: ldur            w1, [x0, #0x13]
    // 0xb125ac: DecompressPointer r1
    //     0xb125ac: add             x1, x1, HEAP, lsl #32
    // 0xb125b0: r16 = Sentinel
    //     0xb125b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb125b4: cmp             w1, w16
    // 0xb125b8: b.eq            #0xb12664
    // 0xb125bc: LoadField: r0 = r1->field_27
    //     0xb125bc: ldur            w0, [x1, #0x27]
    // 0xb125c0: DecompressPointer r0
    //     0xb125c0: add             x0, x0, HEAP, lsl #32
    // 0xb125c4: LoadField: r1 = r0->field_4b
    //     0xb125c4: ldur            w1, [x0, #0x4b]
    // 0xb125c8: DecompressPointer r1
    //     0xb125c8: add             x1, x1, HEAP, lsl #32
    // 0xb125cc: tbnz            w1, #4, #0xb1262c
    // 0xb125d0: mov             x1, x0
    // 0xb125d4: r0 = aspectRatio()
    //     0xb125d4: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0xb125d8: ldur            x0, [fp, #-8]
    // 0xb125dc: stur            d0, [fp, #-0x18]
    // 0xb125e0: LoadField: r1 = r0->field_f
    //     0xb125e0: ldur            w1, [x0, #0xf]
    // 0xb125e4: DecompressPointer r1
    //     0xb125e4: add             x1, x1, HEAP, lsl #32
    // 0xb125e8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb125e8: ldur            w0, [x1, #0x17]
    // 0xb125ec: DecompressPointer r0
    //     0xb125ec: add             x0, x0, HEAP, lsl #32
    // 0xb125f0: r16 = Sentinel
    //     0xb125f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb125f4: cmp             w0, w16
    // 0xb125f8: b.eq            #0xb12670
    // 0xb125fc: stur            x0, [fp, #-8]
    // 0xb12600: r0 = Chewie()
    //     0xb12600: bl              #0x9d1c78  ; AllocateChewieStub -> Chewie (size=0x10)
    // 0xb12604: mov             x1, x0
    // 0xb12608: ldur            x0, [fp, #-8]
    // 0xb1260c: stur            x1, [fp, #-0x10]
    // 0xb12610: StoreField: r1->field_b = r0
    //     0xb12610: stur            w0, [x1, #0xb]
    // 0xb12614: r0 = AspectRatio()
    //     0xb12614: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb12618: ldur            d0, [fp, #-0x18]
    // 0xb1261c: StoreField: r0->field_f = d0
    //     0xb1261c: stur            d0, [x0, #0xf]
    // 0xb12620: ldur            x1, [fp, #-0x10]
    // 0xb12624: StoreField: r0->field_b = r1
    //     0xb12624: stur            w1, [x0, #0xb]
    // 0xb12628: b               #0xb12634
    // 0xb1262c: r0 = Instance_CircularProgressIndicator
    //     0xb1262c: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3daa8] Obj!CircularProgressIndicator@d65741
    //     0xb12630: ldr             x0, [x0, #0xaa8]
    // 0xb12634: stur            x0, [fp, #-8]
    // 0xb12638: r0 = Center()
    //     0xb12638: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb1263c: r1 = Instance_Alignment
    //     0xb1263c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb12640: ldr             x1, [x1, #0xb10]
    // 0xb12644: StoreField: r0->field_f = r1
    //     0xb12644: stur            w1, [x0, #0xf]
    // 0xb12648: ldur            x1, [fp, #-8]
    // 0xb1264c: StoreField: r0->field_b = r1
    //     0xb1264c: stur            w1, [x0, #0xb]
    // 0xb12650: LeaveFrame
    //     0xb12650: mov             SP, fp
    //     0xb12654: ldp             fp, lr, [SP], #0x10
    // 0xb12658: ret
    //     0xb12658: ret             
    // 0xb1265c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1265c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb12660: b               #0xb125a0
    // 0xb12664: r9 = _controller
    //     0xb12664: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac70] Field <_ProductVideoMediaCarouselState@1509081183._controller@1509081183>: late (offset: 0x14)
    //     0xb12668: ldr             x9, [x9, #0xc70]
    // 0xb1266c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1266c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb12670: r9 = _chewieController
    //     0xb12670: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac78] Field <_ProductVideoMediaCarouselState@1509081183._chewieController@1509081183>: late (offset: 0x18)
    //     0xb12674: ldr             x9, [x9, #0xc78]
    // 0xb12678: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xb12678: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87764, size: 0x54
    // 0xc87764: EnterFrame
    //     0xc87764: stp             fp, lr, [SP, #-0x10]!
    //     0xc87768: mov             fp, SP
    // 0xc8776c: CheckStackOverflow
    //     0xc8776c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87770: cmp             SP, x16
    //     0xc87774: b.ls            #0xc877a4
    // 0xc87778: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc87778: ldur            w0, [x1, #0x17]
    // 0xc8777c: DecompressPointer r0
    //     0xc8777c: add             x0, x0, HEAP, lsl #32
    // 0xc87780: r16 = Sentinel
    //     0xc87780: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87784: cmp             w0, w16
    // 0xc87788: b.eq            #0xc877ac
    // 0xc8778c: mov             x1, x0
    // 0xc87790: r0 = dispose()
    //     0xc87790: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87794: r0 = Null
    //     0xc87794: mov             x0, NULL
    // 0xc87798: LeaveFrame
    //     0xc87798: mov             SP, fp
    //     0xc8779c: ldp             fp, lr, [SP], #0x10
    // 0xc877a0: ret
    //     0xc877a0: ret             
    // 0xc877a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc877a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc877a8: b               #0xc87778
    // 0xc877ac: r9 = _chewieController
    //     0xc877ac: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ac78] Field <_ProductVideoMediaCarouselState@1509081183._chewieController@1509081183>: late (offset: 0x18)
    //     0xc877b0: ldr             x9, [x9, #0xc78]
    // 0xc877b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc877b4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4136, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductVideoMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e148, size: 0x34
    // 0xc7e148: EnterFrame
    //     0xc7e148: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e14c: mov             fp, SP
    // 0xc7e150: mov             x0, x1
    // 0xc7e154: r1 = <ProductVideoMediaCarousel>
    //     0xc7e154: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e80] TypeArguments: <ProductVideoMediaCarousel>
    //     0xc7e158: ldr             x1, [x1, #0xe80]
    // 0xc7e15c: r0 = _ProductVideoMediaCarouselState()
    //     0xc7e15c: bl              #0xc7e17c  ; Allocate_ProductVideoMediaCarouselStateStub -> _ProductVideoMediaCarouselState (size=0x20)
    // 0xc7e160: r1 = Sentinel
    //     0xc7e160: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7e164: StoreField: r0->field_13 = r1
    //     0xc7e164: stur            w1, [x0, #0x13]
    // 0xc7e168: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7e168: stur            w1, [x0, #0x17]
    // 0xc7e16c: StoreField: r0->field_1b = r1
    //     0xc7e16c: stur            w1, [x0, #0x1b]
    // 0xc7e170: LeaveFrame
    //     0xc7e170: mov             SP, fp
    //     0xc7e174: ldp             fp, lr, [SP], #0x10
    // 0xc7e178: ret
    //     0xc7e178: ret             
  }
}
