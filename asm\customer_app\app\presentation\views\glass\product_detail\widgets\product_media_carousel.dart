// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart

// class id: 1049437, size: 0x8
class :: {
}

// class id: 3313, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x7f378c, size: 0x40
    // 0x7f378c: EnterFrame
    //     0x7f378c: stp             fp, lr, [SP, #-0x10]!
    //     0x7f3790: mov             fp, SP
    // 0x7f3794: CheckStackOverflow
    //     0x7f3794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f3798: cmp             SP, x16
    //     0x7f379c: b.ls            #0x7f37c4
    // 0x7f37a0: LoadField: r0 = r1->field_13
    //     0x7f37a0: ldur            w0, [x1, #0x13]
    // 0x7f37a4: DecompressPointer r0
    //     0x7f37a4: add             x0, x0, HEAP, lsl #32
    // 0x7f37a8: cmp             w0, NULL
    // 0x7f37ac: b.eq            #0x7f37b4
    // 0x7f37b0: r0 = _releaseKeepAlive()
    //     0x7f37b0: bl              #0x7f36b0  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x7f37b4: r0 = Null
    //     0x7f37b4: mov             x0, NULL
    // 0x7f37b8: LeaveFrame
    //     0x7f37b8: mov             SP, fp
    //     0x7f37bc: ldp             fp, lr, [SP], #0x10
    // 0x7f37c0: ret
    //     0x7f37c0: ret             
    // 0x7f37c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f37c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f37c8: b               #0x7f37a0
  }
  _ initState(/* No info */) {
    // ** addr: 0x944064, size: 0x30
    // 0x944064: EnterFrame
    //     0x944064: stp             fp, lr, [SP, #-0x10]!
    //     0x944068: mov             fp, SP
    // 0x94406c: CheckStackOverflow
    //     0x94406c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944070: cmp             SP, x16
    //     0x944074: b.ls            #0x94408c
    // 0x944078: r0 = _ensureKeepAlive()
    //     0x944078: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x94407c: r0 = Null
    //     0x94407c: mov             x0, NULL
    // 0x944080: LeaveFrame
    //     0x944080: mov             SP, fp
    //     0x944084: ldp             fp, lr, [SP], #0x10
    // 0x944088: ret
    //     0x944088: ret             
    // 0x94408c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94408c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944090: b               #0x944078
  }
  _ build(/* No info */) {
    // ** addr: 0xb86ccc, size: 0x44
    // 0xb86ccc: EnterFrame
    //     0xb86ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xb86cd0: mov             fp, SP
    // 0xb86cd4: CheckStackOverflow
    //     0xb86cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86cd8: cmp             SP, x16
    //     0xb86cdc: b.ls            #0xb86d08
    // 0xb86ce0: LoadField: r0 = r1->field_13
    //     0xb86ce0: ldur            w0, [x1, #0x13]
    // 0xb86ce4: DecompressPointer r0
    //     0xb86ce4: add             x0, x0, HEAP, lsl #32
    // 0xb86ce8: cmp             w0, NULL
    // 0xb86cec: b.ne            #0xb86cf4
    // 0xb86cf0: r0 = _ensureKeepAlive()
    //     0xb86cf0: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xb86cf4: r0 = Instance__NullWidget
    //     0xb86cf4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b08] Obj!_NullWidget@d66b41
    //     0xb86cf8: ldr             x0, [x0, #0xb08]
    // 0xb86cfc: LeaveFrame
    //     0xb86cfc: mov             SP, fp
    //     0xb86d00: ldp             fp, lr, [SP], #0x10
    // 0xb86d04: ret
    //     0xb86d04: ret             
    // 0xb86d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86d08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86d0c: b               #0xb86ce0
  }
}

// class id: 3314, size: 0x34, field offset: 0x18
class _ProductMediaCarouselState extends __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x80505c, size: 0x180
    // 0x80505c: EnterFrame
    //     0x80505c: stp             fp, lr, [SP, #-0x10]!
    //     0x805060: mov             fp, SP
    // 0x805064: AllocStack(0x20)
    //     0x805064: sub             SP, SP, #0x20
    // 0x805068: SetupParameters(_ProductMediaCarouselState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x805068: mov             x4, x1
    //     0x80506c: mov             x3, x2
    //     0x805070: stur            x1, [fp, #-8]
    //     0x805074: stur            x2, [fp, #-0x10]
    // 0x805078: CheckStackOverflow
    //     0x805078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80507c: cmp             SP, x16
    //     0x805080: b.ls            #0x8051d0
    // 0x805084: mov             x0, x3
    // 0x805088: r2 = Null
    //     0x805088: mov             x2, NULL
    // 0x80508c: r1 = Null
    //     0x80508c: mov             x1, NULL
    // 0x805090: r4 = 60
    //     0x805090: movz            x4, #0x3c
    // 0x805094: branchIfSmi(r0, 0x8050a0)
    //     0x805094: tbz             w0, #0, #0x8050a0
    // 0x805098: r4 = LoadClassIdInstr(r0)
    //     0x805098: ldur            x4, [x0, #-1]
    //     0x80509c: ubfx            x4, x4, #0xc, #0x14
    // 0x8050a0: cmp             x4, #0xfd8
    // 0x8050a4: b.eq            #0x8050bc
    // 0x8050a8: r8 = ProductMediaCarousel
    //     0x8050a8: add             x8, PP, #0x55, lsl #12  ; [pp+0x55718] Type: ProductMediaCarousel
    //     0x8050ac: ldr             x8, [x8, #0x718]
    // 0x8050b0: r3 = Null
    //     0x8050b0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55720] Null
    //     0x8050b4: ldr             x3, [x3, #0x720]
    // 0x8050b8: r0 = ProductMediaCarousel()
    //     0x8050b8: bl              #0x7f37cc  ; IsType_ProductMediaCarousel_Stub
    // 0x8050bc: ldur            x3, [fp, #-8]
    // 0x8050c0: LoadField: r2 = r3->field_7
    //     0x8050c0: ldur            w2, [x3, #7]
    // 0x8050c4: DecompressPointer r2
    //     0x8050c4: add             x2, x2, HEAP, lsl #32
    // 0x8050c8: ldur            x0, [fp, #-0x10]
    // 0x8050cc: r1 = Null
    //     0x8050cc: mov             x1, NULL
    // 0x8050d0: cmp             w2, NULL
    // 0x8050d4: b.eq            #0x8050f8
    // 0x8050d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8050d8: ldur            w4, [x2, #0x17]
    // 0x8050dc: DecompressPointer r4
    //     0x8050dc: add             x4, x4, HEAP, lsl #32
    // 0x8050e0: r8 = X0 bound StatefulWidget
    //     0x8050e0: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x8050e4: ldr             x8, [x8, #0x7a0]
    // 0x8050e8: LoadField: r9 = r4->field_7
    //     0x8050e8: ldur            x9, [x4, #7]
    // 0x8050ec: r3 = Null
    //     0x8050ec: add             x3, PP, #0x55, lsl #12  ; [pp+0x55730] Null
    //     0x8050f0: ldr             x3, [x3, #0x730]
    // 0x8050f4: blr             x9
    // 0x8050f8: ldur            x0, [fp, #-0x10]
    // 0x8050fc: LoadField: r1 = r0->field_b
    //     0x8050fc: ldur            w1, [x0, #0xb]
    // 0x805100: DecompressPointer r1
    //     0x805100: add             x1, x1, HEAP, lsl #32
    // 0x805104: ldur            x2, [fp, #-8]
    // 0x805108: LoadField: r3 = r2->field_b
    //     0x805108: ldur            w3, [x2, #0xb]
    // 0x80510c: DecompressPointer r3
    //     0x80510c: add             x3, x3, HEAP, lsl #32
    // 0x805110: cmp             w3, NULL
    // 0x805114: b.eq            #0x8051d8
    // 0x805118: LoadField: r4 = r3->field_b
    //     0x805118: ldur            w4, [x3, #0xb]
    // 0x80511c: DecompressPointer r4
    //     0x80511c: add             x4, x4, HEAP, lsl #32
    // 0x805120: cmp             w1, w4
    // 0x805124: b.eq            #0x805130
    // 0x805128: mov             x0, x2
    // 0x80512c: b               #0x805164
    // 0x805130: LoadField: r1 = r0->field_f
    //     0x805130: ldur            w1, [x0, #0xf]
    // 0x805134: DecompressPointer r1
    //     0x805134: add             x1, x1, HEAP, lsl #32
    // 0x805138: LoadField: r0 = r3->field_f
    //     0x805138: ldur            w0, [x3, #0xf]
    // 0x80513c: DecompressPointer r0
    //     0x80513c: add             x0, x0, HEAP, lsl #32
    // 0x805140: r3 = LoadClassIdInstr(r1)
    //     0x805140: ldur            x3, [x1, #-1]
    //     0x805144: ubfx            x3, x3, #0xc, #0x14
    // 0x805148: stp             x0, x1, [SP]
    // 0x80514c: mov             x0, x3
    // 0x805150: mov             lr, x0
    // 0x805154: ldr             lr, [x21, lr, lsl #3]
    // 0x805158: blr             lr
    // 0x80515c: tbz             w0, #4, #0x8051c0
    // 0x805160: ldur            x0, [fp, #-8]
    // 0x805164: LoadField: r1 = r0->field_27
    //     0x805164: ldur            w1, [x0, #0x27]
    // 0x805168: DecompressPointer r1
    //     0x805168: add             x1, x1, HEAP, lsl #32
    // 0x80516c: r0 = clear()
    //     0x80516c: bl              #0x649b54  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x805170: ldur            x0, [fp, #-8]
    // 0x805174: StoreField: r0->field_2f = rNULL
    //     0x805174: stur            NULL, [x0, #0x2f]
    // 0x805178: mov             x1, x0
    // 0x80517c: r0 = _initializeMediaItems()
    //     0x80517c: bl              #0x80568c  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x805180: ldur            x1, [fp, #-8]
    // 0x805184: r0 = _preloadImageSizes()
    //     0x805184: bl              #0x8051dc  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x805188: ldur            x0, [fp, #-8]
    // 0x80518c: StoreField: r0->field_1b = rZR
    //     0x80518c: stur            xzr, [x0, #0x1b]
    // 0x805190: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x805190: ldur            w1, [x0, #0x17]
    // 0x805194: DecompressPointer r1
    //     0x805194: add             x1, x1, HEAP, lsl #32
    // 0x805198: LoadField: r0 = r1->field_3b
    //     0x805198: ldur            w0, [x1, #0x3b]
    // 0x80519c: DecompressPointer r0
    //     0x80519c: add             x0, x0, HEAP, lsl #32
    // 0x8051a0: LoadField: r2 = r0->field_b
    //     0x8051a0: ldur            w2, [x0, #0xb]
    // 0x8051a4: cbz             w2, #0x8051c0
    // 0x8051a8: r2 = 0
    //     0x8051a8: movz            x2, #0
    // 0x8051ac: r3 = Instance_Cubic
    //     0x8051ac: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x8051b0: ldr             x3, [x3, #0x2b0]
    // 0x8051b4: r5 = Instance_Duration
    //     0x8051b4: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x8051b8: ldr             x5, [x5, #0xf00]
    // 0x8051bc: r0 = animateToPage()
    //     0x8051bc: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0x8051c0: r0 = Null
    //     0x8051c0: mov             x0, NULL
    // 0x8051c4: LeaveFrame
    //     0x8051c4: mov             SP, fp
    //     0x8051c8: ldp             fp, lr, [SP], #0x10
    // 0x8051cc: ret
    //     0x8051cc: ret             
    // 0x8051d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8051d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8051d4: b               #0x805084
    // 0x8051d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8051d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _preloadImageSizes(/* No info */) async {
    // ** addr: 0x8051dc, size: 0x3f8
    // 0x8051dc: EnterFrame
    //     0x8051dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8051e0: mov             fp, SP
    // 0x8051e4: AllocStack(0xb8)
    //     0x8051e4: sub             SP, SP, #0xb8
    // 0x8051e8: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x80 */)
    //     0x8051e8: stur            NULL, [fp, #-8]
    //     0x8051ec: stur            x1, [fp, #-0x80]
    // 0x8051f0: CheckStackOverflow
    //     0x8051f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8051f4: cmp             SP, x16
    //     0x8051f8: b.ls            #0x8055b0
    // 0x8051fc: r1 = 1
    //     0x8051fc: movz            x1, #0x1
    // 0x805200: r0 = AllocateContext()
    //     0x805200: bl              #0x16f6108  ; AllocateContextStub
    // 0x805204: mov             x2, x0
    // 0x805208: ldur            x1, [fp, #-0x80]
    // 0x80520c: stur            x2, [fp, #-0x88]
    // 0x805210: StoreField: r2->field_f = r1
    //     0x805210: stur            w1, [x2, #0xf]
    // 0x805214: InitAsync() -> Future<void?>
    //     0x805214: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x805218: bl              #0x6326e0  ; InitAsyncStub
    // 0x80521c: ldur            x1, [fp, #-0x80]
    // 0x805220: LoadField: r0 = r1->field_2b
    //     0x805220: ldur            w0, [x1, #0x2b]
    // 0x805224: DecompressPointer r0
    //     0x805224: add             x0, x0, HEAP, lsl #32
    // 0x805228: tbnz            w0, #4, #0x805234
    // 0x80522c: r0 = Null
    //     0x80522c: mov             x0, NULL
    // 0x805230: r0 = ReturnAsyncNotFuture()
    //     0x805230: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x805234: ldur            x0, [fp, #-0x88]
    // 0x805238: r1 = 3
    //     0x805238: movz            x1, #0x3
    // 0x80523c: r0 = AllocateContext()
    //     0x80523c: bl              #0x16f6108  ; AllocateContextStub
    // 0x805240: mov             x1, x0
    // 0x805244: ldur            x0, [fp, #-0x88]
    // 0x805248: StoreField: r1->field_b = r0
    //     0x805248: stur            w0, [x1, #0xb]
    // 0x80524c: StoreField: r1->field_f = rZR
    //     0x80524c: stur            wzr, [x1, #0xf]
    // 0x805250: ldur            x2, [fp, #-0x80]
    // 0x805254: LoadField: r3 = r2->field_27
    //     0x805254: ldur            w3, [x2, #0x27]
    // 0x805258: DecompressPointer r3
    //     0x805258: add             x3, x3, HEAP, lsl #32
    // 0x80525c: stur            x3, [fp, #-0xa0]
    // 0x805260: mov             x6, x1
    // 0x805264: r5 = Null
    //     0x805264: mov             x5, NULL
    // 0x805268: r4 = 0
    //     0x805268: movz            x4, #0
    // 0x80526c: stur            x6, [fp, #-0x90]
    // 0x805270: stur            x5, [fp, #-0x98]
    // 0x805274: CheckStackOverflow
    //     0x805274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805278: cmp             SP, x16
    //     0x80527c: b.ls            #0x8055b8
    // 0x805280: LoadField: r7 = r2->field_23
    //     0x805280: ldur            w7, [x2, #0x23]
    // 0x805284: DecompressPointer r7
    //     0x805284: add             x7, x7, HEAP, lsl #32
    // 0x805288: LoadField: r0 = r7->field_b
    //     0x805288: ldur            w0, [x7, #0xb]
    // 0x80528c: r1 = LoadInt32Instr(r0)
    //     0x80528c: sbfx            x1, x0, #1, #0x1f
    // 0x805290: cmp             x4, x1
    // 0x805294: b.ge            #0x8055a8
    // 0x805298: mov             x0, x1
    // 0x80529c: mov             x1, x4
    // 0x8052a0: cmp             x1, x0
    // 0x8052a4: b.hs            #0x8055c0
    // 0x8052a8: LoadField: r0 = r7->field_f
    //     0x8052a8: ldur            w0, [x7, #0xf]
    // 0x8052ac: DecompressPointer r0
    //     0x8052ac: add             x0, x0, HEAP, lsl #32
    // 0x8052b0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x8052b0: add             x16, x0, x4, lsl #2
    //     0x8052b4: ldur            w1, [x16, #0xf]
    // 0x8052b8: DecompressPointer r1
    //     0x8052b8: add             x1, x1, HEAP, lsl #32
    // 0x8052bc: stur            x1, [fp, #-0x88]
    // 0x8052c0: LoadField: r0 = r1->field_1f
    //     0x8052c0: ldur            w0, [x1, #0x1f]
    // 0x8052c4: DecompressPointer r0
    //     0x8052c4: add             x0, x0, HEAP, lsl #32
    // 0x8052c8: r4 = LoadClassIdInstr(r0)
    //     0x8052c8: ldur            x4, [x0, #-1]
    //     0x8052cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8052d0: r16 = "video"
    //     0x8052d0: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x8052d4: ldr             x16, [x16, #0xb50]
    // 0x8052d8: stp             x16, x0, [SP]
    // 0x8052dc: mov             x0, x4
    // 0x8052e0: mov             lr, x0
    // 0x8052e4: ldr             lr, [x21, lr, lsl #3]
    // 0x8052e8: blr             lr
    // 0x8052ec: tbz             w0, #4, #0x805530
    // 0x8052f0: ldur            x0, [fp, #-0x88]
    // 0x8052f4: LoadField: r1 = r0->field_2b
    //     0x8052f4: ldur            w1, [x0, #0x2b]
    // 0x8052f8: DecompressPointer r1
    //     0x8052f8: add             x1, x1, HEAP, lsl #32
    // 0x8052fc: cmp             w1, NULL
    // 0x805300: b.ne            #0x80530c
    // 0x805304: r0 = Null
    //     0x805304: mov             x0, NULL
    // 0x805308: b               #0x805338
    // 0x80530c: LoadField: r0 = r1->field_b
    //     0x80530c: ldur            w0, [x1, #0xb]
    // 0x805310: DecompressPointer r0
    //     0x805310: add             x0, x0, HEAP, lsl #32
    // 0x805314: cmp             w0, NULL
    // 0x805318: b.ne            #0x805324
    // 0x80531c: r0 = Null
    //     0x80531c: mov             x0, NULL
    // 0x805320: b               #0x805338
    // 0x805324: LoadField: r2 = r0->field_7
    //     0x805324: ldur            w2, [x0, #7]
    // 0x805328: cbnz            w2, #0x805334
    // 0x80532c: r0 = false
    //     0x80532c: add             x0, NULL, #0x30  ; false
    // 0x805330: b               #0x805338
    // 0x805334: r0 = true
    //     0x805334: add             x0, NULL, #0x20  ; true
    // 0x805338: cmp             w0, NULL
    // 0x80533c: b.eq            #0x805530
    // 0x805340: tbnz            w0, #4, #0x805530
    // 0x805344: ldur            x4, [fp, #-0x90]
    // 0x805348: ldur            x3, [fp, #-0xa0]
    // 0x80534c: cmp             w1, NULL
    // 0x805350: b.eq            #0x8055c4
    // 0x805354: LoadField: r5 = r1->field_b
    //     0x805354: ldur            w5, [x1, #0xb]
    // 0x805358: DecompressPointer r5
    //     0x805358: add             x5, x5, HEAP, lsl #32
    // 0x80535c: stur            x5, [fp, #-0xa8]
    // 0x805360: cmp             w5, NULL
    // 0x805364: b.eq            #0x8055c8
    // 0x805368: mov             x0, x5
    // 0x80536c: StoreField: r4->field_13 = r0
    //     0x80536c: stur            w0, [x4, #0x13]
    //     0x805370: ldurb           w16, [x4, #-1]
    //     0x805374: ldurb           w17, [x0, #-1]
    //     0x805378: and             x16, x17, x16, lsr #2
    //     0x80537c: tst             x16, HEAP, lsr #32
    //     0x805380: b.eq            #0x805388
    //     0x805384: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x805388: LoadField: r0 = r3->field_f
    //     0x805388: ldur            w0, [x3, #0xf]
    // 0x80538c: DecompressPointer r0
    //     0x80538c: add             x0, x0, HEAP, lsl #32
    // 0x805390: mov             x1, x3
    // 0x805394: mov             x2, x5
    // 0x805398: stur            x0, [fp, #-0x88]
    // 0x80539c: r0 = _getValueOrData()
    //     0x80539c: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8053a0: mov             x1, x0
    // 0x8053a4: ldur            x0, [fp, #-0x88]
    // 0x8053a8: cmp             w0, w1
    // 0x8053ac: b.ne            #0x8054c0
    // 0x8053b0: ldur            x0, [fp, #-0x80]
    // 0x8053b4: ldur            x2, [fp, #-0x90]
    // 0x8053b8: ldur            x1, [fp, #-0xa8]
    // 0x8053bc: r0 = ImageSizeExtension.getImageSize()
    //     0x8053bc: bl              #0x801f84  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageSizeExtension.getImageSize
    // 0x8053c0: mov             x1, x0
    // 0x8053c4: stur            x1, [fp, #-0x88]
    // 0x8053c8: r0 = Await()
    //     0x8053c8: bl              #0x63248c  ; AwaitStub
    // 0x8053cc: ldur            x3, [fp, #-0x90]
    // 0x8053d0: ArrayStore: r3[0] = r0  ; List_4
    //     0x8053d0: stur            w0, [x3, #0x17]
    //     0x8053d4: tbz             w0, #0, #0x8053f0
    //     0x8053d8: ldurb           w16, [x3, #-1]
    //     0x8053dc: ldurb           w17, [x0, #-1]
    //     0x8053e0: and             x16, x17, x16, lsr #2
    //     0x8053e4: tst             x16, HEAP, lsr #32
    //     0x8053e8: b.eq            #0x8053f0
    //     0x8053ec: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8053f0: ldur            x0, [fp, #-0x80]
    // 0x8053f4: LoadField: r1 = r0->field_2b
    //     0x8053f4: ldur            w1, [x0, #0x2b]
    // 0x8053f8: DecompressPointer r1
    //     0x8053f8: add             x1, x1, HEAP, lsl #32
    // 0x8053fc: tbz             w1, #4, #0x805454
    // 0x805400: LoadField: r1 = r0->field_f
    //     0x805400: ldur            w1, [x0, #0xf]
    // 0x805404: DecompressPointer r1
    //     0x805404: add             x1, x1, HEAP, lsl #32
    // 0x805408: cmp             w1, NULL
    // 0x80540c: b.eq            #0x805454
    // 0x805410: mov             x2, x3
    // 0x805414: r1 = Function '<anonymous closure>':.
    //     0x805414: add             x1, PP, #0x55, lsl #12  ; [pp+0x556f0] AnonymousClosure: (0x8055d4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes (0x8051dc)
    //     0x805418: ldr             x1, [x1, #0x6f0]
    // 0x80541c: r0 = AllocateClosure()
    //     0x80541c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x805420: mov             x1, x0
    // 0x805424: stur            x1, [fp, #-0x88]
    // 0x805428: str             x1, [SP]
    // 0x80542c: mov             x0, x1
    // 0x805430: ClosureCall
    //     0x805430: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x805434: ldur            x2, [x0, #0x1f]
    //     0x805438: blr             x2
    // 0x80543c: ldur            x0, [fp, #-0x80]
    // 0x805440: LoadField: r1 = r0->field_f
    //     0x805440: ldur            w1, [x0, #0xf]
    // 0x805444: DecompressPointer r1
    //     0x805444: add             x1, x1, HEAP, lsl #32
    // 0x805448: cmp             w1, NULL
    // 0x80544c: b.eq            #0x8055cc
    // 0x805450: r0 = markNeedsBuild()
    //     0x805450: bl              #0x78858c  ; [package:flutter/src/widgets/framework.dart] Element::markNeedsBuild
    // 0x805454: ldur            x0, [fp, #-0x98]
    // 0x805458: b               #0x805534
    // 0x80545c: sub             SP, fp, #0xb8
    // 0x805460: stur            x0, [fp, #-0x88]
    // 0x805464: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x805464: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x805468: ldr             x0, [x0, #0xcf0]
    //     0x80546c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x805470: cmp             w0, w16
    //     0x805474: b.ne            #0x805480
    //     0x805478: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x80547c: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x805480: r1 = Null
    //     0x805480: mov             x1, NULL
    // 0x805484: r2 = 4
    //     0x805484: movz            x2, #0x4
    // 0x805488: r0 = AllocateArray()
    //     0x805488: bl              #0x16f7198  ; AllocateArrayStub
    // 0x80548c: r16 = "Error loading image size: "
    //     0x80548c: add             x16, PP, #0x52, lsl #12  ; [pp+0x529b0] "Error loading image size: "
    //     0x805490: ldr             x16, [x16, #0x9b0]
    // 0x805494: StoreField: r0->field_f = r16
    //     0x805494: stur            w16, [x0, #0xf]
    // 0x805498: ldur            x1, [fp, #-0x88]
    // 0x80549c: StoreField: r0->field_13 = r1
    //     0x80549c: stur            w1, [x0, #0x13]
    // 0x8054a0: str             x0, [SP]
    // 0x8054a4: r0 = _interpolate()
    //     0x8054a4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8054a8: str             NULL, [SP]
    // 0x8054ac: mov             x1, x0
    // 0x8054b0: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x8054b0: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x8054b4: r0 = debugPrintThrottled()
    //     0x8054b4: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x8054b8: ldur            x0, [fp, #-0x88]
    // 0x8054bc: b               #0x805534
    // 0x8054c0: ldur            x5, [fp, #-0x90]
    // 0x8054c4: LoadField: r0 = r5->field_f
    //     0x8054c4: ldur            w0, [x5, #0xf]
    // 0x8054c8: DecompressPointer r0
    //     0x8054c8: add             x0, x0, HEAP, lsl #32
    // 0x8054cc: cbnz            w0, #0x805528
    // 0x8054d0: ldur            x0, [fp, #-0x80]
    // 0x8054d4: LoadField: r1 = r0->field_2f
    //     0x8054d4: ldur            w1, [x0, #0x2f]
    // 0x8054d8: DecompressPointer r1
    //     0x8054d8: add             x1, x1, HEAP, lsl #32
    // 0x8054dc: cmp             w1, NULL
    // 0x8054e0: b.ne            #0x805528
    // 0x8054e4: ldur            x3, [fp, #-0xa0]
    // 0x8054e8: mov             x1, x3
    // 0x8054ec: ldur            x2, [fp, #-0xa8]
    // 0x8054f0: r0 = _getValueOrData()
    //     0x8054f0: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8054f4: mov             x1, x0
    // 0x8054f8: ldur            x0, [fp, #-0xa0]
    // 0x8054fc: LoadField: r2 = r0->field_f
    //     0x8054fc: ldur            w2, [x0, #0xf]
    // 0x805500: DecompressPointer r2
    //     0x805500: add             x2, x2, HEAP, lsl #32
    // 0x805504: cmp             w2, w1
    // 0x805508: b.ne            #0x805514
    // 0x80550c: r2 = Null
    //     0x80550c: mov             x2, NULL
    // 0x805510: b               #0x805518
    // 0x805514: mov             x2, x1
    // 0x805518: cmp             w2, NULL
    // 0x80551c: b.eq            #0x8055d0
    // 0x805520: ldur            x1, [fp, #-0x80]
    // 0x805524: r0 = _calculateCarouselHeight()
    //     0x805524: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x805528: ldur            x0, [fp, #-0x98]
    // 0x80552c: b               #0x805534
    // 0x805530: ldur            x0, [fp, #-0x98]
    // 0x805534: ldur            x5, [fp, #-0x90]
    // 0x805538: stur            x0, [fp, #-0x88]
    // 0x80553c: r0 = CloneContext()
    //     0x80553c: bl              #0x16f5ae8  ; CloneContextStub
    // 0x805540: mov             x2, x0
    // 0x805544: LoadField: r3 = r2->field_f
    //     0x805544: ldur            w3, [x2, #0xf]
    // 0x805548: DecompressPointer r3
    //     0x805548: add             x3, x3, HEAP, lsl #32
    // 0x80554c: r4 = LoadInt32Instr(r3)
    //     0x80554c: sbfx            x4, x3, #1, #0x1f
    //     0x805550: tbz             w3, #0, #0x805558
    //     0x805554: ldur            x4, [x3, #7]
    // 0x805558: add             x3, x4, #1
    // 0x80555c: r0 = BoxInt64Instr(r3)
    //     0x80555c: sbfiz           x0, x3, #1, #0x1f
    //     0x805560: cmp             x3, x0, asr #1
    //     0x805564: b.eq            #0x805570
    //     0x805568: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x80556c: stur            x3, [x0, #7]
    // 0x805570: StoreField: r2->field_f = r0
    //     0x805570: stur            w0, [x2, #0xf]
    //     0x805574: tbz             w0, #0, #0x805590
    //     0x805578: ldurb           w16, [x2, #-1]
    //     0x80557c: ldurb           w17, [x0, #-1]
    //     0x805580: and             x16, x17, x16, lsr #2
    //     0x805584: tst             x16, HEAP, lsr #32
    //     0x805588: b.eq            #0x805590
    //     0x80558c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x805590: mov             x6, x2
    // 0x805594: ldur            x5, [fp, #-0x88]
    // 0x805598: mov             x4, x3
    // 0x80559c: ldur            x2, [fp, #-0x80]
    // 0x8055a0: ldur            x3, [fp, #-0xa0]
    // 0x8055a4: b               #0x80526c
    // 0x8055a8: r0 = Null
    //     0x8055a8: mov             x0, NULL
    // 0x8055ac: r0 = ReturnAsyncNotFuture()
    //     0x8055ac: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x8055b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8055b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8055b4: b               #0x8051fc
    // 0x8055b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8055b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8055bc: b               #0x805280
    // 0x8055c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8055c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8055c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8055c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8055c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8055c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8055cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8055cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8055d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8055d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8055d4, size: 0xb8
    // 0x8055d4: EnterFrame
    //     0x8055d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8055d8: mov             fp, SP
    // 0x8055dc: AllocStack(0x18)
    //     0x8055dc: sub             SP, SP, #0x18
    // 0x8055e0: SetupParameters()
    //     0x8055e0: ldr             x0, [fp, #0x10]
    //     0x8055e4: ldur            w4, [x0, #0x17]
    //     0x8055e8: add             x4, x4, HEAP, lsl #32
    //     0x8055ec: stur            x4, [fp, #-0x18]
    // 0x8055f0: CheckStackOverflow
    //     0x8055f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8055f4: cmp             SP, x16
    //     0x8055f8: b.ls            #0x805684
    // 0x8055fc: LoadField: r0 = r4->field_b
    //     0x8055fc: ldur            w0, [x4, #0xb]
    // 0x805600: DecompressPointer r0
    //     0x805600: add             x0, x0, HEAP, lsl #32
    // 0x805604: stur            x0, [fp, #-0x10]
    // 0x805608: LoadField: r1 = r0->field_f
    //     0x805608: ldur            w1, [x0, #0xf]
    // 0x80560c: DecompressPointer r1
    //     0x80560c: add             x1, x1, HEAP, lsl #32
    // 0x805610: LoadField: r2 = r1->field_27
    //     0x805610: ldur            w2, [x1, #0x27]
    // 0x805614: DecompressPointer r2
    //     0x805614: add             x2, x2, HEAP, lsl #32
    // 0x805618: LoadField: r1 = r4->field_13
    //     0x805618: ldur            w1, [x4, #0x13]
    // 0x80561c: DecompressPointer r1
    //     0x80561c: add             x1, x1, HEAP, lsl #32
    // 0x805620: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x805620: ldur            w5, [x4, #0x17]
    // 0x805624: DecompressPointer r5
    //     0x805624: add             x5, x5, HEAP, lsl #32
    // 0x805628: mov             x16, x1
    // 0x80562c: mov             x1, x2
    // 0x805630: mov             x2, x16
    // 0x805634: mov             x3, x5
    // 0x805638: stur            x5, [fp, #-8]
    // 0x80563c: r0 = []=()
    //     0x80563c: bl              #0x1699264  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x805640: ldur            x0, [fp, #-0x18]
    // 0x805644: LoadField: r1 = r0->field_f
    //     0x805644: ldur            w1, [x0, #0xf]
    // 0x805648: DecompressPointer r1
    //     0x805648: add             x1, x1, HEAP, lsl #32
    // 0x80564c: cbnz            w1, #0x805674
    // 0x805650: ldur            x0, [fp, #-0x10]
    // 0x805654: LoadField: r1 = r0->field_f
    //     0x805654: ldur            w1, [x0, #0xf]
    // 0x805658: DecompressPointer r1
    //     0x805658: add             x1, x1, HEAP, lsl #32
    // 0x80565c: LoadField: r0 = r1->field_2f
    //     0x80565c: ldur            w0, [x1, #0x2f]
    // 0x805660: DecompressPointer r0
    //     0x805660: add             x0, x0, HEAP, lsl #32
    // 0x805664: cmp             w0, NULL
    // 0x805668: b.ne            #0x805674
    // 0x80566c: ldur            x2, [fp, #-8]
    // 0x805670: r0 = _calculateCarouselHeight()
    //     0x805670: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x805674: r0 = Null
    //     0x805674: mov             x0, NULL
    // 0x805678: LeaveFrame
    //     0x805678: mov             SP, fp
    //     0x80567c: ldp             fp, lr, [SP], #0x10
    // 0x805680: ret
    //     0x805680: ret             
    // 0x805684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805688: b               #0x8055fc
  }
  _ _initializeMediaItems(/* No info */) {
    // ** addr: 0x80568c, size: 0xd4
    // 0x80568c: EnterFrame
    //     0x80568c: stp             fp, lr, [SP, #-0x10]!
    //     0x805690: mov             fp, SP
    // 0x805694: AllocStack(0x8)
    //     0x805694: sub             SP, SP, #8
    // 0x805698: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x805698: stur            x1, [fp, #-8]
    // 0x80569c: CheckStackOverflow
    //     0x80569c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8056a0: cmp             SP, x16
    //     0x8056a4: b.ls            #0x805754
    // 0x8056a8: r1 = 1
    //     0x8056a8: movz            x1, #0x1
    // 0x8056ac: r0 = AllocateContext()
    //     0x8056ac: bl              #0x16f6108  ; AllocateContextStub
    // 0x8056b0: mov             x1, x0
    // 0x8056b4: ldur            x0, [fp, #-8]
    // 0x8056b8: StoreField: r1->field_f = r0
    //     0x8056b8: stur            w0, [x1, #0xf]
    // 0x8056bc: LoadField: r2 = r0->field_b
    //     0x8056bc: ldur            w2, [x0, #0xb]
    // 0x8056c0: DecompressPointer r2
    //     0x8056c0: add             x2, x2, HEAP, lsl #32
    // 0x8056c4: cmp             w2, NULL
    // 0x8056c8: b.eq            #0x80575c
    // 0x8056cc: LoadField: r3 = r2->field_b
    //     0x8056cc: ldur            w3, [x2, #0xb]
    // 0x8056d0: DecompressPointer r3
    //     0x8056d0: add             x3, x3, HEAP, lsl #32
    // 0x8056d4: cmp             w3, NULL
    // 0x8056d8: b.eq            #0x8056e4
    // 0x8056dc: LoadField: r2 = r3->field_b
    //     0x8056dc: ldur            w2, [x3, #0xb]
    // 0x8056e0: cbnz            w2, #0x805724
    // 0x8056e4: r1 = <WidgetEntity>
    //     0x8056e4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x8056e8: ldr             x1, [x1, #0x878]
    // 0x8056ec: r2 = 0
    //     0x8056ec: movz            x2, #0
    // 0x8056f0: r0 = _GrowableList()
    //     0x8056f0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x8056f4: ldur            x3, [fp, #-8]
    // 0x8056f8: StoreField: r3->field_23 = r0
    //     0x8056f8: stur            w0, [x3, #0x23]
    //     0x8056fc: ldurb           w16, [x3, #-1]
    //     0x805700: ldurb           w17, [x0, #-1]
    //     0x805704: and             x16, x17, x16, lsr #2
    //     0x805708: tst             x16, HEAP, lsr #32
    //     0x80570c: b.eq            #0x805714
    //     0x805710: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x805714: r0 = Null
    //     0x805714: mov             x0, NULL
    // 0x805718: LeaveFrame
    //     0x805718: mov             SP, fp
    //     0x80571c: ldp             fp, lr, [SP], #0x10
    // 0x805720: ret
    //     0x805720: ret             
    // 0x805724: mov             x3, x0
    // 0x805728: mov             x2, x1
    // 0x80572c: r1 = Function '<anonymous closure>':.
    //     0x80572c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55740] AnonymousClosure: (0x805760), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x80568c)
    //     0x805730: ldr             x1, [x1, #0x740]
    // 0x805734: r0 = AllocateClosure()
    //     0x805734: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x805738: ldur            x1, [fp, #-8]
    // 0x80573c: mov             x2, x0
    // 0x805740: r0 = setState()
    //     0x805740: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x805744: r0 = Null
    //     0x805744: mov             x0, NULL
    // 0x805748: LeaveFrame
    //     0x805748: mov             SP, fp
    //     0x80574c: ldp             fp, lr, [SP], #0x10
    // 0x805750: ret
    //     0x805750: ret             
    // 0x805754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805754: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805758: b               #0x8056a8
    // 0x80575c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80575c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x805760, size: 0x348
    // 0x805760: EnterFrame
    //     0x805760: stp             fp, lr, [SP, #-0x10]!
    //     0x805764: mov             fp, SP
    // 0x805768: AllocStack(0x50)
    //     0x805768: sub             SP, SP, #0x50
    // 0x80576c: SetupParameters()
    //     0x80576c: ldr             x0, [fp, #0x10]
    //     0x805770: ldur            w3, [x0, #0x17]
    //     0x805774: add             x3, x3, HEAP, lsl #32
    //     0x805778: stur            x3, [fp, #-0x10]
    // 0x80577c: CheckStackOverflow
    //     0x80577c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805780: cmp             SP, x16
    //     0x805784: b.ls            #0x805a88
    // 0x805788: LoadField: r0 = r3->field_f
    //     0x805788: ldur            w0, [x3, #0xf]
    // 0x80578c: DecompressPointer r0
    //     0x80578c: add             x0, x0, HEAP, lsl #32
    // 0x805790: stur            x0, [fp, #-8]
    // 0x805794: LoadField: r1 = r0->field_b
    //     0x805794: ldur            w1, [x0, #0xb]
    // 0x805798: DecompressPointer r1
    //     0x805798: add             x1, x1, HEAP, lsl #32
    // 0x80579c: cmp             w1, NULL
    // 0x8057a0: b.eq            #0x805a90
    // 0x8057a4: LoadField: r2 = r1->field_b
    //     0x8057a4: ldur            w2, [x1, #0xb]
    // 0x8057a8: DecompressPointer r2
    //     0x8057a8: add             x2, x2, HEAP, lsl #32
    // 0x8057ac: cmp             w2, NULL
    // 0x8057b0: b.eq            #0x805a94
    // 0x8057b4: r1 = <WidgetEntity>
    //     0x8057b4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x8057b8: ldr             x1, [x1, #0x878]
    // 0x8057bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8057bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8057c0: r0 = List.from()
    //     0x8057c0: bl              #0x6cdc40  ; [dart:core] List::List.from
    // 0x8057c4: ldur            x1, [fp, #-8]
    // 0x8057c8: StoreField: r1->field_23 = r0
    //     0x8057c8: stur            w0, [x1, #0x23]
    //     0x8057cc: ldurb           w16, [x1, #-1]
    //     0x8057d0: ldurb           w17, [x0, #-1]
    //     0x8057d4: and             x16, x17, x16, lsr #2
    //     0x8057d8: tst             x16, HEAP, lsr #32
    //     0x8057dc: b.eq            #0x8057e4
    //     0x8057e0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8057e4: ldur            x0, [fp, #-0x10]
    // 0x8057e8: LoadField: r1 = r0->field_f
    //     0x8057e8: ldur            w1, [x0, #0xf]
    // 0x8057ec: DecompressPointer r1
    //     0x8057ec: add             x1, x1, HEAP, lsl #32
    // 0x8057f0: StoreField: r1->field_1b = rZR
    //     0x8057f0: stur            xzr, [x1, #0x1b]
    // 0x8057f4: StoreField: r1->field_2f = rNULL
    //     0x8057f4: stur            NULL, [x1, #0x2f]
    // 0x8057f8: LoadField: r3 = r1->field_23
    //     0x8057f8: ldur            w3, [x1, #0x23]
    // 0x8057fc: DecompressPointer r3
    //     0x8057fc: add             x3, x3, HEAP, lsl #32
    // 0x805800: stur            x3, [fp, #-8]
    // 0x805804: r1 = Function '<anonymous closure>':.
    //     0x805804: add             x1, PP, #0x55, lsl #12  ; [pp+0x55748] AnonymousClosure: (0x802880), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x802c20)
    //     0x805808: ldr             x1, [x1, #0x748]
    // 0x80580c: r2 = Null
    //     0x80580c: mov             x2, NULL
    // 0x805810: r0 = AllocateClosure()
    //     0x805810: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x805814: ldur            x1, [fp, #-8]
    // 0x805818: mov             x2, x0
    // 0x80581c: r0 = where()
    //     0x80581c: bl              #0x7d9800  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x805820: mov             x1, x0
    // 0x805824: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x805824: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x805828: r0 = toList()
    //     0x805828: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x80582c: mov             x3, x0
    // 0x805830: stur            x3, [fp, #-0x38]
    // 0x805834: LoadField: r4 = r3->field_7
    //     0x805834: ldur            w4, [x3, #7]
    // 0x805838: DecompressPointer r4
    //     0x805838: add             x4, x4, HEAP, lsl #32
    // 0x80583c: stur            x4, [fp, #-0x30]
    // 0x805840: LoadField: r0 = r3->field_b
    //     0x805840: ldur            w0, [x3, #0xb]
    // 0x805844: r5 = LoadInt32Instr(r0)
    //     0x805844: sbfx            x5, x0, #1, #0x1f
    // 0x805848: stur            x5, [fp, #-0x28]
    // 0x80584c: r7 = 1
    //     0x80584c: movz            x7, #0x1
    // 0x805850: r0 = 0
    //     0x805850: movz            x0, #0
    // 0x805854: ldur            x6, [fp, #-0x10]
    // 0x805858: stur            x7, [fp, #-0x20]
    // 0x80585c: CheckStackOverflow
    //     0x80585c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805860: cmp             SP, x16
    //     0x805864: b.ls            #0x805a98
    // 0x805868: LoadField: r1 = r3->field_b
    //     0x805868: ldur            w1, [x3, #0xb]
    // 0x80586c: r2 = LoadInt32Instr(r1)
    //     0x80586c: sbfx            x2, x1, #1, #0x1f
    // 0x805870: cmp             x5, x2
    // 0x805874: b.ne            #0x805a68
    // 0x805878: cmp             x0, x2
    // 0x80587c: b.ge            #0x805a58
    // 0x805880: LoadField: r1 = r3->field_f
    //     0x805880: ldur            w1, [x3, #0xf]
    // 0x805884: DecompressPointer r1
    //     0x805884: add             x1, x1, HEAP, lsl #32
    // 0x805888: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x805888: add             x16, x1, x0, lsl #2
    //     0x80588c: ldur            w8, [x16, #0xf]
    // 0x805890: DecompressPointer r8
    //     0x805890: add             x8, x8, HEAP, lsl #32
    // 0x805894: stur            x8, [fp, #-8]
    // 0x805898: add             x9, x0, #1
    // 0x80589c: stur            x9, [fp, #-0x18]
    // 0x8058a0: cmp             w8, NULL
    // 0x8058a4: b.ne            #0x8058d8
    // 0x8058a8: mov             x0, x8
    // 0x8058ac: mov             x2, x4
    // 0x8058b0: r1 = Null
    //     0x8058b0: mov             x1, NULL
    // 0x8058b4: cmp             w2, NULL
    // 0x8058b8: b.eq            #0x8058d8
    // 0x8058bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8058bc: ldur            w4, [x2, #0x17]
    // 0x8058c0: DecompressPointer r4
    //     0x8058c0: add             x4, x4, HEAP, lsl #32
    // 0x8058c4: r8 = X0
    //     0x8058c4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8058c8: LoadField: r9 = r4->field_7
    //     0x8058c8: ldur            x9, [x4, #7]
    // 0x8058cc: r3 = Null
    //     0x8058cc: add             x3, PP, #0x55, lsl #12  ; [pp+0x55750] Null
    //     0x8058d0: ldr             x3, [x3, #0x750]
    // 0x8058d4: blr             x9
    // 0x8058d8: ldur            x0, [fp, #-0x10]
    // 0x8058dc: LoadField: r1 = r0->field_f
    //     0x8058dc: ldur            w1, [x0, #0xf]
    // 0x8058e0: DecompressPointer r1
    //     0x8058e0: add             x1, x1, HEAP, lsl #32
    // 0x8058e4: LoadField: r2 = r1->field_23
    //     0x8058e4: ldur            w2, [x1, #0x23]
    // 0x8058e8: DecompressPointer r2
    //     0x8058e8: add             x2, x2, HEAP, lsl #32
    // 0x8058ec: LoadField: r1 = r2->field_b
    //     0x8058ec: ldur            w1, [x2, #0xb]
    // 0x8058f0: r3 = LoadInt32Instr(r1)
    //     0x8058f0: sbfx            x3, x1, #1, #0x1f
    // 0x8058f4: LoadField: r1 = r2->field_f
    //     0x8058f4: ldur            w1, [x2, #0xf]
    // 0x8058f8: DecompressPointer r1
    //     0x8058f8: add             x1, x1, HEAP, lsl #32
    // 0x8058fc: ldur            x4, [fp, #-8]
    // 0x805900: r5 = 0
    //     0x805900: movz            x5, #0
    // 0x805904: CheckStackOverflow
    //     0x805904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805908: cmp             SP, x16
    //     0x80590c: b.ls            #0x805aa0
    // 0x805910: cmp             x5, x3
    // 0x805914: b.ge            #0x805944
    // 0x805918: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x805918: add             x16, x1, x5, lsl #2
    //     0x80591c: ldur            w6, [x16, #0xf]
    // 0x805920: DecompressPointer r6
    //     0x805920: add             x6, x6, HEAP, lsl #32
    // 0x805924: cmp             w6, w4
    // 0x805928: b.eq            #0x805938
    // 0x80592c: add             x6, x5, #1
    // 0x805930: mov             x5, x6
    // 0x805934: b               #0x805904
    // 0x805938: mov             x1, x2
    // 0x80593c: mov             x2, x5
    // 0x805940: r0 = removeAt()
    //     0x805940: bl              #0x7145c0  ; [dart:core] _GrowableList::removeAt
    // 0x805944: ldur            x0, [fp, #-0x10]
    // 0x805948: ldur            x4, [fp, #-0x20]
    // 0x80594c: LoadField: r1 = r0->field_f
    //     0x80594c: ldur            w1, [x0, #0xf]
    // 0x805950: DecompressPointer r1
    //     0x805950: add             x1, x1, HEAP, lsl #32
    // 0x805954: LoadField: r3 = r1->field_23
    //     0x805954: ldur            w3, [x1, #0x23]
    // 0x805958: DecompressPointer r3
    //     0x805958: add             x3, x3, HEAP, lsl #32
    // 0x80595c: stur            x3, [fp, #-0x48]
    // 0x805960: LoadField: r5 = r3->field_b
    //     0x805960: ldur            w5, [x3, #0xb]
    // 0x805964: stur            x5, [fp, #-0x40]
    // 0x805968: r1 = LoadInt32Instr(r5)
    //     0x805968: sbfx            x1, x5, #1, #0x1f
    // 0x80596c: cmp             x4, x1
    // 0x805970: b.ge            #0x805994
    // 0x805974: mov             x1, x3
    // 0x805978: mov             x2, x4
    // 0x80597c: ldur            x3, [fp, #-8]
    // 0x805980: r0 = insert()
    //     0x805980: bl              #0x697f94  ; [dart:core] _GrowableList::insert
    // 0x805984: ldur            x4, [fp, #-0x20]
    // 0x805988: add             x1, x4, #2
    // 0x80598c: mov             x7, x1
    // 0x805990: b               #0x805a44
    // 0x805994: LoadField: r2 = r3->field_7
    //     0x805994: ldur            w2, [x3, #7]
    // 0x805998: DecompressPointer r2
    //     0x805998: add             x2, x2, HEAP, lsl #32
    // 0x80599c: ldur            x0, [fp, #-8]
    // 0x8059a0: r1 = Null
    //     0x8059a0: mov             x1, NULL
    // 0x8059a4: cmp             w2, NULL
    // 0x8059a8: b.eq            #0x8059c8
    // 0x8059ac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8059ac: ldur            w4, [x2, #0x17]
    // 0x8059b0: DecompressPointer r4
    //     0x8059b0: add             x4, x4, HEAP, lsl #32
    // 0x8059b4: r8 = X0
    //     0x8059b4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8059b8: LoadField: r9 = r4->field_7
    //     0x8059b8: ldur            x9, [x4, #7]
    // 0x8059bc: r3 = Null
    //     0x8059bc: add             x3, PP, #0x55, lsl #12  ; [pp+0x55760] Null
    //     0x8059c0: ldr             x3, [x3, #0x760]
    // 0x8059c4: blr             x9
    // 0x8059c8: ldur            x0, [fp, #-0x48]
    // 0x8059cc: LoadField: r1 = r0->field_f
    //     0x8059cc: ldur            w1, [x0, #0xf]
    // 0x8059d0: DecompressPointer r1
    //     0x8059d0: add             x1, x1, HEAP, lsl #32
    // 0x8059d4: LoadField: r2 = r1->field_b
    //     0x8059d4: ldur            w2, [x1, #0xb]
    // 0x8059d8: ldur            x1, [fp, #-0x40]
    // 0x8059dc: r3 = LoadInt32Instr(r1)
    //     0x8059dc: sbfx            x3, x1, #1, #0x1f
    // 0x8059e0: stur            x3, [fp, #-0x50]
    // 0x8059e4: r1 = LoadInt32Instr(r2)
    //     0x8059e4: sbfx            x1, x2, #1, #0x1f
    // 0x8059e8: cmp             x3, x1
    // 0x8059ec: b.ne            #0x8059f8
    // 0x8059f0: mov             x1, x0
    // 0x8059f4: r0 = _growToNextCapacity()
    //     0x8059f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8059f8: ldur            x0, [fp, #-0x48]
    // 0x8059fc: ldur            x2, [fp, #-0x50]
    // 0x805a00: add             x1, x2, #1
    // 0x805a04: lsl             x3, x1, #1
    // 0x805a08: StoreField: r0->field_b = r3
    //     0x805a08: stur            w3, [x0, #0xb]
    // 0x805a0c: LoadField: r1 = r0->field_f
    //     0x805a0c: ldur            w1, [x0, #0xf]
    // 0x805a10: DecompressPointer r1
    //     0x805a10: add             x1, x1, HEAP, lsl #32
    // 0x805a14: ldur            x0, [fp, #-8]
    // 0x805a18: ArrayStore: r1[r2] = r0  ; List_4
    //     0x805a18: add             x25, x1, x2, lsl #2
    //     0x805a1c: add             x25, x25, #0xf
    //     0x805a20: str             w0, [x25]
    //     0x805a24: tbz             w0, #0, #0x805a40
    //     0x805a28: ldurb           w16, [x1, #-1]
    //     0x805a2c: ldurb           w17, [x0, #-1]
    //     0x805a30: and             x16, x17, x16, lsr #2
    //     0x805a34: tst             x16, HEAP, lsr #32
    //     0x805a38: b.eq            #0x805a40
    //     0x805a3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x805a40: ldur            x7, [fp, #-0x20]
    // 0x805a44: ldur            x0, [fp, #-0x18]
    // 0x805a48: ldur            x3, [fp, #-0x38]
    // 0x805a4c: ldur            x4, [fp, #-0x30]
    // 0x805a50: ldur            x5, [fp, #-0x28]
    // 0x805a54: b               #0x805854
    // 0x805a58: r0 = Null
    //     0x805a58: mov             x0, NULL
    // 0x805a5c: LeaveFrame
    //     0x805a5c: mov             SP, fp
    //     0x805a60: ldp             fp, lr, [SP], #0x10
    // 0x805a64: ret
    //     0x805a64: ret             
    // 0x805a68: mov             x0, x3
    // 0x805a6c: r0 = ConcurrentModificationError()
    //     0x805a6c: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x805a70: mov             x1, x0
    // 0x805a74: ldur            x0, [fp, #-0x38]
    // 0x805a78: StoreField: r1->field_b = r0
    //     0x805a78: stur            w0, [x1, #0xb]
    // 0x805a7c: mov             x0, x1
    // 0x805a80: r0 = Throw()
    //     0x805a80: bl              #0x16f5420  ; ThrowStub
    // 0x805a84: brk             #0
    // 0x805a88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805a88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805a8c: b               #0x805788
    // 0x805a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x805a90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x805a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x805a94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x805a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805a98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805a9c: b               #0x805868
    // 0x805aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805aa0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805aa4: b               #0x805910
  }
  _ initState(/* No info */) {
    // ** addr: 0x944014, size: 0x50
    // 0x944014: EnterFrame
    //     0x944014: stp             fp, lr, [SP, #-0x10]!
    //     0x944018: mov             fp, SP
    // 0x94401c: AllocStack(0x8)
    //     0x94401c: sub             SP, SP, #8
    // 0x944020: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x944020: mov             x0, x1
    //     0x944024: stur            x1, [fp, #-8]
    // 0x944028: CheckStackOverflow
    //     0x944028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94402c: cmp             SP, x16
    //     0x944030: b.ls            #0x94405c
    // 0x944034: mov             x1, x0
    // 0x944038: r0 = initState()
    //     0x944038: bl              #0x944064  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::initState
    // 0x94403c: ldur            x1, [fp, #-8]
    // 0x944040: r0 = _initializeMediaItems()
    //     0x944040: bl              #0x80568c  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x944044: ldur            x1, [fp, #-8]
    // 0x944048: r0 = _preloadImageSizes()
    //     0x944048: bl              #0x8051dc  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x94404c: r0 = Null
    //     0x94404c: mov             x0, NULL
    // 0x944050: LeaveFrame
    //     0x944050: mov             SP, fp
    //     0x944054: ldp             fp, lr, [SP], #0x10
    // 0x944058: ret
    //     0x944058: ret             
    // 0x94405c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94405c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944060: b               #0x944034
  }
  [closure] Text <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa81aec, size: 0x100
    // 0xa81aec: EnterFrame
    //     0xa81aec: stp             fp, lr, [SP, #-0x10]!
    //     0xa81af0: mov             fp, SP
    // 0xa81af4: AllocStack(0x18)
    //     0xa81af4: sub             SP, SP, #0x18
    // 0xa81af8: SetupParameters()
    //     0xa81af8: ldr             x0, [fp, #0x20]
    //     0xa81afc: ldur            w3, [x0, #0x17]
    //     0xa81b00: add             x3, x3, HEAP, lsl #32
    //     0xa81b04: stur            x3, [fp, #-8]
    // 0xa81b08: CheckStackOverflow
    //     0xa81b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa81b0c: cmp             SP, x16
    //     0xa81b10: b.ls            #0xa81be0
    // 0xa81b14: r1 = Null
    //     0xa81b14: mov             x1, NULL
    // 0xa81b18: r2 = 4
    //     0xa81b18: movz            x2, #0x4
    // 0xa81b1c: r0 = AllocateArray()
    //     0xa81b1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa81b20: mov             x2, x0
    // 0xa81b24: r16 = "• "
    //     0xa81b24: add             x16, PP, #0x52, lsl #12  ; [pp+0x52930] "• "
    //     0xa81b28: ldr             x16, [x16, #0x930]
    // 0xa81b2c: StoreField: r2->field_f = r16
    //     0xa81b2c: stur            w16, [x2, #0xf]
    // 0xa81b30: ldur            x0, [fp, #-8]
    // 0xa81b34: LoadField: r3 = r0->field_f
    //     0xa81b34: ldur            w3, [x0, #0xf]
    // 0xa81b38: DecompressPointer r3
    //     0xa81b38: add             x3, x3, HEAP, lsl #32
    // 0xa81b3c: LoadField: r0 = r3->field_b
    //     0xa81b3c: ldur            w0, [x3, #0xb]
    // 0xa81b40: ldr             x1, [fp, #0x10]
    // 0xa81b44: r4 = LoadInt32Instr(r1)
    //     0xa81b44: sbfx            x4, x1, #1, #0x1f
    //     0xa81b48: tbz             w1, #0, #0xa81b50
    //     0xa81b4c: ldur            x4, [x1, #7]
    // 0xa81b50: r1 = LoadInt32Instr(r0)
    //     0xa81b50: sbfx            x1, x0, #1, #0x1f
    // 0xa81b54: mov             x0, x1
    // 0xa81b58: mov             x1, x4
    // 0xa81b5c: cmp             x1, x0
    // 0xa81b60: b.hs            #0xa81be8
    // 0xa81b64: LoadField: r0 = r3->field_f
    //     0xa81b64: ldur            w0, [x3, #0xf]
    // 0xa81b68: DecompressPointer r0
    //     0xa81b68: add             x0, x0, HEAP, lsl #32
    // 0xa81b6c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa81b6c: add             x16, x0, x4, lsl #2
    //     0xa81b70: ldur            w1, [x16, #0xf]
    // 0xa81b74: DecompressPointer r1
    //     0xa81b74: add             x1, x1, HEAP, lsl #32
    // 0xa81b78: StoreField: r2->field_13 = r1
    //     0xa81b78: stur            w1, [x2, #0x13]
    // 0xa81b7c: str             x2, [SP]
    // 0xa81b80: r0 = _interpolate()
    //     0xa81b80: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa81b84: ldr             x1, [fp, #0x18]
    // 0xa81b88: stur            x0, [fp, #-8]
    // 0xa81b8c: r0 = of()
    //     0xa81b8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa81b90: LoadField: r1 = r0->field_87
    //     0xa81b90: ldur            w1, [x0, #0x87]
    // 0xa81b94: DecompressPointer r1
    //     0xa81b94: add             x1, x1, HEAP, lsl #32
    // 0xa81b98: LoadField: r0 = r1->field_2b
    //     0xa81b98: ldur            w0, [x1, #0x2b]
    // 0xa81b9c: DecompressPointer r0
    //     0xa81b9c: add             x0, x0, HEAP, lsl #32
    // 0xa81ba0: r16 = 12.000000
    //     0xa81ba0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa81ba4: ldr             x16, [x16, #0x9e8]
    // 0xa81ba8: str             x16, [SP]
    // 0xa81bac: mov             x1, x0
    // 0xa81bb0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xa81bb0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xa81bb4: ldr             x4, [x4, #0x798]
    // 0xa81bb8: r0 = copyWith()
    //     0xa81bb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa81bbc: stur            x0, [fp, #-0x10]
    // 0xa81bc0: r0 = Text()
    //     0xa81bc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa81bc4: ldur            x1, [fp, #-8]
    // 0xa81bc8: StoreField: r0->field_b = r1
    //     0xa81bc8: stur            w1, [x0, #0xb]
    // 0xa81bcc: ldur            x1, [fp, #-0x10]
    // 0xa81bd0: StoreField: r0->field_13 = r1
    //     0xa81bd0: stur            w1, [x0, #0x13]
    // 0xa81bd4: LeaveFrame
    //     0xa81bd4: mov             SP, fp
    //     0xa81bd8: ldp             fp, lr, [SP], #0x10
    // 0xa81bdc: ret
    //     0xa81bdc: ret             
    // 0xa81be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa81be0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa81be4: b               #0xa81b14
    // 0xa81be8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa81be8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildRulesList(/* No info */) {
    // ** addr: 0xa81bec, size: 0x180
    // 0xa81bec: EnterFrame
    //     0xa81bec: stp             fp, lr, [SP, #-0x10]!
    //     0xa81bf0: mov             fp, SP
    // 0xa81bf4: AllocStack(0x30)
    //     0xa81bf4: sub             SP, SP, #0x30
    // 0xa81bf8: SetupParameters(_ProductMediaCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa81bf8: mov             x0, x1
    //     0xa81bfc: mov             x1, x2
    //     0xa81c00: stur            x2, [fp, #-0x10]
    // 0xa81c04: CheckStackOverflow
    //     0xa81c04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa81c08: cmp             SP, x16
    //     0xa81c0c: b.ls            #0xa81d50
    // 0xa81c10: cmp             w3, NULL
    // 0xa81c14: b.eq            #0xa81d58
    // 0xa81c18: LoadField: r0 = r3->field_7
    //     0xa81c18: ldur            w0, [x3, #7]
    // 0xa81c1c: DecompressPointer r0
    //     0xa81c1c: add             x0, x0, HEAP, lsl #32
    // 0xa81c20: stur            x0, [fp, #-8]
    // 0xa81c24: r1 = 1
    //     0xa81c24: movz            x1, #0x1
    // 0xa81c28: r0 = AllocateContext()
    //     0xa81c28: bl              #0x16f6108  ; AllocateContextStub
    // 0xa81c2c: mov             x2, x0
    // 0xa81c30: ldur            x0, [fp, #-8]
    // 0xa81c34: stur            x2, [fp, #-0x18]
    // 0xa81c38: StoreField: r2->field_f = r0
    //     0xa81c38: stur            w0, [x2, #0xf]
    // 0xa81c3c: LoadField: r1 = r0->field_b
    //     0xa81c3c: ldur            w1, [x0, #0xb]
    // 0xa81c40: cbnz            w1, #0xa81c54
    // 0xa81c44: r0 = Instance_SizedBox
    //     0xa81c44: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa81c48: LeaveFrame
    //     0xa81c48: mov             SP, fp
    //     0xa81c4c: ldp             fp, lr, [SP], #0x10
    // 0xa81c50: ret
    //     0xa81c50: ret             
    // 0xa81c54: cmp             w1, #2
    // 0xa81c58: b.ne            #0xa81c84
    // 0xa81c5c: ldur            x1, [fp, #-0x10]
    // 0xa81c60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa81c60: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa81c64: r0 = _of()
    //     0xa81c64: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa81c68: LoadField: r1 = r0->field_7
    //     0xa81c68: ldur            w1, [x0, #7]
    // 0xa81c6c: DecompressPointer r1
    //     0xa81c6c: add             x1, x1, HEAP, lsl #32
    // 0xa81c70: LoadField: d0 = r1->field_f
    //     0xa81c70: ldur            d0, [x1, #0xf]
    // 0xa81c74: d1 = 0.040000
    //     0xa81c74: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0xa81c78: fmul            d2, d0, d1
    // 0xa81c7c: mov             v0.16b, v2.16b
    // 0xa81c80: b               #0xa81cac
    // 0xa81c84: ldur            x1, [fp, #-0x10]
    // 0xa81c88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa81c88: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa81c8c: r0 = _of()
    //     0xa81c8c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa81c90: LoadField: r1 = r0->field_7
    //     0xa81c90: ldur            w1, [x0, #7]
    // 0xa81c94: DecompressPointer r1
    //     0xa81c94: add             x1, x1, HEAP, lsl #32
    // 0xa81c98: LoadField: d0 = r1->field_f
    //     0xa81c98: ldur            d0, [x1, #0xf]
    // 0xa81c9c: d1 = 0.090000
    //     0xa81c9c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0xa81ca0: ldr             d1, [x17, #0x920]
    // 0xa81ca4: fmul            d2, d0, d1
    // 0xa81ca8: mov             v0.16b, v2.16b
    // 0xa81cac: ldur            x0, [fp, #-8]
    // 0xa81cb0: stur            d0, [fp, #-0x20]
    // 0xa81cb4: LoadField: r3 = r0->field_b
    //     0xa81cb4: ldur            w3, [x0, #0xb]
    // 0xa81cb8: ldur            x2, [fp, #-0x18]
    // 0xa81cbc: stur            x3, [fp, #-0x10]
    // 0xa81cc0: r1 = Function '<anonymous closure>':.
    //     0xa81cc0: add             x1, PP, #0x55, lsl #12  ; [pp+0x556a0] AnonymousClosure: (0xa81aec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList (0xa81bec)
    //     0xa81cc4: ldr             x1, [x1, #0x6a0]
    // 0xa81cc8: r0 = AllocateClosure()
    //     0xa81cc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa81ccc: stur            x0, [fp, #-8]
    // 0xa81cd0: r0 = ListView()
    //     0xa81cd0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa81cd4: stur            x0, [fp, #-0x18]
    // 0xa81cd8: r16 = true
    //     0xa81cd8: add             x16, NULL, #0x20  ; true
    // 0xa81cdc: r30 = Instance_NeverScrollableScrollPhysics
    //     0xa81cdc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xa81ce0: ldr             lr, [lr, #0x1c8]
    // 0xa81ce4: stp             lr, x16, [SP]
    // 0xa81ce8: mov             x1, x0
    // 0xa81cec: ldur            x2, [fp, #-8]
    // 0xa81cf0: ldur            x3, [fp, #-0x10]
    // 0xa81cf4: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xa81cf4: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xa81cf8: ldr             x4, [x4, #8]
    // 0xa81cfc: r0 = ListView.builder()
    //     0xa81cfc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xa81d00: ldur            d0, [fp, #-0x20]
    // 0xa81d04: r0 = inline_Allocate_Double()
    //     0xa81d04: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa81d08: add             x0, x0, #0x10
    //     0xa81d0c: cmp             x1, x0
    //     0xa81d10: b.ls            #0xa81d5c
    //     0xa81d14: str             x0, [THR, #0x50]  ; THR::top
    //     0xa81d18: sub             x0, x0, #0xf
    //     0xa81d1c: movz            x1, #0xe15c
    //     0xa81d20: movk            x1, #0x3, lsl #16
    //     0xa81d24: stur            x1, [x0, #-1]
    // 0xa81d28: StoreField: r0->field_7 = d0
    //     0xa81d28: stur            d0, [x0, #7]
    // 0xa81d2c: stur            x0, [fp, #-8]
    // 0xa81d30: r0 = SizedBox()
    //     0xa81d30: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa81d34: ldur            x1, [fp, #-8]
    // 0xa81d38: StoreField: r0->field_13 = r1
    //     0xa81d38: stur            w1, [x0, #0x13]
    // 0xa81d3c: ldur            x1, [fp, #-0x18]
    // 0xa81d40: StoreField: r0->field_b = r1
    //     0xa81d40: stur            w1, [x0, #0xb]
    // 0xa81d44: LeaveFrame
    //     0xa81d44: mov             SP, fp
    //     0xa81d48: ldp             fp, lr, [SP], #0x10
    // 0xa81d4c: ret
    //     0xa81d4c: ret             
    // 0xa81d50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa81d50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa81d54: b               #0xa81c10
    // 0xa81d58: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa81d58: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa81d5c: SaveReg d0
    //     0xa81d5c: str             q0, [SP, #-0x10]!
    // 0xa81d60: r0 = AllocateDouble()
    //     0xa81d60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa81d64: RestoreReg d0
    //     0xa81d64: ldr             q0, [SP], #0x10
    // 0xa81d68: b               #0xa81d28
  }
  _ build(/* No info */) {
    // ** addr: 0xb85068, size: 0x124
    // 0xb85068: EnterFrame
    //     0xb85068: stp             fp, lr, [SP, #-0x10]!
    //     0xb8506c: mov             fp, SP
    // 0xb85070: AllocStack(0x18)
    //     0xb85070: sub             SP, SP, #0x18
    // 0xb85074: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb85074: mov             x3, x1
    //     0xb85078: mov             x0, x2
    //     0xb8507c: stur            x1, [fp, #-8]
    //     0xb85080: stur            x2, [fp, #-0x10]
    // 0xb85084: CheckStackOverflow
    //     0xb85084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85088: cmp             SP, x16
    //     0xb8508c: b.ls            #0xb85184
    // 0xb85090: mov             x1, x3
    // 0xb85094: mov             x2, x0
    // 0xb85098: r0 = build()
    //     0xb85098: bl              #0xb86ccc  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::build
    // 0xb8509c: ldur            x0, [fp, #-8]
    // 0xb850a0: LoadField: r1 = r0->field_23
    //     0xb850a0: ldur            w1, [x0, #0x23]
    // 0xb850a4: DecompressPointer r1
    //     0xb850a4: add             x1, x1, HEAP, lsl #32
    // 0xb850a8: LoadField: r2 = r1->field_b
    //     0xb850a8: ldur            w2, [x1, #0xb]
    // 0xb850ac: cbnz            w2, #0xb850c0
    // 0xb850b0: r0 = Instance_SizedBox
    //     0xb850b0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb850b4: LeaveFrame
    //     0xb850b4: mov             SP, fp
    //     0xb850b8: ldp             fp, lr, [SP], #0x10
    // 0xb850bc: ret
    //     0xb850bc: ret             
    // 0xb850c0: mov             x1, x0
    // 0xb850c4: ldur            x2, [fp, #-0x10]
    // 0xb850c8: r0 = _buildCarouselSection()
    //     0xb850c8: bl              #0xb8518c  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCarouselSection
    // 0xb850cc: ldur            x1, [fp, #-8]
    // 0xb850d0: ldur            x2, [fp, #-0x10]
    // 0xb850d4: stur            x0, [fp, #-8]
    // 0xb850d8: r0 = _buildIndicator()
    //     0xb850d8: bl              #0xb0c240  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildIndicator
    // 0xb850dc: r1 = Null
    //     0xb850dc: mov             x1, NULL
    // 0xb850e0: r2 = 4
    //     0xb850e0: movz            x2, #0x4
    // 0xb850e4: stur            x0, [fp, #-0x10]
    // 0xb850e8: r0 = AllocateArray()
    //     0xb850e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb850ec: mov             x2, x0
    // 0xb850f0: ldur            x0, [fp, #-8]
    // 0xb850f4: stur            x2, [fp, #-0x18]
    // 0xb850f8: StoreField: r2->field_f = r0
    //     0xb850f8: stur            w0, [x2, #0xf]
    // 0xb850fc: ldur            x0, [fp, #-0x10]
    // 0xb85100: StoreField: r2->field_13 = r0
    //     0xb85100: stur            w0, [x2, #0x13]
    // 0xb85104: r1 = <Widget>
    //     0xb85104: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb85108: r0 = AllocateGrowableArray()
    //     0xb85108: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8510c: mov             x1, x0
    // 0xb85110: ldur            x0, [fp, #-0x18]
    // 0xb85114: stur            x1, [fp, #-8]
    // 0xb85118: StoreField: r1->field_f = r0
    //     0xb85118: stur            w0, [x1, #0xf]
    // 0xb8511c: r0 = 4
    //     0xb8511c: movz            x0, #0x4
    // 0xb85120: StoreField: r1->field_b = r0
    //     0xb85120: stur            w0, [x1, #0xb]
    // 0xb85124: r0 = Column()
    //     0xb85124: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb85128: r1 = Instance_Axis
    //     0xb85128: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8512c: StoreField: r0->field_f = r1
    //     0xb8512c: stur            w1, [x0, #0xf]
    // 0xb85130: r1 = Instance_MainAxisAlignment
    //     0xb85130: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb85134: ldr             x1, [x1, #0xa08]
    // 0xb85138: StoreField: r0->field_13 = r1
    //     0xb85138: stur            w1, [x0, #0x13]
    // 0xb8513c: r1 = Instance_MainAxisSize
    //     0xb8513c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb85140: ldr             x1, [x1, #0xa10]
    // 0xb85144: ArrayStore: r0[0] = r1  ; List_4
    //     0xb85144: stur            w1, [x0, #0x17]
    // 0xb85148: r1 = Instance_CrossAxisAlignment
    //     0xb85148: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8514c: ldr             x1, [x1, #0xa18]
    // 0xb85150: StoreField: r0->field_1b = r1
    //     0xb85150: stur            w1, [x0, #0x1b]
    // 0xb85154: r1 = Instance_VerticalDirection
    //     0xb85154: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb85158: ldr             x1, [x1, #0xa20]
    // 0xb8515c: StoreField: r0->field_23 = r1
    //     0xb8515c: stur            w1, [x0, #0x23]
    // 0xb85160: r1 = Instance_Clip
    //     0xb85160: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb85164: ldr             x1, [x1, #0x38]
    // 0xb85168: StoreField: r0->field_2b = r1
    //     0xb85168: stur            w1, [x0, #0x2b]
    // 0xb8516c: StoreField: r0->field_2f = rZR
    //     0xb8516c: stur            xzr, [x0, #0x2f]
    // 0xb85170: ldur            x1, [fp, #-8]
    // 0xb85174: StoreField: r0->field_b = r1
    //     0xb85174: stur            w1, [x0, #0xb]
    // 0xb85178: LeaveFrame
    //     0xb85178: mov             SP, fp
    //     0xb8517c: ldp             fp, lr, [SP], #0x10
    // 0xb85180: ret
    //     0xb85180: ret             
    // 0xb85184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85184: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85188: b               #0xb85090
  }
  _ _buildCarouselSection(/* No info */) {
    // ** addr: 0xb8518c, size: 0x16c
    // 0xb8518c: EnterFrame
    //     0xb8518c: stp             fp, lr, [SP, #-0x10]!
    //     0xb85190: mov             fp, SP
    // 0xb85194: AllocStack(0x28)
    //     0xb85194: sub             SP, SP, #0x28
    // 0xb85198: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb85198: mov             x3, x1
    //     0xb8519c: mov             x0, x2
    //     0xb851a0: stur            x1, [fp, #-8]
    //     0xb851a4: stur            x2, [fp, #-0x10]
    // 0xb851a8: CheckStackOverflow
    //     0xb851a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb851ac: cmp             SP, x16
    //     0xb851b0: b.ls            #0xb852ec
    // 0xb851b4: mov             x1, x3
    // 0xb851b8: mov             x2, x0
    // 0xb851bc: r0 = _buildPageView()
    //     0xb851bc: bl              #0xb86190  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView
    // 0xb851c0: r1 = Null
    //     0xb851c0: mov             x1, NULL
    // 0xb851c4: r2 = 2
    //     0xb851c4: movz            x2, #0x2
    // 0xb851c8: stur            x0, [fp, #-0x18]
    // 0xb851cc: r0 = AllocateArray()
    //     0xb851cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb851d0: mov             x2, x0
    // 0xb851d4: ldur            x0, [fp, #-0x18]
    // 0xb851d8: stur            x2, [fp, #-0x20]
    // 0xb851dc: StoreField: r2->field_f = r0
    //     0xb851dc: stur            w0, [x2, #0xf]
    // 0xb851e0: r1 = <Widget>
    //     0xb851e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb851e4: r0 = AllocateGrowableArray()
    //     0xb851e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb851e8: mov             x3, x0
    // 0xb851ec: ldur            x0, [fp, #-0x20]
    // 0xb851f0: stur            x3, [fp, #-0x18]
    // 0xb851f4: StoreField: r3->field_f = r0
    //     0xb851f4: stur            w0, [x3, #0xf]
    // 0xb851f8: r0 = 2
    //     0xb851f8: movz            x0, #0x2
    // 0xb851fc: StoreField: r3->field_b = r0
    //     0xb851fc: stur            w0, [x3, #0xb]
    // 0xb85200: ldur            x1, [fp, #-8]
    // 0xb85204: LoadField: r0 = r1->field_b
    //     0xb85204: ldur            w0, [x1, #0xb]
    // 0xb85208: DecompressPointer r0
    //     0xb85208: add             x0, x0, HEAP, lsl #32
    // 0xb8520c: cmp             w0, NULL
    // 0xb85210: b.eq            #0xb852f4
    // 0xb85214: LoadField: r2 = r0->field_13
    //     0xb85214: ldur            w2, [x0, #0x13]
    // 0xb85218: DecompressPointer r2
    //     0xb85218: add             x2, x2, HEAP, lsl #32
    // 0xb8521c: tbnz            w2, #4, #0xb852ac
    // 0xb85220: ldur            x2, [fp, #-0x10]
    // 0xb85224: r0 = _buildFreeGiftButton()
    //     0xb85224: bl              #0xb852f8  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftButton
    // 0xb85228: mov             x2, x0
    // 0xb8522c: ldur            x0, [fp, #-0x18]
    // 0xb85230: stur            x2, [fp, #-8]
    // 0xb85234: LoadField: r1 = r0->field_b
    //     0xb85234: ldur            w1, [x0, #0xb]
    // 0xb85238: LoadField: r3 = r0->field_f
    //     0xb85238: ldur            w3, [x0, #0xf]
    // 0xb8523c: DecompressPointer r3
    //     0xb8523c: add             x3, x3, HEAP, lsl #32
    // 0xb85240: LoadField: r4 = r3->field_b
    //     0xb85240: ldur            w4, [x3, #0xb]
    // 0xb85244: r3 = LoadInt32Instr(r1)
    //     0xb85244: sbfx            x3, x1, #1, #0x1f
    // 0xb85248: stur            x3, [fp, #-0x28]
    // 0xb8524c: r1 = LoadInt32Instr(r4)
    //     0xb8524c: sbfx            x1, x4, #1, #0x1f
    // 0xb85250: cmp             x3, x1
    // 0xb85254: b.ne            #0xb85260
    // 0xb85258: mov             x1, x0
    // 0xb8525c: r0 = _growToNextCapacity()
    //     0xb8525c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb85260: ldur            x2, [fp, #-0x18]
    // 0xb85264: ldur            x3, [fp, #-0x28]
    // 0xb85268: add             x0, x3, #1
    // 0xb8526c: lsl             x1, x0, #1
    // 0xb85270: StoreField: r2->field_b = r1
    //     0xb85270: stur            w1, [x2, #0xb]
    // 0xb85274: LoadField: r1 = r2->field_f
    //     0xb85274: ldur            w1, [x2, #0xf]
    // 0xb85278: DecompressPointer r1
    //     0xb85278: add             x1, x1, HEAP, lsl #32
    // 0xb8527c: ldur            x0, [fp, #-8]
    // 0xb85280: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb85280: add             x25, x1, x3, lsl #2
    //     0xb85284: add             x25, x25, #0xf
    //     0xb85288: str             w0, [x25]
    //     0xb8528c: tbz             w0, #0, #0xb852a8
    //     0xb85290: ldurb           w16, [x1, #-1]
    //     0xb85294: ldurb           w17, [x0, #-1]
    //     0xb85298: and             x16, x17, x16, lsr #2
    //     0xb8529c: tst             x16, HEAP, lsr #32
    //     0xb852a0: b.eq            #0xb852a8
    //     0xb852a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb852a8: b               #0xb852b0
    // 0xb852ac: mov             x2, x3
    // 0xb852b0: r0 = Stack()
    //     0xb852b0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb852b4: r1 = Instance_AlignmentDirectional
    //     0xb852b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb852b8: ldr             x1, [x1, #0xd08]
    // 0xb852bc: StoreField: r0->field_f = r1
    //     0xb852bc: stur            w1, [x0, #0xf]
    // 0xb852c0: r1 = Instance_StackFit
    //     0xb852c0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb852c4: ldr             x1, [x1, #0xfa8]
    // 0xb852c8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb852c8: stur            w1, [x0, #0x17]
    // 0xb852cc: r1 = Instance_Clip
    //     0xb852cc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb852d0: ldr             x1, [x1, #0x7e0]
    // 0xb852d4: StoreField: r0->field_1b = r1
    //     0xb852d4: stur            w1, [x0, #0x1b]
    // 0xb852d8: ldur            x1, [fp, #-0x18]
    // 0xb852dc: StoreField: r0->field_b = r1
    //     0xb852dc: stur            w1, [x0, #0xb]
    // 0xb852e0: LeaveFrame
    //     0xb852e0: mov             SP, fp
    //     0xb852e4: ldp             fp, lr, [SP], #0x10
    // 0xb852e8: ret
    //     0xb852e8: ret             
    // 0xb852ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb852ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb852f0: b               #0xb851b4
    // 0xb852f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb852f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildFreeGiftButton(/* No info */) {
    // ** addr: 0xb852f8, size: 0x2a4
    // 0xb852f8: EnterFrame
    //     0xb852f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb852fc: mov             fp, SP
    // 0xb85300: AllocStack(0x48)
    //     0xb85300: sub             SP, SP, #0x48
    // 0xb85304: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb85304: mov             x0, x1
    //     0xb85308: stur            x1, [fp, #-8]
    //     0xb8530c: mov             x1, x2
    //     0xb85310: stur            x2, [fp, #-0x10]
    // 0xb85314: CheckStackOverflow
    //     0xb85314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85318: cmp             SP, x16
    //     0xb8531c: b.ls            #0xb85594
    // 0xb85320: r1 = 2
    //     0xb85320: movz            x1, #0x2
    // 0xb85324: r0 = AllocateContext()
    //     0xb85324: bl              #0x16f6108  ; AllocateContextStub
    // 0xb85328: mov             x2, x0
    // 0xb8532c: ldur            x0, [fp, #-8]
    // 0xb85330: stur            x2, [fp, #-0x18]
    // 0xb85334: StoreField: r2->field_f = r0
    //     0xb85334: stur            w0, [x2, #0xf]
    // 0xb85338: ldur            x1, [fp, #-0x10]
    // 0xb8533c: StoreField: r2->field_13 = r1
    //     0xb8533c: stur            w1, [x2, #0x13]
    // 0xb85340: r0 = of()
    //     0xb85340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb85344: LoadField: r1 = r0->field_87
    //     0xb85344: ldur            w1, [x0, #0x87]
    // 0xb85348: DecompressPointer r1
    //     0xb85348: add             x1, x1, HEAP, lsl #32
    // 0xb8534c: LoadField: r0 = r1->field_7
    //     0xb8534c: ldur            w0, [x1, #7]
    // 0xb85350: DecompressPointer r0
    //     0xb85350: add             x0, x0, HEAP, lsl #32
    // 0xb85354: r16 = 15.000000
    //     0xb85354: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb85358: ldr             x16, [x16, #0x480]
    // 0xb8535c: r30 = Instance_Color
    //     0xb8535c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb85360: ldr             lr, [lr, #0x858]
    // 0xb85364: stp             lr, x16, [SP]
    // 0xb85368: mov             x1, x0
    // 0xb8536c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8536c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb85370: ldr             x4, [x4, #0xaa0]
    // 0xb85374: r0 = copyWith()
    //     0xb85374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb85378: stur            x0, [fp, #-8]
    // 0xb8537c: r0 = Text()
    //     0xb8537c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb85380: mov             x1, x0
    // 0xb85384: r0 = "Get Free Gift"
    //     0xb85384: add             x0, PP, #0x52, lsl #12  ; [pp+0x52880] "Get Free Gift"
    //     0xb85388: ldr             x0, [x0, #0x880]
    // 0xb8538c: stur            x1, [fp, #-0x10]
    // 0xb85390: StoreField: r1->field_b = r0
    //     0xb85390: stur            w0, [x1, #0xb]
    // 0xb85394: ldur            x0, [fp, #-8]
    // 0xb85398: StoreField: r1->field_13 = r0
    //     0xb85398: stur            w0, [x1, #0x13]
    // 0xb8539c: r0 = SvgPicture()
    //     0xb8539c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb853a0: mov             x1, x0
    // 0xb853a4: r2 = "assets/images/gift.svg"
    //     0xb853a4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52888] "assets/images/gift.svg"
    //     0xb853a8: ldr             x2, [x2, #0x888]
    // 0xb853ac: stur            x0, [fp, #-8]
    // 0xb853b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb853b0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb853b4: r0 = SvgPicture.asset()
    //     0xb853b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb853b8: r0 = Padding()
    //     0xb853b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb853bc: mov             x3, x0
    // 0xb853c0: r0 = Instance_EdgeInsets
    //     0xb853c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xb853c4: ldr             x0, [x0, #0xe60]
    // 0xb853c8: stur            x3, [fp, #-0x20]
    // 0xb853cc: StoreField: r3->field_f = r0
    //     0xb853cc: stur            w0, [x3, #0xf]
    // 0xb853d0: ldur            x0, [fp, #-8]
    // 0xb853d4: StoreField: r3->field_b = r0
    //     0xb853d4: stur            w0, [x3, #0xb]
    // 0xb853d8: r1 = Null
    //     0xb853d8: mov             x1, NULL
    // 0xb853dc: r2 = 4
    //     0xb853dc: movz            x2, #0x4
    // 0xb853e0: r0 = AllocateArray()
    //     0xb853e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb853e4: mov             x2, x0
    // 0xb853e8: ldur            x0, [fp, #-0x10]
    // 0xb853ec: stur            x2, [fp, #-8]
    // 0xb853f0: StoreField: r2->field_f = r0
    //     0xb853f0: stur            w0, [x2, #0xf]
    // 0xb853f4: ldur            x0, [fp, #-0x20]
    // 0xb853f8: StoreField: r2->field_13 = r0
    //     0xb853f8: stur            w0, [x2, #0x13]
    // 0xb853fc: r1 = <Widget>
    //     0xb853fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb85400: r0 = AllocateGrowableArray()
    //     0xb85400: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb85404: mov             x1, x0
    // 0xb85408: ldur            x0, [fp, #-8]
    // 0xb8540c: stur            x1, [fp, #-0x10]
    // 0xb85410: StoreField: r1->field_f = r0
    //     0xb85410: stur            w0, [x1, #0xf]
    // 0xb85414: r0 = 4
    //     0xb85414: movz            x0, #0x4
    // 0xb85418: StoreField: r1->field_b = r0
    //     0xb85418: stur            w0, [x1, #0xb]
    // 0xb8541c: r0 = Row()
    //     0xb8541c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb85420: mov             x1, x0
    // 0xb85424: r0 = Instance_Axis
    //     0xb85424: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb85428: stur            x1, [fp, #-8]
    // 0xb8542c: StoreField: r1->field_f = r0
    //     0xb8542c: stur            w0, [x1, #0xf]
    // 0xb85430: r0 = Instance_MainAxisAlignment
    //     0xb85430: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4daf8] Obj!MainAxisAlignment@d734e1
    //     0xb85434: ldr             x0, [x0, #0xaf8]
    // 0xb85438: StoreField: r1->field_13 = r0
    //     0xb85438: stur            w0, [x1, #0x13]
    // 0xb8543c: r0 = Instance_MainAxisSize
    //     0xb8543c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb85440: ldr             x0, [x0, #0xa10]
    // 0xb85444: ArrayStore: r1[0] = r0  ; List_4
    //     0xb85444: stur            w0, [x1, #0x17]
    // 0xb85448: r0 = Instance_CrossAxisAlignment
    //     0xb85448: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8544c: ldr             x0, [x0, #0xa18]
    // 0xb85450: StoreField: r1->field_1b = r0
    //     0xb85450: stur            w0, [x1, #0x1b]
    // 0xb85454: r0 = Instance_VerticalDirection
    //     0xb85454: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb85458: ldr             x0, [x0, #0xa20]
    // 0xb8545c: StoreField: r1->field_23 = r0
    //     0xb8545c: stur            w0, [x1, #0x23]
    // 0xb85460: r0 = Instance_Clip
    //     0xb85460: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb85464: ldr             x0, [x0, #0x38]
    // 0xb85468: StoreField: r1->field_2b = r0
    //     0xb85468: stur            w0, [x1, #0x2b]
    // 0xb8546c: StoreField: r1->field_2f = rZR
    //     0xb8546c: stur            xzr, [x1, #0x2f]
    // 0xb85470: ldur            x0, [fp, #-0x10]
    // 0xb85474: StoreField: r1->field_b = r0
    //     0xb85474: stur            w0, [x1, #0xb]
    // 0xb85478: r0 = Container()
    //     0xb85478: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8547c: stur            x0, [fp, #-0x10]
    // 0xb85480: r16 = 147.000000
    //     0xb85480: add             x16, PP, #0x52, lsl #12  ; [pp+0x52890] 147
    //     0xb85484: ldr             x16, [x16, #0x890]
    // 0xb85488: r30 = 35.000000
    //     0xb85488: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb8548c: ldr             lr, [lr, #0x2b0]
    // 0xb85490: stp             lr, x16, [SP, #0x18]
    // 0xb85494: r16 = Instance_EdgeInsets
    //     0xb85494: add             x16, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb85498: ldr             x16, [x16, #0x850]
    // 0xb8549c: r30 = Instance_BoxDecoration
    //     0xb8549c: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb854a0: ldr             lr, [lr, #0x5a8]
    // 0xb854a4: stp             lr, x16, [SP, #8]
    // 0xb854a8: ldur            x16, [fp, #-8]
    // 0xb854ac: str             x16, [SP]
    // 0xb854b0: mov             x1, x0
    // 0xb854b4: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0xb854b4: add             x4, PP, #0x38, lsl #12  ; [pp+0x38060] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xb854b8: ldr             x4, [x4, #0x60]
    // 0xb854bc: r0 = Container()
    //     0xb854bc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb854c0: r1 = <Path>
    //     0xb854c0: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb854c4: ldr             x1, [x1, #0xd30]
    // 0xb854c8: r0 = CustomPath()
    //     0xb854c8: bl              #0xb8559c  ; AllocateCustomPathStub -> CustomPath (size=0x10)
    // 0xb854cc: stur            x0, [fp, #-8]
    // 0xb854d0: r0 = ClipPath()
    //     0xb854d0: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb854d4: mov             x1, x0
    // 0xb854d8: ldur            x0, [fp, #-8]
    // 0xb854dc: stur            x1, [fp, #-0x20]
    // 0xb854e0: StoreField: r1->field_f = r0
    //     0xb854e0: stur            w0, [x1, #0xf]
    // 0xb854e4: r0 = Instance_Clip
    //     0xb854e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb854e8: ldr             x0, [x0, #0x138]
    // 0xb854ec: StoreField: r1->field_13 = r0
    //     0xb854ec: stur            w0, [x1, #0x13]
    // 0xb854f0: ldur            x0, [fp, #-0x10]
    // 0xb854f4: StoreField: r1->field_b = r0
    //     0xb854f4: stur            w0, [x1, #0xb]
    // 0xb854f8: r0 = InkWell()
    //     0xb854f8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb854fc: mov             x3, x0
    // 0xb85500: ldur            x0, [fp, #-0x20]
    // 0xb85504: stur            x3, [fp, #-8]
    // 0xb85508: StoreField: r3->field_b = r0
    //     0xb85508: stur            w0, [x3, #0xb]
    // 0xb8550c: ldur            x2, [fp, #-0x18]
    // 0xb85510: r1 = Function '<anonymous closure>':.
    //     0xb85510: add             x1, PP, #0x55, lsl #12  ; [pp+0x55670] AnonymousClosure: (0xb855a8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftButton (0xb852f8)
    //     0xb85514: ldr             x1, [x1, #0x670]
    // 0xb85518: r0 = AllocateClosure()
    //     0xb85518: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8551c: mov             x1, x0
    // 0xb85520: ldur            x0, [fp, #-8]
    // 0xb85524: StoreField: r0->field_f = r1
    //     0xb85524: stur            w1, [x0, #0xf]
    // 0xb85528: r1 = true
    //     0xb85528: add             x1, NULL, #0x20  ; true
    // 0xb8552c: StoreField: r0->field_43 = r1
    //     0xb8552c: stur            w1, [x0, #0x43]
    // 0xb85530: r2 = Instance_BoxShape
    //     0xb85530: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb85534: ldr             x2, [x2, #0x80]
    // 0xb85538: StoreField: r0->field_47 = r2
    //     0xb85538: stur            w2, [x0, #0x47]
    // 0xb8553c: StoreField: r0->field_6f = r1
    //     0xb8553c: stur            w1, [x0, #0x6f]
    // 0xb85540: r2 = false
    //     0xb85540: add             x2, NULL, #0x30  ; false
    // 0xb85544: StoreField: r0->field_73 = r2
    //     0xb85544: stur            w2, [x0, #0x73]
    // 0xb85548: StoreField: r0->field_83 = r1
    //     0xb85548: stur            w1, [x0, #0x83]
    // 0xb8554c: StoreField: r0->field_7b = r2
    //     0xb8554c: stur            w2, [x0, #0x7b]
    // 0xb85550: r0 = Align()
    //     0xb85550: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb85554: mov             x1, x0
    // 0xb85558: r0 = Instance_Alignment
    //     0xb85558: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb8555c: ldr             x0, [x0, #0xa78]
    // 0xb85560: stur            x1, [fp, #-0x10]
    // 0xb85564: StoreField: r1->field_f = r0
    //     0xb85564: stur            w0, [x1, #0xf]
    // 0xb85568: ldur            x0, [fp, #-8]
    // 0xb8556c: StoreField: r1->field_b = r0
    //     0xb8556c: stur            w0, [x1, #0xb]
    // 0xb85570: r0 = Padding()
    //     0xb85570: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb85574: r1 = Instance_EdgeInsets
    //     0xb85574: add             x1, PP, #0x52, lsl #12  ; [pp+0x528a0] Obj!EdgeInsets@d58941
    //     0xb85578: ldr             x1, [x1, #0x8a0]
    // 0xb8557c: StoreField: r0->field_f = r1
    //     0xb8557c: stur            w1, [x0, #0xf]
    // 0xb85580: ldur            x1, [fp, #-0x10]
    // 0xb85584: StoreField: r0->field_b = r1
    //     0xb85584: stur            w1, [x0, #0xb]
    // 0xb85588: LeaveFrame
    //     0xb85588: mov             SP, fp
    //     0xb8558c: ldp             fp, lr, [SP], #0x10
    // 0xb85590: ret
    //     0xb85590: ret             
    // 0xb85594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85594: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85598: b               #0xb85320
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb855a8, size: 0x98
    // 0xb855a8: EnterFrame
    //     0xb855a8: stp             fp, lr, [SP, #-0x10]!
    //     0xb855ac: mov             fp, SP
    // 0xb855b0: AllocStack(0x10)
    //     0xb855b0: sub             SP, SP, #0x10
    // 0xb855b4: SetupParameters()
    //     0xb855b4: ldr             x0, [fp, #0x10]
    //     0xb855b8: ldur            w1, [x0, #0x17]
    //     0xb855bc: add             x1, x1, HEAP, lsl #32
    //     0xb855c0: stur            x1, [fp, #-8]
    // 0xb855c4: CheckStackOverflow
    //     0xb855c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb855c8: cmp             SP, x16
    //     0xb855cc: b.ls            #0xb85634
    // 0xb855d0: LoadField: r0 = r1->field_f
    //     0xb855d0: ldur            w0, [x1, #0xf]
    // 0xb855d4: DecompressPointer r0
    //     0xb855d4: add             x0, x0, HEAP, lsl #32
    // 0xb855d8: LoadField: r2 = r0->field_b
    //     0xb855d8: ldur            w2, [x0, #0xb]
    // 0xb855dc: DecompressPointer r2
    //     0xb855dc: add             x2, x2, HEAP, lsl #32
    // 0xb855e0: cmp             w2, NULL
    // 0xb855e4: b.eq            #0xb8563c
    // 0xb855e8: LoadField: r0 = r2->field_1f
    //     0xb855e8: ldur            w0, [x2, #0x1f]
    // 0xb855ec: DecompressPointer r0
    //     0xb855ec: add             x0, x0, HEAP, lsl #32
    // 0xb855f0: str             x0, [SP]
    // 0xb855f4: r4 = 0
    //     0xb855f4: movz            x4, #0
    // 0xb855f8: ldr             x0, [SP]
    // 0xb855fc: r16 = UnlinkedCall_0x613b5c
    //     0xb855fc: add             x16, PP, #0x55, lsl #12  ; [pp+0x55678] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb85600: add             x16, x16, #0x678
    // 0xb85604: ldp             x5, lr, [x16]
    // 0xb85608: blr             lr
    // 0xb8560c: ldur            x0, [fp, #-8]
    // 0xb85610: LoadField: r1 = r0->field_f
    //     0xb85610: ldur            w1, [x0, #0xf]
    // 0xb85614: DecompressPointer r1
    //     0xb85614: add             x1, x1, HEAP, lsl #32
    // 0xb85618: LoadField: r2 = r0->field_13
    //     0xb85618: ldur            w2, [x0, #0x13]
    // 0xb8561c: DecompressPointer r2
    //     0xb8561c: add             x2, x2, HEAP, lsl #32
    // 0xb85620: r0 = _showFreeGiftDialog()
    //     0xb85620: bl              #0xb85640  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog
    // 0xb85624: r0 = Null
    //     0xb85624: mov             x0, NULL
    // 0xb85628: LeaveFrame
    //     0xb85628: mov             SP, fp
    //     0xb8562c: ldp             fp, lr, [SP], #0x10
    // 0xb85630: ret
    //     0xb85630: ret             
    // 0xb85634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb85634: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85638: b               #0xb855d0
    // 0xb8563c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8563c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showFreeGiftDialog(/* No info */) {
    // ** addr: 0xb85640, size: 0x1c0
    // 0xb85640: EnterFrame
    //     0xb85640: stp             fp, lr, [SP, #-0x10]!
    //     0xb85644: mov             fp, SP
    // 0xb85648: AllocStack(0x38)
    //     0xb85648: sub             SP, SP, #0x38
    // 0xb8564c: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb8564c: mov             x0, x1
    //     0xb85650: stur            x1, [fp, #-8]
    //     0xb85654: mov             x1, x2
    //     0xb85658: stur            x2, [fp, #-0x10]
    // 0xb8565c: CheckStackOverflow
    //     0xb8565c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85660: cmp             SP, x16
    //     0xb85664: b.ls            #0xb857dc
    // 0xb85668: r1 = 3
    //     0xb85668: movz            x1, #0x3
    // 0xb8566c: r0 = AllocateContext()
    //     0xb8566c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb85670: mov             x2, x0
    // 0xb85674: ldur            x0, [fp, #-8]
    // 0xb85678: stur            x2, [fp, #-0x18]
    // 0xb8567c: StoreField: r2->field_f = r0
    //     0xb8567c: stur            w0, [x2, #0xf]
    // 0xb85680: LoadField: r1 = r0->field_b
    //     0xb85680: ldur            w1, [x0, #0xb]
    // 0xb85684: DecompressPointer r1
    //     0xb85684: add             x1, x1, HEAP, lsl #32
    // 0xb85688: cmp             w1, NULL
    // 0xb8568c: b.eq            #0xb857e4
    // 0xb85690: LoadField: r0 = r1->field_1b
    //     0xb85690: ldur            w0, [x1, #0x1b]
    // 0xb85694: DecompressPointer r0
    //     0xb85694: add             x0, x0, HEAP, lsl #32
    // 0xb85698: LoadField: r1 = r0->field_b
    //     0xb85698: ldur            w1, [x0, #0xb]
    // 0xb8569c: DecompressPointer r1
    //     0xb8569c: add             x1, x1, HEAP, lsl #32
    // 0xb856a0: StoreField: r2->field_13 = r1
    //     0xb856a0: stur            w1, [x2, #0x13]
    // 0xb856a4: cmp             w1, NULL
    // 0xb856a8: b.ne            #0xb856bc
    // 0xb856ac: r0 = Null
    //     0xb856ac: mov             x0, NULL
    // 0xb856b0: LeaveFrame
    //     0xb856b0: mov             SP, fp
    //     0xb856b4: ldp             fp, lr, [SP], #0x10
    // 0xb856b8: ret
    //     0xb856b8: ret             
    // 0xb856bc: LoadField: r0 = r1->field_7
    //     0xb856bc: ldur            w0, [x1, #7]
    // 0xb856c0: DecompressPointer r0
    //     0xb856c0: add             x0, x0, HEAP, lsl #32
    // 0xb856c4: LoadField: r1 = r0->field_b
    //     0xb856c4: ldur            w1, [x0, #0xb]
    // 0xb856c8: r0 = LoadInt32Instr(r1)
    //     0xb856c8: sbfx            x0, x1, #1, #0x1f
    // 0xb856cc: cbnz            x0, #0xb856fc
    // 0xb856d0: ldur            x1, [fp, #-0x10]
    // 0xb856d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb856d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb856d8: r0 = _of()
    //     0xb856d8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb856dc: LoadField: r1 = r0->field_7
    //     0xb856dc: ldur            w1, [x0, #7]
    // 0xb856e0: DecompressPointer r1
    //     0xb856e0: add             x1, x1, HEAP, lsl #32
    // 0xb856e4: LoadField: d0 = r1->field_f
    //     0xb856e4: ldur            d0, [x1, #0xf]
    // 0xb856e8: d1 = 0.230000
    //     0xb856e8: add             x17, PP, #0x52, lsl #12  ; [pp+0x528b8] IMM: double(0.23) from 0x3fcd70a3d70a3d71
    //     0xb856ec: ldr             d1, [x17, #0x8b8]
    // 0xb856f0: fmul            d2, d0, d1
    // 0xb856f4: mov             v0.16b, v2.16b
    // 0xb856f8: b               #0xb85758
    // 0xb856fc: cmp             x0, #1
    // 0xb85700: b.ne            #0xb85730
    // 0xb85704: ldur            x1, [fp, #-0x10]
    // 0xb85708: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb85708: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8570c: r0 = _of()
    //     0xb8570c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb85710: LoadField: r1 = r0->field_7
    //     0xb85710: ldur            w1, [x0, #7]
    // 0xb85714: DecompressPointer r1
    //     0xb85714: add             x1, x1, HEAP, lsl #32
    // 0xb85718: LoadField: d0 = r1->field_f
    //     0xb85718: ldur            d0, [x1, #0xf]
    // 0xb8571c: d1 = 0.210000
    //     0xb8571c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c0] IMM: double(0.21) from 0x3fcae147ae147ae1
    //     0xb85720: ldr             d1, [x17, #0x8c0]
    // 0xb85724: fmul            d2, d0, d1
    // 0xb85728: mov             v0.16b, v2.16b
    // 0xb8572c: b               #0xb85758
    // 0xb85730: ldur            x1, [fp, #-0x10]
    // 0xb85734: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb85734: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb85738: r0 = _of()
    //     0xb85738: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb8573c: LoadField: r1 = r0->field_7
    //     0xb8573c: ldur            w1, [x0, #7]
    // 0xb85740: DecompressPointer r1
    //     0xb85740: add             x1, x1, HEAP, lsl #32
    // 0xb85744: LoadField: d0 = r1->field_f
    //     0xb85744: ldur            d0, [x1, #0xf]
    // 0xb85748: d1 = 0.185000
    //     0xb85748: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c8] IMM: double(0.185) from 0x3fc7ae147ae147ae
    //     0xb8574c: ldr             d1, [x17, #0x8c8]
    // 0xb85750: fmul            d2, d0, d1
    // 0xb85754: mov             v0.16b, v2.16b
    // 0xb85758: ldur            x2, [fp, #-0x18]
    // 0xb8575c: r0 = inline_Allocate_Double()
    //     0xb8575c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb85760: add             x0, x0, #0x10
    //     0xb85764: cmp             x1, x0
    //     0xb85768: b.ls            #0xb857e8
    //     0xb8576c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb85770: sub             x0, x0, #0xf
    //     0xb85774: movz            x1, #0xe15c
    //     0xb85778: movk            x1, #0x3, lsl #16
    //     0xb8577c: stur            x1, [x0, #-1]
    // 0xb85780: StoreField: r0->field_7 = d0
    //     0xb85780: stur            d0, [x0, #7]
    // 0xb85784: ArrayStore: r2[0] = r0  ; List_4
    //     0xb85784: stur            w0, [x2, #0x17]
    //     0xb85788: ldurb           w16, [x2, #-1]
    //     0xb8578c: ldurb           w17, [x0, #-1]
    //     0xb85790: and             x16, x17, x16, lsr #2
    //     0xb85794: tst             x16, HEAP, lsr #32
    //     0xb85798: b.eq            #0xb857a0
    //     0xb8579c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb857a0: r1 = Function '<anonymous closure>':.
    //     0xb857a0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55688] AnonymousClosure: (0xb85800), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0xb85640)
    //     0xb857a4: ldr             x1, [x1, #0x688]
    // 0xb857a8: r0 = AllocateClosure()
    //     0xb857a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb857ac: r16 = <void?>
    //     0xb857ac: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0xb857b0: stp             x0, x16, [SP, #0x10]
    // 0xb857b4: ldur            x16, [fp, #-0x10]
    // 0xb857b8: r30 = false
    //     0xb857b8: add             lr, NULL, #0x30  ; false
    // 0xb857bc: stp             lr, x16, [SP]
    // 0xb857c0: r4 = const [0x1, 0x3, 0x3, 0x2, barrierDismissible, 0x2, null]
    //     0xb857c0: add             x4, PP, #0x52, lsl #12  ; [pp+0x528d8] List(7) [0x1, 0x3, 0x3, 0x2, "barrierDismissible", 0x2, Null]
    //     0xb857c4: ldr             x4, [x4, #0x8d8]
    // 0xb857c8: r0 = showDialog()
    //     0xb857c8: bl              #0x99c870  ; [package:flutter/src/material/dialog.dart] ::showDialog
    // 0xb857cc: r0 = Null
    //     0xb857cc: mov             x0, NULL
    // 0xb857d0: LeaveFrame
    //     0xb857d0: mov             SP, fp
    //     0xb857d4: ldp             fp, lr, [SP], #0x10
    // 0xb857d8: ret
    //     0xb857d8: ret             
    // 0xb857dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb857dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb857e0: b               #0xb85668
    // 0xb857e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb857e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb857e8: SaveReg d0
    //     0xb857e8: str             q0, [SP, #-0x10]!
    // 0xb857ec: SaveReg r2
    //     0xb857ec: str             x2, [SP, #-8]!
    // 0xb857f0: r0 = AllocateDouble()
    //     0xb857f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb857f4: RestoreReg r2
    //     0xb857f4: ldr             x2, [SP], #8
    // 0xb857f8: RestoreReg d0
    //     0xb857f8: ldr             q0, [SP], #0x10
    // 0xb857fc: b               #0xb85780
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb85800, size: 0x990
    // 0xb85800: EnterFrame
    //     0xb85800: stp             fp, lr, [SP, #-0x10]!
    //     0xb85804: mov             fp, SP
    // 0xb85808: AllocStack(0x78)
    //     0xb85808: sub             SP, SP, #0x78
    // 0xb8580c: SetupParameters()
    //     0xb8580c: ldr             x0, [fp, #0x18]
    //     0xb85810: ldur            w1, [x0, #0x17]
    //     0xb85814: add             x1, x1, HEAP, lsl #32
    //     0xb85818: stur            x1, [fp, #-8]
    // 0xb8581c: CheckStackOverflow
    //     0xb8581c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb85820: cmp             SP, x16
    //     0xb85824: b.ls            #0xb86124
    // 0xb85828: r1 = 1
    //     0xb85828: movz            x1, #0x1
    // 0xb8582c: r0 = AllocateContext()
    //     0xb8582c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb85830: mov             x2, x0
    // 0xb85834: ldur            x0, [fp, #-8]
    // 0xb85838: stur            x2, [fp, #-0x10]
    // 0xb8583c: StoreField: r2->field_b = r0
    //     0xb8583c: stur            w0, [x2, #0xb]
    // 0xb85840: ldr             x1, [fp, #0x10]
    // 0xb85844: StoreField: r2->field_f = r1
    //     0xb85844: stur            w1, [x2, #0xf]
    // 0xb85848: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb85848: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8584c: r0 = _of()
    //     0xb8584c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb85850: LoadField: r1 = r0->field_7
    //     0xb85850: ldur            w1, [x0, #7]
    // 0xb85854: DecompressPointer r1
    //     0xb85854: add             x1, x1, HEAP, lsl #32
    // 0xb85858: LoadField: d0 = r1->field_7
    //     0xb85858: ldur            d0, [x1, #7]
    // 0xb8585c: d1 = 0.880000
    //     0xb8585c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0xb85860: ldr             d1, [x17, #0x8e0]
    // 0xb85864: fmul            d2, d0, d1
    // 0xb85868: ldur            x2, [fp, #-0x10]
    // 0xb8586c: stur            d2, [fp, #-0x50]
    // 0xb85870: LoadField: r1 = r2->field_f
    //     0xb85870: ldur            w1, [x2, #0xf]
    // 0xb85874: DecompressPointer r1
    //     0xb85874: add             x1, x1, HEAP, lsl #32
    // 0xb85878: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb85878: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8587c: r0 = _of()
    //     0xb8587c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb85880: LoadField: r1 = r0->field_7
    //     0xb85880: ldur            w1, [x0, #7]
    // 0xb85884: DecompressPointer r1
    //     0xb85884: add             x1, x1, HEAP, lsl #32
    // 0xb85888: LoadField: d0 = r1->field_7
    //     0xb85888: ldur            d0, [x1, #7]
    // 0xb8588c: d1 = 0.880000
    //     0xb8588c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0xb85890: ldr             d1, [x17, #0x8e0]
    // 0xb85894: fmul            d2, d0, d1
    // 0xb85898: stur            d2, [fp, #-0x58]
    // 0xb8589c: r0 = Radius()
    //     0xb8589c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb858a0: d0 = 12.000000
    //     0xb858a0: fmov            d0, #12.00000000
    // 0xb858a4: stur            x0, [fp, #-0x18]
    // 0xb858a8: StoreField: r0->field_7 = d0
    //     0xb858a8: stur            d0, [x0, #7]
    // 0xb858ac: StoreField: r0->field_f = d0
    //     0xb858ac: stur            d0, [x0, #0xf]
    // 0xb858b0: r0 = BorderRadius()
    //     0xb858b0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb858b4: mov             x1, x0
    // 0xb858b8: ldur            x0, [fp, #-0x18]
    // 0xb858bc: stur            x1, [fp, #-0x20]
    // 0xb858c0: StoreField: r1->field_7 = r0
    //     0xb858c0: stur            w0, [x1, #7]
    // 0xb858c4: StoreField: r1->field_b = r0
    //     0xb858c4: stur            w0, [x1, #0xb]
    // 0xb858c8: StoreField: r1->field_f = r0
    //     0xb858c8: stur            w0, [x1, #0xf]
    // 0xb858cc: StoreField: r1->field_13 = r0
    //     0xb858cc: stur            w0, [x1, #0x13]
    // 0xb858d0: r0 = RoundedRectangleBorder()
    //     0xb858d0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb858d4: mov             x3, x0
    // 0xb858d8: ldur            x0, [fp, #-0x20]
    // 0xb858dc: stur            x3, [fp, #-0x18]
    // 0xb858e0: StoreField: r3->field_b = r0
    //     0xb858e0: stur            w0, [x3, #0xb]
    // 0xb858e4: r0 = Instance_BorderSide
    //     0xb858e4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb858e8: ldr             x0, [x0, #0xe20]
    // 0xb858ec: StoreField: r3->field_7 = r0
    //     0xb858ec: stur            w0, [x3, #7]
    // 0xb858f0: r1 = <Widget>
    //     0xb858f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb858f4: r2 = 20
    //     0xb858f4: movz            x2, #0x14
    // 0xb858f8: r0 = AllocateArray()
    //     0xb858f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb858fc: stur            x0, [fp, #-0x20]
    // 0xb85900: r16 = Instance_SizedBox
    //     0xb85900: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xb85904: ldr             x16, [x16, #0xd68]
    // 0xb85908: StoreField: r0->field_f = r16
    //     0xb85908: stur            w16, [x0, #0xf]
    // 0xb8590c: ldur            x2, [fp, #-0x10]
    // 0xb85910: LoadField: r1 = r2->field_f
    //     0xb85910: ldur            w1, [x2, #0xf]
    // 0xb85914: DecompressPointer r1
    //     0xb85914: add             x1, x1, HEAP, lsl #32
    // 0xb85918: r0 = of()
    //     0xb85918: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8591c: LoadField: r1 = r0->field_87
    //     0xb8591c: ldur            w1, [x0, #0x87]
    // 0xb85920: DecompressPointer r1
    //     0xb85920: add             x1, x1, HEAP, lsl #32
    // 0xb85924: LoadField: r0 = r1->field_7
    //     0xb85924: ldur            w0, [x1, #7]
    // 0xb85928: DecompressPointer r0
    //     0xb85928: add             x0, x0, HEAP, lsl #32
    // 0xb8592c: r16 = 12.000000
    //     0xb8592c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb85930: ldr             x16, [x16, #0x9e8]
    // 0xb85934: str             x16, [SP]
    // 0xb85938: mov             x1, x0
    // 0xb8593c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb8593c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb85940: ldr             x4, [x4, #0x798]
    // 0xb85944: r0 = copyWith()
    //     0xb85944: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb85948: stur            x0, [fp, #-0x28]
    // 0xb8594c: r0 = Text()
    //     0xb8594c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb85950: mov             x1, x0
    // 0xb85954: r0 = "Get a Free Gift with this product!"
    //     0xb85954: add             x0, PP, #0x52, lsl #12  ; [pp+0x52910] "Get a Free Gift with this product!"
    //     0xb85958: ldr             x0, [x0, #0x910]
    // 0xb8595c: StoreField: r1->field_b = r0
    //     0xb8595c: stur            w0, [x1, #0xb]
    // 0xb85960: ldur            x0, [fp, #-0x28]
    // 0xb85964: StoreField: r1->field_13 = r0
    //     0xb85964: stur            w0, [x1, #0x13]
    // 0xb85968: mov             x0, x1
    // 0xb8596c: ldur            x1, [fp, #-0x20]
    // 0xb85970: ArrayStore: r1[1] = r0  ; List_4
    //     0xb85970: add             x25, x1, #0x13
    //     0xb85974: str             w0, [x25]
    //     0xb85978: tbz             w0, #0, #0xb85994
    //     0xb8597c: ldurb           w16, [x1, #-1]
    //     0xb85980: ldurb           w17, [x0, #-1]
    //     0xb85984: and             x16, x17, x16, lsr #2
    //     0xb85988: tst             x16, HEAP, lsr #32
    //     0xb8598c: b.eq            #0xb85994
    //     0xb85990: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb85994: ldur            x0, [fp, #-0x20]
    // 0xb85998: r16 = Instance_SizedBox
    //     0xb85998: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb8599c: ldr             x16, [x16, #0x578]
    // 0xb859a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb859a0: stur            w16, [x0, #0x17]
    // 0xb859a4: ldur            x2, [fp, #-8]
    // 0xb859a8: LoadField: r3 = r2->field_13
    //     0xb859a8: ldur            w3, [x2, #0x13]
    // 0xb859ac: DecompressPointer r3
    //     0xb859ac: add             x3, x3, HEAP, lsl #32
    // 0xb859b0: stur            x3, [fp, #-0x30]
    // 0xb859b4: cmp             w3, NULL
    // 0xb859b8: b.eq            #0xb8612c
    // 0xb859bc: LoadField: r1 = r3->field_f
    //     0xb859bc: ldur            w1, [x3, #0xf]
    // 0xb859c0: DecompressPointer r1
    //     0xb859c0: add             x1, x1, HEAP, lsl #32
    // 0xb859c4: cmp             w1, NULL
    // 0xb859c8: b.ne            #0xb859d4
    // 0xb859cc: r5 = ""
    //     0xb859cc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb859d0: b               #0xb859d8
    // 0xb859d4: mov             x5, x1
    // 0xb859d8: ldur            x4, [fp, #-0x10]
    // 0xb859dc: stur            x5, [fp, #-0x28]
    // 0xb859e0: LoadField: r1 = r4->field_f
    //     0xb859e0: ldur            w1, [x4, #0xf]
    // 0xb859e4: DecompressPointer r1
    //     0xb859e4: add             x1, x1, HEAP, lsl #32
    // 0xb859e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb859e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb859ec: r0 = _of()
    //     0xb859ec: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb859f0: LoadField: r1 = r0->field_7
    //     0xb859f0: ldur            w1, [x0, #7]
    // 0xb859f4: DecompressPointer r1
    //     0xb859f4: add             x1, x1, HEAP, lsl #32
    // 0xb859f8: LoadField: d0 = r1->field_f
    //     0xb859f8: ldur            d0, [x1, #0xf]
    // 0xb859fc: d1 = 0.200000
    //     0xb859fc: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb85a00: fmul            d2, d0, d1
    // 0xb85a04: ldur            x2, [fp, #-0x10]
    // 0xb85a08: stur            d2, [fp, #-0x60]
    // 0xb85a0c: LoadField: r1 = r2->field_f
    //     0xb85a0c: ldur            w1, [x2, #0xf]
    // 0xb85a10: DecompressPointer r1
    //     0xb85a10: add             x1, x1, HEAP, lsl #32
    // 0xb85a14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb85a14: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb85a18: r0 = _of()
    //     0xb85a18: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb85a1c: LoadField: r1 = r0->field_7
    //     0xb85a1c: ldur            w1, [x0, #7]
    // 0xb85a20: DecompressPointer r1
    //     0xb85a20: add             x1, x1, HEAP, lsl #32
    // 0xb85a24: LoadField: d0 = r1->field_7
    //     0xb85a24: ldur            d0, [x1, #7]
    // 0xb85a28: d1 = 0.770000
    //     0xb85a28: add             x17, PP, #0x52, lsl #12  ; [pp+0x52918] IMM: double(0.77) from 0x3fe8a3d70a3d70a4
    //     0xb85a2c: ldr             d1, [x17, #0x918]
    // 0xb85a30: fmul            d2, d0, d1
    // 0xb85a34: ldur            d0, [fp, #-0x60]
    // 0xb85a38: r0 = inline_Allocate_Double()
    //     0xb85a38: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb85a3c: add             x0, x0, #0x10
    //     0xb85a40: cmp             x1, x0
    //     0xb85a44: b.ls            #0xb86130
    //     0xb85a48: str             x0, [THR, #0x50]  ; THR::top
    //     0xb85a4c: sub             x0, x0, #0xf
    //     0xb85a50: movz            x1, #0xe15c
    //     0xb85a54: movk            x1, #0x3, lsl #16
    //     0xb85a58: stur            x1, [x0, #-1]
    // 0xb85a5c: StoreField: r0->field_7 = d0
    //     0xb85a5c: stur            d0, [x0, #7]
    // 0xb85a60: stur            x0, [fp, #-0x40]
    // 0xb85a64: r1 = inline_Allocate_Double()
    //     0xb85a64: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb85a68: add             x1, x1, #0x10
    //     0xb85a6c: cmp             x2, x1
    //     0xb85a70: b.ls            #0xb86140
    //     0xb85a74: str             x1, [THR, #0x50]  ; THR::top
    //     0xb85a78: sub             x1, x1, #0xf
    //     0xb85a7c: movz            x2, #0xe15c
    //     0xb85a80: movk            x2, #0x3, lsl #16
    //     0xb85a84: stur            x2, [x1, #-1]
    // 0xb85a88: StoreField: r1->field_7 = d2
    //     0xb85a88: stur            d2, [x1, #7]
    // 0xb85a8c: stur            x1, [fp, #-0x38]
    // 0xb85a90: r0 = CachedNetworkImage()
    //     0xb85a90: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb85a94: stur            x0, [fp, #-0x48]
    // 0xb85a98: r16 = Instance_BoxFit
    //     0xb85a98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb85a9c: ldr             x16, [x16, #0x118]
    // 0xb85aa0: ldur            lr, [fp, #-0x40]
    // 0xb85aa4: stp             lr, x16, [SP, #8]
    // 0xb85aa8: ldur            x16, [fp, #-0x38]
    // 0xb85aac: str             x16, [SP]
    // 0xb85ab0: mov             x1, x0
    // 0xb85ab4: ldur            x2, [fp, #-0x28]
    // 0xb85ab8: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xb85ab8: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xb85abc: ldr             x4, [x4, #0x8e0]
    // 0xb85ac0: r0 = CachedNetworkImage()
    //     0xb85ac0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb85ac4: ldur            x1, [fp, #-0x20]
    // 0xb85ac8: ldur            x0, [fp, #-0x48]
    // 0xb85acc: ArrayStore: r1[3] = r0  ; List_4
    //     0xb85acc: add             x25, x1, #0x1b
    //     0xb85ad0: str             w0, [x25]
    //     0xb85ad4: tbz             w0, #0, #0xb85af0
    //     0xb85ad8: ldurb           w16, [x1, #-1]
    //     0xb85adc: ldurb           w17, [x0, #-1]
    //     0xb85ae0: and             x16, x17, x16, lsr #2
    //     0xb85ae4: tst             x16, HEAP, lsr #32
    //     0xb85ae8: b.eq            #0xb85af0
    //     0xb85aec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb85af0: ldur            x0, [fp, #-0x20]
    // 0xb85af4: r16 = Instance_SizedBox
    //     0xb85af4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb85af8: ldr             x16, [x16, #0x578]
    // 0xb85afc: StoreField: r0->field_1f = r16
    //     0xb85afc: stur            w16, [x0, #0x1f]
    // 0xb85b00: ldur            x3, [fp, #-0x30]
    // 0xb85b04: LoadField: r1 = r3->field_b
    //     0xb85b04: ldur            w1, [x3, #0xb]
    // 0xb85b08: DecompressPointer r1
    //     0xb85b08: add             x1, x1, HEAP, lsl #32
    // 0xb85b0c: cmp             w1, NULL
    // 0xb85b10: b.ne            #0xb85b1c
    // 0xb85b14: r4 = ""
    //     0xb85b14: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb85b18: b               #0xb85b20
    // 0xb85b1c: mov             x4, x1
    // 0xb85b20: ldur            x2, [fp, #-0x10]
    // 0xb85b24: stur            x4, [fp, #-0x28]
    // 0xb85b28: LoadField: r1 = r2->field_f
    //     0xb85b28: ldur            w1, [x2, #0xf]
    // 0xb85b2c: DecompressPointer r1
    //     0xb85b2c: add             x1, x1, HEAP, lsl #32
    // 0xb85b30: r0 = of()
    //     0xb85b30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb85b34: LoadField: r1 = r0->field_87
    //     0xb85b34: ldur            w1, [x0, #0x87]
    // 0xb85b38: DecompressPointer r1
    //     0xb85b38: add             x1, x1, HEAP, lsl #32
    // 0xb85b3c: LoadField: r0 = r1->field_7
    //     0xb85b3c: ldur            w0, [x1, #7]
    // 0xb85b40: DecompressPointer r0
    //     0xb85b40: add             x0, x0, HEAP, lsl #32
    // 0xb85b44: r16 = 12.000000
    //     0xb85b44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb85b48: ldr             x16, [x16, #0x9e8]
    // 0xb85b4c: str             x16, [SP]
    // 0xb85b50: mov             x1, x0
    // 0xb85b54: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb85b54: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb85b58: ldr             x4, [x4, #0x798]
    // 0xb85b5c: r0 = copyWith()
    //     0xb85b5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb85b60: stur            x0, [fp, #-0x38]
    // 0xb85b64: r0 = Text()
    //     0xb85b64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb85b68: mov             x1, x0
    // 0xb85b6c: ldur            x0, [fp, #-0x28]
    // 0xb85b70: StoreField: r1->field_b = r0
    //     0xb85b70: stur            w0, [x1, #0xb]
    // 0xb85b74: ldur            x0, [fp, #-0x38]
    // 0xb85b78: StoreField: r1->field_13 = r0
    //     0xb85b78: stur            w0, [x1, #0x13]
    // 0xb85b7c: mov             x0, x1
    // 0xb85b80: ldur            x1, [fp, #-0x20]
    // 0xb85b84: ArrayStore: r1[5] = r0  ; List_4
    //     0xb85b84: add             x25, x1, #0x23
    //     0xb85b88: str             w0, [x25]
    //     0xb85b8c: tbz             w0, #0, #0xb85ba8
    //     0xb85b90: ldurb           w16, [x1, #-1]
    //     0xb85b94: ldurb           w17, [x0, #-1]
    //     0xb85b98: and             x16, x17, x16, lsr #2
    //     0xb85b9c: tst             x16, HEAP, lsr #32
    //     0xb85ba0: b.eq            #0xb85ba8
    //     0xb85ba4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb85ba8: ldur            x0, [fp, #-0x20]
    // 0xb85bac: r16 = Instance_SizedBox
    //     0xb85bac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb85bb0: ldr             x16, [x16, #0x8f0]
    // 0xb85bb4: StoreField: r0->field_27 = r16
    //     0xb85bb4: stur            w16, [x0, #0x27]
    // 0xb85bb8: ldur            x2, [fp, #-0x10]
    // 0xb85bbc: LoadField: r1 = r2->field_f
    //     0xb85bbc: ldur            w1, [x2, #0xf]
    // 0xb85bc0: DecompressPointer r1
    //     0xb85bc0: add             x1, x1, HEAP, lsl #32
    // 0xb85bc4: r0 = of()
    //     0xb85bc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb85bc8: LoadField: r1 = r0->field_87
    //     0xb85bc8: ldur            w1, [x0, #0x87]
    // 0xb85bcc: DecompressPointer r1
    //     0xb85bcc: add             x1, x1, HEAP, lsl #32
    // 0xb85bd0: LoadField: r0 = r1->field_2b
    //     0xb85bd0: ldur            w0, [x1, #0x2b]
    // 0xb85bd4: DecompressPointer r0
    //     0xb85bd4: add             x0, x0, HEAP, lsl #32
    // 0xb85bd8: r16 = 12.000000
    //     0xb85bd8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb85bdc: ldr             x16, [x16, #0x9e8]
    // 0xb85be0: r30 = Instance_Color
    //     0xb85be0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb85be4: ldr             lr, [lr, #0x858]
    // 0xb85be8: stp             lr, x16, [SP]
    // 0xb85bec: mov             x1, x0
    // 0xb85bf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb85bf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb85bf4: ldr             x4, [x4, #0xaa0]
    // 0xb85bf8: r0 = copyWith()
    //     0xb85bf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb85bfc: stur            x0, [fp, #-0x28]
    // 0xb85c00: r0 = Text()
    //     0xb85c00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb85c04: mov             x2, x0
    // 0xb85c08: r0 = "Free"
    //     0xb85c08: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb85c0c: ldr             x0, [x0, #0x668]
    // 0xb85c10: stur            x2, [fp, #-0x38]
    // 0xb85c14: StoreField: r2->field_b = r0
    //     0xb85c14: stur            w0, [x2, #0xb]
    // 0xb85c18: ldur            x0, [fp, #-0x28]
    // 0xb85c1c: StoreField: r2->field_13 = r0
    //     0xb85c1c: stur            w0, [x2, #0x13]
    // 0xb85c20: ldur            x3, [fp, #-0x30]
    // 0xb85c24: LoadField: r0 = r3->field_13
    //     0xb85c24: ldur            w0, [x3, #0x13]
    // 0xb85c28: DecompressPointer r0
    //     0xb85c28: add             x0, x0, HEAP, lsl #32
    // 0xb85c2c: cmp             w0, NULL
    // 0xb85c30: b.ne            #0xb85c3c
    // 0xb85c34: r7 = ""
    //     0xb85c34: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb85c38: b               #0xb85c40
    // 0xb85c3c: mov             x7, x0
    // 0xb85c40: ldur            x5, [fp, #-8]
    // 0xb85c44: ldur            x4, [fp, #-0x10]
    // 0xb85c48: ldur            d1, [fp, #-0x50]
    // 0xb85c4c: ldur            d0, [fp, #-0x58]
    // 0xb85c50: ldur            x6, [fp, #-0x18]
    // 0xb85c54: ldur            x0, [fp, #-0x20]
    // 0xb85c58: stur            x7, [fp, #-0x28]
    // 0xb85c5c: LoadField: r1 = r4->field_f
    //     0xb85c5c: ldur            w1, [x4, #0xf]
    // 0xb85c60: DecompressPointer r1
    //     0xb85c60: add             x1, x1, HEAP, lsl #32
    // 0xb85c64: r0 = of()
    //     0xb85c64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb85c68: LoadField: r1 = r0->field_87
    //     0xb85c68: ldur            w1, [x0, #0x87]
    // 0xb85c6c: DecompressPointer r1
    //     0xb85c6c: add             x1, x1, HEAP, lsl #32
    // 0xb85c70: LoadField: r0 = r1->field_2b
    //     0xb85c70: ldur            w0, [x1, #0x2b]
    // 0xb85c74: DecompressPointer r0
    //     0xb85c74: add             x0, x0, HEAP, lsl #32
    // 0xb85c78: r16 = 12.000000
    //     0xb85c78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb85c7c: ldr             x16, [x16, #0x9e8]
    // 0xb85c80: r30 = Instance_TextDecoration
    //     0xb85c80: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb85c84: ldr             lr, [lr, #0xe30]
    // 0xb85c88: stp             lr, x16, [SP]
    // 0xb85c8c: mov             x1, x0
    // 0xb85c90: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb85c90: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb85c94: ldr             x4, [x4, #0x698]
    // 0xb85c98: r0 = copyWith()
    //     0xb85c98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb85c9c: stur            x0, [fp, #-0x40]
    // 0xb85ca0: r0 = Text()
    //     0xb85ca0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb85ca4: mov             x3, x0
    // 0xb85ca8: ldur            x0, [fp, #-0x28]
    // 0xb85cac: stur            x3, [fp, #-0x48]
    // 0xb85cb0: StoreField: r3->field_b = r0
    //     0xb85cb0: stur            w0, [x3, #0xb]
    // 0xb85cb4: ldur            x0, [fp, #-0x40]
    // 0xb85cb8: StoreField: r3->field_13 = r0
    //     0xb85cb8: stur            w0, [x3, #0x13]
    // 0xb85cbc: r1 = Null
    //     0xb85cbc: mov             x1, NULL
    // 0xb85cc0: r2 = 6
    //     0xb85cc0: movz            x2, #0x6
    // 0xb85cc4: r0 = AllocateArray()
    //     0xb85cc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb85cc8: mov             x2, x0
    // 0xb85ccc: ldur            x0, [fp, #-0x38]
    // 0xb85cd0: stur            x2, [fp, #-0x28]
    // 0xb85cd4: StoreField: r2->field_f = r0
    //     0xb85cd4: stur            w0, [x2, #0xf]
    // 0xb85cd8: r16 = Instance_SizedBox
    //     0xb85cd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb85cdc: ldr             x16, [x16, #0xa50]
    // 0xb85ce0: StoreField: r2->field_13 = r16
    //     0xb85ce0: stur            w16, [x2, #0x13]
    // 0xb85ce4: ldur            x0, [fp, #-0x48]
    // 0xb85ce8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb85ce8: stur            w0, [x2, #0x17]
    // 0xb85cec: r1 = <Widget>
    //     0xb85cec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb85cf0: r0 = AllocateGrowableArray()
    //     0xb85cf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb85cf4: mov             x1, x0
    // 0xb85cf8: ldur            x0, [fp, #-0x28]
    // 0xb85cfc: stur            x1, [fp, #-0x38]
    // 0xb85d00: StoreField: r1->field_f = r0
    //     0xb85d00: stur            w0, [x1, #0xf]
    // 0xb85d04: r0 = 6
    //     0xb85d04: movz            x0, #0x6
    // 0xb85d08: StoreField: r1->field_b = r0
    //     0xb85d08: stur            w0, [x1, #0xb]
    // 0xb85d0c: r0 = Row()
    //     0xb85d0c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb85d10: mov             x1, x0
    // 0xb85d14: r0 = Instance_Axis
    //     0xb85d14: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb85d18: StoreField: r1->field_f = r0
    //     0xb85d18: stur            w0, [x1, #0xf]
    // 0xb85d1c: r4 = Instance_MainAxisAlignment
    //     0xb85d1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb85d20: ldr             x4, [x4, #0xa08]
    // 0xb85d24: StoreField: r1->field_13 = r4
    //     0xb85d24: stur            w4, [x1, #0x13]
    // 0xb85d28: r0 = Instance_MainAxisSize
    //     0xb85d28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb85d2c: ldr             x0, [x0, #0xa10]
    // 0xb85d30: ArrayStore: r1[0] = r0  ; List_4
    //     0xb85d30: stur            w0, [x1, #0x17]
    // 0xb85d34: r0 = Instance_CrossAxisAlignment
    //     0xb85d34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb85d38: ldr             x0, [x0, #0xa18]
    // 0xb85d3c: StoreField: r1->field_1b = r0
    //     0xb85d3c: stur            w0, [x1, #0x1b]
    // 0xb85d40: r5 = Instance_VerticalDirection
    //     0xb85d40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb85d44: ldr             x5, [x5, #0xa20]
    // 0xb85d48: StoreField: r1->field_23 = r5
    //     0xb85d48: stur            w5, [x1, #0x23]
    // 0xb85d4c: r6 = Instance_Clip
    //     0xb85d4c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb85d50: ldr             x6, [x6, #0x38]
    // 0xb85d54: StoreField: r1->field_2b = r6
    //     0xb85d54: stur            w6, [x1, #0x2b]
    // 0xb85d58: StoreField: r1->field_2f = rZR
    //     0xb85d58: stur            xzr, [x1, #0x2f]
    // 0xb85d5c: ldur            x0, [fp, #-0x38]
    // 0xb85d60: StoreField: r1->field_b = r0
    //     0xb85d60: stur            w0, [x1, #0xb]
    // 0xb85d64: mov             x0, x1
    // 0xb85d68: ldur            x1, [fp, #-0x20]
    // 0xb85d6c: ArrayStore: r1[7] = r0  ; List_4
    //     0xb85d6c: add             x25, x1, #0x2b
    //     0xb85d70: str             w0, [x25]
    //     0xb85d74: tbz             w0, #0, #0xb85d90
    //     0xb85d78: ldurb           w16, [x1, #-1]
    //     0xb85d7c: ldurb           w17, [x0, #-1]
    //     0xb85d80: and             x16, x17, x16, lsr #2
    //     0xb85d84: tst             x16, HEAP, lsr #32
    //     0xb85d88: b.eq            #0xb85d90
    //     0xb85d8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb85d90: ldur            x0, [fp, #-0x20]
    // 0xb85d94: r16 = Instance_SizedBox
    //     0xb85d94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb85d98: ldr             x16, [x16, #0x8f0]
    // 0xb85d9c: StoreField: r0->field_2f = r16
    //     0xb85d9c: stur            w16, [x0, #0x2f]
    // 0xb85da0: ldur            x7, [fp, #-8]
    // 0xb85da4: LoadField: r1 = r7->field_f
    //     0xb85da4: ldur            w1, [x7, #0xf]
    // 0xb85da8: DecompressPointer r1
    //     0xb85da8: add             x1, x1, HEAP, lsl #32
    // 0xb85dac: ldur            x8, [fp, #-0x10]
    // 0xb85db0: LoadField: r2 = r8->field_f
    //     0xb85db0: ldur            w2, [x8, #0xf]
    // 0xb85db4: DecompressPointer r2
    //     0xb85db4: add             x2, x2, HEAP, lsl #32
    // 0xb85db8: ldur            x3, [fp, #-0x30]
    // 0xb85dbc: r0 = _buildRulesList()
    //     0xb85dbc: bl              #0xa81bec  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList
    // 0xb85dc0: ldur            x1, [fp, #-0x20]
    // 0xb85dc4: ArrayStore: r1[9] = r0  ; List_4
    //     0xb85dc4: add             x25, x1, #0x33
    //     0xb85dc8: str             w0, [x25]
    //     0xb85dcc: tbz             w0, #0, #0xb85de8
    //     0xb85dd0: ldurb           w16, [x1, #-1]
    //     0xb85dd4: ldurb           w17, [x0, #-1]
    //     0xb85dd8: and             x16, x17, x16, lsr #2
    //     0xb85ddc: tst             x16, HEAP, lsr #32
    //     0xb85de0: b.eq            #0xb85de8
    //     0xb85de4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb85de8: r1 = <Widget>
    //     0xb85de8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb85dec: r0 = AllocateGrowableArray()
    //     0xb85dec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb85df0: mov             x1, x0
    // 0xb85df4: ldur            x0, [fp, #-0x20]
    // 0xb85df8: stur            x1, [fp, #-0x28]
    // 0xb85dfc: StoreField: r1->field_f = r0
    //     0xb85dfc: stur            w0, [x1, #0xf]
    // 0xb85e00: r0 = 20
    //     0xb85e00: movz            x0, #0x14
    // 0xb85e04: StoreField: r1->field_b = r0
    //     0xb85e04: stur            w0, [x1, #0xb]
    // 0xb85e08: r0 = Column()
    //     0xb85e08: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb85e0c: mov             x1, x0
    // 0xb85e10: r0 = Instance_Axis
    //     0xb85e10: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb85e14: stur            x1, [fp, #-0x20]
    // 0xb85e18: StoreField: r1->field_f = r0
    //     0xb85e18: stur            w0, [x1, #0xf]
    // 0xb85e1c: r0 = Instance_MainAxisAlignment
    //     0xb85e1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb85e20: ldr             x0, [x0, #0xa08]
    // 0xb85e24: StoreField: r1->field_13 = r0
    //     0xb85e24: stur            w0, [x1, #0x13]
    // 0xb85e28: r0 = Instance_MainAxisSize
    //     0xb85e28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb85e2c: ldr             x0, [x0, #0xdd0]
    // 0xb85e30: ArrayStore: r1[0] = r0  ; List_4
    //     0xb85e30: stur            w0, [x1, #0x17]
    // 0xb85e34: r0 = Instance_CrossAxisAlignment
    //     0xb85e34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb85e38: ldr             x0, [x0, #0x890]
    // 0xb85e3c: StoreField: r1->field_1b = r0
    //     0xb85e3c: stur            w0, [x1, #0x1b]
    // 0xb85e40: r0 = Instance_VerticalDirection
    //     0xb85e40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb85e44: ldr             x0, [x0, #0xa20]
    // 0xb85e48: StoreField: r1->field_23 = r0
    //     0xb85e48: stur            w0, [x1, #0x23]
    // 0xb85e4c: r0 = Instance_Clip
    //     0xb85e4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb85e50: ldr             x0, [x0, #0x38]
    // 0xb85e54: StoreField: r1->field_2b = r0
    //     0xb85e54: stur            w0, [x1, #0x2b]
    // 0xb85e58: StoreField: r1->field_2f = rZR
    //     0xb85e58: stur            xzr, [x1, #0x2f]
    // 0xb85e5c: ldur            x0, [fp, #-0x28]
    // 0xb85e60: StoreField: r1->field_b = r0
    //     0xb85e60: stur            w0, [x1, #0xb]
    // 0xb85e64: r0 = AlertDialog()
    //     0xb85e64: bl              #0x99db14  ; AllocateAlertDialogStub -> AlertDialog (size=0x50)
    // 0xb85e68: mov             x1, x0
    // 0xb85e6c: ldur            x0, [fp, #-0x20]
    // 0xb85e70: stur            x1, [fp, #-0x28]
    // 0xb85e74: StoreField: r1->field_f = r0
    //     0xb85e74: stur            w0, [x1, #0xf]
    // 0xb85e78: ldur            x0, [fp, #-0x18]
    // 0xb85e7c: StoreField: r1->field_3f = r0
    //     0xb85e7c: stur            w0, [x1, #0x3f]
    // 0xb85e80: r0 = false
    //     0xb85e80: add             x0, NULL, #0x30  ; false
    // 0xb85e84: StoreField: r1->field_4b = r0
    //     0xb85e84: stur            w0, [x1, #0x4b]
    // 0xb85e88: ldur            d0, [fp, #-0x58]
    // 0xb85e8c: r2 = inline_Allocate_Double()
    //     0xb85e8c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb85e90: add             x2, x2, #0x10
    //     0xb85e94: cmp             x3, x2
    //     0xb85e98: b.ls            #0xb8615c
    //     0xb85e9c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb85ea0: sub             x2, x2, #0xf
    //     0xb85ea4: movz            x3, #0xe15c
    //     0xb85ea8: movk            x3, #0x3, lsl #16
    //     0xb85eac: stur            x3, [x2, #-1]
    // 0xb85eb0: StoreField: r2->field_7 = d0
    //     0xb85eb0: stur            d0, [x2, #7]
    // 0xb85eb4: stur            x2, [fp, #-0x18]
    // 0xb85eb8: r0 = SizedBox()
    //     0xb85eb8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb85ebc: mov             x1, x0
    // 0xb85ec0: ldur            x0, [fp, #-0x18]
    // 0xb85ec4: stur            x1, [fp, #-0x20]
    // 0xb85ec8: StoreField: r1->field_f = r0
    //     0xb85ec8: stur            w0, [x1, #0xf]
    // 0xb85ecc: ldur            x0, [fp, #-0x28]
    // 0xb85ed0: StoreField: r1->field_b = r0
    //     0xb85ed0: stur            w0, [x1, #0xb]
    // 0xb85ed4: r0 = Center()
    //     0xb85ed4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb85ed8: mov             x1, x0
    // 0xb85edc: r0 = Instance_Alignment
    //     0xb85edc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb85ee0: ldr             x0, [x0, #0xb10]
    // 0xb85ee4: stur            x1, [fp, #-0x18]
    // 0xb85ee8: StoreField: r1->field_f = r0
    //     0xb85ee8: stur            w0, [x1, #0xf]
    // 0xb85eec: ldur            x0, [fp, #-0x20]
    // 0xb85ef0: StoreField: r1->field_b = r0
    //     0xb85ef0: stur            w0, [x1, #0xb]
    // 0xb85ef4: r0 = SvgPicture()
    //     0xb85ef4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb85ef8: mov             x1, x0
    // 0xb85efc: r2 = "assets/images/gift-icon-popup.svg"
    //     0xb85efc: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xb85f00: ldr             x2, [x2, #0x8e8]
    // 0xb85f04: stur            x0, [fp, #-0x20]
    // 0xb85f08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb85f08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb85f0c: r0 = SvgPicture.asset()
    //     0xb85f0c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb85f10: ldur            x0, [fp, #-8]
    // 0xb85f14: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb85f14: ldur            w2, [x0, #0x17]
    // 0xb85f18: DecompressPointer r2
    //     0xb85f18: add             x2, x2, HEAP, lsl #32
    // 0xb85f1c: stur            x2, [fp, #-0x28]
    // 0xb85f20: r1 = <StackParentData>
    //     0xb85f20: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb85f24: ldr             x1, [x1, #0x8e0]
    // 0xb85f28: r0 = Positioned()
    //     0xb85f28: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb85f2c: mov             x3, x0
    // 0xb85f30: ldur            x0, [fp, #-0x28]
    // 0xb85f34: stur            x3, [fp, #-8]
    // 0xb85f38: ArrayStore: r3[0] = r0  ; List_4
    //     0xb85f38: stur            w0, [x3, #0x17]
    // 0xb85f3c: ldur            x1, [fp, #-0x20]
    // 0xb85f40: StoreField: r3->field_b = r1
    //     0xb85f40: stur            w1, [x3, #0xb]
    // 0xb85f44: r1 = Null
    //     0xb85f44: mov             x1, NULL
    // 0xb85f48: r2 = 4
    //     0xb85f48: movz            x2, #0x4
    // 0xb85f4c: r0 = AllocateArray()
    //     0xb85f4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb85f50: mov             x2, x0
    // 0xb85f54: ldur            x0, [fp, #-0x18]
    // 0xb85f58: stur            x2, [fp, #-0x20]
    // 0xb85f5c: StoreField: r2->field_f = r0
    //     0xb85f5c: stur            w0, [x2, #0xf]
    // 0xb85f60: ldur            x0, [fp, #-8]
    // 0xb85f64: StoreField: r2->field_13 = r0
    //     0xb85f64: stur            w0, [x2, #0x13]
    // 0xb85f68: r1 = <Widget>
    //     0xb85f68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb85f6c: r0 = AllocateGrowableArray()
    //     0xb85f6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb85f70: mov             x1, x0
    // 0xb85f74: ldur            x0, [fp, #-0x20]
    // 0xb85f78: stur            x1, [fp, #-8]
    // 0xb85f7c: StoreField: r1->field_f = r0
    //     0xb85f7c: stur            w0, [x1, #0xf]
    // 0xb85f80: r2 = 4
    //     0xb85f80: movz            x2, #0x4
    // 0xb85f84: StoreField: r1->field_b = r2
    //     0xb85f84: stur            w2, [x1, #0xb]
    // 0xb85f88: r0 = Stack()
    //     0xb85f88: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb85f8c: mov             x3, x0
    // 0xb85f90: r0 = Instance_Alignment
    //     0xb85f90: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb85f94: ldr             x0, [x0, #0xce0]
    // 0xb85f98: stur            x3, [fp, #-0x18]
    // 0xb85f9c: StoreField: r3->field_f = r0
    //     0xb85f9c: stur            w0, [x3, #0xf]
    // 0xb85fa0: r4 = Instance_StackFit
    //     0xb85fa0: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb85fa4: ldr             x4, [x4, #0xfa8]
    // 0xb85fa8: ArrayStore: r3[0] = r4  ; List_4
    //     0xb85fa8: stur            w4, [x3, #0x17]
    // 0xb85fac: r5 = Instance_Clip
    //     0xb85fac: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb85fb0: ldr             x5, [x5, #0x7e0]
    // 0xb85fb4: StoreField: r3->field_1b = r5
    //     0xb85fb4: stur            w5, [x3, #0x1b]
    // 0xb85fb8: ldur            x1, [fp, #-8]
    // 0xb85fbc: StoreField: r3->field_b = r1
    //     0xb85fbc: stur            w1, [x3, #0xb]
    // 0xb85fc0: ldur            x2, [fp, #-0x10]
    // 0xb85fc4: r1 = Function '<anonymous closure>':.
    //     0xb85fc4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55690] AnonymousClosure: (0x99db20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0xb85fc8: ldr             x1, [x1, #0x690]
    // 0xb85fcc: r0 = AllocateClosure()
    //     0xb85fcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb85fd0: stur            x0, [fp, #-8]
    // 0xb85fd4: r0 = IconButton()
    //     0xb85fd4: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xb85fd8: mov             x2, x0
    // 0xb85fdc: ldur            x0, [fp, #-8]
    // 0xb85fe0: stur            x2, [fp, #-0x10]
    // 0xb85fe4: StoreField: r2->field_3b = r0
    //     0xb85fe4: stur            w0, [x2, #0x3b]
    // 0xb85fe8: r0 = false
    //     0xb85fe8: add             x0, NULL, #0x30  ; false
    // 0xb85fec: StoreField: r2->field_4f = r0
    //     0xb85fec: stur            w0, [x2, #0x4f]
    // 0xb85ff0: r0 = Instance_Icon
    //     0xb85ff0: add             x0, PP, #0x52, lsl #12  ; [pp+0x528f8] Obj!Icon@d65d71
    //     0xb85ff4: ldr             x0, [x0, #0x8f8]
    // 0xb85ff8: StoreField: r2->field_1f = r0
    //     0xb85ff8: stur            w0, [x2, #0x1f]
    // 0xb85ffc: r0 = Instance__IconButtonVariant
    //     0xb85ffc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xb86000: ldr             x0, [x0, #0x900]
    // 0xb86004: StoreField: r2->field_6b = r0
    //     0xb86004: stur            w0, [x2, #0x6b]
    // 0xb86008: r1 = <StackParentData>
    //     0xb86008: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb8600c: ldr             x1, [x1, #0x8e0]
    // 0xb86010: r0 = Positioned()
    //     0xb86010: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb86014: mov             x3, x0
    // 0xb86018: ldur            x0, [fp, #-0x28]
    // 0xb8601c: stur            x3, [fp, #-8]
    // 0xb86020: ArrayStore: r3[0] = r0  ; List_4
    //     0xb86020: stur            w0, [x3, #0x17]
    // 0xb86024: r0 = 50.000000
    //     0xb86024: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xb86028: ldr             x0, [x0, #0xa90]
    // 0xb8602c: StoreField: r3->field_1b = r0
    //     0xb8602c: stur            w0, [x3, #0x1b]
    // 0xb86030: ldur            x0, [fp, #-0x10]
    // 0xb86034: StoreField: r3->field_b = r0
    //     0xb86034: stur            w0, [x3, #0xb]
    // 0xb86038: r1 = Null
    //     0xb86038: mov             x1, NULL
    // 0xb8603c: r2 = 4
    //     0xb8603c: movz            x2, #0x4
    // 0xb86040: r0 = AllocateArray()
    //     0xb86040: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb86044: mov             x2, x0
    // 0xb86048: ldur            x0, [fp, #-0x18]
    // 0xb8604c: stur            x2, [fp, #-0x10]
    // 0xb86050: StoreField: r2->field_f = r0
    //     0xb86050: stur            w0, [x2, #0xf]
    // 0xb86054: ldur            x0, [fp, #-8]
    // 0xb86058: StoreField: r2->field_13 = r0
    //     0xb86058: stur            w0, [x2, #0x13]
    // 0xb8605c: r1 = <Widget>
    //     0xb8605c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb86060: r0 = AllocateGrowableArray()
    //     0xb86060: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb86064: mov             x1, x0
    // 0xb86068: ldur            x0, [fp, #-0x10]
    // 0xb8606c: stur            x1, [fp, #-8]
    // 0xb86070: StoreField: r1->field_f = r0
    //     0xb86070: stur            w0, [x1, #0xf]
    // 0xb86074: r0 = 4
    //     0xb86074: movz            x0, #0x4
    // 0xb86078: StoreField: r1->field_b = r0
    //     0xb86078: stur            w0, [x1, #0xb]
    // 0xb8607c: r0 = Stack()
    //     0xb8607c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb86080: mov             x1, x0
    // 0xb86084: r0 = Instance_Alignment
    //     0xb86084: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb86088: ldr             x0, [x0, #0xce0]
    // 0xb8608c: stur            x1, [fp, #-0x10]
    // 0xb86090: StoreField: r1->field_f = r0
    //     0xb86090: stur            w0, [x1, #0xf]
    // 0xb86094: r0 = Instance_StackFit
    //     0xb86094: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb86098: ldr             x0, [x0, #0xfa8]
    // 0xb8609c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8609c: stur            w0, [x1, #0x17]
    // 0xb860a0: r0 = Instance_Clip
    //     0xb860a0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb860a4: ldr             x0, [x0, #0x7e0]
    // 0xb860a8: StoreField: r1->field_1b = r0
    //     0xb860a8: stur            w0, [x1, #0x1b]
    // 0xb860ac: ldur            x0, [fp, #-8]
    // 0xb860b0: StoreField: r1->field_b = r0
    //     0xb860b0: stur            w0, [x1, #0xb]
    // 0xb860b4: ldur            d0, [fp, #-0x50]
    // 0xb860b8: r0 = inline_Allocate_Double()
    //     0xb860b8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb860bc: add             x0, x0, #0x10
    //     0xb860c0: cmp             x2, x0
    //     0xb860c4: b.ls            #0xb86178
    //     0xb860c8: str             x0, [THR, #0x50]  ; THR::top
    //     0xb860cc: sub             x0, x0, #0xf
    //     0xb860d0: movz            x2, #0xe15c
    //     0xb860d4: movk            x2, #0x3, lsl #16
    //     0xb860d8: stur            x2, [x0, #-1]
    // 0xb860dc: StoreField: r0->field_7 = d0
    //     0xb860dc: stur            d0, [x0, #7]
    // 0xb860e0: stur            x0, [fp, #-8]
    // 0xb860e4: r0 = Container()
    //     0xb860e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb860e8: stur            x0, [fp, #-0x18]
    // 0xb860ec: r16 = Instance_Color
    //     0xb860ec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb860f0: ldr             x16, [x16, #0xf88]
    // 0xb860f4: ldur            lr, [fp, #-8]
    // 0xb860f8: stp             lr, x16, [SP, #8]
    // 0xb860fc: ldur            x16, [fp, #-0x10]
    // 0xb86100: str             x16, [SP]
    // 0xb86104: mov             x1, x0
    // 0xb86108: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, width, 0x2, null]
    //     0xb86108: add             x4, PP, #0x52, lsl #12  ; [pp+0x52908] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "width", 0x2, Null]
    //     0xb8610c: ldr             x4, [x4, #0x908]
    // 0xb86110: r0 = Container()
    //     0xb86110: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb86114: ldur            x0, [fp, #-0x18]
    // 0xb86118: LeaveFrame
    //     0xb86118: mov             SP, fp
    //     0xb8611c: ldp             fp, lr, [SP], #0x10
    // 0xb86120: ret
    //     0xb86120: ret             
    // 0xb86124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86124: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86128: b               #0xb85828
    // 0xb8612c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb8612c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb86130: stp             q0, q2, [SP, #-0x20]!
    // 0xb86134: r0 = AllocateDouble()
    //     0xb86134: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb86138: ldp             q0, q2, [SP], #0x20
    // 0xb8613c: b               #0xb85a5c
    // 0xb86140: SaveReg d2
    //     0xb86140: str             q2, [SP, #-0x10]!
    // 0xb86144: SaveReg r0
    //     0xb86144: str             x0, [SP, #-8]!
    // 0xb86148: r0 = AllocateDouble()
    //     0xb86148: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb8614c: mov             x1, x0
    // 0xb86150: RestoreReg r0
    //     0xb86150: ldr             x0, [SP], #8
    // 0xb86154: RestoreReg d2
    //     0xb86154: ldr             q2, [SP], #0x10
    // 0xb86158: b               #0xb85a88
    // 0xb8615c: SaveReg d0
    //     0xb8615c: str             q0, [SP, #-0x10]!
    // 0xb86160: stp             x0, x1, [SP, #-0x10]!
    // 0xb86164: r0 = AllocateDouble()
    //     0xb86164: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb86168: mov             x2, x0
    // 0xb8616c: ldp             x0, x1, [SP], #0x10
    // 0xb86170: RestoreReg d0
    //     0xb86170: ldr             q0, [SP], #0x10
    // 0xb86174: b               #0xb85eb0
    // 0xb86178: SaveReg d0
    //     0xb86178: str             q0, [SP, #-0x10]!
    // 0xb8617c: SaveReg r1
    //     0xb8617c: str             x1, [SP, #-8]!
    // 0xb86180: r0 = AllocateDouble()
    //     0xb86180: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb86184: RestoreReg r1
    //     0xb86184: ldr             x1, [SP], #8
    // 0xb86188: RestoreReg d0
    //     0xb86188: ldr             q0, [SP], #0x10
    // 0xb8618c: b               #0xb860dc
  }
  _ _buildPageView(/* No info */) {
    // ** addr: 0xb86190, size: 0x174
    // 0xb86190: EnterFrame
    //     0xb86190: stp             fp, lr, [SP, #-0x10]!
    //     0xb86194: mov             fp, SP
    // 0xb86198: AllocStack(0x40)
    //     0xb86198: sub             SP, SP, #0x40
    // 0xb8619c: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb8619c: mov             x0, x1
    //     0xb861a0: stur            x1, [fp, #-8]
    //     0xb861a4: mov             x1, x2
    //     0xb861a8: stur            x2, [fp, #-0x10]
    // 0xb861ac: CheckStackOverflow
    //     0xb861ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb861b0: cmp             SP, x16
    //     0xb861b4: b.ls            #0xb862e0
    // 0xb861b8: r1 = 1
    //     0xb861b8: movz            x1, #0x1
    // 0xb861bc: r0 = AllocateContext()
    //     0xb861bc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb861c0: mov             x2, x0
    // 0xb861c4: ldur            x0, [fp, #-8]
    // 0xb861c8: stur            x2, [fp, #-0x18]
    // 0xb861cc: StoreField: r2->field_f = r0
    //     0xb861cc: stur            w0, [x2, #0xf]
    // 0xb861d0: LoadField: r1 = r0->field_2f
    //     0xb861d0: ldur            w1, [x0, #0x2f]
    // 0xb861d4: DecompressPointer r1
    //     0xb861d4: add             x1, x1, HEAP, lsl #32
    // 0xb861d8: cmp             w1, NULL
    // 0xb861dc: b.ne            #0xb86208
    // 0xb861e0: ldur            x1, [fp, #-0x10]
    // 0xb861e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb861e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb861e8: r0 = _of()
    //     0xb861e8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb861ec: LoadField: r1 = r0->field_7
    //     0xb861ec: ldur            w1, [x0, #7]
    // 0xb861f0: DecompressPointer r1
    //     0xb861f0: add             x1, x1, HEAP, lsl #32
    // 0xb861f4: LoadField: d0 = r1->field_f
    //     0xb861f4: ldur            d0, [x1, #0xf]
    // 0xb861f8: d1 = 0.400000
    //     0xb861f8: ldr             d1, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb861fc: fmul            d2, d0, d1
    // 0xb86200: mov             v0.16b, v2.16b
    // 0xb86204: b               #0xb8620c
    // 0xb86208: LoadField: d0 = r1->field_7
    //     0xb86208: ldur            d0, [x1, #7]
    // 0xb8620c: ldur            x0, [fp, #-8]
    // 0xb86210: stur            d0, [fp, #-0x30]
    // 0xb86214: LoadField: r1 = r0->field_23
    //     0xb86214: ldur            w1, [x0, #0x23]
    // 0xb86218: DecompressPointer r1
    //     0xb86218: add             x1, x1, HEAP, lsl #32
    // 0xb8621c: LoadField: r3 = r1->field_b
    //     0xb8621c: ldur            w3, [x1, #0xb]
    // 0xb86220: stur            x3, [fp, #-0x20]
    // 0xb86224: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xb86224: ldur            w4, [x0, #0x17]
    // 0xb86228: DecompressPointer r4
    //     0xb86228: add             x4, x4, HEAP, lsl #32
    // 0xb8622c: ldur            x2, [fp, #-0x18]
    // 0xb86230: stur            x4, [fp, #-0x10]
    // 0xb86234: r1 = Function '<anonymous closure>':.
    //     0xb86234: add             x1, PP, #0x55, lsl #12  ; [pp+0x556a8] AnonymousClosure: (0xb86c48), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView (0xb86190)
    //     0xb86238: ldr             x1, [x1, #0x6a8]
    // 0xb8623c: r0 = AllocateClosure()
    //     0xb8623c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86240: ldur            x2, [fp, #-0x18]
    // 0xb86244: r1 = Function '<anonymous closure>':.
    //     0xb86244: add             x1, PP, #0x55, lsl #12  ; [pp+0x556b0] AnonymousClosure: (0xb86304), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildPageView (0xb86190)
    //     0xb86248: ldr             x1, [x1, #0x6b0]
    // 0xb8624c: stur            x0, [fp, #-8]
    // 0xb86250: r0 = AllocateClosure()
    //     0xb86250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86254: stur            x0, [fp, #-0x18]
    // 0xb86258: r0 = PageView()
    //     0xb86258: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb8625c: stur            x0, [fp, #-0x28]
    // 0xb86260: r16 = Instance_BouncingScrollPhysics
    //     0xb86260: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb86264: ldr             x16, [x16, #0x890]
    // 0xb86268: ldur            lr, [fp, #-0x10]
    // 0xb8626c: stp             lr, x16, [SP]
    // 0xb86270: mov             x1, x0
    // 0xb86274: ldur            x2, [fp, #-0x18]
    // 0xb86278: ldur            x3, [fp, #-0x20]
    // 0xb8627c: ldur            x5, [fp, #-8]
    // 0xb86280: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb86280: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb86284: ldr             x4, [x4, #0xe40]
    // 0xb86288: r0 = PageView.builder()
    //     0xb86288: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb8628c: r0 = SizedBox()
    //     0xb8628c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb86290: r1 = inf
    //     0xb86290: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb86294: ldr             x1, [x1, #0x9f8]
    // 0xb86298: StoreField: r0->field_f = r1
    //     0xb86298: stur            w1, [x0, #0xf]
    // 0xb8629c: ldur            d0, [fp, #-0x30]
    // 0xb862a0: r1 = inline_Allocate_Double()
    //     0xb862a0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb862a4: add             x1, x1, #0x10
    //     0xb862a8: cmp             x2, x1
    //     0xb862ac: b.ls            #0xb862e8
    //     0xb862b0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb862b4: sub             x1, x1, #0xf
    //     0xb862b8: movz            x2, #0xe15c
    //     0xb862bc: movk            x2, #0x3, lsl #16
    //     0xb862c0: stur            x2, [x1, #-1]
    // 0xb862c4: StoreField: r1->field_7 = d0
    //     0xb862c4: stur            d0, [x1, #7]
    // 0xb862c8: StoreField: r0->field_13 = r1
    //     0xb862c8: stur            w1, [x0, #0x13]
    // 0xb862cc: ldur            x1, [fp, #-0x28]
    // 0xb862d0: StoreField: r0->field_b = r1
    //     0xb862d0: stur            w1, [x0, #0xb]
    // 0xb862d4: LeaveFrame
    //     0xb862d4: mov             SP, fp
    //     0xb862d8: ldp             fp, lr, [SP], #0x10
    // 0xb862dc: ret
    //     0xb862dc: ret             
    // 0xb862e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb862e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb862e4: b               #0xb861b8
    // 0xb862e8: SaveReg d0
    //     0xb862e8: str             q0, [SP, #-0x10]!
    // 0xb862ec: SaveReg r0
    //     0xb862ec: str             x0, [SP, #-8]!
    // 0xb862f0: r0 = AllocateDouble()
    //     0xb862f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb862f4: mov             x1, x0
    // 0xb862f8: RestoreReg r0
    //     0xb862f8: ldr             x0, [SP], #8
    // 0xb862fc: RestoreReg d0
    //     0xb862fc: ldr             q0, [SP], #0x10
    // 0xb86300: b               #0xb862c4
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb86304, size: 0x148
    // 0xb86304: EnterFrame
    //     0xb86304: stp             fp, lr, [SP, #-0x10]!
    //     0xb86308: mov             fp, SP
    // 0xb8630c: AllocStack(0x28)
    //     0xb8630c: sub             SP, SP, #0x28
    // 0xb86310: SetupParameters()
    //     0xb86310: ldr             x0, [fp, #0x20]
    //     0xb86314: ldur            w2, [x0, #0x17]
    //     0xb86318: add             x2, x2, HEAP, lsl #32
    //     0xb8631c: stur            x2, [fp, #-0x10]
    // 0xb86320: CheckStackOverflow
    //     0xb86320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86324: cmp             SP, x16
    //     0xb86328: b.ls            #0xb8643c
    // 0xb8632c: LoadField: r0 = r2->field_f
    //     0xb8632c: ldur            w0, [x2, #0xf]
    // 0xb86330: DecompressPointer r0
    //     0xb86330: add             x0, x0, HEAP, lsl #32
    // 0xb86334: LoadField: r3 = r0->field_23
    //     0xb86334: ldur            w3, [x0, #0x23]
    // 0xb86338: DecompressPointer r3
    //     0xb86338: add             x3, x3, HEAP, lsl #32
    // 0xb8633c: LoadField: r0 = r3->field_b
    //     0xb8633c: ldur            w0, [x3, #0xb]
    // 0xb86340: ldr             x1, [fp, #0x10]
    // 0xb86344: r4 = LoadInt32Instr(r1)
    //     0xb86344: sbfx            x4, x1, #1, #0x1f
    //     0xb86348: tbz             w1, #0, #0xb86350
    //     0xb8634c: ldur            x4, [x1, #7]
    // 0xb86350: stur            x4, [fp, #-8]
    // 0xb86354: r1 = LoadInt32Instr(r0)
    //     0xb86354: sbfx            x1, x0, #1, #0x1f
    // 0xb86358: mov             x0, x1
    // 0xb8635c: mov             x1, x4
    // 0xb86360: cmp             x1, x0
    // 0xb86364: b.hs            #0xb86444
    // 0xb86368: LoadField: r0 = r3->field_f
    //     0xb86368: ldur            w0, [x3, #0xf]
    // 0xb8636c: DecompressPointer r0
    //     0xb8636c: add             x0, x0, HEAP, lsl #32
    // 0xb86370: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb86370: add             x16, x0, x4, lsl #2
    //     0xb86374: ldur            w1, [x16, #0xf]
    // 0xb86378: DecompressPointer r1
    //     0xb86378: add             x1, x1, HEAP, lsl #32
    // 0xb8637c: LoadField: r0 = r1->field_1f
    //     0xb8637c: ldur            w0, [x1, #0x1f]
    // 0xb86380: DecompressPointer r0
    //     0xb86380: add             x0, x0, HEAP, lsl #32
    // 0xb86384: r1 = LoadClassIdInstr(r0)
    //     0xb86384: ldur            x1, [x0, #-1]
    //     0xb86388: ubfx            x1, x1, #0xc, #0x14
    // 0xb8638c: r16 = "video"
    //     0xb8638c: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0xb86390: ldr             x16, [x16, #0xb50]
    // 0xb86394: stp             x16, x0, [SP]
    // 0xb86398: mov             x0, x1
    // 0xb8639c: mov             lr, x0
    // 0xb863a0: ldr             lr, [x21, lr, lsl #3]
    // 0xb863a4: blr             lr
    // 0xb863a8: tbnz            w0, #4, #0xb8640c
    // 0xb863ac: ldur            x0, [fp, #-0x10]
    // 0xb863b0: ldur            x2, [fp, #-8]
    // 0xb863b4: LoadField: r1 = r0->field_f
    //     0xb863b4: ldur            w1, [x0, #0xf]
    // 0xb863b8: DecompressPointer r1
    //     0xb863b8: add             x1, x1, HEAP, lsl #32
    // 0xb863bc: LoadField: r3 = r1->field_23
    //     0xb863bc: ldur            w3, [x1, #0x23]
    // 0xb863c0: DecompressPointer r3
    //     0xb863c0: add             x3, x3, HEAP, lsl #32
    // 0xb863c4: LoadField: r0 = r3->field_b
    //     0xb863c4: ldur            w0, [x3, #0xb]
    // 0xb863c8: r1 = LoadInt32Instr(r0)
    //     0xb863c8: sbfx            x1, x0, #1, #0x1f
    // 0xb863cc: mov             x0, x1
    // 0xb863d0: mov             x1, x2
    // 0xb863d4: cmp             x1, x0
    // 0xb863d8: b.hs            #0xb86448
    // 0xb863dc: LoadField: r0 = r3->field_f
    //     0xb863dc: ldur            w0, [x3, #0xf]
    // 0xb863e0: DecompressPointer r0
    //     0xb863e0: add             x0, x0, HEAP, lsl #32
    // 0xb863e4: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb863e4: add             x16, x0, x2, lsl #2
    //     0xb863e8: ldur            w1, [x16, #0xf]
    // 0xb863ec: DecompressPointer r1
    //     0xb863ec: add             x1, x1, HEAP, lsl #32
    // 0xb863f0: stur            x1, [fp, #-0x18]
    // 0xb863f4: r0 = ProductVideoMediaCarousel()
    //     0xb863f4: bl              #0xb86c3c  ; AllocateProductVideoMediaCarouselStub -> ProductVideoMediaCarousel (size=0x10)
    // 0xb863f8: mov             x1, x0
    // 0xb863fc: ldur            x0, [fp, #-0x18]
    // 0xb86400: StoreField: r1->field_b = r0
    //     0xb86400: stur            w0, [x1, #0xb]
    // 0xb86404: mov             x0, x1
    // 0xb86408: b               #0xb86430
    // 0xb8640c: ldur            x0, [fp, #-0x10]
    // 0xb86410: ldur            x2, [fp, #-8]
    // 0xb86414: LoadField: r1 = r0->field_f
    //     0xb86414: ldur            w1, [x0, #0xf]
    // 0xb86418: DecompressPointer r1
    //     0xb86418: add             x1, x1, HEAP, lsl #32
    // 0xb8641c: LoadField: r0 = r1->field_23
    //     0xb8641c: ldur            w0, [x1, #0x23]
    // 0xb86420: DecompressPointer r0
    //     0xb86420: add             x0, x0, HEAP, lsl #32
    // 0xb86424: mov             x3, x2
    // 0xb86428: mov             x2, x0
    // 0xb8642c: r0 = _buildImageSlider()
    //     0xb8642c: bl              #0xb8644c  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageSlider
    // 0xb86430: LeaveFrame
    //     0xb86430: mov             SP, fp
    //     0xb86434: ldp             fp, lr, [SP], #0x10
    // 0xb86438: ret
    //     0xb86438: ret             
    // 0xb8643c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8643c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86440: b               #0xb8632c
    // 0xb86444: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb86444: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb86448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb86448: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildImageSlider(/* No info */) {
    // ** addr: 0xb8644c, size: 0x234
    // 0xb8644c: EnterFrame
    //     0xb8644c: stp             fp, lr, [SP, #-0x10]!
    //     0xb86450: mov             fp, SP
    // 0xb86454: AllocStack(0x28)
    //     0xb86454: sub             SP, SP, #0x28
    // 0xb86458: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb86458: mov             x0, x1
    //     0xb8645c: stur            x1, [fp, #-8]
    //     0xb86460: mov             x1, x3
    //     0xb86464: stur            x2, [fp, #-0x10]
    //     0xb86468: stur            x3, [fp, #-0x18]
    // 0xb8646c: CheckStackOverflow
    //     0xb8646c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86470: cmp             SP, x16
    //     0xb86474: b.ls            #0xb86674
    // 0xb86478: r1 = 3
    //     0xb86478: movz            x1, #0x3
    // 0xb8647c: r0 = AllocateContext()
    //     0xb8647c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb86480: mov             x4, x0
    // 0xb86484: ldur            x3, [fp, #-8]
    // 0xb86488: stur            x4, [fp, #-0x20]
    // 0xb8648c: StoreField: r4->field_f = r3
    //     0xb8648c: stur            w3, [x4, #0xf]
    // 0xb86490: ldur            x2, [fp, #-0x10]
    // 0xb86494: StoreField: r4->field_13 = r2
    //     0xb86494: stur            w2, [x4, #0x13]
    // 0xb86498: ldur            x5, [fp, #-0x18]
    // 0xb8649c: r0 = BoxInt64Instr(r5)
    //     0xb8649c: sbfiz           x0, x5, #1, #0x1f
    //     0xb864a0: cmp             x5, x0, asr #1
    //     0xb864a4: b.eq            #0xb864b0
    //     0xb864a8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb864ac: stur            x5, [x0, #7]
    // 0xb864b0: ArrayStore: r4[0] = r0  ; List_4
    //     0xb864b0: stur            w0, [x4, #0x17]
    // 0xb864b4: LoadField: r0 = r2->field_b
    //     0xb864b4: ldur            w0, [x2, #0xb]
    // 0xb864b8: r1 = LoadInt32Instr(r0)
    //     0xb864b8: sbfx            x1, x0, #1, #0x1f
    // 0xb864bc: cmp             x5, x1
    // 0xb864c0: b.lt            #0xb864d4
    // 0xb864c4: r0 = Instance_SizedBox
    //     0xb864c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb864c8: LeaveFrame
    //     0xb864c8: mov             SP, fp
    //     0xb864cc: ldp             fp, lr, [SP], #0x10
    // 0xb864d0: ret
    //     0xb864d0: ret             
    // 0xb864d4: LoadField: r2 = r3->field_23
    //     0xb864d4: ldur            w2, [x3, #0x23]
    // 0xb864d8: DecompressPointer r2
    //     0xb864d8: add             x2, x2, HEAP, lsl #32
    // 0xb864dc: LoadField: r0 = r2->field_b
    //     0xb864dc: ldur            w0, [x2, #0xb]
    // 0xb864e0: r1 = LoadInt32Instr(r0)
    //     0xb864e0: sbfx            x1, x0, #1, #0x1f
    // 0xb864e4: mov             x0, x1
    // 0xb864e8: mov             x1, x5
    // 0xb864ec: cmp             x1, x0
    // 0xb864f0: b.hs            #0xb8667c
    // 0xb864f4: LoadField: r0 = r2->field_f
    //     0xb864f4: ldur            w0, [x2, #0xf]
    // 0xb864f8: DecompressPointer r0
    //     0xb864f8: add             x0, x0, HEAP, lsl #32
    // 0xb864fc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb864fc: add             x16, x0, x5, lsl #2
    //     0xb86500: ldur            w1, [x16, #0xf]
    // 0xb86504: DecompressPointer r1
    //     0xb86504: add             x1, x1, HEAP, lsl #32
    // 0xb86508: LoadField: r0 = r1->field_2b
    //     0xb86508: ldur            w0, [x1, #0x2b]
    // 0xb8650c: DecompressPointer r0
    //     0xb8650c: add             x0, x0, HEAP, lsl #32
    // 0xb86510: cmp             w0, NULL
    // 0xb86514: b.ne            #0xb86520
    // 0xb86518: r0 = Null
    //     0xb86518: mov             x0, NULL
    // 0xb8651c: b               #0xb8652c
    // 0xb86520: LoadField: r1 = r0->field_b
    //     0xb86520: ldur            w1, [x0, #0xb]
    // 0xb86524: DecompressPointer r1
    //     0xb86524: add             x1, x1, HEAP, lsl #32
    // 0xb86528: mov             x0, x1
    // 0xb8652c: cmp             w0, NULL
    // 0xb86530: b.ne            #0xb8653c
    // 0xb86534: r2 = ""
    //     0xb86534: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb86538: b               #0xb86540
    // 0xb8653c: mov             x2, x0
    // 0xb86540: mov             x1, x3
    // 0xb86544: r0 = _buildNetworkImage()
    //     0xb86544: bl              #0xb86898  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage
    // 0xb86548: stur            x0, [fp, #-0x10]
    // 0xb8654c: r0 = Center()
    //     0xb8654c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb86550: mov             x1, x0
    // 0xb86554: r0 = Instance_Alignment
    //     0xb86554: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb86558: ldr             x0, [x0, #0xb10]
    // 0xb8655c: stur            x1, [fp, #-0x28]
    // 0xb86560: StoreField: r1->field_f = r0
    //     0xb86560: stur            w0, [x1, #0xf]
    // 0xb86564: ldur            x0, [fp, #-0x10]
    // 0xb86568: StoreField: r1->field_b = r0
    //     0xb86568: stur            w0, [x1, #0xb]
    // 0xb8656c: r0 = SizedBox()
    //     0xb8656c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb86570: mov             x1, x0
    // 0xb86574: r0 = inf
    //     0xb86574: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb86578: ldr             x0, [x0, #0x9f8]
    // 0xb8657c: stur            x1, [fp, #-0x10]
    // 0xb86580: StoreField: r1->field_f = r0
    //     0xb86580: stur            w0, [x1, #0xf]
    // 0xb86584: StoreField: r1->field_13 = r0
    //     0xb86584: stur            w0, [x1, #0x13]
    // 0xb86588: ldur            x0, [fp, #-0x28]
    // 0xb8658c: StoreField: r1->field_b = r0
    //     0xb8658c: stur            w0, [x1, #0xb]
    // 0xb86590: r0 = InkWell()
    //     0xb86590: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb86594: mov             x3, x0
    // 0xb86598: ldur            x0, [fp, #-0x10]
    // 0xb8659c: stur            x3, [fp, #-0x28]
    // 0xb865a0: StoreField: r3->field_b = r0
    //     0xb865a0: stur            w0, [x3, #0xb]
    // 0xb865a4: ldur            x2, [fp, #-0x20]
    // 0xb865a8: r1 = Function '<anonymous closure>':.
    //     0xb865a8: add             x1, PP, #0x55, lsl #12  ; [pp+0x556b8] AnonymousClosure: (0xb86bac), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageSlider (0xb8644c)
    //     0xb865ac: ldr             x1, [x1, #0x6b8]
    // 0xb865b0: r0 = AllocateClosure()
    //     0xb865b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb865b4: mov             x1, x0
    // 0xb865b8: ldur            x0, [fp, #-0x28]
    // 0xb865bc: StoreField: r0->field_f = r1
    //     0xb865bc: stur            w1, [x0, #0xf]
    // 0xb865c0: r1 = true
    //     0xb865c0: add             x1, NULL, #0x20  ; true
    // 0xb865c4: StoreField: r0->field_43 = r1
    //     0xb865c4: stur            w1, [x0, #0x43]
    // 0xb865c8: r2 = Instance_BoxShape
    //     0xb865c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb865cc: ldr             x2, [x2, #0x80]
    // 0xb865d0: StoreField: r0->field_47 = r2
    //     0xb865d0: stur            w2, [x0, #0x47]
    // 0xb865d4: StoreField: r0->field_6f = r1
    //     0xb865d4: stur            w1, [x0, #0x6f]
    // 0xb865d8: r2 = false
    //     0xb865d8: add             x2, NULL, #0x30  ; false
    // 0xb865dc: StoreField: r0->field_73 = r2
    //     0xb865dc: stur            w2, [x0, #0x73]
    // 0xb865e0: StoreField: r0->field_83 = r1
    //     0xb865e0: stur            w1, [x0, #0x83]
    // 0xb865e4: StoreField: r0->field_7b = r2
    //     0xb865e4: stur            w2, [x0, #0x7b]
    // 0xb865e8: ldur            x1, [fp, #-8]
    // 0xb865ec: r0 = _buildCustomizationLabel()
    //     0xb865ec: bl              #0xb86680  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCustomizationLabel
    // 0xb865f0: r1 = Null
    //     0xb865f0: mov             x1, NULL
    // 0xb865f4: r2 = 4
    //     0xb865f4: movz            x2, #0x4
    // 0xb865f8: stur            x0, [fp, #-8]
    // 0xb865fc: r0 = AllocateArray()
    //     0xb865fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb86600: mov             x2, x0
    // 0xb86604: ldur            x0, [fp, #-0x28]
    // 0xb86608: stur            x2, [fp, #-0x10]
    // 0xb8660c: StoreField: r2->field_f = r0
    //     0xb8660c: stur            w0, [x2, #0xf]
    // 0xb86610: ldur            x0, [fp, #-8]
    // 0xb86614: StoreField: r2->field_13 = r0
    //     0xb86614: stur            w0, [x2, #0x13]
    // 0xb86618: r1 = <Widget>
    //     0xb86618: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8661c: r0 = AllocateGrowableArray()
    //     0xb8661c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb86620: mov             x1, x0
    // 0xb86624: ldur            x0, [fp, #-0x10]
    // 0xb86628: stur            x1, [fp, #-8]
    // 0xb8662c: StoreField: r1->field_f = r0
    //     0xb8662c: stur            w0, [x1, #0xf]
    // 0xb86630: r0 = 4
    //     0xb86630: movz            x0, #0x4
    // 0xb86634: StoreField: r1->field_b = r0
    //     0xb86634: stur            w0, [x1, #0xb]
    // 0xb86638: r0 = Stack()
    //     0xb86638: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb8663c: r1 = Instance_Alignment
    //     0xb8663c: add             x1, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb86640: ldr             x1, [x1, #0x5b8]
    // 0xb86644: StoreField: r0->field_f = r1
    //     0xb86644: stur            w1, [x0, #0xf]
    // 0xb86648: r1 = Instance_StackFit
    //     0xb86648: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb8664c: ldr             x1, [x1, #0xfa8]
    // 0xb86650: ArrayStore: r0[0] = r1  ; List_4
    //     0xb86650: stur            w1, [x0, #0x17]
    // 0xb86654: r1 = Instance_Clip
    //     0xb86654: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb86658: ldr             x1, [x1, #0x7e0]
    // 0xb8665c: StoreField: r0->field_1b = r1
    //     0xb8665c: stur            w1, [x0, #0x1b]
    // 0xb86660: ldur            x1, [fp, #-8]
    // 0xb86664: StoreField: r0->field_b = r1
    //     0xb86664: stur            w1, [x0, #0xb]
    // 0xb86668: LeaveFrame
    //     0xb86668: mov             SP, fp
    //     0xb8666c: ldp             fp, lr, [SP], #0x10
    // 0xb86670: ret
    //     0xb86670: ret             
    // 0xb86674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86678: b               #0xb86478
    // 0xb8667c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8667c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationLabel(/* No info */) {
    // ** addr: 0xb86680, size: 0x218
    // 0xb86680: EnterFrame
    //     0xb86680: stp             fp, lr, [SP, #-0x10]!
    //     0xb86684: mov             fp, SP
    // 0xb86688: AllocStack(0x30)
    //     0xb86688: sub             SP, SP, #0x30
    // 0xb8668c: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0xb8668c: mov             x0, x1
    //     0xb86690: stur            x1, [fp, #-8]
    // 0xb86694: CheckStackOverflow
    //     0xb86694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86698: cmp             SP, x16
    //     0xb8669c: b.ls            #0xb86884
    // 0xb866a0: LoadField: r1 = r0->field_b
    //     0xb866a0: ldur            w1, [x0, #0xb]
    // 0xb866a4: DecompressPointer r1
    //     0xb866a4: add             x1, x1, HEAP, lsl #32
    // 0xb866a8: cmp             w1, NULL
    // 0xb866ac: b.eq            #0xb8688c
    // 0xb866b0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb866b0: ldur            w2, [x1, #0x17]
    // 0xb866b4: DecompressPointer r2
    //     0xb866b4: add             x2, x2, HEAP, lsl #32
    // 0xb866b8: tbz             w2, #4, #0xb866cc
    // 0xb866bc: r0 = Instance_SizedBox
    //     0xb866bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb866c0: LeaveFrame
    //     0xb866c0: mov             SP, fp
    //     0xb866c4: ldp             fp, lr, [SP], #0x10
    // 0xb866c8: ret
    //     0xb866c8: ret             
    // 0xb866cc: LoadField: r1 = r0->field_f
    //     0xb866cc: ldur            w1, [x0, #0xf]
    // 0xb866d0: DecompressPointer r1
    //     0xb866d0: add             x1, x1, HEAP, lsl #32
    // 0xb866d4: cmp             w1, NULL
    // 0xb866d8: b.eq            #0xb86890
    // 0xb866dc: r0 = of()
    //     0xb866dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb866e0: LoadField: r1 = r0->field_5b
    //     0xb866e0: ldur            w1, [x0, #0x5b]
    // 0xb866e4: DecompressPointer r1
    //     0xb866e4: add             x1, x1, HEAP, lsl #32
    // 0xb866e8: r16 = <Color>
    //     0xb866e8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb866ec: ldr             x16, [x16, #0xf80]
    // 0xb866f0: stp             x1, x16, [SP]
    // 0xb866f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb866f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb866f8: r0 = all()
    //     0xb866f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb866fc: stur            x0, [fp, #-0x10]
    // 0xb86700: r0 = Radius()
    //     0xb86700: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb86704: d0 = 5.000000
    //     0xb86704: fmov            d0, #5.00000000
    // 0xb86708: stur            x0, [fp, #-0x18]
    // 0xb8670c: StoreField: r0->field_7 = d0
    //     0xb8670c: stur            d0, [x0, #7]
    // 0xb86710: StoreField: r0->field_f = d0
    //     0xb86710: stur            d0, [x0, #0xf]
    // 0xb86714: r0 = BorderRadius()
    //     0xb86714: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb86718: mov             x1, x0
    // 0xb8671c: ldur            x0, [fp, #-0x18]
    // 0xb86720: stur            x1, [fp, #-0x20]
    // 0xb86724: StoreField: r1->field_7 = r0
    //     0xb86724: stur            w0, [x1, #7]
    // 0xb86728: StoreField: r1->field_b = r0
    //     0xb86728: stur            w0, [x1, #0xb]
    // 0xb8672c: StoreField: r1->field_f = r0
    //     0xb8672c: stur            w0, [x1, #0xf]
    // 0xb86730: StoreField: r1->field_13 = r0
    //     0xb86730: stur            w0, [x1, #0x13]
    // 0xb86734: r0 = RoundedRectangleBorder()
    //     0xb86734: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb86738: mov             x1, x0
    // 0xb8673c: ldur            x0, [fp, #-0x20]
    // 0xb86740: StoreField: r1->field_b = r0
    //     0xb86740: stur            w0, [x1, #0xb]
    // 0xb86744: r0 = Instance_BorderSide
    //     0xb86744: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb86748: ldr             x0, [x0, #0xe20]
    // 0xb8674c: StoreField: r1->field_7 = r0
    //     0xb8674c: stur            w0, [x1, #7]
    // 0xb86750: r16 = <RoundedRectangleBorder>
    //     0xb86750: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb86754: ldr             x16, [x16, #0xf78]
    // 0xb86758: stp             x1, x16, [SP]
    // 0xb8675c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb8675c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb86760: r0 = all()
    //     0xb86760: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb86764: stur            x0, [fp, #-0x18]
    // 0xb86768: r0 = ButtonStyle()
    //     0xb86768: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb8676c: mov             x1, x0
    // 0xb86770: ldur            x0, [fp, #-0x10]
    // 0xb86774: stur            x1, [fp, #-0x20]
    // 0xb86778: StoreField: r1->field_b = r0
    //     0xb86778: stur            w0, [x1, #0xb]
    // 0xb8677c: ldur            x0, [fp, #-0x18]
    // 0xb86780: StoreField: r1->field_43 = r0
    //     0xb86780: stur            w0, [x1, #0x43]
    // 0xb86784: r0 = TextButtonThemeData()
    //     0xb86784: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb86788: mov             x2, x0
    // 0xb8678c: ldur            x0, [fp, #-0x20]
    // 0xb86790: stur            x2, [fp, #-0x10]
    // 0xb86794: StoreField: r2->field_7 = r0
    //     0xb86794: stur            w0, [x2, #7]
    // 0xb86798: ldur            x0, [fp, #-8]
    // 0xb8679c: LoadField: r1 = r0->field_f
    //     0xb8679c: ldur            w1, [x0, #0xf]
    // 0xb867a0: DecompressPointer r1
    //     0xb867a0: add             x1, x1, HEAP, lsl #32
    // 0xb867a4: cmp             w1, NULL
    // 0xb867a8: b.eq            #0xb86894
    // 0xb867ac: r0 = of()
    //     0xb867ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb867b0: LoadField: r1 = r0->field_87
    //     0xb867b0: ldur            w1, [x0, #0x87]
    // 0xb867b4: DecompressPointer r1
    //     0xb867b4: add             x1, x1, HEAP, lsl #32
    // 0xb867b8: LoadField: r0 = r1->field_2b
    //     0xb867b8: ldur            w0, [x1, #0x2b]
    // 0xb867bc: DecompressPointer r0
    //     0xb867bc: add             x0, x0, HEAP, lsl #32
    // 0xb867c0: r16 = 14.000000
    //     0xb867c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb867c4: ldr             x16, [x16, #0x1d8]
    // 0xb867c8: r30 = Instance_Color
    //     0xb867c8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb867cc: stp             lr, x16, [SP]
    // 0xb867d0: mov             x1, x0
    // 0xb867d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb867d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb867d8: ldr             x4, [x4, #0xaa0]
    // 0xb867dc: r0 = copyWith()
    //     0xb867dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb867e0: stur            x0, [fp, #-8]
    // 0xb867e4: r0 = Text()
    //     0xb867e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb867e8: mov             x3, x0
    // 0xb867ec: r0 = "Customisable"
    //     0xb867ec: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb867f0: ldr             x0, [x0, #0x970]
    // 0xb867f4: stur            x3, [fp, #-0x18]
    // 0xb867f8: StoreField: r3->field_b = r0
    //     0xb867f8: stur            w0, [x3, #0xb]
    // 0xb867fc: ldur            x0, [fp, #-8]
    // 0xb86800: StoreField: r3->field_13 = r0
    //     0xb86800: stur            w0, [x3, #0x13]
    // 0xb86804: r1 = Function '<anonymous closure>':.
    //     0xb86804: add             x1, PP, #0x55, lsl #12  ; [pp+0x556d0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb86808: ldr             x1, [x1, #0x6d0]
    // 0xb8680c: r2 = Null
    //     0xb8680c: mov             x2, NULL
    // 0xb86810: r0 = AllocateClosure()
    //     0xb86810: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86814: stur            x0, [fp, #-8]
    // 0xb86818: r0 = TextButton()
    //     0xb86818: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb8681c: mov             x1, x0
    // 0xb86820: ldur            x0, [fp, #-8]
    // 0xb86824: stur            x1, [fp, #-0x20]
    // 0xb86828: StoreField: r1->field_b = r0
    //     0xb86828: stur            w0, [x1, #0xb]
    // 0xb8682c: r0 = false
    //     0xb8682c: add             x0, NULL, #0x30  ; false
    // 0xb86830: StoreField: r1->field_27 = r0
    //     0xb86830: stur            w0, [x1, #0x27]
    // 0xb86834: r0 = true
    //     0xb86834: add             x0, NULL, #0x20  ; true
    // 0xb86838: StoreField: r1->field_2f = r0
    //     0xb86838: stur            w0, [x1, #0x2f]
    // 0xb8683c: ldur            x0, [fp, #-0x18]
    // 0xb86840: StoreField: r1->field_37 = r0
    //     0xb86840: stur            w0, [x1, #0x37]
    // 0xb86844: r0 = TextButtonTheme()
    //     0xb86844: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb86848: mov             x1, x0
    // 0xb8684c: ldur            x0, [fp, #-0x10]
    // 0xb86850: stur            x1, [fp, #-8]
    // 0xb86854: StoreField: r1->field_f = r0
    //     0xb86854: stur            w0, [x1, #0xf]
    // 0xb86858: ldur            x0, [fp, #-0x20]
    // 0xb8685c: StoreField: r1->field_b = r0
    //     0xb8685c: stur            w0, [x1, #0xb]
    // 0xb86860: r0 = Padding()
    //     0xb86860: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb86864: r1 = Instance_EdgeInsets
    //     0xb86864: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb86868: ldr             x1, [x1, #0xe68]
    // 0xb8686c: StoreField: r0->field_f = r1
    //     0xb8686c: stur            w1, [x0, #0xf]
    // 0xb86870: ldur            x1, [fp, #-8]
    // 0xb86874: StoreField: r0->field_b = r1
    //     0xb86874: stur            w1, [x0, #0xb]
    // 0xb86878: LeaveFrame
    //     0xb86878: mov             SP, fp
    //     0xb8687c: ldp             fp, lr, [SP], #0x10
    // 0xb86880: ret
    //     0xb86880: ret             
    // 0xb86884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86884: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86888: b               #0xb866a0
    // 0xb8688c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8688c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb86894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildNetworkImage(/* No info */) {
    // ** addr: 0xb86898, size: 0x1ac
    // 0xb86898: EnterFrame
    //     0xb86898: stp             fp, lr, [SP, #-0x10]!
    //     0xb8689c: mov             fp, SP
    // 0xb868a0: AllocStack(0x28)
    //     0xb868a0: sub             SP, SP, #0x28
    // 0xb868a4: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb868a4: stur            x1, [fp, #-8]
    //     0xb868a8: stur            x2, [fp, #-0x10]
    // 0xb868ac: CheckStackOverflow
    //     0xb868ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb868b0: cmp             SP, x16
    //     0xb868b4: b.ls            #0xb86a38
    // 0xb868b8: r1 = 1
    //     0xb868b8: movz            x1, #0x1
    // 0xb868bc: r0 = AllocateContext()
    //     0xb868bc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb868c0: mov             x3, x0
    // 0xb868c4: ldur            x0, [fp, #-8]
    // 0xb868c8: stur            x3, [fp, #-0x20]
    // 0xb868cc: StoreField: r3->field_f = r0
    //     0xb868cc: stur            w0, [x3, #0xf]
    // 0xb868d0: ldur            x4, [fp, #-0x10]
    // 0xb868d4: LoadField: r1 = r4->field_7
    //     0xb868d4: ldur            w1, [x4, #7]
    // 0xb868d8: cbnz            w1, #0xb868f0
    // 0xb868dc: r0 = Instance_SizedBox
    //     0xb868dc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52988] Obj!SizedBox@d67fc1
    //     0xb868e0: ldr             x0, [x0, #0x988]
    // 0xb868e4: LeaveFrame
    //     0xb868e4: mov             SP, fp
    //     0xb868e8: ldp             fp, lr, [SP], #0x10
    // 0xb868ec: ret
    //     0xb868ec: ret             
    // 0xb868f0: LoadField: r5 = r0->field_27
    //     0xb868f0: ldur            w5, [x0, #0x27]
    // 0xb868f4: DecompressPointer r5
    //     0xb868f4: add             x5, x5, HEAP, lsl #32
    // 0xb868f8: mov             x1, x5
    // 0xb868fc: mov             x2, x4
    // 0xb86900: stur            x5, [fp, #-0x18]
    // 0xb86904: r0 = _getValueOrData()
    //     0xb86904: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb86908: mov             x1, x0
    // 0xb8690c: ldur            x0, [fp, #-0x18]
    // 0xb86910: LoadField: r2 = r0->field_f
    //     0xb86910: ldur            w2, [x0, #0xf]
    // 0xb86914: DecompressPointer r2
    //     0xb86914: add             x2, x2, HEAP, lsl #32
    // 0xb86918: cmp             w2, w1
    // 0xb8691c: b.eq            #0xb86940
    // 0xb86920: cmp             w1, NULL
    // 0xb86924: b.eq            #0xb86940
    // 0xb86928: ldur            x1, [fp, #-8]
    // 0xb8692c: ldur            x2, [fp, #-0x10]
    // 0xb86930: r0 = _buildCachedImage()
    //     0xb86930: bl              #0xb86a44  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage
    // 0xb86934: LeaveFrame
    //     0xb86934: mov             SP, fp
    //     0xb86938: ldp             fp, lr, [SP], #0x10
    // 0xb8693c: ret
    //     0xb8693c: ret             
    // 0xb86940: r0 = LoadStaticField(0x878)
    //     0xb86940: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb86944: ldr             x0, [x0, #0x10f0]
    // 0xb86948: cmp             w0, NULL
    // 0xb8694c: b.eq            #0xb86a40
    // 0xb86950: LoadField: r3 = r0->field_53
    //     0xb86950: ldur            w3, [x0, #0x53]
    // 0xb86954: DecompressPointer r3
    //     0xb86954: add             x3, x3, HEAP, lsl #32
    // 0xb86958: stur            x3, [fp, #-0x18]
    // 0xb8695c: LoadField: r0 = r3->field_7
    //     0xb8695c: ldur            w0, [x3, #7]
    // 0xb86960: DecompressPointer r0
    //     0xb86960: add             x0, x0, HEAP, lsl #32
    // 0xb86964: ldur            x2, [fp, #-0x20]
    // 0xb86968: stur            x0, [fp, #-0x10]
    // 0xb8696c: r1 = Function '<anonymous closure>':.
    //     0xb8696c: add             x1, PP, #0x55, lsl #12  ; [pp+0x556d8] AnonymousClosure: (0xb86b48), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage (0xb86898)
    //     0xb86970: ldr             x1, [x1, #0x6d8]
    // 0xb86974: r0 = AllocateClosure()
    //     0xb86974: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86978: ldur            x2, [fp, #-0x10]
    // 0xb8697c: mov             x3, x0
    // 0xb86980: r1 = Null
    //     0xb86980: mov             x1, NULL
    // 0xb86984: stur            x3, [fp, #-0x10]
    // 0xb86988: cmp             w2, NULL
    // 0xb8698c: b.eq            #0xb869ac
    // 0xb86990: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb86990: ldur            w4, [x2, #0x17]
    // 0xb86994: DecompressPointer r4
    //     0xb86994: add             x4, x4, HEAP, lsl #32
    // 0xb86998: r8 = X0
    //     0xb86998: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xb8699c: LoadField: r9 = r4->field_7
    //     0xb8699c: ldur            x9, [x4, #7]
    // 0xb869a0: r3 = Null
    //     0xb869a0: add             x3, PP, #0x55, lsl #12  ; [pp+0x556e0] Null
    //     0xb869a4: ldr             x3, [x3, #0x6e0]
    // 0xb869a8: blr             x9
    // 0xb869ac: ldur            x0, [fp, #-0x18]
    // 0xb869b0: LoadField: r1 = r0->field_b
    //     0xb869b0: ldur            w1, [x0, #0xb]
    // 0xb869b4: LoadField: r2 = r0->field_f
    //     0xb869b4: ldur            w2, [x0, #0xf]
    // 0xb869b8: DecompressPointer r2
    //     0xb869b8: add             x2, x2, HEAP, lsl #32
    // 0xb869bc: LoadField: r3 = r2->field_b
    //     0xb869bc: ldur            w3, [x2, #0xb]
    // 0xb869c0: r2 = LoadInt32Instr(r1)
    //     0xb869c0: sbfx            x2, x1, #1, #0x1f
    // 0xb869c4: stur            x2, [fp, #-0x28]
    // 0xb869c8: r1 = LoadInt32Instr(r3)
    //     0xb869c8: sbfx            x1, x3, #1, #0x1f
    // 0xb869cc: cmp             x2, x1
    // 0xb869d0: b.ne            #0xb869dc
    // 0xb869d4: mov             x1, x0
    // 0xb869d8: r0 = _growToNextCapacity()
    //     0xb869d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb869dc: ldur            x0, [fp, #-0x18]
    // 0xb869e0: ldur            x2, [fp, #-0x28]
    // 0xb869e4: add             x1, x2, #1
    // 0xb869e8: lsl             x3, x1, #1
    // 0xb869ec: StoreField: r0->field_b = r3
    //     0xb869ec: stur            w3, [x0, #0xb]
    // 0xb869f0: LoadField: r1 = r0->field_f
    //     0xb869f0: ldur            w1, [x0, #0xf]
    // 0xb869f4: DecompressPointer r1
    //     0xb869f4: add             x1, x1, HEAP, lsl #32
    // 0xb869f8: ldur            x0, [fp, #-0x10]
    // 0xb869fc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb869fc: add             x25, x1, x2, lsl #2
    //     0xb86a00: add             x25, x25, #0xf
    //     0xb86a04: str             w0, [x25]
    //     0xb86a08: tbz             w0, #0, #0xb86a24
    //     0xb86a0c: ldurb           w16, [x1, #-1]
    //     0xb86a10: ldurb           w17, [x0, #-1]
    //     0xb86a14: and             x16, x17, x16, lsr #2
    //     0xb86a18: tst             x16, HEAP, lsr #32
    //     0xb86a1c: b.eq            #0xb86a24
    //     0xb86a20: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb86a24: ldur            x1, [fp, #-8]
    // 0xb86a28: r0 = _buildProgressIndicator()
    //     0xb86a28: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xb86a2c: LeaveFrame
    //     0xb86a2c: mov             SP, fp
    //     0xb86a30: ldp             fp, lr, [SP], #0x10
    // 0xb86a34: ret
    //     0xb86a34: ret             
    // 0xb86a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86a38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86a3c: b               #0xb868b8
    // 0xb86a40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86a40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCachedImage(/* No info */) {
    // ** addr: 0xb86a44, size: 0xc0
    // 0xb86a44: EnterFrame
    //     0xb86a44: stp             fp, lr, [SP, #-0x10]!
    //     0xb86a48: mov             fp, SP
    // 0xb86a4c: AllocStack(0x38)
    //     0xb86a4c: sub             SP, SP, #0x38
    // 0xb86a50: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb86a50: stur            x1, [fp, #-8]
    //     0xb86a54: stur            x2, [fp, #-0x10]
    // 0xb86a58: CheckStackOverflow
    //     0xb86a58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86a5c: cmp             SP, x16
    //     0xb86a60: b.ls            #0xb86afc
    // 0xb86a64: r1 = 1
    //     0xb86a64: movz            x1, #0x1
    // 0xb86a68: r0 = AllocateContext()
    //     0xb86a68: bl              #0x16f6108  ; AllocateContextStub
    // 0xb86a6c: mov             x1, x0
    // 0xb86a70: ldur            x0, [fp, #-8]
    // 0xb86a74: stur            x1, [fp, #-0x18]
    // 0xb86a78: StoreField: r1->field_f = r0
    //     0xb86a78: stur            w0, [x1, #0xf]
    // 0xb86a7c: r0 = CachedNetworkImage()
    //     0xb86a7c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb86a80: ldur            x2, [fp, #-0x18]
    // 0xb86a84: r1 = Function '<anonymous closure>':.
    //     0xb86a84: add             x1, PP, #0x55, lsl #12  ; [pp+0x556f8] AnonymousClosure: (0xb86b04), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage (0xb86a44)
    //     0xb86a88: ldr             x1, [x1, #0x6f8]
    // 0xb86a8c: stur            x0, [fp, #-8]
    // 0xb86a90: r0 = AllocateClosure()
    //     0xb86a90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86a94: r1 = Function '<anonymous closure>':.
    //     0xb86a94: add             x1, PP, #0x55, lsl #12  ; [pp+0x55700] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb86a98: ldr             x1, [x1, #0x700]
    // 0xb86a9c: r2 = Null
    //     0xb86a9c: mov             x2, NULL
    // 0xb86aa0: stur            x0, [fp, #-0x18]
    // 0xb86aa4: r0 = AllocateClosure()
    //     0xb86aa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86aa8: r16 = inf
    //     0xb86aa8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb86aac: ldr             x16, [x16, #0x9f8]
    // 0xb86ab0: r30 = Instance_BoxFit
    //     0xb86ab0: add             lr, PP, #0x2d, lsl #12  ; [pp+0x2d7d0] Obj!BoxFit@d73881
    //     0xb86ab4: ldr             lr, [lr, #0x7d0]
    // 0xb86ab8: stp             lr, x16, [SP, #0x10]
    // 0xb86abc: ldur            x16, [fp, #-0x18]
    // 0xb86ac0: stp             x0, x16, [SP]
    // 0xb86ac4: ldur            x1, [fp, #-8]
    // 0xb86ac8: ldur            x2, [fp, #-0x10]
    // 0xb86acc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, placeholder, 0x4, width, 0x2, null]
    //     0xb86acc: add             x4, PP, #0x55, lsl #12  ; [pp+0x55708] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "placeholder", 0x4, "width", 0x2, Null]
    //     0xb86ad0: ldr             x4, [x4, #0x708]
    // 0xb86ad4: r0 = CachedNetworkImage()
    //     0xb86ad4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb86ad8: r0 = Center()
    //     0xb86ad8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb86adc: r1 = Instance_Alignment
    //     0xb86adc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb86ae0: ldr             x1, [x1, #0xb10]
    // 0xb86ae4: StoreField: r0->field_f = r1
    //     0xb86ae4: stur            w1, [x0, #0xf]
    // 0xb86ae8: ldur            x1, [fp, #-8]
    // 0xb86aec: StoreField: r0->field_b = r1
    //     0xb86aec: stur            w1, [x0, #0xb]
    // 0xb86af0: LeaveFrame
    //     0xb86af0: mov             SP, fp
    //     0xb86af4: ldp             fp, lr, [SP], #0x10
    // 0xb86af8: ret
    //     0xb86af8: ret             
    // 0xb86afc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86afc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86b00: b               #0xb86a64
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, String) {
    // ** addr: 0xb86b04, size: 0x44
    // 0xb86b04: EnterFrame
    //     0xb86b04: stp             fp, lr, [SP, #-0x10]!
    //     0xb86b08: mov             fp, SP
    // 0xb86b0c: ldr             x0, [fp, #0x20]
    // 0xb86b10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb86b10: ldur            w1, [x0, #0x17]
    // 0xb86b14: DecompressPointer r1
    //     0xb86b14: add             x1, x1, HEAP, lsl #32
    // 0xb86b18: CheckStackOverflow
    //     0xb86b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86b1c: cmp             SP, x16
    //     0xb86b20: b.ls            #0xb86b40
    // 0xb86b24: LoadField: r0 = r1->field_f
    //     0xb86b24: ldur            w0, [x1, #0xf]
    // 0xb86b28: DecompressPointer r0
    //     0xb86b28: add             x0, x0, HEAP, lsl #32
    // 0xb86b2c: mov             x1, x0
    // 0xb86b30: r0 = _buildProgressIndicator()
    //     0xb86b30: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xb86b34: LeaveFrame
    //     0xb86b34: mov             SP, fp
    //     0xb86b38: ldp             fp, lr, [SP], #0x10
    // 0xb86b3c: ret
    //     0xb86b3c: ret             
    // 0xb86b40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86b40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86b44: b               #0xb86b24
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xb86b48, size: 0x64
    // 0xb86b48: EnterFrame
    //     0xb86b48: stp             fp, lr, [SP, #-0x10]!
    //     0xb86b4c: mov             fp, SP
    // 0xb86b50: ldr             x0, [fp, #0x18]
    // 0xb86b54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb86b54: ldur            w1, [x0, #0x17]
    // 0xb86b58: DecompressPointer r1
    //     0xb86b58: add             x1, x1, HEAP, lsl #32
    // 0xb86b5c: CheckStackOverflow
    //     0xb86b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86b60: cmp             SP, x16
    //     0xb86b64: b.ls            #0xb86ba4
    // 0xb86b68: LoadField: r0 = r1->field_f
    //     0xb86b68: ldur            w0, [x1, #0xf]
    // 0xb86b6c: DecompressPointer r0
    //     0xb86b6c: add             x0, x0, HEAP, lsl #32
    // 0xb86b70: LoadField: r1 = r0->field_2b
    //     0xb86b70: ldur            w1, [x0, #0x2b]
    // 0xb86b74: DecompressPointer r1
    //     0xb86b74: add             x1, x1, HEAP, lsl #32
    // 0xb86b78: tbz             w1, #4, #0xb86b94
    // 0xb86b7c: LoadField: r1 = r0->field_f
    //     0xb86b7c: ldur            w1, [x0, #0xf]
    // 0xb86b80: DecompressPointer r1
    //     0xb86b80: add             x1, x1, HEAP, lsl #32
    // 0xb86b84: cmp             w1, NULL
    // 0xb86b88: b.eq            #0xb86b94
    // 0xb86b8c: mov             x1, x0
    // 0xb86b90: r0 = _preloadImageSizes()
    //     0xb86b90: bl              #0x8051dc  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0xb86b94: r0 = Null
    //     0xb86b94: mov             x0, NULL
    // 0xb86b98: LeaveFrame
    //     0xb86b98: mov             SP, fp
    //     0xb86b9c: ldp             fp, lr, [SP], #0x10
    // 0xb86ba0: ret
    //     0xb86ba0: ret             
    // 0xb86ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86ba4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86ba8: b               #0xb86b68
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb86bac, size: 0x90
    // 0xb86bac: EnterFrame
    //     0xb86bac: stp             fp, lr, [SP, #-0x10]!
    //     0xb86bb0: mov             fp, SP
    // 0xb86bb4: AllocStack(0x18)
    //     0xb86bb4: sub             SP, SP, #0x18
    // 0xb86bb8: SetupParameters()
    //     0xb86bb8: ldr             x0, [fp, #0x10]
    //     0xb86bbc: ldur            w1, [x0, #0x17]
    //     0xb86bc0: add             x1, x1, HEAP, lsl #32
    // 0xb86bc4: CheckStackOverflow
    //     0xb86bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86bc8: cmp             SP, x16
    //     0xb86bcc: b.ls            #0xb86c30
    // 0xb86bd0: LoadField: r0 = r1->field_f
    //     0xb86bd0: ldur            w0, [x1, #0xf]
    // 0xb86bd4: DecompressPointer r0
    //     0xb86bd4: add             x0, x0, HEAP, lsl #32
    // 0xb86bd8: LoadField: r2 = r0->field_b
    //     0xb86bd8: ldur            w2, [x0, #0xb]
    // 0xb86bdc: DecompressPointer r2
    //     0xb86bdc: add             x2, x2, HEAP, lsl #32
    // 0xb86be0: cmp             w2, NULL
    // 0xb86be4: b.eq            #0xb86c38
    // 0xb86be8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb86be8: ldur            w0, [x1, #0x17]
    // 0xb86bec: DecompressPointer r0
    //     0xb86bec: add             x0, x0, HEAP, lsl #32
    // 0xb86bf0: LoadField: r3 = r1->field_13
    //     0xb86bf0: ldur            w3, [x1, #0x13]
    // 0xb86bf4: DecompressPointer r3
    //     0xb86bf4: add             x3, x3, HEAP, lsl #32
    // 0xb86bf8: LoadField: r1 = r2->field_23
    //     0xb86bf8: ldur            w1, [x2, #0x23]
    // 0xb86bfc: DecompressPointer r1
    //     0xb86bfc: add             x1, x1, HEAP, lsl #32
    // 0xb86c00: stp             x0, x1, [SP, #8]
    // 0xb86c04: str             x3, [SP]
    // 0xb86c08: r4 = 0
    //     0xb86c08: movz            x4, #0
    // 0xb86c0c: ldr             x0, [SP, #0x10]
    // 0xb86c10: r16 = UnlinkedCall_0x613b5c
    //     0xb86c10: add             x16, PP, #0x55, lsl #12  ; [pp+0x556c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb86c14: add             x16, x16, #0x6c0
    // 0xb86c18: ldp             x5, lr, [x16]
    // 0xb86c1c: blr             lr
    // 0xb86c20: r0 = Null
    //     0xb86c20: mov             x0, NULL
    // 0xb86c24: LeaveFrame
    //     0xb86c24: mov             SP, fp
    //     0xb86c28: ldp             fp, lr, [SP], #0x10
    // 0xb86c2c: ret
    //     0xb86c2c: ret             
    // 0xb86c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86c30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86c34: b               #0xb86bd0
    // 0xb86c38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb86c38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb86c48, size: 0x84
    // 0xb86c48: EnterFrame
    //     0xb86c48: stp             fp, lr, [SP, #-0x10]!
    //     0xb86c4c: mov             fp, SP
    // 0xb86c50: AllocStack(0x10)
    //     0xb86c50: sub             SP, SP, #0x10
    // 0xb86c54: SetupParameters()
    //     0xb86c54: ldr             x0, [fp, #0x18]
    //     0xb86c58: ldur            w1, [x0, #0x17]
    //     0xb86c5c: add             x1, x1, HEAP, lsl #32
    //     0xb86c60: stur            x1, [fp, #-8]
    // 0xb86c64: CheckStackOverflow
    //     0xb86c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86c68: cmp             SP, x16
    //     0xb86c6c: b.ls            #0xb86cc4
    // 0xb86c70: r1 = 1
    //     0xb86c70: movz            x1, #0x1
    // 0xb86c74: r0 = AllocateContext()
    //     0xb86c74: bl              #0x16f6108  ; AllocateContextStub
    // 0xb86c78: mov             x1, x0
    // 0xb86c7c: ldur            x0, [fp, #-8]
    // 0xb86c80: StoreField: r1->field_b = r0
    //     0xb86c80: stur            w0, [x1, #0xb]
    // 0xb86c84: ldr             x2, [fp, #0x10]
    // 0xb86c88: StoreField: r1->field_f = r2
    //     0xb86c88: stur            w2, [x1, #0xf]
    // 0xb86c8c: LoadField: r3 = r0->field_f
    //     0xb86c8c: ldur            w3, [x0, #0xf]
    // 0xb86c90: DecompressPointer r3
    //     0xb86c90: add             x3, x3, HEAP, lsl #32
    // 0xb86c94: mov             x2, x1
    // 0xb86c98: stur            x3, [fp, #-0x10]
    // 0xb86c9c: r1 = Function '<anonymous closure>':.
    //     0xb86c9c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55710] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xb86ca0: ldr             x1, [x1, #0x710]
    // 0xb86ca4: r0 = AllocateClosure()
    //     0xb86ca4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86ca8: ldur            x1, [fp, #-0x10]
    // 0xb86cac: mov             x2, x0
    // 0xb86cb0: r0 = setState()
    //     0xb86cb0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb86cb4: r0 = Null
    //     0xb86cb4: mov             x0, NULL
    // 0xb86cb8: LeaveFrame
    //     0xb86cb8: mov             SP, fp
    //     0xb86cbc: ldp             fp, lr, [SP], #0x10
    // 0xb86cc0: ret
    //     0xb86cc0: ret             
    // 0xb86cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb86cc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb86cc8: b               #0xb86c70
  }
}

// class id: 4056, size: 0x28, field offset: 0xc
//   const constructor, 
class ProductMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f850, size: 0x48
    // 0xc7f850: EnterFrame
    //     0xc7f850: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f854: mov             fp, SP
    // 0xc7f858: AllocStack(0x8)
    //     0xc7f858: sub             SP, SP, #8
    // 0xc7f85c: CheckStackOverflow
    //     0xc7f85c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f860: cmp             SP, x16
    //     0xc7f864: b.ls            #0xc7f890
    // 0xc7f868: r1 = <ProductMediaCarousel>
    //     0xc7f868: add             x1, PP, #0x48, lsl #12  ; [pp+0x48758] TypeArguments: <ProductMediaCarousel>
    //     0xc7f86c: ldr             x1, [x1, #0x758]
    // 0xc7f870: r0 = _ProductMediaCarouselState()
    //     0xc7f870: bl              #0xc7f898  ; Allocate_ProductMediaCarouselStateStub -> _ProductMediaCarouselState (size=0x34)
    // 0xc7f874: mov             x1, x0
    // 0xc7f878: stur            x0, [fp, #-8]
    // 0xc7f87c: r0 = _ProductMediaCarouselState()
    //     0xc7f87c: bl              #0xc7c270  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_ProductMediaCarouselState
    // 0xc7f880: ldur            x0, [fp, #-8]
    // 0xc7f884: LeaveFrame
    //     0xc7f884: mov             SP, fp
    //     0xc7f888: ldp             fp, lr, [SP], #0x10
    // 0xc7f88c: ret
    //     0xc7f88c: ret             
    // 0xc7f890: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f890: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f894: b               #0xc7f868
  }
}

// class id: 4860, size: 0x10, field offset: 0x10
class CustomPath extends CustomClipper<dynamic> {

  _ shouldReclip(/* No info */) {
    // ** addr: 0x161d654, size: 0x40
    // 0x161d654: EnterFrame
    //     0x161d654: stp             fp, lr, [SP, #-0x10]!
    //     0x161d658: mov             fp, SP
    // 0x161d65c: mov             x0, x2
    // 0x161d660: mov             x4, x1
    // 0x161d664: mov             x3, x2
    // 0x161d668: r2 = Null
    //     0x161d668: mov             x2, NULL
    // 0x161d66c: r1 = Null
    //     0x161d66c: mov             x1, NULL
    // 0x161d670: r8 = CustomClipper<Path>
    //     0x161d670: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4cc88] Type: CustomClipper<Path>
    //     0x161d674: ldr             x8, [x8, #0xc88]
    // 0x161d678: r3 = Null
    //     0x161d678: add             x3, PP, #0x61, lsl #12  ; [pp+0x61d68] Null
    //     0x161d67c: ldr             x3, [x3, #0xd68]
    // 0x161d680: r0 = CustomClipper<Path>()
    //     0x161d680: bl              #0x99065c  ; IsType_CustomClipper<Path>_Stub
    // 0x161d684: r0 = true
    //     0x161d684: add             x0, NULL, #0x20  ; true
    // 0x161d688: LeaveFrame
    //     0x161d688: mov             SP, fp
    //     0x161d68c: ldp             fp, lr, [SP], #0x10
    // 0x161d690: ret
    //     0x161d690: ret             
  }
}
