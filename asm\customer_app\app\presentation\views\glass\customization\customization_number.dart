// lib: , url: package:customer_app/app/presentation/views/glass/customization/customization_number.dart

// class id: 1049386, size: 0x8
class :: {
}

// class id: 3346, size: 0x1c, field offset: 0x14
class _CustomisationNumberState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb58edc, size: 0x788
    // 0xb58edc: EnterFrame
    //     0xb58edc: stp             fp, lr, [SP, #-0x10]!
    //     0xb58ee0: mov             fp, SP
    // 0xb58ee4: AllocStack(0x88)
    //     0xb58ee4: sub             SP, SP, #0x88
    // 0xb58ee8: SetupParameters(_CustomisationNumberState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb58ee8: mov             x0, x1
    //     0xb58eec: stur            x1, [fp, #-8]
    //     0xb58ef0: mov             x1, x2
    //     0xb58ef4: stur            x2, [fp, #-0x10]
    // 0xb58ef8: CheckStackOverflow
    //     0xb58ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58efc: cmp             SP, x16
    //     0xb58f00: b.ls            #0xb59650
    // 0xb58f04: r1 = 1
    //     0xb58f04: movz            x1, #0x1
    // 0xb58f08: r0 = AllocateContext()
    //     0xb58f08: bl              #0x16f6108  ; AllocateContextStub
    // 0xb58f0c: mov             x3, x0
    // 0xb58f10: ldur            x0, [fp, #-8]
    // 0xb58f14: stur            x3, [fp, #-0x20]
    // 0xb58f18: StoreField: r3->field_f = r0
    //     0xb58f18: stur            w0, [x3, #0xf]
    // 0xb58f1c: LoadField: r1 = r0->field_b
    //     0xb58f1c: ldur            w1, [x0, #0xb]
    // 0xb58f20: DecompressPointer r1
    //     0xb58f20: add             x1, x1, HEAP, lsl #32
    // 0xb58f24: cmp             w1, NULL
    // 0xb58f28: b.eq            #0xb59658
    // 0xb58f2c: LoadField: r2 = r1->field_b
    //     0xb58f2c: ldur            w2, [x1, #0xb]
    // 0xb58f30: DecompressPointer r2
    //     0xb58f30: add             x2, x2, HEAP, lsl #32
    // 0xb58f34: cmp             w2, NULL
    // 0xb58f38: b.ne            #0xb58f44
    // 0xb58f3c: r1 = Null
    //     0xb58f3c: mov             x1, NULL
    // 0xb58f40: b               #0xb58f4c
    // 0xb58f44: LoadField: r1 = r2->field_2b
    //     0xb58f44: ldur            w1, [x2, #0x2b]
    // 0xb58f48: DecompressPointer r1
    //     0xb58f48: add             x1, x1, HEAP, lsl #32
    // 0xb58f4c: cmp             w1, NULL
    // 0xb58f50: b.eq            #0xb58fac
    // 0xb58f54: tbnz            w1, #4, #0xb58fac
    // 0xb58f58: cmp             w2, NULL
    // 0xb58f5c: b.ne            #0xb58f68
    // 0xb58f60: r4 = Null
    //     0xb58f60: mov             x4, NULL
    // 0xb58f64: b               #0xb58f74
    // 0xb58f68: LoadField: r1 = r2->field_1b
    //     0xb58f68: ldur            w1, [x2, #0x1b]
    // 0xb58f6c: DecompressPointer r1
    //     0xb58f6c: add             x1, x1, HEAP, lsl #32
    // 0xb58f70: mov             x4, x1
    // 0xb58f74: stur            x4, [fp, #-0x18]
    // 0xb58f78: r1 = Null
    //     0xb58f78: mov             x1, NULL
    // 0xb58f7c: r2 = 4
    //     0xb58f7c: movz            x2, #0x4
    // 0xb58f80: r0 = AllocateArray()
    //     0xb58f80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb58f84: mov             x1, x0
    // 0xb58f88: ldur            x0, [fp, #-0x18]
    // 0xb58f8c: StoreField: r1->field_f = r0
    //     0xb58f8c: stur            w0, [x1, #0xf]
    // 0xb58f90: r16 = " *"
    //     0xb58f90: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xb58f94: ldr             x16, [x16, #0xfc8]
    // 0xb58f98: StoreField: r1->field_13 = r16
    //     0xb58f98: stur            w16, [x1, #0x13]
    // 0xb58f9c: str             x1, [SP]
    // 0xb58fa0: r0 = _interpolate()
    //     0xb58fa0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb58fa4: mov             x2, x0
    // 0xb58fa8: b               #0xb58fd0
    // 0xb58fac: cmp             w2, NULL
    // 0xb58fb0: b.ne            #0xb58fbc
    // 0xb58fb4: r0 = Null
    //     0xb58fb4: mov             x0, NULL
    // 0xb58fb8: b               #0xb58fc4
    // 0xb58fbc: LoadField: r0 = r2->field_1b
    //     0xb58fbc: ldur            w0, [x2, #0x1b]
    // 0xb58fc0: DecompressPointer r0
    //     0xb58fc0: add             x0, x0, HEAP, lsl #32
    // 0xb58fc4: str             x0, [SP]
    // 0xb58fc8: r0 = _interpolateSingle()
    //     0xb58fc8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb58fcc: mov             x2, x0
    // 0xb58fd0: ldur            x0, [fp, #-8]
    // 0xb58fd4: ldur            x1, [fp, #-0x10]
    // 0xb58fd8: stur            x2, [fp, #-0x18]
    // 0xb58fdc: r0 = of()
    //     0xb58fdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb58fe0: LoadField: r1 = r0->field_87
    //     0xb58fe0: ldur            w1, [x0, #0x87]
    // 0xb58fe4: DecompressPointer r1
    //     0xb58fe4: add             x1, x1, HEAP, lsl #32
    // 0xb58fe8: LoadField: r0 = r1->field_7
    //     0xb58fe8: ldur            w0, [x1, #7]
    // 0xb58fec: DecompressPointer r0
    //     0xb58fec: add             x0, x0, HEAP, lsl #32
    // 0xb58ff0: r16 = Instance_Color
    //     0xb58ff0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb58ff4: r30 = 14.000000
    //     0xb58ff4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb58ff8: ldr             lr, [lr, #0x1d8]
    // 0xb58ffc: stp             lr, x16, [SP]
    // 0xb59000: mov             x1, x0
    // 0xb59004: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb59004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb59008: ldr             x4, [x4, #0x9b8]
    // 0xb5900c: r0 = copyWith()
    //     0xb5900c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb59010: stur            x0, [fp, #-0x28]
    // 0xb59014: r0 = Text()
    //     0xb59014: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb59018: mov             x1, x0
    // 0xb5901c: ldur            x0, [fp, #-0x18]
    // 0xb59020: stur            x1, [fp, #-0x30]
    // 0xb59024: StoreField: r1->field_b = r0
    //     0xb59024: stur            w0, [x1, #0xb]
    // 0xb59028: ldur            x0, [fp, #-0x28]
    // 0xb5902c: StoreField: r1->field_13 = r0
    //     0xb5902c: stur            w0, [x1, #0x13]
    // 0xb59030: r0 = Padding()
    //     0xb59030: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb59034: mov             x3, x0
    // 0xb59038: r0 = Instance_EdgeInsets
    //     0xb59038: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5903c: ldr             x0, [x0, #0x1f0]
    // 0xb59040: stur            x3, [fp, #-0x38]
    // 0xb59044: StoreField: r3->field_f = r0
    //     0xb59044: stur            w0, [x3, #0xf]
    // 0xb59048: ldur            x0, [fp, #-0x30]
    // 0xb5904c: StoreField: r3->field_b = r0
    //     0xb5904c: stur            w0, [x3, #0xb]
    // 0xb59050: ldur            x0, [fp, #-8]
    // 0xb59054: LoadField: r1 = r0->field_b
    //     0xb59054: ldur            w1, [x0, #0xb]
    // 0xb59058: DecompressPointer r1
    //     0xb59058: add             x1, x1, HEAP, lsl #32
    // 0xb5905c: cmp             w1, NULL
    // 0xb59060: b.eq            #0xb5965c
    // 0xb59064: LoadField: r4 = r1->field_b
    //     0xb59064: ldur            w4, [x1, #0xb]
    // 0xb59068: DecompressPointer r4
    //     0xb59068: add             x4, x4, HEAP, lsl #32
    // 0xb5906c: stur            x4, [fp, #-0x28]
    // 0xb59070: cmp             w4, NULL
    // 0xb59074: b.ne            #0xb59080
    // 0xb59078: r1 = Null
    //     0xb59078: mov             x1, NULL
    // 0xb5907c: b               #0xb59088
    // 0xb59080: LoadField: r1 = r4->field_23
    //     0xb59080: ldur            w1, [x4, #0x23]
    // 0xb59084: DecompressPointer r1
    //     0xb59084: add             x1, x1, HEAP, lsl #32
    // 0xb59088: cbnz            w1, #0xb59094
    // 0xb5908c: r5 = false
    //     0xb5908c: add             x5, NULL, #0x30  ; false
    // 0xb59090: b               #0xb59098
    // 0xb59094: r5 = true
    //     0xb59094: add             x5, NULL, #0x20  ; true
    // 0xb59098: stur            x5, [fp, #-0x18]
    // 0xb5909c: r1 = Null
    //     0xb5909c: mov             x1, NULL
    // 0xb590a0: r2 = 4
    //     0xb590a0: movz            x2, #0x4
    // 0xb590a4: r0 = AllocateArray()
    //     0xb590a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb590a8: r16 = "+ "
    //     0xb590a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xb590ac: ldr             x16, [x16, #0xc30]
    // 0xb590b0: StoreField: r0->field_f = r16
    //     0xb590b0: stur            w16, [x0, #0xf]
    // 0xb590b4: ldur            x1, [fp, #-0x28]
    // 0xb590b8: cmp             w1, NULL
    // 0xb590bc: b.ne            #0xb590c8
    // 0xb590c0: r1 = Null
    //     0xb590c0: mov             x1, NULL
    // 0xb590c4: b               #0xb590d4
    // 0xb590c8: LoadField: r2 = r1->field_27
    //     0xb590c8: ldur            w2, [x1, #0x27]
    // 0xb590cc: DecompressPointer r2
    //     0xb590cc: add             x2, x2, HEAP, lsl #32
    // 0xb590d0: mov             x1, x2
    // 0xb590d4: cmp             w1, NULL
    // 0xb590d8: b.ne            #0xb590e4
    // 0xb590dc: r4 = " "
    //     0xb590dc: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb590e0: b               #0xb590e8
    // 0xb590e4: mov             x4, x1
    // 0xb590e8: ldur            x2, [fp, #-8]
    // 0xb590ec: ldur            x1, [fp, #-0x38]
    // 0xb590f0: ldur            x3, [fp, #-0x18]
    // 0xb590f4: StoreField: r0->field_13 = r4
    //     0xb590f4: stur            w4, [x0, #0x13]
    // 0xb590f8: str             x0, [SP]
    // 0xb590fc: r0 = _interpolate()
    //     0xb590fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb59100: ldur            x1, [fp, #-0x10]
    // 0xb59104: stur            x0, [fp, #-0x28]
    // 0xb59108: r0 = of()
    //     0xb59108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5910c: LoadField: r1 = r0->field_87
    //     0xb5910c: ldur            w1, [x0, #0x87]
    // 0xb59110: DecompressPointer r1
    //     0xb59110: add             x1, x1, HEAP, lsl #32
    // 0xb59114: LoadField: r0 = r1->field_2b
    //     0xb59114: ldur            w0, [x1, #0x2b]
    // 0xb59118: DecompressPointer r0
    //     0xb59118: add             x0, x0, HEAP, lsl #32
    // 0xb5911c: stur            x0, [fp, #-0x30]
    // 0xb59120: r1 = Instance_Color
    //     0xb59120: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb59124: d0 = 0.700000
    //     0xb59124: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb59128: ldr             d0, [x17, #0xf48]
    // 0xb5912c: r0 = withOpacity()
    //     0xb5912c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb59130: r16 = 14.000000
    //     0xb59130: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb59134: ldr             x16, [x16, #0x1d8]
    // 0xb59138: stp             x16, x0, [SP]
    // 0xb5913c: ldur            x1, [fp, #-0x30]
    // 0xb59140: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb59140: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb59144: ldr             x4, [x4, #0x9b8]
    // 0xb59148: r0 = copyWith()
    //     0xb59148: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5914c: stur            x0, [fp, #-0x30]
    // 0xb59150: r0 = Text()
    //     0xb59150: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb59154: mov             x1, x0
    // 0xb59158: ldur            x0, [fp, #-0x28]
    // 0xb5915c: stur            x1, [fp, #-0x40]
    // 0xb59160: StoreField: r1->field_b = r0
    //     0xb59160: stur            w0, [x1, #0xb]
    // 0xb59164: ldur            x0, [fp, #-0x30]
    // 0xb59168: StoreField: r1->field_13 = r0
    //     0xb59168: stur            w0, [x1, #0x13]
    // 0xb5916c: r0 = Visibility()
    //     0xb5916c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb59170: mov             x1, x0
    // 0xb59174: ldur            x0, [fp, #-0x40]
    // 0xb59178: stur            x1, [fp, #-0x28]
    // 0xb5917c: StoreField: r1->field_b = r0
    //     0xb5917c: stur            w0, [x1, #0xb]
    // 0xb59180: r0 = Instance_SizedBox
    //     0xb59180: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb59184: StoreField: r1->field_f = r0
    //     0xb59184: stur            w0, [x1, #0xf]
    // 0xb59188: ldur            x2, [fp, #-0x18]
    // 0xb5918c: StoreField: r1->field_13 = r2
    //     0xb5918c: stur            w2, [x1, #0x13]
    // 0xb59190: r2 = false
    //     0xb59190: add             x2, NULL, #0x30  ; false
    // 0xb59194: ArrayStore: r1[0] = r2  ; List_4
    //     0xb59194: stur            w2, [x1, #0x17]
    // 0xb59198: StoreField: r1->field_1b = r2
    //     0xb59198: stur            w2, [x1, #0x1b]
    // 0xb5919c: StoreField: r1->field_1f = r2
    //     0xb5919c: stur            w2, [x1, #0x1f]
    // 0xb591a0: StoreField: r1->field_23 = r2
    //     0xb591a0: stur            w2, [x1, #0x23]
    // 0xb591a4: StoreField: r1->field_27 = r2
    //     0xb591a4: stur            w2, [x1, #0x27]
    // 0xb591a8: StoreField: r1->field_2b = r2
    //     0xb591a8: stur            w2, [x1, #0x2b]
    // 0xb591ac: r0 = Padding()
    //     0xb591ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb591b0: mov             x3, x0
    // 0xb591b4: r0 = Instance_EdgeInsets
    //     0xb591b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb591b8: ldr             x0, [x0, #0x980]
    // 0xb591bc: stur            x3, [fp, #-0x18]
    // 0xb591c0: StoreField: r3->field_f = r0
    //     0xb591c0: stur            w0, [x3, #0xf]
    // 0xb591c4: ldur            x0, [fp, #-0x28]
    // 0xb591c8: StoreField: r3->field_b = r0
    //     0xb591c8: stur            w0, [x3, #0xb]
    // 0xb591cc: r1 = Null
    //     0xb591cc: mov             x1, NULL
    // 0xb591d0: r2 = 6
    //     0xb591d0: movz            x2, #0x6
    // 0xb591d4: r0 = AllocateArray()
    //     0xb591d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb591d8: mov             x2, x0
    // 0xb591dc: ldur            x0, [fp, #-0x38]
    // 0xb591e0: stur            x2, [fp, #-0x28]
    // 0xb591e4: StoreField: r2->field_f = r0
    //     0xb591e4: stur            w0, [x2, #0xf]
    // 0xb591e8: r16 = Instance_Spacer
    //     0xb591e8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb591ec: ldr             x16, [x16, #0xf0]
    // 0xb591f0: StoreField: r2->field_13 = r16
    //     0xb591f0: stur            w16, [x2, #0x13]
    // 0xb591f4: ldur            x0, [fp, #-0x18]
    // 0xb591f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb591f8: stur            w0, [x2, #0x17]
    // 0xb591fc: r1 = <Widget>
    //     0xb591fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb59200: r0 = AllocateGrowableArray()
    //     0xb59200: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb59204: mov             x1, x0
    // 0xb59208: ldur            x0, [fp, #-0x28]
    // 0xb5920c: stur            x1, [fp, #-0x18]
    // 0xb59210: StoreField: r1->field_f = r0
    //     0xb59210: stur            w0, [x1, #0xf]
    // 0xb59214: r2 = 6
    //     0xb59214: movz            x2, #0x6
    // 0xb59218: StoreField: r1->field_b = r2
    //     0xb59218: stur            w2, [x1, #0xb]
    // 0xb5921c: r0 = Row()
    //     0xb5921c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb59220: mov             x2, x0
    // 0xb59224: r0 = Instance_Axis
    //     0xb59224: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb59228: stur            x2, [fp, #-0x30]
    // 0xb5922c: StoreField: r2->field_f = r0
    //     0xb5922c: stur            w0, [x2, #0xf]
    // 0xb59230: r0 = Instance_MainAxisAlignment
    //     0xb59230: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb59234: ldr             x0, [x0, #0xa08]
    // 0xb59238: StoreField: r2->field_13 = r0
    //     0xb59238: stur            w0, [x2, #0x13]
    // 0xb5923c: r3 = Instance_MainAxisSize
    //     0xb5923c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb59240: ldr             x3, [x3, #0xa10]
    // 0xb59244: ArrayStore: r2[0] = r3  ; List_4
    //     0xb59244: stur            w3, [x2, #0x17]
    // 0xb59248: r1 = Instance_CrossAxisAlignment
    //     0xb59248: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5924c: ldr             x1, [x1, #0xa18]
    // 0xb59250: StoreField: r2->field_1b = r1
    //     0xb59250: stur            w1, [x2, #0x1b]
    // 0xb59254: r4 = Instance_VerticalDirection
    //     0xb59254: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb59258: ldr             x4, [x4, #0xa20]
    // 0xb5925c: StoreField: r2->field_23 = r4
    //     0xb5925c: stur            w4, [x2, #0x23]
    // 0xb59260: r5 = Instance_Clip
    //     0xb59260: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb59264: ldr             x5, [x5, #0x38]
    // 0xb59268: StoreField: r2->field_2b = r5
    //     0xb59268: stur            w5, [x2, #0x2b]
    // 0xb5926c: StoreField: r2->field_2f = rZR
    //     0xb5926c: stur            xzr, [x2, #0x2f]
    // 0xb59270: ldur            x1, [fp, #-0x18]
    // 0xb59274: StoreField: r2->field_b = r1
    //     0xb59274: stur            w1, [x2, #0xb]
    // 0xb59278: ldur            x6, [fp, #-8]
    // 0xb5927c: LoadField: r1 = r6->field_b
    //     0xb5927c: ldur            w1, [x6, #0xb]
    // 0xb59280: DecompressPointer r1
    //     0xb59280: add             x1, x1, HEAP, lsl #32
    // 0xb59284: cmp             w1, NULL
    // 0xb59288: b.eq            #0xb59660
    // 0xb5928c: LoadField: r7 = r1->field_b
    //     0xb5928c: ldur            w7, [x1, #0xb]
    // 0xb59290: DecompressPointer r7
    //     0xb59290: add             x7, x7, HEAP, lsl #32
    // 0xb59294: cmp             w7, NULL
    // 0xb59298: b.ne            #0xb592a4
    // 0xb5929c: r1 = Null
    //     0xb5929c: mov             x1, NULL
    // 0xb592a0: b               #0xb592d0
    // 0xb592a4: LoadField: r1 = r7->field_1f
    //     0xb592a4: ldur            w1, [x7, #0x1f]
    // 0xb592a8: DecompressPointer r1
    //     0xb592a8: add             x1, x1, HEAP, lsl #32
    // 0xb592ac: cmp             w1, NULL
    // 0xb592b0: b.ne            #0xb592bc
    // 0xb592b4: r1 = Null
    //     0xb592b4: mov             x1, NULL
    // 0xb592b8: b               #0xb592d0
    // 0xb592bc: LoadField: r8 = r1->field_7
    //     0xb592bc: ldur            w8, [x1, #7]
    // 0xb592c0: cbnz            w8, #0xb592cc
    // 0xb592c4: r1 = false
    //     0xb592c4: add             x1, NULL, #0x30  ; false
    // 0xb592c8: b               #0xb592d0
    // 0xb592cc: r1 = true
    //     0xb592cc: add             x1, NULL, #0x20  ; true
    // 0xb592d0: cmp             w1, NULL
    // 0xb592d4: b.ne            #0xb592e0
    // 0xb592d8: r8 = false
    //     0xb592d8: add             x8, NULL, #0x30  ; false
    // 0xb592dc: b               #0xb592e4
    // 0xb592e0: mov             x8, x1
    // 0xb592e4: stur            x8, [fp, #-0x28]
    // 0xb592e8: cmp             w7, NULL
    // 0xb592ec: b.ne            #0xb592f8
    // 0xb592f0: r1 = Null
    //     0xb592f0: mov             x1, NULL
    // 0xb592f4: b               #0xb59300
    // 0xb592f8: LoadField: r1 = r7->field_1f
    //     0xb592f8: ldur            w1, [x7, #0x1f]
    // 0xb592fc: DecompressPointer r1
    //     0xb592fc: add             x1, x1, HEAP, lsl #32
    // 0xb59300: cmp             w1, NULL
    // 0xb59304: b.ne            #0xb59310
    // 0xb59308: r7 = ""
    //     0xb59308: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5930c: b               #0xb59314
    // 0xb59310: mov             x7, x1
    // 0xb59314: ldur            x1, [fp, #-0x10]
    // 0xb59318: stur            x7, [fp, #-0x18]
    // 0xb5931c: r0 = of()
    //     0xb5931c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb59320: LoadField: r1 = r0->field_87
    //     0xb59320: ldur            w1, [x0, #0x87]
    // 0xb59324: DecompressPointer r1
    //     0xb59324: add             x1, x1, HEAP, lsl #32
    // 0xb59328: LoadField: r0 = r1->field_2b
    //     0xb59328: ldur            w0, [x1, #0x2b]
    // 0xb5932c: DecompressPointer r0
    //     0xb5932c: add             x0, x0, HEAP, lsl #32
    // 0xb59330: stur            x0, [fp, #-0x38]
    // 0xb59334: r1 = Instance_Color
    //     0xb59334: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb59338: d0 = 0.400000
    //     0xb59338: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb5933c: r0 = withOpacity()
    //     0xb5933c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb59340: r16 = 12.000000
    //     0xb59340: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb59344: ldr             x16, [x16, #0x9e8]
    // 0xb59348: stp             x16, x0, [SP]
    // 0xb5934c: ldur            x1, [fp, #-0x38]
    // 0xb59350: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb59350: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb59354: ldr             x4, [x4, #0x9b8]
    // 0xb59358: r0 = copyWith()
    //     0xb59358: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5935c: stur            x0, [fp, #-0x38]
    // 0xb59360: r0 = Text()
    //     0xb59360: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb59364: mov             x1, x0
    // 0xb59368: ldur            x0, [fp, #-0x18]
    // 0xb5936c: stur            x1, [fp, #-0x40]
    // 0xb59370: StoreField: r1->field_b = r0
    //     0xb59370: stur            w0, [x1, #0xb]
    // 0xb59374: ldur            x0, [fp, #-0x38]
    // 0xb59378: StoreField: r1->field_13 = r0
    //     0xb59378: stur            w0, [x1, #0x13]
    // 0xb5937c: r0 = Padding()
    //     0xb5937c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb59380: mov             x1, x0
    // 0xb59384: r0 = Instance_EdgeInsets
    //     0xb59384: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a408] Obj!EdgeInsets@d582b1
    //     0xb59388: ldr             x0, [x0, #0x408]
    // 0xb5938c: stur            x1, [fp, #-0x18]
    // 0xb59390: StoreField: r1->field_f = r0
    //     0xb59390: stur            w0, [x1, #0xf]
    // 0xb59394: ldur            x0, [fp, #-0x40]
    // 0xb59398: StoreField: r1->field_b = r0
    //     0xb59398: stur            w0, [x1, #0xb]
    // 0xb5939c: r0 = Visibility()
    //     0xb5939c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb593a0: mov             x1, x0
    // 0xb593a4: ldur            x0, [fp, #-0x18]
    // 0xb593a8: stur            x1, [fp, #-0x38]
    // 0xb593ac: StoreField: r1->field_b = r0
    //     0xb593ac: stur            w0, [x1, #0xb]
    // 0xb593b0: r0 = Instance_SizedBox
    //     0xb593b0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb593b4: StoreField: r1->field_f = r0
    //     0xb593b4: stur            w0, [x1, #0xf]
    // 0xb593b8: ldur            x0, [fp, #-0x28]
    // 0xb593bc: StoreField: r1->field_13 = r0
    //     0xb593bc: stur            w0, [x1, #0x13]
    // 0xb593c0: r0 = false
    //     0xb593c0: add             x0, NULL, #0x30  ; false
    // 0xb593c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb593c4: stur            w0, [x1, #0x17]
    // 0xb593c8: StoreField: r1->field_1b = r0
    //     0xb593c8: stur            w0, [x1, #0x1b]
    // 0xb593cc: StoreField: r1->field_1f = r0
    //     0xb593cc: stur            w0, [x1, #0x1f]
    // 0xb593d0: StoreField: r1->field_23 = r0
    //     0xb593d0: stur            w0, [x1, #0x23]
    // 0xb593d4: StoreField: r1->field_27 = r0
    //     0xb593d4: stur            w0, [x1, #0x27]
    // 0xb593d8: StoreField: r1->field_2b = r0
    //     0xb593d8: stur            w0, [x1, #0x2b]
    // 0xb593dc: ldur            x0, [fp, #-8]
    // 0xb593e0: LoadField: r2 = r0->field_13
    //     0xb593e0: ldur            w2, [x0, #0x13]
    // 0xb593e4: DecompressPointer r2
    //     0xb593e4: add             x2, x2, HEAP, lsl #32
    // 0xb593e8: stur            x2, [fp, #-0x18]
    // 0xb593ec: r0 = LengthLimitingTextInputFormatter()
    //     0xb593ec: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb593f0: mov             x1, x0
    // 0xb593f4: r0 = 60
    //     0xb593f4: movz            x0, #0x3c
    // 0xb593f8: stur            x1, [fp, #-0x28]
    // 0xb593fc: StoreField: r1->field_7 = r0
    //     0xb593fc: stur            w0, [x1, #7]
    // 0xb59400: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb59400: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb59404: ldr             x0, [x0, #0x1530]
    //     0xb59408: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb5940c: cmp             w0, w16
    //     0xb59410: b.ne            #0xb59420
    //     0xb59414: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb59418: ldr             x2, [x2, #0x120]
    //     0xb5941c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb59420: r1 = Null
    //     0xb59420: mov             x1, NULL
    // 0xb59424: r2 = 4
    //     0xb59424: movz            x2, #0x4
    // 0xb59428: stur            x0, [fp, #-0x40]
    // 0xb5942c: r0 = AllocateArray()
    //     0xb5942c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb59430: mov             x2, x0
    // 0xb59434: ldur            x0, [fp, #-0x28]
    // 0xb59438: stur            x2, [fp, #-0x48]
    // 0xb5943c: StoreField: r2->field_f = r0
    //     0xb5943c: stur            w0, [x2, #0xf]
    // 0xb59440: ldur            x0, [fp, #-0x40]
    // 0xb59444: StoreField: r2->field_13 = r0
    //     0xb59444: stur            w0, [x2, #0x13]
    // 0xb59448: r1 = <TextInputFormatter>
    //     0xb59448: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb5944c: ldr             x1, [x1, #0x7b0]
    // 0xb59450: r0 = AllocateGrowableArray()
    //     0xb59450: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb59454: mov             x2, x0
    // 0xb59458: ldur            x0, [fp, #-0x48]
    // 0xb5945c: stur            x2, [fp, #-0x28]
    // 0xb59460: StoreField: r2->field_f = r0
    //     0xb59460: stur            w0, [x2, #0xf]
    // 0xb59464: r0 = 4
    //     0xb59464: movz            x0, #0x4
    // 0xb59468: StoreField: r2->field_b = r0
    //     0xb59468: stur            w0, [x2, #0xb]
    // 0xb5946c: ldur            x1, [fp, #-0x10]
    // 0xb59470: r0 = of()
    //     0xb59470: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb59474: LoadField: r1 = r0->field_87
    //     0xb59474: ldur            w1, [x0, #0x87]
    // 0xb59478: DecompressPointer r1
    //     0xb59478: add             x1, x1, HEAP, lsl #32
    // 0xb5947c: LoadField: r0 = r1->field_2b
    //     0xb5947c: ldur            w0, [x1, #0x2b]
    // 0xb59480: DecompressPointer r0
    //     0xb59480: add             x0, x0, HEAP, lsl #32
    // 0xb59484: ldur            x1, [fp, #-0x10]
    // 0xb59488: stur            x0, [fp, #-0x40]
    // 0xb5948c: r0 = of()
    //     0xb5948c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb59490: LoadField: r1 = r0->field_5b
    //     0xb59490: ldur            w1, [x0, #0x5b]
    // 0xb59494: DecompressPointer r1
    //     0xb59494: add             x1, x1, HEAP, lsl #32
    // 0xb59498: r16 = 12.000000
    //     0xb59498: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5949c: ldr             x16, [x16, #0x9e8]
    // 0xb594a0: stp             x1, x16, [SP]
    // 0xb594a4: ldur            x1, [fp, #-0x40]
    // 0xb594a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb594a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb594ac: ldr             x4, [x4, #0xaa0]
    // 0xb594b0: r0 = copyWith()
    //     0xb594b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb594b4: mov             x2, x0
    // 0xb594b8: ldur            x0, [fp, #-8]
    // 0xb594bc: stur            x2, [fp, #-0x48]
    // 0xb594c0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb594c0: ldur            w3, [x0, #0x17]
    // 0xb594c4: DecompressPointer r3
    //     0xb594c4: add             x3, x3, HEAP, lsl #32
    // 0xb594c8: ldur            x1, [fp, #-0x10]
    // 0xb594cc: stur            x3, [fp, #-0x40]
    // 0xb594d0: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb594d0: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb594d4: r16 = true
    //     0xb594d4: add             x16, NULL, #0x20  ; true
    // 0xb594d8: str             x16, [SP]
    // 0xb594dc: mov             x1, x0
    // 0xb594e0: r4 = const [0, 0x2, 0x1, 0x1, filled, 0x1, null]
    //     0xb594e0: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a948] List(7) [0, 0x2, 0x1, 0x1, "filled", 0x1, Null]
    //     0xb594e4: ldr             x4, [x4, #0x948]
    // 0xb594e8: r0 = copyWith()
    //     0xb594e8: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb594ec: ldur            x2, [fp, #-0x20]
    // 0xb594f0: r1 = Function '<anonymous closure>':.
    //     0xb594f0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a950] AnonymousClosure: (0xb59684), in [package:customer_app/app/presentation/views/glass/customization/customization_number.dart] _CustomisationNumberState::build (0xb58edc)
    //     0xb594f4: ldr             x1, [x1, #0x950]
    // 0xb594f8: stur            x0, [fp, #-8]
    // 0xb594fc: r0 = AllocateClosure()
    //     0xb594fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb59500: r1 = <String>
    //     0xb59500: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb59504: stur            x0, [fp, #-0x10]
    // 0xb59508: r0 = TextFormField()
    //     0xb59508: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb5950c: stur            x0, [fp, #-0x20]
    // 0xb59510: r16 = false
    //     0xb59510: add             x16, NULL, #0x30  ; false
    // 0xb59514: r30 = Instance_AutovalidateMode
    //     0xb59514: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb59518: ldr             lr, [lr, #0x7e8]
    // 0xb5951c: stp             lr, x16, [SP, #0x30]
    // 0xb59520: ldur            x16, [fp, #-0x28]
    // 0xb59524: r30 = Instance_TextInputType
    //     0xb59524: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6a420] Obj!TextInputType@d55bc1
    //     0xb59528: ldr             lr, [lr, #0x420]
    // 0xb5952c: stp             lr, x16, [SP, #0x20]
    // 0xb59530: r16 = 2
    //     0xb59530: movz            x16, #0x2
    // 0xb59534: ldur            lr, [fp, #-0x48]
    // 0xb59538: stp             lr, x16, [SP, #0x10]
    // 0xb5953c: ldur            x16, [fp, #-0x40]
    // 0xb59540: ldur            lr, [fp, #-0x10]
    // 0xb59544: stp             lr, x16, [SP]
    // 0xb59548: mov             x1, x0
    // 0xb5954c: ldur            x2, [fp, #-8]
    // 0xb59550: r4 = const [0, 0xa, 0x8, 0x2, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x6, onChanged, 0x9, style, 0x7, null]
    //     0xb59550: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a428] List(21) [0, 0xa, 0x8, 0x2, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x6, "onChanged", 0x9, "style", 0x7, Null]
    //     0xb59554: ldr             x4, [x4, #0x428]
    // 0xb59558: r0 = TextFormField()
    //     0xb59558: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb5955c: r0 = Form()
    //     0xb5955c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb59560: mov             x1, x0
    // 0xb59564: ldur            x0, [fp, #-0x20]
    // 0xb59568: stur            x1, [fp, #-8]
    // 0xb5956c: StoreField: r1->field_b = r0
    //     0xb5956c: stur            w0, [x1, #0xb]
    // 0xb59570: r0 = Instance_AutovalidateMode
    //     0xb59570: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb59574: ldr             x0, [x0, #0x800]
    // 0xb59578: StoreField: r1->field_23 = r0
    //     0xb59578: stur            w0, [x1, #0x23]
    // 0xb5957c: ldur            x0, [fp, #-0x18]
    // 0xb59580: StoreField: r1->field_7 = r0
    //     0xb59580: stur            w0, [x1, #7]
    // 0xb59584: r0 = Padding()
    //     0xb59584: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb59588: mov             x3, x0
    // 0xb5958c: r0 = Instance_EdgeInsets
    //     0xb5958c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb59590: ldr             x0, [x0, #0x668]
    // 0xb59594: stur            x3, [fp, #-0x10]
    // 0xb59598: StoreField: r3->field_f = r0
    //     0xb59598: stur            w0, [x3, #0xf]
    // 0xb5959c: ldur            x0, [fp, #-8]
    // 0xb595a0: StoreField: r3->field_b = r0
    //     0xb595a0: stur            w0, [x3, #0xb]
    // 0xb595a4: r1 = Null
    //     0xb595a4: mov             x1, NULL
    // 0xb595a8: r2 = 6
    //     0xb595a8: movz            x2, #0x6
    // 0xb595ac: r0 = AllocateArray()
    //     0xb595ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb595b0: mov             x2, x0
    // 0xb595b4: ldur            x0, [fp, #-0x30]
    // 0xb595b8: stur            x2, [fp, #-8]
    // 0xb595bc: StoreField: r2->field_f = r0
    //     0xb595bc: stur            w0, [x2, #0xf]
    // 0xb595c0: ldur            x0, [fp, #-0x38]
    // 0xb595c4: StoreField: r2->field_13 = r0
    //     0xb595c4: stur            w0, [x2, #0x13]
    // 0xb595c8: ldur            x0, [fp, #-0x10]
    // 0xb595cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb595cc: stur            w0, [x2, #0x17]
    // 0xb595d0: r1 = <Widget>
    //     0xb595d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb595d4: r0 = AllocateGrowableArray()
    //     0xb595d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb595d8: mov             x1, x0
    // 0xb595dc: ldur            x0, [fp, #-8]
    // 0xb595e0: stur            x1, [fp, #-0x10]
    // 0xb595e4: StoreField: r1->field_f = r0
    //     0xb595e4: stur            w0, [x1, #0xf]
    // 0xb595e8: r0 = 6
    //     0xb595e8: movz            x0, #0x6
    // 0xb595ec: StoreField: r1->field_b = r0
    //     0xb595ec: stur            w0, [x1, #0xb]
    // 0xb595f0: r0 = Column()
    //     0xb595f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb595f4: r1 = Instance_Axis
    //     0xb595f4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb595f8: StoreField: r0->field_f = r1
    //     0xb595f8: stur            w1, [x0, #0xf]
    // 0xb595fc: r1 = Instance_MainAxisAlignment
    //     0xb595fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb59600: ldr             x1, [x1, #0xa08]
    // 0xb59604: StoreField: r0->field_13 = r1
    //     0xb59604: stur            w1, [x0, #0x13]
    // 0xb59608: r1 = Instance_MainAxisSize
    //     0xb59608: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5960c: ldr             x1, [x1, #0xa10]
    // 0xb59610: ArrayStore: r0[0] = r1  ; List_4
    //     0xb59610: stur            w1, [x0, #0x17]
    // 0xb59614: r1 = Instance_CrossAxisAlignment
    //     0xb59614: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb59618: ldr             x1, [x1, #0x890]
    // 0xb5961c: StoreField: r0->field_1b = r1
    //     0xb5961c: stur            w1, [x0, #0x1b]
    // 0xb59620: r1 = Instance_VerticalDirection
    //     0xb59620: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb59624: ldr             x1, [x1, #0xa20]
    // 0xb59628: StoreField: r0->field_23 = r1
    //     0xb59628: stur            w1, [x0, #0x23]
    // 0xb5962c: r1 = Instance_Clip
    //     0xb5962c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb59630: ldr             x1, [x1, #0x38]
    // 0xb59634: StoreField: r0->field_2b = r1
    //     0xb59634: stur            w1, [x0, #0x2b]
    // 0xb59638: StoreField: r0->field_2f = rZR
    //     0xb59638: stur            xzr, [x0, #0x2f]
    // 0xb5963c: ldur            x1, [fp, #-0x10]
    // 0xb59640: StoreField: r0->field_b = r1
    //     0xb59640: stur            w1, [x0, #0xb]
    // 0xb59644: LeaveFrame
    //     0xb59644: mov             SP, fp
    //     0xb59648: ldp             fp, lr, [SP], #0x10
    // 0xb5964c: ret
    //     0xb5964c: ret             
    // 0xb59650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb59650: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb59654: b               #0xb58f04
    // 0xb59658: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59658: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5965c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5965c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59660: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb59684, size: 0x5cc
    // 0xb59684: EnterFrame
    //     0xb59684: stp             fp, lr, [SP, #-0x10]!
    //     0xb59688: mov             fp, SP
    // 0xb5968c: AllocStack(0x78)
    //     0xb5968c: sub             SP, SP, #0x78
    // 0xb59690: SetupParameters()
    //     0xb59690: ldr             x0, [fp, #0x18]
    //     0xb59694: ldur            w3, [x0, #0x17]
    //     0xb59698: add             x3, x3, HEAP, lsl #32
    //     0xb5969c: stur            x3, [fp, #-0x10]
    // 0xb596a0: CheckStackOverflow
    //     0xb596a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb596a4: cmp             SP, x16
    //     0xb596a8: b.ls            #0xb59c2c
    // 0xb596ac: LoadField: r0 = r3->field_f
    //     0xb596ac: ldur            w0, [x3, #0xf]
    // 0xb596b0: DecompressPointer r0
    //     0xb596b0: add             x0, x0, HEAP, lsl #32
    // 0xb596b4: LoadField: r1 = r0->field_b
    //     0xb596b4: ldur            w1, [x0, #0xb]
    // 0xb596b8: DecompressPointer r1
    //     0xb596b8: add             x1, x1, HEAP, lsl #32
    // 0xb596bc: cmp             w1, NULL
    // 0xb596c0: b.eq            #0xb59c34
    // 0xb596c4: LoadField: r0 = r1->field_f
    //     0xb596c4: ldur            w0, [x1, #0xf]
    // 0xb596c8: DecompressPointer r0
    //     0xb596c8: add             x0, x0, HEAP, lsl #32
    // 0xb596cc: stur            x0, [fp, #-8]
    // 0xb596d0: LoadField: r1 = r0->field_b
    //     0xb596d0: ldur            w1, [x0, #0xb]
    // 0xb596d4: cbz             w1, #0xb5978c
    // 0xb596d8: mov             x2, x3
    // 0xb596dc: r1 = Function '<anonymous closure>':.
    //     0xb596dc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a958] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xb596e0: ldr             x1, [x1, #0x958]
    // 0xb596e4: r0 = AllocateClosure()
    //     0xb596e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb596e8: r1 = Function '<anonymous closure>':.
    //     0xb596e8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a960] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xb596ec: ldr             x1, [x1, #0x960]
    // 0xb596f0: r2 = Null
    //     0xb596f0: mov             x2, NULL
    // 0xb596f4: stur            x0, [fp, #-0x18]
    // 0xb596f8: r0 = AllocateClosure()
    //     0xb596f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb596fc: str             x0, [SP]
    // 0xb59700: ldur            x1, [fp, #-8]
    // 0xb59704: ldur            x2, [fp, #-0x18]
    // 0xb59708: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb59708: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb5970c: ldr             x4, [x4, #0xb48]
    // 0xb59710: r0 = firstWhere()
    //     0xb59710: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb59714: LoadField: r1 = r0->field_b
    //     0xb59714: ldur            w1, [x0, #0xb]
    // 0xb59718: DecompressPointer r1
    //     0xb59718: add             x1, x1, HEAP, lsl #32
    // 0xb5971c: cmp             w1, NULL
    // 0xb59720: b.eq            #0xb5978c
    // 0xb59724: ldur            x1, [fp, #-0x10]
    // 0xb59728: LoadField: r2 = r1->field_f
    //     0xb59728: ldur            w2, [x1, #0xf]
    // 0xb5972c: DecompressPointer r2
    //     0xb5972c: add             x2, x2, HEAP, lsl #32
    // 0xb59730: LoadField: r3 = r2->field_b
    //     0xb59730: ldur            w3, [x2, #0xb]
    // 0xb59734: DecompressPointer r3
    //     0xb59734: add             x3, x3, HEAP, lsl #32
    // 0xb59738: cmp             w3, NULL
    // 0xb5973c: b.eq            #0xb59c38
    // 0xb59740: LoadField: r2 = r3->field_b
    //     0xb59740: ldur            w2, [x3, #0xb]
    // 0xb59744: DecompressPointer r2
    //     0xb59744: add             x2, x2, HEAP, lsl #32
    // 0xb59748: cmp             w2, NULL
    // 0xb5974c: b.ne            #0xb59758
    // 0xb59750: r2 = Null
    //     0xb59750: mov             x2, NULL
    // 0xb59754: b               #0xb59764
    // 0xb59758: LoadField: r4 = r2->field_23
    //     0xb59758: ldur            w4, [x2, #0x23]
    // 0xb5975c: DecompressPointer r4
    //     0xb5975c: add             x4, x4, HEAP, lsl #32
    // 0xb59760: mov             x2, x4
    // 0xb59764: LoadField: r4 = r3->field_13
    //     0xb59764: ldur            w4, [x3, #0x13]
    // 0xb59768: DecompressPointer r4
    //     0xb59768: add             x4, x4, HEAP, lsl #32
    // 0xb5976c: stp             x2, x4, [SP, #8]
    // 0xb59770: str             x0, [SP]
    // 0xb59774: r4 = 0
    //     0xb59774: movz            x4, #0
    // 0xb59778: ldr             x0, [SP, #0x10]
    // 0xb5977c: r16 = UnlinkedCall_0x613b5c
    //     0xb5977c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a968] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb59780: add             x16, x16, #0x968
    // 0xb59784: ldp             x5, lr, [x16]
    // 0xb59788: blr             lr
    // 0xb5978c: ldr             x0, [fp, #0x10]
    // 0xb59790: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb59790: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb59794: ldr             x0, [x0]
    //     0xb59798: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb5979c: cmp             w0, w16
    //     0xb597a0: b.ne            #0xb597ac
    //     0xb597a4: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb597a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb597ac: r1 = <CustomerResponse>
    //     0xb597ac: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xb597b0: ldr             x1, [x1, #0x5a8]
    // 0xb597b4: stur            x0, [fp, #-8]
    // 0xb597b8: r0 = AllocateGrowableArray()
    //     0xb597b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb597bc: mov             x2, x0
    // 0xb597c0: ldur            x0, [fp, #-8]
    // 0xb597c4: stur            x2, [fp, #-0x18]
    // 0xb597c8: StoreField: r2->field_f = r0
    //     0xb597c8: stur            w0, [x2, #0xf]
    // 0xb597cc: StoreField: r2->field_b = rZR
    //     0xb597cc: stur            wzr, [x2, #0xb]
    // 0xb597d0: r1 = <ProductCustomisation>
    //     0xb597d0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xb597d4: ldr             x1, [x1, #0x370]
    // 0xb597d8: r0 = AllocateGrowableArray()
    //     0xb597d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb597dc: mov             x1, x0
    // 0xb597e0: ldur            x0, [fp, #-8]
    // 0xb597e4: stur            x1, [fp, #-0x28]
    // 0xb597e8: StoreField: r1->field_f = r0
    //     0xb597e8: stur            w0, [x1, #0xf]
    // 0xb597ec: StoreField: r1->field_b = rZR
    //     0xb597ec: stur            wzr, [x1, #0xb]
    // 0xb597f0: ldr             x2, [fp, #0x10]
    // 0xb597f4: cmp             w2, NULL
    // 0xb597f8: b.eq            #0xb59b7c
    // 0xb597fc: ldur            x3, [fp, #-0x10]
    // 0xb59800: LoadField: r4 = r3->field_f
    //     0xb59800: ldur            w4, [x3, #0xf]
    // 0xb59804: DecompressPointer r4
    //     0xb59804: add             x4, x4, HEAP, lsl #32
    // 0xb59808: LoadField: r5 = r4->field_b
    //     0xb59808: ldur            w5, [x4, #0xb]
    // 0xb5980c: DecompressPointer r5
    //     0xb5980c: add             x5, x5, HEAP, lsl #32
    // 0xb59810: cmp             w5, NULL
    // 0xb59814: b.eq            #0xb59c3c
    // 0xb59818: LoadField: r4 = r5->field_b
    //     0xb59818: ldur            w4, [x5, #0xb]
    // 0xb5981c: DecompressPointer r4
    //     0xb5981c: add             x4, x4, HEAP, lsl #32
    // 0xb59820: cmp             w4, NULL
    // 0xb59824: b.ne            #0xb59830
    // 0xb59828: r4 = Null
    //     0xb59828: mov             x4, NULL
    // 0xb5982c: b               #0xb5983c
    // 0xb59830: LoadField: r5 = r4->field_1b
    //     0xb59830: ldur            w5, [x4, #0x1b]
    // 0xb59834: DecompressPointer r5
    //     0xb59834: add             x5, x5, HEAP, lsl #32
    // 0xb59838: mov             x4, x5
    // 0xb5983c: stur            x4, [fp, #-0x20]
    // 0xb59840: r0 = CustomerResponse()
    //     0xb59840: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xb59844: mov             x2, x0
    // 0xb59848: ldur            x0, [fp, #-0x20]
    // 0xb5984c: stur            x2, [fp, #-0x30]
    // 0xb59850: StoreField: r2->field_7 = r0
    //     0xb59850: stur            w0, [x2, #7]
    // 0xb59854: ldr             x0, [fp, #0x10]
    // 0xb59858: StoreField: r2->field_b = r0
    //     0xb59858: stur            w0, [x2, #0xb]
    // 0xb5985c: ldur            x1, [fp, #-8]
    // 0xb59860: LoadField: r3 = r1->field_b
    //     0xb59860: ldur            w3, [x1, #0xb]
    // 0xb59864: cbnz            w3, #0xb59870
    // 0xb59868: ldur            x1, [fp, #-0x18]
    // 0xb5986c: r0 = _growToNextCapacity()
    //     0xb5986c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb59870: ldur            x3, [fp, #-0x10]
    // 0xb59874: ldur            x4, [fp, #-0x18]
    // 0xb59878: r0 = 2
    //     0xb59878: movz            x0, #0x2
    // 0xb5987c: StoreField: r4->field_b = r0
    //     0xb5987c: stur            w0, [x4, #0xb]
    // 0xb59880: LoadField: r1 = r4->field_f
    //     0xb59880: ldur            w1, [x4, #0xf]
    // 0xb59884: DecompressPointer r1
    //     0xb59884: add             x1, x1, HEAP, lsl #32
    // 0xb59888: ldur            x0, [fp, #-0x30]
    // 0xb5988c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5988c: add             x25, x1, #0xf
    //     0xb59890: str             w0, [x25]
    //     0xb59894: tbz             w0, #0, #0xb598b0
    //     0xb59898: ldurb           w16, [x1, #-1]
    //     0xb5989c: ldurb           w17, [x0, #-1]
    //     0xb598a0: and             x16, x17, x16, lsr #2
    //     0xb598a4: tst             x16, HEAP, lsr #32
    //     0xb598a8: b.eq            #0xb598b0
    //     0xb598ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb598b0: LoadField: r0 = r3->field_f
    //     0xb598b0: ldur            w0, [x3, #0xf]
    // 0xb598b4: DecompressPointer r0
    //     0xb598b4: add             x0, x0, HEAP, lsl #32
    // 0xb598b8: LoadField: r1 = r0->field_b
    //     0xb598b8: ldur            w1, [x0, #0xb]
    // 0xb598bc: DecompressPointer r1
    //     0xb598bc: add             x1, x1, HEAP, lsl #32
    // 0xb598c0: cmp             w1, NULL
    // 0xb598c4: b.eq            #0xb59c40
    // 0xb598c8: LoadField: r0 = r1->field_b
    //     0xb598c8: ldur            w0, [x1, #0xb]
    // 0xb598cc: DecompressPointer r0
    //     0xb598cc: add             x0, x0, HEAP, lsl #32
    // 0xb598d0: cmp             w0, NULL
    // 0xb598d4: b.ne            #0xb598e0
    // 0xb598d8: r5 = Null
    //     0xb598d8: mov             x5, NULL
    // 0xb598dc: b               #0xb598ec
    // 0xb598e0: LoadField: r1 = r0->field_7
    //     0xb598e0: ldur            w1, [x0, #7]
    // 0xb598e4: DecompressPointer r1
    //     0xb598e4: add             x1, x1, HEAP, lsl #32
    // 0xb598e8: mov             x5, x1
    // 0xb598ec: stur            x5, [fp, #-0x38]
    // 0xb598f0: cmp             w0, NULL
    // 0xb598f4: b.ne            #0xb59900
    // 0xb598f8: r6 = Null
    //     0xb598f8: mov             x6, NULL
    // 0xb598fc: b               #0xb5990c
    // 0xb59900: LoadField: r1 = r0->field_b
    //     0xb59900: ldur            w1, [x0, #0xb]
    // 0xb59904: DecompressPointer r1
    //     0xb59904: add             x1, x1, HEAP, lsl #32
    // 0xb59908: mov             x6, x1
    // 0xb5990c: stur            x6, [fp, #-0x30]
    // 0xb59910: cmp             w0, NULL
    // 0xb59914: b.ne            #0xb59920
    // 0xb59918: r7 = Null
    //     0xb59918: mov             x7, NULL
    // 0xb5991c: b               #0xb5992c
    // 0xb59920: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb59920: ldur            w1, [x0, #0x17]
    // 0xb59924: DecompressPointer r1
    //     0xb59924: add             x1, x1, HEAP, lsl #32
    // 0xb59928: mov             x7, x1
    // 0xb5992c: stur            x7, [fp, #-0x20]
    // 0xb59930: cmp             w0, NULL
    // 0xb59934: b.ne            #0xb59940
    // 0xb59938: r0 = Null
    //     0xb59938: mov             x0, NULL
    // 0xb5993c: b               #0xb5994c
    // 0xb59940: LoadField: r1 = r0->field_23
    //     0xb59940: ldur            w1, [x0, #0x23]
    // 0xb59944: DecompressPointer r1
    //     0xb59944: add             x1, x1, HEAP, lsl #32
    // 0xb59948: mov             x0, x1
    // 0xb5994c: stur            x0, [fp, #-8]
    // 0xb59950: r1 = Null
    //     0xb59950: mov             x1, NULL
    // 0xb59954: r2 = 4
    //     0xb59954: movz            x2, #0x4
    // 0xb59958: r0 = AllocateArray()
    //     0xb59958: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5995c: stur            x0, [fp, #-0x40]
    // 0xb59960: r16 = "₹"
    //     0xb59960: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xb59964: ldr             x16, [x16, #0x360]
    // 0xb59968: StoreField: r0->field_f = r16
    //     0xb59968: stur            w16, [x0, #0xf]
    // 0xb5996c: r1 = Function '<anonymous closure>': static.
    //     0xb5996c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xb59970: ldr             x1, [x1, #0x1a0]
    // 0xb59974: r2 = Null
    //     0xb59974: mov             x2, NULL
    // 0xb59978: r0 = AllocateClosure()
    //     0xb59978: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5997c: mov             x3, x0
    // 0xb59980: r1 = Null
    //     0xb59980: mov             x1, NULL
    // 0xb59984: r2 = Null
    //     0xb59984: mov             x2, NULL
    // 0xb59988: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb59988: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb5998c: r0 = NumberFormat._forPattern()
    //     0xb5998c: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xb59990: mov             x1, x0
    // 0xb59994: ldur            x0, [fp, #-0x10]
    // 0xb59998: LoadField: r2 = r0->field_f
    //     0xb59998: ldur            w2, [x0, #0xf]
    // 0xb5999c: DecompressPointer r2
    //     0xb5999c: add             x2, x2, HEAP, lsl #32
    // 0xb599a0: LoadField: r3 = r2->field_b
    //     0xb599a0: ldur            w3, [x2, #0xb]
    // 0xb599a4: DecompressPointer r3
    //     0xb599a4: add             x3, x3, HEAP, lsl #32
    // 0xb599a8: cmp             w3, NULL
    // 0xb599ac: b.eq            #0xb59c44
    // 0xb599b0: LoadField: r2 = r3->field_b
    //     0xb599b0: ldur            w2, [x3, #0xb]
    // 0xb599b4: DecompressPointer r2
    //     0xb599b4: add             x2, x2, HEAP, lsl #32
    // 0xb599b8: cmp             w2, NULL
    // 0xb599bc: b.ne            #0xb599c8
    // 0xb599c0: r2 = Null
    //     0xb599c0: mov             x2, NULL
    // 0xb599c4: b               #0xb599d4
    // 0xb599c8: LoadField: r3 = r2->field_23
    //     0xb599c8: ldur            w3, [x2, #0x23]
    // 0xb599cc: DecompressPointer r3
    //     0xb599cc: add             x3, x3, HEAP, lsl #32
    // 0xb599d0: mov             x2, x3
    // 0xb599d4: ldur            x4, [fp, #-0x38]
    // 0xb599d8: ldur            x5, [fp, #-0x30]
    // 0xb599dc: ldur            x6, [fp, #-0x20]
    // 0xb599e0: ldur            x7, [fp, #-8]
    // 0xb599e4: ldur            x3, [fp, #-0x18]
    // 0xb599e8: ldur            x8, [fp, #-0x28]
    // 0xb599ec: r0 = format()
    //     0xb599ec: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xb599f0: ldur            x1, [fp, #-0x40]
    // 0xb599f4: ArrayStore: r1[1] = r0  ; List_4
    //     0xb599f4: add             x25, x1, #0x13
    //     0xb599f8: str             w0, [x25]
    //     0xb599fc: tbz             w0, #0, #0xb59a18
    //     0xb59a00: ldurb           w16, [x1, #-1]
    //     0xb59a04: ldurb           w17, [x0, #-1]
    //     0xb59a08: and             x16, x17, x16, lsr #2
    //     0xb59a0c: tst             x16, HEAP, lsr #32
    //     0xb59a10: b.eq            #0xb59a18
    //     0xb59a14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb59a18: ldur            x16, [fp, #-0x40]
    // 0xb59a1c: str             x16, [SP]
    // 0xb59a20: r0 = _interpolate()
    //     0xb59a20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb59a24: stur            x0, [fp, #-0x40]
    // 0xb59a28: r0 = ProductCustomisation()
    //     0xb59a28: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xb59a2c: mov             x2, x0
    // 0xb59a30: ldur            x0, [fp, #-0x38]
    // 0xb59a34: stur            x2, [fp, #-0x48]
    // 0xb59a38: StoreField: r2->field_b = r0
    //     0xb59a38: stur            w0, [x2, #0xb]
    // 0xb59a3c: ldur            x0, [fp, #-0x30]
    // 0xb59a40: StoreField: r2->field_f = r0
    //     0xb59a40: stur            w0, [x2, #0xf]
    // 0xb59a44: ldur            x0, [fp, #-0x20]
    // 0xb59a48: ArrayStore: r2[0] = r0  ; List_4
    //     0xb59a48: stur            w0, [x2, #0x17]
    // 0xb59a4c: ldur            x0, [fp, #-0x18]
    // 0xb59a50: StoreField: r2->field_23 = r0
    //     0xb59a50: stur            w0, [x2, #0x23]
    // 0xb59a54: ldur            x0, [fp, #-8]
    // 0xb59a58: StoreField: r2->field_27 = r0
    //     0xb59a58: stur            w0, [x2, #0x27]
    // 0xb59a5c: ldur            x0, [fp, #-0x40]
    // 0xb59a60: StoreField: r2->field_2b = r0
    //     0xb59a60: stur            w0, [x2, #0x2b]
    // 0xb59a64: ldur            x1, [fp, #-0x28]
    // 0xb59a68: r0 = clear()
    //     0xb59a68: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb59a6c: ldur            x0, [fp, #-0x28]
    // 0xb59a70: LoadField: r1 = r0->field_b
    //     0xb59a70: ldur            w1, [x0, #0xb]
    // 0xb59a74: LoadField: r2 = r0->field_f
    //     0xb59a74: ldur            w2, [x0, #0xf]
    // 0xb59a78: DecompressPointer r2
    //     0xb59a78: add             x2, x2, HEAP, lsl #32
    // 0xb59a7c: LoadField: r3 = r2->field_b
    //     0xb59a7c: ldur            w3, [x2, #0xb]
    // 0xb59a80: r2 = LoadInt32Instr(r1)
    //     0xb59a80: sbfx            x2, x1, #1, #0x1f
    // 0xb59a84: stur            x2, [fp, #-0x50]
    // 0xb59a88: r1 = LoadInt32Instr(r3)
    //     0xb59a88: sbfx            x1, x3, #1, #0x1f
    // 0xb59a8c: cmp             x2, x1
    // 0xb59a90: b.ne            #0xb59a9c
    // 0xb59a94: mov             x1, x0
    // 0xb59a98: r0 = _growToNextCapacity()
    //     0xb59a98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb59a9c: ldur            x4, [fp, #-0x10]
    // 0xb59aa0: ldur            x2, [fp, #-0x28]
    // 0xb59aa4: ldur            x3, [fp, #-0x50]
    // 0xb59aa8: add             x0, x3, #1
    // 0xb59aac: lsl             x1, x0, #1
    // 0xb59ab0: StoreField: r2->field_b = r1
    //     0xb59ab0: stur            w1, [x2, #0xb]
    // 0xb59ab4: LoadField: r1 = r2->field_f
    //     0xb59ab4: ldur            w1, [x2, #0xf]
    // 0xb59ab8: DecompressPointer r1
    //     0xb59ab8: add             x1, x1, HEAP, lsl #32
    // 0xb59abc: ldur            x0, [fp, #-0x48]
    // 0xb59ac0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb59ac0: add             x25, x1, x3, lsl #2
    //     0xb59ac4: add             x25, x25, #0xf
    //     0xb59ac8: str             w0, [x25]
    //     0xb59acc: tbz             w0, #0, #0xb59ae8
    //     0xb59ad0: ldurb           w16, [x1, #-1]
    //     0xb59ad4: ldurb           w17, [x0, #-1]
    //     0xb59ad8: and             x16, x17, x16, lsr #2
    //     0xb59adc: tst             x16, HEAP, lsr #32
    //     0xb59ae0: b.eq            #0xb59ae8
    //     0xb59ae4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb59ae8: LoadField: r0 = r4->field_f
    //     0xb59ae8: ldur            w0, [x4, #0xf]
    // 0xb59aec: DecompressPointer r0
    //     0xb59aec: add             x0, x0, HEAP, lsl #32
    // 0xb59af0: LoadField: r1 = r0->field_b
    //     0xb59af0: ldur            w1, [x0, #0xb]
    // 0xb59af4: DecompressPointer r1
    //     0xb59af4: add             x1, x1, HEAP, lsl #32
    // 0xb59af8: cmp             w1, NULL
    // 0xb59afc: b.eq            #0xb59c48
    // 0xb59b00: LoadField: r0 = r1->field_b
    //     0xb59b00: ldur            w0, [x1, #0xb]
    // 0xb59b04: DecompressPointer r0
    //     0xb59b04: add             x0, x0, HEAP, lsl #32
    // 0xb59b08: cmp             w0, NULL
    // 0xb59b0c: b.ne            #0xb59b18
    // 0xb59b10: r3 = Null
    //     0xb59b10: mov             x3, NULL
    // 0xb59b14: b               #0xb59b20
    // 0xb59b18: LoadField: r3 = r0->field_23
    //     0xb59b18: ldur            w3, [x0, #0x23]
    // 0xb59b1c: DecompressPointer r3
    //     0xb59b1c: add             x3, x3, HEAP, lsl #32
    // 0xb59b20: cmp             w0, NULL
    // 0xb59b24: b.ne            #0xb59b30
    // 0xb59b28: r0 = Null
    //     0xb59b28: mov             x0, NULL
    // 0xb59b2c: b               #0xb59b3c
    // 0xb59b30: LoadField: r4 = r0->field_2b
    //     0xb59b30: ldur            w4, [x0, #0x2b]
    // 0xb59b34: DecompressPointer r4
    //     0xb59b34: add             x4, x4, HEAP, lsl #32
    // 0xb59b38: mov             x0, x4
    // 0xb59b3c: cmp             w0, NULL
    // 0xb59b40: b.ne            #0xb59b48
    // 0xb59b44: r0 = false
    //     0xb59b44: add             x0, NULL, #0x30  ; false
    // 0xb59b48: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xb59b48: ldur            w4, [x1, #0x17]
    // 0xb59b4c: DecompressPointer r4
    //     0xb59b4c: add             x4, x4, HEAP, lsl #32
    // 0xb59b50: ldr             x16, [fp, #0x10]
    // 0xb59b54: stp             x16, x4, [SP, #0x18]
    // 0xb59b58: stp             x2, x3, [SP, #8]
    // 0xb59b5c: str             x0, [SP]
    // 0xb59b60: r4 = 0
    //     0xb59b60: movz            x4, #0
    // 0xb59b64: ldr             x0, [SP, #0x20]
    // 0xb59b68: r16 = UnlinkedCall_0x613b5c
    //     0xb59b68: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a978] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb59b6c: add             x16, x16, #0x978
    // 0xb59b70: ldp             x5, lr, [x16]
    // 0xb59b74: blr             lr
    // 0xb59b78: b               #0xb59c1c
    // 0xb59b7c: ldur            x4, [fp, #-0x10]
    // 0xb59b80: mov             x2, x1
    // 0xb59b84: mov             x1, x2
    // 0xb59b88: r0 = clear()
    //     0xb59b88: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb59b8c: ldur            x0, [fp, #-0x10]
    // 0xb59b90: LoadField: r1 = r0->field_f
    //     0xb59b90: ldur            w1, [x0, #0xf]
    // 0xb59b94: DecompressPointer r1
    //     0xb59b94: add             x1, x1, HEAP, lsl #32
    // 0xb59b98: LoadField: r0 = r1->field_b
    //     0xb59b98: ldur            w0, [x1, #0xb]
    // 0xb59b9c: DecompressPointer r0
    //     0xb59b9c: add             x0, x0, HEAP, lsl #32
    // 0xb59ba0: cmp             w0, NULL
    // 0xb59ba4: b.eq            #0xb59c4c
    // 0xb59ba8: LoadField: r1 = r0->field_b
    //     0xb59ba8: ldur            w1, [x0, #0xb]
    // 0xb59bac: DecompressPointer r1
    //     0xb59bac: add             x1, x1, HEAP, lsl #32
    // 0xb59bb0: cmp             w1, NULL
    // 0xb59bb4: b.ne            #0xb59bc0
    // 0xb59bb8: r2 = Null
    //     0xb59bb8: mov             x2, NULL
    // 0xb59bbc: b               #0xb59bc8
    // 0xb59bc0: LoadField: r2 = r1->field_23
    //     0xb59bc0: ldur            w2, [x1, #0x23]
    // 0xb59bc4: DecompressPointer r2
    //     0xb59bc4: add             x2, x2, HEAP, lsl #32
    // 0xb59bc8: cmp             w1, NULL
    // 0xb59bcc: b.ne            #0xb59bd8
    // 0xb59bd0: r1 = Null
    //     0xb59bd0: mov             x1, NULL
    // 0xb59bd4: b               #0xb59be4
    // 0xb59bd8: LoadField: r3 = r1->field_2b
    //     0xb59bd8: ldur            w3, [x1, #0x2b]
    // 0xb59bdc: DecompressPointer r3
    //     0xb59bdc: add             x3, x3, HEAP, lsl #32
    // 0xb59be0: mov             x1, x3
    // 0xb59be4: cmp             w1, NULL
    // 0xb59be8: b.ne            #0xb59bf0
    // 0xb59bec: r1 = false
    //     0xb59bec: add             x1, NULL, #0x30  ; false
    // 0xb59bf0: LoadField: r3 = r0->field_1b
    //     0xb59bf0: ldur            w3, [x0, #0x1b]
    // 0xb59bf4: DecompressPointer r3
    //     0xb59bf4: add             x3, x3, HEAP, lsl #32
    // 0xb59bf8: stp             x2, x3, [SP, #0x10]
    // 0xb59bfc: ldur            x16, [fp, #-0x28]
    // 0xb59c00: stp             x1, x16, [SP]
    // 0xb59c04: r4 = 0
    //     0xb59c04: movz            x4, #0
    // 0xb59c08: ldr             x0, [SP, #0x18]
    // 0xb59c0c: r16 = UnlinkedCall_0x613b5c
    //     0xb59c0c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a988] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb59c10: add             x16, x16, #0x988
    // 0xb59c14: ldp             x5, lr, [x16]
    // 0xb59c18: blr             lr
    // 0xb59c1c: r0 = Null
    //     0xb59c1c: mov             x0, NULL
    // 0xb59c20: LeaveFrame
    //     0xb59c20: mov             SP, fp
    //     0xb59c24: ldp             fp, lr, [SP], #0x10
    // 0xb59c28: ret
    //     0xb59c28: ret             
    // 0xb59c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb59c2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb59c30: b               #0xb596ac
    // 0xb59c34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59c4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59c4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4086, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomisationNumber extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f0f4, size: 0x48
    // 0xc7f0f4: EnterFrame
    //     0xc7f0f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f0f8: mov             fp, SP
    // 0xc7f0fc: AllocStack(0x8)
    //     0xc7f0fc: sub             SP, SP, #8
    // 0xc7f100: CheckStackOverflow
    //     0xc7f100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f104: cmp             SP, x16
    //     0xc7f108: b.ls            #0xc7f134
    // 0xc7f10c: r1 = <CustomisationNumber>
    //     0xc7f10c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e18] TypeArguments: <CustomisationNumber>
    //     0xc7f110: ldr             x1, [x1, #0xe18]
    // 0xc7f114: r0 = _CustomisationNumberState()
    //     0xc7f114: bl              #0xc7f13c  ; Allocate_CustomisationNumberStateStub -> _CustomisationNumberState (size=0x1c)
    // 0xc7f118: mov             x1, x0
    // 0xc7f11c: stur            x0, [fp, #-8]
    // 0xc7f120: r0 = _CustomisationTextState()
    //     0xc7f120: bl              #0xc7b760  ; [package:customer_app/app/presentation/views/basic/customization/customisation_text.dart] _CustomisationTextState::_CustomisationTextState
    // 0xc7f124: ldur            x0, [fp, #-8]
    // 0xc7f128: LeaveFrame
    //     0xc7f128: mov             SP, fp
    //     0xc7f12c: ldp             fp, lr, [SP], #0x10
    // 0xc7f130: ret
    //     0xc7f130: ret             
    // 0xc7f134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f134: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f138: b               #0xc7f10c
  }
}
