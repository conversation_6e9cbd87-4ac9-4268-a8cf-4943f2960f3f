// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/view_size_chat.dart

// class id: 1049451, size: 0x8
class :: {
}

// class id: 3301, size: 0x14, field offset: 0x14
class _ViewSizeChartState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb9443c, size: 0x3d8
    // 0xb9443c: EnterFrame
    //     0xb9443c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94440: mov             fp, SP
    // 0xb94444: AllocStack(0x38)
    //     0xb94444: sub             SP, SP, #0x38
    // 0xb94448: SetupParameters(_ViewSizeChartState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb94448: mov             x0, x2
    //     0xb9444c: stur            x2, [fp, #-0x10]
    //     0xb94450: mov             x2, x1
    //     0xb94454: stur            x1, [fp, #-8]
    // 0xb94458: CheckStackOverflow
    //     0xb94458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9445c: cmp             SP, x16
    //     0xb94460: b.ls            #0xb94808
    // 0xb94464: mov             x1, x0
    // 0xb94468: r0 = of()
    //     0xb94468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9446c: LoadField: r1 = r0->field_87
    //     0xb9446c: ldur            w1, [x0, #0x87]
    // 0xb94470: DecompressPointer r1
    //     0xb94470: add             x1, x1, HEAP, lsl #32
    // 0xb94474: LoadField: r0 = r1->field_27
    //     0xb94474: ldur            w0, [x1, #0x27]
    // 0xb94478: DecompressPointer r0
    //     0xb94478: add             x0, x0, HEAP, lsl #32
    // 0xb9447c: r16 = Instance_FontWeight
    //     0xb9447c: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb94480: ldr             x16, [x16, #0x20]
    // 0xb94484: r30 = 22.000000
    //     0xb94484: add             lr, PP, #0x51, lsl #12  ; [pp+0x510a0] 22
    //     0xb94488: ldr             lr, [lr, #0xa0]
    // 0xb9448c: stp             lr, x16, [SP]
    // 0xb94490: mov             x1, x0
    // 0xb94494: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb94494: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb94498: ldr             x4, [x4, #0xdc8]
    // 0xb9449c: r0 = copyWith()
    //     0xb9449c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb944a0: stur            x0, [fp, #-0x18]
    // 0xb944a4: r0 = Text()
    //     0xb944a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb944a8: mov             x2, x0
    // 0xb944ac: r0 = "SIZE GUIDE"
    //     0xb944ac: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd0] "SIZE GUIDE"
    //     0xb944b0: ldr             x0, [x0, #0xdd0]
    // 0xb944b4: stur            x2, [fp, #-0x20]
    // 0xb944b8: StoreField: r2->field_b = r0
    //     0xb944b8: stur            w0, [x2, #0xb]
    // 0xb944bc: ldur            x0, [fp, #-0x18]
    // 0xb944c0: StoreField: r2->field_13 = r0
    //     0xb944c0: stur            w0, [x2, #0x13]
    // 0xb944c4: ldur            x1, [fp, #-0x10]
    // 0xb944c8: r0 = of()
    //     0xb944c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb944cc: LoadField: r1 = r0->field_5b
    //     0xb944cc: ldur            w1, [x0, #0x5b]
    // 0xb944d0: DecompressPointer r1
    //     0xb944d0: add             x1, x1, HEAP, lsl #32
    // 0xb944d4: stur            x1, [fp, #-0x18]
    // 0xb944d8: r0 = Icon()
    //     0xb944d8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb944dc: mov             x1, x0
    // 0xb944e0: r0 = Instance_IconData
    //     0xb944e0: add             x0, PP, #0x53, lsl #12  ; [pp+0x53dd8] Obj!IconData@d55661
    //     0xb944e4: ldr             x0, [x0, #0xdd8]
    // 0xb944e8: stur            x1, [fp, #-0x28]
    // 0xb944ec: StoreField: r1->field_b = r0
    //     0xb944ec: stur            w0, [x1, #0xb]
    // 0xb944f0: ldur            x0, [fp, #-0x18]
    // 0xb944f4: StoreField: r1->field_23 = r0
    //     0xb944f4: stur            w0, [x1, #0x23]
    // 0xb944f8: r0 = InkWell()
    //     0xb944f8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb944fc: mov             x3, x0
    // 0xb94500: ldur            x0, [fp, #-0x28]
    // 0xb94504: stur            x3, [fp, #-0x18]
    // 0xb94508: StoreField: r3->field_b = r0
    //     0xb94508: stur            w0, [x3, #0xb]
    // 0xb9450c: r1 = Function '<anonymous closure>':.
    //     0xb9450c: add             x1, PP, #0x56, lsl #12  ; [pp+0x565c8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb94510: ldr             x1, [x1, #0x5c8]
    // 0xb94514: r2 = Null
    //     0xb94514: mov             x2, NULL
    // 0xb94518: r0 = AllocateClosure()
    //     0xb94518: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9451c: mov             x1, x0
    // 0xb94520: ldur            x0, [fp, #-0x18]
    // 0xb94524: StoreField: r0->field_f = r1
    //     0xb94524: stur            w1, [x0, #0xf]
    // 0xb94528: r3 = true
    //     0xb94528: add             x3, NULL, #0x20  ; true
    // 0xb9452c: StoreField: r0->field_43 = r3
    //     0xb9452c: stur            w3, [x0, #0x43]
    // 0xb94530: r1 = Instance_BoxShape
    //     0xb94530: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb94534: ldr             x1, [x1, #0x80]
    // 0xb94538: StoreField: r0->field_47 = r1
    //     0xb94538: stur            w1, [x0, #0x47]
    // 0xb9453c: StoreField: r0->field_6f = r3
    //     0xb9453c: stur            w3, [x0, #0x6f]
    // 0xb94540: r4 = false
    //     0xb94540: add             x4, NULL, #0x30  ; false
    // 0xb94544: StoreField: r0->field_73 = r4
    //     0xb94544: stur            w4, [x0, #0x73]
    // 0xb94548: StoreField: r0->field_83 = r3
    //     0xb94548: stur            w3, [x0, #0x83]
    // 0xb9454c: StoreField: r0->field_7b = r4
    //     0xb9454c: stur            w4, [x0, #0x7b]
    // 0xb94550: r1 = Null
    //     0xb94550: mov             x1, NULL
    // 0xb94554: r2 = 4
    //     0xb94554: movz            x2, #0x4
    // 0xb94558: r0 = AllocateArray()
    //     0xb94558: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9455c: mov             x2, x0
    // 0xb94560: ldur            x0, [fp, #-0x20]
    // 0xb94564: stur            x2, [fp, #-0x28]
    // 0xb94568: StoreField: r2->field_f = r0
    //     0xb94568: stur            w0, [x2, #0xf]
    // 0xb9456c: ldur            x0, [fp, #-0x18]
    // 0xb94570: StoreField: r2->field_13 = r0
    //     0xb94570: stur            w0, [x2, #0x13]
    // 0xb94574: r1 = <Widget>
    //     0xb94574: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb94578: r0 = AllocateGrowableArray()
    //     0xb94578: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9457c: mov             x1, x0
    // 0xb94580: ldur            x0, [fp, #-0x28]
    // 0xb94584: stur            x1, [fp, #-0x18]
    // 0xb94588: StoreField: r1->field_f = r0
    //     0xb94588: stur            w0, [x1, #0xf]
    // 0xb9458c: r0 = 4
    //     0xb9458c: movz            x0, #0x4
    // 0xb94590: StoreField: r1->field_b = r0
    //     0xb94590: stur            w0, [x1, #0xb]
    // 0xb94594: r0 = Row()
    //     0xb94594: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb94598: mov             x1, x0
    // 0xb9459c: r0 = Instance_Axis
    //     0xb9459c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb945a0: stur            x1, [fp, #-0x20]
    // 0xb945a4: StoreField: r1->field_f = r0
    //     0xb945a4: stur            w0, [x1, #0xf]
    // 0xb945a8: r0 = Instance_MainAxisAlignment
    //     0xb945a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb945ac: ldr             x0, [x0, #0xa8]
    // 0xb945b0: StoreField: r1->field_13 = r0
    //     0xb945b0: stur            w0, [x1, #0x13]
    // 0xb945b4: r0 = Instance_MainAxisSize
    //     0xb945b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb945b8: ldr             x0, [x0, #0xa10]
    // 0xb945bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb945bc: stur            w0, [x1, #0x17]
    // 0xb945c0: r0 = Instance_CrossAxisAlignment
    //     0xb945c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb945c4: ldr             x0, [x0, #0xa18]
    // 0xb945c8: StoreField: r1->field_1b = r0
    //     0xb945c8: stur            w0, [x1, #0x1b]
    // 0xb945cc: r2 = Instance_VerticalDirection
    //     0xb945cc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb945d0: ldr             x2, [x2, #0xa20]
    // 0xb945d4: StoreField: r1->field_23 = r2
    //     0xb945d4: stur            w2, [x1, #0x23]
    // 0xb945d8: r3 = Instance_Clip
    //     0xb945d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb945dc: ldr             x3, [x3, #0x38]
    // 0xb945e0: StoreField: r1->field_2b = r3
    //     0xb945e0: stur            w3, [x1, #0x2b]
    // 0xb945e4: StoreField: r1->field_2f = rZR
    //     0xb945e4: stur            xzr, [x1, #0x2f]
    // 0xb945e8: ldur            x4, [fp, #-0x18]
    // 0xb945ec: StoreField: r1->field_b = r4
    //     0xb945ec: stur            w4, [x1, #0xb]
    // 0xb945f0: r0 = Padding()
    //     0xb945f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb945f4: mov             x2, x0
    // 0xb945f8: r0 = Instance_EdgeInsets
    //     0xb945f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb945fc: ldr             x0, [x0, #0x980]
    // 0xb94600: stur            x2, [fp, #-0x18]
    // 0xb94604: StoreField: r2->field_f = r0
    //     0xb94604: stur            w0, [x2, #0xf]
    // 0xb94608: ldur            x1, [fp, #-0x20]
    // 0xb9460c: StoreField: r2->field_b = r1
    //     0xb9460c: stur            w1, [x2, #0xb]
    // 0xb94610: ldur            x1, [fp, #-0x10]
    // 0xb94614: r0 = of()
    //     0xb94614: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb94618: LoadField: r1 = r0->field_87
    //     0xb94618: ldur            w1, [x0, #0x87]
    // 0xb9461c: DecompressPointer r1
    //     0xb9461c: add             x1, x1, HEAP, lsl #32
    // 0xb94620: LoadField: r0 = r1->field_27
    //     0xb94620: ldur            w0, [x1, #0x27]
    // 0xb94624: DecompressPointer r0
    //     0xb94624: add             x0, x0, HEAP, lsl #32
    // 0xb94628: r16 = Instance_FontWeight
    //     0xb94628: add             x16, PP, #0x13, lsl #12  ; [pp+0x13010] Obj!FontWeight@d68d61
    //     0xb9462c: ldr             x16, [x16, #0x10]
    // 0xb94630: r30 = 16.000000
    //     0xb94630: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb94634: ldr             lr, [lr, #0x188]
    // 0xb94638: stp             lr, x16, [SP]
    // 0xb9463c: mov             x1, x0
    // 0xb94640: r4 = const [0, 0x3, 0x2, 0x1, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xb94640: add             x4, PP, #0x53, lsl #12  ; [pp+0x53dc8] List(9) [0, 0x3, 0x2, 0x1, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xb94644: ldr             x4, [x4, #0xdc8]
    // 0xb94648: r0 = copyWith()
    //     0xb94648: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9464c: stur            x0, [fp, #-0x10]
    // 0xb94650: r0 = Text()
    //     0xb94650: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb94654: mov             x1, x0
    // 0xb94658: r0 = "We have provided the body measurements to help you decide which size to buy."
    //     0xb94658: add             x0, PP, #0x53, lsl #12  ; [pp+0x53de8] "We have provided the body measurements to help you decide which size to buy."
    //     0xb9465c: ldr             x0, [x0, #0xde8]
    // 0xb94660: stur            x1, [fp, #-0x20]
    // 0xb94664: StoreField: r1->field_b = r0
    //     0xb94664: stur            w0, [x1, #0xb]
    // 0xb94668: ldur            x0, [fp, #-0x10]
    // 0xb9466c: StoreField: r1->field_13 = r0
    //     0xb9466c: stur            w0, [x1, #0x13]
    // 0xb94670: r0 = Padding()
    //     0xb94670: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb94674: mov             x1, x0
    // 0xb94678: r0 = Instance_EdgeInsets
    //     0xb94678: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb9467c: ldr             x0, [x0, #0x980]
    // 0xb94680: stur            x1, [fp, #-0x10]
    // 0xb94684: StoreField: r1->field_f = r0
    //     0xb94684: stur            w0, [x1, #0xf]
    // 0xb94688: ldur            x0, [fp, #-0x20]
    // 0xb9468c: StoreField: r1->field_b = r0
    //     0xb9468c: stur            w0, [x1, #0xb]
    // 0xb94690: ldur            x0, [fp, #-8]
    // 0xb94694: LoadField: r2 = r0->field_b
    //     0xb94694: ldur            w2, [x0, #0xb]
    // 0xb94698: DecompressPointer r2
    //     0xb94698: add             x2, x2, HEAP, lsl #32
    // 0xb9469c: cmp             w2, NULL
    // 0xb946a0: b.eq            #0xb94810
    // 0xb946a4: LoadField: r0 = r2->field_b
    //     0xb946a4: ldur            w0, [x2, #0xb]
    // 0xb946a8: DecompressPointer r0
    //     0xb946a8: add             x0, x0, HEAP, lsl #32
    // 0xb946ac: cmp             w0, NULL
    // 0xb946b0: b.ne            #0xb946bc
    // 0xb946b4: r2 = ""
    //     0xb946b4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb946b8: b               #0xb946c0
    // 0xb946bc: mov             x2, x0
    // 0xb946c0: ldur            x0, [fp, #-0x18]
    // 0xb946c4: stur            x2, [fp, #-8]
    // 0xb946c8: r0 = CachedNetworkImage()
    //     0xb946c8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb946cc: stur            x0, [fp, #-0x20]
    // 0xb946d0: r16 = Instance_BoxFit
    //     0xb946d0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb946d4: ldr             x16, [x16, #0xb18]
    // 0xb946d8: str             x16, [SP]
    // 0xb946dc: mov             x1, x0
    // 0xb946e0: ldur            x2, [fp, #-8]
    // 0xb946e4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb946e4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb946e8: ldr             x4, [x4, #0xb0]
    // 0xb946ec: r0 = CachedNetworkImage()
    //     0xb946ec: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb946f0: r1 = Null
    //     0xb946f0: mov             x1, NULL
    // 0xb946f4: r2 = 8
    //     0xb946f4: movz            x2, #0x8
    // 0xb946f8: r0 = AllocateArray()
    //     0xb946f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb946fc: mov             x2, x0
    // 0xb94700: ldur            x0, [fp, #-0x18]
    // 0xb94704: stur            x2, [fp, #-8]
    // 0xb94708: StoreField: r2->field_f = r0
    //     0xb94708: stur            w0, [x2, #0xf]
    // 0xb9470c: ldur            x0, [fp, #-0x10]
    // 0xb94710: StoreField: r2->field_13 = r0
    //     0xb94710: stur            w0, [x2, #0x13]
    // 0xb94714: r16 = Instance_SizedBox
    //     0xb94714: add             x16, PP, #0x53, lsl #12  ; [pp+0x53df8] Obj!SizedBox@d68061
    //     0xb94718: ldr             x16, [x16, #0xdf8]
    // 0xb9471c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb9471c: stur            w16, [x2, #0x17]
    // 0xb94720: ldur            x0, [fp, #-0x20]
    // 0xb94724: StoreField: r2->field_1b = r0
    //     0xb94724: stur            w0, [x2, #0x1b]
    // 0xb94728: r1 = <Widget>
    //     0xb94728: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9472c: r0 = AllocateGrowableArray()
    //     0xb9472c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb94730: mov             x1, x0
    // 0xb94734: ldur            x0, [fp, #-8]
    // 0xb94738: stur            x1, [fp, #-0x10]
    // 0xb9473c: StoreField: r1->field_f = r0
    //     0xb9473c: stur            w0, [x1, #0xf]
    // 0xb94740: r0 = 8
    //     0xb94740: movz            x0, #0x8
    // 0xb94744: StoreField: r1->field_b = r0
    //     0xb94744: stur            w0, [x1, #0xb]
    // 0xb94748: r0 = Column()
    //     0xb94748: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9474c: mov             x1, x0
    // 0xb94750: r0 = Instance_Axis
    //     0xb94750: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb94754: stur            x1, [fp, #-8]
    // 0xb94758: StoreField: r1->field_f = r0
    //     0xb94758: stur            w0, [x1, #0xf]
    // 0xb9475c: r0 = Instance_MainAxisAlignment
    //     0xb9475c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb94760: ldr             x0, [x0, #0xa08]
    // 0xb94764: StoreField: r1->field_13 = r0
    //     0xb94764: stur            w0, [x1, #0x13]
    // 0xb94768: r0 = Instance_MainAxisSize
    //     0xb94768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb9476c: ldr             x0, [x0, #0xdd0]
    // 0xb94770: ArrayStore: r1[0] = r0  ; List_4
    //     0xb94770: stur            w0, [x1, #0x17]
    // 0xb94774: r0 = Instance_CrossAxisAlignment
    //     0xb94774: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb94778: ldr             x0, [x0, #0xa18]
    // 0xb9477c: StoreField: r1->field_1b = r0
    //     0xb9477c: stur            w0, [x1, #0x1b]
    // 0xb94780: r0 = Instance_VerticalDirection
    //     0xb94780: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb94784: ldr             x0, [x0, #0xa20]
    // 0xb94788: StoreField: r1->field_23 = r0
    //     0xb94788: stur            w0, [x1, #0x23]
    // 0xb9478c: r0 = Instance_Clip
    //     0xb9478c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb94790: ldr             x0, [x0, #0x38]
    // 0xb94794: StoreField: r1->field_2b = r0
    //     0xb94794: stur            w0, [x1, #0x2b]
    // 0xb94798: StoreField: r1->field_2f = rZR
    //     0xb94798: stur            xzr, [x1, #0x2f]
    // 0xb9479c: ldur            x0, [fp, #-0x10]
    // 0xb947a0: StoreField: r1->field_b = r0
    //     0xb947a0: stur            w0, [x1, #0xb]
    // 0xb947a4: r0 = Scaffold()
    //     0xb947a4: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb947a8: mov             x1, x0
    // 0xb947ac: ldur            x0, [fp, #-8]
    // 0xb947b0: stur            x1, [fp, #-0x10]
    // 0xb947b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb947b4: stur            w0, [x1, #0x17]
    // 0xb947b8: r0 = true
    //     0xb947b8: add             x0, NULL, #0x20  ; true
    // 0xb947bc: StoreField: r1->field_43 = r0
    //     0xb947bc: stur            w0, [x1, #0x43]
    // 0xb947c0: r2 = false
    //     0xb947c0: add             x2, NULL, #0x30  ; false
    // 0xb947c4: StoreField: r1->field_b = r2
    //     0xb947c4: stur            w2, [x1, #0xb]
    // 0xb947c8: StoreField: r1->field_f = r2
    //     0xb947c8: stur            w2, [x1, #0xf]
    // 0xb947cc: r0 = SafeArea()
    //     0xb947cc: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb947d0: r1 = true
    //     0xb947d0: add             x1, NULL, #0x20  ; true
    // 0xb947d4: StoreField: r0->field_b = r1
    //     0xb947d4: stur            w1, [x0, #0xb]
    // 0xb947d8: StoreField: r0->field_f = r1
    //     0xb947d8: stur            w1, [x0, #0xf]
    // 0xb947dc: StoreField: r0->field_13 = r1
    //     0xb947dc: stur            w1, [x0, #0x13]
    // 0xb947e0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb947e0: stur            w1, [x0, #0x17]
    // 0xb947e4: r1 = Instance_EdgeInsets
    //     0xb947e4: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb947e8: StoreField: r0->field_1b = r1
    //     0xb947e8: stur            w1, [x0, #0x1b]
    // 0xb947ec: r1 = false
    //     0xb947ec: add             x1, NULL, #0x30  ; false
    // 0xb947f0: StoreField: r0->field_1f = r1
    //     0xb947f0: stur            w1, [x0, #0x1f]
    // 0xb947f4: ldur            x1, [fp, #-0x10]
    // 0xb947f8: StoreField: r0->field_23 = r1
    //     0xb947f8: stur            w1, [x0, #0x23]
    // 0xb947fc: LeaveFrame
    //     0xb947fc: mov             SP, fp
    //     0xb94800: ldp             fp, lr, [SP], #0x10
    // 0xb94804: ret
    //     0xb94804: ret             
    // 0xb94808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94808: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9480c: b               #0xb94464
    // 0xb94810: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94810: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4044, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeChart extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fc1c, size: 0x24
    // 0xc7fc1c: EnterFrame
    //     0xc7fc1c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fc20: mov             fp, SP
    // 0xc7fc24: mov             x0, x1
    // 0xc7fc28: r1 = <ViewSizeChart>
    //     0xc7fc28: add             x1, PP, #0x48, lsl #12  ; [pp+0x489c8] TypeArguments: <ViewSizeChart>
    //     0xc7fc2c: ldr             x1, [x1, #0x9c8]
    // 0xc7fc30: r0 = _ViewSizeChartState()
    //     0xc7fc30: bl              #0xc7fc40  ; Allocate_ViewSizeChartStateStub -> _ViewSizeChartState (size=0x14)
    // 0xc7fc34: LeaveFrame
    //     0xc7fc34: mov             SP, fp
    //     0xc7fc38: ldp             fp, lr, [SP], #0x10
    // 0xc7fc3c: ret
    //     0xc7fc3c: ret             
  }
}
