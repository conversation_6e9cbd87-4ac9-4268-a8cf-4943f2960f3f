// lib: , url: package:customer_app/app/presentation/views/cosmetic/rating_review/rating_review_media_screen.dart

// class id: 1049333, size: 0x8
class :: {
}

// class id: 4584, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewMediaScreen extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d17a8, size: 0x64
    // 0x14d17a8: EnterFrame
    //     0x14d17a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14d17ac: mov             fp, SP
    // 0x14d17b0: AllocStack(0x18)
    //     0x14d17b0: sub             SP, SP, #0x18
    // 0x14d17b4: SetupParameters(RatingReviewMediaScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d17b4: stur            x1, [fp, #-8]
    //     0x14d17b8: stur            x2, [fp, #-0x10]
    // 0x14d17bc: r1 = 2
    //     0x14d17bc: movz            x1, #0x2
    // 0x14d17c0: r0 = AllocateContext()
    //     0x14d17c0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d17c4: mov             x1, x0
    // 0x14d17c8: ldur            x0, [fp, #-8]
    // 0x14d17cc: stur            x1, [fp, #-0x18]
    // 0x14d17d0: StoreField: r1->field_f = r0
    //     0x14d17d0: stur            w0, [x1, #0xf]
    // 0x14d17d4: ldur            x0, [fp, #-0x10]
    // 0x14d17d8: StoreField: r1->field_13 = r0
    //     0x14d17d8: stur            w0, [x1, #0x13]
    // 0x14d17dc: r0 = Obx()
    //     0x14d17dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d17e0: ldur            x2, [fp, #-0x18]
    // 0x14d17e4: r1 = Function '<anonymous closure>':.
    //     0x14d17e4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c68] AnonymousClosure: (0x14d180c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14d17a8)
    //     0x14d17e8: ldr             x1, [x1, #0xc68]
    // 0x14d17ec: stur            x0, [fp, #-8]
    // 0x14d17f0: r0 = AllocateClosure()
    //     0x14d17f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d17f4: mov             x1, x0
    // 0x14d17f8: ldur            x0, [fp, #-8]
    // 0x14d17fc: StoreField: r0->field_b = r1
    //     0x14d17fc: stur            w1, [x0, #0xb]
    // 0x14d1800: LeaveFrame
    //     0x14d1800: mov             SP, fp
    //     0x14d1804: ldp             fp, lr, [SP], #0x10
    // 0x14d1808: ret
    //     0x14d1808: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14d180c, size: 0x454
    // 0x14d180c: EnterFrame
    //     0x14d180c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1810: mov             fp, SP
    // 0x14d1814: AllocStack(0x38)
    //     0x14d1814: sub             SP, SP, #0x38
    // 0x14d1818: SetupParameters()
    //     0x14d1818: ldr             x0, [fp, #0x10]
    //     0x14d181c: ldur            w2, [x0, #0x17]
    //     0x14d1820: add             x2, x2, HEAP, lsl #32
    //     0x14d1824: stur            x2, [fp, #-8]
    // 0x14d1828: CheckStackOverflow
    //     0x14d1828: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d182c: cmp             SP, x16
    //     0x14d1830: b.ls            #0x14d1c58
    // 0x14d1834: LoadField: r1 = r2->field_f
    //     0x14d1834: ldur            w1, [x2, #0xf]
    // 0x14d1838: DecompressPointer r1
    //     0x14d1838: add             x1, x1, HEAP, lsl #32
    // 0x14d183c: r0 = controller()
    //     0x14d183c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d1840: LoadField: r1 = r0->field_4f
    //     0x14d1840: ldur            w1, [x0, #0x4f]
    // 0x14d1844: DecompressPointer r1
    //     0x14d1844: add             x1, x1, HEAP, lsl #32
    // 0x14d1848: r0 = value()
    //     0x14d1848: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d184c: LoadField: r1 = r0->field_b
    //     0x14d184c: ldur            w1, [x0, #0xb]
    // 0x14d1850: DecompressPointer r1
    //     0x14d1850: add             x1, x1, HEAP, lsl #32
    // 0x14d1854: cmp             w1, NULL
    // 0x14d1858: b.ne            #0x14d1878
    // 0x14d185c: r0 = Container()
    //     0x14d185c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d1860: mov             x1, x0
    // 0x14d1864: stur            x0, [fp, #-0x10]
    // 0x14d1868: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d1868: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d186c: r0 = Container()
    //     0x14d186c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d1870: ldur            x0, [fp, #-0x10]
    // 0x14d1874: b               #0x14d1c4c
    // 0x14d1878: ldur            x2, [fp, #-8]
    // 0x14d187c: LoadField: r1 = r2->field_13
    //     0x14d187c: ldur            w1, [x2, #0x13]
    // 0x14d1880: DecompressPointer r1
    //     0x14d1880: add             x1, x1, HEAP, lsl #32
    // 0x14d1884: r0 = of()
    //     0x14d1884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d1888: LoadField: r1 = r0->field_87
    //     0x14d1888: ldur            w1, [x0, #0x87]
    // 0x14d188c: DecompressPointer r1
    //     0x14d188c: add             x1, x1, HEAP, lsl #32
    // 0x14d1890: LoadField: r0 = r1->field_7
    //     0x14d1890: ldur            w0, [x1, #7]
    // 0x14d1894: DecompressPointer r0
    //     0x14d1894: add             x0, x0, HEAP, lsl #32
    // 0x14d1898: r16 = 16.000000
    //     0x14d1898: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14d189c: ldr             x16, [x16, #0x188]
    // 0x14d18a0: r30 = Instance_Color
    //     0x14d18a0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d18a4: stp             lr, x16, [SP]
    // 0x14d18a8: mov             x1, x0
    // 0x14d18ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14d18ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14d18b0: ldr             x4, [x4, #0xaa0]
    // 0x14d18b4: r0 = copyWith()
    //     0x14d18b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d18b8: stur            x0, [fp, #-0x10]
    // 0x14d18bc: r0 = Text()
    //     0x14d18bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d18c0: mov             x2, x0
    // 0x14d18c4: r0 = "Real images from customers"
    //     0x14d18c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0x14d18c8: ldr             x0, [x0, #0x88]
    // 0x14d18cc: stur            x2, [fp, #-0x18]
    // 0x14d18d0: StoreField: r2->field_b = r0
    //     0x14d18d0: stur            w0, [x2, #0xb]
    // 0x14d18d4: ldur            x0, [fp, #-0x10]
    // 0x14d18d8: StoreField: r2->field_13 = r0
    //     0x14d18d8: stur            w0, [x2, #0x13]
    // 0x14d18dc: ldur            x0, [fp, #-8]
    // 0x14d18e0: LoadField: r1 = r0->field_13
    //     0x14d18e0: ldur            w1, [x0, #0x13]
    // 0x14d18e4: DecompressPointer r1
    //     0x14d18e4: add             x1, x1, HEAP, lsl #32
    // 0x14d18e8: r0 = of()
    //     0x14d18e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d18ec: LoadField: r1 = r0->field_5b
    //     0x14d18ec: ldur            w1, [x0, #0x5b]
    // 0x14d18f0: DecompressPointer r1
    //     0x14d18f0: add             x1, x1, HEAP, lsl #32
    // 0x14d18f4: stur            x1, [fp, #-0x10]
    // 0x14d18f8: r0 = Icon()
    //     0x14d18f8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14d18fc: mov             x1, x0
    // 0x14d1900: r0 = Instance_IconData
    //     0x14d1900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f090] Obj!IconData@d55181
    //     0x14d1904: ldr             x0, [x0, #0x90]
    // 0x14d1908: stur            x1, [fp, #-0x20]
    // 0x14d190c: StoreField: r1->field_b = r0
    //     0x14d190c: stur            w0, [x1, #0xb]
    // 0x14d1910: r0 = 25.000000
    //     0x14d1910: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x14d1914: ldr             x0, [x0, #0x98]
    // 0x14d1918: StoreField: r1->field_f = r0
    //     0x14d1918: stur            w0, [x1, #0xf]
    // 0x14d191c: ldur            x0, [fp, #-0x10]
    // 0x14d1920: StoreField: r1->field_23 = r0
    //     0x14d1920: stur            w0, [x1, #0x23]
    // 0x14d1924: r0 = InkWell()
    //     0x14d1924: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d1928: mov             x3, x0
    // 0x14d192c: ldur            x0, [fp, #-0x20]
    // 0x14d1930: stur            x3, [fp, #-0x10]
    // 0x14d1934: StoreField: r3->field_b = r0
    //     0x14d1934: stur            w0, [x3, #0xb]
    // 0x14d1938: r1 = Function '<anonymous closure>':.
    //     0x14d1938: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c70] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14d193c: ldr             x1, [x1, #0xc70]
    // 0x14d1940: r2 = Null
    //     0x14d1940: mov             x2, NULL
    // 0x14d1944: r0 = AllocateClosure()
    //     0x14d1944: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1948: mov             x1, x0
    // 0x14d194c: ldur            x0, [fp, #-0x10]
    // 0x14d1950: StoreField: r0->field_f = r1
    //     0x14d1950: stur            w1, [x0, #0xf]
    // 0x14d1954: r3 = true
    //     0x14d1954: add             x3, NULL, #0x20  ; true
    // 0x14d1958: StoreField: r0->field_43 = r3
    //     0x14d1958: stur            w3, [x0, #0x43]
    // 0x14d195c: r1 = Instance_BoxShape
    //     0x14d195c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d1960: ldr             x1, [x1, #0x80]
    // 0x14d1964: StoreField: r0->field_47 = r1
    //     0x14d1964: stur            w1, [x0, #0x47]
    // 0x14d1968: StoreField: r0->field_6f = r3
    //     0x14d1968: stur            w3, [x0, #0x6f]
    // 0x14d196c: r4 = false
    //     0x14d196c: add             x4, NULL, #0x30  ; false
    // 0x14d1970: StoreField: r0->field_73 = r4
    //     0x14d1970: stur            w4, [x0, #0x73]
    // 0x14d1974: StoreField: r0->field_83 = r3
    //     0x14d1974: stur            w3, [x0, #0x83]
    // 0x14d1978: StoreField: r0->field_7b = r4
    //     0x14d1978: stur            w4, [x0, #0x7b]
    // 0x14d197c: r1 = Null
    //     0x14d197c: mov             x1, NULL
    // 0x14d1980: r2 = 4
    //     0x14d1980: movz            x2, #0x4
    // 0x14d1984: r0 = AllocateArray()
    //     0x14d1984: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d1988: mov             x2, x0
    // 0x14d198c: ldur            x0, [fp, #-0x18]
    // 0x14d1990: stur            x2, [fp, #-0x20]
    // 0x14d1994: StoreField: r2->field_f = r0
    //     0x14d1994: stur            w0, [x2, #0xf]
    // 0x14d1998: ldur            x0, [fp, #-0x10]
    // 0x14d199c: StoreField: r2->field_13 = r0
    //     0x14d199c: stur            w0, [x2, #0x13]
    // 0x14d19a0: r1 = <Widget>
    //     0x14d19a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d19a4: r0 = AllocateGrowableArray()
    //     0x14d19a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d19a8: mov             x1, x0
    // 0x14d19ac: ldur            x0, [fp, #-0x20]
    // 0x14d19b0: stur            x1, [fp, #-0x10]
    // 0x14d19b4: StoreField: r1->field_f = r0
    //     0x14d19b4: stur            w0, [x1, #0xf]
    // 0x14d19b8: r2 = 4
    //     0x14d19b8: movz            x2, #0x4
    // 0x14d19bc: StoreField: r1->field_b = r2
    //     0x14d19bc: stur            w2, [x1, #0xb]
    // 0x14d19c0: r0 = Row()
    //     0x14d19c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d19c4: mov             x1, x0
    // 0x14d19c8: r0 = Instance_Axis
    //     0x14d19c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d19cc: stur            x1, [fp, #-0x18]
    // 0x14d19d0: StoreField: r1->field_f = r0
    //     0x14d19d0: stur            w0, [x1, #0xf]
    // 0x14d19d4: r0 = Instance_MainAxisAlignment
    //     0x14d19d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14d19d8: ldr             x0, [x0, #0xa8]
    // 0x14d19dc: StoreField: r1->field_13 = r0
    //     0x14d19dc: stur            w0, [x1, #0x13]
    // 0x14d19e0: r0 = Instance_MainAxisSize
    //     0x14d19e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d19e4: ldr             x0, [x0, #0xa10]
    // 0x14d19e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d19e8: stur            w0, [x1, #0x17]
    // 0x14d19ec: r2 = Instance_CrossAxisAlignment
    //     0x14d19ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d19f0: ldr             x2, [x2, #0xa18]
    // 0x14d19f4: StoreField: r1->field_1b = r2
    //     0x14d19f4: stur            w2, [x1, #0x1b]
    // 0x14d19f8: r3 = Instance_VerticalDirection
    //     0x14d19f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d19fc: ldr             x3, [x3, #0xa20]
    // 0x14d1a00: StoreField: r1->field_23 = r3
    //     0x14d1a00: stur            w3, [x1, #0x23]
    // 0x14d1a04: r4 = Instance_Clip
    //     0x14d1a04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d1a08: ldr             x4, [x4, #0x38]
    // 0x14d1a0c: StoreField: r1->field_2b = r4
    //     0x14d1a0c: stur            w4, [x1, #0x2b]
    // 0x14d1a10: StoreField: r1->field_2f = rZR
    //     0x14d1a10: stur            xzr, [x1, #0x2f]
    // 0x14d1a14: ldur            x5, [fp, #-0x10]
    // 0x14d1a18: StoreField: r1->field_b = r5
    //     0x14d1a18: stur            w5, [x1, #0xb]
    // 0x14d1a1c: r0 = Padding()
    //     0x14d1a1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d1a20: mov             x2, x0
    // 0x14d1a24: r0 = Instance_EdgeInsets
    //     0x14d1a24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x14d1a28: ldr             x0, [x0, #0xb0]
    // 0x14d1a2c: stur            x2, [fp, #-0x10]
    // 0x14d1a30: StoreField: r2->field_f = r0
    //     0x14d1a30: stur            w0, [x2, #0xf]
    // 0x14d1a34: ldur            x0, [fp, #-0x18]
    // 0x14d1a38: StoreField: r2->field_b = r0
    //     0x14d1a38: stur            w0, [x2, #0xb]
    // 0x14d1a3c: ldur            x0, [fp, #-8]
    // 0x14d1a40: LoadField: r1 = r0->field_f
    //     0x14d1a40: ldur            w1, [x0, #0xf]
    // 0x14d1a44: DecompressPointer r1
    //     0x14d1a44: add             x1, x1, HEAP, lsl #32
    // 0x14d1a48: r0 = controller()
    //     0x14d1a48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d1a4c: LoadField: r1 = r0->field_4f
    //     0x14d1a4c: ldur            w1, [x0, #0x4f]
    // 0x14d1a50: DecompressPointer r1
    //     0x14d1a50: add             x1, x1, HEAP, lsl #32
    // 0x14d1a54: r0 = value()
    //     0x14d1a54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d1a58: LoadField: r1 = r0->field_b
    //     0x14d1a58: ldur            w1, [x0, #0xb]
    // 0x14d1a5c: DecompressPointer r1
    //     0x14d1a5c: add             x1, x1, HEAP, lsl #32
    // 0x14d1a60: cmp             w1, NULL
    // 0x14d1a64: b.ne            #0x14d1a70
    // 0x14d1a68: r5 = Null
    //     0x14d1a68: mov             x5, NULL
    // 0x14d1a6c: b               #0x14d1aac
    // 0x14d1a70: LoadField: r0 = r1->field_b
    //     0x14d1a70: ldur            w0, [x1, #0xb]
    // 0x14d1a74: DecompressPointer r0
    //     0x14d1a74: add             x0, x0, HEAP, lsl #32
    // 0x14d1a78: stur            x0, [fp, #-0x18]
    // 0x14d1a7c: r1 = Function '<anonymous closure>':.
    //     0x14d1a7c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c78] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14d1a80: ldr             x1, [x1, #0xc78]
    // 0x14d1a84: r2 = Null
    //     0x14d1a84: mov             x2, NULL
    // 0x14d1a88: r0 = AllocateClosure()
    //     0x14d1a88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1a8c: ldur            x16, [fp, #-0x18]
    // 0x14d1a90: stp             x16, NULL, [SP, #8]
    // 0x14d1a94: str             x0, [SP]
    // 0x14d1a98: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14d1a98: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14d1a9c: r0 = expand()
    //     0x14d1a9c: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14d1aa0: str             x0, [SP]
    // 0x14d1aa4: r0 = length()
    //     0x14d1aa4: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x14d1aa8: mov             x5, x0
    // 0x14d1aac: ldur            x0, [fp, #-0x10]
    // 0x14d1ab0: ldur            x2, [fp, #-8]
    // 0x14d1ab4: stur            x5, [fp, #-0x18]
    // 0x14d1ab8: r1 = Function '<anonymous closure>':.
    //     0x14d1ab8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c80] AnonymousClosure: (0x14d1c60), in [package:customer_app/app/presentation/views/cosmetic/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14d17a8)
    //     0x14d1abc: ldr             x1, [x1, #0xc80]
    // 0x14d1ac0: r0 = AllocateClosure()
    //     0x14d1ac0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1ac4: stur            x0, [fp, #-8]
    // 0x14d1ac8: r0 = GridView()
    //     0x14d1ac8: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x14d1acc: mov             x1, x0
    // 0x14d1ad0: ldur            x3, [fp, #-8]
    // 0x14d1ad4: ldur            x5, [fp, #-0x18]
    // 0x14d1ad8: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x14d1ad8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0c8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d56481
    //     0x14d1adc: ldr             x2, [x2, #0xc8]
    // 0x14d1ae0: stur            x0, [fp, #-8]
    // 0x14d1ae4: r0 = GridView.builder()
    //     0x14d1ae4: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x14d1ae8: r1 = Null
    //     0x14d1ae8: mov             x1, NULL
    // 0x14d1aec: r2 = 4
    //     0x14d1aec: movz            x2, #0x4
    // 0x14d1af0: r0 = AllocateArray()
    //     0x14d1af0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d1af4: mov             x2, x0
    // 0x14d1af8: ldur            x0, [fp, #-0x10]
    // 0x14d1afc: stur            x2, [fp, #-0x18]
    // 0x14d1b00: StoreField: r2->field_f = r0
    //     0x14d1b00: stur            w0, [x2, #0xf]
    // 0x14d1b04: ldur            x0, [fp, #-8]
    // 0x14d1b08: StoreField: r2->field_13 = r0
    //     0x14d1b08: stur            w0, [x2, #0x13]
    // 0x14d1b0c: r1 = <Widget>
    //     0x14d1b0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d1b10: r0 = AllocateGrowableArray()
    //     0x14d1b10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d1b14: mov             x1, x0
    // 0x14d1b18: ldur            x0, [fp, #-0x18]
    // 0x14d1b1c: stur            x1, [fp, #-8]
    // 0x14d1b20: StoreField: r1->field_f = r0
    //     0x14d1b20: stur            w0, [x1, #0xf]
    // 0x14d1b24: r0 = 4
    //     0x14d1b24: movz            x0, #0x4
    // 0x14d1b28: StoreField: r1->field_b = r0
    //     0x14d1b28: stur            w0, [x1, #0xb]
    // 0x14d1b2c: r0 = Column()
    //     0x14d1b2c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d1b30: mov             x1, x0
    // 0x14d1b34: r0 = Instance_Axis
    //     0x14d1b34: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d1b38: stur            x1, [fp, #-0x10]
    // 0x14d1b3c: StoreField: r1->field_f = r0
    //     0x14d1b3c: stur            w0, [x1, #0xf]
    // 0x14d1b40: r2 = Instance_MainAxisAlignment
    //     0x14d1b40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d1b44: ldr             x2, [x2, #0xa08]
    // 0x14d1b48: StoreField: r1->field_13 = r2
    //     0x14d1b48: stur            w2, [x1, #0x13]
    // 0x14d1b4c: r2 = Instance_MainAxisSize
    //     0x14d1b4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d1b50: ldr             x2, [x2, #0xa10]
    // 0x14d1b54: ArrayStore: r1[0] = r2  ; List_4
    //     0x14d1b54: stur            w2, [x1, #0x17]
    // 0x14d1b58: r2 = Instance_CrossAxisAlignment
    //     0x14d1b58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d1b5c: ldr             x2, [x2, #0xa18]
    // 0x14d1b60: StoreField: r1->field_1b = r2
    //     0x14d1b60: stur            w2, [x1, #0x1b]
    // 0x14d1b64: r2 = Instance_VerticalDirection
    //     0x14d1b64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d1b68: ldr             x2, [x2, #0xa20]
    // 0x14d1b6c: StoreField: r1->field_23 = r2
    //     0x14d1b6c: stur            w2, [x1, #0x23]
    // 0x14d1b70: r2 = Instance_Clip
    //     0x14d1b70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d1b74: ldr             x2, [x2, #0x38]
    // 0x14d1b78: StoreField: r1->field_2b = r2
    //     0x14d1b78: stur            w2, [x1, #0x2b]
    // 0x14d1b7c: StoreField: r1->field_2f = rZR
    //     0x14d1b7c: stur            xzr, [x1, #0x2f]
    // 0x14d1b80: ldur            x2, [fp, #-8]
    // 0x14d1b84: StoreField: r1->field_b = r2
    //     0x14d1b84: stur            w2, [x1, #0xb]
    // 0x14d1b88: r0 = Padding()
    //     0x14d1b88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d1b8c: mov             x1, x0
    // 0x14d1b90: r0 = Instance_EdgeInsets
    //     0x14d1b90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x14d1b94: ldr             x0, [x0, #0xd0]
    // 0x14d1b98: stur            x1, [fp, #-8]
    // 0x14d1b9c: StoreField: r1->field_f = r0
    //     0x14d1b9c: stur            w0, [x1, #0xf]
    // 0x14d1ba0: ldur            x0, [fp, #-0x10]
    // 0x14d1ba4: StoreField: r1->field_b = r0
    //     0x14d1ba4: stur            w0, [x1, #0xb]
    // 0x14d1ba8: r0 = SingleChildScrollView()
    //     0x14d1ba8: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14d1bac: mov             x1, x0
    // 0x14d1bb0: r0 = Instance_Axis
    //     0x14d1bb0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d1bb4: stur            x1, [fp, #-0x10]
    // 0x14d1bb8: StoreField: r1->field_b = r0
    //     0x14d1bb8: stur            w0, [x1, #0xb]
    // 0x14d1bbc: r0 = false
    //     0x14d1bbc: add             x0, NULL, #0x30  ; false
    // 0x14d1bc0: StoreField: r1->field_f = r0
    //     0x14d1bc0: stur            w0, [x1, #0xf]
    // 0x14d1bc4: ldur            x2, [fp, #-8]
    // 0x14d1bc8: StoreField: r1->field_23 = r2
    //     0x14d1bc8: stur            w2, [x1, #0x23]
    // 0x14d1bcc: r2 = Instance_DragStartBehavior
    //     0x14d1bcc: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14d1bd0: StoreField: r1->field_27 = r2
    //     0x14d1bd0: stur            w2, [x1, #0x27]
    // 0x14d1bd4: r2 = Instance_Clip
    //     0x14d1bd4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14d1bd8: ldr             x2, [x2, #0x7e0]
    // 0x14d1bdc: StoreField: r1->field_2b = r2
    //     0x14d1bdc: stur            w2, [x1, #0x2b]
    // 0x14d1be0: r2 = Instance_HitTestBehavior
    //     0x14d1be0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14d1be4: ldr             x2, [x2, #0x288]
    // 0x14d1be8: StoreField: r1->field_2f = r2
    //     0x14d1be8: stur            w2, [x1, #0x2f]
    // 0x14d1bec: r0 = SafeArea()
    //     0x14d1bec: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14d1bf0: mov             x1, x0
    // 0x14d1bf4: r0 = true
    //     0x14d1bf4: add             x0, NULL, #0x20  ; true
    // 0x14d1bf8: stur            x1, [fp, #-8]
    // 0x14d1bfc: StoreField: r1->field_b = r0
    //     0x14d1bfc: stur            w0, [x1, #0xb]
    // 0x14d1c00: StoreField: r1->field_f = r0
    //     0x14d1c00: stur            w0, [x1, #0xf]
    // 0x14d1c04: StoreField: r1->field_13 = r0
    //     0x14d1c04: stur            w0, [x1, #0x13]
    // 0x14d1c08: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d1c08: stur            w0, [x1, #0x17]
    // 0x14d1c0c: r2 = Instance_EdgeInsets
    //     0x14d1c0c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d1c10: StoreField: r1->field_1b = r2
    //     0x14d1c10: stur            w2, [x1, #0x1b]
    // 0x14d1c14: r2 = false
    //     0x14d1c14: add             x2, NULL, #0x30  ; false
    // 0x14d1c18: StoreField: r1->field_1f = r2
    //     0x14d1c18: stur            w2, [x1, #0x1f]
    // 0x14d1c1c: ldur            x3, [fp, #-0x10]
    // 0x14d1c20: StoreField: r1->field_23 = r3
    //     0x14d1c20: stur            w3, [x1, #0x23]
    // 0x14d1c24: r0 = Scaffold()
    //     0x14d1c24: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0x14d1c28: ldur            x1, [fp, #-8]
    // 0x14d1c2c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d1c2c: stur            w1, [x0, #0x17]
    // 0x14d1c30: r1 = Instance_Color
    //     0x14d1c30: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14d1c34: StoreField: r0->field_33 = r1
    //     0x14d1c34: stur            w1, [x0, #0x33]
    // 0x14d1c38: r1 = true
    //     0x14d1c38: add             x1, NULL, #0x20  ; true
    // 0x14d1c3c: StoreField: r0->field_43 = r1
    //     0x14d1c3c: stur            w1, [x0, #0x43]
    // 0x14d1c40: r1 = false
    //     0x14d1c40: add             x1, NULL, #0x30  ; false
    // 0x14d1c44: StoreField: r0->field_b = r1
    //     0x14d1c44: stur            w1, [x0, #0xb]
    // 0x14d1c48: StoreField: r0->field_f = r1
    //     0x14d1c48: stur            w1, [x0, #0xf]
    // 0x14d1c4c: LeaveFrame
    //     0x14d1c4c: mov             SP, fp
    //     0x14d1c50: ldp             fp, lr, [SP], #0x10
    // 0x14d1c54: ret
    //     0x14d1c54: ret             
    // 0x14d1c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d1c58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d1c5c: b               #0x14d1834
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14d1c60, size: 0x33c
    // 0x14d1c60: EnterFrame
    //     0x14d1c60: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1c64: mov             fp, SP
    // 0x14d1c68: AllocStack(0x60)
    //     0x14d1c68: sub             SP, SP, #0x60
    // 0x14d1c6c: SetupParameters()
    //     0x14d1c6c: ldr             x0, [fp, #0x20]
    //     0x14d1c70: ldur            w1, [x0, #0x17]
    //     0x14d1c74: add             x1, x1, HEAP, lsl #32
    //     0x14d1c78: stur            x1, [fp, #-8]
    // 0x14d1c7c: CheckStackOverflow
    //     0x14d1c7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d1c80: cmp             SP, x16
    //     0x14d1c84: b.ls            #0x14d1f90
    // 0x14d1c88: r1 = 3
    //     0x14d1c88: movz            x1, #0x3
    // 0x14d1c8c: r0 = AllocateContext()
    //     0x14d1c8c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d1c90: mov             x2, x0
    // 0x14d1c94: ldur            x0, [fp, #-8]
    // 0x14d1c98: stur            x2, [fp, #-0x10]
    // 0x14d1c9c: StoreField: r2->field_b = r0
    //     0x14d1c9c: stur            w0, [x2, #0xb]
    // 0x14d1ca0: ldr             x1, [fp, #0x18]
    // 0x14d1ca4: StoreField: r2->field_f = r1
    //     0x14d1ca4: stur            w1, [x2, #0xf]
    // 0x14d1ca8: ldr             x3, [fp, #0x10]
    // 0x14d1cac: StoreField: r2->field_13 = r3
    //     0x14d1cac: stur            w3, [x2, #0x13]
    // 0x14d1cb0: LoadField: r1 = r0->field_f
    //     0x14d1cb0: ldur            w1, [x0, #0xf]
    // 0x14d1cb4: DecompressPointer r1
    //     0x14d1cb4: add             x1, x1, HEAP, lsl #32
    // 0x14d1cb8: r0 = controller()
    //     0x14d1cb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d1cbc: LoadField: r1 = r0->field_4f
    //     0x14d1cbc: ldur            w1, [x0, #0x4f]
    // 0x14d1cc0: DecompressPointer r1
    //     0x14d1cc0: add             x1, x1, HEAP, lsl #32
    // 0x14d1cc4: r0 = value()
    //     0x14d1cc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d1cc8: LoadField: r1 = r0->field_b
    //     0x14d1cc8: ldur            w1, [x0, #0xb]
    // 0x14d1ccc: DecompressPointer r1
    //     0x14d1ccc: add             x1, x1, HEAP, lsl #32
    // 0x14d1cd0: cmp             w1, NULL
    // 0x14d1cd4: b.ne            #0x14d1ce0
    // 0x14d1cd8: r2 = Null
    //     0x14d1cd8: mov             x2, NULL
    // 0x14d1cdc: b               #0x14d1d20
    // 0x14d1ce0: LoadField: r0 = r1->field_b
    //     0x14d1ce0: ldur            w0, [x1, #0xb]
    // 0x14d1ce4: DecompressPointer r0
    //     0x14d1ce4: add             x0, x0, HEAP, lsl #32
    // 0x14d1ce8: stur            x0, [fp, #-8]
    // 0x14d1cec: r1 = Function '<anonymous closure>':.
    //     0x14d1cec: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c88] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14d1cf0: ldr             x1, [x1, #0xc88]
    // 0x14d1cf4: r2 = Null
    //     0x14d1cf4: mov             x2, NULL
    // 0x14d1cf8: r0 = AllocateClosure()
    //     0x14d1cf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1cfc: ldur            x16, [fp, #-8]
    // 0x14d1d00: stp             x16, NULL, [SP, #8]
    // 0x14d1d04: str             x0, [SP]
    // 0x14d1d08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14d1d08: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14d1d0c: r0 = expand()
    //     0x14d1d0c: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14d1d10: mov             x1, x0
    // 0x14d1d14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d1d14: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d1d18: r0 = toList()
    //     0x14d1d18: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x14d1d1c: mov             x2, x0
    // 0x14d1d20: cmp             w2, NULL
    // 0x14d1d24: b.ne            #0x14d1d30
    // 0x14d1d28: r1 = Null
    //     0x14d1d28: mov             x1, NULL
    // 0x14d1d2c: b               #0x14d1d68
    // 0x14d1d30: ldr             x0, [fp, #0x10]
    // 0x14d1d34: LoadField: r1 = r2->field_b
    //     0x14d1d34: ldur            w1, [x2, #0xb]
    // 0x14d1d38: r3 = LoadInt32Instr(r0)
    //     0x14d1d38: sbfx            x3, x0, #1, #0x1f
    //     0x14d1d3c: tbz             w0, #0, #0x14d1d44
    //     0x14d1d40: ldur            x3, [x0, #7]
    // 0x14d1d44: r0 = LoadInt32Instr(r1)
    //     0x14d1d44: sbfx            x0, x1, #1, #0x1f
    // 0x14d1d48: mov             x1, x3
    // 0x14d1d4c: cmp             x1, x0
    // 0x14d1d50: b.hs            #0x14d1f98
    // 0x14d1d54: LoadField: r0 = r2->field_f
    //     0x14d1d54: ldur            w0, [x2, #0xf]
    // 0x14d1d58: DecompressPointer r0
    //     0x14d1d58: add             x0, x0, HEAP, lsl #32
    // 0x14d1d5c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14d1d5c: add             x16, x0, x3, lsl #2
    //     0x14d1d60: ldur            w1, [x16, #0xf]
    // 0x14d1d64: DecompressPointer r1
    //     0x14d1d64: add             x1, x1, HEAP, lsl #32
    // 0x14d1d68: ldur            x2, [fp, #-0x10]
    // 0x14d1d6c: mov             x0, x1
    // 0x14d1d70: stur            x1, [fp, #-8]
    // 0x14d1d74: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d1d74: stur            w0, [x2, #0x17]
    //     0x14d1d78: tbz             w0, #0, #0x14d1d94
    //     0x14d1d7c: ldurb           w16, [x2, #-1]
    //     0x14d1d80: ldurb           w17, [x0, #-1]
    //     0x14d1d84: and             x16, x17, x16, lsr #2
    //     0x14d1d88: tst             x16, HEAP, lsr #32
    //     0x14d1d8c: b.eq            #0x14d1d94
    //     0x14d1d90: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x14d1d94: r0 = Radius()
    //     0x14d1d94: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d1d98: d0 = 12.000000
    //     0x14d1d98: fmov            d0, #12.00000000
    // 0x14d1d9c: stur            x0, [fp, #-0x18]
    // 0x14d1da0: StoreField: r0->field_7 = d0
    //     0x14d1da0: stur            d0, [x0, #7]
    // 0x14d1da4: StoreField: r0->field_f = d0
    //     0x14d1da4: stur            d0, [x0, #0xf]
    // 0x14d1da8: r0 = BorderRadius()
    //     0x14d1da8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d1dac: mov             x1, x0
    // 0x14d1db0: ldur            x0, [fp, #-0x18]
    // 0x14d1db4: stur            x1, [fp, #-0x20]
    // 0x14d1db8: StoreField: r1->field_7 = r0
    //     0x14d1db8: stur            w0, [x1, #7]
    // 0x14d1dbc: StoreField: r1->field_b = r0
    //     0x14d1dbc: stur            w0, [x1, #0xb]
    // 0x14d1dc0: StoreField: r1->field_f = r0
    //     0x14d1dc0: stur            w0, [x1, #0xf]
    // 0x14d1dc4: StoreField: r1->field_13 = r0
    //     0x14d1dc4: stur            w0, [x1, #0x13]
    // 0x14d1dc8: ldur            x16, [fp, #-8]
    // 0x14d1dcc: str             x16, [SP]
    // 0x14d1dd0: r4 = 0
    //     0x14d1dd0: movz            x4, #0
    // 0x14d1dd4: ldr             x0, [SP]
    // 0x14d1dd8: r16 = UnlinkedCall_0x613b5c
    //     0x14d1dd8: add             x16, PP, #0x41, lsl #12  ; [pp+0x41c90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d1ddc: add             x16, x16, #0xc90
    // 0x14d1de0: ldp             x5, lr, [x16]
    // 0x14d1de4: blr             lr
    // 0x14d1de8: r1 = 60
    //     0x14d1de8: movz            x1, #0x3c
    // 0x14d1dec: branchIfSmi(r0, 0x14d1df8)
    //     0x14d1dec: tbz             w0, #0, #0x14d1df8
    // 0x14d1df0: r1 = LoadClassIdInstr(r0)
    //     0x14d1df0: ldur            x1, [x0, #-1]
    //     0x14d1df4: ubfx            x1, x1, #0xc, #0x14
    // 0x14d1df8: r16 = "image"
    //     0x14d1df8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x14d1dfc: stp             x16, x0, [SP]
    // 0x14d1e00: mov             x0, x1
    // 0x14d1e04: mov             lr, x0
    // 0x14d1e08: ldr             lr, [x21, lr, lsl #3]
    // 0x14d1e0c: blr             lr
    // 0x14d1e10: tbnz            w0, #4, #0x14d1ebc
    // 0x14d1e14: ldur            x16, [fp, #-8]
    // 0x14d1e18: str             x16, [SP]
    // 0x14d1e1c: r4 = 0
    //     0x14d1e1c: movz            x4, #0
    // 0x14d1e20: ldr             x0, [SP]
    // 0x14d1e24: r16 = UnlinkedCall_0x613b5c
    //     0x14d1e24: add             x16, PP, #0x41, lsl #12  ; [pp+0x41ca0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d1e28: add             x16, x16, #0xca0
    // 0x14d1e2c: ldp             x5, lr, [x16]
    // 0x14d1e30: blr             lr
    // 0x14d1e34: cmp             w0, NULL
    // 0x14d1e38: b.ne            #0x14d1e40
    // 0x14d1e3c: r0 = ""
    //     0x14d1e3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d1e40: stur            x0, [fp, #-0x18]
    // 0x14d1e44: r1 = Function '<anonymous closure>':.
    //     0x14d1e44: add             x1, PP, #0x41, lsl #12  ; [pp+0x41cb0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14d1e48: ldr             x1, [x1, #0xcb0]
    // 0x14d1e4c: r2 = Null
    //     0x14d1e4c: mov             x2, NULL
    // 0x14d1e50: r0 = AllocateClosure()
    //     0x14d1e50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1e54: r1 = Function '<anonymous closure>':.
    //     0x14d1e54: add             x1, PP, #0x41, lsl #12  ; [pp+0x41cb8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14d1e58: ldr             x1, [x1, #0xcb8]
    // 0x14d1e5c: r2 = Null
    //     0x14d1e5c: mov             x2, NULL
    // 0x14d1e60: stur            x0, [fp, #-0x28]
    // 0x14d1e64: r0 = AllocateClosure()
    //     0x14d1e64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1e68: stur            x0, [fp, #-0x30]
    // 0x14d1e6c: r0 = CachedNetworkImage()
    //     0x14d1e6c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14d1e70: stur            x0, [fp, #-0x38]
    // 0x14d1e74: r16 = 60.000000
    //     0x14d1e74: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14d1e78: ldr             x16, [x16, #0x110]
    // 0x14d1e7c: r30 = 60.000000
    //     0x14d1e7c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14d1e80: ldr             lr, [lr, #0x110]
    // 0x14d1e84: stp             lr, x16, [SP, #0x18]
    // 0x14d1e88: r16 = Instance_BoxFit
    //     0x14d1e88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14d1e8c: ldr             x16, [x16, #0x118]
    // 0x14d1e90: ldur            lr, [fp, #-0x28]
    // 0x14d1e94: stp             lr, x16, [SP, #8]
    // 0x14d1e98: ldur            x16, [fp, #-0x30]
    // 0x14d1e9c: str             x16, [SP]
    // 0x14d1ea0: mov             x1, x0
    // 0x14d1ea4: ldur            x2, [fp, #-0x18]
    // 0x14d1ea8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14d1ea8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14d1eac: ldr             x4, [x4, #0xc28]
    // 0x14d1eb0: r0 = CachedNetworkImage()
    //     0x14d1eb0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14d1eb4: ldur            x1, [fp, #-0x38]
    // 0x14d1eb8: b               #0x14d1efc
    // 0x14d1ebc: ldur            x16, [fp, #-8]
    // 0x14d1ec0: str             x16, [SP]
    // 0x14d1ec4: r4 = 0
    //     0x14d1ec4: movz            x4, #0
    // 0x14d1ec8: ldr             x0, [SP]
    // 0x14d1ecc: r16 = UnlinkedCall_0x613b5c
    //     0x14d1ecc: add             x16, PP, #0x41, lsl #12  ; [pp+0x41cc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d1ed0: add             x16, x16, #0xcc0
    // 0x14d1ed4: ldp             x5, lr, [x16]
    // 0x14d1ed8: blr             lr
    // 0x14d1edc: cmp             w0, NULL
    // 0x14d1ee0: b.ne            #0x14d1ee8
    // 0x14d1ee4: r0 = ""
    //     0x14d1ee4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d1ee8: stur            x0, [fp, #-8]
    // 0x14d1eec: r0 = VideoPlayerWidget()
    //     0x14d1eec: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x14d1ef0: mov             x1, x0
    // 0x14d1ef4: ldur            x0, [fp, #-8]
    // 0x14d1ef8: StoreField: r1->field_b = r0
    //     0x14d1ef8: stur            w0, [x1, #0xb]
    // 0x14d1efc: ldur            x0, [fp, #-0x20]
    // 0x14d1f00: stur            x1, [fp, #-8]
    // 0x14d1f04: r0 = ClipRRect()
    //     0x14d1f04: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14d1f08: mov             x1, x0
    // 0x14d1f0c: ldur            x0, [fp, #-0x20]
    // 0x14d1f10: stur            x1, [fp, #-0x18]
    // 0x14d1f14: StoreField: r1->field_f = r0
    //     0x14d1f14: stur            w0, [x1, #0xf]
    // 0x14d1f18: r0 = Instance_Clip
    //     0x14d1f18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14d1f1c: ldr             x0, [x0, #0x138]
    // 0x14d1f20: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d1f20: stur            w0, [x1, #0x17]
    // 0x14d1f24: ldur            x0, [fp, #-8]
    // 0x14d1f28: StoreField: r1->field_b = r0
    //     0x14d1f28: stur            w0, [x1, #0xb]
    // 0x14d1f2c: r0 = InkWell()
    //     0x14d1f2c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d1f30: mov             x3, x0
    // 0x14d1f34: ldur            x0, [fp, #-0x18]
    // 0x14d1f38: stur            x3, [fp, #-8]
    // 0x14d1f3c: StoreField: r3->field_b = r0
    //     0x14d1f3c: stur            w0, [x3, #0xb]
    // 0x14d1f40: ldur            x2, [fp, #-0x10]
    // 0x14d1f44: r1 = Function '<anonymous closure>':.
    //     0x14d1f44: add             x1, PP, #0x41, lsl #12  ; [pp+0x41cd0] AnonymousClosure: (0x14d1f9c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14d17a8)
    //     0x14d1f48: ldr             x1, [x1, #0xcd0]
    // 0x14d1f4c: r0 = AllocateClosure()
    //     0x14d1f4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1f50: mov             x1, x0
    // 0x14d1f54: ldur            x0, [fp, #-8]
    // 0x14d1f58: StoreField: r0->field_f = r1
    //     0x14d1f58: stur            w1, [x0, #0xf]
    // 0x14d1f5c: r1 = true
    //     0x14d1f5c: add             x1, NULL, #0x20  ; true
    // 0x14d1f60: StoreField: r0->field_43 = r1
    //     0x14d1f60: stur            w1, [x0, #0x43]
    // 0x14d1f64: r2 = Instance_BoxShape
    //     0x14d1f64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d1f68: ldr             x2, [x2, #0x80]
    // 0x14d1f6c: StoreField: r0->field_47 = r2
    //     0x14d1f6c: stur            w2, [x0, #0x47]
    // 0x14d1f70: StoreField: r0->field_6f = r1
    //     0x14d1f70: stur            w1, [x0, #0x6f]
    // 0x14d1f74: r2 = false
    //     0x14d1f74: add             x2, NULL, #0x30  ; false
    // 0x14d1f78: StoreField: r0->field_73 = r2
    //     0x14d1f78: stur            w2, [x0, #0x73]
    // 0x14d1f7c: StoreField: r0->field_83 = r1
    //     0x14d1f7c: stur            w1, [x0, #0x83]
    // 0x14d1f80: StoreField: r0->field_7b = r2
    //     0x14d1f80: stur            w2, [x0, #0x7b]
    // 0x14d1f84: LeaveFrame
    //     0x14d1f84: mov             SP, fp
    //     0x14d1f88: ldp             fp, lr, [SP], #0x10
    // 0x14d1f8c: ret
    //     0x14d1f8c: ret             
    // 0x14d1f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d1f90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d1f94: b               #0x14d1c88
    // 0x14d1f98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d1f98: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d1f9c, size: 0x9c
    // 0x14d1f9c: EnterFrame
    //     0x14d1f9c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1fa0: mov             fp, SP
    // 0x14d1fa4: AllocStack(0x28)
    //     0x14d1fa4: sub             SP, SP, #0x28
    // 0x14d1fa8: SetupParameters()
    //     0x14d1fa8: ldr             x0, [fp, #0x10]
    //     0x14d1fac: ldur            w2, [x0, #0x17]
    //     0x14d1fb0: add             x2, x2, HEAP, lsl #32
    //     0x14d1fb4: stur            x2, [fp, #-8]
    // 0x14d1fb8: CheckStackOverflow
    //     0x14d1fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d1fbc: cmp             SP, x16
    //     0x14d1fc0: b.ls            #0x14d2030
    // 0x14d1fc4: LoadField: r1 = r2->field_f
    //     0x14d1fc4: ldur            w1, [x2, #0xf]
    // 0x14d1fc8: DecompressPointer r1
    //     0x14d1fc8: add             x1, x1, HEAP, lsl #32
    // 0x14d1fcc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d1fcc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d1fd0: r0 = of()
    //     0x14d1fd0: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x14d1fd4: ldur            x2, [fp, #-8]
    // 0x14d1fd8: r1 = Function '<anonymous closure>':.
    //     0x14d1fd8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41cd8] AnonymousClosure: (0x14d2038), in [package:customer_app/app/presentation/views/cosmetic/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14d17a8)
    //     0x14d1fdc: ldr             x1, [x1, #0xcd8]
    // 0x14d1fe0: stur            x0, [fp, #-8]
    // 0x14d1fe4: r0 = AllocateClosure()
    //     0x14d1fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1fe8: r1 = Null
    //     0x14d1fe8: mov             x1, NULL
    // 0x14d1fec: stur            x0, [fp, #-0x10]
    // 0x14d1ff0: r0 = MaterialPageRoute()
    //     0x14d1ff0: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x14d1ff4: mov             x1, x0
    // 0x14d1ff8: ldur            x2, [fp, #-0x10]
    // 0x14d1ffc: stur            x0, [fp, #-0x10]
    // 0x14d2000: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14d2000: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14d2004: r0 = MaterialPageRoute()
    //     0x14d2004: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x14d2008: ldur            x16, [fp, #-8]
    // 0x14d200c: stp             x16, NULL, [SP, #8]
    // 0x14d2010: ldur            x16, [fp, #-0x10]
    // 0x14d2014: str             x16, [SP]
    // 0x14d2018: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14d2018: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14d201c: r0 = push()
    //     0x14d201c: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x14d2020: r0 = Null
    //     0x14d2020: mov             x0, NULL
    // 0x14d2024: LeaveFrame
    //     0x14d2024: mov             SP, fp
    //     0x14d2028: ldp             fp, lr, [SP], #0x10
    // 0x14d202c: ret
    //     0x14d202c: ret             
    // 0x14d2030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d2030: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d2034: b               #0x14d1fc4
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14d2038, size: 0x1a4
    // 0x14d2038: EnterFrame
    //     0x14d2038: stp             fp, lr, [SP, #-0x10]!
    //     0x14d203c: mov             fp, SP
    // 0x14d2040: AllocStack(0x38)
    //     0x14d2040: sub             SP, SP, #0x38
    // 0x14d2044: SetupParameters()
    //     0x14d2044: ldr             x0, [fp, #0x18]
    //     0x14d2048: ldur            w2, [x0, #0x17]
    //     0x14d204c: add             x2, x2, HEAP, lsl #32
    //     0x14d2050: stur            x2, [fp, #-0x10]
    // 0x14d2054: CheckStackOverflow
    //     0x14d2054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d2058: cmp             SP, x16
    //     0x14d205c: b.ls            #0x14d21d4
    // 0x14d2060: LoadField: r0 = r2->field_b
    //     0x14d2060: ldur            w0, [x2, #0xb]
    // 0x14d2064: DecompressPointer r0
    //     0x14d2064: add             x0, x0, HEAP, lsl #32
    // 0x14d2068: stur            x0, [fp, #-8]
    // 0x14d206c: LoadField: r1 = r0->field_f
    //     0x14d206c: ldur            w1, [x0, #0xf]
    // 0x14d2070: DecompressPointer r1
    //     0x14d2070: add             x1, x1, HEAP, lsl #32
    // 0x14d2074: r0 = controller()
    //     0x14d2074: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2078: LoadField: r1 = r0->field_4f
    //     0x14d2078: ldur            w1, [x0, #0x4f]
    // 0x14d207c: DecompressPointer r1
    //     0x14d207c: add             x1, x1, HEAP, lsl #32
    // 0x14d2080: r0 = value()
    //     0x14d2080: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d2084: LoadField: r1 = r0->field_b
    //     0x14d2084: ldur            w1, [x0, #0xb]
    // 0x14d2088: DecompressPointer r1
    //     0x14d2088: add             x1, x1, HEAP, lsl #32
    // 0x14d208c: cmp             w1, NULL
    // 0x14d2090: b.ne            #0x14d209c
    // 0x14d2094: r0 = Null
    //     0x14d2094: mov             x0, NULL
    // 0x14d2098: b               #0x14d20a4
    // 0x14d209c: LoadField: r0 = r1->field_b
    //     0x14d209c: ldur            w0, [x1, #0xb]
    // 0x14d20a0: DecompressPointer r0
    //     0x14d20a0: add             x0, x0, HEAP, lsl #32
    // 0x14d20a4: cmp             w0, NULL
    // 0x14d20a8: b.ne            #0x14d20c4
    // 0x14d20ac: r1 = <ReviewRatingEntity>
    //     0x14d20ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0x14d20b0: ldr             x1, [x1, #0x150]
    // 0x14d20b4: r2 = 0
    //     0x14d20b4: movz            x2, #0
    // 0x14d20b8: r0 = _GrowableList()
    //     0x14d20b8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14d20bc: mov             x1, x0
    // 0x14d20c0: b               #0x14d20c8
    // 0x14d20c4: mov             x1, x0
    // 0x14d20c8: ldur            x2, [fp, #-0x10]
    // 0x14d20cc: ldur            x0, [fp, #-8]
    // 0x14d20d0: stur            x1, [fp, #-0x20]
    // 0x14d20d4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14d20d4: ldur            w3, [x2, #0x17]
    // 0x14d20d8: DecompressPointer r3
    //     0x14d20d8: add             x3, x3, HEAP, lsl #32
    // 0x14d20dc: stur            x3, [fp, #-0x18]
    // 0x14d20e0: str             x3, [SP]
    // 0x14d20e4: r4 = 0
    //     0x14d20e4: movz            x4, #0
    // 0x14d20e8: ldr             x0, [SP]
    // 0x14d20ec: r16 = UnlinkedCall_0x613b5c
    //     0x14d20ec: add             x16, PP, #0x41, lsl #12  ; [pp+0x41ce0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d20f0: add             x16, x16, #0xce0
    // 0x14d20f4: ldp             x5, lr, [x16]
    // 0x14d20f8: blr             lr
    // 0x14d20fc: stur            x0, [fp, #-0x28]
    // 0x14d2100: ldur            x16, [fp, #-0x18]
    // 0x14d2104: str             x16, [SP]
    // 0x14d2108: r4 = 0
    //     0x14d2108: movz            x4, #0
    // 0x14d210c: ldr             x0, [SP]
    // 0x14d2110: r16 = UnlinkedCall_0x613b5c
    //     0x14d2110: add             x16, PP, #0x41, lsl #12  ; [pp+0x41cf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14d2114: add             x16, x16, #0xcf0
    // 0x14d2118: ldp             x5, lr, [x16]
    // 0x14d211c: blr             lr
    // 0x14d2120: mov             x3, x0
    // 0x14d2124: r2 = Null
    //     0x14d2124: mov             x2, NULL
    // 0x14d2128: r1 = Null
    //     0x14d2128: mov             x1, NULL
    // 0x14d212c: stur            x3, [fp, #-0x18]
    // 0x14d2130: branchIfSmi(r0, 0x14d2158)
    //     0x14d2130: tbz             w0, #0, #0x14d2158
    // 0x14d2134: r4 = LoadClassIdInstr(r0)
    //     0x14d2134: ldur            x4, [x0, #-1]
    //     0x14d2138: ubfx            x4, x4, #0xc, #0x14
    // 0x14d213c: sub             x4, x4, #0x3c
    // 0x14d2140: cmp             x4, #1
    // 0x14d2144: b.ls            #0x14d2158
    // 0x14d2148: r8 = int?
    //     0x14d2148: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0x14d214c: r3 = Null
    //     0x14d214c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41d00] Null
    //     0x14d2150: ldr             x3, [x3, #0xd00]
    // 0x14d2154: r0 = int?()
    //     0x14d2154: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0x14d2158: ldur            x0, [fp, #-8]
    // 0x14d215c: LoadField: r1 = r0->field_f
    //     0x14d215c: ldur            w1, [x0, #0xf]
    // 0x14d2160: DecompressPointer r1
    //     0x14d2160: add             x1, x1, HEAP, lsl #32
    // 0x14d2164: r0 = controller()
    //     0x14d2164: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2168: LoadField: r1 = r0->field_63
    //     0x14d2168: ldur            w1, [x0, #0x63]
    // 0x14d216c: DecompressPointer r1
    //     0x14d216c: add             x1, x1, HEAP, lsl #32
    // 0x14d2170: stur            x1, [fp, #-8]
    // 0x14d2174: r0 = RatingReviewAllMediaOnTapImage()
    //     0x14d2174: bl              #0xb15c24  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0x14d2178: mov             x3, x0
    // 0x14d217c: ldur            x0, [fp, #-0x20]
    // 0x14d2180: stur            x3, [fp, #-0x30]
    // 0x14d2184: StoreField: r3->field_f = r0
    //     0x14d2184: stur            w0, [x3, #0xf]
    // 0x14d2188: ldur            x0, [fp, #-0x28]
    // 0x14d218c: r1 = LoadInt32Instr(r0)
    //     0x14d218c: sbfx            x1, x0, #1, #0x1f
    //     0x14d2190: tbz             w0, #0, #0x14d2198
    //     0x14d2194: ldur            x1, [x0, #7]
    // 0x14d2198: StoreField: r3->field_13 = r1
    //     0x14d2198: stur            x1, [x3, #0x13]
    // 0x14d219c: ldur            x0, [fp, #-0x18]
    // 0x14d21a0: StoreField: r3->field_1b = r0
    //     0x14d21a0: stur            w0, [x3, #0x1b]
    // 0x14d21a4: ldur            x2, [fp, #-0x10]
    // 0x14d21a8: r1 = Function '<anonymous closure>':.
    //     0x14d21a8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d10] AnonymousClosure: (0x8ff1ac), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14d21ac: ldr             x1, [x1, #0xd10]
    // 0x14d21b0: r0 = AllocateClosure()
    //     0x14d21b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d21b4: mov             x1, x0
    // 0x14d21b8: ldur            x0, [fp, #-0x30]
    // 0x14d21bc: StoreField: r0->field_1f = r1
    //     0x14d21bc: stur            w1, [x0, #0x1f]
    // 0x14d21c0: ldur            x1, [fp, #-8]
    // 0x14d21c4: StoreField: r0->field_23 = r1
    //     0x14d21c4: stur            w1, [x0, #0x23]
    // 0x14d21c8: LeaveFrame
    //     0x14d21c8: mov             SP, fp
    //     0x14d21cc: ldp             fp, lr, [SP], #0x10
    // 0x14d21d0: ret
    //     0x14d21d0: ret             
    // 0x14d21d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d21d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d21d8: b               #0x14d2060
  }
}
