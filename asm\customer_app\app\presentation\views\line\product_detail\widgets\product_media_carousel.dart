// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart

// class id: 1049560, size: 0x8
class :: {
}

// class id: 3223, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin extends State<dynamic>
     with AutomaticKeepAliveClientMixin<X0 bound StatefulWidget> {

  _ deactivate(/* No info */) {
    // ** addr: 0x7f37ec, size: 0x40
    // 0x7f37ec: EnterFrame
    //     0x7f37ec: stp             fp, lr, [SP, #-0x10]!
    //     0x7f37f0: mov             fp, SP
    // 0x7f37f4: CheckStackOverflow
    //     0x7f37f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f37f8: cmp             SP, x16
    //     0x7f37fc: b.ls            #0x7f3824
    // 0x7f3800: LoadField: r0 = r1->field_13
    //     0x7f3800: ldur            w0, [x1, #0x13]
    // 0x7f3804: DecompressPointer r0
    //     0x7f3804: add             x0, x0, HEAP, lsl #32
    // 0x7f3808: cmp             w0, NULL
    // 0x7f380c: b.eq            #0x7f3814
    // 0x7f3810: r0 = _releaseKeepAlive()
    //     0x7f3810: bl              #0x7f36b0  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_releaseKeepAlive
    // 0x7f3814: r0 = Null
    //     0x7f3814: mov             x0, NULL
    // 0x7f3818: LeaveFrame
    //     0x7f3818: mov             SP, fp
    //     0x7f381c: ldp             fp, lr, [SP], #0x10
    // 0x7f3820: ret
    //     0x7f3820: ret             
    // 0x7f3824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f3824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f3828: b               #0x7f3800
  }
  _ initState(/* No info */) {
    // ** addr: 0x94a9e8, size: 0x30
    // 0x94a9e8: EnterFrame
    //     0x94a9e8: stp             fp, lr, [SP, #-0x10]!
    //     0x94a9ec: mov             fp, SP
    // 0x94a9f0: CheckStackOverflow
    //     0x94a9f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a9f4: cmp             SP, x16
    //     0x94a9f8: b.ls            #0x94aa10
    // 0x94a9fc: r0 = _ensureKeepAlive()
    //     0x94a9fc: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0x94aa00: r0 = Null
    //     0x94aa00: mov             x0, NULL
    // 0x94aa04: LeaveFrame
    //     0x94aa04: mov             SP, fp
    //     0x94aa08: ldp             fp, lr, [SP], #0x10
    // 0x94aa0c: ret
    //     0x94aa0c: ret             
    // 0x94aa10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94aa10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94aa14: b               #0x94a9fc
  }
  _ build(/* No info */) {
    // ** addr: 0xc050b8, size: 0x44
    // 0xc050b8: EnterFrame
    //     0xc050b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc050bc: mov             fp, SP
    // 0xc050c0: CheckStackOverflow
    //     0xc050c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc050c4: cmp             SP, x16
    //     0xc050c8: b.ls            #0xc050f4
    // 0xc050cc: LoadField: r0 = r1->field_13
    //     0xc050cc: ldur            w0, [x1, #0x13]
    // 0xc050d0: DecompressPointer r0
    //     0xc050d0: add             x0, x0, HEAP, lsl #32
    // 0xc050d4: cmp             w0, NULL
    // 0xc050d8: b.ne            #0xc050e0
    // 0xc050dc: r0 = _ensureKeepAlive()
    //     0xc050dc: bl              #0x80e0d4  ; [package:flutter/src/material/ink_well.dart] __InkResponseState&State&AutomaticKeepAliveClientMixin::_ensureKeepAlive
    // 0xc050e0: r0 = Instance__NullWidget
    //     0xc050e0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b08] Obj!_NullWidget@d66b41
    //     0xc050e4: ldr             x0, [x0, #0xb08]
    // 0xc050e8: LeaveFrame
    //     0xc050e8: mov             SP, fp
    //     0xc050ec: ldp             fp, lr, [SP], #0x10
    // 0xc050f0: ret
    //     0xc050f0: ret             
    // 0xc050f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc050f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc050f8: b               #0xc050cc
  }
}

// class id: 3224, size: 0x34, field offset: 0x18
class _ProductMediaCarouselState extends __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin {

  [closure] bool <anonymous closure>(dynamic, WidgetEntity) {
    // ** addr: 0x802880, size: 0x58
    // 0x802880: EnterFrame
    //     0x802880: stp             fp, lr, [SP, #-0x10]!
    //     0x802884: mov             fp, SP
    // 0x802888: AllocStack(0x10)
    //     0x802888: sub             SP, SP, #0x10
    // 0x80288c: CheckStackOverflow
    //     0x80288c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802890: cmp             SP, x16
    //     0x802894: b.ls            #0x8028d0
    // 0x802898: ldr             x0, [fp, #0x10]
    // 0x80289c: LoadField: r1 = r0->field_1f
    //     0x80289c: ldur            w1, [x0, #0x1f]
    // 0x8028a0: DecompressPointer r1
    //     0x8028a0: add             x1, x1, HEAP, lsl #32
    // 0x8028a4: r0 = LoadClassIdInstr(r1)
    //     0x8028a4: ldur            x0, [x1, #-1]
    //     0x8028a8: ubfx            x0, x0, #0xc, #0x14
    // 0x8028ac: r16 = "video"
    //     0x8028ac: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x8028b0: ldr             x16, [x16, #0xb50]
    // 0x8028b4: stp             x16, x1, [SP]
    // 0x8028b8: mov             lr, x0
    // 0x8028bc: ldr             lr, [x21, lr, lsl #3]
    // 0x8028c0: blr             lr
    // 0x8028c4: LeaveFrame
    //     0x8028c4: mov             SP, fp
    //     0x8028c8: ldp             fp, lr, [SP], #0x10
    // 0x8028cc: ret
    //     0x8028cc: ret             
    // 0x8028d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8028d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8028d4: b               #0x802898
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8028d8, size: 0x348
    // 0x8028d8: EnterFrame
    //     0x8028d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8028dc: mov             fp, SP
    // 0x8028e0: AllocStack(0x50)
    //     0x8028e0: sub             SP, SP, #0x50
    // 0x8028e4: SetupParameters()
    //     0x8028e4: ldr             x0, [fp, #0x10]
    //     0x8028e8: ldur            w3, [x0, #0x17]
    //     0x8028ec: add             x3, x3, HEAP, lsl #32
    //     0x8028f0: stur            x3, [fp, #-0x10]
    // 0x8028f4: CheckStackOverflow
    //     0x8028f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8028f8: cmp             SP, x16
    //     0x8028fc: b.ls            #0x802c00
    // 0x802900: LoadField: r0 = r3->field_f
    //     0x802900: ldur            w0, [x3, #0xf]
    // 0x802904: DecompressPointer r0
    //     0x802904: add             x0, x0, HEAP, lsl #32
    // 0x802908: stur            x0, [fp, #-8]
    // 0x80290c: LoadField: r1 = r0->field_b
    //     0x80290c: ldur            w1, [x0, #0xb]
    // 0x802910: DecompressPointer r1
    //     0x802910: add             x1, x1, HEAP, lsl #32
    // 0x802914: cmp             w1, NULL
    // 0x802918: b.eq            #0x802c08
    // 0x80291c: LoadField: r2 = r1->field_b
    //     0x80291c: ldur            w2, [x1, #0xb]
    // 0x802920: DecompressPointer r2
    //     0x802920: add             x2, x2, HEAP, lsl #32
    // 0x802924: cmp             w2, NULL
    // 0x802928: b.eq            #0x802c0c
    // 0x80292c: r1 = <WidgetEntity>
    //     0x80292c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x802930: ldr             x1, [x1, #0x878]
    // 0x802934: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x802934: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x802938: r0 = List.from()
    //     0x802938: bl              #0x6cdc40  ; [dart:core] List::List.from
    // 0x80293c: ldur            x1, [fp, #-8]
    // 0x802940: StoreField: r1->field_23 = r0
    //     0x802940: stur            w0, [x1, #0x23]
    //     0x802944: ldurb           w16, [x1, #-1]
    //     0x802948: ldurb           w17, [x0, #-1]
    //     0x80294c: and             x16, x17, x16, lsr #2
    //     0x802950: tst             x16, HEAP, lsr #32
    //     0x802954: b.eq            #0x80295c
    //     0x802958: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x80295c: ldur            x0, [fp, #-0x10]
    // 0x802960: LoadField: r1 = r0->field_f
    //     0x802960: ldur            w1, [x0, #0xf]
    // 0x802964: DecompressPointer r1
    //     0x802964: add             x1, x1, HEAP, lsl #32
    // 0x802968: StoreField: r1->field_1b = rZR
    //     0x802968: stur            xzr, [x1, #0x1b]
    // 0x80296c: StoreField: r1->field_2f = rNULL
    //     0x80296c: stur            NULL, [x1, #0x2f]
    // 0x802970: LoadField: r3 = r1->field_23
    //     0x802970: ldur            w3, [x1, #0x23]
    // 0x802974: DecompressPointer r3
    //     0x802974: add             x3, x3, HEAP, lsl #32
    // 0x802978: stur            x3, [fp, #-8]
    // 0x80297c: r1 = Function '<anonymous closure>':.
    //     0x80297c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52ae0] AnonymousClosure: (0x802880), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x802c20)
    //     0x802980: ldr             x1, [x1, #0xae0]
    // 0x802984: r2 = Null
    //     0x802984: mov             x2, NULL
    // 0x802988: r0 = AllocateClosure()
    //     0x802988: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x80298c: ldur            x1, [fp, #-8]
    // 0x802990: mov             x2, x0
    // 0x802994: r0 = where()
    //     0x802994: bl              #0x7d9800  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x802998: mov             x1, x0
    // 0x80299c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x80299c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8029a0: r0 = toList()
    //     0x8029a0: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x8029a4: mov             x3, x0
    // 0x8029a8: stur            x3, [fp, #-0x38]
    // 0x8029ac: LoadField: r4 = r3->field_7
    //     0x8029ac: ldur            w4, [x3, #7]
    // 0x8029b0: DecompressPointer r4
    //     0x8029b0: add             x4, x4, HEAP, lsl #32
    // 0x8029b4: stur            x4, [fp, #-0x30]
    // 0x8029b8: LoadField: r0 = r3->field_b
    //     0x8029b8: ldur            w0, [x3, #0xb]
    // 0x8029bc: r5 = LoadInt32Instr(r0)
    //     0x8029bc: sbfx            x5, x0, #1, #0x1f
    // 0x8029c0: stur            x5, [fp, #-0x28]
    // 0x8029c4: r7 = 1
    //     0x8029c4: movz            x7, #0x1
    // 0x8029c8: r0 = 0
    //     0x8029c8: movz            x0, #0
    // 0x8029cc: ldur            x6, [fp, #-0x10]
    // 0x8029d0: stur            x7, [fp, #-0x20]
    // 0x8029d4: CheckStackOverflow
    //     0x8029d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8029d8: cmp             SP, x16
    //     0x8029dc: b.ls            #0x802c10
    // 0x8029e0: LoadField: r1 = r3->field_b
    //     0x8029e0: ldur            w1, [x3, #0xb]
    // 0x8029e4: r2 = LoadInt32Instr(r1)
    //     0x8029e4: sbfx            x2, x1, #1, #0x1f
    // 0x8029e8: cmp             x5, x2
    // 0x8029ec: b.ne            #0x802be0
    // 0x8029f0: cmp             x0, x2
    // 0x8029f4: b.ge            #0x802bd0
    // 0x8029f8: LoadField: r1 = r3->field_f
    //     0x8029f8: ldur            w1, [x3, #0xf]
    // 0x8029fc: DecompressPointer r1
    //     0x8029fc: add             x1, x1, HEAP, lsl #32
    // 0x802a00: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x802a00: add             x16, x1, x0, lsl #2
    //     0x802a04: ldur            w8, [x16, #0xf]
    // 0x802a08: DecompressPointer r8
    //     0x802a08: add             x8, x8, HEAP, lsl #32
    // 0x802a0c: stur            x8, [fp, #-8]
    // 0x802a10: add             x9, x0, #1
    // 0x802a14: stur            x9, [fp, #-0x18]
    // 0x802a18: cmp             w8, NULL
    // 0x802a1c: b.ne            #0x802a50
    // 0x802a20: mov             x0, x8
    // 0x802a24: mov             x2, x4
    // 0x802a28: r1 = Null
    //     0x802a28: mov             x1, NULL
    // 0x802a2c: cmp             w2, NULL
    // 0x802a30: b.eq            #0x802a50
    // 0x802a34: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x802a34: ldur            w4, [x2, #0x17]
    // 0x802a38: DecompressPointer r4
    //     0x802a38: add             x4, x4, HEAP, lsl #32
    // 0x802a3c: r8 = X0
    //     0x802a3c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x802a40: LoadField: r9 = r4->field_7
    //     0x802a40: ldur            x9, [x4, #7]
    // 0x802a44: r3 = Null
    //     0x802a44: add             x3, PP, #0x52, lsl #12  ; [pp+0x52ae8] Null
    //     0x802a48: ldr             x3, [x3, #0xae8]
    // 0x802a4c: blr             x9
    // 0x802a50: ldur            x0, [fp, #-0x10]
    // 0x802a54: LoadField: r1 = r0->field_f
    //     0x802a54: ldur            w1, [x0, #0xf]
    // 0x802a58: DecompressPointer r1
    //     0x802a58: add             x1, x1, HEAP, lsl #32
    // 0x802a5c: LoadField: r2 = r1->field_23
    //     0x802a5c: ldur            w2, [x1, #0x23]
    // 0x802a60: DecompressPointer r2
    //     0x802a60: add             x2, x2, HEAP, lsl #32
    // 0x802a64: LoadField: r1 = r2->field_b
    //     0x802a64: ldur            w1, [x2, #0xb]
    // 0x802a68: r3 = LoadInt32Instr(r1)
    //     0x802a68: sbfx            x3, x1, #1, #0x1f
    // 0x802a6c: LoadField: r1 = r2->field_f
    //     0x802a6c: ldur            w1, [x2, #0xf]
    // 0x802a70: DecompressPointer r1
    //     0x802a70: add             x1, x1, HEAP, lsl #32
    // 0x802a74: ldur            x4, [fp, #-8]
    // 0x802a78: r5 = 0
    //     0x802a78: movz            x5, #0
    // 0x802a7c: CheckStackOverflow
    //     0x802a7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802a80: cmp             SP, x16
    //     0x802a84: b.ls            #0x802c18
    // 0x802a88: cmp             x5, x3
    // 0x802a8c: b.ge            #0x802abc
    // 0x802a90: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x802a90: add             x16, x1, x5, lsl #2
    //     0x802a94: ldur            w6, [x16, #0xf]
    // 0x802a98: DecompressPointer r6
    //     0x802a98: add             x6, x6, HEAP, lsl #32
    // 0x802a9c: cmp             w6, w4
    // 0x802aa0: b.eq            #0x802ab0
    // 0x802aa4: add             x6, x5, #1
    // 0x802aa8: mov             x5, x6
    // 0x802aac: b               #0x802a7c
    // 0x802ab0: mov             x1, x2
    // 0x802ab4: mov             x2, x5
    // 0x802ab8: r0 = removeAt()
    //     0x802ab8: bl              #0x7145c0  ; [dart:core] _GrowableList::removeAt
    // 0x802abc: ldur            x0, [fp, #-0x10]
    // 0x802ac0: ldur            x4, [fp, #-0x20]
    // 0x802ac4: LoadField: r1 = r0->field_f
    //     0x802ac4: ldur            w1, [x0, #0xf]
    // 0x802ac8: DecompressPointer r1
    //     0x802ac8: add             x1, x1, HEAP, lsl #32
    // 0x802acc: LoadField: r3 = r1->field_23
    //     0x802acc: ldur            w3, [x1, #0x23]
    // 0x802ad0: DecompressPointer r3
    //     0x802ad0: add             x3, x3, HEAP, lsl #32
    // 0x802ad4: stur            x3, [fp, #-0x48]
    // 0x802ad8: LoadField: r5 = r3->field_b
    //     0x802ad8: ldur            w5, [x3, #0xb]
    // 0x802adc: stur            x5, [fp, #-0x40]
    // 0x802ae0: r1 = LoadInt32Instr(r5)
    //     0x802ae0: sbfx            x1, x5, #1, #0x1f
    // 0x802ae4: cmp             x4, x1
    // 0x802ae8: b.ge            #0x802b0c
    // 0x802aec: mov             x1, x3
    // 0x802af0: mov             x2, x4
    // 0x802af4: ldur            x3, [fp, #-8]
    // 0x802af8: r0 = insert()
    //     0x802af8: bl              #0x697f94  ; [dart:core] _GrowableList::insert
    // 0x802afc: ldur            x4, [fp, #-0x20]
    // 0x802b00: add             x1, x4, #2
    // 0x802b04: mov             x7, x1
    // 0x802b08: b               #0x802bbc
    // 0x802b0c: LoadField: r2 = r3->field_7
    //     0x802b0c: ldur            w2, [x3, #7]
    // 0x802b10: DecompressPointer r2
    //     0x802b10: add             x2, x2, HEAP, lsl #32
    // 0x802b14: ldur            x0, [fp, #-8]
    // 0x802b18: r1 = Null
    //     0x802b18: mov             x1, NULL
    // 0x802b1c: cmp             w2, NULL
    // 0x802b20: b.eq            #0x802b40
    // 0x802b24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x802b24: ldur            w4, [x2, #0x17]
    // 0x802b28: DecompressPointer r4
    //     0x802b28: add             x4, x4, HEAP, lsl #32
    // 0x802b2c: r8 = X0
    //     0x802b2c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x802b30: LoadField: r9 = r4->field_7
    //     0x802b30: ldur            x9, [x4, #7]
    // 0x802b34: r3 = Null
    //     0x802b34: add             x3, PP, #0x52, lsl #12  ; [pp+0x52af8] Null
    //     0x802b38: ldr             x3, [x3, #0xaf8]
    // 0x802b3c: blr             x9
    // 0x802b40: ldur            x0, [fp, #-0x48]
    // 0x802b44: LoadField: r1 = r0->field_f
    //     0x802b44: ldur            w1, [x0, #0xf]
    // 0x802b48: DecompressPointer r1
    //     0x802b48: add             x1, x1, HEAP, lsl #32
    // 0x802b4c: LoadField: r2 = r1->field_b
    //     0x802b4c: ldur            w2, [x1, #0xb]
    // 0x802b50: ldur            x1, [fp, #-0x40]
    // 0x802b54: r3 = LoadInt32Instr(r1)
    //     0x802b54: sbfx            x3, x1, #1, #0x1f
    // 0x802b58: stur            x3, [fp, #-0x50]
    // 0x802b5c: r1 = LoadInt32Instr(r2)
    //     0x802b5c: sbfx            x1, x2, #1, #0x1f
    // 0x802b60: cmp             x3, x1
    // 0x802b64: b.ne            #0x802b70
    // 0x802b68: mov             x1, x0
    // 0x802b6c: r0 = _growToNextCapacity()
    //     0x802b6c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x802b70: ldur            x0, [fp, #-0x48]
    // 0x802b74: ldur            x2, [fp, #-0x50]
    // 0x802b78: add             x1, x2, #1
    // 0x802b7c: lsl             x3, x1, #1
    // 0x802b80: StoreField: r0->field_b = r3
    //     0x802b80: stur            w3, [x0, #0xb]
    // 0x802b84: LoadField: r1 = r0->field_f
    //     0x802b84: ldur            w1, [x0, #0xf]
    // 0x802b88: DecompressPointer r1
    //     0x802b88: add             x1, x1, HEAP, lsl #32
    // 0x802b8c: ldur            x0, [fp, #-8]
    // 0x802b90: ArrayStore: r1[r2] = r0  ; List_4
    //     0x802b90: add             x25, x1, x2, lsl #2
    //     0x802b94: add             x25, x25, #0xf
    //     0x802b98: str             w0, [x25]
    //     0x802b9c: tbz             w0, #0, #0x802bb8
    //     0x802ba0: ldurb           w16, [x1, #-1]
    //     0x802ba4: ldurb           w17, [x0, #-1]
    //     0x802ba8: and             x16, x17, x16, lsr #2
    //     0x802bac: tst             x16, HEAP, lsr #32
    //     0x802bb0: b.eq            #0x802bb8
    //     0x802bb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x802bb8: ldur            x7, [fp, #-0x20]
    // 0x802bbc: ldur            x0, [fp, #-0x18]
    // 0x802bc0: ldur            x3, [fp, #-0x38]
    // 0x802bc4: ldur            x4, [fp, #-0x30]
    // 0x802bc8: ldur            x5, [fp, #-0x28]
    // 0x802bcc: b               #0x8029cc
    // 0x802bd0: r0 = Null
    //     0x802bd0: mov             x0, NULL
    // 0x802bd4: LeaveFrame
    //     0x802bd4: mov             SP, fp
    //     0x802bd8: ldp             fp, lr, [SP], #0x10
    // 0x802bdc: ret
    //     0x802bdc: ret             
    // 0x802be0: mov             x0, x3
    // 0x802be4: r0 = ConcurrentModificationError()
    //     0x802be4: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x802be8: mov             x1, x0
    // 0x802bec: ldur            x0, [fp, #-0x38]
    // 0x802bf0: StoreField: r1->field_b = r0
    //     0x802bf0: stur            w0, [x1, #0xb]
    // 0x802bf4: mov             x0, x1
    // 0x802bf8: r0 = Throw()
    //     0x802bf8: bl              #0x16f5420  ; ThrowStub
    // 0x802bfc: brk             #0
    // 0x802c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802c00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802c04: b               #0x802900
    // 0x802c08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x802c08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x802c0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x802c0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x802c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802c10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802c14: b               #0x8029e0
    // 0x802c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802c18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802c1c: b               #0x802a88
  }
  _ _initializeMediaItems(/* No info */) {
    // ** addr: 0x802c20, size: 0xd4
    // 0x802c20: EnterFrame
    //     0x802c20: stp             fp, lr, [SP, #-0x10]!
    //     0x802c24: mov             fp, SP
    // 0x802c28: AllocStack(0x8)
    //     0x802c28: sub             SP, SP, #8
    // 0x802c2c: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x802c2c: stur            x1, [fp, #-8]
    // 0x802c30: CheckStackOverflow
    //     0x802c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x802c34: cmp             SP, x16
    //     0x802c38: b.ls            #0x802ce8
    // 0x802c3c: r1 = 1
    //     0x802c3c: movz            x1, #0x1
    // 0x802c40: r0 = AllocateContext()
    //     0x802c40: bl              #0x16f6108  ; AllocateContextStub
    // 0x802c44: mov             x1, x0
    // 0x802c48: ldur            x0, [fp, #-8]
    // 0x802c4c: StoreField: r1->field_f = r0
    //     0x802c4c: stur            w0, [x1, #0xf]
    // 0x802c50: LoadField: r2 = r0->field_b
    //     0x802c50: ldur            w2, [x0, #0xb]
    // 0x802c54: DecompressPointer r2
    //     0x802c54: add             x2, x2, HEAP, lsl #32
    // 0x802c58: cmp             w2, NULL
    // 0x802c5c: b.eq            #0x802cf0
    // 0x802c60: LoadField: r3 = r2->field_b
    //     0x802c60: ldur            w3, [x2, #0xb]
    // 0x802c64: DecompressPointer r3
    //     0x802c64: add             x3, x3, HEAP, lsl #32
    // 0x802c68: cmp             w3, NULL
    // 0x802c6c: b.eq            #0x802c78
    // 0x802c70: LoadField: r2 = r3->field_b
    //     0x802c70: ldur            w2, [x3, #0xb]
    // 0x802c74: cbnz            w2, #0x802cb8
    // 0x802c78: r1 = <WidgetEntity>
    //     0x802c78: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0x802c7c: ldr             x1, [x1, #0x878]
    // 0x802c80: r2 = 0
    //     0x802c80: movz            x2, #0
    // 0x802c84: r0 = _GrowableList()
    //     0x802c84: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x802c88: ldur            x3, [fp, #-8]
    // 0x802c8c: StoreField: r3->field_23 = r0
    //     0x802c8c: stur            w0, [x3, #0x23]
    //     0x802c90: ldurb           w16, [x3, #-1]
    //     0x802c94: ldurb           w17, [x0, #-1]
    //     0x802c98: and             x16, x17, x16, lsr #2
    //     0x802c9c: tst             x16, HEAP, lsr #32
    //     0x802ca0: b.eq            #0x802ca8
    //     0x802ca4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x802ca8: r0 = Null
    //     0x802ca8: mov             x0, NULL
    // 0x802cac: LeaveFrame
    //     0x802cac: mov             SP, fp
    //     0x802cb0: ldp             fp, lr, [SP], #0x10
    // 0x802cb4: ret
    //     0x802cb4: ret             
    // 0x802cb8: mov             x3, x0
    // 0x802cbc: mov             x2, x1
    // 0x802cc0: r1 = Function '<anonymous closure>':.
    //     0x802cc0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52ad8] AnonymousClosure: (0x8028d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems (0x802c20)
    //     0x802cc4: ldr             x1, [x1, #0xad8]
    // 0x802cc8: r0 = AllocateClosure()
    //     0x802cc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x802ccc: ldur            x1, [fp, #-8]
    // 0x802cd0: mov             x2, x0
    // 0x802cd4: r0 = setState()
    //     0x802cd4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x802cd8: r0 = Null
    //     0x802cd8: mov             x0, NULL
    // 0x802cdc: LeaveFrame
    //     0x802cdc: mov             SP, fp
    //     0x802ce0: ldp             fp, lr, [SP], #0x10
    // 0x802ce4: ret
    //     0x802ce4: ret             
    // 0x802ce8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x802ce8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x802cec: b               #0x802c3c
    // 0x802cf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x802cf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x807470, size: 0x180
    // 0x807470: EnterFrame
    //     0x807470: stp             fp, lr, [SP, #-0x10]!
    //     0x807474: mov             fp, SP
    // 0x807478: AllocStack(0x20)
    //     0x807478: sub             SP, SP, #0x20
    // 0x80747c: SetupParameters(_ProductMediaCarouselState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x80747c: mov             x4, x1
    //     0x807480: mov             x3, x2
    //     0x807484: stur            x1, [fp, #-8]
    //     0x807488: stur            x2, [fp, #-0x10]
    // 0x80748c: CheckStackOverflow
    //     0x80748c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807490: cmp             SP, x16
    //     0x807494: b.ls            #0x8075e4
    // 0x807498: mov             x0, x3
    // 0x80749c: r2 = Null
    //     0x80749c: mov             x2, NULL
    // 0x8074a0: r1 = Null
    //     0x8074a0: mov             x1, NULL
    // 0x8074a4: r4 = 60
    //     0x8074a4: movz            x4, #0x3c
    // 0x8074a8: branchIfSmi(r0, 0x8074b4)
    //     0x8074a8: tbz             w0, #0, #0x8074b4
    // 0x8074ac: r4 = LoadClassIdInstr(r0)
    //     0x8074ac: ldur            x4, [x0, #-1]
    //     0x8074b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8074b4: cmp             x4, #0xf83
    // 0x8074b8: b.eq            #0x8074d0
    // 0x8074bc: r8 = ProductMediaCarousel
    //     0x8074bc: add             x8, PP, #0x52, lsl #12  ; [pp+0x52ab0] Type: ProductMediaCarousel
    //     0x8074c0: ldr             x8, [x8, #0xab0]
    // 0x8074c4: r3 = Null
    //     0x8074c4: add             x3, PP, #0x52, lsl #12  ; [pp+0x52ab8] Null
    //     0x8074c8: ldr             x3, [x3, #0xab8]
    // 0x8074cc: r0 = ProductMediaCarousel()
    //     0x8074cc: bl              #0x7f382c  ; IsType_ProductMediaCarousel_Stub
    // 0x8074d0: ldur            x3, [fp, #-8]
    // 0x8074d4: LoadField: r2 = r3->field_7
    //     0x8074d4: ldur            w2, [x3, #7]
    // 0x8074d8: DecompressPointer r2
    //     0x8074d8: add             x2, x2, HEAP, lsl #32
    // 0x8074dc: ldur            x0, [fp, #-0x10]
    // 0x8074e0: r1 = Null
    //     0x8074e0: mov             x1, NULL
    // 0x8074e4: cmp             w2, NULL
    // 0x8074e8: b.eq            #0x80750c
    // 0x8074ec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8074ec: ldur            w4, [x2, #0x17]
    // 0x8074f0: DecompressPointer r4
    //     0x8074f0: add             x4, x4, HEAP, lsl #32
    // 0x8074f4: r8 = X0 bound StatefulWidget
    //     0x8074f4: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x8074f8: ldr             x8, [x8, #0x7a0]
    // 0x8074fc: LoadField: r9 = r4->field_7
    //     0x8074fc: ldur            x9, [x4, #7]
    // 0x807500: r3 = Null
    //     0x807500: add             x3, PP, #0x52, lsl #12  ; [pp+0x52ac8] Null
    //     0x807504: ldr             x3, [x3, #0xac8]
    // 0x807508: blr             x9
    // 0x80750c: ldur            x0, [fp, #-0x10]
    // 0x807510: LoadField: r1 = r0->field_b
    //     0x807510: ldur            w1, [x0, #0xb]
    // 0x807514: DecompressPointer r1
    //     0x807514: add             x1, x1, HEAP, lsl #32
    // 0x807518: ldur            x2, [fp, #-8]
    // 0x80751c: LoadField: r3 = r2->field_b
    //     0x80751c: ldur            w3, [x2, #0xb]
    // 0x807520: DecompressPointer r3
    //     0x807520: add             x3, x3, HEAP, lsl #32
    // 0x807524: cmp             w3, NULL
    // 0x807528: b.eq            #0x8075ec
    // 0x80752c: LoadField: r4 = r3->field_b
    //     0x80752c: ldur            w4, [x3, #0xb]
    // 0x807530: DecompressPointer r4
    //     0x807530: add             x4, x4, HEAP, lsl #32
    // 0x807534: cmp             w1, w4
    // 0x807538: b.eq            #0x807544
    // 0x80753c: mov             x0, x2
    // 0x807540: b               #0x807578
    // 0x807544: LoadField: r1 = r0->field_f
    //     0x807544: ldur            w1, [x0, #0xf]
    // 0x807548: DecompressPointer r1
    //     0x807548: add             x1, x1, HEAP, lsl #32
    // 0x80754c: LoadField: r0 = r3->field_f
    //     0x80754c: ldur            w0, [x3, #0xf]
    // 0x807550: DecompressPointer r0
    //     0x807550: add             x0, x0, HEAP, lsl #32
    // 0x807554: r3 = LoadClassIdInstr(r1)
    //     0x807554: ldur            x3, [x1, #-1]
    //     0x807558: ubfx            x3, x3, #0xc, #0x14
    // 0x80755c: stp             x0, x1, [SP]
    // 0x807560: mov             x0, x3
    // 0x807564: mov             lr, x0
    // 0x807568: ldr             lr, [x21, lr, lsl #3]
    // 0x80756c: blr             lr
    // 0x807570: tbz             w0, #4, #0x8075d4
    // 0x807574: ldur            x0, [fp, #-8]
    // 0x807578: LoadField: r1 = r0->field_27
    //     0x807578: ldur            w1, [x0, #0x27]
    // 0x80757c: DecompressPointer r1
    //     0x80757c: add             x1, x1, HEAP, lsl #32
    // 0x807580: r0 = clear()
    //     0x807580: bl              #0x649b54  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x807584: ldur            x0, [fp, #-8]
    // 0x807588: StoreField: r0->field_2f = rNULL
    //     0x807588: stur            NULL, [x0, #0x2f]
    // 0x80758c: mov             x1, x0
    // 0x807590: r0 = _initializeMediaItems()
    //     0x807590: bl              #0x802c20  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x807594: ldur            x1, [fp, #-8]
    // 0x807598: r0 = _preloadImageSizes()
    //     0x807598: bl              #0x8075f0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x80759c: ldur            x0, [fp, #-8]
    // 0x8075a0: StoreField: r0->field_1b = rZR
    //     0x8075a0: stur            xzr, [x0, #0x1b]
    // 0x8075a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8075a4: ldur            w1, [x0, #0x17]
    // 0x8075a8: DecompressPointer r1
    //     0x8075a8: add             x1, x1, HEAP, lsl #32
    // 0x8075ac: LoadField: r0 = r1->field_3b
    //     0x8075ac: ldur            w0, [x1, #0x3b]
    // 0x8075b0: DecompressPointer r0
    //     0x8075b0: add             x0, x0, HEAP, lsl #32
    // 0x8075b4: LoadField: r2 = r0->field_b
    //     0x8075b4: ldur            w2, [x0, #0xb]
    // 0x8075b8: cbz             w2, #0x8075d4
    // 0x8075bc: r2 = 0
    //     0x8075bc: movz            x2, #0
    // 0x8075c0: r3 = Instance_Cubic
    //     0x8075c0: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x8075c4: ldr             x3, [x3, #0x2b0]
    // 0x8075c8: r5 = Instance_Duration
    //     0x8075c8: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x8075cc: ldr             x5, [x5, #0xf00]
    // 0x8075d0: r0 = animateToPage()
    //     0x8075d0: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0x8075d4: r0 = Null
    //     0x8075d4: mov             x0, NULL
    // 0x8075d8: LeaveFrame
    //     0x8075d8: mov             SP, fp
    //     0x8075dc: ldp             fp, lr, [SP], #0x10
    // 0x8075e0: ret
    //     0x8075e0: ret             
    // 0x8075e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8075e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8075e8: b               #0x807498
    // 0x8075ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8075ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _preloadImageSizes(/* No info */) async {
    // ** addr: 0x8075f0, size: 0x3f8
    // 0x8075f0: EnterFrame
    //     0x8075f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8075f4: mov             fp, SP
    // 0x8075f8: AllocStack(0xb8)
    //     0x8075f8: sub             SP, SP, #0xb8
    // 0x8075fc: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x80 */)
    //     0x8075fc: stur            NULL, [fp, #-8]
    //     0x807600: stur            x1, [fp, #-0x80]
    // 0x807604: CheckStackOverflow
    //     0x807604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807608: cmp             SP, x16
    //     0x80760c: b.ls            #0x8079c4
    // 0x807610: r1 = 1
    //     0x807610: movz            x1, #0x1
    // 0x807614: r0 = AllocateContext()
    //     0x807614: bl              #0x16f6108  ; AllocateContextStub
    // 0x807618: mov             x2, x0
    // 0x80761c: ldur            x1, [fp, #-0x80]
    // 0x807620: stur            x2, [fp, #-0x88]
    // 0x807624: StoreField: r2->field_f = r1
    //     0x807624: stur            w1, [x2, #0xf]
    // 0x807628: InitAsync() -> Future<void?>
    //     0x807628: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x80762c: bl              #0x6326e0  ; InitAsyncStub
    // 0x807630: ldur            x1, [fp, #-0x80]
    // 0x807634: LoadField: r0 = r1->field_2b
    //     0x807634: ldur            w0, [x1, #0x2b]
    // 0x807638: DecompressPointer r0
    //     0x807638: add             x0, x0, HEAP, lsl #32
    // 0x80763c: tbnz            w0, #4, #0x807648
    // 0x807640: r0 = Null
    //     0x807640: mov             x0, NULL
    // 0x807644: r0 = ReturnAsyncNotFuture()
    //     0x807644: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x807648: ldur            x0, [fp, #-0x88]
    // 0x80764c: r1 = 3
    //     0x80764c: movz            x1, #0x3
    // 0x807650: r0 = AllocateContext()
    //     0x807650: bl              #0x16f6108  ; AllocateContextStub
    // 0x807654: mov             x1, x0
    // 0x807658: ldur            x0, [fp, #-0x88]
    // 0x80765c: StoreField: r1->field_b = r0
    //     0x80765c: stur            w0, [x1, #0xb]
    // 0x807660: StoreField: r1->field_f = rZR
    //     0x807660: stur            wzr, [x1, #0xf]
    // 0x807664: ldur            x2, [fp, #-0x80]
    // 0x807668: LoadField: r3 = r2->field_27
    //     0x807668: ldur            w3, [x2, #0x27]
    // 0x80766c: DecompressPointer r3
    //     0x80766c: add             x3, x3, HEAP, lsl #32
    // 0x807670: stur            x3, [fp, #-0xa0]
    // 0x807674: mov             x6, x1
    // 0x807678: r5 = Null
    //     0x807678: mov             x5, NULL
    // 0x80767c: r4 = 0
    //     0x80767c: movz            x4, #0
    // 0x807680: stur            x6, [fp, #-0x90]
    // 0x807684: stur            x5, [fp, #-0x98]
    // 0x807688: CheckStackOverflow
    //     0x807688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80768c: cmp             SP, x16
    //     0x807690: b.ls            #0x8079cc
    // 0x807694: LoadField: r7 = r2->field_23
    //     0x807694: ldur            w7, [x2, #0x23]
    // 0x807698: DecompressPointer r7
    //     0x807698: add             x7, x7, HEAP, lsl #32
    // 0x80769c: LoadField: r0 = r7->field_b
    //     0x80769c: ldur            w0, [x7, #0xb]
    // 0x8076a0: r1 = LoadInt32Instr(r0)
    //     0x8076a0: sbfx            x1, x0, #1, #0x1f
    // 0x8076a4: cmp             x4, x1
    // 0x8076a8: b.ge            #0x8079bc
    // 0x8076ac: mov             x0, x1
    // 0x8076b0: mov             x1, x4
    // 0x8076b4: cmp             x1, x0
    // 0x8076b8: b.hs            #0x8079d4
    // 0x8076bc: LoadField: r0 = r7->field_f
    //     0x8076bc: ldur            w0, [x7, #0xf]
    // 0x8076c0: DecompressPointer r0
    //     0x8076c0: add             x0, x0, HEAP, lsl #32
    // 0x8076c4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x8076c4: add             x16, x0, x4, lsl #2
    //     0x8076c8: ldur            w1, [x16, #0xf]
    // 0x8076cc: DecompressPointer r1
    //     0x8076cc: add             x1, x1, HEAP, lsl #32
    // 0x8076d0: stur            x1, [fp, #-0x88]
    // 0x8076d4: LoadField: r0 = r1->field_1f
    //     0x8076d4: ldur            w0, [x1, #0x1f]
    // 0x8076d8: DecompressPointer r0
    //     0x8076d8: add             x0, x0, HEAP, lsl #32
    // 0x8076dc: r4 = LoadClassIdInstr(r0)
    //     0x8076dc: ldur            x4, [x0, #-1]
    //     0x8076e0: ubfx            x4, x4, #0xc, #0x14
    // 0x8076e4: r16 = "video"
    //     0x8076e4: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x8076e8: ldr             x16, [x16, #0xb50]
    // 0x8076ec: stp             x16, x0, [SP]
    // 0x8076f0: mov             x0, x4
    // 0x8076f4: mov             lr, x0
    // 0x8076f8: ldr             lr, [x21, lr, lsl #3]
    // 0x8076fc: blr             lr
    // 0x807700: tbz             w0, #4, #0x807944
    // 0x807704: ldur            x0, [fp, #-0x88]
    // 0x807708: LoadField: r1 = r0->field_2b
    //     0x807708: ldur            w1, [x0, #0x2b]
    // 0x80770c: DecompressPointer r1
    //     0x80770c: add             x1, x1, HEAP, lsl #32
    // 0x807710: cmp             w1, NULL
    // 0x807714: b.ne            #0x807720
    // 0x807718: r0 = Null
    //     0x807718: mov             x0, NULL
    // 0x80771c: b               #0x80774c
    // 0x807720: LoadField: r0 = r1->field_b
    //     0x807720: ldur            w0, [x1, #0xb]
    // 0x807724: DecompressPointer r0
    //     0x807724: add             x0, x0, HEAP, lsl #32
    // 0x807728: cmp             w0, NULL
    // 0x80772c: b.ne            #0x807738
    // 0x807730: r0 = Null
    //     0x807730: mov             x0, NULL
    // 0x807734: b               #0x80774c
    // 0x807738: LoadField: r2 = r0->field_7
    //     0x807738: ldur            w2, [x0, #7]
    // 0x80773c: cbnz            w2, #0x807748
    // 0x807740: r0 = false
    //     0x807740: add             x0, NULL, #0x30  ; false
    // 0x807744: b               #0x80774c
    // 0x807748: r0 = true
    //     0x807748: add             x0, NULL, #0x20  ; true
    // 0x80774c: cmp             w0, NULL
    // 0x807750: b.eq            #0x807944
    // 0x807754: tbnz            w0, #4, #0x807944
    // 0x807758: ldur            x4, [fp, #-0x90]
    // 0x80775c: ldur            x3, [fp, #-0xa0]
    // 0x807760: cmp             w1, NULL
    // 0x807764: b.eq            #0x8079d8
    // 0x807768: LoadField: r5 = r1->field_b
    //     0x807768: ldur            w5, [x1, #0xb]
    // 0x80776c: DecompressPointer r5
    //     0x80776c: add             x5, x5, HEAP, lsl #32
    // 0x807770: stur            x5, [fp, #-0xa8]
    // 0x807774: cmp             w5, NULL
    // 0x807778: b.eq            #0x8079dc
    // 0x80777c: mov             x0, x5
    // 0x807780: StoreField: r4->field_13 = r0
    //     0x807780: stur            w0, [x4, #0x13]
    //     0x807784: ldurb           w16, [x4, #-1]
    //     0x807788: ldurb           w17, [x0, #-1]
    //     0x80778c: and             x16, x17, x16, lsr #2
    //     0x807790: tst             x16, HEAP, lsr #32
    //     0x807794: b.eq            #0x80779c
    //     0x807798: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x80779c: LoadField: r0 = r3->field_f
    //     0x80779c: ldur            w0, [x3, #0xf]
    // 0x8077a0: DecompressPointer r0
    //     0x8077a0: add             x0, x0, HEAP, lsl #32
    // 0x8077a4: mov             x1, x3
    // 0x8077a8: mov             x2, x5
    // 0x8077ac: stur            x0, [fp, #-0x88]
    // 0x8077b0: r0 = _getValueOrData()
    //     0x8077b0: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x8077b4: mov             x1, x0
    // 0x8077b8: ldur            x0, [fp, #-0x88]
    // 0x8077bc: cmp             w0, w1
    // 0x8077c0: b.ne            #0x8078d4
    // 0x8077c4: ldur            x0, [fp, #-0x80]
    // 0x8077c8: ldur            x2, [fp, #-0x90]
    // 0x8077cc: ldur            x1, [fp, #-0xa8]
    // 0x8077d0: r0 = ImageSizeExtension.getImageSize()
    //     0x8077d0: bl              #0x801f84  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageSizeExtension.getImageSize
    // 0x8077d4: mov             x1, x0
    // 0x8077d8: stur            x1, [fp, #-0x88]
    // 0x8077dc: r0 = Await()
    //     0x8077dc: bl              #0x63248c  ; AwaitStub
    // 0x8077e0: ldur            x3, [fp, #-0x90]
    // 0x8077e4: ArrayStore: r3[0] = r0  ; List_4
    //     0x8077e4: stur            w0, [x3, #0x17]
    //     0x8077e8: tbz             w0, #0, #0x807804
    //     0x8077ec: ldurb           w16, [x3, #-1]
    //     0x8077f0: ldurb           w17, [x0, #-1]
    //     0x8077f4: and             x16, x17, x16, lsr #2
    //     0x8077f8: tst             x16, HEAP, lsr #32
    //     0x8077fc: b.eq            #0x807804
    //     0x807800: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x807804: ldur            x0, [fp, #-0x80]
    // 0x807808: LoadField: r1 = r0->field_2b
    //     0x807808: ldur            w1, [x0, #0x2b]
    // 0x80780c: DecompressPointer r1
    //     0x80780c: add             x1, x1, HEAP, lsl #32
    // 0x807810: tbz             w1, #4, #0x807868
    // 0x807814: LoadField: r1 = r0->field_f
    //     0x807814: ldur            w1, [x0, #0xf]
    // 0x807818: DecompressPointer r1
    //     0x807818: add             x1, x1, HEAP, lsl #32
    // 0x80781c: cmp             w1, NULL
    // 0x807820: b.eq            #0x807868
    // 0x807824: mov             x2, x3
    // 0x807828: r1 = Function '<anonymous closure>':.
    //     0x807828: add             x1, PP, #0x52, lsl #12  ; [pp+0x529a8] AnonymousClosure: (0x8079e8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes (0x8075f0)
    //     0x80782c: ldr             x1, [x1, #0x9a8]
    // 0x807830: r0 = AllocateClosure()
    //     0x807830: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x807834: mov             x1, x0
    // 0x807838: stur            x1, [fp, #-0x88]
    // 0x80783c: str             x1, [SP]
    // 0x807840: mov             x0, x1
    // 0x807844: ClosureCall
    //     0x807844: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x807848: ldur            x2, [x0, #0x1f]
    //     0x80784c: blr             x2
    // 0x807850: ldur            x0, [fp, #-0x80]
    // 0x807854: LoadField: r1 = r0->field_f
    //     0x807854: ldur            w1, [x0, #0xf]
    // 0x807858: DecompressPointer r1
    //     0x807858: add             x1, x1, HEAP, lsl #32
    // 0x80785c: cmp             w1, NULL
    // 0x807860: b.eq            #0x8079e0
    // 0x807864: r0 = markNeedsBuild()
    //     0x807864: bl              #0x78858c  ; [package:flutter/src/widgets/framework.dart] Element::markNeedsBuild
    // 0x807868: ldur            x0, [fp, #-0x98]
    // 0x80786c: b               #0x807948
    // 0x807870: sub             SP, fp, #0xb8
    // 0x807874: stur            x0, [fp, #-0x88]
    // 0x807878: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x807878: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x80787c: ldr             x0, [x0, #0xcf0]
    //     0x807880: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x807884: cmp             w0, w16
    //     0x807888: b.ne            #0x807894
    //     0x80788c: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x807890: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x807894: r1 = Null
    //     0x807894: mov             x1, NULL
    // 0x807898: r2 = 4
    //     0x807898: movz            x2, #0x4
    // 0x80789c: r0 = AllocateArray()
    //     0x80789c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8078a0: r16 = "Error loading image size: "
    //     0x8078a0: add             x16, PP, #0x52, lsl #12  ; [pp+0x529b0] "Error loading image size: "
    //     0x8078a4: ldr             x16, [x16, #0x9b0]
    // 0x8078a8: StoreField: r0->field_f = r16
    //     0x8078a8: stur            w16, [x0, #0xf]
    // 0x8078ac: ldur            x1, [fp, #-0x88]
    // 0x8078b0: StoreField: r0->field_13 = r1
    //     0x8078b0: stur            w1, [x0, #0x13]
    // 0x8078b4: str             x0, [SP]
    // 0x8078b8: r0 = _interpolate()
    //     0x8078b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8078bc: str             NULL, [SP]
    // 0x8078c0: mov             x1, x0
    // 0x8078c4: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x8078c4: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x8078c8: r0 = debugPrintThrottled()
    //     0x8078c8: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x8078cc: ldur            x0, [fp, #-0x88]
    // 0x8078d0: b               #0x807948
    // 0x8078d4: ldur            x5, [fp, #-0x90]
    // 0x8078d8: LoadField: r0 = r5->field_f
    //     0x8078d8: ldur            w0, [x5, #0xf]
    // 0x8078dc: DecompressPointer r0
    //     0x8078dc: add             x0, x0, HEAP, lsl #32
    // 0x8078e0: cbnz            w0, #0x80793c
    // 0x8078e4: ldur            x0, [fp, #-0x80]
    // 0x8078e8: LoadField: r1 = r0->field_2f
    //     0x8078e8: ldur            w1, [x0, #0x2f]
    // 0x8078ec: DecompressPointer r1
    //     0x8078ec: add             x1, x1, HEAP, lsl #32
    // 0x8078f0: cmp             w1, NULL
    // 0x8078f4: b.ne            #0x80793c
    // 0x8078f8: ldur            x3, [fp, #-0xa0]
    // 0x8078fc: mov             x1, x3
    // 0x807900: ldur            x2, [fp, #-0xa8]
    // 0x807904: r0 = _getValueOrData()
    //     0x807904: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x807908: mov             x1, x0
    // 0x80790c: ldur            x0, [fp, #-0xa0]
    // 0x807910: LoadField: r2 = r0->field_f
    //     0x807910: ldur            w2, [x0, #0xf]
    // 0x807914: DecompressPointer r2
    //     0x807914: add             x2, x2, HEAP, lsl #32
    // 0x807918: cmp             w2, w1
    // 0x80791c: b.ne            #0x807928
    // 0x807920: r2 = Null
    //     0x807920: mov             x2, NULL
    // 0x807924: b               #0x80792c
    // 0x807928: mov             x2, x1
    // 0x80792c: cmp             w2, NULL
    // 0x807930: b.eq            #0x8079e4
    // 0x807934: ldur            x1, [fp, #-0x80]
    // 0x807938: r0 = _calculateCarouselHeight()
    //     0x807938: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x80793c: ldur            x0, [fp, #-0x98]
    // 0x807940: b               #0x807948
    // 0x807944: ldur            x0, [fp, #-0x98]
    // 0x807948: ldur            x5, [fp, #-0x90]
    // 0x80794c: stur            x0, [fp, #-0x88]
    // 0x807950: r0 = CloneContext()
    //     0x807950: bl              #0x16f5ae8  ; CloneContextStub
    // 0x807954: mov             x2, x0
    // 0x807958: LoadField: r3 = r2->field_f
    //     0x807958: ldur            w3, [x2, #0xf]
    // 0x80795c: DecompressPointer r3
    //     0x80795c: add             x3, x3, HEAP, lsl #32
    // 0x807960: r4 = LoadInt32Instr(r3)
    //     0x807960: sbfx            x4, x3, #1, #0x1f
    //     0x807964: tbz             w3, #0, #0x80796c
    //     0x807968: ldur            x4, [x3, #7]
    // 0x80796c: add             x3, x4, #1
    // 0x807970: r0 = BoxInt64Instr(r3)
    //     0x807970: sbfiz           x0, x3, #1, #0x1f
    //     0x807974: cmp             x3, x0, asr #1
    //     0x807978: b.eq            #0x807984
    //     0x80797c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x807980: stur            x3, [x0, #7]
    // 0x807984: StoreField: r2->field_f = r0
    //     0x807984: stur            w0, [x2, #0xf]
    //     0x807988: tbz             w0, #0, #0x8079a4
    //     0x80798c: ldurb           w16, [x2, #-1]
    //     0x807990: ldurb           w17, [x0, #-1]
    //     0x807994: and             x16, x17, x16, lsr #2
    //     0x807998: tst             x16, HEAP, lsr #32
    //     0x80799c: b.eq            #0x8079a4
    //     0x8079a0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x8079a4: mov             x6, x2
    // 0x8079a8: ldur            x5, [fp, #-0x88]
    // 0x8079ac: mov             x4, x3
    // 0x8079b0: ldur            x2, [fp, #-0x80]
    // 0x8079b4: ldur            x3, [fp, #-0xa0]
    // 0x8079b8: b               #0x807680
    // 0x8079bc: r0 = Null
    //     0x8079bc: mov             x0, NULL
    // 0x8079c0: r0 = ReturnAsyncNotFuture()
    //     0x8079c0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x8079c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8079c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8079c8: b               #0x807610
    // 0x8079cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8079cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8079d0: b               #0x807694
    // 0x8079d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8079d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8079d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8079d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8079dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8079dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8079e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8079e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8079e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8079e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8079e8, size: 0xb8
    // 0x8079e8: EnterFrame
    //     0x8079e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8079ec: mov             fp, SP
    // 0x8079f0: AllocStack(0x18)
    //     0x8079f0: sub             SP, SP, #0x18
    // 0x8079f4: SetupParameters()
    //     0x8079f4: ldr             x0, [fp, #0x10]
    //     0x8079f8: ldur            w4, [x0, #0x17]
    //     0x8079fc: add             x4, x4, HEAP, lsl #32
    //     0x807a00: stur            x4, [fp, #-0x18]
    // 0x807a04: CheckStackOverflow
    //     0x807a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x807a08: cmp             SP, x16
    //     0x807a0c: b.ls            #0x807a98
    // 0x807a10: LoadField: r0 = r4->field_b
    //     0x807a10: ldur            w0, [x4, #0xb]
    // 0x807a14: DecompressPointer r0
    //     0x807a14: add             x0, x0, HEAP, lsl #32
    // 0x807a18: stur            x0, [fp, #-0x10]
    // 0x807a1c: LoadField: r1 = r0->field_f
    //     0x807a1c: ldur            w1, [x0, #0xf]
    // 0x807a20: DecompressPointer r1
    //     0x807a20: add             x1, x1, HEAP, lsl #32
    // 0x807a24: LoadField: r2 = r1->field_27
    //     0x807a24: ldur            w2, [x1, #0x27]
    // 0x807a28: DecompressPointer r2
    //     0x807a28: add             x2, x2, HEAP, lsl #32
    // 0x807a2c: LoadField: r1 = r4->field_13
    //     0x807a2c: ldur            w1, [x4, #0x13]
    // 0x807a30: DecompressPointer r1
    //     0x807a30: add             x1, x1, HEAP, lsl #32
    // 0x807a34: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x807a34: ldur            w5, [x4, #0x17]
    // 0x807a38: DecompressPointer r5
    //     0x807a38: add             x5, x5, HEAP, lsl #32
    // 0x807a3c: mov             x16, x1
    // 0x807a40: mov             x1, x2
    // 0x807a44: mov             x2, x16
    // 0x807a48: mov             x3, x5
    // 0x807a4c: stur            x5, [fp, #-8]
    // 0x807a50: r0 = []=()
    //     0x807a50: bl              #0x1699264  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x807a54: ldur            x0, [fp, #-0x18]
    // 0x807a58: LoadField: r1 = r0->field_f
    //     0x807a58: ldur            w1, [x0, #0xf]
    // 0x807a5c: DecompressPointer r1
    //     0x807a5c: add             x1, x1, HEAP, lsl #32
    // 0x807a60: cbnz            w1, #0x807a88
    // 0x807a64: ldur            x0, [fp, #-0x10]
    // 0x807a68: LoadField: r1 = r0->field_f
    //     0x807a68: ldur            w1, [x0, #0xf]
    // 0x807a6c: DecompressPointer r1
    //     0x807a6c: add             x1, x1, HEAP, lsl #32
    // 0x807a70: LoadField: r0 = r1->field_2f
    //     0x807a70: ldur            w0, [x1, #0x2f]
    // 0x807a74: DecompressPointer r0
    //     0x807a74: add             x0, x0, HEAP, lsl #32
    // 0x807a78: cmp             w0, NULL
    // 0x807a7c: b.ne            #0x807a88
    // 0x807a80: ldur            x2, [fp, #-8]
    // 0x807a84: r0 = _calculateCarouselHeight()
    //     0x807a84: bl              #0x801e60  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_calculateCarouselHeight
    // 0x807a88: r0 = Null
    //     0x807a88: mov             x0, NULL
    // 0x807a8c: LeaveFrame
    //     0x807a8c: mov             SP, fp
    //     0x807a90: ldp             fp, lr, [SP], #0x10
    // 0x807a94: ret
    //     0x807a94: ret             
    // 0x807a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x807a98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x807a9c: b               #0x807a10
  }
  _ initState(/* No info */) {
    // ** addr: 0x94a998, size: 0x50
    // 0x94a998: EnterFrame
    //     0x94a998: stp             fp, lr, [SP, #-0x10]!
    //     0x94a99c: mov             fp, SP
    // 0x94a9a0: AllocStack(0x8)
    //     0x94a9a0: sub             SP, SP, #8
    // 0x94a9a4: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x94a9a4: mov             x0, x1
    //     0x94a9a8: stur            x1, [fp, #-8]
    // 0x94a9ac: CheckStackOverflow
    //     0x94a9ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a9b0: cmp             SP, x16
    //     0x94a9b4: b.ls            #0x94a9e0
    // 0x94a9b8: mov             x1, x0
    // 0x94a9bc: r0 = initState()
    //     0x94a9bc: bl              #0x94a9e8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::initState
    // 0x94a9c0: ldur            x1, [fp, #-8]
    // 0x94a9c4: r0 = _initializeMediaItems()
    //     0x94a9c4: bl              #0x802c20  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_initializeMediaItems
    // 0x94a9c8: ldur            x1, [fp, #-8]
    // 0x94a9cc: r0 = _preloadImageSizes()
    //     0x94a9cc: bl              #0x8075f0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0x94a9d0: r0 = Null
    //     0x94a9d0: mov             x0, NULL
    // 0x94a9d4: LeaveFrame
    //     0x94a9d4: mov             SP, fp
    //     0x94a9d8: ldp             fp, lr, [SP], #0x10
    // 0x94a9dc: ret
    //     0x94a9dc: ret             
    // 0x94a9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a9e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a9e4: b               #0x94a9b8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x99db20, size: 0x60
    // 0x99db20: EnterFrame
    //     0x99db20: stp             fp, lr, [SP, #-0x10]!
    //     0x99db24: mov             fp, SP
    // 0x99db28: AllocStack(0x10)
    //     0x99db28: sub             SP, SP, #0x10
    // 0x99db2c: SetupParameters()
    //     0x99db2c: ldr             x0, [fp, #0x10]
    //     0x99db30: ldur            w1, [x0, #0x17]
    //     0x99db34: add             x1, x1, HEAP, lsl #32
    // 0x99db38: CheckStackOverflow
    //     0x99db38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99db3c: cmp             SP, x16
    //     0x99db40: b.ls            #0x99db78
    // 0x99db44: LoadField: r0 = r1->field_f
    //     0x99db44: ldur            w0, [x1, #0xf]
    // 0x99db48: DecompressPointer r0
    //     0x99db48: add             x0, x0, HEAP, lsl #32
    // 0x99db4c: mov             x1, x0
    // 0x99db50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99db50: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99db54: r0 = of()
    //     0x99db54: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x99db58: r16 = <Object?>
    //     0x99db58: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0x99db5c: stp             x0, x16, [SP]
    // 0x99db60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x99db60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x99db64: r0 = pop()
    //     0x99db64: bl              #0x67c754  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0x99db68: r0 = Null
    //     0x99db68: mov             x0, NULL
    // 0x99db6c: LeaveFrame
    //     0x99db6c: mov             SP, fp
    //     0x99db70: ldp             fp, lr, [SP], #0x10
    // 0x99db74: ret
    //     0x99db74: ret             
    // 0x99db78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99db78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99db7c: b               #0x99db44
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x99db80, size: 0x3c0
    // 0x99db80: EnterFrame
    //     0x99db80: stp             fp, lr, [SP, #-0x10]!
    //     0x99db84: mov             fp, SP
    // 0x99db88: AllocStack(0x50)
    //     0x99db88: sub             SP, SP, #0x50
    // 0x99db8c: SetupParameters()
    //     0x99db8c: ldr             x0, [fp, #0x18]
    //     0x99db90: ldur            w1, [x0, #0x17]
    //     0x99db94: add             x1, x1, HEAP, lsl #32
    //     0x99db98: stur            x1, [fp, #-8]
    // 0x99db9c: CheckStackOverflow
    //     0x99db9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99dba0: cmp             SP, x16
    //     0x99dba4: b.ls            #0x99df04
    // 0x99dba8: r1 = 1
    //     0x99dba8: movz            x1, #0x1
    // 0x99dbac: r0 = AllocateContext()
    //     0x99dbac: bl              #0x16f6108  ; AllocateContextStub
    // 0x99dbb0: mov             x2, x0
    // 0x99dbb4: ldur            x0, [fp, #-8]
    // 0x99dbb8: stur            x2, [fp, #-0x10]
    // 0x99dbbc: StoreField: r2->field_b = r0
    //     0x99dbbc: stur            w0, [x2, #0xb]
    // 0x99dbc0: ldr             x1, [fp, #0x10]
    // 0x99dbc4: StoreField: r2->field_f = r1
    //     0x99dbc4: stur            w1, [x2, #0xf]
    // 0x99dbc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99dbc8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99dbcc: r0 = _of()
    //     0x99dbcc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99dbd0: LoadField: r1 = r0->field_7
    //     0x99dbd0: ldur            w1, [x0, #7]
    // 0x99dbd4: DecompressPointer r1
    //     0x99dbd4: add             x1, x1, HEAP, lsl #32
    // 0x99dbd8: LoadField: d0 = r1->field_7
    //     0x99dbd8: ldur            d0, [x1, #7]
    // 0x99dbdc: d1 = 0.880000
    //     0x99dbdc: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0x99dbe0: ldr             d1, [x17, #0x8e0]
    // 0x99dbe4: fmul            d2, d0, d1
    // 0x99dbe8: ldur            x2, [fp, #-0x10]
    // 0x99dbec: stur            d2, [fp, #-0x30]
    // 0x99dbf0: LoadField: r1 = r2->field_f
    //     0x99dbf0: ldur            w1, [x2, #0xf]
    // 0x99dbf4: DecompressPointer r1
    //     0x99dbf4: add             x1, x1, HEAP, lsl #32
    // 0x99dbf8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99dbf8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99dbfc: r0 = _of()
    //     0x99dbfc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99dc00: LoadField: r1 = r0->field_7
    //     0x99dc00: ldur            w1, [x0, #7]
    // 0x99dc04: DecompressPointer r1
    //     0x99dc04: add             x1, x1, HEAP, lsl #32
    // 0x99dc08: LoadField: d0 = r1->field_7
    //     0x99dc08: ldur            d0, [x1, #7]
    // 0x99dc0c: d1 = 0.880000
    //     0x99dc0c: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0x99dc10: ldr             d1, [x17, #0x8e0]
    // 0x99dc14: fmul            d2, d0, d1
    // 0x99dc18: ldur            x0, [fp, #-8]
    // 0x99dc1c: stur            d2, [fp, #-0x38]
    // 0x99dc20: LoadField: r1 = r0->field_f
    //     0x99dc20: ldur            w1, [x0, #0xf]
    // 0x99dc24: DecompressPointer r1
    //     0x99dc24: add             x1, x1, HEAP, lsl #32
    // 0x99dc28: LoadField: r2 = r0->field_13
    //     0x99dc28: ldur            w2, [x0, #0x13]
    // 0x99dc2c: DecompressPointer r2
    //     0x99dc2c: add             x2, x2, HEAP, lsl #32
    // 0x99dc30: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x99dc30: ldur            w3, [x0, #0x17]
    // 0x99dc34: DecompressPointer r3
    //     0x99dc34: add             x3, x3, HEAP, lsl #32
    // 0x99dc38: r0 = _buildDialogContent()
    //     0x99dc38: bl              #0x99df40  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildDialogContent
    // 0x99dc3c: stur            x0, [fp, #-0x18]
    // 0x99dc40: r0 = AlertDialog()
    //     0x99dc40: bl              #0x99db14  ; AllocateAlertDialogStub -> AlertDialog (size=0x50)
    // 0x99dc44: mov             x1, x0
    // 0x99dc48: ldur            x0, [fp, #-0x18]
    // 0x99dc4c: stur            x1, [fp, #-0x20]
    // 0x99dc50: StoreField: r1->field_f = r0
    //     0x99dc50: stur            w0, [x1, #0xf]
    // 0x99dc54: r0 = Instance_RoundedRectangleBorder
    //     0x99dc54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x99dc58: ldr             x0, [x0, #0xd68]
    // 0x99dc5c: StoreField: r1->field_3f = r0
    //     0x99dc5c: stur            w0, [x1, #0x3f]
    // 0x99dc60: r0 = false
    //     0x99dc60: add             x0, NULL, #0x30  ; false
    // 0x99dc64: StoreField: r1->field_4b = r0
    //     0x99dc64: stur            w0, [x1, #0x4b]
    // 0x99dc68: ldur            d0, [fp, #-0x38]
    // 0x99dc6c: r2 = inline_Allocate_Double()
    //     0x99dc6c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x99dc70: add             x2, x2, #0x10
    //     0x99dc74: cmp             x3, x2
    //     0x99dc78: b.ls            #0x99df0c
    //     0x99dc7c: str             x2, [THR, #0x50]  ; THR::top
    //     0x99dc80: sub             x2, x2, #0xf
    //     0x99dc84: movz            x3, #0xe15c
    //     0x99dc88: movk            x3, #0x3, lsl #16
    //     0x99dc8c: stur            x3, [x2, #-1]
    // 0x99dc90: StoreField: r2->field_7 = d0
    //     0x99dc90: stur            d0, [x2, #7]
    // 0x99dc94: stur            x2, [fp, #-0x18]
    // 0x99dc98: r0 = SizedBox()
    //     0x99dc98: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x99dc9c: mov             x1, x0
    // 0x99dca0: ldur            x0, [fp, #-0x18]
    // 0x99dca4: stur            x1, [fp, #-0x28]
    // 0x99dca8: StoreField: r1->field_f = r0
    //     0x99dca8: stur            w0, [x1, #0xf]
    // 0x99dcac: ldur            x0, [fp, #-0x20]
    // 0x99dcb0: StoreField: r1->field_b = r0
    //     0x99dcb0: stur            w0, [x1, #0xb]
    // 0x99dcb4: r0 = Center()
    //     0x99dcb4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x99dcb8: mov             x1, x0
    // 0x99dcbc: r0 = Instance_Alignment
    //     0x99dcbc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x99dcc0: ldr             x0, [x0, #0xb10]
    // 0x99dcc4: stur            x1, [fp, #-0x18]
    // 0x99dcc8: StoreField: r1->field_f = r0
    //     0x99dcc8: stur            w0, [x1, #0xf]
    // 0x99dccc: ldur            x0, [fp, #-0x28]
    // 0x99dcd0: StoreField: r1->field_b = r0
    //     0x99dcd0: stur            w0, [x1, #0xb]
    // 0x99dcd4: r0 = SvgPicture()
    //     0x99dcd4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x99dcd8: mov             x1, x0
    // 0x99dcdc: r2 = "assets/images/gift-icon-popup.svg"
    //     0x99dcdc: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0x99dce0: ldr             x2, [x2, #0x8e8]
    // 0x99dce4: stur            x0, [fp, #-0x20]
    // 0x99dce8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x99dce8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x99dcec: r0 = SvgPicture.asset()
    //     0x99dcec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x99dcf0: ldur            x0, [fp, #-8]
    // 0x99dcf4: LoadField: r2 = r0->field_1b
    //     0x99dcf4: ldur            w2, [x0, #0x1b]
    // 0x99dcf8: DecompressPointer r2
    //     0x99dcf8: add             x2, x2, HEAP, lsl #32
    // 0x99dcfc: stur            x2, [fp, #-0x28]
    // 0x99dd00: r1 = <StackParentData>
    //     0x99dd00: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x99dd04: ldr             x1, [x1, #0x8e0]
    // 0x99dd08: r0 = Positioned()
    //     0x99dd08: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x99dd0c: mov             x3, x0
    // 0x99dd10: ldur            x0, [fp, #-0x28]
    // 0x99dd14: stur            x3, [fp, #-8]
    // 0x99dd18: ArrayStore: r3[0] = r0  ; List_4
    //     0x99dd18: stur            w0, [x3, #0x17]
    // 0x99dd1c: ldur            x1, [fp, #-0x20]
    // 0x99dd20: StoreField: r3->field_b = r1
    //     0x99dd20: stur            w1, [x3, #0xb]
    // 0x99dd24: r1 = Null
    //     0x99dd24: mov             x1, NULL
    // 0x99dd28: r2 = 4
    //     0x99dd28: movz            x2, #0x4
    // 0x99dd2c: r0 = AllocateArray()
    //     0x99dd2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99dd30: mov             x2, x0
    // 0x99dd34: ldur            x0, [fp, #-0x18]
    // 0x99dd38: stur            x2, [fp, #-0x20]
    // 0x99dd3c: StoreField: r2->field_f = r0
    //     0x99dd3c: stur            w0, [x2, #0xf]
    // 0x99dd40: ldur            x0, [fp, #-8]
    // 0x99dd44: StoreField: r2->field_13 = r0
    //     0x99dd44: stur            w0, [x2, #0x13]
    // 0x99dd48: r1 = <Widget>
    //     0x99dd48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x99dd4c: r0 = AllocateGrowableArray()
    //     0x99dd4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x99dd50: mov             x1, x0
    // 0x99dd54: ldur            x0, [fp, #-0x20]
    // 0x99dd58: stur            x1, [fp, #-8]
    // 0x99dd5c: StoreField: r1->field_f = r0
    //     0x99dd5c: stur            w0, [x1, #0xf]
    // 0x99dd60: r2 = 4
    //     0x99dd60: movz            x2, #0x4
    // 0x99dd64: StoreField: r1->field_b = r2
    //     0x99dd64: stur            w2, [x1, #0xb]
    // 0x99dd68: r0 = Stack()
    //     0x99dd68: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x99dd6c: mov             x3, x0
    // 0x99dd70: r0 = Instance_Alignment
    //     0x99dd70: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x99dd74: ldr             x0, [x0, #0xce0]
    // 0x99dd78: stur            x3, [fp, #-0x18]
    // 0x99dd7c: StoreField: r3->field_f = r0
    //     0x99dd7c: stur            w0, [x3, #0xf]
    // 0x99dd80: r4 = Instance_StackFit
    //     0x99dd80: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x99dd84: ldr             x4, [x4, #0xfa8]
    // 0x99dd88: ArrayStore: r3[0] = r4  ; List_4
    //     0x99dd88: stur            w4, [x3, #0x17]
    // 0x99dd8c: r5 = Instance_Clip
    //     0x99dd8c: add             x5, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x99dd90: ldr             x5, [x5, #0x7e0]
    // 0x99dd94: StoreField: r3->field_1b = r5
    //     0x99dd94: stur            w5, [x3, #0x1b]
    // 0x99dd98: ldur            x1, [fp, #-8]
    // 0x99dd9c: StoreField: r3->field_b = r1
    //     0x99dd9c: stur            w1, [x3, #0xb]
    // 0x99dda0: ldur            x2, [fp, #-0x10]
    // 0x99dda4: r1 = Function '<anonymous closure>':.
    //     0x99dda4: add             x1, PP, #0x52, lsl #12  ; [pp+0x528f0] AnonymousClosure: (0x99db20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0x99dda8: ldr             x1, [x1, #0x8f0]
    // 0x99ddac: r0 = AllocateClosure()
    //     0x99ddac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x99ddb0: stur            x0, [fp, #-8]
    // 0x99ddb4: r0 = IconButton()
    //     0x99ddb4: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0x99ddb8: mov             x2, x0
    // 0x99ddbc: ldur            x0, [fp, #-8]
    // 0x99ddc0: stur            x2, [fp, #-0x10]
    // 0x99ddc4: StoreField: r2->field_3b = r0
    //     0x99ddc4: stur            w0, [x2, #0x3b]
    // 0x99ddc8: r0 = false
    //     0x99ddc8: add             x0, NULL, #0x30  ; false
    // 0x99ddcc: StoreField: r2->field_4f = r0
    //     0x99ddcc: stur            w0, [x2, #0x4f]
    // 0x99ddd0: r0 = Instance_Icon
    //     0x99ddd0: add             x0, PP, #0x52, lsl #12  ; [pp+0x528f8] Obj!Icon@d65d71
    //     0x99ddd4: ldr             x0, [x0, #0x8f8]
    // 0x99ddd8: StoreField: r2->field_1f = r0
    //     0x99ddd8: stur            w0, [x2, #0x1f]
    // 0x99dddc: r0 = Instance__IconButtonVariant
    //     0x99dddc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0x99dde0: ldr             x0, [x0, #0x900]
    // 0x99dde4: StoreField: r2->field_6b = r0
    //     0x99dde4: stur            w0, [x2, #0x6b]
    // 0x99dde8: r1 = <StackParentData>
    //     0x99dde8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x99ddec: ldr             x1, [x1, #0x8e0]
    // 0x99ddf0: r0 = Positioned()
    //     0x99ddf0: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x99ddf4: mov             x3, x0
    // 0x99ddf8: ldur            x0, [fp, #-0x28]
    // 0x99ddfc: stur            x3, [fp, #-8]
    // 0x99de00: ArrayStore: r3[0] = r0  ; List_4
    //     0x99de00: stur            w0, [x3, #0x17]
    // 0x99de04: r0 = 50.000000
    //     0x99de04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x99de08: ldr             x0, [x0, #0xa90]
    // 0x99de0c: StoreField: r3->field_1b = r0
    //     0x99de0c: stur            w0, [x3, #0x1b]
    // 0x99de10: ldur            x0, [fp, #-0x10]
    // 0x99de14: StoreField: r3->field_b = r0
    //     0x99de14: stur            w0, [x3, #0xb]
    // 0x99de18: r1 = Null
    //     0x99de18: mov             x1, NULL
    // 0x99de1c: r2 = 4
    //     0x99de1c: movz            x2, #0x4
    // 0x99de20: r0 = AllocateArray()
    //     0x99de20: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99de24: mov             x2, x0
    // 0x99de28: ldur            x0, [fp, #-0x18]
    // 0x99de2c: stur            x2, [fp, #-0x10]
    // 0x99de30: StoreField: r2->field_f = r0
    //     0x99de30: stur            w0, [x2, #0xf]
    // 0x99de34: ldur            x0, [fp, #-8]
    // 0x99de38: StoreField: r2->field_13 = r0
    //     0x99de38: stur            w0, [x2, #0x13]
    // 0x99de3c: r1 = <Widget>
    //     0x99de3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x99de40: r0 = AllocateGrowableArray()
    //     0x99de40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x99de44: mov             x1, x0
    // 0x99de48: ldur            x0, [fp, #-0x10]
    // 0x99de4c: stur            x1, [fp, #-8]
    // 0x99de50: StoreField: r1->field_f = r0
    //     0x99de50: stur            w0, [x1, #0xf]
    // 0x99de54: r0 = 4
    //     0x99de54: movz            x0, #0x4
    // 0x99de58: StoreField: r1->field_b = r0
    //     0x99de58: stur            w0, [x1, #0xb]
    // 0x99de5c: r0 = Stack()
    //     0x99de5c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x99de60: mov             x1, x0
    // 0x99de64: r0 = Instance_Alignment
    //     0x99de64: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x99de68: ldr             x0, [x0, #0xce0]
    // 0x99de6c: stur            x1, [fp, #-0x10]
    // 0x99de70: StoreField: r1->field_f = r0
    //     0x99de70: stur            w0, [x1, #0xf]
    // 0x99de74: r0 = Instance_StackFit
    //     0x99de74: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x99de78: ldr             x0, [x0, #0xfa8]
    // 0x99de7c: ArrayStore: r1[0] = r0  ; List_4
    //     0x99de7c: stur            w0, [x1, #0x17]
    // 0x99de80: r0 = Instance_Clip
    //     0x99de80: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x99de84: ldr             x0, [x0, #0x7e0]
    // 0x99de88: StoreField: r1->field_1b = r0
    //     0x99de88: stur            w0, [x1, #0x1b]
    // 0x99de8c: ldur            x0, [fp, #-8]
    // 0x99de90: StoreField: r1->field_b = r0
    //     0x99de90: stur            w0, [x1, #0xb]
    // 0x99de94: ldur            d0, [fp, #-0x30]
    // 0x99de98: r0 = inline_Allocate_Double()
    //     0x99de98: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x99de9c: add             x0, x0, #0x10
    //     0x99dea0: cmp             x2, x0
    //     0x99dea4: b.ls            #0x99df28
    //     0x99dea8: str             x0, [THR, #0x50]  ; THR::top
    //     0x99deac: sub             x0, x0, #0xf
    //     0x99deb0: movz            x2, #0xe15c
    //     0x99deb4: movk            x2, #0x3, lsl #16
    //     0x99deb8: stur            x2, [x0, #-1]
    // 0x99debc: StoreField: r0->field_7 = d0
    //     0x99debc: stur            d0, [x0, #7]
    // 0x99dec0: stur            x0, [fp, #-8]
    // 0x99dec4: r0 = Container()
    //     0x99dec4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x99dec8: stur            x0, [fp, #-0x18]
    // 0x99decc: r16 = Instance_Color
    //     0x99decc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x99ded0: ldr             x16, [x16, #0xf88]
    // 0x99ded4: ldur            lr, [fp, #-8]
    // 0x99ded8: stp             lr, x16, [SP, #8]
    // 0x99dedc: ldur            x16, [fp, #-0x10]
    // 0x99dee0: str             x16, [SP]
    // 0x99dee4: mov             x1, x0
    // 0x99dee8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, width, 0x2, null]
    //     0x99dee8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52908] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "width", 0x2, Null]
    //     0x99deec: ldr             x4, [x4, #0x908]
    // 0x99def0: r0 = Container()
    //     0x99def0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x99def4: ldur            x0, [fp, #-0x18]
    // 0x99def8: LeaveFrame
    //     0x99def8: mov             SP, fp
    //     0x99defc: ldp             fp, lr, [SP], #0x10
    // 0x99df00: ret
    //     0x99df00: ret             
    // 0x99df04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99df04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99df08: b               #0x99dba8
    // 0x99df0c: SaveReg d0
    //     0x99df0c: str             q0, [SP, #-0x10]!
    // 0x99df10: stp             x0, x1, [SP, #-0x10]!
    // 0x99df14: r0 = AllocateDouble()
    //     0x99df14: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99df18: mov             x2, x0
    // 0x99df1c: ldp             x0, x1, [SP], #0x10
    // 0x99df20: RestoreReg d0
    //     0x99df20: ldr             q0, [SP], #0x10
    // 0x99df24: b               #0x99dc90
    // 0x99df28: SaveReg d0
    //     0x99df28: str             q0, [SP, #-0x10]!
    // 0x99df2c: SaveReg r1
    //     0x99df2c: str             x1, [SP, #-8]!
    // 0x99df30: r0 = AllocateDouble()
    //     0x99df30: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99df34: RestoreReg r1
    //     0x99df34: ldr             x1, [SP], #8
    // 0x99df38: RestoreReg d0
    //     0x99df38: ldr             q0, [SP], #0x10
    // 0x99df3c: b               #0x99debc
  }
  _ _buildDialogContent(/* No info */) {
    // ** addr: 0x99df40, size: 0x694
    // 0x99df40: EnterFrame
    //     0x99df40: stp             fp, lr, [SP, #-0x10]!
    //     0x99df44: mov             fp, SP
    // 0x99df48: AllocStack(0x78)
    //     0x99df48: sub             SP, SP, #0x78
    // 0x99df4c: SetupParameters(_ProductMediaCarouselState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x99df4c: mov             x4, x1
    //     0x99df50: mov             x0, x2
    //     0x99df54: stur            x1, [fp, #-8]
    //     0x99df58: stur            x2, [fp, #-0x10]
    //     0x99df5c: stur            x3, [fp, #-0x18]
    // 0x99df60: CheckStackOverflow
    //     0x99df60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99df64: cmp             SP, x16
    //     0x99df68: b.ls            #0x99e588
    // 0x99df6c: r1 = <Widget>
    //     0x99df6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x99df70: r2 = 18
    //     0x99df70: movz            x2, #0x12
    // 0x99df74: r0 = AllocateArray()
    //     0x99df74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99df78: stur            x0, [fp, #-0x20]
    // 0x99df7c: r16 = Instance_SizedBox
    //     0x99df7c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x99df80: ldr             x16, [x16, #0xd68]
    // 0x99df84: StoreField: r0->field_f = r16
    //     0x99df84: stur            w16, [x0, #0xf]
    // 0x99df88: ldur            x2, [fp, #-8]
    // 0x99df8c: LoadField: r1 = r2->field_f
    //     0x99df8c: ldur            w1, [x2, #0xf]
    // 0x99df90: DecompressPointer r1
    //     0x99df90: add             x1, x1, HEAP, lsl #32
    // 0x99df94: cmp             w1, NULL
    // 0x99df98: b.eq            #0x99e590
    // 0x99df9c: r0 = of()
    //     0x99df9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99dfa0: LoadField: r1 = r0->field_87
    //     0x99dfa0: ldur            w1, [x0, #0x87]
    // 0x99dfa4: DecompressPointer r1
    //     0x99dfa4: add             x1, x1, HEAP, lsl #32
    // 0x99dfa8: LoadField: r0 = r1->field_7
    //     0x99dfa8: ldur            w0, [x1, #7]
    // 0x99dfac: DecompressPointer r0
    //     0x99dfac: add             x0, x0, HEAP, lsl #32
    // 0x99dfb0: r16 = 12.000000
    //     0x99dfb0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x99dfb4: ldr             x16, [x16, #0x9e8]
    // 0x99dfb8: str             x16, [SP]
    // 0x99dfbc: mov             x1, x0
    // 0x99dfc0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x99dfc0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x99dfc4: ldr             x4, [x4, #0x798]
    // 0x99dfc8: r0 = copyWith()
    //     0x99dfc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99dfcc: stur            x0, [fp, #-0x28]
    // 0x99dfd0: r0 = Text()
    //     0x99dfd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99dfd4: mov             x1, x0
    // 0x99dfd8: r0 = "Get a Free Gift with this product!"
    //     0x99dfd8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52910] "Get a Free Gift with this product!"
    //     0x99dfdc: ldr             x0, [x0, #0x910]
    // 0x99dfe0: StoreField: r1->field_b = r0
    //     0x99dfe0: stur            w0, [x1, #0xb]
    // 0x99dfe4: ldur            x0, [fp, #-0x28]
    // 0x99dfe8: StoreField: r1->field_13 = r0
    //     0x99dfe8: stur            w0, [x1, #0x13]
    // 0x99dfec: mov             x0, x1
    // 0x99dff0: ldur            x1, [fp, #-0x20]
    // 0x99dff4: ArrayStore: r1[1] = r0  ; List_4
    //     0x99dff4: add             x25, x1, #0x13
    //     0x99dff8: str             w0, [x25]
    //     0x99dffc: tbz             w0, #0, #0x99e018
    //     0x99e000: ldurb           w16, [x1, #-1]
    //     0x99e004: ldurb           w17, [x0, #-1]
    //     0x99e008: and             x16, x17, x16, lsr #2
    //     0x99e00c: tst             x16, HEAP, lsr #32
    //     0x99e010: b.eq            #0x99e018
    //     0x99e014: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99e018: ldur            x1, [fp, #-0x20]
    // 0x99e01c: r16 = Instance_SizedBox
    //     0x99e01c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x99e020: ldr             x16, [x16, #0x578]
    // 0x99e024: ArrayStore: r1[0] = r16  ; List_4
    //     0x99e024: stur            w16, [x1, #0x17]
    // 0x99e028: r0 = ImageHeaders.forImages()
    //     0x99e028: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x99e02c: ldur            x2, [fp, #-0x10]
    // 0x99e030: stur            x0, [fp, #-0x30]
    // 0x99e034: cmp             w2, NULL
    // 0x99e038: b.ne            #0x99e044
    // 0x99e03c: r1 = Null
    //     0x99e03c: mov             x1, NULL
    // 0x99e040: b               #0x99e04c
    // 0x99e044: LoadField: r1 = r2->field_f
    //     0x99e044: ldur            w1, [x2, #0xf]
    // 0x99e048: DecompressPointer r1
    //     0x99e048: add             x1, x1, HEAP, lsl #32
    // 0x99e04c: cmp             w1, NULL
    // 0x99e050: b.ne            #0x99e05c
    // 0x99e054: r5 = ""
    //     0x99e054: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x99e058: b               #0x99e060
    // 0x99e05c: mov             x5, x1
    // 0x99e060: ldur            x4, [fp, #-8]
    // 0x99e064: ldur            x3, [fp, #-0x20]
    // 0x99e068: stur            x5, [fp, #-0x28]
    // 0x99e06c: LoadField: r1 = r4->field_f
    //     0x99e06c: ldur            w1, [x4, #0xf]
    // 0x99e070: DecompressPointer r1
    //     0x99e070: add             x1, x1, HEAP, lsl #32
    // 0x99e074: cmp             w1, NULL
    // 0x99e078: b.eq            #0x99e594
    // 0x99e07c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e07c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e080: r0 = _of()
    //     0x99e080: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e084: LoadField: r1 = r0->field_7
    //     0x99e084: ldur            w1, [x0, #7]
    // 0x99e088: DecompressPointer r1
    //     0x99e088: add             x1, x1, HEAP, lsl #32
    // 0x99e08c: LoadField: d0 = r1->field_f
    //     0x99e08c: ldur            d0, [x1, #0xf]
    // 0x99e090: d1 = 0.200000
    //     0x99e090: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x99e094: fmul            d2, d0, d1
    // 0x99e098: ldur            x0, [fp, #-8]
    // 0x99e09c: stur            d2, [fp, #-0x58]
    // 0x99e0a0: LoadField: r1 = r0->field_f
    //     0x99e0a0: ldur            w1, [x0, #0xf]
    // 0x99e0a4: DecompressPointer r1
    //     0x99e0a4: add             x1, x1, HEAP, lsl #32
    // 0x99e0a8: cmp             w1, NULL
    // 0x99e0ac: b.eq            #0x99e598
    // 0x99e0b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e0b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e0b4: r0 = _of()
    //     0x99e0b4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e0b8: LoadField: r1 = r0->field_7
    //     0x99e0b8: ldur            w1, [x0, #7]
    // 0x99e0bc: DecompressPointer r1
    //     0x99e0bc: add             x1, x1, HEAP, lsl #32
    // 0x99e0c0: LoadField: d0 = r1->field_7
    //     0x99e0c0: ldur            d0, [x1, #7]
    // 0x99e0c4: d1 = 0.770000
    //     0x99e0c4: add             x17, PP, #0x52, lsl #12  ; [pp+0x52918] IMM: double(0.77) from 0x3fe8a3d70a3d70a4
    //     0x99e0c8: ldr             d1, [x17, #0x918]
    // 0x99e0cc: fmul            d2, d0, d1
    // 0x99e0d0: ldur            d0, [fp, #-0x58]
    // 0x99e0d4: r0 = inline_Allocate_Double()
    //     0x99e0d4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x99e0d8: add             x0, x0, #0x10
    //     0x99e0dc: cmp             x1, x0
    //     0x99e0e0: b.ls            #0x99e59c
    //     0x99e0e4: str             x0, [THR, #0x50]  ; THR::top
    //     0x99e0e8: sub             x0, x0, #0xf
    //     0x99e0ec: movz            x1, #0xe15c
    //     0x99e0f0: movk            x1, #0x3, lsl #16
    //     0x99e0f4: stur            x1, [x0, #-1]
    // 0x99e0f8: StoreField: r0->field_7 = d0
    //     0x99e0f8: stur            d0, [x0, #7]
    // 0x99e0fc: stur            x0, [fp, #-0x40]
    // 0x99e100: r1 = inline_Allocate_Double()
    //     0x99e100: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x99e104: add             x1, x1, #0x10
    //     0x99e108: cmp             x2, x1
    //     0x99e10c: b.ls            #0x99e5ac
    //     0x99e110: str             x1, [THR, #0x50]  ; THR::top
    //     0x99e114: sub             x1, x1, #0xf
    //     0x99e118: movz            x2, #0xe15c
    //     0x99e11c: movk            x2, #0x3, lsl #16
    //     0x99e120: stur            x2, [x1, #-1]
    // 0x99e124: StoreField: r1->field_7 = d2
    //     0x99e124: stur            d2, [x1, #7]
    // 0x99e128: stur            x1, [fp, #-0x38]
    // 0x99e12c: r0 = CachedNetworkImage()
    //     0x99e12c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x99e130: stur            x0, [fp, #-0x48]
    // 0x99e134: ldur            x16, [fp, #-0x30]
    // 0x99e138: r30 = Instance_BoxFit
    //     0x99e138: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x99e13c: ldr             lr, [lr, #0x118]
    // 0x99e140: stp             lr, x16, [SP, #0x10]
    // 0x99e144: ldur            x16, [fp, #-0x40]
    // 0x99e148: ldur            lr, [fp, #-0x38]
    // 0x99e14c: stp             lr, x16, [SP]
    // 0x99e150: mov             x1, x0
    // 0x99e154: ldur            x2, [fp, #-0x28]
    // 0x99e158: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x99e158: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x99e15c: ldr             x4, [x4, #0xa98]
    // 0x99e160: r0 = CachedNetworkImage()
    //     0x99e160: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x99e164: ldur            x1, [fp, #-0x20]
    // 0x99e168: ldur            x0, [fp, #-0x48]
    // 0x99e16c: ArrayStore: r1[3] = r0  ; List_4
    //     0x99e16c: add             x25, x1, #0x1b
    //     0x99e170: str             w0, [x25]
    //     0x99e174: tbz             w0, #0, #0x99e190
    //     0x99e178: ldurb           w16, [x1, #-1]
    //     0x99e17c: ldurb           w17, [x0, #-1]
    //     0x99e180: and             x16, x17, x16, lsr #2
    //     0x99e184: tst             x16, HEAP, lsr #32
    //     0x99e188: b.eq            #0x99e190
    //     0x99e18c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99e190: ldur            x0, [fp, #-0x20]
    // 0x99e194: r16 = Instance_SizedBox
    //     0x99e194: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0x99e198: ldr             x16, [x16, #0x578]
    // 0x99e19c: StoreField: r0->field_1f = r16
    //     0x99e19c: stur            w16, [x0, #0x1f]
    // 0x99e1a0: ldur            x2, [fp, #-0x10]
    // 0x99e1a4: cmp             w2, NULL
    // 0x99e1a8: b.ne            #0x99e1b4
    // 0x99e1ac: r1 = Null
    //     0x99e1ac: mov             x1, NULL
    // 0x99e1b0: b               #0x99e1bc
    // 0x99e1b4: LoadField: r1 = r2->field_b
    //     0x99e1b4: ldur            w1, [x2, #0xb]
    // 0x99e1b8: DecompressPointer r1
    //     0x99e1b8: add             x1, x1, HEAP, lsl #32
    // 0x99e1bc: cmp             w1, NULL
    // 0x99e1c0: b.ne            #0x99e1cc
    // 0x99e1c4: r4 = ""
    //     0x99e1c4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x99e1c8: b               #0x99e1d0
    // 0x99e1cc: mov             x4, x1
    // 0x99e1d0: ldur            x3, [fp, #-8]
    // 0x99e1d4: stur            x4, [fp, #-0x28]
    // 0x99e1d8: LoadField: r1 = r3->field_f
    //     0x99e1d8: ldur            w1, [x3, #0xf]
    // 0x99e1dc: DecompressPointer r1
    //     0x99e1dc: add             x1, x1, HEAP, lsl #32
    // 0x99e1e0: cmp             w1, NULL
    // 0x99e1e4: b.eq            #0x99e5c8
    // 0x99e1e8: r0 = of()
    //     0x99e1e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99e1ec: LoadField: r1 = r0->field_87
    //     0x99e1ec: ldur            w1, [x0, #0x87]
    // 0x99e1f0: DecompressPointer r1
    //     0x99e1f0: add             x1, x1, HEAP, lsl #32
    // 0x99e1f4: LoadField: r0 = r1->field_7
    //     0x99e1f4: ldur            w0, [x1, #7]
    // 0x99e1f8: DecompressPointer r0
    //     0x99e1f8: add             x0, x0, HEAP, lsl #32
    // 0x99e1fc: r16 = 12.000000
    //     0x99e1fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x99e200: ldr             x16, [x16, #0x9e8]
    // 0x99e204: str             x16, [SP]
    // 0x99e208: mov             x1, x0
    // 0x99e20c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x99e20c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x99e210: ldr             x4, [x4, #0x798]
    // 0x99e214: r0 = copyWith()
    //     0x99e214: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99e218: stur            x0, [fp, #-0x30]
    // 0x99e21c: r0 = Text()
    //     0x99e21c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99e220: mov             x1, x0
    // 0x99e224: ldur            x0, [fp, #-0x28]
    // 0x99e228: StoreField: r1->field_b = r0
    //     0x99e228: stur            w0, [x1, #0xb]
    // 0x99e22c: ldur            x0, [fp, #-0x30]
    // 0x99e230: StoreField: r1->field_13 = r0
    //     0x99e230: stur            w0, [x1, #0x13]
    // 0x99e234: mov             x0, x1
    // 0x99e238: ldur            x1, [fp, #-0x20]
    // 0x99e23c: ArrayStore: r1[5] = r0  ; List_4
    //     0x99e23c: add             x25, x1, #0x23
    //     0x99e240: str             w0, [x25]
    //     0x99e244: tbz             w0, #0, #0x99e260
    //     0x99e248: ldurb           w16, [x1, #-1]
    //     0x99e24c: ldurb           w17, [x0, #-1]
    //     0x99e250: and             x16, x17, x16, lsr #2
    //     0x99e254: tst             x16, HEAP, lsr #32
    //     0x99e258: b.eq            #0x99e260
    //     0x99e25c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99e260: ldur            x0, [fp, #-0x20]
    // 0x99e264: r16 = Instance_SizedBox
    //     0x99e264: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x99e268: ldr             x16, [x16, #0x8f0]
    // 0x99e26c: StoreField: r0->field_27 = r16
    //     0x99e26c: stur            w16, [x0, #0x27]
    // 0x99e270: ldur            x2, [fp, #-8]
    // 0x99e274: LoadField: r1 = r2->field_f
    //     0x99e274: ldur            w1, [x2, #0xf]
    // 0x99e278: DecompressPointer r1
    //     0x99e278: add             x1, x1, HEAP, lsl #32
    // 0x99e27c: cmp             w1, NULL
    // 0x99e280: b.eq            #0x99e5cc
    // 0x99e284: r0 = of()
    //     0x99e284: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99e288: LoadField: r1 = r0->field_87
    //     0x99e288: ldur            w1, [x0, #0x87]
    // 0x99e28c: DecompressPointer r1
    //     0x99e28c: add             x1, x1, HEAP, lsl #32
    // 0x99e290: LoadField: r0 = r1->field_2b
    //     0x99e290: ldur            w0, [x1, #0x2b]
    // 0x99e294: DecompressPointer r0
    //     0x99e294: add             x0, x0, HEAP, lsl #32
    // 0x99e298: r16 = 12.000000
    //     0x99e298: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x99e29c: ldr             x16, [x16, #0x9e8]
    // 0x99e2a0: r30 = Instance_Color
    //     0x99e2a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x99e2a4: ldr             lr, [lr, #0x858]
    // 0x99e2a8: stp             lr, x16, [SP]
    // 0x99e2ac: mov             x1, x0
    // 0x99e2b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x99e2b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x99e2b4: ldr             x4, [x4, #0xaa0]
    // 0x99e2b8: r0 = copyWith()
    //     0x99e2b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99e2bc: stur            x0, [fp, #-0x28]
    // 0x99e2c0: r0 = Text()
    //     0x99e2c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99e2c4: mov             x2, x0
    // 0x99e2c8: r0 = "Free"
    //     0x99e2c8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x99e2cc: ldr             x0, [x0, #0x668]
    // 0x99e2d0: stur            x2, [fp, #-0x30]
    // 0x99e2d4: StoreField: r2->field_b = r0
    //     0x99e2d4: stur            w0, [x2, #0xb]
    // 0x99e2d8: ldur            x0, [fp, #-0x28]
    // 0x99e2dc: StoreField: r2->field_13 = r0
    //     0x99e2dc: stur            w0, [x2, #0x13]
    // 0x99e2e0: ldur            x0, [fp, #-0x10]
    // 0x99e2e4: cmp             w0, NULL
    // 0x99e2e8: b.ne            #0x99e2f4
    // 0x99e2ec: r1 = Null
    //     0x99e2ec: mov             x1, NULL
    // 0x99e2f0: b               #0x99e2fc
    // 0x99e2f4: LoadField: r1 = r0->field_13
    //     0x99e2f4: ldur            w1, [x0, #0x13]
    // 0x99e2f8: DecompressPointer r1
    //     0x99e2f8: add             x1, x1, HEAP, lsl #32
    // 0x99e2fc: cmp             w1, NULL
    // 0x99e300: b.ne            #0x99e30c
    // 0x99e304: r6 = ""
    //     0x99e304: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x99e308: b               #0x99e310
    // 0x99e30c: mov             x6, x1
    // 0x99e310: ldur            x4, [fp, #-8]
    // 0x99e314: ldur            x5, [fp, #-0x18]
    // 0x99e318: ldur            x3, [fp, #-0x20]
    // 0x99e31c: stur            x6, [fp, #-0x28]
    // 0x99e320: LoadField: r1 = r4->field_f
    //     0x99e320: ldur            w1, [x4, #0xf]
    // 0x99e324: DecompressPointer r1
    //     0x99e324: add             x1, x1, HEAP, lsl #32
    // 0x99e328: cmp             w1, NULL
    // 0x99e32c: b.eq            #0x99e5d0
    // 0x99e330: r0 = of()
    //     0x99e330: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99e334: LoadField: r1 = r0->field_87
    //     0x99e334: ldur            w1, [x0, #0x87]
    // 0x99e338: DecompressPointer r1
    //     0x99e338: add             x1, x1, HEAP, lsl #32
    // 0x99e33c: LoadField: r0 = r1->field_2b
    //     0x99e33c: ldur            w0, [x1, #0x2b]
    // 0x99e340: DecompressPointer r0
    //     0x99e340: add             x0, x0, HEAP, lsl #32
    // 0x99e344: r16 = 12.000000
    //     0x99e344: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x99e348: ldr             x16, [x16, #0x9e8]
    // 0x99e34c: r30 = Instance_TextDecoration
    //     0x99e34c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x99e350: ldr             lr, [lr, #0xe30]
    // 0x99e354: stp             lr, x16, [SP]
    // 0x99e358: mov             x1, x0
    // 0x99e35c: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x99e35c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x99e360: ldr             x4, [x4, #0x698]
    // 0x99e364: r0 = copyWith()
    //     0x99e364: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99e368: stur            x0, [fp, #-0x38]
    // 0x99e36c: r0 = Text()
    //     0x99e36c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99e370: mov             x3, x0
    // 0x99e374: ldur            x0, [fp, #-0x28]
    // 0x99e378: stur            x3, [fp, #-0x40]
    // 0x99e37c: StoreField: r3->field_b = r0
    //     0x99e37c: stur            w0, [x3, #0xb]
    // 0x99e380: ldur            x0, [fp, #-0x38]
    // 0x99e384: StoreField: r3->field_13 = r0
    //     0x99e384: stur            w0, [x3, #0x13]
    // 0x99e388: r1 = Null
    //     0x99e388: mov             x1, NULL
    // 0x99e38c: r2 = 6
    //     0x99e38c: movz            x2, #0x6
    // 0x99e390: r0 = AllocateArray()
    //     0x99e390: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99e394: mov             x2, x0
    // 0x99e398: ldur            x0, [fp, #-0x30]
    // 0x99e39c: stur            x2, [fp, #-0x28]
    // 0x99e3a0: StoreField: r2->field_f = r0
    //     0x99e3a0: stur            w0, [x2, #0xf]
    // 0x99e3a4: r16 = Instance_SizedBox
    //     0x99e3a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x99e3a8: ldr             x16, [x16, #0xa50]
    // 0x99e3ac: StoreField: r2->field_13 = r16
    //     0x99e3ac: stur            w16, [x2, #0x13]
    // 0x99e3b0: ldur            x0, [fp, #-0x40]
    // 0x99e3b4: ArrayStore: r2[0] = r0  ; List_4
    //     0x99e3b4: stur            w0, [x2, #0x17]
    // 0x99e3b8: r1 = <Widget>
    //     0x99e3b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x99e3bc: r0 = AllocateGrowableArray()
    //     0x99e3bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x99e3c0: mov             x1, x0
    // 0x99e3c4: ldur            x0, [fp, #-0x28]
    // 0x99e3c8: stur            x1, [fp, #-0x30]
    // 0x99e3cc: StoreField: r1->field_f = r0
    //     0x99e3cc: stur            w0, [x1, #0xf]
    // 0x99e3d0: r0 = 6
    //     0x99e3d0: movz            x0, #0x6
    // 0x99e3d4: StoreField: r1->field_b = r0
    //     0x99e3d4: stur            w0, [x1, #0xb]
    // 0x99e3d8: r0 = Row()
    //     0x99e3d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x99e3dc: mov             x1, x0
    // 0x99e3e0: r0 = Instance_Axis
    //     0x99e3e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x99e3e4: StoreField: r1->field_f = r0
    //     0x99e3e4: stur            w0, [x1, #0xf]
    // 0x99e3e8: r2 = Instance_MainAxisAlignment
    //     0x99e3e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x99e3ec: ldr             x2, [x2, #0xa08]
    // 0x99e3f0: StoreField: r1->field_13 = r2
    //     0x99e3f0: stur            w2, [x1, #0x13]
    // 0x99e3f4: r0 = Instance_MainAxisSize
    //     0x99e3f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x99e3f8: ldr             x0, [x0, #0xa10]
    // 0x99e3fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x99e3fc: stur            w0, [x1, #0x17]
    // 0x99e400: r0 = Instance_CrossAxisAlignment
    //     0x99e400: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x99e404: ldr             x0, [x0, #0xa18]
    // 0x99e408: StoreField: r1->field_1b = r0
    //     0x99e408: stur            w0, [x1, #0x1b]
    // 0x99e40c: r3 = Instance_VerticalDirection
    //     0x99e40c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x99e410: ldr             x3, [x3, #0xa20]
    // 0x99e414: StoreField: r1->field_23 = r3
    //     0x99e414: stur            w3, [x1, #0x23]
    // 0x99e418: r4 = Instance_Clip
    //     0x99e418: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x99e41c: ldr             x4, [x4, #0x38]
    // 0x99e420: StoreField: r1->field_2b = r4
    //     0x99e420: stur            w4, [x1, #0x2b]
    // 0x99e424: StoreField: r1->field_2f = rZR
    //     0x99e424: stur            xzr, [x1, #0x2f]
    // 0x99e428: ldur            x0, [fp, #-0x30]
    // 0x99e42c: StoreField: r1->field_b = r0
    //     0x99e42c: stur            w0, [x1, #0xb]
    // 0x99e430: mov             x0, x1
    // 0x99e434: ldur            x1, [fp, #-0x20]
    // 0x99e438: ArrayStore: r1[7] = r0  ; List_4
    //     0x99e438: add             x25, x1, #0x2b
    //     0x99e43c: str             w0, [x25]
    //     0x99e440: tbz             w0, #0, #0x99e45c
    //     0x99e444: ldurb           w16, [x1, #-1]
    //     0x99e448: ldurb           w17, [x0, #-1]
    //     0x99e44c: and             x16, x17, x16, lsr #2
    //     0x99e450: tst             x16, HEAP, lsr #32
    //     0x99e454: b.eq            #0x99e45c
    //     0x99e458: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99e45c: ldur            x0, [fp, #-0x20]
    // 0x99e460: r16 = Instance_SizedBox
    //     0x99e460: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x99e464: ldr             x16, [x16, #0x8f0]
    // 0x99e468: StoreField: r0->field_2f = r16
    //     0x99e468: stur            w16, [x0, #0x2f]
    // 0x99e46c: r1 = <Widget>
    //     0x99e46c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x99e470: r0 = AllocateGrowableArray()
    //     0x99e470: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x99e474: mov             x3, x0
    // 0x99e478: ldur            x0, [fp, #-0x20]
    // 0x99e47c: stur            x3, [fp, #-0x28]
    // 0x99e480: StoreField: r3->field_f = r0
    //     0x99e480: stur            w0, [x3, #0xf]
    // 0x99e484: r0 = 18
    //     0x99e484: movz            x0, #0x12
    // 0x99e488: StoreField: r3->field_b = r0
    //     0x99e488: stur            w0, [x3, #0xb]
    // 0x99e48c: ldur            x0, [fp, #-0x18]
    // 0x99e490: tbnz            w0, #4, #0x99e524
    // 0x99e494: ldur            x1, [fp, #-8]
    // 0x99e498: ldur            x2, [fp, #-0x10]
    // 0x99e49c: r0 = _buildRulesList()
    //     0x99e49c: bl              #0x99e5d4  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList
    // 0x99e4a0: mov             x2, x0
    // 0x99e4a4: ldur            x0, [fp, #-0x28]
    // 0x99e4a8: stur            x2, [fp, #-8]
    // 0x99e4ac: LoadField: r1 = r0->field_b
    //     0x99e4ac: ldur            w1, [x0, #0xb]
    // 0x99e4b0: LoadField: r3 = r0->field_f
    //     0x99e4b0: ldur            w3, [x0, #0xf]
    // 0x99e4b4: DecompressPointer r3
    //     0x99e4b4: add             x3, x3, HEAP, lsl #32
    // 0x99e4b8: LoadField: r4 = r3->field_b
    //     0x99e4b8: ldur            w4, [x3, #0xb]
    // 0x99e4bc: r3 = LoadInt32Instr(r1)
    //     0x99e4bc: sbfx            x3, x1, #1, #0x1f
    // 0x99e4c0: stur            x3, [fp, #-0x50]
    // 0x99e4c4: r1 = LoadInt32Instr(r4)
    //     0x99e4c4: sbfx            x1, x4, #1, #0x1f
    // 0x99e4c8: cmp             x3, x1
    // 0x99e4cc: b.ne            #0x99e4d8
    // 0x99e4d0: mov             x1, x0
    // 0x99e4d4: r0 = _growToNextCapacity()
    //     0x99e4d4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x99e4d8: ldur            x2, [fp, #-0x28]
    // 0x99e4dc: ldur            x3, [fp, #-0x50]
    // 0x99e4e0: add             x0, x3, #1
    // 0x99e4e4: lsl             x1, x0, #1
    // 0x99e4e8: StoreField: r2->field_b = r1
    //     0x99e4e8: stur            w1, [x2, #0xb]
    // 0x99e4ec: LoadField: r1 = r2->field_f
    //     0x99e4ec: ldur            w1, [x2, #0xf]
    // 0x99e4f0: DecompressPointer r1
    //     0x99e4f0: add             x1, x1, HEAP, lsl #32
    // 0x99e4f4: ldur            x0, [fp, #-8]
    // 0x99e4f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x99e4f8: add             x25, x1, x3, lsl #2
    //     0x99e4fc: add             x25, x25, #0xf
    //     0x99e500: str             w0, [x25]
    //     0x99e504: tbz             w0, #0, #0x99e520
    //     0x99e508: ldurb           w16, [x1, #-1]
    //     0x99e50c: ldurb           w17, [x0, #-1]
    //     0x99e510: and             x16, x17, x16, lsr #2
    //     0x99e514: tst             x16, HEAP, lsr #32
    //     0x99e518: b.eq            #0x99e520
    //     0x99e51c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x99e520: b               #0x99e528
    // 0x99e524: mov             x2, x3
    // 0x99e528: r0 = Column()
    //     0x99e528: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x99e52c: r1 = Instance_Axis
    //     0x99e52c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x99e530: StoreField: r0->field_f = r1
    //     0x99e530: stur            w1, [x0, #0xf]
    // 0x99e534: r1 = Instance_MainAxisAlignment
    //     0x99e534: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x99e538: ldr             x1, [x1, #0xa08]
    // 0x99e53c: StoreField: r0->field_13 = r1
    //     0x99e53c: stur            w1, [x0, #0x13]
    // 0x99e540: r1 = Instance_MainAxisSize
    //     0x99e540: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x99e544: ldr             x1, [x1, #0xdd0]
    // 0x99e548: ArrayStore: r0[0] = r1  ; List_4
    //     0x99e548: stur            w1, [x0, #0x17]
    // 0x99e54c: r1 = Instance_CrossAxisAlignment
    //     0x99e54c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x99e550: ldr             x1, [x1, #0x890]
    // 0x99e554: StoreField: r0->field_1b = r1
    //     0x99e554: stur            w1, [x0, #0x1b]
    // 0x99e558: r1 = Instance_VerticalDirection
    //     0x99e558: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x99e55c: ldr             x1, [x1, #0xa20]
    // 0x99e560: StoreField: r0->field_23 = r1
    //     0x99e560: stur            w1, [x0, #0x23]
    // 0x99e564: r1 = Instance_Clip
    //     0x99e564: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x99e568: ldr             x1, [x1, #0x38]
    // 0x99e56c: StoreField: r0->field_2b = r1
    //     0x99e56c: stur            w1, [x0, #0x2b]
    // 0x99e570: StoreField: r0->field_2f = rZR
    //     0x99e570: stur            xzr, [x0, #0x2f]
    // 0x99e574: ldur            x1, [fp, #-0x28]
    // 0x99e578: StoreField: r0->field_b = r1
    //     0x99e578: stur            w1, [x0, #0xb]
    // 0x99e57c: LeaveFrame
    //     0x99e57c: mov             SP, fp
    //     0x99e580: ldp             fp, lr, [SP], #0x10
    // 0x99e584: ret
    //     0x99e584: ret             
    // 0x99e588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99e588: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99e58c: b               #0x99df6c
    // 0x99e590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e590: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e598: r0 = NullCastErrorSharedWithFPURegs()
    //     0x99e598: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x99e59c: stp             q0, q2, [SP, #-0x20]!
    // 0x99e5a0: r0 = AllocateDouble()
    //     0x99e5a0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99e5a4: ldp             q0, q2, [SP], #0x20
    // 0x99e5a8: b               #0x99e0f8
    // 0x99e5ac: SaveReg d2
    //     0x99e5ac: str             q2, [SP, #-0x10]!
    // 0x99e5b0: SaveReg r0
    //     0x99e5b0: str             x0, [SP, #-8]!
    // 0x99e5b4: r0 = AllocateDouble()
    //     0x99e5b4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99e5b8: mov             x1, x0
    // 0x99e5bc: RestoreReg r0
    //     0x99e5bc: ldr             x0, [SP], #8
    // 0x99e5c0: RestoreReg d2
    //     0x99e5c0: ldr             q2, [SP], #0x10
    // 0x99e5c4: b               #0x99e124
    // 0x99e5c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e5c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e5cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e5cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e5d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e5d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildRulesList(/* No info */) {
    // ** addr: 0x99e5d4, size: 0x1b4
    // 0x99e5d4: EnterFrame
    //     0x99e5d4: stp             fp, lr, [SP, #-0x10]!
    //     0x99e5d8: mov             fp, SP
    // 0x99e5dc: AllocStack(0x38)
    //     0x99e5dc: sub             SP, SP, #0x38
    // 0x99e5e0: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x99e5e0: stur            x1, [fp, #-8]
    //     0x99e5e4: stur            x2, [fp, #-0x10]
    // 0x99e5e8: CheckStackOverflow
    //     0x99e5e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99e5ec: cmp             SP, x16
    //     0x99e5f0: b.ls            #0x99e768
    // 0x99e5f4: r1 = 1
    //     0x99e5f4: movz            x1, #0x1
    // 0x99e5f8: r0 = AllocateContext()
    //     0x99e5f8: bl              #0x16f6108  ; AllocateContextStub
    // 0x99e5fc: mov             x2, x0
    // 0x99e600: ldur            x0, [fp, #-0x10]
    // 0x99e604: stur            x2, [fp, #-0x20]
    // 0x99e608: StoreField: r2->field_f = r0
    //     0x99e608: stur            w0, [x2, #0xf]
    // 0x99e60c: cmp             w0, NULL
    // 0x99e610: b.ne            #0x99e61c
    // 0x99e614: r0 = Null
    //     0x99e614: mov             x0, NULL
    // 0x99e618: b               #0x99e628
    // 0x99e61c: LoadField: r1 = r0->field_7
    //     0x99e61c: ldur            w1, [x0, #7]
    // 0x99e620: DecompressPointer r1
    //     0x99e620: add             x1, x1, HEAP, lsl #32
    // 0x99e624: LoadField: r0 = r1->field_b
    //     0x99e624: ldur            w0, [x1, #0xb]
    // 0x99e628: cmp             w0, NULL
    // 0x99e62c: b.ne            #0x99e638
    // 0x99e630: r0 = 0
    //     0x99e630: movz            x0, #0
    // 0x99e634: b               #0x99e640
    // 0x99e638: r1 = LoadInt32Instr(r0)
    //     0x99e638: sbfx            x1, x0, #1, #0x1f
    // 0x99e63c: mov             x0, x1
    // 0x99e640: stur            x0, [fp, #-0x18]
    // 0x99e644: cmp             x0, #1
    // 0x99e648: b.ne            #0x99e688
    // 0x99e64c: ldur            x1, [fp, #-8]
    // 0x99e650: LoadField: r3 = r1->field_f
    //     0x99e650: ldur            w3, [x1, #0xf]
    // 0x99e654: DecompressPointer r3
    //     0x99e654: add             x3, x3, HEAP, lsl #32
    // 0x99e658: cmp             w3, NULL
    // 0x99e65c: b.eq            #0x99e770
    // 0x99e660: mov             x1, x3
    // 0x99e664: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e664: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e668: r0 = _of()
    //     0x99e668: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e66c: LoadField: r1 = r0->field_7
    //     0x99e66c: ldur            w1, [x0, #7]
    // 0x99e670: DecompressPointer r1
    //     0x99e670: add             x1, x1, HEAP, lsl #32
    // 0x99e674: LoadField: d0 = r1->field_f
    //     0x99e674: ldur            d0, [x1, #0xf]
    // 0x99e678: d1 = 0.040000
    //     0x99e678: ldr             d1, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0x99e67c: fmul            d2, d0, d1
    // 0x99e680: mov             v0.16b, v2.16b
    // 0x99e684: b               #0x99e6c4
    // 0x99e688: ldur            x1, [fp, #-8]
    // 0x99e68c: LoadField: r0 = r1->field_f
    //     0x99e68c: ldur            w0, [x1, #0xf]
    // 0x99e690: DecompressPointer r0
    //     0x99e690: add             x0, x0, HEAP, lsl #32
    // 0x99e694: cmp             w0, NULL
    // 0x99e698: b.eq            #0x99e774
    // 0x99e69c: mov             x1, x0
    // 0x99e6a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e6a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e6a4: r0 = _of()
    //     0x99e6a4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e6a8: LoadField: r1 = r0->field_7
    //     0x99e6a8: ldur            w1, [x0, #7]
    // 0x99e6ac: DecompressPointer r1
    //     0x99e6ac: add             x1, x1, HEAP, lsl #32
    // 0x99e6b0: LoadField: d0 = r1->field_f
    //     0x99e6b0: ldur            d0, [x1, #0xf]
    // 0x99e6b4: d1 = 0.090000
    //     0x99e6b4: add             x17, PP, #0x52, lsl #12  ; [pp+0x52920] IMM: double(0.09) from 0x3fb70a3d70a3d70a
    //     0x99e6b8: ldr             d1, [x17, #0x920]
    // 0x99e6bc: fmul            d2, d0, d1
    // 0x99e6c0: mov             v0.16b, v2.16b
    // 0x99e6c4: ldur            x0, [fp, #-0x18]
    // 0x99e6c8: stur            d0, [fp, #-0x28]
    // 0x99e6cc: lsl             x3, x0, #1
    // 0x99e6d0: ldur            x2, [fp, #-0x20]
    // 0x99e6d4: stur            x3, [fp, #-8]
    // 0x99e6d8: r1 = Function '<anonymous closure>':.
    //     0x99e6d8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52928] AnonymousClosure: (0x99e788), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildRulesList (0x99e5d4)
    //     0x99e6dc: ldr             x1, [x1, #0x928]
    // 0x99e6e0: r0 = AllocateClosure()
    //     0x99e6e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x99e6e4: stur            x0, [fp, #-0x10]
    // 0x99e6e8: r0 = ListView()
    //     0x99e6e8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x99e6ec: stur            x0, [fp, #-0x20]
    // 0x99e6f0: r16 = true
    //     0x99e6f0: add             x16, NULL, #0x20  ; true
    // 0x99e6f4: r30 = Instance_NeverScrollableScrollPhysics
    //     0x99e6f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x99e6f8: ldr             lr, [lr, #0x1c8]
    // 0x99e6fc: stp             lr, x16, [SP]
    // 0x99e700: mov             x1, x0
    // 0x99e704: ldur            x2, [fp, #-0x10]
    // 0x99e708: ldur            x3, [fp, #-8]
    // 0x99e70c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x99e70c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x99e710: ldr             x4, [x4, #8]
    // 0x99e714: r0 = ListView.builder()
    //     0x99e714: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x99e718: ldur            d0, [fp, #-0x28]
    // 0x99e71c: r0 = inline_Allocate_Double()
    //     0x99e71c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x99e720: add             x0, x0, #0x10
    //     0x99e724: cmp             x1, x0
    //     0x99e728: b.ls            #0x99e778
    //     0x99e72c: str             x0, [THR, #0x50]  ; THR::top
    //     0x99e730: sub             x0, x0, #0xf
    //     0x99e734: movz            x1, #0xe15c
    //     0x99e738: movk            x1, #0x3, lsl #16
    //     0x99e73c: stur            x1, [x0, #-1]
    // 0x99e740: StoreField: r0->field_7 = d0
    //     0x99e740: stur            d0, [x0, #7]
    // 0x99e744: stur            x0, [fp, #-8]
    // 0x99e748: r0 = SizedBox()
    //     0x99e748: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x99e74c: ldur            x1, [fp, #-8]
    // 0x99e750: StoreField: r0->field_13 = r1
    //     0x99e750: stur            w1, [x0, #0x13]
    // 0x99e754: ldur            x1, [fp, #-0x20]
    // 0x99e758: StoreField: r0->field_b = r1
    //     0x99e758: stur            w1, [x0, #0xb]
    // 0x99e75c: LeaveFrame
    //     0x99e75c: mov             SP, fp
    //     0x99e760: ldp             fp, lr, [SP], #0x10
    // 0x99e764: ret
    //     0x99e764: ret             
    // 0x99e768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99e768: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99e76c: b               #0x99e5f4
    // 0x99e770: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e770: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99e774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99e778: SaveReg d0
    //     0x99e778: str             q0, [SP, #-0x10]!
    // 0x99e77c: r0 = AllocateDouble()
    //     0x99e77c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99e780: RestoreReg d0
    //     0x99e780: ldr             q0, [SP], #0x10
    // 0x99e784: b               #0x99e740
  }
  [closure] Text <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x99e788, size: 0x114
    // 0x99e788: EnterFrame
    //     0x99e788: stp             fp, lr, [SP, #-0x10]!
    //     0x99e78c: mov             fp, SP
    // 0x99e790: AllocStack(0x18)
    //     0x99e790: sub             SP, SP, #0x18
    // 0x99e794: SetupParameters()
    //     0x99e794: ldr             x0, [fp, #0x20]
    //     0x99e798: ldur            w3, [x0, #0x17]
    //     0x99e79c: add             x3, x3, HEAP, lsl #32
    //     0x99e7a0: stur            x3, [fp, #-8]
    // 0x99e7a4: CheckStackOverflow
    //     0x99e7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99e7a8: cmp             SP, x16
    //     0x99e7ac: b.ls            #0x99e88c
    // 0x99e7b0: r1 = Null
    //     0x99e7b0: mov             x1, NULL
    // 0x99e7b4: r2 = 4
    //     0x99e7b4: movz            x2, #0x4
    // 0x99e7b8: r0 = AllocateArray()
    //     0x99e7b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99e7bc: mov             x2, x0
    // 0x99e7c0: r16 = "• "
    //     0x99e7c0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52930] "• "
    //     0x99e7c4: ldr             x16, [x16, #0x930]
    // 0x99e7c8: StoreField: r2->field_f = r16
    //     0x99e7c8: stur            w16, [x2, #0xf]
    // 0x99e7cc: ldur            x0, [fp, #-8]
    // 0x99e7d0: LoadField: r1 = r0->field_f
    //     0x99e7d0: ldur            w1, [x0, #0xf]
    // 0x99e7d4: DecompressPointer r1
    //     0x99e7d4: add             x1, x1, HEAP, lsl #32
    // 0x99e7d8: cmp             w1, NULL
    // 0x99e7dc: b.eq            #0x99e894
    // 0x99e7e0: LoadField: r3 = r1->field_7
    //     0x99e7e0: ldur            w3, [x1, #7]
    // 0x99e7e4: DecompressPointer r3
    //     0x99e7e4: add             x3, x3, HEAP, lsl #32
    // 0x99e7e8: LoadField: r0 = r3->field_b
    //     0x99e7e8: ldur            w0, [x3, #0xb]
    // 0x99e7ec: ldr             x1, [fp, #0x10]
    // 0x99e7f0: r4 = LoadInt32Instr(r1)
    //     0x99e7f0: sbfx            x4, x1, #1, #0x1f
    //     0x99e7f4: tbz             w1, #0, #0x99e7fc
    //     0x99e7f8: ldur            x4, [x1, #7]
    // 0x99e7fc: r1 = LoadInt32Instr(r0)
    //     0x99e7fc: sbfx            x1, x0, #1, #0x1f
    // 0x99e800: mov             x0, x1
    // 0x99e804: mov             x1, x4
    // 0x99e808: cmp             x1, x0
    // 0x99e80c: b.hs            #0x99e898
    // 0x99e810: LoadField: r0 = r3->field_f
    //     0x99e810: ldur            w0, [x3, #0xf]
    // 0x99e814: DecompressPointer r0
    //     0x99e814: add             x0, x0, HEAP, lsl #32
    // 0x99e818: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x99e818: add             x16, x0, x4, lsl #2
    //     0x99e81c: ldur            w1, [x16, #0xf]
    // 0x99e820: DecompressPointer r1
    //     0x99e820: add             x1, x1, HEAP, lsl #32
    // 0x99e824: StoreField: r2->field_13 = r1
    //     0x99e824: stur            w1, [x2, #0x13]
    // 0x99e828: str             x2, [SP]
    // 0x99e82c: r0 = _interpolate()
    //     0x99e82c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x99e830: ldr             x1, [fp, #0x18]
    // 0x99e834: stur            x0, [fp, #-8]
    // 0x99e838: r0 = of()
    //     0x99e838: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99e83c: LoadField: r1 = r0->field_87
    //     0x99e83c: ldur            w1, [x0, #0x87]
    // 0x99e840: DecompressPointer r1
    //     0x99e840: add             x1, x1, HEAP, lsl #32
    // 0x99e844: LoadField: r0 = r1->field_2b
    //     0x99e844: ldur            w0, [x1, #0x2b]
    // 0x99e848: DecompressPointer r0
    //     0x99e848: add             x0, x0, HEAP, lsl #32
    // 0x99e84c: r16 = 12.000000
    //     0x99e84c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x99e850: ldr             x16, [x16, #0x9e8]
    // 0x99e854: str             x16, [SP]
    // 0x99e858: mov             x1, x0
    // 0x99e85c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x99e85c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x99e860: ldr             x4, [x4, #0x798]
    // 0x99e864: r0 = copyWith()
    //     0x99e864: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99e868: stur            x0, [fp, #-0x10]
    // 0x99e86c: r0 = Text()
    //     0x99e86c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99e870: ldur            x1, [fp, #-8]
    // 0x99e874: StoreField: r0->field_b = r1
    //     0x99e874: stur            w1, [x0, #0xb]
    // 0x99e878: ldur            x1, [fp, #-0x10]
    // 0x99e87c: StoreField: r0->field_13 = r1
    //     0x99e87c: stur            w1, [x0, #0x13]
    // 0x99e880: LeaveFrame
    //     0x99e880: mov             SP, fp
    //     0x99e884: ldp             fp, lr, [SP], #0x10
    // 0x99e888: ret
    //     0x99e888: ret             
    // 0x99e88c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99e88c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99e890: b               #0x99e7b0
    // 0x99e894: r0 = NullErrorSharedWithoutFPURegs()
    //     0x99e894: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x99e898: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x99e898: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _showFreeGiftDialog(/* No info */) {
    // ** addr: 0x99e89c, size: 0x230
    // 0x99e89c: EnterFrame
    //     0x99e89c: stp             fp, lr, [SP, #-0x10]!
    //     0x99e8a0: mov             fp, SP
    // 0x99e8a4: AllocStack(0x38)
    //     0x99e8a4: sub             SP, SP, #0x38
    // 0x99e8a8: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x99e8a8: stur            x1, [fp, #-8]
    // 0x99e8ac: CheckStackOverflow
    //     0x99e8ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99e8b0: cmp             SP, x16
    //     0x99e8b4: b.ls            #0x99ea98
    // 0x99e8b8: r1 = 4
    //     0x99e8b8: movz            x1, #0x4
    // 0x99e8bc: r0 = AllocateContext()
    //     0x99e8bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x99e8c0: mov             x2, x0
    // 0x99e8c4: ldur            x0, [fp, #-8]
    // 0x99e8c8: stur            x2, [fp, #-0x10]
    // 0x99e8cc: StoreField: r2->field_f = r0
    //     0x99e8cc: stur            w0, [x2, #0xf]
    // 0x99e8d0: LoadField: r1 = r0->field_b
    //     0x99e8d0: ldur            w1, [x0, #0xb]
    // 0x99e8d4: DecompressPointer r1
    //     0x99e8d4: add             x1, x1, HEAP, lsl #32
    // 0x99e8d8: cmp             w1, NULL
    // 0x99e8dc: b.eq            #0x99eaa0
    // 0x99e8e0: LoadField: r3 = r1->field_1b
    //     0x99e8e0: ldur            w3, [x1, #0x1b]
    // 0x99e8e4: DecompressPointer r3
    //     0x99e8e4: add             x3, x3, HEAP, lsl #32
    // 0x99e8e8: LoadField: r1 = r3->field_b
    //     0x99e8e8: ldur            w1, [x3, #0xb]
    // 0x99e8ec: DecompressPointer r1
    //     0x99e8ec: add             x1, x1, HEAP, lsl #32
    // 0x99e8f0: StoreField: r2->field_13 = r1
    //     0x99e8f0: stur            w1, [x2, #0x13]
    // 0x99e8f4: cmp             w1, NULL
    // 0x99e8f8: b.ne            #0x99e904
    // 0x99e8fc: r3 = Null
    //     0x99e8fc: mov             x3, NULL
    // 0x99e900: b               #0x99e920
    // 0x99e904: LoadField: r3 = r1->field_7
    //     0x99e904: ldur            w3, [x1, #7]
    // 0x99e908: DecompressPointer r3
    //     0x99e908: add             x3, x3, HEAP, lsl #32
    // 0x99e90c: LoadField: r4 = r3->field_b
    //     0x99e90c: ldur            w4, [x3, #0xb]
    // 0x99e910: cbnz            w4, #0x99e91c
    // 0x99e914: r3 = false
    //     0x99e914: add             x3, NULL, #0x30  ; false
    // 0x99e918: b               #0x99e920
    // 0x99e91c: r3 = true
    //     0x99e91c: add             x3, NULL, #0x20  ; true
    // 0x99e920: cmp             w3, NULL
    // 0x99e924: b.ne            #0x99e92c
    // 0x99e928: r3 = false
    //     0x99e928: add             x3, NULL, #0x30  ; false
    // 0x99e92c: ArrayStore: r2[0] = r3  ; List_4
    //     0x99e92c: stur            w3, [x2, #0x17]
    // 0x99e930: tbz             w3, #4, #0x99e96c
    // 0x99e934: LoadField: r1 = r0->field_f
    //     0x99e934: ldur            w1, [x0, #0xf]
    // 0x99e938: DecompressPointer r1
    //     0x99e938: add             x1, x1, HEAP, lsl #32
    // 0x99e93c: cmp             w1, NULL
    // 0x99e940: b.eq            #0x99eaa4
    // 0x99e944: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e944: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e948: r0 = _of()
    //     0x99e948: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e94c: LoadField: r1 = r0->field_7
    //     0x99e94c: ldur            w1, [x0, #7]
    // 0x99e950: DecompressPointer r1
    //     0x99e950: add             x1, x1, HEAP, lsl #32
    // 0x99e954: LoadField: d0 = r1->field_f
    //     0x99e954: ldur            d0, [x1, #0xf]
    // 0x99e958: d1 = 0.230000
    //     0x99e958: add             x17, PP, #0x52, lsl #12  ; [pp+0x528b8] IMM: double(0.23) from 0x3fcd70a3d70a3d71
    //     0x99e95c: ldr             d1, [x17, #0x8b8]
    // 0x99e960: fmul            d2, d0, d1
    // 0x99e964: mov             v0.16b, v2.16b
    // 0x99e968: b               #0x99e9fc
    // 0x99e96c: cmp             w1, NULL
    // 0x99e970: b.eq            #0x99e9c4
    // 0x99e974: LoadField: r0 = r1->field_7
    //     0x99e974: ldur            w0, [x1, #7]
    // 0x99e978: DecompressPointer r0
    //     0x99e978: add             x0, x0, HEAP, lsl #32
    // 0x99e97c: LoadField: r1 = r0->field_b
    //     0x99e97c: ldur            w1, [x0, #0xb]
    // 0x99e980: cmp             w1, #2
    // 0x99e984: b.ne            #0x99e9c4
    // 0x99e988: ldur            x0, [fp, #-8]
    // 0x99e98c: LoadField: r1 = r0->field_f
    //     0x99e98c: ldur            w1, [x0, #0xf]
    // 0x99e990: DecompressPointer r1
    //     0x99e990: add             x1, x1, HEAP, lsl #32
    // 0x99e994: cmp             w1, NULL
    // 0x99e998: b.eq            #0x99eaa8
    // 0x99e99c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e99c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e9a0: r0 = _of()
    //     0x99e9a0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e9a4: LoadField: r1 = r0->field_7
    //     0x99e9a4: ldur            w1, [x0, #7]
    // 0x99e9a8: DecompressPointer r1
    //     0x99e9a8: add             x1, x1, HEAP, lsl #32
    // 0x99e9ac: LoadField: d0 = r1->field_f
    //     0x99e9ac: ldur            d0, [x1, #0xf]
    // 0x99e9b0: d1 = 0.210000
    //     0x99e9b0: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c0] IMM: double(0.21) from 0x3fcae147ae147ae1
    //     0x99e9b4: ldr             d1, [x17, #0x8c0]
    // 0x99e9b8: fmul            d2, d0, d1
    // 0x99e9bc: mov             v0.16b, v2.16b
    // 0x99e9c0: b               #0x99e9fc
    // 0x99e9c4: ldur            x0, [fp, #-8]
    // 0x99e9c8: LoadField: r1 = r0->field_f
    //     0x99e9c8: ldur            w1, [x0, #0xf]
    // 0x99e9cc: DecompressPointer r1
    //     0x99e9cc: add             x1, x1, HEAP, lsl #32
    // 0x99e9d0: cmp             w1, NULL
    // 0x99e9d4: b.eq            #0x99eaac
    // 0x99e9d8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99e9d8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x99e9dc: r0 = _of()
    //     0x99e9dc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x99e9e0: LoadField: r1 = r0->field_7
    //     0x99e9e0: ldur            w1, [x0, #7]
    // 0x99e9e4: DecompressPointer r1
    //     0x99e9e4: add             x1, x1, HEAP, lsl #32
    // 0x99e9e8: LoadField: d0 = r1->field_f
    //     0x99e9e8: ldur            d0, [x1, #0xf]
    // 0x99e9ec: d1 = 0.185000
    //     0x99e9ec: add             x17, PP, #0x52, lsl #12  ; [pp+0x528c8] IMM: double(0.185) from 0x3fc7ae147ae147ae
    //     0x99e9f0: ldr             d1, [x17, #0x8c8]
    // 0x99e9f4: fmul            d2, d0, d1
    // 0x99e9f8: mov             v0.16b, v2.16b
    // 0x99e9fc: ldur            x1, [fp, #-8]
    // 0x99ea00: ldur            x2, [fp, #-0x10]
    // 0x99ea04: r0 = inline_Allocate_Double()
    //     0x99ea04: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x99ea08: add             x0, x0, #0x10
    //     0x99ea0c: cmp             x3, x0
    //     0x99ea10: b.ls            #0x99eab0
    //     0x99ea14: str             x0, [THR, #0x50]  ; THR::top
    //     0x99ea18: sub             x0, x0, #0xf
    //     0x99ea1c: movz            x3, #0xe15c
    //     0x99ea20: movk            x3, #0x3, lsl #16
    //     0x99ea24: stur            x3, [x0, #-1]
    // 0x99ea28: StoreField: r0->field_7 = d0
    //     0x99ea28: stur            d0, [x0, #7]
    // 0x99ea2c: StoreField: r2->field_1b = r0
    //     0x99ea2c: stur            w0, [x2, #0x1b]
    //     0x99ea30: ldurb           w16, [x2, #-1]
    //     0x99ea34: ldurb           w17, [x0, #-1]
    //     0x99ea38: and             x16, x17, x16, lsr #2
    //     0x99ea3c: tst             x16, HEAP, lsr #32
    //     0x99ea40: b.eq            #0x99ea48
    //     0x99ea44: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x99ea48: LoadField: r0 = r1->field_f
    //     0x99ea48: ldur            w0, [x1, #0xf]
    // 0x99ea4c: DecompressPointer r0
    //     0x99ea4c: add             x0, x0, HEAP, lsl #32
    // 0x99ea50: stur            x0, [fp, #-0x18]
    // 0x99ea54: cmp             w0, NULL
    // 0x99ea58: b.eq            #0x99eac8
    // 0x99ea5c: r1 = Function '<anonymous closure>':.
    //     0x99ea5c: add             x1, PP, #0x52, lsl #12  ; [pp+0x528d0] AnonymousClosure: (0x99db80), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0x99ea60: ldr             x1, [x1, #0x8d0]
    // 0x99ea64: r0 = AllocateClosure()
    //     0x99ea64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x99ea68: r16 = <void?>
    //     0x99ea68: ldr             x16, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x99ea6c: stp             x0, x16, [SP, #0x10]
    // 0x99ea70: ldur            x16, [fp, #-0x18]
    // 0x99ea74: r30 = false
    //     0x99ea74: add             lr, NULL, #0x30  ; false
    // 0x99ea78: stp             lr, x16, [SP]
    // 0x99ea7c: r4 = const [0x1, 0x3, 0x3, 0x2, barrierDismissible, 0x2, null]
    //     0x99ea7c: add             x4, PP, #0x52, lsl #12  ; [pp+0x528d8] List(7) [0x1, 0x3, 0x3, 0x2, "barrierDismissible", 0x2, Null]
    //     0x99ea80: ldr             x4, [x4, #0x8d8]
    // 0x99ea84: r0 = showDialog()
    //     0x99ea84: bl              #0x99c870  ; [package:flutter/src/material/dialog.dart] ::showDialog
    // 0x99ea88: r0 = Null
    //     0x99ea88: mov             x0, NULL
    // 0x99ea8c: LeaveFrame
    //     0x99ea8c: mov             SP, fp
    //     0x99ea90: ldp             fp, lr, [SP], #0x10
    // 0x99ea94: ret
    //     0x99ea94: ret             
    // 0x99ea98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x99ea98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99ea9c: b               #0x99e8b8
    // 0x99eaa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99eaa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99eaa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99eaa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99eaa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99eaa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99eaac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99eaac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99eab0: SaveReg d0
    //     0x99eab0: str             q0, [SP, #-0x10]!
    // 0x99eab4: stp             x1, x2, [SP, #-0x10]!
    // 0x99eab8: r0 = AllocateDouble()
    //     0x99eab8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x99eabc: ldp             x1, x2, [SP], #0x10
    // 0x99eac0: RestoreReg d0
    //     0x99eac0: ldr             q0, [SP], #0x10
    // 0x99eac4: b               #0x99ea28
    // 0x99eac8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99eac8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa59828, size: 0x3c
    // 0xa59828: ldr             x1, [SP]
    // 0xa5982c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa5982c: ldur            w2, [x1, #0x17]
    // 0xa59830: DecompressPointer r2
    //     0xa59830: add             x2, x2, HEAP, lsl #32
    // 0xa59834: LoadField: r1 = r2->field_b
    //     0xa59834: ldur            w1, [x2, #0xb]
    // 0xa59838: DecompressPointer r1
    //     0xa59838: add             x1, x1, HEAP, lsl #32
    // 0xa5983c: LoadField: r3 = r1->field_f
    //     0xa5983c: ldur            w3, [x1, #0xf]
    // 0xa59840: DecompressPointer r3
    //     0xa59840: add             x3, x3, HEAP, lsl #32
    // 0xa59844: LoadField: r1 = r2->field_f
    //     0xa59844: ldur            w1, [x2, #0xf]
    // 0xa59848: DecompressPointer r1
    //     0xa59848: add             x1, x1, HEAP, lsl #32
    // 0xa5984c: r2 = LoadInt32Instr(r1)
    //     0xa5984c: sbfx            x2, x1, #1, #0x1f
    //     0xa59850: tbz             w1, #0, #0xa59858
    //     0xa59854: ldur            x2, [x1, #7]
    // 0xa59858: StoreField: r3->field_1b = r2
    //     0xa59858: stur            x2, [x3, #0x1b]
    // 0xa5985c: r0 = Null
    //     0xa5985c: mov             x0, NULL
    // 0xa59860: ret
    //     0xa59860: ret             
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa59864, size: 0x84
    // 0xa59864: EnterFrame
    //     0xa59864: stp             fp, lr, [SP, #-0x10]!
    //     0xa59868: mov             fp, SP
    // 0xa5986c: AllocStack(0x10)
    //     0xa5986c: sub             SP, SP, #0x10
    // 0xa59870: SetupParameters()
    //     0xa59870: ldr             x0, [fp, #0x18]
    //     0xa59874: ldur            w1, [x0, #0x17]
    //     0xa59878: add             x1, x1, HEAP, lsl #32
    //     0xa5987c: stur            x1, [fp, #-8]
    // 0xa59880: CheckStackOverflow
    //     0xa59880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59884: cmp             SP, x16
    //     0xa59888: b.ls            #0xa598e0
    // 0xa5988c: r1 = 1
    //     0xa5988c: movz            x1, #0x1
    // 0xa59890: r0 = AllocateContext()
    //     0xa59890: bl              #0x16f6108  ; AllocateContextStub
    // 0xa59894: mov             x1, x0
    // 0xa59898: ldur            x0, [fp, #-8]
    // 0xa5989c: StoreField: r1->field_b = r0
    //     0xa5989c: stur            w0, [x1, #0xb]
    // 0xa598a0: ldr             x2, [fp, #0x10]
    // 0xa598a4: StoreField: r1->field_f = r2
    //     0xa598a4: stur            w2, [x1, #0xf]
    // 0xa598a8: LoadField: r3 = r0->field_f
    //     0xa598a8: ldur            w3, [x0, #0xf]
    // 0xa598ac: DecompressPointer r3
    //     0xa598ac: add             x3, x3, HEAP, lsl #32
    // 0xa598b0: mov             x2, x1
    // 0xa598b4: stur            x3, [fp, #-0x10]
    // 0xa598b8: r1 = Function '<anonymous closure>':.
    //     0xa598b8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52aa8] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xa598bc: ldr             x1, [x1, #0xaa8]
    // 0xa598c0: r0 = AllocateClosure()
    //     0xa598c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa598c4: ldur            x1, [fp, #-0x10]
    // 0xa598c8: mov             x2, x0
    // 0xa598cc: r0 = setState()
    //     0xa598cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa598d0: r0 = Null
    //     0xa598d0: mov             x0, NULL
    // 0xa598d4: LeaveFrame
    //     0xa598d4: mov             SP, fp
    //     0xa598d8: ldp             fp, lr, [SP], #0x10
    // 0xa598dc: ret
    //     0xa598dc: ret             
    // 0xa598e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa598e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa598e4: b               #0xa5988c
  }
  _ _buildMediaCarousel(/* No info */) {
    // ** addr: 0xa598e8, size: 0x1ac
    // 0xa598e8: EnterFrame
    //     0xa598e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa598ec: mov             fp, SP
    // 0xa598f0: AllocStack(0x50)
    //     0xa598f0: sub             SP, SP, #0x50
    // 0xa598f4: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa598f4: mov             x0, x1
    //     0xa598f8: stur            x1, [fp, #-8]
    //     0xa598fc: mov             x1, x2
    //     0xa59900: stur            x2, [fp, #-0x10]
    // 0xa59904: CheckStackOverflow
    //     0xa59904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59908: cmp             SP, x16
    //     0xa5990c: b.ls            #0xa59a70
    // 0xa59910: r1 = 1
    //     0xa59910: movz            x1, #0x1
    // 0xa59914: r0 = AllocateContext()
    //     0xa59914: bl              #0x16f6108  ; AllocateContextStub
    // 0xa59918: mov             x2, x0
    // 0xa5991c: ldur            x0, [fp, #-8]
    // 0xa59920: stur            x2, [fp, #-0x18]
    // 0xa59924: StoreField: r2->field_f = r0
    //     0xa59924: stur            w0, [x2, #0xf]
    // 0xa59928: LoadField: r1 = r0->field_2f
    //     0xa59928: ldur            w1, [x0, #0x2f]
    // 0xa5992c: DecompressPointer r1
    //     0xa5992c: add             x1, x1, HEAP, lsl #32
    // 0xa59930: cmp             w1, NULL
    // 0xa59934: b.ne            #0xa59960
    // 0xa59938: ldur            x1, [fp, #-0x10]
    // 0xa5993c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa5993c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa59940: r0 = _of()
    //     0xa59940: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa59944: LoadField: r1 = r0->field_7
    //     0xa59944: ldur            w1, [x0, #7]
    // 0xa59948: DecompressPointer r1
    //     0xa59948: add             x1, x1, HEAP, lsl #32
    // 0xa5994c: LoadField: d0 = r1->field_f
    //     0xa5994c: ldur            d0, [x1, #0xf]
    // 0xa59950: d1 = 0.400000
    //     0xa59950: ldr             d1, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa59954: fmul            d2, d0, d1
    // 0xa59958: mov             v0.16b, v2.16b
    // 0xa5995c: b               #0xa59964
    // 0xa59960: LoadField: d0 = r1->field_7
    //     0xa59960: ldur            d0, [x1, #7]
    // 0xa59964: ldur            x0, [fp, #-8]
    // 0xa59968: stur            d0, [fp, #-0x30]
    // 0xa5996c: LoadField: r1 = r0->field_23
    //     0xa5996c: ldur            w1, [x0, #0x23]
    // 0xa59970: DecompressPointer r1
    //     0xa59970: add             x1, x1, HEAP, lsl #32
    // 0xa59974: LoadField: r3 = r1->field_b
    //     0xa59974: ldur            w3, [x1, #0xb]
    // 0xa59978: stur            x3, [fp, #-0x20]
    // 0xa5997c: cbnz            w3, #0xa5998c
    // 0xa59980: r0 = Instance_Center
    //     0xa59980: add             x0, PP, #0x52, lsl #12  ; [pp+0x52940] Obj!Center@d68301
    //     0xa59984: ldr             x0, [x0, #0x940]
    // 0xa59988: b               #0xa599fc
    // 0xa5998c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa5998c: ldur            w4, [x0, #0x17]
    // 0xa59990: DecompressPointer r4
    //     0xa59990: add             x4, x4, HEAP, lsl #32
    // 0xa59994: ldur            x2, [fp, #-0x18]
    // 0xa59998: stur            x4, [fp, #-0x10]
    // 0xa5999c: r1 = Function '<anonymous closure>':.
    //     0xa5999c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52948] AnonymousClosure: (0xa59864), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xa599a0: ldr             x1, [x1, #0x948]
    // 0xa599a4: r0 = AllocateClosure()
    //     0xa599a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa599a8: ldur            x2, [fp, #-0x18]
    // 0xa599ac: r1 = Function '<anonymous closure>':.
    //     0xa599ac: add             x1, PP, #0x52, lsl #12  ; [pp+0x52950] AnonymousClosure: (0xa59a94), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xa599b0: ldr             x1, [x1, #0x950]
    // 0xa599b4: stur            x0, [fp, #-8]
    // 0xa599b8: r0 = AllocateClosure()
    //     0xa599b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa599bc: stur            x0, [fp, #-0x18]
    // 0xa599c0: r0 = PageView()
    //     0xa599c0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa599c4: stur            x0, [fp, #-0x28]
    // 0xa599c8: r16 = Instance_BouncingScrollPhysics
    //     0xa599c8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xa599cc: ldr             x16, [x16, #0x890]
    // 0xa599d0: ldur            lr, [fp, #-0x10]
    // 0xa599d4: stp             lr, x16, [SP]
    // 0xa599d8: mov             x1, x0
    // 0xa599dc: ldur            x2, [fp, #-0x18]
    // 0xa599e0: ldur            x3, [fp, #-0x20]
    // 0xa599e4: ldur            x5, [fp, #-8]
    // 0xa599e8: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xa599e8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xa599ec: ldr             x4, [x4, #0xe40]
    // 0xa599f0: r0 = PageView.builder()
    //     0xa599f0: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa599f4: ldur            x0, [fp, #-0x28]
    // 0xa599f8: ldur            d0, [fp, #-0x30]
    // 0xa599fc: stur            x0, [fp, #-0x10]
    // 0xa59a00: r1 = inline_Allocate_Double()
    //     0xa59a00: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xa59a04: add             x1, x1, #0x10
    //     0xa59a08: cmp             x2, x1
    //     0xa59a0c: b.ls            #0xa59a78
    //     0xa59a10: str             x1, [THR, #0x50]  ; THR::top
    //     0xa59a14: sub             x1, x1, #0xf
    //     0xa59a18: movz            x2, #0xe15c
    //     0xa59a1c: movk            x2, #0x3, lsl #16
    //     0xa59a20: stur            x2, [x1, #-1]
    // 0xa59a24: StoreField: r1->field_7 = d0
    //     0xa59a24: stur            d0, [x1, #7]
    // 0xa59a28: stur            x1, [fp, #-8]
    // 0xa59a2c: r0 = Container()
    //     0xa59a2c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa59a30: stur            x0, [fp, #-0x18]
    // 0xa59a34: ldur            x16, [fp, #-8]
    // 0xa59a38: r30 = inf
    //     0xa59a38: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa59a3c: ldr             lr, [lr, #0x9f8]
    // 0xa59a40: stp             lr, x16, [SP, #0x10]
    // 0xa59a44: r16 = Instance_Color
    //     0xa59a44: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa59a48: ldur            lr, [fp, #-0x10]
    // 0xa59a4c: stp             lr, x16, [SP]
    // 0xa59a50: mov             x1, x0
    // 0xa59a54: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xa59a54: add             x4, PP, #0x48, lsl #12  ; [pp+0x48588] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa59a58: ldr             x4, [x4, #0x588]
    // 0xa59a5c: r0 = Container()
    //     0xa59a5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa59a60: ldur            x0, [fp, #-0x18]
    // 0xa59a64: LeaveFrame
    //     0xa59a64: mov             SP, fp
    //     0xa59a68: ldp             fp, lr, [SP], #0x10
    // 0xa59a6c: ret
    //     0xa59a6c: ret             
    // 0xa59a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59a70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59a74: b               #0xa59910
    // 0xa59a78: SaveReg d0
    //     0xa59a78: str             q0, [SP, #-0x10]!
    // 0xa59a7c: SaveReg r0
    //     0xa59a7c: str             x0, [SP, #-8]!
    // 0xa59a80: r0 = AllocateDouble()
    //     0xa59a80: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa59a84: mov             x1, x0
    // 0xa59a88: RestoreReg r0
    //     0xa59a88: ldr             x0, [SP], #8
    // 0xa59a8c: RestoreReg d0
    //     0xa59a8c: ldr             q0, [SP], #0x10
    // 0xa59a90: b               #0xa59a24
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa59a94, size: 0x54
    // 0xa59a94: EnterFrame
    //     0xa59a94: stp             fp, lr, [SP, #-0x10]!
    //     0xa59a98: mov             fp, SP
    // 0xa59a9c: ldr             x0, [fp, #0x20]
    // 0xa59aa0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa59aa0: ldur            w1, [x0, #0x17]
    // 0xa59aa4: DecompressPointer r1
    //     0xa59aa4: add             x1, x1, HEAP, lsl #32
    // 0xa59aa8: CheckStackOverflow
    //     0xa59aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59aac: cmp             SP, x16
    //     0xa59ab0: b.ls            #0xa59ae0
    // 0xa59ab4: LoadField: r0 = r1->field_f
    //     0xa59ab4: ldur            w0, [x1, #0xf]
    // 0xa59ab8: DecompressPointer r0
    //     0xa59ab8: add             x0, x0, HEAP, lsl #32
    // 0xa59abc: ldr             x1, [fp, #0x10]
    // 0xa59ac0: r2 = LoadInt32Instr(r1)
    //     0xa59ac0: sbfx            x2, x1, #1, #0x1f
    //     0xa59ac4: tbz             w1, #0, #0xa59acc
    //     0xa59ac8: ldur            x2, [x1, #7]
    // 0xa59acc: mov             x1, x0
    // 0xa59ad0: r0 = _buildMediaItem()
    //     0xa59ad0: bl              #0xa59ae8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaItem
    // 0xa59ad4: LeaveFrame
    //     0xa59ad4: mov             SP, fp
    //     0xa59ad8: ldp             fp, lr, [SP], #0x10
    // 0xa59adc: ret
    //     0xa59adc: ret             
    // 0xa59ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59ae0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59ae4: b               #0xa59ab4
  }
  _ _buildMediaItem(/* No info */) {
    // ** addr: 0xa59ae8, size: 0xf8
    // 0xa59ae8: EnterFrame
    //     0xa59ae8: stp             fp, lr, [SP, #-0x10]!
    //     0xa59aec: mov             fp, SP
    // 0xa59af0: AllocStack(0x28)
    //     0xa59af0: sub             SP, SP, #0x28
    // 0xa59af4: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa59af4: mov             x3, x1
    //     0xa59af8: stur            x1, [fp, #-0x10]
    //     0xa59afc: stur            x2, [fp, #-0x18]
    // 0xa59b00: CheckStackOverflow
    //     0xa59b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59b04: cmp             SP, x16
    //     0xa59b08: b.ls            #0xa59bd4
    // 0xa59b0c: LoadField: r4 = r3->field_23
    //     0xa59b0c: ldur            w4, [x3, #0x23]
    // 0xa59b10: DecompressPointer r4
    //     0xa59b10: add             x4, x4, HEAP, lsl #32
    // 0xa59b14: LoadField: r0 = r4->field_b
    //     0xa59b14: ldur            w0, [x4, #0xb]
    // 0xa59b18: r1 = LoadInt32Instr(r0)
    //     0xa59b18: sbfx            x1, x0, #1, #0x1f
    // 0xa59b1c: cmp             x2, x1
    // 0xa59b20: b.lt            #0xa59b48
    // 0xa59b24: r0 = Container()
    //     0xa59b24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa59b28: mov             x1, x0
    // 0xa59b2c: stur            x0, [fp, #-8]
    // 0xa59b30: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa59b30: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa59b34: r0 = Container()
    //     0xa59b34: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa59b38: ldur            x0, [fp, #-8]
    // 0xa59b3c: LeaveFrame
    //     0xa59b3c: mov             SP, fp
    //     0xa59b40: ldp             fp, lr, [SP], #0x10
    // 0xa59b44: ret
    //     0xa59b44: ret             
    // 0xa59b48: mov             x0, x1
    // 0xa59b4c: mov             x1, x2
    // 0xa59b50: cmp             x1, x0
    // 0xa59b54: b.hs            #0xa59bdc
    // 0xa59b58: LoadField: r0 = r4->field_f
    //     0xa59b58: ldur            w0, [x4, #0xf]
    // 0xa59b5c: DecompressPointer r0
    //     0xa59b5c: add             x0, x0, HEAP, lsl #32
    // 0xa59b60: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa59b60: add             x16, x0, x2, lsl #2
    //     0xa59b64: ldur            w1, [x16, #0xf]
    // 0xa59b68: DecompressPointer r1
    //     0xa59b68: add             x1, x1, HEAP, lsl #32
    // 0xa59b6c: stur            x1, [fp, #-8]
    // 0xa59b70: LoadField: r0 = r1->field_1f
    //     0xa59b70: ldur            w0, [x1, #0x1f]
    // 0xa59b74: DecompressPointer r0
    //     0xa59b74: add             x0, x0, HEAP, lsl #32
    // 0xa59b78: r4 = LoadClassIdInstr(r0)
    //     0xa59b78: ldur            x4, [x0, #-1]
    //     0xa59b7c: ubfx            x4, x4, #0xc, #0x14
    // 0xa59b80: r16 = "video"
    //     0xa59b80: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0xa59b84: ldr             x16, [x16, #0xb50]
    // 0xa59b88: stp             x16, x0, [SP]
    // 0xa59b8c: mov             x0, x4
    // 0xa59b90: mov             lr, x0
    // 0xa59b94: ldr             lr, [x21, lr, lsl #3]
    // 0xa59b98: blr             lr
    // 0xa59b9c: tbnz            w0, #4, #0xa59bbc
    // 0xa59ba0: ldur            x0, [fp, #-8]
    // 0xa59ba4: r0 = ProductVideoMediaCarousel()
    //     0xa59ba4: bl              #0xa5a41c  ; AllocateProductVideoMediaCarouselStub -> ProductVideoMediaCarousel (size=0x10)
    // 0xa59ba8: mov             x1, x0
    // 0xa59bac: ldur            x0, [fp, #-8]
    // 0xa59bb0: StoreField: r1->field_b = r0
    //     0xa59bb0: stur            w0, [x1, #0xb]
    // 0xa59bb4: mov             x0, x1
    // 0xa59bb8: b               #0xa59bc8
    // 0xa59bbc: ldur            x1, [fp, #-0x10]
    // 0xa59bc0: ldur            x2, [fp, #-0x18]
    // 0xa59bc4: r0 = _buildImageItem()
    //     0xa59bc4: bl              #0xa59be0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageItem
    // 0xa59bc8: LeaveFrame
    //     0xa59bc8: mov             SP, fp
    //     0xa59bcc: ldp             fp, lr, [SP], #0x10
    // 0xa59bd0: ret
    //     0xa59bd0: ret             
    // 0xa59bd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59bd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59bd8: b               #0xa59b0c
    // 0xa59bdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa59bdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildImageItem(/* No info */) {
    // ** addr: 0xa59be0, size: 0x27c
    // 0xa59be0: EnterFrame
    //     0xa59be0: stp             fp, lr, [SP, #-0x10]!
    //     0xa59be4: mov             fp, SP
    // 0xa59be8: AllocStack(0x28)
    //     0xa59be8: sub             SP, SP, #0x28
    // 0xa59bec: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa59bec: mov             x0, x1
    //     0xa59bf0: stur            x1, [fp, #-8]
    //     0xa59bf4: mov             x1, x2
    //     0xa59bf8: stur            x2, [fp, #-0x10]
    // 0xa59bfc: CheckStackOverflow
    //     0xa59bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59c00: cmp             SP, x16
    //     0xa59c04: b.ls            #0xa59e4c
    // 0xa59c08: r1 = 2
    //     0xa59c08: movz            x1, #0x2
    // 0xa59c0c: r0 = AllocateContext()
    //     0xa59c0c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa59c10: mov             x4, x0
    // 0xa59c14: ldur            x3, [fp, #-8]
    // 0xa59c18: stur            x4, [fp, #-0x18]
    // 0xa59c1c: StoreField: r4->field_f = r3
    //     0xa59c1c: stur            w3, [x4, #0xf]
    // 0xa59c20: ldur            x2, [fp, #-0x10]
    // 0xa59c24: r0 = BoxInt64Instr(r2)
    //     0xa59c24: sbfiz           x0, x2, #1, #0x1f
    //     0xa59c28: cmp             x2, x0, asr #1
    //     0xa59c2c: b.eq            #0xa59c38
    //     0xa59c30: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa59c34: stur            x2, [x0, #7]
    // 0xa59c38: StoreField: r4->field_13 = r0
    //     0xa59c38: stur            w0, [x4, #0x13]
    // 0xa59c3c: LoadField: r5 = r3->field_23
    //     0xa59c3c: ldur            w5, [x3, #0x23]
    // 0xa59c40: DecompressPointer r5
    //     0xa59c40: add             x5, x5, HEAP, lsl #32
    // 0xa59c44: LoadField: r0 = r5->field_b
    //     0xa59c44: ldur            w0, [x5, #0xb]
    // 0xa59c48: r1 = LoadInt32Instr(r0)
    //     0xa59c48: sbfx            x1, x0, #1, #0x1f
    // 0xa59c4c: mov             x0, x1
    // 0xa59c50: mov             x1, x2
    // 0xa59c54: cmp             x1, x0
    // 0xa59c58: b.hs            #0xa59e54
    // 0xa59c5c: LoadField: r0 = r5->field_f
    //     0xa59c5c: ldur            w0, [x5, #0xf]
    // 0xa59c60: DecompressPointer r0
    //     0xa59c60: add             x0, x0, HEAP, lsl #32
    // 0xa59c64: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa59c64: add             x16, x0, x2, lsl #2
    //     0xa59c68: ldur            w1, [x16, #0xf]
    // 0xa59c6c: DecompressPointer r1
    //     0xa59c6c: add             x1, x1, HEAP, lsl #32
    // 0xa59c70: LoadField: r0 = r1->field_2b
    //     0xa59c70: ldur            w0, [x1, #0x2b]
    // 0xa59c74: DecompressPointer r0
    //     0xa59c74: add             x0, x0, HEAP, lsl #32
    // 0xa59c78: cmp             w0, NULL
    // 0xa59c7c: b.ne            #0xa59c88
    // 0xa59c80: r0 = Null
    //     0xa59c80: mov             x0, NULL
    // 0xa59c84: b               #0xa59c94
    // 0xa59c88: LoadField: r1 = r0->field_b
    //     0xa59c88: ldur            w1, [x0, #0xb]
    // 0xa59c8c: DecompressPointer r1
    //     0xa59c8c: add             x1, x1, HEAP, lsl #32
    // 0xa59c90: mov             x0, x1
    // 0xa59c94: cmp             w0, NULL
    // 0xa59c98: b.ne            #0xa59ca4
    // 0xa59c9c: r2 = ""
    //     0xa59c9c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa59ca0: b               #0xa59ca8
    // 0xa59ca4: mov             x2, x0
    // 0xa59ca8: mov             x1, x3
    // 0xa59cac: r0 = _buildNetworkImage()
    //     0xa59cac: bl              #0xa59fb8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage
    // 0xa59cb0: stur            x0, [fp, #-0x20]
    // 0xa59cb4: r0 = SizedBox()
    //     0xa59cb4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa59cb8: mov             x1, x0
    // 0xa59cbc: r0 = inf
    //     0xa59cbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa59cc0: ldr             x0, [x0, #0x9f8]
    // 0xa59cc4: stur            x1, [fp, #-0x28]
    // 0xa59cc8: StoreField: r1->field_f = r0
    //     0xa59cc8: stur            w0, [x1, #0xf]
    // 0xa59ccc: StoreField: r1->field_13 = r0
    //     0xa59ccc: stur            w0, [x1, #0x13]
    // 0xa59cd0: ldur            x0, [fp, #-0x20]
    // 0xa59cd4: StoreField: r1->field_b = r0
    //     0xa59cd4: stur            w0, [x1, #0xb]
    // 0xa59cd8: r0 = InkWell()
    //     0xa59cd8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa59cdc: mov             x3, x0
    // 0xa59ce0: ldur            x0, [fp, #-0x28]
    // 0xa59ce4: stur            x3, [fp, #-0x20]
    // 0xa59ce8: StoreField: r3->field_b = r0
    //     0xa59ce8: stur            w0, [x3, #0xb]
    // 0xa59cec: ldur            x2, [fp, #-0x18]
    // 0xa59cf0: r1 = Function '<anonymous closure>':.
    //     0xa59cf0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52958] AnonymousClosure: (0xa5a38c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildImageItem (0xa59be0)
    //     0xa59cf4: ldr             x1, [x1, #0x958]
    // 0xa59cf8: r0 = AllocateClosure()
    //     0xa59cf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa59cfc: mov             x1, x0
    // 0xa59d00: ldur            x0, [fp, #-0x20]
    // 0xa59d04: StoreField: r0->field_f = r1
    //     0xa59d04: stur            w1, [x0, #0xf]
    // 0xa59d08: r1 = true
    //     0xa59d08: add             x1, NULL, #0x20  ; true
    // 0xa59d0c: StoreField: r0->field_43 = r1
    //     0xa59d0c: stur            w1, [x0, #0x43]
    // 0xa59d10: r2 = Instance_BoxShape
    //     0xa59d10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa59d14: ldr             x2, [x2, #0x80]
    // 0xa59d18: StoreField: r0->field_47 = r2
    //     0xa59d18: stur            w2, [x0, #0x47]
    // 0xa59d1c: StoreField: r0->field_6f = r1
    //     0xa59d1c: stur            w1, [x0, #0x6f]
    // 0xa59d20: r2 = false
    //     0xa59d20: add             x2, NULL, #0x30  ; false
    // 0xa59d24: StoreField: r0->field_73 = r2
    //     0xa59d24: stur            w2, [x0, #0x73]
    // 0xa59d28: StoreField: r0->field_83 = r1
    //     0xa59d28: stur            w1, [x0, #0x83]
    // 0xa59d2c: StoreField: r0->field_7b = r2
    //     0xa59d2c: stur            w2, [x0, #0x7b]
    // 0xa59d30: r1 = Null
    //     0xa59d30: mov             x1, NULL
    // 0xa59d34: r2 = 2
    //     0xa59d34: movz            x2, #0x2
    // 0xa59d38: r0 = AllocateArray()
    //     0xa59d38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa59d3c: mov             x2, x0
    // 0xa59d40: ldur            x0, [fp, #-0x20]
    // 0xa59d44: stur            x2, [fp, #-0x18]
    // 0xa59d48: StoreField: r2->field_f = r0
    //     0xa59d48: stur            w0, [x2, #0xf]
    // 0xa59d4c: r1 = <Widget>
    //     0xa59d4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa59d50: r0 = AllocateGrowableArray()
    //     0xa59d50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa59d54: mov             x2, x0
    // 0xa59d58: ldur            x0, [fp, #-0x18]
    // 0xa59d5c: stur            x2, [fp, #-0x20]
    // 0xa59d60: StoreField: r2->field_f = r0
    //     0xa59d60: stur            w0, [x2, #0xf]
    // 0xa59d64: r0 = 2
    //     0xa59d64: movz            x0, #0x2
    // 0xa59d68: StoreField: r2->field_b = r0
    //     0xa59d68: stur            w0, [x2, #0xb]
    // 0xa59d6c: ldur            x1, [fp, #-8]
    // 0xa59d70: LoadField: r0 = r1->field_b
    //     0xa59d70: ldur            w0, [x1, #0xb]
    // 0xa59d74: DecompressPointer r0
    //     0xa59d74: add             x0, x0, HEAP, lsl #32
    // 0xa59d78: cmp             w0, NULL
    // 0xa59d7c: b.eq            #0xa59e58
    // 0xa59d80: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa59d80: ldur            w3, [x0, #0x17]
    // 0xa59d84: DecompressPointer r3
    //     0xa59d84: add             x3, x3, HEAP, lsl #32
    // 0xa59d88: tbnz            w3, #4, #0xa59e10
    // 0xa59d8c: r0 = _buildCustomizableBadge()
    //     0xa59d8c: bl              #0xa59e5c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCustomizableBadge
    // 0xa59d90: mov             x2, x0
    // 0xa59d94: ldur            x0, [fp, #-0x20]
    // 0xa59d98: stur            x2, [fp, #-8]
    // 0xa59d9c: LoadField: r1 = r0->field_b
    //     0xa59d9c: ldur            w1, [x0, #0xb]
    // 0xa59da0: LoadField: r3 = r0->field_f
    //     0xa59da0: ldur            w3, [x0, #0xf]
    // 0xa59da4: DecompressPointer r3
    //     0xa59da4: add             x3, x3, HEAP, lsl #32
    // 0xa59da8: LoadField: r4 = r3->field_b
    //     0xa59da8: ldur            w4, [x3, #0xb]
    // 0xa59dac: r3 = LoadInt32Instr(r1)
    //     0xa59dac: sbfx            x3, x1, #1, #0x1f
    // 0xa59db0: stur            x3, [fp, #-0x10]
    // 0xa59db4: r1 = LoadInt32Instr(r4)
    //     0xa59db4: sbfx            x1, x4, #1, #0x1f
    // 0xa59db8: cmp             x3, x1
    // 0xa59dbc: b.ne            #0xa59dc8
    // 0xa59dc0: mov             x1, x0
    // 0xa59dc4: r0 = _growToNextCapacity()
    //     0xa59dc4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa59dc8: ldur            x2, [fp, #-0x20]
    // 0xa59dcc: ldur            x3, [fp, #-0x10]
    // 0xa59dd0: add             x0, x3, #1
    // 0xa59dd4: lsl             x1, x0, #1
    // 0xa59dd8: StoreField: r2->field_b = r1
    //     0xa59dd8: stur            w1, [x2, #0xb]
    // 0xa59ddc: LoadField: r1 = r2->field_f
    //     0xa59ddc: ldur            w1, [x2, #0xf]
    // 0xa59de0: DecompressPointer r1
    //     0xa59de0: add             x1, x1, HEAP, lsl #32
    // 0xa59de4: ldur            x0, [fp, #-8]
    // 0xa59de8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa59de8: add             x25, x1, x3, lsl #2
    //     0xa59dec: add             x25, x25, #0xf
    //     0xa59df0: str             w0, [x25]
    //     0xa59df4: tbz             w0, #0, #0xa59e10
    //     0xa59df8: ldurb           w16, [x1, #-1]
    //     0xa59dfc: ldurb           w17, [x0, #-1]
    //     0xa59e00: and             x16, x17, x16, lsr #2
    //     0xa59e04: tst             x16, HEAP, lsr #32
    //     0xa59e08: b.eq            #0xa59e10
    //     0xa59e0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa59e10: r0 = Stack()
    //     0xa59e10: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa59e14: r1 = Instance_Alignment
    //     0xa59e14: add             x1, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa59e18: ldr             x1, [x1, #0x5b8]
    // 0xa59e1c: StoreField: r0->field_f = r1
    //     0xa59e1c: stur            w1, [x0, #0xf]
    // 0xa59e20: r1 = Instance_StackFit
    //     0xa59e20: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa59e24: ldr             x1, [x1, #0xfa8]
    // 0xa59e28: ArrayStore: r0[0] = r1  ; List_4
    //     0xa59e28: stur            w1, [x0, #0x17]
    // 0xa59e2c: r1 = Instance_Clip
    //     0xa59e2c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa59e30: ldr             x1, [x1, #0x7e0]
    // 0xa59e34: StoreField: r0->field_1b = r1
    //     0xa59e34: stur            w1, [x0, #0x1b]
    // 0xa59e38: ldur            x1, [fp, #-0x20]
    // 0xa59e3c: StoreField: r0->field_b = r1
    //     0xa59e3c: stur            w1, [x0, #0xb]
    // 0xa59e40: LeaveFrame
    //     0xa59e40: mov             SP, fp
    //     0xa59e44: ldp             fp, lr, [SP], #0x10
    // 0xa59e48: ret
    //     0xa59e48: ret             
    // 0xa59e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59e4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59e50: b               #0xa59c08
    // 0xa59e54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa59e54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa59e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa59e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizableBadge(/* No info */) {
    // ** addr: 0xa59e5c, size: 0x15c
    // 0xa59e5c: EnterFrame
    //     0xa59e5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa59e60: mov             fp, SP
    // 0xa59e64: AllocStack(0x38)
    //     0xa59e64: sub             SP, SP, #0x38
    // 0xa59e68: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0xa59e68: mov             x0, x1
    //     0xa59e6c: stur            x1, [fp, #-8]
    // 0xa59e70: CheckStackOverflow
    //     0xa59e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59e74: cmp             SP, x16
    //     0xa59e78: b.ls            #0xa59fac
    // 0xa59e7c: r1 = Instance_Color
    //     0xa59e7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa59e80: d0 = 0.700000
    //     0xa59e80: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa59e84: ldr             d0, [x17, #0xf48]
    // 0xa59e88: r0 = withOpacity()
    //     0xa59e88: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa59e8c: stur            x0, [fp, #-0x10]
    // 0xa59e90: r0 = Radius()
    //     0xa59e90: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa59e94: d0 = 4.000000
    //     0xa59e94: fmov            d0, #4.00000000
    // 0xa59e98: stur            x0, [fp, #-0x18]
    // 0xa59e9c: StoreField: r0->field_7 = d0
    //     0xa59e9c: stur            d0, [x0, #7]
    // 0xa59ea0: StoreField: r0->field_f = d0
    //     0xa59ea0: stur            d0, [x0, #0xf]
    // 0xa59ea4: r0 = BorderRadius()
    //     0xa59ea4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa59ea8: mov             x1, x0
    // 0xa59eac: ldur            x0, [fp, #-0x18]
    // 0xa59eb0: stur            x1, [fp, #-0x20]
    // 0xa59eb4: StoreField: r1->field_7 = r0
    //     0xa59eb4: stur            w0, [x1, #7]
    // 0xa59eb8: StoreField: r1->field_b = r0
    //     0xa59eb8: stur            w0, [x1, #0xb]
    // 0xa59ebc: StoreField: r1->field_f = r0
    //     0xa59ebc: stur            w0, [x1, #0xf]
    // 0xa59ec0: StoreField: r1->field_13 = r0
    //     0xa59ec0: stur            w0, [x1, #0x13]
    // 0xa59ec4: r0 = BoxDecoration()
    //     0xa59ec4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa59ec8: mov             x2, x0
    // 0xa59ecc: ldur            x0, [fp, #-0x10]
    // 0xa59ed0: stur            x2, [fp, #-0x18]
    // 0xa59ed4: StoreField: r2->field_7 = r0
    //     0xa59ed4: stur            w0, [x2, #7]
    // 0xa59ed8: ldur            x0, [fp, #-0x20]
    // 0xa59edc: StoreField: r2->field_13 = r0
    //     0xa59edc: stur            w0, [x2, #0x13]
    // 0xa59ee0: r0 = Instance_BoxShape
    //     0xa59ee0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa59ee4: ldr             x0, [x0, #0x80]
    // 0xa59ee8: StoreField: r2->field_23 = r0
    //     0xa59ee8: stur            w0, [x2, #0x23]
    // 0xa59eec: ldur            x0, [fp, #-8]
    // 0xa59ef0: LoadField: r1 = r0->field_f
    //     0xa59ef0: ldur            w1, [x0, #0xf]
    // 0xa59ef4: DecompressPointer r1
    //     0xa59ef4: add             x1, x1, HEAP, lsl #32
    // 0xa59ef8: cmp             w1, NULL
    // 0xa59efc: b.eq            #0xa59fb4
    // 0xa59f00: r0 = of()
    //     0xa59f00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa59f04: LoadField: r1 = r0->field_87
    //     0xa59f04: ldur            w1, [x0, #0x87]
    // 0xa59f08: DecompressPointer r1
    //     0xa59f08: add             x1, x1, HEAP, lsl #32
    // 0xa59f0c: LoadField: r0 = r1->field_2b
    //     0xa59f0c: ldur            w0, [x1, #0x2b]
    // 0xa59f10: DecompressPointer r0
    //     0xa59f10: add             x0, x0, HEAP, lsl #32
    // 0xa59f14: r16 = 14.000000
    //     0xa59f14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa59f18: ldr             x16, [x16, #0x1d8]
    // 0xa59f1c: r30 = Instance_Color
    //     0xa59f1c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa59f20: stp             lr, x16, [SP]
    // 0xa59f24: mov             x1, x0
    // 0xa59f28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa59f28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa59f2c: ldr             x4, [x4, #0xaa0]
    // 0xa59f30: r0 = copyWith()
    //     0xa59f30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa59f34: stur            x0, [fp, #-8]
    // 0xa59f38: r0 = Text()
    //     0xa59f38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa59f3c: mov             x1, x0
    // 0xa59f40: r0 = "Customisable"
    //     0xa59f40: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xa59f44: ldr             x0, [x0, #0x970]
    // 0xa59f48: stur            x1, [fp, #-0x10]
    // 0xa59f4c: StoreField: r1->field_b = r0
    //     0xa59f4c: stur            w0, [x1, #0xb]
    // 0xa59f50: ldur            x0, [fp, #-8]
    // 0xa59f54: StoreField: r1->field_13 = r0
    //     0xa59f54: stur            w0, [x1, #0x13]
    // 0xa59f58: r0 = Container()
    //     0xa59f58: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa59f5c: stur            x0, [fp, #-8]
    // 0xa59f60: r16 = Instance_EdgeInsets
    //     0xa59f60: add             x16, PP, #0x52, lsl #12  ; [pp+0x52978] Obj!EdgeInsets@d58731
    //     0xa59f64: ldr             x16, [x16, #0x978]
    // 0xa59f68: ldur            lr, [fp, #-0x18]
    // 0xa59f6c: stp             lr, x16, [SP, #8]
    // 0xa59f70: ldur            x16, [fp, #-0x10]
    // 0xa59f74: str             x16, [SP]
    // 0xa59f78: mov             x1, x0
    // 0xa59f7c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xa59f7c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xa59f80: ldr             x4, [x4, #0x610]
    // 0xa59f84: r0 = Container()
    //     0xa59f84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa59f88: r0 = Padding()
    //     0xa59f88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa59f8c: r1 = Instance_EdgeInsets
    //     0xa59f8c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52980] Obj!EdgeInsets@d58701
    //     0xa59f90: ldr             x1, [x1, #0x980]
    // 0xa59f94: StoreField: r0->field_f = r1
    //     0xa59f94: stur            w1, [x0, #0xf]
    // 0xa59f98: ldur            x1, [fp, #-8]
    // 0xa59f9c: StoreField: r0->field_b = r1
    //     0xa59f9c: stur            w1, [x0, #0xb]
    // 0xa59fa0: LeaveFrame
    //     0xa59fa0: mov             SP, fp
    //     0xa59fa4: ldp             fp, lr, [SP], #0x10
    // 0xa59fa8: ret
    //     0xa59fa8: ret             
    // 0xa59fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa59fac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa59fb0: b               #0xa59e7c
    // 0xa59fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa59fb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildNetworkImage(/* No info */) {
    // ** addr: 0xa59fb8, size: 0x1ac
    // 0xa59fb8: EnterFrame
    //     0xa59fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xa59fbc: mov             fp, SP
    // 0xa59fc0: AllocStack(0x28)
    //     0xa59fc0: sub             SP, SP, #0x28
    // 0xa59fc4: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa59fc4: stur            x1, [fp, #-8]
    //     0xa59fc8: stur            x2, [fp, #-0x10]
    // 0xa59fcc: CheckStackOverflow
    //     0xa59fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa59fd0: cmp             SP, x16
    //     0xa59fd4: b.ls            #0xa5a158
    // 0xa59fd8: r1 = 1
    //     0xa59fd8: movz            x1, #0x1
    // 0xa59fdc: r0 = AllocateContext()
    //     0xa59fdc: bl              #0x16f6108  ; AllocateContextStub
    // 0xa59fe0: mov             x3, x0
    // 0xa59fe4: ldur            x0, [fp, #-8]
    // 0xa59fe8: stur            x3, [fp, #-0x20]
    // 0xa59fec: StoreField: r3->field_f = r0
    //     0xa59fec: stur            w0, [x3, #0xf]
    // 0xa59ff0: ldur            x4, [fp, #-0x10]
    // 0xa59ff4: LoadField: r1 = r4->field_7
    //     0xa59ff4: ldur            w1, [x4, #7]
    // 0xa59ff8: cbnz            w1, #0xa5a010
    // 0xa59ffc: r0 = Instance_SizedBox
    //     0xa59ffc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52988] Obj!SizedBox@d67fc1
    //     0xa5a000: ldr             x0, [x0, #0x988]
    // 0xa5a004: LeaveFrame
    //     0xa5a004: mov             SP, fp
    //     0xa5a008: ldp             fp, lr, [SP], #0x10
    // 0xa5a00c: ret
    //     0xa5a00c: ret             
    // 0xa5a010: LoadField: r5 = r0->field_27
    //     0xa5a010: ldur            w5, [x0, #0x27]
    // 0xa5a014: DecompressPointer r5
    //     0xa5a014: add             x5, x5, HEAP, lsl #32
    // 0xa5a018: mov             x1, x5
    // 0xa5a01c: mov             x2, x4
    // 0xa5a020: stur            x5, [fp, #-0x18]
    // 0xa5a024: r0 = _getValueOrData()
    //     0xa5a024: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa5a028: mov             x1, x0
    // 0xa5a02c: ldur            x0, [fp, #-0x18]
    // 0xa5a030: LoadField: r2 = r0->field_f
    //     0xa5a030: ldur            w2, [x0, #0xf]
    // 0xa5a034: DecompressPointer r2
    //     0xa5a034: add             x2, x2, HEAP, lsl #32
    // 0xa5a038: cmp             w2, w1
    // 0xa5a03c: b.eq            #0xa5a060
    // 0xa5a040: cmp             w1, NULL
    // 0xa5a044: b.eq            #0xa5a060
    // 0xa5a048: ldur            x1, [fp, #-8]
    // 0xa5a04c: ldur            x2, [fp, #-0x10]
    // 0xa5a050: r0 = _buildCachedImage()
    //     0xa5a050: bl              #0xa5a214  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage
    // 0xa5a054: LeaveFrame
    //     0xa5a054: mov             SP, fp
    //     0xa5a058: ldp             fp, lr, [SP], #0x10
    // 0xa5a05c: ret
    //     0xa5a05c: ret             
    // 0xa5a060: r0 = LoadStaticField(0x878)
    //     0xa5a060: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa5a064: ldr             x0, [x0, #0x10f0]
    // 0xa5a068: cmp             w0, NULL
    // 0xa5a06c: b.eq            #0xa5a160
    // 0xa5a070: LoadField: r3 = r0->field_53
    //     0xa5a070: ldur            w3, [x0, #0x53]
    // 0xa5a074: DecompressPointer r3
    //     0xa5a074: add             x3, x3, HEAP, lsl #32
    // 0xa5a078: stur            x3, [fp, #-0x18]
    // 0xa5a07c: LoadField: r0 = r3->field_7
    //     0xa5a07c: ldur            w0, [x3, #7]
    // 0xa5a080: DecompressPointer r0
    //     0xa5a080: add             x0, x0, HEAP, lsl #32
    // 0xa5a084: ldur            x2, [fp, #-0x20]
    // 0xa5a088: stur            x0, [fp, #-0x10]
    // 0xa5a08c: r1 = Function '<anonymous closure>':.
    //     0xa5a08c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52990] AnonymousClosure: (0xa5a328), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildNetworkImage (0xa59fb8)
    //     0xa5a090: ldr             x1, [x1, #0x990]
    // 0xa5a094: r0 = AllocateClosure()
    //     0xa5a094: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5a098: ldur            x2, [fp, #-0x10]
    // 0xa5a09c: mov             x3, x0
    // 0xa5a0a0: r1 = Null
    //     0xa5a0a0: mov             x1, NULL
    // 0xa5a0a4: stur            x3, [fp, #-0x10]
    // 0xa5a0a8: cmp             w2, NULL
    // 0xa5a0ac: b.eq            #0xa5a0cc
    // 0xa5a0b0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa5a0b0: ldur            w4, [x2, #0x17]
    // 0xa5a0b4: DecompressPointer r4
    //     0xa5a0b4: add             x4, x4, HEAP, lsl #32
    // 0xa5a0b8: r8 = X0
    //     0xa5a0b8: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xa5a0bc: LoadField: r9 = r4->field_7
    //     0xa5a0bc: ldur            x9, [x4, #7]
    // 0xa5a0c0: r3 = Null
    //     0xa5a0c0: add             x3, PP, #0x52, lsl #12  ; [pp+0x52998] Null
    //     0xa5a0c4: ldr             x3, [x3, #0x998]
    // 0xa5a0c8: blr             x9
    // 0xa5a0cc: ldur            x0, [fp, #-0x18]
    // 0xa5a0d0: LoadField: r1 = r0->field_b
    //     0xa5a0d0: ldur            w1, [x0, #0xb]
    // 0xa5a0d4: LoadField: r2 = r0->field_f
    //     0xa5a0d4: ldur            w2, [x0, #0xf]
    // 0xa5a0d8: DecompressPointer r2
    //     0xa5a0d8: add             x2, x2, HEAP, lsl #32
    // 0xa5a0dc: LoadField: r3 = r2->field_b
    //     0xa5a0dc: ldur            w3, [x2, #0xb]
    // 0xa5a0e0: r2 = LoadInt32Instr(r1)
    //     0xa5a0e0: sbfx            x2, x1, #1, #0x1f
    // 0xa5a0e4: stur            x2, [fp, #-0x28]
    // 0xa5a0e8: r1 = LoadInt32Instr(r3)
    //     0xa5a0e8: sbfx            x1, x3, #1, #0x1f
    // 0xa5a0ec: cmp             x2, x1
    // 0xa5a0f0: b.ne            #0xa5a0fc
    // 0xa5a0f4: mov             x1, x0
    // 0xa5a0f8: r0 = _growToNextCapacity()
    //     0xa5a0f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa5a0fc: ldur            x0, [fp, #-0x18]
    // 0xa5a100: ldur            x2, [fp, #-0x28]
    // 0xa5a104: add             x1, x2, #1
    // 0xa5a108: lsl             x3, x1, #1
    // 0xa5a10c: StoreField: r0->field_b = r3
    //     0xa5a10c: stur            w3, [x0, #0xb]
    // 0xa5a110: LoadField: r1 = r0->field_f
    //     0xa5a110: ldur            w1, [x0, #0xf]
    // 0xa5a114: DecompressPointer r1
    //     0xa5a114: add             x1, x1, HEAP, lsl #32
    // 0xa5a118: ldur            x0, [fp, #-0x10]
    // 0xa5a11c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa5a11c: add             x25, x1, x2, lsl #2
    //     0xa5a120: add             x25, x25, #0xf
    //     0xa5a124: str             w0, [x25]
    //     0xa5a128: tbz             w0, #0, #0xa5a144
    //     0xa5a12c: ldurb           w16, [x1, #-1]
    //     0xa5a130: ldurb           w17, [x0, #-1]
    //     0xa5a134: and             x16, x17, x16, lsr #2
    //     0xa5a138: tst             x16, HEAP, lsr #32
    //     0xa5a13c: b.eq            #0xa5a144
    //     0xa5a140: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa5a144: ldur            x1, [fp, #-8]
    // 0xa5a148: r0 = _buildProgressIndicator()
    //     0xa5a148: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xa5a14c: LeaveFrame
    //     0xa5a14c: mov             SP, fp
    //     0xa5a150: ldp             fp, lr, [SP], #0x10
    // 0xa5a154: ret
    //     0xa5a154: ret             
    // 0xa5a158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5a158: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5a15c: b               #0xa59fd8
    // 0xa5a160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5a160: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCachedImage(/* No info */) {
    // ** addr: 0xa5a214, size: 0xd0
    // 0xa5a214: EnterFrame
    //     0xa5a214: stp             fp, lr, [SP, #-0x10]!
    //     0xa5a218: mov             fp, SP
    // 0xa5a21c: AllocStack(0x48)
    //     0xa5a21c: sub             SP, SP, #0x48
    // 0xa5a220: SetupParameters(_ProductMediaCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa5a220: stur            x1, [fp, #-8]
    //     0xa5a224: stur            x2, [fp, #-0x10]
    // 0xa5a228: CheckStackOverflow
    //     0xa5a228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5a22c: cmp             SP, x16
    //     0xa5a230: b.ls            #0xa5a2dc
    // 0xa5a234: r1 = 1
    //     0xa5a234: movz            x1, #0x1
    // 0xa5a238: r0 = AllocateContext()
    //     0xa5a238: bl              #0x16f6108  ; AllocateContextStub
    // 0xa5a23c: mov             x1, x0
    // 0xa5a240: ldur            x0, [fp, #-8]
    // 0xa5a244: stur            x1, [fp, #-0x18]
    // 0xa5a248: StoreField: r1->field_f = r0
    //     0xa5a248: stur            w0, [x1, #0xf]
    // 0xa5a24c: r0 = ImageHeaders.forImages()
    //     0xa5a24c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa5a250: stur            x0, [fp, #-8]
    // 0xa5a254: r0 = CachedNetworkImage()
    //     0xa5a254: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa5a258: ldur            x2, [fp, #-0x18]
    // 0xa5a25c: r1 = Function '<anonymous closure>':.
    //     0xa5a25c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52a90] AnonymousClosure: (0xa5a2e4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCachedImage (0xa5a214)
    //     0xa5a260: ldr             x1, [x1, #0xa90]
    // 0xa5a264: stur            x0, [fp, #-0x18]
    // 0xa5a268: r0 = AllocateClosure()
    //     0xa5a268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5a26c: r1 = Function '<anonymous closure>':.
    //     0xa5a26c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52a98] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa5a270: ldr             x1, [x1, #0xa98]
    // 0xa5a274: r2 = Null
    //     0xa5a274: mov             x2, NULL
    // 0xa5a278: stur            x0, [fp, #-0x20]
    // 0xa5a27c: r0 = AllocateClosure()
    //     0xa5a27c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5a280: ldur            x16, [fp, #-8]
    // 0xa5a284: r30 = inf
    //     0xa5a284: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa5a288: ldr             lr, [lr, #0x9f8]
    // 0xa5a28c: stp             lr, x16, [SP, #0x18]
    // 0xa5a290: r16 = Instance_BoxFit
    //     0xa5a290: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d7d0] Obj!BoxFit@d73881
    //     0xa5a294: ldr             x16, [x16, #0x7d0]
    // 0xa5a298: ldur            lr, [fp, #-0x20]
    // 0xa5a29c: stp             lr, x16, [SP, #8]
    // 0xa5a2a0: str             x0, [SP]
    // 0xa5a2a4: ldur            x1, [fp, #-0x18]
    // 0xa5a2a8: ldur            x2, [fp, #-0x10]
    // 0xa5a2ac: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, httpHeaders, 0x2, placeholder, 0x5, width, 0x3, null]
    //     0xa5a2ac: add             x4, PP, #0x52, lsl #12  ; [pp+0x52aa0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "httpHeaders", 0x2, "placeholder", 0x5, "width", 0x3, Null]
    //     0xa5a2b0: ldr             x4, [x4, #0xaa0]
    // 0xa5a2b4: r0 = CachedNetworkImage()
    //     0xa5a2b4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa5a2b8: r0 = Center()
    //     0xa5a2b8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa5a2bc: r1 = Instance_Alignment
    //     0xa5a2bc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa5a2c0: ldr             x1, [x1, #0xb10]
    // 0xa5a2c4: StoreField: r0->field_f = r1
    //     0xa5a2c4: stur            w1, [x0, #0xf]
    // 0xa5a2c8: ldur            x1, [fp, #-0x18]
    // 0xa5a2cc: StoreField: r0->field_b = r1
    //     0xa5a2cc: stur            w1, [x0, #0xb]
    // 0xa5a2d0: LeaveFrame
    //     0xa5a2d0: mov             SP, fp
    //     0xa5a2d4: ldp             fp, lr, [SP], #0x10
    // 0xa5a2d8: ret
    //     0xa5a2d8: ret             
    // 0xa5a2dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5a2dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5a2e0: b               #0xa5a234
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, String) {
    // ** addr: 0xa5a2e4, size: 0x44
    // 0xa5a2e4: EnterFrame
    //     0xa5a2e4: stp             fp, lr, [SP, #-0x10]!
    //     0xa5a2e8: mov             fp, SP
    // 0xa5a2ec: ldr             x0, [fp, #0x20]
    // 0xa5a2f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa5a2f0: ldur            w1, [x0, #0x17]
    // 0xa5a2f4: DecompressPointer r1
    //     0xa5a2f4: add             x1, x1, HEAP, lsl #32
    // 0xa5a2f8: CheckStackOverflow
    //     0xa5a2f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5a2fc: cmp             SP, x16
    //     0xa5a300: b.ls            #0xa5a320
    // 0xa5a304: LoadField: r0 = r1->field_f
    //     0xa5a304: ldur            w0, [x1, #0xf]
    // 0xa5a308: DecompressPointer r0
    //     0xa5a308: add             x0, x0, HEAP, lsl #32
    // 0xa5a30c: mov             x1, x0
    // 0xa5a310: r0 = _buildProgressIndicator()
    //     0xa5a310: bl              #0xa5a164  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildProgressIndicator
    // 0xa5a314: LeaveFrame
    //     0xa5a314: mov             SP, fp
    //     0xa5a318: ldp             fp, lr, [SP], #0x10
    // 0xa5a31c: ret
    //     0xa5a31c: ret             
    // 0xa5a320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5a320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5a324: b               #0xa5a304
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xa5a328, size: 0x64
    // 0xa5a328: EnterFrame
    //     0xa5a328: stp             fp, lr, [SP, #-0x10]!
    //     0xa5a32c: mov             fp, SP
    // 0xa5a330: ldr             x0, [fp, #0x18]
    // 0xa5a334: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa5a334: ldur            w1, [x0, #0x17]
    // 0xa5a338: DecompressPointer r1
    //     0xa5a338: add             x1, x1, HEAP, lsl #32
    // 0xa5a33c: CheckStackOverflow
    //     0xa5a33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5a340: cmp             SP, x16
    //     0xa5a344: b.ls            #0xa5a384
    // 0xa5a348: LoadField: r0 = r1->field_f
    //     0xa5a348: ldur            w0, [x1, #0xf]
    // 0xa5a34c: DecompressPointer r0
    //     0xa5a34c: add             x0, x0, HEAP, lsl #32
    // 0xa5a350: LoadField: r1 = r0->field_2b
    //     0xa5a350: ldur            w1, [x0, #0x2b]
    // 0xa5a354: DecompressPointer r1
    //     0xa5a354: add             x1, x1, HEAP, lsl #32
    // 0xa5a358: tbz             w1, #4, #0xa5a374
    // 0xa5a35c: LoadField: r1 = r0->field_f
    //     0xa5a35c: ldur            w1, [x0, #0xf]
    // 0xa5a360: DecompressPointer r1
    //     0xa5a360: add             x1, x1, HEAP, lsl #32
    // 0xa5a364: cmp             w1, NULL
    // 0xa5a368: b.eq            #0xa5a374
    // 0xa5a36c: mov             x1, x0
    // 0xa5a370: r0 = _preloadImageSizes()
    //     0xa5a370: bl              #0x8075f0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_preloadImageSizes
    // 0xa5a374: r0 = Null
    //     0xa5a374: mov             x0, NULL
    // 0xa5a378: LeaveFrame
    //     0xa5a378: mov             SP, fp
    //     0xa5a37c: ldp             fp, lr, [SP], #0x10
    // 0xa5a380: ret
    //     0xa5a380: ret             
    // 0xa5a384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5a384: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5a388: b               #0xa5a348
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa5a38c, size: 0x90
    // 0xa5a38c: EnterFrame
    //     0xa5a38c: stp             fp, lr, [SP, #-0x10]!
    //     0xa5a390: mov             fp, SP
    // 0xa5a394: AllocStack(0x18)
    //     0xa5a394: sub             SP, SP, #0x18
    // 0xa5a398: SetupParameters()
    //     0xa5a398: ldr             x0, [fp, #0x10]
    //     0xa5a39c: ldur            w1, [x0, #0x17]
    //     0xa5a3a0: add             x1, x1, HEAP, lsl #32
    // 0xa5a3a4: CheckStackOverflow
    //     0xa5a3a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5a3a8: cmp             SP, x16
    //     0xa5a3ac: b.ls            #0xa5a410
    // 0xa5a3b0: LoadField: r0 = r1->field_f
    //     0xa5a3b0: ldur            w0, [x1, #0xf]
    // 0xa5a3b4: DecompressPointer r0
    //     0xa5a3b4: add             x0, x0, HEAP, lsl #32
    // 0xa5a3b8: LoadField: r2 = r0->field_b
    //     0xa5a3b8: ldur            w2, [x0, #0xb]
    // 0xa5a3bc: DecompressPointer r2
    //     0xa5a3bc: add             x2, x2, HEAP, lsl #32
    // 0xa5a3c0: cmp             w2, NULL
    // 0xa5a3c4: b.eq            #0xa5a418
    // 0xa5a3c8: LoadField: r3 = r1->field_13
    //     0xa5a3c8: ldur            w3, [x1, #0x13]
    // 0xa5a3cc: DecompressPointer r3
    //     0xa5a3cc: add             x3, x3, HEAP, lsl #32
    // 0xa5a3d0: LoadField: r1 = r0->field_23
    //     0xa5a3d0: ldur            w1, [x0, #0x23]
    // 0xa5a3d4: DecompressPointer r1
    //     0xa5a3d4: add             x1, x1, HEAP, lsl #32
    // 0xa5a3d8: LoadField: r0 = r2->field_23
    //     0xa5a3d8: ldur            w0, [x2, #0x23]
    // 0xa5a3dc: DecompressPointer r0
    //     0xa5a3dc: add             x0, x0, HEAP, lsl #32
    // 0xa5a3e0: stp             x3, x0, [SP, #8]
    // 0xa5a3e4: str             x1, [SP]
    // 0xa5a3e8: r4 = 0
    //     0xa5a3e8: movz            x4, #0
    // 0xa5a3ec: ldr             x0, [SP, #0x10]
    // 0xa5a3f0: r16 = UnlinkedCall_0x613b5c
    //     0xa5a3f0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52960] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa5a3f4: add             x16, x16, #0x960
    // 0xa5a3f8: ldp             x5, lr, [x16]
    // 0xa5a3fc: blr             lr
    // 0xa5a400: r0 = Null
    //     0xa5a400: mov             x0, NULL
    // 0xa5a404: LeaveFrame
    //     0xa5a404: mov             SP, fp
    //     0xa5a408: ldp             fp, lr, [SP], #0x10
    // 0xa5a40c: ret
    //     0xa5a40c: ret             
    // 0xa5a410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5a410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5a414: b               #0xa5a3b0
    // 0xa5a418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5a418: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc04ab8, size: 0x124
    // 0xc04ab8: EnterFrame
    //     0xc04ab8: stp             fp, lr, [SP, #-0x10]!
    //     0xc04abc: mov             fp, SP
    // 0xc04ac0: AllocStack(0x18)
    //     0xc04ac0: sub             SP, SP, #0x18
    // 0xc04ac4: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc04ac4: mov             x3, x1
    //     0xc04ac8: mov             x0, x2
    //     0xc04acc: stur            x1, [fp, #-8]
    //     0xc04ad0: stur            x2, [fp, #-0x10]
    // 0xc04ad4: CheckStackOverflow
    //     0xc04ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04ad8: cmp             SP, x16
    //     0xc04adc: b.ls            #0xc04bd4
    // 0xc04ae0: mov             x1, x3
    // 0xc04ae4: mov             x2, x0
    // 0xc04ae8: r0 = build()
    //     0xc04ae8: bl              #0xc050b8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] __ProductMediaCarouselState&State&AutomaticKeepAliveClientMixin::build
    // 0xc04aec: ldur            x0, [fp, #-8]
    // 0xc04af0: LoadField: r1 = r0->field_23
    //     0xc04af0: ldur            w1, [x0, #0x23]
    // 0xc04af4: DecompressPointer r1
    //     0xc04af4: add             x1, x1, HEAP, lsl #32
    // 0xc04af8: LoadField: r2 = r1->field_b
    //     0xc04af8: ldur            w2, [x1, #0xb]
    // 0xc04afc: cbnz            w2, #0xc04b10
    // 0xc04b00: r0 = Instance_SizedBox
    //     0xc04b00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc04b04: LeaveFrame
    //     0xc04b04: mov             SP, fp
    //     0xc04b08: ldp             fp, lr, [SP], #0x10
    // 0xc04b0c: ret
    //     0xc04b0c: ret             
    // 0xc04b10: mov             x1, x0
    // 0xc04b14: ldur            x2, [fp, #-0x10]
    // 0xc04b18: r0 = _buildCarouselSection()
    //     0xc04b18: bl              #0xc04c9c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildCarouselSection
    // 0xc04b1c: ldur            x1, [fp, #-8]
    // 0xc04b20: ldur            x2, [fp, #-0x10]
    // 0xc04b24: stur            x0, [fp, #-8]
    // 0xc04b28: r0 = _buildIndicator()
    //     0xc04b28: bl              #0xc04bdc  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildIndicator
    // 0xc04b2c: r1 = Null
    //     0xc04b2c: mov             x1, NULL
    // 0xc04b30: r2 = 4
    //     0xc04b30: movz            x2, #0x4
    // 0xc04b34: stur            x0, [fp, #-0x10]
    // 0xc04b38: r0 = AllocateArray()
    //     0xc04b38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc04b3c: mov             x2, x0
    // 0xc04b40: ldur            x0, [fp, #-8]
    // 0xc04b44: stur            x2, [fp, #-0x18]
    // 0xc04b48: StoreField: r2->field_f = r0
    //     0xc04b48: stur            w0, [x2, #0xf]
    // 0xc04b4c: ldur            x0, [fp, #-0x10]
    // 0xc04b50: StoreField: r2->field_13 = r0
    //     0xc04b50: stur            w0, [x2, #0x13]
    // 0xc04b54: r1 = <Widget>
    //     0xc04b54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc04b58: r0 = AllocateGrowableArray()
    //     0xc04b58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc04b5c: mov             x1, x0
    // 0xc04b60: ldur            x0, [fp, #-0x18]
    // 0xc04b64: stur            x1, [fp, #-8]
    // 0xc04b68: StoreField: r1->field_f = r0
    //     0xc04b68: stur            w0, [x1, #0xf]
    // 0xc04b6c: r0 = 4
    //     0xc04b6c: movz            x0, #0x4
    // 0xc04b70: StoreField: r1->field_b = r0
    //     0xc04b70: stur            w0, [x1, #0xb]
    // 0xc04b74: r0 = Column()
    //     0xc04b74: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc04b78: r1 = Instance_Axis
    //     0xc04b78: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc04b7c: StoreField: r0->field_f = r1
    //     0xc04b7c: stur            w1, [x0, #0xf]
    // 0xc04b80: r1 = Instance_MainAxisAlignment
    //     0xc04b80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc04b84: ldr             x1, [x1, #0xa08]
    // 0xc04b88: StoreField: r0->field_13 = r1
    //     0xc04b88: stur            w1, [x0, #0x13]
    // 0xc04b8c: r1 = Instance_MainAxisSize
    //     0xc04b8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc04b90: ldr             x1, [x1, #0xa10]
    // 0xc04b94: ArrayStore: r0[0] = r1  ; List_4
    //     0xc04b94: stur            w1, [x0, #0x17]
    // 0xc04b98: r1 = Instance_CrossAxisAlignment
    //     0xc04b98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc04b9c: ldr             x1, [x1, #0xa18]
    // 0xc04ba0: StoreField: r0->field_1b = r1
    //     0xc04ba0: stur            w1, [x0, #0x1b]
    // 0xc04ba4: r1 = Instance_VerticalDirection
    //     0xc04ba4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc04ba8: ldr             x1, [x1, #0xa20]
    // 0xc04bac: StoreField: r0->field_23 = r1
    //     0xc04bac: stur            w1, [x0, #0x23]
    // 0xc04bb0: r1 = Instance_Clip
    //     0xc04bb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc04bb4: ldr             x1, [x1, #0x38]
    // 0xc04bb8: StoreField: r0->field_2b = r1
    //     0xc04bb8: stur            w1, [x0, #0x2b]
    // 0xc04bbc: StoreField: r0->field_2f = rZR
    //     0xc04bbc: stur            xzr, [x0, #0x2f]
    // 0xc04bc0: ldur            x1, [fp, #-8]
    // 0xc04bc4: StoreField: r0->field_b = r1
    //     0xc04bc4: stur            w1, [x0, #0xb]
    // 0xc04bc8: LeaveFrame
    //     0xc04bc8: mov             SP, fp
    //     0xc04bcc: ldp             fp, lr, [SP], #0x10
    // 0xc04bd0: ret
    //     0xc04bd0: ret             
    // 0xc04bd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04bd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04bd8: b               #0xc04ae0
  }
  _ _buildIndicator(/* No info */) {
    // ** addr: 0xc04bdc, size: 0xc0
    // 0xc04bdc: EnterFrame
    //     0xc04bdc: stp             fp, lr, [SP, #-0x10]!
    //     0xc04be0: mov             fp, SP
    // 0xc04be4: AllocStack(0x20)
    //     0xc04be4: sub             SP, SP, #0x20
    // 0xc04be8: SetupParameters(_ProductMediaCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0xc04be8: mov             x0, x1
    //     0xc04bec: mov             x1, x2
    // 0xc04bf0: CheckStackOverflow
    //     0xc04bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04bf4: cmp             SP, x16
    //     0xc04bf8: b.ls            #0xc04c94
    // 0xc04bfc: LoadField: r2 = r0->field_23
    //     0xc04bfc: ldur            w2, [x0, #0x23]
    // 0xc04c00: DecompressPointer r2
    //     0xc04c00: add             x2, x2, HEAP, lsl #32
    // 0xc04c04: LoadField: r3 = r2->field_b
    //     0xc04c04: ldur            w3, [x2, #0xb]
    // 0xc04c08: r2 = LoadInt32Instr(r3)
    //     0xc04c08: sbfx            x2, x3, #1, #0x1f
    // 0xc04c0c: stur            x2, [fp, #-0x10]
    // 0xc04c10: cmp             x2, #1
    // 0xc04c14: b.gt            #0xc04c28
    // 0xc04c18: r0 = Instance_SizedBox
    //     0xc04c18: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc04c1c: LeaveFrame
    //     0xc04c1c: mov             SP, fp
    //     0xc04c20: ldp             fp, lr, [SP], #0x10
    // 0xc04c24: ret
    //     0xc04c24: ret             
    // 0xc04c28: LoadField: r3 = r0->field_1b
    //     0xc04c28: ldur            x3, [x0, #0x1b]
    // 0xc04c2c: stur            x3, [fp, #-8]
    // 0xc04c30: r0 = of()
    //     0xc04c30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc04c34: LoadField: r1 = r0->field_5b
    //     0xc04c34: ldur            w1, [x0, #0x5b]
    // 0xc04c38: DecompressPointer r1
    //     0xc04c38: add             x1, x1, HEAP, lsl #32
    // 0xc04c3c: stur            x1, [fp, #-0x18]
    // 0xc04c40: r0 = CarouselIndicator()
    //     0xc04c40: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xc04c44: mov             x1, x0
    // 0xc04c48: ldur            x0, [fp, #-0x10]
    // 0xc04c4c: stur            x1, [fp, #-0x20]
    // 0xc04c50: StoreField: r1->field_b = r0
    //     0xc04c50: stur            x0, [x1, #0xb]
    // 0xc04c54: ldur            x0, [fp, #-8]
    // 0xc04c58: StoreField: r1->field_13 = r0
    //     0xc04c58: stur            x0, [x1, #0x13]
    // 0xc04c5c: ldur            x0, [fp, #-0x18]
    // 0xc04c60: StoreField: r1->field_1b = r0
    //     0xc04c60: stur            w0, [x1, #0x1b]
    // 0xc04c64: r0 = Instance_Color
    //     0xc04c64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xc04c68: ldr             x0, [x0, #0x90]
    // 0xc04c6c: StoreField: r1->field_1f = r0
    //     0xc04c6c: stur            w0, [x1, #0x1f]
    // 0xc04c70: r0 = Padding()
    //     0xc04c70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04c74: r1 = Instance_EdgeInsets
    //     0xc04c74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc04c78: ldr             x1, [x1, #0xa00]
    // 0xc04c7c: StoreField: r0->field_f = r1
    //     0xc04c7c: stur            w1, [x0, #0xf]
    // 0xc04c80: ldur            x1, [fp, #-0x20]
    // 0xc04c84: StoreField: r0->field_b = r1
    //     0xc04c84: stur            w1, [x0, #0xb]
    // 0xc04c88: LeaveFrame
    //     0xc04c88: mov             SP, fp
    //     0xc04c8c: ldp             fp, lr, [SP], #0x10
    // 0xc04c90: ret
    //     0xc04c90: ret             
    // 0xc04c94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04c94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04c98: b               #0xc04bfc
  }
  _ _buildCarouselSection(/* No info */) {
    // ** addr: 0xc04c9c, size: 0xd0
    // 0xc04c9c: EnterFrame
    //     0xc04c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc04ca0: mov             fp, SP
    // 0xc04ca4: AllocStack(0x18)
    //     0xc04ca4: sub             SP, SP, #0x18
    // 0xc04ca8: SetupParameters(_ProductMediaCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc04ca8: mov             x3, x1
    //     0xc04cac: mov             x0, x2
    //     0xc04cb0: stur            x1, [fp, #-8]
    //     0xc04cb4: stur            x2, [fp, #-0x10]
    // 0xc04cb8: CheckStackOverflow
    //     0xc04cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04cbc: cmp             SP, x16
    //     0xc04cc0: b.ls            #0xc04d64
    // 0xc04cc4: mov             x1, x3
    // 0xc04cc8: mov             x2, x0
    // 0xc04ccc: r0 = _buildMediaCarousel()
    //     0xc04ccc: bl              #0xa598e8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel
    // 0xc04cd0: ldur            x1, [fp, #-8]
    // 0xc04cd4: ldur            x2, [fp, #-0x10]
    // 0xc04cd8: stur            x0, [fp, #-8]
    // 0xc04cdc: r0 = _buildFreeGiftBanner()
    //     0xc04cdc: bl              #0xc04d6c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftBanner
    // 0xc04ce0: r1 = Null
    //     0xc04ce0: mov             x1, NULL
    // 0xc04ce4: r2 = 4
    //     0xc04ce4: movz            x2, #0x4
    // 0xc04ce8: stur            x0, [fp, #-0x10]
    // 0xc04cec: r0 = AllocateArray()
    //     0xc04cec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc04cf0: mov             x2, x0
    // 0xc04cf4: ldur            x0, [fp, #-8]
    // 0xc04cf8: stur            x2, [fp, #-0x18]
    // 0xc04cfc: StoreField: r2->field_f = r0
    //     0xc04cfc: stur            w0, [x2, #0xf]
    // 0xc04d00: ldur            x0, [fp, #-0x10]
    // 0xc04d04: StoreField: r2->field_13 = r0
    //     0xc04d04: stur            w0, [x2, #0x13]
    // 0xc04d08: r1 = <Widget>
    //     0xc04d08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc04d0c: r0 = AllocateGrowableArray()
    //     0xc04d0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc04d10: mov             x1, x0
    // 0xc04d14: ldur            x0, [fp, #-0x18]
    // 0xc04d18: stur            x1, [fp, #-8]
    // 0xc04d1c: StoreField: r1->field_f = r0
    //     0xc04d1c: stur            w0, [x1, #0xf]
    // 0xc04d20: r0 = 4
    //     0xc04d20: movz            x0, #0x4
    // 0xc04d24: StoreField: r1->field_b = r0
    //     0xc04d24: stur            w0, [x1, #0xb]
    // 0xc04d28: r0 = Stack()
    //     0xc04d28: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc04d2c: r1 = Instance_Alignment
    //     0xc04d2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xc04d30: ldr             x1, [x1, #0x950]
    // 0xc04d34: StoreField: r0->field_f = r1
    //     0xc04d34: stur            w1, [x0, #0xf]
    // 0xc04d38: r1 = Instance_StackFit
    //     0xc04d38: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc04d3c: ldr             x1, [x1, #0xfa8]
    // 0xc04d40: ArrayStore: r0[0] = r1  ; List_4
    //     0xc04d40: stur            w1, [x0, #0x17]
    // 0xc04d44: r1 = Instance_Clip
    //     0xc04d44: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc04d48: ldr             x1, [x1, #0x7e0]
    // 0xc04d4c: StoreField: r0->field_1b = r1
    //     0xc04d4c: stur            w1, [x0, #0x1b]
    // 0xc04d50: ldur            x1, [fp, #-8]
    // 0xc04d54: StoreField: r0->field_b = r1
    //     0xc04d54: stur            w1, [x0, #0xb]
    // 0xc04d58: LeaveFrame
    //     0xc04d58: mov             SP, fp
    //     0xc04d5c: ldp             fp, lr, [SP], #0x10
    // 0xc04d60: ret
    //     0xc04d60: ret             
    // 0xc04d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04d64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04d68: b               #0xc04cc4
  }
  _ _buildFreeGiftBanner(/* No info */) {
    // ** addr: 0xc04d6c, size: 0x2b0
    // 0xc04d6c: EnterFrame
    //     0xc04d6c: stp             fp, lr, [SP, #-0x10]!
    //     0xc04d70: mov             fp, SP
    // 0xc04d74: AllocStack(0x48)
    //     0xc04d74: sub             SP, SP, #0x48
    // 0xc04d78: SetupParameters(_ProductMediaCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc04d78: mov             x0, x1
    //     0xc04d7c: stur            x1, [fp, #-8]
    //     0xc04d80: mov             x1, x2
    //     0xc04d84: stur            x2, [fp, #-0x10]
    // 0xc04d88: CheckStackOverflow
    //     0xc04d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04d8c: cmp             SP, x16
    //     0xc04d90: b.ls            #0xc05010
    // 0xc04d94: r1 = 1
    //     0xc04d94: movz            x1, #0x1
    // 0xc04d98: r0 = AllocateContext()
    //     0xc04d98: bl              #0x16f6108  ; AllocateContextStub
    // 0xc04d9c: mov             x2, x0
    // 0xc04da0: ldur            x0, [fp, #-8]
    // 0xc04da4: stur            x2, [fp, #-0x18]
    // 0xc04da8: StoreField: r2->field_f = r0
    //     0xc04da8: stur            w0, [x2, #0xf]
    // 0xc04dac: LoadField: r1 = r0->field_b
    //     0xc04dac: ldur            w1, [x0, #0xb]
    // 0xc04db0: DecompressPointer r1
    //     0xc04db0: add             x1, x1, HEAP, lsl #32
    // 0xc04db4: cmp             w1, NULL
    // 0xc04db8: b.eq            #0xc05018
    // 0xc04dbc: LoadField: r0 = r1->field_13
    //     0xc04dbc: ldur            w0, [x1, #0x13]
    // 0xc04dc0: DecompressPointer r0
    //     0xc04dc0: add             x0, x0, HEAP, lsl #32
    // 0xc04dc4: tbz             w0, #4, #0xc04dd8
    // 0xc04dc8: r0 = Instance_SizedBox
    //     0xc04dc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc04dcc: LeaveFrame
    //     0xc04dcc: mov             SP, fp
    //     0xc04dd0: ldp             fp, lr, [SP], #0x10
    // 0xc04dd4: ret
    //     0xc04dd4: ret             
    // 0xc04dd8: ldur            x1, [fp, #-0x10]
    // 0xc04ddc: r0 = of()
    //     0xc04ddc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc04de0: LoadField: r1 = r0->field_87
    //     0xc04de0: ldur            w1, [x0, #0x87]
    // 0xc04de4: DecompressPointer r1
    //     0xc04de4: add             x1, x1, HEAP, lsl #32
    // 0xc04de8: LoadField: r0 = r1->field_7
    //     0xc04de8: ldur            w0, [x1, #7]
    // 0xc04dec: DecompressPointer r0
    //     0xc04dec: add             x0, x0, HEAP, lsl #32
    // 0xc04df0: r16 = 15.000000
    //     0xc04df0: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xc04df4: ldr             x16, [x16, #0x480]
    // 0xc04df8: r30 = Instance_Color
    //     0xc04df8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc04dfc: ldr             lr, [lr, #0x858]
    // 0xc04e00: stp             lr, x16, [SP]
    // 0xc04e04: mov             x1, x0
    // 0xc04e08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc04e08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc04e0c: ldr             x4, [x4, #0xaa0]
    // 0xc04e10: r0 = copyWith()
    //     0xc04e10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc04e14: stur            x0, [fp, #-8]
    // 0xc04e18: r0 = Text()
    //     0xc04e18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc04e1c: mov             x1, x0
    // 0xc04e20: r0 = "Get Free Gift"
    //     0xc04e20: add             x0, PP, #0x52, lsl #12  ; [pp+0x52880] "Get Free Gift"
    //     0xc04e24: ldr             x0, [x0, #0x880]
    // 0xc04e28: stur            x1, [fp, #-0x10]
    // 0xc04e2c: StoreField: r1->field_b = r0
    //     0xc04e2c: stur            w0, [x1, #0xb]
    // 0xc04e30: ldur            x0, [fp, #-8]
    // 0xc04e34: StoreField: r1->field_13 = r0
    //     0xc04e34: stur            w0, [x1, #0x13]
    // 0xc04e38: r0 = SvgPicture()
    //     0xc04e38: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc04e3c: mov             x1, x0
    // 0xc04e40: r2 = "assets/images/gift.svg"
    //     0xc04e40: add             x2, PP, #0x52, lsl #12  ; [pp+0x52888] "assets/images/gift.svg"
    //     0xc04e44: ldr             x2, [x2, #0x888]
    // 0xc04e48: stur            x0, [fp, #-8]
    // 0xc04e4c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc04e4c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc04e50: r0 = SvgPicture.asset()
    //     0xc04e50: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc04e54: r0 = Padding()
    //     0xc04e54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04e58: mov             x3, x0
    // 0xc04e5c: r0 = Instance_EdgeInsets
    //     0xc04e5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xc04e60: ldr             x0, [x0, #0xe60]
    // 0xc04e64: stur            x3, [fp, #-0x20]
    // 0xc04e68: StoreField: r3->field_f = r0
    //     0xc04e68: stur            w0, [x3, #0xf]
    // 0xc04e6c: ldur            x0, [fp, #-8]
    // 0xc04e70: StoreField: r3->field_b = r0
    //     0xc04e70: stur            w0, [x3, #0xb]
    // 0xc04e74: r1 = Null
    //     0xc04e74: mov             x1, NULL
    // 0xc04e78: r2 = 4
    //     0xc04e78: movz            x2, #0x4
    // 0xc04e7c: r0 = AllocateArray()
    //     0xc04e7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc04e80: mov             x2, x0
    // 0xc04e84: ldur            x0, [fp, #-0x10]
    // 0xc04e88: stur            x2, [fp, #-8]
    // 0xc04e8c: StoreField: r2->field_f = r0
    //     0xc04e8c: stur            w0, [x2, #0xf]
    // 0xc04e90: ldur            x0, [fp, #-0x20]
    // 0xc04e94: StoreField: r2->field_13 = r0
    //     0xc04e94: stur            w0, [x2, #0x13]
    // 0xc04e98: r1 = <Widget>
    //     0xc04e98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc04e9c: r0 = AllocateGrowableArray()
    //     0xc04e9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc04ea0: mov             x1, x0
    // 0xc04ea4: ldur            x0, [fp, #-8]
    // 0xc04ea8: stur            x1, [fp, #-0x10]
    // 0xc04eac: StoreField: r1->field_f = r0
    //     0xc04eac: stur            w0, [x1, #0xf]
    // 0xc04eb0: r0 = 4
    //     0xc04eb0: movz            x0, #0x4
    // 0xc04eb4: StoreField: r1->field_b = r0
    //     0xc04eb4: stur            w0, [x1, #0xb]
    // 0xc04eb8: r0 = Row()
    //     0xc04eb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc04ebc: mov             x1, x0
    // 0xc04ec0: r0 = Instance_Axis
    //     0xc04ec0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc04ec4: stur            x1, [fp, #-8]
    // 0xc04ec8: StoreField: r1->field_f = r0
    //     0xc04ec8: stur            w0, [x1, #0xf]
    // 0xc04ecc: r0 = Instance_MainAxisAlignment
    //     0xc04ecc: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4daf8] Obj!MainAxisAlignment@d734e1
    //     0xc04ed0: ldr             x0, [x0, #0xaf8]
    // 0xc04ed4: StoreField: r1->field_13 = r0
    //     0xc04ed4: stur            w0, [x1, #0x13]
    // 0xc04ed8: r0 = Instance_MainAxisSize
    //     0xc04ed8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc04edc: ldr             x0, [x0, #0xa10]
    // 0xc04ee0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc04ee0: stur            w0, [x1, #0x17]
    // 0xc04ee4: r0 = Instance_CrossAxisAlignment
    //     0xc04ee4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc04ee8: ldr             x0, [x0, #0xa18]
    // 0xc04eec: StoreField: r1->field_1b = r0
    //     0xc04eec: stur            w0, [x1, #0x1b]
    // 0xc04ef0: r0 = Instance_VerticalDirection
    //     0xc04ef0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc04ef4: ldr             x0, [x0, #0xa20]
    // 0xc04ef8: StoreField: r1->field_23 = r0
    //     0xc04ef8: stur            w0, [x1, #0x23]
    // 0xc04efc: r0 = Instance_Clip
    //     0xc04efc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc04f00: ldr             x0, [x0, #0x38]
    // 0xc04f04: StoreField: r1->field_2b = r0
    //     0xc04f04: stur            w0, [x1, #0x2b]
    // 0xc04f08: StoreField: r1->field_2f = rZR
    //     0xc04f08: stur            xzr, [x1, #0x2f]
    // 0xc04f0c: ldur            x0, [fp, #-0x10]
    // 0xc04f10: StoreField: r1->field_b = r0
    //     0xc04f10: stur            w0, [x1, #0xb]
    // 0xc04f14: r0 = Container()
    //     0xc04f14: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc04f18: stur            x0, [fp, #-0x10]
    // 0xc04f1c: r16 = 147.000000
    //     0xc04f1c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52890] 147
    //     0xc04f20: ldr             x16, [x16, #0x890]
    // 0xc04f24: r30 = 35.000000
    //     0xc04f24: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xc04f28: ldr             lr, [lr, #0x2b0]
    // 0xc04f2c: stp             lr, x16, [SP, #0x18]
    // 0xc04f30: r16 = Instance_EdgeInsets
    //     0xc04f30: add             x16, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xc04f34: ldr             x16, [x16, #0x850]
    // 0xc04f38: r30 = Instance_BoxDecoration
    //     0xc04f38: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc04f3c: ldr             lr, [lr, #0x5a8]
    // 0xc04f40: stp             lr, x16, [SP, #8]
    // 0xc04f44: ldur            x16, [fp, #-8]
    // 0xc04f48: str             x16, [SP]
    // 0xc04f4c: mov             x1, x0
    // 0xc04f50: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x2, padding, 0x3, width, 0x1, null]
    //     0xc04f50: add             x4, PP, #0x38, lsl #12  ; [pp+0x38060] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x2, "padding", 0x3, "width", 0x1, Null]
    //     0xc04f54: ldr             x4, [x4, #0x60]
    // 0xc04f58: r0 = Container()
    //     0xc04f58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc04f5c: r1 = <Path>
    //     0xc04f5c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xc04f60: ldr             x1, [x1, #0xd30]
    // 0xc04f64: r0 = _CustomPath()
    //     0xc04f64: bl              #0xc0501c  ; Allocate_CustomPathStub -> _CustomPath (size=0x10)
    // 0xc04f68: stur            x0, [fp, #-8]
    // 0xc04f6c: r0 = ClipPath()
    //     0xc04f6c: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xc04f70: mov             x1, x0
    // 0xc04f74: ldur            x0, [fp, #-8]
    // 0xc04f78: stur            x1, [fp, #-0x20]
    // 0xc04f7c: StoreField: r1->field_f = r0
    //     0xc04f7c: stur            w0, [x1, #0xf]
    // 0xc04f80: r0 = Instance_Clip
    //     0xc04f80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xc04f84: ldr             x0, [x0, #0x138]
    // 0xc04f88: StoreField: r1->field_13 = r0
    //     0xc04f88: stur            w0, [x1, #0x13]
    // 0xc04f8c: ldur            x0, [fp, #-0x10]
    // 0xc04f90: StoreField: r1->field_b = r0
    //     0xc04f90: stur            w0, [x1, #0xb]
    // 0xc04f94: r0 = InkWell()
    //     0xc04f94: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc04f98: mov             x3, x0
    // 0xc04f9c: ldur            x0, [fp, #-0x20]
    // 0xc04fa0: stur            x3, [fp, #-8]
    // 0xc04fa4: StoreField: r3->field_b = r0
    //     0xc04fa4: stur            w0, [x3, #0xb]
    // 0xc04fa8: ldur            x2, [fp, #-0x18]
    // 0xc04fac: r1 = Function '<anonymous closure>':.
    //     0xc04fac: add             x1, PP, #0x52, lsl #12  ; [pp+0x52898] AnonymousClosure: (0xc05028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildFreeGiftBanner (0xc04d6c)
    //     0xc04fb0: ldr             x1, [x1, #0x898]
    // 0xc04fb4: r0 = AllocateClosure()
    //     0xc04fb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc04fb8: mov             x1, x0
    // 0xc04fbc: ldur            x0, [fp, #-8]
    // 0xc04fc0: StoreField: r0->field_f = r1
    //     0xc04fc0: stur            w1, [x0, #0xf]
    // 0xc04fc4: r1 = true
    //     0xc04fc4: add             x1, NULL, #0x20  ; true
    // 0xc04fc8: StoreField: r0->field_43 = r1
    //     0xc04fc8: stur            w1, [x0, #0x43]
    // 0xc04fcc: r2 = Instance_BoxShape
    //     0xc04fcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc04fd0: ldr             x2, [x2, #0x80]
    // 0xc04fd4: StoreField: r0->field_47 = r2
    //     0xc04fd4: stur            w2, [x0, #0x47]
    // 0xc04fd8: StoreField: r0->field_6f = r1
    //     0xc04fd8: stur            w1, [x0, #0x6f]
    // 0xc04fdc: r2 = false
    //     0xc04fdc: add             x2, NULL, #0x30  ; false
    // 0xc04fe0: StoreField: r0->field_73 = r2
    //     0xc04fe0: stur            w2, [x0, #0x73]
    // 0xc04fe4: StoreField: r0->field_83 = r1
    //     0xc04fe4: stur            w1, [x0, #0x83]
    // 0xc04fe8: StoreField: r0->field_7b = r2
    //     0xc04fe8: stur            w2, [x0, #0x7b]
    // 0xc04fec: r0 = Padding()
    //     0xc04fec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc04ff0: r1 = Instance_EdgeInsets
    //     0xc04ff0: add             x1, PP, #0x52, lsl #12  ; [pp+0x528a0] Obj!EdgeInsets@d58941
    //     0xc04ff4: ldr             x1, [x1, #0x8a0]
    // 0xc04ff8: StoreField: r0->field_f = r1
    //     0xc04ff8: stur            w1, [x0, #0xf]
    // 0xc04ffc: ldur            x1, [fp, #-8]
    // 0xc05000: StoreField: r0->field_b = r1
    //     0xc05000: stur            w1, [x0, #0xb]
    // 0xc05004: LeaveFrame
    //     0xc05004: mov             SP, fp
    //     0xc05008: ldp             fp, lr, [SP], #0x10
    // 0xc0500c: ret
    //     0xc0500c: ret             
    // 0xc05010: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc05010: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc05014: b               #0xc04d94
    // 0xc05018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc05028, size: 0x90
    // 0xc05028: EnterFrame
    //     0xc05028: stp             fp, lr, [SP, #-0x10]!
    //     0xc0502c: mov             fp, SP
    // 0xc05030: AllocStack(0x10)
    //     0xc05030: sub             SP, SP, #0x10
    // 0xc05034: SetupParameters()
    //     0xc05034: ldr             x0, [fp, #0x10]
    //     0xc05038: ldur            w1, [x0, #0x17]
    //     0xc0503c: add             x1, x1, HEAP, lsl #32
    //     0xc05040: stur            x1, [fp, #-8]
    // 0xc05044: CheckStackOverflow
    //     0xc05044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc05048: cmp             SP, x16
    //     0xc0504c: b.ls            #0xc050ac
    // 0xc05050: LoadField: r0 = r1->field_f
    //     0xc05050: ldur            w0, [x1, #0xf]
    // 0xc05054: DecompressPointer r0
    //     0xc05054: add             x0, x0, HEAP, lsl #32
    // 0xc05058: LoadField: r2 = r0->field_b
    //     0xc05058: ldur            w2, [x0, #0xb]
    // 0xc0505c: DecompressPointer r2
    //     0xc0505c: add             x2, x2, HEAP, lsl #32
    // 0xc05060: cmp             w2, NULL
    // 0xc05064: b.eq            #0xc050b4
    // 0xc05068: LoadField: r0 = r2->field_1f
    //     0xc05068: ldur            w0, [x2, #0x1f]
    // 0xc0506c: DecompressPointer r0
    //     0xc0506c: add             x0, x0, HEAP, lsl #32
    // 0xc05070: str             x0, [SP]
    // 0xc05074: r4 = 0
    //     0xc05074: movz            x4, #0
    // 0xc05078: ldr             x0, [SP]
    // 0xc0507c: r16 = UnlinkedCall_0x613b5c
    //     0xc0507c: add             x16, PP, #0x52, lsl #12  ; [pp+0x528a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc05080: add             x16, x16, #0x8a8
    // 0xc05084: ldp             x5, lr, [x16]
    // 0xc05088: blr             lr
    // 0xc0508c: ldur            x0, [fp, #-8]
    // 0xc05090: LoadField: r1 = r0->field_f
    //     0xc05090: ldur            w1, [x0, #0xf]
    // 0xc05094: DecompressPointer r1
    //     0xc05094: add             x1, x1, HEAP, lsl #32
    // 0xc05098: r0 = _showFreeGiftDialog()
    //     0xc05098: bl              #0x99e89c  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog
    // 0xc0509c: r0 = Null
    //     0xc0509c: mov             x0, NULL
    // 0xc050a0: LeaveFrame
    //     0xc050a0: mov             SP, fp
    //     0xc050a4: ldp             fp, lr, [SP], #0x10
    // 0xc050a8: ret
    //     0xc050a8: ret             
    // 0xc050ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc050ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc050b0: b               #0xc05050
    // 0xc050b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc050b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc884b8, size: 0x5c
    // 0xc884b8: EnterFrame
    //     0xc884b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc884bc: mov             fp, SP
    // 0xc884c0: AllocStack(0x8)
    //     0xc884c0: sub             SP, SP, #8
    // 0xc884c4: r0 = true
    //     0xc884c4: add             x0, NULL, #0x20  ; true
    // 0xc884c8: mov             x2, x1
    // 0xc884cc: stur            x1, [fp, #-8]
    // 0xc884d0: CheckStackOverflow
    //     0xc884d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc884d4: cmp             SP, x16
    //     0xc884d8: b.ls            #0xc8850c
    // 0xc884dc: StoreField: r2->field_2b = r0
    //     0xc884dc: stur            w0, [x2, #0x2b]
    // 0xc884e0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xc884e0: ldur            w1, [x2, #0x17]
    // 0xc884e4: DecompressPointer r1
    //     0xc884e4: add             x1, x1, HEAP, lsl #32
    // 0xc884e8: r0 = dispose()
    //     0xc884e8: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc884ec: ldur            x0, [fp, #-8]
    // 0xc884f0: LoadField: r1 = r0->field_27
    //     0xc884f0: ldur            w1, [x0, #0x27]
    // 0xc884f4: DecompressPointer r1
    //     0xc884f4: add             x1, x1, HEAP, lsl #32
    // 0xc884f8: r0 = clear()
    //     0xc884f8: bl              #0x649b54  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0xc884fc: r0 = Null
    //     0xc884fc: mov             x0, NULL
    // 0xc88500: LeaveFrame
    //     0xc88500: mov             SP, fp
    //     0xc88504: ldp             fp, lr, [SP], #0x10
    // 0xc88508: ret
    //     0xc88508: ret             
    // 0xc8850c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8850c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88510: b               #0xc884dc
  }
}

// class id: 3971, size: 0x28, field offset: 0xc
//   const constructor, 
class ProductMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80fec, size: 0x48
    // 0xc80fec: EnterFrame
    //     0xc80fec: stp             fp, lr, [SP, #-0x10]!
    //     0xc80ff0: mov             fp, SP
    // 0xc80ff4: AllocStack(0x8)
    //     0xc80ff4: sub             SP, SP, #8
    // 0xc80ff8: CheckStackOverflow
    //     0xc80ff8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80ffc: cmp             SP, x16
    //     0xc81000: b.ls            #0xc8102c
    // 0xc81004: r1 = <ProductMediaCarousel>
    //     0xc81004: add             x1, PP, #0x48, lsl #12  ; [pp+0x482a8] TypeArguments: <ProductMediaCarousel>
    //     0xc81008: ldr             x1, [x1, #0x2a8]
    // 0xc8100c: r0 = _ProductMediaCarouselState()
    //     0xc8100c: bl              #0xc81034  ; Allocate_ProductMediaCarouselStateStub -> _ProductMediaCarouselState (size=0x34)
    // 0xc81010: mov             x1, x0
    // 0xc81014: stur            x0, [fp, #-8]
    // 0xc81018: r0 = _ProductMediaCarouselState()
    //     0xc81018: bl              #0xc7c270  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_ProductMediaCarouselState
    // 0xc8101c: ldur            x0, [fp, #-8]
    // 0xc81020: LeaveFrame
    //     0xc81020: mov             SP, fp
    //     0xc81024: ldp             fp, lr, [SP], #0x10
    // 0xc81028: ret
    //     0xc81028: ret             
    // 0xc8102c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8102c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81030: b               #0xc81004
  }
}

// class id: 4859, size: 0x10, field offset: 0x10
class _CustomPath extends CustomClipper<dynamic> {

  _ shouldReclip(/* No info */) {
    // ** addr: 0x161d694, size: 0x40
    // 0x161d694: EnterFrame
    //     0x161d694: stp             fp, lr, [SP, #-0x10]!
    //     0x161d698: mov             fp, SP
    // 0x161d69c: mov             x0, x2
    // 0x161d6a0: mov             x4, x1
    // 0x161d6a4: mov             x3, x2
    // 0x161d6a8: r2 = Null
    //     0x161d6a8: mov             x2, NULL
    // 0x161d6ac: r1 = Null
    //     0x161d6ac: mov             x1, NULL
    // 0x161d6b0: r8 = CustomClipper<Path>
    //     0x161d6b0: add             x8, PP, #0x4c, lsl #12  ; [pp+0x4cc88] Type: CustomClipper<Path>
    //     0x161d6b4: ldr             x8, [x8, #0xc88]
    // 0x161d6b8: r3 = Null
    //     0x161d6b8: add             x3, PP, #0x61, lsl #12  ; [pp+0x61b68] Null
    //     0x161d6bc: ldr             x3, [x3, #0xb68]
    // 0x161d6c0: r0 = CustomClipper<Path>()
    //     0x161d6c0: bl              #0x99065c  ; IsType_CustomClipper<Path>_Stub
    // 0x161d6c4: r0 = true
    //     0x161d6c4: add             x0, NULL, #0x20  ; true
    // 0x161d6c8: LeaveFrame
    //     0x161d6c8: mov             SP, fp
    //     0x161d6cc: ldp             fp, lr, [SP], #0x10
    // 0x161d6d0: ret
    //     0x161d6d0: ret             
  }
  _ getClip(/* No info */) {
    // ** addr: 0x161e484, size: 0x230
    // 0x161e484: EnterFrame
    //     0x161e484: stp             fp, lr, [SP, #-0x10]!
    //     0x161e488: mov             fp, SP
    // 0x161e48c: AllocStack(0x30)
    //     0x161e48c: sub             SP, SP, #0x30
    // 0x161e490: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x161e490: stur            x2, [fp, #-8]
    // 0x161e494: CheckStackOverflow
    //     0x161e494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x161e498: cmp             SP, x16
    //     0x161e49c: b.ls            #0x161e698
    // 0x161e4a0: r0 = _NativePath()
    //     0x161e4a0: bl              #0x76e63c  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x161e4a4: mov             x1, x0
    // 0x161e4a8: stur            x0, [fp, #-0x10]
    // 0x161e4ac: r0 = __constructor$Method$FfiNative()
    //     0x161e4ac: bl              #0x76edd4  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x161e4b0: ldur            x0, [fp, #-8]
    // 0x161e4b4: LoadField: d1 = r0->field_f
    //     0x161e4b4: ldur            d1, [x0, #0xf]
    // 0x161e4b8: stur            d1, [fp, #-0x28]
    // 0x161e4bc: d0 = 2.000000
    //     0x161e4bc: fmov            d0, #2.00000000
    // 0x161e4c0: fdiv            d2, d1, d0
    // 0x161e4c4: ldur            x2, [fp, #-0x10]
    // 0x161e4c8: stur            d2, [fp, #-0x20]
    // 0x161e4cc: LoadField: r1 = r2->field_7
    //     0x161e4cc: ldur            w1, [x2, #7]
    // 0x161e4d0: DecompressPointer r1
    //     0x161e4d0: add             x1, x1, HEAP, lsl #32
    // 0x161e4d4: cmp             w1, NULL
    // 0x161e4d8: b.eq            #0x161e6a0
    // 0x161e4dc: LoadField: r3 = r1->field_7
    //     0x161e4dc: ldur            x3, [x1, #7]
    // 0x161e4e0: ldr             x1, [x3]
    // 0x161e4e4: cbz             x1, #0x161e648
    // 0x161e4e8: mov             x3, x1
    // 0x161e4ec: stur            x3, [fp, #-0x18]
    // 0x161e4f0: r1 = <Never>
    //     0x161e4f0: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x161e4f4: r0 = Pointer()
    //     0x161e4f4: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x161e4f8: mov             x1, x0
    // 0x161e4fc: ldur            x0, [fp, #-0x18]
    // 0x161e500: StoreField: r1->field_7 = r0
    //     0x161e500: stur            x0, [x1, #7]
    // 0x161e504: ldur            d1, [fp, #-0x20]
    // 0x161e508: d0 = 10.000000
    //     0x161e508: fmov            d0, #10.00000000
    // 0x161e50c: r0 = _lineTo$Method$FfiNative()
    //     0x161e50c: bl              #0x775fcc  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x161e510: ldur            x0, [fp, #-0x10]
    // 0x161e514: LoadField: r1 = r0->field_7
    //     0x161e514: ldur            w1, [x0, #7]
    // 0x161e518: DecompressPointer r1
    //     0x161e518: add             x1, x1, HEAP, lsl #32
    // 0x161e51c: cmp             w1, NULL
    // 0x161e520: b.eq            #0x161e6a4
    // 0x161e524: LoadField: r2 = r1->field_7
    //     0x161e524: ldur            x2, [x1, #7]
    // 0x161e528: ldr             x1, [x2]
    // 0x161e52c: cbz             x1, #0x161e658
    // 0x161e530: ldur            x2, [fp, #-8]
    // 0x161e534: mov             x3, x1
    // 0x161e538: stur            x3, [fp, #-0x18]
    // 0x161e53c: r1 = <Never>
    //     0x161e53c: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x161e540: r0 = Pointer()
    //     0x161e540: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x161e544: mov             x1, x0
    // 0x161e548: ldur            x0, [fp, #-0x18]
    // 0x161e54c: StoreField: r1->field_7 = r0
    //     0x161e54c: stur            x0, [x1, #7]
    // 0x161e550: ldur            d1, [fp, #-0x28]
    // 0x161e554: d0 = 0.000000
    //     0x161e554: eor             v0.16b, v0.16b, v0.16b
    // 0x161e558: r0 = _lineTo$Method$FfiNative()
    //     0x161e558: bl              #0x775fcc  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x161e55c: ldur            x0, [fp, #-8]
    // 0x161e560: LoadField: d0 = r0->field_7
    //     0x161e560: ldur            d0, [x0, #7]
    // 0x161e564: ldur            x0, [fp, #-0x10]
    // 0x161e568: stur            d0, [fp, #-0x20]
    // 0x161e56c: LoadField: r1 = r0->field_7
    //     0x161e56c: ldur            w1, [x0, #7]
    // 0x161e570: DecompressPointer r1
    //     0x161e570: add             x1, x1, HEAP, lsl #32
    // 0x161e574: cmp             w1, NULL
    // 0x161e578: b.eq            #0x161e6a8
    // 0x161e57c: LoadField: r2 = r1->field_7
    //     0x161e57c: ldur            x2, [x1, #7]
    // 0x161e580: ldr             x1, [x2]
    // 0x161e584: cbz             x1, #0x161e668
    // 0x161e588: mov             x2, x1
    // 0x161e58c: stur            x2, [fp, #-0x18]
    // 0x161e590: r1 = <Never>
    //     0x161e590: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x161e594: r0 = Pointer()
    //     0x161e594: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x161e598: mov             x1, x0
    // 0x161e59c: ldur            x0, [fp, #-0x18]
    // 0x161e5a0: StoreField: r1->field_7 = r0
    //     0x161e5a0: stur            x0, [x1, #7]
    // 0x161e5a4: ldur            d0, [fp, #-0x20]
    // 0x161e5a8: ldur            d1, [fp, #-0x28]
    // 0x161e5ac: r0 = _lineTo$Method$FfiNative()
    //     0x161e5ac: bl              #0x775fcc  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x161e5b0: ldur            x0, [fp, #-0x10]
    // 0x161e5b4: LoadField: r1 = r0->field_7
    //     0x161e5b4: ldur            w1, [x0, #7]
    // 0x161e5b8: DecompressPointer r1
    //     0x161e5b8: add             x1, x1, HEAP, lsl #32
    // 0x161e5bc: cmp             w1, NULL
    // 0x161e5c0: b.eq            #0x161e6ac
    // 0x161e5c4: LoadField: r2 = r1->field_7
    //     0x161e5c4: ldur            x2, [x1, #7]
    // 0x161e5c8: ldr             x1, [x2]
    // 0x161e5cc: cbz             x1, #0x161e678
    // 0x161e5d0: mov             x2, x1
    // 0x161e5d4: stur            x2, [fp, #-0x18]
    // 0x161e5d8: r1 = <Never>
    //     0x161e5d8: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x161e5dc: r0 = Pointer()
    //     0x161e5dc: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x161e5e0: mov             x1, x0
    // 0x161e5e4: ldur            x0, [fp, #-0x18]
    // 0x161e5e8: StoreField: r1->field_7 = r0
    //     0x161e5e8: stur            x0, [x1, #7]
    // 0x161e5ec: ldur            d0, [fp, #-0x20]
    // 0x161e5f0: d1 = 0.000000
    //     0x161e5f0: eor             v1.16b, v1.16b, v1.16b
    // 0x161e5f4: r0 = _lineTo$Method$FfiNative()
    //     0x161e5f4: bl              #0x775fcc  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x161e5f8: ldur            x0, [fp, #-0x10]
    // 0x161e5fc: LoadField: r1 = r0->field_7
    //     0x161e5fc: ldur            w1, [x0, #7]
    // 0x161e600: DecompressPointer r1
    //     0x161e600: add             x1, x1, HEAP, lsl #32
    // 0x161e604: cmp             w1, NULL
    // 0x161e608: b.eq            #0x161e6b0
    // 0x161e60c: LoadField: r2 = r1->field_7
    //     0x161e60c: ldur            x2, [x1, #7]
    // 0x161e610: ldr             x1, [x2]
    // 0x161e614: cbz             x1, #0x161e688
    // 0x161e618: mov             x2, x1
    // 0x161e61c: stur            x2, [fp, #-0x18]
    // 0x161e620: r1 = <Never>
    //     0x161e620: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x161e624: r0 = Pointer()
    //     0x161e624: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x161e628: mov             x1, x0
    // 0x161e62c: ldur            x0, [fp, #-0x18]
    // 0x161e630: StoreField: r1->field_7 = r0
    //     0x161e630: stur            x0, [x1, #7]
    // 0x161e634: r0 = _close$Method$FfiNative()
    //     0x161e634: bl              #0x775924  ; [dart:ui] _NativePath::_close$Method$FfiNative
    // 0x161e638: ldur            x0, [fp, #-0x10]
    // 0x161e63c: LeaveFrame
    //     0x161e63c: mov             SP, fp
    //     0x161e640: ldp             fp, lr, [SP], #0x10
    // 0x161e644: ret
    //     0x161e644: ret             
    // 0x161e648: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x161e648: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x161e64c: str             x16, [SP]
    // 0x161e650: r0 = _throwNew()
    //     0x161e650: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x161e654: brk             #0
    // 0x161e658: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x161e658: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x161e65c: str             x16, [SP]
    // 0x161e660: r0 = _throwNew()
    //     0x161e660: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x161e664: brk             #0
    // 0x161e668: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x161e668: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x161e66c: str             x16, [SP]
    // 0x161e670: r0 = _throwNew()
    //     0x161e670: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x161e674: brk             #0
    // 0x161e678: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x161e678: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x161e67c: str             x16, [SP]
    // 0x161e680: r0 = _throwNew()
    //     0x161e680: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x161e684: brk             #0
    // 0x161e688: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x161e688: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x161e68c: str             x16, [SP]
    // 0x161e690: r0 = _throwNew()
    //     0x161e690: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x161e694: brk             #0
    // 0x161e698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x161e698: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x161e69c: b               #0x161e4a0
    // 0x161e6a0: r0 = NullErrorSharedWithFPURegs()
    //     0x161e6a0: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
    // 0x161e6a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x161e6a4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x161e6a8: r0 = NullErrorSharedWithFPURegs()
    //     0x161e6a8: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
    // 0x161e6ac: r0 = NullErrorSharedWithoutFPURegs()
    //     0x161e6ac: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x161e6b0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x161e6b0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
}
