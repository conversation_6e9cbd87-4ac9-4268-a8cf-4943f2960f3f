// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart

// class id: 1049574, size: 0x8
class :: {
}

// class id: 3211, size: 0x20, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x94b238, size: 0x80
    // 0x94b238: EnterFrame
    //     0x94b238: stp             fp, lr, [SP, #-0x10]!
    //     0x94b23c: mov             fp, SP
    // 0x94b240: AllocStack(0x10)
    //     0x94b240: sub             SP, SP, #0x10
    // 0x94b244: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x94b244: stur            x1, [fp, #-8]
    // 0x94b248: CheckStackOverflow
    //     0x94b248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b24c: cmp             SP, x16
    //     0x94b250: b.ls            #0x94b2b0
    // 0x94b254: r0 = PageController()
    //     0x94b254: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94b258: stur            x0, [fp, #-0x10]
    // 0x94b25c: StoreField: r0->field_3f = rZR
    //     0x94b25c: stur            xzr, [x0, #0x3f]
    // 0x94b260: r1 = true
    //     0x94b260: add             x1, NULL, #0x20  ; true
    // 0x94b264: StoreField: r0->field_47 = r1
    //     0x94b264: stur            w1, [x0, #0x47]
    // 0x94b268: d0 = 1.000000
    //     0x94b268: fmov            d0, #1.00000000
    // 0x94b26c: StoreField: r0->field_4b = d0
    //     0x94b26c: stur            d0, [x0, #0x4b]
    // 0x94b270: mov             x1, x0
    // 0x94b274: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94b274: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94b278: r0 = ScrollController()
    //     0x94b278: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94b27c: ldur            x0, [fp, #-0x10]
    // 0x94b280: ldur            x1, [fp, #-8]
    // 0x94b284: StoreField: r1->field_13 = r0
    //     0x94b284: stur            w0, [x1, #0x13]
    //     0x94b288: ldurb           w16, [x1, #-1]
    //     0x94b28c: ldurb           w17, [x0, #-1]
    //     0x94b290: and             x16, x17, x16, lsr #2
    //     0x94b294: tst             x16, HEAP, lsr #32
    //     0x94b298: b.eq            #0x94b2a0
    //     0x94b29c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b2a0: r0 = Null
    //     0x94b2a0: mov             x0, NULL
    // 0x94b2a4: LeaveFrame
    //     0x94b2a4: mov             SP, fp
    //     0x94b2a8: ldp             fp, lr, [SP], #0x10
    // 0x94b2ac: ret
    //     0x94b2ac: ret             
    // 0x94b2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b2b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b2b4: b               #0x94b254
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x98e9d8, size: 0x3c
    // 0x98e9d8: ldr             x1, [SP]
    // 0x98e9dc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x98e9dc: ldur            w2, [x1, #0x17]
    // 0x98e9e0: DecompressPointer r2
    //     0x98e9e0: add             x2, x2, HEAP, lsl #32
    // 0x98e9e4: LoadField: r1 = r2->field_b
    //     0x98e9e4: ldur            w1, [x2, #0xb]
    // 0x98e9e8: DecompressPointer r1
    //     0x98e9e8: add             x1, x1, HEAP, lsl #32
    // 0x98e9ec: LoadField: r3 = r1->field_f
    //     0x98e9ec: ldur            w3, [x1, #0xf]
    // 0x98e9f0: DecompressPointer r3
    //     0x98e9f0: add             x3, x3, HEAP, lsl #32
    // 0x98e9f4: LoadField: r1 = r2->field_f
    //     0x98e9f4: ldur            w1, [x2, #0xf]
    // 0x98e9f8: DecompressPointer r1
    //     0x98e9f8: add             x1, x1, HEAP, lsl #32
    // 0x98e9fc: r2 = LoadInt32Instr(r1)
    //     0x98e9fc: sbfx            x2, x1, #1, #0x1f
    //     0x98ea00: tbz             w1, #0, #0x98ea08
    //     0x98ea04: ldur            x2, [x1, #7]
    // 0x98ea08: ArrayStore: r3[0] = r2  ; List_8
    //     0x98ea08: stur            x2, [x3, #0x17]
    // 0x98ea0c: r0 = Null
    //     0x98ea0c: mov             x0, NULL
    // 0x98ea10: ret
    //     0x98ea10: ret             
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0x98ea14, size: 0x84
    // 0x98ea14: EnterFrame
    //     0x98ea14: stp             fp, lr, [SP, #-0x10]!
    //     0x98ea18: mov             fp, SP
    // 0x98ea1c: AllocStack(0x10)
    //     0x98ea1c: sub             SP, SP, #0x10
    // 0x98ea20: SetupParameters()
    //     0x98ea20: ldr             x0, [fp, #0x18]
    //     0x98ea24: ldur            w1, [x0, #0x17]
    //     0x98ea28: add             x1, x1, HEAP, lsl #32
    //     0x98ea2c: stur            x1, [fp, #-8]
    // 0x98ea30: CheckStackOverflow
    //     0x98ea30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98ea34: cmp             SP, x16
    //     0x98ea38: b.ls            #0x98ea90
    // 0x98ea3c: r1 = 1
    //     0x98ea3c: movz            x1, #0x1
    // 0x98ea40: r0 = AllocateContext()
    //     0x98ea40: bl              #0x16f6108  ; AllocateContextStub
    // 0x98ea44: mov             x1, x0
    // 0x98ea48: ldur            x0, [fp, #-8]
    // 0x98ea4c: StoreField: r1->field_b = r0
    //     0x98ea4c: stur            w0, [x1, #0xb]
    // 0x98ea50: ldr             x2, [fp, #0x10]
    // 0x98ea54: StoreField: r1->field_f = r2
    //     0x98ea54: stur            w2, [x1, #0xf]
    // 0x98ea58: LoadField: r3 = r0->field_f
    //     0x98ea58: ldur            w3, [x0, #0xf]
    // 0x98ea5c: DecompressPointer r3
    //     0x98ea5c: add             x3, x3, HEAP, lsl #32
    // 0x98ea60: mov             x2, x1
    // 0x98ea64: stur            x3, [fp, #-0x10]
    // 0x98ea68: r1 = Function '<anonymous closure>':.
    //     0x98ea68: add             x1, PP, #0x52, lsl #12  ; [pp+0x52348] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0x98ea6c: ldr             x1, [x1, #0x348]
    // 0x98ea70: r0 = AllocateClosure()
    //     0x98ea70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98ea74: ldur            x1, [fp, #-0x10]
    // 0x98ea78: mov             x2, x0
    // 0x98ea7c: r0 = setState()
    //     0x98ea7c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x98ea80: r0 = Null
    //     0x98ea80: mov             x0, NULL
    // 0x98ea84: LeaveFrame
    //     0x98ea84: mov             SP, fp
    //     0x98ea88: ldp             fp, lr, [SP], #0x10
    // 0x98ea8c: ret
    //     0x98ea8c: ret             
    // 0x98ea90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98ea90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98ea94: b               #0x98ea3c
  }
  _ build(/* No info */) {
    // ** addr: 0xc0f5ac, size: 0x9fc
    // 0xc0f5ac: EnterFrame
    //     0xc0f5ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc0f5b0: mov             fp, SP
    // 0xc0f5b4: AllocStack(0x80)
    //     0xc0f5b4: sub             SP, SP, #0x80
    // 0xc0f5b8: SetupParameters(_TestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc0f5b8: mov             x0, x1
    //     0xc0f5bc: stur            x1, [fp, #-8]
    //     0xc0f5c0: mov             x1, x2
    //     0xc0f5c4: stur            x2, [fp, #-0x10]
    // 0xc0f5c8: CheckStackOverflow
    //     0xc0f5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0f5cc: cmp             SP, x16
    //     0xc0f5d0: b.ls            #0xc0ff50
    // 0xc0f5d4: r1 = 1
    //     0xc0f5d4: movz            x1, #0x1
    // 0xc0f5d8: r0 = AllocateContext()
    //     0xc0f5d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0f5dc: mov             x3, x0
    // 0xc0f5e0: ldur            x0, [fp, #-8]
    // 0xc0f5e4: stur            x3, [fp, #-0x20]
    // 0xc0f5e8: StoreField: r3->field_f = r0
    //     0xc0f5e8: stur            w0, [x3, #0xf]
    // 0xc0f5ec: LoadField: r1 = r0->field_b
    //     0xc0f5ec: ldur            w1, [x0, #0xb]
    // 0xc0f5f0: DecompressPointer r1
    //     0xc0f5f0: add             x1, x1, HEAP, lsl #32
    // 0xc0f5f4: cmp             w1, NULL
    // 0xc0f5f8: b.eq            #0xc0ff58
    // 0xc0f5fc: LoadField: r2 = r1->field_13
    //     0xc0f5fc: ldur            w2, [x1, #0x13]
    // 0xc0f600: DecompressPointer r2
    //     0xc0f600: add             x2, x2, HEAP, lsl #32
    // 0xc0f604: LoadField: r1 = r2->field_7
    //     0xc0f604: ldur            w1, [x2, #7]
    // 0xc0f608: DecompressPointer r1
    //     0xc0f608: add             x1, x1, HEAP, lsl #32
    // 0xc0f60c: cmp             w1, NULL
    // 0xc0f610: b.ne            #0xc0f61c
    // 0xc0f614: r1 = Instance_TitleAlignment
    //     0xc0f614: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc0f618: ldr             x1, [x1, #0x518]
    // 0xc0f61c: r16 = Instance_TitleAlignment
    //     0xc0f61c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc0f620: ldr             x16, [x16, #0x520]
    // 0xc0f624: cmp             w1, w16
    // 0xc0f628: b.ne            #0xc0f638
    // 0xc0f62c: r4 = Instance_CrossAxisAlignment
    //     0xc0f62c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xc0f630: ldr             x4, [x4, #0xc68]
    // 0xc0f634: b               #0xc0f65c
    // 0xc0f638: r16 = Instance_TitleAlignment
    //     0xc0f638: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc0f63c: ldr             x16, [x16, #0x518]
    // 0xc0f640: cmp             w1, w16
    // 0xc0f644: b.ne            #0xc0f654
    // 0xc0f648: r4 = Instance_CrossAxisAlignment
    //     0xc0f648: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0f64c: ldr             x4, [x4, #0x890]
    // 0xc0f650: b               #0xc0f65c
    // 0xc0f654: r4 = Instance_CrossAxisAlignment
    //     0xc0f654: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0f658: ldr             x4, [x4, #0xa18]
    // 0xc0f65c: stur            x4, [fp, #-0x18]
    // 0xc0f660: r1 = <Widget>
    //     0xc0f660: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0f664: r2 = 0
    //     0xc0f664: movz            x2, #0
    // 0xc0f668: r0 = _GrowableList()
    //     0xc0f668: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0f66c: mov             x2, x0
    // 0xc0f670: ldur            x1, [fp, #-8]
    // 0xc0f674: stur            x2, [fp, #-0x28]
    // 0xc0f678: LoadField: r0 = r1->field_b
    //     0xc0f678: ldur            w0, [x1, #0xb]
    // 0xc0f67c: DecompressPointer r0
    //     0xc0f67c: add             x0, x0, HEAP, lsl #32
    // 0xc0f680: cmp             w0, NULL
    // 0xc0f684: b.eq            #0xc0ff5c
    // 0xc0f688: LoadField: r3 = r0->field_f
    //     0xc0f688: ldur            w3, [x0, #0xf]
    // 0xc0f68c: DecompressPointer r3
    //     0xc0f68c: add             x3, x3, HEAP, lsl #32
    // 0xc0f690: LoadField: r0 = r3->field_7
    //     0xc0f690: ldur            w0, [x3, #7]
    // 0xc0f694: cbz             w0, #0xc0f820
    // 0xc0f698: r0 = LoadClassIdInstr(r3)
    //     0xc0f698: ldur            x0, [x3, #-1]
    //     0xc0f69c: ubfx            x0, x0, #0xc, #0x14
    // 0xc0f6a0: str             x3, [SP]
    // 0xc0f6a4: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc0f6a4: sub             lr, x0, #1, lsl #12
    //     0xc0f6a8: ldr             lr, [x21, lr, lsl #3]
    //     0xc0f6ac: blr             lr
    // 0xc0f6b0: mov             x2, x0
    // 0xc0f6b4: ldur            x0, [fp, #-8]
    // 0xc0f6b8: stur            x2, [fp, #-0x38]
    // 0xc0f6bc: LoadField: r1 = r0->field_b
    //     0xc0f6bc: ldur            w1, [x0, #0xb]
    // 0xc0f6c0: DecompressPointer r1
    //     0xc0f6c0: add             x1, x1, HEAP, lsl #32
    // 0xc0f6c4: cmp             w1, NULL
    // 0xc0f6c8: b.eq            #0xc0ff60
    // 0xc0f6cc: LoadField: r3 = r1->field_13
    //     0xc0f6cc: ldur            w3, [x1, #0x13]
    // 0xc0f6d0: DecompressPointer r3
    //     0xc0f6d0: add             x3, x3, HEAP, lsl #32
    // 0xc0f6d4: LoadField: r1 = r3->field_7
    //     0xc0f6d4: ldur            w1, [x3, #7]
    // 0xc0f6d8: DecompressPointer r1
    //     0xc0f6d8: add             x1, x1, HEAP, lsl #32
    // 0xc0f6dc: cmp             w1, NULL
    // 0xc0f6e0: b.ne            #0xc0f6ec
    // 0xc0f6e4: r1 = Instance_TitleAlignment
    //     0xc0f6e4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc0f6e8: ldr             x1, [x1, #0x518]
    // 0xc0f6ec: r16 = Instance_TitleAlignment
    //     0xc0f6ec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc0f6f0: ldr             x16, [x16, #0x520]
    // 0xc0f6f4: cmp             w1, w16
    // 0xc0f6f8: b.ne            #0xc0f704
    // 0xc0f6fc: r4 = Instance_TextAlign
    //     0xc0f6fc: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xc0f700: b               #0xc0f720
    // 0xc0f704: r16 = Instance_TitleAlignment
    //     0xc0f704: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc0f708: ldr             x16, [x16, #0x518]
    // 0xc0f70c: cmp             w1, w16
    // 0xc0f710: b.ne            #0xc0f71c
    // 0xc0f714: r4 = Instance_TextAlign
    //     0xc0f714: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc0f718: b               #0xc0f720
    // 0xc0f71c: r4 = Instance_TextAlign
    //     0xc0f71c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc0f720: ldur            x3, [fp, #-0x28]
    // 0xc0f724: ldur            x1, [fp, #-0x10]
    // 0xc0f728: stur            x4, [fp, #-0x30]
    // 0xc0f72c: r0 = of()
    //     0xc0f72c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0f730: LoadField: r1 = r0->field_87
    //     0xc0f730: ldur            w1, [x0, #0x87]
    // 0xc0f734: DecompressPointer r1
    //     0xc0f734: add             x1, x1, HEAP, lsl #32
    // 0xc0f738: LoadField: r0 = r1->field_27
    //     0xc0f738: ldur            w0, [x1, #0x27]
    // 0xc0f73c: DecompressPointer r0
    //     0xc0f73c: add             x0, x0, HEAP, lsl #32
    // 0xc0f740: r16 = 21.000000
    //     0xc0f740: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xc0f744: ldr             x16, [x16, #0x9b0]
    // 0xc0f748: r30 = Instance_Color
    //     0xc0f748: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0f74c: stp             lr, x16, [SP]
    // 0xc0f750: mov             x1, x0
    // 0xc0f754: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0f754: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0f758: ldr             x4, [x4, #0xaa0]
    // 0xc0f75c: r0 = copyWith()
    //     0xc0f75c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0f760: stur            x0, [fp, #-0x40]
    // 0xc0f764: r0 = Text()
    //     0xc0f764: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0f768: mov             x1, x0
    // 0xc0f76c: ldur            x0, [fp, #-0x38]
    // 0xc0f770: stur            x1, [fp, #-0x48]
    // 0xc0f774: StoreField: r1->field_b = r0
    //     0xc0f774: stur            w0, [x1, #0xb]
    // 0xc0f778: ldur            x0, [fp, #-0x40]
    // 0xc0f77c: StoreField: r1->field_13 = r0
    //     0xc0f77c: stur            w0, [x1, #0x13]
    // 0xc0f780: ldur            x0, [fp, #-0x30]
    // 0xc0f784: StoreField: r1->field_1b = r0
    //     0xc0f784: stur            w0, [x1, #0x1b]
    // 0xc0f788: r0 = Padding()
    //     0xc0f788: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0f78c: mov             x2, x0
    // 0xc0f790: r0 = Instance_EdgeInsets
    //     0xc0f790: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xc0f794: ldr             x0, [x0, #0x4c0]
    // 0xc0f798: stur            x2, [fp, #-0x30]
    // 0xc0f79c: StoreField: r2->field_f = r0
    //     0xc0f79c: stur            w0, [x2, #0xf]
    // 0xc0f7a0: ldur            x0, [fp, #-0x48]
    // 0xc0f7a4: StoreField: r2->field_b = r0
    //     0xc0f7a4: stur            w0, [x2, #0xb]
    // 0xc0f7a8: ldur            x0, [fp, #-0x28]
    // 0xc0f7ac: LoadField: r1 = r0->field_b
    //     0xc0f7ac: ldur            w1, [x0, #0xb]
    // 0xc0f7b0: LoadField: r3 = r0->field_f
    //     0xc0f7b0: ldur            w3, [x0, #0xf]
    // 0xc0f7b4: DecompressPointer r3
    //     0xc0f7b4: add             x3, x3, HEAP, lsl #32
    // 0xc0f7b8: LoadField: r4 = r3->field_b
    //     0xc0f7b8: ldur            w4, [x3, #0xb]
    // 0xc0f7bc: r3 = LoadInt32Instr(r1)
    //     0xc0f7bc: sbfx            x3, x1, #1, #0x1f
    // 0xc0f7c0: stur            x3, [fp, #-0x50]
    // 0xc0f7c4: r1 = LoadInt32Instr(r4)
    //     0xc0f7c4: sbfx            x1, x4, #1, #0x1f
    // 0xc0f7c8: cmp             x3, x1
    // 0xc0f7cc: b.ne            #0xc0f7d8
    // 0xc0f7d0: mov             x1, x0
    // 0xc0f7d4: r0 = _growToNextCapacity()
    //     0xc0f7d4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0f7d8: ldur            x2, [fp, #-0x28]
    // 0xc0f7dc: ldur            x3, [fp, #-0x50]
    // 0xc0f7e0: add             x0, x3, #1
    // 0xc0f7e4: lsl             x1, x0, #1
    // 0xc0f7e8: StoreField: r2->field_b = r1
    //     0xc0f7e8: stur            w1, [x2, #0xb]
    // 0xc0f7ec: LoadField: r1 = r2->field_f
    //     0xc0f7ec: ldur            w1, [x2, #0xf]
    // 0xc0f7f0: DecompressPointer r1
    //     0xc0f7f0: add             x1, x1, HEAP, lsl #32
    // 0xc0f7f4: ldur            x0, [fp, #-0x30]
    // 0xc0f7f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0f7f8: add             x25, x1, x3, lsl #2
    //     0xc0f7fc: add             x25, x25, #0xf
    //     0xc0f800: str             w0, [x25]
    //     0xc0f804: tbz             w0, #0, #0xc0f820
    //     0xc0f808: ldurb           w16, [x1, #-1]
    //     0xc0f80c: ldurb           w17, [x0, #-1]
    //     0xc0f810: and             x16, x17, x16, lsr #2
    //     0xc0f814: tst             x16, HEAP, lsr #32
    //     0xc0f818: b.eq            #0xc0f820
    //     0xc0f81c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0f820: ldur            x1, [fp, #-8]
    // 0xc0f824: LoadField: r0 = r1->field_b
    //     0xc0f824: ldur            w0, [x1, #0xb]
    // 0xc0f828: DecompressPointer r0
    //     0xc0f828: add             x0, x0, HEAP, lsl #32
    // 0xc0f82c: cmp             w0, NULL
    // 0xc0f830: b.eq            #0xc0ff64
    // 0xc0f834: LoadField: r3 = r0->field_2b
    //     0xc0f834: ldur            w3, [x0, #0x2b]
    // 0xc0f838: DecompressPointer r3
    //     0xc0f838: add             x3, x3, HEAP, lsl #32
    // 0xc0f83c: cmp             w3, NULL
    // 0xc0f840: b.ne            #0xc0f84c
    // 0xc0f844: r0 = Null
    //     0xc0f844: mov             x0, NULL
    // 0xc0f848: b               #0xc0f878
    // 0xc0f84c: LoadField: r0 = r3->field_7
    //     0xc0f84c: ldur            w0, [x3, #7]
    // 0xc0f850: DecompressPointer r0
    //     0xc0f850: add             x0, x0, HEAP, lsl #32
    // 0xc0f854: cmp             w0, NULL
    // 0xc0f858: b.ne            #0xc0f864
    // 0xc0f85c: r0 = Null
    //     0xc0f85c: mov             x0, NULL
    // 0xc0f860: b               #0xc0f878
    // 0xc0f864: LoadField: r4 = r0->field_7
    //     0xc0f864: ldur            w4, [x0, #7]
    // 0xc0f868: cbnz            w4, #0xc0f874
    // 0xc0f86c: r0 = false
    //     0xc0f86c: add             x0, NULL, #0x30  ; false
    // 0xc0f870: b               #0xc0f878
    // 0xc0f874: r0 = true
    //     0xc0f874: add             x0, NULL, #0x20  ; true
    // 0xc0f878: cmp             w0, NULL
    // 0xc0f87c: b.ne            #0xc0f888
    // 0xc0f880: r4 = false
    //     0xc0f880: add             x4, NULL, #0x30  ; false
    // 0xc0f884: b               #0xc0f88c
    // 0xc0f888: mov             x4, x0
    // 0xc0f88c: stur            x4, [fp, #-0x30]
    // 0xc0f890: cmp             w3, NULL
    // 0xc0f894: b.ne            #0xc0f8a8
    // 0xc0f898: mov             x1, x2
    // 0xc0f89c: mov             x0, x4
    // 0xc0f8a0: r2 = Null
    //     0xc0f8a0: mov             x2, NULL
    // 0xc0f8a4: b               #0xc0f8e8
    // 0xc0f8a8: LoadField: r0 = r3->field_7
    //     0xc0f8a8: ldur            w0, [x3, #7]
    // 0xc0f8ac: DecompressPointer r0
    //     0xc0f8ac: add             x0, x0, HEAP, lsl #32
    // 0xc0f8b0: cmp             w0, NULL
    // 0xc0f8b4: b.ne            #0xc0f8c0
    // 0xc0f8b8: r0 = Null
    //     0xc0f8b8: mov             x0, NULL
    // 0xc0f8bc: b               #0xc0f8dc
    // 0xc0f8c0: r3 = LoadClassIdInstr(r0)
    //     0xc0f8c0: ldur            x3, [x0, #-1]
    //     0xc0f8c4: ubfx            x3, x3, #0xc, #0x14
    // 0xc0f8c8: str             x0, [SP]
    // 0xc0f8cc: mov             x0, x3
    // 0xc0f8d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc0f8d0: sub             lr, x0, #1, lsl #12
    //     0xc0f8d4: ldr             lr, [x21, lr, lsl #3]
    //     0xc0f8d8: blr             lr
    // 0xc0f8dc: mov             x2, x0
    // 0xc0f8e0: ldur            x1, [fp, #-0x28]
    // 0xc0f8e4: ldur            x0, [fp, #-0x30]
    // 0xc0f8e8: str             x2, [SP]
    // 0xc0f8ec: r0 = _interpolateSingle()
    //     0xc0f8ec: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xc0f8f0: ldur            x1, [fp, #-0x10]
    // 0xc0f8f4: stur            x0, [fp, #-0x38]
    // 0xc0f8f8: r0 = of()
    //     0xc0f8f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0f8fc: LoadField: r1 = r0->field_87
    //     0xc0f8fc: ldur            w1, [x0, #0x87]
    // 0xc0f900: DecompressPointer r1
    //     0xc0f900: add             x1, x1, HEAP, lsl #32
    // 0xc0f904: LoadField: r0 = r1->field_2b
    //     0xc0f904: ldur            w0, [x1, #0x2b]
    // 0xc0f908: DecompressPointer r0
    //     0xc0f908: add             x0, x0, HEAP, lsl #32
    // 0xc0f90c: stur            x0, [fp, #-0x40]
    // 0xc0f910: r1 = Instance_Color
    //     0xc0f910: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0f914: d0 = 0.700000
    //     0xc0f914: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc0f918: ldr             d0, [x17, #0xf48]
    // 0xc0f91c: r0 = withOpacity()
    //     0xc0f91c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0f920: r16 = 12.000000
    //     0xc0f920: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0f924: ldr             x16, [x16, #0x9e8]
    // 0xc0f928: stp             x0, x16, [SP, #8]
    // 0xc0f92c: r16 = Instance_TextDecoration
    //     0xc0f92c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc0f930: ldr             x16, [x16, #0x10]
    // 0xc0f934: str             x16, [SP]
    // 0xc0f938: ldur            x1, [fp, #-0x40]
    // 0xc0f93c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xc0f93c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xc0f940: ldr             x4, [x4, #0xe38]
    // 0xc0f944: r0 = copyWith()
    //     0xc0f944: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0f948: stur            x0, [fp, #-0x40]
    // 0xc0f94c: r0 = Text()
    //     0xc0f94c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0f950: mov             x1, x0
    // 0xc0f954: ldur            x0, [fp, #-0x38]
    // 0xc0f958: stur            x1, [fp, #-0x48]
    // 0xc0f95c: StoreField: r1->field_b = r0
    //     0xc0f95c: stur            w0, [x1, #0xb]
    // 0xc0f960: ldur            x0, [fp, #-0x40]
    // 0xc0f964: StoreField: r1->field_13 = r0
    //     0xc0f964: stur            w0, [x1, #0x13]
    // 0xc0f968: r0 = Visibility()
    //     0xc0f968: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0f96c: mov             x1, x0
    // 0xc0f970: ldur            x0, [fp, #-0x48]
    // 0xc0f974: stur            x1, [fp, #-0x38]
    // 0xc0f978: StoreField: r1->field_b = r0
    //     0xc0f978: stur            w0, [x1, #0xb]
    // 0xc0f97c: r0 = Instance_SizedBox
    //     0xc0f97c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0f980: StoreField: r1->field_f = r0
    //     0xc0f980: stur            w0, [x1, #0xf]
    // 0xc0f984: ldur            x0, [fp, #-0x30]
    // 0xc0f988: StoreField: r1->field_13 = r0
    //     0xc0f988: stur            w0, [x1, #0x13]
    // 0xc0f98c: r0 = false
    //     0xc0f98c: add             x0, NULL, #0x30  ; false
    // 0xc0f990: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0f990: stur            w0, [x1, #0x17]
    // 0xc0f994: StoreField: r1->field_1b = r0
    //     0xc0f994: stur            w0, [x1, #0x1b]
    // 0xc0f998: StoreField: r1->field_1f = r0
    //     0xc0f998: stur            w0, [x1, #0x1f]
    // 0xc0f99c: StoreField: r1->field_23 = r0
    //     0xc0f99c: stur            w0, [x1, #0x23]
    // 0xc0f9a0: StoreField: r1->field_27 = r0
    //     0xc0f9a0: stur            w0, [x1, #0x27]
    // 0xc0f9a4: StoreField: r1->field_2b = r0
    //     0xc0f9a4: stur            w0, [x1, #0x2b]
    // 0xc0f9a8: r0 = InkWell()
    //     0xc0f9a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0f9ac: mov             x3, x0
    // 0xc0f9b0: ldur            x0, [fp, #-0x38]
    // 0xc0f9b4: stur            x3, [fp, #-0x30]
    // 0xc0f9b8: StoreField: r3->field_b = r0
    //     0xc0f9b8: stur            w0, [x3, #0xb]
    // 0xc0f9bc: ldur            x2, [fp, #-0x20]
    // 0xc0f9c0: r1 = Function '<anonymous closure>':.
    //     0xc0f9c0: add             x1, PP, #0x52, lsl #12  ; [pp+0x520a0] AnonymousClosure: (0xc119b4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xc0f9c4: ldr             x1, [x1, #0xa0]
    // 0xc0f9c8: r0 = AllocateClosure()
    //     0xc0f9c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0f9cc: mov             x1, x0
    // 0xc0f9d0: ldur            x0, [fp, #-0x30]
    // 0xc0f9d4: StoreField: r0->field_f = r1
    //     0xc0f9d4: stur            w1, [x0, #0xf]
    // 0xc0f9d8: r1 = true
    //     0xc0f9d8: add             x1, NULL, #0x20  ; true
    // 0xc0f9dc: StoreField: r0->field_43 = r1
    //     0xc0f9dc: stur            w1, [x0, #0x43]
    // 0xc0f9e0: r2 = Instance_BoxShape
    //     0xc0f9e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0f9e4: ldr             x2, [x2, #0x80]
    // 0xc0f9e8: StoreField: r0->field_47 = r2
    //     0xc0f9e8: stur            w2, [x0, #0x47]
    // 0xc0f9ec: StoreField: r0->field_6f = r1
    //     0xc0f9ec: stur            w1, [x0, #0x6f]
    // 0xc0f9f0: r2 = false
    //     0xc0f9f0: add             x2, NULL, #0x30  ; false
    // 0xc0f9f4: StoreField: r0->field_73 = r2
    //     0xc0f9f4: stur            w2, [x0, #0x73]
    // 0xc0f9f8: StoreField: r0->field_83 = r1
    //     0xc0f9f8: stur            w1, [x0, #0x83]
    // 0xc0f9fc: StoreField: r0->field_7b = r2
    //     0xc0f9fc: stur            w2, [x0, #0x7b]
    // 0xc0fa00: ldur            x2, [fp, #-0x28]
    // 0xc0fa04: LoadField: r1 = r2->field_b
    //     0xc0fa04: ldur            w1, [x2, #0xb]
    // 0xc0fa08: LoadField: r3 = r2->field_f
    //     0xc0fa08: ldur            w3, [x2, #0xf]
    // 0xc0fa0c: DecompressPointer r3
    //     0xc0fa0c: add             x3, x3, HEAP, lsl #32
    // 0xc0fa10: LoadField: r4 = r3->field_b
    //     0xc0fa10: ldur            w4, [x3, #0xb]
    // 0xc0fa14: r3 = LoadInt32Instr(r1)
    //     0xc0fa14: sbfx            x3, x1, #1, #0x1f
    // 0xc0fa18: stur            x3, [fp, #-0x50]
    // 0xc0fa1c: r1 = LoadInt32Instr(r4)
    //     0xc0fa1c: sbfx            x1, x4, #1, #0x1f
    // 0xc0fa20: cmp             x3, x1
    // 0xc0fa24: b.ne            #0xc0fa30
    // 0xc0fa28: mov             x1, x2
    // 0xc0fa2c: r0 = _growToNextCapacity()
    //     0xc0fa2c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0fa30: ldur            x2, [fp, #-0x28]
    // 0xc0fa34: ldur            x3, [fp, #-0x50]
    // 0xc0fa38: add             x4, x3, #1
    // 0xc0fa3c: stur            x4, [fp, #-0x58]
    // 0xc0fa40: lsl             x0, x4, #1
    // 0xc0fa44: StoreField: r2->field_b = r0
    //     0xc0fa44: stur            w0, [x2, #0xb]
    // 0xc0fa48: LoadField: r5 = r2->field_f
    //     0xc0fa48: ldur            w5, [x2, #0xf]
    // 0xc0fa4c: DecompressPointer r5
    //     0xc0fa4c: add             x5, x5, HEAP, lsl #32
    // 0xc0fa50: mov             x1, x5
    // 0xc0fa54: ldur            x0, [fp, #-0x30]
    // 0xc0fa58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0fa58: add             x25, x1, x3, lsl #2
    //     0xc0fa5c: add             x25, x25, #0xf
    //     0xc0fa60: str             w0, [x25]
    //     0xc0fa64: tbz             w0, #0, #0xc0fa80
    //     0xc0fa68: ldurb           w16, [x1, #-1]
    //     0xc0fa6c: ldurb           w17, [x0, #-1]
    //     0xc0fa70: and             x16, x17, x16, lsr #2
    //     0xc0fa74: tst             x16, HEAP, lsr #32
    //     0xc0fa78: b.eq            #0xc0fa80
    //     0xc0fa7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0fa80: LoadField: r0 = r5->field_b
    //     0xc0fa80: ldur            w0, [x5, #0xb]
    // 0xc0fa84: r1 = LoadInt32Instr(r0)
    //     0xc0fa84: sbfx            x1, x0, #1, #0x1f
    // 0xc0fa88: cmp             x4, x1
    // 0xc0fa8c: b.ne            #0xc0fa98
    // 0xc0fa90: mov             x1, x2
    // 0xc0fa94: r0 = _growToNextCapacity()
    //     0xc0fa94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0fa98: ldur            x2, [fp, #-8]
    // 0xc0fa9c: ldur            x0, [fp, #-0x28]
    // 0xc0faa0: ldur            x1, [fp, #-0x58]
    // 0xc0faa4: add             x3, x1, #1
    // 0xc0faa8: lsl             x4, x3, #1
    // 0xc0faac: StoreField: r0->field_b = r4
    //     0xc0faac: stur            w4, [x0, #0xb]
    // 0xc0fab0: LoadField: r3 = r0->field_f
    //     0xc0fab0: ldur            w3, [x0, #0xf]
    // 0xc0fab4: DecompressPointer r3
    //     0xc0fab4: add             x3, x3, HEAP, lsl #32
    // 0xc0fab8: add             x4, x3, x1, lsl #2
    // 0xc0fabc: r16 = Instance_SizedBox
    //     0xc0fabc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0fac0: ldr             x16, [x16, #0x8f0]
    // 0xc0fac4: StoreField: r4->field_f = r16
    //     0xc0fac4: stur            w16, [x4, #0xf]
    // 0xc0fac8: ldur            x1, [fp, #-0x10]
    // 0xc0facc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc0facc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc0fad0: r0 = _of()
    //     0xc0fad0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc0fad4: LoadField: r1 = r0->field_7
    //     0xc0fad4: ldur            w1, [x0, #7]
    // 0xc0fad8: DecompressPointer r1
    //     0xc0fad8: add             x1, x1, HEAP, lsl #32
    // 0xc0fadc: LoadField: d0 = r1->field_f
    //     0xc0fadc: ldur            d0, [x1, #0xf]
    // 0xc0fae0: d1 = 0.400000
    //     0xc0fae0: ldr             d1, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc0fae4: fmul            d2, d0, d1
    // 0xc0fae8: ldur            x1, [fp, #-0x10]
    // 0xc0faec: stur            d2, [fp, #-0x60]
    // 0xc0faf0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc0faf0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc0faf4: r0 = _of()
    //     0xc0faf4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc0faf8: LoadField: r1 = r0->field_7
    //     0xc0faf8: ldur            w1, [x0, #7]
    // 0xc0fafc: DecompressPointer r1
    //     0xc0fafc: add             x1, x1, HEAP, lsl #32
    // 0xc0fb00: LoadField: d0 = r1->field_7
    //     0xc0fb00: ldur            d0, [x1, #7]
    // 0xc0fb04: ldur            x1, [fp, #-8]
    // 0xc0fb08: stur            d0, [fp, #-0x68]
    // 0xc0fb0c: LoadField: r0 = r1->field_b
    //     0xc0fb0c: ldur            w0, [x1, #0xb]
    // 0xc0fb10: DecompressPointer r0
    //     0xc0fb10: add             x0, x0, HEAP, lsl #32
    // 0xc0fb14: cmp             w0, NULL
    // 0xc0fb18: b.eq            #0xc0ff68
    // 0xc0fb1c: LoadField: r2 = r0->field_b
    //     0xc0fb1c: ldur            w2, [x0, #0xb]
    // 0xc0fb20: DecompressPointer r2
    //     0xc0fb20: add             x2, x2, HEAP, lsl #32
    // 0xc0fb24: r0 = LoadClassIdInstr(r2)
    //     0xc0fb24: ldur            x0, [x2, #-1]
    //     0xc0fb28: ubfx            x0, x0, #0xc, #0x14
    // 0xc0fb2c: str             x2, [SP]
    // 0xc0fb30: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc0fb30: movz            x17, #0xc898
    //     0xc0fb34: add             lr, x0, x17
    //     0xc0fb38: ldr             lr, [x21, lr, lsl #3]
    //     0xc0fb3c: blr             lr
    // 0xc0fb40: mov             x3, x0
    // 0xc0fb44: ldur            x0, [fp, #-8]
    // 0xc0fb48: stur            x3, [fp, #-0x38]
    // 0xc0fb4c: LoadField: r4 = r0->field_13
    //     0xc0fb4c: ldur            w4, [x0, #0x13]
    // 0xc0fb50: DecompressPointer r4
    //     0xc0fb50: add             x4, x4, HEAP, lsl #32
    // 0xc0fb54: r16 = Sentinel
    //     0xc0fb54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc0fb58: cmp             w4, w16
    // 0xc0fb5c: b.eq            #0xc0ff6c
    // 0xc0fb60: ldur            x2, [fp, #-0x20]
    // 0xc0fb64: stur            x4, [fp, #-0x30]
    // 0xc0fb68: r1 = Function '<anonymous closure>':.
    //     0xc0fb68: add             x1, PP, #0x52, lsl #12  ; [pp+0x520a8] AnonymousClosure: (0x98ea14), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xc0fb6c: ldr             x1, [x1, #0xa8]
    // 0xc0fb70: r0 = AllocateClosure()
    //     0xc0fb70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0fb74: ldur            x2, [fp, #-0x20]
    // 0xc0fb78: r1 = Function '<anonymous closure>':.
    //     0xc0fb78: add             x1, PP, #0x52, lsl #12  ; [pp+0x520b0] AnonymousClosure: (0xc0ffa8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xc0fb7c: ldr             x1, [x1, #0xb0]
    // 0xc0fb80: stur            x0, [fp, #-0x20]
    // 0xc0fb84: r0 = AllocateClosure()
    //     0xc0fb84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0fb88: stur            x0, [fp, #-0x40]
    // 0xc0fb8c: r0 = PageView()
    //     0xc0fb8c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc0fb90: stur            x0, [fp, #-0x48]
    // 0xc0fb94: r16 = Instance_BouncingScrollPhysics
    //     0xc0fb94: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xc0fb98: ldr             x16, [x16, #0x890]
    // 0xc0fb9c: ldur            lr, [fp, #-0x30]
    // 0xc0fba0: stp             lr, x16, [SP]
    // 0xc0fba4: mov             x1, x0
    // 0xc0fba8: ldur            x2, [fp, #-0x40]
    // 0xc0fbac: ldur            x3, [fp, #-0x38]
    // 0xc0fbb0: ldur            x5, [fp, #-0x20]
    // 0xc0fbb4: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xc0fbb4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xc0fbb8: ldr             x4, [x4, #0xe40]
    // 0xc0fbbc: r0 = PageView.builder()
    //     0xc0fbbc: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc0fbc0: ldur            d0, [fp, #-0x68]
    // 0xc0fbc4: r0 = inline_Allocate_Double()
    //     0xc0fbc4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc0fbc8: add             x0, x0, #0x10
    //     0xc0fbcc: cmp             x1, x0
    //     0xc0fbd0: b.ls            #0xc0ff78
    //     0xc0fbd4: str             x0, [THR, #0x50]  ; THR::top
    //     0xc0fbd8: sub             x0, x0, #0xf
    //     0xc0fbdc: movz            x1, #0xe15c
    //     0xc0fbe0: movk            x1, #0x3, lsl #16
    //     0xc0fbe4: stur            x1, [x0, #-1]
    // 0xc0fbe8: StoreField: r0->field_7 = d0
    //     0xc0fbe8: stur            d0, [x0, #7]
    // 0xc0fbec: stur            x0, [fp, #-0x20]
    // 0xc0fbf0: r0 = SizedBox()
    //     0xc0fbf0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc0fbf4: mov             x2, x0
    // 0xc0fbf8: ldur            x0, [fp, #-0x20]
    // 0xc0fbfc: stur            x2, [fp, #-0x30]
    // 0xc0fc00: StoreField: r2->field_f = r0
    //     0xc0fc00: stur            w0, [x2, #0xf]
    // 0xc0fc04: ldur            d0, [fp, #-0x60]
    // 0xc0fc08: r0 = inline_Allocate_Double()
    //     0xc0fc08: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc0fc0c: add             x0, x0, #0x10
    //     0xc0fc10: cmp             x1, x0
    //     0xc0fc14: b.ls            #0xc0ff88
    //     0xc0fc18: str             x0, [THR, #0x50]  ; THR::top
    //     0xc0fc1c: sub             x0, x0, #0xf
    //     0xc0fc20: movz            x1, #0xe15c
    //     0xc0fc24: movk            x1, #0x3, lsl #16
    //     0xc0fc28: stur            x1, [x0, #-1]
    // 0xc0fc2c: StoreField: r0->field_7 = d0
    //     0xc0fc2c: stur            d0, [x0, #7]
    // 0xc0fc30: StoreField: r2->field_13 = r0
    //     0xc0fc30: stur            w0, [x2, #0x13]
    // 0xc0fc34: ldur            x0, [fp, #-0x48]
    // 0xc0fc38: StoreField: r2->field_b = r0
    //     0xc0fc38: stur            w0, [x2, #0xb]
    // 0xc0fc3c: ldur            x0, [fp, #-0x28]
    // 0xc0fc40: LoadField: r1 = r0->field_b
    //     0xc0fc40: ldur            w1, [x0, #0xb]
    // 0xc0fc44: LoadField: r3 = r0->field_f
    //     0xc0fc44: ldur            w3, [x0, #0xf]
    // 0xc0fc48: DecompressPointer r3
    //     0xc0fc48: add             x3, x3, HEAP, lsl #32
    // 0xc0fc4c: LoadField: r4 = r3->field_b
    //     0xc0fc4c: ldur            w4, [x3, #0xb]
    // 0xc0fc50: r3 = LoadInt32Instr(r1)
    //     0xc0fc50: sbfx            x3, x1, #1, #0x1f
    // 0xc0fc54: stur            x3, [fp, #-0x50]
    // 0xc0fc58: r1 = LoadInt32Instr(r4)
    //     0xc0fc58: sbfx            x1, x4, #1, #0x1f
    // 0xc0fc5c: cmp             x3, x1
    // 0xc0fc60: b.ne            #0xc0fc6c
    // 0xc0fc64: mov             x1, x0
    // 0xc0fc68: r0 = _growToNextCapacity()
    //     0xc0fc68: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0fc6c: ldur            x4, [fp, #-8]
    // 0xc0fc70: ldur            x2, [fp, #-0x28]
    // 0xc0fc74: ldur            x3, [fp, #-0x50]
    // 0xc0fc78: add             x0, x3, #1
    // 0xc0fc7c: lsl             x1, x0, #1
    // 0xc0fc80: StoreField: r2->field_b = r1
    //     0xc0fc80: stur            w1, [x2, #0xb]
    // 0xc0fc84: LoadField: r1 = r2->field_f
    //     0xc0fc84: ldur            w1, [x2, #0xf]
    // 0xc0fc88: DecompressPointer r1
    //     0xc0fc88: add             x1, x1, HEAP, lsl #32
    // 0xc0fc8c: ldur            x0, [fp, #-0x30]
    // 0xc0fc90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0fc90: add             x25, x1, x3, lsl #2
    //     0xc0fc94: add             x25, x25, #0xf
    //     0xc0fc98: str             w0, [x25]
    //     0xc0fc9c: tbz             w0, #0, #0xc0fcb8
    //     0xc0fca0: ldurb           w16, [x1, #-1]
    //     0xc0fca4: ldurb           w17, [x0, #-1]
    //     0xc0fca8: and             x16, x17, x16, lsr #2
    //     0xc0fcac: tst             x16, HEAP, lsr #32
    //     0xc0fcb0: b.eq            #0xc0fcb8
    //     0xc0fcb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0fcb8: LoadField: r0 = r4->field_b
    //     0xc0fcb8: ldur            w0, [x4, #0xb]
    // 0xc0fcbc: DecompressPointer r0
    //     0xc0fcbc: add             x0, x0, HEAP, lsl #32
    // 0xc0fcc0: cmp             w0, NULL
    // 0xc0fcc4: b.eq            #0xc0ffa0
    // 0xc0fcc8: LoadField: r1 = r0->field_b
    //     0xc0fcc8: ldur            w1, [x0, #0xb]
    // 0xc0fccc: DecompressPointer r1
    //     0xc0fccc: add             x1, x1, HEAP, lsl #32
    // 0xc0fcd0: r0 = LoadClassIdInstr(r1)
    //     0xc0fcd0: ldur            x0, [x1, #-1]
    //     0xc0fcd4: ubfx            x0, x0, #0xc, #0x14
    // 0xc0fcd8: str             x1, [SP]
    // 0xc0fcdc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc0fcdc: movz            x17, #0xc898
    //     0xc0fce0: add             lr, x0, x17
    //     0xc0fce4: ldr             lr, [x21, lr, lsl #3]
    //     0xc0fce8: blr             lr
    // 0xc0fcec: r1 = LoadInt32Instr(r0)
    //     0xc0fcec: sbfx            x1, x0, #1, #0x1f
    // 0xc0fcf0: cmp             x1, #1
    // 0xc0fcf4: b.le            #0xc0fecc
    // 0xc0fcf8: ldur            x2, [fp, #-8]
    // 0xc0fcfc: ldur            x1, [fp, #-0x28]
    // 0xc0fd00: LoadField: r0 = r2->field_b
    //     0xc0fd00: ldur            w0, [x2, #0xb]
    // 0xc0fd04: DecompressPointer r0
    //     0xc0fd04: add             x0, x0, HEAP, lsl #32
    // 0xc0fd08: cmp             w0, NULL
    // 0xc0fd0c: b.eq            #0xc0ffa4
    // 0xc0fd10: LoadField: r3 = r0->field_b
    //     0xc0fd10: ldur            w3, [x0, #0xb]
    // 0xc0fd14: DecompressPointer r3
    //     0xc0fd14: add             x3, x3, HEAP, lsl #32
    // 0xc0fd18: r0 = LoadClassIdInstr(r3)
    //     0xc0fd18: ldur            x0, [x3, #-1]
    //     0xc0fd1c: ubfx            x0, x0, #0xc, #0x14
    // 0xc0fd20: str             x3, [SP]
    // 0xc0fd24: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc0fd24: movz            x17, #0xc898
    //     0xc0fd28: add             lr, x0, x17
    //     0xc0fd2c: ldr             lr, [x21, lr, lsl #3]
    //     0xc0fd30: blr             lr
    // 0xc0fd34: mov             x2, x0
    // 0xc0fd38: ldur            x0, [fp, #-8]
    // 0xc0fd3c: stur            x2, [fp, #-0x20]
    // 0xc0fd40: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xc0fd40: ldur            x3, [x0, #0x17]
    // 0xc0fd44: ldur            x1, [fp, #-0x10]
    // 0xc0fd48: stur            x3, [fp, #-0x50]
    // 0xc0fd4c: r0 = of()
    //     0xc0fd4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0fd50: LoadField: r1 = r0->field_5b
    //     0xc0fd50: ldur            w1, [x0, #0x5b]
    // 0xc0fd54: DecompressPointer r1
    //     0xc0fd54: add             x1, x1, HEAP, lsl #32
    // 0xc0fd58: ldur            x0, [fp, #-0x20]
    // 0xc0fd5c: stur            x1, [fp, #-8]
    // 0xc0fd60: r2 = LoadInt32Instr(r0)
    //     0xc0fd60: sbfx            x2, x0, #1, #0x1f
    // 0xc0fd64: stur            x2, [fp, #-0x58]
    // 0xc0fd68: r0 = CarouselIndicator()
    //     0xc0fd68: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xc0fd6c: mov             x3, x0
    // 0xc0fd70: ldur            x0, [fp, #-0x58]
    // 0xc0fd74: stur            x3, [fp, #-0x10]
    // 0xc0fd78: StoreField: r3->field_b = r0
    //     0xc0fd78: stur            x0, [x3, #0xb]
    // 0xc0fd7c: ldur            x0, [fp, #-0x50]
    // 0xc0fd80: StoreField: r3->field_13 = r0
    //     0xc0fd80: stur            x0, [x3, #0x13]
    // 0xc0fd84: ldur            x0, [fp, #-8]
    // 0xc0fd88: StoreField: r3->field_1b = r0
    //     0xc0fd88: stur            w0, [x3, #0x1b]
    // 0xc0fd8c: r0 = Instance_Color
    //     0xc0fd8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xc0fd90: ldr             x0, [x0, #0x90]
    // 0xc0fd94: StoreField: r3->field_1f = r0
    //     0xc0fd94: stur            w0, [x3, #0x1f]
    // 0xc0fd98: r1 = Null
    //     0xc0fd98: mov             x1, NULL
    // 0xc0fd9c: r2 = 2
    //     0xc0fd9c: movz            x2, #0x2
    // 0xc0fda0: r0 = AllocateArray()
    //     0xc0fda0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0fda4: mov             x2, x0
    // 0xc0fda8: ldur            x0, [fp, #-0x10]
    // 0xc0fdac: stur            x2, [fp, #-8]
    // 0xc0fdb0: StoreField: r2->field_f = r0
    //     0xc0fdb0: stur            w0, [x2, #0xf]
    // 0xc0fdb4: r1 = <Widget>
    //     0xc0fdb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0fdb8: r0 = AllocateGrowableArray()
    //     0xc0fdb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0fdbc: mov             x1, x0
    // 0xc0fdc0: ldur            x0, [fp, #-8]
    // 0xc0fdc4: stur            x1, [fp, #-0x10]
    // 0xc0fdc8: StoreField: r1->field_f = r0
    //     0xc0fdc8: stur            w0, [x1, #0xf]
    // 0xc0fdcc: r0 = 2
    //     0xc0fdcc: movz            x0, #0x2
    // 0xc0fdd0: StoreField: r1->field_b = r0
    //     0xc0fdd0: stur            w0, [x1, #0xb]
    // 0xc0fdd4: r0 = Row()
    //     0xc0fdd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0fdd8: mov             x1, x0
    // 0xc0fddc: r0 = Instance_Axis
    //     0xc0fddc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0fde0: stur            x1, [fp, #-8]
    // 0xc0fde4: StoreField: r1->field_f = r0
    //     0xc0fde4: stur            w0, [x1, #0xf]
    // 0xc0fde8: r0 = Instance_MainAxisAlignment
    //     0xc0fde8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc0fdec: ldr             x0, [x0, #0xab0]
    // 0xc0fdf0: StoreField: r1->field_13 = r0
    //     0xc0fdf0: stur            w0, [x1, #0x13]
    // 0xc0fdf4: r0 = Instance_MainAxisSize
    //     0xc0fdf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0fdf8: ldr             x0, [x0, #0xa10]
    // 0xc0fdfc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0fdfc: stur            w0, [x1, #0x17]
    // 0xc0fe00: r0 = Instance_CrossAxisAlignment
    //     0xc0fe00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0fe04: ldr             x0, [x0, #0xa18]
    // 0xc0fe08: StoreField: r1->field_1b = r0
    //     0xc0fe08: stur            w0, [x1, #0x1b]
    // 0xc0fe0c: r0 = Instance_VerticalDirection
    //     0xc0fe0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0fe10: ldr             x0, [x0, #0xa20]
    // 0xc0fe14: StoreField: r1->field_23 = r0
    //     0xc0fe14: stur            w0, [x1, #0x23]
    // 0xc0fe18: r2 = Instance_Clip
    //     0xc0fe18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0fe1c: ldr             x2, [x2, #0x38]
    // 0xc0fe20: StoreField: r1->field_2b = r2
    //     0xc0fe20: stur            w2, [x1, #0x2b]
    // 0xc0fe24: StoreField: r1->field_2f = rZR
    //     0xc0fe24: stur            xzr, [x1, #0x2f]
    // 0xc0fe28: ldur            x3, [fp, #-0x10]
    // 0xc0fe2c: StoreField: r1->field_b = r3
    //     0xc0fe2c: stur            w3, [x1, #0xb]
    // 0xc0fe30: r0 = Padding()
    //     0xc0fe30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0fe34: mov             x2, x0
    // 0xc0fe38: r0 = Instance_EdgeInsets
    //     0xc0fe38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc0fe3c: ldr             x0, [x0, #0xa00]
    // 0xc0fe40: stur            x2, [fp, #-0x10]
    // 0xc0fe44: StoreField: r2->field_f = r0
    //     0xc0fe44: stur            w0, [x2, #0xf]
    // 0xc0fe48: ldur            x0, [fp, #-8]
    // 0xc0fe4c: StoreField: r2->field_b = r0
    //     0xc0fe4c: stur            w0, [x2, #0xb]
    // 0xc0fe50: ldur            x0, [fp, #-0x28]
    // 0xc0fe54: LoadField: r1 = r0->field_b
    //     0xc0fe54: ldur            w1, [x0, #0xb]
    // 0xc0fe58: LoadField: r3 = r0->field_f
    //     0xc0fe58: ldur            w3, [x0, #0xf]
    // 0xc0fe5c: DecompressPointer r3
    //     0xc0fe5c: add             x3, x3, HEAP, lsl #32
    // 0xc0fe60: LoadField: r4 = r3->field_b
    //     0xc0fe60: ldur            w4, [x3, #0xb]
    // 0xc0fe64: r3 = LoadInt32Instr(r1)
    //     0xc0fe64: sbfx            x3, x1, #1, #0x1f
    // 0xc0fe68: stur            x3, [fp, #-0x50]
    // 0xc0fe6c: r1 = LoadInt32Instr(r4)
    //     0xc0fe6c: sbfx            x1, x4, #1, #0x1f
    // 0xc0fe70: cmp             x3, x1
    // 0xc0fe74: b.ne            #0xc0fe80
    // 0xc0fe78: mov             x1, x0
    // 0xc0fe7c: r0 = _growToNextCapacity()
    //     0xc0fe7c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0fe80: ldur            x2, [fp, #-0x28]
    // 0xc0fe84: ldur            x3, [fp, #-0x50]
    // 0xc0fe88: add             x0, x3, #1
    // 0xc0fe8c: lsl             x1, x0, #1
    // 0xc0fe90: StoreField: r2->field_b = r1
    //     0xc0fe90: stur            w1, [x2, #0xb]
    // 0xc0fe94: LoadField: r1 = r2->field_f
    //     0xc0fe94: ldur            w1, [x2, #0xf]
    // 0xc0fe98: DecompressPointer r1
    //     0xc0fe98: add             x1, x1, HEAP, lsl #32
    // 0xc0fe9c: ldur            x0, [fp, #-0x10]
    // 0xc0fea0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0fea0: add             x25, x1, x3, lsl #2
    //     0xc0fea4: add             x25, x25, #0xf
    //     0xc0fea8: str             w0, [x25]
    //     0xc0feac: tbz             w0, #0, #0xc0fec8
    //     0xc0feb0: ldurb           w16, [x1, #-1]
    //     0xc0feb4: ldurb           w17, [x0, #-1]
    //     0xc0feb8: and             x16, x17, x16, lsr #2
    //     0xc0febc: tst             x16, HEAP, lsr #32
    //     0xc0fec0: b.eq            #0xc0fec8
    //     0xc0fec4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0fec8: b               #0xc0fed0
    // 0xc0fecc: ldur            x2, [fp, #-0x28]
    // 0xc0fed0: ldur            x0, [fp, #-0x18]
    // 0xc0fed4: r0 = Column()
    //     0xc0fed4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0fed8: mov             x1, x0
    // 0xc0fedc: r0 = Instance_Axis
    //     0xc0fedc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0fee0: stur            x1, [fp, #-8]
    // 0xc0fee4: StoreField: r1->field_f = r0
    //     0xc0fee4: stur            w0, [x1, #0xf]
    // 0xc0fee8: r0 = Instance_MainAxisAlignment
    //     0xc0fee8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0feec: ldr             x0, [x0, #0xa08]
    // 0xc0fef0: StoreField: r1->field_13 = r0
    //     0xc0fef0: stur            w0, [x1, #0x13]
    // 0xc0fef4: r0 = Instance_MainAxisSize
    //     0xc0fef4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc0fef8: ldr             x0, [x0, #0xdd0]
    // 0xc0fefc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0fefc: stur            w0, [x1, #0x17]
    // 0xc0ff00: ldur            x0, [fp, #-0x18]
    // 0xc0ff04: StoreField: r1->field_1b = r0
    //     0xc0ff04: stur            w0, [x1, #0x1b]
    // 0xc0ff08: r0 = Instance_VerticalDirection
    //     0xc0ff08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0ff0c: ldr             x0, [x0, #0xa20]
    // 0xc0ff10: StoreField: r1->field_23 = r0
    //     0xc0ff10: stur            w0, [x1, #0x23]
    // 0xc0ff14: r0 = Instance_Clip
    //     0xc0ff14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0ff18: ldr             x0, [x0, #0x38]
    // 0xc0ff1c: StoreField: r1->field_2b = r0
    //     0xc0ff1c: stur            w0, [x1, #0x2b]
    // 0xc0ff20: StoreField: r1->field_2f = rZR
    //     0xc0ff20: stur            xzr, [x1, #0x2f]
    // 0xc0ff24: ldur            x0, [fp, #-0x28]
    // 0xc0ff28: StoreField: r1->field_b = r0
    //     0xc0ff28: stur            w0, [x1, #0xb]
    // 0xc0ff2c: r0 = Padding()
    //     0xc0ff2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0ff30: r1 = Instance_EdgeInsets
    //     0xc0ff30: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xc0ff34: ldr             x1, [x1, #0x110]
    // 0xc0ff38: StoreField: r0->field_f = r1
    //     0xc0ff38: stur            w1, [x0, #0xf]
    // 0xc0ff3c: ldur            x1, [fp, #-8]
    // 0xc0ff40: StoreField: r0->field_b = r1
    //     0xc0ff40: stur            w1, [x0, #0xb]
    // 0xc0ff44: LeaveFrame
    //     0xc0ff44: mov             SP, fp
    //     0xc0ff48: ldp             fp, lr, [SP], #0x10
    // 0xc0ff4c: ret
    //     0xc0ff4c: ret             
    // 0xc0ff50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0ff50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0ff54: b               #0xc0f5d4
    // 0xc0ff58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ff58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0ff5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ff5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0ff60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ff60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0ff64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ff64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0ff68: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc0ff68: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc0ff6c: r9 = _pageController
    //     0xc0ff6c: add             x9, PP, #0x52, lsl #12  ; [pp+0x520b8] Field <_TestimonialCarouselState@1745398749._pageController@1745398749>: late (offset: 0x14)
    //     0xc0ff70: ldr             x9, [x9, #0xb8]
    // 0xc0ff74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc0ff74: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc0ff78: SaveReg d0
    //     0xc0ff78: str             q0, [SP, #-0x10]!
    // 0xc0ff7c: r0 = AllocateDouble()
    //     0xc0ff7c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc0ff80: RestoreReg d0
    //     0xc0ff80: ldr             q0, [SP], #0x10
    // 0xc0ff84: b               #0xc0fbe8
    // 0xc0ff88: SaveReg d0
    //     0xc0ff88: str             q0, [SP, #-0x10]!
    // 0xc0ff8c: SaveReg r2
    //     0xc0ff8c: str             x2, [SP, #-8]!
    // 0xc0ff90: r0 = AllocateDouble()
    //     0xc0ff90: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc0ff94: RestoreReg r2
    //     0xc0ff94: ldr             x2, [SP], #8
    // 0xc0ff98: RestoreReg d0
    //     0xc0ff98: ldr             q0, [SP], #0x10
    // 0xc0ff9c: b               #0xc0fc2c
    // 0xc0ffa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ffa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0ffa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0ffa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc0ffa8, size: 0x70
    // 0xc0ffa8: EnterFrame
    //     0xc0ffa8: stp             fp, lr, [SP, #-0x10]!
    //     0xc0ffac: mov             fp, SP
    // 0xc0ffb0: ldr             x0, [fp, #0x20]
    // 0xc0ffb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc0ffb4: ldur            w1, [x0, #0x17]
    // 0xc0ffb8: DecompressPointer r1
    //     0xc0ffb8: add             x1, x1, HEAP, lsl #32
    // 0xc0ffbc: CheckStackOverflow
    //     0xc0ffbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0ffc0: cmp             SP, x16
    //     0xc0ffc4: b.ls            #0xc1000c
    // 0xc0ffc8: LoadField: r0 = r1->field_f
    //     0xc0ffc8: ldur            w0, [x1, #0xf]
    // 0xc0ffcc: DecompressPointer r0
    //     0xc0ffcc: add             x0, x0, HEAP, lsl #32
    // 0xc0ffd0: LoadField: r1 = r0->field_b
    //     0xc0ffd0: ldur            w1, [x0, #0xb]
    // 0xc0ffd4: DecompressPointer r1
    //     0xc0ffd4: add             x1, x1, HEAP, lsl #32
    // 0xc0ffd8: cmp             w1, NULL
    // 0xc0ffdc: b.eq            #0xc10014
    // 0xc0ffe0: LoadField: r2 = r1->field_b
    //     0xc0ffe0: ldur            w2, [x1, #0xb]
    // 0xc0ffe4: DecompressPointer r2
    //     0xc0ffe4: add             x2, x2, HEAP, lsl #32
    // 0xc0ffe8: ldr             x1, [fp, #0x10]
    // 0xc0ffec: r3 = LoadInt32Instr(r1)
    //     0xc0ffec: sbfx            x3, x1, #1, #0x1f
    //     0xc0fff0: tbz             w1, #0, #0xc0fff8
    //     0xc0fff4: ldur            x3, [x1, #7]
    // 0xc0fff8: mov             x1, x0
    // 0xc0fffc: r0 = lineThemeSlider()
    //     0xc0fffc: bl              #0xc10018  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider
    // 0xc10000: LeaveFrame
    //     0xc10000: mov             SP, fp
    //     0xc10004: ldp             fp, lr, [SP], #0x10
    // 0xc10008: ret
    //     0xc10008: ret             
    // 0xc1000c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1000c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc10010: b               #0xc0ffc8
    // 0xc10014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xc10018, size: 0xebc
    // 0xc10018: EnterFrame
    //     0xc10018: stp             fp, lr, [SP, #-0x10]!
    //     0xc1001c: mov             fp, SP
    // 0xc10020: AllocStack(0x80)
    //     0xc10020: sub             SP, SP, #0x80
    // 0xc10024: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xc10024: stur            x1, [fp, #-8]
    //     0xc10028: stur            x2, [fp, #-0x10]
    //     0xc1002c: stur            x3, [fp, #-0x18]
    // 0xc10030: CheckStackOverflow
    //     0xc10030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc10034: cmp             SP, x16
    //     0xc10038: b.ls            #0xc10e88
    // 0xc1003c: r1 = 2
    //     0xc1003c: movz            x1, #0x2
    // 0xc10040: r0 = AllocateContext()
    //     0xc10040: bl              #0x16f6108  ; AllocateContextStub
    // 0xc10044: mov             x2, x0
    // 0xc10048: ldur            x0, [fp, #-0x10]
    // 0xc1004c: stur            x2, [fp, #-0x20]
    // 0xc10050: StoreField: r2->field_f = r0
    //     0xc10050: stur            w0, [x2, #0xf]
    // 0xc10054: ldur            x3, [fp, #-0x18]
    // 0xc10058: r0 = BoxInt64Instr(r3)
    //     0xc10058: sbfiz           x0, x3, #1, #0x1f
    //     0xc1005c: cmp             x3, x0, asr #1
    //     0xc10060: b.eq            #0xc1006c
    //     0xc10064: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc10068: stur            x3, [x0, #7]
    // 0xc1006c: StoreField: r2->field_13 = r0
    //     0xc1006c: stur            w0, [x2, #0x13]
    // 0xc10070: r1 = Instance_Color
    //     0xc10070: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc10074: d0 = 0.100000
    //     0xc10074: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc10078: r0 = withOpacity()
    //     0xc10078: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc1007c: r16 = 1.000000
    //     0xc1007c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc10080: str             x16, [SP]
    // 0xc10084: mov             x2, x0
    // 0xc10088: r1 = Null
    //     0xc10088: mov             x1, NULL
    // 0xc1008c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc1008c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc10090: ldr             x4, [x4, #0x108]
    // 0xc10094: r0 = Border.all()
    //     0xc10094: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc10098: stur            x0, [fp, #-0x10]
    // 0xc1009c: r0 = BoxDecoration()
    //     0xc1009c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc100a0: mov             x1, x0
    // 0xc100a4: ldur            x0, [fp, #-0x10]
    // 0xc100a8: stur            x1, [fp, #-0x28]
    // 0xc100ac: StoreField: r1->field_f = r0
    //     0xc100ac: stur            w0, [x1, #0xf]
    // 0xc100b0: r0 = Instance_BoxShape
    //     0xc100b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc100b4: ldr             x0, [x0, #0x80]
    // 0xc100b8: StoreField: r1->field_23 = r0
    //     0xc100b8: stur            w0, [x1, #0x23]
    // 0xc100bc: ldur            x2, [fp, #-0x20]
    // 0xc100c0: LoadField: r0 = r2->field_f
    //     0xc100c0: ldur            w0, [x2, #0xf]
    // 0xc100c4: DecompressPointer r0
    //     0xc100c4: add             x0, x0, HEAP, lsl #32
    // 0xc100c8: LoadField: r3 = r2->field_13
    //     0xc100c8: ldur            w3, [x2, #0x13]
    // 0xc100cc: DecompressPointer r3
    //     0xc100cc: add             x3, x3, HEAP, lsl #32
    // 0xc100d0: stp             x3, x0, [SP]
    // 0xc100d4: r4 = 0
    //     0xc100d4: movz            x4, #0
    // 0xc100d8: ldr             x0, [SP, #8]
    // 0xc100dc: r5 = UnlinkedCall_0x613b5c
    //     0xc100dc: add             x16, PP, #0x52, lsl #12  ; [pp+0x520c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc100e0: ldp             x5, lr, [x16, #0xc0]
    // 0xc100e4: blr             lr
    // 0xc100e8: cmp             w0, NULL
    // 0xc100ec: b.eq            #0xc10e90
    // 0xc100f0: LoadField: r1 = r0->field_cb
    //     0xc100f0: ldur            w1, [x0, #0xcb]
    // 0xc100f4: DecompressPointer r1
    //     0xc100f4: add             x1, x1, HEAP, lsl #32
    // 0xc100f8: cmp             w1, NULL
    // 0xc100fc: b.ne            #0xc10104
    // 0xc10100: r1 = ""
    //     0xc10100: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc10104: ldur            x0, [fp, #-8]
    // 0xc10108: ldur            x2, [fp, #-0x20]
    // 0xc1010c: r0 = capitalizeFirstWord()
    //     0xc1010c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xc10110: mov             x2, x0
    // 0xc10114: ldur            x0, [fp, #-8]
    // 0xc10118: stur            x2, [fp, #-0x10]
    // 0xc1011c: LoadField: r1 = r0->field_f
    //     0xc1011c: ldur            w1, [x0, #0xf]
    // 0xc10120: DecompressPointer r1
    //     0xc10120: add             x1, x1, HEAP, lsl #32
    // 0xc10124: cmp             w1, NULL
    // 0xc10128: b.eq            #0xc10e94
    // 0xc1012c: r0 = of()
    //     0xc1012c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10130: LoadField: r1 = r0->field_87
    //     0xc10130: ldur            w1, [x0, #0x87]
    // 0xc10134: DecompressPointer r1
    //     0xc10134: add             x1, x1, HEAP, lsl #32
    // 0xc10138: LoadField: r0 = r1->field_7
    //     0xc10138: ldur            w0, [x1, #7]
    // 0xc1013c: DecompressPointer r0
    //     0xc1013c: add             x0, x0, HEAP, lsl #32
    // 0xc10140: stur            x0, [fp, #-0x30]
    // 0xc10144: r1 = Instance_Color
    //     0xc10144: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc10148: d0 = 0.700000
    //     0xc10148: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc1014c: ldr             d0, [x17, #0xf48]
    // 0xc10150: r0 = withOpacity()
    //     0xc10150: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc10154: r16 = 16.000000
    //     0xc10154: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc10158: ldr             x16, [x16, #0x188]
    // 0xc1015c: stp             x0, x16, [SP]
    // 0xc10160: ldur            x1, [fp, #-0x30]
    // 0xc10164: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc10164: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc10168: ldr             x4, [x4, #0xaa0]
    // 0xc1016c: r0 = copyWith()
    //     0xc1016c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc10170: stur            x0, [fp, #-0x30]
    // 0xc10174: r0 = Text()
    //     0xc10174: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc10178: mov             x3, x0
    // 0xc1017c: ldur            x0, [fp, #-0x10]
    // 0xc10180: stur            x3, [fp, #-0x38]
    // 0xc10184: StoreField: r3->field_b = r0
    //     0xc10184: stur            w0, [x3, #0xb]
    // 0xc10188: ldur            x0, [fp, #-0x30]
    // 0xc1018c: StoreField: r3->field_13 = r0
    //     0xc1018c: stur            w0, [x3, #0x13]
    // 0xc10190: r1 = <Widget>
    //     0xc10190: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc10194: r2 = 18
    //     0xc10194: movz            x2, #0x12
    // 0xc10198: r0 = AllocateArray()
    //     0xc10198: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc1019c: stur            x0, [fp, #-0x10]
    // 0xc101a0: r16 = Instance_Icon
    //     0xc101a0: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xc101a4: ldr             x16, [x16, #0xd0]
    // 0xc101a8: StoreField: r0->field_f = r16
    //     0xc101a8: stur            w16, [x0, #0xf]
    // 0xc101ac: r16 = Instance_SizedBox
    //     0xc101ac: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xc101b0: ldr             x16, [x16, #0xe98]
    // 0xc101b4: StoreField: r0->field_13 = r16
    //     0xc101b4: stur            w16, [x0, #0x13]
    // 0xc101b8: ldur            x2, [fp, #-8]
    // 0xc101bc: LoadField: r1 = r2->field_f
    //     0xc101bc: ldur            w1, [x2, #0xf]
    // 0xc101c0: DecompressPointer r1
    //     0xc101c0: add             x1, x1, HEAP, lsl #32
    // 0xc101c4: cmp             w1, NULL
    // 0xc101c8: b.eq            #0xc10e98
    // 0xc101cc: r0 = of()
    //     0xc101cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc101d0: LoadField: r1 = r0->field_87
    //     0xc101d0: ldur            w1, [x0, #0x87]
    // 0xc101d4: DecompressPointer r1
    //     0xc101d4: add             x1, x1, HEAP, lsl #32
    // 0xc101d8: LoadField: r0 = r1->field_2b
    //     0xc101d8: ldur            w0, [x1, #0x2b]
    // 0xc101dc: DecompressPointer r0
    //     0xc101dc: add             x0, x0, HEAP, lsl #32
    // 0xc101e0: stur            x0, [fp, #-0x30]
    // 0xc101e4: r1 = Instance_Color
    //     0xc101e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc101e8: d0 = 0.700000
    //     0xc101e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc101ec: ldr             d0, [x17, #0xf48]
    // 0xc101f0: r0 = withOpacity()
    //     0xc101f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc101f4: r16 = 12.000000
    //     0xc101f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc101f8: ldr             x16, [x16, #0x9e8]
    // 0xc101fc: stp             x0, x16, [SP]
    // 0xc10200: ldur            x1, [fp, #-0x30]
    // 0xc10204: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc10204: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc10208: ldr             x4, [x4, #0xaa0]
    // 0xc1020c: r0 = copyWith()
    //     0xc1020c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc10210: stur            x0, [fp, #-0x30]
    // 0xc10214: r0 = Text()
    //     0xc10214: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc10218: mov             x1, x0
    // 0xc1021c: r0 = "Verified Buyer"
    //     0xc1021c: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xc10220: ldr             x0, [x0, #0xd8]
    // 0xc10224: StoreField: r1->field_b = r0
    //     0xc10224: stur            w0, [x1, #0xb]
    // 0xc10228: ldur            x0, [fp, #-0x30]
    // 0xc1022c: StoreField: r1->field_13 = r0
    //     0xc1022c: stur            w0, [x1, #0x13]
    // 0xc10230: mov             x0, x1
    // 0xc10234: ldur            x1, [fp, #-0x10]
    // 0xc10238: ArrayStore: r1[2] = r0  ; List_4
    //     0xc10238: add             x25, x1, #0x17
    //     0xc1023c: str             w0, [x25]
    //     0xc10240: tbz             w0, #0, #0xc1025c
    //     0xc10244: ldurb           w16, [x1, #-1]
    //     0xc10248: ldurb           w17, [x0, #-1]
    //     0xc1024c: and             x16, x17, x16, lsr #2
    //     0xc10250: tst             x16, HEAP, lsr #32
    //     0xc10254: b.eq            #0xc1025c
    //     0xc10258: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc1025c: r0 = Container()
    //     0xc1025c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc10260: stur            x0, [fp, #-0x30]
    // 0xc10264: r16 = 5.000000
    //     0xc10264: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc10268: ldr             x16, [x16, #0xcf0]
    // 0xc1026c: str             x16, [SP]
    // 0xc10270: mov             x1, x0
    // 0xc10274: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xc10274: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xc10278: ldr             x4, [x4, #0xe0]
    // 0xc1027c: r0 = Container()
    //     0xc1027c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc10280: ldur            x1, [fp, #-0x10]
    // 0xc10284: ldur            x0, [fp, #-0x30]
    // 0xc10288: ArrayStore: r1[3] = r0  ; List_4
    //     0xc10288: add             x25, x1, #0x1b
    //     0xc1028c: str             w0, [x25]
    //     0xc10290: tbz             w0, #0, #0xc102ac
    //     0xc10294: ldurb           w16, [x1, #-1]
    //     0xc10298: ldurb           w17, [x0, #-1]
    //     0xc1029c: and             x16, x17, x16, lsr #2
    //     0xc102a0: tst             x16, HEAP, lsr #32
    //     0xc102a4: b.eq            #0xc102ac
    //     0xc102a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc102ac: ldur            x2, [fp, #-0x20]
    // 0xc102b0: LoadField: r0 = r2->field_f
    //     0xc102b0: ldur            w0, [x2, #0xf]
    // 0xc102b4: DecompressPointer r0
    //     0xc102b4: add             x0, x0, HEAP, lsl #32
    // 0xc102b8: LoadField: r1 = r2->field_13
    //     0xc102b8: ldur            w1, [x2, #0x13]
    // 0xc102bc: DecompressPointer r1
    //     0xc102bc: add             x1, x1, HEAP, lsl #32
    // 0xc102c0: stp             x1, x0, [SP]
    // 0xc102c4: r4 = 0
    //     0xc102c4: movz            x4, #0
    // 0xc102c8: ldr             x0, [SP, #8]
    // 0xc102cc: r5 = UnlinkedCall_0x613b5c
    //     0xc102cc: add             x16, PP, #0x52, lsl #12  ; [pp+0x520e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc102d0: ldp             x5, lr, [x16, #0xe8]
    // 0xc102d4: blr             lr
    // 0xc102d8: cmp             w0, NULL
    // 0xc102dc: b.eq            #0xc1036c
    // 0xc102e0: LoadField: r1 = r0->field_c7
    //     0xc102e0: ldur            w1, [x0, #0xc7]
    // 0xc102e4: DecompressPointer r1
    //     0xc102e4: add             x1, x1, HEAP, lsl #32
    // 0xc102e8: cmp             w1, NULL
    // 0xc102ec: b.eq            #0xc1036c
    // 0xc102f0: ldur            x2, [fp, #-0x20]
    // 0xc102f4: LoadField: r0 = r2->field_f
    //     0xc102f4: ldur            w0, [x2, #0xf]
    // 0xc102f8: DecompressPointer r0
    //     0xc102f8: add             x0, x0, HEAP, lsl #32
    // 0xc102fc: LoadField: r1 = r2->field_13
    //     0xc102fc: ldur            w1, [x2, #0x13]
    // 0xc10300: DecompressPointer r1
    //     0xc10300: add             x1, x1, HEAP, lsl #32
    // 0xc10304: stp             x1, x0, [SP]
    // 0xc10308: r4 = 0
    //     0xc10308: movz            x4, #0
    // 0xc1030c: ldr             x0, [SP, #8]
    // 0xc10310: r5 = UnlinkedCall_0x613b5c
    //     0xc10310: add             x16, PP, #0x52, lsl #12  ; [pp+0x520f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10314: ldp             x5, lr, [x16, #0xf8]
    // 0xc10318: blr             lr
    // 0xc1031c: cmp             w0, NULL
    // 0xc10320: b.ne            #0xc1032c
    // 0xc10324: r0 = Null
    //     0xc10324: mov             x0, NULL
    // 0xc10328: b               #0xc1035c
    // 0xc1032c: LoadField: r1 = r0->field_c7
    //     0xc1032c: ldur            w1, [x0, #0xc7]
    // 0xc10330: DecompressPointer r1
    //     0xc10330: add             x1, x1, HEAP, lsl #32
    // 0xc10334: cmp             w1, NULL
    // 0xc10338: b.ne            #0xc10344
    // 0xc1033c: r0 = Null
    //     0xc1033c: mov             x0, NULL
    // 0xc10340: b               #0xc1035c
    // 0xc10344: LoadField: r0 = r1->field_7
    //     0xc10344: ldur            w0, [x1, #7]
    // 0xc10348: cbnz            w0, #0xc10354
    // 0xc1034c: r1 = false
    //     0xc1034c: add             x1, NULL, #0x30  ; false
    // 0xc10350: b               #0xc10358
    // 0xc10354: r1 = true
    //     0xc10354: add             x1, NULL, #0x20  ; true
    // 0xc10358: mov             x0, x1
    // 0xc1035c: cmp             w0, NULL
    // 0xc10360: b.ne            #0xc10370
    // 0xc10364: r0 = false
    //     0xc10364: add             x0, NULL, #0x30  ; false
    // 0xc10368: b               #0xc10370
    // 0xc1036c: r0 = false
    //     0xc1036c: add             x0, NULL, #0x30  ; false
    // 0xc10370: ldur            x2, [fp, #-0x20]
    // 0xc10374: stur            x0, [fp, #-0x30]
    // 0xc10378: r0 = Container()
    //     0xc10378: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1037c: stur            x0, [fp, #-0x40]
    // 0xc10380: r16 = Instance_EdgeInsets
    //     0xc10380: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xc10384: ldr             x16, [x16, #0x108]
    // 0xc10388: r30 = 5.000000
    //     0xc10388: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc1038c: ldr             lr, [lr, #0xcf0]
    // 0xc10390: stp             lr, x16, [SP, #0x10]
    // 0xc10394: r16 = 5.000000
    //     0xc10394: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc10398: ldr             x16, [x16, #0xcf0]
    // 0xc1039c: r30 = Instance_BoxDecoration
    //     0xc1039c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52110] Obj!BoxDecoration@d64a11
    //     0xc103a0: ldr             lr, [lr, #0x110]
    // 0xc103a4: stp             lr, x16, [SP]
    // 0xc103a8: mov             x1, x0
    // 0xc103ac: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xc103ac: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xc103b0: ldr             x4, [x4, #0x118]
    // 0xc103b4: r0 = Container()
    //     0xc103b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc103b8: r0 = Visibility()
    //     0xc103b8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc103bc: mov             x1, x0
    // 0xc103c0: ldur            x0, [fp, #-0x40]
    // 0xc103c4: StoreField: r1->field_b = r0
    //     0xc103c4: stur            w0, [x1, #0xb]
    // 0xc103c8: r0 = Instance_SizedBox
    //     0xc103c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc103cc: StoreField: r1->field_f = r0
    //     0xc103cc: stur            w0, [x1, #0xf]
    // 0xc103d0: ldur            x0, [fp, #-0x30]
    // 0xc103d4: StoreField: r1->field_13 = r0
    //     0xc103d4: stur            w0, [x1, #0x13]
    // 0xc103d8: r2 = false
    //     0xc103d8: add             x2, NULL, #0x30  ; false
    // 0xc103dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xc103dc: stur            w2, [x1, #0x17]
    // 0xc103e0: StoreField: r1->field_1b = r2
    //     0xc103e0: stur            w2, [x1, #0x1b]
    // 0xc103e4: StoreField: r1->field_1f = r2
    //     0xc103e4: stur            w2, [x1, #0x1f]
    // 0xc103e8: StoreField: r1->field_23 = r2
    //     0xc103e8: stur            w2, [x1, #0x23]
    // 0xc103ec: StoreField: r1->field_27 = r2
    //     0xc103ec: stur            w2, [x1, #0x27]
    // 0xc103f0: StoreField: r1->field_2b = r2
    //     0xc103f0: stur            w2, [x1, #0x2b]
    // 0xc103f4: mov             x0, x1
    // 0xc103f8: ldur            x1, [fp, #-0x10]
    // 0xc103fc: ArrayStore: r1[4] = r0  ; List_4
    //     0xc103fc: add             x25, x1, #0x1f
    //     0xc10400: str             w0, [x25]
    //     0xc10404: tbz             w0, #0, #0xc10420
    //     0xc10408: ldurb           w16, [x1, #-1]
    //     0xc1040c: ldurb           w17, [x0, #-1]
    //     0xc10410: and             x16, x17, x16, lsr #2
    //     0xc10414: tst             x16, HEAP, lsr #32
    //     0xc10418: b.eq            #0xc10420
    //     0xc1041c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc10420: ldur            x0, [fp, #-0x20]
    // 0xc10424: LoadField: r1 = r0->field_f
    //     0xc10424: ldur            w1, [x0, #0xf]
    // 0xc10428: DecompressPointer r1
    //     0xc10428: add             x1, x1, HEAP, lsl #32
    // 0xc1042c: LoadField: r3 = r0->field_13
    //     0xc1042c: ldur            w3, [x0, #0x13]
    // 0xc10430: DecompressPointer r3
    //     0xc10430: add             x3, x3, HEAP, lsl #32
    // 0xc10434: stp             x3, x1, [SP]
    // 0xc10438: r4 = 0
    //     0xc10438: movz            x4, #0
    // 0xc1043c: ldr             x0, [SP, #8]
    // 0xc10440: r5 = UnlinkedCall_0x613b5c
    //     0xc10440: add             x16, PP, #0x52, lsl #12  ; [pp+0x52120] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10444: ldp             x5, lr, [x16, #0x120]
    // 0xc10448: blr             lr
    // 0xc1044c: cmp             w0, NULL
    // 0xc10450: b.ne            #0xc1045c
    // 0xc10454: r0 = Null
    //     0xc10454: mov             x0, NULL
    // 0xc10458: b               #0xc10468
    // 0xc1045c: LoadField: r1 = r0->field_c7
    //     0xc1045c: ldur            w1, [x0, #0xc7]
    // 0xc10460: DecompressPointer r1
    //     0xc10460: add             x1, x1, HEAP, lsl #32
    // 0xc10464: mov             x0, x1
    // 0xc10468: cmp             w0, NULL
    // 0xc1046c: b.ne            #0xc10478
    // 0xc10470: r4 = ""
    //     0xc10470: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc10474: b               #0xc1047c
    // 0xc10478: mov             x4, x0
    // 0xc1047c: ldur            x3, [fp, #-8]
    // 0xc10480: ldur            x2, [fp, #-0x20]
    // 0xc10484: ldur            x0, [fp, #-0x10]
    // 0xc10488: stur            x4, [fp, #-0x30]
    // 0xc1048c: LoadField: r1 = r3->field_f
    //     0xc1048c: ldur            w1, [x3, #0xf]
    // 0xc10490: DecompressPointer r1
    //     0xc10490: add             x1, x1, HEAP, lsl #32
    // 0xc10494: cmp             w1, NULL
    // 0xc10498: b.eq            #0xc10e9c
    // 0xc1049c: r0 = of()
    //     0xc1049c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc104a0: LoadField: r1 = r0->field_87
    //     0xc104a0: ldur            w1, [x0, #0x87]
    // 0xc104a4: DecompressPointer r1
    //     0xc104a4: add             x1, x1, HEAP, lsl #32
    // 0xc104a8: LoadField: r0 = r1->field_2b
    //     0xc104a8: ldur            w0, [x1, #0x2b]
    // 0xc104ac: DecompressPointer r0
    //     0xc104ac: add             x0, x0, HEAP, lsl #32
    // 0xc104b0: stur            x0, [fp, #-0x40]
    // 0xc104b4: r1 = Instance_Color
    //     0xc104b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc104b8: d0 = 0.700000
    //     0xc104b8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc104bc: ldr             d0, [x17, #0xf48]
    // 0xc104c0: r0 = withOpacity()
    //     0xc104c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc104c4: r16 = 12.000000
    //     0xc104c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc104c8: ldr             x16, [x16, #0x9e8]
    // 0xc104cc: stp             x0, x16, [SP]
    // 0xc104d0: ldur            x1, [fp, #-0x40]
    // 0xc104d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc104d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc104d8: ldr             x4, [x4, #0xaa0]
    // 0xc104dc: r0 = copyWith()
    //     0xc104dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc104e0: stur            x0, [fp, #-0x40]
    // 0xc104e4: r0 = Text()
    //     0xc104e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc104e8: mov             x1, x0
    // 0xc104ec: ldur            x0, [fp, #-0x30]
    // 0xc104f0: StoreField: r1->field_b = r0
    //     0xc104f0: stur            w0, [x1, #0xb]
    // 0xc104f4: ldur            x0, [fp, #-0x40]
    // 0xc104f8: StoreField: r1->field_13 = r0
    //     0xc104f8: stur            w0, [x1, #0x13]
    // 0xc104fc: mov             x0, x1
    // 0xc10500: ldur            x1, [fp, #-0x10]
    // 0xc10504: ArrayStore: r1[5] = r0  ; List_4
    //     0xc10504: add             x25, x1, #0x23
    //     0xc10508: str             w0, [x25]
    //     0xc1050c: tbz             w0, #0, #0xc10528
    //     0xc10510: ldurb           w16, [x1, #-1]
    //     0xc10514: ldurb           w17, [x0, #-1]
    //     0xc10518: and             x16, x17, x16, lsr #2
    //     0xc1051c: tst             x16, HEAP, lsr #32
    //     0xc10520: b.eq            #0xc10528
    //     0xc10524: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc10528: r0 = Container()
    //     0xc10528: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1052c: stur            x0, [fp, #-0x30]
    // 0xc10530: r16 = 5.000000
    //     0xc10530: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc10534: ldr             x16, [x16, #0xcf0]
    // 0xc10538: str             x16, [SP]
    // 0xc1053c: mov             x1, x0
    // 0xc10540: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xc10540: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xc10544: ldr             x4, [x4, #0xe0]
    // 0xc10548: r0 = Container()
    //     0xc10548: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc1054c: ldur            x1, [fp, #-0x10]
    // 0xc10550: ldur            x0, [fp, #-0x30]
    // 0xc10554: ArrayStore: r1[6] = r0  ; List_4
    //     0xc10554: add             x25, x1, #0x27
    //     0xc10558: str             w0, [x25]
    //     0xc1055c: tbz             w0, #0, #0xc10578
    //     0xc10560: ldurb           w16, [x1, #-1]
    //     0xc10564: ldurb           w17, [x0, #-1]
    //     0xc10568: and             x16, x17, x16, lsr #2
    //     0xc1056c: tst             x16, HEAP, lsr #32
    //     0xc10570: b.eq            #0xc10578
    //     0xc10574: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc10578: r0 = Container()
    //     0xc10578: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1057c: stur            x0, [fp, #-0x30]
    // 0xc10580: r16 = Instance_EdgeInsets
    //     0xc10580: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xc10584: ldr             x16, [x16, #0x108]
    // 0xc10588: r30 = 5.000000
    //     0xc10588: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc1058c: ldr             lr, [lr, #0xcf0]
    // 0xc10590: stp             lr, x16, [SP, #0x10]
    // 0xc10594: r16 = 5.000000
    //     0xc10594: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xc10598: ldr             x16, [x16, #0xcf0]
    // 0xc1059c: r30 = Instance_BoxDecoration
    //     0xc1059c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52110] Obj!BoxDecoration@d64a11
    //     0xc105a0: ldr             lr, [lr, #0x110]
    // 0xc105a4: stp             lr, x16, [SP]
    // 0xc105a8: mov             x1, x0
    // 0xc105ac: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xc105ac: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xc105b0: ldr             x4, [x4, #0x118]
    // 0xc105b4: r0 = Container()
    //     0xc105b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc105b8: ldur            x1, [fp, #-0x10]
    // 0xc105bc: ldur            x0, [fp, #-0x30]
    // 0xc105c0: ArrayStore: r1[7] = r0  ; List_4
    //     0xc105c0: add             x25, x1, #0x2b
    //     0xc105c4: str             w0, [x25]
    //     0xc105c8: tbz             w0, #0, #0xc105e4
    //     0xc105cc: ldurb           w16, [x1, #-1]
    //     0xc105d0: ldurb           w17, [x0, #-1]
    //     0xc105d4: and             x16, x17, x16, lsr #2
    //     0xc105d8: tst             x16, HEAP, lsr #32
    //     0xc105dc: b.eq            #0xc105e4
    //     0xc105e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc105e4: ldur            x2, [fp, #-0x20]
    // 0xc105e8: LoadField: r0 = r2->field_f
    //     0xc105e8: ldur            w0, [x2, #0xf]
    // 0xc105ec: DecompressPointer r0
    //     0xc105ec: add             x0, x0, HEAP, lsl #32
    // 0xc105f0: LoadField: r1 = r2->field_13
    //     0xc105f0: ldur            w1, [x2, #0x13]
    // 0xc105f4: DecompressPointer r1
    //     0xc105f4: add             x1, x1, HEAP, lsl #32
    // 0xc105f8: stp             x1, x0, [SP]
    // 0xc105fc: r4 = 0
    //     0xc105fc: movz            x4, #0
    // 0xc10600: ldr             x0, [SP, #8]
    // 0xc10604: r5 = UnlinkedCall_0x613b5c
    //     0xc10604: add             x16, PP, #0x52, lsl #12  ; [pp+0x52130] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10608: ldp             x5, lr, [x16, #0x130]
    // 0xc1060c: blr             lr
    // 0xc10610: cmp             w0, NULL
    // 0xc10614: b.eq            #0xc10ea0
    // 0xc10618: LoadField: r3 = r0->field_c3
    //     0xc10618: ldur            w3, [x0, #0xc3]
    // 0xc1061c: DecompressPointer r3
    //     0xc1061c: add             x3, x3, HEAP, lsl #32
    // 0xc10620: mov             x0, x3
    // 0xc10624: stur            x3, [fp, #-0x30]
    // 0xc10628: r2 = Null
    //     0xc10628: mov             x2, NULL
    // 0xc1062c: r1 = Null
    //     0xc1062c: mov             x1, NULL
    // 0xc10630: r4 = LoadClassIdInstr(r0)
    //     0xc10630: ldur            x4, [x0, #-1]
    //     0xc10634: ubfx            x4, x4, #0xc, #0x14
    // 0xc10638: sub             x4, x4, #0x5e
    // 0xc1063c: cmp             x4, #1
    // 0xc10640: b.ls            #0xc10654
    // 0xc10644: r8 = String
    //     0xc10644: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc10648: r3 = Null
    //     0xc10648: add             x3, PP, #0x52, lsl #12  ; [pp+0x52140] Null
    //     0xc1064c: ldr             x3, [x3, #0x140]
    // 0xc10650: r0 = String()
    //     0xc10650: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc10654: ldur            x0, [fp, #-8]
    // 0xc10658: LoadField: r1 = r0->field_f
    //     0xc10658: ldur            w1, [x0, #0xf]
    // 0xc1065c: DecompressPointer r1
    //     0xc1065c: add             x1, x1, HEAP, lsl #32
    // 0xc10660: cmp             w1, NULL
    // 0xc10664: b.eq            #0xc10ea4
    // 0xc10668: r0 = of()
    //     0xc10668: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc1066c: LoadField: r1 = r0->field_87
    //     0xc1066c: ldur            w1, [x0, #0x87]
    // 0xc10670: DecompressPointer r1
    //     0xc10670: add             x1, x1, HEAP, lsl #32
    // 0xc10674: LoadField: r0 = r1->field_2b
    //     0xc10674: ldur            w0, [x1, #0x2b]
    // 0xc10678: DecompressPointer r0
    //     0xc10678: add             x0, x0, HEAP, lsl #32
    // 0xc1067c: stur            x0, [fp, #-0x40]
    // 0xc10680: r1 = Instance_Color
    //     0xc10680: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc10684: d0 = 0.700000
    //     0xc10684: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc10688: ldr             d0, [x17, #0xf48]
    // 0xc1068c: r0 = withOpacity()
    //     0xc1068c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc10690: r16 = 12.000000
    //     0xc10690: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc10694: ldr             x16, [x16, #0x9e8]
    // 0xc10698: stp             x0, x16, [SP]
    // 0xc1069c: ldur            x1, [fp, #-0x40]
    // 0xc106a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc106a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc106a4: ldr             x4, [x4, #0xaa0]
    // 0xc106a8: r0 = copyWith()
    //     0xc106a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc106ac: stur            x0, [fp, #-0x40]
    // 0xc106b0: r0 = Text()
    //     0xc106b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc106b4: mov             x1, x0
    // 0xc106b8: ldur            x0, [fp, #-0x30]
    // 0xc106bc: StoreField: r1->field_b = r0
    //     0xc106bc: stur            w0, [x1, #0xb]
    // 0xc106c0: ldur            x0, [fp, #-0x40]
    // 0xc106c4: StoreField: r1->field_13 = r0
    //     0xc106c4: stur            w0, [x1, #0x13]
    // 0xc106c8: mov             x0, x1
    // 0xc106cc: ldur            x1, [fp, #-0x10]
    // 0xc106d0: ArrayStore: r1[8] = r0  ; List_4
    //     0xc106d0: add             x25, x1, #0x2f
    //     0xc106d4: str             w0, [x25]
    //     0xc106d8: tbz             w0, #0, #0xc106f4
    //     0xc106dc: ldurb           w16, [x1, #-1]
    //     0xc106e0: ldurb           w17, [x0, #-1]
    //     0xc106e4: and             x16, x17, x16, lsr #2
    //     0xc106e8: tst             x16, HEAP, lsr #32
    //     0xc106ec: b.eq            #0xc106f4
    //     0xc106f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc106f4: r1 = <Widget>
    //     0xc106f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc106f8: r0 = AllocateGrowableArray()
    //     0xc106f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc106fc: mov             x1, x0
    // 0xc10700: ldur            x0, [fp, #-0x10]
    // 0xc10704: stur            x1, [fp, #-0x30]
    // 0xc10708: StoreField: r1->field_f = r0
    //     0xc10708: stur            w0, [x1, #0xf]
    // 0xc1070c: r0 = 18
    //     0xc1070c: movz            x0, #0x12
    // 0xc10710: StoreField: r1->field_b = r0
    //     0xc10710: stur            w0, [x1, #0xb]
    // 0xc10714: r0 = Row()
    //     0xc10714: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc10718: mov             x1, x0
    // 0xc1071c: r0 = Instance_Axis
    //     0xc1071c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc10720: stur            x1, [fp, #-0x10]
    // 0xc10724: StoreField: r1->field_f = r0
    //     0xc10724: stur            w0, [x1, #0xf]
    // 0xc10728: r2 = Instance_MainAxisAlignment
    //     0xc10728: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc1072c: ldr             x2, [x2, #0xa08]
    // 0xc10730: StoreField: r1->field_13 = r2
    //     0xc10730: stur            w2, [x1, #0x13]
    // 0xc10734: r3 = Instance_MainAxisSize
    //     0xc10734: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc10738: ldr             x3, [x3, #0xa10]
    // 0xc1073c: ArrayStore: r1[0] = r3  ; List_4
    //     0xc1073c: stur            w3, [x1, #0x17]
    // 0xc10740: r3 = Instance_CrossAxisAlignment
    //     0xc10740: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc10744: ldr             x3, [x3, #0xa18]
    // 0xc10748: StoreField: r1->field_1b = r3
    //     0xc10748: stur            w3, [x1, #0x1b]
    // 0xc1074c: r3 = Instance_VerticalDirection
    //     0xc1074c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc10750: ldr             x3, [x3, #0xa20]
    // 0xc10754: StoreField: r1->field_23 = r3
    //     0xc10754: stur            w3, [x1, #0x23]
    // 0xc10758: r4 = Instance_Clip
    //     0xc10758: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc1075c: ldr             x4, [x4, #0x38]
    // 0xc10760: StoreField: r1->field_2b = r4
    //     0xc10760: stur            w4, [x1, #0x2b]
    // 0xc10764: StoreField: r1->field_2f = rZR
    //     0xc10764: stur            xzr, [x1, #0x2f]
    // 0xc10768: ldur            x5, [fp, #-0x30]
    // 0xc1076c: StoreField: r1->field_b = r5
    //     0xc1076c: stur            w5, [x1, #0xb]
    // 0xc10770: ldur            x5, [fp, #-0x20]
    // 0xc10774: LoadField: r6 = r5->field_f
    //     0xc10774: ldur            w6, [x5, #0xf]
    // 0xc10778: DecompressPointer r6
    //     0xc10778: add             x6, x6, HEAP, lsl #32
    // 0xc1077c: LoadField: r7 = r5->field_13
    //     0xc1077c: ldur            w7, [x5, #0x13]
    // 0xc10780: DecompressPointer r7
    //     0xc10780: add             x7, x7, HEAP, lsl #32
    // 0xc10784: stp             x7, x6, [SP]
    // 0xc10788: r4 = 0
    //     0xc10788: movz            x4, #0
    // 0xc1078c: ldr             x0, [SP, #8]
    // 0xc10790: r5 = UnlinkedCall_0x613b5c
    //     0xc10790: add             x16, PP, #0x52, lsl #12  ; [pp+0x52150] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10794: ldp             x5, lr, [x16, #0x150]
    // 0xc10798: blr             lr
    // 0xc1079c: cmp             w0, NULL
    // 0xc107a0: b.eq            #0xc10ea8
    // 0xc107a4: LoadField: r3 = r0->field_bb
    //     0xc107a4: ldur            w3, [x0, #0xbb]
    // 0xc107a8: DecompressPointer r3
    //     0xc107a8: add             x3, x3, HEAP, lsl #32
    // 0xc107ac: mov             x0, x3
    // 0xc107b0: stur            x3, [fp, #-0x30]
    // 0xc107b4: r2 = Null
    //     0xc107b4: mov             x2, NULL
    // 0xc107b8: r1 = Null
    //     0xc107b8: mov             x1, NULL
    // 0xc107bc: r4 = LoadClassIdInstr(r0)
    //     0xc107bc: ldur            x4, [x0, #-1]
    //     0xc107c0: ubfx            x4, x4, #0xc, #0x14
    // 0xc107c4: sub             x4, x4, #0x5e
    // 0xc107c8: cmp             x4, #1
    // 0xc107cc: b.ls            #0xc107e0
    // 0xc107d0: r8 = String
    //     0xc107d0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc107d4: r3 = Null
    //     0xc107d4: add             x3, PP, #0x52, lsl #12  ; [pp+0x52160] Null
    //     0xc107d8: ldr             x3, [x3, #0x160]
    // 0xc107dc: r0 = String()
    //     0xc107dc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc107e0: ldur            x1, [fp, #-0x30]
    // 0xc107e4: r0 = parse()
    //     0xc107e4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc107e8: ldur            x2, [fp, #-0x20]
    // 0xc107ec: stur            d0, [fp, #-0x60]
    // 0xc107f0: LoadField: r0 = r2->field_f
    //     0xc107f0: ldur            w0, [x2, #0xf]
    // 0xc107f4: DecompressPointer r0
    //     0xc107f4: add             x0, x0, HEAP, lsl #32
    // 0xc107f8: LoadField: r1 = r2->field_13
    //     0xc107f8: ldur            w1, [x2, #0x13]
    // 0xc107fc: DecompressPointer r1
    //     0xc107fc: add             x1, x1, HEAP, lsl #32
    // 0xc10800: stp             x1, x0, [SP]
    // 0xc10804: r4 = 0
    //     0xc10804: movz            x4, #0
    // 0xc10808: ldr             x0, [SP, #8]
    // 0xc1080c: r5 = UnlinkedCall_0x613b5c
    //     0xc1080c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52170] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10810: ldp             x5, lr, [x16, #0x170]
    // 0xc10814: blr             lr
    // 0xc10818: cmp             w0, NULL
    // 0xc1081c: b.eq            #0xc10eac
    // 0xc10820: LoadField: r3 = r0->field_bb
    //     0xc10820: ldur            w3, [x0, #0xbb]
    // 0xc10824: DecompressPointer r3
    //     0xc10824: add             x3, x3, HEAP, lsl #32
    // 0xc10828: mov             x0, x3
    // 0xc1082c: stur            x3, [fp, #-0x30]
    // 0xc10830: r2 = Null
    //     0xc10830: mov             x2, NULL
    // 0xc10834: r1 = Null
    //     0xc10834: mov             x1, NULL
    // 0xc10838: r4 = LoadClassIdInstr(r0)
    //     0xc10838: ldur            x4, [x0, #-1]
    //     0xc1083c: ubfx            x4, x4, #0xc, #0x14
    // 0xc10840: sub             x4, x4, #0x5e
    // 0xc10844: cmp             x4, #1
    // 0xc10848: b.ls            #0xc1085c
    // 0xc1084c: r8 = String
    //     0xc1084c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc10850: r3 = Null
    //     0xc10850: add             x3, PP, #0x52, lsl #12  ; [pp+0x52180] Null
    //     0xc10854: ldr             x3, [x3, #0x180]
    // 0xc10858: r0 = String()
    //     0xc10858: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc1085c: ldur            x1, [fp, #-0x30]
    // 0xc10860: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc10860: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc10864: r0 = parse()
    //     0xc10864: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xc10868: stur            x0, [fp, #-0x18]
    // 0xc1086c: r0 = RatingWidget()
    //     0xc1086c: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xc10870: mov             x3, x0
    // 0xc10874: r0 = Instance_Icon
    //     0xc10874: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xc10878: ldr             x0, [x0, #0x190]
    // 0xc1087c: stur            x3, [fp, #-0x30]
    // 0xc10880: StoreField: r3->field_7 = r0
    //     0xc10880: stur            w0, [x3, #7]
    // 0xc10884: r0 = Instance_Icon
    //     0xc10884: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xc10888: ldr             x0, [x0, #0x198]
    // 0xc1088c: StoreField: r3->field_b = r0
    //     0xc1088c: stur            w0, [x3, #0xb]
    // 0xc10890: r0 = Instance_Icon
    //     0xc10890: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xc10894: ldr             x0, [x0, #0x1a0]
    // 0xc10898: StoreField: r3->field_f = r0
    //     0xc10898: stur            w0, [x3, #0xf]
    // 0xc1089c: r1 = Function '<anonymous closure>':.
    //     0xc1089c: add             x1, PP, #0x52, lsl #12  ; [pp+0x521a8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc108a0: ldr             x1, [x1, #0x1a8]
    // 0xc108a4: r2 = Null
    //     0xc108a4: mov             x2, NULL
    // 0xc108a8: r0 = AllocateClosure()
    //     0xc108a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc108ac: stur            x0, [fp, #-0x40]
    // 0xc108b0: r0 = RatingBar()
    //     0xc108b0: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xc108b4: mov             x1, x0
    // 0xc108b8: ldur            x0, [fp, #-0x40]
    // 0xc108bc: stur            x1, [fp, #-0x48]
    // 0xc108c0: StoreField: r1->field_b = r0
    //     0xc108c0: stur            w0, [x1, #0xb]
    // 0xc108c4: r0 = true
    //     0xc108c4: add             x0, NULL, #0x20  ; true
    // 0xc108c8: StoreField: r1->field_1f = r0
    //     0xc108c8: stur            w0, [x1, #0x1f]
    // 0xc108cc: r2 = Instance_Axis
    //     0xc108cc: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc108d0: StoreField: r1->field_23 = r2
    //     0xc108d0: stur            w2, [x1, #0x23]
    // 0xc108d4: StoreField: r1->field_27 = r0
    //     0xc108d4: stur            w0, [x1, #0x27]
    // 0xc108d8: d0 = 2.000000
    //     0xc108d8: fmov            d0, #2.00000000
    // 0xc108dc: StoreField: r1->field_2b = d0
    //     0xc108dc: stur            d0, [x1, #0x2b]
    // 0xc108e0: StoreField: r1->field_33 = r0
    //     0xc108e0: stur            w0, [x1, #0x33]
    // 0xc108e4: ldur            d0, [fp, #-0x60]
    // 0xc108e8: StoreField: r1->field_37 = d0
    //     0xc108e8: stur            d0, [x1, #0x37]
    // 0xc108ec: ldur            x2, [fp, #-0x18]
    // 0xc108f0: StoreField: r1->field_3f = r2
    //     0xc108f0: stur            x2, [x1, #0x3f]
    // 0xc108f4: r2 = Instance_EdgeInsets
    //     0xc108f4: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc108f8: StoreField: r1->field_47 = r2
    //     0xc108f8: stur            w2, [x1, #0x47]
    // 0xc108fc: d0 = 20.000000
    //     0xc108fc: fmov            d0, #20.00000000
    // 0xc10900: StoreField: r1->field_4b = d0
    //     0xc10900: stur            d0, [x1, #0x4b]
    // 0xc10904: StoreField: r1->field_53 = rZR
    //     0xc10904: stur            xzr, [x1, #0x53]
    // 0xc10908: r2 = false
    //     0xc10908: add             x2, NULL, #0x30  ; false
    // 0xc1090c: StoreField: r1->field_5b = r2
    //     0xc1090c: stur            w2, [x1, #0x5b]
    // 0xc10910: StoreField: r1->field_5f = r2
    //     0xc10910: stur            w2, [x1, #0x5f]
    // 0xc10914: ldur            x2, [fp, #-0x30]
    // 0xc10918: StoreField: r1->field_67 = r2
    //     0xc10918: stur            w2, [x1, #0x67]
    // 0xc1091c: r0 = Padding()
    //     0xc1091c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc10920: mov             x1, x0
    // 0xc10924: r0 = Instance_EdgeInsets
    //     0xc10924: add             x0, PP, #0x52, lsl #12  ; [pp+0x521b0] Obj!EdgeInsets@d57201
    //     0xc10928: ldr             x0, [x0, #0x1b0]
    // 0xc1092c: stur            x1, [fp, #-0x30]
    // 0xc10930: StoreField: r1->field_f = r0
    //     0xc10930: stur            w0, [x1, #0xf]
    // 0xc10934: ldur            x0, [fp, #-0x48]
    // 0xc10938: StoreField: r1->field_b = r0
    //     0xc10938: stur            w0, [x1, #0xb]
    // 0xc1093c: ldur            x2, [fp, #-0x20]
    // 0xc10940: LoadField: r0 = r2->field_f
    //     0xc10940: ldur            w0, [x2, #0xf]
    // 0xc10944: DecompressPointer r0
    //     0xc10944: add             x0, x0, HEAP, lsl #32
    // 0xc10948: LoadField: r3 = r2->field_13
    //     0xc10948: ldur            w3, [x2, #0x13]
    // 0xc1094c: DecompressPointer r3
    //     0xc1094c: add             x3, x3, HEAP, lsl #32
    // 0xc10950: stp             x3, x0, [SP]
    // 0xc10954: r4 = 0
    //     0xc10954: movz            x4, #0
    // 0xc10958: ldr             x0, [SP, #8]
    // 0xc1095c: r5 = UnlinkedCall_0x613b5c
    //     0xc1095c: add             x16, PP, #0x52, lsl #12  ; [pp+0x521b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10960: ldp             x5, lr, [x16, #0x1b8]
    // 0xc10964: blr             lr
    // 0xc10968: cmp             w0, NULL
    // 0xc1096c: b.eq            #0xc10eb0
    // 0xc10970: LoadField: r1 = r0->field_bf
    //     0xc10970: ldur            w1, [x0, #0xbf]
    // 0xc10974: DecompressPointer r1
    //     0xc10974: add             x1, x1, HEAP, lsl #32
    // 0xc10978: cmp             w1, NULL
    // 0xc1097c: b.eq            #0xc10eb4
    // 0xc10980: r0 = trim()
    //     0xc10980: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xc10984: mov             x2, x0
    // 0xc10988: ldur            x0, [fp, #-8]
    // 0xc1098c: stur            x2, [fp, #-0x40]
    // 0xc10990: LoadField: r1 = r0->field_f
    //     0xc10990: ldur            w1, [x0, #0xf]
    // 0xc10994: DecompressPointer r1
    //     0xc10994: add             x1, x1, HEAP, lsl #32
    // 0xc10998: cmp             w1, NULL
    // 0xc1099c: b.eq            #0xc10eb8
    // 0xc109a0: r0 = of()
    //     0xc109a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc109a4: LoadField: r1 = r0->field_87
    //     0xc109a4: ldur            w1, [x0, #0x87]
    // 0xc109a8: DecompressPointer r1
    //     0xc109a8: add             x1, x1, HEAP, lsl #32
    // 0xc109ac: LoadField: r0 = r1->field_2b
    //     0xc109ac: ldur            w0, [x1, #0x2b]
    // 0xc109b0: DecompressPointer r0
    //     0xc109b0: add             x0, x0, HEAP, lsl #32
    // 0xc109b4: LoadField: r2 = r0->field_13
    //     0xc109b4: ldur            w2, [x0, #0x13]
    // 0xc109b8: DecompressPointer r2
    //     0xc109b8: add             x2, x2, HEAP, lsl #32
    // 0xc109bc: stur            x2, [fp, #-0x48]
    // 0xc109c0: r1 = Instance_Color
    //     0xc109c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc109c4: d0 = 0.700000
    //     0xc109c4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc109c8: ldr             d0, [x17, #0xf48]
    // 0xc109cc: r0 = withOpacity()
    //     0xc109cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc109d0: ldur            x16, [fp, #-0x48]
    // 0xc109d4: stp             x0, x16, [SP]
    // 0xc109d8: r1 = Instance_TextStyle
    //     0xc109d8: add             x1, PP, #0x52, lsl #12  ; [pp+0x521c8] Obj!TextStyle@d62951
    //     0xc109dc: ldr             x1, [x1, #0x1c8]
    // 0xc109e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xc109e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xc109e4: ldr             x4, [x4, #0x9b8]
    // 0xc109e8: r0 = copyWith()
    //     0xc109e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc109ec: mov             x2, x0
    // 0xc109f0: ldur            x0, [fp, #-8]
    // 0xc109f4: stur            x2, [fp, #-0x48]
    // 0xc109f8: LoadField: r1 = r0->field_f
    //     0xc109f8: ldur            w1, [x0, #0xf]
    // 0xc109fc: DecompressPointer r1
    //     0xc109fc: add             x1, x1, HEAP, lsl #32
    // 0xc10a00: cmp             w1, NULL
    // 0xc10a04: b.eq            #0xc10ebc
    // 0xc10a08: r0 = of()
    //     0xc10a08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10a0c: LoadField: r1 = r0->field_87
    //     0xc10a0c: ldur            w1, [x0, #0x87]
    // 0xc10a10: DecompressPointer r1
    //     0xc10a10: add             x1, x1, HEAP, lsl #32
    // 0xc10a14: LoadField: r0 = r1->field_7
    //     0xc10a14: ldur            w0, [x1, #7]
    // 0xc10a18: DecompressPointer r0
    //     0xc10a18: add             x0, x0, HEAP, lsl #32
    // 0xc10a1c: ldur            x2, [fp, #-8]
    // 0xc10a20: stur            x0, [fp, #-0x50]
    // 0xc10a24: LoadField: r1 = r2->field_f
    //     0xc10a24: ldur            w1, [x2, #0xf]
    // 0xc10a28: DecompressPointer r1
    //     0xc10a28: add             x1, x1, HEAP, lsl #32
    // 0xc10a2c: cmp             w1, NULL
    // 0xc10a30: b.eq            #0xc10ec0
    // 0xc10a34: r0 = of()
    //     0xc10a34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10a38: LoadField: r1 = r0->field_5b
    //     0xc10a38: ldur            w1, [x0, #0x5b]
    // 0xc10a3c: DecompressPointer r1
    //     0xc10a3c: add             x1, x1, HEAP, lsl #32
    // 0xc10a40: r16 = 12.000000
    //     0xc10a40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc10a44: ldr             x16, [x16, #0x9e8]
    // 0xc10a48: stp             x1, x16, [SP, #8]
    // 0xc10a4c: r16 = Instance_TextDecoration
    //     0xc10a4c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc10a50: ldr             x16, [x16, #0x10]
    // 0xc10a54: str             x16, [SP]
    // 0xc10a58: ldur            x1, [fp, #-0x50]
    // 0xc10a5c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xc10a5c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xc10a60: ldr             x4, [x4, #0xe38]
    // 0xc10a64: r0 = copyWith()
    //     0xc10a64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc10a68: mov             x2, x0
    // 0xc10a6c: ldur            x0, [fp, #-8]
    // 0xc10a70: stur            x2, [fp, #-0x50]
    // 0xc10a74: LoadField: r1 = r0->field_f
    //     0xc10a74: ldur            w1, [x0, #0xf]
    // 0xc10a78: DecompressPointer r1
    //     0xc10a78: add             x1, x1, HEAP, lsl #32
    // 0xc10a7c: cmp             w1, NULL
    // 0xc10a80: b.eq            #0xc10ec4
    // 0xc10a84: r0 = of()
    //     0xc10a84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10a88: LoadField: r1 = r0->field_87
    //     0xc10a88: ldur            w1, [x0, #0x87]
    // 0xc10a8c: DecompressPointer r1
    //     0xc10a8c: add             x1, x1, HEAP, lsl #32
    // 0xc10a90: LoadField: r0 = r1->field_7
    //     0xc10a90: ldur            w0, [x1, #7]
    // 0xc10a94: DecompressPointer r0
    //     0xc10a94: add             x0, x0, HEAP, lsl #32
    // 0xc10a98: ldur            x1, [fp, #-8]
    // 0xc10a9c: stur            x0, [fp, #-0x58]
    // 0xc10aa0: LoadField: r2 = r1->field_f
    //     0xc10aa0: ldur            w2, [x1, #0xf]
    // 0xc10aa4: DecompressPointer r2
    //     0xc10aa4: add             x2, x2, HEAP, lsl #32
    // 0xc10aa8: cmp             w2, NULL
    // 0xc10aac: b.eq            #0xc10ec8
    // 0xc10ab0: mov             x1, x2
    // 0xc10ab4: r0 = of()
    //     0xc10ab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10ab8: LoadField: r1 = r0->field_5b
    //     0xc10ab8: ldur            w1, [x0, #0x5b]
    // 0xc10abc: DecompressPointer r1
    //     0xc10abc: add             x1, x1, HEAP, lsl #32
    // 0xc10ac0: r16 = 12.000000
    //     0xc10ac0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc10ac4: ldr             x16, [x16, #0x9e8]
    // 0xc10ac8: stp             x1, x16, [SP, #8]
    // 0xc10acc: r16 = Instance_TextDecoration
    //     0xc10acc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc10ad0: ldr             x16, [x16, #0x10]
    // 0xc10ad4: str             x16, [SP]
    // 0xc10ad8: ldur            x1, [fp, #-0x58]
    // 0xc10adc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xc10adc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xc10ae0: ldr             x4, [x4, #0xe38]
    // 0xc10ae4: r0 = copyWith()
    //     0xc10ae4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc10ae8: stur            x0, [fp, #-8]
    // 0xc10aec: r0 = ReadMoreText()
    //     0xc10aec: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xc10af0: mov             x1, x0
    // 0xc10af4: ldur            x0, [fp, #-0x40]
    // 0xc10af8: stur            x1, [fp, #-0x58]
    // 0xc10afc: StoreField: r1->field_3f = r0
    //     0xc10afc: stur            w0, [x1, #0x3f]
    // 0xc10b00: r0 = "Know Less"
    //     0xc10b00: add             x0, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xc10b04: ldr             x0, [x0, #0x1d0]
    // 0xc10b08: StoreField: r1->field_43 = r0
    //     0xc10b08: stur            w0, [x1, #0x43]
    // 0xc10b0c: r0 = "Know more"
    //     0xc10b0c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xc10b10: ldr             x0, [x0, #0x20]
    // 0xc10b14: StoreField: r1->field_47 = r0
    //     0xc10b14: stur            w0, [x1, #0x47]
    // 0xc10b18: r0 = Instance_Color
    //     0xc10b18: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc10b1c: StoreField: r1->field_4b = r0
    //     0xc10b1c: stur            w0, [x1, #0x4b]
    // 0xc10b20: r0 = 240
    //     0xc10b20: movz            x0, #0xf0
    // 0xc10b24: StoreField: r1->field_f = r0
    //     0xc10b24: stur            x0, [x1, #0xf]
    // 0xc10b28: r0 = 2
    //     0xc10b28: movz            x0, #0x2
    // 0xc10b2c: ArrayStore: r1[0] = r0  ; List_8
    //     0xc10b2c: stur            x0, [x1, #0x17]
    // 0xc10b30: r0 = Instance_TrimMode
    //     0xc10b30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xc10b34: ldr             x0, [x0, #0x9d0]
    // 0xc10b38: StoreField: r1->field_1f = r0
    //     0xc10b38: stur            w0, [x1, #0x1f]
    // 0xc10b3c: ldur            x0, [fp, #-0x48]
    // 0xc10b40: StoreField: r1->field_4f = r0
    //     0xc10b40: stur            w0, [x1, #0x4f]
    // 0xc10b44: r0 = Instance_TextAlign
    //     0xc10b44: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc10b48: StoreField: r1->field_53 = r0
    //     0xc10b48: stur            w0, [x1, #0x53]
    // 0xc10b4c: ldur            x0, [fp, #-0x50]
    // 0xc10b50: StoreField: r1->field_23 = r0
    //     0xc10b50: stur            w0, [x1, #0x23]
    // 0xc10b54: ldur            x0, [fp, #-8]
    // 0xc10b58: StoreField: r1->field_27 = r0
    //     0xc10b58: stur            w0, [x1, #0x27]
    // 0xc10b5c: r0 = "… "
    //     0xc10b5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xc10b60: ldr             x0, [x0, #0x9d8]
    // 0xc10b64: StoreField: r1->field_3b = r0
    //     0xc10b64: stur            w0, [x1, #0x3b]
    // 0xc10b68: r0 = true
    //     0xc10b68: add             x0, NULL, #0x20  ; true
    // 0xc10b6c: StoreField: r1->field_37 = r0
    //     0xc10b6c: stur            w0, [x1, #0x37]
    // 0xc10b70: ldur            x2, [fp, #-0x20]
    // 0xc10b74: LoadField: r0 = r2->field_f
    //     0xc10b74: ldur            w0, [x2, #0xf]
    // 0xc10b78: DecompressPointer r0
    //     0xc10b78: add             x0, x0, HEAP, lsl #32
    // 0xc10b7c: LoadField: r3 = r2->field_13
    //     0xc10b7c: ldur            w3, [x2, #0x13]
    // 0xc10b80: DecompressPointer r3
    //     0xc10b80: add             x3, x3, HEAP, lsl #32
    // 0xc10b84: stp             x3, x0, [SP]
    // 0xc10b88: r4 = 0
    //     0xc10b88: movz            x4, #0
    // 0xc10b8c: ldr             x0, [SP, #8]
    // 0xc10b90: r5 = UnlinkedCall_0x613b5c
    //     0xc10b90: add             x16, PP, #0x52, lsl #12  ; [pp+0x521d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10b94: ldp             x5, lr, [x16, #0x1d8]
    // 0xc10b98: blr             lr
    // 0xc10b9c: cmp             w0, NULL
    // 0xc10ba0: b.eq            #0xc10ecc
    // 0xc10ba4: LoadField: r1 = r0->field_b7
    //     0xc10ba4: ldur            w1, [x0, #0xb7]
    // 0xc10ba8: DecompressPointer r1
    //     0xc10ba8: add             x1, x1, HEAP, lsl #32
    // 0xc10bac: cmp             w1, NULL
    // 0xc10bb0: b.eq            #0xc10ed0
    // 0xc10bb4: LoadField: r0 = r1->field_b
    //     0xc10bb4: ldur            w0, [x1, #0xb]
    // 0xc10bb8: r1 = LoadInt32Instr(r0)
    //     0xc10bb8: sbfx            x1, x0, #1, #0x1f
    // 0xc10bbc: cmp             x1, #3
    // 0xc10bc0: b.gt            #0xc10c90
    // 0xc10bc4: ldur            x2, [fp, #-0x20]
    // 0xc10bc8: LoadField: r0 = r2->field_f
    //     0xc10bc8: ldur            w0, [x2, #0xf]
    // 0xc10bcc: DecompressPointer r0
    //     0xc10bcc: add             x0, x0, HEAP, lsl #32
    // 0xc10bd0: LoadField: r1 = r2->field_13
    //     0xc10bd0: ldur            w1, [x2, #0x13]
    // 0xc10bd4: DecompressPointer r1
    //     0xc10bd4: add             x1, x1, HEAP, lsl #32
    // 0xc10bd8: stp             x1, x0, [SP]
    // 0xc10bdc: r4 = 0
    //     0xc10bdc: movz            x4, #0
    // 0xc10be0: ldr             x0, [SP, #8]
    // 0xc10be4: r5 = UnlinkedCall_0x613b5c
    //     0xc10be4: add             x16, PP, #0x52, lsl #12  ; [pp+0x521e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10be8: ldp             x5, lr, [x16, #0x1e8]
    // 0xc10bec: blr             lr
    // 0xc10bf0: cmp             w0, NULL
    // 0xc10bf4: b.ne            #0xc10c00
    // 0xc10bf8: r0 = Null
    //     0xc10bf8: mov             x0, NULL
    // 0xc10bfc: b               #0xc10c1c
    // 0xc10c00: LoadField: r1 = r0->field_b7
    //     0xc10c00: ldur            w1, [x0, #0xb7]
    // 0xc10c04: DecompressPointer r1
    //     0xc10c04: add             x1, x1, HEAP, lsl #32
    // 0xc10c08: cmp             w1, NULL
    // 0xc10c0c: b.ne            #0xc10c18
    // 0xc10c10: r0 = Null
    //     0xc10c10: mov             x0, NULL
    // 0xc10c14: b               #0xc10c1c
    // 0xc10c18: LoadField: r0 = r1->field_b
    //     0xc10c18: ldur            w0, [x1, #0xb]
    // 0xc10c1c: cmp             w0, NULL
    // 0xc10c20: b.ne            #0xc10c2c
    // 0xc10c24: r0 = 0
    //     0xc10c24: movz            x0, #0
    // 0xc10c28: b               #0xc10c34
    // 0xc10c2c: r1 = LoadInt32Instr(r0)
    //     0xc10c2c: sbfx            x1, x0, #1, #0x1f
    // 0xc10c30: mov             x0, x1
    // 0xc10c34: lsl             x3, x0, #1
    // 0xc10c38: ldur            x2, [fp, #-0x20]
    // 0xc10c3c: stur            x3, [fp, #-8]
    // 0xc10c40: r1 = Function '<anonymous closure>':.
    //     0xc10c40: add             x1, PP, #0x52, lsl #12  ; [pp+0x521f8] AnonymousClosure: (0xc11638), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc10c44: ldr             x1, [x1, #0x1f8]
    // 0xc10c48: r0 = AllocateClosure()
    //     0xc10c48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc10c4c: stur            x0, [fp, #-0x40]
    // 0xc10c50: r0 = ListView()
    //     0xc10c50: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc10c54: stur            x0, [fp, #-0x48]
    // 0xc10c58: r16 = true
    //     0xc10c58: add             x16, NULL, #0x20  ; true
    // 0xc10c5c: r30 = Instance_Axis
    //     0xc10c5c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc10c60: stp             lr, x16, [SP, #8]
    // 0xc10c64: r16 = Instance_NeverScrollableScrollPhysics
    //     0xc10c64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc10c68: ldr             x16, [x16, #0x1c8]
    // 0xc10c6c: str             x16, [SP]
    // 0xc10c70: mov             x1, x0
    // 0xc10c74: ldur            x2, [fp, #-0x40]
    // 0xc10c78: ldur            x3, [fp, #-8]
    // 0xc10c7c: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xc10c7c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xc10c80: ldr             x4, [x4, #0x200]
    // 0xc10c84: r0 = ListView.builder()
    //     0xc10c84: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xc10c88: ldur            x5, [fp, #-0x48]
    // 0xc10c8c: b               #0xc10ce0
    // 0xc10c90: ldur            x2, [fp, #-0x20]
    // 0xc10c94: r1 = Function '<anonymous closure>':.
    //     0xc10c94: add             x1, PP, #0x52, lsl #12  ; [pp+0x52208] AnonymousClosure: (0xc10ed4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc10c98: ldr             x1, [x1, #0x208]
    // 0xc10c9c: r0 = AllocateClosure()
    //     0xc10c9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc10ca0: stur            x0, [fp, #-8]
    // 0xc10ca4: r0 = ListView()
    //     0xc10ca4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc10ca8: stur            x0, [fp, #-0x20]
    // 0xc10cac: r16 = true
    //     0xc10cac: add             x16, NULL, #0x20  ; true
    // 0xc10cb0: r30 = Instance_Axis
    //     0xc10cb0: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc10cb4: stp             lr, x16, [SP, #8]
    // 0xc10cb8: r16 = Instance_NeverScrollableScrollPhysics
    //     0xc10cb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc10cbc: ldr             x16, [x16, #0x1c8]
    // 0xc10cc0: str             x16, [SP]
    // 0xc10cc4: mov             x1, x0
    // 0xc10cc8: ldur            x2, [fp, #-8]
    // 0xc10ccc: r3 = 6
    //     0xc10ccc: movz            x3, #0x6
    // 0xc10cd0: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xc10cd0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xc10cd4: ldr             x4, [x4, #0x200]
    // 0xc10cd8: r0 = ListView.builder()
    //     0xc10cd8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xc10cdc: ldur            x5, [fp, #-0x20]
    // 0xc10ce0: ldur            x4, [fp, #-0x38]
    // 0xc10ce4: ldur            x3, [fp, #-0x10]
    // 0xc10ce8: ldur            x2, [fp, #-0x30]
    // 0xc10cec: ldur            x0, [fp, #-0x58]
    // 0xc10cf0: stur            x5, [fp, #-8]
    // 0xc10cf4: r1 = <FlexParentData>
    //     0xc10cf4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc10cf8: ldr             x1, [x1, #0xe00]
    // 0xc10cfc: r0 = Expanded()
    //     0xc10cfc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc10d00: mov             x3, x0
    // 0xc10d04: r0 = 1
    //     0xc10d04: movz            x0, #0x1
    // 0xc10d08: stur            x3, [fp, #-0x20]
    // 0xc10d0c: StoreField: r3->field_13 = r0
    //     0xc10d0c: stur            x0, [x3, #0x13]
    // 0xc10d10: r0 = Instance_FlexFit
    //     0xc10d10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc10d14: ldr             x0, [x0, #0xe08]
    // 0xc10d18: StoreField: r3->field_1b = r0
    //     0xc10d18: stur            w0, [x3, #0x1b]
    // 0xc10d1c: ldur            x0, [fp, #-8]
    // 0xc10d20: StoreField: r3->field_b = r0
    //     0xc10d20: stur            w0, [x3, #0xb]
    // 0xc10d24: r1 = Null
    //     0xc10d24: mov             x1, NULL
    // 0xc10d28: r2 = 16
    //     0xc10d28: movz            x2, #0x10
    // 0xc10d2c: r0 = AllocateArray()
    //     0xc10d2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc10d30: mov             x2, x0
    // 0xc10d34: ldur            x0, [fp, #-0x38]
    // 0xc10d38: stur            x2, [fp, #-8]
    // 0xc10d3c: StoreField: r2->field_f = r0
    //     0xc10d3c: stur            w0, [x2, #0xf]
    // 0xc10d40: r16 = Instance_SizedBox
    //     0xc10d40: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xc10d44: ldr             x16, [x16, #0x328]
    // 0xc10d48: StoreField: r2->field_13 = r16
    //     0xc10d48: stur            w16, [x2, #0x13]
    // 0xc10d4c: ldur            x0, [fp, #-0x10]
    // 0xc10d50: ArrayStore: r2[0] = r0  ; List_4
    //     0xc10d50: stur            w0, [x2, #0x17]
    // 0xc10d54: r16 = Instance_SizedBox
    //     0xc10d54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xc10d58: ldr             x16, [x16, #0x9f0]
    // 0xc10d5c: StoreField: r2->field_1b = r16
    //     0xc10d5c: stur            w16, [x2, #0x1b]
    // 0xc10d60: ldur            x0, [fp, #-0x30]
    // 0xc10d64: StoreField: r2->field_1f = r0
    //     0xc10d64: stur            w0, [x2, #0x1f]
    // 0xc10d68: r16 = Instance_SizedBox
    //     0xc10d68: add             x16, PP, #0x52, lsl #12  ; [pp+0x52210] Obj!SizedBox@d680e1
    //     0xc10d6c: ldr             x16, [x16, #0x210]
    // 0xc10d70: StoreField: r2->field_23 = r16
    //     0xc10d70: stur            w16, [x2, #0x23]
    // 0xc10d74: ldur            x0, [fp, #-0x58]
    // 0xc10d78: StoreField: r2->field_27 = r0
    //     0xc10d78: stur            w0, [x2, #0x27]
    // 0xc10d7c: ldur            x0, [fp, #-0x20]
    // 0xc10d80: StoreField: r2->field_2b = r0
    //     0xc10d80: stur            w0, [x2, #0x2b]
    // 0xc10d84: r1 = <Widget>
    //     0xc10d84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc10d88: r0 = AllocateGrowableArray()
    //     0xc10d88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc10d8c: mov             x1, x0
    // 0xc10d90: ldur            x0, [fp, #-8]
    // 0xc10d94: stur            x1, [fp, #-0x10]
    // 0xc10d98: StoreField: r1->field_f = r0
    //     0xc10d98: stur            w0, [x1, #0xf]
    // 0xc10d9c: r0 = 16
    //     0xc10d9c: movz            x0, #0x10
    // 0xc10da0: StoreField: r1->field_b = r0
    //     0xc10da0: stur            w0, [x1, #0xb]
    // 0xc10da4: r0 = Column()
    //     0xc10da4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc10da8: mov             x1, x0
    // 0xc10dac: r0 = Instance_Axis
    //     0xc10dac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc10db0: stur            x1, [fp, #-8]
    // 0xc10db4: StoreField: r1->field_f = r0
    //     0xc10db4: stur            w0, [x1, #0xf]
    // 0xc10db8: r0 = Instance_MainAxisAlignment
    //     0xc10db8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc10dbc: ldr             x0, [x0, #0xa08]
    // 0xc10dc0: StoreField: r1->field_13 = r0
    //     0xc10dc0: stur            w0, [x1, #0x13]
    // 0xc10dc4: r0 = Instance_MainAxisSize
    //     0xc10dc4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc10dc8: ldr             x0, [x0, #0xdd0]
    // 0xc10dcc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc10dcc: stur            w0, [x1, #0x17]
    // 0xc10dd0: r0 = Instance_CrossAxisAlignment
    //     0xc10dd0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc10dd4: ldr             x0, [x0, #0x890]
    // 0xc10dd8: StoreField: r1->field_1b = r0
    //     0xc10dd8: stur            w0, [x1, #0x1b]
    // 0xc10ddc: r0 = Instance_VerticalDirection
    //     0xc10ddc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc10de0: ldr             x0, [x0, #0xa20]
    // 0xc10de4: StoreField: r1->field_23 = r0
    //     0xc10de4: stur            w0, [x1, #0x23]
    // 0xc10de8: r0 = Instance_Clip
    //     0xc10de8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc10dec: ldr             x0, [x0, #0x38]
    // 0xc10df0: StoreField: r1->field_2b = r0
    //     0xc10df0: stur            w0, [x1, #0x2b]
    // 0xc10df4: StoreField: r1->field_2f = rZR
    //     0xc10df4: stur            xzr, [x1, #0x2f]
    // 0xc10df8: ldur            x0, [fp, #-0x10]
    // 0xc10dfc: StoreField: r1->field_b = r0
    //     0xc10dfc: stur            w0, [x1, #0xb]
    // 0xc10e00: r0 = Padding()
    //     0xc10e00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc10e04: mov             x1, x0
    // 0xc10e08: r0 = Instance_EdgeInsets
    //     0xc10e08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc10e0c: ldr             x0, [x0, #0x1f0]
    // 0xc10e10: stur            x1, [fp, #-0x10]
    // 0xc10e14: StoreField: r1->field_f = r0
    //     0xc10e14: stur            w0, [x1, #0xf]
    // 0xc10e18: ldur            x0, [fp, #-8]
    // 0xc10e1c: StoreField: r1->field_b = r0
    //     0xc10e1c: stur            w0, [x1, #0xb]
    // 0xc10e20: r0 = Container()
    //     0xc10e20: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc10e24: stur            x0, [fp, #-8]
    // 0xc10e28: ldur            x16, [fp, #-0x28]
    // 0xc10e2c: ldur            lr, [fp, #-0x10]
    // 0xc10e30: stp             lr, x16, [SP]
    // 0xc10e34: mov             x1, x0
    // 0xc10e38: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xc10e38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xc10e3c: ldr             x4, [x4, #0x88]
    // 0xc10e40: r0 = Container()
    //     0xc10e40: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc10e44: r0 = AnimatedContainer()
    //     0xc10e44: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xc10e48: stur            x0, [fp, #-0x10]
    // 0xc10e4c: r16 = Instance_Cubic
    //     0xc10e4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xc10e50: ldr             x16, [x16, #0xaf8]
    // 0xc10e54: r30 = Instance_EdgeInsets
    //     0xc10e54: add             lr, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xc10e58: ldr             lr, [lr, #0x18]
    // 0xc10e5c: stp             lr, x16, [SP]
    // 0xc10e60: mov             x1, x0
    // 0xc10e64: ldur            x2, [fp, #-8]
    // 0xc10e68: r3 = Instance_Duration
    //     0xc10e68: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xc10e6c: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, margin, 0x4, null]
    //     0xc10e6c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52218] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "margin", 0x4, Null]
    //     0xc10e70: ldr             x4, [x4, #0x218]
    // 0xc10e74: r0 = AnimatedContainer()
    //     0xc10e74: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xc10e78: ldur            x0, [fp, #-0x10]
    // 0xc10e7c: LeaveFrame
    //     0xc10e7c: mov             SP, fp
    //     0xc10e80: ldp             fp, lr, [SP], #0x10
    // 0xc10e84: ret
    //     0xc10e84: ret             
    // 0xc10e88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc10e88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc10e8c: b               #0xc1003c
    // 0xc10e90: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10e90: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10e94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10e94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10e98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10e98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10e9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10e9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ea0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10ea0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10ea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10ea4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ea8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10ea8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10eac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10eac: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10eb0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10eb0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10eb4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10eb4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10eb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10eb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10ebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10ec4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc10ec8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc10ecc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10ecc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc10ed0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc10ed0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc10ed4, size: 0x4cc
    // 0xc10ed4: EnterFrame
    //     0xc10ed4: stp             fp, lr, [SP, #-0x10]!
    //     0xc10ed8: mov             fp, SP
    // 0xc10edc: AllocStack(0x50)
    //     0xc10edc: sub             SP, SP, #0x50
    // 0xc10ee0: SetupParameters()
    //     0xc10ee0: ldr             x0, [fp, #0x20]
    //     0xc10ee4: ldur            w1, [x0, #0x17]
    //     0xc10ee8: add             x1, x1, HEAP, lsl #32
    //     0xc10eec: stur            x1, [fp, #-8]
    // 0xc10ef0: CheckStackOverflow
    //     0xc10ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc10ef4: cmp             SP, x16
    //     0xc10ef8: b.ls            #0xc11384
    // 0xc10efc: r1 = 2
    //     0xc10efc: movz            x1, #0x2
    // 0xc10f00: r0 = AllocateContext()
    //     0xc10f00: bl              #0x16f6108  ; AllocateContextStub
    // 0xc10f04: mov             x3, x0
    // 0xc10f08: ldur            x0, [fp, #-8]
    // 0xc10f0c: stur            x3, [fp, #-0x10]
    // 0xc10f10: StoreField: r3->field_b = r0
    //     0xc10f10: stur            w0, [x3, #0xb]
    // 0xc10f14: ldr             x1, [fp, #0x18]
    // 0xc10f18: StoreField: r3->field_f = r1
    //     0xc10f18: stur            w1, [x3, #0xf]
    // 0xc10f1c: ldr             x1, [fp, #0x10]
    // 0xc10f20: StoreField: r3->field_13 = r1
    //     0xc10f20: stur            w1, [x3, #0x13]
    // 0xc10f24: cmp             w1, #4
    // 0xc10f28: b.ne            #0xc111b4
    // 0xc10f2c: r4 = 4
    //     0xc10f2c: movz            x4, #0x4
    // 0xc10f30: mov             x2, x4
    // 0xc10f34: r1 = Null
    //     0xc10f34: mov             x1, NULL
    // 0xc10f38: r0 = AllocateArray()
    //     0xc10f38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc10f3c: stur            x0, [fp, #-0x18]
    // 0xc10f40: r16 = "+"
    //     0xc10f40: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xc10f44: StoreField: r0->field_f = r16
    //     0xc10f44: stur            w16, [x0, #0xf]
    // 0xc10f48: ldur            x1, [fp, #-8]
    // 0xc10f4c: LoadField: r2 = r1->field_f
    //     0xc10f4c: ldur            w2, [x1, #0xf]
    // 0xc10f50: DecompressPointer r2
    //     0xc10f50: add             x2, x2, HEAP, lsl #32
    // 0xc10f54: LoadField: r3 = r1->field_13
    //     0xc10f54: ldur            w3, [x1, #0x13]
    // 0xc10f58: DecompressPointer r3
    //     0xc10f58: add             x3, x3, HEAP, lsl #32
    // 0xc10f5c: stp             x3, x2, [SP]
    // 0xc10f60: r4 = 0
    //     0xc10f60: movz            x4, #0
    // 0xc10f64: ldr             x0, [SP, #8]
    // 0xc10f68: r16 = UnlinkedCall_0x613b5c
    //     0xc10f68: add             x16, PP, #0x52, lsl #12  ; [pp+0x52250] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc10f6c: add             x16, x16, #0x250
    // 0xc10f70: ldp             x5, lr, [x16]
    // 0xc10f74: blr             lr
    // 0xc10f78: cmp             w0, NULL
    // 0xc10f7c: b.eq            #0xc1138c
    // 0xc10f80: LoadField: r1 = r0->field_b7
    //     0xc10f80: ldur            w1, [x0, #0xb7]
    // 0xc10f84: DecompressPointer r1
    //     0xc10f84: add             x1, x1, HEAP, lsl #32
    // 0xc10f88: cmp             w1, NULL
    // 0xc10f8c: b.eq            #0xc11390
    // 0xc10f90: LoadField: r0 = r1->field_b
    //     0xc10f90: ldur            w0, [x1, #0xb]
    // 0xc10f94: r1 = LoadInt32Instr(r0)
    //     0xc10f94: sbfx            x1, x0, #1, #0x1f
    // 0xc10f98: sub             x0, x1, #2
    // 0xc10f9c: lsl             x1, x0, #1
    // 0xc10fa0: ldur            x0, [fp, #-0x18]
    // 0xc10fa4: StoreField: r0->field_13 = r1
    //     0xc10fa4: stur            w1, [x0, #0x13]
    // 0xc10fa8: str             x0, [SP]
    // 0xc10fac: r0 = _interpolate()
    //     0xc10fac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc10fb0: ldur            x2, [fp, #-0x10]
    // 0xc10fb4: stur            x0, [fp, #-0x18]
    // 0xc10fb8: LoadField: r1 = r2->field_f
    //     0xc10fb8: ldur            w1, [x2, #0xf]
    // 0xc10fbc: DecompressPointer r1
    //     0xc10fbc: add             x1, x1, HEAP, lsl #32
    // 0xc10fc0: r0 = of()
    //     0xc10fc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc10fc4: LoadField: r1 = r0->field_87
    //     0xc10fc4: ldur            w1, [x0, #0x87]
    // 0xc10fc8: DecompressPointer r1
    //     0xc10fc8: add             x1, x1, HEAP, lsl #32
    // 0xc10fcc: LoadField: r0 = r1->field_7
    //     0xc10fcc: ldur            w0, [x1, #7]
    // 0xc10fd0: DecompressPointer r0
    //     0xc10fd0: add             x0, x0, HEAP, lsl #32
    // 0xc10fd4: stur            x0, [fp, #-0x20]
    // 0xc10fd8: r1 = Instance_Color
    //     0xc10fd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc10fdc: d0 = 0.700000
    //     0xc10fdc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc10fe0: ldr             d0, [x17, #0xf48]
    // 0xc10fe4: r0 = withOpacity()
    //     0xc10fe4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc10fe8: r16 = 14.000000
    //     0xc10fe8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc10fec: ldr             x16, [x16, #0x1d8]
    // 0xc10ff0: stp             x0, x16, [SP]
    // 0xc10ff4: ldur            x1, [fp, #-0x20]
    // 0xc10ff8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc10ff8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc10ffc: ldr             x4, [x4, #0xaa0]
    // 0xc11000: r0 = copyWith()
    //     0xc11000: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc11004: stur            x0, [fp, #-0x20]
    // 0xc11008: r0 = Text()
    //     0xc11008: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc1100c: mov             x2, x0
    // 0xc11010: ldur            x0, [fp, #-0x18]
    // 0xc11014: stur            x2, [fp, #-0x28]
    // 0xc11018: StoreField: r2->field_b = r0
    //     0xc11018: stur            w0, [x2, #0xb]
    // 0xc1101c: ldur            x0, [fp, #-0x20]
    // 0xc11020: StoreField: r2->field_13 = r0
    //     0xc11020: stur            w0, [x2, #0x13]
    // 0xc11024: ldur            x0, [fp, #-0x10]
    // 0xc11028: LoadField: r1 = r0->field_f
    //     0xc11028: ldur            w1, [x0, #0xf]
    // 0xc1102c: DecompressPointer r1
    //     0xc1102c: add             x1, x1, HEAP, lsl #32
    // 0xc11030: r0 = of()
    //     0xc11030: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc11034: LoadField: r1 = r0->field_87
    //     0xc11034: ldur            w1, [x0, #0x87]
    // 0xc11038: DecompressPointer r1
    //     0xc11038: add             x1, x1, HEAP, lsl #32
    // 0xc1103c: LoadField: r0 = r1->field_7
    //     0xc1103c: ldur            w0, [x1, #7]
    // 0xc11040: DecompressPointer r0
    //     0xc11040: add             x0, x0, HEAP, lsl #32
    // 0xc11044: stur            x0, [fp, #-0x18]
    // 0xc11048: r1 = Instance_Color
    //     0xc11048: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1104c: d0 = 0.700000
    //     0xc1104c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc11050: ldr             d0, [x17, #0xf48]
    // 0xc11054: r0 = withOpacity()
    //     0xc11054: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc11058: r16 = 12.000000
    //     0xc11058: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc1105c: ldr             x16, [x16, #0x9e8]
    // 0xc11060: stp             x0, x16, [SP]
    // 0xc11064: ldur            x1, [fp, #-0x18]
    // 0xc11068: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc11068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc1106c: ldr             x4, [x4, #0xaa0]
    // 0xc11070: r0 = copyWith()
    //     0xc11070: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc11074: stur            x0, [fp, #-0x18]
    // 0xc11078: r0 = Text()
    //     0xc11078: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc1107c: mov             x3, x0
    // 0xc11080: r0 = "Photos"
    //     0xc11080: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xc11084: ldr             x0, [x0, #0x260]
    // 0xc11088: stur            x3, [fp, #-0x20]
    // 0xc1108c: StoreField: r3->field_b = r0
    //     0xc1108c: stur            w0, [x3, #0xb]
    // 0xc11090: ldur            x0, [fp, #-0x18]
    // 0xc11094: StoreField: r3->field_13 = r0
    //     0xc11094: stur            w0, [x3, #0x13]
    // 0xc11098: r1 = Null
    //     0xc11098: mov             x1, NULL
    // 0xc1109c: r2 = 4
    //     0xc1109c: movz            x2, #0x4
    // 0xc110a0: r0 = AllocateArray()
    //     0xc110a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc110a4: mov             x2, x0
    // 0xc110a8: ldur            x0, [fp, #-0x28]
    // 0xc110ac: stur            x2, [fp, #-0x18]
    // 0xc110b0: StoreField: r2->field_f = r0
    //     0xc110b0: stur            w0, [x2, #0xf]
    // 0xc110b4: ldur            x0, [fp, #-0x20]
    // 0xc110b8: StoreField: r2->field_13 = r0
    //     0xc110b8: stur            w0, [x2, #0x13]
    // 0xc110bc: r1 = <Widget>
    //     0xc110bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc110c0: r0 = AllocateGrowableArray()
    //     0xc110c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc110c4: mov             x1, x0
    // 0xc110c8: ldur            x0, [fp, #-0x18]
    // 0xc110cc: stur            x1, [fp, #-0x20]
    // 0xc110d0: StoreField: r1->field_f = r0
    //     0xc110d0: stur            w0, [x1, #0xf]
    // 0xc110d4: r0 = 4
    //     0xc110d4: movz            x0, #0x4
    // 0xc110d8: StoreField: r1->field_b = r0
    //     0xc110d8: stur            w0, [x1, #0xb]
    // 0xc110dc: r0 = Column()
    //     0xc110dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc110e0: mov             x1, x0
    // 0xc110e4: r0 = Instance_Axis
    //     0xc110e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc110e8: stur            x1, [fp, #-0x18]
    // 0xc110ec: StoreField: r1->field_f = r0
    //     0xc110ec: stur            w0, [x1, #0xf]
    // 0xc110f0: r0 = Instance_MainAxisAlignment
    //     0xc110f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc110f4: ldr             x0, [x0, #0xab0]
    // 0xc110f8: StoreField: r1->field_13 = r0
    //     0xc110f8: stur            w0, [x1, #0x13]
    // 0xc110fc: r0 = Instance_MainAxisSize
    //     0xc110fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc11100: ldr             x0, [x0, #0xa10]
    // 0xc11104: ArrayStore: r1[0] = r0  ; List_4
    //     0xc11104: stur            w0, [x1, #0x17]
    // 0xc11108: r0 = Instance_CrossAxisAlignment
    //     0xc11108: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc1110c: ldr             x0, [x0, #0xa18]
    // 0xc11110: StoreField: r1->field_1b = r0
    //     0xc11110: stur            w0, [x1, #0x1b]
    // 0xc11114: r0 = Instance_VerticalDirection
    //     0xc11114: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc11118: ldr             x0, [x0, #0xa20]
    // 0xc1111c: StoreField: r1->field_23 = r0
    //     0xc1111c: stur            w0, [x1, #0x23]
    // 0xc11120: r0 = Instance_Clip
    //     0xc11120: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc11124: ldr             x0, [x0, #0x38]
    // 0xc11128: StoreField: r1->field_2b = r0
    //     0xc11128: stur            w0, [x1, #0x2b]
    // 0xc1112c: StoreField: r1->field_2f = rZR
    //     0xc1112c: stur            xzr, [x1, #0x2f]
    // 0xc11130: ldur            x0, [fp, #-0x20]
    // 0xc11134: StoreField: r1->field_b = r0
    //     0xc11134: stur            w0, [x1, #0xb]
    // 0xc11138: r0 = Container()
    //     0xc11138: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1113c: stur            x0, [fp, #-0x20]
    // 0xc11140: r16 = 80.000000
    //     0xc11140: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xc11144: ldr             x16, [x16, #0x2f8]
    // 0xc11148: r30 = 65.000000
    //     0xc11148: add             lr, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xc1114c: ldr             lr, [lr, #0xd28]
    // 0xc11150: stp             lr, x16, [SP, #0x10]
    // 0xc11154: r16 = Instance_EdgeInsets
    //     0xc11154: add             x16, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xc11158: ldr             x16, [x16, #0x268]
    // 0xc1115c: ldur            lr, [fp, #-0x18]
    // 0xc11160: stp             lr, x16, [SP]
    // 0xc11164: mov             x1, x0
    // 0xc11168: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc11168: add             x4, PP, #0x52, lsl #12  ; [pp+0x52270] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc1116c: ldr             x4, [x4, #0x270]
    // 0xc11170: r0 = Container()
    //     0xc11170: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc11174: r0 = GestureDetector()
    //     0xc11174: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc11178: ldur            x2, [fp, #-0x10]
    // 0xc1117c: r1 = Function '<anonymous closure>':.
    //     0xc1117c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52278] AnonymousClosure: (0xc114ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc11180: ldr             x1, [x1, #0x278]
    // 0xc11184: stur            x0, [fp, #-0x18]
    // 0xc11188: r0 = AllocateClosure()
    //     0xc11188: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1118c: ldur            x16, [fp, #-0x20]
    // 0xc11190: stp             x16, x0, [SP]
    // 0xc11194: ldur            x1, [fp, #-0x18]
    // 0xc11198: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc11198: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc1119c: ldr             x4, [x4, #0xaf0]
    // 0xc111a0: r0 = GestureDetector()
    //     0xc111a0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc111a4: ldur            x0, [fp, #-0x18]
    // 0xc111a8: LeaveFrame
    //     0xc111a8: mov             SP, fp
    //     0xc111ac: ldp             fp, lr, [SP], #0x10
    // 0xc111b0: ret
    //     0xc111b0: ret             
    // 0xc111b4: mov             x1, x0
    // 0xc111b8: r0 = ImageHeaders.forImages()
    //     0xc111b8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc111bc: mov             x1, x0
    // 0xc111c0: ldur            x0, [fp, #-8]
    // 0xc111c4: stur            x1, [fp, #-0x18]
    // 0xc111c8: LoadField: r2 = r0->field_f
    //     0xc111c8: ldur            w2, [x0, #0xf]
    // 0xc111cc: DecompressPointer r2
    //     0xc111cc: add             x2, x2, HEAP, lsl #32
    // 0xc111d0: LoadField: r3 = r0->field_13
    //     0xc111d0: ldur            w3, [x0, #0x13]
    // 0xc111d4: DecompressPointer r3
    //     0xc111d4: add             x3, x3, HEAP, lsl #32
    // 0xc111d8: stp             x3, x2, [SP]
    // 0xc111dc: r4 = 0
    //     0xc111dc: movz            x4, #0
    // 0xc111e0: ldr             x0, [SP, #8]
    // 0xc111e4: r16 = UnlinkedCall_0x613b5c
    //     0xc111e4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52280] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc111e8: add             x16, x16, #0x280
    // 0xc111ec: ldp             x5, lr, [x16]
    // 0xc111f0: blr             lr
    // 0xc111f4: cmp             w0, NULL
    // 0xc111f8: b.ne            #0xc11208
    // 0xc111fc: ldur            x3, [fp, #-0x10]
    // 0xc11200: r4 = Null
    //     0xc11200: mov             x4, NULL
    // 0xc11204: b               #0xc11274
    // 0xc11208: ldur            x3, [fp, #-0x10]
    // 0xc1120c: LoadField: r2 = r0->field_b7
    //     0xc1120c: ldur            w2, [x0, #0xb7]
    // 0xc11210: DecompressPointer r2
    //     0xc11210: add             x2, x2, HEAP, lsl #32
    // 0xc11214: LoadField: r0 = r3->field_13
    //     0xc11214: ldur            w0, [x3, #0x13]
    // 0xc11218: DecompressPointer r0
    //     0xc11218: add             x0, x0, HEAP, lsl #32
    // 0xc1121c: cmp             w2, NULL
    // 0xc11220: b.eq            #0xc11394
    // 0xc11224: LoadField: r1 = r2->field_b
    //     0xc11224: ldur            w1, [x2, #0xb]
    // 0xc11228: r4 = LoadInt32Instr(r0)
    //     0xc11228: sbfx            x4, x0, #1, #0x1f
    //     0xc1122c: tbz             w0, #0, #0xc11234
    //     0xc11230: ldur            x4, [x0, #7]
    // 0xc11234: r0 = LoadInt32Instr(r1)
    //     0xc11234: sbfx            x0, x1, #1, #0x1f
    // 0xc11238: mov             x1, x4
    // 0xc1123c: cmp             x1, x0
    // 0xc11240: b.hs            #0xc11398
    // 0xc11244: LoadField: r0 = r2->field_f
    //     0xc11244: ldur            w0, [x2, #0xf]
    // 0xc11248: DecompressPointer r0
    //     0xc11248: add             x0, x0, HEAP, lsl #32
    // 0xc1124c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc1124c: add             x16, x0, x4, lsl #2
    //     0xc11250: ldur            w1, [x16, #0xf]
    // 0xc11254: DecompressPointer r1
    //     0xc11254: add             x1, x1, HEAP, lsl #32
    // 0xc11258: LoadField: r0 = r1->field_7
    //     0xc11258: ldur            w0, [x1, #7]
    // 0xc1125c: DecompressPointer r0
    //     0xc1125c: add             x0, x0, HEAP, lsl #32
    // 0xc11260: cmp             w0, NULL
    // 0xc11264: b.eq            #0xc1139c
    // 0xc11268: LoadField: r1 = r0->field_b
    //     0xc11268: ldur            w1, [x0, #0xb]
    // 0xc1126c: DecompressPointer r1
    //     0xc1126c: add             x1, x1, HEAP, lsl #32
    // 0xc11270: mov             x4, x1
    // 0xc11274: mov             x0, x4
    // 0xc11278: stur            x4, [fp, #-8]
    // 0xc1127c: r2 = Null
    //     0xc1127c: mov             x2, NULL
    // 0xc11280: r1 = Null
    //     0xc11280: mov             x1, NULL
    // 0xc11284: r4 = LoadClassIdInstr(r0)
    //     0xc11284: ldur            x4, [x0, #-1]
    //     0xc11288: ubfx            x4, x4, #0xc, #0x14
    // 0xc1128c: sub             x4, x4, #0x5e
    // 0xc11290: cmp             x4, #1
    // 0xc11294: b.ls            #0xc112a8
    // 0xc11298: r8 = String
    //     0xc11298: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc1129c: r3 = Null
    //     0xc1129c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52290] Null
    //     0xc112a0: ldr             x3, [x3, #0x290]
    // 0xc112a4: r0 = String()
    //     0xc112a4: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc112a8: r1 = Function '<anonymous closure>':.
    //     0xc112a8: add             x1, PP, #0x52, lsl #12  ; [pp+0x522a0] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc112ac: ldr             x1, [x1, #0x2a0]
    // 0xc112b0: r2 = Null
    //     0xc112b0: mov             x2, NULL
    // 0xc112b4: r0 = AllocateClosure()
    //     0xc112b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc112b8: r1 = Function '<anonymous closure>':.
    //     0xc112b8: add             x1, PP, #0x52, lsl #12  ; [pp+0x522a8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc112bc: ldr             x1, [x1, #0x2a8]
    // 0xc112c0: r2 = Null
    //     0xc112c0: mov             x2, NULL
    // 0xc112c4: stur            x0, [fp, #-0x20]
    // 0xc112c8: r0 = AllocateClosure()
    //     0xc112c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc112cc: stur            x0, [fp, #-0x28]
    // 0xc112d0: r0 = CachedNetworkImage()
    //     0xc112d0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc112d4: stur            x0, [fp, #-0x30]
    // 0xc112d8: ldur            x16, [fp, #-0x18]
    // 0xc112dc: ldur            lr, [fp, #-0x20]
    // 0xc112e0: stp             lr, x16, [SP, #0x10]
    // 0xc112e4: ldur            x16, [fp, #-0x28]
    // 0xc112e8: r30 = Instance_BoxFit
    //     0xc112e8: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc112ec: ldr             lr, [lr, #0xb18]
    // 0xc112f0: stp             lr, x16, [SP]
    // 0xc112f4: mov             x1, x0
    // 0xc112f8: ldur            x2, [fp, #-8]
    // 0xc112fc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xc112fc: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xc11300: ldr             x4, [x4, #0x2b0]
    // 0xc11304: r0 = CachedNetworkImage()
    //     0xc11304: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc11308: r0 = Container()
    //     0xc11308: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1130c: stur            x0, [fp, #-8]
    // 0xc11310: r16 = 80.000000
    //     0xc11310: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xc11314: ldr             x16, [x16, #0x2f8]
    // 0xc11318: r30 = 65.000000
    //     0xc11318: add             lr, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xc1131c: ldr             lr, [lr, #0xd28]
    // 0xc11320: stp             lr, x16, [SP, #0x10]
    // 0xc11324: r16 = Instance_EdgeInsets
    //     0xc11324: add             x16, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xc11328: ldr             x16, [x16, #0x268]
    // 0xc1132c: ldur            lr, [fp, #-0x30]
    // 0xc11330: stp             lr, x16, [SP]
    // 0xc11334: mov             x1, x0
    // 0xc11338: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc11338: add             x4, PP, #0x52, lsl #12  ; [pp+0x52270] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc1133c: ldr             x4, [x4, #0x270]
    // 0xc11340: r0 = Container()
    //     0xc11340: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc11344: r0 = GestureDetector()
    //     0xc11344: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc11348: ldur            x2, [fp, #-0x10]
    // 0xc1134c: r1 = Function '<anonymous closure>':.
    //     0xc1134c: add             x1, PP, #0x52, lsl #12  ; [pp+0x522b8] AnonymousClosure: (0xc113a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc11350: ldr             x1, [x1, #0x2b8]
    // 0xc11354: stur            x0, [fp, #-0x10]
    // 0xc11358: r0 = AllocateClosure()
    //     0xc11358: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1135c: ldur            x16, [fp, #-8]
    // 0xc11360: stp             x16, x0, [SP]
    // 0xc11364: ldur            x1, [fp, #-0x10]
    // 0xc11368: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc11368: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc1136c: ldr             x4, [x4, #0xaf0]
    // 0xc11370: r0 = GestureDetector()
    //     0xc11370: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc11374: ldur            x0, [fp, #-0x10]
    // 0xc11378: LeaveFrame
    //     0xc11378: mov             SP, fp
    //     0xc1137c: ldp             fp, lr, [SP], #0x10
    // 0xc11380: ret
    //     0xc11380: ret             
    // 0xc11384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11384: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11388: b               #0xc10efc
    // 0xc1138c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc1138c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc11390: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc11390: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc11394: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc11394: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc11398: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc11398: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc1139c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc1139c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc113a0, size: 0x9c
    // 0xc113a0: EnterFrame
    //     0xc113a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc113a4: mov             fp, SP
    // 0xc113a8: AllocStack(0x28)
    //     0xc113a8: sub             SP, SP, #0x28
    // 0xc113ac: SetupParameters()
    //     0xc113ac: ldr             x0, [fp, #0x10]
    //     0xc113b0: ldur            w2, [x0, #0x17]
    //     0xc113b4: add             x2, x2, HEAP, lsl #32
    //     0xc113b8: stur            x2, [fp, #-8]
    // 0xc113bc: CheckStackOverflow
    //     0xc113bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc113c0: cmp             SP, x16
    //     0xc113c4: b.ls            #0xc11434
    // 0xc113c8: LoadField: r1 = r2->field_f
    //     0xc113c8: ldur            w1, [x2, #0xf]
    // 0xc113cc: DecompressPointer r1
    //     0xc113cc: add             x1, x1, HEAP, lsl #32
    // 0xc113d0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc113d0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc113d4: r0 = of()
    //     0xc113d4: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xc113d8: ldur            x2, [fp, #-8]
    // 0xc113dc: r1 = Function '<anonymous closure>':.
    //     0xc113dc: add             x1, PP, #0x52, lsl #12  ; [pp+0x522c8] AnonymousClosure: (0xc1143c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc113e0: ldr             x1, [x1, #0x2c8]
    // 0xc113e4: stur            x0, [fp, #-8]
    // 0xc113e8: r0 = AllocateClosure()
    //     0xc113e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc113ec: r1 = Null
    //     0xc113ec: mov             x1, NULL
    // 0xc113f0: stur            x0, [fp, #-0x10]
    // 0xc113f4: r0 = MaterialPageRoute()
    //     0xc113f4: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xc113f8: mov             x1, x0
    // 0xc113fc: ldur            x2, [fp, #-0x10]
    // 0xc11400: stur            x0, [fp, #-0x10]
    // 0xc11404: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc11404: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc11408: r0 = MaterialPageRoute()
    //     0xc11408: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xc1140c: ldur            x16, [fp, #-8]
    // 0xc11410: stp             x16, NULL, [SP, #8]
    // 0xc11414: ldur            x16, [fp, #-0x10]
    // 0xc11418: str             x16, [SP]
    // 0xc1141c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc1141c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc11420: r0 = push()
    //     0xc11420: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xc11424: r0 = Null
    //     0xc11424: mov             x0, NULL
    // 0xc11428: LeaveFrame
    //     0xc11428: mov             SP, fp
    //     0xc1142c: ldp             fp, lr, [SP], #0x10
    // 0xc11430: ret
    //     0xc11430: ret             
    // 0xc11434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11434: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11438: b               #0xc113c8
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xc1143c, size: 0xb0
    // 0xc1143c: EnterFrame
    //     0xc1143c: stp             fp, lr, [SP, #-0x10]!
    //     0xc11440: mov             fp, SP
    // 0xc11444: AllocStack(0x20)
    //     0xc11444: sub             SP, SP, #0x20
    // 0xc11448: SetupParameters()
    //     0xc11448: ldr             x0, [fp, #0x18]
    //     0xc1144c: ldur            w1, [x0, #0x17]
    //     0xc11450: add             x1, x1, HEAP, lsl #32
    // 0xc11454: CheckStackOverflow
    //     0xc11454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11458: cmp             SP, x16
    //     0xc1145c: b.ls            #0xc114e0
    // 0xc11460: LoadField: r0 = r1->field_13
    //     0xc11460: ldur            w0, [x1, #0x13]
    // 0xc11464: DecompressPointer r0
    //     0xc11464: add             x0, x0, HEAP, lsl #32
    // 0xc11468: stur            x0, [fp, #-8]
    // 0xc1146c: LoadField: r2 = r1->field_b
    //     0xc1146c: ldur            w2, [x1, #0xb]
    // 0xc11470: DecompressPointer r2
    //     0xc11470: add             x2, x2, HEAP, lsl #32
    // 0xc11474: LoadField: r1 = r2->field_f
    //     0xc11474: ldur            w1, [x2, #0xf]
    // 0xc11478: DecompressPointer r1
    //     0xc11478: add             x1, x1, HEAP, lsl #32
    // 0xc1147c: LoadField: r3 = r2->field_13
    //     0xc1147c: ldur            w3, [x2, #0x13]
    // 0xc11480: DecompressPointer r3
    //     0xc11480: add             x3, x3, HEAP, lsl #32
    // 0xc11484: stp             x3, x1, [SP]
    // 0xc11488: r4 = 0
    //     0xc11488: movz            x4, #0
    // 0xc1148c: ldr             x0, [SP, #8]
    // 0xc11490: r16 = UnlinkedCall_0x613b5c
    //     0xc11490: add             x16, PP, #0x52, lsl #12  ; [pp+0x522d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc11494: add             x16, x16, #0x2d0
    // 0xc11498: ldp             x5, lr, [x16]
    // 0xc1149c: blr             lr
    // 0xc114a0: cmp             w0, NULL
    // 0xc114a4: b.eq            #0xc114e8
    // 0xc114a8: LoadField: r1 = r0->field_b7
    //     0xc114a8: ldur            w1, [x0, #0xb7]
    // 0xc114ac: DecompressPointer r1
    //     0xc114ac: add             x1, x1, HEAP, lsl #32
    // 0xc114b0: stur            x1, [fp, #-0x10]
    // 0xc114b4: r0 = TestimonialMoreImagesWidget()
    //     0xc114b4: bl              #0xbf27a4  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xc114b8: ldur            x1, [fp, #-0x10]
    // 0xc114bc: StoreField: r0->field_b = r1
    //     0xc114bc: stur            w1, [x0, #0xb]
    // 0xc114c0: ldur            x1, [fp, #-8]
    // 0xc114c4: r2 = LoadInt32Instr(r1)
    //     0xc114c4: sbfx            x2, x1, #1, #0x1f
    //     0xc114c8: tbz             w1, #0, #0xc114d0
    //     0xc114cc: ldur            x2, [x1, #7]
    // 0xc114d0: StoreField: r0->field_f = r2
    //     0xc114d0: stur            x2, [x0, #0xf]
    // 0xc114d4: LeaveFrame
    //     0xc114d4: mov             SP, fp
    //     0xc114d8: ldp             fp, lr, [SP], #0x10
    // 0xc114dc: ret
    //     0xc114dc: ret             
    // 0xc114e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc114e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc114e4: b               #0xc11460
    // 0xc114e8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc114e8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc114ec, size: 0x9c
    // 0xc114ec: EnterFrame
    //     0xc114ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc114f0: mov             fp, SP
    // 0xc114f4: AllocStack(0x28)
    //     0xc114f4: sub             SP, SP, #0x28
    // 0xc114f8: SetupParameters()
    //     0xc114f8: ldr             x0, [fp, #0x10]
    //     0xc114fc: ldur            w2, [x0, #0x17]
    //     0xc11500: add             x2, x2, HEAP, lsl #32
    //     0xc11504: stur            x2, [fp, #-8]
    // 0xc11508: CheckStackOverflow
    //     0xc11508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1150c: cmp             SP, x16
    //     0xc11510: b.ls            #0xc11580
    // 0xc11514: LoadField: r1 = r2->field_f
    //     0xc11514: ldur            w1, [x2, #0xf]
    // 0xc11518: DecompressPointer r1
    //     0xc11518: add             x1, x1, HEAP, lsl #32
    // 0xc1151c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1151c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc11520: r0 = of()
    //     0xc11520: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xc11524: ldur            x2, [fp, #-8]
    // 0xc11528: r1 = Function '<anonymous closure>':.
    //     0xc11528: add             x1, PP, #0x52, lsl #12  ; [pp+0x522e0] AnonymousClosure: (0xc11588), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc1152c: ldr             x1, [x1, #0x2e0]
    // 0xc11530: stur            x0, [fp, #-8]
    // 0xc11534: r0 = AllocateClosure()
    //     0xc11534: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc11538: r1 = Null
    //     0xc11538: mov             x1, NULL
    // 0xc1153c: stur            x0, [fp, #-0x10]
    // 0xc11540: r0 = MaterialPageRoute()
    //     0xc11540: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xc11544: mov             x1, x0
    // 0xc11548: ldur            x2, [fp, #-0x10]
    // 0xc1154c: stur            x0, [fp, #-0x10]
    // 0xc11550: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc11550: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc11554: r0 = MaterialPageRoute()
    //     0xc11554: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xc11558: ldur            x16, [fp, #-8]
    // 0xc1155c: stp             x16, NULL, [SP, #8]
    // 0xc11560: ldur            x16, [fp, #-0x10]
    // 0xc11564: str             x16, [SP]
    // 0xc11568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc11568: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc1156c: r0 = push()
    //     0xc1156c: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xc11570: r0 = Null
    //     0xc11570: mov             x0, NULL
    // 0xc11574: LeaveFrame
    //     0xc11574: mov             SP, fp
    //     0xc11578: ldp             fp, lr, [SP], #0x10
    // 0xc1157c: ret
    //     0xc1157c: ret             
    // 0xc11580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11580: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11584: b               #0xc11514
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xc11588, size: 0xb0
    // 0xc11588: EnterFrame
    //     0xc11588: stp             fp, lr, [SP, #-0x10]!
    //     0xc1158c: mov             fp, SP
    // 0xc11590: AllocStack(0x20)
    //     0xc11590: sub             SP, SP, #0x20
    // 0xc11594: SetupParameters()
    //     0xc11594: ldr             x0, [fp, #0x18]
    //     0xc11598: ldur            w1, [x0, #0x17]
    //     0xc1159c: add             x1, x1, HEAP, lsl #32
    // 0xc115a0: CheckStackOverflow
    //     0xc115a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc115a4: cmp             SP, x16
    //     0xc115a8: b.ls            #0xc1162c
    // 0xc115ac: LoadField: r0 = r1->field_13
    //     0xc115ac: ldur            w0, [x1, #0x13]
    // 0xc115b0: DecompressPointer r0
    //     0xc115b0: add             x0, x0, HEAP, lsl #32
    // 0xc115b4: stur            x0, [fp, #-8]
    // 0xc115b8: LoadField: r2 = r1->field_b
    //     0xc115b8: ldur            w2, [x1, #0xb]
    // 0xc115bc: DecompressPointer r2
    //     0xc115bc: add             x2, x2, HEAP, lsl #32
    // 0xc115c0: LoadField: r1 = r2->field_f
    //     0xc115c0: ldur            w1, [x2, #0xf]
    // 0xc115c4: DecompressPointer r1
    //     0xc115c4: add             x1, x1, HEAP, lsl #32
    // 0xc115c8: LoadField: r3 = r2->field_13
    //     0xc115c8: ldur            w3, [x2, #0x13]
    // 0xc115cc: DecompressPointer r3
    //     0xc115cc: add             x3, x3, HEAP, lsl #32
    // 0xc115d0: stp             x3, x1, [SP]
    // 0xc115d4: r4 = 0
    //     0xc115d4: movz            x4, #0
    // 0xc115d8: ldr             x0, [SP, #8]
    // 0xc115dc: r16 = UnlinkedCall_0x613b5c
    //     0xc115dc: add             x16, PP, #0x52, lsl #12  ; [pp+0x522e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc115e0: add             x16, x16, #0x2e8
    // 0xc115e4: ldp             x5, lr, [x16]
    // 0xc115e8: blr             lr
    // 0xc115ec: cmp             w0, NULL
    // 0xc115f0: b.eq            #0xc11634
    // 0xc115f4: LoadField: r1 = r0->field_b7
    //     0xc115f4: ldur            w1, [x0, #0xb7]
    // 0xc115f8: DecompressPointer r1
    //     0xc115f8: add             x1, x1, HEAP, lsl #32
    // 0xc115fc: stur            x1, [fp, #-0x10]
    // 0xc11600: r0 = TestimonialMoreImagesWidget()
    //     0xc11600: bl              #0xbf27a4  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xc11604: ldur            x1, [fp, #-0x10]
    // 0xc11608: StoreField: r0->field_b = r1
    //     0xc11608: stur            w1, [x0, #0xb]
    // 0xc1160c: ldur            x1, [fp, #-8]
    // 0xc11610: r2 = LoadInt32Instr(r1)
    //     0xc11610: sbfx            x2, x1, #1, #0x1f
    //     0xc11614: tbz             w1, #0, #0xc1161c
    //     0xc11618: ldur            x2, [x1, #7]
    // 0xc1161c: StoreField: r0->field_f = r2
    //     0xc1161c: stur            x2, [x0, #0xf]
    // 0xc11620: LeaveFrame
    //     0xc11620: mov             SP, fp
    //     0xc11624: ldp             fp, lr, [SP], #0x10
    // 0xc11628: ret
    //     0xc11628: ret             
    // 0xc1162c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1162c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11630: b               #0xc115ac
    // 0xc11634: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc11634: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc11638, size: 0x230
    // 0xc11638: EnterFrame
    //     0xc11638: stp             fp, lr, [SP, #-0x10]!
    //     0xc1163c: mov             fp, SP
    // 0xc11640: AllocStack(0x50)
    //     0xc11640: sub             SP, SP, #0x50
    // 0xc11644: SetupParameters()
    //     0xc11644: ldr             x0, [fp, #0x20]
    //     0xc11648: ldur            w1, [x0, #0x17]
    //     0xc1164c: add             x1, x1, HEAP, lsl #32
    //     0xc11650: stur            x1, [fp, #-8]
    // 0xc11654: CheckStackOverflow
    //     0xc11654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11658: cmp             SP, x16
    //     0xc1165c: b.ls            #0xc11854
    // 0xc11660: r1 = 2
    //     0xc11660: movz            x1, #0x2
    // 0xc11664: r0 = AllocateContext()
    //     0xc11664: bl              #0x16f6108  ; AllocateContextStub
    // 0xc11668: mov             x1, x0
    // 0xc1166c: ldur            x0, [fp, #-8]
    // 0xc11670: stur            x1, [fp, #-0x10]
    // 0xc11674: StoreField: r1->field_b = r0
    //     0xc11674: stur            w0, [x1, #0xb]
    // 0xc11678: ldr             x2, [fp, #0x18]
    // 0xc1167c: StoreField: r1->field_f = r2
    //     0xc1167c: stur            w2, [x1, #0xf]
    // 0xc11680: ldr             x2, [fp, #0x10]
    // 0xc11684: StoreField: r1->field_13 = r2
    //     0xc11684: stur            w2, [x1, #0x13]
    // 0xc11688: r0 = ImageHeaders.forImages()
    //     0xc11688: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc1168c: mov             x1, x0
    // 0xc11690: ldur            x0, [fp, #-8]
    // 0xc11694: stur            x1, [fp, #-0x18]
    // 0xc11698: LoadField: r2 = r0->field_f
    //     0xc11698: ldur            w2, [x0, #0xf]
    // 0xc1169c: DecompressPointer r2
    //     0xc1169c: add             x2, x2, HEAP, lsl #32
    // 0xc116a0: LoadField: r3 = r0->field_13
    //     0xc116a0: ldur            w3, [x0, #0x13]
    // 0xc116a4: DecompressPointer r3
    //     0xc116a4: add             x3, x3, HEAP, lsl #32
    // 0xc116a8: stp             x3, x2, [SP]
    // 0xc116ac: r4 = 0
    //     0xc116ac: movz            x4, #0
    // 0xc116b0: ldr             x0, [SP, #8]
    // 0xc116b4: r16 = UnlinkedCall_0x613b5c
    //     0xc116b4: add             x16, PP, #0x52, lsl #12  ; [pp+0x522f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc116b8: add             x16, x16, #0x2f8
    // 0xc116bc: ldp             x5, lr, [x16]
    // 0xc116c0: blr             lr
    // 0xc116c4: cmp             w0, NULL
    // 0xc116c8: b.ne            #0xc116d8
    // 0xc116cc: ldur            x3, [fp, #-0x10]
    // 0xc116d0: r4 = Null
    //     0xc116d0: mov             x4, NULL
    // 0xc116d4: b               #0xc11744
    // 0xc116d8: ldur            x3, [fp, #-0x10]
    // 0xc116dc: LoadField: r2 = r0->field_b7
    //     0xc116dc: ldur            w2, [x0, #0xb7]
    // 0xc116e0: DecompressPointer r2
    //     0xc116e0: add             x2, x2, HEAP, lsl #32
    // 0xc116e4: LoadField: r0 = r3->field_13
    //     0xc116e4: ldur            w0, [x3, #0x13]
    // 0xc116e8: DecompressPointer r0
    //     0xc116e8: add             x0, x0, HEAP, lsl #32
    // 0xc116ec: cmp             w2, NULL
    // 0xc116f0: b.eq            #0xc1185c
    // 0xc116f4: LoadField: r1 = r2->field_b
    //     0xc116f4: ldur            w1, [x2, #0xb]
    // 0xc116f8: r4 = LoadInt32Instr(r0)
    //     0xc116f8: sbfx            x4, x0, #1, #0x1f
    //     0xc116fc: tbz             w0, #0, #0xc11704
    //     0xc11700: ldur            x4, [x0, #7]
    // 0xc11704: r0 = LoadInt32Instr(r1)
    //     0xc11704: sbfx            x0, x1, #1, #0x1f
    // 0xc11708: mov             x1, x4
    // 0xc1170c: cmp             x1, x0
    // 0xc11710: b.hs            #0xc11860
    // 0xc11714: LoadField: r0 = r2->field_f
    //     0xc11714: ldur            w0, [x2, #0xf]
    // 0xc11718: DecompressPointer r0
    //     0xc11718: add             x0, x0, HEAP, lsl #32
    // 0xc1171c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc1171c: add             x16, x0, x4, lsl #2
    //     0xc11720: ldur            w1, [x16, #0xf]
    // 0xc11724: DecompressPointer r1
    //     0xc11724: add             x1, x1, HEAP, lsl #32
    // 0xc11728: LoadField: r0 = r1->field_7
    //     0xc11728: ldur            w0, [x1, #7]
    // 0xc1172c: DecompressPointer r0
    //     0xc1172c: add             x0, x0, HEAP, lsl #32
    // 0xc11730: cmp             w0, NULL
    // 0xc11734: b.eq            #0xc11864
    // 0xc11738: LoadField: r1 = r0->field_b
    //     0xc11738: ldur            w1, [x0, #0xb]
    // 0xc1173c: DecompressPointer r1
    //     0xc1173c: add             x1, x1, HEAP, lsl #32
    // 0xc11740: mov             x4, x1
    // 0xc11744: mov             x0, x4
    // 0xc11748: stur            x4, [fp, #-8]
    // 0xc1174c: r2 = Null
    //     0xc1174c: mov             x2, NULL
    // 0xc11750: r1 = Null
    //     0xc11750: mov             x1, NULL
    // 0xc11754: r4 = LoadClassIdInstr(r0)
    //     0xc11754: ldur            x4, [x0, #-1]
    //     0xc11758: ubfx            x4, x4, #0xc, #0x14
    // 0xc1175c: sub             x4, x4, #0x5e
    // 0xc11760: cmp             x4, #1
    // 0xc11764: b.ls            #0xc11778
    // 0xc11768: r8 = String
    //     0xc11768: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc1176c: r3 = Null
    //     0xc1176c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52308] Null
    //     0xc11770: ldr             x3, [x3, #0x308]
    // 0xc11774: r0 = String()
    //     0xc11774: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc11778: r1 = Function '<anonymous closure>':.
    //     0xc11778: add             x1, PP, #0x52, lsl #12  ; [pp+0x52318] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc1177c: ldr             x1, [x1, #0x318]
    // 0xc11780: r2 = Null
    //     0xc11780: mov             x2, NULL
    // 0xc11784: r0 = AllocateClosure()
    //     0xc11784: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc11788: r1 = Function '<anonymous closure>':.
    //     0xc11788: add             x1, PP, #0x52, lsl #12  ; [pp+0x52320] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc1178c: ldr             x1, [x1, #0x320]
    // 0xc11790: r2 = Null
    //     0xc11790: mov             x2, NULL
    // 0xc11794: stur            x0, [fp, #-0x20]
    // 0xc11798: r0 = AllocateClosure()
    //     0xc11798: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1179c: stur            x0, [fp, #-0x28]
    // 0xc117a0: r0 = CachedNetworkImage()
    //     0xc117a0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc117a4: stur            x0, [fp, #-0x30]
    // 0xc117a8: ldur            x16, [fp, #-0x18]
    // 0xc117ac: ldur            lr, [fp, #-0x20]
    // 0xc117b0: stp             lr, x16, [SP, #0x10]
    // 0xc117b4: ldur            x16, [fp, #-0x28]
    // 0xc117b8: r30 = Instance_BoxFit
    //     0xc117b8: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc117bc: ldr             lr, [lr, #0xb18]
    // 0xc117c0: stp             lr, x16, [SP]
    // 0xc117c4: mov             x1, x0
    // 0xc117c8: ldur            x2, [fp, #-8]
    // 0xc117cc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xc117cc: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xc117d0: ldr             x4, [x4, #0x2b0]
    // 0xc117d4: r0 = CachedNetworkImage()
    //     0xc117d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc117d8: r0 = Container()
    //     0xc117d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc117dc: stur            x0, [fp, #-8]
    // 0xc117e0: r16 = 80.000000
    //     0xc117e0: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xc117e4: ldr             x16, [x16, #0x2f8]
    // 0xc117e8: r30 = 65.000000
    //     0xc117e8: add             lr, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xc117ec: ldr             lr, [lr, #0xd28]
    // 0xc117f0: stp             lr, x16, [SP, #0x10]
    // 0xc117f4: r16 = Instance_EdgeInsets
    //     0xc117f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc117f8: ldr             x16, [x16, #0x980]
    // 0xc117fc: ldur            lr, [fp, #-0x30]
    // 0xc11800: stp             lr, x16, [SP]
    // 0xc11804: mov             x1, x0
    // 0xc11808: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc11808: add             x4, PP, #0x52, lsl #12  ; [pp+0x52270] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc1180c: ldr             x4, [x4, #0x270]
    // 0xc11810: r0 = Container()
    //     0xc11810: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc11814: r0 = GestureDetector()
    //     0xc11814: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc11818: ldur            x2, [fp, #-0x10]
    // 0xc1181c: r1 = Function '<anonymous closure>':.
    //     0xc1181c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52328] AnonymousClosure: (0xc11868), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc11820: ldr             x1, [x1, #0x328]
    // 0xc11824: stur            x0, [fp, #-0x10]
    // 0xc11828: r0 = AllocateClosure()
    //     0xc11828: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1182c: ldur            x16, [fp, #-8]
    // 0xc11830: stp             x16, x0, [SP]
    // 0xc11834: ldur            x1, [fp, #-0x10]
    // 0xc11838: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc11838: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc1183c: ldr             x4, [x4, #0xaf0]
    // 0xc11840: r0 = GestureDetector()
    //     0xc11840: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc11844: ldur            x0, [fp, #-0x10]
    // 0xc11848: LeaveFrame
    //     0xc11848: mov             SP, fp
    //     0xc1184c: ldp             fp, lr, [SP], #0x10
    // 0xc11850: ret
    //     0xc11850: ret             
    // 0xc11854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11854: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11858: b               #0xc11660
    // 0xc1185c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc1185c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xc11860: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc11860: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc11864: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc11864: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc11868, size: 0x9c
    // 0xc11868: EnterFrame
    //     0xc11868: stp             fp, lr, [SP, #-0x10]!
    //     0xc1186c: mov             fp, SP
    // 0xc11870: AllocStack(0x28)
    //     0xc11870: sub             SP, SP, #0x28
    // 0xc11874: SetupParameters()
    //     0xc11874: ldr             x0, [fp, #0x10]
    //     0xc11878: ldur            w2, [x0, #0x17]
    //     0xc1187c: add             x2, x2, HEAP, lsl #32
    //     0xc11880: stur            x2, [fp, #-8]
    // 0xc11884: CheckStackOverflow
    //     0xc11884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11888: cmp             SP, x16
    //     0xc1188c: b.ls            #0xc118fc
    // 0xc11890: LoadField: r1 = r2->field_f
    //     0xc11890: ldur            w1, [x2, #0xf]
    // 0xc11894: DecompressPointer r1
    //     0xc11894: add             x1, x1, HEAP, lsl #32
    // 0xc11898: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc11898: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc1189c: r0 = of()
    //     0xc1189c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xc118a0: ldur            x2, [fp, #-8]
    // 0xc118a4: r1 = Function '<anonymous closure>':.
    //     0xc118a4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52330] AnonymousClosure: (0xc11904), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::lineThemeSlider (0xc10018)
    //     0xc118a8: ldr             x1, [x1, #0x330]
    // 0xc118ac: stur            x0, [fp, #-8]
    // 0xc118b0: r0 = AllocateClosure()
    //     0xc118b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc118b4: r1 = Null
    //     0xc118b4: mov             x1, NULL
    // 0xc118b8: stur            x0, [fp, #-0x10]
    // 0xc118bc: r0 = MaterialPageRoute()
    //     0xc118bc: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xc118c0: mov             x1, x0
    // 0xc118c4: ldur            x2, [fp, #-0x10]
    // 0xc118c8: stur            x0, [fp, #-0x10]
    // 0xc118cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc118cc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc118d0: r0 = MaterialPageRoute()
    //     0xc118d0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xc118d4: ldur            x16, [fp, #-8]
    // 0xc118d8: stp             x16, NULL, [SP, #8]
    // 0xc118dc: ldur            x16, [fp, #-0x10]
    // 0xc118e0: str             x16, [SP]
    // 0xc118e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc118e4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc118e8: r0 = push()
    //     0xc118e8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xc118ec: r0 = Null
    //     0xc118ec: mov             x0, NULL
    // 0xc118f0: LeaveFrame
    //     0xc118f0: mov             SP, fp
    //     0xc118f4: ldp             fp, lr, [SP], #0x10
    // 0xc118f8: ret
    //     0xc118f8: ret             
    // 0xc118fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc118fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11900: b               #0xc11890
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xc11904, size: 0xb0
    // 0xc11904: EnterFrame
    //     0xc11904: stp             fp, lr, [SP, #-0x10]!
    //     0xc11908: mov             fp, SP
    // 0xc1190c: AllocStack(0x20)
    //     0xc1190c: sub             SP, SP, #0x20
    // 0xc11910: SetupParameters()
    //     0xc11910: ldr             x0, [fp, #0x18]
    //     0xc11914: ldur            w1, [x0, #0x17]
    //     0xc11918: add             x1, x1, HEAP, lsl #32
    // 0xc1191c: CheckStackOverflow
    //     0xc1191c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11920: cmp             SP, x16
    //     0xc11924: b.ls            #0xc119a8
    // 0xc11928: LoadField: r0 = r1->field_13
    //     0xc11928: ldur            w0, [x1, #0x13]
    // 0xc1192c: DecompressPointer r0
    //     0xc1192c: add             x0, x0, HEAP, lsl #32
    // 0xc11930: stur            x0, [fp, #-8]
    // 0xc11934: LoadField: r2 = r1->field_b
    //     0xc11934: ldur            w2, [x1, #0xb]
    // 0xc11938: DecompressPointer r2
    //     0xc11938: add             x2, x2, HEAP, lsl #32
    // 0xc1193c: LoadField: r1 = r2->field_f
    //     0xc1193c: ldur            w1, [x2, #0xf]
    // 0xc11940: DecompressPointer r1
    //     0xc11940: add             x1, x1, HEAP, lsl #32
    // 0xc11944: LoadField: r3 = r2->field_13
    //     0xc11944: ldur            w3, [x2, #0x13]
    // 0xc11948: DecompressPointer r3
    //     0xc11948: add             x3, x3, HEAP, lsl #32
    // 0xc1194c: stp             x3, x1, [SP]
    // 0xc11950: r4 = 0
    //     0xc11950: movz            x4, #0
    // 0xc11954: ldr             x0, [SP, #8]
    // 0xc11958: r16 = UnlinkedCall_0x613b5c
    //     0xc11958: add             x16, PP, #0x52, lsl #12  ; [pp+0x52338] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc1195c: add             x16, x16, #0x338
    // 0xc11960: ldp             x5, lr, [x16]
    // 0xc11964: blr             lr
    // 0xc11968: cmp             w0, NULL
    // 0xc1196c: b.eq            #0xc119b0
    // 0xc11970: LoadField: r1 = r0->field_b7
    //     0xc11970: ldur            w1, [x0, #0xb7]
    // 0xc11974: DecompressPointer r1
    //     0xc11974: add             x1, x1, HEAP, lsl #32
    // 0xc11978: stur            x1, [fp, #-0x10]
    // 0xc1197c: r0 = TestimonialMoreImagesWidget()
    //     0xc1197c: bl              #0xbf27a4  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xc11980: ldur            x1, [fp, #-0x10]
    // 0xc11984: StoreField: r0->field_b = r1
    //     0xc11984: stur            w1, [x0, #0xb]
    // 0xc11988: ldur            x1, [fp, #-8]
    // 0xc1198c: r2 = LoadInt32Instr(r1)
    //     0xc1198c: sbfx            x2, x1, #1, #0x1f
    //     0xc11990: tbz             w1, #0, #0xc11998
    //     0xc11994: ldur            x2, [x1, #7]
    // 0xc11998: StoreField: r0->field_f = r2
    //     0xc11998: stur            x2, [x0, #0xf]
    // 0xc1199c: LeaveFrame
    //     0xc1199c: mov             SP, fp
    //     0xc119a0: ldp             fp, lr, [SP], #0x10
    // 0xc119a4: ret
    //     0xc119a4: ret             
    // 0xc119a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc119a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc119ac: b               #0xc11928
    // 0xc119b0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc119b0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc119b4, size: 0x1c4
    // 0xc119b4: EnterFrame
    //     0xc119b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc119b8: mov             fp, SP
    // 0xc119bc: AllocStack(0x48)
    //     0xc119bc: sub             SP, SP, #0x48
    // 0xc119c0: SetupParameters()
    //     0xc119c0: ldr             x0, [fp, #0x10]
    //     0xc119c4: ldur            w1, [x0, #0x17]
    //     0xc119c8: add             x1, x1, HEAP, lsl #32
    //     0xc119cc: stur            x1, [fp, #-0x28]
    // 0xc119d0: CheckStackOverflow
    //     0xc119d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc119d4: cmp             SP, x16
    //     0xc119d8: b.ls            #0xc11b68
    // 0xc119dc: LoadField: r0 = r1->field_f
    //     0xc119dc: ldur            w0, [x1, #0xf]
    // 0xc119e0: DecompressPointer r0
    //     0xc119e0: add             x0, x0, HEAP, lsl #32
    // 0xc119e4: LoadField: r2 = r0->field_b
    //     0xc119e4: ldur            w2, [x0, #0xb]
    // 0xc119e8: DecompressPointer r2
    //     0xc119e8: add             x2, x2, HEAP, lsl #32
    // 0xc119ec: stur            x2, [fp, #-0x20]
    // 0xc119f0: cmp             w2, NULL
    // 0xc119f4: b.eq            #0xc11b70
    // 0xc119f8: LoadField: r0 = r2->field_1b
    //     0xc119f8: ldur            w0, [x2, #0x1b]
    // 0xc119fc: DecompressPointer r0
    //     0xc119fc: add             x0, x0, HEAP, lsl #32
    // 0xc11a00: stur            x0, [fp, #-0x18]
    // 0xc11a04: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xc11a04: ldur            w3, [x2, #0x17]
    // 0xc11a08: DecompressPointer r3
    //     0xc11a08: add             x3, x3, HEAP, lsl #32
    // 0xc11a0c: stur            x3, [fp, #-0x10]
    // 0xc11a10: LoadField: r4 = r2->field_f
    //     0xc11a10: ldur            w4, [x2, #0xf]
    // 0xc11a14: DecompressPointer r4
    //     0xc11a14: add             x4, x4, HEAP, lsl #32
    // 0xc11a18: stur            x4, [fp, #-8]
    // 0xc11a1c: r0 = EventData()
    //     0xc11a1c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xc11a20: mov             x1, x0
    // 0xc11a24: r0 = "product_page"
    //     0xc11a24: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xc11a28: ldr             x0, [x0, #0x480]
    // 0xc11a2c: stur            x1, [fp, #-0x30]
    // 0xc11a30: StoreField: r1->field_13 = r0
    //     0xc11a30: stur            w0, [x1, #0x13]
    // 0xc11a34: ldur            x2, [fp, #-0x10]
    // 0xc11a38: StoreField: r1->field_53 = r2
    //     0xc11a38: stur            w2, [x1, #0x53]
    // 0xc11a3c: ldur            x2, [fp, #-0x18]
    // 0xc11a40: StoreField: r1->field_57 = r2
    //     0xc11a40: stur            w2, [x1, #0x57]
    // 0xc11a44: r2 = "view_all"
    //     0xc11a44: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xc11a48: ldr             x2, [x2, #0xba0]
    // 0xc11a4c: StoreField: r1->field_eb = r2
    //     0xc11a4c: stur            w2, [x1, #0xeb]
    // 0xc11a50: ldur            x2, [fp, #-8]
    // 0xc11a54: StoreField: r1->field_ef = r2
    //     0xc11a54: stur            w2, [x1, #0xef]
    // 0xc11a58: StoreField: r1->field_f3 = r0
    //     0xc11a58: stur            w0, [x1, #0xf3]
    // 0xc11a5c: r0 = EventsRequest()
    //     0xc11a5c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xc11a60: mov             x1, x0
    // 0xc11a64: r0 = "cta_clicked"
    //     0xc11a64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xc11a68: ldr             x0, [x0, #0xdf8]
    // 0xc11a6c: StoreField: r1->field_7 = r0
    //     0xc11a6c: stur            w0, [x1, #7]
    // 0xc11a70: ldur            x0, [fp, #-0x30]
    // 0xc11a74: StoreField: r1->field_b = r0
    //     0xc11a74: stur            w0, [x1, #0xb]
    // 0xc11a78: ldur            x0, [fp, #-0x20]
    // 0xc11a7c: LoadField: r2 = r0->field_27
    //     0xc11a7c: ldur            w2, [x0, #0x27]
    // 0xc11a80: DecompressPointer r2
    //     0xc11a80: add             x2, x2, HEAP, lsl #32
    // 0xc11a84: stp             x1, x2, [SP]
    // 0xc11a88: r4 = 0
    //     0xc11a88: movz            x4, #0
    // 0xc11a8c: ldr             x0, [SP, #8]
    // 0xc11a90: r16 = UnlinkedCall_0x613b5c
    //     0xc11a90: add             x16, PP, #0x52, lsl #12  ; [pp+0x52350] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc11a94: add             x16, x16, #0x350
    // 0xc11a98: ldp             x5, lr, [x16]
    // 0xc11a9c: blr             lr
    // 0xc11aa0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc11aa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc11aa4: ldr             x0, [x0, #0x1c80]
    //     0xc11aa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc11aac: cmp             w0, w16
    //     0xc11ab0: b.ne            #0xc11abc
    //     0xc11ab4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc11ab8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc11abc: r1 = Null
    //     0xc11abc: mov             x1, NULL
    // 0xc11ac0: r2 = 12
    //     0xc11ac0: movz            x2, #0xc
    // 0xc11ac4: r0 = AllocateArray()
    //     0xc11ac4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc11ac8: r16 = "previousScreenSource"
    //     0xc11ac8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xc11acc: ldr             x16, [x16, #0x448]
    // 0xc11ad0: StoreField: r0->field_f = r16
    //     0xc11ad0: stur            w16, [x0, #0xf]
    // 0xc11ad4: ldur            x1, [fp, #-0x28]
    // 0xc11ad8: LoadField: r2 = r1->field_f
    //     0xc11ad8: ldur            w2, [x1, #0xf]
    // 0xc11adc: DecompressPointer r2
    //     0xc11adc: add             x2, x2, HEAP, lsl #32
    // 0xc11ae0: LoadField: r1 = r2->field_b
    //     0xc11ae0: ldur            w1, [x2, #0xb]
    // 0xc11ae4: DecompressPointer r1
    //     0xc11ae4: add             x1, x1, HEAP, lsl #32
    // 0xc11ae8: cmp             w1, NULL
    // 0xc11aec: b.eq            #0xc11b74
    // 0xc11af0: LoadField: r2 = r1->field_23
    //     0xc11af0: ldur            w2, [x1, #0x23]
    // 0xc11af4: DecompressPointer r2
    //     0xc11af4: add             x2, x2, HEAP, lsl #32
    // 0xc11af8: StoreField: r0->field_13 = r2
    //     0xc11af8: stur            w2, [x0, #0x13]
    // 0xc11afc: r16 = "screenSource"
    //     0xc11afc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xc11b00: ldr             x16, [x16, #0x450]
    // 0xc11b04: ArrayStore: r0[0] = r16  ; List_4
    //     0xc11b04: stur            w16, [x0, #0x17]
    // 0xc11b08: LoadField: r2 = r1->field_1f
    //     0xc11b08: ldur            w2, [x1, #0x1f]
    // 0xc11b0c: DecompressPointer r2
    //     0xc11b0c: add             x2, x2, HEAP, lsl #32
    // 0xc11b10: StoreField: r0->field_1b = r2
    //     0xc11b10: stur            w2, [x0, #0x1b]
    // 0xc11b14: r16 = "widgetType"
    //     0xc11b14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xc11b18: ldr             x16, [x16, #0x338]
    // 0xc11b1c: StoreField: r0->field_1f = r16
    //     0xc11b1c: stur            w16, [x0, #0x1f]
    // 0xc11b20: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc11b20: ldur            w2, [x1, #0x17]
    // 0xc11b24: DecompressPointer r2
    //     0xc11b24: add             x2, x2, HEAP, lsl #32
    // 0xc11b28: StoreField: r0->field_23 = r2
    //     0xc11b28: stur            w2, [x0, #0x23]
    // 0xc11b2c: r16 = <String, String?>
    //     0xc11b2c: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xc11b30: ldr             x16, [x16, #0x3c8]
    // 0xc11b34: stp             x0, x16, [SP]
    // 0xc11b38: r0 = Map._fromLiteral()
    //     0xc11b38: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc11b3c: r16 = "/testimonials"
    //     0xc11b3c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xc11b40: ldr             x16, [x16, #0x898]
    // 0xc11b44: stp             x16, NULL, [SP, #8]
    // 0xc11b48: str             x0, [SP]
    // 0xc11b4c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xc11b4c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xc11b50: ldr             x4, [x4, #0x438]
    // 0xc11b54: r0 = GetNavigation.toNamed()
    //     0xc11b54: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xc11b58: r0 = Null
    //     0xc11b58: mov             x0, NULL
    // 0xc11b5c: LeaveFrame
    //     0xc11b5c: mov             SP, fp
    //     0xc11b60: ldp             fp, lr, [SP], #0x10
    // 0xc11b64: ret
    //     0xc11b64: ret             
    // 0xc11b68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc11b68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc11b6c: b               #0xc119dc
    // 0xc11b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc11b70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc11b74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc11b74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _TestimonialCarouselState(/* No info */) {
    // ** addr: 0xc813d4, size: 0x70
    // 0xc813d4: EnterFrame
    //     0xc813d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc813d8: mov             fp, SP
    // 0xc813dc: AllocStack(0x8)
    //     0xc813dc: sub             SP, SP, #8
    // 0xc813e0: r0 = Sentinel
    //     0xc813e0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc813e4: CheckStackOverflow
    //     0xc813e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc813e8: cmp             SP, x16
    //     0xc813ec: b.ls            #0xc8143c
    // 0xc813f0: StoreField: r1->field_13 = r0
    //     0xc813f0: stur            w0, [x1, #0x13]
    // 0xc813f4: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc813f4: stur            xzr, [x1, #0x17]
    // 0xc813f8: r1 = <bool>
    //     0xc813f8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xc813fc: r0 = RxBool()
    //     0xc813fc: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xc81400: mov             x2, x0
    // 0xc81404: r0 = Sentinel
    //     0xc81404: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc81408: stur            x2, [fp, #-8]
    // 0xc8140c: StoreField: r2->field_13 = r0
    //     0xc8140c: stur            w0, [x2, #0x13]
    // 0xc81410: r0 = true
    //     0xc81410: add             x0, NULL, #0x20  ; true
    // 0xc81414: ArrayStore: r2[0] = r0  ; List_4
    //     0xc81414: stur            w0, [x2, #0x17]
    // 0xc81418: mov             x1, x2
    // 0xc8141c: r0 = RxNotifier()
    //     0xc8141c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xc81420: ldur            x1, [fp, #-8]
    // 0xc81424: r2 = false
    //     0xc81424: add             x2, NULL, #0x30  ; false
    // 0xc81428: StoreField: r1->field_13 = r2
    //     0xc81428: stur            w2, [x1, #0x13]
    // 0xc8142c: r0 = Null
    //     0xc8142c: mov             x0, NULL
    // 0xc81430: LeaveFrame
    //     0xc81430: mov             SP, fp
    //     0xc81434: ldp             fp, lr, [SP], #0x10
    // 0xc81438: ret
    //     0xc81438: ret             
    // 0xc8143c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8143c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81440: b               #0xc813f0
  }
}

// class id: 3959, size: 0x30, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8138c, size: 0x48
    // 0xc8138c: EnterFrame
    //     0xc8138c: stp             fp, lr, [SP, #-0x10]!
    //     0xc81390: mov             fp, SP
    // 0xc81394: AllocStack(0x8)
    //     0xc81394: sub             SP, SP, #8
    // 0xc81398: CheckStackOverflow
    //     0xc81398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8139c: cmp             SP, x16
    //     0xc813a0: b.ls            #0xc813cc
    // 0xc813a4: r1 = <TestimonialCarousel>
    //     0xc813a4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48268] TypeArguments: <TestimonialCarousel>
    //     0xc813a8: ldr             x1, [x1, #0x268]
    // 0xc813ac: r0 = _TestimonialCarouselState()
    //     0xc813ac: bl              #0xc81444  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x20)
    // 0xc813b0: mov             x1, x0
    // 0xc813b4: stur            x0, [fp, #-8]
    // 0xc813b8: r0 = _TestimonialCarouselState()
    //     0xc813b8: bl              #0xc813d4  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::_TestimonialCarouselState
    // 0xc813bc: ldur            x0, [fp, #-8]
    // 0xc813c0: LeaveFrame
    //     0xc813c0: mov             SP, fp
    //     0xc813c4: ldp             fp, lr, [SP], #0x10
    // 0xc813c8: ret
    //     0xc813c8: ret             
    // 0xc813cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc813cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc813d0: b               #0xc813a4
  }
}
