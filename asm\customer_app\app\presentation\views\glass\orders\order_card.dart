// lib: , url: package:customer_app/app/presentation/views/glass/orders/order_card.dart

// class id: 1049413, size: 0x8
class :: {
}

// class id: 3326, size: 0x1c, field offset: 0x14
class _OrderCardState extends State<dynamic> {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x804e48, size: 0x1f4
    // 0x804e48: EnterFrame
    //     0x804e48: stp             fp, lr, [SP, #-0x10]!
    //     0x804e4c: mov             fp, SP
    // 0x804e50: AllocStack(0x20)
    //     0x804e50: sub             SP, SP, #0x20
    // 0x804e54: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x804e54: mov             x4, x1
    //     0x804e58: mov             x3, x2
    //     0x804e5c: stur            x1, [fp, #-8]
    //     0x804e60: stur            x2, [fp, #-0x10]
    // 0x804e64: CheckStackOverflow
    //     0x804e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804e68: cmp             SP, x16
    //     0x804e6c: b.ls            #0x805030
    // 0x804e70: mov             x0, x3
    // 0x804e74: r2 = Null
    //     0x804e74: mov             x2, NULL
    // 0x804e78: r1 = Null
    //     0x804e78: mov             x1, NULL
    // 0x804e7c: r4 = 60
    //     0x804e7c: movz            x4, #0x3c
    // 0x804e80: branchIfSmi(r0, 0x804e8c)
    //     0x804e80: tbz             w0, #0, #0x804e8c
    // 0x804e84: r4 = LoadClassIdInstr(r0)
    //     0x804e84: ldur            x4, [x0, #-1]
    //     0x804e88: ubfx            x4, x4, #0xc, #0x14
    // 0x804e8c: cmp             x4, #0xfe4
    // 0x804e90: b.eq            #0x804ea8
    // 0x804e94: r8 = OrderCard
    //     0x804e94: add             x8, PP, #0x6a, lsl #12  ; [pp+0x6a8c0] Type: OrderCard
    //     0x804e98: ldr             x8, [x8, #0x8c0]
    // 0x804e9c: r3 = Null
    //     0x804e9c: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a8c8] Null
    //     0x804ea0: ldr             x3, [x3, #0x8c8]
    // 0x804ea4: r0 = OrderCard()
    //     0x804ea4: bl              #0x80503c  ; IsType_OrderCard_Stub
    // 0x804ea8: ldur            x3, [fp, #-8]
    // 0x804eac: LoadField: r2 = r3->field_7
    //     0x804eac: ldur            w2, [x3, #7]
    // 0x804eb0: DecompressPointer r2
    //     0x804eb0: add             x2, x2, HEAP, lsl #32
    // 0x804eb4: ldur            x0, [fp, #-0x10]
    // 0x804eb8: r1 = Null
    //     0x804eb8: mov             x1, NULL
    // 0x804ebc: cmp             w2, NULL
    // 0x804ec0: b.eq            #0x804ee4
    // 0x804ec4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x804ec4: ldur            w4, [x2, #0x17]
    // 0x804ec8: DecompressPointer r4
    //     0x804ec8: add             x4, x4, HEAP, lsl #32
    // 0x804ecc: r8 = X0 bound StatefulWidget
    //     0x804ecc: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x804ed0: ldr             x8, [x8, #0x7a0]
    // 0x804ed4: LoadField: r9 = r4->field_7
    //     0x804ed4: ldur            x9, [x4, #7]
    // 0x804ed8: r3 = Null
    //     0x804ed8: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a8d8] Null
    //     0x804edc: ldr             x3, [x3, #0x8d8]
    // 0x804ee0: blr             x9
    // 0x804ee4: ldur            x2, [fp, #-8]
    // 0x804ee8: LoadField: r0 = r2->field_b
    //     0x804ee8: ldur            w0, [x2, #0xb]
    // 0x804eec: DecompressPointer r0
    //     0x804eec: add             x0, x0, HEAP, lsl #32
    // 0x804ef0: cmp             w0, NULL
    // 0x804ef4: b.eq            #0x805038
    // 0x804ef8: LoadField: r1 = r0->field_b
    //     0x804ef8: ldur            w1, [x0, #0xb]
    // 0x804efc: DecompressPointer r1
    //     0x804efc: add             x1, x1, HEAP, lsl #32
    // 0x804f00: LoadField: r3 = r1->field_8b
    //     0x804f00: ldur            w3, [x1, #0x8b]
    // 0x804f04: DecompressPointer r3
    //     0x804f04: add             x3, x3, HEAP, lsl #32
    // 0x804f08: cmp             w3, NULL
    // 0x804f0c: b.ne            #0x804f18
    // 0x804f10: r4 = Null
    //     0x804f10: mov             x4, NULL
    // 0x804f14: b               #0x804f34
    // 0x804f18: LoadField: r4 = r3->field_f
    //     0x804f18: ldur            x4, [x3, #0xf]
    // 0x804f1c: r0 = BoxInt64Instr(r4)
    //     0x804f1c: sbfiz           x0, x4, #1, #0x1f
    //     0x804f20: cmp             x4, x0, asr #1
    //     0x804f24: b.eq            #0x804f30
    //     0x804f28: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x804f2c: stur            x4, [x0, #7]
    // 0x804f30: mov             x4, x0
    // 0x804f34: ldur            x0, [fp, #-0x10]
    // 0x804f38: LoadField: r1 = r0->field_b
    //     0x804f38: ldur            w1, [x0, #0xb]
    // 0x804f3c: DecompressPointer r1
    //     0x804f3c: add             x1, x1, HEAP, lsl #32
    // 0x804f40: LoadField: r0 = r1->field_8b
    //     0x804f40: ldur            w0, [x1, #0x8b]
    // 0x804f44: DecompressPointer r0
    //     0x804f44: add             x0, x0, HEAP, lsl #32
    // 0x804f48: cmp             w0, NULL
    // 0x804f4c: b.ne            #0x804f58
    // 0x804f50: r0 = Null
    //     0x804f50: mov             x0, NULL
    // 0x804f54: b               #0x804f70
    // 0x804f58: LoadField: r5 = r0->field_f
    //     0x804f58: ldur            x5, [x0, #0xf]
    // 0x804f5c: r0 = BoxInt64Instr(r5)
    //     0x804f5c: sbfiz           x0, x5, #1, #0x1f
    //     0x804f60: cmp             x5, x0, asr #1
    //     0x804f64: b.eq            #0x804f70
    //     0x804f68: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x804f6c: stur            x5, [x0, #7]
    // 0x804f70: cmp             w4, w0
    // 0x804f74: b.eq            #0x805020
    // 0x804f78: and             w16, w4, w0
    // 0x804f7c: branchIfSmi(r16, 0x804fb0)
    //     0x804f7c: tbz             w16, #0, #0x804fb0
    // 0x804f80: r16 = LoadClassIdInstr(r4)
    //     0x804f80: ldur            x16, [x4, #-1]
    //     0x804f84: ubfx            x16, x16, #0xc, #0x14
    // 0x804f88: cmp             x16, #0x3d
    // 0x804f8c: b.ne            #0x804fb0
    // 0x804f90: r16 = LoadClassIdInstr(r0)
    //     0x804f90: ldur            x16, [x0, #-1]
    //     0x804f94: ubfx            x16, x16, #0xc, #0x14
    // 0x804f98: cmp             x16, #0x3d
    // 0x804f9c: b.ne            #0x804fb0
    // 0x804fa0: LoadField: r16 = r4->field_7
    //     0x804fa0: ldur            x16, [x4, #7]
    // 0x804fa4: LoadField: r17 = r0->field_7
    //     0x804fa4: ldur            x17, [x0, #7]
    // 0x804fa8: cmp             x16, x17
    // 0x804fac: b.eq            #0x805020
    // 0x804fb0: cmp             w3, NULL
    // 0x804fb4: b.ne            #0x804fc0
    // 0x804fb8: r0 = Null
    //     0x804fb8: mov             x0, NULL
    // 0x804fbc: b               #0x804fd8
    // 0x804fc0: LoadField: r4 = r3->field_f
    //     0x804fc0: ldur            x4, [x3, #0xf]
    // 0x804fc4: r0 = BoxInt64Instr(r4)
    //     0x804fc4: sbfiz           x0, x4, #1, #0x1f
    //     0x804fc8: cmp             x4, x0, asr #1
    //     0x804fcc: b.eq            #0x804fd8
    //     0x804fd0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x804fd4: stur            x4, [x0, #7]
    // 0x804fd8: cmp             w0, NULL
    // 0x804fdc: b.ne            #0x804fe8
    // 0x804fe0: r3 = 0
    //     0x804fe0: movz            x3, #0
    // 0x804fe4: b               #0x804ff8
    // 0x804fe8: r1 = LoadInt32Instr(r0)
    //     0x804fe8: sbfx            x1, x0, #1, #0x1f
    //     0x804fec: tbz             w0, #0, #0x804ff4
    //     0x804ff0: ldur            x1, [x0, #7]
    // 0x804ff4: mov             x3, x1
    // 0x804ff8: r0 = BoxInt64Instr(r3)
    //     0x804ff8: sbfiz           x0, x3, #1, #0x1f
    //     0x804ffc: cmp             x3, x0, asr #1
    //     0x805000: b.eq            #0x80500c
    //     0x805004: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x805008: stur            x3, [x0, #7]
    // 0x80500c: stp             x0, NULL, [SP]
    // 0x805010: r0 = _Double.fromInteger()
    //     0x805010: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x805014: LoadField: d0 = r0->field_7
    //     0x805014: ldur            d0, [x0, #7]
    // 0x805018: ldur            x1, [fp, #-8]
    // 0x80501c: StoreField: r1->field_13 = d0
    //     0x80501c: stur            d0, [x1, #0x13]
    // 0x805020: r0 = Null
    //     0x805020: mov             x0, NULL
    // 0x805024: LeaveFrame
    //     0x805024: mov             SP, fp
    //     0x805028: ldp             fp, lr, [SP], #0x10
    // 0x80502c: ret
    //     0x80502c: ret             
    // 0x805030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x805030: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x805034: b               #0x804e70
    // 0x805038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x805038: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SvgPicture <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xafce60, size: 0x114
    // 0xafce60: EnterFrame
    //     0xafce60: stp             fp, lr, [SP, #-0x10]!
    //     0xafce64: mov             fp, SP
    // 0xafce68: AllocStack(0x18)
    //     0xafce68: sub             SP, SP, #0x18
    // 0xafce6c: SetupParameters()
    //     0xafce6c: fmov            d0, #1.00000000
    //     0xafce70: ldr             x0, [fp, #0x20]
    //     0xafce74: ldur            w1, [x0, #0x17]
    //     0xafce78: add             x1, x1, HEAP, lsl #32
    // 0xafce6c: d0 = 1.000000
    // 0xafce7c: CheckStackOverflow
    //     0xafce7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafce80: cmp             SP, x16
    //     0xafce84: b.ls            #0xafcf44
    // 0xafce88: LoadField: r0 = r1->field_f
    //     0xafce88: ldur            w0, [x1, #0xf]
    // 0xafce8c: DecompressPointer r0
    //     0xafce8c: add             x0, x0, HEAP, lsl #32
    // 0xafce90: LoadField: d1 = r0->field_13
    //     0xafce90: ldur            d1, [x0, #0x13]
    // 0xafce94: stur            d1, [fp, #-0x10]
    // 0xafce98: fsub            d2, d1, d0
    // 0xafce9c: ldr             x0, [fp, #0x10]
    // 0xafcea0: r1 = LoadInt32Instr(r0)
    //     0xafcea0: sbfx            x1, x0, #1, #0x1f
    //     0xafcea4: tbz             w0, #0, #0xafceac
    //     0xafcea8: ldur            x1, [x0, #7]
    // 0xafceac: scvtf           d0, x1
    // 0xafceb0: fcmp            d0, d2
    // 0xafceb4: b.ne            #0xafcf10
    // 0xafceb8: r1 = Null
    //     0xafceb8: mov             x1, NULL
    // 0xafcebc: r2 = 6
    //     0xafcebc: movz            x2, #0x6
    // 0xafcec0: r0 = AllocateArray()
    //     0xafcec0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafcec4: r16 = "assets/images/star"
    //     0xafcec4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb0] "assets/images/star"
    //     0xafcec8: ldr             x16, [x16, #0xbb0]
    // 0xafcecc: StoreField: r0->field_f = r16
    //     0xafcecc: stur            w16, [x0, #0xf]
    // 0xafced0: ldur            d0, [fp, #-0x10]
    // 0xafced4: fcmp            d0, d0
    // 0xafced8: b.vs            #0xafcf4c
    // 0xafcedc: fcvtzs          x1, d0
    // 0xafcee0: asr             x16, x1, #0x1e
    // 0xafcee4: cmp             x16, x1, asr #63
    // 0xafcee8: b.ne            #0xafcf4c
    // 0xafceec: lsl             x1, x1, #1
    // 0xafcef0: StoreField: r0->field_13 = r1
    //     0xafcef0: stur            w1, [x0, #0x13]
    // 0xafcef4: r16 = ".svg"
    //     0xafcef4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36bb8] ".svg"
    //     0xafcef8: ldr             x16, [x16, #0xbb8]
    // 0xafcefc: ArrayStore: r0[0] = r16  ; List_4
    //     0xafcefc: stur            w16, [x0, #0x17]
    // 0xafcf00: str             x0, [SP]
    // 0xafcf04: r0 = _interpolate()
    //     0xafcf04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xafcf08: mov             x2, x0
    // 0xafcf0c: b               #0xafcf18
    // 0xafcf10: r2 = "assets/images/ratedStar.svg"
    //     0xafcf10: add             x2, PP, #0x36, lsl #12  ; [pp+0x36bc0] "assets/images/ratedStar.svg"
    //     0xafcf14: ldr             x2, [x2, #0xbc0]
    // 0xafcf18: stur            x2, [fp, #-8]
    // 0xafcf1c: r0 = SvgPicture()
    //     0xafcf1c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xafcf20: mov             x1, x0
    // 0xafcf24: ldur            x2, [fp, #-8]
    // 0xafcf28: stur            x0, [fp, #-8]
    // 0xafcf2c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xafcf2c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xafcf30: r0 = SvgPicture.asset()
    //     0xafcf30: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xafcf34: ldur            x0, [fp, #-8]
    // 0xafcf38: LeaveFrame
    //     0xafcf38: mov             SP, fp
    //     0xafcf3c: ldp             fp, lr, [SP], #0x10
    // 0xafcf40: ret
    //     0xafcf40: ret             
    // 0xafcf44: r0 = StackOverflowSharedWithFPURegs()
    //     0xafcf44: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0xafcf48: b               #0xafce88
    // 0xafcf4c: SaveReg d0
    //     0xafcf4c: str             q0, [SP, #-0x10]!
    // 0xafcf50: SaveReg r0
    //     0xafcf50: str             x0, [SP, #-8]!
    // 0xafcf54: r0 = 74
    //     0xafcf54: movz            x0, #0x4a
    // 0xafcf58: r30 = DoubleToIntegerStub
    //     0xafcf58: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xafcf5c: LoadField: r30 = r30->field_7
    //     0xafcf5c: ldur            lr, [lr, #7]
    // 0xafcf60: blr             lr
    // 0xafcf64: mov             x1, x0
    // 0xafcf68: RestoreReg r0
    //     0xafcf68: ldr             x0, [SP], #8
    // 0xafcf6c: RestoreReg d0
    //     0xafcf6c: ldr             q0, [SP], #0x10
    // 0xafcf70: b               #0xafcef0
  }
  _ build(/* No info */) {
    // ** addr: 0xb753b0, size: 0x18c4
    // 0xb753b0: EnterFrame
    //     0xb753b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb753b4: mov             fp, SP
    // 0xb753b8: AllocStack(0x88)
    //     0xb753b8: sub             SP, SP, #0x88
    // 0xb753bc: SetupParameters(_OrderCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb753bc: mov             x0, x1
    //     0xb753c0: stur            x1, [fp, #-8]
    //     0xb753c4: mov             x1, x2
    //     0xb753c8: stur            x2, [fp, #-0x10]
    // 0xb753cc: CheckStackOverflow
    //     0xb753cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb753d0: cmp             SP, x16
    //     0xb753d4: b.ls            #0xb76c0c
    // 0xb753d8: r1 = 1
    //     0xb753d8: movz            x1, #0x1
    // 0xb753dc: r0 = AllocateContext()
    //     0xb753dc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb753e0: mov             x1, x0
    // 0xb753e4: ldur            x0, [fp, #-8]
    // 0xb753e8: stur            x1, [fp, #-0x18]
    // 0xb753ec: StoreField: r1->field_f = r0
    //     0xb753ec: stur            w0, [x1, #0xf]
    // 0xb753f0: r0 = Radius()
    //     0xb753f0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb753f4: d0 = 15.000000
    //     0xb753f4: fmov            d0, #15.00000000
    // 0xb753f8: stur            x0, [fp, #-0x20]
    // 0xb753fc: StoreField: r0->field_7 = d0
    //     0xb753fc: stur            d0, [x0, #7]
    // 0xb75400: StoreField: r0->field_f = d0
    //     0xb75400: stur            d0, [x0, #0xf]
    // 0xb75404: r0 = BorderRadius()
    //     0xb75404: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb75408: mov             x1, x0
    // 0xb7540c: ldur            x0, [fp, #-0x20]
    // 0xb75410: stur            x1, [fp, #-0x28]
    // 0xb75414: StoreField: r1->field_7 = r0
    //     0xb75414: stur            w0, [x1, #7]
    // 0xb75418: StoreField: r1->field_b = r0
    //     0xb75418: stur            w0, [x1, #0xb]
    // 0xb7541c: StoreField: r1->field_f = r0
    //     0xb7541c: stur            w0, [x1, #0xf]
    // 0xb75420: StoreField: r1->field_13 = r0
    //     0xb75420: stur            w0, [x1, #0x13]
    // 0xb75424: r0 = RoundedRectangleBorder()
    //     0xb75424: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb75428: mov             x3, x0
    // 0xb7542c: ldur            x0, [fp, #-0x28]
    // 0xb75430: stur            x3, [fp, #-0x30]
    // 0xb75434: StoreField: r3->field_b = r0
    //     0xb75434: stur            w0, [x3, #0xb]
    // 0xb75438: r0 = Instance_BorderSide
    //     0xb75438: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb7543c: ldr             x0, [x0, #0xe20]
    // 0xb75440: StoreField: r3->field_7 = r0
    //     0xb75440: stur            w0, [x3, #7]
    // 0xb75444: ldur            x0, [fp, #-8]
    // 0xb75448: LoadField: r1 = r0->field_b
    //     0xb75448: ldur            w1, [x0, #0xb]
    // 0xb7544c: DecompressPointer r1
    //     0xb7544c: add             x1, x1, HEAP, lsl #32
    // 0xb75450: cmp             w1, NULL
    // 0xb75454: b.eq            #0xb76c14
    // 0xb75458: LoadField: r2 = r1->field_b
    //     0xb75458: ldur            w2, [x1, #0xb]
    // 0xb7545c: DecompressPointer r2
    //     0xb7545c: add             x2, x2, HEAP, lsl #32
    // 0xb75460: LoadField: r1 = r2->field_b
    //     0xb75460: ldur            w1, [x2, #0xb]
    // 0xb75464: DecompressPointer r1
    //     0xb75464: add             x1, x1, HEAP, lsl #32
    // 0xb75468: cmp             w1, NULL
    // 0xb7546c: b.ne            #0xb75478
    // 0xb75470: r4 = ""
    //     0xb75470: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb75474: b               #0xb7547c
    // 0xb75478: mov             x4, x1
    // 0xb7547c: stur            x4, [fp, #-0x20]
    // 0xb75480: r1 = Function '<anonymous closure>':.
    //     0xb75480: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7c8] AnonymousClosure: (0xb78040), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb75484: ldr             x1, [x1, #0x7c8]
    // 0xb75488: r2 = Null
    //     0xb75488: mov             x2, NULL
    // 0xb7548c: r0 = AllocateClosure()
    //     0xb7548c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb75490: r1 = Function '<anonymous closure>':.
    //     0xb75490: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7d0] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb75494: ldr             x1, [x1, #0x7d0]
    // 0xb75498: r2 = Null
    //     0xb75498: mov             x2, NULL
    // 0xb7549c: stur            x0, [fp, #-0x28]
    // 0xb754a0: r0 = AllocateClosure()
    //     0xb754a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb754a4: r1 = Function '<anonymous closure>':.
    //     0xb754a4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7d8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb754a8: ldr             x1, [x1, #0x7d8]
    // 0xb754ac: r2 = Null
    //     0xb754ac: mov             x2, NULL
    // 0xb754b0: stur            x0, [fp, #-0x38]
    // 0xb754b4: r0 = AllocateClosure()
    //     0xb754b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb754b8: stur            x0, [fp, #-0x40]
    // 0xb754bc: r0 = CachedNetworkImage()
    //     0xb754bc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb754c0: stur            x0, [fp, #-0x48]
    // 0xb754c4: ldur            x16, [fp, #-0x28]
    // 0xb754c8: ldur            lr, [fp, #-0x38]
    // 0xb754cc: stp             lr, x16, [SP, #8]
    // 0xb754d0: ldur            x16, [fp, #-0x40]
    // 0xb754d4: str             x16, [SP]
    // 0xb754d8: mov             x1, x0
    // 0xb754dc: ldur            x2, [fp, #-0x20]
    // 0xb754e0: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, imageBuilder, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb754e0: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f428] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "imageBuilder", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb754e4: ldr             x4, [x4, #0x428]
    // 0xb754e8: r0 = CachedNetworkImage()
    //     0xb754e8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb754ec: ldur            x0, [fp, #-8]
    // 0xb754f0: LoadField: r1 = r0->field_b
    //     0xb754f0: ldur            w1, [x0, #0xb]
    // 0xb754f4: DecompressPointer r1
    //     0xb754f4: add             x1, x1, HEAP, lsl #32
    // 0xb754f8: cmp             w1, NULL
    // 0xb754fc: b.eq            #0xb76c18
    // 0xb75500: LoadField: r2 = r1->field_b
    //     0xb75500: ldur            w2, [x1, #0xb]
    // 0xb75504: DecompressPointer r2
    //     0xb75504: add             x2, x2, HEAP, lsl #32
    // 0xb75508: LoadField: r1 = r2->field_5b
    //     0xb75508: ldur            w1, [x2, #0x5b]
    // 0xb7550c: DecompressPointer r1
    //     0xb7550c: add             x1, x1, HEAP, lsl #32
    // 0xb75510: cmp             w1, NULL
    // 0xb75514: b.ne            #0xb75520
    // 0xb75518: r2 = false
    //     0xb75518: add             x2, NULL, #0x30  ; false
    // 0xb7551c: b               #0xb75524
    // 0xb75520: mov             x2, x1
    // 0xb75524: ldur            x1, [fp, #-0x10]
    // 0xb75528: stur            x2, [fp, #-0x20]
    // 0xb7552c: r0 = of()
    //     0xb7552c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75530: LoadField: r1 = r0->field_5b
    //     0xb75530: ldur            w1, [x0, #0x5b]
    // 0xb75534: DecompressPointer r1
    //     0xb75534: add             x1, x1, HEAP, lsl #32
    // 0xb75538: stur            x1, [fp, #-0x28]
    // 0xb7553c: r0 = Radius()
    //     0xb7553c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb75540: d0 = 20.000000
    //     0xb75540: fmov            d0, #20.00000000
    // 0xb75544: stur            x0, [fp, #-0x38]
    // 0xb75548: StoreField: r0->field_7 = d0
    //     0xb75548: stur            d0, [x0, #7]
    // 0xb7554c: StoreField: r0->field_f = d0
    //     0xb7554c: stur            d0, [x0, #0xf]
    // 0xb75550: r0 = BorderRadius()
    //     0xb75550: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb75554: mov             x1, x0
    // 0xb75558: ldur            x0, [fp, #-0x38]
    // 0xb7555c: stur            x1, [fp, #-0x40]
    // 0xb75560: StoreField: r1->field_7 = r0
    //     0xb75560: stur            w0, [x1, #7]
    // 0xb75564: StoreField: r1->field_b = r0
    //     0xb75564: stur            w0, [x1, #0xb]
    // 0xb75568: StoreField: r1->field_f = r0
    //     0xb75568: stur            w0, [x1, #0xf]
    // 0xb7556c: StoreField: r1->field_13 = r0
    //     0xb7556c: stur            w0, [x1, #0x13]
    // 0xb75570: r0 = BoxDecoration()
    //     0xb75570: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb75574: mov             x2, x0
    // 0xb75578: ldur            x0, [fp, #-0x28]
    // 0xb7557c: stur            x2, [fp, #-0x38]
    // 0xb75580: StoreField: r2->field_7 = r0
    //     0xb75580: stur            w0, [x2, #7]
    // 0xb75584: ldur            x0, [fp, #-0x40]
    // 0xb75588: StoreField: r2->field_13 = r0
    //     0xb75588: stur            w0, [x2, #0x13]
    // 0xb7558c: r0 = Instance_BoxShape
    //     0xb7558c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb75590: ldr             x0, [x0, #0x80]
    // 0xb75594: StoreField: r2->field_23 = r0
    //     0xb75594: stur            w0, [x2, #0x23]
    // 0xb75598: ldur            x1, [fp, #-0x10]
    // 0xb7559c: r0 = of()
    //     0xb7559c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb755a0: LoadField: r1 = r0->field_87
    //     0xb755a0: ldur            w1, [x0, #0x87]
    // 0xb755a4: DecompressPointer r1
    //     0xb755a4: add             x1, x1, HEAP, lsl #32
    // 0xb755a8: LoadField: r0 = r1->field_7
    //     0xb755a8: ldur            w0, [x1, #7]
    // 0xb755ac: DecompressPointer r0
    //     0xb755ac: add             x0, x0, HEAP, lsl #32
    // 0xb755b0: r16 = 12.000000
    //     0xb755b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb755b4: ldr             x16, [x16, #0x9e8]
    // 0xb755b8: r30 = Instance_Color
    //     0xb755b8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb755bc: stp             lr, x16, [SP]
    // 0xb755c0: mov             x1, x0
    // 0xb755c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb755c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb755c8: ldr             x4, [x4, #0xaa0]
    // 0xb755cc: r0 = copyWith()
    //     0xb755cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb755d0: stur            x0, [fp, #-0x28]
    // 0xb755d4: r0 = Text()
    //     0xb755d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb755d8: mov             x1, x0
    // 0xb755dc: r0 = "Exchange"
    //     0xb755dc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ee8] "Exchange"
    //     0xb755e0: ldr             x0, [x0, #0xee8]
    // 0xb755e4: stur            x1, [fp, #-0x40]
    // 0xb755e8: StoreField: r1->field_b = r0
    //     0xb755e8: stur            w0, [x1, #0xb]
    // 0xb755ec: ldur            x0, [fp, #-0x28]
    // 0xb755f0: StoreField: r1->field_13 = r0
    //     0xb755f0: stur            w0, [x1, #0x13]
    // 0xb755f4: r0 = Container()
    //     0xb755f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb755f8: stur            x0, [fp, #-0x28]
    // 0xb755fc: r16 = Instance_EdgeInsets
    //     0xb755fc: add             x16, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xb75600: ldr             x16, [x16, #0xdb0]
    // 0xb75604: ldur            lr, [fp, #-0x38]
    // 0xb75608: stp             lr, x16, [SP, #8]
    // 0xb7560c: ldur            x16, [fp, #-0x40]
    // 0xb75610: str             x16, [SP]
    // 0xb75614: mov             x1, x0
    // 0xb75618: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb75618: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb7561c: ldr             x4, [x4, #0x610]
    // 0xb75620: r0 = Container()
    //     0xb75620: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb75624: r0 = Visibility()
    //     0xb75624: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb75628: mov             x2, x0
    // 0xb7562c: ldur            x0, [fp, #-0x28]
    // 0xb75630: stur            x2, [fp, #-0x38]
    // 0xb75634: StoreField: r2->field_b = r0
    //     0xb75634: stur            w0, [x2, #0xb]
    // 0xb75638: r0 = Instance_SizedBox
    //     0xb75638: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7563c: StoreField: r2->field_f = r0
    //     0xb7563c: stur            w0, [x2, #0xf]
    // 0xb75640: ldur            x1, [fp, #-0x20]
    // 0xb75644: StoreField: r2->field_13 = r1
    //     0xb75644: stur            w1, [x2, #0x13]
    // 0xb75648: r3 = false
    //     0xb75648: add             x3, NULL, #0x30  ; false
    // 0xb7564c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb7564c: stur            w3, [x2, #0x17]
    // 0xb75650: StoreField: r2->field_1b = r3
    //     0xb75650: stur            w3, [x2, #0x1b]
    // 0xb75654: StoreField: r2->field_1f = r3
    //     0xb75654: stur            w3, [x2, #0x1f]
    // 0xb75658: StoreField: r2->field_23 = r3
    //     0xb75658: stur            w3, [x2, #0x23]
    // 0xb7565c: StoreField: r2->field_27 = r3
    //     0xb7565c: stur            w3, [x2, #0x27]
    // 0xb75660: StoreField: r2->field_2b = r3
    //     0xb75660: stur            w3, [x2, #0x2b]
    // 0xb75664: ldur            x4, [fp, #-8]
    // 0xb75668: LoadField: r1 = r4->field_b
    //     0xb75668: ldur            w1, [x4, #0xb]
    // 0xb7566c: DecompressPointer r1
    //     0xb7566c: add             x1, x1, HEAP, lsl #32
    // 0xb75670: cmp             w1, NULL
    // 0xb75674: b.eq            #0xb76c1c
    // 0xb75678: LoadField: r5 = r1->field_b
    //     0xb75678: ldur            w5, [x1, #0xb]
    // 0xb7567c: DecompressPointer r5
    //     0xb7567c: add             x5, x5, HEAP, lsl #32
    // 0xb75680: LoadField: r1 = r5->field_f
    //     0xb75680: ldur            w1, [x5, #0xf]
    // 0xb75684: DecompressPointer r1
    //     0xb75684: add             x1, x1, HEAP, lsl #32
    // 0xb75688: cmp             w1, NULL
    // 0xb7568c: b.ne            #0xb75694
    // 0xb75690: r1 = ""
    //     0xb75690: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb75694: r0 = capitalizeFirstWord()
    //     0xb75694: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb75698: ldur            x1, [fp, #-0x10]
    // 0xb7569c: stur            x0, [fp, #-0x20]
    // 0xb756a0: r0 = of()
    //     0xb756a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb756a4: LoadField: r1 = r0->field_87
    //     0xb756a4: ldur            w1, [x0, #0x87]
    // 0xb756a8: DecompressPointer r1
    //     0xb756a8: add             x1, x1, HEAP, lsl #32
    // 0xb756ac: LoadField: r0 = r1->field_2b
    //     0xb756ac: ldur            w0, [x1, #0x2b]
    // 0xb756b0: DecompressPointer r0
    //     0xb756b0: add             x0, x0, HEAP, lsl #32
    // 0xb756b4: stur            x0, [fp, #-0x28]
    // 0xb756b8: r1 = Instance_Color
    //     0xb756b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb756bc: d0 = 0.700000
    //     0xb756bc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb756c0: ldr             d0, [x17, #0xf48]
    // 0xb756c4: r0 = withOpacity()
    //     0xb756c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb756c8: r16 = 12.000000
    //     0xb756c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb756cc: ldr             x16, [x16, #0x9e8]
    // 0xb756d0: stp             x16, x0, [SP]
    // 0xb756d4: ldur            x1, [fp, #-0x28]
    // 0xb756d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb756d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb756dc: ldr             x4, [x4, #0x9b8]
    // 0xb756e0: r0 = copyWith()
    //     0xb756e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb756e4: stur            x0, [fp, #-0x28]
    // 0xb756e8: r0 = Text()
    //     0xb756e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb756ec: mov             x1, x0
    // 0xb756f0: ldur            x0, [fp, #-0x20]
    // 0xb756f4: stur            x1, [fp, #-0x40]
    // 0xb756f8: StoreField: r1->field_b = r0
    //     0xb756f8: stur            w0, [x1, #0xb]
    // 0xb756fc: ldur            x0, [fp, #-0x28]
    // 0xb75700: StoreField: r1->field_13 = r0
    //     0xb75700: stur            w0, [x1, #0x13]
    // 0xb75704: r0 = Instance_TextOverflow
    //     0xb75704: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb75708: ldr             x0, [x0, #0xe10]
    // 0xb7570c: StoreField: r1->field_2b = r0
    //     0xb7570c: stur            w0, [x1, #0x2b]
    // 0xb75710: r2 = 4
    //     0xb75710: movz            x2, #0x4
    // 0xb75714: StoreField: r1->field_37 = r2
    //     0xb75714: stur            w2, [x1, #0x37]
    // 0xb75718: ldur            x3, [fp, #-8]
    // 0xb7571c: LoadField: r0 = r3->field_b
    //     0xb7571c: ldur            w0, [x3, #0xb]
    // 0xb75720: DecompressPointer r0
    //     0xb75720: add             x0, x0, HEAP, lsl #32
    // 0xb75724: cmp             w0, NULL
    // 0xb75728: b.eq            #0xb76c20
    // 0xb7572c: LoadField: r4 = r0->field_b
    //     0xb7572c: ldur            w4, [x0, #0xb]
    // 0xb75730: DecompressPointer r4
    //     0xb75730: add             x4, x4, HEAP, lsl #32
    // 0xb75734: LoadField: r0 = r4->field_47
    //     0xb75734: ldur            w0, [x4, #0x47]
    // 0xb75738: DecompressPointer r0
    //     0xb75738: add             x0, x0, HEAP, lsl #32
    // 0xb7573c: r4 = LoadClassIdInstr(r0)
    //     0xb7573c: ldur            x4, [x0, #-1]
    //     0xb75740: ubfx            x4, x4, #0xc, #0x14
    // 0xb75744: r16 = "size"
    //     0xb75744: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb75748: ldr             x16, [x16, #0x9c0]
    // 0xb7574c: stp             x16, x0, [SP]
    // 0xb75750: mov             x0, x4
    // 0xb75754: mov             lr, x0
    // 0xb75758: ldr             lr, [x21, lr, lsl #3]
    // 0xb7575c: blr             lr
    // 0xb75760: tbnz            w0, #4, #0xb757b8
    // 0xb75764: ldur            x0, [fp, #-8]
    // 0xb75768: r1 = Null
    //     0xb75768: mov             x1, NULL
    // 0xb7576c: r2 = 4
    //     0xb7576c: movz            x2, #0x4
    // 0xb75770: r0 = AllocateArray()
    //     0xb75770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75774: r16 = "Size : "
    //     0xb75774: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a7e0] "Size : "
    //     0xb75778: ldr             x16, [x16, #0x7e0]
    // 0xb7577c: StoreField: r0->field_f = r16
    //     0xb7577c: stur            w16, [x0, #0xf]
    // 0xb75780: ldur            x1, [fp, #-8]
    // 0xb75784: LoadField: r2 = r1->field_b
    //     0xb75784: ldur            w2, [x1, #0xb]
    // 0xb75788: DecompressPointer r2
    //     0xb75788: add             x2, x2, HEAP, lsl #32
    // 0xb7578c: cmp             w2, NULL
    // 0xb75790: b.eq            #0xb76c24
    // 0xb75794: LoadField: r3 = r2->field_b
    //     0xb75794: ldur            w3, [x2, #0xb]
    // 0xb75798: DecompressPointer r3
    //     0xb75798: add             x3, x3, HEAP, lsl #32
    // 0xb7579c: LoadField: r2 = r3->field_1f
    //     0xb7579c: ldur            w2, [x3, #0x1f]
    // 0xb757a0: DecompressPointer r2
    //     0xb757a0: add             x2, x2, HEAP, lsl #32
    // 0xb757a4: StoreField: r0->field_13 = r2
    //     0xb757a4: stur            w2, [x0, #0x13]
    // 0xb757a8: str             x0, [SP]
    // 0xb757ac: r0 = _interpolate()
    //     0xb757ac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb757b0: mov             x2, x0
    // 0xb757b4: b               #0xb75808
    // 0xb757b8: ldur            x0, [fp, #-8]
    // 0xb757bc: r1 = Null
    //     0xb757bc: mov             x1, NULL
    // 0xb757c0: r2 = 4
    //     0xb757c0: movz            x2, #0x4
    // 0xb757c4: r0 = AllocateArray()
    //     0xb757c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb757c8: r16 = "Variant : "
    //     0xb757c8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xb757cc: ldr             x16, [x16, #0x768]
    // 0xb757d0: StoreField: r0->field_f = r16
    //     0xb757d0: stur            w16, [x0, #0xf]
    // 0xb757d4: ldur            x1, [fp, #-8]
    // 0xb757d8: LoadField: r2 = r1->field_b
    //     0xb757d8: ldur            w2, [x1, #0xb]
    // 0xb757dc: DecompressPointer r2
    //     0xb757dc: add             x2, x2, HEAP, lsl #32
    // 0xb757e0: cmp             w2, NULL
    // 0xb757e4: b.eq            #0xb76c28
    // 0xb757e8: LoadField: r3 = r2->field_b
    //     0xb757e8: ldur            w3, [x2, #0xb]
    // 0xb757ec: DecompressPointer r3
    //     0xb757ec: add             x3, x3, HEAP, lsl #32
    // 0xb757f0: LoadField: r2 = r3->field_1f
    //     0xb757f0: ldur            w2, [x3, #0x1f]
    // 0xb757f4: DecompressPointer r2
    //     0xb757f4: add             x2, x2, HEAP, lsl #32
    // 0xb757f8: StoreField: r0->field_13 = r2
    //     0xb757f8: stur            w2, [x0, #0x13]
    // 0xb757fc: str             x0, [SP]
    // 0xb75800: r0 = _interpolate()
    //     0xb75800: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb75804: mov             x2, x0
    // 0xb75808: ldur            x0, [fp, #-8]
    // 0xb7580c: ldur            x1, [fp, #-0x10]
    // 0xb75810: stur            x2, [fp, #-0x20]
    // 0xb75814: r0 = of()
    //     0xb75814: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75818: LoadField: r1 = r0->field_87
    //     0xb75818: ldur            w1, [x0, #0x87]
    // 0xb7581c: DecompressPointer r1
    //     0xb7581c: add             x1, x1, HEAP, lsl #32
    // 0xb75820: LoadField: r0 = r1->field_2b
    //     0xb75820: ldur            w0, [x1, #0x2b]
    // 0xb75824: DecompressPointer r0
    //     0xb75824: add             x0, x0, HEAP, lsl #32
    // 0xb75828: stur            x0, [fp, #-0x28]
    // 0xb7582c: r1 = Instance_Color
    //     0xb7582c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb75830: d0 = 0.400000
    //     0xb75830: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb75834: r0 = withOpacity()
    //     0xb75834: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb75838: r16 = 12.000000
    //     0xb75838: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7583c: ldr             x16, [x16, #0x9e8]
    // 0xb75840: stp             x0, x16, [SP]
    // 0xb75844: ldur            x1, [fp, #-0x28]
    // 0xb75848: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb75848: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7584c: ldr             x4, [x4, #0xaa0]
    // 0xb75850: r0 = copyWith()
    //     0xb75850: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb75854: stur            x0, [fp, #-0x28]
    // 0xb75858: r0 = Text()
    //     0xb75858: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7585c: mov             x1, x0
    // 0xb75860: ldur            x0, [fp, #-0x20]
    // 0xb75864: stur            x1, [fp, #-0x50]
    // 0xb75868: StoreField: r1->field_b = r0
    //     0xb75868: stur            w0, [x1, #0xb]
    // 0xb7586c: ldur            x0, [fp, #-0x28]
    // 0xb75870: StoreField: r1->field_13 = r0
    //     0xb75870: stur            w0, [x1, #0x13]
    // 0xb75874: r0 = Padding()
    //     0xb75874: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb75878: mov             x1, x0
    // 0xb7587c: r0 = Instance_EdgeInsets
    //     0xb7587c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb75880: ldr             x0, [x0, #0x990]
    // 0xb75884: stur            x1, [fp, #-0x20]
    // 0xb75888: StoreField: r1->field_f = r0
    //     0xb75888: stur            w0, [x1, #0xf]
    // 0xb7588c: ldur            x2, [fp, #-0x50]
    // 0xb75890: StoreField: r1->field_b = r2
    //     0xb75890: stur            w2, [x1, #0xb]
    // 0xb75894: r0 = Visibility()
    //     0xb75894: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb75898: mov             x3, x0
    // 0xb7589c: ldur            x0, [fp, #-0x20]
    // 0xb758a0: stur            x3, [fp, #-0x28]
    // 0xb758a4: StoreField: r3->field_b = r0
    //     0xb758a4: stur            w0, [x3, #0xb]
    // 0xb758a8: r0 = Instance_SizedBox
    //     0xb758a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb758ac: StoreField: r3->field_f = r0
    //     0xb758ac: stur            w0, [x3, #0xf]
    // 0xb758b0: r4 = true
    //     0xb758b0: add             x4, NULL, #0x20  ; true
    // 0xb758b4: StoreField: r3->field_13 = r4
    //     0xb758b4: stur            w4, [x3, #0x13]
    // 0xb758b8: r5 = false
    //     0xb758b8: add             x5, NULL, #0x30  ; false
    // 0xb758bc: ArrayStore: r3[0] = r5  ; List_4
    //     0xb758bc: stur            w5, [x3, #0x17]
    // 0xb758c0: StoreField: r3->field_1b = r5
    //     0xb758c0: stur            w5, [x3, #0x1b]
    // 0xb758c4: StoreField: r3->field_1f = r5
    //     0xb758c4: stur            w5, [x3, #0x1f]
    // 0xb758c8: StoreField: r3->field_23 = r5
    //     0xb758c8: stur            w5, [x3, #0x23]
    // 0xb758cc: StoreField: r3->field_27 = r5
    //     0xb758cc: stur            w5, [x3, #0x27]
    // 0xb758d0: StoreField: r3->field_2b = r5
    //     0xb758d0: stur            w5, [x3, #0x2b]
    // 0xb758d4: ldur            x6, [fp, #-8]
    // 0xb758d8: LoadField: r1 = r6->field_b
    //     0xb758d8: ldur            w1, [x6, #0xb]
    // 0xb758dc: DecompressPointer r1
    //     0xb758dc: add             x1, x1, HEAP, lsl #32
    // 0xb758e0: cmp             w1, NULL
    // 0xb758e4: b.eq            #0xb76c2c
    // 0xb758e8: LoadField: r2 = r1->field_b
    //     0xb758e8: ldur            w2, [x1, #0xb]
    // 0xb758ec: DecompressPointer r2
    //     0xb758ec: add             x2, x2, HEAP, lsl #32
    // 0xb758f0: LoadField: r1 = r2->field_3b
    //     0xb758f0: ldur            w1, [x2, #0x3b]
    // 0xb758f4: DecompressPointer r1
    //     0xb758f4: add             x1, x1, HEAP, lsl #32
    // 0xb758f8: cmp             w1, NULL
    // 0xb758fc: b.ne            #0xb75914
    // 0xb75900: r1 = <ProductCustomisation>
    //     0xb75900: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xb75904: ldr             x1, [x1, #0x370]
    // 0xb75908: r2 = 0
    //     0xb75908: movz            x2, #0
    // 0xb7590c: r0 = AllocateArray()
    //     0xb7590c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75910: mov             x1, x0
    // 0xb75914: ldur            x3, [fp, #-8]
    // 0xb75918: ldur            x2, [fp, #-0x28]
    // 0xb7591c: r0 = LoadClassIdInstr(r1)
    //     0xb7591c: ldur            x0, [x1, #-1]
    //     0xb75920: ubfx            x0, x0, #0xc, #0x14
    // 0xb75924: r0 = GDT[cid_x0 + 0xe517]()
    //     0xb75924: movz            x17, #0xe517
    //     0xb75928: add             lr, x0, x17
    //     0xb7592c: ldr             lr, [x21, lr, lsl #3]
    //     0xb75930: blr             lr
    // 0xb75934: ldur            x1, [fp, #-0x10]
    // 0xb75938: stur            x0, [fp, #-0x20]
    // 0xb7593c: r0 = of()
    //     0xb7593c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75940: LoadField: r1 = r0->field_87
    //     0xb75940: ldur            w1, [x0, #0x87]
    // 0xb75944: DecompressPointer r1
    //     0xb75944: add             x1, x1, HEAP, lsl #32
    // 0xb75948: LoadField: r0 = r1->field_2b
    //     0xb75948: ldur            w0, [x1, #0x2b]
    // 0xb7594c: DecompressPointer r0
    //     0xb7594c: add             x0, x0, HEAP, lsl #32
    // 0xb75950: r16 = 12.000000
    //     0xb75950: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb75954: ldr             x16, [x16, #0x9e8]
    // 0xb75958: r30 = Instance_Color
    //     0xb75958: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7595c: stp             lr, x16, [SP]
    // 0xb75960: mov             x1, x0
    // 0xb75964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb75964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb75968: ldr             x4, [x4, #0xaa0]
    // 0xb7596c: r0 = copyWith()
    //     0xb7596c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb75970: stur            x0, [fp, #-0x50]
    // 0xb75974: r0 = Text()
    //     0xb75974: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb75978: mov             x1, x0
    // 0xb7597c: r0 = "Customised"
    //     0xb7597c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xb75980: ldr             x0, [x0, #0xd88]
    // 0xb75984: stur            x1, [fp, #-0x58]
    // 0xb75988: StoreField: r1->field_b = r0
    //     0xb75988: stur            w0, [x1, #0xb]
    // 0xb7598c: ldur            x0, [fp, #-0x50]
    // 0xb75990: StoreField: r1->field_13 = r0
    //     0xb75990: stur            w0, [x1, #0x13]
    // 0xb75994: r0 = Padding()
    //     0xb75994: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb75998: mov             x1, x0
    // 0xb7599c: r0 = Instance_EdgeInsets
    //     0xb7599c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb759a0: ldr             x0, [x0, #0x990]
    // 0xb759a4: stur            x1, [fp, #-0x50]
    // 0xb759a8: StoreField: r1->field_f = r0
    //     0xb759a8: stur            w0, [x1, #0xf]
    // 0xb759ac: ldur            x2, [fp, #-0x58]
    // 0xb759b0: StoreField: r1->field_b = r2
    //     0xb759b0: stur            w2, [x1, #0xb]
    // 0xb759b4: r0 = Visibility()
    //     0xb759b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb759b8: mov             x3, x0
    // 0xb759bc: ldur            x0, [fp, #-0x50]
    // 0xb759c0: stur            x3, [fp, #-0x58]
    // 0xb759c4: StoreField: r3->field_b = r0
    //     0xb759c4: stur            w0, [x3, #0xb]
    // 0xb759c8: r0 = Instance_SizedBox
    //     0xb759c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb759cc: StoreField: r3->field_f = r0
    //     0xb759cc: stur            w0, [x3, #0xf]
    // 0xb759d0: ldur            x1, [fp, #-0x20]
    // 0xb759d4: StoreField: r3->field_13 = r1
    //     0xb759d4: stur            w1, [x3, #0x13]
    // 0xb759d8: r4 = false
    //     0xb759d8: add             x4, NULL, #0x30  ; false
    // 0xb759dc: ArrayStore: r3[0] = r4  ; List_4
    //     0xb759dc: stur            w4, [x3, #0x17]
    // 0xb759e0: StoreField: r3->field_1b = r4
    //     0xb759e0: stur            w4, [x3, #0x1b]
    // 0xb759e4: StoreField: r3->field_1f = r4
    //     0xb759e4: stur            w4, [x3, #0x1f]
    // 0xb759e8: StoreField: r3->field_23 = r4
    //     0xb759e8: stur            w4, [x3, #0x23]
    // 0xb759ec: StoreField: r3->field_27 = r4
    //     0xb759ec: stur            w4, [x3, #0x27]
    // 0xb759f0: StoreField: r3->field_2b = r4
    //     0xb759f0: stur            w4, [x3, #0x2b]
    // 0xb759f4: r1 = Null
    //     0xb759f4: mov             x1, NULL
    // 0xb759f8: r2 = 4
    //     0xb759f8: movz            x2, #0x4
    // 0xb759fc: r0 = AllocateArray()
    //     0xb759fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75a00: mov             x2, x0
    // 0xb75a04: ldur            x0, [fp, #-0x28]
    // 0xb75a08: stur            x2, [fp, #-0x20]
    // 0xb75a0c: StoreField: r2->field_f = r0
    //     0xb75a0c: stur            w0, [x2, #0xf]
    // 0xb75a10: ldur            x0, [fp, #-0x58]
    // 0xb75a14: StoreField: r2->field_13 = r0
    //     0xb75a14: stur            w0, [x2, #0x13]
    // 0xb75a18: r1 = <Widget>
    //     0xb75a18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb75a1c: r0 = AllocateGrowableArray()
    //     0xb75a1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb75a20: mov             x1, x0
    // 0xb75a24: ldur            x0, [fp, #-0x20]
    // 0xb75a28: stur            x1, [fp, #-0x28]
    // 0xb75a2c: StoreField: r1->field_f = r0
    //     0xb75a2c: stur            w0, [x1, #0xf]
    // 0xb75a30: r2 = 4
    //     0xb75a30: movz            x2, #0x4
    // 0xb75a34: StoreField: r1->field_b = r2
    //     0xb75a34: stur            w2, [x1, #0xb]
    // 0xb75a38: r0 = Column()
    //     0xb75a38: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb75a3c: mov             x2, x0
    // 0xb75a40: r0 = Instance_Axis
    //     0xb75a40: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb75a44: stur            x2, [fp, #-0x50]
    // 0xb75a48: StoreField: r2->field_f = r0
    //     0xb75a48: stur            w0, [x2, #0xf]
    // 0xb75a4c: r3 = Instance_MainAxisAlignment
    //     0xb75a4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb75a50: ldr             x3, [x3, #0xa08]
    // 0xb75a54: StoreField: r2->field_13 = r3
    //     0xb75a54: stur            w3, [x2, #0x13]
    // 0xb75a58: r4 = Instance_MainAxisSize
    //     0xb75a58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb75a5c: ldr             x4, [x4, #0xa10]
    // 0xb75a60: ArrayStore: r2[0] = r4  ; List_4
    //     0xb75a60: stur            w4, [x2, #0x17]
    // 0xb75a64: r5 = Instance_CrossAxisAlignment
    //     0xb75a64: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb75a68: ldr             x5, [x5, #0x890]
    // 0xb75a6c: StoreField: r2->field_1b = r5
    //     0xb75a6c: stur            w5, [x2, #0x1b]
    // 0xb75a70: r6 = Instance_VerticalDirection
    //     0xb75a70: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb75a74: ldr             x6, [x6, #0xa20]
    // 0xb75a78: StoreField: r2->field_23 = r6
    //     0xb75a78: stur            w6, [x2, #0x23]
    // 0xb75a7c: r7 = Instance_Clip
    //     0xb75a7c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb75a80: ldr             x7, [x7, #0x38]
    // 0xb75a84: StoreField: r2->field_2b = r7
    //     0xb75a84: stur            w7, [x2, #0x2b]
    // 0xb75a88: StoreField: r2->field_2f = rZR
    //     0xb75a88: stur            xzr, [x2, #0x2f]
    // 0xb75a8c: ldur            x1, [fp, #-0x28]
    // 0xb75a90: StoreField: r2->field_b = r1
    //     0xb75a90: stur            w1, [x2, #0xb]
    // 0xb75a94: ldur            x8, [fp, #-8]
    // 0xb75a98: LoadField: r1 = r8->field_b
    //     0xb75a98: ldur            w1, [x8, #0xb]
    // 0xb75a9c: DecompressPointer r1
    //     0xb75a9c: add             x1, x1, HEAP, lsl #32
    // 0xb75aa0: cmp             w1, NULL
    // 0xb75aa4: b.eq            #0xb76c30
    // 0xb75aa8: LoadField: r9 = r1->field_b
    //     0xb75aa8: ldur            w9, [x1, #0xb]
    // 0xb75aac: DecompressPointer r9
    //     0xb75aac: add             x9, x9, HEAP, lsl #32
    // 0xb75ab0: LoadField: r1 = r9->field_13
    //     0xb75ab0: ldur            w1, [x9, #0x13]
    // 0xb75ab4: DecompressPointer r1
    //     0xb75ab4: add             x1, x1, HEAP, lsl #32
    // 0xb75ab8: cmp             w1, NULL
    // 0xb75abc: b.ne            #0xb75ac8
    // 0xb75ac0: r9 = ""
    //     0xb75ac0: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb75ac4: b               #0xb75acc
    // 0xb75ac8: mov             x9, x1
    // 0xb75acc: ldur            x1, [fp, #-0x10]
    // 0xb75ad0: stur            x9, [fp, #-0x20]
    // 0xb75ad4: r0 = of()
    //     0xb75ad4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75ad8: LoadField: r1 = r0->field_87
    //     0xb75ad8: ldur            w1, [x0, #0x87]
    // 0xb75adc: DecompressPointer r1
    //     0xb75adc: add             x1, x1, HEAP, lsl #32
    // 0xb75ae0: LoadField: r2 = r1->field_2b
    //     0xb75ae0: ldur            w2, [x1, #0x2b]
    // 0xb75ae4: DecompressPointer r2
    //     0xb75ae4: add             x2, x2, HEAP, lsl #32
    // 0xb75ae8: ldur            x1, [fp, #-8]
    // 0xb75aec: stur            x2, [fp, #-0x28]
    // 0xb75af0: LoadField: r0 = r1->field_b
    //     0xb75af0: ldur            w0, [x1, #0xb]
    // 0xb75af4: DecompressPointer r0
    //     0xb75af4: add             x0, x0, HEAP, lsl #32
    // 0xb75af8: cmp             w0, NULL
    // 0xb75afc: b.eq            #0xb76c34
    // 0xb75b00: LoadField: r3 = r0->field_b
    //     0xb75b00: ldur            w3, [x0, #0xb]
    // 0xb75b04: DecompressPointer r3
    //     0xb75b04: add             x3, x3, HEAP, lsl #32
    // 0xb75b08: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb75b08: ldur            w0, [x3, #0x17]
    // 0xb75b0c: DecompressPointer r0
    //     0xb75b0c: add             x0, x0, HEAP, lsl #32
    // 0xb75b10: r3 = LoadClassIdInstr(r0)
    //     0xb75b10: ldur            x3, [x0, #-1]
    //     0xb75b14: ubfx            x3, x3, #0xc, #0x14
    // 0xb75b18: r16 = "pending"
    //     0xb75b18: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xb75b1c: ldr             x16, [x16, #0xec0]
    // 0xb75b20: stp             x16, x0, [SP]
    // 0xb75b24: mov             x0, x3
    // 0xb75b28: mov             lr, x0
    // 0xb75b2c: ldr             lr, [x21, lr, lsl #3]
    // 0xb75b30: blr             lr
    // 0xb75b34: tbnz            w0, #4, #0xb75b44
    // 0xb75b38: r1 = Instance_MaterialColor
    //     0xb75b38: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!MaterialColor@d6bda1
    //     0xb75b3c: ldr             x1, [x1, #0xec8]
    // 0xb75b40: b               #0xb75bf4
    // 0xb75b44: ldur            x1, [fp, #-8]
    // 0xb75b48: LoadField: r0 = r1->field_b
    //     0xb75b48: ldur            w0, [x1, #0xb]
    // 0xb75b4c: DecompressPointer r0
    //     0xb75b4c: add             x0, x0, HEAP, lsl #32
    // 0xb75b50: cmp             w0, NULL
    // 0xb75b54: b.eq            #0xb76c38
    // 0xb75b58: LoadField: r2 = r0->field_b
    //     0xb75b58: ldur            w2, [x0, #0xb]
    // 0xb75b5c: DecompressPointer r2
    //     0xb75b5c: add             x2, x2, HEAP, lsl #32
    // 0xb75b60: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb75b60: ldur            w0, [x2, #0x17]
    // 0xb75b64: DecompressPointer r0
    //     0xb75b64: add             x0, x0, HEAP, lsl #32
    // 0xb75b68: r2 = LoadClassIdInstr(r0)
    //     0xb75b68: ldur            x2, [x0, #-1]
    //     0xb75b6c: ubfx            x2, x2, #0xc, #0x14
    // 0xb75b70: r16 = "cancel_initiated"
    //     0xb75b70: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ed0] "cancel_initiated"
    //     0xb75b74: ldr             x16, [x16, #0xed0]
    // 0xb75b78: stp             x16, x0, [SP]
    // 0xb75b7c: mov             x0, x2
    // 0xb75b80: mov             lr, x0
    // 0xb75b84: ldr             lr, [x21, lr, lsl #3]
    // 0xb75b88: blr             lr
    // 0xb75b8c: tbz             w0, #4, #0xb75bdc
    // 0xb75b90: ldur            x1, [fp, #-8]
    // 0xb75b94: LoadField: r0 = r1->field_b
    //     0xb75b94: ldur            w0, [x1, #0xb]
    // 0xb75b98: DecompressPointer r0
    //     0xb75b98: add             x0, x0, HEAP, lsl #32
    // 0xb75b9c: cmp             w0, NULL
    // 0xb75ba0: b.eq            #0xb76c3c
    // 0xb75ba4: LoadField: r2 = r0->field_b
    //     0xb75ba4: ldur            w2, [x0, #0xb]
    // 0xb75ba8: DecompressPointer r2
    //     0xb75ba8: add             x2, x2, HEAP, lsl #32
    // 0xb75bac: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb75bac: ldur            w0, [x2, #0x17]
    // 0xb75bb0: DecompressPointer r0
    //     0xb75bb0: add             x0, x0, HEAP, lsl #32
    // 0xb75bb4: r2 = LoadClassIdInstr(r0)
    //     0xb75bb4: ldur            x2, [x0, #-1]
    //     0xb75bb8: ubfx            x2, x2, #0xc, #0x14
    // 0xb75bbc: r16 = "failure"
    //     0xb75bbc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ebe8] "failure"
    //     0xb75bc0: ldr             x16, [x16, #0xbe8]
    // 0xb75bc4: stp             x16, x0, [SP]
    // 0xb75bc8: mov             x0, x2
    // 0xb75bcc: mov             lr, x0
    // 0xb75bd0: ldr             lr, [x21, lr, lsl #3]
    // 0xb75bd4: blr             lr
    // 0xb75bd8: tbnz            w0, #4, #0xb75be8
    // 0xb75bdc: r0 = Instance_MaterialAccentColor
    //     0xb75bdc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!MaterialAccentColor@d6bca1
    //     0xb75be0: ldr             x0, [x0, #0xed8]
    // 0xb75be4: b               #0xb75bf0
    // 0xb75be8: r0 = Instance_Color
    //     0xb75be8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb75bec: ldr             x0, [x0, #0x858]
    // 0xb75bf0: mov             x1, x0
    // 0xb75bf4: ldur            x0, [fp, #-8]
    // 0xb75bf8: ldur            x2, [fp, #-0x20]
    // 0xb75bfc: r16 = 14.000000
    //     0xb75bfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb75c00: ldr             x16, [x16, #0x1d8]
    // 0xb75c04: stp             x1, x16, [SP]
    // 0xb75c08: ldur            x1, [fp, #-0x28]
    // 0xb75c0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb75c0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb75c10: ldr             x4, [x4, #0xaa0]
    // 0xb75c14: r0 = copyWith()
    //     0xb75c14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb75c18: stur            x0, [fp, #-0x28]
    // 0xb75c1c: r0 = Text()
    //     0xb75c1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb75c20: mov             x1, x0
    // 0xb75c24: ldur            x0, [fp, #-0x20]
    // 0xb75c28: stur            x1, [fp, #-0x58]
    // 0xb75c2c: StoreField: r1->field_b = r0
    //     0xb75c2c: stur            w0, [x1, #0xb]
    // 0xb75c30: ldur            x0, [fp, #-0x28]
    // 0xb75c34: StoreField: r1->field_13 = r0
    //     0xb75c34: stur            w0, [x1, #0x13]
    // 0xb75c38: r0 = Padding()
    //     0xb75c38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb75c3c: mov             x1, x0
    // 0xb75c40: r0 = Instance_EdgeInsets
    //     0xb75c40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb75c44: ldr             x0, [x0, #0x990]
    // 0xb75c48: stur            x1, [fp, #-0x20]
    // 0xb75c4c: StoreField: r1->field_f = r0
    //     0xb75c4c: stur            w0, [x1, #0xf]
    // 0xb75c50: ldur            x0, [fp, #-0x58]
    // 0xb75c54: StoreField: r1->field_b = r0
    //     0xb75c54: stur            w0, [x1, #0xb]
    // 0xb75c58: r0 = Visibility()
    //     0xb75c58: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb75c5c: mov             x3, x0
    // 0xb75c60: ldur            x0, [fp, #-0x20]
    // 0xb75c64: stur            x3, [fp, #-0x58]
    // 0xb75c68: StoreField: r3->field_b = r0
    //     0xb75c68: stur            w0, [x3, #0xb]
    // 0xb75c6c: r0 = Instance_SizedBox
    //     0xb75c6c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb75c70: StoreField: r3->field_f = r0
    //     0xb75c70: stur            w0, [x3, #0xf]
    // 0xb75c74: r4 = true
    //     0xb75c74: add             x4, NULL, #0x20  ; true
    // 0xb75c78: StoreField: r3->field_13 = r4
    //     0xb75c78: stur            w4, [x3, #0x13]
    // 0xb75c7c: r5 = false
    //     0xb75c7c: add             x5, NULL, #0x30  ; false
    // 0xb75c80: ArrayStore: r3[0] = r5  ; List_4
    //     0xb75c80: stur            w5, [x3, #0x17]
    // 0xb75c84: StoreField: r3->field_1b = r5
    //     0xb75c84: stur            w5, [x3, #0x1b]
    // 0xb75c88: StoreField: r3->field_1f = r5
    //     0xb75c88: stur            w5, [x3, #0x1f]
    // 0xb75c8c: StoreField: r3->field_23 = r5
    //     0xb75c8c: stur            w5, [x3, #0x23]
    // 0xb75c90: StoreField: r3->field_27 = r5
    //     0xb75c90: stur            w5, [x3, #0x27]
    // 0xb75c94: StoreField: r3->field_2b = r5
    //     0xb75c94: stur            w5, [x3, #0x2b]
    // 0xb75c98: ldur            x6, [fp, #-8]
    // 0xb75c9c: LoadField: r1 = r6->field_b
    //     0xb75c9c: ldur            w1, [x6, #0xb]
    // 0xb75ca0: DecompressPointer r1
    //     0xb75ca0: add             x1, x1, HEAP, lsl #32
    // 0xb75ca4: cmp             w1, NULL
    // 0xb75ca8: b.eq            #0xb76c40
    // 0xb75cac: LoadField: r7 = r1->field_b
    //     0xb75cac: ldur            w7, [x1, #0xb]
    // 0xb75cb0: DecompressPointer r7
    //     0xb75cb0: add             x7, x7, HEAP, lsl #32
    // 0xb75cb4: stur            x7, [fp, #-0x28]
    // 0xb75cb8: LoadField: r1 = r7->field_23
    //     0xb75cb8: ldur            w1, [x7, #0x23]
    // 0xb75cbc: DecompressPointer r1
    //     0xb75cbc: add             x1, x1, HEAP, lsl #32
    // 0xb75cc0: cmp             w1, NULL
    // 0xb75cc4: b.ne            #0xb75cd0
    // 0xb75cc8: r8 = ""
    //     0xb75cc8: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb75ccc: b               #0xb75cd4
    // 0xb75cd0: mov             x8, x1
    // 0xb75cd4: stur            x8, [fp, #-0x20]
    // 0xb75cd8: r1 = Null
    //     0xb75cd8: mov             x1, NULL
    // 0xb75cdc: r2 = 6
    //     0xb75cdc: movz            x2, #0x6
    // 0xb75ce0: r0 = AllocateArray()
    //     0xb75ce0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75ce4: mov             x1, x0
    // 0xb75ce8: ldur            x0, [fp, #-0x20]
    // 0xb75cec: StoreField: r1->field_f = r0
    //     0xb75cec: stur            w0, [x1, #0xf]
    // 0xb75cf0: r16 = " x "
    //     0xb75cf0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a7e8] " x "
    //     0xb75cf4: ldr             x16, [x16, #0x7e8]
    // 0xb75cf8: StoreField: r1->field_13 = r16
    //     0xb75cf8: stur            w16, [x1, #0x13]
    // 0xb75cfc: ldur            x0, [fp, #-0x28]
    // 0xb75d00: LoadField: r2 = r0->field_27
    //     0xb75d00: ldur            w2, [x0, #0x27]
    // 0xb75d04: DecompressPointer r2
    //     0xb75d04: add             x2, x2, HEAP, lsl #32
    // 0xb75d08: cmp             w2, NULL
    // 0xb75d0c: b.ne            #0xb75d18
    // 0xb75d10: r7 = ""
    //     0xb75d10: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb75d14: b               #0xb75d1c
    // 0xb75d18: mov             x7, x2
    // 0xb75d1c: ldur            x2, [fp, #-8]
    // 0xb75d20: ldur            x6, [fp, #-0x48]
    // 0xb75d24: ldur            x5, [fp, #-0x38]
    // 0xb75d28: ldur            x4, [fp, #-0x40]
    // 0xb75d2c: ldur            x3, [fp, #-0x50]
    // 0xb75d30: ldur            x0, [fp, #-0x58]
    // 0xb75d34: ArrayStore: r1[0] = r7  ; List_4
    //     0xb75d34: stur            w7, [x1, #0x17]
    // 0xb75d38: str             x1, [SP]
    // 0xb75d3c: r0 = _interpolate()
    //     0xb75d3c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb75d40: ldur            x1, [fp, #-0x10]
    // 0xb75d44: stur            x0, [fp, #-0x20]
    // 0xb75d48: r0 = of()
    //     0xb75d48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75d4c: LoadField: r1 = r0->field_87
    //     0xb75d4c: ldur            w1, [x0, #0x87]
    // 0xb75d50: DecompressPointer r1
    //     0xb75d50: add             x1, x1, HEAP, lsl #32
    // 0xb75d54: LoadField: r0 = r1->field_2b
    //     0xb75d54: ldur            w0, [x1, #0x2b]
    // 0xb75d58: DecompressPointer r0
    //     0xb75d58: add             x0, x0, HEAP, lsl #32
    // 0xb75d5c: stur            x0, [fp, #-0x28]
    // 0xb75d60: r1 = Instance_Color
    //     0xb75d60: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb75d64: d0 = 0.700000
    //     0xb75d64: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb75d68: ldr             d0, [x17, #0xf48]
    // 0xb75d6c: r0 = withOpacity()
    //     0xb75d6c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb75d70: r16 = 12.000000
    //     0xb75d70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb75d74: ldr             x16, [x16, #0x9e8]
    // 0xb75d78: stp             x16, x0, [SP]
    // 0xb75d7c: ldur            x1, [fp, #-0x28]
    // 0xb75d80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb75d80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb75d84: ldr             x4, [x4, #0x9b8]
    // 0xb75d88: r0 = copyWith()
    //     0xb75d88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb75d8c: stur            x0, [fp, #-0x28]
    // 0xb75d90: r0 = Text()
    //     0xb75d90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb75d94: mov             x1, x0
    // 0xb75d98: ldur            x0, [fp, #-0x20]
    // 0xb75d9c: stur            x1, [fp, #-0x60]
    // 0xb75da0: StoreField: r1->field_b = r0
    //     0xb75da0: stur            w0, [x1, #0xb]
    // 0xb75da4: ldur            x0, [fp, #-0x28]
    // 0xb75da8: StoreField: r1->field_13 = r0
    //     0xb75da8: stur            w0, [x1, #0x13]
    // 0xb75dac: r0 = Visibility()
    //     0xb75dac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb75db0: mov             x3, x0
    // 0xb75db4: ldur            x0, [fp, #-0x60]
    // 0xb75db8: stur            x3, [fp, #-0x20]
    // 0xb75dbc: StoreField: r3->field_b = r0
    //     0xb75dbc: stur            w0, [x3, #0xb]
    // 0xb75dc0: r0 = Instance_SizedBox
    //     0xb75dc0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb75dc4: StoreField: r3->field_f = r0
    //     0xb75dc4: stur            w0, [x3, #0xf]
    // 0xb75dc8: r4 = false
    //     0xb75dc8: add             x4, NULL, #0x30  ; false
    // 0xb75dcc: StoreField: r3->field_13 = r4
    //     0xb75dcc: stur            w4, [x3, #0x13]
    // 0xb75dd0: ArrayStore: r3[0] = r4  ; List_4
    //     0xb75dd0: stur            w4, [x3, #0x17]
    // 0xb75dd4: StoreField: r3->field_1b = r4
    //     0xb75dd4: stur            w4, [x3, #0x1b]
    // 0xb75dd8: StoreField: r3->field_1f = r4
    //     0xb75dd8: stur            w4, [x3, #0x1f]
    // 0xb75ddc: StoreField: r3->field_23 = r4
    //     0xb75ddc: stur            w4, [x3, #0x23]
    // 0xb75de0: StoreField: r3->field_27 = r4
    //     0xb75de0: stur            w4, [x3, #0x27]
    // 0xb75de4: StoreField: r3->field_2b = r4
    //     0xb75de4: stur            w4, [x3, #0x2b]
    // 0xb75de8: r1 = Null
    //     0xb75de8: mov             x1, NULL
    // 0xb75dec: r2 = 14
    //     0xb75dec: movz            x2, #0xe
    // 0xb75df0: r0 = AllocateArray()
    //     0xb75df0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75df4: mov             x2, x0
    // 0xb75df8: ldur            x0, [fp, #-0x38]
    // 0xb75dfc: stur            x2, [fp, #-0x28]
    // 0xb75e00: StoreField: r2->field_f = r0
    //     0xb75e00: stur            w0, [x2, #0xf]
    // 0xb75e04: r16 = Instance_SizedBox
    //     0xb75e04: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb75e08: ldr             x16, [x16, #0x328]
    // 0xb75e0c: StoreField: r2->field_13 = r16
    //     0xb75e0c: stur            w16, [x2, #0x13]
    // 0xb75e10: ldur            x0, [fp, #-0x40]
    // 0xb75e14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb75e14: stur            w0, [x2, #0x17]
    // 0xb75e18: ldur            x0, [fp, #-0x50]
    // 0xb75e1c: StoreField: r2->field_1b = r0
    //     0xb75e1c: stur            w0, [x2, #0x1b]
    // 0xb75e20: ldur            x0, [fp, #-0x58]
    // 0xb75e24: StoreField: r2->field_1f = r0
    //     0xb75e24: stur            w0, [x2, #0x1f]
    // 0xb75e28: r16 = Instance_SizedBox
    //     0xb75e28: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb75e2c: ldr             x16, [x16, #0xc70]
    // 0xb75e30: StoreField: r2->field_23 = r16
    //     0xb75e30: stur            w16, [x2, #0x23]
    // 0xb75e34: ldur            x0, [fp, #-0x20]
    // 0xb75e38: StoreField: r2->field_27 = r0
    //     0xb75e38: stur            w0, [x2, #0x27]
    // 0xb75e3c: r1 = <Widget>
    //     0xb75e3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb75e40: r0 = AllocateGrowableArray()
    //     0xb75e40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb75e44: mov             x1, x0
    // 0xb75e48: ldur            x0, [fp, #-0x28]
    // 0xb75e4c: stur            x1, [fp, #-0x20]
    // 0xb75e50: StoreField: r1->field_f = r0
    //     0xb75e50: stur            w0, [x1, #0xf]
    // 0xb75e54: r0 = 14
    //     0xb75e54: movz            x0, #0xe
    // 0xb75e58: StoreField: r1->field_b = r0
    //     0xb75e58: stur            w0, [x1, #0xb]
    // 0xb75e5c: r0 = Column()
    //     0xb75e5c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb75e60: mov             x2, x0
    // 0xb75e64: r0 = Instance_Axis
    //     0xb75e64: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb75e68: stur            x2, [fp, #-0x28]
    // 0xb75e6c: StoreField: r2->field_f = r0
    //     0xb75e6c: stur            w0, [x2, #0xf]
    // 0xb75e70: r3 = Instance_MainAxisAlignment
    //     0xb75e70: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb75e74: ldr             x3, [x3, #0xa08]
    // 0xb75e78: StoreField: r2->field_13 = r3
    //     0xb75e78: stur            w3, [x2, #0x13]
    // 0xb75e7c: r1 = Instance_MainAxisSize
    //     0xb75e7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb75e80: ldr             x1, [x1, #0xdd0]
    // 0xb75e84: ArrayStore: r2[0] = r1  ; List_4
    //     0xb75e84: stur            w1, [x2, #0x17]
    // 0xb75e88: r4 = Instance_CrossAxisAlignment
    //     0xb75e88: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb75e8c: ldr             x4, [x4, #0x890]
    // 0xb75e90: StoreField: r2->field_1b = r4
    //     0xb75e90: stur            w4, [x2, #0x1b]
    // 0xb75e94: r5 = Instance_VerticalDirection
    //     0xb75e94: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb75e98: ldr             x5, [x5, #0xa20]
    // 0xb75e9c: StoreField: r2->field_23 = r5
    //     0xb75e9c: stur            w5, [x2, #0x23]
    // 0xb75ea0: r6 = Instance_Clip
    //     0xb75ea0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb75ea4: ldr             x6, [x6, #0x38]
    // 0xb75ea8: StoreField: r2->field_2b = r6
    //     0xb75ea8: stur            w6, [x2, #0x2b]
    // 0xb75eac: StoreField: r2->field_2f = rZR
    //     0xb75eac: stur            xzr, [x2, #0x2f]
    // 0xb75eb0: ldur            x1, [fp, #-0x20]
    // 0xb75eb4: StoreField: r2->field_b = r1
    //     0xb75eb4: stur            w1, [x2, #0xb]
    // 0xb75eb8: r1 = <FlexParentData>
    //     0xb75eb8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb75ebc: ldr             x1, [x1, #0xe00]
    // 0xb75ec0: r0 = Expanded()
    //     0xb75ec0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb75ec4: mov             x2, x0
    // 0xb75ec8: r0 = 1
    //     0xb75ec8: movz            x0, #0x1
    // 0xb75ecc: stur            x2, [fp, #-0x20]
    // 0xb75ed0: StoreField: r2->field_13 = r0
    //     0xb75ed0: stur            x0, [x2, #0x13]
    // 0xb75ed4: r1 = Instance_FlexFit
    //     0xb75ed4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb75ed8: ldr             x1, [x1, #0xe08]
    // 0xb75edc: StoreField: r2->field_1b = r1
    //     0xb75edc: stur            w1, [x2, #0x1b]
    // 0xb75ee0: ldur            x1, [fp, #-0x28]
    // 0xb75ee4: StoreField: r2->field_b = r1
    //     0xb75ee4: stur            w1, [x2, #0xb]
    // 0xb75ee8: ldur            x1, [fp, #-0x10]
    // 0xb75eec: r0 = of()
    //     0xb75eec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb75ef0: LoadField: r1 = r0->field_5b
    //     0xb75ef0: ldur            w1, [x0, #0x5b]
    // 0xb75ef4: DecompressPointer r1
    //     0xb75ef4: add             x1, x1, HEAP, lsl #32
    // 0xb75ef8: stur            x1, [fp, #-0x28]
    // 0xb75efc: r0 = ColorFilter()
    //     0xb75efc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb75f00: mov             x1, x0
    // 0xb75f04: ldur            x0, [fp, #-0x28]
    // 0xb75f08: stur            x1, [fp, #-0x38]
    // 0xb75f0c: StoreField: r1->field_7 = r0
    //     0xb75f0c: stur            w0, [x1, #7]
    // 0xb75f10: r0 = Instance_BlendMode
    //     0xb75f10: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb75f14: ldr             x0, [x0, #0xb30]
    // 0xb75f18: StoreField: r1->field_b = r0
    //     0xb75f18: stur            w0, [x1, #0xb]
    // 0xb75f1c: r0 = 1
    //     0xb75f1c: movz            x0, #0x1
    // 0xb75f20: StoreField: r1->field_13 = r0
    //     0xb75f20: stur            x0, [x1, #0x13]
    // 0xb75f24: r0 = SvgPicture()
    //     0xb75f24: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb75f28: stur            x0, [fp, #-0x28]
    // 0xb75f2c: ldur            x16, [fp, #-0x38]
    // 0xb75f30: str             x16, [SP]
    // 0xb75f34: mov             x1, x0
    // 0xb75f38: r2 = "assets/images/small_right.svg"
    //     0xb75f38: add             x2, PP, #0x46, lsl #12  ; [pp+0x46a70] "assets/images/small_right.svg"
    //     0xb75f3c: ldr             x2, [x2, #0xa70]
    // 0xb75f40: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb75f40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb75f44: ldr             x4, [x4, #0xa38]
    // 0xb75f48: r0 = SvgPicture.asset()
    //     0xb75f48: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb75f4c: r1 = Null
    //     0xb75f4c: mov             x1, NULL
    // 0xb75f50: r2 = 10
    //     0xb75f50: movz            x2, #0xa
    // 0xb75f54: r0 = AllocateArray()
    //     0xb75f54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75f58: mov             x2, x0
    // 0xb75f5c: ldur            x0, [fp, #-0x48]
    // 0xb75f60: stur            x2, [fp, #-0x38]
    // 0xb75f64: StoreField: r2->field_f = r0
    //     0xb75f64: stur            w0, [x2, #0xf]
    // 0xb75f68: r16 = Instance_SizedBox
    //     0xb75f68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb75f6c: ldr             x16, [x16, #0xb20]
    // 0xb75f70: StoreField: r2->field_13 = r16
    //     0xb75f70: stur            w16, [x2, #0x13]
    // 0xb75f74: ldur            x0, [fp, #-0x20]
    // 0xb75f78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb75f78: stur            w0, [x2, #0x17]
    // 0xb75f7c: r16 = Instance_SizedBox
    //     0xb75f7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb75f80: ldr             x16, [x16, #0xb20]
    // 0xb75f84: StoreField: r2->field_1b = r16
    //     0xb75f84: stur            w16, [x2, #0x1b]
    // 0xb75f88: ldur            x0, [fp, #-0x28]
    // 0xb75f8c: StoreField: r2->field_1f = r0
    //     0xb75f8c: stur            w0, [x2, #0x1f]
    // 0xb75f90: r1 = <Widget>
    //     0xb75f90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb75f94: r0 = AllocateGrowableArray()
    //     0xb75f94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb75f98: mov             x1, x0
    // 0xb75f9c: ldur            x0, [fp, #-0x38]
    // 0xb75fa0: stur            x1, [fp, #-0x20]
    // 0xb75fa4: StoreField: r1->field_f = r0
    //     0xb75fa4: stur            w0, [x1, #0xf]
    // 0xb75fa8: r0 = 10
    //     0xb75fa8: movz            x0, #0xa
    // 0xb75fac: StoreField: r1->field_b = r0
    //     0xb75fac: stur            w0, [x1, #0xb]
    // 0xb75fb0: r0 = Row()
    //     0xb75fb0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb75fb4: mov             x1, x0
    // 0xb75fb8: r0 = Instance_Axis
    //     0xb75fb8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb75fbc: stur            x1, [fp, #-0x28]
    // 0xb75fc0: StoreField: r1->field_f = r0
    //     0xb75fc0: stur            w0, [x1, #0xf]
    // 0xb75fc4: r2 = Instance_MainAxisAlignment
    //     0xb75fc4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb75fc8: ldr             x2, [x2, #0xa08]
    // 0xb75fcc: StoreField: r1->field_13 = r2
    //     0xb75fcc: stur            w2, [x1, #0x13]
    // 0xb75fd0: r3 = Instance_MainAxisSize
    //     0xb75fd0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb75fd4: ldr             x3, [x3, #0xa10]
    // 0xb75fd8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb75fd8: stur            w3, [x1, #0x17]
    // 0xb75fdc: r4 = Instance_CrossAxisAlignment
    //     0xb75fdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb75fe0: ldr             x4, [x4, #0xa18]
    // 0xb75fe4: StoreField: r1->field_1b = r4
    //     0xb75fe4: stur            w4, [x1, #0x1b]
    // 0xb75fe8: r5 = Instance_VerticalDirection
    //     0xb75fe8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb75fec: ldr             x5, [x5, #0xa20]
    // 0xb75ff0: StoreField: r1->field_23 = r5
    //     0xb75ff0: stur            w5, [x1, #0x23]
    // 0xb75ff4: r6 = Instance_Clip
    //     0xb75ff4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb75ff8: ldr             x6, [x6, #0x38]
    // 0xb75ffc: StoreField: r1->field_2b = r6
    //     0xb75ffc: stur            w6, [x1, #0x2b]
    // 0xb76000: StoreField: r1->field_2f = rZR
    //     0xb76000: stur            xzr, [x1, #0x2f]
    // 0xb76004: ldur            x7, [fp, #-0x20]
    // 0xb76008: StoreField: r1->field_b = r7
    //     0xb76008: stur            w7, [x1, #0xb]
    // 0xb7600c: r0 = Padding()
    //     0xb7600c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb76010: mov             x1, x0
    // 0xb76014: r0 = Instance_EdgeInsets
    //     0xb76014: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xb76018: ldr             x0, [x0, #0xf98]
    // 0xb7601c: stur            x1, [fp, #-0x40]
    // 0xb76020: StoreField: r1->field_f = r0
    //     0xb76020: stur            w0, [x1, #0xf]
    // 0xb76024: ldur            x0, [fp, #-0x28]
    // 0xb76028: StoreField: r1->field_b = r0
    //     0xb76028: stur            w0, [x1, #0xb]
    // 0xb7602c: ldur            x0, [fp, #-8]
    // 0xb76030: LoadField: r2 = r0->field_b
    //     0xb76030: ldur            w2, [x0, #0xb]
    // 0xb76034: DecompressPointer r2
    //     0xb76034: add             x2, x2, HEAP, lsl #32
    // 0xb76038: cmp             w2, NULL
    // 0xb7603c: b.eq            #0xb76c44
    // 0xb76040: LoadField: r3 = r2->field_b
    //     0xb76040: ldur            w3, [x2, #0xb]
    // 0xb76044: DecompressPointer r3
    //     0xb76044: add             x3, x3, HEAP, lsl #32
    // 0xb76048: stur            x3, [fp, #-0x38]
    // 0xb7604c: LoadField: r2 = r3->field_3b
    //     0xb7604c: ldur            w2, [x3, #0x3b]
    // 0xb76050: DecompressPointer r2
    //     0xb76050: add             x2, x2, HEAP, lsl #32
    // 0xb76054: stur            x2, [fp, #-0x28]
    // 0xb76058: LoadField: r4 = r3->field_43
    //     0xb76058: ldur            w4, [x3, #0x43]
    // 0xb7605c: DecompressPointer r4
    //     0xb7605c: add             x4, x4, HEAP, lsl #32
    // 0xb76060: stur            x4, [fp, #-0x20]
    // 0xb76064: r0 = CustomisedStrip()
    //     0xb76064: bl              #0xa1c2f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xb76068: mov             x1, x0
    // 0xb7606c: ldur            x0, [fp, #-0x28]
    // 0xb76070: stur            x1, [fp, #-0x48]
    // 0xb76074: StoreField: r1->field_b = r0
    //     0xb76074: stur            w0, [x1, #0xb]
    // 0xb76078: ldur            x0, [fp, #-0x20]
    // 0xb7607c: StoreField: r1->field_13 = r0
    //     0xb7607c: stur            w0, [x1, #0x13]
    // 0xb76080: r0 = Visibility()
    //     0xb76080: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb76084: mov             x3, x0
    // 0xb76088: ldur            x0, [fp, #-0x48]
    // 0xb7608c: stur            x3, [fp, #-0x20]
    // 0xb76090: StoreField: r3->field_b = r0
    //     0xb76090: stur            w0, [x3, #0xb]
    // 0xb76094: r0 = Instance_SizedBox
    //     0xb76094: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb76098: StoreField: r3->field_f = r0
    //     0xb76098: stur            w0, [x3, #0xf]
    // 0xb7609c: r4 = false
    //     0xb7609c: add             x4, NULL, #0x30  ; false
    // 0xb760a0: StoreField: r3->field_13 = r4
    //     0xb760a0: stur            w4, [x3, #0x13]
    // 0xb760a4: ArrayStore: r3[0] = r4  ; List_4
    //     0xb760a4: stur            w4, [x3, #0x17]
    // 0xb760a8: StoreField: r3->field_1b = r4
    //     0xb760a8: stur            w4, [x3, #0x1b]
    // 0xb760ac: StoreField: r3->field_1f = r4
    //     0xb760ac: stur            w4, [x3, #0x1f]
    // 0xb760b0: StoreField: r3->field_23 = r4
    //     0xb760b0: stur            w4, [x3, #0x23]
    // 0xb760b4: StoreField: r3->field_27 = r4
    //     0xb760b4: stur            w4, [x3, #0x27]
    // 0xb760b8: StoreField: r3->field_2b = r4
    //     0xb760b8: stur            w4, [x3, #0x2b]
    // 0xb760bc: r1 = Null
    //     0xb760bc: mov             x1, NULL
    // 0xb760c0: r2 = 4
    //     0xb760c0: movz            x2, #0x4
    // 0xb760c4: r0 = AllocateArray()
    //     0xb760c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb760c8: mov             x2, x0
    // 0xb760cc: ldur            x0, [fp, #-0x40]
    // 0xb760d0: stur            x2, [fp, #-0x28]
    // 0xb760d4: StoreField: r2->field_f = r0
    //     0xb760d4: stur            w0, [x2, #0xf]
    // 0xb760d8: ldur            x0, [fp, #-0x20]
    // 0xb760dc: StoreField: r2->field_13 = r0
    //     0xb760dc: stur            w0, [x2, #0x13]
    // 0xb760e0: r1 = <Widget>
    //     0xb760e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb760e4: r0 = AllocateGrowableArray()
    //     0xb760e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb760e8: mov             x1, x0
    // 0xb760ec: ldur            x0, [fp, #-0x28]
    // 0xb760f0: stur            x1, [fp, #-0x20]
    // 0xb760f4: StoreField: r1->field_f = r0
    //     0xb760f4: stur            w0, [x1, #0xf]
    // 0xb760f8: r2 = 4
    //     0xb760f8: movz            x2, #0x4
    // 0xb760fc: StoreField: r1->field_b = r2
    //     0xb760fc: stur            w2, [x1, #0xb]
    // 0xb76100: r0 = Column()
    //     0xb76100: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb76104: mov             x1, x0
    // 0xb76108: r0 = Instance_Axis
    //     0xb76108: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7610c: stur            x1, [fp, #-0x28]
    // 0xb76110: StoreField: r1->field_f = r0
    //     0xb76110: stur            w0, [x1, #0xf]
    // 0xb76114: r2 = Instance_MainAxisAlignment
    //     0xb76114: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb76118: ldr             x2, [x2, #0xa08]
    // 0xb7611c: StoreField: r1->field_13 = r2
    //     0xb7611c: stur            w2, [x1, #0x13]
    // 0xb76120: r3 = Instance_MainAxisSize
    //     0xb76120: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb76124: ldr             x3, [x3, #0xa10]
    // 0xb76128: ArrayStore: r1[0] = r3  ; List_4
    //     0xb76128: stur            w3, [x1, #0x17]
    // 0xb7612c: r4 = Instance_CrossAxisAlignment
    //     0xb7612c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb76130: ldr             x4, [x4, #0xa18]
    // 0xb76134: StoreField: r1->field_1b = r4
    //     0xb76134: stur            w4, [x1, #0x1b]
    // 0xb76138: r5 = Instance_VerticalDirection
    //     0xb76138: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7613c: ldr             x5, [x5, #0xa20]
    // 0xb76140: StoreField: r1->field_23 = r5
    //     0xb76140: stur            w5, [x1, #0x23]
    // 0xb76144: r6 = Instance_Clip
    //     0xb76144: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb76148: ldr             x6, [x6, #0x38]
    // 0xb7614c: StoreField: r1->field_2b = r6
    //     0xb7614c: stur            w6, [x1, #0x2b]
    // 0xb76150: StoreField: r1->field_2f = rZR
    //     0xb76150: stur            xzr, [x1, #0x2f]
    // 0xb76154: ldur            x7, [fp, #-0x20]
    // 0xb76158: StoreField: r1->field_b = r7
    //     0xb76158: stur            w7, [x1, #0xb]
    // 0xb7615c: r0 = InkWell()
    //     0xb7615c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb76160: mov             x3, x0
    // 0xb76164: ldur            x0, [fp, #-0x28]
    // 0xb76168: stur            x3, [fp, #-0x20]
    // 0xb7616c: StoreField: r3->field_b = r0
    //     0xb7616c: stur            w0, [x3, #0xb]
    // 0xb76170: ldur            x2, [fp, #-0x18]
    // 0xb76174: r1 = Function '<anonymous closure>':.
    //     0xb76174: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7f0] AnonymousClosure: (0xb77e70), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb76178: ldr             x1, [x1, #0x7f0]
    // 0xb7617c: r0 = AllocateClosure()
    //     0xb7617c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb76180: mov             x1, x0
    // 0xb76184: ldur            x0, [fp, #-0x20]
    // 0xb76188: StoreField: r0->field_f = r1
    //     0xb76188: stur            w1, [x0, #0xf]
    // 0xb7618c: r2 = true
    //     0xb7618c: add             x2, NULL, #0x20  ; true
    // 0xb76190: StoreField: r0->field_43 = r2
    //     0xb76190: stur            w2, [x0, #0x43]
    // 0xb76194: r3 = Instance_BoxShape
    //     0xb76194: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb76198: ldr             x3, [x3, #0x80]
    // 0xb7619c: StoreField: r0->field_47 = r3
    //     0xb7619c: stur            w3, [x0, #0x47]
    // 0xb761a0: StoreField: r0->field_6f = r2
    //     0xb761a0: stur            w2, [x0, #0x6f]
    // 0xb761a4: r4 = false
    //     0xb761a4: add             x4, NULL, #0x30  ; false
    // 0xb761a8: StoreField: r0->field_73 = r4
    //     0xb761a8: stur            w4, [x0, #0x73]
    // 0xb761ac: StoreField: r0->field_83 = r2
    //     0xb761ac: stur            w2, [x0, #0x83]
    // 0xb761b0: StoreField: r0->field_7b = r4
    //     0xb761b0: stur            w4, [x0, #0x7b]
    // 0xb761b4: ldur            x1, [fp, #-0x38]
    // 0xb761b8: LoadField: r5 = r1->field_1b
    //     0xb761b8: ldur            w5, [x1, #0x1b]
    // 0xb761bc: DecompressPointer r5
    //     0xb761bc: add             x5, x5, HEAP, lsl #32
    // 0xb761c0: cmp             w5, NULL
    // 0xb761c4: b.ne            #0xb761d0
    // 0xb761c8: r5 = Null
    //     0xb761c8: mov             x5, NULL
    // 0xb761cc: b               #0xb761e4
    // 0xb761d0: LoadField: r6 = r5->field_7
    //     0xb761d0: ldur            w6, [x5, #7]
    // 0xb761d4: cbnz            w6, #0xb761e0
    // 0xb761d8: r5 = false
    //     0xb761d8: add             x5, NULL, #0x30  ; false
    // 0xb761dc: b               #0xb761e4
    // 0xb761e0: r5 = true
    //     0xb761e0: add             x5, NULL, #0x20  ; true
    // 0xb761e4: cmp             w5, NULL
    // 0xb761e8: b.ne            #0xb761f0
    // 0xb761ec: r5 = false
    //     0xb761ec: add             x5, NULL, #0x30  ; false
    // 0xb761f0: stur            x5, [fp, #-0x40]
    // 0xb761f4: LoadField: r6 = r1->field_13
    //     0xb761f4: ldur            w6, [x1, #0x13]
    // 0xb761f8: DecompressPointer r6
    //     0xb761f8: add             x6, x6, HEAP, lsl #32
    // 0xb761fc: cmp             w6, NULL
    // 0xb76200: b.ne            #0xb7620c
    // 0xb76204: r7 = ""
    //     0xb76204: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb76208: b               #0xb76210
    // 0xb7620c: mov             x7, x6
    // 0xb76210: ldur            x6, [fp, #-8]
    // 0xb76214: ldur            x1, [fp, #-0x10]
    // 0xb76218: stur            x7, [fp, #-0x28]
    // 0xb7621c: r0 = of()
    //     0xb7621c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb76220: LoadField: r1 = r0->field_87
    //     0xb76220: ldur            w1, [x0, #0x87]
    // 0xb76224: DecompressPointer r1
    //     0xb76224: add             x1, x1, HEAP, lsl #32
    // 0xb76228: LoadField: r0 = r1->field_7
    //     0xb76228: ldur            w0, [x1, #7]
    // 0xb7622c: DecompressPointer r0
    //     0xb7622c: add             x0, x0, HEAP, lsl #32
    // 0xb76230: r16 = Instance_Color
    //     0xb76230: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb76234: r30 = 12.000000
    //     0xb76234: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb76238: ldr             lr, [lr, #0x9e8]
    // 0xb7623c: stp             lr, x16, [SP]
    // 0xb76240: mov             x1, x0
    // 0xb76244: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb76244: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb76248: ldr             x4, [x4, #0x9b8]
    // 0xb7624c: r0 = copyWith()
    //     0xb7624c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb76250: stur            x0, [fp, #-0x38]
    // 0xb76254: r0 = Text()
    //     0xb76254: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb76258: mov             x2, x0
    // 0xb7625c: ldur            x0, [fp, #-0x28]
    // 0xb76260: stur            x2, [fp, #-0x48]
    // 0xb76264: StoreField: r2->field_b = r0
    //     0xb76264: stur            w0, [x2, #0xb]
    // 0xb76268: ldur            x0, [fp, #-0x38]
    // 0xb7626c: StoreField: r2->field_13 = r0
    //     0xb7626c: stur            w0, [x2, #0x13]
    // 0xb76270: ldur            x0, [fp, #-8]
    // 0xb76274: LoadField: r1 = r0->field_b
    //     0xb76274: ldur            w1, [x0, #0xb]
    // 0xb76278: DecompressPointer r1
    //     0xb76278: add             x1, x1, HEAP, lsl #32
    // 0xb7627c: cmp             w1, NULL
    // 0xb76280: b.eq            #0xb76c48
    // 0xb76284: LoadField: r3 = r1->field_b
    //     0xb76284: ldur            w3, [x1, #0xb]
    // 0xb76288: DecompressPointer r3
    //     0xb76288: add             x3, x3, HEAP, lsl #32
    // 0xb7628c: LoadField: r1 = r3->field_1b
    //     0xb7628c: ldur            w1, [x3, #0x1b]
    // 0xb76290: DecompressPointer r1
    //     0xb76290: add             x1, x1, HEAP, lsl #32
    // 0xb76294: cmp             w1, NULL
    // 0xb76298: b.ne            #0xb762a4
    // 0xb7629c: r4 = ""
    //     0xb7629c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb762a0: b               #0xb762a8
    // 0xb762a4: mov             x4, x1
    // 0xb762a8: ldur            x3, [fp, #-0x40]
    // 0xb762ac: ldur            x1, [fp, #-0x10]
    // 0xb762b0: stur            x4, [fp, #-0x28]
    // 0xb762b4: r0 = of()
    //     0xb762b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb762b8: LoadField: r1 = r0->field_87
    //     0xb762b8: ldur            w1, [x0, #0x87]
    // 0xb762bc: DecompressPointer r1
    //     0xb762bc: add             x1, x1, HEAP, lsl #32
    // 0xb762c0: LoadField: r0 = r1->field_2b
    //     0xb762c0: ldur            w0, [x1, #0x2b]
    // 0xb762c4: DecompressPointer r0
    //     0xb762c4: add             x0, x0, HEAP, lsl #32
    // 0xb762c8: stur            x0, [fp, #-0x38]
    // 0xb762cc: r1 = Instance_Color
    //     0xb762cc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb762d0: d0 = 0.400000
    //     0xb762d0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb762d4: r0 = withOpacity()
    //     0xb762d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb762d8: r16 = 12.000000
    //     0xb762d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb762dc: ldr             x16, [x16, #0x9e8]
    // 0xb762e0: stp             x16, x0, [SP]
    // 0xb762e4: ldur            x1, [fp, #-0x38]
    // 0xb762e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb762e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb762ec: ldr             x4, [x4, #0x9b8]
    // 0xb762f0: r0 = copyWith()
    //     0xb762f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb762f4: stur            x0, [fp, #-0x38]
    // 0xb762f8: r0 = Text()
    //     0xb762f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb762fc: mov             x3, x0
    // 0xb76300: ldur            x0, [fp, #-0x28]
    // 0xb76304: stur            x3, [fp, #-0x50]
    // 0xb76308: StoreField: r3->field_b = r0
    //     0xb76308: stur            w0, [x3, #0xb]
    // 0xb7630c: ldur            x0, [fp, #-0x38]
    // 0xb76310: StoreField: r3->field_13 = r0
    //     0xb76310: stur            w0, [x3, #0x13]
    // 0xb76314: r1 = Null
    //     0xb76314: mov             x1, NULL
    // 0xb76318: r2 = 6
    //     0xb76318: movz            x2, #0x6
    // 0xb7631c: r0 = AllocateArray()
    //     0xb7631c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb76320: mov             x2, x0
    // 0xb76324: ldur            x0, [fp, #-0x48]
    // 0xb76328: stur            x2, [fp, #-0x28]
    // 0xb7632c: StoreField: r2->field_f = r0
    //     0xb7632c: stur            w0, [x2, #0xf]
    // 0xb76330: r16 = Instance_SizedBox
    //     0xb76330: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb76334: ldr             x16, [x16, #0x328]
    // 0xb76338: StoreField: r2->field_13 = r16
    //     0xb76338: stur            w16, [x2, #0x13]
    // 0xb7633c: ldur            x0, [fp, #-0x50]
    // 0xb76340: ArrayStore: r2[0] = r0  ; List_4
    //     0xb76340: stur            w0, [x2, #0x17]
    // 0xb76344: r1 = <Widget>
    //     0xb76344: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb76348: r0 = AllocateGrowableArray()
    //     0xb76348: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7634c: mov             x1, x0
    // 0xb76350: ldur            x0, [fp, #-0x28]
    // 0xb76354: stur            x1, [fp, #-0x38]
    // 0xb76358: StoreField: r1->field_f = r0
    //     0xb76358: stur            w0, [x1, #0xf]
    // 0xb7635c: r0 = 6
    //     0xb7635c: movz            x0, #0x6
    // 0xb76360: StoreField: r1->field_b = r0
    //     0xb76360: stur            w0, [x1, #0xb]
    // 0xb76364: r0 = Column()
    //     0xb76364: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb76368: mov             x1, x0
    // 0xb7636c: r0 = Instance_Axis
    //     0xb7636c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb76370: stur            x1, [fp, #-0x28]
    // 0xb76374: StoreField: r1->field_f = r0
    //     0xb76374: stur            w0, [x1, #0xf]
    // 0xb76378: r2 = Instance_MainAxisAlignment
    //     0xb76378: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7637c: ldr             x2, [x2, #0xa08]
    // 0xb76380: StoreField: r1->field_13 = r2
    //     0xb76380: stur            w2, [x1, #0x13]
    // 0xb76384: r3 = Instance_MainAxisSize
    //     0xb76384: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb76388: ldr             x3, [x3, #0xa10]
    // 0xb7638c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb7638c: stur            w3, [x1, #0x17]
    // 0xb76390: r4 = Instance_CrossAxisAlignment
    //     0xb76390: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb76394: ldr             x4, [x4, #0x890]
    // 0xb76398: StoreField: r1->field_1b = r4
    //     0xb76398: stur            w4, [x1, #0x1b]
    // 0xb7639c: r5 = Instance_VerticalDirection
    //     0xb7639c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb763a0: ldr             x5, [x5, #0xa20]
    // 0xb763a4: StoreField: r1->field_23 = r5
    //     0xb763a4: stur            w5, [x1, #0x23]
    // 0xb763a8: r6 = Instance_Clip
    //     0xb763a8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb763ac: ldr             x6, [x6, #0x38]
    // 0xb763b0: StoreField: r1->field_2b = r6
    //     0xb763b0: stur            w6, [x1, #0x2b]
    // 0xb763b4: StoreField: r1->field_2f = rZR
    //     0xb763b4: stur            xzr, [x1, #0x2f]
    // 0xb763b8: ldur            x7, [fp, #-0x38]
    // 0xb763bc: StoreField: r1->field_b = r7
    //     0xb763bc: stur            w7, [x1, #0xb]
    // 0xb763c0: r0 = Padding()
    //     0xb763c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb763c4: mov             x1, x0
    // 0xb763c8: r0 = Instance_EdgeInsets
    //     0xb763c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb763cc: ldr             x0, [x0, #0x980]
    // 0xb763d0: stur            x1, [fp, #-0x38]
    // 0xb763d4: StoreField: r1->field_f = r0
    //     0xb763d4: stur            w0, [x1, #0xf]
    // 0xb763d8: ldur            x2, [fp, #-0x28]
    // 0xb763dc: StoreField: r1->field_b = r2
    //     0xb763dc: stur            w2, [x1, #0xb]
    // 0xb763e0: r0 = Container()
    //     0xb763e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb763e4: stur            x0, [fp, #-0x28]
    // 0xb763e8: r16 = Instance_BoxDecoration
    //     0xb763e8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a7f8] Obj!BoxDecoration@d64b31
    //     0xb763ec: ldr             x16, [x16, #0x7f8]
    // 0xb763f0: ldur            lr, [fp, #-0x38]
    // 0xb763f4: stp             lr, x16, [SP]
    // 0xb763f8: mov             x1, x0
    // 0xb763fc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb763fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb76400: ldr             x4, [x4, #0x88]
    // 0xb76404: r0 = Container()
    //     0xb76404: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb76408: r0 = Padding()
    //     0xb76408: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7640c: mov             x1, x0
    // 0xb76410: r0 = Instance_EdgeInsets
    //     0xb76410: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb76414: ldr             x0, [x0, #0x980]
    // 0xb76418: stur            x1, [fp, #-0x38]
    // 0xb7641c: StoreField: r1->field_f = r0
    //     0xb7641c: stur            w0, [x1, #0xf]
    // 0xb76420: ldur            x0, [fp, #-0x28]
    // 0xb76424: StoreField: r1->field_b = r0
    //     0xb76424: stur            w0, [x1, #0xb]
    // 0xb76428: r0 = Visibility()
    //     0xb76428: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7642c: mov             x2, x0
    // 0xb76430: ldur            x0, [fp, #-0x38]
    // 0xb76434: stur            x2, [fp, #-0x48]
    // 0xb76438: StoreField: r2->field_b = r0
    //     0xb76438: stur            w0, [x2, #0xb]
    // 0xb7643c: r3 = Instance_SizedBox
    //     0xb7643c: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb76440: StoreField: r2->field_f = r3
    //     0xb76440: stur            w3, [x2, #0xf]
    // 0xb76444: ldur            x0, [fp, #-0x40]
    // 0xb76448: StoreField: r2->field_13 = r0
    //     0xb76448: stur            w0, [x2, #0x13]
    // 0xb7644c: r4 = false
    //     0xb7644c: add             x4, NULL, #0x30  ; false
    // 0xb76450: ArrayStore: r2[0] = r4  ; List_4
    //     0xb76450: stur            w4, [x2, #0x17]
    // 0xb76454: StoreField: r2->field_1b = r4
    //     0xb76454: stur            w4, [x2, #0x1b]
    // 0xb76458: StoreField: r2->field_1f = r4
    //     0xb76458: stur            w4, [x2, #0x1f]
    // 0xb7645c: StoreField: r2->field_23 = r4
    //     0xb7645c: stur            w4, [x2, #0x23]
    // 0xb76460: StoreField: r2->field_27 = r4
    //     0xb76460: stur            w4, [x2, #0x27]
    // 0xb76464: StoreField: r2->field_2b = r4
    //     0xb76464: stur            w4, [x2, #0x2b]
    // 0xb76468: ldur            x5, [fp, #-8]
    // 0xb7646c: LoadField: r0 = r5->field_b
    //     0xb7646c: ldur            w0, [x5, #0xb]
    // 0xb76470: DecompressPointer r0
    //     0xb76470: add             x0, x0, HEAP, lsl #32
    // 0xb76474: cmp             w0, NULL
    // 0xb76478: b.eq            #0xb76c4c
    // 0xb7647c: LoadField: r1 = r0->field_b
    //     0xb7647c: ldur            w1, [x0, #0xb]
    // 0xb76480: DecompressPointer r1
    //     0xb76480: add             x1, x1, HEAP, lsl #32
    // 0xb76484: LoadField: r0 = r1->field_83
    //     0xb76484: ldur            w0, [x1, #0x83]
    // 0xb76488: DecompressPointer r0
    //     0xb76488: add             x0, x0, HEAP, lsl #32
    // 0xb7648c: cmp             w0, NULL
    // 0xb76490: b.ne            #0xb7649c
    // 0xb76494: r6 = false
    //     0xb76494: add             x6, NULL, #0x30  ; false
    // 0xb76498: b               #0xb764a0
    // 0xb7649c: mov             x6, x0
    // 0xb764a0: stur            x6, [fp, #-0x28]
    // 0xb764a4: LoadField: r0 = r1->field_8b
    //     0xb764a4: ldur            w0, [x1, #0x8b]
    // 0xb764a8: DecompressPointer r0
    //     0xb764a8: add             x0, x0, HEAP, lsl #32
    // 0xb764ac: cmp             w0, NULL
    // 0xb764b0: b.ne            #0xb764bc
    // 0xb764b4: r0 = Null
    //     0xb764b4: mov             x0, NULL
    // 0xb764b8: b               #0xb764d4
    // 0xb764bc: LoadField: r7 = r0->field_f
    //     0xb764bc: ldur            x7, [x0, #0xf]
    // 0xb764c0: r0 = BoxInt64Instr(r7)
    //     0xb764c0: sbfiz           x0, x7, #1, #0x1f
    //     0xb764c4: cmp             x7, x0, asr #1
    //     0xb764c8: b.eq            #0xb764d4
    //     0xb764cc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb764d0: stur            x7, [x0, #7]
    // 0xb764d4: cmp             w0, NULL
    // 0xb764d8: b.ne            #0xb764e4
    // 0xb764dc: r7 = 0
    //     0xb764dc: movz            x7, #0
    // 0xb764e0: b               #0xb764f4
    // 0xb764e4: r1 = LoadInt32Instr(r0)
    //     0xb764e4: sbfx            x1, x0, #1, #0x1f
    //     0xb764e8: tbz             w0, #0, #0xb764f0
    //     0xb764ec: ldur            x1, [x0, #7]
    // 0xb764f0: mov             x7, x1
    // 0xb764f4: r0 = BoxInt64Instr(r7)
    //     0xb764f4: sbfiz           x0, x7, #1, #0x1f
    //     0xb764f8: cmp             x7, x0, asr #1
    //     0xb764fc: b.eq            #0xb76508
    //     0xb76500: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb76504: stur            x7, [x0, #7]
    // 0xb76508: stp             x0, NULL, [SP]
    // 0xb7650c: r0 = _Double.fromInteger()
    //     0xb7650c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb76510: ldur            x2, [fp, #-0x18]
    // 0xb76514: r1 = Function '<anonymous closure>':.
    //     0xb76514: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a800] AnonymousClosure: (0xb77d50), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb76518: ldr             x1, [x1, #0x800]
    // 0xb7651c: stur            x0, [fp, #-0x38]
    // 0xb76520: r0 = AllocateClosure()
    //     0xb76520: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb76524: stur            x0, [fp, #-0x40]
    // 0xb76528: r0 = RatingBar()
    //     0xb76528: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb7652c: mov             x3, x0
    // 0xb76530: ldur            x0, [fp, #-0x40]
    // 0xb76534: stur            x3, [fp, #-0x50]
    // 0xb76538: StoreField: r3->field_b = r0
    //     0xb76538: stur            w0, [x3, #0xb]
    // 0xb7653c: r0 = false
    //     0xb7653c: add             x0, NULL, #0x30  ; false
    // 0xb76540: StoreField: r3->field_1f = r0
    //     0xb76540: stur            w0, [x3, #0x1f]
    // 0xb76544: r4 = Instance_Axis
    //     0xb76544: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb76548: StoreField: r3->field_23 = r4
    //     0xb76548: stur            w4, [x3, #0x23]
    // 0xb7654c: r5 = true
    //     0xb7654c: add             x5, NULL, #0x20  ; true
    // 0xb76550: StoreField: r3->field_27 = r5
    //     0xb76550: stur            w5, [x3, #0x27]
    // 0xb76554: d0 = 2.000000
    //     0xb76554: fmov            d0, #2.00000000
    // 0xb76558: StoreField: r3->field_2b = d0
    //     0xb76558: stur            d0, [x3, #0x2b]
    // 0xb7655c: StoreField: r3->field_33 = r0
    //     0xb7655c: stur            w0, [x3, #0x33]
    // 0xb76560: ldur            x1, [fp, #-0x38]
    // 0xb76564: LoadField: d0 = r1->field_7
    //     0xb76564: ldur            d0, [x1, #7]
    // 0xb76568: StoreField: r3->field_37 = d0
    //     0xb76568: stur            d0, [x3, #0x37]
    // 0xb7656c: r1 = 5
    //     0xb7656c: movz            x1, #0x5
    // 0xb76570: StoreField: r3->field_3f = r1
    //     0xb76570: stur            x1, [x3, #0x3f]
    // 0xb76574: r1 = Instance_EdgeInsets
    //     0xb76574: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xb76578: ldr             x1, [x1, #0xa68]
    // 0xb7657c: StoreField: r3->field_47 = r1
    //     0xb7657c: stur            w1, [x3, #0x47]
    // 0xb76580: d0 = 20.000000
    //     0xb76580: fmov            d0, #20.00000000
    // 0xb76584: StoreField: r3->field_4b = d0
    //     0xb76584: stur            d0, [x3, #0x4b]
    // 0xb76588: d0 = 1.000000
    //     0xb76588: fmov            d0, #1.00000000
    // 0xb7658c: StoreField: r3->field_53 = d0
    //     0xb7658c: stur            d0, [x3, #0x53]
    // 0xb76590: StoreField: r3->field_5b = r0
    //     0xb76590: stur            w0, [x3, #0x5b]
    // 0xb76594: StoreField: r3->field_5f = r0
    //     0xb76594: stur            w0, [x3, #0x5f]
    // 0xb76598: ldur            x2, [fp, #-0x18]
    // 0xb7659c: r1 = Function '<anonymous closure>':.
    //     0xb7659c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a808] AnonymousClosure: (0xafce60), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb765a0: ldr             x1, [x1, #0x808]
    // 0xb765a4: r0 = AllocateClosure()
    //     0xb765a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb765a8: mov             x1, x0
    // 0xb765ac: ldur            x0, [fp, #-0x50]
    // 0xb765b0: StoreField: r0->field_63 = r1
    //     0xb765b0: stur            w1, [x0, #0x63]
    // 0xb765b4: ldur            x1, [fp, #-0x10]
    // 0xb765b8: r0 = of()
    //     0xb765b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb765bc: LoadField: r1 = r0->field_87
    //     0xb765bc: ldur            w1, [x0, #0x87]
    // 0xb765c0: DecompressPointer r1
    //     0xb765c0: add             x1, x1, HEAP, lsl #32
    // 0xb765c4: LoadField: r0 = r1->field_2b
    //     0xb765c4: ldur            w0, [x1, #0x2b]
    // 0xb765c8: DecompressPointer r0
    //     0xb765c8: add             x0, x0, HEAP, lsl #32
    // 0xb765cc: stur            x0, [fp, #-0x38]
    // 0xb765d0: r1 = Instance_Color
    //     0xb765d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb765d4: d0 = 0.300000
    //     0xb765d4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb765d8: ldr             d0, [x17, #0x658]
    // 0xb765dc: r0 = withOpacity()
    //     0xb765dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb765e0: r16 = 12.000000
    //     0xb765e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb765e4: ldr             x16, [x16, #0x9e8]
    // 0xb765e8: stp             x0, x16, [SP]
    // 0xb765ec: ldur            x1, [fp, #-0x38]
    // 0xb765f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb765f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb765f4: ldr             x4, [x4, #0xaa0]
    // 0xb765f8: r0 = copyWith()
    //     0xb765f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb765fc: stur            x0, [fp, #-0x38]
    // 0xb76600: r0 = Text()
    //     0xb76600: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb76604: mov             x1, x0
    // 0xb76608: r0 = "Rate this product"
    //     0xb76608: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fcb0] "Rate this product"
    //     0xb7660c: ldr             x0, [x0, #0xcb0]
    // 0xb76610: stur            x1, [fp, #-0x40]
    // 0xb76614: StoreField: r1->field_b = r0
    //     0xb76614: stur            w0, [x1, #0xb]
    // 0xb76618: ldur            x0, [fp, #-0x38]
    // 0xb7661c: StoreField: r1->field_13 = r0
    //     0xb7661c: stur            w0, [x1, #0x13]
    // 0xb76620: r0 = Padding()
    //     0xb76620: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb76624: mov             x3, x0
    // 0xb76628: r0 = Instance_EdgeInsets
    //     0xb76628: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb7662c: ldr             x0, [x0, #0x668]
    // 0xb76630: stur            x3, [fp, #-0x38]
    // 0xb76634: StoreField: r3->field_f = r0
    //     0xb76634: stur            w0, [x3, #0xf]
    // 0xb76638: ldur            x0, [fp, #-0x40]
    // 0xb7663c: StoreField: r3->field_b = r0
    //     0xb7663c: stur            w0, [x3, #0xb]
    // 0xb76640: r1 = Null
    //     0xb76640: mov             x1, NULL
    // 0xb76644: r2 = 4
    //     0xb76644: movz            x2, #0x4
    // 0xb76648: r0 = AllocateArray()
    //     0xb76648: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7664c: mov             x2, x0
    // 0xb76650: ldur            x0, [fp, #-0x50]
    // 0xb76654: stur            x2, [fp, #-0x40]
    // 0xb76658: StoreField: r2->field_f = r0
    //     0xb76658: stur            w0, [x2, #0xf]
    // 0xb7665c: ldur            x0, [fp, #-0x38]
    // 0xb76660: StoreField: r2->field_13 = r0
    //     0xb76660: stur            w0, [x2, #0x13]
    // 0xb76664: r1 = <Widget>
    //     0xb76664: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb76668: r0 = AllocateGrowableArray()
    //     0xb76668: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7666c: mov             x1, x0
    // 0xb76670: ldur            x0, [fp, #-0x40]
    // 0xb76674: stur            x1, [fp, #-0x38]
    // 0xb76678: StoreField: r1->field_f = r0
    //     0xb76678: stur            w0, [x1, #0xf]
    // 0xb7667c: r2 = 4
    //     0xb7667c: movz            x2, #0x4
    // 0xb76680: StoreField: r1->field_b = r2
    //     0xb76680: stur            w2, [x1, #0xb]
    // 0xb76684: r0 = Column()
    //     0xb76684: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb76688: mov             x2, x0
    // 0xb7668c: r0 = Instance_Axis
    //     0xb7668c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb76690: stur            x2, [fp, #-0x40]
    // 0xb76694: StoreField: r2->field_f = r0
    //     0xb76694: stur            w0, [x2, #0xf]
    // 0xb76698: r3 = Instance_MainAxisAlignment
    //     0xb76698: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7669c: ldr             x3, [x3, #0xa08]
    // 0xb766a0: StoreField: r2->field_13 = r3
    //     0xb766a0: stur            w3, [x2, #0x13]
    // 0xb766a4: r4 = Instance_MainAxisSize
    //     0xb766a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb766a8: ldr             x4, [x4, #0xa10]
    // 0xb766ac: ArrayStore: r2[0] = r4  ; List_4
    //     0xb766ac: stur            w4, [x2, #0x17]
    // 0xb766b0: r1 = Instance_CrossAxisAlignment
    //     0xb766b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb766b4: ldr             x1, [x1, #0x890]
    // 0xb766b8: StoreField: r2->field_1b = r1
    //     0xb766b8: stur            w1, [x2, #0x1b]
    // 0xb766bc: r5 = Instance_VerticalDirection
    //     0xb766bc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb766c0: ldr             x5, [x5, #0xa20]
    // 0xb766c4: StoreField: r2->field_23 = r5
    //     0xb766c4: stur            w5, [x2, #0x23]
    // 0xb766c8: r6 = Instance_Clip
    //     0xb766c8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb766cc: ldr             x6, [x6, #0x38]
    // 0xb766d0: StoreField: r2->field_2b = r6
    //     0xb766d0: stur            w6, [x2, #0x2b]
    // 0xb766d4: StoreField: r2->field_2f = rZR
    //     0xb766d4: stur            xzr, [x2, #0x2f]
    // 0xb766d8: ldur            x1, [fp, #-0x38]
    // 0xb766dc: StoreField: r2->field_b = r1
    //     0xb766dc: stur            w1, [x2, #0xb]
    // 0xb766e0: ldur            x7, [fp, #-8]
    // 0xb766e4: LoadField: r1 = r7->field_b
    //     0xb766e4: ldur            w1, [x7, #0xb]
    // 0xb766e8: DecompressPointer r1
    //     0xb766e8: add             x1, x1, HEAP, lsl #32
    // 0xb766ec: cmp             w1, NULL
    // 0xb766f0: b.eq            #0xb76c50
    // 0xb766f4: LoadField: r8 = r1->field_b
    //     0xb766f4: ldur            w8, [x1, #0xb]
    // 0xb766f8: DecompressPointer r8
    //     0xb766f8: add             x8, x8, HEAP, lsl #32
    // 0xb766fc: LoadField: r1 = r8->field_8f
    //     0xb766fc: ldur            w1, [x8, #0x8f]
    // 0xb76700: DecompressPointer r1
    //     0xb76700: add             x1, x1, HEAP, lsl #32
    // 0xb76704: cmp             w1, NULL
    // 0xb76708: b.ne            #0xb76714
    // 0xb7670c: r8 = ""
    //     0xb7670c: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb76710: b               #0xb76718
    // 0xb76714: mov             x8, x1
    // 0xb76718: ldur            x1, [fp, #-0x10]
    // 0xb7671c: stur            x8, [fp, #-0x38]
    // 0xb76720: r0 = of()
    //     0xb76720: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb76724: LoadField: r1 = r0->field_87
    //     0xb76724: ldur            w1, [x0, #0x87]
    // 0xb76728: DecompressPointer r1
    //     0xb76728: add             x1, x1, HEAP, lsl #32
    // 0xb7672c: LoadField: r0 = r1->field_f
    //     0xb7672c: ldur            w0, [x1, #0xf]
    // 0xb76730: DecompressPointer r0
    //     0xb76730: add             x0, x0, HEAP, lsl #32
    // 0xb76734: cmp             w0, NULL
    // 0xb76738: b.ne            #0xb76744
    // 0xb7673c: r4 = Null
    //     0xb7673c: mov             x4, NULL
    // 0xb76740: b               #0xb76778
    // 0xb76744: r16 = 12.000000
    //     0xb76744: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb76748: ldr             x16, [x16, #0x9e8]
    // 0xb7674c: r30 = Instance_Color
    //     0xb7674c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb76750: ldr             lr, [lr, #0x858]
    // 0xb76754: stp             lr, x16, [SP, #8]
    // 0xb76758: r16 = Instance_TextDecoration
    //     0xb76758: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb7675c: ldr             x16, [x16, #0x10]
    // 0xb76760: str             x16, [SP]
    // 0xb76764: mov             x1, x0
    // 0xb76768: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb76768: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb7676c: ldr             x4, [x4, #0xe38]
    // 0xb76770: r0 = copyWith()
    //     0xb76770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb76774: mov             x4, x0
    // 0xb76778: ldur            x1, [fp, #-8]
    // 0xb7677c: ldur            x3, [fp, #-0x28]
    // 0xb76780: ldur            x0, [fp, #-0x40]
    // 0xb76784: ldur            x2, [fp, #-0x38]
    // 0xb76788: stur            x4, [fp, #-0x10]
    // 0xb7678c: r0 = Text()
    //     0xb7678c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb76790: mov             x1, x0
    // 0xb76794: ldur            x0, [fp, #-0x38]
    // 0xb76798: stur            x1, [fp, #-0x50]
    // 0xb7679c: StoreField: r1->field_b = r0
    //     0xb7679c: stur            w0, [x1, #0xb]
    // 0xb767a0: ldur            x0, [fp, #-0x10]
    // 0xb767a4: StoreField: r1->field_13 = r0
    //     0xb767a4: stur            w0, [x1, #0x13]
    // 0xb767a8: r0 = InkWell()
    //     0xb767a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb767ac: mov             x3, x0
    // 0xb767b0: ldur            x0, [fp, #-0x50]
    // 0xb767b4: stur            x3, [fp, #-0x10]
    // 0xb767b8: StoreField: r3->field_b = r0
    //     0xb767b8: stur            w0, [x3, #0xb]
    // 0xb767bc: ldur            x2, [fp, #-0x18]
    // 0xb767c0: r1 = Function '<anonymous closure>':.
    //     0xb767c0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a810] AnonymousClosure: (0xb77c70), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb767c4: ldr             x1, [x1, #0x810]
    // 0xb767c8: r0 = AllocateClosure()
    //     0xb767c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb767cc: mov             x1, x0
    // 0xb767d0: ldur            x0, [fp, #-0x10]
    // 0xb767d4: StoreField: r0->field_f = r1
    //     0xb767d4: stur            w1, [x0, #0xf]
    // 0xb767d8: r3 = true
    //     0xb767d8: add             x3, NULL, #0x20  ; true
    // 0xb767dc: StoreField: r0->field_43 = r3
    //     0xb767dc: stur            w3, [x0, #0x43]
    // 0xb767e0: r1 = Instance_BoxShape
    //     0xb767e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb767e4: ldr             x1, [x1, #0x80]
    // 0xb767e8: StoreField: r0->field_47 = r1
    //     0xb767e8: stur            w1, [x0, #0x47]
    // 0xb767ec: StoreField: r0->field_6f = r3
    //     0xb767ec: stur            w3, [x0, #0x6f]
    // 0xb767f0: r4 = false
    //     0xb767f0: add             x4, NULL, #0x30  ; false
    // 0xb767f4: StoreField: r0->field_73 = r4
    //     0xb767f4: stur            w4, [x0, #0x73]
    // 0xb767f8: StoreField: r0->field_83 = r3
    //     0xb767f8: stur            w3, [x0, #0x83]
    // 0xb767fc: StoreField: r0->field_7b = r4
    //     0xb767fc: stur            w4, [x0, #0x7b]
    // 0xb76800: r1 = Null
    //     0xb76800: mov             x1, NULL
    // 0xb76804: r2 = 4
    //     0xb76804: movz            x2, #0x4
    // 0xb76808: r0 = AllocateArray()
    //     0xb76808: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7680c: mov             x2, x0
    // 0xb76810: ldur            x0, [fp, #-0x40]
    // 0xb76814: stur            x2, [fp, #-0x38]
    // 0xb76818: StoreField: r2->field_f = r0
    //     0xb76818: stur            w0, [x2, #0xf]
    // 0xb7681c: ldur            x0, [fp, #-0x10]
    // 0xb76820: StoreField: r2->field_13 = r0
    //     0xb76820: stur            w0, [x2, #0x13]
    // 0xb76824: r1 = <Widget>
    //     0xb76824: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb76828: r0 = AllocateGrowableArray()
    //     0xb76828: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7682c: mov             x1, x0
    // 0xb76830: ldur            x0, [fp, #-0x38]
    // 0xb76834: stur            x1, [fp, #-0x10]
    // 0xb76838: StoreField: r1->field_f = r0
    //     0xb76838: stur            w0, [x1, #0xf]
    // 0xb7683c: r0 = 4
    //     0xb7683c: movz            x0, #0x4
    // 0xb76840: StoreField: r1->field_b = r0
    //     0xb76840: stur            w0, [x1, #0xb]
    // 0xb76844: r0 = Row()
    //     0xb76844: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb76848: mov             x1, x0
    // 0xb7684c: r0 = Instance_Axis
    //     0xb7684c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb76850: stur            x1, [fp, #-0x38]
    // 0xb76854: StoreField: r1->field_f = r0
    //     0xb76854: stur            w0, [x1, #0xf]
    // 0xb76858: r0 = Instance_MainAxisAlignment
    //     0xb76858: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb7685c: ldr             x0, [x0, #0xa8]
    // 0xb76860: StoreField: r1->field_13 = r0
    //     0xb76860: stur            w0, [x1, #0x13]
    // 0xb76864: r0 = Instance_MainAxisSize
    //     0xb76864: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb76868: ldr             x0, [x0, #0xa10]
    // 0xb7686c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7686c: stur            w0, [x1, #0x17]
    // 0xb76870: r2 = Instance_CrossAxisAlignment
    //     0xb76870: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb76874: ldr             x2, [x2, #0xa18]
    // 0xb76878: StoreField: r1->field_1b = r2
    //     0xb76878: stur            w2, [x1, #0x1b]
    // 0xb7687c: r3 = Instance_VerticalDirection
    //     0xb7687c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb76880: ldr             x3, [x3, #0xa20]
    // 0xb76884: StoreField: r1->field_23 = r3
    //     0xb76884: stur            w3, [x1, #0x23]
    // 0xb76888: r4 = Instance_Clip
    //     0xb76888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7688c: ldr             x4, [x4, #0x38]
    // 0xb76890: StoreField: r1->field_2b = r4
    //     0xb76890: stur            w4, [x1, #0x2b]
    // 0xb76894: StoreField: r1->field_2f = rZR
    //     0xb76894: stur            xzr, [x1, #0x2f]
    // 0xb76898: ldur            x5, [fp, #-0x10]
    // 0xb7689c: StoreField: r1->field_b = r5
    //     0xb7689c: stur            w5, [x1, #0xb]
    // 0xb768a0: r0 = Padding()
    //     0xb768a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb768a4: mov             x1, x0
    // 0xb768a8: r0 = Instance_EdgeInsets
    //     0xb768a8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb768ac: ldr             x0, [x0, #0xf30]
    // 0xb768b0: stur            x1, [fp, #-0x10]
    // 0xb768b4: StoreField: r1->field_f = r0
    //     0xb768b4: stur            w0, [x1, #0xf]
    // 0xb768b8: ldur            x0, [fp, #-0x38]
    // 0xb768bc: StoreField: r1->field_b = r0
    //     0xb768bc: stur            w0, [x1, #0xb]
    // 0xb768c0: r0 = Visibility()
    //     0xb768c0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb768c4: mov             x1, x0
    // 0xb768c8: ldur            x0, [fp, #-0x10]
    // 0xb768cc: stur            x1, [fp, #-0x38]
    // 0xb768d0: StoreField: r1->field_b = r0
    //     0xb768d0: stur            w0, [x1, #0xb]
    // 0xb768d4: r2 = Instance_SizedBox
    //     0xb768d4: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb768d8: StoreField: r1->field_f = r2
    //     0xb768d8: stur            w2, [x1, #0xf]
    // 0xb768dc: ldur            x0, [fp, #-0x28]
    // 0xb768e0: StoreField: r1->field_13 = r0
    //     0xb768e0: stur            w0, [x1, #0x13]
    // 0xb768e4: r3 = false
    //     0xb768e4: add             x3, NULL, #0x30  ; false
    // 0xb768e8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb768e8: stur            w3, [x1, #0x17]
    // 0xb768ec: StoreField: r1->field_1b = r3
    //     0xb768ec: stur            w3, [x1, #0x1b]
    // 0xb768f0: StoreField: r1->field_1f = r3
    //     0xb768f0: stur            w3, [x1, #0x1f]
    // 0xb768f4: StoreField: r1->field_23 = r3
    //     0xb768f4: stur            w3, [x1, #0x23]
    // 0xb768f8: StoreField: r1->field_27 = r3
    //     0xb768f8: stur            w3, [x1, #0x27]
    // 0xb768fc: StoreField: r1->field_2b = r3
    //     0xb768fc: stur            w3, [x1, #0x2b]
    // 0xb76900: ldur            x4, [fp, #-8]
    // 0xb76904: LoadField: r0 = r4->field_b
    //     0xb76904: ldur            w0, [x4, #0xb]
    // 0xb76908: DecompressPointer r0
    //     0xb76908: add             x0, x0, HEAP, lsl #32
    // 0xb7690c: cmp             w0, NULL
    // 0xb76910: b.eq            #0xb76c54
    // 0xb76914: LoadField: r5 = r0->field_b
    //     0xb76914: ldur            w5, [x0, #0xb]
    // 0xb76918: DecompressPointer r5
    //     0xb76918: add             x5, x5, HEAP, lsl #32
    // 0xb7691c: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xb7691c: ldur            w0, [x5, #0x17]
    // 0xb76920: DecompressPointer r0
    //     0xb76920: add             x0, x0, HEAP, lsl #32
    // 0xb76924: r5 = LoadClassIdInstr(r0)
    //     0xb76924: ldur            x5, [x0, #-1]
    //     0xb76928: ubfx            x5, x5, #0xc, #0x14
    // 0xb7692c: r16 = "pending"
    //     0xb7692c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xb76930: ldr             x16, [x16, #0xec0]
    // 0xb76934: stp             x16, x0, [SP]
    // 0xb76938: mov             x0, x5
    // 0xb7693c: mov             lr, x0
    // 0xb76940: ldr             lr, [x21, lr, lsl #3]
    // 0xb76944: blr             lr
    // 0xb76948: eor             x1, x0, #0x10
    // 0xb7694c: stur            x1, [fp, #-0x10]
    // 0xb76950: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb76950: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb76954: ldr             x0, [x0, #0x1c80]
    //     0xb76958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb7695c: cmp             w0, w16
    //     0xb76960: b.ne            #0xb7696c
    //     0xb76964: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb76968: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb7696c: r0 = GetNavigation.size()
    //     0xb7696c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb76970: LoadField: d0 = r0->field_7
    //     0xb76970: ldur            d0, [x0, #7]
    // 0xb76974: ldur            x0, [fp, #-8]
    // 0xb76978: stur            d0, [fp, #-0x70]
    // 0xb7697c: LoadField: r1 = r0->field_b
    //     0xb7697c: ldur            w1, [x0, #0xb]
    // 0xb76980: DecompressPointer r1
    //     0xb76980: add             x1, x1, HEAP, lsl #32
    // 0xb76984: cmp             w1, NULL
    // 0xb76988: b.eq            #0xb76c58
    // 0xb7698c: LoadField: r0 = r1->field_b
    //     0xb7698c: ldur            w0, [x1, #0xb]
    // 0xb76990: DecompressPointer r0
    //     0xb76990: add             x0, x0, HEAP, lsl #32
    // 0xb76994: LoadField: r1 = r0->field_57
    //     0xb76994: ldur            w1, [x0, #0x57]
    // 0xb76998: DecompressPointer r1
    //     0xb76998: add             x1, x1, HEAP, lsl #32
    // 0xb7699c: cmp             w1, NULL
    // 0xb769a0: b.ne            #0xb769ac
    // 0xb769a4: r0 = Null
    //     0xb769a4: mov             x0, NULL
    // 0xb769a8: b               #0xb769b0
    // 0xb769ac: LoadField: r0 = r1->field_b
    //     0xb769ac: ldur            w0, [x1, #0xb]
    // 0xb769b0: cmp             w0, NULL
    // 0xb769b4: b.ne            #0xb769c0
    // 0xb769b8: r7 = 0
    //     0xb769b8: movz            x7, #0
    // 0xb769bc: b               #0xb769c8
    // 0xb769c0: r1 = LoadInt32Instr(r0)
    //     0xb769c0: sbfx            x1, x0, #1, #0x1f
    // 0xb769c4: mov             x7, x1
    // 0xb769c8: ldur            x6, [fp, #-0x30]
    // 0xb769cc: ldur            x5, [fp, #-0x20]
    // 0xb769d0: ldur            x4, [fp, #-0x48]
    // 0xb769d4: ldur            x3, [fp, #-0x38]
    // 0xb769d8: ldur            x0, [fp, #-0x10]
    // 0xb769dc: stur            x7, [fp, #-0x68]
    // 0xb769e0: r1 = Function '<anonymous closure>':.
    //     0xb769e0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a818] AnonymousClosure: (0xa6d658), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xb769e4: ldr             x1, [x1, #0x818]
    // 0xb769e8: r2 = Null
    //     0xb769e8: mov             x2, NULL
    // 0xb769ec: r0 = AllocateClosure()
    //     0xb769ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb769f0: ldur            x2, [fp, #-0x18]
    // 0xb769f4: r1 = Function '<anonymous closure>':.
    //     0xb769f4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a820] AnonymousClosure: (0xb76c74), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb769f8: ldr             x1, [x1, #0x820]
    // 0xb769fc: stur            x0, [fp, #-8]
    // 0xb76a00: r0 = AllocateClosure()
    //     0xb76a00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb76a04: stur            x0, [fp, #-0x18]
    // 0xb76a08: r0 = ListView()
    //     0xb76a08: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb76a0c: stur            x0, [fp, #-0x28]
    // 0xb76a10: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb76a10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb76a14: ldr             x16, [x16, #0x1c8]
    // 0xb76a18: r30 = Instance_Axis
    //     0xb76a18: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb76a1c: stp             lr, x16, [SP, #8]
    // 0xb76a20: r16 = true
    //     0xb76a20: add             x16, NULL, #0x20  ; true
    // 0xb76a24: str             x16, [SP]
    // 0xb76a28: mov             x1, x0
    // 0xb76a2c: ldur            x2, [fp, #-0x18]
    // 0xb76a30: ldur            x3, [fp, #-0x68]
    // 0xb76a34: ldur            x5, [fp, #-8]
    // 0xb76a38: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x5, shrinkWrap, 0x6, null]
    //     0xb76a38: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a228] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x6, Null]
    //     0xb76a3c: ldr             x4, [x4, #0x228]
    // 0xb76a40: r0 = ListView.separated()
    //     0xb76a40: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb76a44: r0 = Align()
    //     0xb76a44: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb76a48: mov             x1, x0
    // 0xb76a4c: r0 = Instance_Alignment
    //     0xb76a4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb76a50: ldr             x0, [x0, #0xb10]
    // 0xb76a54: stur            x1, [fp, #-0x18]
    // 0xb76a58: StoreField: r1->field_f = r0
    //     0xb76a58: stur            w0, [x1, #0xf]
    // 0xb76a5c: ldur            x0, [fp, #-0x28]
    // 0xb76a60: StoreField: r1->field_b = r0
    //     0xb76a60: stur            w0, [x1, #0xb]
    // 0xb76a64: ldur            d0, [fp, #-0x70]
    // 0xb76a68: r0 = inline_Allocate_Double()
    //     0xb76a68: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb76a6c: add             x0, x0, #0x10
    //     0xb76a70: cmp             x2, x0
    //     0xb76a74: b.ls            #0xb76c5c
    //     0xb76a78: str             x0, [THR, #0x50]  ; THR::top
    //     0xb76a7c: sub             x0, x0, #0xf
    //     0xb76a80: movz            x2, #0xe15c
    //     0xb76a84: movk            x2, #0x3, lsl #16
    //     0xb76a88: stur            x2, [x0, #-1]
    // 0xb76a8c: StoreField: r0->field_7 = d0
    //     0xb76a8c: stur            d0, [x0, #7]
    // 0xb76a90: stur            x0, [fp, #-8]
    // 0xb76a94: r0 = SizedBox()
    //     0xb76a94: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb76a98: mov             x1, x0
    // 0xb76a9c: ldur            x0, [fp, #-8]
    // 0xb76aa0: stur            x1, [fp, #-0x28]
    // 0xb76aa4: StoreField: r1->field_f = r0
    //     0xb76aa4: stur            w0, [x1, #0xf]
    // 0xb76aa8: r0 = 25.000000
    //     0xb76aa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0xb76aac: ldr             x0, [x0, #0x98]
    // 0xb76ab0: StoreField: r1->field_13 = r0
    //     0xb76ab0: stur            w0, [x1, #0x13]
    // 0xb76ab4: ldur            x0, [fp, #-0x18]
    // 0xb76ab8: StoreField: r1->field_b = r0
    //     0xb76ab8: stur            w0, [x1, #0xb]
    // 0xb76abc: r0 = Visibility()
    //     0xb76abc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb76ac0: mov             x3, x0
    // 0xb76ac4: ldur            x0, [fp, #-0x28]
    // 0xb76ac8: stur            x3, [fp, #-8]
    // 0xb76acc: StoreField: r3->field_b = r0
    //     0xb76acc: stur            w0, [x3, #0xb]
    // 0xb76ad0: r0 = Instance_SizedBox
    //     0xb76ad0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb76ad4: StoreField: r3->field_f = r0
    //     0xb76ad4: stur            w0, [x3, #0xf]
    // 0xb76ad8: ldur            x0, [fp, #-0x10]
    // 0xb76adc: StoreField: r3->field_13 = r0
    //     0xb76adc: stur            w0, [x3, #0x13]
    // 0xb76ae0: r0 = false
    //     0xb76ae0: add             x0, NULL, #0x30  ; false
    // 0xb76ae4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb76ae4: stur            w0, [x3, #0x17]
    // 0xb76ae8: StoreField: r3->field_1b = r0
    //     0xb76ae8: stur            w0, [x3, #0x1b]
    // 0xb76aec: StoreField: r3->field_1f = r0
    //     0xb76aec: stur            w0, [x3, #0x1f]
    // 0xb76af0: StoreField: r3->field_23 = r0
    //     0xb76af0: stur            w0, [x3, #0x23]
    // 0xb76af4: StoreField: r3->field_27 = r0
    //     0xb76af4: stur            w0, [x3, #0x27]
    // 0xb76af8: StoreField: r3->field_2b = r0
    //     0xb76af8: stur            w0, [x3, #0x2b]
    // 0xb76afc: r1 = Null
    //     0xb76afc: mov             x1, NULL
    // 0xb76b00: r2 = 8
    //     0xb76b00: movz            x2, #0x8
    // 0xb76b04: r0 = AllocateArray()
    //     0xb76b04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb76b08: mov             x2, x0
    // 0xb76b0c: ldur            x0, [fp, #-0x20]
    // 0xb76b10: stur            x2, [fp, #-0x10]
    // 0xb76b14: StoreField: r2->field_f = r0
    //     0xb76b14: stur            w0, [x2, #0xf]
    // 0xb76b18: ldur            x0, [fp, #-0x48]
    // 0xb76b1c: StoreField: r2->field_13 = r0
    //     0xb76b1c: stur            w0, [x2, #0x13]
    // 0xb76b20: ldur            x0, [fp, #-0x38]
    // 0xb76b24: ArrayStore: r2[0] = r0  ; List_4
    //     0xb76b24: stur            w0, [x2, #0x17]
    // 0xb76b28: ldur            x0, [fp, #-8]
    // 0xb76b2c: StoreField: r2->field_1b = r0
    //     0xb76b2c: stur            w0, [x2, #0x1b]
    // 0xb76b30: r1 = <Widget>
    //     0xb76b30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb76b34: r0 = AllocateGrowableArray()
    //     0xb76b34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb76b38: mov             x1, x0
    // 0xb76b3c: ldur            x0, [fp, #-0x10]
    // 0xb76b40: stur            x1, [fp, #-8]
    // 0xb76b44: StoreField: r1->field_f = r0
    //     0xb76b44: stur            w0, [x1, #0xf]
    // 0xb76b48: r0 = 8
    //     0xb76b48: movz            x0, #0x8
    // 0xb76b4c: StoreField: r1->field_b = r0
    //     0xb76b4c: stur            w0, [x1, #0xb]
    // 0xb76b50: r0 = Column()
    //     0xb76b50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb76b54: mov             x1, x0
    // 0xb76b58: r0 = Instance_Axis
    //     0xb76b58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb76b5c: stur            x1, [fp, #-0x10]
    // 0xb76b60: StoreField: r1->field_f = r0
    //     0xb76b60: stur            w0, [x1, #0xf]
    // 0xb76b64: r0 = Instance_MainAxisAlignment
    //     0xb76b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb76b68: ldr             x0, [x0, #0xa08]
    // 0xb76b6c: StoreField: r1->field_13 = r0
    //     0xb76b6c: stur            w0, [x1, #0x13]
    // 0xb76b70: r0 = Instance_MainAxisSize
    //     0xb76b70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb76b74: ldr             x0, [x0, #0xa10]
    // 0xb76b78: ArrayStore: r1[0] = r0  ; List_4
    //     0xb76b78: stur            w0, [x1, #0x17]
    // 0xb76b7c: r0 = Instance_CrossAxisAlignment
    //     0xb76b7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb76b80: ldr             x0, [x0, #0xa18]
    // 0xb76b84: StoreField: r1->field_1b = r0
    //     0xb76b84: stur            w0, [x1, #0x1b]
    // 0xb76b88: r0 = Instance_VerticalDirection
    //     0xb76b88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb76b8c: ldr             x0, [x0, #0xa20]
    // 0xb76b90: StoreField: r1->field_23 = r0
    //     0xb76b90: stur            w0, [x1, #0x23]
    // 0xb76b94: r0 = Instance_Clip
    //     0xb76b94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb76b98: ldr             x0, [x0, #0x38]
    // 0xb76b9c: StoreField: r1->field_2b = r0
    //     0xb76b9c: stur            w0, [x1, #0x2b]
    // 0xb76ba0: StoreField: r1->field_2f = rZR
    //     0xb76ba0: stur            xzr, [x1, #0x2f]
    // 0xb76ba4: ldur            x0, [fp, #-8]
    // 0xb76ba8: StoreField: r1->field_b = r0
    //     0xb76ba8: stur            w0, [x1, #0xb]
    // 0xb76bac: r0 = Padding()
    //     0xb76bac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb76bb0: mov             x1, x0
    // 0xb76bb4: r0 = Instance_EdgeInsets
    //     0xb76bb4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xb76bb8: ldr             x0, [x0, #0xb00]
    // 0xb76bbc: stur            x1, [fp, #-8]
    // 0xb76bc0: StoreField: r1->field_f = r0
    //     0xb76bc0: stur            w0, [x1, #0xf]
    // 0xb76bc4: ldur            x0, [fp, #-0x10]
    // 0xb76bc8: StoreField: r1->field_b = r0
    //     0xb76bc8: stur            w0, [x1, #0xb]
    // 0xb76bcc: r0 = Card()
    //     0xb76bcc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb76bd0: r1 = 0.000000
    //     0xb76bd0: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb76bd4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb76bd4: stur            w1, [x0, #0x17]
    // 0xb76bd8: ldur            x1, [fp, #-0x30]
    // 0xb76bdc: StoreField: r0->field_1b = r1
    //     0xb76bdc: stur            w1, [x0, #0x1b]
    // 0xb76be0: r1 = true
    //     0xb76be0: add             x1, NULL, #0x20  ; true
    // 0xb76be4: StoreField: r0->field_1f = r1
    //     0xb76be4: stur            w1, [x0, #0x1f]
    // 0xb76be8: ldur            x2, [fp, #-8]
    // 0xb76bec: StoreField: r0->field_2f = r2
    //     0xb76bec: stur            w2, [x0, #0x2f]
    // 0xb76bf0: StoreField: r0->field_2b = r1
    //     0xb76bf0: stur            w1, [x0, #0x2b]
    // 0xb76bf4: r1 = Instance__CardVariant
    //     0xb76bf4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb76bf8: ldr             x1, [x1, #0xa68]
    // 0xb76bfc: StoreField: r0->field_33 = r1
    //     0xb76bfc: stur            w1, [x0, #0x33]
    // 0xb76c00: LeaveFrame
    //     0xb76c00: mov             SP, fp
    //     0xb76c04: ldp             fp, lr, [SP], #0x10
    // 0xb76c08: ret
    //     0xb76c08: ret             
    // 0xb76c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb76c0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb76c10: b               #0xb753d8
    // 0xb76c14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb76c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb76c58: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb76c58: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb76c5c: SaveReg d0
    //     0xb76c5c: str             q0, [SP, #-0x10]!
    // 0xb76c60: SaveReg r1
    //     0xb76c60: str             x1, [SP, #-8]!
    // 0xb76c64: r0 = AllocateDouble()
    //     0xb76c64: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb76c68: RestoreReg r1
    //     0xb76c68: ldr             x1, [SP], #8
    // 0xb76c6c: RestoreReg d0
    //     0xb76c6c: ldr             q0, [SP], #0x10
    // 0xb76c70: b               #0xb76a8c
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb76c74, size: 0x83c
    // 0xb76c74: EnterFrame
    //     0xb76c74: stp             fp, lr, [SP, #-0x10]!
    //     0xb76c78: mov             fp, SP
    // 0xb76c7c: AllocStack(0x50)
    //     0xb76c7c: sub             SP, SP, #0x50
    // 0xb76c80: SetupParameters()
    //     0xb76c80: ldr             x0, [fp, #0x20]
    //     0xb76c84: ldur            w1, [x0, #0x17]
    //     0xb76c88: add             x1, x1, HEAP, lsl #32
    //     0xb76c8c: stur            x1, [fp, #-8]
    // 0xb76c90: CheckStackOverflow
    //     0xb76c90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb76c94: cmp             SP, x16
    //     0xb76c98: b.ls            #0xb7747c
    // 0xb76c9c: r1 = 2
    //     0xb76c9c: movz            x1, #0x2
    // 0xb76ca0: r0 = AllocateContext()
    //     0xb76ca0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb76ca4: mov             x3, x0
    // 0xb76ca8: ldur            x0, [fp, #-8]
    // 0xb76cac: stur            x3, [fp, #-0x10]
    // 0xb76cb0: StoreField: r3->field_b = r0
    //     0xb76cb0: stur            w0, [x3, #0xb]
    // 0xb76cb4: ldr             x4, [fp, #0x18]
    // 0xb76cb8: StoreField: r3->field_f = r4
    //     0xb76cb8: stur            w4, [x3, #0xf]
    // 0xb76cbc: ldr             x5, [fp, #0x10]
    // 0xb76cc0: StoreField: r3->field_13 = r5
    //     0xb76cc0: stur            w5, [x3, #0x13]
    // 0xb76cc4: r1 = <Widget>
    //     0xb76cc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb76cc8: r2 = 0
    //     0xb76cc8: movz            x2, #0
    // 0xb76ccc: r0 = _GrowableList()
    //     0xb76ccc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb76cd0: mov             x3, x0
    // 0xb76cd4: ldur            x2, [fp, #-8]
    // 0xb76cd8: stur            x3, [fp, #-0x18]
    // 0xb76cdc: LoadField: r0 = r2->field_f
    //     0xb76cdc: ldur            w0, [x2, #0xf]
    // 0xb76ce0: DecompressPointer r0
    //     0xb76ce0: add             x0, x0, HEAP, lsl #32
    // 0xb76ce4: LoadField: r1 = r0->field_b
    //     0xb76ce4: ldur            w1, [x0, #0xb]
    // 0xb76ce8: DecompressPointer r1
    //     0xb76ce8: add             x1, x1, HEAP, lsl #32
    // 0xb76cec: cmp             w1, NULL
    // 0xb76cf0: b.eq            #0xb77484
    // 0xb76cf4: LoadField: r0 = r1->field_b
    //     0xb76cf4: ldur            w0, [x1, #0xb]
    // 0xb76cf8: DecompressPointer r0
    //     0xb76cf8: add             x0, x0, HEAP, lsl #32
    // 0xb76cfc: LoadField: r4 = r0->field_57
    //     0xb76cfc: ldur            w4, [x0, #0x57]
    // 0xb76d00: DecompressPointer r4
    //     0xb76d00: add             x4, x4, HEAP, lsl #32
    // 0xb76d04: cmp             w4, NULL
    // 0xb76d08: b.ne            #0xb76d18
    // 0xb76d0c: ldr             x5, [fp, #0x10]
    // 0xb76d10: r0 = Null
    //     0xb76d10: mov             x0, NULL
    // 0xb76d14: b               #0xb76d6c
    // 0xb76d18: ldr             x5, [fp, #0x10]
    // 0xb76d1c: LoadField: r0 = r4->field_b
    //     0xb76d1c: ldur            w0, [x4, #0xb]
    // 0xb76d20: r6 = LoadInt32Instr(r5)
    //     0xb76d20: sbfx            x6, x5, #1, #0x1f
    //     0xb76d24: tbz             w5, #0, #0xb76d2c
    //     0xb76d28: ldur            x6, [x5, #7]
    // 0xb76d2c: r1 = LoadInt32Instr(r0)
    //     0xb76d2c: sbfx            x1, x0, #1, #0x1f
    // 0xb76d30: mov             x0, x1
    // 0xb76d34: mov             x1, x6
    // 0xb76d38: cmp             x1, x0
    // 0xb76d3c: b.hs            #0xb77488
    // 0xb76d40: LoadField: r0 = r4->field_f
    //     0xb76d40: ldur            w0, [x4, #0xf]
    // 0xb76d44: DecompressPointer r0
    //     0xb76d44: add             x0, x0, HEAP, lsl #32
    // 0xb76d48: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb76d48: add             x16, x0, x6, lsl #2
    //     0xb76d4c: ldur            w1, [x16, #0xf]
    // 0xb76d50: DecompressPointer r1
    //     0xb76d50: add             x1, x1, HEAP, lsl #32
    // 0xb76d54: cmp             w1, NULL
    // 0xb76d58: b.ne            #0xb76d64
    // 0xb76d5c: r0 = Null
    //     0xb76d5c: mov             x0, NULL
    // 0xb76d60: b               #0xb76d6c
    // 0xb76d64: LoadField: r0 = r1->field_7
    //     0xb76d64: ldur            w0, [x1, #7]
    // 0xb76d68: DecompressPointer r0
    //     0xb76d68: add             x0, x0, HEAP, lsl #32
    // 0xb76d6c: r1 = LoadClassIdInstr(r0)
    //     0xb76d6c: ldur            x1, [x0, #-1]
    //     0xb76d70: ubfx            x1, x1, #0xc, #0x14
    // 0xb76d74: r16 = "cancel_order"
    //     0xb76d74: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb76d78: ldr             x16, [x16, #0x98]
    // 0xb76d7c: stp             x16, x0, [SP]
    // 0xb76d80: mov             x0, x1
    // 0xb76d84: mov             lr, x0
    // 0xb76d88: ldr             lr, [x21, lr, lsl #3]
    // 0xb76d8c: blr             lr
    // 0xb76d90: tbz             w0, #4, #0xb76f0c
    // 0xb76d94: ldur            x2, [fp, #-8]
    // 0xb76d98: LoadField: r0 = r2->field_f
    //     0xb76d98: ldur            w0, [x2, #0xf]
    // 0xb76d9c: DecompressPointer r0
    //     0xb76d9c: add             x0, x0, HEAP, lsl #32
    // 0xb76da0: LoadField: r1 = r0->field_b
    //     0xb76da0: ldur            w1, [x0, #0xb]
    // 0xb76da4: DecompressPointer r1
    //     0xb76da4: add             x1, x1, HEAP, lsl #32
    // 0xb76da8: cmp             w1, NULL
    // 0xb76dac: b.eq            #0xb7748c
    // 0xb76db0: LoadField: r0 = r1->field_b
    //     0xb76db0: ldur            w0, [x1, #0xb]
    // 0xb76db4: DecompressPointer r0
    //     0xb76db4: add             x0, x0, HEAP, lsl #32
    // 0xb76db8: LoadField: r3 = r0->field_57
    //     0xb76db8: ldur            w3, [x0, #0x57]
    // 0xb76dbc: DecompressPointer r3
    //     0xb76dbc: add             x3, x3, HEAP, lsl #32
    // 0xb76dc0: cmp             w3, NULL
    // 0xb76dc4: b.ne            #0xb76dd4
    // 0xb76dc8: ldr             x4, [fp, #0x10]
    // 0xb76dcc: r0 = Null
    //     0xb76dcc: mov             x0, NULL
    // 0xb76dd0: b               #0xb76e28
    // 0xb76dd4: ldr             x4, [fp, #0x10]
    // 0xb76dd8: LoadField: r0 = r3->field_b
    //     0xb76dd8: ldur            w0, [x3, #0xb]
    // 0xb76ddc: r5 = LoadInt32Instr(r4)
    //     0xb76ddc: sbfx            x5, x4, #1, #0x1f
    //     0xb76de0: tbz             w4, #0, #0xb76de8
    //     0xb76de4: ldur            x5, [x4, #7]
    // 0xb76de8: r1 = LoadInt32Instr(r0)
    //     0xb76de8: sbfx            x1, x0, #1, #0x1f
    // 0xb76dec: mov             x0, x1
    // 0xb76df0: mov             x1, x5
    // 0xb76df4: cmp             x1, x0
    // 0xb76df8: b.hs            #0xb77490
    // 0xb76dfc: LoadField: r0 = r3->field_f
    //     0xb76dfc: ldur            w0, [x3, #0xf]
    // 0xb76e00: DecompressPointer r0
    //     0xb76e00: add             x0, x0, HEAP, lsl #32
    // 0xb76e04: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb76e04: add             x16, x0, x5, lsl #2
    //     0xb76e08: ldur            w1, [x16, #0xf]
    // 0xb76e0c: DecompressPointer r1
    //     0xb76e0c: add             x1, x1, HEAP, lsl #32
    // 0xb76e10: cmp             w1, NULL
    // 0xb76e14: b.ne            #0xb76e20
    // 0xb76e18: r0 = Null
    //     0xb76e18: mov             x0, NULL
    // 0xb76e1c: b               #0xb76e28
    // 0xb76e20: LoadField: r0 = r1->field_7
    //     0xb76e20: ldur            w0, [x1, #7]
    // 0xb76e24: DecompressPointer r0
    //     0xb76e24: add             x0, x0, HEAP, lsl #32
    // 0xb76e28: r1 = LoadClassIdInstr(r0)
    //     0xb76e28: ldur            x1, [x0, #-1]
    //     0xb76e2c: ubfx            x1, x1, #0xc, #0x14
    // 0xb76e30: r16 = "exchange_cancel"
    //     0xb76e30: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xb76e34: ldr             x16, [x16, #0xdf8]
    // 0xb76e38: stp             x16, x0, [SP]
    // 0xb76e3c: mov             x0, x1
    // 0xb76e40: mov             lr, x0
    // 0xb76e44: ldr             lr, [x21, lr, lsl #3]
    // 0xb76e48: blr             lr
    // 0xb76e4c: tbz             w0, #4, #0xb76f0c
    // 0xb76e50: ldur            x2, [fp, #-8]
    // 0xb76e54: LoadField: r0 = r2->field_f
    //     0xb76e54: ldur            w0, [x2, #0xf]
    // 0xb76e58: DecompressPointer r0
    //     0xb76e58: add             x0, x0, HEAP, lsl #32
    // 0xb76e5c: LoadField: r1 = r0->field_b
    //     0xb76e5c: ldur            w1, [x0, #0xb]
    // 0xb76e60: DecompressPointer r1
    //     0xb76e60: add             x1, x1, HEAP, lsl #32
    // 0xb76e64: cmp             w1, NULL
    // 0xb76e68: b.eq            #0xb77494
    // 0xb76e6c: LoadField: r0 = r1->field_b
    //     0xb76e6c: ldur            w0, [x1, #0xb]
    // 0xb76e70: DecompressPointer r0
    //     0xb76e70: add             x0, x0, HEAP, lsl #32
    // 0xb76e74: LoadField: r3 = r0->field_57
    //     0xb76e74: ldur            w3, [x0, #0x57]
    // 0xb76e78: DecompressPointer r3
    //     0xb76e78: add             x3, x3, HEAP, lsl #32
    // 0xb76e7c: cmp             w3, NULL
    // 0xb76e80: b.ne            #0xb76e90
    // 0xb76e84: ldr             x4, [fp, #0x10]
    // 0xb76e88: r0 = Null
    //     0xb76e88: mov             x0, NULL
    // 0xb76e8c: b               #0xb76ee4
    // 0xb76e90: ldr             x4, [fp, #0x10]
    // 0xb76e94: LoadField: r0 = r3->field_b
    //     0xb76e94: ldur            w0, [x3, #0xb]
    // 0xb76e98: r5 = LoadInt32Instr(r4)
    //     0xb76e98: sbfx            x5, x4, #1, #0x1f
    //     0xb76e9c: tbz             w4, #0, #0xb76ea4
    //     0xb76ea0: ldur            x5, [x4, #7]
    // 0xb76ea4: r1 = LoadInt32Instr(r0)
    //     0xb76ea4: sbfx            x1, x0, #1, #0x1f
    // 0xb76ea8: mov             x0, x1
    // 0xb76eac: mov             x1, x5
    // 0xb76eb0: cmp             x1, x0
    // 0xb76eb4: b.hs            #0xb77498
    // 0xb76eb8: LoadField: r0 = r3->field_f
    //     0xb76eb8: ldur            w0, [x3, #0xf]
    // 0xb76ebc: DecompressPointer r0
    //     0xb76ebc: add             x0, x0, HEAP, lsl #32
    // 0xb76ec0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb76ec0: add             x16, x0, x5, lsl #2
    //     0xb76ec4: ldur            w1, [x16, #0xf]
    // 0xb76ec8: DecompressPointer r1
    //     0xb76ec8: add             x1, x1, HEAP, lsl #32
    // 0xb76ecc: cmp             w1, NULL
    // 0xb76ed0: b.ne            #0xb76edc
    // 0xb76ed4: r0 = Null
    //     0xb76ed4: mov             x0, NULL
    // 0xb76ed8: b               #0xb76ee4
    // 0xb76edc: LoadField: r0 = r1->field_7
    //     0xb76edc: ldur            w0, [x1, #7]
    // 0xb76ee0: DecompressPointer r0
    //     0xb76ee0: add             x0, x0, HEAP, lsl #32
    // 0xb76ee4: r1 = LoadClassIdInstr(r0)
    //     0xb76ee4: ldur            x1, [x0, #-1]
    //     0xb76ee8: ubfx            x1, x1, #0xc, #0x14
    // 0xb76eec: r16 = "cancel_return"
    //     0xb76eec: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xb76ef0: ldr             x16, [x16, #0xdf0]
    // 0xb76ef4: stp             x16, x0, [SP]
    // 0xb76ef8: mov             x0, x1
    // 0xb76efc: mov             lr, x0
    // 0xb76f00: ldr             lr, [x21, lr, lsl #3]
    // 0xb76f04: blr             lr
    // 0xb76f08: tbnz            w0, #4, #0xb77018
    // 0xb76f0c: ldur            x0, [fp, #-0x18]
    // 0xb76f10: ldr             x1, [fp, #0x18]
    // 0xb76f14: r0 = of()
    //     0xb76f14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb76f18: LoadField: r1 = r0->field_5b
    //     0xb76f18: ldur            w1, [x0, #0x5b]
    // 0xb76f1c: DecompressPointer r1
    //     0xb76f1c: add             x1, x1, HEAP, lsl #32
    // 0xb76f20: stur            x1, [fp, #-0x20]
    // 0xb76f24: r0 = ColorFilter()
    //     0xb76f24: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb76f28: mov             x1, x0
    // 0xb76f2c: ldur            x0, [fp, #-0x20]
    // 0xb76f30: stur            x1, [fp, #-0x28]
    // 0xb76f34: StoreField: r1->field_7 = r0
    //     0xb76f34: stur            w0, [x1, #7]
    // 0xb76f38: r2 = Instance_BlendMode
    //     0xb76f38: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb76f3c: ldr             x2, [x2, #0xb30]
    // 0xb76f40: StoreField: r1->field_b = r2
    //     0xb76f40: stur            w2, [x1, #0xb]
    // 0xb76f44: r3 = 1
    //     0xb76f44: movz            x3, #0x1
    // 0xb76f48: StoreField: r1->field_13 = r3
    //     0xb76f48: stur            x3, [x1, #0x13]
    // 0xb76f4c: r0 = SvgPicture()
    //     0xb76f4c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb76f50: stur            x0, [fp, #-0x20]
    // 0xb76f54: r16 = 16.000000
    //     0xb76f54: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb76f58: ldr             x16, [x16, #0x188]
    // 0xb76f5c: r30 = 16.000000
    //     0xb76f5c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb76f60: ldr             lr, [lr, #0x188]
    // 0xb76f64: stp             lr, x16, [SP, #0x10]
    // 0xb76f68: r16 = Instance_BoxFit
    //     0xb76f68: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb76f6c: ldr             x16, [x16, #0xb18]
    // 0xb76f70: ldur            lr, [fp, #-0x28]
    // 0xb76f74: stp             lr, x16, [SP]
    // 0xb76f78: mov             x1, x0
    // 0xb76f7c: r2 = "assets/images/x-circle.svg"
    //     0xb76f7c: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a230] "assets/images/x-circle.svg"
    //     0xb76f80: ldr             x2, [x2, #0x230]
    // 0xb76f84: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb76f84: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb76f88: ldr             x4, [x4, #0x238]
    // 0xb76f8c: r0 = SvgPicture.asset()
    //     0xb76f8c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb76f90: ldur            x0, [fp, #-0x18]
    // 0xb76f94: LoadField: r1 = r0->field_b
    //     0xb76f94: ldur            w1, [x0, #0xb]
    // 0xb76f98: LoadField: r2 = r0->field_f
    //     0xb76f98: ldur            w2, [x0, #0xf]
    // 0xb76f9c: DecompressPointer r2
    //     0xb76f9c: add             x2, x2, HEAP, lsl #32
    // 0xb76fa0: LoadField: r3 = r2->field_b
    //     0xb76fa0: ldur            w3, [x2, #0xb]
    // 0xb76fa4: r2 = LoadInt32Instr(r1)
    //     0xb76fa4: sbfx            x2, x1, #1, #0x1f
    // 0xb76fa8: stur            x2, [fp, #-0x30]
    // 0xb76fac: r1 = LoadInt32Instr(r3)
    //     0xb76fac: sbfx            x1, x3, #1, #0x1f
    // 0xb76fb0: cmp             x2, x1
    // 0xb76fb4: b.ne            #0xb76fc0
    // 0xb76fb8: mov             x1, x0
    // 0xb76fbc: r0 = _growToNextCapacity()
    //     0xb76fbc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb76fc0: ldur            x4, [fp, #-0x18]
    // 0xb76fc4: ldur            x2, [fp, #-0x30]
    // 0xb76fc8: add             x3, x2, #1
    // 0xb76fcc: lsl             x0, x3, #1
    // 0xb76fd0: StoreField: r4->field_b = r0
    //     0xb76fd0: stur            w0, [x4, #0xb]
    // 0xb76fd4: LoadField: r5 = r4->field_f
    //     0xb76fd4: ldur            w5, [x4, #0xf]
    // 0xb76fd8: DecompressPointer r5
    //     0xb76fd8: add             x5, x5, HEAP, lsl #32
    // 0xb76fdc: mov             x1, x5
    // 0xb76fe0: ldur            x0, [fp, #-0x20]
    // 0xb76fe4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb76fe4: add             x25, x1, x2, lsl #2
    //     0xb76fe8: add             x25, x25, #0xf
    //     0xb76fec: str             w0, [x25]
    //     0xb76ff0: tbz             w0, #0, #0xb7700c
    //     0xb76ff4: ldurb           w16, [x1, #-1]
    //     0xb76ff8: ldurb           w17, [x0, #-1]
    //     0xb76ffc: and             x16, x17, x16, lsr #2
    //     0xb77000: tst             x16, HEAP, lsr #32
    //     0xb77004: b.eq            #0xb7700c
    //     0xb77008: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7700c: mov             x0, x5
    // 0xb77010: mov             x2, x4
    // 0xb77014: b               #0xb771fc
    // 0xb77018: ldur            x5, [fp, #-8]
    // 0xb7701c: ldur            x4, [fp, #-0x18]
    // 0xb77020: r2 = Instance_BlendMode
    //     0xb77020: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb77024: ldr             x2, [x2, #0xb30]
    // 0xb77028: r3 = 1
    //     0xb77028: movz            x3, #0x1
    // 0xb7702c: LoadField: r0 = r5->field_f
    //     0xb7702c: ldur            w0, [x5, #0xf]
    // 0xb77030: DecompressPointer r0
    //     0xb77030: add             x0, x0, HEAP, lsl #32
    // 0xb77034: LoadField: r1 = r0->field_b
    //     0xb77034: ldur            w1, [x0, #0xb]
    // 0xb77038: DecompressPointer r1
    //     0xb77038: add             x1, x1, HEAP, lsl #32
    // 0xb7703c: cmp             w1, NULL
    // 0xb77040: b.eq            #0xb7749c
    // 0xb77044: LoadField: r0 = r1->field_b
    //     0xb77044: ldur            w0, [x1, #0xb]
    // 0xb77048: DecompressPointer r0
    //     0xb77048: add             x0, x0, HEAP, lsl #32
    // 0xb7704c: LoadField: r6 = r0->field_57
    //     0xb7704c: ldur            w6, [x0, #0x57]
    // 0xb77050: DecompressPointer r6
    //     0xb77050: add             x6, x6, HEAP, lsl #32
    // 0xb77054: cmp             w6, NULL
    // 0xb77058: b.ne            #0xb77068
    // 0xb7705c: ldr             x7, [fp, #0x10]
    // 0xb77060: r0 = Null
    //     0xb77060: mov             x0, NULL
    // 0xb77064: b               #0xb770bc
    // 0xb77068: ldr             x7, [fp, #0x10]
    // 0xb7706c: LoadField: r0 = r6->field_b
    //     0xb7706c: ldur            w0, [x6, #0xb]
    // 0xb77070: r8 = LoadInt32Instr(r7)
    //     0xb77070: sbfx            x8, x7, #1, #0x1f
    //     0xb77074: tbz             w7, #0, #0xb7707c
    //     0xb77078: ldur            x8, [x7, #7]
    // 0xb7707c: r1 = LoadInt32Instr(r0)
    //     0xb7707c: sbfx            x1, x0, #1, #0x1f
    // 0xb77080: mov             x0, x1
    // 0xb77084: mov             x1, x8
    // 0xb77088: cmp             x1, x0
    // 0xb7708c: b.hs            #0xb774a0
    // 0xb77090: LoadField: r0 = r6->field_f
    //     0xb77090: ldur            w0, [x6, #0xf]
    // 0xb77094: DecompressPointer r0
    //     0xb77094: add             x0, x0, HEAP, lsl #32
    // 0xb77098: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb77098: add             x16, x0, x8, lsl #2
    //     0xb7709c: ldur            w1, [x16, #0xf]
    // 0xb770a0: DecompressPointer r1
    //     0xb770a0: add             x1, x1, HEAP, lsl #32
    // 0xb770a4: cmp             w1, NULL
    // 0xb770a8: b.ne            #0xb770b4
    // 0xb770ac: r0 = Null
    //     0xb770ac: mov             x0, NULL
    // 0xb770b0: b               #0xb770bc
    // 0xb770b4: LoadField: r0 = r1->field_7
    //     0xb770b4: ldur            w0, [x1, #7]
    // 0xb770b8: DecompressPointer r0
    //     0xb770b8: add             x0, x0, HEAP, lsl #32
    // 0xb770bc: r1 = LoadClassIdInstr(r0)
    //     0xb770bc: ldur            x1, [x0, #-1]
    //     0xb770c0: ubfx            x1, x1, #0xc, #0x14
    // 0xb770c4: r16 = "track_order"
    //     0xb770c4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xb770c8: ldr             x16, [x16, #0x80]
    // 0xb770cc: stp             x16, x0, [SP]
    // 0xb770d0: mov             x0, x1
    // 0xb770d4: mov             lr, x0
    // 0xb770d8: ldr             lr, [x21, lr, lsl #3]
    // 0xb770dc: blr             lr
    // 0xb770e0: tbnz            w0, #4, #0xb770f0
    // 0xb770e4: r2 = Instance_Icon
    //     0xb770e4: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a240] Obj!Icon@d663f1
    //     0xb770e8: ldr             x2, [x2, #0x240]
    // 0xb770ec: b               #0xb77174
    // 0xb770f0: ldr             x1, [fp, #0x18]
    // 0xb770f4: r0 = of()
    //     0xb770f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb770f8: LoadField: r1 = r0->field_5b
    //     0xb770f8: ldur            w1, [x0, #0x5b]
    // 0xb770fc: DecompressPointer r1
    //     0xb770fc: add             x1, x1, HEAP, lsl #32
    // 0xb77100: stur            x1, [fp, #-0x20]
    // 0xb77104: r0 = ColorFilter()
    //     0xb77104: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb77108: mov             x1, x0
    // 0xb7710c: ldur            x0, [fp, #-0x20]
    // 0xb77110: stur            x1, [fp, #-0x28]
    // 0xb77114: StoreField: r1->field_7 = r0
    //     0xb77114: stur            w0, [x1, #7]
    // 0xb77118: r0 = Instance_BlendMode
    //     0xb77118: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb7711c: ldr             x0, [x0, #0xb30]
    // 0xb77120: StoreField: r1->field_b = r0
    //     0xb77120: stur            w0, [x1, #0xb]
    // 0xb77124: r0 = 1
    //     0xb77124: movz            x0, #0x1
    // 0xb77128: StoreField: r1->field_13 = r0
    //     0xb77128: stur            x0, [x1, #0x13]
    // 0xb7712c: r0 = SvgPicture()
    //     0xb7712c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb77130: stur            x0, [fp, #-0x20]
    // 0xb77134: r16 = 16.000000
    //     0xb77134: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb77138: ldr             x16, [x16, #0x188]
    // 0xb7713c: r30 = 16.000000
    //     0xb7713c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb77140: ldr             lr, [lr, #0x188]
    // 0xb77144: stp             lr, x16, [SP, #0x10]
    // 0xb77148: r16 = Instance_BoxFit
    //     0xb77148: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7714c: ldr             x16, [x16, #0xb18]
    // 0xb77150: ldur            lr, [fp, #-0x28]
    // 0xb77154: stp             lr, x16, [SP]
    // 0xb77158: mov             x1, x0
    // 0xb7715c: r2 = "assets/images/exchange.svg"
    //     0xb7715c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xb77160: ldr             x2, [x2, #0xca0]
    // 0xb77164: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb77164: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb77168: ldr             x4, [x4, #0x238]
    // 0xb7716c: r0 = SvgPicture.asset()
    //     0xb7716c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb77170: ldur            x2, [fp, #-0x20]
    // 0xb77174: ldur            x0, [fp, #-0x18]
    // 0xb77178: stur            x2, [fp, #-0x20]
    // 0xb7717c: LoadField: r1 = r0->field_b
    //     0xb7717c: ldur            w1, [x0, #0xb]
    // 0xb77180: LoadField: r3 = r0->field_f
    //     0xb77180: ldur            w3, [x0, #0xf]
    // 0xb77184: DecompressPointer r3
    //     0xb77184: add             x3, x3, HEAP, lsl #32
    // 0xb77188: LoadField: r4 = r3->field_b
    //     0xb77188: ldur            w4, [x3, #0xb]
    // 0xb7718c: r3 = LoadInt32Instr(r1)
    //     0xb7718c: sbfx            x3, x1, #1, #0x1f
    // 0xb77190: stur            x3, [fp, #-0x30]
    // 0xb77194: r1 = LoadInt32Instr(r4)
    //     0xb77194: sbfx            x1, x4, #1, #0x1f
    // 0xb77198: cmp             x3, x1
    // 0xb7719c: b.ne            #0xb771a8
    // 0xb771a0: mov             x1, x0
    // 0xb771a4: r0 = _growToNextCapacity()
    //     0xb771a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb771a8: ldur            x2, [fp, #-0x18]
    // 0xb771ac: ldur            x3, [fp, #-0x30]
    // 0xb771b0: add             x4, x3, #1
    // 0xb771b4: lsl             x0, x4, #1
    // 0xb771b8: StoreField: r2->field_b = r0
    //     0xb771b8: stur            w0, [x2, #0xb]
    // 0xb771bc: LoadField: r5 = r2->field_f
    //     0xb771bc: ldur            w5, [x2, #0xf]
    // 0xb771c0: DecompressPointer r5
    //     0xb771c0: add             x5, x5, HEAP, lsl #32
    // 0xb771c4: mov             x1, x5
    // 0xb771c8: ldur            x0, [fp, #-0x20]
    // 0xb771cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb771cc: add             x25, x1, x3, lsl #2
    //     0xb771d0: add             x25, x25, #0xf
    //     0xb771d4: str             w0, [x25]
    //     0xb771d8: tbz             w0, #0, #0xb771f4
    //     0xb771dc: ldurb           w16, [x1, #-1]
    //     0xb771e0: ldurb           w17, [x0, #-1]
    //     0xb771e4: and             x16, x17, x16, lsr #2
    //     0xb771e8: tst             x16, HEAP, lsr #32
    //     0xb771ec: b.eq            #0xb771f4
    //     0xb771f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb771f4: mov             x3, x4
    // 0xb771f8: mov             x0, x5
    // 0xb771fc: stur            x3, [fp, #-0x30]
    // 0xb77200: LoadField: r1 = r0->field_b
    //     0xb77200: ldur            w1, [x0, #0xb]
    // 0xb77204: r0 = LoadInt32Instr(r1)
    //     0xb77204: sbfx            x0, x1, #1, #0x1f
    // 0xb77208: cmp             x3, x0
    // 0xb7720c: b.ne            #0xb77218
    // 0xb77210: mov             x1, x2
    // 0xb77214: r0 = _growToNextCapacity()
    //     0xb77214: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb77218: ldur            x4, [fp, #-8]
    // 0xb7721c: ldur            x2, [fp, #-0x18]
    // 0xb77220: ldur            x3, [fp, #-0x30]
    // 0xb77224: add             x0, x3, #1
    // 0xb77228: lsl             x1, x0, #1
    // 0xb7722c: StoreField: r2->field_b = r1
    //     0xb7722c: stur            w1, [x2, #0xb]
    // 0xb77230: mov             x1, x3
    // 0xb77234: cmp             x1, x0
    // 0xb77238: b.hs            #0xb774a4
    // 0xb7723c: LoadField: r0 = r2->field_f
    //     0xb7723c: ldur            w0, [x2, #0xf]
    // 0xb77240: DecompressPointer r0
    //     0xb77240: add             x0, x0, HEAP, lsl #32
    // 0xb77244: add             x1, x0, x3, lsl #2
    // 0xb77248: r16 = Instance_SizedBox
    //     0xb77248: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb7724c: ldr             x16, [x16, #0x998]
    // 0xb77250: StoreField: r1->field_f = r16
    //     0xb77250: stur            w16, [x1, #0xf]
    // 0xb77254: LoadField: r0 = r4->field_f
    //     0xb77254: ldur            w0, [x4, #0xf]
    // 0xb77258: DecompressPointer r0
    //     0xb77258: add             x0, x0, HEAP, lsl #32
    // 0xb7725c: LoadField: r1 = r0->field_b
    //     0xb7725c: ldur            w1, [x0, #0xb]
    // 0xb77260: DecompressPointer r1
    //     0xb77260: add             x1, x1, HEAP, lsl #32
    // 0xb77264: cmp             w1, NULL
    // 0xb77268: b.eq            #0xb774a8
    // 0xb7726c: LoadField: r0 = r1->field_b
    //     0xb7726c: ldur            w0, [x1, #0xb]
    // 0xb77270: DecompressPointer r0
    //     0xb77270: add             x0, x0, HEAP, lsl #32
    // 0xb77274: LoadField: r3 = r0->field_57
    //     0xb77274: ldur            w3, [x0, #0x57]
    // 0xb77278: DecompressPointer r3
    //     0xb77278: add             x3, x3, HEAP, lsl #32
    // 0xb7727c: cmp             w3, NULL
    // 0xb77280: b.ne            #0xb7728c
    // 0xb77284: r0 = Null
    //     0xb77284: mov             x0, NULL
    // 0xb77288: b               #0xb772dc
    // 0xb7728c: ldr             x0, [fp, #0x10]
    // 0xb77290: LoadField: r1 = r3->field_b
    //     0xb77290: ldur            w1, [x3, #0xb]
    // 0xb77294: r4 = LoadInt32Instr(r0)
    //     0xb77294: sbfx            x4, x0, #1, #0x1f
    //     0xb77298: tbz             w0, #0, #0xb772a0
    //     0xb7729c: ldur            x4, [x0, #7]
    // 0xb772a0: r0 = LoadInt32Instr(r1)
    //     0xb772a0: sbfx            x0, x1, #1, #0x1f
    // 0xb772a4: mov             x1, x4
    // 0xb772a8: cmp             x1, x0
    // 0xb772ac: b.hs            #0xb774ac
    // 0xb772b0: LoadField: r0 = r3->field_f
    //     0xb772b0: ldur            w0, [x3, #0xf]
    // 0xb772b4: DecompressPointer r0
    //     0xb772b4: add             x0, x0, HEAP, lsl #32
    // 0xb772b8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb772b8: add             x16, x0, x4, lsl #2
    //     0xb772bc: ldur            w1, [x16, #0xf]
    // 0xb772c0: DecompressPointer r1
    //     0xb772c0: add             x1, x1, HEAP, lsl #32
    // 0xb772c4: cmp             w1, NULL
    // 0xb772c8: b.ne            #0xb772d4
    // 0xb772cc: r0 = Null
    //     0xb772cc: mov             x0, NULL
    // 0xb772d0: b               #0xb772dc
    // 0xb772d4: LoadField: r0 = r1->field_b
    //     0xb772d4: ldur            w0, [x1, #0xb]
    // 0xb772d8: DecompressPointer r0
    //     0xb772d8: add             x0, x0, HEAP, lsl #32
    // 0xb772dc: cmp             w0, NULL
    // 0xb772e0: b.ne            #0xb772e8
    // 0xb772e4: r0 = ""
    //     0xb772e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb772e8: ldr             x1, [fp, #0x18]
    // 0xb772ec: stur            x0, [fp, #-8]
    // 0xb772f0: r0 = of()
    //     0xb772f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb772f4: LoadField: r1 = r0->field_87
    //     0xb772f4: ldur            w1, [x0, #0x87]
    // 0xb772f8: DecompressPointer r1
    //     0xb772f8: add             x1, x1, HEAP, lsl #32
    // 0xb772fc: LoadField: r0 = r1->field_2b
    //     0xb772fc: ldur            w0, [x1, #0x2b]
    // 0xb77300: DecompressPointer r0
    //     0xb77300: add             x0, x0, HEAP, lsl #32
    // 0xb77304: r16 = 12.000000
    //     0xb77304: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb77308: ldr             x16, [x16, #0x9e8]
    // 0xb7730c: r30 = Instance_Color
    //     0xb7730c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb77310: stp             lr, x16, [SP]
    // 0xb77314: mov             x1, x0
    // 0xb77318: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb77318: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7731c: ldr             x4, [x4, #0xaa0]
    // 0xb77320: r0 = copyWith()
    //     0xb77320: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb77324: stur            x0, [fp, #-0x20]
    // 0xb77328: r0 = Text()
    //     0xb77328: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7732c: mov             x2, x0
    // 0xb77330: ldur            x0, [fp, #-8]
    // 0xb77334: stur            x2, [fp, #-0x28]
    // 0xb77338: StoreField: r2->field_b = r0
    //     0xb77338: stur            w0, [x2, #0xb]
    // 0xb7733c: ldur            x0, [fp, #-0x20]
    // 0xb77340: StoreField: r2->field_13 = r0
    //     0xb77340: stur            w0, [x2, #0x13]
    // 0xb77344: ldur            x0, [fp, #-0x18]
    // 0xb77348: LoadField: r1 = r0->field_b
    //     0xb77348: ldur            w1, [x0, #0xb]
    // 0xb7734c: LoadField: r3 = r0->field_f
    //     0xb7734c: ldur            w3, [x0, #0xf]
    // 0xb77350: DecompressPointer r3
    //     0xb77350: add             x3, x3, HEAP, lsl #32
    // 0xb77354: LoadField: r4 = r3->field_b
    //     0xb77354: ldur            w4, [x3, #0xb]
    // 0xb77358: r3 = LoadInt32Instr(r1)
    //     0xb77358: sbfx            x3, x1, #1, #0x1f
    // 0xb7735c: stur            x3, [fp, #-0x30]
    // 0xb77360: r1 = LoadInt32Instr(r4)
    //     0xb77360: sbfx            x1, x4, #1, #0x1f
    // 0xb77364: cmp             x3, x1
    // 0xb77368: b.ne            #0xb77374
    // 0xb7736c: mov             x1, x0
    // 0xb77370: r0 = _growToNextCapacity()
    //     0xb77370: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb77374: ldur            x2, [fp, #-0x18]
    // 0xb77378: ldur            x3, [fp, #-0x30]
    // 0xb7737c: add             x0, x3, #1
    // 0xb77380: lsl             x1, x0, #1
    // 0xb77384: StoreField: r2->field_b = r1
    //     0xb77384: stur            w1, [x2, #0xb]
    // 0xb77388: LoadField: r1 = r2->field_f
    //     0xb77388: ldur            w1, [x2, #0xf]
    // 0xb7738c: DecompressPointer r1
    //     0xb7738c: add             x1, x1, HEAP, lsl #32
    // 0xb77390: ldur            x0, [fp, #-0x28]
    // 0xb77394: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb77394: add             x25, x1, x3, lsl #2
    //     0xb77398: add             x25, x25, #0xf
    //     0xb7739c: str             w0, [x25]
    //     0xb773a0: tbz             w0, #0, #0xb773bc
    //     0xb773a4: ldurb           w16, [x1, #-1]
    //     0xb773a8: ldurb           w17, [x0, #-1]
    //     0xb773ac: and             x16, x17, x16, lsr #2
    //     0xb773b0: tst             x16, HEAP, lsr #32
    //     0xb773b4: b.eq            #0xb773bc
    //     0xb773b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb773bc: r0 = Row()
    //     0xb773bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb773c0: mov             x1, x0
    // 0xb773c4: r0 = Instance_Axis
    //     0xb773c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb773c8: stur            x1, [fp, #-8]
    // 0xb773cc: StoreField: r1->field_f = r0
    //     0xb773cc: stur            w0, [x1, #0xf]
    // 0xb773d0: r0 = Instance_MainAxisAlignment
    //     0xb773d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb773d4: ldr             x0, [x0, #0xa08]
    // 0xb773d8: StoreField: r1->field_13 = r0
    //     0xb773d8: stur            w0, [x1, #0x13]
    // 0xb773dc: r0 = Instance_MainAxisSize
    //     0xb773dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb773e0: ldr             x0, [x0, #0xa10]
    // 0xb773e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb773e4: stur            w0, [x1, #0x17]
    // 0xb773e8: r0 = Instance_CrossAxisAlignment
    //     0xb773e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb773ec: ldr             x0, [x0, #0xa18]
    // 0xb773f0: StoreField: r1->field_1b = r0
    //     0xb773f0: stur            w0, [x1, #0x1b]
    // 0xb773f4: r0 = Instance_VerticalDirection
    //     0xb773f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb773f8: ldr             x0, [x0, #0xa20]
    // 0xb773fc: StoreField: r1->field_23 = r0
    //     0xb773fc: stur            w0, [x1, #0x23]
    // 0xb77400: r0 = Instance_Clip
    //     0xb77400: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb77404: ldr             x0, [x0, #0x38]
    // 0xb77408: StoreField: r1->field_2b = r0
    //     0xb77408: stur            w0, [x1, #0x2b]
    // 0xb7740c: StoreField: r1->field_2f = rZR
    //     0xb7740c: stur            xzr, [x1, #0x2f]
    // 0xb77410: ldur            x0, [fp, #-0x18]
    // 0xb77414: StoreField: r1->field_b = r0
    //     0xb77414: stur            w0, [x1, #0xb]
    // 0xb77418: r0 = InkWell()
    //     0xb77418: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb7741c: mov             x3, x0
    // 0xb77420: ldur            x0, [fp, #-8]
    // 0xb77424: stur            x3, [fp, #-0x18]
    // 0xb77428: StoreField: r3->field_b = r0
    //     0xb77428: stur            w0, [x3, #0xb]
    // 0xb7742c: ldur            x2, [fp, #-0x10]
    // 0xb77430: r1 = Function '<anonymous closure>':.
    //     0xb77430: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a828] AnonymousClosure: (0xb774b0), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb77434: ldr             x1, [x1, #0x828]
    // 0xb77438: r0 = AllocateClosure()
    //     0xb77438: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7743c: mov             x1, x0
    // 0xb77440: ldur            x0, [fp, #-0x18]
    // 0xb77444: StoreField: r0->field_f = r1
    //     0xb77444: stur            w1, [x0, #0xf]
    // 0xb77448: r1 = true
    //     0xb77448: add             x1, NULL, #0x20  ; true
    // 0xb7744c: StoreField: r0->field_43 = r1
    //     0xb7744c: stur            w1, [x0, #0x43]
    // 0xb77450: r2 = Instance_BoxShape
    //     0xb77450: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb77454: ldr             x2, [x2, #0x80]
    // 0xb77458: StoreField: r0->field_47 = r2
    //     0xb77458: stur            w2, [x0, #0x47]
    // 0xb7745c: StoreField: r0->field_6f = r1
    //     0xb7745c: stur            w1, [x0, #0x6f]
    // 0xb77460: r2 = false
    //     0xb77460: add             x2, NULL, #0x30  ; false
    // 0xb77464: StoreField: r0->field_73 = r2
    //     0xb77464: stur            w2, [x0, #0x73]
    // 0xb77468: StoreField: r0->field_83 = r1
    //     0xb77468: stur            w1, [x0, #0x83]
    // 0xb7746c: StoreField: r0->field_7b = r2
    //     0xb7746c: stur            w2, [x0, #0x7b]
    // 0xb77470: LeaveFrame
    //     0xb77470: mov             SP, fp
    //     0xb77474: ldp             fp, lr, [SP], #0x10
    // 0xb77478: ret
    //     0xb77478: ret             
    // 0xb7747c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7747c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77480: b               #0xb76c9c
    // 0xb77484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77488: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77488: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb7748c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7748c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77490: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77490: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77494: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77498: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77498: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb7749c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7749c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb774a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb774a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb774a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb774a4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb774a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb774a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb774ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb774ac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb774b0, size: 0x68c
    // 0xb774b0: EnterFrame
    //     0xb774b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb774b4: mov             fp, SP
    // 0xb774b8: AllocStack(0x60)
    //     0xb774b8: sub             SP, SP, #0x60
    // 0xb774bc: SetupParameters()
    //     0xb774bc: ldr             x0, [fp, #0x10]
    //     0xb774c0: ldur            w2, [x0, #0x17]
    //     0xb774c4: add             x2, x2, HEAP, lsl #32
    //     0xb774c8: stur            x2, [fp, #-0x10]
    // 0xb774cc: CheckStackOverflow
    //     0xb774cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb774d0: cmp             SP, x16
    //     0xb774d4: b.ls            #0xb77af8
    // 0xb774d8: LoadField: r3 = r2->field_b
    //     0xb774d8: ldur            w3, [x2, #0xb]
    // 0xb774dc: DecompressPointer r3
    //     0xb774dc: add             x3, x3, HEAP, lsl #32
    // 0xb774e0: stur            x3, [fp, #-8]
    // 0xb774e4: LoadField: r0 = r3->field_f
    //     0xb774e4: ldur            w0, [x3, #0xf]
    // 0xb774e8: DecompressPointer r0
    //     0xb774e8: add             x0, x0, HEAP, lsl #32
    // 0xb774ec: LoadField: r1 = r0->field_b
    //     0xb774ec: ldur            w1, [x0, #0xb]
    // 0xb774f0: DecompressPointer r1
    //     0xb774f0: add             x1, x1, HEAP, lsl #32
    // 0xb774f4: cmp             w1, NULL
    // 0xb774f8: b.eq            #0xb77b00
    // 0xb774fc: LoadField: r0 = r1->field_b
    //     0xb774fc: ldur            w0, [x1, #0xb]
    // 0xb77500: DecompressPointer r0
    //     0xb77500: add             x0, x0, HEAP, lsl #32
    // 0xb77504: LoadField: r4 = r0->field_57
    //     0xb77504: ldur            w4, [x0, #0x57]
    // 0xb77508: DecompressPointer r4
    //     0xb77508: add             x4, x4, HEAP, lsl #32
    // 0xb7750c: cmp             w4, NULL
    // 0xb77510: b.ne            #0xb7751c
    // 0xb77514: r0 = Null
    //     0xb77514: mov             x0, NULL
    // 0xb77518: b               #0xb77570
    // 0xb7751c: LoadField: r0 = r2->field_13
    //     0xb7751c: ldur            w0, [x2, #0x13]
    // 0xb77520: DecompressPointer r0
    //     0xb77520: add             x0, x0, HEAP, lsl #32
    // 0xb77524: LoadField: r1 = r4->field_b
    //     0xb77524: ldur            w1, [x4, #0xb]
    // 0xb77528: r5 = LoadInt32Instr(r0)
    //     0xb77528: sbfx            x5, x0, #1, #0x1f
    //     0xb7752c: tbz             w0, #0, #0xb77534
    //     0xb77530: ldur            x5, [x0, #7]
    // 0xb77534: r0 = LoadInt32Instr(r1)
    //     0xb77534: sbfx            x0, x1, #1, #0x1f
    // 0xb77538: mov             x1, x5
    // 0xb7753c: cmp             x1, x0
    // 0xb77540: b.hs            #0xb77b04
    // 0xb77544: LoadField: r0 = r4->field_f
    //     0xb77544: ldur            w0, [x4, #0xf]
    // 0xb77548: DecompressPointer r0
    //     0xb77548: add             x0, x0, HEAP, lsl #32
    // 0xb7754c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb7754c: add             x16, x0, x5, lsl #2
    //     0xb77550: ldur            w1, [x16, #0xf]
    // 0xb77554: DecompressPointer r1
    //     0xb77554: add             x1, x1, HEAP, lsl #32
    // 0xb77558: cmp             w1, NULL
    // 0xb7755c: b.ne            #0xb77568
    // 0xb77560: r0 = Null
    //     0xb77560: mov             x0, NULL
    // 0xb77564: b               #0xb77570
    // 0xb77568: LoadField: r0 = r1->field_7
    //     0xb77568: ldur            w0, [x1, #7]
    // 0xb7756c: DecompressPointer r0
    //     0xb7756c: add             x0, x0, HEAP, lsl #32
    // 0xb77570: r1 = LoadClassIdInstr(r0)
    //     0xb77570: ldur            x1, [x0, #-1]
    //     0xb77574: ubfx            x1, x1, #0xc, #0x14
    // 0xb77578: r16 = "cancel_order"
    //     0xb77578: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb7757c: ldr             x16, [x16, #0x98]
    // 0xb77580: stp             x16, x0, [SP]
    // 0xb77584: mov             x0, x1
    // 0xb77588: mov             lr, x0
    // 0xb7758c: ldr             lr, [x21, lr, lsl #3]
    // 0xb77590: blr             lr
    // 0xb77594: tbnz            w0, #4, #0xb77694
    // 0xb77598: ldur            x2, [fp, #-8]
    // 0xb7759c: LoadField: r0 = r2->field_f
    //     0xb7759c: ldur            w0, [x2, #0xf]
    // 0xb775a0: DecompressPointer r0
    //     0xb775a0: add             x0, x0, HEAP, lsl #32
    // 0xb775a4: LoadField: r1 = r0->field_b
    //     0xb775a4: ldur            w1, [x0, #0xb]
    // 0xb775a8: DecompressPointer r1
    //     0xb775a8: add             x1, x1, HEAP, lsl #32
    // 0xb775ac: cmp             w1, NULL
    // 0xb775b0: b.eq            #0xb77b08
    // 0xb775b4: LoadField: r0 = r1->field_b
    //     0xb775b4: ldur            w0, [x1, #0xb]
    // 0xb775b8: DecompressPointer r0
    //     0xb775b8: add             x0, x0, HEAP, lsl #32
    // 0xb775bc: LoadField: r2 = r0->field_7f
    //     0xb775bc: ldur            w2, [x0, #0x7f]
    // 0xb775c0: DecompressPointer r2
    //     0xb775c0: add             x2, x2, HEAP, lsl #32
    // 0xb775c4: cmp             w2, NULL
    // 0xb775c8: b.ne            #0xb775d4
    // 0xb775cc: r0 = Null
    //     0xb775cc: mov             x0, NULL
    // 0xb775d0: b               #0xb775dc
    // 0xb775d4: LoadField: r0 = r2->field_2b
    //     0xb775d4: ldur            w0, [x2, #0x2b]
    // 0xb775d8: DecompressPointer r0
    //     0xb775d8: add             x0, x0, HEAP, lsl #32
    // 0xb775dc: cmp             w0, NULL
    // 0xb775e0: b.ne            #0xb775ec
    // 0xb775e4: ldur            x3, [fp, #-0x10]
    // 0xb775e8: b               #0xb7763c
    // 0xb775ec: tbnz            w0, #4, #0xb77638
    // 0xb775f0: ldur            x3, [fp, #-0x10]
    // 0xb775f4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb775f4: ldur            w0, [x1, #0x17]
    // 0xb775f8: DecompressPointer r0
    //     0xb775f8: add             x0, x0, HEAP, lsl #32
    // 0xb775fc: LoadField: r2 = r1->field_b
    //     0xb775fc: ldur            w2, [x1, #0xb]
    // 0xb77600: DecompressPointer r2
    //     0xb77600: add             x2, x2, HEAP, lsl #32
    // 0xb77604: LoadField: r1 = r2->field_7
    //     0xb77604: ldur            w1, [x2, #7]
    // 0xb77608: DecompressPointer r1
    //     0xb77608: add             x1, x1, HEAP, lsl #32
    // 0xb7760c: LoadField: r4 = r3->field_f
    //     0xb7760c: ldur            w4, [x3, #0xf]
    // 0xb77610: DecompressPointer r4
    //     0xb77610: add             x4, x4, HEAP, lsl #32
    // 0xb77614: stp             x2, x0, [SP, #0x10]
    // 0xb77618: stp             x4, x1, [SP]
    // 0xb7761c: r4 = 0
    //     0xb7761c: movz            x4, #0
    // 0xb77620: ldr             x0, [SP, #0x18]
    // 0xb77624: r16 = UnlinkedCall_0x613b5c
    //     0xb77624: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a830] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77628: add             x16, x16, #0x830
    // 0xb7762c: ldp             x5, lr, [x16]
    // 0xb77630: blr             lr
    // 0xb77634: b               #0xb77ae8
    // 0xb77638: ldur            x3, [fp, #-0x10]
    // 0xb7763c: LoadField: r0 = r1->field_13
    //     0xb7763c: ldur            w0, [x1, #0x13]
    // 0xb77640: DecompressPointer r0
    //     0xb77640: add             x0, x0, HEAP, lsl #32
    // 0xb77644: LoadField: r2 = r1->field_b
    //     0xb77644: ldur            w2, [x1, #0xb]
    // 0xb77648: DecompressPointer r2
    //     0xb77648: add             x2, x2, HEAP, lsl #32
    // 0xb7764c: LoadField: r1 = r2->field_37
    //     0xb7764c: ldur            w1, [x2, #0x37]
    // 0xb77650: DecompressPointer r1
    //     0xb77650: add             x1, x1, HEAP, lsl #32
    // 0xb77654: LoadField: r4 = r2->field_7
    //     0xb77654: ldur            w4, [x2, #7]
    // 0xb77658: DecompressPointer r4
    //     0xb77658: add             x4, x4, HEAP, lsl #32
    // 0xb7765c: LoadField: r5 = r3->field_f
    //     0xb7765c: ldur            w5, [x3, #0xf]
    // 0xb77660: DecompressPointer r5
    //     0xb77660: add             x5, x5, HEAP, lsl #32
    // 0xb77664: LoadField: r3 = r2->field_2f
    //     0xb77664: ldur            w3, [x2, #0x2f]
    // 0xb77668: DecompressPointer r3
    //     0xb77668: add             x3, x3, HEAP, lsl #32
    // 0xb7766c: stp             x1, x0, [SP, #0x18]
    // 0xb77670: stp             x5, x4, [SP, #8]
    // 0xb77674: str             x3, [SP]
    // 0xb77678: r4 = 0
    //     0xb77678: movz            x4, #0
    // 0xb7767c: ldr             x0, [SP, #0x20]
    // 0xb77680: r16 = UnlinkedCall_0x613b5c
    //     0xb77680: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a840] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77684: add             x16, x16, #0x840
    // 0xb77688: ldp             x5, lr, [x16]
    // 0xb7768c: blr             lr
    // 0xb77690: b               #0xb77ae8
    // 0xb77694: ldur            x3, [fp, #-0x10]
    // 0xb77698: ldur            x2, [fp, #-8]
    // 0xb7769c: LoadField: r0 = r2->field_f
    //     0xb7769c: ldur            w0, [x2, #0xf]
    // 0xb776a0: DecompressPointer r0
    //     0xb776a0: add             x0, x0, HEAP, lsl #32
    // 0xb776a4: LoadField: r1 = r0->field_b
    //     0xb776a4: ldur            w1, [x0, #0xb]
    // 0xb776a8: DecompressPointer r1
    //     0xb776a8: add             x1, x1, HEAP, lsl #32
    // 0xb776ac: cmp             w1, NULL
    // 0xb776b0: b.eq            #0xb77b0c
    // 0xb776b4: LoadField: r0 = r1->field_b
    //     0xb776b4: ldur            w0, [x1, #0xb]
    // 0xb776b8: DecompressPointer r0
    //     0xb776b8: add             x0, x0, HEAP, lsl #32
    // 0xb776bc: LoadField: r4 = r0->field_57
    //     0xb776bc: ldur            w4, [x0, #0x57]
    // 0xb776c0: DecompressPointer r4
    //     0xb776c0: add             x4, x4, HEAP, lsl #32
    // 0xb776c4: cmp             w4, NULL
    // 0xb776c8: b.ne            #0xb776d4
    // 0xb776cc: r0 = Null
    //     0xb776cc: mov             x0, NULL
    // 0xb776d0: b               #0xb77728
    // 0xb776d4: LoadField: r0 = r3->field_13
    //     0xb776d4: ldur            w0, [x3, #0x13]
    // 0xb776d8: DecompressPointer r0
    //     0xb776d8: add             x0, x0, HEAP, lsl #32
    // 0xb776dc: LoadField: r1 = r4->field_b
    //     0xb776dc: ldur            w1, [x4, #0xb]
    // 0xb776e0: r5 = LoadInt32Instr(r0)
    //     0xb776e0: sbfx            x5, x0, #1, #0x1f
    //     0xb776e4: tbz             w0, #0, #0xb776ec
    //     0xb776e8: ldur            x5, [x0, #7]
    // 0xb776ec: r0 = LoadInt32Instr(r1)
    //     0xb776ec: sbfx            x0, x1, #1, #0x1f
    // 0xb776f0: mov             x1, x5
    // 0xb776f4: cmp             x1, x0
    // 0xb776f8: b.hs            #0xb77b10
    // 0xb776fc: LoadField: r0 = r4->field_f
    //     0xb776fc: ldur            w0, [x4, #0xf]
    // 0xb77700: DecompressPointer r0
    //     0xb77700: add             x0, x0, HEAP, lsl #32
    // 0xb77704: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb77704: add             x16, x0, x5, lsl #2
    //     0xb77708: ldur            w1, [x16, #0xf]
    // 0xb7770c: DecompressPointer r1
    //     0xb7770c: add             x1, x1, HEAP, lsl #32
    // 0xb77710: cmp             w1, NULL
    // 0xb77714: b.ne            #0xb77720
    // 0xb77718: r0 = Null
    //     0xb77718: mov             x0, NULL
    // 0xb7771c: b               #0xb77728
    // 0xb77720: LoadField: r0 = r1->field_7
    //     0xb77720: ldur            w0, [x1, #7]
    // 0xb77724: DecompressPointer r0
    //     0xb77724: add             x0, x0, HEAP, lsl #32
    // 0xb77728: r1 = LoadClassIdInstr(r0)
    //     0xb77728: ldur            x1, [x0, #-1]
    //     0xb7772c: ubfx            x1, x1, #0xc, #0x14
    // 0xb77730: r16 = "exchange_cancel"
    //     0xb77730: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xb77734: ldr             x16, [x16, #0xdf8]
    // 0xb77738: stp             x16, x0, [SP]
    // 0xb7773c: mov             x0, x1
    // 0xb77740: mov             lr, x0
    // 0xb77744: ldr             lr, [x21, lr, lsl #3]
    // 0xb77748: blr             lr
    // 0xb7774c: tbnz            w0, #4, #0xb77870
    // 0xb77750: ldur            x3, [fp, #-0x10]
    // 0xb77754: ldur            x2, [fp, #-8]
    // 0xb77758: LoadField: r0 = r2->field_f
    //     0xb77758: ldur            w0, [x2, #0xf]
    // 0xb7775c: DecompressPointer r0
    //     0xb7775c: add             x0, x0, HEAP, lsl #32
    // 0xb77760: LoadField: r2 = r0->field_b
    //     0xb77760: ldur            w2, [x0, #0xb]
    // 0xb77764: DecompressPointer r2
    //     0xb77764: add             x2, x2, HEAP, lsl #32
    // 0xb77768: cmp             w2, NULL
    // 0xb7776c: b.eq            #0xb77b14
    // 0xb77770: LoadField: r4 = r2->field_1f
    //     0xb77770: ldur            w4, [x2, #0x1f]
    // 0xb77774: DecompressPointer r4
    //     0xb77774: add             x4, x4, HEAP, lsl #32
    // 0xb77778: LoadField: r5 = r3->field_f
    //     0xb77778: ldur            w5, [x3, #0xf]
    // 0xb7777c: DecompressPointer r5
    //     0xb7777c: add             x5, x5, HEAP, lsl #32
    // 0xb77780: LoadField: r0 = r2->field_b
    //     0xb77780: ldur            w0, [x2, #0xb]
    // 0xb77784: DecompressPointer r0
    //     0xb77784: add             x0, x0, HEAP, lsl #32
    // 0xb77788: LoadField: r6 = r0->field_63
    //     0xb77788: ldur            w6, [x0, #0x63]
    // 0xb7778c: DecompressPointer r6
    //     0xb7778c: add             x6, x6, HEAP, lsl #32
    // 0xb77790: LoadField: r7 = r0->field_67
    //     0xb77790: ldur            w7, [x0, #0x67]
    // 0xb77794: DecompressPointer r7
    //     0xb77794: add             x7, x7, HEAP, lsl #32
    // 0xb77798: LoadField: r8 = r0->field_73
    //     0xb77798: ldur            w8, [x0, #0x73]
    // 0xb7779c: DecompressPointer r8
    //     0xb7779c: add             x8, x8, HEAP, lsl #32
    // 0xb777a0: LoadField: r9 = r0->field_6f
    //     0xb777a0: ldur            w9, [x0, #0x6f]
    // 0xb777a4: DecompressPointer r9
    //     0xb777a4: add             x9, x9, HEAP, lsl #32
    // 0xb777a8: LoadField: r10 = r0->field_6b
    //     0xb777a8: ldur            w10, [x0, #0x6b]
    // 0xb777ac: DecompressPointer r10
    //     0xb777ac: add             x10, x10, HEAP, lsl #32
    // 0xb777b0: LoadField: r11 = r0->field_77
    //     0xb777b0: ldur            w11, [x0, #0x77]
    // 0xb777b4: DecompressPointer r11
    //     0xb777b4: add             x11, x11, HEAP, lsl #32
    // 0xb777b8: LoadField: r12 = r0->field_57
    //     0xb777b8: ldur            w12, [x0, #0x57]
    // 0xb777bc: DecompressPointer r12
    //     0xb777bc: add             x12, x12, HEAP, lsl #32
    // 0xb777c0: cmp             w12, NULL
    // 0xb777c4: b.ne            #0xb777d0
    // 0xb777c8: r0 = Null
    //     0xb777c8: mov             x0, NULL
    // 0xb777cc: b               #0xb77824
    // 0xb777d0: LoadField: r0 = r3->field_13
    //     0xb777d0: ldur            w0, [x3, #0x13]
    // 0xb777d4: DecompressPointer r0
    //     0xb777d4: add             x0, x0, HEAP, lsl #32
    // 0xb777d8: LoadField: r1 = r12->field_b
    //     0xb777d8: ldur            w1, [x12, #0xb]
    // 0xb777dc: r3 = LoadInt32Instr(r0)
    //     0xb777dc: sbfx            x3, x0, #1, #0x1f
    //     0xb777e0: tbz             w0, #0, #0xb777e8
    //     0xb777e4: ldur            x3, [x0, #7]
    // 0xb777e8: r0 = LoadInt32Instr(r1)
    //     0xb777e8: sbfx            x0, x1, #1, #0x1f
    // 0xb777ec: mov             x1, x3
    // 0xb777f0: cmp             x1, x0
    // 0xb777f4: b.hs            #0xb77b18
    // 0xb777f8: LoadField: r0 = r12->field_f
    //     0xb777f8: ldur            w0, [x12, #0xf]
    // 0xb777fc: DecompressPointer r0
    //     0xb777fc: add             x0, x0, HEAP, lsl #32
    // 0xb77800: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb77800: add             x16, x0, x3, lsl #2
    //     0xb77804: ldur            w1, [x16, #0xf]
    // 0xb77808: DecompressPointer r1
    //     0xb77808: add             x1, x1, HEAP, lsl #32
    // 0xb7780c: cmp             w1, NULL
    // 0xb77810: b.ne            #0xb7781c
    // 0xb77814: r0 = Null
    //     0xb77814: mov             x0, NULL
    // 0xb77818: b               #0xb77824
    // 0xb7781c: LoadField: r0 = r1->field_b
    //     0xb7781c: ldur            w0, [x1, #0xb]
    // 0xb77820: DecompressPointer r0
    //     0xb77820: add             x0, x0, HEAP, lsl #32
    // 0xb77824: cmp             w0, NULL
    // 0xb77828: b.ne            #0xb77830
    // 0xb7782c: r0 = ""
    //     0xb7782c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb77830: LoadField: r1 = r2->field_b
    //     0xb77830: ldur            w1, [x2, #0xb]
    // 0xb77834: DecompressPointer r1
    //     0xb77834: add             x1, x1, HEAP, lsl #32
    // 0xb77838: LoadField: r2 = r1->field_7
    //     0xb77838: ldur            w2, [x1, #7]
    // 0xb7783c: DecompressPointer r2
    //     0xb7783c: add             x2, x2, HEAP, lsl #32
    // 0xb77840: stp             x5, x4, [SP, #0x40]
    // 0xb77844: stp             x7, x6, [SP, #0x30]
    // 0xb77848: stp             x9, x8, [SP, #0x20]
    // 0xb7784c: stp             x11, x10, [SP, #0x10]
    // 0xb77850: stp             x2, x0, [SP]
    // 0xb77854: r4 = 0
    //     0xb77854: movz            x4, #0
    // 0xb77858: ldr             x0, [SP, #0x48]
    // 0xb7785c: r16 = UnlinkedCall_0x613b5c
    //     0xb7785c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a850] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77860: add             x16, x16, #0x850
    // 0xb77864: ldp             x5, lr, [x16]
    // 0xb77868: blr             lr
    // 0xb7786c: b               #0xb77ae8
    // 0xb77870: ldur            x3, [fp, #-0x10]
    // 0xb77874: ldur            x2, [fp, #-8]
    // 0xb77878: LoadField: r0 = r2->field_f
    //     0xb77878: ldur            w0, [x2, #0xf]
    // 0xb7787c: DecompressPointer r0
    //     0xb7787c: add             x0, x0, HEAP, lsl #32
    // 0xb77880: LoadField: r1 = r0->field_b
    //     0xb77880: ldur            w1, [x0, #0xb]
    // 0xb77884: DecompressPointer r1
    //     0xb77884: add             x1, x1, HEAP, lsl #32
    // 0xb77888: cmp             w1, NULL
    // 0xb7788c: b.eq            #0xb77b1c
    // 0xb77890: LoadField: r0 = r1->field_b
    //     0xb77890: ldur            w0, [x1, #0xb]
    // 0xb77894: DecompressPointer r0
    //     0xb77894: add             x0, x0, HEAP, lsl #32
    // 0xb77898: LoadField: r4 = r0->field_57
    //     0xb77898: ldur            w4, [x0, #0x57]
    // 0xb7789c: DecompressPointer r4
    //     0xb7789c: add             x4, x4, HEAP, lsl #32
    // 0xb778a0: cmp             w4, NULL
    // 0xb778a4: b.ne            #0xb778b0
    // 0xb778a8: r0 = Null
    //     0xb778a8: mov             x0, NULL
    // 0xb778ac: b               #0xb77904
    // 0xb778b0: LoadField: r0 = r3->field_13
    //     0xb778b0: ldur            w0, [x3, #0x13]
    // 0xb778b4: DecompressPointer r0
    //     0xb778b4: add             x0, x0, HEAP, lsl #32
    // 0xb778b8: LoadField: r1 = r4->field_b
    //     0xb778b8: ldur            w1, [x4, #0xb]
    // 0xb778bc: r5 = LoadInt32Instr(r0)
    //     0xb778bc: sbfx            x5, x0, #1, #0x1f
    //     0xb778c0: tbz             w0, #0, #0xb778c8
    //     0xb778c4: ldur            x5, [x0, #7]
    // 0xb778c8: r0 = LoadInt32Instr(r1)
    //     0xb778c8: sbfx            x0, x1, #1, #0x1f
    // 0xb778cc: mov             x1, x5
    // 0xb778d0: cmp             x1, x0
    // 0xb778d4: b.hs            #0xb77b20
    // 0xb778d8: LoadField: r0 = r4->field_f
    //     0xb778d8: ldur            w0, [x4, #0xf]
    // 0xb778dc: DecompressPointer r0
    //     0xb778dc: add             x0, x0, HEAP, lsl #32
    // 0xb778e0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb778e0: add             x16, x0, x5, lsl #2
    //     0xb778e4: ldur            w1, [x16, #0xf]
    // 0xb778e8: DecompressPointer r1
    //     0xb778e8: add             x1, x1, HEAP, lsl #32
    // 0xb778ec: cmp             w1, NULL
    // 0xb778f0: b.ne            #0xb778fc
    // 0xb778f4: r0 = Null
    //     0xb778f4: mov             x0, NULL
    // 0xb778f8: b               #0xb77904
    // 0xb778fc: LoadField: r0 = r1->field_7
    //     0xb778fc: ldur            w0, [x1, #7]
    // 0xb77900: DecompressPointer r0
    //     0xb77900: add             x0, x0, HEAP, lsl #32
    // 0xb77904: r1 = LoadClassIdInstr(r0)
    //     0xb77904: ldur            x1, [x0, #-1]
    //     0xb77908: ubfx            x1, x1, #0xc, #0x14
    // 0xb7790c: r16 = "cancel_return"
    //     0xb7790c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xb77910: ldr             x16, [x16, #0xdf0]
    // 0xb77914: stp             x16, x0, [SP]
    // 0xb77918: mov             x0, x1
    // 0xb7791c: mov             lr, x0
    // 0xb77920: ldr             lr, [x21, lr, lsl #3]
    // 0xb77924: blr             lr
    // 0xb77928: tbnz            w0, #4, #0xb77998
    // 0xb7792c: ldur            x0, [fp, #-0x10]
    // 0xb77930: ldur            x2, [fp, #-8]
    // 0xb77934: LoadField: r1 = r2->field_f
    //     0xb77934: ldur            w1, [x2, #0xf]
    // 0xb77938: DecompressPointer r1
    //     0xb77938: add             x1, x1, HEAP, lsl #32
    // 0xb7793c: LoadField: r2 = r1->field_b
    //     0xb7793c: ldur            w2, [x1, #0xb]
    // 0xb77940: DecompressPointer r2
    //     0xb77940: add             x2, x2, HEAP, lsl #32
    // 0xb77944: cmp             w2, NULL
    // 0xb77948: b.eq            #0xb77b24
    // 0xb7794c: LoadField: r1 = r2->field_1b
    //     0xb7794c: ldur            w1, [x2, #0x1b]
    // 0xb77950: DecompressPointer r1
    //     0xb77950: add             x1, x1, HEAP, lsl #32
    // 0xb77954: LoadField: r3 = r0->field_f
    //     0xb77954: ldur            w3, [x0, #0xf]
    // 0xb77958: DecompressPointer r3
    //     0xb77958: add             x3, x3, HEAP, lsl #32
    // 0xb7795c: LoadField: r0 = r2->field_b
    //     0xb7795c: ldur            w0, [x2, #0xb]
    // 0xb77960: DecompressPointer r0
    //     0xb77960: add             x0, x0, HEAP, lsl #32
    // 0xb77964: LoadField: r2 = r0->field_5f
    //     0xb77964: ldur            w2, [x0, #0x5f]
    // 0xb77968: DecompressPointer r2
    //     0xb77968: add             x2, x2, HEAP, lsl #32
    // 0xb7796c: LoadField: r4 = r0->field_67
    //     0xb7796c: ldur            w4, [x0, #0x67]
    // 0xb77970: DecompressPointer r4
    //     0xb77970: add             x4, x4, HEAP, lsl #32
    // 0xb77974: stp             x3, x1, [SP, #0x10]
    // 0xb77978: stp             x4, x2, [SP]
    // 0xb7797c: r4 = 0
    //     0xb7797c: movz            x4, #0
    // 0xb77980: ldr             x0, [SP, #0x18]
    // 0xb77984: r16 = UnlinkedCall_0x613b5c
    //     0xb77984: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a860] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77988: add             x16, x16, #0x860
    // 0xb7798c: ldp             x5, lr, [x16]
    // 0xb77990: blr             lr
    // 0xb77994: b               #0xb77ae8
    // 0xb77998: ldur            x0, [fp, #-0x10]
    // 0xb7799c: ldur            x2, [fp, #-8]
    // 0xb779a0: LoadField: r1 = r2->field_f
    //     0xb779a0: ldur            w1, [x2, #0xf]
    // 0xb779a4: DecompressPointer r1
    //     0xb779a4: add             x1, x1, HEAP, lsl #32
    // 0xb779a8: LoadField: r3 = r1->field_b
    //     0xb779a8: ldur            w3, [x1, #0xb]
    // 0xb779ac: DecompressPointer r3
    //     0xb779ac: add             x3, x3, HEAP, lsl #32
    // 0xb779b0: cmp             w3, NULL
    // 0xb779b4: b.eq            #0xb77b28
    // 0xb779b8: LoadField: r1 = r3->field_b
    //     0xb779b8: ldur            w1, [x3, #0xb]
    // 0xb779bc: DecompressPointer r1
    //     0xb779bc: add             x1, x1, HEAP, lsl #32
    // 0xb779c0: LoadField: r3 = r1->field_57
    //     0xb779c0: ldur            w3, [x1, #0x57]
    // 0xb779c4: DecompressPointer r3
    //     0xb779c4: add             x3, x3, HEAP, lsl #32
    // 0xb779c8: cmp             w3, NULL
    // 0xb779cc: b.ne            #0xb779d8
    // 0xb779d0: r0 = Null
    //     0xb779d0: mov             x0, NULL
    // 0xb779d4: b               #0xb77a30
    // 0xb779d8: LoadField: r1 = r0->field_13
    //     0xb779d8: ldur            w1, [x0, #0x13]
    // 0xb779dc: DecompressPointer r1
    //     0xb779dc: add             x1, x1, HEAP, lsl #32
    // 0xb779e0: LoadField: r0 = r3->field_b
    //     0xb779e0: ldur            w0, [x3, #0xb]
    // 0xb779e4: r4 = LoadInt32Instr(r1)
    //     0xb779e4: sbfx            x4, x1, #1, #0x1f
    //     0xb779e8: tbz             w1, #0, #0xb779f0
    //     0xb779ec: ldur            x4, [x1, #7]
    // 0xb779f0: r1 = LoadInt32Instr(r0)
    //     0xb779f0: sbfx            x1, x0, #1, #0x1f
    // 0xb779f4: mov             x0, x1
    // 0xb779f8: mov             x1, x4
    // 0xb779fc: cmp             x1, x0
    // 0xb77a00: b.hs            #0xb77b2c
    // 0xb77a04: LoadField: r0 = r3->field_f
    //     0xb77a04: ldur            w0, [x3, #0xf]
    // 0xb77a08: DecompressPointer r0
    //     0xb77a08: add             x0, x0, HEAP, lsl #32
    // 0xb77a0c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb77a0c: add             x16, x0, x4, lsl #2
    //     0xb77a10: ldur            w1, [x16, #0xf]
    // 0xb77a14: DecompressPointer r1
    //     0xb77a14: add             x1, x1, HEAP, lsl #32
    // 0xb77a18: cmp             w1, NULL
    // 0xb77a1c: b.ne            #0xb77a28
    // 0xb77a20: r0 = Null
    //     0xb77a20: mov             x0, NULL
    // 0xb77a24: b               #0xb77a30
    // 0xb77a28: LoadField: r0 = r1->field_7
    //     0xb77a28: ldur            w0, [x1, #7]
    // 0xb77a2c: DecompressPointer r0
    //     0xb77a2c: add             x0, x0, HEAP, lsl #32
    // 0xb77a30: r1 = LoadClassIdInstr(r0)
    //     0xb77a30: ldur            x1, [x0, #-1]
    //     0xb77a34: ubfx            x1, x1, #0xc, #0x14
    // 0xb77a38: r16 = "track_order"
    //     0xb77a38: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xb77a3c: ldr             x16, [x16, #0x80]
    // 0xb77a40: stp             x16, x0, [SP]
    // 0xb77a44: mov             x0, x1
    // 0xb77a48: mov             lr, x0
    // 0xb77a4c: ldr             lr, [x21, lr, lsl #3]
    // 0xb77a50: blr             lr
    // 0xb77a54: tbnz            w0, #4, #0xb77ac0
    // 0xb77a58: ldur            x0, [fp, #-8]
    // 0xb77a5c: LoadField: r1 = r0->field_f
    //     0xb77a5c: ldur            w1, [x0, #0xf]
    // 0xb77a60: DecompressPointer r1
    //     0xb77a60: add             x1, x1, HEAP, lsl #32
    // 0xb77a64: LoadField: r0 = r1->field_b
    //     0xb77a64: ldur            w0, [x1, #0xb]
    // 0xb77a68: DecompressPointer r0
    //     0xb77a68: add             x0, x0, HEAP, lsl #32
    // 0xb77a6c: cmp             w0, NULL
    // 0xb77a70: b.eq            #0xb77b30
    // 0xb77a74: LoadField: r1 = r0->field_b
    //     0xb77a74: ldur            w1, [x0, #0xb]
    // 0xb77a78: DecompressPointer r1
    //     0xb77a78: add             x1, x1, HEAP, lsl #32
    // 0xb77a7c: LoadField: r2 = r1->field_33
    //     0xb77a7c: ldur            w2, [x1, #0x33]
    // 0xb77a80: DecompressPointer r2
    //     0xb77a80: add             x2, x2, HEAP, lsl #32
    // 0xb77a84: cmp             w2, NULL
    // 0xb77a88: b.eq            #0xb77ae8
    // 0xb77a8c: LoadField: r1 = r0->field_b
    //     0xb77a8c: ldur            w1, [x0, #0xb]
    // 0xb77a90: DecompressPointer r1
    //     0xb77a90: add             x1, x1, HEAP, lsl #32
    // 0xb77a94: LoadField: r0 = r1->field_33
    //     0xb77a94: ldur            w0, [x1, #0x33]
    // 0xb77a98: DecompressPointer r0
    //     0xb77a98: add             x0, x0, HEAP, lsl #32
    // 0xb77a9c: cmp             w0, NULL
    // 0xb77aa0: b.eq            #0xb77b34
    // 0xb77aa4: mov             x1, x0
    // 0xb77aa8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb77aa8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb77aac: r0 = parse()
    //     0xb77aac: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0xb77ab0: mov             x1, x0
    // 0xb77ab4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb77ab4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb77ab8: r0 = launchUrl()
    //     0xb77ab8: bl              #0x85cb94  ; [package:url_launcher/src/url_launcher_uri.dart] ::launchUrl
    // 0xb77abc: b               #0xb77ae8
    // 0xb77ac0: ldur            x0, [fp, #-8]
    // 0xb77ac4: LoadField: r1 = r0->field_f
    //     0xb77ac4: ldur            w1, [x0, #0xf]
    // 0xb77ac8: DecompressPointer r1
    //     0xb77ac8: add             x1, x1, HEAP, lsl #32
    // 0xb77acc: LoadField: r0 = r1->field_b
    //     0xb77acc: ldur            w0, [x1, #0xb]
    // 0xb77ad0: DecompressPointer r0
    //     0xb77ad0: add             x0, x0, HEAP, lsl #32
    // 0xb77ad4: cmp             w0, NULL
    // 0xb77ad8: b.eq            #0xb77b38
    // 0xb77adc: LoadField: r2 = r0->field_b
    //     0xb77adc: ldur            w2, [x0, #0xb]
    // 0xb77ae0: DecompressPointer r2
    //     0xb77ae0: add             x2, x2, HEAP, lsl #32
    // 0xb77ae4: r0 = findProductIds()
    //     0xb77ae4: bl              #0xb77b3c  ; [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::findProductIds
    // 0xb77ae8: r0 = Null
    //     0xb77ae8: mov             x0, NULL
    // 0xb77aec: LeaveFrame
    //     0xb77aec: mov             SP, fp
    //     0xb77af0: ldp             fp, lr, [SP], #0x10
    // 0xb77af4: ret
    //     0xb77af4: ret             
    // 0xb77af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb77af8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77afc: b               #0xb774d8
    // 0xb77b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77b04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77b08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77b10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77b18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77b20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77b24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77b2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77b30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77b38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77b38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findProductIds(/* No info */) {
    // ** addr: 0xb77b3c, size: 0x134
    // 0xb77b3c: EnterFrame
    //     0xb77b3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb77b40: mov             fp, SP
    // 0xb77b44: AllocStack(0x40)
    //     0xb77b44: sub             SP, SP, #0x40
    // 0xb77b48: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xb77b48: mov             x4, x1
    //     0xb77b4c: mov             x3, x2
    //     0xb77b50: stur            x1, [fp, #-8]
    //     0xb77b54: stur            x2, [fp, #-0x10]
    // 0xb77b58: CheckStackOverflow
    //     0xb77b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb77b5c: cmp             SP, x16
    //     0xb77b60: b.ls            #0xb77c5c
    // 0xb77b64: LoadField: r1 = r3->field_2b
    //     0xb77b64: ldur            w1, [x3, #0x2b]
    // 0xb77b68: DecompressPointer r1
    //     0xb77b68: add             x1, x1, HEAP, lsl #32
    // 0xb77b6c: cmp             w1, NULL
    // 0xb77b70: b.ne            #0xb77b7c
    // 0xb77b74: r2 = Null
    //     0xb77b74: mov             x2, NULL
    // 0xb77b78: b               #0xb77b98
    // 0xb77b7c: r0 = LoadClassIdInstr(r1)
    //     0xb77b7c: ldur            x0, [x1, #-1]
    //     0xb77b80: ubfx            x0, x0, #0xc, #0x14
    // 0xb77b84: r2 = "/"
    //     0xb77b84: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0xb77b88: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb77b88: sub             lr, x0, #0xffc
    //     0xb77b8c: ldr             lr, [x21, lr, lsl #3]
    //     0xb77b90: blr             lr
    // 0xb77b94: mov             x2, x0
    // 0xb77b98: cmp             w2, NULL
    // 0xb77b9c: b.eq            #0xb77c4c
    // 0xb77ba0: ldur            x4, [fp, #-8]
    // 0xb77ba4: ldur            x3, [fp, #-0x10]
    // 0xb77ba8: LoadField: r0 = r2->field_b
    //     0xb77ba8: ldur            w0, [x2, #0xb]
    // 0xb77bac: r5 = LoadInt32Instr(r0)
    //     0xb77bac: sbfx            x5, x0, #1, #0x1f
    // 0xb77bb0: sub             x6, x5, #2
    // 0xb77bb4: mov             x0, x5
    // 0xb77bb8: mov             x1, x6
    // 0xb77bbc: cmp             x1, x0
    // 0xb77bc0: b.hs            #0xb77c64
    // 0xb77bc4: LoadField: r7 = r2->field_f
    //     0xb77bc4: ldur            w7, [x2, #0xf]
    // 0xb77bc8: DecompressPointer r7
    //     0xb77bc8: add             x7, x7, HEAP, lsl #32
    // 0xb77bcc: ArrayLoad: r2 = r7[r6]  ; Unknown_4
    //     0xb77bcc: add             x16, x7, x6, lsl #2
    //     0xb77bd0: ldur            w2, [x16, #0xf]
    // 0xb77bd4: DecompressPointer r2
    //     0xb77bd4: add             x2, x2, HEAP, lsl #32
    // 0xb77bd8: sub             x6, x5, #1
    // 0xb77bdc: mov             x0, x5
    // 0xb77be0: mov             x1, x6
    // 0xb77be4: cmp             x1, x0
    // 0xb77be8: b.hs            #0xb77c68
    // 0xb77bec: ArrayLoad: r0 = r7[r6]  ; Unknown_4
    //     0xb77bec: add             x16, x7, x6, lsl #2
    //     0xb77bf0: ldur            w0, [x16, #0xf]
    // 0xb77bf4: DecompressPointer r0
    //     0xb77bf4: add             x0, x0, HEAP, lsl #32
    // 0xb77bf8: LoadField: r1 = r4->field_b
    //     0xb77bf8: ldur            w1, [x4, #0xb]
    // 0xb77bfc: DecompressPointer r1
    //     0xb77bfc: add             x1, x1, HEAP, lsl #32
    // 0xb77c00: cmp             w1, NULL
    // 0xb77c04: b.eq            #0xb77c6c
    // 0xb77c08: LoadField: r4 = r1->field_27
    //     0xb77c08: ldur            w4, [x1, #0x27]
    // 0xb77c0c: DecompressPointer r4
    //     0xb77c0c: add             x4, x4, HEAP, lsl #32
    // 0xb77c10: LoadField: r1 = r3->field_6b
    //     0xb77c10: ldur            w1, [x3, #0x6b]
    // 0xb77c14: DecompressPointer r1
    //     0xb77c14: add             x1, x1, HEAP, lsl #32
    // 0xb77c18: LoadField: r5 = r3->field_6f
    //     0xb77c18: ldur            w5, [x3, #0x6f]
    // 0xb77c1c: DecompressPointer r5
    //     0xb77c1c: add             x5, x5, HEAP, lsl #32
    // 0xb77c20: LoadField: r6 = r3->field_7
    //     0xb77c20: ldur            w6, [x3, #7]
    // 0xb77c24: DecompressPointer r6
    //     0xb77c24: add             x6, x6, HEAP, lsl #32
    // 0xb77c28: stp             x2, x4, [SP, #0x20]
    // 0xb77c2c: stp             x1, x0, [SP, #0x10]
    // 0xb77c30: stp             x6, x5, [SP]
    // 0xb77c34: r4 = 0
    //     0xb77c34: movz            x4, #0
    // 0xb77c38: ldr             x0, [SP, #0x28]
    // 0xb77c3c: r16 = UnlinkedCall_0x613b5c
    //     0xb77c3c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a870] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77c40: add             x16, x16, #0x870
    // 0xb77c44: ldp             x5, lr, [x16]
    // 0xb77c48: blr             lr
    // 0xb77c4c: r0 = Null
    //     0xb77c4c: mov             x0, NULL
    // 0xb77c50: LeaveFrame
    //     0xb77c50: mov             SP, fp
    //     0xb77c54: ldp             fp, lr, [SP], #0x10
    // 0xb77c58: ret
    //     0xb77c58: ret             
    // 0xb77c5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb77c5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77c60: b               #0xb77b64
    // 0xb77c64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77c64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77c68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb77c68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb77c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77c6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb77c70, size: 0xe0
    // 0xb77c70: EnterFrame
    //     0xb77c70: stp             fp, lr, [SP, #-0x10]!
    //     0xb77c74: mov             fp, SP
    // 0xb77c78: AllocStack(0x20)
    //     0xb77c78: sub             SP, SP, #0x20
    // 0xb77c7c: SetupParameters()
    //     0xb77c7c: ldr             x0, [fp, #0x10]
    //     0xb77c80: ldur            w1, [x0, #0x17]
    //     0xb77c84: add             x1, x1, HEAP, lsl #32
    // 0xb77c88: CheckStackOverflow
    //     0xb77c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb77c8c: cmp             SP, x16
    //     0xb77c90: b.ls            #0xb77d20
    // 0xb77c94: LoadField: r0 = r1->field_f
    //     0xb77c94: ldur            w0, [x1, #0xf]
    // 0xb77c98: DecompressPointer r0
    //     0xb77c98: add             x0, x0, HEAP, lsl #32
    // 0xb77c9c: LoadField: r1 = r0->field_b
    //     0xb77c9c: ldur            w1, [x0, #0xb]
    // 0xb77ca0: DecompressPointer r1
    //     0xb77ca0: add             x1, x1, HEAP, lsl #32
    // 0xb77ca4: cmp             w1, NULL
    // 0xb77ca8: b.eq            #0xb77d28
    // 0xb77cac: LoadField: d0 = r0->field_13
    //     0xb77cac: ldur            d0, [x0, #0x13]
    // 0xb77cb0: LoadField: r0 = r1->field_b
    //     0xb77cb0: ldur            w0, [x1, #0xb]
    // 0xb77cb4: DecompressPointer r0
    //     0xb77cb4: add             x0, x0, HEAP, lsl #32
    // 0xb77cb8: LoadField: r2 = r0->field_8f
    //     0xb77cb8: ldur            w2, [x0, #0x8f]
    // 0xb77cbc: DecompressPointer r2
    //     0xb77cbc: add             x2, x2, HEAP, lsl #32
    // 0xb77cc0: LoadField: r3 = r1->field_2b
    //     0xb77cc0: ldur            w3, [x1, #0x2b]
    // 0xb77cc4: DecompressPointer r3
    //     0xb77cc4: add             x3, x3, HEAP, lsl #32
    // 0xb77cc8: r1 = inline_Allocate_Double()
    //     0xb77cc8: ldp             x1, x4, [THR, #0x50]  ; THR::top
    //     0xb77ccc: add             x1, x1, #0x10
    //     0xb77cd0: cmp             x4, x1
    //     0xb77cd4: b.ls            #0xb77d2c
    //     0xb77cd8: str             x1, [THR, #0x50]  ; THR::top
    //     0xb77cdc: sub             x1, x1, #0xf
    //     0xb77ce0: movz            x4, #0xe15c
    //     0xb77ce4: movk            x4, #0x3, lsl #16
    //     0xb77ce8: stur            x4, [x1, #-1]
    // 0xb77cec: StoreField: r1->field_7 = d0
    //     0xb77cec: stur            d0, [x1, #7]
    // 0xb77cf0: stp             x1, x3, [SP, #0x10]
    // 0xb77cf4: stp             x2, x0, [SP]
    // 0xb77cf8: r4 = 0
    //     0xb77cf8: movz            x4, #0
    // 0xb77cfc: ldr             x0, [SP, #0x18]
    // 0xb77d00: r16 = UnlinkedCall_0x613b5c
    //     0xb77d00: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a880] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77d04: add             x16, x16, #0x880
    // 0xb77d08: ldp             x5, lr, [x16]
    // 0xb77d0c: blr             lr
    // 0xb77d10: r0 = Null
    //     0xb77d10: mov             x0, NULL
    // 0xb77d14: LeaveFrame
    //     0xb77d14: mov             SP, fp
    //     0xb77d18: ldp             fp, lr, [SP], #0x10
    // 0xb77d1c: ret
    //     0xb77d1c: ret             
    // 0xb77d20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb77d20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77d24: b               #0xb77c94
    // 0xb77d28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77d28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77d2c: SaveReg d0
    //     0xb77d2c: str             q0, [SP, #-0x10]!
    // 0xb77d30: stp             x2, x3, [SP, #-0x10]!
    // 0xb77d34: SaveReg r0
    //     0xb77d34: str             x0, [SP, #-8]!
    // 0xb77d38: r0 = AllocateDouble()
    //     0xb77d38: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb77d3c: mov             x1, x0
    // 0xb77d40: RestoreReg r0
    //     0xb77d40: ldr             x0, [SP], #8
    // 0xb77d44: ldp             x2, x3, [SP], #0x10
    // 0xb77d48: RestoreReg d0
    //     0xb77d48: ldr             q0, [SP], #0x10
    // 0xb77d4c: b               #0xb77cec
  }
  [closure] void <anonymous closure>(dynamic, double) {
    // ** addr: 0xb77d50, size: 0x120
    // 0xb77d50: EnterFrame
    //     0xb77d50: stp             fp, lr, [SP, #-0x10]!
    //     0xb77d54: mov             fp, SP
    // 0xb77d58: AllocStack(0x30)
    //     0xb77d58: sub             SP, SP, #0x30
    // 0xb77d5c: SetupParameters()
    //     0xb77d5c: ldr             x0, [fp, #0x18]
    //     0xb77d60: ldur            w1, [x0, #0x17]
    //     0xb77d64: add             x1, x1, HEAP, lsl #32
    //     0xb77d68: stur            x1, [fp, #-8]
    // 0xb77d6c: CheckStackOverflow
    //     0xb77d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb77d70: cmp             SP, x16
    //     0xb77d74: b.ls            #0xb77e4c
    // 0xb77d78: r1 = 1
    //     0xb77d78: movz            x1, #0x1
    // 0xb77d7c: r0 = AllocateContext()
    //     0xb77d7c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb77d80: mov             x1, x0
    // 0xb77d84: ldur            x0, [fp, #-8]
    // 0xb77d88: StoreField: r1->field_b = r0
    //     0xb77d88: stur            w0, [x1, #0xb]
    // 0xb77d8c: ldr             x2, [fp, #0x10]
    // 0xb77d90: StoreField: r1->field_f = r2
    //     0xb77d90: stur            w2, [x1, #0xf]
    // 0xb77d94: LoadField: r3 = r0->field_f
    //     0xb77d94: ldur            w3, [x0, #0xf]
    // 0xb77d98: DecompressPointer r3
    //     0xb77d98: add             x3, x3, HEAP, lsl #32
    // 0xb77d9c: mov             x2, x1
    // 0xb77da0: stur            x3, [fp, #-0x10]
    // 0xb77da4: r1 = Function '<anonymous closure>':.
    //     0xb77da4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a890] AnonymousClosure: (0xa6d968), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xb77da8: ldr             x1, [x1, #0x890]
    // 0xb77dac: r0 = AllocateClosure()
    //     0xb77dac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb77db0: ldur            x1, [fp, #-0x10]
    // 0xb77db4: mov             x2, x0
    // 0xb77db8: r0 = setState()
    //     0xb77db8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb77dbc: ldur            x0, [fp, #-8]
    // 0xb77dc0: LoadField: r1 = r0->field_f
    //     0xb77dc0: ldur            w1, [x0, #0xf]
    // 0xb77dc4: DecompressPointer r1
    //     0xb77dc4: add             x1, x1, HEAP, lsl #32
    // 0xb77dc8: LoadField: r0 = r1->field_b
    //     0xb77dc8: ldur            w0, [x1, #0xb]
    // 0xb77dcc: DecompressPointer r0
    //     0xb77dcc: add             x0, x0, HEAP, lsl #32
    // 0xb77dd0: cmp             w0, NULL
    // 0xb77dd4: b.eq            #0xb77e54
    // 0xb77dd8: LoadField: d0 = r1->field_13
    //     0xb77dd8: ldur            d0, [x1, #0x13]
    // 0xb77ddc: LoadField: r1 = r0->field_b
    //     0xb77ddc: ldur            w1, [x0, #0xb]
    // 0xb77de0: DecompressPointer r1
    //     0xb77de0: add             x1, x1, HEAP, lsl #32
    // 0xb77de4: LoadField: r2 = r0->field_2b
    //     0xb77de4: ldur            w2, [x0, #0x2b]
    // 0xb77de8: DecompressPointer r2
    //     0xb77de8: add             x2, x2, HEAP, lsl #32
    // 0xb77dec: r0 = inline_Allocate_Double()
    //     0xb77dec: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xb77df0: add             x0, x0, #0x10
    //     0xb77df4: cmp             x3, x0
    //     0xb77df8: b.ls            #0xb77e58
    //     0xb77dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb77e00: sub             x0, x0, #0xf
    //     0xb77e04: movz            x3, #0xe15c
    //     0xb77e08: movk            x3, #0x3, lsl #16
    //     0xb77e0c: stur            x3, [x0, #-1]
    // 0xb77e10: StoreField: r0->field_7 = d0
    //     0xb77e10: stur            d0, [x0, #7]
    // 0xb77e14: stp             x0, x2, [SP, #0x10]
    // 0xb77e18: r16 = "stars"
    //     0xb77e18: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0xb77e1c: ldr             x16, [x16, #0xb28]
    // 0xb77e20: stp             x16, x1, [SP]
    // 0xb77e24: r4 = 0
    //     0xb77e24: movz            x4, #0
    // 0xb77e28: ldr             x0, [SP, #0x18]
    // 0xb77e2c: r16 = UnlinkedCall_0x613b5c
    //     0xb77e2c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a898] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb77e30: add             x16, x16, #0x898
    // 0xb77e34: ldp             x5, lr, [x16]
    // 0xb77e38: blr             lr
    // 0xb77e3c: r0 = Null
    //     0xb77e3c: mov             x0, NULL
    // 0xb77e40: LeaveFrame
    //     0xb77e40: mov             SP, fp
    //     0xb77e44: ldp             fp, lr, [SP], #0x10
    // 0xb77e48: ret
    //     0xb77e48: ret             
    // 0xb77e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb77e4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77e50: b               #0xb77d78
    // 0xb77e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77e58: SaveReg d0
    //     0xb77e58: str             q0, [SP, #-0x10]!
    // 0xb77e5c: stp             x1, x2, [SP, #-0x10]!
    // 0xb77e60: r0 = AllocateDouble()
    //     0xb77e60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb77e64: ldp             x1, x2, [SP], #0x10
    // 0xb77e68: RestoreReg d0
    //     0xb77e68: ldr             q0, [SP], #0x10
    // 0xb77e6c: b               #0xb77e10
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb77e70, size: 0x154
    // 0xb77e70: EnterFrame
    //     0xb77e70: stp             fp, lr, [SP, #-0x10]!
    //     0xb77e74: mov             fp, SP
    // 0xb77e78: AllocStack(0x38)
    //     0xb77e78: sub             SP, SP, #0x38
    // 0xb77e7c: SetupParameters()
    //     0xb77e7c: ldr             x0, [fp, #0x10]
    //     0xb77e80: ldur            w2, [x0, #0x17]
    //     0xb77e84: add             x2, x2, HEAP, lsl #32
    //     0xb77e88: stur            x2, [fp, #-8]
    // 0xb77e8c: CheckStackOverflow
    //     0xb77e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb77e90: cmp             SP, x16
    //     0xb77e94: b.ls            #0xb77fa8
    // 0xb77e98: LoadField: r0 = r2->field_f
    //     0xb77e98: ldur            w0, [x2, #0xf]
    // 0xb77e9c: DecompressPointer r0
    //     0xb77e9c: add             x0, x0, HEAP, lsl #32
    // 0xb77ea0: LoadField: r1 = r0->field_b
    //     0xb77ea0: ldur            w1, [x0, #0xb]
    // 0xb77ea4: DecompressPointer r1
    //     0xb77ea4: add             x1, x1, HEAP, lsl #32
    // 0xb77ea8: cmp             w1, NULL
    // 0xb77eac: b.eq            #0xb77fb0
    // 0xb77eb0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb77eb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb77eb4: ldr             x0, [x0, #0x1c80]
    //     0xb77eb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb77ebc: cmp             w0, w16
    //     0xb77ec0: b.ne            #0xb77ecc
    //     0xb77ec4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb77ec8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb77ecc: ldur            x2, [fp, #-8]
    // 0xb77ed0: LoadField: r0 = r2->field_f
    //     0xb77ed0: ldur            w0, [x2, #0xf]
    // 0xb77ed4: DecompressPointer r0
    //     0xb77ed4: add             x0, x0, HEAP, lsl #32
    // 0xb77ed8: LoadField: r1 = r0->field_b
    //     0xb77ed8: ldur            w1, [x0, #0xb]
    // 0xb77edc: DecompressPointer r1
    //     0xb77edc: add             x1, x1, HEAP, lsl #32
    // 0xb77ee0: cmp             w1, NULL
    // 0xb77ee4: b.eq            #0xb77fb4
    // 0xb77ee8: LoadField: r0 = r1->field_b
    //     0xb77ee8: ldur            w0, [x1, #0xb]
    // 0xb77eec: DecompressPointer r0
    //     0xb77eec: add             x0, x0, HEAP, lsl #32
    // 0xb77ef0: LoadField: r1 = r0->field_7
    //     0xb77ef0: ldur            w1, [x0, #7]
    // 0xb77ef4: DecompressPointer r1
    //     0xb77ef4: add             x1, x1, HEAP, lsl #32
    // 0xb77ef8: stur            x1, [fp, #-0x20]
    // 0xb77efc: cmp             w1, NULL
    // 0xb77f00: b.eq            #0xb77fb8
    // 0xb77f04: LoadField: r3 = r0->field_f
    //     0xb77f04: ldur            w3, [x0, #0xf]
    // 0xb77f08: DecompressPointer r3
    //     0xb77f08: add             x3, x3, HEAP, lsl #32
    // 0xb77f0c: stur            x3, [fp, #-0x18]
    // 0xb77f10: cmp             w3, NULL
    // 0xb77f14: b.eq            #0xb77fbc
    // 0xb77f18: LoadField: r4 = r0->field_23
    //     0xb77f18: ldur            w4, [x0, #0x23]
    // 0xb77f1c: DecompressPointer r4
    //     0xb77f1c: add             x4, x4, HEAP, lsl #32
    // 0xb77f20: stur            x4, [fp, #-0x10]
    // 0xb77f24: cmp             w4, NULL
    // 0xb77f28: b.eq            #0xb77fc0
    // 0xb77f2c: r0 = OrderItemModel()
    //     0xb77f2c: bl              #0x925de8  ; AllocateOrderItemModelStub -> OrderItemModel (size=0x14)
    // 0xb77f30: mov             x1, x0
    // 0xb77f34: ldur            x0, [fp, #-0x20]
    // 0xb77f38: StoreField: r1->field_7 = r0
    //     0xb77f38: stur            w0, [x1, #7]
    // 0xb77f3c: ldur            x0, [fp, #-0x18]
    // 0xb77f40: StoreField: r1->field_b = r0
    //     0xb77f40: stur            w0, [x1, #0xb]
    // 0xb77f44: ldur            x0, [fp, #-0x10]
    // 0xb77f48: StoreField: r1->field_f = r0
    //     0xb77f48: stur            w0, [x1, #0xf]
    // 0xb77f4c: r16 = "/order"
    //     0xb77f4c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb430] "/order"
    //     0xb77f50: ldr             x16, [x16, #0x430]
    // 0xb77f54: stp             x16, NULL, [SP, #8]
    // 0xb77f58: str             x1, [SP]
    // 0xb77f5c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb77f5c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb77f60: ldr             x4, [x4, #0x438]
    // 0xb77f64: r0 = GetNavigation.toNamed()
    //     0xb77f64: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb77f68: stur            x0, [fp, #-0x10]
    // 0xb77f6c: cmp             w0, NULL
    // 0xb77f70: b.eq            #0xb77f98
    // 0xb77f74: ldur            x2, [fp, #-8]
    // 0xb77f78: r1 = Function '<anonymous closure>':.
    //     0xb77f78: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a8a8] AnonymousClosure: (0xb77fc4), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xb77f7c: ldr             x1, [x1, #0x8a8]
    // 0xb77f80: r0 = AllocateClosure()
    //     0xb77f80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb77f84: ldur            x16, [fp, #-0x10]
    // 0xb77f88: stp             x16, NULL, [SP, #8]
    // 0xb77f8c: str             x0, [SP]
    // 0xb77f90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb77f90: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb77f94: r0 = then()
    //     0xb77f94: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb77f98: r0 = Null
    //     0xb77f98: mov             x0, NULL
    // 0xb77f9c: LeaveFrame
    //     0xb77f9c: mov             SP, fp
    //     0xb77fa0: ldp             fp, lr, [SP], #0x10
    // 0xb77fa4: ret
    //     0xb77fa4: ret             
    // 0xb77fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb77fa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb77fac: b               #0xb77e98
    // 0xb77fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77fb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77fb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77fb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77fbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb77fc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb77fc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb77fc4, size: 0x7c
    // 0xb77fc4: EnterFrame
    //     0xb77fc4: stp             fp, lr, [SP, #-0x10]!
    //     0xb77fc8: mov             fp, SP
    // 0xb77fcc: AllocStack(0x8)
    //     0xb77fcc: sub             SP, SP, #8
    // 0xb77fd0: SetupParameters()
    //     0xb77fd0: ldr             x0, [fp, #0x18]
    //     0xb77fd4: ldur            w1, [x0, #0x17]
    //     0xb77fd8: add             x1, x1, HEAP, lsl #32
    // 0xb77fdc: CheckStackOverflow
    //     0xb77fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb77fe0: cmp             SP, x16
    //     0xb77fe4: b.ls            #0xb78034
    // 0xb77fe8: LoadField: r0 = r1->field_f
    //     0xb77fe8: ldur            w0, [x1, #0xf]
    // 0xb77fec: DecompressPointer r0
    //     0xb77fec: add             x0, x0, HEAP, lsl #32
    // 0xb77ff0: LoadField: r1 = r0->field_b
    //     0xb77ff0: ldur            w1, [x0, #0xb]
    // 0xb77ff4: DecompressPointer r1
    //     0xb77ff4: add             x1, x1, HEAP, lsl #32
    // 0xb77ff8: cmp             w1, NULL
    // 0xb77ffc: b.eq            #0xb7803c
    // 0xb78000: LoadField: r0 = r1->field_23
    //     0xb78000: ldur            w0, [x1, #0x23]
    // 0xb78004: DecompressPointer r0
    //     0xb78004: add             x0, x0, HEAP, lsl #32
    // 0xb78008: str             x0, [SP]
    // 0xb7800c: r4 = 0
    //     0xb7800c: movz            x4, #0
    // 0xb78010: ldr             x0, [SP]
    // 0xb78014: r16 = UnlinkedCall_0x613b5c
    //     0xb78014: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a8b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb78018: add             x16, x16, #0x8b0
    // 0xb7801c: ldp             x5, lr, [x16]
    // 0xb78020: blr             lr
    // 0xb78024: r0 = Null
    //     0xb78024: mov             x0, NULL
    // 0xb78028: LeaveFrame
    //     0xb78028: mov             SP, fp
    //     0xb7802c: ldp             fp, lr, [SP], #0x10
    // 0xb78030: ret
    //     0xb78030: ret             
    // 0xb78034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb78034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb78038: b               #0xb77fe8
    // 0xb7803c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7803c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, ImageProvider<Object>) {
    // ** addr: 0xb78040, size: 0xf0
    // 0xb78040: EnterFrame
    //     0xb78040: stp             fp, lr, [SP, #-0x10]!
    //     0xb78044: mov             fp, SP
    // 0xb78048: AllocStack(0x28)
    //     0xb78048: sub             SP, SP, #0x28
    // 0xb7804c: CheckStackOverflow
    //     0xb7804c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb78050: cmp             SP, x16
    //     0xb78054: b.ls            #0xb78128
    // 0xb78058: r0 = DecorationImage()
    //     0xb78058: bl              #0x83fce0  ; AllocateDecorationImageStub -> DecorationImage (size=0x44)
    // 0xb7805c: mov             x1, x0
    // 0xb78060: ldr             x0, [fp, #0x10]
    // 0xb78064: stur            x1, [fp, #-8]
    // 0xb78068: StoreField: r1->field_7 = r0
    //     0xb78068: stur            w0, [x1, #7]
    // 0xb7806c: r0 = Instance_BoxFit
    //     0xb7806c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb78070: ldr             x0, [x0, #0x118]
    // 0xb78074: StoreField: r1->field_13 = r0
    //     0xb78074: stur            w0, [x1, #0x13]
    // 0xb78078: r0 = Instance_Alignment
    //     0xb78078: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7807c: ldr             x0, [x0, #0xb10]
    // 0xb78080: ArrayStore: r1[0] = r0  ; List_4
    //     0xb78080: stur            w0, [x1, #0x17]
    // 0xb78084: r0 = Instance_ImageRepeat
    //     0xb78084: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb00] Obj!ImageRepeat@d73821
    //     0xb78088: ldr             x0, [x0, #0xb00]
    // 0xb7808c: StoreField: r1->field_1f = r0
    //     0xb7808c: stur            w0, [x1, #0x1f]
    // 0xb78090: r0 = false
    //     0xb78090: add             x0, NULL, #0x30  ; false
    // 0xb78094: StoreField: r1->field_23 = r0
    //     0xb78094: stur            w0, [x1, #0x23]
    // 0xb78098: d0 = 1.000000
    //     0xb78098: fmov            d0, #1.00000000
    // 0xb7809c: StoreField: r1->field_27 = d0
    //     0xb7809c: stur            d0, [x1, #0x27]
    // 0xb780a0: StoreField: r1->field_2f = d0
    //     0xb780a0: stur            d0, [x1, #0x2f]
    // 0xb780a4: r2 = Instance_FilterQuality
    //     0xb780a4: add             x2, PP, #0x33, lsl #12  ; [pp+0x33a98] Obj!FilterQuality@d77121
    //     0xb780a8: ldr             x2, [x2, #0xa98]
    // 0xb780ac: StoreField: r1->field_37 = r2
    //     0xb780ac: stur            w2, [x1, #0x37]
    // 0xb780b0: StoreField: r1->field_3b = r0
    //     0xb780b0: stur            w0, [x1, #0x3b]
    // 0xb780b4: StoreField: r1->field_3f = r0
    //     0xb780b4: stur            w0, [x1, #0x3f]
    // 0xb780b8: r0 = BoxDecoration()
    //     0xb780b8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb780bc: mov             x1, x0
    // 0xb780c0: ldur            x0, [fp, #-8]
    // 0xb780c4: stur            x1, [fp, #-0x10]
    // 0xb780c8: StoreField: r1->field_b = r0
    //     0xb780c8: stur            w0, [x1, #0xb]
    // 0xb780cc: r0 = Instance_BorderRadius
    //     0xb780cc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb780d0: ldr             x0, [x0, #0x460]
    // 0xb780d4: StoreField: r1->field_13 = r0
    //     0xb780d4: stur            w0, [x1, #0x13]
    // 0xb780d8: r0 = Instance_BoxShape
    //     0xb780d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb780dc: ldr             x0, [x0, #0x80]
    // 0xb780e0: StoreField: r1->field_23 = r0
    //     0xb780e0: stur            w0, [x1, #0x23]
    // 0xb780e4: r0 = Container()
    //     0xb780e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb780e8: stur            x0, [fp, #-8]
    // 0xb780ec: r16 = 84.000000
    //     0xb780ec: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb780f0: ldr             x16, [x16, #0xf90]
    // 0xb780f4: r30 = 84.000000
    //     0xb780f4: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xb780f8: ldr             lr, [lr, #0xf90]
    // 0xb780fc: stp             lr, x16, [SP, #8]
    // 0xb78100: ldur            x16, [fp, #-0x10]
    // 0xb78104: str             x16, [SP]
    // 0xb78108: mov             x1, x0
    // 0xb7810c: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb7810c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb78110: ldr             x4, [x4, #0x468]
    // 0xb78114: r0 = Container()
    //     0xb78114: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb78118: ldur            x0, [fp, #-8]
    // 0xb7811c: LeaveFrame
    //     0xb7811c: mov             SP, fp
    //     0xb78120: ldp             fp, lr, [SP], #0x10
    // 0xb78124: ret
    //     0xb78124: ret             
    // 0xb78128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb78128: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7812c: b               #0xb78058
  }
}

// class id: 4068, size: 0x30, field offset: 0xc
//   const constructor, 
class OrderCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f5d8, size: 0x28
    // 0xc7f5d8: EnterFrame
    //     0xc7f5d8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f5dc: mov             fp, SP
    // 0xc7f5e0: mov             x0, x1
    // 0xc7f5e4: r1 = <OrderCard>
    //     0xc7f5e4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e08] TypeArguments: <OrderCard>
    //     0xc7f5e8: ldr             x1, [x1, #0xe08]
    // 0xc7f5ec: r0 = _OrderCardState()
    //     0xc7f5ec: bl              #0xc7f600  ; Allocate_OrderCardStateStub -> _OrderCardState (size=0x1c)
    // 0xc7f5f0: StoreField: r0->field_13 = rZR
    //     0xc7f5f0: stur            xzr, [x0, #0x13]
    // 0xc7f5f4: LeaveFrame
    //     0xc7f5f4: mov             SP, fp
    //     0xc7f5f8: ldp             fp, lr, [SP], #0x10
    // 0xc7f5fc: ret
    //     0xc7f5fc: ret             
  }
}
