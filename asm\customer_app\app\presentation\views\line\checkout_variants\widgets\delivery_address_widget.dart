// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_address_widget.dart

// class id: 1049490, size: 0x8
class :: {
}

// class id: 3274, size: 0x14, field offset: 0x14
class _DeliveryAddressWidgetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x906014, size: 0x70
    // 0x906014: EnterFrame
    //     0x906014: stp             fp, lr, [SP, #-0x10]!
    //     0x906018: mov             fp, SP
    // 0x90601c: AllocStack(0x10)
    //     0x90601c: sub             SP, SP, #0x10
    // 0x906020: SetupParameters()
    //     0x906020: ldr             x0, [fp, #0x18]
    //     0x906024: ldur            w1, [x0, #0x17]
    //     0x906028: add             x1, x1, HEAP, lsl #32
    // 0x90602c: CheckStackOverflow
    //     0x90602c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x906030: cmp             SP, x16
    //     0x906034: b.ls            #0x906078
    // 0x906038: LoadField: r0 = r1->field_f
    //     0x906038: ldur            w0, [x1, #0xf]
    // 0x90603c: DecompressPointer r0
    //     0x90603c: add             x0, x0, HEAP, lsl #32
    // 0x906040: LoadField: r1 = r0->field_b
    //     0x906040: ldur            w1, [x0, #0xb]
    // 0x906044: DecompressPointer r1
    //     0x906044: add             x1, x1, HEAP, lsl #32
    // 0x906048: cmp             w1, NULL
    // 0x90604c: b.eq            #0x906080
    // 0x906050: LoadField: r0 = r1->field_23
    //     0x906050: ldur            w0, [x1, #0x23]
    // 0x906054: DecompressPointer r0
    //     0x906054: add             x0, x0, HEAP, lsl #32
    // 0x906058: r16 = true
    //     0x906058: add             x16, NULL, #0x20  ; true
    // 0x90605c: stp             x16, x0, [SP]
    // 0x906060: ClosureCall
    //     0x906060: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x906064: ldur            x2, [x0, #0x1f]
    //     0x906068: blr             x2
    // 0x90606c: LeaveFrame
    //     0x90606c: mov             SP, fp
    //     0x906070: ldp             fp, lr, [SP], #0x10
    // 0x906074: ret
    //     0x906074: ret             
    // 0x906078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90607c: b               #0x906038
    // 0x906080: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x906080: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x947f8c, size: 0x130
    // 0x947f8c: EnterFrame
    //     0x947f8c: stp             fp, lr, [SP, #-0x10]!
    //     0x947f90: mov             fp, SP
    // 0x947f94: AllocStack(0x18)
    //     0x947f94: sub             SP, SP, #0x18
    // 0x947f98: SetupParameters(_DeliveryAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x947f98: stur            x1, [fp, #-8]
    // 0x947f9c: CheckStackOverflow
    //     0x947f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947fa0: cmp             SP, x16
    //     0x947fa4: b.ls            #0x9480b0
    // 0x947fa8: r1 = 1
    //     0x947fa8: movz            x1, #0x1
    // 0x947fac: r0 = AllocateContext()
    //     0x947fac: bl              #0x16f6108  ; AllocateContextStub
    // 0x947fb0: mov             x1, x0
    // 0x947fb4: ldur            x0, [fp, #-8]
    // 0x947fb8: StoreField: r1->field_f = r0
    //     0x947fb8: stur            w0, [x1, #0xf]
    // 0x947fbc: r0 = LoadStaticField(0x878)
    //     0x947fbc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x947fc0: ldr             x0, [x0, #0x10f0]
    // 0x947fc4: cmp             w0, NULL
    // 0x947fc8: b.eq            #0x9480b8
    // 0x947fcc: LoadField: r3 = r0->field_53
    //     0x947fcc: ldur            w3, [x0, #0x53]
    // 0x947fd0: DecompressPointer r3
    //     0x947fd0: add             x3, x3, HEAP, lsl #32
    // 0x947fd4: stur            x3, [fp, #-0x10]
    // 0x947fd8: LoadField: r0 = r3->field_7
    //     0x947fd8: ldur            w0, [x3, #7]
    // 0x947fdc: DecompressPointer r0
    //     0x947fdc: add             x0, x0, HEAP, lsl #32
    // 0x947fe0: mov             x2, x1
    // 0x947fe4: stur            x0, [fp, #-8]
    // 0x947fe8: r1 = Function '<anonymous closure>':.
    //     0x947fe8: add             x1, PP, #0x54, lsl #12  ; [pp+0x547c0] AnonymousClosure: (0x906014), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_address_widget.dart] _DeliveryAddressWidgetState::initState (0x947f8c)
    //     0x947fec: ldr             x1, [x1, #0x7c0]
    // 0x947ff0: r0 = AllocateClosure()
    //     0x947ff0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x947ff4: ldur            x2, [fp, #-8]
    // 0x947ff8: mov             x3, x0
    // 0x947ffc: r1 = Null
    //     0x947ffc: mov             x1, NULL
    // 0x948000: stur            x3, [fp, #-8]
    // 0x948004: cmp             w2, NULL
    // 0x948008: b.eq            #0x948028
    // 0x94800c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x94800c: ldur            w4, [x2, #0x17]
    // 0x948010: DecompressPointer r4
    //     0x948010: add             x4, x4, HEAP, lsl #32
    // 0x948014: r8 = X0
    //     0x948014: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x948018: LoadField: r9 = r4->field_7
    //     0x948018: ldur            x9, [x4, #7]
    // 0x94801c: r3 = Null
    //     0x94801c: add             x3, PP, #0x54, lsl #12  ; [pp+0x547c8] Null
    //     0x948020: ldr             x3, [x3, #0x7c8]
    // 0x948024: blr             x9
    // 0x948028: ldur            x0, [fp, #-0x10]
    // 0x94802c: LoadField: r1 = r0->field_b
    //     0x94802c: ldur            w1, [x0, #0xb]
    // 0x948030: LoadField: r2 = r0->field_f
    //     0x948030: ldur            w2, [x0, #0xf]
    // 0x948034: DecompressPointer r2
    //     0x948034: add             x2, x2, HEAP, lsl #32
    // 0x948038: LoadField: r3 = r2->field_b
    //     0x948038: ldur            w3, [x2, #0xb]
    // 0x94803c: r2 = LoadInt32Instr(r1)
    //     0x94803c: sbfx            x2, x1, #1, #0x1f
    // 0x948040: stur            x2, [fp, #-0x18]
    // 0x948044: r1 = LoadInt32Instr(r3)
    //     0x948044: sbfx            x1, x3, #1, #0x1f
    // 0x948048: cmp             x2, x1
    // 0x94804c: b.ne            #0x948058
    // 0x948050: mov             x1, x0
    // 0x948054: r0 = _growToNextCapacity()
    //     0x948054: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x948058: ldur            x2, [fp, #-0x10]
    // 0x94805c: ldur            x3, [fp, #-0x18]
    // 0x948060: add             x4, x3, #1
    // 0x948064: lsl             x5, x4, #1
    // 0x948068: StoreField: r2->field_b = r5
    //     0x948068: stur            w5, [x2, #0xb]
    // 0x94806c: LoadField: r1 = r2->field_f
    //     0x94806c: ldur            w1, [x2, #0xf]
    // 0x948070: DecompressPointer r1
    //     0x948070: add             x1, x1, HEAP, lsl #32
    // 0x948074: ldur            x0, [fp, #-8]
    // 0x948078: ArrayStore: r1[r3] = r0  ; List_4
    //     0x948078: add             x25, x1, x3, lsl #2
    //     0x94807c: add             x25, x25, #0xf
    //     0x948080: str             w0, [x25]
    //     0x948084: tbz             w0, #0, #0x9480a0
    //     0x948088: ldurb           w16, [x1, #-1]
    //     0x94808c: ldurb           w17, [x0, #-1]
    //     0x948090: and             x16, x17, x16, lsr #2
    //     0x948094: tst             x16, HEAP, lsr #32
    //     0x948098: b.eq            #0x9480a0
    //     0x94809c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9480a0: r0 = Null
    //     0x9480a0: mov             x0, NULL
    // 0x9480a4: LeaveFrame
    //     0x9480a4: mov             SP, fp
    //     0x9480a8: ldp             fp, lr, [SP], #0x10
    // 0x9480ac: ret
    //     0x9480ac: ret             
    // 0x9480b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9480b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9480b4: b               #0x947fa8
    // 0x9480b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9480b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbb701c, size: 0xe3c
    // 0xbb701c: EnterFrame
    //     0xbb701c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7020: mov             fp, SP
    // 0xbb7024: AllocStack(0x60)
    //     0xbb7024: sub             SP, SP, #0x60
    // 0xbb7028: SetupParameters(_DeliveryAddressWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb7028: mov             x0, x1
    //     0xbb702c: stur            x1, [fp, #-8]
    //     0xbb7030: mov             x1, x2
    //     0xbb7034: stur            x2, [fp, #-0x10]
    // 0xbb7038: CheckStackOverflow
    //     0xbb7038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb703c: cmp             SP, x16
    //     0xbb7040: b.ls            #0xbb7e14
    // 0xbb7044: r1 = 2
    //     0xbb7044: movz            x1, #0x2
    // 0xbb7048: r0 = AllocateContext()
    //     0xbb7048: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb704c: mov             x2, x0
    // 0xbb7050: ldur            x0, [fp, #-8]
    // 0xbb7054: stur            x2, [fp, #-0x18]
    // 0xbb7058: StoreField: r2->field_f = r0
    //     0xbb7058: stur            w0, [x2, #0xf]
    // 0xbb705c: ldur            x1, [fp, #-0x10]
    // 0xbb7060: StoreField: r2->field_13 = r1
    //     0xbb7060: stur            w1, [x2, #0x13]
    // 0xbb7064: r0 = of()
    //     0xbb7064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb7068: LoadField: r1 = r0->field_87
    //     0xbb7068: ldur            w1, [x0, #0x87]
    // 0xbb706c: DecompressPointer r1
    //     0xbb706c: add             x1, x1, HEAP, lsl #32
    // 0xbb7070: LoadField: r0 = r1->field_7
    //     0xbb7070: ldur            w0, [x1, #7]
    // 0xbb7074: DecompressPointer r0
    //     0xbb7074: add             x0, x0, HEAP, lsl #32
    // 0xbb7078: r16 = Instance_Color
    //     0xbb7078: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb707c: r30 = 14.000000
    //     0xbb707c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb7080: ldr             lr, [lr, #0x1d8]
    // 0xbb7084: stp             lr, x16, [SP]
    // 0xbb7088: mov             x1, x0
    // 0xbb708c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb708c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb7090: ldr             x4, [x4, #0x9b8]
    // 0xbb7094: r0 = copyWith()
    //     0xbb7094: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb7098: stur            x0, [fp, #-0x10]
    // 0xbb709c: r0 = Text()
    //     0xbb709c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb70a0: mov             x1, x0
    // 0xbb70a4: r0 = "Delivery Address"
    //     0xbb70a4: add             x0, PP, #0x54, lsl #12  ; [pp+0x54780] "Delivery Address"
    //     0xbb70a8: ldr             x0, [x0, #0x780]
    // 0xbb70ac: stur            x1, [fp, #-0x20]
    // 0xbb70b0: StoreField: r1->field_b = r0
    //     0xbb70b0: stur            w0, [x1, #0xb]
    // 0xbb70b4: ldur            x0, [fp, #-0x10]
    // 0xbb70b8: StoreField: r1->field_13 = r0
    //     0xbb70b8: stur            w0, [x1, #0x13]
    // 0xbb70bc: r0 = Padding()
    //     0xbb70bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb70c0: mov             x2, x0
    // 0xbb70c4: r0 = Instance_EdgeInsets
    //     0xbb70c4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbb70c8: ldr             x0, [x0, #0x770]
    // 0xbb70cc: stur            x2, [fp, #-0x10]
    // 0xbb70d0: StoreField: r2->field_f = r0
    //     0xbb70d0: stur            w0, [x2, #0xf]
    // 0xbb70d4: ldur            x0, [fp, #-0x20]
    // 0xbb70d8: StoreField: r2->field_b = r0
    //     0xbb70d8: stur            w0, [x2, #0xb]
    // 0xbb70dc: ldur            x0, [fp, #-0x18]
    // 0xbb70e0: LoadField: r1 = r0->field_13
    //     0xbb70e0: ldur            w1, [x0, #0x13]
    // 0xbb70e4: DecompressPointer r1
    //     0xbb70e4: add             x1, x1, HEAP, lsl #32
    // 0xbb70e8: r0 = of()
    //     0xbb70e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb70ec: LoadField: r1 = r0->field_5b
    //     0xbb70ec: ldur            w1, [x0, #0x5b]
    // 0xbb70f0: DecompressPointer r1
    //     0xbb70f0: add             x1, x1, HEAP, lsl #32
    // 0xbb70f4: r0 = LoadClassIdInstr(r1)
    //     0xbb70f4: ldur            x0, [x1, #-1]
    //     0xbb70f8: ubfx            x0, x0, #0xc, #0x14
    // 0xbb70fc: d0 = 0.070000
    //     0xbb70fc: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbb7100: ldr             d0, [x17, #0x5f8]
    // 0xbb7104: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb7104: sub             lr, x0, #0xffa
    //     0xbb7108: ldr             lr, [x21, lr, lsl #3]
    //     0xbb710c: blr             lr
    // 0xbb7110: mov             x2, x0
    // 0xbb7114: r1 = Null
    //     0xbb7114: mov             x1, NULL
    // 0xbb7118: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbb7118: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbb711c: r0 = Border.all()
    //     0xbb711c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbb7120: stur            x0, [fp, #-0x20]
    // 0xbb7124: r0 = BoxDecoration()
    //     0xbb7124: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb7128: mov             x3, x0
    // 0xbb712c: ldur            x0, [fp, #-0x20]
    // 0xbb7130: stur            x3, [fp, #-0x30]
    // 0xbb7134: StoreField: r3->field_f = r0
    //     0xbb7134: stur            w0, [x3, #0xf]
    // 0xbb7138: r0 = Instance_BorderRadius
    //     0xbb7138: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb713c: ldr             x0, [x0, #0xf70]
    // 0xbb7140: StoreField: r3->field_13 = r0
    //     0xbb7140: stur            w0, [x3, #0x13]
    // 0xbb7144: r0 = Instance_BoxShape
    //     0xbb7144: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb7148: ldr             x0, [x0, #0x80]
    // 0xbb714c: StoreField: r3->field_23 = r0
    //     0xbb714c: stur            w0, [x3, #0x23]
    // 0xbb7150: ldur            x4, [fp, #-8]
    // 0xbb7154: LoadField: r1 = r4->field_b
    //     0xbb7154: ldur            w1, [x4, #0xb]
    // 0xbb7158: DecompressPointer r1
    //     0xbb7158: add             x1, x1, HEAP, lsl #32
    // 0xbb715c: cmp             w1, NULL
    // 0xbb7160: b.eq            #0xbb7e1c
    // 0xbb7164: LoadField: r5 = r1->field_13
    //     0xbb7164: ldur            w5, [x1, #0x13]
    // 0xbb7168: DecompressPointer r5
    //     0xbb7168: add             x5, x5, HEAP, lsl #32
    // 0xbb716c: stur            x5, [fp, #-0x28]
    // 0xbb7170: LoadField: r6 = r5->field_b
    //     0xbb7170: ldur            w6, [x5, #0xb]
    // 0xbb7174: DecompressPointer r6
    //     0xbb7174: add             x6, x6, HEAP, lsl #32
    // 0xbb7178: stur            x6, [fp, #-0x20]
    // 0xbb717c: r1 = Null
    //     0xbb717c: mov             x1, NULL
    // 0xbb7180: r2 = 6
    //     0xbb7180: movz            x2, #0x6
    // 0xbb7184: r0 = AllocateArray()
    //     0xbb7184: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb7188: mov             x1, x0
    // 0xbb718c: ldur            x0, [fp, #-0x20]
    // 0xbb7190: StoreField: r1->field_f = r0
    //     0xbb7190: stur            w0, [x1, #0xf]
    // 0xbb7194: r16 = " "
    //     0xbb7194: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb7198: StoreField: r1->field_13 = r16
    //     0xbb7198: stur            w16, [x1, #0x13]
    // 0xbb719c: ldur            x0, [fp, #-0x28]
    // 0xbb71a0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbb71a0: ldur            w2, [x0, #0x17]
    // 0xbb71a4: DecompressPointer r2
    //     0xbb71a4: add             x2, x2, HEAP, lsl #32
    // 0xbb71a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbb71a8: stur            w2, [x1, #0x17]
    // 0xbb71ac: str             x1, [SP]
    // 0xbb71b0: r0 = _interpolate()
    //     0xbb71b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb71b4: ldur            x2, [fp, #-0x18]
    // 0xbb71b8: stur            x0, [fp, #-0x20]
    // 0xbb71bc: LoadField: r1 = r2->field_13
    //     0xbb71bc: ldur            w1, [x2, #0x13]
    // 0xbb71c0: DecompressPointer r1
    //     0xbb71c0: add             x1, x1, HEAP, lsl #32
    // 0xbb71c4: r0 = of()
    //     0xbb71c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb71c8: LoadField: r1 = r0->field_87
    //     0xbb71c8: ldur            w1, [x0, #0x87]
    // 0xbb71cc: DecompressPointer r1
    //     0xbb71cc: add             x1, x1, HEAP, lsl #32
    // 0xbb71d0: LoadField: r0 = r1->field_2b
    //     0xbb71d0: ldur            w0, [x1, #0x2b]
    // 0xbb71d4: DecompressPointer r0
    //     0xbb71d4: add             x0, x0, HEAP, lsl #32
    // 0xbb71d8: r16 = Instance_Color
    //     0xbb71d8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb71dc: r30 = 12.000000
    //     0xbb71dc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb71e0: ldr             lr, [lr, #0x9e8]
    // 0xbb71e4: stp             lr, x16, [SP]
    // 0xbb71e8: mov             x1, x0
    // 0xbb71ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb71ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb71f0: ldr             x4, [x4, #0x9b8]
    // 0xbb71f4: r0 = copyWith()
    //     0xbb71f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb71f8: stur            x0, [fp, #-0x28]
    // 0xbb71fc: r0 = Text()
    //     0xbb71fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb7200: mov             x2, x0
    // 0xbb7204: ldur            x0, [fp, #-0x20]
    // 0xbb7208: stur            x2, [fp, #-0x38]
    // 0xbb720c: StoreField: r2->field_b = r0
    //     0xbb720c: stur            w0, [x2, #0xb]
    // 0xbb7210: ldur            x0, [fp, #-0x28]
    // 0xbb7214: StoreField: r2->field_13 = r0
    //     0xbb7214: stur            w0, [x2, #0x13]
    // 0xbb7218: r1 = <FlexParentData>
    //     0xbb7218: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb721c: ldr             x1, [x1, #0xe00]
    // 0xbb7220: r0 = Expanded()
    //     0xbb7220: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbb7224: mov             x2, x0
    // 0xbb7228: r0 = 1
    //     0xbb7228: movz            x0, #0x1
    // 0xbb722c: stur            x2, [fp, #-0x28]
    // 0xbb7230: StoreField: r2->field_13 = r0
    //     0xbb7230: stur            x0, [x2, #0x13]
    // 0xbb7234: r0 = Instance_FlexFit
    //     0xbb7234: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb7238: ldr             x0, [x0, #0xe08]
    // 0xbb723c: StoreField: r2->field_1b = r0
    //     0xbb723c: stur            w0, [x2, #0x1b]
    // 0xbb7240: ldur            x0, [fp, #-0x38]
    // 0xbb7244: StoreField: r2->field_b = r0
    //     0xbb7244: stur            w0, [x2, #0xb]
    // 0xbb7248: ldur            x0, [fp, #-8]
    // 0xbb724c: LoadField: r1 = r0->field_b
    //     0xbb724c: ldur            w1, [x0, #0xb]
    // 0xbb7250: DecompressPointer r1
    //     0xbb7250: add             x1, x1, HEAP, lsl #32
    // 0xbb7254: cmp             w1, NULL
    // 0xbb7258: b.eq            #0xbb7e20
    // 0xbb725c: LoadField: r3 = r1->field_1b
    //     0xbb725c: ldur            w3, [x1, #0x1b]
    // 0xbb7260: DecompressPointer r3
    //     0xbb7260: add             x3, x3, HEAP, lsl #32
    // 0xbb7264: cmp             w3, NULL
    // 0xbb7268: b.ne            #0xbb7274
    // 0xbb726c: r4 = false
    //     0xbb726c: add             x4, NULL, #0x30  ; false
    // 0xbb7270: b               #0xbb7278
    // 0xbb7274: mov             x4, x3
    // 0xbb7278: ldur            x3, [fp, #-0x18]
    // 0xbb727c: stur            x4, [fp, #-0x20]
    // 0xbb7280: LoadField: r1 = r3->field_13
    //     0xbb7280: ldur            w1, [x3, #0x13]
    // 0xbb7284: DecompressPointer r1
    //     0xbb7284: add             x1, x1, HEAP, lsl #32
    // 0xbb7288: r0 = of()
    //     0xbb7288: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb728c: LoadField: r1 = r0->field_87
    //     0xbb728c: ldur            w1, [x0, #0x87]
    // 0xbb7290: DecompressPointer r1
    //     0xbb7290: add             x1, x1, HEAP, lsl #32
    // 0xbb7294: LoadField: r0 = r1->field_7
    //     0xbb7294: ldur            w0, [x1, #7]
    // 0xbb7298: DecompressPointer r0
    //     0xbb7298: add             x0, x0, HEAP, lsl #32
    // 0xbb729c: r16 = Instance_Color
    //     0xbb729c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb72a0: ldr             x16, [x16, #0x858]
    // 0xbb72a4: r30 = 12.000000
    //     0xbb72a4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb72a8: ldr             lr, [lr, #0x9e8]
    // 0xbb72ac: stp             lr, x16, [SP]
    // 0xbb72b0: mov             x1, x0
    // 0xbb72b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb72b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb72b8: ldr             x4, [x4, #0x9b8]
    // 0xbb72bc: r0 = copyWith()
    //     0xbb72bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb72c0: stur            x0, [fp, #-0x38]
    // 0xbb72c4: r0 = Text()
    //     0xbb72c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb72c8: mov             x1, x0
    // 0xbb72cc: r0 = "CHANGE"
    //     0xbb72cc: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xbb72d0: ldr             x0, [x0, #0xd10]
    // 0xbb72d4: stur            x1, [fp, #-0x40]
    // 0xbb72d8: StoreField: r1->field_b = r0
    //     0xbb72d8: stur            w0, [x1, #0xb]
    // 0xbb72dc: ldur            x0, [fp, #-0x38]
    // 0xbb72e0: StoreField: r1->field_13 = r0
    //     0xbb72e0: stur            w0, [x1, #0x13]
    // 0xbb72e4: r0 = InkWell()
    //     0xbb72e4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbb72e8: mov             x3, x0
    // 0xbb72ec: ldur            x0, [fp, #-0x40]
    // 0xbb72f0: stur            x3, [fp, #-0x38]
    // 0xbb72f4: StoreField: r3->field_b = r0
    //     0xbb72f4: stur            w0, [x3, #0xb]
    // 0xbb72f8: ldur            x2, [fp, #-0x18]
    // 0xbb72fc: r1 = Function '<anonymous closure>':.
    //     0xbb72fc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54788] AnonymousClosure: (0xbb7e58), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_address_widget.dart] _DeliveryAddressWidgetState::build (0xbb701c)
    //     0xbb7300: ldr             x1, [x1, #0x788]
    // 0xbb7304: r0 = AllocateClosure()
    //     0xbb7304: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb7308: mov             x1, x0
    // 0xbb730c: ldur            x0, [fp, #-0x38]
    // 0xbb7310: StoreField: r0->field_f = r1
    //     0xbb7310: stur            w1, [x0, #0xf]
    // 0xbb7314: r1 = true
    //     0xbb7314: add             x1, NULL, #0x20  ; true
    // 0xbb7318: StoreField: r0->field_43 = r1
    //     0xbb7318: stur            w1, [x0, #0x43]
    // 0xbb731c: r2 = Instance_BoxShape
    //     0xbb731c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb7320: ldr             x2, [x2, #0x80]
    // 0xbb7324: StoreField: r0->field_47 = r2
    //     0xbb7324: stur            w2, [x0, #0x47]
    // 0xbb7328: StoreField: r0->field_6f = r1
    //     0xbb7328: stur            w1, [x0, #0x6f]
    // 0xbb732c: r2 = false
    //     0xbb732c: add             x2, NULL, #0x30  ; false
    // 0xbb7330: StoreField: r0->field_73 = r2
    //     0xbb7330: stur            w2, [x0, #0x73]
    // 0xbb7334: StoreField: r0->field_83 = r1
    //     0xbb7334: stur            w1, [x0, #0x83]
    // 0xbb7338: StoreField: r0->field_7b = r2
    //     0xbb7338: stur            w2, [x0, #0x7b]
    // 0xbb733c: r0 = Visibility()
    //     0xbb733c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb7340: mov             x3, x0
    // 0xbb7344: ldur            x0, [fp, #-0x38]
    // 0xbb7348: stur            x3, [fp, #-0x40]
    // 0xbb734c: StoreField: r3->field_b = r0
    //     0xbb734c: stur            w0, [x3, #0xb]
    // 0xbb7350: r0 = Instance_SizedBox
    //     0xbb7350: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb7354: StoreField: r3->field_f = r0
    //     0xbb7354: stur            w0, [x3, #0xf]
    // 0xbb7358: ldur            x1, [fp, #-0x20]
    // 0xbb735c: StoreField: r3->field_13 = r1
    //     0xbb735c: stur            w1, [x3, #0x13]
    // 0xbb7360: r4 = false
    //     0xbb7360: add             x4, NULL, #0x30  ; false
    // 0xbb7364: ArrayStore: r3[0] = r4  ; List_4
    //     0xbb7364: stur            w4, [x3, #0x17]
    // 0xbb7368: StoreField: r3->field_1b = r4
    //     0xbb7368: stur            w4, [x3, #0x1b]
    // 0xbb736c: StoreField: r3->field_1f = r4
    //     0xbb736c: stur            w4, [x3, #0x1f]
    // 0xbb7370: StoreField: r3->field_23 = r4
    //     0xbb7370: stur            w4, [x3, #0x23]
    // 0xbb7374: StoreField: r3->field_27 = r4
    //     0xbb7374: stur            w4, [x3, #0x27]
    // 0xbb7378: StoreField: r3->field_2b = r4
    //     0xbb7378: stur            w4, [x3, #0x2b]
    // 0xbb737c: r1 = Null
    //     0xbb737c: mov             x1, NULL
    // 0xbb7380: r2 = 4
    //     0xbb7380: movz            x2, #0x4
    // 0xbb7384: r0 = AllocateArray()
    //     0xbb7384: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb7388: mov             x2, x0
    // 0xbb738c: ldur            x0, [fp, #-0x28]
    // 0xbb7390: stur            x2, [fp, #-0x20]
    // 0xbb7394: StoreField: r2->field_f = r0
    //     0xbb7394: stur            w0, [x2, #0xf]
    // 0xbb7398: ldur            x0, [fp, #-0x40]
    // 0xbb739c: StoreField: r2->field_13 = r0
    //     0xbb739c: stur            w0, [x2, #0x13]
    // 0xbb73a0: r1 = <Widget>
    //     0xbb73a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb73a4: r0 = AllocateGrowableArray()
    //     0xbb73a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb73a8: mov             x1, x0
    // 0xbb73ac: ldur            x0, [fp, #-0x20]
    // 0xbb73b0: stur            x1, [fp, #-0x28]
    // 0xbb73b4: StoreField: r1->field_f = r0
    //     0xbb73b4: stur            w0, [x1, #0xf]
    // 0xbb73b8: r2 = 4
    //     0xbb73b8: movz            x2, #0x4
    // 0xbb73bc: StoreField: r1->field_b = r2
    //     0xbb73bc: stur            w2, [x1, #0xb]
    // 0xbb73c0: r0 = Row()
    //     0xbb73c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb73c4: mov             x1, x0
    // 0xbb73c8: r0 = Instance_Axis
    //     0xbb73c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb73cc: stur            x1, [fp, #-0x20]
    // 0xbb73d0: StoreField: r1->field_f = r0
    //     0xbb73d0: stur            w0, [x1, #0xf]
    // 0xbb73d4: r0 = Instance_MainAxisAlignment
    //     0xbb73d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbb73d8: ldr             x0, [x0, #0xa8]
    // 0xbb73dc: StoreField: r1->field_13 = r0
    //     0xbb73dc: stur            w0, [x1, #0x13]
    // 0xbb73e0: r0 = Instance_MainAxisSize
    //     0xbb73e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb73e4: ldr             x0, [x0, #0xa10]
    // 0xbb73e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb73e8: stur            w0, [x1, #0x17]
    // 0xbb73ec: r2 = Instance_CrossAxisAlignment
    //     0xbb73ec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb73f0: ldr             x2, [x2, #0x890]
    // 0xbb73f4: StoreField: r1->field_1b = r2
    //     0xbb73f4: stur            w2, [x1, #0x1b]
    // 0xbb73f8: r3 = Instance_VerticalDirection
    //     0xbb73f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb73fc: ldr             x3, [x3, #0xa20]
    // 0xbb7400: StoreField: r1->field_23 = r3
    //     0xbb7400: stur            w3, [x1, #0x23]
    // 0xbb7404: r4 = Instance_Clip
    //     0xbb7404: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb7408: ldr             x4, [x4, #0x38]
    // 0xbb740c: StoreField: r1->field_2b = r4
    //     0xbb740c: stur            w4, [x1, #0x2b]
    // 0xbb7410: StoreField: r1->field_2f = rZR
    //     0xbb7410: stur            xzr, [x1, #0x2f]
    // 0xbb7414: ldur            x5, [fp, #-0x28]
    // 0xbb7418: StoreField: r1->field_b = r5
    //     0xbb7418: stur            w5, [x1, #0xb]
    // 0xbb741c: r0 = Padding()
    //     0xbb741c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb7420: mov             x3, x0
    // 0xbb7424: r0 = Instance_EdgeInsets
    //     0xbb7424: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xbb7428: ldr             x0, [x0, #0xd0]
    // 0xbb742c: stur            x3, [fp, #-0x38]
    // 0xbb7430: StoreField: r3->field_f = r0
    //     0xbb7430: stur            w0, [x3, #0xf]
    // 0xbb7434: ldur            x0, [fp, #-0x20]
    // 0xbb7438: StoreField: r3->field_b = r0
    //     0xbb7438: stur            w0, [x3, #0xb]
    // 0xbb743c: ldur            x4, [fp, #-8]
    // 0xbb7440: LoadField: r0 = r4->field_b
    //     0xbb7440: ldur            w0, [x4, #0xb]
    // 0xbb7444: DecompressPointer r0
    //     0xbb7444: add             x0, x0, HEAP, lsl #32
    // 0xbb7448: cmp             w0, NULL
    // 0xbb744c: b.eq            #0xbb7e24
    // 0xbb7450: LoadField: r1 = r0->field_13
    //     0xbb7450: ldur            w1, [x0, #0x13]
    // 0xbb7454: DecompressPointer r1
    //     0xbb7454: add             x1, x1, HEAP, lsl #32
    // 0xbb7458: LoadField: r5 = r1->field_1b
    //     0xbb7458: ldur            w5, [x1, #0x1b]
    // 0xbb745c: DecompressPointer r5
    //     0xbb745c: add             x5, x5, HEAP, lsl #32
    // 0xbb7460: stur            x5, [fp, #-0x28]
    // 0xbb7464: cmp             w5, NULL
    // 0xbb7468: b.ne            #0xbb7474
    // 0xbb746c: r0 = Null
    //     0xbb746c: mov             x0, NULL
    // 0xbb7470: b               #0xbb74b4
    // 0xbb7474: LoadField: r0 = r5->field_b
    //     0xbb7474: ldur            w0, [x5, #0xb]
    // 0xbb7478: r1 = LoadInt32Instr(r0)
    //     0xbb7478: sbfx            x1, x0, #1, #0x1f
    // 0xbb747c: mov             x0, x1
    // 0xbb7480: r1 = 0
    //     0xbb7480: movz            x1, #0
    // 0xbb7484: cmp             x1, x0
    // 0xbb7488: b.hs            #0xbb7e28
    // 0xbb748c: LoadField: r0 = r5->field_f
    //     0xbb748c: ldur            w0, [x5, #0xf]
    // 0xbb7490: DecompressPointer r0
    //     0xbb7490: add             x0, x0, HEAP, lsl #32
    // 0xbb7494: LoadField: r1 = r0->field_f
    //     0xbb7494: ldur            w1, [x0, #0xf]
    // 0xbb7498: DecompressPointer r1
    //     0xbb7498: add             x1, x1, HEAP, lsl #32
    // 0xbb749c: cmp             w1, NULL
    // 0xbb74a0: b.ne            #0xbb74ac
    // 0xbb74a4: r0 = Null
    //     0xbb74a4: mov             x0, NULL
    // 0xbb74a8: b               #0xbb74b4
    // 0xbb74ac: LoadField: r0 = r1->field_2b
    //     0xbb74ac: ldur            w0, [x1, #0x2b]
    // 0xbb74b0: DecompressPointer r0
    //     0xbb74b0: add             x0, x0, HEAP, lsl #32
    // 0xbb74b4: stur            x0, [fp, #-0x20]
    // 0xbb74b8: r1 = Null
    //     0xbb74b8: mov             x1, NULL
    // 0xbb74bc: r2 = 26
    //     0xbb74bc: movz            x2, #0x1a
    // 0xbb74c0: r0 = AllocateArray()
    //     0xbb74c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb74c4: mov             x3, x0
    // 0xbb74c8: ldur            x0, [fp, #-0x20]
    // 0xbb74cc: stur            x3, [fp, #-0x40]
    // 0xbb74d0: StoreField: r3->field_f = r0
    //     0xbb74d0: stur            w0, [x3, #0xf]
    // 0xbb74d4: r16 = " "
    //     0xbb74d4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb74d8: StoreField: r3->field_13 = r16
    //     0xbb74d8: stur            w16, [x3, #0x13]
    // 0xbb74dc: ldur            x4, [fp, #-0x28]
    // 0xbb74e0: cmp             w4, NULL
    // 0xbb74e4: b.ne            #0xbb74f0
    // 0xbb74e8: r0 = Null
    //     0xbb74e8: mov             x0, NULL
    // 0xbb74ec: b               #0xbb7530
    // 0xbb74f0: LoadField: r0 = r4->field_b
    //     0xbb74f0: ldur            w0, [x4, #0xb]
    // 0xbb74f4: r1 = LoadInt32Instr(r0)
    //     0xbb74f4: sbfx            x1, x0, #1, #0x1f
    // 0xbb74f8: mov             x0, x1
    // 0xbb74fc: r1 = 0
    //     0xbb74fc: movz            x1, #0
    // 0xbb7500: cmp             x1, x0
    // 0xbb7504: b.hs            #0xbb7e2c
    // 0xbb7508: LoadField: r0 = r4->field_f
    //     0xbb7508: ldur            w0, [x4, #0xf]
    // 0xbb750c: DecompressPointer r0
    //     0xbb750c: add             x0, x0, HEAP, lsl #32
    // 0xbb7510: LoadField: r1 = r0->field_f
    //     0xbb7510: ldur            w1, [x0, #0xf]
    // 0xbb7514: DecompressPointer r1
    //     0xbb7514: add             x1, x1, HEAP, lsl #32
    // 0xbb7518: cmp             w1, NULL
    // 0xbb751c: b.ne            #0xbb7528
    // 0xbb7520: r0 = Null
    //     0xbb7520: mov             x0, NULL
    // 0xbb7524: b               #0xbb7530
    // 0xbb7528: LoadField: r0 = r1->field_13
    //     0xbb7528: ldur            w0, [x1, #0x13]
    // 0xbb752c: DecompressPointer r0
    //     0xbb752c: add             x0, x0, HEAP, lsl #32
    // 0xbb7530: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb7530: stur            w0, [x3, #0x17]
    // 0xbb7534: r16 = " "
    //     0xbb7534: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb7538: StoreField: r3->field_1b = r16
    //     0xbb7538: stur            w16, [x3, #0x1b]
    // 0xbb753c: cmp             w4, NULL
    // 0xbb7540: b.ne            #0xbb754c
    // 0xbb7544: r0 = Null
    //     0xbb7544: mov             x0, NULL
    // 0xbb7548: b               #0xbb75b0
    // 0xbb754c: LoadField: r0 = r4->field_b
    //     0xbb754c: ldur            w0, [x4, #0xb]
    // 0xbb7550: r1 = LoadInt32Instr(r0)
    //     0xbb7550: sbfx            x1, x0, #1, #0x1f
    // 0xbb7554: mov             x0, x1
    // 0xbb7558: r1 = 0
    //     0xbb7558: movz            x1, #0
    // 0xbb755c: cmp             x1, x0
    // 0xbb7560: b.hs            #0xbb7e30
    // 0xbb7564: LoadField: r0 = r4->field_f
    //     0xbb7564: ldur            w0, [x4, #0xf]
    // 0xbb7568: DecompressPointer r0
    //     0xbb7568: add             x0, x0, HEAP, lsl #32
    // 0xbb756c: LoadField: r1 = r0->field_f
    //     0xbb756c: ldur            w1, [x0, #0xf]
    // 0xbb7570: DecompressPointer r1
    //     0xbb7570: add             x1, x1, HEAP, lsl #32
    // 0xbb7574: cmp             w1, NULL
    // 0xbb7578: b.ne            #0xbb7584
    // 0xbb757c: r0 = Null
    //     0xbb757c: mov             x0, NULL
    // 0xbb7580: b               #0xbb75b0
    // 0xbb7584: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb7584: ldur            w0, [x1, #0x17]
    // 0xbb7588: DecompressPointer r0
    //     0xbb7588: add             x0, x0, HEAP, lsl #32
    // 0xbb758c: cmp             w0, NULL
    // 0xbb7590: b.ne            #0xbb759c
    // 0xbb7594: r0 = Null
    //     0xbb7594: mov             x0, NULL
    // 0xbb7598: b               #0xbb75b0
    // 0xbb759c: LoadField: r1 = r0->field_7
    //     0xbb759c: ldur            w1, [x0, #7]
    // 0xbb75a0: cbnz            w1, #0xbb75ac
    // 0xbb75a4: r0 = false
    //     0xbb75a4: add             x0, NULL, #0x30  ; false
    // 0xbb75a8: b               #0xbb75b0
    // 0xbb75ac: r0 = true
    //     0xbb75ac: add             x0, NULL, #0x20  ; true
    // 0xbb75b0: cmp             w0, NULL
    // 0xbb75b4: b.eq            #0xbb7638
    // 0xbb75b8: tbnz            w0, #4, #0xbb7638
    // 0xbb75bc: r1 = Null
    //     0xbb75bc: mov             x1, NULL
    // 0xbb75c0: r2 = 4
    //     0xbb75c0: movz            x2, #0x4
    // 0xbb75c4: r0 = AllocateArray()
    //     0xbb75c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb75c8: mov             x2, x0
    // 0xbb75cc: r16 = ", "
    //     0xbb75cc: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xbb75d0: StoreField: r2->field_f = r16
    //     0xbb75d0: stur            w16, [x2, #0xf]
    // 0xbb75d4: ldur            x3, [fp, #-0x28]
    // 0xbb75d8: cmp             w3, NULL
    // 0xbb75dc: b.ne            #0xbb75e8
    // 0xbb75e0: r0 = Null
    //     0xbb75e0: mov             x0, NULL
    // 0xbb75e4: b               #0xbb7628
    // 0xbb75e8: LoadField: r0 = r3->field_b
    //     0xbb75e8: ldur            w0, [x3, #0xb]
    // 0xbb75ec: r1 = LoadInt32Instr(r0)
    //     0xbb75ec: sbfx            x1, x0, #1, #0x1f
    // 0xbb75f0: mov             x0, x1
    // 0xbb75f4: r1 = 0
    //     0xbb75f4: movz            x1, #0
    // 0xbb75f8: cmp             x1, x0
    // 0xbb75fc: b.hs            #0xbb7e34
    // 0xbb7600: LoadField: r0 = r3->field_f
    //     0xbb7600: ldur            w0, [x3, #0xf]
    // 0xbb7604: DecompressPointer r0
    //     0xbb7604: add             x0, x0, HEAP, lsl #32
    // 0xbb7608: LoadField: r1 = r0->field_f
    //     0xbb7608: ldur            w1, [x0, #0xf]
    // 0xbb760c: DecompressPointer r1
    //     0xbb760c: add             x1, x1, HEAP, lsl #32
    // 0xbb7610: cmp             w1, NULL
    // 0xbb7614: b.ne            #0xbb7620
    // 0xbb7618: r0 = Null
    //     0xbb7618: mov             x0, NULL
    // 0xbb761c: b               #0xbb7628
    // 0xbb7620: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb7620: ldur            w0, [x1, #0x17]
    // 0xbb7624: DecompressPointer r0
    //     0xbb7624: add             x0, x0, HEAP, lsl #32
    // 0xbb7628: StoreField: r2->field_13 = r0
    //     0xbb7628: stur            w0, [x2, #0x13]
    // 0xbb762c: str             x2, [SP]
    // 0xbb7630: r0 = _interpolate()
    //     0xbb7630: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb7634: b               #0xbb763c
    // 0xbb7638: r0 = ""
    //     0xbb7638: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb763c: ldur            x4, [fp, #-8]
    // 0xbb7640: ldur            x3, [fp, #-0x40]
    // 0xbb7644: mov             x1, x3
    // 0xbb7648: ArrayStore: r1[4] = r0  ; List_4
    //     0xbb7648: add             x25, x1, #0x1f
    //     0xbb764c: str             w0, [x25]
    //     0xbb7650: tbz             w0, #0, #0xbb766c
    //     0xbb7654: ldurb           w16, [x1, #-1]
    //     0xbb7658: ldurb           w17, [x0, #-1]
    //     0xbb765c: and             x16, x17, x16, lsr #2
    //     0xbb7660: tst             x16, HEAP, lsr #32
    //     0xbb7664: b.eq            #0xbb766c
    //     0xbb7668: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb766c: r16 = " "
    //     0xbb766c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb7670: StoreField: r3->field_23 = r16
    //     0xbb7670: stur            w16, [x3, #0x23]
    // 0xbb7674: LoadField: r5 = r4->field_b
    //     0xbb7674: ldur            w5, [x4, #0xb]
    // 0xbb7678: DecompressPointer r5
    //     0xbb7678: add             x5, x5, HEAP, lsl #32
    // 0xbb767c: stur            x5, [fp, #-0x28]
    // 0xbb7680: cmp             w5, NULL
    // 0xbb7684: b.eq            #0xbb7e38
    // 0xbb7688: LoadField: r0 = r5->field_13
    //     0xbb7688: ldur            w0, [x5, #0x13]
    // 0xbb768c: DecompressPointer r0
    //     0xbb768c: add             x0, x0, HEAP, lsl #32
    // 0xbb7690: LoadField: r6 = r0->field_1b
    //     0xbb7690: ldur            w6, [x0, #0x1b]
    // 0xbb7694: DecompressPointer r6
    //     0xbb7694: add             x6, x6, HEAP, lsl #32
    // 0xbb7698: stur            x6, [fp, #-0x20]
    // 0xbb769c: cmp             w6, NULL
    // 0xbb76a0: b.ne            #0xbb76ac
    // 0xbb76a4: r0 = Null
    //     0xbb76a4: mov             x0, NULL
    // 0xbb76a8: b               #0xbb76ec
    // 0xbb76ac: LoadField: r0 = r6->field_b
    //     0xbb76ac: ldur            w0, [x6, #0xb]
    // 0xbb76b0: r1 = LoadInt32Instr(r0)
    //     0xbb76b0: sbfx            x1, x0, #1, #0x1f
    // 0xbb76b4: mov             x0, x1
    // 0xbb76b8: r1 = 0
    //     0xbb76b8: movz            x1, #0
    // 0xbb76bc: cmp             x1, x0
    // 0xbb76c0: b.hs            #0xbb7e3c
    // 0xbb76c4: LoadField: r0 = r6->field_f
    //     0xbb76c4: ldur            w0, [x6, #0xf]
    // 0xbb76c8: DecompressPointer r0
    //     0xbb76c8: add             x0, x0, HEAP, lsl #32
    // 0xbb76cc: LoadField: r1 = r0->field_f
    //     0xbb76cc: ldur            w1, [x0, #0xf]
    // 0xbb76d0: DecompressPointer r1
    //     0xbb76d0: add             x1, x1, HEAP, lsl #32
    // 0xbb76d4: cmp             w1, NULL
    // 0xbb76d8: b.ne            #0xbb76e4
    // 0xbb76dc: r0 = Null
    //     0xbb76dc: mov             x0, NULL
    // 0xbb76e0: b               #0xbb76ec
    // 0xbb76e4: LoadField: r0 = r1->field_1f
    //     0xbb76e4: ldur            w0, [x1, #0x1f]
    // 0xbb76e8: DecompressPointer r0
    //     0xbb76e8: add             x0, x0, HEAP, lsl #32
    // 0xbb76ec: mov             x1, x3
    // 0xbb76f0: ArrayStore: r1[6] = r0  ; List_4
    //     0xbb76f0: add             x25, x1, #0x27
    //     0xbb76f4: str             w0, [x25]
    //     0xbb76f8: tbz             w0, #0, #0xbb7714
    //     0xbb76fc: ldurb           w16, [x1, #-1]
    //     0xbb7700: ldurb           w17, [x0, #-1]
    //     0xbb7704: and             x16, x17, x16, lsr #2
    //     0xbb7708: tst             x16, HEAP, lsr #32
    //     0xbb770c: b.eq            #0xbb7714
    //     0xbb7710: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb7714: r16 = ", "
    //     0xbb7714: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xbb7718: StoreField: r3->field_2b = r16
    //     0xbb7718: stur            w16, [x3, #0x2b]
    // 0xbb771c: cmp             w6, NULL
    // 0xbb7720: b.ne            #0xbb772c
    // 0xbb7724: r0 = Null
    //     0xbb7724: mov             x0, NULL
    // 0xbb7728: b               #0xbb776c
    // 0xbb772c: LoadField: r0 = r6->field_b
    //     0xbb772c: ldur            w0, [x6, #0xb]
    // 0xbb7730: r1 = LoadInt32Instr(r0)
    //     0xbb7730: sbfx            x1, x0, #1, #0x1f
    // 0xbb7734: mov             x0, x1
    // 0xbb7738: r1 = 0
    //     0xbb7738: movz            x1, #0
    // 0xbb773c: cmp             x1, x0
    // 0xbb7740: b.hs            #0xbb7e40
    // 0xbb7744: LoadField: r0 = r6->field_f
    //     0xbb7744: ldur            w0, [x6, #0xf]
    // 0xbb7748: DecompressPointer r0
    //     0xbb7748: add             x0, x0, HEAP, lsl #32
    // 0xbb774c: LoadField: r1 = r0->field_f
    //     0xbb774c: ldur            w1, [x0, #0xf]
    // 0xbb7750: DecompressPointer r1
    //     0xbb7750: add             x1, x1, HEAP, lsl #32
    // 0xbb7754: cmp             w1, NULL
    // 0xbb7758: b.ne            #0xbb7764
    // 0xbb775c: r0 = Null
    //     0xbb775c: mov             x0, NULL
    // 0xbb7760: b               #0xbb776c
    // 0xbb7764: LoadField: r0 = r1->field_23
    //     0xbb7764: ldur            w0, [x1, #0x23]
    // 0xbb7768: DecompressPointer r0
    //     0xbb7768: add             x0, x0, HEAP, lsl #32
    // 0xbb776c: mov             x1, x3
    // 0xbb7770: ArrayStore: r1[8] = r0  ; List_4
    //     0xbb7770: add             x25, x1, #0x2f
    //     0xbb7774: str             w0, [x25]
    //     0xbb7778: tbz             w0, #0, #0xbb7794
    //     0xbb777c: ldurb           w16, [x1, #-1]
    //     0xbb7780: ldurb           w17, [x0, #-1]
    //     0xbb7784: and             x16, x17, x16, lsr #2
    //     0xbb7788: tst             x16, HEAP, lsr #32
    //     0xbb778c: b.eq            #0xbb7794
    //     0xbb7790: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb7794: r16 = ", "
    //     0xbb7794: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xbb7798: StoreField: r3->field_33 = r16
    //     0xbb7798: stur            w16, [x3, #0x33]
    // 0xbb779c: cmp             w6, NULL
    // 0xbb77a0: b.ne            #0xbb77ac
    // 0xbb77a4: r0 = Null
    //     0xbb77a4: mov             x0, NULL
    // 0xbb77a8: b               #0xbb77ec
    // 0xbb77ac: LoadField: r0 = r6->field_b
    //     0xbb77ac: ldur            w0, [x6, #0xb]
    // 0xbb77b0: r1 = LoadInt32Instr(r0)
    //     0xbb77b0: sbfx            x1, x0, #1, #0x1f
    // 0xbb77b4: mov             x0, x1
    // 0xbb77b8: r1 = 0
    //     0xbb77b8: movz            x1, #0
    // 0xbb77bc: cmp             x1, x0
    // 0xbb77c0: b.hs            #0xbb7e44
    // 0xbb77c4: LoadField: r0 = r6->field_f
    //     0xbb77c4: ldur            w0, [x6, #0xf]
    // 0xbb77c8: DecompressPointer r0
    //     0xbb77c8: add             x0, x0, HEAP, lsl #32
    // 0xbb77cc: LoadField: r1 = r0->field_f
    //     0xbb77cc: ldur            w1, [x0, #0xf]
    // 0xbb77d0: DecompressPointer r1
    //     0xbb77d0: add             x1, x1, HEAP, lsl #32
    // 0xbb77d4: cmp             w1, NULL
    // 0xbb77d8: b.ne            #0xbb77e4
    // 0xbb77dc: r0 = Null
    //     0xbb77dc: mov             x0, NULL
    // 0xbb77e0: b               #0xbb77ec
    // 0xbb77e4: LoadField: r0 = r1->field_1b
    //     0xbb77e4: ldur            w0, [x1, #0x1b]
    // 0xbb77e8: DecompressPointer r0
    //     0xbb77e8: add             x0, x0, HEAP, lsl #32
    // 0xbb77ec: mov             x1, x3
    // 0xbb77f0: ArrayStore: r1[10] = r0  ; List_4
    //     0xbb77f0: add             x25, x1, #0x37
    //     0xbb77f4: str             w0, [x25]
    //     0xbb77f8: tbz             w0, #0, #0xbb7814
    //     0xbb77fc: ldurb           w16, [x1, #-1]
    //     0xbb7800: ldurb           w17, [x0, #-1]
    //     0xbb7804: and             x16, x17, x16, lsr #2
    //     0xbb7808: tst             x16, HEAP, lsr #32
    //     0xbb780c: b.eq            #0xbb7814
    //     0xbb7810: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb7814: r16 = "\n"
    //     0xbb7814: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xbb7818: StoreField: r3->field_3b = r16
    //     0xbb7818: stur            w16, [x3, #0x3b]
    // 0xbb781c: cmp             w6, NULL
    // 0xbb7820: b.ne            #0xbb782c
    // 0xbb7824: r0 = Null
    //     0xbb7824: mov             x0, NULL
    // 0xbb7828: b               #0xbb7890
    // 0xbb782c: LoadField: r0 = r6->field_b
    //     0xbb782c: ldur            w0, [x6, #0xb]
    // 0xbb7830: r1 = LoadInt32Instr(r0)
    //     0xbb7830: sbfx            x1, x0, #1, #0x1f
    // 0xbb7834: mov             x0, x1
    // 0xbb7838: r1 = 0
    //     0xbb7838: movz            x1, #0
    // 0xbb783c: cmp             x1, x0
    // 0xbb7840: b.hs            #0xbb7e48
    // 0xbb7844: LoadField: r0 = r6->field_f
    //     0xbb7844: ldur            w0, [x6, #0xf]
    // 0xbb7848: DecompressPointer r0
    //     0xbb7848: add             x0, x0, HEAP, lsl #32
    // 0xbb784c: LoadField: r1 = r0->field_f
    //     0xbb784c: ldur            w1, [x0, #0xf]
    // 0xbb7850: DecompressPointer r1
    //     0xbb7850: add             x1, x1, HEAP, lsl #32
    // 0xbb7854: cmp             w1, NULL
    // 0xbb7858: b.ne            #0xbb7864
    // 0xbb785c: r0 = Null
    //     0xbb785c: mov             x0, NULL
    // 0xbb7860: b               #0xbb7890
    // 0xbb7864: LoadField: r0 = r1->field_2f
    //     0xbb7864: ldur            w0, [x1, #0x2f]
    // 0xbb7868: DecompressPointer r0
    //     0xbb7868: add             x0, x0, HEAP, lsl #32
    // 0xbb786c: cmp             w0, NULL
    // 0xbb7870: b.ne            #0xbb787c
    // 0xbb7874: r0 = Null
    //     0xbb7874: mov             x0, NULL
    // 0xbb7878: b               #0xbb7890
    // 0xbb787c: LoadField: r1 = r0->field_7
    //     0xbb787c: ldur            w1, [x0, #7]
    // 0xbb7880: cbnz            w1, #0xbb788c
    // 0xbb7884: r0 = false
    //     0xbb7884: add             x0, NULL, #0x30  ; false
    // 0xbb7888: b               #0xbb7890
    // 0xbb788c: r0 = true
    //     0xbb788c: add             x0, NULL, #0x20  ; true
    // 0xbb7890: cmp             w0, NULL
    // 0xbb7894: b.ne            #0xbb78a0
    // 0xbb7898: mov             x0, x5
    // 0xbb789c: b               #0xbb7940
    // 0xbb78a0: tbnz            w0, #4, #0xbb793c
    // 0xbb78a4: r1 = Null
    //     0xbb78a4: mov             x1, NULL
    // 0xbb78a8: r2 = 8
    //     0xbb78a8: movz            x2, #0x8
    // 0xbb78ac: r0 = AllocateArray()
    //     0xbb78ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb78b0: mov             x2, x0
    // 0xbb78b4: r16 = "Contact Number: "
    //     0xbb78b4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54790] "Contact Number: "
    //     0xbb78b8: ldr             x16, [x16, #0x790]
    // 0xbb78bc: StoreField: r2->field_f = r16
    //     0xbb78bc: stur            w16, [x2, #0xf]
    // 0xbb78c0: ldur            x0, [fp, #-0x28]
    // 0xbb78c4: LoadField: r1 = r0->field_b
    //     0xbb78c4: ldur            w1, [x0, #0xb]
    // 0xbb78c8: DecompressPointer r1
    //     0xbb78c8: add             x1, x1, HEAP, lsl #32
    // 0xbb78cc: StoreField: r2->field_13 = r1
    //     0xbb78cc: stur            w1, [x2, #0x13]
    // 0xbb78d0: r16 = ", "
    //     0xbb78d0: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0xbb78d4: ArrayStore: r2[0] = r16  ; List_4
    //     0xbb78d4: stur            w16, [x2, #0x17]
    // 0xbb78d8: ldur            x3, [fp, #-0x20]
    // 0xbb78dc: cmp             w3, NULL
    // 0xbb78e0: b.ne            #0xbb78ec
    // 0xbb78e4: r0 = Null
    //     0xbb78e4: mov             x0, NULL
    // 0xbb78e8: b               #0xbb792c
    // 0xbb78ec: LoadField: r0 = r3->field_b
    //     0xbb78ec: ldur            w0, [x3, #0xb]
    // 0xbb78f0: r1 = LoadInt32Instr(r0)
    //     0xbb78f0: sbfx            x1, x0, #1, #0x1f
    // 0xbb78f4: mov             x0, x1
    // 0xbb78f8: r1 = 0
    //     0xbb78f8: movz            x1, #0
    // 0xbb78fc: cmp             x1, x0
    // 0xbb7900: b.hs            #0xbb7e4c
    // 0xbb7904: LoadField: r0 = r3->field_f
    //     0xbb7904: ldur            w0, [x3, #0xf]
    // 0xbb7908: DecompressPointer r0
    //     0xbb7908: add             x0, x0, HEAP, lsl #32
    // 0xbb790c: LoadField: r1 = r0->field_f
    //     0xbb790c: ldur            w1, [x0, #0xf]
    // 0xbb7910: DecompressPointer r1
    //     0xbb7910: add             x1, x1, HEAP, lsl #32
    // 0xbb7914: cmp             w1, NULL
    // 0xbb7918: b.ne            #0xbb7924
    // 0xbb791c: r0 = Null
    //     0xbb791c: mov             x0, NULL
    // 0xbb7920: b               #0xbb792c
    // 0xbb7924: LoadField: r0 = r1->field_2f
    //     0xbb7924: ldur            w0, [x1, #0x2f]
    // 0xbb7928: DecompressPointer r0
    //     0xbb7928: add             x0, x0, HEAP, lsl #32
    // 0xbb792c: StoreField: r2->field_1b = r0
    //     0xbb792c: stur            w0, [x2, #0x1b]
    // 0xbb7930: str             x2, [SP]
    // 0xbb7934: r0 = _interpolate()
    //     0xbb7934: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb7938: b               #0xbb7970
    // 0xbb793c: mov             x0, x5
    // 0xbb7940: r1 = Null
    //     0xbb7940: mov             x1, NULL
    // 0xbb7944: r2 = 4
    //     0xbb7944: movz            x2, #0x4
    // 0xbb7948: r0 = AllocateArray()
    //     0xbb7948: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb794c: r16 = "Contact Number: "
    //     0xbb794c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54790] "Contact Number: "
    //     0xbb7950: ldr             x16, [x16, #0x790]
    // 0xbb7954: StoreField: r0->field_f = r16
    //     0xbb7954: stur            w16, [x0, #0xf]
    // 0xbb7958: ldur            x1, [fp, #-0x28]
    // 0xbb795c: LoadField: r2 = r1->field_b
    //     0xbb795c: ldur            w2, [x1, #0xb]
    // 0xbb7960: DecompressPointer r2
    //     0xbb7960: add             x2, x2, HEAP, lsl #32
    // 0xbb7964: StoreField: r0->field_13 = r2
    //     0xbb7964: stur            w2, [x0, #0x13]
    // 0xbb7968: str             x0, [SP]
    // 0xbb796c: r0 = _interpolate()
    //     0xbb796c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb7970: ldur            x2, [fp, #-8]
    // 0xbb7974: ldur            x3, [fp, #-0x18]
    // 0xbb7978: ldur            x1, [fp, #-0x40]
    // 0xbb797c: ArrayStore: r1[12] = r0  ; List_4
    //     0xbb797c: add             x25, x1, #0x3f
    //     0xbb7980: str             w0, [x25]
    //     0xbb7984: tbz             w0, #0, #0xbb79a0
    //     0xbb7988: ldurb           w16, [x1, #-1]
    //     0xbb798c: ldurb           w17, [x0, #-1]
    //     0xbb7990: and             x16, x17, x16, lsr #2
    //     0xbb7994: tst             x16, HEAP, lsr #32
    //     0xbb7998: b.eq            #0xbb79a0
    //     0xbb799c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb79a0: ldur            x16, [fp, #-0x40]
    // 0xbb79a4: str             x16, [SP]
    // 0xbb79a8: r0 = _interpolate()
    //     0xbb79a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb79ac: mov             x2, x0
    // 0xbb79b0: ldur            x0, [fp, #-0x18]
    // 0xbb79b4: stur            x2, [fp, #-0x20]
    // 0xbb79b8: LoadField: r1 = r0->field_13
    //     0xbb79b8: ldur            w1, [x0, #0x13]
    // 0xbb79bc: DecompressPointer r1
    //     0xbb79bc: add             x1, x1, HEAP, lsl #32
    // 0xbb79c0: r0 = of()
    //     0xbb79c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb79c4: LoadField: r1 = r0->field_87
    //     0xbb79c4: ldur            w1, [x0, #0x87]
    // 0xbb79c8: DecompressPointer r1
    //     0xbb79c8: add             x1, x1, HEAP, lsl #32
    // 0xbb79cc: LoadField: r0 = r1->field_2b
    //     0xbb79cc: ldur            w0, [x1, #0x2b]
    // 0xbb79d0: DecompressPointer r0
    //     0xbb79d0: add             x0, x0, HEAP, lsl #32
    // 0xbb79d4: stur            x0, [fp, #-0x28]
    // 0xbb79d8: r1 = Instance_Color
    //     0xbb79d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb79dc: d0 = 0.700000
    //     0xbb79dc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb79e0: ldr             d0, [x17, #0xf48]
    // 0xbb79e4: r0 = withOpacity()
    //     0xbb79e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb79e8: r16 = 12.000000
    //     0xbb79e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb79ec: ldr             x16, [x16, #0x9e8]
    // 0xbb79f0: stp             x16, x0, [SP]
    // 0xbb79f4: ldur            x1, [fp, #-0x28]
    // 0xbb79f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb79f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb79fc: ldr             x4, [x4, #0x9b8]
    // 0xbb7a00: r0 = copyWith()
    //     0xbb7a00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb7a04: stur            x0, [fp, #-0x28]
    // 0xbb7a08: r0 = Text()
    //     0xbb7a08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb7a0c: mov             x1, x0
    // 0xbb7a10: ldur            x0, [fp, #-0x20]
    // 0xbb7a14: stur            x1, [fp, #-0x40]
    // 0xbb7a18: StoreField: r1->field_b = r0
    //     0xbb7a18: stur            w0, [x1, #0xb]
    // 0xbb7a1c: ldur            x0, [fp, #-0x28]
    // 0xbb7a20: StoreField: r1->field_13 = r0
    //     0xbb7a20: stur            w0, [x1, #0x13]
    // 0xbb7a24: r0 = Padding()
    //     0xbb7a24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb7a28: mov             x2, x0
    // 0xbb7a2c: r0 = Instance_EdgeInsets
    //     0xbb7a2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0xbb7a30: ldr             x0, [x0, #0xe68]
    // 0xbb7a34: stur            x2, [fp, #-0x28]
    // 0xbb7a38: StoreField: r2->field_f = r0
    //     0xbb7a38: stur            w0, [x2, #0xf]
    // 0xbb7a3c: ldur            x0, [fp, #-0x40]
    // 0xbb7a40: StoreField: r2->field_b = r0
    //     0xbb7a40: stur            w0, [x2, #0xb]
    // 0xbb7a44: ldur            x0, [fp, #-8]
    // 0xbb7a48: LoadField: r1 = r0->field_b
    //     0xbb7a48: ldur            w1, [x0, #0xb]
    // 0xbb7a4c: DecompressPointer r1
    //     0xbb7a4c: add             x1, x1, HEAP, lsl #32
    // 0xbb7a50: cmp             w1, NULL
    // 0xbb7a54: b.eq            #0xbb7e50
    // 0xbb7a58: LoadField: r3 = r1->field_f
    //     0xbb7a58: ldur            w3, [x1, #0xf]
    // 0xbb7a5c: DecompressPointer r3
    //     0xbb7a5c: add             x3, x3, HEAP, lsl #32
    // 0xbb7a60: LoadField: r1 = r3->field_b
    //     0xbb7a60: ldur            w1, [x3, #0xb]
    // 0xbb7a64: DecompressPointer r1
    //     0xbb7a64: add             x1, x1, HEAP, lsl #32
    // 0xbb7a68: cmp             w1, NULL
    // 0xbb7a6c: b.ne            #0xbb7a78
    // 0xbb7a70: r1 = Null
    //     0xbb7a70: mov             x1, NULL
    // 0xbb7a74: b               #0xbb7aa8
    // 0xbb7a78: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbb7a78: ldur            w3, [x1, #0x17]
    // 0xbb7a7c: DecompressPointer r3
    //     0xbb7a7c: add             x3, x3, HEAP, lsl #32
    // 0xbb7a80: cmp             w3, NULL
    // 0xbb7a84: b.ne            #0xbb7a90
    // 0xbb7a88: r1 = Null
    //     0xbb7a88: mov             x1, NULL
    // 0xbb7a8c: b               #0xbb7aa8
    // 0xbb7a90: LoadField: r1 = r3->field_7
    //     0xbb7a90: ldur            w1, [x3, #7]
    // 0xbb7a94: cbnz            w1, #0xbb7aa0
    // 0xbb7a98: r3 = false
    //     0xbb7a98: add             x3, NULL, #0x30  ; false
    // 0xbb7a9c: b               #0xbb7aa4
    // 0xbb7aa0: r3 = true
    //     0xbb7aa0: add             x3, NULL, #0x20  ; true
    // 0xbb7aa4: mov             x1, x3
    // 0xbb7aa8: cmp             w1, NULL
    // 0xbb7aac: b.ne            #0xbb7ab8
    // 0xbb7ab0: r4 = false
    //     0xbb7ab0: add             x4, NULL, #0x30  ; false
    // 0xbb7ab4: b               #0xbb7abc
    // 0xbb7ab8: mov             x4, x1
    // 0xbb7abc: ldur            x3, [fp, #-0x18]
    // 0xbb7ac0: stur            x4, [fp, #-0x20]
    // 0xbb7ac4: LoadField: r1 = r3->field_13
    //     0xbb7ac4: ldur            w1, [x3, #0x13]
    // 0xbb7ac8: DecompressPointer r1
    //     0xbb7ac8: add             x1, x1, HEAP, lsl #32
    // 0xbb7acc: r0 = of()
    //     0xbb7acc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb7ad0: LoadField: r1 = r0->field_5b
    //     0xbb7ad0: ldur            w1, [x0, #0x5b]
    // 0xbb7ad4: DecompressPointer r1
    //     0xbb7ad4: add             x1, x1, HEAP, lsl #32
    // 0xbb7ad8: r0 = LoadClassIdInstr(r1)
    //     0xbb7ad8: ldur            x0, [x1, #-1]
    //     0xbb7adc: ubfx            x0, x0, #0xc, #0x14
    // 0xbb7ae0: d0 = 0.030000
    //     0xbb7ae0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbb7ae4: ldr             d0, [x17, #0x238]
    // 0xbb7ae8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb7ae8: sub             lr, x0, #0xffa
    //     0xbb7aec: ldr             lr, [x21, lr, lsl #3]
    //     0xbb7af0: blr             lr
    // 0xbb7af4: mov             x2, x0
    // 0xbb7af8: ldur            x0, [fp, #-8]
    // 0xbb7afc: stur            x2, [fp, #-0x40]
    // 0xbb7b00: LoadField: r1 = r0->field_b
    //     0xbb7b00: ldur            w1, [x0, #0xb]
    // 0xbb7b04: DecompressPointer r1
    //     0xbb7b04: add             x1, x1, HEAP, lsl #32
    // 0xbb7b08: cmp             w1, NULL
    // 0xbb7b0c: b.eq            #0xbb7e54
    // 0xbb7b10: LoadField: r0 = r1->field_f
    //     0xbb7b10: ldur            w0, [x1, #0xf]
    // 0xbb7b14: DecompressPointer r0
    //     0xbb7b14: add             x0, x0, HEAP, lsl #32
    // 0xbb7b18: LoadField: r1 = r0->field_b
    //     0xbb7b18: ldur            w1, [x0, #0xb]
    // 0xbb7b1c: DecompressPointer r1
    //     0xbb7b1c: add             x1, x1, HEAP, lsl #32
    // 0xbb7b20: cmp             w1, NULL
    // 0xbb7b24: b.ne            #0xbb7b30
    // 0xbb7b28: r0 = Null
    //     0xbb7b28: mov             x0, NULL
    // 0xbb7b2c: b               #0xbb7b38
    // 0xbb7b30: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb7b30: ldur            w0, [x1, #0x17]
    // 0xbb7b34: DecompressPointer r0
    //     0xbb7b34: add             x0, x0, HEAP, lsl #32
    // 0xbb7b38: cmp             w0, NULL
    // 0xbb7b3c: b.ne            #0xbb7b48
    // 0xbb7b40: r6 = ""
    //     0xbb7b40: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb7b44: b               #0xbb7b4c
    // 0xbb7b48: mov             x6, x0
    // 0xbb7b4c: ldur            x1, [fp, #-0x18]
    // 0xbb7b50: ldur            x5, [fp, #-0x10]
    // 0xbb7b54: ldur            x4, [fp, #-0x38]
    // 0xbb7b58: ldur            x0, [fp, #-0x28]
    // 0xbb7b5c: ldur            x3, [fp, #-0x20]
    // 0xbb7b60: stur            x6, [fp, #-8]
    // 0xbb7b64: LoadField: r7 = r1->field_13
    //     0xbb7b64: ldur            w7, [x1, #0x13]
    // 0xbb7b68: DecompressPointer r7
    //     0xbb7b68: add             x7, x7, HEAP, lsl #32
    // 0xbb7b6c: mov             x1, x7
    // 0xbb7b70: r0 = of()
    //     0xbb7b70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb7b74: LoadField: r1 = r0->field_87
    //     0xbb7b74: ldur            w1, [x0, #0x87]
    // 0xbb7b78: DecompressPointer r1
    //     0xbb7b78: add             x1, x1, HEAP, lsl #32
    // 0xbb7b7c: LoadField: r0 = r1->field_7
    //     0xbb7b7c: ldur            w0, [x1, #7]
    // 0xbb7b80: DecompressPointer r0
    //     0xbb7b80: add             x0, x0, HEAP, lsl #32
    // 0xbb7b84: stur            x0, [fp, #-0x18]
    // 0xbb7b88: r1 = Instance_Color
    //     0xbb7b88: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb7b8c: d0 = 0.700000
    //     0xbb7b8c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb7b90: ldr             d0, [x17, #0xf48]
    // 0xbb7b94: r0 = withOpacity()
    //     0xbb7b94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb7b98: r16 = 12.000000
    //     0xbb7b98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb7b9c: ldr             x16, [x16, #0x9e8]
    // 0xbb7ba0: stp             x16, x0, [SP]
    // 0xbb7ba4: ldur            x1, [fp, #-0x18]
    // 0xbb7ba8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbb7ba8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbb7bac: ldr             x4, [x4, #0x9b8]
    // 0xbb7bb0: r0 = copyWith()
    //     0xbb7bb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb7bb4: stur            x0, [fp, #-0x18]
    // 0xbb7bb8: r0 = Text()
    //     0xbb7bb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb7bbc: mov             x1, x0
    // 0xbb7bc0: ldur            x0, [fp, #-8]
    // 0xbb7bc4: stur            x1, [fp, #-0x48]
    // 0xbb7bc8: StoreField: r1->field_b = r0
    //     0xbb7bc8: stur            w0, [x1, #0xb]
    // 0xbb7bcc: ldur            x0, [fp, #-0x18]
    // 0xbb7bd0: StoreField: r1->field_13 = r0
    //     0xbb7bd0: stur            w0, [x1, #0x13]
    // 0xbb7bd4: r0 = Center()
    //     0xbb7bd4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbb7bd8: mov             x1, x0
    // 0xbb7bdc: r0 = Instance_Alignment
    //     0xbb7bdc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbb7be0: ldr             x0, [x0, #0xb10]
    // 0xbb7be4: stur            x1, [fp, #-8]
    // 0xbb7be8: StoreField: r1->field_f = r0
    //     0xbb7be8: stur            w0, [x1, #0xf]
    // 0xbb7bec: ldur            x0, [fp, #-0x48]
    // 0xbb7bf0: StoreField: r1->field_b = r0
    //     0xbb7bf0: stur            w0, [x1, #0xb]
    // 0xbb7bf4: r0 = Container()
    //     0xbb7bf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb7bf8: stur            x0, [fp, #-0x18]
    // 0xbb7bfc: r16 = 24.000000
    //     0xbb7bfc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbb7c00: ldr             x16, [x16, #0xba8]
    // 0xbb7c04: ldur            lr, [fp, #-0x40]
    // 0xbb7c08: stp             lr, x16, [SP, #8]
    // 0xbb7c0c: ldur            x16, [fp, #-8]
    // 0xbb7c10: str             x16, [SP]
    // 0xbb7c14: mov             x1, x0
    // 0xbb7c18: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, height, 0x1, null]
    //     0xbb7c18: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "height", 0x1, Null]
    //     0xbb7c1c: ldr             x4, [x4, #0xd50]
    // 0xbb7c20: r0 = Container()
    //     0xbb7c20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb7c24: r0 = Visibility()
    //     0xbb7c24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb7c28: mov             x3, x0
    // 0xbb7c2c: ldur            x0, [fp, #-0x18]
    // 0xbb7c30: stur            x3, [fp, #-8]
    // 0xbb7c34: StoreField: r3->field_b = r0
    //     0xbb7c34: stur            w0, [x3, #0xb]
    // 0xbb7c38: r0 = Instance_SizedBox
    //     0xbb7c38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb7c3c: StoreField: r3->field_f = r0
    //     0xbb7c3c: stur            w0, [x3, #0xf]
    // 0xbb7c40: ldur            x0, [fp, #-0x20]
    // 0xbb7c44: StoreField: r3->field_13 = r0
    //     0xbb7c44: stur            w0, [x3, #0x13]
    // 0xbb7c48: r0 = false
    //     0xbb7c48: add             x0, NULL, #0x30  ; false
    // 0xbb7c4c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb7c4c: stur            w0, [x3, #0x17]
    // 0xbb7c50: StoreField: r3->field_1b = r0
    //     0xbb7c50: stur            w0, [x3, #0x1b]
    // 0xbb7c54: StoreField: r3->field_1f = r0
    //     0xbb7c54: stur            w0, [x3, #0x1f]
    // 0xbb7c58: StoreField: r3->field_23 = r0
    //     0xbb7c58: stur            w0, [x3, #0x23]
    // 0xbb7c5c: StoreField: r3->field_27 = r0
    //     0xbb7c5c: stur            w0, [x3, #0x27]
    // 0xbb7c60: StoreField: r3->field_2b = r0
    //     0xbb7c60: stur            w0, [x3, #0x2b]
    // 0xbb7c64: r1 = Null
    //     0xbb7c64: mov             x1, NULL
    // 0xbb7c68: r2 = 6
    //     0xbb7c68: movz            x2, #0x6
    // 0xbb7c6c: r0 = AllocateArray()
    //     0xbb7c6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb7c70: mov             x2, x0
    // 0xbb7c74: ldur            x0, [fp, #-0x38]
    // 0xbb7c78: stur            x2, [fp, #-0x18]
    // 0xbb7c7c: StoreField: r2->field_f = r0
    //     0xbb7c7c: stur            w0, [x2, #0xf]
    // 0xbb7c80: ldur            x0, [fp, #-0x28]
    // 0xbb7c84: StoreField: r2->field_13 = r0
    //     0xbb7c84: stur            w0, [x2, #0x13]
    // 0xbb7c88: ldur            x0, [fp, #-8]
    // 0xbb7c8c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb7c8c: stur            w0, [x2, #0x17]
    // 0xbb7c90: r1 = <Widget>
    //     0xbb7c90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb7c94: r0 = AllocateGrowableArray()
    //     0xbb7c94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb7c98: mov             x1, x0
    // 0xbb7c9c: ldur            x0, [fp, #-0x18]
    // 0xbb7ca0: stur            x1, [fp, #-8]
    // 0xbb7ca4: StoreField: r1->field_f = r0
    //     0xbb7ca4: stur            w0, [x1, #0xf]
    // 0xbb7ca8: r0 = 6
    //     0xbb7ca8: movz            x0, #0x6
    // 0xbb7cac: StoreField: r1->field_b = r0
    //     0xbb7cac: stur            w0, [x1, #0xb]
    // 0xbb7cb0: r0 = Column()
    //     0xbb7cb0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb7cb4: mov             x1, x0
    // 0xbb7cb8: r0 = Instance_Axis
    //     0xbb7cb8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb7cbc: stur            x1, [fp, #-0x18]
    // 0xbb7cc0: StoreField: r1->field_f = r0
    //     0xbb7cc0: stur            w0, [x1, #0xf]
    // 0xbb7cc4: r2 = Instance_MainAxisAlignment
    //     0xbb7cc4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb7cc8: ldr             x2, [x2, #0xa08]
    // 0xbb7ccc: StoreField: r1->field_13 = r2
    //     0xbb7ccc: stur            w2, [x1, #0x13]
    // 0xbb7cd0: r3 = Instance_MainAxisSize
    //     0xbb7cd0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb7cd4: ldr             x3, [x3, #0xa10]
    // 0xbb7cd8: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb7cd8: stur            w3, [x1, #0x17]
    // 0xbb7cdc: r4 = Instance_CrossAxisAlignment
    //     0xbb7cdc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb7ce0: ldr             x4, [x4, #0x890]
    // 0xbb7ce4: StoreField: r1->field_1b = r4
    //     0xbb7ce4: stur            w4, [x1, #0x1b]
    // 0xbb7ce8: r5 = Instance_VerticalDirection
    //     0xbb7ce8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb7cec: ldr             x5, [x5, #0xa20]
    // 0xbb7cf0: StoreField: r1->field_23 = r5
    //     0xbb7cf0: stur            w5, [x1, #0x23]
    // 0xbb7cf4: r6 = Instance_Clip
    //     0xbb7cf4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb7cf8: ldr             x6, [x6, #0x38]
    // 0xbb7cfc: StoreField: r1->field_2b = r6
    //     0xbb7cfc: stur            w6, [x1, #0x2b]
    // 0xbb7d00: StoreField: r1->field_2f = rZR
    //     0xbb7d00: stur            xzr, [x1, #0x2f]
    // 0xbb7d04: ldur            x7, [fp, #-8]
    // 0xbb7d08: StoreField: r1->field_b = r7
    //     0xbb7d08: stur            w7, [x1, #0xb]
    // 0xbb7d0c: r0 = Container()
    //     0xbb7d0c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb7d10: stur            x0, [fp, #-8]
    // 0xbb7d14: ldur            x16, [fp, #-0x30]
    // 0xbb7d18: ldur            lr, [fp, #-0x18]
    // 0xbb7d1c: stp             lr, x16, [SP]
    // 0xbb7d20: mov             x1, x0
    // 0xbb7d24: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbb7d24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbb7d28: ldr             x4, [x4, #0x88]
    // 0xbb7d2c: r0 = Container()
    //     0xbb7d2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb7d30: r0 = Padding()
    //     0xbb7d30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb7d34: mov             x3, x0
    // 0xbb7d38: r0 = Instance_EdgeInsets
    //     0xbb7d38: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xbb7d3c: ldr             x0, [x0, #0x868]
    // 0xbb7d40: stur            x3, [fp, #-0x18]
    // 0xbb7d44: StoreField: r3->field_f = r0
    //     0xbb7d44: stur            w0, [x3, #0xf]
    // 0xbb7d48: ldur            x0, [fp, #-8]
    // 0xbb7d4c: StoreField: r3->field_b = r0
    //     0xbb7d4c: stur            w0, [x3, #0xb]
    // 0xbb7d50: r1 = Null
    //     0xbb7d50: mov             x1, NULL
    // 0xbb7d54: r2 = 4
    //     0xbb7d54: movz            x2, #0x4
    // 0xbb7d58: r0 = AllocateArray()
    //     0xbb7d58: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb7d5c: mov             x2, x0
    // 0xbb7d60: ldur            x0, [fp, #-0x10]
    // 0xbb7d64: stur            x2, [fp, #-8]
    // 0xbb7d68: StoreField: r2->field_f = r0
    //     0xbb7d68: stur            w0, [x2, #0xf]
    // 0xbb7d6c: ldur            x0, [fp, #-0x18]
    // 0xbb7d70: StoreField: r2->field_13 = r0
    //     0xbb7d70: stur            w0, [x2, #0x13]
    // 0xbb7d74: r1 = <Widget>
    //     0xbb7d74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb7d78: r0 = AllocateGrowableArray()
    //     0xbb7d78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb7d7c: mov             x1, x0
    // 0xbb7d80: ldur            x0, [fp, #-8]
    // 0xbb7d84: stur            x1, [fp, #-0x10]
    // 0xbb7d88: StoreField: r1->field_f = r0
    //     0xbb7d88: stur            w0, [x1, #0xf]
    // 0xbb7d8c: r0 = 4
    //     0xbb7d8c: movz            x0, #0x4
    // 0xbb7d90: StoreField: r1->field_b = r0
    //     0xbb7d90: stur            w0, [x1, #0xb]
    // 0xbb7d94: r0 = Column()
    //     0xbb7d94: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb7d98: mov             x1, x0
    // 0xbb7d9c: r0 = Instance_Axis
    //     0xbb7d9c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb7da0: stur            x1, [fp, #-8]
    // 0xbb7da4: StoreField: r1->field_f = r0
    //     0xbb7da4: stur            w0, [x1, #0xf]
    // 0xbb7da8: r0 = Instance_MainAxisAlignment
    //     0xbb7da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb7dac: ldr             x0, [x0, #0xa08]
    // 0xbb7db0: StoreField: r1->field_13 = r0
    //     0xbb7db0: stur            w0, [x1, #0x13]
    // 0xbb7db4: r0 = Instance_MainAxisSize
    //     0xbb7db4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb7db8: ldr             x0, [x0, #0xa10]
    // 0xbb7dbc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb7dbc: stur            w0, [x1, #0x17]
    // 0xbb7dc0: r0 = Instance_CrossAxisAlignment
    //     0xbb7dc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb7dc4: ldr             x0, [x0, #0x890]
    // 0xbb7dc8: StoreField: r1->field_1b = r0
    //     0xbb7dc8: stur            w0, [x1, #0x1b]
    // 0xbb7dcc: r0 = Instance_VerticalDirection
    //     0xbb7dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb7dd0: ldr             x0, [x0, #0xa20]
    // 0xbb7dd4: StoreField: r1->field_23 = r0
    //     0xbb7dd4: stur            w0, [x1, #0x23]
    // 0xbb7dd8: r0 = Instance_Clip
    //     0xbb7dd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb7ddc: ldr             x0, [x0, #0x38]
    // 0xbb7de0: StoreField: r1->field_2b = r0
    //     0xbb7de0: stur            w0, [x1, #0x2b]
    // 0xbb7de4: StoreField: r1->field_2f = rZR
    //     0xbb7de4: stur            xzr, [x1, #0x2f]
    // 0xbb7de8: ldur            x0, [fp, #-0x10]
    // 0xbb7dec: StoreField: r1->field_b = r0
    //     0xbb7dec: stur            w0, [x1, #0xb]
    // 0xbb7df0: r0 = Padding()
    //     0xbb7df0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb7df4: r1 = Instance_EdgeInsets
    //     0xbb7df4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbb7df8: ldr             x1, [x1, #0x1f0]
    // 0xbb7dfc: StoreField: r0->field_f = r1
    //     0xbb7dfc: stur            w1, [x0, #0xf]
    // 0xbb7e00: ldur            x1, [fp, #-8]
    // 0xbb7e04: StoreField: r0->field_b = r1
    //     0xbb7e04: stur            w1, [x0, #0xb]
    // 0xbb7e08: LeaveFrame
    //     0xbb7e08: mov             SP, fp
    //     0xbb7e0c: ldp             fp, lr, [SP], #0x10
    // 0xbb7e10: ret
    //     0xbb7e10: ret             
    // 0xbb7e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7e14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7e18: b               #0xbb7044
    // 0xbb7e1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7e24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7e3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e3c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb7e4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbb7e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb7e58, size: 0xe0
    // 0xbb7e58: EnterFrame
    //     0xbb7e58: stp             fp, lr, [SP, #-0x10]!
    //     0xbb7e5c: mov             fp, SP
    // 0xbb7e60: AllocStack(0x20)
    //     0xbb7e60: sub             SP, SP, #0x20
    // 0xbb7e64: SetupParameters()
    //     0xbb7e64: ldr             x0, [fp, #0x10]
    //     0xbb7e68: ldur            w1, [x0, #0x17]
    //     0xbb7e6c: add             x1, x1, HEAP, lsl #32
    //     0xbb7e70: stur            x1, [fp, #-8]
    // 0xbb7e74: CheckStackOverflow
    //     0xbb7e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb7e78: cmp             SP, x16
    //     0xbb7e7c: b.ls            #0xbb7f28
    // 0xbb7e80: LoadField: r0 = r1->field_f
    //     0xbb7e80: ldur            w0, [x1, #0xf]
    // 0xbb7e84: DecompressPointer r0
    //     0xbb7e84: add             x0, x0, HEAP, lsl #32
    // 0xbb7e88: LoadField: r2 = r0->field_b
    //     0xbb7e88: ldur            w2, [x0, #0xb]
    // 0xbb7e8c: DecompressPointer r2
    //     0xbb7e8c: add             x2, x2, HEAP, lsl #32
    // 0xbb7e90: cmp             w2, NULL
    // 0xbb7e94: b.eq            #0xbb7f30
    // 0xbb7e98: LoadField: r0 = r1->field_13
    //     0xbb7e98: ldur            w0, [x1, #0x13]
    // 0xbb7e9c: DecompressPointer r0
    //     0xbb7e9c: add             x0, x0, HEAP, lsl #32
    // 0xbb7ea0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbb7ea0: ldur            w3, [x2, #0x17]
    // 0xbb7ea4: DecompressPointer r3
    //     0xbb7ea4: add             x3, x3, HEAP, lsl #32
    // 0xbb7ea8: stp             x0, x3, [SP]
    // 0xbb7eac: r4 = 0
    //     0xbb7eac: movz            x4, #0
    // 0xbb7eb0: ldr             x0, [SP, #8]
    // 0xbb7eb4: r16 = UnlinkedCall_0x613b5c
    //     0xbb7eb4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54798] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb7eb8: add             x16, x16, #0x798
    // 0xbb7ebc: ldp             x5, lr, [x16]
    // 0xbb7ec0: blr             lr
    // 0xbb7ec4: ldur            x0, [fp, #-8]
    // 0xbb7ec8: LoadField: r1 = r0->field_f
    //     0xbb7ec8: ldur            w1, [x0, #0xf]
    // 0xbb7ecc: DecompressPointer r1
    //     0xbb7ecc: add             x1, x1, HEAP, lsl #32
    // 0xbb7ed0: LoadField: r0 = r1->field_b
    //     0xbb7ed0: ldur            w0, [x1, #0xb]
    // 0xbb7ed4: DecompressPointer r0
    //     0xbb7ed4: add             x0, x0, HEAP, lsl #32
    // 0xbb7ed8: cmp             w0, NULL
    // 0xbb7edc: b.eq            #0xbb7f34
    // 0xbb7ee0: LoadField: r1 = r0->field_1f
    //     0xbb7ee0: ldur            w1, [x0, #0x1f]
    // 0xbb7ee4: DecompressPointer r1
    //     0xbb7ee4: add             x1, x1, HEAP, lsl #32
    // 0xbb7ee8: r16 = "change"
    //     0xbb7ee8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d3c8] "change"
    //     0xbb7eec: ldr             x16, [x16, #0x3c8]
    // 0xbb7ef0: stp             x16, x1, [SP, #8]
    // 0xbb7ef4: r16 = "Change"
    //     0xbb7ef4: add             x16, PP, #0x54, lsl #12  ; [pp+0x547a8] "Change"
    //     0xbb7ef8: ldr             x16, [x16, #0x7a8]
    // 0xbb7efc: str             x16, [SP]
    // 0xbb7f00: r4 = 0
    //     0xbb7f00: movz            x4, #0
    // 0xbb7f04: ldr             x0, [SP, #0x10]
    // 0xbb7f08: r16 = UnlinkedCall_0x613b5c
    //     0xbb7f08: add             x16, PP, #0x54, lsl #12  ; [pp+0x547b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb7f0c: add             x16, x16, #0x7b0
    // 0xbb7f10: ldp             x5, lr, [x16]
    // 0xbb7f14: blr             lr
    // 0xbb7f18: r0 = Null
    //     0xbb7f18: mov             x0, NULL
    // 0xbb7f1c: LeaveFrame
    //     0xbb7f1c: mov             SP, fp
    //     0xbb7f20: ldp             fp, lr, [SP], #0x10
    // 0xbb7f24: ret
    //     0xbb7f24: ret             
    // 0xbb7f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb7f28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb7f2c: b               #0xbb7e80
    // 0xbb7f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb7f34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb7f34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4018, size: 0x28, field offset: 0xc
//   const constructor, 
class DeliveryAddressWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8032c, size: 0x24
    // 0xc8032c: EnterFrame
    //     0xc8032c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80330: mov             fp, SP
    // 0xc80334: mov             x0, x1
    // 0xc80338: r1 = <DeliveryAddressWidget>
    //     0xc80338: add             x1, PP, #0x48, lsl #12  ; [pp+0x486a0] TypeArguments: <DeliveryAddressWidget>
    //     0xc8033c: ldr             x1, [x1, #0x6a0]
    // 0xc80340: r0 = _DeliveryAddressWidgetState()
    //     0xc80340: bl              #0xc80350  ; Allocate_DeliveryAddressWidgetStateStub -> _DeliveryAddressWidgetState (size=0x14)
    // 0xc80344: LeaveFrame
    //     0xc80344: mov             SP, fp
    //     0xc80348: ldp             fp, lr, [SP], #0x10
    // 0xc8034c: ret
    //     0xc8034c: ret             
  }
}
