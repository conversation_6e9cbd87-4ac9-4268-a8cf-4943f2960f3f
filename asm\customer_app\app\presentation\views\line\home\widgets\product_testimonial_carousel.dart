// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart

// class id: 1049527, size: 0x8
class :: {
}

// class id: 3244, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xbec24c, size: 0x92c
    // 0xbec24c: EnterFrame
    //     0xbec24c: stp             fp, lr, [SP, #-0x10]!
    //     0xbec250: mov             fp, SP
    // 0xbec254: AllocStack(0x78)
    //     0xbec254: sub             SP, SP, #0x78
    // 0xbec258: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbec258: mov             x0, x1
    //     0xbec25c: stur            x1, [fp, #-8]
    //     0xbec260: mov             x1, x2
    //     0xbec264: stur            x2, [fp, #-0x10]
    // 0xbec268: CheckStackOverflow
    //     0xbec268: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbec26c: cmp             SP, x16
    //     0xbec270: b.ls            #0xbecb38
    // 0xbec274: r1 = 1
    //     0xbec274: movz            x1, #0x1
    // 0xbec278: r0 = AllocateContext()
    //     0xbec278: bl              #0x16f6108  ; AllocateContextStub
    // 0xbec27c: mov             x3, x0
    // 0xbec280: ldur            x0, [fp, #-8]
    // 0xbec284: stur            x3, [fp, #-0x20]
    // 0xbec288: StoreField: r3->field_f = r0
    //     0xbec288: stur            w0, [x3, #0xf]
    // 0xbec28c: LoadField: r1 = r0->field_b
    //     0xbec28c: ldur            w1, [x0, #0xb]
    // 0xbec290: DecompressPointer r1
    //     0xbec290: add             x1, x1, HEAP, lsl #32
    // 0xbec294: cmp             w1, NULL
    // 0xbec298: b.eq            #0xbecb40
    // 0xbec29c: LoadField: r2 = r1->field_13
    //     0xbec29c: ldur            w2, [x1, #0x13]
    // 0xbec2a0: DecompressPointer r2
    //     0xbec2a0: add             x2, x2, HEAP, lsl #32
    // 0xbec2a4: LoadField: r1 = r2->field_7
    //     0xbec2a4: ldur            w1, [x2, #7]
    // 0xbec2a8: DecompressPointer r1
    //     0xbec2a8: add             x1, x1, HEAP, lsl #32
    // 0xbec2ac: cmp             w1, NULL
    // 0xbec2b0: b.ne            #0xbec2bc
    // 0xbec2b4: r1 = Instance_TitleAlignment
    //     0xbec2b4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbec2b8: ldr             x1, [x1, #0x518]
    // 0xbec2bc: r16 = Instance_TitleAlignment
    //     0xbec2bc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbec2c0: ldr             x16, [x16, #0x520]
    // 0xbec2c4: cmp             w1, w16
    // 0xbec2c8: b.ne            #0xbec2d8
    // 0xbec2cc: r4 = Instance_CrossAxisAlignment
    //     0xbec2cc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbec2d0: ldr             x4, [x4, #0xc68]
    // 0xbec2d4: b               #0xbec2fc
    // 0xbec2d8: r16 = Instance_TitleAlignment
    //     0xbec2d8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbec2dc: ldr             x16, [x16, #0x518]
    // 0xbec2e0: cmp             w1, w16
    // 0xbec2e4: b.ne            #0xbec2f4
    // 0xbec2e8: r4 = Instance_CrossAxisAlignment
    //     0xbec2e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbec2ec: ldr             x4, [x4, #0x890]
    // 0xbec2f0: b               #0xbec2fc
    // 0xbec2f4: r4 = Instance_CrossAxisAlignment
    //     0xbec2f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbec2f8: ldr             x4, [x4, #0xa18]
    // 0xbec2fc: stur            x4, [fp, #-0x18]
    // 0xbec300: r1 = <Widget>
    //     0xbec300: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbec304: r2 = 0
    //     0xbec304: movz            x2, #0
    // 0xbec308: r0 = _GrowableList()
    //     0xbec308: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbec30c: mov             x2, x0
    // 0xbec310: ldur            x1, [fp, #-8]
    // 0xbec314: stur            x2, [fp, #-0x28]
    // 0xbec318: LoadField: r0 = r1->field_b
    //     0xbec318: ldur            w0, [x1, #0xb]
    // 0xbec31c: DecompressPointer r0
    //     0xbec31c: add             x0, x0, HEAP, lsl #32
    // 0xbec320: cmp             w0, NULL
    // 0xbec324: b.eq            #0xbecb44
    // 0xbec328: LoadField: r3 = r0->field_f
    //     0xbec328: ldur            w3, [x0, #0xf]
    // 0xbec32c: DecompressPointer r3
    //     0xbec32c: add             x3, x3, HEAP, lsl #32
    // 0xbec330: LoadField: r0 = r3->field_7
    //     0xbec330: ldur            w0, [x3, #7]
    // 0xbec334: cbz             w0, #0xbec544
    // 0xbec338: r0 = LoadClassIdInstr(r3)
    //     0xbec338: ldur            x0, [x3, #-1]
    //     0xbec33c: ubfx            x0, x0, #0xc, #0x14
    // 0xbec340: str             x3, [SP]
    // 0xbec344: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbec344: sub             lr, x0, #1, lsl #12
    //     0xbec348: ldr             lr, [x21, lr, lsl #3]
    //     0xbec34c: blr             lr
    // 0xbec350: mov             x2, x0
    // 0xbec354: ldur            x0, [fp, #-8]
    // 0xbec358: stur            x2, [fp, #-0x38]
    // 0xbec35c: LoadField: r1 = r0->field_b
    //     0xbec35c: ldur            w1, [x0, #0xb]
    // 0xbec360: DecompressPointer r1
    //     0xbec360: add             x1, x1, HEAP, lsl #32
    // 0xbec364: cmp             w1, NULL
    // 0xbec368: b.eq            #0xbecb48
    // 0xbec36c: LoadField: r3 = r1->field_13
    //     0xbec36c: ldur            w3, [x1, #0x13]
    // 0xbec370: DecompressPointer r3
    //     0xbec370: add             x3, x3, HEAP, lsl #32
    // 0xbec374: LoadField: r1 = r3->field_7
    //     0xbec374: ldur            w1, [x3, #7]
    // 0xbec378: DecompressPointer r1
    //     0xbec378: add             x1, x1, HEAP, lsl #32
    // 0xbec37c: cmp             w1, NULL
    // 0xbec380: b.ne            #0xbec38c
    // 0xbec384: r1 = Instance_TitleAlignment
    //     0xbec384: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbec388: ldr             x1, [x1, #0x518]
    // 0xbec38c: r16 = Instance_TitleAlignment
    //     0xbec38c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbec390: ldr             x16, [x16, #0x520]
    // 0xbec394: cmp             w1, w16
    // 0xbec398: b.ne            #0xbec3a4
    // 0xbec39c: r4 = Instance_TextAlign
    //     0xbec39c: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xbec3a0: b               #0xbec3c0
    // 0xbec3a4: r16 = Instance_TitleAlignment
    //     0xbec3a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbec3a8: ldr             x16, [x16, #0x518]
    // 0xbec3ac: cmp             w1, w16
    // 0xbec3b0: b.ne            #0xbec3bc
    // 0xbec3b4: r4 = Instance_TextAlign
    //     0xbec3b4: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbec3b8: b               #0xbec3c0
    // 0xbec3bc: r4 = Instance_TextAlign
    //     0xbec3bc: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbec3c0: ldur            x3, [fp, #-0x28]
    // 0xbec3c4: ldur            x1, [fp, #-0x10]
    // 0xbec3c8: stur            x4, [fp, #-0x30]
    // 0xbec3cc: r0 = of()
    //     0xbec3cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbec3d0: LoadField: r1 = r0->field_87
    //     0xbec3d0: ldur            w1, [x0, #0x87]
    // 0xbec3d4: DecompressPointer r1
    //     0xbec3d4: add             x1, x1, HEAP, lsl #32
    // 0xbec3d8: LoadField: r0 = r1->field_27
    //     0xbec3d8: ldur            w0, [x1, #0x27]
    // 0xbec3dc: DecompressPointer r0
    //     0xbec3dc: add             x0, x0, HEAP, lsl #32
    // 0xbec3e0: r16 = 21.000000
    //     0xbec3e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbec3e4: ldr             x16, [x16, #0x9b0]
    // 0xbec3e8: r30 = Instance_Color
    //     0xbec3e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbec3ec: stp             lr, x16, [SP]
    // 0xbec3f0: mov             x1, x0
    // 0xbec3f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbec3f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbec3f8: ldr             x4, [x4, #0xaa0]
    // 0xbec3fc: r0 = copyWith()
    //     0xbec3fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbec400: stur            x0, [fp, #-0x40]
    // 0xbec404: r0 = Text()
    //     0xbec404: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbec408: mov             x3, x0
    // 0xbec40c: ldur            x0, [fp, #-0x38]
    // 0xbec410: stur            x3, [fp, #-0x48]
    // 0xbec414: StoreField: r3->field_b = r0
    //     0xbec414: stur            w0, [x3, #0xb]
    // 0xbec418: ldur            x0, [fp, #-0x40]
    // 0xbec41c: StoreField: r3->field_13 = r0
    //     0xbec41c: stur            w0, [x3, #0x13]
    // 0xbec420: ldur            x0, [fp, #-0x30]
    // 0xbec424: StoreField: r3->field_1b = r0
    //     0xbec424: stur            w0, [x3, #0x1b]
    // 0xbec428: r1 = Null
    //     0xbec428: mov             x1, NULL
    // 0xbec42c: r2 = 4
    //     0xbec42c: movz            x2, #0x4
    // 0xbec430: r0 = AllocateArray()
    //     0xbec430: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbec434: mov             x2, x0
    // 0xbec438: ldur            x0, [fp, #-0x48]
    // 0xbec43c: stur            x2, [fp, #-0x30]
    // 0xbec440: StoreField: r2->field_f = r0
    //     0xbec440: stur            w0, [x2, #0xf]
    // 0xbec444: r16 = Instance_SizedBox
    //     0xbec444: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbec448: ldr             x16, [x16, #0xc70]
    // 0xbec44c: StoreField: r2->field_13 = r16
    //     0xbec44c: stur            w16, [x2, #0x13]
    // 0xbec450: r1 = <Widget>
    //     0xbec450: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbec454: r0 = AllocateGrowableArray()
    //     0xbec454: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbec458: mov             x1, x0
    // 0xbec45c: ldur            x0, [fp, #-0x30]
    // 0xbec460: stur            x1, [fp, #-0x38]
    // 0xbec464: StoreField: r1->field_f = r0
    //     0xbec464: stur            w0, [x1, #0xf]
    // 0xbec468: r0 = 4
    //     0xbec468: movz            x0, #0x4
    // 0xbec46c: StoreField: r1->field_b = r0
    //     0xbec46c: stur            w0, [x1, #0xb]
    // 0xbec470: r0 = Column()
    //     0xbec470: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbec474: mov             x2, x0
    // 0xbec478: r0 = Instance_Axis
    //     0xbec478: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbec47c: stur            x2, [fp, #-0x30]
    // 0xbec480: StoreField: r2->field_f = r0
    //     0xbec480: stur            w0, [x2, #0xf]
    // 0xbec484: r3 = Instance_MainAxisAlignment
    //     0xbec484: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbec488: ldr             x3, [x3, #0xa08]
    // 0xbec48c: StoreField: r2->field_13 = r3
    //     0xbec48c: stur            w3, [x2, #0x13]
    // 0xbec490: r4 = Instance_MainAxisSize
    //     0xbec490: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbec494: ldr             x4, [x4, #0xa10]
    // 0xbec498: ArrayStore: r2[0] = r4  ; List_4
    //     0xbec498: stur            w4, [x2, #0x17]
    // 0xbec49c: r1 = Instance_CrossAxisAlignment
    //     0xbec49c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbec4a0: ldr             x1, [x1, #0x890]
    // 0xbec4a4: StoreField: r2->field_1b = r1
    //     0xbec4a4: stur            w1, [x2, #0x1b]
    // 0xbec4a8: r5 = Instance_VerticalDirection
    //     0xbec4a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbec4ac: ldr             x5, [x5, #0xa20]
    // 0xbec4b0: StoreField: r2->field_23 = r5
    //     0xbec4b0: stur            w5, [x2, #0x23]
    // 0xbec4b4: r6 = Instance_Clip
    //     0xbec4b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbec4b8: ldr             x6, [x6, #0x38]
    // 0xbec4bc: StoreField: r2->field_2b = r6
    //     0xbec4bc: stur            w6, [x2, #0x2b]
    // 0xbec4c0: StoreField: r2->field_2f = rZR
    //     0xbec4c0: stur            xzr, [x2, #0x2f]
    // 0xbec4c4: ldur            x1, [fp, #-0x38]
    // 0xbec4c8: StoreField: r2->field_b = r1
    //     0xbec4c8: stur            w1, [x2, #0xb]
    // 0xbec4cc: ldur            x7, [fp, #-0x28]
    // 0xbec4d0: LoadField: r1 = r7->field_b
    //     0xbec4d0: ldur            w1, [x7, #0xb]
    // 0xbec4d4: LoadField: r8 = r7->field_f
    //     0xbec4d4: ldur            w8, [x7, #0xf]
    // 0xbec4d8: DecompressPointer r8
    //     0xbec4d8: add             x8, x8, HEAP, lsl #32
    // 0xbec4dc: LoadField: r9 = r8->field_b
    //     0xbec4dc: ldur            w9, [x8, #0xb]
    // 0xbec4e0: r8 = LoadInt32Instr(r1)
    //     0xbec4e0: sbfx            x8, x1, #1, #0x1f
    // 0xbec4e4: stur            x8, [fp, #-0x50]
    // 0xbec4e8: r1 = LoadInt32Instr(r9)
    //     0xbec4e8: sbfx            x1, x9, #1, #0x1f
    // 0xbec4ec: cmp             x8, x1
    // 0xbec4f0: b.ne            #0xbec4fc
    // 0xbec4f4: mov             x1, x7
    // 0xbec4f8: r0 = _growToNextCapacity()
    //     0xbec4f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbec4fc: ldur            x2, [fp, #-0x28]
    // 0xbec500: ldur            x3, [fp, #-0x50]
    // 0xbec504: add             x0, x3, #1
    // 0xbec508: lsl             x1, x0, #1
    // 0xbec50c: StoreField: r2->field_b = r1
    //     0xbec50c: stur            w1, [x2, #0xb]
    // 0xbec510: LoadField: r1 = r2->field_f
    //     0xbec510: ldur            w1, [x2, #0xf]
    // 0xbec514: DecompressPointer r1
    //     0xbec514: add             x1, x1, HEAP, lsl #32
    // 0xbec518: ldur            x0, [fp, #-0x30]
    // 0xbec51c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbec51c: add             x25, x1, x3, lsl #2
    //     0xbec520: add             x25, x25, #0xf
    //     0xbec524: str             w0, [x25]
    //     0xbec528: tbz             w0, #0, #0xbec544
    //     0xbec52c: ldurb           w16, [x1, #-1]
    //     0xbec530: ldurb           w17, [x0, #-1]
    //     0xbec534: and             x16, x17, x16, lsr #2
    //     0xbec538: tst             x16, HEAP, lsr #32
    //     0xbec53c: b.eq            #0xbec544
    //     0xbec540: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbec544: ldur            x1, [fp, #-8]
    // 0xbec548: LoadField: r0 = r1->field_b
    //     0xbec548: ldur            w0, [x1, #0xb]
    // 0xbec54c: DecompressPointer r0
    //     0xbec54c: add             x0, x0, HEAP, lsl #32
    // 0xbec550: cmp             w0, NULL
    // 0xbec554: b.eq            #0xbecb4c
    // 0xbec558: LoadField: r3 = r0->field_2b
    //     0xbec558: ldur            w3, [x0, #0x2b]
    // 0xbec55c: DecompressPointer r3
    //     0xbec55c: add             x3, x3, HEAP, lsl #32
    // 0xbec560: cmp             w3, NULL
    // 0xbec564: b.ne            #0xbec570
    // 0xbec568: r0 = Null
    //     0xbec568: mov             x0, NULL
    // 0xbec56c: b               #0xbec59c
    // 0xbec570: LoadField: r0 = r3->field_7
    //     0xbec570: ldur            w0, [x3, #7]
    // 0xbec574: DecompressPointer r0
    //     0xbec574: add             x0, x0, HEAP, lsl #32
    // 0xbec578: cmp             w0, NULL
    // 0xbec57c: b.ne            #0xbec588
    // 0xbec580: r0 = Null
    //     0xbec580: mov             x0, NULL
    // 0xbec584: b               #0xbec59c
    // 0xbec588: LoadField: r4 = r0->field_7
    //     0xbec588: ldur            w4, [x0, #7]
    // 0xbec58c: cbnz            w4, #0xbec598
    // 0xbec590: r0 = false
    //     0xbec590: add             x0, NULL, #0x30  ; false
    // 0xbec594: b               #0xbec59c
    // 0xbec598: r0 = true
    //     0xbec598: add             x0, NULL, #0x20  ; true
    // 0xbec59c: cmp             w0, NULL
    // 0xbec5a0: b.ne            #0xbec5a8
    // 0xbec5a4: r0 = false
    //     0xbec5a4: add             x0, NULL, #0x30  ; false
    // 0xbec5a8: stur            x0, [fp, #-0x30]
    // 0xbec5ac: cmp             w3, NULL
    // 0xbec5b0: b.ne            #0xbec5bc
    // 0xbec5b4: r3 = Null
    //     0xbec5b4: mov             x3, NULL
    // 0xbec5b8: b               #0xbec5c8
    // 0xbec5bc: LoadField: r4 = r3->field_7
    //     0xbec5bc: ldur            w4, [x3, #7]
    // 0xbec5c0: DecompressPointer r4
    //     0xbec5c0: add             x4, x4, HEAP, lsl #32
    // 0xbec5c4: mov             x3, x4
    // 0xbec5c8: str             x3, [SP]
    // 0xbec5cc: r0 = _interpolateSingle()
    //     0xbec5cc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbec5d0: ldur            x1, [fp, #-0x10]
    // 0xbec5d4: stur            x0, [fp, #-0x38]
    // 0xbec5d8: r0 = of()
    //     0xbec5d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbec5dc: LoadField: r1 = r0->field_87
    //     0xbec5dc: ldur            w1, [x0, #0x87]
    // 0xbec5e0: DecompressPointer r1
    //     0xbec5e0: add             x1, x1, HEAP, lsl #32
    // 0xbec5e4: LoadField: r0 = r1->field_2b
    //     0xbec5e4: ldur            w0, [x1, #0x2b]
    // 0xbec5e8: DecompressPointer r0
    //     0xbec5e8: add             x0, x0, HEAP, lsl #32
    // 0xbec5ec: stur            x0, [fp, #-0x40]
    // 0xbec5f0: r1 = Instance_Color
    //     0xbec5f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbec5f4: d0 = 0.700000
    //     0xbec5f4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbec5f8: ldr             d0, [x17, #0xf48]
    // 0xbec5fc: r0 = withOpacity()
    //     0xbec5fc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbec600: r16 = 12.000000
    //     0xbec600: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbec604: ldr             x16, [x16, #0x9e8]
    // 0xbec608: stp             x0, x16, [SP, #8]
    // 0xbec60c: r16 = Instance_TextDecoration
    //     0xbec60c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbec610: ldr             x16, [x16, #0x10]
    // 0xbec614: str             x16, [SP]
    // 0xbec618: ldur            x1, [fp, #-0x40]
    // 0xbec61c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbec61c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbec620: ldr             x4, [x4, #0xe38]
    // 0xbec624: r0 = copyWith()
    //     0xbec624: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbec628: stur            x0, [fp, #-0x40]
    // 0xbec62c: r0 = Text()
    //     0xbec62c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbec630: mov             x1, x0
    // 0xbec634: ldur            x0, [fp, #-0x38]
    // 0xbec638: stur            x1, [fp, #-0x48]
    // 0xbec63c: StoreField: r1->field_b = r0
    //     0xbec63c: stur            w0, [x1, #0xb]
    // 0xbec640: ldur            x0, [fp, #-0x40]
    // 0xbec644: StoreField: r1->field_13 = r0
    //     0xbec644: stur            w0, [x1, #0x13]
    // 0xbec648: r0 = Visibility()
    //     0xbec648: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbec64c: mov             x1, x0
    // 0xbec650: ldur            x0, [fp, #-0x48]
    // 0xbec654: stur            x1, [fp, #-0x38]
    // 0xbec658: StoreField: r1->field_b = r0
    //     0xbec658: stur            w0, [x1, #0xb]
    // 0xbec65c: r0 = Instance_SizedBox
    //     0xbec65c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbec660: StoreField: r1->field_f = r0
    //     0xbec660: stur            w0, [x1, #0xf]
    // 0xbec664: ldur            x0, [fp, #-0x30]
    // 0xbec668: StoreField: r1->field_13 = r0
    //     0xbec668: stur            w0, [x1, #0x13]
    // 0xbec66c: r0 = false
    //     0xbec66c: add             x0, NULL, #0x30  ; false
    // 0xbec670: ArrayStore: r1[0] = r0  ; List_4
    //     0xbec670: stur            w0, [x1, #0x17]
    // 0xbec674: StoreField: r1->field_1b = r0
    //     0xbec674: stur            w0, [x1, #0x1b]
    // 0xbec678: StoreField: r1->field_1f = r0
    //     0xbec678: stur            w0, [x1, #0x1f]
    // 0xbec67c: StoreField: r1->field_23 = r0
    //     0xbec67c: stur            w0, [x1, #0x23]
    // 0xbec680: StoreField: r1->field_27 = r0
    //     0xbec680: stur            w0, [x1, #0x27]
    // 0xbec684: StoreField: r1->field_2b = r0
    //     0xbec684: stur            w0, [x1, #0x2b]
    // 0xbec688: r0 = InkWell()
    //     0xbec688: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbec68c: mov             x3, x0
    // 0xbec690: ldur            x0, [fp, #-0x38]
    // 0xbec694: stur            x3, [fp, #-0x30]
    // 0xbec698: StoreField: r3->field_b = r0
    //     0xbec698: stur            w0, [x3, #0xb]
    // 0xbec69c: ldur            x2, [fp, #-0x20]
    // 0xbec6a0: r1 = Function '<anonymous closure>':.
    //     0xbec6a0: add             x1, PP, #0x53, lsl #12  ; [pp+0x535d8] AnonymousClosure: (0xbedb0c), in [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xbec24c)
    //     0xbec6a4: ldr             x1, [x1, #0x5d8]
    // 0xbec6a8: r0 = AllocateClosure()
    //     0xbec6a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbec6ac: mov             x1, x0
    // 0xbec6b0: ldur            x0, [fp, #-0x30]
    // 0xbec6b4: StoreField: r0->field_f = r1
    //     0xbec6b4: stur            w1, [x0, #0xf]
    // 0xbec6b8: r1 = true
    //     0xbec6b8: add             x1, NULL, #0x20  ; true
    // 0xbec6bc: StoreField: r0->field_43 = r1
    //     0xbec6bc: stur            w1, [x0, #0x43]
    // 0xbec6c0: r2 = Instance_BoxShape
    //     0xbec6c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbec6c4: ldr             x2, [x2, #0x80]
    // 0xbec6c8: StoreField: r0->field_47 = r2
    //     0xbec6c8: stur            w2, [x0, #0x47]
    // 0xbec6cc: StoreField: r0->field_6f = r1
    //     0xbec6cc: stur            w1, [x0, #0x6f]
    // 0xbec6d0: r2 = false
    //     0xbec6d0: add             x2, NULL, #0x30  ; false
    // 0xbec6d4: StoreField: r0->field_73 = r2
    //     0xbec6d4: stur            w2, [x0, #0x73]
    // 0xbec6d8: StoreField: r0->field_83 = r1
    //     0xbec6d8: stur            w1, [x0, #0x83]
    // 0xbec6dc: StoreField: r0->field_7b = r2
    //     0xbec6dc: stur            w2, [x0, #0x7b]
    // 0xbec6e0: ldur            x2, [fp, #-0x28]
    // 0xbec6e4: LoadField: r1 = r2->field_b
    //     0xbec6e4: ldur            w1, [x2, #0xb]
    // 0xbec6e8: LoadField: r3 = r2->field_f
    //     0xbec6e8: ldur            w3, [x2, #0xf]
    // 0xbec6ec: DecompressPointer r3
    //     0xbec6ec: add             x3, x3, HEAP, lsl #32
    // 0xbec6f0: LoadField: r4 = r3->field_b
    //     0xbec6f0: ldur            w4, [x3, #0xb]
    // 0xbec6f4: r3 = LoadInt32Instr(r1)
    //     0xbec6f4: sbfx            x3, x1, #1, #0x1f
    // 0xbec6f8: stur            x3, [fp, #-0x50]
    // 0xbec6fc: r1 = LoadInt32Instr(r4)
    //     0xbec6fc: sbfx            x1, x4, #1, #0x1f
    // 0xbec700: cmp             x3, x1
    // 0xbec704: b.ne            #0xbec710
    // 0xbec708: mov             x1, x2
    // 0xbec70c: r0 = _growToNextCapacity()
    //     0xbec70c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbec710: ldur            x4, [fp, #-8]
    // 0xbec714: ldur            x3, [fp, #-0x28]
    // 0xbec718: ldur            x2, [fp, #-0x50]
    // 0xbec71c: add             x0, x2, #1
    // 0xbec720: lsl             x1, x0, #1
    // 0xbec724: StoreField: r3->field_b = r1
    //     0xbec724: stur            w1, [x3, #0xb]
    // 0xbec728: LoadField: r1 = r3->field_f
    //     0xbec728: ldur            w1, [x3, #0xf]
    // 0xbec72c: DecompressPointer r1
    //     0xbec72c: add             x1, x1, HEAP, lsl #32
    // 0xbec730: ldur            x0, [fp, #-0x30]
    // 0xbec734: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbec734: add             x25, x1, x2, lsl #2
    //     0xbec738: add             x25, x25, #0xf
    //     0xbec73c: str             w0, [x25]
    //     0xbec740: tbz             w0, #0, #0xbec75c
    //     0xbec744: ldurb           w16, [x1, #-1]
    //     0xbec748: ldurb           w17, [x0, #-1]
    //     0xbec74c: and             x16, x17, x16, lsr #2
    //     0xbec750: tst             x16, HEAP, lsr #32
    //     0xbec754: b.eq            #0xbec75c
    //     0xbec758: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbec75c: ArrayLoad: r2 = r4[0]  ; List_8
    //     0xbec75c: ldur            x2, [x4, #0x17]
    // 0xbec760: mov             x1, x4
    // 0xbec764: r0 = _calculateCardHeight()
    //     0xbec764: bl              #0xa5b7d8  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xbec768: ldur            x0, [fp, #-8]
    // 0xbec76c: stur            d0, [fp, #-0x60]
    // 0xbec770: LoadField: r1 = r0->field_b
    //     0xbec770: ldur            w1, [x0, #0xb]
    // 0xbec774: DecompressPointer r1
    //     0xbec774: add             x1, x1, HEAP, lsl #32
    // 0xbec778: cmp             w1, NULL
    // 0xbec77c: b.eq            #0xbecb50
    // 0xbec780: LoadField: r2 = r1->field_b
    //     0xbec780: ldur            w2, [x1, #0xb]
    // 0xbec784: DecompressPointer r2
    //     0xbec784: add             x2, x2, HEAP, lsl #32
    // 0xbec788: cmp             w2, NULL
    // 0xbec78c: b.ne            #0xbec798
    // 0xbec790: r4 = Null
    //     0xbec790: mov             x4, NULL
    // 0xbec794: b               #0xbec7a0
    // 0xbec798: LoadField: r1 = r2->field_b
    //     0xbec798: ldur            w1, [x2, #0xb]
    // 0xbec79c: mov             x4, x1
    // 0xbec7a0: ldur            x3, [fp, #-0x28]
    // 0xbec7a4: stur            x4, [fp, #-0x38]
    // 0xbec7a8: LoadField: r5 = r0->field_13
    //     0xbec7a8: ldur            w5, [x0, #0x13]
    // 0xbec7ac: DecompressPointer r5
    //     0xbec7ac: add             x5, x5, HEAP, lsl #32
    // 0xbec7b0: r16 = Sentinel
    //     0xbec7b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbec7b4: cmp             w5, w16
    // 0xbec7b8: b.eq            #0xbecb54
    // 0xbec7bc: ldur            x2, [fp, #-0x20]
    // 0xbec7c0: stur            x5, [fp, #-0x30]
    // 0xbec7c4: r1 = Function '<anonymous closure>':.
    //     0xbec7c4: add             x1, PP, #0x53, lsl #12  ; [pp+0x535e0] AnonymousClosure: (0xbed9fc), in [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xbec24c)
    //     0xbec7c8: ldr             x1, [x1, #0x5e0]
    // 0xbec7cc: r0 = AllocateClosure()
    //     0xbec7cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbec7d0: ldur            x2, [fp, #-0x20]
    // 0xbec7d4: r1 = Function '<anonymous closure>':.
    //     0xbec7d4: add             x1, PP, #0x53, lsl #12  ; [pp+0x535e8] AnonymousClosure: (0xbecb78), in [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xbec24c)
    //     0xbec7d8: ldr             x1, [x1, #0x5e8]
    // 0xbec7dc: stur            x0, [fp, #-0x20]
    // 0xbec7e0: r0 = AllocateClosure()
    //     0xbec7e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbec7e4: stur            x0, [fp, #-0x40]
    // 0xbec7e8: r0 = PageView()
    //     0xbec7e8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbec7ec: stur            x0, [fp, #-0x48]
    // 0xbec7f0: r16 = Instance_BouncingScrollPhysics
    //     0xbec7f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbec7f4: ldr             x16, [x16, #0x890]
    // 0xbec7f8: ldur            lr, [fp, #-0x30]
    // 0xbec7fc: stp             lr, x16, [SP]
    // 0xbec800: mov             x1, x0
    // 0xbec804: ldur            x2, [fp, #-0x40]
    // 0xbec808: ldur            x3, [fp, #-0x38]
    // 0xbec80c: ldur            x5, [fp, #-0x20]
    // 0xbec810: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xbec810: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xbec814: ldr             x4, [x4, #0xe40]
    // 0xbec818: r0 = PageView.builder()
    //     0xbec818: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbec81c: ldur            d0, [fp, #-0x60]
    // 0xbec820: r0 = inline_Allocate_Double()
    //     0xbec820: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbec824: add             x0, x0, #0x10
    //     0xbec828: cmp             x1, x0
    //     0xbec82c: b.ls            #0xbecb60
    //     0xbec830: str             x0, [THR, #0x50]  ; THR::top
    //     0xbec834: sub             x0, x0, #0xf
    //     0xbec838: movz            x1, #0xe15c
    //     0xbec83c: movk            x1, #0x3, lsl #16
    //     0xbec840: stur            x1, [x0, #-1]
    // 0xbec844: StoreField: r0->field_7 = d0
    //     0xbec844: stur            d0, [x0, #7]
    // 0xbec848: stur            x0, [fp, #-0x20]
    // 0xbec84c: r0 = SizedBox()
    //     0xbec84c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbec850: mov             x1, x0
    // 0xbec854: ldur            x0, [fp, #-0x20]
    // 0xbec858: stur            x1, [fp, #-0x30]
    // 0xbec85c: StoreField: r1->field_13 = r0
    //     0xbec85c: stur            w0, [x1, #0x13]
    // 0xbec860: ldur            x0, [fp, #-0x48]
    // 0xbec864: StoreField: r1->field_b = r0
    //     0xbec864: stur            w0, [x1, #0xb]
    // 0xbec868: r0 = Padding()
    //     0xbec868: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbec86c: mov             x2, x0
    // 0xbec870: r0 = Instance_EdgeInsets
    //     0xbec870: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xbec874: ldr             x0, [x0, #0x100]
    // 0xbec878: stur            x2, [fp, #-0x20]
    // 0xbec87c: StoreField: r2->field_f = r0
    //     0xbec87c: stur            w0, [x2, #0xf]
    // 0xbec880: ldur            x0, [fp, #-0x30]
    // 0xbec884: StoreField: r2->field_b = r0
    //     0xbec884: stur            w0, [x2, #0xb]
    // 0xbec888: ldur            x0, [fp, #-0x28]
    // 0xbec88c: LoadField: r1 = r0->field_b
    //     0xbec88c: ldur            w1, [x0, #0xb]
    // 0xbec890: LoadField: r3 = r0->field_f
    //     0xbec890: ldur            w3, [x0, #0xf]
    // 0xbec894: DecompressPointer r3
    //     0xbec894: add             x3, x3, HEAP, lsl #32
    // 0xbec898: LoadField: r4 = r3->field_b
    //     0xbec898: ldur            w4, [x3, #0xb]
    // 0xbec89c: r3 = LoadInt32Instr(r1)
    //     0xbec89c: sbfx            x3, x1, #1, #0x1f
    // 0xbec8a0: stur            x3, [fp, #-0x50]
    // 0xbec8a4: r1 = LoadInt32Instr(r4)
    //     0xbec8a4: sbfx            x1, x4, #1, #0x1f
    // 0xbec8a8: cmp             x3, x1
    // 0xbec8ac: b.ne            #0xbec8b8
    // 0xbec8b0: mov             x1, x0
    // 0xbec8b4: r0 = _growToNextCapacity()
    //     0xbec8b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbec8b8: ldur            x4, [fp, #-8]
    // 0xbec8bc: ldur            x2, [fp, #-0x28]
    // 0xbec8c0: ldur            x3, [fp, #-0x50]
    // 0xbec8c4: add             x0, x3, #1
    // 0xbec8c8: lsl             x1, x0, #1
    // 0xbec8cc: StoreField: r2->field_b = r1
    //     0xbec8cc: stur            w1, [x2, #0xb]
    // 0xbec8d0: LoadField: r1 = r2->field_f
    //     0xbec8d0: ldur            w1, [x2, #0xf]
    // 0xbec8d4: DecompressPointer r1
    //     0xbec8d4: add             x1, x1, HEAP, lsl #32
    // 0xbec8d8: ldur            x0, [fp, #-0x20]
    // 0xbec8dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbec8dc: add             x25, x1, x3, lsl #2
    //     0xbec8e0: add             x25, x25, #0xf
    //     0xbec8e4: str             w0, [x25]
    //     0xbec8e8: tbz             w0, #0, #0xbec904
    //     0xbec8ec: ldurb           w16, [x1, #-1]
    //     0xbec8f0: ldurb           w17, [x0, #-1]
    //     0xbec8f4: and             x16, x17, x16, lsr #2
    //     0xbec8f8: tst             x16, HEAP, lsr #32
    //     0xbec8fc: b.eq            #0xbec904
    //     0xbec900: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbec904: LoadField: r0 = r4->field_b
    //     0xbec904: ldur            w0, [x4, #0xb]
    // 0xbec908: DecompressPointer r0
    //     0xbec908: add             x0, x0, HEAP, lsl #32
    // 0xbec90c: cmp             w0, NULL
    // 0xbec910: b.eq            #0xbecb70
    // 0xbec914: LoadField: r1 = r0->field_b
    //     0xbec914: ldur            w1, [x0, #0xb]
    // 0xbec918: DecompressPointer r1
    //     0xbec918: add             x1, x1, HEAP, lsl #32
    // 0xbec91c: cmp             w1, NULL
    // 0xbec920: b.eq            #0xbecb74
    // 0xbec924: LoadField: r0 = r1->field_b
    //     0xbec924: ldur            w0, [x1, #0xb]
    // 0xbec928: r1 = LoadInt32Instr(r0)
    //     0xbec928: sbfx            x1, x0, #1, #0x1f
    // 0xbec92c: cmp             x1, #1
    // 0xbec930: b.le            #0xbecab8
    // 0xbec934: r3 = LoadInt32Instr(r0)
    //     0xbec934: sbfx            x3, x0, #1, #0x1f
    // 0xbec938: stur            x3, [fp, #-0x58]
    // 0xbec93c: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xbec93c: ldur            x0, [x4, #0x17]
    // 0xbec940: ldur            x1, [fp, #-0x10]
    // 0xbec944: stur            x0, [fp, #-0x50]
    // 0xbec948: r0 = of()
    //     0xbec948: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbec94c: LoadField: r1 = r0->field_5b
    //     0xbec94c: ldur            w1, [x0, #0x5b]
    // 0xbec950: DecompressPointer r1
    //     0xbec950: add             x1, x1, HEAP, lsl #32
    // 0xbec954: stur            x1, [fp, #-8]
    // 0xbec958: r0 = CarouselIndicator()
    //     0xbec958: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbec95c: mov             x3, x0
    // 0xbec960: ldur            x0, [fp, #-0x58]
    // 0xbec964: stur            x3, [fp, #-0x10]
    // 0xbec968: StoreField: r3->field_b = r0
    //     0xbec968: stur            x0, [x3, #0xb]
    // 0xbec96c: ldur            x0, [fp, #-0x50]
    // 0xbec970: StoreField: r3->field_13 = r0
    //     0xbec970: stur            x0, [x3, #0x13]
    // 0xbec974: ldur            x0, [fp, #-8]
    // 0xbec978: StoreField: r3->field_1b = r0
    //     0xbec978: stur            w0, [x3, #0x1b]
    // 0xbec97c: r0 = Instance_Color
    //     0xbec97c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbec980: ldr             x0, [x0, #0x90]
    // 0xbec984: StoreField: r3->field_1f = r0
    //     0xbec984: stur            w0, [x3, #0x1f]
    // 0xbec988: r1 = Null
    //     0xbec988: mov             x1, NULL
    // 0xbec98c: r2 = 2
    //     0xbec98c: movz            x2, #0x2
    // 0xbec990: r0 = AllocateArray()
    //     0xbec990: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbec994: mov             x2, x0
    // 0xbec998: ldur            x0, [fp, #-0x10]
    // 0xbec99c: stur            x2, [fp, #-8]
    // 0xbec9a0: StoreField: r2->field_f = r0
    //     0xbec9a0: stur            w0, [x2, #0xf]
    // 0xbec9a4: r1 = <Widget>
    //     0xbec9a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbec9a8: r0 = AllocateGrowableArray()
    //     0xbec9a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbec9ac: mov             x1, x0
    // 0xbec9b0: ldur            x0, [fp, #-8]
    // 0xbec9b4: stur            x1, [fp, #-0x10]
    // 0xbec9b8: StoreField: r1->field_f = r0
    //     0xbec9b8: stur            w0, [x1, #0xf]
    // 0xbec9bc: r0 = 2
    //     0xbec9bc: movz            x0, #0x2
    // 0xbec9c0: StoreField: r1->field_b = r0
    //     0xbec9c0: stur            w0, [x1, #0xb]
    // 0xbec9c4: r0 = Row()
    //     0xbec9c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbec9c8: mov             x1, x0
    // 0xbec9cc: r0 = Instance_Axis
    //     0xbec9cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbec9d0: stur            x1, [fp, #-8]
    // 0xbec9d4: StoreField: r1->field_f = r0
    //     0xbec9d4: stur            w0, [x1, #0xf]
    // 0xbec9d8: r0 = Instance_MainAxisAlignment
    //     0xbec9d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbec9dc: ldr             x0, [x0, #0xab0]
    // 0xbec9e0: StoreField: r1->field_13 = r0
    //     0xbec9e0: stur            w0, [x1, #0x13]
    // 0xbec9e4: r0 = Instance_MainAxisSize
    //     0xbec9e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbec9e8: ldr             x0, [x0, #0xa10]
    // 0xbec9ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xbec9ec: stur            w0, [x1, #0x17]
    // 0xbec9f0: r0 = Instance_CrossAxisAlignment
    //     0xbec9f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbec9f4: ldr             x0, [x0, #0xa18]
    // 0xbec9f8: StoreField: r1->field_1b = r0
    //     0xbec9f8: stur            w0, [x1, #0x1b]
    // 0xbec9fc: r0 = Instance_VerticalDirection
    //     0xbec9fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbeca00: ldr             x0, [x0, #0xa20]
    // 0xbeca04: StoreField: r1->field_23 = r0
    //     0xbeca04: stur            w0, [x1, #0x23]
    // 0xbeca08: r2 = Instance_Clip
    //     0xbeca08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbeca0c: ldr             x2, [x2, #0x38]
    // 0xbeca10: StoreField: r1->field_2b = r2
    //     0xbeca10: stur            w2, [x1, #0x2b]
    // 0xbeca14: StoreField: r1->field_2f = rZR
    //     0xbeca14: stur            xzr, [x1, #0x2f]
    // 0xbeca18: ldur            x3, [fp, #-0x10]
    // 0xbeca1c: StoreField: r1->field_b = r3
    //     0xbeca1c: stur            w3, [x1, #0xb]
    // 0xbeca20: r0 = Padding()
    //     0xbeca20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbeca24: mov             x2, x0
    // 0xbeca28: r0 = Instance_EdgeInsets
    //     0xbeca28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbeca2c: ldr             x0, [x0, #0xa00]
    // 0xbeca30: stur            x2, [fp, #-0x10]
    // 0xbeca34: StoreField: r2->field_f = r0
    //     0xbeca34: stur            w0, [x2, #0xf]
    // 0xbeca38: ldur            x0, [fp, #-8]
    // 0xbeca3c: StoreField: r2->field_b = r0
    //     0xbeca3c: stur            w0, [x2, #0xb]
    // 0xbeca40: ldur            x0, [fp, #-0x28]
    // 0xbeca44: LoadField: r1 = r0->field_b
    //     0xbeca44: ldur            w1, [x0, #0xb]
    // 0xbeca48: LoadField: r3 = r0->field_f
    //     0xbeca48: ldur            w3, [x0, #0xf]
    // 0xbeca4c: DecompressPointer r3
    //     0xbeca4c: add             x3, x3, HEAP, lsl #32
    // 0xbeca50: LoadField: r4 = r3->field_b
    //     0xbeca50: ldur            w4, [x3, #0xb]
    // 0xbeca54: r3 = LoadInt32Instr(r1)
    //     0xbeca54: sbfx            x3, x1, #1, #0x1f
    // 0xbeca58: stur            x3, [fp, #-0x50]
    // 0xbeca5c: r1 = LoadInt32Instr(r4)
    //     0xbeca5c: sbfx            x1, x4, #1, #0x1f
    // 0xbeca60: cmp             x3, x1
    // 0xbeca64: b.ne            #0xbeca70
    // 0xbeca68: mov             x1, x0
    // 0xbeca6c: r0 = _growToNextCapacity()
    //     0xbeca6c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeca70: ldur            x2, [fp, #-0x28]
    // 0xbeca74: ldur            x3, [fp, #-0x50]
    // 0xbeca78: add             x0, x3, #1
    // 0xbeca7c: lsl             x1, x0, #1
    // 0xbeca80: StoreField: r2->field_b = r1
    //     0xbeca80: stur            w1, [x2, #0xb]
    // 0xbeca84: LoadField: r1 = r2->field_f
    //     0xbeca84: ldur            w1, [x2, #0xf]
    // 0xbeca88: DecompressPointer r1
    //     0xbeca88: add             x1, x1, HEAP, lsl #32
    // 0xbeca8c: ldur            x0, [fp, #-0x10]
    // 0xbeca90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbeca90: add             x25, x1, x3, lsl #2
    //     0xbeca94: add             x25, x25, #0xf
    //     0xbeca98: str             w0, [x25]
    //     0xbeca9c: tbz             w0, #0, #0xbecab8
    //     0xbecaa0: ldurb           w16, [x1, #-1]
    //     0xbecaa4: ldurb           w17, [x0, #-1]
    //     0xbecaa8: and             x16, x17, x16, lsr #2
    //     0xbecaac: tst             x16, HEAP, lsr #32
    //     0xbecab0: b.eq            #0xbecab8
    //     0xbecab4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbecab8: ldur            x0, [fp, #-0x18]
    // 0xbecabc: r0 = Column()
    //     0xbecabc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbecac0: mov             x1, x0
    // 0xbecac4: r0 = Instance_Axis
    //     0xbecac4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbecac8: stur            x1, [fp, #-8]
    // 0xbecacc: StoreField: r1->field_f = r0
    //     0xbecacc: stur            w0, [x1, #0xf]
    // 0xbecad0: r0 = Instance_MainAxisAlignment
    //     0xbecad0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbecad4: ldr             x0, [x0, #0xa08]
    // 0xbecad8: StoreField: r1->field_13 = r0
    //     0xbecad8: stur            w0, [x1, #0x13]
    // 0xbecadc: r0 = Instance_MainAxisSize
    //     0xbecadc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbecae0: ldr             x0, [x0, #0xdd0]
    // 0xbecae4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbecae4: stur            w0, [x1, #0x17]
    // 0xbecae8: ldur            x0, [fp, #-0x18]
    // 0xbecaec: StoreField: r1->field_1b = r0
    //     0xbecaec: stur            w0, [x1, #0x1b]
    // 0xbecaf0: r0 = Instance_VerticalDirection
    //     0xbecaf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbecaf4: ldr             x0, [x0, #0xa20]
    // 0xbecaf8: StoreField: r1->field_23 = r0
    //     0xbecaf8: stur            w0, [x1, #0x23]
    // 0xbecafc: r0 = Instance_Clip
    //     0xbecafc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbecb00: ldr             x0, [x0, #0x38]
    // 0xbecb04: StoreField: r1->field_2b = r0
    //     0xbecb04: stur            w0, [x1, #0x2b]
    // 0xbecb08: StoreField: r1->field_2f = rZR
    //     0xbecb08: stur            xzr, [x1, #0x2f]
    // 0xbecb0c: ldur            x0, [fp, #-0x28]
    // 0xbecb10: StoreField: r1->field_b = r0
    //     0xbecb10: stur            w0, [x1, #0xb]
    // 0xbecb14: r0 = Padding()
    //     0xbecb14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbecb18: r1 = Instance_EdgeInsets
    //     0xbecb18: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbecb1c: ldr             x1, [x1, #0x110]
    // 0xbecb20: StoreField: r0->field_f = r1
    //     0xbecb20: stur            w1, [x0, #0xf]
    // 0xbecb24: ldur            x1, [fp, #-8]
    // 0xbecb28: StoreField: r0->field_b = r1
    //     0xbecb28: stur            w1, [x0, #0xb]
    // 0xbecb2c: LeaveFrame
    //     0xbecb2c: mov             SP, fp
    //     0xbecb30: ldp             fp, lr, [SP], #0x10
    // 0xbecb34: ret
    //     0xbecb34: ret             
    // 0xbecb38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbecb38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbecb3c: b               #0xbec274
    // 0xbecb40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbecb44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbecb48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbecb4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbecb50: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbecb50: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbecb54: r9 = _pageController
    //     0xbecb54: add             x9, PP, #0x53, lsl #12  ; [pp+0x535d0] Field <_ProductTestimonialCarouselState@1707128090._pageController@1707128090>: late (offset: 0x14)
    //     0xbecb58: ldr             x9, [x9, #0x5d0]
    // 0xbecb5c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbecb5c: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbecb60: SaveReg d0
    //     0xbecb60: str             q0, [SP, #-0x10]!
    // 0xbecb64: r0 = AllocateDouble()
    //     0xbecb64: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbecb68: RestoreReg d0
    //     0xbecb68: ldr             q0, [SP], #0x10
    // 0xbecb6c: b               #0xbec844
    // 0xbecb70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbecb74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecb74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbecb78, size: 0x94
    // 0xbecb78: EnterFrame
    //     0xbecb78: stp             fp, lr, [SP, #-0x10]!
    //     0xbecb7c: mov             fp, SP
    // 0xbecb80: AllocStack(0x8)
    //     0xbecb80: sub             SP, SP, #8
    // 0xbecb84: SetupParameters()
    //     0xbecb84: ldr             x0, [fp, #0x20]
    //     0xbecb88: ldur            w1, [x0, #0x17]
    //     0xbecb8c: add             x1, x1, HEAP, lsl #32
    // 0xbecb90: CheckStackOverflow
    //     0xbecb90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbecb94: cmp             SP, x16
    //     0xbecb98: b.ls            #0xbecc00
    // 0xbecb9c: LoadField: r0 = r1->field_f
    //     0xbecb9c: ldur            w0, [x1, #0xf]
    // 0xbecba0: DecompressPointer r0
    //     0xbecba0: add             x0, x0, HEAP, lsl #32
    // 0xbecba4: stur            x0, [fp, #-8]
    // 0xbecba8: LoadField: r1 = r0->field_b
    //     0xbecba8: ldur            w1, [x0, #0xb]
    // 0xbecbac: DecompressPointer r1
    //     0xbecbac: add             x1, x1, HEAP, lsl #32
    // 0xbecbb0: cmp             w1, NULL
    // 0xbecbb4: b.eq            #0xbecc08
    // 0xbecbb8: LoadField: r2 = r1->field_b
    //     0xbecbb8: ldur            w2, [x1, #0xb]
    // 0xbecbbc: DecompressPointer r2
    //     0xbecbbc: add             x2, x2, HEAP, lsl #32
    // 0xbecbc0: cmp             w2, NULL
    // 0xbecbc4: b.ne            #0xbecbdc
    // 0xbecbc8: r1 = <Entity>
    //     0xbecbc8: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xbecbcc: ldr             x1, [x1, #0xb68]
    // 0xbecbd0: r2 = 0
    //     0xbecbd0: movz            x2, #0
    // 0xbecbd4: r0 = AllocateArray()
    //     0xbecbd4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbecbd8: mov             x2, x0
    // 0xbecbdc: ldr             x0, [fp, #0x10]
    // 0xbecbe0: r3 = LoadInt32Instr(r0)
    //     0xbecbe0: sbfx            x3, x0, #1, #0x1f
    //     0xbecbe4: tbz             w0, #0, #0xbecbec
    //     0xbecbe8: ldur            x3, [x0, #7]
    // 0xbecbec: ldur            x1, [fp, #-8]
    // 0xbecbf0: r0 = _testimonialCard()
    //     0xbecbf0: bl              #0xbecc0c  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xbecbf4: LeaveFrame
    //     0xbecbf4: mov             SP, fp
    //     0xbecbf8: ldp             fp, lr, [SP], #0x10
    // 0xbecbfc: ret
    //     0xbecbfc: ret             
    // 0xbecc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbecc00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbecc04: b               #0xbecb9c
    // 0xbecc08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbecc08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xbecc0c, size: 0xd64
    // 0xbecc0c: EnterFrame
    //     0xbecc0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbecc10: mov             fp, SP
    // 0xbecc14: AllocStack(0xa8)
    //     0xbecc14: sub             SP, SP, #0xa8
    // 0xbecc18: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbecc18: stur            x1, [fp, #-8]
    //     0xbecc1c: stur            x2, [fp, #-0x10]
    //     0xbecc20: stur            x3, [fp, #-0x18]
    // 0xbecc24: CheckStackOverflow
    //     0xbecc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbecc28: cmp             SP, x16
    //     0xbecc2c: b.ls            #0xbed94c
    // 0xbecc30: r1 = 2
    //     0xbecc30: movz            x1, #0x2
    // 0xbecc34: r0 = AllocateContext()
    //     0xbecc34: bl              #0x16f6108  ; AllocateContextStub
    // 0xbecc38: mov             x3, x0
    // 0xbecc3c: ldur            x2, [fp, #-8]
    // 0xbecc40: stur            x3, [fp, #-0x28]
    // 0xbecc44: StoreField: r3->field_f = r2
    //     0xbecc44: stur            w2, [x3, #0xf]
    // 0xbecc48: ldur            x4, [fp, #-0x18]
    // 0xbecc4c: r0 = BoxInt64Instr(r4)
    //     0xbecc4c: sbfiz           x0, x4, #1, #0x1f
    //     0xbecc50: cmp             x4, x0, asr #1
    //     0xbecc54: b.eq            #0xbecc60
    //     0xbecc58: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbecc5c: stur            x4, [x0, #7]
    // 0xbecc60: mov             x4, x0
    // 0xbecc64: ldur            x1, [fp, #-0x10]
    // 0xbecc68: stur            x4, [fp, #-0x20]
    // 0xbecc6c: r0 = LoadClassIdInstr(r1)
    //     0xbecc6c: ldur            x0, [x1, #-1]
    //     0xbecc70: ubfx            x0, x0, #0xc, #0x14
    // 0xbecc74: stp             x4, x1, [SP]
    // 0xbecc78: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbecc78: sub             lr, x0, #0xb7
    //     0xbecc7c: ldr             lr, [x21, lr, lsl #3]
    //     0xbecc80: blr             lr
    // 0xbecc84: stur            x0, [fp, #-0x40]
    // 0xbecc88: LoadField: r1 = r0->field_ab
    //     0xbecc88: ldur            w1, [x0, #0xab]
    // 0xbecc8c: DecompressPointer r1
    //     0xbecc8c: add             x1, x1, HEAP, lsl #32
    // 0xbecc90: cmp             w1, NULL
    // 0xbecc94: b.ne            #0xbecca0
    // 0xbecc98: r1 = Null
    //     0xbecc98: mov             x1, NULL
    // 0xbecc9c: b               #0xbeccb4
    // 0xbecca0: LoadField: r2 = r1->field_b
    //     0xbecca0: ldur            w2, [x1, #0xb]
    // 0xbecca4: cbnz            w2, #0xbeccb0
    // 0xbecca8: r1 = false
    //     0xbecca8: add             x1, NULL, #0x30  ; false
    // 0xbeccac: b               #0xbeccb4
    // 0xbeccb0: r1 = true
    //     0xbeccb0: add             x1, NULL, #0x20  ; true
    // 0xbeccb4: cmp             w1, NULL
    // 0xbeccb8: b.ne            #0xbeccc4
    // 0xbeccbc: r4 = false
    //     0xbeccbc: add             x4, NULL, #0x30  ; false
    // 0xbeccc0: b               #0xbeccc8
    // 0xbeccc4: mov             x4, x1
    // 0xbeccc8: ldur            x3, [fp, #-8]
    // 0xbecccc: stur            x4, [fp, #-0x38]
    // 0xbeccd0: LoadField: r5 = r3->field_1f
    //     0xbeccd0: ldur            w5, [x3, #0x1f]
    // 0xbeccd4: DecompressPointer r5
    //     0xbeccd4: add             x5, x5, HEAP, lsl #32
    // 0xbeccd8: mov             x1, x5
    // 0xbeccdc: ldur            x2, [fp, #-0x20]
    // 0xbecce0: stur            x5, [fp, #-0x30]
    // 0xbecce4: r0 = _getValueOrData()
    //     0xbecce4: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xbecce8: mov             x1, x0
    // 0xbeccec: ldur            x0, [fp, #-0x30]
    // 0xbeccf0: LoadField: r2 = r0->field_f
    //     0xbeccf0: ldur            w2, [x0, #0xf]
    // 0xbeccf4: DecompressPointer r2
    //     0xbeccf4: add             x2, x2, HEAP, lsl #32
    // 0xbeccf8: cmp             w2, w1
    // 0xbeccfc: b.ne            #0xbecd08
    // 0xbecd00: r0 = Null
    //     0xbecd00: mov             x0, NULL
    // 0xbecd04: b               #0xbecd0c
    // 0xbecd08: mov             x0, x1
    // 0xbecd0c: cmp             w0, NULL
    // 0xbecd10: b.ne            #0xbecd50
    // 0xbecd14: r1 = <bool>
    //     0xbecd14: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xbecd18: r0 = RxBool()
    //     0xbecd18: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xbecd1c: mov             x2, x0
    // 0xbecd20: r0 = Sentinel
    //     0xbecd20: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbecd24: stur            x2, [fp, #-0x30]
    // 0xbecd28: StoreField: r2->field_13 = r0
    //     0xbecd28: stur            w0, [x2, #0x13]
    // 0xbecd2c: r0 = true
    //     0xbecd2c: add             x0, NULL, #0x20  ; true
    // 0xbecd30: ArrayStore: r2[0] = r0  ; List_4
    //     0xbecd30: stur            w0, [x2, #0x17]
    // 0xbecd34: mov             x1, x2
    // 0xbecd38: r0 = RxNotifier()
    //     0xbecd38: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xbecd3c: ldur            x0, [fp, #-0x30]
    // 0xbecd40: r2 = false
    //     0xbecd40: add             x2, NULL, #0x30  ; false
    // 0xbecd44: StoreField: r0->field_13 = r2
    //     0xbecd44: stur            w2, [x0, #0x13]
    // 0xbecd48: mov             x5, x0
    // 0xbecd4c: b               #0xbecd58
    // 0xbecd50: r2 = false
    //     0xbecd50: add             x2, NULL, #0x30  ; false
    // 0xbecd54: mov             x5, x0
    // 0xbecd58: ldur            x4, [fp, #-0x28]
    // 0xbecd5c: ldur            x3, [fp, #-0x38]
    // 0xbecd60: mov             x0, x5
    // 0xbecd64: stur            x5, [fp, #-0x30]
    // 0xbecd68: StoreField: r4->field_13 = r0
    //     0xbecd68: stur            w0, [x4, #0x13]
    //     0xbecd6c: ldurb           w16, [x4, #-1]
    //     0xbecd70: ldurb           w17, [x0, #-1]
    //     0xbecd74: and             x16, x17, x16, lsr #2
    //     0xbecd78: tst             x16, HEAP, lsr #32
    //     0xbecd7c: b.eq            #0xbecd84
    //     0xbecd80: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xbecd84: r1 = Instance_Color
    //     0xbecd84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbecd88: d0 = 0.100000
    //     0xbecd88: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbecd8c: r0 = withOpacity()
    //     0xbecd8c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbecd90: mov             x2, x0
    // 0xbecd94: r1 = Null
    //     0xbecd94: mov             x1, NULL
    // 0xbecd98: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbecd98: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbecd9c: r0 = Border.all()
    //     0xbecd9c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbecda0: stur            x0, [fp, #-0x48]
    // 0xbecda4: r0 = BoxDecoration()
    //     0xbecda4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbecda8: mov             x3, x0
    // 0xbecdac: ldur            x0, [fp, #-0x48]
    // 0xbecdb0: stur            x3, [fp, #-0x50]
    // 0xbecdb4: StoreField: r3->field_f = r0
    //     0xbecdb4: stur            w0, [x3, #0xf]
    // 0xbecdb8: r0 = Instance_BoxShape
    //     0xbecdb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbecdbc: ldr             x0, [x0, #0x80]
    // 0xbecdc0: StoreField: r3->field_23 = r0
    //     0xbecdc0: stur            w0, [x3, #0x23]
    // 0xbecdc4: r1 = <Widget>
    //     0xbecdc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbecdc8: r2 = 0
    //     0xbecdc8: movz            x2, #0
    // 0xbecdcc: r0 = _GrowableList()
    //     0xbecdcc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbecdd0: mov             x1, x0
    // 0xbecdd4: ldur            x0, [fp, #-0x38]
    // 0xbecdd8: stur            x1, [fp, #-0x48]
    // 0xbecddc: tbnz            w0, #4, #0xbecf78
    // 0xbecde0: ldur            x2, [fp, #-0x10]
    // 0xbecde4: r0 = LoadClassIdInstr(r2)
    //     0xbecde4: ldur            x0, [x2, #-1]
    //     0xbecde8: ubfx            x0, x0, #0xc, #0x14
    // 0xbecdec: ldur            x16, [fp, #-0x20]
    // 0xbecdf0: stp             x16, x2, [SP]
    // 0xbecdf4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbecdf4: sub             lr, x0, #0xb7
    //     0xbecdf8: ldr             lr, [x21, lr, lsl #3]
    //     0xbecdfc: blr             lr
    // 0xbece00: LoadField: r2 = r0->field_ab
    //     0xbece00: ldur            w2, [x0, #0xab]
    // 0xbece04: DecompressPointer r2
    //     0xbece04: add             x2, x2, HEAP, lsl #32
    // 0xbece08: cmp             w2, NULL
    // 0xbece0c: b.ne            #0xbece18
    // 0xbece10: r0 = Null
    //     0xbece10: mov             x0, NULL
    // 0xbece14: b               #0xbece64
    // 0xbece18: LoadField: r0 = r2->field_b
    //     0xbece18: ldur            w0, [x2, #0xb]
    // 0xbece1c: r1 = LoadInt32Instr(r0)
    //     0xbece1c: sbfx            x1, x0, #1, #0x1f
    // 0xbece20: mov             x0, x1
    // 0xbece24: r1 = 0
    //     0xbece24: movz            x1, #0
    // 0xbece28: cmp             x1, x0
    // 0xbece2c: b.hs            #0xbed954
    // 0xbece30: LoadField: r0 = r2->field_f
    //     0xbece30: ldur            w0, [x2, #0xf]
    // 0xbece34: DecompressPointer r0
    //     0xbece34: add             x0, x0, HEAP, lsl #32
    // 0xbece38: LoadField: r1 = r0->field_f
    //     0xbece38: ldur            w1, [x0, #0xf]
    // 0xbece3c: DecompressPointer r1
    //     0xbece3c: add             x1, x1, HEAP, lsl #32
    // 0xbece40: LoadField: r0 = r1->field_7
    //     0xbece40: ldur            w0, [x1, #7]
    // 0xbece44: DecompressPointer r0
    //     0xbece44: add             x0, x0, HEAP, lsl #32
    // 0xbece48: cmp             w0, NULL
    // 0xbece4c: b.ne            #0xbece58
    // 0xbece50: r0 = Null
    //     0xbece50: mov             x0, NULL
    // 0xbece54: b               #0xbece64
    // 0xbece58: LoadField: r1 = r0->field_b
    //     0xbece58: ldur            w1, [x0, #0xb]
    // 0xbece5c: DecompressPointer r1
    //     0xbece5c: add             x1, x1, HEAP, lsl #32
    // 0xbece60: mov             x0, x1
    // 0xbece64: cmp             w0, NULL
    // 0xbece68: b.ne            #0xbece74
    // 0xbece6c: r2 = ""
    //     0xbece6c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbece70: b               #0xbece78
    // 0xbece74: mov             x2, x0
    // 0xbece78: ldur            x1, [fp, #-0x48]
    // 0xbece7c: stur            x2, [fp, #-0x38]
    // 0xbece80: r0 = ImageHeaders.forImages()
    //     0xbece80: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbece84: r1 = Function '<anonymous closure>':.
    //     0xbece84: add             x1, PP, #0x53, lsl #12  ; [pp+0x535f0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbece88: ldr             x1, [x1, #0x5f0]
    // 0xbece8c: r2 = Null
    //     0xbece8c: mov             x2, NULL
    // 0xbece90: stur            x0, [fp, #-0x58]
    // 0xbece94: r0 = AllocateClosure()
    //     0xbece94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbece98: r1 = Function '<anonymous closure>':.
    //     0xbece98: add             x1, PP, #0x53, lsl #12  ; [pp+0x535f8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbece9c: ldr             x1, [x1, #0x5f8]
    // 0xbecea0: r2 = Null
    //     0xbecea0: mov             x2, NULL
    // 0xbecea4: stur            x0, [fp, #-0x60]
    // 0xbecea8: r0 = AllocateClosure()
    //     0xbecea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeceac: stur            x0, [fp, #-0x68]
    // 0xbeceb0: r0 = CachedNetworkImage()
    //     0xbeceb0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbeceb4: stur            x0, [fp, #-0x70]
    // 0xbeceb8: ldur            x16, [fp, #-0x58]
    // 0xbecebc: ldur            lr, [fp, #-0x60]
    // 0xbecec0: stp             lr, x16, [SP, #0x20]
    // 0xbecec4: r16 = Instance_BoxFit
    //     0xbecec4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbecec8: ldr             x16, [x16, #0x118]
    // 0xbececc: ldur            lr, [fp, #-0x68]
    // 0xbeced0: stp             lr, x16, [SP, #0x10]
    // 0xbeced4: r16 = inf
    //     0xbeced4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbeced8: ldr             x16, [x16, #0x9f8]
    // 0xbecedc: r30 = 366.000000
    //     0xbecedc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52680] 366
    //     0xbecee0: ldr             lr, [lr, #0x680]
    // 0xbecee4: stp             lr, x16, [SP]
    // 0xbecee8: mov             x1, x0
    // 0xbeceec: ldur            x2, [fp, #-0x38]
    // 0xbecef0: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x7, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, width, 0x6, null]
    //     0xbecef0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52688] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x7, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, "width", 0x6, Null]
    //     0xbecef4: ldr             x4, [x4, #0x688]
    // 0xbecef8: r0 = CachedNetworkImage()
    //     0xbecef8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbecefc: ldur            x0, [fp, #-0x48]
    // 0xbecf00: LoadField: r1 = r0->field_b
    //     0xbecf00: ldur            w1, [x0, #0xb]
    // 0xbecf04: LoadField: r2 = r0->field_f
    //     0xbecf04: ldur            w2, [x0, #0xf]
    // 0xbecf08: DecompressPointer r2
    //     0xbecf08: add             x2, x2, HEAP, lsl #32
    // 0xbecf0c: LoadField: r3 = r2->field_b
    //     0xbecf0c: ldur            w3, [x2, #0xb]
    // 0xbecf10: r2 = LoadInt32Instr(r1)
    //     0xbecf10: sbfx            x2, x1, #1, #0x1f
    // 0xbecf14: stur            x2, [fp, #-0x18]
    // 0xbecf18: r1 = LoadInt32Instr(r3)
    //     0xbecf18: sbfx            x1, x3, #1, #0x1f
    // 0xbecf1c: cmp             x2, x1
    // 0xbecf20: b.ne            #0xbecf2c
    // 0xbecf24: mov             x1, x0
    // 0xbecf28: r0 = _growToNextCapacity()
    //     0xbecf28: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbecf2c: ldur            x2, [fp, #-0x48]
    // 0xbecf30: ldur            x3, [fp, #-0x18]
    // 0xbecf34: add             x0, x3, #1
    // 0xbecf38: lsl             x1, x0, #1
    // 0xbecf3c: StoreField: r2->field_b = r1
    //     0xbecf3c: stur            w1, [x2, #0xb]
    // 0xbecf40: LoadField: r1 = r2->field_f
    //     0xbecf40: ldur            w1, [x2, #0xf]
    // 0xbecf44: DecompressPointer r1
    //     0xbecf44: add             x1, x1, HEAP, lsl #32
    // 0xbecf48: ldur            x0, [fp, #-0x70]
    // 0xbecf4c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbecf4c: add             x25, x1, x3, lsl #2
    //     0xbecf50: add             x25, x25, #0xf
    //     0xbecf54: str             w0, [x25]
    //     0xbecf58: tbz             w0, #0, #0xbecf74
    //     0xbecf5c: ldurb           w16, [x1, #-1]
    //     0xbecf60: ldurb           w17, [x0, #-1]
    //     0xbecf64: and             x16, x17, x16, lsr #2
    //     0xbecf68: tst             x16, HEAP, lsr #32
    //     0xbecf6c: b.eq            #0xbecf74
    //     0xbecf70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbecf74: b               #0xbed008
    // 0xbecf78: mov             x2, x1
    // 0xbecf7c: r0 = Container()
    //     0xbecf7c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbecf80: mov             x1, x0
    // 0xbecf84: stur            x0, [fp, #-0x38]
    // 0xbecf88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbecf88: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbecf8c: r0 = Container()
    //     0xbecf8c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbecf90: ldur            x0, [fp, #-0x48]
    // 0xbecf94: LoadField: r1 = r0->field_b
    //     0xbecf94: ldur            w1, [x0, #0xb]
    // 0xbecf98: LoadField: r2 = r0->field_f
    //     0xbecf98: ldur            w2, [x0, #0xf]
    // 0xbecf9c: DecompressPointer r2
    //     0xbecf9c: add             x2, x2, HEAP, lsl #32
    // 0xbecfa0: LoadField: r3 = r2->field_b
    //     0xbecfa0: ldur            w3, [x2, #0xb]
    // 0xbecfa4: r2 = LoadInt32Instr(r1)
    //     0xbecfa4: sbfx            x2, x1, #1, #0x1f
    // 0xbecfa8: stur            x2, [fp, #-0x18]
    // 0xbecfac: r1 = LoadInt32Instr(r3)
    //     0xbecfac: sbfx            x1, x3, #1, #0x1f
    // 0xbecfb0: cmp             x2, x1
    // 0xbecfb4: b.ne            #0xbecfc0
    // 0xbecfb8: mov             x1, x0
    // 0xbecfbc: r0 = _growToNextCapacity()
    //     0xbecfbc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbecfc0: ldur            x2, [fp, #-0x48]
    // 0xbecfc4: ldur            x3, [fp, #-0x18]
    // 0xbecfc8: add             x0, x3, #1
    // 0xbecfcc: lsl             x1, x0, #1
    // 0xbecfd0: StoreField: r2->field_b = r1
    //     0xbecfd0: stur            w1, [x2, #0xb]
    // 0xbecfd4: LoadField: r1 = r2->field_f
    //     0xbecfd4: ldur            w1, [x2, #0xf]
    // 0xbecfd8: DecompressPointer r1
    //     0xbecfd8: add             x1, x1, HEAP, lsl #32
    // 0xbecfdc: ldur            x0, [fp, #-0x38]
    // 0xbecfe0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbecfe0: add             x25, x1, x3, lsl #2
    //     0xbecfe4: add             x25, x25, #0xf
    //     0xbecfe8: str             w0, [x25]
    //     0xbecfec: tbz             w0, #0, #0xbed008
    //     0xbecff0: ldurb           w16, [x1, #-1]
    //     0xbecff4: ldurb           w17, [x0, #-1]
    //     0xbecff8: and             x16, x17, x16, lsr #2
    //     0xbecffc: tst             x16, HEAP, lsr #32
    //     0xbed000: b.eq            #0xbed008
    //     0xbed004: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbed008: ldur            x1, [fp, #-0x10]
    // 0xbed00c: r0 = LoadClassIdInstr(r1)
    //     0xbed00c: ldur            x0, [x1, #-1]
    //     0xbed010: ubfx            x0, x0, #0xc, #0x14
    // 0xbed014: ldur            x16, [fp, #-0x20]
    // 0xbed018: stp             x16, x1, [SP]
    // 0xbed01c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbed01c: sub             lr, x0, #0xb7
    //     0xbed020: ldr             lr, [x21, lr, lsl #3]
    //     0xbed024: blr             lr
    // 0xbed028: LoadField: r1 = r0->field_97
    //     0xbed028: ldur            w1, [x0, #0x97]
    // 0xbed02c: DecompressPointer r1
    //     0xbed02c: add             x1, x1, HEAP, lsl #32
    // 0xbed030: cmp             w1, NULL
    // 0xbed034: b.ne            #0xbed03c
    // 0xbed038: r1 = ""
    //     0xbed038: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbed03c: ldur            x2, [fp, #-8]
    // 0xbed040: ldur            x0, [fp, #-0x48]
    // 0xbed044: r0 = capitalizeFirstWord()
    //     0xbed044: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbed048: mov             x2, x0
    // 0xbed04c: ldur            x0, [fp, #-8]
    // 0xbed050: stur            x2, [fp, #-0x38]
    // 0xbed054: LoadField: r1 = r0->field_f
    //     0xbed054: ldur            w1, [x0, #0xf]
    // 0xbed058: DecompressPointer r1
    //     0xbed058: add             x1, x1, HEAP, lsl #32
    // 0xbed05c: cmp             w1, NULL
    // 0xbed060: b.eq            #0xbed958
    // 0xbed064: r0 = of()
    //     0xbed064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbed068: LoadField: r1 = r0->field_87
    //     0xbed068: ldur            w1, [x0, #0x87]
    // 0xbed06c: DecompressPointer r1
    //     0xbed06c: add             x1, x1, HEAP, lsl #32
    // 0xbed070: LoadField: r0 = r1->field_7
    //     0xbed070: ldur            w0, [x1, #7]
    // 0xbed074: DecompressPointer r0
    //     0xbed074: add             x0, x0, HEAP, lsl #32
    // 0xbed078: r16 = Instance_Color
    //     0xbed078: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbed07c: r30 = 14.000000
    //     0xbed07c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbed080: ldr             lr, [lr, #0x1d8]
    // 0xbed084: stp             lr, x16, [SP]
    // 0xbed088: mov             x1, x0
    // 0xbed08c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbed08c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbed090: ldr             x4, [x4, #0x9b8]
    // 0xbed094: r0 = copyWith()
    //     0xbed094: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbed098: stur            x0, [fp, #-0x58]
    // 0xbed09c: r0 = Text()
    //     0xbed09c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbed0a0: mov             x1, x0
    // 0xbed0a4: ldur            x0, [fp, #-0x38]
    // 0xbed0a8: stur            x1, [fp, #-0x60]
    // 0xbed0ac: StoreField: r1->field_b = r0
    //     0xbed0ac: stur            w0, [x1, #0xb]
    // 0xbed0b0: ldur            x0, [fp, #-0x58]
    // 0xbed0b4: StoreField: r1->field_13 = r0
    //     0xbed0b4: stur            w0, [x1, #0x13]
    // 0xbed0b8: r0 = Padding()
    //     0xbed0b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbed0bc: mov             x2, x0
    // 0xbed0c0: r0 = Instance_EdgeInsets
    //     0xbed0c0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52690] Obj!EdgeInsets@d59781
    //     0xbed0c4: ldr             x0, [x0, #0x690]
    // 0xbed0c8: stur            x2, [fp, #-0x38]
    // 0xbed0cc: StoreField: r2->field_f = r0
    //     0xbed0cc: stur            w0, [x2, #0xf]
    // 0xbed0d0: ldur            x0, [fp, #-0x60]
    // 0xbed0d4: StoreField: r2->field_b = r0
    //     0xbed0d4: stur            w0, [x2, #0xb]
    // 0xbed0d8: ldur            x0, [fp, #-0x48]
    // 0xbed0dc: LoadField: r1 = r0->field_b
    //     0xbed0dc: ldur            w1, [x0, #0xb]
    // 0xbed0e0: LoadField: r3 = r0->field_f
    //     0xbed0e0: ldur            w3, [x0, #0xf]
    // 0xbed0e4: DecompressPointer r3
    //     0xbed0e4: add             x3, x3, HEAP, lsl #32
    // 0xbed0e8: LoadField: r4 = r3->field_b
    //     0xbed0e8: ldur            w4, [x3, #0xb]
    // 0xbed0ec: r3 = LoadInt32Instr(r1)
    //     0xbed0ec: sbfx            x3, x1, #1, #0x1f
    // 0xbed0f0: stur            x3, [fp, #-0x18]
    // 0xbed0f4: r1 = LoadInt32Instr(r4)
    //     0xbed0f4: sbfx            x1, x4, #1, #0x1f
    // 0xbed0f8: cmp             x3, x1
    // 0xbed0fc: b.ne            #0xbed108
    // 0xbed100: mov             x1, x0
    // 0xbed104: r0 = _growToNextCapacity()
    //     0xbed104: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbed108: ldur            x4, [fp, #-0x10]
    // 0xbed10c: ldur            x2, [fp, #-0x48]
    // 0xbed110: ldur            x3, [fp, #-0x18]
    // 0xbed114: add             x0, x3, #1
    // 0xbed118: lsl             x1, x0, #1
    // 0xbed11c: StoreField: r2->field_b = r1
    //     0xbed11c: stur            w1, [x2, #0xb]
    // 0xbed120: LoadField: r1 = r2->field_f
    //     0xbed120: ldur            w1, [x2, #0xf]
    // 0xbed124: DecompressPointer r1
    //     0xbed124: add             x1, x1, HEAP, lsl #32
    // 0xbed128: ldur            x0, [fp, #-0x38]
    // 0xbed12c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbed12c: add             x25, x1, x3, lsl #2
    //     0xbed130: add             x25, x25, #0xf
    //     0xbed134: str             w0, [x25]
    //     0xbed138: tbz             w0, #0, #0xbed154
    //     0xbed13c: ldurb           w16, [x1, #-1]
    //     0xbed140: ldurb           w17, [x0, #-1]
    //     0xbed144: and             x16, x17, x16, lsr #2
    //     0xbed148: tst             x16, HEAP, lsr #32
    //     0xbed14c: b.eq            #0xbed154
    //     0xbed150: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbed154: r0 = LoadClassIdInstr(r4)
    //     0xbed154: ldur            x0, [x4, #-1]
    //     0xbed158: ubfx            x0, x0, #0xc, #0x14
    // 0xbed15c: ldur            x16, [fp, #-0x20]
    // 0xbed160: stp             x16, x4, [SP]
    // 0xbed164: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbed164: sub             lr, x0, #0xb7
    //     0xbed168: ldr             lr, [x21, lr, lsl #3]
    //     0xbed16c: blr             lr
    // 0xbed170: LoadField: r1 = r0->field_9b
    //     0xbed170: ldur            w1, [x0, #0x9b]
    // 0xbed174: DecompressPointer r1
    //     0xbed174: add             x1, x1, HEAP, lsl #32
    // 0xbed178: cmp             w1, NULL
    // 0xbed17c: b.ne            #0xbed184
    // 0xbed180: r1 = ""
    //     0xbed180: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbed184: ldur            x0, [fp, #-0x10]
    // 0xbed188: r0 = parse()
    //     0xbed188: bl              #0x64333c  ; [dart:core] double::parse
    // 0xbed18c: ldur            x1, [fp, #-0x10]
    // 0xbed190: stur            d0, [fp, #-0x78]
    // 0xbed194: r0 = LoadClassIdInstr(r1)
    //     0xbed194: ldur            x0, [x1, #-1]
    //     0xbed198: ubfx            x0, x0, #0xc, #0x14
    // 0xbed19c: ldur            x16, [fp, #-0x20]
    // 0xbed1a0: stp             x16, x1, [SP]
    // 0xbed1a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbed1a4: sub             lr, x0, #0xb7
    //     0xbed1a8: ldr             lr, [x21, lr, lsl #3]
    //     0xbed1ac: blr             lr
    // 0xbed1b0: LoadField: r1 = r0->field_9b
    //     0xbed1b0: ldur            w1, [x0, #0x9b]
    // 0xbed1b4: DecompressPointer r1
    //     0xbed1b4: add             x1, x1, HEAP, lsl #32
    // 0xbed1b8: cmp             w1, NULL
    // 0xbed1bc: b.ne            #0xbed1c4
    // 0xbed1c0: r1 = "0"
    //     0xbed1c0: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xbed1c4: ldur            x0, [fp, #-0x10]
    // 0xbed1c8: ldur            d0, [fp, #-0x78]
    // 0xbed1cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbed1cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbed1d0: r0 = parse()
    //     0xbed1d0: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xbed1d4: stur            x0, [fp, #-0x18]
    // 0xbed1d8: r0 = RatingWidget()
    //     0xbed1d8: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xbed1dc: mov             x3, x0
    // 0xbed1e0: r0 = Instance_Icon
    //     0xbed1e0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xbed1e4: ldr             x0, [x0, #0x190]
    // 0xbed1e8: stur            x3, [fp, #-0x38]
    // 0xbed1ec: StoreField: r3->field_7 = r0
    //     0xbed1ec: stur            w0, [x3, #7]
    // 0xbed1f0: r0 = Instance_Icon
    //     0xbed1f0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xbed1f4: ldr             x0, [x0, #0x198]
    // 0xbed1f8: StoreField: r3->field_b = r0
    //     0xbed1f8: stur            w0, [x3, #0xb]
    // 0xbed1fc: r0 = Instance_Icon
    //     0xbed1fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xbed200: ldr             x0, [x0, #0x1a0]
    // 0xbed204: StoreField: r3->field_f = r0
    //     0xbed204: stur            w0, [x3, #0xf]
    // 0xbed208: r1 = Function '<anonymous closure>':.
    //     0xbed208: add             x1, PP, #0x53, lsl #12  ; [pp+0x53600] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbed20c: ldr             x1, [x1, #0x600]
    // 0xbed210: r2 = Null
    //     0xbed210: mov             x2, NULL
    // 0xbed214: r0 = AllocateClosure()
    //     0xbed214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbed218: stur            x0, [fp, #-0x58]
    // 0xbed21c: r0 = RatingBar()
    //     0xbed21c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xbed220: mov             x1, x0
    // 0xbed224: ldur            x0, [fp, #-0x58]
    // 0xbed228: stur            x1, [fp, #-0x60]
    // 0xbed22c: StoreField: r1->field_b = r0
    //     0xbed22c: stur            w0, [x1, #0xb]
    // 0xbed230: r0 = true
    //     0xbed230: add             x0, NULL, #0x20  ; true
    // 0xbed234: StoreField: r1->field_1f = r0
    //     0xbed234: stur            w0, [x1, #0x1f]
    // 0xbed238: r2 = Instance_Axis
    //     0xbed238: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbed23c: StoreField: r1->field_23 = r2
    //     0xbed23c: stur            w2, [x1, #0x23]
    // 0xbed240: StoreField: r1->field_27 = r0
    //     0xbed240: stur            w0, [x1, #0x27]
    // 0xbed244: d0 = 2.000000
    //     0xbed244: fmov            d0, #2.00000000
    // 0xbed248: StoreField: r1->field_2b = d0
    //     0xbed248: stur            d0, [x1, #0x2b]
    // 0xbed24c: StoreField: r1->field_33 = r0
    //     0xbed24c: stur            w0, [x1, #0x33]
    // 0xbed250: ldur            d0, [fp, #-0x78]
    // 0xbed254: StoreField: r1->field_37 = d0
    //     0xbed254: stur            d0, [x1, #0x37]
    // 0xbed258: ldur            x3, [fp, #-0x18]
    // 0xbed25c: StoreField: r1->field_3f = r3
    //     0xbed25c: stur            x3, [x1, #0x3f]
    // 0xbed260: r3 = Instance_EdgeInsets
    //     0xbed260: ldr             x3, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbed264: StoreField: r1->field_47 = r3
    //     0xbed264: stur            w3, [x1, #0x47]
    // 0xbed268: d0 = 20.000000
    //     0xbed268: fmov            d0, #20.00000000
    // 0xbed26c: StoreField: r1->field_4b = d0
    //     0xbed26c: stur            d0, [x1, #0x4b]
    // 0xbed270: StoreField: r1->field_53 = rZR
    //     0xbed270: stur            xzr, [x1, #0x53]
    // 0xbed274: r3 = false
    //     0xbed274: add             x3, NULL, #0x30  ; false
    // 0xbed278: StoreField: r1->field_5b = r3
    //     0xbed278: stur            w3, [x1, #0x5b]
    // 0xbed27c: StoreField: r1->field_5f = r3
    //     0xbed27c: stur            w3, [x1, #0x5f]
    // 0xbed280: ldur            x3, [fp, #-0x38]
    // 0xbed284: StoreField: r1->field_67 = r3
    //     0xbed284: stur            w3, [x1, #0x67]
    // 0xbed288: r0 = SizedBox()
    //     0xbed288: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbed28c: mov             x1, x0
    // 0xbed290: ldur            x0, [fp, #-0x60]
    // 0xbed294: stur            x1, [fp, #-0x38]
    // 0xbed298: StoreField: r1->field_b = r0
    //     0xbed298: stur            w0, [x1, #0xb]
    // 0xbed29c: ldur            x0, [fp, #-0x10]
    // 0xbed2a0: r2 = LoadClassIdInstr(r0)
    //     0xbed2a0: ldur            x2, [x0, #-1]
    //     0xbed2a4: ubfx            x2, x2, #0xc, #0x14
    // 0xbed2a8: ldur            x16, [fp, #-0x20]
    // 0xbed2ac: stp             x16, x0, [SP]
    // 0xbed2b0: mov             x0, x2
    // 0xbed2b4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbed2b4: sub             lr, x0, #0xb7
    //     0xbed2b8: ldr             lr, [x21, lr, lsl #3]
    //     0xbed2bc: blr             lr
    // 0xbed2c0: LoadField: r1 = r0->field_a7
    //     0xbed2c0: ldur            w1, [x0, #0xa7]
    // 0xbed2c4: DecompressPointer r1
    //     0xbed2c4: add             x1, x1, HEAP, lsl #32
    // 0xbed2c8: cmp             w1, NULL
    // 0xbed2cc: b.ne            #0xbed2d8
    // 0xbed2d0: r4 = ""
    //     0xbed2d0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbed2d4: b               #0xbed2dc
    // 0xbed2d8: mov             x4, x1
    // 0xbed2dc: ldur            x3, [fp, #-8]
    // 0xbed2e0: ldur            x2, [fp, #-0x48]
    // 0xbed2e4: ldur            x0, [fp, #-0x38]
    // 0xbed2e8: stur            x4, [fp, #-0x10]
    // 0xbed2ec: LoadField: r1 = r3->field_f
    //     0xbed2ec: ldur            w1, [x3, #0xf]
    // 0xbed2f0: DecompressPointer r1
    //     0xbed2f0: add             x1, x1, HEAP, lsl #32
    // 0xbed2f4: cmp             w1, NULL
    // 0xbed2f8: b.eq            #0xbed95c
    // 0xbed2fc: r0 = of()
    //     0xbed2fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbed300: LoadField: r1 = r0->field_87
    //     0xbed300: ldur            w1, [x0, #0x87]
    // 0xbed304: DecompressPointer r1
    //     0xbed304: add             x1, x1, HEAP, lsl #32
    // 0xbed308: LoadField: r0 = r1->field_2b
    //     0xbed308: ldur            w0, [x1, #0x2b]
    // 0xbed30c: DecompressPointer r0
    //     0xbed30c: add             x0, x0, HEAP, lsl #32
    // 0xbed310: r16 = 12.000000
    //     0xbed310: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbed314: ldr             x16, [x16, #0x9e8]
    // 0xbed318: r30 = Instance_Color
    //     0xbed318: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbed31c: stp             lr, x16, [SP]
    // 0xbed320: mov             x1, x0
    // 0xbed324: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbed324: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbed328: ldr             x4, [x4, #0xaa0]
    // 0xbed32c: r0 = copyWith()
    //     0xbed32c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbed330: stur            x0, [fp, #-0x20]
    // 0xbed334: r0 = Text()
    //     0xbed334: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbed338: mov             x3, x0
    // 0xbed33c: ldur            x0, [fp, #-0x10]
    // 0xbed340: stur            x3, [fp, #-0x58]
    // 0xbed344: StoreField: r3->field_b = r0
    //     0xbed344: stur            w0, [x3, #0xb]
    // 0xbed348: ldur            x0, [fp, #-0x20]
    // 0xbed34c: StoreField: r3->field_13 = r0
    //     0xbed34c: stur            w0, [x3, #0x13]
    // 0xbed350: r1 = Null
    //     0xbed350: mov             x1, NULL
    // 0xbed354: r2 = 4
    //     0xbed354: movz            x2, #0x4
    // 0xbed358: r0 = AllocateArray()
    //     0xbed358: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbed35c: mov             x2, x0
    // 0xbed360: ldur            x0, [fp, #-0x38]
    // 0xbed364: stur            x2, [fp, #-0x10]
    // 0xbed368: StoreField: r2->field_f = r0
    //     0xbed368: stur            w0, [x2, #0xf]
    // 0xbed36c: ldur            x0, [fp, #-0x58]
    // 0xbed370: StoreField: r2->field_13 = r0
    //     0xbed370: stur            w0, [x2, #0x13]
    // 0xbed374: r1 = <Widget>
    //     0xbed374: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbed378: r0 = AllocateGrowableArray()
    //     0xbed378: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbed37c: mov             x1, x0
    // 0xbed380: ldur            x0, [fp, #-0x10]
    // 0xbed384: stur            x1, [fp, #-0x20]
    // 0xbed388: StoreField: r1->field_f = r0
    //     0xbed388: stur            w0, [x1, #0xf]
    // 0xbed38c: r0 = 4
    //     0xbed38c: movz            x0, #0x4
    // 0xbed390: StoreField: r1->field_b = r0
    //     0xbed390: stur            w0, [x1, #0xb]
    // 0xbed394: r0 = Row()
    //     0xbed394: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbed398: mov             x1, x0
    // 0xbed39c: r0 = Instance_Axis
    //     0xbed39c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbed3a0: stur            x1, [fp, #-0x10]
    // 0xbed3a4: StoreField: r1->field_f = r0
    //     0xbed3a4: stur            w0, [x1, #0xf]
    // 0xbed3a8: r0 = Instance_MainAxisAlignment
    //     0xbed3a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbed3ac: ldr             x0, [x0, #0xa08]
    // 0xbed3b0: StoreField: r1->field_13 = r0
    //     0xbed3b0: stur            w0, [x1, #0x13]
    // 0xbed3b4: r2 = Instance_MainAxisSize
    //     0xbed3b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbed3b8: ldr             x2, [x2, #0xa10]
    // 0xbed3bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xbed3bc: stur            w2, [x1, #0x17]
    // 0xbed3c0: r3 = Instance_CrossAxisAlignment
    //     0xbed3c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbed3c4: ldr             x3, [x3, #0xa18]
    // 0xbed3c8: StoreField: r1->field_1b = r3
    //     0xbed3c8: stur            w3, [x1, #0x1b]
    // 0xbed3cc: r3 = Instance_VerticalDirection
    //     0xbed3cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbed3d0: ldr             x3, [x3, #0xa20]
    // 0xbed3d4: StoreField: r1->field_23 = r3
    //     0xbed3d4: stur            w3, [x1, #0x23]
    // 0xbed3d8: r4 = Instance_Clip
    //     0xbed3d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbed3dc: ldr             x4, [x4, #0x38]
    // 0xbed3e0: StoreField: r1->field_2b = r4
    //     0xbed3e0: stur            w4, [x1, #0x2b]
    // 0xbed3e4: StoreField: r1->field_2f = rZR
    //     0xbed3e4: stur            xzr, [x1, #0x2f]
    // 0xbed3e8: ldur            x5, [fp, #-0x20]
    // 0xbed3ec: StoreField: r1->field_b = r5
    //     0xbed3ec: stur            w5, [x1, #0xb]
    // 0xbed3f0: r0 = Padding()
    //     0xbed3f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbed3f4: mov             x2, x0
    // 0xbed3f8: r0 = Instance_EdgeInsets
    //     0xbed3f8: add             x0, PP, #0x52, lsl #12  ; [pp+0x526a0] Obj!EdgeInsets@d59751
    //     0xbed3fc: ldr             x0, [x0, #0x6a0]
    // 0xbed400: stur            x2, [fp, #-0x20]
    // 0xbed404: StoreField: r2->field_f = r0
    //     0xbed404: stur            w0, [x2, #0xf]
    // 0xbed408: ldur            x0, [fp, #-0x10]
    // 0xbed40c: StoreField: r2->field_b = r0
    //     0xbed40c: stur            w0, [x2, #0xb]
    // 0xbed410: ldur            x0, [fp, #-0x48]
    // 0xbed414: LoadField: r1 = r0->field_b
    //     0xbed414: ldur            w1, [x0, #0xb]
    // 0xbed418: LoadField: r3 = r0->field_f
    //     0xbed418: ldur            w3, [x0, #0xf]
    // 0xbed41c: DecompressPointer r3
    //     0xbed41c: add             x3, x3, HEAP, lsl #32
    // 0xbed420: LoadField: r4 = r3->field_b
    //     0xbed420: ldur            w4, [x3, #0xb]
    // 0xbed424: r3 = LoadInt32Instr(r1)
    //     0xbed424: sbfx            x3, x1, #1, #0x1f
    // 0xbed428: stur            x3, [fp, #-0x18]
    // 0xbed42c: r1 = LoadInt32Instr(r4)
    //     0xbed42c: sbfx            x1, x4, #1, #0x1f
    // 0xbed430: cmp             x3, x1
    // 0xbed434: b.ne            #0xbed440
    // 0xbed438: mov             x1, x0
    // 0xbed43c: r0 = _growToNextCapacity()
    //     0xbed43c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbed440: ldur            x4, [fp, #-0x40]
    // 0xbed444: ldur            x2, [fp, #-0x48]
    // 0xbed448: ldur            x3, [fp, #-0x18]
    // 0xbed44c: add             x0, x3, #1
    // 0xbed450: lsl             x1, x0, #1
    // 0xbed454: StoreField: r2->field_b = r1
    //     0xbed454: stur            w1, [x2, #0xb]
    // 0xbed458: LoadField: r1 = r2->field_f
    //     0xbed458: ldur            w1, [x2, #0xf]
    // 0xbed45c: DecompressPointer r1
    //     0xbed45c: add             x1, x1, HEAP, lsl #32
    // 0xbed460: ldur            x0, [fp, #-0x20]
    // 0xbed464: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbed464: add             x25, x1, x3, lsl #2
    //     0xbed468: add             x25, x25, #0xf
    //     0xbed46c: str             w0, [x25]
    //     0xbed470: tbz             w0, #0, #0xbed48c
    //     0xbed474: ldurb           w16, [x1, #-1]
    //     0xbed478: ldurb           w17, [x0, #-1]
    //     0xbed47c: and             x16, x17, x16, lsr #2
    //     0xbed480: tst             x16, HEAP, lsr #32
    //     0xbed484: b.eq            #0xbed48c
    //     0xbed488: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbed48c: LoadField: r1 = r4->field_9f
    //     0xbed48c: ldur            w1, [x4, #0x9f]
    // 0xbed490: DecompressPointer r1
    //     0xbed490: add             x1, x1, HEAP, lsl #32
    // 0xbed494: cmp             w1, NULL
    // 0xbed498: b.ne            #0xbed4a4
    // 0xbed49c: r0 = Null
    //     0xbed49c: mov             x0, NULL
    // 0xbed4a0: b               #0xbed4a8
    // 0xbed4a4: r0 = trim()
    //     0xbed4a4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xbed4a8: cmp             w0, NULL
    // 0xbed4ac: b.ne            #0xbed4b4
    // 0xbed4b0: r0 = ""
    //     0xbed4b0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbed4b4: ldur            x1, [fp, #-0x30]
    // 0xbed4b8: stur            x0, [fp, #-0x10]
    // 0xbed4bc: r0 = value()
    //     0xbed4bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbed4c0: tbnz            w0, #4, #0xbed4cc
    // 0xbed4c4: r0 = Null
    //     0xbed4c4: mov             x0, NULL
    // 0xbed4c8: b               #0xbed4d0
    // 0xbed4cc: r0 = 4
    //     0xbed4cc: movz            x0, #0x4
    // 0xbed4d0: ldur            x1, [fp, #-0x30]
    // 0xbed4d4: stur            x0, [fp, #-0x20]
    // 0xbed4d8: r0 = value()
    //     0xbed4d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbed4dc: tbnz            w0, #4, #0xbed4ec
    // 0xbed4e0: r5 = Instance_TextOverflow
    //     0xbed4e0: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xbed4e4: ldr             x5, [x5, #0x3a8]
    // 0xbed4e8: b               #0xbed4f4
    // 0xbed4ec: r5 = Instance_TextOverflow
    //     0xbed4ec: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbed4f0: ldr             x5, [x5, #0xe10]
    // 0xbed4f4: ldur            x4, [fp, #-8]
    // 0xbed4f8: ldur            x3, [fp, #-0x40]
    // 0xbed4fc: ldur            x2, [fp, #-0x10]
    // 0xbed500: ldur            x0, [fp, #-0x20]
    // 0xbed504: stur            x5, [fp, #-0x38]
    // 0xbed508: LoadField: r1 = r4->field_f
    //     0xbed508: ldur            w1, [x4, #0xf]
    // 0xbed50c: DecompressPointer r1
    //     0xbed50c: add             x1, x1, HEAP, lsl #32
    // 0xbed510: cmp             w1, NULL
    // 0xbed514: b.eq            #0xbed960
    // 0xbed518: r0 = of()
    //     0xbed518: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbed51c: LoadField: r1 = r0->field_87
    //     0xbed51c: ldur            w1, [x0, #0x87]
    // 0xbed520: DecompressPointer r1
    //     0xbed520: add             x1, x1, HEAP, lsl #32
    // 0xbed524: LoadField: r0 = r1->field_2b
    //     0xbed524: ldur            w0, [x1, #0x2b]
    // 0xbed528: DecompressPointer r0
    //     0xbed528: add             x0, x0, HEAP, lsl #32
    // 0xbed52c: LoadField: r1 = r0->field_13
    //     0xbed52c: ldur            w1, [x0, #0x13]
    // 0xbed530: DecompressPointer r1
    //     0xbed530: add             x1, x1, HEAP, lsl #32
    // 0xbed534: stur            x1, [fp, #-0x58]
    // 0xbed538: r0 = TextStyle()
    //     0xbed538: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xbed53c: mov             x1, x0
    // 0xbed540: r0 = true
    //     0xbed540: add             x0, NULL, #0x20  ; true
    // 0xbed544: stur            x1, [fp, #-0x60]
    // 0xbed548: StoreField: r1->field_7 = r0
    //     0xbed548: stur            w0, [x1, #7]
    // 0xbed54c: r0 = Instance_Color
    //     0xbed54c: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbed550: StoreField: r1->field_b = r0
    //     0xbed550: stur            w0, [x1, #0xb]
    // 0xbed554: r0 = 12.000000
    //     0xbed554: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbed558: ldr             x0, [x0, #0x9e8]
    // 0xbed55c: StoreField: r1->field_1f = r0
    //     0xbed55c: stur            w0, [x1, #0x1f]
    // 0xbed560: ldur            x0, [fp, #-0x58]
    // 0xbed564: StoreField: r1->field_13 = r0
    //     0xbed564: stur            w0, [x1, #0x13]
    // 0xbed568: r0 = Text()
    //     0xbed568: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbed56c: mov             x3, x0
    // 0xbed570: ldur            x0, [fp, #-0x10]
    // 0xbed574: stur            x3, [fp, #-0x58]
    // 0xbed578: StoreField: r3->field_b = r0
    //     0xbed578: stur            w0, [x3, #0xb]
    // 0xbed57c: ldur            x0, [fp, #-0x60]
    // 0xbed580: StoreField: r3->field_13 = r0
    //     0xbed580: stur            w0, [x3, #0x13]
    // 0xbed584: r0 = Instance_TextAlign
    //     0xbed584: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbed588: StoreField: r3->field_1b = r0
    //     0xbed588: stur            w0, [x3, #0x1b]
    // 0xbed58c: ldur            x0, [fp, #-0x38]
    // 0xbed590: StoreField: r3->field_2b = r0
    //     0xbed590: stur            w0, [x3, #0x2b]
    // 0xbed594: ldur            x0, [fp, #-0x20]
    // 0xbed598: StoreField: r3->field_37 = r0
    //     0xbed598: stur            w0, [x3, #0x37]
    // 0xbed59c: r1 = Null
    //     0xbed59c: mov             x1, NULL
    // 0xbed5a0: r2 = 2
    //     0xbed5a0: movz            x2, #0x2
    // 0xbed5a4: r0 = AllocateArray()
    //     0xbed5a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbed5a8: mov             x2, x0
    // 0xbed5ac: ldur            x0, [fp, #-0x58]
    // 0xbed5b0: stur            x2, [fp, #-0x10]
    // 0xbed5b4: StoreField: r2->field_f = r0
    //     0xbed5b4: stur            w0, [x2, #0xf]
    // 0xbed5b8: r1 = <Widget>
    //     0xbed5b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbed5bc: r0 = AllocateGrowableArray()
    //     0xbed5bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbed5c0: mov             x2, x0
    // 0xbed5c4: ldur            x0, [fp, #-0x10]
    // 0xbed5c8: stur            x2, [fp, #-0x20]
    // 0xbed5cc: StoreField: r2->field_f = r0
    //     0xbed5cc: stur            w0, [x2, #0xf]
    // 0xbed5d0: r0 = 2
    //     0xbed5d0: movz            x0, #0x2
    // 0xbed5d4: StoreField: r2->field_b = r0
    //     0xbed5d4: stur            w0, [x2, #0xb]
    // 0xbed5d8: ldur            x0, [fp, #-0x40]
    // 0xbed5dc: LoadField: r1 = r0->field_9f
    //     0xbed5dc: ldur            w1, [x0, #0x9f]
    // 0xbed5e0: DecompressPointer r1
    //     0xbed5e0: add             x1, x1, HEAP, lsl #32
    // 0xbed5e4: cmp             w1, NULL
    // 0xbed5e8: b.ne            #0xbed5f4
    // 0xbed5ec: r0 = Null
    //     0xbed5ec: mov             x0, NULL
    // 0xbed5f0: b               #0xbed5f8
    // 0xbed5f4: r0 = trim()
    //     0xbed5f4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xbed5f8: cmp             w0, NULL
    // 0xbed5fc: b.ne            #0xbed608
    // 0xbed600: r1 = ""
    //     0xbed600: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbed604: b               #0xbed60c
    // 0xbed608: mov             x1, x0
    // 0xbed60c: ldur            x0, [fp, #-8]
    // 0xbed610: LoadField: r2 = r0->field_f
    //     0xbed610: ldur            w2, [x0, #0xf]
    // 0xbed614: DecompressPointer r2
    //     0xbed614: add             x2, x2, HEAP, lsl #32
    // 0xbed618: cmp             w2, NULL
    // 0xbed61c: b.eq            #0xbed964
    // 0xbed620: r0 = TextExceeds.textExceedsLines()
    //     0xbed620: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xbed624: tbnz            w0, #4, #0xbed7b4
    // 0xbed628: ldur            x1, [fp, #-0x30]
    // 0xbed62c: r0 = value()
    //     0xbed62c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbed630: tbnz            w0, #4, #0xbed640
    // 0xbed634: r3 = "Know Less"
    //     0xbed634: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xbed638: ldr             x3, [x3, #0x1d0]
    // 0xbed63c: b               #0xbed648
    // 0xbed640: r3 = "Know more"
    //     0xbed640: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xbed644: ldr             x3, [x3, #0x20]
    // 0xbed648: ldur            x0, [fp, #-8]
    // 0xbed64c: ldur            x2, [fp, #-0x20]
    // 0xbed650: stur            x3, [fp, #-0x10]
    // 0xbed654: LoadField: r1 = r0->field_f
    //     0xbed654: ldur            w1, [x0, #0xf]
    // 0xbed658: DecompressPointer r1
    //     0xbed658: add             x1, x1, HEAP, lsl #32
    // 0xbed65c: cmp             w1, NULL
    // 0xbed660: b.eq            #0xbed968
    // 0xbed664: r0 = of()
    //     0xbed664: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbed668: LoadField: r1 = r0->field_87
    //     0xbed668: ldur            w1, [x0, #0x87]
    // 0xbed66c: DecompressPointer r1
    //     0xbed66c: add             x1, x1, HEAP, lsl #32
    // 0xbed670: LoadField: r0 = r1->field_7
    //     0xbed670: ldur            w0, [x1, #7]
    // 0xbed674: DecompressPointer r0
    //     0xbed674: add             x0, x0, HEAP, lsl #32
    // 0xbed678: ldur            x1, [fp, #-8]
    // 0xbed67c: stur            x0, [fp, #-0x30]
    // 0xbed680: LoadField: r2 = r1->field_f
    //     0xbed680: ldur            w2, [x1, #0xf]
    // 0xbed684: DecompressPointer r2
    //     0xbed684: add             x2, x2, HEAP, lsl #32
    // 0xbed688: cmp             w2, NULL
    // 0xbed68c: b.eq            #0xbed96c
    // 0xbed690: mov             x1, x2
    // 0xbed694: r0 = of()
    //     0xbed694: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbed698: LoadField: r1 = r0->field_5b
    //     0xbed698: ldur            w1, [x0, #0x5b]
    // 0xbed69c: DecompressPointer r1
    //     0xbed69c: add             x1, x1, HEAP, lsl #32
    // 0xbed6a0: r16 = 12.000000
    //     0xbed6a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbed6a4: ldr             x16, [x16, #0x9e8]
    // 0xbed6a8: stp             x1, x16, [SP, #8]
    // 0xbed6ac: r16 = Instance_TextDecoration
    //     0xbed6ac: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbed6b0: ldr             x16, [x16, #0x10]
    // 0xbed6b4: str             x16, [SP]
    // 0xbed6b8: ldur            x1, [fp, #-0x30]
    // 0xbed6bc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbed6bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbed6c0: ldr             x4, [x4, #0xe38]
    // 0xbed6c4: r0 = copyWith()
    //     0xbed6c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbed6c8: stur            x0, [fp, #-8]
    // 0xbed6cc: r0 = Text()
    //     0xbed6cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbed6d0: mov             x1, x0
    // 0xbed6d4: ldur            x0, [fp, #-0x10]
    // 0xbed6d8: stur            x1, [fp, #-0x30]
    // 0xbed6dc: StoreField: r1->field_b = r0
    //     0xbed6dc: stur            w0, [x1, #0xb]
    // 0xbed6e0: ldur            x0, [fp, #-8]
    // 0xbed6e4: StoreField: r1->field_13 = r0
    //     0xbed6e4: stur            w0, [x1, #0x13]
    // 0xbed6e8: r0 = Padding()
    //     0xbed6e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbed6ec: mov             x1, x0
    // 0xbed6f0: r0 = Instance_EdgeInsets
    //     0xbed6f0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbed6f4: ldr             x0, [x0, #0x668]
    // 0xbed6f8: stur            x1, [fp, #-8]
    // 0xbed6fc: StoreField: r1->field_f = r0
    //     0xbed6fc: stur            w0, [x1, #0xf]
    // 0xbed700: ldur            x0, [fp, #-0x30]
    // 0xbed704: StoreField: r1->field_b = r0
    //     0xbed704: stur            w0, [x1, #0xb]
    // 0xbed708: r0 = GestureDetector()
    //     0xbed708: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbed70c: ldur            x2, [fp, #-0x28]
    // 0xbed710: r1 = Function '<anonymous closure>':.
    //     0xbed710: add             x1, PP, #0x53, lsl #12  ; [pp+0x53608] AnonymousClosure: (0xbed970), in [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xbecc0c)
    //     0xbed714: ldr             x1, [x1, #0x608]
    // 0xbed718: stur            x0, [fp, #-0x10]
    // 0xbed71c: r0 = AllocateClosure()
    //     0xbed71c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbed720: ldur            x16, [fp, #-8]
    // 0xbed724: stp             x16, x0, [SP]
    // 0xbed728: ldur            x1, [fp, #-0x10]
    // 0xbed72c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbed72c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbed730: ldr             x4, [x4, #0xaf0]
    // 0xbed734: r0 = GestureDetector()
    //     0xbed734: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbed738: ldur            x0, [fp, #-0x20]
    // 0xbed73c: LoadField: r1 = r0->field_b
    //     0xbed73c: ldur            w1, [x0, #0xb]
    // 0xbed740: LoadField: r2 = r0->field_f
    //     0xbed740: ldur            w2, [x0, #0xf]
    // 0xbed744: DecompressPointer r2
    //     0xbed744: add             x2, x2, HEAP, lsl #32
    // 0xbed748: LoadField: r3 = r2->field_b
    //     0xbed748: ldur            w3, [x2, #0xb]
    // 0xbed74c: r2 = LoadInt32Instr(r1)
    //     0xbed74c: sbfx            x2, x1, #1, #0x1f
    // 0xbed750: stur            x2, [fp, #-0x18]
    // 0xbed754: r1 = LoadInt32Instr(r3)
    //     0xbed754: sbfx            x1, x3, #1, #0x1f
    // 0xbed758: cmp             x2, x1
    // 0xbed75c: b.ne            #0xbed768
    // 0xbed760: mov             x1, x0
    // 0xbed764: r0 = _growToNextCapacity()
    //     0xbed764: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbed768: ldur            x2, [fp, #-0x20]
    // 0xbed76c: ldur            x3, [fp, #-0x18]
    // 0xbed770: add             x0, x3, #1
    // 0xbed774: lsl             x1, x0, #1
    // 0xbed778: StoreField: r2->field_b = r1
    //     0xbed778: stur            w1, [x2, #0xb]
    // 0xbed77c: LoadField: r1 = r2->field_f
    //     0xbed77c: ldur            w1, [x2, #0xf]
    // 0xbed780: DecompressPointer r1
    //     0xbed780: add             x1, x1, HEAP, lsl #32
    // 0xbed784: ldur            x0, [fp, #-0x10]
    // 0xbed788: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbed788: add             x25, x1, x3, lsl #2
    //     0xbed78c: add             x25, x25, #0xf
    //     0xbed790: str             w0, [x25]
    //     0xbed794: tbz             w0, #0, #0xbed7b0
    //     0xbed798: ldurb           w16, [x1, #-1]
    //     0xbed79c: ldurb           w17, [x0, #-1]
    //     0xbed7a0: and             x16, x17, x16, lsr #2
    //     0xbed7a4: tst             x16, HEAP, lsr #32
    //     0xbed7a8: b.eq            #0xbed7b0
    //     0xbed7ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbed7b0: b               #0xbed7b8
    // 0xbed7b4: ldur            x2, [fp, #-0x20]
    // 0xbed7b8: ldur            x1, [fp, #-0x48]
    // 0xbed7bc: r0 = Column()
    //     0xbed7bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbed7c0: mov             x1, x0
    // 0xbed7c4: r0 = Instance_Axis
    //     0xbed7c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbed7c8: stur            x1, [fp, #-8]
    // 0xbed7cc: StoreField: r1->field_f = r0
    //     0xbed7cc: stur            w0, [x1, #0xf]
    // 0xbed7d0: r0 = Instance_MainAxisAlignment
    //     0xbed7d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbed7d4: ldr             x0, [x0, #0xa08]
    // 0xbed7d8: StoreField: r1->field_13 = r0
    //     0xbed7d8: stur            w0, [x1, #0x13]
    // 0xbed7dc: r0 = Instance_MainAxisSize
    //     0xbed7dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbed7e0: ldr             x0, [x0, #0xa10]
    // 0xbed7e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbed7e4: stur            w0, [x1, #0x17]
    // 0xbed7e8: r0 = Instance_CrossAxisAlignment
    //     0xbed7e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbed7ec: ldr             x0, [x0, #0x890]
    // 0xbed7f0: StoreField: r1->field_1b = r0
    //     0xbed7f0: stur            w0, [x1, #0x1b]
    // 0xbed7f4: r0 = Instance_VerticalDirection
    //     0xbed7f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbed7f8: ldr             x0, [x0, #0xa20]
    // 0xbed7fc: StoreField: r1->field_23 = r0
    //     0xbed7fc: stur            w0, [x1, #0x23]
    // 0xbed800: r0 = Instance_Clip
    //     0xbed800: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbed804: ldr             x0, [x0, #0x38]
    // 0xbed808: StoreField: r1->field_2b = r0
    //     0xbed808: stur            w0, [x1, #0x2b]
    // 0xbed80c: StoreField: r1->field_2f = rZR
    //     0xbed80c: stur            xzr, [x1, #0x2f]
    // 0xbed810: ldur            x0, [fp, #-0x20]
    // 0xbed814: StoreField: r1->field_b = r0
    //     0xbed814: stur            w0, [x1, #0xb]
    // 0xbed818: r0 = Padding()
    //     0xbed818: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbed81c: mov             x2, x0
    // 0xbed820: r0 = Instance_EdgeInsets
    //     0xbed820: add             x0, PP, #0x52, lsl #12  ; [pp+0x526b0] Obj!EdgeInsets@d59721
    //     0xbed824: ldr             x0, [x0, #0x6b0]
    // 0xbed828: stur            x2, [fp, #-0x10]
    // 0xbed82c: StoreField: r2->field_f = r0
    //     0xbed82c: stur            w0, [x2, #0xf]
    // 0xbed830: ldur            x0, [fp, #-8]
    // 0xbed834: StoreField: r2->field_b = r0
    //     0xbed834: stur            w0, [x2, #0xb]
    // 0xbed838: ldur            x0, [fp, #-0x48]
    // 0xbed83c: LoadField: r1 = r0->field_b
    //     0xbed83c: ldur            w1, [x0, #0xb]
    // 0xbed840: LoadField: r3 = r0->field_f
    //     0xbed840: ldur            w3, [x0, #0xf]
    // 0xbed844: DecompressPointer r3
    //     0xbed844: add             x3, x3, HEAP, lsl #32
    // 0xbed848: LoadField: r4 = r3->field_b
    //     0xbed848: ldur            w4, [x3, #0xb]
    // 0xbed84c: r3 = LoadInt32Instr(r1)
    //     0xbed84c: sbfx            x3, x1, #1, #0x1f
    // 0xbed850: stur            x3, [fp, #-0x18]
    // 0xbed854: r1 = LoadInt32Instr(r4)
    //     0xbed854: sbfx            x1, x4, #1, #0x1f
    // 0xbed858: cmp             x3, x1
    // 0xbed85c: b.ne            #0xbed868
    // 0xbed860: mov             x1, x0
    // 0xbed864: r0 = _growToNextCapacity()
    //     0xbed864: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbed868: ldur            x2, [fp, #-0x48]
    // 0xbed86c: ldur            x3, [fp, #-0x18]
    // 0xbed870: add             x0, x3, #1
    // 0xbed874: lsl             x1, x0, #1
    // 0xbed878: StoreField: r2->field_b = r1
    //     0xbed878: stur            w1, [x2, #0xb]
    // 0xbed87c: LoadField: r1 = r2->field_f
    //     0xbed87c: ldur            w1, [x2, #0xf]
    // 0xbed880: DecompressPointer r1
    //     0xbed880: add             x1, x1, HEAP, lsl #32
    // 0xbed884: ldur            x0, [fp, #-0x10]
    // 0xbed888: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbed888: add             x25, x1, x3, lsl #2
    //     0xbed88c: add             x25, x25, #0xf
    //     0xbed890: str             w0, [x25]
    //     0xbed894: tbz             w0, #0, #0xbed8b0
    //     0xbed898: ldurb           w16, [x1, #-1]
    //     0xbed89c: ldurb           w17, [x0, #-1]
    //     0xbed8a0: and             x16, x17, x16, lsr #2
    //     0xbed8a4: tst             x16, HEAP, lsr #32
    //     0xbed8a8: b.eq            #0xbed8b0
    //     0xbed8ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbed8b0: r0 = ListView()
    //     0xbed8b0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbed8b4: stur            x0, [fp, #-8]
    // 0xbed8b8: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbed8b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbed8bc: ldr             x16, [x16, #0x1c8]
    // 0xbed8c0: r30 = true
    //     0xbed8c0: add             lr, NULL, #0x20  ; true
    // 0xbed8c4: stp             lr, x16, [SP, #8]
    // 0xbed8c8: r16 = Instance_EdgeInsets
    //     0xbed8c8: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbed8cc: str             x16, [SP]
    // 0xbed8d0: mov             x1, x0
    // 0xbed8d4: ldur            x2, [fp, #-0x48]
    // 0xbed8d8: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xbed8d8: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xbed8dc: ldr             x4, [x4, #0x6b8]
    // 0xbed8e0: r0 = ListView()
    //     0xbed8e0: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xbed8e4: r0 = Container()
    //     0xbed8e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbed8e8: stur            x0, [fp, #-0x10]
    // 0xbed8ec: ldur            x16, [fp, #-0x50]
    // 0xbed8f0: ldur            lr, [fp, #-8]
    // 0xbed8f4: stp             lr, x16, [SP]
    // 0xbed8f8: mov             x1, x0
    // 0xbed8fc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbed8fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbed900: ldr             x4, [x4, #0x88]
    // 0xbed904: r0 = Container()
    //     0xbed904: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbed908: r0 = AnimatedContainer()
    //     0xbed908: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbed90c: stur            x0, [fp, #-8]
    // 0xbed910: r16 = Instance_Cubic
    //     0xbed910: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xbed914: ldr             x16, [x16, #0xaf8]
    // 0xbed918: r30 = Instance_EdgeInsets
    //     0xbed918: add             lr, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xbed91c: ldr             lr, [lr, #0x18]
    // 0xbed920: stp             lr, x16, [SP]
    // 0xbed924: mov             x1, x0
    // 0xbed928: ldur            x2, [fp, #-0x10]
    // 0xbed92c: r3 = Instance_Duration
    //     0xbed92c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbed930: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, margin, 0x4, null]
    //     0xbed930: add             x4, PP, #0x52, lsl #12  ; [pp+0x52218] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "margin", 0x4, Null]
    //     0xbed934: ldr             x4, [x4, #0x218]
    // 0xbed938: r0 = AnimatedContainer()
    //     0xbed938: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbed93c: ldur            x0, [fp, #-8]
    // 0xbed940: LeaveFrame
    //     0xbed940: mov             SP, fp
    //     0xbed944: ldp             fp, lr, [SP], #0x10
    // 0xbed948: ret
    //     0xbed948: ret             
    // 0xbed94c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbed94c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbed950: b               #0xbecc30
    // 0xbed954: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbed954: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbed958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed958: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbed95c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed95c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbed960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed960: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbed964: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed964: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbed968: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed968: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbed96c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbed96c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbed970, size: 0x8c
    // 0xbed970: EnterFrame
    //     0xbed970: stp             fp, lr, [SP, #-0x10]!
    //     0xbed974: mov             fp, SP
    // 0xbed978: AllocStack(0x10)
    //     0xbed978: sub             SP, SP, #0x10
    // 0xbed97c: SetupParameters()
    //     0xbed97c: ldr             x0, [fp, #0x10]
    //     0xbed980: ldur            w2, [x0, #0x17]
    //     0xbed984: add             x2, x2, HEAP, lsl #32
    //     0xbed988: stur            x2, [fp, #-0x10]
    // 0xbed98c: CheckStackOverflow
    //     0xbed98c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbed990: cmp             SP, x16
    //     0xbed994: b.ls            #0xbed9f4
    // 0xbed998: LoadField: r0 = r2->field_13
    //     0xbed998: ldur            w0, [x2, #0x13]
    // 0xbed99c: DecompressPointer r0
    //     0xbed99c: add             x0, x0, HEAP, lsl #32
    // 0xbed9a0: mov             x1, x0
    // 0xbed9a4: stur            x0, [fp, #-8]
    // 0xbed9a8: r0 = value()
    //     0xbed9a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbed9ac: eor             x2, x0, #0x10
    // 0xbed9b0: ldur            x1, [fp, #-8]
    // 0xbed9b4: r0 = value=()
    //     0xbed9b4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbed9b8: ldur            x0, [fp, #-0x10]
    // 0xbed9bc: LoadField: r3 = r0->field_f
    //     0xbed9bc: ldur            w3, [x0, #0xf]
    // 0xbed9c0: DecompressPointer r3
    //     0xbed9c0: add             x3, x3, HEAP, lsl #32
    // 0xbed9c4: stur            x3, [fp, #-8]
    // 0xbed9c8: r1 = Function '<anonymous closure>':.
    //     0xbed9c8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53610] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbed9cc: ldr             x1, [x1, #0x610]
    // 0xbed9d0: r2 = Null
    //     0xbed9d0: mov             x2, NULL
    // 0xbed9d4: r0 = AllocateClosure()
    //     0xbed9d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbed9d8: ldur            x1, [fp, #-8]
    // 0xbed9dc: mov             x2, x0
    // 0xbed9e0: r0 = setState()
    //     0xbed9e0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbed9e4: r0 = Null
    //     0xbed9e4: mov             x0, NULL
    // 0xbed9e8: LeaveFrame
    //     0xbed9e8: mov             SP, fp
    //     0xbed9ec: ldp             fp, lr, [SP], #0x10
    // 0xbed9f0: ret
    //     0xbed9f0: ret             
    // 0xbed9f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbed9f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbed9f8: b               #0xbed998
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbed9fc, size: 0x84
    // 0xbed9fc: EnterFrame
    //     0xbed9fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbeda00: mov             fp, SP
    // 0xbeda04: AllocStack(0x10)
    //     0xbeda04: sub             SP, SP, #0x10
    // 0xbeda08: SetupParameters()
    //     0xbeda08: ldr             x0, [fp, #0x18]
    //     0xbeda0c: ldur            w1, [x0, #0x17]
    //     0xbeda10: add             x1, x1, HEAP, lsl #32
    //     0xbeda14: stur            x1, [fp, #-8]
    // 0xbeda18: CheckStackOverflow
    //     0xbeda18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeda1c: cmp             SP, x16
    //     0xbeda20: b.ls            #0xbeda78
    // 0xbeda24: r1 = 1
    //     0xbeda24: movz            x1, #0x1
    // 0xbeda28: r0 = AllocateContext()
    //     0xbeda28: bl              #0x16f6108  ; AllocateContextStub
    // 0xbeda2c: mov             x1, x0
    // 0xbeda30: ldur            x0, [fp, #-8]
    // 0xbeda34: StoreField: r1->field_b = r0
    //     0xbeda34: stur            w0, [x1, #0xb]
    // 0xbeda38: ldr             x2, [fp, #0x10]
    // 0xbeda3c: StoreField: r1->field_f = r2
    //     0xbeda3c: stur            w2, [x1, #0xf]
    // 0xbeda40: LoadField: r3 = r0->field_f
    //     0xbeda40: ldur            w3, [x0, #0xf]
    // 0xbeda44: DecompressPointer r3
    //     0xbeda44: add             x3, x3, HEAP, lsl #32
    // 0xbeda48: mov             x2, x1
    // 0xbeda4c: stur            x3, [fp, #-0x10]
    // 0xbeda50: r1 = Function '<anonymous closure>':.
    //     0xbeda50: add             x1, PP, #0x53, lsl #12  ; [pp+0x53618] AnonymousClosure: (0xbeda80), in [package:customer_app/app/presentation/views/line/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xbec24c)
    //     0xbeda54: ldr             x1, [x1, #0x618]
    // 0xbeda58: r0 = AllocateClosure()
    //     0xbeda58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeda5c: ldur            x1, [fp, #-0x10]
    // 0xbeda60: mov             x2, x0
    // 0xbeda64: r0 = setState()
    //     0xbeda64: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbeda68: r0 = Null
    //     0xbeda68: mov             x0, NULL
    // 0xbeda6c: LeaveFrame
    //     0xbeda6c: mov             SP, fp
    //     0xbeda70: ldp             fp, lr, [SP], #0x10
    // 0xbeda74: ret
    //     0xbeda74: ret             
    // 0xbeda78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeda78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeda7c: b               #0xbeda24
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbeda80, size: 0x8c
    // 0xbeda80: EnterFrame
    //     0xbeda80: stp             fp, lr, [SP, #-0x10]!
    //     0xbeda84: mov             fp, SP
    // 0xbeda88: AllocStack(0x8)
    //     0xbeda88: sub             SP, SP, #8
    // 0xbeda8c: SetupParameters()
    //     0xbeda8c: ldr             x0, [fp, #0x10]
    //     0xbeda90: ldur            w1, [x0, #0x17]
    //     0xbeda94: add             x1, x1, HEAP, lsl #32
    // 0xbeda98: CheckStackOverflow
    //     0xbeda98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeda9c: cmp             SP, x16
    //     0xbedaa0: b.ls            #0xbedb04
    // 0xbedaa4: LoadField: r0 = r1->field_b
    //     0xbedaa4: ldur            w0, [x1, #0xb]
    // 0xbedaa8: DecompressPointer r0
    //     0xbedaa8: add             x0, x0, HEAP, lsl #32
    // 0xbedaac: LoadField: r2 = r0->field_f
    //     0xbedaac: ldur            w2, [x0, #0xf]
    // 0xbedab0: DecompressPointer r2
    //     0xbedab0: add             x2, x2, HEAP, lsl #32
    // 0xbedab4: LoadField: r0 = r1->field_f
    //     0xbedab4: ldur            w0, [x1, #0xf]
    // 0xbedab8: DecompressPointer r0
    //     0xbedab8: add             x0, x0, HEAP, lsl #32
    // 0xbedabc: r1 = LoadInt32Instr(r0)
    //     0xbedabc: sbfx            x1, x0, #1, #0x1f
    //     0xbedac0: tbz             w0, #0, #0xbedac8
    //     0xbedac4: ldur            x1, [x0, #7]
    // 0xbedac8: ArrayStore: r2[0] = r1  ; List_8
    //     0xbedac8: stur            x1, [x2, #0x17]
    // 0xbedacc: LoadField: r0 = r2->field_1f
    //     0xbedacc: ldur            w0, [x2, #0x1f]
    // 0xbedad0: DecompressPointer r0
    //     0xbedad0: add             x0, x0, HEAP, lsl #32
    // 0xbedad4: stur            x0, [fp, #-8]
    // 0xbedad8: r1 = Function '<anonymous closure>':.
    //     0xbedad8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53620] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xbedadc: ldr             x1, [x1, #0x620]
    // 0xbedae0: r2 = Null
    //     0xbedae0: mov             x2, NULL
    // 0xbedae4: r0 = AllocateClosure()
    //     0xbedae4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbedae8: ldur            x1, [fp, #-8]
    // 0xbedaec: mov             x2, x0
    // 0xbedaf0: r0 = forEach()
    //     0xbedaf0: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xbedaf4: r0 = Null
    //     0xbedaf4: mov             x0, NULL
    // 0xbedaf8: LeaveFrame
    //     0xbedaf8: mov             SP, fp
    //     0xbedafc: ldp             fp, lr, [SP], #0x10
    // 0xbedb00: ret
    //     0xbedb00: ret             
    // 0xbedb04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbedb04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbedb08: b               #0xbedaa4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbedb0c, size: 0xa4
    // 0xbedb0c: EnterFrame
    //     0xbedb0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbedb10: mov             fp, SP
    // 0xbedb14: AllocStack(0x28)
    //     0xbedb14: sub             SP, SP, #0x28
    // 0xbedb18: SetupParameters()
    //     0xbedb18: ldr             x0, [fp, #0x10]
    //     0xbedb1c: ldur            w1, [x0, #0x17]
    //     0xbedb20: add             x1, x1, HEAP, lsl #32
    // 0xbedb24: CheckStackOverflow
    //     0xbedb24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbedb28: cmp             SP, x16
    //     0xbedb2c: b.ls            #0xbedba4
    // 0xbedb30: LoadField: r0 = r1->field_f
    //     0xbedb30: ldur            w0, [x1, #0xf]
    // 0xbedb34: DecompressPointer r0
    //     0xbedb34: add             x0, x0, HEAP, lsl #32
    // 0xbedb38: LoadField: r1 = r0->field_b
    //     0xbedb38: ldur            w1, [x0, #0xb]
    // 0xbedb3c: DecompressPointer r1
    //     0xbedb3c: add             x1, x1, HEAP, lsl #32
    // 0xbedb40: cmp             w1, NULL
    // 0xbedb44: b.eq            #0xbedbac
    // 0xbedb48: LoadField: r0 = r1->field_1b
    //     0xbedb48: ldur            w0, [x1, #0x1b]
    // 0xbedb4c: DecompressPointer r0
    //     0xbedb4c: add             x0, x0, HEAP, lsl #32
    // 0xbedb50: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbedb50: ldur            w2, [x1, #0x17]
    // 0xbedb54: DecompressPointer r2
    //     0xbedb54: add             x2, x2, HEAP, lsl #32
    // 0xbedb58: LoadField: r3 = r1->field_23
    //     0xbedb58: ldur            w3, [x1, #0x23]
    // 0xbedb5c: DecompressPointer r3
    //     0xbedb5c: add             x3, x3, HEAP, lsl #32
    // 0xbedb60: LoadField: r4 = r1->field_1f
    //     0xbedb60: ldur            w4, [x1, #0x1f]
    // 0xbedb64: DecompressPointer r4
    //     0xbedb64: add             x4, x4, HEAP, lsl #32
    // 0xbedb68: LoadField: r5 = r1->field_27
    //     0xbedb68: ldur            w5, [x1, #0x27]
    // 0xbedb6c: DecompressPointer r5
    //     0xbedb6c: add             x5, x5, HEAP, lsl #32
    // 0xbedb70: stp             x0, x5, [SP, #0x18]
    // 0xbedb74: stp             x3, x2, [SP, #8]
    // 0xbedb78: str             x4, [SP]
    // 0xbedb7c: r4 = 0
    //     0xbedb7c: movz            x4, #0
    // 0xbedb80: ldr             x0, [SP, #0x20]
    // 0xbedb84: r16 = UnlinkedCall_0x613b5c
    //     0xbedb84: add             x16, PP, #0x53, lsl #12  ; [pp+0x53628] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbedb88: add             x16, x16, #0x628
    // 0xbedb8c: ldp             x5, lr, [x16]
    // 0xbedb90: blr             lr
    // 0xbedb94: r0 = Null
    //     0xbedb94: mov             x0, NULL
    // 0xbedb98: LeaveFrame
    //     0xbedb98: mov             SP, fp
    //     0xbedb9c: ldp             fp, lr, [SP], #0x10
    // 0xbedba0: ret
    //     0xbedba0: ret             
    // 0xbedba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbedba4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbedba8: b               #0xbedb30
    // 0xbedbac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbedbac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8821c, size: 0x54
    // 0xc8821c: EnterFrame
    //     0xc8821c: stp             fp, lr, [SP, #-0x10]!
    //     0xc88220: mov             fp, SP
    // 0xc88224: CheckStackOverflow
    //     0xc88224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88228: cmp             SP, x16
    //     0xc8822c: b.ls            #0xc8825c
    // 0xc88230: LoadField: r0 = r1->field_13
    //     0xc88230: ldur            w0, [x1, #0x13]
    // 0xc88234: DecompressPointer r0
    //     0xc88234: add             x0, x0, HEAP, lsl #32
    // 0xc88238: r16 = Sentinel
    //     0xc88238: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc8823c: cmp             w0, w16
    // 0xc88240: b.eq            #0xc88264
    // 0xc88244: mov             x1, x0
    // 0xc88248: r0 = dispose()
    //     0xc88248: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc8824c: r0 = Null
    //     0xc8824c: mov             x0, NULL
    // 0xc88250: LeaveFrame
    //     0xc88250: mov             SP, fp
    //     0xc88254: ldp             fp, lr, [SP], #0x10
    // 0xc88258: ret
    //     0xc88258: ret             
    // 0xc8825c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8825c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88260: b               #0xc88230
    // 0xc88264: r9 = _pageController
    //     0xc88264: add             x9, PP, #0x53, lsl #12  ; [pp+0x535d0] Field <_ProductTestimonialCarouselState@1707128090._pageController@1707128090>: late (offset: 0x14)
    //     0xc88268: ldr             x9, [x9, #0x5d0]
    // 0xc8826c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc8826c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3990, size: 0x30, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80b10, size: 0x84
    // 0xc80b10: EnterFrame
    //     0xc80b10: stp             fp, lr, [SP, #-0x10]!
    //     0xc80b14: mov             fp, SP
    // 0xc80b18: AllocStack(0x18)
    //     0xc80b18: sub             SP, SP, #0x18
    // 0xc80b1c: CheckStackOverflow
    //     0xc80b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80b20: cmp             SP, x16
    //     0xc80b24: b.ls            #0xc80b8c
    // 0xc80b28: r1 = <ProductTestimonialCarousel>
    //     0xc80b28: add             x1, PP, #0x48, lsl #12  ; [pp+0x48408] TypeArguments: <ProductTestimonialCarousel>
    //     0xc80b2c: ldr             x1, [x1, #0x408]
    // 0xc80b30: r0 = _ProductTestimonialCarouselState()
    //     0xc80b30: bl              #0xc80b94  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc80b34: mov             x1, x0
    // 0xc80b38: r0 = Sentinel
    //     0xc80b38: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80b3c: stur            x1, [fp, #-8]
    // 0xc80b40: StoreField: r1->field_13 = r0
    //     0xc80b40: stur            w0, [x1, #0x13]
    // 0xc80b44: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc80b44: stur            xzr, [x1, #0x17]
    // 0xc80b48: r16 = <int, RxBool>
    //     0xc80b48: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc80b4c: ldr             x16, [x16, #0x298]
    // 0xc80b50: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc80b54: stp             lr, x16, [SP]
    // 0xc80b58: r0 = Map._fromLiteral()
    //     0xc80b58: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc80b5c: ldur            x1, [fp, #-8]
    // 0xc80b60: StoreField: r1->field_1f = r0
    //     0xc80b60: stur            w0, [x1, #0x1f]
    //     0xc80b64: ldurb           w16, [x1, #-1]
    //     0xc80b68: ldurb           w17, [x0, #-1]
    //     0xc80b6c: and             x16, x17, x16, lsr #2
    //     0xc80b70: tst             x16, HEAP, lsr #32
    //     0xc80b74: b.eq            #0xc80b7c
    //     0xc80b78: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc80b7c: mov             x0, x1
    // 0xc80b80: LeaveFrame
    //     0xc80b80: mov             SP, fp
    //     0xc80b84: ldp             fp, lr, [SP], #0x10
    // 0xc80b88: ret
    //     0xc80b88: ret             
    // 0xc80b8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80b8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80b90: b               #0xc80b28
  }
}
