// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart

// class id: 1049284, size: 0x8
class :: {
}

// class id: 3421, size: 0x30, field offset: 0x14
class _SelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x93a61c, size: 0x574
    // 0x93a61c: EnterFrame
    //     0x93a61c: stp             fp, lr, [SP, #-0x10]!
    //     0x93a620: mov             fp, SP
    // 0x93a624: AllocStack(0x38)
    //     0x93a624: sub             SP, SP, #0x38
    // 0x93a628: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x93a628: stur            x1, [fp, #-8]
    // 0x93a62c: CheckStackOverflow
    //     0x93a62c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a630: cmp             SP, x16
    //     0x93a634: b.ls            #0x93ab60
    // 0x93a638: r1 = 1
    //     0x93a638: movz            x1, #0x1
    // 0x93a63c: r0 = AllocateContext()
    //     0x93a63c: bl              #0x16f6108  ; AllocateContextStub
    // 0x93a640: mov             x2, x0
    // 0x93a644: ldur            x1, [fp, #-8]
    // 0x93a648: stur            x2, [fp, #-0x10]
    // 0x93a64c: StoreField: r2->field_f = r1
    //     0x93a64c: stur            w1, [x2, #0xf]
    // 0x93a650: LoadField: r0 = r1->field_b
    //     0x93a650: ldur            w0, [x1, #0xb]
    // 0x93a654: DecompressPointer r0
    //     0x93a654: add             x0, x0, HEAP, lsl #32
    // 0x93a658: cmp             w0, NULL
    // 0x93a65c: b.eq            #0x93ab68
    // 0x93a660: LoadField: r3 = r0->field_f
    //     0x93a660: ldur            w3, [x0, #0xf]
    // 0x93a664: DecompressPointer r3
    //     0x93a664: add             x3, x3, HEAP, lsl #32
    // 0x93a668: r0 = LoadClassIdInstr(r3)
    //     0x93a668: ldur            x0, [x3, #-1]
    //     0x93a66c: ubfx            x0, x0, #0xc, #0x14
    // 0x93a670: r16 = "home_page"
    //     0x93a670: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x93a674: ldr             x16, [x16, #0xe60]
    // 0x93a678: stp             x16, x3, [SP]
    // 0x93a67c: mov             lr, x0
    // 0x93a680: ldr             lr, [x21, lr, lsl #3]
    // 0x93a684: blr             lr
    // 0x93a688: tbnz            w0, #4, #0x93a6a0
    // 0x93a68c: ldur            x0, [fp, #-8]
    // 0x93a690: r1 = "product_card"
    //     0x93a690: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0x93a694: ldr             x1, [x1, #0xc28]
    // 0x93a698: StoreField: r0->field_2b = r1
    //     0x93a698: stur            w1, [x0, #0x2b]
    // 0x93a69c: b               #0x93a6a4
    // 0x93a6a0: ldur            x0, [fp, #-8]
    // 0x93a6a4: LoadField: r1 = r0->field_b
    //     0x93a6a4: ldur            w1, [x0, #0xb]
    // 0x93a6a8: DecompressPointer r1
    //     0x93a6a8: add             x1, x1, HEAP, lsl #32
    // 0x93a6ac: cmp             w1, NULL
    // 0x93a6b0: b.eq            #0x93ab6c
    // 0x93a6b4: LoadField: r2 = r1->field_b
    //     0x93a6b4: ldur            w2, [x1, #0xb]
    // 0x93a6b8: DecompressPointer r2
    //     0x93a6b8: add             x2, x2, HEAP, lsl #32
    // 0x93a6bc: LoadField: r1 = r2->field_73
    //     0x93a6bc: ldur            w1, [x2, #0x73]
    // 0x93a6c0: DecompressPointer r1
    //     0x93a6c0: add             x1, x1, HEAP, lsl #32
    // 0x93a6c4: cmp             w1, NULL
    // 0x93a6c8: b.ne            #0x93a6d4
    // 0x93a6cc: r3 = Null
    //     0x93a6cc: mov             x3, NULL
    // 0x93a6d0: b               #0x93a6ec
    // 0x93a6d4: LoadField: r3 = r1->field_b
    //     0x93a6d4: ldur            w3, [x1, #0xb]
    // 0x93a6d8: cbnz            w3, #0x93a6e4
    // 0x93a6dc: r4 = false
    //     0x93a6dc: add             x4, NULL, #0x30  ; false
    // 0x93a6e0: b               #0x93a6e8
    // 0x93a6e4: r4 = true
    //     0x93a6e4: add             x4, NULL, #0x20  ; true
    // 0x93a6e8: mov             x3, x4
    // 0x93a6ec: cmp             w3, NULL
    // 0x93a6f0: b.ne            #0x93a6fc
    // 0x93a6f4: mov             x1, x0
    // 0x93a6f8: b               #0x93a9bc
    // 0x93a6fc: tbnz            w3, #4, #0x93a9b8
    // 0x93a700: cmp             w1, NULL
    // 0x93a704: b.ne            #0x93a710
    // 0x93a708: r2 = Null
    //     0x93a708: mov             x2, NULL
    // 0x93a70c: b               #0x93a768
    // 0x93a710: r16 = <AllGroupedSkusDatum?>
    //     0x93a710: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c98] TypeArguments: <AllGroupedSkusDatum?>
    //     0x93a714: ldr             x16, [x16, #0xc98]
    // 0x93a718: stp             x1, x16, [SP]
    // 0x93a71c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x93a71c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x93a720: r0 = cast()
    //     0x93a720: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x93a724: ldur            x2, [fp, #-0x10]
    // 0x93a728: r1 = Function '<anonymous closure>':.
    //     0x93a728: add             x1, PP, #0x58, lsl #12  ; [pp+0x586d0] AnonymousClosure: (0x9084a8), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x93a72c: ldr             x1, [x1, #0x6d0]
    // 0x93a730: stur            x0, [fp, #-0x18]
    // 0x93a734: r0 = AllocateClosure()
    //     0x93a734: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93a738: r1 = Function '<anonymous closure>':.
    //     0x93a738: add             x1, PP, #0x58, lsl #12  ; [pp+0x586d8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x93a73c: ldr             x1, [x1, #0x6d8]
    // 0x93a740: r2 = Null
    //     0x93a740: mov             x2, NULL
    // 0x93a744: stur            x0, [fp, #-0x20]
    // 0x93a748: r0 = AllocateClosure()
    //     0x93a748: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93a74c: str             x0, [SP]
    // 0x93a750: ldur            x1, [fp, #-0x18]
    // 0x93a754: ldur            x2, [fp, #-0x20]
    // 0x93a758: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x93a758: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x93a75c: ldr             x4, [x4, #0xb48]
    // 0x93a760: r0 = firstWhere()
    //     0x93a760: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x93a764: mov             x2, x0
    // 0x93a768: cmp             w2, NULL
    // 0x93a76c: b.eq            #0x93a7f4
    // 0x93a770: ldur            x0, [fp, #-8]
    // 0x93a774: LoadField: r1 = r0->field_b
    //     0x93a774: ldur            w1, [x0, #0xb]
    // 0x93a778: DecompressPointer r1
    //     0x93a778: add             x1, x1, HEAP, lsl #32
    // 0x93a77c: cmp             w1, NULL
    // 0x93a780: b.eq            #0x93ab70
    // 0x93a784: LoadField: r3 = r1->field_b
    //     0x93a784: ldur            w3, [x1, #0xb]
    // 0x93a788: DecompressPointer r3
    //     0x93a788: add             x3, x3, HEAP, lsl #32
    // 0x93a78c: LoadField: r1 = r3->field_73
    //     0x93a78c: ldur            w1, [x3, #0x73]
    // 0x93a790: DecompressPointer r1
    //     0x93a790: add             x1, x1, HEAP, lsl #32
    // 0x93a794: cmp             w1, NULL
    // 0x93a798: b.ne            #0x93a7a4
    // 0x93a79c: r0 = Null
    //     0x93a79c: mov             x0, NULL
    // 0x93a7a0: b               #0x93a7c4
    // 0x93a7a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93a7a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93a7a8: r0 = indexOf()
    //     0x93a7a8: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x93a7ac: mov             x2, x0
    // 0x93a7b0: r0 = BoxInt64Instr(r2)
    //     0x93a7b0: sbfiz           x0, x2, #1, #0x1f
    //     0x93a7b4: cmp             x2, x0, asr #1
    //     0x93a7b8: b.eq            #0x93a7c4
    //     0x93a7bc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93a7c0: stur            x2, [x0, #7]
    // 0x93a7c4: cmp             w0, NULL
    // 0x93a7c8: b.ne            #0x93a7d4
    // 0x93a7cc: r0 = 0
    //     0x93a7cc: movz            x0, #0
    // 0x93a7d0: b               #0x93a7e4
    // 0x93a7d4: r1 = LoadInt32Instr(r0)
    //     0x93a7d4: sbfx            x1, x0, #1, #0x1f
    //     0x93a7d8: tbz             w0, #0, #0x93a7e0
    //     0x93a7dc: ldur            x1, [x0, #7]
    // 0x93a7e0: mov             x0, x1
    // 0x93a7e4: ldur            x2, [fp, #-8]
    // 0x93a7e8: ArrayStore: r2[0] = r0  ; List_8
    //     0x93a7e8: stur            x0, [x2, #0x17]
    // 0x93a7ec: mov             x3, x0
    // 0x93a7f0: b               #0x93a800
    // 0x93a7f4: ldur            x2, [fp, #-8]
    // 0x93a7f8: ArrayStore: r2[0] = rZR  ; List_8
    //     0x93a7f8: stur            xzr, [x2, #0x17]
    // 0x93a7fc: r3 = 0
    //     0x93a7fc: movz            x3, #0
    // 0x93a800: stur            x3, [fp, #-0x28]
    // 0x93a804: LoadField: r0 = r2->field_b
    //     0x93a804: ldur            w0, [x2, #0xb]
    // 0x93a808: DecompressPointer r0
    //     0x93a808: add             x0, x0, HEAP, lsl #32
    // 0x93a80c: cmp             w0, NULL
    // 0x93a810: b.eq            #0x93ab74
    // 0x93a814: LoadField: r1 = r0->field_b
    //     0x93a814: ldur            w1, [x0, #0xb]
    // 0x93a818: DecompressPointer r1
    //     0x93a818: add             x1, x1, HEAP, lsl #32
    // 0x93a81c: LoadField: r4 = r1->field_73
    //     0x93a81c: ldur            w4, [x1, #0x73]
    // 0x93a820: DecompressPointer r4
    //     0x93a820: add             x4, x4, HEAP, lsl #32
    // 0x93a824: stur            x4, [fp, #-0x18]
    // 0x93a828: cmp             w4, NULL
    // 0x93a82c: b.ne            #0x93a838
    // 0x93a830: r0 = Null
    //     0x93a830: mov             x0, NULL
    // 0x93a834: b               #0x93a8ac
    // 0x93a838: LoadField: r0 = r4->field_b
    //     0x93a838: ldur            w0, [x4, #0xb]
    // 0x93a83c: r1 = LoadInt32Instr(r0)
    //     0x93a83c: sbfx            x1, x0, #1, #0x1f
    // 0x93a840: mov             x0, x1
    // 0x93a844: mov             x1, x3
    // 0x93a848: cmp             x1, x0
    // 0x93a84c: b.hs            #0x93ab78
    // 0x93a850: LoadField: r0 = r4->field_f
    //     0x93a850: ldur            w0, [x4, #0xf]
    // 0x93a854: DecompressPointer r0
    //     0x93a854: add             x0, x0, HEAP, lsl #32
    // 0x93a858: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93a858: add             x16, x0, x3, lsl #2
    //     0x93a85c: ldur            w1, [x16, #0xf]
    // 0x93a860: DecompressPointer r1
    //     0x93a860: add             x1, x1, HEAP, lsl #32
    // 0x93a864: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x93a864: ldur            w5, [x1, #0x17]
    // 0x93a868: DecompressPointer r5
    //     0x93a868: add             x5, x5, HEAP, lsl #32
    // 0x93a86c: cmp             w5, NULL
    // 0x93a870: b.ne            #0x93a87c
    // 0x93a874: r0 = Null
    //     0x93a874: mov             x0, NULL
    // 0x93a878: b               #0x93a8ac
    // 0x93a87c: LoadField: r0 = r5->field_b
    //     0x93a87c: ldur            w0, [x5, #0xb]
    // 0x93a880: r1 = LoadInt32Instr(r0)
    //     0x93a880: sbfx            x1, x0, #1, #0x1f
    // 0x93a884: mov             x0, x1
    // 0x93a888: mov             x1, x3
    // 0x93a88c: cmp             x1, x0
    // 0x93a890: b.hs            #0x93ab7c
    // 0x93a894: LoadField: r0 = r5->field_f
    //     0x93a894: ldur            w0, [x5, #0xf]
    // 0x93a898: DecompressPointer r0
    //     0x93a898: add             x0, x0, HEAP, lsl #32
    // 0x93a89c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93a89c: add             x16, x0, x3, lsl #2
    //     0x93a8a0: ldur            w1, [x16, #0xf]
    // 0x93a8a4: DecompressPointer r1
    //     0x93a8a4: add             x1, x1, HEAP, lsl #32
    // 0x93a8a8: mov             x0, x1
    // 0x93a8ac: cmp             w0, NULL
    // 0x93a8b0: b.ne            #0x93a8b8
    // 0x93a8b4: r0 = AllSkuDatum()
    //     0x93a8b4: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x93a8b8: ldur            x1, [fp, #-8]
    // 0x93a8bc: ldur            x2, [fp, #-0x18]
    // 0x93a8c0: StoreField: r1->field_13 = r0
    //     0x93a8c0: stur            w0, [x1, #0x13]
    //     0x93a8c4: ldurb           w16, [x1, #-1]
    //     0x93a8c8: ldurb           w17, [x0, #-1]
    //     0x93a8cc: and             x16, x17, x16, lsr #2
    //     0x93a8d0: tst             x16, HEAP, lsr #32
    //     0x93a8d4: b.eq            #0x93a8dc
    //     0x93a8d8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93a8dc: LoadField: r3 = r1->field_27
    //     0x93a8dc: ldur            w3, [x1, #0x27]
    // 0x93a8e0: DecompressPointer r3
    //     0x93a8e0: add             x3, x3, HEAP, lsl #32
    // 0x93a8e4: stur            x3, [fp, #-0x20]
    // 0x93a8e8: cmp             w2, NULL
    // 0x93a8ec: b.ne            #0x93a8f8
    // 0x93a8f0: r0 = Null
    //     0x93a8f0: mov             x0, NULL
    // 0x93a8f4: b               #0x93a930
    // 0x93a8f8: ldur            x4, [fp, #-0x28]
    // 0x93a8fc: LoadField: r0 = r2->field_b
    //     0x93a8fc: ldur            w0, [x2, #0xb]
    // 0x93a900: r1 = LoadInt32Instr(r0)
    //     0x93a900: sbfx            x1, x0, #1, #0x1f
    // 0x93a904: mov             x0, x1
    // 0x93a908: mov             x1, x4
    // 0x93a90c: cmp             x1, x0
    // 0x93a910: b.hs            #0x93ab80
    // 0x93a914: LoadField: r0 = r2->field_f
    //     0x93a914: ldur            w0, [x2, #0xf]
    // 0x93a918: DecompressPointer r0
    //     0x93a918: add             x0, x0, HEAP, lsl #32
    // 0x93a91c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x93a91c: add             x16, x0, x4, lsl #2
    //     0x93a920: ldur            w1, [x16, #0xf]
    // 0x93a924: DecompressPointer r1
    //     0x93a924: add             x1, x1, HEAP, lsl #32
    // 0x93a928: LoadField: r0 = r1->field_7
    //     0x93a928: ldur            w0, [x1, #7]
    // 0x93a92c: DecompressPointer r0
    //     0x93a92c: add             x0, x0, HEAP, lsl #32
    // 0x93a930: cmp             w0, NULL
    // 0x93a934: b.ne            #0x93a93c
    // 0x93a938: r0 = ""
    //     0x93a938: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93a93c: stur            x0, [fp, #-0x18]
    // 0x93a940: LoadField: r1 = r3->field_b
    //     0x93a940: ldur            w1, [x3, #0xb]
    // 0x93a944: LoadField: r2 = r3->field_f
    //     0x93a944: ldur            w2, [x3, #0xf]
    // 0x93a948: DecompressPointer r2
    //     0x93a948: add             x2, x2, HEAP, lsl #32
    // 0x93a94c: LoadField: r4 = r2->field_b
    //     0x93a94c: ldur            w4, [x2, #0xb]
    // 0x93a950: r2 = LoadInt32Instr(r1)
    //     0x93a950: sbfx            x2, x1, #1, #0x1f
    // 0x93a954: stur            x2, [fp, #-0x28]
    // 0x93a958: r1 = LoadInt32Instr(r4)
    //     0x93a958: sbfx            x1, x4, #1, #0x1f
    // 0x93a95c: cmp             x2, x1
    // 0x93a960: b.ne            #0x93a96c
    // 0x93a964: mov             x1, x3
    // 0x93a968: r0 = _growToNextCapacity()
    //     0x93a968: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93a96c: ldur            x0, [fp, #-0x20]
    // 0x93a970: ldur            x2, [fp, #-0x28]
    // 0x93a974: add             x1, x2, #1
    // 0x93a978: lsl             x3, x1, #1
    // 0x93a97c: StoreField: r0->field_b = r3
    //     0x93a97c: stur            w3, [x0, #0xb]
    // 0x93a980: LoadField: r1 = r0->field_f
    //     0x93a980: ldur            w1, [x0, #0xf]
    // 0x93a984: DecompressPointer r1
    //     0x93a984: add             x1, x1, HEAP, lsl #32
    // 0x93a988: ldur            x0, [fp, #-0x18]
    // 0x93a98c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93a98c: add             x25, x1, x2, lsl #2
    //     0x93a990: add             x25, x25, #0xf
    //     0x93a994: str             w0, [x25]
    //     0x93a998: tbz             w0, #0, #0x93a9b4
    //     0x93a99c: ldurb           w16, [x1, #-1]
    //     0x93a9a0: ldurb           w17, [x0, #-1]
    //     0x93a9a4: and             x16, x17, x16, lsr #2
    //     0x93a9a8: tst             x16, HEAP, lsr #32
    //     0x93a9ac: b.eq            #0x93a9b4
    //     0x93a9b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93a9b4: b               #0x93ab50
    // 0x93a9b8: mov             x1, x0
    // 0x93a9bc: LoadField: r0 = r2->field_6f
    //     0x93a9bc: ldur            w0, [x2, #0x6f]
    // 0x93a9c0: DecompressPointer r0
    //     0x93a9c0: add             x0, x0, HEAP, lsl #32
    // 0x93a9c4: cmp             w0, NULL
    // 0x93a9c8: b.ne            #0x93a9d4
    // 0x93a9cc: r2 = Null
    //     0x93a9cc: mov             x2, NULL
    // 0x93a9d0: b               #0x93aa2c
    // 0x93a9d4: r16 = <AllSkuDatum?>
    //     0x93a9d4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x93a9d8: ldr             x16, [x16, #0x868]
    // 0x93a9dc: stp             x0, x16, [SP]
    // 0x93a9e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x93a9e0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x93a9e4: r0 = cast()
    //     0x93a9e4: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x93a9e8: ldur            x2, [fp, #-0x10]
    // 0x93a9ec: r1 = Function '<anonymous closure>':.
    //     0x93a9ec: add             x1, PP, #0x58, lsl #12  ; [pp+0x586e0] AnonymousClosure: (0x9083cc), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x93a9f0: ldr             x1, [x1, #0x6e0]
    // 0x93a9f4: stur            x0, [fp, #-0x10]
    // 0x93a9f8: r0 = AllocateClosure()
    //     0x93a9f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93a9fc: r1 = Function '<anonymous closure>':.
    //     0x93a9fc: add             x1, PP, #0x58, lsl #12  ; [pp+0x586e8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x93aa00: ldr             x1, [x1, #0x6e8]
    // 0x93aa04: r2 = Null
    //     0x93aa04: mov             x2, NULL
    // 0x93aa08: stur            x0, [fp, #-0x18]
    // 0x93aa0c: r0 = AllocateClosure()
    //     0x93aa0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93aa10: str             x0, [SP]
    // 0x93aa14: ldur            x1, [fp, #-0x10]
    // 0x93aa18: ldur            x2, [fp, #-0x18]
    // 0x93aa1c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x93aa1c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x93aa20: ldr             x4, [x4, #0xb48]
    // 0x93aa24: r0 = firstWhere()
    //     0x93aa24: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x93aa28: mov             x2, x0
    // 0x93aa2c: cmp             w2, NULL
    // 0x93aa30: b.eq            #0x93aab8
    // 0x93aa34: ldur            x0, [fp, #-8]
    // 0x93aa38: LoadField: r1 = r0->field_b
    //     0x93aa38: ldur            w1, [x0, #0xb]
    // 0x93aa3c: DecompressPointer r1
    //     0x93aa3c: add             x1, x1, HEAP, lsl #32
    // 0x93aa40: cmp             w1, NULL
    // 0x93aa44: b.eq            #0x93ab84
    // 0x93aa48: LoadField: r3 = r1->field_b
    //     0x93aa48: ldur            w3, [x1, #0xb]
    // 0x93aa4c: DecompressPointer r3
    //     0x93aa4c: add             x3, x3, HEAP, lsl #32
    // 0x93aa50: LoadField: r1 = r3->field_6f
    //     0x93aa50: ldur            w1, [x3, #0x6f]
    // 0x93aa54: DecompressPointer r1
    //     0x93aa54: add             x1, x1, HEAP, lsl #32
    // 0x93aa58: cmp             w1, NULL
    // 0x93aa5c: b.ne            #0x93aa68
    // 0x93aa60: r0 = Null
    //     0x93aa60: mov             x0, NULL
    // 0x93aa64: b               #0x93aa88
    // 0x93aa68: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93aa68: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93aa6c: r0 = indexOf()
    //     0x93aa6c: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x93aa70: mov             x2, x0
    // 0x93aa74: r0 = BoxInt64Instr(r2)
    //     0x93aa74: sbfiz           x0, x2, #1, #0x1f
    //     0x93aa78: cmp             x2, x0, asr #1
    //     0x93aa7c: b.eq            #0x93aa88
    //     0x93aa80: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93aa84: stur            x2, [x0, #7]
    // 0x93aa88: cmp             w0, NULL
    // 0x93aa8c: b.ne            #0x93aa98
    // 0x93aa90: r0 = 0
    //     0x93aa90: movz            x0, #0
    // 0x93aa94: b               #0x93aaa8
    // 0x93aa98: r1 = LoadInt32Instr(r0)
    //     0x93aa98: sbfx            x1, x0, #1, #0x1f
    //     0x93aa9c: tbz             w0, #0, #0x93aaa4
    //     0x93aaa0: ldur            x1, [x0, #7]
    // 0x93aaa4: mov             x0, x1
    // 0x93aaa8: ldur            x2, [fp, #-8]
    // 0x93aaac: ArrayStore: r2[0] = r0  ; List_8
    //     0x93aaac: stur            x0, [x2, #0x17]
    // 0x93aab0: mov             x3, x0
    // 0x93aab4: b               #0x93aac4
    // 0x93aab8: ldur            x2, [fp, #-8]
    // 0x93aabc: ArrayStore: r2[0] = rZR  ; List_8
    //     0x93aabc: stur            xzr, [x2, #0x17]
    // 0x93aac0: r3 = 0
    //     0x93aac0: movz            x3, #0
    // 0x93aac4: LoadField: r0 = r2->field_b
    //     0x93aac4: ldur            w0, [x2, #0xb]
    // 0x93aac8: DecompressPointer r0
    //     0x93aac8: add             x0, x0, HEAP, lsl #32
    // 0x93aacc: cmp             w0, NULL
    // 0x93aad0: b.eq            #0x93ab88
    // 0x93aad4: LoadField: r1 = r0->field_b
    //     0x93aad4: ldur            w1, [x0, #0xb]
    // 0x93aad8: DecompressPointer r1
    //     0x93aad8: add             x1, x1, HEAP, lsl #32
    // 0x93aadc: LoadField: r4 = r1->field_6f
    //     0x93aadc: ldur            w4, [x1, #0x6f]
    // 0x93aae0: DecompressPointer r4
    //     0x93aae0: add             x4, x4, HEAP, lsl #32
    // 0x93aae4: cmp             w4, NULL
    // 0x93aae8: b.ne            #0x93aaf4
    // 0x93aaec: r0 = Null
    //     0x93aaec: mov             x0, NULL
    // 0x93aaf0: b               #0x93ab24
    // 0x93aaf4: LoadField: r0 = r4->field_b
    //     0x93aaf4: ldur            w0, [x4, #0xb]
    // 0x93aaf8: r1 = LoadInt32Instr(r0)
    //     0x93aaf8: sbfx            x1, x0, #1, #0x1f
    // 0x93aafc: mov             x0, x1
    // 0x93ab00: mov             x1, x3
    // 0x93ab04: cmp             x1, x0
    // 0x93ab08: b.hs            #0x93ab8c
    // 0x93ab0c: LoadField: r0 = r4->field_f
    //     0x93ab0c: ldur            w0, [x4, #0xf]
    // 0x93ab10: DecompressPointer r0
    //     0x93ab10: add             x0, x0, HEAP, lsl #32
    // 0x93ab14: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x93ab14: add             x16, x0, x3, lsl #2
    //     0x93ab18: ldur            w1, [x16, #0xf]
    // 0x93ab1c: DecompressPointer r1
    //     0x93ab1c: add             x1, x1, HEAP, lsl #32
    // 0x93ab20: mov             x0, x1
    // 0x93ab24: cmp             w0, NULL
    // 0x93ab28: b.ne            #0x93ab30
    // 0x93ab2c: r0 = AllSkuDatum()
    //     0x93ab2c: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x93ab30: ldur            x1, [fp, #-8]
    // 0x93ab34: StoreField: r1->field_13 = r0
    //     0x93ab34: stur            w0, [x1, #0x13]
    //     0x93ab38: ldurb           w16, [x1, #-1]
    //     0x93ab3c: ldurb           w17, [x0, #-1]
    //     0x93ab40: and             x16, x17, x16, lsr #2
    //     0x93ab44: tst             x16, HEAP, lsr #32
    //     0x93ab48: b.eq            #0x93ab50
    //     0x93ab4c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93ab50: r0 = Null
    //     0x93ab50: mov             x0, NULL
    // 0x93ab54: LeaveFrame
    //     0x93ab54: mov             SP, fp
    //     0x93ab58: ldp             fp, lr, [SP], #0x10
    // 0x93ab5c: ret
    //     0x93ab5c: ret             
    // 0x93ab60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ab60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ab64: b               #0x93a638
    // 0x93ab68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93ab78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93ab7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93ab7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93ab80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93ab80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93ab84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93ab88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93ab8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93ab8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xaf0c5c, size: 0xbd4
    // 0xaf0c5c: EnterFrame
    //     0xaf0c5c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf0c60: mov             fp, SP
    // 0xaf0c64: AllocStack(0x58)
    //     0xaf0c64: sub             SP, SP, #0x58
    // 0xaf0c68: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaf0c68: mov             x0, x1
    //     0xaf0c6c: stur            x1, [fp, #-8]
    //     0xaf0c70: mov             x1, x2
    //     0xaf0c74: stur            x2, [fp, #-0x10]
    // 0xaf0c78: CheckStackOverflow
    //     0xaf0c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf0c7c: cmp             SP, x16
    //     0xaf0c80: b.ls            #0xaf1814
    // 0xaf0c84: r1 = 2
    //     0xaf0c84: movz            x1, #0x2
    // 0xaf0c88: r0 = AllocateContext()
    //     0xaf0c88: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf0c8c: mov             x2, x0
    // 0xaf0c90: ldur            x0, [fp, #-8]
    // 0xaf0c94: stur            x2, [fp, #-0x18]
    // 0xaf0c98: StoreField: r2->field_f = r0
    //     0xaf0c98: stur            w0, [x2, #0xf]
    // 0xaf0c9c: ldur            x1, [fp, #-0x10]
    // 0xaf0ca0: StoreField: r2->field_13 = r1
    //     0xaf0ca0: stur            w1, [x2, #0x13]
    // 0xaf0ca4: r0 = of()
    //     0xaf0ca4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf0ca8: LoadField: r1 = r0->field_87
    //     0xaf0ca8: ldur            w1, [x0, #0x87]
    // 0xaf0cac: DecompressPointer r1
    //     0xaf0cac: add             x1, x1, HEAP, lsl #32
    // 0xaf0cb0: LoadField: r0 = r1->field_7
    //     0xaf0cb0: ldur            w0, [x1, #7]
    // 0xaf0cb4: DecompressPointer r0
    //     0xaf0cb4: add             x0, x0, HEAP, lsl #32
    // 0xaf0cb8: r16 = Instance_Color
    //     0xaf0cb8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf0cbc: r30 = 16.000000
    //     0xaf0cbc: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf0cc0: ldr             lr, [lr, #0x188]
    // 0xaf0cc4: stp             lr, x16, [SP]
    // 0xaf0cc8: mov             x1, x0
    // 0xaf0ccc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaf0ccc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaf0cd0: ldr             x4, [x4, #0x9b8]
    // 0xaf0cd4: r0 = copyWith()
    //     0xaf0cd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf0cd8: stur            x0, [fp, #-0x10]
    // 0xaf0cdc: r0 = Text()
    //     0xaf0cdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf0ce0: mov             x1, x0
    // 0xaf0ce4: r0 = "Select Preference"
    //     0xaf0ce4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xaf0ce8: ldr             x0, [x0, #0x710]
    // 0xaf0cec: stur            x1, [fp, #-0x20]
    // 0xaf0cf0: StoreField: r1->field_b = r0
    //     0xaf0cf0: stur            w0, [x1, #0xb]
    // 0xaf0cf4: ldur            x0, [fp, #-0x10]
    // 0xaf0cf8: StoreField: r1->field_13 = r0
    //     0xaf0cf8: stur            w0, [x1, #0x13]
    // 0xaf0cfc: r0 = InkWell()
    //     0xaf0cfc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaf0d00: mov             x3, x0
    // 0xaf0d04: r0 = Instance_Icon
    //     0xaf0d04: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xaf0d08: ldr             x0, [x0, #0x2b8]
    // 0xaf0d0c: stur            x3, [fp, #-0x10]
    // 0xaf0d10: StoreField: r3->field_b = r0
    //     0xaf0d10: stur            w0, [x3, #0xb]
    // 0xaf0d14: ldur            x2, [fp, #-0x18]
    // 0xaf0d18: r1 = Function '<anonymous closure>':.
    //     0xaf0d18: add             x1, PP, #0x58, lsl #12  ; [pp+0x58630] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xaf0d1c: ldr             x1, [x1, #0x630]
    // 0xaf0d20: r0 = AllocateClosure()
    //     0xaf0d20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0d24: mov             x1, x0
    // 0xaf0d28: ldur            x0, [fp, #-0x10]
    // 0xaf0d2c: StoreField: r0->field_f = r1
    //     0xaf0d2c: stur            w1, [x0, #0xf]
    // 0xaf0d30: r3 = true
    //     0xaf0d30: add             x3, NULL, #0x20  ; true
    // 0xaf0d34: StoreField: r0->field_43 = r3
    //     0xaf0d34: stur            w3, [x0, #0x43]
    // 0xaf0d38: r1 = Instance_BoxShape
    //     0xaf0d38: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf0d3c: ldr             x1, [x1, #0x80]
    // 0xaf0d40: StoreField: r0->field_47 = r1
    //     0xaf0d40: stur            w1, [x0, #0x47]
    // 0xaf0d44: StoreField: r0->field_6f = r3
    //     0xaf0d44: stur            w3, [x0, #0x6f]
    // 0xaf0d48: r4 = false
    //     0xaf0d48: add             x4, NULL, #0x30  ; false
    // 0xaf0d4c: StoreField: r0->field_73 = r4
    //     0xaf0d4c: stur            w4, [x0, #0x73]
    // 0xaf0d50: StoreField: r0->field_83 = r3
    //     0xaf0d50: stur            w3, [x0, #0x83]
    // 0xaf0d54: StoreField: r0->field_7b = r4
    //     0xaf0d54: stur            w4, [x0, #0x7b]
    // 0xaf0d58: r1 = Null
    //     0xaf0d58: mov             x1, NULL
    // 0xaf0d5c: r2 = 6
    //     0xaf0d5c: movz            x2, #0x6
    // 0xaf0d60: r0 = AllocateArray()
    //     0xaf0d60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf0d64: mov             x2, x0
    // 0xaf0d68: ldur            x0, [fp, #-0x20]
    // 0xaf0d6c: stur            x2, [fp, #-0x28]
    // 0xaf0d70: StoreField: r2->field_f = r0
    //     0xaf0d70: stur            w0, [x2, #0xf]
    // 0xaf0d74: r16 = Instance_Spacer
    //     0xaf0d74: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xaf0d78: ldr             x16, [x16, #0xf0]
    // 0xaf0d7c: StoreField: r2->field_13 = r16
    //     0xaf0d7c: stur            w16, [x2, #0x13]
    // 0xaf0d80: ldur            x0, [fp, #-0x10]
    // 0xaf0d84: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf0d84: stur            w0, [x2, #0x17]
    // 0xaf0d88: r1 = <Widget>
    //     0xaf0d88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf0d8c: r0 = AllocateGrowableArray()
    //     0xaf0d8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf0d90: mov             x1, x0
    // 0xaf0d94: ldur            x0, [fp, #-0x28]
    // 0xaf0d98: stur            x1, [fp, #-0x10]
    // 0xaf0d9c: StoreField: r1->field_f = r0
    //     0xaf0d9c: stur            w0, [x1, #0xf]
    // 0xaf0da0: r2 = 6
    //     0xaf0da0: movz            x2, #0x6
    // 0xaf0da4: StoreField: r1->field_b = r2
    //     0xaf0da4: stur            w2, [x1, #0xb]
    // 0xaf0da8: r0 = Row()
    //     0xaf0da8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf0dac: mov             x1, x0
    // 0xaf0db0: r0 = Instance_Axis
    //     0xaf0db0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf0db4: stur            x1, [fp, #-0x20]
    // 0xaf0db8: StoreField: r1->field_f = r0
    //     0xaf0db8: stur            w0, [x1, #0xf]
    // 0xaf0dbc: r0 = Instance_MainAxisAlignment
    //     0xaf0dbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf0dc0: ldr             x0, [x0, #0xa08]
    // 0xaf0dc4: StoreField: r1->field_13 = r0
    //     0xaf0dc4: stur            w0, [x1, #0x13]
    // 0xaf0dc8: r2 = Instance_MainAxisSize
    //     0xaf0dc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf0dcc: ldr             x2, [x2, #0xa10]
    // 0xaf0dd0: ArrayStore: r1[0] = r2  ; List_4
    //     0xaf0dd0: stur            w2, [x1, #0x17]
    // 0xaf0dd4: r2 = Instance_CrossAxisAlignment
    //     0xaf0dd4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf0dd8: ldr             x2, [x2, #0xa18]
    // 0xaf0ddc: StoreField: r1->field_1b = r2
    //     0xaf0ddc: stur            w2, [x1, #0x1b]
    // 0xaf0de0: r2 = Instance_VerticalDirection
    //     0xaf0de0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf0de4: ldr             x2, [x2, #0xa20]
    // 0xaf0de8: StoreField: r1->field_23 = r2
    //     0xaf0de8: stur            w2, [x1, #0x23]
    // 0xaf0dec: r3 = Instance_Clip
    //     0xaf0dec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf0df0: ldr             x3, [x3, #0x38]
    // 0xaf0df4: StoreField: r1->field_2b = r3
    //     0xaf0df4: stur            w3, [x1, #0x2b]
    // 0xaf0df8: StoreField: r1->field_2f = rZR
    //     0xaf0df8: stur            xzr, [x1, #0x2f]
    // 0xaf0dfc: ldur            x4, [fp, #-0x10]
    // 0xaf0e00: StoreField: r1->field_b = r4
    //     0xaf0e00: stur            w4, [x1, #0xb]
    // 0xaf0e04: r0 = Padding()
    //     0xaf0e04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf0e08: mov             x3, x0
    // 0xaf0e0c: r0 = Instance_EdgeInsets
    //     0xaf0e0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xaf0e10: ldr             x0, [x0, #0xd0]
    // 0xaf0e14: stur            x3, [fp, #-0x28]
    // 0xaf0e18: StoreField: r3->field_f = r0
    //     0xaf0e18: stur            w0, [x3, #0xf]
    // 0xaf0e1c: ldur            x0, [fp, #-0x20]
    // 0xaf0e20: StoreField: r3->field_b = r0
    //     0xaf0e20: stur            w0, [x3, #0xb]
    // 0xaf0e24: ldur            x0, [fp, #-8]
    // 0xaf0e28: LoadField: r1 = r0->field_b
    //     0xaf0e28: ldur            w1, [x0, #0xb]
    // 0xaf0e2c: DecompressPointer r1
    //     0xaf0e2c: add             x1, x1, HEAP, lsl #32
    // 0xaf0e30: cmp             w1, NULL
    // 0xaf0e34: b.eq            #0xaf181c
    // 0xaf0e38: LoadField: r2 = r1->field_b
    //     0xaf0e38: ldur            w2, [x1, #0xb]
    // 0xaf0e3c: DecompressPointer r2
    //     0xaf0e3c: add             x2, x2, HEAP, lsl #32
    // 0xaf0e40: LoadField: r1 = r2->field_73
    //     0xaf0e40: ldur            w1, [x2, #0x73]
    // 0xaf0e44: DecompressPointer r1
    //     0xaf0e44: add             x1, x1, HEAP, lsl #32
    // 0xaf0e48: cmp             w1, NULL
    // 0xaf0e4c: b.ne            #0xaf0e58
    // 0xaf0e50: r2 = Null
    //     0xaf0e50: mov             x2, NULL
    // 0xaf0e54: b               #0xaf0e70
    // 0xaf0e58: LoadField: r2 = r1->field_b
    //     0xaf0e58: ldur            w2, [x1, #0xb]
    // 0xaf0e5c: cbnz            w2, #0xaf0e68
    // 0xaf0e60: r4 = false
    //     0xaf0e60: add             x4, NULL, #0x30  ; false
    // 0xaf0e64: b               #0xaf0e6c
    // 0xaf0e68: r4 = true
    //     0xaf0e68: add             x4, NULL, #0x20  ; true
    // 0xaf0e6c: mov             x2, x4
    // 0xaf0e70: cmp             w2, NULL
    // 0xaf0e74: b.ne            #0xaf0e80
    // 0xaf0e78: r4 = false
    //     0xaf0e78: add             x4, NULL, #0x30  ; false
    // 0xaf0e7c: b               #0xaf0e84
    // 0xaf0e80: mov             x4, x2
    // 0xaf0e84: stur            x4, [fp, #-0x20]
    // 0xaf0e88: cmp             w1, NULL
    // 0xaf0e8c: b.ne            #0xaf0e98
    // 0xaf0e90: r5 = Null
    //     0xaf0e90: mov             x5, NULL
    // 0xaf0e94: b               #0xaf0ea0
    // 0xaf0e98: LoadField: r2 = r1->field_b
    //     0xaf0e98: ldur            w2, [x1, #0xb]
    // 0xaf0e9c: mov             x5, x2
    // 0xaf0ea0: ldur            x2, [fp, #-0x18]
    // 0xaf0ea4: stur            x5, [fp, #-0x10]
    // 0xaf0ea8: r1 = Function '<anonymous closure>':.
    //     0xaf0ea8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58638] AnonymousClosure: (0xaf3430), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf0eac: ldr             x1, [x1, #0x638]
    // 0xaf0eb0: r0 = AllocateClosure()
    //     0xaf0eb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf0eb4: stur            x0, [fp, #-0x30]
    // 0xaf0eb8: r0 = ListView()
    //     0xaf0eb8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xaf0ebc: stur            x0, [fp, #-0x38]
    // 0xaf0ec0: r16 = true
    //     0xaf0ec0: add             x16, NULL, #0x20  ; true
    // 0xaf0ec4: r30 = Instance_Axis
    //     0xaf0ec4: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf0ec8: stp             lr, x16, [SP]
    // 0xaf0ecc: mov             x1, x0
    // 0xaf0ed0: ldur            x2, [fp, #-0x30]
    // 0xaf0ed4: ldur            x3, [fp, #-0x10]
    // 0xaf0ed8: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xaf0ed8: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xaf0edc: ldr             x4, [x4, #0x2d0]
    // 0xaf0ee0: r0 = ListView.builder()
    //     0xaf0ee0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaf0ee4: r0 = SizedBox()
    //     0xaf0ee4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf0ee8: mov             x1, x0
    // 0xaf0eec: r0 = 130.000000
    //     0xaf0eec: add             x0, PP, #0x42, lsl #12  ; [pp+0x427b0] 130
    //     0xaf0ef0: ldr             x0, [x0, #0x7b0]
    // 0xaf0ef4: stur            x1, [fp, #-0x10]
    // 0xaf0ef8: StoreField: r1->field_13 = r0
    //     0xaf0ef8: stur            w0, [x1, #0x13]
    // 0xaf0efc: ldur            x0, [fp, #-0x38]
    // 0xaf0f00: StoreField: r1->field_b = r0
    //     0xaf0f00: stur            w0, [x1, #0xb]
    // 0xaf0f04: r0 = Visibility()
    //     0xaf0f04: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xaf0f08: mov             x3, x0
    // 0xaf0f0c: ldur            x0, [fp, #-0x10]
    // 0xaf0f10: stur            x3, [fp, #-0x30]
    // 0xaf0f14: StoreField: r3->field_b = r0
    //     0xaf0f14: stur            w0, [x3, #0xb]
    // 0xaf0f18: r0 = Instance_SizedBox
    //     0xaf0f18: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaf0f1c: StoreField: r3->field_f = r0
    //     0xaf0f1c: stur            w0, [x3, #0xf]
    // 0xaf0f20: ldur            x0, [fp, #-0x20]
    // 0xaf0f24: StoreField: r3->field_13 = r0
    //     0xaf0f24: stur            w0, [x3, #0x13]
    // 0xaf0f28: r0 = false
    //     0xaf0f28: add             x0, NULL, #0x30  ; false
    // 0xaf0f2c: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf0f2c: stur            w0, [x3, #0x17]
    // 0xaf0f30: StoreField: r3->field_1b = r0
    //     0xaf0f30: stur            w0, [x3, #0x1b]
    // 0xaf0f34: StoreField: r3->field_1f = r0
    //     0xaf0f34: stur            w0, [x3, #0x1f]
    // 0xaf0f38: StoreField: r3->field_23 = r0
    //     0xaf0f38: stur            w0, [x3, #0x23]
    // 0xaf0f3c: StoreField: r3->field_27 = r0
    //     0xaf0f3c: stur            w0, [x3, #0x27]
    // 0xaf0f40: StoreField: r3->field_2b = r0
    //     0xaf0f40: stur            w0, [x3, #0x2b]
    // 0xaf0f44: r1 = Null
    //     0xaf0f44: mov             x1, NULL
    // 0xaf0f48: r2 = 6
    //     0xaf0f48: movz            x2, #0x6
    // 0xaf0f4c: r0 = AllocateArray()
    //     0xaf0f4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf0f50: mov             x2, x0
    // 0xaf0f54: ldur            x0, [fp, #-0x28]
    // 0xaf0f58: stur            x2, [fp, #-0x10]
    // 0xaf0f5c: StoreField: r2->field_f = r0
    //     0xaf0f5c: stur            w0, [x2, #0xf]
    // 0xaf0f60: r16 = Instance_Divider
    //     0xaf0f60: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0xaf0f64: ldr             x16, [x16, #0x2e0]
    // 0xaf0f68: StoreField: r2->field_13 = r16
    //     0xaf0f68: stur            w16, [x2, #0x13]
    // 0xaf0f6c: ldur            x0, [fp, #-0x30]
    // 0xaf0f70: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf0f70: stur            w0, [x2, #0x17]
    // 0xaf0f74: r1 = <Widget>
    //     0xaf0f74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf0f78: r0 = AllocateGrowableArray()
    //     0xaf0f78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf0f7c: mov             x1, x0
    // 0xaf0f80: ldur            x0, [fp, #-0x10]
    // 0xaf0f84: stur            x1, [fp, #-0x20]
    // 0xaf0f88: StoreField: r1->field_f = r0
    //     0xaf0f88: stur            w0, [x1, #0xf]
    // 0xaf0f8c: r0 = 6
    //     0xaf0f8c: movz            x0, #0x6
    // 0xaf0f90: StoreField: r1->field_b = r0
    //     0xaf0f90: stur            w0, [x1, #0xb]
    // 0xaf0f94: ldur            x2, [fp, #-8]
    // 0xaf0f98: LoadField: r0 = r2->field_b
    //     0xaf0f98: ldur            w0, [x2, #0xb]
    // 0xaf0f9c: DecompressPointer r0
    //     0xaf0f9c: add             x0, x0, HEAP, lsl #32
    // 0xaf0fa0: cmp             w0, NULL
    // 0xaf0fa4: b.eq            #0xaf1820
    // 0xaf0fa8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xaf0fa8: ldur            w3, [x0, #0x17]
    // 0xaf0fac: DecompressPointer r3
    //     0xaf0fac: add             x3, x3, HEAP, lsl #32
    // 0xaf0fb0: r0 = LoadClassIdInstr(r3)
    //     0xaf0fb0: ldur            x0, [x3, #-1]
    //     0xaf0fb4: ubfx            x0, x0, #0xc, #0x14
    // 0xaf0fb8: r16 = "size"
    //     0xaf0fb8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xaf0fbc: ldr             x16, [x16, #0x9c0]
    // 0xaf0fc0: stp             x16, x3, [SP]
    // 0xaf0fc4: mov             lr, x0
    // 0xaf0fc8: ldr             lr, [x21, lr, lsl #3]
    // 0xaf0fcc: blr             lr
    // 0xaf0fd0: tbnz            w0, #4, #0xaf10dc
    // 0xaf0fd4: ldur            x2, [fp, #-0x18]
    // 0xaf0fd8: ldur            x0, [fp, #-0x20]
    // 0xaf0fdc: LoadField: r1 = r2->field_13
    //     0xaf0fdc: ldur            w1, [x2, #0x13]
    // 0xaf0fe0: DecompressPointer r1
    //     0xaf0fe0: add             x1, x1, HEAP, lsl #32
    // 0xaf0fe4: r0 = of()
    //     0xaf0fe4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf0fe8: LoadField: r1 = r0->field_87
    //     0xaf0fe8: ldur            w1, [x0, #0x87]
    // 0xaf0fec: DecompressPointer r1
    //     0xaf0fec: add             x1, x1, HEAP, lsl #32
    // 0xaf0ff0: LoadField: r0 = r1->field_7
    //     0xaf0ff0: ldur            w0, [x1, #7]
    // 0xaf0ff4: DecompressPointer r0
    //     0xaf0ff4: add             x0, x0, HEAP, lsl #32
    // 0xaf0ff8: r16 = 16.000000
    //     0xaf0ff8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf0ffc: ldr             x16, [x16, #0x188]
    // 0xaf1000: r30 = Instance_Color
    //     0xaf1000: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf1004: stp             lr, x16, [SP]
    // 0xaf1008: mov             x1, x0
    // 0xaf100c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf100c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf1010: ldr             x4, [x4, #0xaa0]
    // 0xaf1014: r0 = copyWith()
    //     0xaf1014: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf1018: stur            x0, [fp, #-0x10]
    // 0xaf101c: r0 = Text()
    //     0xaf101c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf1020: mov             x1, x0
    // 0xaf1024: r0 = "Size"
    //     0xaf1024: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xaf1028: ldr             x0, [x0, #0x730]
    // 0xaf102c: stur            x1, [fp, #-0x28]
    // 0xaf1030: StoreField: r1->field_b = r0
    //     0xaf1030: stur            w0, [x1, #0xb]
    // 0xaf1034: ldur            x0, [fp, #-0x10]
    // 0xaf1038: StoreField: r1->field_13 = r0
    //     0xaf1038: stur            w0, [x1, #0x13]
    // 0xaf103c: r0 = Padding()
    //     0xaf103c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf1040: mov             x2, x0
    // 0xaf1044: r0 = Instance_EdgeInsets
    //     0xaf1044: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xaf1048: ldr             x0, [x0, #0xa78]
    // 0xaf104c: stur            x2, [fp, #-0x10]
    // 0xaf1050: StoreField: r2->field_f = r0
    //     0xaf1050: stur            w0, [x2, #0xf]
    // 0xaf1054: ldur            x0, [fp, #-0x28]
    // 0xaf1058: StoreField: r2->field_b = r0
    //     0xaf1058: stur            w0, [x2, #0xb]
    // 0xaf105c: ldur            x0, [fp, #-0x20]
    // 0xaf1060: LoadField: r1 = r0->field_b
    //     0xaf1060: ldur            w1, [x0, #0xb]
    // 0xaf1064: LoadField: r3 = r0->field_f
    //     0xaf1064: ldur            w3, [x0, #0xf]
    // 0xaf1068: DecompressPointer r3
    //     0xaf1068: add             x3, x3, HEAP, lsl #32
    // 0xaf106c: LoadField: r4 = r3->field_b
    //     0xaf106c: ldur            w4, [x3, #0xb]
    // 0xaf1070: r3 = LoadInt32Instr(r1)
    //     0xaf1070: sbfx            x3, x1, #1, #0x1f
    // 0xaf1074: stur            x3, [fp, #-0x40]
    // 0xaf1078: r1 = LoadInt32Instr(r4)
    //     0xaf1078: sbfx            x1, x4, #1, #0x1f
    // 0xaf107c: cmp             x3, x1
    // 0xaf1080: b.ne            #0xaf108c
    // 0xaf1084: mov             x1, x0
    // 0xaf1088: r0 = _growToNextCapacity()
    //     0xaf1088: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf108c: ldur            x2, [fp, #-0x20]
    // 0xaf1090: ldur            x3, [fp, #-0x40]
    // 0xaf1094: add             x0, x3, #1
    // 0xaf1098: lsl             x1, x0, #1
    // 0xaf109c: StoreField: r2->field_b = r1
    //     0xaf109c: stur            w1, [x2, #0xb]
    // 0xaf10a0: LoadField: r1 = r2->field_f
    //     0xaf10a0: ldur            w1, [x2, #0xf]
    // 0xaf10a4: DecompressPointer r1
    //     0xaf10a4: add             x1, x1, HEAP, lsl #32
    // 0xaf10a8: ldur            x0, [fp, #-0x10]
    // 0xaf10ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf10ac: add             x25, x1, x3, lsl #2
    //     0xaf10b0: add             x25, x25, #0xf
    //     0xaf10b4: str             w0, [x25]
    //     0xaf10b8: tbz             w0, #0, #0xaf10d4
    //     0xaf10bc: ldurb           w16, [x1, #-1]
    //     0xaf10c0: ldurb           w17, [x0, #-1]
    //     0xaf10c4: and             x16, x17, x16, lsr #2
    //     0xaf10c8: tst             x16, HEAP, lsr #32
    //     0xaf10cc: b.eq            #0xaf10d4
    //     0xaf10d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf10d4: mov             x3, x2
    // 0xaf10d8: b               #0xaf11e4
    // 0xaf10dc: ldur            x3, [fp, #-0x18]
    // 0xaf10e0: ldur            x2, [fp, #-0x20]
    // 0xaf10e4: r0 = Instance_EdgeInsets
    //     0xaf10e4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xaf10e8: ldr             x0, [x0, #0xa78]
    // 0xaf10ec: LoadField: r1 = r3->field_13
    //     0xaf10ec: ldur            w1, [x3, #0x13]
    // 0xaf10f0: DecompressPointer r1
    //     0xaf10f0: add             x1, x1, HEAP, lsl #32
    // 0xaf10f4: r0 = of()
    //     0xaf10f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf10f8: LoadField: r1 = r0->field_87
    //     0xaf10f8: ldur            w1, [x0, #0x87]
    // 0xaf10fc: DecompressPointer r1
    //     0xaf10fc: add             x1, x1, HEAP, lsl #32
    // 0xaf1100: LoadField: r0 = r1->field_7
    //     0xaf1100: ldur            w0, [x1, #7]
    // 0xaf1104: DecompressPointer r0
    //     0xaf1104: add             x0, x0, HEAP, lsl #32
    // 0xaf1108: r16 = 16.000000
    //     0xaf1108: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf110c: ldr             x16, [x16, #0x188]
    // 0xaf1110: r30 = Instance_Color
    //     0xaf1110: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf1114: stp             lr, x16, [SP]
    // 0xaf1118: mov             x1, x0
    // 0xaf111c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf111c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf1120: ldr             x4, [x4, #0xaa0]
    // 0xaf1124: r0 = copyWith()
    //     0xaf1124: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf1128: stur            x0, [fp, #-0x10]
    // 0xaf112c: r0 = Text()
    //     0xaf112c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf1130: mov             x1, x0
    // 0xaf1134: r0 = "Variant"
    //     0xaf1134: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xaf1138: ldr             x0, [x0, #0x738]
    // 0xaf113c: stur            x1, [fp, #-0x28]
    // 0xaf1140: StoreField: r1->field_b = r0
    //     0xaf1140: stur            w0, [x1, #0xb]
    // 0xaf1144: ldur            x0, [fp, #-0x10]
    // 0xaf1148: StoreField: r1->field_13 = r0
    //     0xaf1148: stur            w0, [x1, #0x13]
    // 0xaf114c: r0 = Padding()
    //     0xaf114c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf1150: mov             x2, x0
    // 0xaf1154: r0 = Instance_EdgeInsets
    //     0xaf1154: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xaf1158: ldr             x0, [x0, #0xa78]
    // 0xaf115c: stur            x2, [fp, #-0x10]
    // 0xaf1160: StoreField: r2->field_f = r0
    //     0xaf1160: stur            w0, [x2, #0xf]
    // 0xaf1164: ldur            x0, [fp, #-0x28]
    // 0xaf1168: StoreField: r2->field_b = r0
    //     0xaf1168: stur            w0, [x2, #0xb]
    // 0xaf116c: ldur            x0, [fp, #-0x20]
    // 0xaf1170: LoadField: r1 = r0->field_b
    //     0xaf1170: ldur            w1, [x0, #0xb]
    // 0xaf1174: LoadField: r3 = r0->field_f
    //     0xaf1174: ldur            w3, [x0, #0xf]
    // 0xaf1178: DecompressPointer r3
    //     0xaf1178: add             x3, x3, HEAP, lsl #32
    // 0xaf117c: LoadField: r4 = r3->field_b
    //     0xaf117c: ldur            w4, [x3, #0xb]
    // 0xaf1180: r3 = LoadInt32Instr(r1)
    //     0xaf1180: sbfx            x3, x1, #1, #0x1f
    // 0xaf1184: stur            x3, [fp, #-0x40]
    // 0xaf1188: r1 = LoadInt32Instr(r4)
    //     0xaf1188: sbfx            x1, x4, #1, #0x1f
    // 0xaf118c: cmp             x3, x1
    // 0xaf1190: b.ne            #0xaf119c
    // 0xaf1194: mov             x1, x0
    // 0xaf1198: r0 = _growToNextCapacity()
    //     0xaf1198: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf119c: ldur            x3, [fp, #-0x20]
    // 0xaf11a0: ldur            x2, [fp, #-0x40]
    // 0xaf11a4: add             x0, x2, #1
    // 0xaf11a8: lsl             x1, x0, #1
    // 0xaf11ac: StoreField: r3->field_b = r1
    //     0xaf11ac: stur            w1, [x3, #0xb]
    // 0xaf11b0: LoadField: r1 = r3->field_f
    //     0xaf11b0: ldur            w1, [x3, #0xf]
    // 0xaf11b4: DecompressPointer r1
    //     0xaf11b4: add             x1, x1, HEAP, lsl #32
    // 0xaf11b8: ldur            x0, [fp, #-0x10]
    // 0xaf11bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaf11bc: add             x25, x1, x2, lsl #2
    //     0xaf11c0: add             x25, x25, #0xf
    //     0xaf11c4: str             w0, [x25]
    //     0xaf11c8: tbz             w0, #0, #0xaf11e4
    //     0xaf11cc: ldurb           w16, [x1, #-1]
    //     0xaf11d0: ldurb           w17, [x0, #-1]
    //     0xaf11d4: and             x16, x17, x16, lsr #2
    //     0xaf11d8: tst             x16, HEAP, lsr #32
    //     0xaf11dc: b.eq            #0xaf11e4
    //     0xaf11e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf11e4: ldur            x0, [fp, #-8]
    // 0xaf11e8: LoadField: r1 = r0->field_b
    //     0xaf11e8: ldur            w1, [x0, #0xb]
    // 0xaf11ec: DecompressPointer r1
    //     0xaf11ec: add             x1, x1, HEAP, lsl #32
    // 0xaf11f0: cmp             w1, NULL
    // 0xaf11f4: b.eq            #0xaf1824
    // 0xaf11f8: LoadField: r2 = r1->field_b
    //     0xaf11f8: ldur            w2, [x1, #0xb]
    // 0xaf11fc: DecompressPointer r2
    //     0xaf11fc: add             x2, x2, HEAP, lsl #32
    // 0xaf1200: LoadField: r4 = r2->field_73
    //     0xaf1200: ldur            w4, [x2, #0x73]
    // 0xaf1204: DecompressPointer r4
    //     0xaf1204: add             x4, x4, HEAP, lsl #32
    // 0xaf1208: cmp             w4, NULL
    // 0xaf120c: b.ne            #0xaf1218
    // 0xaf1210: r1 = Null
    //     0xaf1210: mov             x1, NULL
    // 0xaf1214: b               #0xaf1230
    // 0xaf1218: LoadField: r1 = r4->field_b
    //     0xaf1218: ldur            w1, [x4, #0xb]
    // 0xaf121c: cbz             w1, #0xaf1228
    // 0xaf1220: r5 = false
    //     0xaf1220: add             x5, NULL, #0x30  ; false
    // 0xaf1224: b               #0xaf122c
    // 0xaf1228: r5 = true
    //     0xaf1228: add             x5, NULL, #0x20  ; true
    // 0xaf122c: mov             x1, x5
    // 0xaf1230: cmp             w1, NULL
    // 0xaf1234: b.eq            #0xaf123c
    // 0xaf1238: tbnz            w1, #4, #0xaf1364
    // 0xaf123c: LoadField: r1 = r2->field_6f
    //     0xaf123c: ldur            w1, [x2, #0x6f]
    // 0xaf1240: DecompressPointer r1
    //     0xaf1240: add             x1, x1, HEAP, lsl #32
    // 0xaf1244: cmp             w1, NULL
    // 0xaf1248: b.ne            #0xaf1254
    // 0xaf124c: r1 = Null
    //     0xaf124c: mov             x1, NULL
    // 0xaf1250: b               #0xaf125c
    // 0xaf1254: LoadField: r2 = r1->field_b
    //     0xaf1254: ldur            w2, [x1, #0xb]
    // 0xaf1258: mov             x1, x2
    // 0xaf125c: cmp             w1, NULL
    // 0xaf1260: b.ne            #0xaf126c
    // 0xaf1264: r1 = 0
    //     0xaf1264: movz            x1, #0
    // 0xaf1268: b               #0xaf1274
    // 0xaf126c: r2 = LoadInt32Instr(r1)
    //     0xaf126c: sbfx            x2, x1, #1, #0x1f
    // 0xaf1270: mov             x1, x2
    // 0xaf1274: lsl             x4, x1, #1
    // 0xaf1278: ldur            x2, [fp, #-0x18]
    // 0xaf127c: stur            x4, [fp, #-0x10]
    // 0xaf1280: r1 = Function '<anonymous closure>':.
    //     0xaf1280: add             x1, PP, #0x58, lsl #12  ; [pp+0x58640] AnonymousClosure: (0xaf309c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf1284: ldr             x1, [x1, #0x640]
    // 0xaf1288: r0 = AllocateClosure()
    //     0xaf1288: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf128c: stur            x0, [fp, #-0x28]
    // 0xaf1290: r0 = ListView()
    //     0xaf1290: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xaf1294: stur            x0, [fp, #-0x30]
    // 0xaf1298: r16 = Instance_Axis
    //     0xaf1298: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf129c: str             x16, [SP]
    // 0xaf12a0: mov             x1, x0
    // 0xaf12a4: ldur            x2, [fp, #-0x28]
    // 0xaf12a8: ldur            x3, [fp, #-0x10]
    // 0xaf12ac: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xaf12ac: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xaf12b0: ldr             x4, [x4, #0x4a0]
    // 0xaf12b4: r0 = ListView.builder()
    //     0xaf12b4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaf12b8: r0 = SizedBox()
    //     0xaf12b8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf12bc: mov             x2, x0
    // 0xaf12c0: r0 = inf
    //     0xaf12c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xaf12c4: ldr             x0, [x0, #0x9f8]
    // 0xaf12c8: stur            x2, [fp, #-0x10]
    // 0xaf12cc: StoreField: r2->field_f = r0
    //     0xaf12cc: stur            w0, [x2, #0xf]
    // 0xaf12d0: r3 = 55.000000
    //     0xaf12d0: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xaf12d4: ldr             x3, [x3, #0x9b8]
    // 0xaf12d8: StoreField: r2->field_13 = r3
    //     0xaf12d8: stur            w3, [x2, #0x13]
    // 0xaf12dc: ldur            x1, [fp, #-0x30]
    // 0xaf12e0: StoreField: r2->field_b = r1
    //     0xaf12e0: stur            w1, [x2, #0xb]
    // 0xaf12e4: ldur            x3, [fp, #-0x20]
    // 0xaf12e8: LoadField: r1 = r3->field_b
    //     0xaf12e8: ldur            w1, [x3, #0xb]
    // 0xaf12ec: LoadField: r4 = r3->field_f
    //     0xaf12ec: ldur            w4, [x3, #0xf]
    // 0xaf12f0: DecompressPointer r4
    //     0xaf12f0: add             x4, x4, HEAP, lsl #32
    // 0xaf12f4: LoadField: r5 = r4->field_b
    //     0xaf12f4: ldur            w5, [x4, #0xb]
    // 0xaf12f8: r4 = LoadInt32Instr(r1)
    //     0xaf12f8: sbfx            x4, x1, #1, #0x1f
    // 0xaf12fc: stur            x4, [fp, #-0x40]
    // 0xaf1300: r1 = LoadInt32Instr(r5)
    //     0xaf1300: sbfx            x1, x5, #1, #0x1f
    // 0xaf1304: cmp             x4, x1
    // 0xaf1308: b.ne            #0xaf1314
    // 0xaf130c: mov             x1, x3
    // 0xaf1310: r0 = _growToNextCapacity()
    //     0xaf1310: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf1314: ldur            x5, [fp, #-0x20]
    // 0xaf1318: ldur            x2, [fp, #-0x40]
    // 0xaf131c: add             x0, x2, #1
    // 0xaf1320: lsl             x1, x0, #1
    // 0xaf1324: StoreField: r5->field_b = r1
    //     0xaf1324: stur            w1, [x5, #0xb]
    // 0xaf1328: LoadField: r1 = r5->field_f
    //     0xaf1328: ldur            w1, [x5, #0xf]
    // 0xaf132c: DecompressPointer r1
    //     0xaf132c: add             x1, x1, HEAP, lsl #32
    // 0xaf1330: ldur            x0, [fp, #-0x10]
    // 0xaf1334: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaf1334: add             x25, x1, x2, lsl #2
    //     0xaf1338: add             x25, x25, #0xf
    //     0xaf133c: str             w0, [x25]
    //     0xaf1340: tbz             w0, #0, #0xaf135c
    //     0xaf1344: ldurb           w16, [x1, #-1]
    //     0xaf1348: ldurb           w17, [x0, #-1]
    //     0xaf134c: and             x16, x17, x16, lsr #2
    //     0xaf1350: tst             x16, HEAP, lsr #32
    //     0xaf1354: b.eq            #0xaf135c
    //     0xaf1358: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf135c: mov             x2, x5
    // 0xaf1360: b               #0xaf14d8
    // 0xaf1364: mov             x5, x3
    // 0xaf1368: r3 = 55.000000
    //     0xaf1368: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xaf136c: ldr             x3, [x3, #0x9b8]
    // 0xaf1370: cmp             w4, NULL
    // 0xaf1374: b.ne            #0xaf1384
    // 0xaf1378: ldur            x6, [fp, #-8]
    // 0xaf137c: r0 = Null
    //     0xaf137c: mov             x0, NULL
    // 0xaf1380: b               #0xaf13d8
    // 0xaf1384: ldur            x6, [fp, #-8]
    // 0xaf1388: LoadField: r2 = r6->field_1f
    //     0xaf1388: ldur            x2, [x6, #0x1f]
    // 0xaf138c: LoadField: r0 = r4->field_b
    //     0xaf138c: ldur            w0, [x4, #0xb]
    // 0xaf1390: r1 = LoadInt32Instr(r0)
    //     0xaf1390: sbfx            x1, x0, #1, #0x1f
    // 0xaf1394: mov             x0, x1
    // 0xaf1398: mov             x1, x2
    // 0xaf139c: cmp             x1, x0
    // 0xaf13a0: b.hs            #0xaf1828
    // 0xaf13a4: LoadField: r0 = r4->field_f
    //     0xaf13a4: ldur            w0, [x4, #0xf]
    // 0xaf13a8: DecompressPointer r0
    //     0xaf13a8: add             x0, x0, HEAP, lsl #32
    // 0xaf13ac: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xaf13ac: add             x16, x0, x2, lsl #2
    //     0xaf13b0: ldur            w1, [x16, #0xf]
    // 0xaf13b4: DecompressPointer r1
    //     0xaf13b4: add             x1, x1, HEAP, lsl #32
    // 0xaf13b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf13b8: ldur            w0, [x1, #0x17]
    // 0xaf13bc: DecompressPointer r0
    //     0xaf13bc: add             x0, x0, HEAP, lsl #32
    // 0xaf13c0: cmp             w0, NULL
    // 0xaf13c4: b.ne            #0xaf13d0
    // 0xaf13c8: r0 = Null
    //     0xaf13c8: mov             x0, NULL
    // 0xaf13cc: b               #0xaf13d8
    // 0xaf13d0: LoadField: r1 = r0->field_b
    //     0xaf13d0: ldur            w1, [x0, #0xb]
    // 0xaf13d4: mov             x0, x1
    // 0xaf13d8: cmp             w0, NULL
    // 0xaf13dc: b.ne            #0xaf13e8
    // 0xaf13e0: r0 = 0
    //     0xaf13e0: movz            x0, #0
    // 0xaf13e4: b               #0xaf13f0
    // 0xaf13e8: r1 = LoadInt32Instr(r0)
    //     0xaf13e8: sbfx            x1, x0, #1, #0x1f
    // 0xaf13ec: mov             x0, x1
    // 0xaf13f0: lsl             x4, x0, #1
    // 0xaf13f4: ldur            x2, [fp, #-0x18]
    // 0xaf13f8: stur            x4, [fp, #-0x10]
    // 0xaf13fc: r1 = Function '<anonymous closure>':.
    //     0xaf13fc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58648] AnonymousClosure: (0xaf2cb8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf1400: ldr             x1, [x1, #0x648]
    // 0xaf1404: r0 = AllocateClosure()
    //     0xaf1404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf1408: stur            x0, [fp, #-0x28]
    // 0xaf140c: r0 = ListView()
    //     0xaf140c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xaf1410: stur            x0, [fp, #-0x30]
    // 0xaf1414: r16 = Instance_Axis
    //     0xaf1414: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf1418: str             x16, [SP]
    // 0xaf141c: mov             x1, x0
    // 0xaf1420: ldur            x2, [fp, #-0x28]
    // 0xaf1424: ldur            x3, [fp, #-0x10]
    // 0xaf1428: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xaf1428: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xaf142c: ldr             x4, [x4, #0x4a0]
    // 0xaf1430: r0 = ListView.builder()
    //     0xaf1430: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaf1434: r0 = SizedBox()
    //     0xaf1434: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf1438: mov             x2, x0
    // 0xaf143c: r0 = inf
    //     0xaf143c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xaf1440: ldr             x0, [x0, #0x9f8]
    // 0xaf1444: stur            x2, [fp, #-0x10]
    // 0xaf1448: StoreField: r2->field_f = r0
    //     0xaf1448: stur            w0, [x2, #0xf]
    // 0xaf144c: r1 = 55.000000
    //     0xaf144c: add             x1, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xaf1450: ldr             x1, [x1, #0x9b8]
    // 0xaf1454: StoreField: r2->field_13 = r1
    //     0xaf1454: stur            w1, [x2, #0x13]
    // 0xaf1458: ldur            x1, [fp, #-0x30]
    // 0xaf145c: StoreField: r2->field_b = r1
    //     0xaf145c: stur            w1, [x2, #0xb]
    // 0xaf1460: ldur            x3, [fp, #-0x20]
    // 0xaf1464: LoadField: r1 = r3->field_b
    //     0xaf1464: ldur            w1, [x3, #0xb]
    // 0xaf1468: LoadField: r4 = r3->field_f
    //     0xaf1468: ldur            w4, [x3, #0xf]
    // 0xaf146c: DecompressPointer r4
    //     0xaf146c: add             x4, x4, HEAP, lsl #32
    // 0xaf1470: LoadField: r5 = r4->field_b
    //     0xaf1470: ldur            w5, [x4, #0xb]
    // 0xaf1474: r4 = LoadInt32Instr(r1)
    //     0xaf1474: sbfx            x4, x1, #1, #0x1f
    // 0xaf1478: stur            x4, [fp, #-0x40]
    // 0xaf147c: r1 = LoadInt32Instr(r5)
    //     0xaf147c: sbfx            x1, x5, #1, #0x1f
    // 0xaf1480: cmp             x4, x1
    // 0xaf1484: b.ne            #0xaf1490
    // 0xaf1488: mov             x1, x3
    // 0xaf148c: r0 = _growToNextCapacity()
    //     0xaf148c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf1490: ldur            x2, [fp, #-0x20]
    // 0xaf1494: ldur            x3, [fp, #-0x40]
    // 0xaf1498: add             x0, x3, #1
    // 0xaf149c: lsl             x1, x0, #1
    // 0xaf14a0: StoreField: r2->field_b = r1
    //     0xaf14a0: stur            w1, [x2, #0xb]
    // 0xaf14a4: LoadField: r1 = r2->field_f
    //     0xaf14a4: ldur            w1, [x2, #0xf]
    // 0xaf14a8: DecompressPointer r1
    //     0xaf14a8: add             x1, x1, HEAP, lsl #32
    // 0xaf14ac: ldur            x0, [fp, #-0x10]
    // 0xaf14b0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf14b0: add             x25, x1, x3, lsl #2
    //     0xaf14b4: add             x25, x25, #0xf
    //     0xaf14b8: str             w0, [x25]
    //     0xaf14bc: tbz             w0, #0, #0xaf14d8
    //     0xaf14c0: ldurb           w16, [x1, #-1]
    //     0xaf14c4: ldurb           w17, [x0, #-1]
    //     0xaf14c8: and             x16, x17, x16, lsr #2
    //     0xaf14cc: tst             x16, HEAP, lsr #32
    //     0xaf14d0: b.eq            #0xaf14d8
    //     0xaf14d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf14d8: ldur            x0, [fp, #-8]
    // 0xaf14dc: ldur            x1, [fp, #-0x18]
    // 0xaf14e0: r16 = <EdgeInsets>
    //     0xaf14e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaf14e4: ldr             x16, [x16, #0xda0]
    // 0xaf14e8: r30 = Instance_EdgeInsets
    //     0xaf14e8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xaf14ec: ldr             lr, [lr, #0x1f0]
    // 0xaf14f0: stp             lr, x16, [SP]
    // 0xaf14f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf14f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf14f8: r0 = all()
    //     0xaf14f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf14fc: ldur            x2, [fp, #-0x18]
    // 0xaf1500: stur            x0, [fp, #-0x10]
    // 0xaf1504: LoadField: r1 = r2->field_13
    //     0xaf1504: ldur            w1, [x2, #0x13]
    // 0xaf1508: DecompressPointer r1
    //     0xaf1508: add             x1, x1, HEAP, lsl #32
    // 0xaf150c: r0 = of()
    //     0xaf150c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf1510: LoadField: r1 = r0->field_5b
    //     0xaf1510: ldur            w1, [x0, #0x5b]
    // 0xaf1514: DecompressPointer r1
    //     0xaf1514: add             x1, x1, HEAP, lsl #32
    // 0xaf1518: r16 = <Color>
    //     0xaf1518: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaf151c: ldr             x16, [x16, #0xf80]
    // 0xaf1520: stp             x1, x16, [SP]
    // 0xaf1524: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf1524: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf1528: r0 = all()
    //     0xaf1528: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf152c: stur            x0, [fp, #-0x28]
    // 0xaf1530: r16 = <Color>
    //     0xaf1530: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaf1534: ldr             x16, [x16, #0xf80]
    // 0xaf1538: r30 = Instance_Color
    //     0xaf1538: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf153c: stp             lr, x16, [SP]
    // 0xaf1540: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf1540: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf1544: r0 = all()
    //     0xaf1544: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf1548: stur            x0, [fp, #-0x30]
    // 0xaf154c: r0 = Radius()
    //     0xaf154c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf1550: d0 = 30.000000
    //     0xaf1550: fmov            d0, #30.00000000
    // 0xaf1554: stur            x0, [fp, #-0x38]
    // 0xaf1558: StoreField: r0->field_7 = d0
    //     0xaf1558: stur            d0, [x0, #7]
    // 0xaf155c: StoreField: r0->field_f = d0
    //     0xaf155c: stur            d0, [x0, #0xf]
    // 0xaf1560: r0 = BorderRadius()
    //     0xaf1560: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf1564: mov             x1, x0
    // 0xaf1568: ldur            x0, [fp, #-0x38]
    // 0xaf156c: stur            x1, [fp, #-0x48]
    // 0xaf1570: StoreField: r1->field_7 = r0
    //     0xaf1570: stur            w0, [x1, #7]
    // 0xaf1574: StoreField: r1->field_b = r0
    //     0xaf1574: stur            w0, [x1, #0xb]
    // 0xaf1578: StoreField: r1->field_f = r0
    //     0xaf1578: stur            w0, [x1, #0xf]
    // 0xaf157c: StoreField: r1->field_13 = r0
    //     0xaf157c: stur            w0, [x1, #0x13]
    // 0xaf1580: r0 = RoundedRectangleBorder()
    //     0xaf1580: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaf1584: mov             x1, x0
    // 0xaf1588: ldur            x0, [fp, #-0x48]
    // 0xaf158c: StoreField: r1->field_b = r0
    //     0xaf158c: stur            w0, [x1, #0xb]
    // 0xaf1590: r0 = Instance_BorderSide
    //     0xaf1590: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaf1594: ldr             x0, [x0, #0xe20]
    // 0xaf1598: StoreField: r1->field_7 = r0
    //     0xaf1598: stur            w0, [x1, #7]
    // 0xaf159c: r16 = <RoundedRectangleBorder>
    //     0xaf159c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaf15a0: ldr             x16, [x16, #0xf78]
    // 0xaf15a4: stp             x1, x16, [SP]
    // 0xaf15a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf15a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf15ac: r0 = all()
    //     0xaf15ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf15b0: stur            x0, [fp, #-0x38]
    // 0xaf15b4: r0 = ButtonStyle()
    //     0xaf15b4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaf15b8: mov             x1, x0
    // 0xaf15bc: ldur            x0, [fp, #-0x28]
    // 0xaf15c0: stur            x1, [fp, #-0x48]
    // 0xaf15c4: StoreField: r1->field_b = r0
    //     0xaf15c4: stur            w0, [x1, #0xb]
    // 0xaf15c8: ldur            x0, [fp, #-0x30]
    // 0xaf15cc: StoreField: r1->field_f = r0
    //     0xaf15cc: stur            w0, [x1, #0xf]
    // 0xaf15d0: ldur            x0, [fp, #-0x10]
    // 0xaf15d4: StoreField: r1->field_23 = r0
    //     0xaf15d4: stur            w0, [x1, #0x23]
    // 0xaf15d8: ldur            x0, [fp, #-0x38]
    // 0xaf15dc: StoreField: r1->field_43 = r0
    //     0xaf15dc: stur            w0, [x1, #0x43]
    // 0xaf15e0: r0 = TextButtonThemeData()
    //     0xaf15e0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaf15e4: mov             x2, x0
    // 0xaf15e8: ldur            x0, [fp, #-0x48]
    // 0xaf15ec: stur            x2, [fp, #-0x10]
    // 0xaf15f0: StoreField: r2->field_7 = r0
    //     0xaf15f0: stur            w0, [x2, #7]
    // 0xaf15f4: ldur            x0, [fp, #-8]
    // 0xaf15f8: LoadField: r1 = r0->field_b
    //     0xaf15f8: ldur            w1, [x0, #0xb]
    // 0xaf15fc: DecompressPointer r1
    //     0xaf15fc: add             x1, x1, HEAP, lsl #32
    // 0xaf1600: cmp             w1, NULL
    // 0xaf1604: b.eq            #0xaf182c
    // 0xaf1608: LoadField: r0 = r1->field_b
    //     0xaf1608: ldur            w0, [x1, #0xb]
    // 0xaf160c: DecompressPointer r0
    //     0xaf160c: add             x0, x0, HEAP, lsl #32
    // 0xaf1610: LoadField: r1 = r0->field_f
    //     0xaf1610: ldur            w1, [x0, #0xf]
    // 0xaf1614: DecompressPointer r1
    //     0xaf1614: add             x1, x1, HEAP, lsl #32
    // 0xaf1618: cmp             w1, NULL
    // 0xaf161c: b.ne            #0xaf1624
    // 0xaf1620: r1 = ""
    //     0xaf1620: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1624: ldur            x0, [fp, #-0x18]
    // 0xaf1628: ldur            x3, [fp, #-0x20]
    // 0xaf162c: r0 = capitalizeFirstWord()
    //     0xaf162c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaf1630: ldur            x2, [fp, #-0x18]
    // 0xaf1634: stur            x0, [fp, #-8]
    // 0xaf1638: LoadField: r1 = r2->field_13
    //     0xaf1638: ldur            w1, [x2, #0x13]
    // 0xaf163c: DecompressPointer r1
    //     0xaf163c: add             x1, x1, HEAP, lsl #32
    // 0xaf1640: r0 = of()
    //     0xaf1640: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf1644: LoadField: r1 = r0->field_87
    //     0xaf1644: ldur            w1, [x0, #0x87]
    // 0xaf1648: DecompressPointer r1
    //     0xaf1648: add             x1, x1, HEAP, lsl #32
    // 0xaf164c: LoadField: r0 = r1->field_7
    //     0xaf164c: ldur            w0, [x1, #7]
    // 0xaf1650: DecompressPointer r0
    //     0xaf1650: add             x0, x0, HEAP, lsl #32
    // 0xaf1654: r16 = 16.000000
    //     0xaf1654: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf1658: ldr             x16, [x16, #0x188]
    // 0xaf165c: r30 = Instance_Color
    //     0xaf165c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf1660: stp             lr, x16, [SP]
    // 0xaf1664: mov             x1, x0
    // 0xaf1668: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf1668: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf166c: ldr             x4, [x4, #0xaa0]
    // 0xaf1670: r0 = copyWith()
    //     0xaf1670: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf1674: stur            x0, [fp, #-0x28]
    // 0xaf1678: r0 = Text()
    //     0xaf1678: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf167c: mov             x3, x0
    // 0xaf1680: ldur            x0, [fp, #-8]
    // 0xaf1684: stur            x3, [fp, #-0x30]
    // 0xaf1688: StoreField: r3->field_b = r0
    //     0xaf1688: stur            w0, [x3, #0xb]
    // 0xaf168c: ldur            x0, [fp, #-0x28]
    // 0xaf1690: StoreField: r3->field_13 = r0
    //     0xaf1690: stur            w0, [x3, #0x13]
    // 0xaf1694: ldur            x2, [fp, #-0x18]
    // 0xaf1698: r1 = Function '<anonymous closure>':.
    //     0xaf1698: add             x1, PP, #0x58, lsl #12  ; [pp+0x58650] AnonymousClosure: (0xaf1830), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf169c: ldr             x1, [x1, #0x650]
    // 0xaf16a0: r0 = AllocateClosure()
    //     0xaf16a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf16a4: stur            x0, [fp, #-8]
    // 0xaf16a8: r0 = TextButton()
    //     0xaf16a8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaf16ac: mov             x1, x0
    // 0xaf16b0: ldur            x0, [fp, #-8]
    // 0xaf16b4: stur            x1, [fp, #-0x18]
    // 0xaf16b8: StoreField: r1->field_b = r0
    //     0xaf16b8: stur            w0, [x1, #0xb]
    // 0xaf16bc: r0 = false
    //     0xaf16bc: add             x0, NULL, #0x30  ; false
    // 0xaf16c0: StoreField: r1->field_27 = r0
    //     0xaf16c0: stur            w0, [x1, #0x27]
    // 0xaf16c4: r0 = true
    //     0xaf16c4: add             x0, NULL, #0x20  ; true
    // 0xaf16c8: StoreField: r1->field_2f = r0
    //     0xaf16c8: stur            w0, [x1, #0x2f]
    // 0xaf16cc: ldur            x0, [fp, #-0x30]
    // 0xaf16d0: StoreField: r1->field_37 = r0
    //     0xaf16d0: stur            w0, [x1, #0x37]
    // 0xaf16d4: r0 = Instance_ValueKey
    //     0xaf16d4: add             x0, PP, #0x53, lsl #12  ; [pp+0x53c18] Obj!ValueKey<String>@d5b2f1
    //     0xaf16d8: ldr             x0, [x0, #0xc18]
    // 0xaf16dc: StoreField: r1->field_7 = r0
    //     0xaf16dc: stur            w0, [x1, #7]
    // 0xaf16e0: r0 = TextButtonTheme()
    //     0xaf16e0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaf16e4: mov             x1, x0
    // 0xaf16e8: ldur            x0, [fp, #-0x10]
    // 0xaf16ec: stur            x1, [fp, #-8]
    // 0xaf16f0: StoreField: r1->field_f = r0
    //     0xaf16f0: stur            w0, [x1, #0xf]
    // 0xaf16f4: ldur            x0, [fp, #-0x18]
    // 0xaf16f8: StoreField: r1->field_b = r0
    //     0xaf16f8: stur            w0, [x1, #0xb]
    // 0xaf16fc: r0 = SizedBox()
    //     0xaf16fc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf1700: mov             x1, x0
    // 0xaf1704: r0 = inf
    //     0xaf1704: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xaf1708: ldr             x0, [x0, #0x9f8]
    // 0xaf170c: stur            x1, [fp, #-0x10]
    // 0xaf1710: StoreField: r1->field_f = r0
    //     0xaf1710: stur            w0, [x1, #0xf]
    // 0xaf1714: ldur            x0, [fp, #-8]
    // 0xaf1718: StoreField: r1->field_b = r0
    //     0xaf1718: stur            w0, [x1, #0xb]
    // 0xaf171c: r0 = Padding()
    //     0xaf171c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf1720: mov             x2, x0
    // 0xaf1724: r0 = Instance_EdgeInsets
    //     0xaf1724: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xaf1728: ldr             x0, [x0, #0xf48]
    // 0xaf172c: stur            x2, [fp, #-8]
    // 0xaf1730: StoreField: r2->field_f = r0
    //     0xaf1730: stur            w0, [x2, #0xf]
    // 0xaf1734: ldur            x0, [fp, #-0x10]
    // 0xaf1738: StoreField: r2->field_b = r0
    //     0xaf1738: stur            w0, [x2, #0xb]
    // 0xaf173c: ldur            x0, [fp, #-0x20]
    // 0xaf1740: LoadField: r1 = r0->field_b
    //     0xaf1740: ldur            w1, [x0, #0xb]
    // 0xaf1744: LoadField: r3 = r0->field_f
    //     0xaf1744: ldur            w3, [x0, #0xf]
    // 0xaf1748: DecompressPointer r3
    //     0xaf1748: add             x3, x3, HEAP, lsl #32
    // 0xaf174c: LoadField: r4 = r3->field_b
    //     0xaf174c: ldur            w4, [x3, #0xb]
    // 0xaf1750: r3 = LoadInt32Instr(r1)
    //     0xaf1750: sbfx            x3, x1, #1, #0x1f
    // 0xaf1754: stur            x3, [fp, #-0x40]
    // 0xaf1758: r1 = LoadInt32Instr(r4)
    //     0xaf1758: sbfx            x1, x4, #1, #0x1f
    // 0xaf175c: cmp             x3, x1
    // 0xaf1760: b.ne            #0xaf176c
    // 0xaf1764: mov             x1, x0
    // 0xaf1768: r0 = _growToNextCapacity()
    //     0xaf1768: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf176c: ldur            x2, [fp, #-0x20]
    // 0xaf1770: ldur            x3, [fp, #-0x40]
    // 0xaf1774: add             x0, x3, #1
    // 0xaf1778: lsl             x1, x0, #1
    // 0xaf177c: StoreField: r2->field_b = r1
    //     0xaf177c: stur            w1, [x2, #0xb]
    // 0xaf1780: LoadField: r1 = r2->field_f
    //     0xaf1780: ldur            w1, [x2, #0xf]
    // 0xaf1784: DecompressPointer r1
    //     0xaf1784: add             x1, x1, HEAP, lsl #32
    // 0xaf1788: ldur            x0, [fp, #-8]
    // 0xaf178c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf178c: add             x25, x1, x3, lsl #2
    //     0xaf1790: add             x25, x25, #0xf
    //     0xaf1794: str             w0, [x25]
    //     0xaf1798: tbz             w0, #0, #0xaf17b4
    //     0xaf179c: ldurb           w16, [x1, #-1]
    //     0xaf17a0: ldurb           w17, [x0, #-1]
    //     0xaf17a4: and             x16, x17, x16, lsr #2
    //     0xaf17a8: tst             x16, HEAP, lsr #32
    //     0xaf17ac: b.eq            #0xaf17b4
    //     0xaf17b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf17b4: r0 = Column()
    //     0xaf17b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf17b8: r1 = Instance_Axis
    //     0xaf17b8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf17bc: StoreField: r0->field_f = r1
    //     0xaf17bc: stur            w1, [x0, #0xf]
    // 0xaf17c0: r1 = Instance_MainAxisAlignment
    //     0xaf17c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf17c4: ldr             x1, [x1, #0xa08]
    // 0xaf17c8: StoreField: r0->field_13 = r1
    //     0xaf17c8: stur            w1, [x0, #0x13]
    // 0xaf17cc: r1 = Instance_MainAxisSize
    //     0xaf17cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xaf17d0: ldr             x1, [x1, #0xdd0]
    // 0xaf17d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf17d4: stur            w1, [x0, #0x17]
    // 0xaf17d8: r1 = Instance_CrossAxisAlignment
    //     0xaf17d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf17dc: ldr             x1, [x1, #0x890]
    // 0xaf17e0: StoreField: r0->field_1b = r1
    //     0xaf17e0: stur            w1, [x0, #0x1b]
    // 0xaf17e4: r1 = Instance_VerticalDirection
    //     0xaf17e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf17e8: ldr             x1, [x1, #0xa20]
    // 0xaf17ec: StoreField: r0->field_23 = r1
    //     0xaf17ec: stur            w1, [x0, #0x23]
    // 0xaf17f0: r1 = Instance_Clip
    //     0xaf17f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf17f4: ldr             x1, [x1, #0x38]
    // 0xaf17f8: StoreField: r0->field_2b = r1
    //     0xaf17f8: stur            w1, [x0, #0x2b]
    // 0xaf17fc: StoreField: r0->field_2f = rZR
    //     0xaf17fc: stur            xzr, [x0, #0x2f]
    // 0xaf1800: ldur            x1, [fp, #-0x20]
    // 0xaf1804: StoreField: r0->field_b = r1
    //     0xaf1804: stur            w1, [x0, #0xb]
    // 0xaf1808: LeaveFrame
    //     0xaf1808: mov             SP, fp
    //     0xaf180c: ldp             fp, lr, [SP], #0x10
    // 0xaf1810: ret
    //     0xaf1810: ret             
    // 0xaf1814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf1814: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf1818: b               #0xaf0c84
    // 0xaf181c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf181c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf1820: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf1820: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf1824: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf1824: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf1828: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf1828: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf182c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf182c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf1830, size: 0x1488
    // 0xaf1830: EnterFrame
    //     0xaf1830: stp             fp, lr, [SP, #-0x10]!
    //     0xaf1834: mov             fp, SP
    // 0xaf1838: AllocStack(0x80)
    //     0xaf1838: sub             SP, SP, #0x80
    // 0xaf183c: SetupParameters()
    //     0xaf183c: ldr             x0, [fp, #0x10]
    //     0xaf1840: ldur            w1, [x0, #0x17]
    //     0xaf1844: add             x1, x1, HEAP, lsl #32
    //     0xaf1848: stur            x1, [fp, #-8]
    // 0xaf184c: CheckStackOverflow
    //     0xaf184c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf1850: cmp             SP, x16
    //     0xaf1854: b.ls            #0xaf2bbc
    // 0xaf1858: LoadField: r0 = r1->field_13
    //     0xaf1858: ldur            w0, [x1, #0x13]
    // 0xaf185c: DecompressPointer r0
    //     0xaf185c: add             x0, x0, HEAP, lsl #32
    // 0xaf1860: r16 = <Object?>
    //     0xaf1860: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xaf1864: stp             x0, x16, [SP]
    // 0xaf1868: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf1868: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf186c: r0 = pop()
    //     0xaf186c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xaf1870: ldur            x1, [fp, #-8]
    // 0xaf1874: LoadField: r0 = r1->field_f
    //     0xaf1874: ldur            w0, [x1, #0xf]
    // 0xaf1878: DecompressPointer r0
    //     0xaf1878: add             x0, x0, HEAP, lsl #32
    // 0xaf187c: LoadField: r2 = r0->field_b
    //     0xaf187c: ldur            w2, [x0, #0xb]
    // 0xaf1880: DecompressPointer r2
    //     0xaf1880: add             x2, x2, HEAP, lsl #32
    // 0xaf1884: cmp             w2, NULL
    // 0xaf1888: b.eq            #0xaf2bc4
    // 0xaf188c: LoadField: r0 = r2->field_f
    //     0xaf188c: ldur            w0, [x2, #0xf]
    // 0xaf1890: DecompressPointer r0
    //     0xaf1890: add             x0, x0, HEAP, lsl #32
    // 0xaf1894: r2 = LoadClassIdInstr(r0)
    //     0xaf1894: ldur            x2, [x0, #-1]
    //     0xaf1898: ubfx            x2, x2, #0xc, #0x14
    // 0xaf189c: r16 = "home_page"
    //     0xaf189c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xaf18a0: ldr             x16, [x16, #0xe60]
    // 0xaf18a4: stp             x16, x0, [SP]
    // 0xaf18a8: mov             x0, x2
    // 0xaf18ac: mov             lr, x0
    // 0xaf18b0: ldr             lr, [x21, lr, lsl #3]
    // 0xaf18b4: blr             lr
    // 0xaf18b8: tbnz            w0, #4, #0xaf18f8
    // 0xaf18bc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf18bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf18c0: ldr             x0, [x0, #0x1c80]
    //     0xaf18c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf18c8: cmp             w0, w16
    //     0xaf18cc: b.ne            #0xaf18d8
    //     0xaf18d0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaf18d4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf18d8: r16 = <HomeController>
    //     0xaf18d8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xaf18dc: ldr             x16, [x16, #0xf98]
    // 0xaf18e0: str             x16, [SP]
    // 0xaf18e4: r4 = const [0x1, 0, 0, 0, null]
    //     0xaf18e4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaf18e8: r0 = Inst.find()
    //     0xaf18e8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xaf18ec: r3 = "product_card"
    //     0xaf18ec: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xaf18f0: ldr             x3, [x3, #0xc28]
    // 0xaf18f4: b               #0xaf1a8c
    // 0xaf18f8: ldur            x1, [fp, #-8]
    // 0xaf18fc: LoadField: r0 = r1->field_f
    //     0xaf18fc: ldur            w0, [x1, #0xf]
    // 0xaf1900: DecompressPointer r0
    //     0xaf1900: add             x0, x0, HEAP, lsl #32
    // 0xaf1904: LoadField: r2 = r0->field_b
    //     0xaf1904: ldur            w2, [x0, #0xb]
    // 0xaf1908: DecompressPointer r2
    //     0xaf1908: add             x2, x2, HEAP, lsl #32
    // 0xaf190c: cmp             w2, NULL
    // 0xaf1910: b.eq            #0xaf2bc8
    // 0xaf1914: LoadField: r0 = r2->field_f
    //     0xaf1914: ldur            w0, [x2, #0xf]
    // 0xaf1918: DecompressPointer r0
    //     0xaf1918: add             x0, x0, HEAP, lsl #32
    // 0xaf191c: r2 = LoadClassIdInstr(r0)
    //     0xaf191c: ldur            x2, [x0, #-1]
    //     0xaf1920: ubfx            x2, x2, #0xc, #0x14
    // 0xaf1924: r16 = "collection_page"
    //     0xaf1924: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xaf1928: ldr             x16, [x16, #0x118]
    // 0xaf192c: stp             x16, x0, [SP]
    // 0xaf1930: mov             x0, x2
    // 0xaf1934: mov             lr, x0
    // 0xaf1938: ldr             lr, [x21, lr, lsl #3]
    // 0xaf193c: blr             lr
    // 0xaf1940: tbnz            w0, #4, #0xaf1980
    // 0xaf1944: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf1944: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf1948: ldr             x0, [x0, #0x1c80]
    //     0xaf194c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf1950: cmp             w0, w16
    //     0xaf1954: b.ne            #0xaf1960
    //     0xaf1958: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaf195c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf1960: r16 = <CollectionsController>
    //     0xaf1960: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xaf1964: ldr             x16, [x16, #0xb00]
    // 0xaf1968: str             x16, [SP]
    // 0xaf196c: r4 = const [0x1, 0, 0, 0, null]
    //     0xaf196c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaf1970: r0 = Inst.find()
    //     0xaf1970: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xaf1974: r0 = "collection_page"
    //     0xaf1974: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xaf1978: ldr             x0, [x0, #0x118]
    // 0xaf197c: b               #0xaf1a88
    // 0xaf1980: ldur            x1, [fp, #-8]
    // 0xaf1984: LoadField: r0 = r1->field_f
    //     0xaf1984: ldur            w0, [x1, #0xf]
    // 0xaf1988: DecompressPointer r0
    //     0xaf1988: add             x0, x0, HEAP, lsl #32
    // 0xaf198c: LoadField: r2 = r0->field_b
    //     0xaf198c: ldur            w2, [x0, #0xb]
    // 0xaf1990: DecompressPointer r2
    //     0xaf1990: add             x2, x2, HEAP, lsl #32
    // 0xaf1994: cmp             w2, NULL
    // 0xaf1998: b.eq            #0xaf2bcc
    // 0xaf199c: LoadField: r0 = r2->field_f
    //     0xaf199c: ldur            w0, [x2, #0xf]
    // 0xaf19a0: DecompressPointer r0
    //     0xaf19a0: add             x0, x0, HEAP, lsl #32
    // 0xaf19a4: r2 = LoadClassIdInstr(r0)
    //     0xaf19a4: ldur            x2, [x0, #-1]
    //     0xaf19a8: ubfx            x2, x2, #0xc, #0x14
    // 0xaf19ac: r16 = "order_success_page"
    //     0xaf19ac: add             x16, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xaf19b0: ldr             x16, [x16, #0x1c0]
    // 0xaf19b4: stp             x16, x0, [SP]
    // 0xaf19b8: mov             x0, x2
    // 0xaf19bc: mov             lr, x0
    // 0xaf19c0: ldr             lr, [x21, lr, lsl #3]
    // 0xaf19c4: blr             lr
    // 0xaf19c8: tbnz            w0, #4, #0xaf1a50
    // 0xaf19cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf19cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf19d0: ldr             x0, [x0, #0x1c80]
    //     0xaf19d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf19d8: cmp             w0, w16
    //     0xaf19dc: b.ne            #0xaf19e8
    //     0xaf19e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaf19e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf19e8: r16 = <OrderSuccessController>
    //     0xaf19e8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c878] TypeArguments: <OrderSuccessController>
    //     0xaf19ec: ldr             x16, [x16, #0x878]
    // 0xaf19f0: str             x16, [SP]
    // 0xaf19f4: r4 = const [0x1, 0, 0, 0, null]
    //     0xaf19f4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaf19f8: r0 = Inst.find()
    //     0xaf19f8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xaf19fc: stur            x0, [fp, #-0x10]
    // 0xaf1a00: r16 = <ProductDetailController>
    //     0xaf1a00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xaf1a04: ldr             x16, [x16, #0xde0]
    // 0xaf1a08: str             x16, [SP]
    // 0xaf1a0c: r4 = const [0x1, 0, 0, 0, null]
    //     0xaf1a0c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaf1a10: r0 = Inst.delete()
    //     0xaf1a10: bl              #0x9c7d48  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.delete
    // 0xaf1a14: r16 = <Type>
    //     0xaf1a14: ldr             x16, [PP, #0x4b40]  ; [pp+0x4b40] TypeArguments: <Type>
    // 0xaf1a18: r30 = ProductDetailController
    //     0xaf1a18: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ede8] Type: ProductDetailController
    //     0xaf1a1c: ldr             lr, [lr, #0xde8]
    // 0xaf1a20: stp             lr, x16, [SP]
    // 0xaf1a24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf1a24: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf1a28: r0 = Inst.put()
    //     0xaf1a28: bl              #0xa5f404  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.put
    // 0xaf1a2c: ldur            x0, [fp, #-0x10]
    // 0xaf1a30: r17 = 283
    //     0xaf1a30: movz            x17, #0x11b
    // 0xaf1a34: ldr             w1, [x0, x17]
    // 0xaf1a38: DecompressPointer r1
    //     0xaf1a38: add             x1, x1, HEAP, lsl #32
    // 0xaf1a3c: r2 = false
    //     0xaf1a3c: add             x2, NULL, #0x30  ; false
    // 0xaf1a40: r0 = value=()
    //     0xaf1a40: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf1a44: r0 = "order_success_page"
    //     0xaf1a44: add             x0, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xaf1a48: ldr             x0, [x0, #0x1c0]
    // 0xaf1a4c: b               #0xaf1a88
    // 0xaf1a50: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf1a50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf1a54: ldr             x0, [x0, #0x1c80]
    //     0xaf1a58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf1a5c: cmp             w0, w16
    //     0xaf1a60: b.ne            #0xaf1a6c
    //     0xaf1a64: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaf1a68: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf1a6c: r16 = <HomeController>
    //     0xaf1a6c: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xaf1a70: ldr             x16, [x16, #0xf98]
    // 0xaf1a74: str             x16, [SP]
    // 0xaf1a78: r4 = const [0x1, 0, 0, 0, null]
    //     0xaf1a78: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaf1a7c: r0 = Inst.find()
    //     0xaf1a7c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xaf1a80: r0 = "home_page"
    //     0xaf1a80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xaf1a84: ldr             x0, [x0, #0xe60]
    // 0xaf1a88: mov             x3, x0
    // 0xaf1a8c: ldur            x2, [fp, #-8]
    // 0xaf1a90: stur            x3, [fp, #-0x30]
    // 0xaf1a94: LoadField: r4 = r2->field_f
    //     0xaf1a94: ldur            w4, [x2, #0xf]
    // 0xaf1a98: DecompressPointer r4
    //     0xaf1a98: add             x4, x4, HEAP, lsl #32
    // 0xaf1a9c: LoadField: r5 = r4->field_b
    //     0xaf1a9c: ldur            w5, [x4, #0xb]
    // 0xaf1aa0: DecompressPointer r5
    //     0xaf1aa0: add             x5, x5, HEAP, lsl #32
    // 0xaf1aa4: stur            x5, [fp, #-0x28]
    // 0xaf1aa8: cmp             w5, NULL
    // 0xaf1aac: b.eq            #0xaf2bd0
    // 0xaf1ab0: LoadField: r6 = r5->field_f
    //     0xaf1ab0: ldur            w6, [x5, #0xf]
    // 0xaf1ab4: DecompressPointer r6
    //     0xaf1ab4: add             x6, x6, HEAP, lsl #32
    // 0xaf1ab8: stur            x6, [fp, #-0x20]
    // 0xaf1abc: LoadField: r0 = r5->field_b
    //     0xaf1abc: ldur            w0, [x5, #0xb]
    // 0xaf1ac0: DecompressPointer r0
    //     0xaf1ac0: add             x0, x0, HEAP, lsl #32
    // 0xaf1ac4: LoadField: r7 = r0->field_6f
    //     0xaf1ac4: ldur            w7, [x0, #0x6f]
    // 0xaf1ac8: DecompressPointer r7
    //     0xaf1ac8: add             x7, x7, HEAP, lsl #32
    // 0xaf1acc: cmp             w7, NULL
    // 0xaf1ad0: b.ne            #0xaf1adc
    // 0xaf1ad4: r8 = Null
    //     0xaf1ad4: mov             x8, NULL
    // 0xaf1ad8: b               #0xaf1b18
    // 0xaf1adc: ArrayLoad: r8 = r4[0]  ; List_8
    //     0xaf1adc: ldur            x8, [x4, #0x17]
    // 0xaf1ae0: LoadField: r0 = r7->field_b
    //     0xaf1ae0: ldur            w0, [x7, #0xb]
    // 0xaf1ae4: r1 = LoadInt32Instr(r0)
    //     0xaf1ae4: sbfx            x1, x0, #1, #0x1f
    // 0xaf1ae8: mov             x0, x1
    // 0xaf1aec: mov             x1, x8
    // 0xaf1af0: cmp             x1, x0
    // 0xaf1af4: b.hs            #0xaf2bd4
    // 0xaf1af8: LoadField: r0 = r7->field_f
    //     0xaf1af8: ldur            w0, [x7, #0xf]
    // 0xaf1afc: DecompressPointer r0
    //     0xaf1afc: add             x0, x0, HEAP, lsl #32
    // 0xaf1b00: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xaf1b00: add             x16, x0, x8, lsl #2
    //     0xaf1b04: ldur            w1, [x16, #0xf]
    // 0xaf1b08: DecompressPointer r1
    //     0xaf1b08: add             x1, x1, HEAP, lsl #32
    // 0xaf1b0c: LoadField: r0 = r1->field_f
    //     0xaf1b0c: ldur            w0, [x1, #0xf]
    // 0xaf1b10: DecompressPointer r0
    //     0xaf1b10: add             x0, x0, HEAP, lsl #32
    // 0xaf1b14: mov             x8, x0
    // 0xaf1b18: stur            x8, [fp, #-0x18]
    // 0xaf1b1c: cmp             w7, NULL
    // 0xaf1b20: b.ne            #0xaf1b2c
    // 0xaf1b24: r9 = Null
    //     0xaf1b24: mov             x9, NULL
    // 0xaf1b28: b               #0xaf1b68
    // 0xaf1b2c: ArrayLoad: r9 = r4[0]  ; List_8
    //     0xaf1b2c: ldur            x9, [x4, #0x17]
    // 0xaf1b30: LoadField: r0 = r7->field_b
    //     0xaf1b30: ldur            w0, [x7, #0xb]
    // 0xaf1b34: r1 = LoadInt32Instr(r0)
    //     0xaf1b34: sbfx            x1, x0, #1, #0x1f
    // 0xaf1b38: mov             x0, x1
    // 0xaf1b3c: mov             x1, x9
    // 0xaf1b40: cmp             x1, x0
    // 0xaf1b44: b.hs            #0xaf2bd8
    // 0xaf1b48: LoadField: r0 = r7->field_f
    //     0xaf1b48: ldur            w0, [x7, #0xf]
    // 0xaf1b4c: DecompressPointer r0
    //     0xaf1b4c: add             x0, x0, HEAP, lsl #32
    // 0xaf1b50: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xaf1b50: add             x16, x0, x9, lsl #2
    //     0xaf1b54: ldur            w1, [x16, #0xf]
    // 0xaf1b58: DecompressPointer r1
    //     0xaf1b58: add             x1, x1, HEAP, lsl #32
    // 0xaf1b5c: LoadField: r0 = r1->field_b
    //     0xaf1b5c: ldur            w0, [x1, #0xb]
    // 0xaf1b60: DecompressPointer r0
    //     0xaf1b60: add             x0, x0, HEAP, lsl #32
    // 0xaf1b64: mov             x9, x0
    // 0xaf1b68: stur            x9, [fp, #-0x10]
    // 0xaf1b6c: cmp             w7, NULL
    // 0xaf1b70: b.ne            #0xaf1b94
    // 0xaf1b74: mov             x0, x2
    // 0xaf1b78: mov             x1, x3
    // 0xaf1b7c: mov             x3, x6
    // 0xaf1b80: mov             x4, x8
    // 0xaf1b84: mov             x2, x5
    // 0xaf1b88: mov             x5, x9
    // 0xaf1b8c: r6 = Null
    //     0xaf1b8c: mov             x6, NULL
    // 0xaf1b90: b               #0xaf1c00
    // 0xaf1b94: ArrayLoad: r10 = r4[0]  ; List_8
    //     0xaf1b94: ldur            x10, [x4, #0x17]
    // 0xaf1b98: LoadField: r0 = r7->field_b
    //     0xaf1b98: ldur            w0, [x7, #0xb]
    // 0xaf1b9c: r1 = LoadInt32Instr(r0)
    //     0xaf1b9c: sbfx            x1, x0, #1, #0x1f
    // 0xaf1ba0: mov             x0, x1
    // 0xaf1ba4: mov             x1, x10
    // 0xaf1ba8: cmp             x1, x0
    // 0xaf1bac: b.hs            #0xaf2bdc
    // 0xaf1bb0: LoadField: r0 = r7->field_f
    //     0xaf1bb0: ldur            w0, [x7, #0xf]
    // 0xaf1bb4: DecompressPointer r0
    //     0xaf1bb4: add             x0, x0, HEAP, lsl #32
    // 0xaf1bb8: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0xaf1bb8: add             x16, x0, x10, lsl #2
    //     0xaf1bbc: ldur            w1, [x16, #0xf]
    // 0xaf1bc0: DecompressPointer r1
    //     0xaf1bc0: add             x1, x1, HEAP, lsl #32
    // 0xaf1bc4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf1bc4: ldur            w0, [x1, #0x17]
    // 0xaf1bc8: DecompressPointer r0
    //     0xaf1bc8: add             x0, x0, HEAP, lsl #32
    // 0xaf1bcc: cmp             w0, NULL
    // 0xaf1bd0: b.ne            #0xaf1bdc
    // 0xaf1bd4: r0 = Null
    //     0xaf1bd4: mov             x0, NULL
    // 0xaf1bd8: b               #0xaf1be4
    // 0xaf1bdc: stp             x0, NULL, [SP]
    // 0xaf1be0: r0 = _Double.fromInteger()
    //     0xaf1be0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xaf1be4: mov             x6, x0
    // 0xaf1be8: ldur            x0, [fp, #-8]
    // 0xaf1bec: ldur            x1, [fp, #-0x30]
    // 0xaf1bf0: ldur            x3, [fp, #-0x20]
    // 0xaf1bf4: ldur            x4, [fp, #-0x18]
    // 0xaf1bf8: ldur            x5, [fp, #-0x10]
    // 0xaf1bfc: ldur            x2, [fp, #-0x28]
    // 0xaf1c00: stur            x6, [fp, #-0x38]
    // 0xaf1c04: r0 = EventData()
    //     0xaf1c04: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xaf1c08: mov             x1, x0
    // 0xaf1c0c: ldur            x0, [fp, #-0x20]
    // 0xaf1c10: stur            x1, [fp, #-0x40]
    // 0xaf1c14: StoreField: r1->field_13 = r0
    //     0xaf1c14: stur            w0, [x1, #0x13]
    // 0xaf1c18: ldur            x0, [fp, #-0x38]
    // 0xaf1c1c: StoreField: r1->field_2f = r0
    //     0xaf1c1c: stur            w0, [x1, #0x2f]
    // 0xaf1c20: ldur            x0, [fp, #-0x18]
    // 0xaf1c24: StoreField: r1->field_33 = r0
    //     0xaf1c24: stur            w0, [x1, #0x33]
    // 0xaf1c28: ldur            x0, [fp, #-0x30]
    // 0xaf1c2c: StoreField: r1->field_3b = r0
    //     0xaf1c2c: stur            w0, [x1, #0x3b]
    // 0xaf1c30: StoreField: r1->field_87 = r0
    //     0xaf1c30: stur            w0, [x1, #0x87]
    // 0xaf1c34: ldur            x0, [fp, #-0x10]
    // 0xaf1c38: StoreField: r1->field_8f = r0
    //     0xaf1c38: stur            w0, [x1, #0x8f]
    // 0xaf1c3c: r0 = EventsRequest()
    //     0xaf1c3c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xaf1c40: mov             x1, x0
    // 0xaf1c44: r0 = "add_to_bag_clicked"
    //     0xaf1c44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xaf1c48: ldr             x0, [x0, #0xec8]
    // 0xaf1c4c: StoreField: r1->field_7 = r0
    //     0xaf1c4c: stur            w0, [x1, #7]
    // 0xaf1c50: ldur            x0, [fp, #-0x40]
    // 0xaf1c54: StoreField: r1->field_b = r0
    //     0xaf1c54: stur            w0, [x1, #0xb]
    // 0xaf1c58: ldur            x0, [fp, #-0x28]
    // 0xaf1c5c: LoadField: r2 = r0->field_13
    //     0xaf1c5c: ldur            w2, [x0, #0x13]
    // 0xaf1c60: DecompressPointer r2
    //     0xaf1c60: add             x2, x2, HEAP, lsl #32
    // 0xaf1c64: stp             x1, x2, [SP]
    // 0xaf1c68: r4 = 0
    //     0xaf1c68: movz            x4, #0
    // 0xaf1c6c: ldr             x0, [SP, #8]
    // 0xaf1c70: r16 = UnlinkedCall_0x613b5c
    //     0xaf1c70: add             x16, PP, #0x58, lsl #12  ; [pp+0x58658] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaf1c74: add             x16, x16, #0x658
    // 0xaf1c78: ldp             x5, lr, [x16]
    // 0xaf1c7c: blr             lr
    // 0xaf1c80: ldur            x2, [fp, #-8]
    // 0xaf1c84: LoadField: r3 = r2->field_f
    //     0xaf1c84: ldur            w3, [x2, #0xf]
    // 0xaf1c88: DecompressPointer r3
    //     0xaf1c88: add             x3, x3, HEAP, lsl #32
    // 0xaf1c8c: LoadField: r4 = r3->field_b
    //     0xaf1c8c: ldur            w4, [x3, #0xb]
    // 0xaf1c90: DecompressPointer r4
    //     0xaf1c90: add             x4, x4, HEAP, lsl #32
    // 0xaf1c94: stur            x4, [fp, #-0x18]
    // 0xaf1c98: cmp             w4, NULL
    // 0xaf1c9c: b.eq            #0xaf2be0
    // 0xaf1ca0: LoadField: r0 = r4->field_b
    //     0xaf1ca0: ldur            w0, [x4, #0xb]
    // 0xaf1ca4: DecompressPointer r0
    //     0xaf1ca4: add             x0, x0, HEAP, lsl #32
    // 0xaf1ca8: LoadField: r1 = r0->field_8b
    //     0xaf1ca8: ldur            w1, [x0, #0x8b]
    // 0xaf1cac: DecompressPointer r1
    //     0xaf1cac: add             x1, x1, HEAP, lsl #32
    // 0xaf1cb0: cmp             w1, NULL
    // 0xaf1cb4: b.eq            #0xaf2388
    // 0xaf1cb8: tbnz            w1, #4, #0xaf2388
    // 0xaf1cbc: LoadField: r1 = r0->field_87
    //     0xaf1cbc: ldur            w1, [x0, #0x87]
    // 0xaf1cc0: DecompressPointer r1
    //     0xaf1cc0: add             x1, x1, HEAP, lsl #32
    // 0xaf1cc4: cmp             w1, NULL
    // 0xaf1cc8: b.eq            #0xaf202c
    // 0xaf1ccc: tbnz            w1, #4, #0xaf202c
    // 0xaf1cd0: LoadField: r2 = r0->field_73
    //     0xaf1cd0: ldur            w2, [x0, #0x73]
    // 0xaf1cd4: DecompressPointer r2
    //     0xaf1cd4: add             x2, x2, HEAP, lsl #32
    // 0xaf1cd8: cmp             w2, NULL
    // 0xaf1cdc: b.ne            #0xaf1ce8
    // 0xaf1ce0: r1 = Null
    //     0xaf1ce0: mov             x1, NULL
    // 0xaf1ce4: b               #0xaf1d00
    // 0xaf1ce8: LoadField: r1 = r2->field_b
    //     0xaf1ce8: ldur            w1, [x2, #0xb]
    // 0xaf1cec: cbz             w1, #0xaf1cf8
    // 0xaf1cf0: r5 = false
    //     0xaf1cf0: add             x5, NULL, #0x30  ; false
    // 0xaf1cf4: b               #0xaf1cfc
    // 0xaf1cf8: r5 = true
    //     0xaf1cf8: add             x5, NULL, #0x20  ; true
    // 0xaf1cfc: mov             x1, x5
    // 0xaf1d00: cmp             w1, NULL
    // 0xaf1d04: b.eq            #0xaf1d0c
    // 0xaf1d08: tbnz            w1, #4, #0xaf1ea0
    // 0xaf1d0c: LoadField: r1 = r0->field_8f
    //     0xaf1d0c: ldur            w1, [x0, #0x8f]
    // 0xaf1d10: DecompressPointer r1
    //     0xaf1d10: add             x1, x1, HEAP, lsl #32
    // 0xaf1d14: cmp             w1, NULL
    // 0xaf1d18: b.ne            #0xaf1d24
    // 0xaf1d1c: r2 = ""
    //     0xaf1d1c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1d20: b               #0xaf1d28
    // 0xaf1d24: mov             x2, x1
    // 0xaf1d28: LoadField: r5 = r0->field_6f
    //     0xaf1d28: ldur            w5, [x0, #0x6f]
    // 0xaf1d2c: DecompressPointer r5
    //     0xaf1d2c: add             x5, x5, HEAP, lsl #32
    // 0xaf1d30: cmp             w5, NULL
    // 0xaf1d34: b.ne            #0xaf1d40
    // 0xaf1d38: r0 = Null
    //     0xaf1d38: mov             x0, NULL
    // 0xaf1d3c: b               #0xaf1d78
    // 0xaf1d40: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf1d40: ldur            x6, [x3, #0x17]
    // 0xaf1d44: LoadField: r0 = r5->field_b
    //     0xaf1d44: ldur            w0, [x5, #0xb]
    // 0xaf1d48: r1 = LoadInt32Instr(r0)
    //     0xaf1d48: sbfx            x1, x0, #1, #0x1f
    // 0xaf1d4c: mov             x0, x1
    // 0xaf1d50: mov             x1, x6
    // 0xaf1d54: cmp             x1, x0
    // 0xaf1d58: b.hs            #0xaf2be4
    // 0xaf1d5c: LoadField: r0 = r5->field_f
    //     0xaf1d5c: ldur            w0, [x5, #0xf]
    // 0xaf1d60: DecompressPointer r0
    //     0xaf1d60: add             x0, x0, HEAP, lsl #32
    // 0xaf1d64: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf1d64: add             x16, x0, x6, lsl #2
    //     0xaf1d68: ldur            w1, [x16, #0xf]
    // 0xaf1d6c: DecompressPointer r1
    //     0xaf1d6c: add             x1, x1, HEAP, lsl #32
    // 0xaf1d70: LoadField: r0 = r1->field_f
    //     0xaf1d70: ldur            w0, [x1, #0xf]
    // 0xaf1d74: DecompressPointer r0
    //     0xaf1d74: add             x0, x0, HEAP, lsl #32
    // 0xaf1d78: cmp             w0, NULL
    // 0xaf1d7c: b.ne            #0xaf1d88
    // 0xaf1d80: r6 = ""
    //     0xaf1d80: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1d84: b               #0xaf1d8c
    // 0xaf1d88: mov             x6, x0
    // 0xaf1d8c: cmp             w5, NULL
    // 0xaf1d90: b.ne            #0xaf1d9c
    // 0xaf1d94: r0 = Null
    //     0xaf1d94: mov             x0, NULL
    // 0xaf1d98: b               #0xaf1dd4
    // 0xaf1d9c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf1d9c: ldur            x7, [x3, #0x17]
    // 0xaf1da0: LoadField: r0 = r5->field_b
    //     0xaf1da0: ldur            w0, [x5, #0xb]
    // 0xaf1da4: r1 = LoadInt32Instr(r0)
    //     0xaf1da4: sbfx            x1, x0, #1, #0x1f
    // 0xaf1da8: mov             x0, x1
    // 0xaf1dac: mov             x1, x7
    // 0xaf1db0: cmp             x1, x0
    // 0xaf1db4: b.hs            #0xaf2be8
    // 0xaf1db8: LoadField: r0 = r5->field_f
    //     0xaf1db8: ldur            w0, [x5, #0xf]
    // 0xaf1dbc: DecompressPointer r0
    //     0xaf1dbc: add             x0, x0, HEAP, lsl #32
    // 0xaf1dc0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf1dc0: add             x16, x0, x7, lsl #2
    //     0xaf1dc4: ldur            w1, [x16, #0xf]
    // 0xaf1dc8: DecompressPointer r1
    //     0xaf1dc8: add             x1, x1, HEAP, lsl #32
    // 0xaf1dcc: LoadField: r0 = r1->field_b
    //     0xaf1dcc: ldur            w0, [x1, #0xb]
    // 0xaf1dd0: DecompressPointer r0
    //     0xaf1dd0: add             x0, x0, HEAP, lsl #32
    // 0xaf1dd4: cmp             w0, NULL
    // 0xaf1dd8: b.ne            #0xaf1de4
    // 0xaf1ddc: r7 = ""
    //     0xaf1ddc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1de0: b               #0xaf1de8
    // 0xaf1de4: mov             x7, x0
    // 0xaf1de8: cmp             w5, NULL
    // 0xaf1dec: b.ne            #0xaf1df8
    // 0xaf1df0: r0 = Null
    //     0xaf1df0: mov             x0, NULL
    // 0xaf1df4: b               #0xaf1e30
    // 0xaf1df8: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xaf1df8: ldur            x8, [x3, #0x17]
    // 0xaf1dfc: LoadField: r0 = r5->field_b
    //     0xaf1dfc: ldur            w0, [x5, #0xb]
    // 0xaf1e00: r1 = LoadInt32Instr(r0)
    //     0xaf1e00: sbfx            x1, x0, #1, #0x1f
    // 0xaf1e04: mov             x0, x1
    // 0xaf1e08: mov             x1, x8
    // 0xaf1e0c: cmp             x1, x0
    // 0xaf1e10: b.hs            #0xaf2bec
    // 0xaf1e14: LoadField: r0 = r5->field_f
    //     0xaf1e14: ldur            w0, [x5, #0xf]
    // 0xaf1e18: DecompressPointer r0
    //     0xaf1e18: add             x0, x0, HEAP, lsl #32
    // 0xaf1e1c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xaf1e1c: add             x16, x0, x8, lsl #2
    //     0xaf1e20: ldur            w1, [x16, #0xf]
    // 0xaf1e24: DecompressPointer r1
    //     0xaf1e24: add             x1, x1, HEAP, lsl #32
    // 0xaf1e28: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf1e28: ldur            w0, [x1, #0x17]
    // 0xaf1e2c: DecompressPointer r0
    //     0xaf1e2c: add             x0, x0, HEAP, lsl #32
    // 0xaf1e30: cmp             w0, NULL
    // 0xaf1e34: b.ne            #0xaf1e40
    // 0xaf1e38: r3 = 0
    //     0xaf1e38: movz            x3, #0
    // 0xaf1e3c: b               #0xaf1e50
    // 0xaf1e40: r1 = LoadInt32Instr(r0)
    //     0xaf1e40: sbfx            x1, x0, #1, #0x1f
    //     0xaf1e44: tbz             w0, #0, #0xaf1e4c
    //     0xaf1e48: ldur            x1, [x0, #7]
    // 0xaf1e4c: mov             x3, x1
    // 0xaf1e50: LoadField: r5 = r4->field_1f
    //     0xaf1e50: ldur            w5, [x4, #0x1f]
    // 0xaf1e54: DecompressPointer r5
    //     0xaf1e54: add             x5, x5, HEAP, lsl #32
    // 0xaf1e58: r0 = BoxInt64Instr(r3)
    //     0xaf1e58: sbfiz           x0, x3, #1, #0x1f
    //     0xaf1e5c: cmp             x3, x0, asr #1
    //     0xaf1e60: b.eq            #0xaf1e6c
    //     0xaf1e64: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf1e68: stur            x3, [x0, #7]
    // 0xaf1e6c: stp             x2, x5, [SP, #0x28]
    // 0xaf1e70: r16 = true
    //     0xaf1e70: add             x16, NULL, #0x20  ; true
    // 0xaf1e74: stp             x6, x16, [SP, #0x18]
    // 0xaf1e78: stp             x0, x7, [SP, #8]
    // 0xaf1e7c: r16 = "add_to_bag"
    //     0xaf1e7c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xaf1e80: ldr             x16, [x16, #0xa38]
    // 0xaf1e84: str             x16, [SP]
    // 0xaf1e88: mov             x0, x5
    // 0xaf1e8c: ClosureCall
    //     0xaf1e8c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xaf1e90: ldr             x4, [x4, #0x330]
    //     0xaf1e94: ldur            x2, [x0, #0x1f]
    //     0xaf1e98: blr             x2
    // 0xaf1e9c: b               #0xaf2bac
    // 0xaf1ea0: LoadField: r1 = r0->field_8f
    //     0xaf1ea0: ldur            w1, [x0, #0x8f]
    // 0xaf1ea4: DecompressPointer r1
    //     0xaf1ea4: add             x1, x1, HEAP, lsl #32
    // 0xaf1ea8: cmp             w1, NULL
    // 0xaf1eac: b.ne            #0xaf1eb8
    // 0xaf1eb0: r5 = ""
    //     0xaf1eb0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1eb4: b               #0xaf1ebc
    // 0xaf1eb8: mov             x5, x1
    // 0xaf1ebc: cmp             w2, NULL
    // 0xaf1ec0: b.ne            #0xaf1ecc
    // 0xaf1ec4: r0 = Null
    //     0xaf1ec4: mov             x0, NULL
    // 0xaf1ec8: b               #0xaf1f04
    // 0xaf1ecc: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf1ecc: ldur            x6, [x3, #0x17]
    // 0xaf1ed0: LoadField: r0 = r2->field_b
    //     0xaf1ed0: ldur            w0, [x2, #0xb]
    // 0xaf1ed4: r1 = LoadInt32Instr(r0)
    //     0xaf1ed4: sbfx            x1, x0, #1, #0x1f
    // 0xaf1ed8: mov             x0, x1
    // 0xaf1edc: mov             x1, x6
    // 0xaf1ee0: cmp             x1, x0
    // 0xaf1ee4: b.hs            #0xaf2bf0
    // 0xaf1ee8: LoadField: r0 = r2->field_f
    //     0xaf1ee8: ldur            w0, [x2, #0xf]
    // 0xaf1eec: DecompressPointer r0
    //     0xaf1eec: add             x0, x0, HEAP, lsl #32
    // 0xaf1ef0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf1ef0: add             x16, x0, x6, lsl #2
    //     0xaf1ef4: ldur            w1, [x16, #0xf]
    // 0xaf1ef8: DecompressPointer r1
    //     0xaf1ef8: add             x1, x1, HEAP, lsl #32
    // 0xaf1efc: LoadField: r0 = r1->field_b
    //     0xaf1efc: ldur            w0, [x1, #0xb]
    // 0xaf1f00: DecompressPointer r0
    //     0xaf1f00: add             x0, x0, HEAP, lsl #32
    // 0xaf1f04: cmp             w0, NULL
    // 0xaf1f08: b.ne            #0xaf1f14
    // 0xaf1f0c: r6 = ""
    //     0xaf1f0c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1f10: b               #0xaf1f18
    // 0xaf1f14: mov             x6, x0
    // 0xaf1f18: cmp             w2, NULL
    // 0xaf1f1c: b.ne            #0xaf1f28
    // 0xaf1f20: r0 = Null
    //     0xaf1f20: mov             x0, NULL
    // 0xaf1f24: b               #0xaf1f60
    // 0xaf1f28: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf1f28: ldur            x7, [x3, #0x17]
    // 0xaf1f2c: LoadField: r0 = r2->field_b
    //     0xaf1f2c: ldur            w0, [x2, #0xb]
    // 0xaf1f30: r1 = LoadInt32Instr(r0)
    //     0xaf1f30: sbfx            x1, x0, #1, #0x1f
    // 0xaf1f34: mov             x0, x1
    // 0xaf1f38: mov             x1, x7
    // 0xaf1f3c: cmp             x1, x0
    // 0xaf1f40: b.hs            #0xaf2bf4
    // 0xaf1f44: LoadField: r0 = r2->field_f
    //     0xaf1f44: ldur            w0, [x2, #0xf]
    // 0xaf1f48: DecompressPointer r0
    //     0xaf1f48: add             x0, x0, HEAP, lsl #32
    // 0xaf1f4c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf1f4c: add             x16, x0, x7, lsl #2
    //     0xaf1f50: ldur            w1, [x16, #0xf]
    // 0xaf1f54: DecompressPointer r1
    //     0xaf1f54: add             x1, x1, HEAP, lsl #32
    // 0xaf1f58: LoadField: r0 = r1->field_7
    //     0xaf1f58: ldur            w0, [x1, #7]
    // 0xaf1f5c: DecompressPointer r0
    //     0xaf1f5c: add             x0, x0, HEAP, lsl #32
    // 0xaf1f60: cmp             w0, NULL
    // 0xaf1f64: b.ne            #0xaf1f70
    // 0xaf1f68: r7 = ""
    //     0xaf1f68: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf1f6c: b               #0xaf1f74
    // 0xaf1f70: mov             x7, x0
    // 0xaf1f74: cmp             w2, NULL
    // 0xaf1f78: b.ne            #0xaf1f84
    // 0xaf1f7c: r0 = Null
    //     0xaf1f7c: mov             x0, NULL
    // 0xaf1f80: b               #0xaf1fbc
    // 0xaf1f84: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xaf1f84: ldur            x8, [x3, #0x17]
    // 0xaf1f88: LoadField: r0 = r2->field_b
    //     0xaf1f88: ldur            w0, [x2, #0xb]
    // 0xaf1f8c: r1 = LoadInt32Instr(r0)
    //     0xaf1f8c: sbfx            x1, x0, #1, #0x1f
    // 0xaf1f90: mov             x0, x1
    // 0xaf1f94: mov             x1, x8
    // 0xaf1f98: cmp             x1, x0
    // 0xaf1f9c: b.hs            #0xaf2bf8
    // 0xaf1fa0: LoadField: r0 = r2->field_f
    //     0xaf1fa0: ldur            w0, [x2, #0xf]
    // 0xaf1fa4: DecompressPointer r0
    //     0xaf1fa4: add             x0, x0, HEAP, lsl #32
    // 0xaf1fa8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xaf1fa8: add             x16, x0, x8, lsl #2
    //     0xaf1fac: ldur            w1, [x16, #0xf]
    // 0xaf1fb0: DecompressPointer r1
    //     0xaf1fb0: add             x1, x1, HEAP, lsl #32
    // 0xaf1fb4: LoadField: r0 = r1->field_1b
    //     0xaf1fb4: ldur            w0, [x1, #0x1b]
    // 0xaf1fb8: DecompressPointer r0
    //     0xaf1fb8: add             x0, x0, HEAP, lsl #32
    // 0xaf1fbc: cmp             w0, NULL
    // 0xaf1fc0: b.ne            #0xaf1fcc
    // 0xaf1fc4: r2 = 0
    //     0xaf1fc4: movz            x2, #0
    // 0xaf1fc8: b               #0xaf1fdc
    // 0xaf1fcc: r1 = LoadInt32Instr(r0)
    //     0xaf1fcc: sbfx            x1, x0, #1, #0x1f
    //     0xaf1fd0: tbz             w0, #0, #0xaf1fd8
    //     0xaf1fd4: ldur            x1, [x0, #7]
    // 0xaf1fd8: mov             x2, x1
    // 0xaf1fdc: LoadField: r3 = r4->field_1f
    //     0xaf1fdc: ldur            w3, [x4, #0x1f]
    // 0xaf1fe0: DecompressPointer r3
    //     0xaf1fe0: add             x3, x3, HEAP, lsl #32
    // 0xaf1fe4: r0 = BoxInt64Instr(r2)
    //     0xaf1fe4: sbfiz           x0, x2, #1, #0x1f
    //     0xaf1fe8: cmp             x2, x0, asr #1
    //     0xaf1fec: b.eq            #0xaf1ff8
    //     0xaf1ff0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf1ff4: stur            x2, [x0, #7]
    // 0xaf1ff8: stp             x5, x3, [SP, #0x28]
    // 0xaf1ffc: r16 = true
    //     0xaf1ffc: add             x16, NULL, #0x20  ; true
    // 0xaf2000: stp             x6, x16, [SP, #0x18]
    // 0xaf2004: stp             x0, x7, [SP, #8]
    // 0xaf2008: r16 = "add_to_bag"
    //     0xaf2008: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xaf200c: ldr             x16, [x16, #0xa38]
    // 0xaf2010: str             x16, [SP]
    // 0xaf2014: mov             x0, x3
    // 0xaf2018: ClosureCall
    //     0xaf2018: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xaf201c: ldr             x4, [x4, #0x330]
    //     0xaf2020: ldur            x2, [x0, #0x1f]
    //     0xaf2024: blr             x2
    // 0xaf2028: b               #0xaf2bac
    // 0xaf202c: LoadField: r2 = r0->field_73
    //     0xaf202c: ldur            w2, [x0, #0x73]
    // 0xaf2030: DecompressPointer r2
    //     0xaf2030: add             x2, x2, HEAP, lsl #32
    // 0xaf2034: cmp             w2, NULL
    // 0xaf2038: b.ne            #0xaf2044
    // 0xaf203c: r1 = Null
    //     0xaf203c: mov             x1, NULL
    // 0xaf2040: b               #0xaf205c
    // 0xaf2044: LoadField: r1 = r2->field_b
    //     0xaf2044: ldur            w1, [x2, #0xb]
    // 0xaf2048: cbz             w1, #0xaf2054
    // 0xaf204c: r5 = false
    //     0xaf204c: add             x5, NULL, #0x30  ; false
    // 0xaf2050: b               #0xaf2058
    // 0xaf2054: r5 = true
    //     0xaf2054: add             x5, NULL, #0x20  ; true
    // 0xaf2058: mov             x1, x5
    // 0xaf205c: cmp             w1, NULL
    // 0xaf2060: b.eq            #0xaf2068
    // 0xaf2064: tbnz            w1, #4, #0xaf21fc
    // 0xaf2068: LoadField: r1 = r0->field_8f
    //     0xaf2068: ldur            w1, [x0, #0x8f]
    // 0xaf206c: DecompressPointer r1
    //     0xaf206c: add             x1, x1, HEAP, lsl #32
    // 0xaf2070: cmp             w1, NULL
    // 0xaf2074: b.ne            #0xaf2080
    // 0xaf2078: r2 = ""
    //     0xaf2078: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf207c: b               #0xaf2084
    // 0xaf2080: mov             x2, x1
    // 0xaf2084: LoadField: r5 = r0->field_6f
    //     0xaf2084: ldur            w5, [x0, #0x6f]
    // 0xaf2088: DecompressPointer r5
    //     0xaf2088: add             x5, x5, HEAP, lsl #32
    // 0xaf208c: cmp             w5, NULL
    // 0xaf2090: b.ne            #0xaf209c
    // 0xaf2094: r0 = Null
    //     0xaf2094: mov             x0, NULL
    // 0xaf2098: b               #0xaf20d4
    // 0xaf209c: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf209c: ldur            x6, [x3, #0x17]
    // 0xaf20a0: LoadField: r0 = r5->field_b
    //     0xaf20a0: ldur            w0, [x5, #0xb]
    // 0xaf20a4: r1 = LoadInt32Instr(r0)
    //     0xaf20a4: sbfx            x1, x0, #1, #0x1f
    // 0xaf20a8: mov             x0, x1
    // 0xaf20ac: mov             x1, x6
    // 0xaf20b0: cmp             x1, x0
    // 0xaf20b4: b.hs            #0xaf2bfc
    // 0xaf20b8: LoadField: r0 = r5->field_f
    //     0xaf20b8: ldur            w0, [x5, #0xf]
    // 0xaf20bc: DecompressPointer r0
    //     0xaf20bc: add             x0, x0, HEAP, lsl #32
    // 0xaf20c0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf20c0: add             x16, x0, x6, lsl #2
    //     0xaf20c4: ldur            w1, [x16, #0xf]
    // 0xaf20c8: DecompressPointer r1
    //     0xaf20c8: add             x1, x1, HEAP, lsl #32
    // 0xaf20cc: LoadField: r0 = r1->field_f
    //     0xaf20cc: ldur            w0, [x1, #0xf]
    // 0xaf20d0: DecompressPointer r0
    //     0xaf20d0: add             x0, x0, HEAP, lsl #32
    // 0xaf20d4: cmp             w0, NULL
    // 0xaf20d8: b.ne            #0xaf20e4
    // 0xaf20dc: r6 = ""
    //     0xaf20dc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf20e0: b               #0xaf20e8
    // 0xaf20e4: mov             x6, x0
    // 0xaf20e8: cmp             w5, NULL
    // 0xaf20ec: b.ne            #0xaf20f8
    // 0xaf20f0: r0 = Null
    //     0xaf20f0: mov             x0, NULL
    // 0xaf20f4: b               #0xaf2130
    // 0xaf20f8: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf20f8: ldur            x7, [x3, #0x17]
    // 0xaf20fc: LoadField: r0 = r5->field_b
    //     0xaf20fc: ldur            w0, [x5, #0xb]
    // 0xaf2100: r1 = LoadInt32Instr(r0)
    //     0xaf2100: sbfx            x1, x0, #1, #0x1f
    // 0xaf2104: mov             x0, x1
    // 0xaf2108: mov             x1, x7
    // 0xaf210c: cmp             x1, x0
    // 0xaf2110: b.hs            #0xaf2c00
    // 0xaf2114: LoadField: r0 = r5->field_f
    //     0xaf2114: ldur            w0, [x5, #0xf]
    // 0xaf2118: DecompressPointer r0
    //     0xaf2118: add             x0, x0, HEAP, lsl #32
    // 0xaf211c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf211c: add             x16, x0, x7, lsl #2
    //     0xaf2120: ldur            w1, [x16, #0xf]
    // 0xaf2124: DecompressPointer r1
    //     0xaf2124: add             x1, x1, HEAP, lsl #32
    // 0xaf2128: LoadField: r0 = r1->field_b
    //     0xaf2128: ldur            w0, [x1, #0xb]
    // 0xaf212c: DecompressPointer r0
    //     0xaf212c: add             x0, x0, HEAP, lsl #32
    // 0xaf2130: cmp             w0, NULL
    // 0xaf2134: b.ne            #0xaf2140
    // 0xaf2138: r7 = ""
    //     0xaf2138: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf213c: b               #0xaf2144
    // 0xaf2140: mov             x7, x0
    // 0xaf2144: cmp             w5, NULL
    // 0xaf2148: b.ne            #0xaf2154
    // 0xaf214c: r0 = Null
    //     0xaf214c: mov             x0, NULL
    // 0xaf2150: b               #0xaf218c
    // 0xaf2154: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xaf2154: ldur            x8, [x3, #0x17]
    // 0xaf2158: LoadField: r0 = r5->field_b
    //     0xaf2158: ldur            w0, [x5, #0xb]
    // 0xaf215c: r1 = LoadInt32Instr(r0)
    //     0xaf215c: sbfx            x1, x0, #1, #0x1f
    // 0xaf2160: mov             x0, x1
    // 0xaf2164: mov             x1, x8
    // 0xaf2168: cmp             x1, x0
    // 0xaf216c: b.hs            #0xaf2c04
    // 0xaf2170: LoadField: r0 = r5->field_f
    //     0xaf2170: ldur            w0, [x5, #0xf]
    // 0xaf2174: DecompressPointer r0
    //     0xaf2174: add             x0, x0, HEAP, lsl #32
    // 0xaf2178: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xaf2178: add             x16, x0, x8, lsl #2
    //     0xaf217c: ldur            w1, [x16, #0xf]
    // 0xaf2180: DecompressPointer r1
    //     0xaf2180: add             x1, x1, HEAP, lsl #32
    // 0xaf2184: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf2184: ldur            w0, [x1, #0x17]
    // 0xaf2188: DecompressPointer r0
    //     0xaf2188: add             x0, x0, HEAP, lsl #32
    // 0xaf218c: cmp             w0, NULL
    // 0xaf2190: b.ne            #0xaf219c
    // 0xaf2194: r3 = 0
    //     0xaf2194: movz            x3, #0
    // 0xaf2198: b               #0xaf21ac
    // 0xaf219c: r1 = LoadInt32Instr(r0)
    //     0xaf219c: sbfx            x1, x0, #1, #0x1f
    //     0xaf21a0: tbz             w0, #0, #0xaf21a8
    //     0xaf21a4: ldur            x1, [x0, #7]
    // 0xaf21a8: mov             x3, x1
    // 0xaf21ac: LoadField: r5 = r4->field_1f
    //     0xaf21ac: ldur            w5, [x4, #0x1f]
    // 0xaf21b0: DecompressPointer r5
    //     0xaf21b0: add             x5, x5, HEAP, lsl #32
    // 0xaf21b4: r0 = BoxInt64Instr(r3)
    //     0xaf21b4: sbfiz           x0, x3, #1, #0x1f
    //     0xaf21b8: cmp             x3, x0, asr #1
    //     0xaf21bc: b.eq            #0xaf21c8
    //     0xaf21c0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf21c4: stur            x3, [x0, #7]
    // 0xaf21c8: stp             x2, x5, [SP, #0x28]
    // 0xaf21cc: r16 = true
    //     0xaf21cc: add             x16, NULL, #0x20  ; true
    // 0xaf21d0: stp             x6, x16, [SP, #0x18]
    // 0xaf21d4: stp             x0, x7, [SP, #8]
    // 0xaf21d8: r16 = "add_to_bag"
    //     0xaf21d8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xaf21dc: ldr             x16, [x16, #0xa38]
    // 0xaf21e0: str             x16, [SP]
    // 0xaf21e4: mov             x0, x5
    // 0xaf21e8: ClosureCall
    //     0xaf21e8: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xaf21ec: ldr             x4, [x4, #0x330]
    //     0xaf21f0: ldur            x2, [x0, #0x1f]
    //     0xaf21f4: blr             x2
    // 0xaf21f8: b               #0xaf2bac
    // 0xaf21fc: LoadField: r1 = r0->field_8f
    //     0xaf21fc: ldur            w1, [x0, #0x8f]
    // 0xaf2200: DecompressPointer r1
    //     0xaf2200: add             x1, x1, HEAP, lsl #32
    // 0xaf2204: cmp             w1, NULL
    // 0xaf2208: b.ne            #0xaf2214
    // 0xaf220c: r5 = ""
    //     0xaf220c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2210: b               #0xaf2218
    // 0xaf2214: mov             x5, x1
    // 0xaf2218: cmp             w2, NULL
    // 0xaf221c: b.ne            #0xaf2228
    // 0xaf2220: r0 = Null
    //     0xaf2220: mov             x0, NULL
    // 0xaf2224: b               #0xaf2260
    // 0xaf2228: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf2228: ldur            x6, [x3, #0x17]
    // 0xaf222c: LoadField: r0 = r2->field_b
    //     0xaf222c: ldur            w0, [x2, #0xb]
    // 0xaf2230: r1 = LoadInt32Instr(r0)
    //     0xaf2230: sbfx            x1, x0, #1, #0x1f
    // 0xaf2234: mov             x0, x1
    // 0xaf2238: mov             x1, x6
    // 0xaf223c: cmp             x1, x0
    // 0xaf2240: b.hs            #0xaf2c08
    // 0xaf2244: LoadField: r0 = r2->field_f
    //     0xaf2244: ldur            w0, [x2, #0xf]
    // 0xaf2248: DecompressPointer r0
    //     0xaf2248: add             x0, x0, HEAP, lsl #32
    // 0xaf224c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf224c: add             x16, x0, x6, lsl #2
    //     0xaf2250: ldur            w1, [x16, #0xf]
    // 0xaf2254: DecompressPointer r1
    //     0xaf2254: add             x1, x1, HEAP, lsl #32
    // 0xaf2258: LoadField: r0 = r1->field_b
    //     0xaf2258: ldur            w0, [x1, #0xb]
    // 0xaf225c: DecompressPointer r0
    //     0xaf225c: add             x0, x0, HEAP, lsl #32
    // 0xaf2260: cmp             w0, NULL
    // 0xaf2264: b.ne            #0xaf2270
    // 0xaf2268: r6 = ""
    //     0xaf2268: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf226c: b               #0xaf2274
    // 0xaf2270: mov             x6, x0
    // 0xaf2274: cmp             w2, NULL
    // 0xaf2278: b.ne            #0xaf2284
    // 0xaf227c: r0 = Null
    //     0xaf227c: mov             x0, NULL
    // 0xaf2280: b               #0xaf22bc
    // 0xaf2284: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf2284: ldur            x7, [x3, #0x17]
    // 0xaf2288: LoadField: r0 = r2->field_b
    //     0xaf2288: ldur            w0, [x2, #0xb]
    // 0xaf228c: r1 = LoadInt32Instr(r0)
    //     0xaf228c: sbfx            x1, x0, #1, #0x1f
    // 0xaf2290: mov             x0, x1
    // 0xaf2294: mov             x1, x7
    // 0xaf2298: cmp             x1, x0
    // 0xaf229c: b.hs            #0xaf2c0c
    // 0xaf22a0: LoadField: r0 = r2->field_f
    //     0xaf22a0: ldur            w0, [x2, #0xf]
    // 0xaf22a4: DecompressPointer r0
    //     0xaf22a4: add             x0, x0, HEAP, lsl #32
    // 0xaf22a8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf22a8: add             x16, x0, x7, lsl #2
    //     0xaf22ac: ldur            w1, [x16, #0xf]
    // 0xaf22b0: DecompressPointer r1
    //     0xaf22b0: add             x1, x1, HEAP, lsl #32
    // 0xaf22b4: LoadField: r0 = r1->field_7
    //     0xaf22b4: ldur            w0, [x1, #7]
    // 0xaf22b8: DecompressPointer r0
    //     0xaf22b8: add             x0, x0, HEAP, lsl #32
    // 0xaf22bc: cmp             w0, NULL
    // 0xaf22c0: b.ne            #0xaf22cc
    // 0xaf22c4: r7 = ""
    //     0xaf22c4: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf22c8: b               #0xaf22d0
    // 0xaf22cc: mov             x7, x0
    // 0xaf22d0: cmp             w2, NULL
    // 0xaf22d4: b.ne            #0xaf22e0
    // 0xaf22d8: r0 = Null
    //     0xaf22d8: mov             x0, NULL
    // 0xaf22dc: b               #0xaf2318
    // 0xaf22e0: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xaf22e0: ldur            x8, [x3, #0x17]
    // 0xaf22e4: LoadField: r0 = r2->field_b
    //     0xaf22e4: ldur            w0, [x2, #0xb]
    // 0xaf22e8: r1 = LoadInt32Instr(r0)
    //     0xaf22e8: sbfx            x1, x0, #1, #0x1f
    // 0xaf22ec: mov             x0, x1
    // 0xaf22f0: mov             x1, x8
    // 0xaf22f4: cmp             x1, x0
    // 0xaf22f8: b.hs            #0xaf2c10
    // 0xaf22fc: LoadField: r0 = r2->field_f
    //     0xaf22fc: ldur            w0, [x2, #0xf]
    // 0xaf2300: DecompressPointer r0
    //     0xaf2300: add             x0, x0, HEAP, lsl #32
    // 0xaf2304: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xaf2304: add             x16, x0, x8, lsl #2
    //     0xaf2308: ldur            w1, [x16, #0xf]
    // 0xaf230c: DecompressPointer r1
    //     0xaf230c: add             x1, x1, HEAP, lsl #32
    // 0xaf2310: LoadField: r0 = r1->field_1b
    //     0xaf2310: ldur            w0, [x1, #0x1b]
    // 0xaf2314: DecompressPointer r0
    //     0xaf2314: add             x0, x0, HEAP, lsl #32
    // 0xaf2318: cmp             w0, NULL
    // 0xaf231c: b.ne            #0xaf2328
    // 0xaf2320: r2 = 0
    //     0xaf2320: movz            x2, #0
    // 0xaf2324: b               #0xaf2338
    // 0xaf2328: r1 = LoadInt32Instr(r0)
    //     0xaf2328: sbfx            x1, x0, #1, #0x1f
    //     0xaf232c: tbz             w0, #0, #0xaf2334
    //     0xaf2330: ldur            x1, [x0, #7]
    // 0xaf2334: mov             x2, x1
    // 0xaf2338: LoadField: r3 = r4->field_1f
    //     0xaf2338: ldur            w3, [x4, #0x1f]
    // 0xaf233c: DecompressPointer r3
    //     0xaf233c: add             x3, x3, HEAP, lsl #32
    // 0xaf2340: r0 = BoxInt64Instr(r2)
    //     0xaf2340: sbfiz           x0, x2, #1, #0x1f
    //     0xaf2344: cmp             x2, x0, asr #1
    //     0xaf2348: b.eq            #0xaf2354
    //     0xaf234c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf2350: stur            x2, [x0, #7]
    // 0xaf2354: stp             x5, x3, [SP, #0x28]
    // 0xaf2358: r16 = true
    //     0xaf2358: add             x16, NULL, #0x20  ; true
    // 0xaf235c: stp             x6, x16, [SP, #0x18]
    // 0xaf2360: stp             x0, x7, [SP, #8]
    // 0xaf2364: r16 = "add_to_bag"
    //     0xaf2364: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xaf2368: ldr             x16, [x16, #0xa38]
    // 0xaf236c: str             x16, [SP]
    // 0xaf2370: mov             x0, x3
    // 0xaf2374: ClosureCall
    //     0xaf2374: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xaf2378: ldr             x4, [x4, #0x330]
    //     0xaf237c: ldur            x2, [x0, #0x1f]
    //     0xaf2380: blr             x2
    // 0xaf2384: b               #0xaf2bac
    // 0xaf2388: LoadField: r5 = r0->field_73
    //     0xaf2388: ldur            w5, [x0, #0x73]
    // 0xaf238c: DecompressPointer r5
    //     0xaf238c: add             x5, x5, HEAP, lsl #32
    // 0xaf2390: cmp             w5, NULL
    // 0xaf2394: b.ne            #0xaf23a0
    // 0xaf2398: r1 = Null
    //     0xaf2398: mov             x1, NULL
    // 0xaf239c: b               #0xaf23b8
    // 0xaf23a0: LoadField: r1 = r5->field_b
    //     0xaf23a0: ldur            w1, [x5, #0xb]
    // 0xaf23a4: cbz             w1, #0xaf23b0
    // 0xaf23a8: r6 = false
    //     0xaf23a8: add             x6, NULL, #0x30  ; false
    // 0xaf23ac: b               #0xaf23b4
    // 0xaf23b0: r6 = true
    //     0xaf23b0: add             x6, NULL, #0x20  ; true
    // 0xaf23b4: mov             x1, x6
    // 0xaf23b8: cmp             w1, NULL
    // 0xaf23bc: b.eq            #0xaf23c4
    // 0xaf23c0: tbnz            w1, #4, #0xaf27b0
    // 0xaf23c4: LoadField: r1 = r4->field_27
    //     0xaf23c4: ldur            w1, [x4, #0x27]
    // 0xaf23c8: DecompressPointer r1
    //     0xaf23c8: add             x1, x1, HEAP, lsl #32
    // 0xaf23cc: tbnz            w1, #4, #0xaf26ac
    // 0xaf23d0: LoadField: r5 = r0->field_6f
    //     0xaf23d0: ldur            w5, [x0, #0x6f]
    // 0xaf23d4: DecompressPointer r5
    //     0xaf23d4: add             x5, x5, HEAP, lsl #32
    // 0xaf23d8: cmp             w5, NULL
    // 0xaf23dc: b.ne            #0xaf23e8
    // 0xaf23e0: r0 = Null
    //     0xaf23e0: mov             x0, NULL
    // 0xaf23e4: b               #0xaf2420
    // 0xaf23e8: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf23e8: ldur            x6, [x3, #0x17]
    // 0xaf23ec: LoadField: r0 = r5->field_b
    //     0xaf23ec: ldur            w0, [x5, #0xb]
    // 0xaf23f0: r1 = LoadInt32Instr(r0)
    //     0xaf23f0: sbfx            x1, x0, #1, #0x1f
    // 0xaf23f4: mov             x0, x1
    // 0xaf23f8: mov             x1, x6
    // 0xaf23fc: cmp             x1, x0
    // 0xaf2400: b.hs            #0xaf2c14
    // 0xaf2404: LoadField: r0 = r5->field_f
    //     0xaf2404: ldur            w0, [x5, #0xf]
    // 0xaf2408: DecompressPointer r0
    //     0xaf2408: add             x0, x0, HEAP, lsl #32
    // 0xaf240c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf240c: add             x16, x0, x6, lsl #2
    //     0xaf2410: ldur            w1, [x16, #0xf]
    // 0xaf2414: DecompressPointer r1
    //     0xaf2414: add             x1, x1, HEAP, lsl #32
    // 0xaf2418: LoadField: r0 = r1->field_b
    //     0xaf2418: ldur            w0, [x1, #0xb]
    // 0xaf241c: DecompressPointer r0
    //     0xaf241c: add             x0, x0, HEAP, lsl #32
    // 0xaf2420: cmp             w0, NULL
    // 0xaf2424: b.ne            #0xaf2430
    // 0xaf2428: r6 = ""
    //     0xaf2428: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf242c: b               #0xaf2434
    // 0xaf2430: mov             x6, x0
    // 0xaf2434: stur            x6, [fp, #-0x10]
    // 0xaf2438: cmp             w5, NULL
    // 0xaf243c: b.ne            #0xaf2448
    // 0xaf2440: r0 = Null
    //     0xaf2440: mov             x0, NULL
    // 0xaf2444: b               #0xaf2498
    // 0xaf2448: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf2448: ldur            x7, [x3, #0x17]
    // 0xaf244c: LoadField: r0 = r5->field_b
    //     0xaf244c: ldur            w0, [x5, #0xb]
    // 0xaf2450: r1 = LoadInt32Instr(r0)
    //     0xaf2450: sbfx            x1, x0, #1, #0x1f
    // 0xaf2454: mov             x0, x1
    // 0xaf2458: mov             x1, x7
    // 0xaf245c: cmp             x1, x0
    // 0xaf2460: b.hs            #0xaf2c18
    // 0xaf2464: LoadField: r0 = r5->field_f
    //     0xaf2464: ldur            w0, [x5, #0xf]
    // 0xaf2468: DecompressPointer r0
    //     0xaf2468: add             x0, x0, HEAP, lsl #32
    // 0xaf246c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf246c: add             x16, x0, x7, lsl #2
    //     0xaf2470: ldur            w1, [x16, #0xf]
    // 0xaf2474: DecompressPointer r1
    //     0xaf2474: add             x1, x1, HEAP, lsl #32
    // 0xaf2478: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf2478: ldur            w0, [x1, #0x17]
    // 0xaf247c: DecompressPointer r0
    //     0xaf247c: add             x0, x0, HEAP, lsl #32
    // 0xaf2480: cmp             w0, NULL
    // 0xaf2484: b.ne            #0xaf2490
    // 0xaf2488: r0 = Null
    //     0xaf2488: mov             x0, NULL
    // 0xaf248c: b               #0xaf2498
    // 0xaf2490: stp             x0, NULL, [SP]
    // 0xaf2494: r0 = _Double.fromInteger()
    //     0xaf2494: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xaf2498: cmp             w0, NULL
    // 0xaf249c: b.ne            #0xaf24a8
    // 0xaf24a0: d0 = 0.000000
    //     0xaf24a0: eor             v0.16b, v0.16b, v0.16b
    // 0xaf24a4: b               #0xaf24ac
    // 0xaf24a8: LoadField: d0 = r0->field_7
    //     0xaf24a8: ldur            d0, [x0, #7]
    // 0xaf24ac: ldur            x0, [fp, #-8]
    // 0xaf24b0: stur            d0, [fp, #-0x48]
    // 0xaf24b4: r1 = Null
    //     0xaf24b4: mov             x1, NULL
    // 0xaf24b8: r2 = 12
    //     0xaf24b8: movz            x2, #0xc
    // 0xaf24bc: r0 = AllocateArray()
    //     0xaf24bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf24c0: mov             x2, x0
    // 0xaf24c4: stur            x2, [fp, #-0x20]
    // 0xaf24c8: r16 = "id"
    //     0xaf24c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xaf24cc: ldr             x16, [x16, #0x400]
    // 0xaf24d0: StoreField: r2->field_f = r16
    //     0xaf24d0: stur            w16, [x2, #0xf]
    // 0xaf24d4: ldur            x3, [fp, #-8]
    // 0xaf24d8: LoadField: r4 = r3->field_f
    //     0xaf24d8: ldur            w4, [x3, #0xf]
    // 0xaf24dc: DecompressPointer r4
    //     0xaf24dc: add             x4, x4, HEAP, lsl #32
    // 0xaf24e0: LoadField: r0 = r4->field_b
    //     0xaf24e0: ldur            w0, [x4, #0xb]
    // 0xaf24e4: DecompressPointer r0
    //     0xaf24e4: add             x0, x0, HEAP, lsl #32
    // 0xaf24e8: cmp             w0, NULL
    // 0xaf24ec: b.eq            #0xaf2c1c
    // 0xaf24f0: LoadField: r1 = r0->field_b
    //     0xaf24f0: ldur            w1, [x0, #0xb]
    // 0xaf24f4: DecompressPointer r1
    //     0xaf24f4: add             x1, x1, HEAP, lsl #32
    // 0xaf24f8: LoadField: r5 = r1->field_6f
    //     0xaf24f8: ldur            w5, [x1, #0x6f]
    // 0xaf24fc: DecompressPointer r5
    //     0xaf24fc: add             x5, x5, HEAP, lsl #32
    // 0xaf2500: cmp             w5, NULL
    // 0xaf2504: b.ne            #0xaf2510
    // 0xaf2508: r0 = Null
    //     0xaf2508: mov             x0, NULL
    // 0xaf250c: b               #0xaf2548
    // 0xaf2510: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xaf2510: ldur            x6, [x4, #0x17]
    // 0xaf2514: LoadField: r0 = r5->field_b
    //     0xaf2514: ldur            w0, [x5, #0xb]
    // 0xaf2518: r1 = LoadInt32Instr(r0)
    //     0xaf2518: sbfx            x1, x0, #1, #0x1f
    // 0xaf251c: mov             x0, x1
    // 0xaf2520: mov             x1, x6
    // 0xaf2524: cmp             x1, x0
    // 0xaf2528: b.hs            #0xaf2c20
    // 0xaf252c: LoadField: r0 = r5->field_f
    //     0xaf252c: ldur            w0, [x5, #0xf]
    // 0xaf2530: DecompressPointer r0
    //     0xaf2530: add             x0, x0, HEAP, lsl #32
    // 0xaf2534: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf2534: add             x16, x0, x6, lsl #2
    //     0xaf2538: ldur            w1, [x16, #0xf]
    // 0xaf253c: DecompressPointer r1
    //     0xaf253c: add             x1, x1, HEAP, lsl #32
    // 0xaf2540: LoadField: r0 = r1->field_b
    //     0xaf2540: ldur            w0, [x1, #0xb]
    // 0xaf2544: DecompressPointer r0
    //     0xaf2544: add             x0, x0, HEAP, lsl #32
    // 0xaf2548: cmp             w0, NULL
    // 0xaf254c: b.ne            #0xaf2554
    // 0xaf2550: r0 = ""
    //     0xaf2550: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2554: StoreField: r2->field_13 = r0
    //     0xaf2554: stur            w0, [x2, #0x13]
    // 0xaf2558: r16 = "quantity"
    //     0xaf2558: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xaf255c: ldr             x16, [x16, #0x428]
    // 0xaf2560: ArrayStore: r2[0] = r16  ; List_4
    //     0xaf2560: stur            w16, [x2, #0x17]
    // 0xaf2564: r16 = 2
    //     0xaf2564: movz            x16, #0x2
    // 0xaf2568: StoreField: r2->field_1b = r16
    //     0xaf2568: stur            w16, [x2, #0x1b]
    // 0xaf256c: r16 = "item_price"
    //     0xaf256c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xaf2570: ldr             x16, [x16, #0x498]
    // 0xaf2574: StoreField: r2->field_1f = r16
    //     0xaf2574: stur            w16, [x2, #0x1f]
    // 0xaf2578: cmp             w5, NULL
    // 0xaf257c: b.ne            #0xaf2588
    // 0xaf2580: r0 = Null
    //     0xaf2580: mov             x0, NULL
    // 0xaf2584: b               #0xaf25d8
    // 0xaf2588: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xaf2588: ldur            x6, [x4, #0x17]
    // 0xaf258c: LoadField: r0 = r5->field_b
    //     0xaf258c: ldur            w0, [x5, #0xb]
    // 0xaf2590: r1 = LoadInt32Instr(r0)
    //     0xaf2590: sbfx            x1, x0, #1, #0x1f
    // 0xaf2594: mov             x0, x1
    // 0xaf2598: mov             x1, x6
    // 0xaf259c: cmp             x1, x0
    // 0xaf25a0: b.hs            #0xaf2c24
    // 0xaf25a4: LoadField: r0 = r5->field_f
    //     0xaf25a4: ldur            w0, [x5, #0xf]
    // 0xaf25a8: DecompressPointer r0
    //     0xaf25a8: add             x0, x0, HEAP, lsl #32
    // 0xaf25ac: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf25ac: add             x16, x0, x6, lsl #2
    //     0xaf25b0: ldur            w1, [x16, #0xf]
    // 0xaf25b4: DecompressPointer r1
    //     0xaf25b4: add             x1, x1, HEAP, lsl #32
    // 0xaf25b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaf25b8: ldur            w0, [x1, #0x17]
    // 0xaf25bc: DecompressPointer r0
    //     0xaf25bc: add             x0, x0, HEAP, lsl #32
    // 0xaf25c0: cmp             w0, NULL
    // 0xaf25c4: b.ne            #0xaf25d0
    // 0xaf25c8: r0 = Null
    //     0xaf25c8: mov             x0, NULL
    // 0xaf25cc: b               #0xaf25d8
    // 0xaf25d0: stp             x0, NULL, [SP]
    // 0xaf25d4: r0 = _Double.fromInteger()
    //     0xaf25d4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xaf25d8: cmp             w0, NULL
    // 0xaf25dc: b.ne            #0xaf25e8
    // 0xaf25e0: d1 = 0.000000
    //     0xaf25e0: eor             v1.16b, v1.16b, v1.16b
    // 0xaf25e4: b               #0xaf25f0
    // 0xaf25e8: LoadField: d0 = r0->field_7
    //     0xaf25e8: ldur            d0, [x0, #7]
    // 0xaf25ec: mov             v1.16b, v0.16b
    // 0xaf25f0: ldur            d0, [fp, #-0x48]
    // 0xaf25f4: ldur            x2, [fp, #-0x18]
    // 0xaf25f8: r0 = inline_Allocate_Double()
    //     0xaf25f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaf25fc: add             x0, x0, #0x10
    //     0xaf2600: cmp             x1, x0
    //     0xaf2604: b.ls            #0xaf2c28
    //     0xaf2608: str             x0, [THR, #0x50]  ; THR::top
    //     0xaf260c: sub             x0, x0, #0xf
    //     0xaf2610: movz            x1, #0xe15c
    //     0xaf2614: movk            x1, #0x3, lsl #16
    //     0xaf2618: stur            x1, [x0, #-1]
    // 0xaf261c: StoreField: r0->field_7 = d1
    //     0xaf261c: stur            d1, [x0, #7]
    // 0xaf2620: ldur            x1, [fp, #-0x20]
    // 0xaf2624: ArrayStore: r1[5] = r0  ; List_4
    //     0xaf2624: add             x25, x1, #0x23
    //     0xaf2628: str             w0, [x25]
    //     0xaf262c: tbz             w0, #0, #0xaf2648
    //     0xaf2630: ldurb           w16, [x1, #-1]
    //     0xaf2634: ldurb           w17, [x0, #-1]
    //     0xaf2638: and             x16, x17, x16, lsr #2
    //     0xaf263c: tst             x16, HEAP, lsr #32
    //     0xaf2640: b.eq            #0xaf2648
    //     0xaf2644: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf2648: r16 = <String, dynamic>
    //     0xaf2648: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xaf264c: ldur            lr, [fp, #-0x20]
    // 0xaf2650: stp             lr, x16, [SP]
    // 0xaf2654: r0 = Map._fromLiteral()
    //     0xaf2654: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xaf2658: ldur            x2, [fp, #-0x18]
    // 0xaf265c: LoadField: r1 = r2->field_23
    //     0xaf265c: ldur            w1, [x2, #0x23]
    // 0xaf2660: DecompressPointer r1
    //     0xaf2660: add             x1, x1, HEAP, lsl #32
    // 0xaf2664: ldur            d0, [fp, #-0x48]
    // 0xaf2668: r2 = inline_Allocate_Double()
    //     0xaf2668: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaf266c: add             x2, x2, #0x10
    //     0xaf2670: cmp             x3, x2
    //     0xaf2674: b.ls            #0xaf2c40
    //     0xaf2678: str             x2, [THR, #0x50]  ; THR::top
    //     0xaf267c: sub             x2, x2, #0xf
    //     0xaf2680: movz            x3, #0xe15c
    //     0xaf2684: movk            x3, #0x3, lsl #16
    //     0xaf2688: stur            x3, [x2, #-1]
    // 0xaf268c: StoreField: r2->field_7 = d0
    //     0xaf268c: stur            d0, [x2, #7]
    // 0xaf2690: ldur            x16, [fp, #-0x10]
    // 0xaf2694: stp             x16, x1, [SP, #0x10]
    // 0xaf2698: stp             x0, x2, [SP]
    // 0xaf269c: mov             x0, x1
    // 0xaf26a0: ClosureCall
    //     0xaf26a0: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xaf26a4: ldur            x2, [x0, #0x1f]
    //     0xaf26a8: blr             x2
    // 0xaf26ac: ldur            x4, [fp, #-8]
    // 0xaf26b0: LoadField: r2 = r4->field_f
    //     0xaf26b0: ldur            w2, [x4, #0xf]
    // 0xaf26b4: DecompressPointer r2
    //     0xaf26b4: add             x2, x2, HEAP, lsl #32
    // 0xaf26b8: LoadField: r3 = r2->field_b
    //     0xaf26b8: ldur            w3, [x2, #0xb]
    // 0xaf26bc: DecompressPointer r3
    //     0xaf26bc: add             x3, x3, HEAP, lsl #32
    // 0xaf26c0: cmp             w3, NULL
    // 0xaf26c4: b.eq            #0xaf2c5c
    // 0xaf26c8: LoadField: r0 = r3->field_b
    //     0xaf26c8: ldur            w0, [x3, #0xb]
    // 0xaf26cc: DecompressPointer r0
    //     0xaf26cc: add             x0, x0, HEAP, lsl #32
    // 0xaf26d0: LoadField: r4 = r0->field_6f
    //     0xaf26d0: ldur            w4, [x0, #0x6f]
    // 0xaf26d4: DecompressPointer r4
    //     0xaf26d4: add             x4, x4, HEAP, lsl #32
    // 0xaf26d8: cmp             w4, NULL
    // 0xaf26dc: b.ne            #0xaf26e8
    // 0xaf26e0: r0 = Null
    //     0xaf26e0: mov             x0, NULL
    // 0xaf26e4: b               #0xaf2720
    // 0xaf26e8: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xaf26e8: ldur            x5, [x2, #0x17]
    // 0xaf26ec: LoadField: r0 = r4->field_b
    //     0xaf26ec: ldur            w0, [x4, #0xb]
    // 0xaf26f0: r1 = LoadInt32Instr(r0)
    //     0xaf26f0: sbfx            x1, x0, #1, #0x1f
    // 0xaf26f4: mov             x0, x1
    // 0xaf26f8: mov             x1, x5
    // 0xaf26fc: cmp             x1, x0
    // 0xaf2700: b.hs            #0xaf2c60
    // 0xaf2704: LoadField: r0 = r4->field_f
    //     0xaf2704: ldur            w0, [x4, #0xf]
    // 0xaf2708: DecompressPointer r0
    //     0xaf2708: add             x0, x0, HEAP, lsl #32
    // 0xaf270c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaf270c: add             x16, x0, x5, lsl #2
    //     0xaf2710: ldur            w1, [x16, #0xf]
    // 0xaf2714: DecompressPointer r1
    //     0xaf2714: add             x1, x1, HEAP, lsl #32
    // 0xaf2718: LoadField: r0 = r1->field_f
    //     0xaf2718: ldur            w0, [x1, #0xf]
    // 0xaf271c: DecompressPointer r0
    //     0xaf271c: add             x0, x0, HEAP, lsl #32
    // 0xaf2720: cmp             w0, NULL
    // 0xaf2724: b.ne            #0xaf2730
    // 0xaf2728: r5 = ""
    //     0xaf2728: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf272c: b               #0xaf2734
    // 0xaf2730: mov             x5, x0
    // 0xaf2734: cmp             w4, NULL
    // 0xaf2738: b.ne            #0xaf2744
    // 0xaf273c: r0 = Null
    //     0xaf273c: mov             x0, NULL
    // 0xaf2740: b               #0xaf277c
    // 0xaf2744: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xaf2744: ldur            x6, [x2, #0x17]
    // 0xaf2748: LoadField: r0 = r4->field_b
    //     0xaf2748: ldur            w0, [x4, #0xb]
    // 0xaf274c: r1 = LoadInt32Instr(r0)
    //     0xaf274c: sbfx            x1, x0, #1, #0x1f
    // 0xaf2750: mov             x0, x1
    // 0xaf2754: mov             x1, x6
    // 0xaf2758: cmp             x1, x0
    // 0xaf275c: b.hs            #0xaf2c64
    // 0xaf2760: LoadField: r0 = r4->field_f
    //     0xaf2760: ldur            w0, [x4, #0xf]
    // 0xaf2764: DecompressPointer r0
    //     0xaf2764: add             x0, x0, HEAP, lsl #32
    // 0xaf2768: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf2768: add             x16, x0, x6, lsl #2
    //     0xaf276c: ldur            w1, [x16, #0xf]
    // 0xaf2770: DecompressPointer r1
    //     0xaf2770: add             x1, x1, HEAP, lsl #32
    // 0xaf2774: LoadField: r0 = r1->field_b
    //     0xaf2774: ldur            w0, [x1, #0xb]
    // 0xaf2778: DecompressPointer r0
    //     0xaf2778: add             x0, x0, HEAP, lsl #32
    // 0xaf277c: cmp             w0, NULL
    // 0xaf2780: b.ne            #0xaf2788
    // 0xaf2784: r0 = ""
    //     0xaf2784: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2788: LoadField: r1 = r3->field_1b
    //     0xaf2788: ldur            w1, [x3, #0x1b]
    // 0xaf278c: DecompressPointer r1
    //     0xaf278c: add             x1, x1, HEAP, lsl #32
    // 0xaf2790: stp             x5, x1, [SP, #0x10]
    // 0xaf2794: r16 = 2
    //     0xaf2794: movz            x16, #0x2
    // 0xaf2798: stp             x16, x0, [SP]
    // 0xaf279c: mov             x0, x1
    // 0xaf27a0: ClosureCall
    //     0xaf27a0: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xaf27a4: ldur            x2, [x0, #0x1f]
    //     0xaf27a8: blr             x2
    // 0xaf27ac: b               #0xaf2bac
    // 0xaf27b0: mov             x16, x4
    // 0xaf27b4: mov             x4, x2
    // 0xaf27b8: mov             x2, x16
    // 0xaf27bc: LoadField: r0 = r2->field_27
    //     0xaf27bc: ldur            w0, [x2, #0x27]
    // 0xaf27c0: DecompressPointer r0
    //     0xaf27c0: add             x0, x0, HEAP, lsl #32
    // 0xaf27c4: tbnz            w0, #4, #0xaf2aa0
    // 0xaf27c8: cmp             w5, NULL
    // 0xaf27cc: b.ne            #0xaf27d8
    // 0xaf27d0: r0 = Null
    //     0xaf27d0: mov             x0, NULL
    // 0xaf27d4: b               #0xaf2810
    // 0xaf27d8: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xaf27d8: ldur            x6, [x3, #0x17]
    // 0xaf27dc: LoadField: r0 = r5->field_b
    //     0xaf27dc: ldur            w0, [x5, #0xb]
    // 0xaf27e0: r1 = LoadInt32Instr(r0)
    //     0xaf27e0: sbfx            x1, x0, #1, #0x1f
    // 0xaf27e4: mov             x0, x1
    // 0xaf27e8: mov             x1, x6
    // 0xaf27ec: cmp             x1, x0
    // 0xaf27f0: b.hs            #0xaf2c68
    // 0xaf27f4: LoadField: r0 = r5->field_f
    //     0xaf27f4: ldur            w0, [x5, #0xf]
    // 0xaf27f8: DecompressPointer r0
    //     0xaf27f8: add             x0, x0, HEAP, lsl #32
    // 0xaf27fc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf27fc: add             x16, x0, x6, lsl #2
    //     0xaf2800: ldur            w1, [x16, #0xf]
    // 0xaf2804: DecompressPointer r1
    //     0xaf2804: add             x1, x1, HEAP, lsl #32
    // 0xaf2808: LoadField: r0 = r1->field_7
    //     0xaf2808: ldur            w0, [x1, #7]
    // 0xaf280c: DecompressPointer r0
    //     0xaf280c: add             x0, x0, HEAP, lsl #32
    // 0xaf2810: cmp             w0, NULL
    // 0xaf2814: b.ne            #0xaf2820
    // 0xaf2818: r6 = ""
    //     0xaf2818: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf281c: b               #0xaf2824
    // 0xaf2820: mov             x6, x0
    // 0xaf2824: stur            x6, [fp, #-0x10]
    // 0xaf2828: cmp             w5, NULL
    // 0xaf282c: b.ne            #0xaf2838
    // 0xaf2830: r0 = Null
    //     0xaf2830: mov             x0, NULL
    // 0xaf2834: b               #0xaf2888
    // 0xaf2838: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xaf2838: ldur            x7, [x3, #0x17]
    // 0xaf283c: LoadField: r0 = r5->field_b
    //     0xaf283c: ldur            w0, [x5, #0xb]
    // 0xaf2840: r1 = LoadInt32Instr(r0)
    //     0xaf2840: sbfx            x1, x0, #1, #0x1f
    // 0xaf2844: mov             x0, x1
    // 0xaf2848: mov             x1, x7
    // 0xaf284c: cmp             x1, x0
    // 0xaf2850: b.hs            #0xaf2c6c
    // 0xaf2854: LoadField: r0 = r5->field_f
    //     0xaf2854: ldur            w0, [x5, #0xf]
    // 0xaf2858: DecompressPointer r0
    //     0xaf2858: add             x0, x0, HEAP, lsl #32
    // 0xaf285c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf285c: add             x16, x0, x7, lsl #2
    //     0xaf2860: ldur            w1, [x16, #0xf]
    // 0xaf2864: DecompressPointer r1
    //     0xaf2864: add             x1, x1, HEAP, lsl #32
    // 0xaf2868: LoadField: r0 = r1->field_1b
    //     0xaf2868: ldur            w0, [x1, #0x1b]
    // 0xaf286c: DecompressPointer r0
    //     0xaf286c: add             x0, x0, HEAP, lsl #32
    // 0xaf2870: cmp             w0, NULL
    // 0xaf2874: b.ne            #0xaf2880
    // 0xaf2878: r0 = Null
    //     0xaf2878: mov             x0, NULL
    // 0xaf287c: b               #0xaf2888
    // 0xaf2880: stp             x0, NULL, [SP]
    // 0xaf2884: r0 = _Double.fromInteger()
    //     0xaf2884: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xaf2888: cmp             w0, NULL
    // 0xaf288c: b.ne            #0xaf2898
    // 0xaf2890: d0 = 0.000000
    //     0xaf2890: eor             v0.16b, v0.16b, v0.16b
    // 0xaf2894: b               #0xaf289c
    // 0xaf2898: LoadField: d0 = r0->field_7
    //     0xaf2898: ldur            d0, [x0, #7]
    // 0xaf289c: ldur            x0, [fp, #-8]
    // 0xaf28a0: stur            d0, [fp, #-0x48]
    // 0xaf28a4: r1 = Null
    //     0xaf28a4: mov             x1, NULL
    // 0xaf28a8: r2 = 12
    //     0xaf28a8: movz            x2, #0xc
    // 0xaf28ac: r0 = AllocateArray()
    //     0xaf28ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf28b0: mov             x2, x0
    // 0xaf28b4: stur            x2, [fp, #-0x20]
    // 0xaf28b8: r16 = "id"
    //     0xaf28b8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xaf28bc: ldr             x16, [x16, #0x400]
    // 0xaf28c0: StoreField: r2->field_f = r16
    //     0xaf28c0: stur            w16, [x2, #0xf]
    // 0xaf28c4: ldur            x3, [fp, #-8]
    // 0xaf28c8: LoadField: r4 = r3->field_f
    //     0xaf28c8: ldur            w4, [x3, #0xf]
    // 0xaf28cc: DecompressPointer r4
    //     0xaf28cc: add             x4, x4, HEAP, lsl #32
    // 0xaf28d0: LoadField: r0 = r4->field_b
    //     0xaf28d0: ldur            w0, [x4, #0xb]
    // 0xaf28d4: DecompressPointer r0
    //     0xaf28d4: add             x0, x0, HEAP, lsl #32
    // 0xaf28d8: cmp             w0, NULL
    // 0xaf28dc: b.eq            #0xaf2c70
    // 0xaf28e0: LoadField: r1 = r0->field_b
    //     0xaf28e0: ldur            w1, [x0, #0xb]
    // 0xaf28e4: DecompressPointer r1
    //     0xaf28e4: add             x1, x1, HEAP, lsl #32
    // 0xaf28e8: LoadField: r5 = r1->field_73
    //     0xaf28e8: ldur            w5, [x1, #0x73]
    // 0xaf28ec: DecompressPointer r5
    //     0xaf28ec: add             x5, x5, HEAP, lsl #32
    // 0xaf28f0: cmp             w5, NULL
    // 0xaf28f4: b.ne            #0xaf2900
    // 0xaf28f8: r0 = Null
    //     0xaf28f8: mov             x0, NULL
    // 0xaf28fc: b               #0xaf2938
    // 0xaf2900: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xaf2900: ldur            x6, [x4, #0x17]
    // 0xaf2904: LoadField: r0 = r5->field_b
    //     0xaf2904: ldur            w0, [x5, #0xb]
    // 0xaf2908: r1 = LoadInt32Instr(r0)
    //     0xaf2908: sbfx            x1, x0, #1, #0x1f
    // 0xaf290c: mov             x0, x1
    // 0xaf2910: mov             x1, x6
    // 0xaf2914: cmp             x1, x0
    // 0xaf2918: b.hs            #0xaf2c74
    // 0xaf291c: LoadField: r0 = r5->field_f
    //     0xaf291c: ldur            w0, [x5, #0xf]
    // 0xaf2920: DecompressPointer r0
    //     0xaf2920: add             x0, x0, HEAP, lsl #32
    // 0xaf2924: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf2924: add             x16, x0, x6, lsl #2
    //     0xaf2928: ldur            w1, [x16, #0xf]
    // 0xaf292c: DecompressPointer r1
    //     0xaf292c: add             x1, x1, HEAP, lsl #32
    // 0xaf2930: LoadField: r0 = r1->field_7
    //     0xaf2930: ldur            w0, [x1, #7]
    // 0xaf2934: DecompressPointer r0
    //     0xaf2934: add             x0, x0, HEAP, lsl #32
    // 0xaf2938: cmp             w0, NULL
    // 0xaf293c: b.ne            #0xaf2944
    // 0xaf2940: r0 = ""
    //     0xaf2940: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2944: StoreField: r2->field_13 = r0
    //     0xaf2944: stur            w0, [x2, #0x13]
    // 0xaf2948: r16 = "quantity"
    //     0xaf2948: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xaf294c: ldr             x16, [x16, #0x428]
    // 0xaf2950: ArrayStore: r2[0] = r16  ; List_4
    //     0xaf2950: stur            w16, [x2, #0x17]
    // 0xaf2954: r16 = 2
    //     0xaf2954: movz            x16, #0x2
    // 0xaf2958: StoreField: r2->field_1b = r16
    //     0xaf2958: stur            w16, [x2, #0x1b]
    // 0xaf295c: r16 = "item_price"
    //     0xaf295c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xaf2960: ldr             x16, [x16, #0x498]
    // 0xaf2964: StoreField: r2->field_1f = r16
    //     0xaf2964: stur            w16, [x2, #0x1f]
    // 0xaf2968: cmp             w5, NULL
    // 0xaf296c: b.ne            #0xaf2978
    // 0xaf2970: r0 = Null
    //     0xaf2970: mov             x0, NULL
    // 0xaf2974: b               #0xaf29c8
    // 0xaf2978: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xaf2978: ldur            x6, [x4, #0x17]
    // 0xaf297c: LoadField: r0 = r5->field_b
    //     0xaf297c: ldur            w0, [x5, #0xb]
    // 0xaf2980: r1 = LoadInt32Instr(r0)
    //     0xaf2980: sbfx            x1, x0, #1, #0x1f
    // 0xaf2984: mov             x0, x1
    // 0xaf2988: mov             x1, x6
    // 0xaf298c: cmp             x1, x0
    // 0xaf2990: b.hs            #0xaf2c78
    // 0xaf2994: LoadField: r0 = r5->field_f
    //     0xaf2994: ldur            w0, [x5, #0xf]
    // 0xaf2998: DecompressPointer r0
    //     0xaf2998: add             x0, x0, HEAP, lsl #32
    // 0xaf299c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf299c: add             x16, x0, x6, lsl #2
    //     0xaf29a0: ldur            w1, [x16, #0xf]
    // 0xaf29a4: DecompressPointer r1
    //     0xaf29a4: add             x1, x1, HEAP, lsl #32
    // 0xaf29a8: LoadField: r0 = r1->field_1b
    //     0xaf29a8: ldur            w0, [x1, #0x1b]
    // 0xaf29ac: DecompressPointer r0
    //     0xaf29ac: add             x0, x0, HEAP, lsl #32
    // 0xaf29b0: cmp             w0, NULL
    // 0xaf29b4: b.ne            #0xaf29c0
    // 0xaf29b8: r0 = Null
    //     0xaf29b8: mov             x0, NULL
    // 0xaf29bc: b               #0xaf29c8
    // 0xaf29c0: stp             x0, NULL, [SP]
    // 0xaf29c4: r0 = _Double.fromInteger()
    //     0xaf29c4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xaf29c8: cmp             w0, NULL
    // 0xaf29cc: b.ne            #0xaf29d8
    // 0xaf29d0: d1 = 0.000000
    //     0xaf29d0: eor             v1.16b, v1.16b, v1.16b
    // 0xaf29d4: b               #0xaf29e0
    // 0xaf29d8: LoadField: d0 = r0->field_7
    //     0xaf29d8: ldur            d0, [x0, #7]
    // 0xaf29dc: mov             v1.16b, v0.16b
    // 0xaf29e0: ldur            d0, [fp, #-0x48]
    // 0xaf29e4: ldur            x2, [fp, #-0x18]
    // 0xaf29e8: r0 = inline_Allocate_Double()
    //     0xaf29e8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaf29ec: add             x0, x0, #0x10
    //     0xaf29f0: cmp             x1, x0
    //     0xaf29f4: b.ls            #0xaf2c7c
    //     0xaf29f8: str             x0, [THR, #0x50]  ; THR::top
    //     0xaf29fc: sub             x0, x0, #0xf
    //     0xaf2a00: movz            x1, #0xe15c
    //     0xaf2a04: movk            x1, #0x3, lsl #16
    //     0xaf2a08: stur            x1, [x0, #-1]
    // 0xaf2a0c: StoreField: r0->field_7 = d1
    //     0xaf2a0c: stur            d1, [x0, #7]
    // 0xaf2a10: ldur            x1, [fp, #-0x20]
    // 0xaf2a14: ArrayStore: r1[5] = r0  ; List_4
    //     0xaf2a14: add             x25, x1, #0x23
    //     0xaf2a18: str             w0, [x25]
    //     0xaf2a1c: tbz             w0, #0, #0xaf2a38
    //     0xaf2a20: ldurb           w16, [x1, #-1]
    //     0xaf2a24: ldurb           w17, [x0, #-1]
    //     0xaf2a28: and             x16, x17, x16, lsr #2
    //     0xaf2a2c: tst             x16, HEAP, lsr #32
    //     0xaf2a30: b.eq            #0xaf2a38
    //     0xaf2a34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf2a38: r16 = <String, dynamic>
    //     0xaf2a38: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xaf2a3c: ldur            lr, [fp, #-0x20]
    // 0xaf2a40: stp             lr, x16, [SP]
    // 0xaf2a44: r0 = Map._fromLiteral()
    //     0xaf2a44: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xaf2a48: mov             x1, x0
    // 0xaf2a4c: ldur            x0, [fp, #-0x18]
    // 0xaf2a50: LoadField: r2 = r0->field_23
    //     0xaf2a50: ldur            w2, [x0, #0x23]
    // 0xaf2a54: DecompressPointer r2
    //     0xaf2a54: add             x2, x2, HEAP, lsl #32
    // 0xaf2a58: ldur            d0, [fp, #-0x48]
    // 0xaf2a5c: r0 = inline_Allocate_Double()
    //     0xaf2a5c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xaf2a60: add             x0, x0, #0x10
    //     0xaf2a64: cmp             x3, x0
    //     0xaf2a68: b.ls            #0xaf2c94
    //     0xaf2a6c: str             x0, [THR, #0x50]  ; THR::top
    //     0xaf2a70: sub             x0, x0, #0xf
    //     0xaf2a74: movz            x3, #0xe15c
    //     0xaf2a78: movk            x3, #0x3, lsl #16
    //     0xaf2a7c: stur            x3, [x0, #-1]
    // 0xaf2a80: StoreField: r0->field_7 = d0
    //     0xaf2a80: stur            d0, [x0, #7]
    // 0xaf2a84: ldur            x16, [fp, #-0x10]
    // 0xaf2a88: stp             x16, x2, [SP, #0x10]
    // 0xaf2a8c: stp             x1, x0, [SP]
    // 0xaf2a90: mov             x0, x2
    // 0xaf2a94: ClosureCall
    //     0xaf2a94: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xaf2a98: ldur            x2, [x0, #0x1f]
    //     0xaf2a9c: blr             x2
    // 0xaf2aa0: ldur            x0, [fp, #-8]
    // 0xaf2aa4: LoadField: r2 = r0->field_f
    //     0xaf2aa4: ldur            w2, [x0, #0xf]
    // 0xaf2aa8: DecompressPointer r2
    //     0xaf2aa8: add             x2, x2, HEAP, lsl #32
    // 0xaf2aac: LoadField: r3 = r2->field_b
    //     0xaf2aac: ldur            w3, [x2, #0xb]
    // 0xaf2ab0: DecompressPointer r3
    //     0xaf2ab0: add             x3, x3, HEAP, lsl #32
    // 0xaf2ab4: cmp             w3, NULL
    // 0xaf2ab8: b.eq            #0xaf2cac
    // 0xaf2abc: LoadField: r0 = r3->field_b
    //     0xaf2abc: ldur            w0, [x3, #0xb]
    // 0xaf2ac0: DecompressPointer r0
    //     0xaf2ac0: add             x0, x0, HEAP, lsl #32
    // 0xaf2ac4: LoadField: r4 = r0->field_73
    //     0xaf2ac4: ldur            w4, [x0, #0x73]
    // 0xaf2ac8: DecompressPointer r4
    //     0xaf2ac8: add             x4, x4, HEAP, lsl #32
    // 0xaf2acc: cmp             w4, NULL
    // 0xaf2ad0: b.ne            #0xaf2adc
    // 0xaf2ad4: r0 = Null
    //     0xaf2ad4: mov             x0, NULL
    // 0xaf2ad8: b               #0xaf2b14
    // 0xaf2adc: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xaf2adc: ldur            x5, [x2, #0x17]
    // 0xaf2ae0: LoadField: r0 = r4->field_b
    //     0xaf2ae0: ldur            w0, [x4, #0xb]
    // 0xaf2ae4: r1 = LoadInt32Instr(r0)
    //     0xaf2ae4: sbfx            x1, x0, #1, #0x1f
    // 0xaf2ae8: mov             x0, x1
    // 0xaf2aec: mov             x1, x5
    // 0xaf2af0: cmp             x1, x0
    // 0xaf2af4: b.hs            #0xaf2cb0
    // 0xaf2af8: LoadField: r0 = r4->field_f
    //     0xaf2af8: ldur            w0, [x4, #0xf]
    // 0xaf2afc: DecompressPointer r0
    //     0xaf2afc: add             x0, x0, HEAP, lsl #32
    // 0xaf2b00: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaf2b00: add             x16, x0, x5, lsl #2
    //     0xaf2b04: ldur            w1, [x16, #0xf]
    // 0xaf2b08: DecompressPointer r1
    //     0xaf2b08: add             x1, x1, HEAP, lsl #32
    // 0xaf2b0c: LoadField: r0 = r1->field_b
    //     0xaf2b0c: ldur            w0, [x1, #0xb]
    // 0xaf2b10: DecompressPointer r0
    //     0xaf2b10: add             x0, x0, HEAP, lsl #32
    // 0xaf2b14: cmp             w0, NULL
    // 0xaf2b18: b.ne            #0xaf2b24
    // 0xaf2b1c: r5 = ""
    //     0xaf2b1c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2b20: b               #0xaf2b28
    // 0xaf2b24: mov             x5, x0
    // 0xaf2b28: cmp             w4, NULL
    // 0xaf2b2c: b.ne            #0xaf2b38
    // 0xaf2b30: r0 = Null
    //     0xaf2b30: mov             x0, NULL
    // 0xaf2b34: b               #0xaf2b70
    // 0xaf2b38: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xaf2b38: ldur            x6, [x2, #0x17]
    // 0xaf2b3c: LoadField: r0 = r4->field_b
    //     0xaf2b3c: ldur            w0, [x4, #0xb]
    // 0xaf2b40: r1 = LoadInt32Instr(r0)
    //     0xaf2b40: sbfx            x1, x0, #1, #0x1f
    // 0xaf2b44: mov             x0, x1
    // 0xaf2b48: mov             x1, x6
    // 0xaf2b4c: cmp             x1, x0
    // 0xaf2b50: b.hs            #0xaf2cb4
    // 0xaf2b54: LoadField: r0 = r4->field_f
    //     0xaf2b54: ldur            w0, [x4, #0xf]
    // 0xaf2b58: DecompressPointer r0
    //     0xaf2b58: add             x0, x0, HEAP, lsl #32
    // 0xaf2b5c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf2b5c: add             x16, x0, x6, lsl #2
    //     0xaf2b60: ldur            w1, [x16, #0xf]
    // 0xaf2b64: DecompressPointer r1
    //     0xaf2b64: add             x1, x1, HEAP, lsl #32
    // 0xaf2b68: LoadField: r0 = r1->field_7
    //     0xaf2b68: ldur            w0, [x1, #7]
    // 0xaf2b6c: DecompressPointer r0
    //     0xaf2b6c: add             x0, x0, HEAP, lsl #32
    // 0xaf2b70: cmp             w0, NULL
    // 0xaf2b74: b.ne            #0xaf2b7c
    // 0xaf2b78: r0 = ""
    //     0xaf2b78: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2b7c: LoadField: r1 = r3->field_1b
    //     0xaf2b7c: ldur            w1, [x3, #0x1b]
    // 0xaf2b80: DecompressPointer r1
    //     0xaf2b80: add             x1, x1, HEAP, lsl #32
    // 0xaf2b84: stp             x5, x1, [SP, #0x18]
    // 0xaf2b88: r16 = 2
    //     0xaf2b88: movz            x16, #0x2
    // 0xaf2b8c: stp             x16, x0, [SP, #8]
    // 0xaf2b90: r16 = ""
    //     0xaf2b90: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2b94: str             x16, [SP]
    // 0xaf2b98: mov             x0, x1
    // 0xaf2b9c: ClosureCall
    //     0xaf2b9c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53c40] List(7) [0, 0x5, 0x5, 0x4, "customisationId", 0x4, Null]
    //     0xaf2ba0: ldr             x4, [x4, #0xc40]
    //     0xaf2ba4: ldur            x2, [x0, #0x1f]
    //     0xaf2ba8: blr             x2
    // 0xaf2bac: r0 = Null
    //     0xaf2bac: mov             x0, NULL
    // 0xaf2bb0: LeaveFrame
    //     0xaf2bb0: mov             SP, fp
    //     0xaf2bb4: ldp             fp, lr, [SP], #0x10
    // 0xaf2bb8: ret
    //     0xaf2bb8: ret             
    // 0xaf2bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2bbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2bc0: b               #0xaf1858
    // 0xaf2bc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2bc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2bc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2bc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2bcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2bcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2bd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2bd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2bd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2be0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2be0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2be4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2be4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2be8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2be8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bf0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bf4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bf8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2bfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2bfc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2c1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2c20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c28: stp             q0, q1, [SP, #-0x20]!
    // 0xaf2c2c: SaveReg r2
    //     0xaf2c2c: str             x2, [SP, #-8]!
    // 0xaf2c30: r0 = AllocateDouble()
    //     0xaf2c30: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaf2c34: RestoreReg r2
    //     0xaf2c34: ldr             x2, [SP], #8
    // 0xaf2c38: ldp             q0, q1, [SP], #0x20
    // 0xaf2c3c: b               #0xaf261c
    // 0xaf2c40: SaveReg d0
    //     0xaf2c40: str             q0, [SP, #-0x10]!
    // 0xaf2c44: stp             x0, x1, [SP, #-0x10]!
    // 0xaf2c48: r0 = AllocateDouble()
    //     0xaf2c48: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaf2c4c: mov             x2, x0
    // 0xaf2c50: ldp             x0, x1, [SP], #0x10
    // 0xaf2c54: RestoreReg d0
    //     0xaf2c54: ldr             q0, [SP], #0x10
    // 0xaf2c58: b               #0xaf268c
    // 0xaf2c5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2c5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2c60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2c70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2c74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2c78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2c7c: stp             q0, q1, [SP, #-0x20]!
    // 0xaf2c80: SaveReg r2
    //     0xaf2c80: str             x2, [SP, #-8]!
    // 0xaf2c84: r0 = AllocateDouble()
    //     0xaf2c84: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaf2c88: RestoreReg r2
    //     0xaf2c88: ldr             x2, [SP], #8
    // 0xaf2c8c: ldp             q0, q1, [SP], #0x20
    // 0xaf2c90: b               #0xaf2a0c
    // 0xaf2c94: SaveReg d0
    //     0xaf2c94: str             q0, [SP, #-0x10]!
    // 0xaf2c98: stp             x1, x2, [SP, #-0x10]!
    // 0xaf2c9c: r0 = AllocateDouble()
    //     0xaf2c9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaf2ca0: ldp             x1, x2, [SP], #0x10
    // 0xaf2ca4: RestoreReg d0
    //     0xaf2ca4: ldr             q0, [SP], #0x10
    // 0xaf2ca8: b               #0xaf2a80
    // 0xaf2cac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2cac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2cb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2cb0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2cb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2cb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf2cb8, size: 0x2d4
    // 0xaf2cb8: EnterFrame
    //     0xaf2cb8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2cbc: mov             fp, SP
    // 0xaf2cc0: AllocStack(0x40)
    //     0xaf2cc0: sub             SP, SP, #0x40
    // 0xaf2cc4: SetupParameters()
    //     0xaf2cc4: ldr             x0, [fp, #0x20]
    //     0xaf2cc8: ldur            w1, [x0, #0x17]
    //     0xaf2ccc: add             x1, x1, HEAP, lsl #32
    //     0xaf2cd0: stur            x1, [fp, #-8]
    // 0xaf2cd4: CheckStackOverflow
    //     0xaf2cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2cd8: cmp             SP, x16
    //     0xaf2cdc: b.ls            #0xaf2f60
    // 0xaf2ce0: r1 = 2
    //     0xaf2ce0: movz            x1, #0x2
    // 0xaf2ce4: r0 = AllocateContext()
    //     0xaf2ce4: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf2ce8: mov             x3, x0
    // 0xaf2cec: ldur            x2, [fp, #-8]
    // 0xaf2cf0: stur            x3, [fp, #-0x18]
    // 0xaf2cf4: StoreField: r3->field_b = r2
    //     0xaf2cf4: stur            w2, [x3, #0xb]
    // 0xaf2cf8: ldr             x4, [fp, #0x10]
    // 0xaf2cfc: StoreField: r3->field_f = r4
    //     0xaf2cfc: stur            w4, [x3, #0xf]
    // 0xaf2d00: LoadField: r5 = r2->field_f
    //     0xaf2d00: ldur            w5, [x2, #0xf]
    // 0xaf2d04: DecompressPointer r5
    //     0xaf2d04: add             x5, x5, HEAP, lsl #32
    // 0xaf2d08: LoadField: r0 = r5->field_b
    //     0xaf2d08: ldur            w0, [x5, #0xb]
    // 0xaf2d0c: DecompressPointer r0
    //     0xaf2d0c: add             x0, x0, HEAP, lsl #32
    // 0xaf2d10: cmp             w0, NULL
    // 0xaf2d14: b.eq            #0xaf2f68
    // 0xaf2d18: LoadField: r1 = r0->field_b
    //     0xaf2d18: ldur            w1, [x0, #0xb]
    // 0xaf2d1c: DecompressPointer r1
    //     0xaf2d1c: add             x1, x1, HEAP, lsl #32
    // 0xaf2d20: LoadField: r6 = r1->field_73
    //     0xaf2d20: ldur            w6, [x1, #0x73]
    // 0xaf2d24: DecompressPointer r6
    //     0xaf2d24: add             x6, x6, HEAP, lsl #32
    // 0xaf2d28: cmp             w6, NULL
    // 0xaf2d2c: b.ne            #0xaf2d38
    // 0xaf2d30: r0 = Null
    //     0xaf2d30: mov             x0, NULL
    // 0xaf2d34: b               #0xaf2dbc
    // 0xaf2d38: LoadField: r7 = r5->field_1f
    //     0xaf2d38: ldur            x7, [x5, #0x1f]
    // 0xaf2d3c: LoadField: r0 = r6->field_b
    //     0xaf2d3c: ldur            w0, [x6, #0xb]
    // 0xaf2d40: r1 = LoadInt32Instr(r0)
    //     0xaf2d40: sbfx            x1, x0, #1, #0x1f
    // 0xaf2d44: mov             x0, x1
    // 0xaf2d48: mov             x1, x7
    // 0xaf2d4c: cmp             x1, x0
    // 0xaf2d50: b.hs            #0xaf2f6c
    // 0xaf2d54: LoadField: r0 = r6->field_f
    //     0xaf2d54: ldur            w0, [x6, #0xf]
    // 0xaf2d58: DecompressPointer r0
    //     0xaf2d58: add             x0, x0, HEAP, lsl #32
    // 0xaf2d5c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf2d5c: add             x16, x0, x7, lsl #2
    //     0xaf2d60: ldur            w1, [x16, #0xf]
    // 0xaf2d64: DecompressPointer r1
    //     0xaf2d64: add             x1, x1, HEAP, lsl #32
    // 0xaf2d68: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xaf2d68: ldur            w6, [x1, #0x17]
    // 0xaf2d6c: DecompressPointer r6
    //     0xaf2d6c: add             x6, x6, HEAP, lsl #32
    // 0xaf2d70: cmp             w6, NULL
    // 0xaf2d74: b.ne            #0xaf2d80
    // 0xaf2d78: r0 = Null
    //     0xaf2d78: mov             x0, NULL
    // 0xaf2d7c: b               #0xaf2dbc
    // 0xaf2d80: LoadField: r0 = r6->field_b
    //     0xaf2d80: ldur            w0, [x6, #0xb]
    // 0xaf2d84: r7 = LoadInt32Instr(r4)
    //     0xaf2d84: sbfx            x7, x4, #1, #0x1f
    //     0xaf2d88: tbz             w4, #0, #0xaf2d90
    //     0xaf2d8c: ldur            x7, [x4, #7]
    // 0xaf2d90: r1 = LoadInt32Instr(r0)
    //     0xaf2d90: sbfx            x1, x0, #1, #0x1f
    // 0xaf2d94: mov             x0, x1
    // 0xaf2d98: mov             x1, x7
    // 0xaf2d9c: cmp             x1, x0
    // 0xaf2da0: b.hs            #0xaf2f70
    // 0xaf2da4: LoadField: r0 = r6->field_f
    //     0xaf2da4: ldur            w0, [x6, #0xf]
    // 0xaf2da8: DecompressPointer r0
    //     0xaf2da8: add             x0, x0, HEAP, lsl #32
    // 0xaf2dac: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf2dac: add             x16, x0, x7, lsl #2
    //     0xaf2db0: ldur            w1, [x16, #0xf]
    // 0xaf2db4: DecompressPointer r1
    //     0xaf2db4: add             x1, x1, HEAP, lsl #32
    // 0xaf2db8: mov             x0, x1
    // 0xaf2dbc: stur            x0, [fp, #-0x10]
    // 0xaf2dc0: StoreField: r3->field_13 = r0
    //     0xaf2dc0: stur            w0, [x3, #0x13]
    // 0xaf2dc4: LoadField: r1 = r5->field_13
    //     0xaf2dc4: ldur            w1, [x5, #0x13]
    // 0xaf2dc8: DecompressPointer r1
    //     0xaf2dc8: add             x1, x1, HEAP, lsl #32
    // 0xaf2dcc: r16 = Sentinel
    //     0xaf2dcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf2dd0: cmp             w1, w16
    // 0xaf2dd4: b.eq            #0xaf2f74
    // 0xaf2dd8: cmp             w1, w0
    // 0xaf2ddc: b.ne            #0xaf2df4
    // 0xaf2de0: ldr             x1, [fp, #0x18]
    // 0xaf2de4: r0 = of()
    //     0xaf2de4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf2de8: LoadField: r1 = r0->field_5b
    //     0xaf2de8: ldur            w1, [x0, #0x5b]
    // 0xaf2dec: DecompressPointer r1
    //     0xaf2dec: add             x1, x1, HEAP, lsl #32
    // 0xaf2df0: b               #0xaf2df8
    // 0xaf2df4: r1 = Instance_Color
    //     0xaf2df4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf2df8: ldur            x0, [fp, #-0x10]
    // 0xaf2dfc: d0 = 0.000000
    //     0xaf2dfc: eor             v0.16b, v0.16b, v0.16b
    // 0xaf2e00: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaf2e00: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaf2e04: r0 = styleFrom()
    //     0xaf2e04: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xaf2e08: mov             x2, x0
    // 0xaf2e0c: ldur            x0, [fp, #-0x10]
    // 0xaf2e10: stur            x2, [fp, #-0x28]
    // 0xaf2e14: cmp             w0, NULL
    // 0xaf2e18: b.ne            #0xaf2e24
    // 0xaf2e1c: r1 = Null
    //     0xaf2e1c: mov             x1, NULL
    // 0xaf2e20: b               #0xaf2e2c
    // 0xaf2e24: LoadField: r1 = r0->field_7
    //     0xaf2e24: ldur            w1, [x0, #7]
    // 0xaf2e28: DecompressPointer r1
    //     0xaf2e28: add             x1, x1, HEAP, lsl #32
    // 0xaf2e2c: cmp             w1, NULL
    // 0xaf2e30: b.ne            #0xaf2e3c
    // 0xaf2e34: r4 = ""
    //     0xaf2e34: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf2e38: b               #0xaf2e40
    // 0xaf2e3c: mov             x4, x1
    // 0xaf2e40: ldur            x3, [fp, #-8]
    // 0xaf2e44: ldr             x1, [fp, #0x18]
    // 0xaf2e48: stur            x4, [fp, #-0x20]
    // 0xaf2e4c: r0 = of()
    //     0xaf2e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf2e50: LoadField: r1 = r0->field_87
    //     0xaf2e50: ldur            w1, [x0, #0x87]
    // 0xaf2e54: DecompressPointer r1
    //     0xaf2e54: add             x1, x1, HEAP, lsl #32
    // 0xaf2e58: LoadField: r0 = r1->field_2b
    //     0xaf2e58: ldur            w0, [x1, #0x2b]
    // 0xaf2e5c: DecompressPointer r0
    //     0xaf2e5c: add             x0, x0, HEAP, lsl #32
    // 0xaf2e60: ldur            x1, [fp, #-8]
    // 0xaf2e64: stur            x0, [fp, #-0x30]
    // 0xaf2e68: LoadField: r2 = r1->field_f
    //     0xaf2e68: ldur            w2, [x1, #0xf]
    // 0xaf2e6c: DecompressPointer r2
    //     0xaf2e6c: add             x2, x2, HEAP, lsl #32
    // 0xaf2e70: LoadField: r1 = r2->field_13
    //     0xaf2e70: ldur            w1, [x2, #0x13]
    // 0xaf2e74: DecompressPointer r1
    //     0xaf2e74: add             x1, x1, HEAP, lsl #32
    // 0xaf2e78: r16 = Sentinel
    //     0xaf2e78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf2e7c: cmp             w1, w16
    // 0xaf2e80: b.eq            #0xaf2f80
    // 0xaf2e84: ldur            x2, [fp, #-0x10]
    // 0xaf2e88: cmp             w1, w2
    // 0xaf2e8c: b.ne            #0xaf2e98
    // 0xaf2e90: r1 = Instance_Color
    //     0xaf2e90: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf2e94: b               #0xaf2ea8
    // 0xaf2e98: r1 = Instance_Color
    //     0xaf2e98: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf2e9c: d0 = 0.400000
    //     0xaf2e9c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaf2ea0: r0 = withOpacity()
    //     0xaf2ea0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf2ea4: mov             x1, x0
    // 0xaf2ea8: ldur            x0, [fp, #-0x28]
    // 0xaf2eac: ldur            x2, [fp, #-0x20]
    // 0xaf2eb0: r16 = 16.000000
    //     0xaf2eb0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf2eb4: ldr             x16, [x16, #0x188]
    // 0xaf2eb8: stp             x1, x16, [SP]
    // 0xaf2ebc: ldur            x1, [fp, #-0x30]
    // 0xaf2ec0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf2ec0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf2ec4: ldr             x4, [x4, #0xaa0]
    // 0xaf2ec8: r0 = copyWith()
    //     0xaf2ec8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf2ecc: stur            x0, [fp, #-8]
    // 0xaf2ed0: r0 = Text()
    //     0xaf2ed0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf2ed4: mov             x3, x0
    // 0xaf2ed8: ldur            x0, [fp, #-0x20]
    // 0xaf2edc: stur            x3, [fp, #-0x10]
    // 0xaf2ee0: StoreField: r3->field_b = r0
    //     0xaf2ee0: stur            w0, [x3, #0xb]
    // 0xaf2ee4: ldur            x0, [fp, #-8]
    // 0xaf2ee8: StoreField: r3->field_13 = r0
    //     0xaf2ee8: stur            w0, [x3, #0x13]
    // 0xaf2eec: r0 = Instance_TextAlign
    //     0xaf2eec: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaf2ef0: StoreField: r3->field_1b = r0
    //     0xaf2ef0: stur            w0, [x3, #0x1b]
    // 0xaf2ef4: ldur            x2, [fp, #-0x18]
    // 0xaf2ef8: r1 = Function '<anonymous closure>':.
    //     0xaf2ef8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58668] AnonymousClosure: (0xaf2f8c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf2efc: ldr             x1, [x1, #0x668]
    // 0xaf2f00: r0 = AllocateClosure()
    //     0xaf2f00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf2f04: stur            x0, [fp, #-8]
    // 0xaf2f08: r0 = ElevatedButton()
    //     0xaf2f08: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xaf2f0c: mov             x1, x0
    // 0xaf2f10: ldur            x0, [fp, #-8]
    // 0xaf2f14: stur            x1, [fp, #-0x18]
    // 0xaf2f18: StoreField: r1->field_b = r0
    //     0xaf2f18: stur            w0, [x1, #0xb]
    // 0xaf2f1c: ldur            x0, [fp, #-0x28]
    // 0xaf2f20: StoreField: r1->field_1b = r0
    //     0xaf2f20: stur            w0, [x1, #0x1b]
    // 0xaf2f24: r0 = false
    //     0xaf2f24: add             x0, NULL, #0x30  ; false
    // 0xaf2f28: StoreField: r1->field_27 = r0
    //     0xaf2f28: stur            w0, [x1, #0x27]
    // 0xaf2f2c: r0 = true
    //     0xaf2f2c: add             x0, NULL, #0x20  ; true
    // 0xaf2f30: StoreField: r1->field_2f = r0
    //     0xaf2f30: stur            w0, [x1, #0x2f]
    // 0xaf2f34: ldur            x0, [fp, #-0x10]
    // 0xaf2f38: StoreField: r1->field_37 = r0
    //     0xaf2f38: stur            w0, [x1, #0x37]
    // 0xaf2f3c: r0 = Padding()
    //     0xaf2f3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf2f40: r1 = Instance_EdgeInsets
    //     0xaf2f40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaf2f44: ldr             x1, [x1, #0x980]
    // 0xaf2f48: StoreField: r0->field_f = r1
    //     0xaf2f48: stur            w1, [x0, #0xf]
    // 0xaf2f4c: ldur            x1, [fp, #-0x18]
    // 0xaf2f50: StoreField: r0->field_b = r1
    //     0xaf2f50: stur            w1, [x0, #0xb]
    // 0xaf2f54: LeaveFrame
    //     0xaf2f54: mov             SP, fp
    //     0xaf2f58: ldp             fp, lr, [SP], #0x10
    // 0xaf2f5c: ret
    //     0xaf2f5c: ret             
    // 0xaf2f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2f60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2f64: b               #0xaf2ce0
    // 0xaf2f68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2f68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2f6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2f6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2f70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf2f70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf2f74: r9 = dropDownValue
    //     0xaf2f74: add             x9, PP, #0x58, lsl #12  ; [pp+0x58670] Field <<EMAIL>>: late (offset: 0x14)
    //     0xaf2f78: ldr             x9, [x9, #0x670]
    // 0xaf2f7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf2f7c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaf2f80: r9 = dropDownValue
    //     0xaf2f80: add             x9, PP, #0x58, lsl #12  ; [pp+0x58670] Field <<EMAIL>>: late (offset: 0x14)
    //     0xaf2f84: ldr             x9, [x9, #0x670]
    // 0xaf2f88: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf2f88: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf2f8c, size: 0x110
    // 0xaf2f8c: EnterFrame
    //     0xaf2f8c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2f90: mov             fp, SP
    // 0xaf2f94: AllocStack(0x40)
    //     0xaf2f94: sub             SP, SP, #0x40
    // 0xaf2f98: SetupParameters()
    //     0xaf2f98: ldr             x0, [fp, #0x10]
    //     0xaf2f9c: ldur            w2, [x0, #0x17]
    //     0xaf2fa0: add             x2, x2, HEAP, lsl #32
    //     0xaf2fa4: stur            x2, [fp, #-0x28]
    // 0xaf2fa8: CheckStackOverflow
    //     0xaf2fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2fac: cmp             SP, x16
    //     0xaf2fb0: b.ls            #0xaf3090
    // 0xaf2fb4: LoadField: r0 = r2->field_b
    //     0xaf2fb4: ldur            w0, [x2, #0xb]
    // 0xaf2fb8: DecompressPointer r0
    //     0xaf2fb8: add             x0, x0, HEAP, lsl #32
    // 0xaf2fbc: stur            x0, [fp, #-0x20]
    // 0xaf2fc0: LoadField: r1 = r0->field_f
    //     0xaf2fc0: ldur            w1, [x0, #0xf]
    // 0xaf2fc4: DecompressPointer r1
    //     0xaf2fc4: add             x1, x1, HEAP, lsl #32
    // 0xaf2fc8: LoadField: r3 = r1->field_b
    //     0xaf2fc8: ldur            w3, [x1, #0xb]
    // 0xaf2fcc: DecompressPointer r3
    //     0xaf2fcc: add             x3, x3, HEAP, lsl #32
    // 0xaf2fd0: stur            x3, [fp, #-0x18]
    // 0xaf2fd4: cmp             w3, NULL
    // 0xaf2fd8: b.eq            #0xaf3098
    // 0xaf2fdc: LoadField: r4 = r1->field_2b
    //     0xaf2fdc: ldur            w4, [x1, #0x2b]
    // 0xaf2fe0: DecompressPointer r4
    //     0xaf2fe0: add             x4, x4, HEAP, lsl #32
    // 0xaf2fe4: stur            x4, [fp, #-0x10]
    // 0xaf2fe8: LoadField: r1 = r3->field_f
    //     0xaf2fe8: ldur            w1, [x3, #0xf]
    // 0xaf2fec: DecompressPointer r1
    //     0xaf2fec: add             x1, x1, HEAP, lsl #32
    // 0xaf2ff0: stur            x1, [fp, #-8]
    // 0xaf2ff4: r0 = EventData()
    //     0xaf2ff4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xaf2ff8: mov             x1, x0
    // 0xaf2ffc: ldur            x0, [fp, #-8]
    // 0xaf3000: stur            x1, [fp, #-0x30]
    // 0xaf3004: StoreField: r1->field_13 = r0
    //     0xaf3004: stur            w0, [x1, #0x13]
    // 0xaf3008: ldur            x0, [fp, #-0x10]
    // 0xaf300c: StoreField: r1->field_3b = r0
    //     0xaf300c: stur            w0, [x1, #0x3b]
    // 0xaf3010: r0 = EventsRequest()
    //     0xaf3010: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xaf3014: mov             x1, x0
    // 0xaf3018: r0 = "size_variation_clicked"
    //     0xaf3018: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xaf301c: ldr             x0, [x0, #0xa28]
    // 0xaf3020: StoreField: r1->field_7 = r0
    //     0xaf3020: stur            w0, [x1, #7]
    // 0xaf3024: ldur            x0, [fp, #-0x30]
    // 0xaf3028: StoreField: r1->field_b = r0
    //     0xaf3028: stur            w0, [x1, #0xb]
    // 0xaf302c: ldur            x0, [fp, #-0x18]
    // 0xaf3030: LoadField: r2 = r0->field_13
    //     0xaf3030: ldur            w2, [x0, #0x13]
    // 0xaf3034: DecompressPointer r2
    //     0xaf3034: add             x2, x2, HEAP, lsl #32
    // 0xaf3038: stp             x1, x2, [SP]
    // 0xaf303c: r4 = 0
    //     0xaf303c: movz            x4, #0
    // 0xaf3040: ldr             x0, [SP, #8]
    // 0xaf3044: r16 = UnlinkedCall_0x613b5c
    //     0xaf3044: add             x16, PP, #0x58, lsl #12  ; [pp+0x58678] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaf3048: add             x16, x16, #0x678
    // 0xaf304c: ldp             x5, lr, [x16]
    // 0xaf3050: blr             lr
    // 0xaf3054: ldur            x0, [fp, #-0x20]
    // 0xaf3058: LoadField: r3 = r0->field_f
    //     0xaf3058: ldur            w3, [x0, #0xf]
    // 0xaf305c: DecompressPointer r3
    //     0xaf305c: add             x3, x3, HEAP, lsl #32
    // 0xaf3060: ldur            x2, [fp, #-0x28]
    // 0xaf3064: stur            x3, [fp, #-8]
    // 0xaf3068: r1 = Function '<anonymous closure>':.
    //     0xaf3068: add             x1, PP, #0x58, lsl #12  ; [pp+0x58688] AnonymousClosure: (0xa60844), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xaf306c: ldr             x1, [x1, #0x688]
    // 0xaf3070: r0 = AllocateClosure()
    //     0xaf3070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3074: ldur            x1, [fp, #-8]
    // 0xaf3078: mov             x2, x0
    // 0xaf307c: r0 = setState()
    //     0xaf307c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf3080: r0 = Null
    //     0xaf3080: mov             x0, NULL
    // 0xaf3084: LeaveFrame
    //     0xaf3084: mov             SP, fp
    //     0xaf3088: ldp             fp, lr, [SP], #0x10
    // 0xaf308c: ret
    //     0xaf308c: ret             
    // 0xaf3090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3094: b               #0xaf2fb4
    // 0xaf3098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3098: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf309c, size: 0x284
    // 0xaf309c: EnterFrame
    //     0xaf309c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf30a0: mov             fp, SP
    // 0xaf30a4: AllocStack(0x40)
    //     0xaf30a4: sub             SP, SP, #0x40
    // 0xaf30a8: SetupParameters()
    //     0xaf30a8: ldr             x0, [fp, #0x20]
    //     0xaf30ac: ldur            w1, [x0, #0x17]
    //     0xaf30b0: add             x1, x1, HEAP, lsl #32
    //     0xaf30b4: stur            x1, [fp, #-8]
    // 0xaf30b8: CheckStackOverflow
    //     0xaf30b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf30bc: cmp             SP, x16
    //     0xaf30c0: b.ls            #0xaf32f8
    // 0xaf30c4: r1 = 2
    //     0xaf30c4: movz            x1, #0x2
    // 0xaf30c8: r0 = AllocateContext()
    //     0xaf30c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf30cc: mov             x3, x0
    // 0xaf30d0: ldur            x2, [fp, #-8]
    // 0xaf30d4: stur            x3, [fp, #-0x18]
    // 0xaf30d8: StoreField: r3->field_b = r2
    //     0xaf30d8: stur            w2, [x3, #0xb]
    // 0xaf30dc: ldr             x0, [fp, #0x10]
    // 0xaf30e0: StoreField: r3->field_f = r0
    //     0xaf30e0: stur            w0, [x3, #0xf]
    // 0xaf30e4: LoadField: r4 = r2->field_f
    //     0xaf30e4: ldur            w4, [x2, #0xf]
    // 0xaf30e8: DecompressPointer r4
    //     0xaf30e8: add             x4, x4, HEAP, lsl #32
    // 0xaf30ec: LoadField: r1 = r4->field_b
    //     0xaf30ec: ldur            w1, [x4, #0xb]
    // 0xaf30f0: DecompressPointer r1
    //     0xaf30f0: add             x1, x1, HEAP, lsl #32
    // 0xaf30f4: cmp             w1, NULL
    // 0xaf30f8: b.eq            #0xaf3300
    // 0xaf30fc: LoadField: r5 = r1->field_b
    //     0xaf30fc: ldur            w5, [x1, #0xb]
    // 0xaf3100: DecompressPointer r5
    //     0xaf3100: add             x5, x5, HEAP, lsl #32
    // 0xaf3104: LoadField: r6 = r5->field_6f
    //     0xaf3104: ldur            w6, [x5, #0x6f]
    // 0xaf3108: DecompressPointer r6
    //     0xaf3108: add             x6, x6, HEAP, lsl #32
    // 0xaf310c: cmp             w6, NULL
    // 0xaf3110: b.ne            #0xaf311c
    // 0xaf3114: r0 = Null
    //     0xaf3114: mov             x0, NULL
    // 0xaf3118: b               #0xaf3154
    // 0xaf311c: LoadField: r1 = r6->field_b
    //     0xaf311c: ldur            w1, [x6, #0xb]
    // 0xaf3120: r5 = LoadInt32Instr(r0)
    //     0xaf3120: sbfx            x5, x0, #1, #0x1f
    //     0xaf3124: tbz             w0, #0, #0xaf312c
    //     0xaf3128: ldur            x5, [x0, #7]
    // 0xaf312c: r0 = LoadInt32Instr(r1)
    //     0xaf312c: sbfx            x0, x1, #1, #0x1f
    // 0xaf3130: mov             x1, x5
    // 0xaf3134: cmp             x1, x0
    // 0xaf3138: b.hs            #0xaf3304
    // 0xaf313c: LoadField: r0 = r6->field_f
    //     0xaf313c: ldur            w0, [x6, #0xf]
    // 0xaf3140: DecompressPointer r0
    //     0xaf3140: add             x0, x0, HEAP, lsl #32
    // 0xaf3144: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaf3144: add             x16, x0, x5, lsl #2
    //     0xaf3148: ldur            w1, [x16, #0xf]
    // 0xaf314c: DecompressPointer r1
    //     0xaf314c: add             x1, x1, HEAP, lsl #32
    // 0xaf3150: mov             x0, x1
    // 0xaf3154: stur            x0, [fp, #-0x10]
    // 0xaf3158: StoreField: r3->field_13 = r0
    //     0xaf3158: stur            w0, [x3, #0x13]
    // 0xaf315c: LoadField: r1 = r4->field_13
    //     0xaf315c: ldur            w1, [x4, #0x13]
    // 0xaf3160: DecompressPointer r1
    //     0xaf3160: add             x1, x1, HEAP, lsl #32
    // 0xaf3164: r16 = Sentinel
    //     0xaf3164: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf3168: cmp             w1, w16
    // 0xaf316c: b.eq            #0xaf3308
    // 0xaf3170: cmp             w1, w0
    // 0xaf3174: b.ne            #0xaf318c
    // 0xaf3178: ldr             x1, [fp, #0x18]
    // 0xaf317c: r0 = of()
    //     0xaf317c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf3180: LoadField: r1 = r0->field_5b
    //     0xaf3180: ldur            w1, [x0, #0x5b]
    // 0xaf3184: DecompressPointer r1
    //     0xaf3184: add             x1, x1, HEAP, lsl #32
    // 0xaf3188: b               #0xaf3190
    // 0xaf318c: r1 = Instance_Color
    //     0xaf318c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf3190: ldur            x0, [fp, #-0x10]
    // 0xaf3194: d0 = 0.000000
    //     0xaf3194: eor             v0.16b, v0.16b, v0.16b
    // 0xaf3198: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaf3198: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaf319c: r0 = styleFrom()
    //     0xaf319c: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xaf31a0: mov             x2, x0
    // 0xaf31a4: ldur            x0, [fp, #-0x10]
    // 0xaf31a8: stur            x2, [fp, #-0x28]
    // 0xaf31ac: cmp             w0, NULL
    // 0xaf31b0: b.ne            #0xaf31bc
    // 0xaf31b4: r1 = Null
    //     0xaf31b4: mov             x1, NULL
    // 0xaf31b8: b               #0xaf31c4
    // 0xaf31bc: LoadField: r1 = r0->field_7
    //     0xaf31bc: ldur            w1, [x0, #7]
    // 0xaf31c0: DecompressPointer r1
    //     0xaf31c0: add             x1, x1, HEAP, lsl #32
    // 0xaf31c4: cmp             w1, NULL
    // 0xaf31c8: b.ne            #0xaf31d4
    // 0xaf31cc: r4 = ""
    //     0xaf31cc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf31d0: b               #0xaf31d8
    // 0xaf31d4: mov             x4, x1
    // 0xaf31d8: ldur            x3, [fp, #-8]
    // 0xaf31dc: ldr             x1, [fp, #0x18]
    // 0xaf31e0: stur            x4, [fp, #-0x20]
    // 0xaf31e4: r0 = of()
    //     0xaf31e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf31e8: LoadField: r1 = r0->field_87
    //     0xaf31e8: ldur            w1, [x0, #0x87]
    // 0xaf31ec: DecompressPointer r1
    //     0xaf31ec: add             x1, x1, HEAP, lsl #32
    // 0xaf31f0: LoadField: r0 = r1->field_2b
    //     0xaf31f0: ldur            w0, [x1, #0x2b]
    // 0xaf31f4: DecompressPointer r0
    //     0xaf31f4: add             x0, x0, HEAP, lsl #32
    // 0xaf31f8: ldur            x1, [fp, #-8]
    // 0xaf31fc: stur            x0, [fp, #-0x30]
    // 0xaf3200: LoadField: r2 = r1->field_f
    //     0xaf3200: ldur            w2, [x1, #0xf]
    // 0xaf3204: DecompressPointer r2
    //     0xaf3204: add             x2, x2, HEAP, lsl #32
    // 0xaf3208: LoadField: r1 = r2->field_13
    //     0xaf3208: ldur            w1, [x2, #0x13]
    // 0xaf320c: DecompressPointer r1
    //     0xaf320c: add             x1, x1, HEAP, lsl #32
    // 0xaf3210: r16 = Sentinel
    //     0xaf3210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf3214: cmp             w1, w16
    // 0xaf3218: b.eq            #0xaf3314
    // 0xaf321c: ldur            x2, [fp, #-0x10]
    // 0xaf3220: cmp             w1, w2
    // 0xaf3224: b.ne            #0xaf3230
    // 0xaf3228: r1 = Instance_Color
    //     0xaf3228: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf322c: b               #0xaf3240
    // 0xaf3230: r1 = Instance_Color
    //     0xaf3230: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf3234: d0 = 0.400000
    //     0xaf3234: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaf3238: r0 = withOpacity()
    //     0xaf3238: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf323c: mov             x1, x0
    // 0xaf3240: ldur            x0, [fp, #-0x28]
    // 0xaf3244: ldur            x2, [fp, #-0x20]
    // 0xaf3248: r16 = 16.000000
    //     0xaf3248: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf324c: ldr             x16, [x16, #0x188]
    // 0xaf3250: stp             x1, x16, [SP]
    // 0xaf3254: ldur            x1, [fp, #-0x30]
    // 0xaf3258: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf3258: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf325c: ldr             x4, [x4, #0xaa0]
    // 0xaf3260: r0 = copyWith()
    //     0xaf3260: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf3264: stur            x0, [fp, #-8]
    // 0xaf3268: r0 = Text()
    //     0xaf3268: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf326c: mov             x3, x0
    // 0xaf3270: ldur            x0, [fp, #-0x20]
    // 0xaf3274: stur            x3, [fp, #-0x10]
    // 0xaf3278: StoreField: r3->field_b = r0
    //     0xaf3278: stur            w0, [x3, #0xb]
    // 0xaf327c: ldur            x0, [fp, #-8]
    // 0xaf3280: StoreField: r3->field_13 = r0
    //     0xaf3280: stur            w0, [x3, #0x13]
    // 0xaf3284: r0 = Instance_TextAlign
    //     0xaf3284: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaf3288: StoreField: r3->field_1b = r0
    //     0xaf3288: stur            w0, [x3, #0x1b]
    // 0xaf328c: ldur            x2, [fp, #-0x18]
    // 0xaf3290: r1 = Function '<anonymous closure>':.
    //     0xaf3290: add             x1, PP, #0x58, lsl #12  ; [pp+0x58690] AnonymousClosure: (0xaf3320), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf3294: ldr             x1, [x1, #0x690]
    // 0xaf3298: r0 = AllocateClosure()
    //     0xaf3298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf329c: stur            x0, [fp, #-8]
    // 0xaf32a0: r0 = ElevatedButton()
    //     0xaf32a0: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xaf32a4: mov             x1, x0
    // 0xaf32a8: ldur            x0, [fp, #-8]
    // 0xaf32ac: stur            x1, [fp, #-0x18]
    // 0xaf32b0: StoreField: r1->field_b = r0
    //     0xaf32b0: stur            w0, [x1, #0xb]
    // 0xaf32b4: ldur            x0, [fp, #-0x28]
    // 0xaf32b8: StoreField: r1->field_1b = r0
    //     0xaf32b8: stur            w0, [x1, #0x1b]
    // 0xaf32bc: r0 = false
    //     0xaf32bc: add             x0, NULL, #0x30  ; false
    // 0xaf32c0: StoreField: r1->field_27 = r0
    //     0xaf32c0: stur            w0, [x1, #0x27]
    // 0xaf32c4: r0 = true
    //     0xaf32c4: add             x0, NULL, #0x20  ; true
    // 0xaf32c8: StoreField: r1->field_2f = r0
    //     0xaf32c8: stur            w0, [x1, #0x2f]
    // 0xaf32cc: ldur            x0, [fp, #-0x10]
    // 0xaf32d0: StoreField: r1->field_37 = r0
    //     0xaf32d0: stur            w0, [x1, #0x37]
    // 0xaf32d4: r0 = Padding()
    //     0xaf32d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf32d8: r1 = Instance_EdgeInsets
    //     0xaf32d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaf32dc: ldr             x1, [x1, #0x980]
    // 0xaf32e0: StoreField: r0->field_f = r1
    //     0xaf32e0: stur            w1, [x0, #0xf]
    // 0xaf32e4: ldur            x1, [fp, #-0x18]
    // 0xaf32e8: StoreField: r0->field_b = r1
    //     0xaf32e8: stur            w1, [x0, #0xb]
    // 0xaf32ec: LeaveFrame
    //     0xaf32ec: mov             SP, fp
    //     0xaf32f0: ldp             fp, lr, [SP], #0x10
    // 0xaf32f4: ret
    //     0xaf32f4: ret             
    // 0xaf32f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf32f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf32fc: b               #0xaf30c4
    // 0xaf3300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf3304: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3304: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf3308: r9 = dropDownValue
    //     0xaf3308: add             x9, PP, #0x58, lsl #12  ; [pp+0x58670] Field <<EMAIL>>: late (offset: 0x14)
    //     0xaf330c: ldr             x9, [x9, #0x670]
    // 0xaf3310: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf3310: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaf3314: r9 = dropDownValue
    //     0xaf3314: add             x9, PP, #0x58, lsl #12  ; [pp+0x58670] Field <<EMAIL>>: late (offset: 0x14)
    //     0xaf3318: ldr             x9, [x9, #0x670]
    // 0xaf331c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf331c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf3320, size: 0x110
    // 0xaf3320: EnterFrame
    //     0xaf3320: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3324: mov             fp, SP
    // 0xaf3328: AllocStack(0x40)
    //     0xaf3328: sub             SP, SP, #0x40
    // 0xaf332c: SetupParameters()
    //     0xaf332c: ldr             x0, [fp, #0x10]
    //     0xaf3330: ldur            w2, [x0, #0x17]
    //     0xaf3334: add             x2, x2, HEAP, lsl #32
    //     0xaf3338: stur            x2, [fp, #-0x28]
    // 0xaf333c: CheckStackOverflow
    //     0xaf333c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3340: cmp             SP, x16
    //     0xaf3344: b.ls            #0xaf3424
    // 0xaf3348: LoadField: r0 = r2->field_b
    //     0xaf3348: ldur            w0, [x2, #0xb]
    // 0xaf334c: DecompressPointer r0
    //     0xaf334c: add             x0, x0, HEAP, lsl #32
    // 0xaf3350: stur            x0, [fp, #-0x20]
    // 0xaf3354: LoadField: r1 = r0->field_f
    //     0xaf3354: ldur            w1, [x0, #0xf]
    // 0xaf3358: DecompressPointer r1
    //     0xaf3358: add             x1, x1, HEAP, lsl #32
    // 0xaf335c: LoadField: r3 = r1->field_b
    //     0xaf335c: ldur            w3, [x1, #0xb]
    // 0xaf3360: DecompressPointer r3
    //     0xaf3360: add             x3, x3, HEAP, lsl #32
    // 0xaf3364: stur            x3, [fp, #-0x18]
    // 0xaf3368: cmp             w3, NULL
    // 0xaf336c: b.eq            #0xaf342c
    // 0xaf3370: LoadField: r4 = r1->field_2b
    //     0xaf3370: ldur            w4, [x1, #0x2b]
    // 0xaf3374: DecompressPointer r4
    //     0xaf3374: add             x4, x4, HEAP, lsl #32
    // 0xaf3378: stur            x4, [fp, #-0x10]
    // 0xaf337c: LoadField: r1 = r3->field_f
    //     0xaf337c: ldur            w1, [x3, #0xf]
    // 0xaf3380: DecompressPointer r1
    //     0xaf3380: add             x1, x1, HEAP, lsl #32
    // 0xaf3384: stur            x1, [fp, #-8]
    // 0xaf3388: r0 = EventData()
    //     0xaf3388: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xaf338c: mov             x1, x0
    // 0xaf3390: ldur            x0, [fp, #-8]
    // 0xaf3394: stur            x1, [fp, #-0x30]
    // 0xaf3398: StoreField: r1->field_13 = r0
    //     0xaf3398: stur            w0, [x1, #0x13]
    // 0xaf339c: ldur            x0, [fp, #-0x10]
    // 0xaf33a0: StoreField: r1->field_3b = r0
    //     0xaf33a0: stur            w0, [x1, #0x3b]
    // 0xaf33a4: r0 = EventsRequest()
    //     0xaf33a4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xaf33a8: mov             x1, x0
    // 0xaf33ac: r0 = "size_variation_clicked"
    //     0xaf33ac: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xaf33b0: ldr             x0, [x0, #0xa28]
    // 0xaf33b4: StoreField: r1->field_7 = r0
    //     0xaf33b4: stur            w0, [x1, #7]
    // 0xaf33b8: ldur            x0, [fp, #-0x30]
    // 0xaf33bc: StoreField: r1->field_b = r0
    //     0xaf33bc: stur            w0, [x1, #0xb]
    // 0xaf33c0: ldur            x0, [fp, #-0x18]
    // 0xaf33c4: LoadField: r2 = r0->field_13
    //     0xaf33c4: ldur            w2, [x0, #0x13]
    // 0xaf33c8: DecompressPointer r2
    //     0xaf33c8: add             x2, x2, HEAP, lsl #32
    // 0xaf33cc: stp             x1, x2, [SP]
    // 0xaf33d0: r4 = 0
    //     0xaf33d0: movz            x4, #0
    // 0xaf33d4: ldr             x0, [SP, #8]
    // 0xaf33d8: r16 = UnlinkedCall_0x613b5c
    //     0xaf33d8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58698] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaf33dc: add             x16, x16, #0x698
    // 0xaf33e0: ldp             x5, lr, [x16]
    // 0xaf33e4: blr             lr
    // 0xaf33e8: ldur            x0, [fp, #-0x20]
    // 0xaf33ec: LoadField: r3 = r0->field_f
    //     0xaf33ec: ldur            w3, [x0, #0xf]
    // 0xaf33f0: DecompressPointer r3
    //     0xaf33f0: add             x3, x3, HEAP, lsl #32
    // 0xaf33f4: ldur            x2, [fp, #-0x28]
    // 0xaf33f8: stur            x3, [fp, #-8]
    // 0xaf33fc: r1 = Function '<anonymous closure>':.
    //     0xaf33fc: add             x1, PP, #0x58, lsl #12  ; [pp+0x586a8] AnonymousClosure: (0xa610b8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xaf3400: ldr             x1, [x1, #0x6a8]
    // 0xaf3404: r0 = AllocateClosure()
    //     0xaf3404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3408: ldur            x1, [fp, #-8]
    // 0xaf340c: mov             x2, x0
    // 0xaf3410: r0 = setState()
    //     0xaf3410: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf3414: r0 = Null
    //     0xaf3414: mov             x0, NULL
    // 0xaf3418: LeaveFrame
    //     0xaf3418: mov             SP, fp
    //     0xaf341c: ldp             fp, lr, [SP], #0x10
    // 0xaf3420: ret
    //     0xaf3420: ret             
    // 0xaf3424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3424: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3428: b               #0xaf3348
    // 0xaf342c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf342c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf3430, size: 0x7a8
    // 0xaf3430: EnterFrame
    //     0xaf3430: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3434: mov             fp, SP
    // 0xaf3438: AllocStack(0x60)
    //     0xaf3438: sub             SP, SP, #0x60
    // 0xaf343c: SetupParameters()
    //     0xaf343c: ldr             x0, [fp, #0x20]
    //     0xaf3440: ldur            w1, [x0, #0x17]
    //     0xaf3444: add             x1, x1, HEAP, lsl #32
    //     0xaf3448: stur            x1, [fp, #-8]
    // 0xaf344c: CheckStackOverflow
    //     0xaf344c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3450: cmp             SP, x16
    //     0xaf3454: b.ls            #0xaf3bb0
    // 0xaf3458: r1 = 1
    //     0xaf3458: movz            x1, #0x1
    // 0xaf345c: r0 = AllocateContext()
    //     0xaf345c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf3460: mov             x4, x0
    // 0xaf3464: ldur            x3, [fp, #-8]
    // 0xaf3468: stur            x4, [fp, #-0x10]
    // 0xaf346c: StoreField: r4->field_b = r3
    //     0xaf346c: stur            w3, [x4, #0xb]
    // 0xaf3470: ldr             x5, [fp, #0x10]
    // 0xaf3474: StoreField: r4->field_f = r5
    //     0xaf3474: stur            w5, [x4, #0xf]
    // 0xaf3478: LoadField: r0 = r3->field_f
    //     0xaf3478: ldur            w0, [x3, #0xf]
    // 0xaf347c: DecompressPointer r0
    //     0xaf347c: add             x0, x0, HEAP, lsl #32
    // 0xaf3480: LoadField: r2 = r0->field_27
    //     0xaf3480: ldur            w2, [x0, #0x27]
    // 0xaf3484: DecompressPointer r2
    //     0xaf3484: add             x2, x2, HEAP, lsl #32
    // 0xaf3488: LoadField: r1 = r0->field_b
    //     0xaf3488: ldur            w1, [x0, #0xb]
    // 0xaf348c: DecompressPointer r1
    //     0xaf348c: add             x1, x1, HEAP, lsl #32
    // 0xaf3490: cmp             w1, NULL
    // 0xaf3494: b.eq            #0xaf3bb8
    // 0xaf3498: LoadField: r0 = r1->field_b
    //     0xaf3498: ldur            w0, [x1, #0xb]
    // 0xaf349c: DecompressPointer r0
    //     0xaf349c: add             x0, x0, HEAP, lsl #32
    // 0xaf34a0: LoadField: r6 = r0->field_73
    //     0xaf34a0: ldur            w6, [x0, #0x73]
    // 0xaf34a4: DecompressPointer r6
    //     0xaf34a4: add             x6, x6, HEAP, lsl #32
    // 0xaf34a8: cmp             w6, NULL
    // 0xaf34ac: b.ne            #0xaf34b8
    // 0xaf34b0: r0 = Null
    //     0xaf34b0: mov             x0, NULL
    // 0xaf34b4: b               #0xaf34f8
    // 0xaf34b8: LoadField: r0 = r6->field_b
    //     0xaf34b8: ldur            w0, [x6, #0xb]
    // 0xaf34bc: r7 = LoadInt32Instr(r5)
    //     0xaf34bc: sbfx            x7, x5, #1, #0x1f
    //     0xaf34c0: tbz             w5, #0, #0xaf34c8
    //     0xaf34c4: ldur            x7, [x5, #7]
    // 0xaf34c8: r1 = LoadInt32Instr(r0)
    //     0xaf34c8: sbfx            x1, x0, #1, #0x1f
    // 0xaf34cc: mov             x0, x1
    // 0xaf34d0: mov             x1, x7
    // 0xaf34d4: cmp             x1, x0
    // 0xaf34d8: b.hs            #0xaf3bbc
    // 0xaf34dc: LoadField: r0 = r6->field_f
    //     0xaf34dc: ldur            w0, [x6, #0xf]
    // 0xaf34e0: DecompressPointer r0
    //     0xaf34e0: add             x0, x0, HEAP, lsl #32
    // 0xaf34e4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xaf34e4: add             x16, x0, x7, lsl #2
    //     0xaf34e8: ldur            w1, [x16, #0xf]
    // 0xaf34ec: DecompressPointer r1
    //     0xaf34ec: add             x1, x1, HEAP, lsl #32
    // 0xaf34f0: LoadField: r0 = r1->field_7
    //     0xaf34f0: ldur            w0, [x1, #7]
    // 0xaf34f4: DecompressPointer r0
    //     0xaf34f4: add             x0, x0, HEAP, lsl #32
    // 0xaf34f8: cmp             w0, NULL
    // 0xaf34fc: b.ne            #0xaf3504
    // 0xaf3500: r0 = ""
    //     0xaf3500: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf3504: mov             x1, x2
    // 0xaf3508: mov             x2, x0
    // 0xaf350c: r0 = contains()
    //     0xaf350c: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xaf3510: tbnz            w0, #4, #0xaf35a4
    // 0xaf3514: ldr             x1, [fp, #0x18]
    // 0xaf3518: r0 = of()
    //     0xaf3518: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf351c: LoadField: r2 = r0->field_5b
    //     0xaf351c: ldur            w2, [x0, #0x5b]
    // 0xaf3520: DecompressPointer r2
    //     0xaf3520: add             x2, x2, HEAP, lsl #32
    // 0xaf3524: r16 = 2.000000
    //     0xaf3524: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xaf3528: ldr             x16, [x16, #0xdf8]
    // 0xaf352c: str             x16, [SP]
    // 0xaf3530: r1 = Null
    //     0xaf3530: mov             x1, NULL
    // 0xaf3534: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xaf3534: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xaf3538: ldr             x4, [x4, #0x108]
    // 0xaf353c: r0 = Border.all()
    //     0xaf353c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xaf3540: stur            x0, [fp, #-0x18]
    // 0xaf3544: r0 = Radius()
    //     0xaf3544: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf3548: d0 = 10.000000
    //     0xaf3548: fmov            d0, #10.00000000
    // 0xaf354c: stur            x0, [fp, #-0x20]
    // 0xaf3550: StoreField: r0->field_7 = d0
    //     0xaf3550: stur            d0, [x0, #7]
    // 0xaf3554: StoreField: r0->field_f = d0
    //     0xaf3554: stur            d0, [x0, #0xf]
    // 0xaf3558: r0 = BorderRadius()
    //     0xaf3558: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf355c: mov             x1, x0
    // 0xaf3560: ldur            x0, [fp, #-0x20]
    // 0xaf3564: stur            x1, [fp, #-0x28]
    // 0xaf3568: StoreField: r1->field_7 = r0
    //     0xaf3568: stur            w0, [x1, #7]
    // 0xaf356c: StoreField: r1->field_b = r0
    //     0xaf356c: stur            w0, [x1, #0xb]
    // 0xaf3570: StoreField: r1->field_f = r0
    //     0xaf3570: stur            w0, [x1, #0xf]
    // 0xaf3574: StoreField: r1->field_13 = r0
    //     0xaf3574: stur            w0, [x1, #0x13]
    // 0xaf3578: r0 = BoxDecoration()
    //     0xaf3578: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf357c: mov             x1, x0
    // 0xaf3580: ldur            x0, [fp, #-0x18]
    // 0xaf3584: StoreField: r1->field_f = r0
    //     0xaf3584: stur            w0, [x1, #0xf]
    // 0xaf3588: ldur            x0, [fp, #-0x28]
    // 0xaf358c: StoreField: r1->field_13 = r0
    //     0xaf358c: stur            w0, [x1, #0x13]
    // 0xaf3590: r0 = Instance_BoxShape
    //     0xaf3590: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf3594: ldr             x0, [x0, #0x80]
    // 0xaf3598: StoreField: r1->field_23 = r0
    //     0xaf3598: stur            w0, [x1, #0x23]
    // 0xaf359c: mov             x2, x1
    // 0xaf35a0: b               #0xaf3620
    // 0xaf35a4: r0 = Instance_BoxShape
    //     0xaf35a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf35a8: ldr             x0, [x0, #0x80]
    // 0xaf35ac: r1 = Null
    //     0xaf35ac: mov             x1, NULL
    // 0xaf35b0: r2 = Instance_Color
    //     0xaf35b0: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xaf35b4: ldr             x2, [x2, #0xf88]
    // 0xaf35b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaf35b8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaf35bc: r0 = Border.all()
    //     0xaf35bc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xaf35c0: stur            x0, [fp, #-0x18]
    // 0xaf35c4: r0 = Radius()
    //     0xaf35c4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf35c8: d0 = 10.000000
    //     0xaf35c8: fmov            d0, #10.00000000
    // 0xaf35cc: stur            x0, [fp, #-0x20]
    // 0xaf35d0: StoreField: r0->field_7 = d0
    //     0xaf35d0: stur            d0, [x0, #7]
    // 0xaf35d4: StoreField: r0->field_f = d0
    //     0xaf35d4: stur            d0, [x0, #0xf]
    // 0xaf35d8: r0 = BorderRadius()
    //     0xaf35d8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf35dc: mov             x1, x0
    // 0xaf35e0: ldur            x0, [fp, #-0x20]
    // 0xaf35e4: stur            x1, [fp, #-0x28]
    // 0xaf35e8: StoreField: r1->field_7 = r0
    //     0xaf35e8: stur            w0, [x1, #7]
    // 0xaf35ec: StoreField: r1->field_b = r0
    //     0xaf35ec: stur            w0, [x1, #0xb]
    // 0xaf35f0: StoreField: r1->field_f = r0
    //     0xaf35f0: stur            w0, [x1, #0xf]
    // 0xaf35f4: StoreField: r1->field_13 = r0
    //     0xaf35f4: stur            w0, [x1, #0x13]
    // 0xaf35f8: r0 = BoxDecoration()
    //     0xaf35f8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf35fc: mov             x1, x0
    // 0xaf3600: ldur            x0, [fp, #-0x18]
    // 0xaf3604: StoreField: r1->field_f = r0
    //     0xaf3604: stur            w0, [x1, #0xf]
    // 0xaf3608: ldur            x0, [fp, #-0x28]
    // 0xaf360c: StoreField: r1->field_13 = r0
    //     0xaf360c: stur            w0, [x1, #0x13]
    // 0xaf3610: r0 = Instance_BoxShape
    //     0xaf3610: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf3614: ldr             x0, [x0, #0x80]
    // 0xaf3618: StoreField: r1->field_23 = r0
    //     0xaf3618: stur            w0, [x1, #0x23]
    // 0xaf361c: mov             x2, x1
    // 0xaf3620: ldur            x1, [fp, #-8]
    // 0xaf3624: stur            x2, [fp, #-0x18]
    // 0xaf3628: r0 = Radius()
    //     0xaf3628: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf362c: d0 = 10.000000
    //     0xaf362c: fmov            d0, #10.00000000
    // 0xaf3630: stur            x0, [fp, #-0x20]
    // 0xaf3634: StoreField: r0->field_7 = d0
    //     0xaf3634: stur            d0, [x0, #7]
    // 0xaf3638: StoreField: r0->field_f = d0
    //     0xaf3638: stur            d0, [x0, #0xf]
    // 0xaf363c: r0 = BorderRadius()
    //     0xaf363c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf3640: mov             x1, x0
    // 0xaf3644: ldur            x0, [fp, #-0x20]
    // 0xaf3648: stur            x1, [fp, #-0x28]
    // 0xaf364c: StoreField: r1->field_7 = r0
    //     0xaf364c: stur            w0, [x1, #7]
    // 0xaf3650: StoreField: r1->field_b = r0
    //     0xaf3650: stur            w0, [x1, #0xb]
    // 0xaf3654: StoreField: r1->field_f = r0
    //     0xaf3654: stur            w0, [x1, #0xf]
    // 0xaf3658: StoreField: r1->field_13 = r0
    //     0xaf3658: stur            w0, [x1, #0x13]
    // 0xaf365c: r0 = RoundedRectangleBorder()
    //     0xaf365c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaf3660: mov             x3, x0
    // 0xaf3664: ldur            x0, [fp, #-0x28]
    // 0xaf3668: stur            x3, [fp, #-0x30]
    // 0xaf366c: StoreField: r3->field_b = r0
    //     0xaf366c: stur            w0, [x3, #0xb]
    // 0xaf3670: r0 = Instance_BorderSide
    //     0xaf3670: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaf3674: ldr             x0, [x0, #0xe20]
    // 0xaf3678: StoreField: r3->field_7 = r0
    //     0xaf3678: stur            w0, [x3, #7]
    // 0xaf367c: ldur            x4, [fp, #-8]
    // 0xaf3680: LoadField: r0 = r4->field_f
    //     0xaf3680: ldur            w0, [x4, #0xf]
    // 0xaf3684: DecompressPointer r0
    //     0xaf3684: add             x0, x0, HEAP, lsl #32
    // 0xaf3688: LoadField: r1 = r0->field_b
    //     0xaf3688: ldur            w1, [x0, #0xb]
    // 0xaf368c: DecompressPointer r1
    //     0xaf368c: add             x1, x1, HEAP, lsl #32
    // 0xaf3690: cmp             w1, NULL
    // 0xaf3694: b.eq            #0xaf3bc0
    // 0xaf3698: LoadField: r0 = r1->field_b
    //     0xaf3698: ldur            w0, [x1, #0xb]
    // 0xaf369c: DecompressPointer r0
    //     0xaf369c: add             x0, x0, HEAP, lsl #32
    // 0xaf36a0: LoadField: r2 = r0->field_73
    //     0xaf36a0: ldur            w2, [x0, #0x73]
    // 0xaf36a4: DecompressPointer r2
    //     0xaf36a4: add             x2, x2, HEAP, lsl #32
    // 0xaf36a8: cmp             w2, NULL
    // 0xaf36ac: b.ne            #0xaf36bc
    // 0xaf36b0: ldr             x5, [fp, #0x10]
    // 0xaf36b4: r0 = Null
    //     0xaf36b4: mov             x0, NULL
    // 0xaf36b8: b               #0xaf3700
    // 0xaf36bc: ldr             x5, [fp, #0x10]
    // 0xaf36c0: LoadField: r0 = r2->field_b
    //     0xaf36c0: ldur            w0, [x2, #0xb]
    // 0xaf36c4: r6 = LoadInt32Instr(r5)
    //     0xaf36c4: sbfx            x6, x5, #1, #0x1f
    //     0xaf36c8: tbz             w5, #0, #0xaf36d0
    //     0xaf36cc: ldur            x6, [x5, #7]
    // 0xaf36d0: r1 = LoadInt32Instr(r0)
    //     0xaf36d0: sbfx            x1, x0, #1, #0x1f
    // 0xaf36d4: mov             x0, x1
    // 0xaf36d8: mov             x1, x6
    // 0xaf36dc: cmp             x1, x0
    // 0xaf36e0: b.hs            #0xaf3bc4
    // 0xaf36e4: LoadField: r0 = r2->field_f
    //     0xaf36e4: ldur            w0, [x2, #0xf]
    // 0xaf36e8: DecompressPointer r0
    //     0xaf36e8: add             x0, x0, HEAP, lsl #32
    // 0xaf36ec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf36ec: add             x16, x0, x6, lsl #2
    //     0xaf36f0: ldur            w1, [x16, #0xf]
    // 0xaf36f4: DecompressPointer r1
    //     0xaf36f4: add             x1, x1, HEAP, lsl #32
    // 0xaf36f8: LoadField: r0 = r1->field_f
    //     0xaf36f8: ldur            w0, [x1, #0xf]
    // 0xaf36fc: DecompressPointer r0
    //     0xaf36fc: add             x0, x0, HEAP, lsl #32
    // 0xaf3700: cmp             w0, NULL
    // 0xaf3704: b.ne            #0xaf370c
    // 0xaf3708: r0 = ""
    //     0xaf3708: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf370c: stur            x0, [fp, #-0x20]
    // 0xaf3710: r1 = Function '<anonymous closure>':.
    //     0xaf3710: add             x1, PP, #0x58, lsl #12  ; [pp+0x586b0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf3714: ldr             x1, [x1, #0x6b0]
    // 0xaf3718: r2 = Null
    //     0xaf3718: mov             x2, NULL
    // 0xaf371c: r0 = AllocateClosure()
    //     0xaf371c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3720: r1 = Function '<anonymous closure>':.
    //     0xaf3720: add             x1, PP, #0x58, lsl #12  ; [pp+0x586b8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf3724: ldr             x1, [x1, #0x6b8]
    // 0xaf3728: r2 = Null
    //     0xaf3728: mov             x2, NULL
    // 0xaf372c: stur            x0, [fp, #-0x28]
    // 0xaf3730: r0 = AllocateClosure()
    //     0xaf3730: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3734: stur            x0, [fp, #-0x38]
    // 0xaf3738: r0 = CachedNetworkImage()
    //     0xaf3738: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaf373c: stur            x0, [fp, #-0x40]
    // 0xaf3740: r16 = Instance_BoxFit
    //     0xaf3740: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf3744: ldr             x16, [x16, #0xb18]
    // 0xaf3748: ldur            lr, [fp, #-0x28]
    // 0xaf374c: stp             lr, x16, [SP, #8]
    // 0xaf3750: ldur            x16, [fp, #-0x38]
    // 0xaf3754: str             x16, [SP]
    // 0xaf3758: mov             x1, x0
    // 0xaf375c: ldur            x2, [fp, #-0x20]
    // 0xaf3760: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xaf3760: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xaf3764: ldr             x4, [x4, #0x638]
    // 0xaf3768: r0 = CachedNetworkImage()
    //     0xaf3768: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaf376c: r0 = Card()
    //     0xaf376c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xaf3770: mov             x1, x0
    // 0xaf3774: r0 = 0.000000
    //     0xaf3774: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaf3778: stur            x1, [fp, #-0x20]
    // 0xaf377c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf377c: stur            w0, [x1, #0x17]
    // 0xaf3780: ldur            x0, [fp, #-0x30]
    // 0xaf3784: StoreField: r1->field_1b = r0
    //     0xaf3784: stur            w0, [x1, #0x1b]
    // 0xaf3788: r0 = true
    //     0xaf3788: add             x0, NULL, #0x20  ; true
    // 0xaf378c: StoreField: r1->field_1f = r0
    //     0xaf378c: stur            w0, [x1, #0x1f]
    // 0xaf3790: r2 = Instance_EdgeInsets
    //     0xaf3790: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaf3794: StoreField: r1->field_27 = r2
    //     0xaf3794: stur            w2, [x1, #0x27]
    // 0xaf3798: r2 = Instance_Clip
    //     0xaf3798: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xaf379c: ldr             x2, [x2, #0xb50]
    // 0xaf37a0: StoreField: r1->field_23 = r2
    //     0xaf37a0: stur            w2, [x1, #0x23]
    // 0xaf37a4: ldur            x2, [fp, #-0x40]
    // 0xaf37a8: StoreField: r1->field_2f = r2
    //     0xaf37a8: stur            w2, [x1, #0x2f]
    // 0xaf37ac: StoreField: r1->field_2b = r0
    //     0xaf37ac: stur            w0, [x1, #0x2b]
    // 0xaf37b0: r2 = Instance__CardVariant
    //     0xaf37b0: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xaf37b4: ldr             x2, [x2, #0xa68]
    // 0xaf37b8: StoreField: r1->field_33 = r2
    //     0xaf37b8: stur            w2, [x1, #0x33]
    // 0xaf37bc: r0 = AspectRatio()
    //     0xaf37bc: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xaf37c0: d0 = 0.500000
    //     0xaf37c0: fmov            d0, #0.50000000
    // 0xaf37c4: stur            x0, [fp, #-0x28]
    // 0xaf37c8: StoreField: r0->field_f = d0
    //     0xaf37c8: stur            d0, [x0, #0xf]
    // 0xaf37cc: ldur            x1, [fp, #-0x20]
    // 0xaf37d0: StoreField: r0->field_b = r1
    //     0xaf37d0: stur            w1, [x0, #0xb]
    // 0xaf37d4: r0 = Container()
    //     0xaf37d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf37d8: stur            x0, [fp, #-0x20]
    // 0xaf37dc: r16 = 80.000000
    //     0xaf37dc: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xaf37e0: ldr             x16, [x16, #0x2f8]
    // 0xaf37e4: r30 = 80.000000
    //     0xaf37e4: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xaf37e8: ldr             lr, [lr, #0x2f8]
    // 0xaf37ec: stp             lr, x16, [SP, #0x10]
    // 0xaf37f0: ldur            x16, [fp, #-0x18]
    // 0xaf37f4: ldur            lr, [fp, #-0x28]
    // 0xaf37f8: stp             lr, x16, [SP]
    // 0xaf37fc: mov             x1, x0
    // 0xaf3800: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xaf3800: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xaf3804: ldr             x4, [x4, #0x870]
    // 0xaf3808: r0 = Container()
    //     0xaf3808: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf380c: ldur            x0, [fp, #-8]
    // 0xaf3810: LoadField: r1 = r0->field_f
    //     0xaf3810: ldur            w1, [x0, #0xf]
    // 0xaf3814: DecompressPointer r1
    //     0xaf3814: add             x1, x1, HEAP, lsl #32
    // 0xaf3818: LoadField: r2 = r1->field_b
    //     0xaf3818: ldur            w2, [x1, #0xb]
    // 0xaf381c: DecompressPointer r2
    //     0xaf381c: add             x2, x2, HEAP, lsl #32
    // 0xaf3820: cmp             w2, NULL
    // 0xaf3824: b.eq            #0xaf3bc8
    // 0xaf3828: LoadField: r0 = r2->field_b
    //     0xaf3828: ldur            w0, [x2, #0xb]
    // 0xaf382c: DecompressPointer r0
    //     0xaf382c: add             x0, x0, HEAP, lsl #32
    // 0xaf3830: LoadField: r3 = r0->field_73
    //     0xaf3830: ldur            w3, [x0, #0x73]
    // 0xaf3834: DecompressPointer r3
    //     0xaf3834: add             x3, x3, HEAP, lsl #32
    // 0xaf3838: cmp             w3, NULL
    // 0xaf383c: b.ne            #0xaf384c
    // 0xaf3840: ldr             x4, [fp, #0x10]
    // 0xaf3844: r0 = Null
    //     0xaf3844: mov             x0, NULL
    // 0xaf3848: b               #0xaf3890
    // 0xaf384c: ldr             x4, [fp, #0x10]
    // 0xaf3850: LoadField: r0 = r3->field_b
    //     0xaf3850: ldur            w0, [x3, #0xb]
    // 0xaf3854: r5 = LoadInt32Instr(r4)
    //     0xaf3854: sbfx            x5, x4, #1, #0x1f
    //     0xaf3858: tbz             w4, #0, #0xaf3860
    //     0xaf385c: ldur            x5, [x4, #7]
    // 0xaf3860: r1 = LoadInt32Instr(r0)
    //     0xaf3860: sbfx            x1, x0, #1, #0x1f
    // 0xaf3864: mov             x0, x1
    // 0xaf3868: mov             x1, x5
    // 0xaf386c: cmp             x1, x0
    // 0xaf3870: b.hs            #0xaf3bcc
    // 0xaf3874: LoadField: r0 = r3->field_f
    //     0xaf3874: ldur            w0, [x3, #0xf]
    // 0xaf3878: DecompressPointer r0
    //     0xaf3878: add             x0, x0, HEAP, lsl #32
    // 0xaf387c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaf387c: add             x16, x0, x5, lsl #2
    //     0xaf3880: ldur            w1, [x16, #0xf]
    // 0xaf3884: DecompressPointer r1
    //     0xaf3884: add             x1, x1, HEAP, lsl #32
    // 0xaf3888: LoadField: r0 = r1->field_13
    //     0xaf3888: ldur            w0, [x1, #0x13]
    // 0xaf388c: DecompressPointer r0
    //     0xaf388c: add             x0, x0, HEAP, lsl #32
    // 0xaf3890: cmp             w0, NULL
    // 0xaf3894: r16 = true
    //     0xaf3894: add             x16, NULL, #0x20  ; true
    // 0xaf3898: r17 = false
    //     0xaf3898: add             x17, NULL, #0x30  ; false
    // 0xaf389c: csel            x3, x16, x17, ne
    // 0xaf38a0: stur            x3, [fp, #-8]
    // 0xaf38a4: LoadField: r0 = r2->field_b
    //     0xaf38a4: ldur            w0, [x2, #0xb]
    // 0xaf38a8: DecompressPointer r0
    //     0xaf38a8: add             x0, x0, HEAP, lsl #32
    // 0xaf38ac: LoadField: r5 = r0->field_73
    //     0xaf38ac: ldur            w5, [x0, #0x73]
    // 0xaf38b0: DecompressPointer r5
    //     0xaf38b0: add             x5, x5, HEAP, lsl #32
    // 0xaf38b4: cmp             w5, NULL
    // 0xaf38b8: b.ne            #0xaf38c4
    // 0xaf38bc: r0 = Null
    //     0xaf38bc: mov             x0, NULL
    // 0xaf38c0: b               #0xaf3928
    // 0xaf38c4: LoadField: r0 = r5->field_b
    //     0xaf38c4: ldur            w0, [x5, #0xb]
    // 0xaf38c8: r6 = LoadInt32Instr(r4)
    //     0xaf38c8: sbfx            x6, x4, #1, #0x1f
    //     0xaf38cc: tbz             w4, #0, #0xaf38d4
    //     0xaf38d0: ldur            x6, [x4, #7]
    // 0xaf38d4: r1 = LoadInt32Instr(r0)
    //     0xaf38d4: sbfx            x1, x0, #1, #0x1f
    // 0xaf38d8: mov             x0, x1
    // 0xaf38dc: mov             x1, x6
    // 0xaf38e0: cmp             x1, x0
    // 0xaf38e4: b.hs            #0xaf3bd0
    // 0xaf38e8: LoadField: r0 = r5->field_f
    //     0xaf38e8: ldur            w0, [x5, #0xf]
    // 0xaf38ec: DecompressPointer r0
    //     0xaf38ec: add             x0, x0, HEAP, lsl #32
    // 0xaf38f0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaf38f0: add             x16, x0, x6, lsl #2
    //     0xaf38f4: ldur            w1, [x16, #0xf]
    // 0xaf38f8: DecompressPointer r1
    //     0xaf38f8: add             x1, x1, HEAP, lsl #32
    // 0xaf38fc: LoadField: r0 = r1->field_13
    //     0xaf38fc: ldur            w0, [x1, #0x13]
    // 0xaf3900: DecompressPointer r0
    //     0xaf3900: add             x0, x0, HEAP, lsl #32
    // 0xaf3904: cmp             w0, NULL
    // 0xaf3908: b.ne            #0xaf3914
    // 0xaf390c: r0 = Null
    //     0xaf390c: mov             x0, NULL
    // 0xaf3910: b               #0xaf3928
    // 0xaf3914: LoadField: r1 = r0->field_7
    //     0xaf3914: ldur            w1, [x0, #7]
    // 0xaf3918: cbnz            w1, #0xaf3924
    // 0xaf391c: r0 = false
    //     0xaf391c: add             x0, NULL, #0x30  ; false
    // 0xaf3920: b               #0xaf3928
    // 0xaf3924: r0 = true
    //     0xaf3924: add             x0, NULL, #0x20  ; true
    // 0xaf3928: cmp             w0, NULL
    // 0xaf392c: b.eq            #0xaf39bc
    // 0xaf3930: tbnz            w0, #4, #0xaf39bc
    // 0xaf3934: LoadField: r0 = r2->field_b
    //     0xaf3934: ldur            w0, [x2, #0xb]
    // 0xaf3938: DecompressPointer r0
    //     0xaf3938: add             x0, x0, HEAP, lsl #32
    // 0xaf393c: LoadField: r2 = r0->field_73
    //     0xaf393c: ldur            w2, [x0, #0x73]
    // 0xaf3940: DecompressPointer r2
    //     0xaf3940: add             x2, x2, HEAP, lsl #32
    // 0xaf3944: cmp             w2, NULL
    // 0xaf3948: b.ne            #0xaf3954
    // 0xaf394c: r0 = Null
    //     0xaf394c: mov             x0, NULL
    // 0xaf3950: b               #0xaf39ac
    // 0xaf3954: LoadField: r0 = r2->field_b
    //     0xaf3954: ldur            w0, [x2, #0xb]
    // 0xaf3958: r5 = LoadInt32Instr(r4)
    //     0xaf3958: sbfx            x5, x4, #1, #0x1f
    //     0xaf395c: tbz             w4, #0, #0xaf3964
    //     0xaf3960: ldur            x5, [x4, #7]
    // 0xaf3964: r1 = LoadInt32Instr(r0)
    //     0xaf3964: sbfx            x1, x0, #1, #0x1f
    // 0xaf3968: mov             x0, x1
    // 0xaf396c: mov             x1, x5
    // 0xaf3970: cmp             x1, x0
    // 0xaf3974: b.hs            #0xaf3bd4
    // 0xaf3978: LoadField: r0 = r2->field_f
    //     0xaf3978: ldur            w0, [x2, #0xf]
    // 0xaf397c: DecompressPointer r0
    //     0xaf397c: add             x0, x0, HEAP, lsl #32
    // 0xaf3980: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaf3980: add             x16, x0, x5, lsl #2
    //     0xaf3984: ldur            w1, [x16, #0xf]
    // 0xaf3988: DecompressPointer r1
    //     0xaf3988: add             x1, x1, HEAP, lsl #32
    // 0xaf398c: LoadField: r0 = r1->field_13
    //     0xaf398c: ldur            w0, [x1, #0x13]
    // 0xaf3990: DecompressPointer r0
    //     0xaf3990: add             x0, x0, HEAP, lsl #32
    // 0xaf3994: cmp             w0, NULL
    // 0xaf3998: b.ne            #0xaf39a4
    // 0xaf399c: r0 = Null
    //     0xaf399c: mov             x0, NULL
    // 0xaf39a0: b               #0xaf39ac
    // 0xaf39a4: mov             x1, x0
    // 0xaf39a8: r0 = StringExtension.toTitleCase()
    //     0xaf39a8: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xaf39ac: str             x0, [SP]
    // 0xaf39b0: r0 = _interpolateSingle()
    //     0xaf39b0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xaf39b4: mov             x3, x0
    // 0xaf39b8: b               #0xaf39c0
    // 0xaf39bc: r3 = ""
    //     0xaf39bc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf39c0: ldur            x2, [fp, #-0x20]
    // 0xaf39c4: ldur            x0, [fp, #-8]
    // 0xaf39c8: ldr             x1, [fp, #0x18]
    // 0xaf39cc: stur            x3, [fp, #-0x18]
    // 0xaf39d0: r0 = of()
    //     0xaf39d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf39d4: LoadField: r1 = r0->field_87
    //     0xaf39d4: ldur            w1, [x0, #0x87]
    // 0xaf39d8: DecompressPointer r1
    //     0xaf39d8: add             x1, x1, HEAP, lsl #32
    // 0xaf39dc: LoadField: r0 = r1->field_2b
    //     0xaf39dc: ldur            w0, [x1, #0x2b]
    // 0xaf39e0: DecompressPointer r0
    //     0xaf39e0: add             x0, x0, HEAP, lsl #32
    // 0xaf39e4: stur            x0, [fp, #-0x28]
    // 0xaf39e8: r1 = Instance_Color
    //     0xaf39e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf39ec: d0 = 0.700000
    //     0xaf39ec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf39f0: ldr             d0, [x17, #0xf48]
    // 0xaf39f4: r0 = withOpacity()
    //     0xaf39f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf39f8: r16 = 12.000000
    //     0xaf39f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf39fc: ldr             x16, [x16, #0x9e8]
    // 0xaf3a00: stp             x0, x16, [SP]
    // 0xaf3a04: ldur            x1, [fp, #-0x28]
    // 0xaf3a08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf3a08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf3a0c: ldr             x4, [x4, #0xaa0]
    // 0xaf3a10: r0 = copyWith()
    //     0xaf3a10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf3a14: stur            x0, [fp, #-0x28]
    // 0xaf3a18: r0 = Text()
    //     0xaf3a18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf3a1c: mov             x1, x0
    // 0xaf3a20: ldur            x0, [fp, #-0x18]
    // 0xaf3a24: stur            x1, [fp, #-0x30]
    // 0xaf3a28: StoreField: r1->field_b = r0
    //     0xaf3a28: stur            w0, [x1, #0xb]
    // 0xaf3a2c: ldur            x0, [fp, #-0x28]
    // 0xaf3a30: StoreField: r1->field_13 = r0
    //     0xaf3a30: stur            w0, [x1, #0x13]
    // 0xaf3a34: r0 = Padding()
    //     0xaf3a34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf3a38: mov             x1, x0
    // 0xaf3a3c: r0 = Instance_EdgeInsets
    //     0xaf3a3c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xaf3a40: ldr             x0, [x0, #0x9e0]
    // 0xaf3a44: stur            x1, [fp, #-0x18]
    // 0xaf3a48: StoreField: r1->field_f = r0
    //     0xaf3a48: stur            w0, [x1, #0xf]
    // 0xaf3a4c: ldur            x0, [fp, #-0x30]
    // 0xaf3a50: StoreField: r1->field_b = r0
    //     0xaf3a50: stur            w0, [x1, #0xb]
    // 0xaf3a54: r0 = Visibility()
    //     0xaf3a54: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xaf3a58: mov             x3, x0
    // 0xaf3a5c: ldur            x0, [fp, #-0x18]
    // 0xaf3a60: stur            x3, [fp, #-0x28]
    // 0xaf3a64: StoreField: r3->field_b = r0
    //     0xaf3a64: stur            w0, [x3, #0xb]
    // 0xaf3a68: r0 = Instance_SizedBox
    //     0xaf3a68: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaf3a6c: StoreField: r3->field_f = r0
    //     0xaf3a6c: stur            w0, [x3, #0xf]
    // 0xaf3a70: ldur            x0, [fp, #-8]
    // 0xaf3a74: StoreField: r3->field_13 = r0
    //     0xaf3a74: stur            w0, [x3, #0x13]
    // 0xaf3a78: r0 = false
    //     0xaf3a78: add             x0, NULL, #0x30  ; false
    // 0xaf3a7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xaf3a7c: stur            w0, [x3, #0x17]
    // 0xaf3a80: StoreField: r3->field_1b = r0
    //     0xaf3a80: stur            w0, [x3, #0x1b]
    // 0xaf3a84: StoreField: r3->field_1f = r0
    //     0xaf3a84: stur            w0, [x3, #0x1f]
    // 0xaf3a88: StoreField: r3->field_23 = r0
    //     0xaf3a88: stur            w0, [x3, #0x23]
    // 0xaf3a8c: StoreField: r3->field_27 = r0
    //     0xaf3a8c: stur            w0, [x3, #0x27]
    // 0xaf3a90: StoreField: r3->field_2b = r0
    //     0xaf3a90: stur            w0, [x3, #0x2b]
    // 0xaf3a94: r1 = Null
    //     0xaf3a94: mov             x1, NULL
    // 0xaf3a98: r2 = 4
    //     0xaf3a98: movz            x2, #0x4
    // 0xaf3a9c: r0 = AllocateArray()
    //     0xaf3a9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf3aa0: mov             x2, x0
    // 0xaf3aa4: ldur            x0, [fp, #-0x20]
    // 0xaf3aa8: stur            x2, [fp, #-8]
    // 0xaf3aac: StoreField: r2->field_f = r0
    //     0xaf3aac: stur            w0, [x2, #0xf]
    // 0xaf3ab0: ldur            x0, [fp, #-0x28]
    // 0xaf3ab4: StoreField: r2->field_13 = r0
    //     0xaf3ab4: stur            w0, [x2, #0x13]
    // 0xaf3ab8: r1 = <Widget>
    //     0xaf3ab8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf3abc: r0 = AllocateGrowableArray()
    //     0xaf3abc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf3ac0: mov             x1, x0
    // 0xaf3ac4: ldur            x0, [fp, #-8]
    // 0xaf3ac8: stur            x1, [fp, #-0x18]
    // 0xaf3acc: StoreField: r1->field_f = r0
    //     0xaf3acc: stur            w0, [x1, #0xf]
    // 0xaf3ad0: r0 = 4
    //     0xaf3ad0: movz            x0, #0x4
    // 0xaf3ad4: StoreField: r1->field_b = r0
    //     0xaf3ad4: stur            w0, [x1, #0xb]
    // 0xaf3ad8: r0 = Column()
    //     0xaf3ad8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf3adc: mov             x1, x0
    // 0xaf3ae0: r0 = Instance_Axis
    //     0xaf3ae0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf3ae4: stur            x1, [fp, #-8]
    // 0xaf3ae8: StoreField: r1->field_f = r0
    //     0xaf3ae8: stur            w0, [x1, #0xf]
    // 0xaf3aec: r0 = Instance_MainAxisAlignment
    //     0xaf3aec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf3af0: ldr             x0, [x0, #0xa08]
    // 0xaf3af4: StoreField: r1->field_13 = r0
    //     0xaf3af4: stur            w0, [x1, #0x13]
    // 0xaf3af8: r0 = Instance_MainAxisSize
    //     0xaf3af8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf3afc: ldr             x0, [x0, #0xa10]
    // 0xaf3b00: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf3b00: stur            w0, [x1, #0x17]
    // 0xaf3b04: r0 = Instance_CrossAxisAlignment
    //     0xaf3b04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf3b08: ldr             x0, [x0, #0xa18]
    // 0xaf3b0c: StoreField: r1->field_1b = r0
    //     0xaf3b0c: stur            w0, [x1, #0x1b]
    // 0xaf3b10: r0 = Instance_VerticalDirection
    //     0xaf3b10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf3b14: ldr             x0, [x0, #0xa20]
    // 0xaf3b18: StoreField: r1->field_23 = r0
    //     0xaf3b18: stur            w0, [x1, #0x23]
    // 0xaf3b1c: r0 = Instance_Clip
    //     0xaf3b1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf3b20: ldr             x0, [x0, #0x38]
    // 0xaf3b24: StoreField: r1->field_2b = r0
    //     0xaf3b24: stur            w0, [x1, #0x2b]
    // 0xaf3b28: StoreField: r1->field_2f = rZR
    //     0xaf3b28: stur            xzr, [x1, #0x2f]
    // 0xaf3b2c: ldur            x0, [fp, #-0x18]
    // 0xaf3b30: StoreField: r1->field_b = r0
    //     0xaf3b30: stur            w0, [x1, #0xb]
    // 0xaf3b34: r0 = InkWell()
    //     0xaf3b34: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaf3b38: mov             x3, x0
    // 0xaf3b3c: ldur            x0, [fp, #-8]
    // 0xaf3b40: stur            x3, [fp, #-0x18]
    // 0xaf3b44: StoreField: r3->field_b = r0
    //     0xaf3b44: stur            w0, [x3, #0xb]
    // 0xaf3b48: ldur            x2, [fp, #-0x10]
    // 0xaf3b4c: r1 = Function '<anonymous closure>':.
    //     0xaf3b4c: add             x1, PP, #0x58, lsl #12  ; [pp+0x586c0] AnonymousClosure: (0xaf3bd8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xaf0c5c)
    //     0xaf3b50: ldr             x1, [x1, #0x6c0]
    // 0xaf3b54: r0 = AllocateClosure()
    //     0xaf3b54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3b58: mov             x1, x0
    // 0xaf3b5c: ldur            x0, [fp, #-0x18]
    // 0xaf3b60: StoreField: r0->field_f = r1
    //     0xaf3b60: stur            w1, [x0, #0xf]
    // 0xaf3b64: r1 = true
    //     0xaf3b64: add             x1, NULL, #0x20  ; true
    // 0xaf3b68: StoreField: r0->field_43 = r1
    //     0xaf3b68: stur            w1, [x0, #0x43]
    // 0xaf3b6c: r2 = Instance_BoxShape
    //     0xaf3b6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf3b70: ldr             x2, [x2, #0x80]
    // 0xaf3b74: StoreField: r0->field_47 = r2
    //     0xaf3b74: stur            w2, [x0, #0x47]
    // 0xaf3b78: StoreField: r0->field_6f = r1
    //     0xaf3b78: stur            w1, [x0, #0x6f]
    // 0xaf3b7c: r2 = false
    //     0xaf3b7c: add             x2, NULL, #0x30  ; false
    // 0xaf3b80: StoreField: r0->field_73 = r2
    //     0xaf3b80: stur            w2, [x0, #0x73]
    // 0xaf3b84: StoreField: r0->field_83 = r1
    //     0xaf3b84: stur            w1, [x0, #0x83]
    // 0xaf3b88: StoreField: r0->field_7b = r2
    //     0xaf3b88: stur            w2, [x0, #0x7b]
    // 0xaf3b8c: r0 = Padding()
    //     0xaf3b8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf3b90: r1 = Instance_EdgeInsets
    //     0xaf3b90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaf3b94: ldr             x1, [x1, #0x980]
    // 0xaf3b98: StoreField: r0->field_f = r1
    //     0xaf3b98: stur            w1, [x0, #0xf]
    // 0xaf3b9c: ldur            x1, [fp, #-0x18]
    // 0xaf3ba0: StoreField: r0->field_b = r1
    //     0xaf3ba0: stur            w1, [x0, #0xb]
    // 0xaf3ba4: LeaveFrame
    //     0xaf3ba4: mov             SP, fp
    //     0xaf3ba8: ldp             fp, lr, [SP], #0x10
    // 0xaf3bac: ret
    //     0xaf3bac: ret             
    // 0xaf3bb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3bb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3bb4: b               #0xaf3458
    // 0xaf3bb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3bb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf3bbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3bbc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf3bc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3bc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf3bc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3bc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf3bc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf3bc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf3bcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3bcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf3bd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3bd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf3bd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf3bd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf3bd8, size: 0x68
    // 0xaf3bd8: EnterFrame
    //     0xaf3bd8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3bdc: mov             fp, SP
    // 0xaf3be0: AllocStack(0x8)
    //     0xaf3be0: sub             SP, SP, #8
    // 0xaf3be4: SetupParameters()
    //     0xaf3be4: ldr             x0, [fp, #0x10]
    //     0xaf3be8: ldur            w2, [x0, #0x17]
    //     0xaf3bec: add             x2, x2, HEAP, lsl #32
    // 0xaf3bf0: CheckStackOverflow
    //     0xaf3bf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3bf4: cmp             SP, x16
    //     0xaf3bf8: b.ls            #0xaf3c38
    // 0xaf3bfc: LoadField: r0 = r2->field_b
    //     0xaf3bfc: ldur            w0, [x2, #0xb]
    // 0xaf3c00: DecompressPointer r0
    //     0xaf3c00: add             x0, x0, HEAP, lsl #32
    // 0xaf3c04: LoadField: r3 = r0->field_f
    //     0xaf3c04: ldur            w3, [x0, #0xf]
    // 0xaf3c08: DecompressPointer r3
    //     0xaf3c08: add             x3, x3, HEAP, lsl #32
    // 0xaf3c0c: stur            x3, [fp, #-8]
    // 0xaf3c10: r1 = Function '<anonymous closure>':.
    //     0xaf3c10: add             x1, PP, #0x58, lsl #12  ; [pp+0x586c8] AnonymousClosure: (0xa61dd4), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xaf3c14: ldr             x1, [x1, #0x6c8]
    // 0xaf3c18: r0 = AllocateClosure()
    //     0xaf3c18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf3c1c: ldur            x1, [fp, #-8]
    // 0xaf3c20: mov             x2, x0
    // 0xaf3c24: r0 = setState()
    //     0xaf3c24: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf3c28: r0 = Null
    //     0xaf3c28: mov             x0, NULL
    // 0xaf3c2c: LeaveFrame
    //     0xaf3c2c: mov             SP, fp
    //     0xaf3c30: ldp             fp, lr, [SP], #0x10
    // 0xaf3c34: ret
    //     0xaf3c34: ret             
    // 0xaf3c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3c38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3c3c: b               #0xaf3bfc
  }
}

// class id: 4157, size: 0x2c, field offset: 0xc
//   const constructor, 
class SelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dbc4, size: 0x48
    // 0xc7dbc4: EnterFrame
    //     0xc7dbc4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dbc8: mov             fp, SP
    // 0xc7dbcc: AllocStack(0x8)
    //     0xc7dbcc: sub             SP, SP, #8
    // 0xc7dbd0: CheckStackOverflow
    //     0xc7dbd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7dbd4: cmp             SP, x16
    //     0xc7dbd8: b.ls            #0xc7dc04
    // 0xc7dbdc: r1 = <SelectSizeBottomSheet>
    //     0xc7dbdc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c20] TypeArguments: <SelectSizeBottomSheet>
    //     0xc7dbe0: ldr             x1, [x1, #0xc20]
    // 0xc7dbe4: r0 = _SelectSizeBottomSheetState()
    //     0xc7dbe4: bl              #0xc7dc0c  ; Allocate_SelectSizeBottomSheetStateStub -> _SelectSizeBottomSheetState (size=0x30)
    // 0xc7dbe8: mov             x1, x0
    // 0xc7dbec: stur            x0, [fp, #-8]
    // 0xc7dbf0: r0 = _SelectSizeBottomSheetState()
    //     0xc7dbf0: bl              #0xc7bc9c  ; [package:customer_app/app/presentation/views/basic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::_SelectSizeBottomSheetState
    // 0xc7dbf4: ldur            x0, [fp, #-8]
    // 0xc7dbf8: LeaveFrame
    //     0xc7dbf8: mov             SP, fp
    //     0xc7dbfc: ldp             fp, lr, [SP], #0x10
    // 0xc7dc00: ret
    //     0xc7dc00: ret             
    // 0xc7dc04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7dc04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7dc08: b               #0xc7dbdc
  }
}
