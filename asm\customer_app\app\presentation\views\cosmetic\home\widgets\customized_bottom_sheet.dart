// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart

// class id: 1049280, size: 0x8
class :: {
}

// class id: 3426, size: 0x14, field offset: 0x14
class _CustomizedBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae6d0c, size: 0xc08
    // 0xae6d0c: EnterFrame
    //     0xae6d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xae6d10: mov             fp, SP
    // 0xae6d14: AllocStack(0x68)
    //     0xae6d14: sub             SP, SP, #0x68
    // 0xae6d18: SetupParameters(_CustomizedBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae6d18: stur            x1, [fp, #-8]
    //     0xae6d1c: stur            x2, [fp, #-0x10]
    // 0xae6d20: CheckStackOverflow
    //     0xae6d20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6d24: cmp             SP, x16
    //     0xae6d28: b.ls            #0xae78fc
    // 0xae6d2c: r1 = 2
    //     0xae6d2c: movz            x1, #0x2
    // 0xae6d30: r0 = AllocateContext()
    //     0xae6d30: bl              #0x16f6108  ; AllocateContextStub
    // 0xae6d34: mov             x1, x0
    // 0xae6d38: ldur            x0, [fp, #-8]
    // 0xae6d3c: stur            x1, [fp, #-0x18]
    // 0xae6d40: StoreField: r1->field_f = r0
    //     0xae6d40: stur            w0, [x1, #0xf]
    // 0xae6d44: ldur            x2, [fp, #-0x10]
    // 0xae6d48: StoreField: r1->field_13 = r2
    //     0xae6d48: stur            w2, [x1, #0x13]
    // 0xae6d4c: LoadField: r2 = r0->field_b
    //     0xae6d4c: ldur            w2, [x0, #0xb]
    // 0xae6d50: DecompressPointer r2
    //     0xae6d50: add             x2, x2, HEAP, lsl #32
    // 0xae6d54: cmp             w2, NULL
    // 0xae6d58: b.eq            #0xae7904
    // 0xae6d5c: LoadField: r3 = r2->field_b
    //     0xae6d5c: ldur            w3, [x2, #0xb]
    // 0xae6d60: DecompressPointer r3
    //     0xae6d60: add             x3, x3, HEAP, lsl #32
    // 0xae6d64: LoadField: r2 = r3->field_b
    //     0xae6d64: ldur            w2, [x3, #0xb]
    // 0xae6d68: DecompressPointer r2
    //     0xae6d68: add             x2, x2, HEAP, lsl #32
    // 0xae6d6c: cmp             w2, NULL
    // 0xae6d70: b.ne            #0xae6d7c
    // 0xae6d74: r2 = Null
    //     0xae6d74: mov             x2, NULL
    // 0xae6d78: b               #0xae6d88
    // 0xae6d7c: LoadField: r3 = r2->field_23
    //     0xae6d7c: ldur            w3, [x2, #0x23]
    // 0xae6d80: DecompressPointer r3
    //     0xae6d80: add             x3, x3, HEAP, lsl #32
    // 0xae6d84: mov             x2, x3
    // 0xae6d88: cmp             w2, NULL
    // 0xae6d8c: b.ne            #0xae6d94
    // 0xae6d90: r2 = ""
    //     0xae6d90: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae6d94: stur            x2, [fp, #-0x10]
    // 0xae6d98: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xae6d98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae6d9c: ldr             x0, [x0, #0x1ab0]
    //     0xae6da0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae6da4: cmp             w0, w16
    //     0xae6da8: b.ne            #0xae6db8
    //     0xae6dac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xae6db0: ldr             x2, [x2, #0x60]
    //     0xae6db4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae6db8: LoadField: r2 = r0->field_87
    //     0xae6db8: ldur            w2, [x0, #0x87]
    // 0xae6dbc: DecompressPointer r2
    //     0xae6dbc: add             x2, x2, HEAP, lsl #32
    // 0xae6dc0: stur            x2, [fp, #-0x20]
    // 0xae6dc4: LoadField: r1 = r2->field_27
    //     0xae6dc4: ldur            w1, [x2, #0x27]
    // 0xae6dc8: DecompressPointer r1
    //     0xae6dc8: add             x1, x1, HEAP, lsl #32
    // 0xae6dcc: r16 = 16.000000
    //     0xae6dcc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae6dd0: ldr             x16, [x16, #0x188]
    // 0xae6dd4: r30 = Instance_Color
    //     0xae6dd4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae6dd8: stp             lr, x16, [SP]
    // 0xae6ddc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae6ddc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae6de0: ldr             x4, [x4, #0xaa0]
    // 0xae6de4: r0 = copyWith()
    //     0xae6de4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6de8: stur            x0, [fp, #-0x28]
    // 0xae6dec: r0 = Text()
    //     0xae6dec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae6df0: mov             x2, x0
    // 0xae6df4: ldur            x0, [fp, #-0x10]
    // 0xae6df8: stur            x2, [fp, #-0x30]
    // 0xae6dfc: StoreField: r2->field_b = r0
    //     0xae6dfc: stur            w0, [x2, #0xb]
    // 0xae6e00: ldur            x0, [fp, #-0x28]
    // 0xae6e04: StoreField: r2->field_13 = r0
    //     0xae6e04: stur            w0, [x2, #0x13]
    // 0xae6e08: r0 = 4
    //     0xae6e08: movz            x0, #0x4
    // 0xae6e0c: StoreField: r2->field_37 = r0
    //     0xae6e0c: stur            w0, [x2, #0x37]
    // 0xae6e10: r1 = <FlexParentData>
    //     0xae6e10: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xae6e14: ldr             x1, [x1, #0xe00]
    // 0xae6e18: r0 = Expanded()
    //     0xae6e18: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae6e1c: mov             x1, x0
    // 0xae6e20: r0 = 1
    //     0xae6e20: movz            x0, #0x1
    // 0xae6e24: stur            x1, [fp, #-0x10]
    // 0xae6e28: StoreField: r1->field_13 = r0
    //     0xae6e28: stur            x0, [x1, #0x13]
    // 0xae6e2c: r2 = Instance_FlexFit
    //     0xae6e2c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xae6e30: ldr             x2, [x2, #0xe08]
    // 0xae6e34: StoreField: r1->field_1b = r2
    //     0xae6e34: stur            w2, [x1, #0x1b]
    // 0xae6e38: ldur            x3, [fp, #-0x30]
    // 0xae6e3c: StoreField: r1->field_b = r3
    //     0xae6e3c: stur            w3, [x1, #0xb]
    // 0xae6e40: r0 = InkWell()
    //     0xae6e40: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xae6e44: mov             x3, x0
    // 0xae6e48: r0 = Instance_Icon
    //     0xae6e48: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xae6e4c: ldr             x0, [x0, #0x2b8]
    // 0xae6e50: stur            x3, [fp, #-0x28]
    // 0xae6e54: StoreField: r3->field_b = r0
    //     0xae6e54: stur            w0, [x3, #0xb]
    // 0xae6e58: r1 = Function '<anonymous closure>':.
    //     0xae6e58: add             x1, PP, #0x58, lsl #12  ; [pp+0x587f0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae6e5c: ldr             x1, [x1, #0x7f0]
    // 0xae6e60: r2 = Null
    //     0xae6e60: mov             x2, NULL
    // 0xae6e64: r0 = AllocateClosure()
    //     0xae6e64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae6e68: mov             x1, x0
    // 0xae6e6c: ldur            x0, [fp, #-0x28]
    // 0xae6e70: StoreField: r0->field_f = r1
    //     0xae6e70: stur            w1, [x0, #0xf]
    // 0xae6e74: r3 = true
    //     0xae6e74: add             x3, NULL, #0x20  ; true
    // 0xae6e78: StoreField: r0->field_43 = r3
    //     0xae6e78: stur            w3, [x0, #0x43]
    // 0xae6e7c: r1 = Instance_BoxShape
    //     0xae6e7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xae6e80: ldr             x1, [x1, #0x80]
    // 0xae6e84: StoreField: r0->field_47 = r1
    //     0xae6e84: stur            w1, [x0, #0x47]
    // 0xae6e88: StoreField: r0->field_6f = r3
    //     0xae6e88: stur            w3, [x0, #0x6f]
    // 0xae6e8c: r4 = false
    //     0xae6e8c: add             x4, NULL, #0x30  ; false
    // 0xae6e90: StoreField: r0->field_73 = r4
    //     0xae6e90: stur            w4, [x0, #0x73]
    // 0xae6e94: StoreField: r0->field_83 = r3
    //     0xae6e94: stur            w3, [x0, #0x83]
    // 0xae6e98: StoreField: r0->field_7b = r4
    //     0xae6e98: stur            w4, [x0, #0x7b]
    // 0xae6e9c: r1 = Null
    //     0xae6e9c: mov             x1, NULL
    // 0xae6ea0: r2 = 4
    //     0xae6ea0: movz            x2, #0x4
    // 0xae6ea4: r0 = AllocateArray()
    //     0xae6ea4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae6ea8: mov             x2, x0
    // 0xae6eac: ldur            x0, [fp, #-0x10]
    // 0xae6eb0: stur            x2, [fp, #-0x30]
    // 0xae6eb4: StoreField: r2->field_f = r0
    //     0xae6eb4: stur            w0, [x2, #0xf]
    // 0xae6eb8: ldur            x0, [fp, #-0x28]
    // 0xae6ebc: StoreField: r2->field_13 = r0
    //     0xae6ebc: stur            w0, [x2, #0x13]
    // 0xae6ec0: r1 = <Widget>
    //     0xae6ec0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae6ec4: r0 = AllocateGrowableArray()
    //     0xae6ec4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae6ec8: mov             x1, x0
    // 0xae6ecc: ldur            x0, [fp, #-0x30]
    // 0xae6ed0: stur            x1, [fp, #-0x10]
    // 0xae6ed4: StoreField: r1->field_f = r0
    //     0xae6ed4: stur            w0, [x1, #0xf]
    // 0xae6ed8: r0 = 4
    //     0xae6ed8: movz            x0, #0x4
    // 0xae6edc: StoreField: r1->field_b = r0
    //     0xae6edc: stur            w0, [x1, #0xb]
    // 0xae6ee0: r0 = Row()
    //     0xae6ee0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae6ee4: mov             x1, x0
    // 0xae6ee8: r0 = Instance_Axis
    //     0xae6ee8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xae6eec: stur            x1, [fp, #-0x28]
    // 0xae6ef0: StoreField: r1->field_f = r0
    //     0xae6ef0: stur            w0, [x1, #0xf]
    // 0xae6ef4: r2 = Instance_MainAxisAlignment
    //     0xae6ef4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xae6ef8: ldr             x2, [x2, #0xa8]
    // 0xae6efc: StoreField: r1->field_13 = r2
    //     0xae6efc: stur            w2, [x1, #0x13]
    // 0xae6f00: r2 = Instance_MainAxisSize
    //     0xae6f00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae6f04: ldr             x2, [x2, #0xa10]
    // 0xae6f08: ArrayStore: r1[0] = r2  ; List_4
    //     0xae6f08: stur            w2, [x1, #0x17]
    // 0xae6f0c: r3 = Instance_CrossAxisAlignment
    //     0xae6f0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae6f10: ldr             x3, [x3, #0xa18]
    // 0xae6f14: StoreField: r1->field_1b = r3
    //     0xae6f14: stur            w3, [x1, #0x1b]
    // 0xae6f18: r4 = Instance_VerticalDirection
    //     0xae6f18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae6f1c: ldr             x4, [x4, #0xa20]
    // 0xae6f20: StoreField: r1->field_23 = r4
    //     0xae6f20: stur            w4, [x1, #0x23]
    // 0xae6f24: r5 = Instance_Clip
    //     0xae6f24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae6f28: ldr             x5, [x5, #0x38]
    // 0xae6f2c: StoreField: r1->field_2b = r5
    //     0xae6f2c: stur            w5, [x1, #0x2b]
    // 0xae6f30: StoreField: r1->field_2f = rZR
    //     0xae6f30: stur            xzr, [x1, #0x2f]
    // 0xae6f34: ldur            x6, [fp, #-0x10]
    // 0xae6f38: StoreField: r1->field_b = r6
    //     0xae6f38: stur            w6, [x1, #0xb]
    // 0xae6f3c: r16 = <EdgeInsets>
    //     0xae6f3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xae6f40: ldr             x16, [x16, #0xda0]
    // 0xae6f44: r30 = Instance_EdgeInsets
    //     0xae6f44: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xae6f48: ldr             lr, [lr, #0x1f0]
    // 0xae6f4c: stp             lr, x16, [SP]
    // 0xae6f50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae6f50: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae6f54: r0 = all()
    //     0xae6f54: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae6f58: mov             x1, x0
    // 0xae6f5c: ldur            x0, [fp, #-8]
    // 0xae6f60: stur            x1, [fp, #-0x10]
    // 0xae6f64: LoadField: r2 = r0->field_b
    //     0xae6f64: ldur            w2, [x0, #0xb]
    // 0xae6f68: DecompressPointer r2
    //     0xae6f68: add             x2, x2, HEAP, lsl #32
    // 0xae6f6c: cmp             w2, NULL
    // 0xae6f70: b.eq            #0xae7908
    // 0xae6f74: LoadField: r3 = r2->field_33
    //     0xae6f74: ldur            w3, [x2, #0x33]
    // 0xae6f78: DecompressPointer r3
    //     0xae6f78: add             x3, x3, HEAP, lsl #32
    // 0xae6f7c: LoadField: r2 = r3->field_3f
    //     0xae6f7c: ldur            w2, [x3, #0x3f]
    // 0xae6f80: DecompressPointer r2
    //     0xae6f80: add             x2, x2, HEAP, lsl #32
    // 0xae6f84: cmp             w2, NULL
    // 0xae6f88: b.ne            #0xae6f94
    // 0xae6f8c: r3 = Null
    //     0xae6f8c: mov             x3, NULL
    // 0xae6f90: b               #0xae6fb8
    // 0xae6f94: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae6f94: ldur            w3, [x2, #0x17]
    // 0xae6f98: DecompressPointer r3
    //     0xae6f98: add             x3, x3, HEAP, lsl #32
    // 0xae6f9c: cmp             w3, NULL
    // 0xae6fa0: b.ne            #0xae6fac
    // 0xae6fa4: r3 = Null
    //     0xae6fa4: mov             x3, NULL
    // 0xae6fa8: b               #0xae6fb8
    // 0xae6fac: LoadField: r4 = r3->field_7
    //     0xae6fac: ldur            w4, [x3, #7]
    // 0xae6fb0: DecompressPointer r4
    //     0xae6fb0: add             x4, x4, HEAP, lsl #32
    // 0xae6fb4: mov             x3, x4
    // 0xae6fb8: cmp             w3, NULL
    // 0xae6fbc: b.ne            #0xae6fc8
    // 0xae6fc0: r3 = 0
    //     0xae6fc0: movz            x3, #0
    // 0xae6fc4: b               #0xae6fd8
    // 0xae6fc8: r4 = LoadInt32Instr(r3)
    //     0xae6fc8: sbfx            x4, x3, #1, #0x1f
    //     0xae6fcc: tbz             w3, #0, #0xae6fd4
    //     0xae6fd0: ldur            x4, [x3, #7]
    // 0xae6fd4: mov             x3, x4
    // 0xae6fd8: stur            x3, [fp, #-0x48]
    // 0xae6fdc: cmp             w2, NULL
    // 0xae6fe0: b.ne            #0xae6fec
    // 0xae6fe4: r4 = Null
    //     0xae6fe4: mov             x4, NULL
    // 0xae6fe8: b               #0xae7010
    // 0xae6fec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xae6fec: ldur            w4, [x2, #0x17]
    // 0xae6ff0: DecompressPointer r4
    //     0xae6ff0: add             x4, x4, HEAP, lsl #32
    // 0xae6ff4: cmp             w4, NULL
    // 0xae6ff8: b.ne            #0xae7004
    // 0xae6ffc: r4 = Null
    //     0xae6ffc: mov             x4, NULL
    // 0xae7000: b               #0xae7010
    // 0xae7004: LoadField: r5 = r4->field_b
    //     0xae7004: ldur            w5, [x4, #0xb]
    // 0xae7008: DecompressPointer r5
    //     0xae7008: add             x5, x5, HEAP, lsl #32
    // 0xae700c: mov             x4, x5
    // 0xae7010: cmp             w4, NULL
    // 0xae7014: b.ne            #0xae7020
    // 0xae7018: r4 = 0
    //     0xae7018: movz            x4, #0
    // 0xae701c: b               #0xae7030
    // 0xae7020: r5 = LoadInt32Instr(r4)
    //     0xae7020: sbfx            x5, x4, #1, #0x1f
    //     0xae7024: tbz             w4, #0, #0xae702c
    //     0xae7028: ldur            x5, [x4, #7]
    // 0xae702c: mov             x4, x5
    // 0xae7030: stur            x4, [fp, #-0x40]
    // 0xae7034: cmp             w2, NULL
    // 0xae7038: b.ne            #0xae7044
    // 0xae703c: r2 = Null
    //     0xae703c: mov             x2, NULL
    // 0xae7040: b               #0xae7064
    // 0xae7044: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xae7044: ldur            w5, [x2, #0x17]
    // 0xae7048: DecompressPointer r5
    //     0xae7048: add             x5, x5, HEAP, lsl #32
    // 0xae704c: cmp             w5, NULL
    // 0xae7050: b.ne            #0xae705c
    // 0xae7054: r2 = Null
    //     0xae7054: mov             x2, NULL
    // 0xae7058: b               #0xae7064
    // 0xae705c: LoadField: r2 = r5->field_f
    //     0xae705c: ldur            w2, [x5, #0xf]
    // 0xae7060: DecompressPointer r2
    //     0xae7060: add             x2, x2, HEAP, lsl #32
    // 0xae7064: cmp             w2, NULL
    // 0xae7068: b.ne            #0xae7074
    // 0xae706c: r5 = 0
    //     0xae706c: movz            x5, #0
    // 0xae7070: b               #0xae7080
    // 0xae7074: r5 = LoadInt32Instr(r2)
    //     0xae7074: sbfx            x5, x2, #1, #0x1f
    //     0xae7078: tbz             w2, #0, #0xae7080
    //     0xae707c: ldur            x5, [x2, #7]
    // 0xae7080: ldur            x2, [fp, #-0x20]
    // 0xae7084: stur            x5, [fp, #-0x38]
    // 0xae7088: r0 = Color()
    //     0xae7088: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xae708c: mov             x1, x0
    // 0xae7090: r0 = Instance_ColorSpace
    //     0xae7090: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xae7094: stur            x1, [fp, #-0x30]
    // 0xae7098: StoreField: r1->field_27 = r0
    //     0xae7098: stur            w0, [x1, #0x27]
    // 0xae709c: d0 = 0.500000
    //     0xae709c: fmov            d0, #0.50000000
    // 0xae70a0: StoreField: r1->field_7 = d0
    //     0xae70a0: stur            d0, [x1, #7]
    // 0xae70a4: ldur            x2, [fp, #-0x48]
    // 0xae70a8: ubfx            x2, x2, #0, #0x20
    // 0xae70ac: and             w3, w2, #0xff
    // 0xae70b0: ubfx            x3, x3, #0, #0x20
    // 0xae70b4: scvtf           d0, x3
    // 0xae70b8: d1 = 255.000000
    //     0xae70b8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xae70bc: fdiv            d2, d0, d1
    // 0xae70c0: StoreField: r1->field_f = d2
    //     0xae70c0: stur            d2, [x1, #0xf]
    // 0xae70c4: ldur            x2, [fp, #-0x40]
    // 0xae70c8: ubfx            x2, x2, #0, #0x20
    // 0xae70cc: and             w3, w2, #0xff
    // 0xae70d0: ubfx            x3, x3, #0, #0x20
    // 0xae70d4: scvtf           d0, x3
    // 0xae70d8: fdiv            d2, d0, d1
    // 0xae70dc: ArrayStore: r1[0] = d2  ; List_8
    //     0xae70dc: stur            d2, [x1, #0x17]
    // 0xae70e0: ldur            x2, [fp, #-0x38]
    // 0xae70e4: ubfx            x2, x2, #0, #0x20
    // 0xae70e8: and             w3, w2, #0xff
    // 0xae70ec: ubfx            x3, x3, #0, #0x20
    // 0xae70f0: scvtf           d0, x3
    // 0xae70f4: fdiv            d2, d0, d1
    // 0xae70f8: StoreField: r1->field_1f = d2
    //     0xae70f8: stur            d2, [x1, #0x1f]
    // 0xae70fc: r0 = BorderSide()
    //     0xae70fc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xae7100: mov             x1, x0
    // 0xae7104: ldur            x0, [fp, #-0x30]
    // 0xae7108: stur            x1, [fp, #-0x50]
    // 0xae710c: StoreField: r1->field_7 = r0
    //     0xae710c: stur            w0, [x1, #7]
    // 0xae7110: d0 = 1.000000
    //     0xae7110: fmov            d0, #1.00000000
    // 0xae7114: StoreField: r1->field_b = d0
    //     0xae7114: stur            d0, [x1, #0xb]
    // 0xae7118: r0 = Instance_BorderStyle
    //     0xae7118: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xae711c: ldr             x0, [x0, #0xf68]
    // 0xae7120: StoreField: r1->field_13 = r0
    //     0xae7120: stur            w0, [x1, #0x13]
    // 0xae7124: d1 = -1.000000
    //     0xae7124: fmov            d1, #-1.00000000
    // 0xae7128: ArrayStore: r1[0] = d1  ; List_8
    //     0xae7128: stur            d1, [x1, #0x17]
    // 0xae712c: r0 = Radius()
    //     0xae712c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae7130: d0 = 30.000000
    //     0xae7130: fmov            d0, #30.00000000
    // 0xae7134: stur            x0, [fp, #-0x30]
    // 0xae7138: StoreField: r0->field_7 = d0
    //     0xae7138: stur            d0, [x0, #7]
    // 0xae713c: StoreField: r0->field_f = d0
    //     0xae713c: stur            d0, [x0, #0xf]
    // 0xae7140: r0 = BorderRadius()
    //     0xae7140: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae7144: mov             x1, x0
    // 0xae7148: ldur            x0, [fp, #-0x30]
    // 0xae714c: stur            x1, [fp, #-0x58]
    // 0xae7150: StoreField: r1->field_7 = r0
    //     0xae7150: stur            w0, [x1, #7]
    // 0xae7154: StoreField: r1->field_b = r0
    //     0xae7154: stur            w0, [x1, #0xb]
    // 0xae7158: StoreField: r1->field_f = r0
    //     0xae7158: stur            w0, [x1, #0xf]
    // 0xae715c: StoreField: r1->field_13 = r0
    //     0xae715c: stur            w0, [x1, #0x13]
    // 0xae7160: r0 = RoundedRectangleBorder()
    //     0xae7160: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xae7164: mov             x1, x0
    // 0xae7168: ldur            x0, [fp, #-0x58]
    // 0xae716c: StoreField: r1->field_b = r0
    //     0xae716c: stur            w0, [x1, #0xb]
    // 0xae7170: ldur            x0, [fp, #-0x50]
    // 0xae7174: StoreField: r1->field_7 = r0
    //     0xae7174: stur            w0, [x1, #7]
    // 0xae7178: r16 = <RoundedRectangleBorder>
    //     0xae7178: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xae717c: ldr             x16, [x16, #0xf78]
    // 0xae7180: stp             x1, x16, [SP]
    // 0xae7184: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae7184: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae7188: r0 = all()
    //     0xae7188: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae718c: stur            x0, [fp, #-0x30]
    // 0xae7190: r0 = ButtonStyle()
    //     0xae7190: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xae7194: mov             x1, x0
    // 0xae7198: ldur            x0, [fp, #-0x10]
    // 0xae719c: stur            x1, [fp, #-0x50]
    // 0xae71a0: StoreField: r1->field_23 = r0
    //     0xae71a0: stur            w0, [x1, #0x23]
    // 0xae71a4: ldur            x0, [fp, #-0x30]
    // 0xae71a8: StoreField: r1->field_43 = r0
    //     0xae71a8: stur            w0, [x1, #0x43]
    // 0xae71ac: r0 = TextButtonThemeData()
    //     0xae71ac: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xae71b0: mov             x1, x0
    // 0xae71b4: ldur            x0, [fp, #-0x50]
    // 0xae71b8: stur            x1, [fp, #-0x30]
    // 0xae71bc: StoreField: r1->field_7 = r0
    //     0xae71bc: stur            w0, [x1, #7]
    // 0xae71c0: ldur            x0, [fp, #-0x20]
    // 0xae71c4: LoadField: r2 = r0->field_7
    //     0xae71c4: ldur            w2, [x0, #7]
    // 0xae71c8: DecompressPointer r2
    //     0xae71c8: add             x2, x2, HEAP, lsl #32
    // 0xae71cc: ldur            x0, [fp, #-8]
    // 0xae71d0: stur            x2, [fp, #-0x10]
    // 0xae71d4: LoadField: r3 = r0->field_b
    //     0xae71d4: ldur            w3, [x0, #0xb]
    // 0xae71d8: DecompressPointer r3
    //     0xae71d8: add             x3, x3, HEAP, lsl #32
    // 0xae71dc: cmp             w3, NULL
    // 0xae71e0: b.eq            #0xae790c
    // 0xae71e4: LoadField: r4 = r3->field_33
    //     0xae71e4: ldur            w4, [x3, #0x33]
    // 0xae71e8: DecompressPointer r4
    //     0xae71e8: add             x4, x4, HEAP, lsl #32
    // 0xae71ec: LoadField: r3 = r4->field_3f
    //     0xae71ec: ldur            w3, [x4, #0x3f]
    // 0xae71f0: DecompressPointer r3
    //     0xae71f0: add             x3, x3, HEAP, lsl #32
    // 0xae71f4: cmp             w3, NULL
    // 0xae71f8: b.ne            #0xae7204
    // 0xae71fc: r4 = Null
    //     0xae71fc: mov             x4, NULL
    // 0xae7200: b               #0xae7228
    // 0xae7204: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xae7204: ldur            w4, [x3, #0x17]
    // 0xae7208: DecompressPointer r4
    //     0xae7208: add             x4, x4, HEAP, lsl #32
    // 0xae720c: cmp             w4, NULL
    // 0xae7210: b.ne            #0xae721c
    // 0xae7214: r4 = Null
    //     0xae7214: mov             x4, NULL
    // 0xae7218: b               #0xae7228
    // 0xae721c: LoadField: r5 = r4->field_7
    //     0xae721c: ldur            w5, [x4, #7]
    // 0xae7220: DecompressPointer r5
    //     0xae7220: add             x5, x5, HEAP, lsl #32
    // 0xae7224: mov             x4, x5
    // 0xae7228: cmp             w4, NULL
    // 0xae722c: b.ne            #0xae7238
    // 0xae7230: r4 = 0
    //     0xae7230: movz            x4, #0
    // 0xae7234: b               #0xae7248
    // 0xae7238: r5 = LoadInt32Instr(r4)
    //     0xae7238: sbfx            x5, x4, #1, #0x1f
    //     0xae723c: tbz             w4, #0, #0xae7244
    //     0xae7240: ldur            x5, [x4, #7]
    // 0xae7244: mov             x4, x5
    // 0xae7248: stur            x4, [fp, #-0x48]
    // 0xae724c: cmp             w3, NULL
    // 0xae7250: b.ne            #0xae725c
    // 0xae7254: r5 = Null
    //     0xae7254: mov             x5, NULL
    // 0xae7258: b               #0xae7280
    // 0xae725c: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xae725c: ldur            w5, [x3, #0x17]
    // 0xae7260: DecompressPointer r5
    //     0xae7260: add             x5, x5, HEAP, lsl #32
    // 0xae7264: cmp             w5, NULL
    // 0xae7268: b.ne            #0xae7274
    // 0xae726c: r5 = Null
    //     0xae726c: mov             x5, NULL
    // 0xae7270: b               #0xae7280
    // 0xae7274: LoadField: r6 = r5->field_b
    //     0xae7274: ldur            w6, [x5, #0xb]
    // 0xae7278: DecompressPointer r6
    //     0xae7278: add             x6, x6, HEAP, lsl #32
    // 0xae727c: mov             x5, x6
    // 0xae7280: cmp             w5, NULL
    // 0xae7284: b.ne            #0xae7290
    // 0xae7288: r5 = 0
    //     0xae7288: movz            x5, #0
    // 0xae728c: b               #0xae72a0
    // 0xae7290: r6 = LoadInt32Instr(r5)
    //     0xae7290: sbfx            x6, x5, #1, #0x1f
    //     0xae7294: tbz             w5, #0, #0xae729c
    //     0xae7298: ldur            x6, [x5, #7]
    // 0xae729c: mov             x5, x6
    // 0xae72a0: stur            x5, [fp, #-0x40]
    // 0xae72a4: cmp             w3, NULL
    // 0xae72a8: b.ne            #0xae72b4
    // 0xae72ac: r3 = Null
    //     0xae72ac: mov             x3, NULL
    // 0xae72b0: b               #0xae72d4
    // 0xae72b4: ArrayLoad: r6 = r3[0]  ; List_4
    //     0xae72b4: ldur            w6, [x3, #0x17]
    // 0xae72b8: DecompressPointer r6
    //     0xae72b8: add             x6, x6, HEAP, lsl #32
    // 0xae72bc: cmp             w6, NULL
    // 0xae72c0: b.ne            #0xae72cc
    // 0xae72c4: r3 = Null
    //     0xae72c4: mov             x3, NULL
    // 0xae72c8: b               #0xae72d4
    // 0xae72cc: LoadField: r3 = r6->field_f
    //     0xae72cc: ldur            w3, [x6, #0xf]
    // 0xae72d0: DecompressPointer r3
    //     0xae72d0: add             x3, x3, HEAP, lsl #32
    // 0xae72d4: cmp             w3, NULL
    // 0xae72d8: b.ne            #0xae72e4
    // 0xae72dc: r3 = 0
    //     0xae72dc: movz            x3, #0
    // 0xae72e0: b               #0xae72f4
    // 0xae72e4: r6 = LoadInt32Instr(r3)
    //     0xae72e4: sbfx            x6, x3, #1, #0x1f
    //     0xae72e8: tbz             w3, #0, #0xae72f0
    //     0xae72ec: ldur            x6, [x3, #7]
    // 0xae72f0: mov             x3, x6
    // 0xae72f4: stur            x3, [fp, #-0x38]
    // 0xae72f8: r0 = Color()
    //     0xae72f8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xae72fc: mov             x1, x0
    // 0xae7300: r0 = Instance_ColorSpace
    //     0xae7300: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xae7304: StoreField: r1->field_27 = r0
    //     0xae7304: stur            w0, [x1, #0x27]
    // 0xae7308: d0 = 1.000000
    //     0xae7308: fmov            d0, #1.00000000
    // 0xae730c: StoreField: r1->field_7 = d0
    //     0xae730c: stur            d0, [x1, #7]
    // 0xae7310: ldur            x2, [fp, #-0x48]
    // 0xae7314: ubfx            x2, x2, #0, #0x20
    // 0xae7318: and             w3, w2, #0xff
    // 0xae731c: ubfx            x3, x3, #0, #0x20
    // 0xae7320: scvtf           d1, x3
    // 0xae7324: d2 = 255.000000
    //     0xae7324: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xae7328: fdiv            d3, d1, d2
    // 0xae732c: StoreField: r1->field_f = d3
    //     0xae732c: stur            d3, [x1, #0xf]
    // 0xae7330: ldur            x2, [fp, #-0x40]
    // 0xae7334: ubfx            x2, x2, #0, #0x20
    // 0xae7338: and             w3, w2, #0xff
    // 0xae733c: ubfx            x3, x3, #0, #0x20
    // 0xae7340: scvtf           d1, x3
    // 0xae7344: fdiv            d3, d1, d2
    // 0xae7348: ArrayStore: r1[0] = d3  ; List_8
    //     0xae7348: stur            d3, [x1, #0x17]
    // 0xae734c: ldur            x2, [fp, #-0x38]
    // 0xae7350: ubfx            x2, x2, #0, #0x20
    // 0xae7354: and             w3, w2, #0xff
    // 0xae7358: ubfx            x3, x3, #0, #0x20
    // 0xae735c: scvtf           d1, x3
    // 0xae7360: fdiv            d3, d1, d2
    // 0xae7364: StoreField: r1->field_1f = d3
    //     0xae7364: stur            d3, [x1, #0x1f]
    // 0xae7368: r16 = 16.000000
    //     0xae7368: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae736c: ldr             x16, [x16, #0x188]
    // 0xae7370: stp             x1, x16, [SP]
    // 0xae7374: ldur            x1, [fp, #-0x10]
    // 0xae7378: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae7378: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae737c: ldr             x4, [x4, #0xaa0]
    // 0xae7380: r0 = copyWith()
    //     0xae7380: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae7384: stur            x0, [fp, #-0x20]
    // 0xae7388: r0 = Text()
    //     0xae7388: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae738c: mov             x3, x0
    // 0xae7390: r0 = "Skip"
    //     0xae7390: add             x0, PP, #0x58, lsl #12  ; [pp+0x587f8] "Skip"
    //     0xae7394: ldr             x0, [x0, #0x7f8]
    // 0xae7398: stur            x3, [fp, #-0x50]
    // 0xae739c: StoreField: r3->field_b = r0
    //     0xae739c: stur            w0, [x3, #0xb]
    // 0xae73a0: ldur            x0, [fp, #-0x20]
    // 0xae73a4: StoreField: r3->field_13 = r0
    //     0xae73a4: stur            w0, [x3, #0x13]
    // 0xae73a8: ldur            x2, [fp, #-0x18]
    // 0xae73ac: r1 = Function '<anonymous closure>':.
    //     0xae73ac: add             x1, PP, #0x58, lsl #12  ; [pp+0x58800] AnonymousClosure: (0xae7c4c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae73b0: ldr             x1, [x1, #0x800]
    // 0xae73b4: r0 = AllocateClosure()
    //     0xae73b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae73b8: stur            x0, [fp, #-0x20]
    // 0xae73bc: r0 = TextButton()
    //     0xae73bc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae73c0: mov             x1, x0
    // 0xae73c4: ldur            x0, [fp, #-0x20]
    // 0xae73c8: stur            x1, [fp, #-0x58]
    // 0xae73cc: StoreField: r1->field_b = r0
    //     0xae73cc: stur            w0, [x1, #0xb]
    // 0xae73d0: r0 = false
    //     0xae73d0: add             x0, NULL, #0x30  ; false
    // 0xae73d4: StoreField: r1->field_27 = r0
    //     0xae73d4: stur            w0, [x1, #0x27]
    // 0xae73d8: r2 = true
    //     0xae73d8: add             x2, NULL, #0x20  ; true
    // 0xae73dc: StoreField: r1->field_2f = r2
    //     0xae73dc: stur            w2, [x1, #0x2f]
    // 0xae73e0: ldur            x3, [fp, #-0x50]
    // 0xae73e4: StoreField: r1->field_37 = r3
    //     0xae73e4: stur            w3, [x1, #0x37]
    // 0xae73e8: r0 = TextButtonTheme()
    //     0xae73e8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xae73ec: mov             x2, x0
    // 0xae73f0: ldur            x0, [fp, #-0x30]
    // 0xae73f4: stur            x2, [fp, #-0x20]
    // 0xae73f8: StoreField: r2->field_f = r0
    //     0xae73f8: stur            w0, [x2, #0xf]
    // 0xae73fc: ldur            x0, [fp, #-0x58]
    // 0xae7400: StoreField: r2->field_b = r0
    //     0xae7400: stur            w0, [x2, #0xb]
    // 0xae7404: r1 = <FlexParentData>
    //     0xae7404: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xae7408: ldr             x1, [x1, #0xe00]
    // 0xae740c: r0 = Expanded()
    //     0xae740c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae7410: mov             x1, x0
    // 0xae7414: r0 = 1
    //     0xae7414: movz            x0, #0x1
    // 0xae7418: stur            x1, [fp, #-0x30]
    // 0xae741c: StoreField: r1->field_13 = r0
    //     0xae741c: stur            x0, [x1, #0x13]
    // 0xae7420: r2 = Instance_FlexFit
    //     0xae7420: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xae7424: ldr             x2, [x2, #0xe08]
    // 0xae7428: StoreField: r1->field_1b = r2
    //     0xae7428: stur            w2, [x1, #0x1b]
    // 0xae742c: ldur            x3, [fp, #-0x20]
    // 0xae7430: StoreField: r1->field_b = r3
    //     0xae7430: stur            w3, [x1, #0xb]
    // 0xae7434: r16 = <EdgeInsets>
    //     0xae7434: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xae7438: ldr             x16, [x16, #0xda0]
    // 0xae743c: r30 = Instance_EdgeInsets
    //     0xae743c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xae7440: ldr             lr, [lr, #0x1f0]
    // 0xae7444: stp             lr, x16, [SP]
    // 0xae7448: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae7448: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae744c: r0 = all()
    //     0xae744c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae7450: mov             x1, x0
    // 0xae7454: ldur            x0, [fp, #-8]
    // 0xae7458: stur            x1, [fp, #-0x20]
    // 0xae745c: LoadField: r2 = r0->field_b
    //     0xae745c: ldur            w2, [x0, #0xb]
    // 0xae7460: DecompressPointer r2
    //     0xae7460: add             x2, x2, HEAP, lsl #32
    // 0xae7464: cmp             w2, NULL
    // 0xae7468: b.eq            #0xae7910
    // 0xae746c: LoadField: r0 = r2->field_33
    //     0xae746c: ldur            w0, [x2, #0x33]
    // 0xae7470: DecompressPointer r0
    //     0xae7470: add             x0, x0, HEAP, lsl #32
    // 0xae7474: LoadField: r2 = r0->field_3f
    //     0xae7474: ldur            w2, [x0, #0x3f]
    // 0xae7478: DecompressPointer r2
    //     0xae7478: add             x2, x2, HEAP, lsl #32
    // 0xae747c: cmp             w2, NULL
    // 0xae7480: b.ne            #0xae748c
    // 0xae7484: r0 = Null
    //     0xae7484: mov             x0, NULL
    // 0xae7488: b               #0xae74b0
    // 0xae748c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xae748c: ldur            w0, [x2, #0x17]
    // 0xae7490: DecompressPointer r0
    //     0xae7490: add             x0, x0, HEAP, lsl #32
    // 0xae7494: cmp             w0, NULL
    // 0xae7498: b.ne            #0xae74a4
    // 0xae749c: r0 = Null
    //     0xae749c: mov             x0, NULL
    // 0xae74a0: b               #0xae74b0
    // 0xae74a4: LoadField: r3 = r0->field_7
    //     0xae74a4: ldur            w3, [x0, #7]
    // 0xae74a8: DecompressPointer r3
    //     0xae74a8: add             x3, x3, HEAP, lsl #32
    // 0xae74ac: mov             x0, x3
    // 0xae74b0: cmp             w0, NULL
    // 0xae74b4: b.ne            #0xae74c0
    // 0xae74b8: r0 = 0
    //     0xae74b8: movz            x0, #0
    // 0xae74bc: b               #0xae74d0
    // 0xae74c0: r3 = LoadInt32Instr(r0)
    //     0xae74c0: sbfx            x3, x0, #1, #0x1f
    //     0xae74c4: tbz             w0, #0, #0xae74cc
    //     0xae74c8: ldur            x3, [x0, #7]
    // 0xae74cc: mov             x0, x3
    // 0xae74d0: stur            x0, [fp, #-0x48]
    // 0xae74d4: cmp             w2, NULL
    // 0xae74d8: b.ne            #0xae74e4
    // 0xae74dc: r3 = Null
    //     0xae74dc: mov             x3, NULL
    // 0xae74e0: b               #0xae7508
    // 0xae74e4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae74e4: ldur            w3, [x2, #0x17]
    // 0xae74e8: DecompressPointer r3
    //     0xae74e8: add             x3, x3, HEAP, lsl #32
    // 0xae74ec: cmp             w3, NULL
    // 0xae74f0: b.ne            #0xae74fc
    // 0xae74f4: r3 = Null
    //     0xae74f4: mov             x3, NULL
    // 0xae74f8: b               #0xae7508
    // 0xae74fc: LoadField: r4 = r3->field_b
    //     0xae74fc: ldur            w4, [x3, #0xb]
    // 0xae7500: DecompressPointer r4
    //     0xae7500: add             x4, x4, HEAP, lsl #32
    // 0xae7504: mov             x3, x4
    // 0xae7508: cmp             w3, NULL
    // 0xae750c: b.ne            #0xae7518
    // 0xae7510: r3 = 0
    //     0xae7510: movz            x3, #0
    // 0xae7514: b               #0xae7528
    // 0xae7518: r4 = LoadInt32Instr(r3)
    //     0xae7518: sbfx            x4, x3, #1, #0x1f
    //     0xae751c: tbz             w3, #0, #0xae7524
    //     0xae7520: ldur            x4, [x3, #7]
    // 0xae7524: mov             x3, x4
    // 0xae7528: stur            x3, [fp, #-0x40]
    // 0xae752c: cmp             w2, NULL
    // 0xae7530: b.ne            #0xae753c
    // 0xae7534: r2 = Null
    //     0xae7534: mov             x2, NULL
    // 0xae7538: b               #0xae755c
    // 0xae753c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xae753c: ldur            w4, [x2, #0x17]
    // 0xae7540: DecompressPointer r4
    //     0xae7540: add             x4, x4, HEAP, lsl #32
    // 0xae7544: cmp             w4, NULL
    // 0xae7548: b.ne            #0xae7554
    // 0xae754c: r2 = Null
    //     0xae754c: mov             x2, NULL
    // 0xae7550: b               #0xae755c
    // 0xae7554: LoadField: r2 = r4->field_f
    //     0xae7554: ldur            w2, [x4, #0xf]
    // 0xae7558: DecompressPointer r2
    //     0xae7558: add             x2, x2, HEAP, lsl #32
    // 0xae755c: cmp             w2, NULL
    // 0xae7560: b.ne            #0xae756c
    // 0xae7564: r5 = 0
    //     0xae7564: movz            x5, #0
    // 0xae7568: b               #0xae757c
    // 0xae756c: r4 = LoadInt32Instr(r2)
    //     0xae756c: sbfx            x4, x2, #1, #0x1f
    //     0xae7570: tbz             w2, #0, #0xae7578
    //     0xae7574: ldur            x4, [x2, #7]
    // 0xae7578: mov             x5, x4
    // 0xae757c: ldur            x4, [fp, #-0x28]
    // 0xae7580: ldur            x2, [fp, #-0x30]
    // 0xae7584: stur            x5, [fp, #-0x38]
    // 0xae7588: r0 = Color()
    //     0xae7588: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xae758c: mov             x1, x0
    // 0xae7590: r0 = Instance_ColorSpace
    //     0xae7590: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xae7594: StoreField: r1->field_27 = r0
    //     0xae7594: stur            w0, [x1, #0x27]
    // 0xae7598: d0 = 1.000000
    //     0xae7598: fmov            d0, #1.00000000
    // 0xae759c: StoreField: r1->field_7 = d0
    //     0xae759c: stur            d0, [x1, #7]
    // 0xae75a0: ldur            x0, [fp, #-0x48]
    // 0xae75a4: ubfx            x0, x0, #0, #0x20
    // 0xae75a8: and             w2, w0, #0xff
    // 0xae75ac: ubfx            x2, x2, #0, #0x20
    // 0xae75b0: scvtf           d0, x2
    // 0xae75b4: d1 = 255.000000
    //     0xae75b4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xae75b8: fdiv            d2, d0, d1
    // 0xae75bc: StoreField: r1->field_f = d2
    //     0xae75bc: stur            d2, [x1, #0xf]
    // 0xae75c0: ldur            x0, [fp, #-0x40]
    // 0xae75c4: ubfx            x0, x0, #0, #0x20
    // 0xae75c8: and             w2, w0, #0xff
    // 0xae75cc: ubfx            x2, x2, #0, #0x20
    // 0xae75d0: scvtf           d0, x2
    // 0xae75d4: fdiv            d2, d0, d1
    // 0xae75d8: ArrayStore: r1[0] = d2  ; List_8
    //     0xae75d8: stur            d2, [x1, #0x17]
    // 0xae75dc: ldur            x0, [fp, #-0x38]
    // 0xae75e0: ubfx            x0, x0, #0, #0x20
    // 0xae75e4: and             w2, w0, #0xff
    // 0xae75e8: ubfx            x2, x2, #0, #0x20
    // 0xae75ec: scvtf           d0, x2
    // 0xae75f0: fdiv            d2, d0, d1
    // 0xae75f4: StoreField: r1->field_1f = d2
    //     0xae75f4: stur            d2, [x1, #0x1f]
    // 0xae75f8: r16 = <Color>
    //     0xae75f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xae75fc: ldr             x16, [x16, #0xf80]
    // 0xae7600: stp             x1, x16, [SP]
    // 0xae7604: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae7604: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae7608: r0 = all()
    //     0xae7608: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae760c: stur            x0, [fp, #-8]
    // 0xae7610: r0 = Radius()
    //     0xae7610: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae7614: d0 = 30.000000
    //     0xae7614: fmov            d0, #30.00000000
    // 0xae7618: stur            x0, [fp, #-0x50]
    // 0xae761c: StoreField: r0->field_7 = d0
    //     0xae761c: stur            d0, [x0, #7]
    // 0xae7620: StoreField: r0->field_f = d0
    //     0xae7620: stur            d0, [x0, #0xf]
    // 0xae7624: r0 = BorderRadius()
    //     0xae7624: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae7628: mov             x1, x0
    // 0xae762c: ldur            x0, [fp, #-0x50]
    // 0xae7630: stur            x1, [fp, #-0x58]
    // 0xae7634: StoreField: r1->field_7 = r0
    //     0xae7634: stur            w0, [x1, #7]
    // 0xae7638: StoreField: r1->field_b = r0
    //     0xae7638: stur            w0, [x1, #0xb]
    // 0xae763c: StoreField: r1->field_f = r0
    //     0xae763c: stur            w0, [x1, #0xf]
    // 0xae7640: StoreField: r1->field_13 = r0
    //     0xae7640: stur            w0, [x1, #0x13]
    // 0xae7644: r0 = RoundedRectangleBorder()
    //     0xae7644: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xae7648: mov             x1, x0
    // 0xae764c: ldur            x0, [fp, #-0x58]
    // 0xae7650: StoreField: r1->field_b = r0
    //     0xae7650: stur            w0, [x1, #0xb]
    // 0xae7654: r0 = Instance_BorderSide
    //     0xae7654: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xae7658: ldr             x0, [x0, #0xe20]
    // 0xae765c: StoreField: r1->field_7 = r0
    //     0xae765c: stur            w0, [x1, #7]
    // 0xae7660: r16 = <RoundedRectangleBorder>
    //     0xae7660: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xae7664: ldr             x16, [x16, #0xf78]
    // 0xae7668: stp             x1, x16, [SP]
    // 0xae766c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae766c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae7670: r0 = all()
    //     0xae7670: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae7674: stur            x0, [fp, #-0x50]
    // 0xae7678: r0 = ButtonStyle()
    //     0xae7678: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xae767c: mov             x1, x0
    // 0xae7680: ldur            x0, [fp, #-8]
    // 0xae7684: stur            x1, [fp, #-0x58]
    // 0xae7688: StoreField: r1->field_b = r0
    //     0xae7688: stur            w0, [x1, #0xb]
    // 0xae768c: ldur            x0, [fp, #-0x20]
    // 0xae7690: StoreField: r1->field_23 = r0
    //     0xae7690: stur            w0, [x1, #0x23]
    // 0xae7694: ldur            x0, [fp, #-0x50]
    // 0xae7698: StoreField: r1->field_43 = r0
    //     0xae7698: stur            w0, [x1, #0x43]
    // 0xae769c: r0 = TextButtonThemeData()
    //     0xae769c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xae76a0: mov             x2, x0
    // 0xae76a4: ldur            x0, [fp, #-0x58]
    // 0xae76a8: stur            x2, [fp, #-8]
    // 0xae76ac: StoreField: r2->field_7 = r0
    //     0xae76ac: stur            w0, [x2, #7]
    // 0xae76b0: r16 = 16.000000
    //     0xae76b0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae76b4: ldr             x16, [x16, #0x188]
    // 0xae76b8: r30 = Instance_Color
    //     0xae76b8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xae76bc: stp             lr, x16, [SP]
    // 0xae76c0: ldur            x1, [fp, #-0x10]
    // 0xae76c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae76c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae76c8: ldr             x4, [x4, #0xaa0]
    // 0xae76cc: r0 = copyWith()
    //     0xae76cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae76d0: stur            x0, [fp, #-0x10]
    // 0xae76d4: r0 = Text()
    //     0xae76d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae76d8: mov             x3, x0
    // 0xae76dc: r0 = "Yes, Continue"
    //     0xae76dc: add             x0, PP, #0x56, lsl #12  ; [pp+0x563a8] "Yes, Continue"
    //     0xae76e0: ldr             x0, [x0, #0x3a8]
    // 0xae76e4: stur            x3, [fp, #-0x20]
    // 0xae76e8: StoreField: r3->field_b = r0
    //     0xae76e8: stur            w0, [x3, #0xb]
    // 0xae76ec: ldur            x0, [fp, #-0x10]
    // 0xae76f0: StoreField: r3->field_13 = r0
    //     0xae76f0: stur            w0, [x3, #0x13]
    // 0xae76f4: ldur            x2, [fp, #-0x18]
    // 0xae76f8: r1 = Function '<anonymous closure>':.
    //     0xae76f8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58808] AnonymousClosure: (0xae7938), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae76fc: ldr             x1, [x1, #0x808]
    // 0xae7700: r0 = AllocateClosure()
    //     0xae7700: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae7704: stur            x0, [fp, #-0x10]
    // 0xae7708: r0 = TextButton()
    //     0xae7708: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae770c: mov             x1, x0
    // 0xae7710: ldur            x0, [fp, #-0x10]
    // 0xae7714: stur            x1, [fp, #-0x18]
    // 0xae7718: StoreField: r1->field_b = r0
    //     0xae7718: stur            w0, [x1, #0xb]
    // 0xae771c: r0 = false
    //     0xae771c: add             x0, NULL, #0x30  ; false
    // 0xae7720: StoreField: r1->field_27 = r0
    //     0xae7720: stur            w0, [x1, #0x27]
    // 0xae7724: r0 = true
    //     0xae7724: add             x0, NULL, #0x20  ; true
    // 0xae7728: StoreField: r1->field_2f = r0
    //     0xae7728: stur            w0, [x1, #0x2f]
    // 0xae772c: ldur            x0, [fp, #-0x20]
    // 0xae7730: StoreField: r1->field_37 = r0
    //     0xae7730: stur            w0, [x1, #0x37]
    // 0xae7734: r0 = TextButtonTheme()
    //     0xae7734: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xae7738: mov             x2, x0
    // 0xae773c: ldur            x0, [fp, #-8]
    // 0xae7740: stur            x2, [fp, #-0x10]
    // 0xae7744: StoreField: r2->field_f = r0
    //     0xae7744: stur            w0, [x2, #0xf]
    // 0xae7748: ldur            x0, [fp, #-0x18]
    // 0xae774c: StoreField: r2->field_b = r0
    //     0xae774c: stur            w0, [x2, #0xb]
    // 0xae7750: r1 = <FlexParentData>
    //     0xae7750: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xae7754: ldr             x1, [x1, #0xe00]
    // 0xae7758: r0 = Expanded()
    //     0xae7758: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae775c: mov             x3, x0
    // 0xae7760: r0 = 1
    //     0xae7760: movz            x0, #0x1
    // 0xae7764: stur            x3, [fp, #-8]
    // 0xae7768: StoreField: r3->field_13 = r0
    //     0xae7768: stur            x0, [x3, #0x13]
    // 0xae776c: r0 = Instance_FlexFit
    //     0xae776c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xae7770: ldr             x0, [x0, #0xe08]
    // 0xae7774: StoreField: r3->field_1b = r0
    //     0xae7774: stur            w0, [x3, #0x1b]
    // 0xae7778: ldur            x0, [fp, #-0x10]
    // 0xae777c: StoreField: r3->field_b = r0
    //     0xae777c: stur            w0, [x3, #0xb]
    // 0xae7780: r1 = Null
    //     0xae7780: mov             x1, NULL
    // 0xae7784: r2 = 6
    //     0xae7784: movz            x2, #0x6
    // 0xae7788: r0 = AllocateArray()
    //     0xae7788: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae778c: mov             x2, x0
    // 0xae7790: ldur            x0, [fp, #-0x30]
    // 0xae7794: stur            x2, [fp, #-0x10]
    // 0xae7798: StoreField: r2->field_f = r0
    //     0xae7798: stur            w0, [x2, #0xf]
    // 0xae779c: r16 = Instance_SizedBox
    //     0xae779c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xae77a0: ldr             x16, [x16, #0x998]
    // 0xae77a4: StoreField: r2->field_13 = r16
    //     0xae77a4: stur            w16, [x2, #0x13]
    // 0xae77a8: ldur            x0, [fp, #-8]
    // 0xae77ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xae77ac: stur            w0, [x2, #0x17]
    // 0xae77b0: r1 = <Widget>
    //     0xae77b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae77b4: r0 = AllocateGrowableArray()
    //     0xae77b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae77b8: mov             x1, x0
    // 0xae77bc: ldur            x0, [fp, #-0x10]
    // 0xae77c0: stur            x1, [fp, #-8]
    // 0xae77c4: StoreField: r1->field_f = r0
    //     0xae77c4: stur            w0, [x1, #0xf]
    // 0xae77c8: r2 = 6
    //     0xae77c8: movz            x2, #0x6
    // 0xae77cc: StoreField: r1->field_b = r2
    //     0xae77cc: stur            w2, [x1, #0xb]
    // 0xae77d0: r0 = Row()
    //     0xae77d0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae77d4: mov             x3, x0
    // 0xae77d8: r0 = Instance_Axis
    //     0xae77d8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xae77dc: stur            x3, [fp, #-0x10]
    // 0xae77e0: StoreField: r3->field_f = r0
    //     0xae77e0: stur            w0, [x3, #0xf]
    // 0xae77e4: r0 = Instance_MainAxisAlignment
    //     0xae77e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xae77e8: ldr             x0, [x0, #0xd10]
    // 0xae77ec: StoreField: r3->field_13 = r0
    //     0xae77ec: stur            w0, [x3, #0x13]
    // 0xae77f0: r0 = Instance_MainAxisSize
    //     0xae77f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae77f4: ldr             x0, [x0, #0xa10]
    // 0xae77f8: ArrayStore: r3[0] = r0  ; List_4
    //     0xae77f8: stur            w0, [x3, #0x17]
    // 0xae77fc: r0 = Instance_CrossAxisAlignment
    //     0xae77fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae7800: ldr             x0, [x0, #0xa18]
    // 0xae7804: StoreField: r3->field_1b = r0
    //     0xae7804: stur            w0, [x3, #0x1b]
    // 0xae7808: r0 = Instance_VerticalDirection
    //     0xae7808: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae780c: ldr             x0, [x0, #0xa20]
    // 0xae7810: StoreField: r3->field_23 = r0
    //     0xae7810: stur            w0, [x3, #0x23]
    // 0xae7814: r4 = Instance_Clip
    //     0xae7814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae7818: ldr             x4, [x4, #0x38]
    // 0xae781c: StoreField: r3->field_2b = r4
    //     0xae781c: stur            w4, [x3, #0x2b]
    // 0xae7820: StoreField: r3->field_2f = rZR
    //     0xae7820: stur            xzr, [x3, #0x2f]
    // 0xae7824: ldur            x1, [fp, #-8]
    // 0xae7828: StoreField: r3->field_b = r1
    //     0xae7828: stur            w1, [x3, #0xb]
    // 0xae782c: r1 = Null
    //     0xae782c: mov             x1, NULL
    // 0xae7830: r2 = 6
    //     0xae7830: movz            x2, #0x6
    // 0xae7834: r0 = AllocateArray()
    //     0xae7834: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae7838: mov             x2, x0
    // 0xae783c: ldur            x0, [fp, #-0x28]
    // 0xae7840: stur            x2, [fp, #-8]
    // 0xae7844: StoreField: r2->field_f = r0
    //     0xae7844: stur            w0, [x2, #0xf]
    // 0xae7848: r16 = Instance_SizedBox
    //     0xae7848: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xae784c: ldr             x16, [x16, #0x9f0]
    // 0xae7850: StoreField: r2->field_13 = r16
    //     0xae7850: stur            w16, [x2, #0x13]
    // 0xae7854: ldur            x0, [fp, #-0x10]
    // 0xae7858: ArrayStore: r2[0] = r0  ; List_4
    //     0xae7858: stur            w0, [x2, #0x17]
    // 0xae785c: r1 = <Widget>
    //     0xae785c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae7860: r0 = AllocateGrowableArray()
    //     0xae7860: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae7864: mov             x1, x0
    // 0xae7868: ldur            x0, [fp, #-8]
    // 0xae786c: stur            x1, [fp, #-0x10]
    // 0xae7870: StoreField: r1->field_f = r0
    //     0xae7870: stur            w0, [x1, #0xf]
    // 0xae7874: r0 = 6
    //     0xae7874: movz            x0, #0x6
    // 0xae7878: StoreField: r1->field_b = r0
    //     0xae7878: stur            w0, [x1, #0xb]
    // 0xae787c: r0 = Column()
    //     0xae787c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae7880: mov             x1, x0
    // 0xae7884: r0 = Instance_Axis
    //     0xae7884: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae7888: stur            x1, [fp, #-8]
    // 0xae788c: StoreField: r1->field_f = r0
    //     0xae788c: stur            w0, [x1, #0xf]
    // 0xae7890: r0 = Instance_MainAxisAlignment
    //     0xae7890: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae7894: ldr             x0, [x0, #0xa08]
    // 0xae7898: StoreField: r1->field_13 = r0
    //     0xae7898: stur            w0, [x1, #0x13]
    // 0xae789c: r0 = Instance_MainAxisSize
    //     0xae789c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xae78a0: ldr             x0, [x0, #0xdd0]
    // 0xae78a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xae78a4: stur            w0, [x1, #0x17]
    // 0xae78a8: r0 = Instance_CrossAxisAlignment
    //     0xae78a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae78ac: ldr             x0, [x0, #0x890]
    // 0xae78b0: StoreField: r1->field_1b = r0
    //     0xae78b0: stur            w0, [x1, #0x1b]
    // 0xae78b4: r0 = Instance_VerticalDirection
    //     0xae78b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae78b8: ldr             x0, [x0, #0xa20]
    // 0xae78bc: StoreField: r1->field_23 = r0
    //     0xae78bc: stur            w0, [x1, #0x23]
    // 0xae78c0: r0 = Instance_Clip
    //     0xae78c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae78c4: ldr             x0, [x0, #0x38]
    // 0xae78c8: StoreField: r1->field_2b = r0
    //     0xae78c8: stur            w0, [x1, #0x2b]
    // 0xae78cc: StoreField: r1->field_2f = rZR
    //     0xae78cc: stur            xzr, [x1, #0x2f]
    // 0xae78d0: ldur            x0, [fp, #-0x10]
    // 0xae78d4: StoreField: r1->field_b = r0
    //     0xae78d4: stur            w0, [x1, #0xb]
    // 0xae78d8: r0 = Padding()
    //     0xae78d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae78dc: r1 = Instance_EdgeInsets
    //     0xae78dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xae78e0: ldr             x1, [x1, #0x1f0]
    // 0xae78e4: StoreField: r0->field_f = r1
    //     0xae78e4: stur            w1, [x0, #0xf]
    // 0xae78e8: ldur            x1, [fp, #-8]
    // 0xae78ec: StoreField: r0->field_b = r1
    //     0xae78ec: stur            w1, [x0, #0xb]
    // 0xae78f0: LeaveFrame
    //     0xae78f0: mov             SP, fp
    //     0xae78f4: ldp             fp, lr, [SP], #0x10
    // 0xae78f8: ret
    //     0xae78f8: ret             
    // 0xae78fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae78fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7900: b               #0xae6d2c
    // 0xae7904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae7904: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae7908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae7908: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae790c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae790c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae7910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae7910: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae7938, size: 0x314
    // 0xae7938: EnterFrame
    //     0xae7938: stp             fp, lr, [SP, #-0x10]!
    //     0xae793c: mov             fp, SP
    // 0xae7940: AllocStack(0x28)
    //     0xae7940: sub             SP, SP, #0x28
    // 0xae7944: SetupParameters()
    //     0xae7944: ldr             x0, [fp, #0x10]
    //     0xae7948: ldur            w2, [x0, #0x17]
    //     0xae794c: add             x2, x2, HEAP, lsl #32
    //     0xae7950: stur            x2, [fp, #-8]
    // 0xae7954: CheckStackOverflow
    //     0xae7954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7958: cmp             SP, x16
    //     0xae795c: b.ls            #0xae7c40
    // 0xae7960: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae7960: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae7964: ldr             x0, [x0, #0x1c80]
    //     0xae7968: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae796c: cmp             w0, w16
    //     0xae7970: b.ne            #0xae797c
    //     0xae7974: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae7978: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae797c: str             NULL, [SP]
    // 0xae7980: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7980: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7984: r0 = GetNavigation.back()
    //     0xae7984: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xae7988: r1 = Null
    //     0xae7988: mov             x1, NULL
    // 0xae798c: r2 = 44
    //     0xae798c: movz            x2, #0x2c
    // 0xae7990: r0 = AllocateArray()
    //     0xae7990: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae7994: mov             x2, x0
    // 0xae7998: r16 = "customizedResponse"
    //     0xae7998: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] "customizedResponse"
    //     0xae799c: ldr             x16, [x16, #0x38]
    // 0xae79a0: StoreField: r2->field_f = r16
    //     0xae79a0: stur            w16, [x2, #0xf]
    // 0xae79a4: ldur            x3, [fp, #-8]
    // 0xae79a8: LoadField: r0 = r3->field_f
    //     0xae79a8: ldur            w0, [x3, #0xf]
    // 0xae79ac: DecompressPointer r0
    //     0xae79ac: add             x0, x0, HEAP, lsl #32
    // 0xae79b0: LoadField: r4 = r0->field_b
    //     0xae79b0: ldur            w4, [x0, #0xb]
    // 0xae79b4: DecompressPointer r4
    //     0xae79b4: add             x4, x4, HEAP, lsl #32
    // 0xae79b8: cmp             w4, NULL
    // 0xae79bc: b.eq            #0xae7c48
    // 0xae79c0: LoadField: r0 = r4->field_b
    //     0xae79c0: ldur            w0, [x4, #0xb]
    // 0xae79c4: DecompressPointer r0
    //     0xae79c4: add             x0, x0, HEAP, lsl #32
    // 0xae79c8: StoreField: r2->field_13 = r0
    //     0xae79c8: stur            w0, [x2, #0x13]
    // 0xae79cc: r16 = "productId"
    //     0xae79cc: ldr             x16, [PP, #0x3970]  ; [pp+0x3970] "productId"
    // 0xae79d0: ArrayStore: r2[0] = r16  ; List_4
    //     0xae79d0: stur            w16, [x2, #0x17]
    // 0xae79d4: LoadField: r0 = r4->field_f
    //     0xae79d4: ldur            w0, [x4, #0xf]
    // 0xae79d8: DecompressPointer r0
    //     0xae79d8: add             x0, x0, HEAP, lsl #32
    // 0xae79dc: StoreField: r2->field_1b = r0
    //     0xae79dc: stur            w0, [x2, #0x1b]
    // 0xae79e0: r16 = "skuId"
    //     0xae79e0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] "skuId"
    //     0xae79e4: ldr             x16, [x16, #0x40]
    // 0xae79e8: StoreField: r2->field_1f = r16
    //     0xae79e8: stur            w16, [x2, #0x1f]
    // 0xae79ec: LoadField: r0 = r4->field_13
    //     0xae79ec: ldur            w0, [x4, #0x13]
    // 0xae79f0: DecompressPointer r0
    //     0xae79f0: add             x0, x0, HEAP, lsl #32
    // 0xae79f4: StoreField: r2->field_23 = r0
    //     0xae79f4: stur            w0, [x2, #0x23]
    // 0xae79f8: r16 = "customisedId"
    //     0xae79f8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30048] "customisedId"
    //     0xae79fc: ldr             x16, [x16, #0x48]
    // 0xae7a00: StoreField: r2->field_27 = r16
    //     0xae7a00: stur            w16, [x2, #0x27]
    // 0xae7a04: r16 = ""
    //     0xae7a04: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae7a08: StoreField: r2->field_2b = r16
    //     0xae7a08: stur            w16, [x2, #0x2b]
    // 0xae7a0c: r16 = "addTypeValue"
    //     0xae7a0c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30050] "addTypeValue"
    //     0xae7a10: ldr             x16, [x16, #0x50]
    // 0xae7a14: StoreField: r2->field_2f = r16
    //     0xae7a14: stur            w16, [x2, #0x2f]
    // 0xae7a18: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xae7a18: ldur            x5, [x4, #0x17]
    // 0xae7a1c: r0 = BoxInt64Instr(r5)
    //     0xae7a1c: sbfiz           x0, x5, #1, #0x1f
    //     0xae7a20: cmp             x5, x0, asr #1
    //     0xae7a24: b.eq            #0xae7a30
    //     0xae7a28: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae7a2c: stur            x5, [x0, #7]
    // 0xae7a30: mov             x1, x2
    // 0xae7a34: ArrayStore: r1[9] = r0  ; List_4
    //     0xae7a34: add             x25, x1, #0x33
    //     0xae7a38: str             w0, [x25]
    //     0xae7a3c: tbz             w0, #0, #0xae7a58
    //     0xae7a40: ldurb           w16, [x1, #-1]
    //     0xae7a44: ldurb           w17, [x0, #-1]
    //     0xae7a48: and             x16, x17, x16, lsr #2
    //     0xae7a4c: tst             x16, HEAP, lsr #32
    //     0xae7a50: b.eq            #0xae7a58
    //     0xae7a54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7a58: r16 = "sellingPrice"
    //     0xae7a58: add             x16, PP, #0x30, lsl #12  ; [pp+0x30058] "sellingPrice"
    //     0xae7a5c: ldr             x16, [x16, #0x58]
    // 0xae7a60: StoreField: r2->field_37 = r16
    //     0xae7a60: stur            w16, [x2, #0x37]
    // 0xae7a64: LoadField: r5 = r4->field_27
    //     0xae7a64: ldur            x5, [x4, #0x27]
    // 0xae7a68: r0 = BoxInt64Instr(r5)
    //     0xae7a68: sbfiz           x0, x5, #1, #0x1f
    //     0xae7a6c: cmp             x5, x0, asr #1
    //     0xae7a70: b.eq            #0xae7a7c
    //     0xae7a74: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae7a78: stur            x5, [x0, #7]
    // 0xae7a7c: mov             x1, x2
    // 0xae7a80: ArrayStore: r1[11] = r0  ; List_4
    //     0xae7a80: add             x25, x1, #0x3b
    //     0xae7a84: str             w0, [x25]
    //     0xae7a88: tbz             w0, #0, #0xae7aa4
    //     0xae7a8c: ldurb           w16, [x1, #-1]
    //     0xae7a90: ldurb           w17, [x0, #-1]
    //     0xae7a94: and             x16, x17, x16, lsr #2
    //     0xae7a98: tst             x16, HEAP, lsr #32
    //     0xae7a9c: b.eq            #0xae7aa4
    //     0xae7aa0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7aa4: r16 = "comingFrom"
    //     0xae7aa4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30060] "comingFrom"
    //     0xae7aa8: ldr             x16, [x16, #0x60]
    // 0xae7aac: StoreField: r2->field_3f = r16
    //     0xae7aac: stur            w16, [x2, #0x3f]
    // 0xae7ab0: LoadField: r0 = r4->field_2f
    //     0xae7ab0: ldur            w0, [x4, #0x2f]
    // 0xae7ab4: DecompressPointer r0
    //     0xae7ab4: add             x0, x0, HEAP, lsl #32
    // 0xae7ab8: mov             x1, x2
    // 0xae7abc: ArrayStore: r1[13] = r0  ; List_4
    //     0xae7abc: add             x25, x1, #0x43
    //     0xae7ac0: str             w0, [x25]
    //     0xae7ac4: tbz             w0, #0, #0xae7ae0
    //     0xae7ac8: ldurb           w16, [x1, #-1]
    //     0xae7acc: ldurb           w17, [x0, #-1]
    //     0xae7ad0: and             x16, x17, x16, lsr #2
    //     0xae7ad4: tst             x16, HEAP, lsr #32
    //     0xae7ad8: b.eq            #0xae7ae0
    //     0xae7adc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7ae0: r16 = "skuData"
    //     0xae7ae0: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] "skuData"
    //     0xae7ae4: ldr             x16, [x16, #0x68]
    // 0xae7ae8: StoreField: r2->field_47 = r16
    //     0xae7ae8: stur            w16, [x2, #0x47]
    // 0xae7aec: LoadField: r0 = r4->field_37
    //     0xae7aec: ldur            w0, [x4, #0x37]
    // 0xae7af0: DecompressPointer r0
    //     0xae7af0: add             x0, x0, HEAP, lsl #32
    // 0xae7af4: mov             x1, x2
    // 0xae7af8: ArrayStore: r1[15] = r0  ; List_4
    //     0xae7af8: add             x25, x1, #0x4b
    //     0xae7afc: str             w0, [x25]
    //     0xae7b00: tbz             w0, #0, #0xae7b1c
    //     0xae7b04: ldurb           w16, [x1, #-1]
    //     0xae7b08: ldurb           w17, [x0, #-1]
    //     0xae7b0c: and             x16, x17, x16, lsr #2
    //     0xae7b10: tst             x16, HEAP, lsr #32
    //     0xae7b14: b.eq            #0xae7b1c
    //     0xae7b18: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7b1c: r16 = "productTitle"
    //     0xae7b1c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] "productTitle"
    //     0xae7b20: ldr             x16, [x16, #0x70]
    // 0xae7b24: StoreField: r2->field_4f = r16
    //     0xae7b24: stur            w16, [x2, #0x4f]
    // 0xae7b28: LoadField: r0 = r4->field_3b
    //     0xae7b28: ldur            w0, [x4, #0x3b]
    // 0xae7b2c: DecompressPointer r0
    //     0xae7b2c: add             x0, x0, HEAP, lsl #32
    // 0xae7b30: mov             x1, x2
    // 0xae7b34: ArrayStore: r1[17] = r0  ; List_4
    //     0xae7b34: add             x25, x1, #0x53
    //     0xae7b38: str             w0, [x25]
    //     0xae7b3c: tbz             w0, #0, #0xae7b58
    //     0xae7b40: ldurb           w16, [x1, #-1]
    //     0xae7b44: ldurb           w17, [x0, #-1]
    //     0xae7b48: and             x16, x17, x16, lsr #2
    //     0xae7b4c: tst             x16, HEAP, lsr #32
    //     0xae7b50: b.eq            #0xae7b58
    //     0xae7b54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7b58: r16 = "productImageUrl"
    //     0xae7b58: add             x16, PP, #0x30, lsl #12  ; [pp+0x30078] "productImageUrl"
    //     0xae7b5c: ldr             x16, [x16, #0x78]
    // 0xae7b60: StoreField: r2->field_57 = r16
    //     0xae7b60: stur            w16, [x2, #0x57]
    // 0xae7b64: LoadField: r0 = r4->field_3f
    //     0xae7b64: ldur            w0, [x4, #0x3f]
    // 0xae7b68: DecompressPointer r0
    //     0xae7b68: add             x0, x0, HEAP, lsl #32
    // 0xae7b6c: mov             x1, x2
    // 0xae7b70: ArrayStore: r1[19] = r0  ; List_4
    //     0xae7b70: add             x25, x1, #0x5b
    //     0xae7b74: str             w0, [x25]
    //     0xae7b78: tbz             w0, #0, #0xae7b94
    //     0xae7b7c: ldurb           w16, [x1, #-1]
    //     0xae7b80: ldurb           w17, [x0, #-1]
    //     0xae7b84: and             x16, x17, x16, lsr #2
    //     0xae7b88: tst             x16, HEAP, lsr #32
    //     0xae7b8c: b.eq            #0xae7b94
    //     0xae7b90: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7b94: r16 = "checkout_variant_response"
    //     0xae7b94: add             x16, PP, #0x30, lsl #12  ; [pp+0x30080] "checkout_variant_response"
    //     0xae7b98: ldr             x16, [x16, #0x80]
    // 0xae7b9c: StoreField: r2->field_5f = r16
    //     0xae7b9c: stur            w16, [x2, #0x5f]
    // 0xae7ba0: LoadField: r0 = r4->field_4b
    //     0xae7ba0: ldur            w0, [x4, #0x4b]
    // 0xae7ba4: DecompressPointer r0
    //     0xae7ba4: add             x0, x0, HEAP, lsl #32
    // 0xae7ba8: mov             x1, x2
    // 0xae7bac: ArrayStore: r1[21] = r0  ; List_4
    //     0xae7bac: add             x25, x1, #0x63
    //     0xae7bb0: str             w0, [x25]
    //     0xae7bb4: tbz             w0, #0, #0xae7bd0
    //     0xae7bb8: ldurb           w16, [x1, #-1]
    //     0xae7bbc: ldurb           w17, [x0, #-1]
    //     0xae7bc0: and             x16, x17, x16, lsr #2
    //     0xae7bc4: tst             x16, HEAP, lsr #32
    //     0xae7bc8: b.eq            #0xae7bd0
    //     0xae7bcc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae7bd0: r16 = <String, Object?>
    //     0xae7bd0: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xae7bd4: ldr             x16, [x16, #0xc28]
    // 0xae7bd8: stp             x2, x16, [SP]
    // 0xae7bdc: r0 = Map._fromLiteral()
    //     0xae7bdc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae7be0: r16 = "/customization"
    //     0xae7be0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0xae7be4: ldr             x16, [x16, #0x8a8]
    // 0xae7be8: stp             x16, NULL, [SP, #8]
    // 0xae7bec: str             x0, [SP]
    // 0xae7bf0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae7bf0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae7bf4: ldr             x4, [x4, #0x438]
    // 0xae7bf8: r0 = GetNavigation.toNamed()
    //     0xae7bf8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae7bfc: stur            x0, [fp, #-0x10]
    // 0xae7c00: cmp             w0, NULL
    // 0xae7c04: b.eq            #0xae7c30
    // 0xae7c08: ldur            x2, [fp, #-8]
    // 0xae7c0c: r1 = Function '<anonymous closure>':.
    //     0xae7c0c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58810] AnonymousClosure: (0xa47358), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xae7c10: ldr             x1, [x1, #0x810]
    // 0xae7c14: r0 = AllocateClosure()
    //     0xae7c14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae7c18: r16 = <Null?>
    //     0xae7c18: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xae7c1c: ldur            lr, [fp, #-0x10]
    // 0xae7c20: stp             lr, x16, [SP, #8]
    // 0xae7c24: str             x0, [SP]
    // 0xae7c28: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae7c28: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae7c2c: r0 = then()
    //     0xae7c2c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xae7c30: r0 = Null
    //     0xae7c30: mov             x0, NULL
    // 0xae7c34: LeaveFrame
    //     0xae7c34: mov             SP, fp
    //     0xae7c38: ldp             fp, lr, [SP], #0x10
    // 0xae7c3c: ret
    //     0xae7c3c: ret             
    // 0xae7c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae7c40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae7c44: b               #0xae7960
    // 0xae7c48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae7c48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae7c4c, size: 0x760
    // 0xae7c4c: EnterFrame
    //     0xae7c4c: stp             fp, lr, [SP, #-0x10]!
    //     0xae7c50: mov             fp, SP
    // 0xae7c54: AllocStack(0x80)
    //     0xae7c54: sub             SP, SP, #0x80
    // 0xae7c58: SetupParameters()
    //     0xae7c58: ldr             x0, [fp, #0x10]
    //     0xae7c5c: ldur            w1, [x0, #0x17]
    //     0xae7c60: add             x1, x1, HEAP, lsl #32
    //     0xae7c64: stur            x1, [fp, #-8]
    // 0xae7c68: CheckStackOverflow
    //     0xae7c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae7c6c: cmp             SP, x16
    //     0xae7c70: b.ls            #0xae8380
    // 0xae7c74: r1 = 1
    //     0xae7c74: movz            x1, #0x1
    // 0xae7c78: r0 = AllocateContext()
    //     0xae7c78: bl              #0x16f6108  ; AllocateContextStub
    // 0xae7c7c: mov             x1, x0
    // 0xae7c80: ldur            x0, [fp, #-8]
    // 0xae7c84: stur            x1, [fp, #-0x10]
    // 0xae7c88: StoreField: r1->field_b = r0
    //     0xae7c88: stur            w0, [x1, #0xb]
    // 0xae7c8c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae7c8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae7c90: ldr             x0, [x0, #0x1c80]
    //     0xae7c94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae7c98: cmp             w0, w16
    //     0xae7c9c: b.ne            #0xae7ca8
    //     0xae7ca0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae7ca4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae7ca8: str             NULL, [SP]
    // 0xae7cac: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7cac: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7cb0: r0 = GetNavigation.back()
    //     0xae7cb0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xae7cb4: ldur            x2, [fp, #-0x10]
    // 0xae7cb8: r0 = Sentinel
    //     0xae7cb8: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae7cbc: StoreField: r2->field_f = r0
    //     0xae7cbc: stur            w0, [x2, #0xf]
    // 0xae7cc0: ldur            x0, [fp, #-8]
    // 0xae7cc4: LoadField: r1 = r0->field_f
    //     0xae7cc4: ldur            w1, [x0, #0xf]
    // 0xae7cc8: DecompressPointer r1
    //     0xae7cc8: add             x1, x1, HEAP, lsl #32
    // 0xae7ccc: LoadField: r3 = r1->field_b
    //     0xae7ccc: ldur            w3, [x1, #0xb]
    // 0xae7cd0: DecompressPointer r3
    //     0xae7cd0: add             x3, x3, HEAP, lsl #32
    // 0xae7cd4: cmp             w3, NULL
    // 0xae7cd8: b.eq            #0xae8388
    // 0xae7cdc: LoadField: r1 = r3->field_23
    //     0xae7cdc: ldur            w1, [x3, #0x23]
    // 0xae7ce0: DecompressPointer r1
    //     0xae7ce0: add             x1, x1, HEAP, lsl #32
    // 0xae7ce4: r16 = "home_page"
    //     0xae7ce4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xae7ce8: ldr             x16, [x16, #0xe60]
    // 0xae7cec: stp             x16, x1, [SP]
    // 0xae7cf0: r0 = ==()
    //     0xae7cf0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xae7cf4: tbnz            w0, #4, #0xae7d40
    // 0xae7cf8: ldur            x2, [fp, #-0x10]
    // 0xae7cfc: r16 = <HomeController>
    //     0xae7cfc: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xae7d00: ldr             x16, [x16, #0xf98]
    // 0xae7d04: str             x16, [SP]
    // 0xae7d08: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7d08: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7d0c: r0 = Inst.find()
    //     0xae7d0c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae7d10: mov             x1, x0
    // 0xae7d14: ldur            x2, [fp, #-0x10]
    // 0xae7d18: StoreField: r2->field_f = r0
    //     0xae7d18: stur            w0, [x2, #0xf]
    //     0xae7d1c: ldurb           w16, [x2, #-1]
    //     0xae7d20: ldurb           w17, [x0, #-1]
    //     0xae7d24: and             x16, x17, x16, lsr #2
    //     0xae7d28: tst             x16, HEAP, lsr #32
    //     0xae7d2c: b.eq            #0xae7d34
    //     0xae7d30: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xae7d34: r3 = "product_card"
    //     0xae7d34: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xae7d38: ldr             x3, [x3, #0xc28]
    // 0xae7d3c: b               #0xae7ea0
    // 0xae7d40: ldur            x0, [fp, #-8]
    // 0xae7d44: ldur            x2, [fp, #-0x10]
    // 0xae7d48: LoadField: r1 = r0->field_f
    //     0xae7d48: ldur            w1, [x0, #0xf]
    // 0xae7d4c: DecompressPointer r1
    //     0xae7d4c: add             x1, x1, HEAP, lsl #32
    // 0xae7d50: LoadField: r3 = r1->field_b
    //     0xae7d50: ldur            w3, [x1, #0xb]
    // 0xae7d54: DecompressPointer r3
    //     0xae7d54: add             x3, x3, HEAP, lsl #32
    // 0xae7d58: cmp             w3, NULL
    // 0xae7d5c: b.eq            #0xae838c
    // 0xae7d60: LoadField: r1 = r3->field_23
    //     0xae7d60: ldur            w1, [x3, #0x23]
    // 0xae7d64: DecompressPointer r1
    //     0xae7d64: add             x1, x1, HEAP, lsl #32
    // 0xae7d68: r16 = "collection_page"
    //     0xae7d68: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xae7d6c: ldr             x16, [x16, #0x118]
    // 0xae7d70: stp             x16, x1, [SP]
    // 0xae7d74: r0 = ==()
    //     0xae7d74: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xae7d78: tbnz            w0, #4, #0xae7dc8
    // 0xae7d7c: ldur            x2, [fp, #-0x10]
    // 0xae7d80: r16 = <CollectionsController>
    //     0xae7d80: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xae7d84: ldr             x16, [x16, #0xb00]
    // 0xae7d88: str             x16, [SP]
    // 0xae7d8c: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7d8c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7d90: r0 = Inst.find()
    //     0xae7d90: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae7d94: mov             x1, x0
    // 0xae7d98: ldur            x2, [fp, #-0x10]
    // 0xae7d9c: StoreField: r2->field_f = r0
    //     0xae7d9c: stur            w0, [x2, #0xf]
    //     0xae7da0: ldurb           w16, [x2, #-1]
    //     0xae7da4: ldurb           w17, [x0, #-1]
    //     0xae7da8: and             x16, x17, x16, lsr #2
    //     0xae7dac: tst             x16, HEAP, lsr #32
    //     0xae7db0: b.eq            #0xae7db8
    //     0xae7db4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xae7db8: mov             x0, x1
    // 0xae7dbc: r1 = "product_card"
    //     0xae7dbc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xae7dc0: ldr             x1, [x1, #0xc28]
    // 0xae7dc4: b               #0xae7e98
    // 0xae7dc8: ldur            x0, [fp, #-8]
    // 0xae7dcc: ldur            x2, [fp, #-0x10]
    // 0xae7dd0: LoadField: r1 = r0->field_f
    //     0xae7dd0: ldur            w1, [x0, #0xf]
    // 0xae7dd4: DecompressPointer r1
    //     0xae7dd4: add             x1, x1, HEAP, lsl #32
    // 0xae7dd8: LoadField: r3 = r1->field_b
    //     0xae7dd8: ldur            w3, [x1, #0xb]
    // 0xae7ddc: DecompressPointer r3
    //     0xae7ddc: add             x3, x3, HEAP, lsl #32
    // 0xae7de0: cmp             w3, NULL
    // 0xae7de4: b.eq            #0xae8390
    // 0xae7de8: LoadField: r1 = r3->field_23
    //     0xae7de8: ldur            w1, [x3, #0x23]
    // 0xae7dec: DecompressPointer r1
    //     0xae7dec: add             x1, x1, HEAP, lsl #32
    // 0xae7df0: r16 = "product_page"
    //     0xae7df0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae7df4: ldr             x16, [x16, #0x480]
    // 0xae7df8: stp             x16, x1, [SP]
    // 0xae7dfc: r0 = ==()
    //     0xae7dfc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xae7e00: tbnz            w0, #4, #0xae7e50
    // 0xae7e04: ldur            x2, [fp, #-0x10]
    // 0xae7e08: r16 = <ProductDetailController>
    //     0xae7e08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xae7e0c: ldr             x16, [x16, #0xde0]
    // 0xae7e10: str             x16, [SP]
    // 0xae7e14: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7e14: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7e18: r0 = Inst.find()
    //     0xae7e18: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae7e1c: mov             x1, x0
    // 0xae7e20: ldur            x2, [fp, #-0x10]
    // 0xae7e24: StoreField: r2->field_f = r0
    //     0xae7e24: stur            w0, [x2, #0xf]
    //     0xae7e28: ldurb           w16, [x2, #-1]
    //     0xae7e2c: ldurb           w17, [x0, #-1]
    //     0xae7e30: and             x16, x17, x16, lsr #2
    //     0xae7e34: tst             x16, HEAP, lsr #32
    //     0xae7e38: b.eq            #0xae7e40
    //     0xae7e3c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xae7e40: mov             x0, x1
    // 0xae7e44: r1 = "product_page"
    //     0xae7e44: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae7e48: ldr             x1, [x1, #0x480]
    // 0xae7e4c: b               #0xae7e98
    // 0xae7e50: ldur            x2, [fp, #-0x10]
    // 0xae7e54: r16 = <HomeController>
    //     0xae7e54: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xae7e58: ldr             x16, [x16, #0xf98]
    // 0xae7e5c: str             x16, [SP]
    // 0xae7e60: r4 = const [0x1, 0, 0, 0, null]
    //     0xae7e60: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae7e64: r0 = Inst.find()
    //     0xae7e64: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae7e68: mov             x1, x0
    // 0xae7e6c: ldur            x2, [fp, #-0x10]
    // 0xae7e70: StoreField: r2->field_f = r0
    //     0xae7e70: stur            w0, [x2, #0xf]
    //     0xae7e74: ldurb           w16, [x2, #-1]
    //     0xae7e78: ldurb           w17, [x0, #-1]
    //     0xae7e7c: and             x16, x17, x16, lsr #2
    //     0xae7e80: tst             x16, HEAP, lsr #32
    //     0xae7e84: b.eq            #0xae7e8c
    //     0xae7e88: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xae7e8c: mov             x0, x1
    // 0xae7e90: r1 = "product_card"
    //     0xae7e90: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xae7e94: ldr             x1, [x1, #0xc28]
    // 0xae7e98: mov             x3, x1
    // 0xae7e9c: mov             x1, x0
    // 0xae7ea0: ldur            x0, [fp, #-8]
    // 0xae7ea4: stur            x3, [fp, #-0x18]
    // 0xae7ea8: str             x1, [SP]
    // 0xae7eac: r4 = 0
    //     0xae7eac: movz            x4, #0
    // 0xae7eb0: ldr             x0, [SP]
    // 0xae7eb4: r16 = UnlinkedCall_0x613b5c
    //     0xae7eb4: add             x16, PP, #0x58, lsl #12  ; [pp+0x58818] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae7eb8: add             x16, x16, #0x818
    // 0xae7ebc: ldp             x5, lr, [x16]
    // 0xae7ec0: blr             lr
    // 0xae7ec4: mov             x3, x0
    // 0xae7ec8: ldur            x2, [fp, #-8]
    // 0xae7ecc: stur            x3, [fp, #-0x28]
    // 0xae7ed0: LoadField: r0 = r2->field_f
    //     0xae7ed0: ldur            w0, [x2, #0xf]
    // 0xae7ed4: DecompressPointer r0
    //     0xae7ed4: add             x0, x0, HEAP, lsl #32
    // 0xae7ed8: LoadField: r1 = r0->field_b
    //     0xae7ed8: ldur            w1, [x0, #0xb]
    // 0xae7edc: DecompressPointer r1
    //     0xae7edc: add             x1, x1, HEAP, lsl #32
    // 0xae7ee0: cmp             w1, NULL
    // 0xae7ee4: b.eq            #0xae8394
    // 0xae7ee8: LoadField: r4 = r1->field_f
    //     0xae7ee8: ldur            w4, [x1, #0xf]
    // 0xae7eec: DecompressPointer r4
    //     0xae7eec: add             x4, x4, HEAP, lsl #32
    // 0xae7ef0: stur            x4, [fp, #-0x20]
    // 0xae7ef4: LoadField: r5 = r1->field_27
    //     0xae7ef4: ldur            x5, [x1, #0x27]
    // 0xae7ef8: r0 = BoxInt64Instr(r5)
    //     0xae7ef8: sbfiz           x0, x5, #1, #0x1f
    //     0xae7efc: cmp             x5, x0, asr #1
    //     0xae7f00: b.eq            #0xae7f0c
    //     0xae7f04: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae7f08: stur            x5, [x0, #7]
    // 0xae7f0c: stp             x0, NULL, [SP]
    // 0xae7f10: r0 = _Double.fromInteger()
    //     0xae7f10: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae7f14: mov             x3, x0
    // 0xae7f18: ldur            x0, [fp, #-0x20]
    // 0xae7f1c: r2 = Null
    //     0xae7f1c: mov             x2, NULL
    // 0xae7f20: r1 = Null
    //     0xae7f20: mov             x1, NULL
    // 0xae7f24: stur            x3, [fp, #-0x30]
    // 0xae7f28: r4 = LoadClassIdInstr(r0)
    //     0xae7f28: ldur            x4, [x0, #-1]
    //     0xae7f2c: ubfx            x4, x4, #0xc, #0x14
    // 0xae7f30: sub             x4, x4, #0x5e
    // 0xae7f34: cmp             x4, #1
    // 0xae7f38: b.ls            #0xae7f4c
    // 0xae7f3c: r8 = String
    //     0xae7f3c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xae7f40: r3 = Null
    //     0xae7f40: add             x3, PP, #0x58, lsl #12  ; [pp+0x58828] Null
    //     0xae7f44: ldr             x3, [x3, #0x828]
    // 0xae7f48: r0 = String()
    //     0xae7f48: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xae7f4c: ldur            x16, [fp, #-0x20]
    // 0xae7f50: stp             x16, NULL, [SP, #0x18]
    // 0xae7f54: r16 = "Product"
    //     0xae7f54: add             x16, PP, #0x56, lsl #12  ; [pp+0x563e0] "Product"
    //     0xae7f58: ldr             x16, [x16, #0x3e0]
    // 0xae7f5c: r30 = "INR"
    //     0xae7f5c: add             lr, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0xae7f60: ldr             lr, [lr, #0x4c0]
    // 0xae7f64: stp             lr, x16, [SP, #8]
    // 0xae7f68: ldur            x16, [fp, #-0x30]
    // 0xae7f6c: str             x16, [SP]
    // 0xae7f70: ldur            x1, [fp, #-0x28]
    // 0xae7f74: r4 = const [0, 0x6, 0x5, 0x1, content, 0x1, currency, 0x4, id, 0x2, price, 0x5, type, 0x3, null]
    //     0xae7f74: add             x4, PP, #0x56, lsl #12  ; [pp+0x563e8] List(15) [0, 0x6, 0x5, 0x1, "content", 0x1, "currency", 0x4, "id", 0x2, "price", 0x5, "type", 0x3, Null]
    //     0xae7f78: ldr             x4, [x4, #0x3e8]
    // 0xae7f7c: r0 = logAddToCart()
    //     0xae7f7c: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0xae7f80: ldur            x1, [fp, #-8]
    // 0xae7f84: LoadField: r0 = r1->field_f
    //     0xae7f84: ldur            w0, [x1, #0xf]
    // 0xae7f88: DecompressPointer r0
    //     0xae7f88: add             x0, x0, HEAP, lsl #32
    // 0xae7f8c: LoadField: r2 = r0->field_b
    //     0xae7f8c: ldur            w2, [x0, #0xb]
    // 0xae7f90: DecompressPointer r2
    //     0xae7f90: add             x2, x2, HEAP, lsl #32
    // 0xae7f94: cmp             w2, NULL
    // 0xae7f98: b.eq            #0xae8398
    // 0xae7f9c: LoadField: r0 = r2->field_2f
    //     0xae7f9c: ldur            w0, [x2, #0x2f]
    // 0xae7fa0: DecompressPointer r0
    //     0xae7fa0: add             x0, x0, HEAP, lsl #32
    // 0xae7fa4: r2 = LoadClassIdInstr(r0)
    //     0xae7fa4: ldur            x2, [x0, #-1]
    //     0xae7fa8: ubfx            x2, x2, #0xc, #0x14
    // 0xae7fac: r16 = "add_to_bag"
    //     0xae7fac: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xae7fb0: ldr             x16, [x16, #0xa38]
    // 0xae7fb4: stp             x16, x0, [SP]
    // 0xae7fb8: mov             x0, x2
    // 0xae7fbc: mov             lr, x0
    // 0xae7fc0: ldr             lr, [x21, lr, lsl #3]
    // 0xae7fc4: blr             lr
    // 0xae7fc8: tbnz            w0, #4, #0xae8154
    // 0xae7fcc: ldur            x2, [fp, #-0x10]
    // 0xae7fd0: LoadField: r3 = r2->field_f
    //     0xae7fd0: ldur            w3, [x2, #0xf]
    // 0xae7fd4: DecompressPointer r3
    //     0xae7fd4: add             x3, x3, HEAP, lsl #32
    // 0xae7fd8: stur            x3, [fp, #-0x38]
    // 0xae7fdc: r16 = Sentinel
    //     0xae7fdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae7fe0: cmp             w3, w16
    // 0xae7fe4: b.eq            #0xae8350
    // 0xae7fe8: ldur            x4, [fp, #-8]
    // 0xae7fec: ldur            x5, [fp, #-0x18]
    // 0xae7ff0: LoadField: r0 = r4->field_f
    //     0xae7ff0: ldur            w0, [x4, #0xf]
    // 0xae7ff4: DecompressPointer r0
    //     0xae7ff4: add             x0, x0, HEAP, lsl #32
    // 0xae7ff8: LoadField: r1 = r0->field_b
    //     0xae7ff8: ldur            w1, [x0, #0xb]
    // 0xae7ffc: DecompressPointer r1
    //     0xae7ffc: add             x1, x1, HEAP, lsl #32
    // 0xae8000: cmp             w1, NULL
    // 0xae8004: b.eq            #0xae839c
    // 0xae8008: LoadField: r6 = r1->field_23
    //     0xae8008: ldur            w6, [x1, #0x23]
    // 0xae800c: DecompressPointer r6
    //     0xae800c: add             x6, x6, HEAP, lsl #32
    // 0xae8010: stur            x6, [fp, #-0x30]
    // 0xae8014: LoadField: r7 = r1->field_f
    //     0xae8014: ldur            w7, [x1, #0xf]
    // 0xae8018: DecompressPointer r7
    //     0xae8018: add             x7, x7, HEAP, lsl #32
    // 0xae801c: stur            x7, [fp, #-0x28]
    // 0xae8020: LoadField: r8 = r1->field_13
    //     0xae8020: ldur            w8, [x1, #0x13]
    // 0xae8024: DecompressPointer r8
    //     0xae8024: add             x8, x8, HEAP, lsl #32
    // 0xae8028: stur            x8, [fp, #-0x20]
    // 0xae802c: LoadField: r9 = r1->field_27
    //     0xae802c: ldur            x9, [x1, #0x27]
    // 0xae8030: r0 = BoxInt64Instr(r9)
    //     0xae8030: sbfiz           x0, x9, #1, #0x1f
    //     0xae8034: cmp             x9, x0, asr #1
    //     0xae8038: b.eq            #0xae8044
    //     0xae803c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8040: stur            x9, [x0, #7]
    // 0xae8044: stp             x0, NULL, [SP]
    // 0xae8048: r0 = _Double.fromInteger()
    //     0xae8048: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae804c: stur            x0, [fp, #-0x40]
    // 0xae8050: r0 = EventData()
    //     0xae8050: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xae8054: mov             x1, x0
    // 0xae8058: ldur            x0, [fp, #-0x30]
    // 0xae805c: stur            x1, [fp, #-0x48]
    // 0xae8060: StoreField: r1->field_13 = r0
    //     0xae8060: stur            w0, [x1, #0x13]
    // 0xae8064: ldur            x0, [fp, #-0x40]
    // 0xae8068: StoreField: r1->field_2f = r0
    //     0xae8068: stur            w0, [x1, #0x2f]
    // 0xae806c: ldur            x0, [fp, #-0x28]
    // 0xae8070: StoreField: r1->field_33 = r0
    //     0xae8070: stur            w0, [x1, #0x33]
    // 0xae8074: ldur            x2, [fp, #-0x18]
    // 0xae8078: StoreField: r1->field_3b = r2
    //     0xae8078: stur            w2, [x1, #0x3b]
    // 0xae807c: StoreField: r1->field_87 = r2
    //     0xae807c: stur            w2, [x1, #0x87]
    // 0xae8080: ldur            x0, [fp, #-0x20]
    // 0xae8084: StoreField: r1->field_8f = r0
    //     0xae8084: stur            w0, [x1, #0x8f]
    // 0xae8088: r0 = EventsRequest()
    //     0xae8088: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xae808c: mov             x1, x0
    // 0xae8090: r0 = "add_to_bag_clicked"
    //     0xae8090: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xae8094: ldr             x0, [x0, #0xec8]
    // 0xae8098: StoreField: r1->field_7 = r0
    //     0xae8098: stur            w0, [x1, #7]
    // 0xae809c: ldur            x0, [fp, #-0x48]
    // 0xae80a0: StoreField: r1->field_b = r0
    //     0xae80a0: stur            w0, [x1, #0xb]
    // 0xae80a4: ldur            x16, [fp, #-0x38]
    // 0xae80a8: stp             x1, x16, [SP]
    // 0xae80ac: r0 = postEvents()
    //     0xae80ac: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xae80b0: ldur            x3, [fp, #-0x10]
    // 0xae80b4: LoadField: r0 = r3->field_f
    //     0xae80b4: ldur            w0, [x3, #0xf]
    // 0xae80b8: DecompressPointer r0
    //     0xae80b8: add             x0, x0, HEAP, lsl #32
    // 0xae80bc: stur            x0, [fp, #-0x30]
    // 0xae80c0: r16 = Sentinel
    //     0xae80c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae80c4: cmp             w0, w16
    // 0xae80c8: b.eq            #0xae8360
    // 0xae80cc: ldur            x4, [fp, #-8]
    // 0xae80d0: LoadField: r1 = r4->field_f
    //     0xae80d0: ldur            w1, [x4, #0xf]
    // 0xae80d4: DecompressPointer r1
    //     0xae80d4: add             x1, x1, HEAP, lsl #32
    // 0xae80d8: LoadField: r2 = r1->field_b
    //     0xae80d8: ldur            w2, [x1, #0xb]
    // 0xae80dc: DecompressPointer r2
    //     0xae80dc: add             x2, x2, HEAP, lsl #32
    // 0xae80e0: cmp             w2, NULL
    // 0xae80e4: b.eq            #0xae83a0
    // 0xae80e8: LoadField: r1 = r2->field_f
    //     0xae80e8: ldur            w1, [x2, #0xf]
    // 0xae80ec: DecompressPointer r1
    //     0xae80ec: add             x1, x1, HEAP, lsl #32
    // 0xae80f0: stur            x1, [fp, #-0x28]
    // 0xae80f4: LoadField: r3 = r2->field_13
    //     0xae80f4: ldur            w3, [x2, #0x13]
    // 0xae80f8: DecompressPointer r3
    //     0xae80f8: add             x3, x3, HEAP, lsl #32
    // 0xae80fc: stur            x3, [fp, #-0x20]
    // 0xae8100: ArrayLoad: r4 = r2[0]  ; List_8
    //     0xae8100: ldur            x4, [x2, #0x17]
    // 0xae8104: stur            x4, [fp, #-0x50]
    // 0xae8108: r0 = AddToBagRequest()
    //     0xae8108: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xae810c: mov             x1, x0
    // 0xae8110: ldur            x0, [fp, #-0x28]
    // 0xae8114: StoreField: r1->field_7 = r0
    //     0xae8114: stur            w0, [x1, #7]
    // 0xae8118: ldur            x0, [fp, #-0x20]
    // 0xae811c: StoreField: r1->field_b = r0
    //     0xae811c: stur            w0, [x1, #0xb]
    // 0xae8120: ldur            x0, [fp, #-0x50]
    // 0xae8124: StoreField: r1->field_f = r0
    //     0xae8124: stur            x0, [x1, #0xf]
    // 0xae8128: ldur            x16, [fp, #-0x30]
    // 0xae812c: stp             x1, x16, [SP, #8]
    // 0xae8130: r16 = false
    //     0xae8130: add             x16, NULL, #0x30  ; false
    // 0xae8134: str             x16, [SP]
    // 0xae8138: r4 = 0
    //     0xae8138: movz            x4, #0
    // 0xae813c: ldr             x0, [SP, #0x10]
    // 0xae8140: r16 = UnlinkedCall_0x613b5c
    //     0xae8140: add             x16, PP, #0x58, lsl #12  ; [pp+0x58838] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8144: add             x16, x16, #0x838
    // 0xae8148: ldp             x5, lr, [x16]
    // 0xae814c: blr             lr
    // 0xae8150: b               #0xae8340
    // 0xae8154: ldur            x4, [fp, #-8]
    // 0xae8158: ldur            x3, [fp, #-0x10]
    // 0xae815c: ldur            x2, [fp, #-0x18]
    // 0xae8160: LoadField: r5 = r3->field_f
    //     0xae8160: ldur            w5, [x3, #0xf]
    // 0xae8164: DecompressPointer r5
    //     0xae8164: add             x5, x5, HEAP, lsl #32
    // 0xae8168: stur            x5, [fp, #-0x38]
    // 0xae816c: r16 = Sentinel
    //     0xae816c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae8170: cmp             w5, w16
    // 0xae8174: b.eq            #0xae8370
    // 0xae8178: LoadField: r0 = r4->field_f
    //     0xae8178: ldur            w0, [x4, #0xf]
    // 0xae817c: DecompressPointer r0
    //     0xae817c: add             x0, x0, HEAP, lsl #32
    // 0xae8180: LoadField: r1 = r0->field_b
    //     0xae8180: ldur            w1, [x0, #0xb]
    // 0xae8184: DecompressPointer r1
    //     0xae8184: add             x1, x1, HEAP, lsl #32
    // 0xae8188: cmp             w1, NULL
    // 0xae818c: b.eq            #0xae83a4
    // 0xae8190: LoadField: r6 = r1->field_23
    //     0xae8190: ldur            w6, [x1, #0x23]
    // 0xae8194: DecompressPointer r6
    //     0xae8194: add             x6, x6, HEAP, lsl #32
    // 0xae8198: stur            x6, [fp, #-0x30]
    // 0xae819c: LoadField: r7 = r1->field_f
    //     0xae819c: ldur            w7, [x1, #0xf]
    // 0xae81a0: DecompressPointer r7
    //     0xae81a0: add             x7, x7, HEAP, lsl #32
    // 0xae81a4: stur            x7, [fp, #-0x28]
    // 0xae81a8: LoadField: r8 = r1->field_13
    //     0xae81a8: ldur            w8, [x1, #0x13]
    // 0xae81ac: DecompressPointer r8
    //     0xae81ac: add             x8, x8, HEAP, lsl #32
    // 0xae81b0: stur            x8, [fp, #-0x20]
    // 0xae81b4: LoadField: r9 = r1->field_27
    //     0xae81b4: ldur            x9, [x1, #0x27]
    // 0xae81b8: r0 = BoxInt64Instr(r9)
    //     0xae81b8: sbfiz           x0, x9, #1, #0x1f
    //     0xae81bc: cmp             x9, x0, asr #1
    //     0xae81c0: b.eq            #0xae81cc
    //     0xae81c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae81c8: stur            x9, [x0, #7]
    // 0xae81cc: stp             x0, NULL, [SP]
    // 0xae81d0: r0 = _Double.fromInteger()
    //     0xae81d0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae81d4: stur            x0, [fp, #-0x40]
    // 0xae81d8: r0 = EventData()
    //     0xae81d8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xae81dc: mov             x1, x0
    // 0xae81e0: ldur            x0, [fp, #-0x30]
    // 0xae81e4: stur            x1, [fp, #-0x48]
    // 0xae81e8: StoreField: r1->field_13 = r0
    //     0xae81e8: stur            w0, [x1, #0x13]
    // 0xae81ec: ldur            x0, [fp, #-0x40]
    // 0xae81f0: StoreField: r1->field_2f = r0
    //     0xae81f0: stur            w0, [x1, #0x2f]
    // 0xae81f4: ldur            x0, [fp, #-0x28]
    // 0xae81f8: StoreField: r1->field_33 = r0
    //     0xae81f8: stur            w0, [x1, #0x33]
    // 0xae81fc: ldur            x0, [fp, #-0x18]
    // 0xae8200: StoreField: r1->field_3b = r0
    //     0xae8200: stur            w0, [x1, #0x3b]
    // 0xae8204: StoreField: r1->field_87 = r0
    //     0xae8204: stur            w0, [x1, #0x87]
    // 0xae8208: ldur            x0, [fp, #-0x20]
    // 0xae820c: StoreField: r1->field_8f = r0
    //     0xae820c: stur            w0, [x1, #0x8f]
    // 0xae8210: r0 = EventsRequest()
    //     0xae8210: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xae8214: mov             x1, x0
    // 0xae8218: r0 = "buy_now_clicked"
    //     0xae8218: add             x0, PP, #0x31, lsl #12  ; [pp+0x31be0] "buy_now_clicked"
    //     0xae821c: ldr             x0, [x0, #0xbe0]
    // 0xae8220: StoreField: r1->field_7 = r0
    //     0xae8220: stur            w0, [x1, #7]
    // 0xae8224: ldur            x0, [fp, #-0x48]
    // 0xae8228: StoreField: r1->field_b = r0
    //     0xae8228: stur            w0, [x1, #0xb]
    // 0xae822c: ldur            x16, [fp, #-0x38]
    // 0xae8230: stp             x1, x16, [SP]
    // 0xae8234: r0 = postEvents()
    //     0xae8234: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xae8238: ldur            x0, [fp, #-8]
    // 0xae823c: LoadField: r1 = r0->field_f
    //     0xae823c: ldur            w1, [x0, #0xf]
    // 0xae8240: DecompressPointer r1
    //     0xae8240: add             x1, x1, HEAP, lsl #32
    // 0xae8244: LoadField: r2 = r1->field_b
    //     0xae8244: ldur            w2, [x1, #0xb]
    // 0xae8248: DecompressPointer r2
    //     0xae8248: add             x2, x2, HEAP, lsl #32
    // 0xae824c: cmp             w2, NULL
    // 0xae8250: b.eq            #0xae83a8
    // 0xae8254: LoadField: r1 = r2->field_43
    //     0xae8254: ldur            w1, [x2, #0x43]
    // 0xae8258: DecompressPointer r1
    //     0xae8258: add             x1, x1, HEAP, lsl #32
    // 0xae825c: cmp             w1, NULL
    // 0xae8260: b.ne            #0xae826c
    // 0xae8264: r1 = Null
    //     0xae8264: mov             x1, NULL
    // 0xae8268: b               #0xae8280
    // 0xae826c: LoadField: r2 = r1->field_7
    //     0xae826c: ldur            w2, [x1, #7]
    // 0xae8270: cbnz            w2, #0xae827c
    // 0xae8274: r1 = false
    //     0xae8274: add             x1, NULL, #0x30  ; false
    // 0xae8278: b               #0xae8280
    // 0xae827c: r1 = true
    //     0xae827c: add             x1, NULL, #0x20  ; true
    // 0xae8280: cmp             w1, NULL
    // 0xae8284: b.eq            #0xae82dc
    // 0xae8288: tbnz            w1, #4, #0xae82dc
    // 0xae828c: LoadField: r3 = r0->field_13
    //     0xae828c: ldur            w3, [x0, #0x13]
    // 0xae8290: DecompressPointer r3
    //     0xae8290: add             x3, x3, HEAP, lsl #32
    // 0xae8294: ldur            x2, [fp, #-0x10]
    // 0xae8298: stur            x3, [fp, #-0x18]
    // 0xae829c: r1 = Function '<anonymous closure>':.
    //     0xae829c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58848] AnonymousClosure: (0xae8d6c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae82a0: ldr             x1, [x1, #0x848]
    // 0xae82a4: r0 = AllocateClosure()
    //     0xae82a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae82a8: stp             x0, NULL, [SP, #0x20]
    // 0xae82ac: ldur            x16, [fp, #-0x18]
    // 0xae82b0: r30 = true
    //     0xae82b0: add             lr, NULL, #0x20  ; true
    // 0xae82b4: stp             lr, x16, [SP, #0x10]
    // 0xae82b8: r16 = Instance_Color
    //     0xae82b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xae82bc: ldr             x16, [x16, #0x90]
    // 0xae82c0: r30 = Instance_RoundedRectangleBorder
    //     0xae82c0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xae82c4: ldr             lr, [lr, #0xc78]
    // 0xae82c8: stp             lr, x16, [SP]
    // 0xae82cc: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0xae82cc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0xae82d0: ldr             x4, [x4, #0x308]
    // 0xae82d4: r0 = showModalBottomSheet()
    //     0xae82d4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xae82d8: b               #0xae8340
    // 0xae82dc: r16 = PreferenceManager
    //     0xae82dc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xae82e0: ldr             x16, [x16, #0x878]
    // 0xae82e4: str             x16, [SP]
    // 0xae82e8: r0 = toString()
    //     0xae82e8: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xae82ec: r16 = <PreferenceManager>
    //     0xae82ec: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xae82f0: ldr             x16, [x16, #0x880]
    // 0xae82f4: stp             x0, x16, [SP]
    // 0xae82f8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae82f8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae82fc: r0 = Inst.find()
    //     0xae82fc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae8300: mov             x1, x0
    // 0xae8304: r2 = "token"
    //     0xae8304: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xae8308: ldr             x2, [x2, #0x958]
    // 0xae830c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae830c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae8310: r0 = getString()
    //     0xae8310: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xae8314: ldur            x2, [fp, #-0x10]
    // 0xae8318: r1 = Function '<anonymous closure>':.
    //     0xae8318: add             x1, PP, #0x58, lsl #12  ; [pp+0x58850] AnonymousClosure: (0xae83ac), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae831c: ldr             x1, [x1, #0x850]
    // 0xae8320: stur            x0, [fp, #-8]
    // 0xae8324: r0 = AllocateClosure()
    //     0xae8324: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae8328: r16 = <Null?>
    //     0xae8328: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xae832c: ldur            lr, [fp, #-8]
    // 0xae8330: stp             lr, x16, [SP, #8]
    // 0xae8334: str             x0, [SP]
    // 0xae8338: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae8338: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae833c: r0 = then()
    //     0xae833c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xae8340: r0 = Null
    //     0xae8340: mov             x0, NULL
    // 0xae8344: LeaveFrame
    //     0xae8344: mov             SP, fp
    //     0xae8348: ldp             fp, lr, [SP], #0x10
    // 0xae834c: ret
    //     0xae834c: ret             
    // 0xae8350: r16 = "controller"
    //     0xae8350: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8354: str             x16, [SP]
    // 0xae8358: r0 = _throwLocalNotInitialized()
    //     0xae8358: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae835c: brk             #0
    // 0xae8360: r16 = "controller"
    //     0xae8360: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8364: str             x16, [SP]
    // 0xae8368: r0 = _throwLocalNotInitialized()
    //     0xae8368: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae836c: brk             #0
    // 0xae8370: r16 = "controller"
    //     0xae8370: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8374: str             x16, [SP]
    // 0xae8378: r0 = _throwLocalNotInitialized()
    //     0xae8378: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae837c: brk             #0
    // 0xae8380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8380: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8384: b               #0xae7c74
    // 0xae8388: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8388: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae838c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae838c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8390: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8390: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8394: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8398: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae839c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae839c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae83a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae83a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae83a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae83a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae83a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae83a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xae83ac, size: 0x9c0
    // 0xae83ac: EnterFrame
    //     0xae83ac: stp             fp, lr, [SP, #-0x10]!
    //     0xae83b0: mov             fp, SP
    // 0xae83b4: AllocStack(0x50)
    //     0xae83b4: sub             SP, SP, #0x50
    // 0xae83b8: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xae83b8: stur            NULL, [fp, #-8]
    //     0xae83bc: movz            x0, #0
    //     0xae83c0: add             x1, fp, w0, sxtw #2
    //     0xae83c4: ldr             x1, [x1, #0x18]
    //     0xae83c8: add             x2, fp, w0, sxtw #2
    //     0xae83cc: ldr             x2, [x2, #0x10]
    //     0xae83d0: stur            x2, [fp, #-0x18]
    //     0xae83d4: ldur            w3, [x1, #0x17]
    //     0xae83d8: add             x3, x3, HEAP, lsl #32
    //     0xae83dc: stur            x3, [fp, #-0x10]
    // 0xae83e0: CheckStackOverflow
    //     0xae83e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae83e4: cmp             SP, x16
    //     0xae83e8: b.ls            #0xae8d50
    // 0xae83ec: InitAsync() -> Future<Null?>
    //     0xae83ec: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xae83f0: bl              #0x6326e0  ; InitAsyncStub
    // 0xae83f4: ldur            x0, [fp, #-0x18]
    // 0xae83f8: r1 = LoadClassIdInstr(r0)
    //     0xae83f8: ldur            x1, [x0, #-1]
    //     0xae83fc: ubfx            x1, x1, #0xc, #0x14
    // 0xae8400: r16 = ""
    //     0xae8400: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae8404: stp             x16, x0, [SP]
    // 0xae8408: mov             x0, x1
    // 0xae840c: mov             lr, x0
    // 0xae8410: ldr             lr, [x21, lr, lsl #3]
    // 0xae8414: blr             lr
    // 0xae8418: tbz             w0, #4, #0xae8ae0
    // 0xae841c: ldur            x2, [fp, #-0x10]
    // 0xae8420: LoadField: r3 = r2->field_b
    //     0xae8420: ldur            w3, [x2, #0xb]
    // 0xae8424: DecompressPointer r3
    //     0xae8424: add             x3, x3, HEAP, lsl #32
    // 0xae8428: stur            x3, [fp, #-0x28]
    // 0xae842c: LoadField: r0 = r3->field_f
    //     0xae842c: ldur            w0, [x3, #0xf]
    // 0xae8430: DecompressPointer r0
    //     0xae8430: add             x0, x0, HEAP, lsl #32
    // 0xae8434: LoadField: r1 = r0->field_b
    //     0xae8434: ldur            w1, [x0, #0xb]
    // 0xae8438: DecompressPointer r1
    //     0xae8438: add             x1, x1, HEAP, lsl #32
    // 0xae843c: cmp             w1, NULL
    // 0xae8440: b.eq            #0xae8d58
    // 0xae8444: LoadField: r4 = r1->field_f
    //     0xae8444: ldur            w4, [x1, #0xf]
    // 0xae8448: DecompressPointer r4
    //     0xae8448: add             x4, x4, HEAP, lsl #32
    // 0xae844c: stur            x4, [fp, #-0x18]
    // 0xae8450: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xae8450: ldur            x5, [x1, #0x17]
    // 0xae8454: stur            x5, [fp, #-0x20]
    // 0xae8458: LoadField: r0 = r1->field_27
    //     0xae8458: ldur            x0, [x1, #0x27]
    // 0xae845c: mul             x6, x0, x5
    // 0xae8460: r0 = BoxInt64Instr(r6)
    //     0xae8460: sbfiz           x0, x6, #1, #0x1f
    //     0xae8464: cmp             x6, x0, asr #1
    //     0xae8468: b.eq            #0xae8474
    //     0xae846c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8470: stur            x6, [x0, #7]
    // 0xae8474: stp             x0, NULL, [SP]
    // 0xae8478: r0 = _Double.fromInteger()
    //     0xae8478: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae847c: stur            x0, [fp, #-0x30]
    // 0xae8480: r0 = CheckoutEventData()
    //     0xae8480: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xae8484: mov             x2, x0
    // 0xae8488: ldur            x0, [fp, #-0x30]
    // 0xae848c: stur            x2, [fp, #-0x38]
    // 0xae8490: StoreField: r2->field_7 = r0
    //     0xae8490: stur            w0, [x2, #7]
    // 0xae8494: ldur            x3, [fp, #-0x20]
    // 0xae8498: r0 = BoxInt64Instr(r3)
    //     0xae8498: sbfiz           x0, x3, #1, #0x1f
    //     0xae849c: cmp             x3, x0, asr #1
    //     0xae84a0: b.eq            #0xae84ac
    //     0xae84a4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae84a8: stur            x3, [x0, #7]
    // 0xae84ac: StoreField: r2->field_b = r0
    //     0xae84ac: stur            w0, [x2, #0xb]
    // 0xae84b0: ldur            x0, [fp, #-0x18]
    // 0xae84b4: StoreField: r2->field_f = r0
    //     0xae84b4: stur            w0, [x2, #0xf]
    // 0xae84b8: ldur            x0, [fp, #-0x10]
    // 0xae84bc: LoadField: r1 = r0->field_f
    //     0xae84bc: ldur            w1, [x0, #0xf]
    // 0xae84c0: DecompressPointer r1
    //     0xae84c0: add             x1, x1, HEAP, lsl #32
    // 0xae84c4: r16 = Sentinel
    //     0xae84c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae84c8: cmp             w1, w16
    // 0xae84cc: b.eq            #0xae8cf0
    // 0xae84d0: str             x1, [SP]
    // 0xae84d4: r4 = 0
    //     0xae84d4: movz            x4, #0
    // 0xae84d8: ldr             x0, [SP]
    // 0xae84dc: r16 = UnlinkedCall_0x613b5c
    //     0xae84dc: add             x16, PP, #0x58, lsl #12  ; [pp+0x58858] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae84e0: add             x16, x16, #0x858
    // 0xae84e4: ldp             x5, lr, [x16]
    // 0xae84e8: blr             lr
    // 0xae84ec: LoadField: r1 = r0->field_1b
    //     0xae84ec: ldur            w1, [x0, #0x1b]
    // 0xae84f0: DecompressPointer r1
    //     0xae84f0: add             x1, x1, HEAP, lsl #32
    // 0xae84f4: cmp             w1, NULL
    // 0xae84f8: b.ne            #0xae8504
    // 0xae84fc: r0 = Null
    //     0xae84fc: mov             x0, NULL
    // 0xae8500: b               #0xae851c
    // 0xae8504: LoadField: r0 = r1->field_b
    //     0xae8504: ldur            w0, [x1, #0xb]
    // 0xae8508: cbz             w0, #0xae8514
    // 0xae850c: r1 = false
    //     0xae850c: add             x1, NULL, #0x30  ; false
    // 0xae8510: b               #0xae8518
    // 0xae8514: r1 = true
    //     0xae8514: add             x1, NULL, #0x20  ; true
    // 0xae8518: mov             x0, x1
    // 0xae851c: cmp             w0, NULL
    // 0xae8520: b.eq            #0xae8528
    // 0xae8524: tbz             w0, #4, #0xae85d0
    // 0xae8528: ldur            x0, [fp, #-0x10]
    // 0xae852c: LoadField: r1 = r0->field_f
    //     0xae852c: ldur            w1, [x0, #0xf]
    // 0xae8530: DecompressPointer r1
    //     0xae8530: add             x1, x1, HEAP, lsl #32
    // 0xae8534: r16 = Sentinel
    //     0xae8534: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae8538: cmp             w1, w16
    // 0xae853c: b.eq            #0xae8d00
    // 0xae8540: str             x1, [SP]
    // 0xae8544: r4 = 0
    //     0xae8544: movz            x4, #0
    // 0xae8548: ldr             x0, [SP]
    // 0xae854c: r16 = UnlinkedCall_0x613b5c
    //     0xae854c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58868] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8550: add             x16, x16, #0x868
    // 0xae8554: ldp             x5, lr, [x16]
    // 0xae8558: blr             lr
    // 0xae855c: LoadField: r1 = r0->field_1b
    //     0xae855c: ldur            w1, [x0, #0x1b]
    // 0xae8560: DecompressPointer r1
    //     0xae8560: add             x1, x1, HEAP, lsl #32
    // 0xae8564: cmp             w1, NULL
    // 0xae8568: b.ne            #0xae8574
    // 0xae856c: r0 = Null
    //     0xae856c: mov             x0, NULL
    // 0xae8570: b               #0xae85b8
    // 0xae8574: r0 = first()
    //     0xae8574: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xae8578: cmp             w0, NULL
    // 0xae857c: b.ne            #0xae8588
    // 0xae8580: r0 = Null
    //     0xae8580: mov             x0, NULL
    // 0xae8584: b               #0xae85b8
    // 0xae8588: LoadField: r1 = r0->field_13
    //     0xae8588: ldur            w1, [x0, #0x13]
    // 0xae858c: DecompressPointer r1
    //     0xae858c: add             x1, x1, HEAP, lsl #32
    // 0xae8590: cmp             w1, NULL
    // 0xae8594: b.ne            #0xae85a0
    // 0xae8598: r0 = Null
    //     0xae8598: mov             x0, NULL
    // 0xae859c: b               #0xae85b8
    // 0xae85a0: LoadField: r0 = r1->field_7
    //     0xae85a0: ldur            w0, [x1, #7]
    // 0xae85a4: cbz             w0, #0xae85b0
    // 0xae85a8: r1 = false
    //     0xae85a8: add             x1, NULL, #0x30  ; false
    // 0xae85ac: b               #0xae85b4
    // 0xae85b0: r1 = true
    //     0xae85b0: add             x1, NULL, #0x20  ; true
    // 0xae85b4: mov             x0, x1
    // 0xae85b8: cmp             w0, NULL
    // 0xae85bc: b.ne            #0xae85cc
    // 0xae85c0: ldur            x2, [fp, #-0x10]
    // 0xae85c4: ldur            x0, [fp, #-0x28]
    // 0xae85c8: b               #0xae8860
    // 0xae85cc: tbnz            w0, #4, #0xae8858
    // 0xae85d0: ldur            x0, [fp, #-0x10]
    // 0xae85d4: ldur            x1, [fp, #-0x28]
    // 0xae85d8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae85d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae85dc: ldr             x0, [x0, #0x1c80]
    //     0xae85e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae85e4: cmp             w0, w16
    //     0xae85e8: b.ne            #0xae85f4
    //     0xae85ec: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae85f0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae85f4: r1 = Null
    //     0xae85f4: mov             x1, NULL
    // 0xae85f8: r2 = 40
    //     0xae85f8: movz            x2, #0x28
    // 0xae85fc: r0 = AllocateArray()
    //     0xae85fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae8600: mov             x2, x0
    // 0xae8604: stur            x2, [fp, #-0x18]
    // 0xae8608: r16 = "couponCode"
    //     0xae8608: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xae860c: ldr             x16, [x16, #0x310]
    // 0xae8610: StoreField: r2->field_f = r16
    //     0xae8610: stur            w16, [x2, #0xf]
    // 0xae8614: r16 = ""
    //     0xae8614: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae8618: StoreField: r2->field_13 = r16
    //     0xae8618: stur            w16, [x2, #0x13]
    // 0xae861c: r16 = "product_id"
    //     0xae861c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae8620: ldr             x16, [x16, #0x9b8]
    // 0xae8624: ArrayStore: r2[0] = r16  ; List_4
    //     0xae8624: stur            w16, [x2, #0x17]
    // 0xae8628: ldur            x0, [fp, #-0x28]
    // 0xae862c: LoadField: r1 = r0->field_f
    //     0xae862c: ldur            w1, [x0, #0xf]
    // 0xae8630: DecompressPointer r1
    //     0xae8630: add             x1, x1, HEAP, lsl #32
    // 0xae8634: LoadField: r3 = r1->field_b
    //     0xae8634: ldur            w3, [x1, #0xb]
    // 0xae8638: DecompressPointer r3
    //     0xae8638: add             x3, x3, HEAP, lsl #32
    // 0xae863c: cmp             w3, NULL
    // 0xae8640: b.eq            #0xae8d5c
    // 0xae8644: LoadField: r0 = r3->field_f
    //     0xae8644: ldur            w0, [x3, #0xf]
    // 0xae8648: DecompressPointer r0
    //     0xae8648: add             x0, x0, HEAP, lsl #32
    // 0xae864c: StoreField: r2->field_1b = r0
    //     0xae864c: stur            w0, [x2, #0x1b]
    // 0xae8650: r16 = "sku_id"
    //     0xae8650: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae8654: ldr             x16, [x16, #0x498]
    // 0xae8658: StoreField: r2->field_1f = r16
    //     0xae8658: stur            w16, [x2, #0x1f]
    // 0xae865c: LoadField: r0 = r3->field_13
    //     0xae865c: ldur            w0, [x3, #0x13]
    // 0xae8660: DecompressPointer r0
    //     0xae8660: add             x0, x0, HEAP, lsl #32
    // 0xae8664: StoreField: r2->field_23 = r0
    //     0xae8664: stur            w0, [x2, #0x23]
    // 0xae8668: r16 = "quantity"
    //     0xae8668: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae866c: ldr             x16, [x16, #0x428]
    // 0xae8670: StoreField: r2->field_27 = r16
    //     0xae8670: stur            w16, [x2, #0x27]
    // 0xae8674: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae8674: ldur            x4, [x3, #0x17]
    // 0xae8678: r0 = BoxInt64Instr(r4)
    //     0xae8678: sbfiz           x0, x4, #1, #0x1f
    //     0xae867c: cmp             x4, x0, asr #1
    //     0xae8680: b.eq            #0xae868c
    //     0xae8684: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8688: stur            x4, [x0, #7]
    // 0xae868c: mov             x1, x2
    // 0xae8690: ArrayStore: r1[7] = r0  ; List_4
    //     0xae8690: add             x25, x1, #0x2b
    //     0xae8694: str             w0, [x25]
    //     0xae8698: tbz             w0, #0, #0xae86b4
    //     0xae869c: ldurb           w16, [x1, #-1]
    //     0xae86a0: ldurb           w17, [x0, #-1]
    //     0xae86a4: and             x16, x17, x16, lsr #2
    //     0xae86a8: tst             x16, HEAP, lsr #32
    //     0xae86ac: b.eq            #0xae86b4
    //     0xae86b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae86b4: r16 = "checkout_event_data"
    //     0xae86b4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae86b8: ldr             x16, [x16, #0xd50]
    // 0xae86bc: StoreField: r2->field_2f = r16
    //     0xae86bc: stur            w16, [x2, #0x2f]
    // 0xae86c0: mov             x1, x2
    // 0xae86c4: ldur            x0, [fp, #-0x38]
    // 0xae86c8: ArrayStore: r1[9] = r0  ; List_4
    //     0xae86c8: add             x25, x1, #0x33
    //     0xae86cc: str             w0, [x25]
    //     0xae86d0: tbz             w0, #0, #0xae86ec
    //     0xae86d4: ldurb           w16, [x1, #-1]
    //     0xae86d8: ldurb           w17, [x0, #-1]
    //     0xae86dc: and             x16, x17, x16, lsr #2
    //     0xae86e0: tst             x16, HEAP, lsr #32
    //     0xae86e4: b.eq            #0xae86ec
    //     0xae86e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae86ec: r16 = "previousScreenSource"
    //     0xae86ec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae86f0: ldr             x16, [x16, #0x448]
    // 0xae86f4: StoreField: r2->field_37 = r16
    //     0xae86f4: stur            w16, [x2, #0x37]
    // 0xae86f8: r16 = "product_page"
    //     0xae86f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae86fc: ldr             x16, [x16, #0x480]
    // 0xae8700: StoreField: r2->field_3b = r16
    //     0xae8700: stur            w16, [x2, #0x3b]
    // 0xae8704: r16 = "is_skipped_address"
    //     0xae8704: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae8708: ldr             x16, [x16, #0xb80]
    // 0xae870c: StoreField: r2->field_3f = r16
    //     0xae870c: stur            w16, [x2, #0x3f]
    // 0xae8710: r16 = false
    //     0xae8710: add             x16, NULL, #0x30  ; false
    // 0xae8714: StoreField: r2->field_43 = r16
    //     0xae8714: stur            w16, [x2, #0x43]
    // 0xae8718: r16 = "coming_from"
    //     0xae8718: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae871c: ldr             x16, [x16, #0x328]
    // 0xae8720: StoreField: r2->field_47 = r16
    //     0xae8720: stur            w16, [x2, #0x47]
    // 0xae8724: LoadField: r0 = r3->field_2f
    //     0xae8724: ldur            w0, [x3, #0x2f]
    // 0xae8728: DecompressPointer r0
    //     0xae8728: add             x0, x0, HEAP, lsl #32
    // 0xae872c: mov             x1, x2
    // 0xae8730: ArrayStore: r1[15] = r0  ; List_4
    //     0xae8730: add             x25, x1, #0x4b
    //     0xae8734: str             w0, [x25]
    //     0xae8738: tbz             w0, #0, #0xae8754
    //     0xae873c: ldurb           w16, [x1, #-1]
    //     0xae8740: ldurb           w17, [x0, #-1]
    //     0xae8744: and             x16, x17, x16, lsr #2
    //     0xae8748: tst             x16, HEAP, lsr #32
    //     0xae874c: b.eq            #0xae8754
    //     0xae8750: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8754: r16 = "checkout_id"
    //     0xae8754: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xae8758: ldr             x16, [x16, #0xb88]
    // 0xae875c: StoreField: r2->field_4f = r16
    //     0xae875c: stur            w16, [x2, #0x4f]
    // 0xae8760: ldur            x0, [fp, #-0x10]
    // 0xae8764: LoadField: r1 = r0->field_f
    //     0xae8764: ldur            w1, [x0, #0xf]
    // 0xae8768: DecompressPointer r1
    //     0xae8768: add             x1, x1, HEAP, lsl #32
    // 0xae876c: r16 = Sentinel
    //     0xae876c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae8770: cmp             w1, w16
    // 0xae8774: b.eq            #0xae8d10
    // 0xae8778: str             x1, [SP]
    // 0xae877c: r4 = 0
    //     0xae877c: movz            x4, #0
    // 0xae8780: ldr             x0, [SP]
    // 0xae8784: r16 = UnlinkedCall_0x613b5c
    //     0xae8784: add             x16, PP, #0x58, lsl #12  ; [pp+0x58878] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8788: add             x16, x16, #0x878
    // 0xae878c: ldp             x5, lr, [x16]
    // 0xae8790: blr             lr
    // 0xae8794: ldur            x1, [fp, #-0x18]
    // 0xae8798: ArrayStore: r1[17] = r0  ; List_4
    //     0xae8798: add             x25, x1, #0x53
    //     0xae879c: str             w0, [x25]
    //     0xae87a0: tbz             w0, #0, #0xae87bc
    //     0xae87a4: ldurb           w16, [x1, #-1]
    //     0xae87a8: ldurb           w17, [x0, #-1]
    //     0xae87ac: and             x16, x17, x16, lsr #2
    //     0xae87b0: tst             x16, HEAP, lsr #32
    //     0xae87b4: b.eq            #0xae87bc
    //     0xae87b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae87bc: ldur            x1, [fp, #-0x18]
    // 0xae87c0: r16 = "user_data"
    //     0xae87c0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xae87c4: ldr             x16, [x16, #0x58]
    // 0xae87c8: StoreField: r1->field_57 = r16
    //     0xae87c8: stur            w16, [x1, #0x57]
    // 0xae87cc: ldur            x2, [fp, #-0x10]
    // 0xae87d0: LoadField: r0 = r2->field_f
    //     0xae87d0: ldur            w0, [x2, #0xf]
    // 0xae87d4: DecompressPointer r0
    //     0xae87d4: add             x0, x0, HEAP, lsl #32
    // 0xae87d8: r16 = Sentinel
    //     0xae87d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae87dc: cmp             w0, w16
    // 0xae87e0: b.eq            #0xae8d20
    // 0xae87e4: str             x0, [SP]
    // 0xae87e8: r4 = 0
    //     0xae87e8: movz            x4, #0
    // 0xae87ec: ldr             x0, [SP]
    // 0xae87f0: r16 = UnlinkedCall_0x613b5c
    //     0xae87f0: add             x16, PP, #0x58, lsl #12  ; [pp+0x58888] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae87f4: add             x16, x16, #0x888
    // 0xae87f8: ldp             x5, lr, [x16]
    // 0xae87fc: blr             lr
    // 0xae8800: ldur            x1, [fp, #-0x18]
    // 0xae8804: ArrayStore: r1[19] = r0  ; List_4
    //     0xae8804: add             x25, x1, #0x5b
    //     0xae8808: str             w0, [x25]
    //     0xae880c: tbz             w0, #0, #0xae8828
    //     0xae8810: ldurb           w16, [x1, #-1]
    //     0xae8814: ldurb           w17, [x0, #-1]
    //     0xae8818: and             x16, x17, x16, lsr #2
    //     0xae881c: tst             x16, HEAP, lsr #32
    //     0xae8820: b.eq            #0xae8828
    //     0xae8824: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8828: r16 = <String, dynamic>
    //     0xae8828: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xae882c: ldur            lr, [fp, #-0x18]
    // 0xae8830: stp             lr, x16, [SP]
    // 0xae8834: r0 = Map._fromLiteral()
    //     0xae8834: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae8838: r16 = "/checkout_request_address_page"
    //     0xae8838: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xae883c: ldr             x16, [x16, #0x9e8]
    // 0xae8840: stp             x16, NULL, [SP, #8]
    // 0xae8844: str             x0, [SP]
    // 0xae8848: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae8848: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae884c: ldr             x4, [x4, #0x438]
    // 0xae8850: r0 = GetNavigation.toNamed()
    //     0xae8850: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae8854: b               #0xae8ce8
    // 0xae8858: ldur            x2, [fp, #-0x10]
    // 0xae885c: ldur            x0, [fp, #-0x28]
    // 0xae8860: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8860: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8864: ldr             x0, [x0, #0x1c80]
    //     0xae8868: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae886c: cmp             w0, w16
    //     0xae8870: b.ne            #0xae887c
    //     0xae8874: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae8878: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae887c: r1 = Null
    //     0xae887c: mov             x1, NULL
    // 0xae8880: r2 = 40
    //     0xae8880: movz            x2, #0x28
    // 0xae8884: r0 = AllocateArray()
    //     0xae8884: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae8888: mov             x2, x0
    // 0xae888c: stur            x2, [fp, #-0x18]
    // 0xae8890: r16 = "couponCode"
    //     0xae8890: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xae8894: ldr             x16, [x16, #0x310]
    // 0xae8898: StoreField: r2->field_f = r16
    //     0xae8898: stur            w16, [x2, #0xf]
    // 0xae889c: r16 = ""
    //     0xae889c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae88a0: StoreField: r2->field_13 = r16
    //     0xae88a0: stur            w16, [x2, #0x13]
    // 0xae88a4: r16 = "product_id"
    //     0xae88a4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae88a8: ldr             x16, [x16, #0x9b8]
    // 0xae88ac: ArrayStore: r2[0] = r16  ; List_4
    //     0xae88ac: stur            w16, [x2, #0x17]
    // 0xae88b0: ldur            x0, [fp, #-0x28]
    // 0xae88b4: LoadField: r1 = r0->field_f
    //     0xae88b4: ldur            w1, [x0, #0xf]
    // 0xae88b8: DecompressPointer r1
    //     0xae88b8: add             x1, x1, HEAP, lsl #32
    // 0xae88bc: LoadField: r3 = r1->field_b
    //     0xae88bc: ldur            w3, [x1, #0xb]
    // 0xae88c0: DecompressPointer r3
    //     0xae88c0: add             x3, x3, HEAP, lsl #32
    // 0xae88c4: cmp             w3, NULL
    // 0xae88c8: b.eq            #0xae8d60
    // 0xae88cc: LoadField: r0 = r3->field_f
    //     0xae88cc: ldur            w0, [x3, #0xf]
    // 0xae88d0: DecompressPointer r0
    //     0xae88d0: add             x0, x0, HEAP, lsl #32
    // 0xae88d4: StoreField: r2->field_1b = r0
    //     0xae88d4: stur            w0, [x2, #0x1b]
    // 0xae88d8: r16 = "sku_id"
    //     0xae88d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae88dc: ldr             x16, [x16, #0x498]
    // 0xae88e0: StoreField: r2->field_1f = r16
    //     0xae88e0: stur            w16, [x2, #0x1f]
    // 0xae88e4: LoadField: r0 = r3->field_13
    //     0xae88e4: ldur            w0, [x3, #0x13]
    // 0xae88e8: DecompressPointer r0
    //     0xae88e8: add             x0, x0, HEAP, lsl #32
    // 0xae88ec: StoreField: r2->field_23 = r0
    //     0xae88ec: stur            w0, [x2, #0x23]
    // 0xae88f0: r16 = "quantity"
    //     0xae88f0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae88f4: ldr             x16, [x16, #0x428]
    // 0xae88f8: StoreField: r2->field_27 = r16
    //     0xae88f8: stur            w16, [x2, #0x27]
    // 0xae88fc: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae88fc: ldur            x4, [x3, #0x17]
    // 0xae8900: r0 = BoxInt64Instr(r4)
    //     0xae8900: sbfiz           x0, x4, #1, #0x1f
    //     0xae8904: cmp             x4, x0, asr #1
    //     0xae8908: b.eq            #0xae8914
    //     0xae890c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8910: stur            x4, [x0, #7]
    // 0xae8914: mov             x1, x2
    // 0xae8918: ArrayStore: r1[7] = r0  ; List_4
    //     0xae8918: add             x25, x1, #0x2b
    //     0xae891c: str             w0, [x25]
    //     0xae8920: tbz             w0, #0, #0xae893c
    //     0xae8924: ldurb           w16, [x1, #-1]
    //     0xae8928: ldurb           w17, [x0, #-1]
    //     0xae892c: and             x16, x17, x16, lsr #2
    //     0xae8930: tst             x16, HEAP, lsr #32
    //     0xae8934: b.eq            #0xae893c
    //     0xae8938: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae893c: r16 = "checkout_event_data"
    //     0xae893c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae8940: ldr             x16, [x16, #0xd50]
    // 0xae8944: StoreField: r2->field_2f = r16
    //     0xae8944: stur            w16, [x2, #0x2f]
    // 0xae8948: mov             x1, x2
    // 0xae894c: ldur            x0, [fp, #-0x38]
    // 0xae8950: ArrayStore: r1[9] = r0  ; List_4
    //     0xae8950: add             x25, x1, #0x33
    //     0xae8954: str             w0, [x25]
    //     0xae8958: tbz             w0, #0, #0xae8974
    //     0xae895c: ldurb           w16, [x1, #-1]
    //     0xae8960: ldurb           w17, [x0, #-1]
    //     0xae8964: and             x16, x17, x16, lsr #2
    //     0xae8968: tst             x16, HEAP, lsr #32
    //     0xae896c: b.eq            #0xae8974
    //     0xae8970: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8974: r16 = "previousScreenSource"
    //     0xae8974: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae8978: ldr             x16, [x16, #0x448]
    // 0xae897c: StoreField: r2->field_37 = r16
    //     0xae897c: stur            w16, [x2, #0x37]
    // 0xae8980: r16 = "product_page"
    //     0xae8980: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae8984: ldr             x16, [x16, #0x480]
    // 0xae8988: StoreField: r2->field_3b = r16
    //     0xae8988: stur            w16, [x2, #0x3b]
    // 0xae898c: r16 = "is_skipped_address"
    //     0xae898c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae8990: ldr             x16, [x16, #0xb80]
    // 0xae8994: StoreField: r2->field_3f = r16
    //     0xae8994: stur            w16, [x2, #0x3f]
    // 0xae8998: r16 = true
    //     0xae8998: add             x16, NULL, #0x20  ; true
    // 0xae899c: StoreField: r2->field_43 = r16
    //     0xae899c: stur            w16, [x2, #0x43]
    // 0xae89a0: r16 = "coming_from"
    //     0xae89a0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae89a4: ldr             x16, [x16, #0x328]
    // 0xae89a8: StoreField: r2->field_47 = r16
    //     0xae89a8: stur            w16, [x2, #0x47]
    // 0xae89ac: LoadField: r0 = r3->field_2f
    //     0xae89ac: ldur            w0, [x3, #0x2f]
    // 0xae89b0: DecompressPointer r0
    //     0xae89b0: add             x0, x0, HEAP, lsl #32
    // 0xae89b4: mov             x1, x2
    // 0xae89b8: ArrayStore: r1[15] = r0  ; List_4
    //     0xae89b8: add             x25, x1, #0x4b
    //     0xae89bc: str             w0, [x25]
    //     0xae89c0: tbz             w0, #0, #0xae89dc
    //     0xae89c4: ldurb           w16, [x1, #-1]
    //     0xae89c8: ldurb           w17, [x0, #-1]
    //     0xae89cc: and             x16, x17, x16, lsr #2
    //     0xae89d0: tst             x16, HEAP, lsr #32
    //     0xae89d4: b.eq            #0xae89dc
    //     0xae89d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae89dc: r16 = "checkout_id"
    //     0xae89dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xae89e0: ldr             x16, [x16, #0xb88]
    // 0xae89e4: StoreField: r2->field_4f = r16
    //     0xae89e4: stur            w16, [x2, #0x4f]
    // 0xae89e8: ldur            x0, [fp, #-0x10]
    // 0xae89ec: LoadField: r1 = r0->field_f
    //     0xae89ec: ldur            w1, [x0, #0xf]
    // 0xae89f0: DecompressPointer r1
    //     0xae89f0: add             x1, x1, HEAP, lsl #32
    // 0xae89f4: r16 = Sentinel
    //     0xae89f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae89f8: cmp             w1, w16
    // 0xae89fc: b.eq            #0xae8d30
    // 0xae8a00: str             x1, [SP]
    // 0xae8a04: r4 = 0
    //     0xae8a04: movz            x4, #0
    // 0xae8a08: ldr             x0, [SP]
    // 0xae8a0c: r16 = UnlinkedCall_0x613b5c
    //     0xae8a0c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58898] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8a10: add             x16, x16, #0x898
    // 0xae8a14: ldp             x5, lr, [x16]
    // 0xae8a18: blr             lr
    // 0xae8a1c: ldur            x1, [fp, #-0x18]
    // 0xae8a20: ArrayStore: r1[17] = r0  ; List_4
    //     0xae8a20: add             x25, x1, #0x53
    //     0xae8a24: str             w0, [x25]
    //     0xae8a28: tbz             w0, #0, #0xae8a44
    //     0xae8a2c: ldurb           w16, [x1, #-1]
    //     0xae8a30: ldurb           w17, [x0, #-1]
    //     0xae8a34: and             x16, x17, x16, lsr #2
    //     0xae8a38: tst             x16, HEAP, lsr #32
    //     0xae8a3c: b.eq            #0xae8a44
    //     0xae8a40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8a44: ldur            x1, [fp, #-0x18]
    // 0xae8a48: r16 = "user_data"
    //     0xae8a48: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xae8a4c: ldr             x16, [x16, #0x58]
    // 0xae8a50: StoreField: r1->field_57 = r16
    //     0xae8a50: stur            w16, [x1, #0x57]
    // 0xae8a54: ldur            x0, [fp, #-0x10]
    // 0xae8a58: LoadField: r2 = r0->field_f
    //     0xae8a58: ldur            w2, [x0, #0xf]
    // 0xae8a5c: DecompressPointer r2
    //     0xae8a5c: add             x2, x2, HEAP, lsl #32
    // 0xae8a60: r16 = Sentinel
    //     0xae8a60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae8a64: cmp             w2, w16
    // 0xae8a68: b.eq            #0xae8d40
    // 0xae8a6c: str             x2, [SP]
    // 0xae8a70: r4 = 0
    //     0xae8a70: movz            x4, #0
    // 0xae8a74: ldr             x0, [SP]
    // 0xae8a78: r16 = UnlinkedCall_0x613b5c
    //     0xae8a78: add             x16, PP, #0x58, lsl #12  ; [pp+0x588a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8a7c: add             x16, x16, #0x8a8
    // 0xae8a80: ldp             x5, lr, [x16]
    // 0xae8a84: blr             lr
    // 0xae8a88: ldur            x1, [fp, #-0x18]
    // 0xae8a8c: ArrayStore: r1[19] = r0  ; List_4
    //     0xae8a8c: add             x25, x1, #0x5b
    //     0xae8a90: str             w0, [x25]
    //     0xae8a94: tbz             w0, #0, #0xae8ab0
    //     0xae8a98: ldurb           w16, [x1, #-1]
    //     0xae8a9c: ldurb           w17, [x0, #-1]
    //     0xae8aa0: and             x16, x17, x16, lsr #2
    //     0xae8aa4: tst             x16, HEAP, lsr #32
    //     0xae8aa8: b.eq            #0xae8ab0
    //     0xae8aac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8ab0: r16 = <String, dynamic>
    //     0xae8ab0: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xae8ab4: ldur            lr, [fp, #-0x18]
    // 0xae8ab8: stp             lr, x16, [SP]
    // 0xae8abc: r0 = Map._fromLiteral()
    //     0xae8abc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae8ac0: r16 = "/checkout_order_summary_page"
    //     0xae8ac0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xae8ac4: ldr             x16, [x16, #0x9d8]
    // 0xae8ac8: stp             x16, NULL, [SP, #8]
    // 0xae8acc: str             x0, [SP]
    // 0xae8ad0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae8ad0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae8ad4: ldr             x4, [x4, #0x438]
    // 0xae8ad8: r0 = GetNavigation.toNamed()
    //     0xae8ad8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae8adc: b               #0xae8ce8
    // 0xae8ae0: ldur            x0, [fp, #-0x10]
    // 0xae8ae4: LoadField: r2 = r0->field_b
    //     0xae8ae4: ldur            w2, [x0, #0xb]
    // 0xae8ae8: DecompressPointer r2
    //     0xae8ae8: add             x2, x2, HEAP, lsl #32
    // 0xae8aec: stur            x2, [fp, #-0x18]
    // 0xae8af0: LoadField: r0 = r2->field_f
    //     0xae8af0: ldur            w0, [x2, #0xf]
    // 0xae8af4: DecompressPointer r0
    //     0xae8af4: add             x0, x0, HEAP, lsl #32
    // 0xae8af8: LoadField: r1 = r0->field_b
    //     0xae8af8: ldur            w1, [x0, #0xb]
    // 0xae8afc: DecompressPointer r1
    //     0xae8afc: add             x1, x1, HEAP, lsl #32
    // 0xae8b00: cmp             w1, NULL
    // 0xae8b04: b.eq            #0xae8d64
    // 0xae8b08: LoadField: r3 = r1->field_f
    //     0xae8b08: ldur            w3, [x1, #0xf]
    // 0xae8b0c: DecompressPointer r3
    //     0xae8b0c: add             x3, x3, HEAP, lsl #32
    // 0xae8b10: stur            x3, [fp, #-0x10]
    // 0xae8b14: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xae8b14: ldur            x4, [x1, #0x17]
    // 0xae8b18: stur            x4, [fp, #-0x20]
    // 0xae8b1c: LoadField: r0 = r1->field_27
    //     0xae8b1c: ldur            x0, [x1, #0x27]
    // 0xae8b20: mul             x5, x0, x4
    // 0xae8b24: r0 = BoxInt64Instr(r5)
    //     0xae8b24: sbfiz           x0, x5, #1, #0x1f
    //     0xae8b28: cmp             x5, x0, asr #1
    //     0xae8b2c: b.eq            #0xae8b38
    //     0xae8b30: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8b34: stur            x5, [x0, #7]
    // 0xae8b38: stp             x0, NULL, [SP]
    // 0xae8b3c: r0 = _Double.fromInteger()
    //     0xae8b3c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae8b40: stur            x0, [fp, #-0x28]
    // 0xae8b44: r0 = CheckoutEventData()
    //     0xae8b44: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xae8b48: mov             x2, x0
    // 0xae8b4c: ldur            x0, [fp, #-0x28]
    // 0xae8b50: stur            x2, [fp, #-0x30]
    // 0xae8b54: StoreField: r2->field_7 = r0
    //     0xae8b54: stur            w0, [x2, #7]
    // 0xae8b58: ldur            x3, [fp, #-0x20]
    // 0xae8b5c: r0 = BoxInt64Instr(r3)
    //     0xae8b5c: sbfiz           x0, x3, #1, #0x1f
    //     0xae8b60: cmp             x3, x0, asr #1
    //     0xae8b64: b.eq            #0xae8b70
    //     0xae8b68: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8b6c: stur            x3, [x0, #7]
    // 0xae8b70: StoreField: r2->field_b = r0
    //     0xae8b70: stur            w0, [x2, #0xb]
    // 0xae8b74: ldur            x0, [fp, #-0x10]
    // 0xae8b78: StoreField: r2->field_f = r0
    //     0xae8b78: stur            w0, [x2, #0xf]
    // 0xae8b7c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8b7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8b80: ldr             x0, [x0, #0x1c80]
    //     0xae8b84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae8b88: cmp             w0, w16
    //     0xae8b8c: b.ne            #0xae8b98
    //     0xae8b90: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae8b94: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae8b98: r1 = Null
    //     0xae8b98: mov             x1, NULL
    // 0xae8b9c: r2 = 28
    //     0xae8b9c: movz            x2, #0x1c
    // 0xae8ba0: r0 = AllocateArray()
    //     0xae8ba0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae8ba4: mov             x2, x0
    // 0xae8ba8: r16 = "previousScreenSource"
    //     0xae8ba8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae8bac: ldr             x16, [x16, #0x448]
    // 0xae8bb0: StoreField: r2->field_f = r16
    //     0xae8bb0: stur            w16, [x2, #0xf]
    // 0xae8bb4: r16 = "product_page"
    //     0xae8bb4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae8bb8: ldr             x16, [x16, #0x480]
    // 0xae8bbc: StoreField: r2->field_13 = r16
    //     0xae8bbc: stur            w16, [x2, #0x13]
    // 0xae8bc0: r16 = "checkout_event_data"
    //     0xae8bc0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae8bc4: ldr             x16, [x16, #0xd50]
    // 0xae8bc8: ArrayStore: r2[0] = r16  ; List_4
    //     0xae8bc8: stur            w16, [x2, #0x17]
    // 0xae8bcc: ldur            x0, [fp, #-0x30]
    // 0xae8bd0: StoreField: r2->field_1b = r0
    //     0xae8bd0: stur            w0, [x2, #0x1b]
    // 0xae8bd4: r16 = "product_id"
    //     0xae8bd4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae8bd8: ldr             x16, [x16, #0x9b8]
    // 0xae8bdc: StoreField: r2->field_1f = r16
    //     0xae8bdc: stur            w16, [x2, #0x1f]
    // 0xae8be0: ldur            x0, [fp, #-0x18]
    // 0xae8be4: LoadField: r1 = r0->field_f
    //     0xae8be4: ldur            w1, [x0, #0xf]
    // 0xae8be8: DecompressPointer r1
    //     0xae8be8: add             x1, x1, HEAP, lsl #32
    // 0xae8bec: LoadField: r3 = r1->field_b
    //     0xae8bec: ldur            w3, [x1, #0xb]
    // 0xae8bf0: DecompressPointer r3
    //     0xae8bf0: add             x3, x3, HEAP, lsl #32
    // 0xae8bf4: cmp             w3, NULL
    // 0xae8bf8: b.eq            #0xae8d68
    // 0xae8bfc: LoadField: r0 = r3->field_f
    //     0xae8bfc: ldur            w0, [x3, #0xf]
    // 0xae8c00: DecompressPointer r0
    //     0xae8c00: add             x0, x0, HEAP, lsl #32
    // 0xae8c04: StoreField: r2->field_23 = r0
    //     0xae8c04: stur            w0, [x2, #0x23]
    // 0xae8c08: r16 = "sku_id"
    //     0xae8c08: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae8c0c: ldr             x16, [x16, #0x498]
    // 0xae8c10: StoreField: r2->field_27 = r16
    //     0xae8c10: stur            w16, [x2, #0x27]
    // 0xae8c14: LoadField: r0 = r3->field_13
    //     0xae8c14: ldur            w0, [x3, #0x13]
    // 0xae8c18: DecompressPointer r0
    //     0xae8c18: add             x0, x0, HEAP, lsl #32
    // 0xae8c1c: StoreField: r2->field_2b = r0
    //     0xae8c1c: stur            w0, [x2, #0x2b]
    // 0xae8c20: r16 = "quantity"
    //     0xae8c20: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae8c24: ldr             x16, [x16, #0x428]
    // 0xae8c28: StoreField: r2->field_2f = r16
    //     0xae8c28: stur            w16, [x2, #0x2f]
    // 0xae8c2c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae8c2c: ldur            x4, [x3, #0x17]
    // 0xae8c30: r0 = BoxInt64Instr(r4)
    //     0xae8c30: sbfiz           x0, x4, #1, #0x1f
    //     0xae8c34: cmp             x4, x0, asr #1
    //     0xae8c38: b.eq            #0xae8c44
    //     0xae8c3c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8c40: stur            x4, [x0, #7]
    // 0xae8c44: mov             x1, x2
    // 0xae8c48: ArrayStore: r1[9] = r0  ; List_4
    //     0xae8c48: add             x25, x1, #0x33
    //     0xae8c4c: str             w0, [x25]
    //     0xae8c50: tbz             w0, #0, #0xae8c6c
    //     0xae8c54: ldurb           w16, [x1, #-1]
    //     0xae8c58: ldurb           w17, [x0, #-1]
    //     0xae8c5c: and             x16, x17, x16, lsr #2
    //     0xae8c60: tst             x16, HEAP, lsr #32
    //     0xae8c64: b.eq            #0xae8c6c
    //     0xae8c68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8c6c: r16 = "coming_from"
    //     0xae8c6c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae8c70: ldr             x16, [x16, #0x328]
    // 0xae8c74: StoreField: r2->field_37 = r16
    //     0xae8c74: stur            w16, [x2, #0x37]
    // 0xae8c78: LoadField: r0 = r3->field_2f
    //     0xae8c78: ldur            w0, [x3, #0x2f]
    // 0xae8c7c: DecompressPointer r0
    //     0xae8c7c: add             x0, x0, HEAP, lsl #32
    // 0xae8c80: mov             x1, x2
    // 0xae8c84: ArrayStore: r1[11] = r0  ; List_4
    //     0xae8c84: add             x25, x1, #0x3b
    //     0xae8c88: str             w0, [x25]
    //     0xae8c8c: tbz             w0, #0, #0xae8ca8
    //     0xae8c90: ldurb           w16, [x1, #-1]
    //     0xae8c94: ldurb           w17, [x0, #-1]
    //     0xae8c98: and             x16, x17, x16, lsr #2
    //     0xae8c9c: tst             x16, HEAP, lsr #32
    //     0xae8ca0: b.eq            #0xae8ca8
    //     0xae8ca4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae8ca8: r16 = "is_skipped_address"
    //     0xae8ca8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae8cac: ldr             x16, [x16, #0xb80]
    // 0xae8cb0: StoreField: r2->field_3f = r16
    //     0xae8cb0: stur            w16, [x2, #0x3f]
    // 0xae8cb4: r16 = true
    //     0xae8cb4: add             x16, NULL, #0x20  ; true
    // 0xae8cb8: StoreField: r2->field_43 = r16
    //     0xae8cb8: stur            w16, [x2, #0x43]
    // 0xae8cbc: r16 = <String, Object?>
    //     0xae8cbc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xae8cc0: ldr             x16, [x16, #0xc28]
    // 0xae8cc4: stp             x2, x16, [SP]
    // 0xae8cc8: r0 = Map._fromLiteral()
    //     0xae8cc8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae8ccc: r16 = "/checkout_request_number_page"
    //     0xae8ccc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xae8cd0: ldr             x16, [x16, #0x9f8]
    // 0xae8cd4: stp             x16, NULL, [SP, #8]
    // 0xae8cd8: str             x0, [SP]
    // 0xae8cdc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae8cdc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae8ce0: ldr             x4, [x4, #0x438]
    // 0xae8ce4: r0 = GetNavigation.toNamed()
    //     0xae8ce4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae8ce8: r0 = Null
    //     0xae8ce8: mov             x0, NULL
    // 0xae8cec: r0 = ReturnAsyncNotFuture()
    //     0xae8cec: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xae8cf0: r16 = "controller"
    //     0xae8cf0: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8cf4: str             x16, [SP]
    // 0xae8cf8: r0 = _throwLocalNotInitialized()
    //     0xae8cf8: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8cfc: brk             #0
    // 0xae8d00: r16 = "controller"
    //     0xae8d00: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8d04: str             x16, [SP]
    // 0xae8d08: r0 = _throwLocalNotInitialized()
    //     0xae8d08: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8d0c: brk             #0
    // 0xae8d10: r16 = "controller"
    //     0xae8d10: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8d14: str             x16, [SP]
    // 0xae8d18: r0 = _throwLocalNotInitialized()
    //     0xae8d18: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8d1c: brk             #0
    // 0xae8d20: r16 = "controller"
    //     0xae8d20: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8d24: str             x16, [SP]
    // 0xae8d28: r0 = _throwLocalNotInitialized()
    //     0xae8d28: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8d2c: brk             #0
    // 0xae8d30: r16 = "controller"
    //     0xae8d30: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8d34: str             x16, [SP]
    // 0xae8d38: r0 = _throwLocalNotInitialized()
    //     0xae8d38: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8d3c: brk             #0
    // 0xae8d40: r16 = "controller"
    //     0xae8d40: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8d44: str             x16, [SP]
    // 0xae8d48: r0 = _throwLocalNotInitialized()
    //     0xae8d48: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8d4c: brk             #0
    // 0xae8d50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8d50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8d54: b               #0xae83ec
    // 0xae8d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8d58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8d5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8d60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8d60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8d64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8d64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae8d68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8d68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xae8d6c, size: 0x1b8
    // 0xae8d6c: EnterFrame
    //     0xae8d6c: stp             fp, lr, [SP, #-0x10]!
    //     0xae8d70: mov             fp, SP
    // 0xae8d74: AllocStack(0x48)
    //     0xae8d74: sub             SP, SP, #0x48
    // 0xae8d78: SetupParameters()
    //     0xae8d78: ldr             x0, [fp, #0x18]
    //     0xae8d7c: ldur            w2, [x0, #0x17]
    //     0xae8d80: add             x2, x2, HEAP, lsl #32
    //     0xae8d84: stur            x2, [fp, #-0x28]
    // 0xae8d88: CheckStackOverflow
    //     0xae8d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8d8c: cmp             SP, x16
    //     0xae8d90: b.ls            #0xae8f18
    // 0xae8d94: LoadField: r0 = r2->field_b
    //     0xae8d94: ldur            w0, [x2, #0xb]
    // 0xae8d98: DecompressPointer r0
    //     0xae8d98: add             x0, x0, HEAP, lsl #32
    // 0xae8d9c: LoadField: r1 = r0->field_f
    //     0xae8d9c: ldur            w1, [x0, #0xf]
    // 0xae8da0: DecompressPointer r1
    //     0xae8da0: add             x1, x1, HEAP, lsl #32
    // 0xae8da4: LoadField: r0 = r1->field_b
    //     0xae8da4: ldur            w0, [x1, #0xb]
    // 0xae8da8: DecompressPointer r0
    //     0xae8da8: add             x0, x0, HEAP, lsl #32
    // 0xae8dac: cmp             w0, NULL
    // 0xae8db0: b.eq            #0xae8f20
    // 0xae8db4: LoadField: r3 = r0->field_47
    //     0xae8db4: ldur            w3, [x0, #0x47]
    // 0xae8db8: DecompressPointer r3
    //     0xae8db8: add             x3, x3, HEAP, lsl #32
    // 0xae8dbc: stur            x3, [fp, #-0x20]
    // 0xae8dc0: LoadField: r4 = r0->field_37
    //     0xae8dc0: ldur            w4, [x0, #0x37]
    // 0xae8dc4: DecompressPointer r4
    //     0xae8dc4: add             x4, x4, HEAP, lsl #32
    // 0xae8dc8: stur            x4, [fp, #-0x18]
    // 0xae8dcc: LoadField: r5 = r0->field_3b
    //     0xae8dcc: ldur            w5, [x0, #0x3b]
    // 0xae8dd0: DecompressPointer r5
    //     0xae8dd0: add             x5, x5, HEAP, lsl #32
    // 0xae8dd4: stur            x5, [fp, #-0x10]
    // 0xae8dd8: LoadField: r6 = r0->field_3f
    //     0xae8dd8: ldur            w6, [x0, #0x3f]
    // 0xae8ddc: DecompressPointer r6
    //     0xae8ddc: add             x6, x6, HEAP, lsl #32
    // 0xae8de0: stur            x6, [fp, #-8]
    // 0xae8de4: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xae8de4: ldur            x7, [x0, #0x17]
    // 0xae8de8: r0 = BoxInt64Instr(r7)
    //     0xae8de8: sbfiz           x0, x7, #1, #0x1f
    //     0xae8dec: cmp             x7, x0, asr #1
    //     0xae8df0: b.eq            #0xae8dfc
    //     0xae8df4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae8df8: stur            x7, [x0, #7]
    // 0xae8dfc: r1 = 60
    //     0xae8dfc: movz            x1, #0x3c
    // 0xae8e00: branchIfSmi(r0, 0xae8e0c)
    //     0xae8e00: tbz             w0, #0, #0xae8e0c
    // 0xae8e04: r1 = LoadClassIdInstr(r0)
    //     0xae8e04: ldur            x1, [x0, #-1]
    //     0xae8e08: ubfx            x1, x1, #0xc, #0x14
    // 0xae8e0c: str             x0, [SP]
    // 0xae8e10: mov             x0, x1
    // 0xae8e14: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xae8e14: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xae8e18: r0 = GDT[cid_x0 + 0x2700]()
    //     0xae8e18: movz            x17, #0x2700
    //     0xae8e1c: add             lr, x0, x17
    //     0xae8e20: ldr             lr, [x21, lr, lsl #3]
    //     0xae8e24: blr             lr
    // 0xae8e28: ldur            x2, [fp, #-0x28]
    // 0xae8e2c: stur            x0, [fp, #-0x30]
    // 0xae8e30: LoadField: r1 = r2->field_f
    //     0xae8e30: ldur            w1, [x2, #0xf]
    // 0xae8e34: DecompressPointer r1
    //     0xae8e34: add             x1, x1, HEAP, lsl #32
    // 0xae8e38: r16 = Sentinel
    //     0xae8e38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae8e3c: cmp             w1, w16
    // 0xae8e40: b.eq            #0xae8f08
    // 0xae8e44: ldur            x3, [fp, #-0x20]
    // 0xae8e48: ldur            x4, [fp, #-0x18]
    // 0xae8e4c: ldur            x5, [fp, #-0x10]
    // 0xae8e50: ldur            x6, [fp, #-8]
    // 0xae8e54: str             x1, [SP]
    // 0xae8e58: r4 = 0
    //     0xae8e58: movz            x4, #0
    // 0xae8e5c: ldr             x0, [SP]
    // 0xae8e60: r16 = UnlinkedCall_0x613b5c
    //     0xae8e60: add             x16, PP, #0x58, lsl #12  ; [pp+0x588b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae8e64: add             x16, x16, #0x8b8
    // 0xae8e68: ldp             x5, lr, [x16]
    // 0xae8e6c: blr             lr
    // 0xae8e70: stur            x0, [fp, #-0x38]
    // 0xae8e74: r0 = SingleExchangeProductBottomSheet()
    //     0xae8e74: bl              #0xae8f24  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0xae8e78: mov             x3, x0
    // 0xae8e7c: ldur            x0, [fp, #-0x20]
    // 0xae8e80: stur            x3, [fp, #-0x40]
    // 0xae8e84: StoreField: r3->field_b = r0
    //     0xae8e84: stur            w0, [x3, #0xb]
    // 0xae8e88: ldur            x0, [fp, #-0x18]
    // 0xae8e8c: StoreField: r3->field_f = r0
    //     0xae8e8c: stur            w0, [x3, #0xf]
    // 0xae8e90: ldur            x0, [fp, #-0x10]
    // 0xae8e94: ArrayStore: r3[0] = r0  ; List_4
    //     0xae8e94: stur            w0, [x3, #0x17]
    // 0xae8e98: ldur            x0, [fp, #-8]
    // 0xae8e9c: StoreField: r3->field_13 = r0
    //     0xae8e9c: stur            w0, [x3, #0x13]
    // 0xae8ea0: ldur            x0, [fp, #-0x30]
    // 0xae8ea4: StoreField: r3->field_1b = r0
    //     0xae8ea4: stur            w0, [x3, #0x1b]
    // 0xae8ea8: ldur            x2, [fp, #-0x28]
    // 0xae8eac: r1 = Function '<anonymous closure>':.
    //     0xae8eac: add             x1, PP, #0x58, lsl #12  ; [pp+0x588c8] AnonymousClosure: (0xae9020), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae8eb0: ldr             x1, [x1, #0x8c8]
    // 0xae8eb4: r0 = AllocateClosure()
    //     0xae8eb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae8eb8: mov             x1, x0
    // 0xae8ebc: ldur            x0, [fp, #-0x40]
    // 0xae8ec0: StoreField: r0->field_1f = r1
    //     0xae8ec0: stur            w1, [x0, #0x1f]
    // 0xae8ec4: ldur            x2, [fp, #-0x28]
    // 0xae8ec8: r1 = Function '<anonymous closure>':.
    //     0xae8ec8: add             x1, PP, #0x58, lsl #12  ; [pp+0x588d0] AnonymousClosure: (0xae8f54), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae8ecc: ldr             x1, [x1, #0x8d0]
    // 0xae8ed0: r0 = AllocateClosure()
    //     0xae8ed0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae8ed4: mov             x1, x0
    // 0xae8ed8: ldur            x0, [fp, #-0x40]
    // 0xae8edc: StoreField: r0->field_23 = r1
    //     0xae8edc: stur            w1, [x0, #0x23]
    // 0xae8ee0: r1 = const []
    //     0xae8ee0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56490] List<ProductCustomisation>(0)
    //     0xae8ee4: ldr             x1, [x1, #0x490]
    // 0xae8ee8: StoreField: r0->field_2b = r1
    //     0xae8ee8: stur            w1, [x0, #0x2b]
    // 0xae8eec: r1 = ""
    //     0xae8eec: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae8ef0: StoreField: r0->field_27 = r1
    //     0xae8ef0: stur            w1, [x0, #0x27]
    // 0xae8ef4: ldur            x1, [fp, #-0x38]
    // 0xae8ef8: StoreField: r0->field_2f = r1
    //     0xae8ef8: stur            w1, [x0, #0x2f]
    // 0xae8efc: LeaveFrame
    //     0xae8efc: mov             SP, fp
    //     0xae8f00: ldp             fp, lr, [SP], #0x10
    // 0xae8f04: ret
    //     0xae8f04: ret             
    // 0xae8f08: r16 = "controller"
    //     0xae8f08: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae8f0c: str             x16, [SP]
    // 0xae8f10: r0 = _throwLocalNotInitialized()
    //     0xae8f10: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae8f14: brk             #0
    // 0xae8f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae8f18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae8f1c: b               #0xae8d94
    // 0xae8f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae8f20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xae8f54, size: 0xcc
    // 0xae8f54: EnterFrame
    //     0xae8f54: stp             fp, lr, [SP, #-0x10]!
    //     0xae8f58: mov             fp, SP
    // 0xae8f5c: AllocStack(0x20)
    //     0xae8f5c: sub             SP, SP, #0x20
    // 0xae8f60: SetupParameters()
    //     0xae8f60: ldr             x0, [fp, #0x10]
    //     0xae8f64: ldur            w2, [x0, #0x17]
    //     0xae8f68: add             x2, x2, HEAP, lsl #32
    //     0xae8f6c: stur            x2, [fp, #-8]
    // 0xae8f70: CheckStackOverflow
    //     0xae8f70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae8f74: cmp             SP, x16
    //     0xae8f78: b.ls            #0xae9018
    // 0xae8f7c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae8f7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae8f80: ldr             x0, [x0, #0x1c80]
    //     0xae8f84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae8f88: cmp             w0, w16
    //     0xae8f8c: b.ne            #0xae8f98
    //     0xae8f90: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae8f94: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae8f98: str             NULL, [SP]
    // 0xae8f9c: r4 = const [0x1, 0, 0, 0, null]
    //     0xae8f9c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae8fa0: r0 = GetNavigation.back()
    //     0xae8fa0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xae8fa4: r16 = PreferenceManager
    //     0xae8fa4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xae8fa8: ldr             x16, [x16, #0x878]
    // 0xae8fac: str             x16, [SP]
    // 0xae8fb0: r0 = toString()
    //     0xae8fb0: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xae8fb4: r16 = <PreferenceManager>
    //     0xae8fb4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xae8fb8: ldr             x16, [x16, #0x880]
    // 0xae8fbc: stp             x0, x16, [SP]
    // 0xae8fc0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae8fc0: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae8fc4: r0 = Inst.find()
    //     0xae8fc4: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae8fc8: mov             x1, x0
    // 0xae8fcc: r2 = "token"
    //     0xae8fcc: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xae8fd0: ldr             x2, [x2, #0x958]
    // 0xae8fd4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae8fd4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae8fd8: r0 = getString()
    //     0xae8fd8: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xae8fdc: ldur            x2, [fp, #-8]
    // 0xae8fe0: r1 = Function '<anonymous closure>':.
    //     0xae8fe0: add             x1, PP, #0x58, lsl #12  ; [pp+0x588d8] AnonymousClosure: (0xa48ad4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xae8fe4: ldr             x1, [x1, #0x8d8]
    // 0xae8fe8: stur            x0, [fp, #-8]
    // 0xae8fec: r0 = AllocateClosure()
    //     0xae8fec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae8ff0: r16 = <Null?>
    //     0xae8ff0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xae8ff4: ldur            lr, [fp, #-8]
    // 0xae8ff8: stp             lr, x16, [SP, #8]
    // 0xae8ffc: str             x0, [SP]
    // 0xae9000: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae9000: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae9004: r0 = then()
    //     0xae9004: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xae9008: r0 = Null
    //     0xae9008: mov             x0, NULL
    // 0xae900c: LeaveFrame
    //     0xae900c: mov             SP, fp
    //     0xae9010: ldp             fp, lr, [SP], #0x10
    // 0xae9014: ret
    //     0xae9014: ret             
    // 0xae9018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9018: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae901c: b               #0xae8f7c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xae9020, size: 0xcc
    // 0xae9020: EnterFrame
    //     0xae9020: stp             fp, lr, [SP, #-0x10]!
    //     0xae9024: mov             fp, SP
    // 0xae9028: AllocStack(0x20)
    //     0xae9028: sub             SP, SP, #0x20
    // 0xae902c: SetupParameters()
    //     0xae902c: ldr             x0, [fp, #0x10]
    //     0xae9030: ldur            w2, [x0, #0x17]
    //     0xae9034: add             x2, x2, HEAP, lsl #32
    //     0xae9038: stur            x2, [fp, #-8]
    // 0xae903c: CheckStackOverflow
    //     0xae903c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9040: cmp             SP, x16
    //     0xae9044: b.ls            #0xae90e4
    // 0xae9048: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae9048: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae904c: ldr             x0, [x0, #0x1c80]
    //     0xae9050: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae9054: cmp             w0, w16
    //     0xae9058: b.ne            #0xae9064
    //     0xae905c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae9060: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae9064: str             NULL, [SP]
    // 0xae9068: r4 = const [0x1, 0, 0, 0, null]
    //     0xae9068: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xae906c: r0 = GetNavigation.back()
    //     0xae906c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xae9070: r16 = PreferenceManager
    //     0xae9070: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xae9074: ldr             x16, [x16, #0x878]
    // 0xae9078: str             x16, [SP]
    // 0xae907c: r0 = toString()
    //     0xae907c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xae9080: r16 = <PreferenceManager>
    //     0xae9080: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xae9084: ldr             x16, [x16, #0x880]
    // 0xae9088: stp             x0, x16, [SP]
    // 0xae908c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xae908c: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xae9090: r0 = Inst.find()
    //     0xae9090: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xae9094: mov             x1, x0
    // 0xae9098: r2 = "token"
    //     0xae9098: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xae909c: ldr             x2, [x2, #0x958]
    // 0xae90a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae90a0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae90a4: r0 = getString()
    //     0xae90a4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xae90a8: ldur            x2, [fp, #-8]
    // 0xae90ac: r1 = Function '<anonymous closure>':.
    //     0xae90ac: add             x1, PP, #0x58, lsl #12  ; [pp+0x588e0] AnonymousClosure: (0xae90ec), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xae6d0c)
    //     0xae90b0: ldr             x1, [x1, #0x8e0]
    // 0xae90b4: stur            x0, [fp, #-8]
    // 0xae90b8: r0 = AllocateClosure()
    //     0xae90b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae90bc: r16 = <Null?>
    //     0xae90bc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xae90c0: ldur            lr, [fp, #-8]
    // 0xae90c4: stp             lr, x16, [SP, #8]
    // 0xae90c8: str             x0, [SP]
    // 0xae90cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae90cc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae90d0: r0 = then()
    //     0xae90d0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xae90d4: r0 = Null
    //     0xae90d4: mov             x0, NULL
    // 0xae90d8: LeaveFrame
    //     0xae90d8: mov             SP, fp
    //     0xae90dc: ldp             fp, lr, [SP], #0x10
    // 0xae90e0: ret
    //     0xae90e0: ret             
    // 0xae90e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae90e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae90e8: b               #0xae9048
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xae90ec, size: 0x9f8
    // 0xae90ec: EnterFrame
    //     0xae90ec: stp             fp, lr, [SP, #-0x10]!
    //     0xae90f0: mov             fp, SP
    // 0xae90f4: AllocStack(0x50)
    //     0xae90f4: sub             SP, SP, #0x50
    // 0xae90f8: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xae90f8: stur            NULL, [fp, #-8]
    //     0xae90fc: movz            x0, #0
    //     0xae9100: add             x1, fp, w0, sxtw #2
    //     0xae9104: ldr             x1, [x1, #0x18]
    //     0xae9108: add             x2, fp, w0, sxtw #2
    //     0xae910c: ldr             x2, [x2, #0x10]
    //     0xae9110: stur            x2, [fp, #-0x18]
    //     0xae9114: ldur            w3, [x1, #0x17]
    //     0xae9118: add             x3, x3, HEAP, lsl #32
    //     0xae911c: stur            x3, [fp, #-0x10]
    // 0xae9120: CheckStackOverflow
    //     0xae9120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9124: cmp             SP, x16
    //     0xae9128: b.ls            #0xae9ac8
    // 0xae912c: InitAsync() -> Future<Null?>
    //     0xae912c: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xae9130: bl              #0x6326e0  ; InitAsyncStub
    // 0xae9134: ldur            x0, [fp, #-0x18]
    // 0xae9138: r1 = LoadClassIdInstr(r0)
    //     0xae9138: ldur            x1, [x0, #-1]
    //     0xae913c: ubfx            x1, x1, #0xc, #0x14
    // 0xae9140: r16 = ""
    //     0xae9140: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae9144: stp             x16, x0, [SP]
    // 0xae9148: mov             x0, x1
    // 0xae914c: mov             lr, x0
    // 0xae9150: ldr             lr, [x21, lr, lsl #3]
    // 0xae9154: blr             lr
    // 0xae9158: tbz             w0, #4, #0xae9820
    // 0xae915c: ldur            x2, [fp, #-0x10]
    // 0xae9160: LoadField: r3 = r2->field_b
    //     0xae9160: ldur            w3, [x2, #0xb]
    // 0xae9164: DecompressPointer r3
    //     0xae9164: add             x3, x3, HEAP, lsl #32
    // 0xae9168: stur            x3, [fp, #-0x28]
    // 0xae916c: LoadField: r0 = r3->field_f
    //     0xae916c: ldur            w0, [x3, #0xf]
    // 0xae9170: DecompressPointer r0
    //     0xae9170: add             x0, x0, HEAP, lsl #32
    // 0xae9174: LoadField: r1 = r0->field_b
    //     0xae9174: ldur            w1, [x0, #0xb]
    // 0xae9178: DecompressPointer r1
    //     0xae9178: add             x1, x1, HEAP, lsl #32
    // 0xae917c: cmp             w1, NULL
    // 0xae9180: b.eq            #0xae9ad0
    // 0xae9184: LoadField: r4 = r1->field_f
    //     0xae9184: ldur            w4, [x1, #0xf]
    // 0xae9188: DecompressPointer r4
    //     0xae9188: add             x4, x4, HEAP, lsl #32
    // 0xae918c: stur            x4, [fp, #-0x18]
    // 0xae9190: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xae9190: ldur            x5, [x1, #0x17]
    // 0xae9194: stur            x5, [fp, #-0x20]
    // 0xae9198: LoadField: r0 = r1->field_27
    //     0xae9198: ldur            x0, [x1, #0x27]
    // 0xae919c: mul             x6, x0, x5
    // 0xae91a0: r0 = BoxInt64Instr(r6)
    //     0xae91a0: sbfiz           x0, x6, #1, #0x1f
    //     0xae91a4: cmp             x6, x0, asr #1
    //     0xae91a8: b.eq            #0xae91b4
    //     0xae91ac: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae91b0: stur            x6, [x0, #7]
    // 0xae91b4: stp             x0, NULL, [SP]
    // 0xae91b8: r0 = _Double.fromInteger()
    //     0xae91b8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae91bc: stur            x0, [fp, #-0x30]
    // 0xae91c0: r0 = CheckoutEventData()
    //     0xae91c0: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xae91c4: mov             x2, x0
    // 0xae91c8: ldur            x0, [fp, #-0x30]
    // 0xae91cc: stur            x2, [fp, #-0x38]
    // 0xae91d0: StoreField: r2->field_7 = r0
    //     0xae91d0: stur            w0, [x2, #7]
    // 0xae91d4: ldur            x3, [fp, #-0x20]
    // 0xae91d8: r0 = BoxInt64Instr(r3)
    //     0xae91d8: sbfiz           x0, x3, #1, #0x1f
    //     0xae91dc: cmp             x3, x0, asr #1
    //     0xae91e0: b.eq            #0xae91ec
    //     0xae91e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae91e8: stur            x3, [x0, #7]
    // 0xae91ec: StoreField: r2->field_b = r0
    //     0xae91ec: stur            w0, [x2, #0xb]
    // 0xae91f0: ldur            x0, [fp, #-0x18]
    // 0xae91f4: StoreField: r2->field_f = r0
    //     0xae91f4: stur            w0, [x2, #0xf]
    // 0xae91f8: ldur            x0, [fp, #-0x10]
    // 0xae91fc: LoadField: r1 = r0->field_f
    //     0xae91fc: ldur            w1, [x0, #0xf]
    // 0xae9200: DecompressPointer r1
    //     0xae9200: add             x1, x1, HEAP, lsl #32
    // 0xae9204: r16 = Sentinel
    //     0xae9204: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae9208: cmp             w1, w16
    // 0xae920c: b.eq            #0xae9a68
    // 0xae9210: str             x1, [SP]
    // 0xae9214: r4 = 0
    //     0xae9214: movz            x4, #0
    // 0xae9218: ldr             x0, [SP]
    // 0xae921c: r16 = UnlinkedCall_0x613b5c
    //     0xae921c: add             x16, PP, #0x58, lsl #12  ; [pp+0x588e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae9220: add             x16, x16, #0x8e8
    // 0xae9224: ldp             x5, lr, [x16]
    // 0xae9228: blr             lr
    // 0xae922c: LoadField: r1 = r0->field_1b
    //     0xae922c: ldur            w1, [x0, #0x1b]
    // 0xae9230: DecompressPointer r1
    //     0xae9230: add             x1, x1, HEAP, lsl #32
    // 0xae9234: cmp             w1, NULL
    // 0xae9238: b.ne            #0xae9244
    // 0xae923c: r0 = Null
    //     0xae923c: mov             x0, NULL
    // 0xae9240: b               #0xae925c
    // 0xae9244: LoadField: r0 = r1->field_b
    //     0xae9244: ldur            w0, [x1, #0xb]
    // 0xae9248: cbz             w0, #0xae9254
    // 0xae924c: r1 = false
    //     0xae924c: add             x1, NULL, #0x30  ; false
    // 0xae9250: b               #0xae9258
    // 0xae9254: r1 = true
    //     0xae9254: add             x1, NULL, #0x20  ; true
    // 0xae9258: mov             x0, x1
    // 0xae925c: cmp             w0, NULL
    // 0xae9260: b.eq            #0xae9268
    // 0xae9264: tbz             w0, #4, #0xae9310
    // 0xae9268: ldur            x0, [fp, #-0x10]
    // 0xae926c: LoadField: r1 = r0->field_f
    //     0xae926c: ldur            w1, [x0, #0xf]
    // 0xae9270: DecompressPointer r1
    //     0xae9270: add             x1, x1, HEAP, lsl #32
    // 0xae9274: r16 = Sentinel
    //     0xae9274: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae9278: cmp             w1, w16
    // 0xae927c: b.eq            #0xae9a78
    // 0xae9280: str             x1, [SP]
    // 0xae9284: r4 = 0
    //     0xae9284: movz            x4, #0
    // 0xae9288: ldr             x0, [SP]
    // 0xae928c: r16 = UnlinkedCall_0x613b5c
    //     0xae928c: add             x16, PP, #0x58, lsl #12  ; [pp+0x588f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae9290: add             x16, x16, #0x8f8
    // 0xae9294: ldp             x5, lr, [x16]
    // 0xae9298: blr             lr
    // 0xae929c: LoadField: r1 = r0->field_1b
    //     0xae929c: ldur            w1, [x0, #0x1b]
    // 0xae92a0: DecompressPointer r1
    //     0xae92a0: add             x1, x1, HEAP, lsl #32
    // 0xae92a4: cmp             w1, NULL
    // 0xae92a8: b.ne            #0xae92b4
    // 0xae92ac: r0 = Null
    //     0xae92ac: mov             x0, NULL
    // 0xae92b0: b               #0xae92f8
    // 0xae92b4: r0 = first()
    //     0xae92b4: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xae92b8: cmp             w0, NULL
    // 0xae92bc: b.ne            #0xae92c8
    // 0xae92c0: r0 = Null
    //     0xae92c0: mov             x0, NULL
    // 0xae92c4: b               #0xae92f8
    // 0xae92c8: LoadField: r1 = r0->field_13
    //     0xae92c8: ldur            w1, [x0, #0x13]
    // 0xae92cc: DecompressPointer r1
    //     0xae92cc: add             x1, x1, HEAP, lsl #32
    // 0xae92d0: cmp             w1, NULL
    // 0xae92d4: b.ne            #0xae92e0
    // 0xae92d8: r0 = Null
    //     0xae92d8: mov             x0, NULL
    // 0xae92dc: b               #0xae92f8
    // 0xae92e0: LoadField: r0 = r1->field_7
    //     0xae92e0: ldur            w0, [x1, #7]
    // 0xae92e4: cbz             w0, #0xae92f0
    // 0xae92e8: r1 = false
    //     0xae92e8: add             x1, NULL, #0x30  ; false
    // 0xae92ec: b               #0xae92f4
    // 0xae92f0: r1 = true
    //     0xae92f0: add             x1, NULL, #0x20  ; true
    // 0xae92f4: mov             x0, x1
    // 0xae92f8: cmp             w0, NULL
    // 0xae92fc: b.ne            #0xae930c
    // 0xae9300: ldur            x2, [fp, #-0x10]
    // 0xae9304: ldur            x0, [fp, #-0x28]
    // 0xae9308: b               #0xae95a0
    // 0xae930c: tbnz            w0, #4, #0xae9598
    // 0xae9310: ldur            x0, [fp, #-0x10]
    // 0xae9314: ldur            x1, [fp, #-0x28]
    // 0xae9318: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae9318: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae931c: ldr             x0, [x0, #0x1c80]
    //     0xae9320: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae9324: cmp             w0, w16
    //     0xae9328: b.ne            #0xae9334
    //     0xae932c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae9330: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae9334: r1 = Null
    //     0xae9334: mov             x1, NULL
    // 0xae9338: r2 = 40
    //     0xae9338: movz            x2, #0x28
    // 0xae933c: r0 = AllocateArray()
    //     0xae933c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae9340: mov             x2, x0
    // 0xae9344: stur            x2, [fp, #-0x18]
    // 0xae9348: r16 = "couponCode"
    //     0xae9348: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xae934c: ldr             x16, [x16, #0x310]
    // 0xae9350: StoreField: r2->field_f = r16
    //     0xae9350: stur            w16, [x2, #0xf]
    // 0xae9354: r16 = ""
    //     0xae9354: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae9358: StoreField: r2->field_13 = r16
    //     0xae9358: stur            w16, [x2, #0x13]
    // 0xae935c: r16 = "product_id"
    //     0xae935c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae9360: ldr             x16, [x16, #0x9b8]
    // 0xae9364: ArrayStore: r2[0] = r16  ; List_4
    //     0xae9364: stur            w16, [x2, #0x17]
    // 0xae9368: ldur            x0, [fp, #-0x28]
    // 0xae936c: LoadField: r1 = r0->field_f
    //     0xae936c: ldur            w1, [x0, #0xf]
    // 0xae9370: DecompressPointer r1
    //     0xae9370: add             x1, x1, HEAP, lsl #32
    // 0xae9374: LoadField: r3 = r1->field_b
    //     0xae9374: ldur            w3, [x1, #0xb]
    // 0xae9378: DecompressPointer r3
    //     0xae9378: add             x3, x3, HEAP, lsl #32
    // 0xae937c: cmp             w3, NULL
    // 0xae9380: b.eq            #0xae9ad4
    // 0xae9384: LoadField: r0 = r3->field_f
    //     0xae9384: ldur            w0, [x3, #0xf]
    // 0xae9388: DecompressPointer r0
    //     0xae9388: add             x0, x0, HEAP, lsl #32
    // 0xae938c: StoreField: r2->field_1b = r0
    //     0xae938c: stur            w0, [x2, #0x1b]
    // 0xae9390: r16 = "sku_id"
    //     0xae9390: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae9394: ldr             x16, [x16, #0x498]
    // 0xae9398: StoreField: r2->field_1f = r16
    //     0xae9398: stur            w16, [x2, #0x1f]
    // 0xae939c: LoadField: r0 = r3->field_13
    //     0xae939c: ldur            w0, [x3, #0x13]
    // 0xae93a0: DecompressPointer r0
    //     0xae93a0: add             x0, x0, HEAP, lsl #32
    // 0xae93a4: StoreField: r2->field_23 = r0
    //     0xae93a4: stur            w0, [x2, #0x23]
    // 0xae93a8: r16 = "quantity"
    //     0xae93a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae93ac: ldr             x16, [x16, #0x428]
    // 0xae93b0: StoreField: r2->field_27 = r16
    //     0xae93b0: stur            w16, [x2, #0x27]
    // 0xae93b4: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae93b4: ldur            x4, [x3, #0x17]
    // 0xae93b8: r0 = BoxInt64Instr(r4)
    //     0xae93b8: sbfiz           x0, x4, #1, #0x1f
    //     0xae93bc: cmp             x4, x0, asr #1
    //     0xae93c0: b.eq            #0xae93cc
    //     0xae93c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae93c8: stur            x4, [x0, #7]
    // 0xae93cc: mov             x1, x2
    // 0xae93d0: ArrayStore: r1[7] = r0  ; List_4
    //     0xae93d0: add             x25, x1, #0x2b
    //     0xae93d4: str             w0, [x25]
    //     0xae93d8: tbz             w0, #0, #0xae93f4
    //     0xae93dc: ldurb           w16, [x1, #-1]
    //     0xae93e0: ldurb           w17, [x0, #-1]
    //     0xae93e4: and             x16, x17, x16, lsr #2
    //     0xae93e8: tst             x16, HEAP, lsr #32
    //     0xae93ec: b.eq            #0xae93f4
    //     0xae93f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae93f4: r16 = "checkout_event_data"
    //     0xae93f4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae93f8: ldr             x16, [x16, #0xd50]
    // 0xae93fc: StoreField: r2->field_2f = r16
    //     0xae93fc: stur            w16, [x2, #0x2f]
    // 0xae9400: mov             x1, x2
    // 0xae9404: ldur            x0, [fp, #-0x38]
    // 0xae9408: ArrayStore: r1[9] = r0  ; List_4
    //     0xae9408: add             x25, x1, #0x33
    //     0xae940c: str             w0, [x25]
    //     0xae9410: tbz             w0, #0, #0xae942c
    //     0xae9414: ldurb           w16, [x1, #-1]
    //     0xae9418: ldurb           w17, [x0, #-1]
    //     0xae941c: and             x16, x17, x16, lsr #2
    //     0xae9420: tst             x16, HEAP, lsr #32
    //     0xae9424: b.eq            #0xae942c
    //     0xae9428: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae942c: r16 = "previousScreenSource"
    //     0xae942c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae9430: ldr             x16, [x16, #0x448]
    // 0xae9434: StoreField: r2->field_37 = r16
    //     0xae9434: stur            w16, [x2, #0x37]
    // 0xae9438: r16 = "product_page"
    //     0xae9438: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae943c: ldr             x16, [x16, #0x480]
    // 0xae9440: StoreField: r2->field_3b = r16
    //     0xae9440: stur            w16, [x2, #0x3b]
    // 0xae9444: r16 = "is_skipped_address"
    //     0xae9444: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae9448: ldr             x16, [x16, #0xb80]
    // 0xae944c: StoreField: r2->field_3f = r16
    //     0xae944c: stur            w16, [x2, #0x3f]
    // 0xae9450: r16 = false
    //     0xae9450: add             x16, NULL, #0x30  ; false
    // 0xae9454: StoreField: r2->field_43 = r16
    //     0xae9454: stur            w16, [x2, #0x43]
    // 0xae9458: r16 = "coming_from"
    //     0xae9458: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae945c: ldr             x16, [x16, #0x328]
    // 0xae9460: StoreField: r2->field_47 = r16
    //     0xae9460: stur            w16, [x2, #0x47]
    // 0xae9464: LoadField: r0 = r3->field_2f
    //     0xae9464: ldur            w0, [x3, #0x2f]
    // 0xae9468: DecompressPointer r0
    //     0xae9468: add             x0, x0, HEAP, lsl #32
    // 0xae946c: mov             x1, x2
    // 0xae9470: ArrayStore: r1[15] = r0  ; List_4
    //     0xae9470: add             x25, x1, #0x4b
    //     0xae9474: str             w0, [x25]
    //     0xae9478: tbz             w0, #0, #0xae9494
    //     0xae947c: ldurb           w16, [x1, #-1]
    //     0xae9480: ldurb           w17, [x0, #-1]
    //     0xae9484: and             x16, x17, x16, lsr #2
    //     0xae9488: tst             x16, HEAP, lsr #32
    //     0xae948c: b.eq            #0xae9494
    //     0xae9490: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9494: r16 = "checkout_id"
    //     0xae9494: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xae9498: ldr             x16, [x16, #0xb88]
    // 0xae949c: StoreField: r2->field_4f = r16
    //     0xae949c: stur            w16, [x2, #0x4f]
    // 0xae94a0: ldur            x0, [fp, #-0x10]
    // 0xae94a4: LoadField: r1 = r0->field_f
    //     0xae94a4: ldur            w1, [x0, #0xf]
    // 0xae94a8: DecompressPointer r1
    //     0xae94a8: add             x1, x1, HEAP, lsl #32
    // 0xae94ac: r16 = Sentinel
    //     0xae94ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae94b0: cmp             w1, w16
    // 0xae94b4: b.eq            #0xae9a88
    // 0xae94b8: str             x1, [SP]
    // 0xae94bc: r4 = 0
    //     0xae94bc: movz            x4, #0
    // 0xae94c0: ldr             x0, [SP]
    // 0xae94c4: r16 = UnlinkedCall_0x613b5c
    //     0xae94c4: add             x16, PP, #0x58, lsl #12  ; [pp+0x58908] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae94c8: add             x16, x16, #0x908
    // 0xae94cc: ldp             x5, lr, [x16]
    // 0xae94d0: blr             lr
    // 0xae94d4: ldur            x1, [fp, #-0x18]
    // 0xae94d8: ArrayStore: r1[17] = r0  ; List_4
    //     0xae94d8: add             x25, x1, #0x53
    //     0xae94dc: str             w0, [x25]
    //     0xae94e0: tbz             w0, #0, #0xae94fc
    //     0xae94e4: ldurb           w16, [x1, #-1]
    //     0xae94e8: ldurb           w17, [x0, #-1]
    //     0xae94ec: and             x16, x17, x16, lsr #2
    //     0xae94f0: tst             x16, HEAP, lsr #32
    //     0xae94f4: b.eq            #0xae94fc
    //     0xae94f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae94fc: ldur            x1, [fp, #-0x18]
    // 0xae9500: r16 = "user_data"
    //     0xae9500: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xae9504: ldr             x16, [x16, #0x58]
    // 0xae9508: StoreField: r1->field_57 = r16
    //     0xae9508: stur            w16, [x1, #0x57]
    // 0xae950c: ldur            x2, [fp, #-0x10]
    // 0xae9510: LoadField: r0 = r2->field_f
    //     0xae9510: ldur            w0, [x2, #0xf]
    // 0xae9514: DecompressPointer r0
    //     0xae9514: add             x0, x0, HEAP, lsl #32
    // 0xae9518: r16 = Sentinel
    //     0xae9518: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae951c: cmp             w0, w16
    // 0xae9520: b.eq            #0xae9a98
    // 0xae9524: str             x0, [SP]
    // 0xae9528: r4 = 0
    //     0xae9528: movz            x4, #0
    // 0xae952c: ldr             x0, [SP]
    // 0xae9530: r16 = UnlinkedCall_0x613b5c
    //     0xae9530: add             x16, PP, #0x58, lsl #12  ; [pp+0x58918] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae9534: add             x16, x16, #0x918
    // 0xae9538: ldp             x5, lr, [x16]
    // 0xae953c: blr             lr
    // 0xae9540: ldur            x1, [fp, #-0x18]
    // 0xae9544: ArrayStore: r1[19] = r0  ; List_4
    //     0xae9544: add             x25, x1, #0x5b
    //     0xae9548: str             w0, [x25]
    //     0xae954c: tbz             w0, #0, #0xae9568
    //     0xae9550: ldurb           w16, [x1, #-1]
    //     0xae9554: ldurb           w17, [x0, #-1]
    //     0xae9558: and             x16, x17, x16, lsr #2
    //     0xae955c: tst             x16, HEAP, lsr #32
    //     0xae9560: b.eq            #0xae9568
    //     0xae9564: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9568: r16 = <String, dynamic>
    //     0xae9568: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xae956c: ldur            lr, [fp, #-0x18]
    // 0xae9570: stp             lr, x16, [SP]
    // 0xae9574: r0 = Map._fromLiteral()
    //     0xae9574: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae9578: r16 = "/checkout_request_address_page"
    //     0xae9578: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xae957c: ldr             x16, [x16, #0x9e8]
    // 0xae9580: stp             x16, NULL, [SP, #8]
    // 0xae9584: str             x0, [SP]
    // 0xae9588: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae9588: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae958c: ldr             x4, [x4, #0x438]
    // 0xae9590: r0 = GetNavigation.toNamed()
    //     0xae9590: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae9594: b               #0xae9a60
    // 0xae9598: ldur            x2, [fp, #-0x10]
    // 0xae959c: ldur            x0, [fp, #-0x28]
    // 0xae95a0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae95a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae95a4: ldr             x0, [x0, #0x1c80]
    //     0xae95a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae95ac: cmp             w0, w16
    //     0xae95b0: b.ne            #0xae95bc
    //     0xae95b4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae95b8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae95bc: r1 = Null
    //     0xae95bc: mov             x1, NULL
    // 0xae95c0: r2 = 40
    //     0xae95c0: movz            x2, #0x28
    // 0xae95c4: r0 = AllocateArray()
    //     0xae95c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae95c8: mov             x2, x0
    // 0xae95cc: stur            x2, [fp, #-0x18]
    // 0xae95d0: r16 = "couponCode"
    //     0xae95d0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xae95d4: ldr             x16, [x16, #0x310]
    // 0xae95d8: StoreField: r2->field_f = r16
    //     0xae95d8: stur            w16, [x2, #0xf]
    // 0xae95dc: r16 = ""
    //     0xae95dc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae95e0: StoreField: r2->field_13 = r16
    //     0xae95e0: stur            w16, [x2, #0x13]
    // 0xae95e4: r16 = "product_id"
    //     0xae95e4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae95e8: ldr             x16, [x16, #0x9b8]
    // 0xae95ec: ArrayStore: r2[0] = r16  ; List_4
    //     0xae95ec: stur            w16, [x2, #0x17]
    // 0xae95f0: ldur            x0, [fp, #-0x28]
    // 0xae95f4: LoadField: r1 = r0->field_f
    //     0xae95f4: ldur            w1, [x0, #0xf]
    // 0xae95f8: DecompressPointer r1
    //     0xae95f8: add             x1, x1, HEAP, lsl #32
    // 0xae95fc: LoadField: r3 = r1->field_b
    //     0xae95fc: ldur            w3, [x1, #0xb]
    // 0xae9600: DecompressPointer r3
    //     0xae9600: add             x3, x3, HEAP, lsl #32
    // 0xae9604: cmp             w3, NULL
    // 0xae9608: b.eq            #0xae9ad8
    // 0xae960c: LoadField: r0 = r3->field_f
    //     0xae960c: ldur            w0, [x3, #0xf]
    // 0xae9610: DecompressPointer r0
    //     0xae9610: add             x0, x0, HEAP, lsl #32
    // 0xae9614: StoreField: r2->field_1b = r0
    //     0xae9614: stur            w0, [x2, #0x1b]
    // 0xae9618: r16 = "sku_id"
    //     0xae9618: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae961c: ldr             x16, [x16, #0x498]
    // 0xae9620: StoreField: r2->field_1f = r16
    //     0xae9620: stur            w16, [x2, #0x1f]
    // 0xae9624: LoadField: r0 = r3->field_13
    //     0xae9624: ldur            w0, [x3, #0x13]
    // 0xae9628: DecompressPointer r0
    //     0xae9628: add             x0, x0, HEAP, lsl #32
    // 0xae962c: StoreField: r2->field_23 = r0
    //     0xae962c: stur            w0, [x2, #0x23]
    // 0xae9630: r16 = "quantity"
    //     0xae9630: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae9634: ldr             x16, [x16, #0x428]
    // 0xae9638: StoreField: r2->field_27 = r16
    //     0xae9638: stur            w16, [x2, #0x27]
    // 0xae963c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae963c: ldur            x4, [x3, #0x17]
    // 0xae9640: r0 = BoxInt64Instr(r4)
    //     0xae9640: sbfiz           x0, x4, #1, #0x1f
    //     0xae9644: cmp             x4, x0, asr #1
    //     0xae9648: b.eq            #0xae9654
    //     0xae964c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae9650: stur            x4, [x0, #7]
    // 0xae9654: mov             x1, x2
    // 0xae9658: ArrayStore: r1[7] = r0  ; List_4
    //     0xae9658: add             x25, x1, #0x2b
    //     0xae965c: str             w0, [x25]
    //     0xae9660: tbz             w0, #0, #0xae967c
    //     0xae9664: ldurb           w16, [x1, #-1]
    //     0xae9668: ldurb           w17, [x0, #-1]
    //     0xae966c: and             x16, x17, x16, lsr #2
    //     0xae9670: tst             x16, HEAP, lsr #32
    //     0xae9674: b.eq            #0xae967c
    //     0xae9678: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae967c: r16 = "checkout_event_data"
    //     0xae967c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae9680: ldr             x16, [x16, #0xd50]
    // 0xae9684: StoreField: r2->field_2f = r16
    //     0xae9684: stur            w16, [x2, #0x2f]
    // 0xae9688: mov             x1, x2
    // 0xae968c: ldur            x0, [fp, #-0x38]
    // 0xae9690: ArrayStore: r1[9] = r0  ; List_4
    //     0xae9690: add             x25, x1, #0x33
    //     0xae9694: str             w0, [x25]
    //     0xae9698: tbz             w0, #0, #0xae96b4
    //     0xae969c: ldurb           w16, [x1, #-1]
    //     0xae96a0: ldurb           w17, [x0, #-1]
    //     0xae96a4: and             x16, x17, x16, lsr #2
    //     0xae96a8: tst             x16, HEAP, lsr #32
    //     0xae96ac: b.eq            #0xae96b4
    //     0xae96b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae96b4: r16 = "previousScreenSource"
    //     0xae96b4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae96b8: ldr             x16, [x16, #0x448]
    // 0xae96bc: StoreField: r2->field_37 = r16
    //     0xae96bc: stur            w16, [x2, #0x37]
    // 0xae96c0: r16 = "product_page"
    //     0xae96c0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae96c4: ldr             x16, [x16, #0x480]
    // 0xae96c8: StoreField: r2->field_3b = r16
    //     0xae96c8: stur            w16, [x2, #0x3b]
    // 0xae96cc: r16 = "is_skipped_address"
    //     0xae96cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae96d0: ldr             x16, [x16, #0xb80]
    // 0xae96d4: StoreField: r2->field_3f = r16
    //     0xae96d4: stur            w16, [x2, #0x3f]
    // 0xae96d8: r16 = true
    //     0xae96d8: add             x16, NULL, #0x20  ; true
    // 0xae96dc: StoreField: r2->field_43 = r16
    //     0xae96dc: stur            w16, [x2, #0x43]
    // 0xae96e0: r16 = "coming_from"
    //     0xae96e0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae96e4: ldr             x16, [x16, #0x328]
    // 0xae96e8: StoreField: r2->field_47 = r16
    //     0xae96e8: stur            w16, [x2, #0x47]
    // 0xae96ec: LoadField: r0 = r3->field_2f
    //     0xae96ec: ldur            w0, [x3, #0x2f]
    // 0xae96f0: DecompressPointer r0
    //     0xae96f0: add             x0, x0, HEAP, lsl #32
    // 0xae96f4: mov             x1, x2
    // 0xae96f8: ArrayStore: r1[15] = r0  ; List_4
    //     0xae96f8: add             x25, x1, #0x4b
    //     0xae96fc: str             w0, [x25]
    //     0xae9700: tbz             w0, #0, #0xae971c
    //     0xae9704: ldurb           w16, [x1, #-1]
    //     0xae9708: ldurb           w17, [x0, #-1]
    //     0xae970c: and             x16, x17, x16, lsr #2
    //     0xae9710: tst             x16, HEAP, lsr #32
    //     0xae9714: b.eq            #0xae971c
    //     0xae9718: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae971c: r16 = "checkout_id"
    //     0xae971c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xae9720: ldr             x16, [x16, #0xb88]
    // 0xae9724: StoreField: r2->field_4f = r16
    //     0xae9724: stur            w16, [x2, #0x4f]
    // 0xae9728: ldur            x0, [fp, #-0x10]
    // 0xae972c: LoadField: r1 = r0->field_f
    //     0xae972c: ldur            w1, [x0, #0xf]
    // 0xae9730: DecompressPointer r1
    //     0xae9730: add             x1, x1, HEAP, lsl #32
    // 0xae9734: r16 = Sentinel
    //     0xae9734: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae9738: cmp             w1, w16
    // 0xae973c: b.eq            #0xae9aa8
    // 0xae9740: str             x1, [SP]
    // 0xae9744: r4 = 0
    //     0xae9744: movz            x4, #0
    // 0xae9748: ldr             x0, [SP]
    // 0xae974c: r16 = UnlinkedCall_0x613b5c
    //     0xae974c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58928] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae9750: add             x16, x16, #0x928
    // 0xae9754: ldp             x5, lr, [x16]
    // 0xae9758: blr             lr
    // 0xae975c: ldur            x1, [fp, #-0x18]
    // 0xae9760: ArrayStore: r1[17] = r0  ; List_4
    //     0xae9760: add             x25, x1, #0x53
    //     0xae9764: str             w0, [x25]
    //     0xae9768: tbz             w0, #0, #0xae9784
    //     0xae976c: ldurb           w16, [x1, #-1]
    //     0xae9770: ldurb           w17, [x0, #-1]
    //     0xae9774: and             x16, x17, x16, lsr #2
    //     0xae9778: tst             x16, HEAP, lsr #32
    //     0xae977c: b.eq            #0xae9784
    //     0xae9780: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9784: ldur            x1, [fp, #-0x18]
    // 0xae9788: r16 = "user_data"
    //     0xae9788: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xae978c: ldr             x16, [x16, #0x58]
    // 0xae9790: StoreField: r1->field_57 = r16
    //     0xae9790: stur            w16, [x1, #0x57]
    // 0xae9794: ldur            x0, [fp, #-0x10]
    // 0xae9798: LoadField: r2 = r0->field_f
    //     0xae9798: ldur            w2, [x0, #0xf]
    // 0xae979c: DecompressPointer r2
    //     0xae979c: add             x2, x2, HEAP, lsl #32
    // 0xae97a0: r16 = Sentinel
    //     0xae97a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae97a4: cmp             w2, w16
    // 0xae97a8: b.eq            #0xae9ab8
    // 0xae97ac: str             x2, [SP]
    // 0xae97b0: r4 = 0
    //     0xae97b0: movz            x4, #0
    // 0xae97b4: ldr             x0, [SP]
    // 0xae97b8: r16 = UnlinkedCall_0x613b5c
    //     0xae97b8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58938] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae97bc: add             x16, x16, #0x938
    // 0xae97c0: ldp             x5, lr, [x16]
    // 0xae97c4: blr             lr
    // 0xae97c8: ldur            x1, [fp, #-0x18]
    // 0xae97cc: ArrayStore: r1[19] = r0  ; List_4
    //     0xae97cc: add             x25, x1, #0x5b
    //     0xae97d0: str             w0, [x25]
    //     0xae97d4: tbz             w0, #0, #0xae97f0
    //     0xae97d8: ldurb           w16, [x1, #-1]
    //     0xae97dc: ldurb           w17, [x0, #-1]
    //     0xae97e0: and             x16, x17, x16, lsr #2
    //     0xae97e4: tst             x16, HEAP, lsr #32
    //     0xae97e8: b.eq            #0xae97f0
    //     0xae97ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae97f0: r16 = <String, dynamic>
    //     0xae97f0: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xae97f4: ldur            lr, [fp, #-0x18]
    // 0xae97f8: stp             lr, x16, [SP]
    // 0xae97fc: r0 = Map._fromLiteral()
    //     0xae97fc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae9800: r16 = "/checkout_order_summary_page"
    //     0xae9800: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xae9804: ldr             x16, [x16, #0x9d8]
    // 0xae9808: stp             x16, NULL, [SP, #8]
    // 0xae980c: str             x0, [SP]
    // 0xae9810: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae9810: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae9814: ldr             x4, [x4, #0x438]
    // 0xae9818: r0 = GetNavigation.toNamed()
    //     0xae9818: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae981c: b               #0xae9a60
    // 0xae9820: ldur            x0, [fp, #-0x10]
    // 0xae9824: LoadField: r2 = r0->field_b
    //     0xae9824: ldur            w2, [x0, #0xb]
    // 0xae9828: DecompressPointer r2
    //     0xae9828: add             x2, x2, HEAP, lsl #32
    // 0xae982c: stur            x2, [fp, #-0x18]
    // 0xae9830: LoadField: r0 = r2->field_f
    //     0xae9830: ldur            w0, [x2, #0xf]
    // 0xae9834: DecompressPointer r0
    //     0xae9834: add             x0, x0, HEAP, lsl #32
    // 0xae9838: LoadField: r1 = r0->field_b
    //     0xae9838: ldur            w1, [x0, #0xb]
    // 0xae983c: DecompressPointer r1
    //     0xae983c: add             x1, x1, HEAP, lsl #32
    // 0xae9840: cmp             w1, NULL
    // 0xae9844: b.eq            #0xae9adc
    // 0xae9848: LoadField: r3 = r1->field_f
    //     0xae9848: ldur            w3, [x1, #0xf]
    // 0xae984c: DecompressPointer r3
    //     0xae984c: add             x3, x3, HEAP, lsl #32
    // 0xae9850: stur            x3, [fp, #-0x10]
    // 0xae9854: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xae9854: ldur            x4, [x1, #0x17]
    // 0xae9858: stur            x4, [fp, #-0x20]
    // 0xae985c: LoadField: r0 = r1->field_27
    //     0xae985c: ldur            x0, [x1, #0x27]
    // 0xae9860: mul             x5, x0, x4
    // 0xae9864: r0 = BoxInt64Instr(r5)
    //     0xae9864: sbfiz           x0, x5, #1, #0x1f
    //     0xae9868: cmp             x5, x0, asr #1
    //     0xae986c: b.eq            #0xae9878
    //     0xae9870: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae9874: stur            x5, [x0, #7]
    // 0xae9878: stp             x0, NULL, [SP]
    // 0xae987c: r0 = _Double.fromInteger()
    //     0xae987c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xae9880: stur            x0, [fp, #-0x28]
    // 0xae9884: r0 = CheckoutEventData()
    //     0xae9884: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xae9888: mov             x2, x0
    // 0xae988c: ldur            x0, [fp, #-0x28]
    // 0xae9890: stur            x2, [fp, #-0x30]
    // 0xae9894: StoreField: r2->field_7 = r0
    //     0xae9894: stur            w0, [x2, #7]
    // 0xae9898: ldur            x3, [fp, #-0x20]
    // 0xae989c: r0 = BoxInt64Instr(r3)
    //     0xae989c: sbfiz           x0, x3, #1, #0x1f
    //     0xae98a0: cmp             x3, x0, asr #1
    //     0xae98a4: b.eq            #0xae98b0
    //     0xae98a8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae98ac: stur            x3, [x0, #7]
    // 0xae98b0: StoreField: r2->field_b = r0
    //     0xae98b0: stur            w0, [x2, #0xb]
    // 0xae98b4: ldur            x0, [fp, #-0x10]
    // 0xae98b8: StoreField: r2->field_f = r0
    //     0xae98b8: stur            w0, [x2, #0xf]
    // 0xae98bc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xae98bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae98c0: ldr             x0, [x0, #0x1c80]
    //     0xae98c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae98c8: cmp             w0, w16
    //     0xae98cc: b.ne            #0xae98d8
    //     0xae98d0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xae98d4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xae98d8: r1 = Null
    //     0xae98d8: mov             x1, NULL
    // 0xae98dc: r2 = 32
    //     0xae98dc: movz            x2, #0x20
    // 0xae98e0: r0 = AllocateArray()
    //     0xae98e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae98e4: mov             x2, x0
    // 0xae98e8: r16 = "couponCode"
    //     0xae98e8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xae98ec: ldr             x16, [x16, #0x310]
    // 0xae98f0: StoreField: r2->field_f = r16
    //     0xae98f0: stur            w16, [x2, #0xf]
    // 0xae98f4: r16 = ""
    //     0xae98f4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae98f8: StoreField: r2->field_13 = r16
    //     0xae98f8: stur            w16, [x2, #0x13]
    // 0xae98fc: r16 = "product_id"
    //     0xae98fc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xae9900: ldr             x16, [x16, #0x9b8]
    // 0xae9904: ArrayStore: r2[0] = r16  ; List_4
    //     0xae9904: stur            w16, [x2, #0x17]
    // 0xae9908: ldur            x0, [fp, #-0x18]
    // 0xae990c: LoadField: r1 = r0->field_f
    //     0xae990c: ldur            w1, [x0, #0xf]
    // 0xae9910: DecompressPointer r1
    //     0xae9910: add             x1, x1, HEAP, lsl #32
    // 0xae9914: LoadField: r3 = r1->field_b
    //     0xae9914: ldur            w3, [x1, #0xb]
    // 0xae9918: DecompressPointer r3
    //     0xae9918: add             x3, x3, HEAP, lsl #32
    // 0xae991c: cmp             w3, NULL
    // 0xae9920: b.eq            #0xae9ae0
    // 0xae9924: LoadField: r0 = r3->field_f
    //     0xae9924: ldur            w0, [x3, #0xf]
    // 0xae9928: DecompressPointer r0
    //     0xae9928: add             x0, x0, HEAP, lsl #32
    // 0xae992c: StoreField: r2->field_1b = r0
    //     0xae992c: stur            w0, [x2, #0x1b]
    // 0xae9930: r16 = "sku_id"
    //     0xae9930: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xae9934: ldr             x16, [x16, #0x498]
    // 0xae9938: StoreField: r2->field_1f = r16
    //     0xae9938: stur            w16, [x2, #0x1f]
    // 0xae993c: LoadField: r0 = r3->field_13
    //     0xae993c: ldur            w0, [x3, #0x13]
    // 0xae9940: DecompressPointer r0
    //     0xae9940: add             x0, x0, HEAP, lsl #32
    // 0xae9944: StoreField: r2->field_23 = r0
    //     0xae9944: stur            w0, [x2, #0x23]
    // 0xae9948: r16 = "quantity"
    //     0xae9948: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xae994c: ldr             x16, [x16, #0x428]
    // 0xae9950: StoreField: r2->field_27 = r16
    //     0xae9950: stur            w16, [x2, #0x27]
    // 0xae9954: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xae9954: ldur            x4, [x3, #0x17]
    // 0xae9958: r0 = BoxInt64Instr(r4)
    //     0xae9958: sbfiz           x0, x4, #1, #0x1f
    //     0xae995c: cmp             x4, x0, asr #1
    //     0xae9960: b.eq            #0xae996c
    //     0xae9964: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae9968: stur            x4, [x0, #7]
    // 0xae996c: mov             x1, x2
    // 0xae9970: ArrayStore: r1[7] = r0  ; List_4
    //     0xae9970: add             x25, x1, #0x2b
    //     0xae9974: str             w0, [x25]
    //     0xae9978: tbz             w0, #0, #0xae9994
    //     0xae997c: ldurb           w16, [x1, #-1]
    //     0xae9980: ldurb           w17, [x0, #-1]
    //     0xae9984: and             x16, x17, x16, lsr #2
    //     0xae9988: tst             x16, HEAP, lsr #32
    //     0xae998c: b.eq            #0xae9994
    //     0xae9990: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9994: r16 = "checkout_event_data"
    //     0xae9994: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xae9998: ldr             x16, [x16, #0xd50]
    // 0xae999c: StoreField: r2->field_2f = r16
    //     0xae999c: stur            w16, [x2, #0x2f]
    // 0xae99a0: mov             x1, x2
    // 0xae99a4: ldur            x0, [fp, #-0x30]
    // 0xae99a8: ArrayStore: r1[9] = r0  ; List_4
    //     0xae99a8: add             x25, x1, #0x33
    //     0xae99ac: str             w0, [x25]
    //     0xae99b0: tbz             w0, #0, #0xae99cc
    //     0xae99b4: ldurb           w16, [x1, #-1]
    //     0xae99b8: ldurb           w17, [x0, #-1]
    //     0xae99bc: and             x16, x17, x16, lsr #2
    //     0xae99c0: tst             x16, HEAP, lsr #32
    //     0xae99c4: b.eq            #0xae99cc
    //     0xae99c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae99cc: r16 = "previousScreenSource"
    //     0xae99cc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xae99d0: ldr             x16, [x16, #0x448]
    // 0xae99d4: StoreField: r2->field_37 = r16
    //     0xae99d4: stur            w16, [x2, #0x37]
    // 0xae99d8: r16 = "product_page"
    //     0xae99d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xae99dc: ldr             x16, [x16, #0x480]
    // 0xae99e0: StoreField: r2->field_3b = r16
    //     0xae99e0: stur            w16, [x2, #0x3b]
    // 0xae99e4: r16 = "coming_from"
    //     0xae99e4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xae99e8: ldr             x16, [x16, #0x328]
    // 0xae99ec: StoreField: r2->field_3f = r16
    //     0xae99ec: stur            w16, [x2, #0x3f]
    // 0xae99f0: LoadField: r0 = r3->field_2f
    //     0xae99f0: ldur            w0, [x3, #0x2f]
    // 0xae99f4: DecompressPointer r0
    //     0xae99f4: add             x0, x0, HEAP, lsl #32
    // 0xae99f8: mov             x1, x2
    // 0xae99fc: ArrayStore: r1[13] = r0  ; List_4
    //     0xae99fc: add             x25, x1, #0x43
    //     0xae9a00: str             w0, [x25]
    //     0xae9a04: tbz             w0, #0, #0xae9a20
    //     0xae9a08: ldurb           w16, [x1, #-1]
    //     0xae9a0c: ldurb           w17, [x0, #-1]
    //     0xae9a10: and             x16, x17, x16, lsr #2
    //     0xae9a14: tst             x16, HEAP, lsr #32
    //     0xae9a18: b.eq            #0xae9a20
    //     0xae9a1c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9a20: r16 = "is_skipped_address"
    //     0xae9a20: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xae9a24: ldr             x16, [x16, #0xb80]
    // 0xae9a28: StoreField: r2->field_47 = r16
    //     0xae9a28: stur            w16, [x2, #0x47]
    // 0xae9a2c: r16 = true
    //     0xae9a2c: add             x16, NULL, #0x20  ; true
    // 0xae9a30: StoreField: r2->field_4b = r16
    //     0xae9a30: stur            w16, [x2, #0x4b]
    // 0xae9a34: r16 = <String, Object?>
    //     0xae9a34: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xae9a38: ldr             x16, [x16, #0xc28]
    // 0xae9a3c: stp             x2, x16, [SP]
    // 0xae9a40: r0 = Map._fromLiteral()
    //     0xae9a40: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xae9a44: r16 = "/checkout_request_number_page"
    //     0xae9a44: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xae9a48: ldr             x16, [x16, #0x9f8]
    // 0xae9a4c: stp             x16, NULL, [SP, #8]
    // 0xae9a50: str             x0, [SP]
    // 0xae9a54: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xae9a54: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xae9a58: ldr             x4, [x4, #0x438]
    // 0xae9a5c: r0 = GetNavigation.toNamed()
    //     0xae9a5c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xae9a60: r0 = Null
    //     0xae9a60: mov             x0, NULL
    // 0xae9a64: r0 = ReturnAsyncNotFuture()
    //     0xae9a64: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xae9a68: r16 = "controller"
    //     0xae9a68: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9a6c: str             x16, [SP]
    // 0xae9a70: r0 = _throwLocalNotInitialized()
    //     0xae9a70: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9a74: brk             #0
    // 0xae9a78: r16 = "controller"
    //     0xae9a78: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9a7c: str             x16, [SP]
    // 0xae9a80: r0 = _throwLocalNotInitialized()
    //     0xae9a80: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9a84: brk             #0
    // 0xae9a88: r16 = "controller"
    //     0xae9a88: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9a8c: str             x16, [SP]
    // 0xae9a90: r0 = _throwLocalNotInitialized()
    //     0xae9a90: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9a94: brk             #0
    // 0xae9a98: r16 = "controller"
    //     0xae9a98: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9a9c: str             x16, [SP]
    // 0xae9aa0: r0 = _throwLocalNotInitialized()
    //     0xae9aa0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9aa4: brk             #0
    // 0xae9aa8: r16 = "controller"
    //     0xae9aa8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9aac: str             x16, [SP]
    // 0xae9ab0: r0 = _throwLocalNotInitialized()
    //     0xae9ab0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9ab4: brk             #0
    // 0xae9ab8: r16 = "controller"
    //     0xae9ab8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xae9abc: str             x16, [SP]
    // 0xae9ac0: r0 = _throwLocalNotInitialized()
    //     0xae9ac0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xae9ac4: brk             #0
    // 0xae9ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae9ac8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae9acc: b               #0xae912c
    // 0xae9ad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae9ad0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae9ad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae9ad4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae9ad8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae9ad8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae9adc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae9adc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae9ae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae9ae0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4161, size: 0x50, field offset: 0xc
//   const constructor, 
class CustomizedBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7da88, size: 0x24
    // 0xc7da88: EnterFrame
    //     0xc7da88: stp             fp, lr, [SP, #-0x10]!
    //     0xc7da8c: mov             fp, SP
    // 0xc7da90: mov             x0, x1
    // 0xc7da94: r1 = <CustomizedBottomSheet>
    //     0xc7da94: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c30] TypeArguments: <CustomizedBottomSheet>
    //     0xc7da98: ldr             x1, [x1, #0xc30]
    // 0xc7da9c: r0 = _CustomizedBottomSheetState()
    //     0xc7da9c: bl              #0xc7daac  ; Allocate_CustomizedBottomSheetStateStub -> _CustomizedBottomSheetState (size=0x14)
    // 0xc7daa0: LeaveFrame
    //     0xc7daa0: mov             SP, fp
    //     0xc7daa4: ldp             fp, lr, [SP], #0x10
    // 0xc7daa8: ret
    //     0xc7daa8: ret             
  }
}
