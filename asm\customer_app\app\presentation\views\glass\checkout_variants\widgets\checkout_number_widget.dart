// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart

// class id: 1049366, size: 0x8
class :: {
}

// class id: 3366, size: 0x28, field offset: 0x14
class _CheckoutNumberWidgetState extends State<dynamic> {

  late final TextEditingController _numberController; // offset: 0x14

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x8048e8, size: 0x184
    // 0x8048e8: EnterFrame
    //     0x8048e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8048ec: mov             fp, SP
    // 0x8048f0: AllocStack(0x20)
    //     0x8048f0: sub             SP, SP, #0x20
    // 0x8048f4: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8048f4: mov             x4, x1
    //     0x8048f8: mov             x3, x2
    //     0x8048fc: stur            x1, [fp, #-8]
    //     0x804900: stur            x2, [fp, #-0x10]
    // 0x804904: CheckStackOverflow
    //     0x804904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804908: cmp             SP, x16
    //     0x80490c: b.ls            #0x804a4c
    // 0x804910: mov             x0, x3
    // 0x804914: r2 = Null
    //     0x804914: mov             x2, NULL
    // 0x804918: r1 = Null
    //     0x804918: mov             x1, NULL
    // 0x80491c: r4 = 60
    //     0x80491c: movz            x4, #0x3c
    // 0x804920: branchIfSmi(r0, 0x80492c)
    //     0x804920: tbz             w0, #0, #0x80492c
    // 0x804924: r4 = LoadClassIdInstr(r0)
    //     0x804924: ldur            x4, [x0, #-1]
    //     0x804928: ubfx            x4, x4, #0xc, #0x14
    // 0x80492c: r17 = 4104
    //     0x80492c: movz            x17, #0x1008
    // 0x804930: cmp             x4, x17
    // 0x804934: b.eq            #0x80494c
    // 0x804938: r8 = CheckoutNumberWidget
    //     0x804938: add             x8, PP, #0x56, lsl #12  ; [pp+0x56790] Type: CheckoutNumberWidget
    //     0x80493c: ldr             x8, [x8, #0x790]
    // 0x804940: r3 = Null
    //     0x804940: add             x3, PP, #0x56, lsl #12  ; [pp+0x56798] Null
    //     0x804944: ldr             x3, [x3, #0x798]
    // 0x804948: r0 = CheckoutNumberWidget()
    //     0x804948: bl              #0x804be0  ; IsType_CheckoutNumberWidget_Stub
    // 0x80494c: ldur            x3, [fp, #-8]
    // 0x804950: LoadField: r2 = r3->field_7
    //     0x804950: ldur            w2, [x3, #7]
    // 0x804954: DecompressPointer r2
    //     0x804954: add             x2, x2, HEAP, lsl #32
    // 0x804958: ldur            x0, [fp, #-0x10]
    // 0x80495c: r1 = Null
    //     0x80495c: mov             x1, NULL
    // 0x804960: cmp             w2, NULL
    // 0x804964: b.eq            #0x804988
    // 0x804968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x804968: ldur            w4, [x2, #0x17]
    // 0x80496c: DecompressPointer r4
    //     0x80496c: add             x4, x4, HEAP, lsl #32
    // 0x804970: r8 = X0 bound StatefulWidget
    //     0x804970: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x804974: ldr             x8, [x8, #0x7a0]
    // 0x804978: LoadField: r9 = r4->field_7
    //     0x804978: ldur            x9, [x4, #7]
    // 0x80497c: r3 = Null
    //     0x80497c: add             x3, PP, #0x56, lsl #12  ; [pp+0x567a8] Null
    //     0x804980: ldr             x3, [x3, #0x7a8]
    // 0x804984: blr             x9
    // 0x804988: ldur            x1, [fp, #-8]
    // 0x80498c: LoadField: r0 = r1->field_b
    //     0x80498c: ldur            w0, [x1, #0xb]
    // 0x804990: DecompressPointer r0
    //     0x804990: add             x0, x0, HEAP, lsl #32
    // 0x804994: cmp             w0, NULL
    // 0x804998: b.eq            #0x804a54
    // 0x80499c: LoadField: r2 = r0->field_13
    //     0x80499c: ldur            w2, [x0, #0x13]
    // 0x8049a0: DecompressPointer r2
    //     0x8049a0: add             x2, x2, HEAP, lsl #32
    // 0x8049a4: ldur            x0, [fp, #-0x10]
    // 0x8049a8: LoadField: r3 = r0->field_13
    //     0x8049a8: ldur            w3, [x0, #0x13]
    // 0x8049ac: DecompressPointer r3
    //     0x8049ac: add             x3, x3, HEAP, lsl #32
    // 0x8049b0: r0 = LoadClassIdInstr(r2)
    //     0x8049b0: ldur            x0, [x2, #-1]
    //     0x8049b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8049b8: stp             x3, x2, [SP]
    // 0x8049bc: mov             lr, x0
    // 0x8049c0: ldr             lr, [x21, lr, lsl #3]
    // 0x8049c4: blr             lr
    // 0x8049c8: tbz             w0, #4, #0x804a3c
    // 0x8049cc: ldur            x0, [fp, #-8]
    // 0x8049d0: LoadField: r1 = r0->field_13
    //     0x8049d0: ldur            w1, [x0, #0x13]
    // 0x8049d4: DecompressPointer r1
    //     0x8049d4: add             x1, x1, HEAP, lsl #32
    // 0x8049d8: r16 = Sentinel
    //     0x8049d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8049dc: cmp             w1, w16
    // 0x8049e0: b.eq            #0x804a58
    // 0x8049e4: LoadField: r2 = r0->field_b
    //     0x8049e4: ldur            w2, [x0, #0xb]
    // 0x8049e8: DecompressPointer r2
    //     0x8049e8: add             x2, x2, HEAP, lsl #32
    // 0x8049ec: cmp             w2, NULL
    // 0x8049f0: b.eq            #0x804a64
    // 0x8049f4: LoadField: r3 = r2->field_13
    //     0x8049f4: ldur            w3, [x2, #0x13]
    // 0x8049f8: DecompressPointer r3
    //     0x8049f8: add             x3, x3, HEAP, lsl #32
    // 0x8049fc: mov             x2, x3
    // 0x804a00: r0 = text=()
    //     0x804a00: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x804a04: ldur            x1, [fp, #-8]
    // 0x804a08: LoadField: r0 = r1->field_b
    //     0x804a08: ldur            w0, [x1, #0xb]
    // 0x804a0c: DecompressPointer r0
    //     0x804a0c: add             x0, x0, HEAP, lsl #32
    // 0x804a10: cmp             w0, NULL
    // 0x804a14: b.eq            #0x804a68
    // 0x804a18: LoadField: r2 = r0->field_13
    //     0x804a18: ldur            w2, [x0, #0x13]
    // 0x804a1c: DecompressPointer r2
    //     0x804a1c: add             x2, x2, HEAP, lsl #32
    // 0x804a20: LoadField: r0 = r2->field_7
    //     0x804a20: ldur            w0, [x2, #7]
    // 0x804a24: cbz             w0, #0x804a30
    // 0x804a28: r2 = false
    //     0x804a28: add             x2, NULL, #0x30  ; false
    // 0x804a2c: b               #0x804a34
    // 0x804a30: r2 = true
    //     0x804a30: add             x2, NULL, #0x20  ; true
    // 0x804a34: StoreField: r1->field_23 = r2
    //     0x804a34: stur            w2, [x1, #0x23]
    // 0x804a38: r0 = _initializeFromExistingNumber()
    //     0x804a38: bl              #0x804a6c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_initializeFromExistingNumber
    // 0x804a3c: r0 = Null
    //     0x804a3c: mov             x0, NULL
    // 0x804a40: LeaveFrame
    //     0x804a40: mov             SP, fp
    //     0x804a44: ldp             fp, lr, [SP], #0x10
    // 0x804a48: ret
    //     0x804a48: ret             
    // 0x804a4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804a4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804a50: b               #0x804910
    // 0x804a54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804a54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x804a58: r9 = _numberController
    //     0x804a58: add             x9, PP, #0x56, lsl #12  ; [pp+0x56780] Field <_CheckoutNumberWidgetState@1552288663._numberController@1552288663>: late final (offset: 0x14)
    //     0x804a5c: ldr             x9, [x9, #0x780]
    // 0x804a60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x804a60: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x804a64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804a64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x804a68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804a68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initializeFromExistingNumber(/* No info */) {
    // ** addr: 0x804a6c, size: 0xc4
    // 0x804a6c: EnterFrame
    //     0x804a6c: stp             fp, lr, [SP, #-0x10]!
    //     0x804a70: mov             fp, SP
    // 0x804a74: AllocStack(0x10)
    //     0x804a74: sub             SP, SP, #0x10
    // 0x804a78: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x804a78: mov             x0, x1
    //     0x804a7c: stur            x1, [fp, #-8]
    // 0x804a80: CheckStackOverflow
    //     0x804a80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804a84: cmp             SP, x16
    //     0x804a88: b.ls            #0x804b18
    // 0x804a8c: LoadField: r1 = r0->field_13
    //     0x804a8c: ldur            w1, [x0, #0x13]
    // 0x804a90: DecompressPointer r1
    //     0x804a90: add             x1, x1, HEAP, lsl #32
    // 0x804a94: r16 = Sentinel
    //     0x804a94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x804a98: cmp             w1, w16
    // 0x804a9c: b.eq            #0x804b20
    // 0x804aa0: LoadField: r2 = r1->field_27
    //     0x804aa0: ldur            w2, [x1, #0x27]
    // 0x804aa4: DecompressPointer r2
    //     0x804aa4: add             x2, x2, HEAP, lsl #32
    // 0x804aa8: LoadField: r1 = r2->field_7
    //     0x804aa8: ldur            w1, [x2, #7]
    // 0x804aac: DecompressPointer r1
    //     0x804aac: add             x1, x1, HEAP, lsl #32
    // 0x804ab0: LoadField: r2 = r1->field_7
    //     0x804ab0: ldur            w2, [x1, #7]
    // 0x804ab4: cbz             w2, #0x804b00
    // 0x804ab8: r2 = true
    //     0x804ab8: add             x2, NULL, #0x20  ; true
    // 0x804abc: StoreField: r0->field_1f = r2
    //     0x804abc: stur            w2, [x0, #0x1f]
    // 0x804ac0: mov             x2, x1
    // 0x804ac4: mov             x1, x0
    // 0x804ac8: r0 = _isValidPhoneNumber()
    //     0x804ac8: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x804acc: ldur            x1, [fp, #-8]
    // 0x804ad0: StoreField: r1->field_1b = r0
    //     0x804ad0: stur            w0, [x1, #0x1b]
    // 0x804ad4: LoadField: r0 = r1->field_b
    //     0x804ad4: ldur            w0, [x1, #0xb]
    // 0x804ad8: DecompressPointer r0
    //     0x804ad8: add             x0, x0, HEAP, lsl #32
    // 0x804adc: cmp             w0, NULL
    // 0x804ae0: b.eq            #0x804b2c
    // 0x804ae4: LoadField: r2 = r0->field_f
    //     0x804ae4: ldur            w2, [x0, #0xf]
    // 0x804ae8: DecompressPointer r2
    //     0x804ae8: add             x2, x2, HEAP, lsl #32
    // 0x804aec: str             x2, [SP]
    // 0x804af0: mov             x0, x2
    // 0x804af4: ClosureCall
    //     0x804af4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x804af8: ldur            x2, [x0, #0x1f]
    //     0x804afc: blr             x2
    // 0x804b00: ldur            x1, [fp, #-8]
    // 0x804b04: r0 = _notifyParent()
    //     0x804b04: bl              #0x804b30  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_notifyParent
    // 0x804b08: r0 = Null
    //     0x804b08: mov             x0, NULL
    // 0x804b0c: LeaveFrame
    //     0x804b0c: mov             SP, fp
    //     0x804b10: ldp             fp, lr, [SP], #0x10
    // 0x804b14: ret
    //     0x804b14: ret             
    // 0x804b18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804b18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804b1c: b               #0x804a8c
    // 0x804b20: r9 = _numberController
    //     0x804b20: add             x9, PP, #0x56, lsl #12  ; [pp+0x56780] Field <_CheckoutNumberWidgetState@1552288663._numberController@1552288663>: late final (offset: 0x14)
    //     0x804b24: ldr             x9, [x9, #0x780]
    // 0x804b28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x804b28: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x804b2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804b2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _notifyParent(/* No info */) {
    // ** addr: 0x804b30, size: 0xb0
    // 0x804b30: EnterFrame
    //     0x804b30: stp             fp, lr, [SP, #-0x10]!
    //     0x804b34: mov             fp, SP
    // 0x804b38: AllocStack(0x28)
    //     0x804b38: sub             SP, SP, #0x28
    // 0x804b3c: CheckStackOverflow
    //     0x804b3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804b40: cmp             SP, x16
    //     0x804b44: b.ls            #0x804bc8
    // 0x804b48: LoadField: r0 = r1->field_b
    //     0x804b48: ldur            w0, [x1, #0xb]
    // 0x804b4c: DecompressPointer r0
    //     0x804b4c: add             x0, x0, HEAP, lsl #32
    // 0x804b50: stur            x0, [fp, #-0x10]
    // 0x804b54: cmp             w0, NULL
    // 0x804b58: b.eq            #0x804bd0
    // 0x804b5c: LoadField: r2 = r1->field_13
    //     0x804b5c: ldur            w2, [x1, #0x13]
    // 0x804b60: DecompressPointer r2
    //     0x804b60: add             x2, x2, HEAP, lsl #32
    // 0x804b64: r16 = Sentinel
    //     0x804b64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x804b68: cmp             w2, w16
    // 0x804b6c: b.eq            #0x804bd4
    // 0x804b70: LoadField: r3 = r2->field_27
    //     0x804b70: ldur            w3, [x2, #0x27]
    // 0x804b74: DecompressPointer r3
    //     0x804b74: add             x3, x3, HEAP, lsl #32
    // 0x804b78: LoadField: r4 = r3->field_7
    //     0x804b78: ldur            w4, [x3, #7]
    // 0x804b7c: DecompressPointer r4
    //     0x804b7c: add             x4, x4, HEAP, lsl #32
    // 0x804b80: mov             x2, x4
    // 0x804b84: stur            x4, [fp, #-8]
    // 0x804b88: r0 = _isValidPhoneNumber()
    //     0x804b88: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x804b8c: mov             x1, x0
    // 0x804b90: ldur            x0, [fp, #-0x10]
    // 0x804b94: LoadField: r2 = r0->field_b
    //     0x804b94: ldur            w2, [x0, #0xb]
    // 0x804b98: DecompressPointer r2
    //     0x804b98: add             x2, x2, HEAP, lsl #32
    // 0x804b9c: ldur            x16, [fp, #-8]
    // 0x804ba0: stp             x16, x2, [SP, #8]
    // 0x804ba4: str             x1, [SP]
    // 0x804ba8: mov             x0, x2
    // 0x804bac: ClosureCall
    //     0x804bac: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x804bb0: ldur            x2, [x0, #0x1f]
    //     0x804bb4: blr             x2
    // 0x804bb8: r0 = Null
    //     0x804bb8: mov             x0, NULL
    // 0x804bbc: LeaveFrame
    //     0x804bbc: mov             SP, fp
    //     0x804bc0: ldp             fp, lr, [SP], #0x10
    // 0x804bc4: ret
    //     0x804bc4: ret             
    // 0x804bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804bc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804bcc: b               #0x804b48
    // 0x804bd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804bd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x804bd4: r9 = _numberController
    //     0x804bd4: add             x9, PP, #0x56, lsl #12  ; [pp+0x56780] Field <_CheckoutNumberWidgetState@1552288663._numberController@1552288663>: late final (offset: 0x14)
    //     0x804bd8: ldr             x9, [x9, #0x780]
    // 0x804bdc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x804bdc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x9404fc, size: 0x100
    // 0x9404fc: EnterFrame
    //     0x9404fc: stp             fp, lr, [SP, #-0x10]!
    //     0x940500: mov             fp, SP
    // 0x940504: AllocStack(0x20)
    //     0x940504: sub             SP, SP, #0x20
    // 0x940508: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r0, fp-0x10 */)
    //     0x940508: mov             x0, x1
    //     0x94050c: stur            x1, [fp, #-0x10]
    // 0x940510: CheckStackOverflow
    //     0x940510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940514: cmp             SP, x16
    //     0x940518: b.ls            #0x9405ec
    // 0x94051c: LoadField: r1 = r0->field_b
    //     0x94051c: ldur            w1, [x0, #0xb]
    // 0x940520: DecompressPointer r1
    //     0x940520: add             x1, x1, HEAP, lsl #32
    // 0x940524: cmp             w1, NULL
    // 0x940528: b.eq            #0x9405f4
    // 0x94052c: LoadField: r2 = r1->field_13
    //     0x94052c: ldur            w2, [x1, #0x13]
    // 0x940530: DecompressPointer r2
    //     0x940530: add             x2, x2, HEAP, lsl #32
    // 0x940534: stur            x2, [fp, #-8]
    // 0x940538: r1 = <TextEditingValue>
    //     0x940538: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0x94053c: r0 = TextEditingController()
    //     0x94053c: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x940540: stur            x0, [fp, #-0x18]
    // 0x940544: ldur            x16, [fp, #-8]
    // 0x940548: str             x16, [SP]
    // 0x94054c: mov             x1, x0
    // 0x940550: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0x940550: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0x940554: ldr             x4, [x4, #0xc40]
    // 0x940558: r0 = TextEditingController()
    //     0x940558: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x94055c: ldur            x1, [fp, #-0x10]
    // 0x940560: LoadField: r0 = r1->field_13
    //     0x940560: ldur            w0, [x1, #0x13]
    // 0x940564: DecompressPointer r0
    //     0x940564: add             x0, x0, HEAP, lsl #32
    // 0x940568: r16 = Sentinel
    //     0x940568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94056c: cmp             w0, w16
    // 0x940570: b.ne            #0x9405d8
    // 0x940574: ldur            x0, [fp, #-0x18]
    // 0x940578: StoreField: r1->field_13 = r0
    //     0x940578: stur            w0, [x1, #0x13]
    //     0x94057c: ldurb           w16, [x1, #-1]
    //     0x940580: ldurb           w17, [x0, #-1]
    //     0x940584: and             x16, x17, x16, lsr #2
    //     0x940588: tst             x16, HEAP, lsr #32
    //     0x94058c: b.eq            #0x940594
    //     0x940590: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x940594: LoadField: r0 = r1->field_b
    //     0x940594: ldur            w0, [x1, #0xb]
    // 0x940598: DecompressPointer r0
    //     0x940598: add             x0, x0, HEAP, lsl #32
    // 0x94059c: cmp             w0, NULL
    // 0x9405a0: b.eq            #0x9405f8
    // 0x9405a4: LoadField: r2 = r0->field_13
    //     0x9405a4: ldur            w2, [x0, #0x13]
    // 0x9405a8: DecompressPointer r2
    //     0x9405a8: add             x2, x2, HEAP, lsl #32
    // 0x9405ac: LoadField: r0 = r2->field_7
    //     0x9405ac: ldur            w0, [x2, #7]
    // 0x9405b0: cbz             w0, #0x9405bc
    // 0x9405b4: r2 = false
    //     0x9405b4: add             x2, NULL, #0x30  ; false
    // 0x9405b8: b               #0x9405c0
    // 0x9405bc: r2 = true
    //     0x9405bc: add             x2, NULL, #0x20  ; true
    // 0x9405c0: StoreField: r1->field_23 = r2
    //     0x9405c0: stur            w2, [x1, #0x23]
    // 0x9405c4: r0 = _initializeFromExistingNumber()
    //     0x9405c4: bl              #0x804a6c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_initializeFromExistingNumber
    // 0x9405c8: r0 = Null
    //     0x9405c8: mov             x0, NULL
    // 0x9405cc: LeaveFrame
    //     0x9405cc: mov             SP, fp
    //     0x9405d0: ldp             fp, lr, [SP], #0x10
    // 0x9405d4: ret
    //     0x9405d4: ret             
    // 0x9405d8: r16 = "_numberController@1552288663"
    //     0x9405d8: add             x16, PP, #0x56, lsl #12  ; [pp+0x567b8] "_numberController@1552288663"
    //     0x9405dc: ldr             x16, [x16, #0x7b8]
    // 0x9405e0: str             x16, [SP]
    // 0x9405e4: r0 = _throwFieldAlreadyInitialized()
    //     0x9405e4: bl              #0x6388d8  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x9405e8: brk             #0
    // 0x9405ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9405ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9405f0: b               #0x94051c
    // 0x9405f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9405f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9405f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9405f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb458c8, size: 0x358
    // 0xb458c8: EnterFrame
    //     0xb458c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb458cc: mov             fp, SP
    // 0xb458d0: AllocStack(0xb0)
    //     0xb458d0: sub             SP, SP, #0xb0
    // 0xb458d4: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb458d4: mov             x0, x2
    //     0xb458d8: stur            x2, [fp, #-0x10]
    //     0xb458dc: mov             x2, x1
    //     0xb458e0: stur            x1, [fp, #-8]
    // 0xb458e4: CheckStackOverflow
    //     0xb458e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb458e8: cmp             SP, x16
    //     0xb458ec: b.ls            #0xb45c08
    // 0xb458f0: mov             x1, x0
    // 0xb458f4: r0 = of()
    //     0xb458f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb458f8: stur            x0, [fp, #-0x20]
    // 0xb458fc: LoadField: r1 = r0->field_87
    //     0xb458fc: ldur            w1, [x0, #0x87]
    // 0xb45900: DecompressPointer r1
    //     0xb45900: add             x1, x1, HEAP, lsl #32
    // 0xb45904: LoadField: r2 = r1->field_2b
    //     0xb45904: ldur            w2, [x1, #0x2b]
    // 0xb45908: DecompressPointer r2
    //     0xb45908: add             x2, x2, HEAP, lsl #32
    // 0xb4590c: stur            x2, [fp, #-0x18]
    // 0xb45910: r16 = 12.000000
    //     0xb45910: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb45914: ldr             x16, [x16, #0x9e8]
    // 0xb45918: r30 = Instance_Color
    //     0xb45918: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4591c: stp             lr, x16, [SP]
    // 0xb45920: mov             x1, x2
    // 0xb45924: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb45924: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb45928: ldr             x4, [x4, #0xaa0]
    // 0xb4592c: r0 = copyWith()
    //     0xb4592c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb45930: stur            x0, [fp, #-0x28]
    // 0xb45934: r0 = Text()
    //     0xb45934: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb45938: mov             x1, x0
    // 0xb4593c: r0 = "Enter WhatsApp no.*"
    //     0xb4593c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53fb8] "Enter WhatsApp no.*"
    //     0xb45940: ldr             x0, [x0, #0xfb8]
    // 0xb45944: stur            x1, [fp, #-0x30]
    // 0xb45948: StoreField: r1->field_b = r0
    //     0xb45948: stur            w0, [x1, #0xb]
    // 0xb4594c: ldur            x0, [fp, #-0x28]
    // 0xb45950: StoreField: r1->field_13 = r0
    //     0xb45950: stur            w0, [x1, #0x13]
    // 0xb45954: r0 = Padding()
    //     0xb45954: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb45958: mov             x1, x0
    // 0xb4595c: r0 = Instance_EdgeInsets
    //     0xb4595c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb45960: ldr             x0, [x0, #0x4c0]
    // 0xb45964: stur            x1, [fp, #-0x40]
    // 0xb45968: StoreField: r1->field_f = r0
    //     0xb45968: stur            w0, [x1, #0xf]
    // 0xb4596c: ldur            x0, [fp, #-0x30]
    // 0xb45970: StoreField: r1->field_b = r0
    //     0xb45970: stur            w0, [x1, #0xb]
    // 0xb45974: ldur            x0, [fp, #-8]
    // 0xb45978: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb45978: ldur            w2, [x0, #0x17]
    // 0xb4597c: DecompressPointer r2
    //     0xb4597c: add             x2, x2, HEAP, lsl #32
    // 0xb45980: stur            x2, [fp, #-0x38]
    // 0xb45984: LoadField: r3 = r0->field_b
    //     0xb45984: ldur            w3, [x0, #0xb]
    // 0xb45988: DecompressPointer r3
    //     0xb45988: add             x3, x3, HEAP, lsl #32
    // 0xb4598c: cmp             w3, NULL
    // 0xb45990: b.eq            #0xb45c10
    // 0xb45994: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb45994: ldur            w4, [x3, #0x17]
    // 0xb45998: DecompressPointer r4
    //     0xb45998: add             x4, x4, HEAP, lsl #32
    // 0xb4599c: stur            x4, [fp, #-0x30]
    // 0xb459a0: LoadField: r3 = r0->field_23
    //     0xb459a0: ldur            w3, [x0, #0x23]
    // 0xb459a4: DecompressPointer r3
    //     0xb459a4: add             x3, x3, HEAP, lsl #32
    // 0xb459a8: stur            x3, [fp, #-0x28]
    // 0xb459ac: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb459ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb459b0: ldr             x0, [x0, #0x1530]
    //     0xb459b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb459b8: cmp             w0, w16
    //     0xb459bc: b.ne            #0xb459cc
    //     0xb459c0: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb459c4: ldr             x2, [x2, #0x120]
    //     0xb459c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb459cc: stur            x0, [fp, #-0x48]
    // 0xb459d0: r0 = LengthLimitingTextInputFormatter()
    //     0xb459d0: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb459d4: mov             x3, x0
    // 0xb459d8: r0 = 20
    //     0xb459d8: movz            x0, #0x14
    // 0xb459dc: stur            x3, [fp, #-0x50]
    // 0xb459e0: StoreField: r3->field_7 = r0
    //     0xb459e0: stur            w0, [x3, #7]
    // 0xb459e4: r1 = Null
    //     0xb459e4: mov             x1, NULL
    // 0xb459e8: r2 = 4
    //     0xb459e8: movz            x2, #0x4
    // 0xb459ec: r0 = AllocateArray()
    //     0xb459ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb459f0: mov             x2, x0
    // 0xb459f4: ldur            x0, [fp, #-0x48]
    // 0xb459f8: stur            x2, [fp, #-0x58]
    // 0xb459fc: StoreField: r2->field_f = r0
    //     0xb459fc: stur            w0, [x2, #0xf]
    // 0xb45a00: ldur            x0, [fp, #-0x50]
    // 0xb45a04: StoreField: r2->field_13 = r0
    //     0xb45a04: stur            w0, [x2, #0x13]
    // 0xb45a08: r1 = <TextInputFormatter>
    //     0xb45a08: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb45a0c: ldr             x1, [x1, #0x7b0]
    // 0xb45a10: r0 = AllocateGrowableArray()
    //     0xb45a10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb45a14: mov             x2, x0
    // 0xb45a18: ldur            x0, [fp, #-0x58]
    // 0xb45a1c: stur            x2, [fp, #-0x48]
    // 0xb45a20: StoreField: r2->field_f = r0
    //     0xb45a20: stur            w0, [x2, #0xf]
    // 0xb45a24: r0 = 4
    //     0xb45a24: movz            x0, #0x4
    // 0xb45a28: StoreField: r2->field_b = r0
    //     0xb45a28: stur            w0, [x2, #0xb]
    // 0xb45a2c: ldur            x1, [fp, #-0x20]
    // 0xb45a30: LoadField: r3 = r1->field_5b
    //     0xb45a30: ldur            w3, [x1, #0x5b]
    // 0xb45a34: DecompressPointer r3
    //     0xb45a34: add             x3, x3, HEAP, lsl #32
    // 0xb45a38: r16 = 14.000000
    //     0xb45a38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb45a3c: ldr             x16, [x16, #0x1d8]
    // 0xb45a40: stp             x3, x16, [SP]
    // 0xb45a44: ldur            x1, [fp, #-0x18]
    // 0xb45a48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb45a48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb45a4c: ldr             x4, [x4, #0xaa0]
    // 0xb45a50: r0 = copyWith()
    //     0xb45a50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb45a54: mov             x3, x0
    // 0xb45a58: ldur            x0, [fp, #-8]
    // 0xb45a5c: stur            x3, [fp, #-0x20]
    // 0xb45a60: LoadField: r4 = r0->field_13
    //     0xb45a60: ldur            w4, [x0, #0x13]
    // 0xb45a64: DecompressPointer r4
    //     0xb45a64: add             x4, x4, HEAP, lsl #32
    // 0xb45a68: r16 = Sentinel
    //     0xb45a68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb45a6c: cmp             w4, w16
    // 0xb45a70: b.eq            #0xb45c14
    // 0xb45a74: mov             x1, x0
    // 0xb45a78: ldur            x2, [fp, #-0x10]
    // 0xb45a7c: stur            x4, [fp, #-0x18]
    // 0xb45a80: r0 = _buildInputDecoration()
    //     0xb45a80: bl              #0xb45c20  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildInputDecoration
    // 0xb45a84: ldur            x2, [fp, #-8]
    // 0xb45a88: r1 = Function '_validatePhoneNumber@1552288663':.
    //     0xb45a88: add             x1, PP, #0x56, lsl #12  ; [pp+0x56770] AnonymousClosure: (0xb45f68), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber (0xa08f3c)
    //     0xb45a8c: ldr             x1, [x1, #0x770]
    // 0xb45a90: stur            x0, [fp, #-0x10]
    // 0xb45a94: r0 = AllocateClosure()
    //     0xb45a94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb45a98: ldur            x2, [fp, #-8]
    // 0xb45a9c: r1 = Function '_handlePhoneNumberChanged@1552288663':.
    //     0xb45a9c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56778] AnonymousClosure: (0xb45e48), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged (0xb45e84)
    //     0xb45aa0: ldr             x1, [x1, #0x778]
    // 0xb45aa4: stur            x0, [fp, #-8]
    // 0xb45aa8: r0 = AllocateClosure()
    //     0xb45aa8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb45aac: r1 = <String>
    //     0xb45aac: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb45ab0: stur            x0, [fp, #-0x50]
    // 0xb45ab4: r0 = TextFormField()
    //     0xb45ab4: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb45ab8: stur            x0, [fp, #-0x58]
    // 0xb45abc: ldur            x16, [fp, #-0x30]
    // 0xb45ac0: ldur            lr, [fp, #-8]
    // 0xb45ac4: stp             lr, x16, [SP, #0x48]
    // 0xb45ac8: r16 = true
    //     0xb45ac8: add             x16, NULL, #0x20  ; true
    // 0xb45acc: ldur            lr, [fp, #-0x28]
    // 0xb45ad0: stp             lr, x16, [SP, #0x38]
    // 0xb45ad4: r16 = Instance_AutovalidateMode
    //     0xb45ad4: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb45ad8: ldr             x16, [x16, #0x7e8]
    // 0xb45adc: ldur            lr, [fp, #-0x48]
    // 0xb45ae0: stp             lr, x16, [SP, #0x28]
    // 0xb45ae4: r16 = Instance_TextInputType
    //     0xb45ae4: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb45ae8: ldr             x16, [x16, #0x1a0]
    // 0xb45aec: ldur            lr, [fp, #-0x20]
    // 0xb45af0: stp             lr, x16, [SP, #0x18]
    // 0xb45af4: r16 = 2
    //     0xb45af4: movz            x16, #0x2
    // 0xb45af8: ldur            lr, [fp, #-0x18]
    // 0xb45afc: stp             lr, x16, [SP, #8]
    // 0xb45b00: ldur            x16, [fp, #-0x50]
    // 0xb45b04: str             x16, [SP]
    // 0xb45b08: mov             x1, x0
    // 0xb45b0c: ldur            x2, [fp, #-0x10]
    // 0xb45b10: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x5, autovalidateMode, 0x6, controller, 0xb, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xc, readOnly, 0x2, style, 0x9, validator, 0x3, null]
    //     0xb45b10: add             x4, PP, #0x53, lsl #12  ; [pp+0x53fd0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x5, "autovalidateMode", 0x6, "controller", 0xb, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xc, "readOnly", 0x2, "style", 0x9, "validator", 0x3, Null]
    //     0xb45b14: ldr             x4, [x4, #0xfd0]
    // 0xb45b18: r0 = TextFormField()
    //     0xb45b18: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb45b1c: r0 = Form()
    //     0xb45b1c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb45b20: mov             x3, x0
    // 0xb45b24: ldur            x0, [fp, #-0x58]
    // 0xb45b28: stur            x3, [fp, #-8]
    // 0xb45b2c: StoreField: r3->field_b = r0
    //     0xb45b2c: stur            w0, [x3, #0xb]
    // 0xb45b30: r0 = Instance_AutovalidateMode
    //     0xb45b30: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb45b34: ldr             x0, [x0, #0x800]
    // 0xb45b38: StoreField: r3->field_23 = r0
    //     0xb45b38: stur            w0, [x3, #0x23]
    // 0xb45b3c: ldur            x0, [fp, #-0x38]
    // 0xb45b40: StoreField: r3->field_7 = r0
    //     0xb45b40: stur            w0, [x3, #7]
    // 0xb45b44: r1 = Null
    //     0xb45b44: mov             x1, NULL
    // 0xb45b48: r2 = 4
    //     0xb45b48: movz            x2, #0x4
    // 0xb45b4c: r0 = AllocateArray()
    //     0xb45b4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb45b50: mov             x2, x0
    // 0xb45b54: ldur            x0, [fp, #-0x40]
    // 0xb45b58: stur            x2, [fp, #-0x10]
    // 0xb45b5c: StoreField: r2->field_f = r0
    //     0xb45b5c: stur            w0, [x2, #0xf]
    // 0xb45b60: ldur            x0, [fp, #-8]
    // 0xb45b64: StoreField: r2->field_13 = r0
    //     0xb45b64: stur            w0, [x2, #0x13]
    // 0xb45b68: r1 = <Widget>
    //     0xb45b68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb45b6c: r0 = AllocateGrowableArray()
    //     0xb45b6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb45b70: mov             x1, x0
    // 0xb45b74: ldur            x0, [fp, #-0x10]
    // 0xb45b78: stur            x1, [fp, #-8]
    // 0xb45b7c: StoreField: r1->field_f = r0
    //     0xb45b7c: stur            w0, [x1, #0xf]
    // 0xb45b80: r0 = 4
    //     0xb45b80: movz            x0, #0x4
    // 0xb45b84: StoreField: r1->field_b = r0
    //     0xb45b84: stur            w0, [x1, #0xb]
    // 0xb45b88: r0 = Column()
    //     0xb45b88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb45b8c: mov             x1, x0
    // 0xb45b90: r0 = Instance_Axis
    //     0xb45b90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb45b94: stur            x1, [fp, #-0x10]
    // 0xb45b98: StoreField: r1->field_f = r0
    //     0xb45b98: stur            w0, [x1, #0xf]
    // 0xb45b9c: r0 = Instance_MainAxisAlignment
    //     0xb45b9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb45ba0: ldr             x0, [x0, #0xa08]
    // 0xb45ba4: StoreField: r1->field_13 = r0
    //     0xb45ba4: stur            w0, [x1, #0x13]
    // 0xb45ba8: r0 = Instance_MainAxisSize
    //     0xb45ba8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb45bac: ldr             x0, [x0, #0xa10]
    // 0xb45bb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb45bb0: stur            w0, [x1, #0x17]
    // 0xb45bb4: r0 = Instance_CrossAxisAlignment
    //     0xb45bb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb45bb8: ldr             x0, [x0, #0x890]
    // 0xb45bbc: StoreField: r1->field_1b = r0
    //     0xb45bbc: stur            w0, [x1, #0x1b]
    // 0xb45bc0: r0 = Instance_VerticalDirection
    //     0xb45bc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb45bc4: ldr             x0, [x0, #0xa20]
    // 0xb45bc8: StoreField: r1->field_23 = r0
    //     0xb45bc8: stur            w0, [x1, #0x23]
    // 0xb45bcc: r0 = Instance_Clip
    //     0xb45bcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb45bd0: ldr             x0, [x0, #0x38]
    // 0xb45bd4: StoreField: r1->field_2b = r0
    //     0xb45bd4: stur            w0, [x1, #0x2b]
    // 0xb45bd8: StoreField: r1->field_2f = rZR
    //     0xb45bd8: stur            xzr, [x1, #0x2f]
    // 0xb45bdc: ldur            x0, [fp, #-8]
    // 0xb45be0: StoreField: r1->field_b = r0
    //     0xb45be0: stur            w0, [x1, #0xb]
    // 0xb45be4: r0 = Padding()
    //     0xb45be4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb45be8: r1 = Instance_EdgeInsets
    //     0xb45be8: add             x1, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xb45bec: ldr             x1, [x1, #0x50]
    // 0xb45bf0: StoreField: r0->field_f = r1
    //     0xb45bf0: stur            w1, [x0, #0xf]
    // 0xb45bf4: ldur            x1, [fp, #-0x10]
    // 0xb45bf8: StoreField: r0->field_b = r1
    //     0xb45bf8: stur            w1, [x0, #0xb]
    // 0xb45bfc: LeaveFrame
    //     0xb45bfc: mov             SP, fp
    //     0xb45c00: ldp             fp, lr, [SP], #0x10
    // 0xb45c04: ret
    //     0xb45c04: ret             
    // 0xb45c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45c08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45c0c: b               #0xb458f0
    // 0xb45c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45c10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45c14: r9 = _numberController
    //     0xb45c14: add             x9, PP, #0x56, lsl #12  ; [pp+0x56780] Field <_CheckoutNumberWidgetState@1552288663._numberController@1552288663>: late final (offset: 0x14)
    //     0xb45c18: ldr             x9, [x9, #0x780]
    // 0xb45c1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb45c1c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildInputDecoration(/* No info */) {
    // ** addr: 0xb45c20, size: 0x228
    // 0xb45c20: EnterFrame
    //     0xb45c20: stp             fp, lr, [SP, #-0x10]!
    //     0xb45c24: mov             fp, SP
    // 0xb45c28: AllocStack(0x80)
    //     0xb45c28: sub             SP, SP, #0x80
    // 0xb45c2c: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb45c2c: mov             x0, x2
    //     0xb45c30: stur            x2, [fp, #-0x10]
    //     0xb45c34: mov             x2, x1
    //     0xb45c38: stur            x1, [fp, #-8]
    // 0xb45c3c: CheckStackOverflow
    //     0xb45c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45c40: cmp             SP, x16
    //     0xb45c44: b.ls            #0xb45e40
    // 0xb45c48: mov             x1, x0
    // 0xb45c4c: r0 = of()
    //     0xb45c4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb45c50: ldur            x1, [fp, #-0x10]
    // 0xb45c54: stur            x0, [fp, #-0x18]
    // 0xb45c58: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb45c58: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb45c5c: ldur            x1, [fp, #-8]
    // 0xb45c60: ldur            x2, [fp, #-0x18]
    // 0xb45c64: stur            x0, [fp, #-0x20]
    // 0xb45c68: r0 = _buildPrefixIcon()
    //     0xb45c68: bl              #0xa08aec  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildPrefixIcon
    // 0xb45c6c: mov             x1, x0
    // 0xb45c70: ldur            x0, [fp, #-8]
    // 0xb45c74: stur            x1, [fp, #-0x30]
    // 0xb45c78: LoadField: r2 = r0->field_1f
    //     0xb45c78: ldur            w2, [x0, #0x1f]
    // 0xb45c7c: DecompressPointer r2
    //     0xb45c7c: add             x2, x2, HEAP, lsl #32
    // 0xb45c80: tbnz            w2, #4, #0xb45ce4
    // 0xb45c84: LoadField: r2 = r0->field_1b
    //     0xb45c84: ldur            w2, [x0, #0x1b]
    // 0xb45c88: DecompressPointer r2
    //     0xb45c88: add             x2, x2, HEAP, lsl #32
    // 0xb45c8c: tbnz            w2, #4, #0xb45c9c
    // 0xb45c90: r0 = Instance_IconData
    //     0xb45c90: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb45c94: ldr             x0, [x0, #0x130]
    // 0xb45c98: b               #0xb45ca4
    // 0xb45c9c: r0 = Instance_IconData
    //     0xb45c9c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb45ca0: ldr             x0, [x0, #0x138]
    // 0xb45ca4: stur            x0, [fp, #-0x28]
    // 0xb45ca8: tbnz            w2, #4, #0xb45cb8
    // 0xb45cac: r2 = Instance_Color
    //     0xb45cac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb45cb0: ldr             x2, [x2, #0x858]
    // 0xb45cb4: b               #0xb45cc0
    // 0xb45cb8: r2 = Instance_Color
    //     0xb45cb8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb45cbc: ldr             x2, [x2, #0x50]
    // 0xb45cc0: stur            x2, [fp, #-8]
    // 0xb45cc4: r0 = Icon()
    //     0xb45cc4: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb45cc8: mov             x1, x0
    // 0xb45ccc: ldur            x0, [fp, #-0x28]
    // 0xb45cd0: StoreField: r1->field_b = r0
    //     0xb45cd0: stur            w0, [x1, #0xb]
    // 0xb45cd4: ldur            x0, [fp, #-8]
    // 0xb45cd8: StoreField: r1->field_23 = r0
    //     0xb45cd8: stur            w0, [x1, #0x23]
    // 0xb45cdc: mov             x2, x1
    // 0xb45ce0: b               #0xb45ce8
    // 0xb45ce4: r2 = Null
    //     0xb45ce4: mov             x2, NULL
    // 0xb45ce8: ldur            x0, [fp, #-0x18]
    // 0xb45cec: stur            x2, [fp, #-0x28]
    // 0xb45cf0: LoadField: r1 = r0->field_87
    //     0xb45cf0: ldur            w1, [x0, #0x87]
    // 0xb45cf4: DecompressPointer r1
    //     0xb45cf4: add             x1, x1, HEAP, lsl #32
    // 0xb45cf8: LoadField: r0 = r1->field_2b
    //     0xb45cf8: ldur            w0, [x1, #0x2b]
    // 0xb45cfc: DecompressPointer r0
    //     0xb45cfc: add             x0, x0, HEAP, lsl #32
    // 0xb45d00: stur            x0, [fp, #-8]
    // 0xb45d04: r1 = Instance_Color
    //     0xb45d04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb45d08: d0 = 0.400000
    //     0xb45d08: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb45d0c: r0 = withOpacity()
    //     0xb45d0c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb45d10: r16 = 14.000000
    //     0xb45d10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb45d14: ldr             x16, [x16, #0x1d8]
    // 0xb45d18: stp             x0, x16, [SP]
    // 0xb45d1c: ldur            x1, [fp, #-8]
    // 0xb45d20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb45d20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb45d24: ldr             x4, [x4, #0xaa0]
    // 0xb45d28: r0 = copyWith()
    //     0xb45d28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb45d2c: stur            x0, [fp, #-0x18]
    // 0xb45d30: r16 = 12.000000
    //     0xb45d30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb45d34: ldr             x16, [x16, #0x9e8]
    // 0xb45d38: r30 = Instance_MaterialColor
    //     0xb45d38: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb45d3c: ldr             lr, [lr, #0x180]
    // 0xb45d40: stp             lr, x16, [SP]
    // 0xb45d44: ldur            x1, [fp, #-8]
    // 0xb45d48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb45d48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb45d4c: ldr             x4, [x4, #0xaa0]
    // 0xb45d50: r0 = copyWith()
    //     0xb45d50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb45d54: ldur            x1, [fp, #-0x10]
    // 0xb45d58: stur            x0, [fp, #-8]
    // 0xb45d5c: r0 = of()
    //     0xb45d5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb45d60: LoadField: r1 = r0->field_5b
    //     0xb45d60: ldur            w1, [x0, #0x5b]
    // 0xb45d64: DecompressPointer r1
    //     0xb45d64: add             x1, x1, HEAP, lsl #32
    // 0xb45d68: stur            x1, [fp, #-0x10]
    // 0xb45d6c: r0 = BorderSide()
    //     0xb45d6c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb45d70: mov             x1, x0
    // 0xb45d74: ldur            x0, [fp, #-0x10]
    // 0xb45d78: stur            x1, [fp, #-0x38]
    // 0xb45d7c: StoreField: r1->field_7 = r0
    //     0xb45d7c: stur            w0, [x1, #7]
    // 0xb45d80: d0 = 1.000000
    //     0xb45d80: fmov            d0, #1.00000000
    // 0xb45d84: StoreField: r1->field_b = d0
    //     0xb45d84: stur            d0, [x1, #0xb]
    // 0xb45d88: r0 = Instance_BorderStyle
    //     0xb45d88: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb45d8c: ldr             x0, [x0, #0xf68]
    // 0xb45d90: StoreField: r1->field_13 = r0
    //     0xb45d90: stur            w0, [x1, #0x13]
    // 0xb45d94: d0 = -1.000000
    //     0xb45d94: fmov            d0, #-1.00000000
    // 0xb45d98: ArrayStore: r1[0] = d0  ; List_8
    //     0xb45d98: stur            d0, [x1, #0x17]
    // 0xb45d9c: r0 = Radius()
    //     0xb45d9c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb45da0: d0 = 30.000000
    //     0xb45da0: fmov            d0, #30.00000000
    // 0xb45da4: stur            x0, [fp, #-0x10]
    // 0xb45da8: StoreField: r0->field_7 = d0
    //     0xb45da8: stur            d0, [x0, #7]
    // 0xb45dac: StoreField: r0->field_f = d0
    //     0xb45dac: stur            d0, [x0, #0xf]
    // 0xb45db0: r0 = BorderRadius()
    //     0xb45db0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb45db4: mov             x1, x0
    // 0xb45db8: ldur            x0, [fp, #-0x10]
    // 0xb45dbc: stur            x1, [fp, #-0x40]
    // 0xb45dc0: StoreField: r1->field_7 = r0
    //     0xb45dc0: stur            w0, [x1, #7]
    // 0xb45dc4: StoreField: r1->field_b = r0
    //     0xb45dc4: stur            w0, [x1, #0xb]
    // 0xb45dc8: StoreField: r1->field_f = r0
    //     0xb45dc8: stur            w0, [x1, #0xf]
    // 0xb45dcc: StoreField: r1->field_13 = r0
    //     0xb45dcc: stur            w0, [x1, #0x13]
    // 0xb45dd0: r0 = OutlineInputBorder()
    //     0xb45dd0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb45dd4: mov             x1, x0
    // 0xb45dd8: ldur            x0, [fp, #-0x40]
    // 0xb45ddc: StoreField: r1->field_13 = r0
    //     0xb45ddc: stur            w0, [x1, #0x13]
    // 0xb45de0: d0 = 4.000000
    //     0xb45de0: fmov            d0, #4.00000000
    // 0xb45de4: StoreField: r1->field_b = d0
    //     0xb45de4: stur            d0, [x1, #0xb]
    // 0xb45de8: ldur            x0, [fp, #-0x38]
    // 0xb45dec: StoreField: r1->field_7 = r0
    //     0xb45dec: stur            w0, [x1, #7]
    // 0xb45df0: ldur            x16, [fp, #-0x30]
    // 0xb45df4: ldur            lr, [fp, #-0x28]
    // 0xb45df8: stp             lr, x16, [SP, #0x30]
    // 0xb45dfc: r16 = Instance_EdgeInsets
    //     0xb45dfc: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb45e00: ldr             x16, [x16, #0xc40]
    // 0xb45e04: r30 = "Enter WhatsApp no."
    //     0xb45e04: add             lr, PP, #0x53, lsl #12  ; [pp+0x53ec8] "Enter WhatsApp no."
    //     0xb45e08: ldr             lr, [lr, #0xec8]
    // 0xb45e0c: stp             lr, x16, [SP, #0x20]
    // 0xb45e10: ldur            x16, [fp, #-0x18]
    // 0xb45e14: ldur            lr, [fp, #-8]
    // 0xb45e18: stp             lr, x16, [SP, #0x10]
    // 0xb45e1c: r16 = 4
    //     0xb45e1c: movz            x16, #0x4
    // 0xb45e20: stp             x1, x16, [SP]
    // 0xb45e24: ldur            x1, [fp, #-0x20]
    // 0xb45e28: r4 = const [0, 0x9, 0x8, 0x1, contentPadding, 0x3, errorMaxLines, 0x7, errorStyle, 0x6, focusedBorder, 0x8, hintStyle, 0x5, hintText, 0x4, prefixIcon, 0x1, suffixIcon, 0x2, null]
    //     0xb45e28: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ed0] List(21) [0, 0x9, 0x8, 0x1, "contentPadding", 0x3, "errorMaxLines", 0x7, "errorStyle", 0x6, "focusedBorder", 0x8, "hintStyle", 0x5, "hintText", 0x4, "prefixIcon", 0x1, "suffixIcon", 0x2, Null]
    //     0xb45e2c: ldr             x4, [x4, #0xed0]
    // 0xb45e30: r0 = copyWith()
    //     0xb45e30: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb45e34: LeaveFrame
    //     0xb45e34: mov             SP, fp
    //     0xb45e38: ldp             fp, lr, [SP], #0x10
    // 0xb45e3c: ret
    //     0xb45e3c: ret             
    // 0xb45e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45e40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45e44: b               #0xb45c48
  }
  [closure] void _handlePhoneNumberChanged(dynamic, String) {
    // ** addr: 0xb45e48, size: 0x3c
    // 0xb45e48: EnterFrame
    //     0xb45e48: stp             fp, lr, [SP, #-0x10]!
    //     0xb45e4c: mov             fp, SP
    // 0xb45e50: ldr             x0, [fp, #0x18]
    // 0xb45e54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb45e54: ldur            w1, [x0, #0x17]
    // 0xb45e58: DecompressPointer r1
    //     0xb45e58: add             x1, x1, HEAP, lsl #32
    // 0xb45e5c: CheckStackOverflow
    //     0xb45e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45e60: cmp             SP, x16
    //     0xb45e64: b.ls            #0xb45e7c
    // 0xb45e68: ldr             x2, [fp, #0x10]
    // 0xb45e6c: r0 = _handlePhoneNumberChanged()
    //     0xb45e6c: bl              #0xb45e84  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged
    // 0xb45e70: LeaveFrame
    //     0xb45e70: mov             SP, fp
    //     0xb45e74: ldp             fp, lr, [SP], #0x10
    // 0xb45e78: ret
    //     0xb45e78: ret             
    // 0xb45e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45e7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45e80: b               #0xb45e68
  }
  _ _handlePhoneNumberChanged(/* No info */) {
    // ** addr: 0xb45e84, size: 0xe4
    // 0xb45e84: EnterFrame
    //     0xb45e84: stp             fp, lr, [SP, #-0x10]!
    //     0xb45e88: mov             fp, SP
    // 0xb45e8c: AllocStack(0x20)
    //     0xb45e8c: sub             SP, SP, #0x20
    // 0xb45e90: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb45e90: stur            x1, [fp, #-8]
    //     0xb45e94: stur            x2, [fp, #-0x10]
    // 0xb45e98: CheckStackOverflow
    //     0xb45e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45e9c: cmp             SP, x16
    //     0xb45ea0: b.ls            #0xb45f5c
    // 0xb45ea4: r1 = 2
    //     0xb45ea4: movz            x1, #0x2
    // 0xb45ea8: r0 = AllocateContext()
    //     0xb45ea8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb45eac: mov             x3, x0
    // 0xb45eb0: ldur            x0, [fp, #-8]
    // 0xb45eb4: stur            x3, [fp, #-0x18]
    // 0xb45eb8: StoreField: r3->field_f = r0
    //     0xb45eb8: stur            w0, [x3, #0xf]
    // 0xb45ebc: r1 = true
    //     0xb45ebc: add             x1, NULL, #0x20  ; true
    // 0xb45ec0: StoreField: r0->field_1f = r1
    //     0xb45ec0: stur            w1, [x0, #0x1f]
    // 0xb45ec4: mov             x1, x0
    // 0xb45ec8: ldur            x2, [fp, #-0x10]
    // 0xb45ecc: r0 = _isValidPhoneNumber()
    //     0xb45ecc: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0xb45ed0: ldur            x2, [fp, #-0x18]
    // 0xb45ed4: StoreField: r2->field_13 = r0
    //     0xb45ed4: stur            w0, [x2, #0x13]
    // 0xb45ed8: ldur            x3, [fp, #-8]
    // 0xb45edc: LoadField: r1 = r3->field_1b
    //     0xb45edc: ldur            w1, [x3, #0x1b]
    // 0xb45ee0: DecompressPointer r1
    //     0xb45ee0: add             x1, x1, HEAP, lsl #32
    // 0xb45ee4: cmp             w1, w0
    // 0xb45ee8: b.eq            #0xb45f04
    // 0xb45eec: r1 = Function '<anonymous closure>':.
    //     0xb45eec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56788] AnonymousClosure: (0xa08dbc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged (0xa08de0)
    //     0xb45ef0: ldr             x1, [x1, #0x788]
    // 0xb45ef4: r0 = AllocateClosure()
    //     0xb45ef4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb45ef8: ldur            x1, [fp, #-8]
    // 0xb45efc: mov             x2, x0
    // 0xb45f00: r0 = setState()
    //     0xb45f00: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb45f04: ldur            x0, [fp, #-0x10]
    // 0xb45f08: ldur            x1, [fp, #-8]
    // 0xb45f0c: r0 = _notifyParent()
    //     0xb45f0c: bl              #0x804b30  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_notifyParent
    // 0xb45f10: ldur            x0, [fp, #-0x10]
    // 0xb45f14: LoadField: r1 = r0->field_7
    //     0xb45f14: ldur            w1, [x0, #7]
    // 0xb45f18: cmp             w1, #2
    // 0xb45f1c: b.ne            #0xb45f4c
    // 0xb45f20: ldur            x0, [fp, #-8]
    // 0xb45f24: LoadField: r1 = r0->field_b
    //     0xb45f24: ldur            w1, [x0, #0xb]
    // 0xb45f28: DecompressPointer r1
    //     0xb45f28: add             x1, x1, HEAP, lsl #32
    // 0xb45f2c: cmp             w1, NULL
    // 0xb45f30: b.eq            #0xb45f64
    // 0xb45f34: LoadField: r0 = r1->field_f
    //     0xb45f34: ldur            w0, [x1, #0xf]
    // 0xb45f38: DecompressPointer r0
    //     0xb45f38: add             x0, x0, HEAP, lsl #32
    // 0xb45f3c: str             x0, [SP]
    // 0xb45f40: ClosureCall
    //     0xb45f40: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xb45f44: ldur            x2, [x0, #0x1f]
    //     0xb45f48: blr             x2
    // 0xb45f4c: r0 = Null
    //     0xb45f4c: mov             x0, NULL
    // 0xb45f50: LeaveFrame
    //     0xb45f50: mov             SP, fp
    //     0xb45f54: ldp             fp, lr, [SP], #0x10
    // 0xb45f58: ret
    //     0xb45f58: ret             
    // 0xb45f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45f5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45f60: b               #0xb45ea4
    // 0xb45f64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45f64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0xb45f68, size: 0x3c
    // 0xb45f68: EnterFrame
    //     0xb45f68: stp             fp, lr, [SP, #-0x10]!
    //     0xb45f6c: mov             fp, SP
    // 0xb45f70: ldr             x0, [fp, #0x18]
    // 0xb45f74: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb45f74: ldur            w1, [x0, #0x17]
    // 0xb45f78: DecompressPointer r1
    //     0xb45f78: add             x1, x1, HEAP, lsl #32
    // 0xb45f7c: CheckStackOverflow
    //     0xb45f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45f80: cmp             SP, x16
    //     0xb45f84: b.ls            #0xb45f9c
    // 0xb45f88: ldr             x2, [fp, #0x10]
    // 0xb45f8c: r0 = _validatePhoneNumber()
    //     0xb45f8c: bl              #0xa08f3c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber
    // 0xb45f90: LeaveFrame
    //     0xb45f90: mov             SP, fp
    //     0xb45f94: ldp             fp, lr, [SP], #0x10
    // 0xb45f98: ret
    //     0xb45f98: ret             
    // 0xb45f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45f9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45fa0: b               #0xb45f88
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc878d0, size: 0x54
    // 0xc878d0: EnterFrame
    //     0xc878d0: stp             fp, lr, [SP, #-0x10]!
    //     0xc878d4: mov             fp, SP
    // 0xc878d8: CheckStackOverflow
    //     0xc878d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc878dc: cmp             SP, x16
    //     0xc878e0: b.ls            #0xc87910
    // 0xc878e4: LoadField: r0 = r1->field_13
    //     0xc878e4: ldur            w0, [x1, #0x13]
    // 0xc878e8: DecompressPointer r0
    //     0xc878e8: add             x0, x0, HEAP, lsl #32
    // 0xc878ec: r16 = Sentinel
    //     0xc878ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc878f0: cmp             w0, w16
    // 0xc878f4: b.eq            #0xc87918
    // 0xc878f8: mov             x1, x0
    // 0xc878fc: r0 = dispose()
    //     0xc878fc: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87900: r0 = Null
    //     0xc87900: mov             x0, NULL
    // 0xc87904: LeaveFrame
    //     0xc87904: mov             SP, fp
    //     0xc87908: ldp             fp, lr, [SP], #0x10
    // 0xc8790c: ret
    //     0xc8790c: ret             
    // 0xc87910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87910: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87914: b               #0xc878e4
    // 0xc87918: r9 = _numberController
    //     0xc87918: add             x9, PP, #0x56, lsl #12  ; [pp+0x56780] Field <_CheckoutNumberWidgetState@1552288663._numberController@1552288663>: late final (offset: 0x14)
    //     0xc8791c: ldr             x9, [x9, #0x780]
    // 0xc87920: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87920: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4104, size: 0x1c, field offset: 0xc
//   const constructor, 
class CheckoutNumberWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7eabc, size: 0x60
    // 0xc7eabc: EnterFrame
    //     0xc7eabc: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eac0: mov             fp, SP
    // 0xc7eac4: AllocStack(0x8)
    //     0xc7eac4: sub             SP, SP, #8
    // 0xc7eac8: SetupParameters(CheckoutNumberWidget this /* r1 => r0 */)
    //     0xc7eac8: mov             x0, x1
    // 0xc7eacc: r1 = <CheckoutNumberWidget>
    //     0xc7eacc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a00] TypeArguments: <CheckoutNumberWidget>
    //     0xc7ead0: ldr             x1, [x1, #0xa00]
    // 0xc7ead4: r0 = _CheckoutNumberWidgetState()
    //     0xc7ead4: bl              #0xc7eb1c  ; Allocate_CheckoutNumberWidgetStateStub -> _CheckoutNumberWidgetState (size=0x28)
    // 0xc7ead8: mov             x2, x0
    // 0xc7eadc: r0 = Sentinel
    //     0xc7eadc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7eae0: stur            x2, [fp, #-8]
    // 0xc7eae4: StoreField: r2->field_13 = r0
    //     0xc7eae4: stur            w0, [x2, #0x13]
    // 0xc7eae8: r0 = false
    //     0xc7eae8: add             x0, NULL, #0x30  ; false
    // 0xc7eaec: StoreField: r2->field_1b = r0
    //     0xc7eaec: stur            w0, [x2, #0x1b]
    // 0xc7eaf0: StoreField: r2->field_1f = r0
    //     0xc7eaf0: stur            w0, [x2, #0x1f]
    // 0xc7eaf4: StoreField: r2->field_23 = r0
    //     0xc7eaf4: stur            w0, [x2, #0x23]
    // 0xc7eaf8: r1 = <FormState>
    //     0xc7eaf8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0xc7eafc: ldr             x1, [x1, #0xad8]
    // 0xc7eb00: r0 = LabeledGlobalKey()
    //     0xc7eb00: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc7eb04: mov             x1, x0
    // 0xc7eb08: ldur            x0, [fp, #-8]
    // 0xc7eb0c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7eb0c: stur            w1, [x0, #0x17]
    // 0xc7eb10: LeaveFrame
    //     0xc7eb10: mov             SP, fp
    //     0xc7eb14: ldp             fp, lr, [SP], #0x10
    // 0xc7eb18: ret
    //     0xc7eb18: ret             
  }
}
