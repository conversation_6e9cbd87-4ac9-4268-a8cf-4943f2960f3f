// lib: , url: package:dio/src/transformers/util/transform_empty_to_null.dart

// class id: 1049618, size: 0x8
class :: {
}

// class id: 4970, size: 0x10, field offset: 0x8
class _DefaultIfEmptyStreamSink extends Object
    implements EventSink<X0> {

  static late final Uint8List _nullUtf8Value; // offset: 0xec8

  dynamic add(dynamic) {
    // ** addr: 0x6511ec, size: 0x24
    // 0x6511ec: EnterFrame
    //     0x6511ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6511f0: mov             fp, SP
    // 0x6511f4: ldr             x2, [fp, #0x10]
    // 0x6511f8: r1 = Function 'add':.
    //     0x6511f8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ae40] AnonymousClosure: (0x651210), in [package:dio/src/transformers/util/transform_empty_to_null.dart] _DefaultIfEmptyStreamSink::add (0x678334)
    //     0x6511fc: ldr             x1, [x1, #0xe40]
    // 0x651200: r0 = AllocateClosure()
    //     0x651200: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x651204: LeaveFrame
    //     0x651204: mov             SP, fp
    //     0x651208: ldp             fp, lr, [SP], #0x10
    // 0x65120c: ret
    //     0x65120c: ret             
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x651210, size: 0x3c
    // 0x651210: EnterFrame
    //     0x651210: stp             fp, lr, [SP, #-0x10]!
    //     0x651214: mov             fp, SP
    // 0x651218: ldr             x0, [fp, #0x18]
    // 0x65121c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x65121c: ldur            w1, [x0, #0x17]
    // 0x651220: DecompressPointer r1
    //     0x651220: add             x1, x1, HEAP, lsl #32
    // 0x651224: CheckStackOverflow
    //     0x651224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x651228: cmp             SP, x16
    //     0x65122c: b.ls            #0x651244
    // 0x651230: ldr             x2, [fp, #0x10]
    // 0x651234: r0 = add()
    //     0x651234: bl              #0x678334  ; [package:dio/src/transformers/util/transform_empty_to_null.dart] _DefaultIfEmptyStreamSink::add
    // 0x651238: LeaveFrame
    //     0x651238: mov             SP, fp
    //     0x65123c: ldp             fp, lr, [SP], #0x10
    // 0x651240: ret
    //     0x651240: ret             
    // 0x651244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x651244: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x651248: b               #0x651230
  }
  _ add(/* No info */) {
    // ** addr: 0x678334, size: 0xdc
    // 0x678334: EnterFrame
    //     0x678334: stp             fp, lr, [SP, #-0x10]!
    //     0x678338: mov             fp, SP
    // 0x67833c: AllocStack(0x10)
    //     0x67833c: sub             SP, SP, #0x10
    // 0x678340: SetupParameters(_DefaultIfEmptyStreamSink this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x678340: mov             x4, x1
    //     0x678344: mov             x3, x2
    //     0x678348: stur            x1, [fp, #-8]
    //     0x67834c: stur            x2, [fp, #-0x10]
    // 0x678350: CheckStackOverflow
    //     0x678350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x678354: cmp             SP, x16
    //     0x678358: b.ls            #0x678408
    // 0x67835c: mov             x0, x3
    // 0x678360: r2 = Null
    //     0x678360: mov             x2, NULL
    // 0x678364: r1 = Null
    //     0x678364: mov             x1, NULL
    // 0x678368: r4 = 60
    //     0x678368: movz            x4, #0x3c
    // 0x67836c: branchIfSmi(r0, 0x678378)
    //     0x67836c: tbz             w0, #0, #0x678378
    // 0x678370: r4 = LoadClassIdInstr(r0)
    //     0x678370: ldur            x4, [x0, #-1]
    //     0x678374: ubfx            x4, x4, #0xc, #0x14
    // 0x678378: sub             x4, x4, #0x74
    // 0x67837c: cmp             x4, #3
    // 0x678380: b.ls            #0x678398
    // 0x678384: r8 = Uint8List
    //     0x678384: add             x8, PP, #8, lsl #12  ; [pp+0x89a0] Type: Uint8List
    //     0x678388: ldr             x8, [x8, #0x9a0]
    // 0x67838c: r3 = Null
    //     0x67838c: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2ae58] Null
    //     0x678390: ldr             x3, [x3, #0xe58]
    // 0x678394: r0 = Uint8List()
    //     0x678394: bl              #0x61da58  ; IsType_Uint8List_Stub
    // 0x678398: ldur            x0, [fp, #-8]
    // 0x67839c: LoadField: r1 = r0->field_b
    //     0x67839c: ldur            w1, [x0, #0xb]
    // 0x6783a0: DecompressPointer r1
    //     0x6783a0: add             x1, x1, HEAP, lsl #32
    // 0x6783a4: tbnz            w1, #4, #0x6783b4
    // 0x6783a8: ldur            x2, [fp, #-0x10]
    // 0x6783ac: r1 = true
    //     0x6783ac: add             x1, NULL, #0x20  ; true
    // 0x6783b0: b               #0x6783d0
    // 0x6783b4: ldur            x2, [fp, #-0x10]
    // 0x6783b8: LoadField: r1 = r2->field_13
    //     0x6783b8: ldur            w1, [x2, #0x13]
    // 0x6783bc: cbnz            w1, #0x6783c8
    // 0x6783c0: r3 = false
    //     0x6783c0: add             x3, NULL, #0x30  ; false
    // 0x6783c4: b               #0x6783cc
    // 0x6783c8: r3 = true
    //     0x6783c8: add             x3, NULL, #0x20  ; true
    // 0x6783cc: mov             x1, x3
    // 0x6783d0: StoreField: r0->field_b = r1
    //     0x6783d0: stur            w1, [x0, #0xb]
    // 0x6783d4: LoadField: r1 = r0->field_7
    //     0x6783d4: ldur            w1, [x0, #7]
    // 0x6783d8: DecompressPointer r1
    //     0x6783d8: add             x1, x1, HEAP, lsl #32
    // 0x6783dc: r0 = LoadClassIdInstr(r1)
    //     0x6783dc: ldur            x0, [x1, #-1]
    //     0x6783e0: ubfx            x0, x0, #0xc, #0x14
    // 0x6783e4: r0 = GDT[cid_x0 + 0x11a81]()
    //     0x6783e4: movz            x17, #0x1a81
    //     0x6783e8: movk            x17, #0x1, lsl #16
    //     0x6783ec: add             lr, x0, x17
    //     0x6783f0: ldr             lr, [x21, lr, lsl #3]
    //     0x6783f4: blr             lr
    // 0x6783f8: r0 = Null
    //     0x6783f8: mov             x0, NULL
    // 0x6783fc: LeaveFrame
    //     0x6783fc: mov             SP, fp
    //     0x678400: ldp             fp, lr, [SP], #0x10
    // 0x678404: ret
    //     0x678404: ret             
    // 0x678408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x678408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67840c: b               #0x67835c
  }
  _ addError(/* No info */) {
    // ** addr: 0x70a968, size: 0x80
    // 0x70a968: EnterFrame
    //     0x70a968: stp             fp, lr, [SP, #-0x10]!
    //     0x70a96c: mov             fp, SP
    // 0x70a970: AllocStack(0x8)
    //     0x70a970: sub             SP, SP, #8
    // 0x70a974: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x70a974: ldur            w0, [x4, #0x13]
    //     0x70a978: sub             x3, x0, #4
    //     0x70a97c: cmp             w3, #2
    //     0x70a980: b.lt            #0x70a990
    //     0x70a984: add             x0, fp, w3, sxtw #2
    //     0x70a988: ldr             x0, [x0, #8]
    //     0x70a98c: b               #0x70a994
    //     0x70a990: mov             x0, NULL
    // 0x70a994: CheckStackOverflow
    //     0x70a994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x70a998: cmp             SP, x16
    //     0x70a99c: b.ls            #0x70a9e0
    // 0x70a9a0: LoadField: r3 = r1->field_7
    //     0x70a9a0: ldur            w3, [x1, #7]
    // 0x70a9a4: DecompressPointer r3
    //     0x70a9a4: add             x3, x3, HEAP, lsl #32
    // 0x70a9a8: r1 = LoadClassIdInstr(r3)
    //     0x70a9a8: ldur            x1, [x3, #-1]
    //     0x70a9ac: ubfx            x1, x1, #0xc, #0x14
    // 0x70a9b0: str             x0, [SP]
    // 0x70a9b4: mov             x0, x1
    // 0x70a9b8: mov             x1, x3
    // 0x70a9bc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x70a9bc: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x70a9c0: r0 = GDT[cid_x0 + 0x11295]()
    //     0x70a9c0: movz            x17, #0x1295
    //     0x70a9c4: movk            x17, #0x1, lsl #16
    //     0x70a9c8: add             lr, x0, x17
    //     0x70a9cc: ldr             lr, [x21, lr, lsl #3]
    //     0x70a9d0: blr             lr
    // 0x70a9d4: LeaveFrame
    //     0x70a9d4: mov             SP, fp
    //     0x70a9d8: ldp             fp, lr, [SP], #0x10
    // 0x70a9dc: ret
    //     0x70a9dc: ret             
    // 0x70a9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x70a9e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x70a9e4: b               #0x70a9a0
  }
  _ close(/* No info */) {
    // ** addr: 0x7125b8, size: 0xc0
    // 0x7125b8: EnterFrame
    //     0x7125b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7125bc: mov             fp, SP
    // 0x7125c0: AllocStack(0x10)
    //     0x7125c0: sub             SP, SP, #0x10
    // 0x7125c4: SetupParameters(_DefaultIfEmptyStreamSink this /* r1 => r1, fp-0x10 */)
    //     0x7125c4: stur            x1, [fp, #-0x10]
    // 0x7125c8: CheckStackOverflow
    //     0x7125c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7125cc: cmp             SP, x16
    //     0x7125d0: b.ls            #0x712670
    // 0x7125d4: LoadField: r0 = r1->field_b
    //     0x7125d4: ldur            w0, [x1, #0xb]
    // 0x7125d8: DecompressPointer r0
    //     0x7125d8: add             x0, x0, HEAP, lsl #32
    // 0x7125dc: tbz             w0, #4, #0x712638
    // 0x7125e0: LoadField: r0 = r1->field_7
    //     0x7125e0: ldur            w0, [x1, #7]
    // 0x7125e4: DecompressPointer r0
    //     0x7125e4: add             x0, x0, HEAP, lsl #32
    // 0x7125e8: stur            x0, [fp, #-8]
    // 0x7125ec: r0 = InitLateStaticField(0xec8) // [package:dio/src/transformers/util/transform_empty_to_null.dart] _DefaultIfEmptyStreamSink::_nullUtf8Value
    //     0x7125ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7125f0: ldr             x0, [x0, #0x1d90]
    //     0x7125f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7125f8: cmp             w0, w16
    //     0x7125fc: b.ne            #0x71260c
    //     0x712600: add             x2, PP, #0x2a, lsl #12  ; [pp+0x2ae48] Field <_DefaultIfEmptyStreamSink@1786287173._nullUtf8Value@1786287173>: static late final (offset: 0xec8)
    //     0x712604: ldr             x2, [x2, #0xe48]
    //     0x712608: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x71260c: ldur            x1, [fp, #-8]
    // 0x712610: r2 = LoadClassIdInstr(r1)
    //     0x712610: ldur            x2, [x1, #-1]
    //     0x712614: ubfx            x2, x2, #0xc, #0x14
    // 0x712618: mov             x16, x0
    // 0x71261c: mov             x0, x2
    // 0x712620: mov             x2, x16
    // 0x712624: r0 = GDT[cid_x0 + 0x11a81]()
    //     0x712624: movz            x17, #0x1a81
    //     0x712628: movk            x17, #0x1, lsl #16
    //     0x71262c: add             lr, x0, x17
    //     0x712630: ldr             lr, [x21, lr, lsl #3]
    //     0x712634: blr             lr
    // 0x712638: ldur            x0, [fp, #-0x10]
    // 0x71263c: LoadField: r1 = r0->field_7
    //     0x71263c: ldur            w1, [x0, #7]
    // 0x712640: DecompressPointer r1
    //     0x712640: add             x1, x1, HEAP, lsl #32
    // 0x712644: r0 = LoadClassIdInstr(r1)
    //     0x712644: ldur            x0, [x1, #-1]
    //     0x712648: ubfx            x0, x0, #0xc, #0x14
    // 0x71264c: r0 = GDT[cid_x0 + 0x10f21]()
    //     0x71264c: movz            x17, #0xf21
    //     0x712650: movk            x17, #0x1, lsl #16
    //     0x712654: add             lr, x0, x17
    //     0x712658: ldr             lr, [x21, lr, lsl #3]
    //     0x71265c: blr             lr
    // 0x712660: r0 = Null
    //     0x712660: mov             x0, NULL
    // 0x712664: LeaveFrame
    //     0x712664: mov             SP, fp
    //     0x712668: ldp             fp, lr, [SP], #0x10
    // 0x71266c: ret
    //     0x71266c: ret             
    // 0x712670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x712670: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x712674: b               #0x7125d4
  }
  static Uint8List _nullUtf8Value() {
    // ** addr: 0x712678, size: 0x58
    // 0x712678: EnterFrame
    //     0x712678: stp             fp, lr, [SP, #-0x10]!
    //     0x71267c: mov             fp, SP
    // 0x712680: AllocStack(0x8)
    //     0x712680: sub             SP, SP, #8
    // 0x712684: CheckStackOverflow
    //     0x712684: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x712688: cmp             SP, x16
    //     0x71268c: b.ls            #0x7126c8
    // 0x712690: r4 = 8
    //     0x712690: movz            x4, #0x8
    // 0x712694: r0 = AllocateUint8Array()
    //     0x712694: bl              #0x16f6e7c  ; AllocateUint8ArrayStub
    // 0x712698: mov             x1, x0
    // 0x71269c: r2 = 0
    //     0x71269c: movz            x2, #0
    // 0x7126a0: r3 = 4
    //     0x7126a0: movz            x3, #0x4
    // 0x7126a4: r5 = const [0x6e, 0x75, 0x6c, 0x6c]
    //     0x7126a4: add             x5, PP, #0x2a, lsl #12  ; [pp+0x2ae50] List<int>(4)
    //     0x7126a8: ldr             x5, [x5, #0xe50]
    // 0x7126ac: r6 = 0
    //     0x7126ac: movz            x6, #0
    // 0x7126b0: stur            x0, [fp, #-8]
    // 0x7126b4: r0 = _slowSetRange()
    //     0x7126b4: bl              #0x1535c5c  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x7126b8: ldur            x0, [fp, #-8]
    // 0x7126bc: LeaveFrame
    //     0x7126bc: mov             SP, fp
    //     0x7126c0: ldp             fp, lr, [SP], #0x10
    // 0x7126c4: ret
    //     0x7126c4: ret             
    // 0x7126c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7126c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7126cc: b               #0x712690
  }
}

// class id: 6542, size: 0xc, field offset: 0xc
//   const constructor, 
class DefaultNullIfEmptyStreamTransformer extends StreamTransformerBase<dynamic, dynamic> {

  _ bind(/* No info */) {
    // ** addr: 0x1608d28, size: 0x7c
    // 0x1608d28: EnterFrame
    //     0x1608d28: stp             fp, lr, [SP, #-0x10]!
    //     0x1608d2c: mov             fp, SP
    // 0x1608d30: AllocStack(0x10)
    //     0x1608d30: sub             SP, SP, #0x10
    // 0x1608d34: SetupParameters(DefaultNullIfEmptyStreamTransformer this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x1608d34: mov             x0, x2
    //     0x1608d38: mov             x4, x1
    //     0x1608d3c: mov             x3, x2
    //     0x1608d40: stur            x2, [fp, #-8]
    // 0x1608d44: r2 = Null
    //     0x1608d44: mov             x2, NULL
    // 0x1608d48: r1 = Null
    //     0x1608d48: mov             x1, NULL
    // 0x1608d4c: r8 = Stream<Uint8List>
    //     0x1608d4c: add             x8, PP, #0x11, lsl #12  ; [pp+0x11c40] Type: Stream<Uint8List>
    //     0x1608d50: ldr             x8, [x8, #0xc40]
    // 0x1608d54: r3 = Null
    //     0x1608d54: add             x3, PP, #0x11, lsl #12  ; [pp+0x11c48] Null
    //     0x1608d58: ldr             x3, [x3, #0xc48]
    // 0x1608d5c: r0 = Stream<Uint8List>()
    //     0x1608d5c: bl              #0x63508c  ; IsType_Stream<Uint8List>_Stub
    // 0x1608d60: r1 = <Uint8List, dynamic, Uint8List>
    //     0x1608d60: add             x1, PP, #0x11, lsl #12  ; [pp+0x11c58] TypeArguments: <Uint8List, dynamic, Uint8List>
    //     0x1608d64: ldr             x1, [x1, #0xc58]
    // 0x1608d68: r0 = _BoundSinkStream()
    //     0x1608d68: bl              #0x1608b3c  ; Allocate_BoundSinkStreamStub -> _BoundSinkStream<C1X0, C1X1> (size=0x14)
    // 0x1608d6c: mov             x3, x0
    // 0x1608d70: ldur            x0, [fp, #-8]
    // 0x1608d74: stur            x3, [fp, #-0x10]
    // 0x1608d78: StoreField: r3->field_f = r0
    //     0x1608d78: stur            w0, [x3, #0xf]
    // 0x1608d7c: r1 = Function '<anonymous closure>':.
    //     0x1608d7c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11c60] AnonymousClosure: (0x1608da4), in [package:dio/src/transformers/util/transform_empty_to_null.dart] DefaultNullIfEmptyStreamTransformer::bind (0x1608d28)
    //     0x1608d80: ldr             x1, [x1, #0xc60]
    // 0x1608d84: r2 = Null
    //     0x1608d84: mov             x2, NULL
    // 0x1608d88: r0 = AllocateClosure()
    //     0x1608d88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1608d8c: mov             x1, x0
    // 0x1608d90: ldur            x0, [fp, #-0x10]
    // 0x1608d94: StoreField: r0->field_b = r1
    //     0x1608d94: stur            w1, [x0, #0xb]
    // 0x1608d98: LeaveFrame
    //     0x1608d98: mov             SP, fp
    //     0x1608d9c: ldp             fp, lr, [SP], #0x10
    // 0x1608da0: ret
    //     0x1608da0: ret             
  }
  [closure] _DefaultIfEmptyStreamSink <anonymous closure>(dynamic, EventSink<Uint8List>) {
    // ** addr: 0x1608da4, size: 0x28
    // 0x1608da4: EnterFrame
    //     0x1608da4: stp             fp, lr, [SP, #-0x10]!
    //     0x1608da8: mov             fp, SP
    // 0x1608dac: r0 = _DefaultIfEmptyStreamSink()
    //     0x1608dac: bl              #0x1608dcc  ; Allocate_DefaultIfEmptyStreamSinkStub -> _DefaultIfEmptyStreamSink (size=0x10)
    // 0x1608db0: r1 = false
    //     0x1608db0: add             x1, NULL, #0x30  ; false
    // 0x1608db4: StoreField: r0->field_b = r1
    //     0x1608db4: stur            w1, [x0, #0xb]
    // 0x1608db8: ldr             x1, [fp, #0x10]
    // 0x1608dbc: StoreField: r0->field_7 = r1
    //     0x1608dbc: stur            w1, [x0, #7]
    // 0x1608dc0: LeaveFrame
    //     0x1608dc0: mov             SP, fp
    //     0x1608dc4: ldp             fp, lr, [SP], #0x10
    // 0x1608dc8: ret
    //     0x1608dc8: ret             
  }
}
