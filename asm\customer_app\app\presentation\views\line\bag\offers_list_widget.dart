// lib: , url: package:customer_app/app/presentation/views/line/bag/offers_list_widget.dart

// class id: 1049472, size: 0x8
class :: {
}

// class id: 3286, size: 0x14, field offset: 0x14
class _OffersListWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xba9db8, size: 0x4e8
    // 0xba9db8: EnterFrame
    //     0xba9db8: stp             fp, lr, [SP, #-0x10]!
    //     0xba9dbc: mov             fp, SP
    // 0xba9dc0: AllocStack(0x50)
    //     0xba9dc0: sub             SP, SP, #0x50
    // 0xba9dc4: SetupParameters(_OffersListWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba9dc4: mov             x0, x1
    //     0xba9dc8: stur            x1, [fp, #-8]
    //     0xba9dcc: mov             x1, x2
    //     0xba9dd0: stur            x2, [fp, #-0x10]
    // 0xba9dd4: CheckStackOverflow
    //     0xba9dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba9dd8: cmp             SP, x16
    //     0xba9ddc: b.ls            #0xbaa290
    // 0xba9de0: r1 = 1
    //     0xba9de0: movz            x1, #0x1
    // 0xba9de4: r0 = AllocateContext()
    //     0xba9de4: bl              #0x16f6108  ; AllocateContextStub
    // 0xba9de8: mov             x1, x0
    // 0xba9dec: ldur            x0, [fp, #-8]
    // 0xba9df0: stur            x1, [fp, #-0x30]
    // 0xba9df4: StoreField: r1->field_f = r0
    //     0xba9df4: stur            w0, [x1, #0xf]
    // 0xba9df8: LoadField: r2 = r0->field_b
    //     0xba9df8: ldur            w2, [x0, #0xb]
    // 0xba9dfc: DecompressPointer r2
    //     0xba9dfc: add             x2, x2, HEAP, lsl #32
    // 0xba9e00: stur            x2, [fp, #-0x28]
    // 0xba9e04: cmp             w2, NULL
    // 0xba9e08: b.eq            #0xbaa298
    // 0xba9e0c: LoadField: r3 = r2->field_23
    //     0xba9e0c: ldur            w3, [x2, #0x23]
    // 0xba9e10: DecompressPointer r3
    //     0xba9e10: add             x3, x3, HEAP, lsl #32
    // 0xba9e14: stur            x3, [fp, #-0x20]
    // 0xba9e18: LoadField: r4 = r2->field_b
    //     0xba9e18: ldur            w4, [x2, #0xb]
    // 0xba9e1c: DecompressPointer r4
    //     0xba9e1c: add             x4, x4, HEAP, lsl #32
    // 0xba9e20: cmp             w4, NULL
    // 0xba9e24: b.ne            #0xba9e30
    // 0xba9e28: r4 = Null
    //     0xba9e28: mov             x4, NULL
    // 0xba9e2c: b               #0xba9e3c
    // 0xba9e30: LoadField: r5 = r4->field_f
    //     0xba9e30: ldur            w5, [x4, #0xf]
    // 0xba9e34: DecompressPointer r5
    //     0xba9e34: add             x5, x5, HEAP, lsl #32
    // 0xba9e38: LoadField: r4 = r5->field_b
    //     0xba9e38: ldur            w4, [x5, #0xb]
    // 0xba9e3c: stur            x4, [fp, #-0x18]
    // 0xba9e40: LoadField: r5 = r2->field_27
    //     0xba9e40: ldur            w5, [x2, #0x27]
    // 0xba9e44: DecompressPointer r5
    //     0xba9e44: add             x5, x5, HEAP, lsl #32
    // 0xba9e48: r16 = "false"
    //     0xba9e48: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0xba9e4c: ldr             x16, [x16, #0xed8]
    // 0xba9e50: stp             x16, x5, [SP]
    // 0xba9e54: r0 = ==()
    //     0xba9e54: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xba9e58: tbnz            w0, #4, #0xba9e64
    // 0xba9e5c: r4 = false
    //     0xba9e5c: add             x4, NULL, #0x30  ; false
    // 0xba9e60: b               #0xba9e68
    // 0xba9e64: r4 = true
    //     0xba9e64: add             x4, NULL, #0x20  ; true
    // 0xba9e68: ldur            x0, [fp, #-8]
    // 0xba9e6c: ldur            x2, [fp, #-0x20]
    // 0xba9e70: ldur            x3, [fp, #-0x18]
    // 0xba9e74: ldur            x1, [fp, #-0x28]
    // 0xba9e78: stur            x4, [fp, #-0x38]
    // 0xba9e7c: r0 = EventData()
    //     0xba9e7c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xba9e80: mov             x1, x0
    // 0xba9e84: ldur            x0, [fp, #-0x20]
    // 0xba9e88: stur            x1, [fp, #-0x40]
    // 0xba9e8c: StoreField: r1->field_87 = r0
    //     0xba9e8c: stur            w0, [x1, #0x87]
    // 0xba9e90: ldur            x0, [fp, #-0x18]
    // 0xba9e94: StoreField: r1->field_9b = r0
    //     0xba9e94: stur            w0, [x1, #0x9b]
    // 0xba9e98: ldur            x0, [fp, #-0x38]
    // 0xba9e9c: StoreField: r1->field_9f = r0
    //     0xba9e9c: stur            w0, [x1, #0x9f]
    // 0xba9ea0: r0 = EventsRequest()
    //     0xba9ea0: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xba9ea4: mov             x1, x0
    // 0xba9ea8: r0 = "offer_clicked"
    //     0xba9ea8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b58] "offer_clicked"
    //     0xba9eac: ldr             x0, [x0, #0xb58]
    // 0xba9eb0: StoreField: r1->field_7 = r0
    //     0xba9eb0: stur            w0, [x1, #7]
    // 0xba9eb4: ldur            x0, [fp, #-0x40]
    // 0xba9eb8: StoreField: r1->field_b = r0
    //     0xba9eb8: stur            w0, [x1, #0xb]
    // 0xba9ebc: ldur            x0, [fp, #-0x28]
    // 0xba9ec0: LoadField: r2 = r0->field_1f
    //     0xba9ec0: ldur            w2, [x0, #0x1f]
    // 0xba9ec4: DecompressPointer r2
    //     0xba9ec4: add             x2, x2, HEAP, lsl #32
    // 0xba9ec8: stp             x1, x2, [SP]
    // 0xba9ecc: r4 = 0
    //     0xba9ecc: movz            x4, #0
    // 0xba9ed0: ldr             x0, [SP, #8]
    // 0xba9ed4: r16 = UnlinkedCall_0x613b5c
    //     0xba9ed4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54b60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba9ed8: add             x16, x16, #0xb60
    // 0xba9edc: ldp             x5, lr, [x16]
    // 0xba9ee0: blr             lr
    // 0xba9ee4: ldur            x1, [fp, #-0x10]
    // 0xba9ee8: r0 = of()
    //     0xba9ee8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba9eec: LoadField: r1 = r0->field_87
    //     0xba9eec: ldur            w1, [x0, #0x87]
    // 0xba9ef0: DecompressPointer r1
    //     0xba9ef0: add             x1, x1, HEAP, lsl #32
    // 0xba9ef4: LoadField: r0 = r1->field_23
    //     0xba9ef4: ldur            w0, [x1, #0x23]
    // 0xba9ef8: DecompressPointer r0
    //     0xba9ef8: add             x0, x0, HEAP, lsl #32
    // 0xba9efc: r16 = Instance_Color
    //     0xba9efc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba9f00: r30 = 21.000000
    //     0xba9f00: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xba9f04: ldr             lr, [lr, #0x9b0]
    // 0xba9f08: stp             lr, x16, [SP]
    // 0xba9f0c: mov             x1, x0
    // 0xba9f10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba9f10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba9f14: ldr             x4, [x4, #0x9b8]
    // 0xba9f18: r0 = copyWith()
    //     0xba9f18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba9f1c: stur            x0, [fp, #-0x10]
    // 0xba9f20: r0 = Text()
    //     0xba9f20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba9f24: mov             x1, x0
    // 0xba9f28: r0 = "ALL OFFERS & COUPONS"
    //     0xba9f28: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b70] "ALL OFFERS & COUPONS"
    //     0xba9f2c: ldr             x0, [x0, #0xb70]
    // 0xba9f30: stur            x1, [fp, #-0x18]
    // 0xba9f34: StoreField: r1->field_b = r0
    //     0xba9f34: stur            w0, [x1, #0xb]
    // 0xba9f38: ldur            x0, [fp, #-0x10]
    // 0xba9f3c: StoreField: r1->field_13 = r0
    //     0xba9f3c: stur            w0, [x1, #0x13]
    // 0xba9f40: r0 = Padding()
    //     0xba9f40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba9f44: mov             x2, x0
    // 0xba9f48: r0 = Instance_EdgeInsets
    //     0xba9f48: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xba9f4c: ldr             x0, [x0, #0xf70]
    // 0xba9f50: stur            x2, [fp, #-0x10]
    // 0xba9f54: StoreField: r2->field_f = r0
    //     0xba9f54: stur            w0, [x2, #0xf]
    // 0xba9f58: ldur            x0, [fp, #-0x18]
    // 0xba9f5c: StoreField: r2->field_b = r0
    //     0xba9f5c: stur            w0, [x2, #0xb]
    // 0xba9f60: r1 = <FlexParentData>
    //     0xba9f60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba9f64: ldr             x1, [x1, #0xe00]
    // 0xba9f68: r0 = Expanded()
    //     0xba9f68: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba9f6c: stur            x0, [fp, #-0x18]
    // 0xba9f70: StoreField: r0->field_13 = rZR
    //     0xba9f70: stur            xzr, [x0, #0x13]
    // 0xba9f74: r1 = Instance_FlexFit
    //     0xba9f74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba9f78: ldr             x1, [x1, #0xe08]
    // 0xba9f7c: StoreField: r0->field_1b = r1
    //     0xba9f7c: stur            w1, [x0, #0x1b]
    // 0xba9f80: ldur            x2, [fp, #-0x10]
    // 0xba9f84: StoreField: r0->field_b = r2
    //     0xba9f84: stur            w2, [x0, #0xb]
    // 0xba9f88: r0 = InkWell()
    //     0xba9f88: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba9f8c: mov             x3, x0
    // 0xba9f90: r0 = Instance_Icon
    //     0xba9f90: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xba9f94: ldr             x0, [x0, #0x2b8]
    // 0xba9f98: stur            x3, [fp, #-0x10]
    // 0xba9f9c: StoreField: r3->field_b = r0
    //     0xba9f9c: stur            w0, [x3, #0xb]
    // 0xba9fa0: r1 = Function '<anonymous closure>':.
    //     0xba9fa0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b78] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xba9fa4: ldr             x1, [x1, #0xb78]
    // 0xba9fa8: r2 = Null
    //     0xba9fa8: mov             x2, NULL
    // 0xba9fac: r0 = AllocateClosure()
    //     0xba9fac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba9fb0: mov             x1, x0
    // 0xba9fb4: ldur            x0, [fp, #-0x10]
    // 0xba9fb8: StoreField: r0->field_f = r1
    //     0xba9fb8: stur            w1, [x0, #0xf]
    // 0xba9fbc: r1 = true
    //     0xba9fbc: add             x1, NULL, #0x20  ; true
    // 0xba9fc0: StoreField: r0->field_43 = r1
    //     0xba9fc0: stur            w1, [x0, #0x43]
    // 0xba9fc4: r2 = Instance_BoxShape
    //     0xba9fc4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba9fc8: ldr             x2, [x2, #0x80]
    // 0xba9fcc: StoreField: r0->field_47 = r2
    //     0xba9fcc: stur            w2, [x0, #0x47]
    // 0xba9fd0: StoreField: r0->field_6f = r1
    //     0xba9fd0: stur            w1, [x0, #0x6f]
    // 0xba9fd4: r2 = false
    //     0xba9fd4: add             x2, NULL, #0x30  ; false
    // 0xba9fd8: StoreField: r0->field_73 = r2
    //     0xba9fd8: stur            w2, [x0, #0x73]
    // 0xba9fdc: StoreField: r0->field_83 = r1
    //     0xba9fdc: stur            w1, [x0, #0x83]
    // 0xba9fe0: StoreField: r0->field_7b = r2
    //     0xba9fe0: stur            w2, [x0, #0x7b]
    // 0xba9fe4: r0 = Padding()
    //     0xba9fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba9fe8: mov             x2, x0
    // 0xba9fec: r0 = Instance_EdgeInsets
    //     0xba9fec: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xba9ff0: ldr             x0, [x0, #0x268]
    // 0xba9ff4: stur            x2, [fp, #-0x20]
    // 0xba9ff8: StoreField: r2->field_f = r0
    //     0xba9ff8: stur            w0, [x2, #0xf]
    // 0xba9ffc: ldur            x0, [fp, #-0x10]
    // 0xbaa000: StoreField: r2->field_b = r0
    //     0xbaa000: stur            w0, [x2, #0xb]
    // 0xbaa004: r1 = <FlexParentData>
    //     0xbaa004: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbaa008: ldr             x1, [x1, #0xe00]
    // 0xbaa00c: r0 = Expanded()
    //     0xbaa00c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbaa010: stur            x0, [fp, #-0x10]
    // 0xbaa014: StoreField: r0->field_13 = rZR
    //     0xbaa014: stur            xzr, [x0, #0x13]
    // 0xbaa018: r1 = Instance_FlexFit
    //     0xbaa018: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbaa01c: ldr             x1, [x1, #0xe08]
    // 0xbaa020: StoreField: r0->field_1b = r1
    //     0xbaa020: stur            w1, [x0, #0x1b]
    // 0xbaa024: ldur            x1, [fp, #-0x20]
    // 0xbaa028: StoreField: r0->field_b = r1
    //     0xbaa028: stur            w1, [x0, #0xb]
    // 0xbaa02c: r1 = Null
    //     0xbaa02c: mov             x1, NULL
    // 0xbaa030: r2 = 6
    //     0xbaa030: movz            x2, #0x6
    // 0xbaa034: r0 = AllocateArray()
    //     0xbaa034: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaa038: mov             x2, x0
    // 0xbaa03c: ldur            x0, [fp, #-0x18]
    // 0xbaa040: stur            x2, [fp, #-0x20]
    // 0xbaa044: StoreField: r2->field_f = r0
    //     0xbaa044: stur            w0, [x2, #0xf]
    // 0xbaa048: r16 = Instance_Spacer
    //     0xbaa048: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbaa04c: ldr             x16, [x16, #0xf0]
    // 0xbaa050: StoreField: r2->field_13 = r16
    //     0xbaa050: stur            w16, [x2, #0x13]
    // 0xbaa054: ldur            x0, [fp, #-0x10]
    // 0xbaa058: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaa058: stur            w0, [x2, #0x17]
    // 0xbaa05c: r1 = <Widget>
    //     0xbaa05c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaa060: r0 = AllocateGrowableArray()
    //     0xbaa060: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaa064: mov             x1, x0
    // 0xbaa068: ldur            x0, [fp, #-0x20]
    // 0xbaa06c: stur            x1, [fp, #-0x10]
    // 0xbaa070: StoreField: r1->field_f = r0
    //     0xbaa070: stur            w0, [x1, #0xf]
    // 0xbaa074: r0 = 6
    //     0xbaa074: movz            x0, #0x6
    // 0xbaa078: StoreField: r1->field_b = r0
    //     0xbaa078: stur            w0, [x1, #0xb]
    // 0xbaa07c: r0 = Row()
    //     0xbaa07c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbaa080: mov             x3, x0
    // 0xbaa084: r0 = Instance_Axis
    //     0xbaa084: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbaa088: stur            x3, [fp, #-0x18]
    // 0xbaa08c: StoreField: r3->field_f = r0
    //     0xbaa08c: stur            w0, [x3, #0xf]
    // 0xbaa090: r0 = Instance_MainAxisAlignment
    //     0xbaa090: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaa094: ldr             x0, [x0, #0xa08]
    // 0xbaa098: StoreField: r3->field_13 = r0
    //     0xbaa098: stur            w0, [x3, #0x13]
    // 0xbaa09c: r1 = Instance_MainAxisSize
    //     0xbaa09c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaa0a0: ldr             x1, [x1, #0xa10]
    // 0xbaa0a4: ArrayStore: r3[0] = r1  ; List_4
    //     0xbaa0a4: stur            w1, [x3, #0x17]
    // 0xbaa0a8: r4 = Instance_CrossAxisAlignment
    //     0xbaa0a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaa0ac: ldr             x4, [x4, #0xa18]
    // 0xbaa0b0: StoreField: r3->field_1b = r4
    //     0xbaa0b0: stur            w4, [x3, #0x1b]
    // 0xbaa0b4: r5 = Instance_VerticalDirection
    //     0xbaa0b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaa0b8: ldr             x5, [x5, #0xa20]
    // 0xbaa0bc: StoreField: r3->field_23 = r5
    //     0xbaa0bc: stur            w5, [x3, #0x23]
    // 0xbaa0c0: r6 = Instance_Clip
    //     0xbaa0c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaa0c4: ldr             x6, [x6, #0x38]
    // 0xbaa0c8: StoreField: r3->field_2b = r6
    //     0xbaa0c8: stur            w6, [x3, #0x2b]
    // 0xbaa0cc: StoreField: r3->field_2f = rZR
    //     0xbaa0cc: stur            xzr, [x3, #0x2f]
    // 0xbaa0d0: ldur            x1, [fp, #-0x10]
    // 0xbaa0d4: StoreField: r3->field_b = r1
    //     0xbaa0d4: stur            w1, [x3, #0xb]
    // 0xbaa0d8: ldur            x1, [fp, #-8]
    // 0xbaa0dc: LoadField: r2 = r1->field_b
    //     0xbaa0dc: ldur            w2, [x1, #0xb]
    // 0xbaa0e0: DecompressPointer r2
    //     0xbaa0e0: add             x2, x2, HEAP, lsl #32
    // 0xbaa0e4: cmp             w2, NULL
    // 0xbaa0e8: b.eq            #0xbaa29c
    // 0xbaa0ec: LoadField: r1 = r2->field_b
    //     0xbaa0ec: ldur            w1, [x2, #0xb]
    // 0xbaa0f0: DecompressPointer r1
    //     0xbaa0f0: add             x1, x1, HEAP, lsl #32
    // 0xbaa0f4: cmp             w1, NULL
    // 0xbaa0f8: b.ne            #0xbaa104
    // 0xbaa0fc: r7 = Null
    //     0xbaa0fc: mov             x7, NULL
    // 0xbaa100: b               #0xbaa114
    // 0xbaa104: LoadField: r2 = r1->field_f
    //     0xbaa104: ldur            w2, [x1, #0xf]
    // 0xbaa108: DecompressPointer r2
    //     0xbaa108: add             x2, x2, HEAP, lsl #32
    // 0xbaa10c: LoadField: r1 = r2->field_b
    //     0xbaa10c: ldur            w1, [x2, #0xb]
    // 0xbaa110: mov             x7, x1
    // 0xbaa114: ldur            x2, [fp, #-0x30]
    // 0xbaa118: stur            x7, [fp, #-8]
    // 0xbaa11c: r1 = Function '<anonymous closure>':.
    //     0xbaa11c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b80] AnonymousClosure: (0xbaa2c0), in [package:customer_app/app/presentation/views/line/bag/offers_list_widget.dart] _OffersListWidgetState::build (0xba9db8)
    //     0xbaa120: ldr             x1, [x1, #0xb80]
    // 0xbaa124: r0 = AllocateClosure()
    //     0xbaa124: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaa128: stur            x0, [fp, #-0x10]
    // 0xbaa12c: r0 = ListView()
    //     0xbaa12c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbaa130: stur            x0, [fp, #-0x20]
    // 0xbaa134: r16 = true
    //     0xbaa134: add             x16, NULL, #0x20  ; true
    // 0xbaa138: r30 = Instance_NeverScrollableScrollPhysics
    //     0xbaa138: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbaa13c: ldr             lr, [lr, #0x1c8]
    // 0xbaa140: stp             lr, x16, [SP]
    // 0xbaa144: mov             x1, x0
    // 0xbaa148: ldur            x2, [fp, #-0x10]
    // 0xbaa14c: ldur            x3, [fp, #-8]
    // 0xbaa150: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xbaa150: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbaa154: ldr             x4, [x4, #8]
    // 0xbaa158: r0 = ListView.builder()
    //     0xbaa158: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbaa15c: r1 = Null
    //     0xbaa15c: mov             x1, NULL
    // 0xbaa160: r2 = 8
    //     0xbaa160: movz            x2, #0x8
    // 0xbaa164: r0 = AllocateArray()
    //     0xbaa164: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaa168: stur            x0, [fp, #-8]
    // 0xbaa16c: r16 = Instance_SizedBox
    //     0xbaa16c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbaa170: ldr             x16, [x16, #0x578]
    // 0xbaa174: StoreField: r0->field_f = r16
    //     0xbaa174: stur            w16, [x0, #0xf]
    // 0xbaa178: ldur            x1, [fp, #-0x18]
    // 0xbaa17c: StoreField: r0->field_13 = r1
    //     0xbaa17c: stur            w1, [x0, #0x13]
    // 0xbaa180: r16 = Instance_SizedBox
    //     0xbaa180: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbaa184: ldr             x16, [x16, #0x578]
    // 0xbaa188: ArrayStore: r0[0] = r16  ; List_4
    //     0xbaa188: stur            w16, [x0, #0x17]
    // 0xbaa18c: ldur            x1, [fp, #-0x20]
    // 0xbaa190: StoreField: r0->field_1b = r1
    //     0xbaa190: stur            w1, [x0, #0x1b]
    // 0xbaa194: r1 = <Widget>
    //     0xbaa194: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaa198: r0 = AllocateGrowableArray()
    //     0xbaa198: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaa19c: mov             x1, x0
    // 0xbaa1a0: ldur            x0, [fp, #-8]
    // 0xbaa1a4: stur            x1, [fp, #-0x10]
    // 0xbaa1a8: StoreField: r1->field_f = r0
    //     0xbaa1a8: stur            w0, [x1, #0xf]
    // 0xbaa1ac: r0 = 8
    //     0xbaa1ac: movz            x0, #0x8
    // 0xbaa1b0: StoreField: r1->field_b = r0
    //     0xbaa1b0: stur            w0, [x1, #0xb]
    // 0xbaa1b4: r0 = Column()
    //     0xbaa1b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbaa1b8: mov             x1, x0
    // 0xbaa1bc: r0 = Instance_Axis
    //     0xbaa1bc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbaa1c0: stur            x1, [fp, #-8]
    // 0xbaa1c4: StoreField: r1->field_f = r0
    //     0xbaa1c4: stur            w0, [x1, #0xf]
    // 0xbaa1c8: r2 = Instance_MainAxisAlignment
    //     0xbaa1c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaa1cc: ldr             x2, [x2, #0xa08]
    // 0xbaa1d0: StoreField: r1->field_13 = r2
    //     0xbaa1d0: stur            w2, [x1, #0x13]
    // 0xbaa1d4: r2 = Instance_MainAxisSize
    //     0xbaa1d4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbaa1d8: ldr             x2, [x2, #0xdd0]
    // 0xbaa1dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xbaa1dc: stur            w2, [x1, #0x17]
    // 0xbaa1e0: r2 = Instance_CrossAxisAlignment
    //     0xbaa1e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaa1e4: ldr             x2, [x2, #0xa18]
    // 0xbaa1e8: StoreField: r1->field_1b = r2
    //     0xbaa1e8: stur            w2, [x1, #0x1b]
    // 0xbaa1ec: r2 = Instance_VerticalDirection
    //     0xbaa1ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaa1f0: ldr             x2, [x2, #0xa20]
    // 0xbaa1f4: StoreField: r1->field_23 = r2
    //     0xbaa1f4: stur            w2, [x1, #0x23]
    // 0xbaa1f8: r2 = Instance_Clip
    //     0xbaa1f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaa1fc: ldr             x2, [x2, #0x38]
    // 0xbaa200: StoreField: r1->field_2b = r2
    //     0xbaa200: stur            w2, [x1, #0x2b]
    // 0xbaa204: StoreField: r1->field_2f = rZR
    //     0xbaa204: stur            xzr, [x1, #0x2f]
    // 0xbaa208: ldur            x2, [fp, #-0x10]
    // 0xbaa20c: StoreField: r1->field_b = r2
    //     0xbaa20c: stur            w2, [x1, #0xb]
    // 0xbaa210: r0 = SingleChildScrollView()
    //     0xbaa210: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbaa214: mov             x1, x0
    // 0xbaa218: r0 = Instance_Axis
    //     0xbaa218: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbaa21c: stur            x1, [fp, #-0x10]
    // 0xbaa220: StoreField: r1->field_b = r0
    //     0xbaa220: stur            w0, [x1, #0xb]
    // 0xbaa224: r0 = false
    //     0xbaa224: add             x0, NULL, #0x30  ; false
    // 0xbaa228: StoreField: r1->field_f = r0
    //     0xbaa228: stur            w0, [x1, #0xf]
    // 0xbaa22c: ldur            x2, [fp, #-8]
    // 0xbaa230: StoreField: r1->field_23 = r2
    //     0xbaa230: stur            w2, [x1, #0x23]
    // 0xbaa234: r2 = Instance_DragStartBehavior
    //     0xbaa234: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbaa238: StoreField: r1->field_27 = r2
    //     0xbaa238: stur            w2, [x1, #0x27]
    // 0xbaa23c: r2 = Instance_Clip
    //     0xbaa23c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbaa240: ldr             x2, [x2, #0x7e0]
    // 0xbaa244: StoreField: r1->field_2b = r2
    //     0xbaa244: stur            w2, [x1, #0x2b]
    // 0xbaa248: r2 = Instance_HitTestBehavior
    //     0xbaa248: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbaa24c: ldr             x2, [x2, #0x288]
    // 0xbaa250: StoreField: r1->field_2f = r2
    //     0xbaa250: stur            w2, [x1, #0x2f]
    // 0xbaa254: r0 = SafeArea()
    //     0xbaa254: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xbaa258: r1 = true
    //     0xbaa258: add             x1, NULL, #0x20  ; true
    // 0xbaa25c: StoreField: r0->field_b = r1
    //     0xbaa25c: stur            w1, [x0, #0xb]
    // 0xbaa260: StoreField: r0->field_f = r1
    //     0xbaa260: stur            w1, [x0, #0xf]
    // 0xbaa264: StoreField: r0->field_13 = r1
    //     0xbaa264: stur            w1, [x0, #0x13]
    // 0xbaa268: ArrayStore: r0[0] = r1  ; List_4
    //     0xbaa268: stur            w1, [x0, #0x17]
    // 0xbaa26c: r1 = Instance_EdgeInsets
    //     0xbaa26c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbaa270: StoreField: r0->field_1b = r1
    //     0xbaa270: stur            w1, [x0, #0x1b]
    // 0xbaa274: r1 = false
    //     0xbaa274: add             x1, NULL, #0x30  ; false
    // 0xbaa278: StoreField: r0->field_1f = r1
    //     0xbaa278: stur            w1, [x0, #0x1f]
    // 0xbaa27c: ldur            x1, [fp, #-0x10]
    // 0xbaa280: StoreField: r0->field_23 = r1
    //     0xbaa280: stur            w1, [x0, #0x23]
    // 0xbaa284: LeaveFrame
    //     0xbaa284: mov             SP, fp
    //     0xbaa288: ldp             fp, lr, [SP], #0x10
    // 0xbaa28c: ret
    //     0xbaa28c: ret             
    // 0xbaa290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaa290: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaa294: b               #0xba9de0
    // 0xbaa298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbaa298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbaa29c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbaa29c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbaa2c0, size: 0x1c8
    // 0xbaa2c0: EnterFrame
    //     0xbaa2c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbaa2c4: mov             fp, SP
    // 0xbaa2c8: AllocStack(0x40)
    //     0xbaa2c8: sub             SP, SP, #0x40
    // 0xbaa2cc: SetupParameters()
    //     0xbaa2cc: ldr             x0, [fp, #0x20]
    //     0xbaa2d0: ldur            w1, [x0, #0x17]
    //     0xbaa2d4: add             x1, x1, HEAP, lsl #32
    //     0xbaa2d8: stur            x1, [fp, #-8]
    // 0xbaa2dc: CheckStackOverflow
    //     0xbaa2dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaa2e0: cmp             SP, x16
    //     0xbaa2e4: b.ls            #0xbaa474
    // 0xbaa2e8: r0 = Container()
    //     0xbaa2e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbaa2ec: mov             x1, x0
    // 0xbaa2f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbaa2f0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbaa2f4: r0 = Container()
    //     0xbaa2f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbaa2f8: ldur            x0, [fp, #-8]
    // 0xbaa2fc: LoadField: r1 = r0->field_f
    //     0xbaa2fc: ldur            w1, [x0, #0xf]
    // 0xbaa300: DecompressPointer r1
    //     0xbaa300: add             x1, x1, HEAP, lsl #32
    // 0xbaa304: LoadField: r2 = r1->field_b
    //     0xbaa304: ldur            w2, [x1, #0xb]
    // 0xbaa308: DecompressPointer r2
    //     0xbaa308: add             x2, x2, HEAP, lsl #32
    // 0xbaa30c: cmp             w2, NULL
    // 0xbaa310: b.eq            #0xbaa47c
    // 0xbaa314: LoadField: r3 = r2->field_b
    //     0xbaa314: ldur            w3, [x2, #0xb]
    // 0xbaa318: DecompressPointer r3
    //     0xbaa318: add             x3, x3, HEAP, lsl #32
    // 0xbaa31c: cmp             w3, NULL
    // 0xbaa320: b.ne            #0xbaa330
    // 0xbaa324: ldr             x4, [fp, #0x10]
    // 0xbaa328: r5 = Null
    //     0xbaa328: mov             x5, NULL
    // 0xbaa32c: b               #0xbaa380
    // 0xbaa330: ldr             x4, [fp, #0x10]
    // 0xbaa334: LoadField: r5 = r3->field_f
    //     0xbaa334: ldur            w5, [x3, #0xf]
    // 0xbaa338: DecompressPointer r5
    //     0xbaa338: add             x5, x5, HEAP, lsl #32
    // 0xbaa33c: LoadField: r0 = r5->field_b
    //     0xbaa33c: ldur            w0, [x5, #0xb]
    // 0xbaa340: r6 = LoadInt32Instr(r4)
    //     0xbaa340: sbfx            x6, x4, #1, #0x1f
    //     0xbaa344: tbz             w4, #0, #0xbaa34c
    //     0xbaa348: ldur            x6, [x4, #7]
    // 0xbaa34c: r1 = LoadInt32Instr(r0)
    //     0xbaa34c: sbfx            x1, x0, #1, #0x1f
    // 0xbaa350: mov             x0, x1
    // 0xbaa354: mov             x1, x6
    // 0xbaa358: cmp             x1, x0
    // 0xbaa35c: b.hs            #0xbaa480
    // 0xbaa360: LoadField: r0 = r5->field_f
    //     0xbaa360: ldur            w0, [x5, #0xf]
    // 0xbaa364: DecompressPointer r0
    //     0xbaa364: add             x0, x0, HEAP, lsl #32
    // 0xbaa368: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbaa368: add             x16, x0, x6, lsl #2
    //     0xbaa36c: ldur            w1, [x16, #0xf]
    // 0xbaa370: DecompressPointer r1
    //     0xbaa370: add             x1, x1, HEAP, lsl #32
    // 0xbaa374: LoadField: r0 = r1->field_7
    //     0xbaa374: ldur            w0, [x1, #7]
    // 0xbaa378: DecompressPointer r0
    //     0xbaa378: add             x0, x0, HEAP, lsl #32
    // 0xbaa37c: mov             x5, x0
    // 0xbaa380: stur            x5, [fp, #-0x40]
    // 0xbaa384: cmp             w3, NULL
    // 0xbaa388: b.ne            #0xbaa394
    // 0xbaa38c: r0 = Null
    //     0xbaa38c: mov             x0, NULL
    // 0xbaa390: b               #0xbaa3d8
    // 0xbaa394: LoadField: r6 = r3->field_f
    //     0xbaa394: ldur            w6, [x3, #0xf]
    // 0xbaa398: DecompressPointer r6
    //     0xbaa398: add             x6, x6, HEAP, lsl #32
    // 0xbaa39c: LoadField: r0 = r6->field_b
    //     0xbaa39c: ldur            w0, [x6, #0xb]
    // 0xbaa3a0: r3 = LoadInt32Instr(r4)
    //     0xbaa3a0: sbfx            x3, x4, #1, #0x1f
    //     0xbaa3a4: tbz             w4, #0, #0xbaa3ac
    //     0xbaa3a8: ldur            x3, [x4, #7]
    // 0xbaa3ac: r1 = LoadInt32Instr(r0)
    //     0xbaa3ac: sbfx            x1, x0, #1, #0x1f
    // 0xbaa3b0: mov             x0, x1
    // 0xbaa3b4: mov             x1, x3
    // 0xbaa3b8: cmp             x1, x0
    // 0xbaa3bc: b.hs            #0xbaa484
    // 0xbaa3c0: LoadField: r0 = r6->field_f
    //     0xbaa3c0: ldur            w0, [x6, #0xf]
    // 0xbaa3c4: DecompressPointer r0
    //     0xbaa3c4: add             x0, x0, HEAP, lsl #32
    // 0xbaa3c8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbaa3c8: add             x16, x0, x3, lsl #2
    //     0xbaa3cc: ldur            w1, [x16, #0xf]
    // 0xbaa3d0: DecompressPointer r1
    //     0xbaa3d0: add             x1, x1, HEAP, lsl #32
    // 0xbaa3d4: mov             x0, x1
    // 0xbaa3d8: stur            x0, [fp, #-0x38]
    // 0xbaa3dc: LoadField: r1 = r2->field_f
    //     0xbaa3dc: ldur            w1, [x2, #0xf]
    // 0xbaa3e0: DecompressPointer r1
    //     0xbaa3e0: add             x1, x1, HEAP, lsl #32
    // 0xbaa3e4: stur            x1, [fp, #-0x30]
    // 0xbaa3e8: LoadField: r3 = r2->field_13
    //     0xbaa3e8: ldur            w3, [x2, #0x13]
    // 0xbaa3ec: DecompressPointer r3
    //     0xbaa3ec: add             x3, x3, HEAP, lsl #32
    // 0xbaa3f0: stur            x3, [fp, #-0x28]
    // 0xbaa3f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbaa3f4: ldur            w4, [x2, #0x17]
    // 0xbaa3f8: DecompressPointer r4
    //     0xbaa3f8: add             x4, x4, HEAP, lsl #32
    // 0xbaa3fc: stur            x4, [fp, #-0x20]
    // 0xbaa400: LoadField: r6 = r2->field_1b
    //     0xbaa400: ldur            w6, [x2, #0x1b]
    // 0xbaa404: DecompressPointer r6
    //     0xbaa404: add             x6, x6, HEAP, lsl #32
    // 0xbaa408: stur            x6, [fp, #-0x18]
    // 0xbaa40c: LoadField: r7 = r2->field_2b
    //     0xbaa40c: ldur            w7, [x2, #0x2b]
    // 0xbaa410: DecompressPointer r7
    //     0xbaa410: add             x7, x7, HEAP, lsl #32
    // 0xbaa414: stur            x7, [fp, #-0x10]
    // 0xbaa418: LoadField: r8 = r2->field_2f
    //     0xbaa418: ldur            w8, [x2, #0x2f]
    // 0xbaa41c: DecompressPointer r8
    //     0xbaa41c: add             x8, x8, HEAP, lsl #32
    // 0xbaa420: stur            x8, [fp, #-8]
    // 0xbaa424: r0 = OffersContentItemView()
    //     0xbaa424: bl              #0xbaa488  ; AllocateOffersContentItemViewStub -> OffersContentItemView (size=0x2c)
    // 0xbaa428: ldur            x1, [fp, #-0x40]
    // 0xbaa42c: StoreField: r0->field_b = r1
    //     0xbaa42c: stur            w1, [x0, #0xb]
    // 0xbaa430: ldur            x1, [fp, #-0x38]
    // 0xbaa434: StoreField: r0->field_f = r1
    //     0xbaa434: stur            w1, [x0, #0xf]
    // 0xbaa438: ldur            x1, [fp, #-0x30]
    // 0xbaa43c: StoreField: r0->field_13 = r1
    //     0xbaa43c: stur            w1, [x0, #0x13]
    // 0xbaa440: ldur            x1, [fp, #-0x28]
    // 0xbaa444: ArrayStore: r0[0] = r1  ; List_4
    //     0xbaa444: stur            w1, [x0, #0x17]
    // 0xbaa448: ldur            x1, [fp, #-0x20]
    // 0xbaa44c: StoreField: r0->field_1b = r1
    //     0xbaa44c: stur            w1, [x0, #0x1b]
    // 0xbaa450: ldur            x1, [fp, #-0x18]
    // 0xbaa454: StoreField: r0->field_1f = r1
    //     0xbaa454: stur            w1, [x0, #0x1f]
    // 0xbaa458: ldur            x1, [fp, #-0x10]
    // 0xbaa45c: StoreField: r0->field_23 = r1
    //     0xbaa45c: stur            w1, [x0, #0x23]
    // 0xbaa460: ldur            x1, [fp, #-8]
    // 0xbaa464: StoreField: r0->field_27 = r1
    //     0xbaa464: stur            w1, [x0, #0x27]
    // 0xbaa468: LeaveFrame
    //     0xbaa468: mov             SP, fp
    //     0xbaa46c: ldp             fp, lr, [SP], #0x10
    // 0xbaa470: ret
    //     0xbaa470: ret             
    // 0xbaa474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaa474: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaa478: b               #0xbaa2e8
    // 0xbaa47c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbaa47c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbaa480: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbaa480: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbaa484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbaa484: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4029, size: 0x34, field offset: 0xc
//   const constructor, 
class OffersListWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80048, size: 0x24
    // 0xc80048: EnterFrame
    //     0xc80048: stp             fp, lr, [SP, #-0x10]!
    //     0xc8004c: mov             fp, SP
    // 0xc80050: mov             x0, x1
    // 0xc80054: r1 = <OffersListWidget>
    //     0xc80054: add             x1, PP, #0x48, lsl #12  ; [pp+0x486d8] TypeArguments: <OffersListWidget>
    //     0xc80058: ldr             x1, [x1, #0x6d8]
    // 0xc8005c: r0 = _OffersListWidgetState()
    //     0xc8005c: bl              #0xc8006c  ; Allocate_OffersListWidgetStateStub -> _OffersListWidgetState (size=0x14)
    // 0xc80060: LeaveFrame
    //     0xc80060: mov             SP, fp
    //     0xc80064: ldp             fp, lr, [SP], #0x10
    // 0xc80068: ret
    //     0xc80068: ret             
  }
}
