// lib: , url: package:customer_app/app/presentation/views/line/bag/offers_bottom_sheet.dart

// class id: 1049470, size: 0x8
class :: {
}

// class id: 3288, size: 0x14, field offset: 0x14
class _OffersBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xba5b10, size: 0x101c
    // 0xba5b10: EnterFrame
    //     0xba5b10: stp             fp, lr, [SP, #-0x10]!
    //     0xba5b14: mov             fp, SP
    // 0xba5b18: AllocStack(0x60)
    //     0xba5b18: sub             SP, SP, #0x60
    // 0xba5b1c: SetupParameters(_OffersBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba5b1c: mov             x0, x1
    //     0xba5b20: stur            x1, [fp, #-8]
    //     0xba5b24: mov             x1, x2
    //     0xba5b28: stur            x2, [fp, #-0x10]
    // 0xba5b2c: CheckStackOverflow
    //     0xba5b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba5b30: cmp             SP, x16
    //     0xba5b34: b.ls            #0xba6aec
    // 0xba5b38: r1 = 2
    //     0xba5b38: movz            x1, #0x2
    // 0xba5b3c: r0 = AllocateContext()
    //     0xba5b3c: bl              #0x16f6108  ; AllocateContextStub
    // 0xba5b40: mov             x2, x0
    // 0xba5b44: ldur            x0, [fp, #-8]
    // 0xba5b48: stur            x2, [fp, #-0x18]
    // 0xba5b4c: StoreField: r2->field_f = r0
    //     0xba5b4c: stur            w0, [x2, #0xf]
    // 0xba5b50: ldur            x1, [fp, #-0x10]
    // 0xba5b54: StoreField: r2->field_13 = r1
    //     0xba5b54: stur            w1, [x2, #0x13]
    // 0xba5b58: r0 = of()
    //     0xba5b58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba5b5c: LoadField: r1 = r0->field_87
    //     0xba5b5c: ldur            w1, [x0, #0x87]
    // 0xba5b60: DecompressPointer r1
    //     0xba5b60: add             x1, x1, HEAP, lsl #32
    // 0xba5b64: LoadField: r0 = r1->field_27
    //     0xba5b64: ldur            w0, [x1, #0x27]
    // 0xba5b68: DecompressPointer r0
    //     0xba5b68: add             x0, x0, HEAP, lsl #32
    // 0xba5b6c: r16 = Instance_Color
    //     0xba5b6c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba5b70: r30 = 21.000000
    //     0xba5b70: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xba5b74: ldr             lr, [lr, #0x9b0]
    // 0xba5b78: stp             lr, x16, [SP]
    // 0xba5b7c: mov             x1, x0
    // 0xba5b80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba5b80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba5b84: ldr             x4, [x4, #0x9b8]
    // 0xba5b88: r0 = copyWith()
    //     0xba5b88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba5b8c: stur            x0, [fp, #-0x10]
    // 0xba5b90: r0 = Text()
    //     0xba5b90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba5b94: mov             x1, x0
    // 0xba5b98: r0 = "Offer"
    //     0xba5b98: add             x0, PP, #0x71, lsl #12  ; [pp+0x71460] "Offer"
    //     0xba5b9c: ldr             x0, [x0, #0x460]
    // 0xba5ba0: stur            x1, [fp, #-0x20]
    // 0xba5ba4: StoreField: r1->field_b = r0
    //     0xba5ba4: stur            w0, [x1, #0xb]
    // 0xba5ba8: ldur            x0, [fp, #-0x10]
    // 0xba5bac: StoreField: r1->field_13 = r0
    //     0xba5bac: stur            w0, [x1, #0x13]
    // 0xba5bb0: r0 = Padding()
    //     0xba5bb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba5bb4: mov             x2, x0
    // 0xba5bb8: r0 = Instance_EdgeInsets
    //     0xba5bb8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xba5bbc: ldr             x0, [x0, #0xf70]
    // 0xba5bc0: stur            x2, [fp, #-0x10]
    // 0xba5bc4: StoreField: r2->field_f = r0
    //     0xba5bc4: stur            w0, [x2, #0xf]
    // 0xba5bc8: ldur            x1, [fp, #-0x20]
    // 0xba5bcc: StoreField: r2->field_b = r1
    //     0xba5bcc: stur            w1, [x2, #0xb]
    // 0xba5bd0: r1 = <FlexParentData>
    //     0xba5bd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba5bd4: ldr             x1, [x1, #0xe00]
    // 0xba5bd8: r0 = Expanded()
    //     0xba5bd8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba5bdc: stur            x0, [fp, #-0x20]
    // 0xba5be0: StoreField: r0->field_13 = rZR
    //     0xba5be0: stur            xzr, [x0, #0x13]
    // 0xba5be4: r1 = Instance_FlexFit
    //     0xba5be4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba5be8: ldr             x1, [x1, #0xe08]
    // 0xba5bec: StoreField: r0->field_1b = r1
    //     0xba5bec: stur            w1, [x0, #0x1b]
    // 0xba5bf0: ldur            x2, [fp, #-0x10]
    // 0xba5bf4: StoreField: r0->field_b = r2
    //     0xba5bf4: stur            w2, [x0, #0xb]
    // 0xba5bf8: r0 = InkWell()
    //     0xba5bf8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba5bfc: mov             x3, x0
    // 0xba5c00: r0 = Instance_Padding
    //     0xba5c00: add             x0, PP, #0x71, lsl #12  ; [pp+0x71468] Obj!Padding@d68441
    //     0xba5c04: ldr             x0, [x0, #0x468]
    // 0xba5c08: stur            x3, [fp, #-0x10]
    // 0xba5c0c: StoreField: r3->field_b = r0
    //     0xba5c0c: stur            w0, [x3, #0xb]
    // 0xba5c10: ldur            x2, [fp, #-0x18]
    // 0xba5c14: r1 = Function '<anonymous closure>':.
    //     0xba5c14: add             x1, PP, #0x71, lsl #12  ; [pp+0x71470] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xba5c18: ldr             x1, [x1, #0x470]
    // 0xba5c1c: r0 = AllocateClosure()
    //     0xba5c1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba5c20: mov             x1, x0
    // 0xba5c24: ldur            x0, [fp, #-0x10]
    // 0xba5c28: StoreField: r0->field_f = r1
    //     0xba5c28: stur            w1, [x0, #0xf]
    // 0xba5c2c: r2 = true
    //     0xba5c2c: add             x2, NULL, #0x20  ; true
    // 0xba5c30: StoreField: r0->field_43 = r2
    //     0xba5c30: stur            w2, [x0, #0x43]
    // 0xba5c34: r3 = Instance_BoxShape
    //     0xba5c34: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba5c38: ldr             x3, [x3, #0x80]
    // 0xba5c3c: StoreField: r0->field_47 = r3
    //     0xba5c3c: stur            w3, [x0, #0x47]
    // 0xba5c40: StoreField: r0->field_6f = r2
    //     0xba5c40: stur            w2, [x0, #0x6f]
    // 0xba5c44: r4 = false
    //     0xba5c44: add             x4, NULL, #0x30  ; false
    // 0xba5c48: StoreField: r0->field_73 = r4
    //     0xba5c48: stur            w4, [x0, #0x73]
    // 0xba5c4c: StoreField: r0->field_83 = r2
    //     0xba5c4c: stur            w2, [x0, #0x83]
    // 0xba5c50: StoreField: r0->field_7b = r4
    //     0xba5c50: stur            w4, [x0, #0x7b]
    // 0xba5c54: r1 = <FlexParentData>
    //     0xba5c54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba5c58: ldr             x1, [x1, #0xe00]
    // 0xba5c5c: r0 = Expanded()
    //     0xba5c5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba5c60: stur            x0, [fp, #-0x28]
    // 0xba5c64: StoreField: r0->field_13 = rZR
    //     0xba5c64: stur            xzr, [x0, #0x13]
    // 0xba5c68: r3 = Instance_FlexFit
    //     0xba5c68: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba5c6c: ldr             x3, [x3, #0xe08]
    // 0xba5c70: StoreField: r0->field_1b = r3
    //     0xba5c70: stur            w3, [x0, #0x1b]
    // 0xba5c74: ldur            x1, [fp, #-0x10]
    // 0xba5c78: StoreField: r0->field_b = r1
    //     0xba5c78: stur            w1, [x0, #0xb]
    // 0xba5c7c: r1 = Null
    //     0xba5c7c: mov             x1, NULL
    // 0xba5c80: r2 = 6
    //     0xba5c80: movz            x2, #0x6
    // 0xba5c84: r0 = AllocateArray()
    //     0xba5c84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba5c88: mov             x2, x0
    // 0xba5c8c: ldur            x0, [fp, #-0x20]
    // 0xba5c90: stur            x2, [fp, #-0x10]
    // 0xba5c94: StoreField: r2->field_f = r0
    //     0xba5c94: stur            w0, [x2, #0xf]
    // 0xba5c98: r16 = Instance_Spacer
    //     0xba5c98: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba5c9c: ldr             x16, [x16, #0xf0]
    // 0xba5ca0: StoreField: r2->field_13 = r16
    //     0xba5ca0: stur            w16, [x2, #0x13]
    // 0xba5ca4: ldur            x0, [fp, #-0x28]
    // 0xba5ca8: ArrayStore: r2[0] = r0  ; List_4
    //     0xba5ca8: stur            w0, [x2, #0x17]
    // 0xba5cac: r1 = <Widget>
    //     0xba5cac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba5cb0: r0 = AllocateGrowableArray()
    //     0xba5cb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba5cb4: mov             x1, x0
    // 0xba5cb8: ldur            x0, [fp, #-0x10]
    // 0xba5cbc: stur            x1, [fp, #-0x20]
    // 0xba5cc0: StoreField: r1->field_f = r0
    //     0xba5cc0: stur            w0, [x1, #0xf]
    // 0xba5cc4: r0 = 6
    //     0xba5cc4: movz            x0, #0x6
    // 0xba5cc8: StoreField: r1->field_b = r0
    //     0xba5cc8: stur            w0, [x1, #0xb]
    // 0xba5ccc: r0 = Row()
    //     0xba5ccc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba5cd0: mov             x3, x0
    // 0xba5cd4: r0 = Instance_Axis
    //     0xba5cd4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba5cd8: stur            x3, [fp, #-0x10]
    // 0xba5cdc: StoreField: r3->field_f = r0
    //     0xba5cdc: stur            w0, [x3, #0xf]
    // 0xba5ce0: r0 = Instance_MainAxisAlignment
    //     0xba5ce0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba5ce4: ldr             x0, [x0, #0xa08]
    // 0xba5ce8: StoreField: r3->field_13 = r0
    //     0xba5ce8: stur            w0, [x3, #0x13]
    // 0xba5cec: r1 = Instance_MainAxisSize
    //     0xba5cec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba5cf0: ldr             x1, [x1, #0xa10]
    // 0xba5cf4: ArrayStore: r3[0] = r1  ; List_4
    //     0xba5cf4: stur            w1, [x3, #0x17]
    // 0xba5cf8: r1 = Instance_CrossAxisAlignment
    //     0xba5cf8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba5cfc: ldr             x1, [x1, #0xa18]
    // 0xba5d00: StoreField: r3->field_1b = r1
    //     0xba5d00: stur            w1, [x3, #0x1b]
    // 0xba5d04: r4 = Instance_VerticalDirection
    //     0xba5d04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba5d08: ldr             x4, [x4, #0xa20]
    // 0xba5d0c: StoreField: r3->field_23 = r4
    //     0xba5d0c: stur            w4, [x3, #0x23]
    // 0xba5d10: r5 = Instance_Clip
    //     0xba5d10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba5d14: ldr             x5, [x5, #0x38]
    // 0xba5d18: StoreField: r3->field_2b = r5
    //     0xba5d18: stur            w5, [x3, #0x2b]
    // 0xba5d1c: StoreField: r3->field_2f = rZR
    //     0xba5d1c: stur            xzr, [x3, #0x2f]
    // 0xba5d20: ldur            x1, [fp, #-0x20]
    // 0xba5d24: StoreField: r3->field_b = r1
    //     0xba5d24: stur            w1, [x3, #0xb]
    // 0xba5d28: r1 = <Widget>
    //     0xba5d28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba5d2c: r2 = 18
    //     0xba5d2c: movz            x2, #0x12
    // 0xba5d30: r0 = AllocateArray()
    //     0xba5d30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba5d34: mov             x2, x0
    // 0xba5d38: ldur            x0, [fp, #-0x10]
    // 0xba5d3c: stur            x2, [fp, #-0x20]
    // 0xba5d40: StoreField: r2->field_f = r0
    //     0xba5d40: stur            w0, [x2, #0xf]
    // 0xba5d44: r16 = Instance_SizedBox
    //     0xba5d44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xba5d48: ldr             x16, [x16, #0x8f0]
    // 0xba5d4c: StoreField: r2->field_13 = r16
    //     0xba5d4c: stur            w16, [x2, #0x13]
    // 0xba5d50: r1 = Instance_Color
    //     0xba5d50: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba5d54: d0 = 0.100000
    //     0xba5d54: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xba5d58: r0 = withOpacity()
    //     0xba5d58: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba5d5c: stur            x0, [fp, #-0x10]
    // 0xba5d60: r0 = Container()
    //     0xba5d60: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba5d64: stur            x0, [fp, #-0x28]
    // 0xba5d68: ldur            x16, [fp, #-0x10]
    // 0xba5d6c: r30 = 50.000000
    //     0xba5d6c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xba5d70: ldr             lr, [lr, #0xa90]
    // 0xba5d74: stp             lr, x16, [SP, #0x10]
    // 0xba5d78: r16 = 50.000000
    //     0xba5d78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xba5d7c: ldr             x16, [x16, #0xa90]
    // 0xba5d80: r30 = Instance_Icon
    //     0xba5d80: add             lr, PP, #0x71, lsl #12  ; [pp+0x71478] Obj!Icon@d66231
    //     0xba5d84: ldr             lr, [lr, #0x478]
    // 0xba5d88: stp             lr, x16, [SP]
    // 0xba5d8c: mov             x1, x0
    // 0xba5d90: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x2, width, 0x3, null]
    //     0xba5d90: add             x4, PP, #0x71, lsl #12  ; [pp+0x71480] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x2, "width", 0x3, Null]
    //     0xba5d94: ldr             x4, [x4, #0x480]
    // 0xba5d98: r0 = Container()
    //     0xba5d98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba5d9c: r0 = Padding()
    //     0xba5d9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba5da0: mov             x1, x0
    // 0xba5da4: r0 = Instance_EdgeInsets
    //     0xba5da4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xba5da8: ldr             x0, [x0, #0xf70]
    // 0xba5dac: StoreField: r1->field_f = r0
    //     0xba5dac: stur            w0, [x1, #0xf]
    // 0xba5db0: ldur            x0, [fp, #-0x28]
    // 0xba5db4: StoreField: r1->field_b = r0
    //     0xba5db4: stur            w0, [x1, #0xb]
    // 0xba5db8: mov             x0, x1
    // 0xba5dbc: ldur            x1, [fp, #-0x20]
    // 0xba5dc0: ArrayStore: r1[2] = r0  ; List_4
    //     0xba5dc0: add             x25, x1, #0x17
    //     0xba5dc4: str             w0, [x25]
    //     0xba5dc8: tbz             w0, #0, #0xba5de4
    //     0xba5dcc: ldurb           w16, [x1, #-1]
    //     0xba5dd0: ldurb           w17, [x0, #-1]
    //     0xba5dd4: and             x16, x17, x16, lsr #2
    //     0xba5dd8: tst             x16, HEAP, lsr #32
    //     0xba5ddc: b.eq            #0xba5de4
    //     0xba5de0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba5de4: ldur            x0, [fp, #-0x20]
    // 0xba5de8: r16 = Instance_SizedBox
    //     0xba5de8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xba5dec: ldr             x16, [x16, #0x8b8]
    // 0xba5df0: StoreField: r0->field_1b = r16
    //     0xba5df0: stur            w16, [x0, #0x1b]
    // 0xba5df4: ldur            x2, [fp, #-8]
    // 0xba5df8: LoadField: r1 = r2->field_b
    //     0xba5df8: ldur            w1, [x2, #0xb]
    // 0xba5dfc: DecompressPointer r1
    //     0xba5dfc: add             x1, x1, HEAP, lsl #32
    // 0xba5e00: cmp             w1, NULL
    // 0xba5e04: b.eq            #0xba6af4
    // 0xba5e08: LoadField: r3 = r1->field_b
    //     0xba5e08: ldur            w3, [x1, #0xb]
    // 0xba5e0c: DecompressPointer r3
    //     0xba5e0c: add             x3, x3, HEAP, lsl #32
    // 0xba5e10: cmp             w3, NULL
    // 0xba5e14: b.ne            #0xba5e20
    // 0xba5e18: r1 = Null
    //     0xba5e18: mov             x1, NULL
    // 0xba5e1c: b               #0xba5e28
    // 0xba5e20: LoadField: r1 = r3->field_13
    //     0xba5e20: ldur            w1, [x3, #0x13]
    // 0xba5e24: DecompressPointer r1
    //     0xba5e24: add             x1, x1, HEAP, lsl #32
    // 0xba5e28: cmp             w1, NULL
    // 0xba5e2c: b.ne            #0xba5e38
    // 0xba5e30: r4 = ""
    //     0xba5e30: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba5e34: b               #0xba5e3c
    // 0xba5e38: mov             x4, x1
    // 0xba5e3c: ldur            x3, [fp, #-0x18]
    // 0xba5e40: stur            x4, [fp, #-0x10]
    // 0xba5e44: LoadField: r1 = r3->field_13
    //     0xba5e44: ldur            w1, [x3, #0x13]
    // 0xba5e48: DecompressPointer r1
    //     0xba5e48: add             x1, x1, HEAP, lsl #32
    // 0xba5e4c: r0 = of()
    //     0xba5e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba5e50: LoadField: r1 = r0->field_87
    //     0xba5e50: ldur            w1, [x0, #0x87]
    // 0xba5e54: DecompressPointer r1
    //     0xba5e54: add             x1, x1, HEAP, lsl #32
    // 0xba5e58: LoadField: r0 = r1->field_7
    //     0xba5e58: ldur            w0, [x1, #7]
    // 0xba5e5c: DecompressPointer r0
    //     0xba5e5c: add             x0, x0, HEAP, lsl #32
    // 0xba5e60: r16 = Instance_Color
    //     0xba5e60: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba5e64: r30 = 16.000000
    //     0xba5e64: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba5e68: ldr             lr, [lr, #0x188]
    // 0xba5e6c: stp             lr, x16, [SP]
    // 0xba5e70: mov             x1, x0
    // 0xba5e74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba5e74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba5e78: ldr             x4, [x4, #0x9b8]
    // 0xba5e7c: r0 = copyWith()
    //     0xba5e7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba5e80: stur            x0, [fp, #-0x28]
    // 0xba5e84: r0 = Text()
    //     0xba5e84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba5e88: mov             x1, x0
    // 0xba5e8c: ldur            x0, [fp, #-0x10]
    // 0xba5e90: stur            x1, [fp, #-0x30]
    // 0xba5e94: StoreField: r1->field_b = r0
    //     0xba5e94: stur            w0, [x1, #0xb]
    // 0xba5e98: ldur            x0, [fp, #-0x28]
    // 0xba5e9c: StoreField: r1->field_13 = r0
    //     0xba5e9c: stur            w0, [x1, #0x13]
    // 0xba5ea0: r0 = Padding()
    //     0xba5ea0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba5ea4: mov             x2, x0
    // 0xba5ea8: r0 = Instance_EdgeInsets
    //     0xba5ea8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xba5eac: ldr             x0, [x0, #0xc40]
    // 0xba5eb0: stur            x2, [fp, #-0x10]
    // 0xba5eb4: StoreField: r2->field_f = r0
    //     0xba5eb4: stur            w0, [x2, #0xf]
    // 0xba5eb8: ldur            x0, [fp, #-0x30]
    // 0xba5ebc: StoreField: r2->field_b = r0
    //     0xba5ebc: stur            w0, [x2, #0xb]
    // 0xba5ec0: r1 = <FlexParentData>
    //     0xba5ec0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba5ec4: ldr             x1, [x1, #0xe00]
    // 0xba5ec8: r0 = Expanded()
    //     0xba5ec8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba5ecc: StoreField: r0->field_13 = rZR
    //     0xba5ecc: stur            xzr, [x0, #0x13]
    // 0xba5ed0: r1 = Instance_FlexFit
    //     0xba5ed0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba5ed4: ldr             x1, [x1, #0xe08]
    // 0xba5ed8: StoreField: r0->field_1b = r1
    //     0xba5ed8: stur            w1, [x0, #0x1b]
    // 0xba5edc: ldur            x1, [fp, #-0x10]
    // 0xba5ee0: StoreField: r0->field_b = r1
    //     0xba5ee0: stur            w1, [x0, #0xb]
    // 0xba5ee4: ldur            x1, [fp, #-0x20]
    // 0xba5ee8: ArrayStore: r1[4] = r0  ; List_4
    //     0xba5ee8: add             x25, x1, #0x1f
    //     0xba5eec: str             w0, [x25]
    //     0xba5ef0: tbz             w0, #0, #0xba5f0c
    //     0xba5ef4: ldurb           w16, [x1, #-1]
    //     0xba5ef8: ldurb           w17, [x0, #-1]
    //     0xba5efc: and             x16, x17, x16, lsr #2
    //     0xba5f00: tst             x16, HEAP, lsr #32
    //     0xba5f04: b.eq            #0xba5f0c
    //     0xba5f08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba5f0c: ldur            x0, [fp, #-8]
    // 0xba5f10: LoadField: r1 = r0->field_b
    //     0xba5f10: ldur            w1, [x0, #0xb]
    // 0xba5f14: DecompressPointer r1
    //     0xba5f14: add             x1, x1, HEAP, lsl #32
    // 0xba5f18: cmp             w1, NULL
    // 0xba5f1c: b.eq            #0xba6af8
    // 0xba5f20: LoadField: r2 = r1->field_b
    //     0xba5f20: ldur            w2, [x1, #0xb]
    // 0xba5f24: DecompressPointer r2
    //     0xba5f24: add             x2, x2, HEAP, lsl #32
    // 0xba5f28: cmp             w2, NULL
    // 0xba5f2c: b.ne            #0xba5f38
    // 0xba5f30: r3 = Null
    //     0xba5f30: mov             x3, NULL
    // 0xba5f34: b               #0xba5f5c
    // 0xba5f38: LoadField: r1 = r2->field_1f
    //     0xba5f38: ldur            w1, [x2, #0x1f]
    // 0xba5f3c: DecompressPointer r1
    //     0xba5f3c: add             x1, x1, HEAP, lsl #32
    // 0xba5f40: cmp             w1, NULL
    // 0xba5f44: b.ne            #0xba5f50
    // 0xba5f48: r1 = Null
    //     0xba5f48: mov             x1, NULL
    // 0xba5f4c: b               #0xba5f58
    // 0xba5f50: LoadField: r2 = r1->field_b
    //     0xba5f50: ldur            w2, [x1, #0xb]
    // 0xba5f54: mov             x1, x2
    // 0xba5f58: mov             x3, x1
    // 0xba5f5c: ldur            x2, [fp, #-0x18]
    // 0xba5f60: stur            x3, [fp, #-0x10]
    // 0xba5f64: r1 = Function '<anonymous closure>':.
    //     0xba5f64: add             x1, PP, #0x71, lsl #12  ; [pp+0x71488] AnonymousClosure: (0xba6d90), in [package:customer_app/app/presentation/views/line/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xba5b10)
    //     0xba5f68: ldr             x1, [x1, #0x488]
    // 0xba5f6c: r0 = AllocateClosure()
    //     0xba5f6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba5f70: stur            x0, [fp, #-0x28]
    // 0xba5f74: r0 = ListView()
    //     0xba5f74: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xba5f78: stur            x0, [fp, #-0x30]
    // 0xba5f7c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xba5f7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xba5f80: ldr             x16, [x16, #0x1c8]
    // 0xba5f84: r30 = true
    //     0xba5f84: add             lr, NULL, #0x20  ; true
    // 0xba5f88: stp             lr, x16, [SP]
    // 0xba5f8c: mov             x1, x0
    // 0xba5f90: ldur            x2, [fp, #-0x28]
    // 0xba5f94: ldur            x3, [fp, #-0x10]
    // 0xba5f98: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xba5f98: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xba5f9c: ldr             x4, [x4, #0xd18]
    // 0xba5fa0: r0 = ListView.builder()
    //     0xba5fa0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xba5fa4: ldur            x1, [fp, #-0x20]
    // 0xba5fa8: ldur            x0, [fp, #-0x30]
    // 0xba5fac: ArrayStore: r1[5] = r0  ; List_4
    //     0xba5fac: add             x25, x1, #0x23
    //     0xba5fb0: str             w0, [x25]
    //     0xba5fb4: tbz             w0, #0, #0xba5fd0
    //     0xba5fb8: ldurb           w16, [x1, #-1]
    //     0xba5fbc: ldurb           w17, [x0, #-1]
    //     0xba5fc0: and             x16, x17, x16, lsr #2
    //     0xba5fc4: tst             x16, HEAP, lsr #32
    //     0xba5fc8: b.eq            #0xba5fd0
    //     0xba5fcc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba5fd0: ldur            x0, [fp, #-8]
    // 0xba5fd4: LoadField: r1 = r0->field_b
    //     0xba5fd4: ldur            w1, [x0, #0xb]
    // 0xba5fd8: DecompressPointer r1
    //     0xba5fd8: add             x1, x1, HEAP, lsl #32
    // 0xba5fdc: cmp             w1, NULL
    // 0xba5fe0: b.eq            #0xba6afc
    // 0xba5fe4: LoadField: r2 = r1->field_b
    //     0xba5fe4: ldur            w2, [x1, #0xb]
    // 0xba5fe8: DecompressPointer r2
    //     0xba5fe8: add             x2, x2, HEAP, lsl #32
    // 0xba5fec: stur            x2, [fp, #-0x28]
    // 0xba5ff0: cmp             w2, NULL
    // 0xba5ff4: b.ne            #0xba6000
    // 0xba5ff8: r1 = Null
    //     0xba5ff8: mov             x1, NULL
    // 0xba5ffc: b               #0xba6008
    // 0xba6000: LoadField: r1 = r2->field_27
    //     0xba6000: ldur            w1, [x2, #0x27]
    // 0xba6004: DecompressPointer r1
    //     0xba6004: add             x1, x1, HEAP, lsl #32
    // 0xba6008: cmp             w1, NULL
    // 0xba600c: b.eq            #0xba6020
    // 0xba6010: tbnz            w1, #4, #0xba6020
    // 0xba6014: r1 = Instance_Color
    //     0xba6014: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba6018: ldr             x1, [x1, #0x858]
    // 0xba601c: b               #0xba6024
    // 0xba6020: r1 = Instance_Color
    //     0xba6020: ldr             x1, [PP, #0x54e8]  ; [pp+0x54e8] Obj!Color@d699f1
    // 0xba6024: stur            x1, [fp, #-0x10]
    // 0xba6028: r0 = BoxDecoration()
    //     0xba6028: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xba602c: mov             x2, x0
    // 0xba6030: ldur            x0, [fp, #-0x10]
    // 0xba6034: stur            x2, [fp, #-0x30]
    // 0xba6038: StoreField: r2->field_7 = r0
    //     0xba6038: stur            w0, [x2, #7]
    // 0xba603c: r0 = Instance_BoxShape
    //     0xba603c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba6040: ldr             x0, [x0, #0x80]
    // 0xba6044: StoreField: r2->field_23 = r0
    //     0xba6044: stur            w0, [x2, #0x23]
    // 0xba6048: ldur            x0, [fp, #-0x28]
    // 0xba604c: cmp             w0, NULL
    // 0xba6050: b.ne            #0xba605c
    // 0xba6054: r0 = Null
    //     0xba6054: mov             x0, NULL
    // 0xba6058: b               #0xba6068
    // 0xba605c: LoadField: r1 = r0->field_1b
    //     0xba605c: ldur            w1, [x0, #0x1b]
    // 0xba6060: DecompressPointer r1
    //     0xba6060: add             x1, x1, HEAP, lsl #32
    // 0xba6064: mov             x0, x1
    // 0xba6068: cmp             w0, NULL
    // 0xba606c: b.ne            #0xba6078
    // 0xba6070: r4 = ""
    //     0xba6070: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba6074: b               #0xba607c
    // 0xba6078: mov             x4, x0
    // 0xba607c: ldur            x0, [fp, #-8]
    // 0xba6080: ldur            x3, [fp, #-0x18]
    // 0xba6084: stur            x4, [fp, #-0x10]
    // 0xba6088: LoadField: r1 = r3->field_13
    //     0xba6088: ldur            w1, [x3, #0x13]
    // 0xba608c: DecompressPointer r1
    //     0xba608c: add             x1, x1, HEAP, lsl #32
    // 0xba6090: r0 = of()
    //     0xba6090: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6094: LoadField: r1 = r0->field_87
    //     0xba6094: ldur            w1, [x0, #0x87]
    // 0xba6098: DecompressPointer r1
    //     0xba6098: add             x1, x1, HEAP, lsl #32
    // 0xba609c: LoadField: r0 = r1->field_2b
    //     0xba609c: ldur            w0, [x1, #0x2b]
    // 0xba60a0: DecompressPointer r0
    //     0xba60a0: add             x0, x0, HEAP, lsl #32
    // 0xba60a4: r16 = Instance_Color
    //     0xba60a4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba60a8: r30 = 12.000000
    //     0xba60a8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba60ac: ldr             lr, [lr, #0x9e8]
    // 0xba60b0: stp             lr, x16, [SP]
    // 0xba60b4: mov             x1, x0
    // 0xba60b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba60b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba60bc: ldr             x4, [x4, #0x9b8]
    // 0xba60c0: r0 = copyWith()
    //     0xba60c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba60c4: stur            x0, [fp, #-0x28]
    // 0xba60c8: r0 = Text()
    //     0xba60c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba60cc: mov             x1, x0
    // 0xba60d0: ldur            x0, [fp, #-0x10]
    // 0xba60d4: stur            x1, [fp, #-0x38]
    // 0xba60d8: StoreField: r1->field_b = r0
    //     0xba60d8: stur            w0, [x1, #0xb]
    // 0xba60dc: ldur            x0, [fp, #-0x28]
    // 0xba60e0: StoreField: r1->field_13 = r0
    //     0xba60e0: stur            w0, [x1, #0x13]
    // 0xba60e4: r0 = Align()
    //     0xba60e4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xba60e8: mov             x1, x0
    // 0xba60ec: r0 = Instance_Alignment
    //     0xba60ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xba60f0: ldr             x0, [x0, #0xb10]
    // 0xba60f4: stur            x1, [fp, #-0x10]
    // 0xba60f8: StoreField: r1->field_f = r0
    //     0xba60f8: stur            w0, [x1, #0xf]
    // 0xba60fc: ldur            x2, [fp, #-0x38]
    // 0xba6100: StoreField: r1->field_b = r2
    //     0xba6100: stur            w2, [x1, #0xb]
    // 0xba6104: r0 = Container()
    //     0xba6104: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba6108: stur            x0, [fp, #-0x28]
    // 0xba610c: r16 = inf
    //     0xba610c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xba6110: ldr             x16, [x16, #0x9f8]
    // 0xba6114: ldur            lr, [fp, #-0x30]
    // 0xba6118: stp             lr, x16, [SP, #8]
    // 0xba611c: ldur            x16, [fp, #-0x10]
    // 0xba6120: str             x16, [SP]
    // 0xba6124: mov             x1, x0
    // 0xba6128: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xba6128: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xba612c: ldr             x4, [x4, #0x830]
    // 0xba6130: r0 = Container()
    //     0xba6130: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba6134: r0 = SizedBox()
    //     0xba6134: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba6138: mov             x1, x0
    // 0xba613c: r0 = 20.000000
    //     0xba613c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xba6140: ldr             x0, [x0, #0xac8]
    // 0xba6144: stur            x1, [fp, #-0x10]
    // 0xba6148: StoreField: r1->field_13 = r0
    //     0xba6148: stur            w0, [x1, #0x13]
    // 0xba614c: ldur            x0, [fp, #-0x28]
    // 0xba6150: StoreField: r1->field_b = r0
    //     0xba6150: stur            w0, [x1, #0xb]
    // 0xba6154: r0 = Padding()
    //     0xba6154: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba6158: mov             x1, x0
    // 0xba615c: r0 = Instance_EdgeInsets
    //     0xba615c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xba6160: ldr             x0, [x0, #0x668]
    // 0xba6164: StoreField: r1->field_f = r0
    //     0xba6164: stur            w0, [x1, #0xf]
    // 0xba6168: ldur            x0, [fp, #-0x10]
    // 0xba616c: StoreField: r1->field_b = r0
    //     0xba616c: stur            w0, [x1, #0xb]
    // 0xba6170: mov             x0, x1
    // 0xba6174: ldur            x1, [fp, #-0x20]
    // 0xba6178: ArrayStore: r1[6] = r0  ; List_4
    //     0xba6178: add             x25, x1, #0x27
    //     0xba617c: str             w0, [x25]
    //     0xba6180: tbz             w0, #0, #0xba619c
    //     0xba6184: ldurb           w16, [x1, #-1]
    //     0xba6188: ldurb           w17, [x0, #-1]
    //     0xba618c: and             x16, x17, x16, lsr #2
    //     0xba6190: tst             x16, HEAP, lsr #32
    //     0xba6194: b.eq            #0xba619c
    //     0xba6198: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba619c: ldur            x1, [fp, #-8]
    // 0xba61a0: LoadField: r0 = r1->field_b
    //     0xba61a0: ldur            w0, [x1, #0xb]
    // 0xba61a4: DecompressPointer r0
    //     0xba61a4: add             x0, x0, HEAP, lsl #32
    // 0xba61a8: cmp             w0, NULL
    // 0xba61ac: b.eq            #0xba6b00
    // 0xba61b0: LoadField: r2 = r0->field_13
    //     0xba61b0: ldur            w2, [x0, #0x13]
    // 0xba61b4: DecompressPointer r2
    //     0xba61b4: add             x2, x2, HEAP, lsl #32
    // 0xba61b8: r0 = LoadClassIdInstr(r2)
    //     0xba61b8: ldur            x0, [x2, #-1]
    //     0xba61bc: ubfx            x0, x0, #0xc, #0x14
    // 0xba61c0: r16 = "checkout_offers"
    //     0xba61c0: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xba61c4: ldr             x16, [x16, #0x1c8]
    // 0xba61c8: stp             x16, x2, [SP]
    // 0xba61cc: mov             lr, x0
    // 0xba61d0: ldr             lr, [x21, lr, lsl #3]
    // 0xba61d4: blr             lr
    // 0xba61d8: tbz             w0, #4, #0xba6200
    // 0xba61dc: ldur            x0, [fp, #-8]
    // 0xba61e0: LoadField: r1 = r0->field_b
    //     0xba61e0: ldur            w1, [x0, #0xb]
    // 0xba61e4: DecompressPointer r1
    //     0xba61e4: add             x1, x1, HEAP, lsl #32
    // 0xba61e8: cmp             w1, NULL
    // 0xba61ec: b.eq            #0xba6b04
    // 0xba61f0: LoadField: r2 = r1->field_1f
    //     0xba61f0: ldur            w2, [x1, #0x1f]
    // 0xba61f4: DecompressPointer r2
    //     0xba61f4: add             x2, x2, HEAP, lsl #32
    // 0xba61f8: mov             x1, x2
    // 0xba61fc: b               #0xba6208
    // 0xba6200: ldur            x0, [fp, #-8]
    // 0xba6204: r1 = false
    //     0xba6204: add             x1, NULL, #0x30  ; false
    // 0xba6208: ldur            x2, [fp, #-0x18]
    // 0xba620c: stur            x1, [fp, #-0x10]
    // 0xba6210: r16 = <EdgeInsets>
    //     0xba6210: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xba6214: ldr             x16, [x16, #0xda0]
    // 0xba6218: r30 = Instance_EdgeInsets
    //     0xba6218: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xba621c: ldr             lr, [lr, #0x1f0]
    // 0xba6220: stp             lr, x16, [SP]
    // 0xba6224: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba6224: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba6228: r0 = all()
    //     0xba6228: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba622c: ldur            x2, [fp, #-0x18]
    // 0xba6230: stur            x0, [fp, #-0x28]
    // 0xba6234: LoadField: r1 = r2->field_13
    //     0xba6234: ldur            w1, [x2, #0x13]
    // 0xba6238: DecompressPointer r1
    //     0xba6238: add             x1, x1, HEAP, lsl #32
    // 0xba623c: r0 = of()
    //     0xba623c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6240: LoadField: r1 = r0->field_5b
    //     0xba6240: ldur            w1, [x0, #0x5b]
    // 0xba6244: DecompressPointer r1
    //     0xba6244: add             x1, x1, HEAP, lsl #32
    // 0xba6248: r0 = LoadClassIdInstr(r1)
    //     0xba6248: ldur            x0, [x1, #-1]
    //     0xba624c: ubfx            x0, x0, #0xc, #0x14
    // 0xba6250: d0 = 0.100000
    //     0xba6250: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xba6254: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba6254: sub             lr, x0, #0xffa
    //     0xba6258: ldr             lr, [x21, lr, lsl #3]
    //     0xba625c: blr             lr
    // 0xba6260: stur            x0, [fp, #-0x30]
    // 0xba6264: r0 = BorderSide()
    //     0xba6264: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xba6268: mov             x1, x0
    // 0xba626c: ldur            x0, [fp, #-0x30]
    // 0xba6270: stur            x1, [fp, #-0x38]
    // 0xba6274: StoreField: r1->field_7 = r0
    //     0xba6274: stur            w0, [x1, #7]
    // 0xba6278: d0 = 1.000000
    //     0xba6278: fmov            d0, #1.00000000
    // 0xba627c: StoreField: r1->field_b = d0
    //     0xba627c: stur            d0, [x1, #0xb]
    // 0xba6280: r0 = Instance_BorderStyle
    //     0xba6280: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xba6284: ldr             x0, [x0, #0xf68]
    // 0xba6288: StoreField: r1->field_13 = r0
    //     0xba6288: stur            w0, [x1, #0x13]
    // 0xba628c: d0 = -1.000000
    //     0xba628c: fmov            d0, #-1.00000000
    // 0xba6290: ArrayStore: r1[0] = d0  ; List_8
    //     0xba6290: stur            d0, [x1, #0x17]
    // 0xba6294: r0 = RoundedRectangleBorder()
    //     0xba6294: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xba6298: mov             x1, x0
    // 0xba629c: r0 = Instance_BorderRadius
    //     0xba629c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xba62a0: ldr             x0, [x0, #0xf70]
    // 0xba62a4: StoreField: r1->field_b = r0
    //     0xba62a4: stur            w0, [x1, #0xb]
    // 0xba62a8: ldur            x0, [fp, #-0x38]
    // 0xba62ac: StoreField: r1->field_7 = r0
    //     0xba62ac: stur            w0, [x1, #7]
    // 0xba62b0: r16 = <RoundedRectangleBorder>
    //     0xba62b0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xba62b4: ldr             x16, [x16, #0xf78]
    // 0xba62b8: stp             x1, x16, [SP]
    // 0xba62bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba62bc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba62c0: r0 = all()
    //     0xba62c0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba62c4: stur            x0, [fp, #-0x30]
    // 0xba62c8: r0 = ButtonStyle()
    //     0xba62c8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xba62cc: mov             x1, x0
    // 0xba62d0: ldur            x0, [fp, #-0x28]
    // 0xba62d4: stur            x1, [fp, #-0x38]
    // 0xba62d8: StoreField: r1->field_23 = r0
    //     0xba62d8: stur            w0, [x1, #0x23]
    // 0xba62dc: ldur            x0, [fp, #-0x30]
    // 0xba62e0: StoreField: r1->field_43 = r0
    //     0xba62e0: stur            w0, [x1, #0x43]
    // 0xba62e4: r0 = TextButtonThemeData()
    //     0xba62e4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xba62e8: mov             x2, x0
    // 0xba62ec: ldur            x0, [fp, #-0x38]
    // 0xba62f0: stur            x2, [fp, #-0x28]
    // 0xba62f4: StoreField: r2->field_7 = r0
    //     0xba62f4: stur            w0, [x2, #7]
    // 0xba62f8: ldur            x0, [fp, #-0x18]
    // 0xba62fc: LoadField: r1 = r0->field_13
    //     0xba62fc: ldur            w1, [x0, #0x13]
    // 0xba6300: DecompressPointer r1
    //     0xba6300: add             x1, x1, HEAP, lsl #32
    // 0xba6304: r0 = of()
    //     0xba6304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6308: LoadField: r1 = r0->field_87
    //     0xba6308: ldur            w1, [x0, #0x87]
    // 0xba630c: DecompressPointer r1
    //     0xba630c: add             x1, x1, HEAP, lsl #32
    // 0xba6310: LoadField: r0 = r1->field_7
    //     0xba6310: ldur            w0, [x1, #7]
    // 0xba6314: DecompressPointer r0
    //     0xba6314: add             x0, x0, HEAP, lsl #32
    // 0xba6318: ldur            x2, [fp, #-0x18]
    // 0xba631c: stur            x0, [fp, #-0x30]
    // 0xba6320: LoadField: r1 = r2->field_13
    //     0xba6320: ldur            w1, [x2, #0x13]
    // 0xba6324: DecompressPointer r1
    //     0xba6324: add             x1, x1, HEAP, lsl #32
    // 0xba6328: r0 = of()
    //     0xba6328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba632c: LoadField: r1 = r0->field_5b
    //     0xba632c: ldur            w1, [x0, #0x5b]
    // 0xba6330: DecompressPointer r1
    //     0xba6330: add             x1, x1, HEAP, lsl #32
    // 0xba6334: r16 = 14.000000
    //     0xba6334: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba6338: ldr             x16, [x16, #0x1d8]
    // 0xba633c: stp             x16, x1, [SP]
    // 0xba6340: ldur            x1, [fp, #-0x30]
    // 0xba6344: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba6344: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba6348: ldr             x4, [x4, #0x9b8]
    // 0xba634c: r0 = copyWith()
    //     0xba634c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba6350: stur            x0, [fp, #-0x30]
    // 0xba6354: r0 = Text()
    //     0xba6354: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba6358: mov             x3, x0
    // 0xba635c: r0 = "OTHER OFFERS"
    //     0xba635c: add             x0, PP, #0x71, lsl #12  ; [pp+0x71490] "OTHER OFFERS"
    //     0xba6360: ldr             x0, [x0, #0x490]
    // 0xba6364: stur            x3, [fp, #-0x38]
    // 0xba6368: StoreField: r3->field_b = r0
    //     0xba6368: stur            w0, [x3, #0xb]
    // 0xba636c: ldur            x0, [fp, #-0x30]
    // 0xba6370: StoreField: r3->field_13 = r0
    //     0xba6370: stur            w0, [x3, #0x13]
    // 0xba6374: ldur            x2, [fp, #-0x18]
    // 0xba6378: r1 = Function '<anonymous closure>':.
    //     0xba6378: add             x1, PP, #0x71, lsl #12  ; [pp+0x71498] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xba637c: ldr             x1, [x1, #0x498]
    // 0xba6380: r0 = AllocateClosure()
    //     0xba6380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba6384: stur            x0, [fp, #-0x30]
    // 0xba6388: r0 = TextButton()
    //     0xba6388: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xba638c: mov             x1, x0
    // 0xba6390: ldur            x0, [fp, #-0x30]
    // 0xba6394: stur            x1, [fp, #-0x40]
    // 0xba6398: StoreField: r1->field_b = r0
    //     0xba6398: stur            w0, [x1, #0xb]
    // 0xba639c: r0 = false
    //     0xba639c: add             x0, NULL, #0x30  ; false
    // 0xba63a0: StoreField: r1->field_27 = r0
    //     0xba63a0: stur            w0, [x1, #0x27]
    // 0xba63a4: r2 = true
    //     0xba63a4: add             x2, NULL, #0x20  ; true
    // 0xba63a8: StoreField: r1->field_2f = r2
    //     0xba63a8: stur            w2, [x1, #0x2f]
    // 0xba63ac: ldur            x3, [fp, #-0x38]
    // 0xba63b0: StoreField: r1->field_37 = r3
    //     0xba63b0: stur            w3, [x1, #0x37]
    // 0xba63b4: r0 = TextButtonTheme()
    //     0xba63b4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xba63b8: mov             x1, x0
    // 0xba63bc: ldur            x0, [fp, #-0x28]
    // 0xba63c0: stur            x1, [fp, #-0x30]
    // 0xba63c4: StoreField: r1->field_f = r0
    //     0xba63c4: stur            w0, [x1, #0xf]
    // 0xba63c8: ldur            x0, [fp, #-0x40]
    // 0xba63cc: StoreField: r1->field_b = r0
    //     0xba63cc: stur            w0, [x1, #0xb]
    // 0xba63d0: r0 = SizedBox()
    //     0xba63d0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba63d4: mov             x1, x0
    // 0xba63d8: r0 = inf
    //     0xba63d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xba63dc: ldr             x0, [x0, #0x9f8]
    // 0xba63e0: stur            x1, [fp, #-0x28]
    // 0xba63e4: StoreField: r1->field_f = r0
    //     0xba63e4: stur            w0, [x1, #0xf]
    // 0xba63e8: ldur            x2, [fp, #-0x30]
    // 0xba63ec: StoreField: r1->field_b = r2
    //     0xba63ec: stur            w2, [x1, #0xb]
    // 0xba63f0: r0 = Align()
    //     0xba63f0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xba63f4: mov             x1, x0
    // 0xba63f8: r0 = Instance_Alignment
    //     0xba63f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xba63fc: ldr             x0, [x0, #0xb10]
    // 0xba6400: stur            x1, [fp, #-0x30]
    // 0xba6404: StoreField: r1->field_f = r0
    //     0xba6404: stur            w0, [x1, #0xf]
    // 0xba6408: ldur            x2, [fp, #-0x28]
    // 0xba640c: StoreField: r1->field_b = r2
    //     0xba640c: stur            w2, [x1, #0xb]
    // 0xba6410: r0 = Padding()
    //     0xba6410: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba6414: mov             x1, x0
    // 0xba6418: r0 = Instance_EdgeInsets
    //     0xba6418: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba641c: ldr             x0, [x0, #0x980]
    // 0xba6420: stur            x1, [fp, #-0x28]
    // 0xba6424: StoreField: r1->field_f = r0
    //     0xba6424: stur            w0, [x1, #0xf]
    // 0xba6428: ldur            x2, [fp, #-0x30]
    // 0xba642c: StoreField: r1->field_b = r2
    //     0xba642c: stur            w2, [x1, #0xb]
    // 0xba6430: r0 = Visibility()
    //     0xba6430: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba6434: mov             x1, x0
    // 0xba6438: ldur            x0, [fp, #-0x28]
    // 0xba643c: StoreField: r1->field_b = r0
    //     0xba643c: stur            w0, [x1, #0xb]
    // 0xba6440: r2 = Instance_SizedBox
    //     0xba6440: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba6444: StoreField: r1->field_f = r2
    //     0xba6444: stur            w2, [x1, #0xf]
    // 0xba6448: ldur            x0, [fp, #-0x10]
    // 0xba644c: StoreField: r1->field_13 = r0
    //     0xba644c: stur            w0, [x1, #0x13]
    // 0xba6450: r3 = false
    //     0xba6450: add             x3, NULL, #0x30  ; false
    // 0xba6454: ArrayStore: r1[0] = r3  ; List_4
    //     0xba6454: stur            w3, [x1, #0x17]
    // 0xba6458: StoreField: r1->field_1b = r3
    //     0xba6458: stur            w3, [x1, #0x1b]
    // 0xba645c: StoreField: r1->field_1f = r3
    //     0xba645c: stur            w3, [x1, #0x1f]
    // 0xba6460: StoreField: r1->field_23 = r3
    //     0xba6460: stur            w3, [x1, #0x23]
    // 0xba6464: StoreField: r1->field_27 = r3
    //     0xba6464: stur            w3, [x1, #0x27]
    // 0xba6468: StoreField: r1->field_2b = r3
    //     0xba6468: stur            w3, [x1, #0x2b]
    // 0xba646c: mov             x0, x1
    // 0xba6470: ldur            x1, [fp, #-0x20]
    // 0xba6474: ArrayStore: r1[7] = r0  ; List_4
    //     0xba6474: add             x25, x1, #0x2b
    //     0xba6478: str             w0, [x25]
    //     0xba647c: tbz             w0, #0, #0xba6498
    //     0xba6480: ldurb           w16, [x1, #-1]
    //     0xba6484: ldurb           w17, [x0, #-1]
    //     0xba6488: and             x16, x17, x16, lsr #2
    //     0xba648c: tst             x16, HEAP, lsr #32
    //     0xba6490: b.eq            #0xba6498
    //     0xba6494: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba6498: ldur            x1, [fp, #-8]
    // 0xba649c: LoadField: r0 = r1->field_b
    //     0xba649c: ldur            w0, [x1, #0xb]
    // 0xba64a0: DecompressPointer r0
    //     0xba64a0: add             x0, x0, HEAP, lsl #32
    // 0xba64a4: cmp             w0, NULL
    // 0xba64a8: b.eq            #0xba6b08
    // 0xba64ac: LoadField: r4 = r0->field_13
    //     0xba64ac: ldur            w4, [x0, #0x13]
    // 0xba64b0: DecompressPointer r4
    //     0xba64b0: add             x4, x4, HEAP, lsl #32
    // 0xba64b4: r0 = LoadClassIdInstr(r4)
    //     0xba64b4: ldur            x0, [x4, #-1]
    //     0xba64b8: ubfx            x0, x0, #0xc, #0x14
    // 0xba64bc: r16 = "checkout_offers"
    //     0xba64bc: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xba64c0: ldr             x16, [x16, #0x1c8]
    // 0xba64c4: stp             x16, x4, [SP]
    // 0xba64c8: mov             lr, x0
    // 0xba64cc: ldr             lr, [x21, lr, lsl #3]
    // 0xba64d0: blr             lr
    // 0xba64d4: tbz             w0, #4, #0xba64fc
    // 0xba64d8: ldur            x0, [fp, #-8]
    // 0xba64dc: LoadField: r1 = r0->field_b
    //     0xba64dc: ldur            w1, [x0, #0xb]
    // 0xba64e0: DecompressPointer r1
    //     0xba64e0: add             x1, x1, HEAP, lsl #32
    // 0xba64e4: cmp             w1, NULL
    // 0xba64e8: b.eq            #0xba6b0c
    // 0xba64ec: LoadField: r2 = r1->field_1f
    //     0xba64ec: ldur            w2, [x1, #0x1f]
    // 0xba64f0: DecompressPointer r2
    //     0xba64f0: add             x2, x2, HEAP, lsl #32
    // 0xba64f4: mov             x1, x2
    // 0xba64f8: b               #0xba6504
    // 0xba64fc: ldur            x0, [fp, #-8]
    // 0xba6500: r1 = false
    //     0xba6500: add             x1, NULL, #0x30  ; false
    // 0xba6504: stur            x1, [fp, #-0x10]
    // 0xba6508: r16 = <EdgeInsets>
    //     0xba6508: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xba650c: ldr             x16, [x16, #0xda0]
    // 0xba6510: r30 = Instance_EdgeInsets
    //     0xba6510: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xba6514: ldr             lr, [lr, #0x1f0]
    // 0xba6518: stp             lr, x16, [SP]
    // 0xba651c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba651c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba6520: r0 = all()
    //     0xba6520: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba6524: mov             x2, x0
    // 0xba6528: ldur            x1, [fp, #-8]
    // 0xba652c: stur            x2, [fp, #-0x28]
    // 0xba6530: LoadField: r0 = r1->field_b
    //     0xba6530: ldur            w0, [x1, #0xb]
    // 0xba6534: DecompressPointer r0
    //     0xba6534: add             x0, x0, HEAP, lsl #32
    // 0xba6538: cmp             w0, NULL
    // 0xba653c: b.eq            #0xba6b10
    // 0xba6540: LoadField: r3 = r0->field_f
    //     0xba6540: ldur            w3, [x0, #0xf]
    // 0xba6544: DecompressPointer r3
    //     0xba6544: add             x3, x3, HEAP, lsl #32
    // 0xba6548: LoadField: r4 = r0->field_b
    //     0xba6548: ldur            w4, [x0, #0xb]
    // 0xba654c: DecompressPointer r4
    //     0xba654c: add             x4, x4, HEAP, lsl #32
    // 0xba6550: cmp             w4, NULL
    // 0xba6554: b.ne            #0xba6560
    // 0xba6558: r0 = Null
    //     0xba6558: mov             x0, NULL
    // 0xba655c: b               #0xba6568
    // 0xba6560: LoadField: r0 = r4->field_7
    //     0xba6560: ldur            w0, [x4, #7]
    // 0xba6564: DecompressPointer r0
    //     0xba6564: add             x0, x0, HEAP, lsl #32
    // 0xba6568: r4 = LoadClassIdInstr(r3)
    //     0xba6568: ldur            x4, [x3, #-1]
    //     0xba656c: ubfx            x4, x4, #0xc, #0x14
    // 0xba6570: stp             x0, x3, [SP]
    // 0xba6574: mov             x0, x4
    // 0xba6578: mov             lr, x0
    // 0xba657c: ldr             lr, [x21, lr, lsl #3]
    // 0xba6580: blr             lr
    // 0xba6584: tbnz            w0, #4, #0xba6590
    // 0xba6588: r1 = Instance_Color
    //     0xba6588: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba658c: b               #0xba6604
    // 0xba6590: ldur            x0, [fp, #-8]
    // 0xba6594: LoadField: r1 = r0->field_b
    //     0xba6594: ldur            w1, [x0, #0xb]
    // 0xba6598: DecompressPointer r1
    //     0xba6598: add             x1, x1, HEAP, lsl #32
    // 0xba659c: cmp             w1, NULL
    // 0xba65a0: b.eq            #0xba6b14
    // 0xba65a4: LoadField: r2 = r1->field_b
    //     0xba65a4: ldur            w2, [x1, #0xb]
    // 0xba65a8: DecompressPointer r2
    //     0xba65a8: add             x2, x2, HEAP, lsl #32
    // 0xba65ac: cmp             w2, NULL
    // 0xba65b0: b.ne            #0xba65bc
    // 0xba65b4: r1 = Null
    //     0xba65b4: mov             x1, NULL
    // 0xba65b8: b               #0xba65c4
    // 0xba65bc: LoadField: r1 = r2->field_27
    //     0xba65bc: ldur            w1, [x2, #0x27]
    // 0xba65c0: DecompressPointer r1
    //     0xba65c0: add             x1, x1, HEAP, lsl #32
    // 0xba65c4: cmp             w1, NULL
    // 0xba65c8: b.eq            #0xba65f0
    // 0xba65cc: tbnz            w1, #4, #0xba65f0
    // 0xba65d0: ldur            x2, [fp, #-0x18]
    // 0xba65d4: LoadField: r1 = r2->field_13
    //     0xba65d4: ldur            w1, [x2, #0x13]
    // 0xba65d8: DecompressPointer r1
    //     0xba65d8: add             x1, x1, HEAP, lsl #32
    // 0xba65dc: r0 = of()
    //     0xba65dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba65e0: LoadField: r1 = r0->field_5b
    //     0xba65e0: ldur            w1, [x0, #0x5b]
    // 0xba65e4: DecompressPointer r1
    //     0xba65e4: add             x1, x1, HEAP, lsl #32
    // 0xba65e8: mov             x0, x1
    // 0xba65ec: b               #0xba6600
    // 0xba65f0: r1 = Instance_MaterialColor
    //     0xba65f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xba65f4: ldr             x1, [x1, #0xdc0]
    // 0xba65f8: d0 = 0.200000
    //     0xba65f8: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xba65fc: r0 = withOpacity()
    //     0xba65fc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba6600: mov             x1, x0
    // 0xba6604: ldur            x0, [fp, #-8]
    // 0xba6608: r16 = <Color>
    //     0xba6608: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xba660c: ldr             x16, [x16, #0xf80]
    // 0xba6610: stp             x1, x16, [SP]
    // 0xba6614: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba6614: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba6618: r0 = all()
    //     0xba6618: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba661c: mov             x2, x0
    // 0xba6620: ldur            x1, [fp, #-8]
    // 0xba6624: stur            x2, [fp, #-0x30]
    // 0xba6628: LoadField: r0 = r1->field_b
    //     0xba6628: ldur            w0, [x1, #0xb]
    // 0xba662c: DecompressPointer r0
    //     0xba662c: add             x0, x0, HEAP, lsl #32
    // 0xba6630: cmp             w0, NULL
    // 0xba6634: b.eq            #0xba6b18
    // 0xba6638: LoadField: r3 = r0->field_f
    //     0xba6638: ldur            w3, [x0, #0xf]
    // 0xba663c: DecompressPointer r3
    //     0xba663c: add             x3, x3, HEAP, lsl #32
    // 0xba6640: LoadField: r4 = r0->field_b
    //     0xba6640: ldur            w4, [x0, #0xb]
    // 0xba6644: DecompressPointer r4
    //     0xba6644: add             x4, x4, HEAP, lsl #32
    // 0xba6648: cmp             w4, NULL
    // 0xba664c: b.ne            #0xba6658
    // 0xba6650: r0 = Null
    //     0xba6650: mov             x0, NULL
    // 0xba6654: b               #0xba6660
    // 0xba6658: LoadField: r0 = r4->field_7
    //     0xba6658: ldur            w0, [x4, #7]
    // 0xba665c: DecompressPointer r0
    //     0xba665c: add             x0, x0, HEAP, lsl #32
    // 0xba6660: r4 = LoadClassIdInstr(r3)
    //     0xba6660: ldur            x4, [x3, #-1]
    //     0xba6664: ubfx            x4, x4, #0xc, #0x14
    // 0xba6668: stp             x0, x3, [SP]
    // 0xba666c: mov             x0, x4
    // 0xba6670: mov             lr, x0
    // 0xba6674: ldr             lr, [x21, lr, lsl #3]
    // 0xba6678: blr             lr
    // 0xba667c: tbnz            w0, #4, #0xba668c
    // 0xba6680: r3 = Instance_RoundedRectangleBorder
    //     0xba6680: add             x3, PP, #0x71, lsl #12  ; [pp+0x714a0] Obj!RoundedRectangleBorder@d5ac41
    //     0xba6684: ldr             x3, [x3, #0x4a0]
    // 0xba6688: b               #0xba6694
    // 0xba668c: r3 = Instance_RoundedRectangleBorder
    //     0xba668c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xba6690: ldr             x3, [x3, #0xd68]
    // 0xba6694: ldur            x0, [fp, #-8]
    // 0xba6698: ldur            x2, [fp, #-0x28]
    // 0xba669c: ldur            x1, [fp, #-0x30]
    // 0xba66a0: r16 = <RoundedRectangleBorder>
    //     0xba66a0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xba66a4: ldr             x16, [x16, #0xf78]
    // 0xba66a8: stp             x3, x16, [SP]
    // 0xba66ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba66ac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba66b0: r0 = all()
    //     0xba66b0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba66b4: stur            x0, [fp, #-0x38]
    // 0xba66b8: r0 = ButtonStyle()
    //     0xba66b8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xba66bc: mov             x1, x0
    // 0xba66c0: ldur            x0, [fp, #-0x30]
    // 0xba66c4: stur            x1, [fp, #-0x40]
    // 0xba66c8: StoreField: r1->field_b = r0
    //     0xba66c8: stur            w0, [x1, #0xb]
    // 0xba66cc: ldur            x0, [fp, #-0x28]
    // 0xba66d0: StoreField: r1->field_23 = r0
    //     0xba66d0: stur            w0, [x1, #0x23]
    // 0xba66d4: ldur            x0, [fp, #-0x38]
    // 0xba66d8: StoreField: r1->field_43 = r0
    //     0xba66d8: stur            w0, [x1, #0x43]
    // 0xba66dc: r0 = TextButtonThemeData()
    //     0xba66dc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xba66e0: mov             x1, x0
    // 0xba66e4: ldur            x0, [fp, #-0x40]
    // 0xba66e8: stur            x1, [fp, #-0x28]
    // 0xba66ec: StoreField: r1->field_7 = r0
    //     0xba66ec: stur            w0, [x1, #7]
    // 0xba66f0: ldur            x2, [fp, #-8]
    // 0xba66f4: LoadField: r0 = r2->field_b
    //     0xba66f4: ldur            w0, [x2, #0xb]
    // 0xba66f8: DecompressPointer r0
    //     0xba66f8: add             x0, x0, HEAP, lsl #32
    // 0xba66fc: cmp             w0, NULL
    // 0xba6700: b.eq            #0xba6b1c
    // 0xba6704: LoadField: r3 = r0->field_f
    //     0xba6704: ldur            w3, [x0, #0xf]
    // 0xba6708: DecompressPointer r3
    //     0xba6708: add             x3, x3, HEAP, lsl #32
    // 0xba670c: LoadField: r4 = r0->field_b
    //     0xba670c: ldur            w4, [x0, #0xb]
    // 0xba6710: DecompressPointer r4
    //     0xba6710: add             x4, x4, HEAP, lsl #32
    // 0xba6714: cmp             w4, NULL
    // 0xba6718: b.ne            #0xba6724
    // 0xba671c: r0 = Null
    //     0xba671c: mov             x0, NULL
    // 0xba6720: b               #0xba672c
    // 0xba6724: LoadField: r0 = r4->field_7
    //     0xba6724: ldur            w0, [x4, #7]
    // 0xba6728: DecompressPointer r0
    //     0xba6728: add             x0, x0, HEAP, lsl #32
    // 0xba672c: r4 = LoadClassIdInstr(r3)
    //     0xba672c: ldur            x4, [x3, #-1]
    //     0xba6730: ubfx            x4, x4, #0xc, #0x14
    // 0xba6734: stp             x0, x3, [SP]
    // 0xba6738: mov             x0, x4
    // 0xba673c: mov             lr, x0
    // 0xba6740: ldr             lr, [x21, lr, lsl #3]
    // 0xba6744: blr             lr
    // 0xba6748: tbnz            w0, #4, #0xba6874
    // 0xba674c: ldur            x0, [fp, #-8]
    // 0xba6750: ldur            x2, [fp, #-0x18]
    // 0xba6754: LoadField: r1 = r2->field_13
    //     0xba6754: ldur            w1, [x2, #0x13]
    // 0xba6758: DecompressPointer r1
    //     0xba6758: add             x1, x1, HEAP, lsl #32
    // 0xba675c: r0 = of()
    //     0xba675c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6760: LoadField: r1 = r0->field_87
    //     0xba6760: ldur            w1, [x0, #0x87]
    // 0xba6764: DecompressPointer r1
    //     0xba6764: add             x1, x1, HEAP, lsl #32
    // 0xba6768: LoadField: r2 = r1->field_7
    //     0xba6768: ldur            w2, [x1, #7]
    // 0xba676c: DecompressPointer r2
    //     0xba676c: add             x2, x2, HEAP, lsl #32
    // 0xba6770: ldur            x1, [fp, #-8]
    // 0xba6774: stur            x2, [fp, #-0x30]
    // 0xba6778: LoadField: r0 = r1->field_b
    //     0xba6778: ldur            w0, [x1, #0xb]
    // 0xba677c: DecompressPointer r0
    //     0xba677c: add             x0, x0, HEAP, lsl #32
    // 0xba6780: cmp             w0, NULL
    // 0xba6784: b.eq            #0xba6b20
    // 0xba6788: LoadField: r3 = r0->field_f
    //     0xba6788: ldur            w3, [x0, #0xf]
    // 0xba678c: DecompressPointer r3
    //     0xba678c: add             x3, x3, HEAP, lsl #32
    // 0xba6790: LoadField: r4 = r0->field_b
    //     0xba6790: ldur            w4, [x0, #0xb]
    // 0xba6794: DecompressPointer r4
    //     0xba6794: add             x4, x4, HEAP, lsl #32
    // 0xba6798: cmp             w4, NULL
    // 0xba679c: b.ne            #0xba67a8
    // 0xba67a0: r0 = Null
    //     0xba67a0: mov             x0, NULL
    // 0xba67a4: b               #0xba67b0
    // 0xba67a8: LoadField: r0 = r4->field_7
    //     0xba67a8: ldur            w0, [x4, #7]
    // 0xba67ac: DecompressPointer r0
    //     0xba67ac: add             x0, x0, HEAP, lsl #32
    // 0xba67b0: r4 = LoadClassIdInstr(r3)
    //     0xba67b0: ldur            x4, [x3, #-1]
    //     0xba67b4: ubfx            x4, x4, #0xc, #0x14
    // 0xba67b8: stp             x0, x3, [SP]
    // 0xba67bc: mov             x0, x4
    // 0xba67c0: mov             lr, x0
    // 0xba67c4: ldr             lr, [x21, lr, lsl #3]
    // 0xba67c8: blr             lr
    // 0xba67cc: tbnz            w0, #4, #0xba67dc
    // 0xba67d0: r0 = Instance_Color
    //     0xba67d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba67d4: ldr             x0, [x0, #0x858]
    // 0xba67d8: b               #0xba6830
    // 0xba67dc: ldur            x0, [fp, #-8]
    // 0xba67e0: LoadField: r1 = r0->field_b
    //     0xba67e0: ldur            w1, [x0, #0xb]
    // 0xba67e4: DecompressPointer r1
    //     0xba67e4: add             x1, x1, HEAP, lsl #32
    // 0xba67e8: cmp             w1, NULL
    // 0xba67ec: b.eq            #0xba6b24
    // 0xba67f0: LoadField: r0 = r1->field_b
    //     0xba67f0: ldur            w0, [x1, #0xb]
    // 0xba67f4: DecompressPointer r0
    //     0xba67f4: add             x0, x0, HEAP, lsl #32
    // 0xba67f8: cmp             w0, NULL
    // 0xba67fc: b.ne            #0xba6808
    // 0xba6800: r0 = Null
    //     0xba6800: mov             x0, NULL
    // 0xba6804: b               #0xba6814
    // 0xba6808: LoadField: r1 = r0->field_27
    //     0xba6808: ldur            w1, [x0, #0x27]
    // 0xba680c: DecompressPointer r1
    //     0xba680c: add             x1, x1, HEAP, lsl #32
    // 0xba6810: mov             x0, x1
    // 0xba6814: cmp             w0, NULL
    // 0xba6818: b.eq            #0xba6828
    // 0xba681c: tbnz            w0, #4, #0xba6828
    // 0xba6820: r0 = Instance_Color
    //     0xba6820: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba6824: b               #0xba6830
    // 0xba6828: r0 = Instance_MaterialColor
    //     0xba6828: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xba682c: ldr             x0, [x0, #0xdc0]
    // 0xba6830: r16 = 14.000000
    //     0xba6830: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba6834: ldr             x16, [x16, #0x1d8]
    // 0xba6838: stp             x0, x16, [SP]
    // 0xba683c: ldur            x1, [fp, #-0x30]
    // 0xba6840: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba6840: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba6844: ldr             x4, [x4, #0xaa0]
    // 0xba6848: r0 = copyWith()
    //     0xba6848: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba684c: stur            x0, [fp, #-0x30]
    // 0xba6850: r0 = Text()
    //     0xba6850: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba6854: mov             x1, x0
    // 0xba6858: r0 = "REMOVE OFFER"
    //     0xba6858: add             x0, PP, #0x71, lsl #12  ; [pp+0x714a8] "REMOVE OFFER"
    //     0xba685c: ldr             x0, [x0, #0x4a8]
    // 0xba6860: StoreField: r1->field_b = r0
    //     0xba6860: stur            w0, [x1, #0xb]
    // 0xba6864: ldur            x0, [fp, #-0x30]
    // 0xba6868: StoreField: r1->field_13 = r0
    //     0xba6868: stur            w0, [x1, #0x13]
    // 0xba686c: mov             x5, x1
    // 0xba6870: b               #0xba6938
    // 0xba6874: ldur            x0, [fp, #-8]
    // 0xba6878: ldur            x2, [fp, #-0x18]
    // 0xba687c: LoadField: r1 = r2->field_13
    //     0xba687c: ldur            w1, [x2, #0x13]
    // 0xba6880: DecompressPointer r1
    //     0xba6880: add             x1, x1, HEAP, lsl #32
    // 0xba6884: r0 = of()
    //     0xba6884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6888: LoadField: r1 = r0->field_87
    //     0xba6888: ldur            w1, [x0, #0x87]
    // 0xba688c: DecompressPointer r1
    //     0xba688c: add             x1, x1, HEAP, lsl #32
    // 0xba6890: LoadField: r0 = r1->field_7
    //     0xba6890: ldur            w0, [x1, #7]
    // 0xba6894: DecompressPointer r0
    //     0xba6894: add             x0, x0, HEAP, lsl #32
    // 0xba6898: ldur            x1, [fp, #-8]
    // 0xba689c: stur            x0, [fp, #-0x30]
    // 0xba68a0: LoadField: r2 = r1->field_b
    //     0xba68a0: ldur            w2, [x1, #0xb]
    // 0xba68a4: DecompressPointer r2
    //     0xba68a4: add             x2, x2, HEAP, lsl #32
    // 0xba68a8: cmp             w2, NULL
    // 0xba68ac: b.eq            #0xba6b28
    // 0xba68b0: LoadField: r1 = r2->field_b
    //     0xba68b0: ldur            w1, [x2, #0xb]
    // 0xba68b4: DecompressPointer r1
    //     0xba68b4: add             x1, x1, HEAP, lsl #32
    // 0xba68b8: cmp             w1, NULL
    // 0xba68bc: b.ne            #0xba68c8
    // 0xba68c0: r1 = Null
    //     0xba68c0: mov             x1, NULL
    // 0xba68c4: b               #0xba68d4
    // 0xba68c8: LoadField: r2 = r1->field_27
    //     0xba68c8: ldur            w2, [x1, #0x27]
    // 0xba68cc: DecompressPointer r2
    //     0xba68cc: add             x2, x2, HEAP, lsl #32
    // 0xba68d0: mov             x1, x2
    // 0xba68d4: cmp             w1, NULL
    // 0xba68d8: b.eq            #0xba68e8
    // 0xba68dc: tbnz            w1, #4, #0xba68e8
    // 0xba68e0: r0 = Instance_Color
    //     0xba68e0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba68e4: b               #0xba68f8
    // 0xba68e8: r1 = Instance_MaterialColor
    //     0xba68e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xba68ec: ldr             x1, [x1, #0xdc0]
    // 0xba68f0: d0 = 0.600000
    //     0xba68f0: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xba68f4: r0 = withOpacity()
    //     0xba68f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba68f8: r16 = 14.000000
    //     0xba68f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba68fc: ldr             x16, [x16, #0x1d8]
    // 0xba6900: stp             x0, x16, [SP]
    // 0xba6904: ldur            x1, [fp, #-0x30]
    // 0xba6908: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba6908: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba690c: ldr             x4, [x4, #0xaa0]
    // 0xba6910: r0 = copyWith()
    //     0xba6910: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba6914: stur            x0, [fp, #-8]
    // 0xba6918: r0 = Text()
    //     0xba6918: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba691c: mov             x1, x0
    // 0xba6920: r0 = "APPLY NOW"
    //     0xba6920: add             x0, PP, #0x71, lsl #12  ; [pp+0x714b0] "APPLY NOW"
    //     0xba6924: ldr             x0, [x0, #0x4b0]
    // 0xba6928: StoreField: r1->field_b = r0
    //     0xba6928: stur            w0, [x1, #0xb]
    // 0xba692c: ldur            x0, [fp, #-8]
    // 0xba6930: StoreField: r1->field_13 = r0
    //     0xba6930: stur            w0, [x1, #0x13]
    // 0xba6934: mov             x5, x1
    // 0xba6938: ldur            x4, [fp, #-0x20]
    // 0xba693c: ldur            x3, [fp, #-0x10]
    // 0xba6940: ldur            x0, [fp, #-0x28]
    // 0xba6944: ldur            x2, [fp, #-0x18]
    // 0xba6948: stur            x5, [fp, #-8]
    // 0xba694c: r1 = Function '<anonymous closure>':.
    //     0xba694c: add             x1, PP, #0x71, lsl #12  ; [pp+0x714b8] AnonymousClosure: (0xba6b4c), in [package:customer_app/app/presentation/views/line/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xba5b10)
    //     0xba6950: ldr             x1, [x1, #0x4b8]
    // 0xba6954: r0 = AllocateClosure()
    //     0xba6954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba6958: stur            x0, [fp, #-0x18]
    // 0xba695c: r0 = TextButton()
    //     0xba695c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xba6960: mov             x1, x0
    // 0xba6964: ldur            x0, [fp, #-0x18]
    // 0xba6968: stur            x1, [fp, #-0x30]
    // 0xba696c: StoreField: r1->field_b = r0
    //     0xba696c: stur            w0, [x1, #0xb]
    // 0xba6970: r0 = false
    //     0xba6970: add             x0, NULL, #0x30  ; false
    // 0xba6974: StoreField: r1->field_27 = r0
    //     0xba6974: stur            w0, [x1, #0x27]
    // 0xba6978: r2 = true
    //     0xba6978: add             x2, NULL, #0x20  ; true
    // 0xba697c: StoreField: r1->field_2f = r2
    //     0xba697c: stur            w2, [x1, #0x2f]
    // 0xba6980: ldur            x2, [fp, #-8]
    // 0xba6984: StoreField: r1->field_37 = r2
    //     0xba6984: stur            w2, [x1, #0x37]
    // 0xba6988: r0 = TextButtonTheme()
    //     0xba6988: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xba698c: mov             x1, x0
    // 0xba6990: ldur            x0, [fp, #-0x28]
    // 0xba6994: stur            x1, [fp, #-8]
    // 0xba6998: StoreField: r1->field_f = r0
    //     0xba6998: stur            w0, [x1, #0xf]
    // 0xba699c: ldur            x0, [fp, #-0x30]
    // 0xba69a0: StoreField: r1->field_b = r0
    //     0xba69a0: stur            w0, [x1, #0xb]
    // 0xba69a4: r0 = SizedBox()
    //     0xba69a4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba69a8: mov             x1, x0
    // 0xba69ac: r0 = inf
    //     0xba69ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xba69b0: ldr             x0, [x0, #0x9f8]
    // 0xba69b4: stur            x1, [fp, #-0x18]
    // 0xba69b8: StoreField: r1->field_f = r0
    //     0xba69b8: stur            w0, [x1, #0xf]
    // 0xba69bc: ldur            x0, [fp, #-8]
    // 0xba69c0: StoreField: r1->field_b = r0
    //     0xba69c0: stur            w0, [x1, #0xb]
    // 0xba69c4: r0 = Align()
    //     0xba69c4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xba69c8: mov             x1, x0
    // 0xba69cc: r0 = Instance_Alignment
    //     0xba69cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xba69d0: ldr             x0, [x0, #0xb10]
    // 0xba69d4: stur            x1, [fp, #-8]
    // 0xba69d8: StoreField: r1->field_f = r0
    //     0xba69d8: stur            w0, [x1, #0xf]
    // 0xba69dc: ldur            x0, [fp, #-0x18]
    // 0xba69e0: StoreField: r1->field_b = r0
    //     0xba69e0: stur            w0, [x1, #0xb]
    // 0xba69e4: r0 = Padding()
    //     0xba69e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba69e8: mov             x1, x0
    // 0xba69ec: r0 = Instance_EdgeInsets
    //     0xba69ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba69f0: ldr             x0, [x0, #0x980]
    // 0xba69f4: stur            x1, [fp, #-0x18]
    // 0xba69f8: StoreField: r1->field_f = r0
    //     0xba69f8: stur            w0, [x1, #0xf]
    // 0xba69fc: ldur            x0, [fp, #-8]
    // 0xba6a00: StoreField: r1->field_b = r0
    //     0xba6a00: stur            w0, [x1, #0xb]
    // 0xba6a04: r0 = Visibility()
    //     0xba6a04: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba6a08: mov             x1, x0
    // 0xba6a0c: ldur            x0, [fp, #-0x18]
    // 0xba6a10: StoreField: r1->field_b = r0
    //     0xba6a10: stur            w0, [x1, #0xb]
    // 0xba6a14: r0 = Instance_SizedBox
    //     0xba6a14: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba6a18: StoreField: r1->field_f = r0
    //     0xba6a18: stur            w0, [x1, #0xf]
    // 0xba6a1c: ldur            x0, [fp, #-0x10]
    // 0xba6a20: StoreField: r1->field_13 = r0
    //     0xba6a20: stur            w0, [x1, #0x13]
    // 0xba6a24: r0 = false
    //     0xba6a24: add             x0, NULL, #0x30  ; false
    // 0xba6a28: ArrayStore: r1[0] = r0  ; List_4
    //     0xba6a28: stur            w0, [x1, #0x17]
    // 0xba6a2c: StoreField: r1->field_1b = r0
    //     0xba6a2c: stur            w0, [x1, #0x1b]
    // 0xba6a30: StoreField: r1->field_1f = r0
    //     0xba6a30: stur            w0, [x1, #0x1f]
    // 0xba6a34: StoreField: r1->field_23 = r0
    //     0xba6a34: stur            w0, [x1, #0x23]
    // 0xba6a38: StoreField: r1->field_27 = r0
    //     0xba6a38: stur            w0, [x1, #0x27]
    // 0xba6a3c: StoreField: r1->field_2b = r0
    //     0xba6a3c: stur            w0, [x1, #0x2b]
    // 0xba6a40: mov             x0, x1
    // 0xba6a44: ldur            x1, [fp, #-0x20]
    // 0xba6a48: ArrayStore: r1[8] = r0  ; List_4
    //     0xba6a48: add             x25, x1, #0x2f
    //     0xba6a4c: str             w0, [x25]
    //     0xba6a50: tbz             w0, #0, #0xba6a6c
    //     0xba6a54: ldurb           w16, [x1, #-1]
    //     0xba6a58: ldurb           w17, [x0, #-1]
    //     0xba6a5c: and             x16, x17, x16, lsr #2
    //     0xba6a60: tst             x16, HEAP, lsr #32
    //     0xba6a64: b.eq            #0xba6a6c
    //     0xba6a68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xba6a6c: r1 = <Widget>
    //     0xba6a6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba6a70: r0 = AllocateGrowableArray()
    //     0xba6a70: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba6a74: mov             x1, x0
    // 0xba6a78: ldur            x0, [fp, #-0x20]
    // 0xba6a7c: stur            x1, [fp, #-8]
    // 0xba6a80: StoreField: r1->field_f = r0
    //     0xba6a80: stur            w0, [x1, #0xf]
    // 0xba6a84: r0 = 18
    //     0xba6a84: movz            x0, #0x12
    // 0xba6a88: StoreField: r1->field_b = r0
    //     0xba6a88: stur            w0, [x1, #0xb]
    // 0xba6a8c: r0 = Column()
    //     0xba6a8c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba6a90: r1 = Instance_Axis
    //     0xba6a90: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba6a94: StoreField: r0->field_f = r1
    //     0xba6a94: stur            w1, [x0, #0xf]
    // 0xba6a98: r1 = Instance_MainAxisAlignment
    //     0xba6a98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba6a9c: ldr             x1, [x1, #0xa08]
    // 0xba6aa0: StoreField: r0->field_13 = r1
    //     0xba6aa0: stur            w1, [x0, #0x13]
    // 0xba6aa4: r1 = Instance_MainAxisSize
    //     0xba6aa4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xba6aa8: ldr             x1, [x1, #0xdd0]
    // 0xba6aac: ArrayStore: r0[0] = r1  ; List_4
    //     0xba6aac: stur            w1, [x0, #0x17]
    // 0xba6ab0: r1 = Instance_CrossAxisAlignment
    //     0xba6ab0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba6ab4: ldr             x1, [x1, #0x890]
    // 0xba6ab8: StoreField: r0->field_1b = r1
    //     0xba6ab8: stur            w1, [x0, #0x1b]
    // 0xba6abc: r1 = Instance_VerticalDirection
    //     0xba6abc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba6ac0: ldr             x1, [x1, #0xa20]
    // 0xba6ac4: StoreField: r0->field_23 = r1
    //     0xba6ac4: stur            w1, [x0, #0x23]
    // 0xba6ac8: r1 = Instance_Clip
    //     0xba6ac8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba6acc: ldr             x1, [x1, #0x38]
    // 0xba6ad0: StoreField: r0->field_2b = r1
    //     0xba6ad0: stur            w1, [x0, #0x2b]
    // 0xba6ad4: StoreField: r0->field_2f = rZR
    //     0xba6ad4: stur            xzr, [x0, #0x2f]
    // 0xba6ad8: ldur            x1, [fp, #-8]
    // 0xba6adc: StoreField: r0->field_b = r1
    //     0xba6adc: stur            w1, [x0, #0xb]
    // 0xba6ae0: LeaveFrame
    //     0xba6ae0: mov             SP, fp
    //     0xba6ae4: ldp             fp, lr, [SP], #0x10
    // 0xba6ae8: ret
    //     0xba6ae8: ret             
    // 0xba6aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba6aec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba6af0: b               #0xba5b38
    // 0xba6af4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6af4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6af8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6af8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6afc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6b28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba6b4c, size: 0x244
    // 0xba6b4c: EnterFrame
    //     0xba6b4c: stp             fp, lr, [SP, #-0x10]!
    //     0xba6b50: mov             fp, SP
    // 0xba6b54: AllocStack(0x28)
    //     0xba6b54: sub             SP, SP, #0x28
    // 0xba6b58: SetupParameters()
    //     0xba6b58: ldr             x0, [fp, #0x10]
    //     0xba6b5c: ldur            w1, [x0, #0x17]
    //     0xba6b60: add             x1, x1, HEAP, lsl #32
    //     0xba6b64: stur            x1, [fp, #-8]
    // 0xba6b68: CheckStackOverflow
    //     0xba6b68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba6b6c: cmp             SP, x16
    //     0xba6b70: b.ls            #0xba6d74
    // 0xba6b74: LoadField: r0 = r1->field_f
    //     0xba6b74: ldur            w0, [x1, #0xf]
    // 0xba6b78: DecompressPointer r0
    //     0xba6b78: add             x0, x0, HEAP, lsl #32
    // 0xba6b7c: LoadField: r2 = r0->field_b
    //     0xba6b7c: ldur            w2, [x0, #0xb]
    // 0xba6b80: DecompressPointer r2
    //     0xba6b80: add             x2, x2, HEAP, lsl #32
    // 0xba6b84: cmp             w2, NULL
    // 0xba6b88: b.eq            #0xba6d7c
    // 0xba6b8c: LoadField: r0 = r2->field_b
    //     0xba6b8c: ldur            w0, [x2, #0xb]
    // 0xba6b90: DecompressPointer r0
    //     0xba6b90: add             x0, x0, HEAP, lsl #32
    // 0xba6b94: cmp             w0, NULL
    // 0xba6b98: b.ne            #0xba6ba4
    // 0xba6b9c: r3 = Null
    //     0xba6b9c: mov             x3, NULL
    // 0xba6ba0: b               #0xba6bac
    // 0xba6ba4: LoadField: r3 = r0->field_27
    //     0xba6ba4: ldur            w3, [x0, #0x27]
    // 0xba6ba8: DecompressPointer r3
    //     0xba6ba8: add             x3, x3, HEAP, lsl #32
    // 0xba6bac: cmp             w3, NULL
    // 0xba6bb0: b.eq            #0xba6d64
    // 0xba6bb4: tbnz            w3, #4, #0xba6d64
    // 0xba6bb8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xba6bb8: ldur            w3, [x2, #0x17]
    // 0xba6bbc: DecompressPointer r3
    //     0xba6bbc: add             x3, x3, HEAP, lsl #32
    // 0xba6bc0: cmp             w3, NULL
    // 0xba6bc4: b.eq            #0xba6cac
    // 0xba6bc8: LoadField: r3 = r2->field_f
    //     0xba6bc8: ldur            w3, [x2, #0xf]
    // 0xba6bcc: DecompressPointer r3
    //     0xba6bcc: add             x3, x3, HEAP, lsl #32
    // 0xba6bd0: cmp             w0, NULL
    // 0xba6bd4: b.ne            #0xba6be0
    // 0xba6bd8: r0 = Null
    //     0xba6bd8: mov             x0, NULL
    // 0xba6bdc: b               #0xba6bec
    // 0xba6be0: LoadField: r2 = r0->field_7
    //     0xba6be0: ldur            w2, [x0, #7]
    // 0xba6be4: DecompressPointer r2
    //     0xba6be4: add             x2, x2, HEAP, lsl #32
    // 0xba6be8: mov             x0, x2
    // 0xba6bec: r2 = LoadClassIdInstr(r3)
    //     0xba6bec: ldur            x2, [x3, #-1]
    //     0xba6bf0: ubfx            x2, x2, #0xc, #0x14
    // 0xba6bf4: stp             x0, x3, [SP]
    // 0xba6bf8: mov             x0, x2
    // 0xba6bfc: mov             lr, x0
    // 0xba6c00: ldr             lr, [x21, lr, lsl #3]
    // 0xba6c04: blr             lr
    // 0xba6c08: tbz             w0, #4, #0xba6cac
    // 0xba6c0c: ldur            x0, [fp, #-8]
    // 0xba6c10: LoadField: r1 = r0->field_f
    //     0xba6c10: ldur            w1, [x0, #0xf]
    // 0xba6c14: DecompressPointer r1
    //     0xba6c14: add             x1, x1, HEAP, lsl #32
    // 0xba6c18: LoadField: r2 = r1->field_b
    //     0xba6c18: ldur            w2, [x1, #0xb]
    // 0xba6c1c: DecompressPointer r2
    //     0xba6c1c: add             x2, x2, HEAP, lsl #32
    // 0xba6c20: cmp             w2, NULL
    // 0xba6c24: b.eq            #0xba6d80
    // 0xba6c28: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xba6c28: ldur            w1, [x2, #0x17]
    // 0xba6c2c: DecompressPointer r1
    //     0xba6c2c: add             x1, x1, HEAP, lsl #32
    // 0xba6c30: cmp             w1, NULL
    // 0xba6c34: b.eq            #0xba6d84
    // 0xba6c38: LoadField: r3 = r2->field_b
    //     0xba6c38: ldur            w3, [x2, #0xb]
    // 0xba6c3c: DecompressPointer r3
    //     0xba6c3c: add             x3, x3, HEAP, lsl #32
    // 0xba6c40: cmp             w3, NULL
    // 0xba6c44: b.ne            #0xba6c50
    // 0xba6c48: r2 = Null
    //     0xba6c48: mov             x2, NULL
    // 0xba6c4c: b               #0xba6c58
    // 0xba6c50: LoadField: r2 = r3->field_7
    //     0xba6c50: ldur            w2, [x3, #7]
    // 0xba6c54: DecompressPointer r2
    //     0xba6c54: add             x2, x2, HEAP, lsl #32
    // 0xba6c58: cmp             w2, NULL
    // 0xba6c5c: b.ne            #0xba6c64
    // 0xba6c60: r2 = ""
    //     0xba6c60: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba6c64: cmp             w3, NULL
    // 0xba6c68: b.ne            #0xba6c74
    // 0xba6c6c: r3 = Null
    //     0xba6c6c: mov             x3, NULL
    // 0xba6c70: b               #0xba6c80
    // 0xba6c74: LoadField: r4 = r3->field_b
    //     0xba6c74: ldur            w4, [x3, #0xb]
    // 0xba6c78: DecompressPointer r4
    //     0xba6c78: add             x4, x4, HEAP, lsl #32
    // 0xba6c7c: mov             x3, x4
    // 0xba6c80: stp             x2, x1, [SP, #0x10]
    // 0xba6c84: r16 = "apply"
    //     0xba6c84: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xba6c88: ldr             x16, [x16, #0xfe8]
    // 0xba6c8c: stp             x16, x3, [SP]
    // 0xba6c90: r4 = 0
    //     0xba6c90: movz            x4, #0
    // 0xba6c94: ldr             x0, [SP, #0x18]
    // 0xba6c98: r16 = UnlinkedCall_0x613b5c
    //     0xba6c98: add             x16, PP, #0x71, lsl #12  ; [pp+0x714c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba6c9c: add             x16, x16, #0x4c0
    // 0xba6ca0: ldp             x5, lr, [x16]
    // 0xba6ca4: blr             lr
    // 0xba6ca8: b               #0xba6d48
    // 0xba6cac: ldur            x0, [fp, #-8]
    // 0xba6cb0: LoadField: r1 = r0->field_f
    //     0xba6cb0: ldur            w1, [x0, #0xf]
    // 0xba6cb4: DecompressPointer r1
    //     0xba6cb4: add             x1, x1, HEAP, lsl #32
    // 0xba6cb8: LoadField: r2 = r1->field_b
    //     0xba6cb8: ldur            w2, [x1, #0xb]
    // 0xba6cbc: DecompressPointer r2
    //     0xba6cbc: add             x2, x2, HEAP, lsl #32
    // 0xba6cc0: cmp             w2, NULL
    // 0xba6cc4: b.eq            #0xba6d88
    // 0xba6cc8: LoadField: r1 = r2->field_1b
    //     0xba6cc8: ldur            w1, [x2, #0x1b]
    // 0xba6ccc: DecompressPointer r1
    //     0xba6ccc: add             x1, x1, HEAP, lsl #32
    // 0xba6cd0: cmp             w1, NULL
    // 0xba6cd4: b.eq            #0xba6d8c
    // 0xba6cd8: LoadField: r3 = r2->field_b
    //     0xba6cd8: ldur            w3, [x2, #0xb]
    // 0xba6cdc: DecompressPointer r3
    //     0xba6cdc: add             x3, x3, HEAP, lsl #32
    // 0xba6ce0: cmp             w3, NULL
    // 0xba6ce4: b.ne            #0xba6cf0
    // 0xba6ce8: r2 = Null
    //     0xba6ce8: mov             x2, NULL
    // 0xba6cec: b               #0xba6cf8
    // 0xba6cf0: LoadField: r2 = r3->field_7
    //     0xba6cf0: ldur            w2, [x3, #7]
    // 0xba6cf4: DecompressPointer r2
    //     0xba6cf4: add             x2, x2, HEAP, lsl #32
    // 0xba6cf8: cmp             w2, NULL
    // 0xba6cfc: b.ne            #0xba6d04
    // 0xba6d00: r2 = ""
    //     0xba6d00: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba6d04: cmp             w3, NULL
    // 0xba6d08: b.ne            #0xba6d14
    // 0xba6d0c: r3 = Null
    //     0xba6d0c: mov             x3, NULL
    // 0xba6d10: b               #0xba6d20
    // 0xba6d14: LoadField: r4 = r3->field_b
    //     0xba6d14: ldur            w4, [x3, #0xb]
    // 0xba6d18: DecompressPointer r4
    //     0xba6d18: add             x4, x4, HEAP, lsl #32
    // 0xba6d1c: mov             x3, x4
    // 0xba6d20: stp             x2, x1, [SP, #0x10]
    // 0xba6d24: r16 = "remove"
    //     0xba6d24: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xba6d28: ldr             x16, [x16, #0xff0]
    // 0xba6d2c: stp             x16, x3, [SP]
    // 0xba6d30: r4 = 0
    //     0xba6d30: movz            x4, #0
    // 0xba6d34: ldr             x0, [SP, #0x18]
    // 0xba6d38: r16 = UnlinkedCall_0x613b5c
    //     0xba6d38: add             x16, PP, #0x71, lsl #12  ; [pp+0x714d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba6d3c: add             x16, x16, #0x4d0
    // 0xba6d40: ldp             x5, lr, [x16]
    // 0xba6d44: blr             lr
    // 0xba6d48: ldur            x0, [fp, #-8]
    // 0xba6d4c: LoadField: r1 = r0->field_13
    //     0xba6d4c: ldur            w1, [x0, #0x13]
    // 0xba6d50: DecompressPointer r1
    //     0xba6d50: add             x1, x1, HEAP, lsl #32
    // 0xba6d54: r16 = <Object?>
    //     0xba6d54: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xba6d58: stp             x1, x16, [SP]
    // 0xba6d5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba6d5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba6d60: r0 = pop()
    //     0xba6d60: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xba6d64: r0 = Null
    //     0xba6d64: mov             x0, NULL
    // 0xba6d68: LeaveFrame
    //     0xba6d68: mov             SP, fp
    //     0xba6d6c: ldp             fp, lr, [SP], #0x10
    // 0xba6d70: ret
    //     0xba6d70: ret             
    // 0xba6d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba6d74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba6d78: b               #0xba6b74
    // 0xba6d7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6d7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6d80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6d80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6d84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6d84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6d88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6d88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6d8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6d8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xba6d90, size: 0x214
    // 0xba6d90: EnterFrame
    //     0xba6d90: stp             fp, lr, [SP, #-0x10]!
    //     0xba6d94: mov             fp, SP
    // 0xba6d98: AllocStack(0x28)
    //     0xba6d98: sub             SP, SP, #0x28
    // 0xba6d9c: SetupParameters()
    //     0xba6d9c: ldr             x0, [fp, #0x20]
    //     0xba6da0: ldur            w1, [x0, #0x17]
    //     0xba6da4: add             x1, x1, HEAP, lsl #32
    // 0xba6da8: CheckStackOverflow
    //     0xba6da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba6dac: cmp             SP, x16
    //     0xba6db0: b.ls            #0xba6f94
    // 0xba6db4: LoadField: r0 = r1->field_f
    //     0xba6db4: ldur            w0, [x1, #0xf]
    // 0xba6db8: DecompressPointer r0
    //     0xba6db8: add             x0, x0, HEAP, lsl #32
    // 0xba6dbc: LoadField: r1 = r0->field_b
    //     0xba6dbc: ldur            w1, [x0, #0xb]
    // 0xba6dc0: DecompressPointer r1
    //     0xba6dc0: add             x1, x1, HEAP, lsl #32
    // 0xba6dc4: cmp             w1, NULL
    // 0xba6dc8: b.eq            #0xba6f9c
    // 0xba6dcc: LoadField: r0 = r1->field_b
    //     0xba6dcc: ldur            w0, [x1, #0xb]
    // 0xba6dd0: DecompressPointer r0
    //     0xba6dd0: add             x0, x0, HEAP, lsl #32
    // 0xba6dd4: cmp             w0, NULL
    // 0xba6dd8: b.ne            #0xba6de4
    // 0xba6ddc: r0 = Null
    //     0xba6ddc: mov             x0, NULL
    // 0xba6de0: b               #0xba6e38
    // 0xba6de4: LoadField: r2 = r0->field_1f
    //     0xba6de4: ldur            w2, [x0, #0x1f]
    // 0xba6de8: DecompressPointer r2
    //     0xba6de8: add             x2, x2, HEAP, lsl #32
    // 0xba6dec: cmp             w2, NULL
    // 0xba6df0: b.ne            #0xba6dfc
    // 0xba6df4: r0 = Null
    //     0xba6df4: mov             x0, NULL
    // 0xba6df8: b               #0xba6e38
    // 0xba6dfc: ldr             x0, [fp, #0x10]
    // 0xba6e00: LoadField: r1 = r2->field_b
    //     0xba6e00: ldur            w1, [x2, #0xb]
    // 0xba6e04: r3 = LoadInt32Instr(r0)
    //     0xba6e04: sbfx            x3, x0, #1, #0x1f
    //     0xba6e08: tbz             w0, #0, #0xba6e10
    //     0xba6e0c: ldur            x3, [x0, #7]
    // 0xba6e10: r0 = LoadInt32Instr(r1)
    //     0xba6e10: sbfx            x0, x1, #1, #0x1f
    // 0xba6e14: mov             x1, x3
    // 0xba6e18: cmp             x1, x0
    // 0xba6e1c: b.hs            #0xba6fa0
    // 0xba6e20: LoadField: r0 = r2->field_f
    //     0xba6e20: ldur            w0, [x2, #0xf]
    // 0xba6e24: DecompressPointer r0
    //     0xba6e24: add             x0, x0, HEAP, lsl #32
    // 0xba6e28: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xba6e28: add             x16, x0, x3, lsl #2
    //     0xba6e2c: ldur            w1, [x16, #0xf]
    // 0xba6e30: DecompressPointer r1
    //     0xba6e30: add             x1, x1, HEAP, lsl #32
    // 0xba6e34: mov             x0, x1
    // 0xba6e38: cmp             w0, NULL
    // 0xba6e3c: b.ne            #0xba6e44
    // 0xba6e40: r0 = ""
    //     0xba6e40: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba6e44: ldr             x1, [fp, #0x18]
    // 0xba6e48: stur            x0, [fp, #-8]
    // 0xba6e4c: r0 = of()
    //     0xba6e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba6e50: LoadField: r1 = r0->field_87
    //     0xba6e50: ldur            w1, [x0, #0x87]
    // 0xba6e54: DecompressPointer r1
    //     0xba6e54: add             x1, x1, HEAP, lsl #32
    // 0xba6e58: LoadField: r0 = r1->field_2b
    //     0xba6e58: ldur            w0, [x1, #0x2b]
    // 0xba6e5c: DecompressPointer r0
    //     0xba6e5c: add             x0, x0, HEAP, lsl #32
    // 0xba6e60: stur            x0, [fp, #-0x10]
    // 0xba6e64: r1 = Instance_Color
    //     0xba6e64: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba6e68: d0 = 0.700000
    //     0xba6e68: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba6e6c: ldr             d0, [x17, #0xf48]
    // 0xba6e70: r0 = withOpacity()
    //     0xba6e70: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba6e74: r16 = 12.000000
    //     0xba6e74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba6e78: ldr             x16, [x16, #0x9e8]
    // 0xba6e7c: stp             x16, x0, [SP]
    // 0xba6e80: ldur            x1, [fp, #-0x10]
    // 0xba6e84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba6e84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba6e88: ldr             x4, [x4, #0x9b8]
    // 0xba6e8c: r0 = copyWith()
    //     0xba6e8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba6e90: stur            x0, [fp, #-0x10]
    // 0xba6e94: r0 = Text()
    //     0xba6e94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba6e98: mov             x1, x0
    // 0xba6e9c: ldur            x0, [fp, #-8]
    // 0xba6ea0: stur            x1, [fp, #-0x18]
    // 0xba6ea4: StoreField: r1->field_b = r0
    //     0xba6ea4: stur            w0, [x1, #0xb]
    // 0xba6ea8: ldur            x0, [fp, #-0x10]
    // 0xba6eac: StoreField: r1->field_13 = r0
    //     0xba6eac: stur            w0, [x1, #0x13]
    // 0xba6eb0: r0 = Padding()
    //     0xba6eb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba6eb4: mov             x2, x0
    // 0xba6eb8: r0 = Instance_EdgeInsets
    //     0xba6eb8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xba6ebc: ldr             x0, [x0, #0xf70]
    // 0xba6ec0: stur            x2, [fp, #-8]
    // 0xba6ec4: StoreField: r2->field_f = r0
    //     0xba6ec4: stur            w0, [x2, #0xf]
    // 0xba6ec8: ldur            x0, [fp, #-0x18]
    // 0xba6ecc: StoreField: r2->field_b = r0
    //     0xba6ecc: stur            w0, [x2, #0xb]
    // 0xba6ed0: r1 = <FlexParentData>
    //     0xba6ed0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba6ed4: ldr             x1, [x1, #0xe00]
    // 0xba6ed8: r0 = Expanded()
    //     0xba6ed8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba6edc: stur            x0, [fp, #-0x10]
    // 0xba6ee0: StoreField: r0->field_13 = rZR
    //     0xba6ee0: stur            xzr, [x0, #0x13]
    // 0xba6ee4: r1 = Instance_FlexFit
    //     0xba6ee4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba6ee8: ldr             x1, [x1, #0xe08]
    // 0xba6eec: StoreField: r0->field_1b = r1
    //     0xba6eec: stur            w1, [x0, #0x1b]
    // 0xba6ef0: ldur            x1, [fp, #-8]
    // 0xba6ef4: StoreField: r0->field_b = r1
    //     0xba6ef4: stur            w1, [x0, #0xb]
    // 0xba6ef8: r1 = Null
    //     0xba6ef8: mov             x1, NULL
    // 0xba6efc: r2 = 2
    //     0xba6efc: movz            x2, #0x2
    // 0xba6f00: r0 = AllocateArray()
    //     0xba6f00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba6f04: mov             x2, x0
    // 0xba6f08: ldur            x0, [fp, #-0x10]
    // 0xba6f0c: stur            x2, [fp, #-8]
    // 0xba6f10: StoreField: r2->field_f = r0
    //     0xba6f10: stur            w0, [x2, #0xf]
    // 0xba6f14: r1 = <Widget>
    //     0xba6f14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba6f18: r0 = AllocateGrowableArray()
    //     0xba6f18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba6f1c: mov             x1, x0
    // 0xba6f20: ldur            x0, [fp, #-8]
    // 0xba6f24: stur            x1, [fp, #-0x10]
    // 0xba6f28: StoreField: r1->field_f = r0
    //     0xba6f28: stur            w0, [x1, #0xf]
    // 0xba6f2c: r0 = 2
    //     0xba6f2c: movz            x0, #0x2
    // 0xba6f30: StoreField: r1->field_b = r0
    //     0xba6f30: stur            w0, [x1, #0xb]
    // 0xba6f34: r0 = Row()
    //     0xba6f34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba6f38: r1 = Instance_Axis
    //     0xba6f38: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba6f3c: StoreField: r0->field_f = r1
    //     0xba6f3c: stur            w1, [x0, #0xf]
    // 0xba6f40: r1 = Instance_MainAxisAlignment
    //     0xba6f40: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba6f44: ldr             x1, [x1, #0xa08]
    // 0xba6f48: StoreField: r0->field_13 = r1
    //     0xba6f48: stur            w1, [x0, #0x13]
    // 0xba6f4c: r1 = Instance_MainAxisSize
    //     0xba6f4c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba6f50: ldr             x1, [x1, #0xa10]
    // 0xba6f54: ArrayStore: r0[0] = r1  ; List_4
    //     0xba6f54: stur            w1, [x0, #0x17]
    // 0xba6f58: r1 = Instance_CrossAxisAlignment
    //     0xba6f58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba6f5c: ldr             x1, [x1, #0xa18]
    // 0xba6f60: StoreField: r0->field_1b = r1
    //     0xba6f60: stur            w1, [x0, #0x1b]
    // 0xba6f64: r1 = Instance_VerticalDirection
    //     0xba6f64: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba6f68: ldr             x1, [x1, #0xa20]
    // 0xba6f6c: StoreField: r0->field_23 = r1
    //     0xba6f6c: stur            w1, [x0, #0x23]
    // 0xba6f70: r1 = Instance_Clip
    //     0xba6f70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba6f74: ldr             x1, [x1, #0x38]
    // 0xba6f78: StoreField: r0->field_2b = r1
    //     0xba6f78: stur            w1, [x0, #0x2b]
    // 0xba6f7c: StoreField: r0->field_2f = rZR
    //     0xba6f7c: stur            xzr, [x0, #0x2f]
    // 0xba6f80: ldur            x1, [fp, #-0x10]
    // 0xba6f84: StoreField: r0->field_b = r1
    //     0xba6f84: stur            w1, [x0, #0xb]
    // 0xba6f88: LeaveFrame
    //     0xba6f88: mov             SP, fp
    //     0xba6f8c: ldp             fp, lr, [SP], #0x10
    // 0xba6f90: ret
    //     0xba6f90: ret             
    // 0xba6f94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba6f94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba6f98: b               #0xba6db4
    // 0xba6f9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba6f9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba6fa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba6fa0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4031, size: 0x24, field offset: 0xc
//   const constructor, 
class OffersBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ffe8, size: 0x24
    // 0xc7ffe8: EnterFrame
    //     0xc7ffe8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ffec: mov             fp, SP
    // 0xc7fff0: mov             x0, x1
    // 0xc7fff4: r1 = <OffersBottomSheet>
    //     0xc7fff4: add             x1, PP, #0x6e, lsl #12  ; [pp+0x6ec88] TypeArguments: <OffersBottomSheet>
    //     0xc7fff8: ldr             x1, [x1, #0xc88]
    // 0xc7fffc: r0 = _OffersBottomSheetState()
    //     0xc7fffc: bl              #0xc8000c  ; Allocate_OffersBottomSheetStateStub -> _OffersBottomSheetState (size=0x14)
    // 0xc80000: LeaveFrame
    //     0xc80000: mov             SP, fp
    //     0xc80004: ldp             fp, lr, [SP], #0x10
    // 0xc80008: ret
    //     0xc80008: ret             
  }
}
