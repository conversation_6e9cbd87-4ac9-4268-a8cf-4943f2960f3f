// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart

// class id: 1049438, size: 0x8
class :: {
}

// class id: 3312, size: 0x2c, field offset: 0x14
class _ProductSelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x944094, size: 0x51c
    // 0x944094: EnterFrame
    //     0x944094: stp             fp, lr, [SP, #-0x10]!
    //     0x944098: mov             fp, SP
    // 0x94409c: AllocStack(0x38)
    //     0x94409c: sub             SP, SP, #0x38
    // 0x9440a0: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x9440a0: stur            x1, [fp, #-8]
    // 0x9440a4: CheckStackOverflow
    //     0x9440a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9440a8: cmp             SP, x16
    //     0x9440ac: b.ls            #0x944584
    // 0x9440b0: r1 = 1
    //     0x9440b0: movz            x1, #0x1
    // 0x9440b4: r0 = AllocateContext()
    //     0x9440b4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9440b8: mov             x1, x0
    // 0x9440bc: ldur            x0, [fp, #-8]
    // 0x9440c0: stur            x1, [fp, #-0x10]
    // 0x9440c4: StoreField: r1->field_f = r0
    //     0x9440c4: stur            w0, [x1, #0xf]
    // 0x9440c8: LoadField: r2 = r0->field_b
    //     0x9440c8: ldur            w2, [x0, #0xb]
    // 0x9440cc: DecompressPointer r2
    //     0x9440cc: add             x2, x2, HEAP, lsl #32
    // 0x9440d0: cmp             w2, NULL
    // 0x9440d4: b.eq            #0x94458c
    // 0x9440d8: LoadField: r3 = r2->field_b
    //     0x9440d8: ldur            w3, [x2, #0xb]
    // 0x9440dc: DecompressPointer r3
    //     0x9440dc: add             x3, x3, HEAP, lsl #32
    // 0x9440e0: LoadField: r2 = r3->field_db
    //     0x9440e0: ldur            w2, [x3, #0xdb]
    // 0x9440e4: DecompressPointer r2
    //     0x9440e4: add             x2, x2, HEAP, lsl #32
    // 0x9440e8: cmp             w2, NULL
    // 0x9440ec: b.ne            #0x9440f8
    // 0x9440f0: r4 = Null
    //     0x9440f0: mov             x4, NULL
    // 0x9440f4: b               #0x944110
    // 0x9440f8: LoadField: r4 = r2->field_b
    //     0x9440f8: ldur            w4, [x2, #0xb]
    // 0x9440fc: cbnz            w4, #0x944108
    // 0x944100: r5 = false
    //     0x944100: add             x5, NULL, #0x30  ; false
    // 0x944104: b               #0x94410c
    // 0x944108: r5 = true
    //     0x944108: add             x5, NULL, #0x20  ; true
    // 0x94410c: mov             x4, x5
    // 0x944110: cmp             w4, NULL
    // 0x944114: b.ne            #0x944120
    // 0x944118: mov             x1, x0
    // 0x94411c: b               #0x9443e0
    // 0x944120: tbnz            w4, #4, #0x9443dc
    // 0x944124: cmp             w2, NULL
    // 0x944128: b.ne            #0x944134
    // 0x94412c: r2 = Null
    //     0x94412c: mov             x2, NULL
    // 0x944130: b               #0x94418c
    // 0x944134: r16 = <AllGroupedSkusDatum?>
    //     0x944134: add             x16, PP, #0x52, lsl #12  ; [pp+0x52850] TypeArguments: <AllGroupedSkusDatum?>
    //     0x944138: ldr             x16, [x16, #0x850]
    // 0x94413c: stp             x2, x16, [SP]
    // 0x944140: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x944140: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x944144: r0 = cast()
    //     0x944144: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x944148: ldur            x2, [fp, #-0x10]
    // 0x94414c: r1 = Function '<anonymous closure>':.
    //     0x94414c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55650] AnonymousClosure: (0x934af0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x944150: ldr             x1, [x1, #0x650]
    // 0x944154: stur            x0, [fp, #-0x18]
    // 0x944158: r0 = AllocateClosure()
    //     0x944158: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94415c: r1 = Function '<anonymous closure>':.
    //     0x94415c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55658] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x944160: ldr             x1, [x1, #0x658]
    // 0x944164: r2 = Null
    //     0x944164: mov             x2, NULL
    // 0x944168: stur            x0, [fp, #-0x20]
    // 0x94416c: r0 = AllocateClosure()
    //     0x94416c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944170: str             x0, [SP]
    // 0x944174: ldur            x1, [fp, #-0x18]
    // 0x944178: ldur            x2, [fp, #-0x20]
    // 0x94417c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x94417c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x944180: ldr             x4, [x4, #0xb48]
    // 0x944184: r0 = firstWhere()
    //     0x944184: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x944188: mov             x2, x0
    // 0x94418c: cmp             w2, NULL
    // 0x944190: b.eq            #0x944218
    // 0x944194: ldur            x0, [fp, #-8]
    // 0x944198: LoadField: r1 = r0->field_b
    //     0x944198: ldur            w1, [x0, #0xb]
    // 0x94419c: DecompressPointer r1
    //     0x94419c: add             x1, x1, HEAP, lsl #32
    // 0x9441a0: cmp             w1, NULL
    // 0x9441a4: b.eq            #0x944590
    // 0x9441a8: LoadField: r3 = r1->field_b
    //     0x9441a8: ldur            w3, [x1, #0xb]
    // 0x9441ac: DecompressPointer r3
    //     0x9441ac: add             x3, x3, HEAP, lsl #32
    // 0x9441b0: LoadField: r1 = r3->field_db
    //     0x9441b0: ldur            w1, [x3, #0xdb]
    // 0x9441b4: DecompressPointer r1
    //     0x9441b4: add             x1, x1, HEAP, lsl #32
    // 0x9441b8: cmp             w1, NULL
    // 0x9441bc: b.ne            #0x9441c8
    // 0x9441c0: r0 = Null
    //     0x9441c0: mov             x0, NULL
    // 0x9441c4: b               #0x9441e8
    // 0x9441c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9441c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9441cc: r0 = indexOf()
    //     0x9441cc: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x9441d0: mov             x2, x0
    // 0x9441d4: r0 = BoxInt64Instr(r2)
    //     0x9441d4: sbfiz           x0, x2, #1, #0x1f
    //     0x9441d8: cmp             x2, x0, asr #1
    //     0x9441dc: b.eq            #0x9441e8
    //     0x9441e0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9441e4: stur            x2, [x0, #7]
    // 0x9441e8: cmp             w0, NULL
    // 0x9441ec: b.ne            #0x9441f8
    // 0x9441f0: r0 = 0
    //     0x9441f0: movz            x0, #0
    // 0x9441f4: b               #0x944208
    // 0x9441f8: r1 = LoadInt32Instr(r0)
    //     0x9441f8: sbfx            x1, x0, #1, #0x1f
    //     0x9441fc: tbz             w0, #0, #0x944204
    //     0x944200: ldur            x1, [x0, #7]
    // 0x944204: mov             x0, x1
    // 0x944208: ldur            x2, [fp, #-8]
    // 0x94420c: ArrayStore: r2[0] = r0  ; List_8
    //     0x94420c: stur            x0, [x2, #0x17]
    // 0x944210: mov             x3, x0
    // 0x944214: b               #0x944224
    // 0x944218: ldur            x2, [fp, #-8]
    // 0x94421c: ArrayStore: r2[0] = rZR  ; List_8
    //     0x94421c: stur            xzr, [x2, #0x17]
    // 0x944220: r3 = 0
    //     0x944220: movz            x3, #0
    // 0x944224: stur            x3, [fp, #-0x28]
    // 0x944228: LoadField: r0 = r2->field_b
    //     0x944228: ldur            w0, [x2, #0xb]
    // 0x94422c: DecompressPointer r0
    //     0x94422c: add             x0, x0, HEAP, lsl #32
    // 0x944230: cmp             w0, NULL
    // 0x944234: b.eq            #0x944594
    // 0x944238: LoadField: r1 = r0->field_b
    //     0x944238: ldur            w1, [x0, #0xb]
    // 0x94423c: DecompressPointer r1
    //     0x94423c: add             x1, x1, HEAP, lsl #32
    // 0x944240: LoadField: r4 = r1->field_db
    //     0x944240: ldur            w4, [x1, #0xdb]
    // 0x944244: DecompressPointer r4
    //     0x944244: add             x4, x4, HEAP, lsl #32
    // 0x944248: stur            x4, [fp, #-0x18]
    // 0x94424c: cmp             w4, NULL
    // 0x944250: b.ne            #0x94425c
    // 0x944254: r0 = Null
    //     0x944254: mov             x0, NULL
    // 0x944258: b               #0x9442d0
    // 0x94425c: LoadField: r0 = r4->field_b
    //     0x94425c: ldur            w0, [x4, #0xb]
    // 0x944260: r1 = LoadInt32Instr(r0)
    //     0x944260: sbfx            x1, x0, #1, #0x1f
    // 0x944264: mov             x0, x1
    // 0x944268: mov             x1, x3
    // 0x94426c: cmp             x1, x0
    // 0x944270: b.hs            #0x944598
    // 0x944274: LoadField: r0 = r4->field_f
    //     0x944274: ldur            w0, [x4, #0xf]
    // 0x944278: DecompressPointer r0
    //     0x944278: add             x0, x0, HEAP, lsl #32
    // 0x94427c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94427c: add             x16, x0, x3, lsl #2
    //     0x944280: ldur            w1, [x16, #0xf]
    // 0x944284: DecompressPointer r1
    //     0x944284: add             x1, x1, HEAP, lsl #32
    // 0x944288: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x944288: ldur            w5, [x1, #0x17]
    // 0x94428c: DecompressPointer r5
    //     0x94428c: add             x5, x5, HEAP, lsl #32
    // 0x944290: cmp             w5, NULL
    // 0x944294: b.ne            #0x9442a0
    // 0x944298: r0 = Null
    //     0x944298: mov             x0, NULL
    // 0x94429c: b               #0x9442d0
    // 0x9442a0: LoadField: r0 = r5->field_b
    //     0x9442a0: ldur            w0, [x5, #0xb]
    // 0x9442a4: r1 = LoadInt32Instr(r0)
    //     0x9442a4: sbfx            x1, x0, #1, #0x1f
    // 0x9442a8: mov             x0, x1
    // 0x9442ac: mov             x1, x3
    // 0x9442b0: cmp             x1, x0
    // 0x9442b4: b.hs            #0x94459c
    // 0x9442b8: LoadField: r0 = r5->field_f
    //     0x9442b8: ldur            w0, [x5, #0xf]
    // 0x9442bc: DecompressPointer r0
    //     0x9442bc: add             x0, x0, HEAP, lsl #32
    // 0x9442c0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x9442c0: add             x16, x0, x3, lsl #2
    //     0x9442c4: ldur            w1, [x16, #0xf]
    // 0x9442c8: DecompressPointer r1
    //     0x9442c8: add             x1, x1, HEAP, lsl #32
    // 0x9442cc: mov             x0, x1
    // 0x9442d0: cmp             w0, NULL
    // 0x9442d4: b.ne            #0x9442dc
    // 0x9442d8: r0 = AllSkuDatum()
    //     0x9442d8: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x9442dc: ldur            x1, [fp, #-8]
    // 0x9442e0: ldur            x2, [fp, #-0x18]
    // 0x9442e4: StoreField: r1->field_13 = r0
    //     0x9442e4: stur            w0, [x1, #0x13]
    //     0x9442e8: ldurb           w16, [x1, #-1]
    //     0x9442ec: ldurb           w17, [x0, #-1]
    //     0x9442f0: and             x16, x17, x16, lsr #2
    //     0x9442f4: tst             x16, HEAP, lsr #32
    //     0x9442f8: b.eq            #0x944300
    //     0x9442fc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x944300: LoadField: r3 = r1->field_27
    //     0x944300: ldur            w3, [x1, #0x27]
    // 0x944304: DecompressPointer r3
    //     0x944304: add             x3, x3, HEAP, lsl #32
    // 0x944308: stur            x3, [fp, #-0x20]
    // 0x94430c: cmp             w2, NULL
    // 0x944310: b.ne            #0x94431c
    // 0x944314: r0 = Null
    //     0x944314: mov             x0, NULL
    // 0x944318: b               #0x944354
    // 0x94431c: ldur            x4, [fp, #-0x28]
    // 0x944320: LoadField: r0 = r2->field_b
    //     0x944320: ldur            w0, [x2, #0xb]
    // 0x944324: r1 = LoadInt32Instr(r0)
    //     0x944324: sbfx            x1, x0, #1, #0x1f
    // 0x944328: mov             x0, x1
    // 0x94432c: mov             x1, x4
    // 0x944330: cmp             x1, x0
    // 0x944334: b.hs            #0x9445a0
    // 0x944338: LoadField: r0 = r2->field_f
    //     0x944338: ldur            w0, [x2, #0xf]
    // 0x94433c: DecompressPointer r0
    //     0x94433c: add             x0, x0, HEAP, lsl #32
    // 0x944340: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x944340: add             x16, x0, x4, lsl #2
    //     0x944344: ldur            w1, [x16, #0xf]
    // 0x944348: DecompressPointer r1
    //     0x944348: add             x1, x1, HEAP, lsl #32
    // 0x94434c: LoadField: r0 = r1->field_7
    //     0x94434c: ldur            w0, [x1, #7]
    // 0x944350: DecompressPointer r0
    //     0x944350: add             x0, x0, HEAP, lsl #32
    // 0x944354: cmp             w0, NULL
    // 0x944358: b.ne            #0x944360
    // 0x94435c: r0 = ""
    //     0x94435c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x944360: stur            x0, [fp, #-0x18]
    // 0x944364: LoadField: r1 = r3->field_b
    //     0x944364: ldur            w1, [x3, #0xb]
    // 0x944368: LoadField: r2 = r3->field_f
    //     0x944368: ldur            w2, [x3, #0xf]
    // 0x94436c: DecompressPointer r2
    //     0x94436c: add             x2, x2, HEAP, lsl #32
    // 0x944370: LoadField: r4 = r2->field_b
    //     0x944370: ldur            w4, [x2, #0xb]
    // 0x944374: r2 = LoadInt32Instr(r1)
    //     0x944374: sbfx            x2, x1, #1, #0x1f
    // 0x944378: stur            x2, [fp, #-0x28]
    // 0x94437c: r1 = LoadInt32Instr(r4)
    //     0x94437c: sbfx            x1, x4, #1, #0x1f
    // 0x944380: cmp             x2, x1
    // 0x944384: b.ne            #0x944390
    // 0x944388: mov             x1, x3
    // 0x94438c: r0 = _growToNextCapacity()
    //     0x94438c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x944390: ldur            x0, [fp, #-0x20]
    // 0x944394: ldur            x2, [fp, #-0x28]
    // 0x944398: add             x1, x2, #1
    // 0x94439c: lsl             x3, x1, #1
    // 0x9443a0: StoreField: r0->field_b = r3
    //     0x9443a0: stur            w3, [x0, #0xb]
    // 0x9443a4: LoadField: r1 = r0->field_f
    //     0x9443a4: ldur            w1, [x0, #0xf]
    // 0x9443a8: DecompressPointer r1
    //     0x9443a8: add             x1, x1, HEAP, lsl #32
    // 0x9443ac: ldur            x0, [fp, #-0x18]
    // 0x9443b0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9443b0: add             x25, x1, x2, lsl #2
    //     0x9443b4: add             x25, x25, #0xf
    //     0x9443b8: str             w0, [x25]
    //     0x9443bc: tbz             w0, #0, #0x9443d8
    //     0x9443c0: ldurb           w16, [x1, #-1]
    //     0x9443c4: ldurb           w17, [x0, #-1]
    //     0x9443c8: and             x16, x17, x16, lsr #2
    //     0x9443cc: tst             x16, HEAP, lsr #32
    //     0x9443d0: b.eq            #0x9443d8
    //     0x9443d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9443d8: b               #0x944574
    // 0x9443dc: mov             x1, x0
    // 0x9443e0: LoadField: r0 = r3->field_d7
    //     0x9443e0: ldur            w0, [x3, #0xd7]
    // 0x9443e4: DecompressPointer r0
    //     0x9443e4: add             x0, x0, HEAP, lsl #32
    // 0x9443e8: cmp             w0, NULL
    // 0x9443ec: b.ne            #0x9443f8
    // 0x9443f0: r2 = Null
    //     0x9443f0: mov             x2, NULL
    // 0x9443f4: b               #0x944450
    // 0x9443f8: r16 = <AllSkuDatum?>
    //     0x9443f8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x9443fc: ldr             x16, [x16, #0x868]
    // 0x944400: stp             x0, x16, [SP]
    // 0x944404: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x944404: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x944408: r0 = cast()
    //     0x944408: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x94440c: ldur            x2, [fp, #-0x10]
    // 0x944410: r1 = Function '<anonymous closure>':.
    //     0x944410: add             x1, PP, #0x55, lsl #12  ; [pp+0x55660] AnonymousClosure: (0x934a14), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x944414: ldr             x1, [x1, #0x660]
    // 0x944418: stur            x0, [fp, #-0x10]
    // 0x94441c: r0 = AllocateClosure()
    //     0x94441c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944420: r1 = Function '<anonymous closure>':.
    //     0x944420: add             x1, PP, #0x55, lsl #12  ; [pp+0x55668] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x944424: ldr             x1, [x1, #0x668]
    // 0x944428: r2 = Null
    //     0x944428: mov             x2, NULL
    // 0x94442c: stur            x0, [fp, #-0x18]
    // 0x944430: r0 = AllocateClosure()
    //     0x944430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944434: str             x0, [SP]
    // 0x944438: ldur            x1, [fp, #-0x10]
    // 0x94443c: ldur            x2, [fp, #-0x18]
    // 0x944440: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x944440: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x944444: ldr             x4, [x4, #0xb48]
    // 0x944448: r0 = firstWhere()
    //     0x944448: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x94444c: mov             x2, x0
    // 0x944450: cmp             w2, NULL
    // 0x944454: b.eq            #0x9444dc
    // 0x944458: ldur            x0, [fp, #-8]
    // 0x94445c: LoadField: r1 = r0->field_b
    //     0x94445c: ldur            w1, [x0, #0xb]
    // 0x944460: DecompressPointer r1
    //     0x944460: add             x1, x1, HEAP, lsl #32
    // 0x944464: cmp             w1, NULL
    // 0x944468: b.eq            #0x9445a4
    // 0x94446c: LoadField: r3 = r1->field_b
    //     0x94446c: ldur            w3, [x1, #0xb]
    // 0x944470: DecompressPointer r3
    //     0x944470: add             x3, x3, HEAP, lsl #32
    // 0x944474: LoadField: r1 = r3->field_d7
    //     0x944474: ldur            w1, [x3, #0xd7]
    // 0x944478: DecompressPointer r1
    //     0x944478: add             x1, x1, HEAP, lsl #32
    // 0x94447c: cmp             w1, NULL
    // 0x944480: b.ne            #0x94448c
    // 0x944484: r0 = Null
    //     0x944484: mov             x0, NULL
    // 0x944488: b               #0x9444ac
    // 0x94448c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x94448c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x944490: r0 = indexOf()
    //     0x944490: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x944494: mov             x2, x0
    // 0x944498: r0 = BoxInt64Instr(r2)
    //     0x944498: sbfiz           x0, x2, #1, #0x1f
    //     0x94449c: cmp             x2, x0, asr #1
    //     0x9444a0: b.eq            #0x9444ac
    //     0x9444a4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9444a8: stur            x2, [x0, #7]
    // 0x9444ac: cmp             w0, NULL
    // 0x9444b0: b.ne            #0x9444bc
    // 0x9444b4: r0 = 0
    //     0x9444b4: movz            x0, #0
    // 0x9444b8: b               #0x9444cc
    // 0x9444bc: r1 = LoadInt32Instr(r0)
    //     0x9444bc: sbfx            x1, x0, #1, #0x1f
    //     0x9444c0: tbz             w0, #0, #0x9444c8
    //     0x9444c4: ldur            x1, [x0, #7]
    // 0x9444c8: mov             x0, x1
    // 0x9444cc: ldur            x2, [fp, #-8]
    // 0x9444d0: ArrayStore: r2[0] = r0  ; List_8
    //     0x9444d0: stur            x0, [x2, #0x17]
    // 0x9444d4: mov             x3, x0
    // 0x9444d8: b               #0x9444e8
    // 0x9444dc: ldur            x2, [fp, #-8]
    // 0x9444e0: ArrayStore: r2[0] = rZR  ; List_8
    //     0x9444e0: stur            xzr, [x2, #0x17]
    // 0x9444e4: r3 = 0
    //     0x9444e4: movz            x3, #0
    // 0x9444e8: LoadField: r0 = r2->field_b
    //     0x9444e8: ldur            w0, [x2, #0xb]
    // 0x9444ec: DecompressPointer r0
    //     0x9444ec: add             x0, x0, HEAP, lsl #32
    // 0x9444f0: cmp             w0, NULL
    // 0x9444f4: b.eq            #0x9445a8
    // 0x9444f8: LoadField: r1 = r0->field_b
    //     0x9444f8: ldur            w1, [x0, #0xb]
    // 0x9444fc: DecompressPointer r1
    //     0x9444fc: add             x1, x1, HEAP, lsl #32
    // 0x944500: LoadField: r4 = r1->field_d7
    //     0x944500: ldur            w4, [x1, #0xd7]
    // 0x944504: DecompressPointer r4
    //     0x944504: add             x4, x4, HEAP, lsl #32
    // 0x944508: cmp             w4, NULL
    // 0x94450c: b.ne            #0x944518
    // 0x944510: r0 = Null
    //     0x944510: mov             x0, NULL
    // 0x944514: b               #0x944548
    // 0x944518: LoadField: r0 = r4->field_b
    //     0x944518: ldur            w0, [x4, #0xb]
    // 0x94451c: r1 = LoadInt32Instr(r0)
    //     0x94451c: sbfx            x1, x0, #1, #0x1f
    // 0x944520: mov             x0, x1
    // 0x944524: mov             x1, x3
    // 0x944528: cmp             x1, x0
    // 0x94452c: b.hs            #0x9445ac
    // 0x944530: LoadField: r0 = r4->field_f
    //     0x944530: ldur            w0, [x4, #0xf]
    // 0x944534: DecompressPointer r0
    //     0x944534: add             x0, x0, HEAP, lsl #32
    // 0x944538: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x944538: add             x16, x0, x3, lsl #2
    //     0x94453c: ldur            w1, [x16, #0xf]
    // 0x944540: DecompressPointer r1
    //     0x944540: add             x1, x1, HEAP, lsl #32
    // 0x944544: mov             x0, x1
    // 0x944548: cmp             w0, NULL
    // 0x94454c: b.ne            #0x944554
    // 0x944550: r0 = AllSkuDatum()
    //     0x944550: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x944554: ldur            x1, [fp, #-8]
    // 0x944558: StoreField: r1->field_13 = r0
    //     0x944558: stur            w0, [x1, #0x13]
    //     0x94455c: ldurb           w16, [x1, #-1]
    //     0x944560: ldurb           w17, [x0, #-1]
    //     0x944564: and             x16, x17, x16, lsr #2
    //     0x944568: tst             x16, HEAP, lsr #32
    //     0x94456c: b.eq            #0x944574
    //     0x944570: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x944574: r0 = Null
    //     0x944574: mov             x0, NULL
    // 0x944578: LeaveFrame
    //     0x944578: mov             SP, fp
    //     0x94457c: ldp             fp, lr, [SP], #0x10
    // 0x944580: ret
    //     0x944580: ret             
    // 0x944584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944584: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944588: b               #0x9440b0
    // 0x94458c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94458c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944590: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944598: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x944598: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94459c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94459c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9445a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9445a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9445a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9445a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9445a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9445a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9445ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9445ac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa60844, size: 0x90
    // 0xa60844: EnterFrame
    //     0xa60844: stp             fp, lr, [SP, #-0x10]!
    //     0xa60848: mov             fp, SP
    // 0xa6084c: AllocStack(0x10)
    //     0xa6084c: sub             SP, SP, #0x10
    // 0xa60850: SetupParameters()
    //     0xa60850: ldr             x0, [fp, #0x10]
    //     0xa60854: ldur            w1, [x0, #0x17]
    //     0xa60858: add             x1, x1, HEAP, lsl #32
    //     0xa6085c: stur            x1, [fp, #-0x10]
    // 0xa60860: LoadField: r0 = r1->field_b
    //     0xa60860: ldur            w0, [x1, #0xb]
    // 0xa60864: DecompressPointer r0
    //     0xa60864: add             x0, x0, HEAP, lsl #32
    // 0xa60868: LoadField: r2 = r0->field_f
    //     0xa60868: ldur            w2, [x0, #0xf]
    // 0xa6086c: DecompressPointer r2
    //     0xa6086c: add             x2, x2, HEAP, lsl #32
    // 0xa60870: stur            x2, [fp, #-8]
    // 0xa60874: LoadField: r0 = r1->field_13
    //     0xa60874: ldur            w0, [x1, #0x13]
    // 0xa60878: DecompressPointer r0
    //     0xa60878: add             x0, x0, HEAP, lsl #32
    // 0xa6087c: cmp             w0, NULL
    // 0xa60880: b.ne            #0xa60888
    // 0xa60884: r0 = AllSkuDatum()
    //     0xa60884: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0xa60888: ldur            x1, [fp, #-0x10]
    // 0xa6088c: ldur            x2, [fp, #-8]
    // 0xa60890: StoreField: r2->field_13 = r0
    //     0xa60890: stur            w0, [x2, #0x13]
    //     0xa60894: ldurb           w16, [x2, #-1]
    //     0xa60898: ldurb           w17, [x0, #-1]
    //     0xa6089c: and             x16, x17, x16, lsr #2
    //     0xa608a0: tst             x16, HEAP, lsr #32
    //     0xa608a4: b.eq            #0xa608ac
    //     0xa608a8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa608ac: LoadField: r3 = r1->field_f
    //     0xa608ac: ldur            w3, [x1, #0xf]
    // 0xa608b0: DecompressPointer r3
    //     0xa608b0: add             x3, x3, HEAP, lsl #32
    // 0xa608b4: r1 = LoadInt32Instr(r3)
    //     0xa608b4: sbfx            x1, x3, #1, #0x1f
    //     0xa608b8: tbz             w3, #0, #0xa608c0
    //     0xa608bc: ldur            x1, [x3, #7]
    // 0xa608c0: ArrayStore: r2[0] = r1  ; List_8
    //     0xa608c0: stur            x1, [x2, #0x17]
    // 0xa608c4: r0 = Null
    //     0xa608c4: mov             x0, NULL
    // 0xa608c8: LeaveFrame
    //     0xa608c8: mov             SP, fp
    //     0xa608cc: ldp             fp, lr, [SP], #0x10
    // 0xa608d0: ret
    //     0xa608d0: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa608d4, size: 0x68
    // 0xa608d4: EnterFrame
    //     0xa608d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa608d8: mov             fp, SP
    // 0xa608dc: AllocStack(0x8)
    //     0xa608dc: sub             SP, SP, #8
    // 0xa608e0: SetupParameters()
    //     0xa608e0: ldr             x0, [fp, #0x10]
    //     0xa608e4: ldur            w2, [x0, #0x17]
    //     0xa608e8: add             x2, x2, HEAP, lsl #32
    // 0xa608ec: CheckStackOverflow
    //     0xa608ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa608f0: cmp             SP, x16
    //     0xa608f4: b.ls            #0xa60934
    // 0xa608f8: LoadField: r0 = r2->field_b
    //     0xa608f8: ldur            w0, [x2, #0xb]
    // 0xa608fc: DecompressPointer r0
    //     0xa608fc: add             x0, x0, HEAP, lsl #32
    // 0xa60900: LoadField: r3 = r0->field_f
    //     0xa60900: ldur            w3, [x0, #0xf]
    // 0xa60904: DecompressPointer r3
    //     0xa60904: add             x3, x3, HEAP, lsl #32
    // 0xa60908: stur            x3, [fp, #-8]
    // 0xa6090c: r1 = Function '<anonymous closure>':.
    //     0xa6090c: add             x1, PP, #0x55, lsl #12  ; [pp+0x555d8] AnonymousClosure: (0xa60844), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xa60910: ldr             x1, [x1, #0x5d8]
    // 0xa60914: r0 = AllocateClosure()
    //     0xa60914: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa60918: ldur            x1, [fp, #-8]
    // 0xa6091c: mov             x2, x0
    // 0xa60920: r0 = setState()
    //     0xa60920: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa60924: r0 = Null
    //     0xa60924: mov             x0, NULL
    // 0xa60928: LeaveFrame
    //     0xa60928: mov             SP, fp
    //     0xa6092c: ldp             fp, lr, [SP], #0x10
    // 0xa60930: ret
    //     0xa60930: ret             
    // 0xa60934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60934: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60938: b               #0xa608f8
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa6093c, size: 0x2d4
    // 0xa6093c: EnterFrame
    //     0xa6093c: stp             fp, lr, [SP, #-0x10]!
    //     0xa60940: mov             fp, SP
    // 0xa60944: AllocStack(0x40)
    //     0xa60944: sub             SP, SP, #0x40
    // 0xa60948: SetupParameters()
    //     0xa60948: ldr             x0, [fp, #0x20]
    //     0xa6094c: ldur            w1, [x0, #0x17]
    //     0xa60950: add             x1, x1, HEAP, lsl #32
    //     0xa60954: stur            x1, [fp, #-8]
    // 0xa60958: CheckStackOverflow
    //     0xa60958: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6095c: cmp             SP, x16
    //     0xa60960: b.ls            #0xa60be4
    // 0xa60964: r1 = 2
    //     0xa60964: movz            x1, #0x2
    // 0xa60968: r0 = AllocateContext()
    //     0xa60968: bl              #0x16f6108  ; AllocateContextStub
    // 0xa6096c: mov             x3, x0
    // 0xa60970: ldur            x2, [fp, #-8]
    // 0xa60974: stur            x3, [fp, #-0x18]
    // 0xa60978: StoreField: r3->field_b = r2
    //     0xa60978: stur            w2, [x3, #0xb]
    // 0xa6097c: ldr             x4, [fp, #0x10]
    // 0xa60980: StoreField: r3->field_f = r4
    //     0xa60980: stur            w4, [x3, #0xf]
    // 0xa60984: LoadField: r5 = r2->field_f
    //     0xa60984: ldur            w5, [x2, #0xf]
    // 0xa60988: DecompressPointer r5
    //     0xa60988: add             x5, x5, HEAP, lsl #32
    // 0xa6098c: LoadField: r0 = r5->field_b
    //     0xa6098c: ldur            w0, [x5, #0xb]
    // 0xa60990: DecompressPointer r0
    //     0xa60990: add             x0, x0, HEAP, lsl #32
    // 0xa60994: cmp             w0, NULL
    // 0xa60998: b.eq            #0xa60bec
    // 0xa6099c: LoadField: r1 = r0->field_b
    //     0xa6099c: ldur            w1, [x0, #0xb]
    // 0xa609a0: DecompressPointer r1
    //     0xa609a0: add             x1, x1, HEAP, lsl #32
    // 0xa609a4: LoadField: r6 = r1->field_db
    //     0xa609a4: ldur            w6, [x1, #0xdb]
    // 0xa609a8: DecompressPointer r6
    //     0xa609a8: add             x6, x6, HEAP, lsl #32
    // 0xa609ac: cmp             w6, NULL
    // 0xa609b0: b.ne            #0xa609bc
    // 0xa609b4: r0 = Null
    //     0xa609b4: mov             x0, NULL
    // 0xa609b8: b               #0xa60a40
    // 0xa609bc: LoadField: r7 = r5->field_1f
    //     0xa609bc: ldur            x7, [x5, #0x1f]
    // 0xa609c0: LoadField: r0 = r6->field_b
    //     0xa609c0: ldur            w0, [x6, #0xb]
    // 0xa609c4: r1 = LoadInt32Instr(r0)
    //     0xa609c4: sbfx            x1, x0, #1, #0x1f
    // 0xa609c8: mov             x0, x1
    // 0xa609cc: mov             x1, x7
    // 0xa609d0: cmp             x1, x0
    // 0xa609d4: b.hs            #0xa60bf0
    // 0xa609d8: LoadField: r0 = r6->field_f
    //     0xa609d8: ldur            w0, [x6, #0xf]
    // 0xa609dc: DecompressPointer r0
    //     0xa609dc: add             x0, x0, HEAP, lsl #32
    // 0xa609e0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa609e0: add             x16, x0, x7, lsl #2
    //     0xa609e4: ldur            w1, [x16, #0xf]
    // 0xa609e8: DecompressPointer r1
    //     0xa609e8: add             x1, x1, HEAP, lsl #32
    // 0xa609ec: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xa609ec: ldur            w6, [x1, #0x17]
    // 0xa609f0: DecompressPointer r6
    //     0xa609f0: add             x6, x6, HEAP, lsl #32
    // 0xa609f4: cmp             w6, NULL
    // 0xa609f8: b.ne            #0xa60a04
    // 0xa609fc: r0 = Null
    //     0xa609fc: mov             x0, NULL
    // 0xa60a00: b               #0xa60a40
    // 0xa60a04: LoadField: r0 = r6->field_b
    //     0xa60a04: ldur            w0, [x6, #0xb]
    // 0xa60a08: r7 = LoadInt32Instr(r4)
    //     0xa60a08: sbfx            x7, x4, #1, #0x1f
    //     0xa60a0c: tbz             w4, #0, #0xa60a14
    //     0xa60a10: ldur            x7, [x4, #7]
    // 0xa60a14: r1 = LoadInt32Instr(r0)
    //     0xa60a14: sbfx            x1, x0, #1, #0x1f
    // 0xa60a18: mov             x0, x1
    // 0xa60a1c: mov             x1, x7
    // 0xa60a20: cmp             x1, x0
    // 0xa60a24: b.hs            #0xa60bf4
    // 0xa60a28: LoadField: r0 = r6->field_f
    //     0xa60a28: ldur            w0, [x6, #0xf]
    // 0xa60a2c: DecompressPointer r0
    //     0xa60a2c: add             x0, x0, HEAP, lsl #32
    // 0xa60a30: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa60a30: add             x16, x0, x7, lsl #2
    //     0xa60a34: ldur            w1, [x16, #0xf]
    // 0xa60a38: DecompressPointer r1
    //     0xa60a38: add             x1, x1, HEAP, lsl #32
    // 0xa60a3c: mov             x0, x1
    // 0xa60a40: stur            x0, [fp, #-0x10]
    // 0xa60a44: StoreField: r3->field_13 = r0
    //     0xa60a44: stur            w0, [x3, #0x13]
    // 0xa60a48: LoadField: r1 = r5->field_13
    //     0xa60a48: ldur            w1, [x5, #0x13]
    // 0xa60a4c: DecompressPointer r1
    //     0xa60a4c: add             x1, x1, HEAP, lsl #32
    // 0xa60a50: r16 = Sentinel
    //     0xa60a50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa60a54: cmp             w1, w16
    // 0xa60a58: b.eq            #0xa60bf8
    // 0xa60a5c: cmp             w1, w0
    // 0xa60a60: b.ne            #0xa60a78
    // 0xa60a64: ldr             x1, [fp, #0x18]
    // 0xa60a68: r0 = of()
    //     0xa60a68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa60a6c: LoadField: r1 = r0->field_5b
    //     0xa60a6c: ldur            w1, [x0, #0x5b]
    // 0xa60a70: DecompressPointer r1
    //     0xa60a70: add             x1, x1, HEAP, lsl #32
    // 0xa60a74: b               #0xa60a7c
    // 0xa60a78: r1 = Instance_Color
    //     0xa60a78: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa60a7c: ldur            x0, [fp, #-0x10]
    // 0xa60a80: d0 = 0.000000
    //     0xa60a80: eor             v0.16b, v0.16b, v0.16b
    // 0xa60a84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa60a84: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa60a88: r0 = styleFrom()
    //     0xa60a88: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xa60a8c: mov             x2, x0
    // 0xa60a90: ldur            x0, [fp, #-0x10]
    // 0xa60a94: stur            x2, [fp, #-0x28]
    // 0xa60a98: cmp             w0, NULL
    // 0xa60a9c: b.ne            #0xa60aa8
    // 0xa60aa0: r1 = Null
    //     0xa60aa0: mov             x1, NULL
    // 0xa60aa4: b               #0xa60ab0
    // 0xa60aa8: LoadField: r1 = r0->field_7
    //     0xa60aa8: ldur            w1, [x0, #7]
    // 0xa60aac: DecompressPointer r1
    //     0xa60aac: add             x1, x1, HEAP, lsl #32
    // 0xa60ab0: cmp             w1, NULL
    // 0xa60ab4: b.ne            #0xa60ac0
    // 0xa60ab8: r4 = ""
    //     0xa60ab8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa60abc: b               #0xa60ac4
    // 0xa60ac0: mov             x4, x1
    // 0xa60ac4: ldur            x3, [fp, #-8]
    // 0xa60ac8: ldr             x1, [fp, #0x18]
    // 0xa60acc: stur            x4, [fp, #-0x20]
    // 0xa60ad0: r0 = of()
    //     0xa60ad0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa60ad4: LoadField: r1 = r0->field_87
    //     0xa60ad4: ldur            w1, [x0, #0x87]
    // 0xa60ad8: DecompressPointer r1
    //     0xa60ad8: add             x1, x1, HEAP, lsl #32
    // 0xa60adc: LoadField: r0 = r1->field_2b
    //     0xa60adc: ldur            w0, [x1, #0x2b]
    // 0xa60ae0: DecompressPointer r0
    //     0xa60ae0: add             x0, x0, HEAP, lsl #32
    // 0xa60ae4: ldur            x1, [fp, #-8]
    // 0xa60ae8: stur            x0, [fp, #-0x30]
    // 0xa60aec: LoadField: r2 = r1->field_f
    //     0xa60aec: ldur            w2, [x1, #0xf]
    // 0xa60af0: DecompressPointer r2
    //     0xa60af0: add             x2, x2, HEAP, lsl #32
    // 0xa60af4: LoadField: r1 = r2->field_13
    //     0xa60af4: ldur            w1, [x2, #0x13]
    // 0xa60af8: DecompressPointer r1
    //     0xa60af8: add             x1, x1, HEAP, lsl #32
    // 0xa60afc: r16 = Sentinel
    //     0xa60afc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa60b00: cmp             w1, w16
    // 0xa60b04: b.eq            #0xa60c04
    // 0xa60b08: ldur            x2, [fp, #-0x10]
    // 0xa60b0c: cmp             w1, w2
    // 0xa60b10: b.ne            #0xa60b1c
    // 0xa60b14: r1 = Instance_Color
    //     0xa60b14: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa60b18: b               #0xa60b2c
    // 0xa60b1c: r1 = Instance_Color
    //     0xa60b1c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa60b20: d0 = 0.400000
    //     0xa60b20: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa60b24: r0 = withOpacity()
    //     0xa60b24: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa60b28: mov             x1, x0
    // 0xa60b2c: ldur            x0, [fp, #-0x28]
    // 0xa60b30: ldur            x2, [fp, #-0x20]
    // 0xa60b34: r16 = 16.000000
    //     0xa60b34: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa60b38: ldr             x16, [x16, #0x188]
    // 0xa60b3c: stp             x1, x16, [SP]
    // 0xa60b40: ldur            x1, [fp, #-0x30]
    // 0xa60b44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa60b44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa60b48: ldr             x4, [x4, #0xaa0]
    // 0xa60b4c: r0 = copyWith()
    //     0xa60b4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa60b50: stur            x0, [fp, #-8]
    // 0xa60b54: r0 = Text()
    //     0xa60b54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa60b58: mov             x3, x0
    // 0xa60b5c: ldur            x0, [fp, #-0x20]
    // 0xa60b60: stur            x3, [fp, #-0x10]
    // 0xa60b64: StoreField: r3->field_b = r0
    //     0xa60b64: stur            w0, [x3, #0xb]
    // 0xa60b68: ldur            x0, [fp, #-8]
    // 0xa60b6c: StoreField: r3->field_13 = r0
    //     0xa60b6c: stur            w0, [x3, #0x13]
    // 0xa60b70: r0 = Instance_TextAlign
    //     0xa60b70: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa60b74: StoreField: r3->field_1b = r0
    //     0xa60b74: stur            w0, [x3, #0x1b]
    // 0xa60b78: ldur            x2, [fp, #-0x18]
    // 0xa60b7c: r1 = Function '<anonymous closure>':.
    //     0xa60b7c: add             x1, PP, #0x55, lsl #12  ; [pp+0x555c8] AnonymousClosure: (0xa608d4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xa60b80: ldr             x1, [x1, #0x5c8]
    // 0xa60b84: r0 = AllocateClosure()
    //     0xa60b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa60b88: stur            x0, [fp, #-8]
    // 0xa60b8c: r0 = ElevatedButton()
    //     0xa60b8c: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xa60b90: mov             x1, x0
    // 0xa60b94: ldur            x0, [fp, #-8]
    // 0xa60b98: stur            x1, [fp, #-0x18]
    // 0xa60b9c: StoreField: r1->field_b = r0
    //     0xa60b9c: stur            w0, [x1, #0xb]
    // 0xa60ba0: ldur            x0, [fp, #-0x28]
    // 0xa60ba4: StoreField: r1->field_1b = r0
    //     0xa60ba4: stur            w0, [x1, #0x1b]
    // 0xa60ba8: r0 = false
    //     0xa60ba8: add             x0, NULL, #0x30  ; false
    // 0xa60bac: StoreField: r1->field_27 = r0
    //     0xa60bac: stur            w0, [x1, #0x27]
    // 0xa60bb0: r0 = true
    //     0xa60bb0: add             x0, NULL, #0x20  ; true
    // 0xa60bb4: StoreField: r1->field_2f = r0
    //     0xa60bb4: stur            w0, [x1, #0x2f]
    // 0xa60bb8: ldur            x0, [fp, #-0x10]
    // 0xa60bbc: StoreField: r1->field_37 = r0
    //     0xa60bbc: stur            w0, [x1, #0x37]
    // 0xa60bc0: r0 = Padding()
    //     0xa60bc0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa60bc4: r1 = Instance_EdgeInsets
    //     0xa60bc4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa60bc8: ldr             x1, [x1, #0x980]
    // 0xa60bcc: StoreField: r0->field_f = r1
    //     0xa60bcc: stur            w1, [x0, #0xf]
    // 0xa60bd0: ldur            x1, [fp, #-0x18]
    // 0xa60bd4: StoreField: r0->field_b = r1
    //     0xa60bd4: stur            w1, [x0, #0xb]
    // 0xa60bd8: LeaveFrame
    //     0xa60bd8: mov             SP, fp
    //     0xa60bdc: ldp             fp, lr, [SP], #0x10
    // 0xa60be0: ret
    //     0xa60be0: ret             
    // 0xa60be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa60be4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa60be8: b               #0xa60964
    // 0xa60bec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa60bec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa60bf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60bf0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa60bf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa60bf4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa60bf8: r9 = dropDownValue
    //     0xa60bf8: add             x9, PP, #0x55, lsl #12  ; [pp+0x555d0] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa60bfc: ldr             x9, [x9, #0x5d0]
    // 0xa60c00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa60c00: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa60c04: r9 = dropDownValue
    //     0xa60c04: add             x9, PP, #0x55, lsl #12  ; [pp+0x555d0] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa60c08: ldr             x9, [x9, #0x5d0]
    // 0xa60c0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa60c0c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa610b8, size: 0x88
    // 0xa610b8: EnterFrame
    //     0xa610b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa610bc: mov             fp, SP
    // 0xa610c0: AllocStack(0x8)
    //     0xa610c0: sub             SP, SP, #8
    // 0xa610c4: SetupParameters()
    //     0xa610c4: ldr             x0, [fp, #0x10]
    //     0xa610c8: ldur            w1, [x0, #0x17]
    //     0xa610cc: add             x1, x1, HEAP, lsl #32
    // 0xa610d0: LoadField: r0 = r1->field_b
    //     0xa610d0: ldur            w0, [x1, #0xb]
    // 0xa610d4: DecompressPointer r0
    //     0xa610d4: add             x0, x0, HEAP, lsl #32
    // 0xa610d8: LoadField: r2 = r0->field_f
    //     0xa610d8: ldur            w2, [x0, #0xf]
    // 0xa610dc: DecompressPointer r2
    //     0xa610dc: add             x2, x2, HEAP, lsl #32
    // 0xa610e0: stur            x2, [fp, #-8]
    // 0xa610e4: LoadField: r0 = r1->field_f
    //     0xa610e4: ldur            w0, [x1, #0xf]
    // 0xa610e8: DecompressPointer r0
    //     0xa610e8: add             x0, x0, HEAP, lsl #32
    // 0xa610ec: r3 = LoadInt32Instr(r0)
    //     0xa610ec: sbfx            x3, x0, #1, #0x1f
    //     0xa610f0: tbz             w0, #0, #0xa610f8
    //     0xa610f4: ldur            x3, [x0, #7]
    // 0xa610f8: ArrayStore: r2[0] = r3  ; List_8
    //     0xa610f8: stur            x3, [x2, #0x17]
    // 0xa610fc: LoadField: r0 = r1->field_13
    //     0xa610fc: ldur            w0, [x1, #0x13]
    // 0xa61100: DecompressPointer r0
    //     0xa61100: add             x0, x0, HEAP, lsl #32
    // 0xa61104: cmp             w0, NULL
    // 0xa61108: b.ne            #0xa61110
    // 0xa6110c: r0 = AllSkuDatum()
    //     0xa6110c: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0xa61110: ldur            x1, [fp, #-8]
    // 0xa61114: StoreField: r1->field_13 = r0
    //     0xa61114: stur            w0, [x1, #0x13]
    //     0xa61118: ldurb           w16, [x1, #-1]
    //     0xa6111c: ldurb           w17, [x0, #-1]
    //     0xa61120: and             x16, x17, x16, lsr #2
    //     0xa61124: tst             x16, HEAP, lsr #32
    //     0xa61128: b.eq            #0xa61130
    //     0xa6112c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa61130: r0 = Null
    //     0xa61130: mov             x0, NULL
    // 0xa61134: LeaveFrame
    //     0xa61134: mov             SP, fp
    //     0xa61138: ldp             fp, lr, [SP], #0x10
    // 0xa6113c: ret
    //     0xa6113c: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa61140, size: 0x68
    // 0xa61140: EnterFrame
    //     0xa61140: stp             fp, lr, [SP, #-0x10]!
    //     0xa61144: mov             fp, SP
    // 0xa61148: AllocStack(0x8)
    //     0xa61148: sub             SP, SP, #8
    // 0xa6114c: SetupParameters()
    //     0xa6114c: ldr             x0, [fp, #0x10]
    //     0xa61150: ldur            w2, [x0, #0x17]
    //     0xa61154: add             x2, x2, HEAP, lsl #32
    // 0xa61158: CheckStackOverflow
    //     0xa61158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6115c: cmp             SP, x16
    //     0xa61160: b.ls            #0xa611a0
    // 0xa61164: LoadField: r0 = r2->field_b
    //     0xa61164: ldur            w0, [x2, #0xb]
    // 0xa61168: DecompressPointer r0
    //     0xa61168: add             x0, x0, HEAP, lsl #32
    // 0xa6116c: LoadField: r3 = r0->field_f
    //     0xa6116c: ldur            w3, [x0, #0xf]
    // 0xa61170: DecompressPointer r3
    //     0xa61170: add             x3, x3, HEAP, lsl #32
    // 0xa61174: stur            x3, [fp, #-8]
    // 0xa61178: r1 = Function '<anonymous closure>':.
    //     0xa61178: add             x1, PP, #0x55, lsl #12  ; [pp+0x55620] AnonymousClosure: (0xa610b8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xa6117c: ldr             x1, [x1, #0x620]
    // 0xa61180: r0 = AllocateClosure()
    //     0xa61180: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa61184: ldur            x1, [fp, #-8]
    // 0xa61188: mov             x2, x0
    // 0xa6118c: r0 = setState()
    //     0xa6118c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa61190: r0 = Null
    //     0xa61190: mov             x0, NULL
    // 0xa61194: LeaveFrame
    //     0xa61194: mov             SP, fp
    //     0xa61198: ldp             fp, lr, [SP], #0x10
    // 0xa6119c: ret
    //     0xa6119c: ret             
    // 0xa611a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa611a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa611a4: b               #0xa61164
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa611a8, size: 0x284
    // 0xa611a8: EnterFrame
    //     0xa611a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa611ac: mov             fp, SP
    // 0xa611b0: AllocStack(0x40)
    //     0xa611b0: sub             SP, SP, #0x40
    // 0xa611b4: SetupParameters()
    //     0xa611b4: ldr             x0, [fp, #0x20]
    //     0xa611b8: ldur            w1, [x0, #0x17]
    //     0xa611bc: add             x1, x1, HEAP, lsl #32
    //     0xa611c0: stur            x1, [fp, #-8]
    // 0xa611c4: CheckStackOverflow
    //     0xa611c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa611c8: cmp             SP, x16
    //     0xa611cc: b.ls            #0xa61404
    // 0xa611d0: r1 = 2
    //     0xa611d0: movz            x1, #0x2
    // 0xa611d4: r0 = AllocateContext()
    //     0xa611d4: bl              #0x16f6108  ; AllocateContextStub
    // 0xa611d8: mov             x3, x0
    // 0xa611dc: ldur            x2, [fp, #-8]
    // 0xa611e0: stur            x3, [fp, #-0x18]
    // 0xa611e4: StoreField: r3->field_b = r2
    //     0xa611e4: stur            w2, [x3, #0xb]
    // 0xa611e8: ldr             x0, [fp, #0x10]
    // 0xa611ec: StoreField: r3->field_f = r0
    //     0xa611ec: stur            w0, [x3, #0xf]
    // 0xa611f0: LoadField: r4 = r2->field_f
    //     0xa611f0: ldur            w4, [x2, #0xf]
    // 0xa611f4: DecompressPointer r4
    //     0xa611f4: add             x4, x4, HEAP, lsl #32
    // 0xa611f8: LoadField: r1 = r4->field_b
    //     0xa611f8: ldur            w1, [x4, #0xb]
    // 0xa611fc: DecompressPointer r1
    //     0xa611fc: add             x1, x1, HEAP, lsl #32
    // 0xa61200: cmp             w1, NULL
    // 0xa61204: b.eq            #0xa6140c
    // 0xa61208: LoadField: r5 = r1->field_b
    //     0xa61208: ldur            w5, [x1, #0xb]
    // 0xa6120c: DecompressPointer r5
    //     0xa6120c: add             x5, x5, HEAP, lsl #32
    // 0xa61210: LoadField: r6 = r5->field_d7
    //     0xa61210: ldur            w6, [x5, #0xd7]
    // 0xa61214: DecompressPointer r6
    //     0xa61214: add             x6, x6, HEAP, lsl #32
    // 0xa61218: cmp             w6, NULL
    // 0xa6121c: b.ne            #0xa61228
    // 0xa61220: r0 = Null
    //     0xa61220: mov             x0, NULL
    // 0xa61224: b               #0xa61260
    // 0xa61228: LoadField: r1 = r6->field_b
    //     0xa61228: ldur            w1, [x6, #0xb]
    // 0xa6122c: r5 = LoadInt32Instr(r0)
    //     0xa6122c: sbfx            x5, x0, #1, #0x1f
    //     0xa61230: tbz             w0, #0, #0xa61238
    //     0xa61234: ldur            x5, [x0, #7]
    // 0xa61238: r0 = LoadInt32Instr(r1)
    //     0xa61238: sbfx            x0, x1, #1, #0x1f
    // 0xa6123c: mov             x1, x5
    // 0xa61240: cmp             x1, x0
    // 0xa61244: b.hs            #0xa61410
    // 0xa61248: LoadField: r0 = r6->field_f
    //     0xa61248: ldur            w0, [x6, #0xf]
    // 0xa6124c: DecompressPointer r0
    //     0xa6124c: add             x0, x0, HEAP, lsl #32
    // 0xa61250: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa61250: add             x16, x0, x5, lsl #2
    //     0xa61254: ldur            w1, [x16, #0xf]
    // 0xa61258: DecompressPointer r1
    //     0xa61258: add             x1, x1, HEAP, lsl #32
    // 0xa6125c: mov             x0, x1
    // 0xa61260: stur            x0, [fp, #-0x10]
    // 0xa61264: StoreField: r3->field_13 = r0
    //     0xa61264: stur            w0, [x3, #0x13]
    // 0xa61268: LoadField: r1 = r4->field_13
    //     0xa61268: ldur            w1, [x4, #0x13]
    // 0xa6126c: DecompressPointer r1
    //     0xa6126c: add             x1, x1, HEAP, lsl #32
    // 0xa61270: r16 = Sentinel
    //     0xa61270: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa61274: cmp             w1, w16
    // 0xa61278: b.eq            #0xa61414
    // 0xa6127c: cmp             w1, w0
    // 0xa61280: b.ne            #0xa61298
    // 0xa61284: ldr             x1, [fp, #0x18]
    // 0xa61288: r0 = of()
    //     0xa61288: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa6128c: LoadField: r1 = r0->field_5b
    //     0xa6128c: ldur            w1, [x0, #0x5b]
    // 0xa61290: DecompressPointer r1
    //     0xa61290: add             x1, x1, HEAP, lsl #32
    // 0xa61294: b               #0xa6129c
    // 0xa61298: r1 = Instance_Color
    //     0xa61298: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa6129c: ldur            x0, [fp, #-0x10]
    // 0xa612a0: d0 = 0.000000
    //     0xa612a0: eor             v0.16b, v0.16b, v0.16b
    // 0xa612a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa612a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa612a8: r0 = styleFrom()
    //     0xa612a8: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xa612ac: mov             x2, x0
    // 0xa612b0: ldur            x0, [fp, #-0x10]
    // 0xa612b4: stur            x2, [fp, #-0x28]
    // 0xa612b8: cmp             w0, NULL
    // 0xa612bc: b.ne            #0xa612c8
    // 0xa612c0: r1 = Null
    //     0xa612c0: mov             x1, NULL
    // 0xa612c4: b               #0xa612d0
    // 0xa612c8: LoadField: r1 = r0->field_7
    //     0xa612c8: ldur            w1, [x0, #7]
    // 0xa612cc: DecompressPointer r1
    //     0xa612cc: add             x1, x1, HEAP, lsl #32
    // 0xa612d0: cmp             w1, NULL
    // 0xa612d4: b.ne            #0xa612e0
    // 0xa612d8: r4 = ""
    //     0xa612d8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa612dc: b               #0xa612e4
    // 0xa612e0: mov             x4, x1
    // 0xa612e4: ldur            x3, [fp, #-8]
    // 0xa612e8: ldr             x1, [fp, #0x18]
    // 0xa612ec: stur            x4, [fp, #-0x20]
    // 0xa612f0: r0 = of()
    //     0xa612f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa612f4: LoadField: r1 = r0->field_87
    //     0xa612f4: ldur            w1, [x0, #0x87]
    // 0xa612f8: DecompressPointer r1
    //     0xa612f8: add             x1, x1, HEAP, lsl #32
    // 0xa612fc: LoadField: r0 = r1->field_2b
    //     0xa612fc: ldur            w0, [x1, #0x2b]
    // 0xa61300: DecompressPointer r0
    //     0xa61300: add             x0, x0, HEAP, lsl #32
    // 0xa61304: ldur            x1, [fp, #-8]
    // 0xa61308: stur            x0, [fp, #-0x30]
    // 0xa6130c: LoadField: r2 = r1->field_f
    //     0xa6130c: ldur            w2, [x1, #0xf]
    // 0xa61310: DecompressPointer r2
    //     0xa61310: add             x2, x2, HEAP, lsl #32
    // 0xa61314: LoadField: r1 = r2->field_13
    //     0xa61314: ldur            w1, [x2, #0x13]
    // 0xa61318: DecompressPointer r1
    //     0xa61318: add             x1, x1, HEAP, lsl #32
    // 0xa6131c: r16 = Sentinel
    //     0xa6131c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa61320: cmp             w1, w16
    // 0xa61324: b.eq            #0xa61420
    // 0xa61328: ldur            x2, [fp, #-0x10]
    // 0xa6132c: cmp             w1, w2
    // 0xa61330: b.ne            #0xa6133c
    // 0xa61334: r1 = Instance_Color
    //     0xa61334: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa61338: b               #0xa6134c
    // 0xa6133c: r1 = Instance_Color
    //     0xa6133c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa61340: d0 = 0.400000
    //     0xa61340: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa61344: r0 = withOpacity()
    //     0xa61344: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa61348: mov             x1, x0
    // 0xa6134c: ldur            x0, [fp, #-0x28]
    // 0xa61350: ldur            x2, [fp, #-0x20]
    // 0xa61354: r16 = 16.000000
    //     0xa61354: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa61358: ldr             x16, [x16, #0x188]
    // 0xa6135c: stp             x1, x16, [SP]
    // 0xa61360: ldur            x1, [fp, #-0x30]
    // 0xa61364: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa61364: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa61368: ldr             x4, [x4, #0xaa0]
    // 0xa6136c: r0 = copyWith()
    //     0xa6136c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa61370: stur            x0, [fp, #-8]
    // 0xa61374: r0 = Text()
    //     0xa61374: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa61378: mov             x3, x0
    // 0xa6137c: ldur            x0, [fp, #-0x20]
    // 0xa61380: stur            x3, [fp, #-0x10]
    // 0xa61384: StoreField: r3->field_b = r0
    //     0xa61384: stur            w0, [x3, #0xb]
    // 0xa61388: ldur            x0, [fp, #-8]
    // 0xa6138c: StoreField: r3->field_13 = r0
    //     0xa6138c: stur            w0, [x3, #0x13]
    // 0xa61390: r0 = Instance_TextAlign
    //     0xa61390: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa61394: StoreField: r3->field_1b = r0
    //     0xa61394: stur            w0, [x3, #0x1b]
    // 0xa61398: ldur            x2, [fp, #-0x18]
    // 0xa6139c: r1 = Function '<anonymous closure>':.
    //     0xa6139c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55618] AnonymousClosure: (0xa61140), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xa613a0: ldr             x1, [x1, #0x618]
    // 0xa613a4: r0 = AllocateClosure()
    //     0xa613a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa613a8: stur            x0, [fp, #-8]
    // 0xa613ac: r0 = ElevatedButton()
    //     0xa613ac: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xa613b0: mov             x1, x0
    // 0xa613b4: ldur            x0, [fp, #-8]
    // 0xa613b8: stur            x1, [fp, #-0x18]
    // 0xa613bc: StoreField: r1->field_b = r0
    //     0xa613bc: stur            w0, [x1, #0xb]
    // 0xa613c0: ldur            x0, [fp, #-0x28]
    // 0xa613c4: StoreField: r1->field_1b = r0
    //     0xa613c4: stur            w0, [x1, #0x1b]
    // 0xa613c8: r0 = false
    //     0xa613c8: add             x0, NULL, #0x30  ; false
    // 0xa613cc: StoreField: r1->field_27 = r0
    //     0xa613cc: stur            w0, [x1, #0x27]
    // 0xa613d0: r0 = true
    //     0xa613d0: add             x0, NULL, #0x20  ; true
    // 0xa613d4: StoreField: r1->field_2f = r0
    //     0xa613d4: stur            w0, [x1, #0x2f]
    // 0xa613d8: ldur            x0, [fp, #-0x10]
    // 0xa613dc: StoreField: r1->field_37 = r0
    //     0xa613dc: stur            w0, [x1, #0x37]
    // 0xa613e0: r0 = Padding()
    //     0xa613e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa613e4: r1 = Instance_EdgeInsets
    //     0xa613e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa613e8: ldr             x1, [x1, #0x980]
    // 0xa613ec: StoreField: r0->field_f = r1
    //     0xa613ec: stur            w1, [x0, #0xf]
    // 0xa613f0: ldur            x1, [fp, #-0x18]
    // 0xa613f4: StoreField: r0->field_b = r1
    //     0xa613f4: stur            w1, [x0, #0xb]
    // 0xa613f8: LeaveFrame
    //     0xa613f8: mov             SP, fp
    //     0xa613fc: ldp             fp, lr, [SP], #0x10
    // 0xa61400: ret
    //     0xa61400: ret             
    // 0xa61404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa61404: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa61408: b               #0xa611d0
    // 0xa6140c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6140c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa61410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa61410: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa61414: r9 = dropDownValue
    //     0xa61414: add             x9, PP, #0x55, lsl #12  ; [pp+0x555d0] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa61418: ldr             x9, [x9, #0x5d0]
    // 0xa6141c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa6141c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa61420: r9 = dropDownValue
    //     0xa61420: add             x9, PP, #0x55, lsl #12  ; [pp+0x555d0] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa61424: ldr             x9, [x9, #0x5d0]
    // 0xa61428: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa61428: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb86d10, size: 0xbf8
    // 0xb86d10: EnterFrame
    //     0xb86d10: stp             fp, lr, [SP, #-0x10]!
    //     0xb86d14: mov             fp, SP
    // 0xb86d18: AllocStack(0x58)
    //     0xb86d18: sub             SP, SP, #0x58
    // 0xb86d1c: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb86d1c: mov             x0, x1
    //     0xb86d20: stur            x1, [fp, #-8]
    //     0xb86d24: mov             x1, x2
    //     0xb86d28: stur            x2, [fp, #-0x10]
    // 0xb86d2c: CheckStackOverflow
    //     0xb86d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb86d30: cmp             SP, x16
    //     0xb86d34: b.ls            #0xb878ec
    // 0xb86d38: r1 = 2
    //     0xb86d38: movz            x1, #0x2
    // 0xb86d3c: r0 = AllocateContext()
    //     0xb86d3c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb86d40: mov             x2, x0
    // 0xb86d44: ldur            x0, [fp, #-8]
    // 0xb86d48: stur            x2, [fp, #-0x18]
    // 0xb86d4c: StoreField: r2->field_f = r0
    //     0xb86d4c: stur            w0, [x2, #0xf]
    // 0xb86d50: ldur            x1, [fp, #-0x10]
    // 0xb86d54: StoreField: r2->field_13 = r1
    //     0xb86d54: stur            w1, [x2, #0x13]
    // 0xb86d58: r0 = of()
    //     0xb86d58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb86d5c: LoadField: r1 = r0->field_87
    //     0xb86d5c: ldur            w1, [x0, #0x87]
    // 0xb86d60: DecompressPointer r1
    //     0xb86d60: add             x1, x1, HEAP, lsl #32
    // 0xb86d64: LoadField: r0 = r1->field_7
    //     0xb86d64: ldur            w0, [x1, #7]
    // 0xb86d68: DecompressPointer r0
    //     0xb86d68: add             x0, x0, HEAP, lsl #32
    // 0xb86d6c: r16 = Instance_Color
    //     0xb86d6c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb86d70: r30 = 16.000000
    //     0xb86d70: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb86d74: ldr             lr, [lr, #0x188]
    // 0xb86d78: stp             lr, x16, [SP]
    // 0xb86d7c: mov             x1, x0
    // 0xb86d80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb86d80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb86d84: ldr             x4, [x4, #0x9b8]
    // 0xb86d88: r0 = copyWith()
    //     0xb86d88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb86d8c: stur            x0, [fp, #-0x10]
    // 0xb86d90: r0 = Text()
    //     0xb86d90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb86d94: mov             x1, x0
    // 0xb86d98: r0 = "Select Preference"
    //     0xb86d98: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xb86d9c: ldr             x0, [x0, #0x710]
    // 0xb86da0: stur            x1, [fp, #-0x20]
    // 0xb86da4: StoreField: r1->field_b = r0
    //     0xb86da4: stur            w0, [x1, #0xb]
    // 0xb86da8: ldur            x0, [fp, #-0x10]
    // 0xb86dac: StoreField: r1->field_13 = r0
    //     0xb86dac: stur            w0, [x1, #0x13]
    // 0xb86db0: r0 = InkWell()
    //     0xb86db0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb86db4: mov             x3, x0
    // 0xb86db8: r0 = Instance_Icon
    //     0xb86db8: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb86dbc: ldr             x0, [x0, #0x2b8]
    // 0xb86dc0: stur            x3, [fp, #-0x10]
    // 0xb86dc4: StoreField: r3->field_b = r0
    //     0xb86dc4: stur            w0, [x3, #0xb]
    // 0xb86dc8: ldur            x2, [fp, #-0x18]
    // 0xb86dcc: r1 = Function '<anonymous closure>':.
    //     0xb86dcc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55588] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb86dd0: ldr             x1, [x1, #0x588]
    // 0xb86dd4: r0 = AllocateClosure()
    //     0xb86dd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86dd8: mov             x1, x0
    // 0xb86ddc: ldur            x0, [fp, #-0x10]
    // 0xb86de0: StoreField: r0->field_f = r1
    //     0xb86de0: stur            w1, [x0, #0xf]
    // 0xb86de4: r3 = true
    //     0xb86de4: add             x3, NULL, #0x20  ; true
    // 0xb86de8: StoreField: r0->field_43 = r3
    //     0xb86de8: stur            w3, [x0, #0x43]
    // 0xb86dec: r1 = Instance_BoxShape
    //     0xb86dec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb86df0: ldr             x1, [x1, #0x80]
    // 0xb86df4: StoreField: r0->field_47 = r1
    //     0xb86df4: stur            w1, [x0, #0x47]
    // 0xb86df8: StoreField: r0->field_6f = r3
    //     0xb86df8: stur            w3, [x0, #0x6f]
    // 0xb86dfc: r4 = false
    //     0xb86dfc: add             x4, NULL, #0x30  ; false
    // 0xb86e00: StoreField: r0->field_73 = r4
    //     0xb86e00: stur            w4, [x0, #0x73]
    // 0xb86e04: StoreField: r0->field_83 = r3
    //     0xb86e04: stur            w3, [x0, #0x83]
    // 0xb86e08: StoreField: r0->field_7b = r4
    //     0xb86e08: stur            w4, [x0, #0x7b]
    // 0xb86e0c: r1 = Null
    //     0xb86e0c: mov             x1, NULL
    // 0xb86e10: r2 = 6
    //     0xb86e10: movz            x2, #0x6
    // 0xb86e14: r0 = AllocateArray()
    //     0xb86e14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb86e18: mov             x2, x0
    // 0xb86e1c: ldur            x0, [fp, #-0x20]
    // 0xb86e20: stur            x2, [fp, #-0x28]
    // 0xb86e24: StoreField: r2->field_f = r0
    //     0xb86e24: stur            w0, [x2, #0xf]
    // 0xb86e28: r16 = Instance_Spacer
    //     0xb86e28: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb86e2c: ldr             x16, [x16, #0xf0]
    // 0xb86e30: StoreField: r2->field_13 = r16
    //     0xb86e30: stur            w16, [x2, #0x13]
    // 0xb86e34: ldur            x0, [fp, #-0x10]
    // 0xb86e38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb86e38: stur            w0, [x2, #0x17]
    // 0xb86e3c: r1 = <Widget>
    //     0xb86e3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb86e40: r0 = AllocateGrowableArray()
    //     0xb86e40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb86e44: mov             x1, x0
    // 0xb86e48: ldur            x0, [fp, #-0x28]
    // 0xb86e4c: stur            x1, [fp, #-0x10]
    // 0xb86e50: StoreField: r1->field_f = r0
    //     0xb86e50: stur            w0, [x1, #0xf]
    // 0xb86e54: r2 = 6
    //     0xb86e54: movz            x2, #0x6
    // 0xb86e58: StoreField: r1->field_b = r2
    //     0xb86e58: stur            w2, [x1, #0xb]
    // 0xb86e5c: r0 = Row()
    //     0xb86e5c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb86e60: mov             x1, x0
    // 0xb86e64: r0 = Instance_Axis
    //     0xb86e64: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb86e68: stur            x1, [fp, #-0x20]
    // 0xb86e6c: StoreField: r1->field_f = r0
    //     0xb86e6c: stur            w0, [x1, #0xf]
    // 0xb86e70: r0 = Instance_MainAxisAlignment
    //     0xb86e70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb86e74: ldr             x0, [x0, #0xa08]
    // 0xb86e78: StoreField: r1->field_13 = r0
    //     0xb86e78: stur            w0, [x1, #0x13]
    // 0xb86e7c: r2 = Instance_MainAxisSize
    //     0xb86e7c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb86e80: ldr             x2, [x2, #0xa10]
    // 0xb86e84: ArrayStore: r1[0] = r2  ; List_4
    //     0xb86e84: stur            w2, [x1, #0x17]
    // 0xb86e88: r2 = Instance_CrossAxisAlignment
    //     0xb86e88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb86e8c: ldr             x2, [x2, #0xa18]
    // 0xb86e90: StoreField: r1->field_1b = r2
    //     0xb86e90: stur            w2, [x1, #0x1b]
    // 0xb86e94: r2 = Instance_VerticalDirection
    //     0xb86e94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb86e98: ldr             x2, [x2, #0xa20]
    // 0xb86e9c: StoreField: r1->field_23 = r2
    //     0xb86e9c: stur            w2, [x1, #0x23]
    // 0xb86ea0: r3 = Instance_Clip
    //     0xb86ea0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb86ea4: ldr             x3, [x3, #0x38]
    // 0xb86ea8: StoreField: r1->field_2b = r3
    //     0xb86ea8: stur            w3, [x1, #0x2b]
    // 0xb86eac: StoreField: r1->field_2f = rZR
    //     0xb86eac: stur            xzr, [x1, #0x2f]
    // 0xb86eb0: ldur            x4, [fp, #-0x10]
    // 0xb86eb4: StoreField: r1->field_b = r4
    //     0xb86eb4: stur            w4, [x1, #0xb]
    // 0xb86eb8: r0 = Padding()
    //     0xb86eb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb86ebc: mov             x2, x0
    // 0xb86ec0: r0 = Instance_EdgeInsets
    //     0xb86ec0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb86ec4: ldr             x0, [x0, #0xd0]
    // 0xb86ec8: stur            x2, [fp, #-0x10]
    // 0xb86ecc: StoreField: r2->field_f = r0
    //     0xb86ecc: stur            w0, [x2, #0xf]
    // 0xb86ed0: ldur            x0, [fp, #-0x20]
    // 0xb86ed4: StoreField: r2->field_b = r0
    //     0xb86ed4: stur            w0, [x2, #0xb]
    // 0xb86ed8: ldur            x0, [fp, #-0x18]
    // 0xb86edc: LoadField: r1 = r0->field_13
    //     0xb86edc: ldur            w1, [x0, #0x13]
    // 0xb86ee0: DecompressPointer r1
    //     0xb86ee0: add             x1, x1, HEAP, lsl #32
    // 0xb86ee4: r0 = of()
    //     0xb86ee4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb86ee8: LoadField: r1 = r0->field_5b
    //     0xb86ee8: ldur            w1, [x0, #0x5b]
    // 0xb86eec: DecompressPointer r1
    //     0xb86eec: add             x1, x1, HEAP, lsl #32
    // 0xb86ef0: r0 = LoadClassIdInstr(r1)
    //     0xb86ef0: ldur            x0, [x1, #-1]
    //     0xb86ef4: ubfx            x0, x0, #0xc, #0x14
    // 0xb86ef8: d0 = 0.030000
    //     0xb86ef8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb86efc: ldr             d0, [x17, #0x238]
    // 0xb86f00: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb86f00: sub             lr, x0, #0xffa
    //     0xb86f04: ldr             lr, [x21, lr, lsl #3]
    //     0xb86f08: blr             lr
    // 0xb86f0c: stur            x0, [fp, #-0x20]
    // 0xb86f10: r0 = Divider()
    //     0xb86f10: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb86f14: mov             x3, x0
    // 0xb86f18: ldur            x0, [fp, #-0x20]
    // 0xb86f1c: stur            x3, [fp, #-0x30]
    // 0xb86f20: StoreField: r3->field_1f = r0
    //     0xb86f20: stur            w0, [x3, #0x1f]
    // 0xb86f24: ldur            x0, [fp, #-8]
    // 0xb86f28: LoadField: r1 = r0->field_b
    //     0xb86f28: ldur            w1, [x0, #0xb]
    // 0xb86f2c: DecompressPointer r1
    //     0xb86f2c: add             x1, x1, HEAP, lsl #32
    // 0xb86f30: cmp             w1, NULL
    // 0xb86f34: b.eq            #0xb878f4
    // 0xb86f38: LoadField: r2 = r1->field_b
    //     0xb86f38: ldur            w2, [x1, #0xb]
    // 0xb86f3c: DecompressPointer r2
    //     0xb86f3c: add             x2, x2, HEAP, lsl #32
    // 0xb86f40: LoadField: r1 = r2->field_db
    //     0xb86f40: ldur            w1, [x2, #0xdb]
    // 0xb86f44: DecompressPointer r1
    //     0xb86f44: add             x1, x1, HEAP, lsl #32
    // 0xb86f48: cmp             w1, NULL
    // 0xb86f4c: b.ne            #0xb86f58
    // 0xb86f50: r2 = Null
    //     0xb86f50: mov             x2, NULL
    // 0xb86f54: b               #0xb86f70
    // 0xb86f58: LoadField: r2 = r1->field_b
    //     0xb86f58: ldur            w2, [x1, #0xb]
    // 0xb86f5c: cbnz            w2, #0xb86f68
    // 0xb86f60: r4 = false
    //     0xb86f60: add             x4, NULL, #0x30  ; false
    // 0xb86f64: b               #0xb86f6c
    // 0xb86f68: r4 = true
    //     0xb86f68: add             x4, NULL, #0x20  ; true
    // 0xb86f6c: mov             x2, x4
    // 0xb86f70: cmp             w2, NULL
    // 0xb86f74: b.ne            #0xb86f80
    // 0xb86f78: r4 = false
    //     0xb86f78: add             x4, NULL, #0x30  ; false
    // 0xb86f7c: b               #0xb86f84
    // 0xb86f80: mov             x4, x2
    // 0xb86f84: stur            x4, [fp, #-0x28]
    // 0xb86f88: cmp             w1, NULL
    // 0xb86f8c: b.ne            #0xb86f98
    // 0xb86f90: r6 = Null
    //     0xb86f90: mov             x6, NULL
    // 0xb86f94: b               #0xb86fa0
    // 0xb86f98: LoadField: r2 = r1->field_b
    //     0xb86f98: ldur            w2, [x1, #0xb]
    // 0xb86f9c: mov             x6, x2
    // 0xb86fa0: ldur            x5, [fp, #-0x10]
    // 0xb86fa4: ldur            x2, [fp, #-0x18]
    // 0xb86fa8: stur            x6, [fp, #-0x20]
    // 0xb86fac: r1 = Function '<anonymous closure>':.
    //     0xb86fac: add             x1, PP, #0x55, lsl #12  ; [pp+0x55590] AnonymousClosure: (0xb87c6c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb86fb0: ldr             x1, [x1, #0x590]
    // 0xb86fb4: r0 = AllocateClosure()
    //     0xb86fb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb86fb8: stur            x0, [fp, #-0x38]
    // 0xb86fbc: r0 = ListView()
    //     0xb86fbc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb86fc0: stur            x0, [fp, #-0x40]
    // 0xb86fc4: r16 = true
    //     0xb86fc4: add             x16, NULL, #0x20  ; true
    // 0xb86fc8: r30 = Instance_Axis
    //     0xb86fc8: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb86fcc: stp             lr, x16, [SP]
    // 0xb86fd0: mov             x1, x0
    // 0xb86fd4: ldur            x2, [fp, #-0x38]
    // 0xb86fd8: ldur            x3, [fp, #-0x20]
    // 0xb86fdc: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb86fdc: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb86fe0: ldr             x4, [x4, #0x2d0]
    // 0xb86fe4: r0 = ListView.builder()
    //     0xb86fe4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb86fe8: r0 = SizedBox()
    //     0xb86fe8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb86fec: mov             x1, x0
    // 0xb86ff0: r0 = 130.000000
    //     0xb86ff0: add             x0, PP, #0x42, lsl #12  ; [pp+0x427b0] 130
    //     0xb86ff4: ldr             x0, [x0, #0x7b0]
    // 0xb86ff8: stur            x1, [fp, #-0x20]
    // 0xb86ffc: StoreField: r1->field_13 = r0
    //     0xb86ffc: stur            w0, [x1, #0x13]
    // 0xb87000: ldur            x0, [fp, #-0x40]
    // 0xb87004: StoreField: r1->field_b = r0
    //     0xb87004: stur            w0, [x1, #0xb]
    // 0xb87008: r0 = Visibility()
    //     0xb87008: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8700c: mov             x3, x0
    // 0xb87010: ldur            x0, [fp, #-0x20]
    // 0xb87014: stur            x3, [fp, #-0x38]
    // 0xb87018: StoreField: r3->field_b = r0
    //     0xb87018: stur            w0, [x3, #0xb]
    // 0xb8701c: r0 = Instance_SizedBox
    //     0xb8701c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb87020: StoreField: r3->field_f = r0
    //     0xb87020: stur            w0, [x3, #0xf]
    // 0xb87024: ldur            x0, [fp, #-0x28]
    // 0xb87028: StoreField: r3->field_13 = r0
    //     0xb87028: stur            w0, [x3, #0x13]
    // 0xb8702c: r0 = false
    //     0xb8702c: add             x0, NULL, #0x30  ; false
    // 0xb87030: ArrayStore: r3[0] = r0  ; List_4
    //     0xb87030: stur            w0, [x3, #0x17]
    // 0xb87034: StoreField: r3->field_1b = r0
    //     0xb87034: stur            w0, [x3, #0x1b]
    // 0xb87038: StoreField: r3->field_1f = r0
    //     0xb87038: stur            w0, [x3, #0x1f]
    // 0xb8703c: StoreField: r3->field_23 = r0
    //     0xb8703c: stur            w0, [x3, #0x23]
    // 0xb87040: StoreField: r3->field_27 = r0
    //     0xb87040: stur            w0, [x3, #0x27]
    // 0xb87044: StoreField: r3->field_2b = r0
    //     0xb87044: stur            w0, [x3, #0x2b]
    // 0xb87048: r1 = Null
    //     0xb87048: mov             x1, NULL
    // 0xb8704c: r2 = 6
    //     0xb8704c: movz            x2, #0x6
    // 0xb87050: r0 = AllocateArray()
    //     0xb87050: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb87054: mov             x2, x0
    // 0xb87058: ldur            x0, [fp, #-0x10]
    // 0xb8705c: stur            x2, [fp, #-0x20]
    // 0xb87060: StoreField: r2->field_f = r0
    //     0xb87060: stur            w0, [x2, #0xf]
    // 0xb87064: ldur            x0, [fp, #-0x30]
    // 0xb87068: StoreField: r2->field_13 = r0
    //     0xb87068: stur            w0, [x2, #0x13]
    // 0xb8706c: ldur            x0, [fp, #-0x38]
    // 0xb87070: ArrayStore: r2[0] = r0  ; List_4
    //     0xb87070: stur            w0, [x2, #0x17]
    // 0xb87074: r1 = <Widget>
    //     0xb87074: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb87078: r0 = AllocateGrowableArray()
    //     0xb87078: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8707c: mov             x1, x0
    // 0xb87080: ldur            x0, [fp, #-0x20]
    // 0xb87084: stur            x1, [fp, #-0x10]
    // 0xb87088: StoreField: r1->field_f = r0
    //     0xb87088: stur            w0, [x1, #0xf]
    // 0xb8708c: r0 = 6
    //     0xb8708c: movz            x0, #0x6
    // 0xb87090: StoreField: r1->field_b = r0
    //     0xb87090: stur            w0, [x1, #0xb]
    // 0xb87094: ldur            x2, [fp, #-8]
    // 0xb87098: LoadField: r0 = r2->field_b
    //     0xb87098: ldur            w0, [x2, #0xb]
    // 0xb8709c: DecompressPointer r0
    //     0xb8709c: add             x0, x0, HEAP, lsl #32
    // 0xb870a0: cmp             w0, NULL
    // 0xb870a4: b.eq            #0xb878f8
    // 0xb870a8: LoadField: r3 = r0->field_b
    //     0xb870a8: ldur            w3, [x0, #0xb]
    // 0xb870ac: DecompressPointer r3
    //     0xb870ac: add             x3, x3, HEAP, lsl #32
    // 0xb870b0: r17 = 303
    //     0xb870b0: movz            x17, #0x12f
    // 0xb870b4: ldr             w0, [x3, x17]
    // 0xb870b8: DecompressPointer r0
    //     0xb870b8: add             x0, x0, HEAP, lsl #32
    // 0xb870bc: r3 = LoadClassIdInstr(r0)
    //     0xb870bc: ldur            x3, [x0, #-1]
    //     0xb870c0: ubfx            x3, x3, #0xc, #0x14
    // 0xb870c4: r16 = "size"
    //     0xb870c4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb870c8: ldr             x16, [x16, #0x9c0]
    // 0xb870cc: stp             x16, x0, [SP]
    // 0xb870d0: mov             x0, x3
    // 0xb870d4: mov             lr, x0
    // 0xb870d8: ldr             lr, [x21, lr, lsl #3]
    // 0xb870dc: blr             lr
    // 0xb870e0: tbnz            w0, #4, #0xb871ec
    // 0xb870e4: ldur            x2, [fp, #-0x18]
    // 0xb870e8: ldur            x0, [fp, #-0x10]
    // 0xb870ec: LoadField: r1 = r2->field_13
    //     0xb870ec: ldur            w1, [x2, #0x13]
    // 0xb870f0: DecompressPointer r1
    //     0xb870f0: add             x1, x1, HEAP, lsl #32
    // 0xb870f4: r0 = of()
    //     0xb870f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb870f8: LoadField: r1 = r0->field_87
    //     0xb870f8: ldur            w1, [x0, #0x87]
    // 0xb870fc: DecompressPointer r1
    //     0xb870fc: add             x1, x1, HEAP, lsl #32
    // 0xb87100: LoadField: r0 = r1->field_7
    //     0xb87100: ldur            w0, [x1, #7]
    // 0xb87104: DecompressPointer r0
    //     0xb87104: add             x0, x0, HEAP, lsl #32
    // 0xb87108: r16 = 16.000000
    //     0xb87108: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb8710c: ldr             x16, [x16, #0x188]
    // 0xb87110: r30 = Instance_Color
    //     0xb87110: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb87114: stp             lr, x16, [SP]
    // 0xb87118: mov             x1, x0
    // 0xb8711c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8711c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb87120: ldr             x4, [x4, #0xaa0]
    // 0xb87124: r0 = copyWith()
    //     0xb87124: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb87128: stur            x0, [fp, #-0x20]
    // 0xb8712c: r0 = Text()
    //     0xb8712c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb87130: mov             x1, x0
    // 0xb87134: r0 = "Select Size"
    //     0xb87134: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xb87138: ldr             x0, [x0, #0x370]
    // 0xb8713c: stur            x1, [fp, #-0x28]
    // 0xb87140: StoreField: r1->field_b = r0
    //     0xb87140: stur            w0, [x1, #0xb]
    // 0xb87144: ldur            x0, [fp, #-0x20]
    // 0xb87148: StoreField: r1->field_13 = r0
    //     0xb87148: stur            w0, [x1, #0x13]
    // 0xb8714c: r0 = Padding()
    //     0xb8714c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb87150: mov             x2, x0
    // 0xb87154: r0 = Instance_EdgeInsets
    //     0xb87154: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb87158: ldr             x0, [x0, #0xa78]
    // 0xb8715c: stur            x2, [fp, #-0x20]
    // 0xb87160: StoreField: r2->field_f = r0
    //     0xb87160: stur            w0, [x2, #0xf]
    // 0xb87164: ldur            x0, [fp, #-0x28]
    // 0xb87168: StoreField: r2->field_b = r0
    //     0xb87168: stur            w0, [x2, #0xb]
    // 0xb8716c: ldur            x0, [fp, #-0x10]
    // 0xb87170: LoadField: r1 = r0->field_b
    //     0xb87170: ldur            w1, [x0, #0xb]
    // 0xb87174: LoadField: r3 = r0->field_f
    //     0xb87174: ldur            w3, [x0, #0xf]
    // 0xb87178: DecompressPointer r3
    //     0xb87178: add             x3, x3, HEAP, lsl #32
    // 0xb8717c: LoadField: r4 = r3->field_b
    //     0xb8717c: ldur            w4, [x3, #0xb]
    // 0xb87180: r3 = LoadInt32Instr(r1)
    //     0xb87180: sbfx            x3, x1, #1, #0x1f
    // 0xb87184: stur            x3, [fp, #-0x48]
    // 0xb87188: r1 = LoadInt32Instr(r4)
    //     0xb87188: sbfx            x1, x4, #1, #0x1f
    // 0xb8718c: cmp             x3, x1
    // 0xb87190: b.ne            #0xb8719c
    // 0xb87194: mov             x1, x0
    // 0xb87198: r0 = _growToNextCapacity()
    //     0xb87198: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8719c: ldur            x2, [fp, #-0x10]
    // 0xb871a0: ldur            x3, [fp, #-0x48]
    // 0xb871a4: add             x0, x3, #1
    // 0xb871a8: lsl             x1, x0, #1
    // 0xb871ac: StoreField: r2->field_b = r1
    //     0xb871ac: stur            w1, [x2, #0xb]
    // 0xb871b0: LoadField: r1 = r2->field_f
    //     0xb871b0: ldur            w1, [x2, #0xf]
    // 0xb871b4: DecompressPointer r1
    //     0xb871b4: add             x1, x1, HEAP, lsl #32
    // 0xb871b8: ldur            x0, [fp, #-0x20]
    // 0xb871bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb871bc: add             x25, x1, x3, lsl #2
    //     0xb871c0: add             x25, x25, #0xf
    //     0xb871c4: str             w0, [x25]
    //     0xb871c8: tbz             w0, #0, #0xb871e4
    //     0xb871cc: ldurb           w16, [x1, #-1]
    //     0xb871d0: ldurb           w17, [x0, #-1]
    //     0xb871d4: and             x16, x17, x16, lsr #2
    //     0xb871d8: tst             x16, HEAP, lsr #32
    //     0xb871dc: b.eq            #0xb871e4
    //     0xb871e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb871e4: mov             x3, x2
    // 0xb871e8: b               #0xb872f4
    // 0xb871ec: ldur            x3, [fp, #-0x18]
    // 0xb871f0: ldur            x2, [fp, #-0x10]
    // 0xb871f4: r0 = Instance_EdgeInsets
    //     0xb871f4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb871f8: ldr             x0, [x0, #0xa78]
    // 0xb871fc: LoadField: r1 = r3->field_13
    //     0xb871fc: ldur            w1, [x3, #0x13]
    // 0xb87200: DecompressPointer r1
    //     0xb87200: add             x1, x1, HEAP, lsl #32
    // 0xb87204: r0 = of()
    //     0xb87204: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb87208: LoadField: r1 = r0->field_87
    //     0xb87208: ldur            w1, [x0, #0x87]
    // 0xb8720c: DecompressPointer r1
    //     0xb8720c: add             x1, x1, HEAP, lsl #32
    // 0xb87210: LoadField: r0 = r1->field_7
    //     0xb87210: ldur            w0, [x1, #7]
    // 0xb87214: DecompressPointer r0
    //     0xb87214: add             x0, x0, HEAP, lsl #32
    // 0xb87218: r16 = 16.000000
    //     0xb87218: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb8721c: ldr             x16, [x16, #0x188]
    // 0xb87220: r30 = Instance_Color
    //     0xb87220: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb87224: stp             lr, x16, [SP]
    // 0xb87228: mov             x1, x0
    // 0xb8722c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8722c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb87230: ldr             x4, [x4, #0xaa0]
    // 0xb87234: r0 = copyWith()
    //     0xb87234: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb87238: stur            x0, [fp, #-0x20]
    // 0xb8723c: r0 = Text()
    //     0xb8723c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb87240: mov             x1, x0
    // 0xb87244: r0 = "Variant"
    //     0xb87244: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xb87248: ldr             x0, [x0, #0x738]
    // 0xb8724c: stur            x1, [fp, #-0x28]
    // 0xb87250: StoreField: r1->field_b = r0
    //     0xb87250: stur            w0, [x1, #0xb]
    // 0xb87254: ldur            x0, [fp, #-0x20]
    // 0xb87258: StoreField: r1->field_13 = r0
    //     0xb87258: stur            w0, [x1, #0x13]
    // 0xb8725c: r0 = Padding()
    //     0xb8725c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb87260: mov             x2, x0
    // 0xb87264: r0 = Instance_EdgeInsets
    //     0xb87264: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb87268: ldr             x0, [x0, #0xa78]
    // 0xb8726c: stur            x2, [fp, #-0x20]
    // 0xb87270: StoreField: r2->field_f = r0
    //     0xb87270: stur            w0, [x2, #0xf]
    // 0xb87274: ldur            x0, [fp, #-0x28]
    // 0xb87278: StoreField: r2->field_b = r0
    //     0xb87278: stur            w0, [x2, #0xb]
    // 0xb8727c: ldur            x0, [fp, #-0x10]
    // 0xb87280: LoadField: r1 = r0->field_b
    //     0xb87280: ldur            w1, [x0, #0xb]
    // 0xb87284: LoadField: r3 = r0->field_f
    //     0xb87284: ldur            w3, [x0, #0xf]
    // 0xb87288: DecompressPointer r3
    //     0xb87288: add             x3, x3, HEAP, lsl #32
    // 0xb8728c: LoadField: r4 = r3->field_b
    //     0xb8728c: ldur            w4, [x3, #0xb]
    // 0xb87290: r3 = LoadInt32Instr(r1)
    //     0xb87290: sbfx            x3, x1, #1, #0x1f
    // 0xb87294: stur            x3, [fp, #-0x48]
    // 0xb87298: r1 = LoadInt32Instr(r4)
    //     0xb87298: sbfx            x1, x4, #1, #0x1f
    // 0xb8729c: cmp             x3, x1
    // 0xb872a0: b.ne            #0xb872ac
    // 0xb872a4: mov             x1, x0
    // 0xb872a8: r0 = _growToNextCapacity()
    //     0xb872a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb872ac: ldur            x3, [fp, #-0x10]
    // 0xb872b0: ldur            x2, [fp, #-0x48]
    // 0xb872b4: add             x0, x2, #1
    // 0xb872b8: lsl             x1, x0, #1
    // 0xb872bc: StoreField: r3->field_b = r1
    //     0xb872bc: stur            w1, [x3, #0xb]
    // 0xb872c0: LoadField: r1 = r3->field_f
    //     0xb872c0: ldur            w1, [x3, #0xf]
    // 0xb872c4: DecompressPointer r1
    //     0xb872c4: add             x1, x1, HEAP, lsl #32
    // 0xb872c8: ldur            x0, [fp, #-0x20]
    // 0xb872cc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb872cc: add             x25, x1, x2, lsl #2
    //     0xb872d0: add             x25, x25, #0xf
    //     0xb872d4: str             w0, [x25]
    //     0xb872d8: tbz             w0, #0, #0xb872f4
    //     0xb872dc: ldurb           w16, [x1, #-1]
    //     0xb872e0: ldurb           w17, [x0, #-1]
    //     0xb872e4: and             x16, x17, x16, lsr #2
    //     0xb872e8: tst             x16, HEAP, lsr #32
    //     0xb872ec: b.eq            #0xb872f4
    //     0xb872f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb872f4: ldur            x0, [fp, #-8]
    // 0xb872f8: LoadField: r1 = r0->field_b
    //     0xb872f8: ldur            w1, [x0, #0xb]
    // 0xb872fc: DecompressPointer r1
    //     0xb872fc: add             x1, x1, HEAP, lsl #32
    // 0xb87300: cmp             w1, NULL
    // 0xb87304: b.eq            #0xb878fc
    // 0xb87308: LoadField: r2 = r1->field_b
    //     0xb87308: ldur            w2, [x1, #0xb]
    // 0xb8730c: DecompressPointer r2
    //     0xb8730c: add             x2, x2, HEAP, lsl #32
    // 0xb87310: LoadField: r4 = r2->field_db
    //     0xb87310: ldur            w4, [x2, #0xdb]
    // 0xb87314: DecompressPointer r4
    //     0xb87314: add             x4, x4, HEAP, lsl #32
    // 0xb87318: cmp             w4, NULL
    // 0xb8731c: b.ne            #0xb87328
    // 0xb87320: r1 = Null
    //     0xb87320: mov             x1, NULL
    // 0xb87324: b               #0xb87340
    // 0xb87328: LoadField: r1 = r4->field_b
    //     0xb87328: ldur            w1, [x4, #0xb]
    // 0xb8732c: cbz             w1, #0xb87338
    // 0xb87330: r5 = false
    //     0xb87330: add             x5, NULL, #0x30  ; false
    // 0xb87334: b               #0xb8733c
    // 0xb87338: r5 = true
    //     0xb87338: add             x5, NULL, #0x20  ; true
    // 0xb8733c: mov             x1, x5
    // 0xb87340: cmp             w1, NULL
    // 0xb87344: b.eq            #0xb8734c
    // 0xb87348: tbnz            w1, #4, #0xb87474
    // 0xb8734c: LoadField: r1 = r2->field_d7
    //     0xb8734c: ldur            w1, [x2, #0xd7]
    // 0xb87350: DecompressPointer r1
    //     0xb87350: add             x1, x1, HEAP, lsl #32
    // 0xb87354: cmp             w1, NULL
    // 0xb87358: b.ne            #0xb87364
    // 0xb8735c: r1 = Null
    //     0xb8735c: mov             x1, NULL
    // 0xb87360: b               #0xb8736c
    // 0xb87364: LoadField: r2 = r1->field_b
    //     0xb87364: ldur            w2, [x1, #0xb]
    // 0xb87368: mov             x1, x2
    // 0xb8736c: cmp             w1, NULL
    // 0xb87370: b.ne            #0xb8737c
    // 0xb87374: r1 = 0
    //     0xb87374: movz            x1, #0
    // 0xb87378: b               #0xb87384
    // 0xb8737c: r2 = LoadInt32Instr(r1)
    //     0xb8737c: sbfx            x2, x1, #1, #0x1f
    // 0xb87380: mov             x1, x2
    // 0xb87384: lsl             x4, x1, #1
    // 0xb87388: ldur            x2, [fp, #-0x18]
    // 0xb8738c: stur            x4, [fp, #-0x20]
    // 0xb87390: r1 = Function '<anonymous closure>':.
    //     0xb87390: add             x1, PP, #0x55, lsl #12  ; [pp+0x55598] AnonymousClosure: (0xa611a8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb87394: ldr             x1, [x1, #0x598]
    // 0xb87398: r0 = AllocateClosure()
    //     0xb87398: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8739c: stur            x0, [fp, #-0x28]
    // 0xb873a0: r0 = ListView()
    //     0xb873a0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb873a4: stur            x0, [fp, #-0x30]
    // 0xb873a8: r16 = Instance_Axis
    //     0xb873a8: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb873ac: str             x16, [SP]
    // 0xb873b0: mov             x1, x0
    // 0xb873b4: ldur            x2, [fp, #-0x28]
    // 0xb873b8: ldur            x3, [fp, #-0x20]
    // 0xb873bc: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb873bc: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb873c0: ldr             x4, [x4, #0x4a0]
    // 0xb873c4: r0 = ListView.builder()
    //     0xb873c4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb873c8: r0 = SizedBox()
    //     0xb873c8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb873cc: mov             x2, x0
    // 0xb873d0: r0 = inf
    //     0xb873d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb873d4: ldr             x0, [x0, #0x9f8]
    // 0xb873d8: stur            x2, [fp, #-0x20]
    // 0xb873dc: StoreField: r2->field_f = r0
    //     0xb873dc: stur            w0, [x2, #0xf]
    // 0xb873e0: r3 = 55.000000
    //     0xb873e0: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb873e4: ldr             x3, [x3, #0x9b8]
    // 0xb873e8: StoreField: r2->field_13 = r3
    //     0xb873e8: stur            w3, [x2, #0x13]
    // 0xb873ec: ldur            x1, [fp, #-0x30]
    // 0xb873f0: StoreField: r2->field_b = r1
    //     0xb873f0: stur            w1, [x2, #0xb]
    // 0xb873f4: ldur            x3, [fp, #-0x10]
    // 0xb873f8: LoadField: r1 = r3->field_b
    //     0xb873f8: ldur            w1, [x3, #0xb]
    // 0xb873fc: LoadField: r4 = r3->field_f
    //     0xb873fc: ldur            w4, [x3, #0xf]
    // 0xb87400: DecompressPointer r4
    //     0xb87400: add             x4, x4, HEAP, lsl #32
    // 0xb87404: LoadField: r5 = r4->field_b
    //     0xb87404: ldur            w5, [x4, #0xb]
    // 0xb87408: r4 = LoadInt32Instr(r1)
    //     0xb87408: sbfx            x4, x1, #1, #0x1f
    // 0xb8740c: stur            x4, [fp, #-0x48]
    // 0xb87410: r1 = LoadInt32Instr(r5)
    //     0xb87410: sbfx            x1, x5, #1, #0x1f
    // 0xb87414: cmp             x4, x1
    // 0xb87418: b.ne            #0xb87424
    // 0xb8741c: mov             x1, x3
    // 0xb87420: r0 = _growToNextCapacity()
    //     0xb87420: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb87424: ldur            x5, [fp, #-0x10]
    // 0xb87428: ldur            x2, [fp, #-0x48]
    // 0xb8742c: add             x0, x2, #1
    // 0xb87430: lsl             x1, x0, #1
    // 0xb87434: StoreField: r5->field_b = r1
    //     0xb87434: stur            w1, [x5, #0xb]
    // 0xb87438: LoadField: r1 = r5->field_f
    //     0xb87438: ldur            w1, [x5, #0xf]
    // 0xb8743c: DecompressPointer r1
    //     0xb8743c: add             x1, x1, HEAP, lsl #32
    // 0xb87440: ldur            x0, [fp, #-0x20]
    // 0xb87444: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb87444: add             x25, x1, x2, lsl #2
    //     0xb87448: add             x25, x25, #0xf
    //     0xb8744c: str             w0, [x25]
    //     0xb87450: tbz             w0, #0, #0xb8746c
    //     0xb87454: ldurb           w16, [x1, #-1]
    //     0xb87458: ldurb           w17, [x0, #-1]
    //     0xb8745c: and             x16, x17, x16, lsr #2
    //     0xb87460: tst             x16, HEAP, lsr #32
    //     0xb87464: b.eq            #0xb8746c
    //     0xb87468: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8746c: mov             x2, x5
    // 0xb87470: b               #0xb875e8
    // 0xb87474: mov             x5, x3
    // 0xb87478: r3 = 55.000000
    //     0xb87478: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb8747c: ldr             x3, [x3, #0x9b8]
    // 0xb87480: cmp             w4, NULL
    // 0xb87484: b.ne            #0xb87494
    // 0xb87488: ldur            x6, [fp, #-8]
    // 0xb8748c: r0 = Null
    //     0xb8748c: mov             x0, NULL
    // 0xb87490: b               #0xb874e8
    // 0xb87494: ldur            x6, [fp, #-8]
    // 0xb87498: LoadField: r2 = r6->field_1f
    //     0xb87498: ldur            x2, [x6, #0x1f]
    // 0xb8749c: LoadField: r0 = r4->field_b
    //     0xb8749c: ldur            w0, [x4, #0xb]
    // 0xb874a0: r1 = LoadInt32Instr(r0)
    //     0xb874a0: sbfx            x1, x0, #1, #0x1f
    // 0xb874a4: mov             x0, x1
    // 0xb874a8: mov             x1, x2
    // 0xb874ac: cmp             x1, x0
    // 0xb874b0: b.hs            #0xb87900
    // 0xb874b4: LoadField: r0 = r4->field_f
    //     0xb874b4: ldur            w0, [x4, #0xf]
    // 0xb874b8: DecompressPointer r0
    //     0xb874b8: add             x0, x0, HEAP, lsl #32
    // 0xb874bc: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb874bc: add             x16, x0, x2, lsl #2
    //     0xb874c0: ldur            w1, [x16, #0xf]
    // 0xb874c4: DecompressPointer r1
    //     0xb874c4: add             x1, x1, HEAP, lsl #32
    // 0xb874c8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb874c8: ldur            w0, [x1, #0x17]
    // 0xb874cc: DecompressPointer r0
    //     0xb874cc: add             x0, x0, HEAP, lsl #32
    // 0xb874d0: cmp             w0, NULL
    // 0xb874d4: b.ne            #0xb874e0
    // 0xb874d8: r0 = Null
    //     0xb874d8: mov             x0, NULL
    // 0xb874dc: b               #0xb874e8
    // 0xb874e0: LoadField: r1 = r0->field_b
    //     0xb874e0: ldur            w1, [x0, #0xb]
    // 0xb874e4: mov             x0, x1
    // 0xb874e8: cmp             w0, NULL
    // 0xb874ec: b.ne            #0xb874f8
    // 0xb874f0: r0 = 0
    //     0xb874f0: movz            x0, #0
    // 0xb874f4: b               #0xb87500
    // 0xb874f8: r1 = LoadInt32Instr(r0)
    //     0xb874f8: sbfx            x1, x0, #1, #0x1f
    // 0xb874fc: mov             x0, x1
    // 0xb87500: lsl             x4, x0, #1
    // 0xb87504: ldur            x2, [fp, #-0x18]
    // 0xb87508: stur            x4, [fp, #-0x20]
    // 0xb8750c: r1 = Function '<anonymous closure>':.
    //     0xb8750c: add             x1, PP, #0x55, lsl #12  ; [pp+0x555a0] AnonymousClosure: (0xa6093c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb87510: ldr             x1, [x1, #0x5a0]
    // 0xb87514: r0 = AllocateClosure()
    //     0xb87514: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb87518: stur            x0, [fp, #-0x28]
    // 0xb8751c: r0 = ListView()
    //     0xb8751c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb87520: stur            x0, [fp, #-0x30]
    // 0xb87524: r16 = Instance_Axis
    //     0xb87524: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb87528: str             x16, [SP]
    // 0xb8752c: mov             x1, x0
    // 0xb87530: ldur            x2, [fp, #-0x28]
    // 0xb87534: ldur            x3, [fp, #-0x20]
    // 0xb87538: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb87538: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb8753c: ldr             x4, [x4, #0x4a0]
    // 0xb87540: r0 = ListView.builder()
    //     0xb87540: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb87544: r0 = SizedBox()
    //     0xb87544: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb87548: mov             x2, x0
    // 0xb8754c: r0 = inf
    //     0xb8754c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb87550: ldr             x0, [x0, #0x9f8]
    // 0xb87554: stur            x2, [fp, #-0x20]
    // 0xb87558: StoreField: r2->field_f = r0
    //     0xb87558: stur            w0, [x2, #0xf]
    // 0xb8755c: r1 = 55.000000
    //     0xb8755c: add             x1, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb87560: ldr             x1, [x1, #0x9b8]
    // 0xb87564: StoreField: r2->field_13 = r1
    //     0xb87564: stur            w1, [x2, #0x13]
    // 0xb87568: ldur            x1, [fp, #-0x30]
    // 0xb8756c: StoreField: r2->field_b = r1
    //     0xb8756c: stur            w1, [x2, #0xb]
    // 0xb87570: ldur            x3, [fp, #-0x10]
    // 0xb87574: LoadField: r1 = r3->field_b
    //     0xb87574: ldur            w1, [x3, #0xb]
    // 0xb87578: LoadField: r4 = r3->field_f
    //     0xb87578: ldur            w4, [x3, #0xf]
    // 0xb8757c: DecompressPointer r4
    //     0xb8757c: add             x4, x4, HEAP, lsl #32
    // 0xb87580: LoadField: r5 = r4->field_b
    //     0xb87580: ldur            w5, [x4, #0xb]
    // 0xb87584: r4 = LoadInt32Instr(r1)
    //     0xb87584: sbfx            x4, x1, #1, #0x1f
    // 0xb87588: stur            x4, [fp, #-0x48]
    // 0xb8758c: r1 = LoadInt32Instr(r5)
    //     0xb8758c: sbfx            x1, x5, #1, #0x1f
    // 0xb87590: cmp             x4, x1
    // 0xb87594: b.ne            #0xb875a0
    // 0xb87598: mov             x1, x3
    // 0xb8759c: r0 = _growToNextCapacity()
    //     0xb8759c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb875a0: ldur            x2, [fp, #-0x10]
    // 0xb875a4: ldur            x3, [fp, #-0x48]
    // 0xb875a8: add             x0, x3, #1
    // 0xb875ac: lsl             x1, x0, #1
    // 0xb875b0: StoreField: r2->field_b = r1
    //     0xb875b0: stur            w1, [x2, #0xb]
    // 0xb875b4: LoadField: r1 = r2->field_f
    //     0xb875b4: ldur            w1, [x2, #0xf]
    // 0xb875b8: DecompressPointer r1
    //     0xb875b8: add             x1, x1, HEAP, lsl #32
    // 0xb875bc: ldur            x0, [fp, #-0x20]
    // 0xb875c0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb875c0: add             x25, x1, x3, lsl #2
    //     0xb875c4: add             x25, x25, #0xf
    //     0xb875c8: str             w0, [x25]
    //     0xb875cc: tbz             w0, #0, #0xb875e8
    //     0xb875d0: ldurb           w16, [x1, #-1]
    //     0xb875d4: ldurb           w17, [x0, #-1]
    //     0xb875d8: and             x16, x17, x16, lsr #2
    //     0xb875dc: tst             x16, HEAP, lsr #32
    //     0xb875e0: b.eq            #0xb875e8
    //     0xb875e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb875e8: ldur            x0, [fp, #-8]
    // 0xb875ec: ldur            x1, [fp, #-0x18]
    // 0xb875f0: r16 = <EdgeInsets>
    //     0xb875f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb875f4: ldr             x16, [x16, #0xda0]
    // 0xb875f8: r30 = Instance_EdgeInsets
    //     0xb875f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb875fc: ldr             lr, [lr, #0x1f0]
    // 0xb87600: stp             lr, x16, [SP]
    // 0xb87604: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb87604: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb87608: r0 = all()
    //     0xb87608: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb8760c: ldur            x2, [fp, #-0x18]
    // 0xb87610: stur            x0, [fp, #-0x20]
    // 0xb87614: LoadField: r1 = r2->field_13
    //     0xb87614: ldur            w1, [x2, #0x13]
    // 0xb87618: DecompressPointer r1
    //     0xb87618: add             x1, x1, HEAP, lsl #32
    // 0xb8761c: r0 = of()
    //     0xb8761c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb87620: LoadField: r1 = r0->field_5b
    //     0xb87620: ldur            w1, [x0, #0x5b]
    // 0xb87624: DecompressPointer r1
    //     0xb87624: add             x1, x1, HEAP, lsl #32
    // 0xb87628: r16 = <Color>
    //     0xb87628: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb8762c: ldr             x16, [x16, #0xf80]
    // 0xb87630: stp             x1, x16, [SP]
    // 0xb87634: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb87634: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb87638: r0 = all()
    //     0xb87638: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb8763c: stur            x0, [fp, #-0x28]
    // 0xb87640: r16 = <RoundedRectangleBorder>
    //     0xb87640: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb87644: ldr             x16, [x16, #0xf78]
    // 0xb87648: r30 = Instance_RoundedRectangleBorder
    //     0xb87648: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb8764c: ldr             lr, [lr, #0x888]
    // 0xb87650: stp             lr, x16, [SP]
    // 0xb87654: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb87654: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb87658: r0 = all()
    //     0xb87658: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb8765c: stur            x0, [fp, #-0x30]
    // 0xb87660: r0 = ButtonStyle()
    //     0xb87660: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb87664: mov             x1, x0
    // 0xb87668: ldur            x0, [fp, #-0x28]
    // 0xb8766c: stur            x1, [fp, #-0x38]
    // 0xb87670: StoreField: r1->field_b = r0
    //     0xb87670: stur            w0, [x1, #0xb]
    // 0xb87674: ldur            x0, [fp, #-0x20]
    // 0xb87678: StoreField: r1->field_23 = r0
    //     0xb87678: stur            w0, [x1, #0x23]
    // 0xb8767c: ldur            x0, [fp, #-0x30]
    // 0xb87680: StoreField: r1->field_43 = r0
    //     0xb87680: stur            w0, [x1, #0x43]
    // 0xb87684: r0 = TextButtonThemeData()
    //     0xb87684: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb87688: mov             x2, x0
    // 0xb8768c: ldur            x0, [fp, #-0x38]
    // 0xb87690: stur            x2, [fp, #-0x20]
    // 0xb87694: StoreField: r2->field_7 = r0
    //     0xb87694: stur            w0, [x2, #7]
    // 0xb87698: ldur            x0, [fp, #-8]
    // 0xb8769c: LoadField: r1 = r0->field_b
    //     0xb8769c: ldur            w1, [x0, #0xb]
    // 0xb876a0: DecompressPointer r1
    //     0xb876a0: add             x1, x1, HEAP, lsl #32
    // 0xb876a4: cmp             w1, NULL
    // 0xb876a8: b.eq            #0xb87904
    // 0xb876ac: LoadField: r0 = r1->field_b
    //     0xb876ac: ldur            w0, [x1, #0xb]
    // 0xb876b0: DecompressPointer r0
    //     0xb876b0: add             x0, x0, HEAP, lsl #32
    // 0xb876b4: LoadField: r1 = r0->field_f
    //     0xb876b4: ldur            w1, [x0, #0xf]
    // 0xb876b8: DecompressPointer r1
    //     0xb876b8: add             x1, x1, HEAP, lsl #32
    // 0xb876bc: cmp             w1, NULL
    // 0xb876c0: b.ne            #0xb876c8
    // 0xb876c4: r1 = ""
    //     0xb876c4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb876c8: ldur            x0, [fp, #-0x18]
    // 0xb876cc: ldur            x3, [fp, #-0x10]
    // 0xb876d0: r0 = capitalizeFirstWord()
    //     0xb876d0: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb876d4: ldur            x2, [fp, #-0x18]
    // 0xb876d8: stur            x0, [fp, #-8]
    // 0xb876dc: LoadField: r1 = r2->field_13
    //     0xb876dc: ldur            w1, [x2, #0x13]
    // 0xb876e0: DecompressPointer r1
    //     0xb876e0: add             x1, x1, HEAP, lsl #32
    // 0xb876e4: r0 = of()
    //     0xb876e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb876e8: LoadField: r1 = r0->field_87
    //     0xb876e8: ldur            w1, [x0, #0x87]
    // 0xb876ec: DecompressPointer r1
    //     0xb876ec: add             x1, x1, HEAP, lsl #32
    // 0xb876f0: LoadField: r0 = r1->field_7
    //     0xb876f0: ldur            w0, [x1, #7]
    // 0xb876f4: DecompressPointer r0
    //     0xb876f4: add             x0, x0, HEAP, lsl #32
    // 0xb876f8: r16 = 16.000000
    //     0xb876f8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb876fc: ldr             x16, [x16, #0x188]
    // 0xb87700: r30 = Instance_Color
    //     0xb87700: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb87704: stp             lr, x16, [SP]
    // 0xb87708: mov             x1, x0
    // 0xb8770c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8770c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb87710: ldr             x4, [x4, #0xaa0]
    // 0xb87714: r0 = copyWith()
    //     0xb87714: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb87718: stur            x0, [fp, #-0x28]
    // 0xb8771c: r0 = Text()
    //     0xb8771c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb87720: mov             x3, x0
    // 0xb87724: ldur            x0, [fp, #-8]
    // 0xb87728: stur            x3, [fp, #-0x30]
    // 0xb8772c: StoreField: r3->field_b = r0
    //     0xb8772c: stur            w0, [x3, #0xb]
    // 0xb87730: ldur            x0, [fp, #-0x28]
    // 0xb87734: StoreField: r3->field_13 = r0
    //     0xb87734: stur            w0, [x3, #0x13]
    // 0xb87738: ldur            x2, [fp, #-0x18]
    // 0xb8773c: r1 = Function '<anonymous closure>':.
    //     0xb8773c: add             x1, PP, #0x55, lsl #12  ; [pp+0x555a8] AnonymousClosure: (0xb87908), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb87740: ldr             x1, [x1, #0x5a8]
    // 0xb87744: r0 = AllocateClosure()
    //     0xb87744: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb87748: stur            x0, [fp, #-8]
    // 0xb8774c: r0 = TextButton()
    //     0xb8774c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb87750: mov             x1, x0
    // 0xb87754: ldur            x0, [fp, #-8]
    // 0xb87758: stur            x1, [fp, #-0x18]
    // 0xb8775c: StoreField: r1->field_b = r0
    //     0xb8775c: stur            w0, [x1, #0xb]
    // 0xb87760: r0 = false
    //     0xb87760: add             x0, NULL, #0x30  ; false
    // 0xb87764: StoreField: r1->field_27 = r0
    //     0xb87764: stur            w0, [x1, #0x27]
    // 0xb87768: r0 = true
    //     0xb87768: add             x0, NULL, #0x20  ; true
    // 0xb8776c: StoreField: r1->field_2f = r0
    //     0xb8776c: stur            w0, [x1, #0x2f]
    // 0xb87770: ldur            x0, [fp, #-0x30]
    // 0xb87774: StoreField: r1->field_37 = r0
    //     0xb87774: stur            w0, [x1, #0x37]
    // 0xb87778: r0 = Instance_ValueKey
    //     0xb87778: add             x0, PP, #0x40, lsl #12  ; [pp+0x40038] Obj!ValueKey<String>@d5b321
    //     0xb8777c: ldr             x0, [x0, #0x38]
    // 0xb87780: StoreField: r1->field_7 = r0
    //     0xb87780: stur            w0, [x1, #7]
    // 0xb87784: r0 = TextButtonTheme()
    //     0xb87784: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb87788: mov             x1, x0
    // 0xb8778c: ldur            x0, [fp, #-0x20]
    // 0xb87790: stur            x1, [fp, #-8]
    // 0xb87794: StoreField: r1->field_f = r0
    //     0xb87794: stur            w0, [x1, #0xf]
    // 0xb87798: ldur            x0, [fp, #-0x18]
    // 0xb8779c: StoreField: r1->field_b = r0
    //     0xb8779c: stur            w0, [x1, #0xb]
    // 0xb877a0: r0 = SizedBox()
    //     0xb877a0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb877a4: mov             x1, x0
    // 0xb877a8: r0 = inf
    //     0xb877a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb877ac: ldr             x0, [x0, #0x9f8]
    // 0xb877b0: stur            x1, [fp, #-0x18]
    // 0xb877b4: StoreField: r1->field_f = r0
    //     0xb877b4: stur            w0, [x1, #0xf]
    // 0xb877b8: ldur            x0, [fp, #-8]
    // 0xb877bc: StoreField: r1->field_b = r0
    //     0xb877bc: stur            w0, [x1, #0xb]
    // 0xb877c0: r0 = Padding()
    //     0xb877c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb877c4: mov             x2, x0
    // 0xb877c8: r0 = Instance_EdgeInsets
    //     0xb877c8: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xb877cc: ldr             x0, [x0, #0xf48]
    // 0xb877d0: stur            x2, [fp, #-8]
    // 0xb877d4: StoreField: r2->field_f = r0
    //     0xb877d4: stur            w0, [x2, #0xf]
    // 0xb877d8: ldur            x0, [fp, #-0x18]
    // 0xb877dc: StoreField: r2->field_b = r0
    //     0xb877dc: stur            w0, [x2, #0xb]
    // 0xb877e0: ldur            x0, [fp, #-0x10]
    // 0xb877e4: LoadField: r1 = r0->field_b
    //     0xb877e4: ldur            w1, [x0, #0xb]
    // 0xb877e8: LoadField: r3 = r0->field_f
    //     0xb877e8: ldur            w3, [x0, #0xf]
    // 0xb877ec: DecompressPointer r3
    //     0xb877ec: add             x3, x3, HEAP, lsl #32
    // 0xb877f0: LoadField: r4 = r3->field_b
    //     0xb877f0: ldur            w4, [x3, #0xb]
    // 0xb877f4: r3 = LoadInt32Instr(r1)
    //     0xb877f4: sbfx            x3, x1, #1, #0x1f
    // 0xb877f8: stur            x3, [fp, #-0x48]
    // 0xb877fc: r1 = LoadInt32Instr(r4)
    //     0xb877fc: sbfx            x1, x4, #1, #0x1f
    // 0xb87800: cmp             x3, x1
    // 0xb87804: b.ne            #0xb87810
    // 0xb87808: mov             x1, x0
    // 0xb8780c: r0 = _growToNextCapacity()
    //     0xb8780c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb87810: ldur            x2, [fp, #-0x10]
    // 0xb87814: ldur            x3, [fp, #-0x48]
    // 0xb87818: add             x0, x3, #1
    // 0xb8781c: lsl             x1, x0, #1
    // 0xb87820: StoreField: r2->field_b = r1
    //     0xb87820: stur            w1, [x2, #0xb]
    // 0xb87824: LoadField: r1 = r2->field_f
    //     0xb87824: ldur            w1, [x2, #0xf]
    // 0xb87828: DecompressPointer r1
    //     0xb87828: add             x1, x1, HEAP, lsl #32
    // 0xb8782c: ldur            x0, [fp, #-8]
    // 0xb87830: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb87830: add             x25, x1, x3, lsl #2
    //     0xb87834: add             x25, x25, #0xf
    //     0xb87838: str             w0, [x25]
    //     0xb8783c: tbz             w0, #0, #0xb87858
    //     0xb87840: ldurb           w16, [x1, #-1]
    //     0xb87844: ldurb           w17, [x0, #-1]
    //     0xb87848: and             x16, x17, x16, lsr #2
    //     0xb8784c: tst             x16, HEAP, lsr #32
    //     0xb87850: b.eq            #0xb87858
    //     0xb87854: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb87858: r0 = Column()
    //     0xb87858: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8785c: mov             x1, x0
    // 0xb87860: r0 = Instance_Axis
    //     0xb87860: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb87864: stur            x1, [fp, #-8]
    // 0xb87868: StoreField: r1->field_f = r0
    //     0xb87868: stur            w0, [x1, #0xf]
    // 0xb8786c: r0 = Instance_MainAxisAlignment
    //     0xb8786c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb87870: ldr             x0, [x0, #0xa08]
    // 0xb87874: StoreField: r1->field_13 = r0
    //     0xb87874: stur            w0, [x1, #0x13]
    // 0xb87878: r0 = Instance_MainAxisSize
    //     0xb87878: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb8787c: ldr             x0, [x0, #0xdd0]
    // 0xb87880: ArrayStore: r1[0] = r0  ; List_4
    //     0xb87880: stur            w0, [x1, #0x17]
    // 0xb87884: r0 = Instance_CrossAxisAlignment
    //     0xb87884: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb87888: ldr             x0, [x0, #0x890]
    // 0xb8788c: StoreField: r1->field_1b = r0
    //     0xb8788c: stur            w0, [x1, #0x1b]
    // 0xb87890: r0 = Instance_VerticalDirection
    //     0xb87890: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb87894: ldr             x0, [x0, #0xa20]
    // 0xb87898: StoreField: r1->field_23 = r0
    //     0xb87898: stur            w0, [x1, #0x23]
    // 0xb8789c: r0 = Instance_Clip
    //     0xb8789c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb878a0: ldr             x0, [x0, #0x38]
    // 0xb878a4: StoreField: r1->field_2b = r0
    //     0xb878a4: stur            w0, [x1, #0x2b]
    // 0xb878a8: StoreField: r1->field_2f = rZR
    //     0xb878a8: stur            xzr, [x1, #0x2f]
    // 0xb878ac: ldur            x0, [fp, #-0x10]
    // 0xb878b0: StoreField: r1->field_b = r0
    //     0xb878b0: stur            w0, [x1, #0xb]
    // 0xb878b4: r0 = Container()
    //     0xb878b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb878b8: stur            x0, [fp, #-0x10]
    // 0xb878bc: r16 = Instance_BoxDecoration
    //     0xb878bc: add             x16, PP, #0x55, lsl #12  ; [pp+0x555b0] Obj!BoxDecoration@d64b91
    //     0xb878c0: ldr             x16, [x16, #0x5b0]
    // 0xb878c4: ldur            lr, [fp, #-8]
    // 0xb878c8: stp             lr, x16, [SP]
    // 0xb878cc: mov             x1, x0
    // 0xb878d0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb878d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb878d4: ldr             x4, [x4, #0x88]
    // 0xb878d8: r0 = Container()
    //     0xb878d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb878dc: ldur            x0, [fp, #-0x10]
    // 0xb878e0: LeaveFrame
    //     0xb878e0: mov             SP, fp
    //     0xb878e4: ldp             fp, lr, [SP], #0x10
    // 0xb878e8: ret
    //     0xb878e8: ret             
    // 0xb878ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb878ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb878f0: b               #0xb86d38
    // 0xb878f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb878f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb878f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb878f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb878fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb878fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87900: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb87900: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb87904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87904: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb87908, size: 0x364
    // 0xb87908: EnterFrame
    //     0xb87908: stp             fp, lr, [SP, #-0x10]!
    //     0xb8790c: mov             fp, SP
    // 0xb87910: AllocStack(0x50)
    //     0xb87910: sub             SP, SP, #0x50
    // 0xb87914: SetupParameters()
    //     0xb87914: ldr             x0, [fp, #0x10]
    //     0xb87918: ldur            w3, [x0, #0x17]
    //     0xb8791c: add             x3, x3, HEAP, lsl #32
    //     0xb87920: stur            x3, [fp, #-8]
    // 0xb87924: CheckStackOverflow
    //     0xb87924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb87928: cmp             SP, x16
    //     0xb8792c: b.ls            #0xb87c48
    // 0xb87930: LoadField: r0 = r3->field_f
    //     0xb87930: ldur            w0, [x3, #0xf]
    // 0xb87934: DecompressPointer r0
    //     0xb87934: add             x0, x0, HEAP, lsl #32
    // 0xb87938: LoadField: r1 = r0->field_b
    //     0xb87938: ldur            w1, [x0, #0xb]
    // 0xb8793c: DecompressPointer r1
    //     0xb8793c: add             x1, x1, HEAP, lsl #32
    // 0xb87940: cmp             w1, NULL
    // 0xb87944: b.eq            #0xb87c50
    // 0xb87948: LoadField: r0 = r1->field_b
    //     0xb87948: ldur            w0, [x1, #0xb]
    // 0xb8794c: DecompressPointer r0
    //     0xb8794c: add             x0, x0, HEAP, lsl #32
    // 0xb87950: LoadField: r1 = r0->field_d7
    //     0xb87950: ldur            w1, [x0, #0xd7]
    // 0xb87954: DecompressPointer r1
    //     0xb87954: add             x1, x1, HEAP, lsl #32
    // 0xb87958: cmp             w1, NULL
    // 0xb8795c: b.ne            #0xb87978
    // 0xb87960: r1 = <AllSkuDatum>
    //     0xb87960: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xb87964: ldr             x1, [x1, #0xea8]
    // 0xb87968: r2 = 0
    //     0xb87968: movz            x2, #0
    // 0xb8796c: r0 = _GrowableList()
    //     0xb8796c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb87970: mov             x3, x0
    // 0xb87974: b               #0xb8797c
    // 0xb87978: mov             x3, x1
    // 0xb8797c: ldur            x0, [fp, #-8]
    // 0xb87980: stur            x3, [fp, #-0x10]
    // 0xb87984: LoadField: r1 = r0->field_f
    //     0xb87984: ldur            w1, [x0, #0xf]
    // 0xb87988: DecompressPointer r1
    //     0xb87988: add             x1, x1, HEAP, lsl #32
    // 0xb8798c: LoadField: r2 = r1->field_b
    //     0xb8798c: ldur            w2, [x1, #0xb]
    // 0xb87990: DecompressPointer r2
    //     0xb87990: add             x2, x2, HEAP, lsl #32
    // 0xb87994: cmp             w2, NULL
    // 0xb87998: b.eq            #0xb87c54
    // 0xb8799c: LoadField: r1 = r2->field_b
    //     0xb8799c: ldur            w1, [x2, #0xb]
    // 0xb879a0: DecompressPointer r1
    //     0xb879a0: add             x1, x1, HEAP, lsl #32
    // 0xb879a4: LoadField: r2 = r1->field_db
    //     0xb879a4: ldur            w2, [x1, #0xdb]
    // 0xb879a8: DecompressPointer r2
    //     0xb879a8: add             x2, x2, HEAP, lsl #32
    // 0xb879ac: cmp             w2, NULL
    // 0xb879b0: b.ne            #0xb879cc
    // 0xb879b4: r1 = <AllGroupedSkusDatum>
    //     0xb879b4: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f28] TypeArguments: <AllGroupedSkusDatum>
    //     0xb879b8: ldr             x1, [x1, #0xf28]
    // 0xb879bc: r2 = 0
    //     0xb879bc: movz            x2, #0
    // 0xb879c0: r0 = _GrowableList()
    //     0xb879c0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb879c4: mov             x3, x0
    // 0xb879c8: b               #0xb879d0
    // 0xb879cc: mov             x3, x2
    // 0xb879d0: ldur            x2, [fp, #-0x10]
    // 0xb879d4: LoadField: r0 = r2->field_b
    //     0xb879d4: ldur            w0, [x2, #0xb]
    // 0xb879d8: r1 = LoadInt32Instr(r0)
    //     0xb879d8: sbfx            x1, x0, #1, #0x1f
    // 0xb879dc: cbz             x1, #0xb879f8
    // 0xb879e0: ldur            x0, [fp, #-8]
    // 0xb879e4: LoadField: r4 = r0->field_f
    //     0xb879e4: ldur            w4, [x0, #0xf]
    // 0xb879e8: DecompressPointer r4
    //     0xb879e8: add             x4, x4, HEAP, lsl #32
    // 0xb879ec: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xb879ec: ldur            x5, [x4, #0x17]
    // 0xb879f0: cmp             x5, x1
    // 0xb879f4: b.lt            #0xb87a08
    // 0xb879f8: r0 = Null
    //     0xb879f8: mov             x0, NULL
    // 0xb879fc: LeaveFrame
    //     0xb879fc: mov             SP, fp
    //     0xb87a00: ldp             fp, lr, [SP], #0x10
    // 0xb87a04: ret
    //     0xb87a04: ret             
    // 0xb87a08: mov             x0, x1
    // 0xb87a0c: mov             x1, x5
    // 0xb87a10: cmp             x1, x0
    // 0xb87a14: b.hs            #0xb87c58
    // 0xb87a18: LoadField: r0 = r2->field_f
    //     0xb87a18: ldur            w0, [x2, #0xf]
    // 0xb87a1c: DecompressPointer r0
    //     0xb87a1c: add             x0, x0, HEAP, lsl #32
    // 0xb87a20: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb87a20: add             x16, x0, x5, lsl #2
    //     0xb87a24: ldur            w1, [x16, #0xf]
    // 0xb87a28: DecompressPointer r1
    //     0xb87a28: add             x1, x1, HEAP, lsl #32
    // 0xb87a2c: LoadField: r0 = r1->field_f
    //     0xb87a2c: ldur            w0, [x1, #0xf]
    // 0xb87a30: DecompressPointer r0
    //     0xb87a30: add             x0, x0, HEAP, lsl #32
    // 0xb87a34: cmp             w0, NULL
    // 0xb87a38: b.ne            #0xb87a44
    // 0xb87a3c: r2 = ""
    //     0xb87a3c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87a40: b               #0xb87a48
    // 0xb87a44: mov             x2, x0
    // 0xb87a48: LoadField: r0 = r1->field_b
    //     0xb87a48: ldur            w0, [x1, #0xb]
    // 0xb87a4c: DecompressPointer r0
    //     0xb87a4c: add             x0, x0, HEAP, lsl #32
    // 0xb87a50: cmp             w0, NULL
    // 0xb87a54: b.ne            #0xb87a60
    // 0xb87a58: r6 = ""
    //     0xb87a58: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87a5c: b               #0xb87a64
    // 0xb87a60: mov             x6, x0
    // 0xb87a64: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb87a64: ldur            w0, [x1, #0x17]
    // 0xb87a68: DecompressPointer r0
    //     0xb87a68: add             x0, x0, HEAP, lsl #32
    // 0xb87a6c: cmp             w0, NULL
    // 0xb87a70: b.ne            #0xb87a7c
    // 0xb87a74: r7 = 0
    //     0xb87a74: movz            x7, #0
    // 0xb87a78: b               #0xb87a8c
    // 0xb87a7c: r1 = LoadInt32Instr(r0)
    //     0xb87a7c: sbfx            x1, x0, #1, #0x1f
    //     0xb87a80: tbz             w0, #0, #0xb87a88
    //     0xb87a84: ldur            x1, [x0, #7]
    // 0xb87a88: mov             x7, x1
    // 0xb87a8c: LoadField: r0 = r3->field_b
    //     0xb87a8c: ldur            w0, [x3, #0xb]
    // 0xb87a90: r1 = LoadInt32Instr(r0)
    //     0xb87a90: sbfx            x1, x0, #1, #0x1f
    // 0xb87a94: cbz             x1, #0xb87bb8
    // 0xb87a98: LoadField: r8 = r4->field_1f
    //     0xb87a98: ldur            x8, [x4, #0x1f]
    // 0xb87a9c: cmp             x8, x1
    // 0xb87aa0: b.ge            #0xb87bb8
    // 0xb87aa4: mov             x0, x1
    // 0xb87aa8: mov             x1, x8
    // 0xb87aac: cmp             x1, x0
    // 0xb87ab0: b.hs            #0xb87c5c
    // 0xb87ab4: LoadField: r0 = r3->field_f
    //     0xb87ab4: ldur            w0, [x3, #0xf]
    // 0xb87ab8: DecompressPointer r0
    //     0xb87ab8: add             x0, x0, HEAP, lsl #32
    // 0xb87abc: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb87abc: add             x16, x0, x8, lsl #2
    //     0xb87ac0: ldur            w1, [x16, #0xf]
    // 0xb87ac4: DecompressPointer r1
    //     0xb87ac4: add             x1, x1, HEAP, lsl #32
    // 0xb87ac8: LoadField: r0 = r1->field_b
    //     0xb87ac8: ldur            w0, [x1, #0xb]
    // 0xb87acc: DecompressPointer r0
    //     0xb87acc: add             x0, x0, HEAP, lsl #32
    // 0xb87ad0: cmp             w0, NULL
    // 0xb87ad4: b.ne            #0xb87ae0
    // 0xb87ad8: r3 = ""
    //     0xb87ad8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87adc: b               #0xb87ae4
    // 0xb87ae0: mov             x3, x0
    // 0xb87ae4: LoadField: r0 = r1->field_7
    //     0xb87ae4: ldur            w0, [x1, #7]
    // 0xb87ae8: DecompressPointer r0
    //     0xb87ae8: add             x0, x0, HEAP, lsl #32
    // 0xb87aec: cmp             w0, NULL
    // 0xb87af0: b.ne            #0xb87afc
    // 0xb87af4: r8 = ""
    //     0xb87af4: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87af8: b               #0xb87b00
    // 0xb87afc: mov             x8, x0
    // 0xb87b00: ArrayLoad: r9 = r1[0]  ; List_4
    //     0xb87b00: ldur            w9, [x1, #0x17]
    // 0xb87b04: DecompressPointer r9
    //     0xb87b04: add             x9, x9, HEAP, lsl #32
    // 0xb87b08: cmp             w9, NULL
    // 0xb87b0c: b.ne            #0xb87b18
    // 0xb87b10: r0 = Null
    //     0xb87b10: mov             x0, NULL
    // 0xb87b14: b               #0xb87b30
    // 0xb87b18: LoadField: r0 = r9->field_b
    //     0xb87b18: ldur            w0, [x9, #0xb]
    // 0xb87b1c: cbnz            w0, #0xb87b28
    // 0xb87b20: r1 = false
    //     0xb87b20: add             x1, NULL, #0x30  ; false
    // 0xb87b24: b               #0xb87b2c
    // 0xb87b28: r1 = true
    //     0xb87b28: add             x1, NULL, #0x20  ; true
    // 0xb87b2c: mov             x0, x1
    // 0xb87b30: cmp             w0, NULL
    // 0xb87b34: b.eq            #0xb87ba4
    // 0xb87b38: tbnz            w0, #4, #0xb87ba4
    // 0xb87b3c: cmp             w9, NULL
    // 0xb87b40: b.eq            #0xb87c60
    // 0xb87b44: LoadField: r0 = r9->field_b
    //     0xb87b44: ldur            w0, [x9, #0xb]
    // 0xb87b48: r1 = LoadInt32Instr(r0)
    //     0xb87b48: sbfx            x1, x0, #1, #0x1f
    // 0xb87b4c: cmp             x5, x1
    // 0xb87b50: b.ge            #0xb87ba4
    // 0xb87b54: mov             x0, x1
    // 0xb87b58: mov             x1, x5
    // 0xb87b5c: cmp             x1, x0
    // 0xb87b60: b.hs            #0xb87c64
    // 0xb87b64: LoadField: r0 = r9->field_f
    //     0xb87b64: ldur            w0, [x9, #0xf]
    // 0xb87b68: DecompressPointer r0
    //     0xb87b68: add             x0, x0, HEAP, lsl #32
    // 0xb87b6c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb87b6c: add             x16, x0, x5, lsl #2
    //     0xb87b70: ldur            w1, [x16, #0xf]
    // 0xb87b74: DecompressPointer r1
    //     0xb87b74: add             x1, x1, HEAP, lsl #32
    // 0xb87b78: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb87b78: ldur            w0, [x1, #0x17]
    // 0xb87b7c: DecompressPointer r0
    //     0xb87b7c: add             x0, x0, HEAP, lsl #32
    // 0xb87b80: cmp             w0, NULL
    // 0xb87b84: b.ne            #0xb87b90
    // 0xb87b88: r0 = 0
    //     0xb87b88: movz            x0, #0
    // 0xb87b8c: b               #0xb87ba8
    // 0xb87b90: r1 = LoadInt32Instr(r0)
    //     0xb87b90: sbfx            x1, x0, #1, #0x1f
    //     0xb87b94: tbz             w0, #0, #0xb87b9c
    //     0xb87b98: ldur            x1, [x0, #7]
    // 0xb87b9c: mov             x0, x1
    // 0xb87ba0: b               #0xb87ba8
    // 0xb87ba4: r0 = 0
    //     0xb87ba4: movz            x0, #0
    // 0xb87ba8: mov             x5, x8
    // 0xb87bac: mov             x8, x3
    // 0xb87bb0: mov             x3, x0
    // 0xb87bb4: b               #0xb87bc4
    // 0xb87bb8: r8 = ""
    //     0xb87bb8: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87bbc: r5 = ""
    //     0xb87bbc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87bc0: r3 = 0
    //     0xb87bc0: movz            x3, #0
    // 0xb87bc4: LoadField: r0 = r4->field_b
    //     0xb87bc4: ldur            w0, [x4, #0xb]
    // 0xb87bc8: DecompressPointer r0
    //     0xb87bc8: add             x0, x0, HEAP, lsl #32
    // 0xb87bcc: cmp             w0, NULL
    // 0xb87bd0: b.eq            #0xb87c68
    // 0xb87bd4: LoadField: r4 = r0->field_b
    //     0xb87bd4: ldur            w4, [x0, #0xb]
    // 0xb87bd8: DecompressPointer r4
    //     0xb87bd8: add             x4, x4, HEAP, lsl #32
    // 0xb87bdc: LoadField: r9 = r0->field_13
    //     0xb87bdc: ldur            w9, [x0, #0x13]
    // 0xb87be0: DecompressPointer r9
    //     0xb87be0: add             x9, x9, HEAP, lsl #32
    // 0xb87be4: r0 = BoxInt64Instr(r7)
    //     0xb87be4: sbfiz           x0, x7, #1, #0x1f
    //     0xb87be8: cmp             x7, x0, asr #1
    //     0xb87bec: b.eq            #0xb87bf8
    //     0xb87bf0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb87bf4: stur            x7, [x0, #7]
    // 0xb87bf8: mov             x7, x0
    // 0xb87bfc: r0 = BoxInt64Instr(r3)
    //     0xb87bfc: sbfiz           x0, x3, #1, #0x1f
    //     0xb87c00: cmp             x3, x0, asr #1
    //     0xb87c04: b.eq            #0xb87c10
    //     0xb87c08: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb87c0c: stur            x3, [x0, #7]
    // 0xb87c10: stp             x2, x9, [SP, #0x30]
    // 0xb87c14: stp             x7, x6, [SP, #0x20]
    // 0xb87c18: stp             x8, x4, [SP, #0x10]
    // 0xb87c1c: stp             x0, x5, [SP]
    // 0xb87c20: r4 = 0
    //     0xb87c20: movz            x4, #0
    // 0xb87c24: ldr             x0, [SP, #0x38]
    // 0xb87c28: r16 = UnlinkedCall_0x613b5c
    //     0xb87c28: add             x16, PP, #0x55, lsl #12  ; [pp+0x555b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb87c2c: add             x16, x16, #0x5b8
    // 0xb87c30: ldp             x5, lr, [x16]
    // 0xb87c34: blr             lr
    // 0xb87c38: r0 = Null
    //     0xb87c38: mov             x0, NULL
    // 0xb87c3c: LeaveFrame
    //     0xb87c3c: mov             SP, fp
    //     0xb87c40: ldp             fp, lr, [SP], #0x10
    // 0xb87c44: ret
    //     0xb87c44: ret             
    // 0xb87c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb87c48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb87c4c: b               #0xb87930
    // 0xb87c50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87c50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87c58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb87c58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb87c5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb87c5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb87c60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87c60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb87c64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb87c64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb87c68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb87c68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb87c6c, size: 0x7d8
    // 0xb87c6c: EnterFrame
    //     0xb87c6c: stp             fp, lr, [SP, #-0x10]!
    //     0xb87c70: mov             fp, SP
    // 0xb87c74: AllocStack(0x60)
    //     0xb87c74: sub             SP, SP, #0x60
    // 0xb87c78: SetupParameters()
    //     0xb87c78: ldr             x0, [fp, #0x20]
    //     0xb87c7c: ldur            w1, [x0, #0x17]
    //     0xb87c80: add             x1, x1, HEAP, lsl #32
    //     0xb87c84: stur            x1, [fp, #-8]
    // 0xb87c88: CheckStackOverflow
    //     0xb87c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb87c8c: cmp             SP, x16
    //     0xb87c90: b.ls            #0xb8841c
    // 0xb87c94: r1 = 1
    //     0xb87c94: movz            x1, #0x1
    // 0xb87c98: r0 = AllocateContext()
    //     0xb87c98: bl              #0x16f6108  ; AllocateContextStub
    // 0xb87c9c: mov             x4, x0
    // 0xb87ca0: ldur            x3, [fp, #-8]
    // 0xb87ca4: stur            x4, [fp, #-0x10]
    // 0xb87ca8: StoreField: r4->field_b = r3
    //     0xb87ca8: stur            w3, [x4, #0xb]
    // 0xb87cac: ldr             x5, [fp, #0x10]
    // 0xb87cb0: StoreField: r4->field_f = r5
    //     0xb87cb0: stur            w5, [x4, #0xf]
    // 0xb87cb4: LoadField: r0 = r3->field_f
    //     0xb87cb4: ldur            w0, [x3, #0xf]
    // 0xb87cb8: DecompressPointer r0
    //     0xb87cb8: add             x0, x0, HEAP, lsl #32
    // 0xb87cbc: LoadField: r2 = r0->field_27
    //     0xb87cbc: ldur            w2, [x0, #0x27]
    // 0xb87cc0: DecompressPointer r2
    //     0xb87cc0: add             x2, x2, HEAP, lsl #32
    // 0xb87cc4: LoadField: r1 = r0->field_b
    //     0xb87cc4: ldur            w1, [x0, #0xb]
    // 0xb87cc8: DecompressPointer r1
    //     0xb87cc8: add             x1, x1, HEAP, lsl #32
    // 0xb87ccc: cmp             w1, NULL
    // 0xb87cd0: b.eq            #0xb88424
    // 0xb87cd4: LoadField: r0 = r1->field_b
    //     0xb87cd4: ldur            w0, [x1, #0xb]
    // 0xb87cd8: DecompressPointer r0
    //     0xb87cd8: add             x0, x0, HEAP, lsl #32
    // 0xb87cdc: LoadField: r6 = r0->field_db
    //     0xb87cdc: ldur            w6, [x0, #0xdb]
    // 0xb87ce0: DecompressPointer r6
    //     0xb87ce0: add             x6, x6, HEAP, lsl #32
    // 0xb87ce4: cmp             w6, NULL
    // 0xb87ce8: b.ne            #0xb87cf4
    // 0xb87cec: r0 = Null
    //     0xb87cec: mov             x0, NULL
    // 0xb87cf0: b               #0xb87d34
    // 0xb87cf4: LoadField: r0 = r6->field_b
    //     0xb87cf4: ldur            w0, [x6, #0xb]
    // 0xb87cf8: r7 = LoadInt32Instr(r5)
    //     0xb87cf8: sbfx            x7, x5, #1, #0x1f
    //     0xb87cfc: tbz             w5, #0, #0xb87d04
    //     0xb87d00: ldur            x7, [x5, #7]
    // 0xb87d04: r1 = LoadInt32Instr(r0)
    //     0xb87d04: sbfx            x1, x0, #1, #0x1f
    // 0xb87d08: mov             x0, x1
    // 0xb87d0c: mov             x1, x7
    // 0xb87d10: cmp             x1, x0
    // 0xb87d14: b.hs            #0xb88428
    // 0xb87d18: LoadField: r0 = r6->field_f
    //     0xb87d18: ldur            w0, [x6, #0xf]
    // 0xb87d1c: DecompressPointer r0
    //     0xb87d1c: add             x0, x0, HEAP, lsl #32
    // 0xb87d20: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb87d20: add             x16, x0, x7, lsl #2
    //     0xb87d24: ldur            w1, [x16, #0xf]
    // 0xb87d28: DecompressPointer r1
    //     0xb87d28: add             x1, x1, HEAP, lsl #32
    // 0xb87d2c: LoadField: r0 = r1->field_7
    //     0xb87d2c: ldur            w0, [x1, #7]
    // 0xb87d30: DecompressPointer r0
    //     0xb87d30: add             x0, x0, HEAP, lsl #32
    // 0xb87d34: cmp             w0, NULL
    // 0xb87d38: b.ne            #0xb87d40
    // 0xb87d3c: r0 = ""
    //     0xb87d3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87d40: mov             x1, x2
    // 0xb87d44: mov             x2, x0
    // 0xb87d48: r0 = contains()
    //     0xb87d48: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb87d4c: tbnz            w0, #4, #0xb87de0
    // 0xb87d50: ldr             x1, [fp, #0x18]
    // 0xb87d54: r0 = of()
    //     0xb87d54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb87d58: LoadField: r2 = r0->field_5b
    //     0xb87d58: ldur            w2, [x0, #0x5b]
    // 0xb87d5c: DecompressPointer r2
    //     0xb87d5c: add             x2, x2, HEAP, lsl #32
    // 0xb87d60: r16 = 2.000000
    //     0xb87d60: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xb87d64: ldr             x16, [x16, #0xdf8]
    // 0xb87d68: str             x16, [SP]
    // 0xb87d6c: r1 = Null
    //     0xb87d6c: mov             x1, NULL
    // 0xb87d70: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb87d70: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb87d74: ldr             x4, [x4, #0x108]
    // 0xb87d78: r0 = Border.all()
    //     0xb87d78: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb87d7c: stur            x0, [fp, #-0x18]
    // 0xb87d80: r0 = Radius()
    //     0xb87d80: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb87d84: d0 = 10.000000
    //     0xb87d84: fmov            d0, #10.00000000
    // 0xb87d88: stur            x0, [fp, #-0x20]
    // 0xb87d8c: StoreField: r0->field_7 = d0
    //     0xb87d8c: stur            d0, [x0, #7]
    // 0xb87d90: StoreField: r0->field_f = d0
    //     0xb87d90: stur            d0, [x0, #0xf]
    // 0xb87d94: r0 = BorderRadius()
    //     0xb87d94: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb87d98: mov             x1, x0
    // 0xb87d9c: ldur            x0, [fp, #-0x20]
    // 0xb87da0: stur            x1, [fp, #-0x28]
    // 0xb87da4: StoreField: r1->field_7 = r0
    //     0xb87da4: stur            w0, [x1, #7]
    // 0xb87da8: StoreField: r1->field_b = r0
    //     0xb87da8: stur            w0, [x1, #0xb]
    // 0xb87dac: StoreField: r1->field_f = r0
    //     0xb87dac: stur            w0, [x1, #0xf]
    // 0xb87db0: StoreField: r1->field_13 = r0
    //     0xb87db0: stur            w0, [x1, #0x13]
    // 0xb87db4: r0 = BoxDecoration()
    //     0xb87db4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb87db8: mov             x1, x0
    // 0xb87dbc: ldur            x0, [fp, #-0x18]
    // 0xb87dc0: StoreField: r1->field_f = r0
    //     0xb87dc0: stur            w0, [x1, #0xf]
    // 0xb87dc4: ldur            x0, [fp, #-0x28]
    // 0xb87dc8: StoreField: r1->field_13 = r0
    //     0xb87dc8: stur            w0, [x1, #0x13]
    // 0xb87dcc: r0 = Instance_BoxShape
    //     0xb87dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb87dd0: ldr             x0, [x0, #0x80]
    // 0xb87dd4: StoreField: r1->field_23 = r0
    //     0xb87dd4: stur            w0, [x1, #0x23]
    // 0xb87dd8: mov             x2, x1
    // 0xb87ddc: b               #0xb87e5c
    // 0xb87de0: r0 = Instance_BoxShape
    //     0xb87de0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb87de4: ldr             x0, [x0, #0x80]
    // 0xb87de8: r1 = Null
    //     0xb87de8: mov             x1, NULL
    // 0xb87dec: r2 = Instance_Color
    //     0xb87dec: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb87df0: ldr             x2, [x2, #0xf88]
    // 0xb87df4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb87df4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb87df8: r0 = Border.all()
    //     0xb87df8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb87dfc: stur            x0, [fp, #-0x18]
    // 0xb87e00: r0 = Radius()
    //     0xb87e00: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb87e04: d0 = 10.000000
    //     0xb87e04: fmov            d0, #10.00000000
    // 0xb87e08: stur            x0, [fp, #-0x20]
    // 0xb87e0c: StoreField: r0->field_7 = d0
    //     0xb87e0c: stur            d0, [x0, #7]
    // 0xb87e10: StoreField: r0->field_f = d0
    //     0xb87e10: stur            d0, [x0, #0xf]
    // 0xb87e14: r0 = BorderRadius()
    //     0xb87e14: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb87e18: mov             x1, x0
    // 0xb87e1c: ldur            x0, [fp, #-0x20]
    // 0xb87e20: stur            x1, [fp, #-0x28]
    // 0xb87e24: StoreField: r1->field_7 = r0
    //     0xb87e24: stur            w0, [x1, #7]
    // 0xb87e28: StoreField: r1->field_b = r0
    //     0xb87e28: stur            w0, [x1, #0xb]
    // 0xb87e2c: StoreField: r1->field_f = r0
    //     0xb87e2c: stur            w0, [x1, #0xf]
    // 0xb87e30: StoreField: r1->field_13 = r0
    //     0xb87e30: stur            w0, [x1, #0x13]
    // 0xb87e34: r0 = BoxDecoration()
    //     0xb87e34: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb87e38: mov             x1, x0
    // 0xb87e3c: ldur            x0, [fp, #-0x18]
    // 0xb87e40: StoreField: r1->field_f = r0
    //     0xb87e40: stur            w0, [x1, #0xf]
    // 0xb87e44: ldur            x0, [fp, #-0x28]
    // 0xb87e48: StoreField: r1->field_13 = r0
    //     0xb87e48: stur            w0, [x1, #0x13]
    // 0xb87e4c: r0 = Instance_BoxShape
    //     0xb87e4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb87e50: ldr             x0, [x0, #0x80]
    // 0xb87e54: StoreField: r1->field_23 = r0
    //     0xb87e54: stur            w0, [x1, #0x23]
    // 0xb87e58: mov             x2, x1
    // 0xb87e5c: ldur            x1, [fp, #-8]
    // 0xb87e60: stur            x2, [fp, #-0x18]
    // 0xb87e64: r0 = Radius()
    //     0xb87e64: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb87e68: d0 = 10.000000
    //     0xb87e68: fmov            d0, #10.00000000
    // 0xb87e6c: stur            x0, [fp, #-0x20]
    // 0xb87e70: StoreField: r0->field_7 = d0
    //     0xb87e70: stur            d0, [x0, #7]
    // 0xb87e74: StoreField: r0->field_f = d0
    //     0xb87e74: stur            d0, [x0, #0xf]
    // 0xb87e78: r0 = BorderRadius()
    //     0xb87e78: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb87e7c: mov             x1, x0
    // 0xb87e80: ldur            x0, [fp, #-0x20]
    // 0xb87e84: stur            x1, [fp, #-0x28]
    // 0xb87e88: StoreField: r1->field_7 = r0
    //     0xb87e88: stur            w0, [x1, #7]
    // 0xb87e8c: StoreField: r1->field_b = r0
    //     0xb87e8c: stur            w0, [x1, #0xb]
    // 0xb87e90: StoreField: r1->field_f = r0
    //     0xb87e90: stur            w0, [x1, #0xf]
    // 0xb87e94: StoreField: r1->field_13 = r0
    //     0xb87e94: stur            w0, [x1, #0x13]
    // 0xb87e98: r0 = RoundedRectangleBorder()
    //     0xb87e98: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb87e9c: mov             x3, x0
    // 0xb87ea0: ldur            x0, [fp, #-0x28]
    // 0xb87ea4: stur            x3, [fp, #-0x30]
    // 0xb87ea8: StoreField: r3->field_b = r0
    //     0xb87ea8: stur            w0, [x3, #0xb]
    // 0xb87eac: r0 = Instance_BorderSide
    //     0xb87eac: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb87eb0: ldr             x0, [x0, #0xe20]
    // 0xb87eb4: StoreField: r3->field_7 = r0
    //     0xb87eb4: stur            w0, [x3, #7]
    // 0xb87eb8: ldur            x4, [fp, #-8]
    // 0xb87ebc: LoadField: r0 = r4->field_f
    //     0xb87ebc: ldur            w0, [x4, #0xf]
    // 0xb87ec0: DecompressPointer r0
    //     0xb87ec0: add             x0, x0, HEAP, lsl #32
    // 0xb87ec4: LoadField: r1 = r0->field_b
    //     0xb87ec4: ldur            w1, [x0, #0xb]
    // 0xb87ec8: DecompressPointer r1
    //     0xb87ec8: add             x1, x1, HEAP, lsl #32
    // 0xb87ecc: cmp             w1, NULL
    // 0xb87ed0: b.eq            #0xb8842c
    // 0xb87ed4: LoadField: r0 = r1->field_b
    //     0xb87ed4: ldur            w0, [x1, #0xb]
    // 0xb87ed8: DecompressPointer r0
    //     0xb87ed8: add             x0, x0, HEAP, lsl #32
    // 0xb87edc: LoadField: r2 = r0->field_db
    //     0xb87edc: ldur            w2, [x0, #0xdb]
    // 0xb87ee0: DecompressPointer r2
    //     0xb87ee0: add             x2, x2, HEAP, lsl #32
    // 0xb87ee4: cmp             w2, NULL
    // 0xb87ee8: b.ne            #0xb87ef8
    // 0xb87eec: ldr             x5, [fp, #0x10]
    // 0xb87ef0: r0 = Null
    //     0xb87ef0: mov             x0, NULL
    // 0xb87ef4: b               #0xb87f3c
    // 0xb87ef8: ldr             x5, [fp, #0x10]
    // 0xb87efc: LoadField: r0 = r2->field_b
    //     0xb87efc: ldur            w0, [x2, #0xb]
    // 0xb87f00: r6 = LoadInt32Instr(r5)
    //     0xb87f00: sbfx            x6, x5, #1, #0x1f
    //     0xb87f04: tbz             w5, #0, #0xb87f0c
    //     0xb87f08: ldur            x6, [x5, #7]
    // 0xb87f0c: r1 = LoadInt32Instr(r0)
    //     0xb87f0c: sbfx            x1, x0, #1, #0x1f
    // 0xb87f10: mov             x0, x1
    // 0xb87f14: mov             x1, x6
    // 0xb87f18: cmp             x1, x0
    // 0xb87f1c: b.hs            #0xb88430
    // 0xb87f20: LoadField: r0 = r2->field_f
    //     0xb87f20: ldur            w0, [x2, #0xf]
    // 0xb87f24: DecompressPointer r0
    //     0xb87f24: add             x0, x0, HEAP, lsl #32
    // 0xb87f28: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb87f28: add             x16, x0, x6, lsl #2
    //     0xb87f2c: ldur            w1, [x16, #0xf]
    // 0xb87f30: DecompressPointer r1
    //     0xb87f30: add             x1, x1, HEAP, lsl #32
    // 0xb87f34: LoadField: r0 = r1->field_f
    //     0xb87f34: ldur            w0, [x1, #0xf]
    // 0xb87f38: DecompressPointer r0
    //     0xb87f38: add             x0, x0, HEAP, lsl #32
    // 0xb87f3c: cmp             w0, NULL
    // 0xb87f40: b.ne            #0xb87f48
    // 0xb87f44: r0 = ""
    //     0xb87f44: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb87f48: stur            x0, [fp, #-0x20]
    // 0xb87f4c: r1 = Function '<anonymous closure>':.
    //     0xb87f4c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55628] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb87f50: ldr             x1, [x1, #0x628]
    // 0xb87f54: r2 = Null
    //     0xb87f54: mov             x2, NULL
    // 0xb87f58: r0 = AllocateClosure()
    //     0xb87f58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb87f5c: r1 = Function '<anonymous closure>':.
    //     0xb87f5c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55630] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb87f60: ldr             x1, [x1, #0x630]
    // 0xb87f64: r2 = Null
    //     0xb87f64: mov             x2, NULL
    // 0xb87f68: stur            x0, [fp, #-0x28]
    // 0xb87f6c: r0 = AllocateClosure()
    //     0xb87f6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb87f70: stur            x0, [fp, #-0x38]
    // 0xb87f74: r0 = CachedNetworkImage()
    //     0xb87f74: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb87f78: stur            x0, [fp, #-0x40]
    // 0xb87f7c: r16 = Instance_BoxFit
    //     0xb87f7c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb87f80: ldr             x16, [x16, #0xb18]
    // 0xb87f84: ldur            lr, [fp, #-0x28]
    // 0xb87f88: stp             lr, x16, [SP, #8]
    // 0xb87f8c: ldur            x16, [fp, #-0x38]
    // 0xb87f90: str             x16, [SP]
    // 0xb87f94: mov             x1, x0
    // 0xb87f98: ldur            x2, [fp, #-0x20]
    // 0xb87f9c: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb87f9c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb87fa0: ldr             x4, [x4, #0x638]
    // 0xb87fa4: r0 = CachedNetworkImage()
    //     0xb87fa4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb87fa8: r0 = Card()
    //     0xb87fa8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb87fac: mov             x1, x0
    // 0xb87fb0: r0 = 0.000000
    //     0xb87fb0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb87fb4: stur            x1, [fp, #-0x20]
    // 0xb87fb8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb87fb8: stur            w0, [x1, #0x17]
    // 0xb87fbc: ldur            x0, [fp, #-0x30]
    // 0xb87fc0: StoreField: r1->field_1b = r0
    //     0xb87fc0: stur            w0, [x1, #0x1b]
    // 0xb87fc4: r0 = true
    //     0xb87fc4: add             x0, NULL, #0x20  ; true
    // 0xb87fc8: StoreField: r1->field_1f = r0
    //     0xb87fc8: stur            w0, [x1, #0x1f]
    // 0xb87fcc: r2 = Instance_EdgeInsets
    //     0xb87fcc: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb87fd0: StoreField: r1->field_27 = r2
    //     0xb87fd0: stur            w2, [x1, #0x27]
    // 0xb87fd4: r2 = Instance_Clip
    //     0xb87fd4: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb87fd8: ldr             x2, [x2, #0xb50]
    // 0xb87fdc: StoreField: r1->field_23 = r2
    //     0xb87fdc: stur            w2, [x1, #0x23]
    // 0xb87fe0: ldur            x2, [fp, #-0x40]
    // 0xb87fe4: StoreField: r1->field_2f = r2
    //     0xb87fe4: stur            w2, [x1, #0x2f]
    // 0xb87fe8: StoreField: r1->field_2b = r0
    //     0xb87fe8: stur            w0, [x1, #0x2b]
    // 0xb87fec: r2 = Instance__CardVariant
    //     0xb87fec: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb87ff0: ldr             x2, [x2, #0xa68]
    // 0xb87ff4: StoreField: r1->field_33 = r2
    //     0xb87ff4: stur            w2, [x1, #0x33]
    // 0xb87ff8: r0 = AspectRatio()
    //     0xb87ff8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb87ffc: d0 = 0.500000
    //     0xb87ffc: fmov            d0, #0.50000000
    // 0xb88000: stur            x0, [fp, #-0x28]
    // 0xb88004: StoreField: r0->field_f = d0
    //     0xb88004: stur            d0, [x0, #0xf]
    // 0xb88008: ldur            x1, [fp, #-0x20]
    // 0xb8800c: StoreField: r0->field_b = r1
    //     0xb8800c: stur            w1, [x0, #0xb]
    // 0xb88010: r0 = Container()
    //     0xb88010: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb88014: stur            x0, [fp, #-0x20]
    // 0xb88018: r16 = 80.000000
    //     0xb88018: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb8801c: ldr             x16, [x16, #0x2f8]
    // 0xb88020: r30 = 80.000000
    //     0xb88020: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb88024: ldr             lr, [lr, #0x2f8]
    // 0xb88028: stp             lr, x16, [SP, #0x10]
    // 0xb8802c: ldur            x16, [fp, #-0x18]
    // 0xb88030: ldur            lr, [fp, #-0x28]
    // 0xb88034: stp             lr, x16, [SP]
    // 0xb88038: mov             x1, x0
    // 0xb8803c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb8803c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb88040: ldr             x4, [x4, #0x870]
    // 0xb88044: r0 = Container()
    //     0xb88044: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb88048: ldur            x0, [fp, #-8]
    // 0xb8804c: LoadField: r1 = r0->field_f
    //     0xb8804c: ldur            w1, [x0, #0xf]
    // 0xb88050: DecompressPointer r1
    //     0xb88050: add             x1, x1, HEAP, lsl #32
    // 0xb88054: LoadField: r2 = r1->field_b
    //     0xb88054: ldur            w2, [x1, #0xb]
    // 0xb88058: DecompressPointer r2
    //     0xb88058: add             x2, x2, HEAP, lsl #32
    // 0xb8805c: cmp             w2, NULL
    // 0xb88060: b.eq            #0xb88434
    // 0xb88064: LoadField: r0 = r2->field_b
    //     0xb88064: ldur            w0, [x2, #0xb]
    // 0xb88068: DecompressPointer r0
    //     0xb88068: add             x0, x0, HEAP, lsl #32
    // 0xb8806c: LoadField: r3 = r0->field_db
    //     0xb8806c: ldur            w3, [x0, #0xdb]
    // 0xb88070: DecompressPointer r3
    //     0xb88070: add             x3, x3, HEAP, lsl #32
    // 0xb88074: cmp             w3, NULL
    // 0xb88078: b.ne            #0xb88088
    // 0xb8807c: ldr             x4, [fp, #0x10]
    // 0xb88080: r0 = Null
    //     0xb88080: mov             x0, NULL
    // 0xb88084: b               #0xb880cc
    // 0xb88088: ldr             x4, [fp, #0x10]
    // 0xb8808c: LoadField: r0 = r3->field_b
    //     0xb8808c: ldur            w0, [x3, #0xb]
    // 0xb88090: r5 = LoadInt32Instr(r4)
    //     0xb88090: sbfx            x5, x4, #1, #0x1f
    //     0xb88094: tbz             w4, #0, #0xb8809c
    //     0xb88098: ldur            x5, [x4, #7]
    // 0xb8809c: r1 = LoadInt32Instr(r0)
    //     0xb8809c: sbfx            x1, x0, #1, #0x1f
    // 0xb880a0: mov             x0, x1
    // 0xb880a4: mov             x1, x5
    // 0xb880a8: cmp             x1, x0
    // 0xb880ac: b.hs            #0xb88438
    // 0xb880b0: LoadField: r0 = r3->field_f
    //     0xb880b0: ldur            w0, [x3, #0xf]
    // 0xb880b4: DecompressPointer r0
    //     0xb880b4: add             x0, x0, HEAP, lsl #32
    // 0xb880b8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb880b8: add             x16, x0, x5, lsl #2
    //     0xb880bc: ldur            w1, [x16, #0xf]
    // 0xb880c0: DecompressPointer r1
    //     0xb880c0: add             x1, x1, HEAP, lsl #32
    // 0xb880c4: LoadField: r0 = r1->field_13
    //     0xb880c4: ldur            w0, [x1, #0x13]
    // 0xb880c8: DecompressPointer r0
    //     0xb880c8: add             x0, x0, HEAP, lsl #32
    // 0xb880cc: cmp             w0, NULL
    // 0xb880d0: r16 = true
    //     0xb880d0: add             x16, NULL, #0x20  ; true
    // 0xb880d4: r17 = false
    //     0xb880d4: add             x17, NULL, #0x30  ; false
    // 0xb880d8: csel            x3, x16, x17, ne
    // 0xb880dc: stur            x3, [fp, #-8]
    // 0xb880e0: LoadField: r0 = r2->field_b
    //     0xb880e0: ldur            w0, [x2, #0xb]
    // 0xb880e4: DecompressPointer r0
    //     0xb880e4: add             x0, x0, HEAP, lsl #32
    // 0xb880e8: LoadField: r5 = r0->field_db
    //     0xb880e8: ldur            w5, [x0, #0xdb]
    // 0xb880ec: DecompressPointer r5
    //     0xb880ec: add             x5, x5, HEAP, lsl #32
    // 0xb880f0: cmp             w5, NULL
    // 0xb880f4: b.ne            #0xb88100
    // 0xb880f8: r0 = Null
    //     0xb880f8: mov             x0, NULL
    // 0xb880fc: b               #0xb88164
    // 0xb88100: LoadField: r0 = r5->field_b
    //     0xb88100: ldur            w0, [x5, #0xb]
    // 0xb88104: r6 = LoadInt32Instr(r4)
    //     0xb88104: sbfx            x6, x4, #1, #0x1f
    //     0xb88108: tbz             w4, #0, #0xb88110
    //     0xb8810c: ldur            x6, [x4, #7]
    // 0xb88110: r1 = LoadInt32Instr(r0)
    //     0xb88110: sbfx            x1, x0, #1, #0x1f
    // 0xb88114: mov             x0, x1
    // 0xb88118: mov             x1, x6
    // 0xb8811c: cmp             x1, x0
    // 0xb88120: b.hs            #0xb8843c
    // 0xb88124: LoadField: r0 = r5->field_f
    //     0xb88124: ldur            w0, [x5, #0xf]
    // 0xb88128: DecompressPointer r0
    //     0xb88128: add             x0, x0, HEAP, lsl #32
    // 0xb8812c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb8812c: add             x16, x0, x6, lsl #2
    //     0xb88130: ldur            w1, [x16, #0xf]
    // 0xb88134: DecompressPointer r1
    //     0xb88134: add             x1, x1, HEAP, lsl #32
    // 0xb88138: LoadField: r0 = r1->field_13
    //     0xb88138: ldur            w0, [x1, #0x13]
    // 0xb8813c: DecompressPointer r0
    //     0xb8813c: add             x0, x0, HEAP, lsl #32
    // 0xb88140: cmp             w0, NULL
    // 0xb88144: b.ne            #0xb88150
    // 0xb88148: r0 = Null
    //     0xb88148: mov             x0, NULL
    // 0xb8814c: b               #0xb88164
    // 0xb88150: LoadField: r1 = r0->field_7
    //     0xb88150: ldur            w1, [x0, #7]
    // 0xb88154: cbnz            w1, #0xb88160
    // 0xb88158: r0 = false
    //     0xb88158: add             x0, NULL, #0x30  ; false
    // 0xb8815c: b               #0xb88164
    // 0xb88160: r0 = true
    //     0xb88160: add             x0, NULL, #0x20  ; true
    // 0xb88164: cmp             w0, NULL
    // 0xb88168: b.eq            #0xb881f8
    // 0xb8816c: tbnz            w0, #4, #0xb881f8
    // 0xb88170: LoadField: r0 = r2->field_b
    //     0xb88170: ldur            w0, [x2, #0xb]
    // 0xb88174: DecompressPointer r0
    //     0xb88174: add             x0, x0, HEAP, lsl #32
    // 0xb88178: LoadField: r2 = r0->field_db
    //     0xb88178: ldur            w2, [x0, #0xdb]
    // 0xb8817c: DecompressPointer r2
    //     0xb8817c: add             x2, x2, HEAP, lsl #32
    // 0xb88180: cmp             w2, NULL
    // 0xb88184: b.ne            #0xb88190
    // 0xb88188: r0 = Null
    //     0xb88188: mov             x0, NULL
    // 0xb8818c: b               #0xb881e8
    // 0xb88190: LoadField: r0 = r2->field_b
    //     0xb88190: ldur            w0, [x2, #0xb]
    // 0xb88194: r5 = LoadInt32Instr(r4)
    //     0xb88194: sbfx            x5, x4, #1, #0x1f
    //     0xb88198: tbz             w4, #0, #0xb881a0
    //     0xb8819c: ldur            x5, [x4, #7]
    // 0xb881a0: r1 = LoadInt32Instr(r0)
    //     0xb881a0: sbfx            x1, x0, #1, #0x1f
    // 0xb881a4: mov             x0, x1
    // 0xb881a8: mov             x1, x5
    // 0xb881ac: cmp             x1, x0
    // 0xb881b0: b.hs            #0xb88440
    // 0xb881b4: LoadField: r0 = r2->field_f
    //     0xb881b4: ldur            w0, [x2, #0xf]
    // 0xb881b8: DecompressPointer r0
    //     0xb881b8: add             x0, x0, HEAP, lsl #32
    // 0xb881bc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb881bc: add             x16, x0, x5, lsl #2
    //     0xb881c0: ldur            w1, [x16, #0xf]
    // 0xb881c4: DecompressPointer r1
    //     0xb881c4: add             x1, x1, HEAP, lsl #32
    // 0xb881c8: LoadField: r0 = r1->field_13
    //     0xb881c8: ldur            w0, [x1, #0x13]
    // 0xb881cc: DecompressPointer r0
    //     0xb881cc: add             x0, x0, HEAP, lsl #32
    // 0xb881d0: cmp             w0, NULL
    // 0xb881d4: b.ne            #0xb881e0
    // 0xb881d8: r0 = Null
    //     0xb881d8: mov             x0, NULL
    // 0xb881dc: b               #0xb881e8
    // 0xb881e0: mov             x1, x0
    // 0xb881e4: r0 = StringExtension.toTitleCase()
    //     0xb881e4: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xb881e8: str             x0, [SP]
    // 0xb881ec: r0 = _interpolateSingle()
    //     0xb881ec: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb881f0: mov             x3, x0
    // 0xb881f4: b               #0xb881fc
    // 0xb881f8: r3 = ""
    //     0xb881f8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb881fc: ldur            x2, [fp, #-0x20]
    // 0xb88200: ldur            x0, [fp, #-8]
    // 0xb88204: ldr             x1, [fp, #0x18]
    // 0xb88208: stur            x3, [fp, #-0x18]
    // 0xb8820c: r0 = of()
    //     0xb8820c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb88210: LoadField: r1 = r0->field_87
    //     0xb88210: ldur            w1, [x0, #0x87]
    // 0xb88214: DecompressPointer r1
    //     0xb88214: add             x1, x1, HEAP, lsl #32
    // 0xb88218: LoadField: r0 = r1->field_2b
    //     0xb88218: ldur            w0, [x1, #0x2b]
    // 0xb8821c: DecompressPointer r0
    //     0xb8821c: add             x0, x0, HEAP, lsl #32
    // 0xb88220: stur            x0, [fp, #-0x28]
    // 0xb88224: r1 = Instance_Color
    //     0xb88224: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb88228: d0 = 0.700000
    //     0xb88228: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb8822c: ldr             d0, [x17, #0xf48]
    // 0xb88230: r0 = withOpacity()
    //     0xb88230: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb88234: r16 = 12.000000
    //     0xb88234: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb88238: ldr             x16, [x16, #0x9e8]
    // 0xb8823c: stp             x0, x16, [SP]
    // 0xb88240: ldur            x1, [fp, #-0x28]
    // 0xb88244: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb88244: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb88248: ldr             x4, [x4, #0xaa0]
    // 0xb8824c: r0 = copyWith()
    //     0xb8824c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb88250: stur            x0, [fp, #-0x28]
    // 0xb88254: r0 = Text()
    //     0xb88254: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb88258: mov             x1, x0
    // 0xb8825c: ldur            x0, [fp, #-0x18]
    // 0xb88260: stur            x1, [fp, #-0x30]
    // 0xb88264: StoreField: r1->field_b = r0
    //     0xb88264: stur            w0, [x1, #0xb]
    // 0xb88268: ldur            x0, [fp, #-0x28]
    // 0xb8826c: StoreField: r1->field_13 = r0
    //     0xb8826c: stur            w0, [x1, #0x13]
    // 0xb88270: r0 = Padding()
    //     0xb88270: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb88274: mov             x2, x0
    // 0xb88278: r0 = Instance_EdgeInsets
    //     0xb88278: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xb8827c: ldr             x0, [x0, #0x9e0]
    // 0xb88280: stur            x2, [fp, #-0x18]
    // 0xb88284: StoreField: r2->field_f = r0
    //     0xb88284: stur            w0, [x2, #0xf]
    // 0xb88288: ldur            x0, [fp, #-0x30]
    // 0xb8828c: StoreField: r2->field_b = r0
    //     0xb8828c: stur            w0, [x2, #0xb]
    // 0xb88290: r1 = <FlexParentData>
    //     0xb88290: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb88294: ldr             x1, [x1, #0xe00]
    // 0xb88298: r0 = Expanded()
    //     0xb88298: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb8829c: mov             x1, x0
    // 0xb882a0: r0 = 1
    //     0xb882a0: movz            x0, #0x1
    // 0xb882a4: stur            x1, [fp, #-0x28]
    // 0xb882a8: StoreField: r1->field_13 = r0
    //     0xb882a8: stur            x0, [x1, #0x13]
    // 0xb882ac: r0 = Instance_FlexFit
    //     0xb882ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb882b0: ldr             x0, [x0, #0xe08]
    // 0xb882b4: StoreField: r1->field_1b = r0
    //     0xb882b4: stur            w0, [x1, #0x1b]
    // 0xb882b8: ldur            x0, [fp, #-0x18]
    // 0xb882bc: StoreField: r1->field_b = r0
    //     0xb882bc: stur            w0, [x1, #0xb]
    // 0xb882c0: r0 = Visibility()
    //     0xb882c0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb882c4: mov             x3, x0
    // 0xb882c8: ldur            x0, [fp, #-0x28]
    // 0xb882cc: stur            x3, [fp, #-0x18]
    // 0xb882d0: StoreField: r3->field_b = r0
    //     0xb882d0: stur            w0, [x3, #0xb]
    // 0xb882d4: r0 = Instance_SizedBox
    //     0xb882d4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb882d8: StoreField: r3->field_f = r0
    //     0xb882d8: stur            w0, [x3, #0xf]
    // 0xb882dc: ldur            x0, [fp, #-8]
    // 0xb882e0: StoreField: r3->field_13 = r0
    //     0xb882e0: stur            w0, [x3, #0x13]
    // 0xb882e4: r0 = false
    //     0xb882e4: add             x0, NULL, #0x30  ; false
    // 0xb882e8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb882e8: stur            w0, [x3, #0x17]
    // 0xb882ec: StoreField: r3->field_1b = r0
    //     0xb882ec: stur            w0, [x3, #0x1b]
    // 0xb882f0: StoreField: r3->field_1f = r0
    //     0xb882f0: stur            w0, [x3, #0x1f]
    // 0xb882f4: StoreField: r3->field_23 = r0
    //     0xb882f4: stur            w0, [x3, #0x23]
    // 0xb882f8: StoreField: r3->field_27 = r0
    //     0xb882f8: stur            w0, [x3, #0x27]
    // 0xb882fc: StoreField: r3->field_2b = r0
    //     0xb882fc: stur            w0, [x3, #0x2b]
    // 0xb88300: r1 = Null
    //     0xb88300: mov             x1, NULL
    // 0xb88304: r2 = 4
    //     0xb88304: movz            x2, #0x4
    // 0xb88308: r0 = AllocateArray()
    //     0xb88308: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8830c: mov             x2, x0
    // 0xb88310: ldur            x0, [fp, #-0x20]
    // 0xb88314: stur            x2, [fp, #-8]
    // 0xb88318: StoreField: r2->field_f = r0
    //     0xb88318: stur            w0, [x2, #0xf]
    // 0xb8831c: ldur            x0, [fp, #-0x18]
    // 0xb88320: StoreField: r2->field_13 = r0
    //     0xb88320: stur            w0, [x2, #0x13]
    // 0xb88324: r1 = <Widget>
    //     0xb88324: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb88328: r0 = AllocateGrowableArray()
    //     0xb88328: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8832c: mov             x1, x0
    // 0xb88330: ldur            x0, [fp, #-8]
    // 0xb88334: stur            x1, [fp, #-0x18]
    // 0xb88338: StoreField: r1->field_f = r0
    //     0xb88338: stur            w0, [x1, #0xf]
    // 0xb8833c: r0 = 4
    //     0xb8833c: movz            x0, #0x4
    // 0xb88340: StoreField: r1->field_b = r0
    //     0xb88340: stur            w0, [x1, #0xb]
    // 0xb88344: r0 = Column()
    //     0xb88344: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb88348: mov             x1, x0
    // 0xb8834c: r0 = Instance_Axis
    //     0xb8834c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb88350: stur            x1, [fp, #-8]
    // 0xb88354: StoreField: r1->field_f = r0
    //     0xb88354: stur            w0, [x1, #0xf]
    // 0xb88358: r0 = Instance_MainAxisAlignment
    //     0xb88358: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8835c: ldr             x0, [x0, #0xa08]
    // 0xb88360: StoreField: r1->field_13 = r0
    //     0xb88360: stur            w0, [x1, #0x13]
    // 0xb88364: r0 = Instance_MainAxisSize
    //     0xb88364: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb88368: ldr             x0, [x0, #0xa10]
    // 0xb8836c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8836c: stur            w0, [x1, #0x17]
    // 0xb88370: r0 = Instance_CrossAxisAlignment
    //     0xb88370: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb88374: ldr             x0, [x0, #0xa18]
    // 0xb88378: StoreField: r1->field_1b = r0
    //     0xb88378: stur            w0, [x1, #0x1b]
    // 0xb8837c: r0 = Instance_VerticalDirection
    //     0xb8837c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb88380: ldr             x0, [x0, #0xa20]
    // 0xb88384: StoreField: r1->field_23 = r0
    //     0xb88384: stur            w0, [x1, #0x23]
    // 0xb88388: r0 = Instance_Clip
    //     0xb88388: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8838c: ldr             x0, [x0, #0x38]
    // 0xb88390: StoreField: r1->field_2b = r0
    //     0xb88390: stur            w0, [x1, #0x2b]
    // 0xb88394: StoreField: r1->field_2f = rZR
    //     0xb88394: stur            xzr, [x1, #0x2f]
    // 0xb88398: ldur            x0, [fp, #-0x18]
    // 0xb8839c: StoreField: r1->field_b = r0
    //     0xb8839c: stur            w0, [x1, #0xb]
    // 0xb883a0: r0 = InkWell()
    //     0xb883a0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb883a4: mov             x3, x0
    // 0xb883a8: ldur            x0, [fp, #-8]
    // 0xb883ac: stur            x3, [fp, #-0x18]
    // 0xb883b0: StoreField: r3->field_b = r0
    //     0xb883b0: stur            w0, [x3, #0xb]
    // 0xb883b4: ldur            x2, [fp, #-0x10]
    // 0xb883b8: r1 = Function '<anonymous closure>':.
    //     0xb883b8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55640] AnonymousClosure: (0xb88444), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb883bc: ldr             x1, [x1, #0x640]
    // 0xb883c0: r0 = AllocateClosure()
    //     0xb883c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb883c4: mov             x1, x0
    // 0xb883c8: ldur            x0, [fp, #-0x18]
    // 0xb883cc: StoreField: r0->field_f = r1
    //     0xb883cc: stur            w1, [x0, #0xf]
    // 0xb883d0: r1 = true
    //     0xb883d0: add             x1, NULL, #0x20  ; true
    // 0xb883d4: StoreField: r0->field_43 = r1
    //     0xb883d4: stur            w1, [x0, #0x43]
    // 0xb883d8: r2 = Instance_BoxShape
    //     0xb883d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb883dc: ldr             x2, [x2, #0x80]
    // 0xb883e0: StoreField: r0->field_47 = r2
    //     0xb883e0: stur            w2, [x0, #0x47]
    // 0xb883e4: StoreField: r0->field_6f = r1
    //     0xb883e4: stur            w1, [x0, #0x6f]
    // 0xb883e8: r2 = false
    //     0xb883e8: add             x2, NULL, #0x30  ; false
    // 0xb883ec: StoreField: r0->field_73 = r2
    //     0xb883ec: stur            w2, [x0, #0x73]
    // 0xb883f0: StoreField: r0->field_83 = r1
    //     0xb883f0: stur            w1, [x0, #0x83]
    // 0xb883f4: StoreField: r0->field_7b = r2
    //     0xb883f4: stur            w2, [x0, #0x7b]
    // 0xb883f8: r0 = Padding()
    //     0xb883f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb883fc: r1 = Instance_EdgeInsets
    //     0xb883fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb88400: ldr             x1, [x1, #0x980]
    // 0xb88404: StoreField: r0->field_f = r1
    //     0xb88404: stur            w1, [x0, #0xf]
    // 0xb88408: ldur            x1, [fp, #-0x18]
    // 0xb8840c: StoreField: r0->field_b = r1
    //     0xb8840c: stur            w1, [x0, #0xb]
    // 0xb88410: LeaveFrame
    //     0xb88410: mov             SP, fp
    //     0xb88414: ldp             fp, lr, [SP], #0x10
    // 0xb88418: ret
    //     0xb88418: ret             
    // 0xb8841c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8841c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb88420: b               #0xb87c94
    // 0xb88424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88428: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8842c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8842c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88430: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb88434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb88438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88438: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8843c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8843c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb88440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb88440: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb88444, size: 0x68
    // 0xb88444: EnterFrame
    //     0xb88444: stp             fp, lr, [SP, #-0x10]!
    //     0xb88448: mov             fp, SP
    // 0xb8844c: AllocStack(0x8)
    //     0xb8844c: sub             SP, SP, #8
    // 0xb88450: SetupParameters()
    //     0xb88450: ldr             x0, [fp, #0x10]
    //     0xb88454: ldur            w2, [x0, #0x17]
    //     0xb88458: add             x2, x2, HEAP, lsl #32
    // 0xb8845c: CheckStackOverflow
    //     0xb8845c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb88460: cmp             SP, x16
    //     0xb88464: b.ls            #0xb884a4
    // 0xb88468: LoadField: r0 = r2->field_b
    //     0xb88468: ldur            w0, [x2, #0xb]
    // 0xb8846c: DecompressPointer r0
    //     0xb8846c: add             x0, x0, HEAP, lsl #32
    // 0xb88470: LoadField: r3 = r0->field_f
    //     0xb88470: ldur            w3, [x0, #0xf]
    // 0xb88474: DecompressPointer r3
    //     0xb88474: add             x3, x3, HEAP, lsl #32
    // 0xb88478: stur            x3, [fp, #-8]
    // 0xb8847c: r1 = Function '<anonymous closure>':.
    //     0xb8847c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55648] AnonymousClosure: (0xa84cc8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb88480: ldr             x1, [x1, #0x648]
    // 0xb88484: r0 = AllocateClosure()
    //     0xb88484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb88488: ldur            x1, [fp, #-8]
    // 0xb8848c: mov             x2, x0
    // 0xb88490: r0 = setState()
    //     0xb88490: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb88494: r0 = Null
    //     0xb88494: mov             x0, NULL
    // 0xb88498: LeaveFrame
    //     0xb88498: mov             SP, fp
    //     0xb8849c: ldp             fp, lr, [SP], #0x10
    // 0xb884a0: ret
    //     0xb884a0: ret             
    // 0xb884a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb884a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb884a8: b               #0xb88468
  }
}

// class id: 4055, size: 0x18, field offset: 0xc
//   const constructor, 
class ProductSelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f8a4, size: 0x48
    // 0xc7f8a4: EnterFrame
    //     0xc7f8a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f8a8: mov             fp, SP
    // 0xc7f8ac: AllocStack(0x8)
    //     0xc7f8ac: sub             SP, SP, #8
    // 0xc7f8b0: CheckStackOverflow
    //     0xc7f8b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f8b4: cmp             SP, x16
    //     0xc7f8b8: b.ls            #0xc7f8e4
    // 0xc7f8bc: r1 = <ProductSelectSizeBottomSheet>
    //     0xc7f8bc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48750] TypeArguments: <ProductSelectSizeBottomSheet>
    //     0xc7f8c0: ldr             x1, [x1, #0x750]
    // 0xc7f8c4: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc7f8c4: bl              #0xc7f8ec  ; Allocate_ProductSelectSizeBottomSheetStateStub -> _ProductSelectSizeBottomSheetState (size=0x2c)
    // 0xc7f8c8: mov             x1, x0
    // 0xc7f8cc: stur            x0, [fp, #-8]
    // 0xc7f8d0: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc7f8d0: bl              #0xc7c3b4  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::_ProductSelectSizeBottomSheetState
    // 0xc7f8d4: ldur            x0, [fp, #-8]
    // 0xc7f8d8: LeaveFrame
    //     0xc7f8d8: mov             SP, fp
    //     0xc7f8dc: ldp             fp, lr, [SP], #0x10
    // 0xc7f8e0: ret
    //     0xc7f8e0: ret             
    // 0xc7f8e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f8e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f8e8: b               #0xc7f8bc
  }
}
