// lib: , url: package:customer_app/app/presentation/views/glass/bag/bottom_view.dart

// class id: 1049346, size: 0x8
class :: {
}

// class id: 3379, size: 0x14, field offset: 0x14
class _BottomViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb30868, size: 0x2234
    // 0xb30868: EnterFrame
    //     0xb30868: stp             fp, lr, [SP, #-0x10]!
    //     0xb3086c: mov             fp, SP
    // 0xb30870: AllocStack(0x88)
    //     0xb30870: sub             SP, SP, #0x88
    // 0xb30874: SetupParameters(_BottomViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb30874: mov             x0, x1
    //     0xb30878: stur            x1, [fp, #-8]
    //     0xb3087c: mov             x1, x2
    //     0xb30880: stur            x2, [fp, #-0x10]
    // 0xb30884: CheckStackOverflow
    //     0xb30884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb30888: cmp             SP, x16
    //     0xb3088c: b.ls            #0xb32a64
    // 0xb30890: r1 = 1
    //     0xb30890: movz            x1, #0x1
    // 0xb30894: r0 = AllocateContext()
    //     0xb30894: bl              #0x16f6108  ; AllocateContextStub
    // 0xb30898: mov             x3, x0
    // 0xb3089c: ldur            x0, [fp, #-8]
    // 0xb308a0: stur            x3, [fp, #-0x18]
    // 0xb308a4: StoreField: r3->field_f = r0
    //     0xb308a4: stur            w0, [x3, #0xf]
    // 0xb308a8: r1 = <Widget>
    //     0xb308a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb308ac: r2 = 0
    //     0xb308ac: movz            x2, #0
    // 0xb308b0: r0 = _GrowableList()
    //     0xb308b0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb308b4: mov             x1, x0
    // 0xb308b8: ldur            x0, [fp, #-8]
    // 0xb308bc: stur            x1, [fp, #-0x28]
    // 0xb308c0: LoadField: r2 = r0->field_b
    //     0xb308c0: ldur            w2, [x0, #0xb]
    // 0xb308c4: DecompressPointer r2
    //     0xb308c4: add             x2, x2, HEAP, lsl #32
    // 0xb308c8: cmp             w2, NULL
    // 0xb308cc: b.eq            #0xb32a6c
    // 0xb308d0: LoadField: r3 = r2->field_b
    //     0xb308d0: ldur            w3, [x2, #0xb]
    // 0xb308d4: DecompressPointer r3
    //     0xb308d4: add             x3, x3, HEAP, lsl #32
    // 0xb308d8: cmp             w3, NULL
    // 0xb308dc: b.eq            #0xb30900
    // 0xb308e0: LoadField: r4 = r3->field_1b
    //     0xb308e0: ldur            w4, [x3, #0x1b]
    // 0xb308e4: DecompressPointer r4
    //     0xb308e4: add             x4, x4, HEAP, lsl #32
    // 0xb308e8: cmp             w4, NULL
    // 0xb308ec: b.eq            #0xb30900
    // 0xb308f0: LoadField: r3 = r4->field_7
    //     0xb308f0: ldur            w3, [x4, #7]
    // 0xb308f4: DecompressPointer r3
    //     0xb308f4: add             x3, x3, HEAP, lsl #32
    // 0xb308f8: cmp             w3, NULL
    // 0xb308fc: b.ne            #0xb30ef0
    // 0xb30900: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb30900: ldur            w3, [x2, #0x17]
    // 0xb30904: DecompressPointer r3
    //     0xb30904: add             x3, x3, HEAP, lsl #32
    // 0xb30908: cmp             w3, NULL
    // 0xb3090c: b.ne            #0xb30918
    // 0xb30910: r2 = Null
    //     0xb30910: mov             x2, NULL
    // 0xb30914: b               #0xb30934
    // 0xb30918: LoadField: r2 = r3->field_f
    //     0xb30918: ldur            w2, [x3, #0xf]
    // 0xb3091c: DecompressPointer r2
    //     0xb3091c: add             x2, x2, HEAP, lsl #32
    // 0xb30920: LoadField: r3 = r2->field_b
    //     0xb30920: ldur            w3, [x2, #0xb]
    // 0xb30924: cbnz            w3, #0xb30930
    // 0xb30928: r2 = false
    //     0xb30928: add             x2, NULL, #0x30  ; false
    // 0xb3092c: b               #0xb30934
    // 0xb30930: r2 = true
    //     0xb30930: add             x2, NULL, #0x20  ; true
    // 0xb30934: cmp             w2, NULL
    // 0xb30938: b.ne            #0xb30940
    // 0xb3093c: r2 = false
    //     0xb3093c: add             x2, NULL, #0x30  ; false
    // 0xb30940: stur            x2, [fp, #-0x20]
    // 0xb30944: r0 = Radius()
    //     0xb30944: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb30948: d0 = 12.000000
    //     0xb30948: fmov            d0, #12.00000000
    // 0xb3094c: stur            x0, [fp, #-0x30]
    // 0xb30950: StoreField: r0->field_7 = d0
    //     0xb30950: stur            d0, [x0, #7]
    // 0xb30954: StoreField: r0->field_f = d0
    //     0xb30954: stur            d0, [x0, #0xf]
    // 0xb30958: r0 = BorderRadius()
    //     0xb30958: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb3095c: mov             x1, x0
    // 0xb30960: ldur            x0, [fp, #-0x30]
    // 0xb30964: stur            x1, [fp, #-0x38]
    // 0xb30968: StoreField: r1->field_7 = r0
    //     0xb30968: stur            w0, [x1, #7]
    // 0xb3096c: StoreField: r1->field_b = r0
    //     0xb3096c: stur            w0, [x1, #0xb]
    // 0xb30970: StoreField: r1->field_f = r0
    //     0xb30970: stur            w0, [x1, #0xf]
    // 0xb30974: StoreField: r1->field_13 = r0
    //     0xb30974: stur            w0, [x1, #0x13]
    // 0xb30978: r0 = BoxDecoration()
    //     0xb30978: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3097c: r1 = Instance_Color
    //     0xb3097c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb30980: ldr             x1, [x1, #0x860]
    // 0xb30984: stur            x0, [fp, #-0x30]
    // 0xb30988: StoreField: r0->field_7 = r1
    //     0xb30988: stur            w1, [x0, #7]
    // 0xb3098c: ldur            x1, [fp, #-0x38]
    // 0xb30990: StoreField: r0->field_13 = r1
    //     0xb30990: stur            w1, [x0, #0x13]
    // 0xb30994: r1 = Instance_BoxShape
    //     0xb30994: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb30998: ldr             x1, [x1, #0x80]
    // 0xb3099c: StoreField: r0->field_23 = r1
    //     0xb3099c: stur            w1, [x0, #0x23]
    // 0xb309a0: r0 = SvgPicture()
    //     0xb309a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb309a4: stur            x0, [fp, #-0x38]
    // 0xb309a8: r16 = 40.000000
    //     0xb309a8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb309ac: ldr             x16, [x16, #8]
    // 0xb309b0: r30 = 40.000000
    //     0xb309b0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb309b4: ldr             lr, [lr, #8]
    // 0xb309b8: stp             lr, x16, [SP, #8]
    // 0xb309bc: r16 = Instance_BoxFit
    //     0xb309bc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb309c0: ldr             x16, [x16, #0xb18]
    // 0xb309c4: str             x16, [SP]
    // 0xb309c8: mov             x1, x0
    // 0xb309cc: r2 = "assets/images/offer_icon.svg"
    //     0xb309cc: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xb309d0: ldr             x2, [x2, #0x758]
    // 0xb309d4: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb309d4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb309d8: ldr             x4, [x4, #0xb48]
    // 0xb309dc: r0 = SvgPicture.asset()
    //     0xb309dc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb309e0: r0 = Padding()
    //     0xb309e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb309e4: mov             x3, x0
    // 0xb309e8: r0 = Instance_EdgeInsets
    //     0xb309e8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb309ec: ldr             x0, [x0, #0x878]
    // 0xb309f0: stur            x3, [fp, #-0x40]
    // 0xb309f4: StoreField: r3->field_f = r0
    //     0xb309f4: stur            w0, [x3, #0xf]
    // 0xb309f8: ldur            x1, [fp, #-0x38]
    // 0xb309fc: StoreField: r3->field_b = r1
    //     0xb309fc: stur            w1, [x3, #0xb]
    // 0xb30a00: r1 = Null
    //     0xb30a00: mov             x1, NULL
    // 0xb30a04: r2 = 4
    //     0xb30a04: movz            x2, #0x4
    // 0xb30a08: r0 = AllocateArray()
    //     0xb30a08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30a0c: r16 = "Save upto "
    //     0xb30a0c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xb30a10: ldr             x16, [x16, #0xca0]
    // 0xb30a14: StoreField: r0->field_f = r16
    //     0xb30a14: stur            w16, [x0, #0xf]
    // 0xb30a18: ldur            x1, [fp, #-8]
    // 0xb30a1c: LoadField: r2 = r1->field_b
    //     0xb30a1c: ldur            w2, [x1, #0xb]
    // 0xb30a20: DecompressPointer r2
    //     0xb30a20: add             x2, x2, HEAP, lsl #32
    // 0xb30a24: cmp             w2, NULL
    // 0xb30a28: b.eq            #0xb32a70
    // 0xb30a2c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb30a2c: ldur            w3, [x2, #0x17]
    // 0xb30a30: DecompressPointer r3
    //     0xb30a30: add             x3, x3, HEAP, lsl #32
    // 0xb30a34: cmp             w3, NULL
    // 0xb30a38: b.ne            #0xb30a44
    // 0xb30a3c: r2 = Null
    //     0xb30a3c: mov             x2, NULL
    // 0xb30a40: b               #0xb30a4c
    // 0xb30a44: LoadField: r2 = r3->field_b
    //     0xb30a44: ldur            w2, [x3, #0xb]
    // 0xb30a48: DecompressPointer r2
    //     0xb30a48: add             x2, x2, HEAP, lsl #32
    // 0xb30a4c: StoreField: r0->field_13 = r2
    //     0xb30a4c: stur            w2, [x0, #0x13]
    // 0xb30a50: str             x0, [SP]
    // 0xb30a54: r0 = _interpolate()
    //     0xb30a54: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb30a58: ldur            x1, [fp, #-0x10]
    // 0xb30a5c: stur            x0, [fp, #-0x38]
    // 0xb30a60: r0 = of()
    //     0xb30a60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30a64: LoadField: r1 = r0->field_87
    //     0xb30a64: ldur            w1, [x0, #0x87]
    // 0xb30a68: DecompressPointer r1
    //     0xb30a68: add             x1, x1, HEAP, lsl #32
    // 0xb30a6c: LoadField: r0 = r1->field_7
    //     0xb30a6c: ldur            w0, [x1, #7]
    // 0xb30a70: DecompressPointer r0
    //     0xb30a70: add             x0, x0, HEAP, lsl #32
    // 0xb30a74: r16 = 16.000000
    //     0xb30a74: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb30a78: ldr             x16, [x16, #0x188]
    // 0xb30a7c: r30 = Instance_Color
    //     0xb30a7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb30a80: stp             lr, x16, [SP]
    // 0xb30a84: mov             x1, x0
    // 0xb30a88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb30a88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30a8c: ldr             x4, [x4, #0xaa0]
    // 0xb30a90: r0 = copyWith()
    //     0xb30a90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30a94: stur            x0, [fp, #-0x48]
    // 0xb30a98: r0 = Text()
    //     0xb30a98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb30a9c: mov             x3, x0
    // 0xb30aa0: ldur            x0, [fp, #-0x38]
    // 0xb30aa4: stur            x3, [fp, #-0x50]
    // 0xb30aa8: StoreField: r3->field_b = r0
    //     0xb30aa8: stur            w0, [x3, #0xb]
    // 0xb30aac: ldur            x0, [fp, #-0x48]
    // 0xb30ab0: StoreField: r3->field_13 = r0
    //     0xb30ab0: stur            w0, [x3, #0x13]
    // 0xb30ab4: ldur            x0, [fp, #-8]
    // 0xb30ab8: LoadField: r1 = r0->field_b
    //     0xb30ab8: ldur            w1, [x0, #0xb]
    // 0xb30abc: DecompressPointer r1
    //     0xb30abc: add             x1, x1, HEAP, lsl #32
    // 0xb30ac0: cmp             w1, NULL
    // 0xb30ac4: b.eq            #0xb32a74
    // 0xb30ac8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb30ac8: ldur            w2, [x1, #0x17]
    // 0xb30acc: DecompressPointer r2
    //     0xb30acc: add             x2, x2, HEAP, lsl #32
    // 0xb30ad0: cmp             w2, NULL
    // 0xb30ad4: b.ne            #0xb30ae0
    // 0xb30ad8: r7 = Null
    //     0xb30ad8: mov             x7, NULL
    // 0xb30adc: b               #0xb30af4
    // 0xb30ae0: LoadField: r1 = r2->field_13
    //     0xb30ae0: ldur            w1, [x2, #0x13]
    // 0xb30ae4: DecompressPointer r1
    //     0xb30ae4: add             x1, x1, HEAP, lsl #32
    // 0xb30ae8: LoadField: r2 = r1->field_7
    //     0xb30ae8: ldur            w2, [x1, #7]
    // 0xb30aec: DecompressPointer r2
    //     0xb30aec: add             x2, x2, HEAP, lsl #32
    // 0xb30af0: mov             x7, x2
    // 0xb30af4: ldur            x5, [fp, #-0x28]
    // 0xb30af8: ldur            x6, [fp, #-0x20]
    // 0xb30afc: ldur            x4, [fp, #-0x40]
    // 0xb30b00: stur            x7, [fp, #-0x38]
    // 0xb30b04: r1 = Null
    //     0xb30b04: mov             x1, NULL
    // 0xb30b08: r2 = 4
    //     0xb30b08: movz            x2, #0x4
    // 0xb30b0c: r0 = AllocateArray()
    //     0xb30b0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30b10: mov             x1, x0
    // 0xb30b14: ldur            x0, [fp, #-0x38]
    // 0xb30b18: StoreField: r1->field_f = r0
    //     0xb30b18: stur            w0, [x1, #0xf]
    // 0xb30b1c: r16 = " offers available"
    //     0xb30b1c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca8] " offers available"
    //     0xb30b20: ldr             x16, [x16, #0xca8]
    // 0xb30b24: StoreField: r1->field_13 = r16
    //     0xb30b24: stur            w16, [x1, #0x13]
    // 0xb30b28: str             x1, [SP]
    // 0xb30b2c: r0 = _interpolate()
    //     0xb30b2c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb30b30: ldur            x1, [fp, #-0x10]
    // 0xb30b34: stur            x0, [fp, #-0x38]
    // 0xb30b38: r0 = of()
    //     0xb30b38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30b3c: LoadField: r1 = r0->field_87
    //     0xb30b3c: ldur            w1, [x0, #0x87]
    // 0xb30b40: DecompressPointer r1
    //     0xb30b40: add             x1, x1, HEAP, lsl #32
    // 0xb30b44: LoadField: r0 = r1->field_2b
    //     0xb30b44: ldur            w0, [x1, #0x2b]
    // 0xb30b48: DecompressPointer r0
    //     0xb30b48: add             x0, x0, HEAP, lsl #32
    // 0xb30b4c: r16 = Instance_Color
    //     0xb30b4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb30b50: ldr             x16, [x16, #0x858]
    // 0xb30b54: r30 = 12.000000
    //     0xb30b54: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb30b58: ldr             lr, [lr, #0x9e8]
    // 0xb30b5c: stp             lr, x16, [SP]
    // 0xb30b60: mov             x1, x0
    // 0xb30b64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb30b64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb30b68: ldr             x4, [x4, #0x9b8]
    // 0xb30b6c: r0 = copyWith()
    //     0xb30b6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30b70: stur            x0, [fp, #-0x48]
    // 0xb30b74: r0 = Text()
    //     0xb30b74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb30b78: mov             x3, x0
    // 0xb30b7c: ldur            x0, [fp, #-0x38]
    // 0xb30b80: stur            x3, [fp, #-0x58]
    // 0xb30b84: StoreField: r3->field_b = r0
    //     0xb30b84: stur            w0, [x3, #0xb]
    // 0xb30b88: ldur            x0, [fp, #-0x48]
    // 0xb30b8c: StoreField: r3->field_13 = r0
    //     0xb30b8c: stur            w0, [x3, #0x13]
    // 0xb30b90: r1 = Null
    //     0xb30b90: mov             x1, NULL
    // 0xb30b94: r2 = 6
    //     0xb30b94: movz            x2, #0x6
    // 0xb30b98: r0 = AllocateArray()
    //     0xb30b98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30b9c: mov             x2, x0
    // 0xb30ba0: ldur            x0, [fp, #-0x50]
    // 0xb30ba4: stur            x2, [fp, #-0x38]
    // 0xb30ba8: StoreField: r2->field_f = r0
    //     0xb30ba8: stur            w0, [x2, #0xf]
    // 0xb30bac: r16 = Instance_SizedBox
    //     0xb30bac: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb30bb0: ldr             x16, [x16, #0x328]
    // 0xb30bb4: StoreField: r2->field_13 = r16
    //     0xb30bb4: stur            w16, [x2, #0x13]
    // 0xb30bb8: ldur            x0, [fp, #-0x58]
    // 0xb30bbc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb30bbc: stur            w0, [x2, #0x17]
    // 0xb30bc0: r1 = <Widget>
    //     0xb30bc0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb30bc4: r0 = AllocateGrowableArray()
    //     0xb30bc4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb30bc8: mov             x1, x0
    // 0xb30bcc: ldur            x0, [fp, #-0x38]
    // 0xb30bd0: stur            x1, [fp, #-0x48]
    // 0xb30bd4: StoreField: r1->field_f = r0
    //     0xb30bd4: stur            w0, [x1, #0xf]
    // 0xb30bd8: r2 = 6
    //     0xb30bd8: movz            x2, #0x6
    // 0xb30bdc: StoreField: r1->field_b = r2
    //     0xb30bdc: stur            w2, [x1, #0xb]
    // 0xb30be0: r0 = Column()
    //     0xb30be0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb30be4: mov             x2, x0
    // 0xb30be8: r0 = Instance_Axis
    //     0xb30be8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb30bec: stur            x2, [fp, #-0x38]
    // 0xb30bf0: StoreField: r2->field_f = r0
    //     0xb30bf0: stur            w0, [x2, #0xf]
    // 0xb30bf4: r3 = Instance_MainAxisAlignment
    //     0xb30bf4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb30bf8: ldr             x3, [x3, #0xab0]
    // 0xb30bfc: StoreField: r2->field_13 = r3
    //     0xb30bfc: stur            w3, [x2, #0x13]
    // 0xb30c00: r3 = Instance_MainAxisSize
    //     0xb30c00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb30c04: ldr             x3, [x3, #0xa10]
    // 0xb30c08: ArrayStore: r2[0] = r3  ; List_4
    //     0xb30c08: stur            w3, [x2, #0x17]
    // 0xb30c0c: r4 = Instance_CrossAxisAlignment
    //     0xb30c0c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb30c10: ldr             x4, [x4, #0x890]
    // 0xb30c14: StoreField: r2->field_1b = r4
    //     0xb30c14: stur            w4, [x2, #0x1b]
    // 0xb30c18: r5 = Instance_VerticalDirection
    //     0xb30c18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb30c1c: ldr             x5, [x5, #0xa20]
    // 0xb30c20: StoreField: r2->field_23 = r5
    //     0xb30c20: stur            w5, [x2, #0x23]
    // 0xb30c24: r6 = Instance_Clip
    //     0xb30c24: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb30c28: ldr             x6, [x6, #0x38]
    // 0xb30c2c: StoreField: r2->field_2b = r6
    //     0xb30c2c: stur            w6, [x2, #0x2b]
    // 0xb30c30: StoreField: r2->field_2f = rZR
    //     0xb30c30: stur            xzr, [x2, #0x2f]
    // 0xb30c34: ldur            x1, [fp, #-0x48]
    // 0xb30c38: StoreField: r2->field_b = r1
    //     0xb30c38: stur            w1, [x2, #0xb]
    // 0xb30c3c: r1 = <FlexParentData>
    //     0xb30c3c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb30c40: ldr             x1, [x1, #0xe00]
    // 0xb30c44: r0 = Expanded()
    //     0xb30c44: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb30c48: stur            x0, [fp, #-0x48]
    // 0xb30c4c: StoreField: r0->field_13 = rZR
    //     0xb30c4c: stur            xzr, [x0, #0x13]
    // 0xb30c50: r4 = Instance_FlexFit
    //     0xb30c50: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb30c54: ldr             x4, [x4, #0xe08]
    // 0xb30c58: StoreField: r0->field_1b = r4
    //     0xb30c58: stur            w4, [x0, #0x1b]
    // 0xb30c5c: ldur            x1, [fp, #-0x38]
    // 0xb30c60: StoreField: r0->field_b = r1
    //     0xb30c60: stur            w1, [x0, #0xb]
    // 0xb30c64: ldur            x1, [fp, #-0x10]
    // 0xb30c68: r0 = of()
    //     0xb30c68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30c6c: LoadField: r1 = r0->field_87
    //     0xb30c6c: ldur            w1, [x0, #0x87]
    // 0xb30c70: DecompressPointer r1
    //     0xb30c70: add             x1, x1, HEAP, lsl #32
    // 0xb30c74: LoadField: r0 = r1->field_7
    //     0xb30c74: ldur            w0, [x1, #7]
    // 0xb30c78: DecompressPointer r0
    //     0xb30c78: add             x0, x0, HEAP, lsl #32
    // 0xb30c7c: r16 = 14.000000
    //     0xb30c7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb30c80: ldr             x16, [x16, #0x1d8]
    // 0xb30c84: r30 = Instance_Color
    //     0xb30c84: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb30c88: ldr             lr, [lr, #0x858]
    // 0xb30c8c: stp             lr, x16, [SP]
    // 0xb30c90: mov             x1, x0
    // 0xb30c94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb30c94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30c98: ldr             x4, [x4, #0xaa0]
    // 0xb30c9c: r0 = copyWith()
    //     0xb30c9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30ca0: stur            x0, [fp, #-0x38]
    // 0xb30ca4: r0 = Text()
    //     0xb30ca4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb30ca8: mov             x1, x0
    // 0xb30cac: r0 = "OFFERS"
    //     0xb30cac: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cb8] "OFFERS"
    //     0xb30cb0: ldr             x0, [x0, #0xcb8]
    // 0xb30cb4: stur            x1, [fp, #-0x50]
    // 0xb30cb8: StoreField: r1->field_b = r0
    //     0xb30cb8: stur            w0, [x1, #0xb]
    // 0xb30cbc: ldur            x0, [fp, #-0x38]
    // 0xb30cc0: StoreField: r1->field_13 = r0
    //     0xb30cc0: stur            w0, [x1, #0x13]
    // 0xb30cc4: r0 = InkWell()
    //     0xb30cc4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb30cc8: mov             x3, x0
    // 0xb30ccc: ldur            x0, [fp, #-0x50]
    // 0xb30cd0: stur            x3, [fp, #-0x38]
    // 0xb30cd4: StoreField: r3->field_b = r0
    //     0xb30cd4: stur            w0, [x3, #0xb]
    // 0xb30cd8: ldur            x2, [fp, #-0x18]
    // 0xb30cdc: r1 = Function '<anonymous closure>':.
    //     0xb30cdc: add             x1, PP, #0x57, lsl #12  ; [pp+0x571d8] AnonymousClosure: (0xb32c58), in [package:customer_app/app/presentation/views/glass/bag/bottom_view.dart] _BottomViewState::build (0xb30868)
    //     0xb30ce0: ldr             x1, [x1, #0x1d8]
    // 0xb30ce4: r0 = AllocateClosure()
    //     0xb30ce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb30ce8: mov             x1, x0
    // 0xb30cec: ldur            x0, [fp, #-0x38]
    // 0xb30cf0: StoreField: r0->field_f = r1
    //     0xb30cf0: stur            w1, [x0, #0xf]
    // 0xb30cf4: r1 = true
    //     0xb30cf4: add             x1, NULL, #0x20  ; true
    // 0xb30cf8: StoreField: r0->field_43 = r1
    //     0xb30cf8: stur            w1, [x0, #0x43]
    // 0xb30cfc: r5 = Instance_BoxShape
    //     0xb30cfc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb30d00: ldr             x5, [x5, #0x80]
    // 0xb30d04: StoreField: r0->field_47 = r5
    //     0xb30d04: stur            w5, [x0, #0x47]
    // 0xb30d08: StoreField: r0->field_6f = r1
    //     0xb30d08: stur            w1, [x0, #0x6f]
    // 0xb30d0c: r2 = false
    //     0xb30d0c: add             x2, NULL, #0x30  ; false
    // 0xb30d10: StoreField: r0->field_73 = r2
    //     0xb30d10: stur            w2, [x0, #0x73]
    // 0xb30d14: StoreField: r0->field_83 = r1
    //     0xb30d14: stur            w1, [x0, #0x83]
    // 0xb30d18: StoreField: r0->field_7b = r2
    //     0xb30d18: stur            w2, [x0, #0x7b]
    // 0xb30d1c: r0 = Padding()
    //     0xb30d1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb30d20: mov             x3, x0
    // 0xb30d24: r0 = Instance_EdgeInsets
    //     0xb30d24: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c08] Obj!EdgeInsets@d58c41
    //     0xb30d28: ldr             x0, [x0, #0xc08]
    // 0xb30d2c: stur            x3, [fp, #-0x50]
    // 0xb30d30: StoreField: r3->field_f = r0
    //     0xb30d30: stur            w0, [x3, #0xf]
    // 0xb30d34: ldur            x0, [fp, #-0x38]
    // 0xb30d38: StoreField: r3->field_b = r0
    //     0xb30d38: stur            w0, [x3, #0xb]
    // 0xb30d3c: r1 = Null
    //     0xb30d3c: mov             x1, NULL
    // 0xb30d40: r2 = 8
    //     0xb30d40: movz            x2, #0x8
    // 0xb30d44: r0 = AllocateArray()
    //     0xb30d44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30d48: mov             x2, x0
    // 0xb30d4c: ldur            x0, [fp, #-0x40]
    // 0xb30d50: stur            x2, [fp, #-0x38]
    // 0xb30d54: StoreField: r2->field_f = r0
    //     0xb30d54: stur            w0, [x2, #0xf]
    // 0xb30d58: ldur            x0, [fp, #-0x48]
    // 0xb30d5c: StoreField: r2->field_13 = r0
    //     0xb30d5c: stur            w0, [x2, #0x13]
    // 0xb30d60: r16 = Instance_Spacer
    //     0xb30d60: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb30d64: ldr             x16, [x16, #0xf0]
    // 0xb30d68: ArrayStore: r2[0] = r16  ; List_4
    //     0xb30d68: stur            w16, [x2, #0x17]
    // 0xb30d6c: ldur            x0, [fp, #-0x50]
    // 0xb30d70: StoreField: r2->field_1b = r0
    //     0xb30d70: stur            w0, [x2, #0x1b]
    // 0xb30d74: r1 = <Widget>
    //     0xb30d74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb30d78: r0 = AllocateGrowableArray()
    //     0xb30d78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb30d7c: mov             x1, x0
    // 0xb30d80: ldur            x0, [fp, #-0x38]
    // 0xb30d84: stur            x1, [fp, #-0x40]
    // 0xb30d88: StoreField: r1->field_f = r0
    //     0xb30d88: stur            w0, [x1, #0xf]
    // 0xb30d8c: r0 = 8
    //     0xb30d8c: movz            x0, #0x8
    // 0xb30d90: StoreField: r1->field_b = r0
    //     0xb30d90: stur            w0, [x1, #0xb]
    // 0xb30d94: r0 = Row()
    //     0xb30d94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb30d98: mov             x1, x0
    // 0xb30d9c: r0 = Instance_Axis
    //     0xb30d9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb30da0: stur            x1, [fp, #-0x38]
    // 0xb30da4: StoreField: r1->field_f = r0
    //     0xb30da4: stur            w0, [x1, #0xf]
    // 0xb30da8: r6 = Instance_MainAxisAlignment
    //     0xb30da8: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb30dac: ldr             x6, [x6, #0xd10]
    // 0xb30db0: StoreField: r1->field_13 = r6
    //     0xb30db0: stur            w6, [x1, #0x13]
    // 0xb30db4: r2 = Instance_MainAxisSize
    //     0xb30db4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb30db8: ldr             x2, [x2, #0xa10]
    // 0xb30dbc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb30dbc: stur            w2, [x1, #0x17]
    // 0xb30dc0: r3 = Instance_CrossAxisAlignment
    //     0xb30dc0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb30dc4: ldr             x3, [x3, #0xa18]
    // 0xb30dc8: StoreField: r1->field_1b = r3
    //     0xb30dc8: stur            w3, [x1, #0x1b]
    // 0xb30dcc: r4 = Instance_VerticalDirection
    //     0xb30dcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb30dd0: ldr             x4, [x4, #0xa20]
    // 0xb30dd4: StoreField: r1->field_23 = r4
    //     0xb30dd4: stur            w4, [x1, #0x23]
    // 0xb30dd8: r5 = Instance_Clip
    //     0xb30dd8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb30ddc: ldr             x5, [x5, #0x38]
    // 0xb30de0: StoreField: r1->field_2b = r5
    //     0xb30de0: stur            w5, [x1, #0x2b]
    // 0xb30de4: StoreField: r1->field_2f = rZR
    //     0xb30de4: stur            xzr, [x1, #0x2f]
    // 0xb30de8: ldur            x6, [fp, #-0x40]
    // 0xb30dec: StoreField: r1->field_b = r6
    //     0xb30dec: stur            w6, [x1, #0xb]
    // 0xb30df0: r0 = Padding()
    //     0xb30df0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb30df4: r7 = Instance_EdgeInsets
    //     0xb30df4: add             x7, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb30df8: ldr             x7, [x7, #0x878]
    // 0xb30dfc: stur            x0, [fp, #-0x40]
    // 0xb30e00: StoreField: r0->field_f = r7
    //     0xb30e00: stur            w7, [x0, #0xf]
    // 0xb30e04: ldur            x1, [fp, #-0x38]
    // 0xb30e08: StoreField: r0->field_b = r1
    //     0xb30e08: stur            w1, [x0, #0xb]
    // 0xb30e0c: r0 = Container()
    //     0xb30e0c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb30e10: stur            x0, [fp, #-0x38]
    // 0xb30e14: ldur            x16, [fp, #-0x30]
    // 0xb30e18: ldur            lr, [fp, #-0x40]
    // 0xb30e1c: stp             lr, x16, [SP]
    // 0xb30e20: mov             x1, x0
    // 0xb30e24: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb30e24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb30e28: ldr             x4, [x4, #0x88]
    // 0xb30e2c: r0 = Container()
    //     0xb30e2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb30e30: r0 = Visibility()
    //     0xb30e30: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb30e34: mov             x2, x0
    // 0xb30e38: ldur            x0, [fp, #-0x38]
    // 0xb30e3c: stur            x2, [fp, #-0x30]
    // 0xb30e40: StoreField: r2->field_b = r0
    //     0xb30e40: stur            w0, [x2, #0xb]
    // 0xb30e44: r0 = Instance_SizedBox
    //     0xb30e44: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb30e48: StoreField: r2->field_f = r0
    //     0xb30e48: stur            w0, [x2, #0xf]
    // 0xb30e4c: ldur            x1, [fp, #-0x20]
    // 0xb30e50: StoreField: r2->field_13 = r1
    //     0xb30e50: stur            w1, [x2, #0x13]
    // 0xb30e54: r3 = false
    //     0xb30e54: add             x3, NULL, #0x30  ; false
    // 0xb30e58: ArrayStore: r2[0] = r3  ; List_4
    //     0xb30e58: stur            w3, [x2, #0x17]
    // 0xb30e5c: StoreField: r2->field_1b = r3
    //     0xb30e5c: stur            w3, [x2, #0x1b]
    // 0xb30e60: StoreField: r2->field_1f = r3
    //     0xb30e60: stur            w3, [x2, #0x1f]
    // 0xb30e64: StoreField: r2->field_23 = r3
    //     0xb30e64: stur            w3, [x2, #0x23]
    // 0xb30e68: StoreField: r2->field_27 = r3
    //     0xb30e68: stur            w3, [x2, #0x27]
    // 0xb30e6c: StoreField: r2->field_2b = r3
    //     0xb30e6c: stur            w3, [x2, #0x2b]
    // 0xb30e70: ldur            x4, [fp, #-0x28]
    // 0xb30e74: LoadField: r1 = r4->field_b
    //     0xb30e74: ldur            w1, [x4, #0xb]
    // 0xb30e78: LoadField: r5 = r4->field_f
    //     0xb30e78: ldur            w5, [x4, #0xf]
    // 0xb30e7c: DecompressPointer r5
    //     0xb30e7c: add             x5, x5, HEAP, lsl #32
    // 0xb30e80: LoadField: r6 = r5->field_b
    //     0xb30e80: ldur            w6, [x5, #0xb]
    // 0xb30e84: r5 = LoadInt32Instr(r1)
    //     0xb30e84: sbfx            x5, x1, #1, #0x1f
    // 0xb30e88: stur            x5, [fp, #-0x60]
    // 0xb30e8c: r1 = LoadInt32Instr(r6)
    //     0xb30e8c: sbfx            x1, x6, #1, #0x1f
    // 0xb30e90: cmp             x5, x1
    // 0xb30e94: b.ne            #0xb30ea0
    // 0xb30e98: mov             x1, x4
    // 0xb30e9c: r0 = _growToNextCapacity()
    //     0xb30e9c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb30ea0: ldur            x8, [fp, #-0x28]
    // 0xb30ea4: ldur            x2, [fp, #-0x60]
    // 0xb30ea8: add             x0, x2, #1
    // 0xb30eac: lsl             x1, x0, #1
    // 0xb30eb0: StoreField: r8->field_b = r1
    //     0xb30eb0: stur            w1, [x8, #0xb]
    // 0xb30eb4: LoadField: r1 = r8->field_f
    //     0xb30eb4: ldur            w1, [x8, #0xf]
    // 0xb30eb8: DecompressPointer r1
    //     0xb30eb8: add             x1, x1, HEAP, lsl #32
    // 0xb30ebc: ldur            x0, [fp, #-0x30]
    // 0xb30ec0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb30ec0: add             x25, x1, x2, lsl #2
    //     0xb30ec4: add             x25, x25, #0xf
    //     0xb30ec8: str             w0, [x25]
    //     0xb30ecc: tbz             w0, #0, #0xb30ee8
    //     0xb30ed0: ldurb           w16, [x1, #-1]
    //     0xb30ed4: ldurb           w17, [x0, #-1]
    //     0xb30ed8: and             x16, x17, x16, lsr #2
    //     0xb30edc: tst             x16, HEAP, lsr #32
    //     0xb30ee0: b.eq            #0xb30ee8
    //     0xb30ee4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb30ee8: mov             x3, x8
    // 0xb30eec: b               #0xb320e4
    // 0xb30ef0: mov             x8, x1
    // 0xb30ef4: r1 = Instance_Color
    //     0xb30ef4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb30ef8: ldr             x1, [x1, #0x860]
    // 0xb30efc: r7 = Instance_EdgeInsets
    //     0xb30efc: add             x7, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb30f00: ldr             x7, [x7, #0x878]
    // 0xb30f04: r3 = Instance_MainAxisAlignment
    //     0xb30f04: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb30f08: ldr             x3, [x3, #0xab0]
    // 0xb30f0c: r6 = Instance_MainAxisAlignment
    //     0xb30f0c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb30f10: ldr             x6, [x6, #0xd10]
    // 0xb30f14: r4 = Instance_FlexFit
    //     0xb30f14: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb30f18: ldr             x4, [x4, #0xe08]
    // 0xb30f1c: r5 = Instance_BoxShape
    //     0xb30f1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb30f20: ldr             x5, [x5, #0x80]
    // 0xb30f24: d0 = 12.000000
    //     0xb30f24: fmov            d0, #12.00000000
    // 0xb30f28: LoadField: r0 = r2->field_2b
    //     0xb30f28: ldur            w0, [x2, #0x2b]
    // 0xb30f2c: DecompressPointer r0
    //     0xb30f2c: add             x0, x0, HEAP, lsl #32
    // 0xb30f30: LoadField: r9 = r0->field_f
    //     0xb30f30: ldur            w9, [x0, #0xf]
    // 0xb30f34: DecompressPointer r9
    //     0xb30f34: add             x9, x9, HEAP, lsl #32
    // 0xb30f38: cmp             w9, NULL
    // 0xb30f3c: b.eq            #0xb317f4
    // 0xb30f40: LoadField: r0 = r2->field_27
    //     0xb30f40: ldur            w0, [x2, #0x27]
    // 0xb30f44: DecompressPointer r0
    //     0xb30f44: add             x0, x0, HEAP, lsl #32
    // 0xb30f48: r2 = LoadClassIdInstr(r0)
    //     0xb30f48: ldur            x2, [x0, #-1]
    //     0xb30f4c: ubfx            x2, x2, #0xc, #0x14
    // 0xb30f50: r16 = "bumper_coupon"
    //     0xb30f50: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xb30f54: ldr             x16, [x16, #0x8d8]
    // 0xb30f58: stp             x16, x0, [SP]
    // 0xb30f5c: mov             x0, x2
    // 0xb30f60: mov             lr, x0
    // 0xb30f64: ldr             lr, [x21, lr, lsl #3]
    // 0xb30f68: blr             lr
    // 0xb30f6c: tbnz            w0, #4, #0xb317b0
    // 0xb30f70: ldur            x0, [fp, #-8]
    // 0xb30f74: LoadField: r1 = r0->field_b
    //     0xb30f74: ldur            w1, [x0, #0xb]
    // 0xb30f78: DecompressPointer r1
    //     0xb30f78: add             x1, x1, HEAP, lsl #32
    // 0xb30f7c: cmp             w1, NULL
    // 0xb30f80: b.eq            #0xb32a78
    // 0xb30f84: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb30f84: ldur            w2, [x1, #0x17]
    // 0xb30f88: DecompressPointer r2
    //     0xb30f88: add             x2, x2, HEAP, lsl #32
    // 0xb30f8c: cmp             w2, NULL
    // 0xb30f90: b.ne            #0xb30f9c
    // 0xb30f94: r2 = Null
    //     0xb30f94: mov             x2, NULL
    // 0xb30f98: b               #0xb30fbc
    // 0xb30f9c: LoadField: r3 = r2->field_f
    //     0xb30f9c: ldur            w3, [x2, #0xf]
    // 0xb30fa0: DecompressPointer r3
    //     0xb30fa0: add             x3, x3, HEAP, lsl #32
    // 0xb30fa4: LoadField: r2 = r3->field_b
    //     0xb30fa4: ldur            w2, [x3, #0xb]
    // 0xb30fa8: cbnz            w2, #0xb30fb4
    // 0xb30fac: r3 = false
    //     0xb30fac: add             x3, NULL, #0x30  ; false
    // 0xb30fb0: b               #0xb30fb8
    // 0xb30fb4: r3 = true
    //     0xb30fb4: add             x3, NULL, #0x20  ; true
    // 0xb30fb8: mov             x2, x3
    // 0xb30fbc: cmp             w2, NULL
    // 0xb30fc0: b.ne            #0xb30fc8
    // 0xb30fc4: r2 = false
    //     0xb30fc4: add             x2, NULL, #0x30  ; false
    // 0xb30fc8: stur            x2, [fp, #-0x30]
    // 0xb30fcc: LoadField: r3 = r1->field_2b
    //     0xb30fcc: ldur            w3, [x1, #0x2b]
    // 0xb30fd0: DecompressPointer r3
    //     0xb30fd0: add             x3, x3, HEAP, lsl #32
    // 0xb30fd4: LoadField: r1 = r3->field_13
    //     0xb30fd4: ldur            w1, [x3, #0x13]
    // 0xb30fd8: DecompressPointer r1
    //     0xb30fd8: add             x1, x1, HEAP, lsl #32
    // 0xb30fdc: stur            x1, [fp, #-0x20]
    // 0xb30fe0: cmp             w1, NULL
    // 0xb30fe4: b.ne            #0xb30ff0
    // 0xb30fe8: r3 = Null
    //     0xb30fe8: mov             x3, NULL
    // 0xb30fec: b               #0xb30ff8
    // 0xb30ff0: LoadField: r3 = r1->field_7
    //     0xb30ff0: ldur            w3, [x1, #7]
    // 0xb30ff4: DecompressPointer r3
    //     0xb30ff4: add             x3, x3, HEAP, lsl #32
    // 0xb30ff8: cmp             w3, NULL
    // 0xb30ffc: b.ne            #0xb31008
    // 0xb31000: r3 = 0
    //     0xb31000: movz            x3, #0
    // 0xb31004: b               #0xb31018
    // 0xb31008: r4 = LoadInt32Instr(r3)
    //     0xb31008: sbfx            x4, x3, #1, #0x1f
    //     0xb3100c: tbz             w3, #0, #0xb31014
    //     0xb31010: ldur            x4, [x3, #7]
    // 0xb31014: mov             x3, x4
    // 0xb31018: stur            x3, [fp, #-0x70]
    // 0xb3101c: cmp             w1, NULL
    // 0xb31020: b.ne            #0xb3102c
    // 0xb31024: r4 = Null
    //     0xb31024: mov             x4, NULL
    // 0xb31028: b               #0xb31034
    // 0xb3102c: LoadField: r4 = r1->field_b
    //     0xb3102c: ldur            w4, [x1, #0xb]
    // 0xb31030: DecompressPointer r4
    //     0xb31030: add             x4, x4, HEAP, lsl #32
    // 0xb31034: cmp             w4, NULL
    // 0xb31038: b.ne            #0xb31044
    // 0xb3103c: r4 = 0
    //     0xb3103c: movz            x4, #0
    // 0xb31040: b               #0xb31054
    // 0xb31044: r5 = LoadInt32Instr(r4)
    //     0xb31044: sbfx            x5, x4, #1, #0x1f
    //     0xb31048: tbz             w4, #0, #0xb31050
    //     0xb3104c: ldur            x5, [x4, #7]
    // 0xb31050: mov             x4, x5
    // 0xb31054: stur            x4, [fp, #-0x68]
    // 0xb31058: cmp             w1, NULL
    // 0xb3105c: b.ne            #0xb31068
    // 0xb31060: r5 = Null
    //     0xb31060: mov             x5, NULL
    // 0xb31064: b               #0xb31070
    // 0xb31068: LoadField: r5 = r1->field_f
    //     0xb31068: ldur            w5, [x1, #0xf]
    // 0xb3106c: DecompressPointer r5
    //     0xb3106c: add             x5, x5, HEAP, lsl #32
    // 0xb31070: cmp             w5, NULL
    // 0xb31074: b.ne            #0xb31080
    // 0xb31078: r5 = 0
    //     0xb31078: movz            x5, #0
    // 0xb3107c: b               #0xb31090
    // 0xb31080: r6 = LoadInt32Instr(r5)
    //     0xb31080: sbfx            x6, x5, #1, #0x1f
    //     0xb31084: tbz             w5, #0, #0xb3108c
    //     0xb31088: ldur            x6, [x5, #7]
    // 0xb3108c: mov             x5, x6
    // 0xb31090: stur            x5, [fp, #-0x60]
    // 0xb31094: r0 = Color()
    //     0xb31094: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb31098: mov             x1, x0
    // 0xb3109c: r0 = Instance_ColorSpace
    //     0xb3109c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb310a0: stur            x1, [fp, #-0x38]
    // 0xb310a4: StoreField: r1->field_27 = r0
    //     0xb310a4: stur            w0, [x1, #0x27]
    // 0xb310a8: d0 = 1.000000
    //     0xb310a8: fmov            d0, #1.00000000
    // 0xb310ac: StoreField: r1->field_7 = d0
    //     0xb310ac: stur            d0, [x1, #7]
    // 0xb310b0: ldur            x2, [fp, #-0x70]
    // 0xb310b4: ubfx            x2, x2, #0, #0x20
    // 0xb310b8: and             w3, w2, #0xff
    // 0xb310bc: ubfx            x3, x3, #0, #0x20
    // 0xb310c0: scvtf           d0, x3
    // 0xb310c4: d1 = 255.000000
    //     0xb310c4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb310c8: fdiv            d2, d0, d1
    // 0xb310cc: StoreField: r1->field_f = d2
    //     0xb310cc: stur            d2, [x1, #0xf]
    // 0xb310d0: ldur            x2, [fp, #-0x68]
    // 0xb310d4: ubfx            x2, x2, #0, #0x20
    // 0xb310d8: and             w3, w2, #0xff
    // 0xb310dc: ubfx            x3, x3, #0, #0x20
    // 0xb310e0: scvtf           d0, x3
    // 0xb310e4: fdiv            d2, d0, d1
    // 0xb310e8: ArrayStore: r1[0] = d2  ; List_8
    //     0xb310e8: stur            d2, [x1, #0x17]
    // 0xb310ec: ldur            x2, [fp, #-0x60]
    // 0xb310f0: ubfx            x2, x2, #0, #0x20
    // 0xb310f4: and             w3, w2, #0xff
    // 0xb310f8: ubfx            x3, x3, #0, #0x20
    // 0xb310fc: scvtf           d0, x3
    // 0xb31100: fdiv            d2, d0, d1
    // 0xb31104: StoreField: r1->field_1f = d2
    //     0xb31104: stur            d2, [x1, #0x1f]
    // 0xb31108: ldur            x2, [fp, #-0x20]
    // 0xb3110c: cmp             w2, NULL
    // 0xb31110: b.ne            #0xb3111c
    // 0xb31114: r3 = Null
    //     0xb31114: mov             x3, NULL
    // 0xb31118: b               #0xb31124
    // 0xb3111c: LoadField: r3 = r2->field_7
    //     0xb3111c: ldur            w3, [x2, #7]
    // 0xb31120: DecompressPointer r3
    //     0xb31120: add             x3, x3, HEAP, lsl #32
    // 0xb31124: cmp             w3, NULL
    // 0xb31128: b.ne            #0xb31134
    // 0xb3112c: r3 = 0
    //     0xb3112c: movz            x3, #0
    // 0xb31130: b               #0xb31144
    // 0xb31134: r4 = LoadInt32Instr(r3)
    //     0xb31134: sbfx            x4, x3, #1, #0x1f
    //     0xb31138: tbz             w3, #0, #0xb31140
    //     0xb3113c: ldur            x4, [x3, #7]
    // 0xb31140: mov             x3, x4
    // 0xb31144: stur            x3, [fp, #-0x70]
    // 0xb31148: cmp             w2, NULL
    // 0xb3114c: b.ne            #0xb31158
    // 0xb31150: r4 = Null
    //     0xb31150: mov             x4, NULL
    // 0xb31154: b               #0xb31160
    // 0xb31158: LoadField: r4 = r2->field_b
    //     0xb31158: ldur            w4, [x2, #0xb]
    // 0xb3115c: DecompressPointer r4
    //     0xb3115c: add             x4, x4, HEAP, lsl #32
    // 0xb31160: cmp             w4, NULL
    // 0xb31164: b.ne            #0xb31170
    // 0xb31168: r4 = 0
    //     0xb31168: movz            x4, #0
    // 0xb3116c: b               #0xb31180
    // 0xb31170: r5 = LoadInt32Instr(r4)
    //     0xb31170: sbfx            x5, x4, #1, #0x1f
    //     0xb31174: tbz             w4, #0, #0xb3117c
    //     0xb31178: ldur            x5, [x4, #7]
    // 0xb3117c: mov             x4, x5
    // 0xb31180: stur            x4, [fp, #-0x68]
    // 0xb31184: cmp             w2, NULL
    // 0xb31188: b.ne            #0xb31194
    // 0xb3118c: r2 = Null
    //     0xb3118c: mov             x2, NULL
    // 0xb31190: b               #0xb311a0
    // 0xb31194: LoadField: r5 = r2->field_f
    //     0xb31194: ldur            w5, [x2, #0xf]
    // 0xb31198: DecompressPointer r5
    //     0xb31198: add             x5, x5, HEAP, lsl #32
    // 0xb3119c: mov             x2, x5
    // 0xb311a0: cmp             w2, NULL
    // 0xb311a4: b.ne            #0xb311b0
    // 0xb311a8: r5 = 0
    //     0xb311a8: movz            x5, #0
    // 0xb311ac: b               #0xb311bc
    // 0xb311b0: r5 = LoadInt32Instr(r2)
    //     0xb311b0: sbfx            x5, x2, #1, #0x1f
    //     0xb311b4: tbz             w2, #0, #0xb311bc
    //     0xb311b8: ldur            x5, [x2, #7]
    // 0xb311bc: ldur            x2, [fp, #-8]
    // 0xb311c0: stur            x5, [fp, #-0x60]
    // 0xb311c4: r0 = Color()
    //     0xb311c4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb311c8: mov             x3, x0
    // 0xb311cc: r0 = Instance_ColorSpace
    //     0xb311cc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb311d0: stur            x3, [fp, #-0x20]
    // 0xb311d4: StoreField: r3->field_27 = r0
    //     0xb311d4: stur            w0, [x3, #0x27]
    // 0xb311d8: d0 = 0.700000
    //     0xb311d8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb311dc: ldr             d0, [x17, #0xf48]
    // 0xb311e0: StoreField: r3->field_7 = d0
    //     0xb311e0: stur            d0, [x3, #7]
    // 0xb311e4: ldur            x0, [fp, #-0x70]
    // 0xb311e8: ubfx            x0, x0, #0, #0x20
    // 0xb311ec: and             w1, w0, #0xff
    // 0xb311f0: ubfx            x1, x1, #0, #0x20
    // 0xb311f4: scvtf           d1, x1
    // 0xb311f8: d2 = 255.000000
    //     0xb311f8: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb311fc: fdiv            d3, d1, d2
    // 0xb31200: StoreField: r3->field_f = d3
    //     0xb31200: stur            d3, [x3, #0xf]
    // 0xb31204: ldur            x0, [fp, #-0x68]
    // 0xb31208: ubfx            x0, x0, #0, #0x20
    // 0xb3120c: and             w1, w0, #0xff
    // 0xb31210: ubfx            x1, x1, #0, #0x20
    // 0xb31214: scvtf           d1, x1
    // 0xb31218: fdiv            d3, d1, d2
    // 0xb3121c: ArrayStore: r3[0] = d3  ; List_8
    //     0xb3121c: stur            d3, [x3, #0x17]
    // 0xb31220: ldur            x0, [fp, #-0x60]
    // 0xb31224: ubfx            x0, x0, #0, #0x20
    // 0xb31228: and             w1, w0, #0xff
    // 0xb3122c: ubfx            x1, x1, #0, #0x20
    // 0xb31230: scvtf           d1, x1
    // 0xb31234: fdiv            d3, d1, d2
    // 0xb31238: StoreField: r3->field_1f = d3
    //     0xb31238: stur            d3, [x3, #0x1f]
    // 0xb3123c: r1 = Null
    //     0xb3123c: mov             x1, NULL
    // 0xb31240: r2 = 4
    //     0xb31240: movz            x2, #0x4
    // 0xb31244: r0 = AllocateArray()
    //     0xb31244: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31248: mov             x2, x0
    // 0xb3124c: ldur            x0, [fp, #-0x38]
    // 0xb31250: stur            x2, [fp, #-0x40]
    // 0xb31254: StoreField: r2->field_f = r0
    //     0xb31254: stur            w0, [x2, #0xf]
    // 0xb31258: ldur            x0, [fp, #-0x20]
    // 0xb3125c: StoreField: r2->field_13 = r0
    //     0xb3125c: stur            w0, [x2, #0x13]
    // 0xb31260: r1 = <Color>
    //     0xb31260: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb31264: ldr             x1, [x1, #0xf80]
    // 0xb31268: r0 = AllocateGrowableArray()
    //     0xb31268: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3126c: mov             x1, x0
    // 0xb31270: ldur            x0, [fp, #-0x40]
    // 0xb31274: stur            x1, [fp, #-0x20]
    // 0xb31278: StoreField: r1->field_f = r0
    //     0xb31278: stur            w0, [x1, #0xf]
    // 0xb3127c: r2 = 4
    //     0xb3127c: movz            x2, #0x4
    // 0xb31280: StoreField: r1->field_b = r2
    //     0xb31280: stur            w2, [x1, #0xb]
    // 0xb31284: r0 = LinearGradient()
    //     0xb31284: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb31288: mov             x1, x0
    // 0xb3128c: r0 = Instance_Alignment
    //     0xb3128c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb31290: ldr             x0, [x0, #0xce0]
    // 0xb31294: stur            x1, [fp, #-0x38]
    // 0xb31298: StoreField: r1->field_13 = r0
    //     0xb31298: stur            w0, [x1, #0x13]
    // 0xb3129c: r0 = Instance_Alignment
    //     0xb3129c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb312a0: ldr             x0, [x0, #0xce8]
    // 0xb312a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb312a4: stur            w0, [x1, #0x17]
    // 0xb312a8: r0 = Instance_TileMode
    //     0xb312a8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb312ac: ldr             x0, [x0, #0xcf0]
    // 0xb312b0: StoreField: r1->field_1b = r0
    //     0xb312b0: stur            w0, [x1, #0x1b]
    // 0xb312b4: ldur            x0, [fp, #-0x20]
    // 0xb312b8: StoreField: r1->field_7 = r0
    //     0xb312b8: stur            w0, [x1, #7]
    // 0xb312bc: r0 = BoxDecoration()
    //     0xb312bc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb312c0: mov             x1, x0
    // 0xb312c4: r0 = Instance_BorderRadius
    //     0xb312c4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb312c8: ldr             x0, [x0, #0xe10]
    // 0xb312cc: stur            x1, [fp, #-0x20]
    // 0xb312d0: StoreField: r1->field_13 = r0
    //     0xb312d0: stur            w0, [x1, #0x13]
    // 0xb312d4: ldur            x0, [fp, #-0x38]
    // 0xb312d8: StoreField: r1->field_1b = r0
    //     0xb312d8: stur            w0, [x1, #0x1b]
    // 0xb312dc: r0 = Instance_BoxShape
    //     0xb312dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb312e0: ldr             x0, [x0, #0x80]
    // 0xb312e4: StoreField: r1->field_23 = r0
    //     0xb312e4: stur            w0, [x1, #0x23]
    // 0xb312e8: r0 = SvgPicture()
    //     0xb312e8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb312ec: stur            x0, [fp, #-0x38]
    // 0xb312f0: r16 = 40.000000
    //     0xb312f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb312f4: ldr             x16, [x16, #8]
    // 0xb312f8: r30 = 40.000000
    //     0xb312f8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb312fc: ldr             lr, [lr, #8]
    // 0xb31300: stp             lr, x16, [SP, #8]
    // 0xb31304: r16 = Instance_BoxFit
    //     0xb31304: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb31308: ldr             x16, [x16, #0xb18]
    // 0xb3130c: str             x16, [SP]
    // 0xb31310: mov             x1, x0
    // 0xb31314: r2 = "assets/images/offer_icon.svg"
    //     0xb31314: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xb31318: ldr             x2, [x2, #0x758]
    // 0xb3131c: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb3131c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb31320: ldr             x4, [x4, #0xb48]
    // 0xb31324: r0 = SvgPicture.asset()
    //     0xb31324: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb31328: r0 = Padding()
    //     0xb31328: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3132c: mov             x3, x0
    // 0xb31330: r0 = Instance_EdgeInsets
    //     0xb31330: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb31334: ldr             x0, [x0, #0x878]
    // 0xb31338: stur            x3, [fp, #-0x40]
    // 0xb3133c: StoreField: r3->field_f = r0
    //     0xb3133c: stur            w0, [x3, #0xf]
    // 0xb31340: ldur            x1, [fp, #-0x38]
    // 0xb31344: StoreField: r3->field_b = r1
    //     0xb31344: stur            w1, [x3, #0xb]
    // 0xb31348: r1 = Null
    //     0xb31348: mov             x1, NULL
    // 0xb3134c: r2 = 6
    //     0xb3134c: movz            x2, #0x6
    // 0xb31350: r0 = AllocateArray()
    //     0xb31350: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31354: r16 = "You\'ve saved "
    //     0xb31354: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xb31358: ldr             x16, [x16, #0xcf8]
    // 0xb3135c: StoreField: r0->field_f = r16
    //     0xb3135c: stur            w16, [x0, #0xf]
    // 0xb31360: ldur            x1, [fp, #-8]
    // 0xb31364: LoadField: r2 = r1->field_b
    //     0xb31364: ldur            w2, [x1, #0xb]
    // 0xb31368: DecompressPointer r2
    //     0xb31368: add             x2, x2, HEAP, lsl #32
    // 0xb3136c: cmp             w2, NULL
    // 0xb31370: b.eq            #0xb32a7c
    // 0xb31374: LoadField: r3 = r2->field_b
    //     0xb31374: ldur            w3, [x2, #0xb]
    // 0xb31378: DecompressPointer r3
    //     0xb31378: add             x3, x3, HEAP, lsl #32
    // 0xb3137c: cmp             w3, NULL
    // 0xb31380: b.ne            #0xb3138c
    // 0xb31384: r4 = Null
    //     0xb31384: mov             x4, NULL
    // 0xb31388: b               #0xb313b4
    // 0xb3138c: LoadField: r2 = r3->field_1b
    //     0xb3138c: ldur            w2, [x3, #0x1b]
    // 0xb31390: DecompressPointer r2
    //     0xb31390: add             x2, x2, HEAP, lsl #32
    // 0xb31394: cmp             w2, NULL
    // 0xb31398: b.ne            #0xb313a4
    // 0xb3139c: r2 = Null
    //     0xb3139c: mov             x2, NULL
    // 0xb313a0: b               #0xb313b0
    // 0xb313a4: LoadField: r3 = r2->field_f
    //     0xb313a4: ldur            w3, [x2, #0xf]
    // 0xb313a8: DecompressPointer r3
    //     0xb313a8: add             x3, x3, HEAP, lsl #32
    // 0xb313ac: mov             x2, x3
    // 0xb313b0: mov             x4, x2
    // 0xb313b4: ldur            x3, [fp, #-0x30]
    // 0xb313b8: ldur            x2, [fp, #-0x40]
    // 0xb313bc: StoreField: r0->field_13 = r4
    //     0xb313bc: stur            w4, [x0, #0x13]
    // 0xb313c0: r16 = "!"
    //     0xb313c0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb313c4: ldr             x16, [x16, #0xd00]
    // 0xb313c8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb313c8: stur            w16, [x0, #0x17]
    // 0xb313cc: str             x0, [SP]
    // 0xb313d0: r0 = _interpolate()
    //     0xb313d0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb313d4: ldur            x1, [fp, #-0x10]
    // 0xb313d8: stur            x0, [fp, #-0x38]
    // 0xb313dc: r0 = of()
    //     0xb313dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb313e0: LoadField: r1 = r0->field_87
    //     0xb313e0: ldur            w1, [x0, #0x87]
    // 0xb313e4: DecompressPointer r1
    //     0xb313e4: add             x1, x1, HEAP, lsl #32
    // 0xb313e8: LoadField: r0 = r1->field_7
    //     0xb313e8: ldur            w0, [x1, #7]
    // 0xb313ec: DecompressPointer r0
    //     0xb313ec: add             x0, x0, HEAP, lsl #32
    // 0xb313f0: r16 = 16.000000
    //     0xb313f0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb313f4: ldr             x16, [x16, #0x188]
    // 0xb313f8: r30 = Instance_Color
    //     0xb313f8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb313fc: stp             lr, x16, [SP]
    // 0xb31400: mov             x1, x0
    // 0xb31404: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb31404: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb31408: ldr             x4, [x4, #0xaa0]
    // 0xb3140c: r0 = copyWith()
    //     0xb3140c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb31410: stur            x0, [fp, #-0x48]
    // 0xb31414: r0 = Text()
    //     0xb31414: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31418: mov             x2, x0
    // 0xb3141c: ldur            x0, [fp, #-0x38]
    // 0xb31420: stur            x2, [fp, #-0x50]
    // 0xb31424: StoreField: r2->field_b = r0
    //     0xb31424: stur            w0, [x2, #0xb]
    // 0xb31428: ldur            x0, [fp, #-0x48]
    // 0xb3142c: StoreField: r2->field_13 = r0
    //     0xb3142c: stur            w0, [x2, #0x13]
    // 0xb31430: ldur            x1, [fp, #-0x10]
    // 0xb31434: r0 = of()
    //     0xb31434: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb31438: LoadField: r1 = r0->field_87
    //     0xb31438: ldur            w1, [x0, #0x87]
    // 0xb3143c: DecompressPointer r1
    //     0xb3143c: add             x1, x1, HEAP, lsl #32
    // 0xb31440: LoadField: r0 = r1->field_2b
    //     0xb31440: ldur            w0, [x1, #0x2b]
    // 0xb31444: DecompressPointer r0
    //     0xb31444: add             x0, x0, HEAP, lsl #32
    // 0xb31448: r16 = Instance_Color
    //     0xb31448: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3144c: r30 = 12.000000
    //     0xb3144c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb31450: ldr             lr, [lr, #0x9e8]
    // 0xb31454: stp             lr, x16, [SP]
    // 0xb31458: mov             x1, x0
    // 0xb3145c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3145c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb31460: ldr             x4, [x4, #0x9b8]
    // 0xb31464: r0 = copyWith()
    //     0xb31464: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb31468: stur            x0, [fp, #-0x38]
    // 0xb3146c: r0 = Text()
    //     0xb3146c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31470: mov             x3, x0
    // 0xb31474: r0 = "Bumper Offer Applied!"
    //     0xb31474: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xb31478: ldr             x0, [x0, #0xd08]
    // 0xb3147c: stur            x3, [fp, #-0x48]
    // 0xb31480: StoreField: r3->field_b = r0
    //     0xb31480: stur            w0, [x3, #0xb]
    // 0xb31484: ldur            x0, [fp, #-0x38]
    // 0xb31488: StoreField: r3->field_13 = r0
    //     0xb31488: stur            w0, [x3, #0x13]
    // 0xb3148c: r1 = Null
    //     0xb3148c: mov             x1, NULL
    // 0xb31490: r2 = 4
    //     0xb31490: movz            x2, #0x4
    // 0xb31494: r0 = AllocateArray()
    //     0xb31494: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31498: mov             x2, x0
    // 0xb3149c: ldur            x0, [fp, #-0x50]
    // 0xb314a0: stur            x2, [fp, #-0x38]
    // 0xb314a4: StoreField: r2->field_f = r0
    //     0xb314a4: stur            w0, [x2, #0xf]
    // 0xb314a8: ldur            x0, [fp, #-0x48]
    // 0xb314ac: StoreField: r2->field_13 = r0
    //     0xb314ac: stur            w0, [x2, #0x13]
    // 0xb314b0: r1 = <Widget>
    //     0xb314b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb314b4: r0 = AllocateGrowableArray()
    //     0xb314b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb314b8: mov             x1, x0
    // 0xb314bc: ldur            x0, [fp, #-0x38]
    // 0xb314c0: stur            x1, [fp, #-0x48]
    // 0xb314c4: StoreField: r1->field_f = r0
    //     0xb314c4: stur            w0, [x1, #0xf]
    // 0xb314c8: r2 = 4
    //     0xb314c8: movz            x2, #0x4
    // 0xb314cc: StoreField: r1->field_b = r2
    //     0xb314cc: stur            w2, [x1, #0xb]
    // 0xb314d0: r0 = Column()
    //     0xb314d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb314d4: mov             x2, x0
    // 0xb314d8: r0 = Instance_Axis
    //     0xb314d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb314dc: stur            x2, [fp, #-0x38]
    // 0xb314e0: StoreField: r2->field_f = r0
    //     0xb314e0: stur            w0, [x2, #0xf]
    // 0xb314e4: r1 = Instance_MainAxisAlignment
    //     0xb314e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb314e8: ldr             x1, [x1, #0xab0]
    // 0xb314ec: StoreField: r2->field_13 = r1
    //     0xb314ec: stur            w1, [x2, #0x13]
    // 0xb314f0: r3 = Instance_MainAxisSize
    //     0xb314f0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb314f4: ldr             x3, [x3, #0xa10]
    // 0xb314f8: ArrayStore: r2[0] = r3  ; List_4
    //     0xb314f8: stur            w3, [x2, #0x17]
    // 0xb314fc: r4 = Instance_CrossAxisAlignment
    //     0xb314fc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb31500: ldr             x4, [x4, #0x890]
    // 0xb31504: StoreField: r2->field_1b = r4
    //     0xb31504: stur            w4, [x2, #0x1b]
    // 0xb31508: r5 = Instance_VerticalDirection
    //     0xb31508: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3150c: ldr             x5, [x5, #0xa20]
    // 0xb31510: StoreField: r2->field_23 = r5
    //     0xb31510: stur            w5, [x2, #0x23]
    // 0xb31514: r6 = Instance_Clip
    //     0xb31514: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb31518: ldr             x6, [x6, #0x38]
    // 0xb3151c: StoreField: r2->field_2b = r6
    //     0xb3151c: stur            w6, [x2, #0x2b]
    // 0xb31520: StoreField: r2->field_2f = rZR
    //     0xb31520: stur            xzr, [x2, #0x2f]
    // 0xb31524: ldur            x1, [fp, #-0x48]
    // 0xb31528: StoreField: r2->field_b = r1
    //     0xb31528: stur            w1, [x2, #0xb]
    // 0xb3152c: r1 = <FlexParentData>
    //     0xb3152c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb31530: ldr             x1, [x1, #0xe00]
    // 0xb31534: r0 = Expanded()
    //     0xb31534: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb31538: stur            x0, [fp, #-0x48]
    // 0xb3153c: StoreField: r0->field_13 = rZR
    //     0xb3153c: stur            xzr, [x0, #0x13]
    // 0xb31540: r2 = Instance_FlexFit
    //     0xb31540: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb31544: ldr             x2, [x2, #0xe08]
    // 0xb31548: StoreField: r0->field_1b = r2
    //     0xb31548: stur            w2, [x0, #0x1b]
    // 0xb3154c: ldur            x1, [fp, #-0x38]
    // 0xb31550: StoreField: r0->field_b = r1
    //     0xb31550: stur            w1, [x0, #0xb]
    // 0xb31554: ldur            x1, [fp, #-0x10]
    // 0xb31558: r0 = of()
    //     0xb31558: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3155c: LoadField: r1 = r0->field_87
    //     0xb3155c: ldur            w1, [x0, #0x87]
    // 0xb31560: DecompressPointer r1
    //     0xb31560: add             x1, x1, HEAP, lsl #32
    // 0xb31564: LoadField: r0 = r1->field_7
    //     0xb31564: ldur            w0, [x1, #7]
    // 0xb31568: DecompressPointer r0
    //     0xb31568: add             x0, x0, HEAP, lsl #32
    // 0xb3156c: r16 = 14.000000
    //     0xb3156c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb31570: ldr             x16, [x16, #0x1d8]
    // 0xb31574: r30 = Instance_Color
    //     0xb31574: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb31578: stp             lr, x16, [SP]
    // 0xb3157c: mov             x1, x0
    // 0xb31580: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb31580: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb31584: ldr             x4, [x4, #0xaa0]
    // 0xb31588: r0 = copyWith()
    //     0xb31588: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3158c: stur            x0, [fp, #-0x38]
    // 0xb31590: r0 = Text()
    //     0xb31590: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31594: mov             x1, x0
    // 0xb31598: r0 = "CHANGE"
    //     0xb31598: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb3159c: ldr             x0, [x0, #0xd10]
    // 0xb315a0: stur            x1, [fp, #-0x50]
    // 0xb315a4: StoreField: r1->field_b = r0
    //     0xb315a4: stur            w0, [x1, #0xb]
    // 0xb315a8: ldur            x0, [fp, #-0x38]
    // 0xb315ac: StoreField: r1->field_13 = r0
    //     0xb315ac: stur            w0, [x1, #0x13]
    // 0xb315b0: r0 = InkWell()
    //     0xb315b0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb315b4: mov             x3, x0
    // 0xb315b8: ldur            x0, [fp, #-0x50]
    // 0xb315bc: stur            x3, [fp, #-0x38]
    // 0xb315c0: StoreField: r3->field_b = r0
    //     0xb315c0: stur            w0, [x3, #0xb]
    // 0xb315c4: ldur            x2, [fp, #-0x18]
    // 0xb315c8: r1 = Function '<anonymous closure>':.
    //     0xb315c8: add             x1, PP, #0x57, lsl #12  ; [pp+0x571e0] AnonymousClosure: (0xb32bdc), in [package:customer_app/app/presentation/views/glass/bag/bottom_view.dart] _BottomViewState::build (0xb30868)
    //     0xb315cc: ldr             x1, [x1, #0x1e0]
    // 0xb315d0: r0 = AllocateClosure()
    //     0xb315d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb315d4: mov             x1, x0
    // 0xb315d8: ldur            x0, [fp, #-0x38]
    // 0xb315dc: StoreField: r0->field_f = r1
    //     0xb315dc: stur            w1, [x0, #0xf]
    // 0xb315e0: r1 = true
    //     0xb315e0: add             x1, NULL, #0x20  ; true
    // 0xb315e4: StoreField: r0->field_43 = r1
    //     0xb315e4: stur            w1, [x0, #0x43]
    // 0xb315e8: r3 = Instance_BoxShape
    //     0xb315e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb315ec: ldr             x3, [x3, #0x80]
    // 0xb315f0: StoreField: r0->field_47 = r3
    //     0xb315f0: stur            w3, [x0, #0x47]
    // 0xb315f4: StoreField: r0->field_6f = r1
    //     0xb315f4: stur            w1, [x0, #0x6f]
    // 0xb315f8: r2 = false
    //     0xb315f8: add             x2, NULL, #0x30  ; false
    // 0xb315fc: StoreField: r0->field_73 = r2
    //     0xb315fc: stur            w2, [x0, #0x73]
    // 0xb31600: StoreField: r0->field_83 = r1
    //     0xb31600: stur            w1, [x0, #0x83]
    // 0xb31604: StoreField: r0->field_7b = r2
    //     0xb31604: stur            w2, [x0, #0x7b]
    // 0xb31608: r0 = Padding()
    //     0xb31608: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3160c: r4 = Instance_EdgeInsets
    //     0xb3160c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb31610: ldr             x4, [x4, #0x878]
    // 0xb31614: stur            x0, [fp, #-0x50]
    // 0xb31618: StoreField: r0->field_f = r4
    //     0xb31618: stur            w4, [x0, #0xf]
    // 0xb3161c: ldur            x1, [fp, #-0x38]
    // 0xb31620: StoreField: r0->field_b = r1
    //     0xb31620: stur            w1, [x0, #0xb]
    // 0xb31624: r1 = Null
    //     0xb31624: mov             x1, NULL
    // 0xb31628: r2 = 10
    //     0xb31628: movz            x2, #0xa
    // 0xb3162c: r0 = AllocateArray()
    //     0xb3162c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31630: mov             x2, x0
    // 0xb31634: ldur            x0, [fp, #-0x40]
    // 0xb31638: stur            x2, [fp, #-0x38]
    // 0xb3163c: StoreField: r2->field_f = r0
    //     0xb3163c: stur            w0, [x2, #0xf]
    // 0xb31640: r16 = Instance_SizedBox
    //     0xb31640: add             x16, PP, #0x40, lsl #12  ; [pp+0x40890] Obj!SizedBox@d67f81
    //     0xb31644: ldr             x16, [x16, #0x890]
    // 0xb31648: StoreField: r2->field_13 = r16
    //     0xb31648: stur            w16, [x2, #0x13]
    // 0xb3164c: ldur            x0, [fp, #-0x48]
    // 0xb31650: ArrayStore: r2[0] = r0  ; List_4
    //     0xb31650: stur            w0, [x2, #0x17]
    // 0xb31654: r16 = Instance_Spacer
    //     0xb31654: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb31658: ldr             x16, [x16, #0xf0]
    // 0xb3165c: StoreField: r2->field_1b = r16
    //     0xb3165c: stur            w16, [x2, #0x1b]
    // 0xb31660: ldur            x0, [fp, #-0x50]
    // 0xb31664: StoreField: r2->field_1f = r0
    //     0xb31664: stur            w0, [x2, #0x1f]
    // 0xb31668: r1 = <Widget>
    //     0xb31668: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3166c: r0 = AllocateGrowableArray()
    //     0xb3166c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb31670: mov             x1, x0
    // 0xb31674: ldur            x0, [fp, #-0x38]
    // 0xb31678: stur            x1, [fp, #-0x40]
    // 0xb3167c: StoreField: r1->field_f = r0
    //     0xb3167c: stur            w0, [x1, #0xf]
    // 0xb31680: r0 = 10
    //     0xb31680: movz            x0, #0xa
    // 0xb31684: StoreField: r1->field_b = r0
    //     0xb31684: stur            w0, [x1, #0xb]
    // 0xb31688: r0 = Row()
    //     0xb31688: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb3168c: mov             x1, x0
    // 0xb31690: r0 = Instance_Axis
    //     0xb31690: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb31694: stur            x1, [fp, #-0x38]
    // 0xb31698: StoreField: r1->field_f = r0
    //     0xb31698: stur            w0, [x1, #0xf]
    // 0xb3169c: r5 = Instance_MainAxisAlignment
    //     0xb3169c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb316a0: ldr             x5, [x5, #0xd10]
    // 0xb316a4: StoreField: r1->field_13 = r5
    //     0xb316a4: stur            w5, [x1, #0x13]
    // 0xb316a8: r2 = Instance_MainAxisSize
    //     0xb316a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb316ac: ldr             x2, [x2, #0xa10]
    // 0xb316b0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb316b0: stur            w2, [x1, #0x17]
    // 0xb316b4: r3 = Instance_CrossAxisAlignment
    //     0xb316b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb316b8: ldr             x3, [x3, #0xa18]
    // 0xb316bc: StoreField: r1->field_1b = r3
    //     0xb316bc: stur            w3, [x1, #0x1b]
    // 0xb316c0: r4 = Instance_VerticalDirection
    //     0xb316c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb316c4: ldr             x4, [x4, #0xa20]
    // 0xb316c8: StoreField: r1->field_23 = r4
    //     0xb316c8: stur            w4, [x1, #0x23]
    // 0xb316cc: r5 = Instance_Clip
    //     0xb316cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb316d0: ldr             x5, [x5, #0x38]
    // 0xb316d4: StoreField: r1->field_2b = r5
    //     0xb316d4: stur            w5, [x1, #0x2b]
    // 0xb316d8: StoreField: r1->field_2f = rZR
    //     0xb316d8: stur            xzr, [x1, #0x2f]
    // 0xb316dc: ldur            x6, [fp, #-0x40]
    // 0xb316e0: StoreField: r1->field_b = r6
    //     0xb316e0: stur            w6, [x1, #0xb]
    // 0xb316e4: r0 = Container()
    //     0xb316e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb316e8: stur            x0, [fp, #-0x40]
    // 0xb316ec: r16 = 100.000000
    //     0xb316ec: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb316f0: ldur            lr, [fp, #-0x20]
    // 0xb316f4: stp             lr, x16, [SP, #8]
    // 0xb316f8: ldur            x16, [fp, #-0x38]
    // 0xb316fc: str             x16, [SP]
    // 0xb31700: mov             x1, x0
    // 0xb31704: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb31704: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb31708: ldr             x4, [x4, #0xc78]
    // 0xb3170c: r0 = Container()
    //     0xb3170c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb31710: r1 = <Path>
    //     0xb31710: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb31714: ldr             x1, [x1, #0xd30]
    // 0xb31718: r0 = MovieTicketClipper()
    //     0xb31718: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb3171c: stur            x0, [fp, #-0x20]
    // 0xb31720: r0 = ClipPath()
    //     0xb31720: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb31724: mov             x1, x0
    // 0xb31728: ldur            x0, [fp, #-0x20]
    // 0xb3172c: stur            x1, [fp, #-0x38]
    // 0xb31730: StoreField: r1->field_f = r0
    //     0xb31730: stur            w0, [x1, #0xf]
    // 0xb31734: r0 = Instance_Clip
    //     0xb31734: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb31738: ldr             x0, [x0, #0x138]
    // 0xb3173c: StoreField: r1->field_13 = r0
    //     0xb3173c: stur            w0, [x1, #0x13]
    // 0xb31740: ldur            x0, [fp, #-0x40]
    // 0xb31744: StoreField: r1->field_b = r0
    //     0xb31744: stur            w0, [x1, #0xb]
    // 0xb31748: r0 = Padding()
    //     0xb31748: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3174c: r6 = Instance_EdgeInsets
    //     0xb3174c: add             x6, PP, #0x3f, lsl #12  ; [pp+0x3fce8] Obj!EdgeInsets@d58131
    //     0xb31750: ldr             x6, [x6, #0xce8]
    // 0xb31754: stur            x0, [fp, #-0x20]
    // 0xb31758: StoreField: r0->field_f = r6
    //     0xb31758: stur            w6, [x0, #0xf]
    // 0xb3175c: ldur            x1, [fp, #-0x38]
    // 0xb31760: StoreField: r0->field_b = r1
    //     0xb31760: stur            w1, [x0, #0xb]
    // 0xb31764: r0 = Visibility()
    //     0xb31764: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb31768: mov             x1, x0
    // 0xb3176c: ldur            x0, [fp, #-0x20]
    // 0xb31770: StoreField: r1->field_b = r0
    //     0xb31770: stur            w0, [x1, #0xb]
    // 0xb31774: r7 = Instance_SizedBox
    //     0xb31774: ldr             x7, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb31778: StoreField: r1->field_f = r7
    //     0xb31778: stur            w7, [x1, #0xf]
    // 0xb3177c: ldur            x0, [fp, #-0x30]
    // 0xb31780: StoreField: r1->field_13 = r0
    //     0xb31780: stur            w0, [x1, #0x13]
    // 0xb31784: r8 = false
    //     0xb31784: add             x8, NULL, #0x30  ; false
    // 0xb31788: ArrayStore: r1[0] = r8  ; List_4
    //     0xb31788: stur            w8, [x1, #0x17]
    // 0xb3178c: StoreField: r1->field_1b = r8
    //     0xb3178c: stur            w8, [x1, #0x1b]
    // 0xb31790: StoreField: r1->field_1f = r8
    //     0xb31790: stur            w8, [x1, #0x1f]
    // 0xb31794: StoreField: r1->field_23 = r8
    //     0xb31794: stur            w8, [x1, #0x23]
    // 0xb31798: StoreField: r1->field_27 = r8
    //     0xb31798: stur            w8, [x1, #0x27]
    // 0xb3179c: StoreField: r1->field_2b = r8
    //     0xb3179c: stur            w8, [x1, #0x2b]
    // 0xb317a0: mov             x4, x1
    // 0xb317a4: mov             x2, x8
    // 0xb317a8: mov             x0, x7
    // 0xb317ac: b               #0xb32068
    // 0xb317b0: r4 = Instance_EdgeInsets
    //     0xb317b0: add             x4, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb317b4: ldr             x4, [x4, #0x878]
    // 0xb317b8: r1 = Instance_MainAxisAlignment
    //     0xb317b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb317bc: ldr             x1, [x1, #0xab0]
    // 0xb317c0: r0 = "CHANGE"
    //     0xb317c0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb317c4: ldr             x0, [x0, #0xd10]
    // 0xb317c8: r5 = Instance_MainAxisAlignment
    //     0xb317c8: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb317cc: ldr             x5, [x5, #0xd10]
    // 0xb317d0: r6 = Instance_EdgeInsets
    //     0xb317d0: add             x6, PP, #0x3f, lsl #12  ; [pp+0x3fce8] Obj!EdgeInsets@d58131
    //     0xb317d4: ldr             x6, [x6, #0xce8]
    // 0xb317d8: r8 = false
    //     0xb317d8: add             x8, NULL, #0x30  ; false
    // 0xb317dc: r7 = Instance_SizedBox
    //     0xb317dc: ldr             x7, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb317e0: r2 = Instance_FlexFit
    //     0xb317e0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb317e4: ldr             x2, [x2, #0xe08]
    // 0xb317e8: r3 = Instance_BoxShape
    //     0xb317e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb317ec: ldr             x3, [x3, #0x80]
    // 0xb317f0: b               #0xb31820
    // 0xb317f4: mov             x2, x4
    // 0xb317f8: mov             x4, x7
    // 0xb317fc: mov             x1, x3
    // 0xb31800: mov             x3, x5
    // 0xb31804: mov             x5, x6
    // 0xb31808: r0 = "CHANGE"
    //     0xb31808: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb3180c: ldr             x0, [x0, #0xd10]
    // 0xb31810: r6 = Instance_EdgeInsets
    //     0xb31810: add             x6, PP, #0x3f, lsl #12  ; [pp+0x3fce8] Obj!EdgeInsets@d58131
    //     0xb31814: ldr             x6, [x6, #0xce8]
    // 0xb31818: r8 = false
    //     0xb31818: add             x8, NULL, #0x30  ; false
    // 0xb3181c: r7 = Instance_SizedBox
    //     0xb3181c: ldr             x7, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb31820: ldur            x9, [fp, #-8]
    // 0xb31824: LoadField: r10 = r9->field_b
    //     0xb31824: ldur            w10, [x9, #0xb]
    // 0xb31828: DecompressPointer r10
    //     0xb31828: add             x10, x10, HEAP, lsl #32
    // 0xb3182c: cmp             w10, NULL
    // 0xb31830: b.eq            #0xb32a80
    // 0xb31834: ArrayLoad: r11 = r10[0]  ; List_4
    //     0xb31834: ldur            w11, [x10, #0x17]
    // 0xb31838: DecompressPointer r11
    //     0xb31838: add             x11, x11, HEAP, lsl #32
    // 0xb3183c: cmp             w11, NULL
    // 0xb31840: b.ne            #0xb3184c
    // 0xb31844: r10 = Null
    //     0xb31844: mov             x10, NULL
    // 0xb31848: b               #0xb31868
    // 0xb3184c: LoadField: r10 = r11->field_f
    //     0xb3184c: ldur            w10, [x11, #0xf]
    // 0xb31850: DecompressPointer r10
    //     0xb31850: add             x10, x10, HEAP, lsl #32
    // 0xb31854: LoadField: r11 = r10->field_b
    //     0xb31854: ldur            w11, [x10, #0xb]
    // 0xb31858: cbnz            w11, #0xb31864
    // 0xb3185c: r10 = false
    //     0xb3185c: add             x10, NULL, #0x30  ; false
    // 0xb31860: b               #0xb31868
    // 0xb31864: r10 = true
    //     0xb31864: add             x10, NULL, #0x20  ; true
    // 0xb31868: cmp             w10, NULL
    // 0xb3186c: b.ne            #0xb31874
    // 0xb31870: r10 = false
    //     0xb31870: add             x10, NULL, #0x30  ; false
    // 0xb31874: stur            x10, [fp, #-0x20]
    // 0xb31878: r0 = Radius()
    //     0xb31878: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3187c: d0 = 12.000000
    //     0xb3187c: fmov            d0, #12.00000000
    // 0xb31880: stur            x0, [fp, #-0x30]
    // 0xb31884: StoreField: r0->field_7 = d0
    //     0xb31884: stur            d0, [x0, #7]
    // 0xb31888: StoreField: r0->field_f = d0
    //     0xb31888: stur            d0, [x0, #0xf]
    // 0xb3188c: r0 = BorderRadius()
    //     0xb3188c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb31890: mov             x1, x0
    // 0xb31894: ldur            x0, [fp, #-0x30]
    // 0xb31898: stur            x1, [fp, #-0x38]
    // 0xb3189c: StoreField: r1->field_7 = r0
    //     0xb3189c: stur            w0, [x1, #7]
    // 0xb318a0: StoreField: r1->field_b = r0
    //     0xb318a0: stur            w0, [x1, #0xb]
    // 0xb318a4: StoreField: r1->field_f = r0
    //     0xb318a4: stur            w0, [x1, #0xf]
    // 0xb318a8: StoreField: r1->field_13 = r0
    //     0xb318a8: stur            w0, [x1, #0x13]
    // 0xb318ac: r0 = BoxDecoration()
    //     0xb318ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb318b0: mov             x3, x0
    // 0xb318b4: r0 = Instance_Color
    //     0xb318b4: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb318b8: ldr             x0, [x0, #0x860]
    // 0xb318bc: stur            x3, [fp, #-0x30]
    // 0xb318c0: StoreField: r3->field_7 = r0
    //     0xb318c0: stur            w0, [x3, #7]
    // 0xb318c4: ldur            x0, [fp, #-0x38]
    // 0xb318c8: StoreField: r3->field_13 = r0
    //     0xb318c8: stur            w0, [x3, #0x13]
    // 0xb318cc: r0 = Instance_BoxShape
    //     0xb318cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb318d0: ldr             x0, [x0, #0x80]
    // 0xb318d4: StoreField: r3->field_23 = r0
    //     0xb318d4: stur            w0, [x3, #0x23]
    // 0xb318d8: r1 = <Widget>
    //     0xb318d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb318dc: r2 = 0
    //     0xb318dc: movz            x2, #0
    // 0xb318e0: r0 = _GrowableList()
    //     0xb318e0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb318e4: mov             x1, x0
    // 0xb318e8: ldur            x0, [fp, #-8]
    // 0xb318ec: stur            x1, [fp, #-0x38]
    // 0xb318f0: LoadField: r2 = r0->field_b
    //     0xb318f0: ldur            w2, [x0, #0xb]
    // 0xb318f4: DecompressPointer r2
    //     0xb318f4: add             x2, x2, HEAP, lsl #32
    // 0xb318f8: cmp             w2, NULL
    // 0xb318fc: b.eq            #0xb32a84
    // 0xb31900: LoadField: r3 = r2->field_b
    //     0xb31900: ldur            w3, [x2, #0xb]
    // 0xb31904: DecompressPointer r3
    //     0xb31904: add             x3, x3, HEAP, lsl #32
    // 0xb31908: cmp             w3, NULL
    // 0xb3190c: b.ne            #0xb31918
    // 0xb31910: r2 = Null
    //     0xb31910: mov             x2, NULL
    // 0xb31914: b               #0xb3193c
    // 0xb31918: LoadField: r2 = r3->field_2f
    //     0xb31918: ldur            w2, [x3, #0x2f]
    // 0xb3191c: DecompressPointer r2
    //     0xb3191c: add             x2, x2, HEAP, lsl #32
    // 0xb31920: cmp             w2, NULL
    // 0xb31924: b.ne            #0xb31930
    // 0xb31928: r2 = Null
    //     0xb31928: mov             x2, NULL
    // 0xb3192c: b               #0xb3193c
    // 0xb31930: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb31930: ldur            w3, [x2, #0x17]
    // 0xb31934: DecompressPointer r3
    //     0xb31934: add             x3, x3, HEAP, lsl #32
    // 0xb31938: mov             x2, x3
    // 0xb3193c: cmp             w2, NULL
    // 0xb31940: b.ne            #0xb31954
    // 0xb31944: mov             x2, x1
    // 0xb31948: r0 = Instance_EdgeInsets
    //     0xb31948: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb3194c: ldr             x0, [x0, #0x878]
    // 0xb31950: b               #0xb31a30
    // 0xb31954: tbnz            w2, #4, #0xb31a24
    // 0xb31958: r0 = SvgPicture()
    //     0xb31958: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb3195c: stur            x0, [fp, #-0x40]
    // 0xb31960: r16 = 30.000000
    //     0xb31960: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb31964: ldr             x16, [x16, #0x768]
    // 0xb31968: str             x16, [SP]
    // 0xb3196c: mov             x1, x0
    // 0xb31970: r2 = "assets/images/gift-icon-popup.svg"
    //     0xb31970: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xb31974: ldr             x2, [x2, #0x8e8]
    // 0xb31978: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0xb31978: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0xb3197c: ldr             x4, [x4, #0x760]
    // 0xb31980: r0 = SvgPicture.asset()
    //     0xb31980: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb31984: r0 = Padding()
    //     0xb31984: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb31988: mov             x2, x0
    // 0xb3198c: r0 = Instance_EdgeInsets
    //     0xb3198c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb31990: ldr             x0, [x0, #0x878]
    // 0xb31994: stur            x2, [fp, #-0x48]
    // 0xb31998: StoreField: r2->field_f = r0
    //     0xb31998: stur            w0, [x2, #0xf]
    // 0xb3199c: ldur            x0, [fp, #-0x40]
    // 0xb319a0: StoreField: r2->field_b = r0
    //     0xb319a0: stur            w0, [x2, #0xb]
    // 0xb319a4: ldur            x0, [fp, #-0x38]
    // 0xb319a8: LoadField: r1 = r0->field_b
    //     0xb319a8: ldur            w1, [x0, #0xb]
    // 0xb319ac: LoadField: r3 = r0->field_f
    //     0xb319ac: ldur            w3, [x0, #0xf]
    // 0xb319b0: DecompressPointer r3
    //     0xb319b0: add             x3, x3, HEAP, lsl #32
    // 0xb319b4: LoadField: r4 = r3->field_b
    //     0xb319b4: ldur            w4, [x3, #0xb]
    // 0xb319b8: r3 = LoadInt32Instr(r1)
    //     0xb319b8: sbfx            x3, x1, #1, #0x1f
    // 0xb319bc: stur            x3, [fp, #-0x60]
    // 0xb319c0: r1 = LoadInt32Instr(r4)
    //     0xb319c0: sbfx            x1, x4, #1, #0x1f
    // 0xb319c4: cmp             x3, x1
    // 0xb319c8: b.ne            #0xb319d4
    // 0xb319cc: mov             x1, x0
    // 0xb319d0: r0 = _growToNextCapacity()
    //     0xb319d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb319d4: ldur            x2, [fp, #-0x38]
    // 0xb319d8: ldur            x3, [fp, #-0x60]
    // 0xb319dc: add             x0, x3, #1
    // 0xb319e0: lsl             x1, x0, #1
    // 0xb319e4: StoreField: r2->field_b = r1
    //     0xb319e4: stur            w1, [x2, #0xb]
    // 0xb319e8: LoadField: r1 = r2->field_f
    //     0xb319e8: ldur            w1, [x2, #0xf]
    // 0xb319ec: DecompressPointer r1
    //     0xb319ec: add             x1, x1, HEAP, lsl #32
    // 0xb319f0: ldur            x0, [fp, #-0x48]
    // 0xb319f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb319f4: add             x25, x1, x3, lsl #2
    //     0xb319f8: add             x25, x25, #0xf
    //     0xb319fc: str             w0, [x25]
    //     0xb31a00: tbz             w0, #0, #0xb31a1c
    //     0xb31a04: ldurb           w16, [x1, #-1]
    //     0xb31a08: ldurb           w17, [x0, #-1]
    //     0xb31a0c: and             x16, x17, x16, lsr #2
    //     0xb31a10: tst             x16, HEAP, lsr #32
    //     0xb31a14: b.eq            #0xb31a1c
    //     0xb31a18: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb31a1c: mov             x3, x2
    // 0xb31a20: b               #0xb31b08
    // 0xb31a24: mov             x2, x1
    // 0xb31a28: r0 = Instance_EdgeInsets
    //     0xb31a28: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb31a2c: ldr             x0, [x0, #0x878]
    // 0xb31a30: r0 = SvgPicture()
    //     0xb31a30: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb31a34: stur            x0, [fp, #-0x40]
    // 0xb31a38: r16 = 40.000000
    //     0xb31a38: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb31a3c: ldr             x16, [x16, #8]
    // 0xb31a40: r30 = 40.000000
    //     0xb31a40: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb31a44: ldr             lr, [lr, #8]
    // 0xb31a48: stp             lr, x16, [SP, #8]
    // 0xb31a4c: r16 = Instance_BoxFit
    //     0xb31a4c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb31a50: ldr             x16, [x16, #0xb18]
    // 0xb31a54: str             x16, [SP]
    // 0xb31a58: mov             x1, x0
    // 0xb31a5c: r2 = "assets/images/offer_icon.svg"
    //     0xb31a5c: add             x2, PP, #0x43, lsl #12  ; [pp+0x43758] "assets/images/offer_icon.svg"
    //     0xb31a60: ldr             x2, [x2, #0x758]
    // 0xb31a64: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xb31a64: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xb31a68: ldr             x4, [x4, #0xb48]
    // 0xb31a6c: r0 = SvgPicture.asset()
    //     0xb31a6c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb31a70: r0 = Padding()
    //     0xb31a70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb31a74: mov             x2, x0
    // 0xb31a78: r0 = Instance_EdgeInsets
    //     0xb31a78: add             x0, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb31a7c: ldr             x0, [x0, #0x878]
    // 0xb31a80: stur            x2, [fp, #-0x48]
    // 0xb31a84: StoreField: r2->field_f = r0
    //     0xb31a84: stur            w0, [x2, #0xf]
    // 0xb31a88: ldur            x0, [fp, #-0x40]
    // 0xb31a8c: StoreField: r2->field_b = r0
    //     0xb31a8c: stur            w0, [x2, #0xb]
    // 0xb31a90: ldur            x0, [fp, #-0x38]
    // 0xb31a94: LoadField: r1 = r0->field_b
    //     0xb31a94: ldur            w1, [x0, #0xb]
    // 0xb31a98: LoadField: r3 = r0->field_f
    //     0xb31a98: ldur            w3, [x0, #0xf]
    // 0xb31a9c: DecompressPointer r3
    //     0xb31a9c: add             x3, x3, HEAP, lsl #32
    // 0xb31aa0: LoadField: r4 = r3->field_b
    //     0xb31aa0: ldur            w4, [x3, #0xb]
    // 0xb31aa4: r3 = LoadInt32Instr(r1)
    //     0xb31aa4: sbfx            x3, x1, #1, #0x1f
    // 0xb31aa8: stur            x3, [fp, #-0x60]
    // 0xb31aac: r1 = LoadInt32Instr(r4)
    //     0xb31aac: sbfx            x1, x4, #1, #0x1f
    // 0xb31ab0: cmp             x3, x1
    // 0xb31ab4: b.ne            #0xb31ac0
    // 0xb31ab8: mov             x1, x0
    // 0xb31abc: r0 = _growToNextCapacity()
    //     0xb31abc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb31ac0: ldur            x3, [fp, #-0x38]
    // 0xb31ac4: ldur            x2, [fp, #-0x60]
    // 0xb31ac8: add             x0, x2, #1
    // 0xb31acc: lsl             x1, x0, #1
    // 0xb31ad0: StoreField: r3->field_b = r1
    //     0xb31ad0: stur            w1, [x3, #0xb]
    // 0xb31ad4: LoadField: r1 = r3->field_f
    //     0xb31ad4: ldur            w1, [x3, #0xf]
    // 0xb31ad8: DecompressPointer r1
    //     0xb31ad8: add             x1, x1, HEAP, lsl #32
    // 0xb31adc: ldur            x0, [fp, #-0x48]
    // 0xb31ae0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb31ae0: add             x25, x1, x2, lsl #2
    //     0xb31ae4: add             x25, x25, #0xf
    //     0xb31ae8: str             w0, [x25]
    //     0xb31aec: tbz             w0, #0, #0xb31b08
    //     0xb31af0: ldurb           w16, [x1, #-1]
    //     0xb31af4: ldurb           w17, [x0, #-1]
    //     0xb31af8: and             x16, x17, x16, lsr #2
    //     0xb31afc: tst             x16, HEAP, lsr #32
    //     0xb31b00: b.eq            #0xb31b08
    //     0xb31b04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb31b08: ldur            x0, [fp, #-8]
    // 0xb31b0c: LoadField: r1 = r0->field_b
    //     0xb31b0c: ldur            w1, [x0, #0xb]
    // 0xb31b10: DecompressPointer r1
    //     0xb31b10: add             x1, x1, HEAP, lsl #32
    // 0xb31b14: cmp             w1, NULL
    // 0xb31b18: b.eq            #0xb32a88
    // 0xb31b1c: LoadField: r4 = r1->field_b
    //     0xb31b1c: ldur            w4, [x1, #0xb]
    // 0xb31b20: DecompressPointer r4
    //     0xb31b20: add             x4, x4, HEAP, lsl #32
    // 0xb31b24: stur            x4, [fp, #-0x40]
    // 0xb31b28: cmp             w4, NULL
    // 0xb31b2c: b.ne            #0xb31b38
    // 0xb31b30: r1 = Null
    //     0xb31b30: mov             x1, NULL
    // 0xb31b34: b               #0xb31b5c
    // 0xb31b38: LoadField: r1 = r4->field_2f
    //     0xb31b38: ldur            w1, [x4, #0x2f]
    // 0xb31b3c: DecompressPointer r1
    //     0xb31b3c: add             x1, x1, HEAP, lsl #32
    // 0xb31b40: cmp             w1, NULL
    // 0xb31b44: b.ne            #0xb31b50
    // 0xb31b48: r1 = Null
    //     0xb31b48: mov             x1, NULL
    // 0xb31b4c: b               #0xb31b5c
    // 0xb31b50: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb31b50: ldur            w2, [x1, #0x17]
    // 0xb31b54: DecompressPointer r2
    //     0xb31b54: add             x2, x2, HEAP, lsl #32
    // 0xb31b58: mov             x1, x2
    // 0xb31b5c: cmp             w1, NULL
    // 0xb31b60: b.eq            #0xb31b78
    // 0xb31b64: tbnz            w1, #4, #0xb31b78
    // 0xb31b68: mov             x0, x3
    // 0xb31b6c: r2 = "Free Gift Added!"
    //     0xb31b6c: add             x2, PP, #0x54, lsl #12  ; [pp+0x54428] "Free Gift Added!"
    //     0xb31b70: ldr             x2, [x2, #0x428]
    // 0xb31b74: b               #0xb31be4
    // 0xb31b78: r1 = Null
    //     0xb31b78: mov             x1, NULL
    // 0xb31b7c: r2 = 6
    //     0xb31b7c: movz            x2, #0x6
    // 0xb31b80: r0 = AllocateArray()
    //     0xb31b80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31b84: r16 = "You\'ve saved "
    //     0xb31b84: add             x16, PP, #0x38, lsl #12  ; [pp+0x38cf8] "You\'ve saved "
    //     0xb31b88: ldr             x16, [x16, #0xcf8]
    // 0xb31b8c: StoreField: r0->field_f = r16
    //     0xb31b8c: stur            w16, [x0, #0xf]
    // 0xb31b90: ldur            x1, [fp, #-0x40]
    // 0xb31b94: cmp             w1, NULL
    // 0xb31b98: b.ne            #0xb31ba4
    // 0xb31b9c: r1 = Null
    //     0xb31b9c: mov             x1, NULL
    // 0xb31ba0: b               #0xb31bc4
    // 0xb31ba4: LoadField: r2 = r1->field_1b
    //     0xb31ba4: ldur            w2, [x1, #0x1b]
    // 0xb31ba8: DecompressPointer r2
    //     0xb31ba8: add             x2, x2, HEAP, lsl #32
    // 0xb31bac: cmp             w2, NULL
    // 0xb31bb0: b.ne            #0xb31bbc
    // 0xb31bb4: r1 = Null
    //     0xb31bb4: mov             x1, NULL
    // 0xb31bb8: b               #0xb31bc4
    // 0xb31bbc: LoadField: r1 = r2->field_f
    //     0xb31bbc: ldur            w1, [x2, #0xf]
    // 0xb31bc0: DecompressPointer r1
    //     0xb31bc0: add             x1, x1, HEAP, lsl #32
    // 0xb31bc4: StoreField: r0->field_13 = r1
    //     0xb31bc4: stur            w1, [x0, #0x13]
    // 0xb31bc8: r16 = "!"
    //     0xb31bc8: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb31bcc: ldr             x16, [x16, #0xd00]
    // 0xb31bd0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb31bd0: stur            w16, [x0, #0x17]
    // 0xb31bd4: str             x0, [SP]
    // 0xb31bd8: r0 = _interpolate()
    //     0xb31bd8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb31bdc: mov             x2, x0
    // 0xb31be0: ldur            x0, [fp, #-0x38]
    // 0xb31be4: ldur            x1, [fp, #-0x10]
    // 0xb31be8: stur            x2, [fp, #-0x40]
    // 0xb31bec: r0 = of()
    //     0xb31bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb31bf0: LoadField: r1 = r0->field_87
    //     0xb31bf0: ldur            w1, [x0, #0x87]
    // 0xb31bf4: DecompressPointer r1
    //     0xb31bf4: add             x1, x1, HEAP, lsl #32
    // 0xb31bf8: LoadField: r0 = r1->field_7
    //     0xb31bf8: ldur            w0, [x1, #7]
    // 0xb31bfc: DecompressPointer r0
    //     0xb31bfc: add             x0, x0, HEAP, lsl #32
    // 0xb31c00: r16 = Instance_Color
    //     0xb31c00: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb31c04: r30 = 16.000000
    //     0xb31c04: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb31c08: ldr             lr, [lr, #0x188]
    // 0xb31c0c: stp             lr, x16, [SP]
    // 0xb31c10: mov             x1, x0
    // 0xb31c14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb31c14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb31c18: ldr             x4, [x4, #0x9b8]
    // 0xb31c1c: r0 = copyWith()
    //     0xb31c1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb31c20: stur            x0, [fp, #-0x48]
    // 0xb31c24: r0 = Text()
    //     0xb31c24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31c28: mov             x2, x0
    // 0xb31c2c: ldur            x0, [fp, #-0x40]
    // 0xb31c30: stur            x2, [fp, #-0x50]
    // 0xb31c34: StoreField: r2->field_b = r0
    //     0xb31c34: stur            w0, [x2, #0xb]
    // 0xb31c38: ldur            x0, [fp, #-0x48]
    // 0xb31c3c: StoreField: r2->field_13 = r0
    //     0xb31c3c: stur            w0, [x2, #0x13]
    // 0xb31c40: ldur            x1, [fp, #-0x10]
    // 0xb31c44: r0 = of()
    //     0xb31c44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb31c48: LoadField: r1 = r0->field_87
    //     0xb31c48: ldur            w1, [x0, #0x87]
    // 0xb31c4c: DecompressPointer r1
    //     0xb31c4c: add             x1, x1, HEAP, lsl #32
    // 0xb31c50: LoadField: r0 = r1->field_2b
    //     0xb31c50: ldur            w0, [x1, #0x2b]
    // 0xb31c54: DecompressPointer r0
    //     0xb31c54: add             x0, x0, HEAP, lsl #32
    // 0xb31c58: r16 = Instance_Color
    //     0xb31c58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb31c5c: ldr             x16, [x16, #0x858]
    // 0xb31c60: r30 = 12.000000
    //     0xb31c60: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb31c64: ldr             lr, [lr, #0x9e8]
    // 0xb31c68: stp             lr, x16, [SP]
    // 0xb31c6c: mov             x1, x0
    // 0xb31c70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb31c70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb31c74: ldr             x4, [x4, #0x9b8]
    // 0xb31c78: r0 = copyWith()
    //     0xb31c78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb31c7c: stur            x0, [fp, #-0x40]
    // 0xb31c80: r0 = Text()
    //     0xb31c80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31c84: mov             x3, x0
    // 0xb31c88: r0 = "Offer Applied!"
    //     0xb31c88: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d38] "Offer Applied!"
    //     0xb31c8c: ldr             x0, [x0, #0xd38]
    // 0xb31c90: stur            x3, [fp, #-0x48]
    // 0xb31c94: StoreField: r3->field_b = r0
    //     0xb31c94: stur            w0, [x3, #0xb]
    // 0xb31c98: ldur            x0, [fp, #-0x40]
    // 0xb31c9c: StoreField: r3->field_13 = r0
    //     0xb31c9c: stur            w0, [x3, #0x13]
    // 0xb31ca0: r1 = Null
    //     0xb31ca0: mov             x1, NULL
    // 0xb31ca4: r2 = 6
    //     0xb31ca4: movz            x2, #0x6
    // 0xb31ca8: r0 = AllocateArray()
    //     0xb31ca8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb31cac: mov             x2, x0
    // 0xb31cb0: ldur            x0, [fp, #-0x50]
    // 0xb31cb4: stur            x2, [fp, #-0x40]
    // 0xb31cb8: StoreField: r2->field_f = r0
    //     0xb31cb8: stur            w0, [x2, #0xf]
    // 0xb31cbc: r16 = Instance_SizedBox
    //     0xb31cbc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb31cc0: ldr             x16, [x16, #0x328]
    // 0xb31cc4: StoreField: r2->field_13 = r16
    //     0xb31cc4: stur            w16, [x2, #0x13]
    // 0xb31cc8: ldur            x0, [fp, #-0x48]
    // 0xb31ccc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb31ccc: stur            w0, [x2, #0x17]
    // 0xb31cd0: r1 = <Widget>
    //     0xb31cd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb31cd4: r0 = AllocateGrowableArray()
    //     0xb31cd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb31cd8: mov             x1, x0
    // 0xb31cdc: ldur            x0, [fp, #-0x40]
    // 0xb31ce0: stur            x1, [fp, #-0x48]
    // 0xb31ce4: StoreField: r1->field_f = r0
    //     0xb31ce4: stur            w0, [x1, #0xf]
    // 0xb31ce8: r2 = 6
    //     0xb31ce8: movz            x2, #0x6
    // 0xb31cec: StoreField: r1->field_b = r2
    //     0xb31cec: stur            w2, [x1, #0xb]
    // 0xb31cf0: r0 = Column()
    //     0xb31cf0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb31cf4: mov             x2, x0
    // 0xb31cf8: r0 = Instance_Axis
    //     0xb31cf8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb31cfc: stur            x2, [fp, #-0x40]
    // 0xb31d00: StoreField: r2->field_f = r0
    //     0xb31d00: stur            w0, [x2, #0xf]
    // 0xb31d04: r1 = Instance_MainAxisAlignment
    //     0xb31d04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb31d08: ldr             x1, [x1, #0xab0]
    // 0xb31d0c: StoreField: r2->field_13 = r1
    //     0xb31d0c: stur            w1, [x2, #0x13]
    // 0xb31d10: r3 = Instance_MainAxisSize
    //     0xb31d10: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb31d14: ldr             x3, [x3, #0xa10]
    // 0xb31d18: ArrayStore: r2[0] = r3  ; List_4
    //     0xb31d18: stur            w3, [x2, #0x17]
    // 0xb31d1c: r4 = Instance_CrossAxisAlignment
    //     0xb31d1c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb31d20: ldr             x4, [x4, #0x890]
    // 0xb31d24: StoreField: r2->field_1b = r4
    //     0xb31d24: stur            w4, [x2, #0x1b]
    // 0xb31d28: r5 = Instance_VerticalDirection
    //     0xb31d28: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb31d2c: ldr             x5, [x5, #0xa20]
    // 0xb31d30: StoreField: r2->field_23 = r5
    //     0xb31d30: stur            w5, [x2, #0x23]
    // 0xb31d34: r6 = Instance_Clip
    //     0xb31d34: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb31d38: ldr             x6, [x6, #0x38]
    // 0xb31d3c: StoreField: r2->field_2b = r6
    //     0xb31d3c: stur            w6, [x2, #0x2b]
    // 0xb31d40: StoreField: r2->field_2f = rZR
    //     0xb31d40: stur            xzr, [x2, #0x2f]
    // 0xb31d44: ldur            x1, [fp, #-0x48]
    // 0xb31d48: StoreField: r2->field_b = r1
    //     0xb31d48: stur            w1, [x2, #0xb]
    // 0xb31d4c: r1 = <FlexParentData>
    //     0xb31d4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb31d50: ldr             x1, [x1, #0xe00]
    // 0xb31d54: r0 = Expanded()
    //     0xb31d54: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb31d58: stur            x0, [fp, #-0x48]
    // 0xb31d5c: StoreField: r0->field_13 = rZR
    //     0xb31d5c: stur            xzr, [x0, #0x13]
    // 0xb31d60: r1 = Instance_FlexFit
    //     0xb31d60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb31d64: ldr             x1, [x1, #0xe08]
    // 0xb31d68: StoreField: r0->field_1b = r1
    //     0xb31d68: stur            w1, [x0, #0x1b]
    // 0xb31d6c: ldur            x1, [fp, #-0x40]
    // 0xb31d70: StoreField: r0->field_b = r1
    //     0xb31d70: stur            w1, [x0, #0xb]
    // 0xb31d74: ldur            x2, [fp, #-0x38]
    // 0xb31d78: LoadField: r1 = r2->field_b
    //     0xb31d78: ldur            w1, [x2, #0xb]
    // 0xb31d7c: LoadField: r3 = r2->field_f
    //     0xb31d7c: ldur            w3, [x2, #0xf]
    // 0xb31d80: DecompressPointer r3
    //     0xb31d80: add             x3, x3, HEAP, lsl #32
    // 0xb31d84: LoadField: r4 = r3->field_b
    //     0xb31d84: ldur            w4, [x3, #0xb]
    // 0xb31d88: r3 = LoadInt32Instr(r1)
    //     0xb31d88: sbfx            x3, x1, #1, #0x1f
    // 0xb31d8c: stur            x3, [fp, #-0x60]
    // 0xb31d90: r1 = LoadInt32Instr(r4)
    //     0xb31d90: sbfx            x1, x4, #1, #0x1f
    // 0xb31d94: cmp             x3, x1
    // 0xb31d98: b.ne            #0xb31da4
    // 0xb31d9c: mov             x1, x2
    // 0xb31da0: r0 = _growToNextCapacity()
    //     0xb31da0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb31da4: ldur            x2, [fp, #-0x38]
    // 0xb31da8: ldur            x3, [fp, #-0x60]
    // 0xb31dac: add             x4, x3, #1
    // 0xb31db0: stur            x4, [fp, #-0x68]
    // 0xb31db4: lsl             x0, x4, #1
    // 0xb31db8: StoreField: r2->field_b = r0
    //     0xb31db8: stur            w0, [x2, #0xb]
    // 0xb31dbc: LoadField: r5 = r2->field_f
    //     0xb31dbc: ldur            w5, [x2, #0xf]
    // 0xb31dc0: DecompressPointer r5
    //     0xb31dc0: add             x5, x5, HEAP, lsl #32
    // 0xb31dc4: mov             x1, x5
    // 0xb31dc8: ldur            x0, [fp, #-0x48]
    // 0xb31dcc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb31dcc: add             x25, x1, x3, lsl #2
    //     0xb31dd0: add             x25, x25, #0xf
    //     0xb31dd4: str             w0, [x25]
    //     0xb31dd8: tbz             w0, #0, #0xb31df4
    //     0xb31ddc: ldurb           w16, [x1, #-1]
    //     0xb31de0: ldurb           w17, [x0, #-1]
    //     0xb31de4: and             x16, x17, x16, lsr #2
    //     0xb31de8: tst             x16, HEAP, lsr #32
    //     0xb31dec: b.eq            #0xb31df4
    //     0xb31df0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb31df4: LoadField: r0 = r5->field_b
    //     0xb31df4: ldur            w0, [x5, #0xb]
    // 0xb31df8: r1 = LoadInt32Instr(r0)
    //     0xb31df8: sbfx            x1, x0, #1, #0x1f
    // 0xb31dfc: cmp             x4, x1
    // 0xb31e00: b.ne            #0xb31e0c
    // 0xb31e04: mov             x1, x2
    // 0xb31e08: r0 = _growToNextCapacity()
    //     0xb31e08: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb31e0c: ldur            x0, [fp, #-0x38]
    // 0xb31e10: ldur            x1, [fp, #-0x68]
    // 0xb31e14: add             x2, x1, #1
    // 0xb31e18: lsl             x3, x2, #1
    // 0xb31e1c: StoreField: r0->field_b = r3
    //     0xb31e1c: stur            w3, [x0, #0xb]
    // 0xb31e20: LoadField: r2 = r0->field_f
    //     0xb31e20: ldur            w2, [x0, #0xf]
    // 0xb31e24: DecompressPointer r2
    //     0xb31e24: add             x2, x2, HEAP, lsl #32
    // 0xb31e28: add             x3, x2, x1, lsl #2
    // 0xb31e2c: r16 = Instance_Spacer
    //     0xb31e2c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb31e30: ldr             x16, [x16, #0xf0]
    // 0xb31e34: StoreField: r3->field_f = r16
    //     0xb31e34: stur            w16, [x3, #0xf]
    // 0xb31e38: ldur            x1, [fp, #-0x10]
    // 0xb31e3c: r0 = of()
    //     0xb31e3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb31e40: LoadField: r1 = r0->field_87
    //     0xb31e40: ldur            w1, [x0, #0x87]
    // 0xb31e44: DecompressPointer r1
    //     0xb31e44: add             x1, x1, HEAP, lsl #32
    // 0xb31e48: LoadField: r0 = r1->field_7
    //     0xb31e48: ldur            w0, [x1, #7]
    // 0xb31e4c: DecompressPointer r0
    //     0xb31e4c: add             x0, x0, HEAP, lsl #32
    // 0xb31e50: r16 = 14.000000
    //     0xb31e50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb31e54: ldr             x16, [x16, #0x1d8]
    // 0xb31e58: r30 = Instance_Color
    //     0xb31e58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb31e5c: ldr             lr, [lr, #0x858]
    // 0xb31e60: stp             lr, x16, [SP]
    // 0xb31e64: mov             x1, x0
    // 0xb31e68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb31e68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb31e6c: ldr             x4, [x4, #0xaa0]
    // 0xb31e70: r0 = copyWith()
    //     0xb31e70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb31e74: stur            x0, [fp, #-0x40]
    // 0xb31e78: r0 = Text()
    //     0xb31e78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb31e7c: mov             x1, x0
    // 0xb31e80: r0 = "CHANGE"
    //     0xb31e80: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d10] "CHANGE"
    //     0xb31e84: ldr             x0, [x0, #0xd10]
    // 0xb31e88: stur            x1, [fp, #-0x48]
    // 0xb31e8c: StoreField: r1->field_b = r0
    //     0xb31e8c: stur            w0, [x1, #0xb]
    // 0xb31e90: ldur            x0, [fp, #-0x40]
    // 0xb31e94: StoreField: r1->field_13 = r0
    //     0xb31e94: stur            w0, [x1, #0x13]
    // 0xb31e98: r0 = InkWell()
    //     0xb31e98: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb31e9c: mov             x3, x0
    // 0xb31ea0: ldur            x0, [fp, #-0x48]
    // 0xb31ea4: stur            x3, [fp, #-0x40]
    // 0xb31ea8: StoreField: r3->field_b = r0
    //     0xb31ea8: stur            w0, [x3, #0xb]
    // 0xb31eac: ldur            x2, [fp, #-0x18]
    // 0xb31eb0: r1 = Function '<anonymous closure>':.
    //     0xb31eb0: add             x1, PP, #0x57, lsl #12  ; [pp+0x571e8] AnonymousClosure: (0xb32b60), in [package:customer_app/app/presentation/views/glass/bag/bottom_view.dart] _BottomViewState::build (0xb30868)
    //     0xb31eb4: ldr             x1, [x1, #0x1e8]
    // 0xb31eb8: r0 = AllocateClosure()
    //     0xb31eb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb31ebc: mov             x1, x0
    // 0xb31ec0: ldur            x0, [fp, #-0x40]
    // 0xb31ec4: StoreField: r0->field_f = r1
    //     0xb31ec4: stur            w1, [x0, #0xf]
    // 0xb31ec8: r1 = true
    //     0xb31ec8: add             x1, NULL, #0x20  ; true
    // 0xb31ecc: StoreField: r0->field_43 = r1
    //     0xb31ecc: stur            w1, [x0, #0x43]
    // 0xb31ed0: r2 = Instance_BoxShape
    //     0xb31ed0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb31ed4: ldr             x2, [x2, #0x80]
    // 0xb31ed8: StoreField: r0->field_47 = r2
    //     0xb31ed8: stur            w2, [x0, #0x47]
    // 0xb31edc: StoreField: r0->field_6f = r1
    //     0xb31edc: stur            w1, [x0, #0x6f]
    // 0xb31ee0: r2 = false
    //     0xb31ee0: add             x2, NULL, #0x30  ; false
    // 0xb31ee4: StoreField: r0->field_73 = r2
    //     0xb31ee4: stur            w2, [x0, #0x73]
    // 0xb31ee8: StoreField: r0->field_83 = r1
    //     0xb31ee8: stur            w1, [x0, #0x83]
    // 0xb31eec: StoreField: r0->field_7b = r2
    //     0xb31eec: stur            w2, [x0, #0x7b]
    // 0xb31ef0: r0 = Padding()
    //     0xb31ef0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb31ef4: mov             x2, x0
    // 0xb31ef8: r0 = Instance_EdgeInsets
    //     0xb31ef8: ldr             x0, [PP, #0x6d10]  ; [pp+0x6d10] Obj!EdgeInsets@d56cf1
    // 0xb31efc: stur            x2, [fp, #-0x48]
    // 0xb31f00: StoreField: r2->field_f = r0
    //     0xb31f00: stur            w0, [x2, #0xf]
    // 0xb31f04: ldur            x0, [fp, #-0x40]
    // 0xb31f08: StoreField: r2->field_b = r0
    //     0xb31f08: stur            w0, [x2, #0xb]
    // 0xb31f0c: ldur            x0, [fp, #-0x38]
    // 0xb31f10: LoadField: r1 = r0->field_b
    //     0xb31f10: ldur            w1, [x0, #0xb]
    // 0xb31f14: LoadField: r3 = r0->field_f
    //     0xb31f14: ldur            w3, [x0, #0xf]
    // 0xb31f18: DecompressPointer r3
    //     0xb31f18: add             x3, x3, HEAP, lsl #32
    // 0xb31f1c: LoadField: r4 = r3->field_b
    //     0xb31f1c: ldur            w4, [x3, #0xb]
    // 0xb31f20: r3 = LoadInt32Instr(r1)
    //     0xb31f20: sbfx            x3, x1, #1, #0x1f
    // 0xb31f24: stur            x3, [fp, #-0x60]
    // 0xb31f28: r1 = LoadInt32Instr(r4)
    //     0xb31f28: sbfx            x1, x4, #1, #0x1f
    // 0xb31f2c: cmp             x3, x1
    // 0xb31f30: b.ne            #0xb31f3c
    // 0xb31f34: mov             x1, x0
    // 0xb31f38: r0 = _growToNextCapacity()
    //     0xb31f38: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb31f3c: ldur            x4, [fp, #-0x20]
    // 0xb31f40: ldur            x2, [fp, #-0x38]
    // 0xb31f44: ldur            x3, [fp, #-0x60]
    // 0xb31f48: add             x0, x3, #1
    // 0xb31f4c: lsl             x1, x0, #1
    // 0xb31f50: StoreField: r2->field_b = r1
    //     0xb31f50: stur            w1, [x2, #0xb]
    // 0xb31f54: LoadField: r1 = r2->field_f
    //     0xb31f54: ldur            w1, [x2, #0xf]
    // 0xb31f58: DecompressPointer r1
    //     0xb31f58: add             x1, x1, HEAP, lsl #32
    // 0xb31f5c: ldur            x0, [fp, #-0x48]
    // 0xb31f60: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb31f60: add             x25, x1, x3, lsl #2
    //     0xb31f64: add             x25, x25, #0xf
    //     0xb31f68: str             w0, [x25]
    //     0xb31f6c: tbz             w0, #0, #0xb31f88
    //     0xb31f70: ldurb           w16, [x1, #-1]
    //     0xb31f74: ldurb           w17, [x0, #-1]
    //     0xb31f78: and             x16, x17, x16, lsr #2
    //     0xb31f7c: tst             x16, HEAP, lsr #32
    //     0xb31f80: b.eq            #0xb31f88
    //     0xb31f84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb31f88: r0 = Row()
    //     0xb31f88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb31f8c: mov             x1, x0
    // 0xb31f90: r0 = Instance_Axis
    //     0xb31f90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb31f94: stur            x1, [fp, #-0x40]
    // 0xb31f98: StoreField: r1->field_f = r0
    //     0xb31f98: stur            w0, [x1, #0xf]
    // 0xb31f9c: r2 = Instance_MainAxisAlignment
    //     0xb31f9c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb31fa0: ldr             x2, [x2, #0xd10]
    // 0xb31fa4: StoreField: r1->field_13 = r2
    //     0xb31fa4: stur            w2, [x1, #0x13]
    // 0xb31fa8: r2 = Instance_MainAxisSize
    //     0xb31fa8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb31fac: ldr             x2, [x2, #0xa10]
    // 0xb31fb0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb31fb0: stur            w2, [x1, #0x17]
    // 0xb31fb4: r3 = Instance_CrossAxisAlignment
    //     0xb31fb4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb31fb8: ldr             x3, [x3, #0xa18]
    // 0xb31fbc: StoreField: r1->field_1b = r3
    //     0xb31fbc: stur            w3, [x1, #0x1b]
    // 0xb31fc0: r4 = Instance_VerticalDirection
    //     0xb31fc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb31fc4: ldr             x4, [x4, #0xa20]
    // 0xb31fc8: StoreField: r1->field_23 = r4
    //     0xb31fc8: stur            w4, [x1, #0x23]
    // 0xb31fcc: r5 = Instance_Clip
    //     0xb31fcc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb31fd0: ldr             x5, [x5, #0x38]
    // 0xb31fd4: StoreField: r1->field_2b = r5
    //     0xb31fd4: stur            w5, [x1, #0x2b]
    // 0xb31fd8: StoreField: r1->field_2f = rZR
    //     0xb31fd8: stur            xzr, [x1, #0x2f]
    // 0xb31fdc: ldur            x6, [fp, #-0x38]
    // 0xb31fe0: StoreField: r1->field_b = r6
    //     0xb31fe0: stur            w6, [x1, #0xb]
    // 0xb31fe4: r0 = Padding()
    //     0xb31fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb31fe8: mov             x1, x0
    // 0xb31fec: r0 = Instance_EdgeInsets
    //     0xb31fec: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fce8] Obj!EdgeInsets@d58131
    //     0xb31ff0: ldr             x0, [x0, #0xce8]
    // 0xb31ff4: stur            x1, [fp, #-0x38]
    // 0xb31ff8: StoreField: r1->field_f = r0
    //     0xb31ff8: stur            w0, [x1, #0xf]
    // 0xb31ffc: ldur            x0, [fp, #-0x40]
    // 0xb32000: StoreField: r1->field_b = r0
    //     0xb32000: stur            w0, [x1, #0xb]
    // 0xb32004: r0 = Container()
    //     0xb32004: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb32008: stur            x0, [fp, #-0x40]
    // 0xb3200c: ldur            x16, [fp, #-0x30]
    // 0xb32010: ldur            lr, [fp, #-0x38]
    // 0xb32014: stp             lr, x16, [SP]
    // 0xb32018: mov             x1, x0
    // 0xb3201c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb3201c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb32020: ldr             x4, [x4, #0x88]
    // 0xb32024: r0 = Container()
    //     0xb32024: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb32028: r0 = Visibility()
    //     0xb32028: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb3202c: mov             x1, x0
    // 0xb32030: ldur            x0, [fp, #-0x40]
    // 0xb32034: StoreField: r1->field_b = r0
    //     0xb32034: stur            w0, [x1, #0xb]
    // 0xb32038: r0 = Instance_SizedBox
    //     0xb32038: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb3203c: StoreField: r1->field_f = r0
    //     0xb3203c: stur            w0, [x1, #0xf]
    // 0xb32040: ldur            x2, [fp, #-0x20]
    // 0xb32044: StoreField: r1->field_13 = r2
    //     0xb32044: stur            w2, [x1, #0x13]
    // 0xb32048: r2 = false
    //     0xb32048: add             x2, NULL, #0x30  ; false
    // 0xb3204c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb3204c: stur            w2, [x1, #0x17]
    // 0xb32050: StoreField: r1->field_1b = r2
    //     0xb32050: stur            w2, [x1, #0x1b]
    // 0xb32054: StoreField: r1->field_1f = r2
    //     0xb32054: stur            w2, [x1, #0x1f]
    // 0xb32058: StoreField: r1->field_23 = r2
    //     0xb32058: stur            w2, [x1, #0x23]
    // 0xb3205c: StoreField: r1->field_27 = r2
    //     0xb3205c: stur            w2, [x1, #0x27]
    // 0xb32060: StoreField: r1->field_2b = r2
    //     0xb32060: stur            w2, [x1, #0x2b]
    // 0xb32064: mov             x4, x1
    // 0xb32068: ldur            x3, [fp, #-0x28]
    // 0xb3206c: stur            x4, [fp, #-0x20]
    // 0xb32070: LoadField: r1 = r3->field_b
    //     0xb32070: ldur            w1, [x3, #0xb]
    // 0xb32074: LoadField: r5 = r3->field_f
    //     0xb32074: ldur            w5, [x3, #0xf]
    // 0xb32078: DecompressPointer r5
    //     0xb32078: add             x5, x5, HEAP, lsl #32
    // 0xb3207c: LoadField: r6 = r5->field_b
    //     0xb3207c: ldur            w6, [x5, #0xb]
    // 0xb32080: r5 = LoadInt32Instr(r1)
    //     0xb32080: sbfx            x5, x1, #1, #0x1f
    // 0xb32084: stur            x5, [fp, #-0x60]
    // 0xb32088: r1 = LoadInt32Instr(r6)
    //     0xb32088: sbfx            x1, x6, #1, #0x1f
    // 0xb3208c: cmp             x5, x1
    // 0xb32090: b.ne            #0xb3209c
    // 0xb32094: mov             x1, x3
    // 0xb32098: r0 = _growToNextCapacity()
    //     0xb32098: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb3209c: ldur            x3, [fp, #-0x28]
    // 0xb320a0: ldur            x2, [fp, #-0x60]
    // 0xb320a4: add             x0, x2, #1
    // 0xb320a8: lsl             x1, x0, #1
    // 0xb320ac: StoreField: r3->field_b = r1
    //     0xb320ac: stur            w1, [x3, #0xb]
    // 0xb320b0: LoadField: r1 = r3->field_f
    //     0xb320b0: ldur            w1, [x3, #0xf]
    // 0xb320b4: DecompressPointer r1
    //     0xb320b4: add             x1, x1, HEAP, lsl #32
    // 0xb320b8: ldur            x0, [fp, #-0x20]
    // 0xb320bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb320bc: add             x25, x1, x2, lsl #2
    //     0xb320c0: add             x25, x25, #0xf
    //     0xb320c4: str             w0, [x25]
    //     0xb320c8: tbz             w0, #0, #0xb320e4
    //     0xb320cc: ldurb           w16, [x1, #-1]
    //     0xb320d0: ldurb           w17, [x0, #-1]
    //     0xb320d4: and             x16, x17, x16, lsr #2
    //     0xb320d8: tst             x16, HEAP, lsr #32
    //     0xb320dc: b.eq            #0xb320e4
    //     0xb320e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb320e4: ldur            x0, [fp, #-8]
    // 0xb320e8: r1 = Null
    //     0xb320e8: mov             x1, NULL
    // 0xb320ec: r2 = 6
    //     0xb320ec: movz            x2, #0x6
    // 0xb320f0: r0 = AllocateArray()
    //     0xb320f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb320f4: r16 = "Subtotal  ("
    //     0xb320f4: add             x16, PP, #0x57, lsl #12  ; [pp+0x571f0] "Subtotal  ("
    //     0xb320f8: ldr             x16, [x16, #0x1f0]
    // 0xb320fc: StoreField: r0->field_f = r16
    //     0xb320fc: stur            w16, [x0, #0xf]
    // 0xb32100: ldur            x1, [fp, #-8]
    // 0xb32104: LoadField: r2 = r1->field_b
    //     0xb32104: ldur            w2, [x1, #0xb]
    // 0xb32108: DecompressPointer r2
    //     0xb32108: add             x2, x2, HEAP, lsl #32
    // 0xb3210c: cmp             w2, NULL
    // 0xb32110: b.eq            #0xb32a8c
    // 0xb32114: LoadField: r3 = r2->field_b
    //     0xb32114: ldur            w3, [x2, #0xb]
    // 0xb32118: DecompressPointer r3
    //     0xb32118: add             x3, x3, HEAP, lsl #32
    // 0xb3211c: cmp             w3, NULL
    // 0xb32120: b.ne            #0xb3212c
    // 0xb32124: r2 = Null
    //     0xb32124: mov             x2, NULL
    // 0xb32128: b               #0xb32134
    // 0xb3212c: LoadField: r2 = r3->field_b
    //     0xb3212c: ldur            w2, [x3, #0xb]
    // 0xb32130: DecompressPointer r2
    //     0xb32130: add             x2, x2, HEAP, lsl #32
    // 0xb32134: StoreField: r0->field_13 = r2
    //     0xb32134: stur            w2, [x0, #0x13]
    // 0xb32138: r16 = " item)"
    //     0xb32138: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0xb3213c: ldr             x16, [x16, #0x4e8]
    // 0xb32140: ArrayStore: r0[0] = r16  ; List_4
    //     0xb32140: stur            w16, [x0, #0x17]
    // 0xb32144: str             x0, [SP]
    // 0xb32148: r0 = _interpolate()
    //     0xb32148: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb3214c: ldur            x1, [fp, #-0x10]
    // 0xb32150: stur            x0, [fp, #-0x20]
    // 0xb32154: r0 = of()
    //     0xb32154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32158: LoadField: r1 = r0->field_87
    //     0xb32158: ldur            w1, [x0, #0x87]
    // 0xb3215c: DecompressPointer r1
    //     0xb3215c: add             x1, x1, HEAP, lsl #32
    // 0xb32160: LoadField: r0 = r1->field_2b
    //     0xb32160: ldur            w0, [x1, #0x2b]
    // 0xb32164: DecompressPointer r0
    //     0xb32164: add             x0, x0, HEAP, lsl #32
    // 0xb32168: stur            x0, [fp, #-0x30]
    // 0xb3216c: r1 = Instance_Color
    //     0xb3216c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb32170: d0 = 0.700000
    //     0xb32170: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb32174: ldr             d0, [x17, #0xf48]
    // 0xb32178: r0 = withOpacity()
    //     0xb32178: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3217c: r16 = 16.000000
    //     0xb3217c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32180: ldr             x16, [x16, #0x188]
    // 0xb32184: stp             x0, x16, [SP]
    // 0xb32188: ldur            x1, [fp, #-0x30]
    // 0xb3218c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3218c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb32190: ldr             x4, [x4, #0xaa0]
    // 0xb32194: r0 = copyWith()
    //     0xb32194: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32198: stur            x0, [fp, #-0x30]
    // 0xb3219c: r0 = Text()
    //     0xb3219c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb321a0: mov             x2, x0
    // 0xb321a4: ldur            x0, [fp, #-0x20]
    // 0xb321a8: stur            x2, [fp, #-0x38]
    // 0xb321ac: StoreField: r2->field_b = r0
    //     0xb321ac: stur            w0, [x2, #0xb]
    // 0xb321b0: ldur            x0, [fp, #-0x30]
    // 0xb321b4: StoreField: r2->field_13 = r0
    //     0xb321b4: stur            w0, [x2, #0x13]
    // 0xb321b8: ldur            x0, [fp, #-8]
    // 0xb321bc: LoadField: r1 = r0->field_b
    //     0xb321bc: ldur            w1, [x0, #0xb]
    // 0xb321c0: DecompressPointer r1
    //     0xb321c0: add             x1, x1, HEAP, lsl #32
    // 0xb321c4: cmp             w1, NULL
    // 0xb321c8: b.eq            #0xb32a90
    // 0xb321cc: LoadField: r3 = r1->field_b
    //     0xb321cc: ldur            w3, [x1, #0xb]
    // 0xb321d0: DecompressPointer r3
    //     0xb321d0: add             x3, x3, HEAP, lsl #32
    // 0xb321d4: cmp             w3, NULL
    // 0xb321d8: b.ne            #0xb321e4
    // 0xb321dc: r1 = Null
    //     0xb321dc: mov             x1, NULL
    // 0xb321e0: b               #0xb321ec
    // 0xb321e4: LoadField: r1 = r3->field_13
    //     0xb321e4: ldur            w1, [x3, #0x13]
    // 0xb321e8: DecompressPointer r1
    //     0xb321e8: add             x1, x1, HEAP, lsl #32
    // 0xb321ec: cmp             w1, NULL
    // 0xb321f0: b.ne            #0xb321fc
    // 0xb321f4: r4 = ""
    //     0xb321f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb321f8: b               #0xb32200
    // 0xb321fc: mov             x4, x1
    // 0xb32200: ldur            x3, [fp, #-0x28]
    // 0xb32204: ldur            x1, [fp, #-0x10]
    // 0xb32208: stur            x4, [fp, #-0x20]
    // 0xb3220c: r0 = of()
    //     0xb3220c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32210: LoadField: r1 = r0->field_87
    //     0xb32210: ldur            w1, [x0, #0x87]
    // 0xb32214: DecompressPointer r1
    //     0xb32214: add             x1, x1, HEAP, lsl #32
    // 0xb32218: LoadField: r0 = r1->field_7
    //     0xb32218: ldur            w0, [x1, #7]
    // 0xb3221c: DecompressPointer r0
    //     0xb3221c: add             x0, x0, HEAP, lsl #32
    // 0xb32220: r16 = Instance_Color
    //     0xb32220: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb32224: r30 = 16.000000
    //     0xb32224: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32228: ldr             lr, [lr, #0x188]
    // 0xb3222c: stp             lr, x16, [SP]
    // 0xb32230: mov             x1, x0
    // 0xb32234: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb32234: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb32238: ldr             x4, [x4, #0x9b8]
    // 0xb3223c: r0 = copyWith()
    //     0xb3223c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32240: stur            x0, [fp, #-0x30]
    // 0xb32244: r0 = Text()
    //     0xb32244: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb32248: mov             x3, x0
    // 0xb3224c: ldur            x0, [fp, #-0x20]
    // 0xb32250: stur            x3, [fp, #-0x40]
    // 0xb32254: StoreField: r3->field_b = r0
    //     0xb32254: stur            w0, [x3, #0xb]
    // 0xb32258: ldur            x0, [fp, #-0x30]
    // 0xb3225c: StoreField: r3->field_13 = r0
    //     0xb3225c: stur            w0, [x3, #0x13]
    // 0xb32260: r1 = Null
    //     0xb32260: mov             x1, NULL
    // 0xb32264: r2 = 4
    //     0xb32264: movz            x2, #0x4
    // 0xb32268: r0 = AllocateArray()
    //     0xb32268: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3226c: mov             x2, x0
    // 0xb32270: ldur            x0, [fp, #-0x38]
    // 0xb32274: stur            x2, [fp, #-0x20]
    // 0xb32278: StoreField: r2->field_f = r0
    //     0xb32278: stur            w0, [x2, #0xf]
    // 0xb3227c: ldur            x0, [fp, #-0x40]
    // 0xb32280: StoreField: r2->field_13 = r0
    //     0xb32280: stur            w0, [x2, #0x13]
    // 0xb32284: r1 = <Widget>
    //     0xb32284: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb32288: r0 = AllocateGrowableArray()
    //     0xb32288: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3228c: mov             x1, x0
    // 0xb32290: ldur            x0, [fp, #-0x20]
    // 0xb32294: stur            x1, [fp, #-0x30]
    // 0xb32298: StoreField: r1->field_f = r0
    //     0xb32298: stur            w0, [x1, #0xf]
    // 0xb3229c: r0 = 4
    //     0xb3229c: movz            x0, #0x4
    // 0xb322a0: StoreField: r1->field_b = r0
    //     0xb322a0: stur            w0, [x1, #0xb]
    // 0xb322a4: r0 = Row()
    //     0xb322a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb322a8: mov             x1, x0
    // 0xb322ac: r0 = Instance_Axis
    //     0xb322ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb322b0: stur            x1, [fp, #-0x20]
    // 0xb322b4: StoreField: r1->field_f = r0
    //     0xb322b4: stur            w0, [x1, #0xf]
    // 0xb322b8: r0 = Instance_MainAxisAlignment
    //     0xb322b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb322bc: ldr             x0, [x0, #0xa8]
    // 0xb322c0: StoreField: r1->field_13 = r0
    //     0xb322c0: stur            w0, [x1, #0x13]
    // 0xb322c4: r0 = Instance_MainAxisSize
    //     0xb322c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb322c8: ldr             x0, [x0, #0xa10]
    // 0xb322cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb322cc: stur            w0, [x1, #0x17]
    // 0xb322d0: r2 = Instance_CrossAxisAlignment
    //     0xb322d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb322d4: ldr             x2, [x2, #0xa18]
    // 0xb322d8: StoreField: r1->field_1b = r2
    //     0xb322d8: stur            w2, [x1, #0x1b]
    // 0xb322dc: r2 = Instance_VerticalDirection
    //     0xb322dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb322e0: ldr             x2, [x2, #0xa20]
    // 0xb322e4: StoreField: r1->field_23 = r2
    //     0xb322e4: stur            w2, [x1, #0x23]
    // 0xb322e8: r3 = Instance_Clip
    //     0xb322e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb322ec: ldr             x3, [x3, #0x38]
    // 0xb322f0: StoreField: r1->field_2b = r3
    //     0xb322f0: stur            w3, [x1, #0x2b]
    // 0xb322f4: StoreField: r1->field_2f = rZR
    //     0xb322f4: stur            xzr, [x1, #0x2f]
    // 0xb322f8: ldur            x4, [fp, #-0x30]
    // 0xb322fc: StoreField: r1->field_b = r4
    //     0xb322fc: stur            w4, [x1, #0xb]
    // 0xb32300: r0 = Padding()
    //     0xb32300: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb32304: mov             x2, x0
    // 0xb32308: r0 = Instance_EdgeInsets
    //     0xb32308: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb3230c: ldr             x0, [x0, #0xa00]
    // 0xb32310: stur            x2, [fp, #-0x30]
    // 0xb32314: StoreField: r2->field_f = r0
    //     0xb32314: stur            w0, [x2, #0xf]
    // 0xb32318: ldur            x0, [fp, #-0x20]
    // 0xb3231c: StoreField: r2->field_b = r0
    //     0xb3231c: stur            w0, [x2, #0xb]
    // 0xb32320: ldur            x0, [fp, #-0x28]
    // 0xb32324: LoadField: r1 = r0->field_b
    //     0xb32324: ldur            w1, [x0, #0xb]
    // 0xb32328: LoadField: r3 = r0->field_f
    //     0xb32328: ldur            w3, [x0, #0xf]
    // 0xb3232c: DecompressPointer r3
    //     0xb3232c: add             x3, x3, HEAP, lsl #32
    // 0xb32330: LoadField: r4 = r3->field_b
    //     0xb32330: ldur            w4, [x3, #0xb]
    // 0xb32334: r3 = LoadInt32Instr(r1)
    //     0xb32334: sbfx            x3, x1, #1, #0x1f
    // 0xb32338: stur            x3, [fp, #-0x60]
    // 0xb3233c: r1 = LoadInt32Instr(r4)
    //     0xb3233c: sbfx            x1, x4, #1, #0x1f
    // 0xb32340: cmp             x3, x1
    // 0xb32344: b.ne            #0xb32350
    // 0xb32348: mov             x1, x0
    // 0xb3234c: r0 = _growToNextCapacity()
    //     0xb3234c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb32350: ldur            x4, [fp, #-8]
    // 0xb32354: ldur            x2, [fp, #-0x28]
    // 0xb32358: ldur            x3, [fp, #-0x60]
    // 0xb3235c: add             x0, x3, #1
    // 0xb32360: lsl             x1, x0, #1
    // 0xb32364: StoreField: r2->field_b = r1
    //     0xb32364: stur            w1, [x2, #0xb]
    // 0xb32368: LoadField: r1 = r2->field_f
    //     0xb32368: ldur            w1, [x2, #0xf]
    // 0xb3236c: DecompressPointer r1
    //     0xb3236c: add             x1, x1, HEAP, lsl #32
    // 0xb32370: ldur            x0, [fp, #-0x30]
    // 0xb32374: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb32374: add             x25, x1, x3, lsl #2
    //     0xb32378: add             x25, x25, #0xf
    //     0xb3237c: str             w0, [x25]
    //     0xb32380: tbz             w0, #0, #0xb3239c
    //     0xb32384: ldurb           w16, [x1, #-1]
    //     0xb32388: ldurb           w17, [x0, #-1]
    //     0xb3238c: and             x16, x17, x16, lsr #2
    //     0xb32390: tst             x16, HEAP, lsr #32
    //     0xb32394: b.eq            #0xb3239c
    //     0xb32398: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb3239c: LoadField: r0 = r4->field_b
    //     0xb3239c: ldur            w0, [x4, #0xb]
    // 0xb323a0: DecompressPointer r0
    //     0xb323a0: add             x0, x0, HEAP, lsl #32
    // 0xb323a4: cmp             w0, NULL
    // 0xb323a8: b.eq            #0xb32a94
    // 0xb323ac: LoadField: r1 = r0->field_b
    //     0xb323ac: ldur            w1, [x0, #0xb]
    // 0xb323b0: DecompressPointer r1
    //     0xb323b0: add             x1, x1, HEAP, lsl #32
    // 0xb323b4: cmp             w1, NULL
    // 0xb323b8: b.ne            #0xb323c4
    // 0xb323bc: r0 = Null
    //     0xb323bc: mov             x0, NULL
    // 0xb323c0: b               #0xb323f0
    // 0xb323c4: LoadField: r0 = r1->field_f
    //     0xb323c4: ldur            w0, [x1, #0xf]
    // 0xb323c8: DecompressPointer r0
    //     0xb323c8: add             x0, x0, HEAP, lsl #32
    // 0xb323cc: cmp             w0, NULL
    // 0xb323d0: b.ne            #0xb323dc
    // 0xb323d4: r0 = Null
    //     0xb323d4: mov             x0, NULL
    // 0xb323d8: b               #0xb323f0
    // 0xb323dc: LoadField: r3 = r0->field_7
    //     0xb323dc: ldur            w3, [x0, #7]
    // 0xb323e0: cbnz            w3, #0xb323ec
    // 0xb323e4: r0 = false
    //     0xb323e4: add             x0, NULL, #0x30  ; false
    // 0xb323e8: b               #0xb323f0
    // 0xb323ec: r0 = true
    //     0xb323ec: add             x0, NULL, #0x20  ; true
    // 0xb323f0: cmp             w0, NULL
    // 0xb323f4: b.ne            #0xb32400
    // 0xb323f8: r3 = false
    //     0xb323f8: add             x3, NULL, #0x30  ; false
    // 0xb323fc: b               #0xb32404
    // 0xb32400: mov             x3, x0
    // 0xb32404: stur            x3, [fp, #-0x20]
    // 0xb32408: cmp             w1, NULL
    // 0xb3240c: b.ne            #0xb32418
    // 0xb32410: r0 = Null
    //     0xb32410: mov             x0, NULL
    // 0xb32414: b               #0xb32444
    // 0xb32418: LoadField: r0 = r1->field_f
    //     0xb32418: ldur            w0, [x1, #0xf]
    // 0xb3241c: DecompressPointer r0
    //     0xb3241c: add             x0, x0, HEAP, lsl #32
    // 0xb32420: r1 = LoadClassIdInstr(r0)
    //     0xb32420: ldur            x1, [x0, #-1]
    //     0xb32424: ubfx            x1, x1, #0xc, #0x14
    // 0xb32428: str             x0, [SP]
    // 0xb3242c: mov             x0, x1
    // 0xb32430: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb32430: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb32434: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb32434: movz            x17, #0x2700
    //     0xb32438: add             lr, x0, x17
    //     0xb3243c: ldr             lr, [x21, lr, lsl #3]
    //     0xb32440: blr             lr
    // 0xb32444: cmp             w0, NULL
    // 0xb32448: b.ne            #0xb32454
    // 0xb3244c: r3 = ""
    //     0xb3244c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb32450: b               #0xb32458
    // 0xb32454: mov             x3, x0
    // 0xb32458: ldur            x0, [fp, #-0x28]
    // 0xb3245c: ldur            x2, [fp, #-0x20]
    // 0xb32460: ldur            x1, [fp, #-0x10]
    // 0xb32464: stur            x3, [fp, #-0x30]
    // 0xb32468: r0 = of()
    //     0xb32468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3246c: LoadField: r1 = r0->field_87
    //     0xb3246c: ldur            w1, [x0, #0x87]
    // 0xb32470: DecompressPointer r1
    //     0xb32470: add             x1, x1, HEAP, lsl #32
    // 0xb32474: LoadField: r0 = r1->field_7
    //     0xb32474: ldur            w0, [x1, #7]
    // 0xb32478: DecompressPointer r0
    //     0xb32478: add             x0, x0, HEAP, lsl #32
    // 0xb3247c: r16 = Instance_Color
    //     0xb3247c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb32480: ldr             x16, [x16, #0x858]
    // 0xb32484: r30 = 16.000000
    //     0xb32484: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32488: ldr             lr, [lr, #0x188]
    // 0xb3248c: stp             lr, x16, [SP]
    // 0xb32490: mov             x1, x0
    // 0xb32494: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb32494: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb32498: ldr             x4, [x4, #0x9b8]
    // 0xb3249c: r0 = copyWith()
    //     0xb3249c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb324a0: stur            x0, [fp, #-0x38]
    // 0xb324a4: r0 = Text()
    //     0xb324a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb324a8: mov             x1, x0
    // 0xb324ac: ldur            x0, [fp, #-0x30]
    // 0xb324b0: stur            x1, [fp, #-0x40]
    // 0xb324b4: StoreField: r1->field_b = r0
    //     0xb324b4: stur            w0, [x1, #0xb]
    // 0xb324b8: ldur            x0, [fp, #-0x38]
    // 0xb324bc: StoreField: r1->field_13 = r0
    //     0xb324bc: stur            w0, [x1, #0x13]
    // 0xb324c0: r0 = Padding()
    //     0xb324c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb324c4: mov             x1, x0
    // 0xb324c8: r0 = Instance_EdgeInsets
    //     0xb324c8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb324cc: ldr             x0, [x0, #0x770]
    // 0xb324d0: stur            x1, [fp, #-0x30]
    // 0xb324d4: StoreField: r1->field_f = r0
    //     0xb324d4: stur            w0, [x1, #0xf]
    // 0xb324d8: ldur            x0, [fp, #-0x40]
    // 0xb324dc: StoreField: r1->field_b = r0
    //     0xb324dc: stur            w0, [x1, #0xb]
    // 0xb324e0: r0 = Visibility()
    //     0xb324e0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb324e4: mov             x2, x0
    // 0xb324e8: ldur            x0, [fp, #-0x30]
    // 0xb324ec: stur            x2, [fp, #-0x38]
    // 0xb324f0: StoreField: r2->field_b = r0
    //     0xb324f0: stur            w0, [x2, #0xb]
    // 0xb324f4: r0 = Instance_SizedBox
    //     0xb324f4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb324f8: StoreField: r2->field_f = r0
    //     0xb324f8: stur            w0, [x2, #0xf]
    // 0xb324fc: ldur            x0, [fp, #-0x20]
    // 0xb32500: StoreField: r2->field_13 = r0
    //     0xb32500: stur            w0, [x2, #0x13]
    // 0xb32504: r0 = false
    //     0xb32504: add             x0, NULL, #0x30  ; false
    // 0xb32508: ArrayStore: r2[0] = r0  ; List_4
    //     0xb32508: stur            w0, [x2, #0x17]
    // 0xb3250c: StoreField: r2->field_1b = r0
    //     0xb3250c: stur            w0, [x2, #0x1b]
    // 0xb32510: StoreField: r2->field_1f = r0
    //     0xb32510: stur            w0, [x2, #0x1f]
    // 0xb32514: StoreField: r2->field_23 = r0
    //     0xb32514: stur            w0, [x2, #0x23]
    // 0xb32518: StoreField: r2->field_27 = r0
    //     0xb32518: stur            w0, [x2, #0x27]
    // 0xb3251c: StoreField: r2->field_2b = r0
    //     0xb3251c: stur            w0, [x2, #0x2b]
    // 0xb32520: ldur            x3, [fp, #-0x28]
    // 0xb32524: LoadField: r1 = r3->field_b
    //     0xb32524: ldur            w1, [x3, #0xb]
    // 0xb32528: LoadField: r4 = r3->field_f
    //     0xb32528: ldur            w4, [x3, #0xf]
    // 0xb3252c: DecompressPointer r4
    //     0xb3252c: add             x4, x4, HEAP, lsl #32
    // 0xb32530: LoadField: r5 = r4->field_b
    //     0xb32530: ldur            w5, [x4, #0xb]
    // 0xb32534: r4 = LoadInt32Instr(r1)
    //     0xb32534: sbfx            x4, x1, #1, #0x1f
    // 0xb32538: stur            x4, [fp, #-0x60]
    // 0xb3253c: r1 = LoadInt32Instr(r5)
    //     0xb3253c: sbfx            x1, x5, #1, #0x1f
    // 0xb32540: cmp             x4, x1
    // 0xb32544: b.ne            #0xb32550
    // 0xb32548: mov             x1, x3
    // 0xb3254c: r0 = _growToNextCapacity()
    //     0xb3254c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb32550: ldur            x4, [fp, #-8]
    // 0xb32554: ldur            x2, [fp, #-0x28]
    // 0xb32558: ldur            x3, [fp, #-0x60]
    // 0xb3255c: add             x0, x3, #1
    // 0xb32560: lsl             x1, x0, #1
    // 0xb32564: StoreField: r2->field_b = r1
    //     0xb32564: stur            w1, [x2, #0xb]
    // 0xb32568: LoadField: r1 = r2->field_f
    //     0xb32568: ldur            w1, [x2, #0xf]
    // 0xb3256c: DecompressPointer r1
    //     0xb3256c: add             x1, x1, HEAP, lsl #32
    // 0xb32570: ldur            x0, [fp, #-0x38]
    // 0xb32574: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb32574: add             x25, x1, x3, lsl #2
    //     0xb32578: add             x25, x25, #0xf
    //     0xb3257c: str             w0, [x25]
    //     0xb32580: tbz             w0, #0, #0xb3259c
    //     0xb32584: ldurb           w16, [x1, #-1]
    //     0xb32588: ldurb           w17, [x0, #-1]
    //     0xb3258c: and             x16, x17, x16, lsr #2
    //     0xb32590: tst             x16, HEAP, lsr #32
    //     0xb32594: b.eq            #0xb3259c
    //     0xb32598: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb3259c: r16 = <EdgeInsets>
    //     0xb3259c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb325a0: ldr             x16, [x16, #0xda0]
    // 0xb325a4: r30 = Instance_EdgeInsets
    //     0xb325a4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb325a8: ldr             lr, [lr, #0x1f0]
    // 0xb325ac: stp             lr, x16, [SP]
    // 0xb325b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb325b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb325b4: r0 = all()
    //     0xb325b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb325b8: ldur            x1, [fp, #-0x10]
    // 0xb325bc: stur            x0, [fp, #-0x20]
    // 0xb325c0: r0 = of()
    //     0xb325c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb325c4: LoadField: r1 = r0->field_5b
    //     0xb325c4: ldur            w1, [x0, #0x5b]
    // 0xb325c8: DecompressPointer r1
    //     0xb325c8: add             x1, x1, HEAP, lsl #32
    // 0xb325cc: r16 = <Color>
    //     0xb325cc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb325d0: ldr             x16, [x16, #0xf80]
    // 0xb325d4: stp             x1, x16, [SP]
    // 0xb325d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb325d8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb325dc: r0 = all()
    //     0xb325dc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb325e0: stur            x0, [fp, #-0x30]
    // 0xb325e4: r0 = Radius()
    //     0xb325e4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb325e8: d0 = 20.000000
    //     0xb325e8: fmov            d0, #20.00000000
    // 0xb325ec: stur            x0, [fp, #-0x38]
    // 0xb325f0: StoreField: r0->field_7 = d0
    //     0xb325f0: stur            d0, [x0, #7]
    // 0xb325f4: StoreField: r0->field_f = d0
    //     0xb325f4: stur            d0, [x0, #0xf]
    // 0xb325f8: r0 = BorderRadius()
    //     0xb325f8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb325fc: mov             x1, x0
    // 0xb32600: ldur            x0, [fp, #-0x38]
    // 0xb32604: stur            x1, [fp, #-0x40]
    // 0xb32608: StoreField: r1->field_7 = r0
    //     0xb32608: stur            w0, [x1, #7]
    // 0xb3260c: StoreField: r1->field_b = r0
    //     0xb3260c: stur            w0, [x1, #0xb]
    // 0xb32610: StoreField: r1->field_f = r0
    //     0xb32610: stur            w0, [x1, #0xf]
    // 0xb32614: StoreField: r1->field_13 = r0
    //     0xb32614: stur            w0, [x1, #0x13]
    // 0xb32618: r0 = RoundedRectangleBorder()
    //     0xb32618: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb3261c: mov             x1, x0
    // 0xb32620: ldur            x0, [fp, #-0x40]
    // 0xb32624: StoreField: r1->field_b = r0
    //     0xb32624: stur            w0, [x1, #0xb]
    // 0xb32628: r0 = Instance_BorderSide
    //     0xb32628: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb3262c: ldr             x0, [x0, #0xe20]
    // 0xb32630: StoreField: r1->field_7 = r0
    //     0xb32630: stur            w0, [x1, #7]
    // 0xb32634: r16 = <RoundedRectangleBorder>
    //     0xb32634: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb32638: ldr             x16, [x16, #0xf78]
    // 0xb3263c: stp             x1, x16, [SP]
    // 0xb32640: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb32640: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb32644: r0 = all()
    //     0xb32644: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb32648: stur            x0, [fp, #-0x38]
    // 0xb3264c: r0 = ButtonStyle()
    //     0xb3264c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb32650: mov             x1, x0
    // 0xb32654: ldur            x0, [fp, #-0x30]
    // 0xb32658: stur            x1, [fp, #-0x40]
    // 0xb3265c: StoreField: r1->field_b = r0
    //     0xb3265c: stur            w0, [x1, #0xb]
    // 0xb32660: ldur            x0, [fp, #-0x20]
    // 0xb32664: StoreField: r1->field_23 = r0
    //     0xb32664: stur            w0, [x1, #0x23]
    // 0xb32668: ldur            x0, [fp, #-0x38]
    // 0xb3266c: StoreField: r1->field_43 = r0
    //     0xb3266c: stur            w0, [x1, #0x43]
    // 0xb32670: r0 = TextButtonThemeData()
    //     0xb32670: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb32674: mov             x3, x0
    // 0xb32678: ldur            x0, [fp, #-0x40]
    // 0xb3267c: stur            x3, [fp, #-0x30]
    // 0xb32680: StoreField: r3->field_7 = r0
    //     0xb32680: stur            w0, [x3, #7]
    // 0xb32684: ldur            x0, [fp, #-8]
    // 0xb32688: LoadField: r4 = r0->field_b
    //     0xb32688: ldur            w4, [x0, #0xb]
    // 0xb3268c: DecompressPointer r4
    //     0xb3268c: add             x4, x4, HEAP, lsl #32
    // 0xb32690: stur            x4, [fp, #-0x20]
    // 0xb32694: cmp             w4, NULL
    // 0xb32698: b.eq            #0xb32a98
    // 0xb3269c: LoadField: r0 = r4->field_13
    //     0xb3269c: ldur            w0, [x4, #0x13]
    // 0xb326a0: DecompressPointer r0
    //     0xb326a0: add             x0, x0, HEAP, lsl #32
    // 0xb326a4: tbz             w0, #4, #0xb326c0
    // 0xb326a8: ldur            x2, [fp, #-0x18]
    // 0xb326ac: r1 = Function '<anonymous closure>':.
    //     0xb326ac: add             x1, PP, #0x57, lsl #12  ; [pp+0x571f8] AnonymousClosure: (0xb32ac0), in [package:customer_app/app/presentation/views/glass/bag/bottom_view.dart] _BottomViewState::build (0xb30868)
    //     0xb326b0: ldr             x1, [x1, #0x1f8]
    // 0xb326b4: r0 = AllocateClosure()
    //     0xb326b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb326b8: mov             x2, x0
    // 0xb326bc: b               #0xb326c4
    // 0xb326c0: r2 = Null
    //     0xb326c0: mov             x2, NULL
    // 0xb326c4: ldur            x0, [fp, #-0x20]
    // 0xb326c8: stur            x2, [fp, #-8]
    // 0xb326cc: LoadField: r1 = r0->field_b
    //     0xb326cc: ldur            w1, [x0, #0xb]
    // 0xb326d0: DecompressPointer r1
    //     0xb326d0: add             x1, x1, HEAP, lsl #32
    // 0xb326d4: cmp             w1, NULL
    // 0xb326d8: b.eq            #0xb326e8
    // 0xb326dc: LoadField: r3 = r1->field_b
    //     0xb326dc: ldur            w3, [x1, #0xb]
    // 0xb326e0: DecompressPointer r3
    //     0xb326e0: add             x3, x3, HEAP, lsl #32
    // 0xb326e4: cbz             w3, #0xb32760
    // 0xb326e8: LoadField: r1 = r0->field_f
    //     0xb326e8: ldur            w1, [x0, #0xf]
    // 0xb326ec: DecompressPointer r1
    //     0xb326ec: add             x1, x1, HEAP, lsl #32
    // 0xb326f0: tbz             w1, #4, #0xb32760
    // 0xb326f4: r1 = "checkout"
    //     0xb326f4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "checkout"
    //     0xb326f8: ldr             x1, [x1, #0x4e8]
    // 0xb326fc: r0 = capitalizeFirstWord()
    //     0xb326fc: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb32700: ldur            x1, [fp, #-0x10]
    // 0xb32704: stur            x0, [fp, #-0x18]
    // 0xb32708: r0 = of()
    //     0xb32708: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3270c: LoadField: r1 = r0->field_87
    //     0xb3270c: ldur            w1, [x0, #0x87]
    // 0xb32710: DecompressPointer r1
    //     0xb32710: add             x1, x1, HEAP, lsl #32
    // 0xb32714: LoadField: r0 = r1->field_7
    //     0xb32714: ldur            w0, [x1, #7]
    // 0xb32718: DecompressPointer r0
    //     0xb32718: add             x0, x0, HEAP, lsl #32
    // 0xb3271c: r16 = 16.000000
    //     0xb3271c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32720: ldr             x16, [x16, #0x188]
    // 0xb32724: r30 = Instance_Color
    //     0xb32724: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb32728: stp             lr, x16, [SP]
    // 0xb3272c: mov             x1, x0
    // 0xb32730: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb32730: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb32734: ldr             x4, [x4, #0xaa0]
    // 0xb32738: r0 = copyWith()
    //     0xb32738: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3273c: stur            x0, [fp, #-0x38]
    // 0xb32740: r0 = Text()
    //     0xb32740: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb32744: mov             x1, x0
    // 0xb32748: ldur            x0, [fp, #-0x18]
    // 0xb3274c: StoreField: r1->field_b = r0
    //     0xb3274c: stur            w0, [x1, #0xb]
    // 0xb32750: ldur            x0, [fp, #-0x38]
    // 0xb32754: StoreField: r1->field_13 = r0
    //     0xb32754: stur            w0, [x1, #0x13]
    // 0xb32758: mov             x3, x1
    // 0xb3275c: b               #0xb328d4
    // 0xb32760: LoadField: r1 = r0->field_f
    //     0xb32760: ldur            w1, [x0, #0xf]
    // 0xb32764: DecompressPointer r1
    //     0xb32764: add             x1, x1, HEAP, lsl #32
    // 0xb32768: tbnz            w1, #4, #0xb327e8
    // 0xb3276c: LoadField: r2 = r0->field_23
    //     0xb3276c: ldur            w2, [x0, #0x23]
    // 0xb32770: DecompressPointer r2
    //     0xb32770: add             x2, x2, HEAP, lsl #32
    // 0xb32774: LoadField: r3 = r2->field_7
    //     0xb32774: ldur            w3, [x2, #7]
    // 0xb32778: cbnz            w3, #0xb327e8
    // 0xb3277c: r1 = "checkout with in-stock product"
    //     0xb3277c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57200] "checkout with in-stock product"
    //     0xb32780: ldr             x1, [x1, #0x200]
    // 0xb32784: r0 = capitalizeFirstWord()
    //     0xb32784: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb32788: ldur            x1, [fp, #-0x10]
    // 0xb3278c: stur            x0, [fp, #-0x18]
    // 0xb32790: r0 = of()
    //     0xb32790: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32794: LoadField: r1 = r0->field_87
    //     0xb32794: ldur            w1, [x0, #0x87]
    // 0xb32798: DecompressPointer r1
    //     0xb32798: add             x1, x1, HEAP, lsl #32
    // 0xb3279c: LoadField: r0 = r1->field_7
    //     0xb3279c: ldur            w0, [x1, #7]
    // 0xb327a0: DecompressPointer r0
    //     0xb327a0: add             x0, x0, HEAP, lsl #32
    // 0xb327a4: r16 = 16.000000
    //     0xb327a4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb327a8: ldr             x16, [x16, #0x188]
    // 0xb327ac: r30 = Instance_Color
    //     0xb327ac: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb327b0: stp             lr, x16, [SP]
    // 0xb327b4: mov             x1, x0
    // 0xb327b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb327b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb327bc: ldr             x4, [x4, #0xaa0]
    // 0xb327c0: r0 = copyWith()
    //     0xb327c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb327c4: stur            x0, [fp, #-0x38]
    // 0xb327c8: r0 = Text()
    //     0xb327c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb327cc: mov             x1, x0
    // 0xb327d0: ldur            x0, [fp, #-0x18]
    // 0xb327d4: StoreField: r1->field_b = r0
    //     0xb327d4: stur            w0, [x1, #0xb]
    // 0xb327d8: ldur            x0, [fp, #-0x38]
    // 0xb327dc: StoreField: r1->field_13 = r0
    //     0xb327dc: stur            w0, [x1, #0x13]
    // 0xb327e0: mov             x0, x1
    // 0xb327e4: b               #0xb328d0
    // 0xb327e8: tbnz            w1, #4, #0xb32868
    // 0xb327ec: LoadField: r1 = r0->field_23
    //     0xb327ec: ldur            w1, [x0, #0x23]
    // 0xb327f0: DecompressPointer r1
    //     0xb327f0: add             x1, x1, HEAP, lsl #32
    // 0xb327f4: LoadField: r0 = r1->field_7
    //     0xb327f4: ldur            w0, [x1, #7]
    // 0xb327f8: cbz             w0, #0xb32868
    // 0xb327fc: r1 = "checkout"
    //     0xb327fc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "checkout"
    //     0xb32800: ldr             x1, [x1, #0x4e8]
    // 0xb32804: r0 = capitalizeFirstWord()
    //     0xb32804: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb32808: ldur            x1, [fp, #-0x10]
    // 0xb3280c: stur            x0, [fp, #-0x18]
    // 0xb32810: r0 = of()
    //     0xb32810: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32814: LoadField: r1 = r0->field_87
    //     0xb32814: ldur            w1, [x0, #0x87]
    // 0xb32818: DecompressPointer r1
    //     0xb32818: add             x1, x1, HEAP, lsl #32
    // 0xb3281c: LoadField: r0 = r1->field_7
    //     0xb3281c: ldur            w0, [x1, #7]
    // 0xb32820: DecompressPointer r0
    //     0xb32820: add             x0, x0, HEAP, lsl #32
    // 0xb32824: r16 = 16.000000
    //     0xb32824: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32828: ldr             x16, [x16, #0x188]
    // 0xb3282c: r30 = Instance_Color
    //     0xb3282c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb32830: stp             lr, x16, [SP]
    // 0xb32834: mov             x1, x0
    // 0xb32838: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb32838: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3283c: ldr             x4, [x4, #0xaa0]
    // 0xb32840: r0 = copyWith()
    //     0xb32840: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32844: stur            x0, [fp, #-0x20]
    // 0xb32848: r0 = Text()
    //     0xb32848: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3284c: mov             x1, x0
    // 0xb32850: ldur            x0, [fp, #-0x18]
    // 0xb32854: StoreField: r1->field_b = r0
    //     0xb32854: stur            w0, [x1, #0xb]
    // 0xb32858: ldur            x0, [fp, #-0x20]
    // 0xb3285c: StoreField: r1->field_13 = r0
    //     0xb3285c: stur            w0, [x1, #0x13]
    // 0xb32860: mov             x0, x1
    // 0xb32864: b               #0xb328d0
    // 0xb32868: r1 = "clear bag"
    //     0xb32868: add             x1, PP, #0x57, lsl #12  ; [pp+0x57208] "clear bag"
    //     0xb3286c: ldr             x1, [x1, #0x208]
    // 0xb32870: r0 = capitalizeFirstWord()
    //     0xb32870: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb32874: ldur            x1, [fp, #-0x10]
    // 0xb32878: stur            x0, [fp, #-0x10]
    // 0xb3287c: r0 = of()
    //     0xb3287c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32880: LoadField: r1 = r0->field_87
    //     0xb32880: ldur            w1, [x0, #0x87]
    // 0xb32884: DecompressPointer r1
    //     0xb32884: add             x1, x1, HEAP, lsl #32
    // 0xb32888: LoadField: r0 = r1->field_7
    //     0xb32888: ldur            w0, [x1, #7]
    // 0xb3288c: DecompressPointer r0
    //     0xb3288c: add             x0, x0, HEAP, lsl #32
    // 0xb32890: r16 = 16.000000
    //     0xb32890: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32894: ldr             x16, [x16, #0x188]
    // 0xb32898: r30 = Instance_Color
    //     0xb32898: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3289c: stp             lr, x16, [SP]
    // 0xb328a0: mov             x1, x0
    // 0xb328a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb328a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb328a8: ldr             x4, [x4, #0xaa0]
    // 0xb328ac: r0 = copyWith()
    //     0xb328ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb328b0: stur            x0, [fp, #-0x18]
    // 0xb328b4: r0 = Text()
    //     0xb328b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb328b8: mov             x1, x0
    // 0xb328bc: ldur            x0, [fp, #-0x10]
    // 0xb328c0: StoreField: r1->field_b = r0
    //     0xb328c0: stur            w0, [x1, #0xb]
    // 0xb328c4: ldur            x0, [fp, #-0x18]
    // 0xb328c8: StoreField: r1->field_13 = r0
    //     0xb328c8: stur            w0, [x1, #0x13]
    // 0xb328cc: mov             x0, x1
    // 0xb328d0: mov             x3, x0
    // 0xb328d4: ldur            x2, [fp, #-0x28]
    // 0xb328d8: ldur            x1, [fp, #-0x30]
    // 0xb328dc: ldur            x0, [fp, #-8]
    // 0xb328e0: stur            x3, [fp, #-0x10]
    // 0xb328e4: r0 = TextButton()
    //     0xb328e4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb328e8: mov             x1, x0
    // 0xb328ec: ldur            x0, [fp, #-8]
    // 0xb328f0: stur            x1, [fp, #-0x18]
    // 0xb328f4: StoreField: r1->field_b = r0
    //     0xb328f4: stur            w0, [x1, #0xb]
    // 0xb328f8: r0 = false
    //     0xb328f8: add             x0, NULL, #0x30  ; false
    // 0xb328fc: StoreField: r1->field_27 = r0
    //     0xb328fc: stur            w0, [x1, #0x27]
    // 0xb32900: r0 = true
    //     0xb32900: add             x0, NULL, #0x20  ; true
    // 0xb32904: StoreField: r1->field_2f = r0
    //     0xb32904: stur            w0, [x1, #0x2f]
    // 0xb32908: ldur            x0, [fp, #-0x10]
    // 0xb3290c: StoreField: r1->field_37 = r0
    //     0xb3290c: stur            w0, [x1, #0x37]
    // 0xb32910: r0 = TextButtonTheme()
    //     0xb32910: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb32914: mov             x1, x0
    // 0xb32918: ldur            x0, [fp, #-0x30]
    // 0xb3291c: stur            x1, [fp, #-8]
    // 0xb32920: StoreField: r1->field_f = r0
    //     0xb32920: stur            w0, [x1, #0xf]
    // 0xb32924: ldur            x0, [fp, #-0x18]
    // 0xb32928: StoreField: r1->field_b = r0
    //     0xb32928: stur            w0, [x1, #0xb]
    // 0xb3292c: r0 = SizedBox()
    //     0xb3292c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb32930: mov             x2, x0
    // 0xb32934: r0 = inf
    //     0xb32934: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb32938: ldr             x0, [x0, #0x9f8]
    // 0xb3293c: stur            x2, [fp, #-0x10]
    // 0xb32940: StoreField: r2->field_f = r0
    //     0xb32940: stur            w0, [x2, #0xf]
    // 0xb32944: ldur            x0, [fp, #-8]
    // 0xb32948: StoreField: r2->field_b = r0
    //     0xb32948: stur            w0, [x2, #0xb]
    // 0xb3294c: ldur            x0, [fp, #-0x28]
    // 0xb32950: LoadField: r1 = r0->field_b
    //     0xb32950: ldur            w1, [x0, #0xb]
    // 0xb32954: LoadField: r3 = r0->field_f
    //     0xb32954: ldur            w3, [x0, #0xf]
    // 0xb32958: DecompressPointer r3
    //     0xb32958: add             x3, x3, HEAP, lsl #32
    // 0xb3295c: LoadField: r4 = r3->field_b
    //     0xb3295c: ldur            w4, [x3, #0xb]
    // 0xb32960: r3 = LoadInt32Instr(r1)
    //     0xb32960: sbfx            x3, x1, #1, #0x1f
    // 0xb32964: stur            x3, [fp, #-0x60]
    // 0xb32968: r1 = LoadInt32Instr(r4)
    //     0xb32968: sbfx            x1, x4, #1, #0x1f
    // 0xb3296c: cmp             x3, x1
    // 0xb32970: b.ne            #0xb3297c
    // 0xb32974: mov             x1, x0
    // 0xb32978: r0 = _growToNextCapacity()
    //     0xb32978: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb3297c: ldur            x2, [fp, #-0x28]
    // 0xb32980: ldur            x3, [fp, #-0x60]
    // 0xb32984: add             x0, x3, #1
    // 0xb32988: lsl             x1, x0, #1
    // 0xb3298c: StoreField: r2->field_b = r1
    //     0xb3298c: stur            w1, [x2, #0xb]
    // 0xb32990: LoadField: r1 = r2->field_f
    //     0xb32990: ldur            w1, [x2, #0xf]
    // 0xb32994: DecompressPointer r1
    //     0xb32994: add             x1, x1, HEAP, lsl #32
    // 0xb32998: ldur            x0, [fp, #-0x10]
    // 0xb3299c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb3299c: add             x25, x1, x3, lsl #2
    //     0xb329a0: add             x25, x25, #0xf
    //     0xb329a4: str             w0, [x25]
    //     0xb329a8: tbz             w0, #0, #0xb329c4
    //     0xb329ac: ldurb           w16, [x1, #-1]
    //     0xb329b0: ldurb           w17, [x0, #-1]
    //     0xb329b4: and             x16, x17, x16, lsr #2
    //     0xb329b8: tst             x16, HEAP, lsr #32
    //     0xb329bc: b.eq            #0xb329c4
    //     0xb329c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb329c4: r0 = Column()
    //     0xb329c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb329c8: mov             x1, x0
    // 0xb329cc: r0 = Instance_Axis
    //     0xb329cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb329d0: stur            x1, [fp, #-8]
    // 0xb329d4: StoreField: r1->field_f = r0
    //     0xb329d4: stur            w0, [x1, #0xf]
    // 0xb329d8: r0 = Instance_MainAxisAlignment
    //     0xb329d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb329dc: ldr             x0, [x0, #0xa08]
    // 0xb329e0: StoreField: r1->field_13 = r0
    //     0xb329e0: stur            w0, [x1, #0x13]
    // 0xb329e4: r0 = Instance_MainAxisSize
    //     0xb329e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb329e8: ldr             x0, [x0, #0xa10]
    // 0xb329ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xb329ec: stur            w0, [x1, #0x17]
    // 0xb329f0: r0 = Instance_CrossAxisAlignment
    //     0xb329f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb329f4: ldr             x0, [x0, #0x890]
    // 0xb329f8: StoreField: r1->field_1b = r0
    //     0xb329f8: stur            w0, [x1, #0x1b]
    // 0xb329fc: r0 = Instance_VerticalDirection
    //     0xb329fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb32a00: ldr             x0, [x0, #0xa20]
    // 0xb32a04: StoreField: r1->field_23 = r0
    //     0xb32a04: stur            w0, [x1, #0x23]
    // 0xb32a08: r0 = Instance_Clip
    //     0xb32a08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb32a0c: ldr             x0, [x0, #0x38]
    // 0xb32a10: StoreField: r1->field_2b = r0
    //     0xb32a10: stur            w0, [x1, #0x2b]
    // 0xb32a14: StoreField: r1->field_2f = rZR
    //     0xb32a14: stur            xzr, [x1, #0x2f]
    // 0xb32a18: ldur            x0, [fp, #-0x28]
    // 0xb32a1c: StoreField: r1->field_b = r0
    //     0xb32a1c: stur            w0, [x1, #0xb]
    // 0xb32a20: r0 = Padding()
    //     0xb32a20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb32a24: mov             x1, x0
    // 0xb32a28: r0 = Instance_EdgeInsets
    //     0xb32a28: add             x0, PP, #0x41, lsl #12  ; [pp+0x41f38] Obj!EdgeInsets@d58c71
    //     0xb32a2c: ldr             x0, [x0, #0xf38]
    // 0xb32a30: stur            x1, [fp, #-0x10]
    // 0xb32a34: StoreField: r1->field_f = r0
    //     0xb32a34: stur            w0, [x1, #0xf]
    // 0xb32a38: ldur            x0, [fp, #-8]
    // 0xb32a3c: StoreField: r1->field_b = r0
    //     0xb32a3c: stur            w0, [x1, #0xb]
    // 0xb32a40: r0 = SizedBox()
    //     0xb32a40: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb32a44: r1 = 210.000000
    //     0xb32a44: add             x1, PP, #0x55, lsl #12  ; [pp+0x55888] 210
    //     0xb32a48: ldr             x1, [x1, #0x888]
    // 0xb32a4c: StoreField: r0->field_13 = r1
    //     0xb32a4c: stur            w1, [x0, #0x13]
    // 0xb32a50: ldur            x1, [fp, #-0x10]
    // 0xb32a54: StoreField: r0->field_b = r1
    //     0xb32a54: stur            w1, [x0, #0xb]
    // 0xb32a58: LeaveFrame
    //     0xb32a58: mov             SP, fp
    //     0xb32a5c: ldp             fp, lr, [SP], #0x10
    // 0xb32a60: ret
    //     0xb32a60: ret             
    // 0xb32a64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32a64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32a68: b               #0xb30890
    // 0xb32a6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb32a98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32a98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb32ac0, size: 0xa0
    // 0xb32ac0: EnterFrame
    //     0xb32ac0: stp             fp, lr, [SP, #-0x10]!
    //     0xb32ac4: mov             fp, SP
    // 0xb32ac8: AllocStack(0x10)
    //     0xb32ac8: sub             SP, SP, #0x10
    // 0xb32acc: SetupParameters()
    //     0xb32acc: ldr             x0, [fp, #0x10]
    //     0xb32ad0: ldur            w1, [x0, #0x17]
    //     0xb32ad4: add             x1, x1, HEAP, lsl #32
    // 0xb32ad8: CheckStackOverflow
    //     0xb32ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32adc: cmp             SP, x16
    //     0xb32ae0: b.ls            #0xb32b54
    // 0xb32ae4: LoadField: r0 = r1->field_f
    //     0xb32ae4: ldur            w0, [x1, #0xf]
    // 0xb32ae8: DecompressPointer r0
    //     0xb32ae8: add             x0, x0, HEAP, lsl #32
    // 0xb32aec: LoadField: r1 = r0->field_b
    //     0xb32aec: ldur            w1, [x0, #0xb]
    // 0xb32af0: DecompressPointer r1
    //     0xb32af0: add             x1, x1, HEAP, lsl #32
    // 0xb32af4: cmp             w1, NULL
    // 0xb32af8: b.eq            #0xb32b5c
    // 0xb32afc: LoadField: r0 = r1->field_b
    //     0xb32afc: ldur            w0, [x1, #0xb]
    // 0xb32b00: DecompressPointer r0
    //     0xb32b00: add             x0, x0, HEAP, lsl #32
    // 0xb32b04: cmp             w0, NULL
    // 0xb32b08: b.ne            #0xb32b14
    // 0xb32b0c: r0 = Null
    //     0xb32b0c: mov             x0, NULL
    // 0xb32b10: b               #0xb32b20
    // 0xb32b14: LoadField: r2 = r0->field_b
    //     0xb32b14: ldur            w2, [x0, #0xb]
    // 0xb32b18: DecompressPointer r2
    //     0xb32b18: add             x2, x2, HEAP, lsl #32
    // 0xb32b1c: mov             x0, x2
    // 0xb32b20: LoadField: r2 = r1->field_1b
    //     0xb32b20: ldur            w2, [x1, #0x1b]
    // 0xb32b24: DecompressPointer r2
    //     0xb32b24: add             x2, x2, HEAP, lsl #32
    // 0xb32b28: stp             x0, x2, [SP]
    // 0xb32b2c: r4 = 0
    //     0xb32b2c: movz            x4, #0
    // 0xb32b30: ldr             x0, [SP, #8]
    // 0xb32b34: r16 = UnlinkedCall_0x613b5c
    //     0xb32b34: add             x16, PP, #0x57, lsl #12  ; [pp+0x57210] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb32b38: add             x16, x16, #0x210
    // 0xb32b3c: ldp             x5, lr, [x16]
    // 0xb32b40: blr             lr
    // 0xb32b44: r0 = Null
    //     0xb32b44: mov             x0, NULL
    // 0xb32b48: LeaveFrame
    //     0xb32b48: mov             SP, fp
    //     0xb32b4c: ldp             fp, lr, [SP], #0x10
    // 0xb32b50: ret
    //     0xb32b50: ret             
    // 0xb32b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32b54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32b58: b               #0xb32ae4
    // 0xb32b5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32b5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb32b60, size: 0x7c
    // 0xb32b60: EnterFrame
    //     0xb32b60: stp             fp, lr, [SP, #-0x10]!
    //     0xb32b64: mov             fp, SP
    // 0xb32b68: AllocStack(0x8)
    //     0xb32b68: sub             SP, SP, #8
    // 0xb32b6c: SetupParameters()
    //     0xb32b6c: ldr             x0, [fp, #0x10]
    //     0xb32b70: ldur            w1, [x0, #0x17]
    //     0xb32b74: add             x1, x1, HEAP, lsl #32
    // 0xb32b78: CheckStackOverflow
    //     0xb32b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32b7c: cmp             SP, x16
    //     0xb32b80: b.ls            #0xb32bd0
    // 0xb32b84: LoadField: r0 = r1->field_f
    //     0xb32b84: ldur            w0, [x1, #0xf]
    // 0xb32b88: DecompressPointer r0
    //     0xb32b88: add             x0, x0, HEAP, lsl #32
    // 0xb32b8c: LoadField: r1 = r0->field_b
    //     0xb32b8c: ldur            w1, [x0, #0xb]
    // 0xb32b90: DecompressPointer r1
    //     0xb32b90: add             x1, x1, HEAP, lsl #32
    // 0xb32b94: cmp             w1, NULL
    // 0xb32b98: b.eq            #0xb32bd8
    // 0xb32b9c: LoadField: r0 = r1->field_1f
    //     0xb32b9c: ldur            w0, [x1, #0x1f]
    // 0xb32ba0: DecompressPointer r0
    //     0xb32ba0: add             x0, x0, HEAP, lsl #32
    // 0xb32ba4: str             x0, [SP]
    // 0xb32ba8: r4 = 0
    //     0xb32ba8: movz            x4, #0
    // 0xb32bac: ldr             x0, [SP]
    // 0xb32bb0: r16 = UnlinkedCall_0x613b5c
    //     0xb32bb0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57220] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb32bb4: add             x16, x16, #0x220
    // 0xb32bb8: ldp             x5, lr, [x16]
    // 0xb32bbc: blr             lr
    // 0xb32bc0: r0 = Null
    //     0xb32bc0: mov             x0, NULL
    // 0xb32bc4: LeaveFrame
    //     0xb32bc4: mov             SP, fp
    //     0xb32bc8: ldp             fp, lr, [SP], #0x10
    // 0xb32bcc: ret
    //     0xb32bcc: ret             
    // 0xb32bd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32bd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32bd4: b               #0xb32b84
    // 0xb32bd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32bd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb32bdc, size: 0x7c
    // 0xb32bdc: EnterFrame
    //     0xb32bdc: stp             fp, lr, [SP, #-0x10]!
    //     0xb32be0: mov             fp, SP
    // 0xb32be4: AllocStack(0x8)
    //     0xb32be4: sub             SP, SP, #8
    // 0xb32be8: SetupParameters()
    //     0xb32be8: ldr             x0, [fp, #0x10]
    //     0xb32bec: ldur            w1, [x0, #0x17]
    //     0xb32bf0: add             x1, x1, HEAP, lsl #32
    // 0xb32bf4: CheckStackOverflow
    //     0xb32bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32bf8: cmp             SP, x16
    //     0xb32bfc: b.ls            #0xb32c4c
    // 0xb32c00: LoadField: r0 = r1->field_f
    //     0xb32c00: ldur            w0, [x1, #0xf]
    // 0xb32c04: DecompressPointer r0
    //     0xb32c04: add             x0, x0, HEAP, lsl #32
    // 0xb32c08: LoadField: r1 = r0->field_b
    //     0xb32c08: ldur            w1, [x0, #0xb]
    // 0xb32c0c: DecompressPointer r1
    //     0xb32c0c: add             x1, x1, HEAP, lsl #32
    // 0xb32c10: cmp             w1, NULL
    // 0xb32c14: b.eq            #0xb32c54
    // 0xb32c18: LoadField: r0 = r1->field_1f
    //     0xb32c18: ldur            w0, [x1, #0x1f]
    // 0xb32c1c: DecompressPointer r0
    //     0xb32c1c: add             x0, x0, HEAP, lsl #32
    // 0xb32c20: str             x0, [SP]
    // 0xb32c24: r4 = 0
    //     0xb32c24: movz            x4, #0
    // 0xb32c28: ldr             x0, [SP]
    // 0xb32c2c: r16 = UnlinkedCall_0x613b5c
    //     0xb32c2c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57230] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb32c30: add             x16, x16, #0x230
    // 0xb32c34: ldp             x5, lr, [x16]
    // 0xb32c38: blr             lr
    // 0xb32c3c: r0 = Null
    //     0xb32c3c: mov             x0, NULL
    // 0xb32c40: LeaveFrame
    //     0xb32c40: mov             SP, fp
    //     0xb32c44: ldp             fp, lr, [SP], #0x10
    // 0xb32c48: ret
    //     0xb32c48: ret             
    // 0xb32c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32c4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32c50: b               #0xb32c00
    // 0xb32c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb32c58, size: 0x7c
    // 0xb32c58: EnterFrame
    //     0xb32c58: stp             fp, lr, [SP, #-0x10]!
    //     0xb32c5c: mov             fp, SP
    // 0xb32c60: AllocStack(0x8)
    //     0xb32c60: sub             SP, SP, #8
    // 0xb32c64: SetupParameters()
    //     0xb32c64: ldr             x0, [fp, #0x10]
    //     0xb32c68: ldur            w1, [x0, #0x17]
    //     0xb32c6c: add             x1, x1, HEAP, lsl #32
    // 0xb32c70: CheckStackOverflow
    //     0xb32c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32c74: cmp             SP, x16
    //     0xb32c78: b.ls            #0xb32cc8
    // 0xb32c7c: LoadField: r0 = r1->field_f
    //     0xb32c7c: ldur            w0, [x1, #0xf]
    // 0xb32c80: DecompressPointer r0
    //     0xb32c80: add             x0, x0, HEAP, lsl #32
    // 0xb32c84: LoadField: r1 = r0->field_b
    //     0xb32c84: ldur            w1, [x0, #0xb]
    // 0xb32c88: DecompressPointer r1
    //     0xb32c88: add             x1, x1, HEAP, lsl #32
    // 0xb32c8c: cmp             w1, NULL
    // 0xb32c90: b.eq            #0xb32cd0
    // 0xb32c94: LoadField: r0 = r1->field_1f
    //     0xb32c94: ldur            w0, [x1, #0x1f]
    // 0xb32c98: DecompressPointer r0
    //     0xb32c98: add             x0, x0, HEAP, lsl #32
    // 0xb32c9c: str             x0, [SP]
    // 0xb32ca0: r4 = 0
    //     0xb32ca0: movz            x4, #0
    // 0xb32ca4: ldr             x0, [SP]
    // 0xb32ca8: r16 = UnlinkedCall_0x613b5c
    //     0xb32ca8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57240] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb32cac: add             x16, x16, #0x240
    // 0xb32cb0: ldp             x5, lr, [x16]
    // 0xb32cb4: blr             lr
    // 0xb32cb8: r0 = Null
    //     0xb32cb8: mov             x0, NULL
    // 0xb32cbc: LeaveFrame
    //     0xb32cbc: mov             SP, fp
    //     0xb32cc0: ldp             fp, lr, [SP], #0x10
    // 0xb32cc4: ret
    //     0xb32cc4: ret             
    // 0xb32cc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb32cc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb32ccc: b               #0xb32c7c
    // 0xb32cd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb32cd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4117, size: 0x30, field offset: 0xc
//   const constructor, 
class BottomView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e718, size: 0x24
    // 0xc7e718: EnterFrame
    //     0xc7e718: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e71c: mov             fp, SP
    // 0xc7e720: mov             x0, x1
    // 0xc7e724: r1 = <BottomView>
    //     0xc7e724: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a98] TypeArguments: <BottomView>
    //     0xc7e728: ldr             x1, [x1, #0xa98]
    // 0xc7e72c: r0 = _BottomViewState()
    //     0xc7e72c: bl              #0xc7e73c  ; Allocate_BottomViewStateStub -> _BottomViewState (size=0x14)
    // 0xc7e730: LeaveFrame
    //     0xc7e730: mov             SP, fp
    //     0xc7e734: ldp             fp, lr, [SP], #0x10
    // 0xc7e738: ret
    //     0xc7e738: ret             
  }
}
