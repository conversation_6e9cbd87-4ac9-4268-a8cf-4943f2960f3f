// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart

// class id: 1049519, size: 0x8
class :: {
}

// class id: 4487, size: 0x28, field offset: 0xc
class BagBottomSheet extends StatelessWidget {

  [closure] bool <anonymous closure>(dynamic, BagImage) {
    // ** addr: 0x901144, size: 0x54
    // 0x901144: EnterFrame
    //     0x901144: stp             fp, lr, [SP, #-0x10]!
    //     0x901148: mov             fp, SP
    // 0x90114c: AllocStack(0x10)
    //     0x90114c: sub             SP, SP, #0x10
    // 0x901150: CheckStackOverflow
    //     0x901150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901154: cmp             SP, x16
    //     0x901158: b.ls            #0x901190
    // 0x90115c: ldr             x0, [fp, #0x10]
    // 0x901160: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x901160: ldur            w1, [x0, #0x17]
    // 0x901164: DecompressPointer r1
    //     0x901164: add             x1, x1, HEAP, lsl #32
    // 0x901168: r0 = LoadClassIdInstr(r1)
    //     0x901168: ldur            x0, [x1, #-1]
    //     0x90116c: ubfx            x0, x0, #0xc, #0x14
    // 0x901170: r16 = "image"
    //     0x901170: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x901174: stp             x16, x1, [SP]
    // 0x901178: mov             lr, x0
    // 0x90117c: ldr             lr, [x21, lr, lsl #3]
    // 0x901180: blr             lr
    // 0x901184: LeaveFrame
    //     0x901184: mov             SP, fp
    //     0x901188: ldp             fp, lr, [SP], #0x10
    // 0x90118c: ret
    //     0x90118c: ret             
    // 0x901190: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901190: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901194: b               #0x90115c
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x901198, size: 0x430
    // 0x901198: EnterFrame
    //     0x901198: stp             fp, lr, [SP, #-0x10]!
    //     0x90119c: mov             fp, SP
    // 0x9011a0: AllocStack(0x30)
    //     0x9011a0: sub             SP, SP, #0x30
    // 0x9011a4: SetupParameters()
    //     0x9011a4: ldr             x0, [fp, #0x20]
    //     0x9011a8: ldur            w1, [x0, #0x17]
    //     0x9011ac: add             x1, x1, HEAP, lsl #32
    //     0x9011b0: stur            x1, [fp, #-8]
    // 0x9011b4: CheckStackOverflow
    //     0x9011b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9011b8: cmp             SP, x16
    //     0x9011bc: b.ls            #0x9015bc
    // 0x9011c0: r1 = 1
    //     0x9011c0: movz            x1, #0x1
    // 0x9011c4: r0 = AllocateContext()
    //     0x9011c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x9011c8: mov             x4, x0
    // 0x9011cc: ldur            x3, [fp, #-8]
    // 0x9011d0: stur            x4, [fp, #-0x18]
    // 0x9011d4: StoreField: r4->field_b = r3
    //     0x9011d4: stur            w3, [x4, #0xb]
    // 0x9011d8: LoadField: r0 = r3->field_f
    //     0x9011d8: ldur            w0, [x3, #0xf]
    // 0x9011dc: DecompressPointer r0
    //     0x9011dc: add             x0, x0, HEAP, lsl #32
    // 0x9011e0: LoadField: r1 = r0->field_b
    //     0x9011e0: ldur            w1, [x0, #0xb]
    // 0x9011e4: DecompressPointer r1
    //     0x9011e4: add             x1, x1, HEAP, lsl #32
    // 0x9011e8: LoadField: r0 = r1->field_b
    //     0x9011e8: ldur            w0, [x1, #0xb]
    // 0x9011ec: DecompressPointer r0
    //     0x9011ec: add             x0, x0, HEAP, lsl #32
    // 0x9011f0: cmp             w0, NULL
    // 0x9011f4: b.ne            #0x901204
    // 0x9011f8: ldr             x5, [fp, #0x10]
    // 0x9011fc: r0 = Null
    //     0x9011fc: mov             x0, NULL
    // 0x901200: b               #0x90124c
    // 0x901204: ldr             x5, [fp, #0x10]
    // 0x901208: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x901208: ldur            w2, [x0, #0x17]
    // 0x90120c: DecompressPointer r2
    //     0x90120c: add             x2, x2, HEAP, lsl #32
    // 0x901210: LoadField: r0 = r2->field_b
    //     0x901210: ldur            w0, [x2, #0xb]
    // 0x901214: r6 = LoadInt32Instr(r5)
    //     0x901214: sbfx            x6, x5, #1, #0x1f
    //     0x901218: tbz             w5, #0, #0x901220
    //     0x90121c: ldur            x6, [x5, #7]
    // 0x901220: r1 = LoadInt32Instr(r0)
    //     0x901220: sbfx            x1, x0, #1, #0x1f
    // 0x901224: mov             x0, x1
    // 0x901228: mov             x1, x6
    // 0x90122c: cmp             x1, x0
    // 0x901230: b.hs            #0x9015c4
    // 0x901234: LoadField: r0 = r2->field_f
    //     0x901234: ldur            w0, [x2, #0xf]
    // 0x901238: DecompressPointer r0
    //     0x901238: add             x0, x0, HEAP, lsl #32
    // 0x90123c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x90123c: add             x16, x0, x6, lsl #2
    //     0x901240: ldur            w1, [x16, #0xf]
    // 0x901244: DecompressPointer r1
    //     0x901244: add             x1, x1, HEAP, lsl #32
    // 0x901248: mov             x0, x1
    // 0x90124c: stur            x0, [fp, #-0x10]
    // 0x901250: cmp             w0, NULL
    // 0x901254: b.ne            #0x901268
    // 0x901258: r0 = Instance_SizedBox
    //     0x901258: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x90125c: LeaveFrame
    //     0x90125c: mov             SP, fp
    //     0x901260: ldp             fp, lr, [SP], #0x10
    // 0x901264: ret
    //     0x901264: ret             
    // 0x901268: LoadField: r1 = r0->field_1b
    //     0x901268: ldur            w1, [x0, #0x1b]
    // 0x90126c: DecompressPointer r1
    //     0x90126c: add             x1, x1, HEAP, lsl #32
    // 0x901270: cmp             w1, NULL
    // 0x901274: b.ne            #0x901290
    // 0x901278: r1 = <BagImage>
    //     0x901278: add             x1, PP, #0x25, lsl #12  ; [pp+0x25528] TypeArguments: <BagImage>
    //     0x90127c: ldr             x1, [x1, #0x528]
    // 0x901280: r2 = 0
    //     0x901280: movz            x2, #0
    // 0x901284: r0 = AllocateArray()
    //     0x901284: bl              #0x16f7198  ; AllocateArrayStub
    // 0x901288: mov             x3, x0
    // 0x90128c: b               #0x901294
    // 0x901290: mov             x3, x1
    // 0x901294: ldur            x2, [fp, #-0x18]
    // 0x901298: stur            x3, [fp, #-0x20]
    // 0x90129c: StoreField: r2->field_f = r3
    //     0x90129c: stur            w3, [x2, #0xf]
    // 0x9012a0: r0 = LoadClassIdInstr(r3)
    //     0x9012a0: ldur            x0, [x3, #-1]
    //     0x9012a4: ubfx            x0, x0, #0xc, #0x14
    // 0x9012a8: mov             x1, x3
    // 0x9012ac: r0 = GDT[cid_x0 + 0xe517]()
    //     0x9012ac: movz            x17, #0xe517
    //     0x9012b0: add             lr, x0, x17
    //     0x9012b4: ldr             lr, [x21, lr, lsl #3]
    //     0x9012b8: blr             lr
    // 0x9012bc: tbnz            w0, #4, #0x901304
    // 0x9012c0: r1 = Function '<anonymous closure>':.
    //     0x9012c0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48560] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x9012c4: ldr             x1, [x1, #0x560]
    // 0x9012c8: r2 = Null
    //     0x9012c8: mov             x2, NULL
    // 0x9012cc: r0 = AllocateClosure()
    //     0x9012cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9012d0: ldur            x2, [fp, #-0x18]
    // 0x9012d4: r1 = Function '<anonymous closure>':.
    //     0x9012d4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48568] AnonymousClosure: (0x901f54), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x9012d8: ldr             x1, [x1, #0x568]
    // 0x9012dc: stur            x0, [fp, #-0x18]
    // 0x9012e0: r0 = AllocateClosure()
    //     0x9012e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9012e4: str             x0, [SP]
    // 0x9012e8: ldur            x1, [fp, #-0x20]
    // 0x9012ec: ldur            x2, [fp, #-0x18]
    // 0x9012f0: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x9012f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x9012f4: ldr             x4, [x4, #0xb48]
    // 0x9012f8: r0 = firstWhere()
    //     0x9012f8: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x9012fc: mov             x1, x0
    // 0x901300: b               #0x901308
    // 0x901304: r1 = Null
    //     0x901304: mov             x1, NULL
    // 0x901308: ldur            x0, [fp, #-8]
    // 0x90130c: LoadField: r2 = r0->field_f
    //     0x90130c: ldur            w2, [x0, #0xf]
    // 0x901310: DecompressPointer r2
    //     0x901310: add             x2, x2, HEAP, lsl #32
    // 0x901314: cmp             w1, NULL
    // 0x901318: b.ne            #0x901324
    // 0x90131c: r1 = Null
    //     0x90131c: mov             x1, NULL
    // 0x901320: b               #0x901330
    // 0x901324: LoadField: r3 = r1->field_b
    //     0x901324: ldur            w3, [x1, #0xb]
    // 0x901328: DecompressPointer r3
    //     0x901328: add             x3, x3, HEAP, lsl #32
    // 0x90132c: mov             x1, x3
    // 0x901330: cmp             w1, NULL
    // 0x901334: b.ne            #0x901340
    // 0x901338: r3 = ""
    //     0x901338: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x90133c: b               #0x901344
    // 0x901340: mov             x3, x1
    // 0x901344: ldr             x4, [fp, #0x10]
    // 0x901348: mov             x1, x2
    // 0x90134c: ldur            x2, [fp, #-0x10]
    // 0x901350: r0 = _buildItemImage()
    //     0x901350: bl              #0x901aec  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemImage
    // 0x901354: mov             x3, x0
    // 0x901358: ldur            x0, [fp, #-8]
    // 0x90135c: stur            x3, [fp, #-0x18]
    // 0x901360: LoadField: r1 = r0->field_f
    //     0x901360: ldur            w1, [x0, #0xf]
    // 0x901364: DecompressPointer r1
    //     0x901364: add             x1, x1, HEAP, lsl #32
    // 0x901368: ldur            x2, [fp, #-0x10]
    // 0x90136c: r0 = _buildItemDetails()
    //     0x90136c: bl              #0x9015c8  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemDetails
    // 0x901370: r1 = Null
    //     0x901370: mov             x1, NULL
    // 0x901374: r2 = 4
    //     0x901374: movz            x2, #0x4
    // 0x901378: stur            x0, [fp, #-0x10]
    // 0x90137c: r0 = AllocateArray()
    //     0x90137c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x901380: mov             x2, x0
    // 0x901384: ldur            x0, [fp, #-0x18]
    // 0x901388: stur            x2, [fp, #-0x20]
    // 0x90138c: StoreField: r2->field_f = r0
    //     0x90138c: stur            w0, [x2, #0xf]
    // 0x901390: ldur            x0, [fp, #-0x10]
    // 0x901394: StoreField: r2->field_13 = r0
    //     0x901394: stur            w0, [x2, #0x13]
    // 0x901398: r1 = <Widget>
    //     0x901398: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x90139c: r0 = AllocateGrowableArray()
    //     0x90139c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9013a0: mov             x1, x0
    // 0x9013a4: ldur            x0, [fp, #-0x20]
    // 0x9013a8: stur            x1, [fp, #-0x10]
    // 0x9013ac: StoreField: r1->field_f = r0
    //     0x9013ac: stur            w0, [x1, #0xf]
    // 0x9013b0: r0 = 4
    //     0x9013b0: movz            x0, #0x4
    // 0x9013b4: StoreField: r1->field_b = r0
    //     0x9013b4: stur            w0, [x1, #0xb]
    // 0x9013b8: r0 = Row()
    //     0x9013b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9013bc: mov             x1, x0
    // 0x9013c0: r0 = Instance_Axis
    //     0x9013c0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9013c4: stur            x1, [fp, #-0x18]
    // 0x9013c8: StoreField: r1->field_f = r0
    //     0x9013c8: stur            w0, [x1, #0xf]
    // 0x9013cc: r0 = Instance_MainAxisAlignment
    //     0x9013cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9013d0: ldr             x0, [x0, #0xa08]
    // 0x9013d4: StoreField: r1->field_13 = r0
    //     0x9013d4: stur            w0, [x1, #0x13]
    // 0x9013d8: r2 = Instance_MainAxisSize
    //     0x9013d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9013dc: ldr             x2, [x2, #0xa10]
    // 0x9013e0: ArrayStore: r1[0] = r2  ; List_4
    //     0x9013e0: stur            w2, [x1, #0x17]
    // 0x9013e4: r3 = Instance_CrossAxisAlignment
    //     0x9013e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9013e8: ldr             x3, [x3, #0xa18]
    // 0x9013ec: StoreField: r1->field_1b = r3
    //     0x9013ec: stur            w3, [x1, #0x1b]
    // 0x9013f0: r4 = Instance_VerticalDirection
    //     0x9013f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9013f4: ldr             x4, [x4, #0xa20]
    // 0x9013f8: StoreField: r1->field_23 = r4
    //     0x9013f8: stur            w4, [x1, #0x23]
    // 0x9013fc: r5 = Instance_Clip
    //     0x9013fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x901400: ldr             x5, [x5, #0x38]
    // 0x901404: StoreField: r1->field_2b = r5
    //     0x901404: stur            w5, [x1, #0x2b]
    // 0x901408: StoreField: r1->field_2f = rZR
    //     0x901408: stur            xzr, [x1, #0x2f]
    // 0x90140c: ldur            x6, [fp, #-0x10]
    // 0x901410: StoreField: r1->field_b = r6
    //     0x901410: stur            w6, [x1, #0xb]
    // 0x901414: r0 = Padding()
    //     0x901414: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x901418: mov             x3, x0
    // 0x90141c: r0 = Instance_EdgeInsets
    //     0x90141c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x901420: ldr             x0, [x0, #0x668]
    // 0x901424: stur            x3, [fp, #-0x10]
    // 0x901428: StoreField: r3->field_f = r0
    //     0x901428: stur            w0, [x3, #0xf]
    // 0x90142c: ldur            x0, [fp, #-0x18]
    // 0x901430: StoreField: r3->field_b = r0
    //     0x901430: stur            w0, [x3, #0xb]
    // 0x901434: r1 = Null
    //     0x901434: mov             x1, NULL
    // 0x901438: r2 = 2
    //     0x901438: movz            x2, #0x2
    // 0x90143c: r0 = AllocateArray()
    //     0x90143c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x901440: mov             x2, x0
    // 0x901444: ldur            x0, [fp, #-0x10]
    // 0x901448: stur            x2, [fp, #-0x18]
    // 0x90144c: StoreField: r2->field_f = r0
    //     0x90144c: stur            w0, [x2, #0xf]
    // 0x901450: r1 = <Widget>
    //     0x901450: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x901454: r0 = AllocateGrowableArray()
    //     0x901454: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x901458: mov             x2, x0
    // 0x90145c: ldur            x0, [fp, #-0x18]
    // 0x901460: stur            x2, [fp, #-0x10]
    // 0x901464: StoreField: r2->field_f = r0
    //     0x901464: stur            w0, [x2, #0xf]
    // 0x901468: r0 = 2
    //     0x901468: movz            x0, #0x2
    // 0x90146c: StoreField: r2->field_b = r0
    //     0x90146c: stur            w0, [x2, #0xb]
    // 0x901470: ldur            x0, [fp, #-8]
    // 0x901474: LoadField: r1 = r0->field_13
    //     0x901474: ldur            w1, [x0, #0x13]
    // 0x901478: r0 = LoadInt32Instr(r1)
    //     0x901478: sbfx            x0, x1, #1, #0x1f
    // 0x90147c: sub             x1, x0, #1
    // 0x901480: ldr             x0, [fp, #0x10]
    // 0x901484: r3 = LoadInt32Instr(r0)
    //     0x901484: sbfx            x3, x0, #1, #0x1f
    //     0x901488: tbz             w0, #0, #0x901490
    //     0x90148c: ldur            x3, [x0, #7]
    // 0x901490: cmp             x3, x1
    // 0x901494: b.ge            #0x90155c
    // 0x901498: r1 = Instance_Color
    //     0x901498: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x90149c: d0 = 0.100000
    //     0x90149c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9014a0: r0 = withOpacity()
    //     0x9014a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9014a4: stur            x0, [fp, #-8]
    // 0x9014a8: r0 = Divider()
    //     0x9014a8: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x9014ac: mov             x1, x0
    // 0x9014b0: r0 = 1.000000
    //     0x9014b0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x9014b4: stur            x1, [fp, #-0x18]
    // 0x9014b8: StoreField: r1->field_f = r0
    //     0x9014b8: stur            w0, [x1, #0xf]
    // 0x9014bc: ldur            x0, [fp, #-8]
    // 0x9014c0: StoreField: r1->field_1f = r0
    //     0x9014c0: stur            w0, [x1, #0x1f]
    // 0x9014c4: r0 = Padding()
    //     0x9014c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9014c8: mov             x2, x0
    // 0x9014cc: r0 = Instance_EdgeInsets
    //     0x9014cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x9014d0: ldr             x0, [x0, #0x1f0]
    // 0x9014d4: stur            x2, [fp, #-8]
    // 0x9014d8: StoreField: r2->field_f = r0
    //     0x9014d8: stur            w0, [x2, #0xf]
    // 0x9014dc: ldur            x0, [fp, #-0x18]
    // 0x9014e0: StoreField: r2->field_b = r0
    //     0x9014e0: stur            w0, [x2, #0xb]
    // 0x9014e4: ldur            x0, [fp, #-0x10]
    // 0x9014e8: LoadField: r1 = r0->field_b
    //     0x9014e8: ldur            w1, [x0, #0xb]
    // 0x9014ec: LoadField: r3 = r0->field_f
    //     0x9014ec: ldur            w3, [x0, #0xf]
    // 0x9014f0: DecompressPointer r3
    //     0x9014f0: add             x3, x3, HEAP, lsl #32
    // 0x9014f4: LoadField: r4 = r3->field_b
    //     0x9014f4: ldur            w4, [x3, #0xb]
    // 0x9014f8: r3 = LoadInt32Instr(r1)
    //     0x9014f8: sbfx            x3, x1, #1, #0x1f
    // 0x9014fc: stur            x3, [fp, #-0x28]
    // 0x901500: r1 = LoadInt32Instr(r4)
    //     0x901500: sbfx            x1, x4, #1, #0x1f
    // 0x901504: cmp             x3, x1
    // 0x901508: b.ne            #0x901514
    // 0x90150c: mov             x1, x0
    // 0x901510: r0 = _growToNextCapacity()
    //     0x901510: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x901514: ldur            x2, [fp, #-0x10]
    // 0x901518: ldur            x3, [fp, #-0x28]
    // 0x90151c: add             x0, x3, #1
    // 0x901520: lsl             x1, x0, #1
    // 0x901524: StoreField: r2->field_b = r1
    //     0x901524: stur            w1, [x2, #0xb]
    // 0x901528: LoadField: r1 = r2->field_f
    //     0x901528: ldur            w1, [x2, #0xf]
    // 0x90152c: DecompressPointer r1
    //     0x90152c: add             x1, x1, HEAP, lsl #32
    // 0x901530: ldur            x0, [fp, #-8]
    // 0x901534: ArrayStore: r1[r3] = r0  ; List_4
    //     0x901534: add             x25, x1, x3, lsl #2
    //     0x901538: add             x25, x25, #0xf
    //     0x90153c: str             w0, [x25]
    //     0x901540: tbz             w0, #0, #0x90155c
    //     0x901544: ldurb           w16, [x1, #-1]
    //     0x901548: ldurb           w17, [x0, #-1]
    //     0x90154c: and             x16, x17, x16, lsr #2
    //     0x901550: tst             x16, HEAP, lsr #32
    //     0x901554: b.eq            #0x90155c
    //     0x901558: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x90155c: r0 = Column()
    //     0x90155c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x901560: r1 = Instance_Axis
    //     0x901560: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x901564: StoreField: r0->field_f = r1
    //     0x901564: stur            w1, [x0, #0xf]
    // 0x901568: r1 = Instance_MainAxisAlignment
    //     0x901568: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x90156c: ldr             x1, [x1, #0xa08]
    // 0x901570: StoreField: r0->field_13 = r1
    //     0x901570: stur            w1, [x0, #0x13]
    // 0x901574: r1 = Instance_MainAxisSize
    //     0x901574: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x901578: ldr             x1, [x1, #0xa10]
    // 0x90157c: ArrayStore: r0[0] = r1  ; List_4
    //     0x90157c: stur            w1, [x0, #0x17]
    // 0x901580: r1 = Instance_CrossAxisAlignment
    //     0x901580: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x901584: ldr             x1, [x1, #0xa18]
    // 0x901588: StoreField: r0->field_1b = r1
    //     0x901588: stur            w1, [x0, #0x1b]
    // 0x90158c: r1 = Instance_VerticalDirection
    //     0x90158c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x901590: ldr             x1, [x1, #0xa20]
    // 0x901594: StoreField: r0->field_23 = r1
    //     0x901594: stur            w1, [x0, #0x23]
    // 0x901598: r1 = Instance_Clip
    //     0x901598: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x90159c: ldr             x1, [x1, #0x38]
    // 0x9015a0: StoreField: r0->field_2b = r1
    //     0x9015a0: stur            w1, [x0, #0x2b]
    // 0x9015a4: StoreField: r0->field_2f = rZR
    //     0x9015a4: stur            xzr, [x0, #0x2f]
    // 0x9015a8: ldur            x1, [fp, #-0x10]
    // 0x9015ac: StoreField: r0->field_b = r1
    //     0x9015ac: stur            w1, [x0, #0xb]
    // 0x9015b0: LeaveFrame
    //     0x9015b0: mov             SP, fp
    //     0x9015b4: ldp             fp, lr, [SP], #0x10
    // 0x9015b8: ret
    //     0x9015b8: ret             
    // 0x9015bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9015bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9015c0: b               #0x9011c0
    // 0x9015c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9015c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildItemDetails(/* No info */) {
    // ** addr: 0x9015c8, size: 0x524
    // 0x9015c8: EnterFrame
    //     0x9015c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9015cc: mov             fp, SP
    // 0x9015d0: AllocStack(0x68)
    //     0x9015d0: sub             SP, SP, #0x68
    // 0x9015d4: SetupParameters(BagBottomSheet this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x9015d4: mov             x0, x1
    //     0x9015d8: stur            x1, [fp, #-0x10]
    //     0x9015dc: stur            x2, [fp, #-0x18]
    // 0x9015e0: CheckStackOverflow
    //     0x9015e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9015e4: cmp             SP, x16
    //     0x9015e8: b.ls            #0x901ae4
    // 0x9015ec: LoadField: r1 = r2->field_6b
    //     0x9015ec: ldur            w1, [x2, #0x6b]
    // 0x9015f0: DecompressPointer r1
    //     0x9015f0: add             x1, x1, HEAP, lsl #32
    // 0x9015f4: cmp             w1, NULL
    // 0x9015f8: b.ne            #0x901604
    // 0x9015fc: r1 = Null
    //     0x9015fc: mov             x1, NULL
    // 0x901600: b               #0x901618
    // 0x901604: LoadField: r3 = r1->field_b
    //     0x901604: ldur            w3, [x1, #0xb]
    // 0x901608: cbnz            w3, #0x901614
    // 0x90160c: r1 = false
    //     0x90160c: add             x1, NULL, #0x30  ; false
    // 0x901610: b               #0x901618
    // 0x901614: r1 = true
    //     0x901614: add             x1, NULL, #0x20  ; true
    // 0x901618: cmp             w1, NULL
    // 0x90161c: b.ne            #0x901628
    // 0x901620: r3 = false
    //     0x901620: add             x3, NULL, #0x30  ; false
    // 0x901624: b               #0x90162c
    // 0x901628: mov             x3, x1
    // 0x90162c: stur            x3, [fp, #-8]
    // 0x901630: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x901630: ldur            w1, [x2, #0x17]
    // 0x901634: DecompressPointer r1
    //     0x901634: add             x1, x1, HEAP, lsl #32
    // 0x901638: cmp             w1, NULL
    // 0x90163c: b.ne            #0x901648
    // 0x901640: r1 = Null
    //     0x901640: mov             x1, NULL
    // 0x901644: b               #0x901654
    // 0x901648: LoadField: r4 = r1->field_7
    //     0x901648: ldur            w4, [x1, #7]
    // 0x90164c: DecompressPointer r4
    //     0x90164c: add             x4, x4, HEAP, lsl #32
    // 0x901650: mov             x1, x4
    // 0x901654: cmp             w1, NULL
    // 0x901658: b.ne            #0x901660
    // 0x90165c: r1 = ""
    //     0x90165c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x901660: r0 = capitalizeFirstWord()
    //     0x901660: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x901664: stur            x0, [fp, #-0x20]
    // 0x901668: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x901668: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x90166c: ldr             x0, [x0, #0x1ab0]
    //     0x901670: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x901674: cmp             w0, w16
    //     0x901678: b.ne            #0x901688
    //     0x90167c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x901680: ldr             x2, [x2, #0x60]
    //     0x901684: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x901688: LoadField: r2 = r0->field_87
    //     0x901688: ldur            w2, [x0, #0x87]
    // 0x90168c: DecompressPointer r2
    //     0x90168c: add             x2, x2, HEAP, lsl #32
    // 0x901690: stur            x2, [fp, #-0x30]
    // 0x901694: LoadField: r0 = r2->field_2b
    //     0x901694: ldur            w0, [x2, #0x2b]
    // 0x901698: DecompressPointer r0
    //     0x901698: add             x0, x0, HEAP, lsl #32
    // 0x90169c: stur            x0, [fp, #-0x28]
    // 0x9016a0: r1 = Instance_Color
    //     0x9016a0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9016a4: d0 = 0.700000
    //     0x9016a4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9016a8: ldr             d0, [x17, #0xf48]
    // 0x9016ac: r0 = withOpacity()
    //     0x9016ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9016b0: r16 = 14.000000
    //     0x9016b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x9016b4: ldr             x16, [x16, #0x1d8]
    // 0x9016b8: stp             x16, x0, [SP]
    // 0x9016bc: ldur            x1, [fp, #-0x28]
    // 0x9016c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9016c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9016c4: ldr             x4, [x4, #0x9b8]
    // 0x9016c8: r0 = copyWith()
    //     0x9016c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9016cc: stur            x0, [fp, #-0x38]
    // 0x9016d0: r0 = Text()
    //     0x9016d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9016d4: mov             x1, x0
    // 0x9016d8: ldur            x0, [fp, #-0x20]
    // 0x9016dc: stur            x1, [fp, #-0x40]
    // 0x9016e0: StoreField: r1->field_b = r0
    //     0x9016e0: stur            w0, [x1, #0xb]
    // 0x9016e4: ldur            x0, [fp, #-0x38]
    // 0x9016e8: StoreField: r1->field_13 = r0
    //     0x9016e8: stur            w0, [x1, #0x13]
    // 0x9016ec: r0 = Instance_TextOverflow
    //     0x9016ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x9016f0: ldr             x0, [x0, #0xe10]
    // 0x9016f4: StoreField: r1->field_2b = r0
    //     0x9016f4: stur            w0, [x1, #0x2b]
    // 0x9016f8: r0 = 2
    //     0x9016f8: movz            x0, #0x2
    // 0x9016fc: StoreField: r1->field_37 = r0
    //     0x9016fc: stur            w0, [x1, #0x37]
    // 0x901700: r0 = SizedBox()
    //     0x901700: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x901704: mov             x3, x0
    // 0x901708: r0 = 200.000000
    //     0x901708: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0x90170c: ldr             x0, [x0, #0x570]
    // 0x901710: stur            x3, [fp, #-0x38]
    // 0x901714: StoreField: r3->field_f = r0
    //     0x901714: stur            w0, [x3, #0xf]
    // 0x901718: ldur            x0, [fp, #-0x40]
    // 0x90171c: StoreField: r3->field_b = r0
    //     0x90171c: stur            w0, [x3, #0xb]
    // 0x901720: ldur            x0, [fp, #-0x18]
    // 0x901724: LoadField: r4 = r0->field_5f
    //     0x901724: ldur            w4, [x0, #0x5f]
    // 0x901728: DecompressPointer r4
    //     0x901728: add             x4, x4, HEAP, lsl #32
    // 0x90172c: stur            x4, [fp, #-0x20]
    // 0x901730: r1 = Null
    //     0x901730: mov             x1, NULL
    // 0x901734: r2 = 8
    //     0x901734: movz            x2, #0x8
    // 0x901738: r0 = AllocateArray()
    //     0x901738: bl              #0x16f7198  ; AllocateArrayStub
    // 0x90173c: mov             x1, x0
    // 0x901740: ldur            x0, [fp, #-0x20]
    // 0x901744: StoreField: r1->field_f = r0
    //     0x901744: stur            w0, [x1, #0xf]
    // 0x901748: r16 = " * "
    //     0x901748: add             x16, PP, #0x48, lsl #12  ; [pp+0x48578] " * "
    //     0x90174c: ldr             x16, [x16, #0x578]
    // 0x901750: StoreField: r1->field_13 = r16
    //     0x901750: stur            w16, [x1, #0x13]
    // 0x901754: ldur            x0, [fp, #-0x18]
    // 0x901758: LoadField: r2 = r0->field_3f
    //     0x901758: ldur            w2, [x0, #0x3f]
    // 0x90175c: DecompressPointer r2
    //     0x90175c: add             x2, x2, HEAP, lsl #32
    // 0x901760: ArrayStore: r1[0] = r2  ; List_4
    //     0x901760: stur            w2, [x1, #0x17]
    // 0x901764: r16 = " "
    //     0x901764: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x901768: StoreField: r1->field_1b = r16
    //     0x901768: stur            w16, [x1, #0x1b]
    // 0x90176c: str             x1, [SP]
    // 0x901770: r0 = _interpolate()
    //     0x901770: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x901774: mov             x2, x0
    // 0x901778: ldur            x0, [fp, #-0x30]
    // 0x90177c: stur            x2, [fp, #-0x20]
    // 0x901780: LoadField: r1 = r0->field_7
    //     0x901780: ldur            w1, [x0, #7]
    // 0x901784: DecompressPointer r1
    //     0x901784: add             x1, x1, HEAP, lsl #32
    // 0x901788: r16 = 12.000000
    //     0x901788: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x90178c: ldr             x16, [x16, #0x9e8]
    // 0x901790: r30 = Instance_Color
    //     0x901790: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x901794: stp             lr, x16, [SP]
    // 0x901798: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x901798: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x90179c: ldr             x4, [x4, #0xaa0]
    // 0x9017a0: r0 = copyWith()
    //     0x9017a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9017a4: stur            x0, [fp, #-0x30]
    // 0x9017a8: r0 = Text()
    //     0x9017a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9017ac: mov             x1, x0
    // 0x9017b0: ldur            x0, [fp, #-0x20]
    // 0x9017b4: stur            x1, [fp, #-0x40]
    // 0x9017b8: StoreField: r1->field_b = r0
    //     0x9017b8: stur            w0, [x1, #0xb]
    // 0x9017bc: ldur            x0, [fp, #-0x30]
    // 0x9017c0: StoreField: r1->field_13 = r0
    //     0x9017c0: stur            w0, [x1, #0x13]
    // 0x9017c4: ldur            x0, [fp, #-0x18]
    // 0x9017c8: LoadField: r2 = r0->field_2f
    //     0x9017c8: ldur            w2, [x0, #0x2f]
    // 0x9017cc: DecompressPointer r2
    //     0x9017cc: add             x2, x2, HEAP, lsl #32
    // 0x9017d0: str             x2, [SP]
    // 0x9017d4: r0 = _interpolateSingle()
    //     0x9017d4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x9017d8: r1 = Instance_Color
    //     0x9017d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9017dc: d0 = 0.400000
    //     0x9017dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x9017e0: stur            x0, [fp, #-0x18]
    // 0x9017e4: r0 = withOpacity()
    //     0x9017e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9017e8: r16 = 12.000000
    //     0x9017e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9017ec: ldr             x16, [x16, #0x9e8]
    // 0x9017f0: stp             x16, x0, [SP, #8]
    // 0x9017f4: r16 = Instance_TextDecoration
    //     0x9017f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x9017f8: ldr             x16, [x16, #0xe30]
    // 0x9017fc: str             x16, [SP]
    // 0x901800: ldur            x1, [fp, #-0x28]
    // 0x901804: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0x901804: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0x901808: ldr             x4, [x4, #0x7c8]
    // 0x90180c: r0 = copyWith()
    //     0x90180c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x901810: stur            x0, [fp, #-0x20]
    // 0x901814: r0 = Text()
    //     0x901814: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x901818: mov             x3, x0
    // 0x90181c: ldur            x0, [fp, #-0x18]
    // 0x901820: stur            x3, [fp, #-0x30]
    // 0x901824: StoreField: r3->field_b = r0
    //     0x901824: stur            w0, [x3, #0xb]
    // 0x901828: ldur            x0, [fp, #-0x20]
    // 0x90182c: StoreField: r3->field_13 = r0
    //     0x90182c: stur            w0, [x3, #0x13]
    // 0x901830: r1 = Null
    //     0x901830: mov             x1, NULL
    // 0x901834: r2 = 4
    //     0x901834: movz            x2, #0x4
    // 0x901838: r0 = AllocateArray()
    //     0x901838: bl              #0x16f7198  ; AllocateArrayStub
    // 0x90183c: mov             x2, x0
    // 0x901840: ldur            x0, [fp, #-0x40]
    // 0x901844: stur            x2, [fp, #-0x18]
    // 0x901848: StoreField: r2->field_f = r0
    //     0x901848: stur            w0, [x2, #0xf]
    // 0x90184c: ldur            x0, [fp, #-0x30]
    // 0x901850: StoreField: r2->field_13 = r0
    //     0x901850: stur            w0, [x2, #0x13]
    // 0x901854: r1 = <Widget>
    //     0x901854: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x901858: r0 = AllocateGrowableArray()
    //     0x901858: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x90185c: mov             x2, x0
    // 0x901860: ldur            x0, [fp, #-0x18]
    // 0x901864: stur            x2, [fp, #-0x20]
    // 0x901868: StoreField: r2->field_f = r0
    //     0x901868: stur            w0, [x2, #0xf]
    // 0x90186c: r0 = 4
    //     0x90186c: movz            x0, #0x4
    // 0x901870: StoreField: r2->field_b = r0
    //     0x901870: stur            w0, [x2, #0xb]
    // 0x901874: ldur            x1, [fp, #-8]
    // 0x901878: tbnz            w1, #4, #0x9019c0
    // 0x90187c: ldur            x1, [fp, #-0x10]
    // 0x901880: LoadField: r3 = r1->field_23
    //     0x901880: ldur            w3, [x1, #0x23]
    // 0x901884: DecompressPointer r3
    //     0x901884: add             x3, x3, HEAP, lsl #32
    // 0x901888: stur            x3, [fp, #-8]
    // 0x90188c: r16 = Instance_Color
    //     0x90188c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x901890: r30 = 12.000000
    //     0x901890: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x901894: ldr             lr, [lr, #0x9e8]
    // 0x901898: stp             lr, x16, [SP]
    // 0x90189c: ldur            x1, [fp, #-0x28]
    // 0x9018a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9018a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9018a4: ldr             x4, [x4, #0x9b8]
    // 0x9018a8: r0 = copyWith()
    //     0x9018a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9018ac: stur            x0, [fp, #-0x10]
    // 0x9018b0: r0 = Text()
    //     0x9018b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9018b4: mov             x1, x0
    // 0x9018b8: r0 = "Customised"
    //     0x9018b8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x9018bc: ldr             x0, [x0, #0xd88]
    // 0x9018c0: stur            x1, [fp, #-0x18]
    // 0x9018c4: StoreField: r1->field_b = r0
    //     0x9018c4: stur            w0, [x1, #0xb]
    // 0x9018c8: ldur            x0, [fp, #-0x10]
    // 0x9018cc: StoreField: r1->field_13 = r0
    //     0x9018cc: stur            w0, [x1, #0x13]
    // 0x9018d0: r0 = Center()
    //     0x9018d0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x9018d4: mov             x1, x0
    // 0x9018d8: r0 = Instance_Alignment
    //     0x9018d8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9018dc: ldr             x0, [x0, #0xb10]
    // 0x9018e0: stur            x1, [fp, #-0x10]
    // 0x9018e4: StoreField: r1->field_f = r0
    //     0x9018e4: stur            w0, [x1, #0xf]
    // 0x9018e8: ldur            x0, [fp, #-0x18]
    // 0x9018ec: StoreField: r1->field_b = r0
    //     0x9018ec: stur            w0, [x1, #0xb]
    // 0x9018f0: r0 = Container()
    //     0x9018f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9018f4: stur            x0, [fp, #-0x18]
    // 0x9018f8: r16 = 16.000000
    //     0x9018f8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9018fc: ldr             x16, [x16, #0x188]
    // 0x901900: r30 = 86.000000
    //     0x901900: add             lr, PP, #0x48, lsl #12  ; [pp+0x48580] 86
    //     0x901904: ldr             lr, [lr, #0x580]
    // 0x901908: stp             lr, x16, [SP, #0x10]
    // 0x90190c: ldur            x16, [fp, #-8]
    // 0x901910: ldur            lr, [fp, #-0x10]
    // 0x901914: stp             lr, x16, [SP]
    // 0x901918: mov             x1, x0
    // 0x90191c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0x90191c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48588] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x901920: ldr             x4, [x4, #0x588]
    // 0x901924: r0 = Container()
    //     0x901924: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x901928: r0 = Padding()
    //     0x901928: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x90192c: mov             x2, x0
    // 0x901930: r0 = Instance_EdgeInsets
    //     0x901930: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0x901934: ldr             x0, [x0, #0xe60]
    // 0x901938: stur            x2, [fp, #-8]
    // 0x90193c: StoreField: r2->field_f = r0
    //     0x90193c: stur            w0, [x2, #0xf]
    // 0x901940: ldur            x0, [fp, #-0x18]
    // 0x901944: StoreField: r2->field_b = r0
    //     0x901944: stur            w0, [x2, #0xb]
    // 0x901948: ldur            x0, [fp, #-0x20]
    // 0x90194c: LoadField: r1 = r0->field_b
    //     0x90194c: ldur            w1, [x0, #0xb]
    // 0x901950: LoadField: r3 = r0->field_f
    //     0x901950: ldur            w3, [x0, #0xf]
    // 0x901954: DecompressPointer r3
    //     0x901954: add             x3, x3, HEAP, lsl #32
    // 0x901958: LoadField: r4 = r3->field_b
    //     0x901958: ldur            w4, [x3, #0xb]
    // 0x90195c: r3 = LoadInt32Instr(r1)
    //     0x90195c: sbfx            x3, x1, #1, #0x1f
    // 0x901960: stur            x3, [fp, #-0x48]
    // 0x901964: r1 = LoadInt32Instr(r4)
    //     0x901964: sbfx            x1, x4, #1, #0x1f
    // 0x901968: cmp             x3, x1
    // 0x90196c: b.ne            #0x901978
    // 0x901970: mov             x1, x0
    // 0x901974: r0 = _growToNextCapacity()
    //     0x901974: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x901978: ldur            x2, [fp, #-0x20]
    // 0x90197c: ldur            x3, [fp, #-0x48]
    // 0x901980: add             x0, x3, #1
    // 0x901984: lsl             x1, x0, #1
    // 0x901988: StoreField: r2->field_b = r1
    //     0x901988: stur            w1, [x2, #0xb]
    // 0x90198c: LoadField: r1 = r2->field_f
    //     0x90198c: ldur            w1, [x2, #0xf]
    // 0x901990: DecompressPointer r1
    //     0x901990: add             x1, x1, HEAP, lsl #32
    // 0x901994: ldur            x0, [fp, #-8]
    // 0x901998: ArrayStore: r1[r3] = r0  ; List_4
    //     0x901998: add             x25, x1, x3, lsl #2
    //     0x90199c: add             x25, x25, #0xf
    //     0x9019a0: str             w0, [x25]
    //     0x9019a4: tbz             w0, #0, #0x9019c0
    //     0x9019a8: ldurb           w16, [x1, #-1]
    //     0x9019ac: ldurb           w17, [x0, #-1]
    //     0x9019b0: and             x16, x17, x16, lsr #2
    //     0x9019b4: tst             x16, HEAP, lsr #32
    //     0x9019b8: b.eq            #0x9019c0
    //     0x9019bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9019c0: ldur            x0, [fp, #-0x38]
    // 0x9019c4: r0 = Row()
    //     0x9019c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9019c8: mov             x3, x0
    // 0x9019cc: r0 = Instance_Axis
    //     0x9019cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9019d0: stur            x3, [fp, #-8]
    // 0x9019d4: StoreField: r3->field_f = r0
    //     0x9019d4: stur            w0, [x3, #0xf]
    // 0x9019d8: r0 = Instance_MainAxisAlignment
    //     0x9019d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9019dc: ldr             x0, [x0, #0xa08]
    // 0x9019e0: StoreField: r3->field_13 = r0
    //     0x9019e0: stur            w0, [x3, #0x13]
    // 0x9019e4: r0 = Instance_MainAxisSize
    //     0x9019e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9019e8: ldr             x0, [x0, #0xa10]
    // 0x9019ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x9019ec: stur            w0, [x3, #0x17]
    // 0x9019f0: r1 = Instance_CrossAxisAlignment
    //     0x9019f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9019f4: ldr             x1, [x1, #0xa18]
    // 0x9019f8: StoreField: r3->field_1b = r1
    //     0x9019f8: stur            w1, [x3, #0x1b]
    // 0x9019fc: r4 = Instance_VerticalDirection
    //     0x9019fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x901a00: ldr             x4, [x4, #0xa20]
    // 0x901a04: StoreField: r3->field_23 = r4
    //     0x901a04: stur            w4, [x3, #0x23]
    // 0x901a08: r5 = Instance_Clip
    //     0x901a08: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x901a0c: ldr             x5, [x5, #0x38]
    // 0x901a10: StoreField: r3->field_2b = r5
    //     0x901a10: stur            w5, [x3, #0x2b]
    // 0x901a14: StoreField: r3->field_2f = rZR
    //     0x901a14: stur            xzr, [x3, #0x2f]
    // 0x901a18: ldur            x1, [fp, #-0x20]
    // 0x901a1c: StoreField: r3->field_b = r1
    //     0x901a1c: stur            w1, [x3, #0xb]
    // 0x901a20: r1 = Null
    //     0x901a20: mov             x1, NULL
    // 0x901a24: r2 = 4
    //     0x901a24: movz            x2, #0x4
    // 0x901a28: r0 = AllocateArray()
    //     0x901a28: bl              #0x16f7198  ; AllocateArrayStub
    // 0x901a2c: mov             x2, x0
    // 0x901a30: ldur            x0, [fp, #-0x38]
    // 0x901a34: stur            x2, [fp, #-0x10]
    // 0x901a38: StoreField: r2->field_f = r0
    //     0x901a38: stur            w0, [x2, #0xf]
    // 0x901a3c: ldur            x0, [fp, #-8]
    // 0x901a40: StoreField: r2->field_13 = r0
    //     0x901a40: stur            w0, [x2, #0x13]
    // 0x901a44: r1 = <Widget>
    //     0x901a44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x901a48: r0 = AllocateGrowableArray()
    //     0x901a48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x901a4c: mov             x1, x0
    // 0x901a50: ldur            x0, [fp, #-0x10]
    // 0x901a54: stur            x1, [fp, #-8]
    // 0x901a58: StoreField: r1->field_f = r0
    //     0x901a58: stur            w0, [x1, #0xf]
    // 0x901a5c: r0 = 4
    //     0x901a5c: movz            x0, #0x4
    // 0x901a60: StoreField: r1->field_b = r0
    //     0x901a60: stur            w0, [x1, #0xb]
    // 0x901a64: r0 = Column()
    //     0x901a64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x901a68: mov             x1, x0
    // 0x901a6c: r0 = Instance_Axis
    //     0x901a6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x901a70: stur            x1, [fp, #-0x10]
    // 0x901a74: StoreField: r1->field_f = r0
    //     0x901a74: stur            w0, [x1, #0xf]
    // 0x901a78: r0 = Instance_MainAxisAlignment
    //     0x901a78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x901a7c: ldr             x0, [x0, #0xab0]
    // 0x901a80: StoreField: r1->field_13 = r0
    //     0x901a80: stur            w0, [x1, #0x13]
    // 0x901a84: r0 = Instance_MainAxisSize
    //     0x901a84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x901a88: ldr             x0, [x0, #0xa10]
    // 0x901a8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x901a8c: stur            w0, [x1, #0x17]
    // 0x901a90: r0 = Instance_CrossAxisAlignment
    //     0x901a90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x901a94: ldr             x0, [x0, #0x890]
    // 0x901a98: StoreField: r1->field_1b = r0
    //     0x901a98: stur            w0, [x1, #0x1b]
    // 0x901a9c: r0 = Instance_VerticalDirection
    //     0x901a9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x901aa0: ldr             x0, [x0, #0xa20]
    // 0x901aa4: StoreField: r1->field_23 = r0
    //     0x901aa4: stur            w0, [x1, #0x23]
    // 0x901aa8: r0 = Instance_Clip
    //     0x901aa8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x901aac: ldr             x0, [x0, #0x38]
    // 0x901ab0: StoreField: r1->field_2b = r0
    //     0x901ab0: stur            w0, [x1, #0x2b]
    // 0x901ab4: StoreField: r1->field_2f = rZR
    //     0x901ab4: stur            xzr, [x1, #0x2f]
    // 0x901ab8: ldur            x0, [fp, #-8]
    // 0x901abc: StoreField: r1->field_b = r0
    //     0x901abc: stur            w0, [x1, #0xb]
    // 0x901ac0: r0 = Padding()
    //     0x901ac0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x901ac4: r1 = Instance_EdgeInsets
    //     0x901ac4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x901ac8: ldr             x1, [x1, #0x1f0]
    // 0x901acc: StoreField: r0->field_f = r1
    //     0x901acc: stur            w1, [x0, #0xf]
    // 0x901ad0: ldur            x1, [fp, #-0x10]
    // 0x901ad4: StoreField: r0->field_b = r1
    //     0x901ad4: stur            w1, [x0, #0xb]
    // 0x901ad8: LeaveFrame
    //     0x901ad8: mov             SP, fp
    //     0x901adc: ldp             fp, lr, [SP], #0x10
    // 0x901ae0: ret
    //     0x901ae0: ret             
    // 0x901ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901ae4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901ae8: b               #0x9015ec
  }
  _ _buildItemImage(/* No info */) {
    // ** addr: 0x901aec, size: 0x248
    // 0x901aec: EnterFrame
    //     0x901aec: stp             fp, lr, [SP, #-0x10]!
    //     0x901af0: mov             fp, SP
    // 0x901af4: AllocStack(0x68)
    //     0x901af4: sub             SP, SP, #0x68
    // 0x901af8: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x901af8: mov             x0, x2
    //     0x901afc: stur            x2, [fp, #-0x10]
    //     0x901b00: mov             x2, x3
    //     0x901b04: stur            x1, [fp, #-8]
    //     0x901b08: stur            x3, [fp, #-0x18]
    // 0x901b0c: CheckStackOverflow
    //     0x901b0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901b10: cmp             SP, x16
    //     0x901b14: b.ls            #0x901d2c
    // 0x901b18: r1 = 2
    //     0x901b18: movz            x1, #0x2
    // 0x901b1c: r0 = AllocateContext()
    //     0x901b1c: bl              #0x16f6108  ; AllocateContextStub
    // 0x901b20: mov             x1, x0
    // 0x901b24: ldur            x0, [fp, #-8]
    // 0x901b28: stur            x1, [fp, #-0x20]
    // 0x901b2c: StoreField: r1->field_f = r0
    //     0x901b2c: stur            w0, [x1, #0xf]
    // 0x901b30: ldur            x2, [fp, #-0x10]
    // 0x901b34: StoreField: r1->field_13 = r2
    //     0x901b34: stur            w2, [x1, #0x13]
    // 0x901b38: r0 = ImageHeaders.forImages()
    //     0x901b38: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x901b3c: stur            x0, [fp, #-0x10]
    // 0x901b40: r0 = CachedNetworkImage()
    //     0x901b40: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x901b44: ldur            x2, [fp, #-0x20]
    // 0x901b48: r1 = Function '<anonymous closure>':.
    //     0x901b48: add             x1, PP, #0x48, lsl #12  ; [pp+0x48590] AnonymousClosure: (0x901e4c), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemImage (0x901aec)
    //     0x901b4c: ldr             x1, [x1, #0x590]
    // 0x901b50: stur            x0, [fp, #-0x28]
    // 0x901b54: r0 = AllocateClosure()
    //     0x901b54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x901b58: r1 = Function '<anonymous closure>':.
    //     0x901b58: add             x1, PP, #0x48, lsl #12  ; [pp+0x48598] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x901b5c: ldr             x1, [x1, #0x598]
    // 0x901b60: r2 = Null
    //     0x901b60: mov             x2, NULL
    // 0x901b64: stur            x0, [fp, #-0x30]
    // 0x901b68: r0 = AllocateClosure()
    //     0x901b68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x901b6c: r16 = 56.000000
    //     0x901b6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x901b70: ldr             x16, [x16, #0xb78]
    // 0x901b74: r30 = 56.000000
    //     0x901b74: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x901b78: ldr             lr, [lr, #0xb78]
    // 0x901b7c: stp             lr, x16, [SP, #0x28]
    // 0x901b80: ldur            x16, [fp, #-0x10]
    // 0x901b84: r30 = Instance_BoxFit
    //     0x901b84: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x901b88: ldr             lr, [lr, #0xb18]
    // 0x901b8c: stp             lr, x16, [SP, #0x18]
    // 0x901b90: r16 = 224
    //     0x901b90: movz            x16, #0xe0
    // 0x901b94: ldur            lr, [fp, #-0x30]
    // 0x901b98: stp             lr, x16, [SP, #8]
    // 0x901b9c: str             x0, [SP]
    // 0x901ba0: ldur            x1, [fp, #-0x28]
    // 0x901ba4: ldur            x2, [fp, #-0x18]
    // 0x901ba8: r4 = const [0, 0x9, 0x7, 0x2, errorWidget, 0x8, fit, 0x5, height, 0x2, httpHeaders, 0x4, memCacheWidth, 0x6, progressIndicatorBuilder, 0x7, width, 0x3, null]
    //     0x901ba8: add             x4, PP, #0x48, lsl #12  ; [pp+0x485a0] List(19) [0, 0x9, 0x7, 0x2, "errorWidget", 0x8, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "memCacheWidth", 0x6, "progressIndicatorBuilder", 0x7, "width", 0x3, Null]
    //     0x901bac: ldr             x4, [x4, #0x5a0]
    // 0x901bb0: r0 = CachedNetworkImage()
    //     0x901bb0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x901bb4: r0 = Container()
    //     0x901bb4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x901bb8: stur            x0, [fp, #-0x10]
    // 0x901bbc: r16 = Instance_BoxDecoration
    //     0x901bbc: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0x901bc0: ldr             x16, [x16, #0x5a8]
    // 0x901bc4: ldur            lr, [fp, #-0x28]
    // 0x901bc8: stp             lr, x16, [SP]
    // 0x901bcc: mov             x1, x0
    // 0x901bd0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x901bd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x901bd4: ldr             x4, [x4, #0x88]
    // 0x901bd8: r0 = Container()
    //     0x901bd8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x901bdc: ldur            x0, [fp, #-8]
    // 0x901be0: LoadField: r1 = r0->field_1f
    //     0x901be0: ldur            w1, [x0, #0x1f]
    // 0x901be4: DecompressPointer r1
    //     0x901be4: add             x1, x1, HEAP, lsl #32
    // 0x901be8: stur            x1, [fp, #-0x18]
    // 0x901bec: r0 = Icon()
    //     0x901bec: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x901bf0: mov             x1, x0
    // 0x901bf4: r0 = Instance_IconData
    //     0x901bf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f090] Obj!IconData@d55181
    //     0x901bf8: ldr             x0, [x0, #0x90]
    // 0x901bfc: stur            x1, [fp, #-8]
    // 0x901c00: StoreField: r1->field_b = r0
    //     0x901c00: stur            w0, [x1, #0xb]
    // 0x901c04: r0 = 20.000000
    //     0x901c04: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x901c08: ldr             x0, [x0, #0xac8]
    // 0x901c0c: StoreField: r1->field_f = r0
    //     0x901c0c: stur            w0, [x1, #0xf]
    // 0x901c10: ldur            x0, [fp, #-0x18]
    // 0x901c14: StoreField: r1->field_23 = r0
    //     0x901c14: stur            w0, [x1, #0x23]
    // 0x901c18: r0 = Container()
    //     0x901c18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x901c1c: stur            x0, [fp, #-0x18]
    // 0x901c20: r16 = 24.000000
    //     0x901c20: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x901c24: ldr             x16, [x16, #0xba8]
    // 0x901c28: r30 = 24.000000
    //     0x901c28: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x901c2c: ldr             lr, [lr, #0xba8]
    // 0x901c30: stp             lr, x16, [SP, #0x10]
    // 0x901c34: r16 = Instance_BoxDecoration
    //     0x901c34: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0x901c38: ldr             x16, [x16, #0x5a8]
    // 0x901c3c: ldur            lr, [fp, #-8]
    // 0x901c40: stp             lr, x16, [SP]
    // 0x901c44: mov             x1, x0
    // 0x901c48: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x901c48: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x901c4c: ldr             x4, [x4, #0x870]
    // 0x901c50: r0 = Container()
    //     0x901c50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x901c54: r0 = InkWell()
    //     0x901c54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x901c58: mov             x3, x0
    // 0x901c5c: ldur            x0, [fp, #-0x18]
    // 0x901c60: stur            x3, [fp, #-8]
    // 0x901c64: StoreField: r3->field_b = r0
    //     0x901c64: stur            w0, [x3, #0xb]
    // 0x901c68: ldur            x2, [fp, #-0x20]
    // 0x901c6c: r1 = Function '<anonymous closure>':.
    //     0x901c6c: add             x1, PP, #0x48, lsl #12  ; [pp+0x485b0] AnonymousClosure: (0x901d40), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemImage (0x901aec)
    //     0x901c70: ldr             x1, [x1, #0x5b0]
    // 0x901c74: r0 = AllocateClosure()
    //     0x901c74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x901c78: mov             x1, x0
    // 0x901c7c: ldur            x0, [fp, #-8]
    // 0x901c80: StoreField: r0->field_f = r1
    //     0x901c80: stur            w1, [x0, #0xf]
    // 0x901c84: r1 = true
    //     0x901c84: add             x1, NULL, #0x20  ; true
    // 0x901c88: StoreField: r0->field_43 = r1
    //     0x901c88: stur            w1, [x0, #0x43]
    // 0x901c8c: r2 = Instance_BoxShape
    //     0x901c8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x901c90: ldr             x2, [x2, #0x80]
    // 0x901c94: StoreField: r0->field_47 = r2
    //     0x901c94: stur            w2, [x0, #0x47]
    // 0x901c98: StoreField: r0->field_6f = r1
    //     0x901c98: stur            w1, [x0, #0x6f]
    // 0x901c9c: r2 = false
    //     0x901c9c: add             x2, NULL, #0x30  ; false
    // 0x901ca0: StoreField: r0->field_73 = r2
    //     0x901ca0: stur            w2, [x0, #0x73]
    // 0x901ca4: StoreField: r0->field_83 = r1
    //     0x901ca4: stur            w1, [x0, #0x83]
    // 0x901ca8: StoreField: r0->field_7b = r2
    //     0x901ca8: stur            w2, [x0, #0x7b]
    // 0x901cac: r1 = Null
    //     0x901cac: mov             x1, NULL
    // 0x901cb0: r2 = 4
    //     0x901cb0: movz            x2, #0x4
    // 0x901cb4: r0 = AllocateArray()
    //     0x901cb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x901cb8: mov             x2, x0
    // 0x901cbc: ldur            x0, [fp, #-0x10]
    // 0x901cc0: stur            x2, [fp, #-0x18]
    // 0x901cc4: StoreField: r2->field_f = r0
    //     0x901cc4: stur            w0, [x2, #0xf]
    // 0x901cc8: ldur            x0, [fp, #-8]
    // 0x901ccc: StoreField: r2->field_13 = r0
    //     0x901ccc: stur            w0, [x2, #0x13]
    // 0x901cd0: r1 = <Widget>
    //     0x901cd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x901cd4: r0 = AllocateGrowableArray()
    //     0x901cd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x901cd8: mov             x1, x0
    // 0x901cdc: ldur            x0, [fp, #-0x18]
    // 0x901ce0: stur            x1, [fp, #-8]
    // 0x901ce4: StoreField: r1->field_f = r0
    //     0x901ce4: stur            w0, [x1, #0xf]
    // 0x901ce8: r0 = 4
    //     0x901ce8: movz            x0, #0x4
    // 0x901cec: StoreField: r1->field_b = r0
    //     0x901cec: stur            w0, [x1, #0xb]
    // 0x901cf0: r0 = Stack()
    //     0x901cf0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x901cf4: r1 = Instance_Alignment
    //     0x901cf4: add             x1, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0x901cf8: ldr             x1, [x1, #0x5b8]
    // 0x901cfc: StoreField: r0->field_f = r1
    //     0x901cfc: stur            w1, [x0, #0xf]
    // 0x901d00: r1 = Instance_StackFit
    //     0x901d00: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x901d04: ldr             x1, [x1, #0xfa8]
    // 0x901d08: ArrayStore: r0[0] = r1  ; List_4
    //     0x901d08: stur            w1, [x0, #0x17]
    // 0x901d0c: r1 = Instance_Clip
    //     0x901d0c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x901d10: ldr             x1, [x1, #0x7e0]
    // 0x901d14: StoreField: r0->field_1b = r1
    //     0x901d14: stur            w1, [x0, #0x1b]
    // 0x901d18: ldur            x1, [fp, #-8]
    // 0x901d1c: StoreField: r0->field_b = r1
    //     0x901d1c: stur            w1, [x0, #0xb]
    // 0x901d20: LeaveFrame
    //     0x901d20: mov             SP, fp
    //     0x901d24: ldp             fp, lr, [SP], #0x10
    // 0x901d28: ret
    //     0x901d28: ret             
    // 0x901d2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901d2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901d30: b               #0x901b18
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x901d40, size: 0x10c
    // 0x901d40: EnterFrame
    //     0x901d40: stp             fp, lr, [SP, #-0x10]!
    //     0x901d44: mov             fp, SP
    // 0x901d48: AllocStack(0x30)
    //     0x901d48: sub             SP, SP, #0x30
    // 0x901d4c: SetupParameters()
    //     0x901d4c: ldr             x0, [fp, #0x10]
    //     0x901d50: ldur            w1, [x0, #0x17]
    //     0x901d54: add             x1, x1, HEAP, lsl #32
    //     0x901d58: stur            x1, [fp, #-8]
    // 0x901d5c: CheckStackOverflow
    //     0x901d5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901d60: cmp             SP, x16
    //     0x901d64: b.ls            #0x901e44
    // 0x901d68: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x901d68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x901d6c: ldr             x0, [x0, #0x1c80]
    //     0x901d70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x901d74: cmp             w0, w16
    //     0x901d78: b.ne            #0x901d84
    //     0x901d7c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x901d80: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x901d84: str             NULL, [SP]
    // 0x901d88: r4 = const [0x1, 0, 0, 0, null]
    //     0x901d88: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x901d8c: r0 = GetNavigation.back()
    //     0x901d8c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x901d90: ldur            x0, [fp, #-8]
    // 0x901d94: LoadField: r1 = r0->field_13
    //     0x901d94: ldur            w1, [x0, #0x13]
    // 0x901d98: DecompressPointer r1
    //     0x901d98: add             x1, x1, HEAP, lsl #32
    // 0x901d9c: LoadField: r2 = r1->field_b
    //     0x901d9c: ldur            w2, [x1, #0xb]
    // 0x901da0: DecompressPointer r2
    //     0x901da0: add             x2, x2, HEAP, lsl #32
    // 0x901da4: stur            x2, [fp, #-0x20]
    // 0x901da8: LoadField: r3 = r1->field_1f
    //     0x901da8: ldur            w3, [x1, #0x1f]
    // 0x901dac: DecompressPointer r3
    //     0x901dac: add             x3, x3, HEAP, lsl #32
    // 0x901db0: cmp             w3, NULL
    // 0x901db4: b.ne            #0x901dc0
    // 0x901db8: r3 = Null
    //     0x901db8: mov             x3, NULL
    // 0x901dbc: b               #0x901dcc
    // 0x901dc0: LoadField: r4 = r3->field_b
    //     0x901dc0: ldur            w4, [x3, #0xb]
    // 0x901dc4: DecompressPointer r4
    //     0x901dc4: add             x4, x4, HEAP, lsl #32
    // 0x901dc8: mov             x3, x4
    // 0x901dcc: stur            x3, [fp, #-0x18]
    // 0x901dd0: LoadField: r4 = r1->field_83
    //     0x901dd0: ldur            w4, [x1, #0x83]
    // 0x901dd4: DecompressPointer r4
    //     0x901dd4: add             x4, x4, HEAP, lsl #32
    // 0x901dd8: stur            x4, [fp, #-0x10]
    // 0x901ddc: r0 = RemoveItemRequest()
    //     0x901ddc: bl              #0x8fc238  ; AllocateRemoveItemRequestStub -> RemoveItemRequest (size=0x18)
    // 0x901de0: mov             x1, x0
    // 0x901de4: ldur            x0, [fp, #-0x20]
    // 0x901de8: StoreField: r1->field_7 = r0
    //     0x901de8: stur            w0, [x1, #7]
    // 0x901dec: ldur            x0, [fp, #-0x18]
    // 0x901df0: StoreField: r1->field_b = r0
    //     0x901df0: stur            w0, [x1, #0xb]
    // 0x901df4: r0 = true
    //     0x901df4: add             x0, NULL, #0x20  ; true
    // 0x901df8: StoreField: r1->field_f = r0
    //     0x901df8: stur            w0, [x1, #0xf]
    // 0x901dfc: ldur            x0, [fp, #-0x10]
    // 0x901e00: StoreField: r1->field_13 = r0
    //     0x901e00: stur            w0, [x1, #0x13]
    // 0x901e04: ldur            x0, [fp, #-8]
    // 0x901e08: LoadField: r2 = r0->field_f
    //     0x901e08: ldur            w2, [x0, #0xf]
    // 0x901e0c: DecompressPointer r2
    //     0x901e0c: add             x2, x2, HEAP, lsl #32
    // 0x901e10: LoadField: r0 = r2->field_f
    //     0x901e10: ldur            w0, [x2, #0xf]
    // 0x901e14: DecompressPointer r0
    //     0x901e14: add             x0, x0, HEAP, lsl #32
    // 0x901e18: stp             x1, x0, [SP]
    // 0x901e1c: r4 = 0
    //     0x901e1c: movz            x4, #0
    // 0x901e20: ldr             x0, [SP, #8]
    // 0x901e24: r16 = UnlinkedCall_0x613b5c
    //     0x901e24: add             x16, PP, #0x48, lsl #12  ; [pp+0x485c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x901e28: add             x16, x16, #0x5c0
    // 0x901e2c: ldp             x5, lr, [x16]
    // 0x901e30: blr             lr
    // 0x901e34: r0 = Null
    //     0x901e34: mov             x0, NULL
    // 0x901e38: LeaveFrame
    //     0x901e38: mov             SP, fp
    //     0x901e3c: ldp             fp, lr, [SP], #0x10
    // 0x901e40: ret
    //     0x901e40: ret             
    // 0x901e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901e44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901e48: b               #0x901d68
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0x901e4c, size: 0x108
    // 0x901e4c: EnterFrame
    //     0x901e4c: stp             fp, lr, [SP, #-0x10]!
    //     0x901e50: mov             fp, SP
    // 0x901e54: AllocStack(0x18)
    //     0x901e54: sub             SP, SP, #0x18
    // 0x901e58: SetupParameters()
    //     0x901e58: ldr             x0, [fp, #0x28]
    //     0x901e5c: ldur            w1, [x0, #0x17]
    //     0x901e60: add             x1, x1, HEAP, lsl #32
    // 0x901e64: ldr             x0, [fp, #0x10]
    // 0x901e68: LoadField: r2 = r0->field_b
    //     0x901e68: ldur            w2, [x0, #0xb]
    // 0x901e6c: DecompressPointer r2
    //     0x901e6c: add             x2, x2, HEAP, lsl #32
    // 0x901e70: cmp             w2, NULL
    // 0x901e74: b.eq            #0x901e90
    // 0x901e78: LoadField: r3 = r0->field_f
    //     0x901e78: ldur            x3, [x0, #0xf]
    // 0x901e7c: r0 = LoadInt32Instr(r2)
    //     0x901e7c: sbfx            x0, x2, #1, #0x1f
    //     0x901e80: tbz             w2, #0, #0x901e88
    //     0x901e84: ldur            x0, [x2, #7]
    // 0x901e88: cmp             x3, x0
    // 0x901e8c: b.le            #0x901e98
    // 0x901e90: r0 = Null
    //     0x901e90: mov             x0, NULL
    // 0x901e94: b               #0x901ecc
    // 0x901e98: scvtf           d0, x3
    // 0x901e9c: scvtf           d1, x0
    // 0x901ea0: fdiv            d2, d0, d1
    // 0x901ea4: r0 = inline_Allocate_Double()
    //     0x901ea4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x901ea8: add             x0, x0, #0x10
    //     0x901eac: cmp             x2, x0
    //     0x901eb0: b.ls            #0x901f3c
    //     0x901eb4: str             x0, [THR, #0x50]  ; THR::top
    //     0x901eb8: sub             x0, x0, #0xf
    //     0x901ebc: movz            x2, #0xe15c
    //     0x901ec0: movk            x2, #0x3, lsl #16
    //     0x901ec4: stur            x2, [x0, #-1]
    // 0x901ec8: StoreField: r0->field_7 = d2
    //     0x901ec8: stur            d2, [x0, #7]
    // 0x901ecc: stur            x0, [fp, #-0x10]
    // 0x901ed0: LoadField: r2 = r1->field_f
    //     0x901ed0: ldur            w2, [x1, #0xf]
    // 0x901ed4: DecompressPointer r2
    //     0x901ed4: add             x2, x2, HEAP, lsl #32
    // 0x901ed8: LoadField: r1 = r2->field_23
    //     0x901ed8: ldur            w1, [x2, #0x23]
    // 0x901edc: DecompressPointer r1
    //     0x901edc: add             x1, x1, HEAP, lsl #32
    // 0x901ee0: stur            x1, [fp, #-8]
    // 0x901ee4: r0 = CircularProgressIndicator()
    //     0x901ee4: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0x901ee8: mov             x1, x0
    // 0x901eec: r0 = 2.000000
    //     0x901eec: add             x0, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0x901ef0: ldr             x0, [x0, #0xdf8]
    // 0x901ef4: stur            x1, [fp, #-0x18]
    // 0x901ef8: StoreField: r1->field_27 = r0
    //     0x901ef8: stur            w0, [x1, #0x27]
    // 0x901efc: r0 = Instance__ActivityIndicatorType
    //     0x901efc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0x901f00: ldr             x0, [x0, #0x1b0]
    // 0x901f04: StoreField: r1->field_23 = r0
    //     0x901f04: stur            w0, [x1, #0x23]
    // 0x901f08: ldur            x0, [fp, #-0x10]
    // 0x901f0c: StoreField: r1->field_b = r0
    //     0x901f0c: stur            w0, [x1, #0xb]
    // 0x901f10: ldur            x0, [fp, #-8]
    // 0x901f14: StoreField: r1->field_13 = r0
    //     0x901f14: stur            w0, [x1, #0x13]
    // 0x901f18: r0 = Center()
    //     0x901f18: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x901f1c: r1 = Instance_Alignment
    //     0x901f1c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x901f20: ldr             x1, [x1, #0xb10]
    // 0x901f24: StoreField: r0->field_f = r1
    //     0x901f24: stur            w1, [x0, #0xf]
    // 0x901f28: ldur            x1, [fp, #-0x18]
    // 0x901f2c: StoreField: r0->field_b = r1
    //     0x901f2c: stur            w1, [x0, #0xb]
    // 0x901f30: LeaveFrame
    //     0x901f30: mov             SP, fp
    //     0x901f34: ldp             fp, lr, [SP], #0x10
    // 0x901f38: ret
    //     0x901f38: ret             
    // 0x901f3c: SaveReg d2
    //     0x901f3c: str             q2, [SP, #-0x10]!
    // 0x901f40: SaveReg r1
    //     0x901f40: str             x1, [SP, #-8]!
    // 0x901f44: r0 = AllocateDouble()
    //     0x901f44: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x901f48: RestoreReg r1
    //     0x901f48: ldr             x1, [SP], #8
    // 0x901f4c: RestoreReg d2
    //     0x901f4c: ldr             q2, [SP], #0x10
    // 0x901f50: b               #0x901ec8
  }
  [closure] BagImage <anonymous closure>(dynamic) {
    // ** addr: 0x901f54, size: 0x60
    // 0x901f54: EnterFrame
    //     0x901f54: stp             fp, lr, [SP, #-0x10]!
    //     0x901f58: mov             fp, SP
    // 0x901f5c: ldr             x0, [fp, #0x10]
    // 0x901f60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x901f60: ldur            w1, [x0, #0x17]
    // 0x901f64: DecompressPointer r1
    //     0x901f64: add             x1, x1, HEAP, lsl #32
    // 0x901f68: CheckStackOverflow
    //     0x901f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901f6c: cmp             SP, x16
    //     0x901f70: b.ls            #0x901fac
    // 0x901f74: LoadField: r0 = r1->field_f
    //     0x901f74: ldur            w0, [x1, #0xf]
    // 0x901f78: DecompressPointer r0
    //     0x901f78: add             x0, x0, HEAP, lsl #32
    // 0x901f7c: r1 = LoadClassIdInstr(r0)
    //     0x901f7c: ldur            x1, [x0, #-1]
    //     0x901f80: ubfx            x1, x1, #0xc, #0x14
    // 0x901f84: mov             x16, x0
    // 0x901f88: mov             x0, x1
    // 0x901f8c: mov             x1, x16
    // 0x901f90: r0 = GDT[cid_x0 + 0xe358]()
    //     0x901f90: movz            x17, #0xe358
    //     0x901f94: add             lr, x0, x17
    //     0x901f98: ldr             lr, [x21, lr, lsl #3]
    //     0x901f9c: blr             lr
    // 0x901fa0: LeaveFrame
    //     0x901fa0: mov             SP, fp
    //     0x901fa4: ldp             fp, lr, [SP], #0x10
    // 0x901fa8: ret
    //     0x901fa8: ret             
    // 0x901fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x901fac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901fb0: b               #0x901f74
  }
  _ _buildItemsList(/* No info */) {
    // ** addr: 0x901fb4, size: 0x11c
    // 0x901fb4: EnterFrame
    //     0x901fb4: stp             fp, lr, [SP, #-0x10]!
    //     0x901fb8: mov             fp, SP
    // 0x901fbc: AllocStack(0x20)
    //     0x901fbc: sub             SP, SP, #0x20
    // 0x901fc0: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x901fc0: stur            x1, [fp, #-8]
    //     0x901fc4: stur            x2, [fp, #-0x10]
    // 0x901fc8: CheckStackOverflow
    //     0x901fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901fcc: cmp             SP, x16
    //     0x901fd0: b.ls            #0x9020b8
    // 0x901fd4: r1 = 2
    //     0x901fd4: movz            x1, #0x2
    // 0x901fd8: r0 = AllocateContext()
    //     0x901fd8: bl              #0x16f6108  ; AllocateContextStub
    // 0x901fdc: mov             x1, x0
    // 0x901fe0: ldur            x0, [fp, #-8]
    // 0x901fe4: StoreField: r1->field_f = r0
    //     0x901fe4: stur            w0, [x1, #0xf]
    // 0x901fe8: ldur            x0, [fp, #-0x10]
    // 0x901fec: lsl             x3, x0, #1
    // 0x901ff0: stur            x3, [fp, #-8]
    // 0x901ff4: StoreField: r1->field_13 = r3
    //     0x901ff4: stur            w3, [x1, #0x13]
    // 0x901ff8: cbnz            x0, #0x90200c
    // 0x901ffc: r0 = Instance_SizedBox
    //     0x901ffc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x902000: LeaveFrame
    //     0x902000: mov             SP, fp
    //     0x902004: ldp             fp, lr, [SP], #0x10
    // 0x902008: ret
    //     0x902008: ret             
    // 0x90200c: cmp             x0, #3
    // 0x902010: b.le            #0x902020
    // 0x902014: d0 = 200.000000
    //     0x902014: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0x902018: ldr             d0, [x17, #0x360]
    // 0x90201c: b               #0x902034
    // 0x902020: d0 = 80.000000
    //     0x902020: ldr             d0, [PP, #0x65b0]  ; [pp+0x65b0] IMM: double(80) from 0x4054000000000000
    // 0x902024: r16 = LoadInt32Instr(r3)
    //     0x902024: sbfx            x16, x3, #1, #0x1f
    // 0x902028: scvtf           d1, w16
    // 0x90202c: fmul            d2, d1, d0
    // 0x902030: mov             v0.16b, v2.16b
    // 0x902034: mov             x2, x1
    // 0x902038: stur            d0, [fp, #-0x20]
    // 0x90203c: r1 = Function '<anonymous closure>':.
    //     0x90203c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48558] AnonymousClosure: (0x901198), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x902040: ldr             x1, [x1, #0x558]
    // 0x902044: r0 = AllocateClosure()
    //     0x902044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x902048: stur            x0, [fp, #-0x18]
    // 0x90204c: r0 = ListView()
    //     0x90204c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x902050: mov             x1, x0
    // 0x902054: ldur            x2, [fp, #-0x18]
    // 0x902058: ldur            x3, [fp, #-8]
    // 0x90205c: stur            x0, [fp, #-8]
    // 0x902060: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x902060: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x902064: r0 = ListView.builder()
    //     0x902064: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x902068: ldur            d0, [fp, #-0x20]
    // 0x90206c: r0 = inline_Allocate_Double()
    //     0x90206c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x902070: add             x0, x0, #0x10
    //     0x902074: cmp             x1, x0
    //     0x902078: b.ls            #0x9020c0
    //     0x90207c: str             x0, [THR, #0x50]  ; THR::top
    //     0x902080: sub             x0, x0, #0xf
    //     0x902084: movz            x1, #0xe15c
    //     0x902088: movk            x1, #0x3, lsl #16
    //     0x90208c: stur            x1, [x0, #-1]
    // 0x902090: StoreField: r0->field_7 = d0
    //     0x902090: stur            d0, [x0, #7]
    // 0x902094: stur            x0, [fp, #-0x18]
    // 0x902098: r0 = SizedBox()
    //     0x902098: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x90209c: ldur            x1, [fp, #-0x18]
    // 0x9020a0: StoreField: r0->field_13 = r1
    //     0x9020a0: stur            w1, [x0, #0x13]
    // 0x9020a4: ldur            x1, [fp, #-8]
    // 0x9020a8: StoreField: r0->field_b = r1
    //     0x9020a8: stur            w1, [x0, #0xb]
    // 0x9020ac: LeaveFrame
    //     0x9020ac: mov             SP, fp
    //     0x9020b0: ldp             fp, lr, [SP], #0x10
    // 0x9020b4: ret
    //     0x9020b4: ret             
    // 0x9020b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9020b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9020bc: b               #0x901fd4
    // 0x9020c0: SaveReg d0
    //     0x9020c0: str             q0, [SP, #-0x10]!
    // 0x9020c4: r0 = AllocateDouble()
    //     0x9020c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9020c8: RestoreReg d0
    //     0x9020c8: ldr             q0, [SP], #0x10
    // 0x9020cc: b               #0x902090
  }
  _ BagBottomSheet(/* No info */) {
    // ** addr: 0x9cfc38, size: 0x404
    // 0x9cfc38: EnterFrame
    //     0x9cfc38: stp             fp, lr, [SP, #-0x10]!
    //     0x9cfc3c: mov             fp, SP
    // 0x9cfc40: AllocStack(0x28)
    //     0x9cfc40: sub             SP, SP, #0x28
    // 0x9cfc44: SetupParameters(BagBottomSheet this /* r1 => r6, fp-0x28 */, dynamic _ /* r2 => r0 */, dynamic _ /* r6 => r2 */, dynamic _ /* r7 => r1 */)
    //     0x9cfc44: mov             x0, x2
    //     0x9cfc48: mov             x4, x2
    //     0x9cfc4c: mov             x2, x6
    //     0x9cfc50: mov             x6, x1
    //     0x9cfc54: stur            x1, [fp, #-0x28]
    //     0x9cfc58: mov             x1, x7
    // 0x9cfc5c: StoreField: r6->field_b = r0
    //     0x9cfc5c: stur            w0, [x6, #0xb]
    //     0x9cfc60: ldurb           w16, [x6, #-1]
    //     0x9cfc64: ldurb           w17, [x0, #-1]
    //     0x9cfc68: and             x16, x17, x16, lsr #2
    //     0x9cfc6c: tst             x16, HEAP, lsr #32
    //     0x9cfc70: b.eq            #0x9cfc78
    //     0x9cfc74: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x9cfc78: mov             x0, x1
    // 0x9cfc7c: StoreField: r6->field_f = r0
    //     0x9cfc7c: stur            w0, [x6, #0xf]
    //     0x9cfc80: ldurb           w16, [x6, #-1]
    //     0x9cfc84: ldurb           w17, [x0, #-1]
    //     0x9cfc88: and             x16, x17, x16, lsr #2
    //     0x9cfc8c: tst             x16, HEAP, lsr #32
    //     0x9cfc90: b.eq            #0x9cfc98
    //     0x9cfc94: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x9cfc98: ldr             x0, [fp, #0x10]
    // 0x9cfc9c: StoreField: r6->field_13 = r0
    //     0x9cfc9c: stur            w0, [x6, #0x13]
    //     0x9cfca0: ldurb           w16, [x6, #-1]
    //     0x9cfca4: ldurb           w17, [x0, #-1]
    //     0x9cfca8: and             x16, x17, x16, lsr #2
    //     0x9cfcac: tst             x16, HEAP, lsr #32
    //     0x9cfcb0: b.eq            #0x9cfcb8
    //     0x9cfcb4: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x9cfcb8: mov             x0, x3
    // 0x9cfcbc: ArrayStore: r6[0] = r0  ; List_4
    //     0x9cfcbc: stur            w0, [x6, #0x17]
    //     0x9cfcc0: ldurb           w16, [x6, #-1]
    //     0x9cfcc4: ldurb           w17, [x0, #-1]
    //     0x9cfcc8: and             x16, x17, x16, lsr #2
    //     0x9cfccc: tst             x16, HEAP, lsr #32
    //     0x9cfcd0: b.eq            #0x9cfcd8
    //     0x9cfcd4: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x9cfcd8: mov             x0, x2
    // 0x9cfcdc: StoreField: r6->field_1b = r0
    //     0x9cfcdc: stur            w0, [x6, #0x1b]
    //     0x9cfce0: ldurb           w16, [x6, #-1]
    //     0x9cfce4: ldurb           w17, [x0, #-1]
    //     0x9cfce8: and             x16, x17, x16, lsr #2
    //     0x9cfcec: tst             x16, HEAP, lsr #32
    //     0x9cfcf0: b.eq            #0x9cfcf8
    //     0x9cfcf4: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x9cfcf8: LoadField: r0 = r5->field_3f
    //     0x9cfcf8: ldur            w0, [x5, #0x3f]
    // 0x9cfcfc: DecompressPointer r0
    //     0x9cfcfc: add             x0, x0, HEAP, lsl #32
    // 0x9cfd00: stur            x0, [fp, #-0x20]
    // 0x9cfd04: cmp             w0, NULL
    // 0x9cfd08: b.ne            #0x9cfd14
    // 0x9cfd0c: r1 = Null
    //     0x9cfd0c: mov             x1, NULL
    // 0x9cfd10: b               #0x9cfd38
    // 0x9cfd14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9cfd14: ldur            w1, [x0, #0x17]
    // 0x9cfd18: DecompressPointer r1
    //     0x9cfd18: add             x1, x1, HEAP, lsl #32
    // 0x9cfd1c: cmp             w1, NULL
    // 0x9cfd20: b.ne            #0x9cfd2c
    // 0x9cfd24: r1 = Null
    //     0x9cfd24: mov             x1, NULL
    // 0x9cfd28: b               #0x9cfd38
    // 0x9cfd2c: LoadField: r2 = r1->field_7
    //     0x9cfd2c: ldur            w2, [x1, #7]
    // 0x9cfd30: DecompressPointer r2
    //     0x9cfd30: add             x2, x2, HEAP, lsl #32
    // 0x9cfd34: mov             x1, x2
    // 0x9cfd38: cmp             w1, NULL
    // 0x9cfd3c: b.ne            #0x9cfd48
    // 0x9cfd40: r1 = 0
    //     0x9cfd40: movz            x1, #0
    // 0x9cfd44: b               #0x9cfd58
    // 0x9cfd48: r2 = LoadInt32Instr(r1)
    //     0x9cfd48: sbfx            x2, x1, #1, #0x1f
    //     0x9cfd4c: tbz             w1, #0, #0x9cfd54
    //     0x9cfd50: ldur            x2, [x1, #7]
    // 0x9cfd54: mov             x1, x2
    // 0x9cfd58: stur            x1, [fp, #-0x18]
    // 0x9cfd5c: cmp             w0, NULL
    // 0x9cfd60: b.ne            #0x9cfd6c
    // 0x9cfd64: r2 = Null
    //     0x9cfd64: mov             x2, NULL
    // 0x9cfd68: b               #0x9cfd90
    // 0x9cfd6c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9cfd6c: ldur            w2, [x0, #0x17]
    // 0x9cfd70: DecompressPointer r2
    //     0x9cfd70: add             x2, x2, HEAP, lsl #32
    // 0x9cfd74: cmp             w2, NULL
    // 0x9cfd78: b.ne            #0x9cfd84
    // 0x9cfd7c: r2 = Null
    //     0x9cfd7c: mov             x2, NULL
    // 0x9cfd80: b               #0x9cfd90
    // 0x9cfd84: LoadField: r3 = r2->field_b
    //     0x9cfd84: ldur            w3, [x2, #0xb]
    // 0x9cfd88: DecompressPointer r3
    //     0x9cfd88: add             x3, x3, HEAP, lsl #32
    // 0x9cfd8c: mov             x2, x3
    // 0x9cfd90: cmp             w2, NULL
    // 0x9cfd94: b.ne            #0x9cfda0
    // 0x9cfd98: r2 = 0
    //     0x9cfd98: movz            x2, #0
    // 0x9cfd9c: b               #0x9cfdb0
    // 0x9cfda0: r3 = LoadInt32Instr(r2)
    //     0x9cfda0: sbfx            x3, x2, #1, #0x1f
    //     0x9cfda4: tbz             w2, #0, #0x9cfdac
    //     0x9cfda8: ldur            x3, [x2, #7]
    // 0x9cfdac: mov             x2, x3
    // 0x9cfdb0: stur            x2, [fp, #-0x10]
    // 0x9cfdb4: cmp             w0, NULL
    // 0x9cfdb8: b.ne            #0x9cfdc4
    // 0x9cfdbc: r3 = Null
    //     0x9cfdbc: mov             x3, NULL
    // 0x9cfdc0: b               #0x9cfde8
    // 0x9cfdc4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9cfdc4: ldur            w3, [x0, #0x17]
    // 0x9cfdc8: DecompressPointer r3
    //     0x9cfdc8: add             x3, x3, HEAP, lsl #32
    // 0x9cfdcc: cmp             w3, NULL
    // 0x9cfdd0: b.ne            #0x9cfddc
    // 0x9cfdd4: r3 = Null
    //     0x9cfdd4: mov             x3, NULL
    // 0x9cfdd8: b               #0x9cfde8
    // 0x9cfddc: LoadField: r4 = r3->field_f
    //     0x9cfddc: ldur            w4, [x3, #0xf]
    // 0x9cfde0: DecompressPointer r4
    //     0x9cfde0: add             x4, x4, HEAP, lsl #32
    // 0x9cfde4: mov             x3, x4
    // 0x9cfde8: cmp             w3, NULL
    // 0x9cfdec: b.ne            #0x9cfdf8
    // 0x9cfdf0: r3 = 0
    //     0x9cfdf0: movz            x3, #0
    // 0x9cfdf4: b               #0x9cfe08
    // 0x9cfdf8: r4 = LoadInt32Instr(r3)
    //     0x9cfdf8: sbfx            x4, x3, #1, #0x1f
    //     0x9cfdfc: tbz             w3, #0, #0x9cfe04
    //     0x9cfe00: ldur            x4, [x3, #7]
    // 0x9cfe04: mov             x3, x4
    // 0x9cfe08: stur            x3, [fp, #-8]
    // 0x9cfe0c: r0 = Color()
    //     0x9cfe0c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x9cfe10: r1 = Instance_ColorSpace
    //     0x9cfe10: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x9cfe14: StoreField: r0->field_27 = r1
    //     0x9cfe14: stur            w1, [x0, #0x27]
    // 0x9cfe18: d0 = 1.000000
    //     0x9cfe18: fmov            d0, #1.00000000
    // 0x9cfe1c: StoreField: r0->field_7 = d0
    //     0x9cfe1c: stur            d0, [x0, #7]
    // 0x9cfe20: ldur            x2, [fp, #-0x18]
    // 0x9cfe24: ubfx            x2, x2, #0, #0x20
    // 0x9cfe28: and             w3, w2, #0xff
    // 0x9cfe2c: ubfx            x3, x3, #0, #0x20
    // 0x9cfe30: scvtf           d0, x3
    // 0x9cfe34: d1 = 255.000000
    //     0x9cfe34: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x9cfe38: fdiv            d2, d0, d1
    // 0x9cfe3c: StoreField: r0->field_f = d2
    //     0x9cfe3c: stur            d2, [x0, #0xf]
    // 0x9cfe40: ldur            x2, [fp, #-0x10]
    // 0x9cfe44: ubfx            x2, x2, #0, #0x20
    // 0x9cfe48: and             w3, w2, #0xff
    // 0x9cfe4c: ubfx            x3, x3, #0, #0x20
    // 0x9cfe50: scvtf           d0, x3
    // 0x9cfe54: fdiv            d2, d0, d1
    // 0x9cfe58: ArrayStore: r0[0] = d2  ; List_8
    //     0x9cfe58: stur            d2, [x0, #0x17]
    // 0x9cfe5c: ldur            x2, [fp, #-8]
    // 0x9cfe60: ubfx            x2, x2, #0, #0x20
    // 0x9cfe64: and             w3, w2, #0xff
    // 0x9cfe68: ubfx            x3, x3, #0, #0x20
    // 0x9cfe6c: scvtf           d0, x3
    // 0x9cfe70: fdiv            d2, d0, d1
    // 0x9cfe74: StoreField: r0->field_1f = d2
    //     0x9cfe74: stur            d2, [x0, #0x1f]
    // 0x9cfe78: ldur            x2, [fp, #-0x28]
    // 0x9cfe7c: StoreField: r2->field_1f = r0
    //     0x9cfe7c: stur            w0, [x2, #0x1f]
    //     0x9cfe80: ldurb           w16, [x2, #-1]
    //     0x9cfe84: ldurb           w17, [x0, #-1]
    //     0x9cfe88: and             x16, x17, x16, lsr #2
    //     0x9cfe8c: tst             x16, HEAP, lsr #32
    //     0x9cfe90: b.eq            #0x9cfe98
    //     0x9cfe94: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9cfe98: ldur            x0, [fp, #-0x20]
    // 0x9cfe9c: cmp             w0, NULL
    // 0x9cfea0: b.ne            #0x9cfeac
    // 0x9cfea4: r3 = Null
    //     0x9cfea4: mov             x3, NULL
    // 0x9cfea8: b               #0x9cfed0
    // 0x9cfeac: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x9cfeac: ldur            w3, [x0, #0x17]
    // 0x9cfeb0: DecompressPointer r3
    //     0x9cfeb0: add             x3, x3, HEAP, lsl #32
    // 0x9cfeb4: cmp             w3, NULL
    // 0x9cfeb8: b.ne            #0x9cfec4
    // 0x9cfebc: r3 = Null
    //     0x9cfebc: mov             x3, NULL
    // 0x9cfec0: b               #0x9cfed0
    // 0x9cfec4: LoadField: r4 = r3->field_7
    //     0x9cfec4: ldur            w4, [x3, #7]
    // 0x9cfec8: DecompressPointer r4
    //     0x9cfec8: add             x4, x4, HEAP, lsl #32
    // 0x9cfecc: mov             x3, x4
    // 0x9cfed0: cmp             w3, NULL
    // 0x9cfed4: b.ne            #0x9cfee0
    // 0x9cfed8: r3 = 0
    //     0x9cfed8: movz            x3, #0
    // 0x9cfedc: b               #0x9cfef0
    // 0x9cfee0: r4 = LoadInt32Instr(r3)
    //     0x9cfee0: sbfx            x4, x3, #1, #0x1f
    //     0x9cfee4: tbz             w3, #0, #0x9cfeec
    //     0x9cfee8: ldur            x4, [x3, #7]
    // 0x9cfeec: mov             x3, x4
    // 0x9cfef0: stur            x3, [fp, #-0x18]
    // 0x9cfef4: cmp             w0, NULL
    // 0x9cfef8: b.ne            #0x9cff04
    // 0x9cfefc: r4 = Null
    //     0x9cfefc: mov             x4, NULL
    // 0x9cff00: b               #0x9cff28
    // 0x9cff04: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x9cff04: ldur            w4, [x0, #0x17]
    // 0x9cff08: DecompressPointer r4
    //     0x9cff08: add             x4, x4, HEAP, lsl #32
    // 0x9cff0c: cmp             w4, NULL
    // 0x9cff10: b.ne            #0x9cff1c
    // 0x9cff14: r4 = Null
    //     0x9cff14: mov             x4, NULL
    // 0x9cff18: b               #0x9cff28
    // 0x9cff1c: LoadField: r5 = r4->field_b
    //     0x9cff1c: ldur            w5, [x4, #0xb]
    // 0x9cff20: DecompressPointer r5
    //     0x9cff20: add             x5, x5, HEAP, lsl #32
    // 0x9cff24: mov             x4, x5
    // 0x9cff28: cmp             w4, NULL
    // 0x9cff2c: b.ne            #0x9cff38
    // 0x9cff30: r4 = 0
    //     0x9cff30: movz            x4, #0
    // 0x9cff34: b               #0x9cff48
    // 0x9cff38: r5 = LoadInt32Instr(r4)
    //     0x9cff38: sbfx            x5, x4, #1, #0x1f
    //     0x9cff3c: tbz             w4, #0, #0x9cff44
    //     0x9cff40: ldur            x5, [x4, #7]
    // 0x9cff44: mov             x4, x5
    // 0x9cff48: stur            x4, [fp, #-0x10]
    // 0x9cff4c: cmp             w0, NULL
    // 0x9cff50: b.ne            #0x9cff5c
    // 0x9cff54: r0 = Null
    //     0x9cff54: mov             x0, NULL
    // 0x9cff58: b               #0x9cff7c
    // 0x9cff5c: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x9cff5c: ldur            w5, [x0, #0x17]
    // 0x9cff60: DecompressPointer r5
    //     0x9cff60: add             x5, x5, HEAP, lsl #32
    // 0x9cff64: cmp             w5, NULL
    // 0x9cff68: b.ne            #0x9cff74
    // 0x9cff6c: r0 = Null
    //     0x9cff6c: mov             x0, NULL
    // 0x9cff70: b               #0x9cff7c
    // 0x9cff74: LoadField: r0 = r5->field_f
    //     0x9cff74: ldur            w0, [x5, #0xf]
    // 0x9cff78: DecompressPointer r0
    //     0x9cff78: add             x0, x0, HEAP, lsl #32
    // 0x9cff7c: cmp             w0, NULL
    // 0x9cff80: b.ne            #0x9cff8c
    // 0x9cff84: r0 = 0
    //     0x9cff84: movz            x0, #0
    // 0x9cff88: b               #0x9cff9c
    // 0x9cff8c: r5 = LoadInt32Instr(r0)
    //     0x9cff8c: sbfx            x5, x0, #1, #0x1f
    //     0x9cff90: tbz             w0, #0, #0x9cff98
    //     0x9cff94: ldur            x5, [x0, #7]
    // 0x9cff98: mov             x0, x5
    // 0x9cff9c: stur            x0, [fp, #-8]
    // 0x9cffa0: r0 = Color()
    //     0x9cffa0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x9cffa4: r1 = Instance_ColorSpace
    //     0x9cffa4: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x9cffa8: StoreField: r0->field_27 = r1
    //     0x9cffa8: stur            w1, [x0, #0x27]
    // 0x9cffac: d0 = 0.400000
    //     0x9cffac: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x9cffb0: StoreField: r0->field_7 = d0
    //     0x9cffb0: stur            d0, [x0, #7]
    // 0x9cffb4: ldur            x1, [fp, #-0x18]
    // 0x9cffb8: ubfx            x1, x1, #0, #0x20
    // 0x9cffbc: and             w2, w1, #0xff
    // 0x9cffc0: ubfx            x2, x2, #0, #0x20
    // 0x9cffc4: scvtf           d0, x2
    // 0x9cffc8: d1 = 255.000000
    //     0x9cffc8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x9cffcc: fdiv            d2, d0, d1
    // 0x9cffd0: StoreField: r0->field_f = d2
    //     0x9cffd0: stur            d2, [x0, #0xf]
    // 0x9cffd4: ldur            x1, [fp, #-0x10]
    // 0x9cffd8: ubfx            x1, x1, #0, #0x20
    // 0x9cffdc: and             w2, w1, #0xff
    // 0x9cffe0: ubfx            x2, x2, #0, #0x20
    // 0x9cffe4: scvtf           d0, x2
    // 0x9cffe8: fdiv            d2, d0, d1
    // 0x9cffec: ArrayStore: r0[0] = d2  ; List_8
    //     0x9cffec: stur            d2, [x0, #0x17]
    // 0x9cfff0: ldur            x1, [fp, #-8]
    // 0x9cfff4: ubfx            x1, x1, #0, #0x20
    // 0x9cfff8: and             w2, w1, #0xff
    // 0x9cfffc: ubfx            x2, x2, #0, #0x20
    // 0x9d0000: scvtf           d0, x2
    // 0x9d0004: fdiv            d2, d0, d1
    // 0x9d0008: StoreField: r0->field_1f = d2
    //     0x9d0008: stur            d2, [x0, #0x1f]
    // 0x9d000c: ldur            x1, [fp, #-0x28]
    // 0x9d0010: StoreField: r1->field_23 = r0
    //     0x9d0010: stur            w0, [x1, #0x23]
    //     0x9d0014: ldurb           w16, [x1, #-1]
    //     0x9d0018: ldurb           w17, [x0, #-1]
    //     0x9d001c: and             x16, x17, x16, lsr #2
    //     0x9d0020: tst             x16, HEAP, lsr #32
    //     0x9d0024: b.eq            #0x9d002c
    //     0x9d0028: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9d002c: r0 = Null
    //     0x9d002c: mov             x0, NULL
    // 0x9d0030: LeaveFrame
    //     0x9d0030: mov             SP, fp
    //     0x9d0034: ldp             fp, lr, [SP], #0x10
    // 0x9d0038: ret
    //     0x9d0038: ret             
  }
  _ build(/* No info */) {
    // ** addr: 0x1294f2c, size: 0x3a4
    // 0x1294f2c: EnterFrame
    //     0x1294f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x1294f30: mov             fp, SP
    // 0x1294f34: AllocStack(0x40)
    //     0x1294f34: sub             SP, SP, #0x40
    // 0x1294f38: SetupParameters(BagBottomSheet this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r0, fp-0x28 */)
    //     0x1294f38: mov             x3, x1
    //     0x1294f3c: mov             x0, x2
    //     0x1294f40: stur            x1, [fp, #-0x20]
    //     0x1294f44: stur            x2, [fp, #-0x28]
    // 0x1294f48: CheckStackOverflow
    //     0x1294f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1294f4c: cmp             SP, x16
    //     0x1294f50: b.ls            #0x12952c8
    // 0x1294f54: LoadField: r1 = r3->field_b
    //     0x1294f54: ldur            w1, [x3, #0xb]
    // 0x1294f58: DecompressPointer r1
    //     0x1294f58: add             x1, x1, HEAP, lsl #32
    // 0x1294f5c: LoadField: r2 = r1->field_b
    //     0x1294f5c: ldur            w2, [x1, #0xb]
    // 0x1294f60: DecompressPointer r2
    //     0x1294f60: add             x2, x2, HEAP, lsl #32
    // 0x1294f64: cmp             w2, NULL
    // 0x1294f68: b.ne            #0x1294f74
    // 0x1294f6c: r4 = Null
    //     0x1294f6c: mov             x4, NULL
    // 0x1294f70: b               #0x1294f80
    // 0x1294f74: LoadField: r1 = r2->field_2f
    //     0x1294f74: ldur            w1, [x2, #0x2f]
    // 0x1294f78: DecompressPointer r1
    //     0x1294f78: add             x1, x1, HEAP, lsl #32
    // 0x1294f7c: mov             x4, x1
    // 0x1294f80: stur            x4, [fp, #-0x18]
    // 0x1294f84: cmp             w2, NULL
    // 0x1294f88: b.ne            #0x1294f94
    // 0x1294f8c: r1 = Null
    //     0x1294f8c: mov             x1, NULL
    // 0x1294f90: b               #0x1294fb8
    // 0x1294f94: LoadField: r1 = r2->field_2f
    //     0x1294f94: ldur            w1, [x2, #0x2f]
    // 0x1294f98: DecompressPointer r1
    //     0x1294f98: add             x1, x1, HEAP, lsl #32
    // 0x1294f9c: cmp             w1, NULL
    // 0x1294fa0: b.ne            #0x1294fac
    // 0x1294fa4: r1 = Null
    //     0x1294fa4: mov             x1, NULL
    // 0x1294fa8: b               #0x1294fb8
    // 0x1294fac: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x1294fac: ldur            w5, [x1, #0x17]
    // 0x1294fb0: DecompressPointer r5
    //     0x1294fb0: add             x5, x5, HEAP, lsl #32
    // 0x1294fb4: mov             x1, x5
    // 0x1294fb8: cmp             w1, NULL
    // 0x1294fbc: b.ne            #0x1294fc8
    // 0x1294fc0: r5 = false
    //     0x1294fc0: add             x5, NULL, #0x30  ; false
    // 0x1294fc4: b               #0x1294fcc
    // 0x1294fc8: mov             x5, x1
    // 0x1294fcc: stur            x5, [fp, #-0x10]
    // 0x1294fd0: cmp             w2, NULL
    // 0x1294fd4: b.ne            #0x1294fe0
    // 0x1294fd8: r1 = Null
    //     0x1294fd8: mov             x1, NULL
    // 0x1294fdc: b               #0x1294ff0
    // 0x1294fe0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x1294fe0: ldur            w1, [x2, #0x17]
    // 0x1294fe4: DecompressPointer r1
    //     0x1294fe4: add             x1, x1, HEAP, lsl #32
    // 0x1294fe8: LoadField: r2 = r1->field_b
    //     0x1294fe8: ldur            w2, [x1, #0xb]
    // 0x1294fec: mov             x1, x2
    // 0x1294ff0: cmp             w1, NULL
    // 0x1294ff4: b.ne            #0x1295000
    // 0x1294ff8: r6 = 0
    //     0x1294ff8: movz            x6, #0
    // 0x1294ffc: b               #0x1295008
    // 0x1295000: r2 = LoadInt32Instr(r1)
    //     0x1295000: sbfx            x2, x1, #1, #0x1f
    // 0x1295004: mov             x6, x2
    // 0x1295008: mov             x1, x3
    // 0x129500c: mov             x2, x0
    // 0x1295010: stur            x6, [fp, #-8]
    // 0x1295014: r0 = _buildHeader()
    //     0x1295014: bl              #0x12965fc  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildHeader
    // 0x1295018: r1 = Null
    //     0x1295018: mov             x1, NULL
    // 0x129501c: r2 = 4
    //     0x129501c: movz            x2, #0x4
    // 0x1295020: stur            x0, [fp, #-0x30]
    // 0x1295024: r0 = AllocateArray()
    //     0x1295024: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1295028: mov             x2, x0
    // 0x129502c: ldur            x0, [fp, #-0x30]
    // 0x1295030: stur            x2, [fp, #-0x38]
    // 0x1295034: StoreField: r2->field_f = r0
    //     0x1295034: stur            w0, [x2, #0xf]
    // 0x1295038: r16 = Instance_SizedBox
    //     0x1295038: add             x16, PP, #0x48, lsl #12  ; [pp+0x484c8] Obj!SizedBox@d68141
    //     0x129503c: ldr             x16, [x16, #0x4c8]
    // 0x1295040: StoreField: r2->field_13 = r16
    //     0x1295040: stur            w16, [x2, #0x13]
    // 0x1295044: r1 = <Widget>
    //     0x1295044: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1295048: r0 = AllocateGrowableArray()
    //     0x1295048: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x129504c: mov             x4, x0
    // 0x1295050: ldur            x0, [fp, #-0x38]
    // 0x1295054: stur            x4, [fp, #-0x30]
    // 0x1295058: StoreField: r4->field_f = r0
    //     0x1295058: stur            w0, [x4, #0xf]
    // 0x129505c: r0 = 4
    //     0x129505c: movz            x0, #0x4
    // 0x1295060: StoreField: r4->field_b = r0
    //     0x1295060: stur            w0, [x4, #0xb]
    // 0x1295064: ldur            x0, [fp, #-0x18]
    // 0x1295068: cmp             w0, NULL
    // 0x129506c: b.eq            #0x1295104
    // 0x1295070: ldur            x1, [fp, #-0x20]
    // 0x1295074: ldur            x2, [fp, #-0x28]
    // 0x1295078: ldur            x3, [fp, #-0x10]
    // 0x129507c: r0 = _buildFreeGiftItem()
    //     0x129507c: bl              #0x1295f2c  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFreeGiftItem
    // 0x1295080: mov             x2, x0
    // 0x1295084: ldur            x0, [fp, #-0x30]
    // 0x1295088: stur            x2, [fp, #-0x10]
    // 0x129508c: LoadField: r1 = r0->field_b
    //     0x129508c: ldur            w1, [x0, #0xb]
    // 0x1295090: LoadField: r3 = r0->field_f
    //     0x1295090: ldur            w3, [x0, #0xf]
    // 0x1295094: DecompressPointer r3
    //     0x1295094: add             x3, x3, HEAP, lsl #32
    // 0x1295098: LoadField: r4 = r3->field_b
    //     0x1295098: ldur            w4, [x3, #0xb]
    // 0x129509c: r3 = LoadInt32Instr(r1)
    //     0x129509c: sbfx            x3, x1, #1, #0x1f
    // 0x12950a0: stur            x3, [fp, #-0x40]
    // 0x12950a4: r1 = LoadInt32Instr(r4)
    //     0x12950a4: sbfx            x1, x4, #1, #0x1f
    // 0x12950a8: cmp             x3, x1
    // 0x12950ac: b.ne            #0x12950b8
    // 0x12950b0: mov             x1, x0
    // 0x12950b4: r0 = _growToNextCapacity()
    //     0x12950b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12950b8: ldur            x3, [fp, #-0x30]
    // 0x12950bc: ldur            x2, [fp, #-0x40]
    // 0x12950c0: add             x0, x2, #1
    // 0x12950c4: lsl             x1, x0, #1
    // 0x12950c8: StoreField: r3->field_b = r1
    //     0x12950c8: stur            w1, [x3, #0xb]
    // 0x12950cc: LoadField: r1 = r3->field_f
    //     0x12950cc: ldur            w1, [x3, #0xf]
    // 0x12950d0: DecompressPointer r1
    //     0x12950d0: add             x1, x1, HEAP, lsl #32
    // 0x12950d4: ldur            x0, [fp, #-0x10]
    // 0x12950d8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x12950d8: add             x25, x1, x2, lsl #2
    //     0x12950dc: add             x25, x25, #0xf
    //     0x12950e0: str             w0, [x25]
    //     0x12950e4: tbz             w0, #0, #0x1295100
    //     0x12950e8: ldurb           w16, [x1, #-1]
    //     0x12950ec: ldurb           w17, [x0, #-1]
    //     0x12950f0: and             x16, x17, x16, lsr #2
    //     0x12950f4: tst             x16, HEAP, lsr #32
    //     0x12950f8: b.eq            #0x1295100
    //     0x12950fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1295100: b               #0x1295108
    // 0x1295104: mov             x3, x4
    // 0x1295108: ldur            x1, [fp, #-0x20]
    // 0x129510c: ldur            x2, [fp, #-8]
    // 0x1295110: r0 = _buildItemsList()
    //     0x1295110: bl              #0x901fb4  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList
    // 0x1295114: mov             x2, x0
    // 0x1295118: ldur            x0, [fp, #-0x30]
    // 0x129511c: stur            x2, [fp, #-0x10]
    // 0x1295120: LoadField: r1 = r0->field_b
    //     0x1295120: ldur            w1, [x0, #0xb]
    // 0x1295124: LoadField: r3 = r0->field_f
    //     0x1295124: ldur            w3, [x0, #0xf]
    // 0x1295128: DecompressPointer r3
    //     0x1295128: add             x3, x3, HEAP, lsl #32
    // 0x129512c: LoadField: r4 = r3->field_b
    //     0x129512c: ldur            w4, [x3, #0xb]
    // 0x1295130: r3 = LoadInt32Instr(r1)
    //     0x1295130: sbfx            x3, x1, #1, #0x1f
    // 0x1295134: stur            x3, [fp, #-8]
    // 0x1295138: r1 = LoadInt32Instr(r4)
    //     0x1295138: sbfx            x1, x4, #1, #0x1f
    // 0x129513c: cmp             x3, x1
    // 0x1295140: b.ne            #0x129514c
    // 0x1295144: mov             x1, x0
    // 0x1295148: r0 = _growToNextCapacity()
    //     0x1295148: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x129514c: ldur            x2, [fp, #-0x30]
    // 0x1295150: ldur            x3, [fp, #-8]
    // 0x1295154: add             x4, x3, #1
    // 0x1295158: stur            x4, [fp, #-0x40]
    // 0x129515c: lsl             x0, x4, #1
    // 0x1295160: StoreField: r2->field_b = r0
    //     0x1295160: stur            w0, [x2, #0xb]
    // 0x1295164: LoadField: r5 = r2->field_f
    //     0x1295164: ldur            w5, [x2, #0xf]
    // 0x1295168: DecompressPointer r5
    //     0x1295168: add             x5, x5, HEAP, lsl #32
    // 0x129516c: mov             x1, x5
    // 0x1295170: ldur            x0, [fp, #-0x10]
    // 0x1295174: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1295174: add             x25, x1, x3, lsl #2
    //     0x1295178: add             x25, x25, #0xf
    //     0x129517c: str             w0, [x25]
    //     0x1295180: tbz             w0, #0, #0x129519c
    //     0x1295184: ldurb           w16, [x1, #-1]
    //     0x1295188: ldurb           w17, [x0, #-1]
    //     0x129518c: and             x16, x17, x16, lsr #2
    //     0x1295190: tst             x16, HEAP, lsr #32
    //     0x1295194: b.eq            #0x129519c
    //     0x1295198: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x129519c: LoadField: r0 = r5->field_b
    //     0x129519c: ldur            w0, [x5, #0xb]
    // 0x12951a0: r1 = LoadInt32Instr(r0)
    //     0x12951a0: sbfx            x1, x0, #1, #0x1f
    // 0x12951a4: cmp             x4, x1
    // 0x12951a8: b.ne            #0x12951b4
    // 0x12951ac: mov             x1, x2
    // 0x12951b0: r0 = _growToNextCapacity()
    //     0x12951b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12951b4: ldur            x1, [fp, #-0x40]
    // 0x12951b8: ldur            x0, [fp, #-0x30]
    // 0x12951bc: add             x2, x1, #1
    // 0x12951c0: lsl             x3, x2, #1
    // 0x12951c4: StoreField: r0->field_b = r3
    //     0x12951c4: stur            w3, [x0, #0xb]
    // 0x12951c8: LoadField: r2 = r0->field_f
    //     0x12951c8: ldur            w2, [x0, #0xf]
    // 0x12951cc: DecompressPointer r2
    //     0x12951cc: add             x2, x2, HEAP, lsl #32
    // 0x12951d0: add             x3, x2, x1, lsl #2
    // 0x12951d4: r16 = Instance_Divider
    //     0x12951d4: add             x16, PP, #0x48, lsl #12  ; [pp+0x484d0] Obj!Divider@d66ca1
    //     0x12951d8: ldr             x16, [x16, #0x4d0]
    // 0x12951dc: StoreField: r3->field_f = r16
    //     0x12951dc: stur            w16, [x3, #0xf]
    // 0x12951e0: ldur            x1, [fp, #-0x20]
    // 0x12951e4: r0 = _buildFooter()
    //     0x12951e4: bl              #0x12952d0  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFooter
    // 0x12951e8: mov             x2, x0
    // 0x12951ec: ldur            x0, [fp, #-0x30]
    // 0x12951f0: stur            x2, [fp, #-0x10]
    // 0x12951f4: LoadField: r1 = r0->field_b
    //     0x12951f4: ldur            w1, [x0, #0xb]
    // 0x12951f8: LoadField: r3 = r0->field_f
    //     0x12951f8: ldur            w3, [x0, #0xf]
    // 0x12951fc: DecompressPointer r3
    //     0x12951fc: add             x3, x3, HEAP, lsl #32
    // 0x1295200: LoadField: r4 = r3->field_b
    //     0x1295200: ldur            w4, [x3, #0xb]
    // 0x1295204: r3 = LoadInt32Instr(r1)
    //     0x1295204: sbfx            x3, x1, #1, #0x1f
    // 0x1295208: stur            x3, [fp, #-8]
    // 0x129520c: r1 = LoadInt32Instr(r4)
    //     0x129520c: sbfx            x1, x4, #1, #0x1f
    // 0x1295210: cmp             x3, x1
    // 0x1295214: b.ne            #0x1295220
    // 0x1295218: mov             x1, x0
    // 0x129521c: r0 = _growToNextCapacity()
    //     0x129521c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1295220: ldur            x2, [fp, #-0x30]
    // 0x1295224: ldur            x3, [fp, #-8]
    // 0x1295228: add             x0, x3, #1
    // 0x129522c: lsl             x1, x0, #1
    // 0x1295230: StoreField: r2->field_b = r1
    //     0x1295230: stur            w1, [x2, #0xb]
    // 0x1295234: LoadField: r1 = r2->field_f
    //     0x1295234: ldur            w1, [x2, #0xf]
    // 0x1295238: DecompressPointer r1
    //     0x1295238: add             x1, x1, HEAP, lsl #32
    // 0x129523c: ldur            x0, [fp, #-0x10]
    // 0x1295240: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1295240: add             x25, x1, x3, lsl #2
    //     0x1295244: add             x25, x25, #0xf
    //     0x1295248: str             w0, [x25]
    //     0x129524c: tbz             w0, #0, #0x1295268
    //     0x1295250: ldurb           w16, [x1, #-1]
    //     0x1295254: ldurb           w17, [x0, #-1]
    //     0x1295258: and             x16, x17, x16, lsr #2
    //     0x129525c: tst             x16, HEAP, lsr #32
    //     0x1295260: b.eq            #0x1295268
    //     0x1295264: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1295268: r0 = Column()
    //     0x1295268: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x129526c: r1 = Instance_Axis
    //     0x129526c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1295270: StoreField: r0->field_f = r1
    //     0x1295270: stur            w1, [x0, #0xf]
    // 0x1295274: r1 = Instance_MainAxisAlignment
    //     0x1295274: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1295278: ldr             x1, [x1, #0xa08]
    // 0x129527c: StoreField: r0->field_13 = r1
    //     0x129527c: stur            w1, [x0, #0x13]
    // 0x1295280: r1 = Instance_MainAxisSize
    //     0x1295280: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1295284: ldr             x1, [x1, #0xdd0]
    // 0x1295288: ArrayStore: r0[0] = r1  ; List_4
    //     0x1295288: stur            w1, [x0, #0x17]
    // 0x129528c: r1 = Instance_CrossAxisAlignment
    //     0x129528c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1295290: ldr             x1, [x1, #0xa18]
    // 0x1295294: StoreField: r0->field_1b = r1
    //     0x1295294: stur            w1, [x0, #0x1b]
    // 0x1295298: r1 = Instance_VerticalDirection
    //     0x1295298: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x129529c: ldr             x1, [x1, #0xa20]
    // 0x12952a0: StoreField: r0->field_23 = r1
    //     0x12952a0: stur            w1, [x0, #0x23]
    // 0x12952a4: r1 = Instance_Clip
    //     0x12952a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12952a8: ldr             x1, [x1, #0x38]
    // 0x12952ac: StoreField: r0->field_2b = r1
    //     0x12952ac: stur            w1, [x0, #0x2b]
    // 0x12952b0: StoreField: r0->field_2f = rZR
    //     0x12952b0: stur            xzr, [x0, #0x2f]
    // 0x12952b4: ldur            x1, [fp, #-0x30]
    // 0x12952b8: StoreField: r0->field_b = r1
    //     0x12952b8: stur            w1, [x0, #0xb]
    // 0x12952bc: LeaveFrame
    //     0x12952bc: mov             SP, fp
    //     0x12952c0: ldp             fp, lr, [SP], #0x10
    // 0x12952c4: ret
    //     0x12952c4: ret             
    // 0x12952c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12952c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12952cc: b               #0x1294f54
  }
  _ _buildFooter(/* No info */) {
    // ** addr: 0x12952d0, size: 0x4c8
    // 0x12952d0: EnterFrame
    //     0x12952d0: stp             fp, lr, [SP, #-0x10]!
    //     0x12952d4: mov             fp, SP
    // 0x12952d8: AllocStack(0x58)
    //     0x12952d8: sub             SP, SP, #0x58
    // 0x12952dc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x10 */)
    //     0x12952dc: stur            x1, [fp, #-0x10]
    // 0x12952e0: CheckStackOverflow
    //     0x12952e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12952e4: cmp             SP, x16
    //     0x12952e8: b.ls            #0x1295778
    // 0x12952ec: LoadField: r0 = r1->field_1b
    //     0x12952ec: ldur            w0, [x1, #0x1b]
    // 0x12952f0: DecompressPointer r0
    //     0x12952f0: add             x0, x0, HEAP, lsl #32
    // 0x12952f4: stur            x0, [fp, #-8]
    // 0x12952f8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x12952f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12952fc: ldr             x0, [x0, #0x1c80]
    //     0x1295300: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1295304: cmp             w0, w16
    //     0x1295308: b.ne            #0x1295314
    //     0x129530c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1295310: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1295314: r0 = GetNavigation.size()
    //     0x1295314: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1295318: LoadField: d0 = r0->field_f
    //     0x1295318: ldur            d0, [x0, #0xf]
    // 0x129531c: d1 = 0.135000
    //     0x129531c: add             x17, PP, #0x48, lsl #12  ; [pp+0x484d8] IMM: double(0.135) from 0x3fc147ae147ae148
    //     0x1295320: ldr             d1, [x17, #0x4d8]
    // 0x1295324: fmul            d2, d0, d1
    // 0x1295328: stur            d2, [fp, #-0x48]
    // 0x129532c: r1 = Null
    //     0x129532c: mov             x1, NULL
    // 0x1295330: r2 = 6
    //     0x1295330: movz            x2, #0x6
    // 0x1295334: r0 = AllocateArray()
    //     0x1295334: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1295338: r16 = "Subtotal ("
    //     0x1295338: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e0] "Subtotal ("
    //     0x129533c: ldr             x16, [x16, #0x4e0]
    // 0x1295340: StoreField: r0->field_f = r16
    //     0x1295340: stur            w16, [x0, #0xf]
    // 0x1295344: ldur            x1, [fp, #-0x10]
    // 0x1295348: LoadField: r2 = r1->field_b
    //     0x1295348: ldur            w2, [x1, #0xb]
    // 0x129534c: DecompressPointer r2
    //     0x129534c: add             x2, x2, HEAP, lsl #32
    // 0x1295350: stur            x2, [fp, #-0x18]
    // 0x1295354: LoadField: r3 = r2->field_b
    //     0x1295354: ldur            w3, [x2, #0xb]
    // 0x1295358: DecompressPointer r3
    //     0x1295358: add             x3, x3, HEAP, lsl #32
    // 0x129535c: cmp             w3, NULL
    // 0x1295360: b.ne            #0x129536c
    // 0x1295364: r3 = Null
    //     0x1295364: mov             x3, NULL
    // 0x1295368: b               #0x1295378
    // 0x129536c: LoadField: r4 = r3->field_b
    //     0x129536c: ldur            w4, [x3, #0xb]
    // 0x1295370: DecompressPointer r4
    //     0x1295370: add             x4, x4, HEAP, lsl #32
    // 0x1295374: mov             x3, x4
    // 0x1295378: StoreField: r0->field_13 = r3
    //     0x1295378: stur            w3, [x0, #0x13]
    // 0x129537c: r16 = " item)"
    //     0x129537c: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0x1295380: ldr             x16, [x16, #0x4e8]
    // 0x1295384: ArrayStore: r0[0] = r16  ; List_4
    //     0x1295384: stur            w16, [x0, #0x17]
    // 0x1295388: str             x0, [SP]
    // 0x129538c: r0 = _interpolate()
    //     0x129538c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1295390: stur            x0, [fp, #-0x20]
    // 0x1295394: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1295394: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1295398: ldr             x0, [x0, #0x1ab0]
    //     0x129539c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12953a0: cmp             w0, w16
    //     0x12953a4: b.ne            #0x12953b4
    //     0x12953a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x12953ac: ldr             x2, [x2, #0x60]
    //     0x12953b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12953b4: LoadField: r2 = r0->field_87
    //     0x12953b4: ldur            w2, [x0, #0x87]
    // 0x12953b8: DecompressPointer r2
    //     0x12953b8: add             x2, x2, HEAP, lsl #32
    // 0x12953bc: stur            x2, [fp, #-0x30]
    // 0x12953c0: LoadField: r0 = r2->field_2b
    //     0x12953c0: ldur            w0, [x2, #0x2b]
    // 0x12953c4: DecompressPointer r0
    //     0x12953c4: add             x0, x0, HEAP, lsl #32
    // 0x12953c8: stur            x0, [fp, #-0x28]
    // 0x12953cc: r1 = Instance_Color
    //     0x12953cc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12953d0: d0 = 0.700000
    //     0x12953d0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x12953d4: ldr             d0, [x17, #0xf48]
    // 0x12953d8: r0 = withOpacity()
    //     0x12953d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12953dc: r16 = 16.000000
    //     0x12953dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x12953e0: ldr             x16, [x16, #0x188]
    // 0x12953e4: stp             x0, x16, [SP]
    // 0x12953e8: ldur            x1, [fp, #-0x28]
    // 0x12953ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12953ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12953f0: ldr             x4, [x4, #0xaa0]
    // 0x12953f4: r0 = copyWith()
    //     0x12953f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12953f8: stur            x0, [fp, #-0x28]
    // 0x12953fc: r0 = Text()
    //     0x12953fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1295400: mov             x2, x0
    // 0x1295404: ldur            x0, [fp, #-0x20]
    // 0x1295408: stur            x2, [fp, #-0x38]
    // 0x129540c: StoreField: r2->field_b = r0
    //     0x129540c: stur            w0, [x2, #0xb]
    // 0x1295410: ldur            x0, [fp, #-0x28]
    // 0x1295414: StoreField: r2->field_13 = r0
    //     0x1295414: stur            w0, [x2, #0x13]
    // 0x1295418: ldur            x0, [fp, #-0x18]
    // 0x129541c: LoadField: r1 = r0->field_b
    //     0x129541c: ldur            w1, [x0, #0xb]
    // 0x1295420: DecompressPointer r1
    //     0x1295420: add             x1, x1, HEAP, lsl #32
    // 0x1295424: cmp             w1, NULL
    // 0x1295428: b.ne            #0x1295434
    // 0x129542c: r0 = Null
    //     0x129542c: mov             x0, NULL
    // 0x1295430: b               #0x129543c
    // 0x1295434: LoadField: r0 = r1->field_13
    //     0x1295434: ldur            w0, [x1, #0x13]
    // 0x1295438: DecompressPointer r0
    //     0x1295438: add             x0, x0, HEAP, lsl #32
    // 0x129543c: cmp             w0, NULL
    // 0x1295440: b.ne            #0x129544c
    // 0x1295444: r4 = ""
    //     0x1295444: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1295448: b               #0x1295450
    // 0x129544c: mov             x4, x0
    // 0x1295450: ldur            x3, [fp, #-8]
    // 0x1295454: ldur            x0, [fp, #-0x30]
    // 0x1295458: stur            x4, [fp, #-0x18]
    // 0x129545c: LoadField: r1 = r0->field_7
    //     0x129545c: ldur            w1, [x0, #7]
    // 0x1295460: DecompressPointer r1
    //     0x1295460: add             x1, x1, HEAP, lsl #32
    // 0x1295464: r16 = 16.000000
    //     0x1295464: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1295468: ldr             x16, [x16, #0x188]
    // 0x129546c: r30 = Instance_Color
    //     0x129546c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1295470: stp             lr, x16, [SP]
    // 0x1295474: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1295474: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1295478: ldr             x4, [x4, #0xaa0]
    // 0x129547c: r0 = copyWith()
    //     0x129547c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1295480: stur            x0, [fp, #-0x20]
    // 0x1295484: r0 = Text()
    //     0x1295484: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1295488: mov             x3, x0
    // 0x129548c: ldur            x0, [fp, #-0x18]
    // 0x1295490: stur            x3, [fp, #-0x28]
    // 0x1295494: StoreField: r3->field_b = r0
    //     0x1295494: stur            w0, [x3, #0xb]
    // 0x1295498: ldur            x0, [fp, #-0x20]
    // 0x129549c: StoreField: r3->field_13 = r0
    //     0x129549c: stur            w0, [x3, #0x13]
    // 0x12954a0: r1 = Null
    //     0x12954a0: mov             x1, NULL
    // 0x12954a4: r2 = 6
    //     0x12954a4: movz            x2, #0x6
    // 0x12954a8: r0 = AllocateArray()
    //     0x12954a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12954ac: mov             x2, x0
    // 0x12954b0: ldur            x0, [fp, #-0x38]
    // 0x12954b4: stur            x2, [fp, #-0x18]
    // 0x12954b8: StoreField: r2->field_f = r0
    //     0x12954b8: stur            w0, [x2, #0xf]
    // 0x12954bc: r16 = Instance_Spacer
    //     0x12954bc: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x12954c0: ldr             x16, [x16, #0xf0]
    // 0x12954c4: StoreField: r2->field_13 = r16
    //     0x12954c4: stur            w16, [x2, #0x13]
    // 0x12954c8: ldur            x0, [fp, #-0x28]
    // 0x12954cc: ArrayStore: r2[0] = r0  ; List_4
    //     0x12954cc: stur            w0, [x2, #0x17]
    // 0x12954d0: r1 = <Widget>
    //     0x12954d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12954d4: r0 = AllocateGrowableArray()
    //     0x12954d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12954d8: mov             x1, x0
    // 0x12954dc: ldur            x0, [fp, #-0x18]
    // 0x12954e0: stur            x1, [fp, #-0x20]
    // 0x12954e4: StoreField: r1->field_f = r0
    //     0x12954e4: stur            w0, [x1, #0xf]
    // 0x12954e8: r0 = 6
    //     0x12954e8: movz            x0, #0x6
    // 0x12954ec: StoreField: r1->field_b = r0
    //     0x12954ec: stur            w0, [x1, #0xb]
    // 0x12954f0: r0 = Row()
    //     0x12954f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12954f4: mov             x3, x0
    // 0x12954f8: r0 = Instance_Axis
    //     0x12954f8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12954fc: stur            x3, [fp, #-0x18]
    // 0x1295500: StoreField: r3->field_f = r0
    //     0x1295500: stur            w0, [x3, #0xf]
    // 0x1295504: r0 = Instance_MainAxisAlignment
    //     0x1295504: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1295508: ldr             x0, [x0, #0xa08]
    // 0x129550c: StoreField: r3->field_13 = r0
    //     0x129550c: stur            w0, [x3, #0x13]
    // 0x1295510: r4 = Instance_MainAxisSize
    //     0x1295510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1295514: ldr             x4, [x4, #0xa10]
    // 0x1295518: ArrayStore: r3[0] = r4  ; List_4
    //     0x1295518: stur            w4, [x3, #0x17]
    // 0x129551c: r1 = Instance_CrossAxisAlignment
    //     0x129551c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1295520: ldr             x1, [x1, #0xa18]
    // 0x1295524: StoreField: r3->field_1b = r1
    //     0x1295524: stur            w1, [x3, #0x1b]
    // 0x1295528: r5 = Instance_VerticalDirection
    //     0x1295528: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x129552c: ldr             x5, [x5, #0xa20]
    // 0x1295530: StoreField: r3->field_23 = r5
    //     0x1295530: stur            w5, [x3, #0x23]
    // 0x1295534: r6 = Instance_Clip
    //     0x1295534: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1295538: ldr             x6, [x6, #0x38]
    // 0x129553c: StoreField: r3->field_2b = r6
    //     0x129553c: stur            w6, [x3, #0x2b]
    // 0x1295540: StoreField: r3->field_2f = rZR
    //     0x1295540: stur            xzr, [x3, #0x2f]
    // 0x1295544: ldur            x1, [fp, #-0x20]
    // 0x1295548: StoreField: r3->field_b = r1
    //     0x1295548: stur            w1, [x3, #0xb]
    // 0x129554c: r1 = Null
    //     0x129554c: mov             x1, NULL
    // 0x1295550: r2 = 2
    //     0x1295550: movz            x2, #0x2
    // 0x1295554: r0 = AllocateArray()
    //     0x1295554: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1295558: mov             x2, x0
    // 0x129555c: ldur            x0, [fp, #-0x18]
    // 0x1295560: stur            x2, [fp, #-0x20]
    // 0x1295564: StoreField: r2->field_f = r0
    //     0x1295564: stur            w0, [x2, #0xf]
    // 0x1295568: r1 = <Widget>
    //     0x1295568: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129556c: r0 = AllocateGrowableArray()
    //     0x129556c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1295570: mov             x2, x0
    // 0x1295574: ldur            x0, [fp, #-0x20]
    // 0x1295578: stur            x2, [fp, #-0x18]
    // 0x129557c: StoreField: r2->field_f = r0
    //     0x129557c: stur            w0, [x2, #0xf]
    // 0x1295580: r0 = 2
    //     0x1295580: movz            x0, #0x2
    // 0x1295584: StoreField: r2->field_b = r0
    //     0x1295584: stur            w0, [x2, #0xb]
    // 0x1295588: ldur            x0, [fp, #-8]
    // 0x129558c: cmp             w0, NULL
    // 0x1295590: b.eq            #0x1295620
    // 0x1295594: ldur            x1, [fp, #-0x10]
    // 0x1295598: r0 = _buildSingleButton()
    //     0x1295598: bl              #0x1295cb0  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton
    // 0x129559c: mov             x2, x0
    // 0x12955a0: ldur            x0, [fp, #-0x18]
    // 0x12955a4: stur            x2, [fp, #-8]
    // 0x12955a8: LoadField: r1 = r0->field_b
    //     0x12955a8: ldur            w1, [x0, #0xb]
    // 0x12955ac: LoadField: r3 = r0->field_f
    //     0x12955ac: ldur            w3, [x0, #0xf]
    // 0x12955b0: DecompressPointer r3
    //     0x12955b0: add             x3, x3, HEAP, lsl #32
    // 0x12955b4: LoadField: r4 = r3->field_b
    //     0x12955b4: ldur            w4, [x3, #0xb]
    // 0x12955b8: r3 = LoadInt32Instr(r1)
    //     0x12955b8: sbfx            x3, x1, #1, #0x1f
    // 0x12955bc: stur            x3, [fp, #-0x40]
    // 0x12955c0: r1 = LoadInt32Instr(r4)
    //     0x12955c0: sbfx            x1, x4, #1, #0x1f
    // 0x12955c4: cmp             x3, x1
    // 0x12955c8: b.ne            #0x12955d4
    // 0x12955cc: mov             x1, x0
    // 0x12955d0: r0 = _growToNextCapacity()
    //     0x12955d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12955d4: ldur            x2, [fp, #-0x18]
    // 0x12955d8: ldur            x3, [fp, #-0x40]
    // 0x12955dc: add             x0, x3, #1
    // 0x12955e0: lsl             x1, x0, #1
    // 0x12955e4: StoreField: r2->field_b = r1
    //     0x12955e4: stur            w1, [x2, #0xb]
    // 0x12955e8: LoadField: r1 = r2->field_f
    //     0x12955e8: ldur            w1, [x2, #0xf]
    // 0x12955ec: DecompressPointer r1
    //     0x12955ec: add             x1, x1, HEAP, lsl #32
    // 0x12955f0: ldur            x0, [fp, #-8]
    // 0x12955f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x12955f4: add             x25, x1, x3, lsl #2
    //     0x12955f8: add             x25, x25, #0xf
    //     0x12955fc: str             w0, [x25]
    //     0x1295600: tbz             w0, #0, #0x129561c
    //     0x1295604: ldurb           w16, [x1, #-1]
    //     0x1295608: ldurb           w17, [x0, #-1]
    //     0x129560c: and             x16, x17, x16, lsr #2
    //     0x1295610: tst             x16, HEAP, lsr #32
    //     0x1295614: b.eq            #0x129561c
    //     0x1295618: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x129561c: b               #0x12956a8
    // 0x1295620: ldur            x1, [fp, #-0x10]
    // 0x1295624: r0 = _buildDualButtons()
    //     0x1295624: bl              #0x1295798  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons
    // 0x1295628: mov             x2, x0
    // 0x129562c: ldur            x0, [fp, #-0x18]
    // 0x1295630: stur            x2, [fp, #-8]
    // 0x1295634: LoadField: r1 = r0->field_b
    //     0x1295634: ldur            w1, [x0, #0xb]
    // 0x1295638: LoadField: r3 = r0->field_f
    //     0x1295638: ldur            w3, [x0, #0xf]
    // 0x129563c: DecompressPointer r3
    //     0x129563c: add             x3, x3, HEAP, lsl #32
    // 0x1295640: LoadField: r4 = r3->field_b
    //     0x1295640: ldur            w4, [x3, #0xb]
    // 0x1295644: r3 = LoadInt32Instr(r1)
    //     0x1295644: sbfx            x3, x1, #1, #0x1f
    // 0x1295648: stur            x3, [fp, #-0x40]
    // 0x129564c: r1 = LoadInt32Instr(r4)
    //     0x129564c: sbfx            x1, x4, #1, #0x1f
    // 0x1295650: cmp             x3, x1
    // 0x1295654: b.ne            #0x1295660
    // 0x1295658: mov             x1, x0
    // 0x129565c: r0 = _growToNextCapacity()
    //     0x129565c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1295660: ldur            x2, [fp, #-0x18]
    // 0x1295664: ldur            x3, [fp, #-0x40]
    // 0x1295668: add             x0, x3, #1
    // 0x129566c: lsl             x1, x0, #1
    // 0x1295670: StoreField: r2->field_b = r1
    //     0x1295670: stur            w1, [x2, #0xb]
    // 0x1295674: LoadField: r1 = r2->field_f
    //     0x1295674: ldur            w1, [x2, #0xf]
    // 0x1295678: DecompressPointer r1
    //     0x1295678: add             x1, x1, HEAP, lsl #32
    // 0x129567c: ldur            x0, [fp, #-8]
    // 0x1295680: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1295680: add             x25, x1, x3, lsl #2
    //     0x1295684: add             x25, x25, #0xf
    //     0x1295688: str             w0, [x25]
    //     0x129568c: tbz             w0, #0, #0x12956a8
    //     0x1295690: ldurb           w16, [x1, #-1]
    //     0x1295694: ldurb           w17, [x0, #-1]
    //     0x1295698: and             x16, x17, x16, lsr #2
    //     0x129569c: tst             x16, HEAP, lsr #32
    //     0x12956a0: b.eq            #0x12956a8
    //     0x12956a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12956a8: ldur            d0, [fp, #-0x48]
    // 0x12956ac: r0 = Column()
    //     0x12956ac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12956b0: mov             x1, x0
    // 0x12956b4: r0 = Instance_Axis
    //     0x12956b4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12956b8: stur            x1, [fp, #-8]
    // 0x12956bc: StoreField: r1->field_f = r0
    //     0x12956bc: stur            w0, [x1, #0xf]
    // 0x12956c0: r0 = Instance_MainAxisAlignment
    //     0x12956c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12956c4: ldr             x0, [x0, #0xa08]
    // 0x12956c8: StoreField: r1->field_13 = r0
    //     0x12956c8: stur            w0, [x1, #0x13]
    // 0x12956cc: r0 = Instance_MainAxisSize
    //     0x12956cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12956d0: ldr             x0, [x0, #0xa10]
    // 0x12956d4: ArrayStore: r1[0] = r0  ; List_4
    //     0x12956d4: stur            w0, [x1, #0x17]
    // 0x12956d8: r0 = Instance_CrossAxisAlignment
    //     0x12956d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x12956dc: ldr             x0, [x0, #0x890]
    // 0x12956e0: StoreField: r1->field_1b = r0
    //     0x12956e0: stur            w0, [x1, #0x1b]
    // 0x12956e4: r0 = Instance_VerticalDirection
    //     0x12956e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12956e8: ldr             x0, [x0, #0xa20]
    // 0x12956ec: StoreField: r1->field_23 = r0
    //     0x12956ec: stur            w0, [x1, #0x23]
    // 0x12956f0: r0 = Instance_Clip
    //     0x12956f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12956f4: ldr             x0, [x0, #0x38]
    // 0x12956f8: StoreField: r1->field_2b = r0
    //     0x12956f8: stur            w0, [x1, #0x2b]
    // 0x12956fc: StoreField: r1->field_2f = rZR
    //     0x12956fc: stur            xzr, [x1, #0x2f]
    // 0x1295700: ldur            x0, [fp, #-0x18]
    // 0x1295704: StoreField: r1->field_b = r0
    //     0x1295704: stur            w0, [x1, #0xb]
    // 0x1295708: r0 = Padding()
    //     0x1295708: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x129570c: mov             x1, x0
    // 0x1295710: r0 = Instance_EdgeInsets
    //     0x1295710: add             x0, PP, #0x48, lsl #12  ; [pp+0x484f0] Obj!EdgeInsets@d598a1
    //     0x1295714: ldr             x0, [x0, #0x4f0]
    // 0x1295718: stur            x1, [fp, #-0x10]
    // 0x129571c: StoreField: r1->field_f = r0
    //     0x129571c: stur            w0, [x1, #0xf]
    // 0x1295720: ldur            x0, [fp, #-8]
    // 0x1295724: StoreField: r1->field_b = r0
    //     0x1295724: stur            w0, [x1, #0xb]
    // 0x1295728: ldur            d0, [fp, #-0x48]
    // 0x129572c: r0 = inline_Allocate_Double()
    //     0x129572c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1295730: add             x0, x0, #0x10
    //     0x1295734: cmp             x2, x0
    //     0x1295738: b.ls            #0x1295780
    //     0x129573c: str             x0, [THR, #0x50]  ; THR::top
    //     0x1295740: sub             x0, x0, #0xf
    //     0x1295744: movz            x2, #0xe15c
    //     0x1295748: movk            x2, #0x3, lsl #16
    //     0x129574c: stur            x2, [x0, #-1]
    // 0x1295750: StoreField: r0->field_7 = d0
    //     0x1295750: stur            d0, [x0, #7]
    // 0x1295754: stur            x0, [fp, #-8]
    // 0x1295758: r0 = SizedBox()
    //     0x1295758: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x129575c: ldur            x1, [fp, #-8]
    // 0x1295760: StoreField: r0->field_13 = r1
    //     0x1295760: stur            w1, [x0, #0x13]
    // 0x1295764: ldur            x1, [fp, #-0x10]
    // 0x1295768: StoreField: r0->field_b = r1
    //     0x1295768: stur            w1, [x0, #0xb]
    // 0x129576c: LeaveFrame
    //     0x129576c: mov             SP, fp
    //     0x1295770: ldp             fp, lr, [SP], #0x10
    // 0x1295774: ret
    //     0x1295774: ret             
    // 0x1295778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295778: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129577c: b               #0x12952ec
    // 0x1295780: SaveReg d0
    //     0x1295780: str             q0, [SP, #-0x10]!
    // 0x1295784: SaveReg r1
    //     0x1295784: str             x1, [SP, #-8]!
    // 0x1295788: r0 = AllocateDouble()
    //     0x1295788: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129578c: RestoreReg r1
    //     0x129578c: ldr             x1, [SP], #8
    // 0x1295790: RestoreReg d0
    //     0x1295790: ldr             q0, [SP], #0x10
    // 0x1295794: b               #0x1295750
  }
  _ _buildDualButtons(/* No info */) {
    // ** addr: 0x1295798, size: 0x450
    // 0x1295798: EnterFrame
    //     0x1295798: stp             fp, lr, [SP, #-0x10]!
    //     0x129579c: mov             fp, SP
    // 0x12957a0: AllocStack(0x48)
    //     0x12957a0: sub             SP, SP, #0x48
    // 0x12957a4: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x12957a4: stur            x1, [fp, #-8]
    // 0x12957a8: CheckStackOverflow
    //     0x12957a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12957ac: cmp             SP, x16
    //     0x12957b0: b.ls            #0x1295be0
    // 0x12957b4: r1 = 1
    //     0x12957b4: movz            x1, #0x1
    // 0x12957b8: r0 = AllocateContext()
    //     0x12957b8: bl              #0x16f6108  ; AllocateContextStub
    // 0x12957bc: mov             x1, x0
    // 0x12957c0: ldur            x0, [fp, #-8]
    // 0x12957c4: stur            x1, [fp, #-0x10]
    // 0x12957c8: StoreField: r1->field_f = r0
    //     0x12957c8: stur            w0, [x1, #0xf]
    // 0x12957cc: r16 = <EdgeInsets>
    //     0x12957cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x12957d0: ldr             x16, [x16, #0xda0]
    // 0x12957d4: r30 = Instance_EdgeInsets
    //     0x12957d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x12957d8: ldr             lr, [lr, #0x1f0]
    // 0x12957dc: stp             lr, x16, [SP]
    // 0x12957e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12957e0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12957e4: r0 = all()
    //     0x12957e4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12957e8: mov             x1, x0
    // 0x12957ec: ldur            x0, [fp, #-8]
    // 0x12957f0: stur            x1, [fp, #-0x20]
    // 0x12957f4: LoadField: r2 = r0->field_1f
    //     0x12957f4: ldur            w2, [x0, #0x1f]
    // 0x12957f8: DecompressPointer r2
    //     0x12957f8: add             x2, x2, HEAP, lsl #32
    // 0x12957fc: stur            x2, [fp, #-0x18]
    // 0x1295800: r0 = BorderSide()
    //     0x1295800: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1295804: mov             x1, x0
    // 0x1295808: ldur            x0, [fp, #-0x18]
    // 0x129580c: stur            x1, [fp, #-8]
    // 0x1295810: StoreField: r1->field_7 = r0
    //     0x1295810: stur            w0, [x1, #7]
    // 0x1295814: d0 = 1.000000
    //     0x1295814: fmov            d0, #1.00000000
    // 0x1295818: StoreField: r1->field_b = d0
    //     0x1295818: stur            d0, [x1, #0xb]
    // 0x129581c: r2 = Instance_BorderStyle
    //     0x129581c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1295820: ldr             x2, [x2, #0xf68]
    // 0x1295824: StoreField: r1->field_13 = r2
    //     0x1295824: stur            w2, [x1, #0x13]
    // 0x1295828: d0 = -1.000000
    //     0x1295828: fmov            d0, #-1.00000000
    // 0x129582c: ArrayStore: r1[0] = d0  ; List_8
    //     0x129582c: stur            d0, [x1, #0x17]
    // 0x1295830: r0 = RoundedRectangleBorder()
    //     0x1295830: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1295834: mov             x1, x0
    // 0x1295838: r0 = Instance_BorderRadius
    //     0x1295838: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x129583c: ldr             x0, [x0, #0xf70]
    // 0x1295840: StoreField: r1->field_b = r0
    //     0x1295840: stur            w0, [x1, #0xb]
    // 0x1295844: ldur            x0, [fp, #-8]
    // 0x1295848: StoreField: r1->field_7 = r0
    //     0x1295848: stur            w0, [x1, #7]
    // 0x129584c: r16 = <RoundedRectangleBorder>
    //     0x129584c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1295850: ldr             x16, [x16, #0xf78]
    // 0x1295854: stp             x1, x16, [SP]
    // 0x1295858: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1295858: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x129585c: r0 = all()
    //     0x129585c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1295860: stur            x0, [fp, #-8]
    // 0x1295864: r0 = ButtonStyle()
    //     0x1295864: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1295868: mov             x1, x0
    // 0x129586c: ldur            x0, [fp, #-0x20]
    // 0x1295870: stur            x1, [fp, #-0x28]
    // 0x1295874: StoreField: r1->field_23 = r0
    //     0x1295874: stur            w0, [x1, #0x23]
    // 0x1295878: ldur            x0, [fp, #-8]
    // 0x129587c: StoreField: r1->field_43 = r0
    //     0x129587c: stur            w0, [x1, #0x43]
    // 0x1295880: r0 = TextButtonThemeData()
    //     0x1295880: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1295884: mov             x1, x0
    // 0x1295888: ldur            x0, [fp, #-0x28]
    // 0x129588c: stur            x1, [fp, #-8]
    // 0x1295890: StoreField: r1->field_7 = r0
    //     0x1295890: stur            w0, [x1, #7]
    // 0x1295894: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1295894: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1295898: ldr             x0, [x0, #0x1ab0]
    //     0x129589c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12958a0: cmp             w0, w16
    //     0x12958a4: b.ne            #0x12958b4
    //     0x12958a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x12958ac: ldr             x2, [x2, #0x60]
    //     0x12958b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12958b4: LoadField: r1 = r0->field_87
    //     0x12958b4: ldur            w1, [x0, #0x87]
    // 0x12958b8: DecompressPointer r1
    //     0x12958b8: add             x1, x1, HEAP, lsl #32
    // 0x12958bc: LoadField: r0 = r1->field_7
    //     0x12958bc: ldur            w0, [x1, #7]
    // 0x12958c0: DecompressPointer r0
    //     0x12958c0: add             x0, x0, HEAP, lsl #32
    // 0x12958c4: stur            x0, [fp, #-0x20]
    // 0x12958c8: r16 = 14.000000
    //     0x12958c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x12958cc: ldr             x16, [x16, #0x1d8]
    // 0x12958d0: ldur            lr, [fp, #-0x18]
    // 0x12958d4: stp             lr, x16, [SP]
    // 0x12958d8: mov             x1, x0
    // 0x12958dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12958dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12958e0: ldr             x4, [x4, #0xaa0]
    // 0x12958e4: r0 = copyWith()
    //     0x12958e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12958e8: stur            x0, [fp, #-0x28]
    // 0x12958ec: r0 = Text()
    //     0x12958ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12958f0: mov             x3, x0
    // 0x12958f4: r0 = "VIEW BAG"
    //     0x12958f4: add             x0, PP, #0x48, lsl #12  ; [pp+0x484f8] "VIEW BAG"
    //     0x12958f8: ldr             x0, [x0, #0x4f8]
    // 0x12958fc: stur            x3, [fp, #-0x30]
    // 0x1295900: StoreField: r3->field_b = r0
    //     0x1295900: stur            w0, [x3, #0xb]
    // 0x1295904: ldur            x0, [fp, #-0x28]
    // 0x1295908: StoreField: r3->field_13 = r0
    //     0x1295908: stur            w0, [x3, #0x13]
    // 0x129590c: ldur            x2, [fp, #-0x10]
    // 0x1295910: r1 = Function '<anonymous closure>':.
    //     0x1295910: add             x1, PP, #0x48, lsl #12  ; [pp+0x48500] AnonymousClosure: (0x1295c4c), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x1295798)
    //     0x1295914: ldr             x1, [x1, #0x500]
    // 0x1295918: r0 = AllocateClosure()
    //     0x1295918: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x129591c: stur            x0, [fp, #-0x28]
    // 0x1295920: r0 = TextButton()
    //     0x1295920: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1295924: mov             x1, x0
    // 0x1295928: ldur            x0, [fp, #-0x28]
    // 0x129592c: stur            x1, [fp, #-0x38]
    // 0x1295930: StoreField: r1->field_b = r0
    //     0x1295930: stur            w0, [x1, #0xb]
    // 0x1295934: r0 = false
    //     0x1295934: add             x0, NULL, #0x30  ; false
    // 0x1295938: StoreField: r1->field_27 = r0
    //     0x1295938: stur            w0, [x1, #0x27]
    // 0x129593c: r2 = true
    //     0x129593c: add             x2, NULL, #0x20  ; true
    // 0x1295940: StoreField: r1->field_2f = r2
    //     0x1295940: stur            w2, [x1, #0x2f]
    // 0x1295944: ldur            x3, [fp, #-0x30]
    // 0x1295948: StoreField: r1->field_37 = r3
    //     0x1295948: stur            w3, [x1, #0x37]
    // 0x129594c: r0 = TextButtonTheme()
    //     0x129594c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1295950: mov             x1, x0
    // 0x1295954: ldur            x0, [fp, #-8]
    // 0x1295958: stur            x1, [fp, #-0x28]
    // 0x129595c: StoreField: r1->field_f = r0
    //     0x129595c: stur            w0, [x1, #0xf]
    // 0x1295960: ldur            x0, [fp, #-0x38]
    // 0x1295964: StoreField: r1->field_b = r0
    //     0x1295964: stur            w0, [x1, #0xb]
    // 0x1295968: r0 = Padding()
    //     0x1295968: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x129596c: mov             x2, x0
    // 0x1295970: r0 = Instance_EdgeInsets
    //     0x1295970: add             x0, PP, #0x48, lsl #12  ; [pp+0x48508] Obj!EdgeInsets@d59871
    //     0x1295974: ldr             x0, [x0, #0x508]
    // 0x1295978: stur            x2, [fp, #-8]
    // 0x129597c: StoreField: r2->field_f = r0
    //     0x129597c: stur            w0, [x2, #0xf]
    // 0x1295980: ldur            x0, [fp, #-0x28]
    // 0x1295984: StoreField: r2->field_b = r0
    //     0x1295984: stur            w0, [x2, #0xb]
    // 0x1295988: r1 = <FlexParentData>
    //     0x1295988: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x129598c: ldr             x1, [x1, #0xe00]
    // 0x1295990: r0 = Flexible()
    //     0x1295990: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1295994: mov             x1, x0
    // 0x1295998: r0 = 1
    //     0x1295998: movz            x0, #0x1
    // 0x129599c: stur            x1, [fp, #-0x28]
    // 0x12959a0: StoreField: r1->field_13 = r0
    //     0x12959a0: stur            x0, [x1, #0x13]
    // 0x12959a4: r2 = Instance_FlexFit
    //     0x12959a4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x12959a8: ldr             x2, [x2, #0xe08]
    // 0x12959ac: StoreField: r1->field_1b = r2
    //     0x12959ac: stur            w2, [x1, #0x1b]
    // 0x12959b0: ldur            x3, [fp, #-8]
    // 0x12959b4: StoreField: r1->field_b = r3
    //     0x12959b4: stur            w3, [x1, #0xb]
    // 0x12959b8: r16 = <EdgeInsets>
    //     0x12959b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x12959bc: ldr             x16, [x16, #0xda0]
    // 0x12959c0: r30 = Instance_EdgeInsets
    //     0x12959c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x12959c4: ldr             lr, [lr, #0x1f0]
    // 0x12959c8: stp             lr, x16, [SP]
    // 0x12959cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12959cc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12959d0: r0 = all()
    //     0x12959d0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12959d4: stur            x0, [fp, #-8]
    // 0x12959d8: r16 = <Color>
    //     0x12959d8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x12959dc: ldr             x16, [x16, #0xf80]
    // 0x12959e0: ldur            lr, [fp, #-0x18]
    // 0x12959e4: stp             lr, x16, [SP]
    // 0x12959e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12959e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12959ec: r0 = all()
    //     0x12959ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12959f0: stur            x0, [fp, #-0x18]
    // 0x12959f4: r16 = <RoundedRectangleBorder>
    //     0x12959f4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x12959f8: ldr             x16, [x16, #0xf78]
    // 0x12959fc: r30 = Instance_RoundedRectangleBorder
    //     0x12959fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1295a00: ldr             lr, [lr, #0xd68]
    // 0x1295a04: stp             lr, x16, [SP]
    // 0x1295a08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1295a08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1295a0c: r0 = all()
    //     0x1295a0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1295a10: stur            x0, [fp, #-0x30]
    // 0x1295a14: r0 = ButtonStyle()
    //     0x1295a14: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1295a18: mov             x1, x0
    // 0x1295a1c: ldur            x0, [fp, #-0x18]
    // 0x1295a20: stur            x1, [fp, #-0x38]
    // 0x1295a24: StoreField: r1->field_b = r0
    //     0x1295a24: stur            w0, [x1, #0xb]
    // 0x1295a28: ldur            x0, [fp, #-8]
    // 0x1295a2c: StoreField: r1->field_23 = r0
    //     0x1295a2c: stur            w0, [x1, #0x23]
    // 0x1295a30: ldur            x0, [fp, #-0x30]
    // 0x1295a34: StoreField: r1->field_43 = r0
    //     0x1295a34: stur            w0, [x1, #0x43]
    // 0x1295a38: r0 = TextButtonThemeData()
    //     0x1295a38: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1295a3c: mov             x2, x0
    // 0x1295a40: ldur            x0, [fp, #-0x38]
    // 0x1295a44: stur            x2, [fp, #-8]
    // 0x1295a48: StoreField: r2->field_7 = r0
    //     0x1295a48: stur            w0, [x2, #7]
    // 0x1295a4c: r16 = 14.000000
    //     0x1295a4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1295a50: ldr             x16, [x16, #0x1d8]
    // 0x1295a54: r30 = Instance_Color
    //     0x1295a54: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1295a58: stp             lr, x16, [SP]
    // 0x1295a5c: ldur            x1, [fp, #-0x20]
    // 0x1295a60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1295a60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1295a64: ldr             x4, [x4, #0xaa0]
    // 0x1295a68: r0 = copyWith()
    //     0x1295a68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1295a6c: stur            x0, [fp, #-0x18]
    // 0x1295a70: r0 = Text()
    //     0x1295a70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1295a74: mov             x3, x0
    // 0x1295a78: r0 = "CHECKOUT"
    //     0x1295a78: add             x0, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0x1295a7c: ldr             x0, [x0, #0x858]
    // 0x1295a80: stur            x3, [fp, #-0x20]
    // 0x1295a84: StoreField: r3->field_b = r0
    //     0x1295a84: stur            w0, [x3, #0xb]
    // 0x1295a88: ldur            x0, [fp, #-0x18]
    // 0x1295a8c: StoreField: r3->field_13 = r0
    //     0x1295a8c: stur            w0, [x3, #0x13]
    // 0x1295a90: ldur            x2, [fp, #-0x10]
    // 0x1295a94: r1 = Function '<anonymous closure>':.
    //     0x1295a94: add             x1, PP, #0x48, lsl #12  ; [pp+0x48510] AnonymousClosure: (0x1295be8), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x1295798)
    //     0x1295a98: ldr             x1, [x1, #0x510]
    // 0x1295a9c: r0 = AllocateClosure()
    //     0x1295a9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1295aa0: stur            x0, [fp, #-0x10]
    // 0x1295aa4: r0 = TextButton()
    //     0x1295aa4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1295aa8: mov             x1, x0
    // 0x1295aac: ldur            x0, [fp, #-0x10]
    // 0x1295ab0: stur            x1, [fp, #-0x18]
    // 0x1295ab4: StoreField: r1->field_b = r0
    //     0x1295ab4: stur            w0, [x1, #0xb]
    // 0x1295ab8: r0 = false
    //     0x1295ab8: add             x0, NULL, #0x30  ; false
    // 0x1295abc: StoreField: r1->field_27 = r0
    //     0x1295abc: stur            w0, [x1, #0x27]
    // 0x1295ac0: r0 = true
    //     0x1295ac0: add             x0, NULL, #0x20  ; true
    // 0x1295ac4: StoreField: r1->field_2f = r0
    //     0x1295ac4: stur            w0, [x1, #0x2f]
    // 0x1295ac8: ldur            x0, [fp, #-0x20]
    // 0x1295acc: StoreField: r1->field_37 = r0
    //     0x1295acc: stur            w0, [x1, #0x37]
    // 0x1295ad0: r0 = TextButtonTheme()
    //     0x1295ad0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1295ad4: mov             x1, x0
    // 0x1295ad8: ldur            x0, [fp, #-8]
    // 0x1295adc: stur            x1, [fp, #-0x10]
    // 0x1295ae0: StoreField: r1->field_f = r0
    //     0x1295ae0: stur            w0, [x1, #0xf]
    // 0x1295ae4: ldur            x0, [fp, #-0x18]
    // 0x1295ae8: StoreField: r1->field_b = r0
    //     0x1295ae8: stur            w0, [x1, #0xb]
    // 0x1295aec: r0 = Padding()
    //     0x1295aec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1295af0: mov             x2, x0
    // 0x1295af4: r0 = Instance_EdgeInsets
    //     0x1295af4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48518] Obj!EdgeInsets@d58911
    //     0x1295af8: ldr             x0, [x0, #0x518]
    // 0x1295afc: stur            x2, [fp, #-8]
    // 0x1295b00: StoreField: r2->field_f = r0
    //     0x1295b00: stur            w0, [x2, #0xf]
    // 0x1295b04: ldur            x0, [fp, #-0x10]
    // 0x1295b08: StoreField: r2->field_b = r0
    //     0x1295b08: stur            w0, [x2, #0xb]
    // 0x1295b0c: r1 = <FlexParentData>
    //     0x1295b0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1295b10: ldr             x1, [x1, #0xe00]
    // 0x1295b14: r0 = Flexible()
    //     0x1295b14: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1295b18: mov             x3, x0
    // 0x1295b1c: r0 = 1
    //     0x1295b1c: movz            x0, #0x1
    // 0x1295b20: stur            x3, [fp, #-0x10]
    // 0x1295b24: StoreField: r3->field_13 = r0
    //     0x1295b24: stur            x0, [x3, #0x13]
    // 0x1295b28: r0 = Instance_FlexFit
    //     0x1295b28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1295b2c: ldr             x0, [x0, #0xe08]
    // 0x1295b30: StoreField: r3->field_1b = r0
    //     0x1295b30: stur            w0, [x3, #0x1b]
    // 0x1295b34: ldur            x0, [fp, #-8]
    // 0x1295b38: StoreField: r3->field_b = r0
    //     0x1295b38: stur            w0, [x3, #0xb]
    // 0x1295b3c: r1 = Null
    //     0x1295b3c: mov             x1, NULL
    // 0x1295b40: r2 = 4
    //     0x1295b40: movz            x2, #0x4
    // 0x1295b44: r0 = AllocateArray()
    //     0x1295b44: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1295b48: mov             x2, x0
    // 0x1295b4c: ldur            x0, [fp, #-0x28]
    // 0x1295b50: stur            x2, [fp, #-8]
    // 0x1295b54: StoreField: r2->field_f = r0
    //     0x1295b54: stur            w0, [x2, #0xf]
    // 0x1295b58: ldur            x0, [fp, #-0x10]
    // 0x1295b5c: StoreField: r2->field_13 = r0
    //     0x1295b5c: stur            w0, [x2, #0x13]
    // 0x1295b60: r1 = <Widget>
    //     0x1295b60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1295b64: r0 = AllocateGrowableArray()
    //     0x1295b64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1295b68: mov             x1, x0
    // 0x1295b6c: ldur            x0, [fp, #-8]
    // 0x1295b70: stur            x1, [fp, #-0x10]
    // 0x1295b74: StoreField: r1->field_f = r0
    //     0x1295b74: stur            w0, [x1, #0xf]
    // 0x1295b78: r0 = 4
    //     0x1295b78: movz            x0, #0x4
    // 0x1295b7c: StoreField: r1->field_b = r0
    //     0x1295b7c: stur            w0, [x1, #0xb]
    // 0x1295b80: r0 = Row()
    //     0x1295b80: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1295b84: r1 = Instance_Axis
    //     0x1295b84: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1295b88: StoreField: r0->field_f = r1
    //     0x1295b88: stur            w1, [x0, #0xf]
    // 0x1295b8c: r1 = Instance_MainAxisAlignment
    //     0x1295b8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x1295b90: ldr             x1, [x1, #0xd10]
    // 0x1295b94: StoreField: r0->field_13 = r1
    //     0x1295b94: stur            w1, [x0, #0x13]
    // 0x1295b98: r1 = Instance_MainAxisSize
    //     0x1295b98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1295b9c: ldr             x1, [x1, #0xa10]
    // 0x1295ba0: ArrayStore: r0[0] = r1  ; List_4
    //     0x1295ba0: stur            w1, [x0, #0x17]
    // 0x1295ba4: r1 = Instance_CrossAxisAlignment
    //     0x1295ba4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1295ba8: ldr             x1, [x1, #0xa18]
    // 0x1295bac: StoreField: r0->field_1b = r1
    //     0x1295bac: stur            w1, [x0, #0x1b]
    // 0x1295bb0: r1 = Instance_VerticalDirection
    //     0x1295bb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1295bb4: ldr             x1, [x1, #0xa20]
    // 0x1295bb8: StoreField: r0->field_23 = r1
    //     0x1295bb8: stur            w1, [x0, #0x23]
    // 0x1295bbc: r1 = Instance_Clip
    //     0x1295bbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1295bc0: ldr             x1, [x1, #0x38]
    // 0x1295bc4: StoreField: r0->field_2b = r1
    //     0x1295bc4: stur            w1, [x0, #0x2b]
    // 0x1295bc8: StoreField: r0->field_2f = rZR
    //     0x1295bc8: stur            xzr, [x0, #0x2f]
    // 0x1295bcc: ldur            x1, [fp, #-0x10]
    // 0x1295bd0: StoreField: r0->field_b = r1
    //     0x1295bd0: stur            w1, [x0, #0xb]
    // 0x1295bd4: LeaveFrame
    //     0x1295bd4: mov             SP, fp
    //     0x1295bd8: ldp             fp, lr, [SP], #0x10
    // 0x1295bdc: ret
    //     0x1295bdc: ret             
    // 0x1295be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295be0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1295be4: b               #0x12957b4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1295be8, size: 0x64
    // 0x1295be8: EnterFrame
    //     0x1295be8: stp             fp, lr, [SP, #-0x10]!
    //     0x1295bec: mov             fp, SP
    // 0x1295bf0: AllocStack(0x8)
    //     0x1295bf0: sub             SP, SP, #8
    // 0x1295bf4: SetupParameters()
    //     0x1295bf4: ldr             x0, [fp, #0x10]
    //     0x1295bf8: ldur            w1, [x0, #0x17]
    //     0x1295bfc: add             x1, x1, HEAP, lsl #32
    // 0x1295c00: CheckStackOverflow
    //     0x1295c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1295c04: cmp             SP, x16
    //     0x1295c08: b.ls            #0x1295c44
    // 0x1295c0c: LoadField: r0 = r1->field_f
    //     0x1295c0c: ldur            w0, [x1, #0xf]
    // 0x1295c10: DecompressPointer r0
    //     0x1295c10: add             x0, x0, HEAP, lsl #32
    // 0x1295c14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1295c14: ldur            w1, [x0, #0x17]
    // 0x1295c18: DecompressPointer r1
    //     0x1295c18: add             x1, x1, HEAP, lsl #32
    // 0x1295c1c: str             x1, [SP]
    // 0x1295c20: r4 = 0
    //     0x1295c20: movz            x4, #0
    // 0x1295c24: ldr             x0, [SP]
    // 0x1295c28: r16 = UnlinkedCall_0x613b5c
    //     0x1295c28: add             x16, PP, #0x48, lsl #12  ; [pp+0x48520] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1295c2c: add             x16, x16, #0x520
    // 0x1295c30: ldp             x5, lr, [x16]
    // 0x1295c34: blr             lr
    // 0x1295c38: LeaveFrame
    //     0x1295c38: mov             SP, fp
    //     0x1295c3c: ldp             fp, lr, [SP], #0x10
    // 0x1295c40: ret
    //     0x1295c40: ret             
    // 0x1295c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295c44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1295c48: b               #0x1295c0c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1295c4c, size: 0x64
    // 0x1295c4c: EnterFrame
    //     0x1295c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x1295c50: mov             fp, SP
    // 0x1295c54: AllocStack(0x8)
    //     0x1295c54: sub             SP, SP, #8
    // 0x1295c58: SetupParameters()
    //     0x1295c58: ldr             x0, [fp, #0x10]
    //     0x1295c5c: ldur            w1, [x0, #0x17]
    //     0x1295c60: add             x1, x1, HEAP, lsl #32
    // 0x1295c64: CheckStackOverflow
    //     0x1295c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1295c68: cmp             SP, x16
    //     0x1295c6c: b.ls            #0x1295ca8
    // 0x1295c70: LoadField: r0 = r1->field_f
    //     0x1295c70: ldur            w0, [x1, #0xf]
    // 0x1295c74: DecompressPointer r0
    //     0x1295c74: add             x0, x0, HEAP, lsl #32
    // 0x1295c78: LoadField: r1 = r0->field_13
    //     0x1295c78: ldur            w1, [x0, #0x13]
    // 0x1295c7c: DecompressPointer r1
    //     0x1295c7c: add             x1, x1, HEAP, lsl #32
    // 0x1295c80: str             x1, [SP]
    // 0x1295c84: r4 = 0
    //     0x1295c84: movz            x4, #0
    // 0x1295c88: ldr             x0, [SP]
    // 0x1295c8c: r16 = UnlinkedCall_0x613b5c
    //     0x1295c8c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48530] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1295c90: add             x16, x16, #0x530
    // 0x1295c94: ldp             x5, lr, [x16]
    // 0x1295c98: blr             lr
    // 0x1295c9c: LeaveFrame
    //     0x1295c9c: mov             SP, fp
    //     0x1295ca0: ldp             fp, lr, [SP], #0x10
    // 0x1295ca4: ret
    //     0x1295ca4: ret             
    // 0x1295ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295ca8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1295cac: b               #0x1295c70
  }
  _ _buildSingleButton(/* No info */) {
    // ** addr: 0x1295cb0, size: 0x218
    // 0x1295cb0: EnterFrame
    //     0x1295cb0: stp             fp, lr, [SP, #-0x10]!
    //     0x1295cb4: mov             fp, SP
    // 0x1295cb8: AllocStack(0x38)
    //     0x1295cb8: sub             SP, SP, #0x38
    // 0x1295cbc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x1295cbc: stur            x1, [fp, #-8]
    // 0x1295cc0: CheckStackOverflow
    //     0x1295cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1295cc4: cmp             SP, x16
    //     0x1295cc8: b.ls            #0x1295ec0
    // 0x1295ccc: r1 = 1
    //     0x1295ccc: movz            x1, #0x1
    // 0x1295cd0: r0 = AllocateContext()
    //     0x1295cd0: bl              #0x16f6108  ; AllocateContextStub
    // 0x1295cd4: mov             x1, x0
    // 0x1295cd8: ldur            x0, [fp, #-8]
    // 0x1295cdc: stur            x1, [fp, #-0x10]
    // 0x1295ce0: StoreField: r1->field_f = r0
    //     0x1295ce0: stur            w0, [x1, #0xf]
    // 0x1295ce4: r16 = <EdgeInsets>
    //     0x1295ce4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1295ce8: ldr             x16, [x16, #0xda0]
    // 0x1295cec: r30 = Instance_EdgeInsets
    //     0x1295cec: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0x1295cf0: ldr             lr, [lr, #0x670]
    // 0x1295cf4: stp             lr, x16, [SP]
    // 0x1295cf8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1295cf8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1295cfc: r0 = all()
    //     0x1295cfc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1295d00: mov             x1, x0
    // 0x1295d04: ldur            x0, [fp, #-8]
    // 0x1295d08: stur            x1, [fp, #-0x20]
    // 0x1295d0c: LoadField: r2 = r0->field_1f
    //     0x1295d0c: ldur            w2, [x0, #0x1f]
    // 0x1295d10: DecompressPointer r2
    //     0x1295d10: add             x2, x2, HEAP, lsl #32
    // 0x1295d14: stur            x2, [fp, #-0x18]
    // 0x1295d18: r0 = BorderSide()
    //     0x1295d18: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1295d1c: mov             x1, x0
    // 0x1295d20: ldur            x0, [fp, #-0x18]
    // 0x1295d24: stur            x1, [fp, #-8]
    // 0x1295d28: StoreField: r1->field_7 = r0
    //     0x1295d28: stur            w0, [x1, #7]
    // 0x1295d2c: d0 = 1.000000
    //     0x1295d2c: fmov            d0, #1.00000000
    // 0x1295d30: StoreField: r1->field_b = d0
    //     0x1295d30: stur            d0, [x1, #0xb]
    // 0x1295d34: r2 = Instance_BorderStyle
    //     0x1295d34: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1295d38: ldr             x2, [x2, #0xf68]
    // 0x1295d3c: StoreField: r1->field_13 = r2
    //     0x1295d3c: stur            w2, [x1, #0x13]
    // 0x1295d40: d0 = -1.000000
    //     0x1295d40: fmov            d0, #-1.00000000
    // 0x1295d44: ArrayStore: r1[0] = d0  ; List_8
    //     0x1295d44: stur            d0, [x1, #0x17]
    // 0x1295d48: r0 = RoundedRectangleBorder()
    //     0x1295d48: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1295d4c: mov             x1, x0
    // 0x1295d50: r0 = Instance_BorderRadius
    //     0x1295d50: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1295d54: ldr             x0, [x0, #0xf70]
    // 0x1295d58: StoreField: r1->field_b = r0
    //     0x1295d58: stur            w0, [x1, #0xb]
    // 0x1295d5c: ldur            x0, [fp, #-8]
    // 0x1295d60: StoreField: r1->field_7 = r0
    //     0x1295d60: stur            w0, [x1, #7]
    // 0x1295d64: r16 = <RoundedRectangleBorder>
    //     0x1295d64: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1295d68: ldr             x16, [x16, #0xf78]
    // 0x1295d6c: stp             x1, x16, [SP]
    // 0x1295d70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1295d70: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1295d74: r0 = all()
    //     0x1295d74: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1295d78: stur            x0, [fp, #-8]
    // 0x1295d7c: r0 = ButtonStyle()
    //     0x1295d7c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1295d80: mov             x1, x0
    // 0x1295d84: ldur            x0, [fp, #-0x20]
    // 0x1295d88: stur            x1, [fp, #-0x28]
    // 0x1295d8c: StoreField: r1->field_23 = r0
    //     0x1295d8c: stur            w0, [x1, #0x23]
    // 0x1295d90: ldur            x0, [fp, #-8]
    // 0x1295d94: StoreField: r1->field_43 = r0
    //     0x1295d94: stur            w0, [x1, #0x43]
    // 0x1295d98: r0 = TextButtonThemeData()
    //     0x1295d98: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1295d9c: mov             x1, x0
    // 0x1295da0: ldur            x0, [fp, #-0x28]
    // 0x1295da4: stur            x1, [fp, #-8]
    // 0x1295da8: StoreField: r1->field_7 = r0
    //     0x1295da8: stur            w0, [x1, #7]
    // 0x1295dac: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1295dac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1295db0: ldr             x0, [x0, #0x1ab0]
    //     0x1295db4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1295db8: cmp             w0, w16
    //     0x1295dbc: b.ne            #0x1295dcc
    //     0x1295dc0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x1295dc4: ldr             x2, [x2, #0x60]
    //     0x1295dc8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1295dcc: LoadField: r1 = r0->field_87
    //     0x1295dcc: ldur            w1, [x0, #0x87]
    // 0x1295dd0: DecompressPointer r1
    //     0x1295dd0: add             x1, x1, HEAP, lsl #32
    // 0x1295dd4: LoadField: r0 = r1->field_7
    //     0x1295dd4: ldur            w0, [x1, #7]
    // 0x1295dd8: DecompressPointer r0
    //     0x1295dd8: add             x0, x0, HEAP, lsl #32
    // 0x1295ddc: r16 = 14.000000
    //     0x1295ddc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1295de0: ldr             x16, [x16, #0x1d8]
    // 0x1295de4: ldur            lr, [fp, #-0x18]
    // 0x1295de8: stp             lr, x16, [SP]
    // 0x1295dec: mov             x1, x0
    // 0x1295df0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1295df0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1295df4: ldr             x4, [x4, #0xaa0]
    // 0x1295df8: r0 = copyWith()
    //     0x1295df8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1295dfc: stur            x0, [fp, #-0x18]
    // 0x1295e00: r0 = Text()
    //     0x1295e00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1295e04: mov             x3, x0
    // 0x1295e08: r0 = "VIEW BAG"
    //     0x1295e08: add             x0, PP, #0x48, lsl #12  ; [pp+0x484f8] "VIEW BAG"
    //     0x1295e0c: ldr             x0, [x0, #0x4f8]
    // 0x1295e10: stur            x3, [fp, #-0x20]
    // 0x1295e14: StoreField: r3->field_b = r0
    //     0x1295e14: stur            w0, [x3, #0xb]
    // 0x1295e18: ldur            x0, [fp, #-0x18]
    // 0x1295e1c: StoreField: r3->field_13 = r0
    //     0x1295e1c: stur            w0, [x3, #0x13]
    // 0x1295e20: ldur            x2, [fp, #-0x10]
    // 0x1295e24: r1 = Function '<anonymous closure>':.
    //     0x1295e24: add             x1, PP, #0x48, lsl #12  ; [pp+0x48540] AnonymousClosure: (0x1295ec8), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton (0x1295cb0)
    //     0x1295e28: ldr             x1, [x1, #0x540]
    // 0x1295e2c: r0 = AllocateClosure()
    //     0x1295e2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1295e30: stur            x0, [fp, #-0x10]
    // 0x1295e34: r0 = TextButton()
    //     0x1295e34: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1295e38: mov             x1, x0
    // 0x1295e3c: ldur            x0, [fp, #-0x10]
    // 0x1295e40: stur            x1, [fp, #-0x18]
    // 0x1295e44: StoreField: r1->field_b = r0
    //     0x1295e44: stur            w0, [x1, #0xb]
    // 0x1295e48: r0 = false
    //     0x1295e48: add             x0, NULL, #0x30  ; false
    // 0x1295e4c: StoreField: r1->field_27 = r0
    //     0x1295e4c: stur            w0, [x1, #0x27]
    // 0x1295e50: r0 = true
    //     0x1295e50: add             x0, NULL, #0x20  ; true
    // 0x1295e54: StoreField: r1->field_2f = r0
    //     0x1295e54: stur            w0, [x1, #0x2f]
    // 0x1295e58: ldur            x0, [fp, #-0x20]
    // 0x1295e5c: StoreField: r1->field_37 = r0
    //     0x1295e5c: stur            w0, [x1, #0x37]
    // 0x1295e60: r0 = TextButtonTheme()
    //     0x1295e60: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1295e64: mov             x1, x0
    // 0x1295e68: ldur            x0, [fp, #-8]
    // 0x1295e6c: stur            x1, [fp, #-0x10]
    // 0x1295e70: StoreField: r1->field_f = r0
    //     0x1295e70: stur            w0, [x1, #0xf]
    // 0x1295e74: ldur            x0, [fp, #-0x18]
    // 0x1295e78: StoreField: r1->field_b = r0
    //     0x1295e78: stur            w0, [x1, #0xb]
    // 0x1295e7c: r0 = SizedBox()
    //     0x1295e7c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1295e80: mov             x1, x0
    // 0x1295e84: r0 = inf
    //     0x1295e84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1295e88: ldr             x0, [x0, #0x9f8]
    // 0x1295e8c: stur            x1, [fp, #-8]
    // 0x1295e90: StoreField: r1->field_f = r0
    //     0x1295e90: stur            w0, [x1, #0xf]
    // 0x1295e94: ldur            x0, [fp, #-0x10]
    // 0x1295e98: StoreField: r1->field_b = r0
    //     0x1295e98: stur            w0, [x1, #0xb]
    // 0x1295e9c: r0 = Padding()
    //     0x1295e9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1295ea0: r1 = Instance_EdgeInsets
    //     0x1295ea0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48508] Obj!EdgeInsets@d59871
    //     0x1295ea4: ldr             x1, [x1, #0x508]
    // 0x1295ea8: StoreField: r0->field_f = r1
    //     0x1295ea8: stur            w1, [x0, #0xf]
    // 0x1295eac: ldur            x1, [fp, #-8]
    // 0x1295eb0: StoreField: r0->field_b = r1
    //     0x1295eb0: stur            w1, [x0, #0xb]
    // 0x1295eb4: LeaveFrame
    //     0x1295eb4: mov             SP, fp
    //     0x1295eb8: ldp             fp, lr, [SP], #0x10
    // 0x1295ebc: ret
    //     0x1295ebc: ret             
    // 0x1295ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295ec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1295ec4: b               #0x1295ccc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1295ec8, size: 0x64
    // 0x1295ec8: EnterFrame
    //     0x1295ec8: stp             fp, lr, [SP, #-0x10]!
    //     0x1295ecc: mov             fp, SP
    // 0x1295ed0: AllocStack(0x8)
    //     0x1295ed0: sub             SP, SP, #8
    // 0x1295ed4: SetupParameters()
    //     0x1295ed4: ldr             x0, [fp, #0x10]
    //     0x1295ed8: ldur            w1, [x0, #0x17]
    //     0x1295edc: add             x1, x1, HEAP, lsl #32
    // 0x1295ee0: CheckStackOverflow
    //     0x1295ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1295ee4: cmp             SP, x16
    //     0x1295ee8: b.ls            #0x1295f24
    // 0x1295eec: LoadField: r0 = r1->field_f
    //     0x1295eec: ldur            w0, [x1, #0xf]
    // 0x1295ef0: DecompressPointer r0
    //     0x1295ef0: add             x0, x0, HEAP, lsl #32
    // 0x1295ef4: LoadField: r1 = r0->field_13
    //     0x1295ef4: ldur            w1, [x0, #0x13]
    // 0x1295ef8: DecompressPointer r1
    //     0x1295ef8: add             x1, x1, HEAP, lsl #32
    // 0x1295efc: str             x1, [SP]
    // 0x1295f00: r4 = 0
    //     0x1295f00: movz            x4, #0
    // 0x1295f04: ldr             x0, [SP]
    // 0x1295f08: r16 = UnlinkedCall_0x613b5c
    //     0x1295f08: add             x16, PP, #0x48, lsl #12  ; [pp+0x48548] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1295f0c: add             x16, x16, #0x548
    // 0x1295f10: ldp             x5, lr, [x16]
    // 0x1295f14: blr             lr
    // 0x1295f18: LeaveFrame
    //     0x1295f18: mov             SP, fp
    //     0x1295f1c: ldp             fp, lr, [SP], #0x10
    // 0x1295f20: ret
    //     0x1295f20: ret             
    // 0x1295f24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1295f24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1295f28: b               #0x1295eec
  }
  _ _buildFreeGiftItem(/* No info */) {
    // ** addr: 0x1295f2c, size: 0x6d0
    // 0x1295f2c: EnterFrame
    //     0x1295f2c: stp             fp, lr, [SP, #-0x10]!
    //     0x1295f30: mov             fp, SP
    // 0x1295f34: AllocStack(0x68)
    //     0x1295f34: sub             SP, SP, #0x68
    // 0x1295f38: SetupParameters(BagBottomSheet this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x1295f38: mov             x0, x2
    //     0x1295f3c: stur            x2, [fp, #-0x18]
    //     0x1295f40: mov             x2, x1
    //     0x1295f44: stur            x1, [fp, #-0x10]
    //     0x1295f48: stur            x3, [fp, #-0x20]
    // 0x1295f4c: CheckStackOverflow
    //     0x1295f4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1295f50: cmp             SP, x16
    //     0x1295f54: b.ls            #0x12965f4
    // 0x1295f58: LoadField: r1 = r2->field_b
    //     0x1295f58: ldur            w1, [x2, #0xb]
    // 0x1295f5c: DecompressPointer r1
    //     0x1295f5c: add             x1, x1, HEAP, lsl #32
    // 0x1295f60: LoadField: r4 = r1->field_b
    //     0x1295f60: ldur            w4, [x1, #0xb]
    // 0x1295f64: DecompressPointer r4
    //     0x1295f64: add             x4, x4, HEAP, lsl #32
    // 0x1295f68: cmp             w4, NULL
    // 0x1295f6c: b.ne            #0x1295f78
    // 0x1295f70: r4 = Null
    //     0x1295f70: mov             x4, NULL
    // 0x1295f74: b               #0x1295f84
    // 0x1295f78: LoadField: r1 = r4->field_2f
    //     0x1295f78: ldur            w1, [x4, #0x2f]
    // 0x1295f7c: DecompressPointer r1
    //     0x1295f7c: add             x1, x1, HEAP, lsl #32
    // 0x1295f80: mov             x4, x1
    // 0x1295f84: stur            x4, [fp, #-8]
    // 0x1295f88: r1 = Instance_Color
    //     0x1295f88: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1295f8c: d0 = 0.070000
    //     0x1295f8c: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x1295f90: ldr             d0, [x17, #0x5f8]
    // 0x1295f94: r0 = withOpacity()
    //     0x1295f94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1295f98: r16 = 1.000000
    //     0x1295f98: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1295f9c: str             x16, [SP]
    // 0x1295fa0: mov             x2, x0
    // 0x1295fa4: r1 = Null
    //     0x1295fa4: mov             x1, NULL
    // 0x1295fa8: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x1295fa8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x1295fac: ldr             x4, [x4, #0x108]
    // 0x1295fb0: r0 = Border.all()
    //     0x1295fb0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1295fb4: mov             x1, x0
    // 0x1295fb8: ldur            x0, [fp, #-0x20]
    // 0x1295fbc: stur            x1, [fp, #-0x30]
    // 0x1295fc0: tbnz            w0, #4, #0x1295fd0
    // 0x1295fc4: r2 = Instance_LinearGradient
    //     0x1295fc4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x1295fc8: ldr             x2, [x2, #0x660]
    // 0x1295fcc: b               #0x1295fd4
    // 0x1295fd0: r2 = Null
    //     0x1295fd0: mov             x2, NULL
    // 0x1295fd4: stur            x2, [fp, #-0x28]
    // 0x1295fd8: r0 = BoxDecoration()
    //     0x1295fd8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1295fdc: mov             x1, x0
    // 0x1295fe0: ldur            x0, [fp, #-0x30]
    // 0x1295fe4: stur            x1, [fp, #-0x38]
    // 0x1295fe8: StoreField: r1->field_f = r0
    //     0x1295fe8: stur            w0, [x1, #0xf]
    // 0x1295fec: ldur            x0, [fp, #-0x28]
    // 0x1295ff0: StoreField: r1->field_1b = r0
    //     0x1295ff0: stur            w0, [x1, #0x1b]
    // 0x1295ff4: r0 = Instance_BoxShape
    //     0x1295ff4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1295ff8: ldr             x0, [x0, #0x80]
    // 0x1295ffc: StoreField: r1->field_23 = r0
    //     0x1295ffc: stur            w0, [x1, #0x23]
    // 0x1296000: ldur            x2, [fp, #-0x20]
    // 0x1296004: tbnz            w2, #4, #0x1296014
    // 0x1296008: r3 = Instance_Color
    //     0x1296008: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x129600c: ldr             x3, [x3, #0x858]
    // 0x1296010: b               #0x1296020
    // 0x1296014: ldur            x2, [fp, #-0x10]
    // 0x1296018: LoadField: r3 = r2->field_23
    //     0x1296018: ldur            w3, [x2, #0x23]
    // 0x129601c: DecompressPointer r3
    //     0x129601c: add             x3, x3, HEAP, lsl #32
    // 0x1296020: ldur            x2, [fp, #-8]
    // 0x1296024: stur            x3, [fp, #-0x10]
    // 0x1296028: r0 = BoxDecoration()
    //     0x1296028: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x129602c: mov             x2, x0
    // 0x1296030: ldur            x0, [fp, #-0x10]
    // 0x1296034: stur            x2, [fp, #-0x20]
    // 0x1296038: StoreField: r2->field_7 = r0
    //     0x1296038: stur            w0, [x2, #7]
    // 0x129603c: r0 = Instance_BoxShape
    //     0x129603c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1296040: ldr             x0, [x0, #0x80]
    // 0x1296044: StoreField: r2->field_23 = r0
    //     0x1296044: stur            w0, [x2, #0x23]
    // 0x1296048: ldur            x1, [fp, #-0x18]
    // 0x129604c: r0 = of()
    //     0x129604c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1296050: LoadField: r1 = r0->field_87
    //     0x1296050: ldur            w1, [x0, #0x87]
    // 0x1296054: DecompressPointer r1
    //     0x1296054: add             x1, x1, HEAP, lsl #32
    // 0x1296058: LoadField: r0 = r1->field_7
    //     0x1296058: ldur            w0, [x1, #7]
    // 0x129605c: DecompressPointer r0
    //     0x129605c: add             x0, x0, HEAP, lsl #32
    // 0x1296060: r16 = 12.000000
    //     0x1296060: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1296064: ldr             x16, [x16, #0x9e8]
    // 0x1296068: r30 = Instance_Color
    //     0x1296068: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x129606c: stp             lr, x16, [SP]
    // 0x1296070: mov             x1, x0
    // 0x1296074: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1296074: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1296078: ldr             x4, [x4, #0xaa0]
    // 0x129607c: r0 = copyWith()
    //     0x129607c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296080: stur            x0, [fp, #-0x10]
    // 0x1296084: r0 = Text()
    //     0x1296084: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296088: mov             x1, x0
    // 0x129608c: r0 = "Free"
    //     0x129608c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x1296090: ldr             x0, [x0, #0x668]
    // 0x1296094: stur            x1, [fp, #-0x28]
    // 0x1296098: StoreField: r1->field_b = r0
    //     0x1296098: stur            w0, [x1, #0xb]
    // 0x129609c: ldur            x2, [fp, #-0x10]
    // 0x12960a0: StoreField: r1->field_13 = r2
    //     0x12960a0: stur            w2, [x1, #0x13]
    // 0x12960a4: r0 = Center()
    //     0x12960a4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x12960a8: mov             x1, x0
    // 0x12960ac: r0 = Instance_Alignment
    //     0x12960ac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x12960b0: ldr             x0, [x0, #0xb10]
    // 0x12960b4: stur            x1, [fp, #-0x10]
    // 0x12960b8: StoreField: r1->field_f = r0
    //     0x12960b8: stur            w0, [x1, #0xf]
    // 0x12960bc: ldur            x0, [fp, #-0x28]
    // 0x12960c0: StoreField: r1->field_b = r0
    //     0x12960c0: stur            w0, [x1, #0xb]
    // 0x12960c4: r0 = RotatedBox()
    //     0x12960c4: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0x12960c8: mov             x1, x0
    // 0x12960cc: r0 = -1
    //     0x12960cc: movn            x0, #0
    // 0x12960d0: stur            x1, [fp, #-0x28]
    // 0x12960d4: StoreField: r1->field_f = r0
    //     0x12960d4: stur            x0, [x1, #0xf]
    // 0x12960d8: ldur            x0, [fp, #-0x10]
    // 0x12960dc: StoreField: r1->field_b = r0
    //     0x12960dc: stur            w0, [x1, #0xb]
    // 0x12960e0: r0 = Container()
    //     0x12960e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12960e4: stur            x0, [fp, #-0x10]
    // 0x12960e8: ldur            x16, [fp, #-0x20]
    // 0x12960ec: r30 = 24.000000
    //     0x12960ec: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x12960f0: ldr             lr, [lr, #0xba8]
    // 0x12960f4: stp             lr, x16, [SP, #0x10]
    // 0x12960f8: r16 = 56.000000
    //     0x12960f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x12960fc: ldr             x16, [x16, #0xb78]
    // 0x1296100: ldur            lr, [fp, #-0x28]
    // 0x1296104: stp             lr, x16, [SP]
    // 0x1296108: mov             x1, x0
    // 0x129610c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0x129610c: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0x1296110: ldr             x4, [x4, #0x5d0]
    // 0x1296114: r0 = Container()
    //     0x1296114: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1296118: ldur            x0, [fp, #-8]
    // 0x129611c: cmp             w0, NULL
    // 0x1296120: b.ne            #0x129612c
    // 0x1296124: r1 = Null
    //     0x1296124: mov             x1, NULL
    // 0x1296128: b               #0x1296134
    // 0x129612c: LoadField: r1 = r0->field_7
    //     0x129612c: ldur            w1, [x0, #7]
    // 0x1296130: DecompressPointer r1
    //     0x1296130: add             x1, x1, HEAP, lsl #32
    // 0x1296134: cmp             w1, NULL
    // 0x1296138: b.ne            #0x1296144
    // 0x129613c: r2 = ""
    //     0x129613c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1296140: b               #0x1296148
    // 0x1296144: mov             x2, x1
    // 0x1296148: stur            x2, [fp, #-0x20]
    // 0x129614c: r0 = ImageHeaders.forImages()
    //     0x129614c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x1296150: stur            x0, [fp, #-0x28]
    // 0x1296154: r0 = CachedNetworkImage()
    //     0x1296154: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1296158: stur            x0, [fp, #-0x30]
    // 0x129615c: ldur            x16, [fp, #-0x28]
    // 0x1296160: r30 = 56.000000
    //     0x1296160: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1296164: ldr             lr, [lr, #0xb78]
    // 0x1296168: stp             lr, x16, [SP, #0x18]
    // 0x129616c: r16 = 56.000000
    //     0x129616c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1296170: ldr             x16, [x16, #0xb78]
    // 0x1296174: r30 = Instance_BoxFit
    //     0x1296174: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x1296178: ldr             lr, [lr, #0x118]
    // 0x129617c: stp             lr, x16, [SP, #8]
    // 0x1296180: r16 = 224
    //     0x1296180: movz            x16, #0xe0
    // 0x1296184: str             x16, [SP]
    // 0x1296188: mov             x1, x0
    // 0x129618c: ldur            x2, [fp, #-0x20]
    // 0x1296190: r4 = const [0, 0x7, 0x5, 0x2, fit, 0x5, height, 0x4, httpHeaders, 0x2, memCacheWidth, 0x6, width, 0x3, null]
    //     0x1296190: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d8] List(15) [0, 0x7, 0x5, 0x2, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "memCacheWidth", 0x6, "width", 0x3, Null]
    //     0x1296194: ldr             x4, [x4, #0x5d8]
    // 0x1296198: r0 = CachedNetworkImage()
    //     0x1296198: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x129619c: ldur            x0, [fp, #-8]
    // 0x12961a0: cmp             w0, NULL
    // 0x12961a4: b.ne            #0x12961b0
    // 0x12961a8: r1 = Null
    //     0x12961a8: mov             x1, NULL
    // 0x12961ac: b               #0x12961b8
    // 0x12961b0: LoadField: r1 = r0->field_b
    //     0x12961b0: ldur            w1, [x0, #0xb]
    // 0x12961b4: DecompressPointer r1
    //     0x12961b4: add             x1, x1, HEAP, lsl #32
    // 0x12961b8: cmp             w1, NULL
    // 0x12961bc: b.ne            #0x12961c8
    // 0x12961c0: r2 = ""
    //     0x12961c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12961c4: b               #0x12961cc
    // 0x12961c8: mov             x2, x1
    // 0x12961cc: ldur            x1, [fp, #-0x18]
    // 0x12961d0: stur            x2, [fp, #-0x20]
    // 0x12961d4: r0 = of()
    //     0x12961d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12961d8: LoadField: r1 = r0->field_87
    //     0x12961d8: ldur            w1, [x0, #0x87]
    // 0x12961dc: DecompressPointer r1
    //     0x12961dc: add             x1, x1, HEAP, lsl #32
    // 0x12961e0: LoadField: r0 = r1->field_7
    //     0x12961e0: ldur            w0, [x1, #7]
    // 0x12961e4: DecompressPointer r0
    //     0x12961e4: add             x0, x0, HEAP, lsl #32
    // 0x12961e8: r16 = 12.000000
    //     0x12961e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x12961ec: ldr             x16, [x16, #0x9e8]
    // 0x12961f0: r30 = Instance_Color
    //     0x12961f0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12961f4: stp             lr, x16, [SP]
    // 0x12961f8: mov             x1, x0
    // 0x12961fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12961fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1296200: ldr             x4, [x4, #0xaa0]
    // 0x1296204: r0 = copyWith()
    //     0x1296204: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296208: stur            x0, [fp, #-0x28]
    // 0x129620c: r0 = Text()
    //     0x129620c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296210: mov             x1, x0
    // 0x1296214: ldur            x0, [fp, #-0x20]
    // 0x1296218: stur            x1, [fp, #-0x40]
    // 0x129621c: StoreField: r1->field_b = r0
    //     0x129621c: stur            w0, [x1, #0xb]
    // 0x1296220: ldur            x0, [fp, #-0x28]
    // 0x1296224: StoreField: r1->field_13 = r0
    //     0x1296224: stur            w0, [x1, #0x13]
    // 0x1296228: r0 = Instance_TextOverflow
    //     0x1296228: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x129622c: ldr             x0, [x0, #0xe10]
    // 0x1296230: StoreField: r1->field_2b = r0
    //     0x1296230: stur            w0, [x1, #0x2b]
    // 0x1296234: r0 = 2
    //     0x1296234: movz            x0, #0x2
    // 0x1296238: StoreField: r1->field_37 = r0
    //     0x1296238: stur            w0, [x1, #0x37]
    // 0x129623c: r0 = SizedBox()
    //     0x129623c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1296240: mov             x2, x0
    // 0x1296244: r0 = 150.000000
    //     0x1296244: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x1296248: ldr             x0, [x0, #0x690]
    // 0x129624c: stur            x2, [fp, #-0x20]
    // 0x1296250: StoreField: r2->field_f = r0
    //     0x1296250: stur            w0, [x2, #0xf]
    // 0x1296254: ldur            x0, [fp, #-0x40]
    // 0x1296258: StoreField: r2->field_b = r0
    //     0x1296258: stur            w0, [x2, #0xb]
    // 0x129625c: ldur            x1, [fp, #-0x18]
    // 0x1296260: r0 = of()
    //     0x1296260: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1296264: LoadField: r1 = r0->field_87
    //     0x1296264: ldur            w1, [x0, #0x87]
    // 0x1296268: DecompressPointer r1
    //     0x1296268: add             x1, x1, HEAP, lsl #32
    // 0x129626c: LoadField: r0 = r1->field_2b
    //     0x129626c: ldur            w0, [x1, #0x2b]
    // 0x1296270: DecompressPointer r0
    //     0x1296270: add             x0, x0, HEAP, lsl #32
    // 0x1296274: r16 = 12.000000
    //     0x1296274: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1296278: ldr             x16, [x16, #0x9e8]
    // 0x129627c: r30 = Instance_Color
    //     0x129627c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x1296280: ldr             lr, [lr, #0x858]
    // 0x1296284: stp             lr, x16, [SP]
    // 0x1296288: mov             x1, x0
    // 0x129628c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x129628c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1296290: ldr             x4, [x4, #0xaa0]
    // 0x1296294: r0 = copyWith()
    //     0x1296294: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296298: stur            x0, [fp, #-0x28]
    // 0x129629c: r0 = Text()
    //     0x129629c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12962a0: mov             x2, x0
    // 0x12962a4: r0 = "Free"
    //     0x12962a4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x12962a8: ldr             x0, [x0, #0x668]
    // 0x12962ac: stur            x2, [fp, #-0x40]
    // 0x12962b0: StoreField: r2->field_b = r0
    //     0x12962b0: stur            w0, [x2, #0xb]
    // 0x12962b4: ldur            x0, [fp, #-0x28]
    // 0x12962b8: StoreField: r2->field_13 = r0
    //     0x12962b8: stur            w0, [x2, #0x13]
    // 0x12962bc: ldur            x0, [fp, #-8]
    // 0x12962c0: cmp             w0, NULL
    // 0x12962c4: b.ne            #0x12962d0
    // 0x12962c8: r0 = Null
    //     0x12962c8: mov             x0, NULL
    // 0x12962cc: b               #0x12962dc
    // 0x12962d0: LoadField: r1 = r0->field_13
    //     0x12962d0: ldur            w1, [x0, #0x13]
    // 0x12962d4: DecompressPointer r1
    //     0x12962d4: add             x1, x1, HEAP, lsl #32
    // 0x12962d8: mov             x0, x1
    // 0x12962dc: cmp             w0, NULL
    // 0x12962e0: b.ne            #0x12962ec
    // 0x12962e4: r5 = ""
    //     0x12962e4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12962e8: b               #0x12962f0
    // 0x12962ec: mov             x5, x0
    // 0x12962f0: ldur            x4, [fp, #-0x10]
    // 0x12962f4: ldur            x3, [fp, #-0x30]
    // 0x12962f8: ldur            x0, [fp, #-0x20]
    // 0x12962fc: ldur            x1, [fp, #-0x18]
    // 0x1296300: stur            x5, [fp, #-8]
    // 0x1296304: r0 = of()
    //     0x1296304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1296308: LoadField: r1 = r0->field_87
    //     0x1296308: ldur            w1, [x0, #0x87]
    // 0x129630c: DecompressPointer r1
    //     0x129630c: add             x1, x1, HEAP, lsl #32
    // 0x1296310: LoadField: r0 = r1->field_2b
    //     0x1296310: ldur            w0, [x1, #0x2b]
    // 0x1296314: DecompressPointer r0
    //     0x1296314: add             x0, x0, HEAP, lsl #32
    // 0x1296318: r16 = 12.000000
    //     0x1296318: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x129631c: ldr             x16, [x16, #0x9e8]
    // 0x1296320: r30 = Instance_TextDecoration
    //     0x1296320: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x1296324: ldr             lr, [lr, #0xe30]
    // 0x1296328: stp             lr, x16, [SP]
    // 0x129632c: mov             x1, x0
    // 0x1296330: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x1296330: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x1296334: ldr             x4, [x4, #0x698]
    // 0x1296338: r0 = copyWith()
    //     0x1296338: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x129633c: stur            x0, [fp, #-0x18]
    // 0x1296340: r0 = Text()
    //     0x1296340: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296344: mov             x3, x0
    // 0x1296348: ldur            x0, [fp, #-8]
    // 0x129634c: stur            x3, [fp, #-0x28]
    // 0x1296350: StoreField: r3->field_b = r0
    //     0x1296350: stur            w0, [x3, #0xb]
    // 0x1296354: ldur            x0, [fp, #-0x18]
    // 0x1296358: StoreField: r3->field_13 = r0
    //     0x1296358: stur            w0, [x3, #0x13]
    // 0x129635c: r1 = Null
    //     0x129635c: mov             x1, NULL
    // 0x1296360: r2 = 6
    //     0x1296360: movz            x2, #0x6
    // 0x1296364: r0 = AllocateArray()
    //     0x1296364: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1296368: mov             x2, x0
    // 0x129636c: ldur            x0, [fp, #-0x40]
    // 0x1296370: stur            x2, [fp, #-8]
    // 0x1296374: StoreField: r2->field_f = r0
    //     0x1296374: stur            w0, [x2, #0xf]
    // 0x1296378: r16 = Instance_SizedBox
    //     0x1296378: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x129637c: ldr             x16, [x16, #0xa50]
    // 0x1296380: StoreField: r2->field_13 = r16
    //     0x1296380: stur            w16, [x2, #0x13]
    // 0x1296384: ldur            x0, [fp, #-0x28]
    // 0x1296388: ArrayStore: r2[0] = r0  ; List_4
    //     0x1296388: stur            w0, [x2, #0x17]
    // 0x129638c: r1 = <Widget>
    //     0x129638c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1296390: r0 = AllocateGrowableArray()
    //     0x1296390: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1296394: mov             x1, x0
    // 0x1296398: ldur            x0, [fp, #-8]
    // 0x129639c: stur            x1, [fp, #-0x18]
    // 0x12963a0: StoreField: r1->field_f = r0
    //     0x12963a0: stur            w0, [x1, #0xf]
    // 0x12963a4: r2 = 6
    //     0x12963a4: movz            x2, #0x6
    // 0x12963a8: StoreField: r1->field_b = r2
    //     0x12963a8: stur            w2, [x1, #0xb]
    // 0x12963ac: r0 = Row()
    //     0x12963ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12963b0: mov             x3, x0
    // 0x12963b4: r0 = Instance_Axis
    //     0x12963b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12963b8: stur            x3, [fp, #-8]
    // 0x12963bc: StoreField: r3->field_f = r0
    //     0x12963bc: stur            w0, [x3, #0xf]
    // 0x12963c0: r4 = Instance_MainAxisAlignment
    //     0x12963c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12963c4: ldr             x4, [x4, #0xa08]
    // 0x12963c8: StoreField: r3->field_13 = r4
    //     0x12963c8: stur            w4, [x3, #0x13]
    // 0x12963cc: r5 = Instance_MainAxisSize
    //     0x12963cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12963d0: ldr             x5, [x5, #0xa10]
    // 0x12963d4: ArrayStore: r3[0] = r5  ; List_4
    //     0x12963d4: stur            w5, [x3, #0x17]
    // 0x12963d8: r6 = Instance_CrossAxisAlignment
    //     0x12963d8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12963dc: ldr             x6, [x6, #0xa18]
    // 0x12963e0: StoreField: r3->field_1b = r6
    //     0x12963e0: stur            w6, [x3, #0x1b]
    // 0x12963e4: r7 = Instance_VerticalDirection
    //     0x12963e4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12963e8: ldr             x7, [x7, #0xa20]
    // 0x12963ec: StoreField: r3->field_23 = r7
    //     0x12963ec: stur            w7, [x3, #0x23]
    // 0x12963f0: r8 = Instance_Clip
    //     0x12963f0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12963f4: ldr             x8, [x8, #0x38]
    // 0x12963f8: StoreField: r3->field_2b = r8
    //     0x12963f8: stur            w8, [x3, #0x2b]
    // 0x12963fc: StoreField: r3->field_2f = rZR
    //     0x12963fc: stur            xzr, [x3, #0x2f]
    // 0x1296400: ldur            x1, [fp, #-0x18]
    // 0x1296404: StoreField: r3->field_b = r1
    //     0x1296404: stur            w1, [x3, #0xb]
    // 0x1296408: r1 = Null
    //     0x1296408: mov             x1, NULL
    // 0x129640c: r2 = 6
    //     0x129640c: movz            x2, #0x6
    // 0x1296410: r0 = AllocateArray()
    //     0x1296410: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1296414: mov             x2, x0
    // 0x1296418: ldur            x0, [fp, #-0x20]
    // 0x129641c: stur            x2, [fp, #-0x18]
    // 0x1296420: StoreField: r2->field_f = r0
    //     0x1296420: stur            w0, [x2, #0xf]
    // 0x1296424: r16 = Instance_SizedBox
    //     0x1296424: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1296428: ldr             x16, [x16, #0xc70]
    // 0x129642c: StoreField: r2->field_13 = r16
    //     0x129642c: stur            w16, [x2, #0x13]
    // 0x1296430: ldur            x0, [fp, #-8]
    // 0x1296434: ArrayStore: r2[0] = r0  ; List_4
    //     0x1296434: stur            w0, [x2, #0x17]
    // 0x1296438: r1 = <Widget>
    //     0x1296438: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129643c: r0 = AllocateGrowableArray()
    //     0x129643c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1296440: mov             x1, x0
    // 0x1296444: ldur            x0, [fp, #-0x18]
    // 0x1296448: stur            x1, [fp, #-8]
    // 0x129644c: StoreField: r1->field_f = r0
    //     0x129644c: stur            w0, [x1, #0xf]
    // 0x1296450: r2 = 6
    //     0x1296450: movz            x2, #0x6
    // 0x1296454: StoreField: r1->field_b = r2
    //     0x1296454: stur            w2, [x1, #0xb]
    // 0x1296458: r0 = Column()
    //     0x1296458: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x129645c: mov             x1, x0
    // 0x1296460: r0 = Instance_Axis
    //     0x1296460: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1296464: stur            x1, [fp, #-0x18]
    // 0x1296468: StoreField: r1->field_f = r0
    //     0x1296468: stur            w0, [x1, #0xf]
    // 0x129646c: r0 = Instance_MainAxisAlignment
    //     0x129646c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1296470: ldr             x0, [x0, #0xa08]
    // 0x1296474: StoreField: r1->field_13 = r0
    //     0x1296474: stur            w0, [x1, #0x13]
    // 0x1296478: r2 = Instance_MainAxisSize
    //     0x1296478: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x129647c: ldr             x2, [x2, #0xa10]
    // 0x1296480: ArrayStore: r1[0] = r2  ; List_4
    //     0x1296480: stur            w2, [x1, #0x17]
    // 0x1296484: r3 = Instance_CrossAxisAlignment
    //     0x1296484: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1296488: ldr             x3, [x3, #0x890]
    // 0x129648c: StoreField: r1->field_1b = r3
    //     0x129648c: stur            w3, [x1, #0x1b]
    // 0x1296490: r3 = Instance_VerticalDirection
    //     0x1296490: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1296494: ldr             x3, [x3, #0xa20]
    // 0x1296498: StoreField: r1->field_23 = r3
    //     0x1296498: stur            w3, [x1, #0x23]
    // 0x129649c: r4 = Instance_Clip
    //     0x129649c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12964a0: ldr             x4, [x4, #0x38]
    // 0x12964a4: StoreField: r1->field_2b = r4
    //     0x12964a4: stur            w4, [x1, #0x2b]
    // 0x12964a8: StoreField: r1->field_2f = rZR
    //     0x12964a8: stur            xzr, [x1, #0x2f]
    // 0x12964ac: ldur            x5, [fp, #-8]
    // 0x12964b0: StoreField: r1->field_b = r5
    //     0x12964b0: stur            w5, [x1, #0xb]
    // 0x12964b4: r0 = Padding()
    //     0x12964b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12964b8: mov             x2, x0
    // 0x12964bc: r0 = Instance_EdgeInsets
    //     0x12964bc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x12964c0: ldr             x0, [x0, #0xa78]
    // 0x12964c4: stur            x2, [fp, #-8]
    // 0x12964c8: StoreField: r2->field_f = r0
    //     0x12964c8: stur            w0, [x2, #0xf]
    // 0x12964cc: ldur            x0, [fp, #-0x18]
    // 0x12964d0: StoreField: r2->field_b = r0
    //     0x12964d0: stur            w0, [x2, #0xb]
    // 0x12964d4: r1 = <FlexParentData>
    //     0x12964d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x12964d8: ldr             x1, [x1, #0xe00]
    // 0x12964dc: r0 = Expanded()
    //     0x12964dc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x12964e0: mov             x3, x0
    // 0x12964e4: r0 = 1
    //     0x12964e4: movz            x0, #0x1
    // 0x12964e8: stur            x3, [fp, #-0x18]
    // 0x12964ec: StoreField: r3->field_13 = r0
    //     0x12964ec: stur            x0, [x3, #0x13]
    // 0x12964f0: r0 = Instance_FlexFit
    //     0x12964f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x12964f4: ldr             x0, [x0, #0xe08]
    // 0x12964f8: StoreField: r3->field_1b = r0
    //     0x12964f8: stur            w0, [x3, #0x1b]
    // 0x12964fc: ldur            x0, [fp, #-8]
    // 0x1296500: StoreField: r3->field_b = r0
    //     0x1296500: stur            w0, [x3, #0xb]
    // 0x1296504: r1 = Null
    //     0x1296504: mov             x1, NULL
    // 0x1296508: r2 = 6
    //     0x1296508: movz            x2, #0x6
    // 0x129650c: r0 = AllocateArray()
    //     0x129650c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1296510: mov             x2, x0
    // 0x1296514: ldur            x0, [fp, #-0x10]
    // 0x1296518: stur            x2, [fp, #-8]
    // 0x129651c: StoreField: r2->field_f = r0
    //     0x129651c: stur            w0, [x2, #0xf]
    // 0x1296520: ldur            x0, [fp, #-0x30]
    // 0x1296524: StoreField: r2->field_13 = r0
    //     0x1296524: stur            w0, [x2, #0x13]
    // 0x1296528: ldur            x0, [fp, #-0x18]
    // 0x129652c: ArrayStore: r2[0] = r0  ; List_4
    //     0x129652c: stur            w0, [x2, #0x17]
    // 0x1296530: r1 = <Widget>
    //     0x1296530: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1296534: r0 = AllocateGrowableArray()
    //     0x1296534: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1296538: mov             x1, x0
    // 0x129653c: ldur            x0, [fp, #-8]
    // 0x1296540: stur            x1, [fp, #-0x10]
    // 0x1296544: StoreField: r1->field_f = r0
    //     0x1296544: stur            w0, [x1, #0xf]
    // 0x1296548: r0 = 6
    //     0x1296548: movz            x0, #0x6
    // 0x129654c: StoreField: r1->field_b = r0
    //     0x129654c: stur            w0, [x1, #0xb]
    // 0x1296550: r0 = Row()
    //     0x1296550: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1296554: mov             x1, x0
    // 0x1296558: r0 = Instance_Axis
    //     0x1296558: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x129655c: stur            x1, [fp, #-8]
    // 0x1296560: StoreField: r1->field_f = r0
    //     0x1296560: stur            w0, [x1, #0xf]
    // 0x1296564: r0 = Instance_MainAxisAlignment
    //     0x1296564: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1296568: ldr             x0, [x0, #0xa08]
    // 0x129656c: StoreField: r1->field_13 = r0
    //     0x129656c: stur            w0, [x1, #0x13]
    // 0x1296570: r0 = Instance_MainAxisSize
    //     0x1296570: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1296574: ldr             x0, [x0, #0xa10]
    // 0x1296578: ArrayStore: r1[0] = r0  ; List_4
    //     0x1296578: stur            w0, [x1, #0x17]
    // 0x129657c: r0 = Instance_CrossAxisAlignment
    //     0x129657c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1296580: ldr             x0, [x0, #0xa18]
    // 0x1296584: StoreField: r1->field_1b = r0
    //     0x1296584: stur            w0, [x1, #0x1b]
    // 0x1296588: r0 = Instance_VerticalDirection
    //     0x1296588: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x129658c: ldr             x0, [x0, #0xa20]
    // 0x1296590: StoreField: r1->field_23 = r0
    //     0x1296590: stur            w0, [x1, #0x23]
    // 0x1296594: r0 = Instance_Clip
    //     0x1296594: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1296598: ldr             x0, [x0, #0x38]
    // 0x129659c: StoreField: r1->field_2b = r0
    //     0x129659c: stur            w0, [x1, #0x2b]
    // 0x12965a0: StoreField: r1->field_2f = rZR
    //     0x12965a0: stur            xzr, [x1, #0x2f]
    // 0x12965a4: ldur            x0, [fp, #-0x10]
    // 0x12965a8: StoreField: r1->field_b = r0
    //     0x12965a8: stur            w0, [x1, #0xb]
    // 0x12965ac: r0 = Container()
    //     0x12965ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12965b0: stur            x0, [fp, #-0x10]
    // 0x12965b4: ldur            x16, [fp, #-0x38]
    // 0x12965b8: ldur            lr, [fp, #-8]
    // 0x12965bc: stp             lr, x16, [SP]
    // 0x12965c0: mov             x1, x0
    // 0x12965c4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x12965c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x12965c8: ldr             x4, [x4, #0x88]
    // 0x12965cc: r0 = Container()
    //     0x12965cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12965d0: r0 = Padding()
    //     0x12965d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12965d4: r1 = Instance_EdgeInsets
    //     0x12965d4: add             x1, PP, #0x48, lsl #12  ; [pp+0x485e0] Obj!EdgeInsets@d59901
    //     0x12965d8: ldr             x1, [x1, #0x5e0]
    // 0x12965dc: StoreField: r0->field_f = r1
    //     0x12965dc: stur            w1, [x0, #0xf]
    // 0x12965e0: ldur            x1, [fp, #-0x10]
    // 0x12965e4: StoreField: r0->field_b = r1
    //     0x12965e4: stur            w1, [x0, #0xb]
    // 0x12965e8: LeaveFrame
    //     0x12965e8: mov             SP, fp
    //     0x12965ec: ldp             fp, lr, [SP], #0x10
    // 0x12965f0: ret
    //     0x12965f0: ret             
    // 0x12965f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12965f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12965f8: b               #0x1295f58
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0x12965fc, size: 0x234
    // 0x12965fc: EnterFrame
    //     0x12965fc: stp             fp, lr, [SP, #-0x10]!
    //     0x1296600: mov             fp, SP
    // 0x1296604: AllocStack(0x28)
    //     0x1296604: sub             SP, SP, #0x28
    // 0x1296608: SetupParameters(BagBottomSheet this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x1296608: mov             x0, x1
    //     0x129660c: mov             x1, x2
    //     0x1296610: stur            x2, [fp, #-8]
    // 0x1296614: CheckStackOverflow
    //     0x1296614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1296618: cmp             SP, x16
    //     0x129661c: b.ls            #0x1296828
    // 0x1296620: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1296620: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1296624: ldr             x0, [x0, #0x1ab0]
    //     0x1296628: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x129662c: cmp             w0, w16
    //     0x1296630: b.ne            #0x1296640
    //     0x1296634: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x1296638: ldr             x2, [x2, #0x60]
    //     0x129663c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1296640: LoadField: r1 = r0->field_87
    //     0x1296640: ldur            w1, [x0, #0x87]
    // 0x1296644: DecompressPointer r1
    //     0x1296644: add             x1, x1, HEAP, lsl #32
    // 0x1296648: LoadField: r0 = r1->field_7
    //     0x1296648: ldur            w0, [x1, #7]
    // 0x129664c: DecompressPointer r0
    //     0x129664c: add             x0, x0, HEAP, lsl #32
    // 0x1296650: r16 = Instance_Color
    //     0x1296650: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1296654: r30 = 16.000000
    //     0x1296654: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1296658: ldr             lr, [lr, #0x188]
    // 0x129665c: stp             lr, x16, [SP]
    // 0x1296660: mov             x1, x0
    // 0x1296664: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1296664: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1296668: ldr             x4, [x4, #0x9b8]
    // 0x129666c: r0 = copyWith()
    //     0x129666c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1296670: stur            x0, [fp, #-0x10]
    // 0x1296674: r0 = Text()
    //     0x1296674: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1296678: mov             x2, x0
    // 0x129667c: r0 = "Bag"
    //     0x129667c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0x1296680: ldr             x0, [x0, #0xd60]
    // 0x1296684: stur            x2, [fp, #-0x18]
    // 0x1296688: StoreField: r2->field_b = r0
    //     0x1296688: stur            w0, [x2, #0xb]
    // 0x129668c: ldur            x0, [fp, #-0x10]
    // 0x1296690: StoreField: r2->field_13 = r0
    //     0x1296690: stur            w0, [x2, #0x13]
    // 0x1296694: ldur            x1, [fp, #-8]
    // 0x1296698: r0 = of()
    //     0x1296698: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x129669c: LoadField: r1 = r0->field_5b
    //     0x129669c: ldur            w1, [x0, #0x5b]
    // 0x12966a0: DecompressPointer r1
    //     0x12966a0: add             x1, x1, HEAP, lsl #32
    // 0x12966a4: stur            x1, [fp, #-8]
    // 0x12966a8: r0 = ColorFilter()
    //     0x12966a8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x12966ac: mov             x1, x0
    // 0x12966b0: ldur            x0, [fp, #-8]
    // 0x12966b4: stur            x1, [fp, #-0x10]
    // 0x12966b8: StoreField: r1->field_7 = r0
    //     0x12966b8: stur            w0, [x1, #7]
    // 0x12966bc: r0 = Instance_BlendMode
    //     0x12966bc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x12966c0: ldr             x0, [x0, #0xb30]
    // 0x12966c4: StoreField: r1->field_b = r0
    //     0x12966c4: stur            w0, [x1, #0xb]
    // 0x12966c8: r0 = 1
    //     0x12966c8: movz            x0, #0x1
    // 0x12966cc: StoreField: r1->field_13 = r0
    //     0x12966cc: stur            x0, [x1, #0x13]
    // 0x12966d0: r0 = SvgPicture()
    //     0x12966d0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x12966d4: stur            x0, [fp, #-8]
    // 0x12966d8: r16 = Instance_BoxFit
    //     0x12966d8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x12966dc: ldr             x16, [x16, #0xb18]
    // 0x12966e0: ldur            lr, [fp, #-0x10]
    // 0x12966e4: stp             lr, x16, [SP]
    // 0x12966e8: mov             x1, x0
    // 0x12966ec: r2 = "assets/images/x.svg"
    //     0x12966ec: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0x12966f0: ldr             x2, [x2, #0x5e8]
    // 0x12966f4: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x12966f4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x12966f8: ldr             x4, [x4, #0x820]
    // 0x12966fc: r0 = SvgPicture.asset()
    //     0x12966fc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1296700: r0 = InkWell()
    //     0x1296700: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1296704: mov             x3, x0
    // 0x1296708: ldur            x0, [fp, #-8]
    // 0x129670c: stur            x3, [fp, #-0x10]
    // 0x1296710: StoreField: r3->field_b = r0
    //     0x1296710: stur            w0, [x3, #0xb]
    // 0x1296714: r1 = Function '<anonymous closure>':.
    //     0x1296714: add             x1, PP, #0x48, lsl #12  ; [pp+0x485f0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1296718: ldr             x1, [x1, #0x5f0]
    // 0x129671c: r2 = Null
    //     0x129671c: mov             x2, NULL
    // 0x1296720: r0 = AllocateClosure()
    //     0x1296720: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1296724: mov             x1, x0
    // 0x1296728: ldur            x0, [fp, #-0x10]
    // 0x129672c: StoreField: r0->field_f = r1
    //     0x129672c: stur            w1, [x0, #0xf]
    // 0x1296730: r1 = true
    //     0x1296730: add             x1, NULL, #0x20  ; true
    // 0x1296734: StoreField: r0->field_43 = r1
    //     0x1296734: stur            w1, [x0, #0x43]
    // 0x1296738: r2 = Instance_BoxShape
    //     0x1296738: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x129673c: ldr             x2, [x2, #0x80]
    // 0x1296740: StoreField: r0->field_47 = r2
    //     0x1296740: stur            w2, [x0, #0x47]
    // 0x1296744: StoreField: r0->field_6f = r1
    //     0x1296744: stur            w1, [x0, #0x6f]
    // 0x1296748: r2 = false
    //     0x1296748: add             x2, NULL, #0x30  ; false
    // 0x129674c: StoreField: r0->field_73 = r2
    //     0x129674c: stur            w2, [x0, #0x73]
    // 0x1296750: StoreField: r0->field_83 = r1
    //     0x1296750: stur            w1, [x0, #0x83]
    // 0x1296754: StoreField: r0->field_7b = r2
    //     0x1296754: stur            w2, [x0, #0x7b]
    // 0x1296758: r1 = Null
    //     0x1296758: mov             x1, NULL
    // 0x129675c: r2 = 6
    //     0x129675c: movz            x2, #0x6
    // 0x1296760: r0 = AllocateArray()
    //     0x1296760: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1296764: mov             x2, x0
    // 0x1296768: ldur            x0, [fp, #-0x18]
    // 0x129676c: stur            x2, [fp, #-8]
    // 0x1296770: StoreField: r2->field_f = r0
    //     0x1296770: stur            w0, [x2, #0xf]
    // 0x1296774: r16 = Instance_Spacer
    //     0x1296774: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1296778: ldr             x16, [x16, #0xf0]
    // 0x129677c: StoreField: r2->field_13 = r16
    //     0x129677c: stur            w16, [x2, #0x13]
    // 0x1296780: ldur            x0, [fp, #-0x10]
    // 0x1296784: ArrayStore: r2[0] = r0  ; List_4
    //     0x1296784: stur            w0, [x2, #0x17]
    // 0x1296788: r1 = <Widget>
    //     0x1296788: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129678c: r0 = AllocateGrowableArray()
    //     0x129678c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1296790: mov             x1, x0
    // 0x1296794: ldur            x0, [fp, #-8]
    // 0x1296798: stur            x1, [fp, #-0x10]
    // 0x129679c: StoreField: r1->field_f = r0
    //     0x129679c: stur            w0, [x1, #0xf]
    // 0x12967a0: r0 = 6
    //     0x12967a0: movz            x0, #0x6
    // 0x12967a4: StoreField: r1->field_b = r0
    //     0x12967a4: stur            w0, [x1, #0xb]
    // 0x12967a8: r0 = Row()
    //     0x12967a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12967ac: mov             x1, x0
    // 0x12967b0: r0 = Instance_Axis
    //     0x12967b0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12967b4: stur            x1, [fp, #-8]
    // 0x12967b8: StoreField: r1->field_f = r0
    //     0x12967b8: stur            w0, [x1, #0xf]
    // 0x12967bc: r0 = Instance_MainAxisAlignment
    //     0x12967bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12967c0: ldr             x0, [x0, #0xa08]
    // 0x12967c4: StoreField: r1->field_13 = r0
    //     0x12967c4: stur            w0, [x1, #0x13]
    // 0x12967c8: r0 = Instance_MainAxisSize
    //     0x12967c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12967cc: ldr             x0, [x0, #0xa10]
    // 0x12967d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x12967d0: stur            w0, [x1, #0x17]
    // 0x12967d4: r0 = Instance_CrossAxisAlignment
    //     0x12967d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12967d8: ldr             x0, [x0, #0xa18]
    // 0x12967dc: StoreField: r1->field_1b = r0
    //     0x12967dc: stur            w0, [x1, #0x1b]
    // 0x12967e0: r0 = Instance_VerticalDirection
    //     0x12967e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12967e4: ldr             x0, [x0, #0xa20]
    // 0x12967e8: StoreField: r1->field_23 = r0
    //     0x12967e8: stur            w0, [x1, #0x23]
    // 0x12967ec: r0 = Instance_Clip
    //     0x12967ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12967f0: ldr             x0, [x0, #0x38]
    // 0x12967f4: StoreField: r1->field_2b = r0
    //     0x12967f4: stur            w0, [x1, #0x2b]
    // 0x12967f8: StoreField: r1->field_2f = rZR
    //     0x12967f8: stur            xzr, [x1, #0x2f]
    // 0x12967fc: ldur            x0, [fp, #-0x10]
    // 0x1296800: StoreField: r1->field_b = r0
    //     0x1296800: stur            w0, [x1, #0xb]
    // 0x1296804: r0 = Padding()
    //     0x1296804: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1296808: r1 = Instance_EdgeInsets
    //     0x1296808: add             x1, PP, #0x48, lsl #12  ; [pp+0x485e0] Obj!EdgeInsets@d59901
    //     0x129680c: ldr             x1, [x1, #0x5e0]
    // 0x1296810: StoreField: r0->field_f = r1
    //     0x1296810: stur            w1, [x0, #0xf]
    // 0x1296814: ldur            x1, [fp, #-8]
    // 0x1296818: StoreField: r0->field_b = r1
    //     0x1296818: stur            w1, [x0, #0xb]
    // 0x129681c: LeaveFrame
    //     0x129681c: mov             SP, fp
    //     0x1296820: ldp             fp, lr, [SP], #0x10
    // 0x1296824: ret
    //     0x1296824: ret             
    // 0x1296828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1296828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129682c: b               #0x1296620
  }
}
