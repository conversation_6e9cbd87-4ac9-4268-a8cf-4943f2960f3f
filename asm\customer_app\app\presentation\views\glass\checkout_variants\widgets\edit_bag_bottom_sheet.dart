// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart

// class id: 1049371, size: 0x8
class :: {
}

// class id: 3360, size: 0x24, field offset: 0x14
class _EditBagBottomSheetState extends State<dynamic> {

  [closure] SkuDetails <anonymous closure>(dynamic) {
    // ** addr: 0x90644c, size: 0x18
    // 0x90644c: EnterFrame
    //     0x90644c: stp             fp, lr, [SP, #-0x10]!
    //     0x906450: mov             fp, SP
    // 0x906454: r0 = SkuDetails()
    //     0x906454: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0x906458: LeaveFrame
    //     0x906458: mov             SP, fp
    //     0x90645c: ldp             fp, lr, [SP], #0x10
    // 0x906460: ret
    //     0x906460: ret             
  }
  [closure] bool <anonymous closure>(dynamic, SkuDetails) {
    // ** addr: 0x906488, size: 0x90
    // 0x906488: EnterFrame
    //     0x906488: stp             fp, lr, [SP, #-0x10]!
    //     0x90648c: mov             fp, SP
    // 0x906490: AllocStack(0x18)
    //     0x906490: sub             SP, SP, #0x18
    // 0x906494: SetupParameters()
    //     0x906494: ldr             x0, [fp, #0x18]
    //     0x906498: ldur            w1, [x0, #0x17]
    //     0x90649c: add             x1, x1, HEAP, lsl #32
    // 0x9064a0: CheckStackOverflow
    //     0x9064a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9064a4: cmp             SP, x16
    //     0x9064a8: b.ls            #0x906510
    // 0x9064ac: ldr             x0, [fp, #0x10]
    // 0x9064b0: LoadField: r2 = r0->field_7
    //     0x9064b0: ldur            w2, [x0, #7]
    // 0x9064b4: DecompressPointer r2
    //     0x9064b4: add             x2, x2, HEAP, lsl #32
    // 0x9064b8: stur            x2, [fp, #-8]
    // 0x9064bc: LoadField: r0 = r1->field_f
    //     0x9064bc: ldur            w0, [x1, #0xf]
    // 0x9064c0: DecompressPointer r0
    //     0x9064c0: add             x0, x0, HEAP, lsl #32
    // 0x9064c4: LoadField: r1 = r0->field_1f
    //     0x9064c4: ldur            w1, [x0, #0x1f]
    // 0x9064c8: DecompressPointer r1
    //     0x9064c8: add             x1, x1, HEAP, lsl #32
    // 0x9064cc: LoadField: r0 = r1->field_8f
    //     0x9064cc: ldur            w0, [x1, #0x8f]
    // 0x9064d0: DecompressPointer r0
    //     0x9064d0: add             x0, x0, HEAP, lsl #32
    // 0x9064d4: mov             x1, x0
    // 0x9064d8: r0 = value()
    //     0x9064d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9064dc: LoadField: r1 = r0->field_b
    //     0x9064dc: ldur            w1, [x0, #0xb]
    // 0x9064e0: DecompressPointer r1
    //     0x9064e0: add             x1, x1, HEAP, lsl #32
    // 0x9064e4: ldur            x0, [fp, #-8]
    // 0x9064e8: r2 = LoadClassIdInstr(r0)
    //     0x9064e8: ldur            x2, [x0, #-1]
    //     0x9064ec: ubfx            x2, x2, #0xc, #0x14
    // 0x9064f0: stp             x1, x0, [SP]
    // 0x9064f4: mov             x0, x2
    // 0x9064f8: mov             lr, x0
    // 0x9064fc: ldr             lr, [x21, lr, lsl #3]
    // 0x906500: blr             lr
    // 0x906504: LeaveFrame
    //     0x906504: mov             SP, fp
    //     0x906508: ldp             fp, lr, [SP], #0x10
    // 0x90650c: ret
    //     0x90650c: ret             
    // 0x906510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x906510: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x906514: b               #0x9064ac
  }
  _ initState(/* No info */) {
    // ** addr: 0x9408ac, size: 0x26c
    // 0x9408ac: EnterFrame
    //     0x9408ac: stp             fp, lr, [SP, #-0x10]!
    //     0x9408b0: mov             fp, SP
    // 0x9408b4: AllocStack(0x28)
    //     0x9408b4: sub             SP, SP, #0x28
    // 0x9408b8: SetupParameters(_EditBagBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x9408b8: stur            x1, [fp, #-8]
    // 0x9408bc: CheckStackOverflow
    //     0x9408bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9408c0: cmp             SP, x16
    //     0x9408c4: b.ls            #0x940b0c
    // 0x9408c8: r1 = 1
    //     0x9408c8: movz            x1, #0x1
    // 0x9408cc: r0 = AllocateContext()
    //     0x9408cc: bl              #0x16f6108  ; AllocateContextStub
    // 0x9408d0: mov             x2, x0
    // 0x9408d4: ldur            x0, [fp, #-8]
    // 0x9408d8: stur            x2, [fp, #-0x10]
    // 0x9408dc: StoreField: r2->field_f = r0
    //     0x9408dc: stur            w0, [x2, #0xf]
    // 0x9408e0: LoadField: r1 = r0->field_1f
    //     0x9408e0: ldur            w1, [x0, #0x1f]
    // 0x9408e4: DecompressPointer r1
    //     0x9408e4: add             x1, x1, HEAP, lsl #32
    // 0x9408e8: LoadField: r3 = r1->field_8f
    //     0x9408e8: ldur            w3, [x1, #0x8f]
    // 0x9408ec: DecompressPointer r3
    //     0x9408ec: add             x3, x3, HEAP, lsl #32
    // 0x9408f0: mov             x1, x3
    // 0x9408f4: r0 = value()
    //     0x9408f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x9408f8: LoadField: r3 = r0->field_53
    //     0x9408f8: ldur            w3, [x0, #0x53]
    // 0x9408fc: DecompressPointer r3
    //     0x9408fc: add             x3, x3, HEAP, lsl #32
    // 0x940900: stur            x3, [fp, #-0x18]
    // 0x940904: cmp             w3, NULL
    // 0x940908: b.ne            #0x940914
    // 0x94090c: r2 = Null
    //     0x94090c: mov             x2, NULL
    // 0x940910: b               #0x940954
    // 0x940914: ldur            x2, [fp, #-0x10]
    // 0x940918: r1 = Function '<anonymous closure>':.
    //     0x940918: add             x1, PP, #0x56, lsl #12  ; [pp+0x56db0] AnonymousClosure: (0x906488), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::initState (0x9408ac)
    //     0x94091c: ldr             x1, [x1, #0xdb0]
    // 0x940920: r0 = AllocateClosure()
    //     0x940920: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940924: r1 = Function '<anonymous closure>':.
    //     0x940924: add             x1, PP, #0x56, lsl #12  ; [pp+0x56db8] AnonymousClosure: (0x90644c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::initState (0x9408ac)
    //     0x940928: ldr             x1, [x1, #0xdb8]
    // 0x94092c: r2 = Null
    //     0x94092c: mov             x2, NULL
    // 0x940930: stur            x0, [fp, #-0x10]
    // 0x940934: r0 = AllocateClosure()
    //     0x940934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940938: str             x0, [SP]
    // 0x94093c: ldur            x1, [fp, #-0x18]
    // 0x940940: ldur            x2, [fp, #-0x10]
    // 0x940944: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x940944: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x940948: ldr             x4, [x4, #0xb48]
    // 0x94094c: r0 = firstWhere()
    //     0x94094c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x940950: mov             x2, x0
    // 0x940954: ldur            x0, [fp, #-8]
    // 0x940958: stur            x2, [fp, #-0x10]
    // 0x94095c: LoadField: r1 = r0->field_1f
    //     0x94095c: ldur            w1, [x0, #0x1f]
    // 0x940960: DecompressPointer r1
    //     0x940960: add             x1, x1, HEAP, lsl #32
    // 0x940964: LoadField: r3 = r1->field_8f
    //     0x940964: ldur            w3, [x1, #0x8f]
    // 0x940968: DecompressPointer r3
    //     0x940968: add             x3, x3, HEAP, lsl #32
    // 0x94096c: mov             x1, x3
    // 0x940970: r0 = value()
    //     0x940970: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x940974: LoadField: r1 = r0->field_53
    //     0x940974: ldur            w1, [x0, #0x53]
    // 0x940978: DecompressPointer r1
    //     0x940978: add             x1, x1, HEAP, lsl #32
    // 0x94097c: stur            x1, [fp, #-0x18]
    // 0x940980: cmp             w1, NULL
    // 0x940984: b.ne            #0x940990
    // 0x940988: r0 = Null
    //     0x940988: mov             x0, NULL
    // 0x94098c: b               #0x9409d0
    // 0x940990: ldur            x0, [fp, #-0x10]
    // 0x940994: cmp             w0, NULL
    // 0x940998: b.ne            #0x9409a8
    // 0x94099c: r0 = SkuDetails()
    //     0x94099c: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0x9409a0: mov             x2, x0
    // 0x9409a4: b               #0x9409ac
    // 0x9409a8: mov             x2, x0
    // 0x9409ac: ldur            x1, [fp, #-0x18]
    // 0x9409b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9409b0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9409b4: r0 = indexOf()
    //     0x9409b4: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x9409b8: mov             x2, x0
    // 0x9409bc: r0 = BoxInt64Instr(r2)
    //     0x9409bc: sbfiz           x0, x2, #1, #0x1f
    //     0x9409c0: cmp             x2, x0, asr #1
    //     0x9409c4: b.eq            #0x9409d0
    //     0x9409c8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9409cc: stur            x2, [x0, #7]
    // 0x9409d0: cmp             w0, NULL
    // 0x9409d4: b.ne            #0x9409e0
    // 0x9409d8: r1 = 0
    //     0x9409d8: movz            x1, #0
    // 0x9409dc: b               #0x9409ec
    // 0x9409e0: r1 = LoadInt32Instr(r0)
    //     0x9409e0: sbfx            x1, x0, #1, #0x1f
    //     0x9409e4: tbz             w0, #0, #0x9409ec
    //     0x9409e8: ldur            x1, [x0, #7]
    // 0x9409ec: ldur            x0, [fp, #-8]
    // 0x9409f0: ArrayStore: r0[0] = r1  ; List_8
    //     0x9409f0: stur            x1, [x0, #0x17]
    // 0x9409f4: LoadField: r2 = r0->field_13
    //     0x9409f4: ldur            w2, [x0, #0x13]
    // 0x9409f8: DecompressPointer r2
    //     0x9409f8: add             x2, x2, HEAP, lsl #32
    // 0x9409fc: stur            x2, [fp, #-0x10]
    // 0x940a00: LoadField: r1 = r0->field_1f
    //     0x940a00: ldur            w1, [x0, #0x1f]
    // 0x940a04: DecompressPointer r1
    //     0x940a04: add             x1, x1, HEAP, lsl #32
    // 0x940a08: LoadField: r3 = r1->field_8f
    //     0x940a08: ldur            w3, [x1, #0x8f]
    // 0x940a0c: DecompressPointer r3
    //     0x940a0c: add             x3, x3, HEAP, lsl #32
    // 0x940a10: mov             x1, x3
    // 0x940a14: r0 = value()
    //     0x940a14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x940a18: LoadField: r2 = r0->field_53
    //     0x940a18: ldur            w2, [x0, #0x53]
    // 0x940a1c: DecompressPointer r2
    //     0x940a1c: add             x2, x2, HEAP, lsl #32
    // 0x940a20: cmp             w2, NULL
    // 0x940a24: b.ne            #0x940a30
    // 0x940a28: r0 = Null
    //     0x940a28: mov             x0, NULL
    // 0x940a2c: b               #0x940a68
    // 0x940a30: ldur            x0, [fp, #-8]
    // 0x940a34: ArrayLoad: r3 = r0[0]  ; List_8
    //     0x940a34: ldur            x3, [x0, #0x17]
    // 0x940a38: LoadField: r0 = r2->field_b
    //     0x940a38: ldur            w0, [x2, #0xb]
    // 0x940a3c: r1 = LoadInt32Instr(r0)
    //     0x940a3c: sbfx            x1, x0, #1, #0x1f
    // 0x940a40: mov             x0, x1
    // 0x940a44: mov             x1, x3
    // 0x940a48: cmp             x1, x0
    // 0x940a4c: b.hs            #0x940b14
    // 0x940a50: LoadField: r0 = r2->field_f
    //     0x940a50: ldur            w0, [x2, #0xf]
    // 0x940a54: DecompressPointer r0
    //     0x940a54: add             x0, x0, HEAP, lsl #32
    // 0x940a58: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x940a58: add             x16, x0, x3, lsl #2
    //     0x940a5c: ldur            w1, [x16, #0xf]
    // 0x940a60: DecompressPointer r1
    //     0x940a60: add             x1, x1, HEAP, lsl #32
    // 0x940a64: mov             x0, x1
    // 0x940a68: cmp             w0, NULL
    // 0x940a6c: b.ne            #0x940a7c
    // 0x940a70: r0 = SkuDetails()
    //     0x940a70: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0x940a74: mov             x2, x0
    // 0x940a78: b               #0x940a80
    // 0x940a7c: mov             x2, x0
    // 0x940a80: ldur            x0, [fp, #-0x10]
    // 0x940a84: stur            x2, [fp, #-8]
    // 0x940a88: LoadField: r1 = r0->field_b
    //     0x940a88: ldur            w1, [x0, #0xb]
    // 0x940a8c: LoadField: r3 = r0->field_f
    //     0x940a8c: ldur            w3, [x0, #0xf]
    // 0x940a90: DecompressPointer r3
    //     0x940a90: add             x3, x3, HEAP, lsl #32
    // 0x940a94: LoadField: r4 = r3->field_b
    //     0x940a94: ldur            w4, [x3, #0xb]
    // 0x940a98: r3 = LoadInt32Instr(r1)
    //     0x940a98: sbfx            x3, x1, #1, #0x1f
    // 0x940a9c: stur            x3, [fp, #-0x20]
    // 0x940aa0: r1 = LoadInt32Instr(r4)
    //     0x940aa0: sbfx            x1, x4, #1, #0x1f
    // 0x940aa4: cmp             x3, x1
    // 0x940aa8: b.ne            #0x940ab4
    // 0x940aac: mov             x1, x0
    // 0x940ab0: r0 = _growToNextCapacity()
    //     0x940ab0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x940ab4: ldur            x2, [fp, #-0x10]
    // 0x940ab8: ldur            x3, [fp, #-0x20]
    // 0x940abc: add             x4, x3, #1
    // 0x940ac0: lsl             x5, x4, #1
    // 0x940ac4: StoreField: r2->field_b = r5
    //     0x940ac4: stur            w5, [x2, #0xb]
    // 0x940ac8: LoadField: r1 = r2->field_f
    //     0x940ac8: ldur            w1, [x2, #0xf]
    // 0x940acc: DecompressPointer r1
    //     0x940acc: add             x1, x1, HEAP, lsl #32
    // 0x940ad0: ldur            x0, [fp, #-8]
    // 0x940ad4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x940ad4: add             x25, x1, x3, lsl #2
    //     0x940ad8: add             x25, x25, #0xf
    //     0x940adc: str             w0, [x25]
    //     0x940ae0: tbz             w0, #0, #0x940afc
    //     0x940ae4: ldurb           w16, [x1, #-1]
    //     0x940ae8: ldurb           w17, [x0, #-1]
    //     0x940aec: and             x16, x17, x16, lsr #2
    //     0x940af0: tst             x16, HEAP, lsr #32
    //     0x940af4: b.eq            #0x940afc
    //     0x940af8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x940afc: r0 = Null
    //     0x940afc: mov             x0, NULL
    // 0x940b00: LeaveFrame
    //     0x940b00: mov             SP, fp
    //     0x940b04: ldp             fp, lr, [SP], #0x10
    // 0x940b08: ret
    //     0x940b08: ret             
    // 0x940b0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x940b0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940b10: b               #0x9408c8
    // 0x940b14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x940b14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1a54c, size: 0x1cc
    // 0xa1a54c: EnterFrame
    //     0xa1a54c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1a550: mov             fp, SP
    // 0xa1a554: AllocStack(0x10)
    //     0xa1a554: sub             SP, SP, #0x10
    // 0xa1a558: SetupParameters()
    //     0xa1a558: ldr             x0, [fp, #0x10]
    //     0xa1a55c: ldur            w2, [x0, #0x17]
    //     0xa1a560: add             x2, x2, HEAP, lsl #32
    //     0xa1a564: stur            x2, [fp, #-8]
    // 0xa1a568: CheckStackOverflow
    //     0xa1a568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1a56c: cmp             SP, x16
    //     0xa1a570: b.ls            #0xa1a710
    // 0xa1a574: LoadField: r0 = r2->field_f
    //     0xa1a574: ldur            w0, [x2, #0xf]
    // 0xa1a578: DecompressPointer r0
    //     0xa1a578: add             x0, x0, HEAP, lsl #32
    // 0xa1a57c: LoadField: r1 = r0->field_1f
    //     0xa1a57c: ldur            w1, [x0, #0x1f]
    // 0xa1a580: DecompressPointer r1
    //     0xa1a580: add             x1, x1, HEAP, lsl #32
    // 0xa1a584: r17 = 287
    //     0xa1a584: movz            x17, #0x11f
    // 0xa1a588: ldr             w0, [x1, x17]
    // 0xa1a58c: DecompressPointer r0
    //     0xa1a58c: add             x0, x0, HEAP, lsl #32
    // 0xa1a590: mov             x1, x0
    // 0xa1a594: r0 = value()
    //     0xa1a594: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a598: cmp             w0, NULL
    // 0xa1a59c: b.ne            #0xa1a5a8
    // 0xa1a5a0: r0 = 0
    //     0xa1a5a0: movz            x0, #0
    // 0xa1a5a4: b               #0xa1a5b8
    // 0xa1a5a8: r1 = LoadInt32Instr(r0)
    //     0xa1a5a8: sbfx            x1, x0, #1, #0x1f
    //     0xa1a5ac: tbz             w0, #0, #0xa1a5b4
    //     0xa1a5b0: ldur            x1, [x0, #7]
    // 0xa1a5b4: mov             x0, x1
    // 0xa1a5b8: cmp             x0, #5
    // 0xa1a5bc: b.ge            #0xa1a700
    // 0xa1a5c0: ldur            x0, [fp, #-8]
    // 0xa1a5c4: LoadField: r1 = r0->field_f
    //     0xa1a5c4: ldur            w1, [x0, #0xf]
    // 0xa1a5c8: DecompressPointer r1
    //     0xa1a5c8: add             x1, x1, HEAP, lsl #32
    // 0xa1a5cc: LoadField: r2 = r1->field_1f
    //     0xa1a5cc: ldur            w2, [x1, #0x1f]
    // 0xa1a5d0: DecompressPointer r2
    //     0xa1a5d0: add             x2, x2, HEAP, lsl #32
    // 0xa1a5d4: LoadField: r1 = r2->field_93
    //     0xa1a5d4: ldur            w1, [x2, #0x93]
    // 0xa1a5d8: DecompressPointer r1
    //     0xa1a5d8: add             x1, x1, HEAP, lsl #32
    // 0xa1a5dc: r0 = value()
    //     0xa1a5dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a5e0: LoadField: r1 = r0->field_b
    //     0xa1a5e0: ldur            w1, [x0, #0xb]
    // 0xa1a5e4: DecompressPointer r1
    //     0xa1a5e4: add             x1, x1, HEAP, lsl #32
    // 0xa1a5e8: cmp             w1, NULL
    // 0xa1a5ec: b.ne            #0xa1a5f8
    // 0xa1a5f0: r0 = Null
    //     0xa1a5f0: mov             x0, NULL
    // 0xa1a5f4: b               #0xa1a600
    // 0xa1a5f8: LoadField: r0 = r1->field_23
    //     0xa1a5f8: ldur            w0, [x1, #0x23]
    // 0xa1a5fc: DecompressPointer r0
    //     0xa1a5fc: add             x0, x0, HEAP, lsl #32
    // 0xa1a600: cmp             w0, NULL
    // 0xa1a604: b.ne            #0xa1a610
    // 0xa1a608: r0 = 0
    //     0xa1a608: movz            x0, #0
    // 0xa1a60c: b               #0xa1a620
    // 0xa1a610: r1 = LoadInt32Instr(r0)
    //     0xa1a610: sbfx            x1, x0, #1, #0x1f
    //     0xa1a614: tbz             w0, #0, #0xa1a61c
    //     0xa1a618: ldur            x1, [x0, #7]
    // 0xa1a61c: mov             x0, x1
    // 0xa1a620: cmp             x0, #1
    // 0xa1a624: b.lt            #0xa1a700
    // 0xa1a628: ldur            x0, [fp, #-8]
    // 0xa1a62c: LoadField: r1 = r0->field_f
    //     0xa1a62c: ldur            w1, [x0, #0xf]
    // 0xa1a630: DecompressPointer r1
    //     0xa1a630: add             x1, x1, HEAP, lsl #32
    // 0xa1a634: LoadField: r2 = r1->field_1f
    //     0xa1a634: ldur            w2, [x1, #0x1f]
    // 0xa1a638: DecompressPointer r2
    //     0xa1a638: add             x2, x2, HEAP, lsl #32
    // 0xa1a63c: LoadField: r1 = r2->field_93
    //     0xa1a63c: ldur            w1, [x2, #0x93]
    // 0xa1a640: DecompressPointer r1
    //     0xa1a640: add             x1, x1, HEAP, lsl #32
    // 0xa1a644: r0 = value()
    //     0xa1a644: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a648: LoadField: r1 = r0->field_b
    //     0xa1a648: ldur            w1, [x0, #0xb]
    // 0xa1a64c: DecompressPointer r1
    //     0xa1a64c: add             x1, x1, HEAP, lsl #32
    // 0xa1a650: cmp             w1, NULL
    // 0xa1a654: b.ne            #0xa1a660
    // 0xa1a658: r0 = Null
    //     0xa1a658: mov             x0, NULL
    // 0xa1a65c: b               #0xa1a668
    // 0xa1a660: LoadField: r0 = r1->field_23
    //     0xa1a660: ldur            w0, [x1, #0x23]
    // 0xa1a664: DecompressPointer r0
    //     0xa1a664: add             x0, x0, HEAP, lsl #32
    // 0xa1a668: cmp             w0, NULL
    // 0xa1a66c: b.ne            #0xa1a678
    // 0xa1a670: r2 = 0
    //     0xa1a670: movz            x2, #0
    // 0xa1a674: b               #0xa1a688
    // 0xa1a678: r1 = LoadInt32Instr(r0)
    //     0xa1a678: sbfx            x1, x0, #1, #0x1f
    //     0xa1a67c: tbz             w0, #0, #0xa1a684
    //     0xa1a680: ldur            x1, [x0, #7]
    // 0xa1a684: mov             x2, x1
    // 0xa1a688: ldur            x0, [fp, #-8]
    // 0xa1a68c: stur            x2, [fp, #-0x10]
    // 0xa1a690: LoadField: r1 = r0->field_f
    //     0xa1a690: ldur            w1, [x0, #0xf]
    // 0xa1a694: DecompressPointer r1
    //     0xa1a694: add             x1, x1, HEAP, lsl #32
    // 0xa1a698: LoadField: r3 = r1->field_1f
    //     0xa1a698: ldur            w3, [x1, #0x1f]
    // 0xa1a69c: DecompressPointer r3
    //     0xa1a69c: add             x3, x3, HEAP, lsl #32
    // 0xa1a6a0: r17 = 287
    //     0xa1a6a0: movz            x17, #0x11f
    // 0xa1a6a4: ldr             w1, [x3, x17]
    // 0xa1a6a8: DecompressPointer r1
    //     0xa1a6a8: add             x1, x1, HEAP, lsl #32
    // 0xa1a6ac: r0 = value()
    //     0xa1a6ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a6b0: cmp             w0, NULL
    // 0xa1a6b4: b.ne            #0xa1a6c0
    // 0xa1a6b8: r1 = 0
    //     0xa1a6b8: movz            x1, #0
    // 0xa1a6bc: b               #0xa1a6cc
    // 0xa1a6c0: r1 = LoadInt32Instr(r0)
    //     0xa1a6c0: sbfx            x1, x0, #1, #0x1f
    //     0xa1a6c4: tbz             w0, #0, #0xa1a6cc
    //     0xa1a6c8: ldur            x1, [x0, #7]
    // 0xa1a6cc: ldur            x0, [fp, #-0x10]
    // 0xa1a6d0: cmp             x0, x1
    // 0xa1a6d4: b.le            #0xa1a700
    // 0xa1a6d8: ldur            x0, [fp, #-8]
    // 0xa1a6dc: LoadField: r1 = r0->field_f
    //     0xa1a6dc: ldur            w1, [x0, #0xf]
    // 0xa1a6e0: DecompressPointer r1
    //     0xa1a6e0: add             x1, x1, HEAP, lsl #32
    // 0xa1a6e4: LoadField: r0 = r1->field_1f
    //     0xa1a6e4: ldur            w0, [x1, #0x1f]
    // 0xa1a6e8: DecompressPointer r0
    //     0xa1a6e8: add             x0, x0, HEAP, lsl #32
    // 0xa1a6ec: r17 = 259
    //     0xa1a6ec: movz            x17, #0x103
    // 0xa1a6f0: ldr             w2, [x0, x17]
    // 0xa1a6f4: DecompressPointer r2
    //     0xa1a6f4: add             x2, x2, HEAP, lsl #32
    // 0xa1a6f8: mov             x1, x0
    // 0xa1a6fc: r0 = addQuantity()
    //     0xa1a6fc: bl              #0xa1dd48  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::addQuantity
    // 0xa1a700: r0 = Null
    //     0xa1a700: mov             x0, NULL
    // 0xa1a704: LeaveFrame
    //     0xa1a704: mov             SP, fp
    //     0xa1a708: ldp             fp, lr, [SP], #0x10
    // 0xa1a70c: ret
    //     0xa1a70c: ret             
    // 0xa1a710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1a710: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1a714: b               #0xa1a574
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0xa1a718, size: 0x1bd8
    // 0xa1a718: EnterFrame
    //     0xa1a718: stp             fp, lr, [SP, #-0x10]!
    //     0xa1a71c: mov             fp, SP
    // 0xa1a720: AllocStack(0x98)
    //     0xa1a720: sub             SP, SP, #0x98
    // 0xa1a724: SetupParameters()
    //     0xa1a724: ldr             x0, [fp, #0x10]
    //     0xa1a728: ldur            w2, [x0, #0x17]
    //     0xa1a72c: add             x2, x2, HEAP, lsl #32
    //     0xa1a730: stur            x2, [fp, #-8]
    // 0xa1a734: CheckStackOverflow
    //     0xa1a734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1a738: cmp             SP, x16
    //     0xa1a73c: b.ls            #0xa1c2bc
    // 0xa1a740: r0 = Radius()
    //     0xa1a740: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa1a744: d0 = 12.000000
    //     0xa1a744: fmov            d0, #12.00000000
    // 0xa1a748: stur            x0, [fp, #-0x10]
    // 0xa1a74c: StoreField: r0->field_7 = d0
    //     0xa1a74c: stur            d0, [x0, #7]
    // 0xa1a750: StoreField: r0->field_f = d0
    //     0xa1a750: stur            d0, [x0, #0xf]
    // 0xa1a754: r0 = BorderRadius()
    //     0xa1a754: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa1a758: mov             x2, x0
    // 0xa1a75c: ldur            x0, [fp, #-0x10]
    // 0xa1a760: stur            x2, [fp, #-0x18]
    // 0xa1a764: StoreField: r2->field_7 = r0
    //     0xa1a764: stur            w0, [x2, #7]
    // 0xa1a768: StoreField: r2->field_b = r0
    //     0xa1a768: stur            w0, [x2, #0xb]
    // 0xa1a76c: StoreField: r2->field_f = r0
    //     0xa1a76c: stur            w0, [x2, #0xf]
    // 0xa1a770: StoreField: r2->field_13 = r0
    //     0xa1a770: stur            w0, [x2, #0x13]
    // 0xa1a774: ldur            x0, [fp, #-8]
    // 0xa1a778: LoadField: r1 = r0->field_f
    //     0xa1a778: ldur            w1, [x0, #0xf]
    // 0xa1a77c: DecompressPointer r1
    //     0xa1a77c: add             x1, x1, HEAP, lsl #32
    // 0xa1a780: LoadField: r3 = r1->field_1f
    //     0xa1a780: ldur            w3, [x1, #0x1f]
    // 0xa1a784: DecompressPointer r3
    //     0xa1a784: add             x3, x3, HEAP, lsl #32
    // 0xa1a788: LoadField: r1 = r3->field_8f
    //     0xa1a788: ldur            w1, [x3, #0x8f]
    // 0xa1a78c: DecompressPointer r1
    //     0xa1a78c: add             x1, x1, HEAP, lsl #32
    // 0xa1a790: r0 = value()
    //     0xa1a790: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a794: LoadField: r1 = r0->field_13
    //     0xa1a794: ldur            w1, [x0, #0x13]
    // 0xa1a798: DecompressPointer r1
    //     0xa1a798: add             x1, x1, HEAP, lsl #32
    // 0xa1a79c: cmp             w1, NULL
    // 0xa1a7a0: b.ne            #0xa1a7ac
    // 0xa1a7a4: r4 = ""
    //     0xa1a7a4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1a7a8: b               #0xa1a7b0
    // 0xa1a7ac: mov             x4, x1
    // 0xa1a7b0: ldur            x3, [fp, #-8]
    // 0xa1a7b4: ldur            x0, [fp, #-0x18]
    // 0xa1a7b8: stur            x4, [fp, #-0x10]
    // 0xa1a7bc: r1 = Function '<anonymous closure>':.
    //     0xa1a7bc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56cb8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa1a7c0: ldr             x1, [x1, #0xcb8]
    // 0xa1a7c4: r2 = Null
    //     0xa1a7c4: mov             x2, NULL
    // 0xa1a7c8: r0 = AllocateClosure()
    //     0xa1a7c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1a7cc: r1 = Function '<anonymous closure>':.
    //     0xa1a7cc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56cc0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa1a7d0: ldr             x1, [x1, #0xcc0]
    // 0xa1a7d4: r2 = Null
    //     0xa1a7d4: mov             x2, NULL
    // 0xa1a7d8: stur            x0, [fp, #-0x20]
    // 0xa1a7dc: r0 = AllocateClosure()
    //     0xa1a7dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1a7e0: stur            x0, [fp, #-0x28]
    // 0xa1a7e4: r0 = CachedNetworkImage()
    //     0xa1a7e4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa1a7e8: stur            x0, [fp, #-0x30]
    // 0xa1a7ec: r16 = 56.000000
    //     0xa1a7ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xa1a7f0: ldr             x16, [x16, #0xb78]
    // 0xa1a7f4: r30 = 56.000000
    //     0xa1a7f4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xa1a7f8: ldr             lr, [lr, #0xb78]
    // 0xa1a7fc: stp             lr, x16, [SP, #0x18]
    // 0xa1a800: r16 = Instance_BoxFit
    //     0xa1a800: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa1a804: ldr             x16, [x16, #0x118]
    // 0xa1a808: ldur            lr, [fp, #-0x20]
    // 0xa1a80c: stp             lr, x16, [SP, #8]
    // 0xa1a810: ldur            x16, [fp, #-0x28]
    // 0xa1a814: str             x16, [SP]
    // 0xa1a818: mov             x1, x0
    // 0xa1a81c: ldur            x2, [fp, #-0x10]
    // 0xa1a820: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xa1a820: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xa1a824: ldr             x4, [x4, #0xc28]
    // 0xa1a828: r0 = CachedNetworkImage()
    //     0xa1a828: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa1a82c: r0 = ClipRRect()
    //     0xa1a82c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa1a830: mov             x2, x0
    // 0xa1a834: ldur            x0, [fp, #-0x18]
    // 0xa1a838: stur            x2, [fp, #-0x10]
    // 0xa1a83c: StoreField: r2->field_f = r0
    //     0xa1a83c: stur            w0, [x2, #0xf]
    // 0xa1a840: r0 = Instance_Clip
    //     0xa1a840: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa1a844: ldr             x0, [x0, #0x138]
    // 0xa1a848: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1a848: stur            w0, [x2, #0x17]
    // 0xa1a84c: ldur            x0, [fp, #-0x30]
    // 0xa1a850: StoreField: r2->field_b = r0
    //     0xa1a850: stur            w0, [x2, #0xb]
    // 0xa1a854: ldur            x0, [fp, #-8]
    // 0xa1a858: LoadField: r1 = r0->field_f
    //     0xa1a858: ldur            w1, [x0, #0xf]
    // 0xa1a85c: DecompressPointer r1
    //     0xa1a85c: add             x1, x1, HEAP, lsl #32
    // 0xa1a860: LoadField: r3 = r1->field_1f
    //     0xa1a860: ldur            w3, [x1, #0x1f]
    // 0xa1a864: DecompressPointer r3
    //     0xa1a864: add             x3, x3, HEAP, lsl #32
    // 0xa1a868: LoadField: r1 = r3->field_8f
    //     0xa1a868: ldur            w1, [x3, #0x8f]
    // 0xa1a86c: DecompressPointer r1
    //     0xa1a86c: add             x1, x1, HEAP, lsl #32
    // 0xa1a870: r0 = value()
    //     0xa1a870: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a874: LoadField: r1 = r0->field_f
    //     0xa1a874: ldur            w1, [x0, #0xf]
    // 0xa1a878: DecompressPointer r1
    //     0xa1a878: add             x1, x1, HEAP, lsl #32
    // 0xa1a87c: cmp             w1, NULL
    // 0xa1a880: b.ne            #0xa1a888
    // 0xa1a884: r1 = ""
    //     0xa1a884: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1a888: ldur            x2, [fp, #-8]
    // 0xa1a88c: r0 = capitalizeFirstWord()
    //     0xa1a88c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa1a890: ldur            x2, [fp, #-8]
    // 0xa1a894: stur            x0, [fp, #-0x18]
    // 0xa1a898: LoadField: r1 = r2->field_13
    //     0xa1a898: ldur            w1, [x2, #0x13]
    // 0xa1a89c: DecompressPointer r1
    //     0xa1a89c: add             x1, x1, HEAP, lsl #32
    // 0xa1a8a0: r0 = of()
    //     0xa1a8a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1a8a4: LoadField: r1 = r0->field_87
    //     0xa1a8a4: ldur            w1, [x0, #0x87]
    // 0xa1a8a8: DecompressPointer r1
    //     0xa1a8a8: add             x1, x1, HEAP, lsl #32
    // 0xa1a8ac: LoadField: r0 = r1->field_7
    //     0xa1a8ac: ldur            w0, [x1, #7]
    // 0xa1a8b0: DecompressPointer r0
    //     0xa1a8b0: add             x0, x0, HEAP, lsl #32
    // 0xa1a8b4: r16 = 12.000000
    //     0xa1a8b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa1a8b8: ldr             x16, [x16, #0x9e8]
    // 0xa1a8bc: r30 = Instance_Color
    //     0xa1a8bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1a8c0: stp             lr, x16, [SP]
    // 0xa1a8c4: mov             x1, x0
    // 0xa1a8c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1a8c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1a8cc: ldr             x4, [x4, #0xaa0]
    // 0xa1a8d0: r0 = copyWith()
    //     0xa1a8d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1a8d4: stur            x0, [fp, #-0x20]
    // 0xa1a8d8: r0 = Text()
    //     0xa1a8d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1a8dc: mov             x1, x0
    // 0xa1a8e0: ldur            x0, [fp, #-0x18]
    // 0xa1a8e4: stur            x1, [fp, #-0x28]
    // 0xa1a8e8: StoreField: r1->field_b = r0
    //     0xa1a8e8: stur            w0, [x1, #0xb]
    // 0xa1a8ec: ldur            x0, [fp, #-0x20]
    // 0xa1a8f0: StoreField: r1->field_13 = r0
    //     0xa1a8f0: stur            w0, [x1, #0x13]
    // 0xa1a8f4: r0 = Instance_TextOverflow
    //     0xa1a8f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xa1a8f8: ldr             x0, [x0, #0xe10]
    // 0xa1a8fc: StoreField: r1->field_2b = r0
    //     0xa1a8fc: stur            w0, [x1, #0x2b]
    // 0xa1a900: r0 = 2
    //     0xa1a900: movz            x0, #0x2
    // 0xa1a904: StoreField: r1->field_37 = r0
    //     0xa1a904: stur            w0, [x1, #0x37]
    // 0xa1a908: r0 = Padding()
    //     0xa1a908: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1a90c: mov             x2, x0
    // 0xa1a910: r0 = Instance_EdgeInsets
    //     0xa1a910: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa1a914: ldr             x0, [x0, #0xa78]
    // 0xa1a918: stur            x2, [fp, #-0x18]
    // 0xa1a91c: StoreField: r2->field_f = r0
    //     0xa1a91c: stur            w0, [x2, #0xf]
    // 0xa1a920: ldur            x1, [fp, #-0x28]
    // 0xa1a924: StoreField: r2->field_b = r1
    //     0xa1a924: stur            w1, [x2, #0xb]
    // 0xa1a928: ldur            x3, [fp, #-8]
    // 0xa1a92c: LoadField: r1 = r3->field_f
    //     0xa1a92c: ldur            w1, [x3, #0xf]
    // 0xa1a930: DecompressPointer r1
    //     0xa1a930: add             x1, x1, HEAP, lsl #32
    // 0xa1a934: LoadField: r4 = r1->field_1f
    //     0xa1a934: ldur            w4, [x1, #0x1f]
    // 0xa1a938: DecompressPointer r4
    //     0xa1a938: add             x4, x4, HEAP, lsl #32
    // 0xa1a93c: LoadField: r1 = r4->field_8f
    //     0xa1a93c: ldur            w1, [x4, #0x8f]
    // 0xa1a940: DecompressPointer r1
    //     0xa1a940: add             x1, x1, HEAP, lsl #32
    // 0xa1a944: r0 = value()
    //     0xa1a944: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a948: LoadField: r1 = r0->field_57
    //     0xa1a948: ldur            w1, [x0, #0x57]
    // 0xa1a94c: DecompressPointer r1
    //     0xa1a94c: add             x1, x1, HEAP, lsl #32
    // 0xa1a950: r0 = LoadClassIdInstr(r1)
    //     0xa1a950: ldur            x0, [x1, #-1]
    //     0xa1a954: ubfx            x0, x0, #0xc, #0x14
    // 0xa1a958: r16 = "size"
    //     0xa1a958: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xa1a95c: ldr             x16, [x16, #0x9c0]
    // 0xa1a960: stp             x16, x1, [SP]
    // 0xa1a964: mov             lr, x0
    // 0xa1a968: ldr             lr, [x21, lr, lsl #3]
    // 0xa1a96c: blr             lr
    // 0xa1a970: tbnz            w0, #4, #0xa1ab1c
    // 0xa1a974: ldur            x0, [fp, #-8]
    // 0xa1a978: r1 = Null
    //     0xa1a978: mov             x1, NULL
    // 0xa1a97c: r2 = 8
    //     0xa1a97c: movz            x2, #0x8
    // 0xa1a980: r0 = AllocateArray()
    //     0xa1a980: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1a984: stur            x0, [fp, #-0x20]
    // 0xa1a988: r16 = "Size: "
    //     0xa1a988: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xa1a98c: ldr             x16, [x16, #0xf00]
    // 0xa1a990: StoreField: r0->field_f = r16
    //     0xa1a990: stur            w16, [x0, #0xf]
    // 0xa1a994: ldur            x2, [fp, #-8]
    // 0xa1a998: LoadField: r1 = r2->field_f
    //     0xa1a998: ldur            w1, [x2, #0xf]
    // 0xa1a99c: DecompressPointer r1
    //     0xa1a99c: add             x1, x1, HEAP, lsl #32
    // 0xa1a9a0: LoadField: r3 = r1->field_1f
    //     0xa1a9a0: ldur            w3, [x1, #0x1f]
    // 0xa1a9a4: DecompressPointer r3
    //     0xa1a9a4: add             x3, x3, HEAP, lsl #32
    // 0xa1a9a8: LoadField: r1 = r3->field_8f
    //     0xa1a9a8: ldur            w1, [x3, #0x8f]
    // 0xa1a9ac: DecompressPointer r1
    //     0xa1a9ac: add             x1, x1, HEAP, lsl #32
    // 0xa1a9b0: r0 = value()
    //     0xa1a9b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1a9b4: LoadField: r2 = r0->field_53
    //     0xa1a9b4: ldur            w2, [x0, #0x53]
    // 0xa1a9b8: DecompressPointer r2
    //     0xa1a9b8: add             x2, x2, HEAP, lsl #32
    // 0xa1a9bc: cmp             w2, NULL
    // 0xa1a9c0: b.ne            #0xa1a9d0
    // 0xa1a9c4: ldur            x3, [fp, #-8]
    // 0xa1a9c8: r0 = Null
    //     0xa1a9c8: mov             x0, NULL
    // 0xa1a9cc: b               #0xa1aa14
    // 0xa1a9d0: ldur            x3, [fp, #-8]
    // 0xa1a9d4: LoadField: r0 = r3->field_f
    //     0xa1a9d4: ldur            w0, [x3, #0xf]
    // 0xa1a9d8: DecompressPointer r0
    //     0xa1a9d8: add             x0, x0, HEAP, lsl #32
    // 0xa1a9dc: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xa1a9dc: ldur            x4, [x0, #0x17]
    // 0xa1a9e0: LoadField: r0 = r2->field_b
    //     0xa1a9e0: ldur            w0, [x2, #0xb]
    // 0xa1a9e4: r1 = LoadInt32Instr(r0)
    //     0xa1a9e4: sbfx            x1, x0, #1, #0x1f
    // 0xa1a9e8: mov             x0, x1
    // 0xa1a9ec: mov             x1, x4
    // 0xa1a9f0: cmp             x1, x0
    // 0xa1a9f4: b.hs            #0xa1c2c4
    // 0xa1a9f8: LoadField: r0 = r2->field_f
    //     0xa1a9f8: ldur            w0, [x2, #0xf]
    // 0xa1a9fc: DecompressPointer r0
    //     0xa1a9fc: add             x0, x0, HEAP, lsl #32
    // 0xa1aa00: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1aa00: add             x16, x0, x4, lsl #2
    //     0xa1aa04: ldur            w1, [x16, #0xf]
    // 0xa1aa08: DecompressPointer r1
    //     0xa1aa08: add             x1, x1, HEAP, lsl #32
    // 0xa1aa0c: LoadField: r0 = r1->field_f
    //     0xa1aa0c: ldur            w0, [x1, #0xf]
    // 0xa1aa10: DecompressPointer r0
    //     0xa1aa10: add             x0, x0, HEAP, lsl #32
    // 0xa1aa14: ldur            x2, [fp, #-0x20]
    // 0xa1aa18: mov             x1, x2
    // 0xa1aa1c: ArrayStore: r1[1] = r0  ; List_4
    //     0xa1aa1c: add             x25, x1, #0x13
    //     0xa1aa20: str             w0, [x25]
    //     0xa1aa24: tbz             w0, #0, #0xa1aa40
    //     0xa1aa28: ldurb           w16, [x1, #-1]
    //     0xa1aa2c: ldurb           w17, [x0, #-1]
    //     0xa1aa30: and             x16, x17, x16, lsr #2
    //     0xa1aa34: tst             x16, HEAP, lsr #32
    //     0xa1aa38: b.eq            #0xa1aa40
    //     0xa1aa3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa1aa40: r16 = " / Qty: "
    //     0xa1aa40: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xa1aa44: ldr             x16, [x16, #0x760]
    // 0xa1aa48: ArrayStore: r2[0] = r16  ; List_4
    //     0xa1aa48: stur            w16, [x2, #0x17]
    // 0xa1aa4c: LoadField: r0 = r3->field_f
    //     0xa1aa4c: ldur            w0, [x3, #0xf]
    // 0xa1aa50: DecompressPointer r0
    //     0xa1aa50: add             x0, x0, HEAP, lsl #32
    // 0xa1aa54: LoadField: r1 = r0->field_1f
    //     0xa1aa54: ldur            w1, [x0, #0x1f]
    // 0xa1aa58: DecompressPointer r1
    //     0xa1aa58: add             x1, x1, HEAP, lsl #32
    // 0xa1aa5c: r17 = 287
    //     0xa1aa5c: movz            x17, #0x11f
    // 0xa1aa60: ldr             w0, [x1, x17]
    // 0xa1aa64: DecompressPointer r0
    //     0xa1aa64: add             x0, x0, HEAP, lsl #32
    // 0xa1aa68: mov             x1, x0
    // 0xa1aa6c: r0 = value()
    //     0xa1aa6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1aa70: ldur            x1, [fp, #-0x20]
    // 0xa1aa74: ArrayStore: r1[3] = r0  ; List_4
    //     0xa1aa74: add             x25, x1, #0x1b
    //     0xa1aa78: str             w0, [x25]
    //     0xa1aa7c: tbz             w0, #0, #0xa1aa98
    //     0xa1aa80: ldurb           w16, [x1, #-1]
    //     0xa1aa84: ldurb           w17, [x0, #-1]
    //     0xa1aa88: and             x16, x17, x16, lsr #2
    //     0xa1aa8c: tst             x16, HEAP, lsr #32
    //     0xa1aa90: b.eq            #0xa1aa98
    //     0xa1aa94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa1aa98: ldur            x16, [fp, #-0x20]
    // 0xa1aa9c: str             x16, [SP]
    // 0xa1aaa0: r0 = _interpolate()
    //     0xa1aaa0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa1aaa4: ldur            x2, [fp, #-8]
    // 0xa1aaa8: stur            x0, [fp, #-0x20]
    // 0xa1aaac: LoadField: r1 = r2->field_13
    //     0xa1aaac: ldur            w1, [x2, #0x13]
    // 0xa1aab0: DecompressPointer r1
    //     0xa1aab0: add             x1, x1, HEAP, lsl #32
    // 0xa1aab4: r0 = of()
    //     0xa1aab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1aab8: LoadField: r1 = r0->field_87
    //     0xa1aab8: ldur            w1, [x0, #0x87]
    // 0xa1aabc: DecompressPointer r1
    //     0xa1aabc: add             x1, x1, HEAP, lsl #32
    // 0xa1aac0: LoadField: r0 = r1->field_2b
    //     0xa1aac0: ldur            w0, [x1, #0x2b]
    // 0xa1aac4: DecompressPointer r0
    //     0xa1aac4: add             x0, x0, HEAP, lsl #32
    // 0xa1aac8: stur            x0, [fp, #-0x28]
    // 0xa1aacc: r1 = Instance_Color
    //     0xa1aacc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1aad0: d0 = 0.700000
    //     0xa1aad0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa1aad4: ldr             d0, [x17, #0xf48]
    // 0xa1aad8: r0 = withOpacity()
    //     0xa1aad8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa1aadc: r16 = 12.000000
    //     0xa1aadc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa1aae0: ldr             x16, [x16, #0x9e8]
    // 0xa1aae4: stp             x0, x16, [SP]
    // 0xa1aae8: ldur            x1, [fp, #-0x28]
    // 0xa1aaec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1aaec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1aaf0: ldr             x4, [x4, #0xaa0]
    // 0xa1aaf4: r0 = copyWith()
    //     0xa1aaf4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1aaf8: stur            x0, [fp, #-0x28]
    // 0xa1aafc: r0 = Text()
    //     0xa1aafc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1ab00: mov             x1, x0
    // 0xa1ab04: ldur            x0, [fp, #-0x20]
    // 0xa1ab08: StoreField: r1->field_b = r0
    //     0xa1ab08: stur            w0, [x1, #0xb]
    // 0xa1ab0c: ldur            x0, [fp, #-0x28]
    // 0xa1ab10: StoreField: r1->field_13 = r0
    //     0xa1ab10: stur            w0, [x1, #0x13]
    // 0xa1ab14: mov             x0, x1
    // 0xa1ab18: b               #0xa1acc0
    // 0xa1ab1c: ldur            x0, [fp, #-8]
    // 0xa1ab20: r1 = Null
    //     0xa1ab20: mov             x1, NULL
    // 0xa1ab24: r2 = 8
    //     0xa1ab24: movz            x2, #0x8
    // 0xa1ab28: r0 = AllocateArray()
    //     0xa1ab28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1ab2c: stur            x0, [fp, #-0x20]
    // 0xa1ab30: r16 = "Variant : "
    //     0xa1ab30: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xa1ab34: ldr             x16, [x16, #0x768]
    // 0xa1ab38: StoreField: r0->field_f = r16
    //     0xa1ab38: stur            w16, [x0, #0xf]
    // 0xa1ab3c: ldur            x2, [fp, #-8]
    // 0xa1ab40: LoadField: r1 = r2->field_f
    //     0xa1ab40: ldur            w1, [x2, #0xf]
    // 0xa1ab44: DecompressPointer r1
    //     0xa1ab44: add             x1, x1, HEAP, lsl #32
    // 0xa1ab48: LoadField: r3 = r1->field_1f
    //     0xa1ab48: ldur            w3, [x1, #0x1f]
    // 0xa1ab4c: DecompressPointer r3
    //     0xa1ab4c: add             x3, x3, HEAP, lsl #32
    // 0xa1ab50: LoadField: r1 = r3->field_8f
    //     0xa1ab50: ldur            w1, [x3, #0x8f]
    // 0xa1ab54: DecompressPointer r1
    //     0xa1ab54: add             x1, x1, HEAP, lsl #32
    // 0xa1ab58: r0 = value()
    //     0xa1ab58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1ab5c: LoadField: r2 = r0->field_53
    //     0xa1ab5c: ldur            w2, [x0, #0x53]
    // 0xa1ab60: DecompressPointer r2
    //     0xa1ab60: add             x2, x2, HEAP, lsl #32
    // 0xa1ab64: cmp             w2, NULL
    // 0xa1ab68: b.ne            #0xa1ab78
    // 0xa1ab6c: ldur            x3, [fp, #-8]
    // 0xa1ab70: r0 = Null
    //     0xa1ab70: mov             x0, NULL
    // 0xa1ab74: b               #0xa1abbc
    // 0xa1ab78: ldur            x3, [fp, #-8]
    // 0xa1ab7c: LoadField: r0 = r3->field_f
    //     0xa1ab7c: ldur            w0, [x3, #0xf]
    // 0xa1ab80: DecompressPointer r0
    //     0xa1ab80: add             x0, x0, HEAP, lsl #32
    // 0xa1ab84: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xa1ab84: ldur            x4, [x0, #0x17]
    // 0xa1ab88: LoadField: r0 = r2->field_b
    //     0xa1ab88: ldur            w0, [x2, #0xb]
    // 0xa1ab8c: r1 = LoadInt32Instr(r0)
    //     0xa1ab8c: sbfx            x1, x0, #1, #0x1f
    // 0xa1ab90: mov             x0, x1
    // 0xa1ab94: mov             x1, x4
    // 0xa1ab98: cmp             x1, x0
    // 0xa1ab9c: b.hs            #0xa1c2c8
    // 0xa1aba0: LoadField: r0 = r2->field_f
    //     0xa1aba0: ldur            w0, [x2, #0xf]
    // 0xa1aba4: DecompressPointer r0
    //     0xa1aba4: add             x0, x0, HEAP, lsl #32
    // 0xa1aba8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1aba8: add             x16, x0, x4, lsl #2
    //     0xa1abac: ldur            w1, [x16, #0xf]
    // 0xa1abb0: DecompressPointer r1
    //     0xa1abb0: add             x1, x1, HEAP, lsl #32
    // 0xa1abb4: LoadField: r0 = r1->field_f
    //     0xa1abb4: ldur            w0, [x1, #0xf]
    // 0xa1abb8: DecompressPointer r0
    //     0xa1abb8: add             x0, x0, HEAP, lsl #32
    // 0xa1abbc: ldur            x2, [fp, #-0x20]
    // 0xa1abc0: mov             x1, x2
    // 0xa1abc4: ArrayStore: r1[1] = r0  ; List_4
    //     0xa1abc4: add             x25, x1, #0x13
    //     0xa1abc8: str             w0, [x25]
    //     0xa1abcc: tbz             w0, #0, #0xa1abe8
    //     0xa1abd0: ldurb           w16, [x1, #-1]
    //     0xa1abd4: ldurb           w17, [x0, #-1]
    //     0xa1abd8: and             x16, x17, x16, lsr #2
    //     0xa1abdc: tst             x16, HEAP, lsr #32
    //     0xa1abe0: b.eq            #0xa1abe8
    //     0xa1abe4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa1abe8: r16 = " / Qty: "
    //     0xa1abe8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xa1abec: ldr             x16, [x16, #0x760]
    // 0xa1abf0: ArrayStore: r2[0] = r16  ; List_4
    //     0xa1abf0: stur            w16, [x2, #0x17]
    // 0xa1abf4: LoadField: r0 = r3->field_f
    //     0xa1abf4: ldur            w0, [x3, #0xf]
    // 0xa1abf8: DecompressPointer r0
    //     0xa1abf8: add             x0, x0, HEAP, lsl #32
    // 0xa1abfc: LoadField: r1 = r0->field_1f
    //     0xa1abfc: ldur            w1, [x0, #0x1f]
    // 0xa1ac00: DecompressPointer r1
    //     0xa1ac00: add             x1, x1, HEAP, lsl #32
    // 0xa1ac04: r17 = 287
    //     0xa1ac04: movz            x17, #0x11f
    // 0xa1ac08: ldr             w0, [x1, x17]
    // 0xa1ac0c: DecompressPointer r0
    //     0xa1ac0c: add             x0, x0, HEAP, lsl #32
    // 0xa1ac10: mov             x1, x0
    // 0xa1ac14: r0 = value()
    //     0xa1ac14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1ac18: ldur            x1, [fp, #-0x20]
    // 0xa1ac1c: ArrayStore: r1[3] = r0  ; List_4
    //     0xa1ac1c: add             x25, x1, #0x1b
    //     0xa1ac20: str             w0, [x25]
    //     0xa1ac24: tbz             w0, #0, #0xa1ac40
    //     0xa1ac28: ldurb           w16, [x1, #-1]
    //     0xa1ac2c: ldurb           w17, [x0, #-1]
    //     0xa1ac30: and             x16, x17, x16, lsr #2
    //     0xa1ac34: tst             x16, HEAP, lsr #32
    //     0xa1ac38: b.eq            #0xa1ac40
    //     0xa1ac3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa1ac40: ldur            x16, [fp, #-0x20]
    // 0xa1ac44: str             x16, [SP]
    // 0xa1ac48: r0 = _interpolate()
    //     0xa1ac48: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa1ac4c: ldur            x2, [fp, #-8]
    // 0xa1ac50: stur            x0, [fp, #-0x20]
    // 0xa1ac54: LoadField: r1 = r2->field_13
    //     0xa1ac54: ldur            w1, [x2, #0x13]
    // 0xa1ac58: DecompressPointer r1
    //     0xa1ac58: add             x1, x1, HEAP, lsl #32
    // 0xa1ac5c: r0 = of()
    //     0xa1ac5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1ac60: LoadField: r1 = r0->field_87
    //     0xa1ac60: ldur            w1, [x0, #0x87]
    // 0xa1ac64: DecompressPointer r1
    //     0xa1ac64: add             x1, x1, HEAP, lsl #32
    // 0xa1ac68: LoadField: r0 = r1->field_2b
    //     0xa1ac68: ldur            w0, [x1, #0x2b]
    // 0xa1ac6c: DecompressPointer r0
    //     0xa1ac6c: add             x0, x0, HEAP, lsl #32
    // 0xa1ac70: stur            x0, [fp, #-0x28]
    // 0xa1ac74: r1 = Instance_Color
    //     0xa1ac74: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1ac78: d0 = 0.700000
    //     0xa1ac78: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa1ac7c: ldr             d0, [x17, #0xf48]
    // 0xa1ac80: r0 = withOpacity()
    //     0xa1ac80: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa1ac84: r16 = 12.000000
    //     0xa1ac84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa1ac88: ldr             x16, [x16, #0x9e8]
    // 0xa1ac8c: stp             x0, x16, [SP]
    // 0xa1ac90: ldur            x1, [fp, #-0x28]
    // 0xa1ac94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1ac94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1ac98: ldr             x4, [x4, #0xaa0]
    // 0xa1ac9c: r0 = copyWith()
    //     0xa1ac9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1aca0: stur            x0, [fp, #-0x28]
    // 0xa1aca4: r0 = Text()
    //     0xa1aca4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1aca8: mov             x1, x0
    // 0xa1acac: ldur            x0, [fp, #-0x20]
    // 0xa1acb0: StoreField: r1->field_b = r0
    //     0xa1acb0: stur            w0, [x1, #0xb]
    // 0xa1acb4: ldur            x0, [fp, #-0x28]
    // 0xa1acb8: StoreField: r1->field_13 = r0
    //     0xa1acb8: stur            w0, [x1, #0x13]
    // 0xa1acbc: mov             x0, x1
    // 0xa1acc0: ldur            x2, [fp, #-8]
    // 0xa1acc4: stur            x0, [fp, #-0x20]
    // 0xa1acc8: r0 = Padding()
    //     0xa1acc8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1accc: mov             x3, x0
    // 0xa1acd0: r0 = Instance_EdgeInsets
    //     0xa1acd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa1acd4: ldr             x0, [x0, #0xa78]
    // 0xa1acd8: stur            x3, [fp, #-0x28]
    // 0xa1acdc: StoreField: r3->field_f = r0
    //     0xa1acdc: stur            w0, [x3, #0xf]
    // 0xa1ace0: ldur            x0, [fp, #-0x20]
    // 0xa1ace4: StoreField: r3->field_b = r0
    //     0xa1ace4: stur            w0, [x3, #0xb]
    // 0xa1ace8: ldur            x0, [fp, #-8]
    // 0xa1acec: LoadField: r1 = r0->field_f
    //     0xa1acec: ldur            w1, [x0, #0xf]
    // 0xa1acf0: DecompressPointer r1
    //     0xa1acf0: add             x1, x1, HEAP, lsl #32
    // 0xa1acf4: LoadField: r2 = r1->field_b
    //     0xa1acf4: ldur            w2, [x1, #0xb]
    // 0xa1acf8: DecompressPointer r2
    //     0xa1acf8: add             x2, x2, HEAP, lsl #32
    // 0xa1acfc: cmp             w2, NULL
    // 0xa1ad00: b.eq            #0xa1c2cc
    // 0xa1ad04: LoadField: r1 = r2->field_b
    //     0xa1ad04: ldur            w1, [x2, #0xb]
    // 0xa1ad08: DecompressPointer r1
    //     0xa1ad08: add             x1, x1, HEAP, lsl #32
    // 0xa1ad0c: LoadField: r2 = r1->field_b
    //     0xa1ad0c: ldur            w2, [x1, #0xb]
    // 0xa1ad10: DecompressPointer r2
    //     0xa1ad10: add             x2, x2, HEAP, lsl #32
    // 0xa1ad14: cmp             w2, NULL
    // 0xa1ad18: b.ne            #0xa1ad24
    // 0xa1ad1c: r7 = Null
    //     0xa1ad1c: mov             x7, NULL
    // 0xa1ad20: b               #0xa1ad30
    // 0xa1ad24: LoadField: r1 = r2->field_1f
    //     0xa1ad24: ldur            w1, [x2, #0x1f]
    // 0xa1ad28: DecompressPointer r1
    //     0xa1ad28: add             x1, x1, HEAP, lsl #32
    // 0xa1ad2c: mov             x7, x1
    // 0xa1ad30: ldur            x5, [fp, #-0x10]
    // 0xa1ad34: ldur            x4, [fp, #-0x18]
    // 0xa1ad38: r6 = 4
    //     0xa1ad38: movz            x6, #0x4
    // 0xa1ad3c: mov             x2, x6
    // 0xa1ad40: stur            x7, [fp, #-0x20]
    // 0xa1ad44: r1 = Null
    //     0xa1ad44: mov             x1, NULL
    // 0xa1ad48: r0 = AllocateArray()
    //     0xa1ad48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1ad4c: mov             x1, x0
    // 0xa1ad50: ldur            x0, [fp, #-0x20]
    // 0xa1ad54: StoreField: r1->field_f = r0
    //     0xa1ad54: stur            w0, [x1, #0xf]
    // 0xa1ad58: r16 = " "
    //     0xa1ad58: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xa1ad5c: StoreField: r1->field_13 = r16
    //     0xa1ad5c: stur            w16, [x1, #0x13]
    // 0xa1ad60: str             x1, [SP]
    // 0xa1ad64: r0 = _interpolate()
    //     0xa1ad64: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa1ad68: ldur            x2, [fp, #-8]
    // 0xa1ad6c: stur            x0, [fp, #-0x20]
    // 0xa1ad70: LoadField: r1 = r2->field_13
    //     0xa1ad70: ldur            w1, [x2, #0x13]
    // 0xa1ad74: DecompressPointer r1
    //     0xa1ad74: add             x1, x1, HEAP, lsl #32
    // 0xa1ad78: r0 = of()
    //     0xa1ad78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1ad7c: LoadField: r1 = r0->field_87
    //     0xa1ad7c: ldur            w1, [x0, #0x87]
    // 0xa1ad80: DecompressPointer r1
    //     0xa1ad80: add             x1, x1, HEAP, lsl #32
    // 0xa1ad84: LoadField: r0 = r1->field_7
    //     0xa1ad84: ldur            w0, [x1, #7]
    // 0xa1ad88: DecompressPointer r0
    //     0xa1ad88: add             x0, x0, HEAP, lsl #32
    // 0xa1ad8c: r16 = 12.000000
    //     0xa1ad8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa1ad90: ldr             x16, [x16, #0x9e8]
    // 0xa1ad94: r30 = Instance_Color
    //     0xa1ad94: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1ad98: stp             lr, x16, [SP]
    // 0xa1ad9c: mov             x1, x0
    // 0xa1ada0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1ada0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1ada4: ldr             x4, [x4, #0xaa0]
    // 0xa1ada8: r0 = copyWith()
    //     0xa1ada8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1adac: stur            x0, [fp, #-0x30]
    // 0xa1adb0: r0 = Text()
    //     0xa1adb0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1adb4: mov             x1, x0
    // 0xa1adb8: ldur            x0, [fp, #-0x20]
    // 0xa1adbc: stur            x1, [fp, #-0x38]
    // 0xa1adc0: StoreField: r1->field_b = r0
    //     0xa1adc0: stur            w0, [x1, #0xb]
    // 0xa1adc4: ldur            x0, [fp, #-0x30]
    // 0xa1adc8: StoreField: r1->field_13 = r0
    //     0xa1adc8: stur            w0, [x1, #0x13]
    // 0xa1adcc: r0 = Padding()
    //     0xa1adcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1add0: mov             x3, x0
    // 0xa1add4: r0 = Instance_EdgeInsets
    //     0xa1add4: add             x0, PP, #0x56, lsl #12  ; [pp+0x56cc8] Obj!EdgeInsets@d57f81
    //     0xa1add8: ldr             x0, [x0, #0xcc8]
    // 0xa1addc: stur            x3, [fp, #-0x20]
    // 0xa1ade0: StoreField: r3->field_f = r0
    //     0xa1ade0: stur            w0, [x3, #0xf]
    // 0xa1ade4: ldur            x0, [fp, #-0x38]
    // 0xa1ade8: StoreField: r3->field_b = r0
    //     0xa1ade8: stur            w0, [x3, #0xb]
    // 0xa1adec: r1 = Null
    //     0xa1adec: mov             x1, NULL
    // 0xa1adf0: r2 = 6
    //     0xa1adf0: movz            x2, #0x6
    // 0xa1adf4: r0 = AllocateArray()
    //     0xa1adf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1adf8: mov             x2, x0
    // 0xa1adfc: ldur            x0, [fp, #-0x18]
    // 0xa1ae00: stur            x2, [fp, #-0x30]
    // 0xa1ae04: StoreField: r2->field_f = r0
    //     0xa1ae04: stur            w0, [x2, #0xf]
    // 0xa1ae08: ldur            x0, [fp, #-0x28]
    // 0xa1ae0c: StoreField: r2->field_13 = r0
    //     0xa1ae0c: stur            w0, [x2, #0x13]
    // 0xa1ae10: ldur            x0, [fp, #-0x20]
    // 0xa1ae14: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1ae14: stur            w0, [x2, #0x17]
    // 0xa1ae18: r1 = <Widget>
    //     0xa1ae18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1ae1c: r0 = AllocateGrowableArray()
    //     0xa1ae1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1ae20: mov             x1, x0
    // 0xa1ae24: ldur            x0, [fp, #-0x30]
    // 0xa1ae28: stur            x1, [fp, #-0x18]
    // 0xa1ae2c: StoreField: r1->field_f = r0
    //     0xa1ae2c: stur            w0, [x1, #0xf]
    // 0xa1ae30: r2 = 6
    //     0xa1ae30: movz            x2, #0x6
    // 0xa1ae34: StoreField: r1->field_b = r2
    //     0xa1ae34: stur            w2, [x1, #0xb]
    // 0xa1ae38: r0 = Column()
    //     0xa1ae38: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa1ae3c: mov             x2, x0
    // 0xa1ae40: r0 = Instance_Axis
    //     0xa1ae40: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa1ae44: stur            x2, [fp, #-0x20]
    // 0xa1ae48: StoreField: r2->field_f = r0
    //     0xa1ae48: stur            w0, [x2, #0xf]
    // 0xa1ae4c: r3 = Instance_MainAxisAlignment
    //     0xa1ae4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1ae50: ldr             x3, [x3, #0xa08]
    // 0xa1ae54: StoreField: r2->field_13 = r3
    //     0xa1ae54: stur            w3, [x2, #0x13]
    // 0xa1ae58: r4 = Instance_MainAxisSize
    //     0xa1ae58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1ae5c: ldr             x4, [x4, #0xa10]
    // 0xa1ae60: ArrayStore: r2[0] = r4  ; List_4
    //     0xa1ae60: stur            w4, [x2, #0x17]
    // 0xa1ae64: r5 = Instance_CrossAxisAlignment
    //     0xa1ae64: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa1ae68: ldr             x5, [x5, #0x890]
    // 0xa1ae6c: StoreField: r2->field_1b = r5
    //     0xa1ae6c: stur            w5, [x2, #0x1b]
    // 0xa1ae70: r6 = Instance_VerticalDirection
    //     0xa1ae70: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1ae74: ldr             x6, [x6, #0xa20]
    // 0xa1ae78: StoreField: r2->field_23 = r6
    //     0xa1ae78: stur            w6, [x2, #0x23]
    // 0xa1ae7c: r7 = Instance_Clip
    //     0xa1ae7c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1ae80: ldr             x7, [x7, #0x38]
    // 0xa1ae84: StoreField: r2->field_2b = r7
    //     0xa1ae84: stur            w7, [x2, #0x2b]
    // 0xa1ae88: StoreField: r2->field_2f = rZR
    //     0xa1ae88: stur            xzr, [x2, #0x2f]
    // 0xa1ae8c: ldur            x1, [fp, #-0x18]
    // 0xa1ae90: StoreField: r2->field_b = r1
    //     0xa1ae90: stur            w1, [x2, #0xb]
    // 0xa1ae94: r1 = <FlexParentData>
    //     0xa1ae94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa1ae98: ldr             x1, [x1, #0xe00]
    // 0xa1ae9c: r0 = Expanded()
    //     0xa1ae9c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa1aea0: mov             x3, x0
    // 0xa1aea4: r0 = 1
    //     0xa1aea4: movz            x0, #0x1
    // 0xa1aea8: stur            x3, [fp, #-0x18]
    // 0xa1aeac: StoreField: r3->field_13 = r0
    //     0xa1aeac: stur            x0, [x3, #0x13]
    // 0xa1aeb0: r1 = Instance_FlexFit
    //     0xa1aeb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa1aeb4: ldr             x1, [x1, #0xe08]
    // 0xa1aeb8: StoreField: r3->field_1b = r1
    //     0xa1aeb8: stur            w1, [x3, #0x1b]
    // 0xa1aebc: ldur            x1, [fp, #-0x20]
    // 0xa1aec0: StoreField: r3->field_b = r1
    //     0xa1aec0: stur            w1, [x3, #0xb]
    // 0xa1aec4: r1 = Null
    //     0xa1aec4: mov             x1, NULL
    // 0xa1aec8: r2 = 4
    //     0xa1aec8: movz            x2, #0x4
    // 0xa1aecc: r0 = AllocateArray()
    //     0xa1aecc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1aed0: mov             x2, x0
    // 0xa1aed4: ldur            x0, [fp, #-0x10]
    // 0xa1aed8: stur            x2, [fp, #-0x20]
    // 0xa1aedc: StoreField: r2->field_f = r0
    //     0xa1aedc: stur            w0, [x2, #0xf]
    // 0xa1aee0: ldur            x0, [fp, #-0x18]
    // 0xa1aee4: StoreField: r2->field_13 = r0
    //     0xa1aee4: stur            w0, [x2, #0x13]
    // 0xa1aee8: r1 = <Widget>
    //     0xa1aee8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1aeec: r0 = AllocateGrowableArray()
    //     0xa1aeec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1aef0: mov             x1, x0
    // 0xa1aef4: ldur            x0, [fp, #-0x20]
    // 0xa1aef8: stur            x1, [fp, #-0x10]
    // 0xa1aefc: StoreField: r1->field_f = r0
    //     0xa1aefc: stur            w0, [x1, #0xf]
    // 0xa1af00: r2 = 4
    //     0xa1af00: movz            x2, #0x4
    // 0xa1af04: StoreField: r1->field_b = r2
    //     0xa1af04: stur            w2, [x1, #0xb]
    // 0xa1af08: r0 = Row()
    //     0xa1af08: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa1af0c: mov             x1, x0
    // 0xa1af10: r0 = Instance_Axis
    //     0xa1af10: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1af14: stur            x1, [fp, #-0x18]
    // 0xa1af18: StoreField: r1->field_f = r0
    //     0xa1af18: stur            w0, [x1, #0xf]
    // 0xa1af1c: r2 = Instance_MainAxisAlignment
    //     0xa1af1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1af20: ldr             x2, [x2, #0xa08]
    // 0xa1af24: StoreField: r1->field_13 = r2
    //     0xa1af24: stur            w2, [x1, #0x13]
    // 0xa1af28: r3 = Instance_MainAxisSize
    //     0xa1af28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1af2c: ldr             x3, [x3, #0xa10]
    // 0xa1af30: ArrayStore: r1[0] = r3  ; List_4
    //     0xa1af30: stur            w3, [x1, #0x17]
    // 0xa1af34: r4 = Instance_CrossAxisAlignment
    //     0xa1af34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa1af38: ldr             x4, [x4, #0xa18]
    // 0xa1af3c: StoreField: r1->field_1b = r4
    //     0xa1af3c: stur            w4, [x1, #0x1b]
    // 0xa1af40: r5 = Instance_VerticalDirection
    //     0xa1af40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1af44: ldr             x5, [x5, #0xa20]
    // 0xa1af48: StoreField: r1->field_23 = r5
    //     0xa1af48: stur            w5, [x1, #0x23]
    // 0xa1af4c: r6 = Instance_Clip
    //     0xa1af4c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1af50: ldr             x6, [x6, #0x38]
    // 0xa1af54: StoreField: r1->field_2b = r6
    //     0xa1af54: stur            w6, [x1, #0x2b]
    // 0xa1af58: StoreField: r1->field_2f = rZR
    //     0xa1af58: stur            xzr, [x1, #0x2f]
    // 0xa1af5c: ldur            x7, [fp, #-0x10]
    // 0xa1af60: StoreField: r1->field_b = r7
    //     0xa1af60: stur            w7, [x1, #0xb]
    // 0xa1af64: r0 = Padding()
    //     0xa1af64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1af68: mov             x2, x0
    // 0xa1af6c: r0 = Instance_EdgeInsets
    //     0xa1af6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xa1af70: ldr             x0, [x0, #0x668]
    // 0xa1af74: stur            x2, [fp, #-0x10]
    // 0xa1af78: StoreField: r2->field_f = r0
    //     0xa1af78: stur            w0, [x2, #0xf]
    // 0xa1af7c: ldur            x1, [fp, #-0x18]
    // 0xa1af80: StoreField: r2->field_b = r1
    //     0xa1af80: stur            w1, [x2, #0xb]
    // 0xa1af84: ldur            x3, [fp, #-8]
    // 0xa1af88: LoadField: r1 = r3->field_f
    //     0xa1af88: ldur            w1, [x3, #0xf]
    // 0xa1af8c: DecompressPointer r1
    //     0xa1af8c: add             x1, x1, HEAP, lsl #32
    // 0xa1af90: LoadField: r4 = r1->field_1f
    //     0xa1af90: ldur            w4, [x1, #0x1f]
    // 0xa1af94: DecompressPointer r4
    //     0xa1af94: add             x4, x4, HEAP, lsl #32
    // 0xa1af98: LoadField: r1 = r4->field_8f
    //     0xa1af98: ldur            w1, [x4, #0x8f]
    // 0xa1af9c: DecompressPointer r1
    //     0xa1af9c: add             x1, x1, HEAP, lsl #32
    // 0xa1afa0: r0 = value()
    //     0xa1afa0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1afa4: LoadField: r1 = r0->field_3f
    //     0xa1afa4: ldur            w1, [x0, #0x3f]
    // 0xa1afa8: DecompressPointer r1
    //     0xa1afa8: add             x1, x1, HEAP, lsl #32
    // 0xa1afac: cmp             w1, NULL
    // 0xa1afb0: b.ne            #0xa1afbc
    // 0xa1afb4: r0 = Null
    //     0xa1afb4: mov             x0, NULL
    // 0xa1afb8: b               #0xa1afd4
    // 0xa1afbc: LoadField: r0 = r1->field_b
    //     0xa1afbc: ldur            w0, [x1, #0xb]
    // 0xa1afc0: cbnz            w0, #0xa1afcc
    // 0xa1afc4: r1 = false
    //     0xa1afc4: add             x1, NULL, #0x30  ; false
    // 0xa1afc8: b               #0xa1afd0
    // 0xa1afcc: r1 = true
    //     0xa1afcc: add             x1, NULL, #0x20  ; true
    // 0xa1afd0: mov             x0, x1
    // 0xa1afd4: cmp             w0, NULL
    // 0xa1afd8: b.eq            #0xa1afe8
    // 0xa1afdc: tbnz            w0, #4, #0xa1afe8
    // 0xa1afe0: r0 = true
    //     0xa1afe0: add             x0, NULL, #0x20  ; true
    // 0xa1afe4: b               #0xa1b048
    // 0xa1afe8: ldur            x2, [fp, #-8]
    // 0xa1afec: LoadField: r0 = r2->field_f
    //     0xa1afec: ldur            w0, [x2, #0xf]
    // 0xa1aff0: DecompressPointer r0
    //     0xa1aff0: add             x0, x0, HEAP, lsl #32
    // 0xa1aff4: LoadField: r1 = r0->field_1f
    //     0xa1aff4: ldur            w1, [x0, #0x1f]
    // 0xa1aff8: DecompressPointer r1
    //     0xa1aff8: add             x1, x1, HEAP, lsl #32
    // 0xa1affc: LoadField: r0 = r1->field_73
    //     0xa1affc: ldur            w0, [x1, #0x73]
    // 0xa1b000: DecompressPointer r0
    //     0xa1b000: add             x0, x0, HEAP, lsl #32
    // 0xa1b004: mov             x1, x0
    // 0xa1b008: r0 = value()
    //     0xa1b008: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b00c: LoadField: r1 = r0->field_13
    //     0xa1b00c: ldur            w1, [x0, #0x13]
    // 0xa1b010: DecompressPointer r1
    //     0xa1b010: add             x1, x1, HEAP, lsl #32
    // 0xa1b014: cmp             w1, NULL
    // 0xa1b018: b.ne            #0xa1b024
    // 0xa1b01c: r0 = Null
    //     0xa1b01c: mov             x0, NULL
    // 0xa1b020: b               #0xa1b03c
    // 0xa1b024: LoadField: r0 = r1->field_b
    //     0xa1b024: ldur            w0, [x1, #0xb]
    // 0xa1b028: cbnz            w0, #0xa1b034
    // 0xa1b02c: r1 = false
    //     0xa1b02c: add             x1, NULL, #0x30  ; false
    // 0xa1b030: b               #0xa1b038
    // 0xa1b034: r1 = true
    //     0xa1b034: add             x1, NULL, #0x20  ; true
    // 0xa1b038: mov             x0, x1
    // 0xa1b03c: cmp             w0, NULL
    // 0xa1b040: b.ne            #0xa1b048
    // 0xa1b044: r0 = false
    //     0xa1b044: add             x0, NULL, #0x30  ; false
    // 0xa1b048: ldur            x2, [fp, #-8]
    // 0xa1b04c: stur            x0, [fp, #-0x18]
    // 0xa1b050: LoadField: r1 = r2->field_f
    //     0xa1b050: ldur            w1, [x2, #0xf]
    // 0xa1b054: DecompressPointer r1
    //     0xa1b054: add             x1, x1, HEAP, lsl #32
    // 0xa1b058: LoadField: r3 = r1->field_1f
    //     0xa1b058: ldur            w3, [x1, #0x1f]
    // 0xa1b05c: DecompressPointer r3
    //     0xa1b05c: add             x3, x3, HEAP, lsl #32
    // 0xa1b060: LoadField: r1 = r3->field_8f
    //     0xa1b060: ldur            w1, [x3, #0x8f]
    // 0xa1b064: DecompressPointer r1
    //     0xa1b064: add             x1, x1, HEAP, lsl #32
    // 0xa1b068: r0 = value()
    //     0xa1b068: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b06c: LoadField: r2 = r0->field_3f
    //     0xa1b06c: ldur            w2, [x0, #0x3f]
    // 0xa1b070: DecompressPointer r2
    //     0xa1b070: add             x2, x2, HEAP, lsl #32
    // 0xa1b074: ldur            x0, [fp, #-8]
    // 0xa1b078: stur            x2, [fp, #-0x20]
    // 0xa1b07c: LoadField: r1 = r0->field_f
    //     0xa1b07c: ldur            w1, [x0, #0xf]
    // 0xa1b080: DecompressPointer r1
    //     0xa1b080: add             x1, x1, HEAP, lsl #32
    // 0xa1b084: LoadField: r3 = r1->field_1f
    //     0xa1b084: ldur            w3, [x1, #0x1f]
    // 0xa1b088: DecompressPointer r3
    //     0xa1b088: add             x3, x3, HEAP, lsl #32
    // 0xa1b08c: LoadField: r1 = r3->field_73
    //     0xa1b08c: ldur            w1, [x3, #0x73]
    // 0xa1b090: DecompressPointer r1
    //     0xa1b090: add             x1, x1, HEAP, lsl #32
    // 0xa1b094: r0 = value()
    //     0xa1b094: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b098: ldur            x2, [fp, #-8]
    // 0xa1b09c: stur            x0, [fp, #-0x28]
    // 0xa1b0a0: LoadField: r1 = r2->field_f
    //     0xa1b0a0: ldur            w1, [x2, #0xf]
    // 0xa1b0a4: DecompressPointer r1
    //     0xa1b0a4: add             x1, x1, HEAP, lsl #32
    // 0xa1b0a8: LoadField: r3 = r1->field_1f
    //     0xa1b0a8: ldur            w3, [x1, #0x1f]
    // 0xa1b0ac: DecompressPointer r3
    //     0xa1b0ac: add             x3, x3, HEAP, lsl #32
    // 0xa1b0b0: LoadField: r1 = r3->field_8f
    //     0xa1b0b0: ldur            w1, [x3, #0x8f]
    // 0xa1b0b4: DecompressPointer r1
    //     0xa1b0b4: add             x1, x1, HEAP, lsl #32
    // 0xa1b0b8: r0 = value()
    //     0xa1b0b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b0bc: LoadField: r1 = r0->field_43
    //     0xa1b0bc: ldur            w1, [x0, #0x43]
    // 0xa1b0c0: DecompressPointer r1
    //     0xa1b0c0: add             x1, x1, HEAP, lsl #32
    // 0xa1b0c4: cbz             w1, #0xa1b0fc
    // 0xa1b0c8: ldur            x2, [fp, #-8]
    // 0xa1b0cc: LoadField: r0 = r2->field_f
    //     0xa1b0cc: ldur            w0, [x2, #0xf]
    // 0xa1b0d0: DecompressPointer r0
    //     0xa1b0d0: add             x0, x0, HEAP, lsl #32
    // 0xa1b0d4: LoadField: r1 = r0->field_1f
    //     0xa1b0d4: ldur            w1, [x0, #0x1f]
    // 0xa1b0d8: DecompressPointer r1
    //     0xa1b0d8: add             x1, x1, HEAP, lsl #32
    // 0xa1b0dc: LoadField: r0 = r1->field_8f
    //     0xa1b0dc: ldur            w0, [x1, #0x8f]
    // 0xa1b0e0: DecompressPointer r0
    //     0xa1b0e0: add             x0, x0, HEAP, lsl #32
    // 0xa1b0e4: mov             x1, x0
    // 0xa1b0e8: r0 = value()
    //     0xa1b0e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b0ec: LoadField: r1 = r0->field_47
    //     0xa1b0ec: ldur            w1, [x0, #0x47]
    // 0xa1b0f0: DecompressPointer r1
    //     0xa1b0f0: add             x1, x1, HEAP, lsl #32
    // 0xa1b0f4: mov             x4, x1
    // 0xa1b0f8: b               #0xa1b184
    // 0xa1b0fc: ldur            x2, [fp, #-8]
    // 0xa1b100: LoadField: r0 = r2->field_f
    //     0xa1b100: ldur            w0, [x2, #0xf]
    // 0xa1b104: DecompressPointer r0
    //     0xa1b104: add             x0, x0, HEAP, lsl #32
    // 0xa1b108: LoadField: r1 = r0->field_1f
    //     0xa1b108: ldur            w1, [x0, #0x1f]
    // 0xa1b10c: DecompressPointer r1
    //     0xa1b10c: add             x1, x1, HEAP, lsl #32
    // 0xa1b110: LoadField: r0 = r1->field_eb
    //     0xa1b110: ldur            w0, [x1, #0xeb]
    // 0xa1b114: DecompressPointer r0
    //     0xa1b114: add             x0, x0, HEAP, lsl #32
    // 0xa1b118: cmp             w0, NULL
    // 0xa1b11c: b.eq            #0xa1b164
    // 0xa1b120: cmp             w0, NULL
    // 0xa1b124: b.ne            #0xa1b130
    // 0xa1b128: r3 = Null
    //     0xa1b128: mov             x3, NULL
    // 0xa1b12c: b               #0xa1b148
    // 0xa1b130: LoadField: r3 = r0->field_7
    //     0xa1b130: ldur            w3, [x0, #7]
    // 0xa1b134: cbnz            w3, #0xa1b140
    // 0xa1b138: r4 = false
    //     0xa1b138: add             x4, NULL, #0x30  ; false
    // 0xa1b13c: b               #0xa1b144
    // 0xa1b140: r4 = true
    //     0xa1b140: add             x4, NULL, #0x20  ; true
    // 0xa1b144: mov             x3, x4
    // 0xa1b148: cmp             w3, NULL
    // 0xa1b14c: b.eq            #0xa1b164
    // 0xa1b150: tbnz            w3, #4, #0xa1b164
    // 0xa1b154: cmp             w0, NULL
    // 0xa1b158: b.ne            #0xa1b180
    // 0xa1b15c: r0 = ""
    //     0xa1b15c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1b160: b               #0xa1b180
    // 0xa1b164: LoadField: r0 = r1->field_8f
    //     0xa1b164: ldur            w0, [x1, #0x8f]
    // 0xa1b168: DecompressPointer r0
    //     0xa1b168: add             x0, x0, HEAP, lsl #32
    // 0xa1b16c: mov             x1, x0
    // 0xa1b170: r0 = value()
    //     0xa1b170: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b174: LoadField: r1 = r0->field_47
    //     0xa1b174: ldur            w1, [x0, #0x47]
    // 0xa1b178: DecompressPointer r1
    //     0xa1b178: add             x1, x1, HEAP, lsl #32
    // 0xa1b17c: mov             x0, x1
    // 0xa1b180: mov             x4, x0
    // 0xa1b184: ldur            x2, [fp, #-8]
    // 0xa1b188: ldur            x3, [fp, #-0x18]
    // 0xa1b18c: ldur            x1, [fp, #-0x20]
    // 0xa1b190: ldur            x0, [fp, #-0x28]
    // 0xa1b194: stur            x4, [fp, #-0x30]
    // 0xa1b198: r0 = CustomisedStrip()
    //     0xa1b198: bl              #0xa1c2f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xa1b19c: mov             x1, x0
    // 0xa1b1a0: ldur            x0, [fp, #-0x20]
    // 0xa1b1a4: stur            x1, [fp, #-0x38]
    // 0xa1b1a8: StoreField: r1->field_b = r0
    //     0xa1b1a8: stur            w0, [x1, #0xb]
    // 0xa1b1ac: ldur            x0, [fp, #-0x28]
    // 0xa1b1b0: StoreField: r1->field_f = r0
    //     0xa1b1b0: stur            w0, [x1, #0xf]
    // 0xa1b1b4: ldur            x0, [fp, #-0x30]
    // 0xa1b1b8: StoreField: r1->field_13 = r0
    //     0xa1b1b8: stur            w0, [x1, #0x13]
    // 0xa1b1bc: r0 = Padding()
    //     0xa1b1bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1b1c0: mov             x1, x0
    // 0xa1b1c4: r0 = Instance_EdgeInsets
    //     0xa1b1c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xa1b1c8: ldr             x0, [x0, #0x668]
    // 0xa1b1cc: stur            x1, [fp, #-0x20]
    // 0xa1b1d0: StoreField: r1->field_f = r0
    //     0xa1b1d0: stur            w0, [x1, #0xf]
    // 0xa1b1d4: ldur            x0, [fp, #-0x38]
    // 0xa1b1d8: StoreField: r1->field_b = r0
    //     0xa1b1d8: stur            w0, [x1, #0xb]
    // 0xa1b1dc: r0 = Visibility()
    //     0xa1b1dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa1b1e0: mov             x1, x0
    // 0xa1b1e4: ldur            x0, [fp, #-0x20]
    // 0xa1b1e8: stur            x1, [fp, #-0x28]
    // 0xa1b1ec: StoreField: r1->field_b = r0
    //     0xa1b1ec: stur            w0, [x1, #0xb]
    // 0xa1b1f0: r2 = Instance_SizedBox
    //     0xa1b1f0: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa1b1f4: StoreField: r1->field_f = r2
    //     0xa1b1f4: stur            w2, [x1, #0xf]
    // 0xa1b1f8: ldur            x0, [fp, #-0x18]
    // 0xa1b1fc: StoreField: r1->field_13 = r0
    //     0xa1b1fc: stur            w0, [x1, #0x13]
    // 0xa1b200: r3 = false
    //     0xa1b200: add             x3, NULL, #0x30  ; false
    // 0xa1b204: ArrayStore: r1[0] = r3  ; List_4
    //     0xa1b204: stur            w3, [x1, #0x17]
    // 0xa1b208: StoreField: r1->field_1b = r3
    //     0xa1b208: stur            w3, [x1, #0x1b]
    // 0xa1b20c: StoreField: r1->field_1f = r3
    //     0xa1b20c: stur            w3, [x1, #0x1f]
    // 0xa1b210: StoreField: r1->field_23 = r3
    //     0xa1b210: stur            w3, [x1, #0x23]
    // 0xa1b214: StoreField: r1->field_27 = r3
    //     0xa1b214: stur            w3, [x1, #0x27]
    // 0xa1b218: StoreField: r1->field_2b = r3
    //     0xa1b218: stur            w3, [x1, #0x2b]
    // 0xa1b21c: ldur            x4, [fp, #-8]
    // 0xa1b220: LoadField: r0 = r4->field_f
    //     0xa1b220: ldur            w0, [x4, #0xf]
    // 0xa1b224: DecompressPointer r0
    //     0xa1b224: add             x0, x0, HEAP, lsl #32
    // 0xa1b228: LoadField: r5 = r0->field_b
    //     0xa1b228: ldur            w5, [x0, #0xb]
    // 0xa1b22c: DecompressPointer r5
    //     0xa1b22c: add             x5, x5, HEAP, lsl #32
    // 0xa1b230: cmp             w5, NULL
    // 0xa1b234: b.eq            #0xa1c2d0
    // 0xa1b238: LoadField: r0 = r5->field_f
    //     0xa1b238: ldur            w0, [x5, #0xf]
    // 0xa1b23c: DecompressPointer r0
    //     0xa1b23c: add             x0, x0, HEAP, lsl #32
    // 0xa1b240: r5 = LoadClassIdInstr(r0)
    //     0xa1b240: ldur            x5, [x0, #-1]
    //     0xa1b244: ubfx            x5, x5, #0xc, #0x14
    // 0xa1b248: r16 = "size"
    //     0xa1b248: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xa1b24c: ldr             x16, [x16, #0x9c0]
    // 0xa1b250: stp             x16, x0, [SP]
    // 0xa1b254: mov             x0, x5
    // 0xa1b258: mov             lr, x0
    // 0xa1b25c: ldr             lr, [x21, lr, lsl #3]
    // 0xa1b260: blr             lr
    // 0xa1b264: tbnz            w0, #4, #0xa1b2d0
    // 0xa1b268: ldur            x2, [fp, #-8]
    // 0xa1b26c: LoadField: r1 = r2->field_13
    //     0xa1b26c: ldur            w1, [x2, #0x13]
    // 0xa1b270: DecompressPointer r1
    //     0xa1b270: add             x1, x1, HEAP, lsl #32
    // 0xa1b274: r0 = of()
    //     0xa1b274: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b278: LoadField: r1 = r0->field_87
    //     0xa1b278: ldur            w1, [x0, #0x87]
    // 0xa1b27c: DecompressPointer r1
    //     0xa1b27c: add             x1, x1, HEAP, lsl #32
    // 0xa1b280: LoadField: r0 = r1->field_7
    //     0xa1b280: ldur            w0, [x1, #7]
    // 0xa1b284: DecompressPointer r0
    //     0xa1b284: add             x0, x0, HEAP, lsl #32
    // 0xa1b288: r16 = Instance_Color
    //     0xa1b288: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1b28c: r30 = 16.000000
    //     0xa1b28c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1b290: ldr             lr, [lr, #0x188]
    // 0xa1b294: stp             lr, x16, [SP]
    // 0xa1b298: mov             x1, x0
    // 0xa1b29c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1b29c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1b2a0: ldr             x4, [x4, #0x9b8]
    // 0xa1b2a4: r0 = copyWith()
    //     0xa1b2a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1b2a8: stur            x0, [fp, #-0x18]
    // 0xa1b2ac: r0 = Text()
    //     0xa1b2ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1b2b0: mov             x1, x0
    // 0xa1b2b4: r0 = "Select Size"
    //     0xa1b2b4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xa1b2b8: ldr             x0, [x0, #0x370]
    // 0xa1b2bc: StoreField: r1->field_b = r0
    //     0xa1b2bc: stur            w0, [x1, #0xb]
    // 0xa1b2c0: ldur            x0, [fp, #-0x18]
    // 0xa1b2c4: StoreField: r1->field_13 = r0
    //     0xa1b2c4: stur            w0, [x1, #0x13]
    // 0xa1b2c8: mov             x0, x1
    // 0xa1b2cc: b               #0xa1b334
    // 0xa1b2d0: ldur            x2, [fp, #-8]
    // 0xa1b2d4: LoadField: r1 = r2->field_13
    //     0xa1b2d4: ldur            w1, [x2, #0x13]
    // 0xa1b2d8: DecompressPointer r1
    //     0xa1b2d8: add             x1, x1, HEAP, lsl #32
    // 0xa1b2dc: r0 = of()
    //     0xa1b2dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b2e0: LoadField: r1 = r0->field_87
    //     0xa1b2e0: ldur            w1, [x0, #0x87]
    // 0xa1b2e4: DecompressPointer r1
    //     0xa1b2e4: add             x1, x1, HEAP, lsl #32
    // 0xa1b2e8: LoadField: r0 = r1->field_7
    //     0xa1b2e8: ldur            w0, [x1, #7]
    // 0xa1b2ec: DecompressPointer r0
    //     0xa1b2ec: add             x0, x0, HEAP, lsl #32
    // 0xa1b2f0: r16 = Instance_Color
    //     0xa1b2f0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1b2f4: r30 = 16.000000
    //     0xa1b2f4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1b2f8: ldr             lr, [lr, #0x188]
    // 0xa1b2fc: stp             lr, x16, [SP]
    // 0xa1b300: mov             x1, x0
    // 0xa1b304: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1b304: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1b308: ldr             x4, [x4, #0x9b8]
    // 0xa1b30c: r0 = copyWith()
    //     0xa1b30c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1b310: stur            x0, [fp, #-0x18]
    // 0xa1b314: r0 = Text()
    //     0xa1b314: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1b318: mov             x1, x0
    // 0xa1b31c: r0 = "Select Variant"
    //     0xa1b31c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52378] "Select Variant"
    //     0xa1b320: ldr             x0, [x0, #0x378]
    // 0xa1b324: StoreField: r1->field_b = r0
    //     0xa1b324: stur            w0, [x1, #0xb]
    // 0xa1b328: ldur            x0, [fp, #-0x18]
    // 0xa1b32c: StoreField: r1->field_13 = r0
    //     0xa1b32c: stur            w0, [x1, #0x13]
    // 0xa1b330: mov             x0, x1
    // 0xa1b334: ldur            x2, [fp, #-8]
    // 0xa1b338: stur            x0, [fp, #-0x18]
    // 0xa1b33c: r0 = Align()
    //     0xa1b33c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa1b340: mov             x1, x0
    // 0xa1b344: r0 = Instance_Alignment
    //     0xa1b344: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa1b348: ldr             x0, [x0, #0xfa0]
    // 0xa1b34c: stur            x1, [fp, #-0x20]
    // 0xa1b350: StoreField: r1->field_f = r0
    //     0xa1b350: stur            w0, [x1, #0xf]
    // 0xa1b354: ldur            x0, [fp, #-0x18]
    // 0xa1b358: StoreField: r1->field_b = r0
    //     0xa1b358: stur            w0, [x1, #0xb]
    // 0xa1b35c: r0 = Padding()
    //     0xa1b35c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1b360: mov             x2, x0
    // 0xa1b364: r0 = Instance_EdgeInsets
    //     0xa1b364: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0xa1b368: ldr             x0, [x0, #0xbe0]
    // 0xa1b36c: stur            x2, [fp, #-0x18]
    // 0xa1b370: StoreField: r2->field_f = r0
    //     0xa1b370: stur            w0, [x2, #0xf]
    // 0xa1b374: ldur            x1, [fp, #-0x20]
    // 0xa1b378: StoreField: r2->field_b = r1
    //     0xa1b378: stur            w1, [x2, #0xb]
    // 0xa1b37c: ldur            x3, [fp, #-8]
    // 0xa1b380: LoadField: r1 = r3->field_f
    //     0xa1b380: ldur            w1, [x3, #0xf]
    // 0xa1b384: DecompressPointer r1
    //     0xa1b384: add             x1, x1, HEAP, lsl #32
    // 0xa1b388: LoadField: r4 = r1->field_1f
    //     0xa1b388: ldur            w4, [x1, #0x1f]
    // 0xa1b38c: DecompressPointer r4
    //     0xa1b38c: add             x4, x4, HEAP, lsl #32
    // 0xa1b390: LoadField: r1 = r4->field_8f
    //     0xa1b390: ldur            w1, [x4, #0x8f]
    // 0xa1b394: DecompressPointer r1
    //     0xa1b394: add             x1, x1, HEAP, lsl #32
    // 0xa1b398: r0 = value()
    //     0xa1b398: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b39c: LoadField: r1 = r0->field_53
    //     0xa1b39c: ldur            w1, [x0, #0x53]
    // 0xa1b3a0: DecompressPointer r1
    //     0xa1b3a0: add             x1, x1, HEAP, lsl #32
    // 0xa1b3a4: cmp             w1, NULL
    // 0xa1b3a8: b.ne            #0xa1b3b4
    // 0xa1b3ac: r0 = Null
    //     0xa1b3ac: mov             x0, NULL
    // 0xa1b3b0: b               #0xa1b3cc
    // 0xa1b3b4: LoadField: r0 = r1->field_b
    //     0xa1b3b4: ldur            w0, [x1, #0xb]
    // 0xa1b3b8: cbnz            w0, #0xa1b3c4
    // 0xa1b3bc: r1 = false
    //     0xa1b3bc: add             x1, NULL, #0x30  ; false
    // 0xa1b3c0: b               #0xa1b3c8
    // 0xa1b3c4: r1 = true
    //     0xa1b3c4: add             x1, NULL, #0x20  ; true
    // 0xa1b3c8: mov             x0, x1
    // 0xa1b3cc: cmp             w0, NULL
    // 0xa1b3d0: b.ne            #0xa1b3d8
    // 0xa1b3d4: r0 = false
    //     0xa1b3d4: add             x0, NULL, #0x30  ; false
    // 0xa1b3d8: ldur            x2, [fp, #-8]
    // 0xa1b3dc: stur            x0, [fp, #-0x20]
    // 0xa1b3e0: LoadField: r1 = r2->field_f
    //     0xa1b3e0: ldur            w1, [x2, #0xf]
    // 0xa1b3e4: DecompressPointer r1
    //     0xa1b3e4: add             x1, x1, HEAP, lsl #32
    // 0xa1b3e8: LoadField: r3 = r1->field_1f
    //     0xa1b3e8: ldur            w3, [x1, #0x1f]
    // 0xa1b3ec: DecompressPointer r3
    //     0xa1b3ec: add             x3, x3, HEAP, lsl #32
    // 0xa1b3f0: LoadField: r1 = r3->field_8f
    //     0xa1b3f0: ldur            w1, [x3, #0x8f]
    // 0xa1b3f4: DecompressPointer r1
    //     0xa1b3f4: add             x1, x1, HEAP, lsl #32
    // 0xa1b3f8: r0 = value()
    //     0xa1b3f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b3fc: LoadField: r1 = r0->field_53
    //     0xa1b3fc: ldur            w1, [x0, #0x53]
    // 0xa1b400: DecompressPointer r1
    //     0xa1b400: add             x1, x1, HEAP, lsl #32
    // 0xa1b404: cmp             w1, NULL
    // 0xa1b408: b.ne            #0xa1b414
    // 0xa1b40c: r4 = Null
    //     0xa1b40c: mov             x4, NULL
    // 0xa1b410: b               #0xa1b41c
    // 0xa1b414: LoadField: r0 = r1->field_b
    //     0xa1b414: ldur            w0, [x1, #0xb]
    // 0xa1b418: mov             x4, x0
    // 0xa1b41c: ldur            x0, [fp, #-8]
    // 0xa1b420: ldur            x3, [fp, #-0x20]
    // 0xa1b424: mov             x2, x0
    // 0xa1b428: stur            x4, [fp, #-0x30]
    // 0xa1b42c: r1 = Function '<anonymous closure>':.
    //     0xa1b42c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56cd0] AnonymousClosure: (0xa1d38c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1b430: ldr             x1, [x1, #0xcd0]
    // 0xa1b434: r0 = AllocateClosure()
    //     0xa1b434: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1b438: stur            x0, [fp, #-0x38]
    // 0xa1b43c: r0 = ListView()
    //     0xa1b43c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa1b440: stur            x0, [fp, #-0x40]
    // 0xa1b444: r16 = true
    //     0xa1b444: add             x16, NULL, #0x20  ; true
    // 0xa1b448: r30 = Instance_Axis
    //     0xa1b448: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1b44c: stp             lr, x16, [SP]
    // 0xa1b450: mov             x1, x0
    // 0xa1b454: ldur            x2, [fp, #-0x38]
    // 0xa1b458: ldur            x3, [fp, #-0x30]
    // 0xa1b45c: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xa1b45c: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xa1b460: ldr             x4, [x4, #0x2d0]
    // 0xa1b464: r0 = ListView.builder()
    //     0xa1b464: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xa1b468: r0 = SizedBox()
    //     0xa1b468: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa1b46c: mov             x1, x0
    // 0xa1b470: r0 = 50.000000
    //     0xa1b470: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xa1b474: ldr             x0, [x0, #0xa90]
    // 0xa1b478: stur            x1, [fp, #-0x30]
    // 0xa1b47c: StoreField: r1->field_13 = r0
    //     0xa1b47c: stur            w0, [x1, #0x13]
    // 0xa1b480: ldur            x0, [fp, #-0x40]
    // 0xa1b484: StoreField: r1->field_b = r0
    //     0xa1b484: stur            w0, [x1, #0xb]
    // 0xa1b488: r0 = Visibility()
    //     0xa1b488: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa1b48c: mov             x2, x0
    // 0xa1b490: ldur            x0, [fp, #-0x30]
    // 0xa1b494: stur            x2, [fp, #-0x38]
    // 0xa1b498: StoreField: r2->field_b = r0
    //     0xa1b498: stur            w0, [x2, #0xb]
    // 0xa1b49c: r0 = Instance_SizedBox
    //     0xa1b49c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa1b4a0: StoreField: r2->field_f = r0
    //     0xa1b4a0: stur            w0, [x2, #0xf]
    // 0xa1b4a4: ldur            x1, [fp, #-0x20]
    // 0xa1b4a8: StoreField: r2->field_13 = r1
    //     0xa1b4a8: stur            w1, [x2, #0x13]
    // 0xa1b4ac: r3 = false
    //     0xa1b4ac: add             x3, NULL, #0x30  ; false
    // 0xa1b4b0: ArrayStore: r2[0] = r3  ; List_4
    //     0xa1b4b0: stur            w3, [x2, #0x17]
    // 0xa1b4b4: StoreField: r2->field_1b = r3
    //     0xa1b4b4: stur            w3, [x2, #0x1b]
    // 0xa1b4b8: StoreField: r2->field_1f = r3
    //     0xa1b4b8: stur            w3, [x2, #0x1f]
    // 0xa1b4bc: StoreField: r2->field_23 = r3
    //     0xa1b4bc: stur            w3, [x2, #0x23]
    // 0xa1b4c0: StoreField: r2->field_27 = r3
    //     0xa1b4c0: stur            w3, [x2, #0x27]
    // 0xa1b4c4: StoreField: r2->field_2b = r3
    //     0xa1b4c4: stur            w3, [x2, #0x2b]
    // 0xa1b4c8: ldur            x4, [fp, #-8]
    // 0xa1b4cc: LoadField: r1 = r4->field_f
    //     0xa1b4cc: ldur            w1, [x4, #0xf]
    // 0xa1b4d0: DecompressPointer r1
    //     0xa1b4d0: add             x1, x1, HEAP, lsl #32
    // 0xa1b4d4: LoadField: r5 = r1->field_1f
    //     0xa1b4d4: ldur            w5, [x1, #0x1f]
    // 0xa1b4d8: DecompressPointer r5
    //     0xa1b4d8: add             x5, x5, HEAP, lsl #32
    // 0xa1b4dc: LoadField: r1 = r5->field_8f
    //     0xa1b4dc: ldur            w1, [x5, #0x8f]
    // 0xa1b4e0: DecompressPointer r1
    //     0xa1b4e0: add             x1, x1, HEAP, lsl #32
    // 0xa1b4e4: r0 = value()
    //     0xa1b4e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b4e8: LoadField: r1 = r0->field_4f
    //     0xa1b4e8: ldur            w1, [x0, #0x4f]
    // 0xa1b4ec: DecompressPointer r1
    //     0xa1b4ec: add             x1, x1, HEAP, lsl #32
    // 0xa1b4f0: cmp             w1, NULL
    // 0xa1b4f4: b.eq            #0xa1b55c
    // 0xa1b4f8: ldur            x2, [fp, #-8]
    // 0xa1b4fc: LoadField: r0 = r2->field_f
    //     0xa1b4fc: ldur            w0, [x2, #0xf]
    // 0xa1b500: DecompressPointer r0
    //     0xa1b500: add             x0, x0, HEAP, lsl #32
    // 0xa1b504: LoadField: r1 = r0->field_1f
    //     0xa1b504: ldur            w1, [x0, #0x1f]
    // 0xa1b508: DecompressPointer r1
    //     0xa1b508: add             x1, x1, HEAP, lsl #32
    // 0xa1b50c: LoadField: r0 = r1->field_8f
    //     0xa1b50c: ldur            w0, [x1, #0x8f]
    // 0xa1b510: DecompressPointer r0
    //     0xa1b510: add             x0, x0, HEAP, lsl #32
    // 0xa1b514: mov             x1, x0
    // 0xa1b518: r0 = value()
    //     0xa1b518: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b51c: LoadField: r1 = r0->field_4f
    //     0xa1b51c: ldur            w1, [x0, #0x4f]
    // 0xa1b520: DecompressPointer r1
    //     0xa1b520: add             x1, x1, HEAP, lsl #32
    // 0xa1b524: cmp             w1, NULL
    // 0xa1b528: b.ne            #0xa1b534
    // 0xa1b52c: r0 = Null
    //     0xa1b52c: mov             x0, NULL
    // 0xa1b530: b               #0xa1b54c
    // 0xa1b534: LoadField: r0 = r1->field_7
    //     0xa1b534: ldur            w0, [x1, #7]
    // 0xa1b538: cbnz            w0, #0xa1b544
    // 0xa1b53c: r1 = false
    //     0xa1b53c: add             x1, NULL, #0x30  ; false
    // 0xa1b540: b               #0xa1b548
    // 0xa1b544: r1 = true
    //     0xa1b544: add             x1, NULL, #0x20  ; true
    // 0xa1b548: mov             x0, x1
    // 0xa1b54c: cmp             w0, NULL
    // 0xa1b550: b.ne            #0xa1b560
    // 0xa1b554: r0 = false
    //     0xa1b554: add             x0, NULL, #0x30  ; false
    // 0xa1b558: b               #0xa1b560
    // 0xa1b55c: r0 = false
    //     0xa1b55c: add             x0, NULL, #0x30  ; false
    // 0xa1b560: ldur            x2, [fp, #-8]
    // 0xa1b564: stur            x0, [fp, #-0x20]
    // 0xa1b568: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa1b568: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa1b56c: ldr             x0, [x0, #0x1c80]
    //     0xa1b570: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa1b574: cmp             w0, w16
    //     0xa1b578: b.ne            #0xa1b584
    //     0xa1b57c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa1b580: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa1b584: r0 = GetNavigation.size()
    //     0xa1b584: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xa1b588: LoadField: d0 = r0->field_7
    //     0xa1b588: ldur            d0, [x0, #7]
    // 0xa1b58c: stur            d0, [fp, #-0x70]
    // 0xa1b590: r0 = Radius()
    //     0xa1b590: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa1b594: d0 = 30.000000
    //     0xa1b594: fmov            d0, #30.00000000
    // 0xa1b598: stur            x0, [fp, #-0x30]
    // 0xa1b59c: StoreField: r0->field_7 = d0
    //     0xa1b59c: stur            d0, [x0, #7]
    // 0xa1b5a0: StoreField: r0->field_f = d0
    //     0xa1b5a0: stur            d0, [x0, #0xf]
    // 0xa1b5a4: r0 = BorderRadius()
    //     0xa1b5a4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa1b5a8: mov             x2, x0
    // 0xa1b5ac: ldur            x0, [fp, #-0x30]
    // 0xa1b5b0: stur            x2, [fp, #-0x40]
    // 0xa1b5b4: StoreField: r2->field_7 = r0
    //     0xa1b5b4: stur            w0, [x2, #7]
    // 0xa1b5b8: StoreField: r2->field_b = r0
    //     0xa1b5b8: stur            w0, [x2, #0xb]
    // 0xa1b5bc: StoreField: r2->field_f = r0
    //     0xa1b5bc: stur            w0, [x2, #0xf]
    // 0xa1b5c0: StoreField: r2->field_13 = r0
    //     0xa1b5c0: stur            w0, [x2, #0x13]
    // 0xa1b5c4: ldur            x0, [fp, #-8]
    // 0xa1b5c8: LoadField: r1 = r0->field_13
    //     0xa1b5c8: ldur            w1, [x0, #0x13]
    // 0xa1b5cc: DecompressPointer r1
    //     0xa1b5cc: add             x1, x1, HEAP, lsl #32
    // 0xa1b5d0: r0 = of()
    //     0xa1b5d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b5d4: LoadField: r2 = r0->field_5b
    //     0xa1b5d4: ldur            w2, [x0, #0x5b]
    // 0xa1b5d8: DecompressPointer r2
    //     0xa1b5d8: add             x2, x2, HEAP, lsl #32
    // 0xa1b5dc: r1 = Null
    //     0xa1b5dc: mov             x1, NULL
    // 0xa1b5e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa1b5e0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa1b5e4: r0 = Border.all()
    //     0xa1b5e4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa1b5e8: stur            x0, [fp, #-0x30]
    // 0xa1b5ec: r0 = BoxDecoration()
    //     0xa1b5ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa1b5f0: mov             x2, x0
    // 0xa1b5f4: r0 = Instance_Color
    //     0xa1b5f4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa1b5f8: stur            x2, [fp, #-0x48]
    // 0xa1b5fc: StoreField: r2->field_7 = r0
    //     0xa1b5fc: stur            w0, [x2, #7]
    // 0xa1b600: ldur            x0, [fp, #-0x30]
    // 0xa1b604: StoreField: r2->field_f = r0
    //     0xa1b604: stur            w0, [x2, #0xf]
    // 0xa1b608: ldur            x0, [fp, #-0x40]
    // 0xa1b60c: StoreField: r2->field_13 = r0
    //     0xa1b60c: stur            w0, [x2, #0x13]
    // 0xa1b610: r0 = Instance_BoxShape
    //     0xa1b610: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1b614: ldr             x0, [x0, #0x80]
    // 0xa1b618: StoreField: r2->field_23 = r0
    //     0xa1b618: stur            w0, [x2, #0x23]
    // 0xa1b61c: ldur            x3, [fp, #-8]
    // 0xa1b620: LoadField: r1 = r3->field_13
    //     0xa1b620: ldur            w1, [x3, #0x13]
    // 0xa1b624: DecompressPointer r1
    //     0xa1b624: add             x1, x1, HEAP, lsl #32
    // 0xa1b628: r0 = of()
    //     0xa1b628: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b62c: LoadField: r1 = r0->field_87
    //     0xa1b62c: ldur            w1, [x0, #0x87]
    // 0xa1b630: DecompressPointer r1
    //     0xa1b630: add             x1, x1, HEAP, lsl #32
    // 0xa1b634: LoadField: r0 = r1->field_7
    //     0xa1b634: ldur            w0, [x1, #7]
    // 0xa1b638: DecompressPointer r0
    //     0xa1b638: add             x0, x0, HEAP, lsl #32
    // 0xa1b63c: r16 = Instance_Color
    //     0xa1b63c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1b640: r30 = 16.000000
    //     0xa1b640: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1b644: ldr             lr, [lr, #0x188]
    // 0xa1b648: stp             lr, x16, [SP]
    // 0xa1b64c: mov             x1, x0
    // 0xa1b650: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1b650: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1b654: ldr             x4, [x4, #0x9b8]
    // 0xa1b658: r0 = copyWith()
    //     0xa1b658: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1b65c: stur            x0, [fp, #-0x30]
    // 0xa1b660: r0 = Text()
    //     0xa1b660: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1b664: mov             x1, x0
    // 0xa1b668: r0 = "Size Chart"
    //     0xa1b668: add             x0, PP, #0x56, lsl #12  ; [pp+0x56cd8] "Size Chart"
    //     0xa1b66c: ldr             x0, [x0, #0xcd8]
    // 0xa1b670: stur            x1, [fp, #-0x40]
    // 0xa1b674: StoreField: r1->field_b = r0
    //     0xa1b674: stur            w0, [x1, #0xb]
    // 0xa1b678: ldur            x0, [fp, #-0x30]
    // 0xa1b67c: StoreField: r1->field_13 = r0
    //     0xa1b67c: stur            w0, [x1, #0x13]
    // 0xa1b680: r0 = Center()
    //     0xa1b680: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa1b684: mov             x1, x0
    // 0xa1b688: r0 = Instance_Alignment
    //     0xa1b688: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa1b68c: ldr             x0, [x0, #0xb10]
    // 0xa1b690: stur            x1, [fp, #-0x50]
    // 0xa1b694: StoreField: r1->field_f = r0
    //     0xa1b694: stur            w0, [x1, #0xf]
    // 0xa1b698: ldur            x0, [fp, #-0x40]
    // 0xa1b69c: StoreField: r1->field_b = r0
    //     0xa1b69c: stur            w0, [x1, #0xb]
    // 0xa1b6a0: ldur            d0, [fp, #-0x70]
    // 0xa1b6a4: r0 = inline_Allocate_Double()
    //     0xa1b6a4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa1b6a8: add             x0, x0, #0x10
    //     0xa1b6ac: cmp             x2, x0
    //     0xa1b6b0: b.ls            #0xa1c2d4
    //     0xa1b6b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xa1b6b8: sub             x0, x0, #0xf
    //     0xa1b6bc: movz            x2, #0xe15c
    //     0xa1b6c0: movk            x2, #0x3, lsl #16
    //     0xa1b6c4: stur            x2, [x0, #-1]
    // 0xa1b6c8: StoreField: r0->field_7 = d0
    //     0xa1b6c8: stur            d0, [x0, #7]
    // 0xa1b6cc: stur            x0, [fp, #-0x30]
    // 0xa1b6d0: r0 = Container()
    //     0xa1b6d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa1b6d4: stur            x0, [fp, #-0x40]
    // 0xa1b6d8: r16 = 48.000000
    //     0xa1b6d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa1b6dc: ldr             x16, [x16, #0xad8]
    // 0xa1b6e0: ldur            lr, [fp, #-0x30]
    // 0xa1b6e4: stp             lr, x16, [SP, #0x10]
    // 0xa1b6e8: ldur            x16, [fp, #-0x48]
    // 0xa1b6ec: ldur            lr, [fp, #-0x50]
    // 0xa1b6f0: stp             lr, x16, [SP]
    // 0xa1b6f4: mov             x1, x0
    // 0xa1b6f8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa1b6f8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa1b6fc: ldr             x4, [x4, #0x8c0]
    // 0xa1b700: r0 = Container()
    //     0xa1b700: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa1b704: r0 = InkWell()
    //     0xa1b704: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa1b708: mov             x3, x0
    // 0xa1b70c: ldur            x0, [fp, #-0x40]
    // 0xa1b710: stur            x3, [fp, #-0x30]
    // 0xa1b714: StoreField: r3->field_b = r0
    //     0xa1b714: stur            w0, [x3, #0xb]
    // 0xa1b718: ldur            x2, [fp, #-8]
    // 0xa1b71c: r1 = Function '<anonymous closure>':.
    //     0xa1b71c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ce0] AnonymousClosure: (0xa1d278), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1b720: ldr             x1, [x1, #0xce0]
    // 0xa1b724: r0 = AllocateClosure()
    //     0xa1b724: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1b728: mov             x1, x0
    // 0xa1b72c: ldur            x0, [fp, #-0x30]
    // 0xa1b730: StoreField: r0->field_f = r1
    //     0xa1b730: stur            w1, [x0, #0xf]
    // 0xa1b734: r1 = true
    //     0xa1b734: add             x1, NULL, #0x20  ; true
    // 0xa1b738: StoreField: r0->field_43 = r1
    //     0xa1b738: stur            w1, [x0, #0x43]
    // 0xa1b73c: r2 = Instance_BoxShape
    //     0xa1b73c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1b740: ldr             x2, [x2, #0x80]
    // 0xa1b744: StoreField: r0->field_47 = r2
    //     0xa1b744: stur            w2, [x0, #0x47]
    // 0xa1b748: StoreField: r0->field_6f = r1
    //     0xa1b748: stur            w1, [x0, #0x6f]
    // 0xa1b74c: r3 = false
    //     0xa1b74c: add             x3, NULL, #0x30  ; false
    // 0xa1b750: StoreField: r0->field_73 = r3
    //     0xa1b750: stur            w3, [x0, #0x73]
    // 0xa1b754: StoreField: r0->field_83 = r1
    //     0xa1b754: stur            w1, [x0, #0x83]
    // 0xa1b758: StoreField: r0->field_7b = r3
    //     0xa1b758: stur            w3, [x0, #0x7b]
    // 0xa1b75c: r0 = Padding()
    //     0xa1b75c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1b760: mov             x1, x0
    // 0xa1b764: r0 = Instance_EdgeInsets
    //     0xa1b764: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xa1b768: ldr             x0, [x0, #0xd0]
    // 0xa1b76c: stur            x1, [fp, #-0x40]
    // 0xa1b770: StoreField: r1->field_f = r0
    //     0xa1b770: stur            w0, [x1, #0xf]
    // 0xa1b774: ldur            x0, [fp, #-0x30]
    // 0xa1b778: StoreField: r1->field_b = r0
    //     0xa1b778: stur            w0, [x1, #0xb]
    // 0xa1b77c: r0 = Visibility()
    //     0xa1b77c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa1b780: mov             x2, x0
    // 0xa1b784: ldur            x0, [fp, #-0x40]
    // 0xa1b788: stur            x2, [fp, #-0x30]
    // 0xa1b78c: StoreField: r2->field_b = r0
    //     0xa1b78c: stur            w0, [x2, #0xb]
    // 0xa1b790: r0 = Instance_SizedBox
    //     0xa1b790: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa1b794: StoreField: r2->field_f = r0
    //     0xa1b794: stur            w0, [x2, #0xf]
    // 0xa1b798: ldur            x0, [fp, #-0x20]
    // 0xa1b79c: StoreField: r2->field_13 = r0
    //     0xa1b79c: stur            w0, [x2, #0x13]
    // 0xa1b7a0: r0 = false
    //     0xa1b7a0: add             x0, NULL, #0x30  ; false
    // 0xa1b7a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1b7a4: stur            w0, [x2, #0x17]
    // 0xa1b7a8: StoreField: r2->field_1b = r0
    //     0xa1b7a8: stur            w0, [x2, #0x1b]
    // 0xa1b7ac: StoreField: r2->field_1f = r0
    //     0xa1b7ac: stur            w0, [x2, #0x1f]
    // 0xa1b7b0: StoreField: r2->field_23 = r0
    //     0xa1b7b0: stur            w0, [x2, #0x23]
    // 0xa1b7b4: StoreField: r2->field_27 = r0
    //     0xa1b7b4: stur            w0, [x2, #0x27]
    // 0xa1b7b8: StoreField: r2->field_2b = r0
    //     0xa1b7b8: stur            w0, [x2, #0x2b]
    // 0xa1b7bc: ldur            x3, [fp, #-8]
    // 0xa1b7c0: LoadField: r1 = r3->field_13
    //     0xa1b7c0: ldur            w1, [x3, #0x13]
    // 0xa1b7c4: DecompressPointer r1
    //     0xa1b7c4: add             x1, x1, HEAP, lsl #32
    // 0xa1b7c8: r0 = of()
    //     0xa1b7c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b7cc: LoadField: r1 = r0->field_87
    //     0xa1b7cc: ldur            w1, [x0, #0x87]
    // 0xa1b7d0: DecompressPointer r1
    //     0xa1b7d0: add             x1, x1, HEAP, lsl #32
    // 0xa1b7d4: LoadField: r0 = r1->field_7
    //     0xa1b7d4: ldur            w0, [x1, #7]
    // 0xa1b7d8: DecompressPointer r0
    //     0xa1b7d8: add             x0, x0, HEAP, lsl #32
    // 0xa1b7dc: r16 = Instance_Color
    //     0xa1b7dc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1b7e0: r30 = 16.000000
    //     0xa1b7e0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1b7e4: ldr             lr, [lr, #0x188]
    // 0xa1b7e8: stp             lr, x16, [SP]
    // 0xa1b7ec: mov             x1, x0
    // 0xa1b7f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1b7f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1b7f4: ldr             x4, [x4, #0x9b8]
    // 0xa1b7f8: r0 = copyWith()
    //     0xa1b7f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1b7fc: stur            x0, [fp, #-0x20]
    // 0xa1b800: r0 = Text()
    //     0xa1b800: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1b804: mov             x2, x0
    // 0xa1b808: r0 = "Select Quantity"
    //     0xa1b808: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ce8] "Select Quantity"
    //     0xa1b80c: ldr             x0, [x0, #0xce8]
    // 0xa1b810: stur            x2, [fp, #-0x40]
    // 0xa1b814: StoreField: r2->field_b = r0
    //     0xa1b814: stur            w0, [x2, #0xb]
    // 0xa1b818: ldur            x0, [fp, #-0x20]
    // 0xa1b81c: StoreField: r2->field_13 = r0
    //     0xa1b81c: stur            w0, [x2, #0x13]
    // 0xa1b820: ldur            x0, [fp, #-8]
    // 0xa1b824: LoadField: r1 = r0->field_13
    //     0xa1b824: ldur            w1, [x0, #0x13]
    // 0xa1b828: DecompressPointer r1
    //     0xa1b828: add             x1, x1, HEAP, lsl #32
    // 0xa1b82c: r0 = of()
    //     0xa1b82c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b830: LoadField: r1 = r0->field_5b
    //     0xa1b830: ldur            w1, [x0, #0x5b]
    // 0xa1b834: DecompressPointer r1
    //     0xa1b834: add             x1, x1, HEAP, lsl #32
    // 0xa1b838: stur            x1, [fp, #-0x20]
    // 0xa1b83c: r0 = ColorFilter()
    //     0xa1b83c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa1b840: mov             x1, x0
    // 0xa1b844: ldur            x0, [fp, #-0x20]
    // 0xa1b848: stur            x1, [fp, #-0x48]
    // 0xa1b84c: StoreField: r1->field_7 = r0
    //     0xa1b84c: stur            w0, [x1, #7]
    // 0xa1b850: r0 = Instance_BlendMode
    //     0xa1b850: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa1b854: ldr             x0, [x0, #0xb30]
    // 0xa1b858: StoreField: r1->field_b = r0
    //     0xa1b858: stur            w0, [x1, #0xb]
    // 0xa1b85c: r2 = 1
    //     0xa1b85c: movz            x2, #0x1
    // 0xa1b860: StoreField: r1->field_13 = r2
    //     0xa1b860: stur            x2, [x1, #0x13]
    // 0xa1b864: r0 = SvgPicture()
    //     0xa1b864: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa1b868: stur            x0, [fp, #-0x20]
    // 0xa1b86c: r16 = Instance_BoxFit
    //     0xa1b86c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xa1b870: ldr             x16, [x16, #0xb18]
    // 0xa1b874: ldur            lr, [fp, #-0x48]
    // 0xa1b878: stp             lr, x16, [SP, #0x10]
    // 0xa1b87c: r16 = 40.000000
    //     0xa1b87c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa1b880: ldr             x16, [x16, #8]
    // 0xa1b884: r30 = 40.000000
    //     0xa1b884: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa1b888: ldr             lr, [lr, #8]
    // 0xa1b88c: stp             lr, x16, [SP]
    // 0xa1b890: mov             x1, x0
    // 0xa1b894: r2 = "assets/images/remove_icon_glass.svg"
    //     0xa1b894: add             x2, PP, #0x56, lsl #12  ; [pp+0x56cf0] "assets/images/remove_icon_glass.svg"
    //     0xa1b898: ldr             x2, [x2, #0xcf0]
    // 0xa1b89c: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x3, fit, 0x2, height, 0x4, width, 0x5, null]
    //     0xa1b89c: add             x4, PP, #0x43, lsl #12  ; [pp+0x436e0] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x3, "fit", 0x2, "height", 0x4, "width", 0x5, Null]
    //     0xa1b8a0: ldr             x4, [x4, #0x6e0]
    // 0xa1b8a4: r0 = SvgPicture.asset()
    //     0xa1b8a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa1b8a8: r0 = InkWell()
    //     0xa1b8a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa1b8ac: mov             x3, x0
    // 0xa1b8b0: ldur            x0, [fp, #-0x20]
    // 0xa1b8b4: stur            x3, [fp, #-0x48]
    // 0xa1b8b8: StoreField: r3->field_b = r0
    //     0xa1b8b8: stur            w0, [x3, #0xb]
    // 0xa1b8bc: ldur            x2, [fp, #-8]
    // 0xa1b8c0: r1 = Function '<anonymous closure>':.
    //     0xa1b8c0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56cf8] AnonymousClosure: (0xa1c498), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1b8c4: ldr             x1, [x1, #0xcf8]
    // 0xa1b8c8: r0 = AllocateClosure()
    //     0xa1b8c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1b8cc: mov             x1, x0
    // 0xa1b8d0: ldur            x0, [fp, #-0x48]
    // 0xa1b8d4: StoreField: r0->field_f = r1
    //     0xa1b8d4: stur            w1, [x0, #0xf]
    // 0xa1b8d8: r2 = true
    //     0xa1b8d8: add             x2, NULL, #0x20  ; true
    // 0xa1b8dc: StoreField: r0->field_43 = r2
    //     0xa1b8dc: stur            w2, [x0, #0x43]
    // 0xa1b8e0: r3 = Instance_BoxShape
    //     0xa1b8e0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1b8e4: ldr             x3, [x3, #0x80]
    // 0xa1b8e8: StoreField: r0->field_47 = r3
    //     0xa1b8e8: stur            w3, [x0, #0x47]
    // 0xa1b8ec: StoreField: r0->field_6f = r2
    //     0xa1b8ec: stur            w2, [x0, #0x6f]
    // 0xa1b8f0: r4 = false
    //     0xa1b8f0: add             x4, NULL, #0x30  ; false
    // 0xa1b8f4: StoreField: r0->field_73 = r4
    //     0xa1b8f4: stur            w4, [x0, #0x73]
    // 0xa1b8f8: StoreField: r0->field_83 = r2
    //     0xa1b8f8: stur            w2, [x0, #0x83]
    // 0xa1b8fc: StoreField: r0->field_7b = r4
    //     0xa1b8fc: stur            w4, [x0, #0x7b]
    // 0xa1b900: ldur            x5, [fp, #-8]
    // 0xa1b904: LoadField: r1 = r5->field_f
    //     0xa1b904: ldur            w1, [x5, #0xf]
    // 0xa1b908: DecompressPointer r1
    //     0xa1b908: add             x1, x1, HEAP, lsl #32
    // 0xa1b90c: LoadField: r6 = r1->field_1f
    //     0xa1b90c: ldur            w6, [x1, #0x1f]
    // 0xa1b910: DecompressPointer r6
    //     0xa1b910: add             x6, x6, HEAP, lsl #32
    // 0xa1b914: r17 = 287
    //     0xa1b914: movz            x17, #0x11f
    // 0xa1b918: ldr             w1, [x6, x17]
    // 0xa1b91c: DecompressPointer r1
    //     0xa1b91c: add             x1, x1, HEAP, lsl #32
    // 0xa1b920: r0 = value()
    //     0xa1b920: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1b924: r1 = 60
    //     0xa1b924: movz            x1, #0x3c
    // 0xa1b928: branchIfSmi(r0, 0xa1b934)
    //     0xa1b928: tbz             w0, #0, #0xa1b934
    // 0xa1b92c: r1 = LoadClassIdInstr(r0)
    //     0xa1b92c: ldur            x1, [x0, #-1]
    //     0xa1b930: ubfx            x1, x1, #0xc, #0x14
    // 0xa1b934: str             x0, [SP]
    // 0xa1b938: mov             x0, x1
    // 0xa1b93c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa1b93c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa1b940: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa1b940: movz            x17, #0x2700
    //     0xa1b944: add             lr, x0, x17
    //     0xa1b948: ldr             lr, [x21, lr, lsl #3]
    //     0xa1b94c: blr             lr
    // 0xa1b950: ldur            x2, [fp, #-8]
    // 0xa1b954: stur            x0, [fp, #-0x20]
    // 0xa1b958: LoadField: r1 = r2->field_13
    //     0xa1b958: ldur            w1, [x2, #0x13]
    // 0xa1b95c: DecompressPointer r1
    //     0xa1b95c: add             x1, x1, HEAP, lsl #32
    // 0xa1b960: r0 = of()
    //     0xa1b960: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b964: LoadField: r1 = r0->field_87
    //     0xa1b964: ldur            w1, [x0, #0x87]
    // 0xa1b968: DecompressPointer r1
    //     0xa1b968: add             x1, x1, HEAP, lsl #32
    // 0xa1b96c: LoadField: r0 = r1->field_7
    //     0xa1b96c: ldur            w0, [x1, #7]
    // 0xa1b970: DecompressPointer r0
    //     0xa1b970: add             x0, x0, HEAP, lsl #32
    // 0xa1b974: ldur            x2, [fp, #-8]
    // 0xa1b978: stur            x0, [fp, #-0x50]
    // 0xa1b97c: LoadField: r1 = r2->field_13
    //     0xa1b97c: ldur            w1, [x2, #0x13]
    // 0xa1b980: DecompressPointer r1
    //     0xa1b980: add             x1, x1, HEAP, lsl #32
    // 0xa1b984: r0 = of()
    //     0xa1b984: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b988: LoadField: r1 = r0->field_5b
    //     0xa1b988: ldur            w1, [x0, #0x5b]
    // 0xa1b98c: DecompressPointer r1
    //     0xa1b98c: add             x1, x1, HEAP, lsl #32
    // 0xa1b990: r16 = 16.000000
    //     0xa1b990: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1b994: ldr             x16, [x16, #0x188]
    // 0xa1b998: stp             x1, x16, [SP]
    // 0xa1b99c: ldur            x1, [fp, #-0x50]
    // 0xa1b9a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1b9a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1b9a4: ldr             x4, [x4, #0xaa0]
    // 0xa1b9a8: r0 = copyWith()
    //     0xa1b9a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1b9ac: stur            x0, [fp, #-0x50]
    // 0xa1b9b0: r0 = Text()
    //     0xa1b9b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1b9b4: mov             x2, x0
    // 0xa1b9b8: ldur            x0, [fp, #-0x20]
    // 0xa1b9bc: stur            x2, [fp, #-0x58]
    // 0xa1b9c0: StoreField: r2->field_b = r0
    //     0xa1b9c0: stur            w0, [x2, #0xb]
    // 0xa1b9c4: ldur            x0, [fp, #-0x50]
    // 0xa1b9c8: StoreField: r2->field_13 = r0
    //     0xa1b9c8: stur            w0, [x2, #0x13]
    // 0xa1b9cc: ldur            x0, [fp, #-8]
    // 0xa1b9d0: LoadField: r1 = r0->field_13
    //     0xa1b9d0: ldur            w1, [x0, #0x13]
    // 0xa1b9d4: DecompressPointer r1
    //     0xa1b9d4: add             x1, x1, HEAP, lsl #32
    // 0xa1b9d8: r0 = of()
    //     0xa1b9d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1b9dc: LoadField: r1 = r0->field_5b
    //     0xa1b9dc: ldur            w1, [x0, #0x5b]
    // 0xa1b9e0: DecompressPointer r1
    //     0xa1b9e0: add             x1, x1, HEAP, lsl #32
    // 0xa1b9e4: stur            x1, [fp, #-0x20]
    // 0xa1b9e8: r0 = ColorFilter()
    //     0xa1b9e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa1b9ec: mov             x1, x0
    // 0xa1b9f0: ldur            x0, [fp, #-0x20]
    // 0xa1b9f4: stur            x1, [fp, #-0x50]
    // 0xa1b9f8: StoreField: r1->field_7 = r0
    //     0xa1b9f8: stur            w0, [x1, #7]
    // 0xa1b9fc: r0 = Instance_BlendMode
    //     0xa1b9fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa1ba00: ldr             x0, [x0, #0xb30]
    // 0xa1ba04: StoreField: r1->field_b = r0
    //     0xa1ba04: stur            w0, [x1, #0xb]
    // 0xa1ba08: r0 = 1
    //     0xa1ba08: movz            x0, #0x1
    // 0xa1ba0c: StoreField: r1->field_13 = r0
    //     0xa1ba0c: stur            x0, [x1, #0x13]
    // 0xa1ba10: r0 = SvgPicture()
    //     0xa1ba10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa1ba14: stur            x0, [fp, #-0x20]
    // 0xa1ba18: r16 = Instance_BoxFit
    //     0xa1ba18: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xa1ba1c: ldr             x16, [x16, #0xb18]
    // 0xa1ba20: ldur            lr, [fp, #-0x50]
    // 0xa1ba24: stp             lr, x16, [SP, #0x10]
    // 0xa1ba28: r16 = 40.000000
    //     0xa1ba28: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa1ba2c: ldr             x16, [x16, #8]
    // 0xa1ba30: r30 = 40.000000
    //     0xa1ba30: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa1ba34: ldr             lr, [lr, #8]
    // 0xa1ba38: stp             lr, x16, [SP]
    // 0xa1ba3c: mov             x1, x0
    // 0xa1ba40: r2 = "assets/images/add_icon_glass.svg"
    //     0xa1ba40: add             x2, PP, #0x56, lsl #12  ; [pp+0x56d00] "assets/images/add_icon_glass.svg"
    //     0xa1ba44: ldr             x2, [x2, #0xd00]
    // 0xa1ba48: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x3, fit, 0x2, height, 0x4, width, 0x5, null]
    //     0xa1ba48: add             x4, PP, #0x43, lsl #12  ; [pp+0x436e0] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x3, "fit", 0x2, "height", 0x4, "width", 0x5, Null]
    //     0xa1ba4c: ldr             x4, [x4, #0x6e0]
    // 0xa1ba50: r0 = SvgPicture.asset()
    //     0xa1ba50: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa1ba54: r0 = InkWell()
    //     0xa1ba54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa1ba58: mov             x3, x0
    // 0xa1ba5c: ldur            x0, [fp, #-0x20]
    // 0xa1ba60: stur            x3, [fp, #-0x50]
    // 0xa1ba64: StoreField: r3->field_b = r0
    //     0xa1ba64: stur            w0, [x3, #0xb]
    // 0xa1ba68: ldur            x2, [fp, #-8]
    // 0xa1ba6c: r1 = Function '<anonymous closure>':.
    //     0xa1ba6c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d08] AnonymousClosure: (0xa1a54c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1ba70: ldr             x1, [x1, #0xd08]
    // 0xa1ba74: r0 = AllocateClosure()
    //     0xa1ba74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1ba78: mov             x1, x0
    // 0xa1ba7c: ldur            x0, [fp, #-0x50]
    // 0xa1ba80: StoreField: r0->field_f = r1
    //     0xa1ba80: stur            w1, [x0, #0xf]
    // 0xa1ba84: r3 = true
    //     0xa1ba84: add             x3, NULL, #0x20  ; true
    // 0xa1ba88: StoreField: r0->field_43 = r3
    //     0xa1ba88: stur            w3, [x0, #0x43]
    // 0xa1ba8c: r1 = Instance_BoxShape
    //     0xa1ba8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1ba90: ldr             x1, [x1, #0x80]
    // 0xa1ba94: StoreField: r0->field_47 = r1
    //     0xa1ba94: stur            w1, [x0, #0x47]
    // 0xa1ba98: StoreField: r0->field_6f = r3
    //     0xa1ba98: stur            w3, [x0, #0x6f]
    // 0xa1ba9c: r4 = false
    //     0xa1ba9c: add             x4, NULL, #0x30  ; false
    // 0xa1baa0: StoreField: r0->field_73 = r4
    //     0xa1baa0: stur            w4, [x0, #0x73]
    // 0xa1baa4: StoreField: r0->field_83 = r3
    //     0xa1baa4: stur            w3, [x0, #0x83]
    // 0xa1baa8: StoreField: r0->field_7b = r4
    //     0xa1baa8: stur            w4, [x0, #0x7b]
    // 0xa1baac: r1 = Null
    //     0xa1baac: mov             x1, NULL
    // 0xa1bab0: r2 = 6
    //     0xa1bab0: movz            x2, #0x6
    // 0xa1bab4: r0 = AllocateArray()
    //     0xa1bab4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1bab8: mov             x2, x0
    // 0xa1babc: ldur            x0, [fp, #-0x48]
    // 0xa1bac0: stur            x2, [fp, #-0x20]
    // 0xa1bac4: StoreField: r2->field_f = r0
    //     0xa1bac4: stur            w0, [x2, #0xf]
    // 0xa1bac8: ldur            x0, [fp, #-0x58]
    // 0xa1bacc: StoreField: r2->field_13 = r0
    //     0xa1bacc: stur            w0, [x2, #0x13]
    // 0xa1bad0: ldur            x0, [fp, #-0x50]
    // 0xa1bad4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1bad4: stur            w0, [x2, #0x17]
    // 0xa1bad8: r1 = <Widget>
    //     0xa1bad8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1badc: r0 = AllocateGrowableArray()
    //     0xa1badc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1bae0: mov             x1, x0
    // 0xa1bae4: ldur            x0, [fp, #-0x20]
    // 0xa1bae8: stur            x1, [fp, #-0x48]
    // 0xa1baec: StoreField: r1->field_f = r0
    //     0xa1baec: stur            w0, [x1, #0xf]
    // 0xa1baf0: r2 = 6
    //     0xa1baf0: movz            x2, #0x6
    // 0xa1baf4: StoreField: r1->field_b = r2
    //     0xa1baf4: stur            w2, [x1, #0xb]
    // 0xa1baf8: r0 = Row()
    //     0xa1baf8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa1bafc: mov             x1, x0
    // 0xa1bb00: r0 = Instance_Axis
    //     0xa1bb00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1bb04: stur            x1, [fp, #-0x20]
    // 0xa1bb08: StoreField: r1->field_f = r0
    //     0xa1bb08: stur            w0, [x1, #0xf]
    // 0xa1bb0c: r2 = Instance_MainAxisAlignment
    //     0xa1bb0c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xa1bb10: ldr             x2, [x2, #0xa8]
    // 0xa1bb14: StoreField: r1->field_13 = r2
    //     0xa1bb14: stur            w2, [x1, #0x13]
    // 0xa1bb18: r2 = Instance_MainAxisSize
    //     0xa1bb18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1bb1c: ldr             x2, [x2, #0xa10]
    // 0xa1bb20: ArrayStore: r1[0] = r2  ; List_4
    //     0xa1bb20: stur            w2, [x1, #0x17]
    // 0xa1bb24: r3 = Instance_CrossAxisAlignment
    //     0xa1bb24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa1bb28: ldr             x3, [x3, #0xa18]
    // 0xa1bb2c: StoreField: r1->field_1b = r3
    //     0xa1bb2c: stur            w3, [x1, #0x1b]
    // 0xa1bb30: r4 = Instance_VerticalDirection
    //     0xa1bb30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1bb34: ldr             x4, [x4, #0xa20]
    // 0xa1bb38: StoreField: r1->field_23 = r4
    //     0xa1bb38: stur            w4, [x1, #0x23]
    // 0xa1bb3c: r5 = Instance_Clip
    //     0xa1bb3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1bb40: ldr             x5, [x5, #0x38]
    // 0xa1bb44: StoreField: r1->field_2b = r5
    //     0xa1bb44: stur            w5, [x1, #0x2b]
    // 0xa1bb48: StoreField: r1->field_2f = rZR
    //     0xa1bb48: stur            xzr, [x1, #0x2f]
    // 0xa1bb4c: ldur            x6, [fp, #-0x48]
    // 0xa1bb50: StoreField: r1->field_b = r6
    //     0xa1bb50: stur            w6, [x1, #0xb]
    // 0xa1bb54: r0 = Container()
    //     0xa1bb54: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa1bb58: stur            x0, [fp, #-0x48]
    // 0xa1bb5c: r16 = 120.000000
    //     0xa1bb5c: add             x16, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xa1bb60: ldr             x16, [x16, #0x3a0]
    // 0xa1bb64: r30 = 45.000000
    //     0xa1bb64: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xa1bb68: ldr             lr, [lr, #0xc88]
    // 0xa1bb6c: stp             lr, x16, [SP, #0x10]
    // 0xa1bb70: r16 = Instance_BoxDecoration
    //     0xa1bb70: add             x16, PP, #0x56, lsl #12  ; [pp+0x56d10] Obj!BoxDecoration@d648f1
    //     0xa1bb74: ldr             x16, [x16, #0xd10]
    // 0xa1bb78: ldur            lr, [fp, #-0x20]
    // 0xa1bb7c: stp             lr, x16, [SP]
    // 0xa1bb80: mov             x1, x0
    // 0xa1bb84: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa1bb84: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa1bb88: ldr             x4, [x4, #0x870]
    // 0xa1bb8c: r0 = Container()
    //     0xa1bb8c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa1bb90: r1 = Null
    //     0xa1bb90: mov             x1, NULL
    // 0xa1bb94: r2 = 6
    //     0xa1bb94: movz            x2, #0x6
    // 0xa1bb98: r0 = AllocateArray()
    //     0xa1bb98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1bb9c: mov             x2, x0
    // 0xa1bba0: ldur            x0, [fp, #-0x40]
    // 0xa1bba4: stur            x2, [fp, #-0x20]
    // 0xa1bba8: StoreField: r2->field_f = r0
    //     0xa1bba8: stur            w0, [x2, #0xf]
    // 0xa1bbac: r16 = Instance_Spacer
    //     0xa1bbac: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa1bbb0: ldr             x16, [x16, #0xf0]
    // 0xa1bbb4: StoreField: r2->field_13 = r16
    //     0xa1bbb4: stur            w16, [x2, #0x13]
    // 0xa1bbb8: ldur            x0, [fp, #-0x48]
    // 0xa1bbbc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1bbbc: stur            w0, [x2, #0x17]
    // 0xa1bbc0: r1 = <Widget>
    //     0xa1bbc0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1bbc4: r0 = AllocateGrowableArray()
    //     0xa1bbc4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1bbc8: mov             x1, x0
    // 0xa1bbcc: ldur            x0, [fp, #-0x20]
    // 0xa1bbd0: stur            x1, [fp, #-0x40]
    // 0xa1bbd4: StoreField: r1->field_f = r0
    //     0xa1bbd4: stur            w0, [x1, #0xf]
    // 0xa1bbd8: r2 = 6
    //     0xa1bbd8: movz            x2, #0x6
    // 0xa1bbdc: StoreField: r1->field_b = r2
    //     0xa1bbdc: stur            w2, [x1, #0xb]
    // 0xa1bbe0: r0 = Row()
    //     0xa1bbe0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa1bbe4: mov             x1, x0
    // 0xa1bbe8: r0 = Instance_Axis
    //     0xa1bbe8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1bbec: stur            x1, [fp, #-0x20]
    // 0xa1bbf0: StoreField: r1->field_f = r0
    //     0xa1bbf0: stur            w0, [x1, #0xf]
    // 0xa1bbf4: r2 = Instance_MainAxisAlignment
    //     0xa1bbf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1bbf8: ldr             x2, [x2, #0xa08]
    // 0xa1bbfc: StoreField: r1->field_13 = r2
    //     0xa1bbfc: stur            w2, [x1, #0x13]
    // 0xa1bc00: r3 = Instance_MainAxisSize
    //     0xa1bc00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1bc04: ldr             x3, [x3, #0xa10]
    // 0xa1bc08: ArrayStore: r1[0] = r3  ; List_4
    //     0xa1bc08: stur            w3, [x1, #0x17]
    // 0xa1bc0c: r4 = Instance_CrossAxisAlignment
    //     0xa1bc0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa1bc10: ldr             x4, [x4, #0xa18]
    // 0xa1bc14: StoreField: r1->field_1b = r4
    //     0xa1bc14: stur            w4, [x1, #0x1b]
    // 0xa1bc18: r5 = Instance_VerticalDirection
    //     0xa1bc18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1bc1c: ldr             x5, [x5, #0xa20]
    // 0xa1bc20: StoreField: r1->field_23 = r5
    //     0xa1bc20: stur            w5, [x1, #0x23]
    // 0xa1bc24: r6 = Instance_Clip
    //     0xa1bc24: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1bc28: ldr             x6, [x6, #0x38]
    // 0xa1bc2c: StoreField: r1->field_2b = r6
    //     0xa1bc2c: stur            w6, [x1, #0x2b]
    // 0xa1bc30: StoreField: r1->field_2f = rZR
    //     0xa1bc30: stur            xzr, [x1, #0x2f]
    // 0xa1bc34: ldur            x7, [fp, #-0x40]
    // 0xa1bc38: StoreField: r1->field_b = r7
    //     0xa1bc38: stur            w7, [x1, #0xb]
    // 0xa1bc3c: r0 = Padding()
    //     0xa1bc3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1bc40: mov             x2, x0
    // 0xa1bc44: r0 = Instance_EdgeInsets
    //     0xa1bc44: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0xa1bc48: ldr             x0, [x0, #0xbe0]
    // 0xa1bc4c: stur            x2, [fp, #-0x40]
    // 0xa1bc50: StoreField: r2->field_f = r0
    //     0xa1bc50: stur            w0, [x2, #0xf]
    // 0xa1bc54: ldur            x0, [fp, #-0x20]
    // 0xa1bc58: StoreField: r2->field_b = r0
    //     0xa1bc58: stur            w0, [x2, #0xb]
    // 0xa1bc5c: ldur            x0, [fp, #-8]
    // 0xa1bc60: LoadField: r1 = r0->field_13
    //     0xa1bc60: ldur            w1, [x0, #0x13]
    // 0xa1bc64: DecompressPointer r1
    //     0xa1bc64: add             x1, x1, HEAP, lsl #32
    // 0xa1bc68: r0 = of()
    //     0xa1bc68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1bc6c: LoadField: r1 = r0->field_5b
    //     0xa1bc6c: ldur            w1, [x0, #0x5b]
    // 0xa1bc70: DecompressPointer r1
    //     0xa1bc70: add             x1, x1, HEAP, lsl #32
    // 0xa1bc74: r0 = LoadClassIdInstr(r1)
    //     0xa1bc74: ldur            x0, [x1, #-1]
    //     0xa1bc78: ubfx            x0, x0, #0xc, #0x14
    // 0xa1bc7c: d0 = 0.070000
    //     0xa1bc7c: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xa1bc80: ldr             d0, [x17, #0x5f8]
    // 0xa1bc84: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa1bc84: sub             lr, x0, #0xffa
    //     0xa1bc88: ldr             lr, [x21, lr, lsl #3]
    //     0xa1bc8c: blr             lr
    // 0xa1bc90: stur            x0, [fp, #-0x20]
    // 0xa1bc94: r0 = Divider()
    //     0xa1bc94: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xa1bc98: mov             x2, x0
    // 0xa1bc9c: ldur            x0, [fp, #-0x20]
    // 0xa1bca0: stur            x2, [fp, #-0x48]
    // 0xa1bca4: StoreField: r2->field_1f = r0
    //     0xa1bca4: stur            w0, [x2, #0x1f]
    // 0xa1bca8: ldur            x0, [fp, #-8]
    // 0xa1bcac: LoadField: r1 = r0->field_13
    //     0xa1bcac: ldur            w1, [x0, #0x13]
    // 0xa1bcb0: DecompressPointer r1
    //     0xa1bcb0: add             x1, x1, HEAP, lsl #32
    // 0xa1bcb4: r0 = of()
    //     0xa1bcb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1bcb8: LoadField: r1 = r0->field_87
    //     0xa1bcb8: ldur            w1, [x0, #0x87]
    // 0xa1bcbc: DecompressPointer r1
    //     0xa1bcbc: add             x1, x1, HEAP, lsl #32
    // 0xa1bcc0: LoadField: r0 = r1->field_2b
    //     0xa1bcc0: ldur            w0, [x1, #0x2b]
    // 0xa1bcc4: DecompressPointer r0
    //     0xa1bcc4: add             x0, x0, HEAP, lsl #32
    // 0xa1bcc8: r16 = Instance_Color
    //     0xa1bcc8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1bccc: r30 = 12.000000
    //     0xa1bccc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa1bcd0: ldr             lr, [lr, #0x9e8]
    // 0xa1bcd4: stp             lr, x16, [SP]
    // 0xa1bcd8: mov             x1, x0
    // 0xa1bcdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1bcdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1bce0: ldr             x4, [x4, #0x9b8]
    // 0xa1bce4: r0 = copyWith()
    //     0xa1bce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1bce8: stur            x0, [fp, #-0x20]
    // 0xa1bcec: r0 = Text()
    //     0xa1bcec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1bcf0: mov             x1, x0
    // 0xa1bcf4: r0 = "Total Amount"
    //     0xa1bcf4: add             x0, PP, #0x56, lsl #12  ; [pp+0x56d18] "Total Amount"
    //     0xa1bcf8: ldr             x0, [x0, #0xd18]
    // 0xa1bcfc: stur            x1, [fp, #-0x50]
    // 0xa1bd00: StoreField: r1->field_b = r0
    //     0xa1bd00: stur            w0, [x1, #0xb]
    // 0xa1bd04: ldur            x0, [fp, #-0x20]
    // 0xa1bd08: StoreField: r1->field_13 = r0
    //     0xa1bd08: stur            w0, [x1, #0x13]
    // 0xa1bd0c: ldur            x2, [fp, #-8]
    // 0xa1bd10: LoadField: r0 = r2->field_f
    //     0xa1bd10: ldur            w0, [x2, #0xf]
    // 0xa1bd14: DecompressPointer r0
    //     0xa1bd14: add             x0, x0, HEAP, lsl #32
    // 0xa1bd18: LoadField: r3 = r0->field_b
    //     0xa1bd18: ldur            w3, [x0, #0xb]
    // 0xa1bd1c: DecompressPointer r3
    //     0xa1bd1c: add             x3, x3, HEAP, lsl #32
    // 0xa1bd20: cmp             w3, NULL
    // 0xa1bd24: b.eq            #0xa1c2ec
    // 0xa1bd28: LoadField: r0 = r3->field_b
    //     0xa1bd28: ldur            w0, [x3, #0xb]
    // 0xa1bd2c: DecompressPointer r0
    //     0xa1bd2c: add             x0, x0, HEAP, lsl #32
    // 0xa1bd30: LoadField: r3 = r0->field_b
    //     0xa1bd30: ldur            w3, [x0, #0xb]
    // 0xa1bd34: DecompressPointer r3
    //     0xa1bd34: add             x3, x3, HEAP, lsl #32
    // 0xa1bd38: cmp             w3, NULL
    // 0xa1bd3c: b.ne            #0xa1bd48
    // 0xa1bd40: r9 = Null
    //     0xa1bd40: mov             x9, NULL
    // 0xa1bd44: b               #0xa1bd54
    // 0xa1bd48: LoadField: r0 = r3->field_1f
    //     0xa1bd48: ldur            w0, [x3, #0x1f]
    // 0xa1bd4c: DecompressPointer r0
    //     0xa1bd4c: add             x0, x0, HEAP, lsl #32
    // 0xa1bd50: mov             x9, x0
    // 0xa1bd54: ldur            x8, [fp, #-0x10]
    // 0xa1bd58: ldur            x7, [fp, #-0x28]
    // 0xa1bd5c: ldur            x6, [fp, #-0x18]
    // 0xa1bd60: ldur            x5, [fp, #-0x38]
    // 0xa1bd64: ldur            x4, [fp, #-0x30]
    // 0xa1bd68: ldur            x3, [fp, #-0x40]
    // 0xa1bd6c: ldur            x0, [fp, #-0x48]
    // 0xa1bd70: str             x9, [SP]
    // 0xa1bd74: r0 = _interpolateSingle()
    //     0xa1bd74: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa1bd78: ldur            x2, [fp, #-8]
    // 0xa1bd7c: stur            x0, [fp, #-0x20]
    // 0xa1bd80: LoadField: r1 = r2->field_13
    //     0xa1bd80: ldur            w1, [x2, #0x13]
    // 0xa1bd84: DecompressPointer r1
    //     0xa1bd84: add             x1, x1, HEAP, lsl #32
    // 0xa1bd88: r0 = of()
    //     0xa1bd88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1bd8c: LoadField: r1 = r0->field_87
    //     0xa1bd8c: ldur            w1, [x0, #0x87]
    // 0xa1bd90: DecompressPointer r1
    //     0xa1bd90: add             x1, x1, HEAP, lsl #32
    // 0xa1bd94: LoadField: r0 = r1->field_7
    //     0xa1bd94: ldur            w0, [x1, #7]
    // 0xa1bd98: DecompressPointer r0
    //     0xa1bd98: add             x0, x0, HEAP, lsl #32
    // 0xa1bd9c: stur            x0, [fp, #-0x58]
    // 0xa1bda0: r1 = Instance_Color
    //     0xa1bda0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1bda4: d0 = 0.700000
    //     0xa1bda4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa1bda8: ldr             d0, [x17, #0xf48]
    // 0xa1bdac: r0 = withOpacity()
    //     0xa1bdac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa1bdb0: r16 = 16.000000
    //     0xa1bdb0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1bdb4: ldr             x16, [x16, #0x188]
    // 0xa1bdb8: stp             x16, x0, [SP]
    // 0xa1bdbc: ldur            x1, [fp, #-0x58]
    // 0xa1bdc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1bdc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1bdc4: ldr             x4, [x4, #0x9b8]
    // 0xa1bdc8: r0 = copyWith()
    //     0xa1bdc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1bdcc: stur            x0, [fp, #-0x58]
    // 0xa1bdd0: r0 = Text()
    //     0xa1bdd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1bdd4: mov             x3, x0
    // 0xa1bdd8: ldur            x0, [fp, #-0x20]
    // 0xa1bddc: stur            x3, [fp, #-0x60]
    // 0xa1bde0: StoreField: r3->field_b = r0
    //     0xa1bde0: stur            w0, [x3, #0xb]
    // 0xa1bde4: ldur            x0, [fp, #-0x58]
    // 0xa1bde8: StoreField: r3->field_13 = r0
    //     0xa1bde8: stur            w0, [x3, #0x13]
    // 0xa1bdec: r1 = Null
    //     0xa1bdec: mov             x1, NULL
    // 0xa1bdf0: r2 = 4
    //     0xa1bdf0: movz            x2, #0x4
    // 0xa1bdf4: r0 = AllocateArray()
    //     0xa1bdf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1bdf8: mov             x2, x0
    // 0xa1bdfc: ldur            x0, [fp, #-0x50]
    // 0xa1be00: stur            x2, [fp, #-0x20]
    // 0xa1be04: StoreField: r2->field_f = r0
    //     0xa1be04: stur            w0, [x2, #0xf]
    // 0xa1be08: ldur            x0, [fp, #-0x60]
    // 0xa1be0c: StoreField: r2->field_13 = r0
    //     0xa1be0c: stur            w0, [x2, #0x13]
    // 0xa1be10: r1 = <Widget>
    //     0xa1be10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1be14: r0 = AllocateGrowableArray()
    //     0xa1be14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1be18: mov             x1, x0
    // 0xa1be1c: ldur            x0, [fp, #-0x20]
    // 0xa1be20: stur            x1, [fp, #-0x50]
    // 0xa1be24: StoreField: r1->field_f = r0
    //     0xa1be24: stur            w0, [x1, #0xf]
    // 0xa1be28: r2 = 4
    //     0xa1be28: movz            x2, #0x4
    // 0xa1be2c: StoreField: r1->field_b = r2
    //     0xa1be2c: stur            w2, [x1, #0xb]
    // 0xa1be30: r0 = Column()
    //     0xa1be30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa1be34: mov             x1, x0
    // 0xa1be38: r0 = Instance_Axis
    //     0xa1be38: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa1be3c: stur            x1, [fp, #-0x20]
    // 0xa1be40: StoreField: r1->field_f = r0
    //     0xa1be40: stur            w0, [x1, #0xf]
    // 0xa1be44: r2 = Instance_MainAxisAlignment
    //     0xa1be44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1be48: ldr             x2, [x2, #0xa08]
    // 0xa1be4c: StoreField: r1->field_13 = r2
    //     0xa1be4c: stur            w2, [x1, #0x13]
    // 0xa1be50: r3 = Instance_MainAxisSize
    //     0xa1be50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1be54: ldr             x3, [x3, #0xa10]
    // 0xa1be58: ArrayStore: r1[0] = r3  ; List_4
    //     0xa1be58: stur            w3, [x1, #0x17]
    // 0xa1be5c: r4 = Instance_CrossAxisAlignment
    //     0xa1be5c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa1be60: ldr             x4, [x4, #0x890]
    // 0xa1be64: StoreField: r1->field_1b = r4
    //     0xa1be64: stur            w4, [x1, #0x1b]
    // 0xa1be68: r5 = Instance_VerticalDirection
    //     0xa1be68: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1be6c: ldr             x5, [x5, #0xa20]
    // 0xa1be70: StoreField: r1->field_23 = r5
    //     0xa1be70: stur            w5, [x1, #0x23]
    // 0xa1be74: r6 = Instance_Clip
    //     0xa1be74: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1be78: ldr             x6, [x6, #0x38]
    // 0xa1be7c: StoreField: r1->field_2b = r6
    //     0xa1be7c: stur            w6, [x1, #0x2b]
    // 0xa1be80: StoreField: r1->field_2f = rZR
    //     0xa1be80: stur            xzr, [x1, #0x2f]
    // 0xa1be84: ldur            x7, [fp, #-0x50]
    // 0xa1be88: StoreField: r1->field_b = r7
    //     0xa1be88: stur            w7, [x1, #0xb]
    // 0xa1be8c: r16 = <EdgeInsets>
    //     0xa1be8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa1be90: ldr             x16, [x16, #0xda0]
    // 0xa1be94: r30 = Instance_EdgeInsets
    //     0xa1be94: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0xa1be98: ldr             lr, [lr, #0x28]
    // 0xa1be9c: stp             lr, x16, [SP]
    // 0xa1bea0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa1bea0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa1bea4: r0 = all()
    //     0xa1bea4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa1bea8: ldur            x2, [fp, #-8]
    // 0xa1beac: stur            x0, [fp, #-0x50]
    // 0xa1beb0: LoadField: r1 = r2->field_13
    //     0xa1beb0: ldur            w1, [x2, #0x13]
    // 0xa1beb4: DecompressPointer r1
    //     0xa1beb4: add             x1, x1, HEAP, lsl #32
    // 0xa1beb8: r0 = of()
    //     0xa1beb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1bebc: LoadField: r1 = r0->field_5b
    //     0xa1bebc: ldur            w1, [x0, #0x5b]
    // 0xa1bec0: DecompressPointer r1
    //     0xa1bec0: add             x1, x1, HEAP, lsl #32
    // 0xa1bec4: r16 = <Color?>
    //     0xa1bec4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac0] TypeArguments: <Color?>
    //     0xa1bec8: ldr             x16, [x16, #0xac0]
    // 0xa1becc: stp             x1, x16, [SP]
    // 0xa1bed0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa1bed0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa1bed4: r0 = all()
    //     0xa1bed4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa1bed8: stur            x0, [fp, #-0x58]
    // 0xa1bedc: r0 = Radius()
    //     0xa1bedc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa1bee0: d0 = 30.000000
    //     0xa1bee0: fmov            d0, #30.00000000
    // 0xa1bee4: stur            x0, [fp, #-0x60]
    // 0xa1bee8: StoreField: r0->field_7 = d0
    //     0xa1bee8: stur            d0, [x0, #7]
    // 0xa1beec: StoreField: r0->field_f = d0
    //     0xa1beec: stur            d0, [x0, #0xf]
    // 0xa1bef0: r0 = BorderRadius()
    //     0xa1bef0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa1bef4: mov             x1, x0
    // 0xa1bef8: ldur            x0, [fp, #-0x60]
    // 0xa1befc: stur            x1, [fp, #-0x68]
    // 0xa1bf00: StoreField: r1->field_7 = r0
    //     0xa1bf00: stur            w0, [x1, #7]
    // 0xa1bf04: StoreField: r1->field_b = r0
    //     0xa1bf04: stur            w0, [x1, #0xb]
    // 0xa1bf08: StoreField: r1->field_f = r0
    //     0xa1bf08: stur            w0, [x1, #0xf]
    // 0xa1bf0c: StoreField: r1->field_13 = r0
    //     0xa1bf0c: stur            w0, [x1, #0x13]
    // 0xa1bf10: r0 = RoundedRectangleBorder()
    //     0xa1bf10: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa1bf14: mov             x1, x0
    // 0xa1bf18: ldur            x0, [fp, #-0x68]
    // 0xa1bf1c: StoreField: r1->field_b = r0
    //     0xa1bf1c: stur            w0, [x1, #0xb]
    // 0xa1bf20: r0 = Instance_BorderSide
    //     0xa1bf20: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa1bf24: ldr             x0, [x0, #0xe20]
    // 0xa1bf28: StoreField: r1->field_7 = r0
    //     0xa1bf28: stur            w0, [x1, #7]
    // 0xa1bf2c: r16 = <RoundedRectangleBorder>
    //     0xa1bf2c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa1bf30: ldr             x16, [x16, #0xf78]
    // 0xa1bf34: stp             x1, x16, [SP]
    // 0xa1bf38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa1bf38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa1bf3c: r0 = all()
    //     0xa1bf3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa1bf40: stur            x0, [fp, #-0x60]
    // 0xa1bf44: r0 = ButtonStyle()
    //     0xa1bf44: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa1bf48: mov             x1, x0
    // 0xa1bf4c: ldur            x0, [fp, #-0x58]
    // 0xa1bf50: stur            x1, [fp, #-0x68]
    // 0xa1bf54: StoreField: r1->field_b = r0
    //     0xa1bf54: stur            w0, [x1, #0xb]
    // 0xa1bf58: ldur            x0, [fp, #-0x50]
    // 0xa1bf5c: StoreField: r1->field_23 = r0
    //     0xa1bf5c: stur            w0, [x1, #0x23]
    // 0xa1bf60: ldur            x0, [fp, #-0x60]
    // 0xa1bf64: StoreField: r1->field_43 = r0
    //     0xa1bf64: stur            w0, [x1, #0x43]
    // 0xa1bf68: r0 = TextButtonThemeData()
    //     0xa1bf68: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa1bf6c: mov             x2, x0
    // 0xa1bf70: ldur            x0, [fp, #-0x68]
    // 0xa1bf74: stur            x2, [fp, #-0x50]
    // 0xa1bf78: StoreField: r2->field_7 = r0
    //     0xa1bf78: stur            w0, [x2, #7]
    // 0xa1bf7c: r1 = "continue"
    //     0xa1bf7c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d640] "continue"
    //     0xa1bf80: ldr             x1, [x1, #0x640]
    // 0xa1bf84: r0 = capitalizeFirstWord()
    //     0xa1bf84: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa1bf88: ldur            x2, [fp, #-8]
    // 0xa1bf8c: stur            x0, [fp, #-0x58]
    // 0xa1bf90: LoadField: r1 = r2->field_13
    //     0xa1bf90: ldur            w1, [x2, #0x13]
    // 0xa1bf94: DecompressPointer r1
    //     0xa1bf94: add             x1, x1, HEAP, lsl #32
    // 0xa1bf98: r0 = of()
    //     0xa1bf98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1bf9c: LoadField: r1 = r0->field_87
    //     0xa1bf9c: ldur            w1, [x0, #0x87]
    // 0xa1bfa0: DecompressPointer r1
    //     0xa1bfa0: add             x1, x1, HEAP, lsl #32
    // 0xa1bfa4: LoadField: r0 = r1->field_7
    //     0xa1bfa4: ldur            w0, [x1, #7]
    // 0xa1bfa8: DecompressPointer r0
    //     0xa1bfa8: add             x0, x0, HEAP, lsl #32
    // 0xa1bfac: r16 = Instance_Color
    //     0xa1bfac: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa1bfb0: r30 = 16.000000
    //     0xa1bfb0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa1bfb4: ldr             lr, [lr, #0x188]
    // 0xa1bfb8: stp             lr, x16, [SP]
    // 0xa1bfbc: mov             x1, x0
    // 0xa1bfc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa1bfc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa1bfc4: ldr             x4, [x4, #0x9b8]
    // 0xa1bfc8: r0 = copyWith()
    //     0xa1bfc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1bfcc: stur            x0, [fp, #-0x60]
    // 0xa1bfd0: r0 = Text()
    //     0xa1bfd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1bfd4: mov             x3, x0
    // 0xa1bfd8: ldur            x0, [fp, #-0x58]
    // 0xa1bfdc: stur            x3, [fp, #-0x68]
    // 0xa1bfe0: StoreField: r3->field_b = r0
    //     0xa1bfe0: stur            w0, [x3, #0xb]
    // 0xa1bfe4: ldur            x0, [fp, #-0x60]
    // 0xa1bfe8: StoreField: r3->field_13 = r0
    //     0xa1bfe8: stur            w0, [x3, #0x13]
    // 0xa1bfec: ldur            x2, [fp, #-8]
    // 0xa1bff0: r1 = Function '<anonymous closure>':.
    //     0xa1bff0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d20] AnonymousClosure: (0xa1c320), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1bff4: ldr             x1, [x1, #0xd20]
    // 0xa1bff8: r0 = AllocateClosure()
    //     0xa1bff8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1bffc: stur            x0, [fp, #-8]
    // 0xa1c000: r0 = TextButton()
    //     0xa1c000: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa1c004: mov             x1, x0
    // 0xa1c008: ldur            x0, [fp, #-8]
    // 0xa1c00c: stur            x1, [fp, #-0x58]
    // 0xa1c010: StoreField: r1->field_b = r0
    //     0xa1c010: stur            w0, [x1, #0xb]
    // 0xa1c014: r0 = false
    //     0xa1c014: add             x0, NULL, #0x30  ; false
    // 0xa1c018: StoreField: r1->field_27 = r0
    //     0xa1c018: stur            w0, [x1, #0x27]
    // 0xa1c01c: r0 = true
    //     0xa1c01c: add             x0, NULL, #0x20  ; true
    // 0xa1c020: StoreField: r1->field_2f = r0
    //     0xa1c020: stur            w0, [x1, #0x2f]
    // 0xa1c024: ldur            x0, [fp, #-0x68]
    // 0xa1c028: StoreField: r1->field_37 = r0
    //     0xa1c028: stur            w0, [x1, #0x37]
    // 0xa1c02c: r0 = TextButtonTheme()
    //     0xa1c02c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa1c030: mov             x3, x0
    // 0xa1c034: ldur            x0, [fp, #-0x50]
    // 0xa1c038: stur            x3, [fp, #-8]
    // 0xa1c03c: StoreField: r3->field_f = r0
    //     0xa1c03c: stur            w0, [x3, #0xf]
    // 0xa1c040: ldur            x0, [fp, #-0x58]
    // 0xa1c044: StoreField: r3->field_b = r0
    //     0xa1c044: stur            w0, [x3, #0xb]
    // 0xa1c048: r1 = Null
    //     0xa1c048: mov             x1, NULL
    // 0xa1c04c: r2 = 6
    //     0xa1c04c: movz            x2, #0x6
    // 0xa1c050: r0 = AllocateArray()
    //     0xa1c050: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1c054: mov             x2, x0
    // 0xa1c058: ldur            x0, [fp, #-0x20]
    // 0xa1c05c: stur            x2, [fp, #-0x50]
    // 0xa1c060: StoreField: r2->field_f = r0
    //     0xa1c060: stur            w0, [x2, #0xf]
    // 0xa1c064: r16 = Instance_Spacer
    //     0xa1c064: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa1c068: ldr             x16, [x16, #0xf0]
    // 0xa1c06c: StoreField: r2->field_13 = r16
    //     0xa1c06c: stur            w16, [x2, #0x13]
    // 0xa1c070: ldur            x0, [fp, #-8]
    // 0xa1c074: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1c074: stur            w0, [x2, #0x17]
    // 0xa1c078: r1 = <Widget>
    //     0xa1c078: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1c07c: r0 = AllocateGrowableArray()
    //     0xa1c07c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1c080: mov             x1, x0
    // 0xa1c084: ldur            x0, [fp, #-0x50]
    // 0xa1c088: stur            x1, [fp, #-8]
    // 0xa1c08c: StoreField: r1->field_f = r0
    //     0xa1c08c: stur            w0, [x1, #0xf]
    // 0xa1c090: r0 = 6
    //     0xa1c090: movz            x0, #0x6
    // 0xa1c094: StoreField: r1->field_b = r0
    //     0xa1c094: stur            w0, [x1, #0xb]
    // 0xa1c098: r0 = Row()
    //     0xa1c098: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa1c09c: mov             x3, x0
    // 0xa1c0a0: r0 = Instance_Axis
    //     0xa1c0a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1c0a4: stur            x3, [fp, #-0x20]
    // 0xa1c0a8: StoreField: r3->field_f = r0
    //     0xa1c0a8: stur            w0, [x3, #0xf]
    // 0xa1c0ac: r4 = Instance_MainAxisAlignment
    //     0xa1c0ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1c0b0: ldr             x4, [x4, #0xa08]
    // 0xa1c0b4: StoreField: r3->field_13 = r4
    //     0xa1c0b4: stur            w4, [x3, #0x13]
    // 0xa1c0b8: r5 = Instance_MainAxisSize
    //     0xa1c0b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1c0bc: ldr             x5, [x5, #0xa10]
    // 0xa1c0c0: ArrayStore: r3[0] = r5  ; List_4
    //     0xa1c0c0: stur            w5, [x3, #0x17]
    // 0xa1c0c4: r1 = Instance_CrossAxisAlignment
    //     0xa1c0c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa1c0c8: ldr             x1, [x1, #0xa18]
    // 0xa1c0cc: StoreField: r3->field_1b = r1
    //     0xa1c0cc: stur            w1, [x3, #0x1b]
    // 0xa1c0d0: r6 = Instance_VerticalDirection
    //     0xa1c0d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1c0d4: ldr             x6, [x6, #0xa20]
    // 0xa1c0d8: StoreField: r3->field_23 = r6
    //     0xa1c0d8: stur            w6, [x3, #0x23]
    // 0xa1c0dc: r7 = Instance_Clip
    //     0xa1c0dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1c0e0: ldr             x7, [x7, #0x38]
    // 0xa1c0e4: StoreField: r3->field_2b = r7
    //     0xa1c0e4: stur            w7, [x3, #0x2b]
    // 0xa1c0e8: StoreField: r3->field_2f = rZR
    //     0xa1c0e8: stur            xzr, [x3, #0x2f]
    // 0xa1c0ec: ldur            x1, [fp, #-8]
    // 0xa1c0f0: StoreField: r3->field_b = r1
    //     0xa1c0f0: stur            w1, [x3, #0xb]
    // 0xa1c0f4: r1 = Null
    //     0xa1c0f4: mov             x1, NULL
    // 0xa1c0f8: r2 = 4
    //     0xa1c0f8: movz            x2, #0x4
    // 0xa1c0fc: r0 = AllocateArray()
    //     0xa1c0fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1c100: mov             x2, x0
    // 0xa1c104: ldur            x0, [fp, #-0x48]
    // 0xa1c108: stur            x2, [fp, #-8]
    // 0xa1c10c: StoreField: r2->field_f = r0
    //     0xa1c10c: stur            w0, [x2, #0xf]
    // 0xa1c110: ldur            x0, [fp, #-0x20]
    // 0xa1c114: StoreField: r2->field_13 = r0
    //     0xa1c114: stur            w0, [x2, #0x13]
    // 0xa1c118: r1 = <Widget>
    //     0xa1c118: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1c11c: r0 = AllocateGrowableArray()
    //     0xa1c11c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1c120: mov             x1, x0
    // 0xa1c124: ldur            x0, [fp, #-8]
    // 0xa1c128: stur            x1, [fp, #-0x20]
    // 0xa1c12c: StoreField: r1->field_f = r0
    //     0xa1c12c: stur            w0, [x1, #0xf]
    // 0xa1c130: r0 = 4
    //     0xa1c130: movz            x0, #0x4
    // 0xa1c134: StoreField: r1->field_b = r0
    //     0xa1c134: stur            w0, [x1, #0xb]
    // 0xa1c138: r0 = Column()
    //     0xa1c138: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa1c13c: mov             x1, x0
    // 0xa1c140: r0 = Instance_Axis
    //     0xa1c140: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa1c144: stur            x1, [fp, #-8]
    // 0xa1c148: StoreField: r1->field_f = r0
    //     0xa1c148: stur            w0, [x1, #0xf]
    // 0xa1c14c: r0 = Instance_MainAxisAlignment
    //     0xa1c14c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa1c150: ldr             x0, [x0, #0xa08]
    // 0xa1c154: StoreField: r1->field_13 = r0
    //     0xa1c154: stur            w0, [x1, #0x13]
    // 0xa1c158: r0 = Instance_MainAxisSize
    //     0xa1c158: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa1c15c: ldr             x0, [x0, #0xa10]
    // 0xa1c160: ArrayStore: r1[0] = r0  ; List_4
    //     0xa1c160: stur            w0, [x1, #0x17]
    // 0xa1c164: r0 = Instance_CrossAxisAlignment
    //     0xa1c164: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa1c168: ldr             x0, [x0, #0x890]
    // 0xa1c16c: StoreField: r1->field_1b = r0
    //     0xa1c16c: stur            w0, [x1, #0x1b]
    // 0xa1c170: r0 = Instance_VerticalDirection
    //     0xa1c170: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1c174: ldr             x0, [x0, #0xa20]
    // 0xa1c178: StoreField: r1->field_23 = r0
    //     0xa1c178: stur            w0, [x1, #0x23]
    // 0xa1c17c: r2 = Instance_Clip
    //     0xa1c17c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1c180: ldr             x2, [x2, #0x38]
    // 0xa1c184: StoreField: r1->field_2b = r2
    //     0xa1c184: stur            w2, [x1, #0x2b]
    // 0xa1c188: StoreField: r1->field_2f = rZR
    //     0xa1c188: stur            xzr, [x1, #0x2f]
    // 0xa1c18c: ldur            x3, [fp, #-0x20]
    // 0xa1c190: StoreField: r1->field_b = r3
    //     0xa1c190: stur            w3, [x1, #0xb]
    // 0xa1c194: r0 = Padding()
    //     0xa1c194: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1c198: mov             x3, x0
    // 0xa1c19c: r0 = Instance_EdgeInsets
    //     0xa1c19c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d70] Obj!EdgeInsets@d56e11
    //     0xa1c1a0: ldr             x0, [x0, #0xd70]
    // 0xa1c1a4: stur            x3, [fp, #-0x20]
    // 0xa1c1a8: StoreField: r3->field_f = r0
    //     0xa1c1a8: stur            w0, [x3, #0xf]
    // 0xa1c1ac: ldur            x0, [fp, #-8]
    // 0xa1c1b0: StoreField: r3->field_b = r0
    //     0xa1c1b0: stur            w0, [x3, #0xb]
    // 0xa1c1b4: r1 = Null
    //     0xa1c1b4: mov             x1, NULL
    // 0xa1c1b8: r2 = 14
    //     0xa1c1b8: movz            x2, #0xe
    // 0xa1c1bc: r0 = AllocateArray()
    //     0xa1c1bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa1c1c0: mov             x2, x0
    // 0xa1c1c4: ldur            x0, [fp, #-0x10]
    // 0xa1c1c8: stur            x2, [fp, #-8]
    // 0xa1c1cc: StoreField: r2->field_f = r0
    //     0xa1c1cc: stur            w0, [x2, #0xf]
    // 0xa1c1d0: ldur            x0, [fp, #-0x28]
    // 0xa1c1d4: StoreField: r2->field_13 = r0
    //     0xa1c1d4: stur            w0, [x2, #0x13]
    // 0xa1c1d8: ldur            x0, [fp, #-0x18]
    // 0xa1c1dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xa1c1dc: stur            w0, [x2, #0x17]
    // 0xa1c1e0: ldur            x0, [fp, #-0x38]
    // 0xa1c1e4: StoreField: r2->field_1b = r0
    //     0xa1c1e4: stur            w0, [x2, #0x1b]
    // 0xa1c1e8: ldur            x0, [fp, #-0x30]
    // 0xa1c1ec: StoreField: r2->field_1f = r0
    //     0xa1c1ec: stur            w0, [x2, #0x1f]
    // 0xa1c1f0: ldur            x0, [fp, #-0x40]
    // 0xa1c1f4: StoreField: r2->field_23 = r0
    //     0xa1c1f4: stur            w0, [x2, #0x23]
    // 0xa1c1f8: ldur            x0, [fp, #-0x20]
    // 0xa1c1fc: StoreField: r2->field_27 = r0
    //     0xa1c1fc: stur            w0, [x2, #0x27]
    // 0xa1c200: r1 = <Widget>
    //     0xa1c200: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa1c204: r0 = AllocateGrowableArray()
    //     0xa1c204: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa1c208: mov             x1, x0
    // 0xa1c20c: ldur            x0, [fp, #-8]
    // 0xa1c210: stur            x1, [fp, #-0x10]
    // 0xa1c214: StoreField: r1->field_f = r0
    //     0xa1c214: stur            w0, [x1, #0xf]
    // 0xa1c218: r0 = 14
    //     0xa1c218: movz            x0, #0xe
    // 0xa1c21c: StoreField: r1->field_b = r0
    //     0xa1c21c: stur            w0, [x1, #0xb]
    // 0xa1c220: r0 = Wrap()
    //     0xa1c220: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xa1c224: mov             x1, x0
    // 0xa1c228: r0 = Instance_Axis
    //     0xa1c228: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa1c22c: stur            x1, [fp, #-8]
    // 0xa1c230: StoreField: r1->field_f = r0
    //     0xa1c230: stur            w0, [x1, #0xf]
    // 0xa1c234: r0 = Instance_WrapAlignment
    //     0xa1c234: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xa1c238: ldr             x0, [x0, #0x6e8]
    // 0xa1c23c: StoreField: r1->field_13 = r0
    //     0xa1c23c: stur            w0, [x1, #0x13]
    // 0xa1c240: ArrayStore: r1[0] = rZR  ; List_8
    //     0xa1c240: stur            xzr, [x1, #0x17]
    // 0xa1c244: StoreField: r1->field_1f = r0
    //     0xa1c244: stur            w0, [x1, #0x1f]
    // 0xa1c248: StoreField: r1->field_23 = rZR
    //     0xa1c248: stur            xzr, [x1, #0x23]
    // 0xa1c24c: r0 = Instance_WrapCrossAlignment
    //     0xa1c24c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xa1c250: ldr             x0, [x0, #0x6f0]
    // 0xa1c254: StoreField: r1->field_2b = r0
    //     0xa1c254: stur            w0, [x1, #0x2b]
    // 0xa1c258: r0 = Instance_VerticalDirection
    //     0xa1c258: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa1c25c: ldr             x0, [x0, #0xa20]
    // 0xa1c260: StoreField: r1->field_33 = r0
    //     0xa1c260: stur            w0, [x1, #0x33]
    // 0xa1c264: r0 = Instance_Clip
    //     0xa1c264: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa1c268: ldr             x0, [x0, #0x38]
    // 0xa1c26c: StoreField: r1->field_37 = r0
    //     0xa1c26c: stur            w0, [x1, #0x37]
    // 0xa1c270: ldur            x0, [fp, #-0x10]
    // 0xa1c274: StoreField: r1->field_b = r0
    //     0xa1c274: stur            w0, [x1, #0xb]
    // 0xa1c278: r0 = Container()
    //     0xa1c278: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa1c27c: stur            x0, [fp, #-0x10]
    // 0xa1c280: r16 = Instance_BoxDecoration
    //     0xa1c280: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec50] Obj!BoxDecoration@d64921
    //     0xa1c284: ldr             x16, [x16, #0xc50]
    // 0xa1c288: r30 = Instance_EdgeInsets
    //     0xa1c288: add             lr, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xa1c28c: ldr             lr, [lr, #0x620]
    // 0xa1c290: stp             lr, x16, [SP, #8]
    // 0xa1c294: ldur            x16, [fp, #-8]
    // 0xa1c298: str             x16, [SP]
    // 0xa1c29c: mov             x1, x0
    // 0xa1c2a0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xa1c2a0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xa1c2a4: ldr             x4, [x4, #0xb40]
    // 0xa1c2a8: r0 = Container()
    //     0xa1c2a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa1c2ac: ldur            x0, [fp, #-0x10]
    // 0xa1c2b0: LeaveFrame
    //     0xa1c2b0: mov             SP, fp
    //     0xa1c2b4: ldp             fp, lr, [SP], #0x10
    // 0xa1c2b8: ret
    //     0xa1c2b8: ret             
    // 0xa1c2bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c2bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c2c0: b               #0xa1a740
    // 0xa1c2c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1c2c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1c2c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1c2c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1c2cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c2cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c2d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c2d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa1c2d4: SaveReg d0
    //     0xa1c2d4: str             q0, [SP, #-0x10]!
    // 0xa1c2d8: SaveReg r1
    //     0xa1c2d8: str             x1, [SP, #-8]!
    // 0xa1c2dc: r0 = AllocateDouble()
    //     0xa1c2dc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa1c2e0: RestoreReg r1
    //     0xa1c2e0: ldr             x1, [SP], #8
    // 0xa1c2e4: RestoreReg d0
    //     0xa1c2e4: ldr             q0, [SP], #0x10
    // 0xa1c2e8: b               #0xa1b6c8
    // 0xa1c2ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c2ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1c320, size: 0x178
    // 0xa1c320: EnterFrame
    //     0xa1c320: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c324: mov             fp, SP
    // 0xa1c328: AllocStack(0x38)
    //     0xa1c328: sub             SP, SP, #0x38
    // 0xa1c32c: SetupParameters()
    //     0xa1c32c: ldr             x0, [fp, #0x10]
    //     0xa1c330: ldur            w1, [x0, #0x17]
    //     0xa1c334: add             x1, x1, HEAP, lsl #32
    //     0xa1c338: stur            x1, [fp, #-8]
    // 0xa1c33c: CheckStackOverflow
    //     0xa1c33c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c340: cmp             SP, x16
    //     0xa1c344: b.ls            #0xa1c48c
    // 0xa1c348: LoadField: r0 = r1->field_f
    //     0xa1c348: ldur            w0, [x1, #0xf]
    // 0xa1c34c: DecompressPointer r0
    //     0xa1c34c: add             x0, x0, HEAP, lsl #32
    // 0xa1c350: LoadField: r2 = r0->field_b
    //     0xa1c350: ldur            w2, [x0, #0xb]
    // 0xa1c354: DecompressPointer r2
    //     0xa1c354: add             x2, x2, HEAP, lsl #32
    // 0xa1c358: cmp             w2, NULL
    // 0xa1c35c: b.eq            #0xa1c494
    // 0xa1c360: LoadField: r0 = r2->field_13
    //     0xa1c360: ldur            w0, [x2, #0x13]
    // 0xa1c364: DecompressPointer r0
    //     0xa1c364: add             x0, x0, HEAP, lsl #32
    // 0xa1c368: str             x0, [SP]
    // 0xa1c36c: r4 = 0
    //     0xa1c36c: movz            x4, #0
    // 0xa1c370: ldr             x0, [SP]
    // 0xa1c374: r16 = UnlinkedCall_0x613b5c
    //     0xa1c374: add             x16, PP, #0x56, lsl #12  ; [pp+0x56d28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa1c378: add             x16, x16, #0xd28
    // 0xa1c37c: ldp             x5, lr, [x16]
    // 0xa1c380: blr             lr
    // 0xa1c384: ldur            x0, [fp, #-8]
    // 0xa1c388: LoadField: r1 = r0->field_13
    //     0xa1c388: ldur            w1, [x0, #0x13]
    // 0xa1c38c: DecompressPointer r1
    //     0xa1c38c: add             x1, x1, HEAP, lsl #32
    // 0xa1c390: r16 = <Object?>
    //     0xa1c390: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xa1c394: stp             x1, x16, [SP]
    // 0xa1c398: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa1c398: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa1c39c: r0 = pop()
    //     0xa1c39c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xa1c3a0: ldur            x0, [fp, #-8]
    // 0xa1c3a4: LoadField: r1 = r0->field_f
    //     0xa1c3a4: ldur            w1, [x0, #0xf]
    // 0xa1c3a8: DecompressPointer r1
    //     0xa1c3a8: add             x1, x1, HEAP, lsl #32
    // 0xa1c3ac: LoadField: r2 = r1->field_1f
    //     0xa1c3ac: ldur            w2, [x1, #0x1f]
    // 0xa1c3b0: DecompressPointer r2
    //     0xa1c3b0: add             x2, x2, HEAP, lsl #32
    // 0xa1c3b4: LoadField: r1 = r2->field_8f
    //     0xa1c3b4: ldur            w1, [x2, #0x8f]
    // 0xa1c3b8: DecompressPointer r1
    //     0xa1c3b8: add             x1, x1, HEAP, lsl #32
    // 0xa1c3bc: r0 = value()
    //     0xa1c3bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1c3c0: LoadField: r2 = r0->field_7
    //     0xa1c3c0: ldur            w2, [x0, #7]
    // 0xa1c3c4: DecompressPointer r2
    //     0xa1c3c4: add             x2, x2, HEAP, lsl #32
    // 0xa1c3c8: ldur            x0, [fp, #-8]
    // 0xa1c3cc: stur            x2, [fp, #-0x18]
    // 0xa1c3d0: LoadField: r1 = r0->field_f
    //     0xa1c3d0: ldur            w1, [x0, #0xf]
    // 0xa1c3d4: DecompressPointer r1
    //     0xa1c3d4: add             x1, x1, HEAP, lsl #32
    // 0xa1c3d8: LoadField: r3 = r1->field_1f
    //     0xa1c3d8: ldur            w3, [x1, #0x1f]
    // 0xa1c3dc: DecompressPointer r3
    //     0xa1c3dc: add             x3, x3, HEAP, lsl #32
    // 0xa1c3e0: r17 = 259
    //     0xa1c3e0: movz            x17, #0x103
    // 0xa1c3e4: ldr             w4, [x3, x17]
    // 0xa1c3e8: DecompressPointer r4
    //     0xa1c3e8: add             x4, x4, HEAP, lsl #32
    // 0xa1c3ec: stur            x4, [fp, #-0x10]
    // 0xa1c3f0: r17 = 287
    //     0xa1c3f0: movz            x17, #0x11f
    // 0xa1c3f4: ldr             w1, [x3, x17]
    // 0xa1c3f8: DecompressPointer r1
    //     0xa1c3f8: add             x1, x1, HEAP, lsl #32
    // 0xa1c3fc: r0 = value()
    //     0xa1c3fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1c400: mov             x2, x0
    // 0xa1c404: ldur            x0, [fp, #-8]
    // 0xa1c408: stur            x2, [fp, #-0x20]
    // 0xa1c40c: LoadField: r1 = r0->field_f
    //     0xa1c40c: ldur            w1, [x0, #0xf]
    // 0xa1c410: DecompressPointer r1
    //     0xa1c410: add             x1, x1, HEAP, lsl #32
    // 0xa1c414: LoadField: r3 = r1->field_1f
    //     0xa1c414: ldur            w3, [x1, #0x1f]
    // 0xa1c418: DecompressPointer r3
    //     0xa1c418: add             x3, x3, HEAP, lsl #32
    // 0xa1c41c: LoadField: r1 = r3->field_8f
    //     0xa1c41c: ldur            w1, [x3, #0x8f]
    // 0xa1c420: DecompressPointer r1
    //     0xa1c420: add             x1, x1, HEAP, lsl #32
    // 0xa1c424: r0 = value()
    //     0xa1c424: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1c428: LoadField: r1 = r0->field_4b
    //     0xa1c428: ldur            w1, [x0, #0x4b]
    // 0xa1c42c: DecompressPointer r1
    //     0xa1c42c: add             x1, x1, HEAP, lsl #32
    // 0xa1c430: stur            x1, [fp, #-0x28]
    // 0xa1c434: r0 = ReplaceBagRequest()
    //     0xa1c434: bl              #0xa1a540  ; AllocateReplaceBagRequestStub -> ReplaceBagRequest (size=0x18)
    // 0xa1c438: mov             x1, x0
    // 0xa1c43c: ldur            x0, [fp, #-0x18]
    // 0xa1c440: StoreField: r1->field_7 = r0
    //     0xa1c440: stur            w0, [x1, #7]
    // 0xa1c444: ldur            x0, [fp, #-0x10]
    // 0xa1c448: StoreField: r1->field_b = r0
    //     0xa1c448: stur            w0, [x1, #0xb]
    // 0xa1c44c: ldur            x0, [fp, #-0x20]
    // 0xa1c450: StoreField: r1->field_f = r0
    //     0xa1c450: stur            w0, [x1, #0xf]
    // 0xa1c454: ldur            x0, [fp, #-0x28]
    // 0xa1c458: StoreField: r1->field_13 = r0
    //     0xa1c458: stur            w0, [x1, #0x13]
    // 0xa1c45c: ldur            x0, [fp, #-8]
    // 0xa1c460: LoadField: r2 = r0->field_f
    //     0xa1c460: ldur            w2, [x0, #0xf]
    // 0xa1c464: DecompressPointer r2
    //     0xa1c464: add             x2, x2, HEAP, lsl #32
    // 0xa1c468: LoadField: r0 = r2->field_1f
    //     0xa1c468: ldur            w0, [x2, #0x1f]
    // 0xa1c46c: DecompressPointer r0
    //     0xa1c46c: add             x0, x0, HEAP, lsl #32
    // 0xa1c470: mov             x2, x1
    // 0xa1c474: mov             x1, x0
    // 0xa1c478: r0 = continueButton()
    //     0xa1c478: bl              #0xa0f5b4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::continueButton
    // 0xa1c47c: r0 = Null
    //     0xa1c47c: mov             x0, NULL
    // 0xa1c480: LeaveFrame
    //     0xa1c480: mov             SP, fp
    //     0xa1c484: ldp             fp, lr, [SP], #0x10
    // 0xa1c488: ret
    //     0xa1c488: ret             
    // 0xa1c48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c48c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c490: b               #0xa1c348
    // 0xa1c494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa1c494: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1c498, size: 0xb4
    // 0xa1c498: EnterFrame
    //     0xa1c498: stp             fp, lr, [SP, #-0x10]!
    //     0xa1c49c: mov             fp, SP
    // 0xa1c4a0: AllocStack(0x8)
    //     0xa1c4a0: sub             SP, SP, #8
    // 0xa1c4a4: SetupParameters()
    //     0xa1c4a4: ldr             x0, [fp, #0x10]
    //     0xa1c4a8: ldur            w2, [x0, #0x17]
    //     0xa1c4ac: add             x2, x2, HEAP, lsl #32
    //     0xa1c4b0: stur            x2, [fp, #-8]
    // 0xa1c4b4: CheckStackOverflow
    //     0xa1c4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1c4b8: cmp             SP, x16
    //     0xa1c4bc: b.ls            #0xa1c544
    // 0xa1c4c0: LoadField: r0 = r2->field_f
    //     0xa1c4c0: ldur            w0, [x2, #0xf]
    // 0xa1c4c4: DecompressPointer r0
    //     0xa1c4c4: add             x0, x0, HEAP, lsl #32
    // 0xa1c4c8: LoadField: r1 = r0->field_1f
    //     0xa1c4c8: ldur            w1, [x0, #0x1f]
    // 0xa1c4cc: DecompressPointer r1
    //     0xa1c4cc: add             x1, x1, HEAP, lsl #32
    // 0xa1c4d0: r17 = 287
    //     0xa1c4d0: movz            x17, #0x11f
    // 0xa1c4d4: ldr             w0, [x1, x17]
    // 0xa1c4d8: DecompressPointer r0
    //     0xa1c4d8: add             x0, x0, HEAP, lsl #32
    // 0xa1c4dc: mov             x1, x0
    // 0xa1c4e0: r0 = value()
    //     0xa1c4e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1c4e4: cmp             w0, NULL
    // 0xa1c4e8: b.ne            #0xa1c4f4
    // 0xa1c4ec: r0 = 0
    //     0xa1c4ec: movz            x0, #0
    // 0xa1c4f0: b               #0xa1c504
    // 0xa1c4f4: r1 = LoadInt32Instr(r0)
    //     0xa1c4f4: sbfx            x1, x0, #1, #0x1f
    //     0xa1c4f8: tbz             w0, #0, #0xa1c500
    //     0xa1c4fc: ldur            x1, [x0, #7]
    // 0xa1c500: mov             x0, x1
    // 0xa1c504: cmp             x0, #1
    // 0xa1c508: b.le            #0xa1c534
    // 0xa1c50c: ldur            x0, [fp, #-8]
    // 0xa1c510: LoadField: r1 = r0->field_f
    //     0xa1c510: ldur            w1, [x0, #0xf]
    // 0xa1c514: DecompressPointer r1
    //     0xa1c514: add             x1, x1, HEAP, lsl #32
    // 0xa1c518: LoadField: r0 = r1->field_1f
    //     0xa1c518: ldur            w0, [x1, #0x1f]
    // 0xa1c51c: DecompressPointer r0
    //     0xa1c51c: add             x0, x0, HEAP, lsl #32
    // 0xa1c520: r17 = 259
    //     0xa1c520: movz            x17, #0x103
    // 0xa1c524: ldr             w2, [x0, x17]
    // 0xa1c528: DecompressPointer r2
    //     0xa1c528: add             x2, x2, HEAP, lsl #32
    // 0xa1c52c: mov             x1, x0
    // 0xa1c530: r0 = removeQuantity()
    //     0xa1c530: bl              #0xa1c54c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::removeQuantity
    // 0xa1c534: r0 = Null
    //     0xa1c534: mov             x0, NULL
    // 0xa1c538: LeaveFrame
    //     0xa1c538: mov             SP, fp
    //     0xa1c53c: ldp             fp, lr, [SP], #0x10
    // 0xa1c540: ret
    //     0xa1c540: ret             
    // 0xa1c544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1c544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1c548: b               #0xa1c4c0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1d278, size: 0x78
    // 0xa1d278: EnterFrame
    //     0xa1d278: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d27c: mov             fp, SP
    // 0xa1d280: AllocStack(0x18)
    //     0xa1d280: sub             SP, SP, #0x18
    // 0xa1d284: SetupParameters()
    //     0xa1d284: ldr             x0, [fp, #0x10]
    //     0xa1d288: ldur            w2, [x0, #0x17]
    //     0xa1d28c: add             x2, x2, HEAP, lsl #32
    //     0xa1d290: stur            x2, [fp, #-8]
    // 0xa1d294: CheckStackOverflow
    //     0xa1d294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d298: cmp             SP, x16
    //     0xa1d29c: b.ls            #0xa1d2e8
    // 0xa1d2a0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa1d2a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa1d2a4: ldr             x0, [x0, #0x1c80]
    //     0xa1d2a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa1d2ac: cmp             w0, w16
    //     0xa1d2b0: b.ne            #0xa1d2bc
    //     0xa1d2b4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa1d2b8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa1d2bc: ldur            x2, [fp, #-8]
    // 0xa1d2c0: r1 = Function '<anonymous closure>':.
    //     0xa1d2c0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56d98] AnonymousClosure: (0xa1d2f0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1d2c4: ldr             x1, [x1, #0xd98]
    // 0xa1d2c8: r0 = AllocateClosure()
    //     0xa1d2c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1d2cc: stp             x0, NULL, [SP]
    // 0xa1d2d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa1d2d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa1d2d4: r0 = GetNavigation.to()
    //     0xa1d2d4: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xa1d2d8: r0 = Null
    //     0xa1d2d8: mov             x0, NULL
    // 0xa1d2dc: LeaveFrame
    //     0xa1d2dc: mov             SP, fp
    //     0xa1d2e0: ldp             fp, lr, [SP], #0x10
    // 0xa1d2e4: ret
    //     0xa1d2e4: ret             
    // 0xa1d2e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d2e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d2ec: b               #0xa1d2a0
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0xa1d2f0, size: 0x70
    // 0xa1d2f0: EnterFrame
    //     0xa1d2f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d2f4: mov             fp, SP
    // 0xa1d2f8: AllocStack(0x8)
    //     0xa1d2f8: sub             SP, SP, #8
    // 0xa1d2fc: SetupParameters()
    //     0xa1d2fc: ldr             x0, [fp, #0x10]
    //     0xa1d300: ldur            w1, [x0, #0x17]
    //     0xa1d304: add             x1, x1, HEAP, lsl #32
    // 0xa1d308: CheckStackOverflow
    //     0xa1d308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d30c: cmp             SP, x16
    //     0xa1d310: b.ls            #0xa1d358
    // 0xa1d314: LoadField: r0 = r1->field_f
    //     0xa1d314: ldur            w0, [x1, #0xf]
    // 0xa1d318: DecompressPointer r0
    //     0xa1d318: add             x0, x0, HEAP, lsl #32
    // 0xa1d31c: LoadField: r1 = r0->field_1f
    //     0xa1d31c: ldur            w1, [x0, #0x1f]
    // 0xa1d320: DecompressPointer r1
    //     0xa1d320: add             x1, x1, HEAP, lsl #32
    // 0xa1d324: LoadField: r0 = r1->field_8f
    //     0xa1d324: ldur            w0, [x1, #0x8f]
    // 0xa1d328: DecompressPointer r0
    //     0xa1d328: add             x0, x0, HEAP, lsl #32
    // 0xa1d32c: mov             x1, x0
    // 0xa1d330: r0 = value()
    //     0xa1d330: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d334: LoadField: r1 = r0->field_4f
    //     0xa1d334: ldur            w1, [x0, #0x4f]
    // 0xa1d338: DecompressPointer r1
    //     0xa1d338: add             x1, x1, HEAP, lsl #32
    // 0xa1d33c: stur            x1, [fp, #-8]
    // 0xa1d340: r0 = ViewSizeChart()
    //     0xa1d340: bl              #0xa1d360  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0xa1d344: ldur            x1, [fp, #-8]
    // 0xa1d348: StoreField: r0->field_b = r1
    //     0xa1d348: stur            w1, [x0, #0xb]
    // 0xa1d34c: LeaveFrame
    //     0xa1d34c: mov             SP, fp
    //     0xa1d350: ldp             fp, lr, [SP], #0x10
    // 0xa1d354: ret
    //     0xa1d354: ret             
    // 0xa1d358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d358: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d35c: b               #0xa1d314
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa1d38c, size: 0x4b0
    // 0xa1d38c: EnterFrame
    //     0xa1d38c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d390: mov             fp, SP
    // 0xa1d394: AllocStack(0x38)
    //     0xa1d394: sub             SP, SP, #0x38
    // 0xa1d398: SetupParameters()
    //     0xa1d398: ldr             x0, [fp, #0x20]
    //     0xa1d39c: ldur            w1, [x0, #0x17]
    //     0xa1d3a0: add             x1, x1, HEAP, lsl #32
    //     0xa1d3a4: stur            x1, [fp, #-8]
    // 0xa1d3a8: CheckStackOverflow
    //     0xa1d3a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d3ac: cmp             SP, x16
    //     0xa1d3b0: b.ls            #0xa1d828
    // 0xa1d3b4: r1 = 1
    //     0xa1d3b4: movz            x1, #0x1
    // 0xa1d3b8: r0 = AllocateContext()
    //     0xa1d3b8: bl              #0x16f6108  ; AllocateContextStub
    // 0xa1d3bc: mov             x2, x0
    // 0xa1d3c0: ldur            x0, [fp, #-8]
    // 0xa1d3c4: stur            x2, [fp, #-0x18]
    // 0xa1d3c8: StoreField: r2->field_b = r0
    //     0xa1d3c8: stur            w0, [x2, #0xb]
    // 0xa1d3cc: ldr             x3, [fp, #0x10]
    // 0xa1d3d0: StoreField: r2->field_f = r3
    //     0xa1d3d0: stur            w3, [x2, #0xf]
    // 0xa1d3d4: LoadField: r1 = r0->field_f
    //     0xa1d3d4: ldur            w1, [x0, #0xf]
    // 0xa1d3d8: DecompressPointer r1
    //     0xa1d3d8: add             x1, x1, HEAP, lsl #32
    // 0xa1d3dc: LoadField: r4 = r1->field_13
    //     0xa1d3dc: ldur            w4, [x1, #0x13]
    // 0xa1d3e0: DecompressPointer r4
    //     0xa1d3e0: add             x4, x4, HEAP, lsl #32
    // 0xa1d3e4: stur            x4, [fp, #-0x10]
    // 0xa1d3e8: LoadField: r5 = r1->field_1f
    //     0xa1d3e8: ldur            w5, [x1, #0x1f]
    // 0xa1d3ec: DecompressPointer r5
    //     0xa1d3ec: add             x5, x5, HEAP, lsl #32
    // 0xa1d3f0: LoadField: r1 = r5->field_8f
    //     0xa1d3f0: ldur            w1, [x5, #0x8f]
    // 0xa1d3f4: DecompressPointer r1
    //     0xa1d3f4: add             x1, x1, HEAP, lsl #32
    // 0xa1d3f8: r0 = value()
    //     0xa1d3f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d3fc: LoadField: r2 = r0->field_53
    //     0xa1d3fc: ldur            w2, [x0, #0x53]
    // 0xa1d400: DecompressPointer r2
    //     0xa1d400: add             x2, x2, HEAP, lsl #32
    // 0xa1d404: cmp             w2, NULL
    // 0xa1d408: b.ne            #0xa1d418
    // 0xa1d40c: ldr             x3, [fp, #0x10]
    // 0xa1d410: r0 = Null
    //     0xa1d410: mov             x0, NULL
    // 0xa1d414: b               #0xa1d458
    // 0xa1d418: ldr             x3, [fp, #0x10]
    // 0xa1d41c: LoadField: r0 = r2->field_b
    //     0xa1d41c: ldur            w0, [x2, #0xb]
    // 0xa1d420: r4 = LoadInt32Instr(r3)
    //     0xa1d420: sbfx            x4, x3, #1, #0x1f
    //     0xa1d424: tbz             w3, #0, #0xa1d42c
    //     0xa1d428: ldur            x4, [x3, #7]
    // 0xa1d42c: r1 = LoadInt32Instr(r0)
    //     0xa1d42c: sbfx            x1, x0, #1, #0x1f
    // 0xa1d430: mov             x0, x1
    // 0xa1d434: mov             x1, x4
    // 0xa1d438: cmp             x1, x0
    // 0xa1d43c: b.hs            #0xa1d830
    // 0xa1d440: LoadField: r0 = r2->field_f
    //     0xa1d440: ldur            w0, [x2, #0xf]
    // 0xa1d444: DecompressPointer r0
    //     0xa1d444: add             x0, x0, HEAP, lsl #32
    // 0xa1d448: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1d448: add             x16, x0, x4, lsl #2
    //     0xa1d44c: ldur            w1, [x16, #0xf]
    // 0xa1d450: DecompressPointer r1
    //     0xa1d450: add             x1, x1, HEAP, lsl #32
    // 0xa1d454: mov             x0, x1
    // 0xa1d458: cmp             w0, NULL
    // 0xa1d45c: b.ne            #0xa1d46c
    // 0xa1d460: r0 = SkuDetails()
    //     0xa1d460: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0xa1d464: mov             x2, x0
    // 0xa1d468: b               #0xa1d470
    // 0xa1d46c: mov             x2, x0
    // 0xa1d470: ldur            x1, [fp, #-0x10]
    // 0xa1d474: r0 = contains()
    //     0xa1d474: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa1d478: tbnz            w0, #4, #0xa1d514
    // 0xa1d47c: ldr             x1, [fp, #0x18]
    // 0xa1d480: r0 = of()
    //     0xa1d480: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d484: LoadField: r2 = r0->field_5b
    //     0xa1d484: ldur            w2, [x0, #0x5b]
    // 0xa1d488: DecompressPointer r2
    //     0xa1d488: add             x2, x2, HEAP, lsl #32
    // 0xa1d48c: r16 = 1.000000
    //     0xa1d48c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xa1d490: str             x16, [SP]
    // 0xa1d494: r1 = Null
    //     0xa1d494: mov             x1, NULL
    // 0xa1d498: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xa1d498: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xa1d49c: ldr             x4, [x4, #0x108]
    // 0xa1d4a0: r0 = Border.all()
    //     0xa1d4a0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa1d4a4: ldr             x1, [fp, #0x18]
    // 0xa1d4a8: stur            x0, [fp, #-0x10]
    // 0xa1d4ac: r0 = of()
    //     0xa1d4ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d4b0: LoadField: r1 = r0->field_5b
    //     0xa1d4b0: ldur            w1, [x0, #0x5b]
    // 0xa1d4b4: DecompressPointer r1
    //     0xa1d4b4: add             x1, x1, HEAP, lsl #32
    // 0xa1d4b8: r0 = LoadClassIdInstr(r1)
    //     0xa1d4b8: ldur            x0, [x1, #-1]
    //     0xa1d4bc: ubfx            x0, x0, #0xc, #0x14
    // 0xa1d4c0: d0 = 0.030000
    //     0xa1d4c0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xa1d4c4: ldr             d0, [x17, #0x238]
    // 0xa1d4c8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa1d4c8: sub             lr, x0, #0xffa
    //     0xa1d4cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa1d4d0: blr             lr
    // 0xa1d4d4: stur            x0, [fp, #-0x20]
    // 0xa1d4d8: r0 = BoxDecoration()
    //     0xa1d4d8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa1d4dc: mov             x1, x0
    // 0xa1d4e0: ldur            x0, [fp, #-0x20]
    // 0xa1d4e4: StoreField: r1->field_7 = r0
    //     0xa1d4e4: stur            w0, [x1, #7]
    // 0xa1d4e8: ldur            x0, [fp, #-0x10]
    // 0xa1d4ec: StoreField: r1->field_f = r0
    //     0xa1d4ec: stur            w0, [x1, #0xf]
    // 0xa1d4f0: r0 = Instance_BorderRadius
    //     0xa1d4f0: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xa1d4f4: ldr             x0, [x0, #0x460]
    // 0xa1d4f8: StoreField: r1->field_13 = r0
    //     0xa1d4f8: stur            w0, [x1, #0x13]
    // 0xa1d4fc: r2 = Instance_BoxShape
    //     0xa1d4fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1d500: ldr             x2, [x2, #0x80]
    // 0xa1d504: StoreField: r1->field_23 = r2
    //     0xa1d504: stur            w2, [x1, #0x23]
    // 0xa1d508: mov             x3, x1
    // 0xa1d50c: mov             x0, x2
    // 0xa1d510: b               #0xa1d580
    // 0xa1d514: r0 = Instance_BorderRadius
    //     0xa1d514: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xa1d518: ldr             x0, [x0, #0x460]
    // 0xa1d51c: r2 = Instance_BoxShape
    //     0xa1d51c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1d520: ldr             x2, [x2, #0x80]
    // 0xa1d524: ldr             x1, [fp, #0x18]
    // 0xa1d528: r0 = of()
    //     0xa1d528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d52c: LoadField: r1 = r0->field_5b
    //     0xa1d52c: ldur            w1, [x0, #0x5b]
    // 0xa1d530: DecompressPointer r1
    //     0xa1d530: add             x1, x1, HEAP, lsl #32
    // 0xa1d534: r0 = LoadClassIdInstr(r1)
    //     0xa1d534: ldur            x0, [x1, #-1]
    //     0xa1d538: ubfx            x0, x0, #0xc, #0x14
    // 0xa1d53c: d0 = 0.030000
    //     0xa1d53c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xa1d540: ldr             d0, [x17, #0x238]
    // 0xa1d544: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa1d544: sub             lr, x0, #0xffa
    //     0xa1d548: ldr             lr, [x21, lr, lsl #3]
    //     0xa1d54c: blr             lr
    // 0xa1d550: stur            x0, [fp, #-0x10]
    // 0xa1d554: r0 = BoxDecoration()
    //     0xa1d554: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa1d558: mov             x1, x0
    // 0xa1d55c: ldur            x0, [fp, #-0x10]
    // 0xa1d560: StoreField: r1->field_7 = r0
    //     0xa1d560: stur            w0, [x1, #7]
    // 0xa1d564: r0 = Instance_BorderRadius
    //     0xa1d564: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xa1d568: ldr             x0, [x0, #0x460]
    // 0xa1d56c: StoreField: r1->field_13 = r0
    //     0xa1d56c: stur            w0, [x1, #0x13]
    // 0xa1d570: r0 = Instance_BoxShape
    //     0xa1d570: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1d574: ldr             x0, [x0, #0x80]
    // 0xa1d578: StoreField: r1->field_23 = r0
    //     0xa1d578: stur            w0, [x1, #0x23]
    // 0xa1d57c: mov             x3, x1
    // 0xa1d580: ldur            x2, [fp, #-8]
    // 0xa1d584: stur            x3, [fp, #-0x10]
    // 0xa1d588: LoadField: r1 = r2->field_f
    //     0xa1d588: ldur            w1, [x2, #0xf]
    // 0xa1d58c: DecompressPointer r1
    //     0xa1d58c: add             x1, x1, HEAP, lsl #32
    // 0xa1d590: LoadField: r4 = r1->field_1f
    //     0xa1d590: ldur            w4, [x1, #0x1f]
    // 0xa1d594: DecompressPointer r4
    //     0xa1d594: add             x4, x4, HEAP, lsl #32
    // 0xa1d598: LoadField: r1 = r4->field_8f
    //     0xa1d598: ldur            w1, [x4, #0x8f]
    // 0xa1d59c: DecompressPointer r1
    //     0xa1d59c: add             x1, x1, HEAP, lsl #32
    // 0xa1d5a0: r0 = value()
    //     0xa1d5a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d5a4: LoadField: r2 = r0->field_53
    //     0xa1d5a4: ldur            w2, [x0, #0x53]
    // 0xa1d5a8: DecompressPointer r2
    //     0xa1d5a8: add             x2, x2, HEAP, lsl #32
    // 0xa1d5ac: cmp             w2, NULL
    // 0xa1d5b0: b.ne            #0xa1d5c0
    // 0xa1d5b4: ldr             x3, [fp, #0x10]
    // 0xa1d5b8: r0 = Null
    //     0xa1d5b8: mov             x0, NULL
    // 0xa1d5bc: b               #0xa1d604
    // 0xa1d5c0: ldr             x3, [fp, #0x10]
    // 0xa1d5c4: LoadField: r0 = r2->field_b
    //     0xa1d5c4: ldur            w0, [x2, #0xb]
    // 0xa1d5c8: r4 = LoadInt32Instr(r3)
    //     0xa1d5c8: sbfx            x4, x3, #1, #0x1f
    //     0xa1d5cc: tbz             w3, #0, #0xa1d5d4
    //     0xa1d5d0: ldur            x4, [x3, #7]
    // 0xa1d5d4: r1 = LoadInt32Instr(r0)
    //     0xa1d5d4: sbfx            x1, x0, #1, #0x1f
    // 0xa1d5d8: mov             x0, x1
    // 0xa1d5dc: mov             x1, x4
    // 0xa1d5e0: cmp             x1, x0
    // 0xa1d5e4: b.hs            #0xa1d834
    // 0xa1d5e8: LoadField: r0 = r2->field_f
    //     0xa1d5e8: ldur            w0, [x2, #0xf]
    // 0xa1d5ec: DecompressPointer r0
    //     0xa1d5ec: add             x0, x0, HEAP, lsl #32
    // 0xa1d5f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1d5f0: add             x16, x0, x4, lsl #2
    //     0xa1d5f4: ldur            w1, [x16, #0xf]
    // 0xa1d5f8: DecompressPointer r1
    //     0xa1d5f8: add             x1, x1, HEAP, lsl #32
    // 0xa1d5fc: LoadField: r0 = r1->field_f
    //     0xa1d5fc: ldur            w0, [x1, #0xf]
    // 0xa1d600: DecompressPointer r0
    //     0xa1d600: add             x0, x0, HEAP, lsl #32
    // 0xa1d604: cmp             w0, NULL
    // 0xa1d608: b.ne            #0xa1d614
    // 0xa1d60c: r2 = ""
    //     0xa1d60c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1d610: b               #0xa1d618
    // 0xa1d614: mov             x2, x0
    // 0xa1d618: ldur            x0, [fp, #-8]
    // 0xa1d61c: ldr             x1, [fp, #0x18]
    // 0xa1d620: stur            x2, [fp, #-0x20]
    // 0xa1d624: r0 = of()
    //     0xa1d624: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa1d628: LoadField: r1 = r0->field_87
    //     0xa1d628: ldur            w1, [x0, #0x87]
    // 0xa1d62c: DecompressPointer r1
    //     0xa1d62c: add             x1, x1, HEAP, lsl #32
    // 0xa1d630: LoadField: r0 = r1->field_2b
    //     0xa1d630: ldur            w0, [x1, #0x2b]
    // 0xa1d634: DecompressPointer r0
    //     0xa1d634: add             x0, x0, HEAP, lsl #32
    // 0xa1d638: ldur            x1, [fp, #-8]
    // 0xa1d63c: stur            x0, [fp, #-0x28]
    // 0xa1d640: LoadField: r2 = r1->field_f
    //     0xa1d640: ldur            w2, [x1, #0xf]
    // 0xa1d644: DecompressPointer r2
    //     0xa1d644: add             x2, x2, HEAP, lsl #32
    // 0xa1d648: LoadField: r3 = r2->field_13
    //     0xa1d648: ldur            w3, [x2, #0x13]
    // 0xa1d64c: DecompressPointer r3
    //     0xa1d64c: add             x3, x3, HEAP, lsl #32
    // 0xa1d650: stur            x3, [fp, #-8]
    // 0xa1d654: LoadField: r1 = r2->field_1f
    //     0xa1d654: ldur            w1, [x2, #0x1f]
    // 0xa1d658: DecompressPointer r1
    //     0xa1d658: add             x1, x1, HEAP, lsl #32
    // 0xa1d65c: LoadField: r2 = r1->field_8f
    //     0xa1d65c: ldur            w2, [x1, #0x8f]
    // 0xa1d660: DecompressPointer r2
    //     0xa1d660: add             x2, x2, HEAP, lsl #32
    // 0xa1d664: mov             x1, x2
    // 0xa1d668: r0 = value()
    //     0xa1d668: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d66c: LoadField: r2 = r0->field_53
    //     0xa1d66c: ldur            w2, [x0, #0x53]
    // 0xa1d670: DecompressPointer r2
    //     0xa1d670: add             x2, x2, HEAP, lsl #32
    // 0xa1d674: cmp             w2, NULL
    // 0xa1d678: b.ne            #0xa1d684
    // 0xa1d67c: r0 = Null
    //     0xa1d67c: mov             x0, NULL
    // 0xa1d680: b               #0xa1d6c0
    // 0xa1d684: ldr             x0, [fp, #0x10]
    // 0xa1d688: LoadField: r1 = r2->field_b
    //     0xa1d688: ldur            w1, [x2, #0xb]
    // 0xa1d68c: r3 = LoadInt32Instr(r0)
    //     0xa1d68c: sbfx            x3, x0, #1, #0x1f
    //     0xa1d690: tbz             w0, #0, #0xa1d698
    //     0xa1d694: ldur            x3, [x0, #7]
    // 0xa1d698: r0 = LoadInt32Instr(r1)
    //     0xa1d698: sbfx            x0, x1, #1, #0x1f
    // 0xa1d69c: mov             x1, x3
    // 0xa1d6a0: cmp             x1, x0
    // 0xa1d6a4: b.hs            #0xa1d838
    // 0xa1d6a8: LoadField: r0 = r2->field_f
    //     0xa1d6a8: ldur            w0, [x2, #0xf]
    // 0xa1d6ac: DecompressPointer r0
    //     0xa1d6ac: add             x0, x0, HEAP, lsl #32
    // 0xa1d6b0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa1d6b0: add             x16, x0, x3, lsl #2
    //     0xa1d6b4: ldur            w1, [x16, #0xf]
    // 0xa1d6b8: DecompressPointer r1
    //     0xa1d6b8: add             x1, x1, HEAP, lsl #32
    // 0xa1d6bc: mov             x0, x1
    // 0xa1d6c0: cmp             w0, NULL
    // 0xa1d6c4: b.ne            #0xa1d6d0
    // 0xa1d6c8: r2 = false
    //     0xa1d6c8: add             x2, NULL, #0x30  ; false
    // 0xa1d6cc: b               #0xa1d6d4
    // 0xa1d6d0: mov             x2, x0
    // 0xa1d6d4: ldur            x1, [fp, #-8]
    // 0xa1d6d8: r0 = contains()
    //     0xa1d6d8: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa1d6dc: tbnz            w0, #4, #0xa1d6f8
    // 0xa1d6e0: r1 = Instance_Color
    //     0xa1d6e0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1d6e4: d0 = 0.700000
    //     0xa1d6e4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa1d6e8: ldr             d0, [x17, #0xf48]
    // 0xa1d6ec: r0 = withOpacity()
    //     0xa1d6ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa1d6f0: mov             x1, x0
    // 0xa1d6f4: b               #0xa1d708
    // 0xa1d6f8: r1 = Instance_Color
    //     0xa1d6f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa1d6fc: d0 = 0.400000
    //     0xa1d6fc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa1d700: r0 = withOpacity()
    //     0xa1d700: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa1d704: mov             x1, x0
    // 0xa1d708: ldur            x0, [fp, #-0x20]
    // 0xa1d70c: r16 = 14.000000
    //     0xa1d70c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa1d710: ldr             x16, [x16, #0x1d8]
    // 0xa1d714: stp             x1, x16, [SP]
    // 0xa1d718: ldur            x1, [fp, #-0x28]
    // 0xa1d71c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa1d71c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa1d720: ldr             x4, [x4, #0xaa0]
    // 0xa1d724: r0 = copyWith()
    //     0xa1d724: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa1d728: stur            x0, [fp, #-8]
    // 0xa1d72c: r0 = Text()
    //     0xa1d72c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa1d730: mov             x1, x0
    // 0xa1d734: ldur            x0, [fp, #-0x20]
    // 0xa1d738: stur            x1, [fp, #-0x28]
    // 0xa1d73c: StoreField: r1->field_b = r0
    //     0xa1d73c: stur            w0, [x1, #0xb]
    // 0xa1d740: ldur            x0, [fp, #-8]
    // 0xa1d744: StoreField: r1->field_13 = r0
    //     0xa1d744: stur            w0, [x1, #0x13]
    // 0xa1d748: r0 = Padding()
    //     0xa1d748: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1d74c: mov             x1, x0
    // 0xa1d750: r0 = Instance_EdgeInsets
    //     0xa1d750: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e88] Obj!EdgeInsets@d57231
    //     0xa1d754: ldr             x0, [x0, #0xe88]
    // 0xa1d758: stur            x1, [fp, #-8]
    // 0xa1d75c: StoreField: r1->field_f = r0
    //     0xa1d75c: stur            w0, [x1, #0xf]
    // 0xa1d760: ldur            x0, [fp, #-0x28]
    // 0xa1d764: StoreField: r1->field_b = r0
    //     0xa1d764: stur            w0, [x1, #0xb]
    // 0xa1d768: r0 = Center()
    //     0xa1d768: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa1d76c: mov             x1, x0
    // 0xa1d770: r0 = Instance_Alignment
    //     0xa1d770: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa1d774: ldr             x0, [x0, #0xb10]
    // 0xa1d778: stur            x1, [fp, #-0x20]
    // 0xa1d77c: StoreField: r1->field_f = r0
    //     0xa1d77c: stur            w0, [x1, #0xf]
    // 0xa1d780: ldur            x0, [fp, #-8]
    // 0xa1d784: StoreField: r1->field_b = r0
    //     0xa1d784: stur            w0, [x1, #0xb]
    // 0xa1d788: r0 = Container()
    //     0xa1d788: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa1d78c: stur            x0, [fp, #-8]
    // 0xa1d790: ldur            x16, [fp, #-0x10]
    // 0xa1d794: ldur            lr, [fp, #-0x20]
    // 0xa1d798: stp             lr, x16, [SP]
    // 0xa1d79c: mov             x1, x0
    // 0xa1d7a0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa1d7a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa1d7a4: ldr             x4, [x4, #0x88]
    // 0xa1d7a8: r0 = Container()
    //     0xa1d7a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa1d7ac: r0 = InkWell()
    //     0xa1d7ac: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa1d7b0: mov             x3, x0
    // 0xa1d7b4: ldur            x0, [fp, #-8]
    // 0xa1d7b8: stur            x3, [fp, #-0x10]
    // 0xa1d7bc: StoreField: r3->field_b = r0
    //     0xa1d7bc: stur            w0, [x3, #0xb]
    // 0xa1d7c0: ldur            x2, [fp, #-0x18]
    // 0xa1d7c4: r1 = Function '<anonymous closure>':.
    //     0xa1d7c4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56da0] AnonymousClosure: (0xa1d83c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1d7c8: ldr             x1, [x1, #0xda0]
    // 0xa1d7cc: r0 = AllocateClosure()
    //     0xa1d7cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1d7d0: mov             x1, x0
    // 0xa1d7d4: ldur            x0, [fp, #-0x10]
    // 0xa1d7d8: StoreField: r0->field_f = r1
    //     0xa1d7d8: stur            w1, [x0, #0xf]
    // 0xa1d7dc: r1 = true
    //     0xa1d7dc: add             x1, NULL, #0x20  ; true
    // 0xa1d7e0: StoreField: r0->field_43 = r1
    //     0xa1d7e0: stur            w1, [x0, #0x43]
    // 0xa1d7e4: r2 = Instance_BoxShape
    //     0xa1d7e4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa1d7e8: ldr             x2, [x2, #0x80]
    // 0xa1d7ec: StoreField: r0->field_47 = r2
    //     0xa1d7ec: stur            w2, [x0, #0x47]
    // 0xa1d7f0: StoreField: r0->field_6f = r1
    //     0xa1d7f0: stur            w1, [x0, #0x6f]
    // 0xa1d7f4: r2 = false
    //     0xa1d7f4: add             x2, NULL, #0x30  ; false
    // 0xa1d7f8: StoreField: r0->field_73 = r2
    //     0xa1d7f8: stur            w2, [x0, #0x73]
    // 0xa1d7fc: StoreField: r0->field_83 = r1
    //     0xa1d7fc: stur            w1, [x0, #0x83]
    // 0xa1d800: StoreField: r0->field_7b = r2
    //     0xa1d800: stur            w2, [x0, #0x7b]
    // 0xa1d804: r0 = Padding()
    //     0xa1d804: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa1d808: r1 = Instance_EdgeInsets
    //     0xa1d808: add             x1, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xa1d80c: ldr             x1, [x1, #0x878]
    // 0xa1d810: StoreField: r0->field_f = r1
    //     0xa1d810: stur            w1, [x0, #0xf]
    // 0xa1d814: ldur            x1, [fp, #-0x10]
    // 0xa1d818: StoreField: r0->field_b = r1
    //     0xa1d818: stur            w1, [x0, #0xb]
    // 0xa1d81c: LeaveFrame
    //     0xa1d81c: mov             SP, fp
    //     0xa1d820: ldp             fp, lr, [SP], #0x10
    // 0xa1d824: ret
    //     0xa1d824: ret             
    // 0xa1d828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d82c: b               #0xa1d3b4
    // 0xa1d830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1d830: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1d834: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1d834: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1d838: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1d838: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1d83c, size: 0x68
    // 0xa1d83c: EnterFrame
    //     0xa1d83c: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d840: mov             fp, SP
    // 0xa1d844: AllocStack(0x8)
    //     0xa1d844: sub             SP, SP, #8
    // 0xa1d848: SetupParameters()
    //     0xa1d848: ldr             x0, [fp, #0x10]
    //     0xa1d84c: ldur            w2, [x0, #0x17]
    //     0xa1d850: add             x2, x2, HEAP, lsl #32
    // 0xa1d854: CheckStackOverflow
    //     0xa1d854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d858: cmp             SP, x16
    //     0xa1d85c: b.ls            #0xa1d89c
    // 0xa1d860: LoadField: r0 = r2->field_b
    //     0xa1d860: ldur            w0, [x2, #0xb]
    // 0xa1d864: DecompressPointer r0
    //     0xa1d864: add             x0, x0, HEAP, lsl #32
    // 0xa1d868: LoadField: r3 = r0->field_f
    //     0xa1d868: ldur            w3, [x0, #0xf]
    // 0xa1d86c: DecompressPointer r3
    //     0xa1d86c: add             x3, x3, HEAP, lsl #32
    // 0xa1d870: stur            x3, [fp, #-8]
    // 0xa1d874: r1 = Function '<anonymous closure>':.
    //     0xa1d874: add             x1, PP, #0x56, lsl #12  ; [pp+0x56da8] AnonymousClosure: (0xa1d8a4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xa1d878: ldr             x1, [x1, #0xda8]
    // 0xa1d87c: r0 = AllocateClosure()
    //     0xa1d87c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1d880: ldur            x1, [fp, #-8]
    // 0xa1d884: mov             x2, x0
    // 0xa1d888: r0 = setState()
    //     0xa1d888: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa1d88c: r0 = Null
    //     0xa1d88c: mov             x0, NULL
    // 0xa1d890: LeaveFrame
    //     0xa1d890: mov             SP, fp
    //     0xa1d894: ldp             fp, lr, [SP], #0x10
    // 0xa1d898: ret
    //     0xa1d898: ret             
    // 0xa1d89c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1d89c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1d8a0: b               #0xa1d860
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1d8a4, size: 0x4a4
    // 0xa1d8a4: EnterFrame
    //     0xa1d8a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa1d8a8: mov             fp, SP
    // 0xa1d8ac: AllocStack(0x28)
    //     0xa1d8ac: sub             SP, SP, #0x28
    // 0xa1d8b0: SetupParameters()
    //     0xa1d8b0: ldr             x0, [fp, #0x10]
    //     0xa1d8b4: ldur            w2, [x0, #0x17]
    //     0xa1d8b8: add             x2, x2, HEAP, lsl #32
    //     0xa1d8bc: stur            x2, [fp, #-0x10]
    // 0xa1d8c0: CheckStackOverflow
    //     0xa1d8c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1d8c4: cmp             SP, x16
    //     0xa1d8c8: b.ls            #0xa1dd2c
    // 0xa1d8cc: LoadField: r0 = r2->field_b
    //     0xa1d8cc: ldur            w0, [x2, #0xb]
    // 0xa1d8d0: DecompressPointer r0
    //     0xa1d8d0: add             x0, x0, HEAP, lsl #32
    // 0xa1d8d4: stur            x0, [fp, #-8]
    // 0xa1d8d8: LoadField: r1 = r0->field_f
    //     0xa1d8d8: ldur            w1, [x0, #0xf]
    // 0xa1d8dc: DecompressPointer r1
    //     0xa1d8dc: add             x1, x1, HEAP, lsl #32
    // 0xa1d8e0: LoadField: r3 = r1->field_13
    //     0xa1d8e0: ldur            w3, [x1, #0x13]
    // 0xa1d8e4: DecompressPointer r3
    //     0xa1d8e4: add             x3, x3, HEAP, lsl #32
    // 0xa1d8e8: mov             x1, x3
    // 0xa1d8ec: r0 = clear()
    //     0xa1d8ec: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa1d8f0: ldur            x0, [fp, #-8]
    // 0xa1d8f4: LoadField: r1 = r0->field_f
    //     0xa1d8f4: ldur            w1, [x0, #0xf]
    // 0xa1d8f8: DecompressPointer r1
    //     0xa1d8f8: add             x1, x1, HEAP, lsl #32
    // 0xa1d8fc: ldur            x2, [fp, #-0x10]
    // 0xa1d900: LoadField: r3 = r2->field_f
    //     0xa1d900: ldur            w3, [x2, #0xf]
    // 0xa1d904: DecompressPointer r3
    //     0xa1d904: add             x3, x3, HEAP, lsl #32
    // 0xa1d908: r4 = LoadInt32Instr(r3)
    //     0xa1d908: sbfx            x4, x3, #1, #0x1f
    //     0xa1d90c: tbz             w3, #0, #0xa1d914
    //     0xa1d910: ldur            x4, [x3, #7]
    // 0xa1d914: ArrayStore: r1[0] = r4  ; List_8
    //     0xa1d914: stur            x4, [x1, #0x17]
    // 0xa1d918: LoadField: r3 = r1->field_1f
    //     0xa1d918: ldur            w3, [x1, #0x1f]
    // 0xa1d91c: DecompressPointer r3
    //     0xa1d91c: add             x3, x3, HEAP, lsl #32
    // 0xa1d920: stur            x3, [fp, #-0x18]
    // 0xa1d924: LoadField: r1 = r3->field_8f
    //     0xa1d924: ldur            w1, [x3, #0x8f]
    // 0xa1d928: DecompressPointer r1
    //     0xa1d928: add             x1, x1, HEAP, lsl #32
    // 0xa1d92c: r0 = value()
    //     0xa1d92c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d930: LoadField: r2 = r0->field_53
    //     0xa1d930: ldur            w2, [x0, #0x53]
    // 0xa1d934: DecompressPointer r2
    //     0xa1d934: add             x2, x2, HEAP, lsl #32
    // 0xa1d938: cmp             w2, NULL
    // 0xa1d93c: b.ne            #0xa1d94c
    // 0xa1d940: ldur            x3, [fp, #-0x10]
    // 0xa1d944: r0 = Null
    //     0xa1d944: mov             x0, NULL
    // 0xa1d948: b               #0xa1d994
    // 0xa1d94c: ldur            x3, [fp, #-0x10]
    // 0xa1d950: LoadField: r0 = r3->field_f
    //     0xa1d950: ldur            w0, [x3, #0xf]
    // 0xa1d954: DecompressPointer r0
    //     0xa1d954: add             x0, x0, HEAP, lsl #32
    // 0xa1d958: LoadField: r1 = r2->field_b
    //     0xa1d958: ldur            w1, [x2, #0xb]
    // 0xa1d95c: r4 = LoadInt32Instr(r0)
    //     0xa1d95c: sbfx            x4, x0, #1, #0x1f
    //     0xa1d960: tbz             w0, #0, #0xa1d968
    //     0xa1d964: ldur            x4, [x0, #7]
    // 0xa1d968: r0 = LoadInt32Instr(r1)
    //     0xa1d968: sbfx            x0, x1, #1, #0x1f
    // 0xa1d96c: mov             x1, x4
    // 0xa1d970: cmp             x1, x0
    // 0xa1d974: b.hs            #0xa1dd34
    // 0xa1d978: LoadField: r0 = r2->field_f
    //     0xa1d978: ldur            w0, [x2, #0xf]
    // 0xa1d97c: DecompressPointer r0
    //     0xa1d97c: add             x0, x0, HEAP, lsl #32
    // 0xa1d980: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1d980: add             x16, x0, x4, lsl #2
    //     0xa1d984: ldur            w1, [x16, #0xf]
    // 0xa1d988: DecompressPointer r1
    //     0xa1d988: add             x1, x1, HEAP, lsl #32
    // 0xa1d98c: LoadField: r0 = r1->field_7
    //     0xa1d98c: ldur            w0, [x1, #7]
    // 0xa1d990: DecompressPointer r0
    //     0xa1d990: add             x0, x0, HEAP, lsl #32
    // 0xa1d994: cmp             w0, NULL
    // 0xa1d998: b.ne            #0xa1d9a0
    // 0xa1d99c: r0 = ""
    //     0xa1d99c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1d9a0: ldur            x2, [fp, #-8]
    // 0xa1d9a4: ldur            x1, [fp, #-0x18]
    // 0xa1d9a8: r17 = 259
    //     0xa1d9a8: movz            x17, #0x103
    // 0xa1d9ac: str             w0, [x1, x17]
    // 0xa1d9b0: WriteBarrierInstr(obj = r1, val = r0)
    //     0xa1d9b0: ldurb           w16, [x1, #-1]
    //     0xa1d9b4: ldurb           w17, [x0, #-1]
    //     0xa1d9b8: and             x16, x17, x16, lsr #2
    //     0xa1d9bc: tst             x16, HEAP, lsr #32
    //     0xa1d9c0: b.eq            #0xa1d9c8
    //     0xa1d9c4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa1d9c8: LoadField: r0 = r2->field_f
    //     0xa1d9c8: ldur            w0, [x2, #0xf]
    // 0xa1d9cc: DecompressPointer r0
    //     0xa1d9cc: add             x0, x0, HEAP, lsl #32
    // 0xa1d9d0: LoadField: r4 = r0->field_13
    //     0xa1d9d0: ldur            w4, [x0, #0x13]
    // 0xa1d9d4: DecompressPointer r4
    //     0xa1d9d4: add             x4, x4, HEAP, lsl #32
    // 0xa1d9d8: stur            x4, [fp, #-0x18]
    // 0xa1d9dc: LoadField: r1 = r0->field_1f
    //     0xa1d9dc: ldur            w1, [x0, #0x1f]
    // 0xa1d9e0: DecompressPointer r1
    //     0xa1d9e0: add             x1, x1, HEAP, lsl #32
    // 0xa1d9e4: LoadField: r0 = r1->field_8f
    //     0xa1d9e4: ldur            w0, [x1, #0x8f]
    // 0xa1d9e8: DecompressPointer r0
    //     0xa1d9e8: add             x0, x0, HEAP, lsl #32
    // 0xa1d9ec: mov             x1, x0
    // 0xa1d9f0: r0 = value()
    //     0xa1d9f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1d9f4: LoadField: r2 = r0->field_53
    //     0xa1d9f4: ldur            w2, [x0, #0x53]
    // 0xa1d9f8: DecompressPointer r2
    //     0xa1d9f8: add             x2, x2, HEAP, lsl #32
    // 0xa1d9fc: cmp             w2, NULL
    // 0xa1da00: b.ne            #0xa1da10
    // 0xa1da04: ldur            x3, [fp, #-0x10]
    // 0xa1da08: r0 = Null
    //     0xa1da08: mov             x0, NULL
    // 0xa1da0c: b               #0xa1da54
    // 0xa1da10: ldur            x3, [fp, #-0x10]
    // 0xa1da14: LoadField: r0 = r3->field_f
    //     0xa1da14: ldur            w0, [x3, #0xf]
    // 0xa1da18: DecompressPointer r0
    //     0xa1da18: add             x0, x0, HEAP, lsl #32
    // 0xa1da1c: LoadField: r1 = r2->field_b
    //     0xa1da1c: ldur            w1, [x2, #0xb]
    // 0xa1da20: r4 = LoadInt32Instr(r0)
    //     0xa1da20: sbfx            x4, x0, #1, #0x1f
    //     0xa1da24: tbz             w0, #0, #0xa1da2c
    //     0xa1da28: ldur            x4, [x0, #7]
    // 0xa1da2c: r0 = LoadInt32Instr(r1)
    //     0xa1da2c: sbfx            x0, x1, #1, #0x1f
    // 0xa1da30: mov             x1, x4
    // 0xa1da34: cmp             x1, x0
    // 0xa1da38: b.hs            #0xa1dd38
    // 0xa1da3c: LoadField: r0 = r2->field_f
    //     0xa1da3c: ldur            w0, [x2, #0xf]
    // 0xa1da40: DecompressPointer r0
    //     0xa1da40: add             x0, x0, HEAP, lsl #32
    // 0xa1da44: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1da44: add             x16, x0, x4, lsl #2
    //     0xa1da48: ldur            w1, [x16, #0xf]
    // 0xa1da4c: DecompressPointer r1
    //     0xa1da4c: add             x1, x1, HEAP, lsl #32
    // 0xa1da50: mov             x0, x1
    // 0xa1da54: cmp             w0, NULL
    // 0xa1da58: b.ne            #0xa1da68
    // 0xa1da5c: r0 = SkuDetails()
    //     0xa1da5c: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0xa1da60: mov             x2, x0
    // 0xa1da64: b               #0xa1da6c
    // 0xa1da68: mov             x2, x0
    // 0xa1da6c: ldur            x1, [fp, #-0x18]
    // 0xa1da70: r0 = contains()
    //     0xa1da70: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa1da74: tbnz            w0, #4, #0xa1db28
    // 0xa1da78: ldur            x0, [fp, #-8]
    // 0xa1da7c: LoadField: r1 = r0->field_f
    //     0xa1da7c: ldur            w1, [x0, #0xf]
    // 0xa1da80: DecompressPointer r1
    //     0xa1da80: add             x1, x1, HEAP, lsl #32
    // 0xa1da84: LoadField: r2 = r1->field_13
    //     0xa1da84: ldur            w2, [x1, #0x13]
    // 0xa1da88: DecompressPointer r2
    //     0xa1da88: add             x2, x2, HEAP, lsl #32
    // 0xa1da8c: stur            x2, [fp, #-0x18]
    // 0xa1da90: LoadField: r3 = r1->field_1f
    //     0xa1da90: ldur            w3, [x1, #0x1f]
    // 0xa1da94: DecompressPointer r3
    //     0xa1da94: add             x3, x3, HEAP, lsl #32
    // 0xa1da98: LoadField: r1 = r3->field_8f
    //     0xa1da98: ldur            w1, [x3, #0x8f]
    // 0xa1da9c: DecompressPointer r1
    //     0xa1da9c: add             x1, x1, HEAP, lsl #32
    // 0xa1daa0: r0 = value()
    //     0xa1daa0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1daa4: LoadField: r2 = r0->field_53
    //     0xa1daa4: ldur            w2, [x0, #0x53]
    // 0xa1daa8: DecompressPointer r2
    //     0xa1daa8: add             x2, x2, HEAP, lsl #32
    // 0xa1daac: cmp             w2, NULL
    // 0xa1dab0: b.ne            #0xa1dac0
    // 0xa1dab4: ldur            x3, [fp, #-0x10]
    // 0xa1dab8: r0 = Null
    //     0xa1dab8: mov             x0, NULL
    // 0xa1dabc: b               #0xa1db04
    // 0xa1dac0: ldur            x3, [fp, #-0x10]
    // 0xa1dac4: LoadField: r0 = r3->field_f
    //     0xa1dac4: ldur            w0, [x3, #0xf]
    // 0xa1dac8: DecompressPointer r0
    //     0xa1dac8: add             x0, x0, HEAP, lsl #32
    // 0xa1dacc: LoadField: r1 = r2->field_b
    //     0xa1dacc: ldur            w1, [x2, #0xb]
    // 0xa1dad0: r4 = LoadInt32Instr(r0)
    //     0xa1dad0: sbfx            x4, x0, #1, #0x1f
    //     0xa1dad4: tbz             w0, #0, #0xa1dadc
    //     0xa1dad8: ldur            x4, [x0, #7]
    // 0xa1dadc: r0 = LoadInt32Instr(r1)
    //     0xa1dadc: sbfx            x0, x1, #1, #0x1f
    // 0xa1dae0: mov             x1, x4
    // 0xa1dae4: cmp             x1, x0
    // 0xa1dae8: b.hs            #0xa1dd3c
    // 0xa1daec: LoadField: r0 = r2->field_f
    //     0xa1daec: ldur            w0, [x2, #0xf]
    // 0xa1daf0: DecompressPointer r0
    //     0xa1daf0: add             x0, x0, HEAP, lsl #32
    // 0xa1daf4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1daf4: add             x16, x0, x4, lsl #2
    //     0xa1daf8: ldur            w1, [x16, #0xf]
    // 0xa1dafc: DecompressPointer r1
    //     0xa1dafc: add             x1, x1, HEAP, lsl #32
    // 0xa1db00: mov             x0, x1
    // 0xa1db04: cmp             w0, NULL
    // 0xa1db08: b.ne            #0xa1db18
    // 0xa1db0c: r0 = SkuDetails()
    //     0xa1db0c: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0xa1db10: mov             x2, x0
    // 0xa1db14: b               #0xa1db1c
    // 0xa1db18: mov             x2, x0
    // 0xa1db1c: ldur            x1, [fp, #-0x18]
    // 0xa1db20: r0 = remove()
    //     0xa1db20: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0xa1db24: b               #0xa1dc48
    // 0xa1db28: ldur            x0, [fp, #-8]
    // 0xa1db2c: LoadField: r1 = r0->field_f
    //     0xa1db2c: ldur            w1, [x0, #0xf]
    // 0xa1db30: DecompressPointer r1
    //     0xa1db30: add             x1, x1, HEAP, lsl #32
    // 0xa1db34: LoadField: r2 = r1->field_13
    //     0xa1db34: ldur            w2, [x1, #0x13]
    // 0xa1db38: DecompressPointer r2
    //     0xa1db38: add             x2, x2, HEAP, lsl #32
    // 0xa1db3c: stur            x2, [fp, #-0x18]
    // 0xa1db40: LoadField: r3 = r1->field_1f
    //     0xa1db40: ldur            w3, [x1, #0x1f]
    // 0xa1db44: DecompressPointer r3
    //     0xa1db44: add             x3, x3, HEAP, lsl #32
    // 0xa1db48: LoadField: r1 = r3->field_8f
    //     0xa1db48: ldur            w1, [x3, #0x8f]
    // 0xa1db4c: DecompressPointer r1
    //     0xa1db4c: add             x1, x1, HEAP, lsl #32
    // 0xa1db50: r0 = value()
    //     0xa1db50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1db54: LoadField: r2 = r0->field_53
    //     0xa1db54: ldur            w2, [x0, #0x53]
    // 0xa1db58: DecompressPointer r2
    //     0xa1db58: add             x2, x2, HEAP, lsl #32
    // 0xa1db5c: cmp             w2, NULL
    // 0xa1db60: b.ne            #0xa1db70
    // 0xa1db64: ldur            x3, [fp, #-0x10]
    // 0xa1db68: r0 = Null
    //     0xa1db68: mov             x0, NULL
    // 0xa1db6c: b               #0xa1dbb4
    // 0xa1db70: ldur            x3, [fp, #-0x10]
    // 0xa1db74: LoadField: r0 = r3->field_f
    //     0xa1db74: ldur            w0, [x3, #0xf]
    // 0xa1db78: DecompressPointer r0
    //     0xa1db78: add             x0, x0, HEAP, lsl #32
    // 0xa1db7c: LoadField: r1 = r2->field_b
    //     0xa1db7c: ldur            w1, [x2, #0xb]
    // 0xa1db80: r4 = LoadInt32Instr(r0)
    //     0xa1db80: sbfx            x4, x0, #1, #0x1f
    //     0xa1db84: tbz             w0, #0, #0xa1db8c
    //     0xa1db88: ldur            x4, [x0, #7]
    // 0xa1db8c: r0 = LoadInt32Instr(r1)
    //     0xa1db8c: sbfx            x0, x1, #1, #0x1f
    // 0xa1db90: mov             x1, x4
    // 0xa1db94: cmp             x1, x0
    // 0xa1db98: b.hs            #0xa1dd40
    // 0xa1db9c: LoadField: r0 = r2->field_f
    //     0xa1db9c: ldur            w0, [x2, #0xf]
    // 0xa1dba0: DecompressPointer r0
    //     0xa1dba0: add             x0, x0, HEAP, lsl #32
    // 0xa1dba4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa1dba4: add             x16, x0, x4, lsl #2
    //     0xa1dba8: ldur            w1, [x16, #0xf]
    // 0xa1dbac: DecompressPointer r1
    //     0xa1dbac: add             x1, x1, HEAP, lsl #32
    // 0xa1dbb0: mov             x0, x1
    // 0xa1dbb4: cmp             w0, NULL
    // 0xa1dbb8: b.ne            #0xa1dbc8
    // 0xa1dbbc: r0 = SkuDetails()
    //     0xa1dbbc: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0xa1dbc0: mov             x2, x0
    // 0xa1dbc4: b               #0xa1dbcc
    // 0xa1dbc8: mov             x2, x0
    // 0xa1dbcc: ldur            x0, [fp, #-0x18]
    // 0xa1dbd0: stur            x2, [fp, #-0x28]
    // 0xa1dbd4: LoadField: r1 = r0->field_b
    //     0xa1dbd4: ldur            w1, [x0, #0xb]
    // 0xa1dbd8: LoadField: r3 = r0->field_f
    //     0xa1dbd8: ldur            w3, [x0, #0xf]
    // 0xa1dbdc: DecompressPointer r3
    //     0xa1dbdc: add             x3, x3, HEAP, lsl #32
    // 0xa1dbe0: LoadField: r4 = r3->field_b
    //     0xa1dbe0: ldur            w4, [x3, #0xb]
    // 0xa1dbe4: r3 = LoadInt32Instr(r1)
    //     0xa1dbe4: sbfx            x3, x1, #1, #0x1f
    // 0xa1dbe8: stur            x3, [fp, #-0x20]
    // 0xa1dbec: r1 = LoadInt32Instr(r4)
    //     0xa1dbec: sbfx            x1, x4, #1, #0x1f
    // 0xa1dbf0: cmp             x3, x1
    // 0xa1dbf4: b.ne            #0xa1dc00
    // 0xa1dbf8: mov             x1, x0
    // 0xa1dbfc: r0 = _growToNextCapacity()
    //     0xa1dbfc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa1dc00: ldur            x0, [fp, #-0x18]
    // 0xa1dc04: ldur            x2, [fp, #-0x20]
    // 0xa1dc08: add             x1, x2, #1
    // 0xa1dc0c: lsl             x3, x1, #1
    // 0xa1dc10: StoreField: r0->field_b = r3
    //     0xa1dc10: stur            w3, [x0, #0xb]
    // 0xa1dc14: LoadField: r1 = r0->field_f
    //     0xa1dc14: ldur            w1, [x0, #0xf]
    // 0xa1dc18: DecompressPointer r1
    //     0xa1dc18: add             x1, x1, HEAP, lsl #32
    // 0xa1dc1c: ldur            x0, [fp, #-0x28]
    // 0xa1dc20: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa1dc20: add             x25, x1, x2, lsl #2
    //     0xa1dc24: add             x25, x25, #0xf
    //     0xa1dc28: str             w0, [x25]
    //     0xa1dc2c: tbz             w0, #0, #0xa1dc48
    //     0xa1dc30: ldurb           w16, [x1, #-1]
    //     0xa1dc34: ldurb           w17, [x0, #-1]
    //     0xa1dc38: and             x16, x17, x16, lsr #2
    //     0xa1dc3c: tst             x16, HEAP, lsr #32
    //     0xa1dc40: b.eq            #0xa1dc48
    //     0xa1dc44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa1dc48: ldur            x0, [fp, #-8]
    // 0xa1dc4c: LoadField: r1 = r0->field_f
    //     0xa1dc4c: ldur            w1, [x0, #0xf]
    // 0xa1dc50: DecompressPointer r1
    //     0xa1dc50: add             x1, x1, HEAP, lsl #32
    // 0xa1dc54: LoadField: r2 = r1->field_1f
    //     0xa1dc54: ldur            w2, [x1, #0x1f]
    // 0xa1dc58: DecompressPointer r2
    //     0xa1dc58: add             x2, x2, HEAP, lsl #32
    // 0xa1dc5c: stur            x2, [fp, #-0x18]
    // 0xa1dc60: LoadField: r1 = r2->field_8f
    //     0xa1dc60: ldur            w1, [x2, #0x8f]
    // 0xa1dc64: DecompressPointer r1
    //     0xa1dc64: add             x1, x1, HEAP, lsl #32
    // 0xa1dc68: r0 = value()
    //     0xa1dc68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1dc6c: mov             x2, x0
    // 0xa1dc70: ldur            x0, [fp, #-8]
    // 0xa1dc74: stur            x2, [fp, #-0x28]
    // 0xa1dc78: LoadField: r1 = r0->field_f
    //     0xa1dc78: ldur            w1, [x0, #0xf]
    // 0xa1dc7c: DecompressPointer r1
    //     0xa1dc7c: add             x1, x1, HEAP, lsl #32
    // 0xa1dc80: LoadField: r0 = r1->field_1f
    //     0xa1dc80: ldur            w0, [x1, #0x1f]
    // 0xa1dc84: DecompressPointer r0
    //     0xa1dc84: add             x0, x0, HEAP, lsl #32
    // 0xa1dc88: LoadField: r1 = r0->field_8f
    //     0xa1dc88: ldur            w1, [x0, #0x8f]
    // 0xa1dc8c: DecompressPointer r1
    //     0xa1dc8c: add             x1, x1, HEAP, lsl #32
    // 0xa1dc90: r0 = value()
    //     0xa1dc90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa1dc94: LoadField: r2 = r0->field_53
    //     0xa1dc94: ldur            w2, [x0, #0x53]
    // 0xa1dc98: DecompressPointer r2
    //     0xa1dc98: add             x2, x2, HEAP, lsl #32
    // 0xa1dc9c: cmp             w2, NULL
    // 0xa1dca0: b.ne            #0xa1dcac
    // 0xa1dca4: r0 = Null
    //     0xa1dca4: mov             x0, NULL
    // 0xa1dca8: b               #0xa1dcf8
    // 0xa1dcac: ldur            x0, [fp, #-0x10]
    // 0xa1dcb0: LoadField: r1 = r0->field_f
    //     0xa1dcb0: ldur            w1, [x0, #0xf]
    // 0xa1dcb4: DecompressPointer r1
    //     0xa1dcb4: add             x1, x1, HEAP, lsl #32
    // 0xa1dcb8: LoadField: r0 = r2->field_b
    //     0xa1dcb8: ldur            w0, [x2, #0xb]
    // 0xa1dcbc: r3 = LoadInt32Instr(r1)
    //     0xa1dcbc: sbfx            x3, x1, #1, #0x1f
    //     0xa1dcc0: tbz             w1, #0, #0xa1dcc8
    //     0xa1dcc4: ldur            x3, [x1, #7]
    // 0xa1dcc8: r1 = LoadInt32Instr(r0)
    //     0xa1dcc8: sbfx            x1, x0, #1, #0x1f
    // 0xa1dccc: mov             x0, x1
    // 0xa1dcd0: mov             x1, x3
    // 0xa1dcd4: cmp             x1, x0
    // 0xa1dcd8: b.hs            #0xa1dd44
    // 0xa1dcdc: LoadField: r0 = r2->field_f
    //     0xa1dcdc: ldur            w0, [x2, #0xf]
    // 0xa1dce0: DecompressPointer r0
    //     0xa1dce0: add             x0, x0, HEAP, lsl #32
    // 0xa1dce4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa1dce4: add             x16, x0, x3, lsl #2
    //     0xa1dce8: ldur            w1, [x16, #0xf]
    // 0xa1dcec: DecompressPointer r1
    //     0xa1dcec: add             x1, x1, HEAP, lsl #32
    // 0xa1dcf0: LoadField: r0 = r1->field_7
    //     0xa1dcf0: ldur            w0, [x1, #7]
    // 0xa1dcf4: DecompressPointer r0
    //     0xa1dcf4: add             x0, x0, HEAP, lsl #32
    // 0xa1dcf8: cmp             w0, NULL
    // 0xa1dcfc: b.ne            #0xa1dd08
    // 0xa1dd00: r3 = ""
    //     0xa1dd00: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa1dd04: b               #0xa1dd0c
    // 0xa1dd08: mov             x3, x0
    // 0xa1dd0c: ldur            x1, [fp, #-0x18]
    // 0xa1dd10: ldur            x2, [fp, #-0x28]
    // 0xa1dd14: r5 = 1
    //     0xa1dd14: movz            x5, #0x1
    // 0xa1dd18: r0 = getCataloguePricing()
    //     0xa1dd18: bl              #0xa1c638  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::getCataloguePricing
    // 0xa1dd1c: r0 = Null
    //     0xa1dd1c: mov             x0, NULL
    // 0xa1dd20: LeaveFrame
    //     0xa1dd20: mov             SP, fp
    //     0xa1dd24: ldp             fp, lr, [SP], #0x10
    // 0xa1dd28: ret
    //     0xa1dd28: ret             
    // 0xa1dd2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1dd2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1dd30: b               #0xa1d8cc
    // 0xa1dd34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1dd34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1dd38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1dd38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1dd3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1dd3c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1dd40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1dd40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa1dd44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa1dd44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb49980, size: 0x64
    // 0xb49980: EnterFrame
    //     0xb49980: stp             fp, lr, [SP, #-0x10]!
    //     0xb49984: mov             fp, SP
    // 0xb49988: AllocStack(0x18)
    //     0xb49988: sub             SP, SP, #0x18
    // 0xb4998c: SetupParameters(_EditBagBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4998c: stur            x1, [fp, #-8]
    //     0xb49990: stur            x2, [fp, #-0x10]
    // 0xb49994: r1 = 2
    //     0xb49994: movz            x1, #0x2
    // 0xb49998: r0 = AllocateContext()
    //     0xb49998: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4999c: mov             x1, x0
    // 0xb499a0: ldur            x0, [fp, #-8]
    // 0xb499a4: stur            x1, [fp, #-0x18]
    // 0xb499a8: StoreField: r1->field_f = r0
    //     0xb499a8: stur            w0, [x1, #0xf]
    // 0xb499ac: ldur            x0, [fp, #-0x10]
    // 0xb499b0: StoreField: r1->field_13 = r0
    //     0xb499b0: stur            w0, [x1, #0x13]
    // 0xb499b4: r0 = Obx()
    //     0xb499b4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb499b8: ldur            x2, [fp, #-0x18]
    // 0xb499bc: r1 = Function '<anonymous closure>':.
    //     0xb499bc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56cb0] AnonymousClosure: (0xa1a718), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xb499c0: ldr             x1, [x1, #0xcb0]
    // 0xb499c4: stur            x0, [fp, #-8]
    // 0xb499c8: r0 = AllocateClosure()
    //     0xb499c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb499cc: mov             x1, x0
    // 0xb499d0: ldur            x0, [fp, #-8]
    // 0xb499d4: StoreField: r0->field_b = r1
    //     0xb499d4: stur            w1, [x0, #0xb]
    // 0xb499d8: LeaveFrame
    //     0xb499d8: mov             SP, fp
    //     0xb499dc: ldp             fp, lr, [SP], #0x10
    // 0xb499e0: ret
    //     0xb499e0: ret             
  }
}

// class id: 4099, size: 0x18, field offset: 0xc
//   const constructor, 
class EditBagBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7eca4, size: 0x48
    // 0xc7eca4: EnterFrame
    //     0xc7eca4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eca8: mov             fp, SP
    // 0xc7ecac: AllocStack(0x8)
    //     0xc7ecac: sub             SP, SP, #8
    // 0xc7ecb0: CheckStackOverflow
    //     0xc7ecb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7ecb4: cmp             SP, x16
    //     0xc7ecb8: b.ls            #0xc7ece4
    // 0xc7ecbc: r1 = <EditBagBottomSheet>
    //     0xc7ecbc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a48] TypeArguments: <EditBagBottomSheet>
    //     0xc7ecc0: ldr             x1, [x1, #0xa48]
    // 0xc7ecc4: r0 = _EditBagBottomSheetState()
    //     0xc7ecc4: bl              #0xc7ecec  ; Allocate_EditBagBottomSheetStateStub -> _EditBagBottomSheetState (size=0x24)
    // 0xc7ecc8: mov             x1, x0
    // 0xc7eccc: stur            x0, [fp, #-8]
    // 0xc7ecd0: r0 = _EditBagBottomSheetState()
    //     0xc7ecd0: bl              #0xc7af20  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::_EditBagBottomSheetState
    // 0xc7ecd4: ldur            x0, [fp, #-8]
    // 0xc7ecd8: LeaveFrame
    //     0xc7ecd8: mov             SP, fp
    //     0xc7ecdc: ldp             fp, lr, [SP], #0x10
    // 0xc7ece0: ret
    //     0xc7ece0: ret             
    // 0xc7ece4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7ece4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7ece8: b               #0xc7ecbc
  }
}
