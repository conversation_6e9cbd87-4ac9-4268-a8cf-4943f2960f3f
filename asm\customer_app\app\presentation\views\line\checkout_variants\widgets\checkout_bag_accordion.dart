// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_bag_accordion.dart

// class id: 1049486, size: 0x8
class :: {
}

// class id: 3279, size: 0x14, field offset: 0x14
class _CheckoutBagAccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbb3b8c, size: 0x1360
    // 0xbb3b8c: EnterFrame
    //     0xbb3b8c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb3b90: mov             fp, SP
    // 0xbb3b94: AllocStack(0x98)
    //     0xbb3b94: sub             SP, SP, #0x98
    // 0xbb3b98: SetupParameters(_CheckoutBagAccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbb3b98: mov             x0, x1
    //     0xbb3b9c: stur            x1, [fp, #-8]
    //     0xbb3ba0: mov             x1, x2
    //     0xbb3ba4: stur            x2, [fp, #-0x10]
    // 0xbb3ba8: CheckStackOverflow
    //     0xbb3ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb3bac: cmp             SP, x16
    //     0xbb3bb0: b.ls            #0xbb4ebc
    // 0xbb3bb4: r1 = 1
    //     0xbb3bb4: movz            x1, #0x1
    // 0xbb3bb8: r0 = AllocateContext()
    //     0xbb3bb8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb3bbc: mov             x3, x0
    // 0xbb3bc0: ldur            x0, [fp, #-8]
    // 0xbb3bc4: stur            x3, [fp, #-0x18]
    // 0xbb3bc8: StoreField: r3->field_f = r0
    //     0xbb3bc8: stur            w0, [x3, #0xf]
    // 0xbb3bcc: r1 = Null
    //     0xbb3bcc: mov             x1, NULL
    // 0xbb3bd0: r2 = 4
    //     0xbb3bd0: movz            x2, #0x4
    // 0xbb3bd4: r0 = AllocateArray()
    //     0xbb3bd4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb3bd8: r16 = "Bag "
    //     0xbb3bd8: add             x16, PP, #0x34, lsl #12  ; [pp+0x346a8] "Bag "
    //     0xbb3bdc: ldr             x16, [x16, #0x6a8]
    // 0xbb3be0: StoreField: r0->field_f = r16
    //     0xbb3be0: stur            w16, [x0, #0xf]
    // 0xbb3be4: ldur            x1, [fp, #-8]
    // 0xbb3be8: LoadField: r2 = r1->field_b
    //     0xbb3be8: ldur            w2, [x1, #0xb]
    // 0xbb3bec: DecompressPointer r2
    //     0xbb3bec: add             x2, x2, HEAP, lsl #32
    // 0xbb3bf0: cmp             w2, NULL
    // 0xbb3bf4: b.eq            #0xbb4ec4
    // 0xbb3bf8: LoadField: r3 = r2->field_b
    //     0xbb3bf8: ldur            w3, [x2, #0xb]
    // 0xbb3bfc: DecompressPointer r3
    //     0xbb3bfc: add             x3, x3, HEAP, lsl #32
    // 0xbb3c00: LoadField: r2 = r3->field_b
    //     0xbb3c00: ldur            w2, [x3, #0xb]
    // 0xbb3c04: DecompressPointer r2
    //     0xbb3c04: add             x2, x2, HEAP, lsl #32
    // 0xbb3c08: cmp             w2, NULL
    // 0xbb3c0c: b.ne            #0xbb3c18
    // 0xbb3c10: r2 = Null
    //     0xbb3c10: mov             x2, NULL
    // 0xbb3c14: b               #0xbb3c38
    // 0xbb3c18: LoadField: r3 = r2->field_f
    //     0xbb3c18: ldur            w3, [x2, #0xf]
    // 0xbb3c1c: DecompressPointer r3
    //     0xbb3c1c: add             x3, x3, HEAP, lsl #32
    // 0xbb3c20: cmp             w3, NULL
    // 0xbb3c24: b.ne            #0xbb3c30
    // 0xbb3c28: r2 = Null
    //     0xbb3c28: mov             x2, NULL
    // 0xbb3c2c: b               #0xbb3c38
    // 0xbb3c30: LoadField: r2 = r3->field_7
    //     0xbb3c30: ldur            w2, [x3, #7]
    // 0xbb3c34: DecompressPointer r2
    //     0xbb3c34: add             x2, x2, HEAP, lsl #32
    // 0xbb3c38: cmp             w2, NULL
    // 0xbb3c3c: b.ne            #0xbb3c44
    // 0xbb3c40: r2 = ""
    //     0xbb3c40: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb3c44: StoreField: r0->field_13 = r2
    //     0xbb3c44: stur            w2, [x0, #0x13]
    // 0xbb3c48: str             x0, [SP]
    // 0xbb3c4c: r0 = _interpolate()
    //     0xbb3c4c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb3c50: ldur            x1, [fp, #-0x10]
    // 0xbb3c54: stur            x0, [fp, #-0x20]
    // 0xbb3c58: r0 = of()
    //     0xbb3c58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3c5c: LoadField: r1 = r0->field_87
    //     0xbb3c5c: ldur            w1, [x0, #0x87]
    // 0xbb3c60: DecompressPointer r1
    //     0xbb3c60: add             x1, x1, HEAP, lsl #32
    // 0xbb3c64: LoadField: r0 = r1->field_7
    //     0xbb3c64: ldur            w0, [x1, #7]
    // 0xbb3c68: DecompressPointer r0
    //     0xbb3c68: add             x0, x0, HEAP, lsl #32
    // 0xbb3c6c: stur            x0, [fp, #-0x28]
    // 0xbb3c70: r1 = Instance_Color
    //     0xbb3c70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb3c74: d0 = 0.700000
    //     0xbb3c74: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb3c78: ldr             d0, [x17, #0xf48]
    // 0xbb3c7c: r0 = withOpacity()
    //     0xbb3c7c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb3c80: r16 = 14.000000
    //     0xbb3c80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb3c84: ldr             x16, [x16, #0x1d8]
    // 0xbb3c88: stp             x0, x16, [SP]
    // 0xbb3c8c: ldur            x1, [fp, #-0x28]
    // 0xbb3c90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3c90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3c94: ldr             x4, [x4, #0xaa0]
    // 0xbb3c98: r0 = copyWith()
    //     0xbb3c98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3c9c: stur            x0, [fp, #-0x28]
    // 0xbb3ca0: r0 = Text()
    //     0xbb3ca0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb3ca4: mov             x3, x0
    // 0xbb3ca8: ldur            x0, [fp, #-0x20]
    // 0xbb3cac: stur            x3, [fp, #-0x30]
    // 0xbb3cb0: StoreField: r3->field_b = r0
    //     0xbb3cb0: stur            w0, [x3, #0xb]
    // 0xbb3cb4: ldur            x0, [fp, #-0x28]
    // 0xbb3cb8: StoreField: r3->field_13 = r0
    //     0xbb3cb8: stur            w0, [x3, #0x13]
    // 0xbb3cbc: r1 = <Widget>
    //     0xbb3cbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb3cc0: r2 = 0
    //     0xbb3cc0: movz            x2, #0
    // 0xbb3cc4: r0 = _GrowableList()
    //     0xbb3cc4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbb3cc8: mov             x2, x0
    // 0xbb3ccc: ldur            x0, [fp, #-8]
    // 0xbb3cd0: stur            x2, [fp, #-0x28]
    // 0xbb3cd4: LoadField: r1 = r0->field_b
    //     0xbb3cd4: ldur            w1, [x0, #0xb]
    // 0xbb3cd8: DecompressPointer r1
    //     0xbb3cd8: add             x1, x1, HEAP, lsl #32
    // 0xbb3cdc: cmp             w1, NULL
    // 0xbb3ce0: b.eq            #0xbb4ec8
    // 0xbb3ce4: LoadField: r3 = r1->field_b
    //     0xbb3ce4: ldur            w3, [x1, #0xb]
    // 0xbb3ce8: DecompressPointer r3
    //     0xbb3ce8: add             x3, x3, HEAP, lsl #32
    // 0xbb3cec: LoadField: r1 = r3->field_b
    //     0xbb3cec: ldur            w1, [x3, #0xb]
    // 0xbb3cf0: DecompressPointer r1
    //     0xbb3cf0: add             x1, x1, HEAP, lsl #32
    // 0xbb3cf4: cmp             w1, NULL
    // 0xbb3cf8: b.eq            #0xbb4494
    // 0xbb3cfc: LoadField: r3 = r1->field_43
    //     0xbb3cfc: ldur            w3, [x1, #0x43]
    // 0xbb3d00: DecompressPointer r3
    //     0xbb3d00: add             x3, x3, HEAP, lsl #32
    // 0xbb3d04: cmp             w3, NULL
    // 0xbb3d08: b.eq            #0xbb4494
    // 0xbb3d0c: cmp             w3, NULL
    // 0xbb3d10: b.ne            #0xbb3d1c
    // 0xbb3d14: r1 = Null
    //     0xbb3d14: mov             x1, NULL
    // 0xbb3d18: b               #0xbb3d24
    // 0xbb3d1c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xbb3d1c: ldur            w1, [x3, #0x17]
    // 0xbb3d20: DecompressPointer r1
    //     0xbb3d20: add             x1, x1, HEAP, lsl #32
    // 0xbb3d24: cmp             w1, NULL
    // 0xbb3d28: b.ne            #0xbb3d34
    // 0xbb3d2c: r3 = false
    //     0xbb3d2c: add             x3, NULL, #0x30  ; false
    // 0xbb3d30: b               #0xbb3d38
    // 0xbb3d34: mov             x3, x1
    // 0xbb3d38: stur            x3, [fp, #-0x20]
    // 0xbb3d3c: r1 = Instance_Color
    //     0xbb3d3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb3d40: d0 = 0.070000
    //     0xbb3d40: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbb3d44: ldr             d0, [x17, #0x5f8]
    // 0xbb3d48: r0 = withOpacity()
    //     0xbb3d48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb3d4c: r16 = 1.000000
    //     0xbb3d4c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbb3d50: str             x16, [SP]
    // 0xbb3d54: mov             x2, x0
    // 0xbb3d58: r1 = Null
    //     0xbb3d58: mov             x1, NULL
    // 0xbb3d5c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbb3d5c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbb3d60: ldr             x4, [x4, #0x108]
    // 0xbb3d64: r0 = Border.all()
    //     0xbb3d64: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbb3d68: stur            x0, [fp, #-0x38]
    // 0xbb3d6c: r0 = BoxDecoration()
    //     0xbb3d6c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb3d70: mov             x2, x0
    // 0xbb3d74: ldur            x0, [fp, #-0x38]
    // 0xbb3d78: stur            x2, [fp, #-0x40]
    // 0xbb3d7c: StoreField: r2->field_f = r0
    //     0xbb3d7c: stur            w0, [x2, #0xf]
    // 0xbb3d80: r0 = Instance_LinearGradient
    //     0xbb3d80: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xbb3d84: ldr             x0, [x0, #0x660]
    // 0xbb3d88: StoreField: r2->field_1b = r0
    //     0xbb3d88: stur            w0, [x2, #0x1b]
    // 0xbb3d8c: r0 = Instance_BoxShape
    //     0xbb3d8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb3d90: ldr             x0, [x0, #0x80]
    // 0xbb3d94: StoreField: r2->field_23 = r0
    //     0xbb3d94: stur            w0, [x2, #0x23]
    // 0xbb3d98: ldur            x1, [fp, #-0x10]
    // 0xbb3d9c: r0 = of()
    //     0xbb3d9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3da0: LoadField: r1 = r0->field_87
    //     0xbb3da0: ldur            w1, [x0, #0x87]
    // 0xbb3da4: DecompressPointer r1
    //     0xbb3da4: add             x1, x1, HEAP, lsl #32
    // 0xbb3da8: LoadField: r0 = r1->field_7
    //     0xbb3da8: ldur            w0, [x1, #7]
    // 0xbb3dac: DecompressPointer r0
    //     0xbb3dac: add             x0, x0, HEAP, lsl #32
    // 0xbb3db0: r16 = 12.000000
    //     0xbb3db0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb3db4: ldr             x16, [x16, #0x9e8]
    // 0xbb3db8: r30 = Instance_Color
    //     0xbb3db8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbb3dbc: stp             lr, x16, [SP]
    // 0xbb3dc0: mov             x1, x0
    // 0xbb3dc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3dc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3dc8: ldr             x4, [x4, #0xaa0]
    // 0xbb3dcc: r0 = copyWith()
    //     0xbb3dcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3dd0: stur            x0, [fp, #-0x38]
    // 0xbb3dd4: r0 = Text()
    //     0xbb3dd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb3dd8: mov             x1, x0
    // 0xbb3ddc: r0 = "Free"
    //     0xbb3ddc: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbb3de0: ldr             x0, [x0, #0x668]
    // 0xbb3de4: stur            x1, [fp, #-0x48]
    // 0xbb3de8: StoreField: r1->field_b = r0
    //     0xbb3de8: stur            w0, [x1, #0xb]
    // 0xbb3dec: ldur            x2, [fp, #-0x38]
    // 0xbb3df0: StoreField: r1->field_13 = r2
    //     0xbb3df0: stur            w2, [x1, #0x13]
    // 0xbb3df4: r0 = Center()
    //     0xbb3df4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbb3df8: mov             x1, x0
    // 0xbb3dfc: r0 = Instance_Alignment
    //     0xbb3dfc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbb3e00: ldr             x0, [x0, #0xb10]
    // 0xbb3e04: stur            x1, [fp, #-0x38]
    // 0xbb3e08: StoreField: r1->field_f = r0
    //     0xbb3e08: stur            w0, [x1, #0xf]
    // 0xbb3e0c: ldur            x2, [fp, #-0x48]
    // 0xbb3e10: StoreField: r1->field_b = r2
    //     0xbb3e10: stur            w2, [x1, #0xb]
    // 0xbb3e14: r0 = RotatedBox()
    //     0xbb3e14: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbb3e18: mov             x1, x0
    // 0xbb3e1c: r0 = -1
    //     0xbb3e1c: movn            x0, #0
    // 0xbb3e20: stur            x1, [fp, #-0x48]
    // 0xbb3e24: StoreField: r1->field_f = r0
    //     0xbb3e24: stur            x0, [x1, #0xf]
    // 0xbb3e28: ldur            x2, [fp, #-0x38]
    // 0xbb3e2c: StoreField: r1->field_b = r2
    //     0xbb3e2c: stur            w2, [x1, #0xb]
    // 0xbb3e30: r0 = Container()
    //     0xbb3e30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb3e34: stur            x0, [fp, #-0x38]
    // 0xbb3e38: r16 = 24.000000
    //     0xbb3e38: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbb3e3c: ldr             x16, [x16, #0xba8]
    // 0xbb3e40: r30 = 56.000000
    //     0xbb3e40: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb3e44: ldr             lr, [lr, #0xb78]
    // 0xbb3e48: stp             lr, x16, [SP, #0x10]
    // 0xbb3e4c: r16 = Instance_Color
    //     0xbb3e4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb3e50: ldr             x16, [x16, #0x858]
    // 0xbb3e54: ldur            lr, [fp, #-0x48]
    // 0xbb3e58: stp             lr, x16, [SP]
    // 0xbb3e5c: mov             x1, x0
    // 0xbb3e60: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbb3e60: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbb3e64: ldr             x4, [x4, #0x670]
    // 0xbb3e68: r0 = Container()
    //     0xbb3e68: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb3e6c: ldur            x0, [fp, #-8]
    // 0xbb3e70: LoadField: r1 = r0->field_b
    //     0xbb3e70: ldur            w1, [x0, #0xb]
    // 0xbb3e74: DecompressPointer r1
    //     0xbb3e74: add             x1, x1, HEAP, lsl #32
    // 0xbb3e78: cmp             w1, NULL
    // 0xbb3e7c: b.eq            #0xbb4ecc
    // 0xbb3e80: LoadField: r2 = r1->field_b
    //     0xbb3e80: ldur            w2, [x1, #0xb]
    // 0xbb3e84: DecompressPointer r2
    //     0xbb3e84: add             x2, x2, HEAP, lsl #32
    // 0xbb3e88: LoadField: r1 = r2->field_b
    //     0xbb3e88: ldur            w1, [x2, #0xb]
    // 0xbb3e8c: DecompressPointer r1
    //     0xbb3e8c: add             x1, x1, HEAP, lsl #32
    // 0xbb3e90: cmp             w1, NULL
    // 0xbb3e94: b.ne            #0xbb3ea0
    // 0xbb3e98: r1 = Null
    //     0xbb3e98: mov             x1, NULL
    // 0xbb3e9c: b               #0xbb3ec0
    // 0xbb3ea0: LoadField: r2 = r1->field_43
    //     0xbb3ea0: ldur            w2, [x1, #0x43]
    // 0xbb3ea4: DecompressPointer r2
    //     0xbb3ea4: add             x2, x2, HEAP, lsl #32
    // 0xbb3ea8: cmp             w2, NULL
    // 0xbb3eac: b.ne            #0xbb3eb8
    // 0xbb3eb0: r1 = Null
    //     0xbb3eb0: mov             x1, NULL
    // 0xbb3eb4: b               #0xbb3ec0
    // 0xbb3eb8: LoadField: r1 = r2->field_7
    //     0xbb3eb8: ldur            w1, [x2, #7]
    // 0xbb3ebc: DecompressPointer r1
    //     0xbb3ebc: add             x1, x1, HEAP, lsl #32
    // 0xbb3ec0: cmp             w1, NULL
    // 0xbb3ec4: b.ne            #0xbb3ed0
    // 0xbb3ec8: r2 = ""
    //     0xbb3ec8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb3ecc: b               #0xbb3ed4
    // 0xbb3ed0: mov             x2, x1
    // 0xbb3ed4: stur            x2, [fp, #-0x48]
    // 0xbb3ed8: r0 = CachedNetworkImage()
    //     0xbb3ed8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbb3edc: stur            x0, [fp, #-0x50]
    // 0xbb3ee0: r16 = 56.000000
    //     0xbb3ee0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb3ee4: ldr             x16, [x16, #0xb78]
    // 0xbb3ee8: r30 = 56.000000
    //     0xbb3ee8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb3eec: ldr             lr, [lr, #0xb78]
    // 0xbb3ef0: stp             lr, x16, [SP, #8]
    // 0xbb3ef4: r16 = Instance_BoxFit
    //     0xbb3ef4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbb3ef8: ldr             x16, [x16, #0x118]
    // 0xbb3efc: str             x16, [SP]
    // 0xbb3f00: mov             x1, x0
    // 0xbb3f04: ldur            x2, [fp, #-0x48]
    // 0xbb3f08: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xbb3f08: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xbb3f0c: ldr             x4, [x4, #0xb40]
    // 0xbb3f10: r0 = CachedNetworkImage()
    //     0xbb3f10: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbb3f14: ldur            x0, [fp, #-8]
    // 0xbb3f18: LoadField: r1 = r0->field_b
    //     0xbb3f18: ldur            w1, [x0, #0xb]
    // 0xbb3f1c: DecompressPointer r1
    //     0xbb3f1c: add             x1, x1, HEAP, lsl #32
    // 0xbb3f20: cmp             w1, NULL
    // 0xbb3f24: b.eq            #0xbb4ed0
    // 0xbb3f28: LoadField: r2 = r1->field_b
    //     0xbb3f28: ldur            w2, [x1, #0xb]
    // 0xbb3f2c: DecompressPointer r2
    //     0xbb3f2c: add             x2, x2, HEAP, lsl #32
    // 0xbb3f30: LoadField: r1 = r2->field_b
    //     0xbb3f30: ldur            w1, [x2, #0xb]
    // 0xbb3f34: DecompressPointer r1
    //     0xbb3f34: add             x1, x1, HEAP, lsl #32
    // 0xbb3f38: cmp             w1, NULL
    // 0xbb3f3c: b.ne            #0xbb3f48
    // 0xbb3f40: r1 = Null
    //     0xbb3f40: mov             x1, NULL
    // 0xbb3f44: b               #0xbb3f68
    // 0xbb3f48: LoadField: r2 = r1->field_43
    //     0xbb3f48: ldur            w2, [x1, #0x43]
    // 0xbb3f4c: DecompressPointer r2
    //     0xbb3f4c: add             x2, x2, HEAP, lsl #32
    // 0xbb3f50: cmp             w2, NULL
    // 0xbb3f54: b.ne            #0xbb3f60
    // 0xbb3f58: r1 = Null
    //     0xbb3f58: mov             x1, NULL
    // 0xbb3f5c: b               #0xbb3f68
    // 0xbb3f60: LoadField: r1 = r2->field_b
    //     0xbb3f60: ldur            w1, [x2, #0xb]
    // 0xbb3f64: DecompressPointer r1
    //     0xbb3f64: add             x1, x1, HEAP, lsl #32
    // 0xbb3f68: cmp             w1, NULL
    // 0xbb3f6c: b.ne            #0xbb3f78
    // 0xbb3f70: r2 = ""
    //     0xbb3f70: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb3f74: b               #0xbb3f7c
    // 0xbb3f78: mov             x2, x1
    // 0xbb3f7c: ldur            x1, [fp, #-0x10]
    // 0xbb3f80: stur            x2, [fp, #-0x48]
    // 0xbb3f84: r0 = of()
    //     0xbb3f84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb3f88: LoadField: r1 = r0->field_87
    //     0xbb3f88: ldur            w1, [x0, #0x87]
    // 0xbb3f8c: DecompressPointer r1
    //     0xbb3f8c: add             x1, x1, HEAP, lsl #32
    // 0xbb3f90: LoadField: r0 = r1->field_7
    //     0xbb3f90: ldur            w0, [x1, #7]
    // 0xbb3f94: DecompressPointer r0
    //     0xbb3f94: add             x0, x0, HEAP, lsl #32
    // 0xbb3f98: r16 = 12.000000
    //     0xbb3f98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb3f9c: ldr             x16, [x16, #0x9e8]
    // 0xbb3fa0: r30 = Instance_Color
    //     0xbb3fa0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb3fa4: stp             lr, x16, [SP]
    // 0xbb3fa8: mov             x1, x0
    // 0xbb3fac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb3fac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb3fb0: ldr             x4, [x4, #0xaa0]
    // 0xbb3fb4: r0 = copyWith()
    //     0xbb3fb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb3fb8: stur            x0, [fp, #-0x58]
    // 0xbb3fbc: r0 = Text()
    //     0xbb3fbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb3fc0: mov             x1, x0
    // 0xbb3fc4: ldur            x0, [fp, #-0x48]
    // 0xbb3fc8: stur            x1, [fp, #-0x60]
    // 0xbb3fcc: StoreField: r1->field_b = r0
    //     0xbb3fcc: stur            w0, [x1, #0xb]
    // 0xbb3fd0: ldur            x0, [fp, #-0x58]
    // 0xbb3fd4: StoreField: r1->field_13 = r0
    //     0xbb3fd4: stur            w0, [x1, #0x13]
    // 0xbb3fd8: r0 = Instance_TextOverflow
    //     0xbb3fd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbb3fdc: ldr             x0, [x0, #0xe10]
    // 0xbb3fe0: StoreField: r1->field_2b = r0
    //     0xbb3fe0: stur            w0, [x1, #0x2b]
    // 0xbb3fe4: r2 = 2
    //     0xbb3fe4: movz            x2, #0x2
    // 0xbb3fe8: StoreField: r1->field_37 = r2
    //     0xbb3fe8: stur            w2, [x1, #0x37]
    // 0xbb3fec: r0 = SizedBox()
    //     0xbb3fec: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb3ff0: mov             x2, x0
    // 0xbb3ff4: r0 = 150.000000
    //     0xbb3ff4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbb3ff8: ldr             x0, [x0, #0x690]
    // 0xbb3ffc: stur            x2, [fp, #-0x48]
    // 0xbb4000: StoreField: r2->field_f = r0
    //     0xbb4000: stur            w0, [x2, #0xf]
    // 0xbb4004: ldur            x1, [fp, #-0x60]
    // 0xbb4008: StoreField: r2->field_b = r1
    //     0xbb4008: stur            w1, [x2, #0xb]
    // 0xbb400c: ldur            x1, [fp, #-0x10]
    // 0xbb4010: r0 = of()
    //     0xbb4010: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb4014: LoadField: r1 = r0->field_87
    //     0xbb4014: ldur            w1, [x0, #0x87]
    // 0xbb4018: DecompressPointer r1
    //     0xbb4018: add             x1, x1, HEAP, lsl #32
    // 0xbb401c: LoadField: r0 = r1->field_2b
    //     0xbb401c: ldur            w0, [x1, #0x2b]
    // 0xbb4020: DecompressPointer r0
    //     0xbb4020: add             x0, x0, HEAP, lsl #32
    // 0xbb4024: r16 = 12.000000
    //     0xbb4024: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb4028: ldr             x16, [x16, #0x9e8]
    // 0xbb402c: r30 = Instance_Color
    //     0xbb402c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb4030: ldr             lr, [lr, #0x858]
    // 0xbb4034: stp             lr, x16, [SP]
    // 0xbb4038: mov             x1, x0
    // 0xbb403c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb403c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb4040: ldr             x4, [x4, #0xaa0]
    // 0xbb4044: r0 = copyWith()
    //     0xbb4044: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb4048: stur            x0, [fp, #-0x58]
    // 0xbb404c: r0 = Text()
    //     0xbb404c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb4050: mov             x2, x0
    // 0xbb4054: r0 = "Free"
    //     0xbb4054: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbb4058: ldr             x0, [x0, #0x668]
    // 0xbb405c: stur            x2, [fp, #-0x60]
    // 0xbb4060: StoreField: r2->field_b = r0
    //     0xbb4060: stur            w0, [x2, #0xb]
    // 0xbb4064: ldur            x1, [fp, #-0x58]
    // 0xbb4068: StoreField: r2->field_13 = r1
    //     0xbb4068: stur            w1, [x2, #0x13]
    // 0xbb406c: ldur            x3, [fp, #-8]
    // 0xbb4070: LoadField: r1 = r3->field_b
    //     0xbb4070: ldur            w1, [x3, #0xb]
    // 0xbb4074: DecompressPointer r1
    //     0xbb4074: add             x1, x1, HEAP, lsl #32
    // 0xbb4078: cmp             w1, NULL
    // 0xbb407c: b.eq            #0xbb4ed4
    // 0xbb4080: LoadField: r4 = r1->field_b
    //     0xbb4080: ldur            w4, [x1, #0xb]
    // 0xbb4084: DecompressPointer r4
    //     0xbb4084: add             x4, x4, HEAP, lsl #32
    // 0xbb4088: LoadField: r1 = r4->field_b
    //     0xbb4088: ldur            w1, [x4, #0xb]
    // 0xbb408c: DecompressPointer r1
    //     0xbb408c: add             x1, x1, HEAP, lsl #32
    // 0xbb4090: cmp             w1, NULL
    // 0xbb4094: b.ne            #0xbb40a0
    // 0xbb4098: r1 = Null
    //     0xbb4098: mov             x1, NULL
    // 0xbb409c: b               #0xbb40c0
    // 0xbb40a0: LoadField: r4 = r1->field_43
    //     0xbb40a0: ldur            w4, [x1, #0x43]
    // 0xbb40a4: DecompressPointer r4
    //     0xbb40a4: add             x4, x4, HEAP, lsl #32
    // 0xbb40a8: cmp             w4, NULL
    // 0xbb40ac: b.ne            #0xbb40b8
    // 0xbb40b0: r1 = Null
    //     0xbb40b0: mov             x1, NULL
    // 0xbb40b4: b               #0xbb40c0
    // 0xbb40b8: LoadField: r1 = r4->field_13
    //     0xbb40b8: ldur            w1, [x4, #0x13]
    // 0xbb40bc: DecompressPointer r1
    //     0xbb40bc: add             x1, x1, HEAP, lsl #32
    // 0xbb40c0: cmp             w1, NULL
    // 0xbb40c4: b.ne            #0xbb40d0
    // 0xbb40c8: r9 = ""
    //     0xbb40c8: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb40cc: b               #0xbb40d4
    // 0xbb40d0: mov             x9, x1
    // 0xbb40d4: ldur            x7, [fp, #-0x28]
    // 0xbb40d8: ldur            x8, [fp, #-0x20]
    // 0xbb40dc: ldur            x6, [fp, #-0x38]
    // 0xbb40e0: ldur            x5, [fp, #-0x50]
    // 0xbb40e4: ldur            x4, [fp, #-0x48]
    // 0xbb40e8: ldur            x1, [fp, #-0x10]
    // 0xbb40ec: stur            x9, [fp, #-0x58]
    // 0xbb40f0: r0 = of()
    //     0xbb40f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb40f4: LoadField: r1 = r0->field_87
    //     0xbb40f4: ldur            w1, [x0, #0x87]
    // 0xbb40f8: DecompressPointer r1
    //     0xbb40f8: add             x1, x1, HEAP, lsl #32
    // 0xbb40fc: LoadField: r0 = r1->field_2b
    //     0xbb40fc: ldur            w0, [x1, #0x2b]
    // 0xbb4100: DecompressPointer r0
    //     0xbb4100: add             x0, x0, HEAP, lsl #32
    // 0xbb4104: r16 = 12.000000
    //     0xbb4104: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb4108: ldr             x16, [x16, #0x9e8]
    // 0xbb410c: r30 = Instance_TextDecoration
    //     0xbb410c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbb4110: ldr             lr, [lr, #0xe30]
    // 0xbb4114: stp             lr, x16, [SP]
    // 0xbb4118: mov             x1, x0
    // 0xbb411c: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbb411c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbb4120: ldr             x4, [x4, #0x698]
    // 0xbb4124: r0 = copyWith()
    //     0xbb4124: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb4128: stur            x0, [fp, #-0x68]
    // 0xbb412c: r0 = Text()
    //     0xbb412c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb4130: mov             x3, x0
    // 0xbb4134: ldur            x0, [fp, #-0x58]
    // 0xbb4138: stur            x3, [fp, #-0x70]
    // 0xbb413c: StoreField: r3->field_b = r0
    //     0xbb413c: stur            w0, [x3, #0xb]
    // 0xbb4140: ldur            x0, [fp, #-0x68]
    // 0xbb4144: StoreField: r3->field_13 = r0
    //     0xbb4144: stur            w0, [x3, #0x13]
    // 0xbb4148: r1 = Null
    //     0xbb4148: mov             x1, NULL
    // 0xbb414c: r2 = 6
    //     0xbb414c: movz            x2, #0x6
    // 0xbb4150: r0 = AllocateArray()
    //     0xbb4150: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb4154: mov             x2, x0
    // 0xbb4158: ldur            x0, [fp, #-0x60]
    // 0xbb415c: stur            x2, [fp, #-0x58]
    // 0xbb4160: StoreField: r2->field_f = r0
    //     0xbb4160: stur            w0, [x2, #0xf]
    // 0xbb4164: r16 = Instance_SizedBox
    //     0xbb4164: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbb4168: ldr             x16, [x16, #0xa50]
    // 0xbb416c: StoreField: r2->field_13 = r16
    //     0xbb416c: stur            w16, [x2, #0x13]
    // 0xbb4170: ldur            x0, [fp, #-0x70]
    // 0xbb4174: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4174: stur            w0, [x2, #0x17]
    // 0xbb4178: r1 = <Widget>
    //     0xbb4178: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb417c: r0 = AllocateGrowableArray()
    //     0xbb417c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb4180: mov             x1, x0
    // 0xbb4184: ldur            x0, [fp, #-0x58]
    // 0xbb4188: stur            x1, [fp, #-0x60]
    // 0xbb418c: StoreField: r1->field_f = r0
    //     0xbb418c: stur            w0, [x1, #0xf]
    // 0xbb4190: r2 = 6
    //     0xbb4190: movz            x2, #0x6
    // 0xbb4194: StoreField: r1->field_b = r2
    //     0xbb4194: stur            w2, [x1, #0xb]
    // 0xbb4198: r0 = Row()
    //     0xbb4198: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb419c: mov             x3, x0
    // 0xbb41a0: r0 = Instance_Axis
    //     0xbb41a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb41a4: stur            x3, [fp, #-0x58]
    // 0xbb41a8: StoreField: r3->field_f = r0
    //     0xbb41a8: stur            w0, [x3, #0xf]
    // 0xbb41ac: r4 = Instance_MainAxisAlignment
    //     0xbb41ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb41b0: ldr             x4, [x4, #0xa08]
    // 0xbb41b4: StoreField: r3->field_13 = r4
    //     0xbb41b4: stur            w4, [x3, #0x13]
    // 0xbb41b8: r5 = Instance_MainAxisSize
    //     0xbb41b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb41bc: ldr             x5, [x5, #0xa10]
    // 0xbb41c0: ArrayStore: r3[0] = r5  ; List_4
    //     0xbb41c0: stur            w5, [x3, #0x17]
    // 0xbb41c4: r6 = Instance_CrossAxisAlignment
    //     0xbb41c4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb41c8: ldr             x6, [x6, #0xa18]
    // 0xbb41cc: StoreField: r3->field_1b = r6
    //     0xbb41cc: stur            w6, [x3, #0x1b]
    // 0xbb41d0: r7 = Instance_VerticalDirection
    //     0xbb41d0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb41d4: ldr             x7, [x7, #0xa20]
    // 0xbb41d8: StoreField: r3->field_23 = r7
    //     0xbb41d8: stur            w7, [x3, #0x23]
    // 0xbb41dc: r8 = Instance_Clip
    //     0xbb41dc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb41e0: ldr             x8, [x8, #0x38]
    // 0xbb41e4: StoreField: r3->field_2b = r8
    //     0xbb41e4: stur            w8, [x3, #0x2b]
    // 0xbb41e8: StoreField: r3->field_2f = rZR
    //     0xbb41e8: stur            xzr, [x3, #0x2f]
    // 0xbb41ec: ldur            x1, [fp, #-0x60]
    // 0xbb41f0: StoreField: r3->field_b = r1
    //     0xbb41f0: stur            w1, [x3, #0xb]
    // 0xbb41f4: r1 = Null
    //     0xbb41f4: mov             x1, NULL
    // 0xbb41f8: r2 = 6
    //     0xbb41f8: movz            x2, #0x6
    // 0xbb41fc: r0 = AllocateArray()
    //     0xbb41fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb4200: mov             x2, x0
    // 0xbb4204: ldur            x0, [fp, #-0x48]
    // 0xbb4208: stur            x2, [fp, #-0x60]
    // 0xbb420c: StoreField: r2->field_f = r0
    //     0xbb420c: stur            w0, [x2, #0xf]
    // 0xbb4210: r16 = Instance_SizedBox
    //     0xbb4210: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbb4214: ldr             x16, [x16, #0xc70]
    // 0xbb4218: StoreField: r2->field_13 = r16
    //     0xbb4218: stur            w16, [x2, #0x13]
    // 0xbb421c: ldur            x0, [fp, #-0x58]
    // 0xbb4220: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4220: stur            w0, [x2, #0x17]
    // 0xbb4224: r1 = <Widget>
    //     0xbb4224: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb4228: r0 = AllocateGrowableArray()
    //     0xbb4228: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb422c: mov             x1, x0
    // 0xbb4230: ldur            x0, [fp, #-0x60]
    // 0xbb4234: stur            x1, [fp, #-0x48]
    // 0xbb4238: StoreField: r1->field_f = r0
    //     0xbb4238: stur            w0, [x1, #0xf]
    // 0xbb423c: r2 = 6
    //     0xbb423c: movz            x2, #0x6
    // 0xbb4240: StoreField: r1->field_b = r2
    //     0xbb4240: stur            w2, [x1, #0xb]
    // 0xbb4244: r0 = Column()
    //     0xbb4244: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb4248: mov             x1, x0
    // 0xbb424c: r0 = Instance_Axis
    //     0xbb424c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb4250: stur            x1, [fp, #-0x58]
    // 0xbb4254: StoreField: r1->field_f = r0
    //     0xbb4254: stur            w0, [x1, #0xf]
    // 0xbb4258: r2 = Instance_MainAxisAlignment
    //     0xbb4258: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb425c: ldr             x2, [x2, #0xa08]
    // 0xbb4260: StoreField: r1->field_13 = r2
    //     0xbb4260: stur            w2, [x1, #0x13]
    // 0xbb4264: r3 = Instance_MainAxisSize
    //     0xbb4264: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb4268: ldr             x3, [x3, #0xa10]
    // 0xbb426c: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb426c: stur            w3, [x1, #0x17]
    // 0xbb4270: r4 = Instance_CrossAxisAlignment
    //     0xbb4270: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb4274: ldr             x4, [x4, #0x890]
    // 0xbb4278: StoreField: r1->field_1b = r4
    //     0xbb4278: stur            w4, [x1, #0x1b]
    // 0xbb427c: r5 = Instance_VerticalDirection
    //     0xbb427c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb4280: ldr             x5, [x5, #0xa20]
    // 0xbb4284: StoreField: r1->field_23 = r5
    //     0xbb4284: stur            w5, [x1, #0x23]
    // 0xbb4288: r6 = Instance_Clip
    //     0xbb4288: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb428c: ldr             x6, [x6, #0x38]
    // 0xbb4290: StoreField: r1->field_2b = r6
    //     0xbb4290: stur            w6, [x1, #0x2b]
    // 0xbb4294: StoreField: r1->field_2f = rZR
    //     0xbb4294: stur            xzr, [x1, #0x2f]
    // 0xbb4298: ldur            x7, [fp, #-0x48]
    // 0xbb429c: StoreField: r1->field_b = r7
    //     0xbb429c: stur            w7, [x1, #0xb]
    // 0xbb42a0: r0 = Padding()
    //     0xbb42a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb42a4: mov             x2, x0
    // 0xbb42a8: r0 = Instance_EdgeInsets
    //     0xbb42a8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbb42ac: ldr             x0, [x0, #0xa78]
    // 0xbb42b0: stur            x2, [fp, #-0x48]
    // 0xbb42b4: StoreField: r2->field_f = r0
    //     0xbb42b4: stur            w0, [x2, #0xf]
    // 0xbb42b8: ldur            x1, [fp, #-0x58]
    // 0xbb42bc: StoreField: r2->field_b = r1
    //     0xbb42bc: stur            w1, [x2, #0xb]
    // 0xbb42c0: r1 = <FlexParentData>
    //     0xbb42c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb42c4: ldr             x1, [x1, #0xe00]
    // 0xbb42c8: r0 = Expanded()
    //     0xbb42c8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbb42cc: mov             x3, x0
    // 0xbb42d0: r0 = 1
    //     0xbb42d0: movz            x0, #0x1
    // 0xbb42d4: stur            x3, [fp, #-0x58]
    // 0xbb42d8: StoreField: r3->field_13 = r0
    //     0xbb42d8: stur            x0, [x3, #0x13]
    // 0xbb42dc: r4 = Instance_FlexFit
    //     0xbb42dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb42e0: ldr             x4, [x4, #0xe08]
    // 0xbb42e4: StoreField: r3->field_1b = r4
    //     0xbb42e4: stur            w4, [x3, #0x1b]
    // 0xbb42e8: ldur            x1, [fp, #-0x48]
    // 0xbb42ec: StoreField: r3->field_b = r1
    //     0xbb42ec: stur            w1, [x3, #0xb]
    // 0xbb42f0: r1 = Null
    //     0xbb42f0: mov             x1, NULL
    // 0xbb42f4: r2 = 6
    //     0xbb42f4: movz            x2, #0x6
    // 0xbb42f8: r0 = AllocateArray()
    //     0xbb42f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb42fc: mov             x2, x0
    // 0xbb4300: ldur            x0, [fp, #-0x38]
    // 0xbb4304: stur            x2, [fp, #-0x48]
    // 0xbb4308: StoreField: r2->field_f = r0
    //     0xbb4308: stur            w0, [x2, #0xf]
    // 0xbb430c: ldur            x0, [fp, #-0x50]
    // 0xbb4310: StoreField: r2->field_13 = r0
    //     0xbb4310: stur            w0, [x2, #0x13]
    // 0xbb4314: ldur            x0, [fp, #-0x58]
    // 0xbb4318: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4318: stur            w0, [x2, #0x17]
    // 0xbb431c: r1 = <Widget>
    //     0xbb431c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb4320: r0 = AllocateGrowableArray()
    //     0xbb4320: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb4324: mov             x1, x0
    // 0xbb4328: ldur            x0, [fp, #-0x48]
    // 0xbb432c: stur            x1, [fp, #-0x38]
    // 0xbb4330: StoreField: r1->field_f = r0
    //     0xbb4330: stur            w0, [x1, #0xf]
    // 0xbb4334: r2 = 6
    //     0xbb4334: movz            x2, #0x6
    // 0xbb4338: StoreField: r1->field_b = r2
    //     0xbb4338: stur            w2, [x1, #0xb]
    // 0xbb433c: r0 = Row()
    //     0xbb433c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb4340: mov             x1, x0
    // 0xbb4344: r0 = Instance_Axis
    //     0xbb4344: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb4348: stur            x1, [fp, #-0x48]
    // 0xbb434c: StoreField: r1->field_f = r0
    //     0xbb434c: stur            w0, [x1, #0xf]
    // 0xbb4350: r2 = Instance_MainAxisAlignment
    //     0xbb4350: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb4354: ldr             x2, [x2, #0xa08]
    // 0xbb4358: StoreField: r1->field_13 = r2
    //     0xbb4358: stur            w2, [x1, #0x13]
    // 0xbb435c: r3 = Instance_MainAxisSize
    //     0xbb435c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb4360: ldr             x3, [x3, #0xa10]
    // 0xbb4364: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb4364: stur            w3, [x1, #0x17]
    // 0xbb4368: r4 = Instance_CrossAxisAlignment
    //     0xbb4368: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb436c: ldr             x4, [x4, #0xa18]
    // 0xbb4370: StoreField: r1->field_1b = r4
    //     0xbb4370: stur            w4, [x1, #0x1b]
    // 0xbb4374: r5 = Instance_VerticalDirection
    //     0xbb4374: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb4378: ldr             x5, [x5, #0xa20]
    // 0xbb437c: StoreField: r1->field_23 = r5
    //     0xbb437c: stur            w5, [x1, #0x23]
    // 0xbb4380: r6 = Instance_Clip
    //     0xbb4380: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb4384: ldr             x6, [x6, #0x38]
    // 0xbb4388: StoreField: r1->field_2b = r6
    //     0xbb4388: stur            w6, [x1, #0x2b]
    // 0xbb438c: StoreField: r1->field_2f = rZR
    //     0xbb438c: stur            xzr, [x1, #0x2f]
    // 0xbb4390: ldur            x7, [fp, #-0x38]
    // 0xbb4394: StoreField: r1->field_b = r7
    //     0xbb4394: stur            w7, [x1, #0xb]
    // 0xbb4398: r0 = Container()
    //     0xbb4398: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb439c: stur            x0, [fp, #-0x38]
    // 0xbb43a0: ldur            x16, [fp, #-0x40]
    // 0xbb43a4: ldur            lr, [fp, #-0x48]
    // 0xbb43a8: stp             lr, x16, [SP]
    // 0xbb43ac: mov             x1, x0
    // 0xbb43b0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbb43b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbb43b4: ldr             x4, [x4, #0x88]
    // 0xbb43b8: r0 = Container()
    //     0xbb43b8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb43bc: r0 = Padding()
    //     0xbb43bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb43c0: mov             x1, x0
    // 0xbb43c4: r0 = Instance_EdgeInsets
    //     0xbb43c4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbb43c8: ldr             x0, [x0, #0x778]
    // 0xbb43cc: stur            x1, [fp, #-0x40]
    // 0xbb43d0: StoreField: r1->field_f = r0
    //     0xbb43d0: stur            w0, [x1, #0xf]
    // 0xbb43d4: ldur            x2, [fp, #-0x38]
    // 0xbb43d8: StoreField: r1->field_b = r2
    //     0xbb43d8: stur            w2, [x1, #0xb]
    // 0xbb43dc: r0 = Visibility()
    //     0xbb43dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb43e0: mov             x2, x0
    // 0xbb43e4: ldur            x0, [fp, #-0x40]
    // 0xbb43e8: stur            x2, [fp, #-0x38]
    // 0xbb43ec: StoreField: r2->field_b = r0
    //     0xbb43ec: stur            w0, [x2, #0xb]
    // 0xbb43f0: r0 = Instance_SizedBox
    //     0xbb43f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb43f4: StoreField: r2->field_f = r0
    //     0xbb43f4: stur            w0, [x2, #0xf]
    // 0xbb43f8: ldur            x1, [fp, #-0x20]
    // 0xbb43fc: StoreField: r2->field_13 = r1
    //     0xbb43fc: stur            w1, [x2, #0x13]
    // 0xbb4400: r3 = false
    //     0xbb4400: add             x3, NULL, #0x30  ; false
    // 0xbb4404: ArrayStore: r2[0] = r3  ; List_4
    //     0xbb4404: stur            w3, [x2, #0x17]
    // 0xbb4408: StoreField: r2->field_1b = r3
    //     0xbb4408: stur            w3, [x2, #0x1b]
    // 0xbb440c: StoreField: r2->field_1f = r3
    //     0xbb440c: stur            w3, [x2, #0x1f]
    // 0xbb4410: StoreField: r2->field_23 = r3
    //     0xbb4410: stur            w3, [x2, #0x23]
    // 0xbb4414: StoreField: r2->field_27 = r3
    //     0xbb4414: stur            w3, [x2, #0x27]
    // 0xbb4418: StoreField: r2->field_2b = r3
    //     0xbb4418: stur            w3, [x2, #0x2b]
    // 0xbb441c: ldur            x4, [fp, #-0x28]
    // 0xbb4420: LoadField: r1 = r4->field_b
    //     0xbb4420: ldur            w1, [x4, #0xb]
    // 0xbb4424: LoadField: r5 = r4->field_f
    //     0xbb4424: ldur            w5, [x4, #0xf]
    // 0xbb4428: DecompressPointer r5
    //     0xbb4428: add             x5, x5, HEAP, lsl #32
    // 0xbb442c: LoadField: r6 = r5->field_b
    //     0xbb442c: ldur            w6, [x5, #0xb]
    // 0xbb4430: r5 = LoadInt32Instr(r1)
    //     0xbb4430: sbfx            x5, x1, #1, #0x1f
    // 0xbb4434: stur            x5, [fp, #-0x78]
    // 0xbb4438: r1 = LoadInt32Instr(r6)
    //     0xbb4438: sbfx            x1, x6, #1, #0x1f
    // 0xbb443c: cmp             x5, x1
    // 0xbb4440: b.ne            #0xbb444c
    // 0xbb4444: mov             x1, x4
    // 0xbb4448: r0 = _growToNextCapacity()
    //     0xbb4448: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbb444c: ldur            x2, [fp, #-0x28]
    // 0xbb4450: ldur            x3, [fp, #-0x78]
    // 0xbb4454: add             x0, x3, #1
    // 0xbb4458: lsl             x1, x0, #1
    // 0xbb445c: StoreField: r2->field_b = r1
    //     0xbb445c: stur            w1, [x2, #0xb]
    // 0xbb4460: LoadField: r1 = r2->field_f
    //     0xbb4460: ldur            w1, [x2, #0xf]
    // 0xbb4464: DecompressPointer r1
    //     0xbb4464: add             x1, x1, HEAP, lsl #32
    // 0xbb4468: ldur            x0, [fp, #-0x38]
    // 0xbb446c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbb446c: add             x25, x1, x3, lsl #2
    //     0xbb4470: add             x25, x25, #0xf
    //     0xbb4474: str             w0, [x25]
    //     0xbb4478: tbz             w0, #0, #0xbb4494
    //     0xbb447c: ldurb           w16, [x1, #-1]
    //     0xbb4480: ldurb           w17, [x0, #-1]
    //     0xbb4484: and             x16, x17, x16, lsr #2
    //     0xbb4488: tst             x16, HEAP, lsr #32
    //     0xbb448c: b.eq            #0xbb4494
    //     0xbb4490: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb4494: ldur            x0, [fp, #-8]
    // 0xbb4498: LoadField: r1 = r0->field_b
    //     0xbb4498: ldur            w1, [x0, #0xb]
    // 0xbb449c: DecompressPointer r1
    //     0xbb449c: add             x1, x1, HEAP, lsl #32
    // 0xbb44a0: cmp             w1, NULL
    // 0xbb44a4: b.eq            #0xbb4ed8
    // 0xbb44a8: LoadField: r3 = r1->field_b
    //     0xbb44a8: ldur            w3, [x1, #0xb]
    // 0xbb44ac: DecompressPointer r3
    //     0xbb44ac: add             x3, x3, HEAP, lsl #32
    // 0xbb44b0: LoadField: r1 = r3->field_b
    //     0xbb44b0: ldur            w1, [x3, #0xb]
    // 0xbb44b4: DecompressPointer r1
    //     0xbb44b4: add             x1, x1, HEAP, lsl #32
    // 0xbb44b8: cmp             w1, NULL
    // 0xbb44bc: b.ne            #0xbb44c8
    // 0xbb44c0: mov             x3, x2
    // 0xbb44c4: b               #0xbb4c7c
    // 0xbb44c8: LoadField: r3 = r1->field_43
    //     0xbb44c8: ldur            w3, [x1, #0x43]
    // 0xbb44cc: DecompressPointer r3
    //     0xbb44cc: add             x3, x3, HEAP, lsl #32
    // 0xbb44d0: cmp             w3, NULL
    // 0xbb44d4: b.eq            #0xbb4c78
    // 0xbb44d8: cmp             w3, NULL
    // 0xbb44dc: b.ne            #0xbb44e8
    // 0xbb44e0: r1 = Null
    //     0xbb44e0: mov             x1, NULL
    // 0xbb44e4: b               #0xbb44f0
    // 0xbb44e8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xbb44e8: ldur            w1, [x3, #0x17]
    // 0xbb44ec: DecompressPointer r1
    //     0xbb44ec: add             x1, x1, HEAP, lsl #32
    // 0xbb44f0: cmp             w1, NULL
    // 0xbb44f4: b.ne            #0xbb44fc
    // 0xbb44f8: r1 = false
    //     0xbb44f8: add             x1, NULL, #0x30  ; false
    // 0xbb44fc: eor             x3, x1, #0x10
    // 0xbb4500: stur            x3, [fp, #-0x20]
    // 0xbb4504: r1 = Instance_Color
    //     0xbb4504: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb4508: d0 = 0.070000
    //     0xbb4508: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbb450c: ldr             d0, [x17, #0x5f8]
    // 0xbb4510: r0 = withOpacity()
    //     0xbb4510: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb4514: r16 = 1.000000
    //     0xbb4514: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbb4518: str             x16, [SP]
    // 0xbb451c: mov             x2, x0
    // 0xbb4520: r1 = Null
    //     0xbb4520: mov             x1, NULL
    // 0xbb4524: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbb4524: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbb4528: ldr             x4, [x4, #0x108]
    // 0xbb452c: r0 = Border.all()
    //     0xbb452c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbb4530: stur            x0, [fp, #-0x38]
    // 0xbb4534: r0 = BoxDecoration()
    //     0xbb4534: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbb4538: mov             x2, x0
    // 0xbb453c: ldur            x0, [fp, #-0x38]
    // 0xbb4540: stur            x2, [fp, #-0x40]
    // 0xbb4544: StoreField: r2->field_f = r0
    //     0xbb4544: stur            w0, [x2, #0xf]
    // 0xbb4548: r0 = Instance_BoxShape
    //     0xbb4548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb454c: ldr             x0, [x0, #0x80]
    // 0xbb4550: StoreField: r2->field_23 = r0
    //     0xbb4550: stur            w0, [x2, #0x23]
    // 0xbb4554: ldur            x1, [fp, #-0x10]
    // 0xbb4558: r0 = of()
    //     0xbb4558: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb455c: LoadField: r1 = r0->field_5b
    //     0xbb455c: ldur            w1, [x0, #0x5b]
    // 0xbb4560: DecompressPointer r1
    //     0xbb4560: add             x1, x1, HEAP, lsl #32
    // 0xbb4564: r0 = LoadClassIdInstr(r1)
    //     0xbb4564: ldur            x0, [x1, #-1]
    //     0xbb4568: ubfx            x0, x0, #0xc, #0x14
    // 0xbb456c: d0 = 0.400000
    //     0xbb456c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb4570: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbb4570: sub             lr, x0, #0xffa
    //     0xbb4574: ldr             lr, [x21, lr, lsl #3]
    //     0xbb4578: blr             lr
    // 0xbb457c: ldur            x1, [fp, #-0x10]
    // 0xbb4580: stur            x0, [fp, #-0x38]
    // 0xbb4584: r0 = of()
    //     0xbb4584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb4588: LoadField: r1 = r0->field_87
    //     0xbb4588: ldur            w1, [x0, #0x87]
    // 0xbb458c: DecompressPointer r1
    //     0xbb458c: add             x1, x1, HEAP, lsl #32
    // 0xbb4590: LoadField: r0 = r1->field_7
    //     0xbb4590: ldur            w0, [x1, #7]
    // 0xbb4594: DecompressPointer r0
    //     0xbb4594: add             x0, x0, HEAP, lsl #32
    // 0xbb4598: r16 = 12.000000
    //     0xbb4598: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb459c: ldr             x16, [x16, #0x9e8]
    // 0xbb45a0: r30 = Instance_Color
    //     0xbb45a0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbb45a4: stp             lr, x16, [SP]
    // 0xbb45a8: mov             x1, x0
    // 0xbb45ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb45ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb45b0: ldr             x4, [x4, #0xaa0]
    // 0xbb45b4: r0 = copyWith()
    //     0xbb45b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb45b8: stur            x0, [fp, #-0x48]
    // 0xbb45bc: r0 = Text()
    //     0xbb45bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb45c0: mov             x1, x0
    // 0xbb45c4: r0 = "Free"
    //     0xbb45c4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbb45c8: ldr             x0, [x0, #0x668]
    // 0xbb45cc: stur            x1, [fp, #-0x50]
    // 0xbb45d0: StoreField: r1->field_b = r0
    //     0xbb45d0: stur            w0, [x1, #0xb]
    // 0xbb45d4: ldur            x2, [fp, #-0x48]
    // 0xbb45d8: StoreField: r1->field_13 = r2
    //     0xbb45d8: stur            w2, [x1, #0x13]
    // 0xbb45dc: r0 = Center()
    //     0xbb45dc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbb45e0: mov             x1, x0
    // 0xbb45e4: r0 = Instance_Alignment
    //     0xbb45e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbb45e8: ldr             x0, [x0, #0xb10]
    // 0xbb45ec: stur            x1, [fp, #-0x48]
    // 0xbb45f0: StoreField: r1->field_f = r0
    //     0xbb45f0: stur            w0, [x1, #0xf]
    // 0xbb45f4: ldur            x0, [fp, #-0x50]
    // 0xbb45f8: StoreField: r1->field_b = r0
    //     0xbb45f8: stur            w0, [x1, #0xb]
    // 0xbb45fc: r0 = RotatedBox()
    //     0xbb45fc: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbb4600: mov             x1, x0
    // 0xbb4604: r0 = -1
    //     0xbb4604: movn            x0, #0
    // 0xbb4608: stur            x1, [fp, #-0x50]
    // 0xbb460c: StoreField: r1->field_f = r0
    //     0xbb460c: stur            x0, [x1, #0xf]
    // 0xbb4610: ldur            x0, [fp, #-0x48]
    // 0xbb4614: StoreField: r1->field_b = r0
    //     0xbb4614: stur            w0, [x1, #0xb]
    // 0xbb4618: r0 = Container()
    //     0xbb4618: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb461c: stur            x0, [fp, #-0x48]
    // 0xbb4620: r16 = 24.000000
    //     0xbb4620: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbb4624: ldr             x16, [x16, #0xba8]
    // 0xbb4628: r30 = 56.000000
    //     0xbb4628: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb462c: ldr             lr, [lr, #0xb78]
    // 0xbb4630: stp             lr, x16, [SP, #0x10]
    // 0xbb4634: ldur            x16, [fp, #-0x38]
    // 0xbb4638: ldur            lr, [fp, #-0x50]
    // 0xbb463c: stp             lr, x16, [SP]
    // 0xbb4640: mov             x1, x0
    // 0xbb4644: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbb4644: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbb4648: ldr             x4, [x4, #0x670]
    // 0xbb464c: r0 = Container()
    //     0xbb464c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb4650: ldur            x0, [fp, #-8]
    // 0xbb4654: LoadField: r1 = r0->field_b
    //     0xbb4654: ldur            w1, [x0, #0xb]
    // 0xbb4658: DecompressPointer r1
    //     0xbb4658: add             x1, x1, HEAP, lsl #32
    // 0xbb465c: cmp             w1, NULL
    // 0xbb4660: b.eq            #0xbb4edc
    // 0xbb4664: LoadField: r2 = r1->field_b
    //     0xbb4664: ldur            w2, [x1, #0xb]
    // 0xbb4668: DecompressPointer r2
    //     0xbb4668: add             x2, x2, HEAP, lsl #32
    // 0xbb466c: LoadField: r1 = r2->field_b
    //     0xbb466c: ldur            w1, [x2, #0xb]
    // 0xbb4670: DecompressPointer r1
    //     0xbb4670: add             x1, x1, HEAP, lsl #32
    // 0xbb4674: cmp             w1, NULL
    // 0xbb4678: b.ne            #0xbb4684
    // 0xbb467c: r1 = Null
    //     0xbb467c: mov             x1, NULL
    // 0xbb4680: b               #0xbb46a4
    // 0xbb4684: LoadField: r2 = r1->field_43
    //     0xbb4684: ldur            w2, [x1, #0x43]
    // 0xbb4688: DecompressPointer r2
    //     0xbb4688: add             x2, x2, HEAP, lsl #32
    // 0xbb468c: cmp             w2, NULL
    // 0xbb4690: b.ne            #0xbb469c
    // 0xbb4694: r1 = Null
    //     0xbb4694: mov             x1, NULL
    // 0xbb4698: b               #0xbb46a4
    // 0xbb469c: LoadField: r1 = r2->field_7
    //     0xbb469c: ldur            w1, [x2, #7]
    // 0xbb46a0: DecompressPointer r1
    //     0xbb46a0: add             x1, x1, HEAP, lsl #32
    // 0xbb46a4: cmp             w1, NULL
    // 0xbb46a8: b.ne            #0xbb46b4
    // 0xbb46ac: r2 = ""
    //     0xbb46ac: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb46b0: b               #0xbb46b8
    // 0xbb46b4: mov             x2, x1
    // 0xbb46b8: stur            x2, [fp, #-0x38]
    // 0xbb46bc: r0 = CachedNetworkImage()
    //     0xbb46bc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbb46c0: stur            x0, [fp, #-0x50]
    // 0xbb46c4: r16 = 56.000000
    //     0xbb46c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb46c8: ldr             x16, [x16, #0xb78]
    // 0xbb46cc: r30 = 56.000000
    //     0xbb46cc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb46d0: ldr             lr, [lr, #0xb78]
    // 0xbb46d4: stp             lr, x16, [SP, #8]
    // 0xbb46d8: r16 = Instance_BoxFit
    //     0xbb46d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbb46dc: ldr             x16, [x16, #0x118]
    // 0xbb46e0: str             x16, [SP]
    // 0xbb46e4: mov             x1, x0
    // 0xbb46e8: ldur            x2, [fp, #-0x38]
    // 0xbb46ec: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xbb46ec: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xbb46f0: ldr             x4, [x4, #0xb40]
    // 0xbb46f4: r0 = CachedNetworkImage()
    //     0xbb46f4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbb46f8: ldur            x0, [fp, #-8]
    // 0xbb46fc: LoadField: r1 = r0->field_b
    //     0xbb46fc: ldur            w1, [x0, #0xb]
    // 0xbb4700: DecompressPointer r1
    //     0xbb4700: add             x1, x1, HEAP, lsl #32
    // 0xbb4704: cmp             w1, NULL
    // 0xbb4708: b.eq            #0xbb4ee0
    // 0xbb470c: LoadField: r2 = r1->field_b
    //     0xbb470c: ldur            w2, [x1, #0xb]
    // 0xbb4710: DecompressPointer r2
    //     0xbb4710: add             x2, x2, HEAP, lsl #32
    // 0xbb4714: LoadField: r1 = r2->field_b
    //     0xbb4714: ldur            w1, [x2, #0xb]
    // 0xbb4718: DecompressPointer r1
    //     0xbb4718: add             x1, x1, HEAP, lsl #32
    // 0xbb471c: cmp             w1, NULL
    // 0xbb4720: b.ne            #0xbb472c
    // 0xbb4724: r1 = Null
    //     0xbb4724: mov             x1, NULL
    // 0xbb4728: b               #0xbb474c
    // 0xbb472c: LoadField: r2 = r1->field_43
    //     0xbb472c: ldur            w2, [x1, #0x43]
    // 0xbb4730: DecompressPointer r2
    //     0xbb4730: add             x2, x2, HEAP, lsl #32
    // 0xbb4734: cmp             w2, NULL
    // 0xbb4738: b.ne            #0xbb4744
    // 0xbb473c: r1 = Null
    //     0xbb473c: mov             x1, NULL
    // 0xbb4740: b               #0xbb474c
    // 0xbb4744: LoadField: r1 = r2->field_b
    //     0xbb4744: ldur            w1, [x2, #0xb]
    // 0xbb4748: DecompressPointer r1
    //     0xbb4748: add             x1, x1, HEAP, lsl #32
    // 0xbb474c: cmp             w1, NULL
    // 0xbb4750: b.ne            #0xbb475c
    // 0xbb4754: r2 = ""
    //     0xbb4754: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb4758: b               #0xbb4760
    // 0xbb475c: mov             x2, x1
    // 0xbb4760: ldur            x1, [fp, #-0x10]
    // 0xbb4764: stur            x2, [fp, #-0x38]
    // 0xbb4768: r0 = of()
    //     0xbb4768: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb476c: LoadField: r1 = r0->field_87
    //     0xbb476c: ldur            w1, [x0, #0x87]
    // 0xbb4770: DecompressPointer r1
    //     0xbb4770: add             x1, x1, HEAP, lsl #32
    // 0xbb4774: LoadField: r0 = r1->field_7
    //     0xbb4774: ldur            w0, [x1, #7]
    // 0xbb4778: DecompressPointer r0
    //     0xbb4778: add             x0, x0, HEAP, lsl #32
    // 0xbb477c: r16 = 12.000000
    //     0xbb477c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb4780: ldr             x16, [x16, #0x9e8]
    // 0xbb4784: r30 = Instance_Color
    //     0xbb4784: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb4788: stp             lr, x16, [SP]
    // 0xbb478c: mov             x1, x0
    // 0xbb4790: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb4790: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb4794: ldr             x4, [x4, #0xaa0]
    // 0xbb4798: r0 = copyWith()
    //     0xbb4798: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb479c: stur            x0, [fp, #-0x58]
    // 0xbb47a0: r0 = Text()
    //     0xbb47a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb47a4: mov             x1, x0
    // 0xbb47a8: ldur            x0, [fp, #-0x38]
    // 0xbb47ac: stur            x1, [fp, #-0x60]
    // 0xbb47b0: StoreField: r1->field_b = r0
    //     0xbb47b0: stur            w0, [x1, #0xb]
    // 0xbb47b4: ldur            x0, [fp, #-0x58]
    // 0xbb47b8: StoreField: r1->field_13 = r0
    //     0xbb47b8: stur            w0, [x1, #0x13]
    // 0xbb47bc: r0 = Instance_TextOverflow
    //     0xbb47bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbb47c0: ldr             x0, [x0, #0xe10]
    // 0xbb47c4: StoreField: r1->field_2b = r0
    //     0xbb47c4: stur            w0, [x1, #0x2b]
    // 0xbb47c8: r0 = 2
    //     0xbb47c8: movz            x0, #0x2
    // 0xbb47cc: StoreField: r1->field_37 = r0
    //     0xbb47cc: stur            w0, [x1, #0x37]
    // 0xbb47d0: r0 = SizedBox()
    //     0xbb47d0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbb47d4: mov             x2, x0
    // 0xbb47d8: r0 = 150.000000
    //     0xbb47d8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbb47dc: ldr             x0, [x0, #0x690]
    // 0xbb47e0: stur            x2, [fp, #-0x38]
    // 0xbb47e4: StoreField: r2->field_f = r0
    //     0xbb47e4: stur            w0, [x2, #0xf]
    // 0xbb47e8: ldur            x0, [fp, #-0x60]
    // 0xbb47ec: StoreField: r2->field_b = r0
    //     0xbb47ec: stur            w0, [x2, #0xb]
    // 0xbb47f0: ldur            x1, [fp, #-0x10]
    // 0xbb47f4: r0 = of()
    //     0xbb47f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb47f8: LoadField: r1 = r0->field_87
    //     0xbb47f8: ldur            w1, [x0, #0x87]
    // 0xbb47fc: DecompressPointer r1
    //     0xbb47fc: add             x1, x1, HEAP, lsl #32
    // 0xbb4800: LoadField: r0 = r1->field_2b
    //     0xbb4800: ldur            w0, [x1, #0x2b]
    // 0xbb4804: DecompressPointer r0
    //     0xbb4804: add             x0, x0, HEAP, lsl #32
    // 0xbb4808: r16 = 12.000000
    //     0xbb4808: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb480c: ldr             x16, [x16, #0x9e8]
    // 0xbb4810: r30 = Instance_Color
    //     0xbb4810: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb4814: stp             lr, x16, [SP]
    // 0xbb4818: mov             x1, x0
    // 0xbb481c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb481c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb4820: ldr             x4, [x4, #0xaa0]
    // 0xbb4824: r0 = copyWith()
    //     0xbb4824: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb4828: stur            x0, [fp, #-0x58]
    // 0xbb482c: r0 = Text()
    //     0xbb482c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb4830: mov             x2, x0
    // 0xbb4834: r0 = "Free"
    //     0xbb4834: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbb4838: ldr             x0, [x0, #0x668]
    // 0xbb483c: stur            x2, [fp, #-0x60]
    // 0xbb4840: StoreField: r2->field_b = r0
    //     0xbb4840: stur            w0, [x2, #0xb]
    // 0xbb4844: ldur            x0, [fp, #-0x58]
    // 0xbb4848: StoreField: r2->field_13 = r0
    //     0xbb4848: stur            w0, [x2, #0x13]
    // 0xbb484c: ldur            x0, [fp, #-8]
    // 0xbb4850: LoadField: r1 = r0->field_b
    //     0xbb4850: ldur            w1, [x0, #0xb]
    // 0xbb4854: DecompressPointer r1
    //     0xbb4854: add             x1, x1, HEAP, lsl #32
    // 0xbb4858: cmp             w1, NULL
    // 0xbb485c: b.eq            #0xbb4ee4
    // 0xbb4860: LoadField: r3 = r1->field_b
    //     0xbb4860: ldur            w3, [x1, #0xb]
    // 0xbb4864: DecompressPointer r3
    //     0xbb4864: add             x3, x3, HEAP, lsl #32
    // 0xbb4868: LoadField: r1 = r3->field_b
    //     0xbb4868: ldur            w1, [x3, #0xb]
    // 0xbb486c: DecompressPointer r1
    //     0xbb486c: add             x1, x1, HEAP, lsl #32
    // 0xbb4870: cmp             w1, NULL
    // 0xbb4874: b.ne            #0xbb4880
    // 0xbb4878: r1 = Null
    //     0xbb4878: mov             x1, NULL
    // 0xbb487c: b               #0xbb48a0
    // 0xbb4880: LoadField: r3 = r1->field_43
    //     0xbb4880: ldur            w3, [x1, #0x43]
    // 0xbb4884: DecompressPointer r3
    //     0xbb4884: add             x3, x3, HEAP, lsl #32
    // 0xbb4888: cmp             w3, NULL
    // 0xbb488c: b.ne            #0xbb4898
    // 0xbb4890: r1 = Null
    //     0xbb4890: mov             x1, NULL
    // 0xbb4894: b               #0xbb48a0
    // 0xbb4898: LoadField: r1 = r3->field_13
    //     0xbb4898: ldur            w1, [x3, #0x13]
    // 0xbb489c: DecompressPointer r1
    //     0xbb489c: add             x1, x1, HEAP, lsl #32
    // 0xbb48a0: cmp             w1, NULL
    // 0xbb48a4: b.ne            #0xbb48b0
    // 0xbb48a8: r8 = ""
    //     0xbb48a8: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb48ac: b               #0xbb48b4
    // 0xbb48b0: mov             x8, x1
    // 0xbb48b4: ldur            x6, [fp, #-0x28]
    // 0xbb48b8: ldur            x7, [fp, #-0x20]
    // 0xbb48bc: ldur            x5, [fp, #-0x48]
    // 0xbb48c0: ldur            x4, [fp, #-0x50]
    // 0xbb48c4: ldur            x3, [fp, #-0x38]
    // 0xbb48c8: ldur            x1, [fp, #-0x10]
    // 0xbb48cc: stur            x8, [fp, #-0x58]
    // 0xbb48d0: r0 = of()
    //     0xbb48d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb48d4: LoadField: r1 = r0->field_87
    //     0xbb48d4: ldur            w1, [x0, #0x87]
    // 0xbb48d8: DecompressPointer r1
    //     0xbb48d8: add             x1, x1, HEAP, lsl #32
    // 0xbb48dc: LoadField: r0 = r1->field_2b
    //     0xbb48dc: ldur            w0, [x1, #0x2b]
    // 0xbb48e0: DecompressPointer r0
    //     0xbb48e0: add             x0, x0, HEAP, lsl #32
    // 0xbb48e4: r16 = 12.000000
    //     0xbb48e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb48e8: ldr             x16, [x16, #0x9e8]
    // 0xbb48ec: r30 = Instance_TextDecoration
    //     0xbb48ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbb48f0: ldr             lr, [lr, #0xe30]
    // 0xbb48f4: stp             lr, x16, [SP]
    // 0xbb48f8: mov             x1, x0
    // 0xbb48fc: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbb48fc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbb4900: ldr             x4, [x4, #0x698]
    // 0xbb4904: r0 = copyWith()
    //     0xbb4904: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb4908: stur            x0, [fp, #-0x10]
    // 0xbb490c: r0 = Text()
    //     0xbb490c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb4910: mov             x3, x0
    // 0xbb4914: ldur            x0, [fp, #-0x58]
    // 0xbb4918: stur            x3, [fp, #-0x68]
    // 0xbb491c: StoreField: r3->field_b = r0
    //     0xbb491c: stur            w0, [x3, #0xb]
    // 0xbb4920: ldur            x0, [fp, #-0x10]
    // 0xbb4924: StoreField: r3->field_13 = r0
    //     0xbb4924: stur            w0, [x3, #0x13]
    // 0xbb4928: r1 = Null
    //     0xbb4928: mov             x1, NULL
    // 0xbb492c: r2 = 6
    //     0xbb492c: movz            x2, #0x6
    // 0xbb4930: r0 = AllocateArray()
    //     0xbb4930: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb4934: mov             x2, x0
    // 0xbb4938: ldur            x0, [fp, #-0x60]
    // 0xbb493c: stur            x2, [fp, #-0x10]
    // 0xbb4940: StoreField: r2->field_f = r0
    //     0xbb4940: stur            w0, [x2, #0xf]
    // 0xbb4944: r16 = Instance_SizedBox
    //     0xbb4944: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbb4948: ldr             x16, [x16, #0xa50]
    // 0xbb494c: StoreField: r2->field_13 = r16
    //     0xbb494c: stur            w16, [x2, #0x13]
    // 0xbb4950: ldur            x0, [fp, #-0x68]
    // 0xbb4954: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4954: stur            w0, [x2, #0x17]
    // 0xbb4958: r1 = <Widget>
    //     0xbb4958: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb495c: r0 = AllocateGrowableArray()
    //     0xbb495c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb4960: mov             x1, x0
    // 0xbb4964: ldur            x0, [fp, #-0x10]
    // 0xbb4968: stur            x1, [fp, #-0x58]
    // 0xbb496c: StoreField: r1->field_f = r0
    //     0xbb496c: stur            w0, [x1, #0xf]
    // 0xbb4970: r2 = 6
    //     0xbb4970: movz            x2, #0x6
    // 0xbb4974: StoreField: r1->field_b = r2
    //     0xbb4974: stur            w2, [x1, #0xb]
    // 0xbb4978: r0 = Row()
    //     0xbb4978: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb497c: mov             x3, x0
    // 0xbb4980: r0 = Instance_Axis
    //     0xbb4980: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb4984: stur            x3, [fp, #-0x10]
    // 0xbb4988: StoreField: r3->field_f = r0
    //     0xbb4988: stur            w0, [x3, #0xf]
    // 0xbb498c: r4 = Instance_MainAxisAlignment
    //     0xbb498c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb4990: ldr             x4, [x4, #0xa08]
    // 0xbb4994: StoreField: r3->field_13 = r4
    //     0xbb4994: stur            w4, [x3, #0x13]
    // 0xbb4998: r5 = Instance_MainAxisSize
    //     0xbb4998: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb499c: ldr             x5, [x5, #0xa10]
    // 0xbb49a0: ArrayStore: r3[0] = r5  ; List_4
    //     0xbb49a0: stur            w5, [x3, #0x17]
    // 0xbb49a4: r6 = Instance_CrossAxisAlignment
    //     0xbb49a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb49a8: ldr             x6, [x6, #0xa18]
    // 0xbb49ac: StoreField: r3->field_1b = r6
    //     0xbb49ac: stur            w6, [x3, #0x1b]
    // 0xbb49b0: r7 = Instance_VerticalDirection
    //     0xbb49b0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb49b4: ldr             x7, [x7, #0xa20]
    // 0xbb49b8: StoreField: r3->field_23 = r7
    //     0xbb49b8: stur            w7, [x3, #0x23]
    // 0xbb49bc: r8 = Instance_Clip
    //     0xbb49bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb49c0: ldr             x8, [x8, #0x38]
    // 0xbb49c4: StoreField: r3->field_2b = r8
    //     0xbb49c4: stur            w8, [x3, #0x2b]
    // 0xbb49c8: StoreField: r3->field_2f = rZR
    //     0xbb49c8: stur            xzr, [x3, #0x2f]
    // 0xbb49cc: ldur            x1, [fp, #-0x58]
    // 0xbb49d0: StoreField: r3->field_b = r1
    //     0xbb49d0: stur            w1, [x3, #0xb]
    // 0xbb49d4: r1 = Null
    //     0xbb49d4: mov             x1, NULL
    // 0xbb49d8: r2 = 6
    //     0xbb49d8: movz            x2, #0x6
    // 0xbb49dc: r0 = AllocateArray()
    //     0xbb49dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb49e0: mov             x2, x0
    // 0xbb49e4: ldur            x0, [fp, #-0x38]
    // 0xbb49e8: stur            x2, [fp, #-0x58]
    // 0xbb49ec: StoreField: r2->field_f = r0
    //     0xbb49ec: stur            w0, [x2, #0xf]
    // 0xbb49f0: r16 = Instance_SizedBox
    //     0xbb49f0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbb49f4: ldr             x16, [x16, #0xc70]
    // 0xbb49f8: StoreField: r2->field_13 = r16
    //     0xbb49f8: stur            w16, [x2, #0x13]
    // 0xbb49fc: ldur            x0, [fp, #-0x10]
    // 0xbb4a00: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4a00: stur            w0, [x2, #0x17]
    // 0xbb4a04: r1 = <Widget>
    //     0xbb4a04: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb4a08: r0 = AllocateGrowableArray()
    //     0xbb4a08: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb4a0c: mov             x1, x0
    // 0xbb4a10: ldur            x0, [fp, #-0x58]
    // 0xbb4a14: stur            x1, [fp, #-0x10]
    // 0xbb4a18: StoreField: r1->field_f = r0
    //     0xbb4a18: stur            w0, [x1, #0xf]
    // 0xbb4a1c: r2 = 6
    //     0xbb4a1c: movz            x2, #0x6
    // 0xbb4a20: StoreField: r1->field_b = r2
    //     0xbb4a20: stur            w2, [x1, #0xb]
    // 0xbb4a24: r0 = Column()
    //     0xbb4a24: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb4a28: mov             x1, x0
    // 0xbb4a2c: r0 = Instance_Axis
    //     0xbb4a2c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb4a30: stur            x1, [fp, #-0x38]
    // 0xbb4a34: StoreField: r1->field_f = r0
    //     0xbb4a34: stur            w0, [x1, #0xf]
    // 0xbb4a38: r2 = Instance_MainAxisAlignment
    //     0xbb4a38: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb4a3c: ldr             x2, [x2, #0xa08]
    // 0xbb4a40: StoreField: r1->field_13 = r2
    //     0xbb4a40: stur            w2, [x1, #0x13]
    // 0xbb4a44: r3 = Instance_MainAxisSize
    //     0xbb4a44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb4a48: ldr             x3, [x3, #0xa10]
    // 0xbb4a4c: ArrayStore: r1[0] = r3  ; List_4
    //     0xbb4a4c: stur            w3, [x1, #0x17]
    // 0xbb4a50: r4 = Instance_CrossAxisAlignment
    //     0xbb4a50: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb4a54: ldr             x4, [x4, #0x890]
    // 0xbb4a58: StoreField: r1->field_1b = r4
    //     0xbb4a58: stur            w4, [x1, #0x1b]
    // 0xbb4a5c: r4 = Instance_VerticalDirection
    //     0xbb4a5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb4a60: ldr             x4, [x4, #0xa20]
    // 0xbb4a64: StoreField: r1->field_23 = r4
    //     0xbb4a64: stur            w4, [x1, #0x23]
    // 0xbb4a68: r5 = Instance_Clip
    //     0xbb4a68: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb4a6c: ldr             x5, [x5, #0x38]
    // 0xbb4a70: StoreField: r1->field_2b = r5
    //     0xbb4a70: stur            w5, [x1, #0x2b]
    // 0xbb4a74: StoreField: r1->field_2f = rZR
    //     0xbb4a74: stur            xzr, [x1, #0x2f]
    // 0xbb4a78: ldur            x6, [fp, #-0x10]
    // 0xbb4a7c: StoreField: r1->field_b = r6
    //     0xbb4a7c: stur            w6, [x1, #0xb]
    // 0xbb4a80: r0 = Padding()
    //     0xbb4a80: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb4a84: mov             x2, x0
    // 0xbb4a88: r0 = Instance_EdgeInsets
    //     0xbb4a88: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbb4a8c: ldr             x0, [x0, #0xa78]
    // 0xbb4a90: stur            x2, [fp, #-0x10]
    // 0xbb4a94: StoreField: r2->field_f = r0
    //     0xbb4a94: stur            w0, [x2, #0xf]
    // 0xbb4a98: ldur            x0, [fp, #-0x38]
    // 0xbb4a9c: StoreField: r2->field_b = r0
    //     0xbb4a9c: stur            w0, [x2, #0xb]
    // 0xbb4aa0: r1 = <FlexParentData>
    //     0xbb4aa0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb4aa4: ldr             x1, [x1, #0xe00]
    // 0xbb4aa8: r0 = Expanded()
    //     0xbb4aa8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbb4aac: mov             x3, x0
    // 0xbb4ab0: r0 = 1
    //     0xbb4ab0: movz            x0, #0x1
    // 0xbb4ab4: stur            x3, [fp, #-0x38]
    // 0xbb4ab8: StoreField: r3->field_13 = r0
    //     0xbb4ab8: stur            x0, [x3, #0x13]
    // 0xbb4abc: r0 = Instance_FlexFit
    //     0xbb4abc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb4ac0: ldr             x0, [x0, #0xe08]
    // 0xbb4ac4: StoreField: r3->field_1b = r0
    //     0xbb4ac4: stur            w0, [x3, #0x1b]
    // 0xbb4ac8: ldur            x0, [fp, #-0x10]
    // 0xbb4acc: StoreField: r3->field_b = r0
    //     0xbb4acc: stur            w0, [x3, #0xb]
    // 0xbb4ad0: r1 = Null
    //     0xbb4ad0: mov             x1, NULL
    // 0xbb4ad4: r2 = 6
    //     0xbb4ad4: movz            x2, #0x6
    // 0xbb4ad8: r0 = AllocateArray()
    //     0xbb4ad8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb4adc: mov             x2, x0
    // 0xbb4ae0: ldur            x0, [fp, #-0x48]
    // 0xbb4ae4: stur            x2, [fp, #-0x10]
    // 0xbb4ae8: StoreField: r2->field_f = r0
    //     0xbb4ae8: stur            w0, [x2, #0xf]
    // 0xbb4aec: ldur            x0, [fp, #-0x50]
    // 0xbb4af0: StoreField: r2->field_13 = r0
    //     0xbb4af0: stur            w0, [x2, #0x13]
    // 0xbb4af4: ldur            x0, [fp, #-0x38]
    // 0xbb4af8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4af8: stur            w0, [x2, #0x17]
    // 0xbb4afc: r1 = <Widget>
    //     0xbb4afc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb4b00: r0 = AllocateGrowableArray()
    //     0xbb4b00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb4b04: mov             x1, x0
    // 0xbb4b08: ldur            x0, [fp, #-0x10]
    // 0xbb4b0c: stur            x1, [fp, #-0x38]
    // 0xbb4b10: StoreField: r1->field_f = r0
    //     0xbb4b10: stur            w0, [x1, #0xf]
    // 0xbb4b14: r0 = 6
    //     0xbb4b14: movz            x0, #0x6
    // 0xbb4b18: StoreField: r1->field_b = r0
    //     0xbb4b18: stur            w0, [x1, #0xb]
    // 0xbb4b1c: r0 = Row()
    //     0xbb4b1c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb4b20: mov             x1, x0
    // 0xbb4b24: r0 = Instance_Axis
    //     0xbb4b24: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb4b28: stur            x1, [fp, #-0x10]
    // 0xbb4b2c: StoreField: r1->field_f = r0
    //     0xbb4b2c: stur            w0, [x1, #0xf]
    // 0xbb4b30: r0 = Instance_MainAxisAlignment
    //     0xbb4b30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb4b34: ldr             x0, [x0, #0xa08]
    // 0xbb4b38: StoreField: r1->field_13 = r0
    //     0xbb4b38: stur            w0, [x1, #0x13]
    // 0xbb4b3c: r2 = Instance_MainAxisSize
    //     0xbb4b3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb4b40: ldr             x2, [x2, #0xa10]
    // 0xbb4b44: ArrayStore: r1[0] = r2  ; List_4
    //     0xbb4b44: stur            w2, [x1, #0x17]
    // 0xbb4b48: r3 = Instance_CrossAxisAlignment
    //     0xbb4b48: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb4b4c: ldr             x3, [x3, #0xa18]
    // 0xbb4b50: StoreField: r1->field_1b = r3
    //     0xbb4b50: stur            w3, [x1, #0x1b]
    // 0xbb4b54: r4 = Instance_VerticalDirection
    //     0xbb4b54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb4b58: ldr             x4, [x4, #0xa20]
    // 0xbb4b5c: StoreField: r1->field_23 = r4
    //     0xbb4b5c: stur            w4, [x1, #0x23]
    // 0xbb4b60: r5 = Instance_Clip
    //     0xbb4b60: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb4b64: ldr             x5, [x5, #0x38]
    // 0xbb4b68: StoreField: r1->field_2b = r5
    //     0xbb4b68: stur            w5, [x1, #0x2b]
    // 0xbb4b6c: StoreField: r1->field_2f = rZR
    //     0xbb4b6c: stur            xzr, [x1, #0x2f]
    // 0xbb4b70: ldur            x6, [fp, #-0x38]
    // 0xbb4b74: StoreField: r1->field_b = r6
    //     0xbb4b74: stur            w6, [x1, #0xb]
    // 0xbb4b78: r0 = Container()
    //     0xbb4b78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb4b7c: stur            x0, [fp, #-0x38]
    // 0xbb4b80: ldur            x16, [fp, #-0x40]
    // 0xbb4b84: ldur            lr, [fp, #-0x10]
    // 0xbb4b88: stp             lr, x16, [SP]
    // 0xbb4b8c: mov             x1, x0
    // 0xbb4b90: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbb4b90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbb4b94: ldr             x4, [x4, #0x88]
    // 0xbb4b98: r0 = Container()
    //     0xbb4b98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb4b9c: r0 = Padding()
    //     0xbb4b9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb4ba0: mov             x1, x0
    // 0xbb4ba4: r0 = Instance_EdgeInsets
    //     0xbb4ba4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbb4ba8: ldr             x0, [x0, #0x778]
    // 0xbb4bac: stur            x1, [fp, #-0x10]
    // 0xbb4bb0: StoreField: r1->field_f = r0
    //     0xbb4bb0: stur            w0, [x1, #0xf]
    // 0xbb4bb4: ldur            x0, [fp, #-0x38]
    // 0xbb4bb8: StoreField: r1->field_b = r0
    //     0xbb4bb8: stur            w0, [x1, #0xb]
    // 0xbb4bbc: r0 = Visibility()
    //     0xbb4bbc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbb4bc0: mov             x2, x0
    // 0xbb4bc4: ldur            x0, [fp, #-0x10]
    // 0xbb4bc8: stur            x2, [fp, #-0x38]
    // 0xbb4bcc: StoreField: r2->field_b = r0
    //     0xbb4bcc: stur            w0, [x2, #0xb]
    // 0xbb4bd0: r0 = Instance_SizedBox
    //     0xbb4bd0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbb4bd4: StoreField: r2->field_f = r0
    //     0xbb4bd4: stur            w0, [x2, #0xf]
    // 0xbb4bd8: ldur            x0, [fp, #-0x20]
    // 0xbb4bdc: StoreField: r2->field_13 = r0
    //     0xbb4bdc: stur            w0, [x2, #0x13]
    // 0xbb4be0: r0 = false
    //     0xbb4be0: add             x0, NULL, #0x30  ; false
    // 0xbb4be4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb4be4: stur            w0, [x2, #0x17]
    // 0xbb4be8: StoreField: r2->field_1b = r0
    //     0xbb4be8: stur            w0, [x2, #0x1b]
    // 0xbb4bec: StoreField: r2->field_1f = r0
    //     0xbb4bec: stur            w0, [x2, #0x1f]
    // 0xbb4bf0: StoreField: r2->field_23 = r0
    //     0xbb4bf0: stur            w0, [x2, #0x23]
    // 0xbb4bf4: StoreField: r2->field_27 = r0
    //     0xbb4bf4: stur            w0, [x2, #0x27]
    // 0xbb4bf8: StoreField: r2->field_2b = r0
    //     0xbb4bf8: stur            w0, [x2, #0x2b]
    // 0xbb4bfc: ldur            x3, [fp, #-0x28]
    // 0xbb4c00: LoadField: r1 = r3->field_b
    //     0xbb4c00: ldur            w1, [x3, #0xb]
    // 0xbb4c04: LoadField: r4 = r3->field_f
    //     0xbb4c04: ldur            w4, [x3, #0xf]
    // 0xbb4c08: DecompressPointer r4
    //     0xbb4c08: add             x4, x4, HEAP, lsl #32
    // 0xbb4c0c: LoadField: r5 = r4->field_b
    //     0xbb4c0c: ldur            w5, [x4, #0xb]
    // 0xbb4c10: r4 = LoadInt32Instr(r1)
    //     0xbb4c10: sbfx            x4, x1, #1, #0x1f
    // 0xbb4c14: stur            x4, [fp, #-0x78]
    // 0xbb4c18: r1 = LoadInt32Instr(r5)
    //     0xbb4c18: sbfx            x1, x5, #1, #0x1f
    // 0xbb4c1c: cmp             x4, x1
    // 0xbb4c20: b.ne            #0xbb4c2c
    // 0xbb4c24: mov             x1, x3
    // 0xbb4c28: r0 = _growToNextCapacity()
    //     0xbb4c28: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbb4c2c: ldur            x3, [fp, #-0x28]
    // 0xbb4c30: ldur            x2, [fp, #-0x78]
    // 0xbb4c34: add             x0, x2, #1
    // 0xbb4c38: lsl             x1, x0, #1
    // 0xbb4c3c: StoreField: r3->field_b = r1
    //     0xbb4c3c: stur            w1, [x3, #0xb]
    // 0xbb4c40: LoadField: r1 = r3->field_f
    //     0xbb4c40: ldur            w1, [x3, #0xf]
    // 0xbb4c44: DecompressPointer r1
    //     0xbb4c44: add             x1, x1, HEAP, lsl #32
    // 0xbb4c48: ldur            x0, [fp, #-0x38]
    // 0xbb4c4c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbb4c4c: add             x25, x1, x2, lsl #2
    //     0xbb4c50: add             x25, x25, #0xf
    //     0xbb4c54: str             w0, [x25]
    //     0xbb4c58: tbz             w0, #0, #0xbb4c74
    //     0xbb4c5c: ldurb           w16, [x1, #-1]
    //     0xbb4c60: ldurb           w17, [x0, #-1]
    //     0xbb4c64: and             x16, x17, x16, lsr #2
    //     0xbb4c68: tst             x16, HEAP, lsr #32
    //     0xbb4c6c: b.eq            #0xbb4c74
    //     0xbb4c70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb4c74: b               #0xbb4c7c
    // 0xbb4c78: mov             x3, x2
    // 0xbb4c7c: ldur            x0, [fp, #-8]
    // 0xbb4c80: LoadField: r1 = r0->field_b
    //     0xbb4c80: ldur            w1, [x0, #0xb]
    // 0xbb4c84: DecompressPointer r1
    //     0xbb4c84: add             x1, x1, HEAP, lsl #32
    // 0xbb4c88: cmp             w1, NULL
    // 0xbb4c8c: b.eq            #0xbb4ee8
    // 0xbb4c90: LoadField: r0 = r1->field_b
    //     0xbb4c90: ldur            w0, [x1, #0xb]
    // 0xbb4c94: DecompressPointer r0
    //     0xbb4c94: add             x0, x0, HEAP, lsl #32
    // 0xbb4c98: LoadField: r1 = r0->field_b
    //     0xbb4c98: ldur            w1, [x0, #0xb]
    // 0xbb4c9c: DecompressPointer r1
    //     0xbb4c9c: add             x1, x1, HEAP, lsl #32
    // 0xbb4ca0: cmp             w1, NULL
    // 0xbb4ca4: b.ne            #0xbb4cb0
    // 0xbb4ca8: r0 = Null
    //     0xbb4ca8: mov             x0, NULL
    // 0xbb4cac: b               #0xbb4cd4
    // 0xbb4cb0: LoadField: r0 = r1->field_f
    //     0xbb4cb0: ldur            w0, [x1, #0xf]
    // 0xbb4cb4: DecompressPointer r0
    //     0xbb4cb4: add             x0, x0, HEAP, lsl #32
    // 0xbb4cb8: cmp             w0, NULL
    // 0xbb4cbc: b.ne            #0xbb4cc8
    // 0xbb4cc0: r0 = Null
    //     0xbb4cc0: mov             x0, NULL
    // 0xbb4cc4: b               #0xbb4cd4
    // 0xbb4cc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb4cc8: ldur            w1, [x0, #0x17]
    // 0xbb4ccc: DecompressPointer r1
    //     0xbb4ccc: add             x1, x1, HEAP, lsl #32
    // 0xbb4cd0: mov             x0, x1
    // 0xbb4cd4: cmp             w0, NULL
    // 0xbb4cd8: b.ne            #0xbb4cec
    // 0xbb4cdc: r1 = <BEntities>
    //     0xbb4cdc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0xbb4ce0: ldr             x1, [x1, #0x130]
    // 0xbb4ce4: r2 = 0
    //     0xbb4ce4: movz            x2, #0
    // 0xbb4ce8: r0 = AllocateArray()
    //     0xbb4ce8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb4cec: ldur            x1, [fp, #-0x28]
    // 0xbb4cf0: r2 = LoadClassIdInstr(r0)
    //     0xbb4cf0: ldur            x2, [x0, #-1]
    //     0xbb4cf4: ubfx            x2, x2, #0xc, #0x14
    // 0xbb4cf8: str             x0, [SP]
    // 0xbb4cfc: mov             x0, x2
    // 0xbb4d00: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbb4d00: movz            x17, #0xc898
    //     0xbb4d04: add             lr, x0, x17
    //     0xbb4d08: ldr             lr, [x21, lr, lsl #3]
    //     0xbb4d0c: blr             lr
    // 0xbb4d10: ldur            x2, [fp, #-0x18]
    // 0xbb4d14: r1 = Function '<anonymous closure>':.
    //     0xbb4d14: add             x1, PP, #0x54, lsl #12  ; [pp+0x54018] AnonymousClosure: (0xbb4f84), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::build (0xbb3b8c)
    //     0xbb4d18: ldr             x1, [x1, #0x18]
    // 0xbb4d1c: stur            x0, [fp, #-8]
    // 0xbb4d20: r0 = AllocateClosure()
    //     0xbb4d20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb4d24: stur            x0, [fp, #-0x10]
    // 0xbb4d28: r0 = ListView()
    //     0xbb4d28: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbb4d2c: stur            x0, [fp, #-0x20]
    // 0xbb4d30: r16 = true
    //     0xbb4d30: add             x16, NULL, #0x20  ; true
    // 0xbb4d34: r30 = false
    //     0xbb4d34: add             lr, NULL, #0x30  ; false
    // 0xbb4d38: stp             lr, x16, [SP, #8]
    // 0xbb4d3c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbb4d3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbb4d40: ldr             x16, [x16, #0x1c8]
    // 0xbb4d44: str             x16, [SP]
    // 0xbb4d48: mov             x1, x0
    // 0xbb4d4c: ldur            x2, [fp, #-0x10]
    // 0xbb4d50: ldur            x3, [fp, #-8]
    // 0xbb4d54: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0xbb4d54: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fd8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbb4d58: ldr             x4, [x4, #0xfd8]
    // 0xbb4d5c: r0 = ListView.builder()
    //     0xbb4d5c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbb4d60: ldur            x0, [fp, #-0x28]
    // 0xbb4d64: LoadField: r1 = r0->field_b
    //     0xbb4d64: ldur            w1, [x0, #0xb]
    // 0xbb4d68: LoadField: r2 = r0->field_f
    //     0xbb4d68: ldur            w2, [x0, #0xf]
    // 0xbb4d6c: DecompressPointer r2
    //     0xbb4d6c: add             x2, x2, HEAP, lsl #32
    // 0xbb4d70: LoadField: r3 = r2->field_b
    //     0xbb4d70: ldur            w3, [x2, #0xb]
    // 0xbb4d74: r2 = LoadInt32Instr(r1)
    //     0xbb4d74: sbfx            x2, x1, #1, #0x1f
    // 0xbb4d78: stur            x2, [fp, #-0x78]
    // 0xbb4d7c: r1 = LoadInt32Instr(r3)
    //     0xbb4d7c: sbfx            x1, x3, #1, #0x1f
    // 0xbb4d80: cmp             x2, x1
    // 0xbb4d84: b.ne            #0xbb4d90
    // 0xbb4d88: mov             x1, x0
    // 0xbb4d8c: r0 = _growToNextCapacity()
    //     0xbb4d8c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbb4d90: ldur            x4, [fp, #-0x30]
    // 0xbb4d94: ldur            x2, [fp, #-0x28]
    // 0xbb4d98: ldur            x3, [fp, #-0x78]
    // 0xbb4d9c: add             x0, x3, #1
    // 0xbb4da0: lsl             x1, x0, #1
    // 0xbb4da4: StoreField: r2->field_b = r1
    //     0xbb4da4: stur            w1, [x2, #0xb]
    // 0xbb4da8: LoadField: r1 = r2->field_f
    //     0xbb4da8: ldur            w1, [x2, #0xf]
    // 0xbb4dac: DecompressPointer r1
    //     0xbb4dac: add             x1, x1, HEAP, lsl #32
    // 0xbb4db0: ldur            x0, [fp, #-0x20]
    // 0xbb4db4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbb4db4: add             x25, x1, x3, lsl #2
    //     0xbb4db8: add             x25, x25, #0xf
    //     0xbb4dbc: str             w0, [x25]
    //     0xbb4dc0: tbz             w0, #0, #0xbb4ddc
    //     0xbb4dc4: ldurb           w16, [x1, #-1]
    //     0xbb4dc8: ldurb           w17, [x0, #-1]
    //     0xbb4dcc: and             x16, x17, x16, lsr #2
    //     0xbb4dd0: tst             x16, HEAP, lsr #32
    //     0xbb4dd4: b.eq            #0xbb4ddc
    //     0xbb4dd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb4ddc: r0 = Column()
    //     0xbb4ddc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb4de0: mov             x1, x0
    // 0xbb4de4: r0 = Instance_Axis
    //     0xbb4de4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb4de8: stur            x1, [fp, #-8]
    // 0xbb4dec: StoreField: r1->field_f = r0
    //     0xbb4dec: stur            w0, [x1, #0xf]
    // 0xbb4df0: r0 = Instance_MainAxisAlignment
    //     0xbb4df0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb4df4: ldr             x0, [x0, #0xa08]
    // 0xbb4df8: StoreField: r1->field_13 = r0
    //     0xbb4df8: stur            w0, [x1, #0x13]
    // 0xbb4dfc: r0 = Instance_MainAxisSize
    //     0xbb4dfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb4e00: ldr             x0, [x0, #0xa10]
    // 0xbb4e04: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb4e04: stur            w0, [x1, #0x17]
    // 0xbb4e08: r0 = Instance_CrossAxisAlignment
    //     0xbb4e08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb4e0c: ldr             x0, [x0, #0xa18]
    // 0xbb4e10: StoreField: r1->field_1b = r0
    //     0xbb4e10: stur            w0, [x1, #0x1b]
    // 0xbb4e14: r0 = Instance_VerticalDirection
    //     0xbb4e14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb4e18: ldr             x0, [x0, #0xa20]
    // 0xbb4e1c: StoreField: r1->field_23 = r0
    //     0xbb4e1c: stur            w0, [x1, #0x23]
    // 0xbb4e20: r0 = Instance_Clip
    //     0xbb4e20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb4e24: ldr             x0, [x0, #0x38]
    // 0xbb4e28: StoreField: r1->field_2b = r0
    //     0xbb4e28: stur            w0, [x1, #0x2b]
    // 0xbb4e2c: StoreField: r1->field_2f = rZR
    //     0xbb4e2c: stur            xzr, [x1, #0x2f]
    // 0xbb4e30: ldur            x0, [fp, #-0x28]
    // 0xbb4e34: StoreField: r1->field_b = r0
    //     0xbb4e34: stur            w0, [x1, #0xb]
    // 0xbb4e38: r0 = Container()
    //     0xbb4e38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbb4e3c: mov             x1, x0
    // 0xbb4e40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbb4e40: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbb4e44: r0 = Container()
    //     0xbb4e44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbb4e48: r0 = Accordion()
    //     0xbb4e48: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xbb4e4c: mov             x3, x0
    // 0xbb4e50: ldur            x0, [fp, #-0x30]
    // 0xbb4e54: stur            x3, [fp, #-0x10]
    // 0xbb4e58: StoreField: r3->field_b = r0
    //     0xbb4e58: stur            w0, [x3, #0xb]
    // 0xbb4e5c: ldur            x0, [fp, #-8]
    // 0xbb4e60: StoreField: r3->field_13 = r0
    //     0xbb4e60: stur            w0, [x3, #0x13]
    // 0xbb4e64: r0 = false
    //     0xbb4e64: add             x0, NULL, #0x30  ; false
    // 0xbb4e68: ArrayStore: r3[0] = r0  ; List_4
    //     0xbb4e68: stur            w0, [x3, #0x17]
    // 0xbb4e6c: d0 = 25.000000
    //     0xbb4e6c: fmov            d0, #25.00000000
    // 0xbb4e70: StoreField: r3->field_1b = d0
    //     0xbb4e70: stur            d0, [x3, #0x1b]
    // 0xbb4e74: r0 = true
    //     0xbb4e74: add             x0, NULL, #0x20  ; true
    // 0xbb4e78: StoreField: r3->field_23 = r0
    //     0xbb4e78: stur            w0, [x3, #0x23]
    // 0xbb4e7c: ldur            x2, [fp, #-0x18]
    // 0xbb4e80: r1 = Function '<anonymous closure>':.
    //     0xbb4e80: add             x1, PP, #0x54, lsl #12  ; [pp+0x54020] AnonymousClosure: (0xbb4f0c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::build (0xbb3b8c)
    //     0xbb4e84: ldr             x1, [x1, #0x20]
    // 0xbb4e88: r0 = AllocateClosure()
    //     0xbb4e88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb4e8c: mov             x1, x0
    // 0xbb4e90: ldur            x0, [fp, #-0x10]
    // 0xbb4e94: StoreField: r0->field_3b = r1
    //     0xbb4e94: stur            w1, [x0, #0x3b]
    // 0xbb4e98: r0 = Padding()
    //     0xbb4e98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb4e9c: r1 = Instance_EdgeInsets
    //     0xbb4e9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbb4ea0: ldr             x1, [x1, #0x668]
    // 0xbb4ea4: StoreField: r0->field_f = r1
    //     0xbb4ea4: stur            w1, [x0, #0xf]
    // 0xbb4ea8: ldur            x1, [fp, #-0x10]
    // 0xbb4eac: StoreField: r0->field_b = r1
    //     0xbb4eac: stur            w1, [x0, #0xb]
    // 0xbb4eb0: LeaveFrame
    //     0xbb4eb0: mov             SP, fp
    //     0xbb4eb4: ldp             fp, lr, [SP], #0x10
    // 0xbb4eb8: ret
    //     0xbb4eb8: ret             
    // 0xbb4ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb4ebc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb4ec0: b               #0xbb3bb4
    // 0xbb4ec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ec4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ec8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ecc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ecc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ed0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ed0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ed4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ed4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ed8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ed8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4edc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4edc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ee0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ee4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb4ee8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4ee8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbb4f0c, size: 0x78
    // 0xbb4f0c: EnterFrame
    //     0xbb4f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbb4f10: mov             fp, SP
    // 0xbb4f14: AllocStack(0x8)
    //     0xbb4f14: sub             SP, SP, #8
    // 0xbb4f18: SetupParameters()
    //     0xbb4f18: ldr             x0, [fp, #0x10]
    //     0xbb4f1c: ldur            w1, [x0, #0x17]
    //     0xbb4f20: add             x1, x1, HEAP, lsl #32
    // 0xbb4f24: CheckStackOverflow
    //     0xbb4f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb4f28: cmp             SP, x16
    //     0xbb4f2c: b.ls            #0xbb4f78
    // 0xbb4f30: LoadField: r0 = r1->field_f
    //     0xbb4f30: ldur            w0, [x1, #0xf]
    // 0xbb4f34: DecompressPointer r0
    //     0xbb4f34: add             x0, x0, HEAP, lsl #32
    // 0xbb4f38: LoadField: r1 = r0->field_b
    //     0xbb4f38: ldur            w1, [x0, #0xb]
    // 0xbb4f3c: DecompressPointer r1
    //     0xbb4f3c: add             x1, x1, HEAP, lsl #32
    // 0xbb4f40: cmp             w1, NULL
    // 0xbb4f44: b.eq            #0xbb4f80
    // 0xbb4f48: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbb4f48: ldur            w0, [x1, #0x17]
    // 0xbb4f4c: DecompressPointer r0
    //     0xbb4f4c: add             x0, x0, HEAP, lsl #32
    // 0xbb4f50: str             x0, [SP]
    // 0xbb4f54: r4 = 0
    //     0xbb4f54: movz            x4, #0
    // 0xbb4f58: ldr             x0, [SP]
    // 0xbb4f5c: r5 = UnlinkedCall_0x613b5c
    //     0xbb4f5c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54028] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbb4f60: ldp             x5, lr, [x16, #0x28]
    // 0xbb4f64: blr             lr
    // 0xbb4f68: r0 = Null
    //     0xbb4f68: mov             x0, NULL
    // 0xbb4f6c: LeaveFrame
    //     0xbb4f6c: mov             SP, fp
    //     0xbb4f70: ldp             fp, lr, [SP], #0x10
    // 0xbb4f74: ret
    //     0xbb4f74: ret             
    // 0xbb4f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb4f78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb4f7c: b               #0xbb4f30
    // 0xbb4f80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb4f80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbb4f84, size: 0x10c
    // 0xbb4f84: EnterFrame
    //     0xbb4f84: stp             fp, lr, [SP, #-0x10]!
    //     0xbb4f88: mov             fp, SP
    // 0xbb4f8c: AllocStack(0x8)
    //     0xbb4f8c: sub             SP, SP, #8
    // 0xbb4f90: SetupParameters()
    //     0xbb4f90: ldr             x0, [fp, #0x20]
    //     0xbb4f94: ldur            w1, [x0, #0x17]
    //     0xbb4f98: add             x1, x1, HEAP, lsl #32
    // 0xbb4f9c: CheckStackOverflow
    //     0xbb4f9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb4fa0: cmp             SP, x16
    //     0xbb4fa4: b.ls            #0xbb5080
    // 0xbb4fa8: LoadField: r2 = r1->field_f
    //     0xbb4fa8: ldur            w2, [x1, #0xf]
    // 0xbb4fac: DecompressPointer r2
    //     0xbb4fac: add             x2, x2, HEAP, lsl #32
    // 0xbb4fb0: stur            x2, [fp, #-8]
    // 0xbb4fb4: LoadField: r0 = r2->field_b
    //     0xbb4fb4: ldur            w0, [x2, #0xb]
    // 0xbb4fb8: DecompressPointer r0
    //     0xbb4fb8: add             x0, x0, HEAP, lsl #32
    // 0xbb4fbc: cmp             w0, NULL
    // 0xbb4fc0: b.eq            #0xbb5088
    // 0xbb4fc4: LoadField: r1 = r0->field_b
    //     0xbb4fc4: ldur            w1, [x0, #0xb]
    // 0xbb4fc8: DecompressPointer r1
    //     0xbb4fc8: add             x1, x1, HEAP, lsl #32
    // 0xbb4fcc: LoadField: r0 = r1->field_b
    //     0xbb4fcc: ldur            w0, [x1, #0xb]
    // 0xbb4fd0: DecompressPointer r0
    //     0xbb4fd0: add             x0, x0, HEAP, lsl #32
    // 0xbb4fd4: cmp             w0, NULL
    // 0xbb4fd8: b.ne            #0xbb4fe4
    // 0xbb4fdc: r0 = Null
    //     0xbb4fdc: mov             x0, NULL
    // 0xbb4fe0: b               #0xbb5050
    // 0xbb4fe4: LoadField: r1 = r0->field_f
    //     0xbb4fe4: ldur            w1, [x0, #0xf]
    // 0xbb4fe8: DecompressPointer r1
    //     0xbb4fe8: add             x1, x1, HEAP, lsl #32
    // 0xbb4fec: cmp             w1, NULL
    // 0xbb4ff0: b.ne            #0xbb4ffc
    // 0xbb4ff4: r0 = Null
    //     0xbb4ff4: mov             x0, NULL
    // 0xbb4ff8: b               #0xbb5050
    // 0xbb4ffc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbb4ffc: ldur            w3, [x1, #0x17]
    // 0xbb5000: DecompressPointer r3
    //     0xbb5000: add             x3, x3, HEAP, lsl #32
    // 0xbb5004: cmp             w3, NULL
    // 0xbb5008: b.ne            #0xbb5014
    // 0xbb500c: r0 = Null
    //     0xbb500c: mov             x0, NULL
    // 0xbb5010: b               #0xbb5050
    // 0xbb5014: ldr             x0, [fp, #0x10]
    // 0xbb5018: LoadField: r1 = r3->field_b
    //     0xbb5018: ldur            w1, [x3, #0xb]
    // 0xbb501c: r4 = LoadInt32Instr(r0)
    //     0xbb501c: sbfx            x4, x0, #1, #0x1f
    //     0xbb5020: tbz             w0, #0, #0xbb5028
    //     0xbb5024: ldur            x4, [x0, #7]
    // 0xbb5028: r0 = LoadInt32Instr(r1)
    //     0xbb5028: sbfx            x0, x1, #1, #0x1f
    // 0xbb502c: mov             x1, x4
    // 0xbb5030: cmp             x1, x0
    // 0xbb5034: b.hs            #0xbb508c
    // 0xbb5038: LoadField: r0 = r3->field_f
    //     0xbb5038: ldur            w0, [x3, #0xf]
    // 0xbb503c: DecompressPointer r0
    //     0xbb503c: add             x0, x0, HEAP, lsl #32
    // 0xbb5040: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbb5040: add             x16, x0, x4, lsl #2
    //     0xbb5044: ldur            w1, [x16, #0xf]
    // 0xbb5048: DecompressPointer r1
    //     0xbb5048: add             x1, x1, HEAP, lsl #32
    // 0xbb504c: mov             x0, x1
    // 0xbb5050: cmp             w0, NULL
    // 0xbb5054: b.ne            #0xbb5064
    // 0xbb5058: r0 = BEntities()
    //     0xbb5058: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xbb505c: mov             x2, x0
    // 0xbb5060: b               #0xbb5068
    // 0xbb5064: mov             x2, x0
    // 0xbb5068: ldur            x1, [fp, #-8]
    // 0xbb506c: ldr             x3, [fp, #0x18]
    // 0xbb5070: r0 = lineThemeBagItem()
    //     0xbb5070: bl              #0xa07044  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::lineThemeBagItem
    // 0xbb5074: LeaveFrame
    //     0xbb5074: mov             SP, fp
    //     0xbb5078: ldp             fp, lr, [SP], #0x10
    // 0xbb507c: ret
    //     0xbb507c: ret             
    // 0xbb5080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5080: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5084: b               #0xbb4fa8
    // 0xbb5088: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb5088: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb508c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbb508c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4022, size: 0x1c, field offset: 0xc
//   const constructor, 
class CheckoutBagAccordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc801e8, size: 0x24
    // 0xc801e8: EnterFrame
    //     0xc801e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc801ec: mov             fp, SP
    // 0xc801f0: mov             x0, x1
    // 0xc801f4: r1 = <CheckoutBagAccordion>
    //     0xc801f4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48660] TypeArguments: <CheckoutBagAccordion>
    //     0xc801f8: ldr             x1, [x1, #0x660]
    // 0xc801fc: r0 = _CheckoutBagAccordionState()
    //     0xc801fc: bl              #0xc8020c  ; Allocate_CheckoutBagAccordionStateStub -> _CheckoutBagAccordionState (size=0x14)
    // 0xc80200: LeaveFrame
    //     0xc80200: mov             SP, fp
    //     0xc80204: ldp             fp, lr, [SP], #0x10
    // 0xc80208: ret
    //     0xc80208: ret             
  }
}
