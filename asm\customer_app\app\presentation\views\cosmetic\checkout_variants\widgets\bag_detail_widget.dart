// lib: , url: package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart

// class id: 1049237, size: 0x8
class :: {
}

// class id: 3462, size: 0x14, field offset: 0x14
class _BagDetailWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93637c, size: 0x130
    // 0x93637c: EnterFrame
    //     0x93637c: stp             fp, lr, [SP, #-0x10]!
    //     0x936380: mov             fp, SP
    // 0x936384: AllocStack(0x18)
    //     0x936384: sub             SP, SP, #0x18
    // 0x936388: SetupParameters(_BagDetailWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x936388: stur            x1, [fp, #-8]
    // 0x93638c: CheckStackOverflow
    //     0x93638c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x936390: cmp             SP, x16
    //     0x936394: b.ls            #0x9364a0
    // 0x936398: r1 = 1
    //     0x936398: movz            x1, #0x1
    // 0x93639c: r0 = AllocateContext()
    //     0x93639c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9363a0: mov             x1, x0
    // 0x9363a4: ldur            x0, [fp, #-8]
    // 0x9363a8: StoreField: r1->field_f = r0
    //     0x9363a8: stur            w0, [x1, #0xf]
    // 0x9363ac: r0 = LoadStaticField(0x878)
    //     0x9363ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9363b0: ldr             x0, [x0, #0x10f0]
    // 0x9363b4: cmp             w0, NULL
    // 0x9363b8: b.eq            #0x9364a8
    // 0x9363bc: LoadField: r3 = r0->field_53
    //     0x9363bc: ldur            w3, [x0, #0x53]
    // 0x9363c0: DecompressPointer r3
    //     0x9363c0: add             x3, x3, HEAP, lsl #32
    // 0x9363c4: stur            x3, [fp, #-0x10]
    // 0x9363c8: LoadField: r0 = r3->field_7
    //     0x9363c8: ldur            w0, [x3, #7]
    // 0x9363cc: DecompressPointer r0
    //     0x9363cc: add             x0, x0, HEAP, lsl #32
    // 0x9363d0: mov             x2, x1
    // 0x9363d4: stur            x0, [fp, #-8]
    // 0x9363d8: r1 = Function '<anonymous closure>':.
    //     0x9363d8: add             x1, PP, #0x59, lsl #12  ; [pp+0x593a0] AnonymousClosure: (0x9364d0), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::initState (0x93637c)
    //     0x9363dc: ldr             x1, [x1, #0x3a0]
    // 0x9363e0: r0 = AllocateClosure()
    //     0x9363e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9363e4: ldur            x2, [fp, #-8]
    // 0x9363e8: mov             x3, x0
    // 0x9363ec: r1 = Null
    //     0x9363ec: mov             x1, NULL
    // 0x9363f0: stur            x3, [fp, #-8]
    // 0x9363f4: cmp             w2, NULL
    // 0x9363f8: b.eq            #0x936418
    // 0x9363fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9363fc: ldur            w4, [x2, #0x17]
    // 0x936400: DecompressPointer r4
    //     0x936400: add             x4, x4, HEAP, lsl #32
    // 0x936404: r8 = X0
    //     0x936404: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x936408: LoadField: r9 = r4->field_7
    //     0x936408: ldur            x9, [x4, #7]
    // 0x93640c: r3 = Null
    //     0x93640c: add             x3, PP, #0x59, lsl #12  ; [pp+0x593a8] Null
    //     0x936410: ldr             x3, [x3, #0x3a8]
    // 0x936414: blr             x9
    // 0x936418: ldur            x0, [fp, #-0x10]
    // 0x93641c: LoadField: r1 = r0->field_b
    //     0x93641c: ldur            w1, [x0, #0xb]
    // 0x936420: LoadField: r2 = r0->field_f
    //     0x936420: ldur            w2, [x0, #0xf]
    // 0x936424: DecompressPointer r2
    //     0x936424: add             x2, x2, HEAP, lsl #32
    // 0x936428: LoadField: r3 = r2->field_b
    //     0x936428: ldur            w3, [x2, #0xb]
    // 0x93642c: r2 = LoadInt32Instr(r1)
    //     0x93642c: sbfx            x2, x1, #1, #0x1f
    // 0x936430: stur            x2, [fp, #-0x18]
    // 0x936434: r1 = LoadInt32Instr(r3)
    //     0x936434: sbfx            x1, x3, #1, #0x1f
    // 0x936438: cmp             x2, x1
    // 0x93643c: b.ne            #0x936448
    // 0x936440: mov             x1, x0
    // 0x936444: r0 = _growToNextCapacity()
    //     0x936444: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x936448: ldur            x2, [fp, #-0x10]
    // 0x93644c: ldur            x3, [fp, #-0x18]
    // 0x936450: add             x4, x3, #1
    // 0x936454: lsl             x5, x4, #1
    // 0x936458: StoreField: r2->field_b = r5
    //     0x936458: stur            w5, [x2, #0xb]
    // 0x93645c: LoadField: r1 = r2->field_f
    //     0x93645c: ldur            w1, [x2, #0xf]
    // 0x936460: DecompressPointer r1
    //     0x936460: add             x1, x1, HEAP, lsl #32
    // 0x936464: ldur            x0, [fp, #-8]
    // 0x936468: ArrayStore: r1[r3] = r0  ; List_4
    //     0x936468: add             x25, x1, x3, lsl #2
    //     0x93646c: add             x25, x25, #0xf
    //     0x936470: str             w0, [x25]
    //     0x936474: tbz             w0, #0, #0x936490
    //     0x936478: ldurb           w16, [x1, #-1]
    //     0x93647c: ldurb           w17, [x0, #-1]
    //     0x936480: and             x16, x17, x16, lsr #2
    //     0x936484: tst             x16, HEAP, lsr #32
    //     0x936488: b.eq            #0x936490
    //     0x93648c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x936490: r0 = Null
    //     0x936490: mov             x0, NULL
    // 0x936494: LeaveFrame
    //     0x936494: mov             SP, fp
    //     0x936498: ldp             fp, lr, [SP], #0x10
    // 0x93649c: ret
    //     0x93649c: ret             
    // 0x9364a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9364a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9364a4: b               #0x936398
    // 0x9364a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9364a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9364d0, size: 0x70
    // 0x9364d0: EnterFrame
    //     0x9364d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9364d4: mov             fp, SP
    // 0x9364d8: AllocStack(0x10)
    //     0x9364d8: sub             SP, SP, #0x10
    // 0x9364dc: SetupParameters()
    //     0x9364dc: ldr             x0, [fp, #0x18]
    //     0x9364e0: ldur            w1, [x0, #0x17]
    //     0x9364e4: add             x1, x1, HEAP, lsl #32
    // 0x9364e8: CheckStackOverflow
    //     0x9364e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9364ec: cmp             SP, x16
    //     0x9364f0: b.ls            #0x936534
    // 0x9364f4: LoadField: r0 = r1->field_f
    //     0x9364f4: ldur            w0, [x1, #0xf]
    // 0x9364f8: DecompressPointer r0
    //     0x9364f8: add             x0, x0, HEAP, lsl #32
    // 0x9364fc: LoadField: r1 = r0->field_b
    //     0x9364fc: ldur            w1, [x0, #0xb]
    // 0x936500: DecompressPointer r1
    //     0x936500: add             x1, x1, HEAP, lsl #32
    // 0x936504: cmp             w1, NULL
    // 0x936508: b.eq            #0x93653c
    // 0x93650c: LoadField: r0 = r1->field_1b
    //     0x93650c: ldur            w0, [x1, #0x1b]
    // 0x936510: DecompressPointer r0
    //     0x936510: add             x0, x0, HEAP, lsl #32
    // 0x936514: r16 = true
    //     0x936514: add             x16, NULL, #0x20  ; true
    // 0x936518: stp             x16, x0, [SP]
    // 0x93651c: ClosureCall
    //     0x93651c: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x936520: ldur            x2, [x0, #0x1f]
    //     0x936524: blr             x2
    // 0x936528: LeaveFrame
    //     0x936528: mov             SP, fp
    //     0x93652c: ldp             fp, lr, [SP], #0x10
    // 0x936530: ret
    //     0x936530: ret             
    // 0x936534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x936534: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x936538: b               #0x9364f4
    // 0x93653c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93653c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xabd0f8, size: 0x1634
    // 0xabd0f8: EnterFrame
    //     0xabd0f8: stp             fp, lr, [SP, #-0x10]!
    //     0xabd0fc: mov             fp, SP
    // 0xabd100: AllocStack(0x98)
    //     0xabd100: sub             SP, SP, #0x98
    // 0xabd104: SetupParameters(_BagDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xabd104: mov             x0, x1
    //     0xabd108: stur            x1, [fp, #-8]
    //     0xabd10c: mov             x1, x2
    //     0xabd110: stur            x2, [fp, #-0x10]
    // 0xabd114: CheckStackOverflow
    //     0xabd114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabd118: cmp             SP, x16
    //     0xabd11c: b.ls            #0xabe704
    // 0xabd120: r1 = 1
    //     0xabd120: movz            x1, #0x1
    // 0xabd124: r0 = AllocateContext()
    //     0xabd124: bl              #0x16f6108  ; AllocateContextStub
    // 0xabd128: mov             x2, x0
    // 0xabd12c: ldur            x0, [fp, #-8]
    // 0xabd130: stur            x2, [fp, #-0x18]
    // 0xabd134: StoreField: r2->field_f = r0
    //     0xabd134: stur            w0, [x2, #0xf]
    // 0xabd138: ldur            x1, [fp, #-0x10]
    // 0xabd13c: r0 = of()
    //     0xabd13c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd140: LoadField: r1 = r0->field_87
    //     0xabd140: ldur            w1, [x0, #0x87]
    // 0xabd144: DecompressPointer r1
    //     0xabd144: add             x1, x1, HEAP, lsl #32
    // 0xabd148: LoadField: r0 = r1->field_7
    //     0xabd148: ldur            w0, [x1, #7]
    // 0xabd14c: DecompressPointer r0
    //     0xabd14c: add             x0, x0, HEAP, lsl #32
    // 0xabd150: stur            x0, [fp, #-0x20]
    // 0xabd154: r1 = Instance_Color
    //     0xabd154: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabd158: d0 = 0.700000
    //     0xabd158: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xabd15c: ldr             d0, [x17, #0xf48]
    // 0xabd160: r0 = withOpacity()
    //     0xabd160: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xabd164: r16 = 14.000000
    //     0xabd164: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xabd168: ldr             x16, [x16, #0x1d8]
    // 0xabd16c: stp             x0, x16, [SP]
    // 0xabd170: ldur            x1, [fp, #-0x20]
    // 0xabd174: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabd174: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabd178: ldr             x4, [x4, #0xaa0]
    // 0xabd17c: r0 = copyWith()
    //     0xabd17c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd180: stur            x0, [fp, #-0x20]
    // 0xabd184: r0 = Text()
    //     0xabd184: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd188: mov             x1, x0
    // 0xabd18c: r0 = "Bag"
    //     0xabd18c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0xabd190: ldr             x0, [x0, #0xd60]
    // 0xabd194: stur            x1, [fp, #-0x28]
    // 0xabd198: StoreField: r1->field_b = r0
    //     0xabd198: stur            w0, [x1, #0xb]
    // 0xabd19c: ldur            x0, [fp, #-0x20]
    // 0xabd1a0: StoreField: r1->field_13 = r0
    //     0xabd1a0: stur            w0, [x1, #0x13]
    // 0xabd1a4: r0 = Padding()
    //     0xabd1a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabd1a8: mov             x1, x0
    // 0xabd1ac: r0 = Instance_EdgeInsets
    //     0xabd1ac: add             x0, PP, #0x40, lsl #12  ; [pp+0x408a8] Obj!EdgeInsets@d57d71
    //     0xabd1b0: ldr             x0, [x0, #0x8a8]
    // 0xabd1b4: stur            x1, [fp, #-0x30]
    // 0xabd1b8: StoreField: r1->field_f = r0
    //     0xabd1b8: stur            w0, [x1, #0xf]
    // 0xabd1bc: ldur            x0, [fp, #-0x28]
    // 0xabd1c0: StoreField: r1->field_b = r0
    //     0xabd1c0: stur            w0, [x1, #0xb]
    // 0xabd1c4: ldur            x0, [fp, #-8]
    // 0xabd1c8: LoadField: r2 = r0->field_b
    //     0xabd1c8: ldur            w2, [x0, #0xb]
    // 0xabd1cc: DecompressPointer r2
    //     0xabd1cc: add             x2, x2, HEAP, lsl #32
    // 0xabd1d0: cmp             w2, NULL
    // 0xabd1d4: b.eq            #0xabe70c
    // 0xabd1d8: LoadField: r3 = r2->field_b
    //     0xabd1d8: ldur            w3, [x2, #0xb]
    // 0xabd1dc: DecompressPointer r3
    //     0xabd1dc: add             x3, x3, HEAP, lsl #32
    // 0xabd1e0: LoadField: r2 = r3->field_b
    //     0xabd1e0: ldur            w2, [x3, #0xb]
    // 0xabd1e4: DecompressPointer r2
    //     0xabd1e4: add             x2, x2, HEAP, lsl #32
    // 0xabd1e8: cmp             w2, NULL
    // 0xabd1ec: b.ne            #0xabd1f8
    // 0xabd1f0: r3 = Null
    //     0xabd1f0: mov             x3, NULL
    // 0xabd1f4: b               #0xabd200
    // 0xabd1f8: LoadField: r3 = r2->field_43
    //     0xabd1f8: ldur            w3, [x2, #0x43]
    // 0xabd1fc: DecompressPointer r3
    //     0xabd1fc: add             x3, x3, HEAP, lsl #32
    // 0xabd200: cmp             w3, NULL
    // 0xabd204: r16 = true
    //     0xabd204: add             x16, NULL, #0x20  ; true
    // 0xabd208: r17 = false
    //     0xabd208: add             x17, NULL, #0x30  ; false
    // 0xabd20c: csel            x4, x16, x17, ne
    // 0xabd210: stur            x4, [fp, #-0x20]
    // 0xabd214: cmp             w2, NULL
    // 0xabd218: b.ne            #0xabd224
    // 0xabd21c: r2 = Null
    //     0xabd21c: mov             x2, NULL
    // 0xabd220: b               #0xabd244
    // 0xabd224: LoadField: r3 = r2->field_43
    //     0xabd224: ldur            w3, [x2, #0x43]
    // 0xabd228: DecompressPointer r3
    //     0xabd228: add             x3, x3, HEAP, lsl #32
    // 0xabd22c: cmp             w3, NULL
    // 0xabd230: b.ne            #0xabd23c
    // 0xabd234: r2 = Null
    //     0xabd234: mov             x2, NULL
    // 0xabd238: b               #0xabd244
    // 0xabd23c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xabd23c: ldur            w2, [x3, #0x17]
    // 0xabd240: DecompressPointer r2
    //     0xabd240: add             x2, x2, HEAP, lsl #32
    // 0xabd244: cmp             w2, NULL
    // 0xabd248: b.ne            #0xabd2c8
    // 0xabd24c: mov             x23, x0
    // 0xabd250: r10 = true
    //     0xabd250: add             x10, NULL, #0x20  ; true
    // 0xabd254: r3 = 2
    //     0xabd254: movz            x3, #0x2
    // 0xabd258: r5 = "Free"
    //     0xabd258: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabd25c: ldr             x5, [x5, #0x668]
    // 0xabd260: r4 = 150.000000
    //     0xabd260: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xabd264: ldr             x4, [x4, #0x690]
    // 0xabd268: r2 = Instance_TextOverflow
    //     0xabd268: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xabd26c: ldr             x2, [x2, #0xe10]
    // 0xabd270: r6 = Instance_EdgeInsets
    //     0xabd270: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xabd274: ldr             x6, [x6, #0xa78]
    // 0xabd278: r7 = Instance_CrossAxisAlignment
    //     0xabd278: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabd27c: ldr             x7, [x7, #0xa18]
    // 0xabd280: r12 = Instance_EdgeInsets
    //     0xabd280: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xabd284: ldr             x12, [x12, #0xa40]
    // 0xabd288: r19 = Instance_MainAxisAlignment
    //     0xabd288: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xabd28c: ldr             x19, [x19, #0xa8]
    // 0xabd290: r20 = Instance_EdgeInsets
    //     0xabd290: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xabd294: ldr             x20, [x20, #0x778]
    // 0xabd298: r13 = 4
    //     0xabd298: movz            x13, #0x4
    // 0xabd29c: r14 = Instance_Axis
    //     0xabd29c: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabd2a0: r9 = Instance_FlexFit
    //     0xabd2a0: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabd2a4: ldr             x9, [x9, #0xe08]
    // 0xabd2a8: r11 = Instance_BoxShape
    //     0xabd2a8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabd2ac: ldr             x11, [x11, #0x80]
    // 0xabd2b0: r0 = Instance_Alignment
    //     0xabd2b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xabd2b4: ldr             x0, [x0, #0xb10]
    // 0xabd2b8: r1 = -1
    //     0xabd2b8: movn            x1, #0
    // 0xabd2bc: d0 = 12.000000
    //     0xabd2bc: fmov            d0, #12.00000000
    // 0xabd2c0: r8 = 1
    //     0xabd2c0: movz            x8, #0x1
    // 0xabd2c4: b               #0xabdbd8
    // 0xabd2c8: tbnz            w2, #4, #0xabdb60
    // 0xabd2cc: r0 = Radius()
    //     0xabd2cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xabd2d0: d0 = 12.000000
    //     0xabd2d0: fmov            d0, #12.00000000
    // 0xabd2d4: stur            x0, [fp, #-0x28]
    // 0xabd2d8: StoreField: r0->field_7 = d0
    //     0xabd2d8: stur            d0, [x0, #7]
    // 0xabd2dc: StoreField: r0->field_f = d0
    //     0xabd2dc: stur            d0, [x0, #0xf]
    // 0xabd2e0: r0 = BorderRadius()
    //     0xabd2e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xabd2e4: mov             x2, x0
    // 0xabd2e8: ldur            x0, [fp, #-0x28]
    // 0xabd2ec: stur            x2, [fp, #-0x38]
    // 0xabd2f0: StoreField: r2->field_7 = r0
    //     0xabd2f0: stur            w0, [x2, #7]
    // 0xabd2f4: StoreField: r2->field_b = r0
    //     0xabd2f4: stur            w0, [x2, #0xb]
    // 0xabd2f8: StoreField: r2->field_f = r0
    //     0xabd2f8: stur            w0, [x2, #0xf]
    // 0xabd2fc: StoreField: r2->field_13 = r0
    //     0xabd2fc: stur            w0, [x2, #0x13]
    // 0xabd300: r1 = Instance_Color
    //     0xabd300: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabd304: d0 = 0.070000
    //     0xabd304: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xabd308: ldr             d0, [x17, #0x5f8]
    // 0xabd30c: r0 = withOpacity()
    //     0xabd30c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xabd310: r16 = 1.000000
    //     0xabd310: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xabd314: str             x16, [SP]
    // 0xabd318: mov             x2, x0
    // 0xabd31c: r1 = Null
    //     0xabd31c: mov             x1, NULL
    // 0xabd320: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xabd320: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xabd324: ldr             x4, [x4, #0x108]
    // 0xabd328: r0 = Border.all()
    //     0xabd328: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xabd32c: stur            x0, [fp, #-0x28]
    // 0xabd330: r0 = BoxDecoration()
    //     0xabd330: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xabd334: mov             x2, x0
    // 0xabd338: ldur            x0, [fp, #-0x28]
    // 0xabd33c: stur            x2, [fp, #-0x40]
    // 0xabd340: StoreField: r2->field_f = r0
    //     0xabd340: stur            w0, [x2, #0xf]
    // 0xabd344: ldur            x0, [fp, #-0x38]
    // 0xabd348: StoreField: r2->field_13 = r0
    //     0xabd348: stur            w0, [x2, #0x13]
    // 0xabd34c: r0 = Instance_LinearGradient
    //     0xabd34c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xabd350: ldr             x0, [x0, #0x660]
    // 0xabd354: StoreField: r2->field_1b = r0
    //     0xabd354: stur            w0, [x2, #0x1b]
    // 0xabd358: r0 = Instance_BoxShape
    //     0xabd358: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabd35c: ldr             x0, [x0, #0x80]
    // 0xabd360: StoreField: r2->field_23 = r0
    //     0xabd360: stur            w0, [x2, #0x23]
    // 0xabd364: ldur            x1, [fp, #-0x10]
    // 0xabd368: r0 = of()
    //     0xabd368: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd36c: LoadField: r1 = r0->field_87
    //     0xabd36c: ldur            w1, [x0, #0x87]
    // 0xabd370: DecompressPointer r1
    //     0xabd370: add             x1, x1, HEAP, lsl #32
    // 0xabd374: LoadField: r0 = r1->field_7
    //     0xabd374: ldur            w0, [x1, #7]
    // 0xabd378: DecompressPointer r0
    //     0xabd378: add             x0, x0, HEAP, lsl #32
    // 0xabd37c: r16 = 12.000000
    //     0xabd37c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabd380: ldr             x16, [x16, #0x9e8]
    // 0xabd384: r30 = Instance_Color
    //     0xabd384: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xabd388: stp             lr, x16, [SP]
    // 0xabd38c: mov             x1, x0
    // 0xabd390: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabd390: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabd394: ldr             x4, [x4, #0xaa0]
    // 0xabd398: r0 = copyWith()
    //     0xabd398: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd39c: stur            x0, [fp, #-0x28]
    // 0xabd3a0: r0 = Text()
    //     0xabd3a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd3a4: mov             x1, x0
    // 0xabd3a8: r0 = "Free"
    //     0xabd3a8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabd3ac: ldr             x0, [x0, #0x668]
    // 0xabd3b0: stur            x1, [fp, #-0x38]
    // 0xabd3b4: StoreField: r1->field_b = r0
    //     0xabd3b4: stur            w0, [x1, #0xb]
    // 0xabd3b8: ldur            x2, [fp, #-0x28]
    // 0xabd3bc: StoreField: r1->field_13 = r2
    //     0xabd3bc: stur            w2, [x1, #0x13]
    // 0xabd3c0: r0 = Center()
    //     0xabd3c0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xabd3c4: mov             x1, x0
    // 0xabd3c8: r0 = Instance_Alignment
    //     0xabd3c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xabd3cc: ldr             x0, [x0, #0xb10]
    // 0xabd3d0: stur            x1, [fp, #-0x28]
    // 0xabd3d4: StoreField: r1->field_f = r0
    //     0xabd3d4: stur            w0, [x1, #0xf]
    // 0xabd3d8: ldur            x0, [fp, #-0x38]
    // 0xabd3dc: StoreField: r1->field_b = r0
    //     0xabd3dc: stur            w0, [x1, #0xb]
    // 0xabd3e0: r0 = RotatedBox()
    //     0xabd3e0: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xabd3e4: r1 = -1
    //     0xabd3e4: movn            x1, #0
    // 0xabd3e8: stur            x0, [fp, #-0x38]
    // 0xabd3ec: StoreField: r0->field_f = r1
    //     0xabd3ec: stur            x1, [x0, #0xf]
    // 0xabd3f0: ldur            x1, [fp, #-0x28]
    // 0xabd3f4: StoreField: r0->field_b = r1
    //     0xabd3f4: stur            w1, [x0, #0xb]
    // 0xabd3f8: r0 = Container()
    //     0xabd3f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xabd3fc: stur            x0, [fp, #-0x28]
    // 0xabd400: r16 = 24.000000
    //     0xabd400: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xabd404: ldr             x16, [x16, #0xba8]
    // 0xabd408: r30 = 56.000000
    //     0xabd408: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabd40c: ldr             lr, [lr, #0xb78]
    // 0xabd410: stp             lr, x16, [SP, #0x10]
    // 0xabd414: r16 = Instance_BoxDecoration
    //     0xabd414: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xabd418: ldr             x16, [x16, #0xdb0]
    // 0xabd41c: ldur            lr, [fp, #-0x38]
    // 0xabd420: stp             lr, x16, [SP]
    // 0xabd424: mov             x1, x0
    // 0xabd428: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xabd428: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xabd42c: ldr             x4, [x4, #0x870]
    // 0xabd430: r0 = Container()
    //     0xabd430: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xabd434: ldur            x0, [fp, #-8]
    // 0xabd438: LoadField: r1 = r0->field_b
    //     0xabd438: ldur            w1, [x0, #0xb]
    // 0xabd43c: DecompressPointer r1
    //     0xabd43c: add             x1, x1, HEAP, lsl #32
    // 0xabd440: cmp             w1, NULL
    // 0xabd444: b.eq            #0xabe710
    // 0xabd448: LoadField: r2 = r1->field_b
    //     0xabd448: ldur            w2, [x1, #0xb]
    // 0xabd44c: DecompressPointer r2
    //     0xabd44c: add             x2, x2, HEAP, lsl #32
    // 0xabd450: LoadField: r1 = r2->field_b
    //     0xabd450: ldur            w1, [x2, #0xb]
    // 0xabd454: DecompressPointer r1
    //     0xabd454: add             x1, x1, HEAP, lsl #32
    // 0xabd458: cmp             w1, NULL
    // 0xabd45c: b.ne            #0xabd468
    // 0xabd460: r1 = Null
    //     0xabd460: mov             x1, NULL
    // 0xabd464: b               #0xabd488
    // 0xabd468: LoadField: r2 = r1->field_43
    //     0xabd468: ldur            w2, [x1, #0x43]
    // 0xabd46c: DecompressPointer r2
    //     0xabd46c: add             x2, x2, HEAP, lsl #32
    // 0xabd470: cmp             w2, NULL
    // 0xabd474: b.ne            #0xabd480
    // 0xabd478: r1 = Null
    //     0xabd478: mov             x1, NULL
    // 0xabd47c: b               #0xabd488
    // 0xabd480: LoadField: r1 = r2->field_7
    //     0xabd480: ldur            w1, [x2, #7]
    // 0xabd484: DecompressPointer r1
    //     0xabd484: add             x1, x1, HEAP, lsl #32
    // 0xabd488: cmp             w1, NULL
    // 0xabd48c: b.ne            #0xabd498
    // 0xabd490: r3 = ""
    //     0xabd490: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabd494: b               #0xabd49c
    // 0xabd498: mov             x3, x1
    // 0xabd49c: stur            x3, [fp, #-0x38]
    // 0xabd4a0: r1 = Function '<anonymous closure>':.
    //     0xabd4a0: add             x1, PP, #0x59, lsl #12  ; [pp+0x592f8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabd4a4: ldr             x1, [x1, #0x2f8]
    // 0xabd4a8: r2 = Null
    //     0xabd4a8: mov             x2, NULL
    // 0xabd4ac: r0 = AllocateClosure()
    //     0xabd4ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabd4b0: r1 = Function '<anonymous closure>':.
    //     0xabd4b0: add             x1, PP, #0x59, lsl #12  ; [pp+0x59300] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabd4b4: ldr             x1, [x1, #0x300]
    // 0xabd4b8: r2 = Null
    //     0xabd4b8: mov             x2, NULL
    // 0xabd4bc: stur            x0, [fp, #-0x48]
    // 0xabd4c0: r0 = AllocateClosure()
    //     0xabd4c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabd4c4: stur            x0, [fp, #-0x50]
    // 0xabd4c8: r0 = CachedNetworkImage()
    //     0xabd4c8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xabd4cc: stur            x0, [fp, #-0x58]
    // 0xabd4d0: r16 = 56.000000
    //     0xabd4d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabd4d4: ldr             x16, [x16, #0xb78]
    // 0xabd4d8: r30 = 56.000000
    //     0xabd4d8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabd4dc: ldr             lr, [lr, #0xb78]
    // 0xabd4e0: stp             lr, x16, [SP, #0x18]
    // 0xabd4e4: r16 = Instance_BoxFit
    //     0xabd4e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xabd4e8: ldr             x16, [x16, #0x118]
    // 0xabd4ec: ldur            lr, [fp, #-0x48]
    // 0xabd4f0: stp             lr, x16, [SP, #8]
    // 0xabd4f4: ldur            x16, [fp, #-0x50]
    // 0xabd4f8: str             x16, [SP]
    // 0xabd4fc: mov             x1, x0
    // 0xabd500: ldur            x2, [fp, #-0x38]
    // 0xabd504: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xabd504: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xabd508: ldr             x4, [x4, #0x710]
    // 0xabd50c: r0 = CachedNetworkImage()
    //     0xabd50c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xabd510: ldur            x0, [fp, #-8]
    // 0xabd514: LoadField: r1 = r0->field_b
    //     0xabd514: ldur            w1, [x0, #0xb]
    // 0xabd518: DecompressPointer r1
    //     0xabd518: add             x1, x1, HEAP, lsl #32
    // 0xabd51c: cmp             w1, NULL
    // 0xabd520: b.eq            #0xabe714
    // 0xabd524: LoadField: r2 = r1->field_b
    //     0xabd524: ldur            w2, [x1, #0xb]
    // 0xabd528: DecompressPointer r2
    //     0xabd528: add             x2, x2, HEAP, lsl #32
    // 0xabd52c: LoadField: r1 = r2->field_b
    //     0xabd52c: ldur            w1, [x2, #0xb]
    // 0xabd530: DecompressPointer r1
    //     0xabd530: add             x1, x1, HEAP, lsl #32
    // 0xabd534: cmp             w1, NULL
    // 0xabd538: b.ne            #0xabd544
    // 0xabd53c: r1 = Null
    //     0xabd53c: mov             x1, NULL
    // 0xabd540: b               #0xabd564
    // 0xabd544: LoadField: r2 = r1->field_43
    //     0xabd544: ldur            w2, [x1, #0x43]
    // 0xabd548: DecompressPointer r2
    //     0xabd548: add             x2, x2, HEAP, lsl #32
    // 0xabd54c: cmp             w2, NULL
    // 0xabd550: b.ne            #0xabd55c
    // 0xabd554: r1 = Null
    //     0xabd554: mov             x1, NULL
    // 0xabd558: b               #0xabd564
    // 0xabd55c: LoadField: r1 = r2->field_b
    //     0xabd55c: ldur            w1, [x2, #0xb]
    // 0xabd560: DecompressPointer r1
    //     0xabd560: add             x1, x1, HEAP, lsl #32
    // 0xabd564: cmp             w1, NULL
    // 0xabd568: b.ne            #0xabd574
    // 0xabd56c: r2 = ""
    //     0xabd56c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabd570: b               #0xabd578
    // 0xabd574: mov             x2, x1
    // 0xabd578: ldur            x1, [fp, #-0x10]
    // 0xabd57c: stur            x2, [fp, #-0x38]
    // 0xabd580: r0 = of()
    //     0xabd580: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd584: LoadField: r1 = r0->field_87
    //     0xabd584: ldur            w1, [x0, #0x87]
    // 0xabd588: DecompressPointer r1
    //     0xabd588: add             x1, x1, HEAP, lsl #32
    // 0xabd58c: LoadField: r0 = r1->field_7
    //     0xabd58c: ldur            w0, [x1, #7]
    // 0xabd590: DecompressPointer r0
    //     0xabd590: add             x0, x0, HEAP, lsl #32
    // 0xabd594: r16 = 12.000000
    //     0xabd594: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabd598: ldr             x16, [x16, #0x9e8]
    // 0xabd59c: r30 = Instance_Color
    //     0xabd59c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabd5a0: stp             lr, x16, [SP]
    // 0xabd5a4: mov             x1, x0
    // 0xabd5a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabd5a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabd5ac: ldr             x4, [x4, #0xaa0]
    // 0xabd5b0: r0 = copyWith()
    //     0xabd5b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd5b4: stur            x0, [fp, #-0x48]
    // 0xabd5b8: r0 = Text()
    //     0xabd5b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd5bc: mov             x1, x0
    // 0xabd5c0: ldur            x0, [fp, #-0x38]
    // 0xabd5c4: stur            x1, [fp, #-0x50]
    // 0xabd5c8: StoreField: r1->field_b = r0
    //     0xabd5c8: stur            w0, [x1, #0xb]
    // 0xabd5cc: ldur            x0, [fp, #-0x48]
    // 0xabd5d0: StoreField: r1->field_13 = r0
    //     0xabd5d0: stur            w0, [x1, #0x13]
    // 0xabd5d4: r2 = Instance_TextOverflow
    //     0xabd5d4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xabd5d8: ldr             x2, [x2, #0xe10]
    // 0xabd5dc: StoreField: r1->field_2b = r2
    //     0xabd5dc: stur            w2, [x1, #0x2b]
    // 0xabd5e0: r3 = 2
    //     0xabd5e0: movz            x3, #0x2
    // 0xabd5e4: StoreField: r1->field_37 = r3
    //     0xabd5e4: stur            w3, [x1, #0x37]
    // 0xabd5e8: r0 = SizedBox()
    //     0xabd5e8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xabd5ec: r4 = 150.000000
    //     0xabd5ec: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xabd5f0: ldr             x4, [x4, #0x690]
    // 0xabd5f4: stur            x0, [fp, #-0x38]
    // 0xabd5f8: StoreField: r0->field_f = r4
    //     0xabd5f8: stur            w4, [x0, #0xf]
    // 0xabd5fc: ldur            x1, [fp, #-0x50]
    // 0xabd600: StoreField: r0->field_b = r1
    //     0xabd600: stur            w1, [x0, #0xb]
    // 0xabd604: ldur            x1, [fp, #-0x10]
    // 0xabd608: r0 = of()
    //     0xabd608: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd60c: LoadField: r1 = r0->field_87
    //     0xabd60c: ldur            w1, [x0, #0x87]
    // 0xabd610: DecompressPointer r1
    //     0xabd610: add             x1, x1, HEAP, lsl #32
    // 0xabd614: LoadField: r0 = r1->field_2b
    //     0xabd614: ldur            w0, [x1, #0x2b]
    // 0xabd618: DecompressPointer r0
    //     0xabd618: add             x0, x0, HEAP, lsl #32
    // 0xabd61c: r16 = 12.000000
    //     0xabd61c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabd620: ldr             x16, [x16, #0x9e8]
    // 0xabd624: r30 = Instance_Color
    //     0xabd624: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xabd628: ldr             lr, [lr, #0x858]
    // 0xabd62c: stp             lr, x16, [SP]
    // 0xabd630: mov             x1, x0
    // 0xabd634: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabd634: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabd638: ldr             x4, [x4, #0xaa0]
    // 0xabd63c: r0 = copyWith()
    //     0xabd63c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd640: stur            x0, [fp, #-0x48]
    // 0xabd644: r0 = Text()
    //     0xabd644: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd648: r5 = "Free"
    //     0xabd648: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabd64c: ldr             x5, [x5, #0x668]
    // 0xabd650: stur            x0, [fp, #-0x50]
    // 0xabd654: StoreField: r0->field_b = r5
    //     0xabd654: stur            w5, [x0, #0xb]
    // 0xabd658: ldur            x1, [fp, #-0x48]
    // 0xabd65c: StoreField: r0->field_13 = r1
    //     0xabd65c: stur            w1, [x0, #0x13]
    // 0xabd660: ldur            x2, [fp, #-8]
    // 0xabd664: LoadField: r1 = r2->field_b
    //     0xabd664: ldur            w1, [x2, #0xb]
    // 0xabd668: DecompressPointer r1
    //     0xabd668: add             x1, x1, HEAP, lsl #32
    // 0xabd66c: cmp             w1, NULL
    // 0xabd670: b.eq            #0xabe718
    // 0xabd674: LoadField: r3 = r1->field_b
    //     0xabd674: ldur            w3, [x1, #0xb]
    // 0xabd678: DecompressPointer r3
    //     0xabd678: add             x3, x3, HEAP, lsl #32
    // 0xabd67c: LoadField: r1 = r3->field_b
    //     0xabd67c: ldur            w1, [x3, #0xb]
    // 0xabd680: DecompressPointer r1
    //     0xabd680: add             x1, x1, HEAP, lsl #32
    // 0xabd684: cmp             w1, NULL
    // 0xabd688: b.ne            #0xabd694
    // 0xabd68c: r1 = Null
    //     0xabd68c: mov             x1, NULL
    // 0xabd690: b               #0xabd6b4
    // 0xabd694: LoadField: r3 = r1->field_43
    //     0xabd694: ldur            w3, [x1, #0x43]
    // 0xabd698: DecompressPointer r3
    //     0xabd698: add             x3, x3, HEAP, lsl #32
    // 0xabd69c: cmp             w3, NULL
    // 0xabd6a0: b.ne            #0xabd6ac
    // 0xabd6a4: r1 = Null
    //     0xabd6a4: mov             x1, NULL
    // 0xabd6a8: b               #0xabd6b4
    // 0xabd6ac: LoadField: r1 = r3->field_13
    //     0xabd6ac: ldur            w1, [x3, #0x13]
    // 0xabd6b0: DecompressPointer r1
    //     0xabd6b0: add             x1, x1, HEAP, lsl #32
    // 0xabd6b4: cmp             w1, NULL
    // 0xabd6b8: b.ne            #0xabd6c4
    // 0xabd6bc: r6 = ""
    //     0xabd6bc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabd6c0: b               #0xabd6c8
    // 0xabd6c4: mov             x6, x1
    // 0xabd6c8: ldur            x5, [fp, #-0x28]
    // 0xabd6cc: ldur            x4, [fp, #-0x58]
    // 0xabd6d0: ldur            x3, [fp, #-0x38]
    // 0xabd6d4: ldur            x1, [fp, #-0x10]
    // 0xabd6d8: stur            x6, [fp, #-0x48]
    // 0xabd6dc: r0 = of()
    //     0xabd6dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd6e0: LoadField: r1 = r0->field_87
    //     0xabd6e0: ldur            w1, [x0, #0x87]
    // 0xabd6e4: DecompressPointer r1
    //     0xabd6e4: add             x1, x1, HEAP, lsl #32
    // 0xabd6e8: LoadField: r0 = r1->field_2b
    //     0xabd6e8: ldur            w0, [x1, #0x2b]
    // 0xabd6ec: DecompressPointer r0
    //     0xabd6ec: add             x0, x0, HEAP, lsl #32
    // 0xabd6f0: r16 = 12.000000
    //     0xabd6f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabd6f4: ldr             x16, [x16, #0x9e8]
    // 0xabd6f8: r30 = Instance_TextDecoration
    //     0xabd6f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xabd6fc: ldr             lr, [lr, #0xe30]
    // 0xabd700: stp             lr, x16, [SP]
    // 0xabd704: mov             x1, x0
    // 0xabd708: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xabd708: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xabd70c: ldr             x4, [x4, #0x698]
    // 0xabd710: r0 = copyWith()
    //     0xabd710: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd714: stur            x0, [fp, #-0x60]
    // 0xabd718: r0 = Text()
    //     0xabd718: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd71c: mov             x3, x0
    // 0xabd720: ldur            x0, [fp, #-0x48]
    // 0xabd724: stur            x3, [fp, #-0x68]
    // 0xabd728: StoreField: r3->field_b = r0
    //     0xabd728: stur            w0, [x3, #0xb]
    // 0xabd72c: ldur            x0, [fp, #-0x60]
    // 0xabd730: StoreField: r3->field_13 = r0
    //     0xabd730: stur            w0, [x3, #0x13]
    // 0xabd734: r1 = Null
    //     0xabd734: mov             x1, NULL
    // 0xabd738: r2 = 6
    //     0xabd738: movz            x2, #0x6
    // 0xabd73c: r0 = AllocateArray()
    //     0xabd73c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabd740: mov             x2, x0
    // 0xabd744: ldur            x0, [fp, #-0x50]
    // 0xabd748: stur            x2, [fp, #-0x48]
    // 0xabd74c: StoreField: r2->field_f = r0
    //     0xabd74c: stur            w0, [x2, #0xf]
    // 0xabd750: r16 = Instance_SizedBox
    //     0xabd750: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xabd754: ldr             x16, [x16, #0xa50]
    // 0xabd758: StoreField: r2->field_13 = r16
    //     0xabd758: stur            w16, [x2, #0x13]
    // 0xabd75c: ldur            x0, [fp, #-0x68]
    // 0xabd760: ArrayStore: r2[0] = r0  ; List_4
    //     0xabd760: stur            w0, [x2, #0x17]
    // 0xabd764: r1 = <Widget>
    //     0xabd764: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabd768: r0 = AllocateGrowableArray()
    //     0xabd768: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabd76c: mov             x1, x0
    // 0xabd770: ldur            x0, [fp, #-0x48]
    // 0xabd774: stur            x1, [fp, #-0x50]
    // 0xabd778: StoreField: r1->field_f = r0
    //     0xabd778: stur            w0, [x1, #0xf]
    // 0xabd77c: r2 = 6
    //     0xabd77c: movz            x2, #0x6
    // 0xabd780: StoreField: r1->field_b = r2
    //     0xabd780: stur            w2, [x1, #0xb]
    // 0xabd784: r0 = Row()
    //     0xabd784: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabd788: mov             x3, x0
    // 0xabd78c: r0 = Instance_Axis
    //     0xabd78c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabd790: stur            x3, [fp, #-0x48]
    // 0xabd794: StoreField: r3->field_f = r0
    //     0xabd794: stur            w0, [x3, #0xf]
    // 0xabd798: r4 = Instance_MainAxisAlignment
    //     0xabd798: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabd79c: ldr             x4, [x4, #0xa08]
    // 0xabd7a0: StoreField: r3->field_13 = r4
    //     0xabd7a0: stur            w4, [x3, #0x13]
    // 0xabd7a4: r5 = Instance_MainAxisSize
    //     0xabd7a4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabd7a8: ldr             x5, [x5, #0xa10]
    // 0xabd7ac: ArrayStore: r3[0] = r5  ; List_4
    //     0xabd7ac: stur            w5, [x3, #0x17]
    // 0xabd7b0: r6 = Instance_CrossAxisAlignment
    //     0xabd7b0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabd7b4: ldr             x6, [x6, #0xa18]
    // 0xabd7b8: StoreField: r3->field_1b = r6
    //     0xabd7b8: stur            w6, [x3, #0x1b]
    // 0xabd7bc: r7 = Instance_VerticalDirection
    //     0xabd7bc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabd7c0: ldr             x7, [x7, #0xa20]
    // 0xabd7c4: StoreField: r3->field_23 = r7
    //     0xabd7c4: stur            w7, [x3, #0x23]
    // 0xabd7c8: r8 = Instance_Clip
    //     0xabd7c8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabd7cc: ldr             x8, [x8, #0x38]
    // 0xabd7d0: StoreField: r3->field_2b = r8
    //     0xabd7d0: stur            w8, [x3, #0x2b]
    // 0xabd7d4: StoreField: r3->field_2f = rZR
    //     0xabd7d4: stur            xzr, [x3, #0x2f]
    // 0xabd7d8: ldur            x1, [fp, #-0x50]
    // 0xabd7dc: StoreField: r3->field_b = r1
    //     0xabd7dc: stur            w1, [x3, #0xb]
    // 0xabd7e0: r1 = Null
    //     0xabd7e0: mov             x1, NULL
    // 0xabd7e4: r2 = 6
    //     0xabd7e4: movz            x2, #0x6
    // 0xabd7e8: r0 = AllocateArray()
    //     0xabd7e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabd7ec: mov             x2, x0
    // 0xabd7f0: ldur            x0, [fp, #-0x38]
    // 0xabd7f4: stur            x2, [fp, #-0x50]
    // 0xabd7f8: StoreField: r2->field_f = r0
    //     0xabd7f8: stur            w0, [x2, #0xf]
    // 0xabd7fc: r16 = Instance_SizedBox
    //     0xabd7fc: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xabd800: ldr             x16, [x16, #0xc70]
    // 0xabd804: StoreField: r2->field_13 = r16
    //     0xabd804: stur            w16, [x2, #0x13]
    // 0xabd808: ldur            x0, [fp, #-0x48]
    // 0xabd80c: ArrayStore: r2[0] = r0  ; List_4
    //     0xabd80c: stur            w0, [x2, #0x17]
    // 0xabd810: r1 = <Widget>
    //     0xabd810: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabd814: r0 = AllocateGrowableArray()
    //     0xabd814: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabd818: mov             x1, x0
    // 0xabd81c: ldur            x0, [fp, #-0x50]
    // 0xabd820: stur            x1, [fp, #-0x38]
    // 0xabd824: StoreField: r1->field_f = r0
    //     0xabd824: stur            w0, [x1, #0xf]
    // 0xabd828: r2 = 6
    //     0xabd828: movz            x2, #0x6
    // 0xabd82c: StoreField: r1->field_b = r2
    //     0xabd82c: stur            w2, [x1, #0xb]
    // 0xabd830: r0 = Column()
    //     0xabd830: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabd834: mov             x1, x0
    // 0xabd838: r0 = Instance_Axis
    //     0xabd838: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabd83c: stur            x1, [fp, #-0x48]
    // 0xabd840: StoreField: r1->field_f = r0
    //     0xabd840: stur            w0, [x1, #0xf]
    // 0xabd844: r2 = Instance_MainAxisAlignment
    //     0xabd844: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabd848: ldr             x2, [x2, #0xa08]
    // 0xabd84c: StoreField: r1->field_13 = r2
    //     0xabd84c: stur            w2, [x1, #0x13]
    // 0xabd850: r3 = Instance_MainAxisSize
    //     0xabd850: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabd854: ldr             x3, [x3, #0xa10]
    // 0xabd858: ArrayStore: r1[0] = r3  ; List_4
    //     0xabd858: stur            w3, [x1, #0x17]
    // 0xabd85c: r4 = Instance_CrossAxisAlignment
    //     0xabd85c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabd860: ldr             x4, [x4, #0x890]
    // 0xabd864: StoreField: r1->field_1b = r4
    //     0xabd864: stur            w4, [x1, #0x1b]
    // 0xabd868: r5 = Instance_VerticalDirection
    //     0xabd868: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabd86c: ldr             x5, [x5, #0xa20]
    // 0xabd870: StoreField: r1->field_23 = r5
    //     0xabd870: stur            w5, [x1, #0x23]
    // 0xabd874: r6 = Instance_Clip
    //     0xabd874: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabd878: ldr             x6, [x6, #0x38]
    // 0xabd87c: StoreField: r1->field_2b = r6
    //     0xabd87c: stur            w6, [x1, #0x2b]
    // 0xabd880: StoreField: r1->field_2f = rZR
    //     0xabd880: stur            xzr, [x1, #0x2f]
    // 0xabd884: ldur            x7, [fp, #-0x38]
    // 0xabd888: StoreField: r1->field_b = r7
    //     0xabd888: stur            w7, [x1, #0xb]
    // 0xabd88c: r0 = Padding()
    //     0xabd88c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabd890: r6 = Instance_EdgeInsets
    //     0xabd890: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xabd894: ldr             x6, [x6, #0xa78]
    // 0xabd898: stur            x0, [fp, #-0x38]
    // 0xabd89c: StoreField: r0->field_f = r6
    //     0xabd89c: stur            w6, [x0, #0xf]
    // 0xabd8a0: ldur            x1, [fp, #-0x48]
    // 0xabd8a4: StoreField: r0->field_b = r1
    //     0xabd8a4: stur            w1, [x0, #0xb]
    // 0xabd8a8: r1 = <FlexParentData>
    //     0xabd8a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabd8ac: ldr             x1, [x1, #0xe00]
    // 0xabd8b0: r0 = Expanded()
    //     0xabd8b0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabd8b4: mov             x3, x0
    // 0xabd8b8: r0 = 1
    //     0xabd8b8: movz            x0, #0x1
    // 0xabd8bc: stur            x3, [fp, #-0x48]
    // 0xabd8c0: StoreField: r3->field_13 = r0
    //     0xabd8c0: stur            x0, [x3, #0x13]
    // 0xabd8c4: r4 = Instance_FlexFit
    //     0xabd8c4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabd8c8: ldr             x4, [x4, #0xe08]
    // 0xabd8cc: StoreField: r3->field_1b = r4
    //     0xabd8cc: stur            w4, [x3, #0x1b]
    // 0xabd8d0: ldur            x1, [fp, #-0x38]
    // 0xabd8d4: StoreField: r3->field_b = r1
    //     0xabd8d4: stur            w1, [x3, #0xb]
    // 0xabd8d8: r1 = Null
    //     0xabd8d8: mov             x1, NULL
    // 0xabd8dc: r2 = 6
    //     0xabd8dc: movz            x2, #0x6
    // 0xabd8e0: r0 = AllocateArray()
    //     0xabd8e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabd8e4: mov             x2, x0
    // 0xabd8e8: ldur            x0, [fp, #-0x28]
    // 0xabd8ec: stur            x2, [fp, #-0x38]
    // 0xabd8f0: StoreField: r2->field_f = r0
    //     0xabd8f0: stur            w0, [x2, #0xf]
    // 0xabd8f4: ldur            x0, [fp, #-0x58]
    // 0xabd8f8: StoreField: r2->field_13 = r0
    //     0xabd8f8: stur            w0, [x2, #0x13]
    // 0xabd8fc: ldur            x0, [fp, #-0x48]
    // 0xabd900: ArrayStore: r2[0] = r0  ; List_4
    //     0xabd900: stur            w0, [x2, #0x17]
    // 0xabd904: r1 = <Widget>
    //     0xabd904: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabd908: r0 = AllocateGrowableArray()
    //     0xabd908: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabd90c: mov             x1, x0
    // 0xabd910: ldur            x0, [fp, #-0x38]
    // 0xabd914: stur            x1, [fp, #-0x28]
    // 0xabd918: StoreField: r1->field_f = r0
    //     0xabd918: stur            w0, [x1, #0xf]
    // 0xabd91c: r2 = 6
    //     0xabd91c: movz            x2, #0x6
    // 0xabd920: StoreField: r1->field_b = r2
    //     0xabd920: stur            w2, [x1, #0xb]
    // 0xabd924: r0 = Row()
    //     0xabd924: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabd928: mov             x2, x0
    // 0xabd92c: r0 = Instance_Axis
    //     0xabd92c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabd930: stur            x2, [fp, #-0x38]
    // 0xabd934: StoreField: r2->field_f = r0
    //     0xabd934: stur            w0, [x2, #0xf]
    // 0xabd938: r3 = Instance_MainAxisAlignment
    //     0xabd938: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabd93c: ldr             x3, [x3, #0xa08]
    // 0xabd940: StoreField: r2->field_13 = r3
    //     0xabd940: stur            w3, [x2, #0x13]
    // 0xabd944: r4 = Instance_MainAxisSize
    //     0xabd944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabd948: ldr             x4, [x4, #0xa10]
    // 0xabd94c: ArrayStore: r2[0] = r4  ; List_4
    //     0xabd94c: stur            w4, [x2, #0x17]
    // 0xabd950: r7 = Instance_CrossAxisAlignment
    //     0xabd950: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabd954: ldr             x7, [x7, #0xa18]
    // 0xabd958: StoreField: r2->field_1b = r7
    //     0xabd958: stur            w7, [x2, #0x1b]
    // 0xabd95c: r5 = Instance_VerticalDirection
    //     0xabd95c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabd960: ldr             x5, [x5, #0xa20]
    // 0xabd964: StoreField: r2->field_23 = r5
    //     0xabd964: stur            w5, [x2, #0x23]
    // 0xabd968: r6 = Instance_Clip
    //     0xabd968: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabd96c: ldr             x6, [x6, #0x38]
    // 0xabd970: StoreField: r2->field_2b = r6
    //     0xabd970: stur            w6, [x2, #0x2b]
    // 0xabd974: StoreField: r2->field_2f = rZR
    //     0xabd974: stur            xzr, [x2, #0x2f]
    // 0xabd978: ldur            x1, [fp, #-0x28]
    // 0xabd97c: StoreField: r2->field_b = r1
    //     0xabd97c: stur            w1, [x2, #0xb]
    // 0xabd980: r1 = <FlexParentData>
    //     0xabd980: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabd984: ldr             x1, [x1, #0xe00]
    // 0xabd988: r0 = Expanded()
    //     0xabd988: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabd98c: r8 = 1
    //     0xabd98c: movz            x8, #0x1
    // 0xabd990: stur            x0, [fp, #-0x28]
    // 0xabd994: StoreField: r0->field_13 = r8
    //     0xabd994: stur            x8, [x0, #0x13]
    // 0xabd998: r9 = Instance_FlexFit
    //     0xabd998: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabd99c: ldr             x9, [x9, #0xe08]
    // 0xabd9a0: StoreField: r0->field_1b = r9
    //     0xabd9a0: stur            w9, [x0, #0x1b]
    // 0xabd9a4: ldur            x1, [fp, #-0x38]
    // 0xabd9a8: StoreField: r0->field_b = r1
    //     0xabd9a8: stur            w1, [x0, #0xb]
    // 0xabd9ac: ldur            x1, [fp, #-0x10]
    // 0xabd9b0: r0 = of()
    //     0xabd9b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabd9b4: LoadField: r1 = r0->field_87
    //     0xabd9b4: ldur            w1, [x0, #0x87]
    // 0xabd9b8: DecompressPointer r1
    //     0xabd9b8: add             x1, x1, HEAP, lsl #32
    // 0xabd9bc: LoadField: r0 = r1->field_7
    //     0xabd9bc: ldur            w0, [x1, #7]
    // 0xabd9c0: DecompressPointer r0
    //     0xabd9c0: add             x0, x0, HEAP, lsl #32
    // 0xabd9c4: r16 = 12.000000
    //     0xabd9c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabd9c8: ldr             x16, [x16, #0x9e8]
    // 0xabd9cc: r30 = Instance_Color
    //     0xabd9cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xabd9d0: ldr             lr, [lr, #0x858]
    // 0xabd9d4: stp             lr, x16, [SP]
    // 0xabd9d8: mov             x1, x0
    // 0xabd9dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabd9dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabd9e0: ldr             x4, [x4, #0xaa0]
    // 0xabd9e4: r0 = copyWith()
    //     0xabd9e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabd9e8: stur            x0, [fp, #-0x38]
    // 0xabd9ec: r0 = Text()
    //     0xabd9ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabd9f0: mov             x1, x0
    // 0xabd9f4: r0 = "Remove"
    //     0xabd9f4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0xabd9f8: ldr             x0, [x0, #0x7d0]
    // 0xabd9fc: stur            x1, [fp, #-0x48]
    // 0xabda00: StoreField: r1->field_b = r0
    //     0xabda00: stur            w0, [x1, #0xb]
    // 0xabda04: ldur            x0, [fp, #-0x38]
    // 0xabda08: StoreField: r1->field_13 = r0
    //     0xabda08: stur            w0, [x1, #0x13]
    // 0xabda0c: r0 = InkWell()
    //     0xabda0c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabda10: mov             x3, x0
    // 0xabda14: ldur            x0, [fp, #-0x48]
    // 0xabda18: stur            x3, [fp, #-0x38]
    // 0xabda1c: StoreField: r3->field_b = r0
    //     0xabda1c: stur            w0, [x3, #0xb]
    // 0xabda20: ldur            x2, [fp, #-0x18]
    // 0xabda24: r1 = Function '<anonymous closure>':.
    //     0xabda24: add             x1, PP, #0x59, lsl #12  ; [pp+0x59308] AnonymousClosure: (0xabf634), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabda28: ldr             x1, [x1, #0x308]
    // 0xabda2c: r0 = AllocateClosure()
    //     0xabda2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabda30: mov             x1, x0
    // 0xabda34: ldur            x0, [fp, #-0x38]
    // 0xabda38: StoreField: r0->field_f = r1
    //     0xabda38: stur            w1, [x0, #0xf]
    // 0xabda3c: r10 = true
    //     0xabda3c: add             x10, NULL, #0x20  ; true
    // 0xabda40: StoreField: r0->field_43 = r10
    //     0xabda40: stur            w10, [x0, #0x43]
    // 0xabda44: r11 = Instance_BoxShape
    //     0xabda44: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabda48: ldr             x11, [x11, #0x80]
    // 0xabda4c: StoreField: r0->field_47 = r11
    //     0xabda4c: stur            w11, [x0, #0x47]
    // 0xabda50: StoreField: r0->field_6f = r10
    //     0xabda50: stur            w10, [x0, #0x6f]
    // 0xabda54: r1 = false
    //     0xabda54: add             x1, NULL, #0x30  ; false
    // 0xabda58: StoreField: r0->field_73 = r1
    //     0xabda58: stur            w1, [x0, #0x73]
    // 0xabda5c: StoreField: r0->field_83 = r10
    //     0xabda5c: stur            w10, [x0, #0x83]
    // 0xabda60: StoreField: r0->field_7b = r1
    //     0xabda60: stur            w1, [x0, #0x7b]
    // 0xabda64: r0 = Padding()
    //     0xabda64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabda68: r12 = Instance_EdgeInsets
    //     0xabda68: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xabda6c: ldr             x12, [x12, #0xa40]
    // 0xabda70: stur            x0, [fp, #-0x48]
    // 0xabda74: StoreField: r0->field_f = r12
    //     0xabda74: stur            w12, [x0, #0xf]
    // 0xabda78: ldur            x1, [fp, #-0x38]
    // 0xabda7c: StoreField: r0->field_b = r1
    //     0xabda7c: stur            w1, [x0, #0xb]
    // 0xabda80: r1 = Null
    //     0xabda80: mov             x1, NULL
    // 0xabda84: r2 = 4
    //     0xabda84: movz            x2, #0x4
    // 0xabda88: r0 = AllocateArray()
    //     0xabda88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabda8c: mov             x2, x0
    // 0xabda90: ldur            x0, [fp, #-0x28]
    // 0xabda94: stur            x2, [fp, #-0x38]
    // 0xabda98: StoreField: r2->field_f = r0
    //     0xabda98: stur            w0, [x2, #0xf]
    // 0xabda9c: ldur            x0, [fp, #-0x48]
    // 0xabdaa0: StoreField: r2->field_13 = r0
    //     0xabdaa0: stur            w0, [x2, #0x13]
    // 0xabdaa4: r1 = <Widget>
    //     0xabdaa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabdaa8: r0 = AllocateGrowableArray()
    //     0xabdaa8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabdaac: mov             x1, x0
    // 0xabdab0: ldur            x0, [fp, #-0x38]
    // 0xabdab4: stur            x1, [fp, #-0x28]
    // 0xabdab8: StoreField: r1->field_f = r0
    //     0xabdab8: stur            w0, [x1, #0xf]
    // 0xabdabc: r13 = 4
    //     0xabdabc: movz            x13, #0x4
    // 0xabdac0: StoreField: r1->field_b = r13
    //     0xabdac0: stur            w13, [x1, #0xb]
    // 0xabdac4: r0 = Row()
    //     0xabdac4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabdac8: r14 = Instance_Axis
    //     0xabdac8: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabdacc: stur            x0, [fp, #-0x38]
    // 0xabdad0: StoreField: r0->field_f = r14
    //     0xabdad0: stur            w14, [x0, #0xf]
    // 0xabdad4: r19 = Instance_MainAxisAlignment
    //     0xabdad4: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xabdad8: ldr             x19, [x19, #0xa8]
    // 0xabdadc: StoreField: r0->field_13 = r19
    //     0xabdadc: stur            w19, [x0, #0x13]
    // 0xabdae0: r1 = Instance_MainAxisSize
    //     0xabdae0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabdae4: ldr             x1, [x1, #0xa10]
    // 0xabdae8: ArrayStore: r0[0] = r1  ; List_4
    //     0xabdae8: stur            w1, [x0, #0x17]
    // 0xabdaec: r2 = Instance_CrossAxisAlignment
    //     0xabdaec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabdaf0: ldr             x2, [x2, #0x890]
    // 0xabdaf4: StoreField: r0->field_1b = r2
    //     0xabdaf4: stur            w2, [x0, #0x1b]
    // 0xabdaf8: r3 = Instance_VerticalDirection
    //     0xabdaf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabdafc: ldr             x3, [x3, #0xa20]
    // 0xabdb00: StoreField: r0->field_23 = r3
    //     0xabdb00: stur            w3, [x0, #0x23]
    // 0xabdb04: r4 = Instance_Clip
    //     0xabdb04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabdb08: ldr             x4, [x4, #0x38]
    // 0xabdb0c: StoreField: r0->field_2b = r4
    //     0xabdb0c: stur            w4, [x0, #0x2b]
    // 0xabdb10: StoreField: r0->field_2f = rZR
    //     0xabdb10: stur            xzr, [x0, #0x2f]
    // 0xabdb14: ldur            x5, [fp, #-0x28]
    // 0xabdb18: StoreField: r0->field_b = r5
    //     0xabdb18: stur            w5, [x0, #0xb]
    // 0xabdb1c: r0 = Container()
    //     0xabdb1c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xabdb20: stur            x0, [fp, #-0x28]
    // 0xabdb24: ldur            x16, [fp, #-0x40]
    // 0xabdb28: ldur            lr, [fp, #-0x38]
    // 0xabdb2c: stp             lr, x16, [SP]
    // 0xabdb30: mov             x1, x0
    // 0xabdb34: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xabdb34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xabdb38: ldr             x4, [x4, #0x88]
    // 0xabdb3c: r0 = Container()
    //     0xabdb3c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xabdb40: r0 = Padding()
    //     0xabdb40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabdb44: r20 = Instance_EdgeInsets
    //     0xabdb44: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xabdb48: ldr             x20, [x20, #0x778]
    // 0xabdb4c: StoreField: r0->field_f = r20
    //     0xabdb4c: stur            w20, [x0, #0xf]
    // 0xabdb50: ldur            x1, [fp, #-0x28]
    // 0xabdb54: StoreField: r0->field_b = r1
    //     0xabdb54: stur            w1, [x0, #0xb]
    // 0xabdb58: mov             x2, x0
    // 0xabdb5c: b               #0xabe4e8
    // 0xabdb60: r10 = true
    //     0xabdb60: add             x10, NULL, #0x20  ; true
    // 0xabdb64: r3 = 2
    //     0xabdb64: movz            x3, #0x2
    // 0xabdb68: r5 = "Free"
    //     0xabdb68: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabdb6c: ldr             x5, [x5, #0x668]
    // 0xabdb70: r4 = 150.000000
    //     0xabdb70: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xabdb74: ldr             x4, [x4, #0x690]
    // 0xabdb78: r2 = Instance_TextOverflow
    //     0xabdb78: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xabdb7c: ldr             x2, [x2, #0xe10]
    // 0xabdb80: r6 = Instance_EdgeInsets
    //     0xabdb80: add             x6, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xabdb84: ldr             x6, [x6, #0xa78]
    // 0xabdb88: r7 = Instance_CrossAxisAlignment
    //     0xabdb88: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabdb8c: ldr             x7, [x7, #0xa18]
    // 0xabdb90: r12 = Instance_EdgeInsets
    //     0xabdb90: add             x12, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xabdb94: ldr             x12, [x12, #0xa40]
    // 0xabdb98: r19 = Instance_MainAxisAlignment
    //     0xabdb98: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xabdb9c: ldr             x19, [x19, #0xa8]
    // 0xabdba0: r20 = Instance_EdgeInsets
    //     0xabdba0: add             x20, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xabdba4: ldr             x20, [x20, #0x778]
    // 0xabdba8: r13 = 4
    //     0xabdba8: movz            x13, #0x4
    // 0xabdbac: r14 = Instance_Axis
    //     0xabdbac: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabdbb0: r9 = Instance_FlexFit
    //     0xabdbb0: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabdbb4: ldr             x9, [x9, #0xe08]
    // 0xabdbb8: r11 = Instance_BoxShape
    //     0xabdbb8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabdbbc: ldr             x11, [x11, #0x80]
    // 0xabdbc0: r0 = Instance_Alignment
    //     0xabdbc0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xabdbc4: ldr             x0, [x0, #0xb10]
    // 0xabdbc8: r1 = -1
    //     0xabdbc8: movn            x1, #0
    // 0xabdbcc: d0 = 12.000000
    //     0xabdbcc: fmov            d0, #12.00000000
    // 0xabdbd0: r8 = 1
    //     0xabdbd0: movz            x8, #0x1
    // 0xabdbd4: ldur            x23, [fp, #-8]
    // 0xabdbd8: r0 = Radius()
    //     0xabdbd8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xabdbdc: d0 = 12.000000
    //     0xabdbdc: fmov            d0, #12.00000000
    // 0xabdbe0: stur            x0, [fp, #-0x28]
    // 0xabdbe4: StoreField: r0->field_7 = d0
    //     0xabdbe4: stur            d0, [x0, #7]
    // 0xabdbe8: StoreField: r0->field_f = d0
    //     0xabdbe8: stur            d0, [x0, #0xf]
    // 0xabdbec: r0 = BorderRadius()
    //     0xabdbec: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xabdbf0: mov             x2, x0
    // 0xabdbf4: ldur            x0, [fp, #-0x28]
    // 0xabdbf8: stur            x2, [fp, #-0x38]
    // 0xabdbfc: StoreField: r2->field_7 = r0
    //     0xabdbfc: stur            w0, [x2, #7]
    // 0xabdc00: StoreField: r2->field_b = r0
    //     0xabdc00: stur            w0, [x2, #0xb]
    // 0xabdc04: StoreField: r2->field_f = r0
    //     0xabdc04: stur            w0, [x2, #0xf]
    // 0xabdc08: StoreField: r2->field_13 = r0
    //     0xabdc08: stur            w0, [x2, #0x13]
    // 0xabdc0c: ldur            x1, [fp, #-0x10]
    // 0xabdc10: r0 = of()
    //     0xabdc10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabdc14: LoadField: r1 = r0->field_5b
    //     0xabdc14: ldur            w1, [x0, #0x5b]
    // 0xabdc18: DecompressPointer r1
    //     0xabdc18: add             x1, x1, HEAP, lsl #32
    // 0xabdc1c: r0 = LoadClassIdInstr(r1)
    //     0xabdc1c: ldur            x0, [x1, #-1]
    //     0xabdc20: ubfx            x0, x0, #0xc, #0x14
    // 0xabdc24: d0 = 0.070000
    //     0xabdc24: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xabdc28: ldr             d0, [x17, #0x5f8]
    // 0xabdc2c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xabdc2c: sub             lr, x0, #0xffa
    //     0xabdc30: ldr             lr, [x21, lr, lsl #3]
    //     0xabdc34: blr             lr
    // 0xabdc38: r16 = 1.000000
    //     0xabdc38: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xabdc3c: str             x16, [SP]
    // 0xabdc40: mov             x2, x0
    // 0xabdc44: r1 = Null
    //     0xabdc44: mov             x1, NULL
    // 0xabdc48: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xabdc48: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xabdc4c: ldr             x4, [x4, #0x108]
    // 0xabdc50: r0 = Border.all()
    //     0xabdc50: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xabdc54: stur            x0, [fp, #-0x28]
    // 0xabdc58: r0 = BoxDecoration()
    //     0xabdc58: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xabdc5c: mov             x2, x0
    // 0xabdc60: ldur            x0, [fp, #-0x28]
    // 0xabdc64: stur            x2, [fp, #-0x40]
    // 0xabdc68: StoreField: r2->field_f = r0
    //     0xabdc68: stur            w0, [x2, #0xf]
    // 0xabdc6c: ldur            x0, [fp, #-0x38]
    // 0xabdc70: StoreField: r2->field_13 = r0
    //     0xabdc70: stur            w0, [x2, #0x13]
    // 0xabdc74: r0 = Instance_BoxShape
    //     0xabdc74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabdc78: ldr             x0, [x0, #0x80]
    // 0xabdc7c: StoreField: r2->field_23 = r0
    //     0xabdc7c: stur            w0, [x2, #0x23]
    // 0xabdc80: ldur            x1, [fp, #-0x10]
    // 0xabdc84: r0 = of()
    //     0xabdc84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabdc88: LoadField: r1 = r0->field_5b
    //     0xabdc88: ldur            w1, [x0, #0x5b]
    // 0xabdc8c: DecompressPointer r1
    //     0xabdc8c: add             x1, x1, HEAP, lsl #32
    // 0xabdc90: r0 = LoadClassIdInstr(r1)
    //     0xabdc90: ldur            x0, [x1, #-1]
    //     0xabdc94: ubfx            x0, x0, #0xc, #0x14
    // 0xabdc98: d0 = 0.400000
    //     0xabdc98: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xabdc9c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xabdc9c: sub             lr, x0, #0xffa
    //     0xabdca0: ldr             lr, [x21, lr, lsl #3]
    //     0xabdca4: blr             lr
    // 0xabdca8: stur            x0, [fp, #-0x28]
    // 0xabdcac: r0 = BoxDecoration()
    //     0xabdcac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xabdcb0: mov             x2, x0
    // 0xabdcb4: ldur            x0, [fp, #-0x28]
    // 0xabdcb8: stur            x2, [fp, #-0x38]
    // 0xabdcbc: StoreField: r2->field_7 = r0
    //     0xabdcbc: stur            w0, [x2, #7]
    // 0xabdcc0: r0 = Instance_BorderRadius
    //     0xabdcc0: add             x0, PP, #0x48, lsl #12  ; [pp+0x48990] Obj!BorderRadius@d5a281
    //     0xabdcc4: ldr             x0, [x0, #0x990]
    // 0xabdcc8: StoreField: r2->field_13 = r0
    //     0xabdcc8: stur            w0, [x2, #0x13]
    // 0xabdccc: r0 = Instance_BoxShape
    //     0xabdccc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabdcd0: ldr             x0, [x0, #0x80]
    // 0xabdcd4: StoreField: r2->field_23 = r0
    //     0xabdcd4: stur            w0, [x2, #0x23]
    // 0xabdcd8: ldur            x1, [fp, #-0x10]
    // 0xabdcdc: r0 = of()
    //     0xabdcdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabdce0: LoadField: r1 = r0->field_87
    //     0xabdce0: ldur            w1, [x0, #0x87]
    // 0xabdce4: DecompressPointer r1
    //     0xabdce4: add             x1, x1, HEAP, lsl #32
    // 0xabdce8: LoadField: r0 = r1->field_7
    //     0xabdce8: ldur            w0, [x1, #7]
    // 0xabdcec: DecompressPointer r0
    //     0xabdcec: add             x0, x0, HEAP, lsl #32
    // 0xabdcf0: r16 = 12.000000
    //     0xabdcf0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabdcf4: ldr             x16, [x16, #0x9e8]
    // 0xabdcf8: r30 = Instance_Color
    //     0xabdcf8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xabdcfc: stp             lr, x16, [SP]
    // 0xabdd00: mov             x1, x0
    // 0xabdd04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabdd04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabdd08: ldr             x4, [x4, #0xaa0]
    // 0xabdd0c: r0 = copyWith()
    //     0xabdd0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabdd10: stur            x0, [fp, #-0x28]
    // 0xabdd14: r0 = Text()
    //     0xabdd14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabdd18: mov             x1, x0
    // 0xabdd1c: r0 = "Free"
    //     0xabdd1c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabdd20: ldr             x0, [x0, #0x668]
    // 0xabdd24: stur            x1, [fp, #-0x48]
    // 0xabdd28: StoreField: r1->field_b = r0
    //     0xabdd28: stur            w0, [x1, #0xb]
    // 0xabdd2c: ldur            x2, [fp, #-0x28]
    // 0xabdd30: StoreField: r1->field_13 = r2
    //     0xabdd30: stur            w2, [x1, #0x13]
    // 0xabdd34: r0 = Center()
    //     0xabdd34: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xabdd38: mov             x1, x0
    // 0xabdd3c: r0 = Instance_Alignment
    //     0xabdd3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xabdd40: ldr             x0, [x0, #0xb10]
    // 0xabdd44: stur            x1, [fp, #-0x28]
    // 0xabdd48: StoreField: r1->field_f = r0
    //     0xabdd48: stur            w0, [x1, #0xf]
    // 0xabdd4c: ldur            x0, [fp, #-0x48]
    // 0xabdd50: StoreField: r1->field_b = r0
    //     0xabdd50: stur            w0, [x1, #0xb]
    // 0xabdd54: r0 = RotatedBox()
    //     0xabdd54: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xabdd58: mov             x1, x0
    // 0xabdd5c: r0 = -1
    //     0xabdd5c: movn            x0, #0
    // 0xabdd60: stur            x1, [fp, #-0x48]
    // 0xabdd64: StoreField: r1->field_f = r0
    //     0xabdd64: stur            x0, [x1, #0xf]
    // 0xabdd68: ldur            x0, [fp, #-0x28]
    // 0xabdd6c: StoreField: r1->field_b = r0
    //     0xabdd6c: stur            w0, [x1, #0xb]
    // 0xabdd70: r0 = Container()
    //     0xabdd70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xabdd74: stur            x0, [fp, #-0x28]
    // 0xabdd78: r16 = 24.000000
    //     0xabdd78: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xabdd7c: ldr             x16, [x16, #0xba8]
    // 0xabdd80: r30 = 56.000000
    //     0xabdd80: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabdd84: ldr             lr, [lr, #0xb78]
    // 0xabdd88: stp             lr, x16, [SP, #0x10]
    // 0xabdd8c: ldur            x16, [fp, #-0x38]
    // 0xabdd90: ldur            lr, [fp, #-0x48]
    // 0xabdd94: stp             lr, x16, [SP]
    // 0xabdd98: mov             x1, x0
    // 0xabdd9c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xabdd9c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xabdda0: ldr             x4, [x4, #0x870]
    // 0xabdda4: r0 = Container()
    //     0xabdda4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xabdda8: ldur            x0, [fp, #-8]
    // 0xabddac: LoadField: r1 = r0->field_b
    //     0xabddac: ldur            w1, [x0, #0xb]
    // 0xabddb0: DecompressPointer r1
    //     0xabddb0: add             x1, x1, HEAP, lsl #32
    // 0xabddb4: cmp             w1, NULL
    // 0xabddb8: b.eq            #0xabe71c
    // 0xabddbc: LoadField: r2 = r1->field_b
    //     0xabddbc: ldur            w2, [x1, #0xb]
    // 0xabddc0: DecompressPointer r2
    //     0xabddc0: add             x2, x2, HEAP, lsl #32
    // 0xabddc4: LoadField: r1 = r2->field_b
    //     0xabddc4: ldur            w1, [x2, #0xb]
    // 0xabddc8: DecompressPointer r1
    //     0xabddc8: add             x1, x1, HEAP, lsl #32
    // 0xabddcc: cmp             w1, NULL
    // 0xabddd0: b.ne            #0xabdddc
    // 0xabddd4: r1 = Null
    //     0xabddd4: mov             x1, NULL
    // 0xabddd8: b               #0xabddfc
    // 0xabdddc: LoadField: r2 = r1->field_43
    //     0xabdddc: ldur            w2, [x1, #0x43]
    // 0xabdde0: DecompressPointer r2
    //     0xabdde0: add             x2, x2, HEAP, lsl #32
    // 0xabdde4: cmp             w2, NULL
    // 0xabdde8: b.ne            #0xabddf4
    // 0xabddec: r1 = Null
    //     0xabddec: mov             x1, NULL
    // 0xabddf0: b               #0xabddfc
    // 0xabddf4: LoadField: r1 = r2->field_7
    //     0xabddf4: ldur            w1, [x2, #7]
    // 0xabddf8: DecompressPointer r1
    //     0xabddf8: add             x1, x1, HEAP, lsl #32
    // 0xabddfc: cmp             w1, NULL
    // 0xabde00: b.ne            #0xabde0c
    // 0xabde04: r3 = ""
    //     0xabde04: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabde08: b               #0xabde10
    // 0xabde0c: mov             x3, x1
    // 0xabde10: stur            x3, [fp, #-0x38]
    // 0xabde14: r1 = Function '<anonymous closure>':.
    //     0xabde14: add             x1, PP, #0x59, lsl #12  ; [pp+0x59310] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabde18: ldr             x1, [x1, #0x310]
    // 0xabde1c: r2 = Null
    //     0xabde1c: mov             x2, NULL
    // 0xabde20: r0 = AllocateClosure()
    //     0xabde20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabde24: r1 = Function '<anonymous closure>':.
    //     0xabde24: add             x1, PP, #0x59, lsl #12  ; [pp+0x59318] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabde28: ldr             x1, [x1, #0x318]
    // 0xabde2c: r2 = Null
    //     0xabde2c: mov             x2, NULL
    // 0xabde30: stur            x0, [fp, #-0x48]
    // 0xabde34: r0 = AllocateClosure()
    //     0xabde34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabde38: stur            x0, [fp, #-0x50]
    // 0xabde3c: r0 = CachedNetworkImage()
    //     0xabde3c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xabde40: stur            x0, [fp, #-0x58]
    // 0xabde44: r16 = 56.000000
    //     0xabde44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabde48: ldr             x16, [x16, #0xb78]
    // 0xabde4c: r30 = 56.000000
    //     0xabde4c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabde50: ldr             lr, [lr, #0xb78]
    // 0xabde54: stp             lr, x16, [SP, #0x18]
    // 0xabde58: r16 = Instance_BoxFit
    //     0xabde58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xabde5c: ldr             x16, [x16, #0x118]
    // 0xabde60: ldur            lr, [fp, #-0x48]
    // 0xabde64: stp             lr, x16, [SP, #8]
    // 0xabde68: ldur            x16, [fp, #-0x50]
    // 0xabde6c: str             x16, [SP]
    // 0xabde70: mov             x1, x0
    // 0xabde74: ldur            x2, [fp, #-0x38]
    // 0xabde78: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xabde78: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xabde7c: ldr             x4, [x4, #0x710]
    // 0xabde80: r0 = CachedNetworkImage()
    //     0xabde80: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xabde84: ldur            x0, [fp, #-8]
    // 0xabde88: LoadField: r1 = r0->field_b
    //     0xabde88: ldur            w1, [x0, #0xb]
    // 0xabde8c: DecompressPointer r1
    //     0xabde8c: add             x1, x1, HEAP, lsl #32
    // 0xabde90: cmp             w1, NULL
    // 0xabde94: b.eq            #0xabe720
    // 0xabde98: LoadField: r2 = r1->field_b
    //     0xabde98: ldur            w2, [x1, #0xb]
    // 0xabde9c: DecompressPointer r2
    //     0xabde9c: add             x2, x2, HEAP, lsl #32
    // 0xabdea0: LoadField: r1 = r2->field_b
    //     0xabdea0: ldur            w1, [x2, #0xb]
    // 0xabdea4: DecompressPointer r1
    //     0xabdea4: add             x1, x1, HEAP, lsl #32
    // 0xabdea8: cmp             w1, NULL
    // 0xabdeac: b.ne            #0xabdeb8
    // 0xabdeb0: r1 = Null
    //     0xabdeb0: mov             x1, NULL
    // 0xabdeb4: b               #0xabded8
    // 0xabdeb8: LoadField: r2 = r1->field_43
    //     0xabdeb8: ldur            w2, [x1, #0x43]
    // 0xabdebc: DecompressPointer r2
    //     0xabdebc: add             x2, x2, HEAP, lsl #32
    // 0xabdec0: cmp             w2, NULL
    // 0xabdec4: b.ne            #0xabded0
    // 0xabdec8: r1 = Null
    //     0xabdec8: mov             x1, NULL
    // 0xabdecc: b               #0xabded8
    // 0xabded0: LoadField: r1 = r2->field_b
    //     0xabded0: ldur            w1, [x2, #0xb]
    // 0xabded4: DecompressPointer r1
    //     0xabded4: add             x1, x1, HEAP, lsl #32
    // 0xabded8: cmp             w1, NULL
    // 0xabdedc: b.ne            #0xabdee8
    // 0xabdee0: r2 = ""
    //     0xabdee0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabdee4: b               #0xabdeec
    // 0xabdee8: mov             x2, x1
    // 0xabdeec: ldur            x1, [fp, #-0x10]
    // 0xabdef0: stur            x2, [fp, #-0x38]
    // 0xabdef4: r0 = of()
    //     0xabdef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabdef8: LoadField: r1 = r0->field_87
    //     0xabdef8: ldur            w1, [x0, #0x87]
    // 0xabdefc: DecompressPointer r1
    //     0xabdefc: add             x1, x1, HEAP, lsl #32
    // 0xabdf00: LoadField: r0 = r1->field_7
    //     0xabdf00: ldur            w0, [x1, #7]
    // 0xabdf04: DecompressPointer r0
    //     0xabdf04: add             x0, x0, HEAP, lsl #32
    // 0xabdf08: r16 = 12.000000
    //     0xabdf08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabdf0c: ldr             x16, [x16, #0x9e8]
    // 0xabdf10: r30 = Instance_Color
    //     0xabdf10: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabdf14: stp             lr, x16, [SP]
    // 0xabdf18: mov             x1, x0
    // 0xabdf1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabdf1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabdf20: ldr             x4, [x4, #0xaa0]
    // 0xabdf24: r0 = copyWith()
    //     0xabdf24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabdf28: stur            x0, [fp, #-0x48]
    // 0xabdf2c: r0 = Text()
    //     0xabdf2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabdf30: mov             x1, x0
    // 0xabdf34: ldur            x0, [fp, #-0x38]
    // 0xabdf38: stur            x1, [fp, #-0x50]
    // 0xabdf3c: StoreField: r1->field_b = r0
    //     0xabdf3c: stur            w0, [x1, #0xb]
    // 0xabdf40: ldur            x0, [fp, #-0x48]
    // 0xabdf44: StoreField: r1->field_13 = r0
    //     0xabdf44: stur            w0, [x1, #0x13]
    // 0xabdf48: r0 = Instance_TextOverflow
    //     0xabdf48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xabdf4c: ldr             x0, [x0, #0xe10]
    // 0xabdf50: StoreField: r1->field_2b = r0
    //     0xabdf50: stur            w0, [x1, #0x2b]
    // 0xabdf54: r0 = 2
    //     0xabdf54: movz            x0, #0x2
    // 0xabdf58: StoreField: r1->field_37 = r0
    //     0xabdf58: stur            w0, [x1, #0x37]
    // 0xabdf5c: r0 = SizedBox()
    //     0xabdf5c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xabdf60: mov             x2, x0
    // 0xabdf64: r0 = 150.000000
    //     0xabdf64: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xabdf68: ldr             x0, [x0, #0x690]
    // 0xabdf6c: stur            x2, [fp, #-0x38]
    // 0xabdf70: StoreField: r2->field_f = r0
    //     0xabdf70: stur            w0, [x2, #0xf]
    // 0xabdf74: ldur            x0, [fp, #-0x50]
    // 0xabdf78: StoreField: r2->field_b = r0
    //     0xabdf78: stur            w0, [x2, #0xb]
    // 0xabdf7c: ldur            x1, [fp, #-0x10]
    // 0xabdf80: r0 = of()
    //     0xabdf80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabdf84: LoadField: r1 = r0->field_87
    //     0xabdf84: ldur            w1, [x0, #0x87]
    // 0xabdf88: DecompressPointer r1
    //     0xabdf88: add             x1, x1, HEAP, lsl #32
    // 0xabdf8c: LoadField: r0 = r1->field_2b
    //     0xabdf8c: ldur            w0, [x1, #0x2b]
    // 0xabdf90: DecompressPointer r0
    //     0xabdf90: add             x0, x0, HEAP, lsl #32
    // 0xabdf94: r16 = 12.000000
    //     0xabdf94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabdf98: ldr             x16, [x16, #0x9e8]
    // 0xabdf9c: r30 = Instance_Color
    //     0xabdf9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabdfa0: stp             lr, x16, [SP]
    // 0xabdfa4: mov             x1, x0
    // 0xabdfa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabdfa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabdfac: ldr             x4, [x4, #0xaa0]
    // 0xabdfb0: r0 = copyWith()
    //     0xabdfb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabdfb4: stur            x0, [fp, #-0x48]
    // 0xabdfb8: r0 = Text()
    //     0xabdfb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabdfbc: mov             x2, x0
    // 0xabdfc0: r0 = "Free"
    //     0xabdfc0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xabdfc4: ldr             x0, [x0, #0x668]
    // 0xabdfc8: stur            x2, [fp, #-0x50]
    // 0xabdfcc: StoreField: r2->field_b = r0
    //     0xabdfcc: stur            w0, [x2, #0xb]
    // 0xabdfd0: ldur            x0, [fp, #-0x48]
    // 0xabdfd4: StoreField: r2->field_13 = r0
    //     0xabdfd4: stur            w0, [x2, #0x13]
    // 0xabdfd8: ldur            x0, [fp, #-8]
    // 0xabdfdc: LoadField: r1 = r0->field_b
    //     0xabdfdc: ldur            w1, [x0, #0xb]
    // 0xabdfe0: DecompressPointer r1
    //     0xabdfe0: add             x1, x1, HEAP, lsl #32
    // 0xabdfe4: cmp             w1, NULL
    // 0xabdfe8: b.eq            #0xabe724
    // 0xabdfec: LoadField: r3 = r1->field_b
    //     0xabdfec: ldur            w3, [x1, #0xb]
    // 0xabdff0: DecompressPointer r3
    //     0xabdff0: add             x3, x3, HEAP, lsl #32
    // 0xabdff4: LoadField: r1 = r3->field_b
    //     0xabdff4: ldur            w1, [x3, #0xb]
    // 0xabdff8: DecompressPointer r1
    //     0xabdff8: add             x1, x1, HEAP, lsl #32
    // 0xabdffc: cmp             w1, NULL
    // 0xabe000: b.ne            #0xabe00c
    // 0xabe004: r1 = Null
    //     0xabe004: mov             x1, NULL
    // 0xabe008: b               #0xabe02c
    // 0xabe00c: LoadField: r3 = r1->field_43
    //     0xabe00c: ldur            w3, [x1, #0x43]
    // 0xabe010: DecompressPointer r3
    //     0xabe010: add             x3, x3, HEAP, lsl #32
    // 0xabe014: cmp             w3, NULL
    // 0xabe018: b.ne            #0xabe024
    // 0xabe01c: r1 = Null
    //     0xabe01c: mov             x1, NULL
    // 0xabe020: b               #0xabe02c
    // 0xabe024: LoadField: r1 = r3->field_13
    //     0xabe024: ldur            w1, [x3, #0x13]
    // 0xabe028: DecompressPointer r1
    //     0xabe028: add             x1, x1, HEAP, lsl #32
    // 0xabe02c: cmp             w1, NULL
    // 0xabe030: b.ne            #0xabe03c
    // 0xabe034: r6 = ""
    //     0xabe034: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabe038: b               #0xabe040
    // 0xabe03c: mov             x6, x1
    // 0xabe040: ldur            x5, [fp, #-0x28]
    // 0xabe044: ldur            x4, [fp, #-0x58]
    // 0xabe048: ldur            x3, [fp, #-0x38]
    // 0xabe04c: ldur            x1, [fp, #-0x10]
    // 0xabe050: stur            x6, [fp, #-0x48]
    // 0xabe054: r0 = of()
    //     0xabe054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabe058: LoadField: r1 = r0->field_87
    //     0xabe058: ldur            w1, [x0, #0x87]
    // 0xabe05c: DecompressPointer r1
    //     0xabe05c: add             x1, x1, HEAP, lsl #32
    // 0xabe060: LoadField: r0 = r1->field_2b
    //     0xabe060: ldur            w0, [x1, #0x2b]
    // 0xabe064: DecompressPointer r0
    //     0xabe064: add             x0, x0, HEAP, lsl #32
    // 0xabe068: r16 = 12.000000
    //     0xabe068: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabe06c: ldr             x16, [x16, #0x9e8]
    // 0xabe070: r30 = Instance_TextDecoration
    //     0xabe070: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xabe074: ldr             lr, [lr, #0xe30]
    // 0xabe078: stp             lr, x16, [SP]
    // 0xabe07c: mov             x1, x0
    // 0xabe080: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xabe080: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xabe084: ldr             x4, [x4, #0x698]
    // 0xabe088: r0 = copyWith()
    //     0xabe088: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabe08c: stur            x0, [fp, #-0x60]
    // 0xabe090: r0 = Text()
    //     0xabe090: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabe094: mov             x3, x0
    // 0xabe098: ldur            x0, [fp, #-0x48]
    // 0xabe09c: stur            x3, [fp, #-0x68]
    // 0xabe0a0: StoreField: r3->field_b = r0
    //     0xabe0a0: stur            w0, [x3, #0xb]
    // 0xabe0a4: ldur            x0, [fp, #-0x60]
    // 0xabe0a8: StoreField: r3->field_13 = r0
    //     0xabe0a8: stur            w0, [x3, #0x13]
    // 0xabe0ac: r1 = Null
    //     0xabe0ac: mov             x1, NULL
    // 0xabe0b0: r2 = 6
    //     0xabe0b0: movz            x2, #0x6
    // 0xabe0b4: r0 = AllocateArray()
    //     0xabe0b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe0b8: mov             x2, x0
    // 0xabe0bc: ldur            x0, [fp, #-0x50]
    // 0xabe0c0: stur            x2, [fp, #-0x48]
    // 0xabe0c4: StoreField: r2->field_f = r0
    //     0xabe0c4: stur            w0, [x2, #0xf]
    // 0xabe0c8: r16 = Instance_SizedBox
    //     0xabe0c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xabe0cc: ldr             x16, [x16, #0xa50]
    // 0xabe0d0: StoreField: r2->field_13 = r16
    //     0xabe0d0: stur            w16, [x2, #0x13]
    // 0xabe0d4: ldur            x0, [fp, #-0x68]
    // 0xabe0d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xabe0d8: stur            w0, [x2, #0x17]
    // 0xabe0dc: r1 = <Widget>
    //     0xabe0dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabe0e0: r0 = AllocateGrowableArray()
    //     0xabe0e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabe0e4: mov             x1, x0
    // 0xabe0e8: ldur            x0, [fp, #-0x48]
    // 0xabe0ec: stur            x1, [fp, #-0x50]
    // 0xabe0f0: StoreField: r1->field_f = r0
    //     0xabe0f0: stur            w0, [x1, #0xf]
    // 0xabe0f4: r2 = 6
    //     0xabe0f4: movz            x2, #0x6
    // 0xabe0f8: StoreField: r1->field_b = r2
    //     0xabe0f8: stur            w2, [x1, #0xb]
    // 0xabe0fc: r0 = Row()
    //     0xabe0fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabe100: mov             x3, x0
    // 0xabe104: r0 = Instance_Axis
    //     0xabe104: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabe108: stur            x3, [fp, #-0x48]
    // 0xabe10c: StoreField: r3->field_f = r0
    //     0xabe10c: stur            w0, [x3, #0xf]
    // 0xabe110: r4 = Instance_MainAxisAlignment
    //     0xabe110: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabe114: ldr             x4, [x4, #0xa08]
    // 0xabe118: StoreField: r3->field_13 = r4
    //     0xabe118: stur            w4, [x3, #0x13]
    // 0xabe11c: r5 = Instance_MainAxisSize
    //     0xabe11c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabe120: ldr             x5, [x5, #0xa10]
    // 0xabe124: ArrayStore: r3[0] = r5  ; List_4
    //     0xabe124: stur            w5, [x3, #0x17]
    // 0xabe128: r6 = Instance_CrossAxisAlignment
    //     0xabe128: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabe12c: ldr             x6, [x6, #0xa18]
    // 0xabe130: StoreField: r3->field_1b = r6
    //     0xabe130: stur            w6, [x3, #0x1b]
    // 0xabe134: r7 = Instance_VerticalDirection
    //     0xabe134: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabe138: ldr             x7, [x7, #0xa20]
    // 0xabe13c: StoreField: r3->field_23 = r7
    //     0xabe13c: stur            w7, [x3, #0x23]
    // 0xabe140: r8 = Instance_Clip
    //     0xabe140: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabe144: ldr             x8, [x8, #0x38]
    // 0xabe148: StoreField: r3->field_2b = r8
    //     0xabe148: stur            w8, [x3, #0x2b]
    // 0xabe14c: StoreField: r3->field_2f = rZR
    //     0xabe14c: stur            xzr, [x3, #0x2f]
    // 0xabe150: ldur            x1, [fp, #-0x50]
    // 0xabe154: StoreField: r3->field_b = r1
    //     0xabe154: stur            w1, [x3, #0xb]
    // 0xabe158: r1 = Null
    //     0xabe158: mov             x1, NULL
    // 0xabe15c: r2 = 6
    //     0xabe15c: movz            x2, #0x6
    // 0xabe160: r0 = AllocateArray()
    //     0xabe160: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe164: mov             x2, x0
    // 0xabe168: ldur            x0, [fp, #-0x38]
    // 0xabe16c: stur            x2, [fp, #-0x50]
    // 0xabe170: StoreField: r2->field_f = r0
    //     0xabe170: stur            w0, [x2, #0xf]
    // 0xabe174: r16 = Instance_SizedBox
    //     0xabe174: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xabe178: ldr             x16, [x16, #0xc70]
    // 0xabe17c: StoreField: r2->field_13 = r16
    //     0xabe17c: stur            w16, [x2, #0x13]
    // 0xabe180: ldur            x0, [fp, #-0x48]
    // 0xabe184: ArrayStore: r2[0] = r0  ; List_4
    //     0xabe184: stur            w0, [x2, #0x17]
    // 0xabe188: r1 = <Widget>
    //     0xabe188: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabe18c: r0 = AllocateGrowableArray()
    //     0xabe18c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabe190: mov             x1, x0
    // 0xabe194: ldur            x0, [fp, #-0x50]
    // 0xabe198: stur            x1, [fp, #-0x38]
    // 0xabe19c: StoreField: r1->field_f = r0
    //     0xabe19c: stur            w0, [x1, #0xf]
    // 0xabe1a0: r2 = 6
    //     0xabe1a0: movz            x2, #0x6
    // 0xabe1a4: StoreField: r1->field_b = r2
    //     0xabe1a4: stur            w2, [x1, #0xb]
    // 0xabe1a8: r0 = Column()
    //     0xabe1a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabe1ac: mov             x1, x0
    // 0xabe1b0: r0 = Instance_Axis
    //     0xabe1b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabe1b4: stur            x1, [fp, #-0x48]
    // 0xabe1b8: StoreField: r1->field_f = r0
    //     0xabe1b8: stur            w0, [x1, #0xf]
    // 0xabe1bc: r2 = Instance_MainAxisAlignment
    //     0xabe1bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabe1c0: ldr             x2, [x2, #0xa08]
    // 0xabe1c4: StoreField: r1->field_13 = r2
    //     0xabe1c4: stur            w2, [x1, #0x13]
    // 0xabe1c8: r3 = Instance_MainAxisSize
    //     0xabe1c8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabe1cc: ldr             x3, [x3, #0xa10]
    // 0xabe1d0: ArrayStore: r1[0] = r3  ; List_4
    //     0xabe1d0: stur            w3, [x1, #0x17]
    // 0xabe1d4: r4 = Instance_CrossAxisAlignment
    //     0xabe1d4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabe1d8: ldr             x4, [x4, #0x890]
    // 0xabe1dc: StoreField: r1->field_1b = r4
    //     0xabe1dc: stur            w4, [x1, #0x1b]
    // 0xabe1e0: r5 = Instance_VerticalDirection
    //     0xabe1e0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabe1e4: ldr             x5, [x5, #0xa20]
    // 0xabe1e8: StoreField: r1->field_23 = r5
    //     0xabe1e8: stur            w5, [x1, #0x23]
    // 0xabe1ec: r6 = Instance_Clip
    //     0xabe1ec: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabe1f0: ldr             x6, [x6, #0x38]
    // 0xabe1f4: StoreField: r1->field_2b = r6
    //     0xabe1f4: stur            w6, [x1, #0x2b]
    // 0xabe1f8: StoreField: r1->field_2f = rZR
    //     0xabe1f8: stur            xzr, [x1, #0x2f]
    // 0xabe1fc: ldur            x7, [fp, #-0x38]
    // 0xabe200: StoreField: r1->field_b = r7
    //     0xabe200: stur            w7, [x1, #0xb]
    // 0xabe204: r0 = Padding()
    //     0xabe204: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabe208: mov             x2, x0
    // 0xabe20c: r0 = Instance_EdgeInsets
    //     0xabe20c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xabe210: ldr             x0, [x0, #0xa78]
    // 0xabe214: stur            x2, [fp, #-0x38]
    // 0xabe218: StoreField: r2->field_f = r0
    //     0xabe218: stur            w0, [x2, #0xf]
    // 0xabe21c: ldur            x0, [fp, #-0x48]
    // 0xabe220: StoreField: r2->field_b = r0
    //     0xabe220: stur            w0, [x2, #0xb]
    // 0xabe224: r1 = <FlexParentData>
    //     0xabe224: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabe228: ldr             x1, [x1, #0xe00]
    // 0xabe22c: r0 = Expanded()
    //     0xabe22c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabe230: mov             x3, x0
    // 0xabe234: r0 = 1
    //     0xabe234: movz            x0, #0x1
    // 0xabe238: stur            x3, [fp, #-0x48]
    // 0xabe23c: StoreField: r3->field_13 = r0
    //     0xabe23c: stur            x0, [x3, #0x13]
    // 0xabe240: r4 = Instance_FlexFit
    //     0xabe240: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabe244: ldr             x4, [x4, #0xe08]
    // 0xabe248: StoreField: r3->field_1b = r4
    //     0xabe248: stur            w4, [x3, #0x1b]
    // 0xabe24c: ldur            x1, [fp, #-0x38]
    // 0xabe250: StoreField: r3->field_b = r1
    //     0xabe250: stur            w1, [x3, #0xb]
    // 0xabe254: r1 = Null
    //     0xabe254: mov             x1, NULL
    // 0xabe258: r2 = 6
    //     0xabe258: movz            x2, #0x6
    // 0xabe25c: r0 = AllocateArray()
    //     0xabe25c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe260: mov             x2, x0
    // 0xabe264: ldur            x0, [fp, #-0x28]
    // 0xabe268: stur            x2, [fp, #-0x38]
    // 0xabe26c: StoreField: r2->field_f = r0
    //     0xabe26c: stur            w0, [x2, #0xf]
    // 0xabe270: ldur            x0, [fp, #-0x58]
    // 0xabe274: StoreField: r2->field_13 = r0
    //     0xabe274: stur            w0, [x2, #0x13]
    // 0xabe278: ldur            x0, [fp, #-0x48]
    // 0xabe27c: ArrayStore: r2[0] = r0  ; List_4
    //     0xabe27c: stur            w0, [x2, #0x17]
    // 0xabe280: r1 = <Widget>
    //     0xabe280: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabe284: r0 = AllocateGrowableArray()
    //     0xabe284: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabe288: mov             x1, x0
    // 0xabe28c: ldur            x0, [fp, #-0x38]
    // 0xabe290: stur            x1, [fp, #-0x28]
    // 0xabe294: StoreField: r1->field_f = r0
    //     0xabe294: stur            w0, [x1, #0xf]
    // 0xabe298: r2 = 6
    //     0xabe298: movz            x2, #0x6
    // 0xabe29c: StoreField: r1->field_b = r2
    //     0xabe29c: stur            w2, [x1, #0xb]
    // 0xabe2a0: r0 = Row()
    //     0xabe2a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabe2a4: mov             x2, x0
    // 0xabe2a8: r0 = Instance_Axis
    //     0xabe2a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabe2ac: stur            x2, [fp, #-0x38]
    // 0xabe2b0: StoreField: r2->field_f = r0
    //     0xabe2b0: stur            w0, [x2, #0xf]
    // 0xabe2b4: r3 = Instance_MainAxisAlignment
    //     0xabe2b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabe2b8: ldr             x3, [x3, #0xa08]
    // 0xabe2bc: StoreField: r2->field_13 = r3
    //     0xabe2bc: stur            w3, [x2, #0x13]
    // 0xabe2c0: r4 = Instance_MainAxisSize
    //     0xabe2c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabe2c4: ldr             x4, [x4, #0xa10]
    // 0xabe2c8: ArrayStore: r2[0] = r4  ; List_4
    //     0xabe2c8: stur            w4, [x2, #0x17]
    // 0xabe2cc: r1 = Instance_CrossAxisAlignment
    //     0xabe2cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabe2d0: ldr             x1, [x1, #0xa18]
    // 0xabe2d4: StoreField: r2->field_1b = r1
    //     0xabe2d4: stur            w1, [x2, #0x1b]
    // 0xabe2d8: r5 = Instance_VerticalDirection
    //     0xabe2d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabe2dc: ldr             x5, [x5, #0xa20]
    // 0xabe2e0: StoreField: r2->field_23 = r5
    //     0xabe2e0: stur            w5, [x2, #0x23]
    // 0xabe2e4: r6 = Instance_Clip
    //     0xabe2e4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabe2e8: ldr             x6, [x6, #0x38]
    // 0xabe2ec: StoreField: r2->field_2b = r6
    //     0xabe2ec: stur            w6, [x2, #0x2b]
    // 0xabe2f0: StoreField: r2->field_2f = rZR
    //     0xabe2f0: stur            xzr, [x2, #0x2f]
    // 0xabe2f4: ldur            x1, [fp, #-0x28]
    // 0xabe2f8: StoreField: r2->field_b = r1
    //     0xabe2f8: stur            w1, [x2, #0xb]
    // 0xabe2fc: r1 = <FlexParentData>
    //     0xabe2fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabe300: ldr             x1, [x1, #0xe00]
    // 0xabe304: r0 = Expanded()
    //     0xabe304: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabe308: mov             x2, x0
    // 0xabe30c: r0 = 1
    //     0xabe30c: movz            x0, #0x1
    // 0xabe310: stur            x2, [fp, #-0x28]
    // 0xabe314: StoreField: r2->field_13 = r0
    //     0xabe314: stur            x0, [x2, #0x13]
    // 0xabe318: r0 = Instance_FlexFit
    //     0xabe318: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabe31c: ldr             x0, [x0, #0xe08]
    // 0xabe320: StoreField: r2->field_1b = r0
    //     0xabe320: stur            w0, [x2, #0x1b]
    // 0xabe324: ldur            x0, [fp, #-0x38]
    // 0xabe328: StoreField: r2->field_b = r0
    //     0xabe328: stur            w0, [x2, #0xb]
    // 0xabe32c: ldur            x1, [fp, #-0x10]
    // 0xabe330: r0 = of()
    //     0xabe330: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabe334: LoadField: r1 = r0->field_87
    //     0xabe334: ldur            w1, [x0, #0x87]
    // 0xabe338: DecompressPointer r1
    //     0xabe338: add             x1, x1, HEAP, lsl #32
    // 0xabe33c: LoadField: r0 = r1->field_7
    //     0xabe33c: ldur            w0, [x1, #7]
    // 0xabe340: DecompressPointer r0
    //     0xabe340: add             x0, x0, HEAP, lsl #32
    // 0xabe344: r16 = 12.000000
    //     0xabe344: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabe348: ldr             x16, [x16, #0x9e8]
    // 0xabe34c: r30 = Instance_Color
    //     0xabe34c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xabe350: ldr             lr, [lr, #0x858]
    // 0xabe354: stp             lr, x16, [SP]
    // 0xabe358: mov             x1, x0
    // 0xabe35c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabe35c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabe360: ldr             x4, [x4, #0xaa0]
    // 0xabe364: r0 = copyWith()
    //     0xabe364: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabe368: stur            x0, [fp, #-0x10]
    // 0xabe36c: r0 = Text()
    //     0xabe36c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabe370: mov             x1, x0
    // 0xabe374: r0 = "Add"
    //     0xabe374: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a70] "Add"
    //     0xabe378: ldr             x0, [x0, #0xa70]
    // 0xabe37c: stur            x1, [fp, #-0x38]
    // 0xabe380: StoreField: r1->field_b = r0
    //     0xabe380: stur            w0, [x1, #0xb]
    // 0xabe384: ldur            x0, [fp, #-0x10]
    // 0xabe388: StoreField: r1->field_13 = r0
    //     0xabe388: stur            w0, [x1, #0x13]
    // 0xabe38c: r0 = InkWell()
    //     0xabe38c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabe390: mov             x3, x0
    // 0xabe394: ldur            x0, [fp, #-0x38]
    // 0xabe398: stur            x3, [fp, #-0x10]
    // 0xabe39c: StoreField: r3->field_b = r0
    //     0xabe39c: stur            w0, [x3, #0xb]
    // 0xabe3a0: ldur            x2, [fp, #-0x18]
    // 0xabe3a4: r1 = Function '<anonymous closure>':.
    //     0xabe3a4: add             x1, PP, #0x59, lsl #12  ; [pp+0x59320] AnonymousClosure: (0xabf558), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabe3a8: ldr             x1, [x1, #0x320]
    // 0xabe3ac: r0 = AllocateClosure()
    //     0xabe3ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe3b0: mov             x1, x0
    // 0xabe3b4: ldur            x0, [fp, #-0x10]
    // 0xabe3b8: StoreField: r0->field_f = r1
    //     0xabe3b8: stur            w1, [x0, #0xf]
    // 0xabe3bc: r1 = true
    //     0xabe3bc: add             x1, NULL, #0x20  ; true
    // 0xabe3c0: StoreField: r0->field_43 = r1
    //     0xabe3c0: stur            w1, [x0, #0x43]
    // 0xabe3c4: r2 = Instance_BoxShape
    //     0xabe3c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabe3c8: ldr             x2, [x2, #0x80]
    // 0xabe3cc: StoreField: r0->field_47 = r2
    //     0xabe3cc: stur            w2, [x0, #0x47]
    // 0xabe3d0: StoreField: r0->field_6f = r1
    //     0xabe3d0: stur            w1, [x0, #0x6f]
    // 0xabe3d4: r2 = false
    //     0xabe3d4: add             x2, NULL, #0x30  ; false
    // 0xabe3d8: StoreField: r0->field_73 = r2
    //     0xabe3d8: stur            w2, [x0, #0x73]
    // 0xabe3dc: StoreField: r0->field_83 = r1
    //     0xabe3dc: stur            w1, [x0, #0x83]
    // 0xabe3e0: StoreField: r0->field_7b = r2
    //     0xabe3e0: stur            w2, [x0, #0x7b]
    // 0xabe3e4: r0 = Padding()
    //     0xabe3e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabe3e8: mov             x3, x0
    // 0xabe3ec: r0 = Instance_EdgeInsets
    //     0xabe3ec: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xabe3f0: ldr             x0, [x0, #0xa40]
    // 0xabe3f4: stur            x3, [fp, #-0x38]
    // 0xabe3f8: StoreField: r3->field_f = r0
    //     0xabe3f8: stur            w0, [x3, #0xf]
    // 0xabe3fc: ldur            x0, [fp, #-0x10]
    // 0xabe400: StoreField: r3->field_b = r0
    //     0xabe400: stur            w0, [x3, #0xb]
    // 0xabe404: r1 = Null
    //     0xabe404: mov             x1, NULL
    // 0xabe408: r2 = 4
    //     0xabe408: movz            x2, #0x4
    // 0xabe40c: r0 = AllocateArray()
    //     0xabe40c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe410: mov             x2, x0
    // 0xabe414: ldur            x0, [fp, #-0x28]
    // 0xabe418: stur            x2, [fp, #-0x10]
    // 0xabe41c: StoreField: r2->field_f = r0
    //     0xabe41c: stur            w0, [x2, #0xf]
    // 0xabe420: ldur            x0, [fp, #-0x38]
    // 0xabe424: StoreField: r2->field_13 = r0
    //     0xabe424: stur            w0, [x2, #0x13]
    // 0xabe428: r1 = <Widget>
    //     0xabe428: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabe42c: r0 = AllocateGrowableArray()
    //     0xabe42c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabe430: mov             x1, x0
    // 0xabe434: ldur            x0, [fp, #-0x10]
    // 0xabe438: stur            x1, [fp, #-0x28]
    // 0xabe43c: StoreField: r1->field_f = r0
    //     0xabe43c: stur            w0, [x1, #0xf]
    // 0xabe440: r0 = 4
    //     0xabe440: movz            x0, #0x4
    // 0xabe444: StoreField: r1->field_b = r0
    //     0xabe444: stur            w0, [x1, #0xb]
    // 0xabe448: r0 = Row()
    //     0xabe448: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabe44c: mov             x1, x0
    // 0xabe450: r0 = Instance_Axis
    //     0xabe450: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabe454: stur            x1, [fp, #-0x10]
    // 0xabe458: StoreField: r1->field_f = r0
    //     0xabe458: stur            w0, [x1, #0xf]
    // 0xabe45c: r0 = Instance_MainAxisAlignment
    //     0xabe45c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xabe460: ldr             x0, [x0, #0xa8]
    // 0xabe464: StoreField: r1->field_13 = r0
    //     0xabe464: stur            w0, [x1, #0x13]
    // 0xabe468: r0 = Instance_MainAxisSize
    //     0xabe468: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabe46c: ldr             x0, [x0, #0xa10]
    // 0xabe470: ArrayStore: r1[0] = r0  ; List_4
    //     0xabe470: stur            w0, [x1, #0x17]
    // 0xabe474: r2 = Instance_CrossAxisAlignment
    //     0xabe474: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabe478: ldr             x2, [x2, #0x890]
    // 0xabe47c: StoreField: r1->field_1b = r2
    //     0xabe47c: stur            w2, [x1, #0x1b]
    // 0xabe480: r3 = Instance_VerticalDirection
    //     0xabe480: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabe484: ldr             x3, [x3, #0xa20]
    // 0xabe488: StoreField: r1->field_23 = r3
    //     0xabe488: stur            w3, [x1, #0x23]
    // 0xabe48c: r4 = Instance_Clip
    //     0xabe48c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabe490: ldr             x4, [x4, #0x38]
    // 0xabe494: StoreField: r1->field_2b = r4
    //     0xabe494: stur            w4, [x1, #0x2b]
    // 0xabe498: StoreField: r1->field_2f = rZR
    //     0xabe498: stur            xzr, [x1, #0x2f]
    // 0xabe49c: ldur            x5, [fp, #-0x28]
    // 0xabe4a0: StoreField: r1->field_b = r5
    //     0xabe4a0: stur            w5, [x1, #0xb]
    // 0xabe4a4: r0 = Container()
    //     0xabe4a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xabe4a8: stur            x0, [fp, #-0x28]
    // 0xabe4ac: ldur            x16, [fp, #-0x40]
    // 0xabe4b0: ldur            lr, [fp, #-0x10]
    // 0xabe4b4: stp             lr, x16, [SP]
    // 0xabe4b8: mov             x1, x0
    // 0xabe4bc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xabe4bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xabe4c0: ldr             x4, [x4, #0x88]
    // 0xabe4c4: r0 = Container()
    //     0xabe4c4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xabe4c8: r0 = Padding()
    //     0xabe4c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabe4cc: mov             x1, x0
    // 0xabe4d0: r0 = Instance_EdgeInsets
    //     0xabe4d0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xabe4d4: ldr             x0, [x0, #0x778]
    // 0xabe4d8: StoreField: r1->field_f = r0
    //     0xabe4d8: stur            w0, [x1, #0xf]
    // 0xabe4dc: ldur            x0, [fp, #-0x28]
    // 0xabe4e0: StoreField: r1->field_b = r0
    //     0xabe4e0: stur            w0, [x1, #0xb]
    // 0xabe4e4: mov             x2, x1
    // 0xabe4e8: ldur            x0, [fp, #-8]
    // 0xabe4ec: ldur            x1, [fp, #-0x20]
    // 0xabe4f0: stur            x2, [fp, #-0x10]
    // 0xabe4f4: r0 = Visibility()
    //     0xabe4f4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xabe4f8: mov             x3, x0
    // 0xabe4fc: ldur            x0, [fp, #-0x10]
    // 0xabe500: stur            x3, [fp, #-0x28]
    // 0xabe504: StoreField: r3->field_b = r0
    //     0xabe504: stur            w0, [x3, #0xb]
    // 0xabe508: r0 = Instance_SizedBox
    //     0xabe508: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xabe50c: StoreField: r3->field_f = r0
    //     0xabe50c: stur            w0, [x3, #0xf]
    // 0xabe510: ldur            x0, [fp, #-0x20]
    // 0xabe514: StoreField: r3->field_13 = r0
    //     0xabe514: stur            w0, [x3, #0x13]
    // 0xabe518: r0 = false
    //     0xabe518: add             x0, NULL, #0x30  ; false
    // 0xabe51c: ArrayStore: r3[0] = r0  ; List_4
    //     0xabe51c: stur            w0, [x3, #0x17]
    // 0xabe520: StoreField: r3->field_1b = r0
    //     0xabe520: stur            w0, [x3, #0x1b]
    // 0xabe524: StoreField: r3->field_1f = r0
    //     0xabe524: stur            w0, [x3, #0x1f]
    // 0xabe528: StoreField: r3->field_23 = r0
    //     0xabe528: stur            w0, [x3, #0x23]
    // 0xabe52c: StoreField: r3->field_27 = r0
    //     0xabe52c: stur            w0, [x3, #0x27]
    // 0xabe530: StoreField: r3->field_2b = r0
    //     0xabe530: stur            w0, [x3, #0x2b]
    // 0xabe534: ldur            x0, [fp, #-8]
    // 0xabe538: LoadField: r1 = r0->field_b
    //     0xabe538: ldur            w1, [x0, #0xb]
    // 0xabe53c: DecompressPointer r1
    //     0xabe53c: add             x1, x1, HEAP, lsl #32
    // 0xabe540: cmp             w1, NULL
    // 0xabe544: b.eq            #0xabe728
    // 0xabe548: LoadField: r0 = r1->field_b
    //     0xabe548: ldur            w0, [x1, #0xb]
    // 0xabe54c: DecompressPointer r0
    //     0xabe54c: add             x0, x0, HEAP, lsl #32
    // 0xabe550: LoadField: r1 = r0->field_b
    //     0xabe550: ldur            w1, [x0, #0xb]
    // 0xabe554: DecompressPointer r1
    //     0xabe554: add             x1, x1, HEAP, lsl #32
    // 0xabe558: cmp             w1, NULL
    // 0xabe55c: b.ne            #0xabe568
    // 0xabe560: r0 = Null
    //     0xabe560: mov             x0, NULL
    // 0xabe564: b               #0xabe58c
    // 0xabe568: LoadField: r0 = r1->field_f
    //     0xabe568: ldur            w0, [x1, #0xf]
    // 0xabe56c: DecompressPointer r0
    //     0xabe56c: add             x0, x0, HEAP, lsl #32
    // 0xabe570: cmp             w0, NULL
    // 0xabe574: b.ne            #0xabe580
    // 0xabe578: r0 = Null
    //     0xabe578: mov             x0, NULL
    // 0xabe57c: b               #0xabe58c
    // 0xabe580: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xabe580: ldur            w1, [x0, #0x17]
    // 0xabe584: DecompressPointer r1
    //     0xabe584: add             x1, x1, HEAP, lsl #32
    // 0xabe588: mov             x0, x1
    // 0xabe58c: cmp             w0, NULL
    // 0xabe590: b.ne            #0xabe5a4
    // 0xabe594: r1 = <BEntities>
    //     0xabe594: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0xabe598: ldr             x1, [x1, #0x130]
    // 0xabe59c: r2 = 0
    //     0xabe59c: movz            x2, #0
    // 0xabe5a0: r0 = AllocateArray()
    //     0xabe5a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe5a4: ldur            x2, [fp, #-0x30]
    // 0xabe5a8: ldur            x1, [fp, #-0x28]
    // 0xabe5ac: r3 = LoadClassIdInstr(r0)
    //     0xabe5ac: ldur            x3, [x0, #-1]
    //     0xabe5b0: ubfx            x3, x3, #0xc, #0x14
    // 0xabe5b4: str             x0, [SP]
    // 0xabe5b8: mov             x0, x3
    // 0xabe5bc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xabe5bc: movz            x17, #0xc898
    //     0xabe5c0: add             lr, x0, x17
    //     0xabe5c4: ldr             lr, [x21, lr, lsl #3]
    //     0xabe5c8: blr             lr
    // 0xabe5cc: r3 = LoadInt32Instr(r0)
    //     0xabe5cc: sbfx            x3, x0, #1, #0x1f
    // 0xabe5d0: ldur            x2, [fp, #-0x18]
    // 0xabe5d4: stur            x3, [fp, #-0x70]
    // 0xabe5d8: r1 = Function '<anonymous closure>':.
    //     0xabe5d8: add             x1, PP, #0x59, lsl #12  ; [pp+0x59328] AnonymousClosure: (0xabe72c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabe5dc: ldr             x1, [x1, #0x328]
    // 0xabe5e0: r0 = AllocateClosure()
    //     0xabe5e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe5e4: r1 = Function '<anonymous closure>':.
    //     0xabe5e4: add             x1, PP, #0x59, lsl #12  ; [pp+0x59330] AnonymousClosure: (0x9fbd20), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xbaa86c)
    //     0xabe5e8: ldr             x1, [x1, #0x330]
    // 0xabe5ec: r2 = Null
    //     0xabe5ec: mov             x2, NULL
    // 0xabe5f0: stur            x0, [fp, #-8]
    // 0xabe5f4: r0 = AllocateClosure()
    //     0xabe5f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe5f8: stur            x0, [fp, #-0x10]
    // 0xabe5fc: r0 = ListView()
    //     0xabe5fc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xabe600: stur            x0, [fp, #-0x18]
    // 0xabe604: r16 = true
    //     0xabe604: add             x16, NULL, #0x20  ; true
    // 0xabe608: r30 = false
    //     0xabe608: add             lr, NULL, #0x30  ; false
    // 0xabe60c: stp             lr, x16, [SP, #8]
    // 0xabe610: r16 = Instance_NeverScrollableScrollPhysics
    //     0xabe610: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xabe614: ldr             x16, [x16, #0x1c8]
    // 0xabe618: str             x16, [SP]
    // 0xabe61c: mov             x1, x0
    // 0xabe620: ldur            x2, [fp, #-8]
    // 0xabe624: ldur            x3, [fp, #-0x70]
    // 0xabe628: ldur            x5, [fp, #-0x10]
    // 0xabe62c: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xabe62c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xabe630: ldr             x4, [x4, #0x138]
    // 0xabe634: r0 = ListView.separated()
    //     0xabe634: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xabe638: r1 = Null
    //     0xabe638: mov             x1, NULL
    // 0xabe63c: r2 = 6
    //     0xabe63c: movz            x2, #0x6
    // 0xabe640: r0 = AllocateArray()
    //     0xabe640: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabe644: mov             x2, x0
    // 0xabe648: ldur            x0, [fp, #-0x30]
    // 0xabe64c: stur            x2, [fp, #-8]
    // 0xabe650: StoreField: r2->field_f = r0
    //     0xabe650: stur            w0, [x2, #0xf]
    // 0xabe654: ldur            x0, [fp, #-0x28]
    // 0xabe658: StoreField: r2->field_13 = r0
    //     0xabe658: stur            w0, [x2, #0x13]
    // 0xabe65c: ldur            x0, [fp, #-0x18]
    // 0xabe660: ArrayStore: r2[0] = r0  ; List_4
    //     0xabe660: stur            w0, [x2, #0x17]
    // 0xabe664: r1 = <Widget>
    //     0xabe664: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabe668: r0 = AllocateGrowableArray()
    //     0xabe668: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabe66c: mov             x1, x0
    // 0xabe670: ldur            x0, [fp, #-8]
    // 0xabe674: stur            x1, [fp, #-0x10]
    // 0xabe678: StoreField: r1->field_f = r0
    //     0xabe678: stur            w0, [x1, #0xf]
    // 0xabe67c: r0 = 6
    //     0xabe67c: movz            x0, #0x6
    // 0xabe680: StoreField: r1->field_b = r0
    //     0xabe680: stur            w0, [x1, #0xb]
    // 0xabe684: r0 = Column()
    //     0xabe684: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabe688: mov             x1, x0
    // 0xabe68c: r0 = Instance_Axis
    //     0xabe68c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabe690: stur            x1, [fp, #-8]
    // 0xabe694: StoreField: r1->field_f = r0
    //     0xabe694: stur            w0, [x1, #0xf]
    // 0xabe698: r0 = Instance_MainAxisAlignment
    //     0xabe698: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabe69c: ldr             x0, [x0, #0xa08]
    // 0xabe6a0: StoreField: r1->field_13 = r0
    //     0xabe6a0: stur            w0, [x1, #0x13]
    // 0xabe6a4: r0 = Instance_MainAxisSize
    //     0xabe6a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabe6a8: ldr             x0, [x0, #0xa10]
    // 0xabe6ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xabe6ac: stur            w0, [x1, #0x17]
    // 0xabe6b0: r0 = Instance_CrossAxisAlignment
    //     0xabe6b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabe6b4: ldr             x0, [x0, #0x890]
    // 0xabe6b8: StoreField: r1->field_1b = r0
    //     0xabe6b8: stur            w0, [x1, #0x1b]
    // 0xabe6bc: r0 = Instance_VerticalDirection
    //     0xabe6bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabe6c0: ldr             x0, [x0, #0xa20]
    // 0xabe6c4: StoreField: r1->field_23 = r0
    //     0xabe6c4: stur            w0, [x1, #0x23]
    // 0xabe6c8: r0 = Instance_Clip
    //     0xabe6c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabe6cc: ldr             x0, [x0, #0x38]
    // 0xabe6d0: StoreField: r1->field_2b = r0
    //     0xabe6d0: stur            w0, [x1, #0x2b]
    // 0xabe6d4: StoreField: r1->field_2f = rZR
    //     0xabe6d4: stur            xzr, [x1, #0x2f]
    // 0xabe6d8: ldur            x0, [fp, #-0x10]
    // 0xabe6dc: StoreField: r1->field_b = r0
    //     0xabe6dc: stur            w0, [x1, #0xb]
    // 0xabe6e0: r0 = Padding()
    //     0xabe6e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabe6e4: r1 = Instance_EdgeInsets
    //     0xabe6e4: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xabe6e8: ldr             x1, [x1, #0xf30]
    // 0xabe6ec: StoreField: r0->field_f = r1
    //     0xabe6ec: stur            w1, [x0, #0xf]
    // 0xabe6f0: ldur            x1, [fp, #-8]
    // 0xabe6f4: StoreField: r0->field_b = r1
    //     0xabe6f4: stur            w1, [x0, #0xb]
    // 0xabe6f8: LeaveFrame
    //     0xabe6f8: mov             SP, fp
    //     0xabe6fc: ldp             fp, lr, [SP], #0x10
    // 0xabe700: ret
    //     0xabe700: ret             
    // 0xabe704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabe704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabe708: b               #0xabd120
    // 0xabe70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe714: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe718: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe71c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe71c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe720: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe724: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xabe72c, size: 0x18c
    // 0xabe72c: EnterFrame
    //     0xabe72c: stp             fp, lr, [SP, #-0x10]!
    //     0xabe730: mov             fp, SP
    // 0xabe734: AllocStack(0x18)
    //     0xabe734: sub             SP, SP, #0x18
    // 0xabe738: SetupParameters()
    //     0xabe738: ldr             x0, [fp, #0x20]
    //     0xabe73c: ldur            w1, [x0, #0x17]
    //     0xabe740: add             x1, x1, HEAP, lsl #32
    //     0xabe744: stur            x1, [fp, #-8]
    // 0xabe748: CheckStackOverflow
    //     0xabe748: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabe74c: cmp             SP, x16
    //     0xabe750: b.ls            #0xabe8a8
    // 0xabe754: r1 = 1
    //     0xabe754: movz            x1, #0x1
    // 0xabe758: r0 = AllocateContext()
    //     0xabe758: bl              #0x16f6108  ; AllocateContextStub
    // 0xabe75c: mov             x2, x0
    // 0xabe760: ldur            x0, [fp, #-8]
    // 0xabe764: stur            x2, [fp, #-0x18]
    // 0xabe768: StoreField: r2->field_b = r0
    //     0xabe768: stur            w0, [x2, #0xb]
    // 0xabe76c: ldr             x1, [fp, #0x10]
    // 0xabe770: StoreField: r2->field_f = r1
    //     0xabe770: stur            w1, [x2, #0xf]
    // 0xabe774: LoadField: r3 = r0->field_f
    //     0xabe774: ldur            w3, [x0, #0xf]
    // 0xabe778: DecompressPointer r3
    //     0xabe778: add             x3, x3, HEAP, lsl #32
    // 0xabe77c: stur            x3, [fp, #-0x10]
    // 0xabe780: LoadField: r0 = r3->field_b
    //     0xabe780: ldur            w0, [x3, #0xb]
    // 0xabe784: DecompressPointer r0
    //     0xabe784: add             x0, x0, HEAP, lsl #32
    // 0xabe788: cmp             w0, NULL
    // 0xabe78c: b.eq            #0xabe8b0
    // 0xabe790: LoadField: r4 = r0->field_b
    //     0xabe790: ldur            w4, [x0, #0xb]
    // 0xabe794: DecompressPointer r4
    //     0xabe794: add             x4, x4, HEAP, lsl #32
    // 0xabe798: LoadField: r0 = r4->field_b
    //     0xabe798: ldur            w0, [x4, #0xb]
    // 0xabe79c: DecompressPointer r0
    //     0xabe79c: add             x0, x0, HEAP, lsl #32
    // 0xabe7a0: cmp             w0, NULL
    // 0xabe7a4: b.ne            #0xabe7b0
    // 0xabe7a8: r0 = Null
    //     0xabe7a8: mov             x0, NULL
    // 0xabe7ac: b               #0xabe81c
    // 0xabe7b0: LoadField: r4 = r0->field_f
    //     0xabe7b0: ldur            w4, [x0, #0xf]
    // 0xabe7b4: DecompressPointer r4
    //     0xabe7b4: add             x4, x4, HEAP, lsl #32
    // 0xabe7b8: cmp             w4, NULL
    // 0xabe7bc: b.ne            #0xabe7c8
    // 0xabe7c0: r0 = Null
    //     0xabe7c0: mov             x0, NULL
    // 0xabe7c4: b               #0xabe81c
    // 0xabe7c8: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xabe7c8: ldur            w5, [x4, #0x17]
    // 0xabe7cc: DecompressPointer r5
    //     0xabe7cc: add             x5, x5, HEAP, lsl #32
    // 0xabe7d0: cmp             w5, NULL
    // 0xabe7d4: b.ne            #0xabe7e0
    // 0xabe7d8: r0 = Null
    //     0xabe7d8: mov             x0, NULL
    // 0xabe7dc: b               #0xabe81c
    // 0xabe7e0: LoadField: r0 = r5->field_b
    //     0xabe7e0: ldur            w0, [x5, #0xb]
    // 0xabe7e4: r4 = LoadInt32Instr(r1)
    //     0xabe7e4: sbfx            x4, x1, #1, #0x1f
    //     0xabe7e8: tbz             w1, #0, #0xabe7f0
    //     0xabe7ec: ldur            x4, [x1, #7]
    // 0xabe7f0: r1 = LoadInt32Instr(r0)
    //     0xabe7f0: sbfx            x1, x0, #1, #0x1f
    // 0xabe7f4: mov             x0, x1
    // 0xabe7f8: mov             x1, x4
    // 0xabe7fc: cmp             x1, x0
    // 0xabe800: b.hs            #0xabe8b4
    // 0xabe804: LoadField: r0 = r5->field_f
    //     0xabe804: ldur            w0, [x5, #0xf]
    // 0xabe808: DecompressPointer r0
    //     0xabe808: add             x0, x0, HEAP, lsl #32
    // 0xabe80c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xabe80c: add             x16, x0, x4, lsl #2
    //     0xabe810: ldur            w1, [x16, #0xf]
    // 0xabe814: DecompressPointer r1
    //     0xabe814: add             x1, x1, HEAP, lsl #32
    // 0xabe818: mov             x0, x1
    // 0xabe81c: cmp             w0, NULL
    // 0xabe820: b.ne            #0xabe830
    // 0xabe824: r0 = BEntities()
    //     0xabe824: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xabe828: mov             x2, x0
    // 0xabe82c: b               #0xabe834
    // 0xabe830: mov             x2, x0
    // 0xabe834: ldur            x1, [fp, #-0x10]
    // 0xabe838: ldr             x3, [fp, #0x18]
    // 0xabe83c: r0 = cosmeticThemeBagItem()
    //     0xabe83c: bl              #0xabe8b8  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::cosmeticThemeBagItem
    // 0xabe840: stur            x0, [fp, #-8]
    // 0xabe844: r0 = InkWell()
    //     0xabe844: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xabe848: mov             x3, x0
    // 0xabe84c: ldur            x0, [fp, #-8]
    // 0xabe850: stur            x3, [fp, #-0x10]
    // 0xabe854: StoreField: r3->field_b = r0
    //     0xabe854: stur            w0, [x3, #0xb]
    // 0xabe858: ldur            x2, [fp, #-0x18]
    // 0xabe85c: r1 = Function '<anonymous closure>':.
    //     0xabe85c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59338] AnonymousClosure: (0xabf244), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabe860: ldr             x1, [x1, #0x338]
    // 0xabe864: r0 = AllocateClosure()
    //     0xabe864: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe868: mov             x1, x0
    // 0xabe86c: ldur            x0, [fp, #-0x10]
    // 0xabe870: StoreField: r0->field_f = r1
    //     0xabe870: stur            w1, [x0, #0xf]
    // 0xabe874: r1 = true
    //     0xabe874: add             x1, NULL, #0x20  ; true
    // 0xabe878: StoreField: r0->field_43 = r1
    //     0xabe878: stur            w1, [x0, #0x43]
    // 0xabe87c: r2 = Instance_BoxShape
    //     0xabe87c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xabe880: ldr             x2, [x2, #0x80]
    // 0xabe884: StoreField: r0->field_47 = r2
    //     0xabe884: stur            w2, [x0, #0x47]
    // 0xabe888: StoreField: r0->field_6f = r1
    //     0xabe888: stur            w1, [x0, #0x6f]
    // 0xabe88c: r2 = false
    //     0xabe88c: add             x2, NULL, #0x30  ; false
    // 0xabe890: StoreField: r0->field_73 = r2
    //     0xabe890: stur            w2, [x0, #0x73]
    // 0xabe894: StoreField: r0->field_83 = r1
    //     0xabe894: stur            w1, [x0, #0x83]
    // 0xabe898: StoreField: r0->field_7b = r2
    //     0xabe898: stur            w2, [x0, #0x7b]
    // 0xabe89c: LeaveFrame
    //     0xabe89c: mov             SP, fp
    //     0xabe8a0: ldp             fp, lr, [SP], #0x10
    // 0xabe8a4: ret
    //     0xabe8a4: ret             
    // 0xabe8a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabe8a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabe8ac: b               #0xabe754
    // 0xabe8b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabe8b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabe8b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabe8b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeBagItem(/* No info */) {
    // ** addr: 0xabe8b8, size: 0x98c
    // 0xabe8b8: EnterFrame
    //     0xabe8b8: stp             fp, lr, [SP, #-0x10]!
    //     0xabe8bc: mov             fp, SP
    // 0xabe8c0: AllocStack(0x78)
    //     0xabe8c0: sub             SP, SP, #0x78
    // 0xabe8c4: SetupParameters(_BagDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xabe8c4: mov             x0, x1
    //     0xabe8c8: stur            x1, [fp, #-8]
    //     0xabe8cc: mov             x1, x3
    //     0xabe8d0: stur            x2, [fp, #-0x10]
    //     0xabe8d4: stur            x3, [fp, #-0x18]
    // 0xabe8d8: CheckStackOverflow
    //     0xabe8d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabe8dc: cmp             SP, x16
    //     0xabe8e0: b.ls            #0xabf230
    // 0xabe8e4: r0 = Radius()
    //     0xabe8e4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xabe8e8: d0 = 12.000000
    //     0xabe8e8: fmov            d0, #12.00000000
    // 0xabe8ec: stur            x0, [fp, #-0x20]
    // 0xabe8f0: StoreField: r0->field_7 = d0
    //     0xabe8f0: stur            d0, [x0, #7]
    // 0xabe8f4: StoreField: r0->field_f = d0
    //     0xabe8f4: stur            d0, [x0, #0xf]
    // 0xabe8f8: r0 = BorderRadius()
    //     0xabe8f8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xabe8fc: mov             x1, x0
    // 0xabe900: ldur            x0, [fp, #-0x20]
    // 0xabe904: stur            x1, [fp, #-0x28]
    // 0xabe908: StoreField: r1->field_7 = r0
    //     0xabe908: stur            w0, [x1, #7]
    // 0xabe90c: StoreField: r1->field_b = r0
    //     0xabe90c: stur            w0, [x1, #0xb]
    // 0xabe910: StoreField: r1->field_f = r0
    //     0xabe910: stur            w0, [x1, #0xf]
    // 0xabe914: StoreField: r1->field_13 = r0
    //     0xabe914: stur            w0, [x1, #0x13]
    // 0xabe918: r0 = RoundedRectangleBorder()
    //     0xabe918: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xabe91c: mov             x3, x0
    // 0xabe920: ldur            x0, [fp, #-0x28]
    // 0xabe924: stur            x3, [fp, #-0x30]
    // 0xabe928: StoreField: r3->field_b = r0
    //     0xabe928: stur            w0, [x3, #0xb]
    // 0xabe92c: r0 = Instance_BorderSide
    //     0xabe92c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xabe930: ldr             x0, [x0, #0xe20]
    // 0xabe934: StoreField: r3->field_7 = r0
    //     0xabe934: stur            w0, [x3, #7]
    // 0xabe938: ldur            x0, [fp, #-0x10]
    // 0xabe93c: LoadField: r1 = r0->field_13
    //     0xabe93c: ldur            w1, [x0, #0x13]
    // 0xabe940: DecompressPointer r1
    //     0xabe940: add             x1, x1, HEAP, lsl #32
    // 0xabe944: cmp             w1, NULL
    // 0xabe948: b.ne            #0xabe954
    // 0xabe94c: r4 = ""
    //     0xabe94c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabe950: b               #0xabe958
    // 0xabe954: mov             x4, x1
    // 0xabe958: stur            x4, [fp, #-0x20]
    // 0xabe95c: r1 = Function '<anonymous closure>':.
    //     0xabe95c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59360] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabe960: ldr             x1, [x1, #0x360]
    // 0xabe964: r2 = Null
    //     0xabe964: mov             x2, NULL
    // 0xabe968: r0 = AllocateClosure()
    //     0xabe968: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe96c: r1 = Function '<anonymous closure>':.
    //     0xabe96c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59368] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xabe970: ldr             x1, [x1, #0x368]
    // 0xabe974: r2 = Null
    //     0xabe974: mov             x2, NULL
    // 0xabe978: stur            x0, [fp, #-0x28]
    // 0xabe97c: r0 = AllocateClosure()
    //     0xabe97c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabe980: stur            x0, [fp, #-0x38]
    // 0xabe984: r0 = CachedNetworkImage()
    //     0xabe984: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xabe988: stur            x0, [fp, #-0x40]
    // 0xabe98c: r16 = 56.000000
    //     0xabe98c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabe990: ldr             x16, [x16, #0xb78]
    // 0xabe994: r30 = 56.000000
    //     0xabe994: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xabe998: ldr             lr, [lr, #0xb78]
    // 0xabe99c: stp             lr, x16, [SP, #0x18]
    // 0xabe9a0: r16 = Instance_BoxFit
    //     0xabe9a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xabe9a4: ldr             x16, [x16, #0x118]
    // 0xabe9a8: ldur            lr, [fp, #-0x28]
    // 0xabe9ac: stp             lr, x16, [SP, #8]
    // 0xabe9b0: ldur            x16, [fp, #-0x38]
    // 0xabe9b4: str             x16, [SP]
    // 0xabe9b8: mov             x1, x0
    // 0xabe9bc: ldur            x2, [fp, #-0x20]
    // 0xabe9c0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x3, progressIndicatorBuilder, 0x5, width, 0x2, null]
    //     0xabe9c0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c710] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x3, "progressIndicatorBuilder", 0x5, "width", 0x2, Null]
    //     0xabe9c4: ldr             x4, [x4, #0x710]
    // 0xabe9c8: r0 = CachedNetworkImage()
    //     0xabe9c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xabe9cc: r0 = Card()
    //     0xabe9cc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xabe9d0: mov             x2, x0
    // 0xabe9d4: r0 = 0.000000
    //     0xabe9d4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xabe9d8: stur            x2, [fp, #-0x28]
    // 0xabe9dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xabe9dc: stur            w0, [x2, #0x17]
    // 0xabe9e0: ldur            x1, [fp, #-0x30]
    // 0xabe9e4: StoreField: r2->field_1b = r1
    //     0xabe9e4: stur            w1, [x2, #0x1b]
    // 0xabe9e8: r3 = true
    //     0xabe9e8: add             x3, NULL, #0x20  ; true
    // 0xabe9ec: StoreField: r2->field_1f = r3
    //     0xabe9ec: stur            w3, [x2, #0x1f]
    // 0xabe9f0: r4 = Instance_EdgeInsets
    //     0xabe9f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xabe9f4: ldr             x4, [x4, #0x980]
    // 0xabe9f8: StoreField: r2->field_27 = r4
    //     0xabe9f8: stur            w4, [x2, #0x27]
    // 0xabe9fc: r1 = Instance_Clip
    //     0xabe9fc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xabea00: ldr             x1, [x1, #0xb50]
    // 0xabea04: StoreField: r2->field_23 = r1
    //     0xabea04: stur            w1, [x2, #0x23]
    // 0xabea08: ldur            x1, [fp, #-0x40]
    // 0xabea0c: StoreField: r2->field_2f = r1
    //     0xabea0c: stur            w1, [x2, #0x2f]
    // 0xabea10: StoreField: r2->field_2b = r3
    //     0xabea10: stur            w3, [x2, #0x2b]
    // 0xabea14: r5 = Instance__CardVariant
    //     0xabea14: add             x5, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xabea18: ldr             x5, [x5, #0xa68]
    // 0xabea1c: StoreField: r2->field_33 = r5
    //     0xabea1c: stur            w5, [x2, #0x33]
    // 0xabea20: ldur            x6, [fp, #-0x10]
    // 0xabea24: LoadField: r1 = r6->field_f
    //     0xabea24: ldur            w1, [x6, #0xf]
    // 0xabea28: DecompressPointer r1
    //     0xabea28: add             x1, x1, HEAP, lsl #32
    // 0xabea2c: cmp             w1, NULL
    // 0xabea30: b.ne            #0xabea3c
    // 0xabea34: r7 = ""
    //     0xabea34: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabea38: b               #0xabea40
    // 0xabea3c: mov             x7, x1
    // 0xabea40: ldur            x1, [fp, #-0x18]
    // 0xabea44: stur            x7, [fp, #-0x20]
    // 0xabea48: r0 = of()
    //     0xabea48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabea4c: LoadField: r1 = r0->field_87
    //     0xabea4c: ldur            w1, [x0, #0x87]
    // 0xabea50: DecompressPointer r1
    //     0xabea50: add             x1, x1, HEAP, lsl #32
    // 0xabea54: LoadField: r0 = r1->field_7
    //     0xabea54: ldur            w0, [x1, #7]
    // 0xabea58: DecompressPointer r0
    //     0xabea58: add             x0, x0, HEAP, lsl #32
    // 0xabea5c: r16 = 12.000000
    //     0xabea5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabea60: ldr             x16, [x16, #0x9e8]
    // 0xabea64: r30 = Instance_Color
    //     0xabea64: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabea68: stp             lr, x16, [SP]
    // 0xabea6c: mov             x1, x0
    // 0xabea70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabea70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabea74: ldr             x4, [x4, #0xaa0]
    // 0xabea78: r0 = copyWith()
    //     0xabea78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabea7c: stur            x0, [fp, #-0x30]
    // 0xabea80: r0 = Text()
    //     0xabea80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabea84: mov             x1, x0
    // 0xabea88: ldur            x0, [fp, #-0x20]
    // 0xabea8c: stur            x1, [fp, #-0x38]
    // 0xabea90: StoreField: r1->field_b = r0
    //     0xabea90: stur            w0, [x1, #0xb]
    // 0xabea94: ldur            x0, [fp, #-0x30]
    // 0xabea98: StoreField: r1->field_13 = r0
    //     0xabea98: stur            w0, [x1, #0x13]
    // 0xabea9c: r0 = 2
    //     0xabea9c: movz            x0, #0x2
    // 0xabeaa0: StoreField: r1->field_37 = r0
    //     0xabeaa0: stur            w0, [x1, #0x37]
    // 0xabeaa4: ldur            x2, [fp, #-0x10]
    // 0xabeaa8: LoadField: r0 = r2->field_57
    //     0xabeaa8: ldur            w0, [x2, #0x57]
    // 0xabeaac: DecompressPointer r0
    //     0xabeaac: add             x0, x0, HEAP, lsl #32
    // 0xabeab0: r3 = LoadClassIdInstr(r0)
    //     0xabeab0: ldur            x3, [x0, #-1]
    //     0xabeab4: ubfx            x3, x3, #0xc, #0x14
    // 0xabeab8: r16 = "size"
    //     0xabeab8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xabeabc: ldr             x16, [x16, #0x9c0]
    // 0xabeac0: stp             x16, x0, [SP]
    // 0xabeac4: mov             x0, x3
    // 0xabeac8: mov             lr, x0
    // 0xabeacc: ldr             lr, [x21, lr, lsl #3]
    // 0xabead0: blr             lr
    // 0xabead4: tbnz            w0, #4, #0xabeb2c
    // 0xabead8: ldur            x0, [fp, #-0x10]
    // 0xabeadc: r1 = Null
    //     0xabeadc: mov             x1, NULL
    // 0xabeae0: r2 = 8
    //     0xabeae0: movz            x2, #0x8
    // 0xabeae4: r0 = AllocateArray()
    //     0xabeae4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabeae8: r16 = "Size: "
    //     0xabeae8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xabeaec: ldr             x16, [x16, #0xf00]
    // 0xabeaf0: StoreField: r0->field_f = r16
    //     0xabeaf0: stur            w16, [x0, #0xf]
    // 0xabeaf4: ldur            x1, [fp, #-0x10]
    // 0xabeaf8: LoadField: r2 = r1->field_2f
    //     0xabeaf8: ldur            w2, [x1, #0x2f]
    // 0xabeafc: DecompressPointer r2
    //     0xabeafc: add             x2, x2, HEAP, lsl #32
    // 0xabeb00: StoreField: r0->field_13 = r2
    //     0xabeb00: stur            w2, [x0, #0x13]
    // 0xabeb04: r16 = " / Qty: "
    //     0xabeb04: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xabeb08: ldr             x16, [x16, #0x760]
    // 0xabeb0c: ArrayStore: r0[0] = r16  ; List_4
    //     0xabeb0c: stur            w16, [x0, #0x17]
    // 0xabeb10: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xabeb10: ldur            w2, [x1, #0x17]
    // 0xabeb14: DecompressPointer r2
    //     0xabeb14: add             x2, x2, HEAP, lsl #32
    // 0xabeb18: StoreField: r0->field_1b = r2
    //     0xabeb18: stur            w2, [x0, #0x1b]
    // 0xabeb1c: str             x0, [SP]
    // 0xabeb20: r0 = _interpolate()
    //     0xabeb20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xabeb24: mov             x3, x0
    // 0xabeb28: b               #0xabeb7c
    // 0xabeb2c: ldur            x0, [fp, #-0x10]
    // 0xabeb30: r1 = Null
    //     0xabeb30: mov             x1, NULL
    // 0xabeb34: r2 = 8
    //     0xabeb34: movz            x2, #0x8
    // 0xabeb38: r0 = AllocateArray()
    //     0xabeb38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabeb3c: r16 = "Variant: "
    //     0xabeb3c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xabeb40: ldr             x16, [x16, #0xf08]
    // 0xabeb44: StoreField: r0->field_f = r16
    //     0xabeb44: stur            w16, [x0, #0xf]
    // 0xabeb48: ldur            x1, [fp, #-0x10]
    // 0xabeb4c: LoadField: r2 = r1->field_2f
    //     0xabeb4c: ldur            w2, [x1, #0x2f]
    // 0xabeb50: DecompressPointer r2
    //     0xabeb50: add             x2, x2, HEAP, lsl #32
    // 0xabeb54: StoreField: r0->field_13 = r2
    //     0xabeb54: stur            w2, [x0, #0x13]
    // 0xabeb58: r16 = " / Qty: "
    //     0xabeb58: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xabeb5c: ldr             x16, [x16, #0x760]
    // 0xabeb60: ArrayStore: r0[0] = r16  ; List_4
    //     0xabeb60: stur            w16, [x0, #0x17]
    // 0xabeb64: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xabeb64: ldur            w2, [x1, #0x17]
    // 0xabeb68: DecompressPointer r2
    //     0xabeb68: add             x2, x2, HEAP, lsl #32
    // 0xabeb6c: StoreField: r0->field_1b = r2
    //     0xabeb6c: stur            w2, [x0, #0x1b]
    // 0xabeb70: str             x0, [SP]
    // 0xabeb74: r0 = _interpolate()
    //     0xabeb74: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xabeb78: mov             x3, x0
    // 0xabeb7c: ldur            x2, [fp, #-8]
    // 0xabeb80: ldur            x0, [fp, #-0x10]
    // 0xabeb84: ldur            x1, [fp, #-0x18]
    // 0xabeb88: stur            x3, [fp, #-0x20]
    // 0xabeb8c: r0 = of()
    //     0xabeb8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabeb90: LoadField: r1 = r0->field_87
    //     0xabeb90: ldur            w1, [x0, #0x87]
    // 0xabeb94: DecompressPointer r1
    //     0xabeb94: add             x1, x1, HEAP, lsl #32
    // 0xabeb98: LoadField: r0 = r1->field_2b
    //     0xabeb98: ldur            w0, [x1, #0x2b]
    // 0xabeb9c: DecompressPointer r0
    //     0xabeb9c: add             x0, x0, HEAP, lsl #32
    // 0xabeba0: stur            x0, [fp, #-0x30]
    // 0xabeba4: r1 = Instance_Color
    //     0xabeba4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabeba8: d0 = 0.700000
    //     0xabeba8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xabebac: ldr             d0, [x17, #0xf48]
    // 0xabebb0: r0 = withOpacity()
    //     0xabebb0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xabebb4: r16 = 12.000000
    //     0xabebb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabebb8: ldr             x16, [x16, #0x9e8]
    // 0xabebbc: stp             x0, x16, [SP]
    // 0xabebc0: ldur            x1, [fp, #-0x30]
    // 0xabebc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabebc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabebc8: ldr             x4, [x4, #0xaa0]
    // 0xabebcc: r0 = copyWith()
    //     0xabebcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabebd0: stur            x0, [fp, #-0x30]
    // 0xabebd4: r0 = Text()
    //     0xabebd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xabebd8: mov             x3, x0
    // 0xabebdc: ldur            x0, [fp, #-0x20]
    // 0xabebe0: stur            x3, [fp, #-0x40]
    // 0xabebe4: StoreField: r3->field_b = r0
    //     0xabebe4: stur            w0, [x3, #0xb]
    // 0xabebe8: ldur            x0, [fp, #-0x30]
    // 0xabebec: StoreField: r3->field_13 = r0
    //     0xabebec: stur            w0, [x3, #0x13]
    // 0xabebf0: ldur            x0, [fp, #-0x10]
    // 0xabebf4: LoadField: r4 = r0->field_2b
    //     0xabebf4: ldur            w4, [x0, #0x2b]
    // 0xabebf8: DecompressPointer r4
    //     0xabebf8: add             x4, x4, HEAP, lsl #32
    // 0xabebfc: stur            x4, [fp, #-0x20]
    // 0xabec00: r1 = Null
    //     0xabec00: mov             x1, NULL
    // 0xabec04: r2 = 4
    //     0xabec04: movz            x2, #0x4
    // 0xabec08: r0 = AllocateArray()
    //     0xabec08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabec0c: mov             x1, x0
    // 0xabec10: ldur            x0, [fp, #-0x20]
    // 0xabec14: StoreField: r1->field_f = r0
    //     0xabec14: stur            w0, [x1, #0xf]
    // 0xabec18: r16 = " "
    //     0xabec18: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xabec1c: StoreField: r1->field_13 = r16
    //     0xabec1c: stur            w16, [x1, #0x13]
    // 0xabec20: str             x1, [SP]
    // 0xabec24: r0 = _interpolate()
    //     0xabec24: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xabec28: ldur            x1, [fp, #-0x18]
    // 0xabec2c: stur            x0, [fp, #-0x20]
    // 0xabec30: r0 = of()
    //     0xabec30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabec34: LoadField: r1 = r0->field_87
    //     0xabec34: ldur            w1, [x0, #0x87]
    // 0xabec38: DecompressPointer r1
    //     0xabec38: add             x1, x1, HEAP, lsl #32
    // 0xabec3c: LoadField: r0 = r1->field_7
    //     0xabec3c: ldur            w0, [x1, #7]
    // 0xabec40: DecompressPointer r0
    //     0xabec40: add             x0, x0, HEAP, lsl #32
    // 0xabec44: r16 = 12.000000
    //     0xabec44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xabec48: ldr             x16, [x16, #0x9e8]
    // 0xabec4c: r30 = Instance_Color
    //     0xabec4c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabec50: stp             lr, x16, [SP]
    // 0xabec54: mov             x1, x0
    // 0xabec58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabec58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabec5c: ldr             x4, [x4, #0xaa0]
    // 0xabec60: r0 = copyWith()
    //     0xabec60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabec64: stur            x0, [fp, #-0x30]
    // 0xabec68: r0 = TextSpan()
    //     0xabec68: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xabec6c: mov             x3, x0
    // 0xabec70: ldur            x0, [fp, #-0x20]
    // 0xabec74: stur            x3, [fp, #-0x48]
    // 0xabec78: StoreField: r3->field_b = r0
    //     0xabec78: stur            w0, [x3, #0xb]
    // 0xabec7c: r0 = Instance__DeferringMouseCursor
    //     0xabec7c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xabec80: ArrayStore: r3[0] = r0  ; List_4
    //     0xabec80: stur            w0, [x3, #0x17]
    // 0xabec84: ldur            x1, [fp, #-0x30]
    // 0xabec88: StoreField: r3->field_7 = r1
    //     0xabec88: stur            w1, [x3, #7]
    // 0xabec8c: r1 = Null
    //     0xabec8c: mov             x1, NULL
    // 0xabec90: r2 = 4
    //     0xabec90: movz            x2, #0x4
    // 0xabec94: r0 = AllocateArray()
    //     0xabec94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabec98: r16 = "  "
    //     0xabec98: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "  "
    // 0xabec9c: StoreField: r0->field_f = r16
    //     0xabec9c: stur            w16, [x0, #0xf]
    // 0xabeca0: ldur            x1, [fp, #-8]
    // 0xabeca4: LoadField: r2 = r1->field_b
    //     0xabeca4: ldur            w2, [x1, #0xb]
    // 0xabeca8: DecompressPointer r2
    //     0xabeca8: add             x2, x2, HEAP, lsl #32
    // 0xabecac: cmp             w2, NULL
    // 0xabecb0: b.eq            #0xabf238
    // 0xabecb4: ldur            x2, [fp, #-0x10]
    // 0xabecb8: LoadField: r3 = r2->field_3f
    //     0xabecb8: ldur            w3, [x2, #0x3f]
    // 0xabecbc: DecompressPointer r3
    //     0xabecbc: add             x3, x3, HEAP, lsl #32
    // 0xabecc0: cmp             w3, NULL
    // 0xabecc4: b.ne            #0xabecd0
    // 0xabecc8: r3 = Null
    //     0xabecc8: mov             x3, NULL
    // 0xabeccc: b               #0xabece4
    // 0xabecd0: LoadField: r4 = r3->field_b
    //     0xabecd0: ldur            w4, [x3, #0xb]
    // 0xabecd4: cbnz            w4, #0xabece0
    // 0xabecd8: r3 = false
    //     0xabecd8: add             x3, NULL, #0x30  ; false
    // 0xabecdc: b               #0xabece4
    // 0xabece0: r3 = true
    //     0xabece0: add             x3, NULL, #0x20  ; true
    // 0xabece4: cmp             w3, NULL
    // 0xabece8: b.eq            #0xabecfc
    // 0xabecec: tbnz            w3, #4, #0xabecfc
    // 0xabecf0: r7 = "Customised"
    //     0xabecf0: add             x7, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xabecf4: ldr             x7, [x7, #0xd88]
    // 0xabecf8: b               #0xabed00
    // 0xabecfc: r7 = ""
    //     0xabecfc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabed00: ldur            x6, [fp, #-0x28]
    // 0xabed04: ldur            x5, [fp, #-0x38]
    // 0xabed08: ldur            x4, [fp, #-0x40]
    // 0xabed0c: ldur            x3, [fp, #-0x48]
    // 0xabed10: StoreField: r0->field_13 = r7
    //     0xabed10: stur            w7, [x0, #0x13]
    // 0xabed14: str             x0, [SP]
    // 0xabed18: r0 = _interpolate()
    //     0xabed18: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xabed1c: ldur            x1, [fp, #-0x18]
    // 0xabed20: stur            x0, [fp, #-0x20]
    // 0xabed24: r0 = of()
    //     0xabed24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabed28: LoadField: r1 = r0->field_87
    //     0xabed28: ldur            w1, [x0, #0x87]
    // 0xabed2c: DecompressPointer r1
    //     0xabed2c: add             x1, x1, HEAP, lsl #32
    // 0xabed30: LoadField: r0 = r1->field_7
    //     0xabed30: ldur            w0, [x1, #7]
    // 0xabed34: DecompressPointer r0
    //     0xabed34: add             x0, x0, HEAP, lsl #32
    // 0xabed38: r16 = 14.000000
    //     0xabed38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xabed3c: ldr             x16, [x16, #0x1d8]
    // 0xabed40: r30 = Instance_Color
    //     0xabed40: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xabed44: stp             lr, x16, [SP]
    // 0xabed48: mov             x1, x0
    // 0xabed4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xabed4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xabed50: ldr             x4, [x4, #0xaa0]
    // 0xabed54: r0 = copyWith()
    //     0xabed54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xabed58: stur            x0, [fp, #-0x30]
    // 0xabed5c: r0 = TextSpan()
    //     0xabed5c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xabed60: mov             x3, x0
    // 0xabed64: ldur            x0, [fp, #-0x20]
    // 0xabed68: stur            x3, [fp, #-0x50]
    // 0xabed6c: StoreField: r3->field_b = r0
    //     0xabed6c: stur            w0, [x3, #0xb]
    // 0xabed70: r0 = Instance__DeferringMouseCursor
    //     0xabed70: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xabed74: ArrayStore: r3[0] = r0  ; List_4
    //     0xabed74: stur            w0, [x3, #0x17]
    // 0xabed78: ldur            x1, [fp, #-0x30]
    // 0xabed7c: StoreField: r3->field_7 = r1
    //     0xabed7c: stur            w1, [x3, #7]
    // 0xabed80: r1 = Null
    //     0xabed80: mov             x1, NULL
    // 0xabed84: r2 = 4
    //     0xabed84: movz            x2, #0x4
    // 0xabed88: r0 = AllocateArray()
    //     0xabed88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabed8c: mov             x2, x0
    // 0xabed90: ldur            x0, [fp, #-0x48]
    // 0xabed94: stur            x2, [fp, #-0x20]
    // 0xabed98: StoreField: r2->field_f = r0
    //     0xabed98: stur            w0, [x2, #0xf]
    // 0xabed9c: ldur            x0, [fp, #-0x50]
    // 0xabeda0: StoreField: r2->field_13 = r0
    //     0xabeda0: stur            w0, [x2, #0x13]
    // 0xabeda4: r1 = <TextSpan>
    //     0xabeda4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xabeda8: ldr             x1, [x1, #0x940]
    // 0xabedac: r0 = AllocateGrowableArray()
    //     0xabedac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabedb0: mov             x1, x0
    // 0xabedb4: ldur            x0, [fp, #-0x20]
    // 0xabedb8: stur            x1, [fp, #-0x30]
    // 0xabedbc: StoreField: r1->field_f = r0
    //     0xabedbc: stur            w0, [x1, #0xf]
    // 0xabedc0: r2 = 4
    //     0xabedc0: movz            x2, #0x4
    // 0xabedc4: StoreField: r1->field_b = r2
    //     0xabedc4: stur            w2, [x1, #0xb]
    // 0xabedc8: r0 = TextSpan()
    //     0xabedc8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xabedcc: mov             x1, x0
    // 0xabedd0: ldur            x0, [fp, #-0x30]
    // 0xabedd4: stur            x1, [fp, #-0x20]
    // 0xabedd8: StoreField: r1->field_f = r0
    //     0xabedd8: stur            w0, [x1, #0xf]
    // 0xabeddc: r0 = Instance__DeferringMouseCursor
    //     0xabeddc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xabede0: ArrayStore: r1[0] = r0  ; List_4
    //     0xabede0: stur            w0, [x1, #0x17]
    // 0xabede4: r0 = RichText()
    //     0xabede4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xabede8: mov             x1, x0
    // 0xabedec: ldur            x2, [fp, #-0x20]
    // 0xabedf0: stur            x0, [fp, #-0x20]
    // 0xabedf4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xabedf4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xabedf8: r0 = RichText()
    //     0xabedf8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xabedfc: r1 = Null
    //     0xabedfc: mov             x1, NULL
    // 0xabee00: r2 = 6
    //     0xabee00: movz            x2, #0x6
    // 0xabee04: r0 = AllocateArray()
    //     0xabee04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabee08: mov             x2, x0
    // 0xabee0c: ldur            x0, [fp, #-0x38]
    // 0xabee10: stur            x2, [fp, #-0x30]
    // 0xabee14: StoreField: r2->field_f = r0
    //     0xabee14: stur            w0, [x2, #0xf]
    // 0xabee18: ldur            x0, [fp, #-0x40]
    // 0xabee1c: StoreField: r2->field_13 = r0
    //     0xabee1c: stur            w0, [x2, #0x13]
    // 0xabee20: ldur            x0, [fp, #-0x20]
    // 0xabee24: ArrayStore: r2[0] = r0  ; List_4
    //     0xabee24: stur            w0, [x2, #0x17]
    // 0xabee28: r1 = <Widget>
    //     0xabee28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabee2c: r0 = AllocateGrowableArray()
    //     0xabee2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabee30: mov             x1, x0
    // 0xabee34: ldur            x0, [fp, #-0x30]
    // 0xabee38: stur            x1, [fp, #-0x20]
    // 0xabee3c: StoreField: r1->field_f = r0
    //     0xabee3c: stur            w0, [x1, #0xf]
    // 0xabee40: r2 = 6
    //     0xabee40: movz            x2, #0x6
    // 0xabee44: StoreField: r1->field_b = r2
    //     0xabee44: stur            w2, [x1, #0xb]
    // 0xabee48: r0 = Column()
    //     0xabee48: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabee4c: mov             x1, x0
    // 0xabee50: r0 = Instance_Axis
    //     0xabee50: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabee54: stur            x1, [fp, #-0x30]
    // 0xabee58: StoreField: r1->field_f = r0
    //     0xabee58: stur            w0, [x1, #0xf]
    // 0xabee5c: r2 = Instance_MainAxisAlignment
    //     0xabee5c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xabee60: ldr             x2, [x2, #0xa8]
    // 0xabee64: StoreField: r1->field_13 = r2
    //     0xabee64: stur            w2, [x1, #0x13]
    // 0xabee68: r2 = Instance_MainAxisSize
    //     0xabee68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabee6c: ldr             x2, [x2, #0xa10]
    // 0xabee70: ArrayStore: r1[0] = r2  ; List_4
    //     0xabee70: stur            w2, [x1, #0x17]
    // 0xabee74: r3 = Instance_CrossAxisAlignment
    //     0xabee74: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabee78: ldr             x3, [x3, #0x890]
    // 0xabee7c: StoreField: r1->field_1b = r3
    //     0xabee7c: stur            w3, [x1, #0x1b]
    // 0xabee80: r4 = Instance_VerticalDirection
    //     0xabee80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabee84: ldr             x4, [x4, #0xa20]
    // 0xabee88: StoreField: r1->field_23 = r4
    //     0xabee88: stur            w4, [x1, #0x23]
    // 0xabee8c: r5 = Instance_Clip
    //     0xabee8c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabee90: ldr             x5, [x5, #0x38]
    // 0xabee94: StoreField: r1->field_2b = r5
    //     0xabee94: stur            w5, [x1, #0x2b]
    // 0xabee98: StoreField: r1->field_2f = rZR
    //     0xabee98: stur            xzr, [x1, #0x2f]
    // 0xabee9c: ldur            x6, [fp, #-0x20]
    // 0xabeea0: StoreField: r1->field_b = r6
    //     0xabeea0: stur            w6, [x1, #0xb]
    // 0xabeea4: r0 = Padding()
    //     0xabeea4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabeea8: mov             x2, x0
    // 0xabeeac: r0 = Instance_EdgeInsets
    //     0xabeeac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xabeeb0: ldr             x0, [x0, #0x980]
    // 0xabeeb4: stur            x2, [fp, #-0x20]
    // 0xabeeb8: StoreField: r2->field_f = r0
    //     0xabeeb8: stur            w0, [x2, #0xf]
    // 0xabeebc: ldur            x0, [fp, #-0x30]
    // 0xabeec0: StoreField: r2->field_b = r0
    //     0xabeec0: stur            w0, [x2, #0xb]
    // 0xabeec4: r1 = <FlexParentData>
    //     0xabeec4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xabeec8: ldr             x1, [x1, #0xe00]
    // 0xabeecc: r0 = Expanded()
    //     0xabeecc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xabeed0: mov             x2, x0
    // 0xabeed4: r0 = 1
    //     0xabeed4: movz            x0, #0x1
    // 0xabeed8: stur            x2, [fp, #-0x30]
    // 0xabeedc: StoreField: r2->field_13 = r0
    //     0xabeedc: stur            x0, [x2, #0x13]
    // 0xabeee0: r1 = Instance_FlexFit
    //     0xabeee0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xabeee4: ldr             x1, [x1, #0xe08]
    // 0xabeee8: StoreField: r2->field_1b = r1
    //     0xabeee8: stur            w1, [x2, #0x1b]
    // 0xabeeec: ldur            x1, [fp, #-0x20]
    // 0xabeef0: StoreField: r2->field_b = r1
    //     0xabeef0: stur            w1, [x2, #0xb]
    // 0xabeef4: ldur            x1, [fp, #-0x18]
    // 0xabeef8: r0 = of()
    //     0xabeef8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xabeefc: LoadField: r1 = r0->field_5b
    //     0xabeefc: ldur            w1, [x0, #0x5b]
    // 0xabef00: DecompressPointer r1
    //     0xabef00: add             x1, x1, HEAP, lsl #32
    // 0xabef04: stur            x1, [fp, #-0x18]
    // 0xabef08: r0 = ColorFilter()
    //     0xabef08: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xabef0c: mov             x1, x0
    // 0xabef10: ldur            x0, [fp, #-0x18]
    // 0xabef14: stur            x1, [fp, #-0x20]
    // 0xabef18: StoreField: r1->field_7 = r0
    //     0xabef18: stur            w0, [x1, #7]
    // 0xabef1c: r0 = Instance_BlendMode
    //     0xabef1c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xabef20: ldr             x0, [x0, #0xb30]
    // 0xabef24: StoreField: r1->field_b = r0
    //     0xabef24: stur            w0, [x1, #0xb]
    // 0xabef28: r0 = 1
    //     0xabef28: movz            x0, #0x1
    // 0xabef2c: StoreField: r1->field_13 = r0
    //     0xabef2c: stur            x0, [x1, #0x13]
    // 0xabef30: r0 = SvgPicture()
    //     0xabef30: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xabef34: stur            x0, [fp, #-0x18]
    // 0xabef38: ldur            x16, [fp, #-0x20]
    // 0xabef3c: str             x16, [SP]
    // 0xabef40: mov             x1, x0
    // 0xabef44: r2 = "assets/images/outline_arrow.svg"
    //     0xabef44: add             x2, PP, #0x34, lsl #12  ; [pp+0x34150] "assets/images/outline_arrow.svg"
    //     0xabef48: ldr             x2, [x2, #0x150]
    // 0xabef4c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xabef4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xabef50: ldr             x4, [x4, #0xa38]
    // 0xabef54: r0 = SvgPicture.asset()
    //     0xabef54: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xabef58: r0 = Align()
    //     0xabef58: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xabef5c: mov             x1, x0
    // 0xabef60: r0 = Instance_Alignment
    //     0xabef60: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xabef64: ldr             x0, [x0, #0xa78]
    // 0xabef68: stur            x1, [fp, #-0x20]
    // 0xabef6c: StoreField: r1->field_f = r0
    //     0xabef6c: stur            w0, [x1, #0xf]
    // 0xabef70: ldur            x0, [fp, #-0x18]
    // 0xabef74: StoreField: r1->field_b = r0
    //     0xabef74: stur            w0, [x1, #0xb]
    // 0xabef78: r0 = Padding()
    //     0xabef78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xabef7c: mov             x3, x0
    // 0xabef80: r0 = Instance_EdgeInsets
    //     0xabef80: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xabef84: ldr             x0, [x0, #0x268]
    // 0xabef88: stur            x3, [fp, #-0x18]
    // 0xabef8c: StoreField: r3->field_f = r0
    //     0xabef8c: stur            w0, [x3, #0xf]
    // 0xabef90: ldur            x0, [fp, #-0x20]
    // 0xabef94: StoreField: r3->field_b = r0
    //     0xabef94: stur            w0, [x3, #0xb]
    // 0xabef98: r1 = Null
    //     0xabef98: mov             x1, NULL
    // 0xabef9c: r2 = 6
    //     0xabef9c: movz            x2, #0x6
    // 0xabefa0: r0 = AllocateArray()
    //     0xabefa0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabefa4: mov             x2, x0
    // 0xabefa8: ldur            x0, [fp, #-0x28]
    // 0xabefac: stur            x2, [fp, #-0x20]
    // 0xabefb0: StoreField: r2->field_f = r0
    //     0xabefb0: stur            w0, [x2, #0xf]
    // 0xabefb4: ldur            x0, [fp, #-0x30]
    // 0xabefb8: StoreField: r2->field_13 = r0
    //     0xabefb8: stur            w0, [x2, #0x13]
    // 0xabefbc: ldur            x0, [fp, #-0x18]
    // 0xabefc0: ArrayStore: r2[0] = r0  ; List_4
    //     0xabefc0: stur            w0, [x2, #0x17]
    // 0xabefc4: r1 = <Widget>
    //     0xabefc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabefc8: r0 = AllocateGrowableArray()
    //     0xabefc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabefcc: mov             x1, x0
    // 0xabefd0: ldur            x0, [fp, #-0x20]
    // 0xabefd4: stur            x1, [fp, #-0x18]
    // 0xabefd8: StoreField: r1->field_f = r0
    //     0xabefd8: stur            w0, [x1, #0xf]
    // 0xabefdc: r0 = 6
    //     0xabefdc: movz            x0, #0x6
    // 0xabefe0: StoreField: r1->field_b = r0
    //     0xabefe0: stur            w0, [x1, #0xb]
    // 0xabefe4: r0 = Row()
    //     0xabefe4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xabefe8: mov             x1, x0
    // 0xabefec: r0 = Instance_Axis
    //     0xabefec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xabeff0: stur            x1, [fp, #-0x20]
    // 0xabeff4: StoreField: r1->field_f = r0
    //     0xabeff4: stur            w0, [x1, #0xf]
    // 0xabeff8: r0 = Instance_MainAxisAlignment
    //     0xabeff8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabeffc: ldr             x0, [x0, #0xa08]
    // 0xabf000: StoreField: r1->field_13 = r0
    //     0xabf000: stur            w0, [x1, #0x13]
    // 0xabf004: r2 = Instance_MainAxisSize
    //     0xabf004: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabf008: ldr             x2, [x2, #0xa10]
    // 0xabf00c: ArrayStore: r1[0] = r2  ; List_4
    //     0xabf00c: stur            w2, [x1, #0x17]
    // 0xabf010: r3 = Instance_CrossAxisAlignment
    //     0xabf010: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xabf014: ldr             x3, [x3, #0x890]
    // 0xabf018: StoreField: r1->field_1b = r3
    //     0xabf018: stur            w3, [x1, #0x1b]
    // 0xabf01c: r3 = Instance_VerticalDirection
    //     0xabf01c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabf020: ldr             x3, [x3, #0xa20]
    // 0xabf024: StoreField: r1->field_23 = r3
    //     0xabf024: stur            w3, [x1, #0x23]
    // 0xabf028: r4 = Instance_Clip
    //     0xabf028: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabf02c: ldr             x4, [x4, #0x38]
    // 0xabf030: StoreField: r1->field_2b = r4
    //     0xabf030: stur            w4, [x1, #0x2b]
    // 0xabf034: StoreField: r1->field_2f = rZR
    //     0xabf034: stur            xzr, [x1, #0x2f]
    // 0xabf038: ldur            x5, [fp, #-0x18]
    // 0xabf03c: StoreField: r1->field_b = r5
    //     0xabf03c: stur            w5, [x1, #0xb]
    // 0xabf040: r0 = IntrinsicHeight()
    //     0xabf040: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xabf044: mov             x1, x0
    // 0xabf048: ldur            x0, [fp, #-0x20]
    // 0xabf04c: stur            x1, [fp, #-0x28]
    // 0xabf050: StoreField: r1->field_b = r0
    //     0xabf050: stur            w0, [x1, #0xb]
    // 0xabf054: ldur            x0, [fp, #-0x10]
    // 0xabf058: LoadField: r2 = r0->field_3f
    //     0xabf058: ldur            w2, [x0, #0x3f]
    // 0xabf05c: DecompressPointer r2
    //     0xabf05c: add             x2, x2, HEAP, lsl #32
    // 0xabf060: stur            x2, [fp, #-0x20]
    // 0xabf064: cmp             w2, NULL
    // 0xabf068: b.ne            #0xabf074
    // 0xabf06c: r3 = Null
    //     0xabf06c: mov             x3, NULL
    // 0xabf070: b               #0xabf08c
    // 0xabf074: LoadField: r3 = r2->field_b
    //     0xabf074: ldur            w3, [x2, #0xb]
    // 0xabf078: cbnz            w3, #0xabf084
    // 0xabf07c: r4 = false
    //     0xabf07c: add             x4, NULL, #0x30  ; false
    // 0xabf080: b               #0xabf088
    // 0xabf084: r4 = true
    //     0xabf084: add             x4, NULL, #0x20  ; true
    // 0xabf088: mov             x3, x4
    // 0xabf08c: cmp             w3, NULL
    // 0xabf090: b.eq            #0xabf0a4
    // 0xabf094: tbnz            w3, #4, #0xabf0a4
    // 0xabf098: ldur            x3, [fp, #-8]
    // 0xabf09c: r4 = true
    //     0xabf09c: add             x4, NULL, #0x20  ; true
    // 0xabf0a0: b               #0xabf0bc
    // 0xabf0a4: ldur            x3, [fp, #-8]
    // 0xabf0a8: LoadField: r4 = r3->field_b
    //     0xabf0a8: ldur            w4, [x3, #0xb]
    // 0xabf0ac: DecompressPointer r4
    //     0xabf0ac: add             x4, x4, HEAP, lsl #32
    // 0xabf0b0: cmp             w4, NULL
    // 0xabf0b4: b.eq            #0xabf23c
    // 0xabf0b8: r4 = false
    //     0xabf0b8: add             x4, NULL, #0x30  ; false
    // 0xabf0bc: stur            x4, [fp, #-0x18]
    // 0xabf0c0: LoadField: r5 = r3->field_b
    //     0xabf0c0: ldur            w5, [x3, #0xb]
    // 0xabf0c4: DecompressPointer r5
    //     0xabf0c4: add             x5, x5, HEAP, lsl #32
    // 0xabf0c8: cmp             w5, NULL
    // 0xabf0cc: b.eq            #0xabf240
    // 0xabf0d0: LoadField: r3 = r0->field_43
    //     0xabf0d0: ldur            w3, [x0, #0x43]
    // 0xabf0d4: DecompressPointer r3
    //     0xabf0d4: add             x3, x3, HEAP, lsl #32
    // 0xabf0d8: cbz             w3, #0xabf0ec
    // 0xabf0dc: LoadField: r3 = r0->field_47
    //     0xabf0dc: ldur            w3, [x0, #0x47]
    // 0xabf0e0: DecompressPointer r3
    //     0xabf0e0: add             x3, x3, HEAP, lsl #32
    // 0xabf0e4: mov             x0, x3
    // 0xabf0e8: b               #0xabf0f8
    // 0xabf0ec: LoadField: r3 = r0->field_47
    //     0xabf0ec: ldur            w3, [x0, #0x47]
    // 0xabf0f0: DecompressPointer r3
    //     0xabf0f0: add             x3, x3, HEAP, lsl #32
    // 0xabf0f4: mov             x0, x3
    // 0xabf0f8: stur            x0, [fp, #-8]
    // 0xabf0fc: r0 = CustomisedStrip()
    //     0xabf0fc: bl              #0xa078f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xabf100: mov             x1, x0
    // 0xabf104: ldur            x0, [fp, #-0x20]
    // 0xabf108: stur            x1, [fp, #-0x10]
    // 0xabf10c: StoreField: r1->field_b = r0
    //     0xabf10c: stur            w0, [x1, #0xb]
    // 0xabf110: ldur            x0, [fp, #-8]
    // 0xabf114: StoreField: r1->field_13 = r0
    //     0xabf114: stur            w0, [x1, #0x13]
    // 0xabf118: r0 = Visibility()
    //     0xabf118: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xabf11c: mov             x3, x0
    // 0xabf120: ldur            x0, [fp, #-0x10]
    // 0xabf124: stur            x3, [fp, #-8]
    // 0xabf128: StoreField: r3->field_b = r0
    //     0xabf128: stur            w0, [x3, #0xb]
    // 0xabf12c: r0 = Instance_SizedBox
    //     0xabf12c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xabf130: StoreField: r3->field_f = r0
    //     0xabf130: stur            w0, [x3, #0xf]
    // 0xabf134: ldur            x0, [fp, #-0x18]
    // 0xabf138: StoreField: r3->field_13 = r0
    //     0xabf138: stur            w0, [x3, #0x13]
    // 0xabf13c: r0 = false
    //     0xabf13c: add             x0, NULL, #0x30  ; false
    // 0xabf140: ArrayStore: r3[0] = r0  ; List_4
    //     0xabf140: stur            w0, [x3, #0x17]
    // 0xabf144: StoreField: r3->field_1b = r0
    //     0xabf144: stur            w0, [x3, #0x1b]
    // 0xabf148: StoreField: r3->field_1f = r0
    //     0xabf148: stur            w0, [x3, #0x1f]
    // 0xabf14c: StoreField: r3->field_23 = r0
    //     0xabf14c: stur            w0, [x3, #0x23]
    // 0xabf150: StoreField: r3->field_27 = r0
    //     0xabf150: stur            w0, [x3, #0x27]
    // 0xabf154: StoreField: r3->field_2b = r0
    //     0xabf154: stur            w0, [x3, #0x2b]
    // 0xabf158: r1 = Null
    //     0xabf158: mov             x1, NULL
    // 0xabf15c: r2 = 4
    //     0xabf15c: movz            x2, #0x4
    // 0xabf160: r0 = AllocateArray()
    //     0xabf160: bl              #0x16f7198  ; AllocateArrayStub
    // 0xabf164: mov             x2, x0
    // 0xabf168: ldur            x0, [fp, #-0x28]
    // 0xabf16c: stur            x2, [fp, #-0x10]
    // 0xabf170: StoreField: r2->field_f = r0
    //     0xabf170: stur            w0, [x2, #0xf]
    // 0xabf174: ldur            x0, [fp, #-8]
    // 0xabf178: StoreField: r2->field_13 = r0
    //     0xabf178: stur            w0, [x2, #0x13]
    // 0xabf17c: r1 = <Widget>
    //     0xabf17c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xabf180: r0 = AllocateGrowableArray()
    //     0xabf180: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xabf184: mov             x1, x0
    // 0xabf188: ldur            x0, [fp, #-0x10]
    // 0xabf18c: stur            x1, [fp, #-8]
    // 0xabf190: StoreField: r1->field_f = r0
    //     0xabf190: stur            w0, [x1, #0xf]
    // 0xabf194: r0 = 4
    //     0xabf194: movz            x0, #0x4
    // 0xabf198: StoreField: r1->field_b = r0
    //     0xabf198: stur            w0, [x1, #0xb]
    // 0xabf19c: r0 = Column()
    //     0xabf19c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xabf1a0: mov             x1, x0
    // 0xabf1a4: r0 = Instance_Axis
    //     0xabf1a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xabf1a8: stur            x1, [fp, #-0x10]
    // 0xabf1ac: StoreField: r1->field_f = r0
    //     0xabf1ac: stur            w0, [x1, #0xf]
    // 0xabf1b0: r0 = Instance_MainAxisAlignment
    //     0xabf1b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xabf1b4: ldr             x0, [x0, #0xa08]
    // 0xabf1b8: StoreField: r1->field_13 = r0
    //     0xabf1b8: stur            w0, [x1, #0x13]
    // 0xabf1bc: r0 = Instance_MainAxisSize
    //     0xabf1bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xabf1c0: ldr             x0, [x0, #0xa10]
    // 0xabf1c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xabf1c4: stur            w0, [x1, #0x17]
    // 0xabf1c8: r0 = Instance_CrossAxisAlignment
    //     0xabf1c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xabf1cc: ldr             x0, [x0, #0xa18]
    // 0xabf1d0: StoreField: r1->field_1b = r0
    //     0xabf1d0: stur            w0, [x1, #0x1b]
    // 0xabf1d4: r0 = Instance_VerticalDirection
    //     0xabf1d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xabf1d8: ldr             x0, [x0, #0xa20]
    // 0xabf1dc: StoreField: r1->field_23 = r0
    //     0xabf1dc: stur            w0, [x1, #0x23]
    // 0xabf1e0: r0 = Instance_Clip
    //     0xabf1e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xabf1e4: ldr             x0, [x0, #0x38]
    // 0xabf1e8: StoreField: r1->field_2b = r0
    //     0xabf1e8: stur            w0, [x1, #0x2b]
    // 0xabf1ec: StoreField: r1->field_2f = rZR
    //     0xabf1ec: stur            xzr, [x1, #0x2f]
    // 0xabf1f0: ldur            x0, [fp, #-8]
    // 0xabf1f4: StoreField: r1->field_b = r0
    //     0xabf1f4: stur            w0, [x1, #0xb]
    // 0xabf1f8: r0 = Card()
    //     0xabf1f8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xabf1fc: r1 = 0.000000
    //     0xabf1fc: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xabf200: ArrayStore: r0[0] = r1  ; List_4
    //     0xabf200: stur            w1, [x0, #0x17]
    // 0xabf204: r1 = true
    //     0xabf204: add             x1, NULL, #0x20  ; true
    // 0xabf208: StoreField: r0->field_1f = r1
    //     0xabf208: stur            w1, [x0, #0x1f]
    // 0xabf20c: ldur            x2, [fp, #-0x10]
    // 0xabf210: StoreField: r0->field_2f = r2
    //     0xabf210: stur            w2, [x0, #0x2f]
    // 0xabf214: StoreField: r0->field_2b = r1
    //     0xabf214: stur            w1, [x0, #0x2b]
    // 0xabf218: r1 = Instance__CardVariant
    //     0xabf218: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xabf21c: ldr             x1, [x1, #0xa68]
    // 0xabf220: StoreField: r0->field_33 = r1
    //     0xabf220: stur            w1, [x0, #0x33]
    // 0xabf224: LeaveFrame
    //     0xabf224: mov             SP, fp
    //     0xabf228: ldp             fp, lr, [SP], #0x10
    // 0xabf22c: ret
    //     0xabf22c: ret             
    // 0xabf230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf230: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf234: b               #0xabe8e4
    // 0xabf238: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf238: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabf23c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf23c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabf240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabf244, size: 0x314
    // 0xabf244: EnterFrame
    //     0xabf244: stp             fp, lr, [SP, #-0x10]!
    //     0xabf248: mov             fp, SP
    // 0xabf24c: AllocStack(0x38)
    //     0xabf24c: sub             SP, SP, #0x38
    // 0xabf250: SetupParameters()
    //     0xabf250: ldr             x0, [fp, #0x10]
    //     0xabf254: ldur            w2, [x0, #0x17]
    //     0xabf258: add             x2, x2, HEAP, lsl #32
    //     0xabf25c: stur            x2, [fp, #-0x18]
    // 0xabf260: CheckStackOverflow
    //     0xabf260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabf264: cmp             SP, x16
    //     0xabf268: b.ls            #0xabf53c
    // 0xabf26c: LoadField: r3 = r2->field_b
    //     0xabf26c: ldur            w3, [x2, #0xb]
    // 0xabf270: DecompressPointer r3
    //     0xabf270: add             x3, x3, HEAP, lsl #32
    // 0xabf274: stur            x3, [fp, #-0x10]
    // 0xabf278: LoadField: r0 = r3->field_f
    //     0xabf278: ldur            w0, [x3, #0xf]
    // 0xabf27c: DecompressPointer r0
    //     0xabf27c: add             x0, x0, HEAP, lsl #32
    // 0xabf280: LoadField: r4 = r0->field_b
    //     0xabf280: ldur            w4, [x0, #0xb]
    // 0xabf284: DecompressPointer r4
    //     0xabf284: add             x4, x4, HEAP, lsl #32
    // 0xabf288: stur            x4, [fp, #-8]
    // 0xabf28c: cmp             w4, NULL
    // 0xabf290: b.eq            #0xabf544
    // 0xabf294: LoadField: r0 = r4->field_b
    //     0xabf294: ldur            w0, [x4, #0xb]
    // 0xabf298: DecompressPointer r0
    //     0xabf298: add             x0, x0, HEAP, lsl #32
    // 0xabf29c: LoadField: r1 = r0->field_b
    //     0xabf29c: ldur            w1, [x0, #0xb]
    // 0xabf2a0: DecompressPointer r1
    //     0xabf2a0: add             x1, x1, HEAP, lsl #32
    // 0xabf2a4: cmp             w1, NULL
    // 0xabf2a8: b.ne            #0xabf2b4
    // 0xabf2ac: r0 = Null
    //     0xabf2ac: mov             x0, NULL
    // 0xabf2b0: b               #0xabf324
    // 0xabf2b4: LoadField: r0 = r1->field_f
    //     0xabf2b4: ldur            w0, [x1, #0xf]
    // 0xabf2b8: DecompressPointer r0
    //     0xabf2b8: add             x0, x0, HEAP, lsl #32
    // 0xabf2bc: cmp             w0, NULL
    // 0xabf2c0: b.ne            #0xabf2cc
    // 0xabf2c4: r0 = Null
    //     0xabf2c4: mov             x0, NULL
    // 0xabf2c8: b               #0xabf324
    // 0xabf2cc: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xabf2cc: ldur            w5, [x0, #0x17]
    // 0xabf2d0: DecompressPointer r5
    //     0xabf2d0: add             x5, x5, HEAP, lsl #32
    // 0xabf2d4: cmp             w5, NULL
    // 0xabf2d8: b.ne            #0xabf2e4
    // 0xabf2dc: r0 = Null
    //     0xabf2dc: mov             x0, NULL
    // 0xabf2e0: b               #0xabf324
    // 0xabf2e4: LoadField: r0 = r2->field_f
    //     0xabf2e4: ldur            w0, [x2, #0xf]
    // 0xabf2e8: DecompressPointer r0
    //     0xabf2e8: add             x0, x0, HEAP, lsl #32
    // 0xabf2ec: LoadField: r1 = r5->field_b
    //     0xabf2ec: ldur            w1, [x5, #0xb]
    // 0xabf2f0: r6 = LoadInt32Instr(r0)
    //     0xabf2f0: sbfx            x6, x0, #1, #0x1f
    //     0xabf2f4: tbz             w0, #0, #0xabf2fc
    //     0xabf2f8: ldur            x6, [x0, #7]
    // 0xabf2fc: r0 = LoadInt32Instr(r1)
    //     0xabf2fc: sbfx            x0, x1, #1, #0x1f
    // 0xabf300: mov             x1, x6
    // 0xabf304: cmp             x1, x0
    // 0xabf308: b.hs            #0xabf548
    // 0xabf30c: LoadField: r0 = r5->field_f
    //     0xabf30c: ldur            w0, [x5, #0xf]
    // 0xabf310: DecompressPointer r0
    //     0xabf310: add             x0, x0, HEAP, lsl #32
    // 0xabf314: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabf314: add             x16, x0, x6, lsl #2
    //     0xabf318: ldur            w1, [x16, #0xf]
    // 0xabf31c: DecompressPointer r1
    //     0xabf31c: add             x1, x1, HEAP, lsl #32
    // 0xabf320: mov             x0, x1
    // 0xabf324: cmp             w0, NULL
    // 0xabf328: b.ne            #0xabf338
    // 0xabf32c: r0 = BEntities()
    //     0xabf32c: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xabf330: mov             x3, x0
    // 0xabf334: b               #0xabf33c
    // 0xabf338: mov             x3, x0
    // 0xabf33c: ldur            x2, [fp, #-8]
    // 0xabf340: LoadField: r0 = r2->field_b
    //     0xabf340: ldur            w0, [x2, #0xb]
    // 0xabf344: DecompressPointer r0
    //     0xabf344: add             x0, x0, HEAP, lsl #32
    // 0xabf348: LoadField: r1 = r0->field_b
    //     0xabf348: ldur            w1, [x0, #0xb]
    // 0xabf34c: DecompressPointer r1
    //     0xabf34c: add             x1, x1, HEAP, lsl #32
    // 0xabf350: cmp             w1, NULL
    // 0xabf354: b.ne            #0xabf364
    // 0xabf358: ldur            x5, [fp, #-0x18]
    // 0xabf35c: r0 = Null
    //     0xabf35c: mov             x0, NULL
    // 0xabf360: b               #0xabf3e4
    // 0xabf364: LoadField: r0 = r1->field_f
    //     0xabf364: ldur            w0, [x1, #0xf]
    // 0xabf368: DecompressPointer r0
    //     0xabf368: add             x0, x0, HEAP, lsl #32
    // 0xabf36c: cmp             w0, NULL
    // 0xabf370: b.ne            #0xabf380
    // 0xabf374: ldur            x5, [fp, #-0x18]
    // 0xabf378: r0 = Null
    //     0xabf378: mov             x0, NULL
    // 0xabf37c: b               #0xabf3e4
    // 0xabf380: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xabf380: ldur            w4, [x0, #0x17]
    // 0xabf384: DecompressPointer r4
    //     0xabf384: add             x4, x4, HEAP, lsl #32
    // 0xabf388: cmp             w4, NULL
    // 0xabf38c: b.ne            #0xabf39c
    // 0xabf390: ldur            x5, [fp, #-0x18]
    // 0xabf394: r0 = Null
    //     0xabf394: mov             x0, NULL
    // 0xabf398: b               #0xabf3e4
    // 0xabf39c: ldur            x5, [fp, #-0x18]
    // 0xabf3a0: LoadField: r0 = r5->field_f
    //     0xabf3a0: ldur            w0, [x5, #0xf]
    // 0xabf3a4: DecompressPointer r0
    //     0xabf3a4: add             x0, x0, HEAP, lsl #32
    // 0xabf3a8: LoadField: r1 = r4->field_b
    //     0xabf3a8: ldur            w1, [x4, #0xb]
    // 0xabf3ac: r6 = LoadInt32Instr(r0)
    //     0xabf3ac: sbfx            x6, x0, #1, #0x1f
    //     0xabf3b0: tbz             w0, #0, #0xabf3b8
    //     0xabf3b4: ldur            x6, [x0, #7]
    // 0xabf3b8: r0 = LoadInt32Instr(r1)
    //     0xabf3b8: sbfx            x0, x1, #1, #0x1f
    // 0xabf3bc: mov             x1, x6
    // 0xabf3c0: cmp             x1, x0
    // 0xabf3c4: b.hs            #0xabf54c
    // 0xabf3c8: LoadField: r0 = r4->field_f
    //     0xabf3c8: ldur            w0, [x4, #0xf]
    // 0xabf3cc: DecompressPointer r0
    //     0xabf3cc: add             x0, x0, HEAP, lsl #32
    // 0xabf3d0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xabf3d0: add             x16, x0, x6, lsl #2
    //     0xabf3d4: ldur            w1, [x16, #0xf]
    // 0xabf3d8: DecompressPointer r1
    //     0xabf3d8: add             x1, x1, HEAP, lsl #32
    // 0xabf3dc: LoadField: r0 = r1->field_b
    //     0xabf3dc: ldur            w0, [x1, #0xb]
    // 0xabf3e0: DecompressPointer r0
    //     0xabf3e0: add             x0, x0, HEAP, lsl #32
    // 0xabf3e4: cmp             w0, NULL
    // 0xabf3e8: b.ne            #0xabf3f4
    // 0xabf3ec: r4 = ""
    //     0xabf3ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xabf3f0: b               #0xabf3f8
    // 0xabf3f4: mov             x4, x0
    // 0xabf3f8: LoadField: r0 = r2->field_b
    //     0xabf3f8: ldur            w0, [x2, #0xb]
    // 0xabf3fc: DecompressPointer r0
    //     0xabf3fc: add             x0, x0, HEAP, lsl #32
    // 0xabf400: LoadField: r1 = r0->field_b
    //     0xabf400: ldur            w1, [x0, #0xb]
    // 0xabf404: DecompressPointer r1
    //     0xabf404: add             x1, x1, HEAP, lsl #32
    // 0xabf408: cmp             w1, NULL
    // 0xabf40c: b.ne            #0xabf418
    // 0xabf410: r0 = Null
    //     0xabf410: mov             x0, NULL
    // 0xabf414: b               #0xabf48c
    // 0xabf418: LoadField: r0 = r1->field_f
    //     0xabf418: ldur            w0, [x1, #0xf]
    // 0xabf41c: DecompressPointer r0
    //     0xabf41c: add             x0, x0, HEAP, lsl #32
    // 0xabf420: cmp             w0, NULL
    // 0xabf424: b.ne            #0xabf430
    // 0xabf428: r0 = Null
    //     0xabf428: mov             x0, NULL
    // 0xabf42c: b               #0xabf48c
    // 0xabf430: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xabf430: ldur            w6, [x0, #0x17]
    // 0xabf434: DecompressPointer r6
    //     0xabf434: add             x6, x6, HEAP, lsl #32
    // 0xabf438: cmp             w6, NULL
    // 0xabf43c: b.ne            #0xabf448
    // 0xabf440: r0 = Null
    //     0xabf440: mov             x0, NULL
    // 0xabf444: b               #0xabf48c
    // 0xabf448: LoadField: r0 = r5->field_f
    //     0xabf448: ldur            w0, [x5, #0xf]
    // 0xabf44c: DecompressPointer r0
    //     0xabf44c: add             x0, x0, HEAP, lsl #32
    // 0xabf450: LoadField: r1 = r6->field_b
    //     0xabf450: ldur            w1, [x6, #0xb]
    // 0xabf454: r5 = LoadInt32Instr(r0)
    //     0xabf454: sbfx            x5, x0, #1, #0x1f
    //     0xabf458: tbz             w0, #0, #0xabf460
    //     0xabf45c: ldur            x5, [x0, #7]
    // 0xabf460: r0 = LoadInt32Instr(r1)
    //     0xabf460: sbfx            x0, x1, #1, #0x1f
    // 0xabf464: mov             x1, x5
    // 0xabf468: cmp             x1, x0
    // 0xabf46c: b.hs            #0xabf550
    // 0xabf470: LoadField: r0 = r6->field_f
    //     0xabf470: ldur            w0, [x6, #0xf]
    // 0xabf474: DecompressPointer r0
    //     0xabf474: add             x0, x0, HEAP, lsl #32
    // 0xabf478: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xabf478: add             x16, x0, x5, lsl #2
    //     0xabf47c: ldur            w1, [x16, #0xf]
    // 0xabf480: DecompressPointer r1
    //     0xabf480: add             x1, x1, HEAP, lsl #32
    // 0xabf484: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xabf484: ldur            w0, [x1, #0x17]
    // 0xabf488: DecompressPointer r0
    //     0xabf488: add             x0, x0, HEAP, lsl #32
    // 0xabf48c: cmp             w0, NULL
    // 0xabf490: b.ne            #0xabf49c
    // 0xabf494: r6 = 0
    //     0xabf494: movz            x6, #0
    // 0xabf498: b               #0xabf4ac
    // 0xabf49c: r1 = LoadInt32Instr(r0)
    //     0xabf49c: sbfx            x1, x0, #1, #0x1f
    //     0xabf4a0: tbz             w0, #0, #0xabf4a8
    //     0xabf4a4: ldur            x1, [x0, #7]
    // 0xabf4a8: mov             x6, x1
    // 0xabf4ac: ldur            x5, [fp, #-0x10]
    // 0xabf4b0: ArrayLoad: r7 = r2[0]  ; List_4
    //     0xabf4b0: ldur            w7, [x2, #0x17]
    // 0xabf4b4: DecompressPointer r7
    //     0xabf4b4: add             x7, x7, HEAP, lsl #32
    // 0xabf4b8: r0 = BoxInt64Instr(r6)
    //     0xabf4b8: sbfiz           x0, x6, #1, #0x1f
    //     0xabf4bc: cmp             x6, x0, asr #1
    //     0xabf4c0: b.eq            #0xabf4cc
    //     0xabf4c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xabf4c8: stur            x6, [x0, #7]
    // 0xabf4cc: stp             x3, x7, [SP, #0x10]
    // 0xabf4d0: stp             x0, x4, [SP]
    // 0xabf4d4: r4 = 0
    //     0xabf4d4: movz            x4, #0
    // 0xabf4d8: ldr             x0, [SP, #0x18]
    // 0xabf4dc: r16 = UnlinkedCall_0x613b5c
    //     0xabf4dc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59340] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabf4e0: add             x16, x16, #0x340
    // 0xabf4e4: ldp             x5, lr, [x16]
    // 0xabf4e8: blr             lr
    // 0xabf4ec: ldur            x0, [fp, #-0x10]
    // 0xabf4f0: LoadField: r1 = r0->field_f
    //     0xabf4f0: ldur            w1, [x0, #0xf]
    // 0xabf4f4: DecompressPointer r1
    //     0xabf4f4: add             x1, x1, HEAP, lsl #32
    // 0xabf4f8: LoadField: r0 = r1->field_b
    //     0xabf4f8: ldur            w0, [x1, #0xb]
    // 0xabf4fc: DecompressPointer r0
    //     0xabf4fc: add             x0, x0, HEAP, lsl #32
    // 0xabf500: cmp             w0, NULL
    // 0xabf504: b.eq            #0xabf554
    // 0xabf508: LoadField: r1 = r0->field_1f
    //     0xabf508: ldur            w1, [x0, #0x1f]
    // 0xabf50c: DecompressPointer r1
    //     0xabf50c: add             x1, x1, HEAP, lsl #32
    // 0xabf510: str             x1, [SP]
    // 0xabf514: r4 = 0
    //     0xabf514: movz            x4, #0
    // 0xabf518: ldr             x0, [SP]
    // 0xabf51c: r16 = UnlinkedCall_0x613b5c
    //     0xabf51c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59350] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabf520: add             x16, x16, #0x350
    // 0xabf524: ldp             x5, lr, [x16]
    // 0xabf528: blr             lr
    // 0xabf52c: r0 = Null
    //     0xabf52c: mov             x0, NULL
    // 0xabf530: LeaveFrame
    //     0xabf530: mov             SP, fp
    //     0xabf534: ldp             fp, lr, [SP], #0x10
    // 0xabf538: ret
    //     0xabf538: ret             
    // 0xabf53c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf53c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf540: b               #0xabf26c
    // 0xabf544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xabf548: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabf548: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabf54c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabf54c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabf550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xabf550: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xabf554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf554: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabf558, size: 0x60
    // 0xabf558: EnterFrame
    //     0xabf558: stp             fp, lr, [SP, #-0x10]!
    //     0xabf55c: mov             fp, SP
    // 0xabf560: AllocStack(0x8)
    //     0xabf560: sub             SP, SP, #8
    // 0xabf564: SetupParameters()
    //     0xabf564: ldr             x0, [fp, #0x10]
    //     0xabf568: ldur            w2, [x0, #0x17]
    //     0xabf56c: add             x2, x2, HEAP, lsl #32
    // 0xabf570: CheckStackOverflow
    //     0xabf570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabf574: cmp             SP, x16
    //     0xabf578: b.ls            #0xabf5b0
    // 0xabf57c: LoadField: r0 = r2->field_f
    //     0xabf57c: ldur            w0, [x2, #0xf]
    // 0xabf580: DecompressPointer r0
    //     0xabf580: add             x0, x0, HEAP, lsl #32
    // 0xabf584: stur            x0, [fp, #-8]
    // 0xabf588: r1 = Function '<anonymous closure>':.
    //     0xabf588: add             x1, PP, #0x59, lsl #12  ; [pp+0x59370] AnonymousClosure: (0xabf5b8), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabf58c: ldr             x1, [x1, #0x370]
    // 0xabf590: r0 = AllocateClosure()
    //     0xabf590: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabf594: ldur            x1, [fp, #-8]
    // 0xabf598: mov             x2, x0
    // 0xabf59c: r0 = setState()
    //     0xabf59c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xabf5a0: r0 = Null
    //     0xabf5a0: mov             x0, NULL
    // 0xabf5a4: LeaveFrame
    //     0xabf5a4: mov             SP, fp
    //     0xabf5a8: ldp             fp, lr, [SP], #0x10
    // 0xabf5ac: ret
    //     0xabf5ac: ret             
    // 0xabf5b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf5b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf5b4: b               #0xabf57c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabf5b8, size: 0x7c
    // 0xabf5b8: EnterFrame
    //     0xabf5b8: stp             fp, lr, [SP, #-0x10]!
    //     0xabf5bc: mov             fp, SP
    // 0xabf5c0: AllocStack(0x8)
    //     0xabf5c0: sub             SP, SP, #8
    // 0xabf5c4: SetupParameters()
    //     0xabf5c4: ldr             x0, [fp, #0x10]
    //     0xabf5c8: ldur            w1, [x0, #0x17]
    //     0xabf5cc: add             x1, x1, HEAP, lsl #32
    // 0xabf5d0: CheckStackOverflow
    //     0xabf5d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabf5d4: cmp             SP, x16
    //     0xabf5d8: b.ls            #0xabf628
    // 0xabf5dc: LoadField: r0 = r1->field_f
    //     0xabf5dc: ldur            w0, [x1, #0xf]
    // 0xabf5e0: DecompressPointer r0
    //     0xabf5e0: add             x0, x0, HEAP, lsl #32
    // 0xabf5e4: LoadField: r1 = r0->field_b
    //     0xabf5e4: ldur            w1, [x0, #0xb]
    // 0xabf5e8: DecompressPointer r1
    //     0xabf5e8: add             x1, x1, HEAP, lsl #32
    // 0xabf5ec: cmp             w1, NULL
    // 0xabf5f0: b.eq            #0xabf630
    // 0xabf5f4: LoadField: r0 = r1->field_23
    //     0xabf5f4: ldur            w0, [x1, #0x23]
    // 0xabf5f8: DecompressPointer r0
    //     0xabf5f8: add             x0, x0, HEAP, lsl #32
    // 0xabf5fc: str             x0, [SP]
    // 0xabf600: r4 = 0
    //     0xabf600: movz            x4, #0
    // 0xabf604: ldr             x0, [SP]
    // 0xabf608: r16 = UnlinkedCall_0x613b5c
    //     0xabf608: add             x16, PP, #0x59, lsl #12  ; [pp+0x59378] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabf60c: add             x16, x16, #0x378
    // 0xabf610: ldp             x5, lr, [x16]
    // 0xabf614: blr             lr
    // 0xabf618: r0 = Null
    //     0xabf618: mov             x0, NULL
    // 0xabf61c: LeaveFrame
    //     0xabf61c: mov             SP, fp
    //     0xabf620: ldp             fp, lr, [SP], #0x10
    // 0xabf624: ret
    //     0xabf624: ret             
    // 0xabf628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf628: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf62c: b               #0xabf5dc
    // 0xabf630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf630: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabf634, size: 0x60
    // 0xabf634: EnterFrame
    //     0xabf634: stp             fp, lr, [SP, #-0x10]!
    //     0xabf638: mov             fp, SP
    // 0xabf63c: AllocStack(0x8)
    //     0xabf63c: sub             SP, SP, #8
    // 0xabf640: SetupParameters()
    //     0xabf640: ldr             x0, [fp, #0x10]
    //     0xabf644: ldur            w2, [x0, #0x17]
    //     0xabf648: add             x2, x2, HEAP, lsl #32
    // 0xabf64c: CheckStackOverflow
    //     0xabf64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabf650: cmp             SP, x16
    //     0xabf654: b.ls            #0xabf68c
    // 0xabf658: LoadField: r0 = r2->field_f
    //     0xabf658: ldur            w0, [x2, #0xf]
    // 0xabf65c: DecompressPointer r0
    //     0xabf65c: add             x0, x0, HEAP, lsl #32
    // 0xabf660: stur            x0, [fp, #-8]
    // 0xabf664: r1 = Function '<anonymous closure>':.
    //     0xabf664: add             x1, PP, #0x59, lsl #12  ; [pp+0x59388] AnonymousClosure: (0xabf694), in [package:customer_app/app/presentation/views/cosmetic/checkout_variants/widgets/bag_detail_widget.dart] _BagDetailWidgetState::build (0xabd0f8)
    //     0xabf668: ldr             x1, [x1, #0x388]
    // 0xabf66c: r0 = AllocateClosure()
    //     0xabf66c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xabf670: ldur            x1, [fp, #-8]
    // 0xabf674: mov             x2, x0
    // 0xabf678: r0 = setState()
    //     0xabf678: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xabf67c: r0 = Null
    //     0xabf67c: mov             x0, NULL
    // 0xabf680: LeaveFrame
    //     0xabf680: mov             SP, fp
    //     0xabf684: ldp             fp, lr, [SP], #0x10
    // 0xabf688: ret
    //     0xabf688: ret             
    // 0xabf68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf68c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf690: b               #0xabf658
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xabf694, size: 0x7c
    // 0xabf694: EnterFrame
    //     0xabf694: stp             fp, lr, [SP, #-0x10]!
    //     0xabf698: mov             fp, SP
    // 0xabf69c: AllocStack(0x8)
    //     0xabf69c: sub             SP, SP, #8
    // 0xabf6a0: SetupParameters()
    //     0xabf6a0: ldr             x0, [fp, #0x10]
    //     0xabf6a4: ldur            w1, [x0, #0x17]
    //     0xabf6a8: add             x1, x1, HEAP, lsl #32
    // 0xabf6ac: CheckStackOverflow
    //     0xabf6ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xabf6b0: cmp             SP, x16
    //     0xabf6b4: b.ls            #0xabf704
    // 0xabf6b8: LoadField: r0 = r1->field_f
    //     0xabf6b8: ldur            w0, [x1, #0xf]
    // 0xabf6bc: DecompressPointer r0
    //     0xabf6bc: add             x0, x0, HEAP, lsl #32
    // 0xabf6c0: LoadField: r1 = r0->field_b
    //     0xabf6c0: ldur            w1, [x0, #0xb]
    // 0xabf6c4: DecompressPointer r1
    //     0xabf6c4: add             x1, x1, HEAP, lsl #32
    // 0xabf6c8: cmp             w1, NULL
    // 0xabf6cc: b.eq            #0xabf70c
    // 0xabf6d0: LoadField: r0 = r1->field_27
    //     0xabf6d0: ldur            w0, [x1, #0x27]
    // 0xabf6d4: DecompressPointer r0
    //     0xabf6d4: add             x0, x0, HEAP, lsl #32
    // 0xabf6d8: str             x0, [SP]
    // 0xabf6dc: r4 = 0
    //     0xabf6dc: movz            x4, #0
    // 0xabf6e0: ldr             x0, [SP]
    // 0xabf6e4: r16 = UnlinkedCall_0x613b5c
    //     0xabf6e4: add             x16, PP, #0x59, lsl #12  ; [pp+0x59390] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xabf6e8: add             x16, x16, #0x390
    // 0xabf6ec: ldp             x5, lr, [x16]
    // 0xabf6f0: blr             lr
    // 0xabf6f4: r0 = Null
    //     0xabf6f4: mov             x0, NULL
    // 0xabf6f8: LeaveFrame
    //     0xabf6f8: mov             SP, fp
    //     0xabf6fc: ldp             fp, lr, [SP], #0x10
    // 0xabf700: ret
    //     0xabf700: ret             
    // 0xabf704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xabf704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xabf708: b               #0xabf6b8
    // 0xabf70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xabf70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4195, size: 0x2c, field offset: 0xc
class BagDetailWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7d090, size: 0x24
    // 0xc7d090: EnterFrame
    //     0xc7d090: stp             fp, lr, [SP, #-0x10]!
    //     0xc7d094: mov             fp, SP
    // 0xc7d098: mov             x0, x1
    // 0xc7d09c: r1 = <BagDetailWidget>
    //     0xc7d09c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48dd0] TypeArguments: <BagDetailWidget>
    //     0xc7d0a0: ldr             x1, [x1, #0xdd0]
    // 0xc7d0a4: r0 = _BagDetailWidgetState()
    //     0xc7d0a4: bl              #0xc7d0b4  ; Allocate_BagDetailWidgetStateStub -> _BagDetailWidgetState (size=0x14)
    // 0xc7d0a8: LeaveFrame
    //     0xc7d0a8: mov             SP, fp
    //     0xc7d0ac: ldp             fp, lr, [SP], #0x10
    // 0xc7d0b0: ret
    //     0xc7d0b0: ret             
  }
}
