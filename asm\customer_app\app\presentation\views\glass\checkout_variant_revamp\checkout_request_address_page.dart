// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart

// class id: 1049354, size: 0x8
class :: {
}

// class id: 4578, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestAddressPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x135d484, size: 0x64
    // 0x135d484: EnterFrame
    //     0x135d484: stp             fp, lr, [SP, #-0x10]!
    //     0x135d488: mov             fp, SP
    // 0x135d48c: AllocStack(0x18)
    //     0x135d48c: sub             SP, SP, #0x18
    // 0x135d490: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x135d490: stur            x1, [fp, #-8]
    //     0x135d494: stur            x2, [fp, #-0x10]
    // 0x135d498: r1 = 2
    //     0x135d498: movz            x1, #0x2
    // 0x135d49c: r0 = AllocateContext()
    //     0x135d49c: bl              #0x16f6108  ; AllocateContextStub
    // 0x135d4a0: mov             x1, x0
    // 0x135d4a4: ldur            x0, [fp, #-8]
    // 0x135d4a8: stur            x1, [fp, #-0x18]
    // 0x135d4ac: StoreField: r1->field_f = r0
    //     0x135d4ac: stur            w0, [x1, #0xf]
    // 0x135d4b0: ldur            x0, [fp, #-0x10]
    // 0x135d4b4: StoreField: r1->field_13 = r0
    //     0x135d4b4: stur            w0, [x1, #0x13]
    // 0x135d4b8: r0 = Obx()
    //     0x135d4b8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x135d4bc: ldur            x2, [fp, #-0x18]
    // 0x135d4c0: r1 = Function '<anonymous closure>':.
    //     0x135d4c0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41400] AnonymousClosure: (0x135d4e8), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x135d484)
    //     0x135d4c4: ldr             x1, [x1, #0x400]
    // 0x135d4c8: stur            x0, [fp, #-8]
    // 0x135d4cc: r0 = AllocateClosure()
    //     0x135d4cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135d4d0: mov             x1, x0
    // 0x135d4d4: ldur            x0, [fp, #-8]
    // 0x135d4d8: StoreField: r0->field_b = r1
    //     0x135d4d8: stur            w1, [x0, #0xb]
    // 0x135d4dc: LeaveFrame
    //     0x135d4dc: mov             SP, fp
    //     0x135d4e0: ldp             fp, lr, [SP], #0x10
    // 0x135d4e4: ret
    //     0x135d4e4: ret             
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x135d4e8, size: 0x640
    // 0x135d4e8: EnterFrame
    //     0x135d4e8: stp             fp, lr, [SP, #-0x10]!
    //     0x135d4ec: mov             fp, SP
    // 0x135d4f0: AllocStack(0x60)
    //     0x135d4f0: sub             SP, SP, #0x60
    // 0x135d4f4: SetupParameters()
    //     0x135d4f4: ldr             x0, [fp, #0x10]
    //     0x135d4f8: ldur            w2, [x0, #0x17]
    //     0x135d4fc: add             x2, x2, HEAP, lsl #32
    //     0x135d500: stur            x2, [fp, #-8]
    // 0x135d504: CheckStackOverflow
    //     0x135d504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135d508: cmp             SP, x16
    //     0x135d50c: b.ls            #0x135dadc
    // 0x135d510: LoadField: r1 = r2->field_13
    //     0x135d510: ldur            w1, [x2, #0x13]
    // 0x135d514: DecompressPointer r1
    //     0x135d514: add             x1, x1, HEAP, lsl #32
    // 0x135d518: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x135d518: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x135d51c: r0 = _of()
    //     0x135d51c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x135d520: LoadField: r1 = r0->field_23
    //     0x135d520: ldur            w1, [x0, #0x23]
    // 0x135d524: DecompressPointer r1
    //     0x135d524: add             x1, x1, HEAP, lsl #32
    // 0x135d528: LoadField: d0 = r1->field_1f
    //     0x135d528: ldur            d0, [x1, #0x1f]
    // 0x135d52c: stur            d0, [fp, #-0x40]
    // 0x135d530: r0 = EdgeInsets()
    //     0x135d530: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x135d534: stur            x0, [fp, #-0x10]
    // 0x135d538: StoreField: r0->field_7 = rZR
    //     0x135d538: stur            xzr, [x0, #7]
    // 0x135d53c: StoreField: r0->field_f = rZR
    //     0x135d53c: stur            xzr, [x0, #0xf]
    // 0x135d540: ArrayStore: r0[0] = rZR  ; List_8
    //     0x135d540: stur            xzr, [x0, #0x17]
    // 0x135d544: ldur            d0, [fp, #-0x40]
    // 0x135d548: StoreField: r0->field_1f = d0
    //     0x135d548: stur            d0, [x0, #0x1f]
    // 0x135d54c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x135d54c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x135d550: ldr             x0, [x0, #0x1c80]
    //     0x135d554: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x135d558: cmp             w0, w16
    //     0x135d55c: b.ne            #0x135d568
    //     0x135d560: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x135d564: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x135d568: r0 = GetNavigation.width()
    //     0x135d568: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x135d56c: stur            d0, [fp, #-0x40]
    // 0x135d570: r16 = <EdgeInsets>
    //     0x135d570: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x135d574: ldr             x16, [x16, #0xda0]
    // 0x135d578: r30 = Instance_EdgeInsets
    //     0x135d578: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x135d57c: ldr             lr, [lr, #0x1f0]
    // 0x135d580: stp             lr, x16, [SP]
    // 0x135d584: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x135d584: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x135d588: r0 = all()
    //     0x135d588: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x135d58c: ldur            x2, [fp, #-8]
    // 0x135d590: stur            x0, [fp, #-0x18]
    // 0x135d594: LoadField: r1 = r2->field_f
    //     0x135d594: ldur            w1, [x2, #0xf]
    // 0x135d598: DecompressPointer r1
    //     0x135d598: add             x1, x1, HEAP, lsl #32
    // 0x135d59c: r0 = controller()
    //     0x135d59c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135d5a0: LoadField: r1 = r0->field_9f
    //     0x135d5a0: ldur            w1, [x0, #0x9f]
    // 0x135d5a4: DecompressPointer r1
    //     0x135d5a4: add             x1, x1, HEAP, lsl #32
    // 0x135d5a8: r0 = value()
    //     0x135d5a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135d5ac: tbnz            w0, #4, #0x135d5cc
    // 0x135d5b0: ldur            x2, [fp, #-8]
    // 0x135d5b4: LoadField: r1 = r2->field_13
    //     0x135d5b4: ldur            w1, [x2, #0x13]
    // 0x135d5b8: DecompressPointer r1
    //     0x135d5b8: add             x1, x1, HEAP, lsl #32
    // 0x135d5bc: r0 = of()
    //     0x135d5bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135d5c0: LoadField: r1 = r0->field_5b
    //     0x135d5c0: ldur            w1, [x0, #0x5b]
    // 0x135d5c4: DecompressPointer r1
    //     0x135d5c4: add             x1, x1, HEAP, lsl #32
    // 0x135d5c8: b               #0x135d5d4
    // 0x135d5cc: r1 = Instance_MaterialColor
    //     0x135d5cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x135d5d0: ldr             x1, [x1, #0xdc0]
    // 0x135d5d4: ldur            x2, [fp, #-8]
    // 0x135d5d8: ldur            x0, [fp, #-0x18]
    // 0x135d5dc: r16 = <Color>
    //     0x135d5dc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x135d5e0: ldr             x16, [x16, #0xf80]
    // 0x135d5e4: stp             x1, x16, [SP]
    // 0x135d5e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x135d5e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x135d5ec: r0 = all()
    //     0x135d5ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x135d5f0: ldur            x2, [fp, #-8]
    // 0x135d5f4: stur            x0, [fp, #-0x20]
    // 0x135d5f8: LoadField: r1 = r2->field_13
    //     0x135d5f8: ldur            w1, [x2, #0x13]
    // 0x135d5fc: DecompressPointer r1
    //     0x135d5fc: add             x1, x1, HEAP, lsl #32
    // 0x135d600: r0 = of()
    //     0x135d600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135d604: LoadField: r1 = r0->field_5b
    //     0x135d604: ldur            w1, [x0, #0x5b]
    // 0x135d608: DecompressPointer r1
    //     0x135d608: add             x1, x1, HEAP, lsl #32
    // 0x135d60c: stur            x1, [fp, #-0x28]
    // 0x135d610: r0 = BorderSide()
    //     0x135d610: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x135d614: mov             x1, x0
    // 0x135d618: ldur            x0, [fp, #-0x28]
    // 0x135d61c: stur            x1, [fp, #-0x30]
    // 0x135d620: StoreField: r1->field_7 = r0
    //     0x135d620: stur            w0, [x1, #7]
    // 0x135d624: d0 = 1.000000
    //     0x135d624: fmov            d0, #1.00000000
    // 0x135d628: StoreField: r1->field_b = d0
    //     0x135d628: stur            d0, [x1, #0xb]
    // 0x135d62c: r0 = Instance_BorderStyle
    //     0x135d62c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x135d630: ldr             x0, [x0, #0xf68]
    // 0x135d634: StoreField: r1->field_13 = r0
    //     0x135d634: stur            w0, [x1, #0x13]
    // 0x135d638: d0 = -1.000000
    //     0x135d638: fmov            d0, #-1.00000000
    // 0x135d63c: ArrayStore: r1[0] = d0  ; List_8
    //     0x135d63c: stur            d0, [x1, #0x17]
    // 0x135d640: r0 = Radius()
    //     0x135d640: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x135d644: d0 = 20.000000
    //     0x135d644: fmov            d0, #20.00000000
    // 0x135d648: stur            x0, [fp, #-0x28]
    // 0x135d64c: StoreField: r0->field_7 = d0
    //     0x135d64c: stur            d0, [x0, #7]
    // 0x135d650: StoreField: r0->field_f = d0
    //     0x135d650: stur            d0, [x0, #0xf]
    // 0x135d654: r0 = BorderRadius()
    //     0x135d654: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x135d658: mov             x1, x0
    // 0x135d65c: ldur            x0, [fp, #-0x28]
    // 0x135d660: stur            x1, [fp, #-0x38]
    // 0x135d664: StoreField: r1->field_7 = r0
    //     0x135d664: stur            w0, [x1, #7]
    // 0x135d668: StoreField: r1->field_b = r0
    //     0x135d668: stur            w0, [x1, #0xb]
    // 0x135d66c: StoreField: r1->field_f = r0
    //     0x135d66c: stur            w0, [x1, #0xf]
    // 0x135d670: StoreField: r1->field_13 = r0
    //     0x135d670: stur            w0, [x1, #0x13]
    // 0x135d674: r0 = RoundedRectangleBorder()
    //     0x135d674: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x135d678: mov             x1, x0
    // 0x135d67c: ldur            x0, [fp, #-0x38]
    // 0x135d680: StoreField: r1->field_b = r0
    //     0x135d680: stur            w0, [x1, #0xb]
    // 0x135d684: ldur            x0, [fp, #-0x30]
    // 0x135d688: StoreField: r1->field_7 = r0
    //     0x135d688: stur            w0, [x1, #7]
    // 0x135d68c: r16 = <RoundedRectangleBorder>
    //     0x135d68c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x135d690: ldr             x16, [x16, #0xf78]
    // 0x135d694: stp             x1, x16, [SP]
    // 0x135d698: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x135d698: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x135d69c: r0 = all()
    //     0x135d69c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x135d6a0: stur            x0, [fp, #-0x28]
    // 0x135d6a4: r0 = ButtonStyle()
    //     0x135d6a4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x135d6a8: mov             x1, x0
    // 0x135d6ac: ldur            x0, [fp, #-0x20]
    // 0x135d6b0: stur            x1, [fp, #-0x30]
    // 0x135d6b4: StoreField: r1->field_b = r0
    //     0x135d6b4: stur            w0, [x1, #0xb]
    // 0x135d6b8: ldur            x0, [fp, #-0x18]
    // 0x135d6bc: StoreField: r1->field_23 = r0
    //     0x135d6bc: stur            w0, [x1, #0x23]
    // 0x135d6c0: ldur            x0, [fp, #-0x28]
    // 0x135d6c4: StoreField: r1->field_43 = r0
    //     0x135d6c4: stur            w0, [x1, #0x43]
    // 0x135d6c8: r0 = TextButtonThemeData()
    //     0x135d6c8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x135d6cc: mov             x2, x0
    // 0x135d6d0: ldur            x0, [fp, #-0x30]
    // 0x135d6d4: stur            x2, [fp, #-0x18]
    // 0x135d6d8: StoreField: r2->field_7 = r0
    //     0x135d6d8: stur            w0, [x2, #7]
    // 0x135d6dc: ldur            x0, [fp, #-8]
    // 0x135d6e0: LoadField: r1 = r0->field_f
    //     0x135d6e0: ldur            w1, [x0, #0xf]
    // 0x135d6e4: DecompressPointer r1
    //     0x135d6e4: add             x1, x1, HEAP, lsl #32
    // 0x135d6e8: r0 = controller()
    //     0x135d6e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135d6ec: LoadField: r1 = r0->field_9f
    //     0x135d6ec: ldur            w1, [x0, #0x9f]
    // 0x135d6f0: DecompressPointer r1
    //     0x135d6f0: add             x1, x1, HEAP, lsl #32
    // 0x135d6f4: r0 = value()
    //     0x135d6f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135d6f8: tbnz            w0, #4, #0x135d714
    // 0x135d6fc: ldur            x2, [fp, #-8]
    // 0x135d700: r1 = Function '<anonymous closure>':.
    //     0x135d700: add             x1, PP, #0x41, lsl #12  ; [pp+0x41408] AnonymousClosure: (0x12e5430), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x1365a7c)
    //     0x135d704: ldr             x1, [x1, #0x408]
    // 0x135d708: r0 = AllocateClosure()
    //     0x135d708: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135d70c: mov             x4, x0
    // 0x135d710: b               #0x135d718
    // 0x135d714: r4 = Null
    //     0x135d714: mov             x4, NULL
    // 0x135d718: ldur            x2, [fp, #-8]
    // 0x135d71c: ldur            x3, [fp, #-0x10]
    // 0x135d720: ldur            d0, [fp, #-0x40]
    // 0x135d724: ldur            x0, [fp, #-0x18]
    // 0x135d728: stur            x4, [fp, #-0x20]
    // 0x135d72c: LoadField: r1 = r2->field_13
    //     0x135d72c: ldur            w1, [x2, #0x13]
    // 0x135d730: DecompressPointer r1
    //     0x135d730: add             x1, x1, HEAP, lsl #32
    // 0x135d734: r0 = of()
    //     0x135d734: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135d738: LoadField: r1 = r0->field_87
    //     0x135d738: ldur            w1, [x0, #0x87]
    // 0x135d73c: DecompressPointer r1
    //     0x135d73c: add             x1, x1, HEAP, lsl #32
    // 0x135d740: LoadField: r0 = r1->field_7
    //     0x135d740: ldur            w0, [x1, #7]
    // 0x135d744: DecompressPointer r0
    //     0x135d744: add             x0, x0, HEAP, lsl #32
    // 0x135d748: r16 = 14.000000
    //     0x135d748: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x135d74c: ldr             x16, [x16, #0x1d8]
    // 0x135d750: r30 = Instance_Color
    //     0x135d750: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x135d754: stp             lr, x16, [SP]
    // 0x135d758: mov             x1, x0
    // 0x135d75c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135d75c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x135d760: ldr             x4, [x4, #0xaa0]
    // 0x135d764: r0 = copyWith()
    //     0x135d764: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x135d768: stur            x0, [fp, #-0x28]
    // 0x135d76c: r0 = Text()
    //     0x135d76c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x135d770: mov             x1, x0
    // 0x135d774: r0 = "Continue"
    //     0x135d774: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x135d778: ldr             x0, [x0, #0xfe0]
    // 0x135d77c: stur            x1, [fp, #-0x30]
    // 0x135d780: StoreField: r1->field_b = r0
    //     0x135d780: stur            w0, [x1, #0xb]
    // 0x135d784: ldur            x0, [fp, #-0x28]
    // 0x135d788: StoreField: r1->field_13 = r0
    //     0x135d788: stur            w0, [x1, #0x13]
    // 0x135d78c: r0 = TextButton()
    //     0x135d78c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x135d790: mov             x1, x0
    // 0x135d794: ldur            x0, [fp, #-0x20]
    // 0x135d798: stur            x1, [fp, #-0x28]
    // 0x135d79c: StoreField: r1->field_b = r0
    //     0x135d79c: stur            w0, [x1, #0xb]
    // 0x135d7a0: r0 = false
    //     0x135d7a0: add             x0, NULL, #0x30  ; false
    // 0x135d7a4: StoreField: r1->field_27 = r0
    //     0x135d7a4: stur            w0, [x1, #0x27]
    // 0x135d7a8: r2 = true
    //     0x135d7a8: add             x2, NULL, #0x20  ; true
    // 0x135d7ac: StoreField: r1->field_2f = r2
    //     0x135d7ac: stur            w2, [x1, #0x2f]
    // 0x135d7b0: ldur            x3, [fp, #-0x30]
    // 0x135d7b4: StoreField: r1->field_37 = r3
    //     0x135d7b4: stur            w3, [x1, #0x37]
    // 0x135d7b8: r0 = TextButtonTheme()
    //     0x135d7b8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x135d7bc: mov             x1, x0
    // 0x135d7c0: ldur            x0, [fp, #-0x18]
    // 0x135d7c4: stur            x1, [fp, #-0x20]
    // 0x135d7c8: StoreField: r1->field_f = r0
    //     0x135d7c8: stur            w0, [x1, #0xf]
    // 0x135d7cc: ldur            x0, [fp, #-0x28]
    // 0x135d7d0: StoreField: r1->field_b = r0
    //     0x135d7d0: stur            w0, [x1, #0xb]
    // 0x135d7d4: ldur            d0, [fp, #-0x40]
    // 0x135d7d8: r0 = inline_Allocate_Double()
    //     0x135d7d8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x135d7dc: add             x0, x0, #0x10
    //     0x135d7e0: cmp             x2, x0
    //     0x135d7e4: b.ls            #0x135dae4
    //     0x135d7e8: str             x0, [THR, #0x50]  ; THR::top
    //     0x135d7ec: sub             x0, x0, #0xf
    //     0x135d7f0: movz            x2, #0xe15c
    //     0x135d7f4: movk            x2, #0x3, lsl #16
    //     0x135d7f8: stur            x2, [x0, #-1]
    // 0x135d7fc: StoreField: r0->field_7 = d0
    //     0x135d7fc: stur            d0, [x0, #7]
    // 0x135d800: stur            x0, [fp, #-0x18]
    // 0x135d804: r0 = Container()
    //     0x135d804: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x135d808: stur            x0, [fp, #-0x28]
    // 0x135d80c: r16 = Instance_EdgeInsets
    //     0x135d80c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x135d810: ldr             x16, [x16, #0xe48]
    // 0x135d814: ldur            lr, [fp, #-0x18]
    // 0x135d818: stp             lr, x16, [SP, #8]
    // 0x135d81c: ldur            x16, [fp, #-0x20]
    // 0x135d820: str             x16, [SP]
    // 0x135d824: mov             x1, x0
    // 0x135d828: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0x135d828: add             x4, PP, #0x41, lsl #12  ; [pp+0x41340] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x135d82c: ldr             x4, [x4, #0x340]
    // 0x135d830: r0 = Container()
    //     0x135d830: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x135d834: r0 = GetNavigation.size()
    //     0x135d834: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x135d838: LoadField: d0 = r0->field_7
    //     0x135d838: ldur            d0, [x0, #7]
    // 0x135d83c: ldur            x0, [fp, #-8]
    // 0x135d840: stur            d0, [fp, #-0x40]
    // 0x135d844: LoadField: r1 = r0->field_13
    //     0x135d844: ldur            w1, [x0, #0x13]
    // 0x135d848: DecompressPointer r1
    //     0x135d848: add             x1, x1, HEAP, lsl #32
    // 0x135d84c: r0 = of()
    //     0x135d84c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135d850: LoadField: r1 = r0->field_87
    //     0x135d850: ldur            w1, [x0, #0x87]
    // 0x135d854: DecompressPointer r1
    //     0x135d854: add             x1, x1, HEAP, lsl #32
    // 0x135d858: LoadField: r0 = r1->field_2b
    //     0x135d858: ldur            w0, [x1, #0x2b]
    // 0x135d85c: DecompressPointer r0
    //     0x135d85c: add             x0, x0, HEAP, lsl #32
    // 0x135d860: stur            x0, [fp, #-8]
    // 0x135d864: r1 = Instance_Color
    //     0x135d864: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x135d868: d0 = 0.700000
    //     0x135d868: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x135d86c: ldr             d0, [x17, #0xf48]
    // 0x135d870: r0 = withOpacity()
    //     0x135d870: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x135d874: r16 = 10.000000
    //     0x135d874: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x135d878: stp             x0, x16, [SP]
    // 0x135d87c: ldur            x1, [fp, #-8]
    // 0x135d880: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135d880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x135d884: ldr             x4, [x4, #0xaa0]
    // 0x135d888: r0 = copyWith()
    //     0x135d888: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x135d88c: stur            x0, [fp, #-8]
    // 0x135d890: r0 = Text()
    //     0x135d890: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x135d894: mov             x1, x0
    // 0x135d898: r0 = "Powered By"
    //     0x135d898: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x135d89c: ldr             x0, [x0, #0x750]
    // 0x135d8a0: stur            x1, [fp, #-0x18]
    // 0x135d8a4: StoreField: r1->field_b = r0
    //     0x135d8a4: stur            w0, [x1, #0xb]
    // 0x135d8a8: ldur            x0, [fp, #-8]
    // 0x135d8ac: StoreField: r1->field_13 = r0
    //     0x135d8ac: stur            w0, [x1, #0x13]
    // 0x135d8b0: r0 = SvgPicture()
    //     0x135d8b0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x135d8b4: stur            x0, [fp, #-8]
    // 0x135d8b8: r16 = 20.000000
    //     0x135d8b8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x135d8bc: ldr             x16, [x16, #0xac8]
    // 0x135d8c0: str             x16, [SP]
    // 0x135d8c4: mov             x1, x0
    // 0x135d8c8: r2 = "assets/images/shopdeck.svg"
    //     0x135d8c8: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x135d8cc: ldr             x2, [x2, #0x758]
    // 0x135d8d0: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x135d8d0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x135d8d4: ldr             x4, [x4, #0x760]
    // 0x135d8d8: r0 = SvgPicture.asset()
    //     0x135d8d8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x135d8dc: r1 = Null
    //     0x135d8dc: mov             x1, NULL
    // 0x135d8e0: r2 = 4
    //     0x135d8e0: movz            x2, #0x4
    // 0x135d8e4: r0 = AllocateArray()
    //     0x135d8e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x135d8e8: mov             x2, x0
    // 0x135d8ec: ldur            x0, [fp, #-0x18]
    // 0x135d8f0: stur            x2, [fp, #-0x20]
    // 0x135d8f4: StoreField: r2->field_f = r0
    //     0x135d8f4: stur            w0, [x2, #0xf]
    // 0x135d8f8: ldur            x0, [fp, #-8]
    // 0x135d8fc: StoreField: r2->field_13 = r0
    //     0x135d8fc: stur            w0, [x2, #0x13]
    // 0x135d900: r1 = <Widget>
    //     0x135d900: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x135d904: r0 = AllocateGrowableArray()
    //     0x135d904: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x135d908: mov             x1, x0
    // 0x135d90c: ldur            x0, [fp, #-0x20]
    // 0x135d910: stur            x1, [fp, #-8]
    // 0x135d914: StoreField: r1->field_f = r0
    //     0x135d914: stur            w0, [x1, #0xf]
    // 0x135d918: r2 = 4
    //     0x135d918: movz            x2, #0x4
    // 0x135d91c: StoreField: r1->field_b = r2
    //     0x135d91c: stur            w2, [x1, #0xb]
    // 0x135d920: r0 = Row()
    //     0x135d920: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x135d924: mov             x1, x0
    // 0x135d928: r0 = Instance_Axis
    //     0x135d928: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x135d92c: stur            x1, [fp, #-0x18]
    // 0x135d930: StoreField: r1->field_f = r0
    //     0x135d930: stur            w0, [x1, #0xf]
    // 0x135d934: r0 = Instance_MainAxisAlignment
    //     0x135d934: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x135d938: ldr             x0, [x0, #0xa08]
    // 0x135d93c: StoreField: r1->field_13 = r0
    //     0x135d93c: stur            w0, [x1, #0x13]
    // 0x135d940: r2 = Instance_MainAxisSize
    //     0x135d940: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x135d944: ldr             x2, [x2, #0xdd0]
    // 0x135d948: ArrayStore: r1[0] = r2  ; List_4
    //     0x135d948: stur            w2, [x1, #0x17]
    // 0x135d94c: r3 = Instance_CrossAxisAlignment
    //     0x135d94c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135d950: ldr             x3, [x3, #0xa18]
    // 0x135d954: StoreField: r1->field_1b = r3
    //     0x135d954: stur            w3, [x1, #0x1b]
    // 0x135d958: r4 = Instance_VerticalDirection
    //     0x135d958: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x135d95c: ldr             x4, [x4, #0xa20]
    // 0x135d960: StoreField: r1->field_23 = r4
    //     0x135d960: stur            w4, [x1, #0x23]
    // 0x135d964: r5 = Instance_Clip
    //     0x135d964: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x135d968: ldr             x5, [x5, #0x38]
    // 0x135d96c: StoreField: r1->field_2b = r5
    //     0x135d96c: stur            w5, [x1, #0x2b]
    // 0x135d970: StoreField: r1->field_2f = rZR
    //     0x135d970: stur            xzr, [x1, #0x2f]
    // 0x135d974: ldur            x6, [fp, #-8]
    // 0x135d978: StoreField: r1->field_b = r6
    //     0x135d978: stur            w6, [x1, #0xb]
    // 0x135d97c: ldur            d0, [fp, #-0x40]
    // 0x135d980: r6 = inline_Allocate_Double()
    //     0x135d980: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x135d984: add             x6, x6, #0x10
    //     0x135d988: cmp             x7, x6
    //     0x135d98c: b.ls            #0x135dafc
    //     0x135d990: str             x6, [THR, #0x50]  ; THR::top
    //     0x135d994: sub             x6, x6, #0xf
    //     0x135d998: movz            x7, #0xe15c
    //     0x135d99c: movk            x7, #0x3, lsl #16
    //     0x135d9a0: stur            x7, [x6, #-1]
    // 0x135d9a4: StoreField: r6->field_7 = d0
    //     0x135d9a4: stur            d0, [x6, #7]
    // 0x135d9a8: stur            x6, [fp, #-8]
    // 0x135d9ac: r0 = Container()
    //     0x135d9ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x135d9b0: stur            x0, [fp, #-0x20]
    // 0x135d9b4: r16 = Instance_Alignment
    //     0x135d9b4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x135d9b8: ldr             x16, [x16, #0xb10]
    // 0x135d9bc: ldur            lr, [fp, #-8]
    // 0x135d9c0: stp             lr, x16, [SP, #0x10]
    // 0x135d9c4: r16 = Instance_BoxDecoration
    //     0x135d9c4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x135d9c8: ldr             x16, [x16, #0x768]
    // 0x135d9cc: ldur            lr, [fp, #-0x18]
    // 0x135d9d0: stp             lr, x16, [SP]
    // 0x135d9d4: mov             x1, x0
    // 0x135d9d8: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x135d9d8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x135d9dc: ldr             x4, [x4, #0x770]
    // 0x135d9e0: r0 = Container()
    //     0x135d9e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x135d9e4: r1 = Null
    //     0x135d9e4: mov             x1, NULL
    // 0x135d9e8: r2 = 4
    //     0x135d9e8: movz            x2, #0x4
    // 0x135d9ec: r0 = AllocateArray()
    //     0x135d9ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x135d9f0: mov             x2, x0
    // 0x135d9f4: ldur            x0, [fp, #-0x28]
    // 0x135d9f8: stur            x2, [fp, #-8]
    // 0x135d9fc: StoreField: r2->field_f = r0
    //     0x135d9fc: stur            w0, [x2, #0xf]
    // 0x135da00: ldur            x0, [fp, #-0x20]
    // 0x135da04: StoreField: r2->field_13 = r0
    //     0x135da04: stur            w0, [x2, #0x13]
    // 0x135da08: r1 = <Widget>
    //     0x135da08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x135da0c: r0 = AllocateGrowableArray()
    //     0x135da0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x135da10: mov             x1, x0
    // 0x135da14: ldur            x0, [fp, #-8]
    // 0x135da18: stur            x1, [fp, #-0x18]
    // 0x135da1c: StoreField: r1->field_f = r0
    //     0x135da1c: stur            w0, [x1, #0xf]
    // 0x135da20: r0 = 4
    //     0x135da20: movz            x0, #0x4
    // 0x135da24: StoreField: r1->field_b = r0
    //     0x135da24: stur            w0, [x1, #0xb]
    // 0x135da28: r0 = Column()
    //     0x135da28: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x135da2c: mov             x1, x0
    // 0x135da30: r0 = Instance_Axis
    //     0x135da30: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x135da34: stur            x1, [fp, #-8]
    // 0x135da38: StoreField: r1->field_f = r0
    //     0x135da38: stur            w0, [x1, #0xf]
    // 0x135da3c: r0 = Instance_MainAxisAlignment
    //     0x135da3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x135da40: ldr             x0, [x0, #0xa08]
    // 0x135da44: StoreField: r1->field_13 = r0
    //     0x135da44: stur            w0, [x1, #0x13]
    // 0x135da48: r0 = Instance_MainAxisSize
    //     0x135da48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x135da4c: ldr             x0, [x0, #0xdd0]
    // 0x135da50: ArrayStore: r1[0] = r0  ; List_4
    //     0x135da50: stur            w0, [x1, #0x17]
    // 0x135da54: r0 = Instance_CrossAxisAlignment
    //     0x135da54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135da58: ldr             x0, [x0, #0xa18]
    // 0x135da5c: StoreField: r1->field_1b = r0
    //     0x135da5c: stur            w0, [x1, #0x1b]
    // 0x135da60: r0 = Instance_VerticalDirection
    //     0x135da60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x135da64: ldr             x0, [x0, #0xa20]
    // 0x135da68: StoreField: r1->field_23 = r0
    //     0x135da68: stur            w0, [x1, #0x23]
    // 0x135da6c: r0 = Instance_Clip
    //     0x135da6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x135da70: ldr             x0, [x0, #0x38]
    // 0x135da74: StoreField: r1->field_2b = r0
    //     0x135da74: stur            w0, [x1, #0x2b]
    // 0x135da78: StoreField: r1->field_2f = rZR
    //     0x135da78: stur            xzr, [x1, #0x2f]
    // 0x135da7c: ldur            x0, [fp, #-0x18]
    // 0x135da80: StoreField: r1->field_b = r0
    //     0x135da80: stur            w0, [x1, #0xb]
    // 0x135da84: r0 = Padding()
    //     0x135da84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x135da88: mov             x1, x0
    // 0x135da8c: ldur            x0, [fp, #-0x10]
    // 0x135da90: stur            x1, [fp, #-0x18]
    // 0x135da94: StoreField: r1->field_f = r0
    //     0x135da94: stur            w0, [x1, #0xf]
    // 0x135da98: ldur            x0, [fp, #-8]
    // 0x135da9c: StoreField: r1->field_b = r0
    //     0x135da9c: stur            w0, [x1, #0xb]
    // 0x135daa0: r0 = SafeArea()
    //     0x135daa0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x135daa4: r1 = true
    //     0x135daa4: add             x1, NULL, #0x20  ; true
    // 0x135daa8: StoreField: r0->field_b = r1
    //     0x135daa8: stur            w1, [x0, #0xb]
    // 0x135daac: StoreField: r0->field_f = r1
    //     0x135daac: stur            w1, [x0, #0xf]
    // 0x135dab0: StoreField: r0->field_13 = r1
    //     0x135dab0: stur            w1, [x0, #0x13]
    // 0x135dab4: ArrayStore: r0[0] = r1  ; List_4
    //     0x135dab4: stur            w1, [x0, #0x17]
    // 0x135dab8: r1 = Instance_EdgeInsets
    //     0x135dab8: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x135dabc: StoreField: r0->field_1b = r1
    //     0x135dabc: stur            w1, [x0, #0x1b]
    // 0x135dac0: r1 = false
    //     0x135dac0: add             x1, NULL, #0x30  ; false
    // 0x135dac4: StoreField: r0->field_1f = r1
    //     0x135dac4: stur            w1, [x0, #0x1f]
    // 0x135dac8: ldur            x1, [fp, #-0x18]
    // 0x135dacc: StoreField: r0->field_23 = r1
    //     0x135dacc: stur            w1, [x0, #0x23]
    // 0x135dad0: LeaveFrame
    //     0x135dad0: mov             SP, fp
    //     0x135dad4: ldp             fp, lr, [SP], #0x10
    // 0x135dad8: ret
    //     0x135dad8: ret             
    // 0x135dadc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135dadc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135dae0: b               #0x135d510
    // 0x135dae4: SaveReg d0
    //     0x135dae4: str             q0, [SP, #-0x10]!
    // 0x135dae8: SaveReg r1
    //     0x135dae8: str             x1, [SP, #-8]!
    // 0x135daec: r0 = AllocateDouble()
    //     0x135daec: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x135daf0: RestoreReg r1
    //     0x135daf0: ldr             x1, [SP], #8
    // 0x135daf4: RestoreReg d0
    //     0x135daf4: ldr             q0, [SP], #0x10
    // 0x135daf8: b               #0x135d7fc
    // 0x135dafc: SaveReg d0
    //     0x135dafc: str             q0, [SP, #-0x10]!
    // 0x135db00: stp             x4, x5, [SP, #-0x10]!
    // 0x135db04: stp             x2, x3, [SP, #-0x10]!
    // 0x135db08: stp             x0, x1, [SP, #-0x10]!
    // 0x135db0c: r0 = AllocateDouble()
    //     0x135db0c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x135db10: mov             x6, x0
    // 0x135db14: ldp             x0, x1, [SP], #0x10
    // 0x135db18: ldp             x2, x3, [SP], #0x10
    // 0x135db1c: ldp             x4, x5, [SP], #0x10
    // 0x135db20: RestoreReg d0
    //     0x135db20: ldr             q0, [SP], #0x10
    // 0x135db24: b               #0x135d9a4
  }
  _ body(/* No info */) {
    // ** addr: 0x14d7430, size: 0x224
    // 0x14d7430: EnterFrame
    //     0x14d7430: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7434: mov             fp, SP
    // 0x14d7438: AllocStack(0x28)
    //     0x14d7438: sub             SP, SP, #0x28
    // 0x14d743c: SetupParameters(CheckoutRequestAddressPage this /* r1 => r0, fp-0x8 */)
    //     0x14d743c: mov             x0, x1
    //     0x14d7440: stur            x1, [fp, #-8]
    // 0x14d7444: r1 = 1
    //     0x14d7444: movz            x1, #0x1
    // 0x14d7448: r0 = AllocateContext()
    //     0x14d7448: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d744c: ldur            x2, [fp, #-8]
    // 0x14d7450: stur            x0, [fp, #-0x10]
    // 0x14d7454: StoreField: r0->field_f = r2
    //     0x14d7454: stur            w2, [x0, #0xf]
    // 0x14d7458: r0 = Obx()
    //     0x14d7458: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d745c: ldur            x2, [fp, #-0x10]
    // 0x14d7460: r1 = Function '<anonymous closure>':.
    //     0x14d7460: add             x1, PP, #0x41, lsl #12  ; [pp+0x41410] AnonymousClosure: (0x14d79c0), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x14d7430)
    //     0x14d7464: ldr             x1, [x1, #0x410]
    // 0x14d7468: stur            x0, [fp, #-0x18]
    // 0x14d746c: r0 = AllocateClosure()
    //     0x14d746c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7470: mov             x1, x0
    // 0x14d7474: ldur            x0, [fp, #-0x18]
    // 0x14d7478: StoreField: r0->field_b = r1
    //     0x14d7478: stur            w1, [x0, #0xb]
    // 0x14d747c: r0 = Obx()
    //     0x14d747c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d7480: ldur            x2, [fp, #-0x10]
    // 0x14d7484: r1 = Function '<anonymous closure>':.
    //     0x14d7484: add             x1, PP, #0x41, lsl #12  ; [pp+0x41418] AnonymousClosure: (0x14d78d0), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x14d7430)
    //     0x14d7488: ldr             x1, [x1, #0x418]
    // 0x14d748c: stur            x0, [fp, #-0x20]
    // 0x14d7490: r0 = AllocateClosure()
    //     0x14d7490: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7494: mov             x1, x0
    // 0x14d7498: ldur            x0, [fp, #-0x20]
    // 0x14d749c: StoreField: r0->field_b = r1
    //     0x14d749c: stur            w1, [x0, #0xb]
    // 0x14d74a0: r1 = <CheckoutRequestAddressController>
    //     0x14d74a0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d7c8] TypeArguments: <CheckoutRequestAddressController>
    //     0x14d74a4: ldr             x1, [x1, #0x7c8]
    // 0x14d74a8: r0 = GetBuilder()
    //     0x14d74a8: bl              #0x12af310  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0x14d74ac: mov             x3, x0
    // 0x14d74b0: r0 = true
    //     0x14d74b0: add             x0, NULL, #0x20  ; true
    // 0x14d74b4: stur            x3, [fp, #-0x28]
    // 0x14d74b8: StoreField: r3->field_13 = r0
    //     0x14d74b8: stur            w0, [x3, #0x13]
    // 0x14d74bc: ldur            x2, [fp, #-0x10]
    // 0x14d74c0: r1 = Function '<anonymous closure>':.
    //     0x14d74c0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41420] AnonymousClosure: (0x14d773c), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x14d7430)
    //     0x14d74c4: ldr             x1, [x1, #0x420]
    // 0x14d74c8: r0 = AllocateClosure()
    //     0x14d74c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d74cc: mov             x1, x0
    // 0x14d74d0: ldur            x0, [fp, #-0x28]
    // 0x14d74d4: StoreField: r0->field_f = r1
    //     0x14d74d4: stur            w1, [x0, #0xf]
    // 0x14d74d8: r3 = true
    //     0x14d74d8: add             x3, NULL, #0x20  ; true
    // 0x14d74dc: StoreField: r0->field_1f = r3
    //     0x14d74dc: stur            w3, [x0, #0x1f]
    // 0x14d74e0: r4 = false
    //     0x14d74e0: add             x4, NULL, #0x30  ; false
    // 0x14d74e4: StoreField: r0->field_23 = r4
    //     0x14d74e4: stur            w4, [x0, #0x23]
    // 0x14d74e8: r1 = "address_widget"
    //     0x14d74e8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc10] "address_widget"
    //     0x14d74ec: ldr             x1, [x1, #0xc10]
    // 0x14d74f0: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d74f0: stur            w1, [x0, #0x17]
    // 0x14d74f4: r1 = Null
    //     0x14d74f4: mov             x1, NULL
    // 0x14d74f8: r2 = 6
    //     0x14d74f8: movz            x2, #0x6
    // 0x14d74fc: r0 = AllocateArray()
    //     0x14d74fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d7500: mov             x2, x0
    // 0x14d7504: ldur            x0, [fp, #-0x18]
    // 0x14d7508: stur            x2, [fp, #-0x10]
    // 0x14d750c: StoreField: r2->field_f = r0
    //     0x14d750c: stur            w0, [x2, #0xf]
    // 0x14d7510: ldur            x0, [fp, #-0x20]
    // 0x14d7514: StoreField: r2->field_13 = r0
    //     0x14d7514: stur            w0, [x2, #0x13]
    // 0x14d7518: ldur            x0, [fp, #-0x28]
    // 0x14d751c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d751c: stur            w0, [x2, #0x17]
    // 0x14d7520: r1 = <Widget>
    //     0x14d7520: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d7524: r0 = AllocateGrowableArray()
    //     0x14d7524: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d7528: mov             x1, x0
    // 0x14d752c: ldur            x0, [fp, #-0x10]
    // 0x14d7530: stur            x1, [fp, #-0x18]
    // 0x14d7534: StoreField: r1->field_f = r0
    //     0x14d7534: stur            w0, [x1, #0xf]
    // 0x14d7538: r0 = 6
    //     0x14d7538: movz            x0, #0x6
    // 0x14d753c: StoreField: r1->field_b = r0
    //     0x14d753c: stur            w0, [x1, #0xb]
    // 0x14d7540: r0 = Column()
    //     0x14d7540: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d7544: mov             x1, x0
    // 0x14d7548: r0 = Instance_Axis
    //     0x14d7548: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d754c: stur            x1, [fp, #-0x10]
    // 0x14d7550: StoreField: r1->field_f = r0
    //     0x14d7550: stur            w0, [x1, #0xf]
    // 0x14d7554: r2 = Instance_MainAxisAlignment
    //     0x14d7554: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d7558: ldr             x2, [x2, #0xa08]
    // 0x14d755c: StoreField: r1->field_13 = r2
    //     0x14d755c: stur            w2, [x1, #0x13]
    // 0x14d7560: r2 = Instance_MainAxisSize
    //     0x14d7560: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14d7564: ldr             x2, [x2, #0xdd0]
    // 0x14d7568: ArrayStore: r1[0] = r2  ; List_4
    //     0x14d7568: stur            w2, [x1, #0x17]
    // 0x14d756c: r2 = Instance_CrossAxisAlignment
    //     0x14d756c: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dc18] Obj!CrossAxisAlignment@d73441
    //     0x14d7570: ldr             x2, [x2, #0xc18]
    // 0x14d7574: StoreField: r1->field_1b = r2
    //     0x14d7574: stur            w2, [x1, #0x1b]
    // 0x14d7578: r2 = Instance_VerticalDirection
    //     0x14d7578: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d757c: ldr             x2, [x2, #0xa20]
    // 0x14d7580: StoreField: r1->field_23 = r2
    //     0x14d7580: stur            w2, [x1, #0x23]
    // 0x14d7584: r2 = Instance_Clip
    //     0x14d7584: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d7588: ldr             x2, [x2, #0x38]
    // 0x14d758c: StoreField: r1->field_2b = r2
    //     0x14d758c: stur            w2, [x1, #0x2b]
    // 0x14d7590: StoreField: r1->field_2f = rZR
    //     0x14d7590: stur            xzr, [x1, #0x2f]
    // 0x14d7594: ldur            x2, [fp, #-0x18]
    // 0x14d7598: StoreField: r1->field_b = r2
    //     0x14d7598: stur            w2, [x1, #0xb]
    // 0x14d759c: r0 = SingleChildScrollView()
    //     0x14d759c: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14d75a0: mov             x1, x0
    // 0x14d75a4: r0 = Instance_Axis
    //     0x14d75a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d75a8: stur            x1, [fp, #-0x18]
    // 0x14d75ac: StoreField: r1->field_b = r0
    //     0x14d75ac: stur            w0, [x1, #0xb]
    // 0x14d75b0: r0 = false
    //     0x14d75b0: add             x0, NULL, #0x30  ; false
    // 0x14d75b4: StoreField: r1->field_f = r0
    //     0x14d75b4: stur            w0, [x1, #0xf]
    // 0x14d75b8: ldur            x2, [fp, #-0x10]
    // 0x14d75bc: StoreField: r1->field_23 = r2
    //     0x14d75bc: stur            w2, [x1, #0x23]
    // 0x14d75c0: r2 = Instance_DragStartBehavior
    //     0x14d75c0: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14d75c4: StoreField: r1->field_27 = r2
    //     0x14d75c4: stur            w2, [x1, #0x27]
    // 0x14d75c8: r2 = Instance_Clip
    //     0x14d75c8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14d75cc: ldr             x2, [x2, #0x7e0]
    // 0x14d75d0: StoreField: r1->field_2b = r2
    //     0x14d75d0: stur            w2, [x1, #0x2b]
    // 0x14d75d4: r2 = Instance_HitTestBehavior
    //     0x14d75d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14d75d8: ldr             x2, [x2, #0x288]
    // 0x14d75dc: StoreField: r1->field_2f = r2
    //     0x14d75dc: stur            w2, [x1, #0x2f]
    // 0x14d75e0: r0 = SafeArea()
    //     0x14d75e0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14d75e4: mov             x1, x0
    // 0x14d75e8: r0 = true
    //     0x14d75e8: add             x0, NULL, #0x20  ; true
    // 0x14d75ec: stur            x1, [fp, #-0x10]
    // 0x14d75f0: StoreField: r1->field_b = r0
    //     0x14d75f0: stur            w0, [x1, #0xb]
    // 0x14d75f4: StoreField: r1->field_f = r0
    //     0x14d75f4: stur            w0, [x1, #0xf]
    // 0x14d75f8: StoreField: r1->field_13 = r0
    //     0x14d75f8: stur            w0, [x1, #0x13]
    // 0x14d75fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d75fc: stur            w0, [x1, #0x17]
    // 0x14d7600: r0 = Instance_EdgeInsets
    //     0x14d7600: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d7604: StoreField: r1->field_1b = r0
    //     0x14d7604: stur            w0, [x1, #0x1b]
    // 0x14d7608: r0 = false
    //     0x14d7608: add             x0, NULL, #0x30  ; false
    // 0x14d760c: StoreField: r1->field_1f = r0
    //     0x14d760c: stur            w0, [x1, #0x1f]
    // 0x14d7610: ldur            x0, [fp, #-0x18]
    // 0x14d7614: StoreField: r1->field_23 = r0
    //     0x14d7614: stur            w0, [x1, #0x23]
    // 0x14d7618: r0 = WillPopScope()
    //     0x14d7618: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d761c: mov             x3, x0
    // 0x14d7620: ldur            x0, [fp, #-0x10]
    // 0x14d7624: stur            x3, [fp, #-0x18]
    // 0x14d7628: StoreField: r3->field_b = r0
    //     0x14d7628: stur            w0, [x3, #0xb]
    // 0x14d762c: ldur            x2, [fp, #-8]
    // 0x14d7630: r1 = Function 'getBack':.
    //     0x14d7630: add             x1, PP, #0x41, lsl #12  ; [pp+0x41428] AnonymousClosure: (0x14d7654), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x14d768c)
    //     0x14d7634: ldr             x1, [x1, #0x428]
    // 0x14d7638: r0 = AllocateClosure()
    //     0x14d7638: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d763c: mov             x1, x0
    // 0x14d7640: ldur            x0, [fp, #-0x18]
    // 0x14d7644: StoreField: r0->field_f = r1
    //     0x14d7644: stur            w1, [x0, #0xf]
    // 0x14d7648: LeaveFrame
    //     0x14d7648: mov             SP, fp
    //     0x14d764c: ldp             fp, lr, [SP], #0x10
    // 0x14d7650: ret
    //     0x14d7650: ret             
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x14d7654, size: 0x38
    // 0x14d7654: EnterFrame
    //     0x14d7654: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7658: mov             fp, SP
    // 0x14d765c: ldr             x0, [fp, #0x10]
    // 0x14d7660: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d7660: ldur            w1, [x0, #0x17]
    // 0x14d7664: DecompressPointer r1
    //     0x14d7664: add             x1, x1, HEAP, lsl #32
    // 0x14d7668: CheckStackOverflow
    //     0x14d7668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d766c: cmp             SP, x16
    //     0x14d7670: b.ls            #0x14d7684
    // 0x14d7674: r0 = getBack()
    //     0x14d7674: bl              #0x14d768c  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x14d7678: LeaveFrame
    //     0x14d7678: mov             SP, fp
    //     0x14d767c: ldp             fp, lr, [SP], #0x10
    // 0x14d7680: ret
    //     0x14d7680: ret             
    // 0x14d7684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7688: b               #0x14d7674
  }
  _ getBack(/* No info */) {
    // ** addr: 0x14d768c, size: 0xb0
    // 0x14d768c: EnterFrame
    //     0x14d768c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7690: mov             fp, SP
    // 0x14d7694: AllocStack(0x8)
    //     0x14d7694: sub             SP, SP, #8
    // 0x14d7698: CheckStackOverflow
    //     0x14d7698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d769c: cmp             SP, x16
    //     0x14d76a0: b.ls            #0x14d7734
    // 0x14d76a4: r2 = false
    //     0x14d76a4: add             x2, NULL, #0x30  ; false
    // 0x14d76a8: r0 = showLoading()
    //     0x14d76a8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x14d76ac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14d76ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d76b0: ldr             x0, [x0, #0x1c80]
    //     0x14d76b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d76b8: cmp             w0, w16
    //     0x14d76bc: b.ne            #0x14d76c8
    //     0x14d76c0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14d76c4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d76c8: r1 = Function '<anonymous closure>':.
    //     0x14d76c8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41430] AnonymousClosure: (0x138f78c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x138f970)
    //     0x14d76cc: ldr             x1, [x1, #0x430]
    // 0x14d76d0: r2 = Null
    //     0x14d76d0: mov             x2, NULL
    // 0x14d76d4: r0 = AllocateClosure()
    //     0x14d76d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d76d8: mov             x1, x0
    // 0x14d76dc: r0 = GetNavigation.until()
    //     0x14d76dc: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x14d76e0: r1 = <bool>
    //     0x14d76e0: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x14d76e4: r0 = _Future()
    //     0x14d76e4: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x14d76e8: stur            x0, [fp, #-8]
    // 0x14d76ec: StoreField: r0->field_b = rZR
    //     0x14d76ec: stur            xzr, [x0, #0xb]
    // 0x14d76f0: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x14d76f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d76f4: ldr             x0, [x0, #0x778]
    //     0x14d76f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d76fc: cmp             w0, w16
    //     0x14d7700: b.ne            #0x14d770c
    //     0x14d7704: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14d7708: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14d770c: mov             x1, x0
    // 0x14d7710: ldur            x0, [fp, #-8]
    // 0x14d7714: StoreField: r0->field_13 = r1
    //     0x14d7714: stur            w1, [x0, #0x13]
    // 0x14d7718: mov             x1, x0
    // 0x14d771c: r2 = false
    //     0x14d771c: add             x2, NULL, #0x30  ; false
    // 0x14d7720: r0 = _asyncComplete()
    //     0x14d7720: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14d7724: ldur            x0, [fp, #-8]
    // 0x14d7728: LeaveFrame
    //     0x14d7728: mov             SP, fp
    //     0x14d772c: ldp             fp, lr, [SP], #0x10
    // 0x14d7730: ret
    //     0x14d7730: ret             
    // 0x14d7734: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7734: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7738: b               #0x14d76a4
  }
  [closure] Flexible <anonymous closure>(dynamic, CheckoutRequestAddressController) {
    // ** addr: 0x14d773c, size: 0x158
    // 0x14d773c: EnterFrame
    //     0x14d773c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7740: mov             fp, SP
    // 0x14d7744: AllocStack(0x30)
    //     0x14d7744: sub             SP, SP, #0x30
    // 0x14d7748: SetupParameters()
    //     0x14d7748: ldr             x0, [fp, #0x18]
    //     0x14d774c: ldur            w1, [x0, #0x17]
    //     0x14d7750: add             x1, x1, HEAP, lsl #32
    //     0x14d7754: stur            x1, [fp, #-8]
    // 0x14d7758: CheckStackOverflow
    //     0x14d7758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d775c: cmp             SP, x16
    //     0x14d7760: b.ls            #0x14d788c
    // 0x14d7764: r1 = 1
    //     0x14d7764: movz            x1, #0x1
    // 0x14d7768: r0 = AllocateContext()
    //     0x14d7768: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d776c: mov             x2, x0
    // 0x14d7770: ldur            x0, [fp, #-8]
    // 0x14d7774: stur            x2, [fp, #-0x10]
    // 0x14d7778: StoreField: r2->field_b = r0
    //     0x14d7778: stur            w0, [x2, #0xb]
    // 0x14d777c: ldr             x3, [fp, #0x10]
    // 0x14d7780: StoreField: r2->field_f = r3
    //     0x14d7780: stur            w3, [x2, #0xf]
    // 0x14d7784: LoadField: r1 = r3->field_53
    //     0x14d7784: ldur            w1, [x3, #0x53]
    // 0x14d7788: DecompressPointer r1
    //     0x14d7788: add             x1, x1, HEAP, lsl #32
    // 0x14d778c: r0 = value()
    //     0x14d778c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7790: mov             x2, x0
    // 0x14d7794: ldr             x0, [fp, #0x10]
    // 0x14d7798: stur            x2, [fp, #-0x18]
    // 0x14d779c: LoadField: r1 = r0->field_73
    //     0x14d779c: ldur            w1, [x0, #0x73]
    // 0x14d77a0: DecompressPointer r1
    //     0x14d77a0: add             x1, x1, HEAP, lsl #32
    // 0x14d77a4: r0 = value()
    //     0x14d77a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d77a8: mov             x2, x0
    // 0x14d77ac: ldur            x0, [fp, #-8]
    // 0x14d77b0: stur            x2, [fp, #-0x28]
    // 0x14d77b4: LoadField: r3 = r0->field_f
    //     0x14d77b4: ldur            w3, [x0, #0xf]
    // 0x14d77b8: DecompressPointer r3
    //     0x14d77b8: add             x3, x3, HEAP, lsl #32
    // 0x14d77bc: ldr             x0, [fp, #0x10]
    // 0x14d77c0: stur            x3, [fp, #-0x20]
    // 0x14d77c4: LoadField: r1 = r0->field_af
    //     0x14d77c4: ldur            w1, [x0, #0xaf]
    // 0x14d77c8: DecompressPointer r1
    //     0x14d77c8: add             x1, x1, HEAP, lsl #32
    // 0x14d77cc: r0 = value()
    //     0x14d77cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d77d0: stur            x0, [fp, #-8]
    // 0x14d77d4: r0 = CheckoutAddressWidget()
    //     0x14d77d4: bl              #0x13aa39c  ; AllocateCheckoutAddressWidgetStub -> CheckoutAddressWidget (size=0x24)
    // 0x14d77d8: mov             x3, x0
    // 0x14d77dc: ldur            x0, [fp, #-0x18]
    // 0x14d77e0: stur            x3, [fp, #-0x30]
    // 0x14d77e4: StoreField: r3->field_b = r0
    //     0x14d77e4: stur            w0, [x3, #0xb]
    // 0x14d77e8: ldur            x2, [fp, #-0x10]
    // 0x14d77ec: r1 = Function '<anonymous closure>':.
    //     0x14d77ec: add             x1, PP, #0x41, lsl #12  ; [pp+0x41438] AnonymousClosure: (0x13906d0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x14d77f0: ldr             x1, [x1, #0x438]
    // 0x14d77f4: r0 = AllocateClosure()
    //     0x14d77f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d77f8: mov             x1, x0
    // 0x14d77fc: ldur            x0, [fp, #-0x30]
    // 0x14d7800: StoreField: r0->field_f = r1
    //     0x14d7800: stur            w1, [x0, #0xf]
    // 0x14d7804: ldur            x1, [fp, #-0x28]
    // 0x14d7808: StoreField: r0->field_13 = r1
    //     0x14d7808: stur            w1, [x0, #0x13]
    // 0x14d780c: ldur            x2, [fp, #-0x20]
    // 0x14d7810: r1 = Function 'pinCodeVerifyCallback':.
    //     0x14d7810: add             x1, PP, #0x41, lsl #12  ; [pp+0x41440] AnonymousClosure: (0x14d7894), in [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback (0x138fdcc)
    //     0x14d7814: ldr             x1, [x1, #0x440]
    // 0x14d7818: r0 = AllocateClosure()
    //     0x14d7818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d781c: mov             x1, x0
    // 0x14d7820: ldur            x0, [fp, #-0x30]
    // 0x14d7824: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d7824: stur            w1, [x0, #0x17]
    // 0x14d7828: ldur            x1, [fp, #-8]
    // 0x14d782c: StoreField: r0->field_1b = r1
    //     0x14d782c: stur            w1, [x0, #0x1b]
    // 0x14d7830: ldur            x2, [fp, #-0x10]
    // 0x14d7834: r1 = Function '<anonymous closure>':.
    //     0x14d7834: add             x1, PP, #0x41, lsl #12  ; [pp+0x41448] AnonymousClosure: (0x138fbbc), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x14d7838: ldr             x1, [x1, #0x448]
    // 0x14d783c: r0 = AllocateClosure()
    //     0x14d783c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7840: mov             x1, x0
    // 0x14d7844: ldur            x0, [fp, #-0x30]
    // 0x14d7848: StoreField: r0->field_1f = r1
    //     0x14d7848: stur            w1, [x0, #0x1f]
    // 0x14d784c: r1 = Instance_ValueKey
    //     0x14d784c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc48] Obj!ValueKey<String>@d5b3a1
    //     0x14d7850: ldr             x1, [x1, #0xc48]
    // 0x14d7854: StoreField: r0->field_7 = r1
    //     0x14d7854: stur            w1, [x0, #7]
    // 0x14d7858: r1 = <FlexParentData>
    //     0x14d7858: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d785c: ldr             x1, [x1, #0xe00]
    // 0x14d7860: r0 = Flexible()
    //     0x14d7860: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d7864: r1 = 1
    //     0x14d7864: movz            x1, #0x1
    // 0x14d7868: StoreField: r0->field_13 = r1
    //     0x14d7868: stur            x1, [x0, #0x13]
    // 0x14d786c: r1 = Instance_FlexFit
    //     0x14d786c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d7870: ldr             x1, [x1, #0xe20]
    // 0x14d7874: StoreField: r0->field_1b = r1
    //     0x14d7874: stur            w1, [x0, #0x1b]
    // 0x14d7878: ldur            x1, [fp, #-0x30]
    // 0x14d787c: StoreField: r0->field_b = r1
    //     0x14d787c: stur            w1, [x0, #0xb]
    // 0x14d7880: LeaveFrame
    //     0x14d7880: mov             SP, fp
    //     0x14d7884: ldp             fp, lr, [SP], #0x10
    // 0x14d7888: ret
    //     0x14d7888: ret             
    // 0x14d788c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d788c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7890: b               #0x14d7764
  }
  [closure] void pinCodeVerifyCallback(dynamic, String) {
    // ** addr: 0x14d7894, size: 0x3c
    // 0x14d7894: EnterFrame
    //     0x14d7894: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7898: mov             fp, SP
    // 0x14d789c: ldr             x0, [fp, #0x18]
    // 0x14d78a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d78a0: ldur            w1, [x0, #0x17]
    // 0x14d78a4: DecompressPointer r1
    //     0x14d78a4: add             x1, x1, HEAP, lsl #32
    // 0x14d78a8: CheckStackOverflow
    //     0x14d78a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d78ac: cmp             SP, x16
    //     0x14d78b0: b.ls            #0x14d78c8
    // 0x14d78b4: ldr             x2, [fp, #0x10]
    // 0x14d78b8: r0 = pinCodeVerifyCallback()
    //     0x14d78b8: bl              #0x138fdcc  ; [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback
    // 0x14d78bc: LeaveFrame
    //     0x14d78bc: mov             SP, fp
    //     0x14d78c0: ldp             fp, lr, [SP], #0x10
    // 0x14d78c4: ret
    //     0x14d78c4: ret             
    // 0x14d78c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d78c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d78cc: b               #0x14d78b4
  }
  [closure] CheckoutBagAccordion <anonymous closure>(dynamic) {
    // ** addr: 0x14d78d0, size: 0xf0
    // 0x14d78d0: EnterFrame
    //     0x14d78d0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d78d4: mov             fp, SP
    // 0x14d78d8: AllocStack(0x28)
    //     0x14d78d8: sub             SP, SP, #0x28
    // 0x14d78dc: SetupParameters()
    //     0x14d78dc: ldr             x0, [fp, #0x10]
    //     0x14d78e0: ldur            w2, [x0, #0x17]
    //     0x14d78e4: add             x2, x2, HEAP, lsl #32
    //     0x14d78e8: stur            x2, [fp, #-8]
    // 0x14d78ec: CheckStackOverflow
    //     0x14d78ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d78f0: cmp             SP, x16
    //     0x14d78f4: b.ls            #0x14d79b8
    // 0x14d78f8: LoadField: r1 = r2->field_f
    //     0x14d78f8: ldur            w1, [x2, #0xf]
    // 0x14d78fc: DecompressPointer r1
    //     0x14d78fc: add             x1, x1, HEAP, lsl #32
    // 0x14d7900: r0 = controller()
    //     0x14d7900: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7904: LoadField: r1 = r0->field_5f
    //     0x14d7904: ldur            w1, [x0, #0x5f]
    // 0x14d7908: DecompressPointer r1
    //     0x14d7908: add             x1, x1, HEAP, lsl #32
    // 0x14d790c: r0 = value()
    //     0x14d790c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7910: ldur            x2, [fp, #-8]
    // 0x14d7914: stur            x0, [fp, #-0x10]
    // 0x14d7918: LoadField: r1 = r2->field_f
    //     0x14d7918: ldur            w1, [x2, #0xf]
    // 0x14d791c: DecompressPointer r1
    //     0x14d791c: add             x1, x1, HEAP, lsl #32
    // 0x14d7920: r0 = controller()
    //     0x14d7920: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7924: LoadField: r1 = r0->field_5b
    //     0x14d7924: ldur            w1, [x0, #0x5b]
    // 0x14d7928: DecompressPointer r1
    //     0x14d7928: add             x1, x1, HEAP, lsl #32
    // 0x14d792c: r0 = value()
    //     0x14d792c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7930: ldur            x2, [fp, #-8]
    // 0x14d7934: stur            x0, [fp, #-0x18]
    // 0x14d7938: LoadField: r1 = r2->field_f
    //     0x14d7938: ldur            w1, [x2, #0xf]
    // 0x14d793c: DecompressPointer r1
    //     0x14d793c: add             x1, x1, HEAP, lsl #32
    // 0x14d7940: r0 = controller()
    //     0x14d7940: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7944: LoadField: r1 = r0->field_53
    //     0x14d7944: ldur            w1, [x0, #0x53]
    // 0x14d7948: DecompressPointer r1
    //     0x14d7948: add             x1, x1, HEAP, lsl #32
    // 0x14d794c: r0 = value()
    //     0x14d794c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7950: ldur            x2, [fp, #-8]
    // 0x14d7954: LoadField: r1 = r2->field_f
    //     0x14d7954: ldur            w1, [x2, #0xf]
    // 0x14d7958: DecompressPointer r1
    //     0x14d7958: add             x1, x1, HEAP, lsl #32
    // 0x14d795c: r0 = controller()
    //     0x14d795c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7960: LoadField: r1 = r0->field_8b
    //     0x14d7960: ldur            w1, [x0, #0x8b]
    // 0x14d7964: DecompressPointer r1
    //     0x14d7964: add             x1, x1, HEAP, lsl #32
    // 0x14d7968: stur            x1, [fp, #-0x20]
    // 0x14d796c: r0 = CheckoutBagAccordion()
    //     0x14d796c: bl              #0x1393984  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x14d7970: mov             x3, x0
    // 0x14d7974: ldur            x0, [fp, #-0x10]
    // 0x14d7978: stur            x3, [fp, #-0x28]
    // 0x14d797c: StoreField: r3->field_b = r0
    //     0x14d797c: stur            w0, [x3, #0xb]
    // 0x14d7980: ldur            x0, [fp, #-0x18]
    // 0x14d7984: StoreField: r3->field_f = r0
    //     0x14d7984: stur            w0, [x3, #0xf]
    // 0x14d7988: ldur            x0, [fp, #-0x20]
    // 0x14d798c: StoreField: r3->field_13 = r0
    //     0x14d798c: stur            w0, [x3, #0x13]
    // 0x14d7990: ldur            x2, [fp, #-8]
    // 0x14d7994: r1 = Function '<anonymous closure>':.
    //     0x14d7994: add             x1, PP, #0x41, lsl #12  ; [pp+0x41450] AnonymousClosure: (0x139093c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x14d7998: ldr             x1, [x1, #0x450]
    // 0x14d799c: r0 = AllocateClosure()
    //     0x14d799c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d79a0: mov             x1, x0
    // 0x14d79a4: ldur            x0, [fp, #-0x28]
    // 0x14d79a8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d79a8: stur            w1, [x0, #0x17]
    // 0x14d79ac: LeaveFrame
    //     0x14d79ac: mov             SP, fp
    //     0x14d79b0: ldp             fp, lr, [SP], #0x10
    // 0x14d79b4: ret
    //     0x14d79b4: ret             
    // 0x14d79b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d79b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d79bc: b               #0x14d78f8
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x14d79c0, size: 0x64
    // 0x14d79c0: EnterFrame
    //     0x14d79c0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d79c4: mov             fp, SP
    // 0x14d79c8: AllocStack(0x8)
    //     0x14d79c8: sub             SP, SP, #8
    // 0x14d79cc: SetupParameters()
    //     0x14d79cc: ldr             x0, [fp, #0x10]
    //     0x14d79d0: ldur            w1, [x0, #0x17]
    //     0x14d79d4: add             x1, x1, HEAP, lsl #32
    // 0x14d79d8: CheckStackOverflow
    //     0x14d79d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d79dc: cmp             SP, x16
    //     0x14d79e0: b.ls            #0x14d7a1c
    // 0x14d79e4: LoadField: r0 = r1->field_f
    //     0x14d79e4: ldur            w0, [x1, #0xf]
    // 0x14d79e8: DecompressPointer r0
    //     0x14d79e8: add             x0, x0, HEAP, lsl #32
    // 0x14d79ec: mov             x1, x0
    // 0x14d79f0: r0 = controller()
    //     0x14d79f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d79f4: LoadField: r1 = r0->field_7b
    //     0x14d79f4: ldur            w1, [x0, #0x7b]
    // 0x14d79f8: DecompressPointer r1
    //     0x14d79f8: add             x1, x1, HEAP, lsl #32
    // 0x14d79fc: r0 = value()
    //     0x14d79fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7a00: stur            x0, [fp, #-8]
    // 0x14d7a04: r0 = CheckoutBreadCrumb()
    //     0x14d7a04: bl              #0x13939f4  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x10)
    // 0x14d7a08: ldur            x1, [fp, #-8]
    // 0x14d7a0c: StoreField: r0->field_b = r1
    //     0x14d7a0c: stur            w1, [x0, #0xb]
    // 0x14d7a10: LeaveFrame
    //     0x14d7a10: mov             SP, fp
    //     0x14d7a14: ldp             fp, lr, [SP], #0x10
    // 0x14d7a18: ret
    //     0x14d7a18: ret             
    // 0x14d7a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7a1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7a20: b               #0x14d79e4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e02f4, size: 0x250
    // 0x15e02f4: EnterFrame
    //     0x15e02f4: stp             fp, lr, [SP, #-0x10]!
    //     0x15e02f8: mov             fp, SP
    // 0x15e02fc: AllocStack(0x28)
    //     0x15e02fc: sub             SP, SP, #0x28
    // 0x15e0300: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e0300: stur            x1, [fp, #-8]
    //     0x15e0304: stur            x2, [fp, #-0x10]
    // 0x15e0308: CheckStackOverflow
    //     0x15e0308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e030c: cmp             SP, x16
    //     0x15e0310: b.ls            #0x15e053c
    // 0x15e0314: r1 = 2
    //     0x15e0314: movz            x1, #0x2
    // 0x15e0318: r0 = AllocateContext()
    //     0x15e0318: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e031c: ldur            x1, [fp, #-8]
    // 0x15e0320: stur            x0, [fp, #-0x18]
    // 0x15e0324: StoreField: r0->field_f = r1
    //     0x15e0324: stur            w1, [x0, #0xf]
    // 0x15e0328: ldur            x2, [fp, #-0x10]
    // 0x15e032c: StoreField: r0->field_13 = r2
    //     0x15e032c: stur            w2, [x0, #0x13]
    // 0x15e0330: r0 = Obx()
    //     0x15e0330: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e0334: ldur            x2, [fp, #-0x18]
    // 0x15e0338: r1 = Function '<anonymous closure>':.
    //     0x15e0338: add             x1, PP, #0x41, lsl #12  ; [pp+0x41458] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15e033c: ldr             x1, [x1, #0x458]
    // 0x15e0340: stur            x0, [fp, #-0x10]
    // 0x15e0344: r0 = AllocateClosure()
    //     0x15e0344: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e0348: mov             x1, x0
    // 0x15e034c: ldur            x0, [fp, #-0x10]
    // 0x15e0350: StoreField: r0->field_b = r1
    //     0x15e0350: stur            w1, [x0, #0xb]
    // 0x15e0354: ldur            x1, [fp, #-8]
    // 0x15e0358: r0 = controller()
    //     0x15e0358: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e035c: LoadField: r1 = r0->field_6b
    //     0x15e035c: ldur            w1, [x0, #0x6b]
    // 0x15e0360: DecompressPointer r1
    //     0x15e0360: add             x1, x1, HEAP, lsl #32
    // 0x15e0364: r0 = value()
    //     0x15e0364: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0368: tbnz            w0, #4, #0x15e0400
    // 0x15e036c: ldur            x2, [fp, #-0x18]
    // 0x15e0370: LoadField: r1 = r2->field_13
    //     0x15e0370: ldur            w1, [x2, #0x13]
    // 0x15e0374: DecompressPointer r1
    //     0x15e0374: add             x1, x1, HEAP, lsl #32
    // 0x15e0378: r0 = of()
    //     0x15e0378: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e037c: LoadField: r1 = r0->field_5b
    //     0x15e037c: ldur            w1, [x0, #0x5b]
    // 0x15e0380: DecompressPointer r1
    //     0x15e0380: add             x1, x1, HEAP, lsl #32
    // 0x15e0384: stur            x1, [fp, #-8]
    // 0x15e0388: r0 = ColorFilter()
    //     0x15e0388: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e038c: mov             x1, x0
    // 0x15e0390: ldur            x0, [fp, #-8]
    // 0x15e0394: stur            x1, [fp, #-0x20]
    // 0x15e0398: StoreField: r1->field_7 = r0
    //     0x15e0398: stur            w0, [x1, #7]
    // 0x15e039c: r0 = Instance_BlendMode
    //     0x15e039c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e03a0: ldr             x0, [x0, #0xb30]
    // 0x15e03a4: StoreField: r1->field_b = r0
    //     0x15e03a4: stur            w0, [x1, #0xb]
    // 0x15e03a8: r2 = 1
    //     0x15e03a8: movz            x2, #0x1
    // 0x15e03ac: StoreField: r1->field_13 = r2
    //     0x15e03ac: stur            x2, [x1, #0x13]
    // 0x15e03b0: r0 = SvgPicture()
    //     0x15e03b0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e03b4: stur            x0, [fp, #-8]
    // 0x15e03b8: ldur            x16, [fp, #-0x20]
    // 0x15e03bc: str             x16, [SP]
    // 0x15e03c0: mov             x1, x0
    // 0x15e03c4: r2 = "assets/images/search.svg"
    //     0x15e03c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e03c8: ldr             x2, [x2, #0xa30]
    // 0x15e03cc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e03cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e03d0: ldr             x4, [x4, #0xa38]
    // 0x15e03d4: r0 = SvgPicture.asset()
    //     0x15e03d4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e03d8: r0 = Align()
    //     0x15e03d8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e03dc: r3 = Instance_Alignment
    //     0x15e03dc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e03e0: ldr             x3, [x3, #0xb10]
    // 0x15e03e4: StoreField: r0->field_f = r3
    //     0x15e03e4: stur            w3, [x0, #0xf]
    // 0x15e03e8: r4 = 1.000000
    //     0x15e03e8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e03ec: StoreField: r0->field_13 = r4
    //     0x15e03ec: stur            w4, [x0, #0x13]
    // 0x15e03f0: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e03f0: stur            w4, [x0, #0x17]
    // 0x15e03f4: ldur            x1, [fp, #-8]
    // 0x15e03f8: StoreField: r0->field_b = r1
    //     0x15e03f8: stur            w1, [x0, #0xb]
    // 0x15e03fc: b               #0x15e04b0
    // 0x15e0400: ldur            x5, [fp, #-0x18]
    // 0x15e0404: r4 = 1.000000
    //     0x15e0404: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0408: r0 = Instance_BlendMode
    //     0x15e0408: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e040c: ldr             x0, [x0, #0xb30]
    // 0x15e0410: r3 = Instance_Alignment
    //     0x15e0410: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e0414: ldr             x3, [x3, #0xb10]
    // 0x15e0418: r2 = 1
    //     0x15e0418: movz            x2, #0x1
    // 0x15e041c: LoadField: r1 = r5->field_13
    //     0x15e041c: ldur            w1, [x5, #0x13]
    // 0x15e0420: DecompressPointer r1
    //     0x15e0420: add             x1, x1, HEAP, lsl #32
    // 0x15e0424: r0 = of()
    //     0x15e0424: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e0428: LoadField: r1 = r0->field_5b
    //     0x15e0428: ldur            w1, [x0, #0x5b]
    // 0x15e042c: DecompressPointer r1
    //     0x15e042c: add             x1, x1, HEAP, lsl #32
    // 0x15e0430: stur            x1, [fp, #-8]
    // 0x15e0434: r0 = ColorFilter()
    //     0x15e0434: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e0438: mov             x1, x0
    // 0x15e043c: ldur            x0, [fp, #-8]
    // 0x15e0440: stur            x1, [fp, #-0x20]
    // 0x15e0444: StoreField: r1->field_7 = r0
    //     0x15e0444: stur            w0, [x1, #7]
    // 0x15e0448: r0 = Instance_BlendMode
    //     0x15e0448: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e044c: ldr             x0, [x0, #0xb30]
    // 0x15e0450: StoreField: r1->field_b = r0
    //     0x15e0450: stur            w0, [x1, #0xb]
    // 0x15e0454: r0 = 1
    //     0x15e0454: movz            x0, #0x1
    // 0x15e0458: StoreField: r1->field_13 = r0
    //     0x15e0458: stur            x0, [x1, #0x13]
    // 0x15e045c: r0 = SvgPicture()
    //     0x15e045c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e0460: stur            x0, [fp, #-8]
    // 0x15e0464: ldur            x16, [fp, #-0x20]
    // 0x15e0468: str             x16, [SP]
    // 0x15e046c: mov             x1, x0
    // 0x15e0470: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e0470: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e0474: ldr             x2, [x2, #0xa40]
    // 0x15e0478: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e0478: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e047c: ldr             x4, [x4, #0xa38]
    // 0x15e0480: r0 = SvgPicture.asset()
    //     0x15e0480: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e0484: r0 = Align()
    //     0x15e0484: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e0488: mov             x1, x0
    // 0x15e048c: r0 = Instance_Alignment
    //     0x15e048c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e0490: ldr             x0, [x0, #0xb10]
    // 0x15e0494: StoreField: r1->field_f = r0
    //     0x15e0494: stur            w0, [x1, #0xf]
    // 0x15e0498: r0 = 1.000000
    //     0x15e0498: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e049c: StoreField: r1->field_13 = r0
    //     0x15e049c: stur            w0, [x1, #0x13]
    // 0x15e04a0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e04a0: stur            w0, [x1, #0x17]
    // 0x15e04a4: ldur            x0, [fp, #-8]
    // 0x15e04a8: StoreField: r1->field_b = r0
    //     0x15e04a8: stur            w0, [x1, #0xb]
    // 0x15e04ac: mov             x0, x1
    // 0x15e04b0: stur            x0, [fp, #-8]
    // 0x15e04b4: r0 = InkWell()
    //     0x15e04b4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e04b8: mov             x3, x0
    // 0x15e04bc: ldur            x0, [fp, #-8]
    // 0x15e04c0: stur            x3, [fp, #-0x20]
    // 0x15e04c4: StoreField: r3->field_b = r0
    //     0x15e04c4: stur            w0, [x3, #0xb]
    // 0x15e04c8: ldur            x2, [fp, #-0x18]
    // 0x15e04cc: r1 = Function '<anonymous closure>':.
    //     0x15e04cc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41460] AnonymousClosure: (0x15e0544), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::appBar (0x15e02f4)
    //     0x15e04d0: ldr             x1, [x1, #0x460]
    // 0x15e04d4: r0 = AllocateClosure()
    //     0x15e04d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e04d8: ldur            x2, [fp, #-0x20]
    // 0x15e04dc: StoreField: r2->field_f = r0
    //     0x15e04dc: stur            w0, [x2, #0xf]
    // 0x15e04e0: r0 = true
    //     0x15e04e0: add             x0, NULL, #0x20  ; true
    // 0x15e04e4: StoreField: r2->field_43 = r0
    //     0x15e04e4: stur            w0, [x2, #0x43]
    // 0x15e04e8: r1 = Instance_BoxShape
    //     0x15e04e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e04ec: ldr             x1, [x1, #0x80]
    // 0x15e04f0: StoreField: r2->field_47 = r1
    //     0x15e04f0: stur            w1, [x2, #0x47]
    // 0x15e04f4: StoreField: r2->field_6f = r0
    //     0x15e04f4: stur            w0, [x2, #0x6f]
    // 0x15e04f8: r1 = false
    //     0x15e04f8: add             x1, NULL, #0x30  ; false
    // 0x15e04fc: StoreField: r2->field_73 = r1
    //     0x15e04fc: stur            w1, [x2, #0x73]
    // 0x15e0500: StoreField: r2->field_83 = r0
    //     0x15e0500: stur            w0, [x2, #0x83]
    // 0x15e0504: StoreField: r2->field_7b = r1
    //     0x15e0504: stur            w1, [x2, #0x7b]
    // 0x15e0508: r0 = AppBar()
    //     0x15e0508: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e050c: stur            x0, [fp, #-8]
    // 0x15e0510: ldur            x16, [fp, #-0x10]
    // 0x15e0514: str             x16, [SP]
    // 0x15e0518: mov             x1, x0
    // 0x15e051c: ldur            x2, [fp, #-0x20]
    // 0x15e0520: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e0520: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e0524: ldr             x4, [x4, #0xf00]
    // 0x15e0528: r0 = AppBar()
    //     0x15e0528: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e052c: ldur            x0, [fp, #-8]
    // 0x15e0530: LeaveFrame
    //     0x15e0530: mov             SP, fp
    //     0x15e0534: ldp             fp, lr, [SP], #0x10
    // 0x15e0538: ret
    //     0x15e0538: ret             
    // 0x15e053c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e053c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0540: b               #0x15e0314
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e0544, size: 0xc8
    // 0x15e0544: EnterFrame
    //     0x15e0544: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0548: mov             fp, SP
    // 0x15e054c: AllocStack(0x18)
    //     0x15e054c: sub             SP, SP, #0x18
    // 0x15e0550: SetupParameters()
    //     0x15e0550: ldr             x0, [fp, #0x10]
    //     0x15e0554: ldur            w3, [x0, #0x17]
    //     0x15e0558: add             x3, x3, HEAP, lsl #32
    //     0x15e055c: stur            x3, [fp, #-8]
    // 0x15e0560: CheckStackOverflow
    //     0x15e0560: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e0564: cmp             SP, x16
    //     0x15e0568: b.ls            #0x15e0604
    // 0x15e056c: LoadField: r1 = r3->field_f
    //     0x15e056c: ldur            w1, [x3, #0xf]
    // 0x15e0570: DecompressPointer r1
    //     0x15e0570: add             x1, x1, HEAP, lsl #32
    // 0x15e0574: r2 = false
    //     0x15e0574: add             x2, NULL, #0x30  ; false
    // 0x15e0578: r0 = showLoading()
    //     0x15e0578: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e057c: ldur            x0, [fp, #-8]
    // 0x15e0580: LoadField: r1 = r0->field_f
    //     0x15e0580: ldur            w1, [x0, #0xf]
    // 0x15e0584: DecompressPointer r1
    //     0x15e0584: add             x1, x1, HEAP, lsl #32
    // 0x15e0588: r0 = controller()
    //     0x15e0588: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e058c: LoadField: r1 = r0->field_6b
    //     0x15e058c: ldur            w1, [x0, #0x6b]
    // 0x15e0590: DecompressPointer r1
    //     0x15e0590: add             x1, x1, HEAP, lsl #32
    // 0x15e0594: r0 = value()
    //     0x15e0594: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0598: tbnz            w0, #4, #0x15e05d0
    // 0x15e059c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e059c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e05a0: ldr             x0, [x0, #0x1c80]
    //     0x15e05a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e05a8: cmp             w0, w16
    //     0x15e05ac: b.ne            #0x15e05b8
    //     0x15e05b0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e05b4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e05b8: r16 = "/search"
    //     0x15e05b8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e05bc: ldr             x16, [x16, #0x838]
    // 0x15e05c0: stp             x16, NULL, [SP]
    // 0x15e05c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e05c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e05c8: r0 = GetNavigation.toNamed()
    //     0x15e05c8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e05cc: b               #0x15e05f4
    // 0x15e05d0: ldur            x0, [fp, #-8]
    // 0x15e05d4: LoadField: r1 = r0->field_f
    //     0x15e05d4: ldur            w1, [x0, #0xf]
    // 0x15e05d8: DecompressPointer r1
    //     0x15e05d8: add             x1, x1, HEAP, lsl #32
    // 0x15e05dc: r2 = false
    //     0x15e05dc: add             x2, NULL, #0x30  ; false
    // 0x15e05e0: r0 = showLoading()
    //     0x15e05e0: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e05e4: ldur            x0, [fp, #-8]
    // 0x15e05e8: LoadField: r1 = r0->field_f
    //     0x15e05e8: ldur            w1, [x0, #0xf]
    // 0x15e05ec: DecompressPointer r1
    //     0x15e05ec: add             x1, x1, HEAP, lsl #32
    // 0x15e05f0: r0 = getBack()
    //     0x15e05f0: bl              #0x14d768c  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x15e05f4: r0 = Null
    //     0x15e05f4: mov             x0, NULL
    // 0x15e05f8: LeaveFrame
    //     0x15e05f8: mov             SP, fp
    //     0x15e05fc: ldp             fp, lr, [SP], #0x10
    // 0x15e0600: ret
    //     0x15e0600: ret             
    // 0x15e0604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e0604: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0608: b               #0x15e056c
  }
}
