// lib: , url: package:easy_stepper/src/core/base_step.dart

// class id: 1049625, size: 0x8
class :: {
}

// class id: 4479, size: 0x68, field offset: 0xc
//   const constructor, 
class BaseStep extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x12995b4, size: 0x3d8
    // 0x12995b4: EnterFrame
    //     0x12995b4: stp             fp, lr, [SP, #-0x10]!
    //     0x12995b8: mov             fp, SP
    // 0x12995bc: AllocStack(0x68)
    //     0x12995bc: sub             SP, SP, #0x68
    // 0x12995c0: d0 = 20.000000
    //     0x12995c0: fmov            d0, #20.00000000
    // 0x12995c4: stur            x1, [fp, #-0x10]
    // 0x12995c8: stur            x2, [fp, #-0x18]
    // 0x12995cc: CheckStackOverflow
    //     0x12995cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12995d0: cmp             SP, x16
    //     0x12995d4: b.ls            #0x129996c
    // 0x12995d8: LoadField: d1 = r1->field_3b
    //     0x12995d8: ldur            d1, [x1, #0x3b]
    // 0x12995dc: fadd            d2, d1, d0
    // 0x12995e0: stur            d2, [fp, #-0x40]
    // 0x12995e4: LoadField: r0 = r1->field_1b
    //     0x12995e4: ldur            w0, [x1, #0x1b]
    // 0x12995e8: DecompressPointer r0
    //     0x12995e8: add             x0, x0, HEAP, lsl #32
    // 0x12995ec: stur            x0, [fp, #-8]
    // 0x12995f0: r0 = BaseStepDelegate()
    //     0x12995f0: bl              #0x1299b00  ; AllocateBaseStepDelegateStub -> BaseStepDelegate (size=0x20)
    // 0x12995f4: d0 = 10.000000
    //     0x12995f4: fmov            d0, #10.00000000
    // 0x12995f8: stur            x0, [fp, #-0x20]
    // 0x12995fc: StoreField: r0->field_f = d0
    //     0x12995fc: stur            d0, [x0, #0xf]
    // 0x1299600: r1 = Instance_Axis
    //     0x1299600: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1299604: ArrayStore: r0[0] = r1  ; List_4
    //     0x1299604: stur            w1, [x0, #0x17]
    // 0x1299608: r4 = false
    //     0x1299608: add             x4, NULL, #0x30  ; false
    // 0x129960c: StoreField: r0->field_1b = r4
    //     0x129960c: stur            w4, [x0, #0x1b]
    // 0x1299610: ldur            x6, [fp, #-0x10]
    // 0x1299614: LoadField: r3 = r6->field_13
    //     0x1299614: ldur            w3, [x6, #0x13]
    // 0x1299618: DecompressPointer r3
    //     0x1299618: add             x3, x3, HEAP, lsl #32
    // 0x129961c: LoadField: r5 = r6->field_f
    //     0x129961c: ldur            w5, [x6, #0xf]
    // 0x1299620: DecompressPointer r5
    //     0x1299620: add             x5, x5, HEAP, lsl #32
    // 0x1299624: mov             x1, x6
    // 0x1299628: ldur            x2, [fp, #-0x18]
    // 0x129962c: r0 = _handleColor()
    //     0x129962c: bl              #0x1299a8c  ; [package:easy_stepper/src/core/base_step.dart] BaseStep::_handleColor
    // 0x1299630: stur            x0, [fp, #-0x18]
    // 0x1299634: r0 = BoxDecoration()
    //     0x1299634: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1299638: mov             x2, x0
    // 0x129963c: ldur            x0, [fp, #-0x18]
    // 0x1299640: stur            x2, [fp, #-0x28]
    // 0x1299644: StoreField: r2->field_7 = r0
    //     0x1299644: stur            w0, [x2, #7]
    // 0x1299648: r0 = Instance_BoxShape
    //     0x1299648: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x129964c: ldr             x0, [x0, #0x970]
    // 0x1299650: StoreField: r2->field_23 = r0
    //     0x1299650: stur            w0, [x2, #0x23]
    // 0x1299654: ldur            x1, [fp, #-0x10]
    // 0x1299658: r0 = _buildIcon()
    //     0x1299658: bl              #0x1299a24  ; [package:easy_stepper/src/core/base_step.dart] BaseStep::_buildIcon
    // 0x129965c: stur            x0, [fp, #-0x18]
    // 0x1299660: r0 = Container()
    //     0x1299660: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1299664: stur            x0, [fp, #-0x30]
    // 0x1299668: r16 = 20.000000
    //     0x1299668: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x129966c: ldr             x16, [x16, #0xac8]
    // 0x1299670: r30 = 20.000000
    //     0x1299670: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1299674: ldr             lr, [lr, #0xac8]
    // 0x1299678: stp             lr, x16, [SP, #0x18]
    // 0x129967c: ldur            x16, [fp, #-0x28]
    // 0x1299680: r30 = Instance_Alignment
    //     0x1299680: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1299684: ldr             lr, [lr, #0xb10]
    // 0x1299688: stp             lr, x16, [SP, #8]
    // 0x129968c: ldur            x16, [fp, #-0x18]
    // 0x1299690: str             x16, [SP]
    // 0x1299694: mov             x1, x0
    // 0x1299698: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x1299698: add             x4, PP, #0x55, lsl #12  ; [pp+0x559e8] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x129969c: ldr             x4, [x4, #0x9e8]
    // 0x12996a0: r0 = Container()
    //     0x12996a0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12996a4: r0 = InkWell()
    //     0x12996a4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x12996a8: mov             x1, x0
    // 0x12996ac: ldur            x0, [fp, #-0x30]
    // 0x12996b0: stur            x1, [fp, #-0x18]
    // 0x12996b4: StoreField: r1->field_b = r0
    //     0x12996b4: stur            w0, [x1, #0xb]
    // 0x12996b8: ldur            x0, [fp, #-8]
    // 0x12996bc: StoreField: r1->field_f = r0
    //     0x12996bc: stur            w0, [x1, #0xf]
    // 0x12996c0: r2 = true
    //     0x12996c0: add             x2, NULL, #0x20  ; true
    // 0x12996c4: StoreField: r1->field_43 = r2
    //     0x12996c4: stur            w2, [x1, #0x43]
    // 0x12996c8: r3 = Instance_BoxShape
    //     0x12996c8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12996cc: ldr             x3, [x3, #0x80]
    // 0x12996d0: StoreField: r1->field_47 = r3
    //     0x12996d0: stur            w3, [x1, #0x47]
    // 0x12996d4: r4 = 10.000000
    //     0x12996d4: ldr             x4, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x12996d8: StoreField: r1->field_4b = r4
    //     0x12996d8: stur            w4, [x1, #0x4b]
    // 0x12996dc: StoreField: r1->field_6f = r2
    //     0x12996dc: stur            w2, [x1, #0x6f]
    // 0x12996e0: r4 = false
    //     0x12996e0: add             x4, NULL, #0x30  ; false
    // 0x12996e4: StoreField: r1->field_73 = r4
    //     0x12996e4: stur            w4, [x1, #0x73]
    // 0x12996e8: StoreField: r1->field_83 = r4
    //     0x12996e8: stur            w4, [x1, #0x83]
    // 0x12996ec: StoreField: r1->field_7b = r4
    //     0x12996ec: stur            w4, [x1, #0x7b]
    // 0x12996f0: r0 = Material()
    //     0x12996f0: bl              #0xaabb1c  ; AllocateMaterialStub -> Material (size=0x44)
    // 0x12996f4: mov             x2, x0
    // 0x12996f8: r0 = Instance_MaterialType
    //     0x12996f8: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bd20] Obj!MaterialType@d741e1
    //     0x12996fc: ldr             x0, [x0, #0xd20]
    // 0x1299700: stur            x2, [fp, #-0x28]
    // 0x1299704: StoreField: r2->field_f = r0
    //     0x1299704: stur            w0, [x2, #0xf]
    // 0x1299708: ArrayStore: r2[0] = rZR  ; List_8
    //     0x1299708: stur            xzr, [x2, #0x17]
    // 0x129970c: r0 = Instance_Color
    //     0x129970c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1299710: ldr             x0, [x0, #0xf88]
    // 0x1299714: StoreField: r2->field_1f = r0
    //     0x1299714: stur            w0, [x2, #0x1f]
    // 0x1299718: r0 = Instance_CircleBorder
    //     0x1299718: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bf50] Obj!CircleBorder@d5ad41
    //     0x129971c: ldr             x0, [x0, #0xf50]
    // 0x1299720: StoreField: r2->field_2f = r0
    //     0x1299720: stur            w0, [x2, #0x2f]
    // 0x1299724: r0 = true
    //     0x1299724: add             x0, NULL, #0x20  ; true
    // 0x1299728: StoreField: r2->field_33 = r0
    //     0x1299728: stur            w0, [x2, #0x33]
    // 0x129972c: r1 = Instance_Clip
    //     0x129972c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x1299730: ldr             x1, [x1, #0x138]
    // 0x1299734: StoreField: r2->field_37 = r1
    //     0x1299734: stur            w1, [x2, #0x37]
    // 0x1299738: r1 = Instance_Duration
    //     0x1299738: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0x129973c: ldr             x1, [x1, #0x150]
    // 0x1299740: StoreField: r2->field_3b = r1
    //     0x1299740: stur            w1, [x2, #0x3b]
    // 0x1299744: ldur            x1, [fp, #-0x18]
    // 0x1299748: StoreField: r2->field_b = r1
    //     0x1299748: stur            w1, [x2, #0xb]
    // 0x129974c: r3 = false
    //     0x129974c: add             x3, NULL, #0x30  ; false
    // 0x1299750: StoreField: r2->field_13 = r3
    //     0x1299750: stur            w3, [x2, #0x13]
    // 0x1299754: r1 = <MultiChildLayoutParentData>
    //     0x1299754: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bd88] TypeArguments: <MultiChildLayoutParentData>
    //     0x1299758: ldr             x1, [x1, #0xd88]
    // 0x129975c: r0 = LayoutId()
    //     0x129975c: bl              #0xc49910  ; AllocateLayoutIdStub -> LayoutId (size=0x18)
    // 0x1299760: mov             x2, x0
    // 0x1299764: r0 = Instance_BaseStepElem
    //     0x1299764: add             x0, PP, #0x6e, lsl #12  ; [pp+0x6ec78] Obj!BaseStepElem@d75021
    //     0x1299768: ldr             x0, [x0, #0xc78]
    // 0x129976c: stur            x2, [fp, #-0x18]
    // 0x1299770: StoreField: r2->field_13 = r0
    //     0x1299770: stur            w0, [x2, #0x13]
    // 0x1299774: r1 = <Object>
    //     0x1299774: ldr             x1, [PP, #0x768]  ; [pp+0x768] TypeArguments: <Object>
    // 0x1299778: r0 = ValueKey()
    //     0x1299778: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0x129977c: mov             x1, x0
    // 0x1299780: r0 = Instance_BaseStepElem
    //     0x1299780: add             x0, PP, #0x6e, lsl #12  ; [pp+0x6ec78] Obj!BaseStepElem@d75021
    //     0x1299784: ldr             x0, [x0, #0xc78]
    // 0x1299788: StoreField: r1->field_b = r0
    //     0x1299788: stur            w0, [x1, #0xb]
    // 0x129978c: ldur            x2, [fp, #-0x28]
    // 0x1299790: ldur            x0, [fp, #-0x18]
    // 0x1299794: StoreField: r0->field_b = r2
    //     0x1299794: stur            w2, [x0, #0xb]
    // 0x1299798: StoreField: r0->field_7 = r1
    //     0x1299798: stur            w1, [x0, #7]
    // 0x129979c: r1 = Null
    //     0x129979c: mov             x1, NULL
    // 0x12997a0: r2 = 2
    //     0x12997a0: movz            x2, #0x2
    // 0x12997a4: r0 = AllocateArray()
    //     0x12997a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12997a8: mov             x2, x0
    // 0x12997ac: ldur            x0, [fp, #-0x18]
    // 0x12997b0: stur            x2, [fp, #-0x28]
    // 0x12997b4: StoreField: r2->field_f = r0
    //     0x12997b4: stur            w0, [x2, #0xf]
    // 0x12997b8: r1 = <Widget>
    //     0x12997b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12997bc: r0 = AllocateGrowableArray()
    //     0x12997bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12997c0: mov             x2, x0
    // 0x12997c4: ldur            x0, [fp, #-0x28]
    // 0x12997c8: stur            x2, [fp, #-0x18]
    // 0x12997cc: StoreField: r2->field_f = r0
    //     0x12997cc: stur            w0, [x2, #0xf]
    // 0x12997d0: r0 = 2
    //     0x12997d0: movz            x0, #0x2
    // 0x12997d4: StoreField: r2->field_b = r0
    //     0x12997d4: stur            w0, [x2, #0xb]
    // 0x12997d8: ldur            x1, [fp, #-0x10]
    // 0x12997dc: r0 = _buildStepTitle()
    //     0x12997dc: bl              #0x129998c  ; [package:easy_stepper/src/core/base_step.dart] BaseStep::_buildStepTitle
    // 0x12997e0: r1 = <MultiChildLayoutParentData>
    //     0x12997e0: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bd88] TypeArguments: <MultiChildLayoutParentData>
    //     0x12997e4: ldr             x1, [x1, #0xd88]
    // 0x12997e8: stur            x0, [fp, #-0x10]
    // 0x12997ec: r0 = LayoutId()
    //     0x12997ec: bl              #0xc49910  ; AllocateLayoutIdStub -> LayoutId (size=0x18)
    // 0x12997f0: mov             x2, x0
    // 0x12997f4: r0 = Instance_BaseStepElem
    //     0x12997f4: add             x0, PP, #0x6e, lsl #12  ; [pp+0x6ec80] Obj!BaseStepElem@d75001
    //     0x12997f8: ldr             x0, [x0, #0xc80]
    // 0x12997fc: stur            x2, [fp, #-0x28]
    // 0x1299800: StoreField: r2->field_13 = r0
    //     0x1299800: stur            w0, [x2, #0x13]
    // 0x1299804: r1 = <Object>
    //     0x1299804: ldr             x1, [PP, #0x768]  ; [pp+0x768] TypeArguments: <Object>
    // 0x1299808: r0 = ValueKey()
    //     0x1299808: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0x129980c: mov             x1, x0
    // 0x1299810: r0 = Instance_BaseStepElem
    //     0x1299810: add             x0, PP, #0x6e, lsl #12  ; [pp+0x6ec80] Obj!BaseStepElem@d75001
    //     0x1299814: ldr             x0, [x0, #0xc80]
    // 0x1299818: StoreField: r1->field_b = r0
    //     0x1299818: stur            w0, [x1, #0xb]
    // 0x129981c: ldur            x2, [fp, #-0x10]
    // 0x1299820: ldur            x0, [fp, #-0x28]
    // 0x1299824: StoreField: r0->field_b = r2
    //     0x1299824: stur            w2, [x0, #0xb]
    // 0x1299828: StoreField: r0->field_7 = r1
    //     0x1299828: stur            w1, [x0, #7]
    // 0x129982c: ldur            x2, [fp, #-0x18]
    // 0x1299830: LoadField: r1 = r2->field_b
    //     0x1299830: ldur            w1, [x2, #0xb]
    // 0x1299834: LoadField: r3 = r2->field_f
    //     0x1299834: ldur            w3, [x2, #0xf]
    // 0x1299838: DecompressPointer r3
    //     0x1299838: add             x3, x3, HEAP, lsl #32
    // 0x129983c: LoadField: r4 = r3->field_b
    //     0x129983c: ldur            w4, [x3, #0xb]
    // 0x1299840: r3 = LoadInt32Instr(r1)
    //     0x1299840: sbfx            x3, x1, #1, #0x1f
    // 0x1299844: stur            x3, [fp, #-0x38]
    // 0x1299848: r1 = LoadInt32Instr(r4)
    //     0x1299848: sbfx            x1, x4, #1, #0x1f
    // 0x129984c: cmp             x3, x1
    // 0x1299850: b.ne            #0x129985c
    // 0x1299854: mov             x1, x2
    // 0x1299858: r0 = _growToNextCapacity()
    //     0x1299858: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x129985c: ldur            d0, [fp, #-0x40]
    // 0x1299860: ldur            x5, [fp, #-0x20]
    // 0x1299864: ldur            x4, [fp, #-8]
    // 0x1299868: ldur            x2, [fp, #-0x18]
    // 0x129986c: ldur            x3, [fp, #-0x38]
    // 0x1299870: add             x0, x3, #1
    // 0x1299874: lsl             x1, x0, #1
    // 0x1299878: StoreField: r2->field_b = r1
    //     0x1299878: stur            w1, [x2, #0xb]
    // 0x129987c: LoadField: r1 = r2->field_f
    //     0x129987c: ldur            w1, [x2, #0xf]
    // 0x1299880: DecompressPointer r1
    //     0x1299880: add             x1, x1, HEAP, lsl #32
    // 0x1299884: ldur            x0, [fp, #-0x28]
    // 0x1299888: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1299888: add             x25, x1, x3, lsl #2
    //     0x129988c: add             x25, x25, #0xf
    //     0x1299890: str             w0, [x25]
    //     0x1299894: tbz             w0, #0, #0x12998b0
    //     0x1299898: ldurb           w16, [x1, #-1]
    //     0x129989c: ldurb           w17, [x0, #-1]
    //     0x12998a0: and             x16, x17, x16, lsr #2
    //     0x12998a4: tst             x16, HEAP, lsr #32
    //     0x12998a8: b.eq            #0x12998b0
    //     0x12998ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12998b0: r0 = CustomMultiChildLayout()
    //     0x12998b0: bl              #0xc49f6c  ; AllocateCustomMultiChildLayoutStub -> CustomMultiChildLayout (size=0x14)
    // 0x12998b4: mov             x1, x0
    // 0x12998b8: ldur            x0, [fp, #-0x20]
    // 0x12998bc: stur            x1, [fp, #-0x10]
    // 0x12998c0: StoreField: r1->field_f = r0
    //     0x12998c0: stur            w0, [x1, #0xf]
    // 0x12998c4: ldur            x0, [fp, #-0x18]
    // 0x12998c8: StoreField: r1->field_b = r0
    //     0x12998c8: stur            w0, [x1, #0xb]
    // 0x12998cc: r0 = InkWell()
    //     0x12998cc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x12998d0: mov             x1, x0
    // 0x12998d4: ldur            x0, [fp, #-0x10]
    // 0x12998d8: stur            x1, [fp, #-0x18]
    // 0x12998dc: StoreField: r1->field_b = r0
    //     0x12998dc: stur            w0, [x1, #0xb]
    // 0x12998e0: ldur            x0, [fp, #-8]
    // 0x12998e4: StoreField: r1->field_f = r0
    //     0x12998e4: stur            w0, [x1, #0xf]
    // 0x12998e8: r0 = true
    //     0x12998e8: add             x0, NULL, #0x20  ; true
    // 0x12998ec: StoreField: r1->field_43 = r0
    //     0x12998ec: stur            w0, [x1, #0x43]
    // 0x12998f0: r2 = Instance_BoxShape
    //     0x12998f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12998f4: ldr             x2, [x2, #0x80]
    // 0x12998f8: StoreField: r1->field_47 = r2
    //     0x12998f8: stur            w2, [x1, #0x47]
    // 0x12998fc: StoreField: r1->field_6f = r0
    //     0x12998fc: stur            w0, [x1, #0x6f]
    // 0x1299900: r0 = false
    //     0x1299900: add             x0, NULL, #0x30  ; false
    // 0x1299904: StoreField: r1->field_73 = r0
    //     0x1299904: stur            w0, [x1, #0x73]
    // 0x1299908: StoreField: r1->field_83 = r0
    //     0x1299908: stur            w0, [x1, #0x83]
    // 0x129990c: StoreField: r1->field_7b = r0
    //     0x129990c: stur            w0, [x1, #0x7b]
    // 0x1299910: ldur            d0, [fp, #-0x40]
    // 0x1299914: r0 = inline_Allocate_Double()
    //     0x1299914: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1299918: add             x0, x0, #0x10
    //     0x129991c: cmp             x2, x0
    //     0x1299920: b.ls            #0x1299974
    //     0x1299924: str             x0, [THR, #0x50]  ; THR::top
    //     0x1299928: sub             x0, x0, #0xf
    //     0x129992c: movz            x2, #0xe15c
    //     0x1299930: movk            x2, #0x3, lsl #16
    //     0x1299934: stur            x2, [x0, #-1]
    // 0x1299938: StoreField: r0->field_7 = d0
    //     0x1299938: stur            d0, [x0, #7]
    // 0x129993c: stur            x0, [fp, #-8]
    // 0x1299940: r0 = SizedBox()
    //     0x1299940: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1299944: ldur            x1, [fp, #-8]
    // 0x1299948: StoreField: r0->field_f = r1
    //     0x1299948: stur            w1, [x0, #0xf]
    // 0x129994c: r1 = 60.000000
    //     0x129994c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x1299950: ldr             x1, [x1, #0x110]
    // 0x1299954: StoreField: r0->field_13 = r1
    //     0x1299954: stur            w1, [x0, #0x13]
    // 0x1299958: ldur            x1, [fp, #-0x18]
    // 0x129995c: StoreField: r0->field_b = r1
    //     0x129995c: stur            w1, [x0, #0xb]
    // 0x1299960: LeaveFrame
    //     0x1299960: mov             SP, fp
    //     0x1299964: ldp             fp, lr, [SP], #0x10
    // 0x1299968: ret
    //     0x1299968: ret             
    // 0x129996c: r0 = StackOverflowSharedWithFPURegs()
    //     0x129996c: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x1299970: b               #0x12995d8
    // 0x1299974: SaveReg d0
    //     0x1299974: str             q0, [SP, #-0x10]!
    // 0x1299978: SaveReg r1
    //     0x1299978: str             x1, [SP, #-8]!
    // 0x129997c: r0 = AllocateDouble()
    //     0x129997c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1299980: RestoreReg r1
    //     0x1299980: ldr             x1, [SP], #8
    // 0x1299984: RestoreReg d0
    //     0x1299984: ldr             q0, [SP], #0x10
    // 0x1299988: b               #0x1299938
  }
  _ _buildStepTitle(/* No info */) {
    // ** addr: 0x129998c, size: 0x98
    // 0x129998c: EnterFrame
    //     0x129998c: stp             fp, lr, [SP, #-0x10]!
    //     0x1299990: mov             fp, SP
    // 0x1299994: AllocStack(0x10)
    //     0x1299994: sub             SP, SP, #0x10
    // 0x1299998: d0 = 20.000000
    //     0x1299998: fmov            d0, #20.00000000
    // 0x129999c: LoadField: d1 = r1->field_3b
    //     0x129999c: ldur            d1, [x1, #0x3b]
    // 0x12999a0: fadd            d2, d1, d0
    // 0x12999a4: LoadField: d0 = r1->field_53
    //     0x12999a4: ldur            d0, [x1, #0x53]
    // 0x12999a8: fadd            d1, d2, d0
    // 0x12999ac: LoadField: r0 = r1->field_b
    //     0x12999ac: ldur            w0, [x1, #0xb]
    // 0x12999b0: DecompressPointer r0
    //     0x12999b0: add             x0, x0, HEAP, lsl #32
    // 0x12999b4: LoadField: r1 = r0->field_b
    //     0x12999b4: ldur            w1, [x0, #0xb]
    // 0x12999b8: DecompressPointer r1
    //     0x12999b8: add             x1, x1, HEAP, lsl #32
    // 0x12999bc: stur            x1, [fp, #-0x10]
    // 0x12999c0: r0 = inline_Allocate_Double()
    //     0x12999c0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x12999c4: add             x0, x0, #0x10
    //     0x12999c8: cmp             x2, x0
    //     0x12999cc: b.ls            #0x1299a0c
    //     0x12999d0: str             x0, [THR, #0x50]  ; THR::top
    //     0x12999d4: sub             x0, x0, #0xf
    //     0x12999d8: movz            x2, #0xe15c
    //     0x12999dc: movk            x2, #0x3, lsl #16
    //     0x12999e0: stur            x2, [x0, #-1]
    // 0x12999e4: StoreField: r0->field_7 = d1
    //     0x12999e4: stur            d1, [x0, #7]
    // 0x12999e8: stur            x0, [fp, #-8]
    // 0x12999ec: r0 = SizedBox()
    //     0x12999ec: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x12999f0: ldur            x1, [fp, #-8]
    // 0x12999f4: StoreField: r0->field_f = r1
    //     0x12999f4: stur            w1, [x0, #0xf]
    // 0x12999f8: ldur            x1, [fp, #-0x10]
    // 0x12999fc: StoreField: r0->field_b = r1
    //     0x12999fc: stur            w1, [x0, #0xb]
    // 0x1299a00: LeaveFrame
    //     0x1299a00: mov             SP, fp
    //     0x1299a04: ldp             fp, lr, [SP], #0x10
    // 0x1299a08: ret
    //     0x1299a08: ret             
    // 0x1299a0c: SaveReg d1
    //     0x1299a0c: str             q1, [SP, #-0x10]!
    // 0x1299a10: SaveReg r1
    //     0x1299a10: str             x1, [SP, #-8]!
    // 0x1299a14: r0 = AllocateDouble()
    //     0x1299a14: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1299a18: RestoreReg r1
    //     0x1299a18: ldr             x1, [SP], #8
    // 0x1299a1c: RestoreReg d1
    //     0x1299a1c: ldr             q1, [SP], #0x10
    // 0x1299a20: b               #0x12999e4
  }
  _ _buildIcon(/* No info */) {
    // ** addr: 0x1299a24, size: 0x68
    // 0x1299a24: EnterFrame
    //     0x1299a24: stp             fp, lr, [SP, #-0x10]!
    //     0x1299a28: mov             fp, SP
    // 0x1299a2c: AllocStack(0x10)
    //     0x1299a2c: sub             SP, SP, #0x10
    // 0x1299a30: LoadField: r0 = r1->field_b
    //     0x1299a30: ldur            w0, [x1, #0xb]
    // 0x1299a34: DecompressPointer r0
    //     0x1299a34: add             x0, x0, HEAP, lsl #32
    // 0x1299a38: LoadField: r1 = r0->field_7
    //     0x1299a38: ldur            w1, [x0, #7]
    // 0x1299a3c: DecompressPointer r1
    //     0x1299a3c: add             x1, x1, HEAP, lsl #32
    // 0x1299a40: stur            x1, [fp, #-8]
    // 0x1299a44: r0 = Center()
    //     0x1299a44: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1299a48: mov             x1, x0
    // 0x1299a4c: r0 = Instance_Alignment
    //     0x1299a4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1299a50: ldr             x0, [x0, #0xb10]
    // 0x1299a54: stur            x1, [fp, #-0x10]
    // 0x1299a58: StoreField: r1->field_f = r0
    //     0x1299a58: stur            w0, [x1, #0xf]
    // 0x1299a5c: ldur            x0, [fp, #-8]
    // 0x1299a60: StoreField: r1->field_b = r0
    //     0x1299a60: stur            w0, [x1, #0xb]
    // 0x1299a64: r0 = SizedBox()
    //     0x1299a64: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1299a68: r1 = 20.000000
    //     0x1299a68: add             x1, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1299a6c: ldr             x1, [x1, #0xac8]
    // 0x1299a70: StoreField: r0->field_f = r1
    //     0x1299a70: stur            w1, [x0, #0xf]
    // 0x1299a74: StoreField: r0->field_13 = r1
    //     0x1299a74: stur            w1, [x0, #0x13]
    // 0x1299a78: ldur            x1, [fp, #-0x10]
    // 0x1299a7c: StoreField: r0->field_b = r1
    //     0x1299a7c: stur            w1, [x0, #0xb]
    // 0x1299a80: LeaveFrame
    //     0x1299a80: mov             SP, fp
    //     0x1299a84: ldp             fp, lr, [SP], #0x10
    // 0x1299a88: ret
    //     0x1299a88: ret             
  }
  _ _handleColor(/* No info */) {
    // ** addr: 0x1299a8c, size: 0x74
    // 0x1299a8c: EnterFrame
    //     0x1299a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x1299a90: mov             fp, SP
    // 0x1299a94: mov             x0, x1
    // 0x1299a98: mov             x1, x2
    // 0x1299a9c: CheckStackOverflow
    //     0x1299a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1299aa0: cmp             SP, x16
    //     0x1299aa4: b.ls            #0x1299af8
    // 0x1299aa8: tbnz            w5, #4, #0x1299ac0
    // 0x1299aac: r0 = Instance_Color
    //     0x1299aac: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1299ab0: ldr             x0, [x0, #0xf88]
    // 0x1299ab4: LeaveFrame
    //     0x1299ab4: mov             SP, fp
    //     0x1299ab8: ldp             fp, lr, [SP], #0x10
    // 0x1299abc: ret
    //     0x1299abc: ret             
    // 0x1299ac0: tbnz            w3, #4, #0x1299ae4
    // 0x1299ac4: r0 = of()
    //     0x1299ac4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1299ac8: LoadField: r1 = r0->field_3f
    //     0x1299ac8: ldur            w1, [x0, #0x3f]
    // 0x1299acc: DecompressPointer r1
    //     0x1299acc: add             x1, x1, HEAP, lsl #32
    // 0x1299ad0: LoadField: r0 = r1->field_b
    //     0x1299ad0: ldur            w0, [x1, #0xb]
    // 0x1299ad4: DecompressPointer r0
    //     0x1299ad4: add             x0, x0, HEAP, lsl #32
    // 0x1299ad8: LeaveFrame
    //     0x1299ad8: mov             SP, fp
    //     0x1299adc: ldp             fp, lr, [SP], #0x10
    // 0x1299ae0: ret
    //     0x1299ae0: ret             
    // 0x1299ae4: r0 = Instance_Color
    //     0x1299ae4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1299ae8: ldr             x0, [x0, #0xf88]
    // 0x1299aec: LeaveFrame
    //     0x1299aec: mov             SP, fp
    //     0x1299af0: ldp             fp, lr, [SP], #0x10
    // 0x1299af4: ret
    //     0x1299af4: ret             
    // 0x1299af8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1299af8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1299afc: b               #0x1299aa8
  }
}

// class id: 7074, size: 0x14, field offset: 0x14
enum BorderType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x1585948, size: 0x64
    // 0x1585948: EnterFrame
    //     0x1585948: stp             fp, lr, [SP, #-0x10]!
    //     0x158594c: mov             fp, SP
    // 0x1585950: AllocStack(0x10)
    //     0x1585950: sub             SP, SP, #0x10
    // 0x1585954: SetupParameters(BorderType this /* r1 => r0, fp-0x8 */)
    //     0x1585954: mov             x0, x1
    //     0x1585958: stur            x1, [fp, #-8]
    // 0x158595c: CheckStackOverflow
    //     0x158595c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1585960: cmp             SP, x16
    //     0x1585964: b.ls            #0x15859a4
    // 0x1585968: r1 = Null
    //     0x1585968: mov             x1, NULL
    // 0x158596c: r2 = 4
    //     0x158596c: movz            x2, #0x4
    // 0x1585970: r0 = AllocateArray()
    //     0x1585970: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1585974: r16 = "BorderType."
    //     0x1585974: add             x16, PP, #0x49, lsl #12  ; [pp+0x49248] "BorderType."
    //     0x1585978: ldr             x16, [x16, #0x248]
    // 0x158597c: StoreField: r0->field_f = r16
    //     0x158597c: stur            w16, [x0, #0xf]
    // 0x1585980: ldur            x1, [fp, #-8]
    // 0x1585984: LoadField: r2 = r1->field_f
    //     0x1585984: ldur            w2, [x1, #0xf]
    // 0x1585988: DecompressPointer r2
    //     0x1585988: add             x2, x2, HEAP, lsl #32
    // 0x158598c: StoreField: r0->field_13 = r2
    //     0x158598c: stur            w2, [x0, #0x13]
    // 0x1585990: str             x0, [SP]
    // 0x1585994: r0 = _interpolate()
    //     0x1585994: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1585998: LeaveFrame
    //     0x1585998: mov             SP, fp
    //     0x158599c: ldp             fp, lr, [SP], #0x10
    // 0x15859a0: ret
    //     0x15859a0: ret             
    // 0x15859a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15859a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15859a8: b               #0x1585968
  }
}

// class id: 7075, size: 0x14, field offset: 0x14
enum StepShape extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x15858e4, size: 0x64
    // 0x15858e4: EnterFrame
    //     0x15858e4: stp             fp, lr, [SP, #-0x10]!
    //     0x15858e8: mov             fp, SP
    // 0x15858ec: AllocStack(0x10)
    //     0x15858ec: sub             SP, SP, #0x10
    // 0x15858f0: SetupParameters(StepShape this /* r1 => r0, fp-0x8 */)
    //     0x15858f0: mov             x0, x1
    //     0x15858f4: stur            x1, [fp, #-8]
    // 0x15858f8: CheckStackOverflow
    //     0x15858f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15858fc: cmp             SP, x16
    //     0x1585900: b.ls            #0x1585940
    // 0x1585904: r1 = Null
    //     0x1585904: mov             x1, NULL
    // 0x1585908: r2 = 4
    //     0x1585908: movz            x2, #0x4
    // 0x158590c: r0 = AllocateArray()
    //     0x158590c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1585910: r16 = "StepShape."
    //     0x1585910: add             x16, PP, #0x61, lsl #12  ; [pp+0x61b18] "StepShape."
    //     0x1585914: ldr             x16, [x16, #0xb18]
    // 0x1585918: StoreField: r0->field_f = r16
    //     0x1585918: stur            w16, [x0, #0xf]
    // 0x158591c: ldur            x1, [fp, #-8]
    // 0x1585920: LoadField: r2 = r1->field_f
    //     0x1585920: ldur            w2, [x1, #0xf]
    // 0x1585924: DecompressPointer r2
    //     0x1585924: add             x2, x2, HEAP, lsl #32
    // 0x1585928: StoreField: r0->field_13 = r2
    //     0x1585928: stur            w2, [x0, #0x13]
    // 0x158592c: str             x0, [SP]
    // 0x1585930: r0 = _interpolate()
    //     0x1585930: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1585934: LeaveFrame
    //     0x1585934: mov             SP, fp
    //     0x1585938: ldp             fp, lr, [SP], #0x10
    // 0x158593c: ret
    //     0x158593c: ret             
    // 0x1585940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1585940: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1585944: b               #0x1585904
  }
}
