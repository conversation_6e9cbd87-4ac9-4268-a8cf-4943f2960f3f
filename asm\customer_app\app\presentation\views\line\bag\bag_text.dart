// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_text.dart

// class id: 1049466, size: 0x8
class :: {
}

// class id: 3291, size: 0x1c, field offset: 0x14
class _BagTextState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customizedValue; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x9457d8, size: 0x150
    // 0x9457d8: EnterFrame
    //     0x9457d8: stp             fp, lr, [SP, #-0x10]!
    //     0x9457dc: mov             fp, SP
    // 0x9457e0: mov             x2, x1
    // 0x9457e4: LoadField: r3 = r2->field_b
    //     0x9457e4: ldur            w3, [x2, #0xb]
    // 0x9457e8: DecompressPointer r3
    //     0x9457e8: add             x3, x3, HEAP, lsl #32
    // 0x9457ec: cmp             w3, NULL
    // 0x9457f0: b.eq            #0x94591c
    // 0x9457f4: LoadField: r4 = r3->field_b
    //     0x9457f4: ldur            w4, [x3, #0xb]
    // 0x9457f8: DecompressPointer r4
    //     0x9457f8: add             x4, x4, HEAP, lsl #32
    // 0x9457fc: cmp             w4, NULL
    // 0x945800: b.ne            #0x94580c
    // 0x945804: r4 = Null
    //     0x945804: mov             x4, NULL
    // 0x945808: b               #0x945850
    // 0x94580c: LoadField: r5 = r4->field_23
    //     0x94580c: ldur            w5, [x4, #0x23]
    // 0x945810: DecompressPointer r5
    //     0x945810: add             x5, x5, HEAP, lsl #32
    // 0x945814: cmp             w5, NULL
    // 0x945818: b.ne            #0x945824
    // 0x94581c: r4 = Null
    //     0x94581c: mov             x4, NULL
    // 0x945820: b               #0x945850
    // 0x945824: LoadField: r4 = r5->field_b
    //     0x945824: ldur            w4, [x5, #0xb]
    // 0x945828: r0 = LoadInt32Instr(r4)
    //     0x945828: sbfx            x0, x4, #1, #0x1f
    // 0x94582c: r1 = 0
    //     0x94582c: movz            x1, #0
    // 0x945830: cmp             x1, x0
    // 0x945834: b.hs            #0x945920
    // 0x945838: LoadField: r4 = r5->field_f
    //     0x945838: ldur            w4, [x5, #0xf]
    // 0x94583c: DecompressPointer r4
    //     0x94583c: add             x4, x4, HEAP, lsl #32
    // 0x945840: LoadField: r5 = r4->field_f
    //     0x945840: ldur            w5, [x4, #0xf]
    // 0x945844: DecompressPointer r5
    //     0x945844: add             x5, x5, HEAP, lsl #32
    // 0x945848: LoadField: r4 = r5->field_b
    //     0x945848: ldur            w4, [x5, #0xb]
    // 0x94584c: DecompressPointer r4
    //     0x94584c: add             x4, x4, HEAP, lsl #32
    // 0x945850: cmp             w4, NULL
    // 0x945854: b.ne            #0x945860
    // 0x945858: r0 = ""
    //     0x945858: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94585c: b               #0x945864
    // 0x945860: mov             x0, x4
    // 0x945864: StoreField: r2->field_13 = r0
    //     0x945864: stur            w0, [x2, #0x13]
    //     0x945868: ldurb           w16, [x2, #-1]
    //     0x94586c: ldurb           w17, [x0, #-1]
    //     0x945870: and             x16, x17, x16, lsr #2
    //     0x945874: tst             x16, HEAP, lsr #32
    //     0x945878: b.eq            #0x945880
    //     0x94587c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x945880: LoadField: r4 = r3->field_f
    //     0x945880: ldur            w4, [x3, #0xf]
    // 0x945884: DecompressPointer r4
    //     0x945884: add             x4, x4, HEAP, lsl #32
    // 0x945888: cmp             w4, NULL
    // 0x94588c: b.ne            #0x945898
    // 0x945890: r1 = Null
    //     0x945890: mov             x1, NULL
    // 0x945894: b               #0x9458dc
    // 0x945898: LoadField: r3 = r4->field_23
    //     0x945898: ldur            w3, [x4, #0x23]
    // 0x94589c: DecompressPointer r3
    //     0x94589c: add             x3, x3, HEAP, lsl #32
    // 0x9458a0: cmp             w3, NULL
    // 0x9458a4: b.ne            #0x9458b0
    // 0x9458a8: r1 = Null
    //     0x9458a8: mov             x1, NULL
    // 0x9458ac: b               #0x9458dc
    // 0x9458b0: LoadField: r4 = r3->field_b
    //     0x9458b0: ldur            w4, [x3, #0xb]
    // 0x9458b4: r0 = LoadInt32Instr(r4)
    //     0x9458b4: sbfx            x0, x4, #1, #0x1f
    // 0x9458b8: r1 = 0
    //     0x9458b8: movz            x1, #0
    // 0x9458bc: cmp             x1, x0
    // 0x9458c0: b.hs            #0x945924
    // 0x9458c4: LoadField: r1 = r3->field_f
    //     0x9458c4: ldur            w1, [x3, #0xf]
    // 0x9458c8: DecompressPointer r1
    //     0x9458c8: add             x1, x1, HEAP, lsl #32
    // 0x9458cc: LoadField: r3 = r1->field_f
    //     0x9458cc: ldur            w3, [x1, #0xf]
    // 0x9458d0: DecompressPointer r3
    //     0x9458d0: add             x3, x3, HEAP, lsl #32
    // 0x9458d4: LoadField: r1 = r3->field_b
    //     0x9458d4: ldur            w1, [x3, #0xb]
    // 0x9458d8: DecompressPointer r1
    //     0x9458d8: add             x1, x1, HEAP, lsl #32
    // 0x9458dc: cmp             w1, NULL
    // 0x9458e0: b.ne            #0x9458ec
    // 0x9458e4: r0 = ""
    //     0x9458e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9458e8: b               #0x9458f0
    // 0x9458ec: mov             x0, x1
    // 0x9458f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x9458f0: stur            w0, [x2, #0x17]
    //     0x9458f4: ldurb           w16, [x2, #-1]
    //     0x9458f8: ldurb           w17, [x0, #-1]
    //     0x9458fc: and             x16, x17, x16, lsr #2
    //     0x945900: tst             x16, HEAP, lsr #32
    //     0x945904: b.eq            #0x94590c
    //     0x945908: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x94590c: r0 = Null
    //     0x94590c: mov             x0, NULL
    // 0x945910: LeaveFrame
    //     0x945910: mov             SP, fp
    //     0x945914: ldp             fp, lr, [SP], #0x10
    // 0x945918: ret
    //     0x945918: ret             
    // 0x94591c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94591c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x945920: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x945920: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x945924: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x945924: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xba31cc, size: 0x95c
    // 0xba31cc: EnterFrame
    //     0xba31cc: stp             fp, lr, [SP, #-0x10]!
    //     0xba31d0: mov             fp, SP
    // 0xba31d4: AllocStack(0x50)
    //     0xba31d4: sub             SP, SP, #0x50
    // 0xba31d8: SetupParameters(_BagTextState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba31d8: mov             x0, x1
    //     0xba31dc: stur            x1, [fp, #-8]
    //     0xba31e0: mov             x1, x2
    //     0xba31e4: stur            x2, [fp, #-0x10]
    // 0xba31e8: CheckStackOverflow
    //     0xba31e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba31ec: cmp             SP, x16
    //     0xba31f0: b.ls            #0xba3af4
    // 0xba31f4: LoadField: r2 = r0->field_b
    //     0xba31f4: ldur            w2, [x0, #0xb]
    // 0xba31f8: DecompressPointer r2
    //     0xba31f8: add             x2, x2, HEAP, lsl #32
    // 0xba31fc: cmp             w2, NULL
    // 0xba3200: b.eq            #0xba3afc
    // 0xba3204: LoadField: r3 = r2->field_b
    //     0xba3204: ldur            w3, [x2, #0xb]
    // 0xba3208: DecompressPointer r3
    //     0xba3208: add             x3, x3, HEAP, lsl #32
    // 0xba320c: cmp             w3, NULL
    // 0xba3210: b.ne            #0xba321c
    // 0xba3214: r2 = Null
    //     0xba3214: mov             x2, NULL
    // 0xba3218: b               #0xba3248
    // 0xba321c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xba321c: ldur            w2, [x3, #0x17]
    // 0xba3220: DecompressPointer r2
    //     0xba3220: add             x2, x2, HEAP, lsl #32
    // 0xba3224: cmp             w2, NULL
    // 0xba3228: b.ne            #0xba3234
    // 0xba322c: r2 = Null
    //     0xba322c: mov             x2, NULL
    // 0xba3230: b               #0xba3248
    // 0xba3234: LoadField: r3 = r2->field_7
    //     0xba3234: ldur            w3, [x2, #7]
    // 0xba3238: cbnz            w3, #0xba3244
    // 0xba323c: r2 = false
    //     0xba323c: add             x2, NULL, #0x30  ; false
    // 0xba3240: b               #0xba3248
    // 0xba3244: r2 = true
    //     0xba3244: add             x2, NULL, #0x20  ; true
    // 0xba3248: cmp             w2, NULL
    // 0xba324c: b.ne            #0xba3298
    // 0xba3250: mov             x1, x0
    // 0xba3254: r3 = 6
    //     0xba3254: movz            x3, #0x6
    // 0xba3258: r2 = 4
    //     0xba3258: movz            x2, #0x4
    // 0xba325c: r7 = Instance_CrossAxisAlignment
    //     0xba325c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba3260: ldr             x7, [x7, #0xa18]
    // 0xba3264: r5 = Instance_MainAxisAlignment
    //     0xba3264: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba3268: ldr             x5, [x5, #0xa08]
    // 0xba326c: r6 = Instance_MainAxisSize
    //     0xba326c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba3270: ldr             x6, [x6, #0xa10]
    // 0xba3274: r4 = Instance_Axis
    //     0xba3274: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba3278: r8 = Instance_VerticalDirection
    //     0xba3278: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba327c: ldr             x8, [x8, #0xa20]
    // 0xba3280: r0 = Instance__DeferringMouseCursor
    //     0xba3280: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba3284: r9 = Instance_Clip
    //     0xba3284: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba3288: ldr             x9, [x9, #0x38]
    // 0xba328c: d1 = 0.500000
    //     0xba328c: fmov            d1, #0.50000000
    // 0xba3290: d0 = inf
    //     0xba3290: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba3294: b               #0xba36e4
    // 0xba3298: tbnz            w2, #4, #0xba36a0
    // 0xba329c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba329c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba32a0: ldr             x0, [x0, #0x1c80]
    //     0xba32a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba32a8: cmp             w0, w16
    //     0xba32ac: b.ne            #0xba32b8
    //     0xba32b0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba32b4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba32b8: r0 = GetNavigation.size()
    //     0xba32b8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba32bc: LoadField: d0 = r0->field_7
    //     0xba32bc: ldur            d0, [x0, #7]
    // 0xba32c0: d1 = 0.500000
    //     0xba32c0: fmov            d1, #0.50000000
    // 0xba32c4: fmul            d2, d0, d1
    // 0xba32c8: stur            d2, [fp, #-0x40]
    // 0xba32cc: r0 = BoxConstraints()
    //     0xba32cc: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba32d0: stur            x0, [fp, #-0x20]
    // 0xba32d4: StoreField: r0->field_7 = rZR
    //     0xba32d4: stur            xzr, [x0, #7]
    // 0xba32d8: ldur            d0, [fp, #-0x40]
    // 0xba32dc: StoreField: r0->field_f = d0
    //     0xba32dc: stur            d0, [x0, #0xf]
    // 0xba32e0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba32e0: stur            xzr, [x0, #0x17]
    // 0xba32e4: d0 = inf
    //     0xba32e4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba32e8: StoreField: r0->field_1f = d0
    //     0xba32e8: stur            d0, [x0, #0x1f]
    // 0xba32ec: ldur            x2, [fp, #-8]
    // 0xba32f0: LoadField: r1 = r2->field_b
    //     0xba32f0: ldur            w1, [x2, #0xb]
    // 0xba32f4: DecompressPointer r1
    //     0xba32f4: add             x1, x1, HEAP, lsl #32
    // 0xba32f8: cmp             w1, NULL
    // 0xba32fc: b.eq            #0xba3b00
    // 0xba3300: LoadField: r3 = r1->field_b
    //     0xba3300: ldur            w3, [x1, #0xb]
    // 0xba3304: DecompressPointer r3
    //     0xba3304: add             x3, x3, HEAP, lsl #32
    // 0xba3308: cmp             w3, NULL
    // 0xba330c: b.ne            #0xba3318
    // 0xba3310: r1 = Null
    //     0xba3310: mov             x1, NULL
    // 0xba3314: b               #0xba3320
    // 0xba3318: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba3318: ldur            w1, [x3, #0x17]
    // 0xba331c: DecompressPointer r1
    //     0xba331c: add             x1, x1, HEAP, lsl #32
    // 0xba3320: cmp             w1, NULL
    // 0xba3324: b.ne            #0xba3330
    // 0xba3328: r3 = ""
    //     0xba3328: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba332c: b               #0xba3334
    // 0xba3330: mov             x3, x1
    // 0xba3334: ldur            x1, [fp, #-0x10]
    // 0xba3338: stur            x3, [fp, #-0x18]
    // 0xba333c: r0 = of()
    //     0xba333c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3340: LoadField: r1 = r0->field_87
    //     0xba3340: ldur            w1, [x0, #0x87]
    // 0xba3344: DecompressPointer r1
    //     0xba3344: add             x1, x1, HEAP, lsl #32
    // 0xba3348: LoadField: r0 = r1->field_2b
    //     0xba3348: ldur            w0, [x1, #0x2b]
    // 0xba334c: DecompressPointer r0
    //     0xba334c: add             x0, x0, HEAP, lsl #32
    // 0xba3350: ldur            x1, [fp, #-0x10]
    // 0xba3354: stur            x0, [fp, #-0x28]
    // 0xba3358: r0 = of()
    //     0xba3358: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba335c: LoadField: r1 = r0->field_5b
    //     0xba335c: ldur            w1, [x0, #0x5b]
    // 0xba3360: DecompressPointer r1
    //     0xba3360: add             x1, x1, HEAP, lsl #32
    // 0xba3364: r0 = LoadClassIdInstr(r1)
    //     0xba3364: ldur            x0, [x1, #-1]
    //     0xba3368: ubfx            x0, x0, #0xc, #0x14
    // 0xba336c: d0 = 0.700000
    //     0xba336c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba3370: ldr             d0, [x17, #0xf48]
    // 0xba3374: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba3374: sub             lr, x0, #0xffa
    //     0xba3378: ldr             lr, [x21, lr, lsl #3]
    //     0xba337c: blr             lr
    // 0xba3380: r16 = 12.000000
    //     0xba3380: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba3384: ldr             x16, [x16, #0x9e8]
    // 0xba3388: stp             x0, x16, [SP]
    // 0xba338c: ldur            x1, [fp, #-0x28]
    // 0xba3390: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3390: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3394: ldr             x4, [x4, #0xaa0]
    // 0xba3398: r0 = copyWith()
    //     0xba3398: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba339c: stur            x0, [fp, #-0x28]
    // 0xba33a0: r0 = TextSpan()
    //     0xba33a0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba33a4: mov             x3, x0
    // 0xba33a8: ldur            x0, [fp, #-0x18]
    // 0xba33ac: stur            x3, [fp, #-0x30]
    // 0xba33b0: StoreField: r3->field_b = r0
    //     0xba33b0: stur            w0, [x3, #0xb]
    // 0xba33b4: r0 = Instance__DeferringMouseCursor
    //     0xba33b4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba33b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xba33b8: stur            w0, [x3, #0x17]
    // 0xba33bc: ldur            x1, [fp, #-0x28]
    // 0xba33c0: StoreField: r3->field_7 = r1
    //     0xba33c0: stur            w1, [x3, #7]
    // 0xba33c4: r1 = Null
    //     0xba33c4: mov             x1, NULL
    // 0xba33c8: r2 = 6
    //     0xba33c8: movz            x2, #0x6
    // 0xba33cc: r0 = AllocateArray()
    //     0xba33cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba33d0: r16 = " : "
    //     0xba33d0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba33d4: ldr             x16, [x16, #0x680]
    // 0xba33d8: StoreField: r0->field_f = r16
    //     0xba33d8: stur            w16, [x0, #0xf]
    // 0xba33dc: ldur            x1, [fp, #-8]
    // 0xba33e0: LoadField: r2 = r1->field_13
    //     0xba33e0: ldur            w2, [x1, #0x13]
    // 0xba33e4: DecompressPointer r2
    //     0xba33e4: add             x2, x2, HEAP, lsl #32
    // 0xba33e8: r16 = Sentinel
    //     0xba33e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xba33ec: cmp             w2, w16
    // 0xba33f0: b.eq            #0xba3b04
    // 0xba33f4: StoreField: r0->field_13 = r2
    //     0xba33f4: stur            w2, [x0, #0x13]
    // 0xba33f8: r16 = " "
    //     0xba33f8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba33fc: ArrayStore: r0[0] = r16  ; List_4
    //     0xba33fc: stur            w16, [x0, #0x17]
    // 0xba3400: str             x0, [SP]
    // 0xba3404: r0 = _interpolate()
    //     0xba3404: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba3408: ldur            x1, [fp, #-0x10]
    // 0xba340c: stur            x0, [fp, #-0x18]
    // 0xba3410: r0 = of()
    //     0xba3410: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3414: LoadField: r1 = r0->field_87
    //     0xba3414: ldur            w1, [x0, #0x87]
    // 0xba3418: DecompressPointer r1
    //     0xba3418: add             x1, x1, HEAP, lsl #32
    // 0xba341c: LoadField: r0 = r1->field_7
    //     0xba341c: ldur            w0, [x1, #7]
    // 0xba3420: DecompressPointer r0
    //     0xba3420: add             x0, x0, HEAP, lsl #32
    // 0xba3424: ldur            x1, [fp, #-0x10]
    // 0xba3428: stur            x0, [fp, #-0x28]
    // 0xba342c: r0 = of()
    //     0xba342c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3430: LoadField: r1 = r0->field_5b
    //     0xba3430: ldur            w1, [x0, #0x5b]
    // 0xba3434: DecompressPointer r1
    //     0xba3434: add             x1, x1, HEAP, lsl #32
    // 0xba3438: r0 = LoadClassIdInstr(r1)
    //     0xba3438: ldur            x0, [x1, #-1]
    //     0xba343c: ubfx            x0, x0, #0xc, #0x14
    // 0xba3440: d0 = 0.700000
    //     0xba3440: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba3444: ldr             d0, [x17, #0xf48]
    // 0xba3448: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba3448: sub             lr, x0, #0xffa
    //     0xba344c: ldr             lr, [x21, lr, lsl #3]
    //     0xba3450: blr             lr
    // 0xba3454: r16 = 12.000000
    //     0xba3454: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba3458: ldr             x16, [x16, #0x9e8]
    // 0xba345c: stp             x0, x16, [SP]
    // 0xba3460: ldur            x1, [fp, #-0x28]
    // 0xba3464: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3464: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3468: ldr             x4, [x4, #0xaa0]
    // 0xba346c: r0 = copyWith()
    //     0xba346c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba3470: stur            x0, [fp, #-0x28]
    // 0xba3474: r0 = TextSpan()
    //     0xba3474: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba3478: mov             x3, x0
    // 0xba347c: ldur            x0, [fp, #-0x18]
    // 0xba3480: stur            x3, [fp, #-0x38]
    // 0xba3484: StoreField: r3->field_b = r0
    //     0xba3484: stur            w0, [x3, #0xb]
    // 0xba3488: r0 = Instance__DeferringMouseCursor
    //     0xba3488: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba348c: ArrayStore: r3[0] = r0  ; List_4
    //     0xba348c: stur            w0, [x3, #0x17]
    // 0xba3490: ldur            x1, [fp, #-0x28]
    // 0xba3494: StoreField: r3->field_7 = r1
    //     0xba3494: stur            w1, [x3, #7]
    // 0xba3498: r1 = Null
    //     0xba3498: mov             x1, NULL
    // 0xba349c: r2 = 4
    //     0xba349c: movz            x2, #0x4
    // 0xba34a0: r0 = AllocateArray()
    //     0xba34a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba34a4: mov             x2, x0
    // 0xba34a8: ldur            x0, [fp, #-0x30]
    // 0xba34ac: stur            x2, [fp, #-0x18]
    // 0xba34b0: StoreField: r2->field_f = r0
    //     0xba34b0: stur            w0, [x2, #0xf]
    // 0xba34b4: ldur            x0, [fp, #-0x38]
    // 0xba34b8: StoreField: r2->field_13 = r0
    //     0xba34b8: stur            w0, [x2, #0x13]
    // 0xba34bc: r1 = <InlineSpan>
    //     0xba34bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba34c0: ldr             x1, [x1, #0xe40]
    // 0xba34c4: r0 = AllocateGrowableArray()
    //     0xba34c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba34c8: mov             x1, x0
    // 0xba34cc: ldur            x0, [fp, #-0x18]
    // 0xba34d0: stur            x1, [fp, #-0x28]
    // 0xba34d4: StoreField: r1->field_f = r0
    //     0xba34d4: stur            w0, [x1, #0xf]
    // 0xba34d8: r2 = 4
    //     0xba34d8: movz            x2, #0x4
    // 0xba34dc: StoreField: r1->field_b = r2
    //     0xba34dc: stur            w2, [x1, #0xb]
    // 0xba34e0: r0 = TextSpan()
    //     0xba34e0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba34e4: mov             x1, x0
    // 0xba34e8: ldur            x0, [fp, #-0x28]
    // 0xba34ec: stur            x1, [fp, #-0x18]
    // 0xba34f0: StoreField: r1->field_f = r0
    //     0xba34f0: stur            w0, [x1, #0xf]
    // 0xba34f4: r0 = Instance__DeferringMouseCursor
    //     0xba34f4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba34f8: ArrayStore: r1[0] = r0  ; List_4
    //     0xba34f8: stur            w0, [x1, #0x17]
    // 0xba34fc: r0 = RichText()
    //     0xba34fc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba3500: mov             x1, x0
    // 0xba3504: ldur            x2, [fp, #-0x18]
    // 0xba3508: stur            x0, [fp, #-0x18]
    // 0xba350c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba350c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba3510: r0 = RichText()
    //     0xba3510: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba3514: r0 = ConstrainedBox()
    //     0xba3514: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba3518: mov             x2, x0
    // 0xba351c: ldur            x0, [fp, #-0x20]
    // 0xba3520: stur            x2, [fp, #-0x28]
    // 0xba3524: StoreField: r2->field_f = r0
    //     0xba3524: stur            w0, [x2, #0xf]
    // 0xba3528: ldur            x0, [fp, #-0x18]
    // 0xba352c: StoreField: r2->field_b = r0
    //     0xba352c: stur            w0, [x2, #0xb]
    // 0xba3530: ldur            x1, [fp, #-8]
    // 0xba3534: LoadField: r0 = r1->field_b
    //     0xba3534: ldur            w0, [x1, #0xb]
    // 0xba3538: DecompressPointer r0
    //     0xba3538: add             x0, x0, HEAP, lsl #32
    // 0xba353c: cmp             w0, NULL
    // 0xba3540: b.eq            #0xba3b10
    // 0xba3544: LoadField: r1 = r0->field_b
    //     0xba3544: ldur            w1, [x0, #0xb]
    // 0xba3548: DecompressPointer r1
    //     0xba3548: add             x1, x1, HEAP, lsl #32
    // 0xba354c: cmp             w1, NULL
    // 0xba3550: b.ne            #0xba355c
    // 0xba3554: r0 = Null
    //     0xba3554: mov             x0, NULL
    // 0xba3558: b               #0xba3564
    // 0xba355c: LoadField: r0 = r1->field_2b
    //     0xba355c: ldur            w0, [x1, #0x2b]
    // 0xba3560: DecompressPointer r0
    //     0xba3560: add             x0, x0, HEAP, lsl #32
    // 0xba3564: cmp             w0, NULL
    // 0xba3568: b.ne            #0xba3570
    // 0xba356c: r0 = ""
    //     0xba356c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba3570: ldur            x1, [fp, #-0x10]
    // 0xba3574: stur            x0, [fp, #-0x18]
    // 0xba3578: r0 = of()
    //     0xba3578: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba357c: LoadField: r1 = r0->field_87
    //     0xba357c: ldur            w1, [x0, #0x87]
    // 0xba3580: DecompressPointer r1
    //     0xba3580: add             x1, x1, HEAP, lsl #32
    // 0xba3584: LoadField: r0 = r1->field_2b
    //     0xba3584: ldur            w0, [x1, #0x2b]
    // 0xba3588: DecompressPointer r0
    //     0xba3588: add             x0, x0, HEAP, lsl #32
    // 0xba358c: ldur            x1, [fp, #-0x10]
    // 0xba3590: stur            x0, [fp, #-0x20]
    // 0xba3594: r0 = of()
    //     0xba3594: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3598: LoadField: r1 = r0->field_5b
    //     0xba3598: ldur            w1, [x0, #0x5b]
    // 0xba359c: DecompressPointer r1
    //     0xba359c: add             x1, x1, HEAP, lsl #32
    // 0xba35a0: r0 = LoadClassIdInstr(r1)
    //     0xba35a0: ldur            x0, [x1, #-1]
    //     0xba35a4: ubfx            x0, x0, #0xc, #0x14
    // 0xba35a8: d0 = 0.700000
    //     0xba35a8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba35ac: ldr             d0, [x17, #0xf48]
    // 0xba35b0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba35b0: sub             lr, x0, #0xffa
    //     0xba35b4: ldr             lr, [x21, lr, lsl #3]
    //     0xba35b8: blr             lr
    // 0xba35bc: r16 = 12.000000
    //     0xba35bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba35c0: ldr             x16, [x16, #0x9e8]
    // 0xba35c4: stp             x0, x16, [SP]
    // 0xba35c8: ldur            x1, [fp, #-0x20]
    // 0xba35cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba35cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba35d0: ldr             x4, [x4, #0xaa0]
    // 0xba35d4: r0 = copyWith()
    //     0xba35d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba35d8: stur            x0, [fp, #-0x20]
    // 0xba35dc: r0 = Text()
    //     0xba35dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba35e0: mov             x3, x0
    // 0xba35e4: ldur            x0, [fp, #-0x18]
    // 0xba35e8: stur            x3, [fp, #-0x30]
    // 0xba35ec: StoreField: r3->field_b = r0
    //     0xba35ec: stur            w0, [x3, #0xb]
    // 0xba35f0: ldur            x0, [fp, #-0x20]
    // 0xba35f4: StoreField: r3->field_13 = r0
    //     0xba35f4: stur            w0, [x3, #0x13]
    // 0xba35f8: r1 = Null
    //     0xba35f8: mov             x1, NULL
    // 0xba35fc: r2 = 6
    //     0xba35fc: movz            x2, #0x6
    // 0xba3600: r0 = AllocateArray()
    //     0xba3600: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba3604: mov             x2, x0
    // 0xba3608: ldur            x0, [fp, #-0x28]
    // 0xba360c: stur            x2, [fp, #-0x18]
    // 0xba3610: StoreField: r2->field_f = r0
    //     0xba3610: stur            w0, [x2, #0xf]
    // 0xba3614: r16 = Instance_Spacer
    //     0xba3614: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba3618: ldr             x16, [x16, #0xf0]
    // 0xba361c: StoreField: r2->field_13 = r16
    //     0xba361c: stur            w16, [x2, #0x13]
    // 0xba3620: ldur            x0, [fp, #-0x30]
    // 0xba3624: ArrayStore: r2[0] = r0  ; List_4
    //     0xba3624: stur            w0, [x2, #0x17]
    // 0xba3628: r1 = <Widget>
    //     0xba3628: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba362c: r0 = AllocateGrowableArray()
    //     0xba362c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba3630: mov             x1, x0
    // 0xba3634: ldur            x0, [fp, #-0x18]
    // 0xba3638: stur            x1, [fp, #-0x20]
    // 0xba363c: StoreField: r1->field_f = r0
    //     0xba363c: stur            w0, [x1, #0xf]
    // 0xba3640: r3 = 6
    //     0xba3640: movz            x3, #0x6
    // 0xba3644: StoreField: r1->field_b = r3
    //     0xba3644: stur            w3, [x1, #0xb]
    // 0xba3648: r0 = Row()
    //     0xba3648: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba364c: r4 = Instance_Axis
    //     0xba364c: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba3650: StoreField: r0->field_f = r4
    //     0xba3650: stur            w4, [x0, #0xf]
    // 0xba3654: r5 = Instance_MainAxisAlignment
    //     0xba3654: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba3658: ldr             x5, [x5, #0xa08]
    // 0xba365c: StoreField: r0->field_13 = r5
    //     0xba365c: stur            w5, [x0, #0x13]
    // 0xba3660: r6 = Instance_MainAxisSize
    //     0xba3660: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba3664: ldr             x6, [x6, #0xa10]
    // 0xba3668: ArrayStore: r0[0] = r6  ; List_4
    //     0xba3668: stur            w6, [x0, #0x17]
    // 0xba366c: r7 = Instance_CrossAxisAlignment
    //     0xba366c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba3670: ldr             x7, [x7, #0xa18]
    // 0xba3674: StoreField: r0->field_1b = r7
    //     0xba3674: stur            w7, [x0, #0x1b]
    // 0xba3678: r8 = Instance_VerticalDirection
    //     0xba3678: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba367c: ldr             x8, [x8, #0xa20]
    // 0xba3680: StoreField: r0->field_23 = r8
    //     0xba3680: stur            w8, [x0, #0x23]
    // 0xba3684: r9 = Instance_Clip
    //     0xba3684: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba3688: ldr             x9, [x9, #0x38]
    // 0xba368c: StoreField: r0->field_2b = r9
    //     0xba368c: stur            w9, [x0, #0x2b]
    // 0xba3690: StoreField: r0->field_2f = rZR
    //     0xba3690: stur            xzr, [x0, #0x2f]
    // 0xba3694: ldur            x1, [fp, #-0x20]
    // 0xba3698: StoreField: r0->field_b = r1
    //     0xba3698: stur            w1, [x0, #0xb]
    // 0xba369c: b               #0xba3ae8
    // 0xba36a0: mov             x1, x0
    // 0xba36a4: r3 = 6
    //     0xba36a4: movz            x3, #0x6
    // 0xba36a8: r2 = 4
    //     0xba36a8: movz            x2, #0x4
    // 0xba36ac: r7 = Instance_CrossAxisAlignment
    //     0xba36ac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba36b0: ldr             x7, [x7, #0xa18]
    // 0xba36b4: r5 = Instance_MainAxisAlignment
    //     0xba36b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba36b8: ldr             x5, [x5, #0xa08]
    // 0xba36bc: r6 = Instance_MainAxisSize
    //     0xba36bc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba36c0: ldr             x6, [x6, #0xa10]
    // 0xba36c4: r4 = Instance_Axis
    //     0xba36c4: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba36c8: r8 = Instance_VerticalDirection
    //     0xba36c8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba36cc: ldr             x8, [x8, #0xa20]
    // 0xba36d0: r0 = Instance__DeferringMouseCursor
    //     0xba36d0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba36d4: r9 = Instance_Clip
    //     0xba36d4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba36d8: ldr             x9, [x9, #0x38]
    // 0xba36dc: d1 = 0.500000
    //     0xba36dc: fmov            d1, #0.50000000
    // 0xba36e0: d0 = inf
    //     0xba36e0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba36e4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba36e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba36e8: ldr             x0, [x0, #0x1c80]
    //     0xba36ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba36f0: cmp             w0, w16
    //     0xba36f4: b.ne            #0xba3700
    //     0xba36f8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba36fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba3700: r0 = GetNavigation.size()
    //     0xba3700: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba3704: LoadField: d0 = r0->field_7
    //     0xba3704: ldur            d0, [x0, #7]
    // 0xba3708: d1 = 0.500000
    //     0xba3708: fmov            d1, #0.50000000
    // 0xba370c: fmul            d2, d0, d1
    // 0xba3710: stur            d2, [fp, #-0x40]
    // 0xba3714: r0 = BoxConstraints()
    //     0xba3714: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba3718: stur            x0, [fp, #-0x20]
    // 0xba371c: StoreField: r0->field_7 = rZR
    //     0xba371c: stur            xzr, [x0, #7]
    // 0xba3720: ldur            d0, [fp, #-0x40]
    // 0xba3724: StoreField: r0->field_f = d0
    //     0xba3724: stur            d0, [x0, #0xf]
    // 0xba3728: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba3728: stur            xzr, [x0, #0x17]
    // 0xba372c: d0 = inf
    //     0xba372c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba3730: StoreField: r0->field_1f = d0
    //     0xba3730: stur            d0, [x0, #0x1f]
    // 0xba3734: ldur            x2, [fp, #-8]
    // 0xba3738: LoadField: r1 = r2->field_b
    //     0xba3738: ldur            w1, [x2, #0xb]
    // 0xba373c: DecompressPointer r1
    //     0xba373c: add             x1, x1, HEAP, lsl #32
    // 0xba3740: cmp             w1, NULL
    // 0xba3744: b.eq            #0xba3b14
    // 0xba3748: LoadField: r3 = r1->field_f
    //     0xba3748: ldur            w3, [x1, #0xf]
    // 0xba374c: DecompressPointer r3
    //     0xba374c: add             x3, x3, HEAP, lsl #32
    // 0xba3750: cmp             w3, NULL
    // 0xba3754: b.ne            #0xba3760
    // 0xba3758: r1 = Null
    //     0xba3758: mov             x1, NULL
    // 0xba375c: b               #0xba3768
    // 0xba3760: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba3760: ldur            w1, [x3, #0x17]
    // 0xba3764: DecompressPointer r1
    //     0xba3764: add             x1, x1, HEAP, lsl #32
    // 0xba3768: cmp             w1, NULL
    // 0xba376c: b.ne            #0xba3778
    // 0xba3770: r3 = ""
    //     0xba3770: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba3774: b               #0xba377c
    // 0xba3778: mov             x3, x1
    // 0xba377c: ldur            x1, [fp, #-0x10]
    // 0xba3780: stur            x3, [fp, #-0x18]
    // 0xba3784: r0 = of()
    //     0xba3784: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3788: LoadField: r1 = r0->field_87
    //     0xba3788: ldur            w1, [x0, #0x87]
    // 0xba378c: DecompressPointer r1
    //     0xba378c: add             x1, x1, HEAP, lsl #32
    // 0xba3790: LoadField: r0 = r1->field_2b
    //     0xba3790: ldur            w0, [x1, #0x2b]
    // 0xba3794: DecompressPointer r0
    //     0xba3794: add             x0, x0, HEAP, lsl #32
    // 0xba3798: ldur            x1, [fp, #-0x10]
    // 0xba379c: stur            x0, [fp, #-0x28]
    // 0xba37a0: r0 = of()
    //     0xba37a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba37a4: LoadField: r1 = r0->field_5b
    //     0xba37a4: ldur            w1, [x0, #0x5b]
    // 0xba37a8: DecompressPointer r1
    //     0xba37a8: add             x1, x1, HEAP, lsl #32
    // 0xba37ac: r0 = LoadClassIdInstr(r1)
    //     0xba37ac: ldur            x0, [x1, #-1]
    //     0xba37b0: ubfx            x0, x0, #0xc, #0x14
    // 0xba37b4: d0 = 0.700000
    //     0xba37b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba37b8: ldr             d0, [x17, #0xf48]
    // 0xba37bc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba37bc: sub             lr, x0, #0xffa
    //     0xba37c0: ldr             lr, [x21, lr, lsl #3]
    //     0xba37c4: blr             lr
    // 0xba37c8: r16 = 12.000000
    //     0xba37c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba37cc: ldr             x16, [x16, #0x9e8]
    // 0xba37d0: stp             x0, x16, [SP]
    // 0xba37d4: ldur            x1, [fp, #-0x28]
    // 0xba37d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba37d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba37dc: ldr             x4, [x4, #0xaa0]
    // 0xba37e0: r0 = copyWith()
    //     0xba37e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba37e4: stur            x0, [fp, #-0x28]
    // 0xba37e8: r0 = TextSpan()
    //     0xba37e8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba37ec: mov             x3, x0
    // 0xba37f0: ldur            x0, [fp, #-0x18]
    // 0xba37f4: stur            x3, [fp, #-0x30]
    // 0xba37f8: StoreField: r3->field_b = r0
    //     0xba37f8: stur            w0, [x3, #0xb]
    // 0xba37fc: r0 = Instance__DeferringMouseCursor
    //     0xba37fc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba3800: ArrayStore: r3[0] = r0  ; List_4
    //     0xba3800: stur            w0, [x3, #0x17]
    // 0xba3804: ldur            x1, [fp, #-0x28]
    // 0xba3808: StoreField: r3->field_7 = r1
    //     0xba3808: stur            w1, [x3, #7]
    // 0xba380c: r1 = Null
    //     0xba380c: mov             x1, NULL
    // 0xba3810: r2 = 6
    //     0xba3810: movz            x2, #0x6
    // 0xba3814: r0 = AllocateArray()
    //     0xba3814: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba3818: r16 = " : "
    //     0xba3818: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba381c: ldr             x16, [x16, #0x680]
    // 0xba3820: StoreField: r0->field_f = r16
    //     0xba3820: stur            w16, [x0, #0xf]
    // 0xba3824: ldur            x1, [fp, #-8]
    // 0xba3828: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xba3828: ldur            w2, [x1, #0x17]
    // 0xba382c: DecompressPointer r2
    //     0xba382c: add             x2, x2, HEAP, lsl #32
    // 0xba3830: r16 = Sentinel
    //     0xba3830: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xba3834: cmp             w2, w16
    // 0xba3838: b.eq            #0xba3b18
    // 0xba383c: StoreField: r0->field_13 = r2
    //     0xba383c: stur            w2, [x0, #0x13]
    // 0xba3840: r16 = " "
    //     0xba3840: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba3844: ArrayStore: r0[0] = r16  ; List_4
    //     0xba3844: stur            w16, [x0, #0x17]
    // 0xba3848: str             x0, [SP]
    // 0xba384c: r0 = _interpolate()
    //     0xba384c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba3850: ldur            x1, [fp, #-0x10]
    // 0xba3854: stur            x0, [fp, #-0x18]
    // 0xba3858: r0 = of()
    //     0xba3858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba385c: LoadField: r1 = r0->field_87
    //     0xba385c: ldur            w1, [x0, #0x87]
    // 0xba3860: DecompressPointer r1
    //     0xba3860: add             x1, x1, HEAP, lsl #32
    // 0xba3864: LoadField: r0 = r1->field_7
    //     0xba3864: ldur            w0, [x1, #7]
    // 0xba3868: DecompressPointer r0
    //     0xba3868: add             x0, x0, HEAP, lsl #32
    // 0xba386c: ldur            x1, [fp, #-0x10]
    // 0xba3870: stur            x0, [fp, #-0x28]
    // 0xba3874: r0 = of()
    //     0xba3874: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3878: LoadField: r1 = r0->field_5b
    //     0xba3878: ldur            w1, [x0, #0x5b]
    // 0xba387c: DecompressPointer r1
    //     0xba387c: add             x1, x1, HEAP, lsl #32
    // 0xba3880: r0 = LoadClassIdInstr(r1)
    //     0xba3880: ldur            x0, [x1, #-1]
    //     0xba3884: ubfx            x0, x0, #0xc, #0x14
    // 0xba3888: d0 = 0.700000
    //     0xba3888: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba388c: ldr             d0, [x17, #0xf48]
    // 0xba3890: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba3890: sub             lr, x0, #0xffa
    //     0xba3894: ldr             lr, [x21, lr, lsl #3]
    //     0xba3898: blr             lr
    // 0xba389c: r16 = 12.000000
    //     0xba389c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba38a0: ldr             x16, [x16, #0x9e8]
    // 0xba38a4: stp             x0, x16, [SP]
    // 0xba38a8: ldur            x1, [fp, #-0x28]
    // 0xba38ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba38ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba38b0: ldr             x4, [x4, #0xaa0]
    // 0xba38b4: r0 = copyWith()
    //     0xba38b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba38b8: stur            x0, [fp, #-0x28]
    // 0xba38bc: r0 = TextSpan()
    //     0xba38bc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba38c0: mov             x3, x0
    // 0xba38c4: ldur            x0, [fp, #-0x18]
    // 0xba38c8: stur            x3, [fp, #-0x38]
    // 0xba38cc: StoreField: r3->field_b = r0
    //     0xba38cc: stur            w0, [x3, #0xb]
    // 0xba38d0: r0 = Instance__DeferringMouseCursor
    //     0xba38d0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba38d4: ArrayStore: r3[0] = r0  ; List_4
    //     0xba38d4: stur            w0, [x3, #0x17]
    // 0xba38d8: ldur            x1, [fp, #-0x28]
    // 0xba38dc: StoreField: r3->field_7 = r1
    //     0xba38dc: stur            w1, [x3, #7]
    // 0xba38e0: r1 = Null
    //     0xba38e0: mov             x1, NULL
    // 0xba38e4: r2 = 4
    //     0xba38e4: movz            x2, #0x4
    // 0xba38e8: r0 = AllocateArray()
    //     0xba38e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba38ec: mov             x2, x0
    // 0xba38f0: ldur            x0, [fp, #-0x30]
    // 0xba38f4: stur            x2, [fp, #-0x18]
    // 0xba38f8: StoreField: r2->field_f = r0
    //     0xba38f8: stur            w0, [x2, #0xf]
    // 0xba38fc: ldur            x0, [fp, #-0x38]
    // 0xba3900: StoreField: r2->field_13 = r0
    //     0xba3900: stur            w0, [x2, #0x13]
    // 0xba3904: r1 = <InlineSpan>
    //     0xba3904: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba3908: ldr             x1, [x1, #0xe40]
    // 0xba390c: r0 = AllocateGrowableArray()
    //     0xba390c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba3910: mov             x1, x0
    // 0xba3914: ldur            x0, [fp, #-0x18]
    // 0xba3918: stur            x1, [fp, #-0x28]
    // 0xba391c: StoreField: r1->field_f = r0
    //     0xba391c: stur            w0, [x1, #0xf]
    // 0xba3920: r0 = 4
    //     0xba3920: movz            x0, #0x4
    // 0xba3924: StoreField: r1->field_b = r0
    //     0xba3924: stur            w0, [x1, #0xb]
    // 0xba3928: r0 = TextSpan()
    //     0xba3928: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba392c: mov             x1, x0
    // 0xba3930: ldur            x0, [fp, #-0x28]
    // 0xba3934: stur            x1, [fp, #-0x18]
    // 0xba3938: StoreField: r1->field_f = r0
    //     0xba3938: stur            w0, [x1, #0xf]
    // 0xba393c: r0 = Instance__DeferringMouseCursor
    //     0xba393c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba3940: ArrayStore: r1[0] = r0  ; List_4
    //     0xba3940: stur            w0, [x1, #0x17]
    // 0xba3944: r0 = RichText()
    //     0xba3944: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba3948: mov             x1, x0
    // 0xba394c: ldur            x2, [fp, #-0x18]
    // 0xba3950: stur            x0, [fp, #-0x18]
    // 0xba3954: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba3954: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba3958: r0 = RichText()
    //     0xba3958: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba395c: r0 = ConstrainedBox()
    //     0xba395c: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba3960: mov             x2, x0
    // 0xba3964: ldur            x0, [fp, #-0x20]
    // 0xba3968: stur            x2, [fp, #-0x28]
    // 0xba396c: StoreField: r2->field_f = r0
    //     0xba396c: stur            w0, [x2, #0xf]
    // 0xba3970: ldur            x0, [fp, #-0x18]
    // 0xba3974: StoreField: r2->field_b = r0
    //     0xba3974: stur            w0, [x2, #0xb]
    // 0xba3978: ldur            x0, [fp, #-8]
    // 0xba397c: LoadField: r1 = r0->field_b
    //     0xba397c: ldur            w1, [x0, #0xb]
    // 0xba3980: DecompressPointer r1
    //     0xba3980: add             x1, x1, HEAP, lsl #32
    // 0xba3984: cmp             w1, NULL
    // 0xba3988: b.eq            #0xba3b24
    // 0xba398c: LoadField: r0 = r1->field_f
    //     0xba398c: ldur            w0, [x1, #0xf]
    // 0xba3990: DecompressPointer r0
    //     0xba3990: add             x0, x0, HEAP, lsl #32
    // 0xba3994: cmp             w0, NULL
    // 0xba3998: b.ne            #0xba39a4
    // 0xba399c: r0 = Null
    //     0xba399c: mov             x0, NULL
    // 0xba39a0: b               #0xba39b0
    // 0xba39a4: LoadField: r1 = r0->field_2b
    //     0xba39a4: ldur            w1, [x0, #0x2b]
    // 0xba39a8: DecompressPointer r1
    //     0xba39a8: add             x1, x1, HEAP, lsl #32
    // 0xba39ac: mov             x0, x1
    // 0xba39b0: cmp             w0, NULL
    // 0xba39b4: b.ne            #0xba39bc
    // 0xba39b8: r0 = ""
    //     0xba39b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba39bc: ldur            x1, [fp, #-0x10]
    // 0xba39c0: stur            x0, [fp, #-8]
    // 0xba39c4: r0 = of()
    //     0xba39c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba39c8: LoadField: r1 = r0->field_87
    //     0xba39c8: ldur            w1, [x0, #0x87]
    // 0xba39cc: DecompressPointer r1
    //     0xba39cc: add             x1, x1, HEAP, lsl #32
    // 0xba39d0: LoadField: r0 = r1->field_2b
    //     0xba39d0: ldur            w0, [x1, #0x2b]
    // 0xba39d4: DecompressPointer r0
    //     0xba39d4: add             x0, x0, HEAP, lsl #32
    // 0xba39d8: ldur            x1, [fp, #-0x10]
    // 0xba39dc: stur            x0, [fp, #-0x18]
    // 0xba39e0: r0 = of()
    //     0xba39e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba39e4: LoadField: r1 = r0->field_5b
    //     0xba39e4: ldur            w1, [x0, #0x5b]
    // 0xba39e8: DecompressPointer r1
    //     0xba39e8: add             x1, x1, HEAP, lsl #32
    // 0xba39ec: r0 = LoadClassIdInstr(r1)
    //     0xba39ec: ldur            x0, [x1, #-1]
    //     0xba39f0: ubfx            x0, x0, #0xc, #0x14
    // 0xba39f4: d0 = 0.700000
    //     0xba39f4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba39f8: ldr             d0, [x17, #0xf48]
    // 0xba39fc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xba39fc: sub             lr, x0, #0xffa
    //     0xba3a00: ldr             lr, [x21, lr, lsl #3]
    //     0xba3a04: blr             lr
    // 0xba3a08: r16 = 12.000000
    //     0xba3a08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba3a0c: ldr             x16, [x16, #0x9e8]
    // 0xba3a10: stp             x0, x16, [SP]
    // 0xba3a14: ldur            x1, [fp, #-0x18]
    // 0xba3a18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3a18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3a1c: ldr             x4, [x4, #0xaa0]
    // 0xba3a20: r0 = copyWith()
    //     0xba3a20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba3a24: stur            x0, [fp, #-0x10]
    // 0xba3a28: r0 = Text()
    //     0xba3a28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba3a2c: mov             x3, x0
    // 0xba3a30: ldur            x0, [fp, #-8]
    // 0xba3a34: stur            x3, [fp, #-0x18]
    // 0xba3a38: StoreField: r3->field_b = r0
    //     0xba3a38: stur            w0, [x3, #0xb]
    // 0xba3a3c: ldur            x0, [fp, #-0x10]
    // 0xba3a40: StoreField: r3->field_13 = r0
    //     0xba3a40: stur            w0, [x3, #0x13]
    // 0xba3a44: r1 = Null
    //     0xba3a44: mov             x1, NULL
    // 0xba3a48: r2 = 6
    //     0xba3a48: movz            x2, #0x6
    // 0xba3a4c: r0 = AllocateArray()
    //     0xba3a4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba3a50: mov             x2, x0
    // 0xba3a54: ldur            x0, [fp, #-0x28]
    // 0xba3a58: stur            x2, [fp, #-8]
    // 0xba3a5c: StoreField: r2->field_f = r0
    //     0xba3a5c: stur            w0, [x2, #0xf]
    // 0xba3a60: r16 = Instance_Spacer
    //     0xba3a60: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba3a64: ldr             x16, [x16, #0xf0]
    // 0xba3a68: StoreField: r2->field_13 = r16
    //     0xba3a68: stur            w16, [x2, #0x13]
    // 0xba3a6c: ldur            x0, [fp, #-0x18]
    // 0xba3a70: ArrayStore: r2[0] = r0  ; List_4
    //     0xba3a70: stur            w0, [x2, #0x17]
    // 0xba3a74: r1 = <Widget>
    //     0xba3a74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba3a78: r0 = AllocateGrowableArray()
    //     0xba3a78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba3a7c: mov             x1, x0
    // 0xba3a80: ldur            x0, [fp, #-8]
    // 0xba3a84: stur            x1, [fp, #-0x10]
    // 0xba3a88: StoreField: r1->field_f = r0
    //     0xba3a88: stur            w0, [x1, #0xf]
    // 0xba3a8c: r0 = 6
    //     0xba3a8c: movz            x0, #0x6
    // 0xba3a90: StoreField: r1->field_b = r0
    //     0xba3a90: stur            w0, [x1, #0xb]
    // 0xba3a94: r0 = Row()
    //     0xba3a94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba3a98: r1 = Instance_Axis
    //     0xba3a98: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba3a9c: StoreField: r0->field_f = r1
    //     0xba3a9c: stur            w1, [x0, #0xf]
    // 0xba3aa0: r1 = Instance_MainAxisAlignment
    //     0xba3aa0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba3aa4: ldr             x1, [x1, #0xa08]
    // 0xba3aa8: StoreField: r0->field_13 = r1
    //     0xba3aa8: stur            w1, [x0, #0x13]
    // 0xba3aac: r1 = Instance_MainAxisSize
    //     0xba3aac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba3ab0: ldr             x1, [x1, #0xa10]
    // 0xba3ab4: ArrayStore: r0[0] = r1  ; List_4
    //     0xba3ab4: stur            w1, [x0, #0x17]
    // 0xba3ab8: r1 = Instance_CrossAxisAlignment
    //     0xba3ab8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba3abc: ldr             x1, [x1, #0xa18]
    // 0xba3ac0: StoreField: r0->field_1b = r1
    //     0xba3ac0: stur            w1, [x0, #0x1b]
    // 0xba3ac4: r1 = Instance_VerticalDirection
    //     0xba3ac4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba3ac8: ldr             x1, [x1, #0xa20]
    // 0xba3acc: StoreField: r0->field_23 = r1
    //     0xba3acc: stur            w1, [x0, #0x23]
    // 0xba3ad0: r1 = Instance_Clip
    //     0xba3ad0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba3ad4: ldr             x1, [x1, #0x38]
    // 0xba3ad8: StoreField: r0->field_2b = r1
    //     0xba3ad8: stur            w1, [x0, #0x2b]
    // 0xba3adc: StoreField: r0->field_2f = rZR
    //     0xba3adc: stur            xzr, [x0, #0x2f]
    // 0xba3ae0: ldur            x1, [fp, #-0x10]
    // 0xba3ae4: StoreField: r0->field_b = r1
    //     0xba3ae4: stur            w1, [x0, #0xb]
    // 0xba3ae8: LeaveFrame
    //     0xba3ae8: mov             SP, fp
    //     0xba3aec: ldp             fp, lr, [SP], #0x10
    // 0xba3af0: ret
    //     0xba3af0: ret             
    // 0xba3af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba3af4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba3af8: b               #0xba31f4
    // 0xba3afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3afc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3b00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3b04: r9 = value
    //     0xba3b04: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a688] Field <<EMAIL>>: late (offset: 0x14)
    //     0xba3b08: ldr             x9, [x9, #0x688]
    // 0xba3b0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xba3b0c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xba3b10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3b10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3b14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3b14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3b18: r9 = customizedValue
    //     0xba3b18: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a690] Field <<EMAIL>>: late (offset: 0x18)
    //     0xba3b1c: ldr             x9, [x9, #0x690]
    // 0xba3b20: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xba3b20: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xba3b24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3b24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4034, size: 0x14, field offset: 0xc
//   const constructor, 
class BagText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ff4c, size: 0x30
    // 0xc7ff4c: EnterFrame
    //     0xc7ff4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ff50: mov             fp, SP
    // 0xc7ff54: mov             x0, x1
    // 0xc7ff58: r1 = <BagText>
    //     0xc7ff58: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d20] TypeArguments: <BagText>
    //     0xc7ff5c: ldr             x1, [x1, #0xd20]
    // 0xc7ff60: r0 = _BagTextState()
    //     0xc7ff60: bl              #0xc7ff7c  ; Allocate_BagTextStateStub -> _BagTextState (size=0x1c)
    // 0xc7ff64: r1 = Sentinel
    //     0xc7ff64: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7ff68: StoreField: r0->field_13 = r1
    //     0xc7ff68: stur            w1, [x0, #0x13]
    // 0xc7ff6c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7ff6c: stur            w1, [x0, #0x17]
    // 0xc7ff70: LeaveFrame
    //     0xc7ff70: mov             SP, fp
    //     0xc7ff74: ldp             fp, lr, [SP], #0x10
    // 0xc7ff78: ret
    //     0xc7ff78: ret             
  }
}
