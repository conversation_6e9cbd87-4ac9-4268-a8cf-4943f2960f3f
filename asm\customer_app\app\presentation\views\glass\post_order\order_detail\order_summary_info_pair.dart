// lib: , url: package:customer_app/app/presentation/views/glass/post_order/order_detail/order_summary_info_pair.dart

// class id: 1049422, size: 0x8
class :: {
}

// class id: 4492, size: 0x10, field offset: 0xc
//   const constructor, 
class OrderSummaryInfoPair extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1292fb0, size: 0x400
    // 0x1292fb0: EnterFrame
    //     0x1292fb0: stp             fp, lr, [SP, #-0x10]!
    //     0x1292fb4: mov             fp, SP
    // 0x1292fb8: AllocStack(0x40)
    //     0x1292fb8: sub             SP, SP, #0x40
    // 0x1292fbc: SetupParameters(OrderSummaryInfoPair this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x1292fbc: mov             x0, x1
    //     0x1292fc0: mov             x1, x2
    //     0x1292fc4: stur            x2, [fp, #-0x10]
    // 0x1292fc8: CheckStackOverflow
    //     0x1292fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1292fcc: cmp             SP, x16
    //     0x1292fd0: b.ls            #0x12933a8
    // 0x1292fd4: LoadField: r2 = r0->field_b
    //     0x1292fd4: ldur            w2, [x0, #0xb]
    // 0x1292fd8: DecompressPointer r2
    //     0x1292fd8: add             x2, x2, HEAP, lsl #32
    // 0x1292fdc: stur            x2, [fp, #-8]
    // 0x1292fe0: LoadField: r0 = r2->field_7
    //     0x1292fe0: ldur            w0, [x2, #7]
    // 0x1292fe4: DecompressPointer r0
    //     0x1292fe4: add             x0, x0, HEAP, lsl #32
    // 0x1292fe8: r3 = LoadClassIdInstr(r0)
    //     0x1292fe8: ldur            x3, [x0, #-1]
    //     0x1292fec: ubfx            x3, x3, #0xc, #0x14
    // 0x1292ff0: r16 = "final"
    //     0x1292ff0: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x1292ff4: ldr             x16, [x16, #0x3d8]
    // 0x1292ff8: stp             x16, x0, [SP]
    // 0x1292ffc: mov             x0, x3
    // 0x1293000: mov             lr, x0
    // 0x1293004: ldr             lr, [x21, lr, lsl #3]
    // 0x1293008: blr             lr
    // 0x129300c: ldur            x1, [fp, #-0x10]
    // 0x1293010: stur            x0, [fp, #-0x18]
    // 0x1293014: r0 = of()
    //     0x1293014: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293018: LoadField: r1 = r0->field_5b
    //     0x1293018: ldur            w1, [x0, #0x5b]
    // 0x129301c: DecompressPointer r1
    //     0x129301c: add             x1, x1, HEAP, lsl #32
    // 0x1293020: r0 = LoadClassIdInstr(r1)
    //     0x1293020: ldur            x0, [x1, #-1]
    //     0x1293024: ubfx            x0, x0, #0xc, #0x14
    // 0x1293028: d0 = 0.100000
    //     0x1293028: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x129302c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x129302c: sub             lr, x0, #0xffa
    //     0x1293030: ldr             lr, [x21, lr, lsl #3]
    //     0x1293034: blr             lr
    // 0x1293038: stur            x0, [fp, #-0x20]
    // 0x129303c: r0 = Divider()
    //     0x129303c: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x1293040: mov             x1, x0
    // 0x1293044: ldur            x0, [fp, #-0x20]
    // 0x1293048: stur            x1, [fp, #-0x28]
    // 0x129304c: StoreField: r1->field_1f = r0
    //     0x129304c: stur            w0, [x1, #0x1f]
    // 0x1293050: r0 = Visibility()
    //     0x1293050: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1293054: mov             x2, x0
    // 0x1293058: ldur            x0, [fp, #-0x28]
    // 0x129305c: stur            x2, [fp, #-0x20]
    // 0x1293060: StoreField: r2->field_b = r0
    //     0x1293060: stur            w0, [x2, #0xb]
    // 0x1293064: r0 = Instance_SizedBox
    //     0x1293064: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1293068: StoreField: r2->field_f = r0
    //     0x1293068: stur            w0, [x2, #0xf]
    // 0x129306c: ldur            x0, [fp, #-0x18]
    // 0x1293070: StoreField: r2->field_13 = r0
    //     0x1293070: stur            w0, [x2, #0x13]
    // 0x1293074: r0 = false
    //     0x1293074: add             x0, NULL, #0x30  ; false
    // 0x1293078: ArrayStore: r2[0] = r0  ; List_4
    //     0x1293078: stur            w0, [x2, #0x17]
    // 0x129307c: StoreField: r2->field_1b = r0
    //     0x129307c: stur            w0, [x2, #0x1b]
    // 0x1293080: StoreField: r2->field_1f = r0
    //     0x1293080: stur            w0, [x2, #0x1f]
    // 0x1293084: StoreField: r2->field_23 = r0
    //     0x1293084: stur            w0, [x2, #0x23]
    // 0x1293088: StoreField: r2->field_27 = r0
    //     0x1293088: stur            w0, [x2, #0x27]
    // 0x129308c: StoreField: r2->field_2b = r0
    //     0x129308c: stur            w0, [x2, #0x2b]
    // 0x1293090: ldur            x0, [fp, #-8]
    // 0x1293094: LoadField: r1 = r0->field_b
    //     0x1293094: ldur            w1, [x0, #0xb]
    // 0x1293098: DecompressPointer r1
    //     0x1293098: add             x1, x1, HEAP, lsl #32
    // 0x129309c: cmp             w1, NULL
    // 0x12930a0: b.ne            #0x12930ac
    // 0x12930a4: r3 = ""
    //     0x12930a4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12930a8: b               #0x12930b0
    // 0x12930ac: mov             x3, x1
    // 0x12930b0: ldur            x1, [fp, #-0x10]
    // 0x12930b4: stur            x3, [fp, #-0x18]
    // 0x12930b8: r0 = of()
    //     0x12930b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12930bc: LoadField: r1 = r0->field_87
    //     0x12930bc: ldur            w1, [x0, #0x87]
    // 0x12930c0: DecompressPointer r1
    //     0x12930c0: add             x1, x1, HEAP, lsl #32
    // 0x12930c4: LoadField: r2 = r1->field_2b
    //     0x12930c4: ldur            w2, [x1, #0x2b]
    // 0x12930c8: DecompressPointer r2
    //     0x12930c8: add             x2, x2, HEAP, lsl #32
    // 0x12930cc: ldur            x1, [fp, #-8]
    // 0x12930d0: stur            x2, [fp, #-0x28]
    // 0x12930d4: LoadField: r0 = r1->field_7
    //     0x12930d4: ldur            w0, [x1, #7]
    // 0x12930d8: DecompressPointer r0
    //     0x12930d8: add             x0, x0, HEAP, lsl #32
    // 0x12930dc: r3 = LoadClassIdInstr(r0)
    //     0x12930dc: ldur            x3, [x0, #-1]
    //     0x12930e0: ubfx            x3, x3, #0xc, #0x14
    // 0x12930e4: r16 = "final"
    //     0x12930e4: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x12930e8: ldr             x16, [x16, #0x3d8]
    // 0x12930ec: stp             x16, x0, [SP]
    // 0x12930f0: mov             x0, x3
    // 0x12930f4: mov             lr, x0
    // 0x12930f8: ldr             lr, [x21, lr, lsl #3]
    // 0x12930fc: blr             lr
    // 0x1293100: tbnz            w0, #4, #0x129310c
    // 0x1293104: r1 = Instance_Color
    //     0x1293104: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1293108: b               #0x1293120
    // 0x129310c: r1 = Instance_Color
    //     0x129310c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1293110: d0 = 0.700000
    //     0x1293110: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1293114: ldr             d0, [x17, #0xf48]
    // 0x1293118: r0 = withOpacity()
    //     0x1293118: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x129311c: mov             x1, x0
    // 0x1293120: ldur            x0, [fp, #-8]
    // 0x1293124: ldur            x2, [fp, #-0x18]
    // 0x1293128: r16 = 14.000000
    //     0x1293128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x129312c: ldr             x16, [x16, #0x1d8]
    // 0x1293130: stp             x16, x1, [SP]
    // 0x1293134: ldur            x1, [fp, #-0x28]
    // 0x1293138: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1293138: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x129313c: ldr             x4, [x4, #0x9b8]
    // 0x1293140: r0 = copyWith()
    //     0x1293140: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293144: stur            x0, [fp, #-0x28]
    // 0x1293148: r0 = Text()
    //     0x1293148: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x129314c: mov             x2, x0
    // 0x1293150: ldur            x0, [fp, #-0x18]
    // 0x1293154: stur            x2, [fp, #-0x30]
    // 0x1293158: StoreField: r2->field_b = r0
    //     0x1293158: stur            w0, [x2, #0xb]
    // 0x129315c: ldur            x0, [fp, #-0x28]
    // 0x1293160: StoreField: r2->field_13 = r0
    //     0x1293160: stur            w0, [x2, #0x13]
    // 0x1293164: ldur            x0, [fp, #-8]
    // 0x1293168: LoadField: r1 = r0->field_f
    //     0x1293168: ldur            w1, [x0, #0xf]
    // 0x129316c: DecompressPointer r1
    //     0x129316c: add             x1, x1, HEAP, lsl #32
    // 0x1293170: cmp             w1, NULL
    // 0x1293174: b.ne            #0x1293180
    // 0x1293178: r3 = ""
    //     0x1293178: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x129317c: b               #0x1293184
    // 0x1293180: mov             x3, x1
    // 0x1293184: ldur            x1, [fp, #-0x10]
    // 0x1293188: stur            x3, [fp, #-0x18]
    // 0x129318c: r0 = of()
    //     0x129318c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293190: LoadField: r1 = r0->field_87
    //     0x1293190: ldur            w1, [x0, #0x87]
    // 0x1293194: DecompressPointer r1
    //     0x1293194: add             x1, x1, HEAP, lsl #32
    // 0x1293198: LoadField: r2 = r1->field_2b
    //     0x1293198: ldur            w2, [x1, #0x2b]
    // 0x129319c: DecompressPointer r2
    //     0x129319c: add             x2, x2, HEAP, lsl #32
    // 0x12931a0: ldur            x0, [fp, #-8]
    // 0x12931a4: stur            x2, [fp, #-0x10]
    // 0x12931a8: LoadField: r1 = r0->field_7
    //     0x12931a8: ldur            w1, [x0, #7]
    // 0x12931ac: DecompressPointer r1
    //     0x12931ac: add             x1, x1, HEAP, lsl #32
    // 0x12931b0: r0 = LoadClassIdInstr(r1)
    //     0x12931b0: ldur            x0, [x1, #-1]
    //     0x12931b4: ubfx            x0, x0, #0xc, #0x14
    // 0x12931b8: r16 = "final"
    //     0x12931b8: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x12931bc: ldr             x16, [x16, #0x3d8]
    // 0x12931c0: stp             x16, x1, [SP]
    // 0x12931c4: mov             lr, x0
    // 0x12931c8: ldr             lr, [x21, lr, lsl #3]
    // 0x12931cc: blr             lr
    // 0x12931d0: tbnz            w0, #4, #0x12931dc
    // 0x12931d4: r1 = Instance_Color
    //     0x12931d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12931d8: b               #0x12931f0
    // 0x12931dc: r1 = Instance_Color
    //     0x12931dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12931e0: d0 = 0.700000
    //     0x12931e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x12931e4: ldr             d0, [x17, #0xf48]
    // 0x12931e8: r0 = withOpacity()
    //     0x12931e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12931ec: mov             x1, x0
    // 0x12931f0: ldur            x3, [fp, #-0x20]
    // 0x12931f4: ldur            x0, [fp, #-0x30]
    // 0x12931f8: ldur            x2, [fp, #-0x18]
    // 0x12931fc: r16 = 14.000000
    //     0x12931fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1293200: ldr             x16, [x16, #0x1d8]
    // 0x1293204: stp             x16, x1, [SP]
    // 0x1293208: ldur            x1, [fp, #-0x10]
    // 0x129320c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x129320c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1293210: ldr             x4, [x4, #0x9b8]
    // 0x1293214: r0 = copyWith()
    //     0x1293214: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293218: stur            x0, [fp, #-8]
    // 0x129321c: r0 = Text()
    //     0x129321c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1293220: mov             x3, x0
    // 0x1293224: ldur            x0, [fp, #-0x18]
    // 0x1293228: stur            x3, [fp, #-0x10]
    // 0x129322c: StoreField: r3->field_b = r0
    //     0x129322c: stur            w0, [x3, #0xb]
    // 0x1293230: ldur            x0, [fp, #-8]
    // 0x1293234: StoreField: r3->field_13 = r0
    //     0x1293234: stur            w0, [x3, #0x13]
    // 0x1293238: r1 = Null
    //     0x1293238: mov             x1, NULL
    // 0x129323c: r2 = 6
    //     0x129323c: movz            x2, #0x6
    // 0x1293240: r0 = AllocateArray()
    //     0x1293240: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1293244: mov             x2, x0
    // 0x1293248: ldur            x0, [fp, #-0x30]
    // 0x129324c: stur            x2, [fp, #-8]
    // 0x1293250: StoreField: r2->field_f = r0
    //     0x1293250: stur            w0, [x2, #0xf]
    // 0x1293254: r16 = Instance_SizedBox
    //     0x1293254: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x1293258: ldr             x16, [x16, #0x8b8]
    // 0x129325c: StoreField: r2->field_13 = r16
    //     0x129325c: stur            w16, [x2, #0x13]
    // 0x1293260: ldur            x0, [fp, #-0x10]
    // 0x1293264: ArrayStore: r2[0] = r0  ; List_4
    //     0x1293264: stur            w0, [x2, #0x17]
    // 0x1293268: r1 = <Widget>
    //     0x1293268: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129326c: r0 = AllocateGrowableArray()
    //     0x129326c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1293270: mov             x1, x0
    // 0x1293274: ldur            x0, [fp, #-8]
    // 0x1293278: stur            x1, [fp, #-0x10]
    // 0x129327c: StoreField: r1->field_f = r0
    //     0x129327c: stur            w0, [x1, #0xf]
    // 0x1293280: r0 = 6
    //     0x1293280: movz            x0, #0x6
    // 0x1293284: StoreField: r1->field_b = r0
    //     0x1293284: stur            w0, [x1, #0xb]
    // 0x1293288: r0 = Row()
    //     0x1293288: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x129328c: mov             x3, x0
    // 0x1293290: r0 = Instance_Axis
    //     0x1293290: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1293294: stur            x3, [fp, #-8]
    // 0x1293298: StoreField: r3->field_f = r0
    //     0x1293298: stur            w0, [x3, #0xf]
    // 0x129329c: r0 = Instance_MainAxisAlignment
    //     0x129329c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x12932a0: ldr             x0, [x0, #0xa8]
    // 0x12932a4: StoreField: r3->field_13 = r0
    //     0x12932a4: stur            w0, [x3, #0x13]
    // 0x12932a8: r0 = Instance_MainAxisSize
    //     0x12932a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12932ac: ldr             x0, [x0, #0xa10]
    // 0x12932b0: ArrayStore: r3[0] = r0  ; List_4
    //     0x12932b0: stur            w0, [x3, #0x17]
    // 0x12932b4: r4 = Instance_CrossAxisAlignment
    //     0x12932b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12932b8: ldr             x4, [x4, #0xa18]
    // 0x12932bc: StoreField: r3->field_1b = r4
    //     0x12932bc: stur            w4, [x3, #0x1b]
    // 0x12932c0: r5 = Instance_VerticalDirection
    //     0x12932c0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12932c4: ldr             x5, [x5, #0xa20]
    // 0x12932c8: StoreField: r3->field_23 = r5
    //     0x12932c8: stur            w5, [x3, #0x23]
    // 0x12932cc: r6 = Instance_Clip
    //     0x12932cc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12932d0: ldr             x6, [x6, #0x38]
    // 0x12932d4: StoreField: r3->field_2b = r6
    //     0x12932d4: stur            w6, [x3, #0x2b]
    // 0x12932d8: StoreField: r3->field_2f = rZR
    //     0x12932d8: stur            xzr, [x3, #0x2f]
    // 0x12932dc: ldur            x1, [fp, #-0x10]
    // 0x12932e0: StoreField: r3->field_b = r1
    //     0x12932e0: stur            w1, [x3, #0xb]
    // 0x12932e4: r1 = Null
    //     0x12932e4: mov             x1, NULL
    // 0x12932e8: r2 = 4
    //     0x12932e8: movz            x2, #0x4
    // 0x12932ec: r0 = AllocateArray()
    //     0x12932ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12932f0: mov             x2, x0
    // 0x12932f4: ldur            x0, [fp, #-0x20]
    // 0x12932f8: stur            x2, [fp, #-0x10]
    // 0x12932fc: StoreField: r2->field_f = r0
    //     0x12932fc: stur            w0, [x2, #0xf]
    // 0x1293300: ldur            x0, [fp, #-8]
    // 0x1293304: StoreField: r2->field_13 = r0
    //     0x1293304: stur            w0, [x2, #0x13]
    // 0x1293308: r1 = <Widget>
    //     0x1293308: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129330c: r0 = AllocateGrowableArray()
    //     0x129330c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1293310: mov             x1, x0
    // 0x1293314: ldur            x0, [fp, #-0x10]
    // 0x1293318: stur            x1, [fp, #-8]
    // 0x129331c: StoreField: r1->field_f = r0
    //     0x129331c: stur            w0, [x1, #0xf]
    // 0x1293320: r0 = 4
    //     0x1293320: movz            x0, #0x4
    // 0x1293324: StoreField: r1->field_b = r0
    //     0x1293324: stur            w0, [x1, #0xb]
    // 0x1293328: r0 = Column()
    //     0x1293328: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x129332c: mov             x1, x0
    // 0x1293330: r0 = Instance_Axis
    //     0x1293330: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1293334: stur            x1, [fp, #-0x10]
    // 0x1293338: StoreField: r1->field_f = r0
    //     0x1293338: stur            w0, [x1, #0xf]
    // 0x129333c: r0 = Instance_MainAxisAlignment
    //     0x129333c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x1293340: ldr             x0, [x0, #0xd10]
    // 0x1293344: StoreField: r1->field_13 = r0
    //     0x1293344: stur            w0, [x1, #0x13]
    // 0x1293348: r0 = Instance_MainAxisSize
    //     0x1293348: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x129334c: ldr             x0, [x0, #0xa10]
    // 0x1293350: ArrayStore: r1[0] = r0  ; List_4
    //     0x1293350: stur            w0, [x1, #0x17]
    // 0x1293354: r0 = Instance_CrossAxisAlignment
    //     0x1293354: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1293358: ldr             x0, [x0, #0xa18]
    // 0x129335c: StoreField: r1->field_1b = r0
    //     0x129335c: stur            w0, [x1, #0x1b]
    // 0x1293360: r0 = Instance_VerticalDirection
    //     0x1293360: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1293364: ldr             x0, [x0, #0xa20]
    // 0x1293368: StoreField: r1->field_23 = r0
    //     0x1293368: stur            w0, [x1, #0x23]
    // 0x129336c: r0 = Instance_Clip
    //     0x129336c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1293370: ldr             x0, [x0, #0x38]
    // 0x1293374: StoreField: r1->field_2b = r0
    //     0x1293374: stur            w0, [x1, #0x2b]
    // 0x1293378: StoreField: r1->field_2f = rZR
    //     0x1293378: stur            xzr, [x1, #0x2f]
    // 0x129337c: ldur            x0, [fp, #-8]
    // 0x1293380: StoreField: r1->field_b = r0
    //     0x1293380: stur            w0, [x1, #0xb]
    // 0x1293384: r0 = Padding()
    //     0x1293384: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1293388: r1 = Instance_EdgeInsets
    //     0x1293388: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x129338c: ldr             x1, [x1, #0x980]
    // 0x1293390: StoreField: r0->field_f = r1
    //     0x1293390: stur            w1, [x0, #0xf]
    // 0x1293394: ldur            x1, [fp, #-0x10]
    // 0x1293398: StoreField: r0->field_b = r1
    //     0x1293398: stur            w1, [x0, #0xb]
    // 0x129339c: LeaveFrame
    //     0x129339c: mov             SP, fp
    //     0x12933a0: ldp             fp, lr, [SP], #0x10
    // 0x12933a4: ret
    //     0x12933a4: ret             
    // 0x12933a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12933a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12933ac: b               #0x1292fd4
  }
}
