// lib: , url: package:customer_app/app/presentation/views/glass/orders/cancel_return_order_with_free_product_bottom_sheet.dart

// class id: 1049412, size: 0x8
class :: {
}

// class id: 3327, size: 0x14, field offset: 0x14
class _CancelReturnOrderWithFreeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb731dc, size: 0x2138
    // 0xb731dc: EnterFrame
    //     0xb731dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb731e0: mov             fp, SP
    // 0xb731e4: AllocStack(0x98)
    //     0xb731e4: sub             SP, SP, #0x98
    // 0xb731e8: SetupParameters(_CancelReturnOrderWithFreeProductBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb731e8: mov             x0, x1
    //     0xb731ec: stur            x1, [fp, #-8]
    //     0xb731f0: mov             x1, x2
    //     0xb731f4: stur            x2, [fp, #-0x10]
    // 0xb731f8: CheckStackOverflow
    //     0xb731f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb731fc: cmp             SP, x16
    //     0xb73200: b.ls            #0xb75234
    // 0xb73204: r1 = 1
    //     0xb73204: movz            x1, #0x1
    // 0xb73208: r0 = AllocateContext()
    //     0xb73208: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7320c: mov             x2, x0
    // 0xb73210: ldur            x0, [fp, #-8]
    // 0xb73214: stur            x2, [fp, #-0x20]
    // 0xb73218: StoreField: r2->field_f = r0
    //     0xb73218: stur            w0, [x2, #0xf]
    // 0xb7321c: LoadField: r1 = r0->field_b
    //     0xb7321c: ldur            w1, [x0, #0xb]
    // 0xb73220: DecompressPointer r1
    //     0xb73220: add             x1, x1, HEAP, lsl #32
    // 0xb73224: cmp             w1, NULL
    // 0xb73228: b.eq            #0xb7523c
    // 0xb7322c: LoadField: r3 = r1->field_13
    //     0xb7322c: ldur            w3, [x1, #0x13]
    // 0xb73230: DecompressPointer r3
    //     0xb73230: add             x3, x3, HEAP, lsl #32
    // 0xb73234: ldur            x1, [fp, #-0x10]
    // 0xb73238: stur            x3, [fp, #-0x18]
    // 0xb7323c: r0 = of()
    //     0xb7323c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb73240: LoadField: r1 = r0->field_87
    //     0xb73240: ldur            w1, [x0, #0x87]
    // 0xb73244: DecompressPointer r1
    //     0xb73244: add             x1, x1, HEAP, lsl #32
    // 0xb73248: LoadField: r0 = r1->field_7
    //     0xb73248: ldur            w0, [x1, #7]
    // 0xb7324c: DecompressPointer r0
    //     0xb7324c: add             x0, x0, HEAP, lsl #32
    // 0xb73250: stur            x0, [fp, #-0x28]
    // 0xb73254: r1 = Instance_Color
    //     0xb73254: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb73258: d0 = 0.700000
    //     0xb73258: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb7325c: ldr             d0, [x17, #0xf48]
    // 0xb73260: r0 = withOpacity()
    //     0xb73260: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb73264: r16 = 14.000000
    //     0xb73264: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb73268: ldr             x16, [x16, #0x1d8]
    // 0xb7326c: stp             x16, x0, [SP]
    // 0xb73270: ldur            x1, [fp, #-0x28]
    // 0xb73274: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb73274: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb73278: ldr             x4, [x4, #0x9b8]
    // 0xb7327c: r0 = copyWith()
    //     0xb7327c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb73280: stur            x0, [fp, #-0x28]
    // 0xb73284: r0 = Text()
    //     0xb73284: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb73288: mov             x2, x0
    // 0xb7328c: ldur            x0, [fp, #-0x18]
    // 0xb73290: stur            x2, [fp, #-0x30]
    // 0xb73294: StoreField: r2->field_b = r0
    //     0xb73294: stur            w0, [x2, #0xb]
    // 0xb73298: ldur            x0, [fp, #-0x28]
    // 0xb7329c: StoreField: r2->field_13 = r0
    //     0xb7329c: stur            w0, [x2, #0x13]
    // 0xb732a0: ldur            x0, [fp, #-8]
    // 0xb732a4: LoadField: r1 = r0->field_b
    //     0xb732a4: ldur            w1, [x0, #0xb]
    // 0xb732a8: DecompressPointer r1
    //     0xb732a8: add             x1, x1, HEAP, lsl #32
    // 0xb732ac: cmp             w1, NULL
    // 0xb732b0: b.eq            #0xb75240
    // 0xb732b4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb732b4: ldur            w3, [x1, #0x17]
    // 0xb732b8: DecompressPointer r3
    //     0xb732b8: add             x3, x3, HEAP, lsl #32
    // 0xb732bc: ldur            x1, [fp, #-0x10]
    // 0xb732c0: stur            x3, [fp, #-0x18]
    // 0xb732c4: r0 = of()
    //     0xb732c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb732c8: LoadField: r1 = r0->field_87
    //     0xb732c8: ldur            w1, [x0, #0x87]
    // 0xb732cc: DecompressPointer r1
    //     0xb732cc: add             x1, x1, HEAP, lsl #32
    // 0xb732d0: LoadField: r0 = r1->field_2b
    //     0xb732d0: ldur            w0, [x1, #0x2b]
    // 0xb732d4: DecompressPointer r0
    //     0xb732d4: add             x0, x0, HEAP, lsl #32
    // 0xb732d8: stur            x0, [fp, #-0x28]
    // 0xb732dc: r1 = Instance_Color
    //     0xb732dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb732e0: d0 = 0.700000
    //     0xb732e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb732e4: ldr             d0, [x17, #0xf48]
    // 0xb732e8: r0 = withOpacity()
    //     0xb732e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb732ec: r16 = 12.000000
    //     0xb732ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb732f0: ldr             x16, [x16, #0x9e8]
    // 0xb732f4: stp             x16, x0, [SP]
    // 0xb732f8: ldur            x1, [fp, #-0x28]
    // 0xb732fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb732fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb73300: ldr             x4, [x4, #0x9b8]
    // 0xb73304: r0 = copyWith()
    //     0xb73304: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb73308: stur            x0, [fp, #-0x28]
    // 0xb7330c: r0 = Text()
    //     0xb7330c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb73310: mov             x1, x0
    // 0xb73314: ldur            x0, [fp, #-0x18]
    // 0xb73318: stur            x1, [fp, #-0x38]
    // 0xb7331c: StoreField: r1->field_b = r0
    //     0xb7331c: stur            w0, [x1, #0xb]
    // 0xb73320: ldur            x0, [fp, #-0x28]
    // 0xb73324: StoreField: r1->field_13 = r0
    //     0xb73324: stur            w0, [x1, #0x13]
    // 0xb73328: r0 = Padding()
    //     0xb73328: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7332c: mov             x1, x0
    // 0xb73330: r0 = Instance_EdgeInsets
    //     0xb73330: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb73334: ldr             x0, [x0, #0x668]
    // 0xb73338: stur            x1, [fp, #-0x18]
    // 0xb7333c: StoreField: r1->field_f = r0
    //     0xb7333c: stur            w0, [x1, #0xf]
    // 0xb73340: ldur            x0, [fp, #-0x38]
    // 0xb73344: StoreField: r1->field_b = r0
    //     0xb73344: stur            w0, [x1, #0xb]
    // 0xb73348: r0 = Radius()
    //     0xb73348: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7334c: d0 = 12.000000
    //     0xb7334c: fmov            d0, #12.00000000
    // 0xb73350: stur            x0, [fp, #-0x28]
    // 0xb73354: StoreField: r0->field_7 = d0
    //     0xb73354: stur            d0, [x0, #7]
    // 0xb73358: StoreField: r0->field_f = d0
    //     0xb73358: stur            d0, [x0, #0xf]
    // 0xb7335c: r0 = BorderRadius()
    //     0xb7335c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb73360: mov             x2, x0
    // 0xb73364: ldur            x0, [fp, #-0x28]
    // 0xb73368: stur            x2, [fp, #-0x38]
    // 0xb7336c: StoreField: r2->field_7 = r0
    //     0xb7336c: stur            w0, [x2, #7]
    // 0xb73370: StoreField: r2->field_b = r0
    //     0xb73370: stur            w0, [x2, #0xb]
    // 0xb73374: StoreField: r2->field_f = r0
    //     0xb73374: stur            w0, [x2, #0xf]
    // 0xb73378: StoreField: r2->field_13 = r0
    //     0xb73378: stur            w0, [x2, #0x13]
    // 0xb7337c: r1 = Instance_Color
    //     0xb7337c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb73380: d0 = 0.070000
    //     0xb73380: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb73384: ldr             d0, [x17, #0x5f8]
    // 0xb73388: r0 = withOpacity()
    //     0xb73388: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7338c: r16 = 1.000000
    //     0xb7338c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb73390: str             x16, [SP]
    // 0xb73394: mov             x2, x0
    // 0xb73398: r1 = Null
    //     0xb73398: mov             x1, NULL
    // 0xb7339c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb7339c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb733a0: ldr             x4, [x4, #0x108]
    // 0xb733a4: r0 = Border.all()
    //     0xb733a4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb733a8: stur            x0, [fp, #-0x28]
    // 0xb733ac: r0 = BoxDecoration()
    //     0xb733ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb733b0: mov             x2, x0
    // 0xb733b4: ldur            x0, [fp, #-0x28]
    // 0xb733b8: stur            x2, [fp, #-0x40]
    // 0xb733bc: StoreField: r2->field_f = r0
    //     0xb733bc: stur            w0, [x2, #0xf]
    // 0xb733c0: ldur            x0, [fp, #-0x38]
    // 0xb733c4: StoreField: r2->field_13 = r0
    //     0xb733c4: stur            w0, [x2, #0x13]
    // 0xb733c8: r0 = Instance_LinearGradient
    //     0xb733c8: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xb733cc: ldr             x0, [x0, #0x660]
    // 0xb733d0: StoreField: r2->field_1b = r0
    //     0xb733d0: stur            w0, [x2, #0x1b]
    // 0xb733d4: r0 = Instance_BoxShape
    //     0xb733d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb733d8: ldr             x0, [x0, #0x80]
    // 0xb733dc: StoreField: r2->field_23 = r0
    //     0xb733dc: stur            w0, [x2, #0x23]
    // 0xb733e0: ldur            x1, [fp, #-0x10]
    // 0xb733e4: r0 = of()
    //     0xb733e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb733e8: LoadField: r1 = r0->field_87
    //     0xb733e8: ldur            w1, [x0, #0x87]
    // 0xb733ec: DecompressPointer r1
    //     0xb733ec: add             x1, x1, HEAP, lsl #32
    // 0xb733f0: LoadField: r0 = r1->field_7
    //     0xb733f0: ldur            w0, [x1, #7]
    // 0xb733f4: DecompressPointer r0
    //     0xb733f4: add             x0, x0, HEAP, lsl #32
    // 0xb733f8: r16 = 12.000000
    //     0xb733f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb733fc: ldr             x16, [x16, #0x9e8]
    // 0xb73400: r30 = Instance_Color
    //     0xb73400: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb73404: stp             lr, x16, [SP]
    // 0xb73408: mov             x1, x0
    // 0xb7340c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7340c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb73410: ldr             x4, [x4, #0xaa0]
    // 0xb73414: r0 = copyWith()
    //     0xb73414: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb73418: stur            x0, [fp, #-0x28]
    // 0xb7341c: r0 = Text()
    //     0xb7341c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb73420: mov             x1, x0
    // 0xb73424: r0 = "Free"
    //     0xb73424: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb73428: ldr             x0, [x0, #0x668]
    // 0xb7342c: stur            x1, [fp, #-0x38]
    // 0xb73430: StoreField: r1->field_b = r0
    //     0xb73430: stur            w0, [x1, #0xb]
    // 0xb73434: ldur            x2, [fp, #-0x28]
    // 0xb73438: StoreField: r1->field_13 = r2
    //     0xb73438: stur            w2, [x1, #0x13]
    // 0xb7343c: r0 = Center()
    //     0xb7343c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb73440: mov             x1, x0
    // 0xb73444: r0 = Instance_Alignment
    //     0xb73444: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb73448: ldr             x0, [x0, #0xb10]
    // 0xb7344c: stur            x1, [fp, #-0x28]
    // 0xb73450: StoreField: r1->field_f = r0
    //     0xb73450: stur            w0, [x1, #0xf]
    // 0xb73454: ldur            x0, [fp, #-0x38]
    // 0xb73458: StoreField: r1->field_b = r0
    //     0xb73458: stur            w0, [x1, #0xb]
    // 0xb7345c: r0 = RotatedBox()
    //     0xb7345c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb73460: mov             x1, x0
    // 0xb73464: r0 = -1
    //     0xb73464: movn            x0, #0
    // 0xb73468: stur            x1, [fp, #-0x38]
    // 0xb7346c: StoreField: r1->field_f = r0
    //     0xb7346c: stur            x0, [x1, #0xf]
    // 0xb73470: ldur            x0, [fp, #-0x28]
    // 0xb73474: StoreField: r1->field_b = r0
    //     0xb73474: stur            w0, [x1, #0xb]
    // 0xb73478: r0 = Container()
    //     0xb73478: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7347c: stur            x0, [fp, #-0x28]
    // 0xb73480: r16 = 24.000000
    //     0xb73480: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb73484: ldr             x16, [x16, #0xba8]
    // 0xb73488: r30 = 56.000000
    //     0xb73488: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb7348c: ldr             lr, [lr, #0xb78]
    // 0xb73490: stp             lr, x16, [SP, #0x10]
    // 0xb73494: r16 = Instance_BoxDecoration
    //     0xb73494: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xb73498: ldr             x16, [x16, #0xdb0]
    // 0xb7349c: ldur            lr, [fp, #-0x38]
    // 0xb734a0: stp             lr, x16, [SP]
    // 0xb734a4: mov             x1, x0
    // 0xb734a8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb734a8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb734ac: ldr             x4, [x4, #0x870]
    // 0xb734b0: r0 = Container()
    //     0xb734b0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb734b4: ldur            x0, [fp, #-8]
    // 0xb734b8: LoadField: r1 = r0->field_b
    //     0xb734b8: ldur            w1, [x0, #0xb]
    // 0xb734bc: DecompressPointer r1
    //     0xb734bc: add             x1, x1, HEAP, lsl #32
    // 0xb734c0: cmp             w1, NULL
    // 0xb734c4: b.eq            #0xb75244
    // 0xb734c8: LoadField: r2 = r1->field_f
    //     0xb734c8: ldur            w2, [x1, #0xf]
    // 0xb734cc: DecompressPointer r2
    //     0xb734cc: add             x2, x2, HEAP, lsl #32
    // 0xb734d0: r16 = "return_order_intermediate"
    //     0xb734d0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb734d4: ldr             x16, [x16, #0xb00]
    // 0xb734d8: stp             x16, x2, [SP]
    // 0xb734dc: r0 = ==()
    //     0xb734dc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb734e0: tbnz            w0, #4, #0xb73548
    // 0xb734e4: ldur            x0, [fp, #-8]
    // 0xb734e8: LoadField: r1 = r0->field_b
    //     0xb734e8: ldur            w1, [x0, #0xb]
    // 0xb734ec: DecompressPointer r1
    //     0xb734ec: add             x1, x1, HEAP, lsl #32
    // 0xb734f0: cmp             w1, NULL
    // 0xb734f4: b.eq            #0xb75248
    // 0xb734f8: LoadField: r2 = r1->field_23
    //     0xb734f8: ldur            w2, [x1, #0x23]
    // 0xb734fc: DecompressPointer r2
    //     0xb734fc: add             x2, x2, HEAP, lsl #32
    // 0xb73500: cmp             w2, NULL
    // 0xb73504: b.ne            #0xb73510
    // 0xb73508: r1 = Null
    //     0xb73508: mov             x1, NULL
    // 0xb7350c: b               #0xb73534
    // 0xb73510: LoadField: r1 = r2->field_3f
    //     0xb73510: ldur            w1, [x2, #0x3f]
    // 0xb73514: DecompressPointer r1
    //     0xb73514: add             x1, x1, HEAP, lsl #32
    // 0xb73518: cmp             w1, NULL
    // 0xb7351c: b.ne            #0xb73528
    // 0xb73520: r1 = Null
    //     0xb73520: mov             x1, NULL
    // 0xb73524: b               #0xb73534
    // 0xb73528: LoadField: r2 = r1->field_7
    //     0xb73528: ldur            w2, [x1, #7]
    // 0xb7352c: DecompressPointer r2
    //     0xb7352c: add             x2, x2, HEAP, lsl #32
    // 0xb73530: mov             x1, x2
    // 0xb73534: cmp             w1, NULL
    // 0xb73538: b.ne            #0xb73540
    // 0xb7353c: r1 = ""
    //     0xb7353c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73540: mov             x2, x1
    // 0xb73544: b               #0xb73638
    // 0xb73548: ldur            x0, [fp, #-8]
    // 0xb7354c: LoadField: r1 = r0->field_b
    //     0xb7354c: ldur            w1, [x0, #0xb]
    // 0xb73550: DecompressPointer r1
    //     0xb73550: add             x1, x1, HEAP, lsl #32
    // 0xb73554: cmp             w1, NULL
    // 0xb73558: b.eq            #0xb7524c
    // 0xb7355c: LoadField: r2 = r1->field_f
    //     0xb7355c: ldur            w2, [x1, #0xf]
    // 0xb73560: DecompressPointer r2
    //     0xb73560: add             x2, x2, HEAP, lsl #32
    // 0xb73564: r16 = "cancel_order"
    //     0xb73564: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb73568: ldr             x16, [x16, #0x98]
    // 0xb7356c: stp             x16, x2, [SP]
    // 0xb73570: r0 = ==()
    //     0xb73570: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73574: tbnz            w0, #4, #0xb735d8
    // 0xb73578: ldur            x0, [fp, #-8]
    // 0xb7357c: LoadField: r1 = r0->field_b
    //     0xb7357c: ldur            w1, [x0, #0xb]
    // 0xb73580: DecompressPointer r1
    //     0xb73580: add             x1, x1, HEAP, lsl #32
    // 0xb73584: cmp             w1, NULL
    // 0xb73588: b.eq            #0xb75250
    // 0xb7358c: LoadField: r2 = r1->field_1b
    //     0xb7358c: ldur            w2, [x1, #0x1b]
    // 0xb73590: DecompressPointer r2
    //     0xb73590: add             x2, x2, HEAP, lsl #32
    // 0xb73594: cmp             w2, NULL
    // 0xb73598: b.ne            #0xb735a4
    // 0xb7359c: r1 = Null
    //     0xb7359c: mov             x1, NULL
    // 0xb735a0: b               #0xb735c8
    // 0xb735a4: LoadField: r1 = r2->field_7f
    //     0xb735a4: ldur            w1, [x2, #0x7f]
    // 0xb735a8: DecompressPointer r1
    //     0xb735a8: add             x1, x1, HEAP, lsl #32
    // 0xb735ac: cmp             w1, NULL
    // 0xb735b0: b.ne            #0xb735bc
    // 0xb735b4: r1 = Null
    //     0xb735b4: mov             x1, NULL
    // 0xb735b8: b               #0xb735c8
    // 0xb735bc: LoadField: r2 = r1->field_7
    //     0xb735bc: ldur            w2, [x1, #7]
    // 0xb735c0: DecompressPointer r2
    //     0xb735c0: add             x2, x2, HEAP, lsl #32
    // 0xb735c4: mov             x1, x2
    // 0xb735c8: cmp             w1, NULL
    // 0xb735cc: b.ne            #0xb73634
    // 0xb735d0: r1 = ""
    //     0xb735d0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb735d4: b               #0xb73634
    // 0xb735d8: ldur            x0, [fp, #-8]
    // 0xb735dc: LoadField: r1 = r0->field_b
    //     0xb735dc: ldur            w1, [x0, #0xb]
    // 0xb735e0: DecompressPointer r1
    //     0xb735e0: add             x1, x1, HEAP, lsl #32
    // 0xb735e4: cmp             w1, NULL
    // 0xb735e8: b.eq            #0xb75254
    // 0xb735ec: LoadField: r2 = r1->field_1f
    //     0xb735ec: ldur            w2, [x1, #0x1f]
    // 0xb735f0: DecompressPointer r2
    //     0xb735f0: add             x2, x2, HEAP, lsl #32
    // 0xb735f4: cmp             w2, NULL
    // 0xb735f8: b.ne            #0xb73604
    // 0xb735fc: r1 = Null
    //     0xb735fc: mov             x1, NULL
    // 0xb73600: b               #0xb73628
    // 0xb73604: LoadField: r1 = r2->field_d7
    //     0xb73604: ldur            w1, [x2, #0xd7]
    // 0xb73608: DecompressPointer r1
    //     0xb73608: add             x1, x1, HEAP, lsl #32
    // 0xb7360c: cmp             w1, NULL
    // 0xb73610: b.ne            #0xb7361c
    // 0xb73614: r1 = Null
    //     0xb73614: mov             x1, NULL
    // 0xb73618: b               #0xb73628
    // 0xb7361c: LoadField: r2 = r1->field_7
    //     0xb7361c: ldur            w2, [x1, #7]
    // 0xb73620: DecompressPointer r2
    //     0xb73620: add             x2, x2, HEAP, lsl #32
    // 0xb73624: mov             x1, x2
    // 0xb73628: cmp             w1, NULL
    // 0xb7362c: b.ne            #0xb73634
    // 0xb73630: r1 = ""
    //     0xb73630: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73634: mov             x2, x1
    // 0xb73638: stur            x2, [fp, #-0x38]
    // 0xb7363c: r0 = CachedNetworkImage()
    //     0xb7363c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb73640: stur            x0, [fp, #-0x48]
    // 0xb73644: r16 = 56.000000
    //     0xb73644: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb73648: ldr             x16, [x16, #0xb78]
    // 0xb7364c: r30 = 56.000000
    //     0xb7364c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb73650: ldr             lr, [lr, #0xb78]
    // 0xb73654: stp             lr, x16, [SP, #8]
    // 0xb73658: r16 = Instance_BoxFit
    //     0xb73658: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb7365c: ldr             x16, [x16, #0x118]
    // 0xb73660: str             x16, [SP]
    // 0xb73664: mov             x1, x0
    // 0xb73668: ldur            x2, [fp, #-0x38]
    // 0xb7366c: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb7366c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb73670: ldr             x4, [x4, #0xb40]
    // 0xb73674: r0 = CachedNetworkImage()
    //     0xb73674: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb73678: ldur            x0, [fp, #-8]
    // 0xb7367c: LoadField: r1 = r0->field_b
    //     0xb7367c: ldur            w1, [x0, #0xb]
    // 0xb73680: DecompressPointer r1
    //     0xb73680: add             x1, x1, HEAP, lsl #32
    // 0xb73684: cmp             w1, NULL
    // 0xb73688: b.eq            #0xb75258
    // 0xb7368c: LoadField: r2 = r1->field_f
    //     0xb7368c: ldur            w2, [x1, #0xf]
    // 0xb73690: DecompressPointer r2
    //     0xb73690: add             x2, x2, HEAP, lsl #32
    // 0xb73694: r16 = "return_order_intermediate"
    //     0xb73694: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb73698: ldr             x16, [x16, #0xb00]
    // 0xb7369c: stp             x16, x2, [SP]
    // 0xb736a0: r0 = ==()
    //     0xb736a0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb736a4: tbnz            w0, #4, #0xb7370c
    // 0xb736a8: ldur            x0, [fp, #-8]
    // 0xb736ac: LoadField: r1 = r0->field_b
    //     0xb736ac: ldur            w1, [x0, #0xb]
    // 0xb736b0: DecompressPointer r1
    //     0xb736b0: add             x1, x1, HEAP, lsl #32
    // 0xb736b4: cmp             w1, NULL
    // 0xb736b8: b.eq            #0xb7525c
    // 0xb736bc: LoadField: r2 = r1->field_23
    //     0xb736bc: ldur            w2, [x1, #0x23]
    // 0xb736c0: DecompressPointer r2
    //     0xb736c0: add             x2, x2, HEAP, lsl #32
    // 0xb736c4: cmp             w2, NULL
    // 0xb736c8: b.ne            #0xb736d4
    // 0xb736cc: r1 = Null
    //     0xb736cc: mov             x1, NULL
    // 0xb736d0: b               #0xb736f8
    // 0xb736d4: LoadField: r1 = r2->field_3f
    //     0xb736d4: ldur            w1, [x2, #0x3f]
    // 0xb736d8: DecompressPointer r1
    //     0xb736d8: add             x1, x1, HEAP, lsl #32
    // 0xb736dc: cmp             w1, NULL
    // 0xb736e0: b.ne            #0xb736ec
    // 0xb736e4: r1 = Null
    //     0xb736e4: mov             x1, NULL
    // 0xb736e8: b               #0xb736f8
    // 0xb736ec: LoadField: r2 = r1->field_b
    //     0xb736ec: ldur            w2, [x1, #0xb]
    // 0xb736f0: DecompressPointer r2
    //     0xb736f0: add             x2, x2, HEAP, lsl #32
    // 0xb736f4: mov             x1, x2
    // 0xb736f8: cmp             w1, NULL
    // 0xb736fc: b.ne            #0xb73704
    // 0xb73700: r1 = ""
    //     0xb73700: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73704: mov             x2, x1
    // 0xb73708: b               #0xb737fc
    // 0xb7370c: ldur            x0, [fp, #-8]
    // 0xb73710: LoadField: r1 = r0->field_b
    //     0xb73710: ldur            w1, [x0, #0xb]
    // 0xb73714: DecompressPointer r1
    //     0xb73714: add             x1, x1, HEAP, lsl #32
    // 0xb73718: cmp             w1, NULL
    // 0xb7371c: b.eq            #0xb75260
    // 0xb73720: LoadField: r2 = r1->field_f
    //     0xb73720: ldur            w2, [x1, #0xf]
    // 0xb73724: DecompressPointer r2
    //     0xb73724: add             x2, x2, HEAP, lsl #32
    // 0xb73728: r16 = "cancel_order"
    //     0xb73728: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb7372c: ldr             x16, [x16, #0x98]
    // 0xb73730: stp             x16, x2, [SP]
    // 0xb73734: r0 = ==()
    //     0xb73734: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73738: tbnz            w0, #4, #0xb7379c
    // 0xb7373c: ldur            x0, [fp, #-8]
    // 0xb73740: LoadField: r1 = r0->field_b
    //     0xb73740: ldur            w1, [x0, #0xb]
    // 0xb73744: DecompressPointer r1
    //     0xb73744: add             x1, x1, HEAP, lsl #32
    // 0xb73748: cmp             w1, NULL
    // 0xb7374c: b.eq            #0xb75264
    // 0xb73750: LoadField: r2 = r1->field_1b
    //     0xb73750: ldur            w2, [x1, #0x1b]
    // 0xb73754: DecompressPointer r2
    //     0xb73754: add             x2, x2, HEAP, lsl #32
    // 0xb73758: cmp             w2, NULL
    // 0xb7375c: b.ne            #0xb73768
    // 0xb73760: r1 = Null
    //     0xb73760: mov             x1, NULL
    // 0xb73764: b               #0xb7378c
    // 0xb73768: LoadField: r1 = r2->field_7f
    //     0xb73768: ldur            w1, [x2, #0x7f]
    // 0xb7376c: DecompressPointer r1
    //     0xb7376c: add             x1, x1, HEAP, lsl #32
    // 0xb73770: cmp             w1, NULL
    // 0xb73774: b.ne            #0xb73780
    // 0xb73778: r1 = Null
    //     0xb73778: mov             x1, NULL
    // 0xb7377c: b               #0xb7378c
    // 0xb73780: LoadField: r2 = r1->field_b
    //     0xb73780: ldur            w2, [x1, #0xb]
    // 0xb73784: DecompressPointer r2
    //     0xb73784: add             x2, x2, HEAP, lsl #32
    // 0xb73788: mov             x1, x2
    // 0xb7378c: cmp             w1, NULL
    // 0xb73790: b.ne            #0xb737f8
    // 0xb73794: r1 = ""
    //     0xb73794: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73798: b               #0xb737f8
    // 0xb7379c: ldur            x0, [fp, #-8]
    // 0xb737a0: LoadField: r1 = r0->field_b
    //     0xb737a0: ldur            w1, [x0, #0xb]
    // 0xb737a4: DecompressPointer r1
    //     0xb737a4: add             x1, x1, HEAP, lsl #32
    // 0xb737a8: cmp             w1, NULL
    // 0xb737ac: b.eq            #0xb75268
    // 0xb737b0: LoadField: r2 = r1->field_1f
    //     0xb737b0: ldur            w2, [x1, #0x1f]
    // 0xb737b4: DecompressPointer r2
    //     0xb737b4: add             x2, x2, HEAP, lsl #32
    // 0xb737b8: cmp             w2, NULL
    // 0xb737bc: b.ne            #0xb737c8
    // 0xb737c0: r1 = Null
    //     0xb737c0: mov             x1, NULL
    // 0xb737c4: b               #0xb737ec
    // 0xb737c8: LoadField: r1 = r2->field_d7
    //     0xb737c8: ldur            w1, [x2, #0xd7]
    // 0xb737cc: DecompressPointer r1
    //     0xb737cc: add             x1, x1, HEAP, lsl #32
    // 0xb737d0: cmp             w1, NULL
    // 0xb737d4: b.ne            #0xb737e0
    // 0xb737d8: r1 = Null
    //     0xb737d8: mov             x1, NULL
    // 0xb737dc: b               #0xb737ec
    // 0xb737e0: LoadField: r2 = r1->field_b
    //     0xb737e0: ldur            w2, [x1, #0xb]
    // 0xb737e4: DecompressPointer r2
    //     0xb737e4: add             x2, x2, HEAP, lsl #32
    // 0xb737e8: mov             x1, x2
    // 0xb737ec: cmp             w1, NULL
    // 0xb737f0: b.ne            #0xb737f8
    // 0xb737f4: r1 = ""
    //     0xb737f4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb737f8: mov             x2, x1
    // 0xb737fc: ldur            x1, [fp, #-0x10]
    // 0xb73800: stur            x2, [fp, #-0x38]
    // 0xb73804: r0 = of()
    //     0xb73804: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb73808: LoadField: r1 = r0->field_87
    //     0xb73808: ldur            w1, [x0, #0x87]
    // 0xb7380c: DecompressPointer r1
    //     0xb7380c: add             x1, x1, HEAP, lsl #32
    // 0xb73810: LoadField: r0 = r1->field_7
    //     0xb73810: ldur            w0, [x1, #7]
    // 0xb73814: DecompressPointer r0
    //     0xb73814: add             x0, x0, HEAP, lsl #32
    // 0xb73818: r16 = 12.000000
    //     0xb73818: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7381c: ldr             x16, [x16, #0x9e8]
    // 0xb73820: r30 = Instance_Color
    //     0xb73820: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb73824: stp             lr, x16, [SP]
    // 0xb73828: mov             x1, x0
    // 0xb7382c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7382c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb73830: ldr             x4, [x4, #0xaa0]
    // 0xb73834: r0 = copyWith()
    //     0xb73834: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb73838: stur            x0, [fp, #-0x50]
    // 0xb7383c: r0 = Text()
    //     0xb7383c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb73840: mov             x1, x0
    // 0xb73844: ldur            x0, [fp, #-0x38]
    // 0xb73848: stur            x1, [fp, #-0x58]
    // 0xb7384c: StoreField: r1->field_b = r0
    //     0xb7384c: stur            w0, [x1, #0xb]
    // 0xb73850: ldur            x0, [fp, #-0x50]
    // 0xb73854: StoreField: r1->field_13 = r0
    //     0xb73854: stur            w0, [x1, #0x13]
    // 0xb73858: r0 = Instance_TextOverflow
    //     0xb73858: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb7385c: ldr             x0, [x0, #0xe10]
    // 0xb73860: StoreField: r1->field_2b = r0
    //     0xb73860: stur            w0, [x1, #0x2b]
    // 0xb73864: r2 = 2
    //     0xb73864: movz            x2, #0x2
    // 0xb73868: StoreField: r1->field_37 = r2
    //     0xb73868: stur            w2, [x1, #0x37]
    // 0xb7386c: r0 = SizedBox()
    //     0xb7386c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb73870: mov             x2, x0
    // 0xb73874: r0 = 150.000000
    //     0xb73874: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb73878: ldr             x0, [x0, #0x690]
    // 0xb7387c: stur            x2, [fp, #-0x38]
    // 0xb73880: StoreField: r2->field_f = r0
    //     0xb73880: stur            w0, [x2, #0xf]
    // 0xb73884: ldur            x0, [fp, #-0x58]
    // 0xb73888: StoreField: r2->field_b = r0
    //     0xb73888: stur            w0, [x2, #0xb]
    // 0xb7388c: ldur            x1, [fp, #-0x10]
    // 0xb73890: r0 = of()
    //     0xb73890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb73894: LoadField: r1 = r0->field_87
    //     0xb73894: ldur            w1, [x0, #0x87]
    // 0xb73898: DecompressPointer r1
    //     0xb73898: add             x1, x1, HEAP, lsl #32
    // 0xb7389c: LoadField: r0 = r1->field_2b
    //     0xb7389c: ldur            w0, [x1, #0x2b]
    // 0xb738a0: DecompressPointer r0
    //     0xb738a0: add             x0, x0, HEAP, lsl #32
    // 0xb738a4: r16 = 12.000000
    //     0xb738a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb738a8: ldr             x16, [x16, #0x9e8]
    // 0xb738ac: r30 = Instance_Color
    //     0xb738ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb738b0: ldr             lr, [lr, #0x858]
    // 0xb738b4: stp             lr, x16, [SP]
    // 0xb738b8: mov             x1, x0
    // 0xb738bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb738bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb738c0: ldr             x4, [x4, #0xaa0]
    // 0xb738c4: r0 = copyWith()
    //     0xb738c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb738c8: stur            x0, [fp, #-0x50]
    // 0xb738cc: r0 = Text()
    //     0xb738cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb738d0: mov             x1, x0
    // 0xb738d4: r0 = "Free"
    //     0xb738d4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb738d8: ldr             x0, [x0, #0x668]
    // 0xb738dc: stur            x1, [fp, #-0x58]
    // 0xb738e0: StoreField: r1->field_b = r0
    //     0xb738e0: stur            w0, [x1, #0xb]
    // 0xb738e4: ldur            x0, [fp, #-0x50]
    // 0xb738e8: StoreField: r1->field_13 = r0
    //     0xb738e8: stur            w0, [x1, #0x13]
    // 0xb738ec: ldur            x0, [fp, #-8]
    // 0xb738f0: LoadField: r2 = r0->field_b
    //     0xb738f0: ldur            w2, [x0, #0xb]
    // 0xb738f4: DecompressPointer r2
    //     0xb738f4: add             x2, x2, HEAP, lsl #32
    // 0xb738f8: cmp             w2, NULL
    // 0xb738fc: b.eq            #0xb7526c
    // 0xb73900: LoadField: r3 = r2->field_f
    //     0xb73900: ldur            w3, [x2, #0xf]
    // 0xb73904: DecompressPointer r3
    //     0xb73904: add             x3, x3, HEAP, lsl #32
    // 0xb73908: r16 = "return_order_intermediate"
    //     0xb73908: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb7390c: ldr             x16, [x16, #0xb00]
    // 0xb73910: stp             x16, x3, [SP]
    // 0xb73914: r0 = ==()
    //     0xb73914: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73918: tbnz            w0, #4, #0xb73980
    // 0xb7391c: ldur            x0, [fp, #-8]
    // 0xb73920: LoadField: r1 = r0->field_b
    //     0xb73920: ldur            w1, [x0, #0xb]
    // 0xb73924: DecompressPointer r1
    //     0xb73924: add             x1, x1, HEAP, lsl #32
    // 0xb73928: cmp             w1, NULL
    // 0xb7392c: b.eq            #0xb75270
    // 0xb73930: LoadField: r2 = r1->field_23
    //     0xb73930: ldur            w2, [x1, #0x23]
    // 0xb73934: DecompressPointer r2
    //     0xb73934: add             x2, x2, HEAP, lsl #32
    // 0xb73938: cmp             w2, NULL
    // 0xb7393c: b.ne            #0xb73948
    // 0xb73940: r1 = Null
    //     0xb73940: mov             x1, NULL
    // 0xb73944: b               #0xb7396c
    // 0xb73948: LoadField: r1 = r2->field_3f
    //     0xb73948: ldur            w1, [x2, #0x3f]
    // 0xb7394c: DecompressPointer r1
    //     0xb7394c: add             x1, x1, HEAP, lsl #32
    // 0xb73950: cmp             w1, NULL
    // 0xb73954: b.ne            #0xb73960
    // 0xb73958: r1 = Null
    //     0xb73958: mov             x1, NULL
    // 0xb7395c: b               #0xb7396c
    // 0xb73960: LoadField: r2 = r1->field_13
    //     0xb73960: ldur            w2, [x1, #0x13]
    // 0xb73964: DecompressPointer r2
    //     0xb73964: add             x2, x2, HEAP, lsl #32
    // 0xb73968: mov             x1, x2
    // 0xb7396c: cmp             w1, NULL
    // 0xb73970: b.ne            #0xb73978
    // 0xb73974: r1 = ""
    //     0xb73974: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73978: mov             x6, x1
    // 0xb7397c: b               #0xb73a70
    // 0xb73980: ldur            x0, [fp, #-8]
    // 0xb73984: LoadField: r1 = r0->field_b
    //     0xb73984: ldur            w1, [x0, #0xb]
    // 0xb73988: DecompressPointer r1
    //     0xb73988: add             x1, x1, HEAP, lsl #32
    // 0xb7398c: cmp             w1, NULL
    // 0xb73990: b.eq            #0xb75274
    // 0xb73994: LoadField: r2 = r1->field_f
    //     0xb73994: ldur            w2, [x1, #0xf]
    // 0xb73998: DecompressPointer r2
    //     0xb73998: add             x2, x2, HEAP, lsl #32
    // 0xb7399c: r16 = "cancel_order"
    //     0xb7399c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb739a0: ldr             x16, [x16, #0x98]
    // 0xb739a4: stp             x16, x2, [SP]
    // 0xb739a8: r0 = ==()
    //     0xb739a8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb739ac: tbnz            w0, #4, #0xb73a10
    // 0xb739b0: ldur            x0, [fp, #-8]
    // 0xb739b4: LoadField: r1 = r0->field_b
    //     0xb739b4: ldur            w1, [x0, #0xb]
    // 0xb739b8: DecompressPointer r1
    //     0xb739b8: add             x1, x1, HEAP, lsl #32
    // 0xb739bc: cmp             w1, NULL
    // 0xb739c0: b.eq            #0xb75278
    // 0xb739c4: LoadField: r2 = r1->field_1b
    //     0xb739c4: ldur            w2, [x1, #0x1b]
    // 0xb739c8: DecompressPointer r2
    //     0xb739c8: add             x2, x2, HEAP, lsl #32
    // 0xb739cc: cmp             w2, NULL
    // 0xb739d0: b.ne            #0xb739dc
    // 0xb739d4: r1 = Null
    //     0xb739d4: mov             x1, NULL
    // 0xb739d8: b               #0xb73a00
    // 0xb739dc: LoadField: r1 = r2->field_7f
    //     0xb739dc: ldur            w1, [x2, #0x7f]
    // 0xb739e0: DecompressPointer r1
    //     0xb739e0: add             x1, x1, HEAP, lsl #32
    // 0xb739e4: cmp             w1, NULL
    // 0xb739e8: b.ne            #0xb739f4
    // 0xb739ec: r1 = Null
    //     0xb739ec: mov             x1, NULL
    // 0xb739f0: b               #0xb73a00
    // 0xb739f4: LoadField: r2 = r1->field_13
    //     0xb739f4: ldur            w2, [x1, #0x13]
    // 0xb739f8: DecompressPointer r2
    //     0xb739f8: add             x2, x2, HEAP, lsl #32
    // 0xb739fc: mov             x1, x2
    // 0xb73a00: cmp             w1, NULL
    // 0xb73a04: b.ne            #0xb73a6c
    // 0xb73a08: r1 = ""
    //     0xb73a08: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73a0c: b               #0xb73a6c
    // 0xb73a10: ldur            x0, [fp, #-8]
    // 0xb73a14: LoadField: r1 = r0->field_b
    //     0xb73a14: ldur            w1, [x0, #0xb]
    // 0xb73a18: DecompressPointer r1
    //     0xb73a18: add             x1, x1, HEAP, lsl #32
    // 0xb73a1c: cmp             w1, NULL
    // 0xb73a20: b.eq            #0xb7527c
    // 0xb73a24: LoadField: r2 = r1->field_1f
    //     0xb73a24: ldur            w2, [x1, #0x1f]
    // 0xb73a28: DecompressPointer r2
    //     0xb73a28: add             x2, x2, HEAP, lsl #32
    // 0xb73a2c: cmp             w2, NULL
    // 0xb73a30: b.ne            #0xb73a3c
    // 0xb73a34: r1 = Null
    //     0xb73a34: mov             x1, NULL
    // 0xb73a38: b               #0xb73a60
    // 0xb73a3c: LoadField: r1 = r2->field_d7
    //     0xb73a3c: ldur            w1, [x2, #0xd7]
    // 0xb73a40: DecompressPointer r1
    //     0xb73a40: add             x1, x1, HEAP, lsl #32
    // 0xb73a44: cmp             w1, NULL
    // 0xb73a48: b.ne            #0xb73a54
    // 0xb73a4c: r1 = Null
    //     0xb73a4c: mov             x1, NULL
    // 0xb73a50: b               #0xb73a60
    // 0xb73a54: LoadField: r2 = r1->field_13
    //     0xb73a54: ldur            w2, [x1, #0x13]
    // 0xb73a58: DecompressPointer r2
    //     0xb73a58: add             x2, x2, HEAP, lsl #32
    // 0xb73a5c: mov             x1, x2
    // 0xb73a60: cmp             w1, NULL
    // 0xb73a64: b.ne            #0xb73a6c
    // 0xb73a68: r1 = ""
    //     0xb73a68: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73a6c: mov             x6, x1
    // 0xb73a70: ldur            x5, [fp, #-0x28]
    // 0xb73a74: ldur            x4, [fp, #-0x48]
    // 0xb73a78: ldur            x3, [fp, #-0x38]
    // 0xb73a7c: ldur            x2, [fp, #-0x58]
    // 0xb73a80: ldur            x1, [fp, #-0x10]
    // 0xb73a84: stur            x6, [fp, #-0x50]
    // 0xb73a88: r0 = of()
    //     0xb73a88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb73a8c: LoadField: r1 = r0->field_87
    //     0xb73a8c: ldur            w1, [x0, #0x87]
    // 0xb73a90: DecompressPointer r1
    //     0xb73a90: add             x1, x1, HEAP, lsl #32
    // 0xb73a94: LoadField: r0 = r1->field_2b
    //     0xb73a94: ldur            w0, [x1, #0x2b]
    // 0xb73a98: DecompressPointer r0
    //     0xb73a98: add             x0, x0, HEAP, lsl #32
    // 0xb73a9c: r16 = 12.000000
    //     0xb73a9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb73aa0: ldr             x16, [x16, #0x9e8]
    // 0xb73aa4: r30 = Instance_TextDecoration
    //     0xb73aa4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb73aa8: ldr             lr, [lr, #0xe30]
    // 0xb73aac: stp             lr, x16, [SP]
    // 0xb73ab0: mov             x1, x0
    // 0xb73ab4: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb73ab4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb73ab8: ldr             x4, [x4, #0x698]
    // 0xb73abc: r0 = copyWith()
    //     0xb73abc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb73ac0: stur            x0, [fp, #-0x60]
    // 0xb73ac4: r0 = Text()
    //     0xb73ac4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb73ac8: mov             x3, x0
    // 0xb73acc: ldur            x0, [fp, #-0x50]
    // 0xb73ad0: stur            x3, [fp, #-0x68]
    // 0xb73ad4: StoreField: r3->field_b = r0
    //     0xb73ad4: stur            w0, [x3, #0xb]
    // 0xb73ad8: ldur            x0, [fp, #-0x60]
    // 0xb73adc: StoreField: r3->field_13 = r0
    //     0xb73adc: stur            w0, [x3, #0x13]
    // 0xb73ae0: r1 = Null
    //     0xb73ae0: mov             x1, NULL
    // 0xb73ae4: r2 = 6
    //     0xb73ae4: movz            x2, #0x6
    // 0xb73ae8: r0 = AllocateArray()
    //     0xb73ae8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb73aec: mov             x2, x0
    // 0xb73af0: ldur            x0, [fp, #-0x58]
    // 0xb73af4: stur            x2, [fp, #-0x50]
    // 0xb73af8: StoreField: r2->field_f = r0
    //     0xb73af8: stur            w0, [x2, #0xf]
    // 0xb73afc: r16 = Instance_SizedBox
    //     0xb73afc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb73b00: ldr             x16, [x16, #0xa50]
    // 0xb73b04: StoreField: r2->field_13 = r16
    //     0xb73b04: stur            w16, [x2, #0x13]
    // 0xb73b08: ldur            x0, [fp, #-0x68]
    // 0xb73b0c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb73b0c: stur            w0, [x2, #0x17]
    // 0xb73b10: r1 = <Widget>
    //     0xb73b10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb73b14: r0 = AllocateGrowableArray()
    //     0xb73b14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb73b18: mov             x1, x0
    // 0xb73b1c: ldur            x0, [fp, #-0x50]
    // 0xb73b20: stur            x1, [fp, #-0x58]
    // 0xb73b24: StoreField: r1->field_f = r0
    //     0xb73b24: stur            w0, [x1, #0xf]
    // 0xb73b28: r2 = 6
    //     0xb73b28: movz            x2, #0x6
    // 0xb73b2c: StoreField: r1->field_b = r2
    //     0xb73b2c: stur            w2, [x1, #0xb]
    // 0xb73b30: r0 = Row()
    //     0xb73b30: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb73b34: mov             x3, x0
    // 0xb73b38: r0 = Instance_Axis
    //     0xb73b38: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb73b3c: stur            x3, [fp, #-0x50]
    // 0xb73b40: StoreField: r3->field_f = r0
    //     0xb73b40: stur            w0, [x3, #0xf]
    // 0xb73b44: r4 = Instance_MainAxisAlignment
    //     0xb73b44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb73b48: ldr             x4, [x4, #0xa08]
    // 0xb73b4c: StoreField: r3->field_13 = r4
    //     0xb73b4c: stur            w4, [x3, #0x13]
    // 0xb73b50: r5 = Instance_MainAxisSize
    //     0xb73b50: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb73b54: ldr             x5, [x5, #0xa10]
    // 0xb73b58: ArrayStore: r3[0] = r5  ; List_4
    //     0xb73b58: stur            w5, [x3, #0x17]
    // 0xb73b5c: r6 = Instance_CrossAxisAlignment
    //     0xb73b5c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb73b60: ldr             x6, [x6, #0xa18]
    // 0xb73b64: StoreField: r3->field_1b = r6
    //     0xb73b64: stur            w6, [x3, #0x1b]
    // 0xb73b68: r7 = Instance_VerticalDirection
    //     0xb73b68: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb73b6c: ldr             x7, [x7, #0xa20]
    // 0xb73b70: StoreField: r3->field_23 = r7
    //     0xb73b70: stur            w7, [x3, #0x23]
    // 0xb73b74: r8 = Instance_Clip
    //     0xb73b74: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb73b78: ldr             x8, [x8, #0x38]
    // 0xb73b7c: StoreField: r3->field_2b = r8
    //     0xb73b7c: stur            w8, [x3, #0x2b]
    // 0xb73b80: StoreField: r3->field_2f = rZR
    //     0xb73b80: stur            xzr, [x3, #0x2f]
    // 0xb73b84: ldur            x1, [fp, #-0x58]
    // 0xb73b88: StoreField: r3->field_b = r1
    //     0xb73b88: stur            w1, [x3, #0xb]
    // 0xb73b8c: r1 = Null
    //     0xb73b8c: mov             x1, NULL
    // 0xb73b90: r2 = 6
    //     0xb73b90: movz            x2, #0x6
    // 0xb73b94: r0 = AllocateArray()
    //     0xb73b94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb73b98: mov             x2, x0
    // 0xb73b9c: ldur            x0, [fp, #-0x38]
    // 0xb73ba0: stur            x2, [fp, #-0x58]
    // 0xb73ba4: StoreField: r2->field_f = r0
    //     0xb73ba4: stur            w0, [x2, #0xf]
    // 0xb73ba8: r16 = Instance_SizedBox
    //     0xb73ba8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb73bac: ldr             x16, [x16, #0xc70]
    // 0xb73bb0: StoreField: r2->field_13 = r16
    //     0xb73bb0: stur            w16, [x2, #0x13]
    // 0xb73bb4: ldur            x0, [fp, #-0x50]
    // 0xb73bb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb73bb8: stur            w0, [x2, #0x17]
    // 0xb73bbc: r1 = <Widget>
    //     0xb73bbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb73bc0: r0 = AllocateGrowableArray()
    //     0xb73bc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb73bc4: mov             x1, x0
    // 0xb73bc8: ldur            x0, [fp, #-0x58]
    // 0xb73bcc: stur            x1, [fp, #-0x38]
    // 0xb73bd0: StoreField: r1->field_f = r0
    //     0xb73bd0: stur            w0, [x1, #0xf]
    // 0xb73bd4: r2 = 6
    //     0xb73bd4: movz            x2, #0x6
    // 0xb73bd8: StoreField: r1->field_b = r2
    //     0xb73bd8: stur            w2, [x1, #0xb]
    // 0xb73bdc: r0 = Column()
    //     0xb73bdc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb73be0: mov             x1, x0
    // 0xb73be4: r0 = Instance_Axis
    //     0xb73be4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb73be8: stur            x1, [fp, #-0x50]
    // 0xb73bec: StoreField: r1->field_f = r0
    //     0xb73bec: stur            w0, [x1, #0xf]
    // 0xb73bf0: r2 = Instance_MainAxisAlignment
    //     0xb73bf0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb73bf4: ldr             x2, [x2, #0xa08]
    // 0xb73bf8: StoreField: r1->field_13 = r2
    //     0xb73bf8: stur            w2, [x1, #0x13]
    // 0xb73bfc: r3 = Instance_MainAxisSize
    //     0xb73bfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb73c00: ldr             x3, [x3, #0xa10]
    // 0xb73c04: ArrayStore: r1[0] = r3  ; List_4
    //     0xb73c04: stur            w3, [x1, #0x17]
    // 0xb73c08: r4 = Instance_CrossAxisAlignment
    //     0xb73c08: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb73c0c: ldr             x4, [x4, #0x890]
    // 0xb73c10: StoreField: r1->field_1b = r4
    //     0xb73c10: stur            w4, [x1, #0x1b]
    // 0xb73c14: r5 = Instance_VerticalDirection
    //     0xb73c14: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb73c18: ldr             x5, [x5, #0xa20]
    // 0xb73c1c: StoreField: r1->field_23 = r5
    //     0xb73c1c: stur            w5, [x1, #0x23]
    // 0xb73c20: r6 = Instance_Clip
    //     0xb73c20: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb73c24: ldr             x6, [x6, #0x38]
    // 0xb73c28: StoreField: r1->field_2b = r6
    //     0xb73c28: stur            w6, [x1, #0x2b]
    // 0xb73c2c: StoreField: r1->field_2f = rZR
    //     0xb73c2c: stur            xzr, [x1, #0x2f]
    // 0xb73c30: ldur            x7, [fp, #-0x38]
    // 0xb73c34: StoreField: r1->field_b = r7
    //     0xb73c34: stur            w7, [x1, #0xb]
    // 0xb73c38: r0 = Padding()
    //     0xb73c38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb73c3c: mov             x2, x0
    // 0xb73c40: r0 = Instance_EdgeInsets
    //     0xb73c40: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb73c44: ldr             x0, [x0, #0xa78]
    // 0xb73c48: stur            x2, [fp, #-0x38]
    // 0xb73c4c: StoreField: r2->field_f = r0
    //     0xb73c4c: stur            w0, [x2, #0xf]
    // 0xb73c50: ldur            x0, [fp, #-0x50]
    // 0xb73c54: StoreField: r2->field_b = r0
    //     0xb73c54: stur            w0, [x2, #0xb]
    // 0xb73c58: r1 = <FlexParentData>
    //     0xb73c58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb73c5c: ldr             x1, [x1, #0xe00]
    // 0xb73c60: r0 = Expanded()
    //     0xb73c60: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb73c64: mov             x3, x0
    // 0xb73c68: r0 = 1
    //     0xb73c68: movz            x0, #0x1
    // 0xb73c6c: stur            x3, [fp, #-0x50]
    // 0xb73c70: StoreField: r3->field_13 = r0
    //     0xb73c70: stur            x0, [x3, #0x13]
    // 0xb73c74: r4 = Instance_FlexFit
    //     0xb73c74: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb73c78: ldr             x4, [x4, #0xe08]
    // 0xb73c7c: StoreField: r3->field_1b = r4
    //     0xb73c7c: stur            w4, [x3, #0x1b]
    // 0xb73c80: ldur            x1, [fp, #-0x38]
    // 0xb73c84: StoreField: r3->field_b = r1
    //     0xb73c84: stur            w1, [x3, #0xb]
    // 0xb73c88: r1 = Null
    //     0xb73c88: mov             x1, NULL
    // 0xb73c8c: r2 = 6
    //     0xb73c8c: movz            x2, #0x6
    // 0xb73c90: r0 = AllocateArray()
    //     0xb73c90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb73c94: mov             x2, x0
    // 0xb73c98: ldur            x0, [fp, #-0x28]
    // 0xb73c9c: stur            x2, [fp, #-0x38]
    // 0xb73ca0: StoreField: r2->field_f = r0
    //     0xb73ca0: stur            w0, [x2, #0xf]
    // 0xb73ca4: ldur            x0, [fp, #-0x48]
    // 0xb73ca8: StoreField: r2->field_13 = r0
    //     0xb73ca8: stur            w0, [x2, #0x13]
    // 0xb73cac: ldur            x0, [fp, #-0x50]
    // 0xb73cb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb73cb0: stur            w0, [x2, #0x17]
    // 0xb73cb4: r1 = <Widget>
    //     0xb73cb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb73cb8: r0 = AllocateGrowableArray()
    //     0xb73cb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb73cbc: mov             x1, x0
    // 0xb73cc0: ldur            x0, [fp, #-0x38]
    // 0xb73cc4: stur            x1, [fp, #-0x28]
    // 0xb73cc8: StoreField: r1->field_f = r0
    //     0xb73cc8: stur            w0, [x1, #0xf]
    // 0xb73ccc: r2 = 6
    //     0xb73ccc: movz            x2, #0x6
    // 0xb73cd0: StoreField: r1->field_b = r2
    //     0xb73cd0: stur            w2, [x1, #0xb]
    // 0xb73cd4: r0 = Row()
    //     0xb73cd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb73cd8: mov             x1, x0
    // 0xb73cdc: r0 = Instance_Axis
    //     0xb73cdc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb73ce0: stur            x1, [fp, #-0x38]
    // 0xb73ce4: StoreField: r1->field_f = r0
    //     0xb73ce4: stur            w0, [x1, #0xf]
    // 0xb73ce8: r2 = Instance_MainAxisAlignment
    //     0xb73ce8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb73cec: ldr             x2, [x2, #0xa08]
    // 0xb73cf0: StoreField: r1->field_13 = r2
    //     0xb73cf0: stur            w2, [x1, #0x13]
    // 0xb73cf4: r3 = Instance_MainAxisSize
    //     0xb73cf4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb73cf8: ldr             x3, [x3, #0xa10]
    // 0xb73cfc: ArrayStore: r1[0] = r3  ; List_4
    //     0xb73cfc: stur            w3, [x1, #0x17]
    // 0xb73d00: r4 = Instance_CrossAxisAlignment
    //     0xb73d00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb73d04: ldr             x4, [x4, #0xa18]
    // 0xb73d08: StoreField: r1->field_1b = r4
    //     0xb73d08: stur            w4, [x1, #0x1b]
    // 0xb73d0c: r5 = Instance_VerticalDirection
    //     0xb73d0c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb73d10: ldr             x5, [x5, #0xa20]
    // 0xb73d14: StoreField: r1->field_23 = r5
    //     0xb73d14: stur            w5, [x1, #0x23]
    // 0xb73d18: r6 = Instance_Clip
    //     0xb73d18: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb73d1c: ldr             x6, [x6, #0x38]
    // 0xb73d20: StoreField: r1->field_2b = r6
    //     0xb73d20: stur            w6, [x1, #0x2b]
    // 0xb73d24: StoreField: r1->field_2f = rZR
    //     0xb73d24: stur            xzr, [x1, #0x2f]
    // 0xb73d28: ldur            x7, [fp, #-0x28]
    // 0xb73d2c: StoreField: r1->field_b = r7
    //     0xb73d2c: stur            w7, [x1, #0xb]
    // 0xb73d30: r0 = Container()
    //     0xb73d30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb73d34: stur            x0, [fp, #-0x28]
    // 0xb73d38: ldur            x16, [fp, #-0x40]
    // 0xb73d3c: ldur            lr, [fp, #-0x38]
    // 0xb73d40: stp             lr, x16, [SP]
    // 0xb73d44: mov             x1, x0
    // 0xb73d48: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb73d48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb73d4c: ldr             x4, [x4, #0x88]
    // 0xb73d50: r0 = Container()
    //     0xb73d50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb73d54: r0 = Padding()
    //     0xb73d54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb73d58: mov             x1, x0
    // 0xb73d5c: r0 = Instance_EdgeInsets
    //     0xb73d5c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xb73d60: ldr             x0, [x0, #0xb00]
    // 0xb73d64: stur            x1, [fp, #-0x38]
    // 0xb73d68: StoreField: r1->field_f = r0
    //     0xb73d68: stur            w0, [x1, #0xf]
    // 0xb73d6c: ldur            x0, [fp, #-0x28]
    // 0xb73d70: StoreField: r1->field_b = r0
    //     0xb73d70: stur            w0, [x1, #0xb]
    // 0xb73d74: r0 = Radius()
    //     0xb73d74: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb73d78: d0 = 12.000000
    //     0xb73d78: fmov            d0, #12.00000000
    // 0xb73d7c: stur            x0, [fp, #-0x28]
    // 0xb73d80: StoreField: r0->field_7 = d0
    //     0xb73d80: stur            d0, [x0, #7]
    // 0xb73d84: StoreField: r0->field_f = d0
    //     0xb73d84: stur            d0, [x0, #0xf]
    // 0xb73d88: r0 = BorderRadius()
    //     0xb73d88: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb73d8c: mov             x2, x0
    // 0xb73d90: ldur            x0, [fp, #-0x28]
    // 0xb73d94: stur            x2, [fp, #-0x40]
    // 0xb73d98: StoreField: r2->field_7 = r0
    //     0xb73d98: stur            w0, [x2, #7]
    // 0xb73d9c: StoreField: r2->field_b = r0
    //     0xb73d9c: stur            w0, [x2, #0xb]
    // 0xb73da0: StoreField: r2->field_f = r0
    //     0xb73da0: stur            w0, [x2, #0xf]
    // 0xb73da4: StoreField: r2->field_13 = r0
    //     0xb73da4: stur            w0, [x2, #0x13]
    // 0xb73da8: ldur            x1, [fp, #-0x10]
    // 0xb73dac: r0 = of()
    //     0xb73dac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb73db0: LoadField: r1 = r0->field_5b
    //     0xb73db0: ldur            w1, [x0, #0x5b]
    // 0xb73db4: DecompressPointer r1
    //     0xb73db4: add             x1, x1, HEAP, lsl #32
    // 0xb73db8: r0 = LoadClassIdInstr(r1)
    //     0xb73db8: ldur            x0, [x1, #-1]
    //     0xb73dbc: ubfx            x0, x0, #0xc, #0x14
    // 0xb73dc0: d0 = 0.100000
    //     0xb73dc0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb73dc4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb73dc4: sub             lr, x0, #0xffa
    //     0xb73dc8: ldr             lr, [x21, lr, lsl #3]
    //     0xb73dcc: blr             lr
    // 0xb73dd0: mov             x2, x0
    // 0xb73dd4: r1 = Null
    //     0xb73dd4: mov             x1, NULL
    // 0xb73dd8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb73dd8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb73ddc: r0 = Border.all()
    //     0xb73ddc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb73de0: stur            x0, [fp, #-0x28]
    // 0xb73de4: r0 = BoxDecoration()
    //     0xb73de4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb73de8: mov             x1, x0
    // 0xb73dec: ldur            x0, [fp, #-0x28]
    // 0xb73df0: stur            x1, [fp, #-0x48]
    // 0xb73df4: StoreField: r1->field_f = r0
    //     0xb73df4: stur            w0, [x1, #0xf]
    // 0xb73df8: ldur            x0, [fp, #-0x40]
    // 0xb73dfc: StoreField: r1->field_13 = r0
    //     0xb73dfc: stur            w0, [x1, #0x13]
    // 0xb73e00: r0 = Instance_BoxShape
    //     0xb73e00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb73e04: ldr             x0, [x0, #0x80]
    // 0xb73e08: StoreField: r1->field_23 = r0
    //     0xb73e08: stur            w0, [x1, #0x23]
    // 0xb73e0c: ldur            x0, [fp, #-8]
    // 0xb73e10: LoadField: r2 = r0->field_b
    //     0xb73e10: ldur            w2, [x0, #0xb]
    // 0xb73e14: DecompressPointer r2
    //     0xb73e14: add             x2, x2, HEAP, lsl #32
    // 0xb73e18: cmp             w2, NULL
    // 0xb73e1c: b.eq            #0xb75280
    // 0xb73e20: LoadField: r3 = r2->field_f
    //     0xb73e20: ldur            w3, [x2, #0xf]
    // 0xb73e24: DecompressPointer r3
    //     0xb73e24: add             x3, x3, HEAP, lsl #32
    // 0xb73e28: r16 = "return_order_intermediate"
    //     0xb73e28: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb73e2c: ldr             x16, [x16, #0xb00]
    // 0xb73e30: stp             x16, x3, [SP]
    // 0xb73e34: r0 = ==()
    //     0xb73e34: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73e38: tbnz            w0, #4, #0xb73ea0
    // 0xb73e3c: ldur            x0, [fp, #-8]
    // 0xb73e40: LoadField: r1 = r0->field_b
    //     0xb73e40: ldur            w1, [x0, #0xb]
    // 0xb73e44: DecompressPointer r1
    //     0xb73e44: add             x1, x1, HEAP, lsl #32
    // 0xb73e48: cmp             w1, NULL
    // 0xb73e4c: b.eq            #0xb75284
    // 0xb73e50: LoadField: r2 = r1->field_23
    //     0xb73e50: ldur            w2, [x1, #0x23]
    // 0xb73e54: DecompressPointer r2
    //     0xb73e54: add             x2, x2, HEAP, lsl #32
    // 0xb73e58: cmp             w2, NULL
    // 0xb73e5c: b.ne            #0xb73e68
    // 0xb73e60: r1 = Null
    //     0xb73e60: mov             x1, NULL
    // 0xb73e64: b               #0xb73e8c
    // 0xb73e68: LoadField: r1 = r2->field_37
    //     0xb73e68: ldur            w1, [x2, #0x37]
    // 0xb73e6c: DecompressPointer r1
    //     0xb73e6c: add             x1, x1, HEAP, lsl #32
    // 0xb73e70: cmp             w1, NULL
    // 0xb73e74: b.ne            #0xb73e80
    // 0xb73e78: r1 = Null
    //     0xb73e78: mov             x1, NULL
    // 0xb73e7c: b               #0xb73e8c
    // 0xb73e80: LoadField: r2 = r1->field_7
    //     0xb73e80: ldur            w2, [x1, #7]
    // 0xb73e84: DecompressPointer r2
    //     0xb73e84: add             x2, x2, HEAP, lsl #32
    // 0xb73e88: mov             x1, x2
    // 0xb73e8c: cmp             w1, NULL
    // 0xb73e90: b.ne            #0xb73e98
    // 0xb73e94: r1 = ""
    //     0xb73e94: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73e98: mov             x3, x1
    // 0xb73e9c: b               #0xb73f58
    // 0xb73ea0: ldur            x0, [fp, #-8]
    // 0xb73ea4: LoadField: r1 = r0->field_b
    //     0xb73ea4: ldur            w1, [x0, #0xb]
    // 0xb73ea8: DecompressPointer r1
    //     0xb73ea8: add             x1, x1, HEAP, lsl #32
    // 0xb73eac: cmp             w1, NULL
    // 0xb73eb0: b.eq            #0xb75288
    // 0xb73eb4: LoadField: r2 = r1->field_f
    //     0xb73eb4: ldur            w2, [x1, #0xf]
    // 0xb73eb8: DecompressPointer r2
    //     0xb73eb8: add             x2, x2, HEAP, lsl #32
    // 0xb73ebc: r16 = "cancel_order"
    //     0xb73ebc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb73ec0: ldr             x16, [x16, #0x98]
    // 0xb73ec4: stp             x16, x2, [SP]
    // 0xb73ec8: r0 = ==()
    //     0xb73ec8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73ecc: tbnz            w0, #4, #0xb73f14
    // 0xb73ed0: ldur            x0, [fp, #-8]
    // 0xb73ed4: LoadField: r1 = r0->field_b
    //     0xb73ed4: ldur            w1, [x0, #0xb]
    // 0xb73ed8: DecompressPointer r1
    //     0xb73ed8: add             x1, x1, HEAP, lsl #32
    // 0xb73edc: cmp             w1, NULL
    // 0xb73ee0: b.eq            #0xb7528c
    // 0xb73ee4: LoadField: r2 = r1->field_1b
    //     0xb73ee4: ldur            w2, [x1, #0x1b]
    // 0xb73ee8: DecompressPointer r2
    //     0xb73ee8: add             x2, x2, HEAP, lsl #32
    // 0xb73eec: cmp             w2, NULL
    // 0xb73ef0: b.ne            #0xb73efc
    // 0xb73ef4: r1 = Null
    //     0xb73ef4: mov             x1, NULL
    // 0xb73ef8: b               #0xb73f04
    // 0xb73efc: LoadField: r1 = r2->field_b
    //     0xb73efc: ldur            w1, [x2, #0xb]
    // 0xb73f00: DecompressPointer r1
    //     0xb73f00: add             x1, x1, HEAP, lsl #32
    // 0xb73f04: cmp             w1, NULL
    // 0xb73f08: b.ne            #0xb73f54
    // 0xb73f0c: r1 = ""
    //     0xb73f0c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73f10: b               #0xb73f54
    // 0xb73f14: ldur            x0, [fp, #-8]
    // 0xb73f18: LoadField: r1 = r0->field_b
    //     0xb73f18: ldur            w1, [x0, #0xb]
    // 0xb73f1c: DecompressPointer r1
    //     0xb73f1c: add             x1, x1, HEAP, lsl #32
    // 0xb73f20: cmp             w1, NULL
    // 0xb73f24: b.eq            #0xb75290
    // 0xb73f28: LoadField: r2 = r1->field_1f
    //     0xb73f28: ldur            w2, [x1, #0x1f]
    // 0xb73f2c: DecompressPointer r2
    //     0xb73f2c: add             x2, x2, HEAP, lsl #32
    // 0xb73f30: cmp             w2, NULL
    // 0xb73f34: b.ne            #0xb73f40
    // 0xb73f38: r1 = Null
    //     0xb73f38: mov             x1, NULL
    // 0xb73f3c: b               #0xb73f48
    // 0xb73f40: LoadField: r1 = r2->field_1b
    //     0xb73f40: ldur            w1, [x2, #0x1b]
    // 0xb73f44: DecompressPointer r1
    //     0xb73f44: add             x1, x1, HEAP, lsl #32
    // 0xb73f48: cmp             w1, NULL
    // 0xb73f4c: b.ne            #0xb73f54
    // 0xb73f50: r1 = ""
    //     0xb73f50: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb73f54: mov             x3, x1
    // 0xb73f58: stur            x3, [fp, #-0x28]
    // 0xb73f5c: r1 = Function '<anonymous closure>':.
    //     0xb73f5c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f80] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb73f60: ldr             x1, [x1, #0xf80]
    // 0xb73f64: r2 = Null
    //     0xb73f64: mov             x2, NULL
    // 0xb73f68: r0 = AllocateClosure()
    //     0xb73f68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb73f6c: r1 = Function '<anonymous closure>':.
    //     0xb73f6c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f88] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb73f70: ldr             x1, [x1, #0xf88]
    // 0xb73f74: r2 = Null
    //     0xb73f74: mov             x2, NULL
    // 0xb73f78: stur            x0, [fp, #-0x40]
    // 0xb73f7c: r0 = AllocateClosure()
    //     0xb73f7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb73f80: stur            x0, [fp, #-0x50]
    // 0xb73f84: r0 = CachedNetworkImage()
    //     0xb73f84: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb73f88: stur            x0, [fp, #-0x58]
    // 0xb73f8c: r16 = 56.000000
    //     0xb73f8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb73f90: ldr             x16, [x16, #0xb78]
    // 0xb73f94: r30 = 56.000000
    //     0xb73f94: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb73f98: ldr             lr, [lr, #0xb78]
    // 0xb73f9c: stp             lr, x16, [SP, #0x18]
    // 0xb73fa0: r16 = Instance_BoxFit
    //     0xb73fa0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb73fa4: ldr             x16, [x16, #0x118]
    // 0xb73fa8: ldur            lr, [fp, #-0x40]
    // 0xb73fac: stp             lr, x16, [SP, #8]
    // 0xb73fb0: ldur            x16, [fp, #-0x50]
    // 0xb73fb4: str             x16, [SP]
    // 0xb73fb8: mov             x1, x0
    // 0xb73fbc: ldur            x2, [fp, #-0x28]
    // 0xb73fc0: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb73fc0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb73fc4: ldr             x4, [x4, #0xc28]
    // 0xb73fc8: r0 = CachedNetworkImage()
    //     0xb73fc8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb73fcc: ldur            x0, [fp, #-8]
    // 0xb73fd0: LoadField: r1 = r0->field_b
    //     0xb73fd0: ldur            w1, [x0, #0xb]
    // 0xb73fd4: DecompressPointer r1
    //     0xb73fd4: add             x1, x1, HEAP, lsl #32
    // 0xb73fd8: cmp             w1, NULL
    // 0xb73fdc: b.eq            #0xb75294
    // 0xb73fe0: LoadField: r2 = r1->field_f
    //     0xb73fe0: ldur            w2, [x1, #0xf]
    // 0xb73fe4: DecompressPointer r2
    //     0xb73fe4: add             x2, x2, HEAP, lsl #32
    // 0xb73fe8: r16 = "return_order_intermediate"
    //     0xb73fe8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb73fec: ldr             x16, [x16, #0xb00]
    // 0xb73ff0: stp             x16, x2, [SP]
    // 0xb73ff4: r0 = ==()
    //     0xb73ff4: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb73ff8: tbnz            w0, #4, #0xb74060
    // 0xb73ffc: ldur            x0, [fp, #-8]
    // 0xb74000: LoadField: r1 = r0->field_b
    //     0xb74000: ldur            w1, [x0, #0xb]
    // 0xb74004: DecompressPointer r1
    //     0xb74004: add             x1, x1, HEAP, lsl #32
    // 0xb74008: cmp             w1, NULL
    // 0xb7400c: b.eq            #0xb75298
    // 0xb74010: LoadField: r2 = r1->field_23
    //     0xb74010: ldur            w2, [x1, #0x23]
    // 0xb74014: DecompressPointer r2
    //     0xb74014: add             x2, x2, HEAP, lsl #32
    // 0xb74018: cmp             w2, NULL
    // 0xb7401c: b.ne            #0xb74028
    // 0xb74020: r1 = Null
    //     0xb74020: mov             x1, NULL
    // 0xb74024: b               #0xb7404c
    // 0xb74028: LoadField: r1 = r2->field_37
    //     0xb74028: ldur            w1, [x2, #0x37]
    // 0xb7402c: DecompressPointer r1
    //     0xb7402c: add             x1, x1, HEAP, lsl #32
    // 0xb74030: cmp             w1, NULL
    // 0xb74034: b.ne            #0xb74040
    // 0xb74038: r1 = Null
    //     0xb74038: mov             x1, NULL
    // 0xb7403c: b               #0xb7404c
    // 0xb74040: LoadField: r2 = r1->field_b
    //     0xb74040: ldur            w2, [x1, #0xb]
    // 0xb74044: DecompressPointer r2
    //     0xb74044: add             x2, x2, HEAP, lsl #32
    // 0xb74048: mov             x1, x2
    // 0xb7404c: cmp             w1, NULL
    // 0xb74050: b.ne            #0xb74058
    // 0xb74054: r1 = ""
    //     0xb74054: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74058: mov             x2, x1
    // 0xb7405c: b               #0xb74118
    // 0xb74060: ldur            x0, [fp, #-8]
    // 0xb74064: LoadField: r1 = r0->field_b
    //     0xb74064: ldur            w1, [x0, #0xb]
    // 0xb74068: DecompressPointer r1
    //     0xb74068: add             x1, x1, HEAP, lsl #32
    // 0xb7406c: cmp             w1, NULL
    // 0xb74070: b.eq            #0xb7529c
    // 0xb74074: LoadField: r2 = r1->field_f
    //     0xb74074: ldur            w2, [x1, #0xf]
    // 0xb74078: DecompressPointer r2
    //     0xb74078: add             x2, x2, HEAP, lsl #32
    // 0xb7407c: r16 = "cancel_order"
    //     0xb7407c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb74080: ldr             x16, [x16, #0x98]
    // 0xb74084: stp             x16, x2, [SP]
    // 0xb74088: r0 = ==()
    //     0xb74088: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb7408c: tbnz            w0, #4, #0xb740d4
    // 0xb74090: ldur            x0, [fp, #-8]
    // 0xb74094: LoadField: r1 = r0->field_b
    //     0xb74094: ldur            w1, [x0, #0xb]
    // 0xb74098: DecompressPointer r1
    //     0xb74098: add             x1, x1, HEAP, lsl #32
    // 0xb7409c: cmp             w1, NULL
    // 0xb740a0: b.eq            #0xb752a0
    // 0xb740a4: LoadField: r2 = r1->field_1b
    //     0xb740a4: ldur            w2, [x1, #0x1b]
    // 0xb740a8: DecompressPointer r2
    //     0xb740a8: add             x2, x2, HEAP, lsl #32
    // 0xb740ac: cmp             w2, NULL
    // 0xb740b0: b.ne            #0xb740bc
    // 0xb740b4: r1 = Null
    //     0xb740b4: mov             x1, NULL
    // 0xb740b8: b               #0xb740c4
    // 0xb740bc: LoadField: r1 = r2->field_f
    //     0xb740bc: ldur            w1, [x2, #0xf]
    // 0xb740c0: DecompressPointer r1
    //     0xb740c0: add             x1, x1, HEAP, lsl #32
    // 0xb740c4: cmp             w1, NULL
    // 0xb740c8: b.ne            #0xb74114
    // 0xb740cc: r1 = ""
    //     0xb740cc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb740d0: b               #0xb74114
    // 0xb740d4: ldur            x0, [fp, #-8]
    // 0xb740d8: LoadField: r1 = r0->field_b
    //     0xb740d8: ldur            w1, [x0, #0xb]
    // 0xb740dc: DecompressPointer r1
    //     0xb740dc: add             x1, x1, HEAP, lsl #32
    // 0xb740e0: cmp             w1, NULL
    // 0xb740e4: b.eq            #0xb752a4
    // 0xb740e8: LoadField: r2 = r1->field_1f
    //     0xb740e8: ldur            w2, [x1, #0x1f]
    // 0xb740ec: DecompressPointer r2
    //     0xb740ec: add             x2, x2, HEAP, lsl #32
    // 0xb740f0: cmp             w2, NULL
    // 0xb740f4: b.ne            #0xb74100
    // 0xb740f8: r1 = Null
    //     0xb740f8: mov             x1, NULL
    // 0xb740fc: b               #0xb74108
    // 0xb74100: LoadField: r1 = r2->field_b
    //     0xb74100: ldur            w1, [x2, #0xb]
    // 0xb74104: DecompressPointer r1
    //     0xb74104: add             x1, x1, HEAP, lsl #32
    // 0xb74108: cmp             w1, NULL
    // 0xb7410c: b.ne            #0xb74114
    // 0xb74110: r1 = ""
    //     0xb74110: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74114: mov             x2, x1
    // 0xb74118: ldur            x1, [fp, #-0x10]
    // 0xb7411c: stur            x2, [fp, #-0x28]
    // 0xb74120: r0 = of()
    //     0xb74120: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74124: LoadField: r1 = r0->field_87
    //     0xb74124: ldur            w1, [x0, #0x87]
    // 0xb74128: DecompressPointer r1
    //     0xb74128: add             x1, x1, HEAP, lsl #32
    // 0xb7412c: LoadField: r0 = r1->field_7
    //     0xb7412c: ldur            w0, [x1, #7]
    // 0xb74130: DecompressPointer r0
    //     0xb74130: add             x0, x0, HEAP, lsl #32
    // 0xb74134: r16 = Instance_Color
    //     0xb74134: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb74138: r30 = 12.000000
    //     0xb74138: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7413c: ldr             lr, [lr, #0x9e8]
    // 0xb74140: stp             lr, x16, [SP]
    // 0xb74144: mov             x1, x0
    // 0xb74148: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb74148: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb7414c: ldr             x4, [x4, #0x9b8]
    // 0xb74150: r0 = copyWith()
    //     0xb74150: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb74154: stur            x0, [fp, #-0x40]
    // 0xb74158: r0 = Text()
    //     0xb74158: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7415c: mov             x1, x0
    // 0xb74160: ldur            x0, [fp, #-0x28]
    // 0xb74164: stur            x1, [fp, #-0x50]
    // 0xb74168: StoreField: r1->field_b = r0
    //     0xb74168: stur            w0, [x1, #0xb]
    // 0xb7416c: ldur            x0, [fp, #-0x40]
    // 0xb74170: StoreField: r1->field_13 = r0
    //     0xb74170: stur            w0, [x1, #0x13]
    // 0xb74174: r0 = Instance_TextOverflow
    //     0xb74174: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb74178: ldr             x0, [x0, #0xe10]
    // 0xb7417c: StoreField: r1->field_2b = r0
    //     0xb7417c: stur            w0, [x1, #0x2b]
    // 0xb74180: r0 = 4
    //     0xb74180: movz            x0, #0x4
    // 0xb74184: StoreField: r1->field_37 = r0
    //     0xb74184: stur            w0, [x1, #0x37]
    // 0xb74188: ldur            x0, [fp, #-8]
    // 0xb7418c: LoadField: r2 = r0->field_b
    //     0xb7418c: ldur            w2, [x0, #0xb]
    // 0xb74190: DecompressPointer r2
    //     0xb74190: add             x2, x2, HEAP, lsl #32
    // 0xb74194: cmp             w2, NULL
    // 0xb74198: b.eq            #0xb752a8
    // 0xb7419c: LoadField: r3 = r2->field_f
    //     0xb7419c: ldur            w3, [x2, #0xf]
    // 0xb741a0: DecompressPointer r3
    //     0xb741a0: add             x3, x3, HEAP, lsl #32
    // 0xb741a4: r16 = "return_order_intermediate"
    //     0xb741a4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb741a8: ldr             x16, [x16, #0xb00]
    // 0xb741ac: stp             x16, x3, [SP]
    // 0xb741b0: r0 = ==()
    //     0xb741b0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb741b4: tbnz            w0, #4, #0xb743cc
    // 0xb741b8: ldur            x1, [fp, #-8]
    // 0xb741bc: LoadField: r0 = r1->field_b
    //     0xb741bc: ldur            w0, [x1, #0xb]
    // 0xb741c0: DecompressPointer r0
    //     0xb741c0: add             x0, x0, HEAP, lsl #32
    // 0xb741c4: cmp             w0, NULL
    // 0xb741c8: b.eq            #0xb752ac
    // 0xb741cc: LoadField: r2 = r0->field_23
    //     0xb741cc: ldur            w2, [x0, #0x23]
    // 0xb741d0: DecompressPointer r2
    //     0xb741d0: add             x2, x2, HEAP, lsl #32
    // 0xb741d4: cmp             w2, NULL
    // 0xb741d8: b.ne            #0xb741e4
    // 0xb741dc: r0 = Null
    //     0xb741dc: mov             x0, NULL
    // 0xb741e0: b               #0xb74208
    // 0xb741e4: LoadField: r0 = r2->field_37
    //     0xb741e4: ldur            w0, [x2, #0x37]
    // 0xb741e8: DecompressPointer r0
    //     0xb741e8: add             x0, x0, HEAP, lsl #32
    // 0xb741ec: cmp             w0, NULL
    // 0xb741f0: b.ne            #0xb741fc
    // 0xb741f4: r0 = Null
    //     0xb741f4: mov             x0, NULL
    // 0xb741f8: b               #0xb74208
    // 0xb741fc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb741fc: ldur            w2, [x0, #0x17]
    // 0xb74200: DecompressPointer r2
    //     0xb74200: add             x2, x2, HEAP, lsl #32
    // 0xb74204: mov             x0, x2
    // 0xb74208: r2 = LoadClassIdInstr(r0)
    //     0xb74208: ldur            x2, [x0, #-1]
    //     0xb7420c: ubfx            x2, x2, #0xc, #0x14
    // 0xb74210: r16 = "size"
    //     0xb74210: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb74214: ldr             x16, [x16, #0x9c0]
    // 0xb74218: stp             x16, x0, [SP]
    // 0xb7421c: mov             x0, x2
    // 0xb74220: mov             lr, x0
    // 0xb74224: ldr             lr, [x21, lr, lsl #3]
    // 0xb74228: blr             lr
    // 0xb7422c: tbnz            w0, #4, #0xb742fc
    // 0xb74230: ldur            x0, [fp, #-8]
    // 0xb74234: r1 = Null
    //     0xb74234: mov             x1, NULL
    // 0xb74238: r2 = 8
    //     0xb74238: movz            x2, #0x8
    // 0xb7423c: r0 = AllocateArray()
    //     0xb7423c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74240: r16 = "Size: "
    //     0xb74240: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb74244: ldr             x16, [x16, #0xf00]
    // 0xb74248: StoreField: r0->field_f = r16
    //     0xb74248: stur            w16, [x0, #0xf]
    // 0xb7424c: ldur            x1, [fp, #-8]
    // 0xb74250: LoadField: r2 = r1->field_b
    //     0xb74250: ldur            w2, [x1, #0xb]
    // 0xb74254: DecompressPointer r2
    //     0xb74254: add             x2, x2, HEAP, lsl #32
    // 0xb74258: cmp             w2, NULL
    // 0xb7425c: b.eq            #0xb752b0
    // 0xb74260: LoadField: r3 = r2->field_23
    //     0xb74260: ldur            w3, [x2, #0x23]
    // 0xb74264: DecompressPointer r3
    //     0xb74264: add             x3, x3, HEAP, lsl #32
    // 0xb74268: cmp             w3, NULL
    // 0xb7426c: b.ne            #0xb74278
    // 0xb74270: r2 = Null
    //     0xb74270: mov             x2, NULL
    // 0xb74274: b               #0xb7429c
    // 0xb74278: LoadField: r2 = r3->field_37
    //     0xb74278: ldur            w2, [x3, #0x37]
    // 0xb7427c: DecompressPointer r2
    //     0xb7427c: add             x2, x2, HEAP, lsl #32
    // 0xb74280: cmp             w2, NULL
    // 0xb74284: b.ne            #0xb74290
    // 0xb74288: r2 = Null
    //     0xb74288: mov             x2, NULL
    // 0xb7428c: b               #0xb7429c
    // 0xb74290: LoadField: r4 = r2->field_f
    //     0xb74290: ldur            w4, [x2, #0xf]
    // 0xb74294: DecompressPointer r4
    //     0xb74294: add             x4, x4, HEAP, lsl #32
    // 0xb74298: mov             x2, x4
    // 0xb7429c: cmp             w2, NULL
    // 0xb742a0: b.ne            #0xb742a8
    // 0xb742a4: r2 = ""
    //     0xb742a4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb742a8: StoreField: r0->field_13 = r2
    //     0xb742a8: stur            w2, [x0, #0x13]
    // 0xb742ac: r16 = " / Qty: "
    //     0xb742ac: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb742b0: ldr             x16, [x16, #0x760]
    // 0xb742b4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb742b4: stur            w16, [x0, #0x17]
    // 0xb742b8: cmp             w3, NULL
    // 0xb742bc: b.ne            #0xb742c8
    // 0xb742c0: r2 = Null
    //     0xb742c0: mov             x2, NULL
    // 0xb742c4: b               #0xb742ec
    // 0xb742c8: LoadField: r2 = r3->field_37
    //     0xb742c8: ldur            w2, [x3, #0x37]
    // 0xb742cc: DecompressPointer r2
    //     0xb742cc: add             x2, x2, HEAP, lsl #32
    // 0xb742d0: cmp             w2, NULL
    // 0xb742d4: b.ne            #0xb742e0
    // 0xb742d8: r2 = Null
    //     0xb742d8: mov             x2, NULL
    // 0xb742dc: b               #0xb742ec
    // 0xb742e0: LoadField: r3 = r2->field_13
    //     0xb742e0: ldur            w3, [x2, #0x13]
    // 0xb742e4: DecompressPointer r3
    //     0xb742e4: add             x3, x3, HEAP, lsl #32
    // 0xb742e8: mov             x2, x3
    // 0xb742ec: StoreField: r0->field_1b = r2
    //     0xb742ec: stur            w2, [x0, #0x1b]
    // 0xb742f0: str             x0, [SP]
    // 0xb742f4: r0 = _interpolate()
    //     0xb742f4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb742f8: b               #0xb743c4
    // 0xb742fc: ldur            x0, [fp, #-8]
    // 0xb74300: r1 = Null
    //     0xb74300: mov             x1, NULL
    // 0xb74304: r2 = 8
    //     0xb74304: movz            x2, #0x8
    // 0xb74308: r0 = AllocateArray()
    //     0xb74308: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7430c: r16 = "Variant: "
    //     0xb7430c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb74310: ldr             x16, [x16, #0xf08]
    // 0xb74314: StoreField: r0->field_f = r16
    //     0xb74314: stur            w16, [x0, #0xf]
    // 0xb74318: ldur            x1, [fp, #-8]
    // 0xb7431c: LoadField: r2 = r1->field_b
    //     0xb7431c: ldur            w2, [x1, #0xb]
    // 0xb74320: DecompressPointer r2
    //     0xb74320: add             x2, x2, HEAP, lsl #32
    // 0xb74324: cmp             w2, NULL
    // 0xb74328: b.eq            #0xb752b4
    // 0xb7432c: LoadField: r3 = r2->field_23
    //     0xb7432c: ldur            w3, [x2, #0x23]
    // 0xb74330: DecompressPointer r3
    //     0xb74330: add             x3, x3, HEAP, lsl #32
    // 0xb74334: cmp             w3, NULL
    // 0xb74338: b.ne            #0xb74344
    // 0xb7433c: r2 = Null
    //     0xb7433c: mov             x2, NULL
    // 0xb74340: b               #0xb74368
    // 0xb74344: LoadField: r2 = r3->field_37
    //     0xb74344: ldur            w2, [x3, #0x37]
    // 0xb74348: DecompressPointer r2
    //     0xb74348: add             x2, x2, HEAP, lsl #32
    // 0xb7434c: cmp             w2, NULL
    // 0xb74350: b.ne            #0xb7435c
    // 0xb74354: r2 = Null
    //     0xb74354: mov             x2, NULL
    // 0xb74358: b               #0xb74368
    // 0xb7435c: LoadField: r4 = r2->field_f
    //     0xb7435c: ldur            w4, [x2, #0xf]
    // 0xb74360: DecompressPointer r4
    //     0xb74360: add             x4, x4, HEAP, lsl #32
    // 0xb74364: mov             x2, x4
    // 0xb74368: cmp             w2, NULL
    // 0xb7436c: b.ne            #0xb74374
    // 0xb74370: r2 = ""
    //     0xb74370: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74374: StoreField: r0->field_13 = r2
    //     0xb74374: stur            w2, [x0, #0x13]
    // 0xb74378: r16 = " / Qty: "
    //     0xb74378: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb7437c: ldr             x16, [x16, #0x760]
    // 0xb74380: ArrayStore: r0[0] = r16  ; List_4
    //     0xb74380: stur            w16, [x0, #0x17]
    // 0xb74384: cmp             w3, NULL
    // 0xb74388: b.ne            #0xb74394
    // 0xb7438c: r2 = Null
    //     0xb7438c: mov             x2, NULL
    // 0xb74390: b               #0xb743b8
    // 0xb74394: LoadField: r2 = r3->field_37
    //     0xb74394: ldur            w2, [x3, #0x37]
    // 0xb74398: DecompressPointer r2
    //     0xb74398: add             x2, x2, HEAP, lsl #32
    // 0xb7439c: cmp             w2, NULL
    // 0xb743a0: b.ne            #0xb743ac
    // 0xb743a4: r2 = Null
    //     0xb743a4: mov             x2, NULL
    // 0xb743a8: b               #0xb743b8
    // 0xb743ac: LoadField: r3 = r2->field_13
    //     0xb743ac: ldur            w3, [x2, #0x13]
    // 0xb743b0: DecompressPointer r3
    //     0xb743b0: add             x3, x3, HEAP, lsl #32
    // 0xb743b4: mov             x2, x3
    // 0xb743b8: StoreField: r0->field_1b = r2
    //     0xb743b8: stur            w2, [x0, #0x1b]
    // 0xb743bc: str             x0, [SP]
    // 0xb743c0: r0 = _interpolate()
    //     0xb743c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb743c4: mov             x2, x0
    // 0xb743c8: b               #0xb74704
    // 0xb743cc: ldur            x0, [fp, #-8]
    // 0xb743d0: LoadField: r1 = r0->field_b
    //     0xb743d0: ldur            w1, [x0, #0xb]
    // 0xb743d4: DecompressPointer r1
    //     0xb743d4: add             x1, x1, HEAP, lsl #32
    // 0xb743d8: cmp             w1, NULL
    // 0xb743dc: b.eq            #0xb752b8
    // 0xb743e0: LoadField: r2 = r1->field_f
    //     0xb743e0: ldur            w2, [x1, #0xf]
    // 0xb743e4: DecompressPointer r2
    //     0xb743e4: add             x2, x2, HEAP, lsl #32
    // 0xb743e8: r16 = "cancel_order"
    //     0xb743e8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb743ec: ldr             x16, [x16, #0x98]
    // 0xb743f0: stp             x16, x2, [SP]
    // 0xb743f4: r0 = ==()
    //     0xb743f4: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb743f8: tbnz            w0, #4, #0xb74580
    // 0xb743fc: ldur            x1, [fp, #-8]
    // 0xb74400: LoadField: r0 = r1->field_b
    //     0xb74400: ldur            w0, [x1, #0xb]
    // 0xb74404: DecompressPointer r0
    //     0xb74404: add             x0, x0, HEAP, lsl #32
    // 0xb74408: cmp             w0, NULL
    // 0xb7440c: b.eq            #0xb752bc
    // 0xb74410: LoadField: r2 = r0->field_1b
    //     0xb74410: ldur            w2, [x0, #0x1b]
    // 0xb74414: DecompressPointer r2
    //     0xb74414: add             x2, x2, HEAP, lsl #32
    // 0xb74418: cmp             w2, NULL
    // 0xb7441c: b.ne            #0xb74428
    // 0xb74420: r0 = Null
    //     0xb74420: mov             x0, NULL
    // 0xb74424: b               #0xb74430
    // 0xb74428: LoadField: r0 = r2->field_47
    //     0xb74428: ldur            w0, [x2, #0x47]
    // 0xb7442c: DecompressPointer r0
    //     0xb7442c: add             x0, x0, HEAP, lsl #32
    // 0xb74430: r2 = LoadClassIdInstr(r0)
    //     0xb74430: ldur            x2, [x0, #-1]
    //     0xb74434: ubfx            x2, x2, #0xc, #0x14
    // 0xb74438: r16 = "size"
    //     0xb74438: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb7443c: ldr             x16, [x16, #0x9c0]
    // 0xb74440: stp             x16, x0, [SP]
    // 0xb74444: mov             x0, x2
    // 0xb74448: mov             lr, x0
    // 0xb7444c: ldr             lr, [x21, lr, lsl #3]
    // 0xb74450: blr             lr
    // 0xb74454: tbnz            w0, #4, #0xb744ec
    // 0xb74458: ldur            x0, [fp, #-8]
    // 0xb7445c: r1 = Null
    //     0xb7445c: mov             x1, NULL
    // 0xb74460: r2 = 8
    //     0xb74460: movz            x2, #0x8
    // 0xb74464: r0 = AllocateArray()
    //     0xb74464: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74468: r16 = "Size: "
    //     0xb74468: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb7446c: ldr             x16, [x16, #0xf00]
    // 0xb74470: StoreField: r0->field_f = r16
    //     0xb74470: stur            w16, [x0, #0xf]
    // 0xb74474: ldur            x1, [fp, #-8]
    // 0xb74478: LoadField: r2 = r1->field_b
    //     0xb74478: ldur            w2, [x1, #0xb]
    // 0xb7447c: DecompressPointer r2
    //     0xb7447c: add             x2, x2, HEAP, lsl #32
    // 0xb74480: cmp             w2, NULL
    // 0xb74484: b.eq            #0xb752c0
    // 0xb74488: LoadField: r3 = r2->field_1b
    //     0xb74488: ldur            w3, [x2, #0x1b]
    // 0xb7448c: DecompressPointer r3
    //     0xb7448c: add             x3, x3, HEAP, lsl #32
    // 0xb74490: cmp             w3, NULL
    // 0xb74494: b.ne            #0xb744a0
    // 0xb74498: r2 = Null
    //     0xb74498: mov             x2, NULL
    // 0xb7449c: b               #0xb744a8
    // 0xb744a0: LoadField: r2 = r3->field_1f
    //     0xb744a0: ldur            w2, [x3, #0x1f]
    // 0xb744a4: DecompressPointer r2
    //     0xb744a4: add             x2, x2, HEAP, lsl #32
    // 0xb744a8: cmp             w2, NULL
    // 0xb744ac: b.ne            #0xb744b4
    // 0xb744b0: r2 = ""
    //     0xb744b0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb744b4: StoreField: r0->field_13 = r2
    //     0xb744b4: stur            w2, [x0, #0x13]
    // 0xb744b8: r16 = " / Qty: "
    //     0xb744b8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb744bc: ldr             x16, [x16, #0x760]
    // 0xb744c0: ArrayStore: r0[0] = r16  ; List_4
    //     0xb744c0: stur            w16, [x0, #0x17]
    // 0xb744c4: cmp             w3, NULL
    // 0xb744c8: b.ne            #0xb744d4
    // 0xb744cc: r2 = Null
    //     0xb744cc: mov             x2, NULL
    // 0xb744d0: b               #0xb744dc
    // 0xb744d4: LoadField: r2 = r3->field_23
    //     0xb744d4: ldur            w2, [x3, #0x23]
    // 0xb744d8: DecompressPointer r2
    //     0xb744d8: add             x2, x2, HEAP, lsl #32
    // 0xb744dc: StoreField: r0->field_1b = r2
    //     0xb744dc: stur            w2, [x0, #0x1b]
    // 0xb744e0: str             x0, [SP]
    // 0xb744e4: r0 = _interpolate()
    //     0xb744e4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb744e8: b               #0xb74700
    // 0xb744ec: ldur            x0, [fp, #-8]
    // 0xb744f0: r1 = Null
    //     0xb744f0: mov             x1, NULL
    // 0xb744f4: r2 = 8
    //     0xb744f4: movz            x2, #0x8
    // 0xb744f8: r0 = AllocateArray()
    //     0xb744f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb744fc: r16 = "Variant: "
    //     0xb744fc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb74500: ldr             x16, [x16, #0xf08]
    // 0xb74504: StoreField: r0->field_f = r16
    //     0xb74504: stur            w16, [x0, #0xf]
    // 0xb74508: ldur            x1, [fp, #-8]
    // 0xb7450c: LoadField: r2 = r1->field_b
    //     0xb7450c: ldur            w2, [x1, #0xb]
    // 0xb74510: DecompressPointer r2
    //     0xb74510: add             x2, x2, HEAP, lsl #32
    // 0xb74514: cmp             w2, NULL
    // 0xb74518: b.eq            #0xb752c4
    // 0xb7451c: LoadField: r3 = r2->field_1b
    //     0xb7451c: ldur            w3, [x2, #0x1b]
    // 0xb74520: DecompressPointer r3
    //     0xb74520: add             x3, x3, HEAP, lsl #32
    // 0xb74524: cmp             w3, NULL
    // 0xb74528: b.ne            #0xb74534
    // 0xb7452c: r2 = Null
    //     0xb7452c: mov             x2, NULL
    // 0xb74530: b               #0xb7453c
    // 0xb74534: LoadField: r2 = r3->field_1f
    //     0xb74534: ldur            w2, [x3, #0x1f]
    // 0xb74538: DecompressPointer r2
    //     0xb74538: add             x2, x2, HEAP, lsl #32
    // 0xb7453c: cmp             w2, NULL
    // 0xb74540: b.ne            #0xb74548
    // 0xb74544: r2 = ""
    //     0xb74544: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74548: StoreField: r0->field_13 = r2
    //     0xb74548: stur            w2, [x0, #0x13]
    // 0xb7454c: r16 = " / Qty: "
    //     0xb7454c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb74550: ldr             x16, [x16, #0x760]
    // 0xb74554: ArrayStore: r0[0] = r16  ; List_4
    //     0xb74554: stur            w16, [x0, #0x17]
    // 0xb74558: cmp             w3, NULL
    // 0xb7455c: b.ne            #0xb74568
    // 0xb74560: r2 = Null
    //     0xb74560: mov             x2, NULL
    // 0xb74564: b               #0xb74570
    // 0xb74568: LoadField: r2 = r3->field_23
    //     0xb74568: ldur            w2, [x3, #0x23]
    // 0xb7456c: DecompressPointer r2
    //     0xb7456c: add             x2, x2, HEAP, lsl #32
    // 0xb74570: StoreField: r0->field_1b = r2
    //     0xb74570: stur            w2, [x0, #0x1b]
    // 0xb74574: str             x0, [SP]
    // 0xb74578: r0 = _interpolate()
    //     0xb74578: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7457c: b               #0xb74700
    // 0xb74580: ldur            x1, [fp, #-8]
    // 0xb74584: LoadField: r0 = r1->field_b
    //     0xb74584: ldur            w0, [x1, #0xb]
    // 0xb74588: DecompressPointer r0
    //     0xb74588: add             x0, x0, HEAP, lsl #32
    // 0xb7458c: cmp             w0, NULL
    // 0xb74590: b.eq            #0xb752c8
    // 0xb74594: LoadField: r2 = r0->field_1f
    //     0xb74594: ldur            w2, [x0, #0x1f]
    // 0xb74598: DecompressPointer r2
    //     0xb74598: add             x2, x2, HEAP, lsl #32
    // 0xb7459c: cmp             w2, NULL
    // 0xb745a0: b.ne            #0xb745ac
    // 0xb745a4: r0 = Null
    //     0xb745a4: mov             x0, NULL
    // 0xb745a8: b               #0xb745b4
    // 0xb745ac: LoadField: r0 = r2->field_7f
    //     0xb745ac: ldur            w0, [x2, #0x7f]
    // 0xb745b0: DecompressPointer r0
    //     0xb745b0: add             x0, x0, HEAP, lsl #32
    // 0xb745b4: r2 = LoadClassIdInstr(r0)
    //     0xb745b4: ldur            x2, [x0, #-1]
    //     0xb745b8: ubfx            x2, x2, #0xc, #0x14
    // 0xb745bc: r16 = "size"
    //     0xb745bc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb745c0: ldr             x16, [x16, #0x9c0]
    // 0xb745c4: stp             x16, x0, [SP]
    // 0xb745c8: mov             x0, x2
    // 0xb745cc: mov             lr, x0
    // 0xb745d0: ldr             lr, [x21, lr, lsl #3]
    // 0xb745d4: blr             lr
    // 0xb745d8: tbnz            w0, #4, #0xb74670
    // 0xb745dc: ldur            x0, [fp, #-8]
    // 0xb745e0: r1 = Null
    //     0xb745e0: mov             x1, NULL
    // 0xb745e4: r2 = 8
    //     0xb745e4: movz            x2, #0x8
    // 0xb745e8: r0 = AllocateArray()
    //     0xb745e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb745ec: r16 = "Size: "
    //     0xb745ec: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb745f0: ldr             x16, [x16, #0xf00]
    // 0xb745f4: StoreField: r0->field_f = r16
    //     0xb745f4: stur            w16, [x0, #0xf]
    // 0xb745f8: ldur            x1, [fp, #-8]
    // 0xb745fc: LoadField: r2 = r1->field_b
    //     0xb745fc: ldur            w2, [x1, #0xb]
    // 0xb74600: DecompressPointer r2
    //     0xb74600: add             x2, x2, HEAP, lsl #32
    // 0xb74604: cmp             w2, NULL
    // 0xb74608: b.eq            #0xb752cc
    // 0xb7460c: LoadField: r3 = r2->field_1f
    //     0xb7460c: ldur            w3, [x2, #0x1f]
    // 0xb74610: DecompressPointer r3
    //     0xb74610: add             x3, x3, HEAP, lsl #32
    // 0xb74614: cmp             w3, NULL
    // 0xb74618: b.ne            #0xb74624
    // 0xb7461c: r2 = Null
    //     0xb7461c: mov             x2, NULL
    // 0xb74620: b               #0xb7462c
    // 0xb74624: LoadField: r2 = r3->field_7
    //     0xb74624: ldur            w2, [x3, #7]
    // 0xb74628: DecompressPointer r2
    //     0xb74628: add             x2, x2, HEAP, lsl #32
    // 0xb7462c: cmp             w2, NULL
    // 0xb74630: b.ne            #0xb74638
    // 0xb74634: r2 = ""
    //     0xb74634: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74638: StoreField: r0->field_13 = r2
    //     0xb74638: stur            w2, [x0, #0x13]
    // 0xb7463c: r16 = " / Qty: "
    //     0xb7463c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb74640: ldr             x16, [x16, #0x760]
    // 0xb74644: ArrayStore: r0[0] = r16  ; List_4
    //     0xb74644: stur            w16, [x0, #0x17]
    // 0xb74648: cmp             w3, NULL
    // 0xb7464c: b.ne            #0xb74658
    // 0xb74650: r2 = Null
    //     0xb74650: mov             x2, NULL
    // 0xb74654: b               #0xb74660
    // 0xb74658: LoadField: r2 = r3->field_f
    //     0xb74658: ldur            w2, [x3, #0xf]
    // 0xb7465c: DecompressPointer r2
    //     0xb7465c: add             x2, x2, HEAP, lsl #32
    // 0xb74660: StoreField: r0->field_1b = r2
    //     0xb74660: stur            w2, [x0, #0x1b]
    // 0xb74664: str             x0, [SP]
    // 0xb74668: r0 = _interpolate()
    //     0xb74668: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7466c: b               #0xb74700
    // 0xb74670: ldur            x0, [fp, #-8]
    // 0xb74674: r1 = Null
    //     0xb74674: mov             x1, NULL
    // 0xb74678: r2 = 8
    //     0xb74678: movz            x2, #0x8
    // 0xb7467c: r0 = AllocateArray()
    //     0xb7467c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74680: r16 = "Variant: "
    //     0xb74680: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb74684: ldr             x16, [x16, #0xf08]
    // 0xb74688: StoreField: r0->field_f = r16
    //     0xb74688: stur            w16, [x0, #0xf]
    // 0xb7468c: ldur            x1, [fp, #-8]
    // 0xb74690: LoadField: r2 = r1->field_b
    //     0xb74690: ldur            w2, [x1, #0xb]
    // 0xb74694: DecompressPointer r2
    //     0xb74694: add             x2, x2, HEAP, lsl #32
    // 0xb74698: cmp             w2, NULL
    // 0xb7469c: b.eq            #0xb752d0
    // 0xb746a0: LoadField: r3 = r2->field_1f
    //     0xb746a0: ldur            w3, [x2, #0x1f]
    // 0xb746a4: DecompressPointer r3
    //     0xb746a4: add             x3, x3, HEAP, lsl #32
    // 0xb746a8: cmp             w3, NULL
    // 0xb746ac: b.ne            #0xb746b8
    // 0xb746b0: r2 = Null
    //     0xb746b0: mov             x2, NULL
    // 0xb746b4: b               #0xb746c0
    // 0xb746b8: LoadField: r2 = r3->field_7
    //     0xb746b8: ldur            w2, [x3, #7]
    // 0xb746bc: DecompressPointer r2
    //     0xb746bc: add             x2, x2, HEAP, lsl #32
    // 0xb746c0: cmp             w2, NULL
    // 0xb746c4: b.ne            #0xb746cc
    // 0xb746c8: r2 = ""
    //     0xb746c8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb746cc: StoreField: r0->field_13 = r2
    //     0xb746cc: stur            w2, [x0, #0x13]
    // 0xb746d0: r16 = " / Qty: "
    //     0xb746d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb746d4: ldr             x16, [x16, #0x760]
    // 0xb746d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb746d8: stur            w16, [x0, #0x17]
    // 0xb746dc: cmp             w3, NULL
    // 0xb746e0: b.ne            #0xb746ec
    // 0xb746e4: r2 = Null
    //     0xb746e4: mov             x2, NULL
    // 0xb746e8: b               #0xb746f4
    // 0xb746ec: LoadField: r2 = r3->field_f
    //     0xb746ec: ldur            w2, [x3, #0xf]
    // 0xb746f0: DecompressPointer r2
    //     0xb746f0: add             x2, x2, HEAP, lsl #32
    // 0xb746f4: StoreField: r0->field_1b = r2
    //     0xb746f4: stur            w2, [x0, #0x1b]
    // 0xb746f8: str             x0, [SP]
    // 0xb746fc: r0 = _interpolate()
    //     0xb746fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb74700: mov             x2, x0
    // 0xb74704: ldur            x0, [fp, #-8]
    // 0xb74708: ldur            x1, [fp, #-0x10]
    // 0xb7470c: stur            x2, [fp, #-0x28]
    // 0xb74710: r0 = of()
    //     0xb74710: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74714: LoadField: r1 = r0->field_87
    //     0xb74714: ldur            w1, [x0, #0x87]
    // 0xb74718: DecompressPointer r1
    //     0xb74718: add             x1, x1, HEAP, lsl #32
    // 0xb7471c: LoadField: r0 = r1->field_2b
    //     0xb7471c: ldur            w0, [x1, #0x2b]
    // 0xb74720: DecompressPointer r0
    //     0xb74720: add             x0, x0, HEAP, lsl #32
    // 0xb74724: r16 = 12.000000
    //     0xb74724: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb74728: ldr             x16, [x16, #0x9e8]
    // 0xb7472c: r30 = Instance_Color
    //     0xb7472c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb74730: stp             lr, x16, [SP]
    // 0xb74734: mov             x1, x0
    // 0xb74738: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb74738: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7473c: ldr             x4, [x4, #0xaa0]
    // 0xb74740: r0 = copyWith()
    //     0xb74740: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb74744: stur            x0, [fp, #-0x40]
    // 0xb74748: r0 = Text()
    //     0xb74748: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7474c: mov             x1, x0
    // 0xb74750: ldur            x0, [fp, #-0x28]
    // 0xb74754: stur            x1, [fp, #-0x60]
    // 0xb74758: StoreField: r1->field_b = r0
    //     0xb74758: stur            w0, [x1, #0xb]
    // 0xb7475c: ldur            x0, [fp, #-0x40]
    // 0xb74760: StoreField: r1->field_13 = r0
    //     0xb74760: stur            w0, [x1, #0x13]
    // 0xb74764: r0 = Padding()
    //     0xb74764: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb74768: mov             x1, x0
    // 0xb7476c: r0 = Instance_EdgeInsets
    //     0xb7476c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb74770: ldr             x0, [x0, #0x770]
    // 0xb74774: stur            x1, [fp, #-0x28]
    // 0xb74778: StoreField: r1->field_f = r0
    //     0xb74778: stur            w0, [x1, #0xf]
    // 0xb7477c: ldur            x0, [fp, #-0x60]
    // 0xb74780: StoreField: r1->field_b = r0
    //     0xb74780: stur            w0, [x1, #0xb]
    // 0xb74784: ldur            x0, [fp, #-8]
    // 0xb74788: LoadField: r2 = r0->field_b
    //     0xb74788: ldur            w2, [x0, #0xb]
    // 0xb7478c: DecompressPointer r2
    //     0xb7478c: add             x2, x2, HEAP, lsl #32
    // 0xb74790: cmp             w2, NULL
    // 0xb74794: b.eq            #0xb752d4
    // 0xb74798: LoadField: r3 = r2->field_f
    //     0xb74798: ldur            w3, [x2, #0xf]
    // 0xb7479c: DecompressPointer r3
    //     0xb7479c: add             x3, x3, HEAP, lsl #32
    // 0xb747a0: r16 = "return_order_intermediate"
    //     0xb747a0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xb747a4: ldr             x16, [x16, #0xb00]
    // 0xb747a8: stp             x16, x3, [SP]
    // 0xb747ac: r0 = ==()
    //     0xb747ac: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb747b0: tbnz            w0, #4, #0xb74830
    // 0xb747b4: ldur            x0, [fp, #-8]
    // 0xb747b8: LoadField: r1 = r0->field_b
    //     0xb747b8: ldur            w1, [x0, #0xb]
    // 0xb747bc: DecompressPointer r1
    //     0xb747bc: add             x1, x1, HEAP, lsl #32
    // 0xb747c0: cmp             w1, NULL
    // 0xb747c4: b.eq            #0xb752d8
    // 0xb747c8: LoadField: r0 = r1->field_23
    //     0xb747c8: ldur            w0, [x1, #0x23]
    // 0xb747cc: DecompressPointer r0
    //     0xb747cc: add             x0, x0, HEAP, lsl #32
    // 0xb747d0: cmp             w0, NULL
    // 0xb747d4: b.ne            #0xb747e0
    // 0xb747d8: r0 = Null
    //     0xb747d8: mov             x0, NULL
    // 0xb747dc: b               #0xb7481c
    // 0xb747e0: LoadField: r1 = r0->field_37
    //     0xb747e0: ldur            w1, [x0, #0x37]
    // 0xb747e4: DecompressPointer r1
    //     0xb747e4: add             x1, x1, HEAP, lsl #32
    // 0xb747e8: cmp             w1, NULL
    // 0xb747ec: b.ne            #0xb747f8
    // 0xb747f0: r0 = Null
    //     0xb747f0: mov             x0, NULL
    // 0xb747f4: b               #0xb7481c
    // 0xb747f8: LoadField: r0 = r1->field_1f
    //     0xb747f8: ldur            w0, [x1, #0x1f]
    // 0xb747fc: DecompressPointer r0
    //     0xb747fc: add             x0, x0, HEAP, lsl #32
    // 0xb74800: cmp             w0, NULL
    // 0xb74804: b.ne            #0xb74810
    // 0xb74808: r0 = Null
    //     0xb74808: mov             x0, NULL
    // 0xb7480c: b               #0xb7481c
    // 0xb74810: LoadField: r1 = r0->field_7
    //     0xb74810: ldur            w1, [x0, #7]
    // 0xb74814: DecompressPointer r1
    //     0xb74814: add             x1, x1, HEAP, lsl #32
    // 0xb74818: mov             x0, x1
    // 0xb7481c: cmp             w0, NULL
    // 0xb74820: b.ne            #0xb74828
    // 0xb74824: r0 = ""
    //     0xb74824: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb74828: mov             x7, x0
    // 0xb7482c: b               #0xb748f0
    // 0xb74830: ldur            x0, [fp, #-8]
    // 0xb74834: LoadField: r1 = r0->field_b
    //     0xb74834: ldur            w1, [x0, #0xb]
    // 0xb74838: DecompressPointer r1
    //     0xb74838: add             x1, x1, HEAP, lsl #32
    // 0xb7483c: cmp             w1, NULL
    // 0xb74840: b.eq            #0xb752dc
    // 0xb74844: LoadField: r2 = r1->field_f
    //     0xb74844: ldur            w2, [x1, #0xf]
    // 0xb74848: DecompressPointer r2
    //     0xb74848: add             x2, x2, HEAP, lsl #32
    // 0xb7484c: r16 = "cancel_order"
    //     0xb7484c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xb74850: ldr             x16, [x16, #0x98]
    // 0xb74854: stp             x16, x2, [SP]
    // 0xb74858: r0 = ==()
    //     0xb74858: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xb7485c: tbnz            w0, #4, #0xb748a8
    // 0xb74860: ldur            x0, [fp, #-8]
    // 0xb74864: LoadField: r1 = r0->field_b
    //     0xb74864: ldur            w1, [x0, #0xb]
    // 0xb74868: DecompressPointer r1
    //     0xb74868: add             x1, x1, HEAP, lsl #32
    // 0xb7486c: cmp             w1, NULL
    // 0xb74870: b.eq            #0xb752e0
    // 0xb74874: LoadField: r0 = r1->field_1b
    //     0xb74874: ldur            w0, [x1, #0x1b]
    // 0xb74878: DecompressPointer r0
    //     0xb74878: add             x0, x0, HEAP, lsl #32
    // 0xb7487c: cmp             w0, NULL
    // 0xb74880: b.ne            #0xb7488c
    // 0xb74884: r0 = Null
    //     0xb74884: mov             x0, NULL
    // 0xb74888: b               #0xb74898
    // 0xb7488c: LoadField: r1 = r0->field_4b
    //     0xb7488c: ldur            w1, [x0, #0x4b]
    // 0xb74890: DecompressPointer r1
    //     0xb74890: add             x1, x1, HEAP, lsl #32
    // 0xb74894: mov             x0, x1
    // 0xb74898: cmp             w0, NULL
    // 0xb7489c: b.ne            #0xb748ec
    // 0xb748a0: r0 = ""
    //     0xb748a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb748a4: b               #0xb748ec
    // 0xb748a8: ldur            x0, [fp, #-8]
    // 0xb748ac: LoadField: r1 = r0->field_b
    //     0xb748ac: ldur            w1, [x0, #0xb]
    // 0xb748b0: DecompressPointer r1
    //     0xb748b0: add             x1, x1, HEAP, lsl #32
    // 0xb748b4: cmp             w1, NULL
    // 0xb748b8: b.eq            #0xb752e4
    // 0xb748bc: LoadField: r0 = r1->field_1f
    //     0xb748bc: ldur            w0, [x1, #0x1f]
    // 0xb748c0: DecompressPointer r0
    //     0xb748c0: add             x0, x0, HEAP, lsl #32
    // 0xb748c4: cmp             w0, NULL
    // 0xb748c8: b.ne            #0xb748d4
    // 0xb748cc: r0 = Null
    //     0xb748cc: mov             x0, NULL
    // 0xb748d0: b               #0xb748e0
    // 0xb748d4: LoadField: r1 = r0->field_13
    //     0xb748d4: ldur            w1, [x0, #0x13]
    // 0xb748d8: DecompressPointer r1
    //     0xb748d8: add             x1, x1, HEAP, lsl #32
    // 0xb748dc: mov             x0, x1
    // 0xb748e0: cmp             w0, NULL
    // 0xb748e4: b.ne            #0xb748ec
    // 0xb748e8: r0 = ""
    //     0xb748e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb748ec: mov             x7, x0
    // 0xb748f0: ldur            x6, [fp, #-0x30]
    // 0xb748f4: ldur            x5, [fp, #-0x18]
    // 0xb748f8: ldur            x4, [fp, #-0x38]
    // 0xb748fc: ldur            x3, [fp, #-0x58]
    // 0xb74900: ldur            x2, [fp, #-0x50]
    // 0xb74904: ldur            x0, [fp, #-0x28]
    // 0xb74908: ldur            x1, [fp, #-0x10]
    // 0xb7490c: stur            x7, [fp, #-8]
    // 0xb74910: r0 = of()
    //     0xb74910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74914: LoadField: r1 = r0->field_87
    //     0xb74914: ldur            w1, [x0, #0x87]
    // 0xb74918: DecompressPointer r1
    //     0xb74918: add             x1, x1, HEAP, lsl #32
    // 0xb7491c: LoadField: r0 = r1->field_7
    //     0xb7491c: ldur            w0, [x1, #7]
    // 0xb74920: DecompressPointer r0
    //     0xb74920: add             x0, x0, HEAP, lsl #32
    // 0xb74924: r16 = 12.000000
    //     0xb74924: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb74928: ldr             x16, [x16, #0x9e8]
    // 0xb7492c: r30 = Instance_Color
    //     0xb7492c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb74930: stp             lr, x16, [SP]
    // 0xb74934: mov             x1, x0
    // 0xb74938: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb74938: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7493c: ldr             x4, [x4, #0xaa0]
    // 0xb74940: r0 = copyWith()
    //     0xb74940: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb74944: stur            x0, [fp, #-0x40]
    // 0xb74948: r0 = Text()
    //     0xb74948: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7494c: mov             x3, x0
    // 0xb74950: ldur            x0, [fp, #-8]
    // 0xb74954: stur            x3, [fp, #-0x60]
    // 0xb74958: StoreField: r3->field_b = r0
    //     0xb74958: stur            w0, [x3, #0xb]
    // 0xb7495c: ldur            x0, [fp, #-0x40]
    // 0xb74960: StoreField: r3->field_13 = r0
    //     0xb74960: stur            w0, [x3, #0x13]
    // 0xb74964: r1 = Null
    //     0xb74964: mov             x1, NULL
    // 0xb74968: r2 = 8
    //     0xb74968: movz            x2, #0x8
    // 0xb7496c: r0 = AllocateArray()
    //     0xb7496c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74970: mov             x2, x0
    // 0xb74974: ldur            x0, [fp, #-0x50]
    // 0xb74978: stur            x2, [fp, #-8]
    // 0xb7497c: StoreField: r2->field_f = r0
    //     0xb7497c: stur            w0, [x2, #0xf]
    // 0xb74980: ldur            x0, [fp, #-0x28]
    // 0xb74984: StoreField: r2->field_13 = r0
    //     0xb74984: stur            w0, [x2, #0x13]
    // 0xb74988: r16 = Instance_SizedBox
    //     0xb74988: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb7498c: ldr             x16, [x16, #0xc70]
    // 0xb74990: ArrayStore: r2[0] = r16  ; List_4
    //     0xb74990: stur            w16, [x2, #0x17]
    // 0xb74994: ldur            x0, [fp, #-0x60]
    // 0xb74998: StoreField: r2->field_1b = r0
    //     0xb74998: stur            w0, [x2, #0x1b]
    // 0xb7499c: r1 = <Widget>
    //     0xb7499c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb749a0: r0 = AllocateGrowableArray()
    //     0xb749a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb749a4: mov             x1, x0
    // 0xb749a8: ldur            x0, [fp, #-8]
    // 0xb749ac: stur            x1, [fp, #-0x28]
    // 0xb749b0: StoreField: r1->field_f = r0
    //     0xb749b0: stur            w0, [x1, #0xf]
    // 0xb749b4: r0 = 8
    //     0xb749b4: movz            x0, #0x8
    // 0xb749b8: StoreField: r1->field_b = r0
    //     0xb749b8: stur            w0, [x1, #0xb]
    // 0xb749bc: r0 = Column()
    //     0xb749bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb749c0: mov             x2, x0
    // 0xb749c4: r0 = Instance_Axis
    //     0xb749c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb749c8: stur            x2, [fp, #-8]
    // 0xb749cc: StoreField: r2->field_f = r0
    //     0xb749cc: stur            w0, [x2, #0xf]
    // 0xb749d0: r3 = Instance_MainAxisAlignment
    //     0xb749d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb749d4: ldr             x3, [x3, #0xa08]
    // 0xb749d8: StoreField: r2->field_13 = r3
    //     0xb749d8: stur            w3, [x2, #0x13]
    // 0xb749dc: r4 = Instance_MainAxisSize
    //     0xb749dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb749e0: ldr             x4, [x4, #0xa10]
    // 0xb749e4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb749e4: stur            w4, [x2, #0x17]
    // 0xb749e8: r5 = Instance_CrossAxisAlignment
    //     0xb749e8: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb749ec: ldr             x5, [x5, #0x890]
    // 0xb749f0: StoreField: r2->field_1b = r5
    //     0xb749f0: stur            w5, [x2, #0x1b]
    // 0xb749f4: r6 = Instance_VerticalDirection
    //     0xb749f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb749f8: ldr             x6, [x6, #0xa20]
    // 0xb749fc: StoreField: r2->field_23 = r6
    //     0xb749fc: stur            w6, [x2, #0x23]
    // 0xb74a00: r7 = Instance_Clip
    //     0xb74a00: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb74a04: ldr             x7, [x7, #0x38]
    // 0xb74a08: StoreField: r2->field_2b = r7
    //     0xb74a08: stur            w7, [x2, #0x2b]
    // 0xb74a0c: StoreField: r2->field_2f = rZR
    //     0xb74a0c: stur            xzr, [x2, #0x2f]
    // 0xb74a10: ldur            x1, [fp, #-0x28]
    // 0xb74a14: StoreField: r2->field_b = r1
    //     0xb74a14: stur            w1, [x2, #0xb]
    // 0xb74a18: r1 = <FlexParentData>
    //     0xb74a18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb74a1c: ldr             x1, [x1, #0xe00]
    // 0xb74a20: r0 = Expanded()
    //     0xb74a20: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb74a24: mov             x3, x0
    // 0xb74a28: r0 = 1
    //     0xb74a28: movz            x0, #0x1
    // 0xb74a2c: stur            x3, [fp, #-0x28]
    // 0xb74a30: StoreField: r3->field_13 = r0
    //     0xb74a30: stur            x0, [x3, #0x13]
    // 0xb74a34: r4 = Instance_FlexFit
    //     0xb74a34: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb74a38: ldr             x4, [x4, #0xe08]
    // 0xb74a3c: StoreField: r3->field_1b = r4
    //     0xb74a3c: stur            w4, [x3, #0x1b]
    // 0xb74a40: ldur            x1, [fp, #-8]
    // 0xb74a44: StoreField: r3->field_b = r1
    //     0xb74a44: stur            w1, [x3, #0xb]
    // 0xb74a48: r1 = Null
    //     0xb74a48: mov             x1, NULL
    // 0xb74a4c: r2 = 6
    //     0xb74a4c: movz            x2, #0x6
    // 0xb74a50: r0 = AllocateArray()
    //     0xb74a50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74a54: mov             x2, x0
    // 0xb74a58: ldur            x0, [fp, #-0x58]
    // 0xb74a5c: stur            x2, [fp, #-8]
    // 0xb74a60: StoreField: r2->field_f = r0
    //     0xb74a60: stur            w0, [x2, #0xf]
    // 0xb74a64: r16 = Instance_SizedBox
    //     0xb74a64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb74a68: ldr             x16, [x16, #0xb20]
    // 0xb74a6c: StoreField: r2->field_13 = r16
    //     0xb74a6c: stur            w16, [x2, #0x13]
    // 0xb74a70: ldur            x0, [fp, #-0x28]
    // 0xb74a74: ArrayStore: r2[0] = r0  ; List_4
    //     0xb74a74: stur            w0, [x2, #0x17]
    // 0xb74a78: r1 = <Widget>
    //     0xb74a78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb74a7c: r0 = AllocateGrowableArray()
    //     0xb74a7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb74a80: mov             x1, x0
    // 0xb74a84: ldur            x0, [fp, #-8]
    // 0xb74a88: stur            x1, [fp, #-0x28]
    // 0xb74a8c: StoreField: r1->field_f = r0
    //     0xb74a8c: stur            w0, [x1, #0xf]
    // 0xb74a90: r2 = 6
    //     0xb74a90: movz            x2, #0x6
    // 0xb74a94: StoreField: r1->field_b = r2
    //     0xb74a94: stur            w2, [x1, #0xb]
    // 0xb74a98: r0 = Row()
    //     0xb74a98: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb74a9c: mov             x3, x0
    // 0xb74aa0: r0 = Instance_Axis
    //     0xb74aa0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb74aa4: stur            x3, [fp, #-8]
    // 0xb74aa8: StoreField: r3->field_f = r0
    //     0xb74aa8: stur            w0, [x3, #0xf]
    // 0xb74aac: r4 = Instance_MainAxisAlignment
    //     0xb74aac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb74ab0: ldr             x4, [x4, #0xa08]
    // 0xb74ab4: StoreField: r3->field_13 = r4
    //     0xb74ab4: stur            w4, [x3, #0x13]
    // 0xb74ab8: r5 = Instance_MainAxisSize
    //     0xb74ab8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb74abc: ldr             x5, [x5, #0xa10]
    // 0xb74ac0: ArrayStore: r3[0] = r5  ; List_4
    //     0xb74ac0: stur            w5, [x3, #0x17]
    // 0xb74ac4: r6 = Instance_CrossAxisAlignment
    //     0xb74ac4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb74ac8: ldr             x6, [x6, #0xa18]
    // 0xb74acc: StoreField: r3->field_1b = r6
    //     0xb74acc: stur            w6, [x3, #0x1b]
    // 0xb74ad0: r7 = Instance_VerticalDirection
    //     0xb74ad0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb74ad4: ldr             x7, [x7, #0xa20]
    // 0xb74ad8: StoreField: r3->field_23 = r7
    //     0xb74ad8: stur            w7, [x3, #0x23]
    // 0xb74adc: r8 = Instance_Clip
    //     0xb74adc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb74ae0: ldr             x8, [x8, #0x38]
    // 0xb74ae4: StoreField: r3->field_2b = r8
    //     0xb74ae4: stur            w8, [x3, #0x2b]
    // 0xb74ae8: StoreField: r3->field_2f = rZR
    //     0xb74ae8: stur            xzr, [x3, #0x2f]
    // 0xb74aec: ldur            x1, [fp, #-0x28]
    // 0xb74af0: StoreField: r3->field_b = r1
    //     0xb74af0: stur            w1, [x3, #0xb]
    // 0xb74af4: r1 = Null
    //     0xb74af4: mov             x1, NULL
    // 0xb74af8: r2 = 2
    //     0xb74af8: movz            x2, #0x2
    // 0xb74afc: r0 = AllocateArray()
    //     0xb74afc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb74b00: mov             x2, x0
    // 0xb74b04: ldur            x0, [fp, #-8]
    // 0xb74b08: stur            x2, [fp, #-0x28]
    // 0xb74b0c: StoreField: r2->field_f = r0
    //     0xb74b0c: stur            w0, [x2, #0xf]
    // 0xb74b10: r1 = <Widget>
    //     0xb74b10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb74b14: r0 = AllocateGrowableArray()
    //     0xb74b14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb74b18: mov             x1, x0
    // 0xb74b1c: ldur            x0, [fp, #-0x28]
    // 0xb74b20: stur            x1, [fp, #-8]
    // 0xb74b24: StoreField: r1->field_f = r0
    //     0xb74b24: stur            w0, [x1, #0xf]
    // 0xb74b28: r0 = 2
    //     0xb74b28: movz            x0, #0x2
    // 0xb74b2c: StoreField: r1->field_b = r0
    //     0xb74b2c: stur            w0, [x1, #0xb]
    // 0xb74b30: r0 = Column()
    //     0xb74b30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb74b34: mov             x1, x0
    // 0xb74b38: r0 = Instance_Axis
    //     0xb74b38: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb74b3c: stur            x1, [fp, #-0x28]
    // 0xb74b40: StoreField: r1->field_f = r0
    //     0xb74b40: stur            w0, [x1, #0xf]
    // 0xb74b44: r2 = Instance_MainAxisAlignment
    //     0xb74b44: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb74b48: ldr             x2, [x2, #0xa08]
    // 0xb74b4c: StoreField: r1->field_13 = r2
    //     0xb74b4c: stur            w2, [x1, #0x13]
    // 0xb74b50: r3 = Instance_MainAxisSize
    //     0xb74b50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb74b54: ldr             x3, [x3, #0xa10]
    // 0xb74b58: ArrayStore: r1[0] = r3  ; List_4
    //     0xb74b58: stur            w3, [x1, #0x17]
    // 0xb74b5c: r4 = Instance_CrossAxisAlignment
    //     0xb74b5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb74b60: ldr             x4, [x4, #0xa18]
    // 0xb74b64: StoreField: r1->field_1b = r4
    //     0xb74b64: stur            w4, [x1, #0x1b]
    // 0xb74b68: r4 = Instance_VerticalDirection
    //     0xb74b68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb74b6c: ldr             x4, [x4, #0xa20]
    // 0xb74b70: StoreField: r1->field_23 = r4
    //     0xb74b70: stur            w4, [x1, #0x23]
    // 0xb74b74: r5 = Instance_Clip
    //     0xb74b74: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb74b78: ldr             x5, [x5, #0x38]
    // 0xb74b7c: StoreField: r1->field_2b = r5
    //     0xb74b7c: stur            w5, [x1, #0x2b]
    // 0xb74b80: StoreField: r1->field_2f = rZR
    //     0xb74b80: stur            xzr, [x1, #0x2f]
    // 0xb74b84: ldur            x6, [fp, #-8]
    // 0xb74b88: StoreField: r1->field_b = r6
    //     0xb74b88: stur            w6, [x1, #0xb]
    // 0xb74b8c: r0 = Padding()
    //     0xb74b8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb74b90: mov             x1, x0
    // 0xb74b94: r0 = Instance_EdgeInsets
    //     0xb74b94: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xb74b98: ldr             x0, [x0, #0xf48]
    // 0xb74b9c: stur            x1, [fp, #-8]
    // 0xb74ba0: StoreField: r1->field_f = r0
    //     0xb74ba0: stur            w0, [x1, #0xf]
    // 0xb74ba4: ldur            x0, [fp, #-0x28]
    // 0xb74ba8: StoreField: r1->field_b = r0
    //     0xb74ba8: stur            w0, [x1, #0xb]
    // 0xb74bac: r0 = Container()
    //     0xb74bac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb74bb0: stur            x0, [fp, #-0x28]
    // 0xb74bb4: r16 = Instance_EdgeInsets
    //     0xb74bb4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb74bb8: ldr             x16, [x16, #0x668]
    // 0xb74bbc: ldur            lr, [fp, #-0x48]
    // 0xb74bc0: stp             lr, x16, [SP, #8]
    // 0xb74bc4: ldur            x16, [fp, #-8]
    // 0xb74bc8: str             x16, [SP]
    // 0xb74bcc: mov             x1, x0
    // 0xb74bd0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xb74bd0: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xb74bd4: ldr             x4, [x4, #0xf50]
    // 0xb74bd8: r0 = Container()
    //     0xb74bd8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb74bdc: r0 = Padding()
    //     0xb74bdc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb74be0: mov             x1, x0
    // 0xb74be4: r0 = Instance_EdgeInsets
    //     0xb74be4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb74be8: ldr             x0, [x0, #0x778]
    // 0xb74bec: stur            x1, [fp, #-8]
    // 0xb74bf0: StoreField: r1->field_f = r0
    //     0xb74bf0: stur            w0, [x1, #0xf]
    // 0xb74bf4: ldur            x0, [fp, #-0x28]
    // 0xb74bf8: StoreField: r1->field_b = r0
    //     0xb74bf8: stur            w0, [x1, #0xb]
    // 0xb74bfc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb74bfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb74c00: ldr             x0, [x0, #0x1c80]
    //     0xb74c04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb74c08: cmp             w0, w16
    //     0xb74c0c: b.ne            #0xb74c18
    //     0xb74c10: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb74c14: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb74c18: r0 = GetNavigation.width()
    //     0xb74c18: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb74c1c: mov             v1.16b, v0.16b
    // 0xb74c20: d0 = 1.200000
    //     0xb74c20: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4dd20] IMM: double(1.2) from 0x3ff3333333333333
    //     0xb74c24: ldr             d0, [x17, #0xd20]
    // 0xb74c28: fmul            d2, d1, d0
    // 0xb74c2c: stur            d2, [fp, #-0x70]
    // 0xb74c30: r16 = <EdgeInsets>
    //     0xb74c30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb74c34: ldr             x16, [x16, #0xda0]
    // 0xb74c38: r30 = Instance_EdgeInsets
    //     0xb74c38: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb74c3c: ldr             lr, [lr, #0x1f0]
    // 0xb74c40: stp             lr, x16, [SP]
    // 0xb74c44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb74c44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb74c48: r0 = all()
    //     0xb74c48: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb74c4c: ldur            x1, [fp, #-0x10]
    // 0xb74c50: stur            x0, [fp, #-0x28]
    // 0xb74c54: r0 = of()
    //     0xb74c54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74c58: LoadField: r1 = r0->field_5b
    //     0xb74c58: ldur            w1, [x0, #0x5b]
    // 0xb74c5c: DecompressPointer r1
    //     0xb74c5c: add             x1, x1, HEAP, lsl #32
    // 0xb74c60: r16 = <Color>
    //     0xb74c60: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb74c64: ldr             x16, [x16, #0xf80]
    // 0xb74c68: stp             x1, x16, [SP]
    // 0xb74c6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb74c6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb74c70: r0 = all()
    //     0xb74c70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb74c74: stur            x0, [fp, #-0x40]
    // 0xb74c78: r0 = Radius()
    //     0xb74c78: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb74c7c: d0 = 20.000000
    //     0xb74c7c: fmov            d0, #20.00000000
    // 0xb74c80: stur            x0, [fp, #-0x48]
    // 0xb74c84: StoreField: r0->field_7 = d0
    //     0xb74c84: stur            d0, [x0, #7]
    // 0xb74c88: StoreField: r0->field_f = d0
    //     0xb74c88: stur            d0, [x0, #0xf]
    // 0xb74c8c: r0 = BorderRadius()
    //     0xb74c8c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb74c90: mov             x1, x0
    // 0xb74c94: ldur            x0, [fp, #-0x48]
    // 0xb74c98: stur            x1, [fp, #-0x50]
    // 0xb74c9c: StoreField: r1->field_7 = r0
    //     0xb74c9c: stur            w0, [x1, #7]
    // 0xb74ca0: StoreField: r1->field_b = r0
    //     0xb74ca0: stur            w0, [x1, #0xb]
    // 0xb74ca4: StoreField: r1->field_f = r0
    //     0xb74ca4: stur            w0, [x1, #0xf]
    // 0xb74ca8: StoreField: r1->field_13 = r0
    //     0xb74ca8: stur            w0, [x1, #0x13]
    // 0xb74cac: r0 = RoundedRectangleBorder()
    //     0xb74cac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb74cb0: mov             x1, x0
    // 0xb74cb4: ldur            x0, [fp, #-0x50]
    // 0xb74cb8: StoreField: r1->field_b = r0
    //     0xb74cb8: stur            w0, [x1, #0xb]
    // 0xb74cbc: r0 = Instance_BorderSide
    //     0xb74cbc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb74cc0: ldr             x0, [x0, #0xe20]
    // 0xb74cc4: StoreField: r1->field_7 = r0
    //     0xb74cc4: stur            w0, [x1, #7]
    // 0xb74cc8: r16 = <RoundedRectangleBorder>
    //     0xb74cc8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb74ccc: ldr             x16, [x16, #0xf78]
    // 0xb74cd0: stp             x1, x16, [SP]
    // 0xb74cd4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb74cd4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb74cd8: r0 = all()
    //     0xb74cd8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb74cdc: stur            x0, [fp, #-0x48]
    // 0xb74ce0: r0 = ButtonStyle()
    //     0xb74ce0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb74ce4: mov             x1, x0
    // 0xb74ce8: ldur            x0, [fp, #-0x40]
    // 0xb74cec: stur            x1, [fp, #-0x50]
    // 0xb74cf0: StoreField: r1->field_b = r0
    //     0xb74cf0: stur            w0, [x1, #0xb]
    // 0xb74cf4: ldur            x0, [fp, #-0x28]
    // 0xb74cf8: StoreField: r1->field_23 = r0
    //     0xb74cf8: stur            w0, [x1, #0x23]
    // 0xb74cfc: ldur            x0, [fp, #-0x48]
    // 0xb74d00: StoreField: r1->field_43 = r0
    //     0xb74d00: stur            w0, [x1, #0x43]
    // 0xb74d04: r0 = TextButtonThemeData()
    //     0xb74d04: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb74d08: mov             x2, x0
    // 0xb74d0c: ldur            x0, [fp, #-0x50]
    // 0xb74d10: stur            x2, [fp, #-0x28]
    // 0xb74d14: StoreField: r2->field_7 = r0
    //     0xb74d14: stur            w0, [x2, #7]
    // 0xb74d18: ldur            x1, [fp, #-0x10]
    // 0xb74d1c: r0 = of()
    //     0xb74d1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74d20: LoadField: r1 = r0->field_87
    //     0xb74d20: ldur            w1, [x0, #0x87]
    // 0xb74d24: DecompressPointer r1
    //     0xb74d24: add             x1, x1, HEAP, lsl #32
    // 0xb74d28: LoadField: r0 = r1->field_7
    //     0xb74d28: ldur            w0, [x1, #7]
    // 0xb74d2c: DecompressPointer r0
    //     0xb74d2c: add             x0, x0, HEAP, lsl #32
    // 0xb74d30: r16 = 14.000000
    //     0xb74d30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb74d34: ldr             x16, [x16, #0x1d8]
    // 0xb74d38: r30 = Instance_Color
    //     0xb74d38: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb74d3c: stp             lr, x16, [SP]
    // 0xb74d40: mov             x1, x0
    // 0xb74d44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb74d44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb74d48: ldr             x4, [x4, #0xaa0]
    // 0xb74d4c: r0 = copyWith()
    //     0xb74d4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb74d50: stur            x0, [fp, #-0x40]
    // 0xb74d54: r0 = Text()
    //     0xb74d54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb74d58: mov             x3, x0
    // 0xb74d5c: r0 = "Continue"
    //     0xb74d5c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0xb74d60: ldr             x0, [x0, #0xfe0]
    // 0xb74d64: stur            x3, [fp, #-0x48]
    // 0xb74d68: StoreField: r3->field_b = r0
    //     0xb74d68: stur            w0, [x3, #0xb]
    // 0xb74d6c: ldur            x0, [fp, #-0x40]
    // 0xb74d70: StoreField: r3->field_13 = r0
    //     0xb74d70: stur            w0, [x3, #0x13]
    // 0xb74d74: ldur            x2, [fp, #-0x20]
    // 0xb74d78: r1 = Function '<anonymous closure>':.
    //     0xb74d78: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f90] AnonymousClosure: (0xb75334), in [package:customer_app/app/presentation/views/glass/orders/cancel_return_order_with_free_product_bottom_sheet.dart] _CancelReturnOrderWithFreeProductBottomSheetState::build (0xb731dc)
    //     0xb74d7c: ldr             x1, [x1, #0xf90]
    // 0xb74d80: r0 = AllocateClosure()
    //     0xb74d80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb74d84: stur            x0, [fp, #-0x20]
    // 0xb74d88: r0 = TextButton()
    //     0xb74d88: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb74d8c: mov             x1, x0
    // 0xb74d90: ldur            x0, [fp, #-0x20]
    // 0xb74d94: stur            x1, [fp, #-0x40]
    // 0xb74d98: StoreField: r1->field_b = r0
    //     0xb74d98: stur            w0, [x1, #0xb]
    // 0xb74d9c: r0 = false
    //     0xb74d9c: add             x0, NULL, #0x30  ; false
    // 0xb74da0: StoreField: r1->field_27 = r0
    //     0xb74da0: stur            w0, [x1, #0x27]
    // 0xb74da4: r2 = true
    //     0xb74da4: add             x2, NULL, #0x20  ; true
    // 0xb74da8: StoreField: r1->field_2f = r2
    //     0xb74da8: stur            w2, [x1, #0x2f]
    // 0xb74dac: ldur            x3, [fp, #-0x48]
    // 0xb74db0: StoreField: r1->field_37 = r3
    //     0xb74db0: stur            w3, [x1, #0x37]
    // 0xb74db4: r0 = TextButtonTheme()
    //     0xb74db4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb74db8: mov             x2, x0
    // 0xb74dbc: ldur            x0, [fp, #-0x28]
    // 0xb74dc0: stur            x2, [fp, #-0x20]
    // 0xb74dc4: StoreField: r2->field_f = r0
    //     0xb74dc4: stur            w0, [x2, #0xf]
    // 0xb74dc8: ldur            x0, [fp, #-0x40]
    // 0xb74dcc: StoreField: r2->field_b = r0
    //     0xb74dcc: stur            w0, [x2, #0xb]
    // 0xb74dd0: r1 = <FlexParentData>
    //     0xb74dd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb74dd4: ldr             x1, [x1, #0xe00]
    // 0xb74dd8: r0 = Expanded()
    //     0xb74dd8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb74ddc: mov             x1, x0
    // 0xb74de0: r0 = 1
    //     0xb74de0: movz            x0, #0x1
    // 0xb74de4: stur            x1, [fp, #-0x28]
    // 0xb74de8: StoreField: r1->field_13 = r0
    //     0xb74de8: stur            x0, [x1, #0x13]
    // 0xb74dec: r2 = Instance_FlexFit
    //     0xb74dec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb74df0: ldr             x2, [x2, #0xe08]
    // 0xb74df4: StoreField: r1->field_1b = r2
    //     0xb74df4: stur            w2, [x1, #0x1b]
    // 0xb74df8: ldur            x3, [fp, #-0x20]
    // 0xb74dfc: StoreField: r1->field_b = r3
    //     0xb74dfc: stur            w3, [x1, #0xb]
    // 0xb74e00: r16 = <EdgeInsets>
    //     0xb74e00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb74e04: ldr             x16, [x16, #0xda0]
    // 0xb74e08: r30 = Instance_EdgeInsets
    //     0xb74e08: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb74e0c: ldr             lr, [lr, #0x1f0]
    // 0xb74e10: stp             lr, x16, [SP]
    // 0xb74e14: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb74e14: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb74e18: r0 = all()
    //     0xb74e18: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb74e1c: stur            x0, [fp, #-0x20]
    // 0xb74e20: r0 = Radius()
    //     0xb74e20: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb74e24: d0 = 20.000000
    //     0xb74e24: fmov            d0, #20.00000000
    // 0xb74e28: stur            x0, [fp, #-0x40]
    // 0xb74e2c: StoreField: r0->field_7 = d0
    //     0xb74e2c: stur            d0, [x0, #7]
    // 0xb74e30: StoreField: r0->field_f = d0
    //     0xb74e30: stur            d0, [x0, #0xf]
    // 0xb74e34: r0 = BorderRadius()
    //     0xb74e34: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb74e38: mov             x2, x0
    // 0xb74e3c: ldur            x0, [fp, #-0x40]
    // 0xb74e40: stur            x2, [fp, #-0x48]
    // 0xb74e44: StoreField: r2->field_7 = r0
    //     0xb74e44: stur            w0, [x2, #7]
    // 0xb74e48: StoreField: r2->field_b = r0
    //     0xb74e48: stur            w0, [x2, #0xb]
    // 0xb74e4c: StoreField: r2->field_f = r0
    //     0xb74e4c: stur            w0, [x2, #0xf]
    // 0xb74e50: StoreField: r2->field_13 = r0
    //     0xb74e50: stur            w0, [x2, #0x13]
    // 0xb74e54: ldur            x1, [fp, #-0x10]
    // 0xb74e58: r0 = of()
    //     0xb74e58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74e5c: LoadField: r1 = r0->field_5b
    //     0xb74e5c: ldur            w1, [x0, #0x5b]
    // 0xb74e60: DecompressPointer r1
    //     0xb74e60: add             x1, x1, HEAP, lsl #32
    // 0xb74e64: r0 = LoadClassIdInstr(r1)
    //     0xb74e64: ldur            x0, [x1, #-1]
    //     0xb74e68: ubfx            x0, x0, #0xc, #0x14
    // 0xb74e6c: d0 = 0.070000
    //     0xb74e6c: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb74e70: ldr             d0, [x17, #0x5f8]
    // 0xb74e74: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb74e74: sub             lr, x0, #0xffa
    //     0xb74e78: ldr             lr, [x21, lr, lsl #3]
    //     0xb74e7c: blr             lr
    // 0xb74e80: stur            x0, [fp, #-0x40]
    // 0xb74e84: r0 = BorderSide()
    //     0xb74e84: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb74e88: mov             x1, x0
    // 0xb74e8c: ldur            x0, [fp, #-0x40]
    // 0xb74e90: stur            x1, [fp, #-0x50]
    // 0xb74e94: StoreField: r1->field_7 = r0
    //     0xb74e94: stur            w0, [x1, #7]
    // 0xb74e98: d0 = 1.000000
    //     0xb74e98: fmov            d0, #1.00000000
    // 0xb74e9c: StoreField: r1->field_b = d0
    //     0xb74e9c: stur            d0, [x1, #0xb]
    // 0xb74ea0: r0 = Instance_BorderStyle
    //     0xb74ea0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb74ea4: ldr             x0, [x0, #0xf68]
    // 0xb74ea8: StoreField: r1->field_13 = r0
    //     0xb74ea8: stur            w0, [x1, #0x13]
    // 0xb74eac: d0 = -1.000000
    //     0xb74eac: fmov            d0, #-1.00000000
    // 0xb74eb0: ArrayStore: r1[0] = d0  ; List_8
    //     0xb74eb0: stur            d0, [x1, #0x17]
    // 0xb74eb4: r0 = RoundedRectangleBorder()
    //     0xb74eb4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb74eb8: mov             x1, x0
    // 0xb74ebc: ldur            x0, [fp, #-0x48]
    // 0xb74ec0: StoreField: r1->field_b = r0
    //     0xb74ec0: stur            w0, [x1, #0xb]
    // 0xb74ec4: ldur            x0, [fp, #-0x50]
    // 0xb74ec8: StoreField: r1->field_7 = r0
    //     0xb74ec8: stur            w0, [x1, #7]
    // 0xb74ecc: r16 = <RoundedRectangleBorder>
    //     0xb74ecc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb74ed0: ldr             x16, [x16, #0xf78]
    // 0xb74ed4: stp             x1, x16, [SP]
    // 0xb74ed8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb74ed8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb74edc: r0 = all()
    //     0xb74edc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb74ee0: stur            x0, [fp, #-0x40]
    // 0xb74ee4: r0 = ButtonStyle()
    //     0xb74ee4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb74ee8: mov             x1, x0
    // 0xb74eec: ldur            x0, [fp, #-0x20]
    // 0xb74ef0: stur            x1, [fp, #-0x48]
    // 0xb74ef4: StoreField: r1->field_23 = r0
    //     0xb74ef4: stur            w0, [x1, #0x23]
    // 0xb74ef8: ldur            x0, [fp, #-0x40]
    // 0xb74efc: StoreField: r1->field_43 = r0
    //     0xb74efc: stur            w0, [x1, #0x43]
    // 0xb74f00: r0 = TextButtonThemeData()
    //     0xb74f00: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb74f04: mov             x2, x0
    // 0xb74f08: ldur            x0, [fp, #-0x48]
    // 0xb74f0c: stur            x2, [fp, #-0x20]
    // 0xb74f10: StoreField: r2->field_7 = r0
    //     0xb74f10: stur            w0, [x2, #7]
    // 0xb74f14: ldur            x1, [fp, #-0x10]
    // 0xb74f18: r0 = of()
    //     0xb74f18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74f1c: LoadField: r1 = r0->field_87
    //     0xb74f1c: ldur            w1, [x0, #0x87]
    // 0xb74f20: DecompressPointer r1
    //     0xb74f20: add             x1, x1, HEAP, lsl #32
    // 0xb74f24: LoadField: r0 = r1->field_7
    //     0xb74f24: ldur            w0, [x1, #7]
    // 0xb74f28: DecompressPointer r0
    //     0xb74f28: add             x0, x0, HEAP, lsl #32
    // 0xb74f2c: ldur            x1, [fp, #-0x10]
    // 0xb74f30: stur            x0, [fp, #-0x40]
    // 0xb74f34: r0 = of()
    //     0xb74f34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb74f38: LoadField: r1 = r0->field_5b
    //     0xb74f38: ldur            w1, [x0, #0x5b]
    // 0xb74f3c: DecompressPointer r1
    //     0xb74f3c: add             x1, x1, HEAP, lsl #32
    // 0xb74f40: r16 = 14.000000
    //     0xb74f40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb74f44: ldr             x16, [x16, #0x1d8]
    // 0xb74f48: stp             x1, x16, [SP]
    // 0xb74f4c: ldur            x1, [fp, #-0x40]
    // 0xb74f50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb74f50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb74f54: ldr             x4, [x4, #0xaa0]
    // 0xb74f58: r0 = copyWith()
    //     0xb74f58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb74f5c: stur            x0, [fp, #-0x10]
    // 0xb74f60: r0 = Text()
    //     0xb74f60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb74f64: mov             x3, x0
    // 0xb74f68: r0 = "Go, Back"
    //     0xb74f68: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0xb74f6c: ldr             x0, [x0, #0xe08]
    // 0xb74f70: stur            x3, [fp, #-0x40]
    // 0xb74f74: StoreField: r3->field_b = r0
    //     0xb74f74: stur            w0, [x3, #0xb]
    // 0xb74f78: ldur            x0, [fp, #-0x10]
    // 0xb74f7c: StoreField: r3->field_13 = r0
    //     0xb74f7c: stur            w0, [x3, #0x13]
    // 0xb74f80: r1 = Function '<anonymous closure>':.
    //     0xb74f80: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f98] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb74f84: ldr             x1, [x1, #0xf98]
    // 0xb74f88: r2 = Null
    //     0xb74f88: mov             x2, NULL
    // 0xb74f8c: r0 = AllocateClosure()
    //     0xb74f8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb74f90: stur            x0, [fp, #-0x10]
    // 0xb74f94: r0 = TextButton()
    //     0xb74f94: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb74f98: mov             x1, x0
    // 0xb74f9c: ldur            x0, [fp, #-0x10]
    // 0xb74fa0: stur            x1, [fp, #-0x48]
    // 0xb74fa4: StoreField: r1->field_b = r0
    //     0xb74fa4: stur            w0, [x1, #0xb]
    // 0xb74fa8: r0 = false
    //     0xb74fa8: add             x0, NULL, #0x30  ; false
    // 0xb74fac: StoreField: r1->field_27 = r0
    //     0xb74fac: stur            w0, [x1, #0x27]
    // 0xb74fb0: r2 = true
    //     0xb74fb0: add             x2, NULL, #0x20  ; true
    // 0xb74fb4: StoreField: r1->field_2f = r2
    //     0xb74fb4: stur            w2, [x1, #0x2f]
    // 0xb74fb8: ldur            x2, [fp, #-0x40]
    // 0xb74fbc: StoreField: r1->field_37 = r2
    //     0xb74fbc: stur            w2, [x1, #0x37]
    // 0xb74fc0: r0 = TextButtonTheme()
    //     0xb74fc0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb74fc4: mov             x2, x0
    // 0xb74fc8: ldur            x0, [fp, #-0x20]
    // 0xb74fcc: stur            x2, [fp, #-0x10]
    // 0xb74fd0: StoreField: r2->field_f = r0
    //     0xb74fd0: stur            w0, [x2, #0xf]
    // 0xb74fd4: ldur            x0, [fp, #-0x48]
    // 0xb74fd8: StoreField: r2->field_b = r0
    //     0xb74fd8: stur            w0, [x2, #0xb]
    // 0xb74fdc: r1 = <FlexParentData>
    //     0xb74fdc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb74fe0: ldr             x1, [x1, #0xe00]
    // 0xb74fe4: r0 = Expanded()
    //     0xb74fe4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb74fe8: mov             x3, x0
    // 0xb74fec: r0 = 1
    //     0xb74fec: movz            x0, #0x1
    // 0xb74ff0: stur            x3, [fp, #-0x20]
    // 0xb74ff4: StoreField: r3->field_13 = r0
    //     0xb74ff4: stur            x0, [x3, #0x13]
    // 0xb74ff8: r0 = Instance_FlexFit
    //     0xb74ff8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb74ffc: ldr             x0, [x0, #0xe08]
    // 0xb75000: StoreField: r3->field_1b = r0
    //     0xb75000: stur            w0, [x3, #0x1b]
    // 0xb75004: ldur            x0, [fp, #-0x10]
    // 0xb75008: StoreField: r3->field_b = r0
    //     0xb75008: stur            w0, [x3, #0xb]
    // 0xb7500c: r1 = Null
    //     0xb7500c: mov             x1, NULL
    // 0xb75010: r2 = 6
    //     0xb75010: movz            x2, #0x6
    // 0xb75014: r0 = AllocateArray()
    //     0xb75014: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75018: mov             x2, x0
    // 0xb7501c: ldur            x0, [fp, #-0x28]
    // 0xb75020: stur            x2, [fp, #-0x10]
    // 0xb75024: StoreField: r2->field_f = r0
    //     0xb75024: stur            w0, [x2, #0xf]
    // 0xb75028: r16 = Instance_SizedBox
    //     0xb75028: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb7502c: ldr             x16, [x16, #0xb20]
    // 0xb75030: StoreField: r2->field_13 = r16
    //     0xb75030: stur            w16, [x2, #0x13]
    // 0xb75034: ldur            x0, [fp, #-0x20]
    // 0xb75038: ArrayStore: r2[0] = r0  ; List_4
    //     0xb75038: stur            w0, [x2, #0x17]
    // 0xb7503c: r1 = <Widget>
    //     0xb7503c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb75040: r0 = AllocateGrowableArray()
    //     0xb75040: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb75044: mov             x1, x0
    // 0xb75048: ldur            x0, [fp, #-0x10]
    // 0xb7504c: stur            x1, [fp, #-0x20]
    // 0xb75050: StoreField: r1->field_f = r0
    //     0xb75050: stur            w0, [x1, #0xf]
    // 0xb75054: r0 = 6
    //     0xb75054: movz            x0, #0x6
    // 0xb75058: StoreField: r1->field_b = r0
    //     0xb75058: stur            w0, [x1, #0xb]
    // 0xb7505c: r0 = Row()
    //     0xb7505c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb75060: mov             x1, x0
    // 0xb75064: r0 = Instance_Axis
    //     0xb75064: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb75068: stur            x1, [fp, #-0x28]
    // 0xb7506c: StoreField: r1->field_f = r0
    //     0xb7506c: stur            w0, [x1, #0xf]
    // 0xb75070: r0 = Instance_MainAxisAlignment
    //     0xb75070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb75074: ldr             x0, [x0, #0xa8]
    // 0xb75078: StoreField: r1->field_13 = r0
    //     0xb75078: stur            w0, [x1, #0x13]
    // 0xb7507c: r0 = Instance_MainAxisSize
    //     0xb7507c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb75080: ldr             x0, [x0, #0xa10]
    // 0xb75084: ArrayStore: r1[0] = r0  ; List_4
    //     0xb75084: stur            w0, [x1, #0x17]
    // 0xb75088: r2 = Instance_CrossAxisAlignment
    //     0xb75088: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb7508c: ldr             x2, [x2, #0x890]
    // 0xb75090: StoreField: r1->field_1b = r2
    //     0xb75090: stur            w2, [x1, #0x1b]
    // 0xb75094: r3 = Instance_VerticalDirection
    //     0xb75094: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb75098: ldr             x3, [x3, #0xa20]
    // 0xb7509c: StoreField: r1->field_23 = r3
    //     0xb7509c: stur            w3, [x1, #0x23]
    // 0xb750a0: r4 = Instance_Clip
    //     0xb750a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb750a4: ldr             x4, [x4, #0x38]
    // 0xb750a8: StoreField: r1->field_2b = r4
    //     0xb750a8: stur            w4, [x1, #0x2b]
    // 0xb750ac: StoreField: r1->field_2f = rZR
    //     0xb750ac: stur            xzr, [x1, #0x2f]
    // 0xb750b0: ldur            x5, [fp, #-0x20]
    // 0xb750b4: StoreField: r1->field_b = r5
    //     0xb750b4: stur            w5, [x1, #0xb]
    // 0xb750b8: ldur            d0, [fp, #-0x70]
    // 0xb750bc: r5 = inline_Allocate_Double()
    //     0xb750bc: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xb750c0: add             x5, x5, #0x10
    //     0xb750c4: cmp             x6, x5
    //     0xb750c8: b.ls            #0xb752e8
    //     0xb750cc: str             x5, [THR, #0x50]  ; THR::top
    //     0xb750d0: sub             x5, x5, #0xf
    //     0xb750d4: movz            x6, #0xe15c
    //     0xb750d8: movk            x6, #0x3, lsl #16
    //     0xb750dc: stur            x6, [x5, #-1]
    // 0xb750e0: StoreField: r5->field_7 = d0
    //     0xb750e0: stur            d0, [x5, #7]
    // 0xb750e4: stur            x5, [fp, #-0x10]
    // 0xb750e8: r0 = Container()
    //     0xb750e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb750ec: stur            x0, [fp, #-0x20]
    // 0xb750f0: ldur            x16, [fp, #-0x10]
    // 0xb750f4: r30 = Instance_Color
    //     0xb750f4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb750f8: stp             lr, x16, [SP, #8]
    // 0xb750fc: ldur            x16, [fp, #-0x28]
    // 0xb75100: str             x16, [SP]
    // 0xb75104: mov             x1, x0
    // 0xb75108: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, width, 0x1, null]
    //     0xb75108: add             x4, PP, #0x33, lsl #12  ; [pp+0x33828] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "width", 0x1, Null]
    //     0xb7510c: ldr             x4, [x4, #0x828]
    // 0xb75110: r0 = Container()
    //     0xb75110: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb75114: r1 = Null
    //     0xb75114: mov             x1, NULL
    // 0xb75118: r2 = 10
    //     0xb75118: movz            x2, #0xa
    // 0xb7511c: r0 = AllocateArray()
    //     0xb7511c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb75120: mov             x2, x0
    // 0xb75124: ldur            x0, [fp, #-0x30]
    // 0xb75128: stur            x2, [fp, #-0x10]
    // 0xb7512c: StoreField: r2->field_f = r0
    //     0xb7512c: stur            w0, [x2, #0xf]
    // 0xb75130: ldur            x0, [fp, #-0x18]
    // 0xb75134: StoreField: r2->field_13 = r0
    //     0xb75134: stur            w0, [x2, #0x13]
    // 0xb75138: ldur            x0, [fp, #-0x38]
    // 0xb7513c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7513c: stur            w0, [x2, #0x17]
    // 0xb75140: ldur            x0, [fp, #-8]
    // 0xb75144: StoreField: r2->field_1b = r0
    //     0xb75144: stur            w0, [x2, #0x1b]
    // 0xb75148: ldur            x0, [fp, #-0x20]
    // 0xb7514c: StoreField: r2->field_1f = r0
    //     0xb7514c: stur            w0, [x2, #0x1f]
    // 0xb75150: r1 = <Widget>
    //     0xb75150: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb75154: r0 = AllocateGrowableArray()
    //     0xb75154: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb75158: mov             x1, x0
    // 0xb7515c: ldur            x0, [fp, #-0x10]
    // 0xb75160: stur            x1, [fp, #-8]
    // 0xb75164: StoreField: r1->field_f = r0
    //     0xb75164: stur            w0, [x1, #0xf]
    // 0xb75168: r0 = 10
    //     0xb75168: movz            x0, #0xa
    // 0xb7516c: StoreField: r1->field_b = r0
    //     0xb7516c: stur            w0, [x1, #0xb]
    // 0xb75170: r0 = Column()
    //     0xb75170: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb75174: mov             x1, x0
    // 0xb75178: r0 = Instance_Axis
    //     0xb75178: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7517c: stur            x1, [fp, #-0x10]
    // 0xb75180: StoreField: r1->field_f = r0
    //     0xb75180: stur            w0, [x1, #0xf]
    // 0xb75184: r2 = Instance_MainAxisAlignment
    //     0xb75184: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb75188: ldr             x2, [x2, #0xa08]
    // 0xb7518c: StoreField: r1->field_13 = r2
    //     0xb7518c: stur            w2, [x1, #0x13]
    // 0xb75190: r2 = Instance_MainAxisSize
    //     0xb75190: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb75194: ldr             x2, [x2, #0xa10]
    // 0xb75198: ArrayStore: r1[0] = r2  ; List_4
    //     0xb75198: stur            w2, [x1, #0x17]
    // 0xb7519c: r2 = Instance_CrossAxisAlignment
    //     0xb7519c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb751a0: ldr             x2, [x2, #0x890]
    // 0xb751a4: StoreField: r1->field_1b = r2
    //     0xb751a4: stur            w2, [x1, #0x1b]
    // 0xb751a8: r2 = Instance_VerticalDirection
    //     0xb751a8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb751ac: ldr             x2, [x2, #0xa20]
    // 0xb751b0: StoreField: r1->field_23 = r2
    //     0xb751b0: stur            w2, [x1, #0x23]
    // 0xb751b4: r2 = Instance_Clip
    //     0xb751b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb751b8: ldr             x2, [x2, #0x38]
    // 0xb751bc: StoreField: r1->field_2b = r2
    //     0xb751bc: stur            w2, [x1, #0x2b]
    // 0xb751c0: StoreField: r1->field_2f = rZR
    //     0xb751c0: stur            xzr, [x1, #0x2f]
    // 0xb751c4: ldur            x2, [fp, #-8]
    // 0xb751c8: StoreField: r1->field_b = r2
    //     0xb751c8: stur            w2, [x1, #0xb]
    // 0xb751cc: r0 = Padding()
    //     0xb751cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb751d0: mov             x1, x0
    // 0xb751d4: r0 = Instance_EdgeInsets
    //     0xb751d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb751d8: ldr             x0, [x0, #0x1f0]
    // 0xb751dc: stur            x1, [fp, #-8]
    // 0xb751e0: StoreField: r1->field_f = r0
    //     0xb751e0: stur            w0, [x1, #0xf]
    // 0xb751e4: ldur            x0, [fp, #-0x10]
    // 0xb751e8: StoreField: r1->field_b = r0
    //     0xb751e8: stur            w0, [x1, #0xb]
    // 0xb751ec: r0 = SingleChildScrollView()
    //     0xb751ec: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb751f0: r1 = Instance_Axis
    //     0xb751f0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb751f4: StoreField: r0->field_b = r1
    //     0xb751f4: stur            w1, [x0, #0xb]
    // 0xb751f8: r1 = false
    //     0xb751f8: add             x1, NULL, #0x30  ; false
    // 0xb751fc: StoreField: r0->field_f = r1
    //     0xb751fc: stur            w1, [x0, #0xf]
    // 0xb75200: ldur            x1, [fp, #-8]
    // 0xb75204: StoreField: r0->field_23 = r1
    //     0xb75204: stur            w1, [x0, #0x23]
    // 0xb75208: r1 = Instance_DragStartBehavior
    //     0xb75208: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb7520c: StoreField: r0->field_27 = r1
    //     0xb7520c: stur            w1, [x0, #0x27]
    // 0xb75210: r1 = Instance_Clip
    //     0xb75210: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb75214: ldr             x1, [x1, #0x7e0]
    // 0xb75218: StoreField: r0->field_2b = r1
    //     0xb75218: stur            w1, [x0, #0x2b]
    // 0xb7521c: r1 = Instance_HitTestBehavior
    //     0xb7521c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb75220: ldr             x1, [x1, #0x288]
    // 0xb75224: StoreField: r0->field_2f = r1
    //     0xb75224: stur            w1, [x0, #0x2f]
    // 0xb75228: LeaveFrame
    //     0xb75228: mov             SP, fp
    //     0xb7522c: ldp             fp, lr, [SP], #0x10
    // 0xb75230: ret
    //     0xb75230: ret             
    // 0xb75234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb75234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb75238: b               #0xb73204
    // 0xb7523c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7523c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75244: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75248: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75248: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7524c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7524c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75250: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7525c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7525c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75268: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75268: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7526c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7526c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75270: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75274: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75278: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7527c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7527c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75280: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75284: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7528c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7528c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75290: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75294: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75294: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb75298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb75298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7529c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7529c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb752e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb752e8: SaveReg d0
    //     0xb752e8: str             q0, [SP, #-0x10]!
    // 0xb752ec: stp             x3, x4, [SP, #-0x10]!
    // 0xb752f0: stp             x1, x2, [SP, #-0x10]!
    // 0xb752f4: SaveReg r0
    //     0xb752f4: str             x0, [SP, #-8]!
    // 0xb752f8: r0 = AllocateDouble()
    //     0xb752f8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb752fc: mov             x5, x0
    // 0xb75300: RestoreReg r0
    //     0xb75300: ldr             x0, [SP], #8
    // 0xb75304: ldp             x1, x2, [SP], #0x10
    // 0xb75308: ldp             x3, x4, [SP], #0x10
    // 0xb7530c: RestoreReg d0
    //     0xb7530c: ldr             q0, [SP], #0x10
    // 0xb75310: b               #0xb750e0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb75334, size: 0x7c
    // 0xb75334: EnterFrame
    //     0xb75334: stp             fp, lr, [SP, #-0x10]!
    //     0xb75338: mov             fp, SP
    // 0xb7533c: AllocStack(0x8)
    //     0xb7533c: sub             SP, SP, #8
    // 0xb75340: SetupParameters()
    //     0xb75340: ldr             x0, [fp, #0x10]
    //     0xb75344: ldur            w1, [x0, #0x17]
    //     0xb75348: add             x1, x1, HEAP, lsl #32
    // 0xb7534c: CheckStackOverflow
    //     0xb7534c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb75350: cmp             SP, x16
    //     0xb75354: b.ls            #0xb753a4
    // 0xb75358: LoadField: r0 = r1->field_f
    //     0xb75358: ldur            w0, [x1, #0xf]
    // 0xb7535c: DecompressPointer r0
    //     0xb7535c: add             x0, x0, HEAP, lsl #32
    // 0xb75360: LoadField: r1 = r0->field_b
    //     0xb75360: ldur            w1, [x0, #0xb]
    // 0xb75364: DecompressPointer r1
    //     0xb75364: add             x1, x1, HEAP, lsl #32
    // 0xb75368: cmp             w1, NULL
    // 0xb7536c: b.eq            #0xb753ac
    // 0xb75370: LoadField: r0 = r1->field_b
    //     0xb75370: ldur            w0, [x1, #0xb]
    // 0xb75374: DecompressPointer r0
    //     0xb75374: add             x0, x0, HEAP, lsl #32
    // 0xb75378: str             x0, [SP]
    // 0xb7537c: r4 = 0
    //     0xb7537c: movz            x4, #0
    // 0xb75380: ldr             x0, [SP]
    // 0xb75384: r16 = UnlinkedCall_0x613b5c
    //     0xb75384: add             x16, PP, #0x55, lsl #12  ; [pp+0x55fa0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb75388: add             x16, x16, #0xfa0
    // 0xb7538c: ldp             x5, lr, [x16]
    // 0xb75390: blr             lr
    // 0xb75394: r0 = Null
    //     0xb75394: mov             x0, NULL
    // 0xb75398: LeaveFrame
    //     0xb75398: mov             SP, fp
    //     0xb7539c: ldp             fp, lr, [SP], #0x10
    // 0xb753a0: ret
    //     0xb753a0: ret             
    // 0xb753a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb753a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb753a8: b               #0xb75358
    // 0xb753ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb753ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4069, size: 0x28, field offset: 0xc
//   const constructor, 
class CancelReturnOrderWithFreeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f5a8, size: 0x24
    // 0xc7f5a8: EnterFrame
    //     0xc7f5a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f5ac: mov             fp, SP
    // 0xc7f5b0: mov             x0, x1
    // 0xc7f5b4: r1 = <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc7f5b4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48848] TypeArguments: <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc7f5b8: ldr             x1, [x1, #0x848]
    // 0xc7f5bc: r0 = _CancelReturnOrderWithFreeProductBottomSheetState()
    //     0xc7f5bc: bl              #0xc7f5cc  ; Allocate_CancelReturnOrderWithFreeProductBottomSheetStateStub -> _CancelReturnOrderWithFreeProductBottomSheetState (size=0x14)
    // 0xc7f5c0: LeaveFrame
    //     0xc7f5c0: mov             SP, fp
    //     0xc7f5c4: ldp             fp, lr, [SP], #0x10
    // 0xc7f5c8: ret
    //     0xc7f5c8: ret             
  }
}
