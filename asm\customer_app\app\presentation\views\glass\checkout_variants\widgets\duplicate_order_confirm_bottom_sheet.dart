// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart

// class id: 1049370, size: 0x8
class :: {

  static _ bagGlassThemeItem(/* No info */) {
    // ** addr: 0xb49190, size: 0x7f0
    // 0xb49190: EnterFrame
    //     0xb49190: stp             fp, lr, [SP, #-0x10]!
    //     0xb49194: mov             fp, SP
    // 0xb49198: AllocStack(0x78)
    //     0xb49198: sub             SP, SP, #0x78
    // 0xb4919c: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4919c: mov             x0, x1
    //     0xb491a0: stur            x1, [fp, #-8]
    //     0xb491a4: stur            x2, [fp, #-0x10]
    // 0xb491a8: CheckStackOverflow
    //     0xb491a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb491ac: cmp             SP, x16
    //     0xb491b0: b.ls            #0xb49978
    // 0xb491b4: r1 = Instance_Color
    //     0xb491b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb491b8: d0 = 0.100000
    //     0xb491b8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb491bc: r0 = withOpacity()
    //     0xb491bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb491c0: mov             x2, x0
    // 0xb491c4: r1 = Null
    //     0xb491c4: mov             x1, NULL
    // 0xb491c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb491c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb491cc: r0 = Border.all()
    //     0xb491cc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb491d0: stur            x0, [fp, #-0x18]
    // 0xb491d4: r0 = Radius()
    //     0xb491d4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb491d8: d0 = 20.000000
    //     0xb491d8: fmov            d0, #20.00000000
    // 0xb491dc: stur            x0, [fp, #-0x20]
    // 0xb491e0: StoreField: r0->field_7 = d0
    //     0xb491e0: stur            d0, [x0, #7]
    // 0xb491e4: StoreField: r0->field_f = d0
    //     0xb491e4: stur            d0, [x0, #0xf]
    // 0xb491e8: r0 = BorderRadius()
    //     0xb491e8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb491ec: mov             x1, x0
    // 0xb491f0: ldur            x0, [fp, #-0x20]
    // 0xb491f4: stur            x1, [fp, #-0x28]
    // 0xb491f8: StoreField: r1->field_7 = r0
    //     0xb491f8: stur            w0, [x1, #7]
    // 0xb491fc: StoreField: r1->field_b = r0
    //     0xb491fc: stur            w0, [x1, #0xb]
    // 0xb49200: StoreField: r1->field_f = r0
    //     0xb49200: stur            w0, [x1, #0xf]
    // 0xb49204: StoreField: r1->field_13 = r0
    //     0xb49204: stur            w0, [x1, #0x13]
    // 0xb49208: r0 = BoxDecoration()
    //     0xb49208: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4920c: mov             x1, x0
    // 0xb49210: ldur            x0, [fp, #-0x18]
    // 0xb49214: stur            x1, [fp, #-0x20]
    // 0xb49218: StoreField: r1->field_f = r0
    //     0xb49218: stur            w0, [x1, #0xf]
    // 0xb4921c: ldur            x0, [fp, #-0x28]
    // 0xb49220: StoreField: r1->field_13 = r0
    //     0xb49220: stur            w0, [x1, #0x13]
    // 0xb49224: r0 = Instance_BoxShape
    //     0xb49224: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb49228: ldr             x0, [x0, #0x80]
    // 0xb4922c: StoreField: r1->field_23 = r0
    //     0xb4922c: stur            w0, [x1, #0x23]
    // 0xb49230: r0 = Radius()
    //     0xb49230: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb49234: d0 = 15.000000
    //     0xb49234: fmov            d0, #15.00000000
    // 0xb49238: stur            x0, [fp, #-0x18]
    // 0xb4923c: StoreField: r0->field_7 = d0
    //     0xb4923c: stur            d0, [x0, #7]
    // 0xb49240: StoreField: r0->field_f = d0
    //     0xb49240: stur            d0, [x0, #0xf]
    // 0xb49244: r0 = BorderRadius()
    //     0xb49244: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb49248: mov             x3, x0
    // 0xb4924c: ldur            x0, [fp, #-0x18]
    // 0xb49250: stur            x3, [fp, #-0x28]
    // 0xb49254: StoreField: r3->field_7 = r0
    //     0xb49254: stur            w0, [x3, #7]
    // 0xb49258: StoreField: r3->field_b = r0
    //     0xb49258: stur            w0, [x3, #0xb]
    // 0xb4925c: StoreField: r3->field_f = r0
    //     0xb4925c: stur            w0, [x3, #0xf]
    // 0xb49260: StoreField: r3->field_13 = r0
    //     0xb49260: stur            w0, [x3, #0x13]
    // 0xb49264: ldur            x0, [fp, #-0x10]
    // 0xb49268: LoadField: r1 = r0->field_13
    //     0xb49268: ldur            w1, [x0, #0x13]
    // 0xb4926c: DecompressPointer r1
    //     0xb4926c: add             x1, x1, HEAP, lsl #32
    // 0xb49270: cmp             w1, NULL
    // 0xb49274: b.ne            #0xb49280
    // 0xb49278: r4 = ""
    //     0xb49278: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4927c: b               #0xb49284
    // 0xb49280: mov             x4, x1
    // 0xb49284: stur            x4, [fp, #-0x18]
    // 0xb49288: r1 = Function '<anonymous closure>': static.
    //     0xb49288: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e00] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb4928c: ldr             x1, [x1, #0xe00]
    // 0xb49290: r2 = Null
    //     0xb49290: mov             x2, NULL
    // 0xb49294: r0 = AllocateClosure()
    //     0xb49294: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb49298: r1 = Function '<anonymous closure>': static.
    //     0xb49298: add             x1, PP, #0x56, lsl #12  ; [pp+0x56e08] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb4929c: ldr             x1, [x1, #0xe08]
    // 0xb492a0: r2 = Null
    //     0xb492a0: mov             x2, NULL
    // 0xb492a4: stur            x0, [fp, #-0x30]
    // 0xb492a8: r0 = AllocateClosure()
    //     0xb492a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb492ac: stur            x0, [fp, #-0x38]
    // 0xb492b0: r0 = CachedNetworkImage()
    //     0xb492b0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb492b4: stur            x0, [fp, #-0x40]
    // 0xb492b8: r16 = Instance_BoxFit
    //     0xb492b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb492bc: ldr             x16, [x16, #0x118]
    // 0xb492c0: r30 = 60.000000
    //     0xb492c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb492c4: ldr             lr, [lr, #0x110]
    // 0xb492c8: stp             lr, x16, [SP, #0x18]
    // 0xb492cc: r16 = 60.000000
    //     0xb492cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb492d0: ldr             x16, [x16, #0x110]
    // 0xb492d4: ldur            lr, [fp, #-0x30]
    // 0xb492d8: stp             lr, x16, [SP, #8]
    // 0xb492dc: ldur            x16, [fp, #-0x38]
    // 0xb492e0: str             x16, [SP]
    // 0xb492e4: mov             x1, x0
    // 0xb492e8: ldur            x2, [fp, #-0x18]
    // 0xb492ec: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb492ec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb492f0: ldr             x4, [x4, #0xae0]
    // 0xb492f4: r0 = CachedNetworkImage()
    //     0xb492f4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb492f8: r0 = ClipRRect()
    //     0xb492f8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb492fc: mov             x2, x0
    // 0xb49300: ldur            x0, [fp, #-0x28]
    // 0xb49304: stur            x2, [fp, #-0x30]
    // 0xb49308: StoreField: r2->field_f = r0
    //     0xb49308: stur            w0, [x2, #0xf]
    // 0xb4930c: r0 = Instance_Clip
    //     0xb4930c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb49310: ldr             x0, [x0, #0x138]
    // 0xb49314: ArrayStore: r2[0] = r0  ; List_4
    //     0xb49314: stur            w0, [x2, #0x17]
    // 0xb49318: ldur            x0, [fp, #-0x40]
    // 0xb4931c: StoreField: r2->field_b = r0
    //     0xb4931c: stur            w0, [x2, #0xb]
    // 0xb49320: ldur            x0, [fp, #-0x10]
    // 0xb49324: LoadField: r1 = r0->field_f
    //     0xb49324: ldur            w1, [x0, #0xf]
    // 0xb49328: DecompressPointer r1
    //     0xb49328: add             x1, x1, HEAP, lsl #32
    // 0xb4932c: cmp             w1, NULL
    // 0xb49330: b.ne            #0xb4933c
    // 0xb49334: r3 = ""
    //     0xb49334: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb49338: b               #0xb49340
    // 0xb4933c: mov             x3, x1
    // 0xb49340: ldur            x1, [fp, #-8]
    // 0xb49344: stur            x3, [fp, #-0x18]
    // 0xb49348: r0 = of()
    //     0xb49348: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4934c: LoadField: r1 = r0->field_87
    //     0xb4934c: ldur            w1, [x0, #0x87]
    // 0xb49350: DecompressPointer r1
    //     0xb49350: add             x1, x1, HEAP, lsl #32
    // 0xb49354: LoadField: r0 = r1->field_2b
    //     0xb49354: ldur            w0, [x1, #0x2b]
    // 0xb49358: DecompressPointer r0
    //     0xb49358: add             x0, x0, HEAP, lsl #32
    // 0xb4935c: stur            x0, [fp, #-0x28]
    // 0xb49360: r1 = Instance_Color
    //     0xb49360: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb49364: d0 = 0.700000
    //     0xb49364: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb49368: ldr             d0, [x17, #0xf48]
    // 0xb4936c: r0 = withOpacity()
    //     0xb4936c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb49370: r16 = 12.000000
    //     0xb49370: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb49374: ldr             x16, [x16, #0x9e8]
    // 0xb49378: stp             x0, x16, [SP]
    // 0xb4937c: ldur            x1, [fp, #-0x28]
    // 0xb49380: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb49380: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb49384: ldr             x4, [x4, #0xaa0]
    // 0xb49388: r0 = copyWith()
    //     0xb49388: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4938c: stur            x0, [fp, #-0x28]
    // 0xb49390: r0 = Text()
    //     0xb49390: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb49394: mov             x3, x0
    // 0xb49398: ldur            x0, [fp, #-0x18]
    // 0xb4939c: stur            x3, [fp, #-0x38]
    // 0xb493a0: StoreField: r3->field_b = r0
    //     0xb493a0: stur            w0, [x3, #0xb]
    // 0xb493a4: ldur            x0, [fp, #-0x28]
    // 0xb493a8: StoreField: r3->field_13 = r0
    //     0xb493a8: stur            w0, [x3, #0x13]
    // 0xb493ac: r0 = 4
    //     0xb493ac: movz            x0, #0x4
    // 0xb493b0: StoreField: r3->field_37 = r0
    //     0xb493b0: stur            w0, [x3, #0x37]
    // 0xb493b4: r1 = Null
    //     0xb493b4: mov             x1, NULL
    // 0xb493b8: r2 = 10
    //     0xb493b8: movz            x2, #0xa
    // 0xb493bc: r0 = AllocateArray()
    //     0xb493bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb493c0: r16 = "Size: "
    //     0xb493c0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb493c4: ldr             x16, [x16, #0xf00]
    // 0xb493c8: StoreField: r0->field_f = r16
    //     0xb493c8: stur            w16, [x0, #0xf]
    // 0xb493cc: ldur            x1, [fp, #-0x10]
    // 0xb493d0: LoadField: r2 = r1->field_2f
    //     0xb493d0: ldur            w2, [x1, #0x2f]
    // 0xb493d4: DecompressPointer r2
    //     0xb493d4: add             x2, x2, HEAP, lsl #32
    // 0xb493d8: StoreField: r0->field_13 = r2
    //     0xb493d8: stur            w2, [x0, #0x13]
    // 0xb493dc: r16 = " / Qty: "
    //     0xb493dc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb493e0: ldr             x16, [x16, #0x760]
    // 0xb493e4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb493e4: stur            w16, [x0, #0x17]
    // 0xb493e8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb493e8: ldur            w2, [x1, #0x17]
    // 0xb493ec: DecompressPointer r2
    //     0xb493ec: add             x2, x2, HEAP, lsl #32
    // 0xb493f0: StoreField: r0->field_1b = r2
    //     0xb493f0: stur            w2, [x0, #0x1b]
    // 0xb493f4: r16 = " "
    //     0xb493f4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb493f8: StoreField: r0->field_1f = r16
    //     0xb493f8: stur            w16, [x0, #0x1f]
    // 0xb493fc: str             x0, [SP]
    // 0xb49400: r0 = _interpolate()
    //     0xb49400: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb49404: ldur            x1, [fp, #-8]
    // 0xb49408: stur            x0, [fp, #-0x18]
    // 0xb4940c: r0 = of()
    //     0xb4940c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49410: LoadField: r1 = r0->field_87
    //     0xb49410: ldur            w1, [x0, #0x87]
    // 0xb49414: DecompressPointer r1
    //     0xb49414: add             x1, x1, HEAP, lsl #32
    // 0xb49418: LoadField: r0 = r1->field_7
    //     0xb49418: ldur            w0, [x1, #7]
    // 0xb4941c: DecompressPointer r0
    //     0xb4941c: add             x0, x0, HEAP, lsl #32
    // 0xb49420: r16 = 12.000000
    //     0xb49420: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb49424: ldr             x16, [x16, #0x9e8]
    // 0xb49428: r30 = Instance_Color
    //     0xb49428: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4942c: stp             lr, x16, [SP]
    // 0xb49430: mov             x1, x0
    // 0xb49434: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb49434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb49438: ldr             x4, [x4, #0xaa0]
    // 0xb4943c: r0 = copyWith()
    //     0xb4943c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb49440: stur            x0, [fp, #-0x28]
    // 0xb49444: r0 = Text()
    //     0xb49444: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb49448: mov             x1, x0
    // 0xb4944c: ldur            x0, [fp, #-0x18]
    // 0xb49450: stur            x1, [fp, #-0x40]
    // 0xb49454: StoreField: r1->field_b = r0
    //     0xb49454: stur            w0, [x1, #0xb]
    // 0xb49458: ldur            x0, [fp, #-0x28]
    // 0xb4945c: StoreField: r1->field_13 = r0
    //     0xb4945c: stur            w0, [x1, #0x13]
    // 0xb49460: r0 = Padding()
    //     0xb49460: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb49464: mov             x1, x0
    // 0xb49468: r0 = Instance_EdgeInsets
    //     0xb49468: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb4946c: ldr             x0, [x0, #0x990]
    // 0xb49470: stur            x1, [fp, #-0x18]
    // 0xb49474: StoreField: r1->field_f = r0
    //     0xb49474: stur            w0, [x1, #0xf]
    // 0xb49478: ldur            x0, [fp, #-0x40]
    // 0xb4947c: StoreField: r1->field_b = r0
    //     0xb4947c: stur            w0, [x1, #0xb]
    // 0xb49480: ldur            x0, [fp, #-0x10]
    // 0xb49484: LoadField: r2 = r0->field_1f
    //     0xb49484: ldur            w2, [x0, #0x1f]
    // 0xb49488: DecompressPointer r2
    //     0xb49488: add             x2, x2, HEAP, lsl #32
    // 0xb4948c: str             x2, [SP]
    // 0xb49490: r0 = _interpolateSingle()
    //     0xb49490: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb49494: ldur            x1, [fp, #-8]
    // 0xb49498: stur            x0, [fp, #-0x28]
    // 0xb4949c: r0 = of()
    //     0xb4949c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb494a0: LoadField: r1 = r0->field_87
    //     0xb494a0: ldur            w1, [x0, #0x87]
    // 0xb494a4: DecompressPointer r1
    //     0xb494a4: add             x1, x1, HEAP, lsl #32
    // 0xb494a8: LoadField: r0 = r1->field_7
    //     0xb494a8: ldur            w0, [x1, #7]
    // 0xb494ac: DecompressPointer r0
    //     0xb494ac: add             x0, x0, HEAP, lsl #32
    // 0xb494b0: r16 = 12.000000
    //     0xb494b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb494b4: ldr             x16, [x16, #0x9e8]
    // 0xb494b8: r30 = Instance_Color
    //     0xb494b8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb494bc: stp             lr, x16, [SP]
    // 0xb494c0: mov             x1, x0
    // 0xb494c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb494c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb494c8: ldr             x4, [x4, #0xaa0]
    // 0xb494cc: r0 = copyWith()
    //     0xb494cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb494d0: stur            x0, [fp, #-0x40]
    // 0xb494d4: r0 = Text()
    //     0xb494d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb494d8: mov             x1, x0
    // 0xb494dc: ldur            x0, [fp, #-0x28]
    // 0xb494e0: stur            x1, [fp, #-0x48]
    // 0xb494e4: StoreField: r1->field_b = r0
    //     0xb494e4: stur            w0, [x1, #0xb]
    // 0xb494e8: ldur            x0, [fp, #-0x40]
    // 0xb494ec: StoreField: r1->field_13 = r0
    //     0xb494ec: stur            w0, [x1, #0x13]
    // 0xb494f0: ldur            x0, [fp, #-0x10]
    // 0xb494f4: LoadField: r2 = r0->field_27
    //     0xb494f4: ldur            w2, [x0, #0x27]
    // 0xb494f8: DecompressPointer r2
    //     0xb494f8: add             x2, x2, HEAP, lsl #32
    // 0xb494fc: str             x2, [SP]
    // 0xb49500: r0 = _interpolateSingle()
    //     0xb49500: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb49504: ldur            x1, [fp, #-8]
    // 0xb49508: stur            x0, [fp, #-0x28]
    // 0xb4950c: r0 = of()
    //     0xb4950c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49510: LoadField: r1 = r0->field_87
    //     0xb49510: ldur            w1, [x0, #0x87]
    // 0xb49514: DecompressPointer r1
    //     0xb49514: add             x1, x1, HEAP, lsl #32
    // 0xb49518: LoadField: r0 = r1->field_7
    //     0xb49518: ldur            w0, [x1, #7]
    // 0xb4951c: DecompressPointer r0
    //     0xb4951c: add             x0, x0, HEAP, lsl #32
    // 0xb49520: stur            x0, [fp, #-0x40]
    // 0xb49524: r1 = Instance_Color
    //     0xb49524: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb49528: d0 = 0.400000
    //     0xb49528: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4952c: r0 = withOpacity()
    //     0xb4952c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb49530: r16 = Instance_TextDecoration
    //     0xb49530: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb49534: ldr             x16, [x16, #0xe30]
    // 0xb49538: r30 = 12.000000
    //     0xb49538: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4953c: ldr             lr, [lr, #0x9e8]
    // 0xb49540: stp             lr, x16, [SP, #8]
    // 0xb49544: str             x0, [SP]
    // 0xb49548: ldur            x1, [fp, #-0x40]
    // 0xb4954c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb4954c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb49550: ldr             x4, [x4, #0xb60]
    // 0xb49554: r0 = copyWith()
    //     0xb49554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb49558: stur            x0, [fp, #-0x40]
    // 0xb4955c: r0 = Text()
    //     0xb4955c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb49560: mov             x3, x0
    // 0xb49564: ldur            x0, [fp, #-0x28]
    // 0xb49568: stur            x3, [fp, #-0x50]
    // 0xb4956c: StoreField: r3->field_b = r0
    //     0xb4956c: stur            w0, [x3, #0xb]
    // 0xb49570: ldur            x0, [fp, #-0x40]
    // 0xb49574: StoreField: r3->field_13 = r0
    //     0xb49574: stur            w0, [x3, #0x13]
    // 0xb49578: r1 = Null
    //     0xb49578: mov             x1, NULL
    // 0xb4957c: r2 = 6
    //     0xb4957c: movz            x2, #0x6
    // 0xb49580: r0 = AllocateArray()
    //     0xb49580: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb49584: mov             x2, x0
    // 0xb49588: ldur            x0, [fp, #-0x48]
    // 0xb4958c: stur            x2, [fp, #-0x28]
    // 0xb49590: StoreField: r2->field_f = r0
    //     0xb49590: stur            w0, [x2, #0xf]
    // 0xb49594: r16 = Instance_SizedBox
    //     0xb49594: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb49598: ldr             x16, [x16, #0xb20]
    // 0xb4959c: StoreField: r2->field_13 = r16
    //     0xb4959c: stur            w16, [x2, #0x13]
    // 0xb495a0: ldur            x0, [fp, #-0x50]
    // 0xb495a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb495a4: stur            w0, [x2, #0x17]
    // 0xb495a8: r1 = <Widget>
    //     0xb495a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb495ac: r0 = AllocateGrowableArray()
    //     0xb495ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb495b0: mov             x1, x0
    // 0xb495b4: ldur            x0, [fp, #-0x28]
    // 0xb495b8: stur            x1, [fp, #-0x40]
    // 0xb495bc: StoreField: r1->field_f = r0
    //     0xb495bc: stur            w0, [x1, #0xf]
    // 0xb495c0: r2 = 6
    //     0xb495c0: movz            x2, #0x6
    // 0xb495c4: StoreField: r1->field_b = r2
    //     0xb495c4: stur            w2, [x1, #0xb]
    // 0xb495c8: r0 = Row()
    //     0xb495c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb495cc: mov             x1, x0
    // 0xb495d0: r0 = Instance_Axis
    //     0xb495d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb495d4: stur            x1, [fp, #-0x28]
    // 0xb495d8: StoreField: r1->field_f = r0
    //     0xb495d8: stur            w0, [x1, #0xf]
    // 0xb495dc: r2 = Instance_MainAxisAlignment
    //     0xb495dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb495e0: ldr             x2, [x2, #0xa08]
    // 0xb495e4: StoreField: r1->field_13 = r2
    //     0xb495e4: stur            w2, [x1, #0x13]
    // 0xb495e8: r3 = Instance_MainAxisSize
    //     0xb495e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb495ec: ldr             x3, [x3, #0xa10]
    // 0xb495f0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb495f0: stur            w3, [x1, #0x17]
    // 0xb495f4: r4 = Instance_CrossAxisAlignment
    //     0xb495f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb495f8: ldr             x4, [x4, #0xa18]
    // 0xb495fc: StoreField: r1->field_1b = r4
    //     0xb495fc: stur            w4, [x1, #0x1b]
    // 0xb49600: r5 = Instance_VerticalDirection
    //     0xb49600: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb49604: ldr             x5, [x5, #0xa20]
    // 0xb49608: StoreField: r1->field_23 = r5
    //     0xb49608: stur            w5, [x1, #0x23]
    // 0xb4960c: r6 = Instance_Clip
    //     0xb4960c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb49610: ldr             x6, [x6, #0x38]
    // 0xb49614: StoreField: r1->field_2b = r6
    //     0xb49614: stur            w6, [x1, #0x2b]
    // 0xb49618: StoreField: r1->field_2f = rZR
    //     0xb49618: stur            xzr, [x1, #0x2f]
    // 0xb4961c: ldur            x7, [fp, #-0x40]
    // 0xb49620: StoreField: r1->field_b = r7
    //     0xb49620: stur            w7, [x1, #0xb]
    // 0xb49624: r0 = Padding()
    //     0xb49624: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb49628: mov             x3, x0
    // 0xb4962c: r0 = Instance_EdgeInsets
    //     0xb4962c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb49630: ldr             x0, [x0, #0x770]
    // 0xb49634: stur            x3, [fp, #-0x40]
    // 0xb49638: StoreField: r3->field_f = r0
    //     0xb49638: stur            w0, [x3, #0xf]
    // 0xb4963c: ldur            x0, [fp, #-0x28]
    // 0xb49640: StoreField: r3->field_b = r0
    //     0xb49640: stur            w0, [x3, #0xb]
    // 0xb49644: r1 = Null
    //     0xb49644: mov             x1, NULL
    // 0xb49648: r2 = 6
    //     0xb49648: movz            x2, #0x6
    // 0xb4964c: r0 = AllocateArray()
    //     0xb4964c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb49650: mov             x2, x0
    // 0xb49654: ldur            x0, [fp, #-0x38]
    // 0xb49658: stur            x2, [fp, #-0x28]
    // 0xb4965c: StoreField: r2->field_f = r0
    //     0xb4965c: stur            w0, [x2, #0xf]
    // 0xb49660: ldur            x0, [fp, #-0x18]
    // 0xb49664: StoreField: r2->field_13 = r0
    //     0xb49664: stur            w0, [x2, #0x13]
    // 0xb49668: ldur            x0, [fp, #-0x40]
    // 0xb4966c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4966c: stur            w0, [x2, #0x17]
    // 0xb49670: r1 = <Widget>
    //     0xb49670: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb49674: r0 = AllocateGrowableArray()
    //     0xb49674: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb49678: mov             x1, x0
    // 0xb4967c: ldur            x0, [fp, #-0x28]
    // 0xb49680: stur            x1, [fp, #-0x18]
    // 0xb49684: StoreField: r1->field_f = r0
    //     0xb49684: stur            w0, [x1, #0xf]
    // 0xb49688: r0 = 6
    //     0xb49688: movz            x0, #0x6
    // 0xb4968c: StoreField: r1->field_b = r0
    //     0xb4968c: stur            w0, [x1, #0xb]
    // 0xb49690: r0 = Column()
    //     0xb49690: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb49694: mov             x1, x0
    // 0xb49698: r0 = Instance_Axis
    //     0xb49698: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4969c: stur            x1, [fp, #-0x28]
    // 0xb496a0: StoreField: r1->field_f = r0
    //     0xb496a0: stur            w0, [x1, #0xf]
    // 0xb496a4: r2 = Instance_MainAxisAlignment
    //     0xb496a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb496a8: ldr             x2, [x2, #0xa08]
    // 0xb496ac: StoreField: r1->field_13 = r2
    //     0xb496ac: stur            w2, [x1, #0x13]
    // 0xb496b0: r3 = Instance_MainAxisSize
    //     0xb496b0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb496b4: ldr             x3, [x3, #0xa10]
    // 0xb496b8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb496b8: stur            w3, [x1, #0x17]
    // 0xb496bc: r4 = Instance_CrossAxisAlignment
    //     0xb496bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb496c0: ldr             x4, [x4, #0x890]
    // 0xb496c4: StoreField: r1->field_1b = r4
    //     0xb496c4: stur            w4, [x1, #0x1b]
    // 0xb496c8: r5 = Instance_VerticalDirection
    //     0xb496c8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb496cc: ldr             x5, [x5, #0xa20]
    // 0xb496d0: StoreField: r1->field_23 = r5
    //     0xb496d0: stur            w5, [x1, #0x23]
    // 0xb496d4: r6 = Instance_Clip
    //     0xb496d4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb496d8: ldr             x6, [x6, #0x38]
    // 0xb496dc: StoreField: r1->field_2b = r6
    //     0xb496dc: stur            w6, [x1, #0x2b]
    // 0xb496e0: StoreField: r1->field_2f = rZR
    //     0xb496e0: stur            xzr, [x1, #0x2f]
    // 0xb496e4: ldur            x7, [fp, #-0x18]
    // 0xb496e8: StoreField: r1->field_b = r7
    //     0xb496e8: stur            w7, [x1, #0xb]
    // 0xb496ec: r0 = Padding()
    //     0xb496ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb496f0: mov             x2, x0
    // 0xb496f4: r0 = Instance_EdgeInsets
    //     0xb496f4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb496f8: ldr             x0, [x0, #0xa78]
    // 0xb496fc: stur            x2, [fp, #-0x18]
    // 0xb49700: StoreField: r2->field_f = r0
    //     0xb49700: stur            w0, [x2, #0xf]
    // 0xb49704: ldur            x0, [fp, #-0x28]
    // 0xb49708: StoreField: r2->field_b = r0
    //     0xb49708: stur            w0, [x2, #0xb]
    // 0xb4970c: r1 = <FlexParentData>
    //     0xb4970c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb49710: ldr             x1, [x1, #0xe00]
    // 0xb49714: r0 = Expanded()
    //     0xb49714: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb49718: mov             x3, x0
    // 0xb4971c: r0 = 1
    //     0xb4971c: movz            x0, #0x1
    // 0xb49720: stur            x3, [fp, #-0x28]
    // 0xb49724: StoreField: r3->field_13 = r0
    //     0xb49724: stur            x0, [x3, #0x13]
    // 0xb49728: r0 = Instance_FlexFit
    //     0xb49728: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4972c: ldr             x0, [x0, #0xe08]
    // 0xb49730: StoreField: r3->field_1b = r0
    //     0xb49730: stur            w0, [x3, #0x1b]
    // 0xb49734: ldur            x0, [fp, #-0x18]
    // 0xb49738: StoreField: r3->field_b = r0
    //     0xb49738: stur            w0, [x3, #0xb]
    // 0xb4973c: r1 = Null
    //     0xb4973c: mov             x1, NULL
    // 0xb49740: r2 = 4
    //     0xb49740: movz            x2, #0x4
    // 0xb49744: r0 = AllocateArray()
    //     0xb49744: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb49748: mov             x2, x0
    // 0xb4974c: ldur            x0, [fp, #-0x30]
    // 0xb49750: stur            x2, [fp, #-0x18]
    // 0xb49754: StoreField: r2->field_f = r0
    //     0xb49754: stur            w0, [x2, #0xf]
    // 0xb49758: ldur            x0, [fp, #-0x28]
    // 0xb4975c: StoreField: r2->field_13 = r0
    //     0xb4975c: stur            w0, [x2, #0x13]
    // 0xb49760: r1 = <Widget>
    //     0xb49760: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb49764: r0 = AllocateGrowableArray()
    //     0xb49764: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb49768: mov             x1, x0
    // 0xb4976c: ldur            x0, [fp, #-0x18]
    // 0xb49770: stur            x1, [fp, #-0x28]
    // 0xb49774: StoreField: r1->field_f = r0
    //     0xb49774: stur            w0, [x1, #0xf]
    // 0xb49778: r0 = 4
    //     0xb49778: movz            x0, #0x4
    // 0xb4977c: StoreField: r1->field_b = r0
    //     0xb4977c: stur            w0, [x1, #0xb]
    // 0xb49780: r0 = Row()
    //     0xb49780: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb49784: mov             x1, x0
    // 0xb49788: r0 = Instance_Axis
    //     0xb49788: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4978c: stur            x1, [fp, #-0x18]
    // 0xb49790: StoreField: r1->field_f = r0
    //     0xb49790: stur            w0, [x1, #0xf]
    // 0xb49794: r0 = Instance_MainAxisAlignment
    //     0xb49794: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb49798: ldr             x0, [x0, #0xa08]
    // 0xb4979c: StoreField: r1->field_13 = r0
    //     0xb4979c: stur            w0, [x1, #0x13]
    // 0xb497a0: r2 = Instance_MainAxisSize
    //     0xb497a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb497a4: ldr             x2, [x2, #0xa10]
    // 0xb497a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb497a8: stur            w2, [x1, #0x17]
    // 0xb497ac: r3 = Instance_CrossAxisAlignment
    //     0xb497ac: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb497b0: ldr             x3, [x3, #0x890]
    // 0xb497b4: StoreField: r1->field_1b = r3
    //     0xb497b4: stur            w3, [x1, #0x1b]
    // 0xb497b8: r3 = Instance_VerticalDirection
    //     0xb497b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb497bc: ldr             x3, [x3, #0xa20]
    // 0xb497c0: StoreField: r1->field_23 = r3
    //     0xb497c0: stur            w3, [x1, #0x23]
    // 0xb497c4: r4 = Instance_Clip
    //     0xb497c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb497c8: ldr             x4, [x4, #0x38]
    // 0xb497cc: StoreField: r1->field_2b = r4
    //     0xb497cc: stur            w4, [x1, #0x2b]
    // 0xb497d0: StoreField: r1->field_2f = rZR
    //     0xb497d0: stur            xzr, [x1, #0x2f]
    // 0xb497d4: ldur            x5, [fp, #-0x28]
    // 0xb497d8: StoreField: r1->field_b = r5
    //     0xb497d8: stur            w5, [x1, #0xb]
    // 0xb497dc: r0 = Padding()
    //     0xb497dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb497e0: mov             x1, x0
    // 0xb497e4: r0 = Instance_EdgeInsets
    //     0xb497e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb497e8: ldr             x0, [x0, #0x1f0]
    // 0xb497ec: stur            x1, [fp, #-0x28]
    // 0xb497f0: StoreField: r1->field_f = r0
    //     0xb497f0: stur            w0, [x1, #0xf]
    // 0xb497f4: ldur            x0, [fp, #-0x18]
    // 0xb497f8: StoreField: r1->field_b = r0
    //     0xb497f8: stur            w0, [x1, #0xb]
    // 0xb497fc: r0 = Container()
    //     0xb497fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb49800: stur            x0, [fp, #-0x18]
    // 0xb49804: ldur            x16, [fp, #-0x20]
    // 0xb49808: ldur            lr, [fp, #-0x28]
    // 0xb4980c: stp             lr, x16, [SP]
    // 0xb49810: mov             x1, x0
    // 0xb49814: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb49814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb49818: ldr             x4, [x4, #0x88]
    // 0xb4981c: r0 = Container()
    //     0xb4981c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb49820: ldur            x0, [fp, #-0x10]
    // 0xb49824: LoadField: r1 = r0->field_2b
    //     0xb49824: ldur            w1, [x0, #0x2b]
    // 0xb49828: DecompressPointer r1
    //     0xb49828: add             x1, x1, HEAP, lsl #32
    // 0xb4982c: str             x1, [SP]
    // 0xb49830: r0 = _interpolateSingle()
    //     0xb49830: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb49834: ldur            x1, [fp, #-8]
    // 0xb49838: stur            x0, [fp, #-8]
    // 0xb4983c: r0 = of()
    //     0xb4983c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49840: LoadField: r1 = r0->field_87
    //     0xb49840: ldur            w1, [x0, #0x87]
    // 0xb49844: DecompressPointer r1
    //     0xb49844: add             x1, x1, HEAP, lsl #32
    // 0xb49848: LoadField: r0 = r1->field_7
    //     0xb49848: ldur            w0, [x1, #7]
    // 0xb4984c: DecompressPointer r0
    //     0xb4984c: add             x0, x0, HEAP, lsl #32
    // 0xb49850: stur            x0, [fp, #-0x10]
    // 0xb49854: r1 = Instance_Color
    //     0xb49854: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb49858: d0 = 0.400000
    //     0xb49858: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4985c: r0 = withOpacity()
    //     0xb4985c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb49860: r16 = 14.000000
    //     0xb49860: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb49864: ldr             x16, [x16, #0x1d8]
    // 0xb49868: stp             x0, x16, [SP]
    // 0xb4986c: ldur            x1, [fp, #-0x10]
    // 0xb49870: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb49870: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb49874: ldr             x4, [x4, #0xaa0]
    // 0xb49878: r0 = copyWith()
    //     0xb49878: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4987c: stur            x0, [fp, #-0x10]
    // 0xb49880: r0 = Text()
    //     0xb49880: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb49884: mov             x1, x0
    // 0xb49888: ldur            x0, [fp, #-8]
    // 0xb4988c: stur            x1, [fp, #-0x20]
    // 0xb49890: StoreField: r1->field_b = r0
    //     0xb49890: stur            w0, [x1, #0xb]
    // 0xb49894: ldur            x0, [fp, #-0x10]
    // 0xb49898: StoreField: r1->field_13 = r0
    //     0xb49898: stur            w0, [x1, #0x13]
    // 0xb4989c: r0 = Align()
    //     0xb4989c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb498a0: mov             x3, x0
    // 0xb498a4: r0 = Instance_Alignment
    //     0xb498a4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xb498a8: ldr             x0, [x0, #0xf98]
    // 0xb498ac: stur            x3, [fp, #-8]
    // 0xb498b0: StoreField: r3->field_f = r0
    //     0xb498b0: stur            w0, [x3, #0xf]
    // 0xb498b4: ldur            x0, [fp, #-0x20]
    // 0xb498b8: StoreField: r3->field_b = r0
    //     0xb498b8: stur            w0, [x3, #0xb]
    // 0xb498bc: r1 = Null
    //     0xb498bc: mov             x1, NULL
    // 0xb498c0: r2 = 8
    //     0xb498c0: movz            x2, #0x8
    // 0xb498c4: r0 = AllocateArray()
    //     0xb498c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb498c8: mov             x2, x0
    // 0xb498cc: ldur            x0, [fp, #-0x18]
    // 0xb498d0: stur            x2, [fp, #-0x10]
    // 0xb498d4: StoreField: r2->field_f = r0
    //     0xb498d4: stur            w0, [x2, #0xf]
    // 0xb498d8: r16 = Instance_SizedBox
    //     0xb498d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb498dc: ldr             x16, [x16, #0x8b8]
    // 0xb498e0: StoreField: r2->field_13 = r16
    //     0xb498e0: stur            w16, [x2, #0x13]
    // 0xb498e4: ldur            x0, [fp, #-8]
    // 0xb498e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb498e8: stur            w0, [x2, #0x17]
    // 0xb498ec: r16 = Instance_SizedBox
    //     0xb498ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb498f0: ldr             x16, [x16, #0x8f0]
    // 0xb498f4: StoreField: r2->field_1b = r16
    //     0xb498f4: stur            w16, [x2, #0x1b]
    // 0xb498f8: r1 = <Widget>
    //     0xb498f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb498fc: r0 = AllocateGrowableArray()
    //     0xb498fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb49900: mov             x1, x0
    // 0xb49904: ldur            x0, [fp, #-0x10]
    // 0xb49908: stur            x1, [fp, #-8]
    // 0xb4990c: StoreField: r1->field_f = r0
    //     0xb4990c: stur            w0, [x1, #0xf]
    // 0xb49910: r0 = 8
    //     0xb49910: movz            x0, #0x8
    // 0xb49914: StoreField: r1->field_b = r0
    //     0xb49914: stur            w0, [x1, #0xb]
    // 0xb49918: r0 = Column()
    //     0xb49918: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4991c: r1 = Instance_Axis
    //     0xb4991c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb49920: StoreField: r0->field_f = r1
    //     0xb49920: stur            w1, [x0, #0xf]
    // 0xb49924: r1 = Instance_MainAxisAlignment
    //     0xb49924: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb49928: ldr             x1, [x1, #0xa08]
    // 0xb4992c: StoreField: r0->field_13 = r1
    //     0xb4992c: stur            w1, [x0, #0x13]
    // 0xb49930: r1 = Instance_MainAxisSize
    //     0xb49930: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb49934: ldr             x1, [x1, #0xa10]
    // 0xb49938: ArrayStore: r0[0] = r1  ; List_4
    //     0xb49938: stur            w1, [x0, #0x17]
    // 0xb4993c: r1 = Instance_CrossAxisAlignment
    //     0xb4993c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb49940: ldr             x1, [x1, #0xa18]
    // 0xb49944: StoreField: r0->field_1b = r1
    //     0xb49944: stur            w1, [x0, #0x1b]
    // 0xb49948: r1 = Instance_VerticalDirection
    //     0xb49948: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4994c: ldr             x1, [x1, #0xa20]
    // 0xb49950: StoreField: r0->field_23 = r1
    //     0xb49950: stur            w1, [x0, #0x23]
    // 0xb49954: r1 = Instance_Clip
    //     0xb49954: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb49958: ldr             x1, [x1, #0x38]
    // 0xb4995c: StoreField: r0->field_2b = r1
    //     0xb4995c: stur            w1, [x0, #0x2b]
    // 0xb49960: StoreField: r0->field_2f = rZR
    //     0xb49960: stur            xzr, [x0, #0x2f]
    // 0xb49964: ldur            x1, [fp, #-8]
    // 0xb49968: StoreField: r0->field_b = r1
    //     0xb49968: stur            w1, [x0, #0xb]
    // 0xb4996c: LeaveFrame
    //     0xb4996c: mov             SP, fp
    //     0xb49970: ldp             fp, lr, [SP], #0x10
    // 0xb49974: ret
    //     0xb49974: ret             
    // 0xb49978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb49978: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4997c: b               #0xb491b4
  }
}

// class id: 3361, size: 0x14, field offset: 0x14
class _DuplicateOrderConfirmBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb486ac, size: 0x888
    // 0xb486ac: EnterFrame
    //     0xb486ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb486b0: mov             fp, SP
    // 0xb486b4: AllocStack(0x78)
    //     0xb486b4: sub             SP, SP, #0x78
    // 0xb486b8: SetupParameters(_DuplicateOrderConfirmBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb486b8: mov             x0, x1
    //     0xb486bc: stur            x1, [fp, #-8]
    //     0xb486c0: mov             x1, x2
    //     0xb486c4: stur            x2, [fp, #-0x10]
    // 0xb486c8: CheckStackOverflow
    //     0xb486c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb486cc: cmp             SP, x16
    //     0xb486d0: b.ls            #0xb48f20
    // 0xb486d4: r1 = 1
    //     0xb486d4: movz            x1, #0x1
    // 0xb486d8: r0 = AllocateContext()
    //     0xb486d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb486dc: mov             x1, x0
    // 0xb486e0: ldur            x0, [fp, #-8]
    // 0xb486e4: stur            x1, [fp, #-0x18]
    // 0xb486e8: StoreField: r1->field_f = r0
    //     0xb486e8: stur            w0, [x1, #0xf]
    // 0xb486ec: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xb486ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb486f0: ldr             x0, [x0, #0x1ab0]
    //     0xb486f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb486f8: cmp             w0, w16
    //     0xb486fc: b.ne            #0xb4870c
    //     0xb48700: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xb48704: ldr             x2, [x2, #0x60]
    //     0xb48708: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4870c: LoadField: r1 = r0->field_87
    //     0xb4870c: ldur            w1, [x0, #0x87]
    // 0xb48710: DecompressPointer r1
    //     0xb48710: add             x1, x1, HEAP, lsl #32
    // 0xb48714: LoadField: r0 = r1->field_7
    //     0xb48714: ldur            w0, [x1, #7]
    // 0xb48718: DecompressPointer r0
    //     0xb48718: add             x0, x0, HEAP, lsl #32
    // 0xb4871c: stur            x0, [fp, #-0x20]
    // 0xb48720: r1 = Instance_Color
    //     0xb48720: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb48724: d0 = 0.700000
    //     0xb48724: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb48728: ldr             d0, [x17, #0xf48]
    // 0xb4872c: r0 = withOpacity()
    //     0xb4872c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb48730: r16 = 14.000000
    //     0xb48730: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb48734: ldr             x16, [x16, #0x1d8]
    // 0xb48738: stp             x16, x0, [SP]
    // 0xb4873c: ldur            x1, [fp, #-0x20]
    // 0xb48740: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb48740: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb48744: ldr             x4, [x4, #0x9b8]
    // 0xb48748: r0 = copyWith()
    //     0xb48748: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4874c: stur            x0, [fp, #-0x28]
    // 0xb48750: r0 = Text()
    //     0xb48750: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb48754: mov             x1, x0
    // 0xb48758: r0 = "You have Already Ordered The Following Products!"
    //     0xb48758: add             x0, PP, #0x54, lsl #12  ; [pp+0x546f8] "You have Already Ordered The Following Products!"
    //     0xb4875c: ldr             x0, [x0, #0x6f8]
    // 0xb48760: stur            x1, [fp, #-0x30]
    // 0xb48764: StoreField: r1->field_b = r0
    //     0xb48764: stur            w0, [x1, #0xb]
    // 0xb48768: ldur            x0, [fp, #-0x28]
    // 0xb4876c: StoreField: r1->field_13 = r0
    //     0xb4876c: stur            w0, [x1, #0x13]
    // 0xb48770: r0 = Center()
    //     0xb48770: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb48774: mov             x1, x0
    // 0xb48778: r0 = Instance_Alignment
    //     0xb48778: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb4877c: ldr             x0, [x0, #0xb10]
    // 0xb48780: stur            x1, [fp, #-0x28]
    // 0xb48784: StoreField: r1->field_f = r0
    //     0xb48784: stur            w0, [x1, #0xf]
    // 0xb48788: ldur            x0, [fp, #-0x30]
    // 0xb4878c: StoreField: r1->field_b = r0
    //     0xb4878c: stur            w0, [x1, #0xb]
    // 0xb48790: r0 = Padding()
    //     0xb48790: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb48794: mov             x3, x0
    // 0xb48798: r0 = Instance_EdgeInsets
    //     0xb48798: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb4879c: ldr             x0, [x0, #0xa00]
    // 0xb487a0: stur            x3, [fp, #-0x30]
    // 0xb487a4: StoreField: r3->field_f = r0
    //     0xb487a4: stur            w0, [x3, #0xf]
    // 0xb487a8: ldur            x1, [fp, #-0x28]
    // 0xb487ac: StoreField: r3->field_b = r1
    //     0xb487ac: stur            w1, [x3, #0xb]
    // 0xb487b0: ldur            x4, [fp, #-8]
    // 0xb487b4: LoadField: r1 = r4->field_b
    //     0xb487b4: ldur            w1, [x4, #0xb]
    // 0xb487b8: DecompressPointer r1
    //     0xb487b8: add             x1, x1, HEAP, lsl #32
    // 0xb487bc: cmp             w1, NULL
    // 0xb487c0: b.eq            #0xb48f28
    // 0xb487c4: LoadField: r2 = r1->field_b
    //     0xb487c4: ldur            w2, [x1, #0xb]
    // 0xb487c8: DecompressPointer r2
    //     0xb487c8: add             x2, x2, HEAP, lsl #32
    // 0xb487cc: LoadField: r1 = r2->field_7
    //     0xb487cc: ldur            w1, [x2, #7]
    // 0xb487d0: DecompressPointer r1
    //     0xb487d0: add             x1, x1, HEAP, lsl #32
    // 0xb487d4: cmp             w1, NULL
    // 0xb487d8: b.ne            #0xb487e4
    // 0xb487dc: r1 = Null
    //     0xb487dc: mov             x1, NULL
    // 0xb487e0: b               #0xb487ec
    // 0xb487e4: LoadField: r2 = r1->field_b
    //     0xb487e4: ldur            w2, [x1, #0xb]
    // 0xb487e8: mov             x1, x2
    // 0xb487ec: cmp             w1, NULL
    // 0xb487f0: b.ne            #0xb487fc
    // 0xb487f4: r1 = 0
    //     0xb487f4: movz            x1, #0
    // 0xb487f8: b               #0xb48804
    // 0xb487fc: r2 = LoadInt32Instr(r1)
    //     0xb487fc: sbfx            x2, x1, #1, #0x1f
    // 0xb48800: mov             x1, x2
    // 0xb48804: lsl             x5, x1, #1
    // 0xb48808: ldur            x2, [fp, #-0x18]
    // 0xb4880c: stur            x5, [fp, #-0x28]
    // 0xb48810: r1 = Function '<anonymous closure>':.
    //     0xb48810: add             x1, PP, #0x56, lsl #12  ; [pp+0x56dc0] AnonymousClosure: (0xb490c0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xb486ac)
    //     0xb48814: ldr             x1, [x1, #0xdc0]
    // 0xb48818: r0 = AllocateClosure()
    //     0xb48818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4881c: stur            x0, [fp, #-0x38]
    // 0xb48820: r0 = ListView()
    //     0xb48820: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb48824: stur            x0, [fp, #-0x40]
    // 0xb48828: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb48828: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb4882c: ldr             x16, [x16, #0x1c8]
    // 0xb48830: r30 = true
    //     0xb48830: add             lr, NULL, #0x20  ; true
    // 0xb48834: stp             lr, x16, [SP]
    // 0xb48838: mov             x1, x0
    // 0xb4883c: ldur            x2, [fp, #-0x38]
    // 0xb48840: ldur            x3, [fp, #-0x28]
    // 0xb48844: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb48844: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb48848: ldr             x4, [x4, #0xd18]
    // 0xb4884c: r0 = ListView.builder()
    //     0xb4884c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb48850: r0 = Padding()
    //     0xb48850: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb48854: mov             x1, x0
    // 0xb48858: r0 = Instance_EdgeInsets
    //     0xb48858: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb4885c: ldr             x0, [x0, #0xa00]
    // 0xb48860: stur            x1, [fp, #-0x28]
    // 0xb48864: StoreField: r1->field_f = r0
    //     0xb48864: stur            w0, [x1, #0xf]
    // 0xb48868: ldur            x0, [fp, #-0x40]
    // 0xb4886c: StoreField: r1->field_b = r0
    //     0xb4886c: stur            w0, [x1, #0xb]
    // 0xb48870: r16 = <EdgeInsets>
    //     0xb48870: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb48874: ldr             x16, [x16, #0xda0]
    // 0xb48878: r30 = Instance_EdgeInsets
    //     0xb48878: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4887c: ldr             lr, [lr, #0x1f0]
    // 0xb48880: stp             lr, x16, [SP]
    // 0xb48884: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb48884: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb48888: r0 = all()
    //     0xb48888: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4888c: ldur            x1, [fp, #-0x10]
    // 0xb48890: stur            x0, [fp, #-0x38]
    // 0xb48894: r0 = of()
    //     0xb48894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb48898: LoadField: r1 = r0->field_5b
    //     0xb48898: ldur            w1, [x0, #0x5b]
    // 0xb4889c: DecompressPointer r1
    //     0xb4889c: add             x1, x1, HEAP, lsl #32
    // 0xb488a0: stur            x1, [fp, #-0x40]
    // 0xb488a4: r0 = BorderSide()
    //     0xb488a4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb488a8: mov             x1, x0
    // 0xb488ac: ldur            x0, [fp, #-0x40]
    // 0xb488b0: stur            x1, [fp, #-0x48]
    // 0xb488b4: StoreField: r1->field_7 = r0
    //     0xb488b4: stur            w0, [x1, #7]
    // 0xb488b8: d0 = 1.000000
    //     0xb488b8: fmov            d0, #1.00000000
    // 0xb488bc: StoreField: r1->field_b = d0
    //     0xb488bc: stur            d0, [x1, #0xb]
    // 0xb488c0: r0 = Instance_BorderStyle
    //     0xb488c0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb488c4: ldr             x0, [x0, #0xf68]
    // 0xb488c8: StoreField: r1->field_13 = r0
    //     0xb488c8: stur            w0, [x1, #0x13]
    // 0xb488cc: d1 = -1.000000
    //     0xb488cc: fmov            d1, #-1.00000000
    // 0xb488d0: ArrayStore: r1[0] = d1  ; List_8
    //     0xb488d0: stur            d1, [x1, #0x17]
    // 0xb488d4: r0 = Radius()
    //     0xb488d4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb488d8: d0 = 20.000000
    //     0xb488d8: fmov            d0, #20.00000000
    // 0xb488dc: stur            x0, [fp, #-0x40]
    // 0xb488e0: StoreField: r0->field_7 = d0
    //     0xb488e0: stur            d0, [x0, #7]
    // 0xb488e4: StoreField: r0->field_f = d0
    //     0xb488e4: stur            d0, [x0, #0xf]
    // 0xb488e8: r0 = BorderRadius()
    //     0xb488e8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb488ec: mov             x1, x0
    // 0xb488f0: ldur            x0, [fp, #-0x40]
    // 0xb488f4: stur            x1, [fp, #-0x50]
    // 0xb488f8: StoreField: r1->field_7 = r0
    //     0xb488f8: stur            w0, [x1, #7]
    // 0xb488fc: StoreField: r1->field_b = r0
    //     0xb488fc: stur            w0, [x1, #0xb]
    // 0xb48900: StoreField: r1->field_f = r0
    //     0xb48900: stur            w0, [x1, #0xf]
    // 0xb48904: StoreField: r1->field_13 = r0
    //     0xb48904: stur            w0, [x1, #0x13]
    // 0xb48908: r0 = RoundedRectangleBorder()
    //     0xb48908: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb4890c: mov             x1, x0
    // 0xb48910: ldur            x0, [fp, #-0x50]
    // 0xb48914: StoreField: r1->field_b = r0
    //     0xb48914: stur            w0, [x1, #0xb]
    // 0xb48918: ldur            x0, [fp, #-0x48]
    // 0xb4891c: StoreField: r1->field_7 = r0
    //     0xb4891c: stur            w0, [x1, #7]
    // 0xb48920: r16 = <RoundedRectangleBorder>
    //     0xb48920: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb48924: ldr             x16, [x16, #0xf78]
    // 0xb48928: stp             x1, x16, [SP]
    // 0xb4892c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4892c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb48930: r0 = all()
    //     0xb48930: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb48934: stur            x0, [fp, #-0x40]
    // 0xb48938: r0 = ButtonStyle()
    //     0xb48938: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb4893c: mov             x1, x0
    // 0xb48940: ldur            x0, [fp, #-0x38]
    // 0xb48944: stur            x1, [fp, #-0x48]
    // 0xb48948: StoreField: r1->field_23 = r0
    //     0xb48948: stur            w0, [x1, #0x23]
    // 0xb4894c: ldur            x0, [fp, #-0x40]
    // 0xb48950: StoreField: r1->field_43 = r0
    //     0xb48950: stur            w0, [x1, #0x43]
    // 0xb48954: r0 = TextButtonThemeData()
    //     0xb48954: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb48958: mov             x1, x0
    // 0xb4895c: ldur            x0, [fp, #-0x48]
    // 0xb48960: stur            x1, [fp, #-0x38]
    // 0xb48964: StoreField: r1->field_7 = r0
    //     0xb48964: stur            w0, [x1, #7]
    // 0xb48968: ldur            x0, [fp, #-8]
    // 0xb4896c: LoadField: r2 = r0->field_b
    //     0xb4896c: ldur            w2, [x0, #0xb]
    // 0xb48970: DecompressPointer r2
    //     0xb48970: add             x2, x2, HEAP, lsl #32
    // 0xb48974: cmp             w2, NULL
    // 0xb48978: b.eq            #0xb48f2c
    // 0xb4897c: LoadField: r3 = r2->field_1b
    //     0xb4897c: ldur            w3, [x2, #0x1b]
    // 0xb48980: DecompressPointer r3
    //     0xb48980: add             x3, x3, HEAP, lsl #32
    // 0xb48984: LoadField: r2 = r3->field_3f
    //     0xb48984: ldur            w2, [x3, #0x3f]
    // 0xb48988: DecompressPointer r2
    //     0xb48988: add             x2, x2, HEAP, lsl #32
    // 0xb4898c: cmp             w2, NULL
    // 0xb48990: b.ne            #0xb4899c
    // 0xb48994: r3 = Null
    //     0xb48994: mov             x3, NULL
    // 0xb48998: b               #0xb489c0
    // 0xb4899c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb4899c: ldur            w3, [x2, #0x17]
    // 0xb489a0: DecompressPointer r3
    //     0xb489a0: add             x3, x3, HEAP, lsl #32
    // 0xb489a4: cmp             w3, NULL
    // 0xb489a8: b.ne            #0xb489b4
    // 0xb489ac: r3 = Null
    //     0xb489ac: mov             x3, NULL
    // 0xb489b0: b               #0xb489c0
    // 0xb489b4: LoadField: r4 = r3->field_7
    //     0xb489b4: ldur            w4, [x3, #7]
    // 0xb489b8: DecompressPointer r4
    //     0xb489b8: add             x4, x4, HEAP, lsl #32
    // 0xb489bc: mov             x3, x4
    // 0xb489c0: cmp             w3, NULL
    // 0xb489c4: b.ne            #0xb489d0
    // 0xb489c8: r3 = 0
    //     0xb489c8: movz            x3, #0
    // 0xb489cc: b               #0xb489e0
    // 0xb489d0: r4 = LoadInt32Instr(r3)
    //     0xb489d0: sbfx            x4, x3, #1, #0x1f
    //     0xb489d4: tbz             w3, #0, #0xb489dc
    //     0xb489d8: ldur            x4, [x3, #7]
    // 0xb489dc: mov             x3, x4
    // 0xb489e0: stur            x3, [fp, #-0x68]
    // 0xb489e4: cmp             w2, NULL
    // 0xb489e8: b.ne            #0xb489f4
    // 0xb489ec: r4 = Null
    //     0xb489ec: mov             x4, NULL
    // 0xb489f0: b               #0xb48a18
    // 0xb489f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb489f4: ldur            w4, [x2, #0x17]
    // 0xb489f8: DecompressPointer r4
    //     0xb489f8: add             x4, x4, HEAP, lsl #32
    // 0xb489fc: cmp             w4, NULL
    // 0xb48a00: b.ne            #0xb48a0c
    // 0xb48a04: r4 = Null
    //     0xb48a04: mov             x4, NULL
    // 0xb48a08: b               #0xb48a18
    // 0xb48a0c: LoadField: r5 = r4->field_b
    //     0xb48a0c: ldur            w5, [x4, #0xb]
    // 0xb48a10: DecompressPointer r5
    //     0xb48a10: add             x5, x5, HEAP, lsl #32
    // 0xb48a14: mov             x4, x5
    // 0xb48a18: cmp             w4, NULL
    // 0xb48a1c: b.ne            #0xb48a28
    // 0xb48a20: r4 = 0
    //     0xb48a20: movz            x4, #0
    // 0xb48a24: b               #0xb48a38
    // 0xb48a28: r5 = LoadInt32Instr(r4)
    //     0xb48a28: sbfx            x5, x4, #1, #0x1f
    //     0xb48a2c: tbz             w4, #0, #0xb48a34
    //     0xb48a30: ldur            x5, [x4, #7]
    // 0xb48a34: mov             x4, x5
    // 0xb48a38: stur            x4, [fp, #-0x60]
    // 0xb48a3c: cmp             w2, NULL
    // 0xb48a40: b.ne            #0xb48a4c
    // 0xb48a44: r2 = Null
    //     0xb48a44: mov             x2, NULL
    // 0xb48a48: b               #0xb48a6c
    // 0xb48a4c: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb48a4c: ldur            w5, [x2, #0x17]
    // 0xb48a50: DecompressPointer r5
    //     0xb48a50: add             x5, x5, HEAP, lsl #32
    // 0xb48a54: cmp             w5, NULL
    // 0xb48a58: b.ne            #0xb48a64
    // 0xb48a5c: r2 = Null
    //     0xb48a5c: mov             x2, NULL
    // 0xb48a60: b               #0xb48a6c
    // 0xb48a64: LoadField: r2 = r5->field_f
    //     0xb48a64: ldur            w2, [x5, #0xf]
    // 0xb48a68: DecompressPointer r2
    //     0xb48a68: add             x2, x2, HEAP, lsl #32
    // 0xb48a6c: cmp             w2, NULL
    // 0xb48a70: b.ne            #0xb48a7c
    // 0xb48a74: r2 = 0
    //     0xb48a74: movz            x2, #0
    // 0xb48a78: b               #0xb48a8c
    // 0xb48a7c: r5 = LoadInt32Instr(r2)
    //     0xb48a7c: sbfx            x5, x2, #1, #0x1f
    //     0xb48a80: tbz             w2, #0, #0xb48a88
    //     0xb48a84: ldur            x5, [x2, #7]
    // 0xb48a88: mov             x2, x5
    // 0xb48a8c: stur            x2, [fp, #-0x58]
    // 0xb48a90: r0 = Color()
    //     0xb48a90: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb48a94: mov             x1, x0
    // 0xb48a98: r0 = Instance_ColorSpace
    //     0xb48a98: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb48a9c: StoreField: r1->field_27 = r0
    //     0xb48a9c: stur            w0, [x1, #0x27]
    // 0xb48aa0: d0 = 1.000000
    //     0xb48aa0: fmov            d0, #1.00000000
    // 0xb48aa4: StoreField: r1->field_7 = d0
    //     0xb48aa4: stur            d0, [x1, #7]
    // 0xb48aa8: ldur            x0, [fp, #-0x68]
    // 0xb48aac: ubfx            x0, x0, #0, #0x20
    // 0xb48ab0: and             w2, w0, #0xff
    // 0xb48ab4: ubfx            x2, x2, #0, #0x20
    // 0xb48ab8: scvtf           d0, x2
    // 0xb48abc: d1 = 255.000000
    //     0xb48abc: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb48ac0: fdiv            d2, d0, d1
    // 0xb48ac4: StoreField: r1->field_f = d2
    //     0xb48ac4: stur            d2, [x1, #0xf]
    // 0xb48ac8: ldur            x0, [fp, #-0x60]
    // 0xb48acc: ubfx            x0, x0, #0, #0x20
    // 0xb48ad0: and             w2, w0, #0xff
    // 0xb48ad4: ubfx            x2, x2, #0, #0x20
    // 0xb48ad8: scvtf           d0, x2
    // 0xb48adc: fdiv            d2, d0, d1
    // 0xb48ae0: ArrayStore: r1[0] = d2  ; List_8
    //     0xb48ae0: stur            d2, [x1, #0x17]
    // 0xb48ae4: ldur            x0, [fp, #-0x58]
    // 0xb48ae8: ubfx            x0, x0, #0, #0x20
    // 0xb48aec: and             w2, w0, #0xff
    // 0xb48af0: ubfx            x2, x2, #0, #0x20
    // 0xb48af4: scvtf           d0, x2
    // 0xb48af8: fdiv            d2, d0, d1
    // 0xb48afc: StoreField: r1->field_1f = d2
    //     0xb48afc: stur            d2, [x1, #0x1f]
    // 0xb48b00: r16 = 14.000000
    //     0xb48b00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb48b04: ldr             x16, [x16, #0x1d8]
    // 0xb48b08: stp             x1, x16, [SP]
    // 0xb48b0c: ldur            x1, [fp, #-0x20]
    // 0xb48b10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb48b10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb48b14: ldr             x4, [x4, #0xaa0]
    // 0xb48b18: r0 = copyWith()
    //     0xb48b18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb48b1c: stur            x0, [fp, #-0x40]
    // 0xb48b20: r0 = Text()
    //     0xb48b20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb48b24: mov             x3, x0
    // 0xb48b28: r0 = "I want to order more"
    //     0xb48b28: add             x0, PP, #0x56, lsl #12  ; [pp+0x56dc8] "I want to order more"
    //     0xb48b2c: ldr             x0, [x0, #0xdc8]
    // 0xb48b30: stur            x3, [fp, #-0x48]
    // 0xb48b34: StoreField: r3->field_b = r0
    //     0xb48b34: stur            w0, [x3, #0xb]
    // 0xb48b38: ldur            x0, [fp, #-0x40]
    // 0xb48b3c: StoreField: r3->field_13 = r0
    //     0xb48b3c: stur            w0, [x3, #0x13]
    // 0xb48b40: ldur            x2, [fp, #-0x18]
    // 0xb48b44: r1 = Function '<anonymous closure>':.
    //     0xb48b44: add             x1, PP, #0x56, lsl #12  ; [pp+0x56dd0] AnonymousClosure: (0xb49030), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xb486ac)
    //     0xb48b48: ldr             x1, [x1, #0xdd0]
    // 0xb48b4c: r0 = AllocateClosure()
    //     0xb48b4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb48b50: stur            x0, [fp, #-0x40]
    // 0xb48b54: r0 = TextButton()
    //     0xb48b54: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb48b58: mov             x1, x0
    // 0xb48b5c: ldur            x0, [fp, #-0x40]
    // 0xb48b60: stur            x1, [fp, #-0x50]
    // 0xb48b64: StoreField: r1->field_b = r0
    //     0xb48b64: stur            w0, [x1, #0xb]
    // 0xb48b68: r0 = false
    //     0xb48b68: add             x0, NULL, #0x30  ; false
    // 0xb48b6c: StoreField: r1->field_27 = r0
    //     0xb48b6c: stur            w0, [x1, #0x27]
    // 0xb48b70: r2 = true
    //     0xb48b70: add             x2, NULL, #0x20  ; true
    // 0xb48b74: StoreField: r1->field_2f = r2
    //     0xb48b74: stur            w2, [x1, #0x2f]
    // 0xb48b78: ldur            x3, [fp, #-0x48]
    // 0xb48b7c: StoreField: r1->field_37 = r3
    //     0xb48b7c: stur            w3, [x1, #0x37]
    // 0xb48b80: r0 = TextButtonTheme()
    //     0xb48b80: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb48b84: mov             x1, x0
    // 0xb48b88: ldur            x0, [fp, #-0x38]
    // 0xb48b8c: stur            x1, [fp, #-0x40]
    // 0xb48b90: StoreField: r1->field_f = r0
    //     0xb48b90: stur            w0, [x1, #0xf]
    // 0xb48b94: ldur            x0, [fp, #-0x50]
    // 0xb48b98: StoreField: r1->field_b = r0
    //     0xb48b98: stur            w0, [x1, #0xb]
    // 0xb48b9c: r0 = SizedBox()
    //     0xb48b9c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb48ba0: mov             x1, x0
    // 0xb48ba4: r0 = inf
    //     0xb48ba4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb48ba8: ldr             x0, [x0, #0x9f8]
    // 0xb48bac: stur            x1, [fp, #-0x38]
    // 0xb48bb0: StoreField: r1->field_f = r0
    //     0xb48bb0: stur            w0, [x1, #0xf]
    // 0xb48bb4: ldur            x2, [fp, #-0x40]
    // 0xb48bb8: StoreField: r1->field_b = r2
    //     0xb48bb8: stur            w2, [x1, #0xb]
    // 0xb48bbc: r16 = <EdgeInsets>
    //     0xb48bbc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb48bc0: ldr             x16, [x16, #0xda0]
    // 0xb48bc4: r30 = Instance_EdgeInsets
    //     0xb48bc4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb48bc8: ldr             lr, [lr, #0x1f0]
    // 0xb48bcc: stp             lr, x16, [SP]
    // 0xb48bd0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb48bd0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb48bd4: r0 = all()
    //     0xb48bd4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb48bd8: ldur            x1, [fp, #-0x10]
    // 0xb48bdc: stur            x0, [fp, #-0x10]
    // 0xb48be0: r0 = of()
    //     0xb48be0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb48be4: LoadField: r1 = r0->field_5b
    //     0xb48be4: ldur            w1, [x0, #0x5b]
    // 0xb48be8: DecompressPointer r1
    //     0xb48be8: add             x1, x1, HEAP, lsl #32
    // 0xb48bec: r16 = <Color>
    //     0xb48bec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb48bf0: ldr             x16, [x16, #0xf80]
    // 0xb48bf4: stp             x1, x16, [SP]
    // 0xb48bf8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb48bf8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb48bfc: r0 = all()
    //     0xb48bfc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb48c00: stur            x0, [fp, #-0x40]
    // 0xb48c04: r0 = Radius()
    //     0xb48c04: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb48c08: d0 = 20.000000
    //     0xb48c08: fmov            d0, #20.00000000
    // 0xb48c0c: stur            x0, [fp, #-0x48]
    // 0xb48c10: StoreField: r0->field_7 = d0
    //     0xb48c10: stur            d0, [x0, #7]
    // 0xb48c14: StoreField: r0->field_f = d0
    //     0xb48c14: stur            d0, [x0, #0xf]
    // 0xb48c18: r0 = BorderRadius()
    //     0xb48c18: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb48c1c: mov             x1, x0
    // 0xb48c20: ldur            x0, [fp, #-0x48]
    // 0xb48c24: stur            x1, [fp, #-0x50]
    // 0xb48c28: StoreField: r1->field_7 = r0
    //     0xb48c28: stur            w0, [x1, #7]
    // 0xb48c2c: StoreField: r1->field_b = r0
    //     0xb48c2c: stur            w0, [x1, #0xb]
    // 0xb48c30: StoreField: r1->field_f = r0
    //     0xb48c30: stur            w0, [x1, #0xf]
    // 0xb48c34: StoreField: r1->field_13 = r0
    //     0xb48c34: stur            w0, [x1, #0x13]
    // 0xb48c38: r0 = RoundedRectangleBorder()
    //     0xb48c38: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb48c3c: mov             x1, x0
    // 0xb48c40: ldur            x0, [fp, #-0x50]
    // 0xb48c44: StoreField: r1->field_b = r0
    //     0xb48c44: stur            w0, [x1, #0xb]
    // 0xb48c48: r0 = Instance_BorderSide
    //     0xb48c48: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb48c4c: ldr             x0, [x0, #0xe20]
    // 0xb48c50: StoreField: r1->field_7 = r0
    //     0xb48c50: stur            w0, [x1, #7]
    // 0xb48c54: r16 = <RoundedRectangleBorder>
    //     0xb48c54: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb48c58: ldr             x16, [x16, #0xf78]
    // 0xb48c5c: stp             x1, x16, [SP]
    // 0xb48c60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb48c60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb48c64: r0 = all()
    //     0xb48c64: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb48c68: stur            x0, [fp, #-0x48]
    // 0xb48c6c: r0 = ButtonStyle()
    //     0xb48c6c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb48c70: mov             x1, x0
    // 0xb48c74: ldur            x0, [fp, #-0x40]
    // 0xb48c78: stur            x1, [fp, #-0x50]
    // 0xb48c7c: StoreField: r1->field_b = r0
    //     0xb48c7c: stur            w0, [x1, #0xb]
    // 0xb48c80: ldur            x0, [fp, #-0x10]
    // 0xb48c84: StoreField: r1->field_23 = r0
    //     0xb48c84: stur            w0, [x1, #0x23]
    // 0xb48c88: ldur            x0, [fp, #-0x48]
    // 0xb48c8c: StoreField: r1->field_43 = r0
    //     0xb48c8c: stur            w0, [x1, #0x43]
    // 0xb48c90: r0 = TextButtonThemeData()
    //     0xb48c90: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb48c94: mov             x2, x0
    // 0xb48c98: ldur            x0, [fp, #-0x50]
    // 0xb48c9c: stur            x2, [fp, #-0x10]
    // 0xb48ca0: StoreField: r2->field_7 = r0
    //     0xb48ca0: stur            w0, [x2, #7]
    // 0xb48ca4: ldur            x0, [fp, #-8]
    // 0xb48ca8: LoadField: r1 = r0->field_b
    //     0xb48ca8: ldur            w1, [x0, #0xb]
    // 0xb48cac: DecompressPointer r1
    //     0xb48cac: add             x1, x1, HEAP, lsl #32
    // 0xb48cb0: cmp             w1, NULL
    // 0xb48cb4: b.eq            #0xb48f30
    // 0xb48cb8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb48cb8: ldur            w0, [x1, #0x17]
    // 0xb48cbc: DecompressPointer r0
    //     0xb48cbc: add             x0, x0, HEAP, lsl #32
    // 0xb48cc0: tbnz            w0, #4, #0xb48cd0
    // 0xb48cc4: r5 = "Don\'t Order"
    //     0xb48cc4: add             x5, PP, #0x54, lsl #12  ; [pp+0x54738] "Don\'t Order"
    //     0xb48cc8: ldr             x5, [x5, #0x738]
    // 0xb48ccc: b               #0xb48cd8
    // 0xb48cd0: r5 = "Edit Bag"
    //     0xb48cd0: add             x5, PP, #0x54, lsl #12  ; [pp+0x54740] "Edit Bag"
    //     0xb48cd4: ldr             x5, [x5, #0x740]
    // 0xb48cd8: ldur            x4, [fp, #-0x30]
    // 0xb48cdc: ldur            x3, [fp, #-0x28]
    // 0xb48ce0: ldur            x0, [fp, #-0x38]
    // 0xb48ce4: stur            x5, [fp, #-8]
    // 0xb48ce8: r16 = 14.000000
    //     0xb48ce8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb48cec: ldr             x16, [x16, #0x1d8]
    // 0xb48cf0: r30 = Instance_Color
    //     0xb48cf0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb48cf4: stp             lr, x16, [SP]
    // 0xb48cf8: ldur            x1, [fp, #-0x20]
    // 0xb48cfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb48cfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb48d00: ldr             x4, [x4, #0xaa0]
    // 0xb48d04: r0 = copyWith()
    //     0xb48d04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb48d08: stur            x0, [fp, #-0x20]
    // 0xb48d0c: r0 = Text()
    //     0xb48d0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb48d10: mov             x3, x0
    // 0xb48d14: ldur            x0, [fp, #-8]
    // 0xb48d18: stur            x3, [fp, #-0x40]
    // 0xb48d1c: StoreField: r3->field_b = r0
    //     0xb48d1c: stur            w0, [x3, #0xb]
    // 0xb48d20: ldur            x0, [fp, #-0x20]
    // 0xb48d24: StoreField: r3->field_13 = r0
    //     0xb48d24: stur            w0, [x3, #0x13]
    // 0xb48d28: ldur            x2, [fp, #-0x18]
    // 0xb48d2c: r1 = Function '<anonymous closure>':.
    //     0xb48d2c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56dd8] AnonymousClosure: (0xb48f58), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] _DuplicateOrderConfirmBottomSheet::build (0xb486ac)
    //     0xb48d30: ldr             x1, [x1, #0xdd8]
    // 0xb48d34: r0 = AllocateClosure()
    //     0xb48d34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb48d38: stur            x0, [fp, #-8]
    // 0xb48d3c: r0 = TextButton()
    //     0xb48d3c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb48d40: mov             x1, x0
    // 0xb48d44: ldur            x0, [fp, #-8]
    // 0xb48d48: stur            x1, [fp, #-0x18]
    // 0xb48d4c: StoreField: r1->field_b = r0
    //     0xb48d4c: stur            w0, [x1, #0xb]
    // 0xb48d50: r0 = false
    //     0xb48d50: add             x0, NULL, #0x30  ; false
    // 0xb48d54: StoreField: r1->field_27 = r0
    //     0xb48d54: stur            w0, [x1, #0x27]
    // 0xb48d58: r0 = true
    //     0xb48d58: add             x0, NULL, #0x20  ; true
    // 0xb48d5c: StoreField: r1->field_2f = r0
    //     0xb48d5c: stur            w0, [x1, #0x2f]
    // 0xb48d60: ldur            x0, [fp, #-0x40]
    // 0xb48d64: StoreField: r1->field_37 = r0
    //     0xb48d64: stur            w0, [x1, #0x37]
    // 0xb48d68: r0 = TextButtonTheme()
    //     0xb48d68: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb48d6c: mov             x1, x0
    // 0xb48d70: ldur            x0, [fp, #-0x10]
    // 0xb48d74: stur            x1, [fp, #-8]
    // 0xb48d78: StoreField: r1->field_f = r0
    //     0xb48d78: stur            w0, [x1, #0xf]
    // 0xb48d7c: ldur            x0, [fp, #-0x18]
    // 0xb48d80: StoreField: r1->field_b = r0
    //     0xb48d80: stur            w0, [x1, #0xb]
    // 0xb48d84: r0 = SizedBox()
    //     0xb48d84: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb48d88: mov             x3, x0
    // 0xb48d8c: r0 = inf
    //     0xb48d8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb48d90: ldr             x0, [x0, #0x9f8]
    // 0xb48d94: stur            x3, [fp, #-0x10]
    // 0xb48d98: StoreField: r3->field_f = r0
    //     0xb48d98: stur            w0, [x3, #0xf]
    // 0xb48d9c: ldur            x0, [fp, #-8]
    // 0xb48da0: StoreField: r3->field_b = r0
    //     0xb48da0: stur            w0, [x3, #0xb]
    // 0xb48da4: r1 = Null
    //     0xb48da4: mov             x1, NULL
    // 0xb48da8: r2 = 6
    //     0xb48da8: movz            x2, #0x6
    // 0xb48dac: r0 = AllocateArray()
    //     0xb48dac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb48db0: mov             x2, x0
    // 0xb48db4: ldur            x0, [fp, #-0x38]
    // 0xb48db8: stur            x2, [fp, #-8]
    // 0xb48dbc: StoreField: r2->field_f = r0
    //     0xb48dbc: stur            w0, [x2, #0xf]
    // 0xb48dc0: r16 = Instance_SizedBox
    //     0xb48dc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb48dc4: ldr             x16, [x16, #0x8b8]
    // 0xb48dc8: StoreField: r2->field_13 = r16
    //     0xb48dc8: stur            w16, [x2, #0x13]
    // 0xb48dcc: ldur            x0, [fp, #-0x10]
    // 0xb48dd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb48dd0: stur            w0, [x2, #0x17]
    // 0xb48dd4: r1 = <Widget>
    //     0xb48dd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb48dd8: r0 = AllocateGrowableArray()
    //     0xb48dd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb48ddc: mov             x1, x0
    // 0xb48de0: ldur            x0, [fp, #-8]
    // 0xb48de4: stur            x1, [fp, #-0x10]
    // 0xb48de8: StoreField: r1->field_f = r0
    //     0xb48de8: stur            w0, [x1, #0xf]
    // 0xb48dec: r0 = 6
    //     0xb48dec: movz            x0, #0x6
    // 0xb48df0: StoreField: r1->field_b = r0
    //     0xb48df0: stur            w0, [x1, #0xb]
    // 0xb48df4: r0 = Column()
    //     0xb48df4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb48df8: mov             x3, x0
    // 0xb48dfc: r0 = Instance_Axis
    //     0xb48dfc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb48e00: stur            x3, [fp, #-8]
    // 0xb48e04: StoreField: r3->field_f = r0
    //     0xb48e04: stur            w0, [x3, #0xf]
    // 0xb48e08: r0 = Instance_MainAxisAlignment
    //     0xb48e08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb48e0c: ldr             x0, [x0, #0xa08]
    // 0xb48e10: StoreField: r3->field_13 = r0
    //     0xb48e10: stur            w0, [x3, #0x13]
    // 0xb48e14: r0 = Instance_MainAxisSize
    //     0xb48e14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb48e18: ldr             x0, [x0, #0xa10]
    // 0xb48e1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb48e1c: stur            w0, [x3, #0x17]
    // 0xb48e20: r0 = Instance_CrossAxisAlignment
    //     0xb48e20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb48e24: ldr             x0, [x0, #0xa18]
    // 0xb48e28: StoreField: r3->field_1b = r0
    //     0xb48e28: stur            w0, [x3, #0x1b]
    // 0xb48e2c: r0 = Instance_VerticalDirection
    //     0xb48e2c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb48e30: ldr             x0, [x0, #0xa20]
    // 0xb48e34: StoreField: r3->field_23 = r0
    //     0xb48e34: stur            w0, [x3, #0x23]
    // 0xb48e38: r4 = Instance_Clip
    //     0xb48e38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb48e3c: ldr             x4, [x4, #0x38]
    // 0xb48e40: StoreField: r3->field_2b = r4
    //     0xb48e40: stur            w4, [x3, #0x2b]
    // 0xb48e44: StoreField: r3->field_2f = rZR
    //     0xb48e44: stur            xzr, [x3, #0x2f]
    // 0xb48e48: ldur            x1, [fp, #-0x10]
    // 0xb48e4c: StoreField: r3->field_b = r1
    //     0xb48e4c: stur            w1, [x3, #0xb]
    // 0xb48e50: r1 = Null
    //     0xb48e50: mov             x1, NULL
    // 0xb48e54: r2 = 8
    //     0xb48e54: movz            x2, #0x8
    // 0xb48e58: r0 = AllocateArray()
    //     0xb48e58: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb48e5c: stur            x0, [fp, #-0x10]
    // 0xb48e60: r16 = Instance_Center
    //     0xb48e60: add             x16, PP, #0x54, lsl #12  ; [pp+0x54730] Obj!Center@d682c1
    //     0xb48e64: ldr             x16, [x16, #0x730]
    // 0xb48e68: StoreField: r0->field_f = r16
    //     0xb48e68: stur            w16, [x0, #0xf]
    // 0xb48e6c: ldur            x1, [fp, #-0x30]
    // 0xb48e70: StoreField: r0->field_13 = r1
    //     0xb48e70: stur            w1, [x0, #0x13]
    // 0xb48e74: ldur            x1, [fp, #-0x28]
    // 0xb48e78: ArrayStore: r0[0] = r1  ; List_4
    //     0xb48e78: stur            w1, [x0, #0x17]
    // 0xb48e7c: ldur            x1, [fp, #-8]
    // 0xb48e80: StoreField: r0->field_1b = r1
    //     0xb48e80: stur            w1, [x0, #0x1b]
    // 0xb48e84: r1 = <Widget>
    //     0xb48e84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb48e88: r0 = AllocateGrowableArray()
    //     0xb48e88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb48e8c: mov             x1, x0
    // 0xb48e90: ldur            x0, [fp, #-0x10]
    // 0xb48e94: stur            x1, [fp, #-8]
    // 0xb48e98: StoreField: r1->field_f = r0
    //     0xb48e98: stur            w0, [x1, #0xf]
    // 0xb48e9c: r0 = 8
    //     0xb48e9c: movz            x0, #0x8
    // 0xb48ea0: StoreField: r1->field_b = r0
    //     0xb48ea0: stur            w0, [x1, #0xb]
    // 0xb48ea4: r0 = Wrap()
    //     0xb48ea4: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xb48ea8: mov             x1, x0
    // 0xb48eac: r0 = Instance_Axis
    //     0xb48eac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb48eb0: stur            x1, [fp, #-0x10]
    // 0xb48eb4: StoreField: r1->field_f = r0
    //     0xb48eb4: stur            w0, [x1, #0xf]
    // 0xb48eb8: r0 = Instance_WrapAlignment
    //     0xb48eb8: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xb48ebc: ldr             x0, [x0, #0x6e8]
    // 0xb48ec0: StoreField: r1->field_13 = r0
    //     0xb48ec0: stur            w0, [x1, #0x13]
    // 0xb48ec4: ArrayStore: r1[0] = rZR  ; List_8
    //     0xb48ec4: stur            xzr, [x1, #0x17]
    // 0xb48ec8: StoreField: r1->field_1f = r0
    //     0xb48ec8: stur            w0, [x1, #0x1f]
    // 0xb48ecc: StoreField: r1->field_23 = rZR
    //     0xb48ecc: stur            xzr, [x1, #0x23]
    // 0xb48ed0: r0 = Instance_WrapCrossAlignment
    //     0xb48ed0: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xb48ed4: ldr             x0, [x0, #0x6f0]
    // 0xb48ed8: StoreField: r1->field_2b = r0
    //     0xb48ed8: stur            w0, [x1, #0x2b]
    // 0xb48edc: r0 = Instance_VerticalDirection
    //     0xb48edc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb48ee0: ldr             x0, [x0, #0xa20]
    // 0xb48ee4: StoreField: r1->field_33 = r0
    //     0xb48ee4: stur            w0, [x1, #0x33]
    // 0xb48ee8: r0 = Instance_Clip
    //     0xb48ee8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb48eec: ldr             x0, [x0, #0x38]
    // 0xb48ef0: StoreField: r1->field_37 = r0
    //     0xb48ef0: stur            w0, [x1, #0x37]
    // 0xb48ef4: ldur            x0, [fp, #-8]
    // 0xb48ef8: StoreField: r1->field_b = r0
    //     0xb48ef8: stur            w0, [x1, #0xb]
    // 0xb48efc: r0 = Padding()
    //     0xb48efc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb48f00: r1 = Instance_EdgeInsets
    //     0xb48f00: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb48f04: ldr             x1, [x1, #0xa98]
    // 0xb48f08: StoreField: r0->field_f = r1
    //     0xb48f08: stur            w1, [x0, #0xf]
    // 0xb48f0c: ldur            x1, [fp, #-0x10]
    // 0xb48f10: StoreField: r0->field_b = r1
    //     0xb48f10: stur            w1, [x0, #0xb]
    // 0xb48f14: LeaveFrame
    //     0xb48f14: mov             SP, fp
    //     0xb48f18: ldp             fp, lr, [SP], #0x10
    // 0xb48f1c: ret
    //     0xb48f1c: ret             
    // 0xb48f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb48f20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb48f24: b               #0xb486d4
    // 0xb48f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48f28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48f2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48f2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48f30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb48f58, size: 0xd8
    // 0xb48f58: EnterFrame
    //     0xb48f58: stp             fp, lr, [SP, #-0x10]!
    //     0xb48f5c: mov             fp, SP
    // 0xb48f60: AllocStack(0x20)
    //     0xb48f60: sub             SP, SP, #0x20
    // 0xb48f64: SetupParameters()
    //     0xb48f64: ldr             x0, [fp, #0x10]
    //     0xb48f68: ldur            w1, [x0, #0x17]
    //     0xb48f6c: add             x1, x1, HEAP, lsl #32
    //     0xb48f70: stur            x1, [fp, #-8]
    // 0xb48f74: CheckStackOverflow
    //     0xb48f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb48f78: cmp             SP, x16
    //     0xb48f7c: b.ls            #0xb49024
    // 0xb48f80: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb48f80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb48f84: ldr             x0, [x0, #0x1c80]
    //     0xb48f88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb48f8c: cmp             w0, w16
    //     0xb48f90: b.ne            #0xb48f9c
    //     0xb48f94: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb48f98: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb48f9c: str             NULL, [SP]
    // 0xb48fa0: r4 = const [0x1, 0, 0, 0, null]
    //     0xb48fa0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb48fa4: r0 = GetNavigation.back()
    //     0xb48fa4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb48fa8: ldur            x0, [fp, #-8]
    // 0xb48fac: LoadField: r1 = r0->field_f
    //     0xb48fac: ldur            w1, [x0, #0xf]
    // 0xb48fb0: DecompressPointer r1
    //     0xb48fb0: add             x1, x1, HEAP, lsl #32
    // 0xb48fb4: LoadField: r0 = r1->field_b
    //     0xb48fb4: ldur            w0, [x1, #0xb]
    // 0xb48fb8: DecompressPointer r0
    //     0xb48fb8: add             x0, x0, HEAP, lsl #32
    // 0xb48fbc: cmp             w0, NULL
    // 0xb48fc0: b.eq            #0xb4902c
    // 0xb48fc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb48fc4: ldur            w1, [x0, #0x17]
    // 0xb48fc8: DecompressPointer r1
    //     0xb48fc8: add             x1, x1, HEAP, lsl #32
    // 0xb48fcc: tbnz            w1, #4, #0xb48fdc
    // 0xb48fd0: r1 = "Don\'t Order"
    //     0xb48fd0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54738] "Don\'t Order"
    //     0xb48fd4: ldr             x1, [x1, #0x738]
    // 0xb48fd8: b               #0xb48fe4
    // 0xb48fdc: r1 = "Edit Bag"
    //     0xb48fdc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54740] "Edit Bag"
    //     0xb48fe0: ldr             x1, [x1, #0x740]
    // 0xb48fe4: LoadField: r2 = r0->field_13
    //     0xb48fe4: ldur            w2, [x0, #0x13]
    // 0xb48fe8: DecompressPointer r2
    //     0xb48fe8: add             x2, x2, HEAP, lsl #32
    // 0xb48fec: r16 = "popup_cta"
    //     0xb48fec: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca60] "popup_cta"
    //     0xb48ff0: ldr             x16, [x16, #0xa60]
    // 0xb48ff4: stp             x16, x2, [SP, #8]
    // 0xb48ff8: str             x1, [SP]
    // 0xb48ffc: r4 = 0
    //     0xb48ffc: movz            x4, #0
    // 0xb49000: ldr             x0, [SP, #0x10]
    // 0xb49004: r16 = UnlinkedCall_0x613b5c
    //     0xb49004: add             x16, PP, #0x56, lsl #12  ; [pp+0x56de0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb49008: add             x16, x16, #0xde0
    // 0xb4900c: ldp             x5, lr, [x16]
    // 0xb49010: blr             lr
    // 0xb49014: r0 = Null
    //     0xb49014: mov             x0, NULL
    // 0xb49018: LeaveFrame
    //     0xb49018: mov             SP, fp
    //     0xb4901c: ldp             fp, lr, [SP], #0x10
    // 0xb49020: ret
    //     0xb49020: ret             
    // 0xb49024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb49024: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb49028: b               #0xb48f80
    // 0xb4902c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4902c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb49030, size: 0x90
    // 0xb49030: EnterFrame
    //     0xb49030: stp             fp, lr, [SP, #-0x10]!
    //     0xb49034: mov             fp, SP
    // 0xb49038: AllocStack(0x18)
    //     0xb49038: sub             SP, SP, #0x18
    // 0xb4903c: SetupParameters()
    //     0xb4903c: ldr             x0, [fp, #0x10]
    //     0xb49040: ldur            w1, [x0, #0x17]
    //     0xb49044: add             x1, x1, HEAP, lsl #32
    // 0xb49048: CheckStackOverflow
    //     0xb49048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4904c: cmp             SP, x16
    //     0xb49050: b.ls            #0xb490b4
    // 0xb49054: LoadField: r0 = r1->field_f
    //     0xb49054: ldur            w0, [x1, #0xf]
    // 0xb49058: DecompressPointer r0
    //     0xb49058: add             x0, x0, HEAP, lsl #32
    // 0xb4905c: LoadField: r1 = r0->field_b
    //     0xb4905c: ldur            w1, [x0, #0xb]
    // 0xb49060: DecompressPointer r1
    //     0xb49060: add             x1, x1, HEAP, lsl #32
    // 0xb49064: cmp             w1, NULL
    // 0xb49068: b.eq            #0xb490bc
    // 0xb4906c: LoadField: r0 = r1->field_f
    //     0xb4906c: ldur            w0, [x1, #0xf]
    // 0xb49070: DecompressPointer r0
    //     0xb49070: add             x0, x0, HEAP, lsl #32
    // 0xb49074: r16 = "popup_cta"
    //     0xb49074: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca60] "popup_cta"
    //     0xb49078: ldr             x16, [x16, #0xa60]
    // 0xb4907c: stp             x16, x0, [SP, #8]
    // 0xb49080: r16 = "i want to order more"
    //     0xb49080: add             x16, PP, #0x54, lsl #12  ; [pp+0x54758] "i want to order more"
    //     0xb49084: ldr             x16, [x16, #0x758]
    // 0xb49088: str             x16, [SP]
    // 0xb4908c: r4 = 0
    //     0xb4908c: movz            x4, #0
    // 0xb49090: ldr             x0, [SP, #0x10]
    // 0xb49094: r16 = UnlinkedCall_0x613b5c
    //     0xb49094: add             x16, PP, #0x56, lsl #12  ; [pp+0x56df0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb49098: add             x16, x16, #0xdf0
    // 0xb4909c: ldp             x5, lr, [x16]
    // 0xb490a0: blr             lr
    // 0xb490a4: r0 = Null
    //     0xb490a4: mov             x0, NULL
    // 0xb490a8: LeaveFrame
    //     0xb490a8: mov             SP, fp
    //     0xb490ac: ldp             fp, lr, [SP], #0x10
    // 0xb490b0: ret
    //     0xb490b0: ret             
    // 0xb490b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb490b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb490b8: b               #0xb49054
    // 0xb490bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb490bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb490c0, size: 0xd0
    // 0xb490c0: EnterFrame
    //     0xb490c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb490c4: mov             fp, SP
    // 0xb490c8: ldr             x0, [fp, #0x20]
    // 0xb490cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb490cc: ldur            w1, [x0, #0x17]
    // 0xb490d0: DecompressPointer r1
    //     0xb490d0: add             x1, x1, HEAP, lsl #32
    // 0xb490d4: CheckStackOverflow
    //     0xb490d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb490d8: cmp             SP, x16
    //     0xb490dc: b.ls            #0xb49180
    // 0xb490e0: LoadField: r0 = r1->field_f
    //     0xb490e0: ldur            w0, [x1, #0xf]
    // 0xb490e4: DecompressPointer r0
    //     0xb490e4: add             x0, x0, HEAP, lsl #32
    // 0xb490e8: LoadField: r1 = r0->field_b
    //     0xb490e8: ldur            w1, [x0, #0xb]
    // 0xb490ec: DecompressPointer r1
    //     0xb490ec: add             x1, x1, HEAP, lsl #32
    // 0xb490f0: cmp             w1, NULL
    // 0xb490f4: b.eq            #0xb49188
    // 0xb490f8: LoadField: r0 = r1->field_b
    //     0xb490f8: ldur            w0, [x1, #0xb]
    // 0xb490fc: DecompressPointer r0
    //     0xb490fc: add             x0, x0, HEAP, lsl #32
    // 0xb49100: LoadField: r2 = r0->field_7
    //     0xb49100: ldur            w2, [x0, #7]
    // 0xb49104: DecompressPointer r2
    //     0xb49104: add             x2, x2, HEAP, lsl #32
    // 0xb49108: cmp             w2, NULL
    // 0xb4910c: b.ne            #0xb49118
    // 0xb49110: r0 = Null
    //     0xb49110: mov             x0, NULL
    // 0xb49114: b               #0xb49154
    // 0xb49118: ldr             x0, [fp, #0x10]
    // 0xb4911c: LoadField: r1 = r2->field_b
    //     0xb4911c: ldur            w1, [x2, #0xb]
    // 0xb49120: r3 = LoadInt32Instr(r0)
    //     0xb49120: sbfx            x3, x0, #1, #0x1f
    //     0xb49124: tbz             w0, #0, #0xb4912c
    //     0xb49128: ldur            x3, [x0, #7]
    // 0xb4912c: r0 = LoadInt32Instr(r1)
    //     0xb4912c: sbfx            x0, x1, #1, #0x1f
    // 0xb49130: mov             x1, x3
    // 0xb49134: cmp             x1, x0
    // 0xb49138: b.hs            #0xb4918c
    // 0xb4913c: LoadField: r0 = r2->field_f
    //     0xb4913c: ldur            w0, [x2, #0xf]
    // 0xb49140: DecompressPointer r0
    //     0xb49140: add             x0, x0, HEAP, lsl #32
    // 0xb49144: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb49144: add             x16, x0, x3, lsl #2
    //     0xb49148: ldur            w1, [x16, #0xf]
    // 0xb4914c: DecompressPointer r1
    //     0xb4914c: add             x1, x1, HEAP, lsl #32
    // 0xb49150: mov             x0, x1
    // 0xb49154: cmp             w0, NULL
    // 0xb49158: b.ne            #0xb49168
    // 0xb4915c: r0 = DEntities()
    //     0xb4915c: bl              #0xa0d26c  ; AllocateDEntitiesStub -> DEntities (size=0x34)
    // 0xb49160: mov             x2, x0
    // 0xb49164: b               #0xb4916c
    // 0xb49168: mov             x2, x0
    // 0xb4916c: ldr             x1, [fp, #0x18]
    // 0xb49170: r0 = bagGlassThemeItem()
    //     0xb49170: bl              #0xb49190  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/duplicate_order_confirm_bottom_sheet.dart] ::bagGlassThemeItem
    // 0xb49174: LeaveFrame
    //     0xb49174: mov             SP, fp
    //     0xb49178: ldp             fp, lr, [SP], #0x10
    // 0xb4917c: ret
    //     0xb4917c: ret             
    // 0xb49180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb49180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb49184: b               #0xb490e0
    // 0xb49188: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb49188: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4918c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4918c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4100, size: 0x20, field offset: 0xc
//   const constructor, 
class DuplicateOrderConfirmBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ec74, size: 0x24
    // 0xc7ec74: EnterFrame
    //     0xc7ec74: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ec78: mov             fp, SP
    // 0xc7ec7c: mov             x0, x1
    // 0xc7ec80: r1 = <DuplicateOrderConfirmBottomSheet>
    //     0xc7ec80: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a50] TypeArguments: <DuplicateOrderConfirmBottomSheet>
    //     0xc7ec84: ldr             x1, [x1, #0xa50]
    // 0xc7ec88: r0 = _DuplicateOrderConfirmBottomSheet()
    //     0xc7ec88: bl              #0xc7ec98  ; Allocate_DuplicateOrderConfirmBottomSheetStub -> _DuplicateOrderConfirmBottomSheet (size=0x14)
    // 0xc7ec8c: LeaveFrame
    //     0xc7ec8c: mov             SP, fp
    //     0xc7ec90: ldp             fp, lr, [SP], #0x10
    // 0xc7ec94: ret
    //     0xc7ec94: ret             
  }
}
