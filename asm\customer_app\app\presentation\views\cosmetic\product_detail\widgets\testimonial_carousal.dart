// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart

// class id: 1049328, size: 0x8
class :: {
}

// class id: 3391, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb1ba14, size: 0x64
    // 0xb1ba14: EnterFrame
    //     0xb1ba14: stp             fp, lr, [SP, #-0x10]!
    //     0xb1ba18: mov             fp, SP
    // 0xb1ba1c: AllocStack(0x18)
    //     0xb1ba1c: sub             SP, SP, #0x18
    // 0xb1ba20: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb1ba20: stur            x1, [fp, #-8]
    //     0xb1ba24: stur            x2, [fp, #-0x10]
    // 0xb1ba28: r1 = 2
    //     0xb1ba28: movz            x1, #0x2
    // 0xb1ba2c: r0 = AllocateContext()
    //     0xb1ba2c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1ba30: mov             x1, x0
    // 0xb1ba34: ldur            x0, [fp, #-8]
    // 0xb1ba38: stur            x1, [fp, #-0x18]
    // 0xb1ba3c: StoreField: r1->field_f = r0
    //     0xb1ba3c: stur            w0, [x1, #0xf]
    // 0xb1ba40: ldur            x0, [fp, #-0x10]
    // 0xb1ba44: StoreField: r1->field_13 = r0
    //     0xb1ba44: stur            w0, [x1, #0x13]
    // 0xb1ba48: r0 = Obx()
    //     0xb1ba48: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb1ba4c: ldur            x2, [fp, #-0x18]
    // 0xb1ba50: r1 = Function '<anonymous closure>':.
    //     0xb1ba50: add             x1, PP, #0x57, lsl #12  ; [pp+0x57698] AnonymousClosure: (0xb1ba9c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb1ba14)
    //     0xb1ba54: ldr             x1, [x1, #0x698]
    // 0xb1ba58: stur            x0, [fp, #-8]
    // 0xb1ba5c: r0 = AllocateClosure()
    //     0xb1ba5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1ba60: mov             x1, x0
    // 0xb1ba64: ldur            x0, [fp, #-8]
    // 0xb1ba68: StoreField: r0->field_b = r1
    //     0xb1ba68: stur            w1, [x0, #0xb]
    // 0xb1ba6c: LeaveFrame
    //     0xb1ba6c: mov             SP, fp
    //     0xb1ba70: ldp             fp, lr, [SP], #0x10
    // 0xb1ba74: ret
    //     0xb1ba74: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb1ba9c, size: 0xaf4
    // 0xb1ba9c: EnterFrame
    //     0xb1ba9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1baa0: mov             fp, SP
    // 0xb1baa4: AllocStack(0x88)
    //     0xb1baa4: sub             SP, SP, #0x88
    // 0xb1baa8: SetupParameters()
    //     0xb1baa8: ldr             x0, [fp, #0x10]
    //     0xb1baac: ldur            w3, [x0, #0x17]
    //     0xb1bab0: add             x3, x3, HEAP, lsl #32
    //     0xb1bab4: stur            x3, [fp, #-0x10]
    // 0xb1bab8: CheckStackOverflow
    //     0xb1bab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1babc: cmp             SP, x16
    //     0xb1bac0: b.ls            #0xb1c560
    // 0xb1bac4: LoadField: r0 = r3->field_f
    //     0xb1bac4: ldur            w0, [x3, #0xf]
    // 0xb1bac8: DecompressPointer r0
    //     0xb1bac8: add             x0, x0, HEAP, lsl #32
    // 0xb1bacc: LoadField: r1 = r0->field_b
    //     0xb1bacc: ldur            w1, [x0, #0xb]
    // 0xb1bad0: DecompressPointer r1
    //     0xb1bad0: add             x1, x1, HEAP, lsl #32
    // 0xb1bad4: cmp             w1, NULL
    // 0xb1bad8: b.eq            #0xb1c568
    // 0xb1badc: LoadField: r0 = r1->field_13
    //     0xb1badc: ldur            w0, [x1, #0x13]
    // 0xb1bae0: DecompressPointer r0
    //     0xb1bae0: add             x0, x0, HEAP, lsl #32
    // 0xb1bae4: LoadField: r1 = r0->field_7
    //     0xb1bae4: ldur            w1, [x0, #7]
    // 0xb1bae8: DecompressPointer r1
    //     0xb1bae8: add             x1, x1, HEAP, lsl #32
    // 0xb1baec: cmp             w1, NULL
    // 0xb1baf0: b.ne            #0xb1bb00
    // 0xb1baf4: r0 = Instance_TitleAlignment
    //     0xb1baf4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1baf8: ldr             x0, [x0, #0x518]
    // 0xb1bafc: b               #0xb1bb04
    // 0xb1bb00: mov             x0, x1
    // 0xb1bb04: r16 = Instance_TitleAlignment
    //     0xb1bb04: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb1bb08: ldr             x16, [x16, #0x520]
    // 0xb1bb0c: cmp             w0, w16
    // 0xb1bb10: b.ne            #0xb1bb20
    // 0xb1bb14: r0 = Instance_CrossAxisAlignment
    //     0xb1bb14: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb1bb18: ldr             x0, [x0, #0xc68]
    // 0xb1bb1c: b               #0xb1bb44
    // 0xb1bb20: r16 = Instance_TitleAlignment
    //     0xb1bb20: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1bb24: ldr             x16, [x16, #0x518]
    // 0xb1bb28: cmp             w0, w16
    // 0xb1bb2c: b.ne            #0xb1bb3c
    // 0xb1bb30: r0 = Instance_CrossAxisAlignment
    //     0xb1bb30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1bb34: ldr             x0, [x0, #0x890]
    // 0xb1bb38: b               #0xb1bb44
    // 0xb1bb3c: r0 = Instance_CrossAxisAlignment
    //     0xb1bb3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1bb40: ldr             x0, [x0, #0xa18]
    // 0xb1bb44: stur            x0, [fp, #-8]
    // 0xb1bb48: r1 = <Widget>
    //     0xb1bb48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1bb4c: r2 = 0
    //     0xb1bb4c: movz            x2, #0
    // 0xb1bb50: r0 = _GrowableList()
    //     0xb1bb50: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb1bb54: mov             x1, x0
    // 0xb1bb58: ldur            x2, [fp, #-0x10]
    // 0xb1bb5c: stur            x1, [fp, #-0x18]
    // 0xb1bb60: LoadField: r0 = r2->field_f
    //     0xb1bb60: ldur            w0, [x2, #0xf]
    // 0xb1bb64: DecompressPointer r0
    //     0xb1bb64: add             x0, x0, HEAP, lsl #32
    // 0xb1bb68: LoadField: r3 = r0->field_b
    //     0xb1bb68: ldur            w3, [x0, #0xb]
    // 0xb1bb6c: DecompressPointer r3
    //     0xb1bb6c: add             x3, x3, HEAP, lsl #32
    // 0xb1bb70: cmp             w3, NULL
    // 0xb1bb74: b.eq            #0xb1c56c
    // 0xb1bb78: LoadField: r0 = r3->field_f
    //     0xb1bb78: ldur            w0, [x3, #0xf]
    // 0xb1bb7c: DecompressPointer r0
    //     0xb1bb7c: add             x0, x0, HEAP, lsl #32
    // 0xb1bb80: LoadField: r3 = r0->field_7
    //     0xb1bb80: ldur            w3, [x0, #7]
    // 0xb1bb84: cbz             w3, #0xb1bd28
    // 0xb1bb88: r3 = LoadClassIdInstr(r0)
    //     0xb1bb88: ldur            x3, [x0, #-1]
    //     0xb1bb8c: ubfx            x3, x3, #0xc, #0x14
    // 0xb1bb90: str             x0, [SP]
    // 0xb1bb94: mov             x0, x3
    // 0xb1bb98: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb1bb98: sub             lr, x0, #1, lsl #12
    //     0xb1bb9c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1bba0: blr             lr
    // 0xb1bba4: ldur            x2, [fp, #-0x10]
    // 0xb1bba8: stur            x0, [fp, #-0x28]
    // 0xb1bbac: LoadField: r1 = r2->field_f
    //     0xb1bbac: ldur            w1, [x2, #0xf]
    // 0xb1bbb0: DecompressPointer r1
    //     0xb1bbb0: add             x1, x1, HEAP, lsl #32
    // 0xb1bbb4: LoadField: r3 = r1->field_b
    //     0xb1bbb4: ldur            w3, [x1, #0xb]
    // 0xb1bbb8: DecompressPointer r3
    //     0xb1bbb8: add             x3, x3, HEAP, lsl #32
    // 0xb1bbbc: cmp             w3, NULL
    // 0xb1bbc0: b.eq            #0xb1c570
    // 0xb1bbc4: LoadField: r1 = r3->field_13
    //     0xb1bbc4: ldur            w1, [x3, #0x13]
    // 0xb1bbc8: DecompressPointer r1
    //     0xb1bbc8: add             x1, x1, HEAP, lsl #32
    // 0xb1bbcc: LoadField: r3 = r1->field_7
    //     0xb1bbcc: ldur            w3, [x1, #7]
    // 0xb1bbd0: DecompressPointer r3
    //     0xb1bbd0: add             x3, x3, HEAP, lsl #32
    // 0xb1bbd4: cmp             w3, NULL
    // 0xb1bbd8: b.ne            #0xb1bbe8
    // 0xb1bbdc: r1 = Instance_TitleAlignment
    //     0xb1bbdc: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1bbe0: ldr             x1, [x1, #0x518]
    // 0xb1bbe4: b               #0xb1bbec
    // 0xb1bbe8: mov             x1, x3
    // 0xb1bbec: r16 = Instance_TitleAlignment
    //     0xb1bbec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb1bbf0: ldr             x16, [x16, #0x520]
    // 0xb1bbf4: cmp             w1, w16
    // 0xb1bbf8: b.ne            #0xb1bc04
    // 0xb1bbfc: r4 = Instance_TextAlign
    //     0xb1bbfc: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb1bc00: b               #0xb1bc20
    // 0xb1bc04: r16 = Instance_TitleAlignment
    //     0xb1bc04: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb1bc08: ldr             x16, [x16, #0x518]
    // 0xb1bc0c: cmp             w1, w16
    // 0xb1bc10: b.ne            #0xb1bc1c
    // 0xb1bc14: r4 = Instance_TextAlign
    //     0xb1bc14: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb1bc18: b               #0xb1bc20
    // 0xb1bc1c: r4 = Instance_TextAlign
    //     0xb1bc1c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb1bc20: ldur            x3, [fp, #-0x18]
    // 0xb1bc24: stur            x4, [fp, #-0x20]
    // 0xb1bc28: LoadField: r1 = r2->field_13
    //     0xb1bc28: ldur            w1, [x2, #0x13]
    // 0xb1bc2c: DecompressPointer r1
    //     0xb1bc2c: add             x1, x1, HEAP, lsl #32
    // 0xb1bc30: r0 = of()
    //     0xb1bc30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1bc34: LoadField: r1 = r0->field_87
    //     0xb1bc34: ldur            w1, [x0, #0x87]
    // 0xb1bc38: DecompressPointer r1
    //     0xb1bc38: add             x1, x1, HEAP, lsl #32
    // 0xb1bc3c: LoadField: r0 = r1->field_7
    //     0xb1bc3c: ldur            w0, [x1, #7]
    // 0xb1bc40: DecompressPointer r0
    //     0xb1bc40: add             x0, x0, HEAP, lsl #32
    // 0xb1bc44: r16 = Instance_Color
    //     0xb1bc44: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1bc48: r30 = 32.000000
    //     0xb1bc48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb1bc4c: ldr             lr, [lr, #0x848]
    // 0xb1bc50: stp             lr, x16, [SP]
    // 0xb1bc54: mov             x1, x0
    // 0xb1bc58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1bc58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb1bc5c: ldr             x4, [x4, #0x9b8]
    // 0xb1bc60: r0 = copyWith()
    //     0xb1bc60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1bc64: stur            x0, [fp, #-0x30]
    // 0xb1bc68: r0 = Text()
    //     0xb1bc68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1bc6c: mov             x1, x0
    // 0xb1bc70: ldur            x0, [fp, #-0x28]
    // 0xb1bc74: stur            x1, [fp, #-0x38]
    // 0xb1bc78: StoreField: r1->field_b = r0
    //     0xb1bc78: stur            w0, [x1, #0xb]
    // 0xb1bc7c: ldur            x0, [fp, #-0x30]
    // 0xb1bc80: StoreField: r1->field_13 = r0
    //     0xb1bc80: stur            w0, [x1, #0x13]
    // 0xb1bc84: ldur            x0, [fp, #-0x20]
    // 0xb1bc88: StoreField: r1->field_1b = r0
    //     0xb1bc88: stur            w0, [x1, #0x1b]
    // 0xb1bc8c: r0 = Padding()
    //     0xb1bc8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1bc90: mov             x2, x0
    // 0xb1bc94: r0 = Instance_EdgeInsets
    //     0xb1bc94: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb1bc98: ldr             x0, [x0, #0x78]
    // 0xb1bc9c: stur            x2, [fp, #-0x20]
    // 0xb1bca0: StoreField: r2->field_f = r0
    //     0xb1bca0: stur            w0, [x2, #0xf]
    // 0xb1bca4: ldur            x0, [fp, #-0x38]
    // 0xb1bca8: StoreField: r2->field_b = r0
    //     0xb1bca8: stur            w0, [x2, #0xb]
    // 0xb1bcac: ldur            x0, [fp, #-0x18]
    // 0xb1bcb0: LoadField: r1 = r0->field_b
    //     0xb1bcb0: ldur            w1, [x0, #0xb]
    // 0xb1bcb4: LoadField: r3 = r0->field_f
    //     0xb1bcb4: ldur            w3, [x0, #0xf]
    // 0xb1bcb8: DecompressPointer r3
    //     0xb1bcb8: add             x3, x3, HEAP, lsl #32
    // 0xb1bcbc: LoadField: r4 = r3->field_b
    //     0xb1bcbc: ldur            w4, [x3, #0xb]
    // 0xb1bcc0: r3 = LoadInt32Instr(r1)
    //     0xb1bcc0: sbfx            x3, x1, #1, #0x1f
    // 0xb1bcc4: stur            x3, [fp, #-0x40]
    // 0xb1bcc8: r1 = LoadInt32Instr(r4)
    //     0xb1bcc8: sbfx            x1, x4, #1, #0x1f
    // 0xb1bccc: cmp             x3, x1
    // 0xb1bcd0: b.ne            #0xb1bcdc
    // 0xb1bcd4: mov             x1, x0
    // 0xb1bcd8: r0 = _growToNextCapacity()
    //     0xb1bcd8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1bcdc: ldur            x2, [fp, #-0x18]
    // 0xb1bce0: ldur            x3, [fp, #-0x40]
    // 0xb1bce4: add             x0, x3, #1
    // 0xb1bce8: lsl             x1, x0, #1
    // 0xb1bcec: StoreField: r2->field_b = r1
    //     0xb1bcec: stur            w1, [x2, #0xb]
    // 0xb1bcf0: LoadField: r1 = r2->field_f
    //     0xb1bcf0: ldur            w1, [x2, #0xf]
    // 0xb1bcf4: DecompressPointer r1
    //     0xb1bcf4: add             x1, x1, HEAP, lsl #32
    // 0xb1bcf8: ldur            x0, [fp, #-0x20]
    // 0xb1bcfc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1bcfc: add             x25, x1, x3, lsl #2
    //     0xb1bd00: add             x25, x25, #0xf
    //     0xb1bd04: str             w0, [x25]
    //     0xb1bd08: tbz             w0, #0, #0xb1bd24
    //     0xb1bd0c: ldurb           w16, [x1, #-1]
    //     0xb1bd10: ldurb           w17, [x0, #-1]
    //     0xb1bd14: and             x16, x17, x16, lsr #2
    //     0xb1bd18: tst             x16, HEAP, lsr #32
    //     0xb1bd1c: b.eq            #0xb1bd24
    //     0xb1bd20: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1bd24: b               #0xb1bd2c
    // 0xb1bd28: mov             x2, x1
    // 0xb1bd2c: ldur            x0, [fp, #-0x10]
    // 0xb1bd30: LoadField: r1 = r0->field_13
    //     0xb1bd30: ldur            w1, [x0, #0x13]
    // 0xb1bd34: DecompressPointer r1
    //     0xb1bd34: add             x1, x1, HEAP, lsl #32
    // 0xb1bd38: r0 = of()
    //     0xb1bd38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1bd3c: LoadField: r1 = r0->field_5b
    //     0xb1bd3c: ldur            w1, [x0, #0x5b]
    // 0xb1bd40: DecompressPointer r1
    //     0xb1bd40: add             x1, x1, HEAP, lsl #32
    // 0xb1bd44: stur            x1, [fp, #-0x20]
    // 0xb1bd48: r0 = BoxDecoration()
    //     0xb1bd48: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1bd4c: mov             x2, x0
    // 0xb1bd50: ldur            x0, [fp, #-0x20]
    // 0xb1bd54: stur            x2, [fp, #-0x28]
    // 0xb1bd58: StoreField: r2->field_7 = r0
    //     0xb1bd58: stur            w0, [x2, #7]
    // 0xb1bd5c: r0 = Instance_BorderRadius
    //     0xb1bd5c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb1bd60: ldr             x0, [x0, #0x460]
    // 0xb1bd64: StoreField: r2->field_13 = r0
    //     0xb1bd64: stur            w0, [x2, #0x13]
    // 0xb1bd68: r0 = Instance_BoxShape
    //     0xb1bd68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1bd6c: ldr             x0, [x0, #0x80]
    // 0xb1bd70: StoreField: r2->field_23 = r0
    //     0xb1bd70: stur            w0, [x2, #0x23]
    // 0xb1bd74: ldur            x3, [fp, #-0x10]
    // 0xb1bd78: LoadField: r1 = r3->field_13
    //     0xb1bd78: ldur            w1, [x3, #0x13]
    // 0xb1bd7c: DecompressPointer r1
    //     0xb1bd7c: add             x1, x1, HEAP, lsl #32
    // 0xb1bd80: r0 = of()
    //     0xb1bd80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1bd84: LoadField: r1 = r0->field_87
    //     0xb1bd84: ldur            w1, [x0, #0x87]
    // 0xb1bd88: DecompressPointer r1
    //     0xb1bd88: add             x1, x1, HEAP, lsl #32
    // 0xb1bd8c: LoadField: r0 = r1->field_2b
    //     0xb1bd8c: ldur            w0, [x1, #0x2b]
    // 0xb1bd90: DecompressPointer r0
    //     0xb1bd90: add             x0, x0, HEAP, lsl #32
    // 0xb1bd94: r16 = 16.000000
    //     0xb1bd94: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1bd98: ldr             x16, [x16, #0x188]
    // 0xb1bd9c: r30 = Instance_Color
    //     0xb1bd9c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb1bda0: stp             lr, x16, [SP]
    // 0xb1bda4: mov             x1, x0
    // 0xb1bda8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1bda8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1bdac: ldr             x4, [x4, #0xaa0]
    // 0xb1bdb0: r0 = copyWith()
    //     0xb1bdb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1bdb4: stur            x0, [fp, #-0x20]
    // 0xb1bdb8: r0 = Text()
    //     0xb1bdb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1bdbc: mov             x1, x0
    // 0xb1bdc0: r0 = "View All"
    //     0xb1bdc0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55098] "View All"
    //     0xb1bdc4: ldr             x0, [x0, #0x98]
    // 0xb1bdc8: stur            x1, [fp, #-0x30]
    // 0xb1bdcc: StoreField: r1->field_b = r0
    //     0xb1bdcc: stur            w0, [x1, #0xb]
    // 0xb1bdd0: ldur            x0, [fp, #-0x20]
    // 0xb1bdd4: StoreField: r1->field_13 = r0
    //     0xb1bdd4: stur            w0, [x1, #0x13]
    // 0xb1bdd8: r0 = Center()
    //     0xb1bdd8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb1bddc: mov             x1, x0
    // 0xb1bde0: r0 = Instance_Alignment
    //     0xb1bde0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb1bde4: ldr             x0, [x0, #0xb10]
    // 0xb1bde8: stur            x1, [fp, #-0x20]
    // 0xb1bdec: StoreField: r1->field_f = r0
    //     0xb1bdec: stur            w0, [x1, #0xf]
    // 0xb1bdf0: ldur            x0, [fp, #-0x30]
    // 0xb1bdf4: StoreField: r1->field_b = r0
    //     0xb1bdf4: stur            w0, [x1, #0xb]
    // 0xb1bdf8: r0 = Container()
    //     0xb1bdf8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1bdfc: stur            x0, [fp, #-0x30]
    // 0xb1be00: r16 = 40.000000
    //     0xb1be00: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb1be04: ldr             x16, [x16, #8]
    // 0xb1be08: r30 = 110.000000
    //     0xb1be08: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb1be0c: ldr             lr, [lr, #0x770]
    // 0xb1be10: stp             lr, x16, [SP, #0x10]
    // 0xb1be14: ldur            x16, [fp, #-0x28]
    // 0xb1be18: ldur            lr, [fp, #-0x20]
    // 0xb1be1c: stp             lr, x16, [SP]
    // 0xb1be20: mov             x1, x0
    // 0xb1be24: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb1be24: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb1be28: ldr             x4, [x4, #0x8c0]
    // 0xb1be2c: r0 = Container()
    //     0xb1be2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1be30: r0 = InkWell()
    //     0xb1be30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb1be34: mov             x3, x0
    // 0xb1be38: ldur            x0, [fp, #-0x30]
    // 0xb1be3c: stur            x3, [fp, #-0x20]
    // 0xb1be40: StoreField: r3->field_b = r0
    //     0xb1be40: stur            w0, [x3, #0xb]
    // 0xb1be44: ldur            x2, [fp, #-0x10]
    // 0xb1be48: r1 = Function '<anonymous closure>':.
    //     0xb1be48: add             x1, PP, #0x57, lsl #12  ; [pp+0x576a0] AnonymousClosure: (0xb20900), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb1ba14)
    //     0xb1be4c: ldr             x1, [x1, #0x6a0]
    // 0xb1be50: r0 = AllocateClosure()
    //     0xb1be50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1be54: mov             x1, x0
    // 0xb1be58: ldur            x0, [fp, #-0x20]
    // 0xb1be5c: StoreField: r0->field_f = r1
    //     0xb1be5c: stur            w1, [x0, #0xf]
    // 0xb1be60: r1 = true
    //     0xb1be60: add             x1, NULL, #0x20  ; true
    // 0xb1be64: StoreField: r0->field_43 = r1
    //     0xb1be64: stur            w1, [x0, #0x43]
    // 0xb1be68: r2 = Instance_BoxShape
    //     0xb1be68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1be6c: ldr             x2, [x2, #0x80]
    // 0xb1be70: StoreField: r0->field_47 = r2
    //     0xb1be70: stur            w2, [x0, #0x47]
    // 0xb1be74: StoreField: r0->field_6f = r1
    //     0xb1be74: stur            w1, [x0, #0x6f]
    // 0xb1be78: r2 = false
    //     0xb1be78: add             x2, NULL, #0x30  ; false
    // 0xb1be7c: StoreField: r0->field_73 = r2
    //     0xb1be7c: stur            w2, [x0, #0x73]
    // 0xb1be80: StoreField: r0->field_83 = r1
    //     0xb1be80: stur            w1, [x0, #0x83]
    // 0xb1be84: StoreField: r0->field_7b = r2
    //     0xb1be84: stur            w2, [x0, #0x7b]
    // 0xb1be88: r0 = Padding()
    //     0xb1be88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1be8c: mov             x2, x0
    // 0xb1be90: r0 = Instance_EdgeInsets
    //     0xb1be90: add             x0, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb1be94: ldr             x0, [x0, #0x110]
    // 0xb1be98: stur            x2, [fp, #-0x28]
    // 0xb1be9c: StoreField: r2->field_f = r0
    //     0xb1be9c: stur            w0, [x2, #0xf]
    // 0xb1bea0: ldur            x0, [fp, #-0x20]
    // 0xb1bea4: StoreField: r2->field_b = r0
    //     0xb1bea4: stur            w0, [x2, #0xb]
    // 0xb1bea8: ldur            x0, [fp, #-0x18]
    // 0xb1beac: LoadField: r1 = r0->field_b
    //     0xb1beac: ldur            w1, [x0, #0xb]
    // 0xb1beb0: LoadField: r3 = r0->field_f
    //     0xb1beb0: ldur            w3, [x0, #0xf]
    // 0xb1beb4: DecompressPointer r3
    //     0xb1beb4: add             x3, x3, HEAP, lsl #32
    // 0xb1beb8: LoadField: r4 = r3->field_b
    //     0xb1beb8: ldur            w4, [x3, #0xb]
    // 0xb1bebc: r3 = LoadInt32Instr(r1)
    //     0xb1bebc: sbfx            x3, x1, #1, #0x1f
    // 0xb1bec0: stur            x3, [fp, #-0x40]
    // 0xb1bec4: r1 = LoadInt32Instr(r4)
    //     0xb1bec4: sbfx            x1, x4, #1, #0x1f
    // 0xb1bec8: cmp             x3, x1
    // 0xb1becc: b.ne            #0xb1bed8
    // 0xb1bed0: mov             x1, x0
    // 0xb1bed4: r0 = _growToNextCapacity()
    //     0xb1bed4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1bed8: ldur            x4, [fp, #-0x10]
    // 0xb1bedc: ldur            x2, [fp, #-0x18]
    // 0xb1bee0: ldur            x3, [fp, #-0x40]
    // 0xb1bee4: add             x0, x3, #1
    // 0xb1bee8: lsl             x1, x0, #1
    // 0xb1beec: StoreField: r2->field_b = r1
    //     0xb1beec: stur            w1, [x2, #0xb]
    // 0xb1bef0: LoadField: r1 = r2->field_f
    //     0xb1bef0: ldur            w1, [x2, #0xf]
    // 0xb1bef4: DecompressPointer r1
    //     0xb1bef4: add             x1, x1, HEAP, lsl #32
    // 0xb1bef8: ldur            x0, [fp, #-0x28]
    // 0xb1befc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1befc: add             x25, x1, x3, lsl #2
    //     0xb1bf00: add             x25, x25, #0xf
    //     0xb1bf04: str             w0, [x25]
    //     0xb1bf08: tbz             w0, #0, #0xb1bf24
    //     0xb1bf0c: ldurb           w16, [x1, #-1]
    //     0xb1bf10: ldurb           w17, [x0, #-1]
    //     0xb1bf14: and             x16, x17, x16, lsr #2
    //     0xb1bf18: tst             x16, HEAP, lsr #32
    //     0xb1bf1c: b.eq            #0xb1bf24
    //     0xb1bf20: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1bf24: LoadField: r0 = r4->field_f
    //     0xb1bf24: ldur            w0, [x4, #0xf]
    // 0xb1bf28: DecompressPointer r0
    //     0xb1bf28: add             x0, x0, HEAP, lsl #32
    // 0xb1bf2c: LoadField: r1 = r0->field_1f
    //     0xb1bf2c: ldur            w1, [x0, #0x1f]
    // 0xb1bf30: DecompressPointer r1
    //     0xb1bf30: add             x1, x1, HEAP, lsl #32
    // 0xb1bf34: r0 = value()
    //     0xb1bf34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1bf38: tbnz            w0, #4, #0xb1c058
    // 0xb1bf3c: ldur            x2, [fp, #-0x10]
    // 0xb1bf40: LoadField: r0 = r2->field_f
    //     0xb1bf40: ldur            w0, [x2, #0xf]
    // 0xb1bf44: DecompressPointer r0
    //     0xb1bf44: add             x0, x0, HEAP, lsl #32
    // 0xb1bf48: LoadField: r1 = r0->field_b
    //     0xb1bf48: ldur            w1, [x0, #0xb]
    // 0xb1bf4c: DecompressPointer r1
    //     0xb1bf4c: add             x1, x1, HEAP, lsl #32
    // 0xb1bf50: cmp             w1, NULL
    // 0xb1bf54: b.eq            #0xb1c574
    // 0xb1bf58: LoadField: r3 = r1->field_b
    //     0xb1bf58: ldur            w3, [x1, #0xb]
    // 0xb1bf5c: DecompressPointer r3
    //     0xb1bf5c: add             x3, x3, HEAP, lsl #32
    // 0xb1bf60: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xb1bf60: ldur            x4, [x0, #0x17]
    // 0xb1bf64: r0 = BoxInt64Instr(r4)
    //     0xb1bf64: sbfiz           x0, x4, #1, #0x1f
    //     0xb1bf68: cmp             x4, x0, asr #1
    //     0xb1bf6c: b.eq            #0xb1bf78
    //     0xb1bf70: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1bf74: stur            x4, [x0, #7]
    // 0xb1bf78: r1 = LoadClassIdInstr(r3)
    //     0xb1bf78: ldur            x1, [x3, #-1]
    //     0xb1bf7c: ubfx            x1, x1, #0xc, #0x14
    // 0xb1bf80: stp             x0, x3, [SP]
    // 0xb1bf84: mov             x0, x1
    // 0xb1bf88: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb1bf88: sub             lr, x0, #0xb7
    //     0xb1bf8c: ldr             lr, [x21, lr, lsl #3]
    //     0xb1bf90: blr             lr
    // 0xb1bf94: cmp             w0, NULL
    // 0xb1bf98: b.ne            #0xb1bfa4
    // 0xb1bf9c: r0 = Null
    //     0xb1bf9c: mov             x0, NULL
    // 0xb1bfa0: b               #0xb1bfc0
    // 0xb1bfa4: LoadField: r1 = r0->field_b7
    //     0xb1bfa4: ldur            w1, [x0, #0xb7]
    // 0xb1bfa8: DecompressPointer r1
    //     0xb1bfa8: add             x1, x1, HEAP, lsl #32
    // 0xb1bfac: cmp             w1, NULL
    // 0xb1bfb0: b.ne            #0xb1bfbc
    // 0xb1bfb4: r0 = Null
    //     0xb1bfb4: mov             x0, NULL
    // 0xb1bfb8: b               #0xb1bfc0
    // 0xb1bfbc: LoadField: r0 = r1->field_b
    //     0xb1bfbc: ldur            w0, [x1, #0xb]
    // 0xb1bfc0: cmp             w0, NULL
    // 0xb1bfc4: b.ne            #0xb1bfd0
    // 0xb1bfc8: r0 = 0
    //     0xb1bfc8: movz            x0, #0
    // 0xb1bfcc: b               #0xb1bfd8
    // 0xb1bfd0: r1 = LoadInt32Instr(r0)
    //     0xb1bfd0: sbfx            x1, x0, #1, #0x1f
    // 0xb1bfd4: mov             x0, x1
    // 0xb1bfd8: cmp             x0, #0
    // 0xb1bfdc: b.le            #0xb1c04c
    // 0xb1bfe0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1bfe0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1bfe4: ldr             x0, [x0, #0x1c80]
    //     0xb1bfe8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1bfec: cmp             w0, w16
    //     0xb1bff0: b.ne            #0xb1bffc
    //     0xb1bff4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb1bff8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1bffc: r0 = GetNavigation.size()
    //     0xb1bffc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1c000: LoadField: d0 = r0->field_f
    //     0xb1c000: ldur            d0, [x0, #0xf]
    // 0xb1c004: d1 = 0.750000
    //     0xb1c004: fmov            d1, #0.75000000
    // 0xb1c008: fmul            d2, d0, d1
    // 0xb1c00c: stur            d2, [fp, #-0x60]
    // 0xb1c010: r0 = GetNavigation.size()
    //     0xb1c010: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1c014: LoadField: d0 = r0->field_f
    //     0xb1c014: ldur            d0, [x0, #0xf]
    // 0xb1c018: d1 = 0.200000
    //     0xb1c018: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1c01c: fmul            d2, d0, d1
    // 0xb1c020: stur            d2, [fp, #-0x68]
    // 0xb1c024: r0 = BoxConstraints()
    //     0xb1c024: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb1c028: StoreField: r0->field_7 = rZR
    //     0xb1c028: stur            xzr, [x0, #7]
    // 0xb1c02c: d0 = inf
    //     0xb1c02c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1c030: StoreField: r0->field_f = d0
    //     0xb1c030: stur            d0, [x0, #0xf]
    // 0xb1c034: ldur            d0, [fp, #-0x68]
    // 0xb1c038: ArrayStore: r0[0] = d0  ; List_8
    //     0xb1c038: stur            d0, [x0, #0x17]
    // 0xb1c03c: ldur            d0, [fp, #-0x60]
    // 0xb1c040: StoreField: r0->field_1f = d0
    //     0xb1c040: stur            d0, [x0, #0x1f]
    // 0xb1c044: mov             x3, x0
    // 0xb1c048: b               #0xb1c0cc
    // 0xb1c04c: d1 = 0.200000
    //     0xb1c04c: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1c050: d0 = inf
    //     0xb1c050: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1c054: b               #0xb1c060
    // 0xb1c058: d1 = 0.200000
    //     0xb1c058: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1c05c: d0 = inf
    //     0xb1c05c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1c060: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1c060: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb1c064: ldr             x0, [x0, #0x1c80]
    //     0xb1c068: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb1c06c: cmp             w0, w16
    //     0xb1c070: b.ne            #0xb1c07c
    //     0xb1c074: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb1c078: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb1c07c: r0 = GetNavigation.size()
    //     0xb1c07c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1c080: LoadField: d0 = r0->field_f
    //     0xb1c080: ldur            d0, [x0, #0xf]
    // 0xb1c084: d1 = 0.630000
    //     0xb1c084: add             x17, PP, #0x55, lsl #12  ; [pp+0x55080] IMM: double(0.63) from 0x3fe428f5c28f5c29
    //     0xb1c088: ldr             d1, [x17, #0x80]
    // 0xb1c08c: fmul            d2, d0, d1
    // 0xb1c090: stur            d2, [fp, #-0x60]
    // 0xb1c094: r0 = GetNavigation.size()
    //     0xb1c094: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1c098: LoadField: d0 = r0->field_f
    //     0xb1c098: ldur            d0, [x0, #0xf]
    // 0xb1c09c: d1 = 0.200000
    //     0xb1c09c: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb1c0a0: fmul            d2, d0, d1
    // 0xb1c0a4: stur            d2, [fp, #-0x68]
    // 0xb1c0a8: r0 = BoxConstraints()
    //     0xb1c0a8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb1c0ac: StoreField: r0->field_7 = rZR
    //     0xb1c0ac: stur            xzr, [x0, #7]
    // 0xb1c0b0: d0 = inf
    //     0xb1c0b0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb1c0b4: StoreField: r0->field_f = d0
    //     0xb1c0b4: stur            d0, [x0, #0xf]
    // 0xb1c0b8: ldur            d0, [fp, #-0x68]
    // 0xb1c0bc: ArrayStore: r0[0] = d0  ; List_8
    //     0xb1c0bc: stur            d0, [x0, #0x17]
    // 0xb1c0c0: ldur            d0, [fp, #-0x60]
    // 0xb1c0c4: StoreField: r0->field_1f = d0
    //     0xb1c0c4: stur            d0, [x0, #0x1f]
    // 0xb1c0c8: mov             x3, x0
    // 0xb1c0cc: ldur            x2, [fp, #-0x10]
    // 0xb1c0d0: ldur            x1, [fp, #-0x18]
    // 0xb1c0d4: stur            x3, [fp, #-0x20]
    // 0xb1c0d8: LoadField: r0 = r2->field_f
    //     0xb1c0d8: ldur            w0, [x2, #0xf]
    // 0xb1c0dc: DecompressPointer r0
    //     0xb1c0dc: add             x0, x0, HEAP, lsl #32
    // 0xb1c0e0: LoadField: r4 = r0->field_b
    //     0xb1c0e0: ldur            w4, [x0, #0xb]
    // 0xb1c0e4: DecompressPointer r4
    //     0xb1c0e4: add             x4, x4, HEAP, lsl #32
    // 0xb1c0e8: cmp             w4, NULL
    // 0xb1c0ec: b.eq            #0xb1c578
    // 0xb1c0f0: LoadField: r0 = r4->field_b
    //     0xb1c0f0: ldur            w0, [x4, #0xb]
    // 0xb1c0f4: DecompressPointer r0
    //     0xb1c0f4: add             x0, x0, HEAP, lsl #32
    // 0xb1c0f8: r4 = LoadClassIdInstr(r0)
    //     0xb1c0f8: ldur            x4, [x0, #-1]
    //     0xb1c0fc: ubfx            x4, x4, #0xc, #0x14
    // 0xb1c100: str             x0, [SP]
    // 0xb1c104: mov             x0, x4
    // 0xb1c108: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1c108: movz            x17, #0xc898
    //     0xb1c10c: add             lr, x0, x17
    //     0xb1c110: ldr             lr, [x21, lr, lsl #3]
    //     0xb1c114: blr             lr
    // 0xb1c118: mov             x3, x0
    // 0xb1c11c: ldur            x0, [fp, #-0x10]
    // 0xb1c120: stur            x3, [fp, #-0x30]
    // 0xb1c124: LoadField: r1 = r0->field_f
    //     0xb1c124: ldur            w1, [x0, #0xf]
    // 0xb1c128: DecompressPointer r1
    //     0xb1c128: add             x1, x1, HEAP, lsl #32
    // 0xb1c12c: LoadField: r4 = r1->field_13
    //     0xb1c12c: ldur            w4, [x1, #0x13]
    // 0xb1c130: DecompressPointer r4
    //     0xb1c130: add             x4, x4, HEAP, lsl #32
    // 0xb1c134: r16 = Sentinel
    //     0xb1c134: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1c138: cmp             w4, w16
    // 0xb1c13c: b.eq            #0xb1c57c
    // 0xb1c140: mov             x2, x0
    // 0xb1c144: stur            x4, [fp, #-0x28]
    // 0xb1c148: r1 = Function '<anonymous closure>':.
    //     0xb1c148: add             x1, PP, #0x57, lsl #12  ; [pp+0x576a8] AnonymousClosure: (0xb1e088), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb1ba14)
    //     0xb1c14c: ldr             x1, [x1, #0x6a8]
    // 0xb1c150: r0 = AllocateClosure()
    //     0xb1c150: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1c154: ldur            x2, [fp, #-0x10]
    // 0xb1c158: r1 = Function '<anonymous closure>':.
    //     0xb1c158: add             x1, PP, #0x57, lsl #12  ; [pp+0x576b0] AnonymousClosure: (0xb1c590), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb1ba14)
    //     0xb1c15c: ldr             x1, [x1, #0x6b0]
    // 0xb1c160: stur            x0, [fp, #-0x38]
    // 0xb1c164: r0 = AllocateClosure()
    //     0xb1c164: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1c168: stur            x0, [fp, #-0x48]
    // 0xb1c16c: r0 = PageView()
    //     0xb1c16c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb1c170: stur            x0, [fp, #-0x50]
    // 0xb1c174: r16 = Instance_BouncingScrollPhysics
    //     0xb1c174: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb1c178: ldr             x16, [x16, #0x890]
    // 0xb1c17c: ldur            lr, [fp, #-0x28]
    // 0xb1c180: stp             lr, x16, [SP]
    // 0xb1c184: mov             x1, x0
    // 0xb1c188: ldur            x2, [fp, #-0x48]
    // 0xb1c18c: ldur            x3, [fp, #-0x30]
    // 0xb1c190: ldur            x5, [fp, #-0x38]
    // 0xb1c194: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb1c194: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb1c198: ldr             x4, [x4, #0xe40]
    // 0xb1c19c: r0 = PageView.builder()
    //     0xb1c19c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb1c1a0: r0 = ConstrainedBox()
    //     0xb1c1a0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb1c1a4: mov             x2, x0
    // 0xb1c1a8: ldur            x0, [fp, #-0x20]
    // 0xb1c1ac: stur            x2, [fp, #-0x28]
    // 0xb1c1b0: StoreField: r2->field_f = r0
    //     0xb1c1b0: stur            w0, [x2, #0xf]
    // 0xb1c1b4: ldur            x0, [fp, #-0x50]
    // 0xb1c1b8: StoreField: r2->field_b = r0
    //     0xb1c1b8: stur            w0, [x2, #0xb]
    // 0xb1c1bc: ldur            x0, [fp, #-0x18]
    // 0xb1c1c0: LoadField: r1 = r0->field_b
    //     0xb1c1c0: ldur            w1, [x0, #0xb]
    // 0xb1c1c4: LoadField: r3 = r0->field_f
    //     0xb1c1c4: ldur            w3, [x0, #0xf]
    // 0xb1c1c8: DecompressPointer r3
    //     0xb1c1c8: add             x3, x3, HEAP, lsl #32
    // 0xb1c1cc: LoadField: r4 = r3->field_b
    //     0xb1c1cc: ldur            w4, [x3, #0xb]
    // 0xb1c1d0: r3 = LoadInt32Instr(r1)
    //     0xb1c1d0: sbfx            x3, x1, #1, #0x1f
    // 0xb1c1d4: stur            x3, [fp, #-0x40]
    // 0xb1c1d8: r1 = LoadInt32Instr(r4)
    //     0xb1c1d8: sbfx            x1, x4, #1, #0x1f
    // 0xb1c1dc: cmp             x3, x1
    // 0xb1c1e0: b.ne            #0xb1c1ec
    // 0xb1c1e4: mov             x1, x0
    // 0xb1c1e8: r0 = _growToNextCapacity()
    //     0xb1c1e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1c1ec: ldur            x4, [fp, #-0x10]
    // 0xb1c1f0: ldur            x2, [fp, #-0x18]
    // 0xb1c1f4: ldur            x3, [fp, #-0x40]
    // 0xb1c1f8: add             x0, x3, #1
    // 0xb1c1fc: lsl             x1, x0, #1
    // 0xb1c200: StoreField: r2->field_b = r1
    //     0xb1c200: stur            w1, [x2, #0xb]
    // 0xb1c204: LoadField: r1 = r2->field_f
    //     0xb1c204: ldur            w1, [x2, #0xf]
    // 0xb1c208: DecompressPointer r1
    //     0xb1c208: add             x1, x1, HEAP, lsl #32
    // 0xb1c20c: ldur            x0, [fp, #-0x28]
    // 0xb1c210: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1c210: add             x25, x1, x3, lsl #2
    //     0xb1c214: add             x25, x25, #0xf
    //     0xb1c218: str             w0, [x25]
    //     0xb1c21c: tbz             w0, #0, #0xb1c238
    //     0xb1c220: ldurb           w16, [x1, #-1]
    //     0xb1c224: ldurb           w17, [x0, #-1]
    //     0xb1c228: and             x16, x17, x16, lsr #2
    //     0xb1c22c: tst             x16, HEAP, lsr #32
    //     0xb1c230: b.eq            #0xb1c238
    //     0xb1c234: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1c238: LoadField: r0 = r4->field_f
    //     0xb1c238: ldur            w0, [x4, #0xf]
    // 0xb1c23c: DecompressPointer r0
    //     0xb1c23c: add             x0, x0, HEAP, lsl #32
    // 0xb1c240: LoadField: r1 = r0->field_b
    //     0xb1c240: ldur            w1, [x0, #0xb]
    // 0xb1c244: DecompressPointer r1
    //     0xb1c244: add             x1, x1, HEAP, lsl #32
    // 0xb1c248: cmp             w1, NULL
    // 0xb1c24c: b.eq            #0xb1c588
    // 0xb1c250: LoadField: r0 = r1->field_b
    //     0xb1c250: ldur            w0, [x1, #0xb]
    // 0xb1c254: DecompressPointer r0
    //     0xb1c254: add             x0, x0, HEAP, lsl #32
    // 0xb1c258: r1 = LoadClassIdInstr(r0)
    //     0xb1c258: ldur            x1, [x0, #-1]
    //     0xb1c25c: ubfx            x1, x1, #0xc, #0x14
    // 0xb1c260: str             x0, [SP]
    // 0xb1c264: mov             x0, x1
    // 0xb1c268: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1c268: movz            x17, #0xc898
    //     0xb1c26c: add             lr, x0, x17
    //     0xb1c270: ldr             lr, [x21, lr, lsl #3]
    //     0xb1c274: blr             lr
    // 0xb1c278: r1 = LoadInt32Instr(r0)
    //     0xb1c278: sbfx            x1, x0, #1, #0x1f
    // 0xb1c27c: cmp             x1, #1
    // 0xb1c280: b.le            #0xb1c450
    // 0xb1c284: ldur            x2, [fp, #-0x10]
    // 0xb1c288: ldur            x1, [fp, #-0x18]
    // 0xb1c28c: LoadField: r0 = r2->field_f
    //     0xb1c28c: ldur            w0, [x2, #0xf]
    // 0xb1c290: DecompressPointer r0
    //     0xb1c290: add             x0, x0, HEAP, lsl #32
    // 0xb1c294: LoadField: r3 = r0->field_b
    //     0xb1c294: ldur            w3, [x0, #0xb]
    // 0xb1c298: DecompressPointer r3
    //     0xb1c298: add             x3, x3, HEAP, lsl #32
    // 0xb1c29c: cmp             w3, NULL
    // 0xb1c2a0: b.eq            #0xb1c58c
    // 0xb1c2a4: LoadField: r0 = r3->field_b
    //     0xb1c2a4: ldur            w0, [x3, #0xb]
    // 0xb1c2a8: DecompressPointer r0
    //     0xb1c2a8: add             x0, x0, HEAP, lsl #32
    // 0xb1c2ac: r3 = LoadClassIdInstr(r0)
    //     0xb1c2ac: ldur            x3, [x0, #-1]
    //     0xb1c2b0: ubfx            x3, x3, #0xc, #0x14
    // 0xb1c2b4: str             x0, [SP]
    // 0xb1c2b8: mov             x0, x3
    // 0xb1c2bc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1c2bc: movz            x17, #0xc898
    //     0xb1c2c0: add             lr, x0, x17
    //     0xb1c2c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb1c2c8: blr             lr
    // 0xb1c2cc: mov             x2, x0
    // 0xb1c2d0: ldur            x0, [fp, #-0x10]
    // 0xb1c2d4: stur            x2, [fp, #-0x20]
    // 0xb1c2d8: LoadField: r1 = r0->field_f
    //     0xb1c2d8: ldur            w1, [x0, #0xf]
    // 0xb1c2dc: DecompressPointer r1
    //     0xb1c2dc: add             x1, x1, HEAP, lsl #32
    // 0xb1c2e0: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xb1c2e0: ldur            x3, [x1, #0x17]
    // 0xb1c2e4: stur            x3, [fp, #-0x40]
    // 0xb1c2e8: LoadField: r1 = r0->field_13
    //     0xb1c2e8: ldur            w1, [x0, #0x13]
    // 0xb1c2ec: DecompressPointer r1
    //     0xb1c2ec: add             x1, x1, HEAP, lsl #32
    // 0xb1c2f0: r0 = of()
    //     0xb1c2f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1c2f4: LoadField: r1 = r0->field_5b
    //     0xb1c2f4: ldur            w1, [x0, #0x5b]
    // 0xb1c2f8: DecompressPointer r1
    //     0xb1c2f8: add             x1, x1, HEAP, lsl #32
    // 0xb1c2fc: ldur            x0, [fp, #-0x20]
    // 0xb1c300: stur            x1, [fp, #-0x10]
    // 0xb1c304: r2 = LoadInt32Instr(r0)
    //     0xb1c304: sbfx            x2, x0, #1, #0x1f
    // 0xb1c308: stur            x2, [fp, #-0x58]
    // 0xb1c30c: r0 = CarouselIndicator()
    //     0xb1c30c: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb1c310: mov             x3, x0
    // 0xb1c314: ldur            x0, [fp, #-0x58]
    // 0xb1c318: stur            x3, [fp, #-0x20]
    // 0xb1c31c: StoreField: r3->field_b = r0
    //     0xb1c31c: stur            x0, [x3, #0xb]
    // 0xb1c320: ldur            x0, [fp, #-0x40]
    // 0xb1c324: StoreField: r3->field_13 = r0
    //     0xb1c324: stur            x0, [x3, #0x13]
    // 0xb1c328: ldur            x0, [fp, #-0x10]
    // 0xb1c32c: StoreField: r3->field_1b = r0
    //     0xb1c32c: stur            w0, [x3, #0x1b]
    // 0xb1c330: r0 = Instance_Color
    //     0xb1c330: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb1c334: ldr             x0, [x0, #0x90]
    // 0xb1c338: StoreField: r3->field_1f = r0
    //     0xb1c338: stur            w0, [x3, #0x1f]
    // 0xb1c33c: r1 = Null
    //     0xb1c33c: mov             x1, NULL
    // 0xb1c340: r2 = 2
    //     0xb1c340: movz            x2, #0x2
    // 0xb1c344: r0 = AllocateArray()
    //     0xb1c344: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1c348: mov             x2, x0
    // 0xb1c34c: ldur            x0, [fp, #-0x20]
    // 0xb1c350: stur            x2, [fp, #-0x10]
    // 0xb1c354: StoreField: r2->field_f = r0
    //     0xb1c354: stur            w0, [x2, #0xf]
    // 0xb1c358: r1 = <Widget>
    //     0xb1c358: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1c35c: r0 = AllocateGrowableArray()
    //     0xb1c35c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1c360: mov             x1, x0
    // 0xb1c364: ldur            x0, [fp, #-0x10]
    // 0xb1c368: stur            x1, [fp, #-0x20]
    // 0xb1c36c: StoreField: r1->field_f = r0
    //     0xb1c36c: stur            w0, [x1, #0xf]
    // 0xb1c370: r0 = 2
    //     0xb1c370: movz            x0, #0x2
    // 0xb1c374: StoreField: r1->field_b = r0
    //     0xb1c374: stur            w0, [x1, #0xb]
    // 0xb1c378: r0 = Row()
    //     0xb1c378: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1c37c: mov             x2, x0
    // 0xb1c380: r0 = Instance_Axis
    //     0xb1c380: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1c384: stur            x2, [fp, #-0x10]
    // 0xb1c388: StoreField: r2->field_f = r0
    //     0xb1c388: stur            w0, [x2, #0xf]
    // 0xb1c38c: r0 = Instance_MainAxisAlignment
    //     0xb1c38c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb1c390: ldr             x0, [x0, #0xab0]
    // 0xb1c394: StoreField: r2->field_13 = r0
    //     0xb1c394: stur            w0, [x2, #0x13]
    // 0xb1c398: r0 = Instance_MainAxisSize
    //     0xb1c398: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1c39c: ldr             x0, [x0, #0xa10]
    // 0xb1c3a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1c3a0: stur            w0, [x2, #0x17]
    // 0xb1c3a4: r0 = Instance_CrossAxisAlignment
    //     0xb1c3a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1c3a8: ldr             x0, [x0, #0xa18]
    // 0xb1c3ac: StoreField: r2->field_1b = r0
    //     0xb1c3ac: stur            w0, [x2, #0x1b]
    // 0xb1c3b0: r0 = Instance_VerticalDirection
    //     0xb1c3b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1c3b4: ldr             x0, [x0, #0xa20]
    // 0xb1c3b8: StoreField: r2->field_23 = r0
    //     0xb1c3b8: stur            w0, [x2, #0x23]
    // 0xb1c3bc: r3 = Instance_Clip
    //     0xb1c3bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1c3c0: ldr             x3, [x3, #0x38]
    // 0xb1c3c4: StoreField: r2->field_2b = r3
    //     0xb1c3c4: stur            w3, [x2, #0x2b]
    // 0xb1c3c8: StoreField: r2->field_2f = rZR
    //     0xb1c3c8: stur            xzr, [x2, #0x2f]
    // 0xb1c3cc: ldur            x1, [fp, #-0x20]
    // 0xb1c3d0: StoreField: r2->field_b = r1
    //     0xb1c3d0: stur            w1, [x2, #0xb]
    // 0xb1c3d4: ldur            x4, [fp, #-0x18]
    // 0xb1c3d8: LoadField: r1 = r4->field_b
    //     0xb1c3d8: ldur            w1, [x4, #0xb]
    // 0xb1c3dc: LoadField: r5 = r4->field_f
    //     0xb1c3dc: ldur            w5, [x4, #0xf]
    // 0xb1c3e0: DecompressPointer r5
    //     0xb1c3e0: add             x5, x5, HEAP, lsl #32
    // 0xb1c3e4: LoadField: r6 = r5->field_b
    //     0xb1c3e4: ldur            w6, [x5, #0xb]
    // 0xb1c3e8: r5 = LoadInt32Instr(r1)
    //     0xb1c3e8: sbfx            x5, x1, #1, #0x1f
    // 0xb1c3ec: stur            x5, [fp, #-0x40]
    // 0xb1c3f0: r1 = LoadInt32Instr(r6)
    //     0xb1c3f0: sbfx            x1, x6, #1, #0x1f
    // 0xb1c3f4: cmp             x5, x1
    // 0xb1c3f8: b.ne            #0xb1c404
    // 0xb1c3fc: mov             x1, x4
    // 0xb1c400: r0 = _growToNextCapacity()
    //     0xb1c400: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1c404: ldur            x2, [fp, #-0x18]
    // 0xb1c408: ldur            x3, [fp, #-0x40]
    // 0xb1c40c: add             x0, x3, #1
    // 0xb1c410: lsl             x1, x0, #1
    // 0xb1c414: StoreField: r2->field_b = r1
    //     0xb1c414: stur            w1, [x2, #0xb]
    // 0xb1c418: LoadField: r1 = r2->field_f
    //     0xb1c418: ldur            w1, [x2, #0xf]
    // 0xb1c41c: DecompressPointer r1
    //     0xb1c41c: add             x1, x1, HEAP, lsl #32
    // 0xb1c420: ldur            x0, [fp, #-0x10]
    // 0xb1c424: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1c424: add             x25, x1, x3, lsl #2
    //     0xb1c428: add             x25, x25, #0xf
    //     0xb1c42c: str             w0, [x25]
    //     0xb1c430: tbz             w0, #0, #0xb1c44c
    //     0xb1c434: ldurb           w16, [x1, #-1]
    //     0xb1c438: ldurb           w17, [x0, #-1]
    //     0xb1c43c: and             x16, x17, x16, lsr #2
    //     0xb1c440: tst             x16, HEAP, lsr #32
    //     0xb1c444: b.eq            #0xb1c44c
    //     0xb1c448: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1c44c: b               #0xb1c4e0
    // 0xb1c450: ldur            x2, [fp, #-0x18]
    // 0xb1c454: r0 = Container()
    //     0xb1c454: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1c458: mov             x1, x0
    // 0xb1c45c: stur            x0, [fp, #-0x10]
    // 0xb1c460: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1c460: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1c464: r0 = Container()
    //     0xb1c464: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1c468: ldur            x0, [fp, #-0x18]
    // 0xb1c46c: LoadField: r1 = r0->field_b
    //     0xb1c46c: ldur            w1, [x0, #0xb]
    // 0xb1c470: LoadField: r2 = r0->field_f
    //     0xb1c470: ldur            w2, [x0, #0xf]
    // 0xb1c474: DecompressPointer r2
    //     0xb1c474: add             x2, x2, HEAP, lsl #32
    // 0xb1c478: LoadField: r3 = r2->field_b
    //     0xb1c478: ldur            w3, [x2, #0xb]
    // 0xb1c47c: r2 = LoadInt32Instr(r1)
    //     0xb1c47c: sbfx            x2, x1, #1, #0x1f
    // 0xb1c480: stur            x2, [fp, #-0x40]
    // 0xb1c484: r1 = LoadInt32Instr(r3)
    //     0xb1c484: sbfx            x1, x3, #1, #0x1f
    // 0xb1c488: cmp             x2, x1
    // 0xb1c48c: b.ne            #0xb1c498
    // 0xb1c490: mov             x1, x0
    // 0xb1c494: r0 = _growToNextCapacity()
    //     0xb1c494: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1c498: ldur            x2, [fp, #-0x18]
    // 0xb1c49c: ldur            x3, [fp, #-0x40]
    // 0xb1c4a0: add             x0, x3, #1
    // 0xb1c4a4: lsl             x1, x0, #1
    // 0xb1c4a8: StoreField: r2->field_b = r1
    //     0xb1c4a8: stur            w1, [x2, #0xb]
    // 0xb1c4ac: LoadField: r1 = r2->field_f
    //     0xb1c4ac: ldur            w1, [x2, #0xf]
    // 0xb1c4b0: DecompressPointer r1
    //     0xb1c4b0: add             x1, x1, HEAP, lsl #32
    // 0xb1c4b4: ldur            x0, [fp, #-0x10]
    // 0xb1c4b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1c4b8: add             x25, x1, x3, lsl #2
    //     0xb1c4bc: add             x25, x25, #0xf
    //     0xb1c4c0: str             w0, [x25]
    //     0xb1c4c4: tbz             w0, #0, #0xb1c4e0
    //     0xb1c4c8: ldurb           w16, [x1, #-1]
    //     0xb1c4cc: ldurb           w17, [x0, #-1]
    //     0xb1c4d0: and             x16, x17, x16, lsr #2
    //     0xb1c4d4: tst             x16, HEAP, lsr #32
    //     0xb1c4d8: b.eq            #0xb1c4e0
    //     0xb1c4dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1c4e0: ldur            x0, [fp, #-8]
    // 0xb1c4e4: r0 = Column()
    //     0xb1c4e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1c4e8: mov             x1, x0
    // 0xb1c4ec: r0 = Instance_Axis
    //     0xb1c4ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1c4f0: stur            x1, [fp, #-0x10]
    // 0xb1c4f4: StoreField: r1->field_f = r0
    //     0xb1c4f4: stur            w0, [x1, #0xf]
    // 0xb1c4f8: r0 = Instance_MainAxisAlignment
    //     0xb1c4f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1c4fc: ldr             x0, [x0, #0xa08]
    // 0xb1c500: StoreField: r1->field_13 = r0
    //     0xb1c500: stur            w0, [x1, #0x13]
    // 0xb1c504: r0 = Instance_MainAxisSize
    //     0xb1c504: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1c508: ldr             x0, [x0, #0xdd0]
    // 0xb1c50c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1c50c: stur            w0, [x1, #0x17]
    // 0xb1c510: ldur            x0, [fp, #-8]
    // 0xb1c514: StoreField: r1->field_1b = r0
    //     0xb1c514: stur            w0, [x1, #0x1b]
    // 0xb1c518: r0 = Instance_VerticalDirection
    //     0xb1c518: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1c51c: ldr             x0, [x0, #0xa20]
    // 0xb1c520: StoreField: r1->field_23 = r0
    //     0xb1c520: stur            w0, [x1, #0x23]
    // 0xb1c524: r0 = Instance_Clip
    //     0xb1c524: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1c528: ldr             x0, [x0, #0x38]
    // 0xb1c52c: StoreField: r1->field_2b = r0
    //     0xb1c52c: stur            w0, [x1, #0x2b]
    // 0xb1c530: StoreField: r1->field_2f = rZR
    //     0xb1c530: stur            xzr, [x1, #0x2f]
    // 0xb1c534: ldur            x0, [fp, #-0x18]
    // 0xb1c538: StoreField: r1->field_b = r0
    //     0xb1c538: stur            w0, [x1, #0xb]
    // 0xb1c53c: r0 = Padding()
    //     0xb1c53c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1c540: r1 = Instance_EdgeInsets
    //     0xb1c540: add             x1, PP, #0x55, lsl #12  ; [pp+0x550a8] Obj!EdgeInsets@d58761
    //     0xb1c544: ldr             x1, [x1, #0xa8]
    // 0xb1c548: StoreField: r0->field_f = r1
    //     0xb1c548: stur            w1, [x0, #0xf]
    // 0xb1c54c: ldur            x1, [fp, #-0x10]
    // 0xb1c550: StoreField: r0->field_b = r1
    //     0xb1c550: stur            w1, [x0, #0xb]
    // 0xb1c554: LeaveFrame
    //     0xb1c554: mov             SP, fp
    //     0xb1c558: ldp             fp, lr, [SP], #0x10
    // 0xb1c55c: ret
    //     0xb1c55c: ret             
    // 0xb1c560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1c560: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1c564: b               #0xb1bac4
    // 0xb1c568: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c568: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c56c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c56c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c570: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c570: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c574: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c574: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c578: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c578: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c57c: r9 = _pageController
    //     0xb1c57c: add             x9, PP, #0x57, lsl #12  ; [pp+0x576b8] Field <_TestimonialCarouselState@1507194687._pageController@1507194687>: late (offset: 0x14)
    //     0xb1c580: ldr             x9, [x9, #0x6b8]
    // 0xb1c584: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb1c584: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb1c588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1c58c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c58c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1c590, size: 0x70
    // 0xb1c590: EnterFrame
    //     0xb1c590: stp             fp, lr, [SP, #-0x10]!
    //     0xb1c594: mov             fp, SP
    // 0xb1c598: ldr             x0, [fp, #0x20]
    // 0xb1c59c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1c59c: ldur            w1, [x0, #0x17]
    // 0xb1c5a0: DecompressPointer r1
    //     0xb1c5a0: add             x1, x1, HEAP, lsl #32
    // 0xb1c5a4: CheckStackOverflow
    //     0xb1c5a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1c5a8: cmp             SP, x16
    //     0xb1c5ac: b.ls            #0xb1c5f4
    // 0xb1c5b0: LoadField: r0 = r1->field_f
    //     0xb1c5b0: ldur            w0, [x1, #0xf]
    // 0xb1c5b4: DecompressPointer r0
    //     0xb1c5b4: add             x0, x0, HEAP, lsl #32
    // 0xb1c5b8: LoadField: r1 = r0->field_b
    //     0xb1c5b8: ldur            w1, [x0, #0xb]
    // 0xb1c5bc: DecompressPointer r1
    //     0xb1c5bc: add             x1, x1, HEAP, lsl #32
    // 0xb1c5c0: cmp             w1, NULL
    // 0xb1c5c4: b.eq            #0xb1c5fc
    // 0xb1c5c8: LoadField: r2 = r1->field_b
    //     0xb1c5c8: ldur            w2, [x1, #0xb]
    // 0xb1c5cc: DecompressPointer r2
    //     0xb1c5cc: add             x2, x2, HEAP, lsl #32
    // 0xb1c5d0: ldr             x1, [fp, #0x10]
    // 0xb1c5d4: r3 = LoadInt32Instr(r1)
    //     0xb1c5d4: sbfx            x3, x1, #1, #0x1f
    //     0xb1c5d8: tbz             w1, #0, #0xb1c5e0
    //     0xb1c5dc: ldur            x3, [x1, #7]
    // 0xb1c5e0: mov             x1, x0
    // 0xb1c5e4: r0 = cosmeticThemeSlider()
    //     0xb1c5e4: bl              #0xb1c600  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider
    // 0xb1c5e8: LeaveFrame
    //     0xb1c5e8: mov             SP, fp
    //     0xb1c5ec: ldp             fp, lr, [SP], #0x10
    // 0xb1c5f0: ret
    //     0xb1c5f0: ret             
    // 0xb1c5f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1c5f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1c5f8: b               #0xb1c5b0
    // 0xb1c5fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1c5fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xb1c600, size: 0xfd0
    // 0xb1c600: EnterFrame
    //     0xb1c600: stp             fp, lr, [SP, #-0x10]!
    //     0xb1c604: mov             fp, SP
    // 0xb1c608: AllocStack(0x70)
    //     0xb1c608: sub             SP, SP, #0x70
    // 0xb1c60c: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb1c60c: stur            x1, [fp, #-8]
    //     0xb1c610: stur            x2, [fp, #-0x10]
    //     0xb1c614: stur            x3, [fp, #-0x18]
    // 0xb1c618: CheckStackOverflow
    //     0xb1c618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1c61c: cmp             SP, x16
    //     0xb1c620: b.ls            #0xb1d57c
    // 0xb1c624: r1 = 2
    //     0xb1c624: movz            x1, #0x2
    // 0xb1c628: r0 = AllocateContext()
    //     0xb1c628: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1c62c: mov             x3, x0
    // 0xb1c630: ldur            x2, [fp, #-0x10]
    // 0xb1c634: stur            x3, [fp, #-0x28]
    // 0xb1c638: StoreField: r3->field_f = r2
    //     0xb1c638: stur            w2, [x3, #0xf]
    // 0xb1c63c: ldur            x4, [fp, #-0x18]
    // 0xb1c640: r0 = BoxInt64Instr(r4)
    //     0xb1c640: sbfiz           x0, x4, #1, #0x1f
    //     0xb1c644: cmp             x4, x0, asr #1
    //     0xb1c648: b.eq            #0xb1c654
    //     0xb1c64c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb1c650: stur            x4, [x0, #7]
    // 0xb1c654: stur            x0, [fp, #-0x20]
    // 0xb1c658: StoreField: r3->field_13 = r0
    //     0xb1c658: stur            w0, [x3, #0x13]
    // 0xb1c65c: r0 = Radius()
    //     0xb1c65c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1c660: d0 = 12.000000
    //     0xb1c660: fmov            d0, #12.00000000
    // 0xb1c664: stur            x0, [fp, #-0x30]
    // 0xb1c668: StoreField: r0->field_7 = d0
    //     0xb1c668: stur            d0, [x0, #7]
    // 0xb1c66c: StoreField: r0->field_f = d0
    //     0xb1c66c: stur            d0, [x0, #0xf]
    // 0xb1c670: r0 = BorderRadius()
    //     0xb1c670: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1c674: mov             x1, x0
    // 0xb1c678: ldur            x0, [fp, #-0x30]
    // 0xb1c67c: stur            x1, [fp, #-0x38]
    // 0xb1c680: StoreField: r1->field_7 = r0
    //     0xb1c680: stur            w0, [x1, #7]
    // 0xb1c684: StoreField: r1->field_b = r0
    //     0xb1c684: stur            w0, [x1, #0xb]
    // 0xb1c688: StoreField: r1->field_f = r0
    //     0xb1c688: stur            w0, [x1, #0xf]
    // 0xb1c68c: StoreField: r1->field_13 = r0
    //     0xb1c68c: stur            w0, [x1, #0x13]
    // 0xb1c690: r0 = RoundedRectangleBorder()
    //     0xb1c690: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb1c694: mov             x1, x0
    // 0xb1c698: ldur            x0, [fp, #-0x38]
    // 0xb1c69c: stur            x1, [fp, #-0x30]
    // 0xb1c6a0: StoreField: r1->field_b = r0
    //     0xb1c6a0: stur            w0, [x1, #0xb]
    // 0xb1c6a4: r0 = Instance_BorderSide
    //     0xb1c6a4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb1c6a8: ldr             x0, [x0, #0xe20]
    // 0xb1c6ac: StoreField: r1->field_7 = r0
    //     0xb1c6ac: stur            w0, [x1, #7]
    // 0xb1c6b0: ldur            x16, [fp, #-0x10]
    // 0xb1c6b4: ldur            lr, [fp, #-0x20]
    // 0xb1c6b8: stp             lr, x16, [SP]
    // 0xb1c6bc: r4 = 0
    //     0xb1c6bc: movz            x4, #0
    // 0xb1c6c0: ldr             x0, [SP, #8]
    // 0xb1c6c4: r16 = UnlinkedCall_0x613b5c
    //     0xb1c6c4: add             x16, PP, #0x57, lsl #12  ; [pp+0x576c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1c6c8: add             x16, x16, #0x6c0
    // 0xb1c6cc: ldp             x5, lr, [x16]
    // 0xb1c6d0: blr             lr
    // 0xb1c6d4: cmp             w0, NULL
    // 0xb1c6d8: b.eq            #0xb1d584
    // 0xb1c6dc: LoadField: r1 = r0->field_cb
    //     0xb1c6dc: ldur            w1, [x0, #0xcb]
    // 0xb1c6e0: DecompressPointer r1
    //     0xb1c6e0: add             x1, x1, HEAP, lsl #32
    // 0xb1c6e4: cmp             w1, NULL
    // 0xb1c6e8: b.ne            #0xb1c6f0
    // 0xb1c6ec: r1 = ""
    //     0xb1c6ec: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1c6f0: ldur            x0, [fp, #-8]
    // 0xb1c6f4: ldur            x2, [fp, #-0x28]
    // 0xb1c6f8: r0 = capitalizeFirstWord()
    //     0xb1c6f8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb1c6fc: mov             x2, x0
    // 0xb1c700: ldur            x0, [fp, #-8]
    // 0xb1c704: stur            x2, [fp, #-0x10]
    // 0xb1c708: LoadField: r1 = r0->field_f
    //     0xb1c708: ldur            w1, [x0, #0xf]
    // 0xb1c70c: DecompressPointer r1
    //     0xb1c70c: add             x1, x1, HEAP, lsl #32
    // 0xb1c710: cmp             w1, NULL
    // 0xb1c714: b.eq            #0xb1d588
    // 0xb1c718: r0 = of()
    //     0xb1c718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1c71c: LoadField: r1 = r0->field_87
    //     0xb1c71c: ldur            w1, [x0, #0x87]
    // 0xb1c720: DecompressPointer r1
    //     0xb1c720: add             x1, x1, HEAP, lsl #32
    // 0xb1c724: LoadField: r0 = r1->field_7
    //     0xb1c724: ldur            w0, [x1, #7]
    // 0xb1c728: DecompressPointer r0
    //     0xb1c728: add             x0, x0, HEAP, lsl #32
    // 0xb1c72c: r16 = 32.000000
    //     0xb1c72c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb1c730: ldr             x16, [x16, #0x848]
    // 0xb1c734: r30 = Instance_Color
    //     0xb1c734: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1c738: stp             lr, x16, [SP]
    // 0xb1c73c: mov             x1, x0
    // 0xb1c740: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1c740: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1c744: ldr             x4, [x4, #0xaa0]
    // 0xb1c748: r0 = copyWith()
    //     0xb1c748: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1c74c: stur            x0, [fp, #-0x20]
    // 0xb1c750: r0 = Text()
    //     0xb1c750: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1c754: mov             x3, x0
    // 0xb1c758: ldur            x0, [fp, #-0x10]
    // 0xb1c75c: stur            x3, [fp, #-0x38]
    // 0xb1c760: StoreField: r3->field_b = r0
    //     0xb1c760: stur            w0, [x3, #0xb]
    // 0xb1c764: ldur            x0, [fp, #-0x20]
    // 0xb1c768: StoreField: r3->field_13 = r0
    //     0xb1c768: stur            w0, [x3, #0x13]
    // 0xb1c76c: r1 = <Widget>
    //     0xb1c76c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1c770: r2 = 18
    //     0xb1c770: movz            x2, #0x12
    // 0xb1c774: r0 = AllocateArray()
    //     0xb1c774: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1c778: mov             x3, x0
    // 0xb1c77c: ldur            x0, [fp, #-0x38]
    // 0xb1c780: stur            x3, [fp, #-0x10]
    // 0xb1c784: StoreField: r3->field_f = r0
    //     0xb1c784: stur            w0, [x3, #0xf]
    // 0xb1c788: r16 = Instance_SizedBox
    //     0xb1c788: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb1c78c: ldr             x16, [x16, #0xd8]
    // 0xb1c790: StoreField: r3->field_13 = r16
    //     0xb1c790: stur            w16, [x3, #0x13]
    // 0xb1c794: r1 = <Widget>
    //     0xb1c794: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1c798: r2 = 18
    //     0xb1c798: movz            x2, #0x12
    // 0xb1c79c: r0 = AllocateArray()
    //     0xb1c79c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1c7a0: stur            x0, [fp, #-0x20]
    // 0xb1c7a4: r16 = Instance_Icon
    //     0xb1c7a4: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xb1c7a8: ldr             x16, [x16, #0xd0]
    // 0xb1c7ac: StoreField: r0->field_f = r16
    //     0xb1c7ac: stur            w16, [x0, #0xf]
    // 0xb1c7b0: r16 = Instance_SizedBox
    //     0xb1c7b0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb1c7b4: ldr             x16, [x16, #0xe98]
    // 0xb1c7b8: StoreField: r0->field_13 = r16
    //     0xb1c7b8: stur            w16, [x0, #0x13]
    // 0xb1c7bc: ldur            x2, [fp, #-8]
    // 0xb1c7c0: LoadField: r1 = r2->field_f
    //     0xb1c7c0: ldur            w1, [x2, #0xf]
    // 0xb1c7c4: DecompressPointer r1
    //     0xb1c7c4: add             x1, x1, HEAP, lsl #32
    // 0xb1c7c8: cmp             w1, NULL
    // 0xb1c7cc: b.eq            #0xb1d58c
    // 0xb1c7d0: r0 = of()
    //     0xb1c7d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1c7d4: LoadField: r1 = r0->field_87
    //     0xb1c7d4: ldur            w1, [x0, #0x87]
    // 0xb1c7d8: DecompressPointer r1
    //     0xb1c7d8: add             x1, x1, HEAP, lsl #32
    // 0xb1c7dc: LoadField: r0 = r1->field_2b
    //     0xb1c7dc: ldur            w0, [x1, #0x2b]
    // 0xb1c7e0: DecompressPointer r0
    //     0xb1c7e0: add             x0, x0, HEAP, lsl #32
    // 0xb1c7e4: stur            x0, [fp, #-0x38]
    // 0xb1c7e8: r1 = Instance_Color
    //     0xb1c7e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1c7ec: d0 = 0.700000
    //     0xb1c7ec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1c7f0: ldr             d0, [x17, #0xf48]
    // 0xb1c7f4: r0 = withOpacity()
    //     0xb1c7f4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1c7f8: r16 = 14.000000
    //     0xb1c7f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1c7fc: ldr             x16, [x16, #0x1d8]
    // 0xb1c800: stp             x0, x16, [SP]
    // 0xb1c804: ldur            x1, [fp, #-0x38]
    // 0xb1c808: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1c808: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1c80c: ldr             x4, [x4, #0xaa0]
    // 0xb1c810: r0 = copyWith()
    //     0xb1c810: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1c814: stur            x0, [fp, #-0x38]
    // 0xb1c818: r0 = Text()
    //     0xb1c818: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1c81c: mov             x1, x0
    // 0xb1c820: r0 = "Verified Buyer"
    //     0xb1c820: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xb1c824: ldr             x0, [x0, #0xd8]
    // 0xb1c828: StoreField: r1->field_b = r0
    //     0xb1c828: stur            w0, [x1, #0xb]
    // 0xb1c82c: ldur            x0, [fp, #-0x38]
    // 0xb1c830: StoreField: r1->field_13 = r0
    //     0xb1c830: stur            w0, [x1, #0x13]
    // 0xb1c834: mov             x0, x1
    // 0xb1c838: ldur            x1, [fp, #-0x20]
    // 0xb1c83c: ArrayStore: r1[2] = r0  ; List_4
    //     0xb1c83c: add             x25, x1, #0x17
    //     0xb1c840: str             w0, [x25]
    //     0xb1c844: tbz             w0, #0, #0xb1c860
    //     0xb1c848: ldurb           w16, [x1, #-1]
    //     0xb1c84c: ldurb           w17, [x0, #-1]
    //     0xb1c850: and             x16, x17, x16, lsr #2
    //     0xb1c854: tst             x16, HEAP, lsr #32
    //     0xb1c858: b.eq            #0xb1c860
    //     0xb1c85c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1c860: r0 = Container()
    //     0xb1c860: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1c864: stur            x0, [fp, #-0x38]
    // 0xb1c868: r16 = 5.000000
    //     0xb1c868: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1c86c: ldr             x16, [x16, #0xcf0]
    // 0xb1c870: str             x16, [SP]
    // 0xb1c874: mov             x1, x0
    // 0xb1c878: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb1c878: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb1c87c: ldr             x4, [x4, #0xe0]
    // 0xb1c880: r0 = Container()
    //     0xb1c880: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1c884: ldur            x1, [fp, #-0x20]
    // 0xb1c888: ldur            x0, [fp, #-0x38]
    // 0xb1c88c: ArrayStore: r1[3] = r0  ; List_4
    //     0xb1c88c: add             x25, x1, #0x1b
    //     0xb1c890: str             w0, [x25]
    //     0xb1c894: tbz             w0, #0, #0xb1c8b0
    //     0xb1c898: ldurb           w16, [x1, #-1]
    //     0xb1c89c: ldurb           w17, [x0, #-1]
    //     0xb1c8a0: and             x16, x17, x16, lsr #2
    //     0xb1c8a4: tst             x16, HEAP, lsr #32
    //     0xb1c8a8: b.eq            #0xb1c8b0
    //     0xb1c8ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1c8b0: ldur            x2, [fp, #-0x28]
    // 0xb1c8b4: LoadField: r0 = r2->field_f
    //     0xb1c8b4: ldur            w0, [x2, #0xf]
    // 0xb1c8b8: DecompressPointer r0
    //     0xb1c8b8: add             x0, x0, HEAP, lsl #32
    // 0xb1c8bc: LoadField: r1 = r2->field_13
    //     0xb1c8bc: ldur            w1, [x2, #0x13]
    // 0xb1c8c0: DecompressPointer r1
    //     0xb1c8c0: add             x1, x1, HEAP, lsl #32
    // 0xb1c8c4: stp             x1, x0, [SP]
    // 0xb1c8c8: r4 = 0
    //     0xb1c8c8: movz            x4, #0
    // 0xb1c8cc: ldr             x0, [SP, #8]
    // 0xb1c8d0: r16 = UnlinkedCall_0x613b5c
    //     0xb1c8d0: add             x16, PP, #0x57, lsl #12  ; [pp+0x576d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1c8d4: add             x16, x16, #0x6d0
    // 0xb1c8d8: ldp             x5, lr, [x16]
    // 0xb1c8dc: blr             lr
    // 0xb1c8e0: cmp             w0, NULL
    // 0xb1c8e4: b.eq            #0xb1c97c
    // 0xb1c8e8: LoadField: r1 = r0->field_c7
    //     0xb1c8e8: ldur            w1, [x0, #0xc7]
    // 0xb1c8ec: DecompressPointer r1
    //     0xb1c8ec: add             x1, x1, HEAP, lsl #32
    // 0xb1c8f0: cmp             w1, NULL
    // 0xb1c8f4: b.eq            #0xb1c97c
    // 0xb1c8f8: ldur            x2, [fp, #-0x28]
    // 0xb1c8fc: LoadField: r0 = r2->field_f
    //     0xb1c8fc: ldur            w0, [x2, #0xf]
    // 0xb1c900: DecompressPointer r0
    //     0xb1c900: add             x0, x0, HEAP, lsl #32
    // 0xb1c904: LoadField: r1 = r2->field_13
    //     0xb1c904: ldur            w1, [x2, #0x13]
    // 0xb1c908: DecompressPointer r1
    //     0xb1c908: add             x1, x1, HEAP, lsl #32
    // 0xb1c90c: stp             x1, x0, [SP]
    // 0xb1c910: r4 = 0
    //     0xb1c910: movz            x4, #0
    // 0xb1c914: ldr             x0, [SP, #8]
    // 0xb1c918: r16 = UnlinkedCall_0x613b5c
    //     0xb1c918: add             x16, PP, #0x57, lsl #12  ; [pp+0x576e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1c91c: add             x16, x16, #0x6e0
    // 0xb1c920: ldp             x5, lr, [x16]
    // 0xb1c924: blr             lr
    // 0xb1c928: cmp             w0, NULL
    // 0xb1c92c: b.ne            #0xb1c938
    // 0xb1c930: r0 = Null
    //     0xb1c930: mov             x0, NULL
    // 0xb1c934: b               #0xb1c968
    // 0xb1c938: LoadField: r1 = r0->field_c7
    //     0xb1c938: ldur            w1, [x0, #0xc7]
    // 0xb1c93c: DecompressPointer r1
    //     0xb1c93c: add             x1, x1, HEAP, lsl #32
    // 0xb1c940: cmp             w1, NULL
    // 0xb1c944: b.ne            #0xb1c950
    // 0xb1c948: r0 = Null
    //     0xb1c948: mov             x0, NULL
    // 0xb1c94c: b               #0xb1c968
    // 0xb1c950: LoadField: r0 = r1->field_7
    //     0xb1c950: ldur            w0, [x1, #7]
    // 0xb1c954: cbnz            w0, #0xb1c960
    // 0xb1c958: r1 = false
    //     0xb1c958: add             x1, NULL, #0x30  ; false
    // 0xb1c95c: b               #0xb1c964
    // 0xb1c960: r1 = true
    //     0xb1c960: add             x1, NULL, #0x20  ; true
    // 0xb1c964: mov             x0, x1
    // 0xb1c968: cmp             w0, NULL
    // 0xb1c96c: b.ne            #0xb1c974
    // 0xb1c970: r0 = false
    //     0xb1c970: add             x0, NULL, #0x30  ; false
    // 0xb1c974: mov             x3, x0
    // 0xb1c978: b               #0xb1c980
    // 0xb1c97c: r3 = false
    //     0xb1c97c: add             x3, NULL, #0x30  ; false
    // 0xb1c980: ldur            x0, [fp, #-8]
    // 0xb1c984: ldur            x2, [fp, #-0x28]
    // 0xb1c988: stur            x3, [fp, #-0x38]
    // 0xb1c98c: LoadField: r1 = r0->field_f
    //     0xb1c98c: ldur            w1, [x0, #0xf]
    // 0xb1c990: DecompressPointer r1
    //     0xb1c990: add             x1, x1, HEAP, lsl #32
    // 0xb1c994: cmp             w1, NULL
    // 0xb1c998: b.eq            #0xb1d590
    // 0xb1c99c: r0 = of()
    //     0xb1c99c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1c9a0: LoadField: r1 = r0->field_5b
    //     0xb1c9a0: ldur            w1, [x0, #0x5b]
    // 0xb1c9a4: DecompressPointer r1
    //     0xb1c9a4: add             x1, x1, HEAP, lsl #32
    // 0xb1c9a8: stur            x1, [fp, #-0x40]
    // 0xb1c9ac: r0 = BoxDecoration()
    //     0xb1c9ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1c9b0: mov             x1, x0
    // 0xb1c9b4: ldur            x0, [fp, #-0x40]
    // 0xb1c9b8: stur            x1, [fp, #-0x48]
    // 0xb1c9bc: StoreField: r1->field_7 = r0
    //     0xb1c9bc: stur            w0, [x1, #7]
    // 0xb1c9c0: r0 = Instance_BoxShape
    //     0xb1c9c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb1c9c4: ldr             x0, [x0, #0x970]
    // 0xb1c9c8: StoreField: r1->field_23 = r0
    //     0xb1c9c8: stur            w0, [x1, #0x23]
    // 0xb1c9cc: r0 = Container()
    //     0xb1c9cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1c9d0: stur            x0, [fp, #-0x40]
    // 0xb1c9d4: r16 = Instance_EdgeInsets
    //     0xb1c9d4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb1c9d8: ldr             x16, [x16, #0x108]
    // 0xb1c9dc: r30 = 5.000000
    //     0xb1c9dc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1c9e0: ldr             lr, [lr, #0xcf0]
    // 0xb1c9e4: stp             lr, x16, [SP, #0x10]
    // 0xb1c9e8: r16 = 5.000000
    //     0xb1c9e8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1c9ec: ldr             x16, [x16, #0xcf0]
    // 0xb1c9f0: ldur            lr, [fp, #-0x48]
    // 0xb1c9f4: stp             lr, x16, [SP]
    // 0xb1c9f8: mov             x1, x0
    // 0xb1c9fc: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb1c9fc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb1ca00: ldr             x4, [x4, #0x118]
    // 0xb1ca04: r0 = Container()
    //     0xb1ca04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1ca08: r0 = Visibility()
    //     0xb1ca08: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb1ca0c: mov             x1, x0
    // 0xb1ca10: ldur            x0, [fp, #-0x40]
    // 0xb1ca14: StoreField: r1->field_b = r0
    //     0xb1ca14: stur            w0, [x1, #0xb]
    // 0xb1ca18: r0 = Instance_SizedBox
    //     0xb1ca18: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb1ca1c: StoreField: r1->field_f = r0
    //     0xb1ca1c: stur            w0, [x1, #0xf]
    // 0xb1ca20: ldur            x0, [fp, #-0x38]
    // 0xb1ca24: StoreField: r1->field_13 = r0
    //     0xb1ca24: stur            w0, [x1, #0x13]
    // 0xb1ca28: r2 = false
    //     0xb1ca28: add             x2, NULL, #0x30  ; false
    // 0xb1ca2c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb1ca2c: stur            w2, [x1, #0x17]
    // 0xb1ca30: StoreField: r1->field_1b = r2
    //     0xb1ca30: stur            w2, [x1, #0x1b]
    // 0xb1ca34: StoreField: r1->field_1f = r2
    //     0xb1ca34: stur            w2, [x1, #0x1f]
    // 0xb1ca38: StoreField: r1->field_23 = r2
    //     0xb1ca38: stur            w2, [x1, #0x23]
    // 0xb1ca3c: StoreField: r1->field_27 = r2
    //     0xb1ca3c: stur            w2, [x1, #0x27]
    // 0xb1ca40: StoreField: r1->field_2b = r2
    //     0xb1ca40: stur            w2, [x1, #0x2b]
    // 0xb1ca44: mov             x0, x1
    // 0xb1ca48: ldur            x1, [fp, #-0x20]
    // 0xb1ca4c: ArrayStore: r1[4] = r0  ; List_4
    //     0xb1ca4c: add             x25, x1, #0x1f
    //     0xb1ca50: str             w0, [x25]
    //     0xb1ca54: tbz             w0, #0, #0xb1ca70
    //     0xb1ca58: ldurb           w16, [x1, #-1]
    //     0xb1ca5c: ldurb           w17, [x0, #-1]
    //     0xb1ca60: and             x16, x17, x16, lsr #2
    //     0xb1ca64: tst             x16, HEAP, lsr #32
    //     0xb1ca68: b.eq            #0xb1ca70
    //     0xb1ca6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1ca70: ldur            x0, [fp, #-0x28]
    // 0xb1ca74: LoadField: r1 = r0->field_f
    //     0xb1ca74: ldur            w1, [x0, #0xf]
    // 0xb1ca78: DecompressPointer r1
    //     0xb1ca78: add             x1, x1, HEAP, lsl #32
    // 0xb1ca7c: LoadField: r3 = r0->field_13
    //     0xb1ca7c: ldur            w3, [x0, #0x13]
    // 0xb1ca80: DecompressPointer r3
    //     0xb1ca80: add             x3, x3, HEAP, lsl #32
    // 0xb1ca84: stp             x3, x1, [SP]
    // 0xb1ca88: r4 = 0
    //     0xb1ca88: movz            x4, #0
    // 0xb1ca8c: ldr             x0, [SP, #8]
    // 0xb1ca90: r16 = UnlinkedCall_0x613b5c
    //     0xb1ca90: add             x16, PP, #0x57, lsl #12  ; [pp+0x576f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ca94: add             x16, x16, #0x6f0
    // 0xb1ca98: ldp             x5, lr, [x16]
    // 0xb1ca9c: blr             lr
    // 0xb1caa0: cmp             w0, NULL
    // 0xb1caa4: b.ne            #0xb1cab0
    // 0xb1caa8: r0 = Null
    //     0xb1caa8: mov             x0, NULL
    // 0xb1caac: b               #0xb1cabc
    // 0xb1cab0: LoadField: r1 = r0->field_c7
    //     0xb1cab0: ldur            w1, [x0, #0xc7]
    // 0xb1cab4: DecompressPointer r1
    //     0xb1cab4: add             x1, x1, HEAP, lsl #32
    // 0xb1cab8: mov             x0, x1
    // 0xb1cabc: cmp             w0, NULL
    // 0xb1cac0: b.ne            #0xb1cacc
    // 0xb1cac4: r3 = ""
    //     0xb1cac4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1cac8: b               #0xb1cad0
    // 0xb1cacc: mov             x3, x0
    // 0xb1cad0: ldur            x0, [fp, #-8]
    // 0xb1cad4: ldur            x2, [fp, #-0x28]
    // 0xb1cad8: stur            x3, [fp, #-0x38]
    // 0xb1cadc: LoadField: r1 = r0->field_f
    //     0xb1cadc: ldur            w1, [x0, #0xf]
    // 0xb1cae0: DecompressPointer r1
    //     0xb1cae0: add             x1, x1, HEAP, lsl #32
    // 0xb1cae4: cmp             w1, NULL
    // 0xb1cae8: b.eq            #0xb1d594
    // 0xb1caec: r0 = of()
    //     0xb1caec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1caf0: LoadField: r1 = r0->field_87
    //     0xb1caf0: ldur            w1, [x0, #0x87]
    // 0xb1caf4: DecompressPointer r1
    //     0xb1caf4: add             x1, x1, HEAP, lsl #32
    // 0xb1caf8: LoadField: r0 = r1->field_2b
    //     0xb1caf8: ldur            w0, [x1, #0x2b]
    // 0xb1cafc: DecompressPointer r0
    //     0xb1cafc: add             x0, x0, HEAP, lsl #32
    // 0xb1cb00: stur            x0, [fp, #-0x40]
    // 0xb1cb04: r1 = Instance_Color
    //     0xb1cb04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1cb08: d0 = 0.700000
    //     0xb1cb08: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1cb0c: ldr             d0, [x17, #0xf48]
    // 0xb1cb10: r0 = withOpacity()
    //     0xb1cb10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1cb14: r16 = 14.000000
    //     0xb1cb14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1cb18: ldr             x16, [x16, #0x1d8]
    // 0xb1cb1c: stp             x0, x16, [SP]
    // 0xb1cb20: ldur            x1, [fp, #-0x40]
    // 0xb1cb24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1cb24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1cb28: ldr             x4, [x4, #0xaa0]
    // 0xb1cb2c: r0 = copyWith()
    //     0xb1cb2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1cb30: stur            x0, [fp, #-0x40]
    // 0xb1cb34: r0 = Text()
    //     0xb1cb34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1cb38: mov             x1, x0
    // 0xb1cb3c: ldur            x0, [fp, #-0x38]
    // 0xb1cb40: StoreField: r1->field_b = r0
    //     0xb1cb40: stur            w0, [x1, #0xb]
    // 0xb1cb44: ldur            x0, [fp, #-0x40]
    // 0xb1cb48: StoreField: r1->field_13 = r0
    //     0xb1cb48: stur            w0, [x1, #0x13]
    // 0xb1cb4c: mov             x0, x1
    // 0xb1cb50: ldur            x1, [fp, #-0x20]
    // 0xb1cb54: ArrayStore: r1[5] = r0  ; List_4
    //     0xb1cb54: add             x25, x1, #0x23
    //     0xb1cb58: str             w0, [x25]
    //     0xb1cb5c: tbz             w0, #0, #0xb1cb78
    //     0xb1cb60: ldurb           w16, [x1, #-1]
    //     0xb1cb64: ldurb           w17, [x0, #-1]
    //     0xb1cb68: and             x16, x17, x16, lsr #2
    //     0xb1cb6c: tst             x16, HEAP, lsr #32
    //     0xb1cb70: b.eq            #0xb1cb78
    //     0xb1cb74: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1cb78: r0 = Container()
    //     0xb1cb78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1cb7c: stur            x0, [fp, #-0x38]
    // 0xb1cb80: r16 = 5.000000
    //     0xb1cb80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1cb84: ldr             x16, [x16, #0xcf0]
    // 0xb1cb88: str             x16, [SP]
    // 0xb1cb8c: mov             x1, x0
    // 0xb1cb90: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xb1cb90: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xb1cb94: ldr             x4, [x4, #0xe0]
    // 0xb1cb98: r0 = Container()
    //     0xb1cb98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1cb9c: ldur            x1, [fp, #-0x20]
    // 0xb1cba0: ldur            x0, [fp, #-0x38]
    // 0xb1cba4: ArrayStore: r1[6] = r0  ; List_4
    //     0xb1cba4: add             x25, x1, #0x27
    //     0xb1cba8: str             w0, [x25]
    //     0xb1cbac: tbz             w0, #0, #0xb1cbc8
    //     0xb1cbb0: ldurb           w16, [x1, #-1]
    //     0xb1cbb4: ldurb           w17, [x0, #-1]
    //     0xb1cbb8: and             x16, x17, x16, lsr #2
    //     0xb1cbbc: tst             x16, HEAP, lsr #32
    //     0xb1cbc0: b.eq            #0xb1cbc8
    //     0xb1cbc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1cbc8: ldur            x0, [fp, #-8]
    // 0xb1cbcc: LoadField: r1 = r0->field_f
    //     0xb1cbcc: ldur            w1, [x0, #0xf]
    // 0xb1cbd0: DecompressPointer r1
    //     0xb1cbd0: add             x1, x1, HEAP, lsl #32
    // 0xb1cbd4: cmp             w1, NULL
    // 0xb1cbd8: b.eq            #0xb1d598
    // 0xb1cbdc: r0 = of()
    //     0xb1cbdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1cbe0: LoadField: r1 = r0->field_5b
    //     0xb1cbe0: ldur            w1, [x0, #0x5b]
    // 0xb1cbe4: DecompressPointer r1
    //     0xb1cbe4: add             x1, x1, HEAP, lsl #32
    // 0xb1cbe8: stur            x1, [fp, #-0x38]
    // 0xb1cbec: r0 = BoxDecoration()
    //     0xb1cbec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1cbf0: mov             x1, x0
    // 0xb1cbf4: ldur            x0, [fp, #-0x38]
    // 0xb1cbf8: stur            x1, [fp, #-0x40]
    // 0xb1cbfc: StoreField: r1->field_7 = r0
    //     0xb1cbfc: stur            w0, [x1, #7]
    // 0xb1cc00: r0 = Instance_BoxShape
    //     0xb1cc00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb1cc04: ldr             x0, [x0, #0x970]
    // 0xb1cc08: StoreField: r1->field_23 = r0
    //     0xb1cc08: stur            w0, [x1, #0x23]
    // 0xb1cc0c: r0 = Container()
    //     0xb1cc0c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1cc10: stur            x0, [fp, #-0x38]
    // 0xb1cc14: r16 = Instance_EdgeInsets
    //     0xb1cc14: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xb1cc18: ldr             x16, [x16, #0x108]
    // 0xb1cc1c: r30 = 5.000000
    //     0xb1cc1c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1cc20: ldr             lr, [lr, #0xcf0]
    // 0xb1cc24: stp             lr, x16, [SP, #0x10]
    // 0xb1cc28: r16 = 5.000000
    //     0xb1cc28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xb1cc2c: ldr             x16, [x16, #0xcf0]
    // 0xb1cc30: ldur            lr, [fp, #-0x40]
    // 0xb1cc34: stp             lr, x16, [SP]
    // 0xb1cc38: mov             x1, x0
    // 0xb1cc3c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb1cc3c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb1cc40: ldr             x4, [x4, #0x118]
    // 0xb1cc44: r0 = Container()
    //     0xb1cc44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1cc48: ldur            x1, [fp, #-0x20]
    // 0xb1cc4c: ldur            x0, [fp, #-0x38]
    // 0xb1cc50: ArrayStore: r1[7] = r0  ; List_4
    //     0xb1cc50: add             x25, x1, #0x2b
    //     0xb1cc54: str             w0, [x25]
    //     0xb1cc58: tbz             w0, #0, #0xb1cc74
    //     0xb1cc5c: ldurb           w16, [x1, #-1]
    //     0xb1cc60: ldurb           w17, [x0, #-1]
    //     0xb1cc64: and             x16, x17, x16, lsr #2
    //     0xb1cc68: tst             x16, HEAP, lsr #32
    //     0xb1cc6c: b.eq            #0xb1cc74
    //     0xb1cc70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1cc74: ldur            x2, [fp, #-0x28]
    // 0xb1cc78: LoadField: r0 = r2->field_f
    //     0xb1cc78: ldur            w0, [x2, #0xf]
    // 0xb1cc7c: DecompressPointer r0
    //     0xb1cc7c: add             x0, x0, HEAP, lsl #32
    // 0xb1cc80: LoadField: r1 = r2->field_13
    //     0xb1cc80: ldur            w1, [x2, #0x13]
    // 0xb1cc84: DecompressPointer r1
    //     0xb1cc84: add             x1, x1, HEAP, lsl #32
    // 0xb1cc88: stp             x1, x0, [SP]
    // 0xb1cc8c: r4 = 0
    //     0xb1cc8c: movz            x4, #0
    // 0xb1cc90: ldr             x0, [SP, #8]
    // 0xb1cc94: r16 = UnlinkedCall_0x613b5c
    //     0xb1cc94: add             x16, PP, #0x57, lsl #12  ; [pp+0x57700] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1cc98: add             x16, x16, #0x700
    // 0xb1cc9c: ldp             x5, lr, [x16]
    // 0xb1cca0: blr             lr
    // 0xb1cca4: cmp             w0, NULL
    // 0xb1cca8: b.eq            #0xb1d59c
    // 0xb1ccac: LoadField: r1 = r0->field_c3
    //     0xb1ccac: ldur            w1, [x0, #0xc3]
    // 0xb1ccb0: DecompressPointer r1
    //     0xb1ccb0: add             x1, x1, HEAP, lsl #32
    // 0xb1ccb4: cmp             w1, NULL
    // 0xb1ccb8: b.ne            #0xb1ccc4
    // 0xb1ccbc: r5 = ""
    //     0xb1ccbc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1ccc0: b               #0xb1ccc8
    // 0xb1ccc4: mov             x5, x1
    // 0xb1ccc8: ldur            x0, [fp, #-8]
    // 0xb1cccc: ldur            x2, [fp, #-0x28]
    // 0xb1ccd0: ldur            x4, [fp, #-0x10]
    // 0xb1ccd4: ldur            x3, [fp, #-0x20]
    // 0xb1ccd8: stur            x5, [fp, #-0x38]
    // 0xb1ccdc: LoadField: r1 = r0->field_f
    //     0xb1ccdc: ldur            w1, [x0, #0xf]
    // 0xb1cce0: DecompressPointer r1
    //     0xb1cce0: add             x1, x1, HEAP, lsl #32
    // 0xb1cce4: cmp             w1, NULL
    // 0xb1cce8: b.eq            #0xb1d5a0
    // 0xb1ccec: r0 = of()
    //     0xb1ccec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1ccf0: LoadField: r1 = r0->field_87
    //     0xb1ccf0: ldur            w1, [x0, #0x87]
    // 0xb1ccf4: DecompressPointer r1
    //     0xb1ccf4: add             x1, x1, HEAP, lsl #32
    // 0xb1ccf8: LoadField: r0 = r1->field_2b
    //     0xb1ccf8: ldur            w0, [x1, #0x2b]
    // 0xb1ccfc: DecompressPointer r0
    //     0xb1ccfc: add             x0, x0, HEAP, lsl #32
    // 0xb1cd00: stur            x0, [fp, #-0x40]
    // 0xb1cd04: r1 = Instance_Color
    //     0xb1cd04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1cd08: d0 = 0.700000
    //     0xb1cd08: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb1cd0c: ldr             d0, [x17, #0xf48]
    // 0xb1cd10: r0 = withOpacity()
    //     0xb1cd10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1cd14: r16 = 14.000000
    //     0xb1cd14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb1cd18: ldr             x16, [x16, #0x1d8]
    // 0xb1cd1c: stp             x0, x16, [SP]
    // 0xb1cd20: ldur            x1, [fp, #-0x40]
    // 0xb1cd24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1cd24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1cd28: ldr             x4, [x4, #0xaa0]
    // 0xb1cd2c: r0 = copyWith()
    //     0xb1cd2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1cd30: stur            x0, [fp, #-0x40]
    // 0xb1cd34: r0 = Text()
    //     0xb1cd34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1cd38: mov             x1, x0
    // 0xb1cd3c: ldur            x0, [fp, #-0x38]
    // 0xb1cd40: StoreField: r1->field_b = r0
    //     0xb1cd40: stur            w0, [x1, #0xb]
    // 0xb1cd44: ldur            x0, [fp, #-0x40]
    // 0xb1cd48: StoreField: r1->field_13 = r0
    //     0xb1cd48: stur            w0, [x1, #0x13]
    // 0xb1cd4c: mov             x0, x1
    // 0xb1cd50: ldur            x1, [fp, #-0x20]
    // 0xb1cd54: ArrayStore: r1[8] = r0  ; List_4
    //     0xb1cd54: add             x25, x1, #0x2f
    //     0xb1cd58: str             w0, [x25]
    //     0xb1cd5c: tbz             w0, #0, #0xb1cd78
    //     0xb1cd60: ldurb           w16, [x1, #-1]
    //     0xb1cd64: ldurb           w17, [x0, #-1]
    //     0xb1cd68: and             x16, x17, x16, lsr #2
    //     0xb1cd6c: tst             x16, HEAP, lsr #32
    //     0xb1cd70: b.eq            #0xb1cd78
    //     0xb1cd74: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1cd78: r1 = <Widget>
    //     0xb1cd78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1cd7c: r0 = AllocateGrowableArray()
    //     0xb1cd7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1cd80: mov             x1, x0
    // 0xb1cd84: ldur            x0, [fp, #-0x20]
    // 0xb1cd88: stur            x1, [fp, #-0x38]
    // 0xb1cd8c: StoreField: r1->field_f = r0
    //     0xb1cd8c: stur            w0, [x1, #0xf]
    // 0xb1cd90: r0 = 18
    //     0xb1cd90: movz            x0, #0x12
    // 0xb1cd94: StoreField: r1->field_b = r0
    //     0xb1cd94: stur            w0, [x1, #0xb]
    // 0xb1cd98: r0 = Row()
    //     0xb1cd98: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1cd9c: r2 = Instance_Axis
    //     0xb1cd9c: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1cda0: StoreField: r0->field_f = r2
    //     0xb1cda0: stur            w2, [x0, #0xf]
    // 0xb1cda4: r3 = Instance_MainAxisAlignment
    //     0xb1cda4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1cda8: ldr             x3, [x3, #0xa08]
    // 0xb1cdac: StoreField: r0->field_13 = r3
    //     0xb1cdac: stur            w3, [x0, #0x13]
    // 0xb1cdb0: r1 = Instance_MainAxisSize
    //     0xb1cdb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1cdb4: ldr             x1, [x1, #0xa10]
    // 0xb1cdb8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1cdb8: stur            w1, [x0, #0x17]
    // 0xb1cdbc: r1 = Instance_CrossAxisAlignment
    //     0xb1cdbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1cdc0: ldr             x1, [x1, #0xa18]
    // 0xb1cdc4: StoreField: r0->field_1b = r1
    //     0xb1cdc4: stur            w1, [x0, #0x1b]
    // 0xb1cdc8: r4 = Instance_VerticalDirection
    //     0xb1cdc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1cdcc: ldr             x4, [x4, #0xa20]
    // 0xb1cdd0: StoreField: r0->field_23 = r4
    //     0xb1cdd0: stur            w4, [x0, #0x23]
    // 0xb1cdd4: r5 = Instance_Clip
    //     0xb1cdd4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1cdd8: ldr             x5, [x5, #0x38]
    // 0xb1cddc: StoreField: r0->field_2b = r5
    //     0xb1cddc: stur            w5, [x0, #0x2b]
    // 0xb1cde0: StoreField: r0->field_2f = rZR
    //     0xb1cde0: stur            xzr, [x0, #0x2f]
    // 0xb1cde4: ldur            x1, [fp, #-0x38]
    // 0xb1cde8: StoreField: r0->field_b = r1
    //     0xb1cde8: stur            w1, [x0, #0xb]
    // 0xb1cdec: ldur            x1, [fp, #-0x10]
    // 0xb1cdf0: ArrayStore: r1[2] = r0  ; List_4
    //     0xb1cdf0: add             x25, x1, #0x17
    //     0xb1cdf4: str             w0, [x25]
    //     0xb1cdf8: tbz             w0, #0, #0xb1ce14
    //     0xb1cdfc: ldurb           w16, [x1, #-1]
    //     0xb1ce00: ldurb           w17, [x0, #-1]
    //     0xb1ce04: and             x16, x17, x16, lsr #2
    //     0xb1ce08: tst             x16, HEAP, lsr #32
    //     0xb1ce0c: b.eq            #0xb1ce14
    //     0xb1ce10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1ce14: ldur            x1, [fp, #-0x10]
    // 0xb1ce18: r16 = Instance_SizedBox
    //     0xb1ce18: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xb1ce1c: ldr             x16, [x16, #0xd8]
    // 0xb1ce20: StoreField: r1->field_1b = r16
    //     0xb1ce20: stur            w16, [x1, #0x1b]
    // 0xb1ce24: ldur            x0, [fp, #-0x28]
    // 0xb1ce28: LoadField: r6 = r0->field_f
    //     0xb1ce28: ldur            w6, [x0, #0xf]
    // 0xb1ce2c: DecompressPointer r6
    //     0xb1ce2c: add             x6, x6, HEAP, lsl #32
    // 0xb1ce30: LoadField: r7 = r0->field_13
    //     0xb1ce30: ldur            w7, [x0, #0x13]
    // 0xb1ce34: DecompressPointer r7
    //     0xb1ce34: add             x7, x7, HEAP, lsl #32
    // 0xb1ce38: stp             x7, x6, [SP]
    // 0xb1ce3c: r4 = 0
    //     0xb1ce3c: movz            x4, #0
    // 0xb1ce40: ldr             x0, [SP, #8]
    // 0xb1ce44: r16 = UnlinkedCall_0x613b5c
    //     0xb1ce44: add             x16, PP, #0x57, lsl #12  ; [pp+0x57710] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ce48: add             x16, x16, #0x710
    // 0xb1ce4c: ldp             x5, lr, [x16]
    // 0xb1ce50: blr             lr
    // 0xb1ce54: cmp             w0, NULL
    // 0xb1ce58: b.eq            #0xb1d5a4
    // 0xb1ce5c: LoadField: r3 = r0->field_bb
    //     0xb1ce5c: ldur            w3, [x0, #0xbb]
    // 0xb1ce60: DecompressPointer r3
    //     0xb1ce60: add             x3, x3, HEAP, lsl #32
    // 0xb1ce64: mov             x0, x3
    // 0xb1ce68: stur            x3, [fp, #-0x20]
    // 0xb1ce6c: r2 = Null
    //     0xb1ce6c: mov             x2, NULL
    // 0xb1ce70: r1 = Null
    //     0xb1ce70: mov             x1, NULL
    // 0xb1ce74: r4 = LoadClassIdInstr(r0)
    //     0xb1ce74: ldur            x4, [x0, #-1]
    //     0xb1ce78: ubfx            x4, x4, #0xc, #0x14
    // 0xb1ce7c: sub             x4, x4, #0x5e
    // 0xb1ce80: cmp             x4, #1
    // 0xb1ce84: b.ls            #0xb1ce98
    // 0xb1ce88: r8 = String
    //     0xb1ce88: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1ce8c: r3 = Null
    //     0xb1ce8c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57720] Null
    //     0xb1ce90: ldr             x3, [x3, #0x720]
    // 0xb1ce94: r0 = String()
    //     0xb1ce94: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1ce98: ldur            x1, [fp, #-0x20]
    // 0xb1ce9c: r0 = parse()
    //     0xb1ce9c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb1cea0: ldur            x2, [fp, #-0x28]
    // 0xb1cea4: stur            d0, [fp, #-0x50]
    // 0xb1cea8: LoadField: r0 = r2->field_f
    //     0xb1cea8: ldur            w0, [x2, #0xf]
    // 0xb1ceac: DecompressPointer r0
    //     0xb1ceac: add             x0, x0, HEAP, lsl #32
    // 0xb1ceb0: LoadField: r1 = r2->field_13
    //     0xb1ceb0: ldur            w1, [x2, #0x13]
    // 0xb1ceb4: DecompressPointer r1
    //     0xb1ceb4: add             x1, x1, HEAP, lsl #32
    // 0xb1ceb8: stp             x1, x0, [SP]
    // 0xb1cebc: r4 = 0
    //     0xb1cebc: movz            x4, #0
    // 0xb1cec0: ldr             x0, [SP, #8]
    // 0xb1cec4: r16 = UnlinkedCall_0x613b5c
    //     0xb1cec4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57730] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1cec8: add             x16, x16, #0x730
    // 0xb1cecc: ldp             x5, lr, [x16]
    // 0xb1ced0: blr             lr
    // 0xb1ced4: cmp             w0, NULL
    // 0xb1ced8: b.eq            #0xb1d5a8
    // 0xb1cedc: LoadField: r3 = r0->field_bb
    //     0xb1cedc: ldur            w3, [x0, #0xbb]
    // 0xb1cee0: DecompressPointer r3
    //     0xb1cee0: add             x3, x3, HEAP, lsl #32
    // 0xb1cee4: mov             x0, x3
    // 0xb1cee8: stur            x3, [fp, #-0x20]
    // 0xb1ceec: r2 = Null
    //     0xb1ceec: mov             x2, NULL
    // 0xb1cef0: r1 = Null
    //     0xb1cef0: mov             x1, NULL
    // 0xb1cef4: r4 = LoadClassIdInstr(r0)
    //     0xb1cef4: ldur            x4, [x0, #-1]
    //     0xb1cef8: ubfx            x4, x4, #0xc, #0x14
    // 0xb1cefc: sub             x4, x4, #0x5e
    // 0xb1cf00: cmp             x4, #1
    // 0xb1cf04: b.ls            #0xb1cf18
    // 0xb1cf08: r8 = String
    //     0xb1cf08: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1cf0c: r3 = Null
    //     0xb1cf0c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57740] Null
    //     0xb1cf10: ldr             x3, [x3, #0x740]
    // 0xb1cf14: r0 = String()
    //     0xb1cf14: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1cf18: ldur            x1, [fp, #-0x20]
    // 0xb1cf1c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1cf1c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1cf20: r0 = parse()
    //     0xb1cf20: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb1cf24: stur            x0, [fp, #-0x18]
    // 0xb1cf28: r0 = RatingWidget()
    //     0xb1cf28: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb1cf2c: mov             x3, x0
    // 0xb1cf30: r0 = Instance_Icon
    //     0xb1cf30: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xb1cf34: ldr             x0, [x0, #0x190]
    // 0xb1cf38: stur            x3, [fp, #-0x20]
    // 0xb1cf3c: StoreField: r3->field_7 = r0
    //     0xb1cf3c: stur            w0, [x3, #7]
    // 0xb1cf40: r0 = Instance_Icon
    //     0xb1cf40: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xb1cf44: ldr             x0, [x0, #0x198]
    // 0xb1cf48: StoreField: r3->field_b = r0
    //     0xb1cf48: stur            w0, [x3, #0xb]
    // 0xb1cf4c: r0 = Instance_Icon
    //     0xb1cf4c: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xb1cf50: ldr             x0, [x0, #0x1a0]
    // 0xb1cf54: StoreField: r3->field_f = r0
    //     0xb1cf54: stur            w0, [x3, #0xf]
    // 0xb1cf58: r1 = Function '<anonymous closure>':.
    //     0xb1cf58: add             x1, PP, #0x57, lsl #12  ; [pp+0x57750] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb1cf5c: ldr             x1, [x1, #0x750]
    // 0xb1cf60: r2 = Null
    //     0xb1cf60: mov             x2, NULL
    // 0xb1cf64: r0 = AllocateClosure()
    //     0xb1cf64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1cf68: stur            x0, [fp, #-0x38]
    // 0xb1cf6c: r0 = RatingBar()
    //     0xb1cf6c: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb1cf70: mov             x1, x0
    // 0xb1cf74: ldur            x0, [fp, #-0x38]
    // 0xb1cf78: StoreField: r1->field_b = r0
    //     0xb1cf78: stur            w0, [x1, #0xb]
    // 0xb1cf7c: r2 = true
    //     0xb1cf7c: add             x2, NULL, #0x20  ; true
    // 0xb1cf80: StoreField: r1->field_1f = r2
    //     0xb1cf80: stur            w2, [x1, #0x1f]
    // 0xb1cf84: r0 = Instance_Axis
    //     0xb1cf84: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1cf88: StoreField: r1->field_23 = r0
    //     0xb1cf88: stur            w0, [x1, #0x23]
    // 0xb1cf8c: StoreField: r1->field_27 = r2
    //     0xb1cf8c: stur            w2, [x1, #0x27]
    // 0xb1cf90: d0 = 2.000000
    //     0xb1cf90: fmov            d0, #2.00000000
    // 0xb1cf94: StoreField: r1->field_2b = d0
    //     0xb1cf94: stur            d0, [x1, #0x2b]
    // 0xb1cf98: StoreField: r1->field_33 = r2
    //     0xb1cf98: stur            w2, [x1, #0x33]
    // 0xb1cf9c: ldur            d0, [fp, #-0x50]
    // 0xb1cfa0: StoreField: r1->field_37 = d0
    //     0xb1cfa0: stur            d0, [x1, #0x37]
    // 0xb1cfa4: ldur            x0, [fp, #-0x18]
    // 0xb1cfa8: StoreField: r1->field_3f = r0
    //     0xb1cfa8: stur            x0, [x1, #0x3f]
    // 0xb1cfac: r0 = Instance_EdgeInsets
    //     0xb1cfac: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb1cfb0: StoreField: r1->field_47 = r0
    //     0xb1cfb0: stur            w0, [x1, #0x47]
    // 0xb1cfb4: d0 = 18.000000
    //     0xb1cfb4: fmov            d0, #18.00000000
    // 0xb1cfb8: StoreField: r1->field_4b = d0
    //     0xb1cfb8: stur            d0, [x1, #0x4b]
    // 0xb1cfbc: StoreField: r1->field_53 = rZR
    //     0xb1cfbc: stur            xzr, [x1, #0x53]
    // 0xb1cfc0: r0 = false
    //     0xb1cfc0: add             x0, NULL, #0x30  ; false
    // 0xb1cfc4: StoreField: r1->field_5b = r0
    //     0xb1cfc4: stur            w0, [x1, #0x5b]
    // 0xb1cfc8: StoreField: r1->field_5f = r0
    //     0xb1cfc8: stur            w0, [x1, #0x5f]
    // 0xb1cfcc: ldur            x0, [fp, #-0x20]
    // 0xb1cfd0: StoreField: r1->field_67 = r0
    //     0xb1cfd0: stur            w0, [x1, #0x67]
    // 0xb1cfd4: mov             x0, x1
    // 0xb1cfd8: ldur            x1, [fp, #-0x10]
    // 0xb1cfdc: ArrayStore: r1[4] = r0  ; List_4
    //     0xb1cfdc: add             x25, x1, #0x1f
    //     0xb1cfe0: str             w0, [x25]
    //     0xb1cfe4: tbz             w0, #0, #0xb1d000
    //     0xb1cfe8: ldurb           w16, [x1, #-1]
    //     0xb1cfec: ldurb           w17, [x0, #-1]
    //     0xb1cff0: and             x16, x17, x16, lsr #2
    //     0xb1cff4: tst             x16, HEAP, lsr #32
    //     0xb1cff8: b.eq            #0xb1d000
    //     0xb1cffc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1d000: ldur            x1, [fp, #-0x10]
    // 0xb1d004: r16 = Instance_SizedBox
    //     0xb1d004: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1d008: ldr             x16, [x16, #0x9f0]
    // 0xb1d00c: StoreField: r1->field_23 = r16
    //     0xb1d00c: stur            w16, [x1, #0x23]
    // 0xb1d010: ldur            x0, [fp, #-0x28]
    // 0xb1d014: LoadField: r3 = r0->field_f
    //     0xb1d014: ldur            w3, [x0, #0xf]
    // 0xb1d018: DecompressPointer r3
    //     0xb1d018: add             x3, x3, HEAP, lsl #32
    // 0xb1d01c: LoadField: r4 = r0->field_13
    //     0xb1d01c: ldur            w4, [x0, #0x13]
    // 0xb1d020: DecompressPointer r4
    //     0xb1d020: add             x4, x4, HEAP, lsl #32
    // 0xb1d024: stp             x4, x3, [SP]
    // 0xb1d028: r4 = 0
    //     0xb1d028: movz            x4, #0
    // 0xb1d02c: ldr             x0, [SP, #8]
    // 0xb1d030: r16 = UnlinkedCall_0x613b5c
    //     0xb1d030: add             x16, PP, #0x57, lsl #12  ; [pp+0x57758] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1d034: add             x16, x16, #0x758
    // 0xb1d038: ldp             x5, lr, [x16]
    // 0xb1d03c: blr             lr
    // 0xb1d040: cmp             w0, NULL
    // 0xb1d044: b.eq            #0xb1d5ac
    // 0xb1d048: LoadField: r1 = r0->field_bf
    //     0xb1d048: ldur            w1, [x0, #0xbf]
    // 0xb1d04c: DecompressPointer r1
    //     0xb1d04c: add             x1, x1, HEAP, lsl #32
    // 0xb1d050: cmp             w1, NULL
    // 0xb1d054: b.eq            #0xb1d5b0
    // 0xb1d058: r0 = trim()
    //     0xb1d058: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb1d05c: mov             x2, x0
    // 0xb1d060: ldur            x0, [fp, #-8]
    // 0xb1d064: stur            x2, [fp, #-0x20]
    // 0xb1d068: LoadField: r1 = r0->field_f
    //     0xb1d068: ldur            w1, [x0, #0xf]
    // 0xb1d06c: DecompressPointer r1
    //     0xb1d06c: add             x1, x1, HEAP, lsl #32
    // 0xb1d070: cmp             w1, NULL
    // 0xb1d074: b.eq            #0xb1d5b4
    // 0xb1d078: r0 = of()
    //     0xb1d078: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d07c: LoadField: r1 = r0->field_87
    //     0xb1d07c: ldur            w1, [x0, #0x87]
    // 0xb1d080: DecompressPointer r1
    //     0xb1d080: add             x1, x1, HEAP, lsl #32
    // 0xb1d084: LoadField: r0 = r1->field_2b
    //     0xb1d084: ldur            w0, [x1, #0x2b]
    // 0xb1d088: DecompressPointer r0
    //     0xb1d088: add             x0, x0, HEAP, lsl #32
    // 0xb1d08c: LoadField: r1 = r0->field_13
    //     0xb1d08c: ldur            w1, [x0, #0x13]
    // 0xb1d090: DecompressPointer r1
    //     0xb1d090: add             x1, x1, HEAP, lsl #32
    // 0xb1d094: r16 = Instance_Color
    //     0xb1d094: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1d098: stp             x16, x1, [SP]
    // 0xb1d09c: r1 = Instance_TextStyle
    //     0xb1d09c: add             x1, PP, #0x52, lsl #12  ; [pp+0x521c8] Obj!TextStyle@d62951
    //     0xb1d0a0: ldr             x1, [x1, #0x1c8]
    // 0xb1d0a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb1d0a4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb1d0a8: ldr             x4, [x4, #0x9b8]
    // 0xb1d0ac: r0 = copyWith()
    //     0xb1d0ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1d0b0: mov             x2, x0
    // 0xb1d0b4: ldur            x0, [fp, #-8]
    // 0xb1d0b8: stur            x2, [fp, #-0x38]
    // 0xb1d0bc: LoadField: r1 = r0->field_f
    //     0xb1d0bc: ldur            w1, [x0, #0xf]
    // 0xb1d0c0: DecompressPointer r1
    //     0xb1d0c0: add             x1, x1, HEAP, lsl #32
    // 0xb1d0c4: cmp             w1, NULL
    // 0xb1d0c8: b.eq            #0xb1d5b8
    // 0xb1d0cc: r0 = of()
    //     0xb1d0cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d0d0: LoadField: r1 = r0->field_87
    //     0xb1d0d0: ldur            w1, [x0, #0x87]
    // 0xb1d0d4: DecompressPointer r1
    //     0xb1d0d4: add             x1, x1, HEAP, lsl #32
    // 0xb1d0d8: LoadField: r0 = r1->field_7
    //     0xb1d0d8: ldur            w0, [x1, #7]
    // 0xb1d0dc: DecompressPointer r0
    //     0xb1d0dc: add             x0, x0, HEAP, lsl #32
    // 0xb1d0e0: ldur            x2, [fp, #-8]
    // 0xb1d0e4: stur            x0, [fp, #-0x40]
    // 0xb1d0e8: LoadField: r1 = r2->field_f
    //     0xb1d0e8: ldur            w1, [x2, #0xf]
    // 0xb1d0ec: DecompressPointer r1
    //     0xb1d0ec: add             x1, x1, HEAP, lsl #32
    // 0xb1d0f0: cmp             w1, NULL
    // 0xb1d0f4: b.eq            #0xb1d5bc
    // 0xb1d0f8: r0 = of()
    //     0xb1d0f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d0fc: LoadField: r1 = r0->field_5b
    //     0xb1d0fc: ldur            w1, [x0, #0x5b]
    // 0xb1d100: DecompressPointer r1
    //     0xb1d100: add             x1, x1, HEAP, lsl #32
    // 0xb1d104: r16 = 12.000000
    //     0xb1d104: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1d108: ldr             x16, [x16, #0x9e8]
    // 0xb1d10c: stp             x1, x16, [SP, #8]
    // 0xb1d110: r16 = Instance_TextDecoration
    //     0xb1d110: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb1d114: ldr             x16, [x16, #0x10]
    // 0xb1d118: str             x16, [SP]
    // 0xb1d11c: ldur            x1, [fp, #-0x40]
    // 0xb1d120: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb1d120: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb1d124: ldr             x4, [x4, #0xe38]
    // 0xb1d128: r0 = copyWith()
    //     0xb1d128: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1d12c: mov             x2, x0
    // 0xb1d130: ldur            x0, [fp, #-8]
    // 0xb1d134: stur            x2, [fp, #-0x40]
    // 0xb1d138: LoadField: r1 = r0->field_f
    //     0xb1d138: ldur            w1, [x0, #0xf]
    // 0xb1d13c: DecompressPointer r1
    //     0xb1d13c: add             x1, x1, HEAP, lsl #32
    // 0xb1d140: cmp             w1, NULL
    // 0xb1d144: b.eq            #0xb1d5c0
    // 0xb1d148: r0 = of()
    //     0xb1d148: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d14c: LoadField: r1 = r0->field_87
    //     0xb1d14c: ldur            w1, [x0, #0x87]
    // 0xb1d150: DecompressPointer r1
    //     0xb1d150: add             x1, x1, HEAP, lsl #32
    // 0xb1d154: LoadField: r0 = r1->field_7
    //     0xb1d154: ldur            w0, [x1, #7]
    // 0xb1d158: DecompressPointer r0
    //     0xb1d158: add             x0, x0, HEAP, lsl #32
    // 0xb1d15c: ldur            x1, [fp, #-8]
    // 0xb1d160: stur            x0, [fp, #-0x48]
    // 0xb1d164: LoadField: r2 = r1->field_f
    //     0xb1d164: ldur            w2, [x1, #0xf]
    // 0xb1d168: DecompressPointer r2
    //     0xb1d168: add             x2, x2, HEAP, lsl #32
    // 0xb1d16c: cmp             w2, NULL
    // 0xb1d170: b.eq            #0xb1d5c4
    // 0xb1d174: mov             x1, x2
    // 0xb1d178: r0 = of()
    //     0xb1d178: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d17c: LoadField: r1 = r0->field_5b
    //     0xb1d17c: ldur            w1, [x0, #0x5b]
    // 0xb1d180: DecompressPointer r1
    //     0xb1d180: add             x1, x1, HEAP, lsl #32
    // 0xb1d184: r16 = 12.000000
    //     0xb1d184: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1d188: ldr             x16, [x16, #0x9e8]
    // 0xb1d18c: stp             x1, x16, [SP, #8]
    // 0xb1d190: r16 = Instance_TextDecoration
    //     0xb1d190: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb1d194: ldr             x16, [x16, #0x10]
    // 0xb1d198: str             x16, [SP]
    // 0xb1d19c: ldur            x1, [fp, #-0x48]
    // 0xb1d1a0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb1d1a0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb1d1a4: ldr             x4, [x4, #0xe38]
    // 0xb1d1a8: r0 = copyWith()
    //     0xb1d1a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1d1ac: stur            x0, [fp, #-8]
    // 0xb1d1b0: r0 = ReadMoreText()
    //     0xb1d1b0: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb1d1b4: mov             x1, x0
    // 0xb1d1b8: ldur            x0, [fp, #-0x20]
    // 0xb1d1bc: StoreField: r1->field_3f = r0
    //     0xb1d1bc: stur            w0, [x1, #0x3f]
    // 0xb1d1c0: r0 = "\n\nRead Less"
    //     0xb1d1c0: add             x0, PP, #0x57, lsl #12  ; [pp+0x57768] "\n\nRead Less"
    //     0xb1d1c4: ldr             x0, [x0, #0x768]
    // 0xb1d1c8: StoreField: r1->field_43 = r0
    //     0xb1d1c8: stur            w0, [x1, #0x43]
    // 0xb1d1cc: r0 = "\n\nRead more"
    //     0xb1d1cc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57770] "\n\nRead more"
    //     0xb1d1d0: ldr             x0, [x0, #0x770]
    // 0xb1d1d4: StoreField: r1->field_47 = r0
    //     0xb1d1d4: stur            w0, [x1, #0x47]
    // 0xb1d1d8: r0 = 240
    //     0xb1d1d8: movz            x0, #0xf0
    // 0xb1d1dc: StoreField: r1->field_f = r0
    //     0xb1d1dc: stur            x0, [x1, #0xf]
    // 0xb1d1e0: r0 = 2
    //     0xb1d1e0: movz            x0, #0x2
    // 0xb1d1e4: ArrayStore: r1[0] = r0  ; List_8
    //     0xb1d1e4: stur            x0, [x1, #0x17]
    // 0xb1d1e8: r0 = Instance_TrimMode
    //     0xb1d1e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb1d1ec: ldr             x0, [x0, #0x9d0]
    // 0xb1d1f0: StoreField: r1->field_1f = r0
    //     0xb1d1f0: stur            w0, [x1, #0x1f]
    // 0xb1d1f4: ldur            x0, [fp, #-0x38]
    // 0xb1d1f8: StoreField: r1->field_4f = r0
    //     0xb1d1f8: stur            w0, [x1, #0x4f]
    // 0xb1d1fc: r0 = Instance_TextAlign
    //     0xb1d1fc: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb1d200: StoreField: r1->field_53 = r0
    //     0xb1d200: stur            w0, [x1, #0x53]
    // 0xb1d204: ldur            x0, [fp, #-0x40]
    // 0xb1d208: StoreField: r1->field_23 = r0
    //     0xb1d208: stur            w0, [x1, #0x23]
    // 0xb1d20c: ldur            x0, [fp, #-8]
    // 0xb1d210: StoreField: r1->field_27 = r0
    //     0xb1d210: stur            w0, [x1, #0x27]
    // 0xb1d214: r0 = "… "
    //     0xb1d214: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb1d218: ldr             x0, [x0, #0x9d8]
    // 0xb1d21c: StoreField: r1->field_3b = r0
    //     0xb1d21c: stur            w0, [x1, #0x3b]
    // 0xb1d220: r2 = true
    //     0xb1d220: add             x2, NULL, #0x20  ; true
    // 0xb1d224: StoreField: r1->field_37 = r2
    //     0xb1d224: stur            w2, [x1, #0x37]
    // 0xb1d228: mov             x0, x1
    // 0xb1d22c: ldur            x1, [fp, #-0x10]
    // 0xb1d230: ArrayStore: r1[6] = r0  ; List_4
    //     0xb1d230: add             x25, x1, #0x27
    //     0xb1d234: str             w0, [x25]
    //     0xb1d238: tbz             w0, #0, #0xb1d254
    //     0xb1d23c: ldurb           w16, [x1, #-1]
    //     0xb1d240: ldurb           w17, [x0, #-1]
    //     0xb1d244: and             x16, x17, x16, lsr #2
    //     0xb1d248: tst             x16, HEAP, lsr #32
    //     0xb1d24c: b.eq            #0xb1d254
    //     0xb1d250: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1d254: ldur            x1, [fp, #-0x10]
    // 0xb1d258: r16 = Instance_SizedBox
    //     0xb1d258: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb1d25c: ldr             x16, [x16, #0x9f0]
    // 0xb1d260: StoreField: r1->field_2b = r16
    //     0xb1d260: stur            w16, [x1, #0x2b]
    // 0xb1d264: ldur            x0, [fp, #-0x28]
    // 0xb1d268: LoadField: r3 = r0->field_f
    //     0xb1d268: ldur            w3, [x0, #0xf]
    // 0xb1d26c: DecompressPointer r3
    //     0xb1d26c: add             x3, x3, HEAP, lsl #32
    // 0xb1d270: LoadField: r4 = r0->field_13
    //     0xb1d270: ldur            w4, [x0, #0x13]
    // 0xb1d274: DecompressPointer r4
    //     0xb1d274: add             x4, x4, HEAP, lsl #32
    // 0xb1d278: stp             x4, x3, [SP]
    // 0xb1d27c: r4 = 0
    //     0xb1d27c: movz            x4, #0
    // 0xb1d280: ldr             x0, [SP, #8]
    // 0xb1d284: r16 = UnlinkedCall_0x613b5c
    //     0xb1d284: add             x16, PP, #0x57, lsl #12  ; [pp+0x57778] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1d288: add             x16, x16, #0x778
    // 0xb1d28c: ldp             x5, lr, [x16]
    // 0xb1d290: blr             lr
    // 0xb1d294: cmp             w0, NULL
    // 0xb1d298: b.eq            #0xb1d5c8
    // 0xb1d29c: LoadField: r1 = r0->field_b7
    //     0xb1d29c: ldur            w1, [x0, #0xb7]
    // 0xb1d2a0: DecompressPointer r1
    //     0xb1d2a0: add             x1, x1, HEAP, lsl #32
    // 0xb1d2a4: cmp             w1, NULL
    // 0xb1d2a8: b.eq            #0xb1d5cc
    // 0xb1d2ac: LoadField: r0 = r1->field_b
    //     0xb1d2ac: ldur            w0, [x1, #0xb]
    // 0xb1d2b0: r1 = LoadInt32Instr(r0)
    //     0xb1d2b0: sbfx            x1, x0, #1, #0x1f
    // 0xb1d2b4: cmp             x1, #3
    // 0xb1d2b8: b.gt            #0xb1d394
    // 0xb1d2bc: ldur            x2, [fp, #-0x28]
    // 0xb1d2c0: LoadField: r0 = r2->field_f
    //     0xb1d2c0: ldur            w0, [x2, #0xf]
    // 0xb1d2c4: DecompressPointer r0
    //     0xb1d2c4: add             x0, x0, HEAP, lsl #32
    // 0xb1d2c8: LoadField: r1 = r2->field_13
    //     0xb1d2c8: ldur            w1, [x2, #0x13]
    // 0xb1d2cc: DecompressPointer r1
    //     0xb1d2cc: add             x1, x1, HEAP, lsl #32
    // 0xb1d2d0: stp             x1, x0, [SP]
    // 0xb1d2d4: r4 = 0
    //     0xb1d2d4: movz            x4, #0
    // 0xb1d2d8: ldr             x0, [SP, #8]
    // 0xb1d2dc: r16 = UnlinkedCall_0x613b5c
    //     0xb1d2dc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57788] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1d2e0: add             x16, x16, #0x788
    // 0xb1d2e4: ldp             x5, lr, [x16]
    // 0xb1d2e8: blr             lr
    // 0xb1d2ec: cmp             w0, NULL
    // 0xb1d2f0: b.ne            #0xb1d2fc
    // 0xb1d2f4: r0 = Null
    //     0xb1d2f4: mov             x0, NULL
    // 0xb1d2f8: b               #0xb1d318
    // 0xb1d2fc: LoadField: r1 = r0->field_b7
    //     0xb1d2fc: ldur            w1, [x0, #0xb7]
    // 0xb1d300: DecompressPointer r1
    //     0xb1d300: add             x1, x1, HEAP, lsl #32
    // 0xb1d304: cmp             w1, NULL
    // 0xb1d308: b.ne            #0xb1d314
    // 0xb1d30c: r0 = Null
    //     0xb1d30c: mov             x0, NULL
    // 0xb1d310: b               #0xb1d318
    // 0xb1d314: LoadField: r0 = r1->field_b
    //     0xb1d314: ldur            w0, [x1, #0xb]
    // 0xb1d318: cmp             w0, NULL
    // 0xb1d31c: b.ne            #0xb1d328
    // 0xb1d320: r0 = 0
    //     0xb1d320: movz            x0, #0
    // 0xb1d324: b               #0xb1d330
    // 0xb1d328: r1 = LoadInt32Instr(r0)
    //     0xb1d328: sbfx            x1, x0, #1, #0x1f
    // 0xb1d32c: mov             x0, x1
    // 0xb1d330: lsl             x3, x0, #1
    // 0xb1d334: ldur            x2, [fp, #-0x28]
    // 0xb1d338: stur            x3, [fp, #-8]
    // 0xb1d33c: r1 = Function '<anonymous closure>':.
    //     0xb1d33c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57798] AnonymousClosure: (0xb1dd54), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1d340: ldr             x1, [x1, #0x798]
    // 0xb1d344: r0 = AllocateClosure()
    //     0xb1d344: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1d348: stur            x0, [fp, #-0x20]
    // 0xb1d34c: r0 = ListView()
    //     0xb1d34c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1d350: stur            x0, [fp, #-0x38]
    // 0xb1d354: r16 = true
    //     0xb1d354: add             x16, NULL, #0x20  ; true
    // 0xb1d358: r30 = Instance_Axis
    //     0xb1d358: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1d35c: stp             lr, x16, [SP, #0x10]
    // 0xb1d360: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb1d360: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb1d364: ldr             x16, [x16, #0x1c8]
    // 0xb1d368: r30 = Instance_EdgeInsets
    //     0xb1d368: add             lr, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb1d36c: ldr             lr, [lr, #0x550]
    // 0xb1d370: stp             lr, x16, [SP]
    // 0xb1d374: mov             x1, x0
    // 0xb1d378: ldur            x2, [fp, #-0x20]
    // 0xb1d37c: ldur            x3, [fp, #-8]
    // 0xb1d380: r4 = const [0, 0x7, 0x4, 0x3, padding, 0x6, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb1d380: add             x4, PP, #0x55, lsl #12  ; [pp+0x551b0] List(13) [0, 0x7, 0x4, 0x3, "padding", 0x6, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb1d384: ldr             x4, [x4, #0x1b0]
    // 0xb1d388: r0 = ListView.builder()
    //     0xb1d388: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb1d38c: ldur            x2, [fp, #-0x38]
    // 0xb1d390: b               #0xb1d3ec
    // 0xb1d394: ldur            x2, [fp, #-0x28]
    // 0xb1d398: r1 = Function '<anonymous closure>':.
    //     0xb1d398: add             x1, PP, #0x57, lsl #12  ; [pp+0x577a0] AnonymousClosure: (0xb1d5d0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1d39c: ldr             x1, [x1, #0x7a0]
    // 0xb1d3a0: r0 = AllocateClosure()
    //     0xb1d3a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1d3a4: stur            x0, [fp, #-8]
    // 0xb1d3a8: r0 = ListView()
    //     0xb1d3a8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1d3ac: stur            x0, [fp, #-0x20]
    // 0xb1d3b0: r16 = true
    //     0xb1d3b0: add             x16, NULL, #0x20  ; true
    // 0xb1d3b4: r30 = Instance_Axis
    //     0xb1d3b4: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb1d3b8: stp             lr, x16, [SP, #0x10]
    // 0xb1d3bc: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb1d3bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb1d3c0: ldr             x16, [x16, #0x1c8]
    // 0xb1d3c4: r30 = Instance_EdgeInsets
    //     0xb1d3c4: add             lr, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb1d3c8: ldr             lr, [lr, #0x550]
    // 0xb1d3cc: stp             lr, x16, [SP]
    // 0xb1d3d0: mov             x1, x0
    // 0xb1d3d4: ldur            x2, [fp, #-8]
    // 0xb1d3d8: r3 = 6
    //     0xb1d3d8: movz            x3, #0x6
    // 0xb1d3dc: r4 = const [0, 0x7, 0x4, 0x3, padding, 0x6, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb1d3dc: add             x4, PP, #0x55, lsl #12  ; [pp+0x551b0] List(13) [0, 0x7, 0x4, 0x3, "padding", 0x6, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb1d3e0: ldr             x4, [x4, #0x1b0]
    // 0xb1d3e4: r0 = ListView.builder()
    //     0xb1d3e4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb1d3e8: ldur            x2, [fp, #-0x20]
    // 0xb1d3ec: ldur            x0, [fp, #-0x30]
    // 0xb1d3f0: ldur            x1, [fp, #-0x10]
    // 0xb1d3f4: stur            x2, [fp, #-8]
    // 0xb1d3f8: r0 = SizedBox()
    //     0xb1d3f8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1d3fc: mov             x1, x0
    // 0xb1d400: r0 = 120.000000
    //     0xb1d400: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb1d404: ldr             x0, [x0, #0x3a0]
    // 0xb1d408: StoreField: r1->field_13 = r0
    //     0xb1d408: stur            w0, [x1, #0x13]
    // 0xb1d40c: ldur            x0, [fp, #-8]
    // 0xb1d410: StoreField: r1->field_b = r0
    //     0xb1d410: stur            w0, [x1, #0xb]
    // 0xb1d414: mov             x0, x1
    // 0xb1d418: ldur            x1, [fp, #-0x10]
    // 0xb1d41c: ArrayStore: r1[8] = r0  ; List_4
    //     0xb1d41c: add             x25, x1, #0x2f
    //     0xb1d420: str             w0, [x25]
    //     0xb1d424: tbz             w0, #0, #0xb1d440
    //     0xb1d428: ldurb           w16, [x1, #-1]
    //     0xb1d42c: ldurb           w17, [x0, #-1]
    //     0xb1d430: and             x16, x17, x16, lsr #2
    //     0xb1d434: tst             x16, HEAP, lsr #32
    //     0xb1d438: b.eq            #0xb1d440
    //     0xb1d43c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1d440: r1 = <Widget>
    //     0xb1d440: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1d444: r0 = AllocateGrowableArray()
    //     0xb1d444: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1d448: mov             x1, x0
    // 0xb1d44c: ldur            x0, [fp, #-0x10]
    // 0xb1d450: stur            x1, [fp, #-8]
    // 0xb1d454: StoreField: r1->field_f = r0
    //     0xb1d454: stur            w0, [x1, #0xf]
    // 0xb1d458: r0 = 18
    //     0xb1d458: movz            x0, #0x12
    // 0xb1d45c: StoreField: r1->field_b = r0
    //     0xb1d45c: stur            w0, [x1, #0xb]
    // 0xb1d460: r0 = Column()
    //     0xb1d460: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1d464: mov             x1, x0
    // 0xb1d468: r0 = Instance_Axis
    //     0xb1d468: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1d46c: stur            x1, [fp, #-0x10]
    // 0xb1d470: StoreField: r1->field_f = r0
    //     0xb1d470: stur            w0, [x1, #0xf]
    // 0xb1d474: r0 = Instance_MainAxisAlignment
    //     0xb1d474: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb1d478: ldr             x0, [x0, #0xa08]
    // 0xb1d47c: StoreField: r1->field_13 = r0
    //     0xb1d47c: stur            w0, [x1, #0x13]
    // 0xb1d480: r0 = Instance_MainAxisSize
    //     0xb1d480: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb1d484: ldr             x0, [x0, #0xdd0]
    // 0xb1d488: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1d488: stur            w0, [x1, #0x17]
    // 0xb1d48c: r0 = Instance_CrossAxisAlignment
    //     0xb1d48c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb1d490: ldr             x0, [x0, #0x890]
    // 0xb1d494: StoreField: r1->field_1b = r0
    //     0xb1d494: stur            w0, [x1, #0x1b]
    // 0xb1d498: r0 = Instance_VerticalDirection
    //     0xb1d498: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1d49c: ldr             x0, [x0, #0xa20]
    // 0xb1d4a0: StoreField: r1->field_23 = r0
    //     0xb1d4a0: stur            w0, [x1, #0x23]
    // 0xb1d4a4: r0 = Instance_Clip
    //     0xb1d4a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1d4a8: ldr             x0, [x0, #0x38]
    // 0xb1d4ac: StoreField: r1->field_2b = r0
    //     0xb1d4ac: stur            w0, [x1, #0x2b]
    // 0xb1d4b0: StoreField: r1->field_2f = rZR
    //     0xb1d4b0: stur            xzr, [x1, #0x2f]
    // 0xb1d4b4: ldur            x0, [fp, #-8]
    // 0xb1d4b8: StoreField: r1->field_b = r0
    //     0xb1d4b8: stur            w0, [x1, #0xb]
    // 0xb1d4bc: r0 = Padding()
    //     0xb1d4bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1d4c0: mov             x1, x0
    // 0xb1d4c4: r0 = Instance_EdgeInsets
    //     0xb1d4c4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xb1d4c8: ldr             x0, [x0, #0x560]
    // 0xb1d4cc: stur            x1, [fp, #-8]
    // 0xb1d4d0: StoreField: r1->field_f = r0
    //     0xb1d4d0: stur            w0, [x1, #0xf]
    // 0xb1d4d4: ldur            x0, [fp, #-0x10]
    // 0xb1d4d8: StoreField: r1->field_b = r0
    //     0xb1d4d8: stur            w0, [x1, #0xb]
    // 0xb1d4dc: r0 = Card()
    //     0xb1d4dc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb1d4e0: mov             x1, x0
    // 0xb1d4e4: r0 = 0.000000
    //     0xb1d4e4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb1d4e8: stur            x1, [fp, #-0x10]
    // 0xb1d4ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1d4ec: stur            w0, [x1, #0x17]
    // 0xb1d4f0: ldur            x0, [fp, #-0x30]
    // 0xb1d4f4: StoreField: r1->field_1b = r0
    //     0xb1d4f4: stur            w0, [x1, #0x1b]
    // 0xb1d4f8: r0 = true
    //     0xb1d4f8: add             x0, NULL, #0x20  ; true
    // 0xb1d4fc: StoreField: r1->field_1f = r0
    //     0xb1d4fc: stur            w0, [x1, #0x1f]
    // 0xb1d500: ldur            x2, [fp, #-8]
    // 0xb1d504: StoreField: r1->field_2f = r2
    //     0xb1d504: stur            w2, [x1, #0x2f]
    // 0xb1d508: StoreField: r1->field_2b = r0
    //     0xb1d508: stur            w0, [x1, #0x2b]
    // 0xb1d50c: r0 = Instance__CardVariant
    //     0xb1d50c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb1d510: ldr             x0, [x0, #0xa68]
    // 0xb1d514: StoreField: r1->field_33 = r0
    //     0xb1d514: stur            w0, [x1, #0x33]
    // 0xb1d518: r0 = Container()
    //     0xb1d518: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1d51c: stur            x0, [fp, #-8]
    // 0xb1d520: r16 = Instance_EdgeInsets
    //     0xb1d520: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb1d524: ldr             x16, [x16, #0x980]
    // 0xb1d528: ldur            lr, [fp, #-0x10]
    // 0xb1d52c: stp             lr, x16, [SP]
    // 0xb1d530: mov             x1, x0
    // 0xb1d534: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb1d534: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb1d538: ldr             x4, [x4, #0x30]
    // 0xb1d53c: r0 = Container()
    //     0xb1d53c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1d540: r0 = AnimatedContainer()
    //     0xb1d540: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb1d544: stur            x0, [fp, #-0x10]
    // 0xb1d548: r16 = Instance_Cubic
    //     0xb1d548: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb1d54c: ldr             x16, [x16, #0xaf8]
    // 0xb1d550: str             x16, [SP]
    // 0xb1d554: mov             x1, x0
    // 0xb1d558: ldur            x2, [fp, #-8]
    // 0xb1d55c: r3 = Instance_Duration
    //     0xb1d55c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb1d560: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb1d560: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb1d564: ldr             x4, [x4, #0xbc8]
    // 0xb1d568: r0 = AnimatedContainer()
    //     0xb1d568: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb1d56c: ldur            x0, [fp, #-0x10]
    // 0xb1d570: LeaveFrame
    //     0xb1d570: mov             SP, fp
    //     0xb1d574: ldp             fp, lr, [SP], #0x10
    // 0xb1d578: ret
    //     0xb1d578: ret             
    // 0xb1d57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1d57c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1d580: b               #0xb1c624
    // 0xb1d584: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d584: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d58c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d58c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d590: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d598: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d59c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d59c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5a4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5a4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5a8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5a8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5ac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5ac: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5b0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5b0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1d5c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1d5c8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5c8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1d5cc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1d5cc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1d5d0, size: 0x4ec
    // 0xb1d5d0: EnterFrame
    //     0xb1d5d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb1d5d4: mov             fp, SP
    // 0xb1d5d8: AllocStack(0x50)
    //     0xb1d5d8: sub             SP, SP, #0x50
    // 0xb1d5dc: SetupParameters()
    //     0xb1d5dc: ldr             x0, [fp, #0x20]
    //     0xb1d5e0: ldur            w1, [x0, #0x17]
    //     0xb1d5e4: add             x1, x1, HEAP, lsl #32
    //     0xb1d5e8: stur            x1, [fp, #-8]
    // 0xb1d5ec: CheckStackOverflow
    //     0xb1d5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1d5f0: cmp             SP, x16
    //     0xb1d5f4: b.ls            #0xb1daa0
    // 0xb1d5f8: r1 = 2
    //     0xb1d5f8: movz            x1, #0x2
    // 0xb1d5fc: r0 = AllocateContext()
    //     0xb1d5fc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1d600: mov             x2, x0
    // 0xb1d604: ldur            x0, [fp, #-8]
    // 0xb1d608: stur            x2, [fp, #-0x10]
    // 0xb1d60c: StoreField: r2->field_b = r0
    //     0xb1d60c: stur            w0, [x2, #0xb]
    // 0xb1d610: ldr             x1, [fp, #0x18]
    // 0xb1d614: StoreField: r2->field_f = r1
    //     0xb1d614: stur            w1, [x2, #0xf]
    // 0xb1d618: ldr             x1, [fp, #0x10]
    // 0xb1d61c: StoreField: r2->field_13 = r1
    //     0xb1d61c: stur            w1, [x2, #0x13]
    // 0xb1d620: cmp             w1, #4
    // 0xb1d624: b.ne            #0xb1d8f0
    // 0xb1d628: r1 = Instance_Color
    //     0xb1d628: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1d62c: d0 = 0.030000
    //     0xb1d62c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb1d630: ldr             d0, [x17, #0x238]
    // 0xb1d634: r0 = withOpacity()
    //     0xb1d634: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1d638: stur            x0, [fp, #-0x18]
    // 0xb1d63c: r0 = Radius()
    //     0xb1d63c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb1d640: d0 = 10.000000
    //     0xb1d640: fmov            d0, #10.00000000
    // 0xb1d644: stur            x0, [fp, #-0x20]
    // 0xb1d648: StoreField: r0->field_7 = d0
    //     0xb1d648: stur            d0, [x0, #7]
    // 0xb1d64c: StoreField: r0->field_f = d0
    //     0xb1d64c: stur            d0, [x0, #0xf]
    // 0xb1d650: r0 = BorderRadius()
    //     0xb1d650: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb1d654: mov             x1, x0
    // 0xb1d658: ldur            x0, [fp, #-0x20]
    // 0xb1d65c: stur            x1, [fp, #-0x28]
    // 0xb1d660: StoreField: r1->field_7 = r0
    //     0xb1d660: stur            w0, [x1, #7]
    // 0xb1d664: StoreField: r1->field_b = r0
    //     0xb1d664: stur            w0, [x1, #0xb]
    // 0xb1d668: StoreField: r1->field_f = r0
    //     0xb1d668: stur            w0, [x1, #0xf]
    // 0xb1d66c: StoreField: r1->field_13 = r0
    //     0xb1d66c: stur            w0, [x1, #0x13]
    // 0xb1d670: r0 = BoxDecoration()
    //     0xb1d670: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1d674: mov             x3, x0
    // 0xb1d678: ldur            x0, [fp, #-0x18]
    // 0xb1d67c: stur            x3, [fp, #-0x20]
    // 0xb1d680: StoreField: r3->field_7 = r0
    //     0xb1d680: stur            w0, [x3, #7]
    // 0xb1d684: ldur            x0, [fp, #-0x28]
    // 0xb1d688: StoreField: r3->field_13 = r0
    //     0xb1d688: stur            w0, [x3, #0x13]
    // 0xb1d68c: r0 = Instance_BoxShape
    //     0xb1d68c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1d690: ldr             x0, [x0, #0x80]
    // 0xb1d694: StoreField: r3->field_23 = r0
    //     0xb1d694: stur            w0, [x3, #0x23]
    // 0xb1d698: r1 = Null
    //     0xb1d698: mov             x1, NULL
    // 0xb1d69c: r2 = 4
    //     0xb1d69c: movz            x2, #0x4
    // 0xb1d6a0: r0 = AllocateArray()
    //     0xb1d6a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1d6a4: stur            x0, [fp, #-0x18]
    // 0xb1d6a8: r16 = "+"
    //     0xb1d6a8: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xb1d6ac: StoreField: r0->field_f = r16
    //     0xb1d6ac: stur            w16, [x0, #0xf]
    // 0xb1d6b0: ldur            x1, [fp, #-8]
    // 0xb1d6b4: LoadField: r2 = r1->field_f
    //     0xb1d6b4: ldur            w2, [x1, #0xf]
    // 0xb1d6b8: DecompressPointer r2
    //     0xb1d6b8: add             x2, x2, HEAP, lsl #32
    // 0xb1d6bc: LoadField: r3 = r1->field_13
    //     0xb1d6bc: ldur            w3, [x1, #0x13]
    // 0xb1d6c0: DecompressPointer r3
    //     0xb1d6c0: add             x3, x3, HEAP, lsl #32
    // 0xb1d6c4: stp             x3, x2, [SP]
    // 0xb1d6c8: r4 = 0
    //     0xb1d6c8: movz            x4, #0
    // 0xb1d6cc: ldr             x0, [SP, #8]
    // 0xb1d6d0: r16 = UnlinkedCall_0x613b5c
    //     0xb1d6d0: add             x16, PP, #0x57, lsl #12  ; [pp+0x577a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1d6d4: add             x16, x16, #0x7a8
    // 0xb1d6d8: ldp             x5, lr, [x16]
    // 0xb1d6dc: blr             lr
    // 0xb1d6e0: cmp             w0, NULL
    // 0xb1d6e4: b.eq            #0xb1daa8
    // 0xb1d6e8: LoadField: r1 = r0->field_b7
    //     0xb1d6e8: ldur            w1, [x0, #0xb7]
    // 0xb1d6ec: DecompressPointer r1
    //     0xb1d6ec: add             x1, x1, HEAP, lsl #32
    // 0xb1d6f0: cmp             w1, NULL
    // 0xb1d6f4: b.eq            #0xb1daac
    // 0xb1d6f8: LoadField: r0 = r1->field_b
    //     0xb1d6f8: ldur            w0, [x1, #0xb]
    // 0xb1d6fc: r1 = LoadInt32Instr(r0)
    //     0xb1d6fc: sbfx            x1, x0, #1, #0x1f
    // 0xb1d700: sub             x0, x1, #2
    // 0xb1d704: lsl             x1, x0, #1
    // 0xb1d708: ldur            x0, [fp, #-0x18]
    // 0xb1d70c: StoreField: r0->field_13 = r1
    //     0xb1d70c: stur            w1, [x0, #0x13]
    // 0xb1d710: str             x0, [SP]
    // 0xb1d714: r0 = _interpolate()
    //     0xb1d714: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb1d718: ldur            x2, [fp, #-0x10]
    // 0xb1d71c: stur            x0, [fp, #-0x18]
    // 0xb1d720: LoadField: r1 = r2->field_f
    //     0xb1d720: ldur            w1, [x2, #0xf]
    // 0xb1d724: DecompressPointer r1
    //     0xb1d724: add             x1, x1, HEAP, lsl #32
    // 0xb1d728: r0 = of()
    //     0xb1d728: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d72c: LoadField: r1 = r0->field_87
    //     0xb1d72c: ldur            w1, [x0, #0x87]
    // 0xb1d730: DecompressPointer r1
    //     0xb1d730: add             x1, x1, HEAP, lsl #32
    // 0xb1d734: LoadField: r0 = r1->field_2b
    //     0xb1d734: ldur            w0, [x1, #0x2b]
    // 0xb1d738: DecompressPointer r0
    //     0xb1d738: add             x0, x0, HEAP, lsl #32
    // 0xb1d73c: r16 = 12.000000
    //     0xb1d73c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1d740: ldr             x16, [x16, #0x9e8]
    // 0xb1d744: r30 = Instance_Color
    //     0xb1d744: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1d748: stp             lr, x16, [SP]
    // 0xb1d74c: mov             x1, x0
    // 0xb1d750: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1d750: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1d754: ldr             x4, [x4, #0xaa0]
    // 0xb1d758: r0 = copyWith()
    //     0xb1d758: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1d75c: stur            x0, [fp, #-0x28]
    // 0xb1d760: r0 = Text()
    //     0xb1d760: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1d764: mov             x2, x0
    // 0xb1d768: ldur            x0, [fp, #-0x18]
    // 0xb1d76c: stur            x2, [fp, #-0x30]
    // 0xb1d770: StoreField: r2->field_b = r0
    //     0xb1d770: stur            w0, [x2, #0xb]
    // 0xb1d774: ldur            x0, [fp, #-0x28]
    // 0xb1d778: StoreField: r2->field_13 = r0
    //     0xb1d778: stur            w0, [x2, #0x13]
    // 0xb1d77c: ldur            x0, [fp, #-0x10]
    // 0xb1d780: LoadField: r1 = r0->field_f
    //     0xb1d780: ldur            w1, [x0, #0xf]
    // 0xb1d784: DecompressPointer r1
    //     0xb1d784: add             x1, x1, HEAP, lsl #32
    // 0xb1d788: r0 = of()
    //     0xb1d788: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1d78c: LoadField: r1 = r0->field_87
    //     0xb1d78c: ldur            w1, [x0, #0x87]
    // 0xb1d790: DecompressPointer r1
    //     0xb1d790: add             x1, x1, HEAP, lsl #32
    // 0xb1d794: LoadField: r0 = r1->field_2b
    //     0xb1d794: ldur            w0, [x1, #0x2b]
    // 0xb1d798: DecompressPointer r0
    //     0xb1d798: add             x0, x0, HEAP, lsl #32
    // 0xb1d79c: r16 = 12.000000
    //     0xb1d79c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb1d7a0: ldr             x16, [x16, #0x9e8]
    // 0xb1d7a4: r30 = Instance_Color
    //     0xb1d7a4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1d7a8: stp             lr, x16, [SP]
    // 0xb1d7ac: mov             x1, x0
    // 0xb1d7b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1d7b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1d7b4: ldr             x4, [x4, #0xaa0]
    // 0xb1d7b8: r0 = copyWith()
    //     0xb1d7b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1d7bc: stur            x0, [fp, #-0x18]
    // 0xb1d7c0: r0 = Text()
    //     0xb1d7c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1d7c4: mov             x3, x0
    // 0xb1d7c8: r0 = "Photos"
    //     0xb1d7c8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xb1d7cc: ldr             x0, [x0, #0x260]
    // 0xb1d7d0: stur            x3, [fp, #-0x28]
    // 0xb1d7d4: StoreField: r3->field_b = r0
    //     0xb1d7d4: stur            w0, [x3, #0xb]
    // 0xb1d7d8: ldur            x0, [fp, #-0x18]
    // 0xb1d7dc: StoreField: r3->field_13 = r0
    //     0xb1d7dc: stur            w0, [x3, #0x13]
    // 0xb1d7e0: r1 = Null
    //     0xb1d7e0: mov             x1, NULL
    // 0xb1d7e4: r2 = 4
    //     0xb1d7e4: movz            x2, #0x4
    // 0xb1d7e8: r0 = AllocateArray()
    //     0xb1d7e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1d7ec: mov             x2, x0
    // 0xb1d7f0: ldur            x0, [fp, #-0x30]
    // 0xb1d7f4: stur            x2, [fp, #-0x18]
    // 0xb1d7f8: StoreField: r2->field_f = r0
    //     0xb1d7f8: stur            w0, [x2, #0xf]
    // 0xb1d7fc: ldur            x0, [fp, #-0x28]
    // 0xb1d800: StoreField: r2->field_13 = r0
    //     0xb1d800: stur            w0, [x2, #0x13]
    // 0xb1d804: r1 = <Widget>
    //     0xb1d804: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb1d808: r0 = AllocateGrowableArray()
    //     0xb1d808: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1d80c: mov             x1, x0
    // 0xb1d810: ldur            x0, [fp, #-0x18]
    // 0xb1d814: stur            x1, [fp, #-0x28]
    // 0xb1d818: StoreField: r1->field_f = r0
    //     0xb1d818: stur            w0, [x1, #0xf]
    // 0xb1d81c: r0 = 4
    //     0xb1d81c: movz            x0, #0x4
    // 0xb1d820: StoreField: r1->field_b = r0
    //     0xb1d820: stur            w0, [x1, #0xb]
    // 0xb1d824: r0 = Column()
    //     0xb1d824: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1d828: mov             x1, x0
    // 0xb1d82c: r0 = Instance_Axis
    //     0xb1d82c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1d830: stur            x1, [fp, #-0x18]
    // 0xb1d834: StoreField: r1->field_f = r0
    //     0xb1d834: stur            w0, [x1, #0xf]
    // 0xb1d838: r0 = Instance_MainAxisAlignment
    //     0xb1d838: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb1d83c: ldr             x0, [x0, #0xab0]
    // 0xb1d840: StoreField: r1->field_13 = r0
    //     0xb1d840: stur            w0, [x1, #0x13]
    // 0xb1d844: r0 = Instance_MainAxisSize
    //     0xb1d844: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1d848: ldr             x0, [x0, #0xa10]
    // 0xb1d84c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1d84c: stur            w0, [x1, #0x17]
    // 0xb1d850: r0 = Instance_CrossAxisAlignment
    //     0xb1d850: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1d854: ldr             x0, [x0, #0xa18]
    // 0xb1d858: StoreField: r1->field_1b = r0
    //     0xb1d858: stur            w0, [x1, #0x1b]
    // 0xb1d85c: r0 = Instance_VerticalDirection
    //     0xb1d85c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1d860: ldr             x0, [x0, #0xa20]
    // 0xb1d864: StoreField: r1->field_23 = r0
    //     0xb1d864: stur            w0, [x1, #0x23]
    // 0xb1d868: r0 = Instance_Clip
    //     0xb1d868: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1d86c: ldr             x0, [x0, #0x38]
    // 0xb1d870: StoreField: r1->field_2b = r0
    //     0xb1d870: stur            w0, [x1, #0x2b]
    // 0xb1d874: StoreField: r1->field_2f = rZR
    //     0xb1d874: stur            xzr, [x1, #0x2f]
    // 0xb1d878: ldur            x0, [fp, #-0x28]
    // 0xb1d87c: StoreField: r1->field_b = r0
    //     0xb1d87c: stur            w0, [x1, #0xb]
    // 0xb1d880: r0 = Container()
    //     0xb1d880: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1d884: stur            x0, [fp, #-0x28]
    // 0xb1d888: r16 = 64.000000
    //     0xb1d888: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb1d88c: ldr             x16, [x16, #0x838]
    // 0xb1d890: ldur            lr, [fp, #-0x20]
    // 0xb1d894: stp             lr, x16, [SP, #8]
    // 0xb1d898: ldur            x16, [fp, #-0x18]
    // 0xb1d89c: str             x16, [SP]
    // 0xb1d8a0: mov             x1, x0
    // 0xb1d8a4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xb1d8a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xb1d8a8: ldr             x4, [x4, #0x830]
    // 0xb1d8ac: r0 = Container()
    //     0xb1d8ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1d8b0: r0 = GestureDetector()
    //     0xb1d8b0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb1d8b4: ldur            x2, [fp, #-0x10]
    // 0xb1d8b8: r1 = Function '<anonymous closure>':.
    //     0xb1d8b8: add             x1, PP, #0x57, lsl #12  ; [pp+0x577b8] AnonymousClosure: (0xb1dc08), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1d8bc: ldr             x1, [x1, #0x7b8]
    // 0xb1d8c0: stur            x0, [fp, #-0x18]
    // 0xb1d8c4: r0 = AllocateClosure()
    //     0xb1d8c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1d8c8: ldur            x16, [fp, #-0x28]
    // 0xb1d8cc: stp             x16, x0, [SP]
    // 0xb1d8d0: ldur            x1, [fp, #-0x18]
    // 0xb1d8d4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb1d8d4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb1d8d8: ldr             x4, [x4, #0xaf0]
    // 0xb1d8dc: r0 = GestureDetector()
    //     0xb1d8dc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb1d8e0: ldur            x0, [fp, #-0x18]
    // 0xb1d8e4: LeaveFrame
    //     0xb1d8e4: mov             SP, fp
    //     0xb1d8e8: ldp             fp, lr, [SP], #0x10
    // 0xb1d8ec: ret
    //     0xb1d8ec: ret             
    // 0xb1d8f0: mov             x1, x0
    // 0xb1d8f4: LoadField: r0 = r1->field_f
    //     0xb1d8f4: ldur            w0, [x1, #0xf]
    // 0xb1d8f8: DecompressPointer r0
    //     0xb1d8f8: add             x0, x0, HEAP, lsl #32
    // 0xb1d8fc: LoadField: r2 = r1->field_13
    //     0xb1d8fc: ldur            w2, [x1, #0x13]
    // 0xb1d900: DecompressPointer r2
    //     0xb1d900: add             x2, x2, HEAP, lsl #32
    // 0xb1d904: stp             x2, x0, [SP]
    // 0xb1d908: r4 = 0
    //     0xb1d908: movz            x4, #0
    // 0xb1d90c: ldr             x0, [SP, #8]
    // 0xb1d910: r16 = UnlinkedCall_0x613b5c
    //     0xb1d910: add             x16, PP, #0x57, lsl #12  ; [pp+0x577c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1d914: add             x16, x16, #0x7c0
    // 0xb1d918: ldp             x5, lr, [x16]
    // 0xb1d91c: blr             lr
    // 0xb1d920: cmp             w0, NULL
    // 0xb1d924: b.ne            #0xb1d934
    // 0xb1d928: ldur            x3, [fp, #-0x10]
    // 0xb1d92c: r4 = Null
    //     0xb1d92c: mov             x4, NULL
    // 0xb1d930: b               #0xb1d9a0
    // 0xb1d934: ldur            x3, [fp, #-0x10]
    // 0xb1d938: LoadField: r2 = r0->field_b7
    //     0xb1d938: ldur            w2, [x0, #0xb7]
    // 0xb1d93c: DecompressPointer r2
    //     0xb1d93c: add             x2, x2, HEAP, lsl #32
    // 0xb1d940: LoadField: r0 = r3->field_13
    //     0xb1d940: ldur            w0, [x3, #0x13]
    // 0xb1d944: DecompressPointer r0
    //     0xb1d944: add             x0, x0, HEAP, lsl #32
    // 0xb1d948: cmp             w2, NULL
    // 0xb1d94c: b.eq            #0xb1dab0
    // 0xb1d950: LoadField: r1 = r2->field_b
    //     0xb1d950: ldur            w1, [x2, #0xb]
    // 0xb1d954: r4 = LoadInt32Instr(r0)
    //     0xb1d954: sbfx            x4, x0, #1, #0x1f
    //     0xb1d958: tbz             w0, #0, #0xb1d960
    //     0xb1d95c: ldur            x4, [x0, #7]
    // 0xb1d960: r0 = LoadInt32Instr(r1)
    //     0xb1d960: sbfx            x0, x1, #1, #0x1f
    // 0xb1d964: mov             x1, x4
    // 0xb1d968: cmp             x1, x0
    // 0xb1d96c: b.hs            #0xb1dab4
    // 0xb1d970: LoadField: r0 = r2->field_f
    //     0xb1d970: ldur            w0, [x2, #0xf]
    // 0xb1d974: DecompressPointer r0
    //     0xb1d974: add             x0, x0, HEAP, lsl #32
    // 0xb1d978: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb1d978: add             x16, x0, x4, lsl #2
    //     0xb1d97c: ldur            w1, [x16, #0xf]
    // 0xb1d980: DecompressPointer r1
    //     0xb1d980: add             x1, x1, HEAP, lsl #32
    // 0xb1d984: LoadField: r0 = r1->field_7
    //     0xb1d984: ldur            w0, [x1, #7]
    // 0xb1d988: DecompressPointer r0
    //     0xb1d988: add             x0, x0, HEAP, lsl #32
    // 0xb1d98c: cmp             w0, NULL
    // 0xb1d990: b.eq            #0xb1dab8
    // 0xb1d994: LoadField: r1 = r0->field_b
    //     0xb1d994: ldur            w1, [x0, #0xb]
    // 0xb1d998: DecompressPointer r1
    //     0xb1d998: add             x1, x1, HEAP, lsl #32
    // 0xb1d99c: mov             x4, x1
    // 0xb1d9a0: mov             x0, x4
    // 0xb1d9a4: stur            x4, [fp, #-8]
    // 0xb1d9a8: r2 = Null
    //     0xb1d9a8: mov             x2, NULL
    // 0xb1d9ac: r1 = Null
    //     0xb1d9ac: mov             x1, NULL
    // 0xb1d9b0: r4 = LoadClassIdInstr(r0)
    //     0xb1d9b0: ldur            x4, [x0, #-1]
    //     0xb1d9b4: ubfx            x4, x4, #0xc, #0x14
    // 0xb1d9b8: sub             x4, x4, #0x5e
    // 0xb1d9bc: cmp             x4, #1
    // 0xb1d9c0: b.ls            #0xb1d9d4
    // 0xb1d9c4: r8 = String
    //     0xb1d9c4: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1d9c8: r3 = Null
    //     0xb1d9c8: add             x3, PP, #0x57, lsl #12  ; [pp+0x577d0] Null
    //     0xb1d9cc: ldr             x3, [x3, #0x7d0]
    // 0xb1d9d0: r0 = String()
    //     0xb1d9d0: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1d9d4: r1 = Function '<anonymous closure>':.
    //     0xb1d9d4: add             x1, PP, #0x57, lsl #12  ; [pp+0x577e0] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb1d9d8: ldr             x1, [x1, #0x7e0]
    // 0xb1d9dc: r2 = Null
    //     0xb1d9dc: mov             x2, NULL
    // 0xb1d9e0: r0 = AllocateClosure()
    //     0xb1d9e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1d9e4: r1 = Function '<anonymous closure>':.
    //     0xb1d9e4: add             x1, PP, #0x57, lsl #12  ; [pp+0x577e8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb1d9e8: ldr             x1, [x1, #0x7e8]
    // 0xb1d9ec: r2 = Null
    //     0xb1d9ec: mov             x2, NULL
    // 0xb1d9f0: stur            x0, [fp, #-0x18]
    // 0xb1d9f4: r0 = AllocateClosure()
    //     0xb1d9f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1d9f8: stur            x0, [fp, #-0x20]
    // 0xb1d9fc: r0 = CachedNetworkImage()
    //     0xb1d9fc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb1da00: stur            x0, [fp, #-0x28]
    // 0xb1da04: r16 = 64.000000
    //     0xb1da04: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb1da08: ldr             x16, [x16, #0x838]
    // 0xb1da0c: r30 = 64.000000
    //     0xb1da0c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb1da10: ldr             lr, [lr, #0x838]
    // 0xb1da14: stp             lr, x16, [SP, #0x10]
    // 0xb1da18: ldur            x16, [fp, #-0x18]
    // 0xb1da1c: ldur            lr, [fp, #-0x20]
    // 0xb1da20: stp             lr, x16, [SP]
    // 0xb1da24: mov             x1, x0
    // 0xb1da28: ldur            x2, [fp, #-8]
    // 0xb1da2c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb1da2c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb1da30: ldr             x4, [x4, #0x388]
    // 0xb1da34: r0 = CachedNetworkImage()
    //     0xb1da34: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb1da38: r0 = Container()
    //     0xb1da38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1da3c: stur            x0, [fp, #-8]
    // 0xb1da40: r16 = Instance_EdgeInsets
    //     0xb1da40: add             x16, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xb1da44: ldr             x16, [x16, #0x550]
    // 0xb1da48: ldur            lr, [fp, #-0x28]
    // 0xb1da4c: stp             lr, x16, [SP]
    // 0xb1da50: mov             x1, x0
    // 0xb1da54: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb1da54: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb1da58: ldr             x4, [x4, #0x30]
    // 0xb1da5c: r0 = Container()
    //     0xb1da5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1da60: r0 = GestureDetector()
    //     0xb1da60: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb1da64: ldur            x2, [fp, #-0x10]
    // 0xb1da68: r1 = Function '<anonymous closure>':.
    //     0xb1da68: add             x1, PP, #0x57, lsl #12  ; [pp+0x577f0] AnonymousClosure: (0xb1dabc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1da6c: ldr             x1, [x1, #0x7f0]
    // 0xb1da70: stur            x0, [fp, #-0x10]
    // 0xb1da74: r0 = AllocateClosure()
    //     0xb1da74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1da78: ldur            x16, [fp, #-8]
    // 0xb1da7c: stp             x16, x0, [SP]
    // 0xb1da80: ldur            x1, [fp, #-0x10]
    // 0xb1da84: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb1da84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb1da88: ldr             x4, [x4, #0xaf0]
    // 0xb1da8c: r0 = GestureDetector()
    //     0xb1da8c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb1da90: ldur            x0, [fp, #-0x10]
    // 0xb1da94: LeaveFrame
    //     0xb1da94: mov             SP, fp
    //     0xb1da98: ldp             fp, lr, [SP], #0x10
    // 0xb1da9c: ret
    //     0xb1da9c: ret             
    // 0xb1daa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1daa0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1daa4: b               #0xb1d5f8
    // 0xb1daa8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1daa8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1daac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1daac: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1dab0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1dab0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1dab4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1dab4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1dab8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1dab8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1dabc, size: 0x9c
    // 0xb1dabc: EnterFrame
    //     0xb1dabc: stp             fp, lr, [SP, #-0x10]!
    //     0xb1dac0: mov             fp, SP
    // 0xb1dac4: AllocStack(0x28)
    //     0xb1dac4: sub             SP, SP, #0x28
    // 0xb1dac8: SetupParameters()
    //     0xb1dac8: ldr             x0, [fp, #0x10]
    //     0xb1dacc: ldur            w2, [x0, #0x17]
    //     0xb1dad0: add             x2, x2, HEAP, lsl #32
    //     0xb1dad4: stur            x2, [fp, #-8]
    // 0xb1dad8: CheckStackOverflow
    //     0xb1dad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1dadc: cmp             SP, x16
    //     0xb1dae0: b.ls            #0xb1db50
    // 0xb1dae4: LoadField: r1 = r2->field_f
    //     0xb1dae4: ldur            w1, [x2, #0xf]
    // 0xb1dae8: DecompressPointer r1
    //     0xb1dae8: add             x1, x1, HEAP, lsl #32
    // 0xb1daec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1daec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1daf0: r0 = of()
    //     0xb1daf0: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb1daf4: ldur            x2, [fp, #-8]
    // 0xb1daf8: r1 = Function '<anonymous closure>':.
    //     0xb1daf8: add             x1, PP, #0x57, lsl #12  ; [pp+0x577f8] AnonymousClosure: (0xb1db58), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1dafc: ldr             x1, [x1, #0x7f8]
    // 0xb1db00: stur            x0, [fp, #-8]
    // 0xb1db04: r0 = AllocateClosure()
    //     0xb1db04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1db08: r1 = Null
    //     0xb1db08: mov             x1, NULL
    // 0xb1db0c: stur            x0, [fp, #-0x10]
    // 0xb1db10: r0 = MaterialPageRoute()
    //     0xb1db10: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb1db14: mov             x1, x0
    // 0xb1db18: ldur            x2, [fp, #-0x10]
    // 0xb1db1c: stur            x0, [fp, #-0x10]
    // 0xb1db20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1db20: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1db24: r0 = MaterialPageRoute()
    //     0xb1db24: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb1db28: ldur            x16, [fp, #-8]
    // 0xb1db2c: stp             x16, NULL, [SP, #8]
    // 0xb1db30: ldur            x16, [fp, #-0x10]
    // 0xb1db34: str             x16, [SP]
    // 0xb1db38: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1db38: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1db3c: r0 = push()
    //     0xb1db3c: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb1db40: r0 = Null
    //     0xb1db40: mov             x0, NULL
    // 0xb1db44: LeaveFrame
    //     0xb1db44: mov             SP, fp
    //     0xb1db48: ldp             fp, lr, [SP], #0x10
    // 0xb1db4c: ret
    //     0xb1db4c: ret             
    // 0xb1db50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1db50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1db54: b               #0xb1dae4
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb1db58, size: 0xb0
    // 0xb1db58: EnterFrame
    //     0xb1db58: stp             fp, lr, [SP, #-0x10]!
    //     0xb1db5c: mov             fp, SP
    // 0xb1db60: AllocStack(0x20)
    //     0xb1db60: sub             SP, SP, #0x20
    // 0xb1db64: SetupParameters()
    //     0xb1db64: ldr             x0, [fp, #0x18]
    //     0xb1db68: ldur            w1, [x0, #0x17]
    //     0xb1db6c: add             x1, x1, HEAP, lsl #32
    // 0xb1db70: CheckStackOverflow
    //     0xb1db70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1db74: cmp             SP, x16
    //     0xb1db78: b.ls            #0xb1dbfc
    // 0xb1db7c: LoadField: r0 = r1->field_13
    //     0xb1db7c: ldur            w0, [x1, #0x13]
    // 0xb1db80: DecompressPointer r0
    //     0xb1db80: add             x0, x0, HEAP, lsl #32
    // 0xb1db84: stur            x0, [fp, #-8]
    // 0xb1db88: LoadField: r2 = r1->field_b
    //     0xb1db88: ldur            w2, [x1, #0xb]
    // 0xb1db8c: DecompressPointer r2
    //     0xb1db8c: add             x2, x2, HEAP, lsl #32
    // 0xb1db90: LoadField: r1 = r2->field_f
    //     0xb1db90: ldur            w1, [x2, #0xf]
    // 0xb1db94: DecompressPointer r1
    //     0xb1db94: add             x1, x1, HEAP, lsl #32
    // 0xb1db98: LoadField: r3 = r2->field_13
    //     0xb1db98: ldur            w3, [x2, #0x13]
    // 0xb1db9c: DecompressPointer r3
    //     0xb1db9c: add             x3, x3, HEAP, lsl #32
    // 0xb1dba0: stp             x3, x1, [SP]
    // 0xb1dba4: r4 = 0
    //     0xb1dba4: movz            x4, #0
    // 0xb1dba8: ldr             x0, [SP, #8]
    // 0xb1dbac: r16 = UnlinkedCall_0x613b5c
    //     0xb1dbac: add             x16, PP, #0x57, lsl #12  ; [pp+0x57800] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1dbb0: add             x16, x16, #0x800
    // 0xb1dbb4: ldp             x5, lr, [x16]
    // 0xb1dbb8: blr             lr
    // 0xb1dbbc: cmp             w0, NULL
    // 0xb1dbc0: b.eq            #0xb1dc04
    // 0xb1dbc4: LoadField: r1 = r0->field_b7
    //     0xb1dbc4: ldur            w1, [x0, #0xb7]
    // 0xb1dbc8: DecompressPointer r1
    //     0xb1dbc8: add             x1, x1, HEAP, lsl #32
    // 0xb1dbcc: stur            x1, [fp, #-0x10]
    // 0xb1dbd0: r0 = TestimonialMoreImagesWidget()
    //     0xb1dbd0: bl              #0xaf5c78  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb1dbd4: ldur            x1, [fp, #-0x10]
    // 0xb1dbd8: StoreField: r0->field_b = r1
    //     0xb1dbd8: stur            w1, [x0, #0xb]
    // 0xb1dbdc: ldur            x1, [fp, #-8]
    // 0xb1dbe0: r2 = LoadInt32Instr(r1)
    //     0xb1dbe0: sbfx            x2, x1, #1, #0x1f
    //     0xb1dbe4: tbz             w1, #0, #0xb1dbec
    //     0xb1dbe8: ldur            x2, [x1, #7]
    // 0xb1dbec: StoreField: r0->field_f = r2
    //     0xb1dbec: stur            x2, [x0, #0xf]
    // 0xb1dbf0: LeaveFrame
    //     0xb1dbf0: mov             SP, fp
    //     0xb1dbf4: ldp             fp, lr, [SP], #0x10
    // 0xb1dbf8: ret
    //     0xb1dbf8: ret             
    // 0xb1dbfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1dbfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1dc00: b               #0xb1db7c
    // 0xb1dc04: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1dc04: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1dc08, size: 0x9c
    // 0xb1dc08: EnterFrame
    //     0xb1dc08: stp             fp, lr, [SP, #-0x10]!
    //     0xb1dc0c: mov             fp, SP
    // 0xb1dc10: AllocStack(0x28)
    //     0xb1dc10: sub             SP, SP, #0x28
    // 0xb1dc14: SetupParameters()
    //     0xb1dc14: ldr             x0, [fp, #0x10]
    //     0xb1dc18: ldur            w2, [x0, #0x17]
    //     0xb1dc1c: add             x2, x2, HEAP, lsl #32
    //     0xb1dc20: stur            x2, [fp, #-8]
    // 0xb1dc24: CheckStackOverflow
    //     0xb1dc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1dc28: cmp             SP, x16
    //     0xb1dc2c: b.ls            #0xb1dc9c
    // 0xb1dc30: LoadField: r1 = r2->field_f
    //     0xb1dc30: ldur            w1, [x2, #0xf]
    // 0xb1dc34: DecompressPointer r1
    //     0xb1dc34: add             x1, x1, HEAP, lsl #32
    // 0xb1dc38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1dc38: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1dc3c: r0 = of()
    //     0xb1dc3c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb1dc40: ldur            x2, [fp, #-8]
    // 0xb1dc44: r1 = Function '<anonymous closure>':.
    //     0xb1dc44: add             x1, PP, #0x57, lsl #12  ; [pp+0x57810] AnonymousClosure: (0xb1dca4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1dc48: ldr             x1, [x1, #0x810]
    // 0xb1dc4c: stur            x0, [fp, #-8]
    // 0xb1dc50: r0 = AllocateClosure()
    //     0xb1dc50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1dc54: r1 = Null
    //     0xb1dc54: mov             x1, NULL
    // 0xb1dc58: stur            x0, [fp, #-0x10]
    // 0xb1dc5c: r0 = MaterialPageRoute()
    //     0xb1dc5c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb1dc60: mov             x1, x0
    // 0xb1dc64: ldur            x2, [fp, #-0x10]
    // 0xb1dc68: stur            x0, [fp, #-0x10]
    // 0xb1dc6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1dc6c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1dc70: r0 = MaterialPageRoute()
    //     0xb1dc70: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb1dc74: ldur            x16, [fp, #-8]
    // 0xb1dc78: stp             x16, NULL, [SP, #8]
    // 0xb1dc7c: ldur            x16, [fp, #-0x10]
    // 0xb1dc80: str             x16, [SP]
    // 0xb1dc84: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1dc84: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1dc88: r0 = push()
    //     0xb1dc88: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb1dc8c: r0 = Null
    //     0xb1dc8c: mov             x0, NULL
    // 0xb1dc90: LeaveFrame
    //     0xb1dc90: mov             SP, fp
    //     0xb1dc94: ldp             fp, lr, [SP], #0x10
    // 0xb1dc98: ret
    //     0xb1dc98: ret             
    // 0xb1dc9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1dc9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1dca0: b               #0xb1dc30
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb1dca4, size: 0xb0
    // 0xb1dca4: EnterFrame
    //     0xb1dca4: stp             fp, lr, [SP, #-0x10]!
    //     0xb1dca8: mov             fp, SP
    // 0xb1dcac: AllocStack(0x20)
    //     0xb1dcac: sub             SP, SP, #0x20
    // 0xb1dcb0: SetupParameters()
    //     0xb1dcb0: ldr             x0, [fp, #0x18]
    //     0xb1dcb4: ldur            w1, [x0, #0x17]
    //     0xb1dcb8: add             x1, x1, HEAP, lsl #32
    // 0xb1dcbc: CheckStackOverflow
    //     0xb1dcbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1dcc0: cmp             SP, x16
    //     0xb1dcc4: b.ls            #0xb1dd48
    // 0xb1dcc8: LoadField: r0 = r1->field_13
    //     0xb1dcc8: ldur            w0, [x1, #0x13]
    // 0xb1dccc: DecompressPointer r0
    //     0xb1dccc: add             x0, x0, HEAP, lsl #32
    // 0xb1dcd0: stur            x0, [fp, #-8]
    // 0xb1dcd4: LoadField: r2 = r1->field_b
    //     0xb1dcd4: ldur            w2, [x1, #0xb]
    // 0xb1dcd8: DecompressPointer r2
    //     0xb1dcd8: add             x2, x2, HEAP, lsl #32
    // 0xb1dcdc: LoadField: r1 = r2->field_f
    //     0xb1dcdc: ldur            w1, [x2, #0xf]
    // 0xb1dce0: DecompressPointer r1
    //     0xb1dce0: add             x1, x1, HEAP, lsl #32
    // 0xb1dce4: LoadField: r3 = r2->field_13
    //     0xb1dce4: ldur            w3, [x2, #0x13]
    // 0xb1dce8: DecompressPointer r3
    //     0xb1dce8: add             x3, x3, HEAP, lsl #32
    // 0xb1dcec: stp             x3, x1, [SP]
    // 0xb1dcf0: r4 = 0
    //     0xb1dcf0: movz            x4, #0
    // 0xb1dcf4: ldr             x0, [SP, #8]
    // 0xb1dcf8: r16 = UnlinkedCall_0x613b5c
    //     0xb1dcf8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57818] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1dcfc: add             x16, x16, #0x818
    // 0xb1dd00: ldp             x5, lr, [x16]
    // 0xb1dd04: blr             lr
    // 0xb1dd08: cmp             w0, NULL
    // 0xb1dd0c: b.eq            #0xb1dd50
    // 0xb1dd10: LoadField: r1 = r0->field_b7
    //     0xb1dd10: ldur            w1, [x0, #0xb7]
    // 0xb1dd14: DecompressPointer r1
    //     0xb1dd14: add             x1, x1, HEAP, lsl #32
    // 0xb1dd18: stur            x1, [fp, #-0x10]
    // 0xb1dd1c: r0 = TestimonialMoreImagesWidget()
    //     0xb1dd1c: bl              #0xaf5c78  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb1dd20: ldur            x1, [fp, #-0x10]
    // 0xb1dd24: StoreField: r0->field_b = r1
    //     0xb1dd24: stur            w1, [x0, #0xb]
    // 0xb1dd28: ldur            x1, [fp, #-8]
    // 0xb1dd2c: r2 = LoadInt32Instr(r1)
    //     0xb1dd2c: sbfx            x2, x1, #1, #0x1f
    //     0xb1dd30: tbz             w1, #0, #0xb1dd38
    //     0xb1dd34: ldur            x2, [x1, #7]
    // 0xb1dd38: StoreField: r0->field_f = r2
    //     0xb1dd38: stur            x2, [x0, #0xf]
    // 0xb1dd3c: LeaveFrame
    //     0xb1dd3c: mov             SP, fp
    //     0xb1dd40: ldp             fp, lr, [SP], #0x10
    // 0xb1dd44: ret
    //     0xb1dd44: ret             
    // 0xb1dd48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1dd48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1dd4c: b               #0xb1dcc8
    // 0xb1dd50: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1dd50: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1dd54, size: 0x1e8
    // 0xb1dd54: EnterFrame
    //     0xb1dd54: stp             fp, lr, [SP, #-0x10]!
    //     0xb1dd58: mov             fp, SP
    // 0xb1dd5c: AllocStack(0x48)
    //     0xb1dd5c: sub             SP, SP, #0x48
    // 0xb1dd60: SetupParameters()
    //     0xb1dd60: ldr             x0, [fp, #0x20]
    //     0xb1dd64: ldur            w1, [x0, #0x17]
    //     0xb1dd68: add             x1, x1, HEAP, lsl #32
    //     0xb1dd6c: stur            x1, [fp, #-8]
    // 0xb1dd70: CheckStackOverflow
    //     0xb1dd70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1dd74: cmp             SP, x16
    //     0xb1dd78: b.ls            #0xb1df28
    // 0xb1dd7c: r1 = 2
    //     0xb1dd7c: movz            x1, #0x2
    // 0xb1dd80: r0 = AllocateContext()
    //     0xb1dd80: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1dd84: mov             x1, x0
    // 0xb1dd88: ldur            x0, [fp, #-8]
    // 0xb1dd8c: stur            x1, [fp, #-0x10]
    // 0xb1dd90: StoreField: r1->field_b = r0
    //     0xb1dd90: stur            w0, [x1, #0xb]
    // 0xb1dd94: ldr             x2, [fp, #0x18]
    // 0xb1dd98: StoreField: r1->field_f = r2
    //     0xb1dd98: stur            w2, [x1, #0xf]
    // 0xb1dd9c: ldr             x2, [fp, #0x10]
    // 0xb1dda0: StoreField: r1->field_13 = r2
    //     0xb1dda0: stur            w2, [x1, #0x13]
    // 0xb1dda4: LoadField: r2 = r0->field_f
    //     0xb1dda4: ldur            w2, [x0, #0xf]
    // 0xb1dda8: DecompressPointer r2
    //     0xb1dda8: add             x2, x2, HEAP, lsl #32
    // 0xb1ddac: LoadField: r3 = r0->field_13
    //     0xb1ddac: ldur            w3, [x0, #0x13]
    // 0xb1ddb0: DecompressPointer r3
    //     0xb1ddb0: add             x3, x3, HEAP, lsl #32
    // 0xb1ddb4: stp             x3, x2, [SP]
    // 0xb1ddb8: r4 = 0
    //     0xb1ddb8: movz            x4, #0
    // 0xb1ddbc: ldr             x0, [SP, #8]
    // 0xb1ddc0: r16 = UnlinkedCall_0x613b5c
    //     0xb1ddc0: add             x16, PP, #0x57, lsl #12  ; [pp+0x57828] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1ddc4: add             x16, x16, #0x828
    // 0xb1ddc8: ldp             x5, lr, [x16]
    // 0xb1ddcc: blr             lr
    // 0xb1ddd0: cmp             w0, NULL
    // 0xb1ddd4: b.ne            #0xb1dde4
    // 0xb1ddd8: ldur            x3, [fp, #-0x10]
    // 0xb1dddc: r4 = Null
    //     0xb1dddc: mov             x4, NULL
    // 0xb1dde0: b               #0xb1de50
    // 0xb1dde4: ldur            x3, [fp, #-0x10]
    // 0xb1dde8: LoadField: r2 = r0->field_b7
    //     0xb1dde8: ldur            w2, [x0, #0xb7]
    // 0xb1ddec: DecompressPointer r2
    //     0xb1ddec: add             x2, x2, HEAP, lsl #32
    // 0xb1ddf0: LoadField: r0 = r3->field_13
    //     0xb1ddf0: ldur            w0, [x3, #0x13]
    // 0xb1ddf4: DecompressPointer r0
    //     0xb1ddf4: add             x0, x0, HEAP, lsl #32
    // 0xb1ddf8: cmp             w2, NULL
    // 0xb1ddfc: b.eq            #0xb1df30
    // 0xb1de00: LoadField: r1 = r2->field_b
    //     0xb1de00: ldur            w1, [x2, #0xb]
    // 0xb1de04: r4 = LoadInt32Instr(r0)
    //     0xb1de04: sbfx            x4, x0, #1, #0x1f
    //     0xb1de08: tbz             w0, #0, #0xb1de10
    //     0xb1de0c: ldur            x4, [x0, #7]
    // 0xb1de10: r0 = LoadInt32Instr(r1)
    //     0xb1de10: sbfx            x0, x1, #1, #0x1f
    // 0xb1de14: mov             x1, x4
    // 0xb1de18: cmp             x1, x0
    // 0xb1de1c: b.hs            #0xb1df34
    // 0xb1de20: LoadField: r0 = r2->field_f
    //     0xb1de20: ldur            w0, [x2, #0xf]
    // 0xb1de24: DecompressPointer r0
    //     0xb1de24: add             x0, x0, HEAP, lsl #32
    // 0xb1de28: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb1de28: add             x16, x0, x4, lsl #2
    //     0xb1de2c: ldur            w1, [x16, #0xf]
    // 0xb1de30: DecompressPointer r1
    //     0xb1de30: add             x1, x1, HEAP, lsl #32
    // 0xb1de34: LoadField: r0 = r1->field_7
    //     0xb1de34: ldur            w0, [x1, #7]
    // 0xb1de38: DecompressPointer r0
    //     0xb1de38: add             x0, x0, HEAP, lsl #32
    // 0xb1de3c: cmp             w0, NULL
    // 0xb1de40: b.eq            #0xb1df38
    // 0xb1de44: LoadField: r1 = r0->field_b
    //     0xb1de44: ldur            w1, [x0, #0xb]
    // 0xb1de48: DecompressPointer r1
    //     0xb1de48: add             x1, x1, HEAP, lsl #32
    // 0xb1de4c: mov             x4, x1
    // 0xb1de50: mov             x0, x4
    // 0xb1de54: stur            x4, [fp, #-8]
    // 0xb1de58: r2 = Null
    //     0xb1de58: mov             x2, NULL
    // 0xb1de5c: r1 = Null
    //     0xb1de5c: mov             x1, NULL
    // 0xb1de60: r4 = LoadClassIdInstr(r0)
    //     0xb1de60: ldur            x4, [x0, #-1]
    //     0xb1de64: ubfx            x4, x4, #0xc, #0x14
    // 0xb1de68: sub             x4, x4, #0x5e
    // 0xb1de6c: cmp             x4, #1
    // 0xb1de70: b.ls            #0xb1de84
    // 0xb1de74: r8 = String
    //     0xb1de74: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb1de78: r3 = Null
    //     0xb1de78: add             x3, PP, #0x57, lsl #12  ; [pp+0x57838] Null
    //     0xb1de7c: ldr             x3, [x3, #0x838]
    // 0xb1de80: r0 = String()
    //     0xb1de80: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb1de84: r1 = Function '<anonymous closure>':.
    //     0xb1de84: add             x1, PP, #0x57, lsl #12  ; [pp+0x57848] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb1de88: ldr             x1, [x1, #0x848]
    // 0xb1de8c: r2 = Null
    //     0xb1de8c: mov             x2, NULL
    // 0xb1de90: r0 = AllocateClosure()
    //     0xb1de90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1de94: r1 = Function '<anonymous closure>':.
    //     0xb1de94: add             x1, PP, #0x57, lsl #12  ; [pp+0x57850] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb1de98: ldr             x1, [x1, #0x850]
    // 0xb1de9c: r2 = Null
    //     0xb1de9c: mov             x2, NULL
    // 0xb1dea0: stur            x0, [fp, #-0x18]
    // 0xb1dea4: r0 = AllocateClosure()
    //     0xb1dea4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1dea8: stur            x0, [fp, #-0x20]
    // 0xb1deac: r0 = CachedNetworkImage()
    //     0xb1deac: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb1deb0: stur            x0, [fp, #-0x28]
    // 0xb1deb4: r16 = 64.000000
    //     0xb1deb4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb1deb8: ldr             x16, [x16, #0x838]
    // 0xb1debc: r30 = 64.000000
    //     0xb1debc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xb1dec0: ldr             lr, [lr, #0x838]
    // 0xb1dec4: stp             lr, x16, [SP, #0x10]
    // 0xb1dec8: ldur            x16, [fp, #-0x18]
    // 0xb1decc: ldur            lr, [fp, #-0x20]
    // 0xb1ded0: stp             lr, x16, [SP]
    // 0xb1ded4: mov             x1, x0
    // 0xb1ded8: ldur            x2, [fp, #-8]
    // 0xb1dedc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb1dedc: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb1dee0: ldr             x4, [x4, #0x388]
    // 0xb1dee4: r0 = CachedNetworkImage()
    //     0xb1dee4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb1dee8: r0 = GestureDetector()
    //     0xb1dee8: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb1deec: ldur            x2, [fp, #-0x10]
    // 0xb1def0: r1 = Function '<anonymous closure>':.
    //     0xb1def0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57858] AnonymousClosure: (0xb1df3c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1def4: ldr             x1, [x1, #0x858]
    // 0xb1def8: stur            x0, [fp, #-8]
    // 0xb1defc: r0 = AllocateClosure()
    //     0xb1defc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1df00: ldur            x16, [fp, #-0x28]
    // 0xb1df04: stp             x16, x0, [SP]
    // 0xb1df08: ldur            x1, [fp, #-8]
    // 0xb1df0c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb1df0c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb1df10: ldr             x4, [x4, #0xaf0]
    // 0xb1df14: r0 = GestureDetector()
    //     0xb1df14: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb1df18: ldur            x0, [fp, #-8]
    // 0xb1df1c: LeaveFrame
    //     0xb1df1c: mov             SP, fp
    //     0xb1df20: ldp             fp, lr, [SP], #0x10
    // 0xb1df24: ret
    //     0xb1df24: ret             
    // 0xb1df28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1df28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1df2c: b               #0xb1dd7c
    // 0xb1df30: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1df30: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb1df34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb1df34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1df38: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1df38: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb1df3c, size: 0x9c
    // 0xb1df3c: EnterFrame
    //     0xb1df3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb1df40: mov             fp, SP
    // 0xb1df44: AllocStack(0x28)
    //     0xb1df44: sub             SP, SP, #0x28
    // 0xb1df48: SetupParameters()
    //     0xb1df48: ldr             x0, [fp, #0x10]
    //     0xb1df4c: ldur            w2, [x0, #0x17]
    //     0xb1df50: add             x2, x2, HEAP, lsl #32
    //     0xb1df54: stur            x2, [fp, #-8]
    // 0xb1df58: CheckStackOverflow
    //     0xb1df58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1df5c: cmp             SP, x16
    //     0xb1df60: b.ls            #0xb1dfd0
    // 0xb1df64: LoadField: r1 = r2->field_f
    //     0xb1df64: ldur            w1, [x2, #0xf]
    // 0xb1df68: DecompressPointer r1
    //     0xb1df68: add             x1, x1, HEAP, lsl #32
    // 0xb1df6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb1df6c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb1df70: r0 = of()
    //     0xb1df70: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb1df74: ldur            x2, [fp, #-8]
    // 0xb1df78: r1 = Function '<anonymous closure>':.
    //     0xb1df78: add             x1, PP, #0x57, lsl #12  ; [pp+0x57860] AnonymousClosure: (0xb1dfd8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::cosmeticThemeSlider (0xb1c600)
    //     0xb1df7c: ldr             x1, [x1, #0x860]
    // 0xb1df80: stur            x0, [fp, #-8]
    // 0xb1df84: r0 = AllocateClosure()
    //     0xb1df84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1df88: r1 = Null
    //     0xb1df88: mov             x1, NULL
    // 0xb1df8c: stur            x0, [fp, #-0x10]
    // 0xb1df90: r0 = MaterialPageRoute()
    //     0xb1df90: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb1df94: mov             x1, x0
    // 0xb1df98: ldur            x2, [fp, #-0x10]
    // 0xb1df9c: stur            x0, [fp, #-0x10]
    // 0xb1dfa0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb1dfa0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1dfa4: r0 = MaterialPageRoute()
    //     0xb1dfa4: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb1dfa8: ldur            x16, [fp, #-8]
    // 0xb1dfac: stp             x16, NULL, [SP, #8]
    // 0xb1dfb0: ldur            x16, [fp, #-0x10]
    // 0xb1dfb4: str             x16, [SP]
    // 0xb1dfb8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb1dfb8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb1dfbc: r0 = push()
    //     0xb1dfbc: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb1dfc0: r0 = Null
    //     0xb1dfc0: mov             x0, NULL
    // 0xb1dfc4: LeaveFrame
    //     0xb1dfc4: mov             SP, fp
    //     0xb1dfc8: ldp             fp, lr, [SP], #0x10
    // 0xb1dfcc: ret
    //     0xb1dfcc: ret             
    // 0xb1dfd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1dfd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1dfd4: b               #0xb1df64
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb1dfd8, size: 0xb0
    // 0xb1dfd8: EnterFrame
    //     0xb1dfd8: stp             fp, lr, [SP, #-0x10]!
    //     0xb1dfdc: mov             fp, SP
    // 0xb1dfe0: AllocStack(0x20)
    //     0xb1dfe0: sub             SP, SP, #0x20
    // 0xb1dfe4: SetupParameters()
    //     0xb1dfe4: ldr             x0, [fp, #0x18]
    //     0xb1dfe8: ldur            w1, [x0, #0x17]
    //     0xb1dfec: add             x1, x1, HEAP, lsl #32
    // 0xb1dff0: CheckStackOverflow
    //     0xb1dff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1dff4: cmp             SP, x16
    //     0xb1dff8: b.ls            #0xb1e07c
    // 0xb1dffc: LoadField: r0 = r1->field_13
    //     0xb1dffc: ldur            w0, [x1, #0x13]
    // 0xb1e000: DecompressPointer r0
    //     0xb1e000: add             x0, x0, HEAP, lsl #32
    // 0xb1e004: stur            x0, [fp, #-8]
    // 0xb1e008: LoadField: r2 = r1->field_b
    //     0xb1e008: ldur            w2, [x1, #0xb]
    // 0xb1e00c: DecompressPointer r2
    //     0xb1e00c: add             x2, x2, HEAP, lsl #32
    // 0xb1e010: LoadField: r1 = r2->field_f
    //     0xb1e010: ldur            w1, [x2, #0xf]
    // 0xb1e014: DecompressPointer r1
    //     0xb1e014: add             x1, x1, HEAP, lsl #32
    // 0xb1e018: LoadField: r3 = r2->field_13
    //     0xb1e018: ldur            w3, [x2, #0x13]
    // 0xb1e01c: DecompressPointer r3
    //     0xb1e01c: add             x3, x3, HEAP, lsl #32
    // 0xb1e020: stp             x3, x1, [SP]
    // 0xb1e024: r4 = 0
    //     0xb1e024: movz            x4, #0
    // 0xb1e028: ldr             x0, [SP, #8]
    // 0xb1e02c: r16 = UnlinkedCall_0x613b5c
    //     0xb1e02c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57868] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1e030: add             x16, x16, #0x868
    // 0xb1e034: ldp             x5, lr, [x16]
    // 0xb1e038: blr             lr
    // 0xb1e03c: cmp             w0, NULL
    // 0xb1e040: b.eq            #0xb1e084
    // 0xb1e044: LoadField: r1 = r0->field_b7
    //     0xb1e044: ldur            w1, [x0, #0xb7]
    // 0xb1e048: DecompressPointer r1
    //     0xb1e048: add             x1, x1, HEAP, lsl #32
    // 0xb1e04c: stur            x1, [fp, #-0x10]
    // 0xb1e050: r0 = TestimonialMoreImagesWidget()
    //     0xb1e050: bl              #0xaf5c78  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xb1e054: ldur            x1, [fp, #-0x10]
    // 0xb1e058: StoreField: r0->field_b = r1
    //     0xb1e058: stur            w1, [x0, #0xb]
    // 0xb1e05c: ldur            x1, [fp, #-8]
    // 0xb1e060: r2 = LoadInt32Instr(r1)
    //     0xb1e060: sbfx            x2, x1, #1, #0x1f
    //     0xb1e064: tbz             w1, #0, #0xb1e06c
    //     0xb1e068: ldur            x2, [x1, #7]
    // 0xb1e06c: StoreField: r0->field_f = r2
    //     0xb1e06c: stur            x2, [x0, #0xf]
    // 0xb1e070: LeaveFrame
    //     0xb1e070: mov             SP, fp
    //     0xb1e074: ldp             fp, lr, [SP], #0x10
    // 0xb1e078: ret
    //     0xb1e078: ret             
    // 0xb1e07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e07c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e080: b               #0xb1dffc
    // 0xb1e084: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb1e084: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb1e088, size: 0x84
    // 0xb1e088: EnterFrame
    //     0xb1e088: stp             fp, lr, [SP, #-0x10]!
    //     0xb1e08c: mov             fp, SP
    // 0xb1e090: AllocStack(0x10)
    //     0xb1e090: sub             SP, SP, #0x10
    // 0xb1e094: SetupParameters()
    //     0xb1e094: ldr             x0, [fp, #0x18]
    //     0xb1e098: ldur            w1, [x0, #0x17]
    //     0xb1e09c: add             x1, x1, HEAP, lsl #32
    //     0xb1e0a0: stur            x1, [fp, #-8]
    // 0xb1e0a4: CheckStackOverflow
    //     0xb1e0a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1e0a8: cmp             SP, x16
    //     0xb1e0ac: b.ls            #0xb1e104
    // 0xb1e0b0: r1 = 1
    //     0xb1e0b0: movz            x1, #0x1
    // 0xb1e0b4: r0 = AllocateContext()
    //     0xb1e0b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1e0b8: mov             x1, x0
    // 0xb1e0bc: ldur            x0, [fp, #-8]
    // 0xb1e0c0: StoreField: r1->field_b = r0
    //     0xb1e0c0: stur            w0, [x1, #0xb]
    // 0xb1e0c4: ldr             x2, [fp, #0x10]
    // 0xb1e0c8: StoreField: r1->field_f = r2
    //     0xb1e0c8: stur            w2, [x1, #0xf]
    // 0xb1e0cc: LoadField: r3 = r0->field_f
    //     0xb1e0cc: ldur            w3, [x0, #0xf]
    // 0xb1e0d0: DecompressPointer r3
    //     0xb1e0d0: add             x3, x3, HEAP, lsl #32
    // 0xb1e0d4: mov             x2, x1
    // 0xb1e0d8: stur            x3, [fp, #-0x10]
    // 0xb1e0dc: r1 = Function '<anonymous closure>':.
    //     0xb1e0dc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57878] AnonymousClosure: (0xb1e10c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xb943d8)
    //     0xb1e0e0: ldr             x1, [x1, #0x878]
    // 0xb1e0e4: r0 = AllocateClosure()
    //     0xb1e0e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1e0e8: ldur            x1, [fp, #-0x10]
    // 0xb1e0ec: mov             x2, x0
    // 0xb1e0f0: r0 = setState()
    //     0xb1e0f0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb1e0f4: r0 = Null
    //     0xb1e0f4: mov             x0, NULL
    // 0xb1e0f8: LeaveFrame
    //     0xb1e0f8: mov             SP, fp
    //     0xb1e0fc: ldp             fp, lr, [SP], #0x10
    // 0xb1e100: ret
    //     0xb1e100: ret             
    // 0xb1e104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1e104: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1e108: b               #0xb1e0b0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb20900, size: 0x1ac
    // 0xb20900: EnterFrame
    //     0xb20900: stp             fp, lr, [SP, #-0x10]!
    //     0xb20904: mov             fp, SP
    // 0xb20908: AllocStack(0x40)
    //     0xb20908: sub             SP, SP, #0x40
    // 0xb2090c: SetupParameters()
    //     0xb2090c: ldr             x0, [fp, #0x10]
    //     0xb20910: ldur            w1, [x0, #0x17]
    //     0xb20914: add             x1, x1, HEAP, lsl #32
    //     0xb20918: stur            x1, [fp, #-0x20]
    // 0xb2091c: CheckStackOverflow
    //     0xb2091c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20920: cmp             SP, x16
    //     0xb20924: b.ls            #0xb20a9c
    // 0xb20928: LoadField: r0 = r1->field_f
    //     0xb20928: ldur            w0, [x1, #0xf]
    // 0xb2092c: DecompressPointer r0
    //     0xb2092c: add             x0, x0, HEAP, lsl #32
    // 0xb20930: LoadField: r2 = r0->field_b
    //     0xb20930: ldur            w2, [x0, #0xb]
    // 0xb20934: DecompressPointer r2
    //     0xb20934: add             x2, x2, HEAP, lsl #32
    // 0xb20938: stur            x2, [fp, #-0x18]
    // 0xb2093c: cmp             w2, NULL
    // 0xb20940: b.eq            #0xb20aa4
    // 0xb20944: LoadField: r0 = r2->field_1b
    //     0xb20944: ldur            w0, [x2, #0x1b]
    // 0xb20948: DecompressPointer r0
    //     0xb20948: add             x0, x0, HEAP, lsl #32
    // 0xb2094c: stur            x0, [fp, #-0x10]
    // 0xb20950: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb20950: ldur            w3, [x2, #0x17]
    // 0xb20954: DecompressPointer r3
    //     0xb20954: add             x3, x3, HEAP, lsl #32
    // 0xb20958: stur            x3, [fp, #-8]
    // 0xb2095c: r0 = EventData()
    //     0xb2095c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb20960: mov             x1, x0
    // 0xb20964: r0 = "product_page"
    //     0xb20964: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb20968: ldr             x0, [x0, #0x480]
    // 0xb2096c: stur            x1, [fp, #-0x28]
    // 0xb20970: StoreField: r1->field_13 = r0
    //     0xb20970: stur            w0, [x1, #0x13]
    // 0xb20974: ldur            x0, [fp, #-8]
    // 0xb20978: StoreField: r1->field_53 = r0
    //     0xb20978: stur            w0, [x1, #0x53]
    // 0xb2097c: ldur            x0, [fp, #-0x10]
    // 0xb20980: StoreField: r1->field_57 = r0
    //     0xb20980: stur            w0, [x1, #0x57]
    // 0xb20984: r0 = "view_all"
    //     0xb20984: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb20988: ldr             x0, [x0, #0xba0]
    // 0xb2098c: StoreField: r1->field_eb = r0
    //     0xb2098c: stur            w0, [x1, #0xeb]
    // 0xb20990: r0 = EventsRequest()
    //     0xb20990: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb20994: mov             x1, x0
    // 0xb20998: r0 = "cta_clicked"
    //     0xb20998: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xb2099c: ldr             x0, [x0, #0xdf8]
    // 0xb209a0: StoreField: r1->field_7 = r0
    //     0xb209a0: stur            w0, [x1, #7]
    // 0xb209a4: ldur            x0, [fp, #-0x28]
    // 0xb209a8: StoreField: r1->field_b = r0
    //     0xb209a8: stur            w0, [x1, #0xb]
    // 0xb209ac: ldur            x0, [fp, #-0x18]
    // 0xb209b0: LoadField: r2 = r0->field_27
    //     0xb209b0: ldur            w2, [x0, #0x27]
    // 0xb209b4: DecompressPointer r2
    //     0xb209b4: add             x2, x2, HEAP, lsl #32
    // 0xb209b8: stp             x1, x2, [SP]
    // 0xb209bc: r4 = 0
    //     0xb209bc: movz            x4, #0
    // 0xb209c0: ldr             x0, [SP, #8]
    // 0xb209c4: r16 = UnlinkedCall_0x613b5c
    //     0xb209c4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57880] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb209c8: add             x16, x16, #0x880
    // 0xb209cc: ldp             x5, lr, [x16]
    // 0xb209d0: blr             lr
    // 0xb209d4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb209d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb209d8: ldr             x0, [x0, #0x1c80]
    //     0xb209dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb209e0: cmp             w0, w16
    //     0xb209e4: b.ne            #0xb209f0
    //     0xb209e8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb209ec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb209f0: r1 = Null
    //     0xb209f0: mov             x1, NULL
    // 0xb209f4: r2 = 12
    //     0xb209f4: movz            x2, #0xc
    // 0xb209f8: r0 = AllocateArray()
    //     0xb209f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb209fc: r16 = "previousScreenSource"
    //     0xb209fc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xb20a00: ldr             x16, [x16, #0x448]
    // 0xb20a04: StoreField: r0->field_f = r16
    //     0xb20a04: stur            w16, [x0, #0xf]
    // 0xb20a08: ldur            x1, [fp, #-0x20]
    // 0xb20a0c: LoadField: r2 = r1->field_f
    //     0xb20a0c: ldur            w2, [x1, #0xf]
    // 0xb20a10: DecompressPointer r2
    //     0xb20a10: add             x2, x2, HEAP, lsl #32
    // 0xb20a14: LoadField: r1 = r2->field_b
    //     0xb20a14: ldur            w1, [x2, #0xb]
    // 0xb20a18: DecompressPointer r1
    //     0xb20a18: add             x1, x1, HEAP, lsl #32
    // 0xb20a1c: cmp             w1, NULL
    // 0xb20a20: b.eq            #0xb20aa8
    // 0xb20a24: LoadField: r2 = r1->field_23
    //     0xb20a24: ldur            w2, [x1, #0x23]
    // 0xb20a28: DecompressPointer r2
    //     0xb20a28: add             x2, x2, HEAP, lsl #32
    // 0xb20a2c: StoreField: r0->field_13 = r2
    //     0xb20a2c: stur            w2, [x0, #0x13]
    // 0xb20a30: r16 = "screenSource"
    //     0xb20a30: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xb20a34: ldr             x16, [x16, #0x450]
    // 0xb20a38: ArrayStore: r0[0] = r16  ; List_4
    //     0xb20a38: stur            w16, [x0, #0x17]
    // 0xb20a3c: LoadField: r2 = r1->field_1f
    //     0xb20a3c: ldur            w2, [x1, #0x1f]
    // 0xb20a40: DecompressPointer r2
    //     0xb20a40: add             x2, x2, HEAP, lsl #32
    // 0xb20a44: StoreField: r0->field_1b = r2
    //     0xb20a44: stur            w2, [x0, #0x1b]
    // 0xb20a48: r16 = "widgetType"
    //     0xb20a48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xb20a4c: ldr             x16, [x16, #0x338]
    // 0xb20a50: StoreField: r0->field_1f = r16
    //     0xb20a50: stur            w16, [x0, #0x1f]
    // 0xb20a54: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb20a54: ldur            w2, [x1, #0x17]
    // 0xb20a58: DecompressPointer r2
    //     0xb20a58: add             x2, x2, HEAP, lsl #32
    // 0xb20a5c: StoreField: r0->field_23 = r2
    //     0xb20a5c: stur            w2, [x0, #0x23]
    // 0xb20a60: r16 = <String, String?>
    //     0xb20a60: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xb20a64: ldr             x16, [x16, #0x3c8]
    // 0xb20a68: stp             x0, x16, [SP]
    // 0xb20a6c: r0 = Map._fromLiteral()
    //     0xb20a6c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb20a70: r16 = "/testimonials"
    //     0xb20a70: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xb20a74: ldr             x16, [x16, #0x898]
    // 0xb20a78: stp             x16, NULL, [SP, #8]
    // 0xb20a7c: str             x0, [SP]
    // 0xb20a80: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb20a80: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb20a84: ldr             x4, [x4, #0x438]
    // 0xb20a88: r0 = GetNavigation.toNamed()
    //     0xb20a88: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb20a8c: r0 = Null
    //     0xb20a8c: mov             x0, NULL
    // 0xb20a90: LeaveFrame
    //     0xb20a90: mov             SP, fp
    //     0xb20a94: ldp             fp, lr, [SP], #0x10
    // 0xb20a98: ret
    //     0xb20a98: ret             
    // 0xb20a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb20a9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb20aa0: b               #0xb20928
    // 0xb20aa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb20aa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb20aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb20aa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4129, size: 0x2c, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e358, size: 0x48
    // 0xc7e358: EnterFrame
    //     0xc7e358: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e35c: mov             fp, SP
    // 0xc7e360: AllocStack(0x8)
    //     0xc7e360: sub             SP, SP, #8
    // 0xc7e364: CheckStackOverflow
    //     0xc7e364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e368: cmp             SP, x16
    //     0xc7e36c: b.ls            #0xc7e398
    // 0xc7e370: r1 = <TestimonialCarousel>
    //     0xc7e370: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ad8] TypeArguments: <TestimonialCarousel>
    //     0xc7e374: ldr             x1, [x1, #0xad8]
    // 0xc7e378: r0 = _TestimonialCarouselState()
    //     0xc7e378: bl              #0xc7e3a0  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc7e37c: mov             x1, x0
    // 0xc7e380: stur            x0, [fp, #-8]
    // 0xc7e384: r0 = _TestimonialCarouselState()
    //     0xc7e384: bl              #0xc7c770  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::_TestimonialCarouselState
    // 0xc7e388: ldur            x0, [fp, #-8]
    // 0xc7e38c: LeaveFrame
    //     0xc7e38c: mov             SP, fp
    //     0xc7e390: ldp             fp, lr, [SP], #0x10
    // 0xc7e394: ret
    //     0xc7e394: ret             
    // 0xc7e398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e398: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e39c: b               #0xc7e370
  }
}
