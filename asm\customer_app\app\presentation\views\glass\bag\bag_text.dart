// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_text.dart

// class id: 1049344, size: 0x8
class :: {
}

// class id: 3380, size: 0x1c, field offset: 0x14
class _BagTextState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customizedValue; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xb2fff0, size: 0x854
    // 0xb2fff0: EnterFrame
    //     0xb2fff0: stp             fp, lr, [SP, #-0x10]!
    //     0xb2fff4: mov             fp, SP
    // 0xb2fff8: AllocStack(0x50)
    //     0xb2fff8: sub             SP, SP, #0x50
    // 0xb2fffc: SetupParameters(_BagTextState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb2fffc: mov             x0, x1
    //     0xb30000: stur            x1, [fp, #-8]
    //     0xb30004: mov             x1, x2
    //     0xb30008: stur            x2, [fp, #-0x10]
    // 0xb3000c: CheckStackOverflow
    //     0xb3000c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb30010: cmp             SP, x16
    //     0xb30014: b.ls            #0xb30810
    // 0xb30018: LoadField: r2 = r0->field_b
    //     0xb30018: ldur            w2, [x0, #0xb]
    // 0xb3001c: DecompressPointer r2
    //     0xb3001c: add             x2, x2, HEAP, lsl #32
    // 0xb30020: cmp             w2, NULL
    // 0xb30024: b.eq            #0xb30818
    // 0xb30028: LoadField: r3 = r2->field_b
    //     0xb30028: ldur            w3, [x2, #0xb]
    // 0xb3002c: DecompressPointer r3
    //     0xb3002c: add             x3, x3, HEAP, lsl #32
    // 0xb30030: cmp             w3, NULL
    // 0xb30034: b.ne            #0xb30040
    // 0xb30038: r2 = Null
    //     0xb30038: mov             x2, NULL
    // 0xb3003c: b               #0xb3006c
    // 0xb30040: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb30040: ldur            w2, [x3, #0x17]
    // 0xb30044: DecompressPointer r2
    //     0xb30044: add             x2, x2, HEAP, lsl #32
    // 0xb30048: cmp             w2, NULL
    // 0xb3004c: b.ne            #0xb30058
    // 0xb30050: r2 = Null
    //     0xb30050: mov             x2, NULL
    // 0xb30054: b               #0xb3006c
    // 0xb30058: LoadField: r3 = r2->field_7
    //     0xb30058: ldur            w3, [x2, #7]
    // 0xb3005c: cbnz            w3, #0xb30068
    // 0xb30060: r2 = false
    //     0xb30060: add             x2, NULL, #0x30  ; false
    // 0xb30064: b               #0xb3006c
    // 0xb30068: r2 = true
    //     0xb30068: add             x2, NULL, #0x20  ; true
    // 0xb3006c: cmp             w2, NULL
    // 0xb30070: b.ne            #0xb300bc
    // 0xb30074: mov             x1, x0
    // 0xb30078: r3 = 6
    //     0xb30078: movz            x3, #0x6
    // 0xb3007c: r2 = 4
    //     0xb3007c: movz            x2, #0x4
    // 0xb30080: r7 = Instance_CrossAxisAlignment
    //     0xb30080: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb30084: ldr             x7, [x7, #0xa18]
    // 0xb30088: r5 = Instance_MainAxisAlignment
    //     0xb30088: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3008c: ldr             x5, [x5, #0xa08]
    // 0xb30090: r6 = Instance_MainAxisSize
    //     0xb30090: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb30094: ldr             x6, [x6, #0xa10]
    // 0xb30098: r4 = Instance_Axis
    //     0xb30098: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb3009c: r8 = Instance_VerticalDirection
    //     0xb3009c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb300a0: ldr             x8, [x8, #0xa20]
    // 0xb300a4: r0 = Instance__DeferringMouseCursor
    //     0xb300a4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb300a8: r9 = Instance_Clip
    //     0xb300a8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb300ac: ldr             x9, [x9, #0x38]
    // 0xb300b0: d1 = 0.500000
    //     0xb300b0: fmov            d1, #0.50000000
    // 0xb300b4: d0 = inf
    //     0xb300b4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb300b8: b               #0xb30484
    // 0xb300bc: tbnz            w2, #4, #0xb30440
    // 0xb300c0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb300c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb300c4: ldr             x0, [x0, #0x1c80]
    //     0xb300c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb300cc: cmp             w0, w16
    //     0xb300d0: b.ne            #0xb300dc
    //     0xb300d4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb300d8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb300dc: r0 = GetNavigation.size()
    //     0xb300dc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb300e0: LoadField: d0 = r0->field_7
    //     0xb300e0: ldur            d0, [x0, #7]
    // 0xb300e4: d1 = 0.500000
    //     0xb300e4: fmov            d1, #0.50000000
    // 0xb300e8: fmul            d2, d0, d1
    // 0xb300ec: stur            d2, [fp, #-0x40]
    // 0xb300f0: r0 = BoxConstraints()
    //     0xb300f0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb300f4: stur            x0, [fp, #-0x20]
    // 0xb300f8: StoreField: r0->field_7 = rZR
    //     0xb300f8: stur            xzr, [x0, #7]
    // 0xb300fc: ldur            d0, [fp, #-0x40]
    // 0xb30100: StoreField: r0->field_f = d0
    //     0xb30100: stur            d0, [x0, #0xf]
    // 0xb30104: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb30104: stur            xzr, [x0, #0x17]
    // 0xb30108: d0 = inf
    //     0xb30108: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb3010c: StoreField: r0->field_1f = d0
    //     0xb3010c: stur            d0, [x0, #0x1f]
    // 0xb30110: ldur            x2, [fp, #-8]
    // 0xb30114: LoadField: r1 = r2->field_b
    //     0xb30114: ldur            w1, [x2, #0xb]
    // 0xb30118: DecompressPointer r1
    //     0xb30118: add             x1, x1, HEAP, lsl #32
    // 0xb3011c: cmp             w1, NULL
    // 0xb30120: b.eq            #0xb3081c
    // 0xb30124: LoadField: r3 = r1->field_b
    //     0xb30124: ldur            w3, [x1, #0xb]
    // 0xb30128: DecompressPointer r3
    //     0xb30128: add             x3, x3, HEAP, lsl #32
    // 0xb3012c: cmp             w3, NULL
    // 0xb30130: b.ne            #0xb3013c
    // 0xb30134: r1 = Null
    //     0xb30134: mov             x1, NULL
    // 0xb30138: b               #0xb30144
    // 0xb3013c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb3013c: ldur            w1, [x3, #0x17]
    // 0xb30140: DecompressPointer r1
    //     0xb30140: add             x1, x1, HEAP, lsl #32
    // 0xb30144: cmp             w1, NULL
    // 0xb30148: b.ne            #0xb30154
    // 0xb3014c: r3 = ""
    //     0xb3014c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb30150: b               #0xb30158
    // 0xb30154: mov             x3, x1
    // 0xb30158: ldur            x1, [fp, #-0x10]
    // 0xb3015c: stur            x3, [fp, #-0x18]
    // 0xb30160: r0 = of()
    //     0xb30160: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30164: LoadField: r1 = r0->field_87
    //     0xb30164: ldur            w1, [x0, #0x87]
    // 0xb30168: DecompressPointer r1
    //     0xb30168: add             x1, x1, HEAP, lsl #32
    // 0xb3016c: LoadField: r0 = r1->field_7
    //     0xb3016c: ldur            w0, [x1, #7]
    // 0xb30170: DecompressPointer r0
    //     0xb30170: add             x0, x0, HEAP, lsl #32
    // 0xb30174: r16 = 16.000000
    //     0xb30174: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb30178: ldr             x16, [x16, #0x188]
    // 0xb3017c: r30 = Instance_Color
    //     0xb3017c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb30180: stp             lr, x16, [SP]
    // 0xb30184: mov             x1, x0
    // 0xb30188: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb30188: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3018c: ldr             x4, [x4, #0xaa0]
    // 0xb30190: r0 = copyWith()
    //     0xb30190: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30194: stur            x0, [fp, #-0x28]
    // 0xb30198: r0 = TextSpan()
    //     0xb30198: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb3019c: mov             x3, x0
    // 0xb301a0: ldur            x0, [fp, #-0x18]
    // 0xb301a4: stur            x3, [fp, #-0x30]
    // 0xb301a8: StoreField: r3->field_b = r0
    //     0xb301a8: stur            w0, [x3, #0xb]
    // 0xb301ac: r0 = Instance__DeferringMouseCursor
    //     0xb301ac: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb301b0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb301b0: stur            w0, [x3, #0x17]
    // 0xb301b4: ldur            x1, [fp, #-0x28]
    // 0xb301b8: StoreField: r3->field_7 = r1
    //     0xb301b8: stur            w1, [x3, #7]
    // 0xb301bc: r1 = Null
    //     0xb301bc: mov             x1, NULL
    // 0xb301c0: r2 = 6
    //     0xb301c0: movz            x2, #0x6
    // 0xb301c4: r0 = AllocateArray()
    //     0xb301c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb301c8: r16 = " : "
    //     0xb301c8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb301cc: ldr             x16, [x16, #0x680]
    // 0xb301d0: StoreField: r0->field_f = r16
    //     0xb301d0: stur            w16, [x0, #0xf]
    // 0xb301d4: ldur            x1, [fp, #-8]
    // 0xb301d8: LoadField: r2 = r1->field_13
    //     0xb301d8: ldur            w2, [x1, #0x13]
    // 0xb301dc: DecompressPointer r2
    //     0xb301dc: add             x2, x2, HEAP, lsl #32
    // 0xb301e0: r16 = Sentinel
    //     0xb301e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb301e4: cmp             w2, w16
    // 0xb301e8: b.eq            #0xb30820
    // 0xb301ec: StoreField: r0->field_13 = r2
    //     0xb301ec: stur            w2, [x0, #0x13]
    // 0xb301f0: r16 = " "
    //     0xb301f0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb301f4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb301f4: stur            w16, [x0, #0x17]
    // 0xb301f8: str             x0, [SP]
    // 0xb301fc: r0 = _interpolate()
    //     0xb301fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb30200: ldur            x1, [fp, #-0x10]
    // 0xb30204: stur            x0, [fp, #-0x18]
    // 0xb30208: r0 = of()
    //     0xb30208: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3020c: LoadField: r1 = r0->field_87
    //     0xb3020c: ldur            w1, [x0, #0x87]
    // 0xb30210: DecompressPointer r1
    //     0xb30210: add             x1, x1, HEAP, lsl #32
    // 0xb30214: LoadField: r0 = r1->field_2b
    //     0xb30214: ldur            w0, [x1, #0x2b]
    // 0xb30218: DecompressPointer r0
    //     0xb30218: add             x0, x0, HEAP, lsl #32
    // 0xb3021c: r16 = 14.000000
    //     0xb3021c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb30220: ldr             x16, [x16, #0x1d8]
    // 0xb30224: r30 = Instance_Color
    //     0xb30224: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb30228: stp             lr, x16, [SP]
    // 0xb3022c: mov             x1, x0
    // 0xb30230: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb30230: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30234: ldr             x4, [x4, #0xaa0]
    // 0xb30238: r0 = copyWith()
    //     0xb30238: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3023c: stur            x0, [fp, #-0x28]
    // 0xb30240: r0 = TextSpan()
    //     0xb30240: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb30244: mov             x3, x0
    // 0xb30248: ldur            x0, [fp, #-0x18]
    // 0xb3024c: stur            x3, [fp, #-0x38]
    // 0xb30250: StoreField: r3->field_b = r0
    //     0xb30250: stur            w0, [x3, #0xb]
    // 0xb30254: r0 = Instance__DeferringMouseCursor
    //     0xb30254: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb30258: ArrayStore: r3[0] = r0  ; List_4
    //     0xb30258: stur            w0, [x3, #0x17]
    // 0xb3025c: ldur            x1, [fp, #-0x28]
    // 0xb30260: StoreField: r3->field_7 = r1
    //     0xb30260: stur            w1, [x3, #7]
    // 0xb30264: r1 = Null
    //     0xb30264: mov             x1, NULL
    // 0xb30268: r2 = 4
    //     0xb30268: movz            x2, #0x4
    // 0xb3026c: r0 = AllocateArray()
    //     0xb3026c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30270: mov             x2, x0
    // 0xb30274: ldur            x0, [fp, #-0x30]
    // 0xb30278: stur            x2, [fp, #-0x18]
    // 0xb3027c: StoreField: r2->field_f = r0
    //     0xb3027c: stur            w0, [x2, #0xf]
    // 0xb30280: ldur            x0, [fp, #-0x38]
    // 0xb30284: StoreField: r2->field_13 = r0
    //     0xb30284: stur            w0, [x2, #0x13]
    // 0xb30288: r1 = <InlineSpan>
    //     0xb30288: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb3028c: ldr             x1, [x1, #0xe40]
    // 0xb30290: r0 = AllocateGrowableArray()
    //     0xb30290: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb30294: mov             x1, x0
    // 0xb30298: ldur            x0, [fp, #-0x18]
    // 0xb3029c: stur            x1, [fp, #-0x28]
    // 0xb302a0: StoreField: r1->field_f = r0
    //     0xb302a0: stur            w0, [x1, #0xf]
    // 0xb302a4: r2 = 4
    //     0xb302a4: movz            x2, #0x4
    // 0xb302a8: StoreField: r1->field_b = r2
    //     0xb302a8: stur            w2, [x1, #0xb]
    // 0xb302ac: r0 = TextSpan()
    //     0xb302ac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb302b0: mov             x1, x0
    // 0xb302b4: ldur            x0, [fp, #-0x28]
    // 0xb302b8: stur            x1, [fp, #-0x18]
    // 0xb302bc: StoreField: r1->field_f = r0
    //     0xb302bc: stur            w0, [x1, #0xf]
    // 0xb302c0: r0 = Instance__DeferringMouseCursor
    //     0xb302c0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb302c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb302c4: stur            w0, [x1, #0x17]
    // 0xb302c8: r0 = RichText()
    //     0xb302c8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb302cc: mov             x1, x0
    // 0xb302d0: ldur            x2, [fp, #-0x18]
    // 0xb302d4: stur            x0, [fp, #-0x18]
    // 0xb302d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb302d8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb302dc: r0 = RichText()
    //     0xb302dc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb302e0: r0 = ConstrainedBox()
    //     0xb302e0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb302e4: mov             x2, x0
    // 0xb302e8: ldur            x0, [fp, #-0x20]
    // 0xb302ec: stur            x2, [fp, #-0x28]
    // 0xb302f0: StoreField: r2->field_f = r0
    //     0xb302f0: stur            w0, [x2, #0xf]
    // 0xb302f4: ldur            x0, [fp, #-0x18]
    // 0xb302f8: StoreField: r2->field_b = r0
    //     0xb302f8: stur            w0, [x2, #0xb]
    // 0xb302fc: ldur            x1, [fp, #-8]
    // 0xb30300: LoadField: r0 = r1->field_b
    //     0xb30300: ldur            w0, [x1, #0xb]
    // 0xb30304: DecompressPointer r0
    //     0xb30304: add             x0, x0, HEAP, lsl #32
    // 0xb30308: cmp             w0, NULL
    // 0xb3030c: b.eq            #0xb3082c
    // 0xb30310: LoadField: r1 = r0->field_b
    //     0xb30310: ldur            w1, [x0, #0xb]
    // 0xb30314: DecompressPointer r1
    //     0xb30314: add             x1, x1, HEAP, lsl #32
    // 0xb30318: cmp             w1, NULL
    // 0xb3031c: b.ne            #0xb30328
    // 0xb30320: r0 = Null
    //     0xb30320: mov             x0, NULL
    // 0xb30324: b               #0xb30330
    // 0xb30328: LoadField: r0 = r1->field_2b
    //     0xb30328: ldur            w0, [x1, #0x2b]
    // 0xb3032c: DecompressPointer r0
    //     0xb3032c: add             x0, x0, HEAP, lsl #32
    // 0xb30330: cmp             w0, NULL
    // 0xb30334: b.ne            #0xb3033c
    // 0xb30338: r0 = ""
    //     0xb30338: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3033c: ldur            x1, [fp, #-0x10]
    // 0xb30340: stur            x0, [fp, #-0x18]
    // 0xb30344: r0 = of()
    //     0xb30344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30348: LoadField: r1 = r0->field_87
    //     0xb30348: ldur            w1, [x0, #0x87]
    // 0xb3034c: DecompressPointer r1
    //     0xb3034c: add             x1, x1, HEAP, lsl #32
    // 0xb30350: LoadField: r0 = r1->field_2b
    //     0xb30350: ldur            w0, [x1, #0x2b]
    // 0xb30354: DecompressPointer r0
    //     0xb30354: add             x0, x0, HEAP, lsl #32
    // 0xb30358: r16 = 14.000000
    //     0xb30358: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3035c: ldr             x16, [x16, #0x1d8]
    // 0xb30360: r30 = Instance_Color
    //     0xb30360: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb30364: stp             lr, x16, [SP]
    // 0xb30368: mov             x1, x0
    // 0xb3036c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3036c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30370: ldr             x4, [x4, #0xaa0]
    // 0xb30374: r0 = copyWith()
    //     0xb30374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30378: stur            x0, [fp, #-0x20]
    // 0xb3037c: r0 = Text()
    //     0xb3037c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb30380: mov             x3, x0
    // 0xb30384: ldur            x0, [fp, #-0x18]
    // 0xb30388: stur            x3, [fp, #-0x30]
    // 0xb3038c: StoreField: r3->field_b = r0
    //     0xb3038c: stur            w0, [x3, #0xb]
    // 0xb30390: ldur            x0, [fp, #-0x20]
    // 0xb30394: StoreField: r3->field_13 = r0
    //     0xb30394: stur            w0, [x3, #0x13]
    // 0xb30398: r1 = Null
    //     0xb30398: mov             x1, NULL
    // 0xb3039c: r2 = 6
    //     0xb3039c: movz            x2, #0x6
    // 0xb303a0: r0 = AllocateArray()
    //     0xb303a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb303a4: mov             x2, x0
    // 0xb303a8: ldur            x0, [fp, #-0x28]
    // 0xb303ac: stur            x2, [fp, #-0x18]
    // 0xb303b0: StoreField: r2->field_f = r0
    //     0xb303b0: stur            w0, [x2, #0xf]
    // 0xb303b4: r16 = Instance_Spacer
    //     0xb303b4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb303b8: ldr             x16, [x16, #0xf0]
    // 0xb303bc: StoreField: r2->field_13 = r16
    //     0xb303bc: stur            w16, [x2, #0x13]
    // 0xb303c0: ldur            x0, [fp, #-0x30]
    // 0xb303c4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb303c4: stur            w0, [x2, #0x17]
    // 0xb303c8: r1 = <Widget>
    //     0xb303c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb303cc: r0 = AllocateGrowableArray()
    //     0xb303cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb303d0: mov             x1, x0
    // 0xb303d4: ldur            x0, [fp, #-0x18]
    // 0xb303d8: stur            x1, [fp, #-0x20]
    // 0xb303dc: StoreField: r1->field_f = r0
    //     0xb303dc: stur            w0, [x1, #0xf]
    // 0xb303e0: r3 = 6
    //     0xb303e0: movz            x3, #0x6
    // 0xb303e4: StoreField: r1->field_b = r3
    //     0xb303e4: stur            w3, [x1, #0xb]
    // 0xb303e8: r0 = Row()
    //     0xb303e8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb303ec: r4 = Instance_Axis
    //     0xb303ec: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb303f0: StoreField: r0->field_f = r4
    //     0xb303f0: stur            w4, [x0, #0xf]
    // 0xb303f4: r5 = Instance_MainAxisAlignment
    //     0xb303f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb303f8: ldr             x5, [x5, #0xa08]
    // 0xb303fc: StoreField: r0->field_13 = r5
    //     0xb303fc: stur            w5, [x0, #0x13]
    // 0xb30400: r6 = Instance_MainAxisSize
    //     0xb30400: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb30404: ldr             x6, [x6, #0xa10]
    // 0xb30408: ArrayStore: r0[0] = r6  ; List_4
    //     0xb30408: stur            w6, [x0, #0x17]
    // 0xb3040c: r7 = Instance_CrossAxisAlignment
    //     0xb3040c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb30410: ldr             x7, [x7, #0xa18]
    // 0xb30414: StoreField: r0->field_1b = r7
    //     0xb30414: stur            w7, [x0, #0x1b]
    // 0xb30418: r8 = Instance_VerticalDirection
    //     0xb30418: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3041c: ldr             x8, [x8, #0xa20]
    // 0xb30420: StoreField: r0->field_23 = r8
    //     0xb30420: stur            w8, [x0, #0x23]
    // 0xb30424: r9 = Instance_Clip
    //     0xb30424: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb30428: ldr             x9, [x9, #0x38]
    // 0xb3042c: StoreField: r0->field_2b = r9
    //     0xb3042c: stur            w9, [x0, #0x2b]
    // 0xb30430: StoreField: r0->field_2f = rZR
    //     0xb30430: stur            xzr, [x0, #0x2f]
    // 0xb30434: ldur            x1, [fp, #-0x20]
    // 0xb30438: StoreField: r0->field_b = r1
    //     0xb30438: stur            w1, [x0, #0xb]
    // 0xb3043c: b               #0xb30804
    // 0xb30440: mov             x1, x0
    // 0xb30444: r3 = 6
    //     0xb30444: movz            x3, #0x6
    // 0xb30448: r2 = 4
    //     0xb30448: movz            x2, #0x4
    // 0xb3044c: r7 = Instance_CrossAxisAlignment
    //     0xb3044c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb30450: ldr             x7, [x7, #0xa18]
    // 0xb30454: r5 = Instance_MainAxisAlignment
    //     0xb30454: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb30458: ldr             x5, [x5, #0xa08]
    // 0xb3045c: r6 = Instance_MainAxisSize
    //     0xb3045c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb30460: ldr             x6, [x6, #0xa10]
    // 0xb30464: r4 = Instance_Axis
    //     0xb30464: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb30468: r8 = Instance_VerticalDirection
    //     0xb30468: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3046c: ldr             x8, [x8, #0xa20]
    // 0xb30470: r0 = Instance__DeferringMouseCursor
    //     0xb30470: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb30474: r9 = Instance_Clip
    //     0xb30474: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb30478: ldr             x9, [x9, #0x38]
    // 0xb3047c: d1 = 0.500000
    //     0xb3047c: fmov            d1, #0.50000000
    // 0xb30480: d0 = inf
    //     0xb30480: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb30484: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb30484: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb30488: ldr             x0, [x0, #0x1c80]
    //     0xb3048c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb30490: cmp             w0, w16
    //     0xb30494: b.ne            #0xb304a0
    //     0xb30498: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb3049c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb304a0: r0 = GetNavigation.size()
    //     0xb304a0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb304a4: LoadField: d0 = r0->field_7
    //     0xb304a4: ldur            d0, [x0, #7]
    // 0xb304a8: d1 = 0.500000
    //     0xb304a8: fmov            d1, #0.50000000
    // 0xb304ac: fmul            d2, d0, d1
    // 0xb304b0: stur            d2, [fp, #-0x40]
    // 0xb304b4: r0 = BoxConstraints()
    //     0xb304b4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb304b8: stur            x0, [fp, #-0x20]
    // 0xb304bc: StoreField: r0->field_7 = rZR
    //     0xb304bc: stur            xzr, [x0, #7]
    // 0xb304c0: ldur            d0, [fp, #-0x40]
    // 0xb304c4: StoreField: r0->field_f = d0
    //     0xb304c4: stur            d0, [x0, #0xf]
    // 0xb304c8: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb304c8: stur            xzr, [x0, #0x17]
    // 0xb304cc: d0 = inf
    //     0xb304cc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb304d0: StoreField: r0->field_1f = d0
    //     0xb304d0: stur            d0, [x0, #0x1f]
    // 0xb304d4: ldur            x2, [fp, #-8]
    // 0xb304d8: LoadField: r1 = r2->field_b
    //     0xb304d8: ldur            w1, [x2, #0xb]
    // 0xb304dc: DecompressPointer r1
    //     0xb304dc: add             x1, x1, HEAP, lsl #32
    // 0xb304e0: cmp             w1, NULL
    // 0xb304e4: b.eq            #0xb30830
    // 0xb304e8: LoadField: r3 = r1->field_f
    //     0xb304e8: ldur            w3, [x1, #0xf]
    // 0xb304ec: DecompressPointer r3
    //     0xb304ec: add             x3, x3, HEAP, lsl #32
    // 0xb304f0: cmp             w3, NULL
    // 0xb304f4: b.ne            #0xb30500
    // 0xb304f8: r1 = Null
    //     0xb304f8: mov             x1, NULL
    // 0xb304fc: b               #0xb30508
    // 0xb30500: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb30500: ldur            w1, [x3, #0x17]
    // 0xb30504: DecompressPointer r1
    //     0xb30504: add             x1, x1, HEAP, lsl #32
    // 0xb30508: cmp             w1, NULL
    // 0xb3050c: b.ne            #0xb30518
    // 0xb30510: r3 = ""
    //     0xb30510: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb30514: b               #0xb3051c
    // 0xb30518: mov             x3, x1
    // 0xb3051c: ldur            x1, [fp, #-0x10]
    // 0xb30520: stur            x3, [fp, #-0x18]
    // 0xb30524: r0 = of()
    //     0xb30524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30528: LoadField: r1 = r0->field_87
    //     0xb30528: ldur            w1, [x0, #0x87]
    // 0xb3052c: DecompressPointer r1
    //     0xb3052c: add             x1, x1, HEAP, lsl #32
    // 0xb30530: LoadField: r0 = r1->field_7
    //     0xb30530: ldur            w0, [x1, #7]
    // 0xb30534: DecompressPointer r0
    //     0xb30534: add             x0, x0, HEAP, lsl #32
    // 0xb30538: r16 = 16.000000
    //     0xb30538: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb3053c: ldr             x16, [x16, #0x188]
    // 0xb30540: r30 = Instance_Color
    //     0xb30540: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb30544: stp             lr, x16, [SP]
    // 0xb30548: mov             x1, x0
    // 0xb3054c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3054c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30550: ldr             x4, [x4, #0xaa0]
    // 0xb30554: r0 = copyWith()
    //     0xb30554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30558: stur            x0, [fp, #-0x28]
    // 0xb3055c: r0 = TextSpan()
    //     0xb3055c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb30560: mov             x3, x0
    // 0xb30564: ldur            x0, [fp, #-0x18]
    // 0xb30568: stur            x3, [fp, #-0x30]
    // 0xb3056c: StoreField: r3->field_b = r0
    //     0xb3056c: stur            w0, [x3, #0xb]
    // 0xb30570: r0 = Instance__DeferringMouseCursor
    //     0xb30570: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb30574: ArrayStore: r3[0] = r0  ; List_4
    //     0xb30574: stur            w0, [x3, #0x17]
    // 0xb30578: ldur            x1, [fp, #-0x28]
    // 0xb3057c: StoreField: r3->field_7 = r1
    //     0xb3057c: stur            w1, [x3, #7]
    // 0xb30580: r1 = Null
    //     0xb30580: mov             x1, NULL
    // 0xb30584: r2 = 6
    //     0xb30584: movz            x2, #0x6
    // 0xb30588: r0 = AllocateArray()
    //     0xb30588: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3058c: r16 = " : "
    //     0xb3058c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb30590: ldr             x16, [x16, #0x680]
    // 0xb30594: StoreField: r0->field_f = r16
    //     0xb30594: stur            w16, [x0, #0xf]
    // 0xb30598: ldur            x1, [fp, #-8]
    // 0xb3059c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb3059c: ldur            w2, [x1, #0x17]
    // 0xb305a0: DecompressPointer r2
    //     0xb305a0: add             x2, x2, HEAP, lsl #32
    // 0xb305a4: r16 = Sentinel
    //     0xb305a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb305a8: cmp             w2, w16
    // 0xb305ac: b.eq            #0xb30834
    // 0xb305b0: StoreField: r0->field_13 = r2
    //     0xb305b0: stur            w2, [x0, #0x13]
    // 0xb305b4: r16 = " "
    //     0xb305b4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb305b8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb305b8: stur            w16, [x0, #0x17]
    // 0xb305bc: str             x0, [SP]
    // 0xb305c0: r0 = _interpolate()
    //     0xb305c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb305c4: ldur            x1, [fp, #-0x10]
    // 0xb305c8: stur            x0, [fp, #-0x18]
    // 0xb305cc: r0 = of()
    //     0xb305cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb305d0: LoadField: r1 = r0->field_87
    //     0xb305d0: ldur            w1, [x0, #0x87]
    // 0xb305d4: DecompressPointer r1
    //     0xb305d4: add             x1, x1, HEAP, lsl #32
    // 0xb305d8: LoadField: r0 = r1->field_2b
    //     0xb305d8: ldur            w0, [x1, #0x2b]
    // 0xb305dc: DecompressPointer r0
    //     0xb305dc: add             x0, x0, HEAP, lsl #32
    // 0xb305e0: r16 = 14.000000
    //     0xb305e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb305e4: ldr             x16, [x16, #0x1d8]
    // 0xb305e8: r30 = Instance_Color
    //     0xb305e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb305ec: stp             lr, x16, [SP]
    // 0xb305f0: mov             x1, x0
    // 0xb305f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb305f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb305f8: ldr             x4, [x4, #0xaa0]
    // 0xb305fc: r0 = copyWith()
    //     0xb305fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30600: stur            x0, [fp, #-0x28]
    // 0xb30604: r0 = TextSpan()
    //     0xb30604: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb30608: mov             x3, x0
    // 0xb3060c: ldur            x0, [fp, #-0x18]
    // 0xb30610: stur            x3, [fp, #-0x38]
    // 0xb30614: StoreField: r3->field_b = r0
    //     0xb30614: stur            w0, [x3, #0xb]
    // 0xb30618: r0 = Instance__DeferringMouseCursor
    //     0xb30618: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb3061c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3061c: stur            w0, [x3, #0x17]
    // 0xb30620: ldur            x1, [fp, #-0x28]
    // 0xb30624: StoreField: r3->field_7 = r1
    //     0xb30624: stur            w1, [x3, #7]
    // 0xb30628: r1 = Null
    //     0xb30628: mov             x1, NULL
    // 0xb3062c: r2 = 4
    //     0xb3062c: movz            x2, #0x4
    // 0xb30630: r0 = AllocateArray()
    //     0xb30630: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb30634: mov             x2, x0
    // 0xb30638: ldur            x0, [fp, #-0x30]
    // 0xb3063c: stur            x2, [fp, #-0x18]
    // 0xb30640: StoreField: r2->field_f = r0
    //     0xb30640: stur            w0, [x2, #0xf]
    // 0xb30644: ldur            x0, [fp, #-0x38]
    // 0xb30648: StoreField: r2->field_13 = r0
    //     0xb30648: stur            w0, [x2, #0x13]
    // 0xb3064c: r1 = <InlineSpan>
    //     0xb3064c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb30650: ldr             x1, [x1, #0xe40]
    // 0xb30654: r0 = AllocateGrowableArray()
    //     0xb30654: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb30658: mov             x1, x0
    // 0xb3065c: ldur            x0, [fp, #-0x18]
    // 0xb30660: stur            x1, [fp, #-0x28]
    // 0xb30664: StoreField: r1->field_f = r0
    //     0xb30664: stur            w0, [x1, #0xf]
    // 0xb30668: r0 = 4
    //     0xb30668: movz            x0, #0x4
    // 0xb3066c: StoreField: r1->field_b = r0
    //     0xb3066c: stur            w0, [x1, #0xb]
    // 0xb30670: r0 = TextSpan()
    //     0xb30670: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb30674: mov             x1, x0
    // 0xb30678: ldur            x0, [fp, #-0x28]
    // 0xb3067c: stur            x1, [fp, #-0x18]
    // 0xb30680: StoreField: r1->field_f = r0
    //     0xb30680: stur            w0, [x1, #0xf]
    // 0xb30684: r0 = Instance__DeferringMouseCursor
    //     0xb30684: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb30688: ArrayStore: r1[0] = r0  ; List_4
    //     0xb30688: stur            w0, [x1, #0x17]
    // 0xb3068c: r0 = RichText()
    //     0xb3068c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb30690: mov             x1, x0
    // 0xb30694: ldur            x2, [fp, #-0x18]
    // 0xb30698: stur            x0, [fp, #-0x18]
    // 0xb3069c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb3069c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb306a0: r0 = RichText()
    //     0xb306a0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb306a4: r0 = ConstrainedBox()
    //     0xb306a4: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb306a8: mov             x2, x0
    // 0xb306ac: ldur            x0, [fp, #-0x20]
    // 0xb306b0: stur            x2, [fp, #-0x28]
    // 0xb306b4: StoreField: r2->field_f = r0
    //     0xb306b4: stur            w0, [x2, #0xf]
    // 0xb306b8: ldur            x0, [fp, #-0x18]
    // 0xb306bc: StoreField: r2->field_b = r0
    //     0xb306bc: stur            w0, [x2, #0xb]
    // 0xb306c0: ldur            x0, [fp, #-8]
    // 0xb306c4: LoadField: r1 = r0->field_b
    //     0xb306c4: ldur            w1, [x0, #0xb]
    // 0xb306c8: DecompressPointer r1
    //     0xb306c8: add             x1, x1, HEAP, lsl #32
    // 0xb306cc: cmp             w1, NULL
    // 0xb306d0: b.eq            #0xb30840
    // 0xb306d4: LoadField: r0 = r1->field_f
    //     0xb306d4: ldur            w0, [x1, #0xf]
    // 0xb306d8: DecompressPointer r0
    //     0xb306d8: add             x0, x0, HEAP, lsl #32
    // 0xb306dc: cmp             w0, NULL
    // 0xb306e0: b.ne            #0xb306ec
    // 0xb306e4: r0 = Null
    //     0xb306e4: mov             x0, NULL
    // 0xb306e8: b               #0xb306f8
    // 0xb306ec: LoadField: r1 = r0->field_2b
    //     0xb306ec: ldur            w1, [x0, #0x2b]
    // 0xb306f0: DecompressPointer r1
    //     0xb306f0: add             x1, x1, HEAP, lsl #32
    // 0xb306f4: mov             x0, x1
    // 0xb306f8: cmp             w0, NULL
    // 0xb306fc: b.ne            #0xb30704
    // 0xb30700: r0 = ""
    //     0xb30700: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb30704: ldur            x1, [fp, #-0x10]
    // 0xb30708: stur            x0, [fp, #-8]
    // 0xb3070c: r0 = of()
    //     0xb3070c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb30710: LoadField: r1 = r0->field_87
    //     0xb30710: ldur            w1, [x0, #0x87]
    // 0xb30714: DecompressPointer r1
    //     0xb30714: add             x1, x1, HEAP, lsl #32
    // 0xb30718: LoadField: r0 = r1->field_2b
    //     0xb30718: ldur            w0, [x1, #0x2b]
    // 0xb3071c: DecompressPointer r0
    //     0xb3071c: add             x0, x0, HEAP, lsl #32
    // 0xb30720: r16 = 14.000000
    //     0xb30720: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb30724: ldr             x16, [x16, #0x1d8]
    // 0xb30728: r30 = Instance_Color
    //     0xb30728: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3072c: stp             lr, x16, [SP]
    // 0xb30730: mov             x1, x0
    // 0xb30734: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb30734: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb30738: ldr             x4, [x4, #0xaa0]
    // 0xb3073c: r0 = copyWith()
    //     0xb3073c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb30740: stur            x0, [fp, #-0x10]
    // 0xb30744: r0 = Text()
    //     0xb30744: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb30748: mov             x3, x0
    // 0xb3074c: ldur            x0, [fp, #-8]
    // 0xb30750: stur            x3, [fp, #-0x18]
    // 0xb30754: StoreField: r3->field_b = r0
    //     0xb30754: stur            w0, [x3, #0xb]
    // 0xb30758: ldur            x0, [fp, #-0x10]
    // 0xb3075c: StoreField: r3->field_13 = r0
    //     0xb3075c: stur            w0, [x3, #0x13]
    // 0xb30760: r1 = Null
    //     0xb30760: mov             x1, NULL
    // 0xb30764: r2 = 6
    //     0xb30764: movz            x2, #0x6
    // 0xb30768: r0 = AllocateArray()
    //     0xb30768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3076c: mov             x2, x0
    // 0xb30770: ldur            x0, [fp, #-0x28]
    // 0xb30774: stur            x2, [fp, #-8]
    // 0xb30778: StoreField: r2->field_f = r0
    //     0xb30778: stur            w0, [x2, #0xf]
    // 0xb3077c: r16 = Instance_Spacer
    //     0xb3077c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb30780: ldr             x16, [x16, #0xf0]
    // 0xb30784: StoreField: r2->field_13 = r16
    //     0xb30784: stur            w16, [x2, #0x13]
    // 0xb30788: ldur            x0, [fp, #-0x18]
    // 0xb3078c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb3078c: stur            w0, [x2, #0x17]
    // 0xb30790: r1 = <Widget>
    //     0xb30790: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb30794: r0 = AllocateGrowableArray()
    //     0xb30794: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb30798: mov             x1, x0
    // 0xb3079c: ldur            x0, [fp, #-8]
    // 0xb307a0: stur            x1, [fp, #-0x10]
    // 0xb307a4: StoreField: r1->field_f = r0
    //     0xb307a4: stur            w0, [x1, #0xf]
    // 0xb307a8: r0 = 6
    //     0xb307a8: movz            x0, #0x6
    // 0xb307ac: StoreField: r1->field_b = r0
    //     0xb307ac: stur            w0, [x1, #0xb]
    // 0xb307b0: r0 = Row()
    //     0xb307b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb307b4: r1 = Instance_Axis
    //     0xb307b4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb307b8: StoreField: r0->field_f = r1
    //     0xb307b8: stur            w1, [x0, #0xf]
    // 0xb307bc: r1 = Instance_MainAxisAlignment
    //     0xb307bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb307c0: ldr             x1, [x1, #0xa08]
    // 0xb307c4: StoreField: r0->field_13 = r1
    //     0xb307c4: stur            w1, [x0, #0x13]
    // 0xb307c8: r1 = Instance_MainAxisSize
    //     0xb307c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb307cc: ldr             x1, [x1, #0xa10]
    // 0xb307d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb307d0: stur            w1, [x0, #0x17]
    // 0xb307d4: r1 = Instance_CrossAxisAlignment
    //     0xb307d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb307d8: ldr             x1, [x1, #0xa18]
    // 0xb307dc: StoreField: r0->field_1b = r1
    //     0xb307dc: stur            w1, [x0, #0x1b]
    // 0xb307e0: r1 = Instance_VerticalDirection
    //     0xb307e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb307e4: ldr             x1, [x1, #0xa20]
    // 0xb307e8: StoreField: r0->field_23 = r1
    //     0xb307e8: stur            w1, [x0, #0x23]
    // 0xb307ec: r1 = Instance_Clip
    //     0xb307ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb307f0: ldr             x1, [x1, #0x38]
    // 0xb307f4: StoreField: r0->field_2b = r1
    //     0xb307f4: stur            w1, [x0, #0x2b]
    // 0xb307f8: StoreField: r0->field_2f = rZR
    //     0xb307f8: stur            xzr, [x0, #0x2f]
    // 0xb307fc: ldur            x1, [fp, #-0x10]
    // 0xb30800: StoreField: r0->field_b = r1
    //     0xb30800: stur            w1, [x0, #0xb]
    // 0xb30804: LeaveFrame
    //     0xb30804: mov             SP, fp
    //     0xb30808: ldp             fp, lr, [SP], #0x10
    // 0xb3080c: ret
    //     0xb3080c: ret             
    // 0xb30810: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb30810: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb30814: b               #0xb30018
    // 0xb30818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb30818: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3081c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3081c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb30820: r9 = value
    //     0xb30820: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ab50] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb30824: ldr             x9, [x9, #0xb50]
    // 0xb30828: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb30828: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb3082c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3082c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb30830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb30830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb30834: r9 = customizedValue
    //     0xb30834: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ab58] Field <<EMAIL>>: late (offset: 0x18)
    //     0xb30838: ldr             x9, [x9, #0xb58]
    // 0xb3083c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb3083c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb30840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb30840: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4118, size: 0x14, field offset: 0xc
//   const constructor, 
class BagText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e6dc, size: 0x30
    // 0xc7e6dc: EnterFrame
    //     0xc7e6dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e6e0: mov             fp, SP
    // 0xc7e6e4: mov             x0, x1
    // 0xc7e6e8: r1 = <BagText>
    //     0xc7e6e8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e48] TypeArguments: <BagText>
    //     0xc7e6ec: ldr             x1, [x1, #0xe48]
    // 0xc7e6f0: r0 = _BagTextState()
    //     0xc7e6f0: bl              #0xc7e70c  ; Allocate_BagTextStateStub -> _BagTextState (size=0x1c)
    // 0xc7e6f4: r1 = Sentinel
    //     0xc7e6f4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7e6f8: StoreField: r0->field_13 = r1
    //     0xc7e6f8: stur            w1, [x0, #0x13]
    // 0xc7e6fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7e6fc: stur            w1, [x0, #0x17]
    // 0xc7e700: LeaveFrame
    //     0xc7e700: mov             SP, fp
    //     0xc7e704: ldp             fp, lr, [SP], #0x10
    // 0xc7e708: ret
    //     0xc7e708: ret             
  }
}
