// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart

// class id: 1049497, size: 0x8
class :: {
}

// class id: 3265, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __OtpBottomSheetState&State&CodeAutoFill extends State<dynamic>
     with CodeAutoFill {
}

// class id: 3266, size: 0x24, field offset: 0x14
class _OtpBottomSheetState extends __OtpBottomSheetState&State&CodeAutoFill {

  _ registerOtpListenListener(/* No info */) async {
    // ** addr: 0x905b7c, size: 0x54
    // 0x905b7c: EnterFrame
    //     0x905b7c: stp             fp, lr, [SP, #-0x10]!
    //     0x905b80: mov             fp, SP
    // 0x905b84: AllocStack(0x18)
    //     0x905b84: sub             SP, SP, #0x18
    // 0x905b88: SetupParameters(_OtpBottomSheetState this /* r1 => r1, fp-0x10 */)
    //     0x905b88: stur            NULL, [fp, #-8]
    //     0x905b8c: stur            x1, [fp, #-0x10]
    // 0x905b90: CheckStackOverflow
    //     0x905b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x905b94: cmp             SP, x16
    //     0x905b98: b.ls            #0x905bc8
    // 0x905b9c: InitAsync() -> Future<void?>
    //     0x905b9c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x905ba0: bl              #0x6326e0  ; InitAsyncStub
    // 0x905ba4: r1 = Null
    //     0x905ba4: mov             x1, NULL
    // 0x905ba8: r0 = SmsAutoFill()
    //     0x905ba8: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0x905bac: mov             x1, x0
    // 0x905bb0: r0 = listenForCode()
    //     0x905bb0: bl              #0x905bf0  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::listenForCode
    // 0x905bb4: mov             x1, x0
    // 0x905bb8: stur            x1, [fp, #-0x18]
    // 0x905bbc: r0 = Await()
    //     0x905bbc: bl              #0x63248c  ; AwaitStub
    // 0x905bc0: r0 = Null
    //     0x905bc0: mov             x0, NULL
    // 0x905bc4: r0 = ReturnAsyncNotFuture()
    //     0x905bc4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x905bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905bc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905bcc: b               #0x905b9c
  }
  _ initState(/* No info */) {
    // ** addr: 0x948694, size: 0x30
    // 0x948694: EnterFrame
    //     0x948694: stp             fp, lr, [SP, #-0x10]!
    //     0x948698: mov             fp, SP
    // 0x94869c: CheckStackOverflow
    //     0x94869c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9486a0: cmp             SP, x16
    //     0x9486a4: b.ls            #0x9486bc
    // 0x9486a8: r0 = registerOtpListenListener()
    //     0x9486a8: bl              #0x905b7c  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::registerOtpListenListener
    // 0x9486ac: r0 = Null
    //     0x9486ac: mov             x0, NULL
    // 0x9486b0: LeaveFrame
    //     0x9486b0: mov             SP, fp
    //     0x9486b4: ldp             fp, lr, [SP], #0x10
    // 0x9486b8: ret
    //     0x9486b8: ret             
    // 0x9486bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9486bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9486c0: b               #0x9486a8
  }
  _ build(/* No info */) {
    // ** addr: 0xbc1670, size: 0xb44
    // 0xbc1670: EnterFrame
    //     0xbc1670: stp             fp, lr, [SP, #-0x10]!
    //     0xbc1674: mov             fp, SP
    // 0xbc1678: AllocStack(0x80)
    //     0xbc1678: sub             SP, SP, #0x80
    // 0xbc167c: SetupParameters(_OtpBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbc167c: mov             x0, x1
    //     0xbc1680: stur            x1, [fp, #-8]
    //     0xbc1684: mov             x1, x2
    //     0xbc1688: stur            x2, [fp, #-0x10]
    // 0xbc168c: CheckStackOverflow
    //     0xbc168c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc1690: cmp             SP, x16
    //     0xbc1694: b.ls            #0xbc218c
    // 0xbc1698: r1 = 2
    //     0xbc1698: movz            x1, #0x2
    // 0xbc169c: r0 = AllocateContext()
    //     0xbc169c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbc16a0: mov             x2, x0
    // 0xbc16a4: ldur            x0, [fp, #-8]
    // 0xbc16a8: stur            x2, [fp, #-0x18]
    // 0xbc16ac: StoreField: r2->field_f = r0
    //     0xbc16ac: stur            w0, [x2, #0xf]
    // 0xbc16b0: ldur            x1, [fp, #-0x10]
    // 0xbc16b4: StoreField: r2->field_13 = r1
    //     0xbc16b4: stur            w1, [x2, #0x13]
    // 0xbc16b8: r0 = of()
    //     0xbc16b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc16bc: LoadField: r1 = r0->field_87
    //     0xbc16bc: ldur            w1, [x0, #0x87]
    // 0xbc16c0: DecompressPointer r1
    //     0xbc16c0: add             x1, x1, HEAP, lsl #32
    // 0xbc16c4: LoadField: r0 = r1->field_7
    //     0xbc16c4: ldur            w0, [x1, #7]
    // 0xbc16c8: DecompressPointer r0
    //     0xbc16c8: add             x0, x0, HEAP, lsl #32
    // 0xbc16cc: r16 = Instance_Color
    //     0xbc16cc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc16d0: r30 = 14.000000
    //     0xbc16d0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc16d4: ldr             lr, [lr, #0x1d8]
    // 0xbc16d8: stp             lr, x16, [SP]
    // 0xbc16dc: mov             x1, x0
    // 0xbc16e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc16e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc16e4: ldr             x4, [x4, #0x9b8]
    // 0xbc16e8: r0 = copyWith()
    //     0xbc16e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc16ec: stur            x0, [fp, #-0x10]
    // 0xbc16f0: r0 = Text()
    //     0xbc16f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc16f4: mov             x1, x0
    // 0xbc16f8: r0 = "Enter OTP to confirm"
    //     0xbc16f8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ee8] "Enter OTP to confirm"
    //     0xbc16fc: ldr             x0, [x0, #0xee8]
    // 0xbc1700: stur            x1, [fp, #-0x20]
    // 0xbc1704: StoreField: r1->field_b = r0
    //     0xbc1704: stur            w0, [x1, #0xb]
    // 0xbc1708: ldur            x0, [fp, #-0x10]
    // 0xbc170c: StoreField: r1->field_13 = r0
    //     0xbc170c: stur            w0, [x1, #0x13]
    // 0xbc1710: r0 = SvgPicture()
    //     0xbc1710: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbc1714: mov             x1, x0
    // 0xbc1718: r2 = "assets/images/x.svg"
    //     0xbc1718: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbc171c: ldr             x2, [x2, #0x5e8]
    // 0xbc1720: stur            x0, [fp, #-0x10]
    // 0xbc1724: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbc1724: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbc1728: r0 = SvgPicture.asset()
    //     0xbc1728: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbc172c: r0 = InkWell()
    //     0xbc172c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc1730: mov             x3, x0
    // 0xbc1734: ldur            x0, [fp, #-0x10]
    // 0xbc1738: stur            x3, [fp, #-0x28]
    // 0xbc173c: StoreField: r3->field_b = r0
    //     0xbc173c: stur            w0, [x3, #0xb]
    // 0xbc1740: ldur            x2, [fp, #-0x18]
    // 0xbc1744: r1 = Function '<anonymous closure>':.
    //     0xbc1744: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c2f8] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbc1748: ldr             x1, [x1, #0x2f8]
    // 0xbc174c: r0 = AllocateClosure()
    //     0xbc174c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1750: mov             x1, x0
    // 0xbc1754: ldur            x0, [fp, #-0x28]
    // 0xbc1758: StoreField: r0->field_f = r1
    //     0xbc1758: stur            w1, [x0, #0xf]
    // 0xbc175c: r3 = true
    //     0xbc175c: add             x3, NULL, #0x20  ; true
    // 0xbc1760: StoreField: r0->field_43 = r3
    //     0xbc1760: stur            w3, [x0, #0x43]
    // 0xbc1764: r4 = Instance_BoxShape
    //     0xbc1764: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc1768: ldr             x4, [x4, #0x80]
    // 0xbc176c: StoreField: r0->field_47 = r4
    //     0xbc176c: stur            w4, [x0, #0x47]
    // 0xbc1770: StoreField: r0->field_6f = r3
    //     0xbc1770: stur            w3, [x0, #0x6f]
    // 0xbc1774: r5 = false
    //     0xbc1774: add             x5, NULL, #0x30  ; false
    // 0xbc1778: StoreField: r0->field_73 = r5
    //     0xbc1778: stur            w5, [x0, #0x73]
    // 0xbc177c: StoreField: r0->field_83 = r3
    //     0xbc177c: stur            w3, [x0, #0x83]
    // 0xbc1780: StoreField: r0->field_7b = r5
    //     0xbc1780: stur            w5, [x0, #0x7b]
    // 0xbc1784: r1 = Null
    //     0xbc1784: mov             x1, NULL
    // 0xbc1788: r2 = 6
    //     0xbc1788: movz            x2, #0x6
    // 0xbc178c: r0 = AllocateArray()
    //     0xbc178c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc1790: mov             x2, x0
    // 0xbc1794: ldur            x0, [fp, #-0x20]
    // 0xbc1798: stur            x2, [fp, #-0x10]
    // 0xbc179c: StoreField: r2->field_f = r0
    //     0xbc179c: stur            w0, [x2, #0xf]
    // 0xbc17a0: r16 = Instance_Spacer
    //     0xbc17a0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbc17a4: ldr             x16, [x16, #0xf0]
    // 0xbc17a8: StoreField: r2->field_13 = r16
    //     0xbc17a8: stur            w16, [x2, #0x13]
    // 0xbc17ac: ldur            x0, [fp, #-0x28]
    // 0xbc17b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc17b0: stur            w0, [x2, #0x17]
    // 0xbc17b4: r1 = <Widget>
    //     0xbc17b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc17b8: r0 = AllocateGrowableArray()
    //     0xbc17b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc17bc: mov             x1, x0
    // 0xbc17c0: ldur            x0, [fp, #-0x10]
    // 0xbc17c4: stur            x1, [fp, #-0x20]
    // 0xbc17c8: StoreField: r1->field_f = r0
    //     0xbc17c8: stur            w0, [x1, #0xf]
    // 0xbc17cc: r0 = 6
    //     0xbc17cc: movz            x0, #0x6
    // 0xbc17d0: StoreField: r1->field_b = r0
    //     0xbc17d0: stur            w0, [x1, #0xb]
    // 0xbc17d4: r0 = Row()
    //     0xbc17d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc17d8: mov             x1, x0
    // 0xbc17dc: r0 = Instance_Axis
    //     0xbc17dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc17e0: stur            x1, [fp, #-0x10]
    // 0xbc17e4: StoreField: r1->field_f = r0
    //     0xbc17e4: stur            w0, [x1, #0xf]
    // 0xbc17e8: r2 = Instance_MainAxisAlignment
    //     0xbc17e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc17ec: ldr             x2, [x2, #0xa08]
    // 0xbc17f0: StoreField: r1->field_13 = r2
    //     0xbc17f0: stur            w2, [x1, #0x13]
    // 0xbc17f4: r3 = Instance_MainAxisSize
    //     0xbc17f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc17f8: ldr             x3, [x3, #0xa10]
    // 0xbc17fc: ArrayStore: r1[0] = r3  ; List_4
    //     0xbc17fc: stur            w3, [x1, #0x17]
    // 0xbc1800: r4 = Instance_CrossAxisAlignment
    //     0xbc1800: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc1804: ldr             x4, [x4, #0xa18]
    // 0xbc1808: StoreField: r1->field_1b = r4
    //     0xbc1808: stur            w4, [x1, #0x1b]
    // 0xbc180c: r5 = Instance_VerticalDirection
    //     0xbc180c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc1810: ldr             x5, [x5, #0xa20]
    // 0xbc1814: StoreField: r1->field_23 = r5
    //     0xbc1814: stur            w5, [x1, #0x23]
    // 0xbc1818: r6 = Instance_Clip
    //     0xbc1818: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc181c: ldr             x6, [x6, #0x38]
    // 0xbc1820: StoreField: r1->field_2b = r6
    //     0xbc1820: stur            w6, [x1, #0x2b]
    // 0xbc1824: StoreField: r1->field_2f = rZR
    //     0xbc1824: stur            xzr, [x1, #0x2f]
    // 0xbc1828: ldur            x7, [fp, #-0x20]
    // 0xbc182c: StoreField: r1->field_b = r7
    //     0xbc182c: stur            w7, [x1, #0xb]
    // 0xbc1830: r0 = Padding()
    //     0xbc1830: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc1834: mov             x3, x0
    // 0xbc1838: r0 = Instance_EdgeInsets
    //     0xbc1838: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc183c: ldr             x0, [x0, #0x668]
    // 0xbc1840: stur            x3, [fp, #-0x20]
    // 0xbc1844: StoreField: r3->field_f = r0
    //     0xbc1844: stur            w0, [x3, #0xf]
    // 0xbc1848: ldur            x1, [fp, #-0x10]
    // 0xbc184c: StoreField: r3->field_b = r1
    //     0xbc184c: stur            w1, [x3, #0xb]
    // 0xbc1850: r1 = Null
    //     0xbc1850: mov             x1, NULL
    // 0xbc1854: r2 = 4
    //     0xbc1854: movz            x2, #0x4
    // 0xbc1858: r0 = AllocateArray()
    //     0xbc1858: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc185c: r16 = "Otp sent to "
    //     0xbc185c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ef0] "Otp sent to "
    //     0xbc1860: ldr             x16, [x16, #0xef0]
    // 0xbc1864: StoreField: r0->field_f = r16
    //     0xbc1864: stur            w16, [x0, #0xf]
    // 0xbc1868: ldur            x1, [fp, #-8]
    // 0xbc186c: LoadField: r2 = r1->field_b
    //     0xbc186c: ldur            w2, [x1, #0xb]
    // 0xbc1870: DecompressPointer r2
    //     0xbc1870: add             x2, x2, HEAP, lsl #32
    // 0xbc1874: cmp             w2, NULL
    // 0xbc1878: b.eq            #0xbc2194
    // 0xbc187c: LoadField: r3 = r2->field_13
    //     0xbc187c: ldur            w3, [x2, #0x13]
    // 0xbc1880: DecompressPointer r3
    //     0xbc1880: add             x3, x3, HEAP, lsl #32
    // 0xbc1884: StoreField: r0->field_13 = r3
    //     0xbc1884: stur            w3, [x0, #0x13]
    // 0xbc1888: str             x0, [SP]
    // 0xbc188c: r0 = _interpolate()
    //     0xbc188c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc1890: ldur            x2, [fp, #-0x18]
    // 0xbc1894: stur            x0, [fp, #-0x10]
    // 0xbc1898: LoadField: r1 = r2->field_13
    //     0xbc1898: ldur            w1, [x2, #0x13]
    // 0xbc189c: DecompressPointer r1
    //     0xbc189c: add             x1, x1, HEAP, lsl #32
    // 0xbc18a0: r0 = of()
    //     0xbc18a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc18a4: LoadField: r1 = r0->field_87
    //     0xbc18a4: ldur            w1, [x0, #0x87]
    // 0xbc18a8: DecompressPointer r1
    //     0xbc18a8: add             x1, x1, HEAP, lsl #32
    // 0xbc18ac: LoadField: r0 = r1->field_7
    //     0xbc18ac: ldur            w0, [x1, #7]
    // 0xbc18b0: DecompressPointer r0
    //     0xbc18b0: add             x0, x0, HEAP, lsl #32
    // 0xbc18b4: r16 = Instance_Color
    //     0xbc18b4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc18b8: r30 = 12.000000
    //     0xbc18b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc18bc: ldr             lr, [lr, #0x9e8]
    // 0xbc18c0: stp             lr, x16, [SP]
    // 0xbc18c4: mov             x1, x0
    // 0xbc18c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc18c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc18cc: ldr             x4, [x4, #0x9b8]
    // 0xbc18d0: r0 = copyWith()
    //     0xbc18d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc18d4: stur            x0, [fp, #-0x28]
    // 0xbc18d8: r0 = Text()
    //     0xbc18d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc18dc: mov             x2, x0
    // 0xbc18e0: ldur            x0, [fp, #-0x10]
    // 0xbc18e4: stur            x2, [fp, #-0x30]
    // 0xbc18e8: StoreField: r2->field_b = r0
    //     0xbc18e8: stur            w0, [x2, #0xb]
    // 0xbc18ec: ldur            x0, [fp, #-0x28]
    // 0xbc18f0: StoreField: r2->field_13 = r0
    //     0xbc18f0: stur            w0, [x2, #0x13]
    // 0xbc18f4: ldur            x0, [fp, #-0x18]
    // 0xbc18f8: LoadField: r1 = r0->field_13
    //     0xbc18f8: ldur            w1, [x0, #0x13]
    // 0xbc18fc: DecompressPointer r1
    //     0xbc18fc: add             x1, x1, HEAP, lsl #32
    // 0xbc1900: r0 = of()
    //     0xbc1900: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1904: LoadField: r1 = r0->field_87
    //     0xbc1904: ldur            w1, [x0, #0x87]
    // 0xbc1908: DecompressPointer r1
    //     0xbc1908: add             x1, x1, HEAP, lsl #32
    // 0xbc190c: LoadField: r0 = r1->field_7
    //     0xbc190c: ldur            w0, [x1, #7]
    // 0xbc1910: DecompressPointer r0
    //     0xbc1910: add             x0, x0, HEAP, lsl #32
    // 0xbc1914: r16 = Instance_Color
    //     0xbc1914: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc1918: r30 = 14.000000
    //     0xbc1918: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc191c: ldr             lr, [lr, #0x1d8]
    // 0xbc1920: stp             lr, x16, [SP, #8]
    // 0xbc1924: r16 = Instance_TextDecoration
    //     0xbc1924: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbc1928: ldr             x16, [x16, #0x10]
    // 0xbc192c: str             x16, [SP]
    // 0xbc1930: mov             x1, x0
    // 0xbc1934: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xbc1934: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xbc1938: ldr             x4, [x4, #0x7c8]
    // 0xbc193c: r0 = copyWith()
    //     0xbc193c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc1940: stur            x0, [fp, #-0x10]
    // 0xbc1944: r0 = Text()
    //     0xbc1944: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc1948: mov             x1, x0
    // 0xbc194c: r0 = "EDIT"
    //     0xbc194c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ef8] "EDIT"
    //     0xbc1950: ldr             x0, [x0, #0xef8]
    // 0xbc1954: stur            x1, [fp, #-0x28]
    // 0xbc1958: StoreField: r1->field_b = r0
    //     0xbc1958: stur            w0, [x1, #0xb]
    // 0xbc195c: ldur            x0, [fp, #-0x10]
    // 0xbc1960: StoreField: r1->field_13 = r0
    //     0xbc1960: stur            w0, [x1, #0x13]
    // 0xbc1964: r0 = InkWell()
    //     0xbc1964: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc1968: mov             x3, x0
    // 0xbc196c: ldur            x0, [fp, #-0x28]
    // 0xbc1970: stur            x3, [fp, #-0x10]
    // 0xbc1974: StoreField: r3->field_b = r0
    //     0xbc1974: stur            w0, [x3, #0xb]
    // 0xbc1978: ldur            x2, [fp, #-0x18]
    // 0xbc197c: r1 = Function '<anonymous closure>':.
    //     0xbc197c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c300] AnonymousClosure: (0xbc2c14), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc1980: ldr             x1, [x1, #0x300]
    // 0xbc1984: r0 = AllocateClosure()
    //     0xbc1984: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1988: mov             x1, x0
    // 0xbc198c: ldur            x0, [fp, #-0x10]
    // 0xbc1990: StoreField: r0->field_f = r1
    //     0xbc1990: stur            w1, [x0, #0xf]
    // 0xbc1994: r1 = true
    //     0xbc1994: add             x1, NULL, #0x20  ; true
    // 0xbc1998: StoreField: r0->field_43 = r1
    //     0xbc1998: stur            w1, [x0, #0x43]
    // 0xbc199c: r2 = Instance_BoxShape
    //     0xbc199c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc19a0: ldr             x2, [x2, #0x80]
    // 0xbc19a4: StoreField: r0->field_47 = r2
    //     0xbc19a4: stur            w2, [x0, #0x47]
    // 0xbc19a8: StoreField: r0->field_6f = r1
    //     0xbc19a8: stur            w1, [x0, #0x6f]
    // 0xbc19ac: r2 = false
    //     0xbc19ac: add             x2, NULL, #0x30  ; false
    // 0xbc19b0: StoreField: r0->field_73 = r2
    //     0xbc19b0: stur            w2, [x0, #0x73]
    // 0xbc19b4: StoreField: r0->field_83 = r1
    //     0xbc19b4: stur            w1, [x0, #0x83]
    // 0xbc19b8: StoreField: r0->field_7b = r2
    //     0xbc19b8: stur            w2, [x0, #0x7b]
    // 0xbc19bc: r0 = Padding()
    //     0xbc19bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc19c0: mov             x3, x0
    // 0xbc19c4: r0 = Instance_EdgeInsets
    //     0xbc19c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xbc19c8: ldr             x0, [x0, #0xe60]
    // 0xbc19cc: stur            x3, [fp, #-0x28]
    // 0xbc19d0: StoreField: r3->field_f = r0
    //     0xbc19d0: stur            w0, [x3, #0xf]
    // 0xbc19d4: ldur            x0, [fp, #-0x10]
    // 0xbc19d8: StoreField: r3->field_b = r0
    //     0xbc19d8: stur            w0, [x3, #0xb]
    // 0xbc19dc: r1 = Null
    //     0xbc19dc: mov             x1, NULL
    // 0xbc19e0: r2 = 4
    //     0xbc19e0: movz            x2, #0x4
    // 0xbc19e4: r0 = AllocateArray()
    //     0xbc19e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc19e8: mov             x2, x0
    // 0xbc19ec: ldur            x0, [fp, #-0x30]
    // 0xbc19f0: stur            x2, [fp, #-0x10]
    // 0xbc19f4: StoreField: r2->field_f = r0
    //     0xbc19f4: stur            w0, [x2, #0xf]
    // 0xbc19f8: ldur            x0, [fp, #-0x28]
    // 0xbc19fc: StoreField: r2->field_13 = r0
    //     0xbc19fc: stur            w0, [x2, #0x13]
    // 0xbc1a00: r1 = <Widget>
    //     0xbc1a00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc1a04: r0 = AllocateGrowableArray()
    //     0xbc1a04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc1a08: mov             x1, x0
    // 0xbc1a0c: ldur            x0, [fp, #-0x10]
    // 0xbc1a10: stur            x1, [fp, #-0x28]
    // 0xbc1a14: StoreField: r1->field_f = r0
    //     0xbc1a14: stur            w0, [x1, #0xf]
    // 0xbc1a18: r2 = 4
    //     0xbc1a18: movz            x2, #0x4
    // 0xbc1a1c: StoreField: r1->field_b = r2
    //     0xbc1a1c: stur            w2, [x1, #0xb]
    // 0xbc1a20: r0 = Row()
    //     0xbc1a20: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc1a24: mov             x1, x0
    // 0xbc1a28: r0 = Instance_Axis
    //     0xbc1a28: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc1a2c: stur            x1, [fp, #-0x10]
    // 0xbc1a30: StoreField: r1->field_f = r0
    //     0xbc1a30: stur            w0, [x1, #0xf]
    // 0xbc1a34: r2 = Instance_MainAxisAlignment
    //     0xbc1a34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc1a38: ldr             x2, [x2, #0xa08]
    // 0xbc1a3c: StoreField: r1->field_13 = r2
    //     0xbc1a3c: stur            w2, [x1, #0x13]
    // 0xbc1a40: r2 = Instance_MainAxisSize
    //     0xbc1a40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc1a44: ldr             x2, [x2, #0xa10]
    // 0xbc1a48: ArrayStore: r1[0] = r2  ; List_4
    //     0xbc1a48: stur            w2, [x1, #0x17]
    // 0xbc1a4c: r2 = Instance_CrossAxisAlignment
    //     0xbc1a4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc1a50: ldr             x2, [x2, #0xa18]
    // 0xbc1a54: StoreField: r1->field_1b = r2
    //     0xbc1a54: stur            w2, [x1, #0x1b]
    // 0xbc1a58: r2 = Instance_VerticalDirection
    //     0xbc1a58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc1a5c: ldr             x2, [x2, #0xa20]
    // 0xbc1a60: StoreField: r1->field_23 = r2
    //     0xbc1a60: stur            w2, [x1, #0x23]
    // 0xbc1a64: r3 = Instance_Clip
    //     0xbc1a64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc1a68: ldr             x3, [x3, #0x38]
    // 0xbc1a6c: StoreField: r1->field_2b = r3
    //     0xbc1a6c: stur            w3, [x1, #0x2b]
    // 0xbc1a70: StoreField: r1->field_2f = rZR
    //     0xbc1a70: stur            xzr, [x1, #0x2f]
    // 0xbc1a74: ldur            x4, [fp, #-0x28]
    // 0xbc1a78: StoreField: r1->field_b = r4
    //     0xbc1a78: stur            w4, [x1, #0xb]
    // 0xbc1a7c: r0 = Padding()
    //     0xbc1a7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc1a80: mov             x1, x0
    // 0xbc1a84: r0 = Instance_EdgeInsets
    //     0xbc1a84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc1a88: ldr             x0, [x0, #0x668]
    // 0xbc1a8c: stur            x1, [fp, #-0x28]
    // 0xbc1a90: StoreField: r1->field_f = r0
    //     0xbc1a90: stur            w0, [x1, #0xf]
    // 0xbc1a94: ldur            x2, [fp, #-0x10]
    // 0xbc1a98: StoreField: r1->field_b = r2
    //     0xbc1a98: stur            w2, [x1, #0xb]
    // 0xbc1a9c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc1a9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1aa0: ldr             x0, [x0, #0x1c80]
    //     0xbc1aa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1aa8: cmp             w0, w16
    //     0xbc1aac: b.ne            #0xbc1ab8
    //     0xbc1ab0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbc1ab4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc1ab8: r0 = GetNavigation.width()
    //     0xbc1ab8: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xbc1abc: stur            d0, [fp, #-0x50]
    // 0xbc1ac0: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbc1ac0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc1ac4: ldr             x0, [x0, #0x1530]
    //     0xbc1ac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc1acc: cmp             w0, w16
    //     0xbc1ad0: b.ne            #0xbc1ae0
    //     0xbc1ad4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbc1ad8: ldr             x2, [x2, #0x120]
    //     0xbc1adc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc1ae0: stur            x0, [fp, #-0x10]
    // 0xbc1ae4: r16 = "[0-9]"
    //     0xbc1ae4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xbc1ae8: ldr             x16, [x16, #0x128]
    // 0xbc1aec: stp             x16, NULL, [SP, #0x20]
    // 0xbc1af0: r16 = false
    //     0xbc1af0: add             x16, NULL, #0x30  ; false
    // 0xbc1af4: r30 = true
    //     0xbc1af4: add             lr, NULL, #0x20  ; true
    // 0xbc1af8: stp             lr, x16, [SP, #0x10]
    // 0xbc1afc: r16 = false
    //     0xbc1afc: add             x16, NULL, #0x30  ; false
    // 0xbc1b00: r30 = false
    //     0xbc1b00: add             lr, NULL, #0x30  ; false
    // 0xbc1b04: stp             lr, x16, [SP]
    // 0xbc1b08: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xbc1b08: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xbc1b0c: r0 = _RegExp()
    //     0xbc1b0c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xbc1b10: stur            x0, [fp, #-0x30]
    // 0xbc1b14: r0 = FilteringTextInputFormatter()
    //     0xbc1b14: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xbc1b18: mov             x3, x0
    // 0xbc1b1c: ldur            x0, [fp, #-0x30]
    // 0xbc1b20: stur            x3, [fp, #-0x38]
    // 0xbc1b24: StoreField: r3->field_b = r0
    //     0xbc1b24: stur            w0, [x3, #0xb]
    // 0xbc1b28: r0 = true
    //     0xbc1b28: add             x0, NULL, #0x20  ; true
    // 0xbc1b2c: StoreField: r3->field_7 = r0
    //     0xbc1b2c: stur            w0, [x3, #7]
    // 0xbc1b30: r1 = ""
    //     0xbc1b30: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc1b34: StoreField: r3->field_f = r1
    //     0xbc1b34: stur            w1, [x3, #0xf]
    // 0xbc1b38: r1 = Null
    //     0xbc1b38: mov             x1, NULL
    // 0xbc1b3c: r2 = 4
    //     0xbc1b3c: movz            x2, #0x4
    // 0xbc1b40: r0 = AllocateArray()
    //     0xbc1b40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc1b44: mov             x2, x0
    // 0xbc1b48: ldur            x0, [fp, #-0x10]
    // 0xbc1b4c: stur            x2, [fp, #-0x30]
    // 0xbc1b50: StoreField: r2->field_f = r0
    //     0xbc1b50: stur            w0, [x2, #0xf]
    // 0xbc1b54: ldur            x0, [fp, #-0x38]
    // 0xbc1b58: StoreField: r2->field_13 = r0
    //     0xbc1b58: stur            w0, [x2, #0x13]
    // 0xbc1b5c: r1 = <TextInputFormatter>
    //     0xbc1b5c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbc1b60: ldr             x1, [x1, #0x7b0]
    // 0xbc1b64: r0 = AllocateGrowableArray()
    //     0xbc1b64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc1b68: mov             x2, x0
    // 0xbc1b6c: ldur            x0, [fp, #-0x30]
    // 0xbc1b70: stur            x2, [fp, #-0x10]
    // 0xbc1b74: StoreField: r2->field_f = r0
    //     0xbc1b74: stur            w0, [x2, #0xf]
    // 0xbc1b78: r0 = 4
    //     0xbc1b78: movz            x0, #0x4
    // 0xbc1b7c: StoreField: r2->field_b = r0
    //     0xbc1b7c: stur            w0, [x2, #0xb]
    // 0xbc1b80: ldur            x0, [fp, #-0x18]
    // 0xbc1b84: LoadField: r1 = r0->field_13
    //     0xbc1b84: ldur            w1, [x0, #0x13]
    // 0xbc1b88: DecompressPointer r1
    //     0xbc1b88: add             x1, x1, HEAP, lsl #32
    // 0xbc1b8c: r0 = of()
    //     0xbc1b8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1b90: LoadField: r1 = r0->field_5b
    //     0xbc1b90: ldur            w1, [x0, #0x5b]
    // 0xbc1b94: DecompressPointer r1
    //     0xbc1b94: add             x1, x1, HEAP, lsl #32
    // 0xbc1b98: stur            x1, [fp, #-0x30]
    // 0xbc1b9c: r0 = Cursor()
    //     0xbc1b9c: bl              #0xa09868  ; AllocateCursorStub -> Cursor (size=0x3c)
    // 0xbc1ba0: d0 = 1.000000
    //     0xbc1ba0: fmov            d0, #1.00000000
    // 0xbc1ba4: stur            x0, [fp, #-0x38]
    // 0xbc1ba8: StoreField: r0->field_7 = d0
    //     0xbc1ba8: stur            d0, [x0, #7]
    // 0xbc1bac: d1 = 27.000000
    //     0xbc1bac: fmov            d1, #27.00000000
    // 0xbc1bb0: StoreField: r0->field_f = d1
    //     0xbc1bb0: stur            d1, [x0, #0xf]
    // 0xbc1bb4: r1 = Instance_Radius
    //     0xbc1bb4: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0xbc1bb8: ldr             x1, [x1, #0xb48]
    // 0xbc1bbc: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc1bbc: stur            w1, [x0, #0x17]
    // 0xbc1bc0: ldur            x1, [fp, #-0x30]
    // 0xbc1bc4: StoreField: r0->field_1b = r1
    //     0xbc1bc4: stur            w1, [x0, #0x1b]
    // 0xbc1bc8: r1 = Instance_Duration
    //     0xbc1bc8: add             x1, PP, #0xa, lsl #12  ; [pp+0xa058] Obj!Duration@d777a1
    //     0xbc1bcc: ldr             x1, [x1, #0x58]
    // 0xbc1bd0: StoreField: r0->field_1f = r1
    //     0xbc1bd0: stur            w1, [x0, #0x1f]
    // 0xbc1bd4: r1 = Instance_Duration
    //     0xbc1bd4: ldr             x1, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbc1bd8: StoreField: r0->field_23 = r1
    //     0xbc1bd8: stur            w1, [x0, #0x23]
    // 0xbc1bdc: r1 = Instance_Duration
    //     0xbc1bdc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] Obj!Duration@d77851
    //     0xbc1be0: ldr             x1, [x1, #0x4b8]
    // 0xbc1be4: StoreField: r0->field_27 = r1
    //     0xbc1be4: stur            w1, [x0, #0x27]
    // 0xbc1be8: r1 = Instance_Orientation
    //     0xbc1be8: add             x1, PP, #0x37, lsl #12  ; [pp+0x37198] Obj!Orientation@d70241
    //     0xbc1bec: ldr             x1, [x1, #0x198]
    // 0xbc1bf0: StoreField: r0->field_2f = r1
    //     0xbc1bf0: stur            w1, [x0, #0x2f]
    // 0xbc1bf4: StoreField: r0->field_33 = rZR
    //     0xbc1bf4: stur            xzr, [x0, #0x33]
    // 0xbc1bf8: r2 = true
    //     0xbc1bf8: add             x2, NULL, #0x20  ; true
    // 0xbc1bfc: StoreField: r0->field_2b = r2
    //     0xbc1bfc: stur            w2, [x0, #0x2b]
    // 0xbc1c00: ldur            x3, [fp, #-0x18]
    // 0xbc1c04: LoadField: r1 = r3->field_13
    //     0xbc1c04: ldur            w1, [x3, #0x13]
    // 0xbc1c08: DecompressPointer r1
    //     0xbc1c08: add             x1, x1, HEAP, lsl #32
    // 0xbc1c0c: r0 = of()
    //     0xbc1c0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1c10: LoadField: r1 = r0->field_87
    //     0xbc1c10: ldur            w1, [x0, #0x87]
    // 0xbc1c14: DecompressPointer r1
    //     0xbc1c14: add             x1, x1, HEAP, lsl #32
    // 0xbc1c18: LoadField: r0 = r1->field_2b
    //     0xbc1c18: ldur            w0, [x1, #0x2b]
    // 0xbc1c1c: DecompressPointer r0
    //     0xbc1c1c: add             x0, x0, HEAP, lsl #32
    // 0xbc1c20: ldur            x2, [fp, #-0x18]
    // 0xbc1c24: stur            x0, [fp, #-0x30]
    // 0xbc1c28: LoadField: r1 = r2->field_13
    //     0xbc1c28: ldur            w1, [x2, #0x13]
    // 0xbc1c2c: DecompressPointer r1
    //     0xbc1c2c: add             x1, x1, HEAP, lsl #32
    // 0xbc1c30: r0 = of()
    //     0xbc1c30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1c34: LoadField: r1 = r0->field_5b
    //     0xbc1c34: ldur            w1, [x0, #0x5b]
    // 0xbc1c38: DecompressPointer r1
    //     0xbc1c38: add             x1, x1, HEAP, lsl #32
    // 0xbc1c3c: r16 = 16.000000
    //     0xbc1c3c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbc1c40: ldr             x16, [x16, #0x188]
    // 0xbc1c44: stp             x1, x16, [SP]
    // 0xbc1c48: ldur            x1, [fp, #-0x30]
    // 0xbc1c4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc1c4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc1c50: ldr             x4, [x4, #0xaa0]
    // 0xbc1c54: r0 = copyWith()
    //     0xbc1c54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc1c58: ldur            x2, [fp, #-0x18]
    // 0xbc1c5c: stur            x0, [fp, #-0x30]
    // 0xbc1c60: LoadField: r1 = r2->field_13
    //     0xbc1c60: ldur            w1, [x2, #0x13]
    // 0xbc1c64: DecompressPointer r1
    //     0xbc1c64: add             x1, x1, HEAP, lsl #32
    // 0xbc1c68: r0 = of()
    //     0xbc1c68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1c6c: LoadField: r1 = r0->field_5b
    //     0xbc1c6c: ldur            w1, [x0, #0x5b]
    // 0xbc1c70: DecompressPointer r1
    //     0xbc1c70: add             x1, x1, HEAP, lsl #32
    // 0xbc1c74: r0 = LoadClassIdInstr(r1)
    //     0xbc1c74: ldur            x0, [x1, #-1]
    //     0xbc1c78: ubfx            x0, x0, #0xc, #0x14
    // 0xbc1c7c: d0 = 0.300000
    //     0xbc1c7c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbc1c80: ldr             d0, [x17, #0x658]
    // 0xbc1c84: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbc1c84: sub             lr, x0, #0xffa
    //     0xbc1c88: ldr             lr, [x21, lr, lsl #3]
    //     0xbc1c8c: blr             lr
    // 0xbc1c90: r1 = <Color>
    //     0xbc1c90: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbc1c94: ldr             x1, [x1, #0xf80]
    // 0xbc1c98: stur            x0, [fp, #-0x40]
    // 0xbc1c9c: r0 = FixedColorBuilder()
    //     0xbc1c9c: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0xbc1ca0: mov             x1, x0
    // 0xbc1ca4: ldur            x0, [fp, #-0x40]
    // 0xbc1ca8: stur            x1, [fp, #-0x48]
    // 0xbc1cac: StoreField: r1->field_b = r0
    //     0xbc1cac: stur            w0, [x1, #0xb]
    // 0xbc1cb0: r0 = UnderlineDecoration()
    //     0xbc1cb0: bl              #0xa09850  ; AllocateUnderlineDecorationStub -> UnderlineDecoration (size=0x48)
    // 0xbc1cb4: d0 = 12.000000
    //     0xbc1cb4: fmov            d0, #12.00000000
    // 0xbc1cb8: stur            x0, [fp, #-0x40]
    // 0xbc1cbc: StoreField: r0->field_27 = d0
    //     0xbc1cbc: stur            d0, [x0, #0x27]
    // 0xbc1cc0: ldur            x1, [fp, #-0x48]
    // 0xbc1cc4: StoreField: r0->field_33 = r1
    //     0xbc1cc4: stur            w1, [x0, #0x33]
    // 0xbc1cc8: d0 = 1.000000
    //     0xbc1cc8: fmov            d0, #1.00000000
    // 0xbc1ccc: StoreField: r0->field_37 = d0
    //     0xbc1ccc: stur            d0, [x0, #0x37]
    // 0xbc1cd0: r1 = Sentinel
    //     0xbc1cd0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbc1cd4: StoreField: r0->field_23 = r1
    //     0xbc1cd4: stur            w1, [x0, #0x23]
    // 0xbc1cd8: ldur            x1, [fp, #-0x30]
    // 0xbc1cdc: StoreField: r0->field_7 = r1
    //     0xbc1cdc: stur            w1, [x0, #7]
    // 0xbc1ce0: ldur            x1, [fp, #-8]
    // 0xbc1ce4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbc1ce4: ldur            w2, [x1, #0x17]
    // 0xbc1ce8: DecompressPointer r2
    //     0xbc1ce8: add             x2, x2, HEAP, lsl #32
    // 0xbc1cec: stur            x2, [fp, #-0x30]
    // 0xbc1cf0: r0 = PinFieldAutoFill()
    //     0xbc1cf0: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0xbc1cf4: mov             x3, x0
    // 0xbc1cf8: r0 = Instance_TextInputType
    //     0xbc1cf8: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbc1cfc: ldr             x0, [x0, #0x1a0]
    // 0xbc1d00: stur            x3, [fp, #-0x48]
    // 0xbc1d04: StoreField: r3->field_33 = r0
    //     0xbc1d04: stur            w0, [x3, #0x33]
    // 0xbc1d08: r0 = Instance_TextInputAction
    //     0xbc1d08: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0xbc1d0c: StoreField: r3->field_37 = r0
    //     0xbc1d0c: stur            w0, [x3, #0x37]
    // 0xbc1d10: ldur            x0, [fp, #-0x38]
    // 0xbc1d14: StoreField: r3->field_2f = r0
    //     0xbc1d14: stur            w0, [x3, #0x2f]
    // 0xbc1d18: ldur            x0, [fp, #-0x10]
    // 0xbc1d1c: StoreField: r3->field_47 = r0
    //     0xbc1d1c: stur            w0, [x3, #0x47]
    // 0xbc1d20: r0 = true
    //     0xbc1d20: add             x0, NULL, #0x20  ; true
    // 0xbc1d24: StoreField: r3->field_3b = r0
    //     0xbc1d24: stur            w0, [x3, #0x3b]
    // 0xbc1d28: StoreField: r3->field_3f = r0
    //     0xbc1d28: stur            w0, [x3, #0x3f]
    // 0xbc1d2c: ldur            x1, [fp, #-0x40]
    // 0xbc1d30: StoreField: r3->field_27 = r1
    //     0xbc1d30: stur            w1, [x3, #0x27]
    // 0xbc1d34: ldur            x2, [fp, #-0x18]
    // 0xbc1d38: r1 = Function '<anonymous closure>':.
    //     0xbc1d38: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c308] AnonymousClosure: (0xbc2bac), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc1d3c: ldr             x1, [x1, #0x308]
    // 0xbc1d40: r0 = AllocateClosure()
    //     0xbc1d40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1d44: mov             x1, x0
    // 0xbc1d48: ldur            x0, [fp, #-0x48]
    // 0xbc1d4c: StoreField: r0->field_1f = r1
    //     0xbc1d4c: stur            w1, [x0, #0x1f]
    // 0xbc1d50: ldur            x2, [fp, #-0x18]
    // 0xbc1d54: r1 = Function '<anonymous closure>':.
    //     0xbc1d54: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c310] AnonymousClosure: (0xbc2a20), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc1d58: ldr             x1, [x1, #0x310]
    // 0xbc1d5c: r0 = AllocateClosure()
    //     0xbc1d5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1d60: mov             x1, x0
    // 0xbc1d64: ldur            x0, [fp, #-0x48]
    // 0xbc1d68: StoreField: r0->field_23 = r1
    //     0xbc1d68: stur            w1, [x0, #0x23]
    // 0xbc1d6c: ldur            x1, [fp, #-0x30]
    // 0xbc1d70: StoreField: r0->field_1b = r1
    //     0xbc1d70: stur            w1, [x0, #0x1b]
    // 0xbc1d74: r1 = false
    //     0xbc1d74: add             x1, NULL, #0x30  ; false
    // 0xbc1d78: StoreField: r0->field_13 = r1
    //     0xbc1d78: stur            w1, [x0, #0x13]
    // 0xbc1d7c: r2 = 4
    //     0xbc1d7c: movz            x2, #0x4
    // 0xbc1d80: StoreField: r0->field_b = r2
    //     0xbc1d80: stur            x2, [x0, #0xb]
    // 0xbc1d84: ldur            d0, [fp, #-0x50]
    // 0xbc1d88: r2 = inline_Allocate_Double()
    //     0xbc1d88: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xbc1d8c: add             x2, x2, #0x10
    //     0xbc1d90: cmp             x3, x2
    //     0xbc1d94: b.ls            #0xbc2198
    //     0xbc1d98: str             x2, [THR, #0x50]  ; THR::top
    //     0xbc1d9c: sub             x2, x2, #0xf
    //     0xbc1da0: movz            x3, #0xe15c
    //     0xbc1da4: movk            x3, #0x3, lsl #16
    //     0xbc1da8: stur            x3, [x2, #-1]
    // 0xbc1dac: StoreField: r2->field_7 = d0
    //     0xbc1dac: stur            d0, [x2, #7]
    // 0xbc1db0: stur            x2, [fp, #-0x10]
    // 0xbc1db4: r0 = Container()
    //     0xbc1db4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbc1db8: stur            x0, [fp, #-0x30]
    // 0xbc1dbc: r16 = Instance_EdgeInsets
    //     0xbc1dbc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f18] Obj!EdgeInsets@d57ec1
    //     0xbc1dc0: ldr             x16, [x16, #0xf18]
    // 0xbc1dc4: ldur            lr, [fp, #-0x10]
    // 0xbc1dc8: stp             lr, x16, [SP, #8]
    // 0xbc1dcc: ldur            x16, [fp, #-0x48]
    // 0xbc1dd0: str             x16, [SP]
    // 0xbc1dd4: mov             x1, x0
    // 0xbc1dd8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbc1dd8: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbc1ddc: ldr             x4, [x4, #0x1b8]
    // 0xbc1de0: r0 = Container()
    //     0xbc1de0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbc1de4: r0 = Padding()
    //     0xbc1de4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc1de8: mov             x3, x0
    // 0xbc1dec: r0 = Instance_EdgeInsets
    //     0xbc1dec: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f20] Obj!EdgeInsets@d57e91
    //     0xbc1df0: ldr             x0, [x0, #0xf20]
    // 0xbc1df4: stur            x3, [fp, #-0x38]
    // 0xbc1df8: StoreField: r3->field_f = r0
    //     0xbc1df8: stur            w0, [x3, #0xf]
    // 0xbc1dfc: ldur            x0, [fp, #-0x30]
    // 0xbc1e00: StoreField: r3->field_b = r0
    //     0xbc1e00: stur            w0, [x3, #0xb]
    // 0xbc1e04: ldur            x0, [fp, #-8]
    // 0xbc1e08: LoadField: r4 = r0->field_1f
    //     0xbc1e08: ldur            w4, [x0, #0x1f]
    // 0xbc1e0c: DecompressPointer r4
    //     0xbc1e0c: add             x4, x4, HEAP, lsl #32
    // 0xbc1e10: ldur            x2, [fp, #-0x18]
    // 0xbc1e14: stur            x4, [fp, #-0x10]
    // 0xbc1e18: r1 = Function '<anonymous closure>':.
    //     0xbc1e18: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c318] AnonymousClosure: (0xbc2244), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc1e1c: ldr             x1, [x1, #0x318]
    // 0xbc1e20: r0 = AllocateClosure()
    //     0xbc1e20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc1e24: r1 = <int, AsyncSnapshot<int>, int>
    //     0xbc1e24: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f30] TypeArguments: <int, AsyncSnapshot<int>, int>
    //     0xbc1e28: ldr             x1, [x1, #0xf30]
    // 0xbc1e2c: stur            x0, [fp, #-0x30]
    // 0xbc1e30: r0 = StreamBuilder()
    //     0xbc1e30: bl              #0xa09838  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xbc1e34: mov             x1, x0
    // 0xbc1e38: ldur            x0, [fp, #-0x30]
    // 0xbc1e3c: stur            x1, [fp, #-0x40]
    // 0xbc1e40: StoreField: r1->field_13 = r0
    //     0xbc1e40: stur            w0, [x1, #0x13]
    // 0xbc1e44: ldur            x0, [fp, #-0x10]
    // 0xbc1e48: StoreField: r1->field_f = r0
    //     0xbc1e48: stur            w0, [x1, #0xf]
    // 0xbc1e4c: r0 = Padding()
    //     0xbc1e4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc1e50: mov             x2, x0
    // 0xbc1e54: r0 = Instance_EdgeInsets
    //     0xbc1e54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc1e58: ldr             x0, [x0, #0x668]
    // 0xbc1e5c: stur            x2, [fp, #-0x10]
    // 0xbc1e60: StoreField: r2->field_f = r0
    //     0xbc1e60: stur            w0, [x2, #0xf]
    // 0xbc1e64: ldur            x1, [fp, #-0x40]
    // 0xbc1e68: StoreField: r2->field_b = r1
    //     0xbc1e68: stur            w1, [x2, #0xb]
    // 0xbc1e6c: ldur            x3, [fp, #-0x18]
    // 0xbc1e70: LoadField: r1 = r3->field_13
    //     0xbc1e70: ldur            w1, [x3, #0x13]
    // 0xbc1e74: DecompressPointer r1
    //     0xbc1e74: add             x1, x1, HEAP, lsl #32
    // 0xbc1e78: r0 = of()
    //     0xbc1e78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1e7c: LoadField: r1 = r0->field_5b
    //     0xbc1e7c: ldur            w1, [x0, #0x5b]
    // 0xbc1e80: DecompressPointer r1
    //     0xbc1e80: add             x1, x1, HEAP, lsl #32
    // 0xbc1e84: stur            x1, [fp, #-0x30]
    // 0xbc1e88: r0 = Divider()
    //     0xbc1e88: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbc1e8c: mov             x1, x0
    // 0xbc1e90: ldur            x0, [fp, #-0x30]
    // 0xbc1e94: stur            x1, [fp, #-0x40]
    // 0xbc1e98: StoreField: r1->field_1f = r0
    //     0xbc1e98: stur            w0, [x1, #0x1f]
    // 0xbc1e9c: r0 = Padding()
    //     0xbc1e9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc1ea0: mov             x2, x0
    // 0xbc1ea4: r0 = Instance_EdgeInsets
    //     0xbc1ea4: add             x0, PP, #0x58, lsl #12  ; [pp+0x58ac8] Obj!EdgeInsets@d581c1
    //     0xbc1ea8: ldr             x0, [x0, #0xac8]
    // 0xbc1eac: stur            x2, [fp, #-0x30]
    // 0xbc1eb0: StoreField: r2->field_f = r0
    //     0xbc1eb0: stur            w0, [x2, #0xf]
    // 0xbc1eb4: ldur            x0, [fp, #-0x40]
    // 0xbc1eb8: StoreField: r2->field_b = r0
    //     0xbc1eb8: stur            w0, [x2, #0xb]
    // 0xbc1ebc: ldur            x0, [fp, #-8]
    // 0xbc1ec0: LoadField: r1 = r0->field_1b
    //     0xbc1ec0: ldur            w1, [x0, #0x1b]
    // 0xbc1ec4: DecompressPointer r1
    //     0xbc1ec4: add             x1, x1, HEAP, lsl #32
    // 0xbc1ec8: tbnz            w1, #4, #0xbc1eec
    // 0xbc1ecc: ldur            x0, [fp, #-0x18]
    // 0xbc1ed0: LoadField: r1 = r0->field_13
    //     0xbc1ed0: ldur            w1, [x0, #0x13]
    // 0xbc1ed4: DecompressPointer r1
    //     0xbc1ed4: add             x1, x1, HEAP, lsl #32
    // 0xbc1ed8: r0 = of()
    //     0xbc1ed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1edc: LoadField: r1 = r0->field_5b
    //     0xbc1edc: ldur            w1, [x0, #0x5b]
    // 0xbc1ee0: DecompressPointer r1
    //     0xbc1ee0: add             x1, x1, HEAP, lsl #32
    // 0xbc1ee4: mov             x6, x1
    // 0xbc1ee8: b               #0xbc1f20
    // 0xbc1eec: ldur            x2, [fp, #-0x18]
    // 0xbc1ef0: LoadField: r1 = r2->field_13
    //     0xbc1ef0: ldur            w1, [x2, #0x13]
    // 0xbc1ef4: DecompressPointer r1
    //     0xbc1ef4: add             x1, x1, HEAP, lsl #32
    // 0xbc1ef8: r0 = of()
    //     0xbc1ef8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1efc: LoadField: r1 = r0->field_5b
    //     0xbc1efc: ldur            w1, [x0, #0x5b]
    // 0xbc1f00: DecompressPointer r1
    //     0xbc1f00: add             x1, x1, HEAP, lsl #32
    // 0xbc1f04: r0 = LoadClassIdInstr(r1)
    //     0xbc1f04: ldur            x0, [x1, #-1]
    //     0xbc1f08: ubfx            x0, x0, #0xc, #0x14
    // 0xbc1f0c: d0 = 0.400000
    //     0xbc1f0c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc1f10: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbc1f10: sub             lr, x0, #0xffa
    //     0xbc1f14: ldr             lr, [x21, lr, lsl #3]
    //     0xbc1f18: blr             lr
    // 0xbc1f1c: mov             x6, x0
    // 0xbc1f20: ldur            x2, [fp, #-0x18]
    // 0xbc1f24: ldur            x5, [fp, #-0x20]
    // 0xbc1f28: ldur            x4, [fp, #-0x28]
    // 0xbc1f2c: ldur            x3, [fp, #-0x38]
    // 0xbc1f30: ldur            x1, [fp, #-0x10]
    // 0xbc1f34: ldur            x0, [fp, #-0x30]
    // 0xbc1f38: r16 = <Color>
    //     0xbc1f38: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbc1f3c: ldr             x16, [x16, #0xf80]
    // 0xbc1f40: stp             x6, x16, [SP]
    // 0xbc1f44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc1f44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc1f48: r0 = all()
    //     0xbc1f48: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbc1f4c: stur            x0, [fp, #-8]
    // 0xbc1f50: r16 = <RoundedRectangleBorder>
    //     0xbc1f50: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbc1f54: ldr             x16, [x16, #0xf78]
    // 0xbc1f58: r30 = Instance_RoundedRectangleBorder
    //     0xbc1f58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbc1f5c: ldr             lr, [lr, #0xd68]
    // 0xbc1f60: stp             lr, x16, [SP]
    // 0xbc1f64: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc1f64: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc1f68: r0 = all()
    //     0xbc1f68: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbc1f6c: stur            x0, [fp, #-0x40]
    // 0xbc1f70: r0 = ButtonStyle()
    //     0xbc1f70: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbc1f74: mov             x1, x0
    // 0xbc1f78: ldur            x0, [fp, #-8]
    // 0xbc1f7c: stur            x1, [fp, #-0x48]
    // 0xbc1f80: StoreField: r1->field_b = r0
    //     0xbc1f80: stur            w0, [x1, #0xb]
    // 0xbc1f84: ldur            x0, [fp, #-0x40]
    // 0xbc1f88: StoreField: r1->field_43 = r0
    //     0xbc1f88: stur            w0, [x1, #0x43]
    // 0xbc1f8c: r0 = TextButtonThemeData()
    //     0xbc1f8c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbc1f90: mov             x2, x0
    // 0xbc1f94: ldur            x0, [fp, #-0x48]
    // 0xbc1f98: stur            x2, [fp, #-8]
    // 0xbc1f9c: StoreField: r2->field_7 = r0
    //     0xbc1f9c: stur            w0, [x2, #7]
    // 0xbc1fa0: ldur            x0, [fp, #-0x18]
    // 0xbc1fa4: LoadField: r1 = r0->field_13
    //     0xbc1fa4: ldur            w1, [x0, #0x13]
    // 0xbc1fa8: DecompressPointer r1
    //     0xbc1fa8: add             x1, x1, HEAP, lsl #32
    // 0xbc1fac: r0 = of()
    //     0xbc1fac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc1fb0: LoadField: r1 = r0->field_87
    //     0xbc1fb0: ldur            w1, [x0, #0x87]
    // 0xbc1fb4: DecompressPointer r1
    //     0xbc1fb4: add             x1, x1, HEAP, lsl #32
    // 0xbc1fb8: LoadField: r0 = r1->field_7
    //     0xbc1fb8: ldur            w0, [x1, #7]
    // 0xbc1fbc: DecompressPointer r0
    //     0xbc1fbc: add             x0, x0, HEAP, lsl #32
    // 0xbc1fc0: r16 = 16.000000
    //     0xbc1fc0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbc1fc4: ldr             x16, [x16, #0x188]
    // 0xbc1fc8: r30 = Instance_Color
    //     0xbc1fc8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc1fcc: stp             lr, x16, [SP]
    // 0xbc1fd0: mov             x1, x0
    // 0xbc1fd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc1fd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc1fd8: ldr             x4, [x4, #0xaa0]
    // 0xbc1fdc: r0 = copyWith()
    //     0xbc1fdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc1fe0: stur            x0, [fp, #-0x40]
    // 0xbc1fe4: r0 = Text()
    //     0xbc1fe4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc1fe8: mov             x3, x0
    // 0xbc1fec: r0 = "CONFIRM"
    //     0xbc1fec: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e98] "CONFIRM"
    //     0xbc1ff0: ldr             x0, [x0, #0xe98]
    // 0xbc1ff4: stur            x3, [fp, #-0x48]
    // 0xbc1ff8: StoreField: r3->field_b = r0
    //     0xbc1ff8: stur            w0, [x3, #0xb]
    // 0xbc1ffc: ldur            x0, [fp, #-0x40]
    // 0xbc2000: StoreField: r3->field_13 = r0
    //     0xbc2000: stur            w0, [x3, #0x13]
    // 0xbc2004: ldur            x2, [fp, #-0x18]
    // 0xbc2008: r1 = Function '<anonymous closure>':.
    //     0xbc2008: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c320] AnonymousClosure: (0xbc21b4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc200c: ldr             x1, [x1, #0x320]
    // 0xbc2010: r0 = AllocateClosure()
    //     0xbc2010: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc2014: stur            x0, [fp, #-0x18]
    // 0xbc2018: r0 = TextButton()
    //     0xbc2018: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbc201c: mov             x1, x0
    // 0xbc2020: ldur            x0, [fp, #-0x18]
    // 0xbc2024: stur            x1, [fp, #-0x40]
    // 0xbc2028: StoreField: r1->field_b = r0
    //     0xbc2028: stur            w0, [x1, #0xb]
    // 0xbc202c: r0 = false
    //     0xbc202c: add             x0, NULL, #0x30  ; false
    // 0xbc2030: StoreField: r1->field_27 = r0
    //     0xbc2030: stur            w0, [x1, #0x27]
    // 0xbc2034: r0 = true
    //     0xbc2034: add             x0, NULL, #0x20  ; true
    // 0xbc2038: StoreField: r1->field_2f = r0
    //     0xbc2038: stur            w0, [x1, #0x2f]
    // 0xbc203c: ldur            x0, [fp, #-0x48]
    // 0xbc2040: StoreField: r1->field_37 = r0
    //     0xbc2040: stur            w0, [x1, #0x37]
    // 0xbc2044: r0 = TextButtonTheme()
    //     0xbc2044: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbc2048: mov             x1, x0
    // 0xbc204c: ldur            x0, [fp, #-8]
    // 0xbc2050: stur            x1, [fp, #-0x18]
    // 0xbc2054: StoreField: r1->field_f = r0
    //     0xbc2054: stur            w0, [x1, #0xf]
    // 0xbc2058: ldur            x0, [fp, #-0x40]
    // 0xbc205c: StoreField: r1->field_b = r0
    //     0xbc205c: stur            w0, [x1, #0xb]
    // 0xbc2060: r0 = SizedBox()
    //     0xbc2060: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbc2064: mov             x1, x0
    // 0xbc2068: r0 = inf
    //     0xbc2068: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbc206c: ldr             x0, [x0, #0x9f8]
    // 0xbc2070: stur            x1, [fp, #-8]
    // 0xbc2074: StoreField: r1->field_f = r0
    //     0xbc2074: stur            w0, [x1, #0xf]
    // 0xbc2078: r0 = 44.000000
    //     0xbc2078: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbc207c: ldr             x0, [x0, #0xad8]
    // 0xbc2080: StoreField: r1->field_13 = r0
    //     0xbc2080: stur            w0, [x1, #0x13]
    // 0xbc2084: ldur            x0, [fp, #-0x18]
    // 0xbc2088: StoreField: r1->field_b = r0
    //     0xbc2088: stur            w0, [x1, #0xb]
    // 0xbc208c: r0 = Padding()
    //     0xbc208c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc2090: mov             x3, x0
    // 0xbc2094: r0 = Instance_EdgeInsets
    //     0xbc2094: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbc2098: ldr             x0, [x0, #0x668]
    // 0xbc209c: stur            x3, [fp, #-0x18]
    // 0xbc20a0: StoreField: r3->field_f = r0
    //     0xbc20a0: stur            w0, [x3, #0xf]
    // 0xbc20a4: ldur            x0, [fp, #-8]
    // 0xbc20a8: StoreField: r3->field_b = r0
    //     0xbc20a8: stur            w0, [x3, #0xb]
    // 0xbc20ac: r1 = Null
    //     0xbc20ac: mov             x1, NULL
    // 0xbc20b0: r2 = 12
    //     0xbc20b0: movz            x2, #0xc
    // 0xbc20b4: r0 = AllocateArray()
    //     0xbc20b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc20b8: mov             x2, x0
    // 0xbc20bc: ldur            x0, [fp, #-0x20]
    // 0xbc20c0: stur            x2, [fp, #-8]
    // 0xbc20c4: StoreField: r2->field_f = r0
    //     0xbc20c4: stur            w0, [x2, #0xf]
    // 0xbc20c8: ldur            x0, [fp, #-0x28]
    // 0xbc20cc: StoreField: r2->field_13 = r0
    //     0xbc20cc: stur            w0, [x2, #0x13]
    // 0xbc20d0: ldur            x0, [fp, #-0x38]
    // 0xbc20d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc20d4: stur            w0, [x2, #0x17]
    // 0xbc20d8: ldur            x0, [fp, #-0x10]
    // 0xbc20dc: StoreField: r2->field_1b = r0
    //     0xbc20dc: stur            w0, [x2, #0x1b]
    // 0xbc20e0: ldur            x0, [fp, #-0x30]
    // 0xbc20e4: StoreField: r2->field_1f = r0
    //     0xbc20e4: stur            w0, [x2, #0x1f]
    // 0xbc20e8: ldur            x0, [fp, #-0x18]
    // 0xbc20ec: StoreField: r2->field_23 = r0
    //     0xbc20ec: stur            w0, [x2, #0x23]
    // 0xbc20f0: r1 = <Widget>
    //     0xbc20f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc20f4: r0 = AllocateGrowableArray()
    //     0xbc20f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc20f8: mov             x1, x0
    // 0xbc20fc: ldur            x0, [fp, #-8]
    // 0xbc2100: stur            x1, [fp, #-0x10]
    // 0xbc2104: StoreField: r1->field_f = r0
    //     0xbc2104: stur            w0, [x1, #0xf]
    // 0xbc2108: r0 = 12
    //     0xbc2108: movz            x0, #0xc
    // 0xbc210c: StoreField: r1->field_b = r0
    //     0xbc210c: stur            w0, [x1, #0xb]
    // 0xbc2110: r0 = Wrap()
    //     0xbc2110: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xbc2114: mov             x1, x0
    // 0xbc2118: r0 = Instance_Axis
    //     0xbc2118: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc211c: stur            x1, [fp, #-8]
    // 0xbc2120: StoreField: r1->field_f = r0
    //     0xbc2120: stur            w0, [x1, #0xf]
    // 0xbc2124: r0 = Instance_WrapAlignment
    //     0xbc2124: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xbc2128: ldr             x0, [x0, #0x6e8]
    // 0xbc212c: StoreField: r1->field_13 = r0
    //     0xbc212c: stur            w0, [x1, #0x13]
    // 0xbc2130: ArrayStore: r1[0] = rZR  ; List_8
    //     0xbc2130: stur            xzr, [x1, #0x17]
    // 0xbc2134: StoreField: r1->field_1f = r0
    //     0xbc2134: stur            w0, [x1, #0x1f]
    // 0xbc2138: StoreField: r1->field_23 = rZR
    //     0xbc2138: stur            xzr, [x1, #0x23]
    // 0xbc213c: r0 = Instance_WrapCrossAlignment
    //     0xbc213c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xbc2140: ldr             x0, [x0, #0x6f0]
    // 0xbc2144: StoreField: r1->field_2b = r0
    //     0xbc2144: stur            w0, [x1, #0x2b]
    // 0xbc2148: r0 = Instance_VerticalDirection
    //     0xbc2148: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc214c: ldr             x0, [x0, #0xa20]
    // 0xbc2150: StoreField: r1->field_33 = r0
    //     0xbc2150: stur            w0, [x1, #0x33]
    // 0xbc2154: r0 = Instance_Clip
    //     0xbc2154: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc2158: ldr             x0, [x0, #0x38]
    // 0xbc215c: StoreField: r1->field_37 = r0
    //     0xbc215c: stur            w0, [x1, #0x37]
    // 0xbc2160: ldur            x0, [fp, #-0x10]
    // 0xbc2164: StoreField: r1->field_b = r0
    //     0xbc2164: stur            w0, [x1, #0xb]
    // 0xbc2168: r0 = Padding()
    //     0xbc2168: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc216c: r1 = Instance_EdgeInsets
    //     0xbc216c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f340] Obj!EdgeInsets@d58161
    //     0xbc2170: ldr             x1, [x1, #0x340]
    // 0xbc2174: StoreField: r0->field_f = r1
    //     0xbc2174: stur            w1, [x0, #0xf]
    // 0xbc2178: ldur            x1, [fp, #-8]
    // 0xbc217c: StoreField: r0->field_b = r1
    //     0xbc217c: stur            w1, [x0, #0xb]
    // 0xbc2180: LeaveFrame
    //     0xbc2180: mov             SP, fp
    //     0xbc2184: ldp             fp, lr, [SP], #0x10
    // 0xbc2188: ret
    //     0xbc2188: ret             
    // 0xbc218c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc218c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2190: b               #0xbc1698
    // 0xbc2194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2194: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc2198: SaveReg d0
    //     0xbc2198: str             q0, [SP, #-0x10]!
    // 0xbc219c: stp             x0, x1, [SP, #-0x10]!
    // 0xbc21a0: r0 = AllocateDouble()
    //     0xbc21a0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbc21a4: mov             x2, x0
    // 0xbc21a8: ldp             x0, x1, [SP], #0x10
    // 0xbc21ac: RestoreReg d0
    //     0xbc21ac: ldr             q0, [SP], #0x10
    // 0xbc21b0: b               #0xbc1dac
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc21b4, size: 0x90
    // 0xbc21b4: EnterFrame
    //     0xbc21b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbc21b8: mov             fp, SP
    // 0xbc21bc: AllocStack(0x10)
    //     0xbc21bc: sub             SP, SP, #0x10
    // 0xbc21c0: SetupParameters()
    //     0xbc21c0: ldr             x0, [fp, #0x10]
    //     0xbc21c4: ldur            w1, [x0, #0x17]
    //     0xbc21c8: add             x1, x1, HEAP, lsl #32
    // 0xbc21cc: CheckStackOverflow
    //     0xbc21cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc21d0: cmp             SP, x16
    //     0xbc21d4: b.ls            #0xbc2238
    // 0xbc21d8: LoadField: r0 = r1->field_f
    //     0xbc21d8: ldur            w0, [x1, #0xf]
    // 0xbc21dc: DecompressPointer r0
    //     0xbc21dc: add             x0, x0, HEAP, lsl #32
    // 0xbc21e0: LoadField: r1 = r0->field_1b
    //     0xbc21e0: ldur            w1, [x0, #0x1b]
    // 0xbc21e4: DecompressPointer r1
    //     0xbc21e4: add             x1, x1, HEAP, lsl #32
    // 0xbc21e8: tbnz            w1, #4, #0xbc2228
    // 0xbc21ec: LoadField: r1 = r0->field_b
    //     0xbc21ec: ldur            w1, [x0, #0xb]
    // 0xbc21f0: DecompressPointer r1
    //     0xbc21f0: add             x1, x1, HEAP, lsl #32
    // 0xbc21f4: cmp             w1, NULL
    // 0xbc21f8: b.eq            #0xbc2240
    // 0xbc21fc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbc21fc: ldur            w2, [x0, #0x17]
    // 0xbc2200: DecompressPointer r2
    //     0xbc2200: add             x2, x2, HEAP, lsl #32
    // 0xbc2204: LoadField: r0 = r1->field_b
    //     0xbc2204: ldur            w0, [x1, #0xb]
    // 0xbc2208: DecompressPointer r0
    //     0xbc2208: add             x0, x0, HEAP, lsl #32
    // 0xbc220c: stp             x2, x0, [SP]
    // 0xbc2210: r4 = 0
    //     0xbc2210: movz            x4, #0
    // 0xbc2214: ldr             x0, [SP, #8]
    // 0xbc2218: r16 = UnlinkedCall_0x613b5c
    //     0xbc2218: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c328] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbc221c: add             x16, x16, #0x328
    // 0xbc2220: ldp             x5, lr, [x16]
    // 0xbc2224: blr             lr
    // 0xbc2228: r0 = Null
    //     0xbc2228: mov             x0, NULL
    // 0xbc222c: LeaveFrame
    //     0xbc222c: mov             SP, fp
    //     0xbc2230: ldp             fp, lr, [SP], #0x10
    // 0xbc2234: ret
    //     0xbc2234: ret             
    // 0xbc2238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc223c: b               #0xbc21d8
    // 0xbc2240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int>) {
    // ** addr: 0xbc2244, size: 0x650
    // 0xbc2244: EnterFrame
    //     0xbc2244: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2248: mov             fp, SP
    // 0xbc224c: AllocStack(0x48)
    //     0xbc224c: sub             SP, SP, #0x48
    // 0xbc2250: SetupParameters()
    //     0xbc2250: ldr             x0, [fp, #0x20]
    //     0xbc2254: ldur            w2, [x0, #0x17]
    //     0xbc2258: add             x2, x2, HEAP, lsl #32
    //     0xbc225c: stur            x2, [fp, #-0x38]
    // 0xbc2260: CheckStackOverflow
    //     0xbc2260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2264: cmp             SP, x16
    //     0xbc2268: b.ls            #0xbc288c
    // 0xbc226c: ldr             x0, [fp, #0x10]
    // 0xbc2270: LoadField: r1 = r0->field_f
    //     0xbc2270: ldur            w1, [x0, #0xf]
    // 0xbc2274: DecompressPointer r1
    //     0xbc2274: add             x1, x1, HEAP, lsl #32
    // 0xbc2278: stur            x1, [fp, #-0x30]
    // 0xbc227c: cmp             w1, NULL
    // 0xbc2280: b.ne            #0xbc24c8
    // 0xbc2284: ldr             x1, [fp, #0x18]
    // 0xbc2288: r0 = of()
    //     0xbc2288: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc228c: LoadField: r1 = r0->field_87
    //     0xbc228c: ldur            w1, [x0, #0x87]
    // 0xbc2290: DecompressPointer r1
    //     0xbc2290: add             x1, x1, HEAP, lsl #32
    // 0xbc2294: LoadField: r0 = r1->field_2b
    //     0xbc2294: ldur            w0, [x1, #0x2b]
    // 0xbc2298: DecompressPointer r0
    //     0xbc2298: add             x0, x0, HEAP, lsl #32
    // 0xbc229c: stur            x0, [fp, #-8]
    // 0xbc22a0: r1 = Instance_Color
    //     0xbc22a0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc22a4: d0 = 0.700000
    //     0xbc22a4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc22a8: ldr             d0, [x17, #0xf48]
    // 0xbc22ac: r0 = withOpacity()
    //     0xbc22ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc22b0: r16 = 12.000000
    //     0xbc22b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc22b4: ldr             x16, [x16, #0x9e8]
    // 0xbc22b8: stp             x16, x0, [SP]
    // 0xbc22bc: ldur            x1, [fp, #-8]
    // 0xbc22c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc22c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc22c4: ldr             x4, [x4, #0x9b8]
    // 0xbc22c8: r0 = copyWith()
    //     0xbc22c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc22cc: stur            x0, [fp, #-8]
    // 0xbc22d0: r0 = Text()
    //     0xbc22d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc22d4: r3 = "Didn\'t receive OTP\? "
    //     0xbc22d4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbc22d8: ldr             x3, [x3, #0xf38]
    // 0xbc22dc: stur            x0, [fp, #-0x10]
    // 0xbc22e0: StoreField: r0->field_b = r3
    //     0xbc22e0: stur            w3, [x0, #0xb]
    // 0xbc22e4: ldur            x1, [fp, #-8]
    // 0xbc22e8: StoreField: r0->field_13 = r1
    //     0xbc22e8: stur            w1, [x0, #0x13]
    // 0xbc22ec: ldr             x1, [fp, #0x18]
    // 0xbc22f0: r0 = of()
    //     0xbc22f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc22f4: LoadField: r1 = r0->field_87
    //     0xbc22f4: ldur            w1, [x0, #0x87]
    // 0xbc22f8: DecompressPointer r1
    //     0xbc22f8: add             x1, x1, HEAP, lsl #32
    // 0xbc22fc: LoadField: r0 = r1->field_2b
    //     0xbc22fc: ldur            w0, [x1, #0x2b]
    // 0xbc2300: DecompressPointer r0
    //     0xbc2300: add             x0, x0, HEAP, lsl #32
    // 0xbc2304: r16 = Instance_Color
    //     0xbc2304: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbc2308: ldr             x16, [x16, #0x858]
    // 0xbc230c: r30 = 12.000000
    //     0xbc230c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc2310: ldr             lr, [lr, #0x9e8]
    // 0xbc2314: stp             lr, x16, [SP]
    // 0xbc2318: mov             x1, x0
    // 0xbc231c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc231c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2320: ldr             x4, [x4, #0x9b8]
    // 0xbc2324: r0 = copyWith()
    //     0xbc2324: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2328: stur            x0, [fp, #-8]
    // 0xbc232c: r0 = Text()
    //     0xbc232c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2330: r4 = "Resend OTP "
    //     0xbc2330: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbc2334: ldr             x4, [x4, #0xf40]
    // 0xbc2338: stur            x0, [fp, #-0x18]
    // 0xbc233c: StoreField: r0->field_b = r4
    //     0xbc233c: stur            w4, [x0, #0xb]
    // 0xbc2340: ldur            x1, [fp, #-8]
    // 0xbc2344: StoreField: r0->field_13 = r1
    //     0xbc2344: stur            w1, [x0, #0x13]
    // 0xbc2348: ldr             x1, [fp, #0x18]
    // 0xbc234c: r0 = of()
    //     0xbc234c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2350: LoadField: r1 = r0->field_87
    //     0xbc2350: ldur            w1, [x0, #0x87]
    // 0xbc2354: DecompressPointer r1
    //     0xbc2354: add             x1, x1, HEAP, lsl #32
    // 0xbc2358: LoadField: r0 = r1->field_2b
    //     0xbc2358: ldur            w0, [x1, #0x2b]
    // 0xbc235c: DecompressPointer r0
    //     0xbc235c: add             x0, x0, HEAP, lsl #32
    // 0xbc2360: stur            x0, [fp, #-8]
    // 0xbc2364: r1 = Instance_Color
    //     0xbc2364: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc2368: d0 = 0.700000
    //     0xbc2368: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc236c: ldr             d0, [x17, #0xf48]
    // 0xbc2370: r0 = withOpacity()
    //     0xbc2370: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc2374: r16 = 12.000000
    //     0xbc2374: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc2378: ldr             x16, [x16, #0x9e8]
    // 0xbc237c: stp             x16, x0, [SP]
    // 0xbc2380: ldur            x1, [fp, #-8]
    // 0xbc2384: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc2384: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2388: ldr             x4, [x4, #0x9b8]
    // 0xbc238c: r0 = copyWith()
    //     0xbc238c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2390: stur            x0, [fp, #-8]
    // 0xbc2394: r0 = Text()
    //     0xbc2394: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2398: mov             x2, x0
    // 0xbc239c: r0 = "in "
    //     0xbc239c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xbc23a0: ldr             x0, [x0, #0xf48]
    // 0xbc23a4: stur            x2, [fp, #-0x20]
    // 0xbc23a8: StoreField: r2->field_b = r0
    //     0xbc23a8: stur            w0, [x2, #0xb]
    // 0xbc23ac: ldur            x0, [fp, #-8]
    // 0xbc23b0: StoreField: r2->field_13 = r0
    //     0xbc23b0: stur            w0, [x2, #0x13]
    // 0xbc23b4: ldr             x1, [fp, #0x18]
    // 0xbc23b8: r0 = of()
    //     0xbc23b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc23bc: LoadField: r1 = r0->field_87
    //     0xbc23bc: ldur            w1, [x0, #0x87]
    // 0xbc23c0: DecompressPointer r1
    //     0xbc23c0: add             x1, x1, HEAP, lsl #32
    // 0xbc23c4: LoadField: r0 = r1->field_2b
    //     0xbc23c4: ldur            w0, [x1, #0x2b]
    // 0xbc23c8: DecompressPointer r0
    //     0xbc23c8: add             x0, x0, HEAP, lsl #32
    // 0xbc23cc: r16 = Instance_Color
    //     0xbc23cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbc23d0: ldr             x16, [x16, #0x50]
    // 0xbc23d4: r30 = 12.000000
    //     0xbc23d4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc23d8: ldr             lr, [lr, #0x9e8]
    // 0xbc23dc: stp             lr, x16, [SP]
    // 0xbc23e0: mov             x1, x0
    // 0xbc23e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc23e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc23e8: ldr             x4, [x4, #0x9b8]
    // 0xbc23ec: r0 = copyWith()
    //     0xbc23ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc23f0: stur            x0, [fp, #-8]
    // 0xbc23f4: r0 = Text()
    //     0xbc23f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc23f8: mov             x3, x0
    // 0xbc23fc: r0 = "30s"
    //     0xbc23fc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f50] "30s"
    //     0xbc2400: ldr             x0, [x0, #0xf50]
    // 0xbc2404: stur            x3, [fp, #-0x28]
    // 0xbc2408: StoreField: r3->field_b = r0
    //     0xbc2408: stur            w0, [x3, #0xb]
    // 0xbc240c: ldur            x0, [fp, #-8]
    // 0xbc2410: StoreField: r3->field_13 = r0
    //     0xbc2410: stur            w0, [x3, #0x13]
    // 0xbc2414: r1 = Null
    //     0xbc2414: mov             x1, NULL
    // 0xbc2418: r2 = 8
    //     0xbc2418: movz            x2, #0x8
    // 0xbc241c: r0 = AllocateArray()
    //     0xbc241c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc2420: mov             x2, x0
    // 0xbc2424: ldur            x0, [fp, #-0x10]
    // 0xbc2428: stur            x2, [fp, #-8]
    // 0xbc242c: StoreField: r2->field_f = r0
    //     0xbc242c: stur            w0, [x2, #0xf]
    // 0xbc2430: ldur            x0, [fp, #-0x18]
    // 0xbc2434: StoreField: r2->field_13 = r0
    //     0xbc2434: stur            w0, [x2, #0x13]
    // 0xbc2438: ldur            x0, [fp, #-0x20]
    // 0xbc243c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc243c: stur            w0, [x2, #0x17]
    // 0xbc2440: ldur            x0, [fp, #-0x28]
    // 0xbc2444: StoreField: r2->field_1b = r0
    //     0xbc2444: stur            w0, [x2, #0x1b]
    // 0xbc2448: r1 = <Widget>
    //     0xbc2448: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc244c: r0 = AllocateGrowableArray()
    //     0xbc244c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc2450: mov             x1, x0
    // 0xbc2454: ldur            x0, [fp, #-8]
    // 0xbc2458: stur            x1, [fp, #-0x10]
    // 0xbc245c: StoreField: r1->field_f = r0
    //     0xbc245c: stur            w0, [x1, #0xf]
    // 0xbc2460: r5 = 8
    //     0xbc2460: movz            x5, #0x8
    // 0xbc2464: StoreField: r1->field_b = r5
    //     0xbc2464: stur            w5, [x1, #0xb]
    // 0xbc2468: r0 = Row()
    //     0xbc2468: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc246c: r6 = Instance_Axis
    //     0xbc246c: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc2470: StoreField: r0->field_f = r6
    //     0xbc2470: stur            w6, [x0, #0xf]
    // 0xbc2474: r7 = Instance_MainAxisAlignment
    //     0xbc2474: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc2478: ldr             x7, [x7, #0xa08]
    // 0xbc247c: StoreField: r0->field_13 = r7
    //     0xbc247c: stur            w7, [x0, #0x13]
    // 0xbc2480: r8 = Instance_MainAxisSize
    //     0xbc2480: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc2484: ldr             x8, [x8, #0xa10]
    // 0xbc2488: ArrayStore: r0[0] = r8  ; List_4
    //     0xbc2488: stur            w8, [x0, #0x17]
    // 0xbc248c: r9 = Instance_CrossAxisAlignment
    //     0xbc248c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc2490: ldr             x9, [x9, #0xa18]
    // 0xbc2494: StoreField: r0->field_1b = r9
    //     0xbc2494: stur            w9, [x0, #0x1b]
    // 0xbc2498: r10 = Instance_VerticalDirection
    //     0xbc2498: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc249c: ldr             x10, [x10, #0xa20]
    // 0xbc24a0: StoreField: r0->field_23 = r10
    //     0xbc24a0: stur            w10, [x0, #0x23]
    // 0xbc24a4: r11 = Instance_Clip
    //     0xbc24a4: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc24a8: ldr             x11, [x11, #0x38]
    // 0xbc24ac: StoreField: r0->field_2b = r11
    //     0xbc24ac: stur            w11, [x0, #0x2b]
    // 0xbc24b0: StoreField: r0->field_2f = rZR
    //     0xbc24b0: stur            xzr, [x0, #0x2f]
    // 0xbc24b4: ldur            x1, [fp, #-0x10]
    // 0xbc24b8: StoreField: r0->field_b = r1
    //     0xbc24b8: stur            w1, [x0, #0xb]
    // 0xbc24bc: LeaveFrame
    //     0xbc24bc: mov             SP, fp
    //     0xbc24c0: ldp             fp, lr, [SP], #0x10
    // 0xbc24c4: ret
    //     0xbc24c4: ret             
    // 0xbc24c8: r3 = "Didn\'t receive OTP\? "
    //     0xbc24c8: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbc24cc: ldr             x3, [x3, #0xf38]
    // 0xbc24d0: r4 = "Resend OTP "
    //     0xbc24d0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbc24d4: ldr             x4, [x4, #0xf40]
    // 0xbc24d8: r5 = 8
    //     0xbc24d8: movz            x5, #0x8
    // 0xbc24dc: r9 = Instance_CrossAxisAlignment
    //     0xbc24dc: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc24e0: ldr             x9, [x9, #0xa18]
    // 0xbc24e4: r7 = Instance_MainAxisAlignment
    //     0xbc24e4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc24e8: ldr             x7, [x7, #0xa08]
    // 0xbc24ec: r8 = Instance_MainAxisSize
    //     0xbc24ec: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc24f0: ldr             x8, [x8, #0xa10]
    // 0xbc24f4: r6 = Instance_Axis
    //     0xbc24f4: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc24f8: r10 = Instance_VerticalDirection
    //     0xbc24f8: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc24fc: ldr             x10, [x10, #0xa20]
    // 0xbc2500: r11 = Instance_Clip
    //     0xbc2500: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc2504: ldr             x11, [x11, #0x38]
    // 0xbc2508: r0 = 60
    //     0xbc2508: movz            x0, #0x3c
    // 0xbc250c: branchIfSmi(r1, 0xbc2518)
    //     0xbc250c: tbz             w1, #0, #0xbc2518
    // 0xbc2510: r0 = LoadClassIdInstr(r1)
    //     0xbc2510: ldur            x0, [x1, #-1]
    //     0xbc2514: ubfx            x0, x0, #0xc, #0x14
    // 0xbc2518: stp             xzr, x1, [SP]
    // 0xbc251c: r0 = GDT[cid_x0 + -0xff5]()
    //     0xbc251c: sub             lr, x0, #0xff5
    //     0xbc2520: ldr             lr, [x21, lr, lsl #3]
    //     0xbc2524: blr             lr
    // 0xbc2528: stur            x0, [fp, #-8]
    // 0xbc252c: tbnz            w0, #4, #0xbc2548
    // 0xbc2530: ldur            x2, [fp, #-0x38]
    // 0xbc2534: r3 = true
    //     0xbc2534: add             x3, NULL, #0x20  ; true
    // 0xbc2538: LoadField: r1 = r2->field_f
    //     0xbc2538: ldur            w1, [x2, #0xf]
    // 0xbc253c: DecompressPointer r1
    //     0xbc253c: add             x1, x1, HEAP, lsl #32
    // 0xbc2540: StoreField: r1->field_13 = r3
    //     0xbc2540: stur            w3, [x1, #0x13]
    // 0xbc2544: b               #0xbc2550
    // 0xbc2548: ldur            x2, [fp, #-0x38]
    // 0xbc254c: r3 = true
    //     0xbc254c: add             x3, NULL, #0x20  ; true
    // 0xbc2550: ldr             x1, [fp, #0x18]
    // 0xbc2554: r0 = of()
    //     0xbc2554: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2558: LoadField: r1 = r0->field_87
    //     0xbc2558: ldur            w1, [x0, #0x87]
    // 0xbc255c: DecompressPointer r1
    //     0xbc255c: add             x1, x1, HEAP, lsl #32
    // 0xbc2560: LoadField: r0 = r1->field_2b
    //     0xbc2560: ldur            w0, [x1, #0x2b]
    // 0xbc2564: DecompressPointer r0
    //     0xbc2564: add             x0, x0, HEAP, lsl #32
    // 0xbc2568: stur            x0, [fp, #-0x10]
    // 0xbc256c: r1 = Instance_Color
    //     0xbc256c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc2570: d0 = 0.700000
    //     0xbc2570: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc2574: ldr             d0, [x17, #0xf48]
    // 0xbc2578: r0 = withOpacity()
    //     0xbc2578: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc257c: r16 = 12.000000
    //     0xbc257c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc2580: ldr             x16, [x16, #0x9e8]
    // 0xbc2584: stp             x16, x0, [SP]
    // 0xbc2588: ldur            x1, [fp, #-0x10]
    // 0xbc258c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc258c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2590: ldr             x4, [x4, #0x9b8]
    // 0xbc2594: r0 = copyWith()
    //     0xbc2594: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2598: stur            x0, [fp, #-0x10]
    // 0xbc259c: r0 = Text()
    //     0xbc259c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc25a0: mov             x3, x0
    // 0xbc25a4: r0 = "Didn\'t receive OTP\? "
    //     0xbc25a4: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xbc25a8: ldr             x0, [x0, #0xf38]
    // 0xbc25ac: stur            x3, [fp, #-0x18]
    // 0xbc25b0: StoreField: r3->field_b = r0
    //     0xbc25b0: stur            w0, [x3, #0xb]
    // 0xbc25b4: ldur            x0, [fp, #-0x10]
    // 0xbc25b8: StoreField: r3->field_13 = r0
    //     0xbc25b8: stur            w0, [x3, #0x13]
    // 0xbc25bc: ldur            x2, [fp, #-0x38]
    // 0xbc25c0: LoadField: r0 = r2->field_f
    //     0xbc25c0: ldur            w0, [x2, #0xf]
    // 0xbc25c4: DecompressPointer r0
    //     0xbc25c4: add             x0, x0, HEAP, lsl #32
    // 0xbc25c8: LoadField: r1 = r0->field_13
    //     0xbc25c8: ldur            w1, [x0, #0x13]
    // 0xbc25cc: DecompressPointer r1
    //     0xbc25cc: add             x1, x1, HEAP, lsl #32
    // 0xbc25d0: tbnz            w1, #4, #0xbc25f0
    // 0xbc25d4: ldur            x0, [fp, #-8]
    // 0xbc25d8: tbnz            w0, #4, #0xbc25f0
    // 0xbc25dc: r1 = Function '<anonymous closure>':.
    //     0xbc25dc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c338] AnonymousClosure: (0xbc2894), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::build (0xbc1670)
    //     0xbc25e0: ldr             x1, [x1, #0x338]
    // 0xbc25e4: r0 = AllocateClosure()
    //     0xbc25e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc25e8: mov             x2, x0
    // 0xbc25ec: b               #0xbc25f4
    // 0xbc25f0: r2 = Null
    //     0xbc25f0: mov             x2, NULL
    // 0xbc25f4: ldur            x0, [fp, #-8]
    // 0xbc25f8: ldr             x1, [fp, #0x18]
    // 0xbc25fc: stur            x2, [fp, #-0x10]
    // 0xbc2600: r0 = of()
    //     0xbc2600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2604: LoadField: r1 = r0->field_87
    //     0xbc2604: ldur            w1, [x0, #0x87]
    // 0xbc2608: DecompressPointer r1
    //     0xbc2608: add             x1, x1, HEAP, lsl #32
    // 0xbc260c: LoadField: r0 = r1->field_2b
    //     0xbc260c: ldur            w0, [x1, #0x2b]
    // 0xbc2610: DecompressPointer r0
    //     0xbc2610: add             x0, x0, HEAP, lsl #32
    // 0xbc2614: r16 = Instance_Color
    //     0xbc2614: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbc2618: ldr             x16, [x16, #0x858]
    // 0xbc261c: r30 = 12.000000
    //     0xbc261c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc2620: ldr             lr, [lr, #0x9e8]
    // 0xbc2624: stp             lr, x16, [SP]
    // 0xbc2628: mov             x1, x0
    // 0xbc262c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc262c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2630: ldr             x4, [x4, #0x9b8]
    // 0xbc2634: r0 = copyWith()
    //     0xbc2634: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2638: stur            x0, [fp, #-0x20]
    // 0xbc263c: r0 = Text()
    //     0xbc263c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2640: mov             x1, x0
    // 0xbc2644: r0 = "Resend OTP "
    //     0xbc2644: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xbc2648: ldr             x0, [x0, #0xf40]
    // 0xbc264c: stur            x1, [fp, #-0x28]
    // 0xbc2650: StoreField: r1->field_b = r0
    //     0xbc2650: stur            w0, [x1, #0xb]
    // 0xbc2654: ldur            x0, [fp, #-0x20]
    // 0xbc2658: StoreField: r1->field_13 = r0
    //     0xbc2658: stur            w0, [x1, #0x13]
    // 0xbc265c: r0 = InkWell()
    //     0xbc265c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbc2660: mov             x2, x0
    // 0xbc2664: ldur            x0, [fp, #-0x28]
    // 0xbc2668: stur            x2, [fp, #-0x20]
    // 0xbc266c: StoreField: r2->field_b = r0
    //     0xbc266c: stur            w0, [x2, #0xb]
    // 0xbc2670: ldur            x0, [fp, #-0x10]
    // 0xbc2674: StoreField: r2->field_f = r0
    //     0xbc2674: stur            w0, [x2, #0xf]
    // 0xbc2678: r0 = true
    //     0xbc2678: add             x0, NULL, #0x20  ; true
    // 0xbc267c: StoreField: r2->field_43 = r0
    //     0xbc267c: stur            w0, [x2, #0x43]
    // 0xbc2680: r1 = Instance_BoxShape
    //     0xbc2680: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbc2684: ldr             x1, [x1, #0x80]
    // 0xbc2688: StoreField: r2->field_47 = r1
    //     0xbc2688: stur            w1, [x2, #0x47]
    // 0xbc268c: StoreField: r2->field_6f = r0
    //     0xbc268c: stur            w0, [x2, #0x6f]
    // 0xbc2690: r1 = false
    //     0xbc2690: add             x1, NULL, #0x30  ; false
    // 0xbc2694: StoreField: r2->field_73 = r1
    //     0xbc2694: stur            w1, [x2, #0x73]
    // 0xbc2698: StoreField: r2->field_83 = r0
    //     0xbc2698: stur            w0, [x2, #0x83]
    // 0xbc269c: StoreField: r2->field_7b = r1
    //     0xbc269c: stur            w1, [x2, #0x7b]
    // 0xbc26a0: ldur            x0, [fp, #-8]
    // 0xbc26a4: tbnz            w0, #4, #0xbc26b0
    // 0xbc26a8: r3 = ""
    //     0xbc26a8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc26ac: b               #0xbc26b8
    // 0xbc26b0: r3 = "in "
    //     0xbc26b0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xbc26b4: ldr             x3, [x3, #0xf48]
    // 0xbc26b8: ldr             x1, [fp, #0x18]
    // 0xbc26bc: stur            x3, [fp, #-0x10]
    // 0xbc26c0: r0 = of()
    //     0xbc26c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc26c4: LoadField: r1 = r0->field_87
    //     0xbc26c4: ldur            w1, [x0, #0x87]
    // 0xbc26c8: DecompressPointer r1
    //     0xbc26c8: add             x1, x1, HEAP, lsl #32
    // 0xbc26cc: LoadField: r0 = r1->field_2b
    //     0xbc26cc: ldur            w0, [x1, #0x2b]
    // 0xbc26d0: DecompressPointer r0
    //     0xbc26d0: add             x0, x0, HEAP, lsl #32
    // 0xbc26d4: stur            x0, [fp, #-0x28]
    // 0xbc26d8: r1 = Instance_Color
    //     0xbc26d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc26dc: d0 = 0.700000
    //     0xbc26dc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbc26e0: ldr             d0, [x17, #0xf48]
    // 0xbc26e4: r0 = withOpacity()
    //     0xbc26e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc26e8: r16 = 12.000000
    //     0xbc26e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc26ec: ldr             x16, [x16, #0x9e8]
    // 0xbc26f0: stp             x16, x0, [SP]
    // 0xbc26f4: ldur            x1, [fp, #-0x28]
    // 0xbc26f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc26f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc26fc: ldr             x4, [x4, #0x9b8]
    // 0xbc2700: r0 = copyWith()
    //     0xbc2700: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2704: stur            x0, [fp, #-0x28]
    // 0xbc2708: r0 = Text()
    //     0xbc2708: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc270c: mov             x3, x0
    // 0xbc2710: ldur            x0, [fp, #-0x10]
    // 0xbc2714: stur            x3, [fp, #-0x38]
    // 0xbc2718: StoreField: r3->field_b = r0
    //     0xbc2718: stur            w0, [x3, #0xb]
    // 0xbc271c: ldur            x0, [fp, #-0x28]
    // 0xbc2720: StoreField: r3->field_13 = r0
    //     0xbc2720: stur            w0, [x3, #0x13]
    // 0xbc2724: ldur            x0, [fp, #-8]
    // 0xbc2728: tbnz            w0, #4, #0xbc2738
    // 0xbc272c: mov             x0, x3
    // 0xbc2730: r4 = ""
    //     0xbc2730: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc2734: b               #0xbc2770
    // 0xbc2738: ldur            x0, [fp, #-0x30]
    // 0xbc273c: r1 = Null
    //     0xbc273c: mov             x1, NULL
    // 0xbc2740: r2 = 4
    //     0xbc2740: movz            x2, #0x4
    // 0xbc2744: r0 = AllocateArray()
    //     0xbc2744: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc2748: mov             x1, x0
    // 0xbc274c: ldur            x0, [fp, #-0x30]
    // 0xbc2750: StoreField: r1->field_f = r0
    //     0xbc2750: stur            w0, [x1, #0xf]
    // 0xbc2754: r16 = "s"
    //     0xbc2754: add             x16, PP, #0xc, lsl #12  ; [pp+0xc728] "s"
    //     0xbc2758: ldr             x16, [x16, #0x728]
    // 0xbc275c: StoreField: r1->field_13 = r16
    //     0xbc275c: stur            w16, [x1, #0x13]
    // 0xbc2760: str             x1, [SP]
    // 0xbc2764: r0 = _interpolate()
    //     0xbc2764: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc2768: mov             x4, x0
    // 0xbc276c: ldur            x0, [fp, #-0x38]
    // 0xbc2770: ldur            x3, [fp, #-0x18]
    // 0xbc2774: ldur            x2, [fp, #-0x20]
    // 0xbc2778: ldr             x1, [fp, #0x18]
    // 0xbc277c: stur            x4, [fp, #-8]
    // 0xbc2780: r0 = of()
    //     0xbc2780: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2784: LoadField: r1 = r0->field_87
    //     0xbc2784: ldur            w1, [x0, #0x87]
    // 0xbc2788: DecompressPointer r1
    //     0xbc2788: add             x1, x1, HEAP, lsl #32
    // 0xbc278c: LoadField: r0 = r1->field_2b
    //     0xbc278c: ldur            w0, [x1, #0x2b]
    // 0xbc2790: DecompressPointer r0
    //     0xbc2790: add             x0, x0, HEAP, lsl #32
    // 0xbc2794: r16 = Instance_Color
    //     0xbc2794: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbc2798: ldr             x16, [x16, #0x50]
    // 0xbc279c: r30 = 12.000000
    //     0xbc279c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc27a0: ldr             lr, [lr, #0x9e8]
    // 0xbc27a4: stp             lr, x16, [SP]
    // 0xbc27a8: mov             x1, x0
    // 0xbc27ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc27ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc27b0: ldr             x4, [x4, #0x9b8]
    // 0xbc27b4: r0 = copyWith()
    //     0xbc27b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc27b8: stur            x0, [fp, #-0x10]
    // 0xbc27bc: r0 = Text()
    //     0xbc27bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc27c0: mov             x3, x0
    // 0xbc27c4: ldur            x0, [fp, #-8]
    // 0xbc27c8: stur            x3, [fp, #-0x28]
    // 0xbc27cc: StoreField: r3->field_b = r0
    //     0xbc27cc: stur            w0, [x3, #0xb]
    // 0xbc27d0: ldur            x0, [fp, #-0x10]
    // 0xbc27d4: StoreField: r3->field_13 = r0
    //     0xbc27d4: stur            w0, [x3, #0x13]
    // 0xbc27d8: r1 = Null
    //     0xbc27d8: mov             x1, NULL
    // 0xbc27dc: r2 = 8
    //     0xbc27dc: movz            x2, #0x8
    // 0xbc27e0: r0 = AllocateArray()
    //     0xbc27e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc27e4: mov             x2, x0
    // 0xbc27e8: ldur            x0, [fp, #-0x18]
    // 0xbc27ec: stur            x2, [fp, #-8]
    // 0xbc27f0: StoreField: r2->field_f = r0
    //     0xbc27f0: stur            w0, [x2, #0xf]
    // 0xbc27f4: ldur            x0, [fp, #-0x20]
    // 0xbc27f8: StoreField: r2->field_13 = r0
    //     0xbc27f8: stur            w0, [x2, #0x13]
    // 0xbc27fc: ldur            x0, [fp, #-0x38]
    // 0xbc2800: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc2800: stur            w0, [x2, #0x17]
    // 0xbc2804: ldur            x0, [fp, #-0x28]
    // 0xbc2808: StoreField: r2->field_1b = r0
    //     0xbc2808: stur            w0, [x2, #0x1b]
    // 0xbc280c: r1 = <Widget>
    //     0xbc280c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc2810: r0 = AllocateGrowableArray()
    //     0xbc2810: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc2814: mov             x1, x0
    // 0xbc2818: ldur            x0, [fp, #-8]
    // 0xbc281c: stur            x1, [fp, #-0x10]
    // 0xbc2820: StoreField: r1->field_f = r0
    //     0xbc2820: stur            w0, [x1, #0xf]
    // 0xbc2824: r0 = 8
    //     0xbc2824: movz            x0, #0x8
    // 0xbc2828: StoreField: r1->field_b = r0
    //     0xbc2828: stur            w0, [x1, #0xb]
    // 0xbc282c: r0 = Row()
    //     0xbc282c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc2830: r1 = Instance_Axis
    //     0xbc2830: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc2834: StoreField: r0->field_f = r1
    //     0xbc2834: stur            w1, [x0, #0xf]
    // 0xbc2838: r1 = Instance_MainAxisAlignment
    //     0xbc2838: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc283c: ldr             x1, [x1, #0xa08]
    // 0xbc2840: StoreField: r0->field_13 = r1
    //     0xbc2840: stur            w1, [x0, #0x13]
    // 0xbc2844: r1 = Instance_MainAxisSize
    //     0xbc2844: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc2848: ldr             x1, [x1, #0xa10]
    // 0xbc284c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbc284c: stur            w1, [x0, #0x17]
    // 0xbc2850: r1 = Instance_CrossAxisAlignment
    //     0xbc2850: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc2854: ldr             x1, [x1, #0xa18]
    // 0xbc2858: StoreField: r0->field_1b = r1
    //     0xbc2858: stur            w1, [x0, #0x1b]
    // 0xbc285c: r1 = Instance_VerticalDirection
    //     0xbc285c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc2860: ldr             x1, [x1, #0xa20]
    // 0xbc2864: StoreField: r0->field_23 = r1
    //     0xbc2864: stur            w1, [x0, #0x23]
    // 0xbc2868: r1 = Instance_Clip
    //     0xbc2868: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc286c: ldr             x1, [x1, #0x38]
    // 0xbc2870: StoreField: r0->field_2b = r1
    //     0xbc2870: stur            w1, [x0, #0x2b]
    // 0xbc2874: StoreField: r0->field_2f = rZR
    //     0xbc2874: stur            xzr, [x0, #0x2f]
    // 0xbc2878: ldur            x1, [fp, #-0x10]
    // 0xbc287c: StoreField: r0->field_b = r1
    //     0xbc287c: stur            w1, [x0, #0xb]
    // 0xbc2880: LeaveFrame
    //     0xbc2880: mov             SP, fp
    //     0xbc2884: ldp             fp, lr, [SP], #0x10
    // 0xbc2888: ret
    //     0xbc2888: ret             
    // 0xbc288c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc288c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2890: b               #0xbc226c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc2894, size: 0x48
    // 0xbc2894: EnterFrame
    //     0xbc2894: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2898: mov             fp, SP
    // 0xbc289c: ldr             x0, [fp, #0x10]
    // 0xbc28a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc28a0: ldur            w1, [x0, #0x17]
    // 0xbc28a4: DecompressPointer r1
    //     0xbc28a4: add             x1, x1, HEAP, lsl #32
    // 0xbc28a8: CheckStackOverflow
    //     0xbc28a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc28ac: cmp             SP, x16
    //     0xbc28b0: b.ls            #0xbc28d4
    // 0xbc28b4: LoadField: r0 = r1->field_f
    //     0xbc28b4: ldur            w0, [x1, #0xf]
    // 0xbc28b8: DecompressPointer r0
    //     0xbc28b8: add             x0, x0, HEAP, lsl #32
    // 0xbc28bc: mov             x1, x0
    // 0xbc28c0: r0 = _resendCode()
    //     0xbc28c0: bl              #0xbc28dc  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_resendCode
    // 0xbc28c4: r0 = Null
    //     0xbc28c4: mov             x0, NULL
    // 0xbc28c8: LeaveFrame
    //     0xbc28c8: mov             SP, fp
    //     0xbc28cc: ldp             fp, lr, [SP], #0x10
    // 0xbc28d0: ret
    //     0xbc28d0: ret             
    // 0xbc28d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc28d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc28d8: b               #0xbc28b4
  }
  _ _resendCode(/* No info */) {
    // ** addr: 0xbc28dc, size: 0x64
    // 0xbc28dc: EnterFrame
    //     0xbc28dc: stp             fp, lr, [SP, #-0x10]!
    //     0xbc28e0: mov             fp, SP
    // 0xbc28e4: AllocStack(0x8)
    //     0xbc28e4: sub             SP, SP, #8
    // 0xbc28e8: SetupParameters(_OtpBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xbc28e8: stur            x1, [fp, #-8]
    // 0xbc28ec: CheckStackOverflow
    //     0xbc28ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc28f0: cmp             SP, x16
    //     0xbc28f4: b.ls            #0xbc2938
    // 0xbc28f8: r1 = 1
    //     0xbc28f8: movz            x1, #0x1
    // 0xbc28fc: r0 = AllocateContext()
    //     0xbc28fc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbc2900: mov             x1, x0
    // 0xbc2904: ldur            x0, [fp, #-8]
    // 0xbc2908: StoreField: r1->field_f = r0
    //     0xbc2908: stur            w0, [x1, #0xf]
    // 0xbc290c: mov             x2, x1
    // 0xbc2910: r1 = Function '<anonymous closure>':.
    //     0xbc2910: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c340] AnonymousClosure: (0xbc2940), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_resendCode (0xbc28dc)
    //     0xbc2914: ldr             x1, [x1, #0x340]
    // 0xbc2918: r0 = AllocateClosure()
    //     0xbc2918: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc291c: ldur            x1, [fp, #-8]
    // 0xbc2920: mov             x2, x0
    // 0xbc2924: r0 = setState()
    //     0xbc2924: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbc2928: r0 = Null
    //     0xbc2928: mov             x0, NULL
    // 0xbc292c: LeaveFrame
    //     0xbc292c: mov             SP, fp
    //     0xbc2930: ldp             fp, lr, [SP], #0x10
    // 0xbc2934: ret
    //     0xbc2934: ret             
    // 0xbc2938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2938: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc293c: b               #0xbc28f8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc2940, size: 0xe0
    // 0xbc2940: EnterFrame
    //     0xbc2940: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2944: mov             fp, SP
    // 0xbc2948: AllocStack(0x18)
    //     0xbc2948: sub             SP, SP, #0x18
    // 0xbc294c: SetupParameters()
    //     0xbc294c: add             x0, NULL, #0x30  ; false
    //     0xbc2950: ldr             x1, [fp, #0x10]
    //     0xbc2954: ldur            w2, [x1, #0x17]
    //     0xbc2958: add             x2, x2, HEAP, lsl #32
    //     0xbc295c: stur            x2, [fp, #-8]
    // 0xbc294c: r0 = false
    // 0xbc2960: CheckStackOverflow
    //     0xbc2960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2964: cmp             SP, x16
    //     0xbc2968: b.ls            #0xbc2a14
    // 0xbc296c: LoadField: r1 = r2->field_f
    //     0xbc296c: ldur            w1, [x2, #0xf]
    // 0xbc2970: DecompressPointer r1
    //     0xbc2970: add             x1, x1, HEAP, lsl #32
    // 0xbc2974: StoreField: r1->field_13 = r0
    //     0xbc2974: stur            w0, [x1, #0x13]
    // 0xbc2978: LoadField: r0 = r1->field_b
    //     0xbc2978: ldur            w0, [x1, #0xb]
    // 0xbc297c: DecompressPointer r0
    //     0xbc297c: add             x0, x0, HEAP, lsl #32
    // 0xbc2980: cmp             w0, NULL
    // 0xbc2984: b.eq            #0xbc2a1c
    // 0xbc2988: LoadField: r1 = r0->field_f
    //     0xbc2988: ldur            w1, [x0, #0xf]
    // 0xbc298c: DecompressPointer r1
    //     0xbc298c: add             x1, x1, HEAP, lsl #32
    // 0xbc2990: str             x1, [SP]
    // 0xbc2994: r4 = 0
    //     0xbc2994: movz            x4, #0
    // 0xbc2998: ldr             x0, [SP]
    // 0xbc299c: r16 = UnlinkedCall_0x613b5c
    //     0xbc299c: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c348] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbc29a0: add             x16, x16, #0x348
    // 0xbc29a4: ldp             x5, lr, [x16]
    // 0xbc29a8: blr             lr
    // 0xbc29ac: ldur            x0, [fp, #-8]
    // 0xbc29b0: LoadField: r3 = r0->field_f
    //     0xbc29b0: ldur            w3, [x0, #0xf]
    // 0xbc29b4: DecompressPointer r3
    //     0xbc29b4: add             x3, x3, HEAP, lsl #32
    // 0xbc29b8: stur            x3, [fp, #-0x10]
    // 0xbc29bc: r1 = Function '<anonymous closure>':.
    //     0xbc29bc: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c358] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xbc29c0: ldr             x1, [x1, #0x358]
    // 0xbc29c4: r2 = Null
    //     0xbc29c4: mov             x2, NULL
    // 0xbc29c8: r0 = AllocateClosure()
    //     0xbc29c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc29cc: mov             x2, x0
    // 0xbc29d0: r1 = <int>
    //     0xbc29d0: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xbc29d4: r0 = Stream.periodic()
    //     0xbc29d4: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xbc29d8: mov             x1, x0
    // 0xbc29dc: r2 = 30
    //     0xbc29dc: movz            x2, #0x1e
    // 0xbc29e0: r0 = take()
    //     0xbc29e0: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xbc29e4: ldur            x1, [fp, #-0x10]
    // 0xbc29e8: StoreField: r1->field_1f = r0
    //     0xbc29e8: stur            w0, [x1, #0x1f]
    //     0xbc29ec: ldurb           w16, [x1, #-1]
    //     0xbc29f0: ldurb           w17, [x0, #-1]
    //     0xbc29f4: and             x16, x17, x16, lsr #2
    //     0xbc29f8: tst             x16, HEAP, lsr #32
    //     0xbc29fc: b.eq            #0xbc2a04
    //     0xbc2a00: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbc2a04: r0 = Null
    //     0xbc2a04: mov             x0, NULL
    // 0xbc2a08: LeaveFrame
    //     0xbc2a08: mov             SP, fp
    //     0xbc2a0c: ldp             fp, lr, [SP], #0x10
    // 0xbc2a10: ret
    //     0xbc2a10: ret             
    // 0xbc2a14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2a14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2a18: b               #0xbc296c
    // 0xbc2a1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2a1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbc2a20, size: 0x120
    // 0xbc2a20: EnterFrame
    //     0xbc2a20: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2a24: mov             fp, SP
    // 0xbc2a28: AllocStack(0x28)
    //     0xbc2a28: sub             SP, SP, #0x28
    // 0xbc2a2c: SetupParameters()
    //     0xbc2a2c: ldr             x0, [fp, #0x18]
    //     0xbc2a30: ldur            w2, [x0, #0x17]
    //     0xbc2a34: add             x2, x2, HEAP, lsl #32
    //     0xbc2a38: stur            x2, [fp, #-8]
    // 0xbc2a3c: CheckStackOverflow
    //     0xbc2a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2a40: cmp             SP, x16
    //     0xbc2a44: b.ls            #0xbc2b34
    // 0xbc2a48: LoadField: r1 = r2->field_f
    //     0xbc2a48: ldur            w1, [x2, #0xf]
    // 0xbc2a4c: DecompressPointer r1
    //     0xbc2a4c: add             x1, x1, HEAP, lsl #32
    // 0xbc2a50: ldr             x3, [fp, #0x10]
    // 0xbc2a54: cmp             w3, NULL
    // 0xbc2a58: b.ne            #0xbc2a64
    // 0xbc2a5c: r0 = ""
    //     0xbc2a5c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc2a60: b               #0xbc2a68
    // 0xbc2a64: mov             x0, x3
    // 0xbc2a68: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc2a68: stur            w0, [x1, #0x17]
    //     0xbc2a6c: ldurb           w16, [x1, #-1]
    //     0xbc2a70: ldurb           w17, [x0, #-1]
    //     0xbc2a74: and             x16, x17, x16, lsr #2
    //     0xbc2a78: tst             x16, HEAP, lsr #32
    //     0xbc2a7c: b.eq            #0xbc2a84
    //     0xbc2a80: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbc2a84: r0 = validateAddress()
    //     0xbc2a84: bl              #0xbc2b40  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress
    // 0xbc2a88: ldr             x0, [fp, #0x10]
    // 0xbc2a8c: cmp             w0, NULL
    // 0xbc2a90: b.eq            #0xbc2b24
    // 0xbc2a94: LoadField: r1 = r0->field_7
    //     0xbc2a94: ldur            w1, [x0, #7]
    // 0xbc2a98: cmp             w1, #8
    // 0xbc2a9c: b.ne            #0xbc2b24
    // 0xbc2aa0: ldur            x0, [fp, #-8]
    // 0xbc2aa4: LoadField: r1 = r0->field_13
    //     0xbc2aa4: ldur            w1, [x0, #0x13]
    // 0xbc2aa8: DecompressPointer r1
    //     0xbc2aa8: add             x1, x1, HEAP, lsl #32
    // 0xbc2aac: r0 = of()
    //     0xbc2aac: bl              #0x81ef68  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xbc2ab0: stur            x0, [fp, #-0x10]
    // 0xbc2ab4: r0 = FocusNode()
    //     0xbc2ab4: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xbc2ab8: mov             x1, x0
    // 0xbc2abc: stur            x0, [fp, #-0x18]
    // 0xbc2ac0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbc2ac0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbc2ac4: r0 = FocusNode()
    //     0xbc2ac4: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xbc2ac8: ldur            x16, [fp, #-0x18]
    // 0xbc2acc: str             x16, [SP]
    // 0xbc2ad0: ldur            x1, [fp, #-0x10]
    // 0xbc2ad4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xbc2ad4: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xbc2ad8: r0 = requestFocus()
    //     0xbc2ad8: bl              #0x6595f4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xbc2adc: ldur            x0, [fp, #-8]
    // 0xbc2ae0: LoadField: r1 = r0->field_f
    //     0xbc2ae0: ldur            w1, [x0, #0xf]
    // 0xbc2ae4: DecompressPointer r1
    //     0xbc2ae4: add             x1, x1, HEAP, lsl #32
    // 0xbc2ae8: LoadField: r0 = r1->field_b
    //     0xbc2ae8: ldur            w0, [x1, #0xb]
    // 0xbc2aec: DecompressPointer r0
    //     0xbc2aec: add             x0, x0, HEAP, lsl #32
    // 0xbc2af0: cmp             w0, NULL
    // 0xbc2af4: b.eq            #0xbc2b3c
    // 0xbc2af8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbc2af8: ldur            w2, [x1, #0x17]
    // 0xbc2afc: DecompressPointer r2
    //     0xbc2afc: add             x2, x2, HEAP, lsl #32
    // 0xbc2b00: LoadField: r1 = r0->field_b
    //     0xbc2b00: ldur            w1, [x0, #0xb]
    // 0xbc2b04: DecompressPointer r1
    //     0xbc2b04: add             x1, x1, HEAP, lsl #32
    // 0xbc2b08: stp             x2, x1, [SP]
    // 0xbc2b0c: r4 = 0
    //     0xbc2b0c: movz            x4, #0
    // 0xbc2b10: ldr             x0, [SP, #8]
    // 0xbc2b14: r16 = UnlinkedCall_0x613b5c
    //     0xbc2b14: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c360] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbc2b18: add             x16, x16, #0x360
    // 0xbc2b1c: ldp             x5, lr, [x16]
    // 0xbc2b20: blr             lr
    // 0xbc2b24: r0 = Null
    //     0xbc2b24: mov             x0, NULL
    // 0xbc2b28: LeaveFrame
    //     0xbc2b28: mov             SP, fp
    //     0xbc2b2c: ldp             fp, lr, [SP], #0x10
    // 0xbc2b30: ret
    //     0xbc2b30: ret             
    // 0xbc2b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2b34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2b38: b               #0xbc2a48
    // 0xbc2b3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2b3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ validateAddress(/* No info */) {
    // ** addr: 0xbc2b40, size: 0x6c
    // 0xbc2b40: EnterFrame
    //     0xbc2b40: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2b44: mov             fp, SP
    // 0xbc2b48: AllocStack(0x8)
    //     0xbc2b48: sub             SP, SP, #8
    // 0xbc2b4c: SetupParameters(_OtpBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xbc2b4c: stur            x1, [fp, #-8]
    // 0xbc2b50: CheckStackOverflow
    //     0xbc2b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2b54: cmp             SP, x16
    //     0xbc2b58: b.ls            #0xbc2ba4
    // 0xbc2b5c: r1 = 1
    //     0xbc2b5c: movz            x1, #0x1
    // 0xbc2b60: r0 = AllocateContext()
    //     0xbc2b60: bl              #0x16f6108  ; AllocateContextStub
    // 0xbc2b64: mov             x1, x0
    // 0xbc2b68: ldur            x0, [fp, #-8]
    // 0xbc2b6c: StoreField: r1->field_f = r0
    //     0xbc2b6c: stur            w0, [x1, #0xf]
    // 0xbc2b70: mov             x2, x1
    // 0xbc2b74: r1 = Function '<anonymous closure>':.
    //     0xbc2b74: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c370] AnonymousClosure: (0xa28738), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress (0xa28784)
    //     0xbc2b78: ldr             x1, [x1, #0x370]
    // 0xbc2b7c: r0 = AllocateClosure()
    //     0xbc2b7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc2b80: ldur            x1, [fp, #-8]
    // 0xbc2b84: mov             x2, x0
    // 0xbc2b88: r0 = setState()
    //     0xbc2b88: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbc2b8c: ldur            x1, [fp, #-8]
    // 0xbc2b90: LoadField: r0 = r1->field_1b
    //     0xbc2b90: ldur            w0, [x1, #0x1b]
    // 0xbc2b94: DecompressPointer r0
    //     0xbc2b94: add             x0, x0, HEAP, lsl #32
    // 0xbc2b98: LeaveFrame
    //     0xbc2b98: mov             SP, fp
    //     0xbc2b9c: ldp             fp, lr, [SP], #0x10
    // 0xbc2ba0: ret
    //     0xbc2ba0: ret             
    // 0xbc2ba4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2ba4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2ba8: b               #0xbc2b5c
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0xbc2bac, size: 0x68
    // 0xbc2bac: EnterFrame
    //     0xbc2bac: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2bb0: mov             fp, SP
    // 0xbc2bb4: ldr             x0, [fp, #0x18]
    // 0xbc2bb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbc2bb8: ldur            w1, [x0, #0x17]
    // 0xbc2bbc: DecompressPointer r1
    //     0xbc2bbc: add             x1, x1, HEAP, lsl #32
    // 0xbc2bc0: CheckStackOverflow
    //     0xbc2bc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2bc4: cmp             SP, x16
    //     0xbc2bc8: b.ls            #0xbc2c0c
    // 0xbc2bcc: LoadField: r2 = r1->field_f
    //     0xbc2bcc: ldur            w2, [x1, #0xf]
    // 0xbc2bd0: DecompressPointer r2
    //     0xbc2bd0: add             x2, x2, HEAP, lsl #32
    // 0xbc2bd4: ldr             x0, [fp, #0x10]
    // 0xbc2bd8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc2bd8: stur            w0, [x2, #0x17]
    //     0xbc2bdc: ldurb           w16, [x2, #-1]
    //     0xbc2be0: ldurb           w17, [x0, #-1]
    //     0xbc2be4: and             x16, x17, x16, lsr #2
    //     0xbc2be8: tst             x16, HEAP, lsr #32
    //     0xbc2bec: b.eq            #0xbc2bf4
    //     0xbc2bf0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbc2bf4: mov             x1, x2
    // 0xbc2bf8: r0 = validateAddress()
    //     0xbc2bf8: bl              #0xbc2b40  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::validateAddress
    // 0xbc2bfc: r0 = Null
    //     0xbc2bfc: mov             x0, NULL
    // 0xbc2c00: LeaveFrame
    //     0xbc2c00: mov             SP, fp
    //     0xbc2c04: ldp             fp, lr, [SP], #0x10
    // 0xbc2c08: ret
    //     0xbc2c08: ret             
    // 0xbc2c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2c0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2c10: b               #0xbc2bcc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc2c14, size: 0x7c
    // 0xbc2c14: EnterFrame
    //     0xbc2c14: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2c18: mov             fp, SP
    // 0xbc2c1c: AllocStack(0x8)
    //     0xbc2c1c: sub             SP, SP, #8
    // 0xbc2c20: SetupParameters()
    //     0xbc2c20: ldr             x0, [fp, #0x10]
    //     0xbc2c24: ldur            w1, [x0, #0x17]
    //     0xbc2c28: add             x1, x1, HEAP, lsl #32
    // 0xbc2c2c: CheckStackOverflow
    //     0xbc2c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2c30: cmp             SP, x16
    //     0xbc2c34: b.ls            #0xbc2c84
    // 0xbc2c38: LoadField: r0 = r1->field_f
    //     0xbc2c38: ldur            w0, [x1, #0xf]
    // 0xbc2c3c: DecompressPointer r0
    //     0xbc2c3c: add             x0, x0, HEAP, lsl #32
    // 0xbc2c40: LoadField: r1 = r0->field_b
    //     0xbc2c40: ldur            w1, [x0, #0xb]
    // 0xbc2c44: DecompressPointer r1
    //     0xbc2c44: add             x1, x1, HEAP, lsl #32
    // 0xbc2c48: cmp             w1, NULL
    // 0xbc2c4c: b.eq            #0xbc2c8c
    // 0xbc2c50: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbc2c50: ldur            w0, [x1, #0x17]
    // 0xbc2c54: DecompressPointer r0
    //     0xbc2c54: add             x0, x0, HEAP, lsl #32
    // 0xbc2c58: str             x0, [SP]
    // 0xbc2c5c: r4 = 0
    //     0xbc2c5c: movz            x4, #0
    // 0xbc2c60: ldr             x0, [SP]
    // 0xbc2c64: r16 = UnlinkedCall_0x613b5c
    //     0xbc2c64: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c378] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbc2c68: add             x16, x16, #0x378
    // 0xbc2c6c: ldp             x5, lr, [x16]
    // 0xbc2c70: blr             lr
    // 0xbc2c74: r0 = Null
    //     0xbc2c74: mov             x0, NULL
    // 0xbc2c78: LeaveFrame
    //     0xbc2c78: mov             SP, fp
    //     0xbc2c7c: ldp             fp, lr, [SP], #0x10
    // 0xbc2c80: ret
    //     0xbc2c80: ret             
    // 0xbc2c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc2c84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc2c88: b               #0xbc2c38
    // 0xbc2c8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc2c8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _OtpBottomSheetState(/* No info */) {
    // ** addr: 0xc805c8, size: 0x9c
    // 0xc805c8: EnterFrame
    //     0xc805c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc805cc: mov             fp, SP
    // 0xc805d0: AllocStack(0x8)
    //     0xc805d0: sub             SP, SP, #8
    // 0xc805d4: r2 = false
    //     0xc805d4: add             x2, NULL, #0x30  ; false
    // 0xc805d8: r0 = ""
    //     0xc805d8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc805dc: mov             x3, x1
    // 0xc805e0: stur            x1, [fp, #-8]
    // 0xc805e4: CheckStackOverflow
    //     0xc805e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc805e8: cmp             SP, x16
    //     0xc805ec: b.ls            #0xc8065c
    // 0xc805f0: StoreField: r3->field_13 = r2
    //     0xc805f0: stur            w2, [x3, #0x13]
    // 0xc805f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xc805f4: stur            w0, [x3, #0x17]
    // 0xc805f8: StoreField: r3->field_1b = r2
    //     0xc805f8: stur            w2, [x3, #0x1b]
    // 0xc805fc: r1 = Function '<anonymous closure>':.
    //     0xc805fc: add             x1, PP, #0x49, lsl #12  ; [pp+0x493f8] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xc80600: ldr             x1, [x1, #0x3f8]
    // 0xc80604: r2 = Null
    //     0xc80604: mov             x2, NULL
    // 0xc80608: r0 = AllocateClosure()
    //     0xc80608: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc8060c: mov             x2, x0
    // 0xc80610: r1 = <int>
    //     0xc80610: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc80614: r0 = Stream.periodic()
    //     0xc80614: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xc80618: mov             x1, x0
    // 0xc8061c: r2 = 30
    //     0xc8061c: movz            x2, #0x1e
    // 0xc80620: r0 = take()
    //     0xc80620: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xc80624: ldur            x1, [fp, #-8]
    // 0xc80628: StoreField: r1->field_1f = r0
    //     0xc80628: stur            w0, [x1, #0x1f]
    //     0xc8062c: ldurb           w16, [x1, #-1]
    //     0xc80630: ldurb           w17, [x0, #-1]
    //     0xc80634: and             x16, x17, x16, lsr #2
    //     0xc80638: tst             x16, HEAP, lsr #32
    //     0xc8063c: b.eq            #0xc80644
    //     0xc80640: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc80644: r1 = Null
    //     0xc80644: mov             x1, NULL
    // 0xc80648: r0 = SmsAutoFill()
    //     0xc80648: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0xc8064c: r0 = Null
    //     0xc8064c: mov             x0, NULL
    // 0xc80650: LeaveFrame
    //     0xc80650: mov             SP, fp
    //     0xc80654: ldp             fp, lr, [SP], #0x10
    // 0xc80658: ret
    //     0xc80658: ret             
    // 0xc8065c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8065c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80660: b               #0xc805f0
  }
}

// class id: 4011, size: 0x1c, field offset: 0xc
//   const constructor, 
class OtpBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80580, size: 0x48
    // 0xc80580: EnterFrame
    //     0xc80580: stp             fp, lr, [SP, #-0x10]!
    //     0xc80584: mov             fp, SP
    // 0xc80588: AllocStack(0x8)
    //     0xc80588: sub             SP, SP, #8
    // 0xc8058c: CheckStackOverflow
    //     0xc8058c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80590: cmp             SP, x16
    //     0xc80594: b.ls            #0xc805c0
    // 0xc80598: r1 = <OtpBottomSheet>
    //     0xc80598: add             x1, PP, #0x49, lsl #12  ; [pp+0x493f0] TypeArguments: <OtpBottomSheet>
    //     0xc8059c: ldr             x1, [x1, #0x3f0]
    // 0xc805a0: r0 = _OtpBottomSheetState()
    //     0xc805a0: bl              #0xc80664  ; Allocate_OtpBottomSheetStateStub -> _OtpBottomSheetState (size=0x24)
    // 0xc805a4: mov             x1, x0
    // 0xc805a8: stur            x0, [fp, #-8]
    // 0xc805ac: r0 = _OtpBottomSheetState()
    //     0xc805ac: bl              #0xc805c8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::_OtpBottomSheetState
    // 0xc805b0: ldur            x0, [fp, #-8]
    // 0xc805b4: LeaveFrame
    //     0xc805b4: mov             SP, fp
    //     0xc805b8: ldp             fp, lr, [SP], #0x10
    // 0xc805bc: ret
    //     0xc805bc: ret             
    // 0xc805c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc805c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc805c4: b               #0xc80598
  }
}
