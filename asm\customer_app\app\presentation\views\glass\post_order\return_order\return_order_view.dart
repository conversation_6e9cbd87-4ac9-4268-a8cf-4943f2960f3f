// lib: , url: package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart

// class id: 1049426, size: 0x8
class :: {
}

// class id: 4557, size: 0x14, field offset: 0x14
//   const constructor, 
class ReturnOrderView extends BaseView<dynamic> {

  [closure] Divider <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb4eeb8, size: 0x64
    // 0xb4eeb8: EnterFrame
    //     0xb4eeb8: stp             fp, lr, [SP, #-0x10]!
    //     0xb4eebc: mov             fp, SP
    // 0xb4eec0: AllocStack(0x8)
    //     0xb4eec0: sub             SP, SP, #8
    // 0xb4eec4: CheckStackOverflow
    //     0xb4eec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4eec8: cmp             SP, x16
    //     0xb4eecc: b.ls            #0xb4ef14
    // 0xb4eed0: ldr             x1, [fp, #0x18]
    // 0xb4eed4: r0 = of()
    //     0xb4eed4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4eed8: LoadField: r1 = r0->field_5b
    //     0xb4eed8: ldur            w1, [x0, #0x5b]
    // 0xb4eedc: DecompressPointer r1
    //     0xb4eedc: add             x1, x1, HEAP, lsl #32
    // 0xb4eee0: r0 = LoadClassIdInstr(r1)
    //     0xb4eee0: ldur            x0, [x1, #-1]
    //     0xb4eee4: ubfx            x0, x0, #0xc, #0x14
    // 0xb4eee8: d0 = 0.100000
    //     0xb4eee8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4eeec: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4eeec: sub             lr, x0, #0xffa
    //     0xb4eef0: ldr             lr, [x21, lr, lsl #3]
    //     0xb4eef4: blr             lr
    // 0xb4eef8: stur            x0, [fp, #-8]
    // 0xb4eefc: r0 = Divider()
    //     0xb4eefc: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb4ef00: ldur            x1, [fp, #-8]
    // 0xb4ef04: StoreField: r0->field_1f = r1
    //     0xb4ef04: stur            w1, [x0, #0x1f]
    // 0xb4ef08: LeaveFrame
    //     0xb4ef08: mov             SP, fp
    //     0xb4ef0c: ldp             fp, lr, [SP], #0x10
    // 0xb4ef10: ret
    //     0xb4ef10: ret             
    // 0xb4ef14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4ef14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4ef18: b               #0xb4eed0
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0xb4ef1c, size: 0x1fb0
    // 0xb4ef1c: EnterFrame
    //     0xb4ef1c: stp             fp, lr, [SP, #-0x10]!
    //     0xb4ef20: mov             fp, SP
    // 0xb4ef24: AllocStack(0xb0)
    //     0xb4ef24: sub             SP, SP, #0xb0
    // 0xb4ef28: SetupParameters()
    //     0xb4ef28: ldr             x0, [fp, #0x10]
    //     0xb4ef2c: ldur            w2, [x0, #0x17]
    //     0xb4ef30: add             x2, x2, HEAP, lsl #32
    //     0xb4ef34: stur            x2, [fp, #-8]
    // 0xb4ef38: CheckStackOverflow
    //     0xb4ef38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4ef3c: cmp             SP, x16
    //     0xb4ef40: b.ls            #0xb50ec4
    // 0xb4ef44: LoadField: r1 = r2->field_f
    //     0xb4ef44: ldur            w1, [x2, #0xf]
    // 0xb4ef48: DecompressPointer r1
    //     0xb4ef48: add             x1, x1, HEAP, lsl #32
    // 0xb4ef4c: r0 = controller()
    //     0xb4ef4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4ef50: LoadField: r1 = r0->field_57
    //     0xb4ef50: ldur            w1, [x0, #0x57]
    // 0xb4ef54: DecompressPointer r1
    //     0xb4ef54: add             x1, x1, HEAP, lsl #32
    // 0xb4ef58: r0 = value()
    //     0xb4ef58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4ef5c: cmp             w0, NULL
    // 0xb4ef60: b.eq            #0xb50ea0
    // 0xb4ef64: ldur            x2, [fp, #-8]
    // 0xb4ef68: r0 = Radius()
    //     0xb4ef68: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4ef6c: d0 = 15.000000
    //     0xb4ef6c: fmov            d0, #15.00000000
    // 0xb4ef70: stur            x0, [fp, #-0x10]
    // 0xb4ef74: StoreField: r0->field_7 = d0
    //     0xb4ef74: stur            d0, [x0, #7]
    // 0xb4ef78: StoreField: r0->field_f = d0
    //     0xb4ef78: stur            d0, [x0, #0xf]
    // 0xb4ef7c: r0 = BorderRadius()
    //     0xb4ef7c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4ef80: mov             x1, x0
    // 0xb4ef84: ldur            x0, [fp, #-0x10]
    // 0xb4ef88: stur            x1, [fp, #-0x18]
    // 0xb4ef8c: StoreField: r1->field_7 = r0
    //     0xb4ef8c: stur            w0, [x1, #7]
    // 0xb4ef90: StoreField: r1->field_b = r0
    //     0xb4ef90: stur            w0, [x1, #0xb]
    // 0xb4ef94: StoreField: r1->field_f = r0
    //     0xb4ef94: stur            w0, [x1, #0xf]
    // 0xb4ef98: StoreField: r1->field_13 = r0
    //     0xb4ef98: stur            w0, [x1, #0x13]
    // 0xb4ef9c: r0 = RoundedRectangleBorder()
    //     0xb4ef9c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb4efa0: mov             x2, x0
    // 0xb4efa4: ldur            x0, [fp, #-0x18]
    // 0xb4efa8: stur            x2, [fp, #-0x10]
    // 0xb4efac: StoreField: r2->field_b = r0
    //     0xb4efac: stur            w0, [x2, #0xb]
    // 0xb4efb0: r0 = Instance_BorderSide
    //     0xb4efb0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb4efb4: ldr             x0, [x0, #0xe20]
    // 0xb4efb8: StoreField: r2->field_7 = r0
    //     0xb4efb8: stur            w0, [x2, #7]
    // 0xb4efbc: ldur            x3, [fp, #-8]
    // 0xb4efc0: LoadField: r1 = r3->field_f
    //     0xb4efc0: ldur            w1, [x3, #0xf]
    // 0xb4efc4: DecompressPointer r1
    //     0xb4efc4: add             x1, x1, HEAP, lsl #32
    // 0xb4efc8: r0 = controller()
    //     0xb4efc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4efcc: LoadField: r1 = r0->field_57
    //     0xb4efcc: ldur            w1, [x0, #0x57]
    // 0xb4efd0: DecompressPointer r1
    //     0xb4efd0: add             x1, x1, HEAP, lsl #32
    // 0xb4efd4: r0 = value()
    //     0xb4efd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4efd8: cmp             w0, NULL
    // 0xb4efdc: b.ne            #0xb4efe8
    // 0xb4efe0: r0 = Null
    //     0xb4efe0: mov             x0, NULL
    // 0xb4efe4: b               #0xb4f008
    // 0xb4efe8: LoadField: r1 = r0->field_f
    //     0xb4efe8: ldur            w1, [x0, #0xf]
    // 0xb4efec: DecompressPointer r1
    //     0xb4efec: add             x1, x1, HEAP, lsl #32
    // 0xb4eff0: cmp             w1, NULL
    // 0xb4eff4: b.ne            #0xb4f000
    // 0xb4eff8: r0 = Null
    //     0xb4eff8: mov             x0, NULL
    // 0xb4effc: b               #0xb4f008
    // 0xb4f000: LoadField: r0 = r1->field_7
    //     0xb4f000: ldur            w0, [x1, #7]
    // 0xb4f004: DecompressPointer r0
    //     0xb4f004: add             x0, x0, HEAP, lsl #32
    // 0xb4f008: cmp             w0, NULL
    // 0xb4f00c: b.ne            #0xb4f018
    // 0xb4f010: r3 = ""
    //     0xb4f010: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4f014: b               #0xb4f01c
    // 0xb4f018: mov             x3, x0
    // 0xb4f01c: ldur            x0, [fp, #-8]
    // 0xb4f020: stur            x3, [fp, #-0x18]
    // 0xb4f024: r1 = Function '<anonymous closure>':.
    //     0xb4f024: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f410] AnonymousClosure: (0xb51738), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb4f028: ldr             x1, [x1, #0x410]
    // 0xb4f02c: r2 = Null
    //     0xb4f02c: mov             x2, NULL
    // 0xb4f030: r0 = AllocateClosure()
    //     0xb4f030: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4f034: r1 = Function '<anonymous closure>':.
    //     0xb4f034: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f418] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb4f038: ldr             x1, [x1, #0x418]
    // 0xb4f03c: r2 = Null
    //     0xb4f03c: mov             x2, NULL
    // 0xb4f040: stur            x0, [fp, #-0x20]
    // 0xb4f044: r0 = AllocateClosure()
    //     0xb4f044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4f048: r1 = Function '<anonymous closure>':.
    //     0xb4f048: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f420] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb4f04c: ldr             x1, [x1, #0x420]
    // 0xb4f050: r2 = Null
    //     0xb4f050: mov             x2, NULL
    // 0xb4f054: stur            x0, [fp, #-0x28]
    // 0xb4f058: r0 = AllocateClosure()
    //     0xb4f058: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4f05c: stur            x0, [fp, #-0x30]
    // 0xb4f060: r0 = CachedNetworkImage()
    //     0xb4f060: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb4f064: stur            x0, [fp, #-0x38]
    // 0xb4f068: ldur            x16, [fp, #-0x20]
    // 0xb4f06c: ldur            lr, [fp, #-0x28]
    // 0xb4f070: stp             lr, x16, [SP, #8]
    // 0xb4f074: ldur            x16, [fp, #-0x30]
    // 0xb4f078: str             x16, [SP]
    // 0xb4f07c: mov             x1, x0
    // 0xb4f080: ldur            x2, [fp, #-0x18]
    // 0xb4f084: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, imageBuilder, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb4f084: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f428] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "imageBuilder", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb4f088: ldr             x4, [x4, #0x428]
    // 0xb4f08c: r0 = CachedNetworkImage()
    //     0xb4f08c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb4f090: ldur            x2, [fp, #-8]
    // 0xb4f094: LoadField: r1 = r2->field_f
    //     0xb4f094: ldur            w1, [x2, #0xf]
    // 0xb4f098: DecompressPointer r1
    //     0xb4f098: add             x1, x1, HEAP, lsl #32
    // 0xb4f09c: r0 = controller()
    //     0xb4f09c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f0a0: LoadField: r1 = r0->field_57
    //     0xb4f0a0: ldur            w1, [x0, #0x57]
    // 0xb4f0a4: DecompressPointer r1
    //     0xb4f0a4: add             x1, x1, HEAP, lsl #32
    // 0xb4f0a8: r0 = value()
    //     0xb4f0a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f0ac: cmp             w0, NULL
    // 0xb4f0b0: b.ne            #0xb4f0bc
    // 0xb4f0b4: r0 = Null
    //     0xb4f0b4: mov             x0, NULL
    // 0xb4f0b8: b               #0xb4f0dc
    // 0xb4f0bc: LoadField: r1 = r0->field_f
    //     0xb4f0bc: ldur            w1, [x0, #0xf]
    // 0xb4f0c0: DecompressPointer r1
    //     0xb4f0c0: add             x1, x1, HEAP, lsl #32
    // 0xb4f0c4: cmp             w1, NULL
    // 0xb4f0c8: b.ne            #0xb4f0d4
    // 0xb4f0cc: r0 = Null
    //     0xb4f0cc: mov             x0, NULL
    // 0xb4f0d0: b               #0xb4f0dc
    // 0xb4f0d4: LoadField: r0 = r1->field_b
    //     0xb4f0d4: ldur            w0, [x1, #0xb]
    // 0xb4f0d8: DecompressPointer r0
    //     0xb4f0d8: add             x0, x0, HEAP, lsl #32
    // 0xb4f0dc: cmp             w0, NULL
    // 0xb4f0e0: b.ne            #0xb4f0ec
    // 0xb4f0e4: r1 = ""
    //     0xb4f0e4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4f0e8: b               #0xb4f0f0
    // 0xb4f0ec: mov             x1, x0
    // 0xb4f0f0: ldur            x2, [fp, #-8]
    // 0xb4f0f4: r0 = capitalizeFirstWord()
    //     0xb4f0f4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb4f0f8: ldur            x2, [fp, #-8]
    // 0xb4f0fc: stur            x0, [fp, #-0x18]
    // 0xb4f100: LoadField: r1 = r2->field_13
    //     0xb4f100: ldur            w1, [x2, #0x13]
    // 0xb4f104: DecompressPointer r1
    //     0xb4f104: add             x1, x1, HEAP, lsl #32
    // 0xb4f108: r0 = of()
    //     0xb4f108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4f10c: LoadField: r1 = r0->field_87
    //     0xb4f10c: ldur            w1, [x0, #0x87]
    // 0xb4f110: DecompressPointer r1
    //     0xb4f110: add             x1, x1, HEAP, lsl #32
    // 0xb4f114: LoadField: r0 = r1->field_2b
    //     0xb4f114: ldur            w0, [x1, #0x2b]
    // 0xb4f118: DecompressPointer r0
    //     0xb4f118: add             x0, x0, HEAP, lsl #32
    // 0xb4f11c: stur            x0, [fp, #-0x20]
    // 0xb4f120: r1 = Instance_Color
    //     0xb4f120: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4f124: d0 = 0.700000
    //     0xb4f124: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4f128: ldr             d0, [x17, #0xf48]
    // 0xb4f12c: r0 = withOpacity()
    //     0xb4f12c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4f130: r16 = 12.000000
    //     0xb4f130: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4f134: ldr             x16, [x16, #0x9e8]
    // 0xb4f138: stp             x16, x0, [SP]
    // 0xb4f13c: ldur            x1, [fp, #-0x20]
    // 0xb4f140: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4f140: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4f144: ldr             x4, [x4, #0x9b8]
    // 0xb4f148: r0 = copyWith()
    //     0xb4f148: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4f14c: stur            x0, [fp, #-0x20]
    // 0xb4f150: r0 = Text()
    //     0xb4f150: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4f154: mov             x2, x0
    // 0xb4f158: ldur            x0, [fp, #-0x18]
    // 0xb4f15c: stur            x2, [fp, #-0x28]
    // 0xb4f160: StoreField: r2->field_b = r0
    //     0xb4f160: stur            w0, [x2, #0xb]
    // 0xb4f164: ldur            x0, [fp, #-0x20]
    // 0xb4f168: StoreField: r2->field_13 = r0
    //     0xb4f168: stur            w0, [x2, #0x13]
    // 0xb4f16c: r0 = Instance_TextOverflow
    //     0xb4f16c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb4f170: ldr             x0, [x0, #0xe10]
    // 0xb4f174: StoreField: r2->field_2b = r0
    //     0xb4f174: stur            w0, [x2, #0x2b]
    // 0xb4f178: r3 = 4
    //     0xb4f178: movz            x3, #0x4
    // 0xb4f17c: StoreField: r2->field_37 = r3
    //     0xb4f17c: stur            w3, [x2, #0x37]
    // 0xb4f180: ldur            x4, [fp, #-8]
    // 0xb4f184: LoadField: r1 = r4->field_f
    //     0xb4f184: ldur            w1, [x4, #0xf]
    // 0xb4f188: DecompressPointer r1
    //     0xb4f188: add             x1, x1, HEAP, lsl #32
    // 0xb4f18c: r0 = controller()
    //     0xb4f18c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f190: LoadField: r1 = r0->field_57
    //     0xb4f190: ldur            w1, [x0, #0x57]
    // 0xb4f194: DecompressPointer r1
    //     0xb4f194: add             x1, x1, HEAP, lsl #32
    // 0xb4f198: r0 = value()
    //     0xb4f198: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f19c: cmp             w0, NULL
    // 0xb4f1a0: b.ne            #0xb4f1ac
    // 0xb4f1a4: r0 = Null
    //     0xb4f1a4: mov             x0, NULL
    // 0xb4f1a8: b               #0xb4f1cc
    // 0xb4f1ac: LoadField: r1 = r0->field_f
    //     0xb4f1ac: ldur            w1, [x0, #0xf]
    // 0xb4f1b0: DecompressPointer r1
    //     0xb4f1b0: add             x1, x1, HEAP, lsl #32
    // 0xb4f1b4: cmp             w1, NULL
    // 0xb4f1b8: b.ne            #0xb4f1c4
    // 0xb4f1bc: r0 = Null
    //     0xb4f1bc: mov             x0, NULL
    // 0xb4f1c0: b               #0xb4f1cc
    // 0xb4f1c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb4f1c4: ldur            w0, [x1, #0x17]
    // 0xb4f1c8: DecompressPointer r0
    //     0xb4f1c8: add             x0, x0, HEAP, lsl #32
    // 0xb4f1cc: r1 = LoadClassIdInstr(r0)
    //     0xb4f1cc: ldur            x1, [x0, #-1]
    //     0xb4f1d0: ubfx            x1, x1, #0xc, #0x14
    // 0xb4f1d4: r16 = "size"
    //     0xb4f1d4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb4f1d8: ldr             x16, [x16, #0x9c0]
    // 0xb4f1dc: stp             x16, x0, [SP]
    // 0xb4f1e0: mov             x0, x1
    // 0xb4f1e4: mov             lr, x0
    // 0xb4f1e8: ldr             lr, [x21, lr, lsl #3]
    // 0xb4f1ec: blr             lr
    // 0xb4f1f0: tbnz            w0, #4, #0xb4f354
    // 0xb4f1f4: ldur            x0, [fp, #-8]
    // 0xb4f1f8: r1 = Null
    //     0xb4f1f8: mov             x1, NULL
    // 0xb4f1fc: r2 = 8
    //     0xb4f1fc: movz            x2, #0x8
    // 0xb4f200: r0 = AllocateArray()
    //     0xb4f200: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f204: stur            x0, [fp, #-0x18]
    // 0xb4f208: r16 = "Size :  "
    //     0xb4f208: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0xb4f20c: ldr             x16, [x16, #0x758]
    // 0xb4f210: StoreField: r0->field_f = r16
    //     0xb4f210: stur            w16, [x0, #0xf]
    // 0xb4f214: ldur            x2, [fp, #-8]
    // 0xb4f218: LoadField: r1 = r2->field_f
    //     0xb4f218: ldur            w1, [x2, #0xf]
    // 0xb4f21c: DecompressPointer r1
    //     0xb4f21c: add             x1, x1, HEAP, lsl #32
    // 0xb4f220: r0 = controller()
    //     0xb4f220: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f224: LoadField: r1 = r0->field_57
    //     0xb4f224: ldur            w1, [x0, #0x57]
    // 0xb4f228: DecompressPointer r1
    //     0xb4f228: add             x1, x1, HEAP, lsl #32
    // 0xb4f22c: r0 = value()
    //     0xb4f22c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f230: cmp             w0, NULL
    // 0xb4f234: b.ne            #0xb4f240
    // 0xb4f238: r0 = Null
    //     0xb4f238: mov             x0, NULL
    // 0xb4f23c: b               #0xb4f260
    // 0xb4f240: LoadField: r1 = r0->field_f
    //     0xb4f240: ldur            w1, [x0, #0xf]
    // 0xb4f244: DecompressPointer r1
    //     0xb4f244: add             x1, x1, HEAP, lsl #32
    // 0xb4f248: cmp             w1, NULL
    // 0xb4f24c: b.ne            #0xb4f258
    // 0xb4f250: r0 = Null
    //     0xb4f250: mov             x0, NULL
    // 0xb4f254: b               #0xb4f260
    // 0xb4f258: LoadField: r0 = r1->field_f
    //     0xb4f258: ldur            w0, [x1, #0xf]
    // 0xb4f25c: DecompressPointer r0
    //     0xb4f25c: add             x0, x0, HEAP, lsl #32
    // 0xb4f260: ldur            x3, [fp, #-8]
    // 0xb4f264: ldur            x2, [fp, #-0x18]
    // 0xb4f268: mov             x1, x2
    // 0xb4f26c: ArrayStore: r1[1] = r0  ; List_4
    //     0xb4f26c: add             x25, x1, #0x13
    //     0xb4f270: str             w0, [x25]
    //     0xb4f274: tbz             w0, #0, #0xb4f290
    //     0xb4f278: ldurb           w16, [x1, #-1]
    //     0xb4f27c: ldurb           w17, [x0, #-1]
    //     0xb4f280: and             x16, x17, x16, lsr #2
    //     0xb4f284: tst             x16, HEAP, lsr #32
    //     0xb4f288: b.eq            #0xb4f290
    //     0xb4f28c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4f290: r16 = " / Qty: "
    //     0xb4f290: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb4f294: ldr             x16, [x16, #0x760]
    // 0xb4f298: ArrayStore: r2[0] = r16  ; List_4
    //     0xb4f298: stur            w16, [x2, #0x17]
    // 0xb4f29c: LoadField: r1 = r3->field_f
    //     0xb4f29c: ldur            w1, [x3, #0xf]
    // 0xb4f2a0: DecompressPointer r1
    //     0xb4f2a0: add             x1, x1, HEAP, lsl #32
    // 0xb4f2a4: r0 = controller()
    //     0xb4f2a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f2a8: LoadField: r1 = r0->field_57
    //     0xb4f2a8: ldur            w1, [x0, #0x57]
    // 0xb4f2ac: DecompressPointer r1
    //     0xb4f2ac: add             x1, x1, HEAP, lsl #32
    // 0xb4f2b0: r0 = value()
    //     0xb4f2b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f2b4: cmp             w0, NULL
    // 0xb4f2b8: b.ne            #0xb4f2c4
    // 0xb4f2bc: r0 = Null
    //     0xb4f2bc: mov             x0, NULL
    // 0xb4f2c0: b               #0xb4f310
    // 0xb4f2c4: LoadField: r1 = r0->field_f
    //     0xb4f2c4: ldur            w1, [x0, #0xf]
    // 0xb4f2c8: DecompressPointer r1
    //     0xb4f2c8: add             x1, x1, HEAP, lsl #32
    // 0xb4f2cc: cmp             w1, NULL
    // 0xb4f2d0: b.ne            #0xb4f2dc
    // 0xb4f2d4: r0 = Null
    //     0xb4f2d4: mov             x0, NULL
    // 0xb4f2d8: b               #0xb4f310
    // 0xb4f2dc: LoadField: r0 = r1->field_13
    //     0xb4f2dc: ldur            w0, [x1, #0x13]
    // 0xb4f2e0: DecompressPointer r0
    //     0xb4f2e0: add             x0, x0, HEAP, lsl #32
    // 0xb4f2e4: r1 = 60
    //     0xb4f2e4: movz            x1, #0x3c
    // 0xb4f2e8: branchIfSmi(r0, 0xb4f2f4)
    //     0xb4f2e8: tbz             w0, #0, #0xb4f2f4
    // 0xb4f2ec: r1 = LoadClassIdInstr(r0)
    //     0xb4f2ec: ldur            x1, [x0, #-1]
    //     0xb4f2f0: ubfx            x1, x1, #0xc, #0x14
    // 0xb4f2f4: str             x0, [SP]
    // 0xb4f2f8: mov             x0, x1
    // 0xb4f2fc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb4f2fc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb4f300: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb4f300: movz            x17, #0x2700
    //     0xb4f304: add             lr, x0, x17
    //     0xb4f308: ldr             lr, [x21, lr, lsl #3]
    //     0xb4f30c: blr             lr
    // 0xb4f310: cmp             w0, NULL
    // 0xb4f314: b.ne            #0xb4f31c
    // 0xb4f318: r0 = ""
    //     0xb4f318: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4f31c: ldur            x1, [fp, #-0x18]
    // 0xb4f320: ArrayStore: r1[3] = r0  ; List_4
    //     0xb4f320: add             x25, x1, #0x1b
    //     0xb4f324: str             w0, [x25]
    //     0xb4f328: tbz             w0, #0, #0xb4f344
    //     0xb4f32c: ldurb           w16, [x1, #-1]
    //     0xb4f330: ldurb           w17, [x0, #-1]
    //     0xb4f334: and             x16, x17, x16, lsr #2
    //     0xb4f338: tst             x16, HEAP, lsr #32
    //     0xb4f33c: b.eq            #0xb4f344
    //     0xb4f340: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4f344: ldur            x16, [fp, #-0x18]
    // 0xb4f348: str             x16, [SP]
    // 0xb4f34c: r0 = _interpolate()
    //     0xb4f34c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4f350: b               #0xb4f4b0
    // 0xb4f354: ldur            x0, [fp, #-8]
    // 0xb4f358: r1 = Null
    //     0xb4f358: mov             x1, NULL
    // 0xb4f35c: r2 = 8
    //     0xb4f35c: movz            x2, #0x8
    // 0xb4f360: r0 = AllocateArray()
    //     0xb4f360: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f364: stur            x0, [fp, #-0x18]
    // 0xb4f368: r16 = "Variant : "
    //     0xb4f368: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xb4f36c: ldr             x16, [x16, #0x768]
    // 0xb4f370: StoreField: r0->field_f = r16
    //     0xb4f370: stur            w16, [x0, #0xf]
    // 0xb4f374: ldur            x2, [fp, #-8]
    // 0xb4f378: LoadField: r1 = r2->field_f
    //     0xb4f378: ldur            w1, [x2, #0xf]
    // 0xb4f37c: DecompressPointer r1
    //     0xb4f37c: add             x1, x1, HEAP, lsl #32
    // 0xb4f380: r0 = controller()
    //     0xb4f380: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f384: LoadField: r1 = r0->field_57
    //     0xb4f384: ldur            w1, [x0, #0x57]
    // 0xb4f388: DecompressPointer r1
    //     0xb4f388: add             x1, x1, HEAP, lsl #32
    // 0xb4f38c: r0 = value()
    //     0xb4f38c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f390: cmp             w0, NULL
    // 0xb4f394: b.ne            #0xb4f3a0
    // 0xb4f398: r0 = Null
    //     0xb4f398: mov             x0, NULL
    // 0xb4f39c: b               #0xb4f3c0
    // 0xb4f3a0: LoadField: r1 = r0->field_f
    //     0xb4f3a0: ldur            w1, [x0, #0xf]
    // 0xb4f3a4: DecompressPointer r1
    //     0xb4f3a4: add             x1, x1, HEAP, lsl #32
    // 0xb4f3a8: cmp             w1, NULL
    // 0xb4f3ac: b.ne            #0xb4f3b8
    // 0xb4f3b0: r0 = Null
    //     0xb4f3b0: mov             x0, NULL
    // 0xb4f3b4: b               #0xb4f3c0
    // 0xb4f3b8: LoadField: r0 = r1->field_f
    //     0xb4f3b8: ldur            w0, [x1, #0xf]
    // 0xb4f3bc: DecompressPointer r0
    //     0xb4f3bc: add             x0, x0, HEAP, lsl #32
    // 0xb4f3c0: ldur            x3, [fp, #-8]
    // 0xb4f3c4: ldur            x2, [fp, #-0x18]
    // 0xb4f3c8: mov             x1, x2
    // 0xb4f3cc: ArrayStore: r1[1] = r0  ; List_4
    //     0xb4f3cc: add             x25, x1, #0x13
    //     0xb4f3d0: str             w0, [x25]
    //     0xb4f3d4: tbz             w0, #0, #0xb4f3f0
    //     0xb4f3d8: ldurb           w16, [x1, #-1]
    //     0xb4f3dc: ldurb           w17, [x0, #-1]
    //     0xb4f3e0: and             x16, x17, x16, lsr #2
    //     0xb4f3e4: tst             x16, HEAP, lsr #32
    //     0xb4f3e8: b.eq            #0xb4f3f0
    //     0xb4f3ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4f3f0: r16 = " / Qty: "
    //     0xb4f3f0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xb4f3f4: ldr             x16, [x16, #0x760]
    // 0xb4f3f8: ArrayStore: r2[0] = r16  ; List_4
    //     0xb4f3f8: stur            w16, [x2, #0x17]
    // 0xb4f3fc: LoadField: r1 = r3->field_f
    //     0xb4f3fc: ldur            w1, [x3, #0xf]
    // 0xb4f400: DecompressPointer r1
    //     0xb4f400: add             x1, x1, HEAP, lsl #32
    // 0xb4f404: r0 = controller()
    //     0xb4f404: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f408: LoadField: r1 = r0->field_57
    //     0xb4f408: ldur            w1, [x0, #0x57]
    // 0xb4f40c: DecompressPointer r1
    //     0xb4f40c: add             x1, x1, HEAP, lsl #32
    // 0xb4f410: r0 = value()
    //     0xb4f410: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f414: cmp             w0, NULL
    // 0xb4f418: b.ne            #0xb4f424
    // 0xb4f41c: r0 = Null
    //     0xb4f41c: mov             x0, NULL
    // 0xb4f420: b               #0xb4f470
    // 0xb4f424: LoadField: r1 = r0->field_f
    //     0xb4f424: ldur            w1, [x0, #0xf]
    // 0xb4f428: DecompressPointer r1
    //     0xb4f428: add             x1, x1, HEAP, lsl #32
    // 0xb4f42c: cmp             w1, NULL
    // 0xb4f430: b.ne            #0xb4f43c
    // 0xb4f434: r0 = Null
    //     0xb4f434: mov             x0, NULL
    // 0xb4f438: b               #0xb4f470
    // 0xb4f43c: LoadField: r0 = r1->field_13
    //     0xb4f43c: ldur            w0, [x1, #0x13]
    // 0xb4f440: DecompressPointer r0
    //     0xb4f440: add             x0, x0, HEAP, lsl #32
    // 0xb4f444: r1 = 60
    //     0xb4f444: movz            x1, #0x3c
    // 0xb4f448: branchIfSmi(r0, 0xb4f454)
    //     0xb4f448: tbz             w0, #0, #0xb4f454
    // 0xb4f44c: r1 = LoadClassIdInstr(r0)
    //     0xb4f44c: ldur            x1, [x0, #-1]
    //     0xb4f450: ubfx            x1, x1, #0xc, #0x14
    // 0xb4f454: str             x0, [SP]
    // 0xb4f458: mov             x0, x1
    // 0xb4f45c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb4f45c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb4f460: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb4f460: movz            x17, #0x2700
    //     0xb4f464: add             lr, x0, x17
    //     0xb4f468: ldr             lr, [x21, lr, lsl #3]
    //     0xb4f46c: blr             lr
    // 0xb4f470: cmp             w0, NULL
    // 0xb4f474: b.ne            #0xb4f47c
    // 0xb4f478: r0 = ""
    //     0xb4f478: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4f47c: ldur            x1, [fp, #-0x18]
    // 0xb4f480: ArrayStore: r1[3] = r0  ; List_4
    //     0xb4f480: add             x25, x1, #0x1b
    //     0xb4f484: str             w0, [x25]
    //     0xb4f488: tbz             w0, #0, #0xb4f4a4
    //     0xb4f48c: ldurb           w16, [x1, #-1]
    //     0xb4f490: ldurb           w17, [x0, #-1]
    //     0xb4f494: and             x16, x17, x16, lsr #2
    //     0xb4f498: tst             x16, HEAP, lsr #32
    //     0xb4f49c: b.eq            #0xb4f4a4
    //     0xb4f4a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4f4a4: ldur            x16, [fp, #-0x18]
    // 0xb4f4a8: str             x16, [SP]
    // 0xb4f4ac: r0 = _interpolate()
    //     0xb4f4ac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb4f4b0: ldur            x2, [fp, #-8]
    // 0xb4f4b4: stur            x0, [fp, #-0x18]
    // 0xb4f4b8: LoadField: r1 = r2->field_13
    //     0xb4f4b8: ldur            w1, [x2, #0x13]
    // 0xb4f4bc: DecompressPointer r1
    //     0xb4f4bc: add             x1, x1, HEAP, lsl #32
    // 0xb4f4c0: r0 = of()
    //     0xb4f4c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4f4c4: LoadField: r1 = r0->field_87
    //     0xb4f4c4: ldur            w1, [x0, #0x87]
    // 0xb4f4c8: DecompressPointer r1
    //     0xb4f4c8: add             x1, x1, HEAP, lsl #32
    // 0xb4f4cc: LoadField: r0 = r1->field_2b
    //     0xb4f4cc: ldur            w0, [x1, #0x2b]
    // 0xb4f4d0: DecompressPointer r0
    //     0xb4f4d0: add             x0, x0, HEAP, lsl #32
    // 0xb4f4d4: stur            x0, [fp, #-0x20]
    // 0xb4f4d8: r1 = Instance_Color
    //     0xb4f4d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4f4dc: d0 = 0.400000
    //     0xb4f4dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4f4e0: r0 = withOpacity()
    //     0xb4f4e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4f4e4: r16 = 12.000000
    //     0xb4f4e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4f4e8: ldr             x16, [x16, #0x9e8]
    // 0xb4f4ec: stp             x0, x16, [SP]
    // 0xb4f4f0: ldur            x1, [fp, #-0x20]
    // 0xb4f4f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4f4f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4f4f8: ldr             x4, [x4, #0xaa0]
    // 0xb4f4fc: r0 = copyWith()
    //     0xb4f4fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4f500: stur            x0, [fp, #-0x20]
    // 0xb4f504: r0 = Text()
    //     0xb4f504: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4f508: mov             x1, x0
    // 0xb4f50c: ldur            x0, [fp, #-0x18]
    // 0xb4f510: stur            x1, [fp, #-0x30]
    // 0xb4f514: StoreField: r1->field_b = r0
    //     0xb4f514: stur            w0, [x1, #0xb]
    // 0xb4f518: ldur            x0, [fp, #-0x20]
    // 0xb4f51c: StoreField: r1->field_13 = r0
    //     0xb4f51c: stur            w0, [x1, #0x13]
    // 0xb4f520: r0 = Padding()
    //     0xb4f520: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4f524: mov             x3, x0
    // 0xb4f528: r0 = Instance_EdgeInsets
    //     0xb4f528: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb4f52c: ldr             x0, [x0, #0x668]
    // 0xb4f530: stur            x3, [fp, #-0x18]
    // 0xb4f534: StoreField: r3->field_f = r0
    //     0xb4f534: stur            w0, [x3, #0xf]
    // 0xb4f538: ldur            x0, [fp, #-0x30]
    // 0xb4f53c: StoreField: r3->field_b = r0
    //     0xb4f53c: stur            w0, [x3, #0xb]
    // 0xb4f540: r1 = Null
    //     0xb4f540: mov             x1, NULL
    // 0xb4f544: r2 = 4
    //     0xb4f544: movz            x2, #0x4
    // 0xb4f548: r0 = AllocateArray()
    //     0xb4f548: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f54c: mov             x2, x0
    // 0xb4f550: ldur            x0, [fp, #-0x18]
    // 0xb4f554: stur            x2, [fp, #-0x20]
    // 0xb4f558: StoreField: r2->field_f = r0
    //     0xb4f558: stur            w0, [x2, #0xf]
    // 0xb4f55c: r16 = Instance_SizedBox
    //     0xb4f55c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb4f560: ldr             x16, [x16, #0xb20]
    // 0xb4f564: StoreField: r2->field_13 = r16
    //     0xb4f564: stur            w16, [x2, #0x13]
    // 0xb4f568: r1 = <Widget>
    //     0xb4f568: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4f56c: r0 = AllocateGrowableArray()
    //     0xb4f56c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4f570: mov             x1, x0
    // 0xb4f574: ldur            x0, [fp, #-0x20]
    // 0xb4f578: stur            x1, [fp, #-0x18]
    // 0xb4f57c: StoreField: r1->field_f = r0
    //     0xb4f57c: stur            w0, [x1, #0xf]
    // 0xb4f580: r2 = 4
    //     0xb4f580: movz            x2, #0x4
    // 0xb4f584: StoreField: r1->field_b = r2
    //     0xb4f584: stur            w2, [x1, #0xb]
    // 0xb4f588: r0 = Row()
    //     0xb4f588: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4f58c: mov             x2, x0
    // 0xb4f590: r0 = Instance_Axis
    //     0xb4f590: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4f594: stur            x2, [fp, #-0x20]
    // 0xb4f598: StoreField: r2->field_f = r0
    //     0xb4f598: stur            w0, [x2, #0xf]
    // 0xb4f59c: r3 = Instance_MainAxisAlignment
    //     0xb4f59c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4f5a0: ldr             x3, [x3, #0xa08]
    // 0xb4f5a4: StoreField: r2->field_13 = r3
    //     0xb4f5a4: stur            w3, [x2, #0x13]
    // 0xb4f5a8: r4 = Instance_MainAxisSize
    //     0xb4f5a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4f5ac: ldr             x4, [x4, #0xa10]
    // 0xb4f5b0: ArrayStore: r2[0] = r4  ; List_4
    //     0xb4f5b0: stur            w4, [x2, #0x17]
    // 0xb4f5b4: r5 = Instance_CrossAxisAlignment
    //     0xb4f5b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4f5b8: ldr             x5, [x5, #0xa18]
    // 0xb4f5bc: StoreField: r2->field_1b = r5
    //     0xb4f5bc: stur            w5, [x2, #0x1b]
    // 0xb4f5c0: r6 = Instance_VerticalDirection
    //     0xb4f5c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4f5c4: ldr             x6, [x6, #0xa20]
    // 0xb4f5c8: StoreField: r2->field_23 = r6
    //     0xb4f5c8: stur            w6, [x2, #0x23]
    // 0xb4f5cc: r7 = Instance_Clip
    //     0xb4f5cc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4f5d0: ldr             x7, [x7, #0x38]
    // 0xb4f5d4: StoreField: r2->field_2b = r7
    //     0xb4f5d4: stur            w7, [x2, #0x2b]
    // 0xb4f5d8: StoreField: r2->field_2f = rZR
    //     0xb4f5d8: stur            xzr, [x2, #0x2f]
    // 0xb4f5dc: ldur            x1, [fp, #-0x18]
    // 0xb4f5e0: StoreField: r2->field_b = r1
    //     0xb4f5e0: stur            w1, [x2, #0xb]
    // 0xb4f5e4: ldur            x8, [fp, #-8]
    // 0xb4f5e8: LoadField: r1 = r8->field_f
    //     0xb4f5e8: ldur            w1, [x8, #0xf]
    // 0xb4f5ec: DecompressPointer r1
    //     0xb4f5ec: add             x1, x1, HEAP, lsl #32
    // 0xb4f5f0: r0 = controller()
    //     0xb4f5f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4f5f4: LoadField: r1 = r0->field_57
    //     0xb4f5f4: ldur            w1, [x0, #0x57]
    // 0xb4f5f8: DecompressPointer r1
    //     0xb4f5f8: add             x1, x1, HEAP, lsl #32
    // 0xb4f5fc: r0 = value()
    //     0xb4f5fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4f600: cmp             w0, NULL
    // 0xb4f604: b.ne            #0xb4f610
    // 0xb4f608: r0 = Null
    //     0xb4f608: mov             x0, NULL
    // 0xb4f60c: b               #0xb4f64c
    // 0xb4f610: LoadField: r1 = r0->field_f
    //     0xb4f610: ldur            w1, [x0, #0xf]
    // 0xb4f614: DecompressPointer r1
    //     0xb4f614: add             x1, x1, HEAP, lsl #32
    // 0xb4f618: cmp             w1, NULL
    // 0xb4f61c: b.ne            #0xb4f628
    // 0xb4f620: r0 = Null
    //     0xb4f620: mov             x0, NULL
    // 0xb4f624: b               #0xb4f64c
    // 0xb4f628: LoadField: r0 = r1->field_1b
    //     0xb4f628: ldur            w0, [x1, #0x1b]
    // 0xb4f62c: DecompressPointer r0
    //     0xb4f62c: add             x0, x0, HEAP, lsl #32
    // 0xb4f630: cmp             w0, NULL
    // 0xb4f634: b.ne            #0xb4f640
    // 0xb4f638: r0 = Null
    //     0xb4f638: mov             x0, NULL
    // 0xb4f63c: b               #0xb4f64c
    // 0xb4f640: LoadField: r1 = r0->field_7
    //     0xb4f640: ldur            w1, [x0, #7]
    // 0xb4f644: DecompressPointer r1
    //     0xb4f644: add             x1, x1, HEAP, lsl #32
    // 0xb4f648: mov             x0, x1
    // 0xb4f64c: cmp             w0, NULL
    // 0xb4f650: b.ne            #0xb4f65c
    // 0xb4f654: r6 = ""
    //     0xb4f654: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4f658: b               #0xb4f660
    // 0xb4f65c: mov             x6, x0
    // 0xb4f660: ldur            x2, [fp, #-8]
    // 0xb4f664: ldur            x5, [fp, #-0x10]
    // 0xb4f668: ldur            x4, [fp, #-0x38]
    // 0xb4f66c: ldur            x3, [fp, #-0x28]
    // 0xb4f670: ldur            x0, [fp, #-0x20]
    // 0xb4f674: stur            x6, [fp, #-0x18]
    // 0xb4f678: LoadField: r1 = r2->field_13
    //     0xb4f678: ldur            w1, [x2, #0x13]
    // 0xb4f67c: DecompressPointer r1
    //     0xb4f67c: add             x1, x1, HEAP, lsl #32
    // 0xb4f680: r0 = of()
    //     0xb4f680: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4f684: LoadField: r1 = r0->field_87
    //     0xb4f684: ldur            w1, [x0, #0x87]
    // 0xb4f688: DecompressPointer r1
    //     0xb4f688: add             x1, x1, HEAP, lsl #32
    // 0xb4f68c: LoadField: r0 = r1->field_2b
    //     0xb4f68c: ldur            w0, [x1, #0x2b]
    // 0xb4f690: DecompressPointer r0
    //     0xb4f690: add             x0, x0, HEAP, lsl #32
    // 0xb4f694: stur            x0, [fp, #-0x30]
    // 0xb4f698: r1 = Instance_Color
    //     0xb4f698: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4f69c: d0 = 0.700000
    //     0xb4f69c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4f6a0: ldr             d0, [x17, #0xf48]
    // 0xb4f6a4: r0 = withOpacity()
    //     0xb4f6a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4f6a8: r16 = 12.000000
    //     0xb4f6a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4f6ac: ldr             x16, [x16, #0x9e8]
    // 0xb4f6b0: stp             x16, x0, [SP]
    // 0xb4f6b4: ldur            x1, [fp, #-0x30]
    // 0xb4f6b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4f6b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4f6bc: ldr             x4, [x4, #0x9b8]
    // 0xb4f6c0: r0 = copyWith()
    //     0xb4f6c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4f6c4: stur            x0, [fp, #-0x30]
    // 0xb4f6c8: r0 = Text()
    //     0xb4f6c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4f6cc: mov             x3, x0
    // 0xb4f6d0: ldur            x0, [fp, #-0x18]
    // 0xb4f6d4: stur            x3, [fp, #-0x40]
    // 0xb4f6d8: StoreField: r3->field_b = r0
    //     0xb4f6d8: stur            w0, [x3, #0xb]
    // 0xb4f6dc: ldur            x0, [fp, #-0x30]
    // 0xb4f6e0: StoreField: r3->field_13 = r0
    //     0xb4f6e0: stur            w0, [x3, #0x13]
    // 0xb4f6e4: r1 = Null
    //     0xb4f6e4: mov             x1, NULL
    // 0xb4f6e8: r2 = 8
    //     0xb4f6e8: movz            x2, #0x8
    // 0xb4f6ec: r0 = AllocateArray()
    //     0xb4f6ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f6f0: mov             x2, x0
    // 0xb4f6f4: ldur            x0, [fp, #-0x28]
    // 0xb4f6f8: stur            x2, [fp, #-0x18]
    // 0xb4f6fc: StoreField: r2->field_f = r0
    //     0xb4f6fc: stur            w0, [x2, #0xf]
    // 0xb4f700: ldur            x0, [fp, #-0x20]
    // 0xb4f704: StoreField: r2->field_13 = r0
    //     0xb4f704: stur            w0, [x2, #0x13]
    // 0xb4f708: r16 = Instance_SizedBox
    //     0xb4f708: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb4f70c: ldr             x16, [x16, #0xc70]
    // 0xb4f710: ArrayStore: r2[0] = r16  ; List_4
    //     0xb4f710: stur            w16, [x2, #0x17]
    // 0xb4f714: ldur            x0, [fp, #-0x40]
    // 0xb4f718: StoreField: r2->field_1b = r0
    //     0xb4f718: stur            w0, [x2, #0x1b]
    // 0xb4f71c: r1 = <Widget>
    //     0xb4f71c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4f720: r0 = AllocateGrowableArray()
    //     0xb4f720: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4f724: mov             x1, x0
    // 0xb4f728: ldur            x0, [fp, #-0x18]
    // 0xb4f72c: stur            x1, [fp, #-0x20]
    // 0xb4f730: StoreField: r1->field_f = r0
    //     0xb4f730: stur            w0, [x1, #0xf]
    // 0xb4f734: r2 = 8
    //     0xb4f734: movz            x2, #0x8
    // 0xb4f738: StoreField: r1->field_b = r2
    //     0xb4f738: stur            w2, [x1, #0xb]
    // 0xb4f73c: r0 = Column()
    //     0xb4f73c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4f740: mov             x2, x0
    // 0xb4f744: r0 = Instance_Axis
    //     0xb4f744: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4f748: stur            x2, [fp, #-0x18]
    // 0xb4f74c: StoreField: r2->field_f = r0
    //     0xb4f74c: stur            w0, [x2, #0xf]
    // 0xb4f750: r1 = Instance_MainAxisAlignment
    //     0xb4f750: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4f754: ldr             x1, [x1, #0xa8]
    // 0xb4f758: StoreField: r2->field_13 = r1
    //     0xb4f758: stur            w1, [x2, #0x13]
    // 0xb4f75c: r3 = Instance_MainAxisSize
    //     0xb4f75c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb4f760: ldr             x3, [x3, #0xdd0]
    // 0xb4f764: ArrayStore: r2[0] = r3  ; List_4
    //     0xb4f764: stur            w3, [x2, #0x17]
    // 0xb4f768: r4 = Instance_CrossAxisAlignment
    //     0xb4f768: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4f76c: ldr             x4, [x4, #0x890]
    // 0xb4f770: StoreField: r2->field_1b = r4
    //     0xb4f770: stur            w4, [x2, #0x1b]
    // 0xb4f774: r5 = Instance_VerticalDirection
    //     0xb4f774: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4f778: ldr             x5, [x5, #0xa20]
    // 0xb4f77c: StoreField: r2->field_23 = r5
    //     0xb4f77c: stur            w5, [x2, #0x23]
    // 0xb4f780: r6 = Instance_Clip
    //     0xb4f780: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4f784: ldr             x6, [x6, #0x38]
    // 0xb4f788: StoreField: r2->field_2b = r6
    //     0xb4f788: stur            w6, [x2, #0x2b]
    // 0xb4f78c: StoreField: r2->field_2f = rZR
    //     0xb4f78c: stur            xzr, [x2, #0x2f]
    // 0xb4f790: ldur            x1, [fp, #-0x20]
    // 0xb4f794: StoreField: r2->field_b = r1
    //     0xb4f794: stur            w1, [x2, #0xb]
    // 0xb4f798: r1 = <FlexParentData>
    //     0xb4f798: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4f79c: ldr             x1, [x1, #0xe00]
    // 0xb4f7a0: r0 = Expanded()
    //     0xb4f7a0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb4f7a4: mov             x3, x0
    // 0xb4f7a8: r0 = 1
    //     0xb4f7a8: movz            x0, #0x1
    // 0xb4f7ac: stur            x3, [fp, #-0x20]
    // 0xb4f7b0: StoreField: r3->field_13 = r0
    //     0xb4f7b0: stur            x0, [x3, #0x13]
    // 0xb4f7b4: r4 = Instance_FlexFit
    //     0xb4f7b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb4f7b8: ldr             x4, [x4, #0xe08]
    // 0xb4f7bc: StoreField: r3->field_1b = r4
    //     0xb4f7bc: stur            w4, [x3, #0x1b]
    // 0xb4f7c0: ldur            x1, [fp, #-0x18]
    // 0xb4f7c4: StoreField: r3->field_b = r1
    //     0xb4f7c4: stur            w1, [x3, #0xb]
    // 0xb4f7c8: r1 = Null
    //     0xb4f7c8: mov             x1, NULL
    // 0xb4f7cc: r2 = 6
    //     0xb4f7cc: movz            x2, #0x6
    // 0xb4f7d0: r0 = AllocateArray()
    //     0xb4f7d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f7d4: mov             x2, x0
    // 0xb4f7d8: ldur            x0, [fp, #-0x38]
    // 0xb4f7dc: stur            x2, [fp, #-0x18]
    // 0xb4f7e0: StoreField: r2->field_f = r0
    //     0xb4f7e0: stur            w0, [x2, #0xf]
    // 0xb4f7e4: r16 = Instance_SizedBox
    //     0xb4f7e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb4f7e8: ldr             x16, [x16, #0xb20]
    // 0xb4f7ec: StoreField: r2->field_13 = r16
    //     0xb4f7ec: stur            w16, [x2, #0x13]
    // 0xb4f7f0: ldur            x0, [fp, #-0x20]
    // 0xb4f7f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4f7f4: stur            w0, [x2, #0x17]
    // 0xb4f7f8: r1 = <Widget>
    //     0xb4f7f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4f7fc: r0 = AllocateGrowableArray()
    //     0xb4f7fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4f800: mov             x1, x0
    // 0xb4f804: ldur            x0, [fp, #-0x18]
    // 0xb4f808: stur            x1, [fp, #-0x20]
    // 0xb4f80c: StoreField: r1->field_f = r0
    //     0xb4f80c: stur            w0, [x1, #0xf]
    // 0xb4f810: r2 = 6
    //     0xb4f810: movz            x2, #0x6
    // 0xb4f814: StoreField: r1->field_b = r2
    //     0xb4f814: stur            w2, [x1, #0xb]
    // 0xb4f818: r0 = Row()
    //     0xb4f818: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4f81c: mov             x3, x0
    // 0xb4f820: r0 = Instance_Axis
    //     0xb4f820: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4f824: stur            x3, [fp, #-0x18]
    // 0xb4f828: StoreField: r3->field_f = r0
    //     0xb4f828: stur            w0, [x3, #0xf]
    // 0xb4f82c: r4 = Instance_MainAxisAlignment
    //     0xb4f82c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4f830: ldr             x4, [x4, #0xa08]
    // 0xb4f834: StoreField: r3->field_13 = r4
    //     0xb4f834: stur            w4, [x3, #0x13]
    // 0xb4f838: r5 = Instance_MainAxisSize
    //     0xb4f838: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4f83c: ldr             x5, [x5, #0xa10]
    // 0xb4f840: ArrayStore: r3[0] = r5  ; List_4
    //     0xb4f840: stur            w5, [x3, #0x17]
    // 0xb4f844: r6 = Instance_CrossAxisAlignment
    //     0xb4f844: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4f848: ldr             x6, [x6, #0xa18]
    // 0xb4f84c: StoreField: r3->field_1b = r6
    //     0xb4f84c: stur            w6, [x3, #0x1b]
    // 0xb4f850: r7 = Instance_VerticalDirection
    //     0xb4f850: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4f854: ldr             x7, [x7, #0xa20]
    // 0xb4f858: StoreField: r3->field_23 = r7
    //     0xb4f858: stur            w7, [x3, #0x23]
    // 0xb4f85c: r8 = Instance_Clip
    //     0xb4f85c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4f860: ldr             x8, [x8, #0x38]
    // 0xb4f864: StoreField: r3->field_2b = r8
    //     0xb4f864: stur            w8, [x3, #0x2b]
    // 0xb4f868: StoreField: r3->field_2f = rZR
    //     0xb4f868: stur            xzr, [x3, #0x2f]
    // 0xb4f86c: ldur            x1, [fp, #-0x20]
    // 0xb4f870: StoreField: r3->field_b = r1
    //     0xb4f870: stur            w1, [x3, #0xb]
    // 0xb4f874: r1 = Null
    //     0xb4f874: mov             x1, NULL
    // 0xb4f878: r2 = 2
    //     0xb4f878: movz            x2, #0x2
    // 0xb4f87c: r0 = AllocateArray()
    //     0xb4f87c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4f880: mov             x2, x0
    // 0xb4f884: ldur            x0, [fp, #-0x18]
    // 0xb4f888: stur            x2, [fp, #-0x20]
    // 0xb4f88c: StoreField: r2->field_f = r0
    //     0xb4f88c: stur            w0, [x2, #0xf]
    // 0xb4f890: r1 = <Widget>
    //     0xb4f890: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4f894: r0 = AllocateGrowableArray()
    //     0xb4f894: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4f898: mov             x1, x0
    // 0xb4f89c: ldur            x0, [fp, #-0x20]
    // 0xb4f8a0: stur            x1, [fp, #-0x18]
    // 0xb4f8a4: StoreField: r1->field_f = r0
    //     0xb4f8a4: stur            w0, [x1, #0xf]
    // 0xb4f8a8: r2 = 2
    //     0xb4f8a8: movz            x2, #0x2
    // 0xb4f8ac: StoreField: r1->field_b = r2
    //     0xb4f8ac: stur            w2, [x1, #0xb]
    // 0xb4f8b0: r0 = Column()
    //     0xb4f8b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4f8b4: mov             x1, x0
    // 0xb4f8b8: r0 = Instance_Axis
    //     0xb4f8b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4f8bc: stur            x1, [fp, #-0x20]
    // 0xb4f8c0: StoreField: r1->field_f = r0
    //     0xb4f8c0: stur            w0, [x1, #0xf]
    // 0xb4f8c4: r2 = Instance_MainAxisAlignment
    //     0xb4f8c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4f8c8: ldr             x2, [x2, #0xa08]
    // 0xb4f8cc: StoreField: r1->field_13 = r2
    //     0xb4f8cc: stur            w2, [x1, #0x13]
    // 0xb4f8d0: r3 = Instance_MainAxisSize
    //     0xb4f8d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4f8d4: ldr             x3, [x3, #0xa10]
    // 0xb4f8d8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb4f8d8: stur            w3, [x1, #0x17]
    // 0xb4f8dc: r4 = Instance_CrossAxisAlignment
    //     0xb4f8dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4f8e0: ldr             x4, [x4, #0xa18]
    // 0xb4f8e4: StoreField: r1->field_1b = r4
    //     0xb4f8e4: stur            w4, [x1, #0x1b]
    // 0xb4f8e8: r5 = Instance_VerticalDirection
    //     0xb4f8e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4f8ec: ldr             x5, [x5, #0xa20]
    // 0xb4f8f0: StoreField: r1->field_23 = r5
    //     0xb4f8f0: stur            w5, [x1, #0x23]
    // 0xb4f8f4: r6 = Instance_Clip
    //     0xb4f8f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4f8f8: ldr             x6, [x6, #0x38]
    // 0xb4f8fc: StoreField: r1->field_2b = r6
    //     0xb4f8fc: stur            w6, [x1, #0x2b]
    // 0xb4f900: StoreField: r1->field_2f = rZR
    //     0xb4f900: stur            xzr, [x1, #0x2f]
    // 0xb4f904: ldur            x7, [fp, #-0x18]
    // 0xb4f908: StoreField: r1->field_b = r7
    //     0xb4f908: stur            w7, [x1, #0xb]
    // 0xb4f90c: r0 = Padding()
    //     0xb4f90c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4f910: mov             x1, x0
    // 0xb4f914: r0 = Instance_EdgeInsets
    //     0xb4f914: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xb4f918: ldr             x0, [x0, #0xf98]
    // 0xb4f91c: stur            x1, [fp, #-0x18]
    // 0xb4f920: StoreField: r1->field_f = r0
    //     0xb4f920: stur            w0, [x1, #0xf]
    // 0xb4f924: ldur            x0, [fp, #-0x20]
    // 0xb4f928: StoreField: r1->field_b = r0
    //     0xb4f928: stur            w0, [x1, #0xb]
    // 0xb4f92c: r0 = Card()
    //     0xb4f92c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb4f930: mov             x1, x0
    // 0xb4f934: r0 = 0.000000
    //     0xb4f934: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb4f938: stur            x1, [fp, #-0x20]
    // 0xb4f93c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4f93c: stur            w0, [x1, #0x17]
    // 0xb4f940: ldur            x2, [fp, #-0x10]
    // 0xb4f944: StoreField: r1->field_1b = r2
    //     0xb4f944: stur            w2, [x1, #0x1b]
    // 0xb4f948: r2 = true
    //     0xb4f948: add             x2, NULL, #0x20  ; true
    // 0xb4f94c: StoreField: r1->field_1f = r2
    //     0xb4f94c: stur            w2, [x1, #0x1f]
    // 0xb4f950: ldur            x3, [fp, #-0x18]
    // 0xb4f954: StoreField: r1->field_2f = r3
    //     0xb4f954: stur            w3, [x1, #0x2f]
    // 0xb4f958: StoreField: r1->field_2b = r2
    //     0xb4f958: stur            w2, [x1, #0x2b]
    // 0xb4f95c: r3 = Instance__CardVariant
    //     0xb4f95c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb4f960: ldr             x3, [x3, #0xa68]
    // 0xb4f964: StoreField: r1->field_33 = r3
    //     0xb4f964: stur            w3, [x1, #0x33]
    // 0xb4f968: r0 = Radius()
    //     0xb4f968: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4f96c: d0 = 20.000000
    //     0xb4f96c: fmov            d0, #20.00000000
    // 0xb4f970: stur            x0, [fp, #-0x10]
    // 0xb4f974: StoreField: r0->field_7 = d0
    //     0xb4f974: stur            d0, [x0, #7]
    // 0xb4f978: StoreField: r0->field_f = d0
    //     0xb4f978: stur            d0, [x0, #0xf]
    // 0xb4f97c: r0 = BorderRadius()
    //     0xb4f97c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4f980: mov             x1, x0
    // 0xb4f984: ldur            x0, [fp, #-0x10]
    // 0xb4f988: stur            x1, [fp, #-0x18]
    // 0xb4f98c: StoreField: r1->field_7 = r0
    //     0xb4f98c: stur            w0, [x1, #7]
    // 0xb4f990: StoreField: r1->field_b = r0
    //     0xb4f990: stur            w0, [x1, #0xb]
    // 0xb4f994: StoreField: r1->field_f = r0
    //     0xb4f994: stur            w0, [x1, #0xf]
    // 0xb4f998: StoreField: r1->field_13 = r0
    //     0xb4f998: stur            w0, [x1, #0x13]
    // 0xb4f99c: r0 = RoundedRectangleBorder()
    //     0xb4f99c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb4f9a0: mov             x2, x0
    // 0xb4f9a4: ldur            x0, [fp, #-0x18]
    // 0xb4f9a8: stur            x2, [fp, #-0x10]
    // 0xb4f9ac: StoreField: r2->field_b = r0
    //     0xb4f9ac: stur            w0, [x2, #0xb]
    // 0xb4f9b0: r0 = Instance_BorderSide
    //     0xb4f9b0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb4f9b4: ldr             x0, [x0, #0xe20]
    // 0xb4f9b8: StoreField: r2->field_7 = r0
    //     0xb4f9b8: stur            w0, [x2, #7]
    // 0xb4f9bc: ldur            x3, [fp, #-8]
    // 0xb4f9c0: LoadField: r1 = r3->field_13
    //     0xb4f9c0: ldur            w1, [x3, #0x13]
    // 0xb4f9c4: DecompressPointer r1
    //     0xb4f9c4: add             x1, x1, HEAP, lsl #32
    // 0xb4f9c8: r0 = of()
    //     0xb4f9c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4f9cc: LoadField: r1 = r0->field_87
    //     0xb4f9cc: ldur            w1, [x0, #0x87]
    // 0xb4f9d0: DecompressPointer r1
    //     0xb4f9d0: add             x1, x1, HEAP, lsl #32
    // 0xb4f9d4: LoadField: r0 = r1->field_7
    //     0xb4f9d4: ldur            w0, [x1, #7]
    // 0xb4f9d8: DecompressPointer r0
    //     0xb4f9d8: add             x0, x0, HEAP, lsl #32
    // 0xb4f9dc: r16 = 16.000000
    //     0xb4f9dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4f9e0: ldr             x16, [x16, #0x188]
    // 0xb4f9e4: r30 = Instance_Color
    //     0xb4f9e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4f9e8: stp             lr, x16, [SP]
    // 0xb4f9ec: mov             x1, x0
    // 0xb4f9f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4f9f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4f9f4: ldr             x4, [x4, #0xaa0]
    // 0xb4f9f8: r0 = copyWith()
    //     0xb4f9f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4f9fc: stur            x0, [fp, #-0x18]
    // 0xb4fa00: r0 = Text()
    //     0xb4fa00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4fa04: mov             x1, x0
    // 0xb4fa08: r0 = "What is the Issue\?"
    //     0xb4fa08: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa0] "What is the Issue\?"
    //     0xb4fa0c: ldr             x0, [x0, #0xfa0]
    // 0xb4fa10: stur            x1, [fp, #-0x28]
    // 0xb4fa14: StoreField: r1->field_b = r0
    //     0xb4fa14: stur            w0, [x1, #0xb]
    // 0xb4fa18: ldur            x0, [fp, #-0x18]
    // 0xb4fa1c: StoreField: r1->field_13 = r0
    //     0xb4fa1c: stur            w0, [x1, #0x13]
    // 0xb4fa20: r0 = Padding()
    //     0xb4fa20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4fa24: mov             x2, x0
    // 0xb4fa28: r0 = Instance_EdgeInsets
    //     0xb4fa28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb4fa2c: ldr             x0, [x0, #0x980]
    // 0xb4fa30: stur            x2, [fp, #-0x18]
    // 0xb4fa34: StoreField: r2->field_f = r0
    //     0xb4fa34: stur            w0, [x2, #0xf]
    // 0xb4fa38: ldur            x1, [fp, #-0x28]
    // 0xb4fa3c: StoreField: r2->field_b = r1
    //     0xb4fa3c: stur            w1, [x2, #0xb]
    // 0xb4fa40: ldur            x3, [fp, #-8]
    // 0xb4fa44: LoadField: r1 = r3->field_13
    //     0xb4fa44: ldur            w1, [x3, #0x13]
    // 0xb4fa48: DecompressPointer r1
    //     0xb4fa48: add             x1, x1, HEAP, lsl #32
    // 0xb4fa4c: r0 = of()
    //     0xb4fa4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4fa50: LoadField: r1 = r0->field_87
    //     0xb4fa50: ldur            w1, [x0, #0x87]
    // 0xb4fa54: DecompressPointer r1
    //     0xb4fa54: add             x1, x1, HEAP, lsl #32
    // 0xb4fa58: LoadField: r0 = r1->field_2b
    //     0xb4fa58: ldur            w0, [x1, #0x2b]
    // 0xb4fa5c: DecompressPointer r0
    //     0xb4fa5c: add             x0, x0, HEAP, lsl #32
    // 0xb4fa60: stur            x0, [fp, #-0x28]
    // 0xb4fa64: r1 = Instance_Color
    //     0xb4fa64: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4fa68: d0 = 0.400000
    //     0xb4fa68: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4fa6c: r0 = withOpacity()
    //     0xb4fa6c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4fa70: r16 = 12.000000
    //     0xb4fa70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4fa74: ldr             x16, [x16, #0x9e8]
    // 0xb4fa78: stp             x0, x16, [SP]
    // 0xb4fa7c: ldur            x1, [fp, #-0x28]
    // 0xb4fa80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4fa80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4fa84: ldr             x4, [x4, #0xaa0]
    // 0xb4fa88: r0 = copyWith()
    //     0xb4fa88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4fa8c: stur            x0, [fp, #-0x28]
    // 0xb4fa90: r0 = Text()
    //     0xb4fa90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4fa94: mov             x1, x0
    // 0xb4fa98: r0 = "Please choose the correct issue for return/replace."
    //     0xb4fa98: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa8] "Please choose the correct issue for return/replace."
    //     0xb4fa9c: ldr             x0, [x0, #0xfa8]
    // 0xb4faa0: stur            x1, [fp, #-0x30]
    // 0xb4faa4: StoreField: r1->field_b = r0
    //     0xb4faa4: stur            w0, [x1, #0xb]
    // 0xb4faa8: ldur            x0, [fp, #-0x28]
    // 0xb4faac: StoreField: r1->field_13 = r0
    //     0xb4faac: stur            w0, [x1, #0x13]
    // 0xb4fab0: r0 = Padding()
    //     0xb4fab0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4fab4: mov             x2, x0
    // 0xb4fab8: r0 = Instance_EdgeInsets
    //     0xb4fab8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb4fabc: ldr             x0, [x0, #0x980]
    // 0xb4fac0: stur            x2, [fp, #-0x28]
    // 0xb4fac4: StoreField: r2->field_f = r0
    //     0xb4fac4: stur            w0, [x2, #0xf]
    // 0xb4fac8: ldur            x1, [fp, #-0x30]
    // 0xb4facc: StoreField: r2->field_b = r1
    //     0xb4facc: stur            w1, [x2, #0xb]
    // 0xb4fad0: ldur            x3, [fp, #-8]
    // 0xb4fad4: LoadField: r1 = r3->field_13
    //     0xb4fad4: ldur            w1, [x3, #0x13]
    // 0xb4fad8: DecompressPointer r1
    //     0xb4fad8: add             x1, x1, HEAP, lsl #32
    // 0xb4fadc: r0 = of()
    //     0xb4fadc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4fae0: LoadField: r1 = r0->field_87
    //     0xb4fae0: ldur            w1, [x0, #0x87]
    // 0xb4fae4: DecompressPointer r1
    //     0xb4fae4: add             x1, x1, HEAP, lsl #32
    // 0xb4fae8: LoadField: r0 = r1->field_7
    //     0xb4fae8: ldur            w0, [x1, #7]
    // 0xb4faec: DecompressPointer r0
    //     0xb4faec: add             x0, x0, HEAP, lsl #32
    // 0xb4faf0: r16 = 12.000000
    //     0xb4faf0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4faf4: ldr             x16, [x16, #0x9e8]
    // 0xb4faf8: r30 = Instance_Color
    //     0xb4faf8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4fafc: stp             lr, x16, [SP, #8]
    // 0xb4fb00: r16 = const [Instance of 'FontFeature']
    //     0xb4fb00: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fb0] List<FontFeature>(1)
    //     0xb4fb04: ldr             x16, [x16, #0xfb0]
    // 0xb4fb08: str             x16, [SP]
    // 0xb4fb0c: mov             x1, x0
    // 0xb4fb10: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontFeatures, 0x3, fontSize, 0x1, null]
    //     0xb4fb10: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fb8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontFeatures", 0x3, "fontSize", 0x1, Null]
    //     0xb4fb14: ldr             x4, [x4, #0xfb8]
    // 0xb4fb18: r0 = copyWith()
    //     0xb4fb18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4fb1c: stur            x0, [fp, #-0x30]
    // 0xb4fb20: r0 = TextSpan()
    //     0xb4fb20: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb4fb24: mov             x2, x0
    // 0xb4fb28: r0 = "Select Reason"
    //     0xb4fb28: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc0] "Select Reason"
    //     0xb4fb2c: ldr             x0, [x0, #0xfc0]
    // 0xb4fb30: stur            x2, [fp, #-0x38]
    // 0xb4fb34: StoreField: r2->field_b = r0
    //     0xb4fb34: stur            w0, [x2, #0xb]
    // 0xb4fb38: r0 = Instance__DeferringMouseCursor
    //     0xb4fb38: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb4fb3c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4fb3c: stur            w0, [x2, #0x17]
    // 0xb4fb40: ldur            x1, [fp, #-0x30]
    // 0xb4fb44: StoreField: r2->field_7 = r1
    //     0xb4fb44: stur            w1, [x2, #7]
    // 0xb4fb48: ldur            x3, [fp, #-8]
    // 0xb4fb4c: LoadField: r1 = r3->field_13
    //     0xb4fb4c: ldur            w1, [x3, #0x13]
    // 0xb4fb50: DecompressPointer r1
    //     0xb4fb50: add             x1, x1, HEAP, lsl #32
    // 0xb4fb54: r0 = of()
    //     0xb4fb54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4fb58: LoadField: r1 = r0->field_87
    //     0xb4fb58: ldur            w1, [x0, #0x87]
    // 0xb4fb5c: DecompressPointer r1
    //     0xb4fb5c: add             x1, x1, HEAP, lsl #32
    // 0xb4fb60: LoadField: r0 = r1->field_2b
    //     0xb4fb60: ldur            w0, [x1, #0x2b]
    // 0xb4fb64: DecompressPointer r0
    //     0xb4fb64: add             x0, x0, HEAP, lsl #32
    // 0xb4fb68: r16 = Instance_MaterialColor
    //     0xb4fb68: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb4fb6c: ldr             x16, [x16, #0x180]
    // 0xb4fb70: str             x16, [SP]
    // 0xb4fb74: mov             x1, x0
    // 0xb4fb78: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb4fb78: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb4fb7c: ldr             x4, [x4, #0xf40]
    // 0xb4fb80: r0 = copyWith()
    //     0xb4fb80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4fb84: stur            x0, [fp, #-0x30]
    // 0xb4fb88: r0 = TextSpan()
    //     0xb4fb88: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb4fb8c: mov             x3, x0
    // 0xb4fb90: r0 = " *"
    //     0xb4fb90: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xb4fb94: ldr             x0, [x0, #0xfc8]
    // 0xb4fb98: stur            x3, [fp, #-0x40]
    // 0xb4fb9c: StoreField: r3->field_b = r0
    //     0xb4fb9c: stur            w0, [x3, #0xb]
    // 0xb4fba0: r0 = Instance__DeferringMouseCursor
    //     0xb4fba0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb4fba4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb4fba4: stur            w0, [x3, #0x17]
    // 0xb4fba8: ldur            x1, [fp, #-0x30]
    // 0xb4fbac: StoreField: r3->field_7 = r1
    //     0xb4fbac: stur            w1, [x3, #7]
    // 0xb4fbb0: r1 = Null
    //     0xb4fbb0: mov             x1, NULL
    // 0xb4fbb4: r2 = 4
    //     0xb4fbb4: movz            x2, #0x4
    // 0xb4fbb8: r0 = AllocateArray()
    //     0xb4fbb8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4fbbc: mov             x2, x0
    // 0xb4fbc0: ldur            x0, [fp, #-0x38]
    // 0xb4fbc4: stur            x2, [fp, #-0x30]
    // 0xb4fbc8: StoreField: r2->field_f = r0
    //     0xb4fbc8: stur            w0, [x2, #0xf]
    // 0xb4fbcc: ldur            x0, [fp, #-0x40]
    // 0xb4fbd0: StoreField: r2->field_13 = r0
    //     0xb4fbd0: stur            w0, [x2, #0x13]
    // 0xb4fbd4: r1 = <InlineSpan>
    //     0xb4fbd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb4fbd8: ldr             x1, [x1, #0xe40]
    // 0xb4fbdc: r0 = AllocateGrowableArray()
    //     0xb4fbdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4fbe0: mov             x1, x0
    // 0xb4fbe4: ldur            x0, [fp, #-0x30]
    // 0xb4fbe8: stur            x1, [fp, #-0x38]
    // 0xb4fbec: StoreField: r1->field_f = r0
    //     0xb4fbec: stur            w0, [x1, #0xf]
    // 0xb4fbf0: r2 = 4
    //     0xb4fbf0: movz            x2, #0x4
    // 0xb4fbf4: StoreField: r1->field_b = r2
    //     0xb4fbf4: stur            w2, [x1, #0xb]
    // 0xb4fbf8: r0 = TextSpan()
    //     0xb4fbf8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb4fbfc: mov             x1, x0
    // 0xb4fc00: ldur            x0, [fp, #-0x38]
    // 0xb4fc04: stur            x1, [fp, #-0x30]
    // 0xb4fc08: StoreField: r1->field_f = r0
    //     0xb4fc08: stur            w0, [x1, #0xf]
    // 0xb4fc0c: r0 = Instance__DeferringMouseCursor
    //     0xb4fc0c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb4fc10: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4fc10: stur            w0, [x1, #0x17]
    // 0xb4fc14: r0 = RichText()
    //     0xb4fc14: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb4fc18: mov             x1, x0
    // 0xb4fc1c: ldur            x2, [fp, #-0x30]
    // 0xb4fc20: stur            x0, [fp, #-0x30]
    // 0xb4fc24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb4fc24: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb4fc28: r0 = RichText()
    //     0xb4fc28: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb4fc2c: r0 = Padding()
    //     0xb4fc2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4fc30: mov             x2, x0
    // 0xb4fc34: r0 = Instance_EdgeInsets
    //     0xb4fc34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb4fc38: ldr             x0, [x0, #0x980]
    // 0xb4fc3c: stur            x2, [fp, #-0x38]
    // 0xb4fc40: StoreField: r2->field_f = r0
    //     0xb4fc40: stur            w0, [x2, #0xf]
    // 0xb4fc44: ldur            x1, [fp, #-0x30]
    // 0xb4fc48: StoreField: r2->field_b = r1
    //     0xb4fc48: stur            w1, [x2, #0xb]
    // 0xb4fc4c: ldur            x3, [fp, #-8]
    // 0xb4fc50: LoadField: r1 = r3->field_13
    //     0xb4fc50: ldur            w1, [x3, #0x13]
    // 0xb4fc54: DecompressPointer r1
    //     0xb4fc54: add             x1, x1, HEAP, lsl #32
    // 0xb4fc58: r0 = of()
    //     0xb4fc58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4fc5c: LoadField: r1 = r0->field_5b
    //     0xb4fc5c: ldur            w1, [x0, #0x5b]
    // 0xb4fc60: DecompressPointer r1
    //     0xb4fc60: add             x1, x1, HEAP, lsl #32
    // 0xb4fc64: r0 = LoadClassIdInstr(r1)
    //     0xb4fc64: ldur            x0, [x1, #-1]
    //     0xb4fc68: ubfx            x0, x0, #0xc, #0x14
    // 0xb4fc6c: d0 = 0.100000
    //     0xb4fc6c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4fc70: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4fc70: sub             lr, x0, #0xffa
    //     0xb4fc74: ldr             lr, [x21, lr, lsl #3]
    //     0xb4fc78: blr             lr
    // 0xb4fc7c: r16 = 1.000000
    //     0xb4fc7c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb4fc80: str             x16, [SP]
    // 0xb4fc84: mov             x2, x0
    // 0xb4fc88: r1 = Null
    //     0xb4fc88: mov             x1, NULL
    // 0xb4fc8c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb4fc8c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb4fc90: ldr             x4, [x4, #0x108]
    // 0xb4fc94: r0 = Border.all()
    //     0xb4fc94: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb4fc98: stur            x0, [fp, #-0x30]
    // 0xb4fc9c: r0 = Radius()
    //     0xb4fc9c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4fca0: d0 = 20.000000
    //     0xb4fca0: fmov            d0, #20.00000000
    // 0xb4fca4: stur            x0, [fp, #-0x40]
    // 0xb4fca8: StoreField: r0->field_7 = d0
    //     0xb4fca8: stur            d0, [x0, #7]
    // 0xb4fcac: StoreField: r0->field_f = d0
    //     0xb4fcac: stur            d0, [x0, #0xf]
    // 0xb4fcb0: r0 = BorderRadius()
    //     0xb4fcb0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4fcb4: mov             x1, x0
    // 0xb4fcb8: ldur            x0, [fp, #-0x40]
    // 0xb4fcbc: stur            x1, [fp, #-0x48]
    // 0xb4fcc0: StoreField: r1->field_7 = r0
    //     0xb4fcc0: stur            w0, [x1, #7]
    // 0xb4fcc4: StoreField: r1->field_b = r0
    //     0xb4fcc4: stur            w0, [x1, #0xb]
    // 0xb4fcc8: StoreField: r1->field_f = r0
    //     0xb4fcc8: stur            w0, [x1, #0xf]
    // 0xb4fccc: StoreField: r1->field_13 = r0
    //     0xb4fccc: stur            w0, [x1, #0x13]
    // 0xb4fcd0: r0 = BoxDecoration()
    //     0xb4fcd0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4fcd4: mov             x2, x0
    // 0xb4fcd8: ldur            x0, [fp, #-0x30]
    // 0xb4fcdc: stur            x2, [fp, #-0x40]
    // 0xb4fce0: StoreField: r2->field_f = r0
    //     0xb4fce0: stur            w0, [x2, #0xf]
    // 0xb4fce4: ldur            x0, [fp, #-0x48]
    // 0xb4fce8: StoreField: r2->field_13 = r0
    //     0xb4fce8: stur            w0, [x2, #0x13]
    // 0xb4fcec: r0 = Instance_BoxShape
    //     0xb4fcec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4fcf0: ldr             x0, [x0, #0x80]
    // 0xb4fcf4: StoreField: r2->field_23 = r0
    //     0xb4fcf4: stur            w0, [x2, #0x23]
    // 0xb4fcf8: ldur            x0, [fp, #-8]
    // 0xb4fcfc: LoadField: r1 = r0->field_f
    //     0xb4fcfc: ldur            w1, [x0, #0xf]
    // 0xb4fd00: DecompressPointer r1
    //     0xb4fd00: add             x1, x1, HEAP, lsl #32
    // 0xb4fd04: r0 = controller()
    //     0xb4fd04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4fd08: LoadField: r1 = r0->field_57
    //     0xb4fd08: ldur            w1, [x0, #0x57]
    // 0xb4fd0c: DecompressPointer r1
    //     0xb4fd0c: add             x1, x1, HEAP, lsl #32
    // 0xb4fd10: r0 = value()
    //     0xb4fd10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4fd14: cmp             w0, NULL
    // 0xb4fd18: b.ne            #0xb4fd24
    // 0xb4fd1c: r0 = Null
    //     0xb4fd1c: mov             x0, NULL
    // 0xb4fd20: b               #0xb4fd30
    // 0xb4fd24: LoadField: r1 = r0->field_7
    //     0xb4fd24: ldur            w1, [x0, #7]
    // 0xb4fd28: DecompressPointer r1
    //     0xb4fd28: add             x1, x1, HEAP, lsl #32
    // 0xb4fd2c: LoadField: r0 = r1->field_b
    //     0xb4fd2c: ldur            w0, [x1, #0xb]
    // 0xb4fd30: cmp             w0, NULL
    // 0xb4fd34: b.ne            #0xb4fd40
    // 0xb4fd38: r8 = 0
    //     0xb4fd38: movz            x8, #0
    // 0xb4fd3c: b               #0xb4fd48
    // 0xb4fd40: r1 = LoadInt32Instr(r0)
    //     0xb4fd40: sbfx            x1, x0, #1, #0x1f
    // 0xb4fd44: mov             x8, x1
    // 0xb4fd48: ldur            x0, [fp, #-8]
    // 0xb4fd4c: ldur            x7, [fp, #-0x20]
    // 0xb4fd50: ldur            x6, [fp, #-0x10]
    // 0xb4fd54: ldur            x5, [fp, #-0x18]
    // 0xb4fd58: ldur            x4, [fp, #-0x28]
    // 0xb4fd5c: ldur            x3, [fp, #-0x38]
    // 0xb4fd60: stur            x8, [fp, #-0x50]
    // 0xb4fd64: r1 = Function '<anonymous closure>':.
    //     0xb4fd64: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f430] AnonymousClosure: (0xb4eeb8), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb4fd68: ldr             x1, [x1, #0x430]
    // 0xb4fd6c: r2 = Null
    //     0xb4fd6c: mov             x2, NULL
    // 0xb4fd70: r0 = AllocateClosure()
    //     0xb4fd70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4fd74: ldur            x2, [fp, #-8]
    // 0xb4fd78: r1 = Function '<anonymous closure>':.
    //     0xb4fd78: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f438] AnonymousClosure: (0xb511e0), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb4fd7c: ldr             x1, [x1, #0x438]
    // 0xb4fd80: stur            x0, [fp, #-0x30]
    // 0xb4fd84: r0 = AllocateClosure()
    //     0xb4fd84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4fd88: stur            x0, [fp, #-0x48]
    // 0xb4fd8c: r0 = ListView()
    //     0xb4fd8c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb4fd90: stur            x0, [fp, #-0x58]
    // 0xb4fd94: r16 = true
    //     0xb4fd94: add             x16, NULL, #0x20  ; true
    // 0xb4fd98: r30 = false
    //     0xb4fd98: add             lr, NULL, #0x30  ; false
    // 0xb4fd9c: stp             lr, x16, [SP, #8]
    // 0xb4fda0: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb4fda0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb4fda4: ldr             x16, [x16, #0x1c8]
    // 0xb4fda8: str             x16, [SP]
    // 0xb4fdac: mov             x1, x0
    // 0xb4fdb0: ldur            x2, [fp, #-0x48]
    // 0xb4fdb4: ldur            x3, [fp, #-0x50]
    // 0xb4fdb8: ldur            x5, [fp, #-0x30]
    // 0xb4fdbc: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xb4fdbc: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb4fdc0: ldr             x4, [x4, #0x138]
    // 0xb4fdc4: r0 = ListView.separated()
    //     0xb4fdc4: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb4fdc8: r0 = Container()
    //     0xb4fdc8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4fdcc: stur            x0, [fp, #-0x30]
    // 0xb4fdd0: ldur            x16, [fp, #-0x40]
    // 0xb4fdd4: ldur            lr, [fp, #-0x58]
    // 0xb4fdd8: stp             lr, x16, [SP]
    // 0xb4fddc: mov             x1, x0
    // 0xb4fde0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb4fde0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4fde4: ldr             x4, [x4, #0x88]
    // 0xb4fde8: r0 = Container()
    //     0xb4fde8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4fdec: r1 = Null
    //     0xb4fdec: mov             x1, NULL
    // 0xb4fdf0: r2 = 8
    //     0xb4fdf0: movz            x2, #0x8
    // 0xb4fdf4: r0 = AllocateArray()
    //     0xb4fdf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4fdf8: mov             x2, x0
    // 0xb4fdfc: ldur            x0, [fp, #-0x18]
    // 0xb4fe00: stur            x2, [fp, #-0x40]
    // 0xb4fe04: StoreField: r2->field_f = r0
    //     0xb4fe04: stur            w0, [x2, #0xf]
    // 0xb4fe08: ldur            x0, [fp, #-0x28]
    // 0xb4fe0c: StoreField: r2->field_13 = r0
    //     0xb4fe0c: stur            w0, [x2, #0x13]
    // 0xb4fe10: ldur            x0, [fp, #-0x38]
    // 0xb4fe14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4fe14: stur            w0, [x2, #0x17]
    // 0xb4fe18: ldur            x0, [fp, #-0x30]
    // 0xb4fe1c: StoreField: r2->field_1b = r0
    //     0xb4fe1c: stur            w0, [x2, #0x1b]
    // 0xb4fe20: r1 = <Widget>
    //     0xb4fe20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4fe24: r0 = AllocateGrowableArray()
    //     0xb4fe24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4fe28: mov             x1, x0
    // 0xb4fe2c: ldur            x0, [fp, #-0x40]
    // 0xb4fe30: stur            x1, [fp, #-0x18]
    // 0xb4fe34: StoreField: r1->field_f = r0
    //     0xb4fe34: stur            w0, [x1, #0xf]
    // 0xb4fe38: r2 = 8
    //     0xb4fe38: movz            x2, #0x8
    // 0xb4fe3c: StoreField: r1->field_b = r2
    //     0xb4fe3c: stur            w2, [x1, #0xb]
    // 0xb4fe40: r0 = Column()
    //     0xb4fe40: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4fe44: mov             x1, x0
    // 0xb4fe48: r0 = Instance_Axis
    //     0xb4fe48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4fe4c: stur            x1, [fp, #-0x28]
    // 0xb4fe50: StoreField: r1->field_f = r0
    //     0xb4fe50: stur            w0, [x1, #0xf]
    // 0xb4fe54: r2 = Instance_MainAxisAlignment
    //     0xb4fe54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4fe58: ldr             x2, [x2, #0xa08]
    // 0xb4fe5c: StoreField: r1->field_13 = r2
    //     0xb4fe5c: stur            w2, [x1, #0x13]
    // 0xb4fe60: r3 = Instance_MainAxisSize
    //     0xb4fe60: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb4fe64: ldr             x3, [x3, #0xdd0]
    // 0xb4fe68: ArrayStore: r1[0] = r3  ; List_4
    //     0xb4fe68: stur            w3, [x1, #0x17]
    // 0xb4fe6c: r4 = Instance_CrossAxisAlignment
    //     0xb4fe6c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4fe70: ldr             x4, [x4, #0x890]
    // 0xb4fe74: StoreField: r1->field_1b = r4
    //     0xb4fe74: stur            w4, [x1, #0x1b]
    // 0xb4fe78: r5 = Instance_VerticalDirection
    //     0xb4fe78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4fe7c: ldr             x5, [x5, #0xa20]
    // 0xb4fe80: StoreField: r1->field_23 = r5
    //     0xb4fe80: stur            w5, [x1, #0x23]
    // 0xb4fe84: r6 = Instance_Clip
    //     0xb4fe84: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4fe88: ldr             x6, [x6, #0x38]
    // 0xb4fe8c: StoreField: r1->field_2b = r6
    //     0xb4fe8c: stur            w6, [x1, #0x2b]
    // 0xb4fe90: StoreField: r1->field_2f = rZR
    //     0xb4fe90: stur            xzr, [x1, #0x2f]
    // 0xb4fe94: ldur            x7, [fp, #-0x18]
    // 0xb4fe98: StoreField: r1->field_b = r7
    //     0xb4fe98: stur            w7, [x1, #0xb]
    // 0xb4fe9c: r0 = Padding()
    //     0xb4fe9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4fea0: mov             x1, x0
    // 0xb4fea4: r0 = Instance_EdgeInsets
    //     0xb4fea4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4fea8: ldr             x0, [x0, #0x1f0]
    // 0xb4feac: stur            x1, [fp, #-0x18]
    // 0xb4feb0: StoreField: r1->field_f = r0
    //     0xb4feb0: stur            w0, [x1, #0xf]
    // 0xb4feb4: ldur            x2, [fp, #-0x28]
    // 0xb4feb8: StoreField: r1->field_b = r2
    //     0xb4feb8: stur            w2, [x1, #0xb]
    // 0xb4febc: r0 = Card()
    //     0xb4febc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb4fec0: mov             x3, x0
    // 0xb4fec4: r0 = 0.000000
    //     0xb4fec4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb4fec8: stur            x3, [fp, #-0x28]
    // 0xb4fecc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb4fecc: stur            w0, [x3, #0x17]
    // 0xb4fed0: ldur            x1, [fp, #-0x10]
    // 0xb4fed4: StoreField: r3->field_1b = r1
    //     0xb4fed4: stur            w1, [x3, #0x1b]
    // 0xb4fed8: r4 = true
    //     0xb4fed8: add             x4, NULL, #0x20  ; true
    // 0xb4fedc: StoreField: r3->field_1f = r4
    //     0xb4fedc: stur            w4, [x3, #0x1f]
    // 0xb4fee0: ldur            x1, [fp, #-0x18]
    // 0xb4fee4: StoreField: r3->field_2f = r1
    //     0xb4fee4: stur            w1, [x3, #0x2f]
    // 0xb4fee8: StoreField: r3->field_2b = r4
    //     0xb4fee8: stur            w4, [x3, #0x2b]
    // 0xb4feec: r5 = Instance__CardVariant
    //     0xb4feec: add             x5, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb4fef0: ldr             x5, [x5, #0xa68]
    // 0xb4fef4: StoreField: r3->field_33 = r5
    //     0xb4fef4: stur            w5, [x3, #0x33]
    // 0xb4fef8: r1 = Null
    //     0xb4fef8: mov             x1, NULL
    // 0xb4fefc: r2 = 6
    //     0xb4fefc: movz            x2, #0x6
    // 0xb4ff00: r0 = AllocateArray()
    //     0xb4ff00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4ff04: mov             x2, x0
    // 0xb4ff08: ldur            x0, [fp, #-0x20]
    // 0xb4ff0c: stur            x2, [fp, #-0x10]
    // 0xb4ff10: StoreField: r2->field_f = r0
    //     0xb4ff10: stur            w0, [x2, #0xf]
    // 0xb4ff14: r16 = Instance_SizedBox
    //     0xb4ff14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb4ff18: ldr             x16, [x16, #0x8b8]
    // 0xb4ff1c: StoreField: r2->field_13 = r16
    //     0xb4ff1c: stur            w16, [x2, #0x13]
    // 0xb4ff20: ldur            x0, [fp, #-0x28]
    // 0xb4ff24: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4ff24: stur            w0, [x2, #0x17]
    // 0xb4ff28: r1 = <Widget>
    //     0xb4ff28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4ff2c: r0 = AllocateGrowableArray()
    //     0xb4ff2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4ff30: mov             x2, x0
    // 0xb4ff34: ldur            x0, [fp, #-0x10]
    // 0xb4ff38: stur            x2, [fp, #-0x18]
    // 0xb4ff3c: StoreField: r2->field_f = r0
    //     0xb4ff3c: stur            w0, [x2, #0xf]
    // 0xb4ff40: r0 = 6
    //     0xb4ff40: movz            x0, #0x6
    // 0xb4ff44: StoreField: r2->field_b = r0
    //     0xb4ff44: stur            w0, [x2, #0xb]
    // 0xb4ff48: ldur            x3, [fp, #-8]
    // 0xb4ff4c: LoadField: r1 = r3->field_f
    //     0xb4ff4c: ldur            w1, [x3, #0xf]
    // 0xb4ff50: DecompressPointer r1
    //     0xb4ff50: add             x1, x1, HEAP, lsl #32
    // 0xb4ff54: r0 = controller()
    //     0xb4ff54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4ff58: LoadField: r1 = r0->field_67
    //     0xb4ff58: ldur            w1, [x0, #0x67]
    // 0xb4ff5c: DecompressPointer r1
    //     0xb4ff5c: add             x1, x1, HEAP, lsl #32
    // 0xb4ff60: r0 = value()
    //     0xb4ff60: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4ff64: r1 = LoadClassIdInstr(r0)
    //     0xb4ff64: ldur            x1, [x0, #-1]
    //     0xb4ff68: ubfx            x1, x1, #0xc, #0x14
    // 0xb4ff6c: r16 = "OTHER"
    //     0xb4ff6c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f68] "OTHER"
    //     0xb4ff70: ldr             x16, [x16, #0xf68]
    // 0xb4ff74: stp             x16, x0, [SP]
    // 0xb4ff78: mov             x0, x1
    // 0xb4ff7c: mov             lr, x0
    // 0xb4ff80: ldr             lr, [x21, lr, lsl #3]
    // 0xb4ff84: blr             lr
    // 0xb4ff88: tbz             w0, #4, #0xb4ffd8
    // 0xb4ff8c: ldur            x2, [fp, #-8]
    // 0xb4ff90: LoadField: r1 = r2->field_f
    //     0xb4ff90: ldur            w1, [x2, #0xf]
    // 0xb4ff94: DecompressPointer r1
    //     0xb4ff94: add             x1, x1, HEAP, lsl #32
    // 0xb4ff98: r0 = controller()
    //     0xb4ff98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4ff9c: LoadField: r1 = r0->field_57
    //     0xb4ff9c: ldur            w1, [x0, #0x57]
    // 0xb4ffa0: DecompressPointer r1
    //     0xb4ffa0: add             x1, x1, HEAP, lsl #32
    // 0xb4ffa4: r0 = value()
    //     0xb4ffa4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb4ffa8: cmp             w0, NULL
    // 0xb4ffac: b.ne            #0xb4ffb8
    // 0xb4ffb0: r0 = Null
    //     0xb4ffb0: mov             x0, NULL
    // 0xb4ffb4: b               #0xb4ffc4
    // 0xb4ffb8: LoadField: r1 = r0->field_23
    //     0xb4ffb8: ldur            w1, [x0, #0x23]
    // 0xb4ffbc: DecompressPointer r1
    //     0xb4ffbc: add             x1, x1, HEAP, lsl #32
    // 0xb4ffc0: mov             x0, x1
    // 0xb4ffc4: cmp             w0, NULL
    // 0xb4ffc8: b.ne            #0xb4ffd4
    // 0xb4ffcc: ldur            x2, [fp, #-0x18]
    // 0xb4ffd0: b               #0xb502f8
    // 0xb4ffd4: tbz             w0, #4, #0xb502f4
    // 0xb4ffd8: ldur            x2, [fp, #-8]
    // 0xb4ffdc: ldur            x0, [fp, #-0x18]
    // 0xb4ffe0: LoadField: r1 = r2->field_f
    //     0xb4ffe0: ldur            w1, [x2, #0xf]
    // 0xb4ffe4: DecompressPointer r1
    //     0xb4ffe4: add             x1, x1, HEAP, lsl #32
    // 0xb4ffe8: r0 = controller()
    //     0xb4ffe8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb4ffec: LoadField: r1 = r0->field_d7
    //     0xb4ffec: ldur            w1, [x0, #0xd7]
    // 0xb4fff0: DecompressPointer r1
    //     0xb4fff0: add             x1, x1, HEAP, lsl #32
    // 0xb4fff4: ldur            x2, [fp, #-8]
    // 0xb4fff8: stur            x1, [fp, #-0x20]
    // 0xb4fffc: LoadField: r0 = r2->field_f
    //     0xb4fffc: ldur            w0, [x2, #0xf]
    // 0xb50000: DecompressPointer r0
    //     0xb50000: add             x0, x0, HEAP, lsl #32
    // 0xb50004: stur            x0, [fp, #-0x10]
    // 0xb50008: r0 = LengthLimitingTextInputFormatter()
    //     0xb50008: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb5000c: mov             x3, x0
    // 0xb50010: r0 = 300
    //     0xb50010: movz            x0, #0x12c
    // 0xb50014: stur            x3, [fp, #-0x28]
    // 0xb50018: StoreField: r3->field_7 = r0
    //     0xb50018: stur            w0, [x3, #7]
    // 0xb5001c: r1 = Null
    //     0xb5001c: mov             x1, NULL
    // 0xb50020: r2 = 2
    //     0xb50020: movz            x2, #0x2
    // 0xb50024: r0 = AllocateArray()
    //     0xb50024: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb50028: mov             x2, x0
    // 0xb5002c: ldur            x0, [fp, #-0x28]
    // 0xb50030: stur            x2, [fp, #-0x30]
    // 0xb50034: StoreField: r2->field_f = r0
    //     0xb50034: stur            w0, [x2, #0xf]
    // 0xb50038: r1 = <TextInputFormatter>
    //     0xb50038: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb5003c: ldr             x1, [x1, #0x7b0]
    // 0xb50040: r0 = AllocateGrowableArray()
    //     0xb50040: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb50044: mov             x2, x0
    // 0xb50048: ldur            x0, [fp, #-0x30]
    // 0xb5004c: stur            x2, [fp, #-0x28]
    // 0xb50050: StoreField: r2->field_f = r0
    //     0xb50050: stur            w0, [x2, #0xf]
    // 0xb50054: r0 = 2
    //     0xb50054: movz            x0, #0x2
    // 0xb50058: StoreField: r2->field_b = r0
    //     0xb50058: stur            w0, [x2, #0xb]
    // 0xb5005c: ldur            x3, [fp, #-8]
    // 0xb50060: LoadField: r1 = r3->field_13
    //     0xb50060: ldur            w1, [x3, #0x13]
    // 0xb50064: DecompressPointer r1
    //     0xb50064: add             x1, x1, HEAP, lsl #32
    // 0xb50068: r0 = of()
    //     0xb50068: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5006c: LoadField: r1 = r0->field_87
    //     0xb5006c: ldur            w1, [x0, #0x87]
    // 0xb50070: DecompressPointer r1
    //     0xb50070: add             x1, x1, HEAP, lsl #32
    // 0xb50074: LoadField: r0 = r1->field_2b
    //     0xb50074: ldur            w0, [x1, #0x2b]
    // 0xb50078: DecompressPointer r0
    //     0xb50078: add             x0, x0, HEAP, lsl #32
    // 0xb5007c: ldur            x2, [fp, #-8]
    // 0xb50080: stur            x0, [fp, #-0x30]
    // 0xb50084: LoadField: r1 = r2->field_13
    //     0xb50084: ldur            w1, [x2, #0x13]
    // 0xb50088: DecompressPointer r1
    //     0xb50088: add             x1, x1, HEAP, lsl #32
    // 0xb5008c: r0 = of()
    //     0xb5008c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50090: LoadField: r1 = r0->field_5b
    //     0xb50090: ldur            w1, [x0, #0x5b]
    // 0xb50094: DecompressPointer r1
    //     0xb50094: add             x1, x1, HEAP, lsl #32
    // 0xb50098: r16 = 12.000000
    //     0xb50098: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5009c: ldr             x16, [x16, #0x9e8]
    // 0xb500a0: stp             x16, x1, [SP]
    // 0xb500a4: ldur            x1, [fp, #-0x30]
    // 0xb500a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb500a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb500ac: ldr             x4, [x4, #0x9b8]
    // 0xb500b0: r0 = copyWith()
    //     0xb500b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb500b4: ldur            x2, [fp, #-8]
    // 0xb500b8: stur            x0, [fp, #-0x30]
    // 0xb500bc: LoadField: r1 = r2->field_f
    //     0xb500bc: ldur            w1, [x2, #0xf]
    // 0xb500c0: DecompressPointer r1
    //     0xb500c0: add             x1, x1, HEAP, lsl #32
    // 0xb500c4: r0 = controller()
    //     0xb500c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb500c8: LoadField: r2 = r0->field_db
    //     0xb500c8: ldur            w2, [x0, #0xdb]
    // 0xb500cc: DecompressPointer r2
    //     0xb500cc: add             x2, x2, HEAP, lsl #32
    // 0xb500d0: ldur            x0, [fp, #-8]
    // 0xb500d4: stur            x2, [fp, #-0x38]
    // 0xb500d8: LoadField: r1 = r0->field_13
    //     0xb500d8: ldur            w1, [x0, #0x13]
    // 0xb500dc: DecompressPointer r1
    //     0xb500dc: add             x1, x1, HEAP, lsl #32
    // 0xb500e0: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb500e0: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb500e4: ldur            x2, [fp, #-8]
    // 0xb500e8: stur            x0, [fp, #-0x40]
    // 0xb500ec: LoadField: r1 = r2->field_13
    //     0xb500ec: ldur            w1, [x2, #0x13]
    // 0xb500f0: DecompressPointer r1
    //     0xb500f0: add             x1, x1, HEAP, lsl #32
    // 0xb500f4: r0 = of()
    //     0xb500f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb500f8: LoadField: r1 = r0->field_87
    //     0xb500f8: ldur            w1, [x0, #0x87]
    // 0xb500fc: DecompressPointer r1
    //     0xb500fc: add             x1, x1, HEAP, lsl #32
    // 0xb50100: LoadField: r0 = r1->field_2b
    //     0xb50100: ldur            w0, [x1, #0x2b]
    // 0xb50104: DecompressPointer r0
    //     0xb50104: add             x0, x0, HEAP, lsl #32
    // 0xb50108: stur            x0, [fp, #-0x48]
    // 0xb5010c: r1 = Instance_Color
    //     0xb5010c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb50110: d0 = 0.400000
    //     0xb50110: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb50114: r0 = withOpacity()
    //     0xb50114: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb50118: r16 = 12.000000
    //     0xb50118: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5011c: ldr             x16, [x16, #0x9e8]
    // 0xb50120: stp             x16, x0, [SP]
    // 0xb50124: ldur            x1, [fp, #-0x48]
    // 0xb50128: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb50128: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5012c: ldr             x4, [x4, #0x9b8]
    // 0xb50130: r0 = copyWith()
    //     0xb50130: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb50134: ldur            x2, [fp, #-8]
    // 0xb50138: stur            x0, [fp, #-0x48]
    // 0xb5013c: LoadField: r1 = r2->field_13
    //     0xb5013c: ldur            w1, [x2, #0x13]
    // 0xb50140: DecompressPointer r1
    //     0xb50140: add             x1, x1, HEAP, lsl #32
    // 0xb50144: r0 = of()
    //     0xb50144: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50148: LoadField: r1 = r0->field_87
    //     0xb50148: ldur            w1, [x0, #0x87]
    // 0xb5014c: DecompressPointer r1
    //     0xb5014c: add             x1, x1, HEAP, lsl #32
    // 0xb50150: LoadField: r0 = r1->field_2b
    //     0xb50150: ldur            w0, [x1, #0x2b]
    // 0xb50154: DecompressPointer r0
    //     0xb50154: add             x0, x0, HEAP, lsl #32
    // 0xb50158: r16 = Instance_Color
    //     0xb50158: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0xb5015c: ldr             x16, [x16, #0x7c0]
    // 0xb50160: r30 = 12.000000
    //     0xb50160: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb50164: ldr             lr, [lr, #0x9e8]
    // 0xb50168: stp             lr, x16, [SP]
    // 0xb5016c: mov             x1, x0
    // 0xb50170: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb50170: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb50174: ldr             x4, [x4, #0x9b8]
    // 0xb50178: r0 = copyWith()
    //     0xb50178: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5017c: r16 = "Remark: Describe your issue in detail"
    //     0xb5017c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fe0] "Remark: Describe your issue in detail"
    //     0xb50180: ldr             x16, [x16, #0xfe0]
    // 0xb50184: ldur            lr, [fp, #-0x48]
    // 0xb50188: stp             lr, x16, [SP, #8]
    // 0xb5018c: str             x0, [SP]
    // 0xb50190: ldur            x1, [fp, #-0x40]
    // 0xb50194: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0xb50194: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0xb50198: ldr             x4, [x4, #0xfe8]
    // 0xb5019c: r0 = copyWith()
    //     0xb5019c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb501a0: ldur            x2, [fp, #-0x10]
    // 0xb501a4: r1 = Function '_validateOther@1612202706':.
    //     0xb501a4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f440] AnonymousClosure: (0xb51164), of [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView
    //     0xb501a8: ldr             x1, [x1, #0x440]
    // 0xb501ac: stur            x0, [fp, #-0x10]
    // 0xb501b0: r0 = AllocateClosure()
    //     0xb501b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb501b4: ldur            x2, [fp, #-8]
    // 0xb501b8: r1 = Function '<anonymous closure>':.
    //     0xb501b8: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f448] AnonymousClosure: (0xb50ecc), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb501bc: ldr             x1, [x1, #0x448]
    // 0xb501c0: stur            x0, [fp, #-0x40]
    // 0xb501c4: r0 = AllocateClosure()
    //     0xb501c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb501c8: r1 = <String>
    //     0xb501c8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb501cc: stur            x0, [fp, #-0x48]
    // 0xb501d0: r0 = TextFormField()
    //     0xb501d0: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb501d4: stur            x0, [fp, #-0x58]
    // 0xb501d8: ldur            x16, [fp, #-0x40]
    // 0xb501dc: r30 = false
    //     0xb501dc: add             lr, NULL, #0x30  ; false
    // 0xb501e0: stp             lr, x16, [SP, #0x40]
    // 0xb501e4: r16 = Instance_AutovalidateMode
    //     0xb501e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb501e8: ldr             x16, [x16, #0x7e8]
    // 0xb501ec: ldur            lr, [fp, #-0x28]
    // 0xb501f0: stp             lr, x16, [SP, #0x30]
    // 0xb501f4: r16 = Instance_TextInputType
    //     0xb501f4: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0xb501f8: ldr             x16, [x16, #0x7f0]
    // 0xb501fc: r30 = 6
    //     0xb501fc: movz            lr, #0x6
    // 0xb50200: stp             lr, x16, [SP, #0x20]
    // 0xb50204: r16 = 10
    //     0xb50204: movz            x16, #0xa
    // 0xb50208: ldur            lr, [fp, #-0x30]
    // 0xb5020c: stp             lr, x16, [SP, #0x10]
    // 0xb50210: ldur            x16, [fp, #-0x38]
    // 0xb50214: ldur            lr, [fp, #-0x48]
    // 0xb50218: stp             lr, x16, [SP]
    // 0xb5021c: mov             x1, x0
    // 0xb50220: ldur            x2, [fp, #-0x10]
    // 0xb50224: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x4, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, validator, 0x2, null]
    //     0xb50224: add             x4, PP, #0x33, lsl #12  ; [pp+0x337f8] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x4, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "validator", 0x2, Null]
    //     0xb50228: ldr             x4, [x4, #0x7f8]
    // 0xb5022c: r0 = TextFormField()
    //     0xb5022c: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb50230: r0 = Form()
    //     0xb50230: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb50234: mov             x1, x0
    // 0xb50238: ldur            x0, [fp, #-0x58]
    // 0xb5023c: stur            x1, [fp, #-0x10]
    // 0xb50240: StoreField: r1->field_b = r0
    //     0xb50240: stur            w0, [x1, #0xb]
    // 0xb50244: r0 = Instance_AutovalidateMode
    //     0xb50244: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb50248: ldr             x0, [x0, #0x800]
    // 0xb5024c: StoreField: r1->field_23 = r0
    //     0xb5024c: stur            w0, [x1, #0x23]
    // 0xb50250: ldur            x0, [fp, #-0x20]
    // 0xb50254: StoreField: r1->field_7 = r0
    //     0xb50254: stur            w0, [x1, #7]
    // 0xb50258: r0 = Padding()
    //     0xb50258: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5025c: mov             x2, x0
    // 0xb50260: r0 = Instance_EdgeInsets
    //     0xb50260: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xb50264: ldr             x0, [x0, #0x868]
    // 0xb50268: stur            x2, [fp, #-0x20]
    // 0xb5026c: StoreField: r2->field_f = r0
    //     0xb5026c: stur            w0, [x2, #0xf]
    // 0xb50270: ldur            x0, [fp, #-0x10]
    // 0xb50274: StoreField: r2->field_b = r0
    //     0xb50274: stur            w0, [x2, #0xb]
    // 0xb50278: ldur            x0, [fp, #-0x18]
    // 0xb5027c: LoadField: r1 = r0->field_b
    //     0xb5027c: ldur            w1, [x0, #0xb]
    // 0xb50280: LoadField: r3 = r0->field_f
    //     0xb50280: ldur            w3, [x0, #0xf]
    // 0xb50284: DecompressPointer r3
    //     0xb50284: add             x3, x3, HEAP, lsl #32
    // 0xb50288: LoadField: r4 = r3->field_b
    //     0xb50288: ldur            w4, [x3, #0xb]
    // 0xb5028c: r3 = LoadInt32Instr(r1)
    //     0xb5028c: sbfx            x3, x1, #1, #0x1f
    // 0xb50290: stur            x3, [fp, #-0x50]
    // 0xb50294: r1 = LoadInt32Instr(r4)
    //     0xb50294: sbfx            x1, x4, #1, #0x1f
    // 0xb50298: cmp             x3, x1
    // 0xb5029c: b.ne            #0xb502a8
    // 0xb502a0: mov             x1, x0
    // 0xb502a4: r0 = _growToNextCapacity()
    //     0xb502a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb502a8: ldur            x2, [fp, #-0x18]
    // 0xb502ac: ldur            x3, [fp, #-0x50]
    // 0xb502b0: add             x0, x3, #1
    // 0xb502b4: lsl             x1, x0, #1
    // 0xb502b8: StoreField: r2->field_b = r1
    //     0xb502b8: stur            w1, [x2, #0xb]
    // 0xb502bc: LoadField: r1 = r2->field_f
    //     0xb502bc: ldur            w1, [x2, #0xf]
    // 0xb502c0: DecompressPointer r1
    //     0xb502c0: add             x1, x1, HEAP, lsl #32
    // 0xb502c4: ldur            x0, [fp, #-0x20]
    // 0xb502c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb502c8: add             x25, x1, x3, lsl #2
    //     0xb502cc: add             x25, x25, #0xf
    //     0xb502d0: str             w0, [x25]
    //     0xb502d4: tbz             w0, #0, #0xb502f0
    //     0xb502d8: ldurb           w16, [x1, #-1]
    //     0xb502dc: ldurb           w17, [x0, #-1]
    //     0xb502e0: and             x16, x17, x16, lsr #2
    //     0xb502e4: tst             x16, HEAP, lsr #32
    //     0xb502e8: b.eq            #0xb502f0
    //     0xb502ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb502f0: b               #0xb50384
    // 0xb502f4: ldur            x2, [fp, #-0x18]
    // 0xb502f8: r0 = Container()
    //     0xb502f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb502fc: mov             x1, x0
    // 0xb50300: stur            x0, [fp, #-0x10]
    // 0xb50304: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb50304: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb50308: r0 = Container()
    //     0xb50308: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5030c: ldur            x0, [fp, #-0x18]
    // 0xb50310: LoadField: r1 = r0->field_b
    //     0xb50310: ldur            w1, [x0, #0xb]
    // 0xb50314: LoadField: r2 = r0->field_f
    //     0xb50314: ldur            w2, [x0, #0xf]
    // 0xb50318: DecompressPointer r2
    //     0xb50318: add             x2, x2, HEAP, lsl #32
    // 0xb5031c: LoadField: r3 = r2->field_b
    //     0xb5031c: ldur            w3, [x2, #0xb]
    // 0xb50320: r2 = LoadInt32Instr(r1)
    //     0xb50320: sbfx            x2, x1, #1, #0x1f
    // 0xb50324: stur            x2, [fp, #-0x50]
    // 0xb50328: r1 = LoadInt32Instr(r3)
    //     0xb50328: sbfx            x1, x3, #1, #0x1f
    // 0xb5032c: cmp             x2, x1
    // 0xb50330: b.ne            #0xb5033c
    // 0xb50334: mov             x1, x0
    // 0xb50338: r0 = _growToNextCapacity()
    //     0xb50338: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5033c: ldur            x2, [fp, #-0x18]
    // 0xb50340: ldur            x3, [fp, #-0x50]
    // 0xb50344: add             x0, x3, #1
    // 0xb50348: lsl             x1, x0, #1
    // 0xb5034c: StoreField: r2->field_b = r1
    //     0xb5034c: stur            w1, [x2, #0xb]
    // 0xb50350: LoadField: r1 = r2->field_f
    //     0xb50350: ldur            w1, [x2, #0xf]
    // 0xb50354: DecompressPointer r1
    //     0xb50354: add             x1, x1, HEAP, lsl #32
    // 0xb50358: ldur            x0, [fp, #-0x10]
    // 0xb5035c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5035c: add             x25, x1, x3, lsl #2
    //     0xb50360: add             x25, x25, #0xf
    //     0xb50364: str             w0, [x25]
    //     0xb50368: tbz             w0, #0, #0xb50384
    //     0xb5036c: ldurb           w16, [x1, #-1]
    //     0xb50370: ldurb           w17, [x0, #-1]
    //     0xb50374: and             x16, x17, x16, lsr #2
    //     0xb50378: tst             x16, HEAP, lsr #32
    //     0xb5037c: b.eq            #0xb50384
    //     0xb50380: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb50384: ldur            x0, [fp, #-8]
    // 0xb50388: LoadField: r1 = r0->field_f
    //     0xb50388: ldur            w1, [x0, #0xf]
    // 0xb5038c: DecompressPointer r1
    //     0xb5038c: add             x1, x1, HEAP, lsl #32
    // 0xb50390: r0 = controller()
    //     0xb50390: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb50394: LoadField: r1 = r0->field_57
    //     0xb50394: ldur            w1, [x0, #0x57]
    // 0xb50398: DecompressPointer r1
    //     0xb50398: add             x1, x1, HEAP, lsl #32
    // 0xb5039c: r0 = value()
    //     0xb5039c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb503a0: cmp             w0, NULL
    // 0xb503a4: b.ne            #0xb503b0
    // 0xb503a8: r0 = Null
    //     0xb503a8: mov             x0, NULL
    // 0xb503ac: b               #0xb503bc
    // 0xb503b0: LoadField: r1 = r0->field_23
    //     0xb503b0: ldur            w1, [x0, #0x23]
    // 0xb503b4: DecompressPointer r1
    //     0xb503b4: add             x1, x1, HEAP, lsl #32
    // 0xb503b8: mov             x0, x1
    // 0xb503bc: cmp             w0, NULL
    // 0xb503c0: b.ne            #0xb503cc
    // 0xb503c4: r1 = true
    //     0xb503c4: add             x1, NULL, #0x20  ; true
    // 0xb503c8: b               #0xb503d0
    // 0xb503cc: mov             x1, x0
    // 0xb503d0: ldur            x0, [fp, #-8]
    // 0xb503d4: eor             x2, x1, #0x10
    // 0xb503d8: stur            x2, [fp, #-0x10]
    // 0xb503dc: r0 = Radius()
    //     0xb503dc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb503e0: d0 = 20.000000
    //     0xb503e0: fmov            d0, #20.00000000
    // 0xb503e4: stur            x0, [fp, #-0x20]
    // 0xb503e8: StoreField: r0->field_7 = d0
    //     0xb503e8: stur            d0, [x0, #7]
    // 0xb503ec: StoreField: r0->field_f = d0
    //     0xb503ec: stur            d0, [x0, #0xf]
    // 0xb503f0: r0 = BorderRadius()
    //     0xb503f0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb503f4: mov             x1, x0
    // 0xb503f8: ldur            x0, [fp, #-0x20]
    // 0xb503fc: stur            x1, [fp, #-0x28]
    // 0xb50400: StoreField: r1->field_7 = r0
    //     0xb50400: stur            w0, [x1, #7]
    // 0xb50404: StoreField: r1->field_b = r0
    //     0xb50404: stur            w0, [x1, #0xb]
    // 0xb50408: StoreField: r1->field_f = r0
    //     0xb50408: stur            w0, [x1, #0xf]
    // 0xb5040c: StoreField: r1->field_13 = r0
    //     0xb5040c: stur            w0, [x1, #0x13]
    // 0xb50410: r0 = RoundedRectangleBorder()
    //     0xb50410: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb50414: mov             x2, x0
    // 0xb50418: ldur            x0, [fp, #-0x28]
    // 0xb5041c: stur            x2, [fp, #-0x20]
    // 0xb50420: StoreField: r2->field_b = r0
    //     0xb50420: stur            w0, [x2, #0xb]
    // 0xb50424: r0 = Instance_BorderSide
    //     0xb50424: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb50428: ldr             x0, [x0, #0xe20]
    // 0xb5042c: StoreField: r2->field_7 = r0
    //     0xb5042c: stur            w0, [x2, #7]
    // 0xb50430: ldur            x3, [fp, #-8]
    // 0xb50434: LoadField: r1 = r3->field_f
    //     0xb50434: ldur            w1, [x3, #0xf]
    // 0xb50438: DecompressPointer r1
    //     0xb50438: add             x1, x1, HEAP, lsl #32
    // 0xb5043c: r0 = controller()
    //     0xb5043c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb50440: LoadField: r1 = r0->field_57
    //     0xb50440: ldur            w1, [x0, #0x57]
    // 0xb50444: DecompressPointer r1
    //     0xb50444: add             x1, x1, HEAP, lsl #32
    // 0xb50448: r0 = value()
    //     0xb50448: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb5044c: cmp             w0, NULL
    // 0xb50450: b.ne            #0xb5045c
    // 0xb50454: r0 = Null
    //     0xb50454: mov             x0, NULL
    // 0xb50458: b               #0xb5047c
    // 0xb5045c: LoadField: r1 = r0->field_13
    //     0xb5045c: ldur            w1, [x0, #0x13]
    // 0xb50460: DecompressPointer r1
    //     0xb50460: add             x1, x1, HEAP, lsl #32
    // 0xb50464: cmp             w1, NULL
    // 0xb50468: b.ne            #0xb50474
    // 0xb5046c: r0 = Null
    //     0xb5046c: mov             x0, NULL
    // 0xb50470: b               #0xb5047c
    // 0xb50474: LoadField: r0 = r1->field_7
    //     0xb50474: ldur            w0, [x1, #7]
    // 0xb50478: DecompressPointer r0
    //     0xb50478: add             x0, x0, HEAP, lsl #32
    // 0xb5047c: cmp             w0, NULL
    // 0xb50480: b.ne            #0xb5048c
    // 0xb50484: r2 = ""
    //     0xb50484: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb50488: b               #0xb50490
    // 0xb5048c: mov             x2, x0
    // 0xb50490: ldur            x0, [fp, #-8]
    // 0xb50494: stur            x2, [fp, #-0x28]
    // 0xb50498: LoadField: r1 = r0->field_13
    //     0xb50498: ldur            w1, [x0, #0x13]
    // 0xb5049c: DecompressPointer r1
    //     0xb5049c: add             x1, x1, HEAP, lsl #32
    // 0xb504a0: r0 = of()
    //     0xb504a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb504a4: LoadField: r1 = r0->field_87
    //     0xb504a4: ldur            w1, [x0, #0x87]
    // 0xb504a8: DecompressPointer r1
    //     0xb504a8: add             x1, x1, HEAP, lsl #32
    // 0xb504ac: LoadField: r0 = r1->field_7
    //     0xb504ac: ldur            w0, [x1, #7]
    // 0xb504b0: DecompressPointer r0
    //     0xb504b0: add             x0, x0, HEAP, lsl #32
    // 0xb504b4: stur            x0, [fp, #-0x30]
    // 0xb504b8: r1 = Instance_Color
    //     0xb504b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb504bc: d0 = 0.700000
    //     0xb504bc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb504c0: ldr             d0, [x17, #0xf48]
    // 0xb504c4: r0 = withOpacity()
    //     0xb504c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb504c8: r16 = 14.000000
    //     0xb504c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb504cc: ldr             x16, [x16, #0x1d8]
    // 0xb504d0: stp             x0, x16, [SP]
    // 0xb504d4: ldur            x1, [fp, #-0x30]
    // 0xb504d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb504d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb504dc: ldr             x4, [x4, #0xaa0]
    // 0xb504e0: r0 = copyWith()
    //     0xb504e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb504e4: stur            x0, [fp, #-0x30]
    // 0xb504e8: r0 = Text()
    //     0xb504e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb504ec: mov             x1, x0
    // 0xb504f0: ldur            x0, [fp, #-0x28]
    // 0xb504f4: stur            x1, [fp, #-0x38]
    // 0xb504f8: StoreField: r1->field_b = r0
    //     0xb504f8: stur            w0, [x1, #0xb]
    // 0xb504fc: ldur            x0, [fp, #-0x30]
    // 0xb50500: StoreField: r1->field_13 = r0
    //     0xb50500: stur            w0, [x1, #0x13]
    // 0xb50504: r0 = Padding()
    //     0xb50504: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50508: mov             x2, x0
    // 0xb5050c: r0 = Instance_EdgeInsets
    //     0xb5050c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33808] Obj!EdgeInsets@d592d1
    //     0xb50510: ldr             x0, [x0, #0x808]
    // 0xb50514: stur            x2, [fp, #-0x28]
    // 0xb50518: StoreField: r2->field_f = r0
    //     0xb50518: stur            w0, [x2, #0xf]
    // 0xb5051c: ldur            x0, [fp, #-0x38]
    // 0xb50520: StoreField: r2->field_b = r0
    //     0xb50520: stur            w0, [x2, #0xb]
    // 0xb50524: ldur            x0, [fp, #-8]
    // 0xb50528: LoadField: r1 = r0->field_f
    //     0xb50528: ldur            w1, [x0, #0xf]
    // 0xb5052c: DecompressPointer r1
    //     0xb5052c: add             x1, x1, HEAP, lsl #32
    // 0xb50530: r0 = controller()
    //     0xb50530: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb50534: LoadField: r1 = r0->field_57
    //     0xb50534: ldur            w1, [x0, #0x57]
    // 0xb50538: DecompressPointer r1
    //     0xb50538: add             x1, x1, HEAP, lsl #32
    // 0xb5053c: r0 = value()
    //     0xb5053c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb50540: cmp             w0, NULL
    // 0xb50544: b.ne            #0xb50550
    // 0xb50548: r0 = Null
    //     0xb50548: mov             x0, NULL
    // 0xb5054c: b               #0xb50570
    // 0xb50550: LoadField: r1 = r0->field_13
    //     0xb50550: ldur            w1, [x0, #0x13]
    // 0xb50554: DecompressPointer r1
    //     0xb50554: add             x1, x1, HEAP, lsl #32
    // 0xb50558: cmp             w1, NULL
    // 0xb5055c: b.ne            #0xb50568
    // 0xb50560: r0 = Null
    //     0xb50560: mov             x0, NULL
    // 0xb50564: b               #0xb50570
    // 0xb50568: LoadField: r0 = r1->field_b
    //     0xb50568: ldur            w0, [x1, #0xb]
    // 0xb5056c: DecompressPointer r0
    //     0xb5056c: add             x0, x0, HEAP, lsl #32
    // 0xb50570: cmp             w0, NULL
    // 0xb50574: b.ne            #0xb50580
    // 0xb50578: r2 = ""
    //     0xb50578: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5057c: b               #0xb50584
    // 0xb50580: mov             x2, x0
    // 0xb50584: ldur            x0, [fp, #-8]
    // 0xb50588: stur            x2, [fp, #-0x30]
    // 0xb5058c: LoadField: r1 = r0->field_13
    //     0xb5058c: ldur            w1, [x0, #0x13]
    // 0xb50590: DecompressPointer r1
    //     0xb50590: add             x1, x1, HEAP, lsl #32
    // 0xb50594: r0 = of()
    //     0xb50594: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50598: LoadField: r1 = r0->field_87
    //     0xb50598: ldur            w1, [x0, #0x87]
    // 0xb5059c: DecompressPointer r1
    //     0xb5059c: add             x1, x1, HEAP, lsl #32
    // 0xb505a0: LoadField: r0 = r1->field_2f
    //     0xb505a0: ldur            w0, [x1, #0x2f]
    // 0xb505a4: DecompressPointer r0
    //     0xb505a4: add             x0, x0, HEAP, lsl #32
    // 0xb505a8: stur            x0, [fp, #-0x38]
    // 0xb505ac: cmp             w0, NULL
    // 0xb505b0: b.ne            #0xb505bc
    // 0xb505b4: r2 = Null
    //     0xb505b4: mov             x2, NULL
    // 0xb505b8: b               #0xb505ec
    // 0xb505bc: r1 = Instance_Color
    //     0xb505bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb505c0: d0 = 0.700000
    //     0xb505c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb505c4: ldr             d0, [x17, #0xf48]
    // 0xb505c8: r0 = withOpacity()
    //     0xb505c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb505cc: r16 = 14.000000
    //     0xb505cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb505d0: ldr             x16, [x16, #0x1d8]
    // 0xb505d4: stp             x0, x16, [SP]
    // 0xb505d8: ldur            x1, [fp, #-0x38]
    // 0xb505dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb505dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb505e0: ldr             x4, [x4, #0xaa0]
    // 0xb505e4: r0 = copyWith()
    //     0xb505e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb505e8: mov             x2, x0
    // 0xb505ec: ldur            x0, [fp, #-8]
    // 0xb505f0: ldur            x1, [fp, #-0x30]
    // 0xb505f4: stur            x2, [fp, #-0x38]
    // 0xb505f8: r0 = Text()
    //     0xb505f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb505fc: mov             x1, x0
    // 0xb50600: ldur            x0, [fp, #-0x30]
    // 0xb50604: stur            x1, [fp, #-0x40]
    // 0xb50608: StoreField: r1->field_b = r0
    //     0xb50608: stur            w0, [x1, #0xb]
    // 0xb5060c: ldur            x0, [fp, #-0x38]
    // 0xb50610: StoreField: r1->field_13 = r0
    //     0xb50610: stur            w0, [x1, #0x13]
    // 0xb50614: r0 = Padding()
    //     0xb50614: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50618: mov             x2, x0
    // 0xb5061c: r0 = Instance_EdgeInsets
    //     0xb5061c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33810] Obj!EdgeInsets@d592a1
    //     0xb50620: ldr             x0, [x0, #0x810]
    // 0xb50624: stur            x2, [fp, #-0x30]
    // 0xb50628: StoreField: r2->field_f = r0
    //     0xb50628: stur            w0, [x2, #0xf]
    // 0xb5062c: ldur            x0, [fp, #-0x40]
    // 0xb50630: StoreField: r2->field_b = r0
    //     0xb50630: stur            w0, [x2, #0xb]
    // 0xb50634: ldur            x0, [fp, #-8]
    // 0xb50638: LoadField: r1 = r0->field_13
    //     0xb50638: ldur            w1, [x0, #0x13]
    // 0xb5063c: DecompressPointer r1
    //     0xb5063c: add             x1, x1, HEAP, lsl #32
    // 0xb50640: r0 = of()
    //     0xb50640: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50644: LoadField: r1 = r0->field_5b
    //     0xb50644: ldur            w1, [x0, #0x5b]
    // 0xb50648: DecompressPointer r1
    //     0xb50648: add             x1, x1, HEAP, lsl #32
    // 0xb5064c: r0 = LoadClassIdInstr(r1)
    //     0xb5064c: ldur            x0, [x1, #-1]
    //     0xb50650: ubfx            x0, x0, #0xc, #0x14
    // 0xb50654: d0 = 0.100000
    //     0xb50654: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb50658: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb50658: sub             lr, x0, #0xffa
    //     0xb5065c: ldr             lr, [x21, lr, lsl #3]
    //     0xb50660: blr             lr
    // 0xb50664: stur            x0, [fp, #-0x38]
    // 0xb50668: r0 = Divider()
    //     0xb50668: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb5066c: mov             x1, x0
    // 0xb50670: ldur            x0, [fp, #-0x38]
    // 0xb50674: stur            x1, [fp, #-0x40]
    // 0xb50678: StoreField: r1->field_1f = r0
    //     0xb50678: stur            w0, [x1, #0x1f]
    // 0xb5067c: r0 = SvgPicture()
    //     0xb5067c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb50680: stur            x0, [fp, #-0x38]
    // 0xb50684: r16 = Instance_BoxFit
    //     0xb50684: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb50688: ldr             x16, [x16, #0xb18]
    // 0xb5068c: r30 = Instance_ColorFilter
    //     0xb5068c: add             lr, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0xb50690: ldr             lr, [lr, #0x818]
    // 0xb50694: stp             lr, x16, [SP]
    // 0xb50698: mov             x1, x0
    // 0xb5069c: r2 = "assets/images/reason_glasses_icon.svg"
    //     0xb5069c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f338] "assets/images/reason_glasses_icon.svg"
    //     0xb506a0: ldr             x2, [x2, #0x338]
    // 0xb506a4: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0xb506a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0xb506a8: ldr             x4, [x4, #0x820]
    // 0xb506ac: r0 = SvgPicture.asset()
    //     0xb506ac: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb506b0: ldur            x0, [fp, #-8]
    // 0xb506b4: LoadField: r1 = r0->field_f
    //     0xb506b4: ldur            w1, [x0, #0xf]
    // 0xb506b8: DecompressPointer r1
    //     0xb506b8: add             x1, x1, HEAP, lsl #32
    // 0xb506bc: r0 = controller()
    //     0xb506bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb506c0: LoadField: r1 = r0->field_57
    //     0xb506c0: ldur            w1, [x0, #0x57]
    // 0xb506c4: DecompressPointer r1
    //     0xb506c4: add             x1, x1, HEAP, lsl #32
    // 0xb506c8: r0 = value()
    //     0xb506c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb506cc: cmp             w0, NULL
    // 0xb506d0: b.ne            #0xb506dc
    // 0xb506d4: r0 = Null
    //     0xb506d4: mov             x0, NULL
    // 0xb506d8: b               #0xb506e8
    // 0xb506dc: LoadField: r1 = r0->field_1b
    //     0xb506dc: ldur            w1, [x0, #0x1b]
    // 0xb506e0: DecompressPointer r1
    //     0xb506e0: add             x1, x1, HEAP, lsl #32
    // 0xb506e4: mov             x0, x1
    // 0xb506e8: cmp             w0, NULL
    // 0xb506ec: b.ne            #0xb506f8
    // 0xb506f0: r7 = ""
    //     0xb506f0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb506f4: b               #0xb506fc
    // 0xb506f8: mov             x7, x0
    // 0xb506fc: ldur            x0, [fp, #-8]
    // 0xb50700: ldur            x6, [fp, #-0x20]
    // 0xb50704: ldur            x5, [fp, #-0x28]
    // 0xb50708: ldur            x4, [fp, #-0x30]
    // 0xb5070c: ldur            x3, [fp, #-0x40]
    // 0xb50710: ldur            x2, [fp, #-0x38]
    // 0xb50714: stur            x7, [fp, #-0x48]
    // 0xb50718: LoadField: r1 = r0->field_13
    //     0xb50718: ldur            w1, [x0, #0x13]
    // 0xb5071c: DecompressPointer r1
    //     0xb5071c: add             x1, x1, HEAP, lsl #32
    // 0xb50720: r0 = of()
    //     0xb50720: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50724: LoadField: r1 = r0->field_87
    //     0xb50724: ldur            w1, [x0, #0x87]
    // 0xb50728: DecompressPointer r1
    //     0xb50728: add             x1, x1, HEAP, lsl #32
    // 0xb5072c: LoadField: r0 = r1->field_2b
    //     0xb5072c: ldur            w0, [x1, #0x2b]
    // 0xb50730: DecompressPointer r0
    //     0xb50730: add             x0, x0, HEAP, lsl #32
    // 0xb50734: stur            x0, [fp, #-0x58]
    // 0xb50738: r1 = Instance_Color
    //     0xb50738: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5073c: d0 = 0.700000
    //     0xb5073c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb50740: ldr             d0, [x17, #0xf48]
    // 0xb50744: r0 = withOpacity()
    //     0xb50744: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb50748: r16 = 12.000000
    //     0xb50748: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5074c: ldr             x16, [x16, #0x9e8]
    // 0xb50750: stp             x0, x16, [SP]
    // 0xb50754: ldur            x1, [fp, #-0x58]
    // 0xb50758: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb50758: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5075c: ldr             x4, [x4, #0xaa0]
    // 0xb50760: r0 = copyWith()
    //     0xb50760: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb50764: stur            x0, [fp, #-0x58]
    // 0xb50768: r0 = Text()
    //     0xb50768: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5076c: mov             x2, x0
    // 0xb50770: ldur            x0, [fp, #-0x48]
    // 0xb50774: stur            x2, [fp, #-0x60]
    // 0xb50778: StoreField: r2->field_b = r0
    //     0xb50778: stur            w0, [x2, #0xb]
    // 0xb5077c: ldur            x0, [fp, #-0x58]
    // 0xb50780: StoreField: r2->field_13 = r0
    //     0xb50780: stur            w0, [x2, #0x13]
    // 0xb50784: r0 = Instance_TextOverflow
    //     0xb50784: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb50788: ldr             x0, [x0, #0xe10]
    // 0xb5078c: StoreField: r2->field_2b = r0
    //     0xb5078c: stur            w0, [x2, #0x2b]
    // 0xb50790: r0 = 4
    //     0xb50790: movz            x0, #0x4
    // 0xb50794: StoreField: r2->field_37 = r0
    //     0xb50794: stur            w0, [x2, #0x37]
    // 0xb50798: r1 = <FlexParentData>
    //     0xb50798: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5079c: ldr             x1, [x1, #0xe00]
    // 0xb507a0: r0 = Expanded()
    //     0xb507a0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb507a4: mov             x3, x0
    // 0xb507a8: r0 = 1
    //     0xb507a8: movz            x0, #0x1
    // 0xb507ac: stur            x3, [fp, #-0x48]
    // 0xb507b0: StoreField: r3->field_13 = r0
    //     0xb507b0: stur            x0, [x3, #0x13]
    // 0xb507b4: r0 = Instance_FlexFit
    //     0xb507b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb507b8: ldr             x0, [x0, #0xe08]
    // 0xb507bc: StoreField: r3->field_1b = r0
    //     0xb507bc: stur            w0, [x3, #0x1b]
    // 0xb507c0: ldur            x0, [fp, #-0x60]
    // 0xb507c4: StoreField: r3->field_b = r0
    //     0xb507c4: stur            w0, [x3, #0xb]
    // 0xb507c8: r1 = Null
    //     0xb507c8: mov             x1, NULL
    // 0xb507cc: r2 = 6
    //     0xb507cc: movz            x2, #0x6
    // 0xb507d0: r0 = AllocateArray()
    //     0xb507d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb507d4: mov             x2, x0
    // 0xb507d8: ldur            x0, [fp, #-0x38]
    // 0xb507dc: stur            x2, [fp, #-0x58]
    // 0xb507e0: StoreField: r2->field_f = r0
    //     0xb507e0: stur            w0, [x2, #0xf]
    // 0xb507e4: r16 = Instance_SizedBox
    //     0xb507e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb507e8: ldr             x16, [x16, #0xb20]
    // 0xb507ec: StoreField: r2->field_13 = r16
    //     0xb507ec: stur            w16, [x2, #0x13]
    // 0xb507f0: ldur            x0, [fp, #-0x48]
    // 0xb507f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb507f4: stur            w0, [x2, #0x17]
    // 0xb507f8: r1 = <Widget>
    //     0xb507f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb507fc: r0 = AllocateGrowableArray()
    //     0xb507fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb50800: mov             x1, x0
    // 0xb50804: ldur            x0, [fp, #-0x58]
    // 0xb50808: stur            x1, [fp, #-0x38]
    // 0xb5080c: StoreField: r1->field_f = r0
    //     0xb5080c: stur            w0, [x1, #0xf]
    // 0xb50810: r0 = 6
    //     0xb50810: movz            x0, #0x6
    // 0xb50814: StoreField: r1->field_b = r0
    //     0xb50814: stur            w0, [x1, #0xb]
    // 0xb50818: r0 = Row()
    //     0xb50818: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5081c: mov             x1, x0
    // 0xb50820: r0 = Instance_Axis
    //     0xb50820: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb50824: stur            x1, [fp, #-0x48]
    // 0xb50828: StoreField: r1->field_f = r0
    //     0xb50828: stur            w0, [x1, #0xf]
    // 0xb5082c: r0 = Instance_MainAxisAlignment
    //     0xb5082c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb50830: ldr             x0, [x0, #0xa08]
    // 0xb50834: StoreField: r1->field_13 = r0
    //     0xb50834: stur            w0, [x1, #0x13]
    // 0xb50838: r2 = Instance_MainAxisSize
    //     0xb50838: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5083c: ldr             x2, [x2, #0xa10]
    // 0xb50840: ArrayStore: r1[0] = r2  ; List_4
    //     0xb50840: stur            w2, [x1, #0x17]
    // 0xb50844: r3 = Instance_CrossAxisAlignment
    //     0xb50844: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb50848: ldr             x3, [x3, #0xa18]
    // 0xb5084c: StoreField: r1->field_1b = r3
    //     0xb5084c: stur            w3, [x1, #0x1b]
    // 0xb50850: r3 = Instance_VerticalDirection
    //     0xb50850: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb50854: ldr             x3, [x3, #0xa20]
    // 0xb50858: StoreField: r1->field_23 = r3
    //     0xb50858: stur            w3, [x1, #0x23]
    // 0xb5085c: r4 = Instance_Clip
    //     0xb5085c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb50860: ldr             x4, [x4, #0x38]
    // 0xb50864: StoreField: r1->field_2b = r4
    //     0xb50864: stur            w4, [x1, #0x2b]
    // 0xb50868: StoreField: r1->field_2f = rZR
    //     0xb50868: stur            xzr, [x1, #0x2f]
    // 0xb5086c: ldur            x5, [fp, #-0x38]
    // 0xb50870: StoreField: r1->field_b = r5
    //     0xb50870: stur            w5, [x1, #0xb]
    // 0xb50874: r0 = Padding()
    //     0xb50874: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50878: mov             x3, x0
    // 0xb5087c: r0 = Instance_EdgeInsets
    //     0xb5087c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb50880: ldr             x0, [x0, #0x980]
    // 0xb50884: stur            x3, [fp, #-0x38]
    // 0xb50888: StoreField: r3->field_f = r0
    //     0xb50888: stur            w0, [x3, #0xf]
    // 0xb5088c: ldur            x0, [fp, #-0x48]
    // 0xb50890: StoreField: r3->field_b = r0
    //     0xb50890: stur            w0, [x3, #0xb]
    // 0xb50894: r1 = Null
    //     0xb50894: mov             x1, NULL
    // 0xb50898: r2 = 8
    //     0xb50898: movz            x2, #0x8
    // 0xb5089c: r0 = AllocateArray()
    //     0xb5089c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb508a0: mov             x2, x0
    // 0xb508a4: ldur            x0, [fp, #-0x28]
    // 0xb508a8: stur            x2, [fp, #-0x48]
    // 0xb508ac: StoreField: r2->field_f = r0
    //     0xb508ac: stur            w0, [x2, #0xf]
    // 0xb508b0: ldur            x0, [fp, #-0x30]
    // 0xb508b4: StoreField: r2->field_13 = r0
    //     0xb508b4: stur            w0, [x2, #0x13]
    // 0xb508b8: ldur            x0, [fp, #-0x40]
    // 0xb508bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb508bc: stur            w0, [x2, #0x17]
    // 0xb508c0: ldur            x0, [fp, #-0x38]
    // 0xb508c4: StoreField: r2->field_1b = r0
    //     0xb508c4: stur            w0, [x2, #0x1b]
    // 0xb508c8: r1 = <Widget>
    //     0xb508c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb508cc: r0 = AllocateGrowableArray()
    //     0xb508cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb508d0: mov             x1, x0
    // 0xb508d4: ldur            x0, [fp, #-0x48]
    // 0xb508d8: stur            x1, [fp, #-0x28]
    // 0xb508dc: StoreField: r1->field_f = r0
    //     0xb508dc: stur            w0, [x1, #0xf]
    // 0xb508e0: r0 = 8
    //     0xb508e0: movz            x0, #0x8
    // 0xb508e4: StoreField: r1->field_b = r0
    //     0xb508e4: stur            w0, [x1, #0xb]
    // 0xb508e8: r0 = Column()
    //     0xb508e8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb508ec: mov             x1, x0
    // 0xb508f0: r0 = Instance_Axis
    //     0xb508f0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb508f4: stur            x1, [fp, #-0x30]
    // 0xb508f8: StoreField: r1->field_f = r0
    //     0xb508f8: stur            w0, [x1, #0xf]
    // 0xb508fc: r2 = Instance_MainAxisAlignment
    //     0xb508fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb50900: ldr             x2, [x2, #0xa08]
    // 0xb50904: StoreField: r1->field_13 = r2
    //     0xb50904: stur            w2, [x1, #0x13]
    // 0xb50908: r3 = Instance_MainAxisSize
    //     0xb50908: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5090c: ldr             x3, [x3, #0xa10]
    // 0xb50910: ArrayStore: r1[0] = r3  ; List_4
    //     0xb50910: stur            w3, [x1, #0x17]
    // 0xb50914: r4 = Instance_CrossAxisAlignment
    //     0xb50914: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb50918: ldr             x4, [x4, #0x890]
    // 0xb5091c: StoreField: r1->field_1b = r4
    //     0xb5091c: stur            w4, [x1, #0x1b]
    // 0xb50920: r5 = Instance_VerticalDirection
    //     0xb50920: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb50924: ldr             x5, [x5, #0xa20]
    // 0xb50928: StoreField: r1->field_23 = r5
    //     0xb50928: stur            w5, [x1, #0x23]
    // 0xb5092c: r6 = Instance_Clip
    //     0xb5092c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb50930: ldr             x6, [x6, #0x38]
    // 0xb50934: StoreField: r1->field_2b = r6
    //     0xb50934: stur            w6, [x1, #0x2b]
    // 0xb50938: StoreField: r1->field_2f = rZR
    //     0xb50938: stur            xzr, [x1, #0x2f]
    // 0xb5093c: ldur            x7, [fp, #-0x28]
    // 0xb50940: StoreField: r1->field_b = r7
    //     0xb50940: stur            w7, [x1, #0xb]
    // 0xb50944: r0 = Card()
    //     0xb50944: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb50948: mov             x1, x0
    // 0xb5094c: r0 = 0.000000
    //     0xb5094c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb50950: stur            x1, [fp, #-0x28]
    // 0xb50954: ArrayStore: r1[0] = r0  ; List_4
    //     0xb50954: stur            w0, [x1, #0x17]
    // 0xb50958: ldur            x2, [fp, #-0x20]
    // 0xb5095c: StoreField: r1->field_1b = r2
    //     0xb5095c: stur            w2, [x1, #0x1b]
    // 0xb50960: r2 = true
    //     0xb50960: add             x2, NULL, #0x20  ; true
    // 0xb50964: StoreField: r1->field_1f = r2
    //     0xb50964: stur            w2, [x1, #0x1f]
    // 0xb50968: r3 = Instance_EdgeInsets
    //     0xb50968: add             x3, PP, #0x3f, lsl #12  ; [pp+0x3f340] Obj!EdgeInsets@d58161
    //     0xb5096c: ldr             x3, [x3, #0x340]
    // 0xb50970: StoreField: r1->field_27 = r3
    //     0xb50970: stur            w3, [x1, #0x27]
    // 0xb50974: ldur            x3, [fp, #-0x30]
    // 0xb50978: StoreField: r1->field_2f = r3
    //     0xb50978: stur            w3, [x1, #0x2f]
    // 0xb5097c: StoreField: r1->field_2b = r2
    //     0xb5097c: stur            w2, [x1, #0x2b]
    // 0xb50980: r3 = Instance__CardVariant
    //     0xb50980: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb50984: ldr             x3, [x3, #0xa68]
    // 0xb50988: StoreField: r1->field_33 = r3
    //     0xb50988: stur            w3, [x1, #0x33]
    // 0xb5098c: r0 = Radius()
    //     0xb5098c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb50990: d0 = 20.000000
    //     0xb50990: fmov            d0, #20.00000000
    // 0xb50994: stur            x0, [fp, #-0x20]
    // 0xb50998: StoreField: r0->field_7 = d0
    //     0xb50998: stur            d0, [x0, #7]
    // 0xb5099c: StoreField: r0->field_f = d0
    //     0xb5099c: stur            d0, [x0, #0xf]
    // 0xb509a0: r0 = BorderRadius()
    //     0xb509a0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb509a4: mov             x1, x0
    // 0xb509a8: ldur            x0, [fp, #-0x20]
    // 0xb509ac: stur            x1, [fp, #-0x30]
    // 0xb509b0: StoreField: r1->field_7 = r0
    //     0xb509b0: stur            w0, [x1, #7]
    // 0xb509b4: StoreField: r1->field_b = r0
    //     0xb509b4: stur            w0, [x1, #0xb]
    // 0xb509b8: StoreField: r1->field_f = r0
    //     0xb509b8: stur            w0, [x1, #0xf]
    // 0xb509bc: StoreField: r1->field_13 = r0
    //     0xb509bc: stur            w0, [x1, #0x13]
    // 0xb509c0: r0 = RoundedRectangleBorder()
    //     0xb509c0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb509c4: mov             x2, x0
    // 0xb509c8: ldur            x0, [fp, #-0x30]
    // 0xb509cc: stur            x2, [fp, #-0x20]
    // 0xb509d0: StoreField: r2->field_b = r0
    //     0xb509d0: stur            w0, [x2, #0xb]
    // 0xb509d4: r0 = Instance_BorderSide
    //     0xb509d4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb509d8: ldr             x0, [x0, #0xe20]
    // 0xb509dc: StoreField: r2->field_7 = r0
    //     0xb509dc: stur            w0, [x2, #7]
    // 0xb509e0: ldur            x0, [fp, #-8]
    // 0xb509e4: LoadField: r1 = r0->field_13
    //     0xb509e4: ldur            w1, [x0, #0x13]
    // 0xb509e8: DecompressPointer r1
    //     0xb509e8: add             x1, x1, HEAP, lsl #32
    // 0xb509ec: r0 = of()
    //     0xb509ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb509f0: LoadField: r1 = r0->field_87
    //     0xb509f0: ldur            w1, [x0, #0x87]
    // 0xb509f4: DecompressPointer r1
    //     0xb509f4: add             x1, x1, HEAP, lsl #32
    // 0xb509f8: LoadField: r0 = r1->field_7
    //     0xb509f8: ldur            w0, [x1, #7]
    // 0xb509fc: DecompressPointer r0
    //     0xb509fc: add             x0, x0, HEAP, lsl #32
    // 0xb50a00: stur            x0, [fp, #-0x30]
    // 0xb50a04: r1 = Instance_Color
    //     0xb50a04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb50a08: d0 = 0.700000
    //     0xb50a08: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb50a0c: ldr             d0, [x17, #0xf48]
    // 0xb50a10: r0 = withOpacity()
    //     0xb50a10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb50a14: r16 = 14.000000
    //     0xb50a14: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb50a18: ldr             x16, [x16, #0x1d8]
    // 0xb50a1c: stp             x0, x16, [SP]
    // 0xb50a20: ldur            x1, [fp, #-0x30]
    // 0xb50a24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb50a24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb50a28: ldr             x4, [x4, #0xaa0]
    // 0xb50a2c: r0 = copyWith()
    //     0xb50a2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb50a30: stur            x0, [fp, #-0x30]
    // 0xb50a34: r0 = Text()
    //     0xb50a34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb50a38: mov             x2, x0
    // 0xb50a3c: r0 = "Note:"
    //     0xb50a3c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33838] "Note:"
    //     0xb50a40: ldr             x0, [x0, #0x838]
    // 0xb50a44: stur            x2, [fp, #-0x38]
    // 0xb50a48: StoreField: r2->field_b = r0
    //     0xb50a48: stur            w0, [x2, #0xb]
    // 0xb50a4c: ldur            x0, [fp, #-0x30]
    // 0xb50a50: StoreField: r2->field_13 = r0
    //     0xb50a50: stur            w0, [x2, #0x13]
    // 0xb50a54: ldur            x0, [fp, #-8]
    // 0xb50a58: LoadField: r1 = r0->field_f
    //     0xb50a58: ldur            w1, [x0, #0xf]
    // 0xb50a5c: DecompressPointer r1
    //     0xb50a5c: add             x1, x1, HEAP, lsl #32
    // 0xb50a60: r0 = controller()
    //     0xb50a60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb50a64: LoadField: r1 = r0->field_57
    //     0xb50a64: ldur            w1, [x0, #0x57]
    // 0xb50a68: DecompressPointer r1
    //     0xb50a68: add             x1, x1, HEAP, lsl #32
    // 0xb50a6c: r0 = value()
    //     0xb50a6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb50a70: cmp             w0, NULL
    // 0xb50a74: b.ne            #0xb50a80
    // 0xb50a78: r0 = Null
    //     0xb50a78: mov             x0, NULL
    // 0xb50a7c: b               #0xb50a8c
    // 0xb50a80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb50a80: ldur            w1, [x0, #0x17]
    // 0xb50a84: DecompressPointer r1
    //     0xb50a84: add             x1, x1, HEAP, lsl #32
    // 0xb50a88: mov             x0, x1
    // 0xb50a8c: cmp             w0, NULL
    // 0xb50a90: b.ne            #0xb50a9c
    // 0xb50a94: r6 = ""
    //     0xb50a94: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb50a98: b               #0xb50aa0
    // 0xb50a9c: mov             x6, x0
    // 0xb50aa0: ldur            x1, [fp, #-8]
    // 0xb50aa4: ldur            x4, [fp, #-0x10]
    // 0xb50aa8: ldur            x3, [fp, #-0x28]
    // 0xb50aac: ldur            x2, [fp, #-0x20]
    // 0xb50ab0: ldur            x0, [fp, #-0x38]
    // 0xb50ab4: ldur            x5, [fp, #-0x18]
    // 0xb50ab8: stur            x6, [fp, #-0x30]
    // 0xb50abc: LoadField: r7 = r1->field_13
    //     0xb50abc: ldur            w7, [x1, #0x13]
    // 0xb50ac0: DecompressPointer r7
    //     0xb50ac0: add             x7, x7, HEAP, lsl #32
    // 0xb50ac4: mov             x1, x7
    // 0xb50ac8: r0 = of()
    //     0xb50ac8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb50acc: LoadField: r1 = r0->field_87
    //     0xb50acc: ldur            w1, [x0, #0x87]
    // 0xb50ad0: DecompressPointer r1
    //     0xb50ad0: add             x1, x1, HEAP, lsl #32
    // 0xb50ad4: LoadField: r0 = r1->field_2b
    //     0xb50ad4: ldur            w0, [x1, #0x2b]
    // 0xb50ad8: DecompressPointer r0
    //     0xb50ad8: add             x0, x0, HEAP, lsl #32
    // 0xb50adc: stur            x0, [fp, #-8]
    // 0xb50ae0: r1 = Instance_Color
    //     0xb50ae0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb50ae4: d0 = 0.700000
    //     0xb50ae4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb50ae8: ldr             d0, [x17, #0xf48]
    // 0xb50aec: r0 = withOpacity()
    //     0xb50aec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb50af0: r16 = 12.000000
    //     0xb50af0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb50af4: ldr             x16, [x16, #0x9e8]
    // 0xb50af8: stp             x0, x16, [SP]
    // 0xb50afc: ldur            x1, [fp, #-8]
    // 0xb50b00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb50b00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb50b04: ldr             x4, [x4, #0xaa0]
    // 0xb50b08: r0 = copyWith()
    //     0xb50b08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb50b0c: stur            x0, [fp, #-8]
    // 0xb50b10: r0 = Text()
    //     0xb50b10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb50b14: mov             x1, x0
    // 0xb50b18: ldur            x0, [fp, #-0x30]
    // 0xb50b1c: stur            x1, [fp, #-0x40]
    // 0xb50b20: StoreField: r1->field_b = r0
    //     0xb50b20: stur            w0, [x1, #0xb]
    // 0xb50b24: ldur            x0, [fp, #-8]
    // 0xb50b28: StoreField: r1->field_13 = r0
    //     0xb50b28: stur            w0, [x1, #0x13]
    // 0xb50b2c: r0 = Padding()
    //     0xb50b2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50b30: mov             x3, x0
    // 0xb50b34: r0 = Instance_EdgeInsets
    //     0xb50b34: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f348] Obj!EdgeInsets@d59271
    //     0xb50b38: ldr             x0, [x0, #0x348]
    // 0xb50b3c: stur            x3, [fp, #-8]
    // 0xb50b40: StoreField: r3->field_f = r0
    //     0xb50b40: stur            w0, [x3, #0xf]
    // 0xb50b44: ldur            x0, [fp, #-0x40]
    // 0xb50b48: StoreField: r3->field_b = r0
    //     0xb50b48: stur            w0, [x3, #0xb]
    // 0xb50b4c: r1 = Null
    //     0xb50b4c: mov             x1, NULL
    // 0xb50b50: r2 = 4
    //     0xb50b50: movz            x2, #0x4
    // 0xb50b54: r0 = AllocateArray()
    //     0xb50b54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb50b58: mov             x2, x0
    // 0xb50b5c: ldur            x0, [fp, #-0x38]
    // 0xb50b60: stur            x2, [fp, #-0x30]
    // 0xb50b64: StoreField: r2->field_f = r0
    //     0xb50b64: stur            w0, [x2, #0xf]
    // 0xb50b68: ldur            x0, [fp, #-8]
    // 0xb50b6c: StoreField: r2->field_13 = r0
    //     0xb50b6c: stur            w0, [x2, #0x13]
    // 0xb50b70: r1 = <Widget>
    //     0xb50b70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb50b74: r0 = AllocateGrowableArray()
    //     0xb50b74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb50b78: mov             x1, x0
    // 0xb50b7c: ldur            x0, [fp, #-0x30]
    // 0xb50b80: stur            x1, [fp, #-8]
    // 0xb50b84: StoreField: r1->field_f = r0
    //     0xb50b84: stur            w0, [x1, #0xf]
    // 0xb50b88: r2 = 4
    //     0xb50b88: movz            x2, #0x4
    // 0xb50b8c: StoreField: r1->field_b = r2
    //     0xb50b8c: stur            w2, [x1, #0xb]
    // 0xb50b90: r0 = Column()
    //     0xb50b90: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb50b94: mov             x1, x0
    // 0xb50b98: r0 = Instance_Axis
    //     0xb50b98: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb50b9c: stur            x1, [fp, #-0x30]
    // 0xb50ba0: StoreField: r1->field_f = r0
    //     0xb50ba0: stur            w0, [x1, #0xf]
    // 0xb50ba4: r2 = Instance_MainAxisAlignment
    //     0xb50ba4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb50ba8: ldr             x2, [x2, #0xa08]
    // 0xb50bac: StoreField: r1->field_13 = r2
    //     0xb50bac: stur            w2, [x1, #0x13]
    // 0xb50bb0: r3 = Instance_MainAxisSize
    //     0xb50bb0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb50bb4: ldr             x3, [x3, #0xa10]
    // 0xb50bb8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb50bb8: stur            w3, [x1, #0x17]
    // 0xb50bbc: r4 = Instance_CrossAxisAlignment
    //     0xb50bbc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb50bc0: ldr             x4, [x4, #0x890]
    // 0xb50bc4: StoreField: r1->field_1b = r4
    //     0xb50bc4: stur            w4, [x1, #0x1b]
    // 0xb50bc8: r5 = Instance_VerticalDirection
    //     0xb50bc8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb50bcc: ldr             x5, [x5, #0xa20]
    // 0xb50bd0: StoreField: r1->field_23 = r5
    //     0xb50bd0: stur            w5, [x1, #0x23]
    // 0xb50bd4: r6 = Instance_Clip
    //     0xb50bd4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb50bd8: ldr             x6, [x6, #0x38]
    // 0xb50bdc: StoreField: r1->field_2b = r6
    //     0xb50bdc: stur            w6, [x1, #0x2b]
    // 0xb50be0: StoreField: r1->field_2f = rZR
    //     0xb50be0: stur            xzr, [x1, #0x2f]
    // 0xb50be4: ldur            x7, [fp, #-8]
    // 0xb50be8: StoreField: r1->field_b = r7
    //     0xb50be8: stur            w7, [x1, #0xb]
    // 0xb50bec: r0 = Padding()
    //     0xb50bec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50bf0: mov             x1, x0
    // 0xb50bf4: r0 = Instance_EdgeInsets
    //     0xb50bf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb50bf8: ldr             x0, [x0, #0x1f0]
    // 0xb50bfc: stur            x1, [fp, #-8]
    // 0xb50c00: StoreField: r1->field_f = r0
    //     0xb50c00: stur            w0, [x1, #0xf]
    // 0xb50c04: ldur            x2, [fp, #-0x30]
    // 0xb50c08: StoreField: r1->field_b = r2
    //     0xb50c08: stur            w2, [x1, #0xb]
    // 0xb50c0c: r0 = Card()
    //     0xb50c0c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb50c10: mov             x3, x0
    // 0xb50c14: r0 = 0.000000
    //     0xb50c14: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb50c18: stur            x3, [fp, #-0x30]
    // 0xb50c1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb50c1c: stur            w0, [x3, #0x17]
    // 0xb50c20: ldur            x0, [fp, #-0x20]
    // 0xb50c24: StoreField: r3->field_1b = r0
    //     0xb50c24: stur            w0, [x3, #0x1b]
    // 0xb50c28: r0 = true
    //     0xb50c28: add             x0, NULL, #0x20  ; true
    // 0xb50c2c: StoreField: r3->field_1f = r0
    //     0xb50c2c: stur            w0, [x3, #0x1f]
    // 0xb50c30: r1 = Instance_EdgeInsets
    //     0xb50c30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb50c34: ldr             x1, [x1, #0xb0]
    // 0xb50c38: StoreField: r3->field_27 = r1
    //     0xb50c38: stur            w1, [x3, #0x27]
    // 0xb50c3c: ldur            x1, [fp, #-8]
    // 0xb50c40: StoreField: r3->field_2f = r1
    //     0xb50c40: stur            w1, [x3, #0x2f]
    // 0xb50c44: StoreField: r3->field_2b = r0
    //     0xb50c44: stur            w0, [x3, #0x2b]
    // 0xb50c48: r0 = Instance__CardVariant
    //     0xb50c48: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb50c4c: ldr             x0, [x0, #0xa68]
    // 0xb50c50: StoreField: r3->field_33 = r0
    //     0xb50c50: stur            w0, [x3, #0x33]
    // 0xb50c54: r1 = Null
    //     0xb50c54: mov             x1, NULL
    // 0xb50c58: r2 = 4
    //     0xb50c58: movz            x2, #0x4
    // 0xb50c5c: r0 = AllocateArray()
    //     0xb50c5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb50c60: mov             x2, x0
    // 0xb50c64: ldur            x0, [fp, #-0x28]
    // 0xb50c68: stur            x2, [fp, #-8]
    // 0xb50c6c: StoreField: r2->field_f = r0
    //     0xb50c6c: stur            w0, [x2, #0xf]
    // 0xb50c70: ldur            x0, [fp, #-0x30]
    // 0xb50c74: StoreField: r2->field_13 = r0
    //     0xb50c74: stur            w0, [x2, #0x13]
    // 0xb50c78: r1 = <Widget>
    //     0xb50c78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb50c7c: r0 = AllocateGrowableArray()
    //     0xb50c7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb50c80: mov             x1, x0
    // 0xb50c84: ldur            x0, [fp, #-8]
    // 0xb50c88: stur            x1, [fp, #-0x20]
    // 0xb50c8c: StoreField: r1->field_f = r0
    //     0xb50c8c: stur            w0, [x1, #0xf]
    // 0xb50c90: r0 = 4
    //     0xb50c90: movz            x0, #0x4
    // 0xb50c94: StoreField: r1->field_b = r0
    //     0xb50c94: stur            w0, [x1, #0xb]
    // 0xb50c98: r0 = Column()
    //     0xb50c98: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb50c9c: mov             x1, x0
    // 0xb50ca0: r0 = Instance_Axis
    //     0xb50ca0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb50ca4: stur            x1, [fp, #-8]
    // 0xb50ca8: StoreField: r1->field_f = r0
    //     0xb50ca8: stur            w0, [x1, #0xf]
    // 0xb50cac: r2 = Instance_MainAxisAlignment
    //     0xb50cac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb50cb0: ldr             x2, [x2, #0xa08]
    // 0xb50cb4: StoreField: r1->field_13 = r2
    //     0xb50cb4: stur            w2, [x1, #0x13]
    // 0xb50cb8: r3 = Instance_MainAxisSize
    //     0xb50cb8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb50cbc: ldr             x3, [x3, #0xdd0]
    // 0xb50cc0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb50cc0: stur            w3, [x1, #0x17]
    // 0xb50cc4: r3 = Instance_CrossAxisAlignment
    //     0xb50cc4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb50cc8: ldr             x3, [x3, #0x890]
    // 0xb50ccc: StoreField: r1->field_1b = r3
    //     0xb50ccc: stur            w3, [x1, #0x1b]
    // 0xb50cd0: r4 = Instance_VerticalDirection
    //     0xb50cd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb50cd4: ldr             x4, [x4, #0xa20]
    // 0xb50cd8: StoreField: r1->field_23 = r4
    //     0xb50cd8: stur            w4, [x1, #0x23]
    // 0xb50cdc: r5 = Instance_Clip
    //     0xb50cdc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb50ce0: ldr             x5, [x5, #0x38]
    // 0xb50ce4: StoreField: r1->field_2b = r5
    //     0xb50ce4: stur            w5, [x1, #0x2b]
    // 0xb50ce8: StoreField: r1->field_2f = rZR
    //     0xb50ce8: stur            xzr, [x1, #0x2f]
    // 0xb50cec: ldur            x6, [fp, #-0x20]
    // 0xb50cf0: StoreField: r1->field_b = r6
    //     0xb50cf0: stur            w6, [x1, #0xb]
    // 0xb50cf4: r0 = Visibility()
    //     0xb50cf4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb50cf8: mov             x2, x0
    // 0xb50cfc: ldur            x0, [fp, #-8]
    // 0xb50d00: stur            x2, [fp, #-0x20]
    // 0xb50d04: StoreField: r2->field_b = r0
    //     0xb50d04: stur            w0, [x2, #0xb]
    // 0xb50d08: r0 = Instance_SizedBox
    //     0xb50d08: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb50d0c: StoreField: r2->field_f = r0
    //     0xb50d0c: stur            w0, [x2, #0xf]
    // 0xb50d10: ldur            x0, [fp, #-0x10]
    // 0xb50d14: StoreField: r2->field_13 = r0
    //     0xb50d14: stur            w0, [x2, #0x13]
    // 0xb50d18: r0 = false
    //     0xb50d18: add             x0, NULL, #0x30  ; false
    // 0xb50d1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb50d1c: stur            w0, [x2, #0x17]
    // 0xb50d20: StoreField: r2->field_1b = r0
    //     0xb50d20: stur            w0, [x2, #0x1b]
    // 0xb50d24: StoreField: r2->field_1f = r0
    //     0xb50d24: stur            w0, [x2, #0x1f]
    // 0xb50d28: StoreField: r2->field_23 = r0
    //     0xb50d28: stur            w0, [x2, #0x23]
    // 0xb50d2c: StoreField: r2->field_27 = r0
    //     0xb50d2c: stur            w0, [x2, #0x27]
    // 0xb50d30: StoreField: r2->field_2b = r0
    //     0xb50d30: stur            w0, [x2, #0x2b]
    // 0xb50d34: ldur            x0, [fp, #-0x18]
    // 0xb50d38: LoadField: r1 = r0->field_b
    //     0xb50d38: ldur            w1, [x0, #0xb]
    // 0xb50d3c: LoadField: r3 = r0->field_f
    //     0xb50d3c: ldur            w3, [x0, #0xf]
    // 0xb50d40: DecompressPointer r3
    //     0xb50d40: add             x3, x3, HEAP, lsl #32
    // 0xb50d44: LoadField: r4 = r3->field_b
    //     0xb50d44: ldur            w4, [x3, #0xb]
    // 0xb50d48: r3 = LoadInt32Instr(r1)
    //     0xb50d48: sbfx            x3, x1, #1, #0x1f
    // 0xb50d4c: stur            x3, [fp, #-0x50]
    // 0xb50d50: r1 = LoadInt32Instr(r4)
    //     0xb50d50: sbfx            x1, x4, #1, #0x1f
    // 0xb50d54: cmp             x3, x1
    // 0xb50d58: b.ne            #0xb50d64
    // 0xb50d5c: mov             x1, x0
    // 0xb50d60: r0 = _growToNextCapacity()
    //     0xb50d60: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb50d64: ldur            x2, [fp, #-0x18]
    // 0xb50d68: ldur            x3, [fp, #-0x50]
    // 0xb50d6c: add             x0, x3, #1
    // 0xb50d70: lsl             x1, x0, #1
    // 0xb50d74: StoreField: r2->field_b = r1
    //     0xb50d74: stur            w1, [x2, #0xb]
    // 0xb50d78: LoadField: r1 = r2->field_f
    //     0xb50d78: ldur            w1, [x2, #0xf]
    // 0xb50d7c: DecompressPointer r1
    //     0xb50d7c: add             x1, x1, HEAP, lsl #32
    // 0xb50d80: ldur            x0, [fp, #-0x20]
    // 0xb50d84: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb50d84: add             x25, x1, x3, lsl #2
    //     0xb50d88: add             x25, x25, #0xf
    //     0xb50d8c: str             w0, [x25]
    //     0xb50d90: tbz             w0, #0, #0xb50dac
    //     0xb50d94: ldurb           w16, [x1, #-1]
    //     0xb50d98: ldurb           w17, [x0, #-1]
    //     0xb50d9c: and             x16, x17, x16, lsr #2
    //     0xb50da0: tst             x16, HEAP, lsr #32
    //     0xb50da4: b.eq            #0xb50dac
    //     0xb50da8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb50dac: r0 = Column()
    //     0xb50dac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb50db0: mov             x1, x0
    // 0xb50db4: r0 = Instance_Axis
    //     0xb50db4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb50db8: stur            x1, [fp, #-8]
    // 0xb50dbc: StoreField: r1->field_f = r0
    //     0xb50dbc: stur            w0, [x1, #0xf]
    // 0xb50dc0: r0 = Instance_MainAxisAlignment
    //     0xb50dc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb50dc4: ldr             x0, [x0, #0xa08]
    // 0xb50dc8: StoreField: r1->field_13 = r0
    //     0xb50dc8: stur            w0, [x1, #0x13]
    // 0xb50dcc: r0 = Instance_MainAxisSize
    //     0xb50dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb50dd0: ldr             x0, [x0, #0xa10]
    // 0xb50dd4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb50dd4: stur            w0, [x1, #0x17]
    // 0xb50dd8: r0 = Instance_CrossAxisAlignment
    //     0xb50dd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb50ddc: ldr             x0, [x0, #0x890]
    // 0xb50de0: StoreField: r1->field_1b = r0
    //     0xb50de0: stur            w0, [x1, #0x1b]
    // 0xb50de4: r0 = Instance_VerticalDirection
    //     0xb50de4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb50de8: ldr             x0, [x0, #0xa20]
    // 0xb50dec: StoreField: r1->field_23 = r0
    //     0xb50dec: stur            w0, [x1, #0x23]
    // 0xb50df0: r0 = Instance_Clip
    //     0xb50df0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb50df4: ldr             x0, [x0, #0x38]
    // 0xb50df8: StoreField: r1->field_2b = r0
    //     0xb50df8: stur            w0, [x1, #0x2b]
    // 0xb50dfc: StoreField: r1->field_2f = rZR
    //     0xb50dfc: stur            xzr, [x1, #0x2f]
    // 0xb50e00: ldur            x0, [fp, #-0x18]
    // 0xb50e04: StoreField: r1->field_b = r0
    //     0xb50e04: stur            w0, [x1, #0xb]
    // 0xb50e08: r0 = Padding()
    //     0xb50e08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb50e0c: mov             x3, x0
    // 0xb50e10: r0 = Instance_EdgeInsets
    //     0xb50e10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb50e14: ldr             x0, [x0, #0x1f0]
    // 0xb50e18: stur            x3, [fp, #-0x10]
    // 0xb50e1c: StoreField: r3->field_f = r0
    //     0xb50e1c: stur            w0, [x3, #0xf]
    // 0xb50e20: ldur            x0, [fp, #-8]
    // 0xb50e24: StoreField: r3->field_b = r0
    //     0xb50e24: stur            w0, [x3, #0xb]
    // 0xb50e28: r1 = Null
    //     0xb50e28: mov             x1, NULL
    // 0xb50e2c: r2 = 2
    //     0xb50e2c: movz            x2, #0x2
    // 0xb50e30: r0 = AllocateArray()
    //     0xb50e30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb50e34: mov             x2, x0
    // 0xb50e38: ldur            x0, [fp, #-0x10]
    // 0xb50e3c: stur            x2, [fp, #-8]
    // 0xb50e40: StoreField: r2->field_f = r0
    //     0xb50e40: stur            w0, [x2, #0xf]
    // 0xb50e44: r1 = <Widget>
    //     0xb50e44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb50e48: r0 = AllocateGrowableArray()
    //     0xb50e48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb50e4c: mov             x1, x0
    // 0xb50e50: ldur            x0, [fp, #-8]
    // 0xb50e54: stur            x1, [fp, #-0x10]
    // 0xb50e58: StoreField: r1->field_f = r0
    //     0xb50e58: stur            w0, [x1, #0xf]
    // 0xb50e5c: r0 = 2
    //     0xb50e5c: movz            x0, #0x2
    // 0xb50e60: StoreField: r1->field_b = r0
    //     0xb50e60: stur            w0, [x1, #0xb]
    // 0xb50e64: r0 = ListView()
    //     0xb50e64: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb50e68: stur            x0, [fp, #-8]
    // 0xb50e6c: r16 = true
    //     0xb50e6c: add             x16, NULL, #0x20  ; true
    // 0xb50e70: r30 = true
    //     0xb50e70: add             lr, NULL, #0x20  ; true
    // 0xb50e74: stp             lr, x16, [SP, #8]
    // 0xb50e78: r16 = Instance_ScrollPhysics
    //     0xb50e78: add             x16, PP, #0x34, lsl #12  ; [pp+0x34010] Obj!ScrollPhysics@d558a1
    //     0xb50e7c: ldr             x16, [x16, #0x10]
    // 0xb50e80: str             x16, [SP]
    // 0xb50e84: mov             x1, x0
    // 0xb50e88: ldur            x2, [fp, #-0x10]
    // 0xb50e8c: r4 = const [0, 0x5, 0x3, 0x2, physics, 0x4, primary, 0x2, shrinkWrap, 0x3, null]
    //     0xb50e8c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34018] List(11) [0, 0x5, 0x3, 0x2, "physics", 0x4, "primary", 0x2, "shrinkWrap", 0x3, Null]
    //     0xb50e90: ldr             x4, [x4, #0x18]
    // 0xb50e94: r0 = ListView()
    //     0xb50e94: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb50e98: ldur            x0, [fp, #-8]
    // 0xb50e9c: b               #0xb50eb8
    // 0xb50ea0: r0 = Container()
    //     0xb50ea0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb50ea4: mov             x1, x0
    // 0xb50ea8: stur            x0, [fp, #-8]
    // 0xb50eac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb50eac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb50eb0: r0 = Container()
    //     0xb50eb0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb50eb4: ldur            x0, [fp, #-8]
    // 0xb50eb8: LeaveFrame
    //     0xb50eb8: mov             SP, fp
    //     0xb50ebc: ldp             fp, lr, [SP], #0x10
    // 0xb50ec0: ret
    //     0xb50ec0: ret             
    // 0xb50ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb50ec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb50ec8: b               #0xb4ef44
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb50ecc, size: 0xc4
    // 0xb50ecc: EnterFrame
    //     0xb50ecc: stp             fp, lr, [SP, #-0x10]!
    //     0xb50ed0: mov             fp, SP
    // 0xb50ed4: ldr             x0, [fp, #0x18]
    // 0xb50ed8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb50ed8: ldur            w1, [x0, #0x17]
    // 0xb50edc: DecompressPointer r1
    //     0xb50edc: add             x1, x1, HEAP, lsl #32
    // 0xb50ee0: CheckStackOverflow
    //     0xb50ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb50ee4: cmp             SP, x16
    //     0xb50ee8: b.ls            #0xb50f88
    // 0xb50eec: ldr             x0, [fp, #0x10]
    // 0xb50ef0: cmp             w0, NULL
    // 0xb50ef4: b.ne            #0xb50f00
    // 0xb50ef8: r2 = Null
    //     0xb50ef8: mov             x2, NULL
    // 0xb50efc: b               #0xb50f04
    // 0xb50f00: LoadField: r2 = r0->field_7
    //     0xb50f00: ldur            w2, [x0, #7]
    // 0xb50f04: cmp             w2, NULL
    // 0xb50f08: b.ne            #0xb50f14
    // 0xb50f0c: r2 = 0
    //     0xb50f0c: movz            x2, #0
    // 0xb50f10: b               #0xb50f1c
    // 0xb50f14: r3 = LoadInt32Instr(r2)
    //     0xb50f14: sbfx            x3, x2, #1, #0x1f
    // 0xb50f18: mov             x2, x3
    // 0xb50f1c: cmp             x2, #0xa
    // 0xb50f20: b.gt            #0xb50f54
    // 0xb50f24: cmp             w0, NULL
    // 0xb50f28: b.ne            #0xb50f34
    // 0xb50f2c: r0 = Null
    //     0xb50f2c: mov             x0, NULL
    // 0xb50f30: b               #0xb50f48
    // 0xb50f34: LoadField: r2 = r0->field_7
    //     0xb50f34: ldur            w2, [x0, #7]
    // 0xb50f38: cbz             w2, #0xb50f44
    // 0xb50f3c: r0 = false
    //     0xb50f3c: add             x0, NULL, #0x30  ; false
    // 0xb50f40: b               #0xb50f48
    // 0xb50f44: r0 = true
    //     0xb50f44: add             x0, NULL, #0x20  ; true
    // 0xb50f48: cmp             w0, NULL
    // 0xb50f4c: b.eq            #0xb50f54
    // 0xb50f50: tbnz            w0, #4, #0xb50f68
    // 0xb50f54: LoadField: r0 = r1->field_f
    //     0xb50f54: ldur            w0, [x1, #0xf]
    // 0xb50f58: DecompressPointer r0
    //     0xb50f58: add             x0, x0, HEAP, lsl #32
    // 0xb50f5c: mov             x1, x0
    // 0xb50f60: r0 = isButtonEnabled()
    //     0xb50f60: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0xb50f64: b               #0xb50f78
    // 0xb50f68: LoadField: r0 = r1->field_f
    //     0xb50f68: ldur            w0, [x1, #0xf]
    // 0xb50f6c: DecompressPointer r0
    //     0xb50f6c: add             x0, x0, HEAP, lsl #32
    // 0xb50f70: mov             x1, x0
    // 0xb50f74: r0 = isButtonEnabled()
    //     0xb50f74: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0xb50f78: r0 = Null
    //     0xb50f78: mov             x0, NULL
    // 0xb50f7c: LeaveFrame
    //     0xb50f7c: mov             SP, fp
    //     0xb50f80: ldp             fp, lr, [SP], #0x10
    // 0xb50f84: ret
    //     0xb50f84: ret             
    // 0xb50f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb50f88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb50f8c: b               #0xb50eec
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb511e0, size: 0xcc
    // 0xb511e0: EnterFrame
    //     0xb511e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb511e4: mov             fp, SP
    // 0xb511e8: AllocStack(0x18)
    //     0xb511e8: sub             SP, SP, #0x18
    // 0xb511ec: SetupParameters()
    //     0xb511ec: ldr             x0, [fp, #0x20]
    //     0xb511f0: ldur            w1, [x0, #0x17]
    //     0xb511f4: add             x1, x1, HEAP, lsl #32
    //     0xb511f8: stur            x1, [fp, #-8]
    // 0xb511fc: r1 = 2
    //     0xb511fc: movz            x1, #0x2
    // 0xb51200: r0 = AllocateContext()
    //     0xb51200: bl              #0x16f6108  ; AllocateContextStub
    // 0xb51204: mov             x1, x0
    // 0xb51208: ldur            x0, [fp, #-8]
    // 0xb5120c: stur            x1, [fp, #-0x10]
    // 0xb51210: StoreField: r1->field_b = r0
    //     0xb51210: stur            w0, [x1, #0xb]
    // 0xb51214: ldr             x0, [fp, #0x18]
    // 0xb51218: StoreField: r1->field_f = r0
    //     0xb51218: stur            w0, [x1, #0xf]
    // 0xb5121c: ldr             x0, [fp, #0x10]
    // 0xb51220: StoreField: r1->field_13 = r0
    //     0xb51220: stur            w0, [x1, #0x13]
    // 0xb51224: r0 = Obx()
    //     0xb51224: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb51228: ldur            x2, [fp, #-0x10]
    // 0xb5122c: r1 = Function '<anonymous closure>':.
    //     0xb5122c: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f450] AnonymousClosure: (0xb513d0), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb51230: ldr             x1, [x1, #0x450]
    // 0xb51234: stur            x0, [fp, #-8]
    // 0xb51238: r0 = AllocateClosure()
    //     0xb51238: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5123c: mov             x1, x0
    // 0xb51240: ldur            x0, [fp, #-8]
    // 0xb51244: StoreField: r0->field_b = r1
    //     0xb51244: stur            w1, [x0, #0xb]
    // 0xb51248: r0 = InkWell()
    //     0xb51248: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5124c: mov             x3, x0
    // 0xb51250: ldur            x0, [fp, #-8]
    // 0xb51254: stur            x3, [fp, #-0x18]
    // 0xb51258: StoreField: r3->field_b = r0
    //     0xb51258: stur            w0, [x3, #0xb]
    // 0xb5125c: ldur            x2, [fp, #-0x10]
    // 0xb51260: r1 = Function '<anonymous closure>':.
    //     0xb51260: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f458] AnonymousClosure: (0xb512ac), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb51264: ldr             x1, [x1, #0x458]
    // 0xb51268: r0 = AllocateClosure()
    //     0xb51268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5126c: mov             x1, x0
    // 0xb51270: ldur            x0, [fp, #-0x18]
    // 0xb51274: StoreField: r0->field_f = r1
    //     0xb51274: stur            w1, [x0, #0xf]
    // 0xb51278: r1 = true
    //     0xb51278: add             x1, NULL, #0x20  ; true
    // 0xb5127c: StoreField: r0->field_43 = r1
    //     0xb5127c: stur            w1, [x0, #0x43]
    // 0xb51280: r2 = Instance_BoxShape
    //     0xb51280: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb51284: ldr             x2, [x2, #0x80]
    // 0xb51288: StoreField: r0->field_47 = r2
    //     0xb51288: stur            w2, [x0, #0x47]
    // 0xb5128c: StoreField: r0->field_6f = r1
    //     0xb5128c: stur            w1, [x0, #0x6f]
    // 0xb51290: r2 = false
    //     0xb51290: add             x2, NULL, #0x30  ; false
    // 0xb51294: StoreField: r0->field_73 = r2
    //     0xb51294: stur            w2, [x0, #0x73]
    // 0xb51298: StoreField: r0->field_83 = r1
    //     0xb51298: stur            w1, [x0, #0x83]
    // 0xb5129c: StoreField: r0->field_7b = r2
    //     0xb5129c: stur            w2, [x0, #0x7b]
    // 0xb512a0: LeaveFrame
    //     0xb512a0: mov             SP, fp
    //     0xb512a4: ldp             fp, lr, [SP], #0x10
    // 0xb512a8: ret
    //     0xb512a8: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb512ac, size: 0x124
    // 0xb512ac: EnterFrame
    //     0xb512ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb512b0: mov             fp, SP
    // 0xb512b4: AllocStack(0x18)
    //     0xb512b4: sub             SP, SP, #0x18
    // 0xb512b8: SetupParameters()
    //     0xb512b8: ldr             x0, [fp, #0x10]
    //     0xb512bc: ldur            w2, [x0, #0x17]
    //     0xb512c0: add             x2, x2, HEAP, lsl #32
    //     0xb512c4: stur            x2, [fp, #-0x10]
    // 0xb512c8: CheckStackOverflow
    //     0xb512c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb512cc: cmp             SP, x16
    //     0xb512d0: b.ls            #0xb513c4
    // 0xb512d4: LoadField: r0 = r2->field_b
    //     0xb512d4: ldur            w0, [x2, #0xb]
    // 0xb512d8: DecompressPointer r0
    //     0xb512d8: add             x0, x0, HEAP, lsl #32
    // 0xb512dc: stur            x0, [fp, #-8]
    // 0xb512e0: LoadField: r1 = r0->field_f
    //     0xb512e0: ldur            w1, [x0, #0xf]
    // 0xb512e4: DecompressPointer r1
    //     0xb512e4: add             x1, x1, HEAP, lsl #32
    // 0xb512e8: r0 = controller()
    //     0xb512e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb512ec: LoadField: r1 = r0->field_db
    //     0xb512ec: ldur            w1, [x0, #0xdb]
    // 0xb512f0: DecompressPointer r1
    //     0xb512f0: add             x1, x1, HEAP, lsl #32
    // 0xb512f4: r2 = ""
    //     0xb512f4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb512f8: r0 = text=()
    //     0xb512f8: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xb512fc: ldur            x0, [fp, #-8]
    // 0xb51300: LoadField: r1 = r0->field_f
    //     0xb51300: ldur            w1, [x0, #0xf]
    // 0xb51304: DecompressPointer r1
    //     0xb51304: add             x1, x1, HEAP, lsl #32
    // 0xb51308: r0 = controller()
    //     0xb51308: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb5130c: LoadField: r2 = r0->field_67
    //     0xb5130c: ldur            w2, [x0, #0x67]
    // 0xb51310: DecompressPointer r2
    //     0xb51310: add             x2, x2, HEAP, lsl #32
    // 0xb51314: ldur            x0, [fp, #-8]
    // 0xb51318: stur            x2, [fp, #-0x18]
    // 0xb5131c: LoadField: r1 = r0->field_f
    //     0xb5131c: ldur            w1, [x0, #0xf]
    // 0xb51320: DecompressPointer r1
    //     0xb51320: add             x1, x1, HEAP, lsl #32
    // 0xb51324: r0 = controller()
    //     0xb51324: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb51328: LoadField: r1 = r0->field_57
    //     0xb51328: ldur            w1, [x0, #0x57]
    // 0xb5132c: DecompressPointer r1
    //     0xb5132c: add             x1, x1, HEAP, lsl #32
    // 0xb51330: r0 = value()
    //     0xb51330: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb51334: cmp             w0, NULL
    // 0xb51338: b.ne            #0xb51344
    // 0xb5133c: r2 = Null
    //     0xb5133c: mov             x2, NULL
    // 0xb51340: b               #0xb51398
    // 0xb51344: ldur            x1, [fp, #-0x10]
    // 0xb51348: LoadField: r2 = r0->field_7
    //     0xb51348: ldur            w2, [x0, #7]
    // 0xb5134c: DecompressPointer r2
    //     0xb5134c: add             x2, x2, HEAP, lsl #32
    // 0xb51350: LoadField: r0 = r1->field_13
    //     0xb51350: ldur            w0, [x1, #0x13]
    // 0xb51354: DecompressPointer r0
    //     0xb51354: add             x0, x0, HEAP, lsl #32
    // 0xb51358: LoadField: r1 = r2->field_b
    //     0xb51358: ldur            w1, [x2, #0xb]
    // 0xb5135c: r3 = LoadInt32Instr(r0)
    //     0xb5135c: sbfx            x3, x0, #1, #0x1f
    //     0xb51360: tbz             w0, #0, #0xb51368
    //     0xb51364: ldur            x3, [x0, #7]
    // 0xb51368: r0 = LoadInt32Instr(r1)
    //     0xb51368: sbfx            x0, x1, #1, #0x1f
    // 0xb5136c: mov             x1, x3
    // 0xb51370: cmp             x1, x0
    // 0xb51374: b.hs            #0xb513cc
    // 0xb51378: LoadField: r0 = r2->field_f
    //     0xb51378: ldur            w0, [x2, #0xf]
    // 0xb5137c: DecompressPointer r0
    //     0xb5137c: add             x0, x0, HEAP, lsl #32
    // 0xb51380: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb51380: add             x16, x0, x3, lsl #2
    //     0xb51384: ldur            w1, [x16, #0xf]
    // 0xb51388: DecompressPointer r1
    //     0xb51388: add             x1, x1, HEAP, lsl #32
    // 0xb5138c: LoadField: r0 = r1->field_b
    //     0xb5138c: ldur            w0, [x1, #0xb]
    // 0xb51390: DecompressPointer r0
    //     0xb51390: add             x0, x0, HEAP, lsl #32
    // 0xb51394: mov             x2, x0
    // 0xb51398: ldur            x0, [fp, #-8]
    // 0xb5139c: ldur            x1, [fp, #-0x18]
    // 0xb513a0: r0 = value=()
    //     0xb513a0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb513a4: ldur            x0, [fp, #-8]
    // 0xb513a8: LoadField: r1 = r0->field_f
    //     0xb513a8: ldur            w1, [x0, #0xf]
    // 0xb513ac: DecompressPointer r1
    //     0xb513ac: add             x1, x1, HEAP, lsl #32
    // 0xb513b0: r0 = isButtonEnabled()
    //     0xb513b0: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0xb513b4: r0 = Null
    //     0xb513b4: mov             x0, NULL
    // 0xb513b8: LeaveFrame
    //     0xb513b8: mov             SP, fp
    //     0xb513bc: ldp             fp, lr, [SP], #0x10
    // 0xb513c0: ret
    //     0xb513c0: ret             
    // 0xb513c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb513c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb513c8: b               #0xb512d4
    // 0xb513cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb513cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb513d0, size: 0x368
    // 0xb513d0: EnterFrame
    //     0xb513d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb513d4: mov             fp, SP
    // 0xb513d8: AllocStack(0x38)
    //     0xb513d8: sub             SP, SP, #0x38
    // 0xb513dc: SetupParameters()
    //     0xb513dc: ldr             x0, [fp, #0x10]
    //     0xb513e0: ldur            w2, [x0, #0x17]
    //     0xb513e4: add             x2, x2, HEAP, lsl #32
    //     0xb513e8: stur            x2, [fp, #-0x10]
    // 0xb513ec: CheckStackOverflow
    //     0xb513ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb513f0: cmp             SP, x16
    //     0xb513f4: b.ls            #0xb51728
    // 0xb513f8: LoadField: r0 = r2->field_b
    //     0xb513f8: ldur            w0, [x2, #0xb]
    // 0xb513fc: DecompressPointer r0
    //     0xb513fc: add             x0, x0, HEAP, lsl #32
    // 0xb51400: stur            x0, [fp, #-8]
    // 0xb51404: LoadField: r1 = r0->field_f
    //     0xb51404: ldur            w1, [x0, #0xf]
    // 0xb51408: DecompressPointer r1
    //     0xb51408: add             x1, x1, HEAP, lsl #32
    // 0xb5140c: r0 = controller()
    //     0xb5140c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb51410: LoadField: r1 = r0->field_67
    //     0xb51410: ldur            w1, [x0, #0x67]
    // 0xb51414: DecompressPointer r1
    //     0xb51414: add             x1, x1, HEAP, lsl #32
    // 0xb51418: r0 = value()
    //     0xb51418: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb5141c: mov             x2, x0
    // 0xb51420: ldur            x0, [fp, #-8]
    // 0xb51424: stur            x2, [fp, #-0x18]
    // 0xb51428: LoadField: r1 = r0->field_f
    //     0xb51428: ldur            w1, [x0, #0xf]
    // 0xb5142c: DecompressPointer r1
    //     0xb5142c: add             x1, x1, HEAP, lsl #32
    // 0xb51430: r0 = controller()
    //     0xb51430: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb51434: LoadField: r1 = r0->field_57
    //     0xb51434: ldur            w1, [x0, #0x57]
    // 0xb51438: DecompressPointer r1
    //     0xb51438: add             x1, x1, HEAP, lsl #32
    // 0xb5143c: r0 = value()
    //     0xb5143c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb51440: cmp             w0, NULL
    // 0xb51444: b.ne            #0xb51454
    // 0xb51448: ldur            x2, [fp, #-0x10]
    // 0xb5144c: r1 = Null
    //     0xb5144c: mov             x1, NULL
    // 0xb51450: b               #0xb514a8
    // 0xb51454: ldur            x2, [fp, #-0x10]
    // 0xb51458: LoadField: r3 = r0->field_7
    //     0xb51458: ldur            w3, [x0, #7]
    // 0xb5145c: DecompressPointer r3
    //     0xb5145c: add             x3, x3, HEAP, lsl #32
    // 0xb51460: LoadField: r0 = r2->field_13
    //     0xb51460: ldur            w0, [x2, #0x13]
    // 0xb51464: DecompressPointer r0
    //     0xb51464: add             x0, x0, HEAP, lsl #32
    // 0xb51468: LoadField: r1 = r3->field_b
    //     0xb51468: ldur            w1, [x3, #0xb]
    // 0xb5146c: r4 = LoadInt32Instr(r0)
    //     0xb5146c: sbfx            x4, x0, #1, #0x1f
    //     0xb51470: tbz             w0, #0, #0xb51478
    //     0xb51474: ldur            x4, [x0, #7]
    // 0xb51478: r0 = LoadInt32Instr(r1)
    //     0xb51478: sbfx            x0, x1, #1, #0x1f
    // 0xb5147c: mov             x1, x4
    // 0xb51480: cmp             x1, x0
    // 0xb51484: b.hs            #0xb51730
    // 0xb51488: LoadField: r0 = r3->field_f
    //     0xb51488: ldur            w0, [x3, #0xf]
    // 0xb5148c: DecompressPointer r0
    //     0xb5148c: add             x0, x0, HEAP, lsl #32
    // 0xb51490: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb51490: add             x16, x0, x4, lsl #2
    //     0xb51494: ldur            w1, [x16, #0xf]
    // 0xb51498: DecompressPointer r1
    //     0xb51498: add             x1, x1, HEAP, lsl #32
    // 0xb5149c: LoadField: r0 = r1->field_b
    //     0xb5149c: ldur            w0, [x1, #0xb]
    // 0xb514a0: DecompressPointer r0
    //     0xb514a0: add             x0, x0, HEAP, lsl #32
    // 0xb514a4: mov             x1, x0
    // 0xb514a8: ldur            x0, [fp, #-0x18]
    // 0xb514ac: r3 = LoadClassIdInstr(r0)
    //     0xb514ac: ldur            x3, [x0, #-1]
    //     0xb514b0: ubfx            x3, x3, #0xc, #0x14
    // 0xb514b4: stp             x1, x0, [SP]
    // 0xb514b8: mov             x0, x3
    // 0xb514bc: mov             lr, x0
    // 0xb514c0: ldr             lr, [x21, lr, lsl #3]
    // 0xb514c4: blr             lr
    // 0xb514c8: tbnz            w0, #4, #0xb514d8
    // 0xb514cc: r3 = Instance_IconData
    //     0xb514cc: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xb514d0: ldr             x3, [x3, #0x30]
    // 0xb514d4: b               #0xb514e0
    // 0xb514d8: r3 = Instance_IconData
    //     0xb514d8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xb514dc: ldr             x3, [x3, #0x38]
    // 0xb514e0: ldur            x0, [fp, #-0x10]
    // 0xb514e4: ldur            x2, [fp, #-8]
    // 0xb514e8: stur            x3, [fp, #-0x18]
    // 0xb514ec: LoadField: r1 = r0->field_f
    //     0xb514ec: ldur            w1, [x0, #0xf]
    // 0xb514f0: DecompressPointer r1
    //     0xb514f0: add             x1, x1, HEAP, lsl #32
    // 0xb514f4: r0 = of()
    //     0xb514f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb514f8: LoadField: r1 = r0->field_5b
    //     0xb514f8: ldur            w1, [x0, #0x5b]
    // 0xb514fc: DecompressPointer r1
    //     0xb514fc: add             x1, x1, HEAP, lsl #32
    // 0xb51500: stur            x1, [fp, #-0x20]
    // 0xb51504: r0 = Icon()
    //     0xb51504: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb51508: mov             x2, x0
    // 0xb5150c: ldur            x0, [fp, #-0x18]
    // 0xb51510: stur            x2, [fp, #-0x28]
    // 0xb51514: StoreField: r2->field_b = r0
    //     0xb51514: stur            w0, [x2, #0xb]
    // 0xb51518: ldur            x0, [fp, #-0x20]
    // 0xb5151c: StoreField: r2->field_23 = r0
    //     0xb5151c: stur            w0, [x2, #0x23]
    // 0xb51520: ldur            x0, [fp, #-8]
    // 0xb51524: LoadField: r1 = r0->field_f
    //     0xb51524: ldur            w1, [x0, #0xf]
    // 0xb51528: DecompressPointer r1
    //     0xb51528: add             x1, x1, HEAP, lsl #32
    // 0xb5152c: r0 = controller()
    //     0xb5152c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xb51530: LoadField: r1 = r0->field_57
    //     0xb51530: ldur            w1, [x0, #0x57]
    // 0xb51534: DecompressPointer r1
    //     0xb51534: add             x1, x1, HEAP, lsl #32
    // 0xb51538: r0 = value()
    //     0xb51538: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb5153c: cmp             w0, NULL
    // 0xb51540: b.ne            #0xb51550
    // 0xb51544: ldur            x2, [fp, #-0x10]
    // 0xb51548: r0 = Null
    //     0xb51548: mov             x0, NULL
    // 0xb5154c: b               #0xb515a0
    // 0xb51550: ldur            x2, [fp, #-0x10]
    // 0xb51554: LoadField: r3 = r0->field_7
    //     0xb51554: ldur            w3, [x0, #7]
    // 0xb51558: DecompressPointer r3
    //     0xb51558: add             x3, x3, HEAP, lsl #32
    // 0xb5155c: LoadField: r0 = r2->field_13
    //     0xb5155c: ldur            w0, [x2, #0x13]
    // 0xb51560: DecompressPointer r0
    //     0xb51560: add             x0, x0, HEAP, lsl #32
    // 0xb51564: LoadField: r1 = r3->field_b
    //     0xb51564: ldur            w1, [x3, #0xb]
    // 0xb51568: r4 = LoadInt32Instr(r0)
    //     0xb51568: sbfx            x4, x0, #1, #0x1f
    //     0xb5156c: tbz             w0, #0, #0xb51574
    //     0xb51570: ldur            x4, [x0, #7]
    // 0xb51574: r0 = LoadInt32Instr(r1)
    //     0xb51574: sbfx            x0, x1, #1, #0x1f
    // 0xb51578: mov             x1, x4
    // 0xb5157c: cmp             x1, x0
    // 0xb51580: b.hs            #0xb51734
    // 0xb51584: LoadField: r0 = r3->field_f
    //     0xb51584: ldur            w0, [x3, #0xf]
    // 0xb51588: DecompressPointer r0
    //     0xb51588: add             x0, x0, HEAP, lsl #32
    // 0xb5158c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb5158c: add             x16, x0, x4, lsl #2
    //     0xb51590: ldur            w1, [x16, #0xf]
    // 0xb51594: DecompressPointer r1
    //     0xb51594: add             x1, x1, HEAP, lsl #32
    // 0xb51598: LoadField: r0 = r1->field_7
    //     0xb51598: ldur            w0, [x1, #7]
    // 0xb5159c: DecompressPointer r0
    //     0xb5159c: add             x0, x0, HEAP, lsl #32
    // 0xb515a0: cmp             w0, NULL
    // 0xb515a4: b.ne            #0xb515b0
    // 0xb515a8: r3 = ""
    //     0xb515a8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb515ac: b               #0xb515b4
    // 0xb515b0: mov             x3, x0
    // 0xb515b4: ldur            x0, [fp, #-0x28]
    // 0xb515b8: stur            x3, [fp, #-8]
    // 0xb515bc: LoadField: r1 = r2->field_f
    //     0xb515bc: ldur            w1, [x2, #0xf]
    // 0xb515c0: DecompressPointer r1
    //     0xb515c0: add             x1, x1, HEAP, lsl #32
    // 0xb515c4: r0 = of()
    //     0xb515c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb515c8: LoadField: r1 = r0->field_87
    //     0xb515c8: ldur            w1, [x0, #0x87]
    // 0xb515cc: DecompressPointer r1
    //     0xb515cc: add             x1, x1, HEAP, lsl #32
    // 0xb515d0: LoadField: r0 = r1->field_2b
    //     0xb515d0: ldur            w0, [x1, #0x2b]
    // 0xb515d4: DecompressPointer r0
    //     0xb515d4: add             x0, x0, HEAP, lsl #32
    // 0xb515d8: stur            x0, [fp, #-0x10]
    // 0xb515dc: r1 = Instance_Color
    //     0xb515dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb515e0: d0 = 0.700000
    //     0xb515e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb515e4: ldr             d0, [x17, #0xf48]
    // 0xb515e8: r0 = withOpacity()
    //     0xb515e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb515ec: r16 = 12.000000
    //     0xb515ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb515f0: ldr             x16, [x16, #0x9e8]
    // 0xb515f4: stp             x0, x16, [SP]
    // 0xb515f8: ldur            x1, [fp, #-0x10]
    // 0xb515fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb515fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb51600: ldr             x4, [x4, #0xaa0]
    // 0xb51604: r0 = copyWith()
    //     0xb51604: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb51608: stur            x0, [fp, #-0x10]
    // 0xb5160c: r0 = Text()
    //     0xb5160c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb51610: mov             x2, x0
    // 0xb51614: ldur            x0, [fp, #-8]
    // 0xb51618: stur            x2, [fp, #-0x18]
    // 0xb5161c: StoreField: r2->field_b = r0
    //     0xb5161c: stur            w0, [x2, #0xb]
    // 0xb51620: ldur            x0, [fp, #-0x10]
    // 0xb51624: StoreField: r2->field_13 = r0
    //     0xb51624: stur            w0, [x2, #0x13]
    // 0xb51628: r1 = <FlexParentData>
    //     0xb51628: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5162c: ldr             x1, [x1, #0xe00]
    // 0xb51630: r0 = Expanded()
    //     0xb51630: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb51634: mov             x3, x0
    // 0xb51638: r0 = 1
    //     0xb51638: movz            x0, #0x1
    // 0xb5163c: stur            x3, [fp, #-8]
    // 0xb51640: StoreField: r3->field_13 = r0
    //     0xb51640: stur            x0, [x3, #0x13]
    // 0xb51644: r0 = Instance_FlexFit
    //     0xb51644: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb51648: ldr             x0, [x0, #0xe08]
    // 0xb5164c: StoreField: r3->field_1b = r0
    //     0xb5164c: stur            w0, [x3, #0x1b]
    // 0xb51650: ldur            x0, [fp, #-0x18]
    // 0xb51654: StoreField: r3->field_b = r0
    //     0xb51654: stur            w0, [x3, #0xb]
    // 0xb51658: r1 = Null
    //     0xb51658: mov             x1, NULL
    // 0xb5165c: r2 = 6
    //     0xb5165c: movz            x2, #0x6
    // 0xb51660: r0 = AllocateArray()
    //     0xb51660: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb51664: mov             x2, x0
    // 0xb51668: ldur            x0, [fp, #-0x28]
    // 0xb5166c: stur            x2, [fp, #-0x10]
    // 0xb51670: StoreField: r2->field_f = r0
    //     0xb51670: stur            w0, [x2, #0xf]
    // 0xb51674: r16 = Instance_SizedBox
    //     0xb51674: add             x16, PP, #0x34, lsl #12  ; [pp+0x34040] Obj!SizedBox@d680c1
    //     0xb51678: ldr             x16, [x16, #0x40]
    // 0xb5167c: StoreField: r2->field_13 = r16
    //     0xb5167c: stur            w16, [x2, #0x13]
    // 0xb51680: ldur            x0, [fp, #-8]
    // 0xb51684: ArrayStore: r2[0] = r0  ; List_4
    //     0xb51684: stur            w0, [x2, #0x17]
    // 0xb51688: r1 = <Widget>
    //     0xb51688: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5168c: r0 = AllocateGrowableArray()
    //     0xb5168c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb51690: mov             x1, x0
    // 0xb51694: ldur            x0, [fp, #-0x10]
    // 0xb51698: stur            x1, [fp, #-8]
    // 0xb5169c: StoreField: r1->field_f = r0
    //     0xb5169c: stur            w0, [x1, #0xf]
    // 0xb516a0: r0 = 6
    //     0xb516a0: movz            x0, #0x6
    // 0xb516a4: StoreField: r1->field_b = r0
    //     0xb516a4: stur            w0, [x1, #0xb]
    // 0xb516a8: r0 = Row()
    //     0xb516a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb516ac: mov             x1, x0
    // 0xb516b0: r0 = Instance_Axis
    //     0xb516b0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb516b4: stur            x1, [fp, #-0x10]
    // 0xb516b8: StoreField: r1->field_f = r0
    //     0xb516b8: stur            w0, [x1, #0xf]
    // 0xb516bc: r0 = Instance_MainAxisAlignment
    //     0xb516bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb516c0: ldr             x0, [x0, #0xa08]
    // 0xb516c4: StoreField: r1->field_13 = r0
    //     0xb516c4: stur            w0, [x1, #0x13]
    // 0xb516c8: r0 = Instance_MainAxisSize
    //     0xb516c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb516cc: ldr             x0, [x0, #0xdd0]
    // 0xb516d0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb516d0: stur            w0, [x1, #0x17]
    // 0xb516d4: r0 = Instance_CrossAxisAlignment
    //     0xb516d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb516d8: ldr             x0, [x0, #0xa18]
    // 0xb516dc: StoreField: r1->field_1b = r0
    //     0xb516dc: stur            w0, [x1, #0x1b]
    // 0xb516e0: r0 = Instance_VerticalDirection
    //     0xb516e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb516e4: ldr             x0, [x0, #0xa20]
    // 0xb516e8: StoreField: r1->field_23 = r0
    //     0xb516e8: stur            w0, [x1, #0x23]
    // 0xb516ec: r0 = Instance_Clip
    //     0xb516ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb516f0: ldr             x0, [x0, #0x38]
    // 0xb516f4: StoreField: r1->field_2b = r0
    //     0xb516f4: stur            w0, [x1, #0x2b]
    // 0xb516f8: StoreField: r1->field_2f = rZR
    //     0xb516f8: stur            xzr, [x1, #0x2f]
    // 0xb516fc: ldur            x0, [fp, #-8]
    // 0xb51700: StoreField: r1->field_b = r0
    //     0xb51700: stur            w0, [x1, #0xb]
    // 0xb51704: r0 = Padding()
    //     0xb51704: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb51708: r1 = Instance_EdgeInsets
    //     0xb51708: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5170c: ldr             x1, [x1, #0x1f0]
    // 0xb51710: StoreField: r0->field_f = r1
    //     0xb51710: stur            w1, [x0, #0xf]
    // 0xb51714: ldur            x1, [fp, #-0x10]
    // 0xb51718: StoreField: r0->field_b = r1
    //     0xb51718: stur            w1, [x0, #0xb]
    // 0xb5171c: LeaveFrame
    //     0xb5171c: mov             SP, fp
    //     0xb51720: ldp             fp, lr, [SP], #0x10
    // 0xb51724: ret
    //     0xb51724: ret             
    // 0xb51728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb51728: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5172c: b               #0xb513f8
    // 0xb51730: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb51730: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb51734: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb51734: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, ImageProvider<Object>) {
    // ** addr: 0xb51738, size: 0xf0
    // 0xb51738: EnterFrame
    //     0xb51738: stp             fp, lr, [SP, #-0x10]!
    //     0xb5173c: mov             fp, SP
    // 0xb51740: AllocStack(0x28)
    //     0xb51740: sub             SP, SP, #0x28
    // 0xb51744: CheckStackOverflow
    //     0xb51744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb51748: cmp             SP, x16
    //     0xb5174c: b.ls            #0xb51820
    // 0xb51750: r0 = DecorationImage()
    //     0xb51750: bl              #0x83fce0  ; AllocateDecorationImageStub -> DecorationImage (size=0x44)
    // 0xb51754: mov             x1, x0
    // 0xb51758: ldr             x0, [fp, #0x10]
    // 0xb5175c: stur            x1, [fp, #-8]
    // 0xb51760: StoreField: r1->field_7 = r0
    //     0xb51760: stur            w0, [x1, #7]
    // 0xb51764: r0 = Instance_BoxFit
    //     0xb51764: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb51768: ldr             x0, [x0, #0x118]
    // 0xb5176c: StoreField: r1->field_13 = r0
    //     0xb5176c: stur            w0, [x1, #0x13]
    // 0xb51770: r0 = Instance_Alignment
    //     0xb51770: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb51774: ldr             x0, [x0, #0xb10]
    // 0xb51778: ArrayStore: r1[0] = r0  ; List_4
    //     0xb51778: stur            w0, [x1, #0x17]
    // 0xb5177c: r0 = Instance_ImageRepeat
    //     0xb5177c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb00] Obj!ImageRepeat@d73821
    //     0xb51780: ldr             x0, [x0, #0xb00]
    // 0xb51784: StoreField: r1->field_1f = r0
    //     0xb51784: stur            w0, [x1, #0x1f]
    // 0xb51788: r0 = false
    //     0xb51788: add             x0, NULL, #0x30  ; false
    // 0xb5178c: StoreField: r1->field_23 = r0
    //     0xb5178c: stur            w0, [x1, #0x23]
    // 0xb51790: d0 = 1.000000
    //     0xb51790: fmov            d0, #1.00000000
    // 0xb51794: StoreField: r1->field_27 = d0
    //     0xb51794: stur            d0, [x1, #0x27]
    // 0xb51798: StoreField: r1->field_2f = d0
    //     0xb51798: stur            d0, [x1, #0x2f]
    // 0xb5179c: r2 = Instance_FilterQuality
    //     0xb5179c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33a98] Obj!FilterQuality@d77121
    //     0xb517a0: ldr             x2, [x2, #0xa98]
    // 0xb517a4: StoreField: r1->field_37 = r2
    //     0xb517a4: stur            w2, [x1, #0x37]
    // 0xb517a8: StoreField: r1->field_3b = r0
    //     0xb517a8: stur            w0, [x1, #0x3b]
    // 0xb517ac: StoreField: r1->field_3f = r0
    //     0xb517ac: stur            w0, [x1, #0x3f]
    // 0xb517b0: r0 = BoxDecoration()
    //     0xb517b0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb517b4: mov             x1, x0
    // 0xb517b8: ldur            x0, [fp, #-8]
    // 0xb517bc: stur            x1, [fp, #-0x10]
    // 0xb517c0: StoreField: r1->field_b = r0
    //     0xb517c0: stur            w0, [x1, #0xb]
    // 0xb517c4: r0 = Instance_BorderRadius
    //     0xb517c4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb517c8: ldr             x0, [x0, #0x460]
    // 0xb517cc: StoreField: r1->field_13 = r0
    //     0xb517cc: stur            w0, [x1, #0x13]
    // 0xb517d0: r0 = Instance_BoxShape
    //     0xb517d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb517d4: ldr             x0, [x0, #0x80]
    // 0xb517d8: StoreField: r1->field_23 = r0
    //     0xb517d8: stur            w0, [x1, #0x23]
    // 0xb517dc: r0 = Container()
    //     0xb517dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb517e0: stur            x0, [fp, #-8]
    // 0xb517e4: r16 = 65.000000
    //     0xb517e4: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xb517e8: ldr             x16, [x16, #0xd28]
    // 0xb517ec: r30 = 65.000000
    //     0xb517ec: add             lr, PP, #0x38, lsl #12  ; [pp+0x38d28] 65
    //     0xb517f0: ldr             lr, [lr, #0xd28]
    // 0xb517f4: stp             lr, x16, [SP, #8]
    // 0xb517f8: ldur            x16, [fp, #-0x10]
    // 0xb517fc: str             x16, [SP]
    // 0xb51800: mov             x1, x0
    // 0xb51804: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb51804: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb51808: ldr             x4, [x4, #0x468]
    // 0xb5180c: r0 = Container()
    //     0xb5180c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb51810: ldur            x0, [fp, #-8]
    // 0xb51814: LeaveFrame
    //     0xb51814: mov             SP, fp
    //     0xb51818: ldp             fp, lr, [SP], #0x10
    // 0xb5181c: ret
    //     0xb5181c: ret             
    // 0xb51820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb51820: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb51824: b               #0xb51750
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13541ec, size: 0xec
    // 0x13541ec: EnterFrame
    //     0x13541ec: stp             fp, lr, [SP, #-0x10]!
    //     0x13541f0: mov             fp, SP
    // 0x13541f4: AllocStack(0x20)
    //     0x13541f4: sub             SP, SP, #0x20
    // 0x13541f8: SetupParameters()
    //     0x13541f8: ldr             x0, [fp, #0x10]
    //     0x13541fc: ldur            w2, [x0, #0x17]
    //     0x1354200: add             x2, x2, HEAP, lsl #32
    //     0x1354204: stur            x2, [fp, #-8]
    // 0x1354208: CheckStackOverflow
    //     0x1354208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135420c: cmp             SP, x16
    //     0x1354210: b.ls            #0x13542d0
    // 0x1354214: LoadField: r1 = r2->field_f
    //     0x1354214: ldur            w1, [x2, #0xf]
    // 0x1354218: DecompressPointer r1
    //     0x1354218: add             x1, x1, HEAP, lsl #32
    // 0x135421c: r0 = controller()
    //     0x135421c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354220: mov             x2, x0
    // 0x1354224: ldur            x0, [fp, #-8]
    // 0x1354228: stur            x2, [fp, #-0x10]
    // 0x135422c: LoadField: r1 = r0->field_f
    //     0x135422c: ldur            w1, [x0, #0xf]
    // 0x1354230: DecompressPointer r1
    //     0x1354230: add             x1, x1, HEAP, lsl #32
    // 0x1354234: r0 = controller()
    //     0x1354234: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354238: LoadField: r1 = r0->field_bb
    //     0x1354238: ldur            w1, [x0, #0xbb]
    // 0x135423c: DecompressPointer r1
    //     0x135423c: add             x1, x1, HEAP, lsl #32
    // 0x1354240: r0 = LoadClassIdInstr(r1)
    //     0x1354240: ldur            x0, [x1, #-1]
    //     0x1354244: ubfx            x0, x0, #0xc, #0x14
    // 0x1354248: r16 = "return"
    //     0x1354248: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x135424c: ldr             x16, [x16, #0x9b8]
    // 0x1354250: stp             x16, x1, [SP]
    // 0x1354254: mov             lr, x0
    // 0x1354258: ldr             lr, [x21, lr, lsl #3]
    // 0x135425c: blr             lr
    // 0x1354260: tbnz            w0, #4, #0x1354270
    // 0x1354264: r5 = "reason_page"
    //     0x1354264: add             x5, PP, #0x33, lsl #12  ; [pp+0x33f48] "reason_page"
    //     0x1354268: ldr             x5, [x5, #0xf48]
    // 0x135426c: b               #0x1354278
    // 0x1354270: r5 = "exchange_reason"
    //     0x1354270: add             x5, PP, #0x33, lsl #12  ; [pp+0x33f50] "exchange_reason"
    //     0x1354274: ldr             x5, [x5, #0xf50]
    // 0x1354278: ldur            x1, [fp, #-0x10]
    // 0x135427c: r2 = "Next"
    //     0x135427c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x1354280: ldr             x2, [x2, #0x3e8]
    // 0x1354284: r3 = "return_exchange_next"
    //     0x1354284: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f60] "return_exchange_next"
    //     0x1354288: ldr             x3, [x3, #0xf60]
    // 0x135428c: r0 = ctaExchangeWithReasonRemarkPostEvent()
    //     0x135428c: bl              #0x13271d8  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::ctaExchangeWithReasonRemarkPostEvent
    // 0x1354290: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1354290: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1354294: ldr             x0, [x0, #0x1c80]
    //     0x1354298: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x135429c: cmp             w0, w16
    //     0x13542a0: b.ne            #0x13542ac
    //     0x13542a4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13542a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13542ac: r16 = "/return-order-confirm"
    //     0x13542ac: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8c8] "/return-order-confirm"
    //     0x13542b0: ldr             x16, [x16, #0x8c8]
    // 0x13542b4: stp             x16, NULL, [SP]
    // 0x13542b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13542b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13542bc: r0 = GetNavigation.toNamed()
    //     0x13542bc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13542c0: r0 = Null
    //     0x13542c0: mov             x0, NULL
    // 0x13542c4: LeaveFrame
    //     0x13542c4: mov             SP, fp
    //     0x13542c8: ldp             fp, lr, [SP], #0x10
    // 0x13542cc: ret
    //     0x13542cc: ret             
    // 0x13542d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13542d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13542d4: b               #0x1354214
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x13542d8, size: 0x1084
    // 0x13542d8: EnterFrame
    //     0x13542d8: stp             fp, lr, [SP, #-0x10]!
    //     0x13542dc: mov             fp, SP
    // 0x13542e0: AllocStack(0x68)
    //     0x13542e0: sub             SP, SP, #0x68
    // 0x13542e4: SetupParameters()
    //     0x13542e4: ldr             x0, [fp, #0x10]
    //     0x13542e8: ldur            w2, [x0, #0x17]
    //     0x13542ec: add             x2, x2, HEAP, lsl #32
    //     0x13542f0: stur            x2, [fp, #-8]
    // 0x13542f4: CheckStackOverflow
    //     0x13542f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13542f8: cmp             SP, x16
    //     0x13542fc: b.ls            #0x1355344
    // 0x1354300: LoadField: r1 = r2->field_f
    //     0x1354300: ldur            w1, [x2, #0xf]
    // 0x1354304: DecompressPointer r1
    //     0x1354304: add             x1, x1, HEAP, lsl #32
    // 0x1354308: r0 = controller()
    //     0x1354308: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135430c: LoadField: r1 = r0->field_57
    //     0x135430c: ldur            w1, [x0, #0x57]
    // 0x1354310: DecompressPointer r1
    //     0x1354310: add             x1, x1, HEAP, lsl #32
    // 0x1354314: r0 = value()
    //     0x1354314: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354318: cmp             w0, NULL
    // 0x135431c: b.ne            #0x1354328
    // 0x1354320: r0 = Null
    //     0x1354320: mov             x0, NULL
    // 0x1354324: b               #0x1354334
    // 0x1354328: LoadField: r1 = r0->field_23
    //     0x1354328: ldur            w1, [x0, #0x23]
    // 0x135432c: DecompressPointer r1
    //     0x135432c: add             x1, x1, HEAP, lsl #32
    // 0x1354330: mov             x0, x1
    // 0x1354334: cmp             w0, NULL
    // 0x1354338: b.eq            #0x1354378
    // 0x135433c: tbnz            w0, #4, #0x1354378
    // 0x1354340: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1354340: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1354344: ldr             x0, [x0, #0x1c80]
    //     0x1354348: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x135434c: cmp             w0, w16
    //     0x1354350: b.ne            #0x135435c
    //     0x1354354: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1354358: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x135435c: r0 = GetNavigation.size()
    //     0x135435c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1354360: LoadField: d0 = r0->field_f
    //     0x1354360: ldur            d0, [x0, #0xf]
    // 0x1354364: d1 = 0.080000
    //     0x1354364: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0x1354368: ldr             d1, [x17, #0x798]
    // 0x135436c: fmul            d2, d0, d1
    // 0x1354370: mov             v0.16b, v2.16b
    // 0x1354374: b               #0x13543ac
    // 0x1354378: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1354378: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x135437c: ldr             x0, [x0, #0x1c80]
    //     0x1354380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1354384: cmp             w0, w16
    //     0x1354388: b.ne            #0x1354394
    //     0x135438c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1354390: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1354394: r0 = GetNavigation.size()
    //     0x1354394: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1354398: LoadField: d0 = r0->field_f
    //     0x1354398: ldur            d0, [x0, #0xf]
    // 0x135439c: d1 = 0.170000
    //     0x135439c: add             x17, PP, #0x33, lsl #12  ; [pp+0x33f10] IMM: double(0.17) from 0x3fc5c28f5c28f5c3
    //     0x13543a0: ldr             d1, [x17, #0xf10]
    // 0x13543a4: fmul            d2, d0, d1
    // 0x13543a8: mov             v0.16b, v2.16b
    // 0x13543ac: ldur            x0, [fp, #-8]
    // 0x13543b0: stur            d0, [fp, #-0x50]
    // 0x13543b4: r1 = _ConstMap len:11
    //     0x13543b4: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x13543b8: ldr             x1, [x1, #0xc28]
    // 0x13543bc: r2 = 8
    //     0x13543bc: movz            x2, #0x8
    // 0x13543c0: r0 = []()
    //     0x13543c0: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x13543c4: stur            x0, [fp, #-0x10]
    // 0x13543c8: r0 = BoxDecoration()
    //     0x13543c8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13543cc: mov             x2, x0
    // 0x13543d0: r0 = Instance_Color
    //     0x13543d0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13543d4: stur            x2, [fp, #-0x18]
    // 0x13543d8: StoreField: r2->field_7 = r0
    //     0x13543d8: stur            w0, [x2, #7]
    // 0x13543dc: ldur            x0, [fp, #-0x10]
    // 0x13543e0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13543e0: stur            w0, [x2, #0x17]
    // 0x13543e4: r0 = Instance_BoxShape
    //     0x13543e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13543e8: ldr             x0, [x0, #0x80]
    // 0x13543ec: StoreField: r2->field_23 = r0
    //     0x13543ec: stur            w0, [x2, #0x23]
    // 0x13543f0: ldur            x3, [fp, #-8]
    // 0x13543f4: LoadField: r1 = r3->field_f
    //     0x13543f4: ldur            w1, [x3, #0xf]
    // 0x13543f8: DecompressPointer r1
    //     0x13543f8: add             x1, x1, HEAP, lsl #32
    // 0x13543fc: r0 = controller()
    //     0x13543fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354400: LoadField: r1 = r0->field_57
    //     0x1354400: ldur            w1, [x0, #0x57]
    // 0x1354404: DecompressPointer r1
    //     0x1354404: add             x1, x1, HEAP, lsl #32
    // 0x1354408: r0 = value()
    //     0x1354408: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135440c: cmp             w0, NULL
    // 0x1354410: b.ne            #0x135441c
    // 0x1354414: r0 = Null
    //     0x1354414: mov             x0, NULL
    // 0x1354418: b               #0x1354428
    // 0x135441c: LoadField: r1 = r0->field_23
    //     0x135441c: ldur            w1, [x0, #0x23]
    // 0x1354420: DecompressPointer r1
    //     0x1354420: add             x1, x1, HEAP, lsl #32
    // 0x1354424: mov             x0, x1
    // 0x1354428: cmp             w0, NULL
    // 0x135442c: b.ne            #0x135447c
    // 0x1354430: ldur            x3, [fp, #-8]
    // 0x1354434: r5 = true
    //     0x1354434: add             x5, NULL, #0x20  ; true
    // 0x1354438: r4 = false
    //     0x1354438: add             x4, NULL, #0x30  ; false
    // 0x135443c: r8 = Instance_MainAxisSize
    //     0x135443c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1354440: ldr             x8, [x8, #0xa10]
    // 0x1354444: r6 = Instance_FlexFit
    //     0x1354444: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1354448: ldr             x6, [x6, #0xe08]
    // 0x135444c: r7 = Instance_Axis
    //     0x135444c: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1354450: r10 = Instance_VerticalDirection
    //     0x1354450: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1354454: ldr             x10, [x10, #0xa20]
    // 0x1354458: r9 = Instance_CrossAxisAlignment
    //     0x1354458: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135445c: ldr             x9, [x9, #0xa18]
    // 0x1354460: r0 = Instance_BorderSide
    //     0x1354460: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1354464: ldr             x0, [x0, #0xe20]
    // 0x1354468: r11 = Instance_Clip
    //     0x1354468: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x135446c: ldr             x11, [x11, #0x38]
    // 0x1354470: d0 = 30.000000
    //     0x1354470: fmov            d0, #30.00000000
    // 0x1354474: r2 = 1
    //     0x1354474: movz            x2, #0x1
    // 0x1354478: b               #0x13547e0
    // 0x135447c: tbnz            w0, #4, #0x1354798
    // 0x1354480: ldur            x2, [fp, #-8]
    // 0x1354484: LoadField: r1 = r2->field_f
    //     0x1354484: ldur            w1, [x2, #0xf]
    // 0x1354488: DecompressPointer r1
    //     0x1354488: add             x1, x1, HEAP, lsl #32
    // 0x135448c: r0 = controller()
    //     0x135448c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354490: LoadField: r1 = r0->field_8f
    //     0x1354490: ldur            w1, [x0, #0x8f]
    // 0x1354494: DecompressPointer r1
    //     0x1354494: add             x1, x1, HEAP, lsl #32
    // 0x1354498: r0 = value()
    //     0x1354498: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135449c: tbnz            w0, #4, #0x13544b4
    // 0x13544a0: ldur            x2, [fp, #-8]
    // 0x13544a4: r1 = Function '<anonymous closure>':.
    //     0x13544a4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3e0] AnonymousClosure: (0x13541ec), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1362c38)
    //     0x13544a8: ldr             x1, [x1, #0x3e0]
    // 0x13544ac: r0 = AllocateClosure()
    //     0x13544ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13544b0: b               #0x13544b8
    // 0x13544b4: r0 = Null
    //     0x13544b4: mov             x0, NULL
    // 0x13544b8: ldur            x2, [fp, #-8]
    // 0x13544bc: stur            x0, [fp, #-0x10]
    // 0x13544c0: r16 = <EdgeInsets>
    //     0x13544c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13544c4: ldr             x16, [x16, #0xda0]
    // 0x13544c8: r30 = Instance_EdgeInsets
    //     0x13544c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13544cc: ldr             lr, [lr, #0x1f0]
    // 0x13544d0: stp             lr, x16, [SP]
    // 0x13544d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13544d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13544d8: r0 = all()
    //     0x13544d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13544dc: ldur            x2, [fp, #-8]
    // 0x13544e0: stur            x0, [fp, #-0x20]
    // 0x13544e4: LoadField: r1 = r2->field_13
    //     0x13544e4: ldur            w1, [x2, #0x13]
    // 0x13544e8: DecompressPointer r1
    //     0x13544e8: add             x1, x1, HEAP, lsl #32
    // 0x13544ec: r0 = of()
    //     0x13544ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13544f0: LoadField: r1 = r0->field_5b
    //     0x13544f0: ldur            w1, [x0, #0x5b]
    // 0x13544f4: DecompressPointer r1
    //     0x13544f4: add             x1, x1, HEAP, lsl #32
    // 0x13544f8: r16 = <Color>
    //     0x13544f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13544fc: ldr             x16, [x16, #0xf80]
    // 0x1354500: stp             x1, x16, [SP]
    // 0x1354504: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1354504: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1354508: r0 = all()
    //     0x1354508: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x135450c: ldur            x2, [fp, #-8]
    // 0x1354510: stur            x0, [fp, #-0x28]
    // 0x1354514: LoadField: r1 = r2->field_f
    //     0x1354514: ldur            w1, [x2, #0xf]
    // 0x1354518: DecompressPointer r1
    //     0x1354518: add             x1, x1, HEAP, lsl #32
    // 0x135451c: r0 = controller()
    //     0x135451c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354520: LoadField: r1 = r0->field_8f
    //     0x1354520: ldur            w1, [x0, #0x8f]
    // 0x1354524: DecompressPointer r1
    //     0x1354524: add             x1, x1, HEAP, lsl #32
    // 0x1354528: r0 = value()
    //     0x1354528: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135452c: tbnz            w0, #4, #0x1354550
    // 0x1354530: ldur            x2, [fp, #-8]
    // 0x1354534: LoadField: r1 = r2->field_13
    //     0x1354534: ldur            w1, [x2, #0x13]
    // 0x1354538: DecompressPointer r1
    //     0x1354538: add             x1, x1, HEAP, lsl #32
    // 0x135453c: r0 = of()
    //     0x135453c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1354540: LoadField: r1 = r0->field_5b
    //     0x1354540: ldur            w1, [x0, #0x5b]
    // 0x1354544: DecompressPointer r1
    //     0x1354544: add             x1, x1, HEAP, lsl #32
    // 0x1354548: mov             x4, x1
    // 0x135454c: b               #0x1354560
    // 0x1354550: r1 = Instance_Color
    //     0x1354550: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1354554: d0 = 0.400000
    //     0x1354554: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1354558: r0 = withOpacity()
    //     0x1354558: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x135455c: mov             x4, x0
    // 0x1354560: ldur            x2, [fp, #-8]
    // 0x1354564: ldur            x3, [fp, #-0x10]
    // 0x1354568: ldur            x1, [fp, #-0x20]
    // 0x135456c: ldur            x0, [fp, #-0x28]
    // 0x1354570: r16 = <Color>
    //     0x1354570: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1354574: ldr             x16, [x16, #0xf80]
    // 0x1354578: stp             x4, x16, [SP]
    // 0x135457c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x135457c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1354580: r0 = all()
    //     0x1354580: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1354584: stur            x0, [fp, #-0x30]
    // 0x1354588: r0 = Radius()
    //     0x1354588: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x135458c: d0 = 30.000000
    //     0x135458c: fmov            d0, #30.00000000
    // 0x1354590: stur            x0, [fp, #-0x38]
    // 0x1354594: StoreField: r0->field_7 = d0
    //     0x1354594: stur            d0, [x0, #7]
    // 0x1354598: StoreField: r0->field_f = d0
    //     0x1354598: stur            d0, [x0, #0xf]
    // 0x135459c: r0 = BorderRadius()
    //     0x135459c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13545a0: mov             x1, x0
    // 0x13545a4: ldur            x0, [fp, #-0x38]
    // 0x13545a8: stur            x1, [fp, #-0x40]
    // 0x13545ac: StoreField: r1->field_7 = r0
    //     0x13545ac: stur            w0, [x1, #7]
    // 0x13545b0: StoreField: r1->field_b = r0
    //     0x13545b0: stur            w0, [x1, #0xb]
    // 0x13545b4: StoreField: r1->field_f = r0
    //     0x13545b4: stur            w0, [x1, #0xf]
    // 0x13545b8: StoreField: r1->field_13 = r0
    //     0x13545b8: stur            w0, [x1, #0x13]
    // 0x13545bc: r0 = RoundedRectangleBorder()
    //     0x13545bc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x13545c0: mov             x1, x0
    // 0x13545c4: ldur            x0, [fp, #-0x40]
    // 0x13545c8: StoreField: r1->field_b = r0
    //     0x13545c8: stur            w0, [x1, #0xb]
    // 0x13545cc: r0 = Instance_BorderSide
    //     0x13545cc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x13545d0: ldr             x0, [x0, #0xe20]
    // 0x13545d4: StoreField: r1->field_7 = r0
    //     0x13545d4: stur            w0, [x1, #7]
    // 0x13545d8: r16 = <RoundedRectangleBorder>
    //     0x13545d8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13545dc: ldr             x16, [x16, #0xf78]
    // 0x13545e0: stp             x1, x16, [SP]
    // 0x13545e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13545e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13545e8: r0 = all()
    //     0x13545e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13545ec: stur            x0, [fp, #-0x38]
    // 0x13545f0: r0 = ButtonStyle()
    //     0x13545f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13545f4: mov             x2, x0
    // 0x13545f8: ldur            x0, [fp, #-0x30]
    // 0x13545fc: stur            x2, [fp, #-0x40]
    // 0x1354600: StoreField: r2->field_b = r0
    //     0x1354600: stur            w0, [x2, #0xb]
    // 0x1354604: ldur            x0, [fp, #-0x28]
    // 0x1354608: StoreField: r2->field_f = r0
    //     0x1354608: stur            w0, [x2, #0xf]
    // 0x135460c: ldur            x0, [fp, #-0x20]
    // 0x1354610: StoreField: r2->field_23 = r0
    //     0x1354610: stur            w0, [x2, #0x23]
    // 0x1354614: ldur            x0, [fp, #-0x38]
    // 0x1354618: StoreField: r2->field_43 = r0
    //     0x1354618: stur            w0, [x2, #0x43]
    // 0x135461c: ldur            x3, [fp, #-8]
    // 0x1354620: LoadField: r1 = r3->field_13
    //     0x1354620: ldur            w1, [x3, #0x13]
    // 0x1354624: DecompressPointer r1
    //     0x1354624: add             x1, x1, HEAP, lsl #32
    // 0x1354628: r0 = of()
    //     0x1354628: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135462c: LoadField: r1 = r0->field_87
    //     0x135462c: ldur            w1, [x0, #0x87]
    // 0x1354630: DecompressPointer r1
    //     0x1354630: add             x1, x1, HEAP, lsl #32
    // 0x1354634: LoadField: r0 = r1->field_7
    //     0x1354634: ldur            w0, [x1, #7]
    // 0x1354638: DecompressPointer r0
    //     0x1354638: add             x0, x0, HEAP, lsl #32
    // 0x135463c: r16 = 14.000000
    //     0x135463c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1354640: ldr             x16, [x16, #0x1d8]
    // 0x1354644: r30 = Instance_Color
    //     0x1354644: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1354648: stp             lr, x16, [SP]
    // 0x135464c: mov             x1, x0
    // 0x1354650: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1354650: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1354654: ldr             x4, [x4, #0xaa0]
    // 0x1354658: r0 = copyWith()
    //     0x1354658: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x135465c: stur            x0, [fp, #-0x20]
    // 0x1354660: r0 = Text()
    //     0x1354660: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1354664: mov             x1, x0
    // 0x1354668: r0 = "Next"
    //     0x1354668: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x135466c: ldr             x0, [x0, #0x3e8]
    // 0x1354670: stur            x1, [fp, #-0x28]
    // 0x1354674: StoreField: r1->field_b = r0
    //     0x1354674: stur            w0, [x1, #0xb]
    // 0x1354678: ldur            x0, [fp, #-0x20]
    // 0x135467c: StoreField: r1->field_13 = r0
    //     0x135467c: stur            w0, [x1, #0x13]
    // 0x1354680: r0 = TextButton()
    //     0x1354680: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1354684: mov             x2, x0
    // 0x1354688: ldur            x0, [fp, #-0x10]
    // 0x135468c: stur            x2, [fp, #-0x20]
    // 0x1354690: StoreField: r2->field_b = r0
    //     0x1354690: stur            w0, [x2, #0xb]
    // 0x1354694: ldur            x0, [fp, #-0x40]
    // 0x1354698: StoreField: r2->field_1b = r0
    //     0x1354698: stur            w0, [x2, #0x1b]
    // 0x135469c: r4 = false
    //     0x135469c: add             x4, NULL, #0x30  ; false
    // 0x13546a0: StoreField: r2->field_27 = r4
    //     0x13546a0: stur            w4, [x2, #0x27]
    // 0x13546a4: r5 = true
    //     0x13546a4: add             x5, NULL, #0x20  ; true
    // 0x13546a8: StoreField: r2->field_2f = r5
    //     0x13546a8: stur            w5, [x2, #0x2f]
    // 0x13546ac: ldur            x0, [fp, #-0x28]
    // 0x13546b0: StoreField: r2->field_37 = r0
    //     0x13546b0: stur            w0, [x2, #0x37]
    // 0x13546b4: r1 = <FlexParentData>
    //     0x13546b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13546b8: ldr             x1, [x1, #0xe00]
    // 0x13546bc: r0 = Expanded()
    //     0x13546bc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13546c0: r2 = 1
    //     0x13546c0: movz            x2, #0x1
    // 0x13546c4: stur            x0, [fp, #-0x10]
    // 0x13546c8: StoreField: r0->field_13 = r2
    //     0x13546c8: stur            x2, [x0, #0x13]
    // 0x13546cc: r6 = Instance_FlexFit
    //     0x13546cc: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13546d0: ldr             x6, [x6, #0xe08]
    // 0x13546d4: StoreField: r0->field_1b = r6
    //     0x13546d4: stur            w6, [x0, #0x1b]
    // 0x13546d8: ldur            x1, [fp, #-0x20]
    // 0x13546dc: StoreField: r0->field_b = r1
    //     0x13546dc: stur            w1, [x0, #0xb]
    // 0x13546e0: r1 = Null
    //     0x13546e0: mov             x1, NULL
    // 0x13546e4: r2 = 2
    //     0x13546e4: movz            x2, #0x2
    // 0x13546e8: r0 = AllocateArray()
    //     0x13546e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13546ec: mov             x2, x0
    // 0x13546f0: ldur            x0, [fp, #-0x10]
    // 0x13546f4: stur            x2, [fp, #-0x20]
    // 0x13546f8: StoreField: r2->field_f = r0
    //     0x13546f8: stur            w0, [x2, #0xf]
    // 0x13546fc: r1 = <Widget>
    //     0x13546fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1354700: r0 = AllocateGrowableArray()
    //     0x1354700: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1354704: mov             x1, x0
    // 0x1354708: ldur            x0, [fp, #-0x20]
    // 0x135470c: stur            x1, [fp, #-0x10]
    // 0x1354710: StoreField: r1->field_f = r0
    //     0x1354710: stur            w0, [x1, #0xf]
    // 0x1354714: r0 = 2
    //     0x1354714: movz            x0, #0x2
    // 0x1354718: StoreField: r1->field_b = r0
    //     0x1354718: stur            w0, [x1, #0xb]
    // 0x135471c: r0 = Row()
    //     0x135471c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1354720: r7 = Instance_Axis
    //     0x1354720: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1354724: stur            x0, [fp, #-0x20]
    // 0x1354728: StoreField: r0->field_f = r7
    //     0x1354728: stur            w7, [x0, #0xf]
    // 0x135472c: r1 = Instance_MainAxisAlignment
    //     0x135472c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0x1354730: ldr             x1, [x1, #0xf28]
    // 0x1354734: StoreField: r0->field_13 = r1
    //     0x1354734: stur            w1, [x0, #0x13]
    // 0x1354738: r8 = Instance_MainAxisSize
    //     0x1354738: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x135473c: ldr             x8, [x8, #0xa10]
    // 0x1354740: ArrayStore: r0[0] = r8  ; List_4
    //     0x1354740: stur            w8, [x0, #0x17]
    // 0x1354744: r9 = Instance_CrossAxisAlignment
    //     0x1354744: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1354748: ldr             x9, [x9, #0xa18]
    // 0x135474c: StoreField: r0->field_1b = r9
    //     0x135474c: stur            w9, [x0, #0x1b]
    // 0x1354750: r10 = Instance_VerticalDirection
    //     0x1354750: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1354754: ldr             x10, [x10, #0xa20]
    // 0x1354758: StoreField: r0->field_23 = r10
    //     0x1354758: stur            w10, [x0, #0x23]
    // 0x135475c: r11 = Instance_Clip
    //     0x135475c: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1354760: ldr             x11, [x11, #0x38]
    // 0x1354764: StoreField: r0->field_2b = r11
    //     0x1354764: stur            w11, [x0, #0x2b]
    // 0x1354768: StoreField: r0->field_2f = rZR
    //     0x1354768: stur            xzr, [x0, #0x2f]
    // 0x135476c: ldur            x1, [fp, #-0x10]
    // 0x1354770: StoreField: r0->field_b = r1
    //     0x1354770: stur            w1, [x0, #0xb]
    // 0x1354774: r0 = Padding()
    //     0x1354774: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1354778: mov             x1, x0
    // 0x135477c: r0 = Instance_EdgeInsets
    //     0x135477c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0x1354780: ldr             x0, [x0, #0xf30]
    // 0x1354784: StoreField: r1->field_f = r0
    //     0x1354784: stur            w0, [x1, #0xf]
    // 0x1354788: ldur            x0, [fp, #-0x20]
    // 0x135478c: StoreField: r1->field_b = r0
    //     0x135478c: stur            w0, [x1, #0xb]
    // 0x1354790: mov             x0, x1
    // 0x1354794: b               #0x13552c8
    // 0x1354798: ldur            x3, [fp, #-8]
    // 0x135479c: r5 = true
    //     0x135479c: add             x5, NULL, #0x20  ; true
    // 0x13547a0: r4 = false
    //     0x13547a0: add             x4, NULL, #0x30  ; false
    // 0x13547a4: r8 = Instance_MainAxisSize
    //     0x13547a4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13547a8: ldr             x8, [x8, #0xa10]
    // 0x13547ac: r6 = Instance_FlexFit
    //     0x13547ac: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13547b0: ldr             x6, [x6, #0xe08]
    // 0x13547b4: r7 = Instance_Axis
    //     0x13547b4: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13547b8: r10 = Instance_VerticalDirection
    //     0x13547b8: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13547bc: ldr             x10, [x10, #0xa20]
    // 0x13547c0: r9 = Instance_CrossAxisAlignment
    //     0x13547c0: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13547c4: ldr             x9, [x9, #0xa18]
    // 0x13547c8: r0 = Instance_BorderSide
    //     0x13547c8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x13547cc: ldr             x0, [x0, #0xe20]
    // 0x13547d0: r11 = Instance_Clip
    //     0x13547d0: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13547d4: ldr             x11, [x11, #0x38]
    // 0x13547d8: d0 = 30.000000
    //     0x13547d8: fmov            d0, #30.00000000
    // 0x13547dc: r2 = 1
    //     0x13547dc: movz            x2, #0x1
    // 0x13547e0: LoadField: r1 = r3->field_f
    //     0x13547e0: ldur            w1, [x3, #0xf]
    // 0x13547e4: DecompressPointer r1
    //     0x13547e4: add             x1, x1, HEAP, lsl #32
    // 0x13547e8: r0 = controller()
    //     0x13547e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13547ec: LoadField: r1 = r0->field_6b
    //     0x13547ec: ldur            w1, [x0, #0x6b]
    // 0x13547f0: DecompressPointer r1
    //     0x13547f0: add             x1, x1, HEAP, lsl #32
    // 0x13547f4: r0 = value()
    //     0x13547f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13547f8: cmp             w0, NULL
    // 0x13547fc: b.eq            #0x1354804
    // 0x1354800: tbz             w0, #4, #0x1354810
    // 0x1354804: r0 = Instance_IconData
    //     0x1354804: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0x1354808: ldr             x0, [x0, #0xc30]
    // 0x135480c: b               #0x1354818
    // 0x1354810: r0 = Instance_IconData
    //     0x1354810: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0x1354814: ldr             x0, [x0, #0xc38]
    // 0x1354818: ldur            x2, [fp, #-8]
    // 0x135481c: stur            x0, [fp, #-0x10]
    // 0x1354820: r0 = Icon()
    //     0x1354820: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1354824: mov             x1, x0
    // 0x1354828: ldur            x0, [fp, #-0x10]
    // 0x135482c: stur            x1, [fp, #-0x20]
    // 0x1354830: StoreField: r1->field_b = r0
    //     0x1354830: stur            w0, [x1, #0xb]
    // 0x1354834: r0 = GetNavigation.size()
    //     0x1354834: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1354838: LoadField: d0 = r0->field_7
    //     0x1354838: ldur            d0, [x0, #7]
    // 0x135483c: d1 = 0.800000
    //     0x135483c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1354840: ldr             d1, [x17, #0xb28]
    // 0x1354844: fmul            d2, d0, d1
    // 0x1354848: stur            d2, [fp, #-0x58]
    // 0x135484c: r0 = BoxConstraints()
    //     0x135484c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1354850: stur            x0, [fp, #-0x10]
    // 0x1354854: StoreField: r0->field_7 = rZR
    //     0x1354854: stur            xzr, [x0, #7]
    // 0x1354858: ldur            d0, [fp, #-0x58]
    // 0x135485c: StoreField: r0->field_f = d0
    //     0x135485c: stur            d0, [x0, #0xf]
    // 0x1354860: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1354860: stur            xzr, [x0, #0x17]
    // 0x1354864: d0 = inf
    //     0x1354864: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1354868: StoreField: r0->field_1f = d0
    //     0x1354868: stur            d0, [x0, #0x1f]
    // 0x135486c: ldur            x2, [fp, #-8]
    // 0x1354870: LoadField: r1 = r2->field_f
    //     0x1354870: ldur            w1, [x2, #0xf]
    // 0x1354874: DecompressPointer r1
    //     0x1354874: add             x1, x1, HEAP, lsl #32
    // 0x1354878: r0 = controller()
    //     0x1354878: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135487c: LoadField: r1 = r0->field_57
    //     0x135487c: ldur            w1, [x0, #0x57]
    // 0x1354880: DecompressPointer r1
    //     0x1354880: add             x1, x1, HEAP, lsl #32
    // 0x1354884: r0 = value()
    //     0x1354884: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354888: cmp             w0, NULL
    // 0x135488c: b.ne            #0x1354898
    // 0x1354890: r0 = Null
    //     0x1354890: mov             x0, NULL
    // 0x1354894: b               #0x13548a4
    // 0x1354898: LoadField: r1 = r0->field_1f
    //     0x1354898: ldur            w1, [x0, #0x1f]
    // 0x135489c: DecompressPointer r1
    //     0x135489c: add             x1, x1, HEAP, lsl #32
    // 0x13548a0: mov             x0, x1
    // 0x13548a4: cmp             w0, NULL
    // 0x13548a8: b.ne            #0x13548b4
    // 0x13548ac: r4 = ""
    //     0x13548ac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13548b0: b               #0x13548b8
    // 0x13548b4: mov             x4, x0
    // 0x13548b8: ldur            x2, [fp, #-8]
    // 0x13548bc: ldur            x3, [fp, #-0x20]
    // 0x13548c0: ldur            x0, [fp, #-0x10]
    // 0x13548c4: stur            x4, [fp, #-0x28]
    // 0x13548c8: LoadField: r1 = r2->field_13
    //     0x13548c8: ldur            w1, [x2, #0x13]
    // 0x13548cc: DecompressPointer r1
    //     0x13548cc: add             x1, x1, HEAP, lsl #32
    // 0x13548d0: r0 = of()
    //     0x13548d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13548d4: LoadField: r1 = r0->field_87
    //     0x13548d4: ldur            w1, [x0, #0x87]
    // 0x13548d8: DecompressPointer r1
    //     0x13548d8: add             x1, x1, HEAP, lsl #32
    // 0x13548dc: LoadField: r0 = r1->field_2b
    //     0x13548dc: ldur            w0, [x1, #0x2b]
    // 0x13548e0: DecompressPointer r0
    //     0x13548e0: add             x0, x0, HEAP, lsl #32
    // 0x13548e4: r16 = 10.000000
    //     0x13548e4: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x13548e8: r30 = Instance_Color
    //     0x13548e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13548ec: stp             lr, x16, [SP]
    // 0x13548f0: mov             x1, x0
    // 0x13548f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13548f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13548f8: ldr             x4, [x4, #0xaa0]
    // 0x13548fc: r0 = copyWith()
    //     0x13548fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1354900: stur            x0, [fp, #-0x30]
    // 0x1354904: r0 = Text()
    //     0x1354904: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1354908: mov             x1, x0
    // 0x135490c: ldur            x0, [fp, #-0x28]
    // 0x1354910: stur            x1, [fp, #-0x38]
    // 0x1354914: StoreField: r1->field_b = r0
    //     0x1354914: stur            w0, [x1, #0xb]
    // 0x1354918: ldur            x0, [fp, #-0x30]
    // 0x135491c: StoreField: r1->field_13 = r0
    //     0x135491c: stur            w0, [x1, #0x13]
    // 0x1354920: r0 = ConstrainedBox()
    //     0x1354920: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1354924: mov             x1, x0
    // 0x1354928: ldur            x0, [fp, #-0x10]
    // 0x135492c: stur            x1, [fp, #-0x28]
    // 0x1354930: StoreField: r1->field_f = r0
    //     0x1354930: stur            w0, [x1, #0xf]
    // 0x1354934: ldur            x0, [fp, #-0x38]
    // 0x1354938: StoreField: r1->field_b = r0
    //     0x1354938: stur            w0, [x1, #0xb]
    // 0x135493c: r0 = Padding()
    //     0x135493c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1354940: mov             x3, x0
    // 0x1354944: r0 = Instance_EdgeInsets
    //     0x1354944: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1354948: ldr             x0, [x0, #0xc40]
    // 0x135494c: stur            x3, [fp, #-0x10]
    // 0x1354950: StoreField: r3->field_f = r0
    //     0x1354950: stur            w0, [x3, #0xf]
    // 0x1354954: ldur            x0, [fp, #-0x28]
    // 0x1354958: StoreField: r3->field_b = r0
    //     0x1354958: stur            w0, [x3, #0xb]
    // 0x135495c: r1 = Null
    //     0x135495c: mov             x1, NULL
    // 0x1354960: r2 = 4
    //     0x1354960: movz            x2, #0x4
    // 0x1354964: r0 = AllocateArray()
    //     0x1354964: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1354968: mov             x2, x0
    // 0x135496c: ldur            x0, [fp, #-0x20]
    // 0x1354970: stur            x2, [fp, #-0x28]
    // 0x1354974: StoreField: r2->field_f = r0
    //     0x1354974: stur            w0, [x2, #0xf]
    // 0x1354978: ldur            x0, [fp, #-0x10]
    // 0x135497c: StoreField: r2->field_13 = r0
    //     0x135497c: stur            w0, [x2, #0x13]
    // 0x1354980: r1 = <Widget>
    //     0x1354980: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1354984: r0 = AllocateGrowableArray()
    //     0x1354984: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1354988: mov             x1, x0
    // 0x135498c: ldur            x0, [fp, #-0x28]
    // 0x1354990: stur            x1, [fp, #-0x10]
    // 0x1354994: StoreField: r1->field_f = r0
    //     0x1354994: stur            w0, [x1, #0xf]
    // 0x1354998: r2 = 4
    //     0x1354998: movz            x2, #0x4
    // 0x135499c: StoreField: r1->field_b = r2
    //     0x135499c: stur            w2, [x1, #0xb]
    // 0x13549a0: r0 = Row()
    //     0x13549a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13549a4: mov             x1, x0
    // 0x13549a8: r0 = Instance_Axis
    //     0x13549a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13549ac: stur            x1, [fp, #-0x20]
    // 0x13549b0: StoreField: r1->field_f = r0
    //     0x13549b0: stur            w0, [x1, #0xf]
    // 0x13549b4: r2 = Instance_MainAxisAlignment
    //     0x13549b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13549b8: ldr             x2, [x2, #0xa08]
    // 0x13549bc: StoreField: r1->field_13 = r2
    //     0x13549bc: stur            w2, [x1, #0x13]
    // 0x13549c0: r3 = Instance_MainAxisSize
    //     0x13549c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13549c4: ldr             x3, [x3, #0xa10]
    // 0x13549c8: ArrayStore: r1[0] = r3  ; List_4
    //     0x13549c8: stur            w3, [x1, #0x17]
    // 0x13549cc: r4 = Instance_CrossAxisAlignment
    //     0x13549cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13549d0: ldr             x4, [x4, #0xa18]
    // 0x13549d4: StoreField: r1->field_1b = r4
    //     0x13549d4: stur            w4, [x1, #0x1b]
    // 0x13549d8: r5 = Instance_VerticalDirection
    //     0x13549d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13549dc: ldr             x5, [x5, #0xa20]
    // 0x13549e0: StoreField: r1->field_23 = r5
    //     0x13549e0: stur            w5, [x1, #0x23]
    // 0x13549e4: r6 = Instance_Clip
    //     0x13549e4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13549e8: ldr             x6, [x6, #0x38]
    // 0x13549ec: StoreField: r1->field_2b = r6
    //     0x13549ec: stur            w6, [x1, #0x2b]
    // 0x13549f0: StoreField: r1->field_2f = rZR
    //     0x13549f0: stur            xzr, [x1, #0x2f]
    // 0x13549f4: ldur            x7, [fp, #-0x10]
    // 0x13549f8: StoreField: r1->field_b = r7
    //     0x13549f8: stur            w7, [x1, #0xb]
    // 0x13549fc: r0 = Padding()
    //     0x13549fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1354a00: mov             x1, x0
    // 0x1354a04: r0 = Instance_EdgeInsets
    //     0x1354a04: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x1354a08: ldr             x0, [x0, #0x868]
    // 0x1354a0c: stur            x1, [fp, #-0x10]
    // 0x1354a10: StoreField: r1->field_f = r0
    //     0x1354a10: stur            w0, [x1, #0xf]
    // 0x1354a14: ldur            x0, [fp, #-0x20]
    // 0x1354a18: StoreField: r1->field_b = r0
    //     0x1354a18: stur            w0, [x1, #0xb]
    // 0x1354a1c: r0 = InkWell()
    //     0x1354a1c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1354a20: mov             x3, x0
    // 0x1354a24: ldur            x0, [fp, #-0x10]
    // 0x1354a28: stur            x3, [fp, #-0x20]
    // 0x1354a2c: StoreField: r3->field_b = r0
    //     0x1354a2c: stur            w0, [x3, #0xb]
    // 0x1354a30: ldur            x2, [fp, #-8]
    // 0x1354a34: r1 = Function '<anonymous closure>':.
    //     0x1354a34: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3f0] AnonymousClosure: (0x135535c), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1362c38)
    //     0x1354a38: ldr             x1, [x1, #0x3f0]
    // 0x1354a3c: r0 = AllocateClosure()
    //     0x1354a3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1354a40: mov             x1, x0
    // 0x1354a44: ldur            x0, [fp, #-0x20]
    // 0x1354a48: StoreField: r0->field_f = r1
    //     0x1354a48: stur            w1, [x0, #0xf]
    // 0x1354a4c: r2 = true
    //     0x1354a4c: add             x2, NULL, #0x20  ; true
    // 0x1354a50: StoreField: r0->field_43 = r2
    //     0x1354a50: stur            w2, [x0, #0x43]
    // 0x1354a54: r1 = Instance_BoxShape
    //     0x1354a54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1354a58: ldr             x1, [x1, #0x80]
    // 0x1354a5c: StoreField: r0->field_47 = r1
    //     0x1354a5c: stur            w1, [x0, #0x47]
    // 0x1354a60: StoreField: r0->field_6f = r2
    //     0x1354a60: stur            w2, [x0, #0x6f]
    // 0x1354a64: r3 = false
    //     0x1354a64: add             x3, NULL, #0x30  ; false
    // 0x1354a68: StoreField: r0->field_73 = r3
    //     0x1354a68: stur            w3, [x0, #0x73]
    // 0x1354a6c: StoreField: r0->field_83 = r2
    //     0x1354a6c: stur            w2, [x0, #0x83]
    // 0x1354a70: StoreField: r0->field_7b = r3
    //     0x1354a70: stur            w3, [x0, #0x7b]
    // 0x1354a74: r1 = <FlexParentData>
    //     0x1354a74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1354a78: ldr             x1, [x1, #0xe00]
    // 0x1354a7c: r0 = Expanded()
    //     0x1354a7c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1354a80: mov             x2, x0
    // 0x1354a84: r0 = 1
    //     0x1354a84: movz            x0, #0x1
    // 0x1354a88: stur            x2, [fp, #-0x10]
    // 0x1354a8c: StoreField: r2->field_13 = r0
    //     0x1354a8c: stur            x0, [x2, #0x13]
    // 0x1354a90: r0 = Instance_FlexFit
    //     0x1354a90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1354a94: ldr             x0, [x0, #0xe08]
    // 0x1354a98: StoreField: r2->field_1b = r0
    //     0x1354a98: stur            w0, [x2, #0x1b]
    // 0x1354a9c: ldur            x1, [fp, #-0x20]
    // 0x1354aa0: StoreField: r2->field_b = r1
    //     0x1354aa0: stur            w1, [x2, #0xb]
    // 0x1354aa4: ldur            x3, [fp, #-8]
    // 0x1354aa8: LoadField: r1 = r3->field_f
    //     0x1354aa8: ldur            w1, [x3, #0xf]
    // 0x1354aac: DecompressPointer r1
    //     0x1354aac: add             x1, x1, HEAP, lsl #32
    // 0x1354ab0: r0 = controller()
    //     0x1354ab0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354ab4: LoadField: r1 = r0->field_57
    //     0x1354ab4: ldur            w1, [x0, #0x57]
    // 0x1354ab8: DecompressPointer r1
    //     0x1354ab8: add             x1, x1, HEAP, lsl #32
    // 0x1354abc: r0 = value()
    //     0x1354abc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354ac0: cmp             w0, NULL
    // 0x1354ac4: b.ne            #0x1354ad0
    // 0x1354ac8: r0 = Null
    //     0x1354ac8: mov             x0, NULL
    // 0x1354acc: b               #0x1354af0
    // 0x1354ad0: LoadField: r1 = r0->field_b
    //     0x1354ad0: ldur            w1, [x0, #0xb]
    // 0x1354ad4: DecompressPointer r1
    //     0x1354ad4: add             x1, x1, HEAP, lsl #32
    // 0x1354ad8: cmp             w1, NULL
    // 0x1354adc: b.ne            #0x1354ae8
    // 0x1354ae0: r0 = Null
    //     0x1354ae0: mov             x0, NULL
    // 0x1354ae4: b               #0x1354af0
    // 0x1354ae8: LoadField: r0 = r1->field_b
    //     0x1354ae8: ldur            w0, [x1, #0xb]
    // 0x1354aec: DecompressPointer r0
    //     0x1354aec: add             x0, x0, HEAP, lsl #32
    // 0x1354af0: ldur            x2, [fp, #-8]
    // 0x1354af4: cbnz            w0, #0x1354b00
    // 0x1354af8: r3 = false
    //     0x1354af8: add             x3, NULL, #0x30  ; false
    // 0x1354afc: b               #0x1354b04
    // 0x1354b00: r3 = true
    //     0x1354b00: add             x3, NULL, #0x20  ; true
    // 0x1354b04: stur            x3, [fp, #-0x20]
    // 0x1354b08: LoadField: r1 = r2->field_f
    //     0x1354b08: ldur            w1, [x2, #0xf]
    // 0x1354b0c: DecompressPointer r1
    //     0x1354b0c: add             x1, x1, HEAP, lsl #32
    // 0x1354b10: r0 = controller()
    //     0x1354b10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354b14: LoadField: r1 = r0->field_57
    //     0x1354b14: ldur            w1, [x0, #0x57]
    // 0x1354b18: DecompressPointer r1
    //     0x1354b18: add             x1, x1, HEAP, lsl #32
    // 0x1354b1c: r0 = value()
    //     0x1354b1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354b20: cmp             w0, NULL
    // 0x1354b24: b.ne            #0x1354b30
    // 0x1354b28: r0 = Null
    //     0x1354b28: mov             x0, NULL
    // 0x1354b2c: b               #0x1354b50
    // 0x1354b30: LoadField: r1 = r0->field_33
    //     0x1354b30: ldur            w1, [x0, #0x33]
    // 0x1354b34: DecompressPointer r1
    //     0x1354b34: add             x1, x1, HEAP, lsl #32
    // 0x1354b38: cmp             w1, NULL
    // 0x1354b3c: b.ne            #0x1354b48
    // 0x1354b40: r0 = Null
    //     0x1354b40: mov             x0, NULL
    // 0x1354b44: b               #0x1354b50
    // 0x1354b48: LoadField: r0 = r1->field_2b
    //     0x1354b48: ldur            w0, [x1, #0x2b]
    // 0x1354b4c: DecompressPointer r0
    //     0x1354b4c: add             x0, x0, HEAP, lsl #32
    // 0x1354b50: cmp             w0, NULL
    // 0x1354b54: b.ne            #0x1354b5c
    // 0x1354b58: r0 = ""
    //     0x1354b58: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1354b5c: ldur            x2, [fp, #-8]
    // 0x1354b60: stur            x0, [fp, #-0x28]
    // 0x1354b64: LoadField: r1 = r2->field_13
    //     0x1354b64: ldur            w1, [x2, #0x13]
    // 0x1354b68: DecompressPointer r1
    //     0x1354b68: add             x1, x1, HEAP, lsl #32
    // 0x1354b6c: r0 = of()
    //     0x1354b6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1354b70: LoadField: r1 = r0->field_87
    //     0x1354b70: ldur            w1, [x0, #0x87]
    // 0x1354b74: DecompressPointer r1
    //     0x1354b74: add             x1, x1, HEAP, lsl #32
    // 0x1354b78: LoadField: r0 = r1->field_7
    //     0x1354b78: ldur            w0, [x1, #7]
    // 0x1354b7c: DecompressPointer r0
    //     0x1354b7c: add             x0, x0, HEAP, lsl #32
    // 0x1354b80: stur            x0, [fp, #-0x30]
    // 0x1354b84: r1 = Instance_Color
    //     0x1354b84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1354b88: d0 = 0.400000
    //     0x1354b88: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1354b8c: r0 = withOpacity()
    //     0x1354b8c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1354b90: r16 = 14.000000
    //     0x1354b90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1354b94: ldr             x16, [x16, #0x1d8]
    // 0x1354b98: stp             x0, x16, [SP]
    // 0x1354b9c: ldur            x1, [fp, #-0x30]
    // 0x1354ba0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1354ba0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1354ba4: ldr             x4, [x4, #0xaa0]
    // 0x1354ba8: r0 = copyWith()
    //     0x1354ba8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1354bac: stur            x0, [fp, #-0x30]
    // 0x1354bb0: r0 = Text()
    //     0x1354bb0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1354bb4: mov             x1, x0
    // 0x1354bb8: ldur            x0, [fp, #-0x28]
    // 0x1354bbc: stur            x1, [fp, #-0x38]
    // 0x1354bc0: StoreField: r1->field_b = r0
    //     0x1354bc0: stur            w0, [x1, #0xb]
    // 0x1354bc4: ldur            x0, [fp, #-0x30]
    // 0x1354bc8: StoreField: r1->field_13 = r0
    //     0x1354bc8: stur            w0, [x1, #0x13]
    // 0x1354bcc: r0 = Padding()
    //     0x1354bcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1354bd0: mov             x2, x0
    // 0x1354bd4: r0 = Instance_EdgeInsets
    //     0x1354bd4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0x1354bd8: ldr             x0, [x0, #0xc50]
    // 0x1354bdc: stur            x2, [fp, #-0x28]
    // 0x1354be0: StoreField: r2->field_f = r0
    //     0x1354be0: stur            w0, [x2, #0xf]
    // 0x1354be4: ldur            x0, [fp, #-0x38]
    // 0x1354be8: StoreField: r2->field_b = r0
    //     0x1354be8: stur            w0, [x2, #0xb]
    // 0x1354bec: ldur            x0, [fp, #-8]
    // 0x1354bf0: LoadField: r1 = r0->field_f
    //     0x1354bf0: ldur            w1, [x0, #0xf]
    // 0x1354bf4: DecompressPointer r1
    //     0x1354bf4: add             x1, x1, HEAP, lsl #32
    // 0x1354bf8: r0 = controller()
    //     0x1354bf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354bfc: LoadField: r1 = r0->field_57
    //     0x1354bfc: ldur            w1, [x0, #0x57]
    // 0x1354c00: DecompressPointer r1
    //     0x1354c00: add             x1, x1, HEAP, lsl #32
    // 0x1354c04: r0 = value()
    //     0x1354c04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354c08: cmp             w0, NULL
    // 0x1354c0c: b.ne            #0x1354c18
    // 0x1354c10: r0 = Null
    //     0x1354c10: mov             x0, NULL
    // 0x1354c14: b               #0x1354c38
    // 0x1354c18: LoadField: r1 = r0->field_b
    //     0x1354c18: ldur            w1, [x0, #0xb]
    // 0x1354c1c: DecompressPointer r1
    //     0x1354c1c: add             x1, x1, HEAP, lsl #32
    // 0x1354c20: cmp             w1, NULL
    // 0x1354c24: b.ne            #0x1354c30
    // 0x1354c28: r0 = Null
    //     0x1354c28: mov             x0, NULL
    // 0x1354c2c: b               #0x1354c38
    // 0x1354c30: LoadField: r0 = r1->field_7
    //     0x1354c30: ldur            w0, [x1, #7]
    // 0x1354c34: DecompressPointer r0
    //     0x1354c34: add             x0, x0, HEAP, lsl #32
    // 0x1354c38: cmp             w0, NULL
    // 0x1354c3c: b.ne            #0x1354c48
    // 0x1354c40: r4 = ""
    //     0x1354c40: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1354c44: b               #0x1354c4c
    // 0x1354c48: mov             x4, x0
    // 0x1354c4c: ldur            x2, [fp, #-8]
    // 0x1354c50: ldur            x0, [fp, #-0x28]
    // 0x1354c54: ldur            x3, [fp, #-0x20]
    // 0x1354c58: stur            x4, [fp, #-0x30]
    // 0x1354c5c: LoadField: r1 = r2->field_13
    //     0x1354c5c: ldur            w1, [x2, #0x13]
    // 0x1354c60: DecompressPointer r1
    //     0x1354c60: add             x1, x1, HEAP, lsl #32
    // 0x1354c64: r0 = of()
    //     0x1354c64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1354c68: LoadField: r1 = r0->field_87
    //     0x1354c68: ldur            w1, [x0, #0x87]
    // 0x1354c6c: DecompressPointer r1
    //     0x1354c6c: add             x1, x1, HEAP, lsl #32
    // 0x1354c70: LoadField: r0 = r1->field_7
    //     0x1354c70: ldur            w0, [x1, #7]
    // 0x1354c74: DecompressPointer r0
    //     0x1354c74: add             x0, x0, HEAP, lsl #32
    // 0x1354c78: r16 = 16.000000
    //     0x1354c78: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1354c7c: ldr             x16, [x16, #0x188]
    // 0x1354c80: r30 = Instance_Color
    //     0x1354c80: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1354c84: stp             lr, x16, [SP]
    // 0x1354c88: mov             x1, x0
    // 0x1354c8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1354c8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1354c90: ldr             x4, [x4, #0xaa0]
    // 0x1354c94: r0 = copyWith()
    //     0x1354c94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1354c98: stur            x0, [fp, #-0x38]
    // 0x1354c9c: r0 = Text()
    //     0x1354c9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1354ca0: mov             x3, x0
    // 0x1354ca4: ldur            x0, [fp, #-0x30]
    // 0x1354ca8: stur            x3, [fp, #-0x40]
    // 0x1354cac: StoreField: r3->field_b = r0
    //     0x1354cac: stur            w0, [x3, #0xb]
    // 0x1354cb0: ldur            x0, [fp, #-0x38]
    // 0x1354cb4: StoreField: r3->field_13 = r0
    //     0x1354cb4: stur            w0, [x3, #0x13]
    // 0x1354cb8: r1 = Null
    //     0x1354cb8: mov             x1, NULL
    // 0x1354cbc: r2 = 4
    //     0x1354cbc: movz            x2, #0x4
    // 0x1354cc0: r0 = AllocateArray()
    //     0x1354cc0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1354cc4: mov             x2, x0
    // 0x1354cc8: ldur            x0, [fp, #-0x28]
    // 0x1354ccc: stur            x2, [fp, #-0x30]
    // 0x1354cd0: StoreField: r2->field_f = r0
    //     0x1354cd0: stur            w0, [x2, #0xf]
    // 0x1354cd4: ldur            x0, [fp, #-0x40]
    // 0x1354cd8: StoreField: r2->field_13 = r0
    //     0x1354cd8: stur            w0, [x2, #0x13]
    // 0x1354cdc: r1 = <Widget>
    //     0x1354cdc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1354ce0: r0 = AllocateGrowableArray()
    //     0x1354ce0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1354ce4: mov             x1, x0
    // 0x1354ce8: ldur            x0, [fp, #-0x30]
    // 0x1354cec: stur            x1, [fp, #-0x28]
    // 0x1354cf0: StoreField: r1->field_f = r0
    //     0x1354cf0: stur            w0, [x1, #0xf]
    // 0x1354cf4: r2 = 4
    //     0x1354cf4: movz            x2, #0x4
    // 0x1354cf8: StoreField: r1->field_b = r2
    //     0x1354cf8: stur            w2, [x1, #0xb]
    // 0x1354cfc: r0 = Column()
    //     0x1354cfc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1354d00: mov             x1, x0
    // 0x1354d04: r0 = Instance_Axis
    //     0x1354d04: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1354d08: stur            x1, [fp, #-0x30]
    // 0x1354d0c: StoreField: r1->field_f = r0
    //     0x1354d0c: stur            w0, [x1, #0xf]
    // 0x1354d10: r2 = Instance_MainAxisAlignment
    //     0x1354d10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1354d14: ldr             x2, [x2, #0xa08]
    // 0x1354d18: StoreField: r1->field_13 = r2
    //     0x1354d18: stur            w2, [x1, #0x13]
    // 0x1354d1c: r3 = Instance_MainAxisSize
    //     0x1354d1c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1354d20: ldr             x3, [x3, #0xa10]
    // 0x1354d24: ArrayStore: r1[0] = r3  ; List_4
    //     0x1354d24: stur            w3, [x1, #0x17]
    // 0x1354d28: r4 = Instance_CrossAxisAlignment
    //     0x1354d28: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1354d2c: ldr             x4, [x4, #0x890]
    // 0x1354d30: StoreField: r1->field_1b = r4
    //     0x1354d30: stur            w4, [x1, #0x1b]
    // 0x1354d34: r4 = Instance_VerticalDirection
    //     0x1354d34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1354d38: ldr             x4, [x4, #0xa20]
    // 0x1354d3c: StoreField: r1->field_23 = r4
    //     0x1354d3c: stur            w4, [x1, #0x23]
    // 0x1354d40: r5 = Instance_Clip
    //     0x1354d40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1354d44: ldr             x5, [x5, #0x38]
    // 0x1354d48: StoreField: r1->field_2b = r5
    //     0x1354d48: stur            w5, [x1, #0x2b]
    // 0x1354d4c: StoreField: r1->field_2f = rZR
    //     0x1354d4c: stur            xzr, [x1, #0x2f]
    // 0x1354d50: ldur            x6, [fp, #-0x28]
    // 0x1354d54: StoreField: r1->field_b = r6
    //     0x1354d54: stur            w6, [x1, #0xb]
    // 0x1354d58: r0 = Visibility()
    //     0x1354d58: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1354d5c: mov             x2, x0
    // 0x1354d60: ldur            x0, [fp, #-0x30]
    // 0x1354d64: stur            x2, [fp, #-0x28]
    // 0x1354d68: StoreField: r2->field_b = r0
    //     0x1354d68: stur            w0, [x2, #0xb]
    // 0x1354d6c: r0 = Instance_SizedBox
    //     0x1354d6c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1354d70: StoreField: r2->field_f = r0
    //     0x1354d70: stur            w0, [x2, #0xf]
    // 0x1354d74: ldur            x0, [fp, #-0x20]
    // 0x1354d78: StoreField: r2->field_13 = r0
    //     0x1354d78: stur            w0, [x2, #0x13]
    // 0x1354d7c: r0 = false
    //     0x1354d7c: add             x0, NULL, #0x30  ; false
    // 0x1354d80: ArrayStore: r2[0] = r0  ; List_4
    //     0x1354d80: stur            w0, [x2, #0x17]
    // 0x1354d84: StoreField: r2->field_1b = r0
    //     0x1354d84: stur            w0, [x2, #0x1b]
    // 0x1354d88: StoreField: r2->field_1f = r0
    //     0x1354d88: stur            w0, [x2, #0x1f]
    // 0x1354d8c: StoreField: r2->field_23 = r0
    //     0x1354d8c: stur            w0, [x2, #0x23]
    // 0x1354d90: StoreField: r2->field_27 = r0
    //     0x1354d90: stur            w0, [x2, #0x27]
    // 0x1354d94: StoreField: r2->field_2b = r0
    //     0x1354d94: stur            w0, [x2, #0x2b]
    // 0x1354d98: ldur            x3, [fp, #-8]
    // 0x1354d9c: LoadField: r1 = r3->field_f
    //     0x1354d9c: ldur            w1, [x3, #0xf]
    // 0x1354da0: DecompressPointer r1
    //     0x1354da0: add             x1, x1, HEAP, lsl #32
    // 0x1354da4: r0 = controller()
    //     0x1354da4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354da8: LoadField: r1 = r0->field_57
    //     0x1354da8: ldur            w1, [x0, #0x57]
    // 0x1354dac: DecompressPointer r1
    //     0x1354dac: add             x1, x1, HEAP, lsl #32
    // 0x1354db0: r0 = value()
    //     0x1354db0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354db4: cmp             w0, NULL
    // 0x1354db8: b.eq            #0x1354dd8
    // 0x1354dbc: LoadField: r1 = r0->field_b
    //     0x1354dbc: ldur            w1, [x0, #0xb]
    // 0x1354dc0: DecompressPointer r1
    //     0x1354dc0: add             x1, x1, HEAP, lsl #32
    // 0x1354dc4: cmp             w1, NULL
    // 0x1354dc8: b.eq            #0x1354dd8
    // 0x1354dcc: LoadField: r0 = r1->field_b
    //     0x1354dcc: ldur            w0, [x1, #0xb]
    // 0x1354dd0: DecompressPointer r0
    //     0x1354dd0: add             x0, x0, HEAP, lsl #32
    // 0x1354dd4: cbz             w0, #0x1354de0
    // 0x1354dd8: r0 = 0
    //     0x1354dd8: movz            x0, #0
    // 0x1354ddc: b               #0x1354de4
    // 0x1354de0: r0 = 1
    //     0x1354de0: movz            x0, #0x1
    // 0x1354de4: ldur            x2, [fp, #-8]
    // 0x1354de8: stur            x0, [fp, #-0x48]
    // 0x1354dec: r16 = <EdgeInsets>
    //     0x1354dec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1354df0: ldr             x16, [x16, #0xda0]
    // 0x1354df4: r30 = Instance_EdgeInsets
    //     0x1354df4: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x1354df8: ldr             lr, [lr, #0x28]
    // 0x1354dfc: stp             lr, x16, [SP]
    // 0x1354e00: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1354e00: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1354e04: r0 = all()
    //     0x1354e04: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1354e08: ldur            x2, [fp, #-8]
    // 0x1354e0c: stur            x0, [fp, #-0x20]
    // 0x1354e10: LoadField: r1 = r2->field_f
    //     0x1354e10: ldur            w1, [x2, #0xf]
    // 0x1354e14: DecompressPointer r1
    //     0x1354e14: add             x1, x1, HEAP, lsl #32
    // 0x1354e18: r0 = controller()
    //     0x1354e18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354e1c: LoadField: r1 = r0->field_8f
    //     0x1354e1c: ldur            w1, [x0, #0x8f]
    // 0x1354e20: DecompressPointer r1
    //     0x1354e20: add             x1, x1, HEAP, lsl #32
    // 0x1354e24: r0 = value()
    //     0x1354e24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354e28: tbnz            w0, #4, #0x1354e70
    // 0x1354e2c: ldur            x2, [fp, #-8]
    // 0x1354e30: LoadField: r1 = r2->field_f
    //     0x1354e30: ldur            w1, [x2, #0xf]
    // 0x1354e34: DecompressPointer r1
    //     0x1354e34: add             x1, x1, HEAP, lsl #32
    // 0x1354e38: r0 = controller()
    //     0x1354e38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354e3c: LoadField: r1 = r0->field_6b
    //     0x1354e3c: ldur            w1, [x0, #0x6b]
    // 0x1354e40: DecompressPointer r1
    //     0x1354e40: add             x1, x1, HEAP, lsl #32
    // 0x1354e44: r0 = value()
    //     0x1354e44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354e48: cmp             w0, NULL
    // 0x1354e4c: b.eq            #0x1354e70
    // 0x1354e50: tbnz            w0, #4, #0x1354e70
    // 0x1354e54: ldur            x2, [fp, #-8]
    // 0x1354e58: LoadField: r1 = r2->field_13
    //     0x1354e58: ldur            w1, [x2, #0x13]
    // 0x1354e5c: DecompressPointer r1
    //     0x1354e5c: add             x1, x1, HEAP, lsl #32
    // 0x1354e60: r0 = of()
    //     0x1354e60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1354e64: LoadField: r1 = r0->field_5b
    //     0x1354e64: ldur            w1, [x0, #0x5b]
    // 0x1354e68: DecompressPointer r1
    //     0x1354e68: add             x1, x1, HEAP, lsl #32
    // 0x1354e6c: b               #0x1354e80
    // 0x1354e70: r1 = Instance_Color
    //     0x1354e70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1354e74: d0 = 0.400000
    //     0x1354e74: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1354e78: r0 = withOpacity()
    //     0x1354e78: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1354e7c: mov             x1, x0
    // 0x1354e80: ldur            x2, [fp, #-8]
    // 0x1354e84: ldur            x0, [fp, #-0x20]
    // 0x1354e88: r16 = <Color>
    //     0x1354e88: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1354e8c: ldr             x16, [x16, #0xf80]
    // 0x1354e90: stp             x1, x16, [SP]
    // 0x1354e94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1354e94: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1354e98: r0 = all()
    //     0x1354e98: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1354e9c: stur            x0, [fp, #-0x30]
    // 0x1354ea0: r0 = Radius()
    //     0x1354ea0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1354ea4: d0 = 30.000000
    //     0x1354ea4: fmov            d0, #30.00000000
    // 0x1354ea8: stur            x0, [fp, #-0x38]
    // 0x1354eac: StoreField: r0->field_7 = d0
    //     0x1354eac: stur            d0, [x0, #7]
    // 0x1354eb0: StoreField: r0->field_f = d0
    //     0x1354eb0: stur            d0, [x0, #0xf]
    // 0x1354eb4: r0 = BorderRadius()
    //     0x1354eb4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1354eb8: mov             x1, x0
    // 0x1354ebc: ldur            x0, [fp, #-0x38]
    // 0x1354ec0: stur            x1, [fp, #-0x40]
    // 0x1354ec4: StoreField: r1->field_7 = r0
    //     0x1354ec4: stur            w0, [x1, #7]
    // 0x1354ec8: StoreField: r1->field_b = r0
    //     0x1354ec8: stur            w0, [x1, #0xb]
    // 0x1354ecc: StoreField: r1->field_f = r0
    //     0x1354ecc: stur            w0, [x1, #0xf]
    // 0x1354ed0: StoreField: r1->field_13 = r0
    //     0x1354ed0: stur            w0, [x1, #0x13]
    // 0x1354ed4: r0 = RoundedRectangleBorder()
    //     0x1354ed4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1354ed8: mov             x1, x0
    // 0x1354edc: ldur            x0, [fp, #-0x40]
    // 0x1354ee0: StoreField: r1->field_b = r0
    //     0x1354ee0: stur            w0, [x1, #0xb]
    // 0x1354ee4: r0 = Instance_BorderSide
    //     0x1354ee4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1354ee8: ldr             x0, [x0, #0xe20]
    // 0x1354eec: StoreField: r1->field_7 = r0
    //     0x1354eec: stur            w0, [x1, #7]
    // 0x1354ef0: r16 = <RoundedRectangleBorder>
    //     0x1354ef0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1354ef4: ldr             x16, [x16, #0xf78]
    // 0x1354ef8: stp             x1, x16, [SP]
    // 0x1354efc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1354efc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1354f00: r0 = all()
    //     0x1354f00: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1354f04: stur            x0, [fp, #-0x38]
    // 0x1354f08: r0 = ButtonStyle()
    //     0x1354f08: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1354f0c: mov             x1, x0
    // 0x1354f10: ldur            x0, [fp, #-0x30]
    // 0x1354f14: stur            x1, [fp, #-0x40]
    // 0x1354f18: StoreField: r1->field_b = r0
    //     0x1354f18: stur            w0, [x1, #0xb]
    // 0x1354f1c: ldur            x0, [fp, #-0x20]
    // 0x1354f20: StoreField: r1->field_23 = r0
    //     0x1354f20: stur            w0, [x1, #0x23]
    // 0x1354f24: ldur            x0, [fp, #-0x38]
    // 0x1354f28: StoreField: r1->field_43 = r0
    //     0x1354f28: stur            w0, [x1, #0x43]
    // 0x1354f2c: r0 = TextButtonThemeData()
    //     0x1354f2c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1354f30: mov             x2, x0
    // 0x1354f34: ldur            x0, [fp, #-0x40]
    // 0x1354f38: stur            x2, [fp, #-0x20]
    // 0x1354f3c: StoreField: r2->field_7 = r0
    //     0x1354f3c: stur            w0, [x2, #7]
    // 0x1354f40: ldur            x0, [fp, #-8]
    // 0x1354f44: LoadField: r1 = r0->field_f
    //     0x1354f44: ldur            w1, [x0, #0xf]
    // 0x1354f48: DecompressPointer r1
    //     0x1354f48: add             x1, x1, HEAP, lsl #32
    // 0x1354f4c: r0 = controller()
    //     0x1354f4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354f50: LoadField: r1 = r0->field_6b
    //     0x1354f50: ldur            w1, [x0, #0x6b]
    // 0x1354f54: DecompressPointer r1
    //     0x1354f54: add             x1, x1, HEAP, lsl #32
    // 0x1354f58: r0 = value()
    //     0x1354f58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1354f5c: cmp             w0, NULL
    // 0x1354f60: b.eq            #0x1355010
    // 0x1354f64: tbnz            w0, #4, #0x1355010
    // 0x1354f68: ldur            x2, [fp, #-8]
    // 0x1354f6c: LoadField: r1 = r2->field_f
    //     0x1354f6c: ldur            w1, [x2, #0xf]
    // 0x1354f70: DecompressPointer r1
    //     0x1354f70: add             x1, x1, HEAP, lsl #32
    // 0x1354f74: r0 = controller()
    //     0x1354f74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354f78: LoadField: r1 = r0->field_67
    //     0x1354f78: ldur            w1, [x0, #0x67]
    // 0x1354f7c: DecompressPointer r1
    //     0x1354f7c: add             x1, x1, HEAP, lsl #32
    // 0x1354f80: r0 = RxnStringExt.isNotEmpty()
    //     0x1354f80: bl              #0x1325838  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxnStringExt.isNotEmpty
    // 0x1354f84: cmp             w0, NULL
    // 0x1354f88: b.eq            #0x1355010
    // 0x1354f8c: tbnz            w0, #4, #0x1355010
    // 0x1354f90: ldur            x2, [fp, #-8]
    // 0x1354f94: LoadField: r1 = r2->field_f
    //     0x1354f94: ldur            w1, [x2, #0xf]
    // 0x1354f98: DecompressPointer r1
    //     0x1354f98: add             x1, x1, HEAP, lsl #32
    // 0x1354f9c: r0 = controller()
    //     0x1354f9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354fa0: LoadField: r1 = r0->field_db
    //     0x1354fa0: ldur            w1, [x0, #0xdb]
    // 0x1354fa4: DecompressPointer r1
    //     0x1354fa4: add             x1, x1, HEAP, lsl #32
    // 0x1354fa8: LoadField: r0 = r1->field_27
    //     0x1354fa8: ldur            w0, [x1, #0x27]
    // 0x1354fac: DecompressPointer r0
    //     0x1354fac: add             x0, x0, HEAP, lsl #32
    // 0x1354fb0: LoadField: r1 = r0->field_7
    //     0x1354fb0: ldur            w1, [x0, #7]
    // 0x1354fb4: DecompressPointer r1
    //     0x1354fb4: add             x1, x1, HEAP, lsl #32
    // 0x1354fb8: LoadField: r0 = r1->field_7
    //     0x1354fb8: ldur            w0, [x1, #7]
    // 0x1354fbc: cbz             w0, #0x1355010
    // 0x1354fc0: ldur            x2, [fp, #-8]
    // 0x1354fc4: LoadField: r1 = r2->field_f
    //     0x1354fc4: ldur            w1, [x2, #0xf]
    // 0x1354fc8: DecompressPointer r1
    //     0x1354fc8: add             x1, x1, HEAP, lsl #32
    // 0x1354fcc: r0 = controller()
    //     0x1354fcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354fd0: LoadField: r1 = r0->field_db
    //     0x1354fd0: ldur            w1, [x0, #0xdb]
    // 0x1354fd4: DecompressPointer r1
    //     0x1354fd4: add             x1, x1, HEAP, lsl #32
    // 0x1354fd8: LoadField: r0 = r1->field_27
    //     0x1354fd8: ldur            w0, [x1, #0x27]
    // 0x1354fdc: DecompressPointer r0
    //     0x1354fdc: add             x0, x0, HEAP, lsl #32
    // 0x1354fe0: LoadField: r1 = r0->field_7
    //     0x1354fe0: ldur            w1, [x0, #7]
    // 0x1354fe4: DecompressPointer r1
    //     0x1354fe4: add             x1, x1, HEAP, lsl #32
    // 0x1354fe8: LoadField: r0 = r1->field_7
    //     0x1354fe8: ldur            w0, [x1, #7]
    // 0x1354fec: r1 = LoadInt32Instr(r0)
    //     0x1354fec: sbfx            x1, x0, #1, #0x1f
    // 0x1354ff0: cmp             x1, #9
    // 0x1354ff4: b.le            #0x1355010
    // 0x1354ff8: ldur            x2, [fp, #-8]
    // 0x1354ffc: r1 = Function '<anonymous closure>':.
    //     0x1354ffc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3f8] AnonymousClosure: (0x132588c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x1355000: ldr             x1, [x1, #0x3f8]
    // 0x1355004: r0 = AllocateClosure()
    //     0x1355004: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1355008: mov             x2, x0
    // 0x135500c: b               #0x1355014
    // 0x1355010: r2 = Null
    //     0x1355010: mov             x2, NULL
    // 0x1355014: ldur            x0, [fp, #-8]
    // 0x1355018: stur            x2, [fp, #-0x30]
    // 0x135501c: LoadField: r1 = r0->field_f
    //     0x135501c: ldur            w1, [x0, #0xf]
    // 0x1355020: DecompressPointer r1
    //     0x1355020: add             x1, x1, HEAP, lsl #32
    // 0x1355024: r0 = controller()
    //     0x1355024: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355028: LoadField: r1 = r0->field_57
    //     0x1355028: ldur            w1, [x0, #0x57]
    // 0x135502c: DecompressPointer r1
    //     0x135502c: add             x1, x1, HEAP, lsl #32
    // 0x1355030: r0 = value()
    //     0x1355030: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355034: cmp             w0, NULL
    // 0x1355038: b.ne            #0x1355044
    // 0x135503c: r0 = Null
    //     0x135503c: mov             x0, NULL
    // 0x1355040: b               #0x1355064
    // 0x1355044: LoadField: r1 = r0->field_33
    //     0x1355044: ldur            w1, [x0, #0x33]
    // 0x1355048: DecompressPointer r1
    //     0x1355048: add             x1, x1, HEAP, lsl #32
    // 0x135504c: cmp             w1, NULL
    // 0x1355050: b.ne            #0x135505c
    // 0x1355054: r0 = Null
    //     0x1355054: mov             x0, NULL
    // 0x1355058: b               #0x1355064
    // 0x135505c: LoadField: r0 = r1->field_27
    //     0x135505c: ldur            w0, [x1, #0x27]
    // 0x1355060: DecompressPointer r0
    //     0x1355060: add             x0, x0, HEAP, lsl #32
    // 0x1355064: cmp             w0, NULL
    // 0x1355068: b.ne            #0x1355074
    // 0x135506c: r7 = ""
    //     0x135506c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1355070: b               #0x1355078
    // 0x1355074: mov             x7, x0
    // 0x1355078: ldur            x0, [fp, #-8]
    // 0x135507c: ldur            x6, [fp, #-0x10]
    // 0x1355080: ldur            x5, [fp, #-0x28]
    // 0x1355084: ldur            x4, [fp, #-0x48]
    // 0x1355088: ldur            x3, [fp, #-0x20]
    // 0x135508c: ldur            x2, [fp, #-0x30]
    // 0x1355090: stur            x7, [fp, #-0x38]
    // 0x1355094: LoadField: r1 = r0->field_13
    //     0x1355094: ldur            w1, [x0, #0x13]
    // 0x1355098: DecompressPointer r1
    //     0x1355098: add             x1, x1, HEAP, lsl #32
    // 0x135509c: r0 = of()
    //     0x135509c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13550a0: LoadField: r1 = r0->field_87
    //     0x13550a0: ldur            w1, [x0, #0x87]
    // 0x13550a4: DecompressPointer r1
    //     0x13550a4: add             x1, x1, HEAP, lsl #32
    // 0x13550a8: LoadField: r0 = r1->field_7
    //     0x13550a8: ldur            w0, [x1, #7]
    // 0x13550ac: DecompressPointer r0
    //     0x13550ac: add             x0, x0, HEAP, lsl #32
    // 0x13550b0: r16 = Instance_Color
    //     0x13550b0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13550b4: r30 = 14.000000
    //     0x13550b4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13550b8: ldr             lr, [lr, #0x1d8]
    // 0x13550bc: stp             lr, x16, [SP]
    // 0x13550c0: mov             x1, x0
    // 0x13550c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13550c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13550c8: ldr             x4, [x4, #0x9b8]
    // 0x13550cc: r0 = copyWith()
    //     0x13550cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13550d0: stur            x0, [fp, #-8]
    // 0x13550d4: r0 = Text()
    //     0x13550d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13550d8: mov             x1, x0
    // 0x13550dc: ldur            x0, [fp, #-0x38]
    // 0x13550e0: stur            x1, [fp, #-0x40]
    // 0x13550e4: StoreField: r1->field_b = r0
    //     0x13550e4: stur            w0, [x1, #0xb]
    // 0x13550e8: ldur            x0, [fp, #-8]
    // 0x13550ec: StoreField: r1->field_13 = r0
    //     0x13550ec: stur            w0, [x1, #0x13]
    // 0x13550f0: r0 = TextButton()
    //     0x13550f0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13550f4: mov             x1, x0
    // 0x13550f8: ldur            x0, [fp, #-0x30]
    // 0x13550fc: stur            x1, [fp, #-8]
    // 0x1355100: StoreField: r1->field_b = r0
    //     0x1355100: stur            w0, [x1, #0xb]
    // 0x1355104: r0 = false
    //     0x1355104: add             x0, NULL, #0x30  ; false
    // 0x1355108: StoreField: r1->field_27 = r0
    //     0x1355108: stur            w0, [x1, #0x27]
    // 0x135510c: r0 = true
    //     0x135510c: add             x0, NULL, #0x20  ; true
    // 0x1355110: StoreField: r1->field_2f = r0
    //     0x1355110: stur            w0, [x1, #0x2f]
    // 0x1355114: ldur            x0, [fp, #-0x40]
    // 0x1355118: StoreField: r1->field_37 = r0
    //     0x1355118: stur            w0, [x1, #0x37]
    // 0x135511c: r0 = TextButtonTheme()
    //     0x135511c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1355120: mov             x2, x0
    // 0x1355124: ldur            x0, [fp, #-0x20]
    // 0x1355128: stur            x2, [fp, #-0x30]
    // 0x135512c: StoreField: r2->field_f = r0
    //     0x135512c: stur            w0, [x2, #0xf]
    // 0x1355130: ldur            x0, [fp, #-8]
    // 0x1355134: StoreField: r2->field_b = r0
    //     0x1355134: stur            w0, [x2, #0xb]
    // 0x1355138: r1 = <FlexParentData>
    //     0x1355138: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x135513c: ldr             x1, [x1, #0xe00]
    // 0x1355140: r0 = Expanded()
    //     0x1355140: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1355144: mov             x3, x0
    // 0x1355148: ldur            x0, [fp, #-0x48]
    // 0x135514c: stur            x3, [fp, #-8]
    // 0x1355150: StoreField: r3->field_13 = r0
    //     0x1355150: stur            x0, [x3, #0x13]
    // 0x1355154: r0 = Instance_FlexFit
    //     0x1355154: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1355158: ldr             x0, [x0, #0xe08]
    // 0x135515c: StoreField: r3->field_1b = r0
    //     0x135515c: stur            w0, [x3, #0x1b]
    // 0x1355160: ldur            x0, [fp, #-0x30]
    // 0x1355164: StoreField: r3->field_b = r0
    //     0x1355164: stur            w0, [x3, #0xb]
    // 0x1355168: r1 = Null
    //     0x1355168: mov             x1, NULL
    // 0x135516c: r2 = 4
    //     0x135516c: movz            x2, #0x4
    // 0x1355170: r0 = AllocateArray()
    //     0x1355170: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1355174: mov             x2, x0
    // 0x1355178: ldur            x0, [fp, #-0x28]
    // 0x135517c: stur            x2, [fp, #-0x20]
    // 0x1355180: StoreField: r2->field_f = r0
    //     0x1355180: stur            w0, [x2, #0xf]
    // 0x1355184: ldur            x0, [fp, #-8]
    // 0x1355188: StoreField: r2->field_13 = r0
    //     0x1355188: stur            w0, [x2, #0x13]
    // 0x135518c: r1 = <Widget>
    //     0x135518c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1355190: r0 = AllocateGrowableArray()
    //     0x1355190: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1355194: mov             x1, x0
    // 0x1355198: ldur            x0, [fp, #-0x20]
    // 0x135519c: stur            x1, [fp, #-8]
    // 0x13551a0: StoreField: r1->field_f = r0
    //     0x13551a0: stur            w0, [x1, #0xf]
    // 0x13551a4: r2 = 4
    //     0x13551a4: movz            x2, #0x4
    // 0x13551a8: StoreField: r1->field_b = r2
    //     0x13551a8: stur            w2, [x1, #0xb]
    // 0x13551ac: r0 = Row()
    //     0x13551ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13551b0: mov             x3, x0
    // 0x13551b4: r0 = Instance_Axis
    //     0x13551b4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13551b8: stur            x3, [fp, #-0x20]
    // 0x13551bc: StoreField: r3->field_f = r0
    //     0x13551bc: stur            w0, [x3, #0xf]
    // 0x13551c0: r0 = Instance_MainAxisAlignment
    //     0x13551c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x13551c4: ldr             x0, [x0, #0xa8]
    // 0x13551c8: StoreField: r3->field_13 = r0
    //     0x13551c8: stur            w0, [x3, #0x13]
    // 0x13551cc: r0 = Instance_MainAxisSize
    //     0x13551cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13551d0: ldr             x0, [x0, #0xa10]
    // 0x13551d4: ArrayStore: r3[0] = r0  ; List_4
    //     0x13551d4: stur            w0, [x3, #0x17]
    // 0x13551d8: r1 = Instance_CrossAxisAlignment
    //     0x13551d8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x13551dc: ldr             x1, [x1, #0xc68]
    // 0x13551e0: StoreField: r3->field_1b = r1
    //     0x13551e0: stur            w1, [x3, #0x1b]
    // 0x13551e4: r4 = Instance_VerticalDirection
    //     0x13551e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13551e8: ldr             x4, [x4, #0xa20]
    // 0x13551ec: StoreField: r3->field_23 = r4
    //     0x13551ec: stur            w4, [x3, #0x23]
    // 0x13551f0: r5 = Instance_Clip
    //     0x13551f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13551f4: ldr             x5, [x5, #0x38]
    // 0x13551f8: StoreField: r3->field_2b = r5
    //     0x13551f8: stur            w5, [x3, #0x2b]
    // 0x13551fc: StoreField: r3->field_2f = rZR
    //     0x13551fc: stur            xzr, [x3, #0x2f]
    // 0x1355200: ldur            x1, [fp, #-8]
    // 0x1355204: StoreField: r3->field_b = r1
    //     0x1355204: stur            w1, [x3, #0xb]
    // 0x1355208: r1 = Null
    //     0x1355208: mov             x1, NULL
    // 0x135520c: r2 = 4
    //     0x135520c: movz            x2, #0x4
    // 0x1355210: r0 = AllocateArray()
    //     0x1355210: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1355214: mov             x2, x0
    // 0x1355218: ldur            x0, [fp, #-0x10]
    // 0x135521c: stur            x2, [fp, #-8]
    // 0x1355220: StoreField: r2->field_f = r0
    //     0x1355220: stur            w0, [x2, #0xf]
    // 0x1355224: ldur            x0, [fp, #-0x20]
    // 0x1355228: StoreField: r2->field_13 = r0
    //     0x1355228: stur            w0, [x2, #0x13]
    // 0x135522c: r1 = <Widget>
    //     0x135522c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1355230: r0 = AllocateGrowableArray()
    //     0x1355230: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1355234: mov             x1, x0
    // 0x1355238: ldur            x0, [fp, #-8]
    // 0x135523c: stur            x1, [fp, #-0x10]
    // 0x1355240: StoreField: r1->field_f = r0
    //     0x1355240: stur            w0, [x1, #0xf]
    // 0x1355244: r0 = 4
    //     0x1355244: movz            x0, #0x4
    // 0x1355248: StoreField: r1->field_b = r0
    //     0x1355248: stur            w0, [x1, #0xb]
    // 0x135524c: r0 = Column()
    //     0x135524c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1355250: mov             x1, x0
    // 0x1355254: r0 = Instance_Axis
    //     0x1355254: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1355258: stur            x1, [fp, #-8]
    // 0x135525c: StoreField: r1->field_f = r0
    //     0x135525c: stur            w0, [x1, #0xf]
    // 0x1355260: r0 = Instance_MainAxisAlignment
    //     0x1355260: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1355264: ldr             x0, [x0, #0xa08]
    // 0x1355268: StoreField: r1->field_13 = r0
    //     0x1355268: stur            w0, [x1, #0x13]
    // 0x135526c: r0 = Instance_MainAxisSize
    //     0x135526c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1355270: ldr             x0, [x0, #0xa10]
    // 0x1355274: ArrayStore: r1[0] = r0  ; List_4
    //     0x1355274: stur            w0, [x1, #0x17]
    // 0x1355278: r0 = Instance_CrossAxisAlignment
    //     0x1355278: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135527c: ldr             x0, [x0, #0xa18]
    // 0x1355280: StoreField: r1->field_1b = r0
    //     0x1355280: stur            w0, [x1, #0x1b]
    // 0x1355284: r0 = Instance_VerticalDirection
    //     0x1355284: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1355288: ldr             x0, [x0, #0xa20]
    // 0x135528c: StoreField: r1->field_23 = r0
    //     0x135528c: stur            w0, [x1, #0x23]
    // 0x1355290: r0 = Instance_Clip
    //     0x1355290: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1355294: ldr             x0, [x0, #0x38]
    // 0x1355298: StoreField: r1->field_2b = r0
    //     0x1355298: stur            w0, [x1, #0x2b]
    // 0x135529c: StoreField: r1->field_2f = rZR
    //     0x135529c: stur            xzr, [x1, #0x2f]
    // 0x13552a0: ldur            x0, [fp, #-0x10]
    // 0x13552a4: StoreField: r1->field_b = r0
    //     0x13552a4: stur            w0, [x1, #0xb]
    // 0x13552a8: r0 = Padding()
    //     0x13552a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13552ac: mov             x1, x0
    // 0x13552b0: r0 = Instance_EdgeInsets
    //     0x13552b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x13552b4: ldr             x0, [x0, #0xd0]
    // 0x13552b8: StoreField: r1->field_f = r0
    //     0x13552b8: stur            w0, [x1, #0xf]
    // 0x13552bc: ldur            x0, [fp, #-8]
    // 0x13552c0: StoreField: r1->field_b = r0
    //     0x13552c0: stur            w0, [x1, #0xb]
    // 0x13552c4: mov             x0, x1
    // 0x13552c8: ldur            d0, [fp, #-0x50]
    // 0x13552cc: stur            x0, [fp, #-8]
    // 0x13552d0: r0 = Container()
    //     0x13552d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13552d4: stur            x0, [fp, #-0x10]
    // 0x13552d8: ldur            x16, [fp, #-0x18]
    // 0x13552dc: ldur            lr, [fp, #-8]
    // 0x13552e0: stp             lr, x16, [SP]
    // 0x13552e4: mov             x1, x0
    // 0x13552e8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13552e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13552ec: ldr             x4, [x4, #0x88]
    // 0x13552f0: r0 = Container()
    //     0x13552f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13552f4: ldur            d0, [fp, #-0x50]
    // 0x13552f8: r0 = inline_Allocate_Double()
    //     0x13552f8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13552fc: add             x0, x0, #0x10
    //     0x1355300: cmp             x1, x0
    //     0x1355304: b.ls            #0x135534c
    //     0x1355308: str             x0, [THR, #0x50]  ; THR::top
    //     0x135530c: sub             x0, x0, #0xf
    //     0x1355310: movz            x1, #0xe15c
    //     0x1355314: movk            x1, #0x3, lsl #16
    //     0x1355318: stur            x1, [x0, #-1]
    // 0x135531c: StoreField: r0->field_7 = d0
    //     0x135531c: stur            d0, [x0, #7]
    // 0x1355320: stur            x0, [fp, #-8]
    // 0x1355324: r0 = SizedBox()
    //     0x1355324: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1355328: ldur            x1, [fp, #-8]
    // 0x135532c: StoreField: r0->field_13 = r1
    //     0x135532c: stur            w1, [x0, #0x13]
    // 0x1355330: ldur            x1, [fp, #-0x10]
    // 0x1355334: StoreField: r0->field_b = r1
    //     0x1355334: stur            w1, [x0, #0xb]
    // 0x1355338: LeaveFrame
    //     0x1355338: mov             SP, fp
    //     0x135533c: ldp             fp, lr, [SP], #0x10
    // 0x1355340: ret
    //     0x1355340: ret             
    // 0x1355344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1355344: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1355348: b               #0x1354300
    // 0x135534c: SaveReg d0
    //     0x135534c: str             q0, [SP, #-0x10]!
    // 0x1355350: r0 = AllocateDouble()
    //     0x1355350: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1355354: RestoreReg d0
    //     0x1355354: ldr             q0, [SP], #0x10
    // 0x1355358: b               #0x135531c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x135535c, size: 0xd0
    // 0x135535c: EnterFrame
    //     0x135535c: stp             fp, lr, [SP, #-0x10]!
    //     0x1355360: mov             fp, SP
    // 0x1355364: AllocStack(0x8)
    //     0x1355364: sub             SP, SP, #8
    // 0x1355368: SetupParameters()
    //     0x1355368: ldr             x0, [fp, #0x10]
    //     0x135536c: ldur            w2, [x0, #0x17]
    //     0x1355370: add             x2, x2, HEAP, lsl #32
    //     0x1355374: stur            x2, [fp, #-8]
    // 0x1355378: CheckStackOverflow
    //     0x1355378: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135537c: cmp             SP, x16
    //     0x1355380: b.ls            #0x1355424
    // 0x1355384: LoadField: r1 = r2->field_f
    //     0x1355384: ldur            w1, [x2, #0xf]
    // 0x1355388: DecompressPointer r1
    //     0x1355388: add             x1, x1, HEAP, lsl #32
    // 0x135538c: r0 = controller()
    //     0x135538c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355390: LoadField: r1 = r0->field_6b
    //     0x1355390: ldur            w1, [x0, #0x6b]
    // 0x1355394: DecompressPointer r1
    //     0x1355394: add             x1, x1, HEAP, lsl #32
    // 0x1355398: r0 = value()
    //     0x1355398: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135539c: cmp             w0, NULL
    // 0x13553a0: b.ne            #0x13553ac
    // 0x13553a4: ldur            x0, [fp, #-8]
    // 0x13553a8: b               #0x13553e8
    // 0x13553ac: tbnz            w0, #4, #0x13553e4
    // 0x13553b0: ldur            x0, [fp, #-8]
    // 0x13553b4: LoadField: r1 = r0->field_f
    //     0x13553b4: ldur            w1, [x0, #0xf]
    // 0x13553b8: DecompressPointer r1
    //     0x13553b8: add             x1, x1, HEAP, lsl #32
    // 0x13553bc: r0 = controller()
    //     0x13553bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13553c0: LoadField: r1 = r0->field_6b
    //     0x13553c0: ldur            w1, [x0, #0x6b]
    // 0x13553c4: DecompressPointer r1
    //     0x13553c4: add             x1, x1, HEAP, lsl #32
    // 0x13553c8: r2 = false
    //     0x13553c8: add             x2, NULL, #0x30  ; false
    // 0x13553cc: r0 = value=()
    //     0x13553cc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13553d0: ldur            x0, [fp, #-8]
    // 0x13553d4: LoadField: r1 = r0->field_f
    //     0x13553d4: ldur            w1, [x0, #0xf]
    // 0x13553d8: DecompressPointer r1
    //     0x13553d8: add             x1, x1, HEAP, lsl #32
    // 0x13553dc: r0 = isButtonEnabled()
    //     0x13553dc: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x13553e0: b               #0x1355414
    // 0x13553e4: ldur            x0, [fp, #-8]
    // 0x13553e8: LoadField: r1 = r0->field_f
    //     0x13553e8: ldur            w1, [x0, #0xf]
    // 0x13553ec: DecompressPointer r1
    //     0x13553ec: add             x1, x1, HEAP, lsl #32
    // 0x13553f0: r0 = controller()
    //     0x13553f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13553f4: LoadField: r1 = r0->field_6b
    //     0x13553f4: ldur            w1, [x0, #0x6b]
    // 0x13553f8: DecompressPointer r1
    //     0x13553f8: add             x1, x1, HEAP, lsl #32
    // 0x13553fc: r2 = true
    //     0x13553fc: add             x2, NULL, #0x20  ; true
    // 0x1355400: r0 = value=()
    //     0x1355400: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1355404: ldur            x0, [fp, #-8]
    // 0x1355408: LoadField: r1 = r0->field_f
    //     0x1355408: ldur            w1, [x0, #0xf]
    // 0x135540c: DecompressPointer r1
    //     0x135540c: add             x1, x1, HEAP, lsl #32
    // 0x1355410: r0 = isButtonEnabled()
    //     0x1355410: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x1355414: r0 = Null
    //     0x1355414: mov             x0, NULL
    // 0x1355418: LeaveFrame
    //     0x1355418: mov             SP, fp
    //     0x135541c: ldp             fp, lr, [SP], #0x10
    // 0x1355420: ret
    //     0x1355420: ret             
    // 0x1355424: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1355424: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1355428: b               #0x1355384
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1362c38, size: 0x64
    // 0x1362c38: EnterFrame
    //     0x1362c38: stp             fp, lr, [SP, #-0x10]!
    //     0x1362c3c: mov             fp, SP
    // 0x1362c40: AllocStack(0x18)
    //     0x1362c40: sub             SP, SP, #0x18
    // 0x1362c44: SetupParameters(ReturnOrderView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1362c44: stur            x1, [fp, #-8]
    //     0x1362c48: stur            x2, [fp, #-0x10]
    // 0x1362c4c: r1 = 2
    //     0x1362c4c: movz            x1, #0x2
    // 0x1362c50: r0 = AllocateContext()
    //     0x1362c50: bl              #0x16f6108  ; AllocateContextStub
    // 0x1362c54: mov             x1, x0
    // 0x1362c58: ldur            x0, [fp, #-8]
    // 0x1362c5c: stur            x1, [fp, #-0x18]
    // 0x1362c60: StoreField: r1->field_f = r0
    //     0x1362c60: stur            w0, [x1, #0xf]
    // 0x1362c64: ldur            x0, [fp, #-0x10]
    // 0x1362c68: StoreField: r1->field_13 = r0
    //     0x1362c68: stur            w0, [x1, #0x13]
    // 0x1362c6c: r0 = Obx()
    //     0x1362c6c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1362c70: ldur            x2, [fp, #-0x18]
    // 0x1362c74: r1 = Function '<anonymous closure>':.
    //     0x1362c74: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f3d8] AnonymousClosure: (0x13542d8), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1362c38)
    //     0x1362c78: ldr             x1, [x1, #0x3d8]
    // 0x1362c7c: stur            x0, [fp, #-8]
    // 0x1362c80: r0 = AllocateClosure()
    //     0x1362c80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1362c84: mov             x1, x0
    // 0x1362c88: ldur            x0, [fp, #-8]
    // 0x1362c8c: StoreField: r0->field_b = r1
    //     0x1362c8c: stur            w1, [x0, #0xb]
    // 0x1362c90: LeaveFrame
    //     0x1362c90: mov             SP, fp
    //     0x1362c94: ldp             fp, lr, [SP], #0x10
    // 0x1362c98: ret
    //     0x1362c98: ret             
  }
  _ body(/* No info */) {
    // ** addr: 0x14f840c, size: 0x110
    // 0x14f840c: EnterFrame
    //     0x14f840c: stp             fp, lr, [SP, #-0x10]!
    //     0x14f8410: mov             fp, SP
    // 0x14f8414: AllocStack(0x38)
    //     0x14f8414: sub             SP, SP, #0x38
    // 0x14f8418: SetupParameters(ReturnOrderView this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x14f8418: stur            x1, [fp, #-8]
    //     0x14f841c: mov             x16, x2
    //     0x14f8420: mov             x2, x1
    //     0x14f8424: mov             x1, x16
    //     0x14f8428: stur            x1, [fp, #-0x10]
    // 0x14f842c: CheckStackOverflow
    //     0x14f842c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f8430: cmp             SP, x16
    //     0x14f8434: b.ls            #0x14f8514
    // 0x14f8438: r1 = 2
    //     0x14f8438: movz            x1, #0x2
    // 0x14f843c: r0 = AllocateContext()
    //     0x14f843c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f8440: ldur            x2, [fp, #-8]
    // 0x14f8444: stur            x0, [fp, #-0x18]
    // 0x14f8448: StoreField: r0->field_f = r2
    //     0x14f8448: stur            w2, [x0, #0xf]
    // 0x14f844c: ldur            x1, [fp, #-0x10]
    // 0x14f8450: StoreField: r0->field_13 = r1
    //     0x14f8450: stur            w1, [x0, #0x13]
    // 0x14f8454: r0 = of()
    //     0x14f8454: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14f8458: LoadField: r1 = r0->field_5b
    //     0x14f8458: ldur            w1, [x0, #0x5b]
    // 0x14f845c: DecompressPointer r1
    //     0x14f845c: add             x1, x1, HEAP, lsl #32
    // 0x14f8460: r0 = LoadClassIdInstr(r1)
    //     0x14f8460: ldur            x0, [x1, #-1]
    //     0x14f8464: ubfx            x0, x0, #0xc, #0x14
    // 0x14f8468: d0 = 0.030000
    //     0x14f8468: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14f846c: ldr             d0, [x17, #0x238]
    // 0x14f8470: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14f8470: sub             lr, x0, #0xffa
    //     0x14f8474: ldr             lr, [x21, lr, lsl #3]
    //     0x14f8478: blr             lr
    // 0x14f847c: stur            x0, [fp, #-0x10]
    // 0x14f8480: r0 = Obx()
    //     0x14f8480: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14f8484: ldur            x2, [fp, #-0x18]
    // 0x14f8488: r1 = Function '<anonymous closure>':.
    //     0x14f8488: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f400] AnonymousClosure: (0xb4ef1c), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0x14f848c: ldr             x1, [x1, #0x400]
    // 0x14f8490: stur            x0, [fp, #-0x18]
    // 0x14f8494: r0 = AllocateClosure()
    //     0x14f8494: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f8498: mov             x1, x0
    // 0x14f849c: ldur            x0, [fp, #-0x18]
    // 0x14f84a0: StoreField: r0->field_b = r1
    //     0x14f84a0: stur            w1, [x0, #0xb]
    // 0x14f84a4: r0 = WillPopScope()
    //     0x14f84a4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14f84a8: mov             x3, x0
    // 0x14f84ac: ldur            x0, [fp, #-0x18]
    // 0x14f84b0: stur            x3, [fp, #-0x20]
    // 0x14f84b4: StoreField: r3->field_b = r0
    //     0x14f84b4: stur            w0, [x3, #0xb]
    // 0x14f84b8: ldur            x2, [fp, #-8]
    // 0x14f84bc: r1 = Function 'onBackPress':.
    //     0x14f84bc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f408] AnonymousClosure: (0x14f851c), in [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress (0x1441560)
    //     0x14f84c0: ldr             x1, [x1, #0x408]
    // 0x14f84c4: r0 = AllocateClosure()
    //     0x14f84c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f84c8: mov             x1, x0
    // 0x14f84cc: ldur            x0, [fp, #-0x20]
    // 0x14f84d0: StoreField: r0->field_f = r1
    //     0x14f84d0: stur            w1, [x0, #0xf]
    // 0x14f84d4: r0 = Container()
    //     0x14f84d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14f84d8: stur            x0, [fp, #-8]
    // 0x14f84dc: ldur            x16, [fp, #-0x10]
    // 0x14f84e0: r30 = inf
    //     0x14f84e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14f84e4: ldr             lr, [lr, #0x9f8]
    // 0x14f84e8: stp             lr, x16, [SP, #8]
    // 0x14f84ec: ldur            x16, [fp, #-0x20]
    // 0x14f84f0: str             x16, [SP]
    // 0x14f84f4: mov             x1, x0
    // 0x14f84f8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, height, 0x2, null]
    //     0x14f84f8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f308] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "height", 0x2, Null]
    //     0x14f84fc: ldr             x4, [x4, #0x308]
    // 0x14f8500: r0 = Container()
    //     0x14f8500: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14f8504: ldur            x0, [fp, #-8]
    // 0x14f8508: LeaveFrame
    //     0x14f8508: mov             SP, fp
    //     0x14f850c: ldp             fp, lr, [SP], #0x10
    // 0x14f8510: ret
    //     0x14f8510: ret             
    // 0x14f8514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f8514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f8518: b               #0x14f8438
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14f851c, size: 0x38
    // 0x14f851c: EnterFrame
    //     0x14f851c: stp             fp, lr, [SP, #-0x10]!
    //     0x14f8520: mov             fp, SP
    // 0x14f8524: ldr             x0, [fp, #0x10]
    // 0x14f8528: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f8528: ldur            w1, [x0, #0x17]
    // 0x14f852c: DecompressPointer r1
    //     0x14f852c: add             x1, x1, HEAP, lsl #32
    // 0x14f8530: CheckStackOverflow
    //     0x14f8530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f8534: cmp             SP, x16
    //     0x14f8538: b.ls            #0x14f854c
    // 0x14f853c: r0 = onBackPress()
    //     0x14f853c: bl              #0x1441560  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress
    // 0x14f8540: LeaveFrame
    //     0x14f8540: mov             SP, fp
    //     0x14f8544: ldp             fp, lr, [SP], #0x10
    // 0x14f8548: ret
    //     0x14f8548: ret             
    // 0x14f854c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f854c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f8550: b               #0x14f853c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e46b8, size: 0x18c
    // 0x15e46b8: EnterFrame
    //     0x15e46b8: stp             fp, lr, [SP, #-0x10]!
    //     0x15e46bc: mov             fp, SP
    // 0x15e46c0: AllocStack(0x28)
    //     0x15e46c0: sub             SP, SP, #0x28
    // 0x15e46c4: SetupParameters(ReturnOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e46c4: mov             x0, x1
    //     0x15e46c8: stur            x1, [fp, #-8]
    //     0x15e46cc: mov             x1, x2
    //     0x15e46d0: stur            x2, [fp, #-0x10]
    // 0x15e46d4: CheckStackOverflow
    //     0x15e46d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e46d8: cmp             SP, x16
    //     0x15e46dc: b.ls            #0x15e483c
    // 0x15e46e0: r1 = 2
    //     0x15e46e0: movz            x1, #0x2
    // 0x15e46e4: r0 = AllocateContext()
    //     0x15e46e4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e46e8: mov             x1, x0
    // 0x15e46ec: ldur            x0, [fp, #-8]
    // 0x15e46f0: stur            x1, [fp, #-0x18]
    // 0x15e46f4: StoreField: r1->field_f = r0
    //     0x15e46f4: stur            w0, [x1, #0xf]
    // 0x15e46f8: ldur            x0, [fp, #-0x10]
    // 0x15e46fc: StoreField: r1->field_13 = r0
    //     0x15e46fc: stur            w0, [x1, #0x13]
    // 0x15e4700: r0 = Obx()
    //     0x15e4700: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e4704: ldur            x2, [fp, #-0x18]
    // 0x15e4708: r1 = Function '<anonymous closure>':.
    //     0x15e4708: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f470] AnonymousClosure: (0x15d5454), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e470c: ldr             x1, [x1, #0x470]
    // 0x15e4710: stur            x0, [fp, #-8]
    // 0x15e4714: r0 = AllocateClosure()
    //     0x15e4714: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e4718: mov             x1, x0
    // 0x15e471c: ldur            x0, [fp, #-8]
    // 0x15e4720: StoreField: r0->field_b = r1
    //     0x15e4720: stur            w1, [x0, #0xb]
    // 0x15e4724: ldur            x1, [fp, #-0x10]
    // 0x15e4728: r0 = of()
    //     0x15e4728: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e472c: LoadField: r1 = r0->field_5b
    //     0x15e472c: ldur            w1, [x0, #0x5b]
    // 0x15e4730: DecompressPointer r1
    //     0x15e4730: add             x1, x1, HEAP, lsl #32
    // 0x15e4734: stur            x1, [fp, #-0x10]
    // 0x15e4738: r0 = ColorFilter()
    //     0x15e4738: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e473c: mov             x1, x0
    // 0x15e4740: ldur            x0, [fp, #-0x10]
    // 0x15e4744: stur            x1, [fp, #-0x20]
    // 0x15e4748: StoreField: r1->field_7 = r0
    //     0x15e4748: stur            w0, [x1, #7]
    // 0x15e474c: r0 = Instance_BlendMode
    //     0x15e474c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e4750: ldr             x0, [x0, #0xb30]
    // 0x15e4754: StoreField: r1->field_b = r0
    //     0x15e4754: stur            w0, [x1, #0xb]
    // 0x15e4758: r0 = 1
    //     0x15e4758: movz            x0, #0x1
    // 0x15e475c: StoreField: r1->field_13 = r0
    //     0x15e475c: stur            x0, [x1, #0x13]
    // 0x15e4760: r0 = SvgPicture()
    //     0x15e4760: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e4764: stur            x0, [fp, #-0x10]
    // 0x15e4768: ldur            x16, [fp, #-0x20]
    // 0x15e476c: str             x16, [SP]
    // 0x15e4770: mov             x1, x0
    // 0x15e4774: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e4774: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e4778: ldr             x2, [x2, #0xa40]
    // 0x15e477c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e477c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e4780: ldr             x4, [x4, #0xa38]
    // 0x15e4784: r0 = SvgPicture.asset()
    //     0x15e4784: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e4788: r0 = Align()
    //     0x15e4788: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e478c: mov             x1, x0
    // 0x15e4790: r0 = Instance_Alignment
    //     0x15e4790: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e4794: ldr             x0, [x0, #0xb10]
    // 0x15e4798: stur            x1, [fp, #-0x20]
    // 0x15e479c: StoreField: r1->field_f = r0
    //     0x15e479c: stur            w0, [x1, #0xf]
    // 0x15e47a0: r0 = 1.000000
    //     0x15e47a0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e47a4: StoreField: r1->field_13 = r0
    //     0x15e47a4: stur            w0, [x1, #0x13]
    // 0x15e47a8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e47a8: stur            w0, [x1, #0x17]
    // 0x15e47ac: ldur            x0, [fp, #-0x10]
    // 0x15e47b0: StoreField: r1->field_b = r0
    //     0x15e47b0: stur            w0, [x1, #0xb]
    // 0x15e47b4: r0 = InkWell()
    //     0x15e47b4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e47b8: mov             x3, x0
    // 0x15e47bc: ldur            x0, [fp, #-0x20]
    // 0x15e47c0: stur            x3, [fp, #-0x10]
    // 0x15e47c4: StoreField: r3->field_b = r0
    //     0x15e47c4: stur            w0, [x3, #0xb]
    // 0x15e47c8: ldur            x2, [fp, #-0x18]
    // 0x15e47cc: r1 = Function '<anonymous closure>':.
    //     0x15e47cc: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f478] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e47d0: ldr             x1, [x1, #0x478]
    // 0x15e47d4: r0 = AllocateClosure()
    //     0x15e47d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e47d8: ldur            x2, [fp, #-0x10]
    // 0x15e47dc: StoreField: r2->field_f = r0
    //     0x15e47dc: stur            w0, [x2, #0xf]
    // 0x15e47e0: r0 = true
    //     0x15e47e0: add             x0, NULL, #0x20  ; true
    // 0x15e47e4: StoreField: r2->field_43 = r0
    //     0x15e47e4: stur            w0, [x2, #0x43]
    // 0x15e47e8: r1 = Instance_BoxShape
    //     0x15e47e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e47ec: ldr             x1, [x1, #0x80]
    // 0x15e47f0: StoreField: r2->field_47 = r1
    //     0x15e47f0: stur            w1, [x2, #0x47]
    // 0x15e47f4: StoreField: r2->field_6f = r0
    //     0x15e47f4: stur            w0, [x2, #0x6f]
    // 0x15e47f8: r1 = false
    //     0x15e47f8: add             x1, NULL, #0x30  ; false
    // 0x15e47fc: StoreField: r2->field_73 = r1
    //     0x15e47fc: stur            w1, [x2, #0x73]
    // 0x15e4800: StoreField: r2->field_83 = r0
    //     0x15e4800: stur            w0, [x2, #0x83]
    // 0x15e4804: StoreField: r2->field_7b = r1
    //     0x15e4804: stur            w1, [x2, #0x7b]
    // 0x15e4808: r0 = AppBar()
    //     0x15e4808: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e480c: stur            x0, [fp, #-0x18]
    // 0x15e4810: ldur            x16, [fp, #-8]
    // 0x15e4814: str             x16, [SP]
    // 0x15e4818: mov             x1, x0
    // 0x15e481c: ldur            x2, [fp, #-0x10]
    // 0x15e4820: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e4820: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e4824: ldr             x4, [x4, #0xf00]
    // 0x15e4828: r0 = AppBar()
    //     0x15e4828: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e482c: ldur            x0, [fp, #-0x18]
    // 0x15e4830: LeaveFrame
    //     0x15e4830: mov             SP, fp
    //     0x15e4834: ldp             fp, lr, [SP], #0x10
    // 0x15e4838: ret
    //     0x15e4838: ret             
    // 0x15e483c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e483c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e4840: b               #0x15e46e0
  }
}
