// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/select_quantity_picker.dart

// class id: 1049325, size: 0x8
class :: {
}

// class id: 3394, size: 0x14, field offset: 0x14
class _SelectQuantityPickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb184a4, size: 0x5f4
    // 0xb184a4: EnterFrame
    //     0xb184a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb184a8: mov             fp, SP
    // 0xb184ac: AllocStack(0x80)
    //     0xb184ac: sub             SP, SP, #0x80
    // 0xb184b0: SetupParameters(_SelectQuantityPickerState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb184b0: mov             x0, x1
    //     0xb184b4: stur            x1, [fp, #-8]
    //     0xb184b8: mov             x1, x2
    //     0xb184bc: stur            x2, [fp, #-0x10]
    // 0xb184c0: CheckStackOverflow
    //     0xb184c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb184c4: cmp             SP, x16
    //     0xb184c8: b.ls            #0xb18a8c
    // 0xb184cc: r1 = 1
    //     0xb184cc: movz            x1, #0x1
    // 0xb184d0: r0 = AllocateContext()
    //     0xb184d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb184d4: mov             x2, x0
    // 0xb184d8: ldur            x0, [fp, #-8]
    // 0xb184dc: stur            x2, [fp, #-0x18]
    // 0xb184e0: StoreField: r2->field_f = r0
    //     0xb184e0: stur            w0, [x2, #0xf]
    // 0xb184e4: ldur            x1, [fp, #-0x10]
    // 0xb184e8: r0 = of()
    //     0xb184e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb184ec: LoadField: r1 = r0->field_87
    //     0xb184ec: ldur            w1, [x0, #0x87]
    // 0xb184f0: DecompressPointer r1
    //     0xb184f0: add             x1, x1, HEAP, lsl #32
    // 0xb184f4: LoadField: r0 = r1->field_7
    //     0xb184f4: ldur            w0, [x1, #7]
    // 0xb184f8: DecompressPointer r0
    //     0xb184f8: add             x0, x0, HEAP, lsl #32
    // 0xb184fc: r16 = 16.000000
    //     0xb184fc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb18500: ldr             x16, [x16, #0x188]
    // 0xb18504: r30 = Instance_Color
    //     0xb18504: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb18508: stp             lr, x16, [SP]
    // 0xb1850c: mov             x1, x0
    // 0xb18510: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb18510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb18514: ldr             x4, [x4, #0xaa0]
    // 0xb18518: r0 = copyWith()
    //     0xb18518: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1851c: stur            x0, [fp, #-0x20]
    // 0xb18520: r0 = Text()
    //     0xb18520: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb18524: mov             x1, x0
    // 0xb18528: r0 = "Quantity"
    //     0xb18528: add             x0, PP, #0x52, lsl #12  ; [pp+0x523f8] "Quantity"
    //     0xb1852c: ldr             x0, [x0, #0x3f8]
    // 0xb18530: stur            x1, [fp, #-0x28]
    // 0xb18534: StoreField: r1->field_b = r0
    //     0xb18534: stur            w0, [x1, #0xb]
    // 0xb18538: ldur            x0, [fp, #-0x20]
    // 0xb1853c: StoreField: r1->field_13 = r0
    //     0xb1853c: stur            w0, [x1, #0x13]
    // 0xb18540: r0 = Radius()
    //     0xb18540: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb18544: d0 = 30.000000
    //     0xb18544: fmov            d0, #30.00000000
    // 0xb18548: stur            x0, [fp, #-0x20]
    // 0xb1854c: StoreField: r0->field_7 = d0
    //     0xb1854c: stur            d0, [x0, #7]
    // 0xb18550: StoreField: r0->field_f = d0
    //     0xb18550: stur            d0, [x0, #0xf]
    // 0xb18554: r0 = BorderRadius()
    //     0xb18554: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb18558: mov             x1, x0
    // 0xb1855c: ldur            x0, [fp, #-0x20]
    // 0xb18560: stur            x1, [fp, #-0x30]
    // 0xb18564: StoreField: r1->field_7 = r0
    //     0xb18564: stur            w0, [x1, #7]
    // 0xb18568: StoreField: r1->field_b = r0
    //     0xb18568: stur            w0, [x1, #0xb]
    // 0xb1856c: StoreField: r1->field_f = r0
    //     0xb1856c: stur            w0, [x1, #0xf]
    // 0xb18570: StoreField: r1->field_13 = r0
    //     0xb18570: stur            w0, [x1, #0x13]
    // 0xb18574: r0 = BoxDecoration()
    //     0xb18574: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb18578: mov             x2, x0
    // 0xb1857c: r0 = Instance_Color
    //     0xb1857c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb18580: stur            x2, [fp, #-0x20]
    // 0xb18584: StoreField: r2->field_7 = r0
    //     0xb18584: stur            w0, [x2, #7]
    // 0xb18588: ldur            x0, [fp, #-0x30]
    // 0xb1858c: StoreField: r2->field_13 = r0
    //     0xb1858c: stur            w0, [x2, #0x13]
    // 0xb18590: r0 = Instance_BoxShape
    //     0xb18590: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb18594: ldr             x0, [x0, #0x80]
    // 0xb18598: StoreField: r2->field_23 = r0
    //     0xb18598: stur            w0, [x2, #0x23]
    // 0xb1859c: ldur            x1, [fp, #-0x10]
    // 0xb185a0: r0 = of()
    //     0xb185a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb185a4: LoadField: r1 = r0->field_5b
    //     0xb185a4: ldur            w1, [x0, #0x5b]
    // 0xb185a8: DecompressPointer r1
    //     0xb185a8: add             x1, x1, HEAP, lsl #32
    // 0xb185ac: stur            x1, [fp, #-0x30]
    // 0xb185b0: r0 = ColorFilter()
    //     0xb185b0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb185b4: mov             x1, x0
    // 0xb185b8: ldur            x0, [fp, #-0x30]
    // 0xb185bc: stur            x1, [fp, #-0x38]
    // 0xb185c0: StoreField: r1->field_7 = r0
    //     0xb185c0: stur            w0, [x1, #7]
    // 0xb185c4: r0 = Instance_BlendMode
    //     0xb185c4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb185c8: ldr             x0, [x0, #0xb30]
    // 0xb185cc: StoreField: r1->field_b = r0
    //     0xb185cc: stur            w0, [x1, #0xb]
    // 0xb185d0: r2 = 1
    //     0xb185d0: movz            x2, #0x1
    // 0xb185d4: StoreField: r1->field_13 = r2
    //     0xb185d4: stur            x2, [x1, #0x13]
    // 0xb185d8: r0 = SvgPicture()
    //     0xb185d8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb185dc: stur            x0, [fp, #-0x30]
    // 0xb185e0: r16 = Instance_BoxFit
    //     0xb185e0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb185e4: ldr             x16, [x16, #0xb18]
    // 0xb185e8: ldur            lr, [fp, #-0x38]
    // 0xb185ec: stp             lr, x16, [SP, #0x10]
    // 0xb185f0: r16 = 40.000000
    //     0xb185f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb185f4: ldr             x16, [x16, #8]
    // 0xb185f8: r30 = 40.000000
    //     0xb185f8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb185fc: ldr             lr, [lr, #8]
    // 0xb18600: stp             lr, x16, [SP]
    // 0xb18604: mov             x1, x0
    // 0xb18608: r2 = "assets/images/qty_minus.svg"
    //     0xb18608: add             x2, PP, #0x57, lsl #12  ; [pp+0x578e0] "assets/images/qty_minus.svg"
    //     0xb1860c: ldr             x2, [x2, #0x8e0]
    // 0xb18610: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x3, fit, 0x2, height, 0x4, width, 0x5, null]
    //     0xb18610: add             x4, PP, #0x43, lsl #12  ; [pp+0x436e0] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x3, "fit", 0x2, "height", 0x4, "width", 0x5, Null]
    //     0xb18614: ldr             x4, [x4, #0x6e0]
    // 0xb18618: r0 = SvgPicture.asset()
    //     0xb18618: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb1861c: r0 = InkWell()
    //     0xb1861c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb18620: mov             x3, x0
    // 0xb18624: ldur            x0, [fp, #-0x30]
    // 0xb18628: stur            x3, [fp, #-0x38]
    // 0xb1862c: StoreField: r3->field_b = r0
    //     0xb1862c: stur            w0, [x3, #0xb]
    // 0xb18630: ldur            x2, [fp, #-0x18]
    // 0xb18634: r1 = Function '<anonymous closure>':.
    //     0xb18634: add             x1, PP, #0x57, lsl #12  ; [pp+0x578e8] AnonymousClosure: (0xb18b9c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xb184a4)
    //     0xb18638: ldr             x1, [x1, #0x8e8]
    // 0xb1863c: r0 = AllocateClosure()
    //     0xb1863c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb18640: mov             x1, x0
    // 0xb18644: ldur            x0, [fp, #-0x38]
    // 0xb18648: StoreField: r0->field_f = r1
    //     0xb18648: stur            w1, [x0, #0xf]
    // 0xb1864c: r1 = true
    //     0xb1864c: add             x1, NULL, #0x20  ; true
    // 0xb18650: StoreField: r0->field_43 = r1
    //     0xb18650: stur            w1, [x0, #0x43]
    // 0xb18654: r2 = Instance_BoxShape
    //     0xb18654: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb18658: ldr             x2, [x2, #0x80]
    // 0xb1865c: StoreField: r0->field_47 = r2
    //     0xb1865c: stur            w2, [x0, #0x47]
    // 0xb18660: StoreField: r0->field_6f = r1
    //     0xb18660: stur            w1, [x0, #0x6f]
    // 0xb18664: r3 = false
    //     0xb18664: add             x3, NULL, #0x30  ; false
    // 0xb18668: StoreField: r0->field_73 = r3
    //     0xb18668: stur            w3, [x0, #0x73]
    // 0xb1866c: StoreField: r0->field_83 = r1
    //     0xb1866c: stur            w1, [x0, #0x83]
    // 0xb18670: StoreField: r0->field_7b = r3
    //     0xb18670: stur            w3, [x0, #0x7b]
    // 0xb18674: ldur            x4, [fp, #-8]
    // 0xb18678: LoadField: r5 = r4->field_b
    //     0xb18678: ldur            w5, [x4, #0xb]
    // 0xb1867c: DecompressPointer r5
    //     0xb1867c: add             x5, x5, HEAP, lsl #32
    // 0xb18680: cmp             w5, NULL
    // 0xb18684: b.eq            #0xb18a94
    // 0xb18688: LoadField: r4 = r5->field_f
    //     0xb18688: ldur            w4, [x5, #0xf]
    // 0xb1868c: DecompressPointer r4
    //     0xb1868c: add             x4, x4, HEAP, lsl #32
    // 0xb18690: stur            x4, [fp, #-8]
    // 0xb18694: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb18694: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb18698: ldr             x0, [x0, #0x1530]
    //     0xb1869c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb186a0: cmp             w0, w16
    //     0xb186a4: b.ne            #0xb186b4
    //     0xb186a8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb186ac: ldr             x2, [x2, #0x120]
    //     0xb186b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb186b4: r1 = Null
    //     0xb186b4: mov             x1, NULL
    // 0xb186b8: r2 = 2
    //     0xb186b8: movz            x2, #0x2
    // 0xb186bc: stur            x0, [fp, #-0x30]
    // 0xb186c0: r0 = AllocateArray()
    //     0xb186c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb186c4: mov             x2, x0
    // 0xb186c8: ldur            x0, [fp, #-0x30]
    // 0xb186cc: stur            x2, [fp, #-0x40]
    // 0xb186d0: StoreField: r2->field_f = r0
    //     0xb186d0: stur            w0, [x2, #0xf]
    // 0xb186d4: r1 = <TextInputFormatter>
    //     0xb186d4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb186d8: ldr             x1, [x1, #0x7b0]
    // 0xb186dc: r0 = AllocateGrowableArray()
    //     0xb186dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb186e0: mov             x2, x0
    // 0xb186e4: ldur            x0, [fp, #-0x40]
    // 0xb186e8: stur            x2, [fp, #-0x30]
    // 0xb186ec: StoreField: r2->field_f = r0
    //     0xb186ec: stur            w0, [x2, #0xf]
    // 0xb186f0: r0 = 2
    //     0xb186f0: movz            x0, #0x2
    // 0xb186f4: StoreField: r2->field_b = r0
    //     0xb186f4: stur            w0, [x2, #0xb]
    // 0xb186f8: ldur            x1, [fp, #-0x10]
    // 0xb186fc: r0 = of()
    //     0xb186fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb18700: LoadField: r1 = r0->field_87
    //     0xb18700: ldur            w1, [x0, #0x87]
    // 0xb18704: DecompressPointer r1
    //     0xb18704: add             x1, x1, HEAP, lsl #32
    // 0xb18708: LoadField: r0 = r1->field_7
    //     0xb18708: ldur            w0, [x1, #7]
    // 0xb1870c: DecompressPointer r0
    //     0xb1870c: add             x0, x0, HEAP, lsl #32
    // 0xb18710: r16 = 16.000000
    //     0xb18710: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb18714: ldr             x16, [x16, #0x188]
    // 0xb18718: r30 = Instance_Color
    //     0xb18718: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1871c: stp             lr, x16, [SP]
    // 0xb18720: mov             x1, x0
    // 0xb18724: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb18724: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb18728: ldr             x4, [x4, #0xaa0]
    // 0xb1872c: r0 = copyWith()
    //     0xb1872c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb18730: r1 = <String>
    //     0xb18730: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb18734: stur            x0, [fp, #-0x40]
    // 0xb18738: r0 = TextFormField()
    //     0xb18738: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb1873c: stur            x0, [fp, #-0x48]
    // 0xb18740: r16 = false
    //     0xb18740: add             x16, NULL, #0x30  ; false
    // 0xb18744: ldur            lr, [fp, #-8]
    // 0xb18748: stp             lr, x16, [SP, #0x28]
    // 0xb1874c: r16 = Instance_TextInputType
    //     0xb1874c: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb18750: ldr             x16, [x16, #0x1a0]
    // 0xb18754: r30 = true
    //     0xb18754: add             lr, NULL, #0x20  ; true
    // 0xb18758: stp             lr, x16, [SP, #0x18]
    // 0xb1875c: ldur            x16, [fp, #-0x30]
    // 0xb18760: r30 = Instance_TextAlign
    //     0xb18760: ldr             lr, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb18764: stp             lr, x16, [SP, #8]
    // 0xb18768: ldur            x16, [fp, #-0x40]
    // 0xb1876c: str             x16, [SP]
    // 0xb18770: mov             x1, x0
    // 0xb18774: r2 = Instance_InputDecoration
    //     0xb18774: add             x2, PP, #0x57, lsl #12  ; [pp+0x578f0] Obj!InputDecoration@d5a931
    //     0xb18778: ldr             x2, [x2, #0x8f0]
    // 0xb1877c: r4 = const [0, 0x9, 0x7, 0x2, controller, 0x3, enableInteractiveSelection, 0x2, inputFormatters, 0x6, keyboardType, 0x4, readOnly, 0x5, style, 0x8, textAlign, 0x7, null]
    //     0xb1877c: add             x4, PP, #0x57, lsl #12  ; [pp+0x578f8] List(19) [0, 0x9, 0x7, 0x2, "controller", 0x3, "enableInteractiveSelection", 0x2, "inputFormatters", 0x6, "keyboardType", 0x4, "readOnly", 0x5, "style", 0x8, "textAlign", 0x7, Null]
    //     0xb18780: ldr             x4, [x4, #0x8f8]
    // 0xb18784: r0 = TextFormField()
    //     0xb18784: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb18788: r1 = <FlexParentData>
    //     0xb18788: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb1878c: ldr             x1, [x1, #0xe00]
    // 0xb18790: r0 = Expanded()
    //     0xb18790: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb18794: mov             x2, x0
    // 0xb18798: r0 = 1
    //     0xb18798: movz            x0, #0x1
    // 0xb1879c: stur            x2, [fp, #-8]
    // 0xb187a0: StoreField: r2->field_13 = r0
    //     0xb187a0: stur            x0, [x2, #0x13]
    // 0xb187a4: r1 = Instance_FlexFit
    //     0xb187a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb187a8: ldr             x1, [x1, #0xe08]
    // 0xb187ac: StoreField: r2->field_1b = r1
    //     0xb187ac: stur            w1, [x2, #0x1b]
    // 0xb187b0: ldur            x1, [fp, #-0x48]
    // 0xb187b4: StoreField: r2->field_b = r1
    //     0xb187b4: stur            w1, [x2, #0xb]
    // 0xb187b8: ldur            x1, [fp, #-0x10]
    // 0xb187bc: r0 = of()
    //     0xb187bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb187c0: LoadField: r1 = r0->field_5b
    //     0xb187c0: ldur            w1, [x0, #0x5b]
    // 0xb187c4: DecompressPointer r1
    //     0xb187c4: add             x1, x1, HEAP, lsl #32
    // 0xb187c8: stur            x1, [fp, #-0x10]
    // 0xb187cc: r0 = ColorFilter()
    //     0xb187cc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb187d0: mov             x1, x0
    // 0xb187d4: ldur            x0, [fp, #-0x10]
    // 0xb187d8: stur            x1, [fp, #-0x30]
    // 0xb187dc: StoreField: r1->field_7 = r0
    //     0xb187dc: stur            w0, [x1, #7]
    // 0xb187e0: r0 = Instance_BlendMode
    //     0xb187e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb187e4: ldr             x0, [x0, #0xb30]
    // 0xb187e8: StoreField: r1->field_b = r0
    //     0xb187e8: stur            w0, [x1, #0xb]
    // 0xb187ec: r0 = 1
    //     0xb187ec: movz            x0, #0x1
    // 0xb187f0: StoreField: r1->field_13 = r0
    //     0xb187f0: stur            x0, [x1, #0x13]
    // 0xb187f4: r0 = SvgPicture()
    //     0xb187f4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb187f8: stur            x0, [fp, #-0x10]
    // 0xb187fc: r16 = Instance_BoxFit
    //     0xb187fc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb18800: ldr             x16, [x16, #0xb18]
    // 0xb18804: ldur            lr, [fp, #-0x30]
    // 0xb18808: stp             lr, x16, [SP, #0x10]
    // 0xb1880c: r16 = 40.000000
    //     0xb1880c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb18810: ldr             x16, [x16, #8]
    // 0xb18814: r30 = 40.000000
    //     0xb18814: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb18818: ldr             lr, [lr, #8]
    // 0xb1881c: stp             lr, x16, [SP]
    // 0xb18820: mov             x1, x0
    // 0xb18824: r2 = "assets/images/qty_add.svg"
    //     0xb18824: add             x2, PP, #0x57, lsl #12  ; [pp+0x57900] "assets/images/qty_add.svg"
    //     0xb18828: ldr             x2, [x2, #0x900]
    // 0xb1882c: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x3, fit, 0x2, height, 0x4, width, 0x5, null]
    //     0xb1882c: add             x4, PP, #0x43, lsl #12  ; [pp+0x436e0] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x3, "fit", 0x2, "height", 0x4, "width", 0x5, Null]
    //     0xb18830: ldr             x4, [x4, #0x6e0]
    // 0xb18834: r0 = SvgPicture.asset()
    //     0xb18834: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb18838: r0 = InkWell()
    //     0xb18838: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb1883c: mov             x3, x0
    // 0xb18840: ldur            x0, [fp, #-0x10]
    // 0xb18844: stur            x3, [fp, #-0x30]
    // 0xb18848: StoreField: r3->field_b = r0
    //     0xb18848: stur            w0, [x3, #0xb]
    // 0xb1884c: ldur            x2, [fp, #-0x18]
    // 0xb18850: r1 = Function '<anonymous closure>':.
    //     0xb18850: add             x1, PP, #0x57, lsl #12  ; [pp+0x57908] AnonymousClosure: (0xb18abc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xb184a4)
    //     0xb18854: ldr             x1, [x1, #0x908]
    // 0xb18858: r0 = AllocateClosure()
    //     0xb18858: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1885c: mov             x1, x0
    // 0xb18860: ldur            x0, [fp, #-0x30]
    // 0xb18864: StoreField: r0->field_f = r1
    //     0xb18864: stur            w1, [x0, #0xf]
    // 0xb18868: r1 = true
    //     0xb18868: add             x1, NULL, #0x20  ; true
    // 0xb1886c: StoreField: r0->field_43 = r1
    //     0xb1886c: stur            w1, [x0, #0x43]
    // 0xb18870: r2 = Instance_BoxShape
    //     0xb18870: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb18874: ldr             x2, [x2, #0x80]
    // 0xb18878: StoreField: r0->field_47 = r2
    //     0xb18878: stur            w2, [x0, #0x47]
    // 0xb1887c: StoreField: r0->field_6f = r1
    //     0xb1887c: stur            w1, [x0, #0x6f]
    // 0xb18880: r2 = false
    //     0xb18880: add             x2, NULL, #0x30  ; false
    // 0xb18884: StoreField: r0->field_73 = r2
    //     0xb18884: stur            w2, [x0, #0x73]
    // 0xb18888: StoreField: r0->field_83 = r1
    //     0xb18888: stur            w1, [x0, #0x83]
    // 0xb1888c: StoreField: r0->field_7b = r2
    //     0xb1888c: stur            w2, [x0, #0x7b]
    // 0xb18890: r1 = Null
    //     0xb18890: mov             x1, NULL
    // 0xb18894: r2 = 10
    //     0xb18894: movz            x2, #0xa
    // 0xb18898: r0 = AllocateArray()
    //     0xb18898: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1889c: mov             x2, x0
    // 0xb188a0: ldur            x0, [fp, #-0x38]
    // 0xb188a4: stur            x2, [fp, #-0x10]
    // 0xb188a8: StoreField: r2->field_f = r0
    //     0xb188a8: stur            w0, [x2, #0xf]
    // 0xb188ac: r16 = Instance_Spacer
    //     0xb188ac: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb188b0: ldr             x16, [x16, #0xf0]
    // 0xb188b4: StoreField: r2->field_13 = r16
    //     0xb188b4: stur            w16, [x2, #0x13]
    // 0xb188b8: ldur            x0, [fp, #-8]
    // 0xb188bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb188bc: stur            w0, [x2, #0x17]
    // 0xb188c0: r16 = Instance_Spacer
    //     0xb188c0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb188c4: ldr             x16, [x16, #0xf0]
    // 0xb188c8: StoreField: r2->field_1b = r16
    //     0xb188c8: stur            w16, [x2, #0x1b]
    // 0xb188cc: ldur            x0, [fp, #-0x30]
    // 0xb188d0: StoreField: r2->field_1f = r0
    //     0xb188d0: stur            w0, [x2, #0x1f]
    // 0xb188d4: r1 = <Widget>
    //     0xb188d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb188d8: r0 = AllocateGrowableArray()
    //     0xb188d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb188dc: mov             x1, x0
    // 0xb188e0: ldur            x0, [fp, #-0x10]
    // 0xb188e4: stur            x1, [fp, #-8]
    // 0xb188e8: StoreField: r1->field_f = r0
    //     0xb188e8: stur            w0, [x1, #0xf]
    // 0xb188ec: r0 = 10
    //     0xb188ec: movz            x0, #0xa
    // 0xb188f0: StoreField: r1->field_b = r0
    //     0xb188f0: stur            w0, [x1, #0xb]
    // 0xb188f4: r0 = Row()
    //     0xb188f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb188f8: mov             x1, x0
    // 0xb188fc: r0 = Instance_Axis
    //     0xb188fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb18900: stur            x1, [fp, #-0x10]
    // 0xb18904: StoreField: r1->field_f = r0
    //     0xb18904: stur            w0, [x1, #0xf]
    // 0xb18908: r0 = Instance_MainAxisAlignment
    //     0xb18908: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb1890c: ldr             x0, [x0, #0xab0]
    // 0xb18910: StoreField: r1->field_13 = r0
    //     0xb18910: stur            w0, [x1, #0x13]
    // 0xb18914: r0 = Instance_MainAxisSize
    //     0xb18914: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb18918: ldr             x0, [x0, #0xa10]
    // 0xb1891c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1891c: stur            w0, [x1, #0x17]
    // 0xb18920: r0 = Instance_CrossAxisAlignment
    //     0xb18920: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb18924: ldr             x0, [x0, #0xa18]
    // 0xb18928: StoreField: r1->field_1b = r0
    //     0xb18928: stur            w0, [x1, #0x1b]
    // 0xb1892c: r0 = Instance_VerticalDirection
    //     0xb1892c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb18930: ldr             x0, [x0, #0xa20]
    // 0xb18934: StoreField: r1->field_23 = r0
    //     0xb18934: stur            w0, [x1, #0x23]
    // 0xb18938: r2 = Instance_Clip
    //     0xb18938: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb1893c: ldr             x2, [x2, #0x38]
    // 0xb18940: StoreField: r1->field_2b = r2
    //     0xb18940: stur            w2, [x1, #0x2b]
    // 0xb18944: StoreField: r1->field_2f = rZR
    //     0xb18944: stur            xzr, [x1, #0x2f]
    // 0xb18948: ldur            x3, [fp, #-8]
    // 0xb1894c: StoreField: r1->field_b = r3
    //     0xb1894c: stur            w3, [x1, #0xb]
    // 0xb18950: r0 = Container()
    //     0xb18950: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb18954: stur            x0, [fp, #-8]
    // 0xb18958: r16 = 48.000000
    //     0xb18958: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb1895c: ldr             x16, [x16, #0xad8]
    // 0xb18960: r30 = inf
    //     0xb18960: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb18964: ldr             lr, [lr, #0x9f8]
    // 0xb18968: stp             lr, x16, [SP, #0x18]
    // 0xb1896c: ldur            x16, [fp, #-0x20]
    // 0xb18970: r30 = Instance_EdgeInsets
    //     0xb18970: add             lr, PP, #0x57, lsl #12  ; [pp+0x57910] Obj!EdgeInsets@d59151
    //     0xb18974: ldr             lr, [lr, #0x910]
    // 0xb18978: stp             lr, x16, [SP, #8]
    // 0xb1897c: ldur            x16, [fp, #-0x10]
    // 0xb18980: str             x16, [SP]
    // 0xb18984: mov             x1, x0
    // 0xb18988: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x3, height, 0x1, padding, 0x4, width, 0x2, null]
    //     0xb18988: add             x4, PP, #0x57, lsl #12  ; [pp+0x57918] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x3, "height", 0x1, "padding", 0x4, "width", 0x2, Null]
    //     0xb1898c: ldr             x4, [x4, #0x918]
    // 0xb18990: r0 = Container()
    //     0xb18990: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb18994: r1 = Null
    //     0xb18994: mov             x1, NULL
    // 0xb18998: r2 = 6
    //     0xb18998: movz            x2, #0x6
    // 0xb1899c: r0 = AllocateArray()
    //     0xb1899c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb189a0: mov             x2, x0
    // 0xb189a4: ldur            x0, [fp, #-0x28]
    // 0xb189a8: stur            x2, [fp, #-0x10]
    // 0xb189ac: StoreField: r2->field_f = r0
    //     0xb189ac: stur            w0, [x2, #0xf]
    // 0xb189b0: r16 = Instance_SizedBox
    //     0xb189b0: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb189b4: ldr             x16, [x16, #0x328]
    // 0xb189b8: StoreField: r2->field_13 = r16
    //     0xb189b8: stur            w16, [x2, #0x13]
    // 0xb189bc: ldur            x0, [fp, #-8]
    // 0xb189c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb189c0: stur            w0, [x2, #0x17]
    // 0xb189c4: r1 = <Widget>
    //     0xb189c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb189c8: r0 = AllocateGrowableArray()
    //     0xb189c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb189cc: mov             x1, x0
    // 0xb189d0: ldur            x0, [fp, #-0x10]
    // 0xb189d4: stur            x1, [fp, #-8]
    // 0xb189d8: StoreField: r1->field_f = r0
    //     0xb189d8: stur            w0, [x1, #0xf]
    // 0xb189dc: r0 = 6
    //     0xb189dc: movz            x0, #0x6
    // 0xb189e0: StoreField: r1->field_b = r0
    //     0xb189e0: stur            w0, [x1, #0xb]
    // 0xb189e4: r0 = Column()
    //     0xb189e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb189e8: mov             x1, x0
    // 0xb189ec: r0 = Instance_Axis
    //     0xb189ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb189f0: stur            x1, [fp, #-0x10]
    // 0xb189f4: StoreField: r1->field_f = r0
    //     0xb189f4: stur            w0, [x1, #0xf]
    // 0xb189f8: r0 = Instance_MainAxisAlignment
    //     0xb189f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb189fc: ldr             x0, [x0, #0xa08]
    // 0xb18a00: StoreField: r1->field_13 = r0
    //     0xb18a00: stur            w0, [x1, #0x13]
    // 0xb18a04: r0 = Instance_MainAxisSize
    //     0xb18a04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb18a08: ldr             x0, [x0, #0xdd0]
    // 0xb18a0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb18a0c: stur            w0, [x1, #0x17]
    // 0xb18a10: r0 = Instance_CrossAxisAlignment
    //     0xb18a10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb18a14: ldr             x0, [x0, #0x890]
    // 0xb18a18: StoreField: r1->field_1b = r0
    //     0xb18a18: stur            w0, [x1, #0x1b]
    // 0xb18a1c: r0 = Instance_VerticalDirection
    //     0xb18a1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb18a20: ldr             x0, [x0, #0xa20]
    // 0xb18a24: StoreField: r1->field_23 = r0
    //     0xb18a24: stur            w0, [x1, #0x23]
    // 0xb18a28: r0 = Instance_Clip
    //     0xb18a28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb18a2c: ldr             x0, [x0, #0x38]
    // 0xb18a30: StoreField: r1->field_2b = r0
    //     0xb18a30: stur            w0, [x1, #0x2b]
    // 0xb18a34: StoreField: r1->field_2f = rZR
    //     0xb18a34: stur            xzr, [x1, #0x2f]
    // 0xb18a38: ldur            x0, [fp, #-8]
    // 0xb18a3c: StoreField: r1->field_b = r0
    //     0xb18a3c: stur            w0, [x1, #0xb]
    // 0xb18a40: r0 = Container()
    //     0xb18a40: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb18a44: stur            x0, [fp, #-8]
    // 0xb18a48: r16 = Instance_Color
    //     0xb18a48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb18a4c: ldr             x16, [x16, #0x90]
    // 0xb18a50: r30 = inf
    //     0xb18a50: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb18a54: ldr             lr, [lr, #0x9f8]
    // 0xb18a58: stp             lr, x16, [SP, #0x10]
    // 0xb18a5c: r16 = Instance_EdgeInsets
    //     0xb18a5c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb18a60: ldr             x16, [x16, #0x110]
    // 0xb18a64: ldur            lr, [fp, #-0x10]
    // 0xb18a68: stp             lr, x16, [SP]
    // 0xb18a6c: mov             x1, x0
    // 0xb18a70: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb18a70: add             x4, PP, #0x57, lsl #12  ; [pp+0x578a8] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb18a74: ldr             x4, [x4, #0x8a8]
    // 0xb18a78: r0 = Container()
    //     0xb18a78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb18a7c: ldur            x0, [fp, #-8]
    // 0xb18a80: LeaveFrame
    //     0xb18a80: mov             SP, fp
    //     0xb18a84: ldp             fp, lr, [SP], #0x10
    // 0xb18a88: ret
    //     0xb18a88: ret             
    // 0xb18a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb18a8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb18a90: b               #0xb184cc
    // 0xb18a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb18a94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb18abc, size: 0xe0
    // 0xb18abc: EnterFrame
    //     0xb18abc: stp             fp, lr, [SP, #-0x10]!
    //     0xb18ac0: mov             fp, SP
    // 0xb18ac4: AllocStack(0x8)
    //     0xb18ac4: sub             SP, SP, #8
    // 0xb18ac8: SetupParameters()
    //     0xb18ac8: ldr             x0, [fp, #0x10]
    //     0xb18acc: ldur            w1, [x0, #0x17]
    //     0xb18ad0: add             x1, x1, HEAP, lsl #32
    // 0xb18ad4: CheckStackOverflow
    //     0xb18ad4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb18ad8: cmp             SP, x16
    //     0xb18adc: b.ls            #0xb18b90
    // 0xb18ae0: LoadField: r0 = r1->field_f
    //     0xb18ae0: ldur            w0, [x1, #0xf]
    // 0xb18ae4: DecompressPointer r0
    //     0xb18ae4: add             x0, x0, HEAP, lsl #32
    // 0xb18ae8: LoadField: r1 = r0->field_b
    //     0xb18ae8: ldur            w1, [x0, #0xb]
    // 0xb18aec: DecompressPointer r1
    //     0xb18aec: add             x1, x1, HEAP, lsl #32
    // 0xb18af0: cmp             w1, NULL
    // 0xb18af4: b.eq            #0xb18b98
    // 0xb18af8: LoadField: r0 = r1->field_13
    //     0xb18af8: ldur            x0, [x1, #0x13]
    // 0xb18afc: cmp             x0, #5
    // 0xb18b00: b.ge            #0xb18b80
    // 0xb18b04: LoadField: r2 = r1->field_b
    //     0xb18b04: ldur            w2, [x1, #0xb]
    // 0xb18b08: DecompressPointer r2
    //     0xb18b08: add             x2, x2, HEAP, lsl #32
    // 0xb18b0c: LoadField: r3 = r2->field_7b
    //     0xb18b0c: ldur            w3, [x2, #0x7b]
    // 0xb18b10: DecompressPointer r3
    //     0xb18b10: add             x3, x3, HEAP, lsl #32
    // 0xb18b14: cmp             w3, NULL
    // 0xb18b18: b.ne            #0xb18b24
    // 0xb18b1c: r2 = 0
    //     0xb18b1c: movz            x2, #0
    // 0xb18b20: b               #0xb18b30
    // 0xb18b24: r2 = LoadInt32Instr(r3)
    //     0xb18b24: sbfx            x2, x3, #1, #0x1f
    //     0xb18b28: tbz             w3, #0, #0xb18b30
    //     0xb18b2c: ldur            x2, [x3, #7]
    // 0xb18b30: cmp             x2, #1
    // 0xb18b34: b.lt            #0xb18b80
    // 0xb18b38: cmp             w3, NULL
    // 0xb18b3c: b.ne            #0xb18b48
    // 0xb18b40: r2 = 0
    //     0xb18b40: movz            x2, #0
    // 0xb18b44: b               #0xb18b54
    // 0xb18b48: r2 = LoadInt32Instr(r3)
    //     0xb18b48: sbfx            x2, x3, #1, #0x1f
    //     0xb18b4c: tbz             w3, #0, #0xb18b54
    //     0xb18b50: ldur            x2, [x3, #7]
    // 0xb18b54: cmp             x2, x0
    // 0xb18b58: b.le            #0xb18b80
    // 0xb18b5c: LoadField: r0 = r1->field_1f
    //     0xb18b5c: ldur            w0, [x1, #0x1f]
    // 0xb18b60: DecompressPointer r0
    //     0xb18b60: add             x0, x0, HEAP, lsl #32
    // 0xb18b64: str             x0, [SP]
    // 0xb18b68: r4 = 0
    //     0xb18b68: movz            x4, #0
    // 0xb18b6c: ldr             x0, [SP]
    // 0xb18b70: r16 = UnlinkedCall_0x613b5c
    //     0xb18b70: add             x16, PP, #0x57, lsl #12  ; [pp+0x57920] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb18b74: add             x16, x16, #0x920
    // 0xb18b78: ldp             x5, lr, [x16]
    // 0xb18b7c: blr             lr
    // 0xb18b80: r0 = Null
    //     0xb18b80: mov             x0, NULL
    // 0xb18b84: LeaveFrame
    //     0xb18b84: mov             SP, fp
    //     0xb18b88: ldp             fp, lr, [SP], #0x10
    // 0xb18b8c: ret
    //     0xb18b8c: ret             
    // 0xb18b90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb18b90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb18b94: b               #0xb18ae0
    // 0xb18b98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb18b98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb18b9c, size: 0x88
    // 0xb18b9c: EnterFrame
    //     0xb18b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb18ba0: mov             fp, SP
    // 0xb18ba4: AllocStack(0x8)
    //     0xb18ba4: sub             SP, SP, #8
    // 0xb18ba8: SetupParameters()
    //     0xb18ba8: ldr             x0, [fp, #0x10]
    //     0xb18bac: ldur            w1, [x0, #0x17]
    //     0xb18bb0: add             x1, x1, HEAP, lsl #32
    // 0xb18bb4: CheckStackOverflow
    //     0xb18bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb18bb8: cmp             SP, x16
    //     0xb18bbc: b.ls            #0xb18c18
    // 0xb18bc0: LoadField: r0 = r1->field_f
    //     0xb18bc0: ldur            w0, [x1, #0xf]
    // 0xb18bc4: DecompressPointer r0
    //     0xb18bc4: add             x0, x0, HEAP, lsl #32
    // 0xb18bc8: LoadField: r1 = r0->field_b
    //     0xb18bc8: ldur            w1, [x0, #0xb]
    // 0xb18bcc: DecompressPointer r1
    //     0xb18bcc: add             x1, x1, HEAP, lsl #32
    // 0xb18bd0: cmp             w1, NULL
    // 0xb18bd4: b.eq            #0xb18c20
    // 0xb18bd8: LoadField: r0 = r1->field_13
    //     0xb18bd8: ldur            x0, [x1, #0x13]
    // 0xb18bdc: cmp             x0, #1
    // 0xb18be0: b.le            #0xb18c08
    // 0xb18be4: LoadField: r0 = r1->field_1b
    //     0xb18be4: ldur            w0, [x1, #0x1b]
    // 0xb18be8: DecompressPointer r0
    //     0xb18be8: add             x0, x0, HEAP, lsl #32
    // 0xb18bec: str             x0, [SP]
    // 0xb18bf0: r4 = 0
    //     0xb18bf0: movz            x4, #0
    // 0xb18bf4: ldr             x0, [SP]
    // 0xb18bf8: r16 = UnlinkedCall_0x613b5c
    //     0xb18bf8: add             x16, PP, #0x57, lsl #12  ; [pp+0x57930] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb18bfc: add             x16, x16, #0x930
    // 0xb18c00: ldp             x5, lr, [x16]
    // 0xb18c04: blr             lr
    // 0xb18c08: r0 = Null
    //     0xb18c08: mov             x0, NULL
    // 0xb18c0c: LeaveFrame
    //     0xb18c0c: mov             SP, fp
    //     0xb18c10: ldp             fp, lr, [SP], #0x10
    // 0xb18c14: ret
    //     0xb18c14: ret             
    // 0xb18c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb18c18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb18c1c: b               #0xb18bc0
    // 0xb18c20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb18c20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4132, size: 0x24, field offset: 0xc
class SelectQuantityPicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e248, size: 0x50
    // 0xc7e248: EnterFrame
    //     0xc7e248: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e24c: mov             fp, SP
    // 0xc7e250: CheckStackOverflow
    //     0xc7e250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e254: cmp             SP, x16
    //     0xc7e258: b.ls            #0xc7e290
    // 0xc7e25c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7e25c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7e260: ldr             x0, [x0]
    //     0xc7e264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7e268: cmp             w0, w16
    //     0xc7e26c: b.ne            #0xc7e278
    //     0xc7e270: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7e274: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7e278: r1 = <SelectQuantityPicker>
    //     0xc7e278: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ae8] TypeArguments: <SelectQuantityPicker>
    //     0xc7e27c: ldr             x1, [x1, #0xae8]
    // 0xc7e280: r0 = _SelectQuantityPickerState()
    //     0xc7e280: bl              #0xc7e298  ; Allocate_SelectQuantityPickerStateStub -> _SelectQuantityPickerState (size=0x14)
    // 0xc7e284: LeaveFrame
    //     0xc7e284: mov             SP, fp
    //     0xc7e288: ldp             fp, lr, [SP], #0x10
    // 0xc7e28c: ret
    //     0xc7e28c: ret             
    // 0xc7e290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e290: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e294: b               #0xc7e25c
  }
}
