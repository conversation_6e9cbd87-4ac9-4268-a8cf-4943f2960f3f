// lib: , url: package:easy_stepper/src/core/custom_scroll_behavior.dart

// class id: 1049627, size: 0x8
class :: {
}

// class id: 2382, size: 0x8, field offset: 0x8
class CustomScrollBehavior extends MaterialScrollBehavior {

  get _ dragDevices(/* No info */) {
    // ** addr: 0x16b9d68, size: 0x78
    // 0x16b9d68: EnterFrame
    //     0x16b9d68: stp             fp, lr, [SP, #-0x10]!
    //     0x16b9d6c: mov             fp, SP
    // 0x16b9d70: AllocStack(0x8)
    //     0x16b9d70: sub             SP, SP, #8
    // 0x16b9d74: CheckStackOverflow
    //     0x16b9d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x16b9d78: cmp             SP, x16
    //     0x16b9d7c: b.ls            #0x16b9dd8
    // 0x16b9d80: r1 = <PointerDeviceKind>
    //     0x16b9d80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e438] TypeArguments: <PointerDeviceKind>
    //     0x16b9d84: ldr             x1, [x1, #0x438]
    // 0x16b9d88: r0 = _Set()
    //     0x16b9d88: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x16b9d8c: mov             x3, x0
    // 0x16b9d90: r0 = _Uint32List
    //     0x16b9d90: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x16b9d94: stur            x3, [fp, #-8]
    // 0x16b9d98: StoreField: r3->field_1b = r0
    //     0x16b9d98: stur            w0, [x3, #0x1b]
    // 0x16b9d9c: StoreField: r3->field_b = rZR
    //     0x16b9d9c: stur            wzr, [x3, #0xb]
    // 0x16b9da0: r0 = const []
    //     0x16b9da0: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x16b9da4: StoreField: r3->field_f = r0
    //     0x16b9da4: stur            w0, [x3, #0xf]
    // 0x16b9da8: StoreField: r3->field_13 = rZR
    //     0x16b9da8: stur            wzr, [x3, #0x13]
    // 0x16b9dac: ArrayStore: r3[0] = rZR  ; List_4
    //     0x16b9dac: stur            wzr, [x3, #0x17]
    // 0x16b9db0: mov             x1, x3
    // 0x16b9db4: r2 = Instance_PointerDeviceKind
    //     0x16b9db4: ldr             x2, [PP, #0x6c48]  ; [pp+0x6c48] Obj!PointerDeviceKind@d76a61
    // 0x16b9db8: r0 = add()
    //     0x16b9db8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x16b9dbc: ldur            x1, [fp, #-8]
    // 0x16b9dc0: r2 = Instance_PointerDeviceKind
    //     0x16b9dc0: ldr             x2, [PP, #0x3ed0]  ; [pp+0x3ed0] Obj!PointerDeviceKind@d76a41
    // 0x16b9dc4: r0 = add()
    //     0x16b9dc4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x16b9dc8: ldur            x0, [fp, #-8]
    // 0x16b9dcc: LeaveFrame
    //     0x16b9dcc: mov             SP, fp
    //     0x16b9dd0: ldp             fp, lr, [SP], #0x10
    // 0x16b9dd4: ret
    //     0x16b9dd4: ret             
    // 0x16b9dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x16b9dd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x16b9ddc: b               #0x16b9d80
  }
}
