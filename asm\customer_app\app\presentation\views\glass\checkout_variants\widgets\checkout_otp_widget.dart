// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart

// class id: 1049367, size: 0x8
class :: {
}

// class id: 3364, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __CheckoutOtpWidgetState&State&CodeAutoFill extends State<dynamic>
     with CodeAutoFill {
}

// class id: 3365, size: 0x20, field offset: 0x14
class _CheckoutOtpWidgetState extends __CheckoutOtpWidgetState&State&CodeAutoFill {

  _ initState(/* No info */) {
    // ** addr: 0x9405fc, size: 0x138
    // 0x9405fc: EnterFrame
    //     0x9405fc: stp             fp, lr, [SP, #-0x10]!
    //     0x940600: mov             fp, SP
    // 0x940604: AllocStack(0x20)
    //     0x940604: sub             SP, SP, #0x20
    // 0x940608: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x940608: stur            x1, [fp, #-8]
    // 0x94060c: CheckStackOverflow
    //     0x94060c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940610: cmp             SP, x16
    //     0x940614: b.ls            #0x940728
    // 0x940618: r1 = 1
    //     0x940618: movz            x1, #0x1
    // 0x94061c: r0 = AllocateContext()
    //     0x94061c: bl              #0x16f6108  ; AllocateContextStub
    // 0x940620: mov             x1, x0
    // 0x940624: ldur            x0, [fp, #-8]
    // 0x940628: StoreField: r1->field_f = r0
    //     0x940628: stur            w0, [x1, #0xf]
    // 0x94062c: r2 = LoadStaticField(0x878)
    //     0x94062c: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x940630: ldr             x2, [x2, #0x10f0]
    // 0x940634: cmp             w2, NULL
    // 0x940638: b.eq            #0x940730
    // 0x94063c: LoadField: r3 = r2->field_53
    //     0x94063c: ldur            w3, [x2, #0x53]
    // 0x940640: DecompressPointer r3
    //     0x940640: add             x3, x3, HEAP, lsl #32
    // 0x940644: stur            x3, [fp, #-0x18]
    // 0x940648: LoadField: r4 = r3->field_7
    //     0x940648: ldur            w4, [x3, #7]
    // 0x94064c: DecompressPointer r4
    //     0x94064c: add             x4, x4, HEAP, lsl #32
    // 0x940650: mov             x2, x1
    // 0x940654: stur            x4, [fp, #-0x10]
    // 0x940658: r1 = Function '<anonymous closure>':.
    //     0x940658: add             x1, PP, #0x56, lsl #12  ; [pp+0x56690] AnonymousClosure: (0x905e34), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::initState (0x947e54)
    //     0x94065c: ldr             x1, [x1, #0x690]
    // 0x940660: r0 = AllocateClosure()
    //     0x940660: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940664: ldur            x2, [fp, #-0x10]
    // 0x940668: mov             x3, x0
    // 0x94066c: r1 = Null
    //     0x94066c: mov             x1, NULL
    // 0x940670: stur            x3, [fp, #-0x10]
    // 0x940674: cmp             w2, NULL
    // 0x940678: b.eq            #0x940698
    // 0x94067c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x94067c: ldur            w4, [x2, #0x17]
    // 0x940680: DecompressPointer r4
    //     0x940680: add             x4, x4, HEAP, lsl #32
    // 0x940684: r8 = X0
    //     0x940684: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x940688: LoadField: r9 = r4->field_7
    //     0x940688: ldur            x9, [x4, #7]
    // 0x94068c: r3 = Null
    //     0x94068c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56698] Null
    //     0x940690: ldr             x3, [x3, #0x698]
    // 0x940694: blr             x9
    // 0x940698: ldur            x0, [fp, #-0x18]
    // 0x94069c: LoadField: r1 = r0->field_b
    //     0x94069c: ldur            w1, [x0, #0xb]
    // 0x9406a0: LoadField: r2 = r0->field_f
    //     0x9406a0: ldur            w2, [x0, #0xf]
    // 0x9406a4: DecompressPointer r2
    //     0x9406a4: add             x2, x2, HEAP, lsl #32
    // 0x9406a8: LoadField: r3 = r2->field_b
    //     0x9406a8: ldur            w3, [x2, #0xb]
    // 0x9406ac: r2 = LoadInt32Instr(r1)
    //     0x9406ac: sbfx            x2, x1, #1, #0x1f
    // 0x9406b0: stur            x2, [fp, #-0x20]
    // 0x9406b4: r1 = LoadInt32Instr(r3)
    //     0x9406b4: sbfx            x1, x3, #1, #0x1f
    // 0x9406b8: cmp             x2, x1
    // 0x9406bc: b.ne            #0x9406c8
    // 0x9406c0: mov             x1, x0
    // 0x9406c4: r0 = _growToNextCapacity()
    //     0x9406c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9406c8: ldur            x0, [fp, #-0x18]
    // 0x9406cc: ldur            x2, [fp, #-0x20]
    // 0x9406d0: add             x1, x2, #1
    // 0x9406d4: lsl             x3, x1, #1
    // 0x9406d8: StoreField: r0->field_b = r3
    //     0x9406d8: stur            w3, [x0, #0xb]
    // 0x9406dc: LoadField: r1 = r0->field_f
    //     0x9406dc: ldur            w1, [x0, #0xf]
    // 0x9406e0: DecompressPointer r1
    //     0x9406e0: add             x1, x1, HEAP, lsl #32
    // 0x9406e4: ldur            x0, [fp, #-0x10]
    // 0x9406e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9406e8: add             x25, x1, x2, lsl #2
    //     0x9406ec: add             x25, x25, #0xf
    //     0x9406f0: str             w0, [x25]
    //     0x9406f4: tbz             w0, #0, #0x940710
    //     0x9406f8: ldurb           w16, [x1, #-1]
    //     0x9406fc: ldurb           w17, [x0, #-1]
    //     0x940700: and             x16, x17, x16, lsr #2
    //     0x940704: tst             x16, HEAP, lsr #32
    //     0x940708: b.eq            #0x940710
    //     0x94070c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x940710: ldur            x1, [fp, #-8]
    // 0x940714: r0 = registerOtpListenListener()
    //     0x940714: bl              #0x905b7c  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/otp_bottom_sheet.dart] _OtpBottomSheetState::registerOtpListenListener
    // 0x940718: r0 = Null
    //     0x940718: mov             x0, NULL
    // 0x94071c: LeaveFrame
    //     0x94071c: mov             SP, fp
    //     0x940720: ldp             fp, lr, [SP], #0x10
    // 0x940724: ret
    //     0x940724: ret             
    // 0x940728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x940728: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94072c: b               #0x940618
    // 0x940730: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940730: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb45fa4, size: 0x840
    // 0xb45fa4: EnterFrame
    //     0xb45fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xb45fa8: mov             fp, SP
    // 0xb45fac: AllocStack(0x88)
    //     0xb45fac: sub             SP, SP, #0x88
    // 0xb45fb0: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb45fb0: mov             x0, x1
    //     0xb45fb4: stur            x1, [fp, #-8]
    //     0xb45fb8: mov             x1, x2
    //     0xb45fbc: stur            x2, [fp, #-0x10]
    // 0xb45fc0: CheckStackOverflow
    //     0xb45fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45fc4: cmp             SP, x16
    //     0xb45fc8: b.ls            #0xb467bc
    // 0xb45fcc: r1 = 2
    //     0xb45fcc: movz            x1, #0x2
    // 0xb45fd0: r0 = AllocateContext()
    //     0xb45fd0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb45fd4: mov             x2, x0
    // 0xb45fd8: ldur            x0, [fp, #-8]
    // 0xb45fdc: stur            x2, [fp, #-0x18]
    // 0xb45fe0: StoreField: r2->field_f = r0
    //     0xb45fe0: stur            w0, [x2, #0xf]
    // 0xb45fe4: ldur            x1, [fp, #-0x10]
    // 0xb45fe8: StoreField: r2->field_13 = r1
    //     0xb45fe8: stur            w1, [x2, #0x13]
    // 0xb45fec: r0 = of()
    //     0xb45fec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb45ff0: LoadField: r1 = r0->field_5b
    //     0xb45ff0: ldur            w1, [x0, #0x5b]
    // 0xb45ff4: DecompressPointer r1
    //     0xb45ff4: add             x1, x1, HEAP, lsl #32
    // 0xb45ff8: r0 = LoadClassIdInstr(r1)
    //     0xb45ff8: ldur            x0, [x1, #-1]
    //     0xb45ffc: ubfx            x0, x0, #0xc, #0x14
    // 0xb46000: d0 = 0.100000
    //     0xb46000: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb46004: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb46004: sub             lr, x0, #0xffa
    //     0xb46008: ldr             lr, [x21, lr, lsl #3]
    //     0xb4600c: blr             lr
    // 0xb46010: stur            x0, [fp, #-0x10]
    // 0xb46014: r0 = Divider()
    //     0xb46014: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb46018: mov             x2, x0
    // 0xb4601c: ldur            x0, [fp, #-0x10]
    // 0xb46020: stur            x2, [fp, #-0x20]
    // 0xb46024: StoreField: r2->field_1f = r0
    //     0xb46024: stur            w0, [x2, #0x1f]
    // 0xb46028: ldur            x0, [fp, #-0x18]
    // 0xb4602c: LoadField: r1 = r0->field_13
    //     0xb4602c: ldur            w1, [x0, #0x13]
    // 0xb46030: DecompressPointer r1
    //     0xb46030: add             x1, x1, HEAP, lsl #32
    // 0xb46034: r0 = of()
    //     0xb46034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46038: LoadField: r1 = r0->field_87
    //     0xb46038: ldur            w1, [x0, #0x87]
    // 0xb4603c: DecompressPointer r1
    //     0xb4603c: add             x1, x1, HEAP, lsl #32
    // 0xb46040: LoadField: r0 = r1->field_7
    //     0xb46040: ldur            w0, [x1, #7]
    // 0xb46044: DecompressPointer r0
    //     0xb46044: add             x0, x0, HEAP, lsl #32
    // 0xb46048: r16 = Instance_Color
    //     0xb46048: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4604c: r30 = 14.000000
    //     0xb4604c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb46050: ldr             lr, [lr, #0x1d8]
    // 0xb46054: stp             lr, x16, [SP]
    // 0xb46058: mov             x1, x0
    // 0xb4605c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4605c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46060: ldr             x4, [x4, #0x9b8]
    // 0xb46064: r0 = copyWith()
    //     0xb46064: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46068: stur            x0, [fp, #-0x10]
    // 0xb4606c: r0 = Text()
    //     0xb4606c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46070: mov             x1, x0
    // 0xb46074: r0 = "Enter OTP to confirm"
    //     0xb46074: add             x0, PP, #0x53, lsl #12  ; [pp+0x53ee8] "Enter OTP to confirm"
    //     0xb46078: ldr             x0, [x0, #0xee8]
    // 0xb4607c: stur            x1, [fp, #-0x28]
    // 0xb46080: StoreField: r1->field_b = r0
    //     0xb46080: stur            w0, [x1, #0xb]
    // 0xb46084: ldur            x0, [fp, #-0x10]
    // 0xb46088: StoreField: r1->field_13 = r0
    //     0xb46088: stur            w0, [x1, #0x13]
    // 0xb4608c: r0 = Padding()
    //     0xb4608c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb46090: mov             x3, x0
    // 0xb46094: r0 = Instance_EdgeInsets
    //     0xb46094: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb46098: ldr             x0, [x0, #0x668]
    // 0xb4609c: stur            x3, [fp, #-0x10]
    // 0xb460a0: StoreField: r3->field_f = r0
    //     0xb460a0: stur            w0, [x3, #0xf]
    // 0xb460a4: ldur            x1, [fp, #-0x28]
    // 0xb460a8: StoreField: r3->field_b = r1
    //     0xb460a8: stur            w1, [x3, #0xb]
    // 0xb460ac: r1 = Null
    //     0xb460ac: mov             x1, NULL
    // 0xb460b0: r2 = 4
    //     0xb460b0: movz            x2, #0x4
    // 0xb460b4: r0 = AllocateArray()
    //     0xb460b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb460b8: r16 = "Otp sent to "
    //     0xb460b8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ef0] "Otp sent to "
    //     0xb460bc: ldr             x16, [x16, #0xef0]
    // 0xb460c0: StoreField: r0->field_f = r16
    //     0xb460c0: stur            w16, [x0, #0xf]
    // 0xb460c4: ldur            x1, [fp, #-8]
    // 0xb460c8: LoadField: r2 = r1->field_b
    //     0xb460c8: ldur            w2, [x1, #0xb]
    // 0xb460cc: DecompressPointer r2
    //     0xb460cc: add             x2, x2, HEAP, lsl #32
    // 0xb460d0: cmp             w2, NULL
    // 0xb460d4: b.eq            #0xb467c4
    // 0xb460d8: LoadField: r3 = r2->field_1b
    //     0xb460d8: ldur            w3, [x2, #0x1b]
    // 0xb460dc: DecompressPointer r3
    //     0xb460dc: add             x3, x3, HEAP, lsl #32
    // 0xb460e0: StoreField: r0->field_13 = r3
    //     0xb460e0: stur            w3, [x0, #0x13]
    // 0xb460e4: str             x0, [SP]
    // 0xb460e8: r0 = _interpolate()
    //     0xb460e8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb460ec: ldur            x2, [fp, #-0x18]
    // 0xb460f0: stur            x0, [fp, #-0x28]
    // 0xb460f4: LoadField: r1 = r2->field_13
    //     0xb460f4: ldur            w1, [x2, #0x13]
    // 0xb460f8: DecompressPointer r1
    //     0xb460f8: add             x1, x1, HEAP, lsl #32
    // 0xb460fc: r0 = of()
    //     0xb460fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46100: LoadField: r1 = r0->field_87
    //     0xb46100: ldur            w1, [x0, #0x87]
    // 0xb46104: DecompressPointer r1
    //     0xb46104: add             x1, x1, HEAP, lsl #32
    // 0xb46108: LoadField: r0 = r1->field_7
    //     0xb46108: ldur            w0, [x1, #7]
    // 0xb4610c: DecompressPointer r0
    //     0xb4610c: add             x0, x0, HEAP, lsl #32
    // 0xb46110: stur            x0, [fp, #-0x30]
    // 0xb46114: r1 = Instance_Color
    //     0xb46114: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46118: r2 = 70
    //     0xb46118: movz            x2, #0x46
    // 0xb4611c: r0 = withAlpha()
    //     0xb4611c: bl              #0x1685b4c  ; [dart:ui] Color::withAlpha
    // 0xb46120: r16 = 12.000000
    //     0xb46120: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46124: ldr             x16, [x16, #0x9e8]
    // 0xb46128: stp             x16, x0, [SP]
    // 0xb4612c: ldur            x1, [fp, #-0x30]
    // 0xb46130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46134: ldr             x4, [x4, #0x9b8]
    // 0xb46138: r0 = copyWith()
    //     0xb46138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4613c: stur            x0, [fp, #-0x30]
    // 0xb46140: r0 = Text()
    //     0xb46140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46144: mov             x2, x0
    // 0xb46148: ldur            x0, [fp, #-0x28]
    // 0xb4614c: stur            x2, [fp, #-0x38]
    // 0xb46150: StoreField: r2->field_b = r0
    //     0xb46150: stur            w0, [x2, #0xb]
    // 0xb46154: ldur            x0, [fp, #-0x30]
    // 0xb46158: StoreField: r2->field_13 = r0
    //     0xb46158: stur            w0, [x2, #0x13]
    // 0xb4615c: ldur            x0, [fp, #-0x18]
    // 0xb46160: LoadField: r1 = r0->field_13
    //     0xb46160: ldur            w1, [x0, #0x13]
    // 0xb46164: DecompressPointer r1
    //     0xb46164: add             x1, x1, HEAP, lsl #32
    // 0xb46168: r0 = of()
    //     0xb46168: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4616c: LoadField: r1 = r0->field_87
    //     0xb4616c: ldur            w1, [x0, #0x87]
    // 0xb46170: DecompressPointer r1
    //     0xb46170: add             x1, x1, HEAP, lsl #32
    // 0xb46174: LoadField: r0 = r1->field_7
    //     0xb46174: ldur            w0, [x1, #7]
    // 0xb46178: DecompressPointer r0
    //     0xb46178: add             x0, x0, HEAP, lsl #32
    // 0xb4617c: stur            x0, [fp, #-0x28]
    // 0xb46180: r1 = Instance_Color
    //     0xb46180: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46184: r2 = 80
    //     0xb46184: movz            x2, #0x50
    // 0xb46188: r0 = withAlpha()
    //     0xb46188: bl              #0x1685b4c  ; [dart:ui] Color::withAlpha
    // 0xb4618c: r16 = 14.000000
    //     0xb4618c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb46190: ldr             x16, [x16, #0x1d8]
    // 0xb46194: stp             x16, x0, [SP, #8]
    // 0xb46198: r16 = Instance_TextDecoration
    //     0xb46198: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb4619c: ldr             x16, [x16, #0x10]
    // 0xb461a0: str             x16, [SP]
    // 0xb461a4: ldur            x1, [fp, #-0x28]
    // 0xb461a8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xb461a8: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xb461ac: ldr             x4, [x4, #0x7c8]
    // 0xb461b0: r0 = copyWith()
    //     0xb461b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb461b4: stur            x0, [fp, #-0x28]
    // 0xb461b8: r0 = Text()
    //     0xb461b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb461bc: mov             x1, x0
    // 0xb461c0: r0 = "Edit"
    //     0xb461c0: add             x0, PP, #0x56, lsl #12  ; [pp+0x56620] "Edit"
    //     0xb461c4: ldr             x0, [x0, #0x620]
    // 0xb461c8: stur            x1, [fp, #-0x30]
    // 0xb461cc: StoreField: r1->field_b = r0
    //     0xb461cc: stur            w0, [x1, #0xb]
    // 0xb461d0: ldur            x0, [fp, #-0x28]
    // 0xb461d4: StoreField: r1->field_13 = r0
    //     0xb461d4: stur            w0, [x1, #0x13]
    // 0xb461d8: r0 = Padding()
    //     0xb461d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb461dc: mov             x1, x0
    // 0xb461e0: r0 = Instance_EdgeInsets
    //     0xb461e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xb461e4: ldr             x0, [x0, #0xe60]
    // 0xb461e8: stur            x1, [fp, #-0x28]
    // 0xb461ec: StoreField: r1->field_f = r0
    //     0xb461ec: stur            w0, [x1, #0xf]
    // 0xb461f0: ldur            x0, [fp, #-0x30]
    // 0xb461f4: StoreField: r1->field_b = r0
    //     0xb461f4: stur            w0, [x1, #0xb]
    // 0xb461f8: r0 = InkWell()
    //     0xb461f8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb461fc: mov             x3, x0
    // 0xb46200: ldur            x0, [fp, #-0x28]
    // 0xb46204: stur            x3, [fp, #-0x30]
    // 0xb46208: StoreField: r3->field_b = r0
    //     0xb46208: stur            w0, [x3, #0xb]
    // 0xb4620c: ldur            x2, [fp, #-0x18]
    // 0xb46210: r1 = Function '<anonymous closure>':.
    //     0xb46210: add             x1, PP, #0x56, lsl #12  ; [pp+0x56628] AnonymousClosure: (0xa0a9a8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xbb5e4c)
    //     0xb46214: ldr             x1, [x1, #0x628]
    // 0xb46218: r0 = AllocateClosure()
    //     0xb46218: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4621c: mov             x1, x0
    // 0xb46220: ldur            x0, [fp, #-0x30]
    // 0xb46224: StoreField: r0->field_f = r1
    //     0xb46224: stur            w1, [x0, #0xf]
    // 0xb46228: r3 = true
    //     0xb46228: add             x3, NULL, #0x20  ; true
    // 0xb4622c: StoreField: r0->field_43 = r3
    //     0xb4622c: stur            w3, [x0, #0x43]
    // 0xb46230: r1 = Instance_BoxShape
    //     0xb46230: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb46234: ldr             x1, [x1, #0x80]
    // 0xb46238: StoreField: r0->field_47 = r1
    //     0xb46238: stur            w1, [x0, #0x47]
    // 0xb4623c: StoreField: r0->field_6f = r3
    //     0xb4623c: stur            w3, [x0, #0x6f]
    // 0xb46240: r4 = false
    //     0xb46240: add             x4, NULL, #0x30  ; false
    // 0xb46244: StoreField: r0->field_73 = r4
    //     0xb46244: stur            w4, [x0, #0x73]
    // 0xb46248: StoreField: r0->field_83 = r3
    //     0xb46248: stur            w3, [x0, #0x83]
    // 0xb4624c: StoreField: r0->field_7b = r4
    //     0xb4624c: stur            w4, [x0, #0x7b]
    // 0xb46250: r1 = Null
    //     0xb46250: mov             x1, NULL
    // 0xb46254: r2 = 4
    //     0xb46254: movz            x2, #0x4
    // 0xb46258: r0 = AllocateArray()
    //     0xb46258: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4625c: mov             x2, x0
    // 0xb46260: ldur            x0, [fp, #-0x38]
    // 0xb46264: stur            x2, [fp, #-0x28]
    // 0xb46268: StoreField: r2->field_f = r0
    //     0xb46268: stur            w0, [x2, #0xf]
    // 0xb4626c: ldur            x0, [fp, #-0x30]
    // 0xb46270: StoreField: r2->field_13 = r0
    //     0xb46270: stur            w0, [x2, #0x13]
    // 0xb46274: r1 = <Widget>
    //     0xb46274: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb46278: r0 = AllocateGrowableArray()
    //     0xb46278: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4627c: mov             x1, x0
    // 0xb46280: ldur            x0, [fp, #-0x28]
    // 0xb46284: stur            x1, [fp, #-0x30]
    // 0xb46288: StoreField: r1->field_f = r0
    //     0xb46288: stur            w0, [x1, #0xf]
    // 0xb4628c: r2 = 4
    //     0xb4628c: movz            x2, #0x4
    // 0xb46290: StoreField: r1->field_b = r2
    //     0xb46290: stur            w2, [x1, #0xb]
    // 0xb46294: r0 = Row()
    //     0xb46294: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb46298: mov             x1, x0
    // 0xb4629c: r0 = Instance_Axis
    //     0xb4629c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb462a0: stur            x1, [fp, #-0x28]
    // 0xb462a4: StoreField: r1->field_f = r0
    //     0xb462a4: stur            w0, [x1, #0xf]
    // 0xb462a8: r0 = Instance_MainAxisAlignment
    //     0xb462a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb462ac: ldr             x0, [x0, #0xa08]
    // 0xb462b0: StoreField: r1->field_13 = r0
    //     0xb462b0: stur            w0, [x1, #0x13]
    // 0xb462b4: r2 = Instance_MainAxisSize
    //     0xb462b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb462b8: ldr             x2, [x2, #0xa10]
    // 0xb462bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb462bc: stur            w2, [x1, #0x17]
    // 0xb462c0: r3 = Instance_CrossAxisAlignment
    //     0xb462c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb462c4: ldr             x3, [x3, #0xa18]
    // 0xb462c8: StoreField: r1->field_1b = r3
    //     0xb462c8: stur            w3, [x1, #0x1b]
    // 0xb462cc: r3 = Instance_VerticalDirection
    //     0xb462cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb462d0: ldr             x3, [x3, #0xa20]
    // 0xb462d4: StoreField: r1->field_23 = r3
    //     0xb462d4: stur            w3, [x1, #0x23]
    // 0xb462d8: r4 = Instance_Clip
    //     0xb462d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb462dc: ldr             x4, [x4, #0x38]
    // 0xb462e0: StoreField: r1->field_2b = r4
    //     0xb462e0: stur            w4, [x1, #0x2b]
    // 0xb462e4: StoreField: r1->field_2f = rZR
    //     0xb462e4: stur            xzr, [x1, #0x2f]
    // 0xb462e8: ldur            x5, [fp, #-0x30]
    // 0xb462ec: StoreField: r1->field_b = r5
    //     0xb462ec: stur            w5, [x1, #0xb]
    // 0xb462f0: r0 = Padding()
    //     0xb462f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb462f4: mov             x1, x0
    // 0xb462f8: r0 = Instance_EdgeInsets
    //     0xb462f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xb462fc: ldr             x0, [x0, #0xe18]
    // 0xb46300: stur            x1, [fp, #-0x30]
    // 0xb46304: StoreField: r1->field_f = r0
    //     0xb46304: stur            w0, [x1, #0xf]
    // 0xb46308: ldur            x0, [fp, #-0x28]
    // 0xb4630c: StoreField: r1->field_b = r0
    //     0xb4630c: stur            w0, [x1, #0xb]
    // 0xb46310: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb46310: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb46314: ldr             x0, [x0, #0x1c80]
    //     0xb46318: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4631c: cmp             w0, w16
    //     0xb46320: b.ne            #0xb4632c
    //     0xb46324: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb46328: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4632c: r0 = GetNavigation.width()
    //     0xb4632c: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb46330: stur            d0, [fp, #-0x58]
    // 0xb46334: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb46334: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb46338: ldr             x0, [x0, #0x1530]
    //     0xb4633c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb46340: cmp             w0, w16
    //     0xb46344: b.ne            #0xb46354
    //     0xb46348: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb4634c: ldr             x2, [x2, #0x120]
    //     0xb46350: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb46354: stur            x0, [fp, #-0x28]
    // 0xb46358: r16 = "[0-9]"
    //     0xb46358: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb4635c: ldr             x16, [x16, #0x128]
    // 0xb46360: stp             x16, NULL, [SP, #0x20]
    // 0xb46364: r16 = false
    //     0xb46364: add             x16, NULL, #0x30  ; false
    // 0xb46368: r30 = true
    //     0xb46368: add             lr, NULL, #0x20  ; true
    // 0xb4636c: stp             lr, x16, [SP, #0x10]
    // 0xb46370: r16 = false
    //     0xb46370: add             x16, NULL, #0x30  ; false
    // 0xb46374: r30 = false
    //     0xb46374: add             lr, NULL, #0x30  ; false
    // 0xb46378: stp             lr, x16, [SP]
    // 0xb4637c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb4637c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb46380: r0 = _RegExp()
    //     0xb46380: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb46384: stur            x0, [fp, #-0x38]
    // 0xb46388: r0 = FilteringTextInputFormatter()
    //     0xb46388: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb4638c: mov             x3, x0
    // 0xb46390: ldur            x0, [fp, #-0x38]
    // 0xb46394: stur            x3, [fp, #-0x40]
    // 0xb46398: StoreField: r3->field_b = r0
    //     0xb46398: stur            w0, [x3, #0xb]
    // 0xb4639c: r0 = true
    //     0xb4639c: add             x0, NULL, #0x20  ; true
    // 0xb463a0: StoreField: r3->field_7 = r0
    //     0xb463a0: stur            w0, [x3, #7]
    // 0xb463a4: r1 = ""
    //     0xb463a4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb463a8: StoreField: r3->field_f = r1
    //     0xb463a8: stur            w1, [x3, #0xf]
    // 0xb463ac: r1 = Null
    //     0xb463ac: mov             x1, NULL
    // 0xb463b0: r2 = 4
    //     0xb463b0: movz            x2, #0x4
    // 0xb463b4: r0 = AllocateArray()
    //     0xb463b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb463b8: mov             x2, x0
    // 0xb463bc: ldur            x0, [fp, #-0x28]
    // 0xb463c0: stur            x2, [fp, #-0x38]
    // 0xb463c4: StoreField: r2->field_f = r0
    //     0xb463c4: stur            w0, [x2, #0xf]
    // 0xb463c8: ldur            x0, [fp, #-0x40]
    // 0xb463cc: StoreField: r2->field_13 = r0
    //     0xb463cc: stur            w0, [x2, #0x13]
    // 0xb463d0: r1 = <TextInputFormatter>
    //     0xb463d0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb463d4: ldr             x1, [x1, #0x7b0]
    // 0xb463d8: r0 = AllocateGrowableArray()
    //     0xb463d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb463dc: mov             x2, x0
    // 0xb463e0: ldur            x0, [fp, #-0x38]
    // 0xb463e4: stur            x2, [fp, #-0x28]
    // 0xb463e8: StoreField: r2->field_f = r0
    //     0xb463e8: stur            w0, [x2, #0xf]
    // 0xb463ec: r0 = 4
    //     0xb463ec: movz            x0, #0x4
    // 0xb463f0: StoreField: r2->field_b = r0
    //     0xb463f0: stur            w0, [x2, #0xb]
    // 0xb463f4: ldur            x0, [fp, #-0x18]
    // 0xb463f8: LoadField: r1 = r0->field_13
    //     0xb463f8: ldur            w1, [x0, #0x13]
    // 0xb463fc: DecompressPointer r1
    //     0xb463fc: add             x1, x1, HEAP, lsl #32
    // 0xb46400: r0 = of()
    //     0xb46400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46404: LoadField: r1 = r0->field_5b
    //     0xb46404: ldur            w1, [x0, #0x5b]
    // 0xb46408: DecompressPointer r1
    //     0xb46408: add             x1, x1, HEAP, lsl #32
    // 0xb4640c: stur            x1, [fp, #-0x38]
    // 0xb46410: r0 = Cursor()
    //     0xb46410: bl              #0xa09868  ; AllocateCursorStub -> Cursor (size=0x3c)
    // 0xb46414: d0 = 1.000000
    //     0xb46414: fmov            d0, #1.00000000
    // 0xb46418: stur            x0, [fp, #-0x40]
    // 0xb4641c: StoreField: r0->field_7 = d0
    //     0xb4641c: stur            d0, [x0, #7]
    // 0xb46420: d1 = 27.000000
    //     0xb46420: fmov            d1, #27.00000000
    // 0xb46424: StoreField: r0->field_f = d1
    //     0xb46424: stur            d1, [x0, #0xf]
    // 0xb46428: r1 = Instance_Radius
    //     0xb46428: add             x1, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0xb4642c: ldr             x1, [x1, #0xb48]
    // 0xb46430: ArrayStore: r0[0] = r1  ; List_4
    //     0xb46430: stur            w1, [x0, #0x17]
    // 0xb46434: ldur            x1, [fp, #-0x38]
    // 0xb46438: StoreField: r0->field_1b = r1
    //     0xb46438: stur            w1, [x0, #0x1b]
    // 0xb4643c: r1 = Instance_Duration
    //     0xb4643c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa058] Obj!Duration@d777a1
    //     0xb46440: ldr             x1, [x1, #0x58]
    // 0xb46444: StoreField: r0->field_1f = r1
    //     0xb46444: stur            w1, [x0, #0x1f]
    // 0xb46448: r1 = Instance_Duration
    //     0xb46448: ldr             x1, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb4644c: StoreField: r0->field_23 = r1
    //     0xb4644c: stur            w1, [x0, #0x23]
    // 0xb46450: r1 = Instance_Duration
    //     0xb46450: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e4b8] Obj!Duration@d77851
    //     0xb46454: ldr             x1, [x1, #0x4b8]
    // 0xb46458: StoreField: r0->field_27 = r1
    //     0xb46458: stur            w1, [x0, #0x27]
    // 0xb4645c: r1 = Instance_Orientation
    //     0xb4645c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37198] Obj!Orientation@d70241
    //     0xb46460: ldr             x1, [x1, #0x198]
    // 0xb46464: StoreField: r0->field_2f = r1
    //     0xb46464: stur            w1, [x0, #0x2f]
    // 0xb46468: StoreField: r0->field_33 = rZR
    //     0xb46468: stur            xzr, [x0, #0x33]
    // 0xb4646c: r2 = true
    //     0xb4646c: add             x2, NULL, #0x20  ; true
    // 0xb46470: StoreField: r0->field_2b = r2
    //     0xb46470: stur            w2, [x0, #0x2b]
    // 0xb46474: ldur            x3, [fp, #-0x18]
    // 0xb46478: LoadField: r1 = r3->field_13
    //     0xb46478: ldur            w1, [x3, #0x13]
    // 0xb4647c: DecompressPointer r1
    //     0xb4647c: add             x1, x1, HEAP, lsl #32
    // 0xb46480: r0 = of()
    //     0xb46480: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46484: LoadField: r1 = r0->field_87
    //     0xb46484: ldur            w1, [x0, #0x87]
    // 0xb46488: DecompressPointer r1
    //     0xb46488: add             x1, x1, HEAP, lsl #32
    // 0xb4648c: LoadField: r0 = r1->field_2b
    //     0xb4648c: ldur            w0, [x1, #0x2b]
    // 0xb46490: DecompressPointer r0
    //     0xb46490: add             x0, x0, HEAP, lsl #32
    // 0xb46494: ldur            x2, [fp, #-0x18]
    // 0xb46498: stur            x0, [fp, #-0x38]
    // 0xb4649c: LoadField: r1 = r2->field_13
    //     0xb4649c: ldur            w1, [x2, #0x13]
    // 0xb464a0: DecompressPointer r1
    //     0xb464a0: add             x1, x1, HEAP, lsl #32
    // 0xb464a4: r0 = of()
    //     0xb464a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb464a8: LoadField: r1 = r0->field_5b
    //     0xb464a8: ldur            w1, [x0, #0x5b]
    // 0xb464ac: DecompressPointer r1
    //     0xb464ac: add             x1, x1, HEAP, lsl #32
    // 0xb464b0: r16 = 16.000000
    //     0xb464b0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb464b4: ldr             x16, [x16, #0x188]
    // 0xb464b8: stp             x1, x16, [SP]
    // 0xb464bc: ldur            x1, [fp, #-0x38]
    // 0xb464c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb464c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb464c4: ldr             x4, [x4, #0xaa0]
    // 0xb464c8: r0 = copyWith()
    //     0xb464c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb464cc: ldur            x2, [fp, #-0x18]
    // 0xb464d0: stur            x0, [fp, #-0x38]
    // 0xb464d4: LoadField: r1 = r2->field_13
    //     0xb464d4: ldur            w1, [x2, #0x13]
    // 0xb464d8: DecompressPointer r1
    //     0xb464d8: add             x1, x1, HEAP, lsl #32
    // 0xb464dc: r0 = of()
    //     0xb464dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb464e0: LoadField: r1 = r0->field_5b
    //     0xb464e0: ldur            w1, [x0, #0x5b]
    // 0xb464e4: DecompressPointer r1
    //     0xb464e4: add             x1, x1, HEAP, lsl #32
    // 0xb464e8: r0 = LoadClassIdInstr(r1)
    //     0xb464e8: ldur            x0, [x1, #-1]
    //     0xb464ec: ubfx            x0, x0, #0xc, #0x14
    // 0xb464f0: d0 = 0.300000
    //     0xb464f0: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb464f4: ldr             d0, [x17, #0x658]
    // 0xb464f8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb464f8: sub             lr, x0, #0xffa
    //     0xb464fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb46500: blr             lr
    // 0xb46504: r1 = <Color>
    //     0xb46504: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb46508: ldr             x1, [x1, #0xf80]
    // 0xb4650c: stur            x0, [fp, #-0x48]
    // 0xb46510: r0 = FixedColorBuilder()
    //     0xb46510: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0xb46514: mov             x1, x0
    // 0xb46518: ldur            x0, [fp, #-0x48]
    // 0xb4651c: stur            x1, [fp, #-0x50]
    // 0xb46520: StoreField: r1->field_b = r0
    //     0xb46520: stur            w0, [x1, #0xb]
    // 0xb46524: r0 = UnderlineDecoration()
    //     0xb46524: bl              #0xa09850  ; AllocateUnderlineDecorationStub -> UnderlineDecoration (size=0x48)
    // 0xb46528: d0 = 12.000000
    //     0xb46528: fmov            d0, #12.00000000
    // 0xb4652c: stur            x0, [fp, #-0x48]
    // 0xb46530: StoreField: r0->field_27 = d0
    //     0xb46530: stur            d0, [x0, #0x27]
    // 0xb46534: ldur            x1, [fp, #-0x50]
    // 0xb46538: StoreField: r0->field_33 = r1
    //     0xb46538: stur            w1, [x0, #0x33]
    // 0xb4653c: d0 = 1.000000
    //     0xb4653c: fmov            d0, #1.00000000
    // 0xb46540: StoreField: r0->field_37 = d0
    //     0xb46540: stur            d0, [x0, #0x37]
    // 0xb46544: r1 = Sentinel
    //     0xb46544: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb46548: StoreField: r0->field_23 = r1
    //     0xb46548: stur            w1, [x0, #0x23]
    // 0xb4654c: ldur            x1, [fp, #-0x38]
    // 0xb46550: StoreField: r0->field_7 = r1
    //     0xb46550: stur            w1, [x0, #7]
    // 0xb46554: ldur            x1, [fp, #-8]
    // 0xb46558: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb46558: ldur            w2, [x1, #0x17]
    // 0xb4655c: DecompressPointer r2
    //     0xb4655c: add             x2, x2, HEAP, lsl #32
    // 0xb46560: stur            x2, [fp, #-0x38]
    // 0xb46564: r0 = PinFieldAutoFill()
    //     0xb46564: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0xb46568: mov             x3, x0
    // 0xb4656c: r0 = Instance_TextInputType
    //     0xb4656c: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb46570: ldr             x0, [x0, #0x1a0]
    // 0xb46574: stur            x3, [fp, #-0x50]
    // 0xb46578: StoreField: r3->field_33 = r0
    //     0xb46578: stur            w0, [x3, #0x33]
    // 0xb4657c: r0 = Instance_TextInputAction
    //     0xb4657c: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0xb46580: StoreField: r3->field_37 = r0
    //     0xb46580: stur            w0, [x3, #0x37]
    // 0xb46584: ldur            x0, [fp, #-0x40]
    // 0xb46588: StoreField: r3->field_2f = r0
    //     0xb46588: stur            w0, [x3, #0x2f]
    // 0xb4658c: ldur            x0, [fp, #-0x28]
    // 0xb46590: StoreField: r3->field_47 = r0
    //     0xb46590: stur            w0, [x3, #0x47]
    // 0xb46594: r0 = true
    //     0xb46594: add             x0, NULL, #0x20  ; true
    // 0xb46598: StoreField: r3->field_3b = r0
    //     0xb46598: stur            w0, [x3, #0x3b]
    // 0xb4659c: StoreField: r3->field_3f = r0
    //     0xb4659c: stur            w0, [x3, #0x3f]
    // 0xb465a0: ldur            x0, [fp, #-0x48]
    // 0xb465a4: StoreField: r3->field_27 = r0
    //     0xb465a4: stur            w0, [x3, #0x27]
    // 0xb465a8: ldur            x2, [fp, #-0x18]
    // 0xb465ac: r1 = Function '<anonymous closure>':.
    //     0xb465ac: add             x1, PP, #0x56, lsl #12  ; [pp+0x56630] AnonymousClosure: (0xb470cc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xb45fa4)
    //     0xb465b0: ldr             x1, [x1, #0x630]
    // 0xb465b4: r0 = AllocateClosure()
    //     0xb465b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb465b8: mov             x1, x0
    // 0xb465bc: ldur            x0, [fp, #-0x50]
    // 0xb465c0: StoreField: r0->field_1f = r1
    //     0xb465c0: stur            w1, [x0, #0x1f]
    // 0xb465c4: ldur            x2, [fp, #-0x18]
    // 0xb465c8: r1 = Function '<anonymous closure>':.
    //     0xb465c8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56638] AnonymousClosure: (0xb46fc0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xb45fa4)
    //     0xb465cc: ldr             x1, [x1, #0x638]
    // 0xb465d0: r0 = AllocateClosure()
    //     0xb465d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb465d4: mov             x1, x0
    // 0xb465d8: ldur            x0, [fp, #-0x50]
    // 0xb465dc: StoreField: r0->field_23 = r1
    //     0xb465dc: stur            w1, [x0, #0x23]
    // 0xb465e0: ldur            x1, [fp, #-0x38]
    // 0xb465e4: StoreField: r0->field_1b = r1
    //     0xb465e4: stur            w1, [x0, #0x1b]
    // 0xb465e8: r1 = false
    //     0xb465e8: add             x1, NULL, #0x30  ; false
    // 0xb465ec: StoreField: r0->field_13 = r1
    //     0xb465ec: stur            w1, [x0, #0x13]
    // 0xb465f0: r1 = 4
    //     0xb465f0: movz            x1, #0x4
    // 0xb465f4: StoreField: r0->field_b = r1
    //     0xb465f4: stur            x1, [x0, #0xb]
    // 0xb465f8: ldur            d0, [fp, #-0x58]
    // 0xb465fc: r1 = inline_Allocate_Double()
    //     0xb465fc: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb46600: add             x1, x1, #0x10
    //     0xb46604: cmp             x2, x1
    //     0xb46608: b.ls            #0xb467c8
    //     0xb4660c: str             x1, [THR, #0x50]  ; THR::top
    //     0xb46610: sub             x1, x1, #0xf
    //     0xb46614: movz            x2, #0xe15c
    //     0xb46618: movk            x2, #0x3, lsl #16
    //     0xb4661c: stur            x2, [x1, #-1]
    // 0xb46620: StoreField: r1->field_7 = d0
    //     0xb46620: stur            d0, [x1, #7]
    // 0xb46624: stur            x1, [fp, #-0x28]
    // 0xb46628: r0 = Container()
    //     0xb46628: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4662c: stur            x0, [fp, #-0x38]
    // 0xb46630: r16 = Instance_EdgeInsets
    //     0xb46630: add             x16, PP, #0x53, lsl #12  ; [pp+0x53f18] Obj!EdgeInsets@d57ec1
    //     0xb46634: ldr             x16, [x16, #0xf18]
    // 0xb46638: ldur            lr, [fp, #-0x28]
    // 0xb4663c: stp             lr, x16, [SP, #8]
    // 0xb46640: ldur            x16, [fp, #-0x50]
    // 0xb46644: str             x16, [SP]
    // 0xb46648: mov             x1, x0
    // 0xb4664c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb4664c: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb46650: ldr             x4, [x4, #0x1b8]
    // 0xb46654: r0 = Container()
    //     0xb46654: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb46658: r0 = Padding()
    //     0xb46658: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4665c: mov             x3, x0
    // 0xb46660: r0 = Instance_EdgeInsets
    //     0xb46660: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f20] Obj!EdgeInsets@d57e91
    //     0xb46664: ldr             x0, [x0, #0xf20]
    // 0xb46668: stur            x3, [fp, #-0x40]
    // 0xb4666c: StoreField: r3->field_f = r0
    //     0xb4666c: stur            w0, [x3, #0xf]
    // 0xb46670: ldur            x0, [fp, #-0x38]
    // 0xb46674: StoreField: r3->field_b = r0
    //     0xb46674: stur            w0, [x3, #0xb]
    // 0xb46678: ldur            x0, [fp, #-8]
    // 0xb4667c: LoadField: r4 = r0->field_1b
    //     0xb4667c: ldur            w4, [x0, #0x1b]
    // 0xb46680: DecompressPointer r4
    //     0xb46680: add             x4, x4, HEAP, lsl #32
    // 0xb46684: ldur            x2, [fp, #-0x18]
    // 0xb46688: stur            x4, [fp, #-0x28]
    // 0xb4668c: r1 = Function '<anonymous closure>':.
    //     0xb4668c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56640] AnonymousClosure: (0xb467e4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xb45fa4)
    //     0xb46690: ldr             x1, [x1, #0x640]
    // 0xb46694: r0 = AllocateClosure()
    //     0xb46694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb46698: r1 = <int, AsyncSnapshot<int>, int>
    //     0xb46698: add             x1, PP, #0x53, lsl #12  ; [pp+0x53f30] TypeArguments: <int, AsyncSnapshot<int>, int>
    //     0xb4669c: ldr             x1, [x1, #0xf30]
    // 0xb466a0: stur            x0, [fp, #-8]
    // 0xb466a4: r0 = StreamBuilder()
    //     0xb466a4: bl              #0xa09838  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xb466a8: mov             x1, x0
    // 0xb466ac: ldur            x0, [fp, #-8]
    // 0xb466b0: stur            x1, [fp, #-0x18]
    // 0xb466b4: StoreField: r1->field_13 = r0
    //     0xb466b4: stur            w0, [x1, #0x13]
    // 0xb466b8: ldur            x0, [fp, #-0x28]
    // 0xb466bc: StoreField: r1->field_f = r0
    //     0xb466bc: stur            w0, [x1, #0xf]
    // 0xb466c0: r0 = Padding()
    //     0xb466c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb466c4: mov             x3, x0
    // 0xb466c8: r0 = Instance_EdgeInsets
    //     0xb466c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb466cc: ldr             x0, [x0, #0x668]
    // 0xb466d0: stur            x3, [fp, #-8]
    // 0xb466d4: StoreField: r3->field_f = r0
    //     0xb466d4: stur            w0, [x3, #0xf]
    // 0xb466d8: ldur            x0, [fp, #-0x18]
    // 0xb466dc: StoreField: r3->field_b = r0
    //     0xb466dc: stur            w0, [x3, #0xb]
    // 0xb466e0: r1 = Null
    //     0xb466e0: mov             x1, NULL
    // 0xb466e4: r2 = 10
    //     0xb466e4: movz            x2, #0xa
    // 0xb466e8: r0 = AllocateArray()
    //     0xb466e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb466ec: mov             x2, x0
    // 0xb466f0: ldur            x0, [fp, #-0x20]
    // 0xb466f4: stur            x2, [fp, #-0x18]
    // 0xb466f8: StoreField: r2->field_f = r0
    //     0xb466f8: stur            w0, [x2, #0xf]
    // 0xb466fc: ldur            x0, [fp, #-0x10]
    // 0xb46700: StoreField: r2->field_13 = r0
    //     0xb46700: stur            w0, [x2, #0x13]
    // 0xb46704: ldur            x0, [fp, #-0x30]
    // 0xb46708: ArrayStore: r2[0] = r0  ; List_4
    //     0xb46708: stur            w0, [x2, #0x17]
    // 0xb4670c: ldur            x0, [fp, #-0x40]
    // 0xb46710: StoreField: r2->field_1b = r0
    //     0xb46710: stur            w0, [x2, #0x1b]
    // 0xb46714: ldur            x0, [fp, #-8]
    // 0xb46718: StoreField: r2->field_1f = r0
    //     0xb46718: stur            w0, [x2, #0x1f]
    // 0xb4671c: r1 = <Widget>
    //     0xb4671c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb46720: r0 = AllocateGrowableArray()
    //     0xb46720: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb46724: mov             x1, x0
    // 0xb46728: ldur            x0, [fp, #-0x18]
    // 0xb4672c: stur            x1, [fp, #-8]
    // 0xb46730: StoreField: r1->field_f = r0
    //     0xb46730: stur            w0, [x1, #0xf]
    // 0xb46734: r0 = 10
    //     0xb46734: movz            x0, #0xa
    // 0xb46738: StoreField: r1->field_b = r0
    //     0xb46738: stur            w0, [x1, #0xb]
    // 0xb4673c: r0 = Column()
    //     0xb4673c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb46740: mov             x1, x0
    // 0xb46744: r0 = Instance_Axis
    //     0xb46744: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb46748: stur            x1, [fp, #-0x10]
    // 0xb4674c: StoreField: r1->field_f = r0
    //     0xb4674c: stur            w0, [x1, #0xf]
    // 0xb46750: r0 = Instance_MainAxisAlignment
    //     0xb46750: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb46754: ldr             x0, [x0, #0xa08]
    // 0xb46758: StoreField: r1->field_13 = r0
    //     0xb46758: stur            w0, [x1, #0x13]
    // 0xb4675c: r0 = Instance_MainAxisSize
    //     0xb4675c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb46760: ldr             x0, [x0, #0xa10]
    // 0xb46764: ArrayStore: r1[0] = r0  ; List_4
    //     0xb46764: stur            w0, [x1, #0x17]
    // 0xb46768: r0 = Instance_CrossAxisAlignment
    //     0xb46768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4676c: ldr             x0, [x0, #0x890]
    // 0xb46770: StoreField: r1->field_1b = r0
    //     0xb46770: stur            w0, [x1, #0x1b]
    // 0xb46774: r0 = Instance_VerticalDirection
    //     0xb46774: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb46778: ldr             x0, [x0, #0xa20]
    // 0xb4677c: StoreField: r1->field_23 = r0
    //     0xb4677c: stur            w0, [x1, #0x23]
    // 0xb46780: r0 = Instance_Clip
    //     0xb46780: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb46784: ldr             x0, [x0, #0x38]
    // 0xb46788: StoreField: r1->field_2b = r0
    //     0xb46788: stur            w0, [x1, #0x2b]
    // 0xb4678c: StoreField: r1->field_2f = rZR
    //     0xb4678c: stur            xzr, [x1, #0x2f]
    // 0xb46790: ldur            x0, [fp, #-8]
    // 0xb46794: StoreField: r1->field_b = r0
    //     0xb46794: stur            w0, [x1, #0xb]
    // 0xb46798: r0 = Padding()
    //     0xb46798: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4679c: r1 = Instance_EdgeInsets
    //     0xb4679c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xb467a0: ldr             x1, [x1, #0xb0]
    // 0xb467a4: StoreField: r0->field_f = r1
    //     0xb467a4: stur            w1, [x0, #0xf]
    // 0xb467a8: ldur            x1, [fp, #-0x10]
    // 0xb467ac: StoreField: r0->field_b = r1
    //     0xb467ac: stur            w1, [x0, #0xb]
    // 0xb467b0: LeaveFrame
    //     0xb467b0: mov             SP, fp
    //     0xb467b4: ldp             fp, lr, [SP], #0x10
    // 0xb467b8: ret
    //     0xb467b8: ret             
    // 0xb467bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb467bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb467c0: b               #0xb45fcc
    // 0xb467c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb467c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb467c8: SaveReg d0
    //     0xb467c8: str             q0, [SP, #-0x10]!
    // 0xb467cc: SaveReg r0
    //     0xb467cc: str             x0, [SP, #-8]!
    // 0xb467d0: r0 = AllocateDouble()
    //     0xb467d0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb467d4: mov             x1, x0
    // 0xb467d8: RestoreReg r0
    //     0xb467d8: ldr             x0, [SP], #8
    // 0xb467dc: RestoreReg d0
    //     0xb467dc: ldr             q0, [SP], #0x10
    // 0xb467e0: b               #0xb46620
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int>) {
    // ** addr: 0xb467e4, size: 0x650
    // 0xb467e4: EnterFrame
    //     0xb467e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb467e8: mov             fp, SP
    // 0xb467ec: AllocStack(0x48)
    //     0xb467ec: sub             SP, SP, #0x48
    // 0xb467f0: SetupParameters()
    //     0xb467f0: ldr             x0, [fp, #0x20]
    //     0xb467f4: ldur            w2, [x0, #0x17]
    //     0xb467f8: add             x2, x2, HEAP, lsl #32
    //     0xb467fc: stur            x2, [fp, #-0x38]
    // 0xb46800: CheckStackOverflow
    //     0xb46800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb46804: cmp             SP, x16
    //     0xb46808: b.ls            #0xb46e2c
    // 0xb4680c: ldr             x0, [fp, #0x10]
    // 0xb46810: LoadField: r1 = r0->field_f
    //     0xb46810: ldur            w1, [x0, #0xf]
    // 0xb46814: DecompressPointer r1
    //     0xb46814: add             x1, x1, HEAP, lsl #32
    // 0xb46818: stur            x1, [fp, #-0x30]
    // 0xb4681c: cmp             w1, NULL
    // 0xb46820: b.ne            #0xb46a68
    // 0xb46824: ldr             x1, [fp, #0x18]
    // 0xb46828: r0 = of()
    //     0xb46828: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4682c: LoadField: r1 = r0->field_87
    //     0xb4682c: ldur            w1, [x0, #0x87]
    // 0xb46830: DecompressPointer r1
    //     0xb46830: add             x1, x1, HEAP, lsl #32
    // 0xb46834: LoadField: r0 = r1->field_2b
    //     0xb46834: ldur            w0, [x1, #0x2b]
    // 0xb46838: DecompressPointer r0
    //     0xb46838: add             x0, x0, HEAP, lsl #32
    // 0xb4683c: stur            x0, [fp, #-8]
    // 0xb46840: r1 = Instance_Color
    //     0xb46840: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46844: d0 = 0.700000
    //     0xb46844: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb46848: ldr             d0, [x17, #0xf48]
    // 0xb4684c: r0 = withOpacity()
    //     0xb4684c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb46850: r16 = 12.000000
    //     0xb46850: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46854: ldr             x16, [x16, #0x9e8]
    // 0xb46858: stp             x16, x0, [SP]
    // 0xb4685c: ldur            x1, [fp, #-8]
    // 0xb46860: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46860: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46864: ldr             x4, [x4, #0x9b8]
    // 0xb46868: r0 = copyWith()
    //     0xb46868: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4686c: stur            x0, [fp, #-8]
    // 0xb46870: r0 = Text()
    //     0xb46870: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46874: r3 = "Didn\'t receive OTP\? "
    //     0xb46874: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xb46878: ldr             x3, [x3, #0xf38]
    // 0xb4687c: stur            x0, [fp, #-0x10]
    // 0xb46880: StoreField: r0->field_b = r3
    //     0xb46880: stur            w3, [x0, #0xb]
    // 0xb46884: ldur            x1, [fp, #-8]
    // 0xb46888: StoreField: r0->field_13 = r1
    //     0xb46888: stur            w1, [x0, #0x13]
    // 0xb4688c: ldr             x1, [fp, #0x18]
    // 0xb46890: r0 = of()
    //     0xb46890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46894: LoadField: r1 = r0->field_87
    //     0xb46894: ldur            w1, [x0, #0x87]
    // 0xb46898: DecompressPointer r1
    //     0xb46898: add             x1, x1, HEAP, lsl #32
    // 0xb4689c: LoadField: r0 = r1->field_2b
    //     0xb4689c: ldur            w0, [x1, #0x2b]
    // 0xb468a0: DecompressPointer r0
    //     0xb468a0: add             x0, x0, HEAP, lsl #32
    // 0xb468a4: r16 = Instance_Color
    //     0xb468a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb468a8: ldr             x16, [x16, #0x858]
    // 0xb468ac: r30 = 12.000000
    //     0xb468ac: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb468b0: ldr             lr, [lr, #0x9e8]
    // 0xb468b4: stp             lr, x16, [SP]
    // 0xb468b8: mov             x1, x0
    // 0xb468bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb468bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb468c0: ldr             x4, [x4, #0x9b8]
    // 0xb468c4: r0 = copyWith()
    //     0xb468c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb468c8: stur            x0, [fp, #-8]
    // 0xb468cc: r0 = Text()
    //     0xb468cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb468d0: r4 = "Resend OTP "
    //     0xb468d0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xb468d4: ldr             x4, [x4, #0xf40]
    // 0xb468d8: stur            x0, [fp, #-0x18]
    // 0xb468dc: StoreField: r0->field_b = r4
    //     0xb468dc: stur            w4, [x0, #0xb]
    // 0xb468e0: ldur            x1, [fp, #-8]
    // 0xb468e4: StoreField: r0->field_13 = r1
    //     0xb468e4: stur            w1, [x0, #0x13]
    // 0xb468e8: ldr             x1, [fp, #0x18]
    // 0xb468ec: r0 = of()
    //     0xb468ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb468f0: LoadField: r1 = r0->field_87
    //     0xb468f0: ldur            w1, [x0, #0x87]
    // 0xb468f4: DecompressPointer r1
    //     0xb468f4: add             x1, x1, HEAP, lsl #32
    // 0xb468f8: LoadField: r0 = r1->field_2b
    //     0xb468f8: ldur            w0, [x1, #0x2b]
    // 0xb468fc: DecompressPointer r0
    //     0xb468fc: add             x0, x0, HEAP, lsl #32
    // 0xb46900: stur            x0, [fp, #-8]
    // 0xb46904: r1 = Instance_Color
    //     0xb46904: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46908: d0 = 0.700000
    //     0xb46908: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4690c: ldr             d0, [x17, #0xf48]
    // 0xb46910: r0 = withOpacity()
    //     0xb46910: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb46914: r16 = 12.000000
    //     0xb46914: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46918: ldr             x16, [x16, #0x9e8]
    // 0xb4691c: stp             x16, x0, [SP]
    // 0xb46920: ldur            x1, [fp, #-8]
    // 0xb46924: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46924: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46928: ldr             x4, [x4, #0x9b8]
    // 0xb4692c: r0 = copyWith()
    //     0xb4692c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46930: stur            x0, [fp, #-8]
    // 0xb46934: r0 = Text()
    //     0xb46934: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46938: mov             x2, x0
    // 0xb4693c: r0 = "in "
    //     0xb4693c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xb46940: ldr             x0, [x0, #0xf48]
    // 0xb46944: stur            x2, [fp, #-0x20]
    // 0xb46948: StoreField: r2->field_b = r0
    //     0xb46948: stur            w0, [x2, #0xb]
    // 0xb4694c: ldur            x0, [fp, #-8]
    // 0xb46950: StoreField: r2->field_13 = r0
    //     0xb46950: stur            w0, [x2, #0x13]
    // 0xb46954: ldr             x1, [fp, #0x18]
    // 0xb46958: r0 = of()
    //     0xb46958: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4695c: LoadField: r1 = r0->field_87
    //     0xb4695c: ldur            w1, [x0, #0x87]
    // 0xb46960: DecompressPointer r1
    //     0xb46960: add             x1, x1, HEAP, lsl #32
    // 0xb46964: LoadField: r0 = r1->field_2b
    //     0xb46964: ldur            w0, [x1, #0x2b]
    // 0xb46968: DecompressPointer r0
    //     0xb46968: add             x0, x0, HEAP, lsl #32
    // 0xb4696c: r16 = Instance_Color
    //     0xb4696c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb46970: ldr             x16, [x16, #0x50]
    // 0xb46974: r30 = 12.000000
    //     0xb46974: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46978: ldr             lr, [lr, #0x9e8]
    // 0xb4697c: stp             lr, x16, [SP]
    // 0xb46980: mov             x1, x0
    // 0xb46984: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46984: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46988: ldr             x4, [x4, #0x9b8]
    // 0xb4698c: r0 = copyWith()
    //     0xb4698c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46990: stur            x0, [fp, #-8]
    // 0xb46994: r0 = Text()
    //     0xb46994: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46998: mov             x3, x0
    // 0xb4699c: r0 = "30s"
    //     0xb4699c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f50] "30s"
    //     0xb469a0: ldr             x0, [x0, #0xf50]
    // 0xb469a4: stur            x3, [fp, #-0x28]
    // 0xb469a8: StoreField: r3->field_b = r0
    //     0xb469a8: stur            w0, [x3, #0xb]
    // 0xb469ac: ldur            x0, [fp, #-8]
    // 0xb469b0: StoreField: r3->field_13 = r0
    //     0xb469b0: stur            w0, [x3, #0x13]
    // 0xb469b4: r1 = Null
    //     0xb469b4: mov             x1, NULL
    // 0xb469b8: r2 = 8
    //     0xb469b8: movz            x2, #0x8
    // 0xb469bc: r0 = AllocateArray()
    //     0xb469bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb469c0: mov             x2, x0
    // 0xb469c4: ldur            x0, [fp, #-0x10]
    // 0xb469c8: stur            x2, [fp, #-8]
    // 0xb469cc: StoreField: r2->field_f = r0
    //     0xb469cc: stur            w0, [x2, #0xf]
    // 0xb469d0: ldur            x0, [fp, #-0x18]
    // 0xb469d4: StoreField: r2->field_13 = r0
    //     0xb469d4: stur            w0, [x2, #0x13]
    // 0xb469d8: ldur            x0, [fp, #-0x20]
    // 0xb469dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb469dc: stur            w0, [x2, #0x17]
    // 0xb469e0: ldur            x0, [fp, #-0x28]
    // 0xb469e4: StoreField: r2->field_1b = r0
    //     0xb469e4: stur            w0, [x2, #0x1b]
    // 0xb469e8: r1 = <Widget>
    //     0xb469e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb469ec: r0 = AllocateGrowableArray()
    //     0xb469ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb469f0: mov             x1, x0
    // 0xb469f4: ldur            x0, [fp, #-8]
    // 0xb469f8: stur            x1, [fp, #-0x10]
    // 0xb469fc: StoreField: r1->field_f = r0
    //     0xb469fc: stur            w0, [x1, #0xf]
    // 0xb46a00: r5 = 8
    //     0xb46a00: movz            x5, #0x8
    // 0xb46a04: StoreField: r1->field_b = r5
    //     0xb46a04: stur            w5, [x1, #0xb]
    // 0xb46a08: r0 = Row()
    //     0xb46a08: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb46a0c: r6 = Instance_Axis
    //     0xb46a0c: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb46a10: StoreField: r0->field_f = r6
    //     0xb46a10: stur            w6, [x0, #0xf]
    // 0xb46a14: r7 = Instance_MainAxisAlignment
    //     0xb46a14: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb46a18: ldr             x7, [x7, #0xa08]
    // 0xb46a1c: StoreField: r0->field_13 = r7
    //     0xb46a1c: stur            w7, [x0, #0x13]
    // 0xb46a20: r8 = Instance_MainAxisSize
    //     0xb46a20: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb46a24: ldr             x8, [x8, #0xa10]
    // 0xb46a28: ArrayStore: r0[0] = r8  ; List_4
    //     0xb46a28: stur            w8, [x0, #0x17]
    // 0xb46a2c: r9 = Instance_CrossAxisAlignment
    //     0xb46a2c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb46a30: ldr             x9, [x9, #0xa18]
    // 0xb46a34: StoreField: r0->field_1b = r9
    //     0xb46a34: stur            w9, [x0, #0x1b]
    // 0xb46a38: r10 = Instance_VerticalDirection
    //     0xb46a38: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb46a3c: ldr             x10, [x10, #0xa20]
    // 0xb46a40: StoreField: r0->field_23 = r10
    //     0xb46a40: stur            w10, [x0, #0x23]
    // 0xb46a44: r11 = Instance_Clip
    //     0xb46a44: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb46a48: ldr             x11, [x11, #0x38]
    // 0xb46a4c: StoreField: r0->field_2b = r11
    //     0xb46a4c: stur            w11, [x0, #0x2b]
    // 0xb46a50: StoreField: r0->field_2f = rZR
    //     0xb46a50: stur            xzr, [x0, #0x2f]
    // 0xb46a54: ldur            x1, [fp, #-0x10]
    // 0xb46a58: StoreField: r0->field_b = r1
    //     0xb46a58: stur            w1, [x0, #0xb]
    // 0xb46a5c: LeaveFrame
    //     0xb46a5c: mov             SP, fp
    //     0xb46a60: ldp             fp, lr, [SP], #0x10
    // 0xb46a64: ret
    //     0xb46a64: ret             
    // 0xb46a68: r3 = "Didn\'t receive OTP\? "
    //     0xb46a68: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xb46a6c: ldr             x3, [x3, #0xf38]
    // 0xb46a70: r4 = "Resend OTP "
    //     0xb46a70: add             x4, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xb46a74: ldr             x4, [x4, #0xf40]
    // 0xb46a78: r5 = 8
    //     0xb46a78: movz            x5, #0x8
    // 0xb46a7c: r9 = Instance_CrossAxisAlignment
    //     0xb46a7c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb46a80: ldr             x9, [x9, #0xa18]
    // 0xb46a84: r7 = Instance_MainAxisAlignment
    //     0xb46a84: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb46a88: ldr             x7, [x7, #0xa08]
    // 0xb46a8c: r8 = Instance_MainAxisSize
    //     0xb46a8c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb46a90: ldr             x8, [x8, #0xa10]
    // 0xb46a94: r6 = Instance_Axis
    //     0xb46a94: ldr             x6, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb46a98: r10 = Instance_VerticalDirection
    //     0xb46a98: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb46a9c: ldr             x10, [x10, #0xa20]
    // 0xb46aa0: r11 = Instance_Clip
    //     0xb46aa0: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb46aa4: ldr             x11, [x11, #0x38]
    // 0xb46aa8: r0 = 60
    //     0xb46aa8: movz            x0, #0x3c
    // 0xb46aac: branchIfSmi(r1, 0xb46ab8)
    //     0xb46aac: tbz             w1, #0, #0xb46ab8
    // 0xb46ab0: r0 = LoadClassIdInstr(r1)
    //     0xb46ab0: ldur            x0, [x1, #-1]
    //     0xb46ab4: ubfx            x0, x0, #0xc, #0x14
    // 0xb46ab8: stp             xzr, x1, [SP]
    // 0xb46abc: r0 = GDT[cid_x0 + -0xff5]()
    //     0xb46abc: sub             lr, x0, #0xff5
    //     0xb46ac0: ldr             lr, [x21, lr, lsl #3]
    //     0xb46ac4: blr             lr
    // 0xb46ac8: stur            x0, [fp, #-8]
    // 0xb46acc: tbnz            w0, #4, #0xb46ae8
    // 0xb46ad0: ldur            x2, [fp, #-0x38]
    // 0xb46ad4: r3 = true
    //     0xb46ad4: add             x3, NULL, #0x20  ; true
    // 0xb46ad8: LoadField: r1 = r2->field_f
    //     0xb46ad8: ldur            w1, [x2, #0xf]
    // 0xb46adc: DecompressPointer r1
    //     0xb46adc: add             x1, x1, HEAP, lsl #32
    // 0xb46ae0: StoreField: r1->field_13 = r3
    //     0xb46ae0: stur            w3, [x1, #0x13]
    // 0xb46ae4: b               #0xb46af0
    // 0xb46ae8: ldur            x2, [fp, #-0x38]
    // 0xb46aec: r3 = true
    //     0xb46aec: add             x3, NULL, #0x20  ; true
    // 0xb46af0: ldr             x1, [fp, #0x18]
    // 0xb46af4: r0 = of()
    //     0xb46af4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46af8: LoadField: r1 = r0->field_87
    //     0xb46af8: ldur            w1, [x0, #0x87]
    // 0xb46afc: DecompressPointer r1
    //     0xb46afc: add             x1, x1, HEAP, lsl #32
    // 0xb46b00: LoadField: r0 = r1->field_2b
    //     0xb46b00: ldur            w0, [x1, #0x2b]
    // 0xb46b04: DecompressPointer r0
    //     0xb46b04: add             x0, x0, HEAP, lsl #32
    // 0xb46b08: stur            x0, [fp, #-0x10]
    // 0xb46b0c: r1 = Instance_Color
    //     0xb46b0c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46b10: d0 = 0.700000
    //     0xb46b10: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb46b14: ldr             d0, [x17, #0xf48]
    // 0xb46b18: r0 = withOpacity()
    //     0xb46b18: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb46b1c: r16 = 12.000000
    //     0xb46b1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46b20: ldr             x16, [x16, #0x9e8]
    // 0xb46b24: stp             x16, x0, [SP]
    // 0xb46b28: ldur            x1, [fp, #-0x10]
    // 0xb46b2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46b2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46b30: ldr             x4, [x4, #0x9b8]
    // 0xb46b34: r0 = copyWith()
    //     0xb46b34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46b38: stur            x0, [fp, #-0x10]
    // 0xb46b3c: r0 = Text()
    //     0xb46b3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46b40: mov             x3, x0
    // 0xb46b44: r0 = "Didn\'t receive OTP\? "
    //     0xb46b44: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f38] "Didn\'t receive OTP\? "
    //     0xb46b48: ldr             x0, [x0, #0xf38]
    // 0xb46b4c: stur            x3, [fp, #-0x18]
    // 0xb46b50: StoreField: r3->field_b = r0
    //     0xb46b50: stur            w0, [x3, #0xb]
    // 0xb46b54: ldur            x0, [fp, #-0x10]
    // 0xb46b58: StoreField: r3->field_13 = r0
    //     0xb46b58: stur            w0, [x3, #0x13]
    // 0xb46b5c: ldur            x2, [fp, #-0x38]
    // 0xb46b60: LoadField: r0 = r2->field_f
    //     0xb46b60: ldur            w0, [x2, #0xf]
    // 0xb46b64: DecompressPointer r0
    //     0xb46b64: add             x0, x0, HEAP, lsl #32
    // 0xb46b68: LoadField: r1 = r0->field_13
    //     0xb46b68: ldur            w1, [x0, #0x13]
    // 0xb46b6c: DecompressPointer r1
    //     0xb46b6c: add             x1, x1, HEAP, lsl #32
    // 0xb46b70: tbnz            w1, #4, #0xb46b90
    // 0xb46b74: ldur            x0, [fp, #-8]
    // 0xb46b78: tbnz            w0, #4, #0xb46b90
    // 0xb46b7c: r1 = Function '<anonymous closure>':.
    //     0xb46b7c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56648] AnonymousClosure: (0xb46e34), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::build (0xb45fa4)
    //     0xb46b80: ldr             x1, [x1, #0x648]
    // 0xb46b84: r0 = AllocateClosure()
    //     0xb46b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb46b88: mov             x2, x0
    // 0xb46b8c: b               #0xb46b94
    // 0xb46b90: r2 = Null
    //     0xb46b90: mov             x2, NULL
    // 0xb46b94: ldur            x0, [fp, #-8]
    // 0xb46b98: ldr             x1, [fp, #0x18]
    // 0xb46b9c: stur            x2, [fp, #-0x10]
    // 0xb46ba0: r0 = of()
    //     0xb46ba0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46ba4: LoadField: r1 = r0->field_87
    //     0xb46ba4: ldur            w1, [x0, #0x87]
    // 0xb46ba8: DecompressPointer r1
    //     0xb46ba8: add             x1, x1, HEAP, lsl #32
    // 0xb46bac: LoadField: r0 = r1->field_2b
    //     0xb46bac: ldur            w0, [x1, #0x2b]
    // 0xb46bb0: DecompressPointer r0
    //     0xb46bb0: add             x0, x0, HEAP, lsl #32
    // 0xb46bb4: r16 = Instance_Color
    //     0xb46bb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb46bb8: ldr             x16, [x16, #0x858]
    // 0xb46bbc: r30 = 12.000000
    //     0xb46bbc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46bc0: ldr             lr, [lr, #0x9e8]
    // 0xb46bc4: stp             lr, x16, [SP]
    // 0xb46bc8: mov             x1, x0
    // 0xb46bcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46bcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46bd0: ldr             x4, [x4, #0x9b8]
    // 0xb46bd4: r0 = copyWith()
    //     0xb46bd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46bd8: stur            x0, [fp, #-0x20]
    // 0xb46bdc: r0 = Text()
    //     0xb46bdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46be0: mov             x1, x0
    // 0xb46be4: r0 = "Resend OTP "
    //     0xb46be4: add             x0, PP, #0x53, lsl #12  ; [pp+0x53f40] "Resend OTP "
    //     0xb46be8: ldr             x0, [x0, #0xf40]
    // 0xb46bec: stur            x1, [fp, #-0x28]
    // 0xb46bf0: StoreField: r1->field_b = r0
    //     0xb46bf0: stur            w0, [x1, #0xb]
    // 0xb46bf4: ldur            x0, [fp, #-0x20]
    // 0xb46bf8: StoreField: r1->field_13 = r0
    //     0xb46bf8: stur            w0, [x1, #0x13]
    // 0xb46bfc: r0 = InkWell()
    //     0xb46bfc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb46c00: mov             x2, x0
    // 0xb46c04: ldur            x0, [fp, #-0x28]
    // 0xb46c08: stur            x2, [fp, #-0x20]
    // 0xb46c0c: StoreField: r2->field_b = r0
    //     0xb46c0c: stur            w0, [x2, #0xb]
    // 0xb46c10: ldur            x0, [fp, #-0x10]
    // 0xb46c14: StoreField: r2->field_f = r0
    //     0xb46c14: stur            w0, [x2, #0xf]
    // 0xb46c18: r0 = true
    //     0xb46c18: add             x0, NULL, #0x20  ; true
    // 0xb46c1c: StoreField: r2->field_43 = r0
    //     0xb46c1c: stur            w0, [x2, #0x43]
    // 0xb46c20: r1 = Instance_BoxShape
    //     0xb46c20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb46c24: ldr             x1, [x1, #0x80]
    // 0xb46c28: StoreField: r2->field_47 = r1
    //     0xb46c28: stur            w1, [x2, #0x47]
    // 0xb46c2c: StoreField: r2->field_6f = r0
    //     0xb46c2c: stur            w0, [x2, #0x6f]
    // 0xb46c30: r1 = false
    //     0xb46c30: add             x1, NULL, #0x30  ; false
    // 0xb46c34: StoreField: r2->field_73 = r1
    //     0xb46c34: stur            w1, [x2, #0x73]
    // 0xb46c38: StoreField: r2->field_83 = r0
    //     0xb46c38: stur            w0, [x2, #0x83]
    // 0xb46c3c: StoreField: r2->field_7b = r1
    //     0xb46c3c: stur            w1, [x2, #0x7b]
    // 0xb46c40: ldur            x0, [fp, #-8]
    // 0xb46c44: tbnz            w0, #4, #0xb46c50
    // 0xb46c48: r3 = ""
    //     0xb46c48: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb46c4c: b               #0xb46c58
    // 0xb46c50: r3 = "in "
    //     0xb46c50: add             x3, PP, #0x53, lsl #12  ; [pp+0x53f48] "in "
    //     0xb46c54: ldr             x3, [x3, #0xf48]
    // 0xb46c58: ldr             x1, [fp, #0x18]
    // 0xb46c5c: stur            x3, [fp, #-0x10]
    // 0xb46c60: r0 = of()
    //     0xb46c60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46c64: LoadField: r1 = r0->field_87
    //     0xb46c64: ldur            w1, [x0, #0x87]
    // 0xb46c68: DecompressPointer r1
    //     0xb46c68: add             x1, x1, HEAP, lsl #32
    // 0xb46c6c: LoadField: r0 = r1->field_2b
    //     0xb46c6c: ldur            w0, [x1, #0x2b]
    // 0xb46c70: DecompressPointer r0
    //     0xb46c70: add             x0, x0, HEAP, lsl #32
    // 0xb46c74: stur            x0, [fp, #-0x28]
    // 0xb46c78: r1 = Instance_Color
    //     0xb46c78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb46c7c: d0 = 0.700000
    //     0xb46c7c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb46c80: ldr             d0, [x17, #0xf48]
    // 0xb46c84: r0 = withOpacity()
    //     0xb46c84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb46c88: r16 = 12.000000
    //     0xb46c88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46c8c: ldr             x16, [x16, #0x9e8]
    // 0xb46c90: stp             x16, x0, [SP]
    // 0xb46c94: ldur            x1, [fp, #-0x28]
    // 0xb46c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46c9c: ldr             x4, [x4, #0x9b8]
    // 0xb46ca0: r0 = copyWith()
    //     0xb46ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46ca4: stur            x0, [fp, #-0x28]
    // 0xb46ca8: r0 = Text()
    //     0xb46ca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46cac: mov             x3, x0
    // 0xb46cb0: ldur            x0, [fp, #-0x10]
    // 0xb46cb4: stur            x3, [fp, #-0x38]
    // 0xb46cb8: StoreField: r3->field_b = r0
    //     0xb46cb8: stur            w0, [x3, #0xb]
    // 0xb46cbc: ldur            x0, [fp, #-0x28]
    // 0xb46cc0: StoreField: r3->field_13 = r0
    //     0xb46cc0: stur            w0, [x3, #0x13]
    // 0xb46cc4: ldur            x0, [fp, #-8]
    // 0xb46cc8: tbnz            w0, #4, #0xb46cd8
    // 0xb46ccc: mov             x0, x3
    // 0xb46cd0: r4 = ""
    //     0xb46cd0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb46cd4: b               #0xb46d10
    // 0xb46cd8: ldur            x0, [fp, #-0x30]
    // 0xb46cdc: r1 = Null
    //     0xb46cdc: mov             x1, NULL
    // 0xb46ce0: r2 = 4
    //     0xb46ce0: movz            x2, #0x4
    // 0xb46ce4: r0 = AllocateArray()
    //     0xb46ce4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb46ce8: mov             x1, x0
    // 0xb46cec: ldur            x0, [fp, #-0x30]
    // 0xb46cf0: StoreField: r1->field_f = r0
    //     0xb46cf0: stur            w0, [x1, #0xf]
    // 0xb46cf4: r16 = "s"
    //     0xb46cf4: add             x16, PP, #0xc, lsl #12  ; [pp+0xc728] "s"
    //     0xb46cf8: ldr             x16, [x16, #0x728]
    // 0xb46cfc: StoreField: r1->field_13 = r16
    //     0xb46cfc: stur            w16, [x1, #0x13]
    // 0xb46d00: str             x1, [SP]
    // 0xb46d04: r0 = _interpolate()
    //     0xb46d04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb46d08: mov             x4, x0
    // 0xb46d0c: ldur            x0, [fp, #-0x38]
    // 0xb46d10: ldur            x3, [fp, #-0x18]
    // 0xb46d14: ldur            x2, [fp, #-0x20]
    // 0xb46d18: ldr             x1, [fp, #0x18]
    // 0xb46d1c: stur            x4, [fp, #-8]
    // 0xb46d20: r0 = of()
    //     0xb46d20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb46d24: LoadField: r1 = r0->field_87
    //     0xb46d24: ldur            w1, [x0, #0x87]
    // 0xb46d28: DecompressPointer r1
    //     0xb46d28: add             x1, x1, HEAP, lsl #32
    // 0xb46d2c: LoadField: r0 = r1->field_2b
    //     0xb46d2c: ldur            w0, [x1, #0x2b]
    // 0xb46d30: DecompressPointer r0
    //     0xb46d30: add             x0, x0, HEAP, lsl #32
    // 0xb46d34: r16 = Instance_Color
    //     0xb46d34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb46d38: ldr             x16, [x16, #0x50]
    // 0xb46d3c: r30 = 12.000000
    //     0xb46d3c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb46d40: ldr             lr, [lr, #0x9e8]
    // 0xb46d44: stp             lr, x16, [SP]
    // 0xb46d48: mov             x1, x0
    // 0xb46d4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb46d4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb46d50: ldr             x4, [x4, #0x9b8]
    // 0xb46d54: r0 = copyWith()
    //     0xb46d54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb46d58: stur            x0, [fp, #-0x10]
    // 0xb46d5c: r0 = Text()
    //     0xb46d5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb46d60: mov             x3, x0
    // 0xb46d64: ldur            x0, [fp, #-8]
    // 0xb46d68: stur            x3, [fp, #-0x28]
    // 0xb46d6c: StoreField: r3->field_b = r0
    //     0xb46d6c: stur            w0, [x3, #0xb]
    // 0xb46d70: ldur            x0, [fp, #-0x10]
    // 0xb46d74: StoreField: r3->field_13 = r0
    //     0xb46d74: stur            w0, [x3, #0x13]
    // 0xb46d78: r1 = Null
    //     0xb46d78: mov             x1, NULL
    // 0xb46d7c: r2 = 8
    //     0xb46d7c: movz            x2, #0x8
    // 0xb46d80: r0 = AllocateArray()
    //     0xb46d80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb46d84: mov             x2, x0
    // 0xb46d88: ldur            x0, [fp, #-0x18]
    // 0xb46d8c: stur            x2, [fp, #-8]
    // 0xb46d90: StoreField: r2->field_f = r0
    //     0xb46d90: stur            w0, [x2, #0xf]
    // 0xb46d94: ldur            x0, [fp, #-0x20]
    // 0xb46d98: StoreField: r2->field_13 = r0
    //     0xb46d98: stur            w0, [x2, #0x13]
    // 0xb46d9c: ldur            x0, [fp, #-0x38]
    // 0xb46da0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb46da0: stur            w0, [x2, #0x17]
    // 0xb46da4: ldur            x0, [fp, #-0x28]
    // 0xb46da8: StoreField: r2->field_1b = r0
    //     0xb46da8: stur            w0, [x2, #0x1b]
    // 0xb46dac: r1 = <Widget>
    //     0xb46dac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb46db0: r0 = AllocateGrowableArray()
    //     0xb46db0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb46db4: mov             x1, x0
    // 0xb46db8: ldur            x0, [fp, #-8]
    // 0xb46dbc: stur            x1, [fp, #-0x10]
    // 0xb46dc0: StoreField: r1->field_f = r0
    //     0xb46dc0: stur            w0, [x1, #0xf]
    // 0xb46dc4: r0 = 8
    //     0xb46dc4: movz            x0, #0x8
    // 0xb46dc8: StoreField: r1->field_b = r0
    //     0xb46dc8: stur            w0, [x1, #0xb]
    // 0xb46dcc: r0 = Row()
    //     0xb46dcc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb46dd0: r1 = Instance_Axis
    //     0xb46dd0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb46dd4: StoreField: r0->field_f = r1
    //     0xb46dd4: stur            w1, [x0, #0xf]
    // 0xb46dd8: r1 = Instance_MainAxisAlignment
    //     0xb46dd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb46ddc: ldr             x1, [x1, #0xa08]
    // 0xb46de0: StoreField: r0->field_13 = r1
    //     0xb46de0: stur            w1, [x0, #0x13]
    // 0xb46de4: r1 = Instance_MainAxisSize
    //     0xb46de4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb46de8: ldr             x1, [x1, #0xa10]
    // 0xb46dec: ArrayStore: r0[0] = r1  ; List_4
    //     0xb46dec: stur            w1, [x0, #0x17]
    // 0xb46df0: r1 = Instance_CrossAxisAlignment
    //     0xb46df0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb46df4: ldr             x1, [x1, #0xa18]
    // 0xb46df8: StoreField: r0->field_1b = r1
    //     0xb46df8: stur            w1, [x0, #0x1b]
    // 0xb46dfc: r1 = Instance_VerticalDirection
    //     0xb46dfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb46e00: ldr             x1, [x1, #0xa20]
    // 0xb46e04: StoreField: r0->field_23 = r1
    //     0xb46e04: stur            w1, [x0, #0x23]
    // 0xb46e08: r1 = Instance_Clip
    //     0xb46e08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb46e0c: ldr             x1, [x1, #0x38]
    // 0xb46e10: StoreField: r0->field_2b = r1
    //     0xb46e10: stur            w1, [x0, #0x2b]
    // 0xb46e14: StoreField: r0->field_2f = rZR
    //     0xb46e14: stur            xzr, [x0, #0x2f]
    // 0xb46e18: ldur            x1, [fp, #-0x10]
    // 0xb46e1c: StoreField: r0->field_b = r1
    //     0xb46e1c: stur            w1, [x0, #0xb]
    // 0xb46e20: LeaveFrame
    //     0xb46e20: mov             SP, fp
    //     0xb46e24: ldp             fp, lr, [SP], #0x10
    // 0xb46e28: ret
    //     0xb46e28: ret             
    // 0xb46e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb46e2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb46e30: b               #0xb4680c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb46e34, size: 0x48
    // 0xb46e34: EnterFrame
    //     0xb46e34: stp             fp, lr, [SP, #-0x10]!
    //     0xb46e38: mov             fp, SP
    // 0xb46e3c: ldr             x0, [fp, #0x10]
    // 0xb46e40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb46e40: ldur            w1, [x0, #0x17]
    // 0xb46e44: DecompressPointer r1
    //     0xb46e44: add             x1, x1, HEAP, lsl #32
    // 0xb46e48: CheckStackOverflow
    //     0xb46e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb46e4c: cmp             SP, x16
    //     0xb46e50: b.ls            #0xb46e74
    // 0xb46e54: LoadField: r0 = r1->field_f
    //     0xb46e54: ldur            w0, [x1, #0xf]
    // 0xb46e58: DecompressPointer r0
    //     0xb46e58: add             x0, x0, HEAP, lsl #32
    // 0xb46e5c: mov             x1, x0
    // 0xb46e60: r0 = _resendCode()
    //     0xb46e60: bl              #0xb46e7c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_resendCode
    // 0xb46e64: r0 = Null
    //     0xb46e64: mov             x0, NULL
    // 0xb46e68: LeaveFrame
    //     0xb46e68: mov             SP, fp
    //     0xb46e6c: ldp             fp, lr, [SP], #0x10
    // 0xb46e70: ret
    //     0xb46e70: ret             
    // 0xb46e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb46e74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb46e78: b               #0xb46e54
  }
  _ _resendCode(/* No info */) {
    // ** addr: 0xb46e7c, size: 0x64
    // 0xb46e7c: EnterFrame
    //     0xb46e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xb46e80: mov             fp, SP
    // 0xb46e84: AllocStack(0x8)
    //     0xb46e84: sub             SP, SP, #8
    // 0xb46e88: SetupParameters(_CheckoutOtpWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xb46e88: stur            x1, [fp, #-8]
    // 0xb46e8c: CheckStackOverflow
    //     0xb46e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb46e90: cmp             SP, x16
    //     0xb46e94: b.ls            #0xb46ed8
    // 0xb46e98: r1 = 1
    //     0xb46e98: movz            x1, #0x1
    // 0xb46e9c: r0 = AllocateContext()
    //     0xb46e9c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb46ea0: mov             x1, x0
    // 0xb46ea4: ldur            x0, [fp, #-8]
    // 0xb46ea8: StoreField: r1->field_f = r0
    //     0xb46ea8: stur            w0, [x1, #0xf]
    // 0xb46eac: mov             x2, x1
    // 0xb46eb0: r1 = Function '<anonymous closure>':.
    //     0xb46eb0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56650] AnonymousClosure: (0xb46ee0), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_resendCode (0xb46e7c)
    //     0xb46eb4: ldr             x1, [x1, #0x650]
    // 0xb46eb8: r0 = AllocateClosure()
    //     0xb46eb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb46ebc: ldur            x1, [fp, #-8]
    // 0xb46ec0: mov             x2, x0
    // 0xb46ec4: r0 = setState()
    //     0xb46ec4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb46ec8: r0 = Null
    //     0xb46ec8: mov             x0, NULL
    // 0xb46ecc: LeaveFrame
    //     0xb46ecc: mov             SP, fp
    //     0xb46ed0: ldp             fp, lr, [SP], #0x10
    // 0xb46ed4: ret
    //     0xb46ed4: ret             
    // 0xb46ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb46ed8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb46edc: b               #0xb46e98
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb46ee0, size: 0xe0
    // 0xb46ee0: EnterFrame
    //     0xb46ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xb46ee4: mov             fp, SP
    // 0xb46ee8: AllocStack(0x18)
    //     0xb46ee8: sub             SP, SP, #0x18
    // 0xb46eec: SetupParameters()
    //     0xb46eec: add             x0, NULL, #0x30  ; false
    //     0xb46ef0: ldr             x1, [fp, #0x10]
    //     0xb46ef4: ldur            w2, [x1, #0x17]
    //     0xb46ef8: add             x2, x2, HEAP, lsl #32
    //     0xb46efc: stur            x2, [fp, #-8]
    // 0xb46eec: r0 = false
    // 0xb46f00: CheckStackOverflow
    //     0xb46f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb46f04: cmp             SP, x16
    //     0xb46f08: b.ls            #0xb46fb4
    // 0xb46f0c: LoadField: r1 = r2->field_f
    //     0xb46f0c: ldur            w1, [x2, #0xf]
    // 0xb46f10: DecompressPointer r1
    //     0xb46f10: add             x1, x1, HEAP, lsl #32
    // 0xb46f14: StoreField: r1->field_13 = r0
    //     0xb46f14: stur            w0, [x1, #0x13]
    // 0xb46f18: LoadField: r0 = r1->field_b
    //     0xb46f18: ldur            w0, [x1, #0xb]
    // 0xb46f1c: DecompressPointer r0
    //     0xb46f1c: add             x0, x0, HEAP, lsl #32
    // 0xb46f20: cmp             w0, NULL
    // 0xb46f24: b.eq            #0xb46fbc
    // 0xb46f28: LoadField: r1 = r0->field_f
    //     0xb46f28: ldur            w1, [x0, #0xf]
    // 0xb46f2c: DecompressPointer r1
    //     0xb46f2c: add             x1, x1, HEAP, lsl #32
    // 0xb46f30: str             x1, [SP]
    // 0xb46f34: r4 = 0
    //     0xb46f34: movz            x4, #0
    // 0xb46f38: ldr             x0, [SP]
    // 0xb46f3c: r16 = UnlinkedCall_0x613b5c
    //     0xb46f3c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56658] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb46f40: add             x16, x16, #0x658
    // 0xb46f44: ldp             x5, lr, [x16]
    // 0xb46f48: blr             lr
    // 0xb46f4c: ldur            x0, [fp, #-8]
    // 0xb46f50: LoadField: r3 = r0->field_f
    //     0xb46f50: ldur            w3, [x0, #0xf]
    // 0xb46f54: DecompressPointer r3
    //     0xb46f54: add             x3, x3, HEAP, lsl #32
    // 0xb46f58: stur            x3, [fp, #-0x10]
    // 0xb46f5c: r1 = Function '<anonymous closure>':.
    //     0xb46f5c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56668] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xb46f60: ldr             x1, [x1, #0x668]
    // 0xb46f64: r2 = Null
    //     0xb46f64: mov             x2, NULL
    // 0xb46f68: r0 = AllocateClosure()
    //     0xb46f68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb46f6c: mov             x2, x0
    // 0xb46f70: r1 = <int>
    //     0xb46f70: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb46f74: r0 = Stream.periodic()
    //     0xb46f74: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xb46f78: mov             x1, x0
    // 0xb46f7c: r2 = 30
    //     0xb46f7c: movz            x2, #0x1e
    // 0xb46f80: r0 = take()
    //     0xb46f80: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xb46f84: ldur            x1, [fp, #-0x10]
    // 0xb46f88: StoreField: r1->field_1b = r0
    //     0xb46f88: stur            w0, [x1, #0x1b]
    //     0xb46f8c: ldurb           w16, [x1, #-1]
    //     0xb46f90: ldurb           w17, [x0, #-1]
    //     0xb46f94: and             x16, x17, x16, lsr #2
    //     0xb46f98: tst             x16, HEAP, lsr #32
    //     0xb46f9c: b.eq            #0xb46fa4
    //     0xb46fa0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb46fa4: r0 = Null
    //     0xb46fa4: mov             x0, NULL
    // 0xb46fa8: LeaveFrame
    //     0xb46fa8: mov             SP, fp
    //     0xb46fac: ldp             fp, lr, [SP], #0x10
    // 0xb46fb0: ret
    //     0xb46fb0: ret             
    // 0xb46fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb46fb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb46fb8: b               #0xb46f0c
    // 0xb46fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb46fbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb46fc0, size: 0x10c
    // 0xb46fc0: EnterFrame
    //     0xb46fc0: stp             fp, lr, [SP, #-0x10]!
    //     0xb46fc4: mov             fp, SP
    // 0xb46fc8: AllocStack(0x28)
    //     0xb46fc8: sub             SP, SP, #0x28
    // 0xb46fcc: SetupParameters()
    //     0xb46fcc: ldr             x0, [fp, #0x18]
    //     0xb46fd0: ldur            w1, [x0, #0x17]
    //     0xb46fd4: add             x1, x1, HEAP, lsl #32
    //     0xb46fd8: stur            x1, [fp, #-8]
    // 0xb46fdc: CheckStackOverflow
    //     0xb46fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb46fe0: cmp             SP, x16
    //     0xb46fe4: b.ls            #0xb470c0
    // 0xb46fe8: LoadField: r2 = r1->field_f
    //     0xb46fe8: ldur            w2, [x1, #0xf]
    // 0xb46fec: DecompressPointer r2
    //     0xb46fec: add             x2, x2, HEAP, lsl #32
    // 0xb46ff0: ldr             x3, [fp, #0x10]
    // 0xb46ff4: cmp             w3, NULL
    // 0xb46ff8: b.ne            #0xb47004
    // 0xb46ffc: r0 = ""
    //     0xb46ffc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb47000: b               #0xb47008
    // 0xb47004: mov             x0, x3
    // 0xb47008: ArrayStore: r2[0] = r0  ; List_4
    //     0xb47008: stur            w0, [x2, #0x17]
    //     0xb4700c: ldurb           w16, [x2, #-1]
    //     0xb47010: ldurb           w17, [x0, #-1]
    //     0xb47014: and             x16, x17, x16, lsr #2
    //     0xb47018: tst             x16, HEAP, lsr #32
    //     0xb4701c: b.eq            #0xb47024
    //     0xb47020: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb47024: cmp             w3, NULL
    // 0xb47028: b.eq            #0xb470b0
    // 0xb4702c: LoadField: r0 = r3->field_7
    //     0xb4702c: ldur            w0, [x3, #7]
    // 0xb47030: cmp             w0, #8
    // 0xb47034: b.ne            #0xb470b0
    // 0xb47038: LoadField: r0 = r2->field_b
    //     0xb47038: ldur            w0, [x2, #0xb]
    // 0xb4703c: DecompressPointer r0
    //     0xb4703c: add             x0, x0, HEAP, lsl #32
    // 0xb47040: cmp             w0, NULL
    // 0xb47044: b.eq            #0xb470c8
    // 0xb47048: LoadField: r2 = r0->field_b
    //     0xb47048: ldur            w2, [x0, #0xb]
    // 0xb4704c: DecompressPointer r2
    //     0xb4704c: add             x2, x2, HEAP, lsl #32
    // 0xb47050: stp             x3, x2, [SP, #8]
    // 0xb47054: r16 = true
    //     0xb47054: add             x16, NULL, #0x20  ; true
    // 0xb47058: str             x16, [SP]
    // 0xb4705c: r4 = 0
    //     0xb4705c: movz            x4, #0
    // 0xb47060: ldr             x0, [SP, #0x10]
    // 0xb47064: r16 = UnlinkedCall_0x613b5c
    //     0xb47064: add             x16, PP, #0x56, lsl #12  ; [pp+0x56670] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb47068: add             x16, x16, #0x670
    // 0xb4706c: ldp             x5, lr, [x16]
    // 0xb47070: blr             lr
    // 0xb47074: ldur            x0, [fp, #-8]
    // 0xb47078: LoadField: r1 = r0->field_13
    //     0xb47078: ldur            w1, [x0, #0x13]
    // 0xb4707c: DecompressPointer r1
    //     0xb4707c: add             x1, x1, HEAP, lsl #32
    // 0xb47080: r0 = of()
    //     0xb47080: bl              #0x81ef68  ; [package:flutter/src/widgets/focus_scope.dart] FocusScope::of
    // 0xb47084: stur            x0, [fp, #-8]
    // 0xb47088: r0 = FocusNode()
    //     0xb47088: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0xb4708c: mov             x1, x0
    // 0xb47090: stur            x0, [fp, #-0x10]
    // 0xb47094: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb47094: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb47098: r0 = FocusNode()
    //     0xb47098: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0xb4709c: ldur            x16, [fp, #-0x10]
    // 0xb470a0: str             x16, [SP]
    // 0xb470a4: ldur            x1, [fp, #-8]
    // 0xb470a8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xb470a8: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xb470ac: r0 = requestFocus()
    //     0xb470ac: bl              #0x6595f4  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::requestFocus
    // 0xb470b0: r0 = Null
    //     0xb470b0: mov             x0, NULL
    // 0xb470b4: LeaveFrame
    //     0xb470b4: mov             SP, fp
    //     0xb470b8: ldp             fp, lr, [SP], #0x10
    // 0xb470bc: ret
    //     0xb470bc: ret             
    // 0xb470c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb470c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb470c4: b               #0xb46fe8
    // 0xb470c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb470c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0xb470cc, size: 0xa8
    // 0xb470cc: EnterFrame
    //     0xb470cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb470d0: mov             fp, SP
    // 0xb470d4: AllocStack(0x18)
    //     0xb470d4: sub             SP, SP, #0x18
    // 0xb470d8: SetupParameters()
    //     0xb470d8: ldr             x0, [fp, #0x18]
    //     0xb470dc: ldur            w1, [x0, #0x17]
    //     0xb470e0: add             x1, x1, HEAP, lsl #32
    // 0xb470e4: CheckStackOverflow
    //     0xb470e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb470e8: cmp             SP, x16
    //     0xb470ec: b.ls            #0xb47168
    // 0xb470f0: LoadField: r2 = r1->field_f
    //     0xb470f0: ldur            w2, [x1, #0xf]
    // 0xb470f4: DecompressPointer r2
    //     0xb470f4: add             x2, x2, HEAP, lsl #32
    // 0xb470f8: ldr             x0, [fp, #0x10]
    // 0xb470fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb470fc: stur            w0, [x2, #0x17]
    //     0xb47100: ldurb           w16, [x2, #-1]
    //     0xb47104: ldurb           w17, [x0, #-1]
    //     0xb47108: and             x16, x17, x16, lsr #2
    //     0xb4710c: tst             x16, HEAP, lsr #32
    //     0xb47110: b.eq            #0xb47118
    //     0xb47114: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb47118: LoadField: r0 = r2->field_b
    //     0xb47118: ldur            w0, [x2, #0xb]
    // 0xb4711c: DecompressPointer r0
    //     0xb4711c: add             x0, x0, HEAP, lsl #32
    // 0xb47120: cmp             w0, NULL
    // 0xb47124: b.eq            #0xb47170
    // 0xb47128: LoadField: r1 = r0->field_b
    //     0xb47128: ldur            w1, [x0, #0xb]
    // 0xb4712c: DecompressPointer r1
    //     0xb4712c: add             x1, x1, HEAP, lsl #32
    // 0xb47130: ldr             x16, [fp, #0x10]
    // 0xb47134: stp             x16, x1, [SP, #8]
    // 0xb47138: r16 = true
    //     0xb47138: add             x16, NULL, #0x20  ; true
    // 0xb4713c: str             x16, [SP]
    // 0xb47140: r4 = 0
    //     0xb47140: movz            x4, #0
    // 0xb47144: ldr             x0, [SP, #0x10]
    // 0xb47148: r16 = UnlinkedCall_0x613b5c
    //     0xb47148: add             x16, PP, #0x56, lsl #12  ; [pp+0x56680] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4714c: add             x16, x16, #0x680
    // 0xb47150: ldp             x5, lr, [x16]
    // 0xb47154: blr             lr
    // 0xb47158: r0 = Null
    //     0xb47158: mov             x0, NULL
    // 0xb4715c: LeaveFrame
    //     0xb4715c: mov             SP, fp
    //     0xb47160: ldp             fp, lr, [SP], #0x10
    // 0xb47164: ret
    //     0xb47164: ret             
    // 0xb47168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb47168: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4716c: b               #0xb470f0
    // 0xb47170: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb47170: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _CheckoutOtpWidgetState(/* No info */) {
    // ** addr: 0xc7eb70, size: 0x98
    // 0xc7eb70: EnterFrame
    //     0xc7eb70: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eb74: mov             fp, SP
    // 0xc7eb78: AllocStack(0x8)
    //     0xc7eb78: sub             SP, SP, #8
    // 0xc7eb7c: r2 = false
    //     0xc7eb7c: add             x2, NULL, #0x30  ; false
    // 0xc7eb80: r0 = ""
    //     0xc7eb80: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc7eb84: mov             x3, x1
    // 0xc7eb88: stur            x1, [fp, #-8]
    // 0xc7eb8c: CheckStackOverflow
    //     0xc7eb8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7eb90: cmp             SP, x16
    //     0xc7eb94: b.ls            #0xc7ec00
    // 0xc7eb98: StoreField: r3->field_13 = r2
    //     0xc7eb98: stur            w2, [x3, #0x13]
    // 0xc7eb9c: ArrayStore: r3[0] = r0  ; List_4
    //     0xc7eb9c: stur            w0, [x3, #0x17]
    // 0xc7eba0: r1 = Function '<anonymous closure>':.
    //     0xc7eba0: add             x1, PP, #0x48, lsl #12  ; [pp+0x489e8] AnonymousClosure: (0xa0a694), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState (0xa0a6d8)
    //     0xc7eba4: ldr             x1, [x1, #0x9e8]
    // 0xc7eba8: r2 = Null
    //     0xc7eba8: mov             x2, NULL
    // 0xc7ebac: r0 = AllocateClosure()
    //     0xc7ebac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc7ebb0: mov             x2, x0
    // 0xc7ebb4: r1 = <int>
    //     0xc7ebb4: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xc7ebb8: r0 = Stream.periodic()
    //     0xc7ebb8: bl              #0xa0a0f8  ; [dart:async] Stream::Stream.periodic
    // 0xc7ebbc: mov             x1, x0
    // 0xc7ebc0: r2 = 30
    //     0xc7ebc0: movz            x2, #0x1e
    // 0xc7ebc4: r0 = take()
    //     0xc7ebc4: bl              #0xa0a080  ; [dart:async] Stream::take
    // 0xc7ebc8: ldur            x1, [fp, #-8]
    // 0xc7ebcc: StoreField: r1->field_1b = r0
    //     0xc7ebcc: stur            w0, [x1, #0x1b]
    //     0xc7ebd0: ldurb           w16, [x1, #-1]
    //     0xc7ebd4: ldurb           w17, [x0, #-1]
    //     0xc7ebd8: and             x16, x17, x16, lsr #2
    //     0xc7ebdc: tst             x16, HEAP, lsr #32
    //     0xc7ebe0: b.eq            #0xc7ebe8
    //     0xc7ebe4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7ebe8: r1 = Null
    //     0xc7ebe8: mov             x1, NULL
    // 0xc7ebec: r0 = SmsAutoFill()
    //     0xc7ebec: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0xc7ebf0: r0 = Null
    //     0xc7ebf0: mov             x0, NULL
    // 0xc7ebf4: LeaveFrame
    //     0xc7ebf4: mov             SP, fp
    //     0xc7ebf8: ldp             fp, lr, [SP], #0x10
    // 0xc7ebfc: ret
    //     0xc7ebfc: ret             
    // 0xc7ec00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7ec00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7ec04: b               #0xc7eb98
  }
}

// class id: 4103, size: 0x20, field offset: 0xc
//   const constructor, 
class CheckoutOtpWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7eb28, size: 0x48
    // 0xc7eb28: EnterFrame
    //     0xc7eb28: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eb2c: mov             fp, SP
    // 0xc7eb30: AllocStack(0x8)
    //     0xc7eb30: sub             SP, SP, #8
    // 0xc7eb34: CheckStackOverflow
    //     0xc7eb34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7eb38: cmp             SP, x16
    //     0xc7eb3c: b.ls            #0xc7eb68
    // 0xc7eb40: r1 = <CheckoutOtpWidget>
    //     0xc7eb40: add             x1, PP, #0x48, lsl #12  ; [pp+0x489e0] TypeArguments: <CheckoutOtpWidget>
    //     0xc7eb44: ldr             x1, [x1, #0x9e0]
    // 0xc7eb48: r0 = _CheckoutOtpWidgetState()
    //     0xc7eb48: bl              #0xc7ec08  ; Allocate_CheckoutOtpWidgetStateStub -> _CheckoutOtpWidgetState (size=0x20)
    // 0xc7eb4c: mov             x1, x0
    // 0xc7eb50: stur            x0, [fp, #-8]
    // 0xc7eb54: r0 = _CheckoutOtpWidgetState()
    //     0xc7eb54: bl              #0xc7eb70  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_otp_widget.dart] _CheckoutOtpWidgetState::_CheckoutOtpWidgetState
    // 0xc7eb58: ldur            x0, [fp, #-8]
    // 0xc7eb5c: LeaveFrame
    //     0xc7eb5c: mov             SP, fp
    //     0xc7eb60: ldp             fp, lr, [SP], #0x10
    // 0xc7eb64: ret
    //     0xc7eb64: ret             
    // 0xc7eb68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7eb68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7eb6c: b               #0xc7eb40
  }
}
