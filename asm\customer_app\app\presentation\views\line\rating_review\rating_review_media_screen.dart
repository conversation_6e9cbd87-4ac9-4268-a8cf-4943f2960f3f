// lib: , url: package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart

// class id: 1049579, size: 0x8
class :: {
}

// class id: 4518, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewMediaScreen extends BaseView<dynamic> {

  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0x8fc578, size: 0x13c
    // 0x8fc578: EnterFrame
    //     0x8fc578: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc57c: mov             fp, SP
    // 0x8fc580: AllocStack(0x18)
    //     0x8fc580: sub             SP, SP, #0x18
    // 0x8fc584: CheckStackOverflow
    //     0x8fc584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc588: cmp             SP, x16
    //     0x8fc58c: b.ls            #0x8fc69c
    // 0x8fc590: ldr             x0, [fp, #0x10]
    // 0x8fc594: LoadField: r1 = r0->field_b
    //     0x8fc594: ldur            w1, [x0, #0xb]
    // 0x8fc598: DecompressPointer r1
    //     0x8fc598: add             x1, x1, HEAP, lsl #32
    // 0x8fc59c: cmp             w1, NULL
    // 0x8fc5a0: b.eq            #0x8fc5bc
    // 0x8fc5a4: LoadField: r2 = r0->field_f
    //     0x8fc5a4: ldur            x2, [x0, #0xf]
    // 0x8fc5a8: r0 = LoadInt32Instr(r1)
    //     0x8fc5a8: sbfx            x0, x1, #1, #0x1f
    //     0x8fc5ac: tbz             w1, #0, #0x8fc5b4
    //     0x8fc5b0: ldur            x0, [x1, #7]
    // 0x8fc5b4: cmp             x2, x0
    // 0x8fc5b8: b.le            #0x8fc5c4
    // 0x8fc5bc: r0 = Null
    //     0x8fc5bc: mov             x0, NULL
    // 0x8fc5c0: b               #0x8fc5f8
    // 0x8fc5c4: scvtf           d0, x2
    // 0x8fc5c8: scvtf           d1, x0
    // 0x8fc5cc: fdiv            d2, d0, d1
    // 0x8fc5d0: r0 = inline_Allocate_Double()
    //     0x8fc5d0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8fc5d4: add             x0, x0, #0x10
    //     0x8fc5d8: cmp             x1, x0
    //     0x8fc5dc: b.ls            #0x8fc6a4
    //     0x8fc5e0: str             x0, [THR, #0x50]  ; THR::top
    //     0x8fc5e4: sub             x0, x0, #0xf
    //     0x8fc5e8: movz            x1, #0xe15c
    //     0x8fc5ec: movk            x1, #0x3, lsl #16
    //     0x8fc5f0: stur            x1, [x0, #-1]
    // 0x8fc5f4: StoreField: r0->field_7 = d2
    //     0x8fc5f4: stur            d2, [x0, #7]
    // 0x8fc5f8: ldr             x1, [fp, #0x20]
    // 0x8fc5fc: stur            x0, [fp, #-8]
    // 0x8fc600: r0 = of()
    //     0x8fc600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x8fc604: LoadField: r1 = r0->field_5b
    //     0x8fc604: ldur            w1, [x0, #0x5b]
    // 0x8fc608: DecompressPointer r1
    //     0x8fc608: add             x1, x1, HEAP, lsl #32
    // 0x8fc60c: r0 = LoadClassIdInstr(r1)
    //     0x8fc60c: ldur            x0, [x1, #-1]
    //     0x8fc610: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc614: d0 = 0.300000
    //     0x8fc614: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x8fc618: ldr             d0, [x17, #0x658]
    // 0x8fc61c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8fc61c: sub             lr, x0, #0xffa
    //     0x8fc620: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc624: blr             lr
    // 0x8fc628: stur            x0, [fp, #-0x10]
    // 0x8fc62c: r0 = CircularProgressIndicator()
    //     0x8fc62c: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0x8fc630: mov             x1, x0
    // 0x8fc634: r0 = Instance__ActivityIndicatorType
    //     0x8fc634: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0x8fc638: ldr             x0, [x0, #0x1b0]
    // 0x8fc63c: stur            x1, [fp, #-0x18]
    // 0x8fc640: StoreField: r1->field_23 = r0
    //     0x8fc640: stur            w0, [x1, #0x23]
    // 0x8fc644: ldur            x0, [fp, #-8]
    // 0x8fc648: StoreField: r1->field_b = r0
    //     0x8fc648: stur            w0, [x1, #0xb]
    // 0x8fc64c: ldur            x0, [fp, #-0x10]
    // 0x8fc650: StoreField: r1->field_13 = r0
    //     0x8fc650: stur            w0, [x1, #0x13]
    // 0x8fc654: r0 = SizedBox()
    //     0x8fc654: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x8fc658: mov             x1, x0
    // 0x8fc65c: r0 = 25.000000
    //     0x8fc65c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x8fc660: ldr             x0, [x0, #0x98]
    // 0x8fc664: stur            x1, [fp, #-8]
    // 0x8fc668: StoreField: r1->field_f = r0
    //     0x8fc668: stur            w0, [x1, #0xf]
    // 0x8fc66c: StoreField: r1->field_13 = r0
    //     0x8fc66c: stur            w0, [x1, #0x13]
    // 0x8fc670: ldur            x0, [fp, #-0x18]
    // 0x8fc674: StoreField: r1->field_b = r0
    //     0x8fc674: stur            w0, [x1, #0xb]
    // 0x8fc678: r0 = Center()
    //     0x8fc678: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x8fc67c: r1 = Instance_Alignment
    //     0x8fc67c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x8fc680: ldr             x1, [x1, #0xb10]
    // 0x8fc684: StoreField: r0->field_f = r1
    //     0x8fc684: stur            w1, [x0, #0xf]
    // 0x8fc688: ldur            x1, [fp, #-8]
    // 0x8fc68c: StoreField: r0->field_b = r1
    //     0x8fc68c: stur            w1, [x0, #0xb]
    // 0x8fc690: LeaveFrame
    //     0x8fc690: mov             SP, fp
    //     0x8fc694: ldp             fp, lr, [SP], #0x10
    // 0x8fc698: ret
    //     0x8fc698: ret             
    // 0x8fc69c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc69c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc6a0: b               #0x8fc590
    // 0x8fc6a4: SaveReg d2
    //     0x8fc6a4: str             q2, [SP, #-0x10]!
    // 0x8fc6a8: r0 = AllocateDouble()
    //     0x8fc6a8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x8fc6ac: RestoreReg d2
    //     0x8fc6ac: ldr             q2, [SP], #0x10
    // 0x8fc6b0: b               #0x8fc5f4
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x8fc6b4, size: 0x308
    // 0x8fc6b4: EnterFrame
    //     0x8fc6b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc6b8: mov             fp, SP
    // 0x8fc6bc: AllocStack(0x68)
    //     0x8fc6bc: sub             SP, SP, #0x68
    // 0x8fc6c0: SetupParameters()
    //     0x8fc6c0: ldr             x0, [fp, #0x20]
    //     0x8fc6c4: ldur            w1, [x0, #0x17]
    //     0x8fc6c8: add             x1, x1, HEAP, lsl #32
    //     0x8fc6cc: stur            x1, [fp, #-8]
    // 0x8fc6d0: CheckStackOverflow
    //     0x8fc6d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc6d4: cmp             SP, x16
    //     0x8fc6d8: b.ls            #0x8fc9b0
    // 0x8fc6dc: r1 = 3
    //     0x8fc6dc: movz            x1, #0x3
    // 0x8fc6e0: r0 = AllocateContext()
    //     0x8fc6e0: bl              #0x16f6108  ; AllocateContextStub
    // 0x8fc6e4: mov             x2, x0
    // 0x8fc6e8: ldur            x0, [fp, #-8]
    // 0x8fc6ec: stur            x2, [fp, #-0x10]
    // 0x8fc6f0: StoreField: r2->field_b = r0
    //     0x8fc6f0: stur            w0, [x2, #0xb]
    // 0x8fc6f4: ldr             x1, [fp, #0x18]
    // 0x8fc6f8: StoreField: r2->field_f = r1
    //     0x8fc6f8: stur            w1, [x2, #0xf]
    // 0x8fc6fc: ldr             x3, [fp, #0x10]
    // 0x8fc700: StoreField: r2->field_13 = r3
    //     0x8fc700: stur            w3, [x2, #0x13]
    // 0x8fc704: LoadField: r1 = r0->field_f
    //     0x8fc704: ldur            w1, [x0, #0xf]
    // 0x8fc708: DecompressPointer r1
    //     0x8fc708: add             x1, x1, HEAP, lsl #32
    // 0x8fc70c: r0 = controller()
    //     0x8fc70c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8fc710: LoadField: r1 = r0->field_4f
    //     0x8fc710: ldur            w1, [x0, #0x4f]
    // 0x8fc714: DecompressPointer r1
    //     0x8fc714: add             x1, x1, HEAP, lsl #32
    // 0x8fc718: r0 = value()
    //     0x8fc718: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8fc71c: LoadField: r1 = r0->field_b
    //     0x8fc71c: ldur            w1, [x0, #0xb]
    // 0x8fc720: DecompressPointer r1
    //     0x8fc720: add             x1, x1, HEAP, lsl #32
    // 0x8fc724: cmp             w1, NULL
    // 0x8fc728: b.ne            #0x8fc734
    // 0x8fc72c: r2 = Null
    //     0x8fc72c: mov             x2, NULL
    // 0x8fc730: b               #0x8fc774
    // 0x8fc734: LoadField: r0 = r1->field_b
    //     0x8fc734: ldur            w0, [x1, #0xb]
    // 0x8fc738: DecompressPointer r0
    //     0x8fc738: add             x0, x0, HEAP, lsl #32
    // 0x8fc73c: stur            x0, [fp, #-8]
    // 0x8fc740: r1 = Function '<anonymous closure>':.
    //     0x8fc740: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d8] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x8fc744: ldr             x1, [x1, #0xd8]
    // 0x8fc748: r2 = Null
    //     0x8fc748: mov             x2, NULL
    // 0x8fc74c: r0 = AllocateClosure()
    //     0x8fc74c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fc750: ldur            x16, [fp, #-8]
    // 0x8fc754: stp             x16, NULL, [SP, #8]
    // 0x8fc758: str             x0, [SP]
    // 0x8fc75c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc75c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc760: r0 = expand()
    //     0x8fc760: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x8fc764: mov             x1, x0
    // 0x8fc768: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fc768: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fc76c: r0 = toList()
    //     0x8fc76c: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x8fc770: mov             x2, x0
    // 0x8fc774: cmp             w2, NULL
    // 0x8fc778: b.ne            #0x8fc784
    // 0x8fc77c: r1 = Null
    //     0x8fc77c: mov             x1, NULL
    // 0x8fc780: b               #0x8fc7bc
    // 0x8fc784: ldr             x0, [fp, #0x10]
    // 0x8fc788: LoadField: r1 = r2->field_b
    //     0x8fc788: ldur            w1, [x2, #0xb]
    // 0x8fc78c: r3 = LoadInt32Instr(r0)
    //     0x8fc78c: sbfx            x3, x0, #1, #0x1f
    //     0x8fc790: tbz             w0, #0, #0x8fc798
    //     0x8fc794: ldur            x3, [x0, #7]
    // 0x8fc798: r0 = LoadInt32Instr(r1)
    //     0x8fc798: sbfx            x0, x1, #1, #0x1f
    // 0x8fc79c: mov             x1, x3
    // 0x8fc7a0: cmp             x1, x0
    // 0x8fc7a4: b.hs            #0x8fc9b8
    // 0x8fc7a8: LoadField: r0 = r2->field_f
    //     0x8fc7a8: ldur            w0, [x2, #0xf]
    // 0x8fc7ac: DecompressPointer r0
    //     0x8fc7ac: add             x0, x0, HEAP, lsl #32
    // 0x8fc7b0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x8fc7b0: add             x16, x0, x3, lsl #2
    //     0x8fc7b4: ldur            w1, [x16, #0xf]
    // 0x8fc7b8: DecompressPointer r1
    //     0x8fc7b8: add             x1, x1, HEAP, lsl #32
    // 0x8fc7bc: ldur            x2, [fp, #-0x10]
    // 0x8fc7c0: mov             x0, x1
    // 0x8fc7c4: stur            x1, [fp, #-8]
    // 0x8fc7c8: ArrayStore: r2[0] = r0  ; List_4
    //     0x8fc7c8: stur            w0, [x2, #0x17]
    //     0x8fc7cc: tbz             w0, #0, #0x8fc7e8
    //     0x8fc7d0: ldurb           w16, [x2, #-1]
    //     0x8fc7d4: ldurb           w17, [x0, #-1]
    //     0x8fc7d8: and             x16, x17, x16, lsr #2
    //     0x8fc7dc: tst             x16, HEAP, lsr #32
    //     0x8fc7e0: b.eq            #0x8fc7e8
    //     0x8fc7e4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x8fc7e8: str             x1, [SP]
    // 0x8fc7ec: r4 = 0
    //     0x8fc7ec: movz            x4, #0
    // 0x8fc7f0: ldr             x0, [SP]
    // 0x8fc7f4: r5 = UnlinkedCall_0x613b5c
    //     0x8fc7f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8fc7f8: ldp             x5, lr, [x16, #0xe0]
    // 0x8fc7fc: blr             lr
    // 0x8fc800: r1 = 60
    //     0x8fc800: movz            x1, #0x3c
    // 0x8fc804: branchIfSmi(r0, 0x8fc810)
    //     0x8fc804: tbz             w0, #0, #0x8fc810
    // 0x8fc808: r1 = LoadClassIdInstr(r0)
    //     0x8fc808: ldur            x1, [x0, #-1]
    //     0x8fc80c: ubfx            x1, x1, #0xc, #0x14
    // 0x8fc810: r16 = "image"
    //     0x8fc810: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x8fc814: stp             x16, x0, [SP]
    // 0x8fc818: mov             x0, x1
    // 0x8fc81c: mov             lr, x0
    // 0x8fc820: ldr             lr, [x21, lr, lsl #3]
    // 0x8fc824: blr             lr
    // 0x8fc828: tbnz            w0, #4, #0x8fc8dc
    // 0x8fc82c: r0 = ImageHeaders.forImages()
    //     0x8fc82c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x8fc830: stur            x0, [fp, #-0x18]
    // 0x8fc834: ldur            x16, [fp, #-8]
    // 0x8fc838: str             x16, [SP]
    // 0x8fc83c: r4 = 0
    //     0x8fc83c: movz            x4, #0
    // 0x8fc840: ldr             x0, [SP]
    // 0x8fc844: r5 = UnlinkedCall_0x613b5c
    //     0x8fc844: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8fc848: ldp             x5, lr, [x16, #0xf0]
    // 0x8fc84c: blr             lr
    // 0x8fc850: cmp             w0, NULL
    // 0x8fc854: b.ne            #0x8fc85c
    // 0x8fc858: r0 = ""
    //     0x8fc858: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fc85c: stur            x0, [fp, #-0x20]
    // 0x8fc860: r1 = Function '<anonymous closure>':.
    //     0x8fc860: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f100] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8fc864: ldr             x1, [x1, #0x100]
    // 0x8fc868: r2 = Null
    //     0x8fc868: mov             x2, NULL
    // 0x8fc86c: r0 = AllocateClosure()
    //     0x8fc86c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fc870: r1 = Function '<anonymous closure>':.
    //     0x8fc870: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f108] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8fc874: ldr             x1, [x1, #0x108]
    // 0x8fc878: r2 = Null
    //     0x8fc878: mov             x2, NULL
    // 0x8fc87c: stur            x0, [fp, #-0x28]
    // 0x8fc880: r0 = AllocateClosure()
    //     0x8fc880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fc884: stur            x0, [fp, #-0x30]
    // 0x8fc888: r0 = CachedNetworkImage()
    //     0x8fc888: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x8fc88c: stur            x0, [fp, #-0x38]
    // 0x8fc890: r16 = 60.000000
    //     0x8fc890: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x8fc894: ldr             x16, [x16, #0x110]
    // 0x8fc898: r30 = 60.000000
    //     0x8fc898: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x8fc89c: ldr             lr, [lr, #0x110]
    // 0x8fc8a0: stp             lr, x16, [SP, #0x20]
    // 0x8fc8a4: r16 = Instance_BoxFit
    //     0x8fc8a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x8fc8a8: ldr             x16, [x16, #0x118]
    // 0x8fc8ac: ldur            lr, [fp, #-0x18]
    // 0x8fc8b0: stp             lr, x16, [SP, #0x10]
    // 0x8fc8b4: ldur            x16, [fp, #-0x28]
    // 0x8fc8b8: ldur            lr, [fp, #-0x30]
    // 0x8fc8bc: stp             lr, x16, [SP]
    // 0x8fc8c0: mov             x1, x0
    // 0x8fc8c4: ldur            x2, [fp, #-0x20]
    // 0x8fc8c8: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0x8fc8c8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f120] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0x8fc8cc: ldr             x4, [x4, #0x120]
    // 0x8fc8d0: r0 = CachedNetworkImage()
    //     0x8fc8d0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x8fc8d4: ldur            x0, [fp, #-0x38]
    // 0x8fc8d8: b               #0x8fc91c
    // 0x8fc8dc: ldur            x16, [fp, #-8]
    // 0x8fc8e0: str             x16, [SP]
    // 0x8fc8e4: r4 = 0
    //     0x8fc8e4: movz            x4, #0
    // 0x8fc8e8: ldr             x0, [SP]
    // 0x8fc8ec: r5 = UnlinkedCall_0x613b5c
    //     0x8fc8ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f128] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8fc8f0: ldp             x5, lr, [x16, #0x128]
    // 0x8fc8f4: blr             lr
    // 0x8fc8f8: cmp             w0, NULL
    // 0x8fc8fc: b.ne            #0x8fc904
    // 0x8fc900: r0 = ""
    //     0x8fc900: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fc904: stur            x0, [fp, #-8]
    // 0x8fc908: r0 = VideoPlayerWidget()
    //     0x8fc908: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x8fc90c: mov             x1, x0
    // 0x8fc910: ldur            x0, [fp, #-8]
    // 0x8fc914: StoreField: r1->field_b = r0
    //     0x8fc914: stur            w0, [x1, #0xb]
    // 0x8fc918: mov             x0, x1
    // 0x8fc91c: stur            x0, [fp, #-8]
    // 0x8fc920: r0 = ClipRRect()
    //     0x8fc920: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x8fc924: mov             x1, x0
    // 0x8fc928: r0 = Instance_BorderRadius
    //     0x8fc928: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x8fc92c: ldr             x0, [x0, #0xf70]
    // 0x8fc930: stur            x1, [fp, #-0x18]
    // 0x8fc934: StoreField: r1->field_f = r0
    //     0x8fc934: stur            w0, [x1, #0xf]
    // 0x8fc938: r0 = Instance_Clip
    //     0x8fc938: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x8fc93c: ldr             x0, [x0, #0x138]
    // 0x8fc940: ArrayStore: r1[0] = r0  ; List_4
    //     0x8fc940: stur            w0, [x1, #0x17]
    // 0x8fc944: ldur            x0, [fp, #-8]
    // 0x8fc948: StoreField: r1->field_b = r0
    //     0x8fc948: stur            w0, [x1, #0xb]
    // 0x8fc94c: r0 = InkWell()
    //     0x8fc94c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x8fc950: mov             x3, x0
    // 0x8fc954: ldur            x0, [fp, #-0x18]
    // 0x8fc958: stur            x3, [fp, #-8]
    // 0x8fc95c: StoreField: r3->field_b = r0
    //     0x8fc95c: stur            w0, [x3, #0xb]
    // 0x8fc960: ldur            x2, [fp, #-0x10]
    // 0x8fc964: r1 = Function '<anonymous closure>':.
    //     0x8fc964: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f140] AnonymousClosure: (0x8fee38), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8fc968: ldr             x1, [x1, #0x140]
    // 0x8fc96c: r0 = AllocateClosure()
    //     0x8fc96c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fc970: mov             x1, x0
    // 0x8fc974: ldur            x0, [fp, #-8]
    // 0x8fc978: StoreField: r0->field_f = r1
    //     0x8fc978: stur            w1, [x0, #0xf]
    // 0x8fc97c: r1 = true
    //     0x8fc97c: add             x1, NULL, #0x20  ; true
    // 0x8fc980: StoreField: r0->field_43 = r1
    //     0x8fc980: stur            w1, [x0, #0x43]
    // 0x8fc984: r2 = Instance_BoxShape
    //     0x8fc984: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x8fc988: ldr             x2, [x2, #0x80]
    // 0x8fc98c: StoreField: r0->field_47 = r2
    //     0x8fc98c: stur            w2, [x0, #0x47]
    // 0x8fc990: StoreField: r0->field_6f = r1
    //     0x8fc990: stur            w1, [x0, #0x6f]
    // 0x8fc994: r2 = false
    //     0x8fc994: add             x2, NULL, #0x30  ; false
    // 0x8fc998: StoreField: r0->field_73 = r2
    //     0x8fc998: stur            w2, [x0, #0x73]
    // 0x8fc99c: StoreField: r0->field_83 = r1
    //     0x8fc99c: stur            w1, [x0, #0x83]
    // 0x8fc9a0: StoreField: r0->field_7b = r2
    //     0x8fc9a0: stur            w2, [x0, #0x7b]
    // 0x8fc9a4: LeaveFrame
    //     0x8fc9a4: mov             SP, fp
    //     0x8fc9a8: ldp             fp, lr, [SP], #0x10
    // 0x8fc9ac: ret
    //     0x8fc9ac: ret             
    // 0x8fc9b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc9b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc9b4: b               #0x8fc6dc
    // 0x8fc9b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fc9b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8fee38, size: 0x9c
    // 0x8fee38: EnterFrame
    //     0x8fee38: stp             fp, lr, [SP, #-0x10]!
    //     0x8fee3c: mov             fp, SP
    // 0x8fee40: AllocStack(0x28)
    //     0x8fee40: sub             SP, SP, #0x28
    // 0x8fee44: SetupParameters()
    //     0x8fee44: ldr             x0, [fp, #0x10]
    //     0x8fee48: ldur            w2, [x0, #0x17]
    //     0x8fee4c: add             x2, x2, HEAP, lsl #32
    //     0x8fee50: stur            x2, [fp, #-8]
    // 0x8fee54: CheckStackOverflow
    //     0x8fee54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fee58: cmp             SP, x16
    //     0x8fee5c: b.ls            #0x8feecc
    // 0x8fee60: LoadField: r1 = r2->field_f
    //     0x8fee60: ldur            w1, [x2, #0xf]
    // 0x8fee64: DecompressPointer r1
    //     0x8fee64: add             x1, x1, HEAP, lsl #32
    // 0x8fee68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8fee68: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8fee6c: r0 = of()
    //     0x8fee6c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x8fee70: ldur            x2, [fp, #-8]
    // 0x8fee74: r1 = Function '<anonymous closure>':.
    //     0x8fee74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f148] AnonymousClosure: (0x8fefe4), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8fee78: ldr             x1, [x1, #0x148]
    // 0x8fee7c: stur            x0, [fp, #-8]
    // 0x8fee80: r0 = AllocateClosure()
    //     0x8fee80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fee84: r1 = Null
    //     0x8fee84: mov             x1, NULL
    // 0x8fee88: stur            x0, [fp, #-0x10]
    // 0x8fee8c: r0 = MaterialPageRoute()
    //     0x8fee8c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x8fee90: mov             x1, x0
    // 0x8fee94: ldur            x2, [fp, #-0x10]
    // 0x8fee98: stur            x0, [fp, #-0x10]
    // 0x8fee9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8fee9c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8feea0: r0 = MaterialPageRoute()
    //     0x8feea0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x8feea4: ldur            x16, [fp, #-8]
    // 0x8feea8: stp             x16, NULL, [SP, #8]
    // 0x8feeac: ldur            x16, [fp, #-0x10]
    // 0x8feeb0: str             x16, [SP]
    // 0x8feeb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8feeb4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8feeb8: r0 = push()
    //     0x8feeb8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x8feebc: r0 = Null
    //     0x8feebc: mov             x0, NULL
    // 0x8feec0: LeaveFrame
    //     0x8feec0: mov             SP, fp
    //     0x8feec4: ldp             fp, lr, [SP], #0x10
    // 0x8feec8: ret
    //     0x8feec8: ret             
    // 0x8feecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8feecc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8feed0: b               #0x8fee60
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8fefe4, size: 0x19c
    // 0x8fefe4: EnterFrame
    //     0x8fefe4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fefe8: mov             fp, SP
    // 0x8fefec: AllocStack(0x38)
    //     0x8fefec: sub             SP, SP, #0x38
    // 0x8feff0: SetupParameters()
    //     0x8feff0: ldr             x0, [fp, #0x18]
    //     0x8feff4: ldur            w2, [x0, #0x17]
    //     0x8feff8: add             x2, x2, HEAP, lsl #32
    //     0x8feffc: stur            x2, [fp, #-0x10]
    // 0x8ff000: CheckStackOverflow
    //     0x8ff000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff004: cmp             SP, x16
    //     0x8ff008: b.ls            #0x8ff178
    // 0x8ff00c: LoadField: r0 = r2->field_b
    //     0x8ff00c: ldur            w0, [x2, #0xb]
    // 0x8ff010: DecompressPointer r0
    //     0x8ff010: add             x0, x0, HEAP, lsl #32
    // 0x8ff014: stur            x0, [fp, #-8]
    // 0x8ff018: LoadField: r1 = r0->field_f
    //     0x8ff018: ldur            w1, [x0, #0xf]
    // 0x8ff01c: DecompressPointer r1
    //     0x8ff01c: add             x1, x1, HEAP, lsl #32
    // 0x8ff020: r0 = controller()
    //     0x8ff020: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8ff024: LoadField: r1 = r0->field_4f
    //     0x8ff024: ldur            w1, [x0, #0x4f]
    // 0x8ff028: DecompressPointer r1
    //     0x8ff028: add             x1, x1, HEAP, lsl #32
    // 0x8ff02c: r0 = value()
    //     0x8ff02c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8ff030: LoadField: r1 = r0->field_b
    //     0x8ff030: ldur            w1, [x0, #0xb]
    // 0x8ff034: DecompressPointer r1
    //     0x8ff034: add             x1, x1, HEAP, lsl #32
    // 0x8ff038: cmp             w1, NULL
    // 0x8ff03c: b.ne            #0x8ff048
    // 0x8ff040: r0 = Null
    //     0x8ff040: mov             x0, NULL
    // 0x8ff044: b               #0x8ff050
    // 0x8ff048: LoadField: r0 = r1->field_b
    //     0x8ff048: ldur            w0, [x1, #0xb]
    // 0x8ff04c: DecompressPointer r0
    //     0x8ff04c: add             x0, x0, HEAP, lsl #32
    // 0x8ff050: cmp             w0, NULL
    // 0x8ff054: b.ne            #0x8ff070
    // 0x8ff058: r1 = <ReviewRatingEntity>
    //     0x8ff058: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0x8ff05c: ldr             x1, [x1, #0x150]
    // 0x8ff060: r2 = 0
    //     0x8ff060: movz            x2, #0
    // 0x8ff064: r0 = _GrowableList()
    //     0x8ff064: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ff068: mov             x1, x0
    // 0x8ff06c: b               #0x8ff074
    // 0x8ff070: mov             x1, x0
    // 0x8ff074: ldur            x2, [fp, #-0x10]
    // 0x8ff078: ldur            x0, [fp, #-8]
    // 0x8ff07c: stur            x1, [fp, #-0x20]
    // 0x8ff080: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8ff080: ldur            w3, [x2, #0x17]
    // 0x8ff084: DecompressPointer r3
    //     0x8ff084: add             x3, x3, HEAP, lsl #32
    // 0x8ff088: stur            x3, [fp, #-0x18]
    // 0x8ff08c: str             x3, [SP]
    // 0x8ff090: r4 = 0
    //     0x8ff090: movz            x4, #0
    // 0x8ff094: ldr             x0, [SP]
    // 0x8ff098: r5 = UnlinkedCall_0x613b5c
    //     0x8ff098: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f158] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8ff09c: ldp             x5, lr, [x16, #0x158]
    // 0x8ff0a0: blr             lr
    // 0x8ff0a4: stur            x0, [fp, #-0x28]
    // 0x8ff0a8: ldur            x16, [fp, #-0x18]
    // 0x8ff0ac: str             x16, [SP]
    // 0x8ff0b0: r4 = 0
    //     0x8ff0b0: movz            x4, #0
    // 0x8ff0b4: ldr             x0, [SP]
    // 0x8ff0b8: r5 = UnlinkedCall_0x613b5c
    //     0x8ff0b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f168] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8ff0bc: ldp             x5, lr, [x16, #0x168]
    // 0x8ff0c0: blr             lr
    // 0x8ff0c4: mov             x3, x0
    // 0x8ff0c8: r2 = Null
    //     0x8ff0c8: mov             x2, NULL
    // 0x8ff0cc: r1 = Null
    //     0x8ff0cc: mov             x1, NULL
    // 0x8ff0d0: stur            x3, [fp, #-0x18]
    // 0x8ff0d4: branchIfSmi(r0, 0x8ff0fc)
    //     0x8ff0d4: tbz             w0, #0, #0x8ff0fc
    // 0x8ff0d8: r4 = LoadClassIdInstr(r0)
    //     0x8ff0d8: ldur            x4, [x0, #-1]
    //     0x8ff0dc: ubfx            x4, x4, #0xc, #0x14
    // 0x8ff0e0: sub             x4, x4, #0x3c
    // 0x8ff0e4: cmp             x4, #1
    // 0x8ff0e8: b.ls            #0x8ff0fc
    // 0x8ff0ec: r8 = int?
    //     0x8ff0ec: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0x8ff0f0: r3 = Null
    //     0x8ff0f0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f178] Null
    //     0x8ff0f4: ldr             x3, [x3, #0x178]
    // 0x8ff0f8: r0 = int?()
    //     0x8ff0f8: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0x8ff0fc: ldur            x0, [fp, #-8]
    // 0x8ff100: LoadField: r1 = r0->field_f
    //     0x8ff100: ldur            w1, [x0, #0xf]
    // 0x8ff104: DecompressPointer r1
    //     0x8ff104: add             x1, x1, HEAP, lsl #32
    // 0x8ff108: r0 = controller()
    //     0x8ff108: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8ff10c: LoadField: r1 = r0->field_63
    //     0x8ff10c: ldur            w1, [x0, #0x63]
    // 0x8ff110: DecompressPointer r1
    //     0x8ff110: add             x1, x1, HEAP, lsl #32
    // 0x8ff114: stur            x1, [fp, #-8]
    // 0x8ff118: r0 = RatingReviewAllMediaOnTapImage()
    //     0x8ff118: bl              #0x8ff180  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0x8ff11c: mov             x3, x0
    // 0x8ff120: ldur            x0, [fp, #-0x20]
    // 0x8ff124: stur            x3, [fp, #-0x30]
    // 0x8ff128: StoreField: r3->field_f = r0
    //     0x8ff128: stur            w0, [x3, #0xf]
    // 0x8ff12c: ldur            x0, [fp, #-0x28]
    // 0x8ff130: r1 = LoadInt32Instr(r0)
    //     0x8ff130: sbfx            x1, x0, #1, #0x1f
    //     0x8ff134: tbz             w0, #0, #0x8ff13c
    //     0x8ff138: ldur            x1, [x0, #7]
    // 0x8ff13c: StoreField: r3->field_13 = r1
    //     0x8ff13c: stur            x1, [x3, #0x13]
    // 0x8ff140: ldur            x0, [fp, #-0x18]
    // 0x8ff144: StoreField: r3->field_1b = r0
    //     0x8ff144: stur            w0, [x3, #0x1b]
    // 0x8ff148: ldur            x2, [fp, #-0x10]
    // 0x8ff14c: r1 = Function '<anonymous closure>':.
    //     0x8ff14c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f188] AnonymousClosure: (0x8ff1ac), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8ff150: ldr             x1, [x1, #0x188]
    // 0x8ff154: r0 = AllocateClosure()
    //     0x8ff154: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8ff158: mov             x1, x0
    // 0x8ff15c: ldur            x0, [fp, #-0x30]
    // 0x8ff160: StoreField: r0->field_1f = r1
    //     0x8ff160: stur            w1, [x0, #0x1f]
    // 0x8ff164: ldur            x1, [fp, #-8]
    // 0x8ff168: StoreField: r0->field_23 = r1
    //     0x8ff168: stur            w1, [x0, #0x23]
    // 0x8ff16c: LeaveFrame
    //     0x8ff16c: mov             SP, fp
    //     0x8ff170: ldp             fp, lr, [SP], #0x10
    // 0x8ff174: ret
    //     0x8ff174: ret             
    // 0x8ff178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff178: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff17c: b               #0x8ff00c
  }
  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0x8ff1ac, size: 0x58
    // 0x8ff1ac: EnterFrame
    //     0x8ff1ac: stp             fp, lr, [SP, #-0x10]!
    //     0x8ff1b0: mov             fp, SP
    // 0x8ff1b4: ldr             x0, [fp, #0x18]
    // 0x8ff1b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ff1b8: ldur            w1, [x0, #0x17]
    // 0x8ff1bc: DecompressPointer r1
    //     0x8ff1bc: add             x1, x1, HEAP, lsl #32
    // 0x8ff1c0: CheckStackOverflow
    //     0x8ff1c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ff1c4: cmp             SP, x16
    //     0x8ff1c8: b.ls            #0x8ff1fc
    // 0x8ff1cc: LoadField: r0 = r1->field_b
    //     0x8ff1cc: ldur            w0, [x1, #0xb]
    // 0x8ff1d0: DecompressPointer r0
    //     0x8ff1d0: add             x0, x0, HEAP, lsl #32
    // 0x8ff1d4: LoadField: r1 = r0->field_f
    //     0x8ff1d4: ldur            w1, [x0, #0xf]
    // 0x8ff1d8: DecompressPointer r1
    //     0x8ff1d8: add             x1, x1, HEAP, lsl #32
    // 0x8ff1dc: r0 = controller()
    //     0x8ff1dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8ff1e0: mov             x1, x0
    // 0x8ff1e4: ldr             x2, [fp, #0x10]
    // 0x8ff1e8: r0 = saveFlaggedData()
    //     0x8ff1e8: bl              #0x8ff204  ; [package:customer_app/app/presentation/controllers/product_detail/product_detail_controller.dart] ProductDetailController::saveFlaggedData
    // 0x8ff1ec: r0 = Null
    //     0x8ff1ec: mov             x0, NULL
    // 0x8ff1f0: LeaveFrame
    //     0x8ff1f0: mov             SP, fp
    //     0x8ff1f4: ldp             fp, lr, [SP], #0x10
    // 0x8ff1f8: ret
    //     0x8ff1f8: ret             
    // 0x8ff1fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ff1fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ff200: b               #0x8ff1cc
  }
  [closure] Icon <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0x900b60, size: 0xc
    // 0x900b60: r0 = Instance_Icon
    //     0x900b60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1a8] Obj!Icon@d65c31
    //     0x900b64: ldr             x0, [x0, #0x1a8]
    // 0x900b68: ret
    //     0x900b68: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x900b6c, size: 0x450
    // 0x900b6c: EnterFrame
    //     0x900b6c: stp             fp, lr, [SP, #-0x10]!
    //     0x900b70: mov             fp, SP
    // 0x900b74: AllocStack(0x38)
    //     0x900b74: sub             SP, SP, #0x38
    // 0x900b78: SetupParameters()
    //     0x900b78: ldr             x0, [fp, #0x10]
    //     0x900b7c: ldur            w2, [x0, #0x17]
    //     0x900b80: add             x2, x2, HEAP, lsl #32
    //     0x900b84: stur            x2, [fp, #-8]
    // 0x900b88: CheckStackOverflow
    //     0x900b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900b8c: cmp             SP, x16
    //     0x900b90: b.ls            #0x900fb4
    // 0x900b94: LoadField: r1 = r2->field_f
    //     0x900b94: ldur            w1, [x2, #0xf]
    // 0x900b98: DecompressPointer r1
    //     0x900b98: add             x1, x1, HEAP, lsl #32
    // 0x900b9c: r0 = controller()
    //     0x900b9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x900ba0: LoadField: r1 = r0->field_4f
    //     0x900ba0: ldur            w1, [x0, #0x4f]
    // 0x900ba4: DecompressPointer r1
    //     0x900ba4: add             x1, x1, HEAP, lsl #32
    // 0x900ba8: r0 = value()
    //     0x900ba8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x900bac: LoadField: r1 = r0->field_b
    //     0x900bac: ldur            w1, [x0, #0xb]
    // 0x900bb0: DecompressPointer r1
    //     0x900bb0: add             x1, x1, HEAP, lsl #32
    // 0x900bb4: cmp             w1, NULL
    // 0x900bb8: b.ne            #0x900bd8
    // 0x900bbc: r0 = Container()
    //     0x900bbc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x900bc0: mov             x1, x0
    // 0x900bc4: stur            x0, [fp, #-0x10]
    // 0x900bc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x900bc8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x900bcc: r0 = Container()
    //     0x900bcc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x900bd0: ldur            x0, [fp, #-0x10]
    // 0x900bd4: b               #0x900fa8
    // 0x900bd8: ldur            x2, [fp, #-8]
    // 0x900bdc: LoadField: r1 = r2->field_13
    //     0x900bdc: ldur            w1, [x2, #0x13]
    // 0x900be0: DecompressPointer r1
    //     0x900be0: add             x1, x1, HEAP, lsl #32
    // 0x900be4: r0 = of()
    //     0x900be4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x900be8: LoadField: r1 = r0->field_87
    //     0x900be8: ldur            w1, [x0, #0x87]
    // 0x900bec: DecompressPointer r1
    //     0x900bec: add             x1, x1, HEAP, lsl #32
    // 0x900bf0: LoadField: r0 = r1->field_7
    //     0x900bf0: ldur            w0, [x1, #7]
    // 0x900bf4: DecompressPointer r0
    //     0x900bf4: add             x0, x0, HEAP, lsl #32
    // 0x900bf8: r16 = 16.000000
    //     0x900bf8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x900bfc: ldr             x16, [x16, #0x188]
    // 0x900c00: r30 = Instance_Color
    //     0x900c00: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x900c04: stp             lr, x16, [SP]
    // 0x900c08: mov             x1, x0
    // 0x900c0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x900c0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x900c10: ldr             x4, [x4, #0xaa0]
    // 0x900c14: r0 = copyWith()
    //     0x900c14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x900c18: stur            x0, [fp, #-0x10]
    // 0x900c1c: r0 = Text()
    //     0x900c1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x900c20: mov             x2, x0
    // 0x900c24: r0 = "Real images from customers"
    //     0x900c24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0x900c28: ldr             x0, [x0, #0x88]
    // 0x900c2c: stur            x2, [fp, #-0x18]
    // 0x900c30: StoreField: r2->field_b = r0
    //     0x900c30: stur            w0, [x2, #0xb]
    // 0x900c34: ldur            x0, [fp, #-0x10]
    // 0x900c38: StoreField: r2->field_13 = r0
    //     0x900c38: stur            w0, [x2, #0x13]
    // 0x900c3c: ldur            x0, [fp, #-8]
    // 0x900c40: LoadField: r1 = r0->field_13
    //     0x900c40: ldur            w1, [x0, #0x13]
    // 0x900c44: DecompressPointer r1
    //     0x900c44: add             x1, x1, HEAP, lsl #32
    // 0x900c48: r0 = of()
    //     0x900c48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x900c4c: LoadField: r1 = r0->field_5b
    //     0x900c4c: ldur            w1, [x0, #0x5b]
    // 0x900c50: DecompressPointer r1
    //     0x900c50: add             x1, x1, HEAP, lsl #32
    // 0x900c54: stur            x1, [fp, #-0x10]
    // 0x900c58: r0 = Icon()
    //     0x900c58: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x900c5c: mov             x1, x0
    // 0x900c60: r0 = Instance_IconData
    //     0x900c60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f090] Obj!IconData@d55181
    //     0x900c64: ldr             x0, [x0, #0x90]
    // 0x900c68: stur            x1, [fp, #-0x20]
    // 0x900c6c: StoreField: r1->field_b = r0
    //     0x900c6c: stur            w0, [x1, #0xb]
    // 0x900c70: r0 = 25.000000
    //     0x900c70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x900c74: ldr             x0, [x0, #0x98]
    // 0x900c78: StoreField: r1->field_f = r0
    //     0x900c78: stur            w0, [x1, #0xf]
    // 0x900c7c: ldur            x0, [fp, #-0x10]
    // 0x900c80: StoreField: r1->field_23 = r0
    //     0x900c80: stur            w0, [x1, #0x23]
    // 0x900c84: r0 = InkWell()
    //     0x900c84: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x900c88: mov             x3, x0
    // 0x900c8c: ldur            x0, [fp, #-0x20]
    // 0x900c90: stur            x3, [fp, #-0x10]
    // 0x900c94: StoreField: r3->field_b = r0
    //     0x900c94: stur            w0, [x3, #0xb]
    // 0x900c98: r1 = Function '<anonymous closure>':.
    //     0x900c98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x900c9c: ldr             x1, [x1, #0xa0]
    // 0x900ca0: r2 = Null
    //     0x900ca0: mov             x2, NULL
    // 0x900ca4: r0 = AllocateClosure()
    //     0x900ca4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x900ca8: mov             x1, x0
    // 0x900cac: ldur            x0, [fp, #-0x10]
    // 0x900cb0: StoreField: r0->field_f = r1
    //     0x900cb0: stur            w1, [x0, #0xf]
    // 0x900cb4: r3 = true
    //     0x900cb4: add             x3, NULL, #0x20  ; true
    // 0x900cb8: StoreField: r0->field_43 = r3
    //     0x900cb8: stur            w3, [x0, #0x43]
    // 0x900cbc: r1 = Instance_BoxShape
    //     0x900cbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x900cc0: ldr             x1, [x1, #0x80]
    // 0x900cc4: StoreField: r0->field_47 = r1
    //     0x900cc4: stur            w1, [x0, #0x47]
    // 0x900cc8: StoreField: r0->field_6f = r3
    //     0x900cc8: stur            w3, [x0, #0x6f]
    // 0x900ccc: r4 = false
    //     0x900ccc: add             x4, NULL, #0x30  ; false
    // 0x900cd0: StoreField: r0->field_73 = r4
    //     0x900cd0: stur            w4, [x0, #0x73]
    // 0x900cd4: StoreField: r0->field_83 = r3
    //     0x900cd4: stur            w3, [x0, #0x83]
    // 0x900cd8: StoreField: r0->field_7b = r4
    //     0x900cd8: stur            w4, [x0, #0x7b]
    // 0x900cdc: r1 = Null
    //     0x900cdc: mov             x1, NULL
    // 0x900ce0: r2 = 4
    //     0x900ce0: movz            x2, #0x4
    // 0x900ce4: r0 = AllocateArray()
    //     0x900ce4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x900ce8: mov             x2, x0
    // 0x900cec: ldur            x0, [fp, #-0x18]
    // 0x900cf0: stur            x2, [fp, #-0x20]
    // 0x900cf4: StoreField: r2->field_f = r0
    //     0x900cf4: stur            w0, [x2, #0xf]
    // 0x900cf8: ldur            x0, [fp, #-0x10]
    // 0x900cfc: StoreField: r2->field_13 = r0
    //     0x900cfc: stur            w0, [x2, #0x13]
    // 0x900d00: r1 = <Widget>
    //     0x900d00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x900d04: r0 = AllocateGrowableArray()
    //     0x900d04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x900d08: mov             x1, x0
    // 0x900d0c: ldur            x0, [fp, #-0x20]
    // 0x900d10: stur            x1, [fp, #-0x10]
    // 0x900d14: StoreField: r1->field_f = r0
    //     0x900d14: stur            w0, [x1, #0xf]
    // 0x900d18: r2 = 4
    //     0x900d18: movz            x2, #0x4
    // 0x900d1c: StoreField: r1->field_b = r2
    //     0x900d1c: stur            w2, [x1, #0xb]
    // 0x900d20: r0 = Row()
    //     0x900d20: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x900d24: mov             x1, x0
    // 0x900d28: r0 = Instance_Axis
    //     0x900d28: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x900d2c: stur            x1, [fp, #-0x18]
    // 0x900d30: StoreField: r1->field_f = r0
    //     0x900d30: stur            w0, [x1, #0xf]
    // 0x900d34: r0 = Instance_MainAxisAlignment
    //     0x900d34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x900d38: ldr             x0, [x0, #0xa8]
    // 0x900d3c: StoreField: r1->field_13 = r0
    //     0x900d3c: stur            w0, [x1, #0x13]
    // 0x900d40: r0 = Instance_MainAxisSize
    //     0x900d40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x900d44: ldr             x0, [x0, #0xa10]
    // 0x900d48: ArrayStore: r1[0] = r0  ; List_4
    //     0x900d48: stur            w0, [x1, #0x17]
    // 0x900d4c: r2 = Instance_CrossAxisAlignment
    //     0x900d4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x900d50: ldr             x2, [x2, #0xa18]
    // 0x900d54: StoreField: r1->field_1b = r2
    //     0x900d54: stur            w2, [x1, #0x1b]
    // 0x900d58: r3 = Instance_VerticalDirection
    //     0x900d58: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x900d5c: ldr             x3, [x3, #0xa20]
    // 0x900d60: StoreField: r1->field_23 = r3
    //     0x900d60: stur            w3, [x1, #0x23]
    // 0x900d64: r4 = Instance_Clip
    //     0x900d64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x900d68: ldr             x4, [x4, #0x38]
    // 0x900d6c: StoreField: r1->field_2b = r4
    //     0x900d6c: stur            w4, [x1, #0x2b]
    // 0x900d70: StoreField: r1->field_2f = rZR
    //     0x900d70: stur            xzr, [x1, #0x2f]
    // 0x900d74: ldur            x5, [fp, #-0x10]
    // 0x900d78: StoreField: r1->field_b = r5
    //     0x900d78: stur            w5, [x1, #0xb]
    // 0x900d7c: r0 = Padding()
    //     0x900d7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x900d80: mov             x2, x0
    // 0x900d84: r0 = Instance_EdgeInsets
    //     0x900d84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x900d88: ldr             x0, [x0, #0xb0]
    // 0x900d8c: stur            x2, [fp, #-0x10]
    // 0x900d90: StoreField: r2->field_f = r0
    //     0x900d90: stur            w0, [x2, #0xf]
    // 0x900d94: ldur            x0, [fp, #-0x18]
    // 0x900d98: StoreField: r2->field_b = r0
    //     0x900d98: stur            w0, [x2, #0xb]
    // 0x900d9c: ldur            x0, [fp, #-8]
    // 0x900da0: LoadField: r1 = r0->field_f
    //     0x900da0: ldur            w1, [x0, #0xf]
    // 0x900da4: DecompressPointer r1
    //     0x900da4: add             x1, x1, HEAP, lsl #32
    // 0x900da8: r0 = controller()
    //     0x900da8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x900dac: mov             x1, x0
    // 0x900db0: r0 = exchangeCheckoutResponse()
    //     0x900db0: bl              #0x8fc9bc  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_controller.dart] ExchangeCheckoutController::exchangeCheckoutResponse
    // 0x900db4: LoadField: r1 = r0->field_b
    //     0x900db4: ldur            w1, [x0, #0xb]
    // 0x900db8: DecompressPointer r1
    //     0x900db8: add             x1, x1, HEAP, lsl #32
    // 0x900dbc: cmp             w1, NULL
    // 0x900dc0: b.ne            #0x900dcc
    // 0x900dc4: r5 = Null
    //     0x900dc4: mov             x5, NULL
    // 0x900dc8: b               #0x900e08
    // 0x900dcc: LoadField: r0 = r1->field_b
    //     0x900dcc: ldur            w0, [x1, #0xb]
    // 0x900dd0: DecompressPointer r0
    //     0x900dd0: add             x0, x0, HEAP, lsl #32
    // 0x900dd4: stur            x0, [fp, #-0x18]
    // 0x900dd8: r1 = Function '<anonymous closure>':.
    //     0x900dd8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b8] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x900ddc: ldr             x1, [x1, #0xb8]
    // 0x900de0: r2 = Null
    //     0x900de0: mov             x2, NULL
    // 0x900de4: r0 = AllocateClosure()
    //     0x900de4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x900de8: ldur            x16, [fp, #-0x18]
    // 0x900dec: stp             x16, NULL, [SP, #8]
    // 0x900df0: str             x0, [SP]
    // 0x900df4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x900df4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x900df8: r0 = expand()
    //     0x900df8: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x900dfc: str             x0, [SP]
    // 0x900e00: r0 = length()
    //     0x900e00: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x900e04: mov             x5, x0
    // 0x900e08: ldur            x0, [fp, #-0x10]
    // 0x900e0c: ldur            x2, [fp, #-8]
    // 0x900e10: stur            x5, [fp, #-0x18]
    // 0x900e14: r1 = Function '<anonymous closure>':.
    //     0x900e14: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0c0] AnonymousClosure: (0x8fc6b4), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x900e18: ldr             x1, [x1, #0xc0]
    // 0x900e1c: r0 = AllocateClosure()
    //     0x900e1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x900e20: stur            x0, [fp, #-8]
    // 0x900e24: r0 = GridView()
    //     0x900e24: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x900e28: mov             x1, x0
    // 0x900e2c: ldur            x3, [fp, #-8]
    // 0x900e30: ldur            x5, [fp, #-0x18]
    // 0x900e34: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x900e34: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0c8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d56481
    //     0x900e38: ldr             x2, [x2, #0xc8]
    // 0x900e3c: stur            x0, [fp, #-8]
    // 0x900e40: r0 = GridView.builder()
    //     0x900e40: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x900e44: r1 = Null
    //     0x900e44: mov             x1, NULL
    // 0x900e48: r2 = 4
    //     0x900e48: movz            x2, #0x4
    // 0x900e4c: r0 = AllocateArray()
    //     0x900e4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x900e50: mov             x2, x0
    // 0x900e54: ldur            x0, [fp, #-0x10]
    // 0x900e58: stur            x2, [fp, #-0x18]
    // 0x900e5c: StoreField: r2->field_f = r0
    //     0x900e5c: stur            w0, [x2, #0xf]
    // 0x900e60: ldur            x0, [fp, #-8]
    // 0x900e64: StoreField: r2->field_13 = r0
    //     0x900e64: stur            w0, [x2, #0x13]
    // 0x900e68: r1 = <Widget>
    //     0x900e68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x900e6c: r0 = AllocateGrowableArray()
    //     0x900e6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x900e70: mov             x1, x0
    // 0x900e74: ldur            x0, [fp, #-0x18]
    // 0x900e78: stur            x1, [fp, #-8]
    // 0x900e7c: StoreField: r1->field_f = r0
    //     0x900e7c: stur            w0, [x1, #0xf]
    // 0x900e80: r0 = 4
    //     0x900e80: movz            x0, #0x4
    // 0x900e84: StoreField: r1->field_b = r0
    //     0x900e84: stur            w0, [x1, #0xb]
    // 0x900e88: r0 = Column()
    //     0x900e88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x900e8c: mov             x1, x0
    // 0x900e90: r0 = Instance_Axis
    //     0x900e90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x900e94: stur            x1, [fp, #-0x10]
    // 0x900e98: StoreField: r1->field_f = r0
    //     0x900e98: stur            w0, [x1, #0xf]
    // 0x900e9c: r2 = Instance_MainAxisAlignment
    //     0x900e9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x900ea0: ldr             x2, [x2, #0xa08]
    // 0x900ea4: StoreField: r1->field_13 = r2
    //     0x900ea4: stur            w2, [x1, #0x13]
    // 0x900ea8: r2 = Instance_MainAxisSize
    //     0x900ea8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x900eac: ldr             x2, [x2, #0xa10]
    // 0x900eb0: ArrayStore: r1[0] = r2  ; List_4
    //     0x900eb0: stur            w2, [x1, #0x17]
    // 0x900eb4: r2 = Instance_CrossAxisAlignment
    //     0x900eb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x900eb8: ldr             x2, [x2, #0xa18]
    // 0x900ebc: StoreField: r1->field_1b = r2
    //     0x900ebc: stur            w2, [x1, #0x1b]
    // 0x900ec0: r2 = Instance_VerticalDirection
    //     0x900ec0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x900ec4: ldr             x2, [x2, #0xa20]
    // 0x900ec8: StoreField: r1->field_23 = r2
    //     0x900ec8: stur            w2, [x1, #0x23]
    // 0x900ecc: r2 = Instance_Clip
    //     0x900ecc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x900ed0: ldr             x2, [x2, #0x38]
    // 0x900ed4: StoreField: r1->field_2b = r2
    //     0x900ed4: stur            w2, [x1, #0x2b]
    // 0x900ed8: StoreField: r1->field_2f = rZR
    //     0x900ed8: stur            xzr, [x1, #0x2f]
    // 0x900edc: ldur            x2, [fp, #-8]
    // 0x900ee0: StoreField: r1->field_b = r2
    //     0x900ee0: stur            w2, [x1, #0xb]
    // 0x900ee4: r0 = Padding()
    //     0x900ee4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x900ee8: mov             x1, x0
    // 0x900eec: r0 = Instance_EdgeInsets
    //     0x900eec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x900ef0: ldr             x0, [x0, #0xd0]
    // 0x900ef4: stur            x1, [fp, #-8]
    // 0x900ef8: StoreField: r1->field_f = r0
    //     0x900ef8: stur            w0, [x1, #0xf]
    // 0x900efc: ldur            x0, [fp, #-0x10]
    // 0x900f00: StoreField: r1->field_b = r0
    //     0x900f00: stur            w0, [x1, #0xb]
    // 0x900f04: r0 = SingleChildScrollView()
    //     0x900f04: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x900f08: mov             x1, x0
    // 0x900f0c: r0 = Instance_Axis
    //     0x900f0c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x900f10: stur            x1, [fp, #-0x10]
    // 0x900f14: StoreField: r1->field_b = r0
    //     0x900f14: stur            w0, [x1, #0xb]
    // 0x900f18: r0 = false
    //     0x900f18: add             x0, NULL, #0x30  ; false
    // 0x900f1c: StoreField: r1->field_f = r0
    //     0x900f1c: stur            w0, [x1, #0xf]
    // 0x900f20: ldur            x2, [fp, #-8]
    // 0x900f24: StoreField: r1->field_23 = r2
    //     0x900f24: stur            w2, [x1, #0x23]
    // 0x900f28: r2 = Instance_DragStartBehavior
    //     0x900f28: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x900f2c: StoreField: r1->field_27 = r2
    //     0x900f2c: stur            w2, [x1, #0x27]
    // 0x900f30: r2 = Instance_Clip
    //     0x900f30: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x900f34: ldr             x2, [x2, #0x7e0]
    // 0x900f38: StoreField: r1->field_2b = r2
    //     0x900f38: stur            w2, [x1, #0x2b]
    // 0x900f3c: r2 = Instance_HitTestBehavior
    //     0x900f3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x900f40: ldr             x2, [x2, #0x288]
    // 0x900f44: StoreField: r1->field_2f = r2
    //     0x900f44: stur            w2, [x1, #0x2f]
    // 0x900f48: r0 = SafeArea()
    //     0x900f48: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x900f4c: mov             x1, x0
    // 0x900f50: r0 = true
    //     0x900f50: add             x0, NULL, #0x20  ; true
    // 0x900f54: stur            x1, [fp, #-8]
    // 0x900f58: StoreField: r1->field_b = r0
    //     0x900f58: stur            w0, [x1, #0xb]
    // 0x900f5c: StoreField: r1->field_f = r0
    //     0x900f5c: stur            w0, [x1, #0xf]
    // 0x900f60: StoreField: r1->field_13 = r0
    //     0x900f60: stur            w0, [x1, #0x13]
    // 0x900f64: ArrayStore: r1[0] = r0  ; List_4
    //     0x900f64: stur            w0, [x1, #0x17]
    // 0x900f68: r2 = Instance_EdgeInsets
    //     0x900f68: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x900f6c: StoreField: r1->field_1b = r2
    //     0x900f6c: stur            w2, [x1, #0x1b]
    // 0x900f70: r2 = false
    //     0x900f70: add             x2, NULL, #0x30  ; false
    // 0x900f74: StoreField: r1->field_1f = r2
    //     0x900f74: stur            w2, [x1, #0x1f]
    // 0x900f78: ldur            x3, [fp, #-0x10]
    // 0x900f7c: StoreField: r1->field_23 = r3
    //     0x900f7c: stur            w3, [x1, #0x23]
    // 0x900f80: r0 = Scaffold()
    //     0x900f80: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0x900f84: ldur            x1, [fp, #-8]
    // 0x900f88: ArrayStore: r0[0] = r1  ; List_4
    //     0x900f88: stur            w1, [x0, #0x17]
    // 0x900f8c: r1 = Instance_Color
    //     0x900f8c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x900f90: StoreField: r0->field_33 = r1
    //     0x900f90: stur            w1, [x0, #0x33]
    // 0x900f94: r1 = true
    //     0x900f94: add             x1, NULL, #0x20  ; true
    // 0x900f98: StoreField: r0->field_43 = r1
    //     0x900f98: stur            w1, [x0, #0x43]
    // 0x900f9c: r1 = false
    //     0x900f9c: add             x1, NULL, #0x30  ; false
    // 0x900fa0: StoreField: r0->field_b = r1
    //     0x900fa0: stur            w1, [x0, #0xb]
    // 0x900fa4: StoreField: r0->field_f = r1
    //     0x900fa4: stur            w1, [x0, #0xf]
    // 0x900fa8: LeaveFrame
    //     0x900fa8: mov             SP, fp
    //     0x900fac: ldp             fp, lr, [SP], #0x10
    // 0x900fb0: ret
    //     0x900fb0: ret             
    // 0x900fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x900fb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x900fb8: b               #0x900b94
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9010ec, size: 0x58
    // 0x9010ec: EnterFrame
    //     0x9010ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9010f0: mov             fp, SP
    // 0x9010f4: AllocStack(0x8)
    //     0x9010f4: sub             SP, SP, #8
    // 0x9010f8: CheckStackOverflow
    //     0x9010f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9010fc: cmp             SP, x16
    //     0x901100: b.ls            #0x90113c
    // 0x901104: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x901104: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x901108: ldr             x0, [x0, #0x1c80]
    //     0x90110c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x901110: cmp             w0, w16
    //     0x901114: b.ne            #0x901120
    //     0x901118: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x90111c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x901120: str             NULL, [SP]
    // 0x901124: r4 = const [0x1, 0, 0, 0, null]
    //     0x901124: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x901128: r0 = GetNavigation.back()
    //     0x901128: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x90112c: r0 = Null
    //     0x90112c: mov             x0, NULL
    // 0x901130: LeaveFrame
    //     0x901130: mov             SP, fp
    //     0x901134: ldp             fp, lr, [SP], #0x10
    // 0x901138: ret
    //     0x901138: ret             
    // 0x90113c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90113c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x901140: b               #0x901104
  }
  _ body(/* No info */) {
    // ** addr: 0x150954c, size: 0x64
    // 0x150954c: EnterFrame
    //     0x150954c: stp             fp, lr, [SP, #-0x10]!
    //     0x1509550: mov             fp, SP
    // 0x1509554: AllocStack(0x18)
    //     0x1509554: sub             SP, SP, #0x18
    // 0x1509558: SetupParameters(RatingReviewMediaScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1509558: stur            x1, [fp, #-8]
    //     0x150955c: stur            x2, [fp, #-0x10]
    // 0x1509560: r1 = 2
    //     0x1509560: movz            x1, #0x2
    // 0x1509564: r0 = AllocateContext()
    //     0x1509564: bl              #0x16f6108  ; AllocateContextStub
    // 0x1509568: mov             x1, x0
    // 0x150956c: ldur            x0, [fp, #-8]
    // 0x1509570: stur            x1, [fp, #-0x18]
    // 0x1509574: StoreField: r1->field_f = r0
    //     0x1509574: stur            w0, [x1, #0xf]
    // 0x1509578: ldur            x0, [fp, #-0x10]
    // 0x150957c: StoreField: r1->field_13 = r0
    //     0x150957c: stur            w0, [x1, #0x13]
    // 0x1509580: r0 = Obx()
    //     0x1509580: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1509584: ldur            x2, [fp, #-0x18]
    // 0x1509588: r1 = Function '<anonymous closure>':.
    //     0x1509588: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f080] AnonymousClosure: (0x900b6c), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x150958c: ldr             x1, [x1, #0x80]
    // 0x1509590: stur            x0, [fp, #-8]
    // 0x1509594: r0 = AllocateClosure()
    //     0x1509594: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509598: mov             x1, x0
    // 0x150959c: ldur            x0, [fp, #-8]
    // 0x15095a0: StoreField: r0->field_b = r1
    //     0x15095a0: stur            w1, [x0, #0xb]
    // 0x15095a4: LeaveFrame
    //     0x15095a4: mov             SP, fp
    //     0x15095a8: ldp             fp, lr, [SP], #0x10
    // 0x15095ac: ret
    //     0x15095ac: ret             
  }
}
