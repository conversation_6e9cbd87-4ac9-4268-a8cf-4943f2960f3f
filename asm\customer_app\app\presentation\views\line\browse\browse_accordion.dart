// lib: , url: package:customer_app/app/presentation/views/line/browse/browse_accordion.dart

// class id: 1049473, size: 0x8
class :: {
}

// class id: 3285, size: 0x18, field offset: 0x14
class _AccordionState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a129c, size: 0x2c
    // 0x9a129c: ldr             x1, [SP]
    // 0x9a12a0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9a12a0: ldur            w2, [x1, #0x17]
    // 0x9a12a4: DecompressPointer r2
    //     0x9a12a4: add             x2, x2, HEAP, lsl #32
    // 0x9a12a8: LoadField: r1 = r2->field_f
    //     0x9a12a8: ldur            w1, [x2, #0xf]
    // 0x9a12ac: DecompressPointer r1
    //     0x9a12ac: add             x1, x1, HEAP, lsl #32
    // 0x9a12b0: LoadField: r2 = r1->field_13
    //     0x9a12b0: ldur            w2, [x1, #0x13]
    // 0x9a12b4: DecompressPointer r2
    //     0x9a12b4: add             x2, x2, HEAP, lsl #32
    // 0x9a12b8: eor             x3, x2, #0x10
    // 0x9a12bc: StoreField: r1->field_13 = r3
    //     0x9a12bc: stur            w3, [x1, #0x13]
    // 0x9a12c0: r0 = Null
    //     0x9a12c0: mov             x0, NULL
    // 0x9a12c4: ret
    //     0x9a12c4: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9a12c8, size: 0x60
    // 0x9a12c8: EnterFrame
    //     0x9a12c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9a12cc: mov             fp, SP
    // 0x9a12d0: AllocStack(0x8)
    //     0x9a12d0: sub             SP, SP, #8
    // 0x9a12d4: SetupParameters()
    //     0x9a12d4: ldr             x0, [fp, #0x10]
    //     0x9a12d8: ldur            w2, [x0, #0x17]
    //     0x9a12dc: add             x2, x2, HEAP, lsl #32
    // 0x9a12e0: CheckStackOverflow
    //     0x9a12e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9a12e4: cmp             SP, x16
    //     0x9a12e8: b.ls            #0x9a1320
    // 0x9a12ec: LoadField: r0 = r2->field_f
    //     0x9a12ec: ldur            w0, [x2, #0xf]
    // 0x9a12f0: DecompressPointer r0
    //     0x9a12f0: add             x0, x0, HEAP, lsl #32
    // 0x9a12f4: stur            x0, [fp, #-8]
    // 0x9a12f8: r1 = Function '<anonymous closure>':.
    //     0x9a12f8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b38] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0x9a12fc: ldr             x1, [x1, #0xb38]
    // 0x9a1300: r0 = AllocateClosure()
    //     0x9a1300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9a1304: ldur            x1, [fp, #-8]
    // 0x9a1308: mov             x2, x0
    // 0x9a130c: r0 = setState()
    //     0x9a130c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9a1310: r0 = Null
    //     0x9a1310: mov             x0, NULL
    // 0x9a1314: LeaveFrame
    //     0x9a1314: mov             SP, fp
    //     0x9a1318: ldp             fp, lr, [SP], #0x10
    // 0x9a131c: ret
    //     0x9a131c: ret             
    // 0x9a1320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9a1320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9a1324: b               #0x9a12ec
  }
  _ build(/* No info */) {
    // ** addr: 0xbaa494, size: 0x378
    // 0xbaa494: EnterFrame
    //     0xbaa494: stp             fp, lr, [SP, #-0x10]!
    //     0xbaa498: mov             fp, SP
    // 0xbaa49c: AllocStack(0x38)
    //     0xbaa49c: sub             SP, SP, #0x38
    // 0xbaa4a0: SetupParameters(_AccordionState this /* r1 => r1, fp-0x8 */)
    //     0xbaa4a0: stur            x1, [fp, #-8]
    // 0xbaa4a4: CheckStackOverflow
    //     0xbaa4a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaa4a8: cmp             SP, x16
    //     0xbaa4ac: b.ls            #0xbaa800
    // 0xbaa4b0: r1 = 1
    //     0xbaa4b0: movz            x1, #0x1
    // 0xbaa4b4: r0 = AllocateContext()
    //     0xbaa4b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbaa4b8: mov             x1, x0
    // 0xbaa4bc: ldur            x0, [fp, #-8]
    // 0xbaa4c0: stur            x1, [fp, #-0x20]
    // 0xbaa4c4: StoreField: r1->field_f = r0
    //     0xbaa4c4: stur            w0, [x1, #0xf]
    // 0xbaa4c8: LoadField: r2 = r0->field_b
    //     0xbaa4c8: ldur            w2, [x0, #0xb]
    // 0xbaa4cc: DecompressPointer r2
    //     0xbaa4cc: add             x2, x2, HEAP, lsl #32
    // 0xbaa4d0: stur            x2, [fp, #-0x18]
    // 0xbaa4d4: cmp             w2, NULL
    // 0xbaa4d8: b.eq            #0xbaa808
    // 0xbaa4dc: LoadField: r3 = r2->field_b
    //     0xbaa4dc: ldur            w3, [x2, #0xb]
    // 0xbaa4e0: DecompressPointer r3
    //     0xbaa4e0: add             x3, x3, HEAP, lsl #32
    // 0xbaa4e4: stur            x3, [fp, #-0x10]
    // 0xbaa4e8: r0 = InkWell()
    //     0xbaa4e8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbaa4ec: mov             x3, x0
    // 0xbaa4f0: ldur            x0, [fp, #-0x10]
    // 0xbaa4f4: stur            x3, [fp, #-0x28]
    // 0xbaa4f8: StoreField: r3->field_b = r0
    //     0xbaa4f8: stur            w0, [x3, #0xb]
    // 0xbaa4fc: ldur            x2, [fp, #-0x20]
    // 0xbaa500: r1 = Function '<anonymous closure>':.
    //     0xbaa500: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b28] AnonymousClosure: (0xbaa80c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xbaa504: ldr             x1, [x1, #0xb28]
    // 0xbaa508: r0 = AllocateClosure()
    //     0xbaa508: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaa50c: mov             x1, x0
    // 0xbaa510: ldur            x0, [fp, #-0x28]
    // 0xbaa514: StoreField: r0->field_f = r1
    //     0xbaa514: stur            w1, [x0, #0xf]
    // 0xbaa518: r1 = true
    //     0xbaa518: add             x1, NULL, #0x20  ; true
    // 0xbaa51c: StoreField: r0->field_43 = r1
    //     0xbaa51c: stur            w1, [x0, #0x43]
    // 0xbaa520: r2 = Instance_BoxShape
    //     0xbaa520: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbaa524: ldr             x2, [x2, #0x80]
    // 0xbaa528: StoreField: r0->field_47 = r2
    //     0xbaa528: stur            w2, [x0, #0x47]
    // 0xbaa52c: StoreField: r0->field_6f = r1
    //     0xbaa52c: stur            w1, [x0, #0x6f]
    // 0xbaa530: r3 = false
    //     0xbaa530: add             x3, NULL, #0x30  ; false
    // 0xbaa534: StoreField: r0->field_73 = r3
    //     0xbaa534: stur            w3, [x0, #0x73]
    // 0xbaa538: StoreField: r0->field_83 = r1
    //     0xbaa538: stur            w1, [x0, #0x83]
    // 0xbaa53c: StoreField: r0->field_7b = r3
    //     0xbaa53c: stur            w3, [x0, #0x7b]
    // 0xbaa540: ldur            x4, [fp, #-8]
    // 0xbaa544: LoadField: r5 = r4->field_13
    //     0xbaa544: ldur            w5, [x4, #0x13]
    // 0xbaa548: DecompressPointer r5
    //     0xbaa548: add             x5, x5, HEAP, lsl #32
    // 0xbaa54c: stur            x5, [fp, #-0x10]
    // 0xbaa550: tbnz            w5, #4, #0xbaa564
    // 0xbaa554: ldur            x4, [fp, #-0x18]
    // 0xbaa558: LoadField: r6 = r4->field_1f
    //     0xbaa558: ldur            w6, [x4, #0x1f]
    // 0xbaa55c: DecompressPointer r6
    //     0xbaa55c: add             x6, x6, HEAP, lsl #32
    // 0xbaa560: b               #0xbaa570
    // 0xbaa564: ldur            x4, [fp, #-0x18]
    // 0xbaa568: LoadField: r6 = r4->field_1b
    //     0xbaa568: ldur            w6, [x4, #0x1b]
    // 0xbaa56c: DecompressPointer r6
    //     0xbaa56c: add             x6, x6, HEAP, lsl #32
    // 0xbaa570: stur            x6, [fp, #-8]
    // 0xbaa574: r0 = InkWell()
    //     0xbaa574: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbaa578: mov             x3, x0
    // 0xbaa57c: ldur            x0, [fp, #-8]
    // 0xbaa580: stur            x3, [fp, #-0x30]
    // 0xbaa584: StoreField: r3->field_b = r0
    //     0xbaa584: stur            w0, [x3, #0xb]
    // 0xbaa588: ldur            x2, [fp, #-0x20]
    // 0xbaa58c: r1 = Function '<anonymous closure>':.
    //     0xbaa58c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b30] AnonymousClosure: (0x9a12c8), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xbaa590: ldr             x1, [x1, #0xb30]
    // 0xbaa594: r0 = AllocateClosure()
    //     0xbaa594: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaa598: mov             x1, x0
    // 0xbaa59c: ldur            x0, [fp, #-0x30]
    // 0xbaa5a0: StoreField: r0->field_f = r1
    //     0xbaa5a0: stur            w1, [x0, #0xf]
    // 0xbaa5a4: r1 = true
    //     0xbaa5a4: add             x1, NULL, #0x20  ; true
    // 0xbaa5a8: StoreField: r0->field_43 = r1
    //     0xbaa5a8: stur            w1, [x0, #0x43]
    // 0xbaa5ac: r2 = Instance_BoxShape
    //     0xbaa5ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbaa5b0: ldr             x2, [x2, #0x80]
    // 0xbaa5b4: StoreField: r0->field_47 = r2
    //     0xbaa5b4: stur            w2, [x0, #0x47]
    // 0xbaa5b8: StoreField: r0->field_6f = r1
    //     0xbaa5b8: stur            w1, [x0, #0x6f]
    // 0xbaa5bc: r2 = false
    //     0xbaa5bc: add             x2, NULL, #0x30  ; false
    // 0xbaa5c0: StoreField: r0->field_73 = r2
    //     0xbaa5c0: stur            w2, [x0, #0x73]
    // 0xbaa5c4: StoreField: r0->field_83 = r1
    //     0xbaa5c4: stur            w1, [x0, #0x83]
    // 0xbaa5c8: StoreField: r0->field_7b = r2
    //     0xbaa5c8: stur            w2, [x0, #0x7b]
    // 0xbaa5cc: r1 = Null
    //     0xbaa5cc: mov             x1, NULL
    // 0xbaa5d0: r2 = 6
    //     0xbaa5d0: movz            x2, #0x6
    // 0xbaa5d4: r0 = AllocateArray()
    //     0xbaa5d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaa5d8: mov             x2, x0
    // 0xbaa5dc: ldur            x0, [fp, #-0x28]
    // 0xbaa5e0: stur            x2, [fp, #-8]
    // 0xbaa5e4: StoreField: r2->field_f = r0
    //     0xbaa5e4: stur            w0, [x2, #0xf]
    // 0xbaa5e8: r16 = Instance_Spacer
    //     0xbaa5e8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbaa5ec: ldr             x16, [x16, #0xf0]
    // 0xbaa5f0: StoreField: r2->field_13 = r16
    //     0xbaa5f0: stur            w16, [x2, #0x13]
    // 0xbaa5f4: ldur            x0, [fp, #-0x30]
    // 0xbaa5f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbaa5f8: stur            w0, [x2, #0x17]
    // 0xbaa5fc: r1 = <Widget>
    //     0xbaa5fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaa600: r0 = AllocateGrowableArray()
    //     0xbaa600: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaa604: mov             x1, x0
    // 0xbaa608: ldur            x0, [fp, #-8]
    // 0xbaa60c: stur            x1, [fp, #-0x20]
    // 0xbaa610: StoreField: r1->field_f = r0
    //     0xbaa610: stur            w0, [x1, #0xf]
    // 0xbaa614: r0 = 6
    //     0xbaa614: movz            x0, #0x6
    // 0xbaa618: StoreField: r1->field_b = r0
    //     0xbaa618: stur            w0, [x1, #0xb]
    // 0xbaa61c: r0 = Row()
    //     0xbaa61c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbaa620: mov             x3, x0
    // 0xbaa624: r0 = Instance_Axis
    //     0xbaa624: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbaa628: stur            x3, [fp, #-8]
    // 0xbaa62c: StoreField: r3->field_f = r0
    //     0xbaa62c: stur            w0, [x3, #0xf]
    // 0xbaa630: r0 = Instance_MainAxisAlignment
    //     0xbaa630: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4daf8] Obj!MainAxisAlignment@d734e1
    //     0xbaa634: ldr             x0, [x0, #0xaf8]
    // 0xbaa638: StoreField: r3->field_13 = r0
    //     0xbaa638: stur            w0, [x3, #0x13]
    // 0xbaa63c: r0 = Instance_MainAxisSize
    //     0xbaa63c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaa640: ldr             x0, [x0, #0xa10]
    // 0xbaa644: ArrayStore: r3[0] = r0  ; List_4
    //     0xbaa644: stur            w0, [x3, #0x17]
    // 0xbaa648: r1 = Instance_CrossAxisAlignment
    //     0xbaa648: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbaa64c: ldr             x1, [x1, #0x890]
    // 0xbaa650: StoreField: r3->field_1b = r1
    //     0xbaa650: stur            w1, [x3, #0x1b]
    // 0xbaa654: r4 = Instance_VerticalDirection
    //     0xbaa654: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaa658: ldr             x4, [x4, #0xa20]
    // 0xbaa65c: StoreField: r3->field_23 = r4
    //     0xbaa65c: stur            w4, [x3, #0x23]
    // 0xbaa660: r5 = Instance_Clip
    //     0xbaa660: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaa664: ldr             x5, [x5, #0x38]
    // 0xbaa668: StoreField: r3->field_2b = r5
    //     0xbaa668: stur            w5, [x3, #0x2b]
    // 0xbaa66c: StoreField: r3->field_2f = rZR
    //     0xbaa66c: stur            xzr, [x3, #0x2f]
    // 0xbaa670: ldur            x1, [fp, #-0x20]
    // 0xbaa674: StoreField: r3->field_b = r1
    //     0xbaa674: stur            w1, [x3, #0xb]
    // 0xbaa678: r1 = Null
    //     0xbaa678: mov             x1, NULL
    // 0xbaa67c: r2 = 2
    //     0xbaa67c: movz            x2, #0x2
    // 0xbaa680: r0 = AllocateArray()
    //     0xbaa680: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbaa684: mov             x2, x0
    // 0xbaa688: ldur            x0, [fp, #-8]
    // 0xbaa68c: stur            x2, [fp, #-0x20]
    // 0xbaa690: StoreField: r2->field_f = r0
    //     0xbaa690: stur            w0, [x2, #0xf]
    // 0xbaa694: r1 = <Widget>
    //     0xbaa694: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbaa698: r0 = AllocateGrowableArray()
    //     0xbaa698: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbaa69c: mov             x2, x0
    // 0xbaa6a0: ldur            x0, [fp, #-0x20]
    // 0xbaa6a4: stur            x2, [fp, #-0x28]
    // 0xbaa6a8: StoreField: r2->field_f = r0
    //     0xbaa6a8: stur            w0, [x2, #0xf]
    // 0xbaa6ac: r0 = 2
    //     0xbaa6ac: movz            x0, #0x2
    // 0xbaa6b0: StoreField: r2->field_b = r0
    //     0xbaa6b0: stur            w0, [x2, #0xb]
    // 0xbaa6b4: ldur            x0, [fp, #-0x10]
    // 0xbaa6b8: tbnz            w0, #4, #0xbaa714
    // 0xbaa6bc: ldur            x0, [fp, #-0x18]
    // 0xbaa6c0: LoadField: r3 = r0->field_f
    //     0xbaa6c0: ldur            w3, [x0, #0xf]
    // 0xbaa6c4: DecompressPointer r3
    //     0xbaa6c4: add             x3, x3, HEAP, lsl #32
    // 0xbaa6c8: mov             x1, x2
    // 0xbaa6cc: stur            x3, [fp, #-8]
    // 0xbaa6d0: r0 = _growToNextCapacity()
    //     0xbaa6d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbaa6d4: ldur            x2, [fp, #-0x28]
    // 0xbaa6d8: r0 = 4
    //     0xbaa6d8: movz            x0, #0x4
    // 0xbaa6dc: StoreField: r2->field_b = r0
    //     0xbaa6dc: stur            w0, [x2, #0xb]
    // 0xbaa6e0: LoadField: r1 = r2->field_f
    //     0xbaa6e0: ldur            w1, [x2, #0xf]
    // 0xbaa6e4: DecompressPointer r1
    //     0xbaa6e4: add             x1, x1, HEAP, lsl #32
    // 0xbaa6e8: ldur            x0, [fp, #-8]
    // 0xbaa6ec: ArrayStore: r1[1] = r0  ; List_4
    //     0xbaa6ec: add             x25, x1, #0x13
    //     0xbaa6f0: str             w0, [x25]
    //     0xbaa6f4: tbz             w0, #0, #0xbaa710
    //     0xbaa6f8: ldurb           w16, [x1, #-1]
    //     0xbaa6fc: ldurb           w17, [x0, #-1]
    //     0xbaa700: and             x16, x17, x16, lsr #2
    //     0xbaa704: tst             x16, HEAP, lsr #32
    //     0xbaa708: b.eq            #0xbaa710
    //     0xbaa70c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbaa710: b               #0xbaa7a0
    // 0xbaa714: r0 = Container()
    //     0xbaa714: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbaa718: mov             x1, x0
    // 0xbaa71c: stur            x0, [fp, #-8]
    // 0xbaa720: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbaa720: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbaa724: r0 = Container()
    //     0xbaa724: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbaa728: ldur            x0, [fp, #-0x28]
    // 0xbaa72c: LoadField: r1 = r0->field_b
    //     0xbaa72c: ldur            w1, [x0, #0xb]
    // 0xbaa730: LoadField: r2 = r0->field_f
    //     0xbaa730: ldur            w2, [x0, #0xf]
    // 0xbaa734: DecompressPointer r2
    //     0xbaa734: add             x2, x2, HEAP, lsl #32
    // 0xbaa738: LoadField: r3 = r2->field_b
    //     0xbaa738: ldur            w3, [x2, #0xb]
    // 0xbaa73c: r2 = LoadInt32Instr(r1)
    //     0xbaa73c: sbfx            x2, x1, #1, #0x1f
    // 0xbaa740: stur            x2, [fp, #-0x38]
    // 0xbaa744: r1 = LoadInt32Instr(r3)
    //     0xbaa744: sbfx            x1, x3, #1, #0x1f
    // 0xbaa748: cmp             x2, x1
    // 0xbaa74c: b.ne            #0xbaa758
    // 0xbaa750: mov             x1, x0
    // 0xbaa754: r0 = _growToNextCapacity()
    //     0xbaa754: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbaa758: ldur            x2, [fp, #-0x28]
    // 0xbaa75c: ldur            x3, [fp, #-0x38]
    // 0xbaa760: add             x0, x3, #1
    // 0xbaa764: lsl             x1, x0, #1
    // 0xbaa768: StoreField: r2->field_b = r1
    //     0xbaa768: stur            w1, [x2, #0xb]
    // 0xbaa76c: LoadField: r1 = r2->field_f
    //     0xbaa76c: ldur            w1, [x2, #0xf]
    // 0xbaa770: DecompressPointer r1
    //     0xbaa770: add             x1, x1, HEAP, lsl #32
    // 0xbaa774: ldur            x0, [fp, #-8]
    // 0xbaa778: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbaa778: add             x25, x1, x3, lsl #2
    //     0xbaa77c: add             x25, x25, #0xf
    //     0xbaa780: str             w0, [x25]
    //     0xbaa784: tbz             w0, #0, #0xbaa7a0
    //     0xbaa788: ldurb           w16, [x1, #-1]
    //     0xbaa78c: ldurb           w17, [x0, #-1]
    //     0xbaa790: and             x16, x17, x16, lsr #2
    //     0xbaa794: tst             x16, HEAP, lsr #32
    //     0xbaa798: b.eq            #0xbaa7a0
    //     0xbaa79c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbaa7a0: r0 = Column()
    //     0xbaa7a0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbaa7a4: r1 = Instance_Axis
    //     0xbaa7a4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbaa7a8: StoreField: r0->field_f = r1
    //     0xbaa7a8: stur            w1, [x0, #0xf]
    // 0xbaa7ac: r1 = Instance_MainAxisAlignment
    //     0xbaa7ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbaa7b0: ldr             x1, [x1, #0xa08]
    // 0xbaa7b4: StoreField: r0->field_13 = r1
    //     0xbaa7b4: stur            w1, [x0, #0x13]
    // 0xbaa7b8: r1 = Instance_MainAxisSize
    //     0xbaa7b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbaa7bc: ldr             x1, [x1, #0xa10]
    // 0xbaa7c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbaa7c0: stur            w1, [x0, #0x17]
    // 0xbaa7c4: r1 = Instance_CrossAxisAlignment
    //     0xbaa7c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbaa7c8: ldr             x1, [x1, #0xa18]
    // 0xbaa7cc: StoreField: r0->field_1b = r1
    //     0xbaa7cc: stur            w1, [x0, #0x1b]
    // 0xbaa7d0: r1 = Instance_VerticalDirection
    //     0xbaa7d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbaa7d4: ldr             x1, [x1, #0xa20]
    // 0xbaa7d8: StoreField: r0->field_23 = r1
    //     0xbaa7d8: stur            w1, [x0, #0x23]
    // 0xbaa7dc: r1 = Instance_Clip
    //     0xbaa7dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbaa7e0: ldr             x1, [x1, #0x38]
    // 0xbaa7e4: StoreField: r0->field_2b = r1
    //     0xbaa7e4: stur            w1, [x0, #0x2b]
    // 0xbaa7e8: StoreField: r0->field_2f = rZR
    //     0xbaa7e8: stur            xzr, [x0, #0x2f]
    // 0xbaa7ec: ldur            x1, [fp, #-0x28]
    // 0xbaa7f0: StoreField: r0->field_b = r1
    //     0xbaa7f0: stur            w1, [x0, #0xb]
    // 0xbaa7f4: LeaveFrame
    //     0xbaa7f4: mov             SP, fp
    //     0xbaa7f8: ldp             fp, lr, [SP], #0x10
    // 0xbaa7fc: ret
    //     0xbaa7fc: ret             
    // 0xbaa800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaa800: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaa804: b               #0xbaa4b0
    // 0xbaa808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbaa808: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbaa80c, size: 0x60
    // 0xbaa80c: EnterFrame
    //     0xbaa80c: stp             fp, lr, [SP, #-0x10]!
    //     0xbaa810: mov             fp, SP
    // 0xbaa814: AllocStack(0x8)
    //     0xbaa814: sub             SP, SP, #8
    // 0xbaa818: SetupParameters()
    //     0xbaa818: ldr             x0, [fp, #0x10]
    //     0xbaa81c: ldur            w2, [x0, #0x17]
    //     0xbaa820: add             x2, x2, HEAP, lsl #32
    // 0xbaa824: CheckStackOverflow
    //     0xbaa824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbaa828: cmp             SP, x16
    //     0xbaa82c: b.ls            #0xbaa864
    // 0xbaa830: LoadField: r0 = r2->field_f
    //     0xbaa830: ldur            w0, [x2, #0xf]
    // 0xbaa834: DecompressPointer r0
    //     0xbaa834: add             x0, x0, HEAP, lsl #32
    // 0xbaa838: stur            x0, [fp, #-8]
    // 0xbaa83c: r1 = Function '<anonymous closure>':.
    //     0xbaa83c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b40] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xbaa840: ldr             x1, [x1, #0xb40]
    // 0xbaa844: r0 = AllocateClosure()
    //     0xbaa844: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbaa848: ldur            x1, [fp, #-8]
    // 0xbaa84c: mov             x2, x0
    // 0xbaa850: r0 = setState()
    //     0xbaa850: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbaa854: r0 = Null
    //     0xbaa854: mov             x0, NULL
    // 0xbaa858: LeaveFrame
    //     0xbaa858: mov             SP, fp
    //     0xbaa85c: ldp             fp, lr, [SP], #0x10
    // 0xbaa860: ret
    //     0xbaa860: ret             
    // 0xbaa864: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbaa864: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbaa868: b               #0xbaa830
  }
}

// class id: 4028, size: 0x24, field offset: 0xc
//   const constructor, 
class BrowseAccordian extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80078, size: 0x2c
    // 0xc80078: EnterFrame
    //     0xc80078: stp             fp, lr, [SP, #-0x10]!
    //     0xc8007c: mov             fp, SP
    // 0xc80080: mov             x0, x1
    // 0xc80084: r1 = <BrowseAccordian>
    //     0xc80084: add             x1, PP, #0x48, lsl #12  ; [pp+0x486c8] TypeArguments: <BrowseAccordian>
    //     0xc80088: ldr             x1, [x1, #0x6c8]
    // 0xc8008c: r0 = _AccordionState()
    //     0xc8008c: bl              #0xc800a4  ; Allocate_AccordionStateStub -> _AccordionState (size=0x18)
    // 0xc80090: r1 = false
    //     0xc80090: add             x1, NULL, #0x30  ; false
    // 0xc80094: StoreField: r0->field_13 = r1
    //     0xc80094: stur            w1, [x0, #0x13]
    // 0xc80098: LeaveFrame
    //     0xc80098: mov             SP, fp
    //     0xc8009c: ldp             fp, lr, [SP], #0x10
    // 0xc800a0: ret
    //     0xc800a0: ret             
  }
}
