// lib: , url: package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart

// class id: 1049304, size: 0x8
class :: {
}

// class id: 4590, size: 0x14, field offset: 0x14
//   const constructor, 
class ReturnOrderView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1353028, size: 0x64
    // 0x1353028: EnterFrame
    //     0x1353028: stp             fp, lr, [SP, #-0x10]!
    //     0x135302c: mov             fp, SP
    // 0x1353030: AllocStack(0x18)
    //     0x1353030: sub             SP, SP, #0x18
    // 0x1353034: SetupParameters(ReturnOrderView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1353034: stur            x1, [fp, #-8]
    //     0x1353038: stur            x2, [fp, #-0x10]
    // 0x135303c: r1 = 2
    //     0x135303c: movz            x1, #0x2
    // 0x1353040: r0 = AllocateContext()
    //     0x1353040: bl              #0x16f6108  ; AllocateContextStub
    // 0x1353044: mov             x1, x0
    // 0x1353048: ldur            x0, [fp, #-8]
    // 0x135304c: stur            x1, [fp, #-0x18]
    // 0x1353050: StoreField: r1->field_f = r0
    //     0x1353050: stur            w0, [x1, #0xf]
    // 0x1353054: ldur            x0, [fp, #-0x10]
    // 0x1353058: StoreField: r1->field_13 = r0
    //     0x1353058: stur            w0, [x1, #0x13]
    // 0x135305c: r0 = Obx()
    //     0x135305c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1353060: ldur            x2, [fp, #-0x18]
    // 0x1353064: r1 = Function '<anonymous closure>':.
    //     0x1353064: add             x1, PP, #0x42, lsl #12  ; [pp+0x426c8] AnonymousClosure: (0x135308c), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1353028)
    //     0x1353068: ldr             x1, [x1, #0x6c8]
    // 0x135306c: stur            x0, [fp, #-8]
    // 0x1353070: r0 = AllocateClosure()
    //     0x1353070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1353074: mov             x1, x0
    // 0x1353078: ldur            x0, [fp, #-8]
    // 0x135307c: StoreField: r0->field_b = r1
    //     0x135307c: stur            w1, [x0, #0xb]
    // 0x1353080: LeaveFrame
    //     0x1353080: mov             SP, fp
    //     0x1353084: ldp             fp, lr, [SP], #0x10
    // 0x1353088: ret
    //     0x1353088: ret             
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x135308c, size: 0x1090
    // 0x135308c: EnterFrame
    //     0x135308c: stp             fp, lr, [SP, #-0x10]!
    //     0x1353090: mov             fp, SP
    // 0x1353094: AllocStack(0x68)
    //     0x1353094: sub             SP, SP, #0x68
    // 0x1353098: SetupParameters()
    //     0x1353098: ldr             x0, [fp, #0x10]
    //     0x135309c: ldur            w2, [x0, #0x17]
    //     0x13530a0: add             x2, x2, HEAP, lsl #32
    //     0x13530a4: stur            x2, [fp, #-8]
    // 0x13530a8: CheckStackOverflow
    //     0x13530a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13530ac: cmp             SP, x16
    //     0x13530b0: b.ls            #0x1354104
    // 0x13530b4: LoadField: r1 = r2->field_f
    //     0x13530b4: ldur            w1, [x2, #0xf]
    // 0x13530b8: DecompressPointer r1
    //     0x13530b8: add             x1, x1, HEAP, lsl #32
    // 0x13530bc: r0 = controller()
    //     0x13530bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13530c0: LoadField: r1 = r0->field_57
    //     0x13530c0: ldur            w1, [x0, #0x57]
    // 0x13530c4: DecompressPointer r1
    //     0x13530c4: add             x1, x1, HEAP, lsl #32
    // 0x13530c8: r0 = value()
    //     0x13530c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13530cc: cmp             w0, NULL
    // 0x13530d0: b.ne            #0x13530dc
    // 0x13530d4: r0 = Null
    //     0x13530d4: mov             x0, NULL
    // 0x13530d8: b               #0x13530e8
    // 0x13530dc: LoadField: r1 = r0->field_23
    //     0x13530dc: ldur            w1, [x0, #0x23]
    // 0x13530e0: DecompressPointer r1
    //     0x13530e0: add             x1, x1, HEAP, lsl #32
    // 0x13530e4: mov             x0, x1
    // 0x13530e8: cmp             w0, NULL
    // 0x13530ec: b.eq            #0x135312c
    // 0x13530f0: tbnz            w0, #4, #0x135312c
    // 0x13530f4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13530f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13530f8: ldr             x0, [x0, #0x1c80]
    //     0x13530fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1353100: cmp             w0, w16
    //     0x1353104: b.ne            #0x1353110
    //     0x1353108: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x135310c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1353110: r0 = GetNavigation.size()
    //     0x1353110: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1353114: LoadField: d0 = r0->field_f
    //     0x1353114: ldur            d0, [x0, #0xf]
    // 0x1353118: d1 = 0.080000
    //     0x1353118: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0x135311c: ldr             d1, [x17, #0x798]
    // 0x1353120: fmul            d2, d0, d1
    // 0x1353124: mov             v0.16b, v2.16b
    // 0x1353128: b               #0x1353160
    // 0x135312c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x135312c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1353130: ldr             x0, [x0, #0x1c80]
    //     0x1353134: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1353138: cmp             w0, w16
    //     0x135313c: b.ne            #0x1353148
    //     0x1353140: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1353144: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1353148: r0 = GetNavigation.size()
    //     0x1353148: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x135314c: LoadField: d0 = r0->field_f
    //     0x135314c: ldur            d0, [x0, #0xf]
    // 0x1353150: d1 = 0.170000
    //     0x1353150: add             x17, PP, #0x33, lsl #12  ; [pp+0x33f10] IMM: double(0.17) from 0x3fc5c28f5c28f5c3
    //     0x1353154: ldr             d1, [x17, #0xf10]
    // 0x1353158: fmul            d2, d0, d1
    // 0x135315c: mov             v0.16b, v2.16b
    // 0x1353160: ldur            x0, [fp, #-8]
    // 0x1353164: stur            d0, [fp, #-0x50]
    // 0x1353168: r1 = _ConstMap len:11
    //     0x1353168: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x135316c: ldr             x1, [x1, #0xc28]
    // 0x1353170: r2 = 8
    //     0x1353170: movz            x2, #0x8
    // 0x1353174: r0 = []()
    //     0x1353174: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1353178: stur            x0, [fp, #-0x10]
    // 0x135317c: r0 = BoxDecoration()
    //     0x135317c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1353180: mov             x2, x0
    // 0x1353184: r0 = Instance_Color
    //     0x1353184: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1353188: stur            x2, [fp, #-0x18]
    // 0x135318c: StoreField: r2->field_7 = r0
    //     0x135318c: stur            w0, [x2, #7]
    // 0x1353190: r0 = Instance_BorderRadius
    //     0x1353190: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0x1353194: ldr             x0, [x0, #0xbf8]
    // 0x1353198: StoreField: r2->field_13 = r0
    //     0x1353198: stur            w0, [x2, #0x13]
    // 0x135319c: ldur            x0, [fp, #-0x10]
    // 0x13531a0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13531a0: stur            w0, [x2, #0x17]
    // 0x13531a4: r0 = Instance_BoxShape
    //     0x13531a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13531a8: ldr             x0, [x0, #0x80]
    // 0x13531ac: StoreField: r2->field_23 = r0
    //     0x13531ac: stur            w0, [x2, #0x23]
    // 0x13531b0: ldur            x3, [fp, #-8]
    // 0x13531b4: LoadField: r1 = r3->field_f
    //     0x13531b4: ldur            w1, [x3, #0xf]
    // 0x13531b8: DecompressPointer r1
    //     0x13531b8: add             x1, x1, HEAP, lsl #32
    // 0x13531bc: r0 = controller()
    //     0x13531bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13531c0: LoadField: r1 = r0->field_57
    //     0x13531c0: ldur            w1, [x0, #0x57]
    // 0x13531c4: DecompressPointer r1
    //     0x13531c4: add             x1, x1, HEAP, lsl #32
    // 0x13531c8: r0 = value()
    //     0x13531c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13531cc: cmp             w0, NULL
    // 0x13531d0: b.ne            #0x13531dc
    // 0x13531d4: r0 = Null
    //     0x13531d4: mov             x0, NULL
    // 0x13531d8: b               #0x13531e8
    // 0x13531dc: LoadField: r1 = r0->field_23
    //     0x13531dc: ldur            w1, [x0, #0x23]
    // 0x13531e0: DecompressPointer r1
    //     0x13531e0: add             x1, x1, HEAP, lsl #32
    // 0x13531e4: mov             x0, x1
    // 0x13531e8: cmp             w0, NULL
    // 0x13531ec: b.ne            #0x135323c
    // 0x13531f0: ldur            x3, [fp, #-8]
    // 0x13531f4: r5 = true
    //     0x13531f4: add             x5, NULL, #0x20  ; true
    // 0x13531f8: r4 = false
    //     0x13531f8: add             x4, NULL, #0x30  ; false
    // 0x13531fc: r8 = Instance_MainAxisSize
    //     0x13531fc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1353200: ldr             x8, [x8, #0xa10]
    // 0x1353204: r6 = Instance_FlexFit
    //     0x1353204: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1353208: ldr             x6, [x6, #0xe08]
    // 0x135320c: r7 = Instance_Axis
    //     0x135320c: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1353210: r10 = Instance_VerticalDirection
    //     0x1353210: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1353214: ldr             x10, [x10, #0xa20]
    // 0x1353218: r9 = Instance_CrossAxisAlignment
    //     0x1353218: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135321c: ldr             x9, [x9, #0xa18]
    // 0x1353220: r0 = Instance_BorderSide
    //     0x1353220: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1353224: ldr             x0, [x0, #0xe20]
    // 0x1353228: r11 = Instance_Clip
    //     0x1353228: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x135322c: ldr             x11, [x11, #0x38]
    // 0x1353230: d0 = 30.000000
    //     0x1353230: fmov            d0, #30.00000000
    // 0x1353234: r2 = 1
    //     0x1353234: movz            x2, #0x1
    // 0x1353238: b               #0x13535a0
    // 0x135323c: tbnz            w0, #4, #0x1353558
    // 0x1353240: ldur            x2, [fp, #-8]
    // 0x1353244: LoadField: r1 = r2->field_f
    //     0x1353244: ldur            w1, [x2, #0xf]
    // 0x1353248: DecompressPointer r1
    //     0x1353248: add             x1, x1, HEAP, lsl #32
    // 0x135324c: r0 = controller()
    //     0x135324c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353250: LoadField: r1 = r0->field_8f
    //     0x1353250: ldur            w1, [x0, #0x8f]
    // 0x1353254: DecompressPointer r1
    //     0x1353254: add             x1, x1, HEAP, lsl #32
    // 0x1353258: r0 = value()
    //     0x1353258: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135325c: tbnz            w0, #4, #0x1353274
    // 0x1353260: ldur            x2, [fp, #-8]
    // 0x1353264: r1 = Function '<anonymous closure>':.
    //     0x1353264: add             x1, PP, #0x42, lsl #12  ; [pp+0x426d0] AnonymousClosure: (0x13541ec), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1362c38)
    //     0x1353268: ldr             x1, [x1, #0x6d0]
    // 0x135326c: r0 = AllocateClosure()
    //     0x135326c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1353270: b               #0x1353278
    // 0x1353274: r0 = Null
    //     0x1353274: mov             x0, NULL
    // 0x1353278: ldur            x2, [fp, #-8]
    // 0x135327c: stur            x0, [fp, #-0x10]
    // 0x1353280: r16 = <EdgeInsets>
    //     0x1353280: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1353284: ldr             x16, [x16, #0xda0]
    // 0x1353288: r30 = Instance_EdgeInsets
    //     0x1353288: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x135328c: ldr             lr, [lr, #0x1f0]
    // 0x1353290: stp             lr, x16, [SP]
    // 0x1353294: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1353294: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1353298: r0 = all()
    //     0x1353298: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x135329c: ldur            x2, [fp, #-8]
    // 0x13532a0: stur            x0, [fp, #-0x20]
    // 0x13532a4: LoadField: r1 = r2->field_13
    //     0x13532a4: ldur            w1, [x2, #0x13]
    // 0x13532a8: DecompressPointer r1
    //     0x13532a8: add             x1, x1, HEAP, lsl #32
    // 0x13532ac: r0 = of()
    //     0x13532ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13532b0: LoadField: r1 = r0->field_5b
    //     0x13532b0: ldur            w1, [x0, #0x5b]
    // 0x13532b4: DecompressPointer r1
    //     0x13532b4: add             x1, x1, HEAP, lsl #32
    // 0x13532b8: r16 = <Color>
    //     0x13532b8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13532bc: ldr             x16, [x16, #0xf80]
    // 0x13532c0: stp             x1, x16, [SP]
    // 0x13532c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13532c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13532c8: r0 = all()
    //     0x13532c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13532cc: ldur            x2, [fp, #-8]
    // 0x13532d0: stur            x0, [fp, #-0x28]
    // 0x13532d4: LoadField: r1 = r2->field_f
    //     0x13532d4: ldur            w1, [x2, #0xf]
    // 0x13532d8: DecompressPointer r1
    //     0x13532d8: add             x1, x1, HEAP, lsl #32
    // 0x13532dc: r0 = controller()
    //     0x13532dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13532e0: LoadField: r1 = r0->field_8f
    //     0x13532e0: ldur            w1, [x0, #0x8f]
    // 0x13532e4: DecompressPointer r1
    //     0x13532e4: add             x1, x1, HEAP, lsl #32
    // 0x13532e8: r0 = value()
    //     0x13532e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13532ec: tbnz            w0, #4, #0x1353310
    // 0x13532f0: ldur            x2, [fp, #-8]
    // 0x13532f4: LoadField: r1 = r2->field_13
    //     0x13532f4: ldur            w1, [x2, #0x13]
    // 0x13532f8: DecompressPointer r1
    //     0x13532f8: add             x1, x1, HEAP, lsl #32
    // 0x13532fc: r0 = of()
    //     0x13532fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353300: LoadField: r1 = r0->field_5b
    //     0x1353300: ldur            w1, [x0, #0x5b]
    // 0x1353304: DecompressPointer r1
    //     0x1353304: add             x1, x1, HEAP, lsl #32
    // 0x1353308: mov             x4, x1
    // 0x135330c: b               #0x1353320
    // 0x1353310: r1 = Instance_Color
    //     0x1353310: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1353314: d0 = 0.400000
    //     0x1353314: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1353318: r0 = withOpacity()
    //     0x1353318: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x135331c: mov             x4, x0
    // 0x1353320: ldur            x2, [fp, #-8]
    // 0x1353324: ldur            x3, [fp, #-0x10]
    // 0x1353328: ldur            x1, [fp, #-0x20]
    // 0x135332c: ldur            x0, [fp, #-0x28]
    // 0x1353330: r16 = <Color>
    //     0x1353330: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1353334: ldr             x16, [x16, #0xf80]
    // 0x1353338: stp             x4, x16, [SP]
    // 0x135333c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x135333c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1353340: r0 = all()
    //     0x1353340: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1353344: stur            x0, [fp, #-0x30]
    // 0x1353348: r0 = Radius()
    //     0x1353348: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x135334c: d0 = 30.000000
    //     0x135334c: fmov            d0, #30.00000000
    // 0x1353350: stur            x0, [fp, #-0x38]
    // 0x1353354: StoreField: r0->field_7 = d0
    //     0x1353354: stur            d0, [x0, #7]
    // 0x1353358: StoreField: r0->field_f = d0
    //     0x1353358: stur            d0, [x0, #0xf]
    // 0x135335c: r0 = BorderRadius()
    //     0x135335c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1353360: mov             x1, x0
    // 0x1353364: ldur            x0, [fp, #-0x38]
    // 0x1353368: stur            x1, [fp, #-0x40]
    // 0x135336c: StoreField: r1->field_7 = r0
    //     0x135336c: stur            w0, [x1, #7]
    // 0x1353370: StoreField: r1->field_b = r0
    //     0x1353370: stur            w0, [x1, #0xb]
    // 0x1353374: StoreField: r1->field_f = r0
    //     0x1353374: stur            w0, [x1, #0xf]
    // 0x1353378: StoreField: r1->field_13 = r0
    //     0x1353378: stur            w0, [x1, #0x13]
    // 0x135337c: r0 = RoundedRectangleBorder()
    //     0x135337c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1353380: mov             x1, x0
    // 0x1353384: ldur            x0, [fp, #-0x40]
    // 0x1353388: StoreField: r1->field_b = r0
    //     0x1353388: stur            w0, [x1, #0xb]
    // 0x135338c: r0 = Instance_BorderSide
    //     0x135338c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1353390: ldr             x0, [x0, #0xe20]
    // 0x1353394: StoreField: r1->field_7 = r0
    //     0x1353394: stur            w0, [x1, #7]
    // 0x1353398: r16 = <RoundedRectangleBorder>
    //     0x1353398: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x135339c: ldr             x16, [x16, #0xf78]
    // 0x13533a0: stp             x1, x16, [SP]
    // 0x13533a4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13533a4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13533a8: r0 = all()
    //     0x13533a8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13533ac: stur            x0, [fp, #-0x38]
    // 0x13533b0: r0 = ButtonStyle()
    //     0x13533b0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13533b4: mov             x2, x0
    // 0x13533b8: ldur            x0, [fp, #-0x30]
    // 0x13533bc: stur            x2, [fp, #-0x40]
    // 0x13533c0: StoreField: r2->field_b = r0
    //     0x13533c0: stur            w0, [x2, #0xb]
    // 0x13533c4: ldur            x0, [fp, #-0x28]
    // 0x13533c8: StoreField: r2->field_f = r0
    //     0x13533c8: stur            w0, [x2, #0xf]
    // 0x13533cc: ldur            x0, [fp, #-0x20]
    // 0x13533d0: StoreField: r2->field_23 = r0
    //     0x13533d0: stur            w0, [x2, #0x23]
    // 0x13533d4: ldur            x0, [fp, #-0x38]
    // 0x13533d8: StoreField: r2->field_43 = r0
    //     0x13533d8: stur            w0, [x2, #0x43]
    // 0x13533dc: ldur            x3, [fp, #-8]
    // 0x13533e0: LoadField: r1 = r3->field_13
    //     0x13533e0: ldur            w1, [x3, #0x13]
    // 0x13533e4: DecompressPointer r1
    //     0x13533e4: add             x1, x1, HEAP, lsl #32
    // 0x13533e8: r0 = of()
    //     0x13533e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13533ec: LoadField: r1 = r0->field_87
    //     0x13533ec: ldur            w1, [x0, #0x87]
    // 0x13533f0: DecompressPointer r1
    //     0x13533f0: add             x1, x1, HEAP, lsl #32
    // 0x13533f4: LoadField: r0 = r1->field_7
    //     0x13533f4: ldur            w0, [x1, #7]
    // 0x13533f8: DecompressPointer r0
    //     0x13533f8: add             x0, x0, HEAP, lsl #32
    // 0x13533fc: r16 = 14.000000
    //     0x13533fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1353400: ldr             x16, [x16, #0x1d8]
    // 0x1353404: r30 = Instance_Color
    //     0x1353404: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1353408: stp             lr, x16, [SP]
    // 0x135340c: mov             x1, x0
    // 0x1353410: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1353410: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1353414: ldr             x4, [x4, #0xaa0]
    // 0x1353418: r0 = copyWith()
    //     0x1353418: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x135341c: stur            x0, [fp, #-0x20]
    // 0x1353420: r0 = Text()
    //     0x1353420: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1353424: mov             x1, x0
    // 0x1353428: r0 = "Next"
    //     0x1353428: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x135342c: ldr             x0, [x0, #0x3e8]
    // 0x1353430: stur            x1, [fp, #-0x28]
    // 0x1353434: StoreField: r1->field_b = r0
    //     0x1353434: stur            w0, [x1, #0xb]
    // 0x1353438: ldur            x0, [fp, #-0x20]
    // 0x135343c: StoreField: r1->field_13 = r0
    //     0x135343c: stur            w0, [x1, #0x13]
    // 0x1353440: r0 = TextButton()
    //     0x1353440: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1353444: mov             x2, x0
    // 0x1353448: ldur            x0, [fp, #-0x10]
    // 0x135344c: stur            x2, [fp, #-0x20]
    // 0x1353450: StoreField: r2->field_b = r0
    //     0x1353450: stur            w0, [x2, #0xb]
    // 0x1353454: ldur            x0, [fp, #-0x40]
    // 0x1353458: StoreField: r2->field_1b = r0
    //     0x1353458: stur            w0, [x2, #0x1b]
    // 0x135345c: r4 = false
    //     0x135345c: add             x4, NULL, #0x30  ; false
    // 0x1353460: StoreField: r2->field_27 = r4
    //     0x1353460: stur            w4, [x2, #0x27]
    // 0x1353464: r5 = true
    //     0x1353464: add             x5, NULL, #0x20  ; true
    // 0x1353468: StoreField: r2->field_2f = r5
    //     0x1353468: stur            w5, [x2, #0x2f]
    // 0x135346c: ldur            x0, [fp, #-0x28]
    // 0x1353470: StoreField: r2->field_37 = r0
    //     0x1353470: stur            w0, [x2, #0x37]
    // 0x1353474: r1 = <FlexParentData>
    //     0x1353474: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1353478: ldr             x1, [x1, #0xe00]
    // 0x135347c: r0 = Expanded()
    //     0x135347c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1353480: r2 = 1
    //     0x1353480: movz            x2, #0x1
    // 0x1353484: stur            x0, [fp, #-0x10]
    // 0x1353488: StoreField: r0->field_13 = r2
    //     0x1353488: stur            x2, [x0, #0x13]
    // 0x135348c: r6 = Instance_FlexFit
    //     0x135348c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1353490: ldr             x6, [x6, #0xe08]
    // 0x1353494: StoreField: r0->field_1b = r6
    //     0x1353494: stur            w6, [x0, #0x1b]
    // 0x1353498: ldur            x1, [fp, #-0x20]
    // 0x135349c: StoreField: r0->field_b = r1
    //     0x135349c: stur            w1, [x0, #0xb]
    // 0x13534a0: r1 = Null
    //     0x13534a0: mov             x1, NULL
    // 0x13534a4: r2 = 2
    //     0x13534a4: movz            x2, #0x2
    // 0x13534a8: r0 = AllocateArray()
    //     0x13534a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13534ac: mov             x2, x0
    // 0x13534b0: ldur            x0, [fp, #-0x10]
    // 0x13534b4: stur            x2, [fp, #-0x20]
    // 0x13534b8: StoreField: r2->field_f = r0
    //     0x13534b8: stur            w0, [x2, #0xf]
    // 0x13534bc: r1 = <Widget>
    //     0x13534bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13534c0: r0 = AllocateGrowableArray()
    //     0x13534c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13534c4: mov             x1, x0
    // 0x13534c8: ldur            x0, [fp, #-0x20]
    // 0x13534cc: stur            x1, [fp, #-0x10]
    // 0x13534d0: StoreField: r1->field_f = r0
    //     0x13534d0: stur            w0, [x1, #0xf]
    // 0x13534d4: r0 = 2
    //     0x13534d4: movz            x0, #0x2
    // 0x13534d8: StoreField: r1->field_b = r0
    //     0x13534d8: stur            w0, [x1, #0xb]
    // 0x13534dc: r0 = Row()
    //     0x13534dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13534e0: r7 = Instance_Axis
    //     0x13534e0: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13534e4: stur            x0, [fp, #-0x20]
    // 0x13534e8: StoreField: r0->field_f = r7
    //     0x13534e8: stur            w7, [x0, #0xf]
    // 0x13534ec: r1 = Instance_MainAxisAlignment
    //     0x13534ec: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0x13534f0: ldr             x1, [x1, #0xf28]
    // 0x13534f4: StoreField: r0->field_13 = r1
    //     0x13534f4: stur            w1, [x0, #0x13]
    // 0x13534f8: r8 = Instance_MainAxisSize
    //     0x13534f8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13534fc: ldr             x8, [x8, #0xa10]
    // 0x1353500: ArrayStore: r0[0] = r8  ; List_4
    //     0x1353500: stur            w8, [x0, #0x17]
    // 0x1353504: r9 = Instance_CrossAxisAlignment
    //     0x1353504: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1353508: ldr             x9, [x9, #0xa18]
    // 0x135350c: StoreField: r0->field_1b = r9
    //     0x135350c: stur            w9, [x0, #0x1b]
    // 0x1353510: r10 = Instance_VerticalDirection
    //     0x1353510: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1353514: ldr             x10, [x10, #0xa20]
    // 0x1353518: StoreField: r0->field_23 = r10
    //     0x1353518: stur            w10, [x0, #0x23]
    // 0x135351c: r11 = Instance_Clip
    //     0x135351c: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1353520: ldr             x11, [x11, #0x38]
    // 0x1353524: StoreField: r0->field_2b = r11
    //     0x1353524: stur            w11, [x0, #0x2b]
    // 0x1353528: StoreField: r0->field_2f = rZR
    //     0x1353528: stur            xzr, [x0, #0x2f]
    // 0x135352c: ldur            x1, [fp, #-0x10]
    // 0x1353530: StoreField: r0->field_b = r1
    //     0x1353530: stur            w1, [x0, #0xb]
    // 0x1353534: r0 = Padding()
    //     0x1353534: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1353538: mov             x1, x0
    // 0x135353c: r0 = Instance_EdgeInsets
    //     0x135353c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0x1353540: ldr             x0, [x0, #0xf30]
    // 0x1353544: StoreField: r1->field_f = r0
    //     0x1353544: stur            w0, [x1, #0xf]
    // 0x1353548: ldur            x0, [fp, #-0x20]
    // 0x135354c: StoreField: r1->field_b = r0
    //     0x135354c: stur            w0, [x1, #0xb]
    // 0x1353550: mov             x0, x1
    // 0x1353554: b               #0x1354088
    // 0x1353558: ldur            x3, [fp, #-8]
    // 0x135355c: r5 = true
    //     0x135355c: add             x5, NULL, #0x20  ; true
    // 0x1353560: r4 = false
    //     0x1353560: add             x4, NULL, #0x30  ; false
    // 0x1353564: r8 = Instance_MainAxisSize
    //     0x1353564: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1353568: ldr             x8, [x8, #0xa10]
    // 0x135356c: r6 = Instance_FlexFit
    //     0x135356c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1353570: ldr             x6, [x6, #0xe08]
    // 0x1353574: r7 = Instance_Axis
    //     0x1353574: ldr             x7, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1353578: r10 = Instance_VerticalDirection
    //     0x1353578: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x135357c: ldr             x10, [x10, #0xa20]
    // 0x1353580: r9 = Instance_CrossAxisAlignment
    //     0x1353580: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1353584: ldr             x9, [x9, #0xa18]
    // 0x1353588: r0 = Instance_BorderSide
    //     0x1353588: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x135358c: ldr             x0, [x0, #0xe20]
    // 0x1353590: r11 = Instance_Clip
    //     0x1353590: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1353594: ldr             x11, [x11, #0x38]
    // 0x1353598: d0 = 30.000000
    //     0x1353598: fmov            d0, #30.00000000
    // 0x135359c: r2 = 1
    //     0x135359c: movz            x2, #0x1
    // 0x13535a0: LoadField: r1 = r3->field_f
    //     0x13535a0: ldur            w1, [x3, #0xf]
    // 0x13535a4: DecompressPointer r1
    //     0x13535a4: add             x1, x1, HEAP, lsl #32
    // 0x13535a8: r0 = controller()
    //     0x13535a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13535ac: LoadField: r1 = r0->field_6b
    //     0x13535ac: ldur            w1, [x0, #0x6b]
    // 0x13535b0: DecompressPointer r1
    //     0x13535b0: add             x1, x1, HEAP, lsl #32
    // 0x13535b4: r0 = value()
    //     0x13535b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13535b8: cmp             w0, NULL
    // 0x13535bc: b.eq            #0x13535c4
    // 0x13535c0: tbz             w0, #4, #0x13535d0
    // 0x13535c4: r0 = Instance_IconData
    //     0x13535c4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0x13535c8: ldr             x0, [x0, #0xc30]
    // 0x13535cc: b               #0x13535d8
    // 0x13535d0: r0 = Instance_IconData
    //     0x13535d0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0x13535d4: ldr             x0, [x0, #0xc38]
    // 0x13535d8: ldur            x2, [fp, #-8]
    // 0x13535dc: stur            x0, [fp, #-0x10]
    // 0x13535e0: r0 = Icon()
    //     0x13535e0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x13535e4: mov             x1, x0
    // 0x13535e8: ldur            x0, [fp, #-0x10]
    // 0x13535ec: stur            x1, [fp, #-0x20]
    // 0x13535f0: StoreField: r1->field_b = r0
    //     0x13535f0: stur            w0, [x1, #0xb]
    // 0x13535f4: r0 = GetNavigation.size()
    //     0x13535f4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13535f8: LoadField: d0 = r0->field_7
    //     0x13535f8: ldur            d0, [x0, #7]
    // 0x13535fc: d1 = 0.800000
    //     0x13535fc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1353600: ldr             d1, [x17, #0xb28]
    // 0x1353604: fmul            d2, d0, d1
    // 0x1353608: stur            d2, [fp, #-0x58]
    // 0x135360c: r0 = BoxConstraints()
    //     0x135360c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1353610: stur            x0, [fp, #-0x10]
    // 0x1353614: StoreField: r0->field_7 = rZR
    //     0x1353614: stur            xzr, [x0, #7]
    // 0x1353618: ldur            d0, [fp, #-0x58]
    // 0x135361c: StoreField: r0->field_f = d0
    //     0x135361c: stur            d0, [x0, #0xf]
    // 0x1353620: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1353620: stur            xzr, [x0, #0x17]
    // 0x1353624: d0 = inf
    //     0x1353624: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1353628: StoreField: r0->field_1f = d0
    //     0x1353628: stur            d0, [x0, #0x1f]
    // 0x135362c: ldur            x2, [fp, #-8]
    // 0x1353630: LoadField: r1 = r2->field_f
    //     0x1353630: ldur            w1, [x2, #0xf]
    // 0x1353634: DecompressPointer r1
    //     0x1353634: add             x1, x1, HEAP, lsl #32
    // 0x1353638: r0 = controller()
    //     0x1353638: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135363c: LoadField: r1 = r0->field_57
    //     0x135363c: ldur            w1, [x0, #0x57]
    // 0x1353640: DecompressPointer r1
    //     0x1353640: add             x1, x1, HEAP, lsl #32
    // 0x1353644: r0 = value()
    //     0x1353644: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353648: cmp             w0, NULL
    // 0x135364c: b.ne            #0x1353658
    // 0x1353650: r0 = Null
    //     0x1353650: mov             x0, NULL
    // 0x1353654: b               #0x1353664
    // 0x1353658: LoadField: r1 = r0->field_1f
    //     0x1353658: ldur            w1, [x0, #0x1f]
    // 0x135365c: DecompressPointer r1
    //     0x135365c: add             x1, x1, HEAP, lsl #32
    // 0x1353660: mov             x0, x1
    // 0x1353664: cmp             w0, NULL
    // 0x1353668: b.ne            #0x1353674
    // 0x135366c: r4 = ""
    //     0x135366c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1353670: b               #0x1353678
    // 0x1353674: mov             x4, x0
    // 0x1353678: ldur            x2, [fp, #-8]
    // 0x135367c: ldur            x3, [fp, #-0x20]
    // 0x1353680: ldur            x0, [fp, #-0x10]
    // 0x1353684: stur            x4, [fp, #-0x28]
    // 0x1353688: LoadField: r1 = r2->field_13
    //     0x1353688: ldur            w1, [x2, #0x13]
    // 0x135368c: DecompressPointer r1
    //     0x135368c: add             x1, x1, HEAP, lsl #32
    // 0x1353690: r0 = of()
    //     0x1353690: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353694: LoadField: r1 = r0->field_87
    //     0x1353694: ldur            w1, [x0, #0x87]
    // 0x1353698: DecompressPointer r1
    //     0x1353698: add             x1, x1, HEAP, lsl #32
    // 0x135369c: LoadField: r0 = r1->field_2b
    //     0x135369c: ldur            w0, [x1, #0x2b]
    // 0x13536a0: DecompressPointer r0
    //     0x13536a0: add             x0, x0, HEAP, lsl #32
    // 0x13536a4: r16 = 10.000000
    //     0x13536a4: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x13536a8: r30 = Instance_Color
    //     0x13536a8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13536ac: stp             lr, x16, [SP]
    // 0x13536b0: mov             x1, x0
    // 0x13536b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13536b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13536b8: ldr             x4, [x4, #0xaa0]
    // 0x13536bc: r0 = copyWith()
    //     0x13536bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13536c0: stur            x0, [fp, #-0x30]
    // 0x13536c4: r0 = Text()
    //     0x13536c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13536c8: mov             x1, x0
    // 0x13536cc: ldur            x0, [fp, #-0x28]
    // 0x13536d0: stur            x1, [fp, #-0x38]
    // 0x13536d4: StoreField: r1->field_b = r0
    //     0x13536d4: stur            w0, [x1, #0xb]
    // 0x13536d8: ldur            x0, [fp, #-0x30]
    // 0x13536dc: StoreField: r1->field_13 = r0
    //     0x13536dc: stur            w0, [x1, #0x13]
    // 0x13536e0: r0 = ConstrainedBox()
    //     0x13536e0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x13536e4: mov             x1, x0
    // 0x13536e8: ldur            x0, [fp, #-0x10]
    // 0x13536ec: stur            x1, [fp, #-0x28]
    // 0x13536f0: StoreField: r1->field_f = r0
    //     0x13536f0: stur            w0, [x1, #0xf]
    // 0x13536f4: ldur            x0, [fp, #-0x38]
    // 0x13536f8: StoreField: r1->field_b = r0
    //     0x13536f8: stur            w0, [x1, #0xb]
    // 0x13536fc: r0 = Padding()
    //     0x13536fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1353700: mov             x3, x0
    // 0x1353704: r0 = Instance_EdgeInsets
    //     0x1353704: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x1353708: ldr             x0, [x0, #0xc40]
    // 0x135370c: stur            x3, [fp, #-0x10]
    // 0x1353710: StoreField: r3->field_f = r0
    //     0x1353710: stur            w0, [x3, #0xf]
    // 0x1353714: ldur            x0, [fp, #-0x28]
    // 0x1353718: StoreField: r3->field_b = r0
    //     0x1353718: stur            w0, [x3, #0xb]
    // 0x135371c: r1 = Null
    //     0x135371c: mov             x1, NULL
    // 0x1353720: r2 = 4
    //     0x1353720: movz            x2, #0x4
    // 0x1353724: r0 = AllocateArray()
    //     0x1353724: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1353728: mov             x2, x0
    // 0x135372c: ldur            x0, [fp, #-0x20]
    // 0x1353730: stur            x2, [fp, #-0x28]
    // 0x1353734: StoreField: r2->field_f = r0
    //     0x1353734: stur            w0, [x2, #0xf]
    // 0x1353738: ldur            x0, [fp, #-0x10]
    // 0x135373c: StoreField: r2->field_13 = r0
    //     0x135373c: stur            w0, [x2, #0x13]
    // 0x1353740: r1 = <Widget>
    //     0x1353740: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1353744: r0 = AllocateGrowableArray()
    //     0x1353744: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1353748: mov             x1, x0
    // 0x135374c: ldur            x0, [fp, #-0x28]
    // 0x1353750: stur            x1, [fp, #-0x10]
    // 0x1353754: StoreField: r1->field_f = r0
    //     0x1353754: stur            w0, [x1, #0xf]
    // 0x1353758: r2 = 4
    //     0x1353758: movz            x2, #0x4
    // 0x135375c: StoreField: r1->field_b = r2
    //     0x135375c: stur            w2, [x1, #0xb]
    // 0x1353760: r0 = Row()
    //     0x1353760: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1353764: mov             x1, x0
    // 0x1353768: r0 = Instance_Axis
    //     0x1353768: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x135376c: stur            x1, [fp, #-0x20]
    // 0x1353770: StoreField: r1->field_f = r0
    //     0x1353770: stur            w0, [x1, #0xf]
    // 0x1353774: r2 = Instance_MainAxisAlignment
    //     0x1353774: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1353778: ldr             x2, [x2, #0xa08]
    // 0x135377c: StoreField: r1->field_13 = r2
    //     0x135377c: stur            w2, [x1, #0x13]
    // 0x1353780: r3 = Instance_MainAxisSize
    //     0x1353780: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1353784: ldr             x3, [x3, #0xa10]
    // 0x1353788: ArrayStore: r1[0] = r3  ; List_4
    //     0x1353788: stur            w3, [x1, #0x17]
    // 0x135378c: r4 = Instance_CrossAxisAlignment
    //     0x135378c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1353790: ldr             x4, [x4, #0xa18]
    // 0x1353794: StoreField: r1->field_1b = r4
    //     0x1353794: stur            w4, [x1, #0x1b]
    // 0x1353798: r5 = Instance_VerticalDirection
    //     0x1353798: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x135379c: ldr             x5, [x5, #0xa20]
    // 0x13537a0: StoreField: r1->field_23 = r5
    //     0x13537a0: stur            w5, [x1, #0x23]
    // 0x13537a4: r6 = Instance_Clip
    //     0x13537a4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13537a8: ldr             x6, [x6, #0x38]
    // 0x13537ac: StoreField: r1->field_2b = r6
    //     0x13537ac: stur            w6, [x1, #0x2b]
    // 0x13537b0: StoreField: r1->field_2f = rZR
    //     0x13537b0: stur            xzr, [x1, #0x2f]
    // 0x13537b4: ldur            x7, [fp, #-0x10]
    // 0x13537b8: StoreField: r1->field_b = r7
    //     0x13537b8: stur            w7, [x1, #0xb]
    // 0x13537bc: r0 = Padding()
    //     0x13537bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13537c0: mov             x1, x0
    // 0x13537c4: r0 = Instance_EdgeInsets
    //     0x13537c4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x13537c8: ldr             x0, [x0, #0x868]
    // 0x13537cc: stur            x1, [fp, #-0x10]
    // 0x13537d0: StoreField: r1->field_f = r0
    //     0x13537d0: stur            w0, [x1, #0xf]
    // 0x13537d4: ldur            x0, [fp, #-0x20]
    // 0x13537d8: StoreField: r1->field_b = r0
    //     0x13537d8: stur            w0, [x1, #0xb]
    // 0x13537dc: r0 = InkWell()
    //     0x13537dc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13537e0: mov             x3, x0
    // 0x13537e4: ldur            x0, [fp, #-0x10]
    // 0x13537e8: stur            x3, [fp, #-0x20]
    // 0x13537ec: StoreField: r3->field_b = r0
    //     0x13537ec: stur            w0, [x3, #0xb]
    // 0x13537f0: ldur            x2, [fp, #-8]
    // 0x13537f4: r1 = Function '<anonymous closure>':.
    //     0x13537f4: add             x1, PP, #0x42, lsl #12  ; [pp+0x426d8] AnonymousClosure: (0x135411c), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1353028)
    //     0x13537f8: ldr             x1, [x1, #0x6d8]
    // 0x13537fc: r0 = AllocateClosure()
    //     0x13537fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1353800: mov             x1, x0
    // 0x1353804: ldur            x0, [fp, #-0x20]
    // 0x1353808: StoreField: r0->field_f = r1
    //     0x1353808: stur            w1, [x0, #0xf]
    // 0x135380c: r2 = true
    //     0x135380c: add             x2, NULL, #0x20  ; true
    // 0x1353810: StoreField: r0->field_43 = r2
    //     0x1353810: stur            w2, [x0, #0x43]
    // 0x1353814: r1 = Instance_BoxShape
    //     0x1353814: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1353818: ldr             x1, [x1, #0x80]
    // 0x135381c: StoreField: r0->field_47 = r1
    //     0x135381c: stur            w1, [x0, #0x47]
    // 0x1353820: StoreField: r0->field_6f = r2
    //     0x1353820: stur            w2, [x0, #0x6f]
    // 0x1353824: r3 = false
    //     0x1353824: add             x3, NULL, #0x30  ; false
    // 0x1353828: StoreField: r0->field_73 = r3
    //     0x1353828: stur            w3, [x0, #0x73]
    // 0x135382c: StoreField: r0->field_83 = r2
    //     0x135382c: stur            w2, [x0, #0x83]
    // 0x1353830: StoreField: r0->field_7b = r3
    //     0x1353830: stur            w3, [x0, #0x7b]
    // 0x1353834: r1 = <FlexParentData>
    //     0x1353834: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1353838: ldr             x1, [x1, #0xe00]
    // 0x135383c: r0 = Expanded()
    //     0x135383c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1353840: mov             x2, x0
    // 0x1353844: r0 = 1
    //     0x1353844: movz            x0, #0x1
    // 0x1353848: stur            x2, [fp, #-0x10]
    // 0x135384c: StoreField: r2->field_13 = r0
    //     0x135384c: stur            x0, [x2, #0x13]
    // 0x1353850: r0 = Instance_FlexFit
    //     0x1353850: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1353854: ldr             x0, [x0, #0xe08]
    // 0x1353858: StoreField: r2->field_1b = r0
    //     0x1353858: stur            w0, [x2, #0x1b]
    // 0x135385c: ldur            x1, [fp, #-0x20]
    // 0x1353860: StoreField: r2->field_b = r1
    //     0x1353860: stur            w1, [x2, #0xb]
    // 0x1353864: ldur            x3, [fp, #-8]
    // 0x1353868: LoadField: r1 = r3->field_f
    //     0x1353868: ldur            w1, [x3, #0xf]
    // 0x135386c: DecompressPointer r1
    //     0x135386c: add             x1, x1, HEAP, lsl #32
    // 0x1353870: r0 = controller()
    //     0x1353870: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353874: LoadField: r1 = r0->field_57
    //     0x1353874: ldur            w1, [x0, #0x57]
    // 0x1353878: DecompressPointer r1
    //     0x1353878: add             x1, x1, HEAP, lsl #32
    // 0x135387c: r0 = value()
    //     0x135387c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353880: cmp             w0, NULL
    // 0x1353884: b.ne            #0x1353890
    // 0x1353888: r0 = Null
    //     0x1353888: mov             x0, NULL
    // 0x135388c: b               #0x13538b0
    // 0x1353890: LoadField: r1 = r0->field_b
    //     0x1353890: ldur            w1, [x0, #0xb]
    // 0x1353894: DecompressPointer r1
    //     0x1353894: add             x1, x1, HEAP, lsl #32
    // 0x1353898: cmp             w1, NULL
    // 0x135389c: b.ne            #0x13538a8
    // 0x13538a0: r0 = Null
    //     0x13538a0: mov             x0, NULL
    // 0x13538a4: b               #0x13538b0
    // 0x13538a8: LoadField: r0 = r1->field_b
    //     0x13538a8: ldur            w0, [x1, #0xb]
    // 0x13538ac: DecompressPointer r0
    //     0x13538ac: add             x0, x0, HEAP, lsl #32
    // 0x13538b0: ldur            x2, [fp, #-8]
    // 0x13538b4: cbnz            w0, #0x13538c0
    // 0x13538b8: r3 = false
    //     0x13538b8: add             x3, NULL, #0x30  ; false
    // 0x13538bc: b               #0x13538c4
    // 0x13538c0: r3 = true
    //     0x13538c0: add             x3, NULL, #0x20  ; true
    // 0x13538c4: stur            x3, [fp, #-0x20]
    // 0x13538c8: LoadField: r1 = r2->field_f
    //     0x13538c8: ldur            w1, [x2, #0xf]
    // 0x13538cc: DecompressPointer r1
    //     0x13538cc: add             x1, x1, HEAP, lsl #32
    // 0x13538d0: r0 = controller()
    //     0x13538d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13538d4: LoadField: r1 = r0->field_57
    //     0x13538d4: ldur            w1, [x0, #0x57]
    // 0x13538d8: DecompressPointer r1
    //     0x13538d8: add             x1, x1, HEAP, lsl #32
    // 0x13538dc: r0 = value()
    //     0x13538dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13538e0: cmp             w0, NULL
    // 0x13538e4: b.ne            #0x13538f0
    // 0x13538e8: r0 = Null
    //     0x13538e8: mov             x0, NULL
    // 0x13538ec: b               #0x1353910
    // 0x13538f0: LoadField: r1 = r0->field_33
    //     0x13538f0: ldur            w1, [x0, #0x33]
    // 0x13538f4: DecompressPointer r1
    //     0x13538f4: add             x1, x1, HEAP, lsl #32
    // 0x13538f8: cmp             w1, NULL
    // 0x13538fc: b.ne            #0x1353908
    // 0x1353900: r0 = Null
    //     0x1353900: mov             x0, NULL
    // 0x1353904: b               #0x1353910
    // 0x1353908: LoadField: r0 = r1->field_2b
    //     0x1353908: ldur            w0, [x1, #0x2b]
    // 0x135390c: DecompressPointer r0
    //     0x135390c: add             x0, x0, HEAP, lsl #32
    // 0x1353910: cmp             w0, NULL
    // 0x1353914: b.ne            #0x135391c
    // 0x1353918: r0 = ""
    //     0x1353918: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135391c: ldur            x2, [fp, #-8]
    // 0x1353920: stur            x0, [fp, #-0x28]
    // 0x1353924: LoadField: r1 = r2->field_13
    //     0x1353924: ldur            w1, [x2, #0x13]
    // 0x1353928: DecompressPointer r1
    //     0x1353928: add             x1, x1, HEAP, lsl #32
    // 0x135392c: r0 = of()
    //     0x135392c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353930: LoadField: r1 = r0->field_87
    //     0x1353930: ldur            w1, [x0, #0x87]
    // 0x1353934: DecompressPointer r1
    //     0x1353934: add             x1, x1, HEAP, lsl #32
    // 0x1353938: LoadField: r0 = r1->field_2b
    //     0x1353938: ldur            w0, [x1, #0x2b]
    // 0x135393c: DecompressPointer r0
    //     0x135393c: add             x0, x0, HEAP, lsl #32
    // 0x1353940: r16 = 16.000000
    //     0x1353940: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1353944: ldr             x16, [x16, #0x188]
    // 0x1353948: r30 = Instance_Color
    //     0x1353948: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x135394c: stp             lr, x16, [SP]
    // 0x1353950: mov             x1, x0
    // 0x1353954: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1353954: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1353958: ldr             x4, [x4, #0xaa0]
    // 0x135395c: r0 = copyWith()
    //     0x135395c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1353960: stur            x0, [fp, #-0x30]
    // 0x1353964: r0 = Text()
    //     0x1353964: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1353968: mov             x1, x0
    // 0x135396c: ldur            x0, [fp, #-0x28]
    // 0x1353970: stur            x1, [fp, #-0x38]
    // 0x1353974: StoreField: r1->field_b = r0
    //     0x1353974: stur            w0, [x1, #0xb]
    // 0x1353978: ldur            x0, [fp, #-0x30]
    // 0x135397c: StoreField: r1->field_13 = r0
    //     0x135397c: stur            w0, [x1, #0x13]
    // 0x1353980: r0 = Padding()
    //     0x1353980: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1353984: mov             x2, x0
    // 0x1353988: r0 = Instance_EdgeInsets
    //     0x1353988: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0x135398c: ldr             x0, [x0, #0xc50]
    // 0x1353990: stur            x2, [fp, #-0x28]
    // 0x1353994: StoreField: r2->field_f = r0
    //     0x1353994: stur            w0, [x2, #0xf]
    // 0x1353998: ldur            x0, [fp, #-0x38]
    // 0x135399c: StoreField: r2->field_b = r0
    //     0x135399c: stur            w0, [x2, #0xb]
    // 0x13539a0: ldur            x0, [fp, #-8]
    // 0x13539a4: LoadField: r1 = r0->field_f
    //     0x13539a4: ldur            w1, [x0, #0xf]
    // 0x13539a8: DecompressPointer r1
    //     0x13539a8: add             x1, x1, HEAP, lsl #32
    // 0x13539ac: r0 = controller()
    //     0x13539ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13539b0: LoadField: r1 = r0->field_57
    //     0x13539b0: ldur            w1, [x0, #0x57]
    // 0x13539b4: DecompressPointer r1
    //     0x13539b4: add             x1, x1, HEAP, lsl #32
    // 0x13539b8: r0 = value()
    //     0x13539b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13539bc: cmp             w0, NULL
    // 0x13539c0: b.ne            #0x13539cc
    // 0x13539c4: r0 = Null
    //     0x13539c4: mov             x0, NULL
    // 0x13539c8: b               #0x13539ec
    // 0x13539cc: LoadField: r1 = r0->field_b
    //     0x13539cc: ldur            w1, [x0, #0xb]
    // 0x13539d0: DecompressPointer r1
    //     0x13539d0: add             x1, x1, HEAP, lsl #32
    // 0x13539d4: cmp             w1, NULL
    // 0x13539d8: b.ne            #0x13539e4
    // 0x13539dc: r0 = Null
    //     0x13539dc: mov             x0, NULL
    // 0x13539e0: b               #0x13539ec
    // 0x13539e4: LoadField: r0 = r1->field_7
    //     0x13539e4: ldur            w0, [x1, #7]
    // 0x13539e8: DecompressPointer r0
    //     0x13539e8: add             x0, x0, HEAP, lsl #32
    // 0x13539ec: cmp             w0, NULL
    // 0x13539f0: b.ne            #0x13539fc
    // 0x13539f4: r4 = ""
    //     0x13539f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13539f8: b               #0x1353a00
    // 0x13539fc: mov             x4, x0
    // 0x1353a00: ldur            x2, [fp, #-8]
    // 0x1353a04: ldur            x0, [fp, #-0x28]
    // 0x1353a08: ldur            x3, [fp, #-0x20]
    // 0x1353a0c: stur            x4, [fp, #-0x30]
    // 0x1353a10: LoadField: r1 = r2->field_13
    //     0x1353a10: ldur            w1, [x2, #0x13]
    // 0x1353a14: DecompressPointer r1
    //     0x1353a14: add             x1, x1, HEAP, lsl #32
    // 0x1353a18: r0 = of()
    //     0x1353a18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353a1c: LoadField: r1 = r0->field_87
    //     0x1353a1c: ldur            w1, [x0, #0x87]
    // 0x1353a20: DecompressPointer r1
    //     0x1353a20: add             x1, x1, HEAP, lsl #32
    // 0x1353a24: LoadField: r0 = r1->field_7
    //     0x1353a24: ldur            w0, [x1, #7]
    // 0x1353a28: DecompressPointer r0
    //     0x1353a28: add             x0, x0, HEAP, lsl #32
    // 0x1353a2c: r16 = 16.000000
    //     0x1353a2c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1353a30: ldr             x16, [x16, #0x188]
    // 0x1353a34: r30 = Instance_Color
    //     0x1353a34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1353a38: stp             lr, x16, [SP]
    // 0x1353a3c: mov             x1, x0
    // 0x1353a40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1353a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1353a44: ldr             x4, [x4, #0xaa0]
    // 0x1353a48: r0 = copyWith()
    //     0x1353a48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1353a4c: stur            x0, [fp, #-0x38]
    // 0x1353a50: r0 = Text()
    //     0x1353a50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1353a54: mov             x3, x0
    // 0x1353a58: ldur            x0, [fp, #-0x30]
    // 0x1353a5c: stur            x3, [fp, #-0x40]
    // 0x1353a60: StoreField: r3->field_b = r0
    //     0x1353a60: stur            w0, [x3, #0xb]
    // 0x1353a64: ldur            x0, [fp, #-0x38]
    // 0x1353a68: StoreField: r3->field_13 = r0
    //     0x1353a68: stur            w0, [x3, #0x13]
    // 0x1353a6c: r1 = Null
    //     0x1353a6c: mov             x1, NULL
    // 0x1353a70: r2 = 4
    //     0x1353a70: movz            x2, #0x4
    // 0x1353a74: r0 = AllocateArray()
    //     0x1353a74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1353a78: mov             x2, x0
    // 0x1353a7c: ldur            x0, [fp, #-0x28]
    // 0x1353a80: stur            x2, [fp, #-0x30]
    // 0x1353a84: StoreField: r2->field_f = r0
    //     0x1353a84: stur            w0, [x2, #0xf]
    // 0x1353a88: ldur            x0, [fp, #-0x40]
    // 0x1353a8c: StoreField: r2->field_13 = r0
    //     0x1353a8c: stur            w0, [x2, #0x13]
    // 0x1353a90: r1 = <Widget>
    //     0x1353a90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1353a94: r0 = AllocateGrowableArray()
    //     0x1353a94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1353a98: mov             x1, x0
    // 0x1353a9c: ldur            x0, [fp, #-0x30]
    // 0x1353aa0: stur            x1, [fp, #-0x28]
    // 0x1353aa4: StoreField: r1->field_f = r0
    //     0x1353aa4: stur            w0, [x1, #0xf]
    // 0x1353aa8: r2 = 4
    //     0x1353aa8: movz            x2, #0x4
    // 0x1353aac: StoreField: r1->field_b = r2
    //     0x1353aac: stur            w2, [x1, #0xb]
    // 0x1353ab0: r0 = Column()
    //     0x1353ab0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1353ab4: mov             x1, x0
    // 0x1353ab8: r0 = Instance_Axis
    //     0x1353ab8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1353abc: stur            x1, [fp, #-0x30]
    // 0x1353ac0: StoreField: r1->field_f = r0
    //     0x1353ac0: stur            w0, [x1, #0xf]
    // 0x1353ac4: r2 = Instance_MainAxisAlignment
    //     0x1353ac4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1353ac8: ldr             x2, [x2, #0xa08]
    // 0x1353acc: StoreField: r1->field_13 = r2
    //     0x1353acc: stur            w2, [x1, #0x13]
    // 0x1353ad0: r3 = Instance_MainAxisSize
    //     0x1353ad0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1353ad4: ldr             x3, [x3, #0xa10]
    // 0x1353ad8: ArrayStore: r1[0] = r3  ; List_4
    //     0x1353ad8: stur            w3, [x1, #0x17]
    // 0x1353adc: r4 = Instance_CrossAxisAlignment
    //     0x1353adc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1353ae0: ldr             x4, [x4, #0x890]
    // 0x1353ae4: StoreField: r1->field_1b = r4
    //     0x1353ae4: stur            w4, [x1, #0x1b]
    // 0x1353ae8: r4 = Instance_VerticalDirection
    //     0x1353ae8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1353aec: ldr             x4, [x4, #0xa20]
    // 0x1353af0: StoreField: r1->field_23 = r4
    //     0x1353af0: stur            w4, [x1, #0x23]
    // 0x1353af4: r5 = Instance_Clip
    //     0x1353af4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1353af8: ldr             x5, [x5, #0x38]
    // 0x1353afc: StoreField: r1->field_2b = r5
    //     0x1353afc: stur            w5, [x1, #0x2b]
    // 0x1353b00: StoreField: r1->field_2f = rZR
    //     0x1353b00: stur            xzr, [x1, #0x2f]
    // 0x1353b04: ldur            x6, [fp, #-0x28]
    // 0x1353b08: StoreField: r1->field_b = r6
    //     0x1353b08: stur            w6, [x1, #0xb]
    // 0x1353b0c: r0 = Visibility()
    //     0x1353b0c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1353b10: mov             x2, x0
    // 0x1353b14: ldur            x0, [fp, #-0x30]
    // 0x1353b18: stur            x2, [fp, #-0x28]
    // 0x1353b1c: StoreField: r2->field_b = r0
    //     0x1353b1c: stur            w0, [x2, #0xb]
    // 0x1353b20: r0 = Instance_SizedBox
    //     0x1353b20: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1353b24: StoreField: r2->field_f = r0
    //     0x1353b24: stur            w0, [x2, #0xf]
    // 0x1353b28: ldur            x0, [fp, #-0x20]
    // 0x1353b2c: StoreField: r2->field_13 = r0
    //     0x1353b2c: stur            w0, [x2, #0x13]
    // 0x1353b30: r0 = false
    //     0x1353b30: add             x0, NULL, #0x30  ; false
    // 0x1353b34: ArrayStore: r2[0] = r0  ; List_4
    //     0x1353b34: stur            w0, [x2, #0x17]
    // 0x1353b38: StoreField: r2->field_1b = r0
    //     0x1353b38: stur            w0, [x2, #0x1b]
    // 0x1353b3c: StoreField: r2->field_1f = r0
    //     0x1353b3c: stur            w0, [x2, #0x1f]
    // 0x1353b40: StoreField: r2->field_23 = r0
    //     0x1353b40: stur            w0, [x2, #0x23]
    // 0x1353b44: StoreField: r2->field_27 = r0
    //     0x1353b44: stur            w0, [x2, #0x27]
    // 0x1353b48: StoreField: r2->field_2b = r0
    //     0x1353b48: stur            w0, [x2, #0x2b]
    // 0x1353b4c: ldur            x3, [fp, #-8]
    // 0x1353b50: LoadField: r1 = r3->field_f
    //     0x1353b50: ldur            w1, [x3, #0xf]
    // 0x1353b54: DecompressPointer r1
    //     0x1353b54: add             x1, x1, HEAP, lsl #32
    // 0x1353b58: r0 = controller()
    //     0x1353b58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353b5c: LoadField: r1 = r0->field_57
    //     0x1353b5c: ldur            w1, [x0, #0x57]
    // 0x1353b60: DecompressPointer r1
    //     0x1353b60: add             x1, x1, HEAP, lsl #32
    // 0x1353b64: r0 = value()
    //     0x1353b64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353b68: cmp             w0, NULL
    // 0x1353b6c: b.eq            #0x1353b8c
    // 0x1353b70: LoadField: r1 = r0->field_b
    //     0x1353b70: ldur            w1, [x0, #0xb]
    // 0x1353b74: DecompressPointer r1
    //     0x1353b74: add             x1, x1, HEAP, lsl #32
    // 0x1353b78: cmp             w1, NULL
    // 0x1353b7c: b.eq            #0x1353b8c
    // 0x1353b80: LoadField: r0 = r1->field_b
    //     0x1353b80: ldur            w0, [x1, #0xb]
    // 0x1353b84: DecompressPointer r0
    //     0x1353b84: add             x0, x0, HEAP, lsl #32
    // 0x1353b88: cbz             w0, #0x1353b94
    // 0x1353b8c: r0 = 0
    //     0x1353b8c: movz            x0, #0
    // 0x1353b90: b               #0x1353b98
    // 0x1353b94: r0 = 1
    //     0x1353b94: movz            x0, #0x1
    // 0x1353b98: ldur            x2, [fp, #-8]
    // 0x1353b9c: stur            x0, [fp, #-0x48]
    // 0x1353ba0: r16 = <EdgeInsets>
    //     0x1353ba0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1353ba4: ldr             x16, [x16, #0xda0]
    // 0x1353ba8: r30 = Instance_EdgeInsets
    //     0x1353ba8: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x1353bac: ldr             lr, [lr, #0x28]
    // 0x1353bb0: stp             lr, x16, [SP]
    // 0x1353bb4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1353bb4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1353bb8: r0 = all()
    //     0x1353bb8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1353bbc: ldur            x2, [fp, #-8]
    // 0x1353bc0: stur            x0, [fp, #-0x20]
    // 0x1353bc4: LoadField: r1 = r2->field_f
    //     0x1353bc4: ldur            w1, [x2, #0xf]
    // 0x1353bc8: DecompressPointer r1
    //     0x1353bc8: add             x1, x1, HEAP, lsl #32
    // 0x1353bcc: r0 = controller()
    //     0x1353bcc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353bd0: LoadField: r1 = r0->field_8f
    //     0x1353bd0: ldur            w1, [x0, #0x8f]
    // 0x1353bd4: DecompressPointer r1
    //     0x1353bd4: add             x1, x1, HEAP, lsl #32
    // 0x1353bd8: r0 = value()
    //     0x1353bd8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353bdc: tbnz            w0, #4, #0x1353c24
    // 0x1353be0: ldur            x2, [fp, #-8]
    // 0x1353be4: LoadField: r1 = r2->field_f
    //     0x1353be4: ldur            w1, [x2, #0xf]
    // 0x1353be8: DecompressPointer r1
    //     0x1353be8: add             x1, x1, HEAP, lsl #32
    // 0x1353bec: r0 = controller()
    //     0x1353bec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353bf0: LoadField: r1 = r0->field_6b
    //     0x1353bf0: ldur            w1, [x0, #0x6b]
    // 0x1353bf4: DecompressPointer r1
    //     0x1353bf4: add             x1, x1, HEAP, lsl #32
    // 0x1353bf8: r0 = value()
    //     0x1353bf8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353bfc: cmp             w0, NULL
    // 0x1353c00: b.eq            #0x1353c24
    // 0x1353c04: tbnz            w0, #4, #0x1353c24
    // 0x1353c08: ldur            x2, [fp, #-8]
    // 0x1353c0c: LoadField: r1 = r2->field_13
    //     0x1353c0c: ldur            w1, [x2, #0x13]
    // 0x1353c10: DecompressPointer r1
    //     0x1353c10: add             x1, x1, HEAP, lsl #32
    // 0x1353c14: r0 = of()
    //     0x1353c14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353c18: LoadField: r1 = r0->field_5b
    //     0x1353c18: ldur            w1, [x0, #0x5b]
    // 0x1353c1c: DecompressPointer r1
    //     0x1353c1c: add             x1, x1, HEAP, lsl #32
    // 0x1353c20: b               #0x1353c34
    // 0x1353c24: r1 = Instance_Color
    //     0x1353c24: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1353c28: d0 = 0.400000
    //     0x1353c28: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1353c2c: r0 = withOpacity()
    //     0x1353c2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1353c30: mov             x1, x0
    // 0x1353c34: ldur            x2, [fp, #-8]
    // 0x1353c38: ldur            x0, [fp, #-0x20]
    // 0x1353c3c: r16 = <Color>
    //     0x1353c3c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1353c40: ldr             x16, [x16, #0xf80]
    // 0x1353c44: stp             x1, x16, [SP]
    // 0x1353c48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1353c48: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1353c4c: r0 = all()
    //     0x1353c4c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1353c50: stur            x0, [fp, #-0x30]
    // 0x1353c54: r0 = Radius()
    //     0x1353c54: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1353c58: d0 = 30.000000
    //     0x1353c58: fmov            d0, #30.00000000
    // 0x1353c5c: stur            x0, [fp, #-0x38]
    // 0x1353c60: StoreField: r0->field_7 = d0
    //     0x1353c60: stur            d0, [x0, #7]
    // 0x1353c64: StoreField: r0->field_f = d0
    //     0x1353c64: stur            d0, [x0, #0xf]
    // 0x1353c68: r0 = BorderRadius()
    //     0x1353c68: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1353c6c: mov             x1, x0
    // 0x1353c70: ldur            x0, [fp, #-0x38]
    // 0x1353c74: stur            x1, [fp, #-0x40]
    // 0x1353c78: StoreField: r1->field_7 = r0
    //     0x1353c78: stur            w0, [x1, #7]
    // 0x1353c7c: StoreField: r1->field_b = r0
    //     0x1353c7c: stur            w0, [x1, #0xb]
    // 0x1353c80: StoreField: r1->field_f = r0
    //     0x1353c80: stur            w0, [x1, #0xf]
    // 0x1353c84: StoreField: r1->field_13 = r0
    //     0x1353c84: stur            w0, [x1, #0x13]
    // 0x1353c88: r0 = RoundedRectangleBorder()
    //     0x1353c88: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1353c8c: mov             x1, x0
    // 0x1353c90: ldur            x0, [fp, #-0x40]
    // 0x1353c94: StoreField: r1->field_b = r0
    //     0x1353c94: stur            w0, [x1, #0xb]
    // 0x1353c98: r0 = Instance_BorderSide
    //     0x1353c98: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1353c9c: ldr             x0, [x0, #0xe20]
    // 0x1353ca0: StoreField: r1->field_7 = r0
    //     0x1353ca0: stur            w0, [x1, #7]
    // 0x1353ca4: r16 = <RoundedRectangleBorder>
    //     0x1353ca4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1353ca8: ldr             x16, [x16, #0xf78]
    // 0x1353cac: stp             x1, x16, [SP]
    // 0x1353cb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1353cb0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1353cb4: r0 = all()
    //     0x1353cb4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1353cb8: stur            x0, [fp, #-0x38]
    // 0x1353cbc: r0 = ButtonStyle()
    //     0x1353cbc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1353cc0: mov             x1, x0
    // 0x1353cc4: ldur            x0, [fp, #-0x30]
    // 0x1353cc8: stur            x1, [fp, #-0x40]
    // 0x1353ccc: StoreField: r1->field_b = r0
    //     0x1353ccc: stur            w0, [x1, #0xb]
    // 0x1353cd0: ldur            x0, [fp, #-0x20]
    // 0x1353cd4: StoreField: r1->field_23 = r0
    //     0x1353cd4: stur            w0, [x1, #0x23]
    // 0x1353cd8: ldur            x0, [fp, #-0x38]
    // 0x1353cdc: StoreField: r1->field_43 = r0
    //     0x1353cdc: stur            w0, [x1, #0x43]
    // 0x1353ce0: r0 = TextButtonThemeData()
    //     0x1353ce0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1353ce4: mov             x2, x0
    // 0x1353ce8: ldur            x0, [fp, #-0x40]
    // 0x1353cec: stur            x2, [fp, #-0x20]
    // 0x1353cf0: StoreField: r2->field_7 = r0
    //     0x1353cf0: stur            w0, [x2, #7]
    // 0x1353cf4: ldur            x0, [fp, #-8]
    // 0x1353cf8: LoadField: r1 = r0->field_f
    //     0x1353cf8: ldur            w1, [x0, #0xf]
    // 0x1353cfc: DecompressPointer r1
    //     0x1353cfc: add             x1, x1, HEAP, lsl #32
    // 0x1353d00: r0 = controller()
    //     0x1353d00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353d04: LoadField: r1 = r0->field_6b
    //     0x1353d04: ldur            w1, [x0, #0x6b]
    // 0x1353d08: DecompressPointer r1
    //     0x1353d08: add             x1, x1, HEAP, lsl #32
    // 0x1353d0c: r0 = value()
    //     0x1353d0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353d10: cmp             w0, NULL
    // 0x1353d14: b.eq            #0x1353dc4
    // 0x1353d18: tbnz            w0, #4, #0x1353dc4
    // 0x1353d1c: ldur            x2, [fp, #-8]
    // 0x1353d20: LoadField: r1 = r2->field_f
    //     0x1353d20: ldur            w1, [x2, #0xf]
    // 0x1353d24: DecompressPointer r1
    //     0x1353d24: add             x1, x1, HEAP, lsl #32
    // 0x1353d28: r0 = controller()
    //     0x1353d28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353d2c: LoadField: r1 = r0->field_67
    //     0x1353d2c: ldur            w1, [x0, #0x67]
    // 0x1353d30: DecompressPointer r1
    //     0x1353d30: add             x1, x1, HEAP, lsl #32
    // 0x1353d34: r0 = RxnStringExt.isNotEmpty()
    //     0x1353d34: bl              #0x1325838  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxnStringExt.isNotEmpty
    // 0x1353d38: cmp             w0, NULL
    // 0x1353d3c: b.eq            #0x1353dc4
    // 0x1353d40: tbnz            w0, #4, #0x1353dc4
    // 0x1353d44: ldur            x2, [fp, #-8]
    // 0x1353d48: LoadField: r1 = r2->field_f
    //     0x1353d48: ldur            w1, [x2, #0xf]
    // 0x1353d4c: DecompressPointer r1
    //     0x1353d4c: add             x1, x1, HEAP, lsl #32
    // 0x1353d50: r0 = controller()
    //     0x1353d50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353d54: LoadField: r1 = r0->field_db
    //     0x1353d54: ldur            w1, [x0, #0xdb]
    // 0x1353d58: DecompressPointer r1
    //     0x1353d58: add             x1, x1, HEAP, lsl #32
    // 0x1353d5c: LoadField: r0 = r1->field_27
    //     0x1353d5c: ldur            w0, [x1, #0x27]
    // 0x1353d60: DecompressPointer r0
    //     0x1353d60: add             x0, x0, HEAP, lsl #32
    // 0x1353d64: LoadField: r1 = r0->field_7
    //     0x1353d64: ldur            w1, [x0, #7]
    // 0x1353d68: DecompressPointer r1
    //     0x1353d68: add             x1, x1, HEAP, lsl #32
    // 0x1353d6c: LoadField: r0 = r1->field_7
    //     0x1353d6c: ldur            w0, [x1, #7]
    // 0x1353d70: cbz             w0, #0x1353dc4
    // 0x1353d74: ldur            x2, [fp, #-8]
    // 0x1353d78: LoadField: r1 = r2->field_f
    //     0x1353d78: ldur            w1, [x2, #0xf]
    // 0x1353d7c: DecompressPointer r1
    //     0x1353d7c: add             x1, x1, HEAP, lsl #32
    // 0x1353d80: r0 = controller()
    //     0x1353d80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353d84: LoadField: r1 = r0->field_db
    //     0x1353d84: ldur            w1, [x0, #0xdb]
    // 0x1353d88: DecompressPointer r1
    //     0x1353d88: add             x1, x1, HEAP, lsl #32
    // 0x1353d8c: LoadField: r0 = r1->field_27
    //     0x1353d8c: ldur            w0, [x1, #0x27]
    // 0x1353d90: DecompressPointer r0
    //     0x1353d90: add             x0, x0, HEAP, lsl #32
    // 0x1353d94: LoadField: r1 = r0->field_7
    //     0x1353d94: ldur            w1, [x0, #7]
    // 0x1353d98: DecompressPointer r1
    //     0x1353d98: add             x1, x1, HEAP, lsl #32
    // 0x1353d9c: LoadField: r0 = r1->field_7
    //     0x1353d9c: ldur            w0, [x1, #7]
    // 0x1353da0: r1 = LoadInt32Instr(r0)
    //     0x1353da0: sbfx            x1, x0, #1, #0x1f
    // 0x1353da4: cmp             x1, #9
    // 0x1353da8: b.le            #0x1353dc4
    // 0x1353dac: ldur            x2, [fp, #-8]
    // 0x1353db0: r1 = Function '<anonymous closure>':.
    //     0x1353db0: add             x1, PP, #0x42, lsl #12  ; [pp+0x426e0] AnonymousClosure: (0x132588c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView::bottomNavigationBar (0x1369f9c)
    //     0x1353db4: ldr             x1, [x1, #0x6e0]
    // 0x1353db8: r0 = AllocateClosure()
    //     0x1353db8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1353dbc: mov             x2, x0
    // 0x1353dc0: b               #0x1353dc8
    // 0x1353dc4: r2 = Null
    //     0x1353dc4: mov             x2, NULL
    // 0x1353dc8: ldur            x0, [fp, #-8]
    // 0x1353dcc: stur            x2, [fp, #-0x30]
    // 0x1353dd0: LoadField: r1 = r0->field_f
    //     0x1353dd0: ldur            w1, [x0, #0xf]
    // 0x1353dd4: DecompressPointer r1
    //     0x1353dd4: add             x1, x1, HEAP, lsl #32
    // 0x1353dd8: r0 = controller()
    //     0x1353dd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1353ddc: LoadField: r1 = r0->field_57
    //     0x1353ddc: ldur            w1, [x0, #0x57]
    // 0x1353de0: DecompressPointer r1
    //     0x1353de0: add             x1, x1, HEAP, lsl #32
    // 0x1353de4: r0 = value()
    //     0x1353de4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1353de8: cmp             w0, NULL
    // 0x1353dec: b.ne            #0x1353df8
    // 0x1353df0: r0 = Null
    //     0x1353df0: mov             x0, NULL
    // 0x1353df4: b               #0x1353e18
    // 0x1353df8: LoadField: r1 = r0->field_33
    //     0x1353df8: ldur            w1, [x0, #0x33]
    // 0x1353dfc: DecompressPointer r1
    //     0x1353dfc: add             x1, x1, HEAP, lsl #32
    // 0x1353e00: cmp             w1, NULL
    // 0x1353e04: b.ne            #0x1353e10
    // 0x1353e08: r0 = Null
    //     0x1353e08: mov             x0, NULL
    // 0x1353e0c: b               #0x1353e18
    // 0x1353e10: LoadField: r0 = r1->field_27
    //     0x1353e10: ldur            w0, [x1, #0x27]
    // 0x1353e14: DecompressPointer r0
    //     0x1353e14: add             x0, x0, HEAP, lsl #32
    // 0x1353e18: cmp             w0, NULL
    // 0x1353e1c: b.ne            #0x1353e28
    // 0x1353e20: r7 = ""
    //     0x1353e20: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1353e24: b               #0x1353e2c
    // 0x1353e28: mov             x7, x0
    // 0x1353e2c: ldur            x0, [fp, #-8]
    // 0x1353e30: ldur            x6, [fp, #-0x10]
    // 0x1353e34: ldur            x5, [fp, #-0x28]
    // 0x1353e38: ldur            x4, [fp, #-0x48]
    // 0x1353e3c: ldur            x3, [fp, #-0x20]
    // 0x1353e40: ldur            x2, [fp, #-0x30]
    // 0x1353e44: stur            x7, [fp, #-0x38]
    // 0x1353e48: LoadField: r1 = r0->field_13
    //     0x1353e48: ldur            w1, [x0, #0x13]
    // 0x1353e4c: DecompressPointer r1
    //     0x1353e4c: add             x1, x1, HEAP, lsl #32
    // 0x1353e50: r0 = of()
    //     0x1353e50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1353e54: LoadField: r1 = r0->field_87
    //     0x1353e54: ldur            w1, [x0, #0x87]
    // 0x1353e58: DecompressPointer r1
    //     0x1353e58: add             x1, x1, HEAP, lsl #32
    // 0x1353e5c: LoadField: r0 = r1->field_7
    //     0x1353e5c: ldur            w0, [x1, #7]
    // 0x1353e60: DecompressPointer r0
    //     0x1353e60: add             x0, x0, HEAP, lsl #32
    // 0x1353e64: r16 = Instance_Color
    //     0x1353e64: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1353e68: r30 = 14.000000
    //     0x1353e68: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1353e6c: ldr             lr, [lr, #0x1d8]
    // 0x1353e70: stp             lr, x16, [SP]
    // 0x1353e74: mov             x1, x0
    // 0x1353e78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1353e78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1353e7c: ldr             x4, [x4, #0x9b8]
    // 0x1353e80: r0 = copyWith()
    //     0x1353e80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1353e84: stur            x0, [fp, #-8]
    // 0x1353e88: r0 = Text()
    //     0x1353e88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1353e8c: mov             x1, x0
    // 0x1353e90: ldur            x0, [fp, #-0x38]
    // 0x1353e94: stur            x1, [fp, #-0x40]
    // 0x1353e98: StoreField: r1->field_b = r0
    //     0x1353e98: stur            w0, [x1, #0xb]
    // 0x1353e9c: ldur            x0, [fp, #-8]
    // 0x1353ea0: StoreField: r1->field_13 = r0
    //     0x1353ea0: stur            w0, [x1, #0x13]
    // 0x1353ea4: r0 = TextButton()
    //     0x1353ea4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1353ea8: mov             x1, x0
    // 0x1353eac: ldur            x0, [fp, #-0x30]
    // 0x1353eb0: stur            x1, [fp, #-8]
    // 0x1353eb4: StoreField: r1->field_b = r0
    //     0x1353eb4: stur            w0, [x1, #0xb]
    // 0x1353eb8: r0 = false
    //     0x1353eb8: add             x0, NULL, #0x30  ; false
    // 0x1353ebc: StoreField: r1->field_27 = r0
    //     0x1353ebc: stur            w0, [x1, #0x27]
    // 0x1353ec0: r0 = true
    //     0x1353ec0: add             x0, NULL, #0x20  ; true
    // 0x1353ec4: StoreField: r1->field_2f = r0
    //     0x1353ec4: stur            w0, [x1, #0x2f]
    // 0x1353ec8: ldur            x0, [fp, #-0x40]
    // 0x1353ecc: StoreField: r1->field_37 = r0
    //     0x1353ecc: stur            w0, [x1, #0x37]
    // 0x1353ed0: r0 = TextButtonTheme()
    //     0x1353ed0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1353ed4: mov             x2, x0
    // 0x1353ed8: ldur            x0, [fp, #-0x20]
    // 0x1353edc: stur            x2, [fp, #-0x30]
    // 0x1353ee0: StoreField: r2->field_f = r0
    //     0x1353ee0: stur            w0, [x2, #0xf]
    // 0x1353ee4: ldur            x0, [fp, #-8]
    // 0x1353ee8: StoreField: r2->field_b = r0
    //     0x1353ee8: stur            w0, [x2, #0xb]
    // 0x1353eec: r1 = <FlexParentData>
    //     0x1353eec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1353ef0: ldr             x1, [x1, #0xe00]
    // 0x1353ef4: r0 = Expanded()
    //     0x1353ef4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1353ef8: mov             x3, x0
    // 0x1353efc: ldur            x0, [fp, #-0x48]
    // 0x1353f00: stur            x3, [fp, #-8]
    // 0x1353f04: StoreField: r3->field_13 = r0
    //     0x1353f04: stur            x0, [x3, #0x13]
    // 0x1353f08: r0 = Instance_FlexFit
    //     0x1353f08: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1353f0c: ldr             x0, [x0, #0xe08]
    // 0x1353f10: StoreField: r3->field_1b = r0
    //     0x1353f10: stur            w0, [x3, #0x1b]
    // 0x1353f14: ldur            x0, [fp, #-0x30]
    // 0x1353f18: StoreField: r3->field_b = r0
    //     0x1353f18: stur            w0, [x3, #0xb]
    // 0x1353f1c: r1 = Null
    //     0x1353f1c: mov             x1, NULL
    // 0x1353f20: r2 = 4
    //     0x1353f20: movz            x2, #0x4
    // 0x1353f24: r0 = AllocateArray()
    //     0x1353f24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1353f28: mov             x2, x0
    // 0x1353f2c: ldur            x0, [fp, #-0x28]
    // 0x1353f30: stur            x2, [fp, #-0x20]
    // 0x1353f34: StoreField: r2->field_f = r0
    //     0x1353f34: stur            w0, [x2, #0xf]
    // 0x1353f38: ldur            x0, [fp, #-8]
    // 0x1353f3c: StoreField: r2->field_13 = r0
    //     0x1353f3c: stur            w0, [x2, #0x13]
    // 0x1353f40: r1 = <Widget>
    //     0x1353f40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1353f44: r0 = AllocateGrowableArray()
    //     0x1353f44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1353f48: mov             x1, x0
    // 0x1353f4c: ldur            x0, [fp, #-0x20]
    // 0x1353f50: stur            x1, [fp, #-8]
    // 0x1353f54: StoreField: r1->field_f = r0
    //     0x1353f54: stur            w0, [x1, #0xf]
    // 0x1353f58: r0 = 4
    //     0x1353f58: movz            x0, #0x4
    // 0x1353f5c: StoreField: r1->field_b = r0
    //     0x1353f5c: stur            w0, [x1, #0xb]
    // 0x1353f60: r0 = Row()
    //     0x1353f60: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1353f64: mov             x3, x0
    // 0x1353f68: r0 = Instance_Axis
    //     0x1353f68: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1353f6c: stur            x3, [fp, #-0x20]
    // 0x1353f70: StoreField: r3->field_f = r0
    //     0x1353f70: stur            w0, [x3, #0xf]
    // 0x1353f74: r0 = Instance_MainAxisAlignment
    //     0x1353f74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x1353f78: ldr             x0, [x0, #0xa8]
    // 0x1353f7c: StoreField: r3->field_13 = r0
    //     0x1353f7c: stur            w0, [x3, #0x13]
    // 0x1353f80: r0 = Instance_MainAxisSize
    //     0x1353f80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1353f84: ldr             x0, [x0, #0xa10]
    // 0x1353f88: ArrayStore: r3[0] = r0  ; List_4
    //     0x1353f88: stur            w0, [x3, #0x17]
    // 0x1353f8c: r1 = Instance_CrossAxisAlignment
    //     0x1353f8c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1353f90: ldr             x1, [x1, #0xc68]
    // 0x1353f94: StoreField: r3->field_1b = r1
    //     0x1353f94: stur            w1, [x3, #0x1b]
    // 0x1353f98: r4 = Instance_VerticalDirection
    //     0x1353f98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1353f9c: ldr             x4, [x4, #0xa20]
    // 0x1353fa0: StoreField: r3->field_23 = r4
    //     0x1353fa0: stur            w4, [x3, #0x23]
    // 0x1353fa4: r5 = Instance_Clip
    //     0x1353fa4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1353fa8: ldr             x5, [x5, #0x38]
    // 0x1353fac: StoreField: r3->field_2b = r5
    //     0x1353fac: stur            w5, [x3, #0x2b]
    // 0x1353fb0: StoreField: r3->field_2f = rZR
    //     0x1353fb0: stur            xzr, [x3, #0x2f]
    // 0x1353fb4: ldur            x1, [fp, #-8]
    // 0x1353fb8: StoreField: r3->field_b = r1
    //     0x1353fb8: stur            w1, [x3, #0xb]
    // 0x1353fbc: r1 = Null
    //     0x1353fbc: mov             x1, NULL
    // 0x1353fc0: r2 = 6
    //     0x1353fc0: movz            x2, #0x6
    // 0x1353fc4: r0 = AllocateArray()
    //     0x1353fc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1353fc8: mov             x2, x0
    // 0x1353fcc: ldur            x0, [fp, #-0x10]
    // 0x1353fd0: stur            x2, [fp, #-8]
    // 0x1353fd4: StoreField: r2->field_f = r0
    //     0x1353fd4: stur            w0, [x2, #0xf]
    // 0x1353fd8: r16 = Instance_SizedBox
    //     0x1353fd8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1353fdc: ldr             x16, [x16, #0xc70]
    // 0x1353fe0: StoreField: r2->field_13 = r16
    //     0x1353fe0: stur            w16, [x2, #0x13]
    // 0x1353fe4: ldur            x0, [fp, #-0x20]
    // 0x1353fe8: ArrayStore: r2[0] = r0  ; List_4
    //     0x1353fe8: stur            w0, [x2, #0x17]
    // 0x1353fec: r1 = <Widget>
    //     0x1353fec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1353ff0: r0 = AllocateGrowableArray()
    //     0x1353ff0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1353ff4: mov             x1, x0
    // 0x1353ff8: ldur            x0, [fp, #-8]
    // 0x1353ffc: stur            x1, [fp, #-0x10]
    // 0x1354000: StoreField: r1->field_f = r0
    //     0x1354000: stur            w0, [x1, #0xf]
    // 0x1354004: r0 = 6
    //     0x1354004: movz            x0, #0x6
    // 0x1354008: StoreField: r1->field_b = r0
    //     0x1354008: stur            w0, [x1, #0xb]
    // 0x135400c: r0 = Column()
    //     0x135400c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1354010: mov             x1, x0
    // 0x1354014: r0 = Instance_Axis
    //     0x1354014: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1354018: stur            x1, [fp, #-8]
    // 0x135401c: StoreField: r1->field_f = r0
    //     0x135401c: stur            w0, [x1, #0xf]
    // 0x1354020: r0 = Instance_MainAxisAlignment
    //     0x1354020: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1354024: ldr             x0, [x0, #0xa08]
    // 0x1354028: StoreField: r1->field_13 = r0
    //     0x1354028: stur            w0, [x1, #0x13]
    // 0x135402c: r0 = Instance_MainAxisSize
    //     0x135402c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1354030: ldr             x0, [x0, #0xa10]
    // 0x1354034: ArrayStore: r1[0] = r0  ; List_4
    //     0x1354034: stur            w0, [x1, #0x17]
    // 0x1354038: r0 = Instance_CrossAxisAlignment
    //     0x1354038: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135403c: ldr             x0, [x0, #0xa18]
    // 0x1354040: StoreField: r1->field_1b = r0
    //     0x1354040: stur            w0, [x1, #0x1b]
    // 0x1354044: r0 = Instance_VerticalDirection
    //     0x1354044: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1354048: ldr             x0, [x0, #0xa20]
    // 0x135404c: StoreField: r1->field_23 = r0
    //     0x135404c: stur            w0, [x1, #0x23]
    // 0x1354050: r0 = Instance_Clip
    //     0x1354050: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1354054: ldr             x0, [x0, #0x38]
    // 0x1354058: StoreField: r1->field_2b = r0
    //     0x1354058: stur            w0, [x1, #0x2b]
    // 0x135405c: StoreField: r1->field_2f = rZR
    //     0x135405c: stur            xzr, [x1, #0x2f]
    // 0x1354060: ldur            x0, [fp, #-0x10]
    // 0x1354064: StoreField: r1->field_b = r0
    //     0x1354064: stur            w0, [x1, #0xb]
    // 0x1354068: r0 = Padding()
    //     0x1354068: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x135406c: mov             x1, x0
    // 0x1354070: r0 = Instance_EdgeInsets
    //     0x1354070: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x1354074: ldr             x0, [x0, #0xd0]
    // 0x1354078: StoreField: r1->field_f = r0
    //     0x1354078: stur            w0, [x1, #0xf]
    // 0x135407c: ldur            x0, [fp, #-8]
    // 0x1354080: StoreField: r1->field_b = r0
    //     0x1354080: stur            w0, [x1, #0xb]
    // 0x1354084: mov             x0, x1
    // 0x1354088: ldur            d0, [fp, #-0x50]
    // 0x135408c: stur            x0, [fp, #-8]
    // 0x1354090: r0 = Container()
    //     0x1354090: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1354094: stur            x0, [fp, #-0x10]
    // 0x1354098: ldur            x16, [fp, #-0x18]
    // 0x135409c: ldur            lr, [fp, #-8]
    // 0x13540a0: stp             lr, x16, [SP]
    // 0x13540a4: mov             x1, x0
    // 0x13540a8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13540a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13540ac: ldr             x4, [x4, #0x88]
    // 0x13540b0: r0 = Container()
    //     0x13540b0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13540b4: ldur            d0, [fp, #-0x50]
    // 0x13540b8: r0 = inline_Allocate_Double()
    //     0x13540b8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13540bc: add             x0, x0, #0x10
    //     0x13540c0: cmp             x1, x0
    //     0x13540c4: b.ls            #0x135410c
    //     0x13540c8: str             x0, [THR, #0x50]  ; THR::top
    //     0x13540cc: sub             x0, x0, #0xf
    //     0x13540d0: movz            x1, #0xe15c
    //     0x13540d4: movk            x1, #0x3, lsl #16
    //     0x13540d8: stur            x1, [x0, #-1]
    // 0x13540dc: StoreField: r0->field_7 = d0
    //     0x13540dc: stur            d0, [x0, #7]
    // 0x13540e0: stur            x0, [fp, #-8]
    // 0x13540e4: r0 = SizedBox()
    //     0x13540e4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13540e8: ldur            x1, [fp, #-8]
    // 0x13540ec: StoreField: r0->field_13 = r1
    //     0x13540ec: stur            w1, [x0, #0x13]
    // 0x13540f0: ldur            x1, [fp, #-0x10]
    // 0x13540f4: StoreField: r0->field_b = r1
    //     0x13540f4: stur            w1, [x0, #0xb]
    // 0x13540f8: LeaveFrame
    //     0x13540f8: mov             SP, fp
    //     0x13540fc: ldp             fp, lr, [SP], #0x10
    // 0x1354100: ret
    //     0x1354100: ret             
    // 0x1354104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1354104: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1354108: b               #0x13530b4
    // 0x135410c: SaveReg d0
    //     0x135410c: str             q0, [SP, #-0x10]!
    // 0x1354110: r0 = AllocateDouble()
    //     0x1354110: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1354114: RestoreReg d0
    //     0x1354114: ldr             q0, [SP], #0x10
    // 0x1354118: b               #0x13540dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x135411c, size: 0xd0
    // 0x135411c: EnterFrame
    //     0x135411c: stp             fp, lr, [SP, #-0x10]!
    //     0x1354120: mov             fp, SP
    // 0x1354124: AllocStack(0x8)
    //     0x1354124: sub             SP, SP, #8
    // 0x1354128: SetupParameters()
    //     0x1354128: ldr             x0, [fp, #0x10]
    //     0x135412c: ldur            w2, [x0, #0x17]
    //     0x1354130: add             x2, x2, HEAP, lsl #32
    //     0x1354134: stur            x2, [fp, #-8]
    // 0x1354138: CheckStackOverflow
    //     0x1354138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135413c: cmp             SP, x16
    //     0x1354140: b.ls            #0x13541e4
    // 0x1354144: LoadField: r1 = r2->field_f
    //     0x1354144: ldur            w1, [x2, #0xf]
    // 0x1354148: DecompressPointer r1
    //     0x1354148: add             x1, x1, HEAP, lsl #32
    // 0x135414c: r0 = controller()
    //     0x135414c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354150: LoadField: r1 = r0->field_6b
    //     0x1354150: ldur            w1, [x0, #0x6b]
    // 0x1354154: DecompressPointer r1
    //     0x1354154: add             x1, x1, HEAP, lsl #32
    // 0x1354158: r0 = value()
    //     0x1354158: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135415c: cmp             w0, NULL
    // 0x1354160: b.ne            #0x135416c
    // 0x1354164: ldur            x0, [fp, #-8]
    // 0x1354168: b               #0x13541a8
    // 0x135416c: tbnz            w0, #4, #0x13541a4
    // 0x1354170: ldur            x0, [fp, #-8]
    // 0x1354174: LoadField: r1 = r0->field_f
    //     0x1354174: ldur            w1, [x0, #0xf]
    // 0x1354178: DecompressPointer r1
    //     0x1354178: add             x1, x1, HEAP, lsl #32
    // 0x135417c: r0 = controller()
    //     0x135417c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1354180: LoadField: r1 = r0->field_6b
    //     0x1354180: ldur            w1, [x0, #0x6b]
    // 0x1354184: DecompressPointer r1
    //     0x1354184: add             x1, x1, HEAP, lsl #32
    // 0x1354188: r2 = false
    //     0x1354188: add             x2, NULL, #0x30  ; false
    // 0x135418c: r0 = value=()
    //     0x135418c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1354190: ldur            x0, [fp, #-8]
    // 0x1354194: LoadField: r1 = r0->field_f
    //     0x1354194: ldur            w1, [x0, #0xf]
    // 0x1354198: DecompressPointer r1
    //     0x1354198: add             x1, x1, HEAP, lsl #32
    // 0x135419c: r0 = isButtonEnabled()
    //     0x135419c: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x13541a0: b               #0x13541d4
    // 0x13541a4: ldur            x0, [fp, #-8]
    // 0x13541a8: LoadField: r1 = r0->field_f
    //     0x13541a8: ldur            w1, [x0, #0xf]
    // 0x13541ac: DecompressPointer r1
    //     0x13541ac: add             x1, x1, HEAP, lsl #32
    // 0x13541b0: r0 = controller()
    //     0x13541b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13541b4: LoadField: r1 = r0->field_6b
    //     0x13541b4: ldur            w1, [x0, #0x6b]
    // 0x13541b8: DecompressPointer r1
    //     0x13541b8: add             x1, x1, HEAP, lsl #32
    // 0x13541bc: r2 = true
    //     0x13541bc: add             x2, NULL, #0x20  ; true
    // 0x13541c0: r0 = value=()
    //     0x13541c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13541c4: ldur            x0, [fp, #-8]
    // 0x13541c8: LoadField: r1 = r0->field_f
    //     0x13541c8: ldur            w1, [x0, #0xf]
    // 0x13541cc: DecompressPointer r1
    //     0x13541cc: add             x1, x1, HEAP, lsl #32
    // 0x13541d0: r0 = isButtonEnabled()
    //     0x13541d0: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x13541d4: r0 = Null
    //     0x13541d4: mov             x0, NULL
    // 0x13541d8: LeaveFrame
    //     0x13541d8: mov             SP, fp
    //     0x13541dc: ldp             fp, lr, [SP], #0x10
    // 0x13541e0: ret
    //     0x13541e0: ret             
    // 0x13541e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13541e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13541e8: b               #0x1354144
  }
  _ body(/* No info */) {
    // ** addr: 0x14c51e8, size: 0xac
    // 0x14c51e8: EnterFrame
    //     0x14c51e8: stp             fp, lr, [SP, #-0x10]!
    //     0x14c51ec: mov             fp, SP
    // 0x14c51f0: AllocStack(0x18)
    //     0x14c51f0: sub             SP, SP, #0x18
    // 0x14c51f4: SetupParameters(ReturnOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14c51f4: mov             x0, x1
    //     0x14c51f8: stur            x1, [fp, #-8]
    //     0x14c51fc: stur            x2, [fp, #-0x10]
    // 0x14c5200: r1 = 2
    //     0x14c5200: movz            x1, #0x2
    // 0x14c5204: r0 = AllocateContext()
    //     0x14c5204: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c5208: ldur            x2, [fp, #-8]
    // 0x14c520c: stur            x0, [fp, #-0x18]
    // 0x14c5210: StoreField: r0->field_f = r2
    //     0x14c5210: stur            w2, [x0, #0xf]
    // 0x14c5214: ldur            x1, [fp, #-0x10]
    // 0x14c5218: StoreField: r0->field_13 = r1
    //     0x14c5218: stur            w1, [x0, #0x13]
    // 0x14c521c: r0 = Obx()
    //     0x14c521c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14c5220: ldur            x2, [fp, #-0x18]
    // 0x14c5224: r1 = Function '<anonymous closure>':.
    //     0x14c5224: add             x1, PP, #0x42, lsl #12  ; [pp+0x426e8] AnonymousClosure: (0x14c52cc), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c5228: ldr             x1, [x1, #0x6e8]
    // 0x14c522c: stur            x0, [fp, #-0x10]
    // 0x14c5230: r0 = AllocateClosure()
    //     0x14c5230: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c5234: mov             x1, x0
    // 0x14c5238: ldur            x0, [fp, #-0x10]
    // 0x14c523c: StoreField: r0->field_b = r1
    //     0x14c523c: stur            w1, [x0, #0xb]
    // 0x14c5240: r0 = WillPopScope()
    //     0x14c5240: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14c5244: mov             x3, x0
    // 0x14c5248: ldur            x0, [fp, #-0x10]
    // 0x14c524c: stur            x3, [fp, #-0x18]
    // 0x14c5250: StoreField: r3->field_b = r0
    //     0x14c5250: stur            w0, [x3, #0xb]
    // 0x14c5254: ldur            x2, [fp, #-8]
    // 0x14c5258: r1 = Function 'onBackPress':.
    //     0x14c5258: add             x1, PP, #0x42, lsl #12  ; [pp+0x426f0] AnonymousClosure: (0x14c5294), in [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress (0x1441560)
    //     0x14c525c: ldr             x1, [x1, #0x6f0]
    // 0x14c5260: r0 = AllocateClosure()
    //     0x14c5260: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c5264: mov             x1, x0
    // 0x14c5268: ldur            x0, [fp, #-0x18]
    // 0x14c526c: StoreField: r0->field_f = r1
    //     0x14c526c: stur            w1, [x0, #0xf]
    // 0x14c5270: r0 = ColoredBox()
    //     0x14c5270: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x14c5274: r1 = Instance_Color
    //     0x14c5274: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14c5278: ldr             x1, [x1, #0x90]
    // 0x14c527c: StoreField: r0->field_f = r1
    //     0x14c527c: stur            w1, [x0, #0xf]
    // 0x14c5280: ldur            x1, [fp, #-0x18]
    // 0x14c5284: StoreField: r0->field_b = r1
    //     0x14c5284: stur            w1, [x0, #0xb]
    // 0x14c5288: LeaveFrame
    //     0x14c5288: mov             SP, fp
    //     0x14c528c: ldp             fp, lr, [SP], #0x10
    // 0x14c5290: ret
    //     0x14c5290: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14c5294, size: 0x38
    // 0x14c5294: EnterFrame
    //     0x14c5294: stp             fp, lr, [SP, #-0x10]!
    //     0x14c5298: mov             fp, SP
    // 0x14c529c: ldr             x0, [fp, #0x10]
    // 0x14c52a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14c52a0: ldur            w1, [x0, #0x17]
    // 0x14c52a4: DecompressPointer r1
    //     0x14c52a4: add             x1, x1, HEAP, lsl #32
    // 0x14c52a8: CheckStackOverflow
    //     0x14c52a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c52ac: cmp             SP, x16
    //     0x14c52b0: b.ls            #0x14c52c4
    // 0x14c52b4: r0 = onBackPress()
    //     0x14c52b4: bl              #0x1441560  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress
    // 0x14c52b8: LeaveFrame
    //     0x14c52b8: mov             SP, fp
    //     0x14c52bc: ldp             fp, lr, [SP], #0x10
    // 0x14c52c0: ret
    //     0x14c52c0: ret             
    // 0x14c52c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c52c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c52c8: b               #0x14c52b4
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14c52cc, size: 0x1be0
    // 0x14c52cc: EnterFrame
    //     0x14c52cc: stp             fp, lr, [SP, #-0x10]!
    //     0x14c52d0: mov             fp, SP
    // 0x14c52d4: AllocStack(0xa8)
    //     0x14c52d4: sub             SP, SP, #0xa8
    // 0x14c52d8: SetupParameters()
    //     0x14c52d8: ldr             x0, [fp, #0x10]
    //     0x14c52dc: ldur            w2, [x0, #0x17]
    //     0x14c52e0: add             x2, x2, HEAP, lsl #32
    //     0x14c52e4: stur            x2, [fp, #-8]
    // 0x14c52e8: CheckStackOverflow
    //     0x14c52e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c52ec: cmp             SP, x16
    //     0x14c52f0: b.ls            #0x14c6ea4
    // 0x14c52f4: LoadField: r1 = r2->field_f
    //     0x14c52f4: ldur            w1, [x2, #0xf]
    // 0x14c52f8: DecompressPointer r1
    //     0x14c52f8: add             x1, x1, HEAP, lsl #32
    // 0x14c52fc: r0 = controller()
    //     0x14c52fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c5300: LoadField: r1 = r0->field_57
    //     0x14c5300: ldur            w1, [x0, #0x57]
    // 0x14c5304: DecompressPointer r1
    //     0x14c5304: add             x1, x1, HEAP, lsl #32
    // 0x14c5308: r0 = value()
    //     0x14c5308: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c530c: cmp             w0, NULL
    // 0x14c5310: b.eq            #0x14c6e80
    // 0x14c5314: ldur            x2, [fp, #-8]
    // 0x14c5318: r0 = Radius()
    //     0x14c5318: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c531c: d0 = 15.000000
    //     0x14c531c: fmov            d0, #15.00000000
    // 0x14c5320: stur            x0, [fp, #-0x10]
    // 0x14c5324: StoreField: r0->field_7 = d0
    //     0x14c5324: stur            d0, [x0, #7]
    // 0x14c5328: StoreField: r0->field_f = d0
    //     0x14c5328: stur            d0, [x0, #0xf]
    // 0x14c532c: r0 = BorderRadius()
    //     0x14c532c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c5330: mov             x1, x0
    // 0x14c5334: ldur            x0, [fp, #-0x10]
    // 0x14c5338: stur            x1, [fp, #-0x18]
    // 0x14c533c: StoreField: r1->field_7 = r0
    //     0x14c533c: stur            w0, [x1, #7]
    // 0x14c5340: StoreField: r1->field_b = r0
    //     0x14c5340: stur            w0, [x1, #0xb]
    // 0x14c5344: StoreField: r1->field_f = r0
    //     0x14c5344: stur            w0, [x1, #0xf]
    // 0x14c5348: StoreField: r1->field_13 = r0
    //     0x14c5348: stur            w0, [x1, #0x13]
    // 0x14c534c: r0 = RoundedRectangleBorder()
    //     0x14c534c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14c5350: mov             x1, x0
    // 0x14c5354: ldur            x0, [fp, #-0x18]
    // 0x14c5358: stur            x1, [fp, #-0x10]
    // 0x14c535c: StoreField: r1->field_b = r0
    //     0x14c535c: stur            w0, [x1, #0xb]
    // 0x14c5360: r0 = Instance_BorderSide
    //     0x14c5360: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14c5364: ldr             x0, [x0, #0xe20]
    // 0x14c5368: StoreField: r1->field_7 = r0
    //     0x14c5368: stur            w0, [x1, #7]
    // 0x14c536c: r0 = Radius()
    //     0x14c536c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c5370: d0 = 12.000000
    //     0x14c5370: fmov            d0, #12.00000000
    // 0x14c5374: stur            x0, [fp, #-0x18]
    // 0x14c5378: StoreField: r0->field_7 = d0
    //     0x14c5378: stur            d0, [x0, #7]
    // 0x14c537c: StoreField: r0->field_f = d0
    //     0x14c537c: stur            d0, [x0, #0xf]
    // 0x14c5380: r0 = BorderRadius()
    //     0x14c5380: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c5384: mov             x2, x0
    // 0x14c5388: ldur            x0, [fp, #-0x18]
    // 0x14c538c: stur            x2, [fp, #-0x20]
    // 0x14c5390: StoreField: r2->field_7 = r0
    //     0x14c5390: stur            w0, [x2, #7]
    // 0x14c5394: StoreField: r2->field_b = r0
    //     0x14c5394: stur            w0, [x2, #0xb]
    // 0x14c5398: StoreField: r2->field_f = r0
    //     0x14c5398: stur            w0, [x2, #0xf]
    // 0x14c539c: StoreField: r2->field_13 = r0
    //     0x14c539c: stur            w0, [x2, #0x13]
    // 0x14c53a0: ldur            x0, [fp, #-8]
    // 0x14c53a4: LoadField: r1 = r0->field_f
    //     0x14c53a4: ldur            w1, [x0, #0xf]
    // 0x14c53a8: DecompressPointer r1
    //     0x14c53a8: add             x1, x1, HEAP, lsl #32
    // 0x14c53ac: r0 = controller()
    //     0x14c53ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c53b0: LoadField: r1 = r0->field_57
    //     0x14c53b0: ldur            w1, [x0, #0x57]
    // 0x14c53b4: DecompressPointer r1
    //     0x14c53b4: add             x1, x1, HEAP, lsl #32
    // 0x14c53b8: r0 = value()
    //     0x14c53b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c53bc: cmp             w0, NULL
    // 0x14c53c0: b.ne            #0x14c53cc
    // 0x14c53c4: r0 = Null
    //     0x14c53c4: mov             x0, NULL
    // 0x14c53c8: b               #0x14c53ec
    // 0x14c53cc: LoadField: r1 = r0->field_f
    //     0x14c53cc: ldur            w1, [x0, #0xf]
    // 0x14c53d0: DecompressPointer r1
    //     0x14c53d0: add             x1, x1, HEAP, lsl #32
    // 0x14c53d4: cmp             w1, NULL
    // 0x14c53d8: b.ne            #0x14c53e4
    // 0x14c53dc: r0 = Null
    //     0x14c53dc: mov             x0, NULL
    // 0x14c53e0: b               #0x14c53ec
    // 0x14c53e4: LoadField: r0 = r1->field_7
    //     0x14c53e4: ldur            w0, [x1, #7]
    // 0x14c53e8: DecompressPointer r0
    //     0x14c53e8: add             x0, x0, HEAP, lsl #32
    // 0x14c53ec: cmp             w0, NULL
    // 0x14c53f0: b.ne            #0x14c53fc
    // 0x14c53f4: r4 = ""
    //     0x14c53f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c53f8: b               #0x14c5400
    // 0x14c53fc: mov             x4, x0
    // 0x14c5400: ldur            x3, [fp, #-8]
    // 0x14c5404: ldur            x0, [fp, #-0x20]
    // 0x14c5408: stur            x4, [fp, #-0x18]
    // 0x14c540c: r1 = Function '<anonymous closure>':.
    //     0x14c540c: add             x1, PP, #0x42, lsl #12  ; [pp+0x426f8] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x14c5410: ldr             x1, [x1, #0x6f8]
    // 0x14c5414: r2 = Null
    //     0x14c5414: mov             x2, NULL
    // 0x14c5418: r0 = AllocateClosure()
    //     0x14c5418: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c541c: r1 = Function '<anonymous closure>':.
    //     0x14c541c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42700] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14c5420: ldr             x1, [x1, #0x700]
    // 0x14c5424: r2 = Null
    //     0x14c5424: mov             x2, NULL
    // 0x14c5428: stur            x0, [fp, #-0x28]
    // 0x14c542c: r0 = AllocateClosure()
    //     0x14c542c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c5430: stur            x0, [fp, #-0x30]
    // 0x14c5434: r0 = CachedNetworkImage()
    //     0x14c5434: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14c5438: stur            x0, [fp, #-0x38]
    // 0x14c543c: r16 = 56.000000
    //     0x14c543c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14c5440: ldr             x16, [x16, #0xb78]
    // 0x14c5444: r30 = 56.000000
    //     0x14c5444: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14c5448: ldr             lr, [lr, #0xb78]
    // 0x14c544c: stp             lr, x16, [SP, #0x18]
    // 0x14c5450: r16 = Instance_BoxFit
    //     0x14c5450: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14c5454: ldr             x16, [x16, #0x118]
    // 0x14c5458: ldur            lr, [fp, #-0x28]
    // 0x14c545c: stp             lr, x16, [SP, #8]
    // 0x14c5460: ldur            x16, [fp, #-0x30]
    // 0x14c5464: str             x16, [SP]
    // 0x14c5468: mov             x1, x0
    // 0x14c546c: ldur            x2, [fp, #-0x18]
    // 0x14c5470: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14c5470: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14c5474: ldr             x4, [x4, #0xc28]
    // 0x14c5478: r0 = CachedNetworkImage()
    //     0x14c5478: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14c547c: r0 = ClipRRect()
    //     0x14c547c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14c5480: mov             x2, x0
    // 0x14c5484: ldur            x0, [fp, #-0x20]
    // 0x14c5488: stur            x2, [fp, #-0x18]
    // 0x14c548c: StoreField: r2->field_f = r0
    //     0x14c548c: stur            w0, [x2, #0xf]
    // 0x14c5490: r0 = Instance_Clip
    //     0x14c5490: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14c5494: ldr             x0, [x0, #0x138]
    // 0x14c5498: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c5498: stur            w0, [x2, #0x17]
    // 0x14c549c: ldur            x0, [fp, #-0x38]
    // 0x14c54a0: StoreField: r2->field_b = r0
    //     0x14c54a0: stur            w0, [x2, #0xb]
    // 0x14c54a4: ldur            x0, [fp, #-8]
    // 0x14c54a8: LoadField: r1 = r0->field_f
    //     0x14c54a8: ldur            w1, [x0, #0xf]
    // 0x14c54ac: DecompressPointer r1
    //     0x14c54ac: add             x1, x1, HEAP, lsl #32
    // 0x14c54b0: r0 = controller()
    //     0x14c54b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c54b4: LoadField: r1 = r0->field_57
    //     0x14c54b4: ldur            w1, [x0, #0x57]
    // 0x14c54b8: DecompressPointer r1
    //     0x14c54b8: add             x1, x1, HEAP, lsl #32
    // 0x14c54bc: r0 = value()
    //     0x14c54bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c54c0: cmp             w0, NULL
    // 0x14c54c4: b.ne            #0x14c54d0
    // 0x14c54c8: r0 = Null
    //     0x14c54c8: mov             x0, NULL
    // 0x14c54cc: b               #0x14c54f0
    // 0x14c54d0: LoadField: r1 = r0->field_f
    //     0x14c54d0: ldur            w1, [x0, #0xf]
    // 0x14c54d4: DecompressPointer r1
    //     0x14c54d4: add             x1, x1, HEAP, lsl #32
    // 0x14c54d8: cmp             w1, NULL
    // 0x14c54dc: b.ne            #0x14c54e8
    // 0x14c54e0: r0 = Null
    //     0x14c54e0: mov             x0, NULL
    // 0x14c54e4: b               #0x14c54f0
    // 0x14c54e8: LoadField: r0 = r1->field_b
    //     0x14c54e8: ldur            w0, [x1, #0xb]
    // 0x14c54ec: DecompressPointer r0
    //     0x14c54ec: add             x0, x0, HEAP, lsl #32
    // 0x14c54f0: cmp             w0, NULL
    // 0x14c54f4: b.ne            #0x14c5500
    // 0x14c54f8: r1 = ""
    //     0x14c54f8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c54fc: b               #0x14c5504
    // 0x14c5500: mov             x1, x0
    // 0x14c5504: ldur            x2, [fp, #-8]
    // 0x14c5508: r0 = capitalizeFirstWord()
    //     0x14c5508: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14c550c: ldur            x2, [fp, #-8]
    // 0x14c5510: stur            x0, [fp, #-0x20]
    // 0x14c5514: LoadField: r1 = r2->field_13
    //     0x14c5514: ldur            w1, [x2, #0x13]
    // 0x14c5518: DecompressPointer r1
    //     0x14c5518: add             x1, x1, HEAP, lsl #32
    // 0x14c551c: r0 = of()
    //     0x14c551c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5520: LoadField: r1 = r0->field_87
    //     0x14c5520: ldur            w1, [x0, #0x87]
    // 0x14c5524: DecompressPointer r1
    //     0x14c5524: add             x1, x1, HEAP, lsl #32
    // 0x14c5528: LoadField: r0 = r1->field_2b
    //     0x14c5528: ldur            w0, [x1, #0x2b]
    // 0x14c552c: DecompressPointer r0
    //     0x14c552c: add             x0, x0, HEAP, lsl #32
    // 0x14c5530: stur            x0, [fp, #-0x28]
    // 0x14c5534: r1 = Instance_Color
    //     0x14c5534: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c5538: d0 = 0.700000
    //     0x14c5538: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c553c: ldr             d0, [x17, #0xf48]
    // 0x14c5540: r0 = withOpacity()
    //     0x14c5540: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c5544: r16 = 12.000000
    //     0x14c5544: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c5548: ldr             x16, [x16, #0x9e8]
    // 0x14c554c: stp             x16, x0, [SP]
    // 0x14c5550: ldur            x1, [fp, #-0x28]
    // 0x14c5554: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c5554: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c5558: ldr             x4, [x4, #0x9b8]
    // 0x14c555c: r0 = copyWith()
    //     0x14c555c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c5560: stur            x0, [fp, #-0x28]
    // 0x14c5564: r0 = Text()
    //     0x14c5564: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c5568: mov             x2, x0
    // 0x14c556c: ldur            x0, [fp, #-0x20]
    // 0x14c5570: stur            x2, [fp, #-0x30]
    // 0x14c5574: StoreField: r2->field_b = r0
    //     0x14c5574: stur            w0, [x2, #0xb]
    // 0x14c5578: ldur            x0, [fp, #-0x28]
    // 0x14c557c: StoreField: r2->field_13 = r0
    //     0x14c557c: stur            w0, [x2, #0x13]
    // 0x14c5580: r0 = Instance_TextOverflow
    //     0x14c5580: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14c5584: ldr             x0, [x0, #0xe10]
    // 0x14c5588: StoreField: r2->field_2b = r0
    //     0x14c5588: stur            w0, [x2, #0x2b]
    // 0x14c558c: r3 = 4
    //     0x14c558c: movz            x3, #0x4
    // 0x14c5590: StoreField: r2->field_37 = r3
    //     0x14c5590: stur            w3, [x2, #0x37]
    // 0x14c5594: ldur            x4, [fp, #-8]
    // 0x14c5598: LoadField: r1 = r4->field_f
    //     0x14c5598: ldur            w1, [x4, #0xf]
    // 0x14c559c: DecompressPointer r1
    //     0x14c559c: add             x1, x1, HEAP, lsl #32
    // 0x14c55a0: r0 = controller()
    //     0x14c55a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c55a4: LoadField: r1 = r0->field_57
    //     0x14c55a4: ldur            w1, [x0, #0x57]
    // 0x14c55a8: DecompressPointer r1
    //     0x14c55a8: add             x1, x1, HEAP, lsl #32
    // 0x14c55ac: r0 = value()
    //     0x14c55ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c55b0: cmp             w0, NULL
    // 0x14c55b4: b.ne            #0x14c55c0
    // 0x14c55b8: r0 = Null
    //     0x14c55b8: mov             x0, NULL
    // 0x14c55bc: b               #0x14c55e0
    // 0x14c55c0: LoadField: r1 = r0->field_f
    //     0x14c55c0: ldur            w1, [x0, #0xf]
    // 0x14c55c4: DecompressPointer r1
    //     0x14c55c4: add             x1, x1, HEAP, lsl #32
    // 0x14c55c8: cmp             w1, NULL
    // 0x14c55cc: b.ne            #0x14c55d8
    // 0x14c55d0: r0 = Null
    //     0x14c55d0: mov             x0, NULL
    // 0x14c55d4: b               #0x14c55e0
    // 0x14c55d8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14c55d8: ldur            w0, [x1, #0x17]
    // 0x14c55dc: DecompressPointer r0
    //     0x14c55dc: add             x0, x0, HEAP, lsl #32
    // 0x14c55e0: r1 = LoadClassIdInstr(r0)
    //     0x14c55e0: ldur            x1, [x0, #-1]
    //     0x14c55e4: ubfx            x1, x1, #0xc, #0x14
    // 0x14c55e8: r16 = "size"
    //     0x14c55e8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14c55ec: ldr             x16, [x16, #0x9c0]
    // 0x14c55f0: stp             x16, x0, [SP]
    // 0x14c55f4: mov             x0, x1
    // 0x14c55f8: mov             lr, x0
    // 0x14c55fc: ldr             lr, [x21, lr, lsl #3]
    // 0x14c5600: blr             lr
    // 0x14c5604: tbnz            w0, #4, #0x14c573c
    // 0x14c5608: ldur            x0, [fp, #-8]
    // 0x14c560c: r1 = Null
    //     0x14c560c: mov             x1, NULL
    // 0x14c5610: r2 = 8
    //     0x14c5610: movz            x2, #0x8
    // 0x14c5614: r0 = AllocateArray()
    //     0x14c5614: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c5618: stur            x0, [fp, #-0x20]
    // 0x14c561c: r16 = "Size :  "
    //     0x14c561c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0x14c5620: ldr             x16, [x16, #0x758]
    // 0x14c5624: StoreField: r0->field_f = r16
    //     0x14c5624: stur            w16, [x0, #0xf]
    // 0x14c5628: ldur            x2, [fp, #-8]
    // 0x14c562c: LoadField: r1 = r2->field_f
    //     0x14c562c: ldur            w1, [x2, #0xf]
    // 0x14c5630: DecompressPointer r1
    //     0x14c5630: add             x1, x1, HEAP, lsl #32
    // 0x14c5634: r0 = controller()
    //     0x14c5634: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c5638: LoadField: r1 = r0->field_57
    //     0x14c5638: ldur            w1, [x0, #0x57]
    // 0x14c563c: DecompressPointer r1
    //     0x14c563c: add             x1, x1, HEAP, lsl #32
    // 0x14c5640: r0 = value()
    //     0x14c5640: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c5644: cmp             w0, NULL
    // 0x14c5648: b.ne            #0x14c5654
    // 0x14c564c: r0 = Null
    //     0x14c564c: mov             x0, NULL
    // 0x14c5650: b               #0x14c5674
    // 0x14c5654: LoadField: r1 = r0->field_f
    //     0x14c5654: ldur            w1, [x0, #0xf]
    // 0x14c5658: DecompressPointer r1
    //     0x14c5658: add             x1, x1, HEAP, lsl #32
    // 0x14c565c: cmp             w1, NULL
    // 0x14c5660: b.ne            #0x14c566c
    // 0x14c5664: r0 = Null
    //     0x14c5664: mov             x0, NULL
    // 0x14c5668: b               #0x14c5674
    // 0x14c566c: LoadField: r0 = r1->field_f
    //     0x14c566c: ldur            w0, [x1, #0xf]
    // 0x14c5670: DecompressPointer r0
    //     0x14c5670: add             x0, x0, HEAP, lsl #32
    // 0x14c5674: ldur            x3, [fp, #-8]
    // 0x14c5678: ldur            x2, [fp, #-0x20]
    // 0x14c567c: mov             x1, x2
    // 0x14c5680: ArrayStore: r1[1] = r0  ; List_4
    //     0x14c5680: add             x25, x1, #0x13
    //     0x14c5684: str             w0, [x25]
    //     0x14c5688: tbz             w0, #0, #0x14c56a4
    //     0x14c568c: ldurb           w16, [x1, #-1]
    //     0x14c5690: ldurb           w17, [x0, #-1]
    //     0x14c5694: and             x16, x17, x16, lsr #2
    //     0x14c5698: tst             x16, HEAP, lsr #32
    //     0x14c569c: b.eq            #0x14c56a4
    //     0x14c56a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c56a4: r16 = "/ Qty: "
    //     0x14c56a4: add             x16, PP, #0x42, lsl #12  ; [pp+0x425f8] "/ Qty: "
    //     0x14c56a8: ldr             x16, [x16, #0x5f8]
    // 0x14c56ac: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c56ac: stur            w16, [x2, #0x17]
    // 0x14c56b0: LoadField: r1 = r3->field_f
    //     0x14c56b0: ldur            w1, [x3, #0xf]
    // 0x14c56b4: DecompressPointer r1
    //     0x14c56b4: add             x1, x1, HEAP, lsl #32
    // 0x14c56b8: r0 = controller()
    //     0x14c56b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c56bc: LoadField: r1 = r0->field_57
    //     0x14c56bc: ldur            w1, [x0, #0x57]
    // 0x14c56c0: DecompressPointer r1
    //     0x14c56c0: add             x1, x1, HEAP, lsl #32
    // 0x14c56c4: r0 = value()
    //     0x14c56c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c56c8: cmp             w0, NULL
    // 0x14c56cc: b.ne            #0x14c56d8
    // 0x14c56d0: r0 = Null
    //     0x14c56d0: mov             x0, NULL
    // 0x14c56d4: b               #0x14c56f8
    // 0x14c56d8: LoadField: r1 = r0->field_f
    //     0x14c56d8: ldur            w1, [x0, #0xf]
    // 0x14c56dc: DecompressPointer r1
    //     0x14c56dc: add             x1, x1, HEAP, lsl #32
    // 0x14c56e0: cmp             w1, NULL
    // 0x14c56e4: b.ne            #0x14c56f0
    // 0x14c56e8: r0 = Null
    //     0x14c56e8: mov             x0, NULL
    // 0x14c56ec: b               #0x14c56f8
    // 0x14c56f0: LoadField: r0 = r1->field_13
    //     0x14c56f0: ldur            w0, [x1, #0x13]
    // 0x14c56f4: DecompressPointer r0
    //     0x14c56f4: add             x0, x0, HEAP, lsl #32
    // 0x14c56f8: cmp             w0, NULL
    // 0x14c56fc: b.ne            #0x14c5704
    // 0x14c5700: r0 = ""
    //     0x14c5700: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c5704: ldur            x1, [fp, #-0x20]
    // 0x14c5708: ArrayStore: r1[3] = r0  ; List_4
    //     0x14c5708: add             x25, x1, #0x1b
    //     0x14c570c: str             w0, [x25]
    //     0x14c5710: tbz             w0, #0, #0x14c572c
    //     0x14c5714: ldurb           w16, [x1, #-1]
    //     0x14c5718: ldurb           w17, [x0, #-1]
    //     0x14c571c: and             x16, x17, x16, lsr #2
    //     0x14c5720: tst             x16, HEAP, lsr #32
    //     0x14c5724: b.eq            #0x14c572c
    //     0x14c5728: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c572c: ldur            x16, [fp, #-0x20]
    // 0x14c5730: str             x16, [SP]
    // 0x14c5734: r0 = _interpolate()
    //     0x14c5734: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14c5738: b               #0x14c586c
    // 0x14c573c: ldur            x0, [fp, #-8]
    // 0x14c5740: r1 = Null
    //     0x14c5740: mov             x1, NULL
    // 0x14c5744: r2 = 8
    //     0x14c5744: movz            x2, #0x8
    // 0x14c5748: r0 = AllocateArray()
    //     0x14c5748: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c574c: stur            x0, [fp, #-0x20]
    // 0x14c5750: r16 = "Variant : "
    //     0x14c5750: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0x14c5754: ldr             x16, [x16, #0x768]
    // 0x14c5758: StoreField: r0->field_f = r16
    //     0x14c5758: stur            w16, [x0, #0xf]
    // 0x14c575c: ldur            x2, [fp, #-8]
    // 0x14c5760: LoadField: r1 = r2->field_f
    //     0x14c5760: ldur            w1, [x2, #0xf]
    // 0x14c5764: DecompressPointer r1
    //     0x14c5764: add             x1, x1, HEAP, lsl #32
    // 0x14c5768: r0 = controller()
    //     0x14c5768: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c576c: LoadField: r1 = r0->field_57
    //     0x14c576c: ldur            w1, [x0, #0x57]
    // 0x14c5770: DecompressPointer r1
    //     0x14c5770: add             x1, x1, HEAP, lsl #32
    // 0x14c5774: r0 = value()
    //     0x14c5774: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c5778: cmp             w0, NULL
    // 0x14c577c: b.ne            #0x14c5788
    // 0x14c5780: r0 = Null
    //     0x14c5780: mov             x0, NULL
    // 0x14c5784: b               #0x14c57a8
    // 0x14c5788: LoadField: r1 = r0->field_f
    //     0x14c5788: ldur            w1, [x0, #0xf]
    // 0x14c578c: DecompressPointer r1
    //     0x14c578c: add             x1, x1, HEAP, lsl #32
    // 0x14c5790: cmp             w1, NULL
    // 0x14c5794: b.ne            #0x14c57a0
    // 0x14c5798: r0 = Null
    //     0x14c5798: mov             x0, NULL
    // 0x14c579c: b               #0x14c57a8
    // 0x14c57a0: LoadField: r0 = r1->field_f
    //     0x14c57a0: ldur            w0, [x1, #0xf]
    // 0x14c57a4: DecompressPointer r0
    //     0x14c57a4: add             x0, x0, HEAP, lsl #32
    // 0x14c57a8: ldur            x3, [fp, #-8]
    // 0x14c57ac: ldur            x2, [fp, #-0x20]
    // 0x14c57b0: mov             x1, x2
    // 0x14c57b4: ArrayStore: r1[1] = r0  ; List_4
    //     0x14c57b4: add             x25, x1, #0x13
    //     0x14c57b8: str             w0, [x25]
    //     0x14c57bc: tbz             w0, #0, #0x14c57d8
    //     0x14c57c0: ldurb           w16, [x1, #-1]
    //     0x14c57c4: ldurb           w17, [x0, #-1]
    //     0x14c57c8: and             x16, x17, x16, lsr #2
    //     0x14c57cc: tst             x16, HEAP, lsr #32
    //     0x14c57d0: b.eq            #0x14c57d8
    //     0x14c57d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c57d8: r16 = "/ Qty: "
    //     0x14c57d8: add             x16, PP, #0x42, lsl #12  ; [pp+0x425f8] "/ Qty: "
    //     0x14c57dc: ldr             x16, [x16, #0x5f8]
    // 0x14c57e0: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c57e0: stur            w16, [x2, #0x17]
    // 0x14c57e4: LoadField: r1 = r3->field_f
    //     0x14c57e4: ldur            w1, [x3, #0xf]
    // 0x14c57e8: DecompressPointer r1
    //     0x14c57e8: add             x1, x1, HEAP, lsl #32
    // 0x14c57ec: r0 = controller()
    //     0x14c57ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c57f0: LoadField: r1 = r0->field_57
    //     0x14c57f0: ldur            w1, [x0, #0x57]
    // 0x14c57f4: DecompressPointer r1
    //     0x14c57f4: add             x1, x1, HEAP, lsl #32
    // 0x14c57f8: r0 = value()
    //     0x14c57f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c57fc: cmp             w0, NULL
    // 0x14c5800: b.ne            #0x14c580c
    // 0x14c5804: r0 = Null
    //     0x14c5804: mov             x0, NULL
    // 0x14c5808: b               #0x14c582c
    // 0x14c580c: LoadField: r1 = r0->field_f
    //     0x14c580c: ldur            w1, [x0, #0xf]
    // 0x14c5810: DecompressPointer r1
    //     0x14c5810: add             x1, x1, HEAP, lsl #32
    // 0x14c5814: cmp             w1, NULL
    // 0x14c5818: b.ne            #0x14c5824
    // 0x14c581c: r0 = Null
    //     0x14c581c: mov             x0, NULL
    // 0x14c5820: b               #0x14c582c
    // 0x14c5824: LoadField: r0 = r1->field_13
    //     0x14c5824: ldur            w0, [x1, #0x13]
    // 0x14c5828: DecompressPointer r0
    //     0x14c5828: add             x0, x0, HEAP, lsl #32
    // 0x14c582c: cmp             w0, NULL
    // 0x14c5830: b.ne            #0x14c5838
    // 0x14c5834: r0 = ""
    //     0x14c5834: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c5838: ldur            x1, [fp, #-0x20]
    // 0x14c583c: ArrayStore: r1[3] = r0  ; List_4
    //     0x14c583c: add             x25, x1, #0x1b
    //     0x14c5840: str             w0, [x25]
    //     0x14c5844: tbz             w0, #0, #0x14c5860
    //     0x14c5848: ldurb           w16, [x1, #-1]
    //     0x14c584c: ldurb           w17, [x0, #-1]
    //     0x14c5850: and             x16, x17, x16, lsr #2
    //     0x14c5854: tst             x16, HEAP, lsr #32
    //     0x14c5858: b.eq            #0x14c5860
    //     0x14c585c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c5860: ldur            x16, [fp, #-0x20]
    // 0x14c5864: str             x16, [SP]
    // 0x14c5868: r0 = _interpolate()
    //     0x14c5868: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14c586c: ldur            x2, [fp, #-8]
    // 0x14c5870: stur            x0, [fp, #-0x20]
    // 0x14c5874: LoadField: r1 = r2->field_13
    //     0x14c5874: ldur            w1, [x2, #0x13]
    // 0x14c5878: DecompressPointer r1
    //     0x14c5878: add             x1, x1, HEAP, lsl #32
    // 0x14c587c: r0 = of()
    //     0x14c587c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5880: LoadField: r1 = r0->field_87
    //     0x14c5880: ldur            w1, [x0, #0x87]
    // 0x14c5884: DecompressPointer r1
    //     0x14c5884: add             x1, x1, HEAP, lsl #32
    // 0x14c5888: LoadField: r0 = r1->field_7
    //     0x14c5888: ldur            w0, [x1, #7]
    // 0x14c588c: DecompressPointer r0
    //     0x14c588c: add             x0, x0, HEAP, lsl #32
    // 0x14c5890: stur            x0, [fp, #-0x28]
    // 0x14c5894: r1 = Instance_Color
    //     0x14c5894: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c5898: d0 = 0.700000
    //     0x14c5898: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c589c: ldr             d0, [x17, #0xf48]
    // 0x14c58a0: r0 = withOpacity()
    //     0x14c58a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c58a4: r16 = 12.000000
    //     0x14c58a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c58a8: ldr             x16, [x16, #0x9e8]
    // 0x14c58ac: stp             x0, x16, [SP]
    // 0x14c58b0: ldur            x1, [fp, #-0x28]
    // 0x14c58b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c58b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c58b8: ldr             x4, [x4, #0xaa0]
    // 0x14c58bc: r0 = copyWith()
    //     0x14c58bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c58c0: stur            x0, [fp, #-0x28]
    // 0x14c58c4: r0 = Text()
    //     0x14c58c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c58c8: mov             x1, x0
    // 0x14c58cc: ldur            x0, [fp, #-0x20]
    // 0x14c58d0: stur            x1, [fp, #-0x38]
    // 0x14c58d4: StoreField: r1->field_b = r0
    //     0x14c58d4: stur            w0, [x1, #0xb]
    // 0x14c58d8: ldur            x0, [fp, #-0x28]
    // 0x14c58dc: StoreField: r1->field_13 = r0
    //     0x14c58dc: stur            w0, [x1, #0x13]
    // 0x14c58e0: r0 = Padding()
    //     0x14c58e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c58e4: mov             x2, x0
    // 0x14c58e8: r0 = Instance_EdgeInsets
    //     0x14c58e8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x14c58ec: ldr             x0, [x0, #0x668]
    // 0x14c58f0: stur            x2, [fp, #-0x20]
    // 0x14c58f4: StoreField: r2->field_f = r0
    //     0x14c58f4: stur            w0, [x2, #0xf]
    // 0x14c58f8: ldur            x1, [fp, #-0x38]
    // 0x14c58fc: StoreField: r2->field_b = r1
    //     0x14c58fc: stur            w1, [x2, #0xb]
    // 0x14c5900: ldur            x3, [fp, #-8]
    // 0x14c5904: LoadField: r1 = r3->field_f
    //     0x14c5904: ldur            w1, [x3, #0xf]
    // 0x14c5908: DecompressPointer r1
    //     0x14c5908: add             x1, x1, HEAP, lsl #32
    // 0x14c590c: r0 = controller()
    //     0x14c590c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c5910: LoadField: r1 = r0->field_57
    //     0x14c5910: ldur            w1, [x0, #0x57]
    // 0x14c5914: DecompressPointer r1
    //     0x14c5914: add             x1, x1, HEAP, lsl #32
    // 0x14c5918: r0 = value()
    //     0x14c5918: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c591c: cmp             w0, NULL
    // 0x14c5920: b.ne            #0x14c592c
    // 0x14c5924: r5 = Null
    //     0x14c5924: mov             x5, NULL
    // 0x14c5928: b               #0x14c596c
    // 0x14c592c: LoadField: r1 = r0->field_f
    //     0x14c592c: ldur            w1, [x0, #0xf]
    // 0x14c5930: DecompressPointer r1
    //     0x14c5930: add             x1, x1, HEAP, lsl #32
    // 0x14c5934: cmp             w1, NULL
    // 0x14c5938: b.ne            #0x14c5944
    // 0x14c593c: r0 = Null
    //     0x14c593c: mov             x0, NULL
    // 0x14c5940: b               #0x14c5968
    // 0x14c5944: LoadField: r0 = r1->field_1b
    //     0x14c5944: ldur            w0, [x1, #0x1b]
    // 0x14c5948: DecompressPointer r0
    //     0x14c5948: add             x0, x0, HEAP, lsl #32
    // 0x14c594c: cmp             w0, NULL
    // 0x14c5950: b.ne            #0x14c595c
    // 0x14c5954: r0 = Null
    //     0x14c5954: mov             x0, NULL
    // 0x14c5958: b               #0x14c5968
    // 0x14c595c: LoadField: r1 = r0->field_7
    //     0x14c595c: ldur            w1, [x0, #7]
    // 0x14c5960: DecompressPointer r1
    //     0x14c5960: add             x1, x1, HEAP, lsl #32
    // 0x14c5964: mov             x0, x1
    // 0x14c5968: mov             x5, x0
    // 0x14c596c: ldur            x2, [fp, #-8]
    // 0x14c5970: ldur            x4, [fp, #-0x10]
    // 0x14c5974: ldur            x3, [fp, #-0x18]
    // 0x14c5978: ldur            x1, [fp, #-0x30]
    // 0x14c597c: ldur            x0, [fp, #-0x20]
    // 0x14c5980: str             x5, [SP]
    // 0x14c5984: r0 = _interpolateSingle()
    //     0x14c5984: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14c5988: ldur            x2, [fp, #-8]
    // 0x14c598c: stur            x0, [fp, #-0x28]
    // 0x14c5990: LoadField: r1 = r2->field_13
    //     0x14c5990: ldur            w1, [x2, #0x13]
    // 0x14c5994: DecompressPointer r1
    //     0x14c5994: add             x1, x1, HEAP, lsl #32
    // 0x14c5998: r0 = of()
    //     0x14c5998: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c599c: LoadField: r1 = r0->field_87
    //     0x14c599c: ldur            w1, [x0, #0x87]
    // 0x14c59a0: DecompressPointer r1
    //     0x14c59a0: add             x1, x1, HEAP, lsl #32
    // 0x14c59a4: LoadField: r0 = r1->field_2b
    //     0x14c59a4: ldur            w0, [x1, #0x2b]
    // 0x14c59a8: DecompressPointer r0
    //     0x14c59a8: add             x0, x0, HEAP, lsl #32
    // 0x14c59ac: r16 = Instance_Color
    //     0x14c59ac: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c59b0: r30 = 12.000000
    //     0x14c59b0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c59b4: ldr             lr, [lr, #0x9e8]
    // 0x14c59b8: stp             lr, x16, [SP]
    // 0x14c59bc: mov             x1, x0
    // 0x14c59c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c59c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c59c4: ldr             x4, [x4, #0x9b8]
    // 0x14c59c8: r0 = copyWith()
    //     0x14c59c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c59cc: stur            x0, [fp, #-0x38]
    // 0x14c59d0: r0 = Text()
    //     0x14c59d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c59d4: mov             x3, x0
    // 0x14c59d8: ldur            x0, [fp, #-0x28]
    // 0x14c59dc: stur            x3, [fp, #-0x40]
    // 0x14c59e0: StoreField: r3->field_b = r0
    //     0x14c59e0: stur            w0, [x3, #0xb]
    // 0x14c59e4: ldur            x0, [fp, #-0x38]
    // 0x14c59e8: StoreField: r3->field_13 = r0
    //     0x14c59e8: stur            w0, [x3, #0x13]
    // 0x14c59ec: r1 = Null
    //     0x14c59ec: mov             x1, NULL
    // 0x14c59f0: r2 = 8
    //     0x14c59f0: movz            x2, #0x8
    // 0x14c59f4: r0 = AllocateArray()
    //     0x14c59f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c59f8: mov             x2, x0
    // 0x14c59fc: ldur            x0, [fp, #-0x30]
    // 0x14c5a00: stur            x2, [fp, #-0x28]
    // 0x14c5a04: StoreField: r2->field_f = r0
    //     0x14c5a04: stur            w0, [x2, #0xf]
    // 0x14c5a08: ldur            x0, [fp, #-0x20]
    // 0x14c5a0c: StoreField: r2->field_13 = r0
    //     0x14c5a0c: stur            w0, [x2, #0x13]
    // 0x14c5a10: r16 = Instance_SizedBox
    //     0x14c5a10: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14c5a14: ldr             x16, [x16, #0xc70]
    // 0x14c5a18: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c5a18: stur            w16, [x2, #0x17]
    // 0x14c5a1c: ldur            x0, [fp, #-0x40]
    // 0x14c5a20: StoreField: r2->field_1b = r0
    //     0x14c5a20: stur            w0, [x2, #0x1b]
    // 0x14c5a24: r1 = <Widget>
    //     0x14c5a24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c5a28: r0 = AllocateGrowableArray()
    //     0x14c5a28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c5a2c: mov             x1, x0
    // 0x14c5a30: ldur            x0, [fp, #-0x28]
    // 0x14c5a34: stur            x1, [fp, #-0x20]
    // 0x14c5a38: StoreField: r1->field_f = r0
    //     0x14c5a38: stur            w0, [x1, #0xf]
    // 0x14c5a3c: r2 = 8
    //     0x14c5a3c: movz            x2, #0x8
    // 0x14c5a40: StoreField: r1->field_b = r2
    //     0x14c5a40: stur            w2, [x1, #0xb]
    // 0x14c5a44: r0 = Column()
    //     0x14c5a44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c5a48: mov             x2, x0
    // 0x14c5a4c: r0 = Instance_Axis
    //     0x14c5a4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c5a50: stur            x2, [fp, #-0x28]
    // 0x14c5a54: StoreField: r2->field_f = r0
    //     0x14c5a54: stur            w0, [x2, #0xf]
    // 0x14c5a58: r1 = Instance_MainAxisAlignment
    //     0x14c5a58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14c5a5c: ldr             x1, [x1, #0xa8]
    // 0x14c5a60: StoreField: r2->field_13 = r1
    //     0x14c5a60: stur            w1, [x2, #0x13]
    // 0x14c5a64: r3 = Instance_MainAxisSize
    //     0x14c5a64: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14c5a68: ldr             x3, [x3, #0xdd0]
    // 0x14c5a6c: ArrayStore: r2[0] = r3  ; List_4
    //     0x14c5a6c: stur            w3, [x2, #0x17]
    // 0x14c5a70: r4 = Instance_CrossAxisAlignment
    //     0x14c5a70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c5a74: ldr             x4, [x4, #0x890]
    // 0x14c5a78: StoreField: r2->field_1b = r4
    //     0x14c5a78: stur            w4, [x2, #0x1b]
    // 0x14c5a7c: r5 = Instance_VerticalDirection
    //     0x14c5a7c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c5a80: ldr             x5, [x5, #0xa20]
    // 0x14c5a84: StoreField: r2->field_23 = r5
    //     0x14c5a84: stur            w5, [x2, #0x23]
    // 0x14c5a88: r6 = Instance_Clip
    //     0x14c5a88: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c5a8c: ldr             x6, [x6, #0x38]
    // 0x14c5a90: StoreField: r2->field_2b = r6
    //     0x14c5a90: stur            w6, [x2, #0x2b]
    // 0x14c5a94: StoreField: r2->field_2f = rZR
    //     0x14c5a94: stur            xzr, [x2, #0x2f]
    // 0x14c5a98: ldur            x1, [fp, #-0x20]
    // 0x14c5a9c: StoreField: r2->field_b = r1
    //     0x14c5a9c: stur            w1, [x2, #0xb]
    // 0x14c5aa0: r1 = <FlexParentData>
    //     0x14c5aa0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14c5aa4: ldr             x1, [x1, #0xe00]
    // 0x14c5aa8: r0 = Expanded()
    //     0x14c5aa8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14c5aac: mov             x3, x0
    // 0x14c5ab0: r0 = 1
    //     0x14c5ab0: movz            x0, #0x1
    // 0x14c5ab4: stur            x3, [fp, #-0x20]
    // 0x14c5ab8: StoreField: r3->field_13 = r0
    //     0x14c5ab8: stur            x0, [x3, #0x13]
    // 0x14c5abc: r4 = Instance_FlexFit
    //     0x14c5abc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14c5ac0: ldr             x4, [x4, #0xe08]
    // 0x14c5ac4: StoreField: r3->field_1b = r4
    //     0x14c5ac4: stur            w4, [x3, #0x1b]
    // 0x14c5ac8: ldur            x1, [fp, #-0x28]
    // 0x14c5acc: StoreField: r3->field_b = r1
    //     0x14c5acc: stur            w1, [x3, #0xb]
    // 0x14c5ad0: r1 = Null
    //     0x14c5ad0: mov             x1, NULL
    // 0x14c5ad4: r2 = 6
    //     0x14c5ad4: movz            x2, #0x6
    // 0x14c5ad8: r0 = AllocateArray()
    //     0x14c5ad8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c5adc: mov             x2, x0
    // 0x14c5ae0: ldur            x0, [fp, #-0x18]
    // 0x14c5ae4: stur            x2, [fp, #-0x28]
    // 0x14c5ae8: StoreField: r2->field_f = r0
    //     0x14c5ae8: stur            w0, [x2, #0xf]
    // 0x14c5aec: r16 = Instance_SizedBox
    //     0x14c5aec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14c5af0: ldr             x16, [x16, #0xb20]
    // 0x14c5af4: StoreField: r2->field_13 = r16
    //     0x14c5af4: stur            w16, [x2, #0x13]
    // 0x14c5af8: ldur            x0, [fp, #-0x20]
    // 0x14c5afc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c5afc: stur            w0, [x2, #0x17]
    // 0x14c5b00: r1 = <Widget>
    //     0x14c5b00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c5b04: r0 = AllocateGrowableArray()
    //     0x14c5b04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c5b08: mov             x1, x0
    // 0x14c5b0c: ldur            x0, [fp, #-0x28]
    // 0x14c5b10: stur            x1, [fp, #-0x18]
    // 0x14c5b14: StoreField: r1->field_f = r0
    //     0x14c5b14: stur            w0, [x1, #0xf]
    // 0x14c5b18: r2 = 6
    //     0x14c5b18: movz            x2, #0x6
    // 0x14c5b1c: StoreField: r1->field_b = r2
    //     0x14c5b1c: stur            w2, [x1, #0xb]
    // 0x14c5b20: r0 = Row()
    //     0x14c5b20: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14c5b24: mov             x3, x0
    // 0x14c5b28: r0 = Instance_Axis
    //     0x14c5b28: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c5b2c: stur            x3, [fp, #-0x20]
    // 0x14c5b30: StoreField: r3->field_f = r0
    //     0x14c5b30: stur            w0, [x3, #0xf]
    // 0x14c5b34: r4 = Instance_MainAxisAlignment
    //     0x14c5b34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c5b38: ldr             x4, [x4, #0xa08]
    // 0x14c5b3c: StoreField: r3->field_13 = r4
    //     0x14c5b3c: stur            w4, [x3, #0x13]
    // 0x14c5b40: r5 = Instance_MainAxisSize
    //     0x14c5b40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c5b44: ldr             x5, [x5, #0xa10]
    // 0x14c5b48: ArrayStore: r3[0] = r5  ; List_4
    //     0x14c5b48: stur            w5, [x3, #0x17]
    // 0x14c5b4c: r6 = Instance_CrossAxisAlignment
    //     0x14c5b4c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c5b50: ldr             x6, [x6, #0xa18]
    // 0x14c5b54: StoreField: r3->field_1b = r6
    //     0x14c5b54: stur            w6, [x3, #0x1b]
    // 0x14c5b58: r7 = Instance_VerticalDirection
    //     0x14c5b58: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c5b5c: ldr             x7, [x7, #0xa20]
    // 0x14c5b60: StoreField: r3->field_23 = r7
    //     0x14c5b60: stur            w7, [x3, #0x23]
    // 0x14c5b64: r8 = Instance_Clip
    //     0x14c5b64: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c5b68: ldr             x8, [x8, #0x38]
    // 0x14c5b6c: StoreField: r3->field_2b = r8
    //     0x14c5b6c: stur            w8, [x3, #0x2b]
    // 0x14c5b70: StoreField: r3->field_2f = rZR
    //     0x14c5b70: stur            xzr, [x3, #0x2f]
    // 0x14c5b74: ldur            x1, [fp, #-0x18]
    // 0x14c5b78: StoreField: r3->field_b = r1
    //     0x14c5b78: stur            w1, [x3, #0xb]
    // 0x14c5b7c: r1 = Null
    //     0x14c5b7c: mov             x1, NULL
    // 0x14c5b80: r2 = 2
    //     0x14c5b80: movz            x2, #0x2
    // 0x14c5b84: r0 = AllocateArray()
    //     0x14c5b84: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c5b88: mov             x2, x0
    // 0x14c5b8c: ldur            x0, [fp, #-0x20]
    // 0x14c5b90: stur            x2, [fp, #-0x18]
    // 0x14c5b94: StoreField: r2->field_f = r0
    //     0x14c5b94: stur            w0, [x2, #0xf]
    // 0x14c5b98: r1 = <Widget>
    //     0x14c5b98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c5b9c: r0 = AllocateGrowableArray()
    //     0x14c5b9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c5ba0: mov             x1, x0
    // 0x14c5ba4: ldur            x0, [fp, #-0x18]
    // 0x14c5ba8: stur            x1, [fp, #-0x20]
    // 0x14c5bac: StoreField: r1->field_f = r0
    //     0x14c5bac: stur            w0, [x1, #0xf]
    // 0x14c5bb0: r2 = 2
    //     0x14c5bb0: movz            x2, #0x2
    // 0x14c5bb4: StoreField: r1->field_b = r2
    //     0x14c5bb4: stur            w2, [x1, #0xb]
    // 0x14c5bb8: r0 = Column()
    //     0x14c5bb8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c5bbc: mov             x3, x0
    // 0x14c5bc0: r0 = Instance_Axis
    //     0x14c5bc0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c5bc4: stur            x3, [fp, #-0x18]
    // 0x14c5bc8: StoreField: r3->field_f = r0
    //     0x14c5bc8: stur            w0, [x3, #0xf]
    // 0x14c5bcc: r4 = Instance_MainAxisAlignment
    //     0x14c5bcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c5bd0: ldr             x4, [x4, #0xa08]
    // 0x14c5bd4: StoreField: r3->field_13 = r4
    //     0x14c5bd4: stur            w4, [x3, #0x13]
    // 0x14c5bd8: r5 = Instance_MainAxisSize
    //     0x14c5bd8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c5bdc: ldr             x5, [x5, #0xa10]
    // 0x14c5be0: ArrayStore: r3[0] = r5  ; List_4
    //     0x14c5be0: stur            w5, [x3, #0x17]
    // 0x14c5be4: r6 = Instance_CrossAxisAlignment
    //     0x14c5be4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c5be8: ldr             x6, [x6, #0xa18]
    // 0x14c5bec: StoreField: r3->field_1b = r6
    //     0x14c5bec: stur            w6, [x3, #0x1b]
    // 0x14c5bf0: r7 = Instance_VerticalDirection
    //     0x14c5bf0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c5bf4: ldr             x7, [x7, #0xa20]
    // 0x14c5bf8: StoreField: r3->field_23 = r7
    //     0x14c5bf8: stur            w7, [x3, #0x23]
    // 0x14c5bfc: r8 = Instance_Clip
    //     0x14c5bfc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c5c00: ldr             x8, [x8, #0x38]
    // 0x14c5c04: StoreField: r3->field_2b = r8
    //     0x14c5c04: stur            w8, [x3, #0x2b]
    // 0x14c5c08: StoreField: r3->field_2f = rZR
    //     0x14c5c08: stur            xzr, [x3, #0x2f]
    // 0x14c5c0c: ldur            x1, [fp, #-0x20]
    // 0x14c5c10: StoreField: r3->field_b = r1
    //     0x14c5c10: stur            w1, [x3, #0xb]
    // 0x14c5c14: r1 = Null
    //     0x14c5c14: mov             x1, NULL
    // 0x14c5c18: r2 = 2
    //     0x14c5c18: movz            x2, #0x2
    // 0x14c5c1c: r0 = AllocateArray()
    //     0x14c5c1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c5c20: mov             x2, x0
    // 0x14c5c24: ldur            x0, [fp, #-0x18]
    // 0x14c5c28: stur            x2, [fp, #-0x20]
    // 0x14c5c2c: StoreField: r2->field_f = r0
    //     0x14c5c2c: stur            w0, [x2, #0xf]
    // 0x14c5c30: r1 = <Widget>
    //     0x14c5c30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c5c34: r0 = AllocateGrowableArray()
    //     0x14c5c34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c5c38: mov             x1, x0
    // 0x14c5c3c: ldur            x0, [fp, #-0x20]
    // 0x14c5c40: stur            x1, [fp, #-0x18]
    // 0x14c5c44: StoreField: r1->field_f = r0
    //     0x14c5c44: stur            w0, [x1, #0xf]
    // 0x14c5c48: r2 = 2
    //     0x14c5c48: movz            x2, #0x2
    // 0x14c5c4c: StoreField: r1->field_b = r2
    //     0x14c5c4c: stur            w2, [x1, #0xb]
    // 0x14c5c50: r0 = Column()
    //     0x14c5c50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c5c54: mov             x1, x0
    // 0x14c5c58: r0 = Instance_Axis
    //     0x14c5c58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c5c5c: stur            x1, [fp, #-0x20]
    // 0x14c5c60: StoreField: r1->field_f = r0
    //     0x14c5c60: stur            w0, [x1, #0xf]
    // 0x14c5c64: r2 = Instance_MainAxisAlignment
    //     0x14c5c64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c5c68: ldr             x2, [x2, #0xa08]
    // 0x14c5c6c: StoreField: r1->field_13 = r2
    //     0x14c5c6c: stur            w2, [x1, #0x13]
    // 0x14c5c70: r3 = Instance_MainAxisSize
    //     0x14c5c70: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c5c74: ldr             x3, [x3, #0xa10]
    // 0x14c5c78: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c5c78: stur            w3, [x1, #0x17]
    // 0x14c5c7c: r4 = Instance_CrossAxisAlignment
    //     0x14c5c7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c5c80: ldr             x4, [x4, #0xa18]
    // 0x14c5c84: StoreField: r1->field_1b = r4
    //     0x14c5c84: stur            w4, [x1, #0x1b]
    // 0x14c5c88: r5 = Instance_VerticalDirection
    //     0x14c5c88: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c5c8c: ldr             x5, [x5, #0xa20]
    // 0x14c5c90: StoreField: r1->field_23 = r5
    //     0x14c5c90: stur            w5, [x1, #0x23]
    // 0x14c5c94: r6 = Instance_Clip
    //     0x14c5c94: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c5c98: ldr             x6, [x6, #0x38]
    // 0x14c5c9c: StoreField: r1->field_2b = r6
    //     0x14c5c9c: stur            w6, [x1, #0x2b]
    // 0x14c5ca0: StoreField: r1->field_2f = rZR
    //     0x14c5ca0: stur            xzr, [x1, #0x2f]
    // 0x14c5ca4: ldur            x7, [fp, #-0x18]
    // 0x14c5ca8: StoreField: r1->field_b = r7
    //     0x14c5ca8: stur            w7, [x1, #0xb]
    // 0x14c5cac: r0 = Padding()
    //     0x14c5cac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c5cb0: mov             x1, x0
    // 0x14c5cb4: r0 = Instance_EdgeInsets
    //     0x14c5cb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c5cb8: ldr             x0, [x0, #0x1f0]
    // 0x14c5cbc: stur            x1, [fp, #-0x18]
    // 0x14c5cc0: StoreField: r1->field_f = r0
    //     0x14c5cc0: stur            w0, [x1, #0xf]
    // 0x14c5cc4: ldur            x2, [fp, #-0x20]
    // 0x14c5cc8: StoreField: r1->field_b = r2
    //     0x14c5cc8: stur            w2, [x1, #0xb]
    // 0x14c5ccc: r0 = Card()
    //     0x14c5ccc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14c5cd0: mov             x2, x0
    // 0x14c5cd4: r0 = 0.000000
    //     0x14c5cd4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14c5cd8: stur            x2, [fp, #-0x20]
    // 0x14c5cdc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c5cdc: stur            w0, [x2, #0x17]
    // 0x14c5ce0: ldur            x0, [fp, #-0x10]
    // 0x14c5ce4: StoreField: r2->field_1b = r0
    //     0x14c5ce4: stur            w0, [x2, #0x1b]
    // 0x14c5ce8: r0 = true
    //     0x14c5ce8: add             x0, NULL, #0x20  ; true
    // 0x14c5cec: StoreField: r2->field_1f = r0
    //     0x14c5cec: stur            w0, [x2, #0x1f]
    // 0x14c5cf0: ldur            x1, [fp, #-0x18]
    // 0x14c5cf4: StoreField: r2->field_2f = r1
    //     0x14c5cf4: stur            w1, [x2, #0x2f]
    // 0x14c5cf8: StoreField: r2->field_2b = r0
    //     0x14c5cf8: stur            w0, [x2, #0x2b]
    // 0x14c5cfc: r0 = Instance__CardVariant
    //     0x14c5cfc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14c5d00: ldr             x0, [x0, #0xa68]
    // 0x14c5d04: StoreField: r2->field_33 = r0
    //     0x14c5d04: stur            w0, [x2, #0x33]
    // 0x14c5d08: ldur            x0, [fp, #-8]
    // 0x14c5d0c: LoadField: r1 = r0->field_13
    //     0x14c5d0c: ldur            w1, [x0, #0x13]
    // 0x14c5d10: DecompressPointer r1
    //     0x14c5d10: add             x1, x1, HEAP, lsl #32
    // 0x14c5d14: r0 = of()
    //     0x14c5d14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5d18: LoadField: r1 = r0->field_87
    //     0x14c5d18: ldur            w1, [x0, #0x87]
    // 0x14c5d1c: DecompressPointer r1
    //     0x14c5d1c: add             x1, x1, HEAP, lsl #32
    // 0x14c5d20: LoadField: r0 = r1->field_7
    //     0x14c5d20: ldur            w0, [x1, #7]
    // 0x14c5d24: DecompressPointer r0
    //     0x14c5d24: add             x0, x0, HEAP, lsl #32
    // 0x14c5d28: r16 = 14.000000
    //     0x14c5d28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c5d2c: ldr             x16, [x16, #0x1d8]
    // 0x14c5d30: r30 = Instance_Color
    //     0x14c5d30: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c5d34: stp             lr, x16, [SP]
    // 0x14c5d38: mov             x1, x0
    // 0x14c5d3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c5d3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c5d40: ldr             x4, [x4, #0xaa0]
    // 0x14c5d44: r0 = copyWith()
    //     0x14c5d44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c5d48: stur            x0, [fp, #-0x10]
    // 0x14c5d4c: r0 = Text()
    //     0x14c5d4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c5d50: mov             x1, x0
    // 0x14c5d54: r0 = "What is the Issue\?"
    //     0x14c5d54: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa0] "What is the Issue\?"
    //     0x14c5d58: ldr             x0, [x0, #0xfa0]
    // 0x14c5d5c: stur            x1, [fp, #-0x18]
    // 0x14c5d60: StoreField: r1->field_b = r0
    //     0x14c5d60: stur            w0, [x1, #0xb]
    // 0x14c5d64: ldur            x0, [fp, #-0x10]
    // 0x14c5d68: StoreField: r1->field_13 = r0
    //     0x14c5d68: stur            w0, [x1, #0x13]
    // 0x14c5d6c: r0 = Padding()
    //     0x14c5d6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c5d70: mov             x2, x0
    // 0x14c5d74: r0 = Instance_EdgeInsets
    //     0x14c5d74: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x14c5d78: ldr             x0, [x0, #0x100]
    // 0x14c5d7c: stur            x2, [fp, #-0x10]
    // 0x14c5d80: StoreField: r2->field_f = r0
    //     0x14c5d80: stur            w0, [x2, #0xf]
    // 0x14c5d84: ldur            x0, [fp, #-0x18]
    // 0x14c5d88: StoreField: r2->field_b = r0
    //     0x14c5d88: stur            w0, [x2, #0xb]
    // 0x14c5d8c: ldur            x0, [fp, #-8]
    // 0x14c5d90: LoadField: r1 = r0->field_13
    //     0x14c5d90: ldur            w1, [x0, #0x13]
    // 0x14c5d94: DecompressPointer r1
    //     0x14c5d94: add             x1, x1, HEAP, lsl #32
    // 0x14c5d98: r0 = of()
    //     0x14c5d98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5d9c: LoadField: r1 = r0->field_87
    //     0x14c5d9c: ldur            w1, [x0, #0x87]
    // 0x14c5da0: DecompressPointer r1
    //     0x14c5da0: add             x1, x1, HEAP, lsl #32
    // 0x14c5da4: LoadField: r0 = r1->field_2b
    //     0x14c5da4: ldur            w0, [x1, #0x2b]
    // 0x14c5da8: DecompressPointer r0
    //     0x14c5da8: add             x0, x0, HEAP, lsl #32
    // 0x14c5dac: stur            x0, [fp, #-0x18]
    // 0x14c5db0: r1 = Instance_Color
    //     0x14c5db0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c5db4: d0 = 0.400000
    //     0x14c5db4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14c5db8: r0 = withOpacity()
    //     0x14c5db8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c5dbc: r16 = 12.000000
    //     0x14c5dbc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c5dc0: ldr             x16, [x16, #0x9e8]
    // 0x14c5dc4: stp             x0, x16, [SP]
    // 0x14c5dc8: ldur            x1, [fp, #-0x18]
    // 0x14c5dcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c5dcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c5dd0: ldr             x4, [x4, #0xaa0]
    // 0x14c5dd4: r0 = copyWith()
    //     0x14c5dd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c5dd8: stur            x0, [fp, #-0x18]
    // 0x14c5ddc: r0 = Text()
    //     0x14c5ddc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c5de0: mov             x1, x0
    // 0x14c5de4: r0 = "Please choose the correct issue for return/replace."
    //     0x14c5de4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fa8] "Please choose the correct issue for return/replace."
    //     0x14c5de8: ldr             x0, [x0, #0xfa8]
    // 0x14c5dec: stur            x1, [fp, #-0x28]
    // 0x14c5df0: StoreField: r1->field_b = r0
    //     0x14c5df0: stur            w0, [x1, #0xb]
    // 0x14c5df4: ldur            x0, [fp, #-0x18]
    // 0x14c5df8: StoreField: r1->field_13 = r0
    //     0x14c5df8: stur            w0, [x1, #0x13]
    // 0x14c5dfc: r0 = Padding()
    //     0x14c5dfc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c5e00: mov             x2, x0
    // 0x14c5e04: r0 = Instance_EdgeInsets
    //     0x14c5e04: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14c5e08: ldr             x0, [x0, #0x770]
    // 0x14c5e0c: stur            x2, [fp, #-0x18]
    // 0x14c5e10: StoreField: r2->field_f = r0
    //     0x14c5e10: stur            w0, [x2, #0xf]
    // 0x14c5e14: ldur            x0, [fp, #-0x28]
    // 0x14c5e18: StoreField: r2->field_b = r0
    //     0x14c5e18: stur            w0, [x2, #0xb]
    // 0x14c5e1c: ldur            x0, [fp, #-8]
    // 0x14c5e20: LoadField: r1 = r0->field_13
    //     0x14c5e20: ldur            w1, [x0, #0x13]
    // 0x14c5e24: DecompressPointer r1
    //     0x14c5e24: add             x1, x1, HEAP, lsl #32
    // 0x14c5e28: r0 = of()
    //     0x14c5e28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5e2c: LoadField: r1 = r0->field_87
    //     0x14c5e2c: ldur            w1, [x0, #0x87]
    // 0x14c5e30: DecompressPointer r1
    //     0x14c5e30: add             x1, x1, HEAP, lsl #32
    // 0x14c5e34: LoadField: r0 = r1->field_7
    //     0x14c5e34: ldur            w0, [x1, #7]
    // 0x14c5e38: DecompressPointer r0
    //     0x14c5e38: add             x0, x0, HEAP, lsl #32
    // 0x14c5e3c: r16 = 12.000000
    //     0x14c5e3c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c5e40: ldr             x16, [x16, #0x9e8]
    // 0x14c5e44: r30 = Instance_Color
    //     0x14c5e44: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c5e48: stp             lr, x16, [SP, #8]
    // 0x14c5e4c: r16 = const [Instance of 'FontFeature']
    //     0x14c5e4c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fb0] List<FontFeature>(1)
    //     0x14c5e50: ldr             x16, [x16, #0xfb0]
    // 0x14c5e54: str             x16, [SP]
    // 0x14c5e58: mov             x1, x0
    // 0x14c5e5c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontFeatures, 0x3, fontSize, 0x1, null]
    //     0x14c5e5c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fb8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontFeatures", 0x3, "fontSize", 0x1, Null]
    //     0x14c5e60: ldr             x4, [x4, #0xfb8]
    // 0x14c5e64: r0 = copyWith()
    //     0x14c5e64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c5e68: stur            x0, [fp, #-0x28]
    // 0x14c5e6c: r0 = TextSpan()
    //     0x14c5e6c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c5e70: mov             x2, x0
    // 0x14c5e74: r0 = "Select Reason"
    //     0x14c5e74: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc0] "Select Reason"
    //     0x14c5e78: ldr             x0, [x0, #0xfc0]
    // 0x14c5e7c: stur            x2, [fp, #-0x30]
    // 0x14c5e80: StoreField: r2->field_b = r0
    //     0x14c5e80: stur            w0, [x2, #0xb]
    // 0x14c5e84: r0 = Instance__DeferringMouseCursor
    //     0x14c5e84: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c5e88: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c5e88: stur            w0, [x2, #0x17]
    // 0x14c5e8c: ldur            x1, [fp, #-0x28]
    // 0x14c5e90: StoreField: r2->field_7 = r1
    //     0x14c5e90: stur            w1, [x2, #7]
    // 0x14c5e94: ldur            x3, [fp, #-8]
    // 0x14c5e98: LoadField: r1 = r3->field_13
    //     0x14c5e98: ldur            w1, [x3, #0x13]
    // 0x14c5e9c: DecompressPointer r1
    //     0x14c5e9c: add             x1, x1, HEAP, lsl #32
    // 0x14c5ea0: r0 = of()
    //     0x14c5ea0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c5ea4: LoadField: r1 = r0->field_87
    //     0x14c5ea4: ldur            w1, [x0, #0x87]
    // 0x14c5ea8: DecompressPointer r1
    //     0x14c5ea8: add             x1, x1, HEAP, lsl #32
    // 0x14c5eac: LoadField: r0 = r1->field_2b
    //     0x14c5eac: ldur            w0, [x1, #0x2b]
    // 0x14c5eb0: DecompressPointer r0
    //     0x14c5eb0: add             x0, x0, HEAP, lsl #32
    // 0x14c5eb4: r16 = Instance_MaterialColor
    //     0x14c5eb4: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x14c5eb8: ldr             x16, [x16, #0x180]
    // 0x14c5ebc: str             x16, [SP]
    // 0x14c5ec0: mov             x1, x0
    // 0x14c5ec4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x14c5ec4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x14c5ec8: ldr             x4, [x4, #0xf40]
    // 0x14c5ecc: r0 = copyWith()
    //     0x14c5ecc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c5ed0: stur            x0, [fp, #-0x28]
    // 0x14c5ed4: r0 = TextSpan()
    //     0x14c5ed4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c5ed8: mov             x3, x0
    // 0x14c5edc: r0 = " *"
    //     0x14c5edc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0x14c5ee0: ldr             x0, [x0, #0xfc8]
    // 0x14c5ee4: stur            x3, [fp, #-0x38]
    // 0x14c5ee8: StoreField: r3->field_b = r0
    //     0x14c5ee8: stur            w0, [x3, #0xb]
    // 0x14c5eec: r0 = Instance__DeferringMouseCursor
    //     0x14c5eec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c5ef0: ArrayStore: r3[0] = r0  ; List_4
    //     0x14c5ef0: stur            w0, [x3, #0x17]
    // 0x14c5ef4: ldur            x1, [fp, #-0x28]
    // 0x14c5ef8: StoreField: r3->field_7 = r1
    //     0x14c5ef8: stur            w1, [x3, #7]
    // 0x14c5efc: r1 = Null
    //     0x14c5efc: mov             x1, NULL
    // 0x14c5f00: r2 = 4
    //     0x14c5f00: movz            x2, #0x4
    // 0x14c5f04: r0 = AllocateArray()
    //     0x14c5f04: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c5f08: mov             x2, x0
    // 0x14c5f0c: ldur            x0, [fp, #-0x30]
    // 0x14c5f10: stur            x2, [fp, #-0x28]
    // 0x14c5f14: StoreField: r2->field_f = r0
    //     0x14c5f14: stur            w0, [x2, #0xf]
    // 0x14c5f18: ldur            x0, [fp, #-0x38]
    // 0x14c5f1c: StoreField: r2->field_13 = r0
    //     0x14c5f1c: stur            w0, [x2, #0x13]
    // 0x14c5f20: r1 = <InlineSpan>
    //     0x14c5f20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14c5f24: ldr             x1, [x1, #0xe40]
    // 0x14c5f28: r0 = AllocateGrowableArray()
    //     0x14c5f28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c5f2c: mov             x1, x0
    // 0x14c5f30: ldur            x0, [fp, #-0x28]
    // 0x14c5f34: stur            x1, [fp, #-0x30]
    // 0x14c5f38: StoreField: r1->field_f = r0
    //     0x14c5f38: stur            w0, [x1, #0xf]
    // 0x14c5f3c: r2 = 4
    //     0x14c5f3c: movz            x2, #0x4
    // 0x14c5f40: StoreField: r1->field_b = r2
    //     0x14c5f40: stur            w2, [x1, #0xb]
    // 0x14c5f44: r0 = TextSpan()
    //     0x14c5f44: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c5f48: mov             x1, x0
    // 0x14c5f4c: ldur            x0, [fp, #-0x30]
    // 0x14c5f50: stur            x1, [fp, #-0x28]
    // 0x14c5f54: StoreField: r1->field_f = r0
    //     0x14c5f54: stur            w0, [x1, #0xf]
    // 0x14c5f58: r0 = Instance__DeferringMouseCursor
    //     0x14c5f58: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c5f5c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14c5f5c: stur            w0, [x1, #0x17]
    // 0x14c5f60: r0 = RichText()
    //     0x14c5f60: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14c5f64: mov             x1, x0
    // 0x14c5f68: ldur            x2, [fp, #-0x28]
    // 0x14c5f6c: stur            x0, [fp, #-0x28]
    // 0x14c5f70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14c5f70: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14c5f74: r0 = RichText()
    //     0x14c5f74: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14c5f78: r0 = Padding()
    //     0x14c5f78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c5f7c: mov             x2, x0
    // 0x14c5f80: r0 = Instance_EdgeInsets
    //     0x14c5f80: add             x0, PP, #0x42, lsl #12  ; [pp+0x42708] Obj!EdgeInsets@d59cf1
    //     0x14c5f84: ldr             x0, [x0, #0x708]
    // 0x14c5f88: stur            x2, [fp, #-0x30]
    // 0x14c5f8c: StoreField: r2->field_f = r0
    //     0x14c5f8c: stur            w0, [x2, #0xf]
    // 0x14c5f90: ldur            x0, [fp, #-0x28]
    // 0x14c5f94: StoreField: r2->field_b = r0
    //     0x14c5f94: stur            w0, [x2, #0xb]
    // 0x14c5f98: ldur            x0, [fp, #-8]
    // 0x14c5f9c: LoadField: r1 = r0->field_f
    //     0x14c5f9c: ldur            w1, [x0, #0xf]
    // 0x14c5fa0: DecompressPointer r1
    //     0x14c5fa0: add             x1, x1, HEAP, lsl #32
    // 0x14c5fa4: r0 = controller()
    //     0x14c5fa4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c5fa8: LoadField: r1 = r0->field_57
    //     0x14c5fa8: ldur            w1, [x0, #0x57]
    // 0x14c5fac: DecompressPointer r1
    //     0x14c5fac: add             x1, x1, HEAP, lsl #32
    // 0x14c5fb0: r0 = value()
    //     0x14c5fb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c5fb4: cmp             w0, NULL
    // 0x14c5fb8: b.ne            #0x14c5fc4
    // 0x14c5fbc: r0 = Null
    //     0x14c5fbc: mov             x0, NULL
    // 0x14c5fc0: b               #0x14c5fd0
    // 0x14c5fc4: LoadField: r1 = r0->field_7
    //     0x14c5fc4: ldur            w1, [x0, #7]
    // 0x14c5fc8: DecompressPointer r1
    //     0x14c5fc8: add             x1, x1, HEAP, lsl #32
    // 0x14c5fcc: LoadField: r0 = r1->field_b
    //     0x14c5fcc: ldur            w0, [x1, #0xb]
    // 0x14c5fd0: cmp             w0, NULL
    // 0x14c5fd4: b.ne            #0x14c5fe0
    // 0x14c5fd8: r7 = 0
    //     0x14c5fd8: movz            x7, #0
    // 0x14c5fdc: b               #0x14c5fe8
    // 0x14c5fe0: r1 = LoadInt32Instr(r0)
    //     0x14c5fe0: sbfx            x1, x0, #1, #0x1f
    // 0x14c5fe4: mov             x7, x1
    // 0x14c5fe8: ldur            x3, [fp, #-8]
    // 0x14c5fec: ldur            x6, [fp, #-0x20]
    // 0x14c5ff0: ldur            x5, [fp, #-0x10]
    // 0x14c5ff4: ldur            x4, [fp, #-0x18]
    // 0x14c5ff8: ldur            x0, [fp, #-0x30]
    // 0x14c5ffc: stur            x7, [fp, #-0x48]
    // 0x14c6000: r1 = Function '<anonymous closure>':.
    //     0x14c6000: add             x1, PP, #0x42, lsl #12  ; [pp+0x42710] AnonymousClosure: (0x14c74c8), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c6004: ldr             x1, [x1, #0x710]
    // 0x14c6008: r2 = Null
    //     0x14c6008: mov             x2, NULL
    // 0x14c600c: r0 = AllocateClosure()
    //     0x14c600c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c6010: ldur            x2, [fp, #-8]
    // 0x14c6014: r1 = Function '<anonymous closure>':.
    //     0x14c6014: add             x1, PP, #0x42, lsl #12  ; [pp+0x42718] AnonymousClosure: (0x14c6f70), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c6018: ldr             x1, [x1, #0x718]
    // 0x14c601c: stur            x0, [fp, #-0x28]
    // 0x14c6020: r0 = AllocateClosure()
    //     0x14c6020: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c6024: stur            x0, [fp, #-0x38]
    // 0x14c6028: r0 = ListView()
    //     0x14c6028: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14c602c: stur            x0, [fp, #-0x40]
    // 0x14c6030: r16 = true
    //     0x14c6030: add             x16, NULL, #0x20  ; true
    // 0x14c6034: r30 = false
    //     0x14c6034: add             lr, NULL, #0x30  ; false
    // 0x14c6038: stp             lr, x16, [SP, #8]
    // 0x14c603c: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14c603c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14c6040: ldr             x16, [x16, #0x1c8]
    // 0x14c6044: str             x16, [SP]
    // 0x14c6048: mov             x1, x0
    // 0x14c604c: ldur            x2, [fp, #-0x38]
    // 0x14c6050: ldur            x3, [fp, #-0x48]
    // 0x14c6054: ldur            x5, [fp, #-0x28]
    // 0x14c6058: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x14c6058: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14c605c: ldr             x4, [x4, #0x138]
    // 0x14c6060: r0 = ListView.separated()
    //     0x14c6060: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14c6064: r1 = Null
    //     0x14c6064: mov             x1, NULL
    // 0x14c6068: r2 = 10
    //     0x14c6068: movz            x2, #0xa
    // 0x14c606c: r0 = AllocateArray()
    //     0x14c606c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c6070: mov             x2, x0
    // 0x14c6074: ldur            x0, [fp, #-0x20]
    // 0x14c6078: stur            x2, [fp, #-0x28]
    // 0x14c607c: StoreField: r2->field_f = r0
    //     0x14c607c: stur            w0, [x2, #0xf]
    // 0x14c6080: ldur            x0, [fp, #-0x10]
    // 0x14c6084: StoreField: r2->field_13 = r0
    //     0x14c6084: stur            w0, [x2, #0x13]
    // 0x14c6088: ldur            x0, [fp, #-0x18]
    // 0x14c608c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c608c: stur            w0, [x2, #0x17]
    // 0x14c6090: ldur            x0, [fp, #-0x30]
    // 0x14c6094: StoreField: r2->field_1b = r0
    //     0x14c6094: stur            w0, [x2, #0x1b]
    // 0x14c6098: ldur            x0, [fp, #-0x40]
    // 0x14c609c: StoreField: r2->field_1f = r0
    //     0x14c609c: stur            w0, [x2, #0x1f]
    // 0x14c60a0: r1 = <Widget>
    //     0x14c60a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c60a4: r0 = AllocateGrowableArray()
    //     0x14c60a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c60a8: mov             x2, x0
    // 0x14c60ac: ldur            x0, [fp, #-0x28]
    // 0x14c60b0: stur            x2, [fp, #-0x10]
    // 0x14c60b4: StoreField: r2->field_f = r0
    //     0x14c60b4: stur            w0, [x2, #0xf]
    // 0x14c60b8: r0 = 10
    //     0x14c60b8: movz            x0, #0xa
    // 0x14c60bc: StoreField: r2->field_b = r0
    //     0x14c60bc: stur            w0, [x2, #0xb]
    // 0x14c60c0: ldur            x0, [fp, #-8]
    // 0x14c60c4: LoadField: r1 = r0->field_f
    //     0x14c60c4: ldur            w1, [x0, #0xf]
    // 0x14c60c8: DecompressPointer r1
    //     0x14c60c8: add             x1, x1, HEAP, lsl #32
    // 0x14c60cc: r0 = controller()
    //     0x14c60cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c60d0: LoadField: r1 = r0->field_67
    //     0x14c60d0: ldur            w1, [x0, #0x67]
    // 0x14c60d4: DecompressPointer r1
    //     0x14c60d4: add             x1, x1, HEAP, lsl #32
    // 0x14c60d8: r0 = value()
    //     0x14c60d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c60dc: r1 = LoadClassIdInstr(r0)
    //     0x14c60dc: ldur            x1, [x0, #-1]
    //     0x14c60e0: ubfx            x1, x1, #0xc, #0x14
    // 0x14c60e4: r16 = "OTHER"
    //     0x14c60e4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f68] "OTHER"
    //     0x14c60e8: ldr             x16, [x16, #0xf68]
    // 0x14c60ec: stp             x16, x0, [SP]
    // 0x14c60f0: mov             x0, x1
    // 0x14c60f4: mov             lr, x0
    // 0x14c60f8: ldr             lr, [x21, lr, lsl #3]
    // 0x14c60fc: blr             lr
    // 0x14c6100: tbz             w0, #4, #0x14c6150
    // 0x14c6104: ldur            x2, [fp, #-8]
    // 0x14c6108: LoadField: r1 = r2->field_f
    //     0x14c6108: ldur            w1, [x2, #0xf]
    // 0x14c610c: DecompressPointer r1
    //     0x14c610c: add             x1, x1, HEAP, lsl #32
    // 0x14c6110: r0 = controller()
    //     0x14c6110: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6114: LoadField: r1 = r0->field_57
    //     0x14c6114: ldur            w1, [x0, #0x57]
    // 0x14c6118: DecompressPointer r1
    //     0x14c6118: add             x1, x1, HEAP, lsl #32
    // 0x14c611c: r0 = value()
    //     0x14c611c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c6120: cmp             w0, NULL
    // 0x14c6124: b.ne            #0x14c6130
    // 0x14c6128: r0 = Null
    //     0x14c6128: mov             x0, NULL
    // 0x14c612c: b               #0x14c613c
    // 0x14c6130: LoadField: r1 = r0->field_23
    //     0x14c6130: ldur            w1, [x0, #0x23]
    // 0x14c6134: DecompressPointer r1
    //     0x14c6134: add             x1, x1, HEAP, lsl #32
    // 0x14c6138: mov             x0, x1
    // 0x14c613c: cmp             w0, NULL
    // 0x14c6140: b.ne            #0x14c614c
    // 0x14c6144: ldur            x2, [fp, #-0x10]
    // 0x14c6148: b               #0x14c6470
    // 0x14c614c: tbz             w0, #4, #0x14c646c
    // 0x14c6150: ldur            x2, [fp, #-8]
    // 0x14c6154: ldur            x0, [fp, #-0x10]
    // 0x14c6158: LoadField: r1 = r2->field_f
    //     0x14c6158: ldur            w1, [x2, #0xf]
    // 0x14c615c: DecompressPointer r1
    //     0x14c615c: add             x1, x1, HEAP, lsl #32
    // 0x14c6160: r0 = controller()
    //     0x14c6160: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6164: LoadField: r1 = r0->field_d7
    //     0x14c6164: ldur            w1, [x0, #0xd7]
    // 0x14c6168: DecompressPointer r1
    //     0x14c6168: add             x1, x1, HEAP, lsl #32
    // 0x14c616c: ldur            x2, [fp, #-8]
    // 0x14c6170: stur            x1, [fp, #-0x20]
    // 0x14c6174: LoadField: r0 = r2->field_f
    //     0x14c6174: ldur            w0, [x2, #0xf]
    // 0x14c6178: DecompressPointer r0
    //     0x14c6178: add             x0, x0, HEAP, lsl #32
    // 0x14c617c: stur            x0, [fp, #-0x18]
    // 0x14c6180: r0 = LengthLimitingTextInputFormatter()
    //     0x14c6180: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14c6184: mov             x3, x0
    // 0x14c6188: r0 = 300
    //     0x14c6188: movz            x0, #0x12c
    // 0x14c618c: stur            x3, [fp, #-0x28]
    // 0x14c6190: StoreField: r3->field_7 = r0
    //     0x14c6190: stur            w0, [x3, #7]
    // 0x14c6194: r1 = Null
    //     0x14c6194: mov             x1, NULL
    // 0x14c6198: r2 = 2
    //     0x14c6198: movz            x2, #0x2
    // 0x14c619c: r0 = AllocateArray()
    //     0x14c619c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c61a0: mov             x2, x0
    // 0x14c61a4: ldur            x0, [fp, #-0x28]
    // 0x14c61a8: stur            x2, [fp, #-0x30]
    // 0x14c61ac: StoreField: r2->field_f = r0
    //     0x14c61ac: stur            w0, [x2, #0xf]
    // 0x14c61b0: r1 = <TextInputFormatter>
    //     0x14c61b0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14c61b4: ldr             x1, [x1, #0x7b0]
    // 0x14c61b8: r0 = AllocateGrowableArray()
    //     0x14c61b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c61bc: mov             x2, x0
    // 0x14c61c0: ldur            x0, [fp, #-0x30]
    // 0x14c61c4: stur            x2, [fp, #-0x28]
    // 0x14c61c8: StoreField: r2->field_f = r0
    //     0x14c61c8: stur            w0, [x2, #0xf]
    // 0x14c61cc: r0 = 2
    //     0x14c61cc: movz            x0, #0x2
    // 0x14c61d0: StoreField: r2->field_b = r0
    //     0x14c61d0: stur            w0, [x2, #0xb]
    // 0x14c61d4: ldur            x3, [fp, #-8]
    // 0x14c61d8: LoadField: r1 = r3->field_13
    //     0x14c61d8: ldur            w1, [x3, #0x13]
    // 0x14c61dc: DecompressPointer r1
    //     0x14c61dc: add             x1, x1, HEAP, lsl #32
    // 0x14c61e0: r0 = of()
    //     0x14c61e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c61e4: LoadField: r1 = r0->field_87
    //     0x14c61e4: ldur            w1, [x0, #0x87]
    // 0x14c61e8: DecompressPointer r1
    //     0x14c61e8: add             x1, x1, HEAP, lsl #32
    // 0x14c61ec: LoadField: r0 = r1->field_2b
    //     0x14c61ec: ldur            w0, [x1, #0x2b]
    // 0x14c61f0: DecompressPointer r0
    //     0x14c61f0: add             x0, x0, HEAP, lsl #32
    // 0x14c61f4: ldur            x2, [fp, #-8]
    // 0x14c61f8: stur            x0, [fp, #-0x30]
    // 0x14c61fc: LoadField: r1 = r2->field_13
    //     0x14c61fc: ldur            w1, [x2, #0x13]
    // 0x14c6200: DecompressPointer r1
    //     0x14c6200: add             x1, x1, HEAP, lsl #32
    // 0x14c6204: r0 = of()
    //     0x14c6204: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c6208: LoadField: r1 = r0->field_5b
    //     0x14c6208: ldur            w1, [x0, #0x5b]
    // 0x14c620c: DecompressPointer r1
    //     0x14c620c: add             x1, x1, HEAP, lsl #32
    // 0x14c6210: r16 = 12.000000
    //     0x14c6210: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c6214: ldr             x16, [x16, #0x9e8]
    // 0x14c6218: stp             x16, x1, [SP]
    // 0x14c621c: ldur            x1, [fp, #-0x30]
    // 0x14c6220: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c6220: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c6224: ldr             x4, [x4, #0x9b8]
    // 0x14c6228: r0 = copyWith()
    //     0x14c6228: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c622c: ldur            x2, [fp, #-8]
    // 0x14c6230: stur            x0, [fp, #-0x30]
    // 0x14c6234: LoadField: r1 = r2->field_f
    //     0x14c6234: ldur            w1, [x2, #0xf]
    // 0x14c6238: DecompressPointer r1
    //     0x14c6238: add             x1, x1, HEAP, lsl #32
    // 0x14c623c: r0 = controller()
    //     0x14c623c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6240: LoadField: r2 = r0->field_db
    //     0x14c6240: ldur            w2, [x0, #0xdb]
    // 0x14c6244: DecompressPointer r2
    //     0x14c6244: add             x2, x2, HEAP, lsl #32
    // 0x14c6248: ldur            x0, [fp, #-8]
    // 0x14c624c: stur            x2, [fp, #-0x38]
    // 0x14c6250: LoadField: r1 = r0->field_13
    //     0x14c6250: ldur            w1, [x0, #0x13]
    // 0x14c6254: DecompressPointer r1
    //     0x14c6254: add             x1, x1, HEAP, lsl #32
    // 0x14c6258: r0 = getTextFormFieldInputDecorationCircle()
    //     0x14c6258: bl              #0xac2dd8  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircle
    // 0x14c625c: ldur            x2, [fp, #-8]
    // 0x14c6260: stur            x0, [fp, #-0x40]
    // 0x14c6264: LoadField: r1 = r2->field_13
    //     0x14c6264: ldur            w1, [x2, #0x13]
    // 0x14c6268: DecompressPointer r1
    //     0x14c6268: add             x1, x1, HEAP, lsl #32
    // 0x14c626c: r0 = of()
    //     0x14c626c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c6270: LoadField: r1 = r0->field_87
    //     0x14c6270: ldur            w1, [x0, #0x87]
    // 0x14c6274: DecompressPointer r1
    //     0x14c6274: add             x1, x1, HEAP, lsl #32
    // 0x14c6278: LoadField: r0 = r1->field_2b
    //     0x14c6278: ldur            w0, [x1, #0x2b]
    // 0x14c627c: DecompressPointer r0
    //     0x14c627c: add             x0, x0, HEAP, lsl #32
    // 0x14c6280: stur            x0, [fp, #-0x50]
    // 0x14c6284: r1 = Instance_Color
    //     0x14c6284: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c6288: d0 = 0.400000
    //     0x14c6288: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14c628c: r0 = withOpacity()
    //     0x14c628c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c6290: r16 = 12.000000
    //     0x14c6290: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c6294: ldr             x16, [x16, #0x9e8]
    // 0x14c6298: stp             x16, x0, [SP]
    // 0x14c629c: ldur            x1, [fp, #-0x50]
    // 0x14c62a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c62a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c62a4: ldr             x4, [x4, #0x9b8]
    // 0x14c62a8: r0 = copyWith()
    //     0x14c62a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c62ac: ldur            x2, [fp, #-8]
    // 0x14c62b0: stur            x0, [fp, #-0x50]
    // 0x14c62b4: LoadField: r1 = r2->field_13
    //     0x14c62b4: ldur            w1, [x2, #0x13]
    // 0x14c62b8: DecompressPointer r1
    //     0x14c62b8: add             x1, x1, HEAP, lsl #32
    // 0x14c62bc: r0 = of()
    //     0x14c62bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c62c0: LoadField: r1 = r0->field_87
    //     0x14c62c0: ldur            w1, [x0, #0x87]
    // 0x14c62c4: DecompressPointer r1
    //     0x14c62c4: add             x1, x1, HEAP, lsl #32
    // 0x14c62c8: LoadField: r0 = r1->field_2b
    //     0x14c62c8: ldur            w0, [x1, #0x2b]
    // 0x14c62cc: DecompressPointer r0
    //     0x14c62cc: add             x0, x0, HEAP, lsl #32
    // 0x14c62d0: r16 = Instance_Color
    //     0x14c62d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x14c62d4: ldr             x16, [x16, #0x7c0]
    // 0x14c62d8: r30 = 12.000000
    //     0x14c62d8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c62dc: ldr             lr, [lr, #0x9e8]
    // 0x14c62e0: stp             lr, x16, [SP]
    // 0x14c62e4: mov             x1, x0
    // 0x14c62e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c62e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c62ec: ldr             x4, [x4, #0x9b8]
    // 0x14c62f0: r0 = copyWith()
    //     0x14c62f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c62f4: r16 = "Remark: Describe your issue in detail"
    //     0x14c62f4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fe0] "Remark: Describe your issue in detail"
    //     0x14c62f8: ldr             x16, [x16, #0xfe0]
    // 0x14c62fc: ldur            lr, [fp, #-0x50]
    // 0x14c6300: stp             lr, x16, [SP, #8]
    // 0x14c6304: str             x0, [SP]
    // 0x14c6308: ldur            x1, [fp, #-0x40]
    // 0x14c630c: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x14c630c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x14c6310: ldr             x4, [x4, #0xfe8]
    // 0x14c6314: r0 = copyWith()
    //     0x14c6314: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14c6318: ldur            x2, [fp, #-0x18]
    // 0x14c631c: r1 = Function '_validateOther@1490036680':.
    //     0x14c631c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42720] AnonymousClosure: (0xb51164), of [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_view.dart] ReturnOrderView
    //     0x14c6320: ldr             x1, [x1, #0x720]
    // 0x14c6324: stur            x0, [fp, #-0x18]
    // 0x14c6328: r0 = AllocateClosure()
    //     0x14c6328: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c632c: ldur            x2, [fp, #-8]
    // 0x14c6330: r1 = Function '<anonymous closure>':.
    //     0x14c6330: add             x1, PP, #0x42, lsl #12  ; [pp+0x42728] AnonymousClosure: (0x14c6eac), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c6334: ldr             x1, [x1, #0x728]
    // 0x14c6338: stur            x0, [fp, #-0x40]
    // 0x14c633c: r0 = AllocateClosure()
    //     0x14c633c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c6340: r1 = <String>
    //     0x14c6340: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14c6344: stur            x0, [fp, #-0x50]
    // 0x14c6348: r0 = TextFormField()
    //     0x14c6348: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14c634c: stur            x0, [fp, #-0x58]
    // 0x14c6350: ldur            x16, [fp, #-0x40]
    // 0x14c6354: r30 = false
    //     0x14c6354: add             lr, NULL, #0x30  ; false
    // 0x14c6358: stp             lr, x16, [SP, #0x40]
    // 0x14c635c: r16 = Instance_AutovalidateMode
    //     0x14c635c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14c6360: ldr             x16, [x16, #0x7e8]
    // 0x14c6364: ldur            lr, [fp, #-0x28]
    // 0x14c6368: stp             lr, x16, [SP, #0x30]
    // 0x14c636c: r16 = Instance_TextInputType
    //     0x14c636c: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x14c6370: ldr             x16, [x16, #0x7f0]
    // 0x14c6374: r30 = 6
    //     0x14c6374: movz            lr, #0x6
    // 0x14c6378: stp             lr, x16, [SP, #0x20]
    // 0x14c637c: r16 = 10
    //     0x14c637c: movz            x16, #0xa
    // 0x14c6380: ldur            lr, [fp, #-0x30]
    // 0x14c6384: stp             lr, x16, [SP, #0x10]
    // 0x14c6388: ldur            x16, [fp, #-0x38]
    // 0x14c638c: ldur            lr, [fp, #-0x50]
    // 0x14c6390: stp             lr, x16, [SP]
    // 0x14c6394: mov             x1, x0
    // 0x14c6398: ldur            x2, [fp, #-0x18]
    // 0x14c639c: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x4, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, validator, 0x2, null]
    //     0x14c639c: add             x4, PP, #0x33, lsl #12  ; [pp+0x337f8] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x4, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "validator", 0x2, Null]
    //     0x14c63a0: ldr             x4, [x4, #0x7f8]
    // 0x14c63a4: r0 = TextFormField()
    //     0x14c63a4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14c63a8: r0 = Form()
    //     0x14c63a8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14c63ac: mov             x1, x0
    // 0x14c63b0: ldur            x0, [fp, #-0x58]
    // 0x14c63b4: stur            x1, [fp, #-0x18]
    // 0x14c63b8: StoreField: r1->field_b = r0
    //     0x14c63b8: stur            w0, [x1, #0xb]
    // 0x14c63bc: r0 = Instance_AutovalidateMode
    //     0x14c63bc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14c63c0: ldr             x0, [x0, #0x800]
    // 0x14c63c4: StoreField: r1->field_23 = r0
    //     0x14c63c4: stur            w0, [x1, #0x23]
    // 0x14c63c8: ldur            x0, [fp, #-0x20]
    // 0x14c63cc: StoreField: r1->field_7 = r0
    //     0x14c63cc: stur            w0, [x1, #7]
    // 0x14c63d0: r0 = Padding()
    //     0x14c63d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c63d4: mov             x2, x0
    // 0x14c63d8: r0 = Instance_EdgeInsets
    //     0x14c63d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14c63dc: ldr             x0, [x0, #0x980]
    // 0x14c63e0: stur            x2, [fp, #-0x20]
    // 0x14c63e4: StoreField: r2->field_f = r0
    //     0x14c63e4: stur            w0, [x2, #0xf]
    // 0x14c63e8: ldur            x0, [fp, #-0x18]
    // 0x14c63ec: StoreField: r2->field_b = r0
    //     0x14c63ec: stur            w0, [x2, #0xb]
    // 0x14c63f0: ldur            x0, [fp, #-0x10]
    // 0x14c63f4: LoadField: r1 = r0->field_b
    //     0x14c63f4: ldur            w1, [x0, #0xb]
    // 0x14c63f8: LoadField: r3 = r0->field_f
    //     0x14c63f8: ldur            w3, [x0, #0xf]
    // 0x14c63fc: DecompressPointer r3
    //     0x14c63fc: add             x3, x3, HEAP, lsl #32
    // 0x14c6400: LoadField: r4 = r3->field_b
    //     0x14c6400: ldur            w4, [x3, #0xb]
    // 0x14c6404: r3 = LoadInt32Instr(r1)
    //     0x14c6404: sbfx            x3, x1, #1, #0x1f
    // 0x14c6408: stur            x3, [fp, #-0x48]
    // 0x14c640c: r1 = LoadInt32Instr(r4)
    //     0x14c640c: sbfx            x1, x4, #1, #0x1f
    // 0x14c6410: cmp             x3, x1
    // 0x14c6414: b.ne            #0x14c6420
    // 0x14c6418: mov             x1, x0
    // 0x14c641c: r0 = _growToNextCapacity()
    //     0x14c641c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14c6420: ldur            x2, [fp, #-0x10]
    // 0x14c6424: ldur            x3, [fp, #-0x48]
    // 0x14c6428: add             x0, x3, #1
    // 0x14c642c: lsl             x1, x0, #1
    // 0x14c6430: StoreField: r2->field_b = r1
    //     0x14c6430: stur            w1, [x2, #0xb]
    // 0x14c6434: LoadField: r1 = r2->field_f
    //     0x14c6434: ldur            w1, [x2, #0xf]
    // 0x14c6438: DecompressPointer r1
    //     0x14c6438: add             x1, x1, HEAP, lsl #32
    // 0x14c643c: ldur            x0, [fp, #-0x20]
    // 0x14c6440: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14c6440: add             x25, x1, x3, lsl #2
    //     0x14c6444: add             x25, x25, #0xf
    //     0x14c6448: str             w0, [x25]
    //     0x14c644c: tbz             w0, #0, #0x14c6468
    //     0x14c6450: ldurb           w16, [x1, #-1]
    //     0x14c6454: ldurb           w17, [x0, #-1]
    //     0x14c6458: and             x16, x17, x16, lsr #2
    //     0x14c645c: tst             x16, HEAP, lsr #32
    //     0x14c6460: b.eq            #0x14c6468
    //     0x14c6464: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c6468: b               #0x14c64fc
    // 0x14c646c: ldur            x2, [fp, #-0x10]
    // 0x14c6470: r0 = Container()
    //     0x14c6470: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c6474: mov             x1, x0
    // 0x14c6478: stur            x0, [fp, #-0x18]
    // 0x14c647c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14c647c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14c6480: r0 = Container()
    //     0x14c6480: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c6484: ldur            x0, [fp, #-0x10]
    // 0x14c6488: LoadField: r1 = r0->field_b
    //     0x14c6488: ldur            w1, [x0, #0xb]
    // 0x14c648c: LoadField: r2 = r0->field_f
    //     0x14c648c: ldur            w2, [x0, #0xf]
    // 0x14c6490: DecompressPointer r2
    //     0x14c6490: add             x2, x2, HEAP, lsl #32
    // 0x14c6494: LoadField: r3 = r2->field_b
    //     0x14c6494: ldur            w3, [x2, #0xb]
    // 0x14c6498: r2 = LoadInt32Instr(r1)
    //     0x14c6498: sbfx            x2, x1, #1, #0x1f
    // 0x14c649c: stur            x2, [fp, #-0x48]
    // 0x14c64a0: r1 = LoadInt32Instr(r3)
    //     0x14c64a0: sbfx            x1, x3, #1, #0x1f
    // 0x14c64a4: cmp             x2, x1
    // 0x14c64a8: b.ne            #0x14c64b4
    // 0x14c64ac: mov             x1, x0
    // 0x14c64b0: r0 = _growToNextCapacity()
    //     0x14c64b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14c64b4: ldur            x2, [fp, #-0x10]
    // 0x14c64b8: ldur            x3, [fp, #-0x48]
    // 0x14c64bc: add             x0, x3, #1
    // 0x14c64c0: lsl             x1, x0, #1
    // 0x14c64c4: StoreField: r2->field_b = r1
    //     0x14c64c4: stur            w1, [x2, #0xb]
    // 0x14c64c8: LoadField: r1 = r2->field_f
    //     0x14c64c8: ldur            w1, [x2, #0xf]
    // 0x14c64cc: DecompressPointer r1
    //     0x14c64cc: add             x1, x1, HEAP, lsl #32
    // 0x14c64d0: ldur            x0, [fp, #-0x18]
    // 0x14c64d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14c64d4: add             x25, x1, x3, lsl #2
    //     0x14c64d8: add             x25, x25, #0xf
    //     0x14c64dc: str             w0, [x25]
    //     0x14c64e0: tbz             w0, #0, #0x14c64fc
    //     0x14c64e4: ldurb           w16, [x1, #-1]
    //     0x14c64e8: ldurb           w17, [x0, #-1]
    //     0x14c64ec: and             x16, x17, x16, lsr #2
    //     0x14c64f0: tst             x16, HEAP, lsr #32
    //     0x14c64f4: b.eq            #0x14c64fc
    //     0x14c64f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c64fc: ldur            x0, [fp, #-8]
    // 0x14c6500: LoadField: r1 = r0->field_f
    //     0x14c6500: ldur            w1, [x0, #0xf]
    // 0x14c6504: DecompressPointer r1
    //     0x14c6504: add             x1, x1, HEAP, lsl #32
    // 0x14c6508: r0 = controller()
    //     0x14c6508: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c650c: LoadField: r1 = r0->field_57
    //     0x14c650c: ldur            w1, [x0, #0x57]
    // 0x14c6510: DecompressPointer r1
    //     0x14c6510: add             x1, x1, HEAP, lsl #32
    // 0x14c6514: r0 = value()
    //     0x14c6514: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c6518: cmp             w0, NULL
    // 0x14c651c: b.ne            #0x14c6528
    // 0x14c6520: r0 = Null
    //     0x14c6520: mov             x0, NULL
    // 0x14c6524: b               #0x14c6534
    // 0x14c6528: LoadField: r1 = r0->field_23
    //     0x14c6528: ldur            w1, [x0, #0x23]
    // 0x14c652c: DecompressPointer r1
    //     0x14c652c: add             x1, x1, HEAP, lsl #32
    // 0x14c6530: mov             x0, x1
    // 0x14c6534: cmp             w0, NULL
    // 0x14c6538: b.ne            #0x14c6544
    // 0x14c653c: r1 = true
    //     0x14c653c: add             x1, NULL, #0x20  ; true
    // 0x14c6540: b               #0x14c6548
    // 0x14c6544: mov             x1, x0
    // 0x14c6548: ldur            x0, [fp, #-8]
    // 0x14c654c: eor             x2, x1, #0x10
    // 0x14c6550: stur            x2, [fp, #-0x18]
    // 0x14c6554: LoadField: r1 = r0->field_f
    //     0x14c6554: ldur            w1, [x0, #0xf]
    // 0x14c6558: DecompressPointer r1
    //     0x14c6558: add             x1, x1, HEAP, lsl #32
    // 0x14c655c: r0 = controller()
    //     0x14c655c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6560: LoadField: r1 = r0->field_57
    //     0x14c6560: ldur            w1, [x0, #0x57]
    // 0x14c6564: DecompressPointer r1
    //     0x14c6564: add             x1, x1, HEAP, lsl #32
    // 0x14c6568: r0 = value()
    //     0x14c6568: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c656c: cmp             w0, NULL
    // 0x14c6570: b.ne            #0x14c657c
    // 0x14c6574: r0 = Null
    //     0x14c6574: mov             x0, NULL
    // 0x14c6578: b               #0x14c659c
    // 0x14c657c: LoadField: r1 = r0->field_13
    //     0x14c657c: ldur            w1, [x0, #0x13]
    // 0x14c6580: DecompressPointer r1
    //     0x14c6580: add             x1, x1, HEAP, lsl #32
    // 0x14c6584: cmp             w1, NULL
    // 0x14c6588: b.ne            #0x14c6594
    // 0x14c658c: r0 = Null
    //     0x14c658c: mov             x0, NULL
    // 0x14c6590: b               #0x14c659c
    // 0x14c6594: LoadField: r0 = r1->field_7
    //     0x14c6594: ldur            w0, [x1, #7]
    // 0x14c6598: DecompressPointer r0
    //     0x14c6598: add             x0, x0, HEAP, lsl #32
    // 0x14c659c: cmp             w0, NULL
    // 0x14c65a0: b.ne            #0x14c65ac
    // 0x14c65a4: r2 = ""
    //     0x14c65a4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c65a8: b               #0x14c65b0
    // 0x14c65ac: mov             x2, x0
    // 0x14c65b0: ldur            x0, [fp, #-8]
    // 0x14c65b4: stur            x2, [fp, #-0x20]
    // 0x14c65b8: LoadField: r1 = r0->field_13
    //     0x14c65b8: ldur            w1, [x0, #0x13]
    // 0x14c65bc: DecompressPointer r1
    //     0x14c65bc: add             x1, x1, HEAP, lsl #32
    // 0x14c65c0: r0 = of()
    //     0x14c65c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c65c4: LoadField: r1 = r0->field_87
    //     0x14c65c4: ldur            w1, [x0, #0x87]
    // 0x14c65c8: DecompressPointer r1
    //     0x14c65c8: add             x1, x1, HEAP, lsl #32
    // 0x14c65cc: LoadField: r0 = r1->field_7
    //     0x14c65cc: ldur            w0, [x1, #7]
    // 0x14c65d0: DecompressPointer r0
    //     0x14c65d0: add             x0, x0, HEAP, lsl #32
    // 0x14c65d4: r16 = 14.000000
    //     0x14c65d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c65d8: ldr             x16, [x16, #0x1d8]
    // 0x14c65dc: r30 = Instance_Color
    //     0x14c65dc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c65e0: stp             lr, x16, [SP]
    // 0x14c65e4: mov             x1, x0
    // 0x14c65e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c65e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c65ec: ldr             x4, [x4, #0xaa0]
    // 0x14c65f0: r0 = copyWith()
    //     0x14c65f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c65f4: stur            x0, [fp, #-0x28]
    // 0x14c65f8: r0 = Text()
    //     0x14c65f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c65fc: mov             x1, x0
    // 0x14c6600: ldur            x0, [fp, #-0x20]
    // 0x14c6604: stur            x1, [fp, #-0x30]
    // 0x14c6608: StoreField: r1->field_b = r0
    //     0x14c6608: stur            w0, [x1, #0xb]
    // 0x14c660c: ldur            x0, [fp, #-0x28]
    // 0x14c6610: StoreField: r1->field_13 = r0
    //     0x14c6610: stur            w0, [x1, #0x13]
    // 0x14c6614: r0 = Padding()
    //     0x14c6614: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6618: mov             x2, x0
    // 0x14c661c: r0 = Instance_EdgeInsets
    //     0x14c661c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x14c6620: ldr             x0, [x0, #0x668]
    // 0x14c6624: stur            x2, [fp, #-0x20]
    // 0x14c6628: StoreField: r2->field_f = r0
    //     0x14c6628: stur            w0, [x2, #0xf]
    // 0x14c662c: ldur            x0, [fp, #-0x30]
    // 0x14c6630: StoreField: r2->field_b = r0
    //     0x14c6630: stur            w0, [x2, #0xb]
    // 0x14c6634: ldur            x0, [fp, #-8]
    // 0x14c6638: LoadField: r1 = r0->field_f
    //     0x14c6638: ldur            w1, [x0, #0xf]
    // 0x14c663c: DecompressPointer r1
    //     0x14c663c: add             x1, x1, HEAP, lsl #32
    // 0x14c6640: r0 = controller()
    //     0x14c6640: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6644: LoadField: r1 = r0->field_57
    //     0x14c6644: ldur            w1, [x0, #0x57]
    // 0x14c6648: DecompressPointer r1
    //     0x14c6648: add             x1, x1, HEAP, lsl #32
    // 0x14c664c: r0 = value()
    //     0x14c664c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c6650: cmp             w0, NULL
    // 0x14c6654: b.ne            #0x14c6660
    // 0x14c6658: r0 = Null
    //     0x14c6658: mov             x0, NULL
    // 0x14c665c: b               #0x14c6680
    // 0x14c6660: LoadField: r1 = r0->field_13
    //     0x14c6660: ldur            w1, [x0, #0x13]
    // 0x14c6664: DecompressPointer r1
    //     0x14c6664: add             x1, x1, HEAP, lsl #32
    // 0x14c6668: cmp             w1, NULL
    // 0x14c666c: b.ne            #0x14c6678
    // 0x14c6670: r0 = Null
    //     0x14c6670: mov             x0, NULL
    // 0x14c6674: b               #0x14c6680
    // 0x14c6678: LoadField: r0 = r1->field_b
    //     0x14c6678: ldur            w0, [x1, #0xb]
    // 0x14c667c: DecompressPointer r0
    //     0x14c667c: add             x0, x0, HEAP, lsl #32
    // 0x14c6680: cmp             w0, NULL
    // 0x14c6684: b.ne            #0x14c6690
    // 0x14c6688: r3 = ""
    //     0x14c6688: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c668c: b               #0x14c6694
    // 0x14c6690: mov             x3, x0
    // 0x14c6694: ldur            x2, [fp, #-8]
    // 0x14c6698: ldur            x0, [fp, #-0x20]
    // 0x14c669c: stur            x3, [fp, #-0x28]
    // 0x14c66a0: LoadField: r1 = r2->field_13
    //     0x14c66a0: ldur            w1, [x2, #0x13]
    // 0x14c66a4: DecompressPointer r1
    //     0x14c66a4: add             x1, x1, HEAP, lsl #32
    // 0x14c66a8: r0 = of()
    //     0x14c66a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c66ac: LoadField: r1 = r0->field_87
    //     0x14c66ac: ldur            w1, [x0, #0x87]
    // 0x14c66b0: DecompressPointer r1
    //     0x14c66b0: add             x1, x1, HEAP, lsl #32
    // 0x14c66b4: LoadField: r0 = r1->field_2b
    //     0x14c66b4: ldur            w0, [x1, #0x2b]
    // 0x14c66b8: DecompressPointer r0
    //     0x14c66b8: add             x0, x0, HEAP, lsl #32
    // 0x14c66bc: stur            x0, [fp, #-0x30]
    // 0x14c66c0: r1 = Instance_Color
    //     0x14c66c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c66c4: d0 = 0.700000
    //     0x14c66c4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c66c8: ldr             d0, [x17, #0xf48]
    // 0x14c66cc: r0 = withOpacity()
    //     0x14c66cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c66d0: r16 = 12.000000
    //     0x14c66d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c66d4: ldr             x16, [x16, #0x9e8]
    // 0x14c66d8: stp             x0, x16, [SP]
    // 0x14c66dc: ldur            x1, [fp, #-0x30]
    // 0x14c66e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c66e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c66e4: ldr             x4, [x4, #0xaa0]
    // 0x14c66e8: r0 = copyWith()
    //     0x14c66e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c66ec: stur            x0, [fp, #-0x30]
    // 0x14c66f0: r0 = Text()
    //     0x14c66f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c66f4: mov             x1, x0
    // 0x14c66f8: ldur            x0, [fp, #-0x28]
    // 0x14c66fc: stur            x1, [fp, #-0x38]
    // 0x14c6700: StoreField: r1->field_b = r0
    //     0x14c6700: stur            w0, [x1, #0xb]
    // 0x14c6704: ldur            x0, [fp, #-0x30]
    // 0x14c6708: StoreField: r1->field_13 = r0
    //     0x14c6708: stur            w0, [x1, #0x13]
    // 0x14c670c: r0 = Padding()
    //     0x14c670c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6710: mov             x3, x0
    // 0x14c6714: r0 = Instance_EdgeInsets
    //     0x14c6714: add             x0, PP, #0x42, lsl #12  ; [pp+0x42618] Obj!EdgeInsets@d59fc1
    //     0x14c6718: ldr             x0, [x0, #0x618]
    // 0x14c671c: stur            x3, [fp, #-0x28]
    // 0x14c6720: StoreField: r3->field_f = r0
    //     0x14c6720: stur            w0, [x3, #0xf]
    // 0x14c6724: ldur            x0, [fp, #-0x38]
    // 0x14c6728: StoreField: r3->field_b = r0
    //     0x14c6728: stur            w0, [x3, #0xb]
    // 0x14c672c: r1 = Null
    //     0x14c672c: mov             x1, NULL
    // 0x14c6730: r2 = 4
    //     0x14c6730: movz            x2, #0x4
    // 0x14c6734: r0 = AllocateArray()
    //     0x14c6734: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c6738: mov             x2, x0
    // 0x14c673c: ldur            x0, [fp, #-0x20]
    // 0x14c6740: stur            x2, [fp, #-0x30]
    // 0x14c6744: StoreField: r2->field_f = r0
    //     0x14c6744: stur            w0, [x2, #0xf]
    // 0x14c6748: ldur            x0, [fp, #-0x28]
    // 0x14c674c: StoreField: r2->field_13 = r0
    //     0x14c674c: stur            w0, [x2, #0x13]
    // 0x14c6750: r1 = <Widget>
    //     0x14c6750: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c6754: r0 = AllocateGrowableArray()
    //     0x14c6754: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c6758: mov             x1, x0
    // 0x14c675c: ldur            x0, [fp, #-0x30]
    // 0x14c6760: stur            x1, [fp, #-0x20]
    // 0x14c6764: StoreField: r1->field_f = r0
    //     0x14c6764: stur            w0, [x1, #0xf]
    // 0x14c6768: r0 = 4
    //     0x14c6768: movz            x0, #0x4
    // 0x14c676c: StoreField: r1->field_b = r0
    //     0x14c676c: stur            w0, [x1, #0xb]
    // 0x14c6770: r0 = Column()
    //     0x14c6770: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c6774: mov             x1, x0
    // 0x14c6778: r0 = Instance_Axis
    //     0x14c6778: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c677c: stur            x1, [fp, #-0x28]
    // 0x14c6780: StoreField: r1->field_f = r0
    //     0x14c6780: stur            w0, [x1, #0xf]
    // 0x14c6784: r2 = Instance_MainAxisAlignment
    //     0x14c6784: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c6788: ldr             x2, [x2, #0xa08]
    // 0x14c678c: StoreField: r1->field_13 = r2
    //     0x14c678c: stur            w2, [x1, #0x13]
    // 0x14c6790: r3 = Instance_MainAxisSize
    //     0x14c6790: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c6794: ldr             x3, [x3, #0xa10]
    // 0x14c6798: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c6798: stur            w3, [x1, #0x17]
    // 0x14c679c: r4 = Instance_CrossAxisAlignment
    //     0x14c679c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c67a0: ldr             x4, [x4, #0x890]
    // 0x14c67a4: StoreField: r1->field_1b = r4
    //     0x14c67a4: stur            w4, [x1, #0x1b]
    // 0x14c67a8: r5 = Instance_VerticalDirection
    //     0x14c67a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c67ac: ldr             x5, [x5, #0xa20]
    // 0x14c67b0: StoreField: r1->field_23 = r5
    //     0x14c67b0: stur            w5, [x1, #0x23]
    // 0x14c67b4: r6 = Instance_Clip
    //     0x14c67b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c67b8: ldr             x6, [x6, #0x38]
    // 0x14c67bc: StoreField: r1->field_2b = r6
    //     0x14c67bc: stur            w6, [x1, #0x2b]
    // 0x14c67c0: StoreField: r1->field_2f = rZR
    //     0x14c67c0: stur            xzr, [x1, #0x2f]
    // 0x14c67c4: ldur            x7, [fp, #-0x20]
    // 0x14c67c8: StoreField: r1->field_b = r7
    //     0x14c67c8: stur            w7, [x1, #0xb]
    // 0x14c67cc: r0 = Container()
    //     0x14c67cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c67d0: stur            x0, [fp, #-0x20]
    // 0x14c67d4: r16 = inf
    //     0x14c67d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14c67d8: ldr             x16, [x16, #0x9f8]
    // 0x14c67dc: r30 = Instance_EdgeInsets
    //     0x14c67dc: add             lr, PP, #0x42, lsl #12  ; [pp+0x42730] Obj!EdgeInsets@d59f91
    //     0x14c67e0: ldr             lr, [lr, #0x730]
    // 0x14c67e4: stp             lr, x16, [SP, #8]
    // 0x14c67e8: ldur            x16, [fp, #-0x28]
    // 0x14c67ec: str             x16, [SP]
    // 0x14c67f0: mov             x1, x0
    // 0x14c67f4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0x14c67f4: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0x14c67f8: ldr             x4, [x4, #0x628]
    // 0x14c67fc: r0 = Container()
    //     0x14c67fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c6800: r1 = Instance_Color
    //     0x14c6800: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c6804: d0 = 0.030000
    //     0x14c6804: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14c6808: ldr             d0, [x17, #0x238]
    // 0x14c680c: r0 = withOpacity()
    //     0x14c680c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c6810: stur            x0, [fp, #-0x28]
    // 0x14c6814: r0 = Radius()
    //     0x14c6814: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c6818: d0 = 12.000000
    //     0x14c6818: fmov            d0, #12.00000000
    // 0x14c681c: stur            x0, [fp, #-0x30]
    // 0x14c6820: StoreField: r0->field_7 = d0
    //     0x14c6820: stur            d0, [x0, #7]
    // 0x14c6824: StoreField: r0->field_f = d0
    //     0x14c6824: stur            d0, [x0, #0xf]
    // 0x14c6828: r0 = BorderRadius()
    //     0x14c6828: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c682c: mov             x1, x0
    // 0x14c6830: ldur            x0, [fp, #-0x30]
    // 0x14c6834: stur            x1, [fp, #-0x38]
    // 0x14c6838: StoreField: r1->field_7 = r0
    //     0x14c6838: stur            w0, [x1, #7]
    // 0x14c683c: StoreField: r1->field_b = r0
    //     0x14c683c: stur            w0, [x1, #0xb]
    // 0x14c6840: StoreField: r1->field_f = r0
    //     0x14c6840: stur            w0, [x1, #0xf]
    // 0x14c6844: StoreField: r1->field_13 = r0
    //     0x14c6844: stur            w0, [x1, #0x13]
    // 0x14c6848: r0 = BoxDecoration()
    //     0x14c6848: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14c684c: mov             x1, x0
    // 0x14c6850: ldur            x0, [fp, #-0x28]
    // 0x14c6854: stur            x1, [fp, #-0x30]
    // 0x14c6858: StoreField: r1->field_7 = r0
    //     0x14c6858: stur            w0, [x1, #7]
    // 0x14c685c: ldur            x0, [fp, #-0x38]
    // 0x14c6860: StoreField: r1->field_13 = r0
    //     0x14c6860: stur            w0, [x1, #0x13]
    // 0x14c6864: r0 = Instance_BoxShape
    //     0x14c6864: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c6868: ldr             x0, [x0, #0x80]
    // 0x14c686c: StoreField: r1->field_23 = r0
    //     0x14c686c: stur            w0, [x1, #0x23]
    // 0x14c6870: r0 = SvgPicture()
    //     0x14c6870: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14c6874: stur            x0, [fp, #-0x28]
    // 0x14c6878: r16 = Instance_BoxFit
    //     0x14c6878: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14c687c: ldr             x16, [x16, #0xb18]
    // 0x14c6880: r30 = Instance_ColorFilter
    //     0x14c6880: add             lr, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0x14c6884: ldr             lr, [lr, #0x818]
    // 0x14c6888: stp             lr, x16, [SP]
    // 0x14c688c: mov             x1, x0
    // 0x14c6890: r2 = "assets/images/reason_cosmetic_icon.svg"
    //     0x14c6890: add             x2, PP, #0x42, lsl #12  ; [pp+0x42630] "assets/images/reason_cosmetic_icon.svg"
    //     0x14c6894: ldr             x2, [x2, #0x630]
    // 0x14c6898: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14c6898: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14c689c: ldr             x4, [x4, #0x820]
    // 0x14c68a0: r0 = SvgPicture.asset()
    //     0x14c68a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14c68a4: ldur            x0, [fp, #-8]
    // 0x14c68a8: LoadField: r1 = r0->field_f
    //     0x14c68a8: ldur            w1, [x0, #0xf]
    // 0x14c68ac: DecompressPointer r1
    //     0x14c68ac: add             x1, x1, HEAP, lsl #32
    // 0x14c68b0: r0 = controller()
    //     0x14c68b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c68b4: LoadField: r1 = r0->field_57
    //     0x14c68b4: ldur            w1, [x0, #0x57]
    // 0x14c68b8: DecompressPointer r1
    //     0x14c68b8: add             x1, x1, HEAP, lsl #32
    // 0x14c68bc: r0 = value()
    //     0x14c68bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c68c0: cmp             w0, NULL
    // 0x14c68c4: b.ne            #0x14c68d0
    // 0x14c68c8: r0 = Null
    //     0x14c68c8: mov             x0, NULL
    // 0x14c68cc: b               #0x14c68dc
    // 0x14c68d0: LoadField: r1 = r0->field_1b
    //     0x14c68d0: ldur            w1, [x0, #0x1b]
    // 0x14c68d4: DecompressPointer r1
    //     0x14c68d4: add             x1, x1, HEAP, lsl #32
    // 0x14c68d8: mov             x0, x1
    // 0x14c68dc: cmp             w0, NULL
    // 0x14c68e0: b.ne            #0x14c68ec
    // 0x14c68e4: r3 = ""
    //     0x14c68e4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c68e8: b               #0x14c68f0
    // 0x14c68ec: mov             x3, x0
    // 0x14c68f0: ldur            x0, [fp, #-8]
    // 0x14c68f4: ldur            x2, [fp, #-0x28]
    // 0x14c68f8: stur            x3, [fp, #-0x38]
    // 0x14c68fc: LoadField: r1 = r0->field_13
    //     0x14c68fc: ldur            w1, [x0, #0x13]
    // 0x14c6900: DecompressPointer r1
    //     0x14c6900: add             x1, x1, HEAP, lsl #32
    // 0x14c6904: r0 = of()
    //     0x14c6904: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c6908: LoadField: r1 = r0->field_87
    //     0x14c6908: ldur            w1, [x0, #0x87]
    // 0x14c690c: DecompressPointer r1
    //     0x14c690c: add             x1, x1, HEAP, lsl #32
    // 0x14c6910: LoadField: r0 = r1->field_2b
    //     0x14c6910: ldur            w0, [x1, #0x2b]
    // 0x14c6914: DecompressPointer r0
    //     0x14c6914: add             x0, x0, HEAP, lsl #32
    // 0x14c6918: stur            x0, [fp, #-0x40]
    // 0x14c691c: r1 = Instance_Color
    //     0x14c691c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c6920: d0 = 0.700000
    //     0x14c6920: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c6924: ldr             d0, [x17, #0xf48]
    // 0x14c6928: r0 = withOpacity()
    //     0x14c6928: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c692c: r16 = 12.000000
    //     0x14c692c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c6930: ldr             x16, [x16, #0x9e8]
    // 0x14c6934: stp             x0, x16, [SP]
    // 0x14c6938: ldur            x1, [fp, #-0x40]
    // 0x14c693c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c693c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c6940: ldr             x4, [x4, #0xaa0]
    // 0x14c6944: r0 = copyWith()
    //     0x14c6944: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c6948: stur            x0, [fp, #-0x40]
    // 0x14c694c: r0 = Text()
    //     0x14c694c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c6950: mov             x2, x0
    // 0x14c6954: ldur            x0, [fp, #-0x38]
    // 0x14c6958: stur            x2, [fp, #-0x50]
    // 0x14c695c: StoreField: r2->field_b = r0
    //     0x14c695c: stur            w0, [x2, #0xb]
    // 0x14c6960: ldur            x0, [fp, #-0x40]
    // 0x14c6964: StoreField: r2->field_13 = r0
    //     0x14c6964: stur            w0, [x2, #0x13]
    // 0x14c6968: r0 = Instance_TextOverflow
    //     0x14c6968: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14c696c: ldr             x0, [x0, #0xe10]
    // 0x14c6970: StoreField: r2->field_2b = r0
    //     0x14c6970: stur            w0, [x2, #0x2b]
    // 0x14c6974: r0 = 4
    //     0x14c6974: movz            x0, #0x4
    // 0x14c6978: StoreField: r2->field_37 = r0
    //     0x14c6978: stur            w0, [x2, #0x37]
    // 0x14c697c: r1 = <FlexParentData>
    //     0x14c697c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14c6980: ldr             x1, [x1, #0xe00]
    // 0x14c6984: r0 = Expanded()
    //     0x14c6984: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14c6988: mov             x3, x0
    // 0x14c698c: r0 = 1
    //     0x14c698c: movz            x0, #0x1
    // 0x14c6990: stur            x3, [fp, #-0x38]
    // 0x14c6994: StoreField: r3->field_13 = r0
    //     0x14c6994: stur            x0, [x3, #0x13]
    // 0x14c6998: r0 = Instance_FlexFit
    //     0x14c6998: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14c699c: ldr             x0, [x0, #0xe08]
    // 0x14c69a0: StoreField: r3->field_1b = r0
    //     0x14c69a0: stur            w0, [x3, #0x1b]
    // 0x14c69a4: ldur            x0, [fp, #-0x50]
    // 0x14c69a8: StoreField: r3->field_b = r0
    //     0x14c69a8: stur            w0, [x3, #0xb]
    // 0x14c69ac: r1 = Null
    //     0x14c69ac: mov             x1, NULL
    // 0x14c69b0: r2 = 6
    //     0x14c69b0: movz            x2, #0x6
    // 0x14c69b4: r0 = AllocateArray()
    //     0x14c69b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c69b8: mov             x2, x0
    // 0x14c69bc: ldur            x0, [fp, #-0x28]
    // 0x14c69c0: stur            x2, [fp, #-0x40]
    // 0x14c69c4: StoreField: r2->field_f = r0
    //     0x14c69c4: stur            w0, [x2, #0xf]
    // 0x14c69c8: r16 = Instance_SizedBox
    //     0x14c69c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14c69cc: ldr             x16, [x16, #0xb20]
    // 0x14c69d0: StoreField: r2->field_13 = r16
    //     0x14c69d0: stur            w16, [x2, #0x13]
    // 0x14c69d4: ldur            x0, [fp, #-0x38]
    // 0x14c69d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c69d8: stur            w0, [x2, #0x17]
    // 0x14c69dc: r1 = <Widget>
    //     0x14c69dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c69e0: r0 = AllocateGrowableArray()
    //     0x14c69e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c69e4: mov             x1, x0
    // 0x14c69e8: ldur            x0, [fp, #-0x40]
    // 0x14c69ec: stur            x1, [fp, #-0x28]
    // 0x14c69f0: StoreField: r1->field_f = r0
    //     0x14c69f0: stur            w0, [x1, #0xf]
    // 0x14c69f4: r0 = 6
    //     0x14c69f4: movz            x0, #0x6
    // 0x14c69f8: StoreField: r1->field_b = r0
    //     0x14c69f8: stur            w0, [x1, #0xb]
    // 0x14c69fc: r0 = Row()
    //     0x14c69fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14c6a00: mov             x1, x0
    // 0x14c6a04: r0 = Instance_Axis
    //     0x14c6a04: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c6a08: stur            x1, [fp, #-0x38]
    // 0x14c6a0c: StoreField: r1->field_f = r0
    //     0x14c6a0c: stur            w0, [x1, #0xf]
    // 0x14c6a10: r0 = Instance_MainAxisAlignment
    //     0x14c6a10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c6a14: ldr             x0, [x0, #0xa08]
    // 0x14c6a18: StoreField: r1->field_13 = r0
    //     0x14c6a18: stur            w0, [x1, #0x13]
    // 0x14c6a1c: r2 = Instance_MainAxisSize
    //     0x14c6a1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c6a20: ldr             x2, [x2, #0xa10]
    // 0x14c6a24: ArrayStore: r1[0] = r2  ; List_4
    //     0x14c6a24: stur            w2, [x1, #0x17]
    // 0x14c6a28: r3 = Instance_CrossAxisAlignment
    //     0x14c6a28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c6a2c: ldr             x3, [x3, #0xa18]
    // 0x14c6a30: StoreField: r1->field_1b = r3
    //     0x14c6a30: stur            w3, [x1, #0x1b]
    // 0x14c6a34: r3 = Instance_VerticalDirection
    //     0x14c6a34: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c6a38: ldr             x3, [x3, #0xa20]
    // 0x14c6a3c: StoreField: r1->field_23 = r3
    //     0x14c6a3c: stur            w3, [x1, #0x23]
    // 0x14c6a40: r4 = Instance_Clip
    //     0x14c6a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c6a44: ldr             x4, [x4, #0x38]
    // 0x14c6a48: StoreField: r1->field_2b = r4
    //     0x14c6a48: stur            w4, [x1, #0x2b]
    // 0x14c6a4c: StoreField: r1->field_2f = rZR
    //     0x14c6a4c: stur            xzr, [x1, #0x2f]
    // 0x14c6a50: ldur            x5, [fp, #-0x28]
    // 0x14c6a54: StoreField: r1->field_b = r5
    //     0x14c6a54: stur            w5, [x1, #0xb]
    // 0x14c6a58: r0 = Container()
    //     0x14c6a58: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c6a5c: stur            x0, [fp, #-0x28]
    // 0x14c6a60: r16 = Instance_EdgeInsets
    //     0x14c6a60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c6a64: ldr             x16, [x16, #0x1f0]
    // 0x14c6a68: ldur            lr, [fp, #-0x30]
    // 0x14c6a6c: stp             lr, x16, [SP, #8]
    // 0x14c6a70: ldur            x16, [fp, #-0x38]
    // 0x14c6a74: str             x16, [SP]
    // 0x14c6a78: mov             x1, x0
    // 0x14c6a7c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x14c6a7c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x14c6a80: ldr             x4, [x4, #0x610]
    // 0x14c6a84: r0 = Container()
    //     0x14c6a84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c6a88: r0 = Padding()
    //     0x14c6a88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6a8c: mov             x2, x0
    // 0x14c6a90: r0 = Instance_EdgeInsets
    //     0x14c6a90: add             x0, PP, #0x42, lsl #12  ; [pp+0x42638] Obj!EdgeInsets@d591e1
    //     0x14c6a94: ldr             x0, [x0, #0x638]
    // 0x14c6a98: stur            x2, [fp, #-0x30]
    // 0x14c6a9c: StoreField: r2->field_f = r0
    //     0x14c6a9c: stur            w0, [x2, #0xf]
    // 0x14c6aa0: ldur            x0, [fp, #-0x28]
    // 0x14c6aa4: StoreField: r2->field_b = r0
    //     0x14c6aa4: stur            w0, [x2, #0xb]
    // 0x14c6aa8: ldur            x0, [fp, #-8]
    // 0x14c6aac: LoadField: r1 = r0->field_13
    //     0x14c6aac: ldur            w1, [x0, #0x13]
    // 0x14c6ab0: DecompressPointer r1
    //     0x14c6ab0: add             x1, x1, HEAP, lsl #32
    // 0x14c6ab4: r0 = of()
    //     0x14c6ab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c6ab8: LoadField: r1 = r0->field_87
    //     0x14c6ab8: ldur            w1, [x0, #0x87]
    // 0x14c6abc: DecompressPointer r1
    //     0x14c6abc: add             x1, x1, HEAP, lsl #32
    // 0x14c6ac0: LoadField: r0 = r1->field_7
    //     0x14c6ac0: ldur            w0, [x1, #7]
    // 0x14c6ac4: DecompressPointer r0
    //     0x14c6ac4: add             x0, x0, HEAP, lsl #32
    // 0x14c6ac8: r16 = 14.000000
    //     0x14c6ac8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c6acc: ldr             x16, [x16, #0x1d8]
    // 0x14c6ad0: r30 = Instance_Color
    //     0x14c6ad0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c6ad4: stp             lr, x16, [SP]
    // 0x14c6ad8: mov             x1, x0
    // 0x14c6adc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c6adc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c6ae0: ldr             x4, [x4, #0xaa0]
    // 0x14c6ae4: r0 = copyWith()
    //     0x14c6ae4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c6ae8: stur            x0, [fp, #-0x28]
    // 0x14c6aec: r0 = Text()
    //     0x14c6aec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c6af0: mov             x1, x0
    // 0x14c6af4: r0 = "Note:"
    //     0x14c6af4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33838] "Note:"
    //     0x14c6af8: ldr             x0, [x0, #0x838]
    // 0x14c6afc: stur            x1, [fp, #-0x38]
    // 0x14c6b00: StoreField: r1->field_b = r0
    //     0x14c6b00: stur            w0, [x1, #0xb]
    // 0x14c6b04: ldur            x0, [fp, #-0x28]
    // 0x14c6b08: StoreField: r1->field_13 = r0
    //     0x14c6b08: stur            w0, [x1, #0x13]
    // 0x14c6b0c: r0 = Padding()
    //     0x14c6b0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6b10: mov             x2, x0
    // 0x14c6b14: r0 = Instance_EdgeInsets
    //     0x14c6b14: add             x0, PP, #0x33, lsl #12  ; [pp+0x33840] Obj!EdgeInsets@d59ed1
    //     0x14c6b18: ldr             x0, [x0, #0x840]
    // 0x14c6b1c: stur            x2, [fp, #-0x28]
    // 0x14c6b20: StoreField: r2->field_f = r0
    //     0x14c6b20: stur            w0, [x2, #0xf]
    // 0x14c6b24: ldur            x0, [fp, #-0x38]
    // 0x14c6b28: StoreField: r2->field_b = r0
    //     0x14c6b28: stur            w0, [x2, #0xb]
    // 0x14c6b2c: ldur            x0, [fp, #-8]
    // 0x14c6b30: LoadField: r1 = r0->field_f
    //     0x14c6b30: ldur            w1, [x0, #0xf]
    // 0x14c6b34: DecompressPointer r1
    //     0x14c6b34: add             x1, x1, HEAP, lsl #32
    // 0x14c6b38: r0 = controller()
    //     0x14c6b38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c6b3c: LoadField: r1 = r0->field_57
    //     0x14c6b3c: ldur            w1, [x0, #0x57]
    // 0x14c6b40: DecompressPointer r1
    //     0x14c6b40: add             x1, x1, HEAP, lsl #32
    // 0x14c6b44: r0 = value()
    //     0x14c6b44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c6b48: cmp             w0, NULL
    // 0x14c6b4c: b.ne            #0x14c6b58
    // 0x14c6b50: r0 = Null
    //     0x14c6b50: mov             x0, NULL
    // 0x14c6b54: b               #0x14c6b64
    // 0x14c6b58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14c6b58: ldur            w1, [x0, #0x17]
    // 0x14c6b5c: DecompressPointer r1
    //     0x14c6b5c: add             x1, x1, HEAP, lsl #32
    // 0x14c6b60: mov             x0, x1
    // 0x14c6b64: cmp             w0, NULL
    // 0x14c6b68: b.ne            #0x14c6b74
    // 0x14c6b6c: r6 = ""
    //     0x14c6b6c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c6b70: b               #0x14c6b78
    // 0x14c6b74: mov             x6, x0
    // 0x14c6b78: ldur            x1, [fp, #-8]
    // 0x14c6b7c: ldur            x4, [fp, #-0x18]
    // 0x14c6b80: ldur            x3, [fp, #-0x20]
    // 0x14c6b84: ldur            x2, [fp, #-0x30]
    // 0x14c6b88: ldur            x0, [fp, #-0x28]
    // 0x14c6b8c: ldur            x5, [fp, #-0x10]
    // 0x14c6b90: stur            x6, [fp, #-0x38]
    // 0x14c6b94: LoadField: r7 = r1->field_13
    //     0x14c6b94: ldur            w7, [x1, #0x13]
    // 0x14c6b98: DecompressPointer r7
    //     0x14c6b98: add             x7, x7, HEAP, lsl #32
    // 0x14c6b9c: mov             x1, x7
    // 0x14c6ba0: r0 = of()
    //     0x14c6ba0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c6ba4: LoadField: r1 = r0->field_87
    //     0x14c6ba4: ldur            w1, [x0, #0x87]
    // 0x14c6ba8: DecompressPointer r1
    //     0x14c6ba8: add             x1, x1, HEAP, lsl #32
    // 0x14c6bac: LoadField: r0 = r1->field_2b
    //     0x14c6bac: ldur            w0, [x1, #0x2b]
    // 0x14c6bb0: DecompressPointer r0
    //     0x14c6bb0: add             x0, x0, HEAP, lsl #32
    // 0x14c6bb4: stur            x0, [fp, #-8]
    // 0x14c6bb8: r1 = Instance_Color
    //     0x14c6bb8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c6bbc: d0 = 0.700000
    //     0x14c6bbc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c6bc0: ldr             d0, [x17, #0xf48]
    // 0x14c6bc4: r0 = withOpacity()
    //     0x14c6bc4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c6bc8: r16 = 12.000000
    //     0x14c6bc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c6bcc: ldr             x16, [x16, #0x9e8]
    // 0x14c6bd0: stp             x0, x16, [SP]
    // 0x14c6bd4: ldur            x1, [fp, #-8]
    // 0x14c6bd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c6bd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c6bdc: ldr             x4, [x4, #0xaa0]
    // 0x14c6be0: r0 = copyWith()
    //     0x14c6be0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c6be4: stur            x0, [fp, #-8]
    // 0x14c6be8: r0 = Text()
    //     0x14c6be8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c6bec: mov             x1, x0
    // 0x14c6bf0: ldur            x0, [fp, #-0x38]
    // 0x14c6bf4: stur            x1, [fp, #-0x40]
    // 0x14c6bf8: StoreField: r1->field_b = r0
    //     0x14c6bf8: stur            w0, [x1, #0xb]
    // 0x14c6bfc: ldur            x0, [fp, #-8]
    // 0x14c6c00: StoreField: r1->field_13 = r0
    //     0x14c6c00: stur            w0, [x1, #0x13]
    // 0x14c6c04: r0 = Padding()
    //     0x14c6c04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6c08: mov             x3, x0
    // 0x14c6c0c: r0 = Instance_EdgeInsets
    //     0x14c6c0c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14c6c10: ldr             x0, [x0, #0x868]
    // 0x14c6c14: stur            x3, [fp, #-8]
    // 0x14c6c18: StoreField: r3->field_f = r0
    //     0x14c6c18: stur            w0, [x3, #0xf]
    // 0x14c6c1c: ldur            x0, [fp, #-0x40]
    // 0x14c6c20: StoreField: r3->field_b = r0
    //     0x14c6c20: stur            w0, [x3, #0xb]
    // 0x14c6c24: r1 = Null
    //     0x14c6c24: mov             x1, NULL
    // 0x14c6c28: r2 = 8
    //     0x14c6c28: movz            x2, #0x8
    // 0x14c6c2c: r0 = AllocateArray()
    //     0x14c6c2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c6c30: mov             x2, x0
    // 0x14c6c34: ldur            x0, [fp, #-0x20]
    // 0x14c6c38: stur            x2, [fp, #-0x38]
    // 0x14c6c3c: StoreField: r2->field_f = r0
    //     0x14c6c3c: stur            w0, [x2, #0xf]
    // 0x14c6c40: ldur            x0, [fp, #-0x30]
    // 0x14c6c44: StoreField: r2->field_13 = r0
    //     0x14c6c44: stur            w0, [x2, #0x13]
    // 0x14c6c48: ldur            x0, [fp, #-0x28]
    // 0x14c6c4c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c6c4c: stur            w0, [x2, #0x17]
    // 0x14c6c50: ldur            x0, [fp, #-8]
    // 0x14c6c54: StoreField: r2->field_1b = r0
    //     0x14c6c54: stur            w0, [x2, #0x1b]
    // 0x14c6c58: r1 = <Widget>
    //     0x14c6c58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c6c5c: r0 = AllocateGrowableArray()
    //     0x14c6c5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c6c60: mov             x1, x0
    // 0x14c6c64: ldur            x0, [fp, #-0x38]
    // 0x14c6c68: stur            x1, [fp, #-8]
    // 0x14c6c6c: StoreField: r1->field_f = r0
    //     0x14c6c6c: stur            w0, [x1, #0xf]
    // 0x14c6c70: r0 = 8
    //     0x14c6c70: movz            x0, #0x8
    // 0x14c6c74: StoreField: r1->field_b = r0
    //     0x14c6c74: stur            w0, [x1, #0xb]
    // 0x14c6c78: r0 = Column()
    //     0x14c6c78: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c6c7c: mov             x1, x0
    // 0x14c6c80: r0 = Instance_Axis
    //     0x14c6c80: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c6c84: stur            x1, [fp, #-0x20]
    // 0x14c6c88: StoreField: r1->field_f = r0
    //     0x14c6c88: stur            w0, [x1, #0xf]
    // 0x14c6c8c: r2 = Instance_MainAxisAlignment
    //     0x14c6c8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c6c90: ldr             x2, [x2, #0xa08]
    // 0x14c6c94: StoreField: r1->field_13 = r2
    //     0x14c6c94: stur            w2, [x1, #0x13]
    // 0x14c6c98: r3 = Instance_MainAxisSize
    //     0x14c6c98: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14c6c9c: ldr             x3, [x3, #0xdd0]
    // 0x14c6ca0: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c6ca0: stur            w3, [x1, #0x17]
    // 0x14c6ca4: r3 = Instance_CrossAxisAlignment
    //     0x14c6ca4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c6ca8: ldr             x3, [x3, #0x890]
    // 0x14c6cac: StoreField: r1->field_1b = r3
    //     0x14c6cac: stur            w3, [x1, #0x1b]
    // 0x14c6cb0: r4 = Instance_VerticalDirection
    //     0x14c6cb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c6cb4: ldr             x4, [x4, #0xa20]
    // 0x14c6cb8: StoreField: r1->field_23 = r4
    //     0x14c6cb8: stur            w4, [x1, #0x23]
    // 0x14c6cbc: r5 = Instance_Clip
    //     0x14c6cbc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c6cc0: ldr             x5, [x5, #0x38]
    // 0x14c6cc4: StoreField: r1->field_2b = r5
    //     0x14c6cc4: stur            w5, [x1, #0x2b]
    // 0x14c6cc8: StoreField: r1->field_2f = rZR
    //     0x14c6cc8: stur            xzr, [x1, #0x2f]
    // 0x14c6ccc: ldur            x6, [fp, #-8]
    // 0x14c6cd0: StoreField: r1->field_b = r6
    //     0x14c6cd0: stur            w6, [x1, #0xb]
    // 0x14c6cd4: r0 = Visibility()
    //     0x14c6cd4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14c6cd8: mov             x2, x0
    // 0x14c6cdc: ldur            x0, [fp, #-0x20]
    // 0x14c6ce0: stur            x2, [fp, #-8]
    // 0x14c6ce4: StoreField: r2->field_b = r0
    //     0x14c6ce4: stur            w0, [x2, #0xb]
    // 0x14c6ce8: r0 = Instance_SizedBox
    //     0x14c6ce8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14c6cec: StoreField: r2->field_f = r0
    //     0x14c6cec: stur            w0, [x2, #0xf]
    // 0x14c6cf0: ldur            x0, [fp, #-0x18]
    // 0x14c6cf4: StoreField: r2->field_13 = r0
    //     0x14c6cf4: stur            w0, [x2, #0x13]
    // 0x14c6cf8: r0 = false
    //     0x14c6cf8: add             x0, NULL, #0x30  ; false
    // 0x14c6cfc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c6cfc: stur            w0, [x2, #0x17]
    // 0x14c6d00: StoreField: r2->field_1b = r0
    //     0x14c6d00: stur            w0, [x2, #0x1b]
    // 0x14c6d04: StoreField: r2->field_1f = r0
    //     0x14c6d04: stur            w0, [x2, #0x1f]
    // 0x14c6d08: StoreField: r2->field_23 = r0
    //     0x14c6d08: stur            w0, [x2, #0x23]
    // 0x14c6d0c: StoreField: r2->field_27 = r0
    //     0x14c6d0c: stur            w0, [x2, #0x27]
    // 0x14c6d10: StoreField: r2->field_2b = r0
    //     0x14c6d10: stur            w0, [x2, #0x2b]
    // 0x14c6d14: ldur            x0, [fp, #-0x10]
    // 0x14c6d18: LoadField: r1 = r0->field_b
    //     0x14c6d18: ldur            w1, [x0, #0xb]
    // 0x14c6d1c: LoadField: r3 = r0->field_f
    //     0x14c6d1c: ldur            w3, [x0, #0xf]
    // 0x14c6d20: DecompressPointer r3
    //     0x14c6d20: add             x3, x3, HEAP, lsl #32
    // 0x14c6d24: LoadField: r4 = r3->field_b
    //     0x14c6d24: ldur            w4, [x3, #0xb]
    // 0x14c6d28: r3 = LoadInt32Instr(r1)
    //     0x14c6d28: sbfx            x3, x1, #1, #0x1f
    // 0x14c6d2c: stur            x3, [fp, #-0x48]
    // 0x14c6d30: r1 = LoadInt32Instr(r4)
    //     0x14c6d30: sbfx            x1, x4, #1, #0x1f
    // 0x14c6d34: cmp             x3, x1
    // 0x14c6d38: b.ne            #0x14c6d44
    // 0x14c6d3c: mov             x1, x0
    // 0x14c6d40: r0 = _growToNextCapacity()
    //     0x14c6d40: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14c6d44: ldur            x2, [fp, #-0x10]
    // 0x14c6d48: ldur            x3, [fp, #-0x48]
    // 0x14c6d4c: add             x0, x3, #1
    // 0x14c6d50: lsl             x1, x0, #1
    // 0x14c6d54: StoreField: r2->field_b = r1
    //     0x14c6d54: stur            w1, [x2, #0xb]
    // 0x14c6d58: LoadField: r1 = r2->field_f
    //     0x14c6d58: ldur            w1, [x2, #0xf]
    // 0x14c6d5c: DecompressPointer r1
    //     0x14c6d5c: add             x1, x1, HEAP, lsl #32
    // 0x14c6d60: ldur            x0, [fp, #-8]
    // 0x14c6d64: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14c6d64: add             x25, x1, x3, lsl #2
    //     0x14c6d68: add             x25, x25, #0xf
    //     0x14c6d6c: str             w0, [x25]
    //     0x14c6d70: tbz             w0, #0, #0x14c6d8c
    //     0x14c6d74: ldurb           w16, [x1, #-1]
    //     0x14c6d78: ldurb           w17, [x0, #-1]
    //     0x14c6d7c: and             x16, x17, x16, lsr #2
    //     0x14c6d80: tst             x16, HEAP, lsr #32
    //     0x14c6d84: b.eq            #0x14c6d8c
    //     0x14c6d88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c6d8c: r0 = Column()
    //     0x14c6d8c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c6d90: mov             x1, x0
    // 0x14c6d94: r0 = Instance_Axis
    //     0x14c6d94: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c6d98: stur            x1, [fp, #-8]
    // 0x14c6d9c: StoreField: r1->field_f = r0
    //     0x14c6d9c: stur            w0, [x1, #0xf]
    // 0x14c6da0: r0 = Instance_MainAxisAlignment
    //     0x14c6da0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c6da4: ldr             x0, [x0, #0xa08]
    // 0x14c6da8: StoreField: r1->field_13 = r0
    //     0x14c6da8: stur            w0, [x1, #0x13]
    // 0x14c6dac: r0 = Instance_MainAxisSize
    //     0x14c6dac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c6db0: ldr             x0, [x0, #0xa10]
    // 0x14c6db4: ArrayStore: r1[0] = r0  ; List_4
    //     0x14c6db4: stur            w0, [x1, #0x17]
    // 0x14c6db8: r0 = Instance_CrossAxisAlignment
    //     0x14c6db8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c6dbc: ldr             x0, [x0, #0x890]
    // 0x14c6dc0: StoreField: r1->field_1b = r0
    //     0x14c6dc0: stur            w0, [x1, #0x1b]
    // 0x14c6dc4: r0 = Instance_VerticalDirection
    //     0x14c6dc4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c6dc8: ldr             x0, [x0, #0xa20]
    // 0x14c6dcc: StoreField: r1->field_23 = r0
    //     0x14c6dcc: stur            w0, [x1, #0x23]
    // 0x14c6dd0: r0 = Instance_Clip
    //     0x14c6dd0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c6dd4: ldr             x0, [x0, #0x38]
    // 0x14c6dd8: StoreField: r1->field_2b = r0
    //     0x14c6dd8: stur            w0, [x1, #0x2b]
    // 0x14c6ddc: StoreField: r1->field_2f = rZR
    //     0x14c6ddc: stur            xzr, [x1, #0x2f]
    // 0x14c6de0: ldur            x0, [fp, #-0x10]
    // 0x14c6de4: StoreField: r1->field_b = r0
    //     0x14c6de4: stur            w0, [x1, #0xb]
    // 0x14c6de8: r0 = Padding()
    //     0x14c6de8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c6dec: mov             x3, x0
    // 0x14c6df0: r0 = Instance_EdgeInsets
    //     0x14c6df0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c6df4: ldr             x0, [x0, #0x1f0]
    // 0x14c6df8: stur            x3, [fp, #-0x10]
    // 0x14c6dfc: StoreField: r3->field_f = r0
    //     0x14c6dfc: stur            w0, [x3, #0xf]
    // 0x14c6e00: ldur            x0, [fp, #-8]
    // 0x14c6e04: StoreField: r3->field_b = r0
    //     0x14c6e04: stur            w0, [x3, #0xb]
    // 0x14c6e08: r1 = Null
    //     0x14c6e08: mov             x1, NULL
    // 0x14c6e0c: r2 = 2
    //     0x14c6e0c: movz            x2, #0x2
    // 0x14c6e10: r0 = AllocateArray()
    //     0x14c6e10: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c6e14: mov             x2, x0
    // 0x14c6e18: ldur            x0, [fp, #-0x10]
    // 0x14c6e1c: stur            x2, [fp, #-8]
    // 0x14c6e20: StoreField: r2->field_f = r0
    //     0x14c6e20: stur            w0, [x2, #0xf]
    // 0x14c6e24: r1 = <Widget>
    //     0x14c6e24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c6e28: r0 = AllocateGrowableArray()
    //     0x14c6e28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c6e2c: mov             x1, x0
    // 0x14c6e30: ldur            x0, [fp, #-8]
    // 0x14c6e34: stur            x1, [fp, #-0x10]
    // 0x14c6e38: StoreField: r1->field_f = r0
    //     0x14c6e38: stur            w0, [x1, #0xf]
    // 0x14c6e3c: r0 = 2
    //     0x14c6e3c: movz            x0, #0x2
    // 0x14c6e40: StoreField: r1->field_b = r0
    //     0x14c6e40: stur            w0, [x1, #0xb]
    // 0x14c6e44: r0 = ListView()
    //     0x14c6e44: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14c6e48: stur            x0, [fp, #-8]
    // 0x14c6e4c: r16 = true
    //     0x14c6e4c: add             x16, NULL, #0x20  ; true
    // 0x14c6e50: r30 = true
    //     0x14c6e50: add             lr, NULL, #0x20  ; true
    // 0x14c6e54: stp             lr, x16, [SP, #8]
    // 0x14c6e58: r16 = Instance_ScrollPhysics
    //     0x14c6e58: add             x16, PP, #0x34, lsl #12  ; [pp+0x34010] Obj!ScrollPhysics@d558a1
    //     0x14c6e5c: ldr             x16, [x16, #0x10]
    // 0x14c6e60: str             x16, [SP]
    // 0x14c6e64: mov             x1, x0
    // 0x14c6e68: ldur            x2, [fp, #-0x10]
    // 0x14c6e6c: r4 = const [0, 0x5, 0x3, 0x2, physics, 0x4, primary, 0x2, shrinkWrap, 0x3, null]
    //     0x14c6e6c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34018] List(11) [0, 0x5, 0x3, 0x2, "physics", 0x4, "primary", 0x2, "shrinkWrap", 0x3, Null]
    //     0x14c6e70: ldr             x4, [x4, #0x18]
    // 0x14c6e74: r0 = ListView()
    //     0x14c6e74: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x14c6e78: ldur            x0, [fp, #-8]
    // 0x14c6e7c: b               #0x14c6e98
    // 0x14c6e80: r0 = Container()
    //     0x14c6e80: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c6e84: mov             x1, x0
    // 0x14c6e88: stur            x0, [fp, #-8]
    // 0x14c6e8c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14c6e8c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14c6e90: r0 = Container()
    //     0x14c6e90: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c6e94: ldur            x0, [fp, #-8]
    // 0x14c6e98: LeaveFrame
    //     0x14c6e98: mov             SP, fp
    //     0x14c6e9c: ldp             fp, lr, [SP], #0x10
    // 0x14c6ea0: ret
    //     0x14c6ea0: ret             
    // 0x14c6ea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c6ea4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c6ea8: b               #0x14c52f4
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14c6eac, size: 0xc4
    // 0x14c6eac: EnterFrame
    //     0x14c6eac: stp             fp, lr, [SP, #-0x10]!
    //     0x14c6eb0: mov             fp, SP
    // 0x14c6eb4: ldr             x0, [fp, #0x18]
    // 0x14c6eb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14c6eb8: ldur            w1, [x0, #0x17]
    // 0x14c6ebc: DecompressPointer r1
    //     0x14c6ebc: add             x1, x1, HEAP, lsl #32
    // 0x14c6ec0: CheckStackOverflow
    //     0x14c6ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c6ec4: cmp             SP, x16
    //     0x14c6ec8: b.ls            #0x14c6f68
    // 0x14c6ecc: ldr             x0, [fp, #0x10]
    // 0x14c6ed0: cmp             w0, NULL
    // 0x14c6ed4: b.ne            #0x14c6ee0
    // 0x14c6ed8: r2 = Null
    //     0x14c6ed8: mov             x2, NULL
    // 0x14c6edc: b               #0x14c6ee4
    // 0x14c6ee0: LoadField: r2 = r0->field_7
    //     0x14c6ee0: ldur            w2, [x0, #7]
    // 0x14c6ee4: cmp             w2, NULL
    // 0x14c6ee8: b.ne            #0x14c6ef4
    // 0x14c6eec: r2 = 0
    //     0x14c6eec: movz            x2, #0
    // 0x14c6ef0: b               #0x14c6efc
    // 0x14c6ef4: r3 = LoadInt32Instr(r2)
    //     0x14c6ef4: sbfx            x3, x2, #1, #0x1f
    // 0x14c6ef8: mov             x2, x3
    // 0x14c6efc: cmp             x2, #0xa
    // 0x14c6f00: b.gt            #0x14c6f34
    // 0x14c6f04: cmp             w0, NULL
    // 0x14c6f08: b.ne            #0x14c6f14
    // 0x14c6f0c: r0 = Null
    //     0x14c6f0c: mov             x0, NULL
    // 0x14c6f10: b               #0x14c6f28
    // 0x14c6f14: LoadField: r2 = r0->field_7
    //     0x14c6f14: ldur            w2, [x0, #7]
    // 0x14c6f18: cbz             w2, #0x14c6f24
    // 0x14c6f1c: r0 = false
    //     0x14c6f1c: add             x0, NULL, #0x30  ; false
    // 0x14c6f20: b               #0x14c6f28
    // 0x14c6f24: r0 = true
    //     0x14c6f24: add             x0, NULL, #0x20  ; true
    // 0x14c6f28: cmp             w0, NULL
    // 0x14c6f2c: b.eq            #0x14c6f34
    // 0x14c6f30: tbnz            w0, #4, #0x14c6f48
    // 0x14c6f34: LoadField: r0 = r1->field_f
    //     0x14c6f34: ldur            w0, [x1, #0xf]
    // 0x14c6f38: DecompressPointer r0
    //     0x14c6f38: add             x0, x0, HEAP, lsl #32
    // 0x14c6f3c: mov             x1, x0
    // 0x14c6f40: r0 = isButtonEnabled()
    //     0x14c6f40: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x14c6f44: b               #0x14c6f58
    // 0x14c6f48: LoadField: r0 = r1->field_f
    //     0x14c6f48: ldur            w0, [x1, #0xf]
    // 0x14c6f4c: DecompressPointer r0
    //     0x14c6f4c: add             x0, x0, HEAP, lsl #32
    // 0x14c6f50: mov             x1, x0
    // 0x14c6f54: r0 = isButtonEnabled()
    //     0x14c6f54: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x14c6f58: r0 = Null
    //     0x14c6f58: mov             x0, NULL
    // 0x14c6f5c: LeaveFrame
    //     0x14c6f5c: mov             SP, fp
    //     0x14c6f60: ldp             fp, lr, [SP], #0x10
    // 0x14c6f64: ret
    //     0x14c6f64: ret             
    // 0x14c6f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c6f68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c6f6c: b               #0x14c6ecc
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14c6f70, size: 0xcc
    // 0x14c6f70: EnterFrame
    //     0x14c6f70: stp             fp, lr, [SP, #-0x10]!
    //     0x14c6f74: mov             fp, SP
    // 0x14c6f78: AllocStack(0x18)
    //     0x14c6f78: sub             SP, SP, #0x18
    // 0x14c6f7c: SetupParameters()
    //     0x14c6f7c: ldr             x0, [fp, #0x20]
    //     0x14c6f80: ldur            w1, [x0, #0x17]
    //     0x14c6f84: add             x1, x1, HEAP, lsl #32
    //     0x14c6f88: stur            x1, [fp, #-8]
    // 0x14c6f8c: r1 = 2
    //     0x14c6f8c: movz            x1, #0x2
    // 0x14c6f90: r0 = AllocateContext()
    //     0x14c6f90: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c6f94: mov             x1, x0
    // 0x14c6f98: ldur            x0, [fp, #-8]
    // 0x14c6f9c: stur            x1, [fp, #-0x10]
    // 0x14c6fa0: StoreField: r1->field_b = r0
    //     0x14c6fa0: stur            w0, [x1, #0xb]
    // 0x14c6fa4: ldr             x0, [fp, #0x18]
    // 0x14c6fa8: StoreField: r1->field_f = r0
    //     0x14c6fa8: stur            w0, [x1, #0xf]
    // 0x14c6fac: ldr             x0, [fp, #0x10]
    // 0x14c6fb0: StoreField: r1->field_13 = r0
    //     0x14c6fb0: stur            w0, [x1, #0x13]
    // 0x14c6fb4: r0 = Obx()
    //     0x14c6fb4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14c6fb8: ldur            x2, [fp, #-0x10]
    // 0x14c6fbc: r1 = Function '<anonymous closure>':.
    //     0x14c6fbc: add             x1, PP, #0x42, lsl #12  ; [pp+0x42738] AnonymousClosure: (0x14c7160), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c6fc0: ldr             x1, [x1, #0x738]
    // 0x14c6fc4: stur            x0, [fp, #-8]
    // 0x14c6fc8: r0 = AllocateClosure()
    //     0x14c6fc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c6fcc: mov             x1, x0
    // 0x14c6fd0: ldur            x0, [fp, #-8]
    // 0x14c6fd4: StoreField: r0->field_b = r1
    //     0x14c6fd4: stur            w1, [x0, #0xb]
    // 0x14c6fd8: r0 = InkWell()
    //     0x14c6fd8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14c6fdc: mov             x3, x0
    // 0x14c6fe0: ldur            x0, [fp, #-8]
    // 0x14c6fe4: stur            x3, [fp, #-0x18]
    // 0x14c6fe8: StoreField: r3->field_b = r0
    //     0x14c6fe8: stur            w0, [x3, #0xb]
    // 0x14c6fec: ldur            x2, [fp, #-0x10]
    // 0x14c6ff0: r1 = Function '<anonymous closure>':.
    //     0x14c6ff0: add             x1, PP, #0x42, lsl #12  ; [pp+0x42740] AnonymousClosure: (0x14c703c), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14c51e8)
    //     0x14c6ff4: ldr             x1, [x1, #0x740]
    // 0x14c6ff8: r0 = AllocateClosure()
    //     0x14c6ff8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c6ffc: mov             x1, x0
    // 0x14c7000: ldur            x0, [fp, #-0x18]
    // 0x14c7004: StoreField: r0->field_f = r1
    //     0x14c7004: stur            w1, [x0, #0xf]
    // 0x14c7008: r1 = true
    //     0x14c7008: add             x1, NULL, #0x20  ; true
    // 0x14c700c: StoreField: r0->field_43 = r1
    //     0x14c700c: stur            w1, [x0, #0x43]
    // 0x14c7010: r2 = Instance_BoxShape
    //     0x14c7010: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c7014: ldr             x2, [x2, #0x80]
    // 0x14c7018: StoreField: r0->field_47 = r2
    //     0x14c7018: stur            w2, [x0, #0x47]
    // 0x14c701c: StoreField: r0->field_6f = r1
    //     0x14c701c: stur            w1, [x0, #0x6f]
    // 0x14c7020: r2 = false
    //     0x14c7020: add             x2, NULL, #0x30  ; false
    // 0x14c7024: StoreField: r0->field_73 = r2
    //     0x14c7024: stur            w2, [x0, #0x73]
    // 0x14c7028: StoreField: r0->field_83 = r1
    //     0x14c7028: stur            w1, [x0, #0x83]
    // 0x14c702c: StoreField: r0->field_7b = r2
    //     0x14c702c: stur            w2, [x0, #0x7b]
    // 0x14c7030: LeaveFrame
    //     0x14c7030: mov             SP, fp
    //     0x14c7034: ldp             fp, lr, [SP], #0x10
    // 0x14c7038: ret
    //     0x14c7038: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14c703c, size: 0x124
    // 0x14c703c: EnterFrame
    //     0x14c703c: stp             fp, lr, [SP, #-0x10]!
    //     0x14c7040: mov             fp, SP
    // 0x14c7044: AllocStack(0x18)
    //     0x14c7044: sub             SP, SP, #0x18
    // 0x14c7048: SetupParameters()
    //     0x14c7048: ldr             x0, [fp, #0x10]
    //     0x14c704c: ldur            w2, [x0, #0x17]
    //     0x14c7050: add             x2, x2, HEAP, lsl #32
    //     0x14c7054: stur            x2, [fp, #-0x10]
    // 0x14c7058: CheckStackOverflow
    //     0x14c7058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c705c: cmp             SP, x16
    //     0x14c7060: b.ls            #0x14c7154
    // 0x14c7064: LoadField: r0 = r2->field_b
    //     0x14c7064: ldur            w0, [x2, #0xb]
    // 0x14c7068: DecompressPointer r0
    //     0x14c7068: add             x0, x0, HEAP, lsl #32
    // 0x14c706c: stur            x0, [fp, #-8]
    // 0x14c7070: LoadField: r1 = r0->field_f
    //     0x14c7070: ldur            w1, [x0, #0xf]
    // 0x14c7074: DecompressPointer r1
    //     0x14c7074: add             x1, x1, HEAP, lsl #32
    // 0x14c7078: r0 = controller()
    //     0x14c7078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c707c: LoadField: r1 = r0->field_db
    //     0x14c707c: ldur            w1, [x0, #0xdb]
    // 0x14c7080: DecompressPointer r1
    //     0x14c7080: add             x1, x1, HEAP, lsl #32
    // 0x14c7084: r2 = ""
    //     0x14c7084: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c7088: r0 = text=()
    //     0x14c7088: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x14c708c: ldur            x0, [fp, #-8]
    // 0x14c7090: LoadField: r1 = r0->field_f
    //     0x14c7090: ldur            w1, [x0, #0xf]
    // 0x14c7094: DecompressPointer r1
    //     0x14c7094: add             x1, x1, HEAP, lsl #32
    // 0x14c7098: r0 = controller()
    //     0x14c7098: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c709c: LoadField: r2 = r0->field_67
    //     0x14c709c: ldur            w2, [x0, #0x67]
    // 0x14c70a0: DecompressPointer r2
    //     0x14c70a0: add             x2, x2, HEAP, lsl #32
    // 0x14c70a4: ldur            x0, [fp, #-8]
    // 0x14c70a8: stur            x2, [fp, #-0x18]
    // 0x14c70ac: LoadField: r1 = r0->field_f
    //     0x14c70ac: ldur            w1, [x0, #0xf]
    // 0x14c70b0: DecompressPointer r1
    //     0x14c70b0: add             x1, x1, HEAP, lsl #32
    // 0x14c70b4: r0 = controller()
    //     0x14c70b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c70b8: LoadField: r1 = r0->field_57
    //     0x14c70b8: ldur            w1, [x0, #0x57]
    // 0x14c70bc: DecompressPointer r1
    //     0x14c70bc: add             x1, x1, HEAP, lsl #32
    // 0x14c70c0: r0 = value()
    //     0x14c70c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c70c4: cmp             w0, NULL
    // 0x14c70c8: b.ne            #0x14c70d4
    // 0x14c70cc: r2 = Null
    //     0x14c70cc: mov             x2, NULL
    // 0x14c70d0: b               #0x14c7128
    // 0x14c70d4: ldur            x1, [fp, #-0x10]
    // 0x14c70d8: LoadField: r2 = r0->field_7
    //     0x14c70d8: ldur            w2, [x0, #7]
    // 0x14c70dc: DecompressPointer r2
    //     0x14c70dc: add             x2, x2, HEAP, lsl #32
    // 0x14c70e0: LoadField: r0 = r1->field_13
    //     0x14c70e0: ldur            w0, [x1, #0x13]
    // 0x14c70e4: DecompressPointer r0
    //     0x14c70e4: add             x0, x0, HEAP, lsl #32
    // 0x14c70e8: LoadField: r1 = r2->field_b
    //     0x14c70e8: ldur            w1, [x2, #0xb]
    // 0x14c70ec: r3 = LoadInt32Instr(r0)
    //     0x14c70ec: sbfx            x3, x0, #1, #0x1f
    //     0x14c70f0: tbz             w0, #0, #0x14c70f8
    //     0x14c70f4: ldur            x3, [x0, #7]
    // 0x14c70f8: r0 = LoadInt32Instr(r1)
    //     0x14c70f8: sbfx            x0, x1, #1, #0x1f
    // 0x14c70fc: mov             x1, x3
    // 0x14c7100: cmp             x1, x0
    // 0x14c7104: b.hs            #0x14c715c
    // 0x14c7108: LoadField: r0 = r2->field_f
    //     0x14c7108: ldur            w0, [x2, #0xf]
    // 0x14c710c: DecompressPointer r0
    //     0x14c710c: add             x0, x0, HEAP, lsl #32
    // 0x14c7110: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14c7110: add             x16, x0, x3, lsl #2
    //     0x14c7114: ldur            w1, [x16, #0xf]
    // 0x14c7118: DecompressPointer r1
    //     0x14c7118: add             x1, x1, HEAP, lsl #32
    // 0x14c711c: LoadField: r0 = r1->field_b
    //     0x14c711c: ldur            w0, [x1, #0xb]
    // 0x14c7120: DecompressPointer r0
    //     0x14c7120: add             x0, x0, HEAP, lsl #32
    // 0x14c7124: mov             x2, x0
    // 0x14c7128: ldur            x0, [fp, #-8]
    // 0x14c712c: ldur            x1, [fp, #-0x18]
    // 0x14c7130: r0 = value=()
    //     0x14c7130: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14c7134: ldur            x0, [fp, #-8]
    // 0x14c7138: LoadField: r1 = r0->field_f
    //     0x14c7138: ldur            w1, [x0, #0xf]
    // 0x14c713c: DecompressPointer r1
    //     0x14c713c: add             x1, x1, HEAP, lsl #32
    // 0x14c7140: r0 = isButtonEnabled()
    //     0x14c7140: bl              #0xb50f90  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::isButtonEnabled
    // 0x14c7144: r0 = Null
    //     0x14c7144: mov             x0, NULL
    // 0x14c7148: LeaveFrame
    //     0x14c7148: mov             SP, fp
    //     0x14c714c: ldp             fp, lr, [SP], #0x10
    // 0x14c7150: ret
    //     0x14c7150: ret             
    // 0x14c7154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c7154: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c7158: b               #0x14c7064
    // 0x14c715c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14c715c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x14c7160, size: 0x368
    // 0x14c7160: EnterFrame
    //     0x14c7160: stp             fp, lr, [SP, #-0x10]!
    //     0x14c7164: mov             fp, SP
    // 0x14c7168: AllocStack(0x38)
    //     0x14c7168: sub             SP, SP, #0x38
    // 0x14c716c: SetupParameters()
    //     0x14c716c: ldr             x0, [fp, #0x10]
    //     0x14c7170: ldur            w2, [x0, #0x17]
    //     0x14c7174: add             x2, x2, HEAP, lsl #32
    //     0x14c7178: stur            x2, [fp, #-0x10]
    // 0x14c717c: CheckStackOverflow
    //     0x14c717c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c7180: cmp             SP, x16
    //     0x14c7184: b.ls            #0x14c74b8
    // 0x14c7188: LoadField: r0 = r2->field_b
    //     0x14c7188: ldur            w0, [x2, #0xb]
    // 0x14c718c: DecompressPointer r0
    //     0x14c718c: add             x0, x0, HEAP, lsl #32
    // 0x14c7190: stur            x0, [fp, #-8]
    // 0x14c7194: LoadField: r1 = r0->field_f
    //     0x14c7194: ldur            w1, [x0, #0xf]
    // 0x14c7198: DecompressPointer r1
    //     0x14c7198: add             x1, x1, HEAP, lsl #32
    // 0x14c719c: r0 = controller()
    //     0x14c719c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c71a0: LoadField: r1 = r0->field_67
    //     0x14c71a0: ldur            w1, [x0, #0x67]
    // 0x14c71a4: DecompressPointer r1
    //     0x14c71a4: add             x1, x1, HEAP, lsl #32
    // 0x14c71a8: r0 = value()
    //     0x14c71a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c71ac: mov             x2, x0
    // 0x14c71b0: ldur            x0, [fp, #-8]
    // 0x14c71b4: stur            x2, [fp, #-0x18]
    // 0x14c71b8: LoadField: r1 = r0->field_f
    //     0x14c71b8: ldur            w1, [x0, #0xf]
    // 0x14c71bc: DecompressPointer r1
    //     0x14c71bc: add             x1, x1, HEAP, lsl #32
    // 0x14c71c0: r0 = controller()
    //     0x14c71c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c71c4: LoadField: r1 = r0->field_57
    //     0x14c71c4: ldur            w1, [x0, #0x57]
    // 0x14c71c8: DecompressPointer r1
    //     0x14c71c8: add             x1, x1, HEAP, lsl #32
    // 0x14c71cc: r0 = value()
    //     0x14c71cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c71d0: cmp             w0, NULL
    // 0x14c71d4: b.ne            #0x14c71e4
    // 0x14c71d8: ldur            x2, [fp, #-0x10]
    // 0x14c71dc: r1 = Null
    //     0x14c71dc: mov             x1, NULL
    // 0x14c71e0: b               #0x14c7238
    // 0x14c71e4: ldur            x2, [fp, #-0x10]
    // 0x14c71e8: LoadField: r3 = r0->field_7
    //     0x14c71e8: ldur            w3, [x0, #7]
    // 0x14c71ec: DecompressPointer r3
    //     0x14c71ec: add             x3, x3, HEAP, lsl #32
    // 0x14c71f0: LoadField: r0 = r2->field_13
    //     0x14c71f0: ldur            w0, [x2, #0x13]
    // 0x14c71f4: DecompressPointer r0
    //     0x14c71f4: add             x0, x0, HEAP, lsl #32
    // 0x14c71f8: LoadField: r1 = r3->field_b
    //     0x14c71f8: ldur            w1, [x3, #0xb]
    // 0x14c71fc: r4 = LoadInt32Instr(r0)
    //     0x14c71fc: sbfx            x4, x0, #1, #0x1f
    //     0x14c7200: tbz             w0, #0, #0x14c7208
    //     0x14c7204: ldur            x4, [x0, #7]
    // 0x14c7208: r0 = LoadInt32Instr(r1)
    //     0x14c7208: sbfx            x0, x1, #1, #0x1f
    // 0x14c720c: mov             x1, x4
    // 0x14c7210: cmp             x1, x0
    // 0x14c7214: b.hs            #0x14c74c0
    // 0x14c7218: LoadField: r0 = r3->field_f
    //     0x14c7218: ldur            w0, [x3, #0xf]
    // 0x14c721c: DecompressPointer r0
    //     0x14c721c: add             x0, x0, HEAP, lsl #32
    // 0x14c7220: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14c7220: add             x16, x0, x4, lsl #2
    //     0x14c7224: ldur            w1, [x16, #0xf]
    // 0x14c7228: DecompressPointer r1
    //     0x14c7228: add             x1, x1, HEAP, lsl #32
    // 0x14c722c: LoadField: r0 = r1->field_b
    //     0x14c722c: ldur            w0, [x1, #0xb]
    // 0x14c7230: DecompressPointer r0
    //     0x14c7230: add             x0, x0, HEAP, lsl #32
    // 0x14c7234: mov             x1, x0
    // 0x14c7238: ldur            x0, [fp, #-0x18]
    // 0x14c723c: r3 = LoadClassIdInstr(r0)
    //     0x14c723c: ldur            x3, [x0, #-1]
    //     0x14c7240: ubfx            x3, x3, #0xc, #0x14
    // 0x14c7244: stp             x1, x0, [SP]
    // 0x14c7248: mov             x0, x3
    // 0x14c724c: mov             lr, x0
    // 0x14c7250: ldr             lr, [x21, lr, lsl #3]
    // 0x14c7254: blr             lr
    // 0x14c7258: tbnz            w0, #4, #0x14c7268
    // 0x14c725c: r3 = Instance_IconData
    //     0x14c725c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x14c7260: ldr             x3, [x3, #0x30]
    // 0x14c7264: b               #0x14c7270
    // 0x14c7268: r3 = Instance_IconData
    //     0x14c7268: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x14c726c: ldr             x3, [x3, #0x38]
    // 0x14c7270: ldur            x0, [fp, #-0x10]
    // 0x14c7274: ldur            x2, [fp, #-8]
    // 0x14c7278: stur            x3, [fp, #-0x18]
    // 0x14c727c: LoadField: r1 = r0->field_f
    //     0x14c727c: ldur            w1, [x0, #0xf]
    // 0x14c7280: DecompressPointer r1
    //     0x14c7280: add             x1, x1, HEAP, lsl #32
    // 0x14c7284: r0 = of()
    //     0x14c7284: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c7288: LoadField: r1 = r0->field_5b
    //     0x14c7288: ldur            w1, [x0, #0x5b]
    // 0x14c728c: DecompressPointer r1
    //     0x14c728c: add             x1, x1, HEAP, lsl #32
    // 0x14c7290: stur            x1, [fp, #-0x20]
    // 0x14c7294: r0 = Icon()
    //     0x14c7294: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14c7298: mov             x2, x0
    // 0x14c729c: ldur            x0, [fp, #-0x18]
    // 0x14c72a0: stur            x2, [fp, #-0x28]
    // 0x14c72a4: StoreField: r2->field_b = r0
    //     0x14c72a4: stur            w0, [x2, #0xb]
    // 0x14c72a8: ldur            x0, [fp, #-0x20]
    // 0x14c72ac: StoreField: r2->field_23 = r0
    //     0x14c72ac: stur            w0, [x2, #0x23]
    // 0x14c72b0: ldur            x0, [fp, #-8]
    // 0x14c72b4: LoadField: r1 = r0->field_f
    //     0x14c72b4: ldur            w1, [x0, #0xf]
    // 0x14c72b8: DecompressPointer r1
    //     0x14c72b8: add             x1, x1, HEAP, lsl #32
    // 0x14c72bc: r0 = controller()
    //     0x14c72bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c72c0: LoadField: r1 = r0->field_57
    //     0x14c72c0: ldur            w1, [x0, #0x57]
    // 0x14c72c4: DecompressPointer r1
    //     0x14c72c4: add             x1, x1, HEAP, lsl #32
    // 0x14c72c8: r0 = value()
    //     0x14c72c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c72cc: cmp             w0, NULL
    // 0x14c72d0: b.ne            #0x14c72e0
    // 0x14c72d4: ldur            x2, [fp, #-0x10]
    // 0x14c72d8: r0 = Null
    //     0x14c72d8: mov             x0, NULL
    // 0x14c72dc: b               #0x14c7330
    // 0x14c72e0: ldur            x2, [fp, #-0x10]
    // 0x14c72e4: LoadField: r3 = r0->field_7
    //     0x14c72e4: ldur            w3, [x0, #7]
    // 0x14c72e8: DecompressPointer r3
    //     0x14c72e8: add             x3, x3, HEAP, lsl #32
    // 0x14c72ec: LoadField: r0 = r2->field_13
    //     0x14c72ec: ldur            w0, [x2, #0x13]
    // 0x14c72f0: DecompressPointer r0
    //     0x14c72f0: add             x0, x0, HEAP, lsl #32
    // 0x14c72f4: LoadField: r1 = r3->field_b
    //     0x14c72f4: ldur            w1, [x3, #0xb]
    // 0x14c72f8: r4 = LoadInt32Instr(r0)
    //     0x14c72f8: sbfx            x4, x0, #1, #0x1f
    //     0x14c72fc: tbz             w0, #0, #0x14c7304
    //     0x14c7300: ldur            x4, [x0, #7]
    // 0x14c7304: r0 = LoadInt32Instr(r1)
    //     0x14c7304: sbfx            x0, x1, #1, #0x1f
    // 0x14c7308: mov             x1, x4
    // 0x14c730c: cmp             x1, x0
    // 0x14c7310: b.hs            #0x14c74c4
    // 0x14c7314: LoadField: r0 = r3->field_f
    //     0x14c7314: ldur            w0, [x3, #0xf]
    // 0x14c7318: DecompressPointer r0
    //     0x14c7318: add             x0, x0, HEAP, lsl #32
    // 0x14c731c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14c731c: add             x16, x0, x4, lsl #2
    //     0x14c7320: ldur            w1, [x16, #0xf]
    // 0x14c7324: DecompressPointer r1
    //     0x14c7324: add             x1, x1, HEAP, lsl #32
    // 0x14c7328: LoadField: r0 = r1->field_7
    //     0x14c7328: ldur            w0, [x1, #7]
    // 0x14c732c: DecompressPointer r0
    //     0x14c732c: add             x0, x0, HEAP, lsl #32
    // 0x14c7330: cmp             w0, NULL
    // 0x14c7334: b.ne            #0x14c7340
    // 0x14c7338: r3 = ""
    //     0x14c7338: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c733c: b               #0x14c7344
    // 0x14c7340: mov             x3, x0
    // 0x14c7344: ldur            x0, [fp, #-0x28]
    // 0x14c7348: stur            x3, [fp, #-8]
    // 0x14c734c: LoadField: r1 = r2->field_f
    //     0x14c734c: ldur            w1, [x2, #0xf]
    // 0x14c7350: DecompressPointer r1
    //     0x14c7350: add             x1, x1, HEAP, lsl #32
    // 0x14c7354: r0 = of()
    //     0x14c7354: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c7358: LoadField: r1 = r0->field_87
    //     0x14c7358: ldur            w1, [x0, #0x87]
    // 0x14c735c: DecompressPointer r1
    //     0x14c735c: add             x1, x1, HEAP, lsl #32
    // 0x14c7360: LoadField: r0 = r1->field_2b
    //     0x14c7360: ldur            w0, [x1, #0x2b]
    // 0x14c7364: DecompressPointer r0
    //     0x14c7364: add             x0, x0, HEAP, lsl #32
    // 0x14c7368: stur            x0, [fp, #-0x10]
    // 0x14c736c: r1 = Instance_Color
    //     0x14c736c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c7370: d0 = 0.700000
    //     0x14c7370: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c7374: ldr             d0, [x17, #0xf48]
    // 0x14c7378: r0 = withOpacity()
    //     0x14c7378: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c737c: r16 = 12.000000
    //     0x14c737c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c7380: ldr             x16, [x16, #0x9e8]
    // 0x14c7384: stp             x0, x16, [SP]
    // 0x14c7388: ldur            x1, [fp, #-0x10]
    // 0x14c738c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c738c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c7390: ldr             x4, [x4, #0xaa0]
    // 0x14c7394: r0 = copyWith()
    //     0x14c7394: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c7398: stur            x0, [fp, #-0x10]
    // 0x14c739c: r0 = Text()
    //     0x14c739c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c73a0: mov             x2, x0
    // 0x14c73a4: ldur            x0, [fp, #-8]
    // 0x14c73a8: stur            x2, [fp, #-0x18]
    // 0x14c73ac: StoreField: r2->field_b = r0
    //     0x14c73ac: stur            w0, [x2, #0xb]
    // 0x14c73b0: ldur            x0, [fp, #-0x10]
    // 0x14c73b4: StoreField: r2->field_13 = r0
    //     0x14c73b4: stur            w0, [x2, #0x13]
    // 0x14c73b8: r1 = <FlexParentData>
    //     0x14c73b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14c73bc: ldr             x1, [x1, #0xe00]
    // 0x14c73c0: r0 = Expanded()
    //     0x14c73c0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14c73c4: mov             x3, x0
    // 0x14c73c8: r0 = 1
    //     0x14c73c8: movz            x0, #0x1
    // 0x14c73cc: stur            x3, [fp, #-8]
    // 0x14c73d0: StoreField: r3->field_13 = r0
    //     0x14c73d0: stur            x0, [x3, #0x13]
    // 0x14c73d4: r0 = Instance_FlexFit
    //     0x14c73d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14c73d8: ldr             x0, [x0, #0xe08]
    // 0x14c73dc: StoreField: r3->field_1b = r0
    //     0x14c73dc: stur            w0, [x3, #0x1b]
    // 0x14c73e0: ldur            x0, [fp, #-0x18]
    // 0x14c73e4: StoreField: r3->field_b = r0
    //     0x14c73e4: stur            w0, [x3, #0xb]
    // 0x14c73e8: r1 = Null
    //     0x14c73e8: mov             x1, NULL
    // 0x14c73ec: r2 = 6
    //     0x14c73ec: movz            x2, #0x6
    // 0x14c73f0: r0 = AllocateArray()
    //     0x14c73f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c73f4: mov             x2, x0
    // 0x14c73f8: ldur            x0, [fp, #-0x28]
    // 0x14c73fc: stur            x2, [fp, #-0x10]
    // 0x14c7400: StoreField: r2->field_f = r0
    //     0x14c7400: stur            w0, [x2, #0xf]
    // 0x14c7404: r16 = Instance_SizedBox
    //     0x14c7404: add             x16, PP, #0x34, lsl #12  ; [pp+0x34040] Obj!SizedBox@d680c1
    //     0x14c7408: ldr             x16, [x16, #0x40]
    // 0x14c740c: StoreField: r2->field_13 = r16
    //     0x14c740c: stur            w16, [x2, #0x13]
    // 0x14c7410: ldur            x0, [fp, #-8]
    // 0x14c7414: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c7414: stur            w0, [x2, #0x17]
    // 0x14c7418: r1 = <Widget>
    //     0x14c7418: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c741c: r0 = AllocateGrowableArray()
    //     0x14c741c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c7420: mov             x1, x0
    // 0x14c7424: ldur            x0, [fp, #-0x10]
    // 0x14c7428: stur            x1, [fp, #-8]
    // 0x14c742c: StoreField: r1->field_f = r0
    //     0x14c742c: stur            w0, [x1, #0xf]
    // 0x14c7430: r0 = 6
    //     0x14c7430: movz            x0, #0x6
    // 0x14c7434: StoreField: r1->field_b = r0
    //     0x14c7434: stur            w0, [x1, #0xb]
    // 0x14c7438: r0 = Row()
    //     0x14c7438: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14c743c: mov             x1, x0
    // 0x14c7440: r0 = Instance_Axis
    //     0x14c7440: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c7444: stur            x1, [fp, #-0x10]
    // 0x14c7448: StoreField: r1->field_f = r0
    //     0x14c7448: stur            w0, [x1, #0xf]
    // 0x14c744c: r0 = Instance_MainAxisAlignment
    //     0x14c744c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c7450: ldr             x0, [x0, #0xa08]
    // 0x14c7454: StoreField: r1->field_13 = r0
    //     0x14c7454: stur            w0, [x1, #0x13]
    // 0x14c7458: r0 = Instance_MainAxisSize
    //     0x14c7458: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14c745c: ldr             x0, [x0, #0xdd0]
    // 0x14c7460: ArrayStore: r1[0] = r0  ; List_4
    //     0x14c7460: stur            w0, [x1, #0x17]
    // 0x14c7464: r0 = Instance_CrossAxisAlignment
    //     0x14c7464: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c7468: ldr             x0, [x0, #0xa18]
    // 0x14c746c: StoreField: r1->field_1b = r0
    //     0x14c746c: stur            w0, [x1, #0x1b]
    // 0x14c7470: r0 = Instance_VerticalDirection
    //     0x14c7470: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c7474: ldr             x0, [x0, #0xa20]
    // 0x14c7478: StoreField: r1->field_23 = r0
    //     0x14c7478: stur            w0, [x1, #0x23]
    // 0x14c747c: r0 = Instance_Clip
    //     0x14c747c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c7480: ldr             x0, [x0, #0x38]
    // 0x14c7484: StoreField: r1->field_2b = r0
    //     0x14c7484: stur            w0, [x1, #0x2b]
    // 0x14c7488: StoreField: r1->field_2f = rZR
    //     0x14c7488: stur            xzr, [x1, #0x2f]
    // 0x14c748c: ldur            x0, [fp, #-8]
    // 0x14c7490: StoreField: r1->field_b = r0
    //     0x14c7490: stur            w0, [x1, #0xb]
    // 0x14c7494: r0 = Padding()
    //     0x14c7494: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c7498: r1 = Instance_EdgeInsets
    //     0x14c7498: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14c749c: ldr             x1, [x1, #0x980]
    // 0x14c74a0: StoreField: r0->field_f = r1
    //     0x14c74a0: stur            w1, [x0, #0xf]
    // 0x14c74a4: ldur            x1, [fp, #-0x10]
    // 0x14c74a8: StoreField: r0->field_b = r1
    //     0x14c74a8: stur            w1, [x0, #0xb]
    // 0x14c74ac: LeaveFrame
    //     0x14c74ac: mov             SP, fp
    //     0x14c74b0: ldp             fp, lr, [SP], #0x10
    // 0x14c74b4: ret
    //     0x14c74b4: ret             
    // 0x14c74b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c74b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c74bc: b               #0x14c7188
    // 0x14c74c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14c74c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14c74c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14c74c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Divider <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14c74c8, size: 0x50
    // 0x14c74c8: EnterFrame
    //     0x14c74c8: stp             fp, lr, [SP, #-0x10]!
    //     0x14c74cc: mov             fp, SP
    // 0x14c74d0: AllocStack(0x8)
    //     0x14c74d0: sub             SP, SP, #8
    // 0x14c74d4: CheckStackOverflow
    //     0x14c74d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c74d8: cmp             SP, x16
    //     0x14c74dc: b.ls            #0x14c7510
    // 0x14c74e0: r1 = Instance_Color
    //     0x14c74e0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0x14c74e4: ldr             x1, [x1, #0x7b8]
    // 0x14c74e8: d0 = 0.300000
    //     0x14c74e8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14c74ec: ldr             d0, [x17, #0x658]
    // 0x14c74f0: r0 = withOpacity()
    //     0x14c74f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c74f4: stur            x0, [fp, #-8]
    // 0x14c74f8: r0 = Divider()
    //     0x14c74f8: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x14c74fc: ldur            x1, [fp, #-8]
    // 0x14c7500: StoreField: r0->field_1f = r1
    //     0x14c7500: stur            w1, [x0, #0x1f]
    // 0x14c7504: LeaveFrame
    //     0x14c7504: mov             SP, fp
    //     0x14c7508: ldp             fp, lr, [SP], #0x10
    // 0x14c750c: ret
    //     0x14c750c: ret             
    // 0x14c7510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c7510: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c7514: b               #0x14c74e0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dd64c, size: 0x18c
    // 0x15dd64c: EnterFrame
    //     0x15dd64c: stp             fp, lr, [SP, #-0x10]!
    //     0x15dd650: mov             fp, SP
    // 0x15dd654: AllocStack(0x28)
    //     0x15dd654: sub             SP, SP, #0x28
    // 0x15dd658: SetupParameters(ReturnOrderView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15dd658: mov             x0, x1
    //     0x15dd65c: stur            x1, [fp, #-8]
    //     0x15dd660: mov             x1, x2
    //     0x15dd664: stur            x2, [fp, #-0x10]
    // 0x15dd668: CheckStackOverflow
    //     0x15dd668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dd66c: cmp             SP, x16
    //     0x15dd670: b.ls            #0x15dd7d0
    // 0x15dd674: r1 = 2
    //     0x15dd674: movz            x1, #0x2
    // 0x15dd678: r0 = AllocateContext()
    //     0x15dd678: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dd67c: mov             x1, x0
    // 0x15dd680: ldur            x0, [fp, #-8]
    // 0x15dd684: stur            x1, [fp, #-0x18]
    // 0x15dd688: StoreField: r1->field_f = r0
    //     0x15dd688: stur            w0, [x1, #0xf]
    // 0x15dd68c: ldur            x0, [fp, #-0x10]
    // 0x15dd690: StoreField: r1->field_13 = r0
    //     0x15dd690: stur            w0, [x1, #0x13]
    // 0x15dd694: r0 = Obx()
    //     0x15dd694: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dd698: ldur            x2, [fp, #-0x18]
    // 0x15dd69c: r1 = Function '<anonymous closure>':.
    //     0x15dd69c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42748] AnonymousClosure: (0x15d5454), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dd6a0: ldr             x1, [x1, #0x748]
    // 0x15dd6a4: stur            x0, [fp, #-8]
    // 0x15dd6a8: r0 = AllocateClosure()
    //     0x15dd6a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dd6ac: mov             x1, x0
    // 0x15dd6b0: ldur            x0, [fp, #-8]
    // 0x15dd6b4: StoreField: r0->field_b = r1
    //     0x15dd6b4: stur            w1, [x0, #0xb]
    // 0x15dd6b8: ldur            x1, [fp, #-0x10]
    // 0x15dd6bc: r0 = of()
    //     0x15dd6bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dd6c0: LoadField: r1 = r0->field_5b
    //     0x15dd6c0: ldur            w1, [x0, #0x5b]
    // 0x15dd6c4: DecompressPointer r1
    //     0x15dd6c4: add             x1, x1, HEAP, lsl #32
    // 0x15dd6c8: stur            x1, [fp, #-0x10]
    // 0x15dd6cc: r0 = ColorFilter()
    //     0x15dd6cc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dd6d0: mov             x1, x0
    // 0x15dd6d4: ldur            x0, [fp, #-0x10]
    // 0x15dd6d8: stur            x1, [fp, #-0x20]
    // 0x15dd6dc: StoreField: r1->field_7 = r0
    //     0x15dd6dc: stur            w0, [x1, #7]
    // 0x15dd6e0: r0 = Instance_BlendMode
    //     0x15dd6e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dd6e4: ldr             x0, [x0, #0xb30]
    // 0x15dd6e8: StoreField: r1->field_b = r0
    //     0x15dd6e8: stur            w0, [x1, #0xb]
    // 0x15dd6ec: r0 = 1
    //     0x15dd6ec: movz            x0, #0x1
    // 0x15dd6f0: StoreField: r1->field_13 = r0
    //     0x15dd6f0: stur            x0, [x1, #0x13]
    // 0x15dd6f4: r0 = SvgPicture()
    //     0x15dd6f4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dd6f8: stur            x0, [fp, #-0x10]
    // 0x15dd6fc: ldur            x16, [fp, #-0x20]
    // 0x15dd700: str             x16, [SP]
    // 0x15dd704: mov             x1, x0
    // 0x15dd708: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dd708: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dd70c: ldr             x2, [x2, #0xa40]
    // 0x15dd710: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dd710: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dd714: ldr             x4, [x4, #0xa38]
    // 0x15dd718: r0 = SvgPicture.asset()
    //     0x15dd718: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dd71c: r0 = Align()
    //     0x15dd71c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dd720: mov             x1, x0
    // 0x15dd724: r0 = Instance_Alignment
    //     0x15dd724: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dd728: ldr             x0, [x0, #0xb10]
    // 0x15dd72c: stur            x1, [fp, #-0x20]
    // 0x15dd730: StoreField: r1->field_f = r0
    //     0x15dd730: stur            w0, [x1, #0xf]
    // 0x15dd734: r0 = 1.000000
    //     0x15dd734: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dd738: StoreField: r1->field_13 = r0
    //     0x15dd738: stur            w0, [x1, #0x13]
    // 0x15dd73c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dd73c: stur            w0, [x1, #0x17]
    // 0x15dd740: ldur            x0, [fp, #-0x10]
    // 0x15dd744: StoreField: r1->field_b = r0
    //     0x15dd744: stur            w0, [x1, #0xb]
    // 0x15dd748: r0 = InkWell()
    //     0x15dd748: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dd74c: mov             x3, x0
    // 0x15dd750: ldur            x0, [fp, #-0x20]
    // 0x15dd754: stur            x3, [fp, #-0x10]
    // 0x15dd758: StoreField: r3->field_b = r0
    //     0x15dd758: stur            w0, [x3, #0xb]
    // 0x15dd75c: ldur            x2, [fp, #-0x18]
    // 0x15dd760: r1 = Function '<anonymous closure>':.
    //     0x15dd760: add             x1, PP, #0x42, lsl #12  ; [pp+0x42750] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dd764: ldr             x1, [x1, #0x750]
    // 0x15dd768: r0 = AllocateClosure()
    //     0x15dd768: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dd76c: ldur            x2, [fp, #-0x10]
    // 0x15dd770: StoreField: r2->field_f = r0
    //     0x15dd770: stur            w0, [x2, #0xf]
    // 0x15dd774: r0 = true
    //     0x15dd774: add             x0, NULL, #0x20  ; true
    // 0x15dd778: StoreField: r2->field_43 = r0
    //     0x15dd778: stur            w0, [x2, #0x43]
    // 0x15dd77c: r1 = Instance_BoxShape
    //     0x15dd77c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dd780: ldr             x1, [x1, #0x80]
    // 0x15dd784: StoreField: r2->field_47 = r1
    //     0x15dd784: stur            w1, [x2, #0x47]
    // 0x15dd788: StoreField: r2->field_6f = r0
    //     0x15dd788: stur            w0, [x2, #0x6f]
    // 0x15dd78c: r1 = false
    //     0x15dd78c: add             x1, NULL, #0x30  ; false
    // 0x15dd790: StoreField: r2->field_73 = r1
    //     0x15dd790: stur            w1, [x2, #0x73]
    // 0x15dd794: StoreField: r2->field_83 = r0
    //     0x15dd794: stur            w0, [x2, #0x83]
    // 0x15dd798: StoreField: r2->field_7b = r1
    //     0x15dd798: stur            w1, [x2, #0x7b]
    // 0x15dd79c: r0 = AppBar()
    //     0x15dd79c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dd7a0: stur            x0, [fp, #-0x18]
    // 0x15dd7a4: ldur            x16, [fp, #-8]
    // 0x15dd7a8: str             x16, [SP]
    // 0x15dd7ac: mov             x1, x0
    // 0x15dd7b0: ldur            x2, [fp, #-0x10]
    // 0x15dd7b4: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15dd7b4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15dd7b8: ldr             x4, [x4, #0xf00]
    // 0x15dd7bc: r0 = AppBar()
    //     0x15dd7bc: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dd7c0: ldur            x0, [fp, #-0x18]
    // 0x15dd7c4: LeaveFrame
    //     0x15dd7c4: mov             SP, fp
    //     0x15dd7c8: ldp             fp, lr, [SP], #0x10
    // 0x15dd7cc: ret
    //     0x15dd7cc: ret             
    // 0x15dd7d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dd7d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dd7d4: b               #0x15dd674
  }
}
