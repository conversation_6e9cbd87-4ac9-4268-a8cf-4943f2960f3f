// lib: , url: package:customer_app/app/presentation/views/cosmetic/post_order/order_detail/cancel_return_order_bottom_sheet.dart

// class id: 1049297, size: 0x8
class :: {
}

// class id: 3411, size: 0x14, field offset: 0x14
class _CancelReturnOrderBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaff2e0, size: 0x918
    // 0xaff2e0: EnterFrame
    //     0xaff2e0: stp             fp, lr, [SP, #-0x10]!
    //     0xaff2e4: mov             fp, SP
    // 0xaff2e8: AllocStack(0x58)
    //     0xaff2e8: sub             SP, SP, #0x58
    // 0xaff2ec: SetupParameters(_CancelReturnOrderBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaff2ec: mov             x0, x1
    //     0xaff2f0: stur            x1, [fp, #-8]
    //     0xaff2f4: mov             x1, x2
    //     0xaff2f8: stur            x2, [fp, #-0x10]
    // 0xaff2fc: CheckStackOverflow
    //     0xaff2fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaff300: cmp             SP, x16
    //     0xaff304: b.ls            #0xaffbe4
    // 0xaff308: r1 = 2
    //     0xaff308: movz            x1, #0x2
    // 0xaff30c: r0 = AllocateContext()
    //     0xaff30c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaff310: mov             x2, x0
    // 0xaff314: ldur            x0, [fp, #-8]
    // 0xaff318: stur            x2, [fp, #-0x20]
    // 0xaff31c: StoreField: r2->field_f = r0
    //     0xaff31c: stur            w0, [x2, #0xf]
    // 0xaff320: ldur            x1, [fp, #-0x10]
    // 0xaff324: StoreField: r2->field_13 = r1
    //     0xaff324: stur            w1, [x2, #0x13]
    // 0xaff328: LoadField: r3 = r0->field_b
    //     0xaff328: ldur            w3, [x0, #0xb]
    // 0xaff32c: DecompressPointer r3
    //     0xaff32c: add             x3, x3, HEAP, lsl #32
    // 0xaff330: cmp             w3, NULL
    // 0xaff334: b.eq            #0xaffbec
    // 0xaff338: LoadField: r4 = r3->field_b
    //     0xaff338: ldur            w4, [x3, #0xb]
    // 0xaff33c: DecompressPointer r4
    //     0xaff33c: add             x4, x4, HEAP, lsl #32
    // 0xaff340: cmp             w4, NULL
    // 0xaff344: b.ne            #0xaff350
    // 0xaff348: r3 = Null
    //     0xaff348: mov             x3, NULL
    // 0xaff34c: b               #0xaff358
    // 0xaff350: LoadField: r3 = r4->field_7
    //     0xaff350: ldur            w3, [x4, #7]
    // 0xaff354: DecompressPointer r3
    //     0xaff354: add             x3, x3, HEAP, lsl #32
    // 0xaff358: cmp             w3, NULL
    // 0xaff35c: b.ne            #0xaff364
    // 0xaff360: r3 = ""
    //     0xaff360: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaff364: stur            x3, [fp, #-0x18]
    // 0xaff368: r0 = of()
    //     0xaff368: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff36c: LoadField: r1 = r0->field_87
    //     0xaff36c: ldur            w1, [x0, #0x87]
    // 0xaff370: DecompressPointer r1
    //     0xaff370: add             x1, x1, HEAP, lsl #32
    // 0xaff374: LoadField: r0 = r1->field_7
    //     0xaff374: ldur            w0, [x1, #7]
    // 0xaff378: DecompressPointer r0
    //     0xaff378: add             x0, x0, HEAP, lsl #32
    // 0xaff37c: r16 = Instance_Color
    //     0xaff37c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaff380: r30 = 16.000000
    //     0xaff380: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaff384: ldr             lr, [lr, #0x188]
    // 0xaff388: stp             lr, x16, [SP]
    // 0xaff38c: mov             x1, x0
    // 0xaff390: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaff390: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaff394: ldr             x4, [x4, #0x9b8]
    // 0xaff398: r0 = copyWith()
    //     0xaff398: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaff39c: stur            x0, [fp, #-0x10]
    // 0xaff3a0: r0 = Text()
    //     0xaff3a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaff3a4: mov             x1, x0
    // 0xaff3a8: ldur            x0, [fp, #-0x18]
    // 0xaff3ac: stur            x1, [fp, #-0x28]
    // 0xaff3b0: StoreField: r1->field_b = r0
    //     0xaff3b0: stur            w0, [x1, #0xb]
    // 0xaff3b4: ldur            x0, [fp, #-0x10]
    // 0xaff3b8: StoreField: r1->field_13 = r0
    //     0xaff3b8: stur            w0, [x1, #0x13]
    // 0xaff3bc: r0 = SvgPicture()
    //     0xaff3bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaff3c0: mov             x1, x0
    // 0xaff3c4: r2 = "assets/images/x.svg"
    //     0xaff3c4: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xaff3c8: ldr             x2, [x2, #0x5e8]
    // 0xaff3cc: stur            x0, [fp, #-0x10]
    // 0xaff3d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaff3d0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaff3d4: r0 = SvgPicture.asset()
    //     0xaff3d4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaff3d8: r0 = InkWell()
    //     0xaff3d8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaff3dc: mov             x3, x0
    // 0xaff3e0: ldur            x0, [fp, #-0x10]
    // 0xaff3e4: stur            x3, [fp, #-0x18]
    // 0xaff3e8: StoreField: r3->field_b = r0
    //     0xaff3e8: stur            w0, [x3, #0xb]
    // 0xaff3ec: ldur            x2, [fp, #-0x20]
    // 0xaff3f0: r1 = Function '<anonymous closure>':.
    //     0xaff3f0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f78] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xaff3f4: ldr             x1, [x1, #0xf78]
    // 0xaff3f8: r0 = AllocateClosure()
    //     0xaff3f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaff3fc: mov             x1, x0
    // 0xaff400: ldur            x0, [fp, #-0x18]
    // 0xaff404: StoreField: r0->field_f = r1
    //     0xaff404: stur            w1, [x0, #0xf]
    // 0xaff408: r3 = true
    //     0xaff408: add             x3, NULL, #0x20  ; true
    // 0xaff40c: StoreField: r0->field_43 = r3
    //     0xaff40c: stur            w3, [x0, #0x43]
    // 0xaff410: r1 = Instance_BoxShape
    //     0xaff410: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaff414: ldr             x1, [x1, #0x80]
    // 0xaff418: StoreField: r0->field_47 = r1
    //     0xaff418: stur            w1, [x0, #0x47]
    // 0xaff41c: StoreField: r0->field_6f = r3
    //     0xaff41c: stur            w3, [x0, #0x6f]
    // 0xaff420: r4 = false
    //     0xaff420: add             x4, NULL, #0x30  ; false
    // 0xaff424: StoreField: r0->field_73 = r4
    //     0xaff424: stur            w4, [x0, #0x73]
    // 0xaff428: StoreField: r0->field_83 = r3
    //     0xaff428: stur            w3, [x0, #0x83]
    // 0xaff42c: StoreField: r0->field_7b = r4
    //     0xaff42c: stur            w4, [x0, #0x7b]
    // 0xaff430: r1 = Null
    //     0xaff430: mov             x1, NULL
    // 0xaff434: r2 = 4
    //     0xaff434: movz            x2, #0x4
    // 0xaff438: r0 = AllocateArray()
    //     0xaff438: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaff43c: mov             x2, x0
    // 0xaff440: ldur            x0, [fp, #-0x28]
    // 0xaff444: stur            x2, [fp, #-0x10]
    // 0xaff448: StoreField: r2->field_f = r0
    //     0xaff448: stur            w0, [x2, #0xf]
    // 0xaff44c: ldur            x0, [fp, #-0x18]
    // 0xaff450: StoreField: r2->field_13 = r0
    //     0xaff450: stur            w0, [x2, #0x13]
    // 0xaff454: r1 = <Widget>
    //     0xaff454: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaff458: r0 = AllocateGrowableArray()
    //     0xaff458: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaff45c: mov             x1, x0
    // 0xaff460: ldur            x0, [fp, #-0x10]
    // 0xaff464: stur            x1, [fp, #-0x18]
    // 0xaff468: StoreField: r1->field_f = r0
    //     0xaff468: stur            w0, [x1, #0xf]
    // 0xaff46c: r0 = 4
    //     0xaff46c: movz            x0, #0x4
    // 0xaff470: StoreField: r1->field_b = r0
    //     0xaff470: stur            w0, [x1, #0xb]
    // 0xaff474: r0 = Row()
    //     0xaff474: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaff478: mov             x2, x0
    // 0xaff47c: r0 = Instance_Axis
    //     0xaff47c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaff480: stur            x2, [fp, #-0x28]
    // 0xaff484: StoreField: r2->field_f = r0
    //     0xaff484: stur            w0, [x2, #0xf]
    // 0xaff488: r3 = Instance_MainAxisAlignment
    //     0xaff488: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xaff48c: ldr             x3, [x3, #0xa8]
    // 0xaff490: StoreField: r2->field_13 = r3
    //     0xaff490: stur            w3, [x2, #0x13]
    // 0xaff494: r4 = Instance_MainAxisSize
    //     0xaff494: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaff498: ldr             x4, [x4, #0xa10]
    // 0xaff49c: ArrayStore: r2[0] = r4  ; List_4
    //     0xaff49c: stur            w4, [x2, #0x17]
    // 0xaff4a0: r1 = Instance_CrossAxisAlignment
    //     0xaff4a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaff4a4: ldr             x1, [x1, #0xa18]
    // 0xaff4a8: StoreField: r2->field_1b = r1
    //     0xaff4a8: stur            w1, [x2, #0x1b]
    // 0xaff4ac: r5 = Instance_VerticalDirection
    //     0xaff4ac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaff4b0: ldr             x5, [x5, #0xa20]
    // 0xaff4b4: StoreField: r2->field_23 = r5
    //     0xaff4b4: stur            w5, [x2, #0x23]
    // 0xaff4b8: r6 = Instance_Clip
    //     0xaff4b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaff4bc: ldr             x6, [x6, #0x38]
    // 0xaff4c0: StoreField: r2->field_2b = r6
    //     0xaff4c0: stur            w6, [x2, #0x2b]
    // 0xaff4c4: StoreField: r2->field_2f = rZR
    //     0xaff4c4: stur            xzr, [x2, #0x2f]
    // 0xaff4c8: ldur            x1, [fp, #-0x18]
    // 0xaff4cc: StoreField: r2->field_b = r1
    //     0xaff4cc: stur            w1, [x2, #0xb]
    // 0xaff4d0: ldur            x7, [fp, #-8]
    // 0xaff4d4: LoadField: r1 = r7->field_b
    //     0xaff4d4: ldur            w1, [x7, #0xb]
    // 0xaff4d8: DecompressPointer r1
    //     0xaff4d8: add             x1, x1, HEAP, lsl #32
    // 0xaff4dc: cmp             w1, NULL
    // 0xaff4e0: b.eq            #0xaffbf0
    // 0xaff4e4: LoadField: r8 = r1->field_b
    //     0xaff4e4: ldur            w8, [x1, #0xb]
    // 0xaff4e8: DecompressPointer r8
    //     0xaff4e8: add             x8, x8, HEAP, lsl #32
    // 0xaff4ec: cmp             w8, NULL
    // 0xaff4f0: b.ne            #0xaff4fc
    // 0xaff4f4: r1 = Null
    //     0xaff4f4: mov             x1, NULL
    // 0xaff4f8: b               #0xaff504
    // 0xaff4fc: LoadField: r1 = r8->field_f
    //     0xaff4fc: ldur            w1, [x8, #0xf]
    // 0xaff500: DecompressPointer r1
    //     0xaff500: add             x1, x1, HEAP, lsl #32
    // 0xaff504: cmp             w1, NULL
    // 0xaff508: b.ne            #0xaff514
    // 0xaff50c: r9 = ""
    //     0xaff50c: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaff510: b               #0xaff518
    // 0xaff514: mov             x9, x1
    // 0xaff518: ldur            x8, [fp, #-0x20]
    // 0xaff51c: stur            x9, [fp, #-0x10]
    // 0xaff520: LoadField: r1 = r8->field_13
    //     0xaff520: ldur            w1, [x8, #0x13]
    // 0xaff524: DecompressPointer r1
    //     0xaff524: add             x1, x1, HEAP, lsl #32
    // 0xaff528: r0 = of()
    //     0xaff528: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff52c: LoadField: r1 = r0->field_87
    //     0xaff52c: ldur            w1, [x0, #0x87]
    // 0xaff530: DecompressPointer r1
    //     0xaff530: add             x1, x1, HEAP, lsl #32
    // 0xaff534: LoadField: r0 = r1->field_2b
    //     0xaff534: ldur            w0, [x1, #0x2b]
    // 0xaff538: DecompressPointer r0
    //     0xaff538: add             x0, x0, HEAP, lsl #32
    // 0xaff53c: stur            x0, [fp, #-0x18]
    // 0xaff540: r1 = Instance_Color
    //     0xaff540: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaff544: d0 = 0.700000
    //     0xaff544: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaff548: ldr             d0, [x17, #0xf48]
    // 0xaff54c: r0 = withOpacity()
    //     0xaff54c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaff550: r16 = 12.000000
    //     0xaff550: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaff554: ldr             x16, [x16, #0x9e8]
    // 0xaff558: stp             x0, x16, [SP]
    // 0xaff55c: ldur            x1, [fp, #-0x18]
    // 0xaff560: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaff560: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaff564: ldr             x4, [x4, #0xaa0]
    // 0xaff568: r0 = copyWith()
    //     0xaff568: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaff56c: stur            x0, [fp, #-0x18]
    // 0xaff570: r0 = HtmlWidget()
    //     0xaff570: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xaff574: mov             x1, x0
    // 0xaff578: ldur            x0, [fp, #-0x10]
    // 0xaff57c: stur            x1, [fp, #-0x30]
    // 0xaff580: StoreField: r1->field_1f = r0
    //     0xaff580: stur            w0, [x1, #0x1f]
    // 0xaff584: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xaff584: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xaff588: ldr             x0, [x0, #0x1e0]
    // 0xaff58c: StoreField: r1->field_23 = r0
    //     0xaff58c: stur            w0, [x1, #0x23]
    // 0xaff590: r0 = Instance_ColumnMode
    //     0xaff590: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xaff594: ldr             x0, [x0, #0x1e8]
    // 0xaff598: StoreField: r1->field_3b = r0
    //     0xaff598: stur            w0, [x1, #0x3b]
    // 0xaff59c: ldur            x0, [fp, #-0x18]
    // 0xaff5a0: StoreField: r1->field_3f = r0
    //     0xaff5a0: stur            w0, [x1, #0x3f]
    // 0xaff5a4: r16 = <EdgeInsets>
    //     0xaff5a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaff5a8: ldr             x16, [x16, #0xda0]
    // 0xaff5ac: r30 = Instance_EdgeInsets
    //     0xaff5ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xaff5b0: ldr             lr, [lr, #0x1f0]
    // 0xaff5b4: stp             lr, x16, [SP]
    // 0xaff5b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff5b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff5bc: r0 = all()
    //     0xaff5bc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff5c0: ldur            x2, [fp, #-0x20]
    // 0xaff5c4: stur            x0, [fp, #-0x10]
    // 0xaff5c8: LoadField: r1 = r2->field_13
    //     0xaff5c8: ldur            w1, [x2, #0x13]
    // 0xaff5cc: DecompressPointer r1
    //     0xaff5cc: add             x1, x1, HEAP, lsl #32
    // 0xaff5d0: r0 = of()
    //     0xaff5d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff5d4: LoadField: r1 = r0->field_5b
    //     0xaff5d4: ldur            w1, [x0, #0x5b]
    // 0xaff5d8: DecompressPointer r1
    //     0xaff5d8: add             x1, x1, HEAP, lsl #32
    // 0xaff5dc: r0 = LoadClassIdInstr(r1)
    //     0xaff5dc: ldur            x0, [x1, #-1]
    //     0xaff5e0: ubfx            x0, x0, #0xc, #0x14
    // 0xaff5e4: d0 = 0.030000
    //     0xaff5e4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xaff5e8: ldr             d0, [x17, #0x238]
    // 0xaff5ec: r0 = GDT[cid_x0 + -0xffa]()
    //     0xaff5ec: sub             lr, x0, #0xffa
    //     0xaff5f0: ldr             lr, [x21, lr, lsl #3]
    //     0xaff5f4: blr             lr
    // 0xaff5f8: r16 = <Color>
    //     0xaff5f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaff5fc: ldr             x16, [x16, #0xf80]
    // 0xaff600: stp             x0, x16, [SP]
    // 0xaff604: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff604: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff608: r0 = all()
    //     0xaff608: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff60c: stur            x0, [fp, #-0x18]
    // 0xaff610: r0 = Radius()
    //     0xaff610: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaff614: d0 = 30.000000
    //     0xaff614: fmov            d0, #30.00000000
    // 0xaff618: stur            x0, [fp, #-0x38]
    // 0xaff61c: StoreField: r0->field_7 = d0
    //     0xaff61c: stur            d0, [x0, #7]
    // 0xaff620: StoreField: r0->field_f = d0
    //     0xaff620: stur            d0, [x0, #0xf]
    // 0xaff624: r0 = BorderRadius()
    //     0xaff624: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaff628: mov             x1, x0
    // 0xaff62c: ldur            x0, [fp, #-0x38]
    // 0xaff630: stur            x1, [fp, #-0x40]
    // 0xaff634: StoreField: r1->field_7 = r0
    //     0xaff634: stur            w0, [x1, #7]
    // 0xaff638: StoreField: r1->field_b = r0
    //     0xaff638: stur            w0, [x1, #0xb]
    // 0xaff63c: StoreField: r1->field_f = r0
    //     0xaff63c: stur            w0, [x1, #0xf]
    // 0xaff640: StoreField: r1->field_13 = r0
    //     0xaff640: stur            w0, [x1, #0x13]
    // 0xaff644: r0 = RoundedRectangleBorder()
    //     0xaff644: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaff648: mov             x1, x0
    // 0xaff64c: ldur            x0, [fp, #-0x40]
    // 0xaff650: StoreField: r1->field_b = r0
    //     0xaff650: stur            w0, [x1, #0xb]
    // 0xaff654: r0 = Instance_BorderSide
    //     0xaff654: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaff658: ldr             x0, [x0, #0xe20]
    // 0xaff65c: StoreField: r1->field_7 = r0
    //     0xaff65c: stur            w0, [x1, #7]
    // 0xaff660: r16 = <RoundedRectangleBorder>
    //     0xaff660: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaff664: ldr             x16, [x16, #0xf78]
    // 0xaff668: stp             x1, x16, [SP]
    // 0xaff66c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff66c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff670: r0 = all()
    //     0xaff670: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff674: stur            x0, [fp, #-0x38]
    // 0xaff678: r0 = ButtonStyle()
    //     0xaff678: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaff67c: mov             x1, x0
    // 0xaff680: ldur            x0, [fp, #-0x18]
    // 0xaff684: stur            x1, [fp, #-0x40]
    // 0xaff688: StoreField: r1->field_b = r0
    //     0xaff688: stur            w0, [x1, #0xb]
    // 0xaff68c: ldur            x0, [fp, #-0x10]
    // 0xaff690: StoreField: r1->field_23 = r0
    //     0xaff690: stur            w0, [x1, #0x23]
    // 0xaff694: ldur            x0, [fp, #-0x38]
    // 0xaff698: StoreField: r1->field_43 = r0
    //     0xaff698: stur            w0, [x1, #0x43]
    // 0xaff69c: r0 = TextButtonThemeData()
    //     0xaff69c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaff6a0: mov             x2, x0
    // 0xaff6a4: ldur            x0, [fp, #-0x40]
    // 0xaff6a8: stur            x2, [fp, #-0x10]
    // 0xaff6ac: StoreField: r2->field_7 = r0
    //     0xaff6ac: stur            w0, [x2, #7]
    // 0xaff6b0: r1 = "go back"
    //     0xaff6b0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a28] "go back"
    //     0xaff6b4: ldr             x1, [x1, #0xa28]
    // 0xaff6b8: r0 = capitalizeFirstWord()
    //     0xaff6b8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaff6bc: ldur            x2, [fp, #-0x20]
    // 0xaff6c0: stur            x0, [fp, #-0x18]
    // 0xaff6c4: LoadField: r1 = r2->field_13
    //     0xaff6c4: ldur            w1, [x2, #0x13]
    // 0xaff6c8: DecompressPointer r1
    //     0xaff6c8: add             x1, x1, HEAP, lsl #32
    // 0xaff6cc: r0 = of()
    //     0xaff6cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff6d0: LoadField: r1 = r0->field_87
    //     0xaff6d0: ldur            w1, [x0, #0x87]
    // 0xaff6d4: DecompressPointer r1
    //     0xaff6d4: add             x1, x1, HEAP, lsl #32
    // 0xaff6d8: LoadField: r0 = r1->field_7
    //     0xaff6d8: ldur            w0, [x1, #7]
    // 0xaff6dc: DecompressPointer r0
    //     0xaff6dc: add             x0, x0, HEAP, lsl #32
    // 0xaff6e0: ldur            x2, [fp, #-0x20]
    // 0xaff6e4: stur            x0, [fp, #-0x38]
    // 0xaff6e8: LoadField: r1 = r2->field_13
    //     0xaff6e8: ldur            w1, [x2, #0x13]
    // 0xaff6ec: DecompressPointer r1
    //     0xaff6ec: add             x1, x1, HEAP, lsl #32
    // 0xaff6f0: r0 = of()
    //     0xaff6f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff6f4: LoadField: r1 = r0->field_5b
    //     0xaff6f4: ldur            w1, [x0, #0x5b]
    // 0xaff6f8: DecompressPointer r1
    //     0xaff6f8: add             x1, x1, HEAP, lsl #32
    // 0xaff6fc: r16 = 14.000000
    //     0xaff6fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaff700: ldr             x16, [x16, #0x1d8]
    // 0xaff704: stp             x1, x16, [SP]
    // 0xaff708: ldur            x1, [fp, #-0x38]
    // 0xaff70c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaff70c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaff710: ldr             x4, [x4, #0xaa0]
    // 0xaff714: r0 = copyWith()
    //     0xaff714: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaff718: stur            x0, [fp, #-0x38]
    // 0xaff71c: r0 = Text()
    //     0xaff71c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaff720: mov             x3, x0
    // 0xaff724: ldur            x0, [fp, #-0x18]
    // 0xaff728: stur            x3, [fp, #-0x40]
    // 0xaff72c: StoreField: r3->field_b = r0
    //     0xaff72c: stur            w0, [x3, #0xb]
    // 0xaff730: ldur            x0, [fp, #-0x38]
    // 0xaff734: StoreField: r3->field_13 = r0
    //     0xaff734: stur            w0, [x3, #0x13]
    // 0xaff738: r1 = Function '<anonymous closure>':.
    //     0xaff738: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f80] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaff73c: ldr             x1, [x1, #0xf80]
    // 0xaff740: r2 = Null
    //     0xaff740: mov             x2, NULL
    // 0xaff744: r0 = AllocateClosure()
    //     0xaff744: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaff748: stur            x0, [fp, #-0x18]
    // 0xaff74c: r0 = TextButton()
    //     0xaff74c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaff750: mov             x1, x0
    // 0xaff754: ldur            x0, [fp, #-0x18]
    // 0xaff758: stur            x1, [fp, #-0x38]
    // 0xaff75c: StoreField: r1->field_b = r0
    //     0xaff75c: stur            w0, [x1, #0xb]
    // 0xaff760: r0 = false
    //     0xaff760: add             x0, NULL, #0x30  ; false
    // 0xaff764: StoreField: r1->field_27 = r0
    //     0xaff764: stur            w0, [x1, #0x27]
    // 0xaff768: r2 = true
    //     0xaff768: add             x2, NULL, #0x20  ; true
    // 0xaff76c: StoreField: r1->field_2f = r2
    //     0xaff76c: stur            w2, [x1, #0x2f]
    // 0xaff770: ldur            x3, [fp, #-0x40]
    // 0xaff774: StoreField: r1->field_37 = r3
    //     0xaff774: stur            w3, [x1, #0x37]
    // 0xaff778: r0 = TextButtonTheme()
    //     0xaff778: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaff77c: mov             x2, x0
    // 0xaff780: ldur            x0, [fp, #-0x10]
    // 0xaff784: stur            x2, [fp, #-0x18]
    // 0xaff788: StoreField: r2->field_f = r0
    //     0xaff788: stur            w0, [x2, #0xf]
    // 0xaff78c: ldur            x0, [fp, #-0x38]
    // 0xaff790: StoreField: r2->field_b = r0
    //     0xaff790: stur            w0, [x2, #0xb]
    // 0xaff794: r1 = <FlexParentData>
    //     0xaff794: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaff798: ldr             x1, [x1, #0xe00]
    // 0xaff79c: r0 = Expanded()
    //     0xaff79c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaff7a0: mov             x1, x0
    // 0xaff7a4: r0 = 1
    //     0xaff7a4: movz            x0, #0x1
    // 0xaff7a8: stur            x1, [fp, #-0x10]
    // 0xaff7ac: StoreField: r1->field_13 = r0
    //     0xaff7ac: stur            x0, [x1, #0x13]
    // 0xaff7b0: r2 = Instance_FlexFit
    //     0xaff7b0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaff7b4: ldr             x2, [x2, #0xe08]
    // 0xaff7b8: StoreField: r1->field_1b = r2
    //     0xaff7b8: stur            w2, [x1, #0x1b]
    // 0xaff7bc: ldur            x3, [fp, #-0x18]
    // 0xaff7c0: StoreField: r1->field_b = r3
    //     0xaff7c0: stur            w3, [x1, #0xb]
    // 0xaff7c4: r16 = <EdgeInsets>
    //     0xaff7c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaff7c8: ldr             x16, [x16, #0xda0]
    // 0xaff7cc: r30 = Instance_EdgeInsets
    //     0xaff7cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xaff7d0: ldr             lr, [lr, #0x1f0]
    // 0xaff7d4: stp             lr, x16, [SP]
    // 0xaff7d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff7d8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff7dc: r0 = all()
    //     0xaff7dc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff7e0: ldur            x2, [fp, #-0x20]
    // 0xaff7e4: stur            x0, [fp, #-0x18]
    // 0xaff7e8: LoadField: r1 = r2->field_13
    //     0xaff7e8: ldur            w1, [x2, #0x13]
    // 0xaff7ec: DecompressPointer r1
    //     0xaff7ec: add             x1, x1, HEAP, lsl #32
    // 0xaff7f0: r0 = of()
    //     0xaff7f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff7f4: LoadField: r1 = r0->field_5b
    //     0xaff7f4: ldur            w1, [x0, #0x5b]
    // 0xaff7f8: DecompressPointer r1
    //     0xaff7f8: add             x1, x1, HEAP, lsl #32
    // 0xaff7fc: r16 = <Color>
    //     0xaff7fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaff800: ldr             x16, [x16, #0xf80]
    // 0xaff804: stp             x1, x16, [SP]
    // 0xaff808: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff808: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff80c: r0 = all()
    //     0xaff80c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff810: stur            x0, [fp, #-0x38]
    // 0xaff814: r0 = Radius()
    //     0xaff814: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaff818: d0 = 30.000000
    //     0xaff818: fmov            d0, #30.00000000
    // 0xaff81c: stur            x0, [fp, #-0x40]
    // 0xaff820: StoreField: r0->field_7 = d0
    //     0xaff820: stur            d0, [x0, #7]
    // 0xaff824: StoreField: r0->field_f = d0
    //     0xaff824: stur            d0, [x0, #0xf]
    // 0xaff828: r0 = BorderRadius()
    //     0xaff828: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaff82c: mov             x1, x0
    // 0xaff830: ldur            x0, [fp, #-0x40]
    // 0xaff834: stur            x1, [fp, #-0x48]
    // 0xaff838: StoreField: r1->field_7 = r0
    //     0xaff838: stur            w0, [x1, #7]
    // 0xaff83c: StoreField: r1->field_b = r0
    //     0xaff83c: stur            w0, [x1, #0xb]
    // 0xaff840: StoreField: r1->field_f = r0
    //     0xaff840: stur            w0, [x1, #0xf]
    // 0xaff844: StoreField: r1->field_13 = r0
    //     0xaff844: stur            w0, [x1, #0x13]
    // 0xaff848: r0 = RoundedRectangleBorder()
    //     0xaff848: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaff84c: mov             x1, x0
    // 0xaff850: ldur            x0, [fp, #-0x48]
    // 0xaff854: StoreField: r1->field_b = r0
    //     0xaff854: stur            w0, [x1, #0xb]
    // 0xaff858: r0 = Instance_BorderSide
    //     0xaff858: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaff85c: ldr             x0, [x0, #0xe20]
    // 0xaff860: StoreField: r1->field_7 = r0
    //     0xaff860: stur            w0, [x1, #7]
    // 0xaff864: r16 = <RoundedRectangleBorder>
    //     0xaff864: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaff868: ldr             x16, [x16, #0xf78]
    // 0xaff86c: stp             x1, x16, [SP]
    // 0xaff870: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaff870: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaff874: r0 = all()
    //     0xaff874: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaff878: stur            x0, [fp, #-0x40]
    // 0xaff87c: r0 = ButtonStyle()
    //     0xaff87c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaff880: mov             x1, x0
    // 0xaff884: ldur            x0, [fp, #-0x38]
    // 0xaff888: stur            x1, [fp, #-0x48]
    // 0xaff88c: StoreField: r1->field_b = r0
    //     0xaff88c: stur            w0, [x1, #0xb]
    // 0xaff890: ldur            x0, [fp, #-0x18]
    // 0xaff894: StoreField: r1->field_23 = r0
    //     0xaff894: stur            w0, [x1, #0x23]
    // 0xaff898: ldur            x0, [fp, #-0x40]
    // 0xaff89c: StoreField: r1->field_43 = r0
    //     0xaff89c: stur            w0, [x1, #0x43]
    // 0xaff8a0: r0 = TextButtonThemeData()
    //     0xaff8a0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaff8a4: mov             x2, x0
    // 0xaff8a8: ldur            x0, [fp, #-0x48]
    // 0xaff8ac: stur            x2, [fp, #-0x18]
    // 0xaff8b0: StoreField: r2->field_7 = r0
    //     0xaff8b0: stur            w0, [x2, #7]
    // 0xaff8b4: ldur            x0, [fp, #-8]
    // 0xaff8b8: LoadField: r1 = r0->field_b
    //     0xaff8b8: ldur            w1, [x0, #0xb]
    // 0xaff8bc: DecompressPointer r1
    //     0xaff8bc: add             x1, x1, HEAP, lsl #32
    // 0xaff8c0: cmp             w1, NULL
    // 0xaff8c4: b.eq            #0xaffbf4
    // 0xaff8c8: LoadField: r0 = r1->field_b
    //     0xaff8c8: ldur            w0, [x1, #0xb]
    // 0xaff8cc: DecompressPointer r0
    //     0xaff8cc: add             x0, x0, HEAP, lsl #32
    // 0xaff8d0: cmp             w0, NULL
    // 0xaff8d4: b.ne            #0xaff8e0
    // 0xaff8d8: r0 = Null
    //     0xaff8d8: mov             x0, NULL
    // 0xaff8dc: b               #0xaff8ec
    // 0xaff8e0: LoadField: r1 = r0->field_b
    //     0xaff8e0: ldur            w1, [x0, #0xb]
    // 0xaff8e4: DecompressPointer r1
    //     0xaff8e4: add             x1, x1, HEAP, lsl #32
    // 0xaff8e8: mov             x0, x1
    // 0xaff8ec: cmp             w0, NULL
    // 0xaff8f0: b.ne            #0xaff8fc
    // 0xaff8f4: r1 = ""
    //     0xaff8f4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaff8f8: b               #0xaff900
    // 0xaff8fc: mov             x1, x0
    // 0xaff900: ldur            x0, [fp, #-0x20]
    // 0xaff904: ldur            x5, [fp, #-0x28]
    // 0xaff908: ldur            x4, [fp, #-0x30]
    // 0xaff90c: ldur            x3, [fp, #-0x10]
    // 0xaff910: r0 = capitalizeFirstWord()
    //     0xaff910: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaff914: ldur            x2, [fp, #-0x20]
    // 0xaff918: stur            x0, [fp, #-8]
    // 0xaff91c: LoadField: r1 = r2->field_13
    //     0xaff91c: ldur            w1, [x2, #0x13]
    // 0xaff920: DecompressPointer r1
    //     0xaff920: add             x1, x1, HEAP, lsl #32
    // 0xaff924: r0 = of()
    //     0xaff924: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaff928: LoadField: r1 = r0->field_87
    //     0xaff928: ldur            w1, [x0, #0x87]
    // 0xaff92c: DecompressPointer r1
    //     0xaff92c: add             x1, x1, HEAP, lsl #32
    // 0xaff930: LoadField: r0 = r1->field_7
    //     0xaff930: ldur            w0, [x1, #7]
    // 0xaff934: DecompressPointer r0
    //     0xaff934: add             x0, x0, HEAP, lsl #32
    // 0xaff938: r16 = 14.000000
    //     0xaff938: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaff93c: ldr             x16, [x16, #0x1d8]
    // 0xaff940: r30 = Instance_Color
    //     0xaff940: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaff944: stp             lr, x16, [SP]
    // 0xaff948: mov             x1, x0
    // 0xaff94c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaff94c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaff950: ldr             x4, [x4, #0xaa0]
    // 0xaff954: r0 = copyWith()
    //     0xaff954: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaff958: stur            x0, [fp, #-0x38]
    // 0xaff95c: r0 = Text()
    //     0xaff95c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaff960: mov             x3, x0
    // 0xaff964: ldur            x0, [fp, #-8]
    // 0xaff968: stur            x3, [fp, #-0x40]
    // 0xaff96c: StoreField: r3->field_b = r0
    //     0xaff96c: stur            w0, [x3, #0xb]
    // 0xaff970: ldur            x0, [fp, #-0x38]
    // 0xaff974: StoreField: r3->field_13 = r0
    //     0xaff974: stur            w0, [x3, #0x13]
    // 0xaff978: r0 = Instance_TextAlign
    //     0xaff978: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaff97c: StoreField: r3->field_1b = r0
    //     0xaff97c: stur            w0, [x3, #0x1b]
    // 0xaff980: ldur            x2, [fp, #-0x20]
    // 0xaff984: r1 = Function '<anonymous closure>':.
    //     0xaff984: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f88] AnonymousClosure: (0xaffbf8), in [package:customer_app/app/presentation/views/cosmetic/post_order/order_detail/cancel_return_order_bottom_sheet.dart] _CancelReturnOrderBottomSheetState::build (0xaff2e0)
    //     0xaff988: ldr             x1, [x1, #0xf88]
    // 0xaff98c: r0 = AllocateClosure()
    //     0xaff98c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaff990: stur            x0, [fp, #-8]
    // 0xaff994: r0 = TextButton()
    //     0xaff994: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaff998: mov             x1, x0
    // 0xaff99c: ldur            x0, [fp, #-8]
    // 0xaff9a0: stur            x1, [fp, #-0x20]
    // 0xaff9a4: StoreField: r1->field_b = r0
    //     0xaff9a4: stur            w0, [x1, #0xb]
    // 0xaff9a8: r0 = false
    //     0xaff9a8: add             x0, NULL, #0x30  ; false
    // 0xaff9ac: StoreField: r1->field_27 = r0
    //     0xaff9ac: stur            w0, [x1, #0x27]
    // 0xaff9b0: r0 = true
    //     0xaff9b0: add             x0, NULL, #0x20  ; true
    // 0xaff9b4: StoreField: r1->field_2f = r0
    //     0xaff9b4: stur            w0, [x1, #0x2f]
    // 0xaff9b8: ldur            x0, [fp, #-0x40]
    // 0xaff9bc: StoreField: r1->field_37 = r0
    //     0xaff9bc: stur            w0, [x1, #0x37]
    // 0xaff9c0: r0 = TextButtonTheme()
    //     0xaff9c0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaff9c4: mov             x2, x0
    // 0xaff9c8: ldur            x0, [fp, #-0x18]
    // 0xaff9cc: stur            x2, [fp, #-8]
    // 0xaff9d0: StoreField: r2->field_f = r0
    //     0xaff9d0: stur            w0, [x2, #0xf]
    // 0xaff9d4: ldur            x0, [fp, #-0x20]
    // 0xaff9d8: StoreField: r2->field_b = r0
    //     0xaff9d8: stur            w0, [x2, #0xb]
    // 0xaff9dc: r1 = <FlexParentData>
    //     0xaff9dc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaff9e0: ldr             x1, [x1, #0xe00]
    // 0xaff9e4: r0 = Expanded()
    //     0xaff9e4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaff9e8: mov             x3, x0
    // 0xaff9ec: r0 = 1
    //     0xaff9ec: movz            x0, #0x1
    // 0xaff9f0: stur            x3, [fp, #-0x18]
    // 0xaff9f4: StoreField: r3->field_13 = r0
    //     0xaff9f4: stur            x0, [x3, #0x13]
    // 0xaff9f8: r0 = Instance_FlexFit
    //     0xaff9f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaff9fc: ldr             x0, [x0, #0xe08]
    // 0xaffa00: StoreField: r3->field_1b = r0
    //     0xaffa00: stur            w0, [x3, #0x1b]
    // 0xaffa04: ldur            x0, [fp, #-8]
    // 0xaffa08: StoreField: r3->field_b = r0
    //     0xaffa08: stur            w0, [x3, #0xb]
    // 0xaffa0c: r1 = Null
    //     0xaffa0c: mov             x1, NULL
    // 0xaffa10: r2 = 6
    //     0xaffa10: movz            x2, #0x6
    // 0xaffa14: r0 = AllocateArray()
    //     0xaffa14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaffa18: mov             x2, x0
    // 0xaffa1c: ldur            x0, [fp, #-0x10]
    // 0xaffa20: stur            x2, [fp, #-8]
    // 0xaffa24: StoreField: r2->field_f = r0
    //     0xaffa24: stur            w0, [x2, #0xf]
    // 0xaffa28: r16 = Instance_SizedBox
    //     0xaffa28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xaffa2c: ldr             x16, [x16, #0xb20]
    // 0xaffa30: StoreField: r2->field_13 = r16
    //     0xaffa30: stur            w16, [x2, #0x13]
    // 0xaffa34: ldur            x0, [fp, #-0x18]
    // 0xaffa38: ArrayStore: r2[0] = r0  ; List_4
    //     0xaffa38: stur            w0, [x2, #0x17]
    // 0xaffa3c: r1 = <Widget>
    //     0xaffa3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaffa40: r0 = AllocateGrowableArray()
    //     0xaffa40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaffa44: mov             x1, x0
    // 0xaffa48: ldur            x0, [fp, #-8]
    // 0xaffa4c: stur            x1, [fp, #-0x10]
    // 0xaffa50: StoreField: r1->field_f = r0
    //     0xaffa50: stur            w0, [x1, #0xf]
    // 0xaffa54: r0 = 6
    //     0xaffa54: movz            x0, #0x6
    // 0xaffa58: StoreField: r1->field_b = r0
    //     0xaffa58: stur            w0, [x1, #0xb]
    // 0xaffa5c: r0 = Row()
    //     0xaffa5c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaffa60: mov             x1, x0
    // 0xaffa64: r0 = Instance_Axis
    //     0xaffa64: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaffa68: stur            x1, [fp, #-8]
    // 0xaffa6c: StoreField: r1->field_f = r0
    //     0xaffa6c: stur            w0, [x1, #0xf]
    // 0xaffa70: r0 = Instance_MainAxisAlignment
    //     0xaffa70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xaffa74: ldr             x0, [x0, #0xa8]
    // 0xaffa78: StoreField: r1->field_13 = r0
    //     0xaffa78: stur            w0, [x1, #0x13]
    // 0xaffa7c: r0 = Instance_MainAxisSize
    //     0xaffa7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaffa80: ldr             x0, [x0, #0xa10]
    // 0xaffa84: ArrayStore: r1[0] = r0  ; List_4
    //     0xaffa84: stur            w0, [x1, #0x17]
    // 0xaffa88: r0 = Instance_CrossAxisAlignment
    //     0xaffa88: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaffa8c: ldr             x0, [x0, #0x890]
    // 0xaffa90: StoreField: r1->field_1b = r0
    //     0xaffa90: stur            w0, [x1, #0x1b]
    // 0xaffa94: r2 = Instance_VerticalDirection
    //     0xaffa94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaffa98: ldr             x2, [x2, #0xa20]
    // 0xaffa9c: StoreField: r1->field_23 = r2
    //     0xaffa9c: stur            w2, [x1, #0x23]
    // 0xaffaa0: r3 = Instance_Clip
    //     0xaffaa0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaffaa4: ldr             x3, [x3, #0x38]
    // 0xaffaa8: StoreField: r1->field_2b = r3
    //     0xaffaa8: stur            w3, [x1, #0x2b]
    // 0xaffaac: StoreField: r1->field_2f = rZR
    //     0xaffaac: stur            xzr, [x1, #0x2f]
    // 0xaffab0: ldur            x4, [fp, #-0x10]
    // 0xaffab4: StoreField: r1->field_b = r4
    //     0xaffab4: stur            w4, [x1, #0xb]
    // 0xaffab8: r0 = Padding()
    //     0xaffab8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaffabc: mov             x3, x0
    // 0xaffac0: r0 = Instance_EdgeInsets
    //     0xaffac0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xaffac4: ldr             x0, [x0, #0x858]
    // 0xaffac8: stur            x3, [fp, #-0x10]
    // 0xaffacc: StoreField: r3->field_f = r0
    //     0xaffacc: stur            w0, [x3, #0xf]
    // 0xaffad0: ldur            x0, [fp, #-8]
    // 0xaffad4: StoreField: r3->field_b = r0
    //     0xaffad4: stur            w0, [x3, #0xb]
    // 0xaffad8: r1 = Null
    //     0xaffad8: mov             x1, NULL
    // 0xaffadc: r2 = 8
    //     0xaffadc: movz            x2, #0x8
    // 0xaffae0: r0 = AllocateArray()
    //     0xaffae0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaffae4: mov             x2, x0
    // 0xaffae8: ldur            x0, [fp, #-0x28]
    // 0xaffaec: stur            x2, [fp, #-8]
    // 0xaffaf0: StoreField: r2->field_f = r0
    //     0xaffaf0: stur            w0, [x2, #0xf]
    // 0xaffaf4: r16 = Instance_SizedBox
    //     0xaffaf4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xaffaf8: ldr             x16, [x16, #0x578]
    // 0xaffafc: StoreField: r2->field_13 = r16
    //     0xaffafc: stur            w16, [x2, #0x13]
    // 0xaffb00: ldur            x0, [fp, #-0x30]
    // 0xaffb04: ArrayStore: r2[0] = r0  ; List_4
    //     0xaffb04: stur            w0, [x2, #0x17]
    // 0xaffb08: ldur            x0, [fp, #-0x10]
    // 0xaffb0c: StoreField: r2->field_1b = r0
    //     0xaffb0c: stur            w0, [x2, #0x1b]
    // 0xaffb10: r1 = <Widget>
    //     0xaffb10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaffb14: r0 = AllocateGrowableArray()
    //     0xaffb14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaffb18: mov             x1, x0
    // 0xaffb1c: ldur            x0, [fp, #-8]
    // 0xaffb20: stur            x1, [fp, #-0x10]
    // 0xaffb24: StoreField: r1->field_f = r0
    //     0xaffb24: stur            w0, [x1, #0xf]
    // 0xaffb28: r0 = 8
    //     0xaffb28: movz            x0, #0x8
    // 0xaffb2c: StoreField: r1->field_b = r0
    //     0xaffb2c: stur            w0, [x1, #0xb]
    // 0xaffb30: r0 = Column()
    //     0xaffb30: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaffb34: mov             x1, x0
    // 0xaffb38: r0 = Instance_Axis
    //     0xaffb38: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaffb3c: stur            x1, [fp, #-8]
    // 0xaffb40: StoreField: r1->field_f = r0
    //     0xaffb40: stur            w0, [x1, #0xf]
    // 0xaffb44: r0 = Instance_MainAxisAlignment
    //     0xaffb44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaffb48: ldr             x0, [x0, #0xa08]
    // 0xaffb4c: StoreField: r1->field_13 = r0
    //     0xaffb4c: stur            w0, [x1, #0x13]
    // 0xaffb50: r0 = Instance_MainAxisSize
    //     0xaffb50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xaffb54: ldr             x0, [x0, #0xdd0]
    // 0xaffb58: ArrayStore: r1[0] = r0  ; List_4
    //     0xaffb58: stur            w0, [x1, #0x17]
    // 0xaffb5c: r0 = Instance_CrossAxisAlignment
    //     0xaffb5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaffb60: ldr             x0, [x0, #0x890]
    // 0xaffb64: StoreField: r1->field_1b = r0
    //     0xaffb64: stur            w0, [x1, #0x1b]
    // 0xaffb68: r0 = Instance_VerticalDirection
    //     0xaffb68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaffb6c: ldr             x0, [x0, #0xa20]
    // 0xaffb70: StoreField: r1->field_23 = r0
    //     0xaffb70: stur            w0, [x1, #0x23]
    // 0xaffb74: r0 = Instance_Clip
    //     0xaffb74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaffb78: ldr             x0, [x0, #0x38]
    // 0xaffb7c: StoreField: r1->field_2b = r0
    //     0xaffb7c: stur            w0, [x1, #0x2b]
    // 0xaffb80: StoreField: r1->field_2f = rZR
    //     0xaffb80: stur            xzr, [x1, #0x2f]
    // 0xaffb84: ldur            x0, [fp, #-0x10]
    // 0xaffb88: StoreField: r1->field_b = r0
    //     0xaffb88: stur            w0, [x1, #0xb]
    // 0xaffb8c: r0 = Padding()
    //     0xaffb8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaffb90: mov             x1, x0
    // 0xaffb94: r0 = Instance_EdgeInsets
    //     0xaffb94: add             x0, PP, #0x55, lsl #12  ; [pp+0x55a40] Obj!EdgeInsets@d58311
    //     0xaffb98: ldr             x0, [x0, #0xa40]
    // 0xaffb9c: stur            x1, [fp, #-0x10]
    // 0xaffba0: StoreField: r1->field_f = r0
    //     0xaffba0: stur            w0, [x1, #0xf]
    // 0xaffba4: ldur            x0, [fp, #-8]
    // 0xaffba8: StoreField: r1->field_b = r0
    //     0xaffba8: stur            w0, [x1, #0xb]
    // 0xaffbac: r0 = Container()
    //     0xaffbac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaffbb0: stur            x0, [fp, #-8]
    // 0xaffbb4: r16 = Instance_BoxDecoration
    //     0xaffbb4: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f90] Obj!BoxDecoration@d64aa1
    //     0xaffbb8: ldr             x16, [x16, #0xf90]
    // 0xaffbbc: ldur            lr, [fp, #-0x10]
    // 0xaffbc0: stp             lr, x16, [SP]
    // 0xaffbc4: mov             x1, x0
    // 0xaffbc8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xaffbc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xaffbcc: ldr             x4, [x4, #0x88]
    // 0xaffbd0: r0 = Container()
    //     0xaffbd0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaffbd4: ldur            x0, [fp, #-8]
    // 0xaffbd8: LeaveFrame
    //     0xaffbd8: mov             SP, fp
    //     0xaffbdc: ldp             fp, lr, [SP], #0x10
    // 0xaffbe0: ret
    //     0xaffbe0: ret             
    // 0xaffbe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaffbe4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaffbe8: b               #0xaff308
    // 0xaffbec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaffbec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaffbf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaffbf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaffbf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaffbf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaffbf8, size: 0xac
    // 0xaffbf8: EnterFrame
    //     0xaffbf8: stp             fp, lr, [SP, #-0x10]!
    //     0xaffbfc: mov             fp, SP
    // 0xaffc00: AllocStack(0x10)
    //     0xaffc00: sub             SP, SP, #0x10
    // 0xaffc04: SetupParameters()
    //     0xaffc04: ldr             x0, [fp, #0x10]
    //     0xaffc08: ldur            w1, [x0, #0x17]
    //     0xaffc0c: add             x1, x1, HEAP, lsl #32
    // 0xaffc10: CheckStackOverflow
    //     0xaffc10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaffc14: cmp             SP, x16
    //     0xaffc18: b.ls            #0xaffc98
    // 0xaffc1c: LoadField: r0 = r1->field_f
    //     0xaffc1c: ldur            w0, [x1, #0xf]
    // 0xaffc20: DecompressPointer r0
    //     0xaffc20: add             x0, x0, HEAP, lsl #32
    // 0xaffc24: LoadField: r1 = r0->field_b
    //     0xaffc24: ldur            w1, [x0, #0xb]
    // 0xaffc28: DecompressPointer r1
    //     0xaffc28: add             x1, x1, HEAP, lsl #32
    // 0xaffc2c: cmp             w1, NULL
    // 0xaffc30: b.eq            #0xaffca0
    // 0xaffc34: LoadField: r0 = r1->field_13
    //     0xaffc34: ldur            w0, [x1, #0x13]
    // 0xaffc38: DecompressPointer r0
    //     0xaffc38: add             x0, x0, HEAP, lsl #32
    // 0xaffc3c: LoadField: r2 = r1->field_f
    //     0xaffc3c: ldur            w2, [x1, #0xf]
    // 0xaffc40: DecompressPointer r2
    //     0xaffc40: add             x2, x2, HEAP, lsl #32
    // 0xaffc44: stp             x0, x2, [SP]
    // 0xaffc48: r4 = 0
    //     0xaffc48: movz            x4, #0
    // 0xaffc4c: ldr             x0, [SP, #8]
    // 0xaffc50: r16 = UnlinkedCall_0x613b5c
    //     0xaffc50: add             x16, PP, #0x57, lsl #12  ; [pp+0x57f98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaffc54: add             x16, x16, #0xf98
    // 0xaffc58: ldp             x5, lr, [x16]
    // 0xaffc5c: blr             lr
    // 0xaffc60: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaffc60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaffc64: ldr             x0, [x0, #0x1c80]
    //     0xaffc68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaffc6c: cmp             w0, w16
    //     0xaffc70: b.ne            #0xaffc7c
    //     0xaffc74: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaffc78: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaffc7c: str             NULL, [SP]
    // 0xaffc80: r4 = const [0x1, 0, 0, 0, null]
    //     0xaffc80: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaffc84: r0 = GetNavigation.back()
    //     0xaffc84: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xaffc88: r0 = Null
    //     0xaffc88: mov             x0, NULL
    // 0xaffc8c: LeaveFrame
    //     0xaffc8c: mov             SP, fp
    //     0xaffc90: ldp             fp, lr, [SP], #0x10
    // 0xaffc94: ret
    //     0xaffc94: ret             
    // 0xaffc98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaffc98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaffc9c: b               #0xaffc1c
    // 0xaffca0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaffca0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4148, size: 0x18, field offset: 0xc
//   const constructor, 
class CancelReturnOrderBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7de3c, size: 0x24
    // 0xc7de3c: EnterFrame
    //     0xc7de3c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7de40: mov             fp, SP
    // 0xc7de44: mov             x0, x1
    // 0xc7de48: r1 = <CancelReturnOrderBottomSheet>
    //     0xc7de48: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ba0] TypeArguments: <CancelReturnOrderBottomSheet>
    //     0xc7de4c: ldr             x1, [x1, #0xba0]
    // 0xc7de50: r0 = _CancelReturnOrderBottomSheetState()
    //     0xc7de50: bl              #0xc7de60  ; Allocate_CancelReturnOrderBottomSheetStateStub -> _CancelReturnOrderBottomSheetState (size=0x14)
    // 0xc7de54: LeaveFrame
    //     0xc7de54: mov             SP, fp
    //     0xc7de58: ldp             fp, lr, [SP], #0x10
    // 0xc7de5c: ret
    //     0xc7de5c: ret             
  }
}
