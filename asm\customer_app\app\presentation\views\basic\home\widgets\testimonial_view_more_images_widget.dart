// lib: , url: package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart

// class id: 1049164, size: 0x8
class :: {
}

// class id: 3509, size: 0x20, field offset: 0x14
class _TestimonialMoreImagesWidgetState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  _ build(/* No info */) {
    // ** addr: 0xa66748, size: 0x41c
    // 0xa66748: EnterFrame
    //     0xa66748: stp             fp, lr, [SP, #-0x10]!
    //     0xa6674c: mov             fp, SP
    // 0xa66750: AllocStack(0x60)
    //     0xa66750: sub             SP, SP, #0x60
    // 0xa66754: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa66754: stur            x1, [fp, #-8]
    //     0xa66758: stur            x2, [fp, #-0x10]
    // 0xa6675c: CheckStackOverflow
    //     0xa6675c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66760: cmp             SP, x16
    //     0xa66764: b.ls            #0xa66b48
    // 0xa66768: r1 = 2
    //     0xa66768: movz            x1, #0x2
    // 0xa6676c: r0 = AllocateContext()
    //     0xa6676c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa66770: mov             x3, x0
    // 0xa66774: ldur            x0, [fp, #-8]
    // 0xa66778: stur            x3, [fp, #-0x18]
    // 0xa6677c: StoreField: r3->field_f = r0
    //     0xa6677c: stur            w0, [x3, #0xf]
    // 0xa66780: ldur            x1, [fp, #-0x10]
    // 0xa66784: StoreField: r3->field_13 = r1
    //     0xa66784: stur            w1, [x3, #0x13]
    // 0xa66788: mov             x2, x3
    // 0xa6678c: r1 = Function '<anonymous closure>':.
    //     0xa6678c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2c0] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xa66790: ldr             x1, [x1, #0x2c0]
    // 0xa66794: r0 = AllocateClosure()
    //     0xa66794: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66798: stur            x0, [fp, #-0x10]
    // 0xa6679c: r0 = IconButton()
    //     0xa6679c: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xa667a0: mov             x1, x0
    // 0xa667a4: ldur            x0, [fp, #-0x10]
    // 0xa667a8: stur            x1, [fp, #-0x20]
    // 0xa667ac: StoreField: r1->field_3b = r0
    //     0xa667ac: stur            w0, [x1, #0x3b]
    // 0xa667b0: r0 = false
    //     0xa667b0: add             x0, NULL, #0x30  ; false
    // 0xa667b4: StoreField: r1->field_4f = r0
    //     0xa667b4: stur            w0, [x1, #0x4f]
    // 0xa667b8: r2 = Instance_Icon
    //     0xa667b8: add             x2, PP, #0x53, lsl #12  ; [pp+0x53478] Obj!Icon@d663b1
    //     0xa667bc: ldr             x2, [x2, #0x478]
    // 0xa667c0: StoreField: r1->field_1f = r2
    //     0xa667c0: stur            w2, [x1, #0x1f]
    // 0xa667c4: r2 = Instance__IconButtonVariant
    //     0xa667c4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xa667c8: ldr             x2, [x2, #0x900]
    // 0xa667cc: StoreField: r1->field_6b = r2
    //     0xa667cc: stur            w2, [x1, #0x6b]
    // 0xa667d0: r0 = Padding()
    //     0xa667d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa667d4: mov             x1, x0
    // 0xa667d8: r0 = Instance_EdgeInsets
    //     0xa667d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa667dc: ldr             x0, [x0, #0x980]
    // 0xa667e0: stur            x1, [fp, #-0x10]
    // 0xa667e4: StoreField: r1->field_f = r0
    //     0xa667e4: stur            w0, [x1, #0xf]
    // 0xa667e8: ldur            x0, [fp, #-0x20]
    // 0xa667ec: StoreField: r1->field_b = r0
    //     0xa667ec: stur            w0, [x1, #0xb]
    // 0xa667f0: r0 = Align()
    //     0xa667f0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa667f4: mov             x1, x0
    // 0xa667f8: r0 = Instance_Alignment
    //     0xa667f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xa667fc: ldr             x0, [x0, #0x950]
    // 0xa66800: stur            x1, [fp, #-0x20]
    // 0xa66804: StoreField: r1->field_f = r0
    //     0xa66804: stur            w0, [x1, #0xf]
    // 0xa66808: ldur            x0, [fp, #-0x10]
    // 0xa6680c: StoreField: r1->field_b = r0
    //     0xa6680c: stur            w0, [x1, #0xb]
    // 0xa66810: ldur            x2, [fp, #-8]
    // 0xa66814: LoadField: r0 = r2->field_b
    //     0xa66814: ldur            w0, [x2, #0xb]
    // 0xa66818: DecompressPointer r0
    //     0xa66818: add             x0, x0, HEAP, lsl #32
    // 0xa6681c: cmp             w0, NULL
    // 0xa66820: b.eq            #0xa66b50
    // 0xa66824: LoadField: r3 = r0->field_b
    //     0xa66824: ldur            w3, [x0, #0xb]
    // 0xa66828: DecompressPointer r3
    //     0xa66828: add             x3, x3, HEAP, lsl #32
    // 0xa6682c: cmp             w3, NULL
    // 0xa66830: b.ne            #0xa6683c
    // 0xa66834: r3 = Null
    //     0xa66834: mov             x3, NULL
    // 0xa66838: b               #0xa66844
    // 0xa6683c: LoadField: r4 = r3->field_b
    //     0xa6683c: ldur            w4, [x3, #0xb]
    // 0xa66840: mov             x3, x4
    // 0xa66844: cmp             w3, NULL
    // 0xa66848: b.ne            #0xa66888
    // 0xa6684c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa6684c: ldur            w3, [x0, #0x17]
    // 0xa66850: DecompressPointer r3
    //     0xa66850: add             x3, x3, HEAP, lsl #32
    // 0xa66854: cmp             w3, NULL
    // 0xa66858: b.ne            #0xa66864
    // 0xa6685c: r0 = Null
    //     0xa6685c: mov             x0, NULL
    // 0xa66860: b               #0xa66880
    // 0xa66864: r0 = LoadClassIdInstr(r3)
    //     0xa66864: ldur            x0, [x3, #-1]
    //     0xa66868: ubfx            x0, x0, #0xc, #0x14
    // 0xa6686c: str             x3, [SP]
    // 0xa66870: r0 = GDT[cid_x0 + 0xc898]()
    //     0xa66870: movz            x17, #0xc898
    //     0xa66874: add             lr, x0, x17
    //     0xa66878: ldr             lr, [x21, lr, lsl #3]
    //     0xa6687c: blr             lr
    // 0xa66880: mov             x4, x0
    // 0xa66884: b               #0xa6688c
    // 0xa66888: mov             x4, x3
    // 0xa6688c: ldur            x3, [fp, #-8]
    // 0xa66890: ldur            x0, [fp, #-0x20]
    // 0xa66894: stur            x4, [fp, #-0x28]
    // 0xa66898: LoadField: r5 = r3->field_1b
    //     0xa66898: ldur            w5, [x3, #0x1b]
    // 0xa6689c: DecompressPointer r5
    //     0xa6689c: add             x5, x5, HEAP, lsl #32
    // 0xa668a0: r16 = Sentinel
    //     0xa668a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa668a4: cmp             w5, w16
    // 0xa668a8: b.eq            #0xa66b54
    // 0xa668ac: ldur            x2, [fp, #-0x18]
    // 0xa668b0: stur            x5, [fp, #-0x10]
    // 0xa668b4: r1 = Function '<anonymous closure>':.
    //     0xa668b4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2c8] AnonymousClosure: (0xa67244), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xa66748)
    //     0xa668b8: ldr             x1, [x1, #0x2c8]
    // 0xa668bc: r0 = AllocateClosure()
    //     0xa668bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa668c0: ldur            x2, [fp, #-0x18]
    // 0xa668c4: r1 = Function '<anonymous closure>':.
    //     0xa668c4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2d0] AnonymousClosure: (0xa6707c), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xa66748)
    //     0xa668c8: ldr             x1, [x1, #0x2d0]
    // 0xa668cc: stur            x0, [fp, #-0x30]
    // 0xa668d0: r0 = AllocateClosure()
    //     0xa668d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa668d4: stur            x0, [fp, #-0x38]
    // 0xa668d8: r0 = PageView()
    //     0xa668d8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa668dc: stur            x0, [fp, #-0x40]
    // 0xa668e0: ldur            x16, [fp, #-0x10]
    // 0xa668e4: str             x16, [SP]
    // 0xa668e8: mov             x1, x0
    // 0xa668ec: ldur            x2, [fp, #-0x38]
    // 0xa668f0: ldur            x3, [fp, #-0x28]
    // 0xa668f4: ldur            x5, [fp, #-0x30]
    // 0xa668f8: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xa668f8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xa668fc: ldr             x4, [x4, #0xd60]
    // 0xa66900: r0 = PageView.builder()
    //     0xa66900: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa66904: r1 = <FlexParentData>
    //     0xa66904: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa66908: ldr             x1, [x1, #0xe00]
    // 0xa6690c: r0 = Expanded()
    //     0xa6690c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa66910: mov             x3, x0
    // 0xa66914: r0 = 1
    //     0xa66914: movz            x0, #0x1
    // 0xa66918: stur            x3, [fp, #-0x10]
    // 0xa6691c: StoreField: r3->field_13 = r0
    //     0xa6691c: stur            x0, [x3, #0x13]
    // 0xa66920: r0 = Instance_FlexFit
    //     0xa66920: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa66924: ldr             x0, [x0, #0xe08]
    // 0xa66928: StoreField: r3->field_1b = r0
    //     0xa66928: stur            w0, [x3, #0x1b]
    // 0xa6692c: ldur            x0, [fp, #-0x40]
    // 0xa66930: StoreField: r3->field_b = r0
    //     0xa66930: stur            w0, [x3, #0xb]
    // 0xa66934: r1 = Null
    //     0xa66934: mov             x1, NULL
    // 0xa66938: r2 = 6
    //     0xa66938: movz            x2, #0x6
    // 0xa6693c: r0 = AllocateArray()
    //     0xa6693c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa66940: mov             x2, x0
    // 0xa66944: ldur            x0, [fp, #-0x20]
    // 0xa66948: stur            x2, [fp, #-0x28]
    // 0xa6694c: StoreField: r2->field_f = r0
    //     0xa6694c: stur            w0, [x2, #0xf]
    // 0xa66950: r16 = Instance_SizedBox
    //     0xa66950: add             x16, PP, #0x53, lsl #12  ; [pp+0x53490] Obj!SizedBox@d68021
    //     0xa66954: ldr             x16, [x16, #0x490]
    // 0xa66958: StoreField: r2->field_13 = r16
    //     0xa66958: stur            w16, [x2, #0x13]
    // 0xa6695c: ldur            x0, [fp, #-0x10]
    // 0xa66960: ArrayStore: r2[0] = r0  ; List_4
    //     0xa66960: stur            w0, [x2, #0x17]
    // 0xa66964: r1 = <Widget>
    //     0xa66964: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa66968: r0 = AllocateGrowableArray()
    //     0xa66968: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa6696c: mov             x1, x0
    // 0xa66970: ldur            x0, [fp, #-0x28]
    // 0xa66974: stur            x1, [fp, #-0x10]
    // 0xa66978: StoreField: r1->field_f = r0
    //     0xa66978: stur            w0, [x1, #0xf]
    // 0xa6697c: r0 = 6
    //     0xa6697c: movz            x0, #0x6
    // 0xa66980: StoreField: r1->field_b = r0
    //     0xa66980: stur            w0, [x1, #0xb]
    // 0xa66984: r0 = Column()
    //     0xa66984: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa66988: mov             x1, x0
    // 0xa6698c: r0 = Instance_Axis
    //     0xa6698c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa66990: stur            x1, [fp, #-0x20]
    // 0xa66994: StoreField: r1->field_f = r0
    //     0xa66994: stur            w0, [x1, #0xf]
    // 0xa66998: r0 = Instance_MainAxisAlignment
    //     0xa66998: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa6699c: ldr             x0, [x0, #0xa08]
    // 0xa669a0: StoreField: r1->field_13 = r0
    //     0xa669a0: stur            w0, [x1, #0x13]
    // 0xa669a4: r0 = Instance_MainAxisSize
    //     0xa669a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa669a8: ldr             x0, [x0, #0xa10]
    // 0xa669ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xa669ac: stur            w0, [x1, #0x17]
    // 0xa669b0: r0 = Instance_CrossAxisAlignment
    //     0xa669b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa669b4: ldr             x0, [x0, #0xa18]
    // 0xa669b8: StoreField: r1->field_1b = r0
    //     0xa669b8: stur            w0, [x1, #0x1b]
    // 0xa669bc: r0 = Instance_VerticalDirection
    //     0xa669bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa669c0: ldr             x0, [x0, #0xa20]
    // 0xa669c4: StoreField: r1->field_23 = r0
    //     0xa669c4: stur            w0, [x1, #0x23]
    // 0xa669c8: r0 = Instance_Clip
    //     0xa669c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa669cc: ldr             x0, [x0, #0x38]
    // 0xa669d0: StoreField: r1->field_2b = r0
    //     0xa669d0: stur            w0, [x1, #0x2b]
    // 0xa669d4: StoreField: r1->field_2f = rZR
    //     0xa669d4: stur            xzr, [x1, #0x2f]
    // 0xa669d8: ldur            x0, [fp, #-0x10]
    // 0xa669dc: StoreField: r1->field_b = r0
    //     0xa669dc: stur            w0, [x1, #0xb]
    // 0xa669e0: r0 = SafeArea()
    //     0xa669e0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xa669e4: mov             x2, x0
    // 0xa669e8: r1 = true
    //     0xa669e8: add             x1, NULL, #0x20  ; true
    // 0xa669ec: stur            x2, [fp, #-0x10]
    // 0xa669f0: StoreField: r2->field_b = r1
    //     0xa669f0: stur            w1, [x2, #0xb]
    // 0xa669f4: StoreField: r2->field_f = r1
    //     0xa669f4: stur            w1, [x2, #0xf]
    // 0xa669f8: StoreField: r2->field_13 = r1
    //     0xa669f8: stur            w1, [x2, #0x13]
    // 0xa669fc: ArrayStore: r2[0] = r1  ; List_4
    //     0xa669fc: stur            w1, [x2, #0x17]
    // 0xa66a00: r0 = Instance_EdgeInsets
    //     0xa66a00: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xa66a04: StoreField: r2->field_1b = r0
    //     0xa66a04: stur            w0, [x2, #0x1b]
    // 0xa66a08: r3 = false
    //     0xa66a08: add             x3, NULL, #0x30  ; false
    // 0xa66a0c: StoreField: r2->field_1f = r3
    //     0xa66a0c: stur            w3, [x2, #0x1f]
    // 0xa66a10: ldur            x0, [fp, #-0x20]
    // 0xa66a14: StoreField: r2->field_23 = r0
    //     0xa66a14: stur            w0, [x2, #0x23]
    // 0xa66a18: ldur            x0, [fp, #-8]
    // 0xa66a1c: LoadField: r4 = r0->field_b
    //     0xa66a1c: ldur            w4, [x0, #0xb]
    // 0xa66a20: DecompressPointer r4
    //     0xa66a20: add             x4, x4, HEAP, lsl #32
    // 0xa66a24: cmp             w4, NULL
    // 0xa66a28: b.eq            #0xa66b60
    // 0xa66a2c: LoadField: r0 = r4->field_b
    //     0xa66a2c: ldur            w0, [x4, #0xb]
    // 0xa66a30: DecompressPointer r0
    //     0xa66a30: add             x0, x0, HEAP, lsl #32
    // 0xa66a34: cmp             w0, NULL
    // 0xa66a38: b.ne            #0xa66a44
    // 0xa66a3c: r0 = Null
    //     0xa66a3c: mov             x0, NULL
    // 0xa66a40: b               #0xa66a4c
    // 0xa66a44: LoadField: r5 = r0->field_b
    //     0xa66a44: ldur            w5, [x0, #0xb]
    // 0xa66a48: mov             x0, x5
    // 0xa66a4c: cmp             w0, NULL
    // 0xa66a50: b.ne            #0xa66a94
    // 0xa66a54: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xa66a54: ldur            w0, [x4, #0x17]
    // 0xa66a58: DecompressPointer r0
    //     0xa66a58: add             x0, x0, HEAP, lsl #32
    // 0xa66a5c: cmp             w0, NULL
    // 0xa66a60: b.ne            #0xa66a6c
    // 0xa66a64: r0 = Null
    //     0xa66a64: mov             x0, NULL
    // 0xa66a68: b               #0xa66a8c
    // 0xa66a6c: r4 = LoadClassIdInstr(r0)
    //     0xa66a6c: ldur            x4, [x0, #-1]
    //     0xa66a70: ubfx            x4, x4, #0xc, #0x14
    // 0xa66a74: str             x0, [SP]
    // 0xa66a78: mov             x0, x4
    // 0xa66a7c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xa66a7c: movz            x17, #0xc898
    //     0xa66a80: add             lr, x0, x17
    //     0xa66a84: ldr             lr, [x21, lr, lsl #3]
    //     0xa66a88: blr             lr
    // 0xa66a8c: mov             x3, x0
    // 0xa66a90: b               #0xa66a98
    // 0xa66a94: mov             x3, x0
    // 0xa66a98: ldur            x0, [fp, #-0x10]
    // 0xa66a9c: ldur            x2, [fp, #-0x18]
    // 0xa66aa0: stur            x3, [fp, #-8]
    // 0xa66aa4: r1 = Function '<anonymous closure>':.
    //     0xa66aa4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2d8] AnonymousClosure: (0xa66b64), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xa66748)
    //     0xa66aa8: ldr             x1, [x1, #0x2d8]
    // 0xa66aac: r0 = AllocateClosure()
    //     0xa66aac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66ab0: stur            x0, [fp, #-0x18]
    // 0xa66ab4: r0 = ListView()
    //     0xa66ab4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa66ab8: stur            x0, [fp, #-0x20]
    // 0xa66abc: r16 = Instance_Axis
    //     0xa66abc: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa66ac0: str             x16, [SP]
    // 0xa66ac4: mov             x1, x0
    // 0xa66ac8: ldur            x2, [fp, #-0x18]
    // 0xa66acc: ldur            x3, [fp, #-8]
    // 0xa66ad0: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xa66ad0: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xa66ad4: ldr             x4, [x4, #0x4a0]
    // 0xa66ad8: r0 = ListView.builder()
    //     0xa66ad8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xa66adc: r0 = Container()
    //     0xa66adc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa66ae0: stur            x0, [fp, #-8]
    // 0xa66ae4: r16 = Instance_Color
    //     0xa66ae4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa66ae8: r30 = 170.000000
    //     0xa66ae8: add             lr, PP, #0x53, lsl #12  ; [pp+0x534a8] 170
    //     0xa66aec: ldr             lr, [lr, #0x4a8]
    // 0xa66af0: stp             lr, x16, [SP, #0x10]
    // 0xa66af4: r16 = Instance_EdgeInsets
    //     0xa66af4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xa66af8: ldr             x16, [x16, #0x878]
    // 0xa66afc: ldur            lr, [fp, #-0x20]
    // 0xa66b00: stp             lr, x16, [SP]
    // 0xa66b04: mov             x1, x0
    // 0xa66b08: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x2, padding, 0x3, null]
    //     0xa66b08: add             x4, PP, #0x53, lsl #12  ; [pp+0x534b0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x2, "padding", 0x3, Null]
    //     0xa66b0c: ldr             x4, [x4, #0x4b0]
    // 0xa66b10: r0 = Container()
    //     0xa66b10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa66b14: r0 = Scaffold()
    //     0xa66b14: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xa66b18: ldur            x1, [fp, #-0x10]
    // 0xa66b1c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa66b1c: stur            w1, [x0, #0x17]
    // 0xa66b20: ldur            x1, [fp, #-8]
    // 0xa66b24: StoreField: r0->field_37 = r1
    //     0xa66b24: stur            w1, [x0, #0x37]
    // 0xa66b28: r1 = true
    //     0xa66b28: add             x1, NULL, #0x20  ; true
    // 0xa66b2c: StoreField: r0->field_43 = r1
    //     0xa66b2c: stur            w1, [x0, #0x43]
    // 0xa66b30: r1 = false
    //     0xa66b30: add             x1, NULL, #0x30  ; false
    // 0xa66b34: StoreField: r0->field_b = r1
    //     0xa66b34: stur            w1, [x0, #0xb]
    // 0xa66b38: StoreField: r0->field_f = r1
    //     0xa66b38: stur            w1, [x0, #0xf]
    // 0xa66b3c: LeaveFrame
    //     0xa66b3c: mov             SP, fp
    //     0xa66b40: ldp             fp, lr, [SP], #0x10
    // 0xa66b44: ret
    //     0xa66b44: ret             
    // 0xa66b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66b48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66b4c: b               #0xa66768
    // 0xa66b50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa66b50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa66b54: r9 = _pageController
    //     0xa66b54: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5a2e0] Field <_TestimonialMoreImagesWidgetState@1341381811._pageController@1341381811>: late (offset: 0x1c)
    //     0xa66b58: ldr             x9, [x9, #0x2e0]
    // 0xa66b5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa66b5c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa66b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa66b60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa66b64, size: 0x310
    // 0xa66b64: EnterFrame
    //     0xa66b64: stp             fp, lr, [SP, #-0x10]!
    //     0xa66b68: mov             fp, SP
    // 0xa66b6c: AllocStack(0x50)
    //     0xa66b6c: sub             SP, SP, #0x50
    // 0xa66b70: SetupParameters()
    //     0xa66b70: ldr             x0, [fp, #0x20]
    //     0xa66b74: ldur            w1, [x0, #0x17]
    //     0xa66b78: add             x1, x1, HEAP, lsl #32
    //     0xa66b7c: stur            x1, [fp, #-8]
    // 0xa66b80: CheckStackOverflow
    //     0xa66b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66b84: cmp             SP, x16
    //     0xa66b88: b.ls            #0xa66e64
    // 0xa66b8c: r1 = 1
    //     0xa66b8c: movz            x1, #0x1
    // 0xa66b90: r0 = AllocateContext()
    //     0xa66b90: bl              #0x16f6108  ; AllocateContextStub
    // 0xa66b94: mov             x3, x0
    // 0xa66b98: ldur            x0, [fp, #-8]
    // 0xa66b9c: stur            x3, [fp, #-0x10]
    // 0xa66ba0: StoreField: r3->field_b = r0
    //     0xa66ba0: stur            w0, [x3, #0xb]
    // 0xa66ba4: ldr             x1, [fp, #0x10]
    // 0xa66ba8: StoreField: r3->field_f = r1
    //     0xa66ba8: stur            w1, [x3, #0xf]
    // 0xa66bac: LoadField: r2 = r0->field_f
    //     0xa66bac: ldur            w2, [x0, #0xf]
    // 0xa66bb0: DecompressPointer r2
    //     0xa66bb0: add             x2, x2, HEAP, lsl #32
    // 0xa66bb4: LoadField: r4 = r2->field_13
    //     0xa66bb4: ldur            x4, [x2, #0x13]
    // 0xa66bb8: r2 = LoadInt32Instr(r1)
    //     0xa66bb8: sbfx            x2, x1, #1, #0x1f
    //     0xa66bbc: tbz             w1, #0, #0xa66bc4
    //     0xa66bc0: ldur            x2, [x1, #7]
    // 0xa66bc4: cmp             x4, x2
    // 0xa66bc8: b.ne            #0xa66bd8
    // 0xa66bcc: r2 = Instance_Color
    //     0xa66bcc: add             x2, PP, #0x53, lsl #12  ; [pp+0x534c0] Obj!Color@d6b101
    //     0xa66bd0: ldr             x2, [x2, #0x4c0]
    // 0xa66bd4: b               #0xa66be0
    // 0xa66bd8: r2 = Instance_Color
    //     0xa66bd8: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa66bdc: ldr             x2, [x2, #0xf88]
    // 0xa66be0: r16 = 3.000000
    //     0xa66be0: add             x16, PP, #0x53, lsl #12  ; [pp+0x534c8] 3
    //     0xa66be4: ldr             x16, [x16, #0x4c8]
    // 0xa66be8: str             x16, [SP]
    // 0xa66bec: r1 = Null
    //     0xa66bec: mov             x1, NULL
    // 0xa66bf0: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xa66bf0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xa66bf4: ldr             x4, [x4, #0x108]
    // 0xa66bf8: r0 = Border.all()
    //     0xa66bf8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa66bfc: stur            x0, [fp, #-0x18]
    // 0xa66c00: r0 = BoxDecoration()
    //     0xa66c00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa66c04: mov             x1, x0
    // 0xa66c08: ldur            x0, [fp, #-0x18]
    // 0xa66c0c: stur            x1, [fp, #-0x20]
    // 0xa66c10: StoreField: r1->field_f = r0
    //     0xa66c10: stur            w0, [x1, #0xf]
    // 0xa66c14: r0 = Instance_BoxShape
    //     0xa66c14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa66c18: ldr             x0, [x0, #0x80]
    // 0xa66c1c: StoreField: r1->field_23 = r0
    //     0xa66c1c: stur            w0, [x1, #0x23]
    // 0xa66c20: r0 = Radius()
    //     0xa66c20: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa66c24: d0 = 12.000000
    //     0xa66c24: fmov            d0, #12.00000000
    // 0xa66c28: stur            x0, [fp, #-0x18]
    // 0xa66c2c: StoreField: r0->field_7 = d0
    //     0xa66c2c: stur            d0, [x0, #7]
    // 0xa66c30: StoreField: r0->field_f = d0
    //     0xa66c30: stur            d0, [x0, #0xf]
    // 0xa66c34: r0 = BorderRadius()
    //     0xa66c34: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa66c38: mov             x2, x0
    // 0xa66c3c: ldur            x0, [fp, #-0x18]
    // 0xa66c40: stur            x2, [fp, #-0x28]
    // 0xa66c44: StoreField: r2->field_7 = r0
    //     0xa66c44: stur            w0, [x2, #7]
    // 0xa66c48: StoreField: r2->field_b = r0
    //     0xa66c48: stur            w0, [x2, #0xb]
    // 0xa66c4c: StoreField: r2->field_f = r0
    //     0xa66c4c: stur            w0, [x2, #0xf]
    // 0xa66c50: StoreField: r2->field_13 = r0
    //     0xa66c50: stur            w0, [x2, #0x13]
    // 0xa66c54: ldur            x0, [fp, #-8]
    // 0xa66c58: LoadField: r1 = r0->field_f
    //     0xa66c58: ldur            w1, [x0, #0xf]
    // 0xa66c5c: DecompressPointer r1
    //     0xa66c5c: add             x1, x1, HEAP, lsl #32
    // 0xa66c60: LoadField: r3 = r1->field_b
    //     0xa66c60: ldur            w3, [x1, #0xb]
    // 0xa66c64: DecompressPointer r3
    //     0xa66c64: add             x3, x3, HEAP, lsl #32
    // 0xa66c68: cmp             w3, NULL
    // 0xa66c6c: b.eq            #0xa66e6c
    // 0xa66c70: LoadField: r4 = r3->field_b
    //     0xa66c70: ldur            w4, [x3, #0xb]
    // 0xa66c74: DecompressPointer r4
    //     0xa66c74: add             x4, x4, HEAP, lsl #32
    // 0xa66c78: cmp             w4, NULL
    // 0xa66c7c: b.ne            #0xa66c8c
    // 0xa66c80: ldur            x5, [fp, #-0x10]
    // 0xa66c84: r0 = Null
    //     0xa66c84: mov             x0, NULL
    // 0xa66c88: b               #0xa66cf0
    // 0xa66c8c: ldur            x5, [fp, #-0x10]
    // 0xa66c90: LoadField: r0 = r5->field_f
    //     0xa66c90: ldur            w0, [x5, #0xf]
    // 0xa66c94: DecompressPointer r0
    //     0xa66c94: add             x0, x0, HEAP, lsl #32
    // 0xa66c98: LoadField: r1 = r4->field_b
    //     0xa66c98: ldur            w1, [x4, #0xb]
    // 0xa66c9c: r6 = LoadInt32Instr(r0)
    //     0xa66c9c: sbfx            x6, x0, #1, #0x1f
    //     0xa66ca0: tbz             w0, #0, #0xa66ca8
    //     0xa66ca4: ldur            x6, [x0, #7]
    // 0xa66ca8: r0 = LoadInt32Instr(r1)
    //     0xa66ca8: sbfx            x0, x1, #1, #0x1f
    // 0xa66cac: mov             x1, x6
    // 0xa66cb0: cmp             x1, x0
    // 0xa66cb4: b.hs            #0xa66e70
    // 0xa66cb8: LoadField: r0 = r4->field_f
    //     0xa66cb8: ldur            w0, [x4, #0xf]
    // 0xa66cbc: DecompressPointer r0
    //     0xa66cbc: add             x0, x0, HEAP, lsl #32
    // 0xa66cc0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa66cc0: add             x16, x0, x6, lsl #2
    //     0xa66cc4: ldur            w1, [x16, #0xf]
    // 0xa66cc8: DecompressPointer r1
    //     0xa66cc8: add             x1, x1, HEAP, lsl #32
    // 0xa66ccc: LoadField: r0 = r1->field_7
    //     0xa66ccc: ldur            w0, [x1, #7]
    // 0xa66cd0: DecompressPointer r0
    //     0xa66cd0: add             x0, x0, HEAP, lsl #32
    // 0xa66cd4: cmp             w0, NULL
    // 0xa66cd8: b.ne            #0xa66ce4
    // 0xa66cdc: r0 = Null
    //     0xa66cdc: mov             x0, NULL
    // 0xa66ce0: b               #0xa66cf0
    // 0xa66ce4: LoadField: r1 = r0->field_b
    //     0xa66ce4: ldur            w1, [x0, #0xb]
    // 0xa66ce8: DecompressPointer r1
    //     0xa66ce8: add             x1, x1, HEAP, lsl #32
    // 0xa66cec: mov             x0, x1
    // 0xa66cf0: cmp             w0, NULL
    // 0xa66cf4: b.ne            #0xa66d54
    // 0xa66cf8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa66cf8: ldur            w0, [x3, #0x17]
    // 0xa66cfc: DecompressPointer r0
    //     0xa66cfc: add             x0, x0, HEAP, lsl #32
    // 0xa66d00: cmp             w0, NULL
    // 0xa66d04: b.ne            #0xa66d10
    // 0xa66d08: r0 = Null
    //     0xa66d08: mov             x0, NULL
    // 0xa66d0c: b               #0xa66d54
    // 0xa66d10: LoadField: r1 = r5->field_f
    //     0xa66d10: ldur            w1, [x5, #0xf]
    // 0xa66d14: DecompressPointer r1
    //     0xa66d14: add             x1, x1, HEAP, lsl #32
    // 0xa66d18: r3 = LoadClassIdInstr(r0)
    //     0xa66d18: ldur            x3, [x0, #-1]
    //     0xa66d1c: ubfx            x3, x3, #0xc, #0x14
    // 0xa66d20: stp             x1, x0, [SP]
    // 0xa66d24: mov             x0, x3
    // 0xa66d28: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa66d28: sub             lr, x0, #0xb7
    //     0xa66d2c: ldr             lr, [x21, lr, lsl #3]
    //     0xa66d30: blr             lr
    // 0xa66d34: LoadField: r1 = r0->field_2b
    //     0xa66d34: ldur            w1, [x0, #0x2b]
    // 0xa66d38: DecompressPointer r1
    //     0xa66d38: add             x1, x1, HEAP, lsl #32
    // 0xa66d3c: cmp             w1, NULL
    // 0xa66d40: b.ne            #0xa66d4c
    // 0xa66d44: r0 = Null
    //     0xa66d44: mov             x0, NULL
    // 0xa66d48: b               #0xa66d54
    // 0xa66d4c: LoadField: r0 = r1->field_b
    //     0xa66d4c: ldur            w0, [x1, #0xb]
    // 0xa66d50: DecompressPointer r0
    //     0xa66d50: add             x0, x0, HEAP, lsl #32
    // 0xa66d54: cmp             w0, NULL
    // 0xa66d58: b.ne            #0xa66d64
    // 0xa66d5c: r3 = ""
    //     0xa66d5c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa66d60: b               #0xa66d68
    // 0xa66d64: mov             x3, x0
    // 0xa66d68: ldur            x0, [fp, #-0x28]
    // 0xa66d6c: stur            x3, [fp, #-8]
    // 0xa66d70: r1 = Function '<anonymous closure>':.
    //     0xa66d70: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2e8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa66d74: ldr             x1, [x1, #0x2e8]
    // 0xa66d78: r2 = Null
    //     0xa66d78: mov             x2, NULL
    // 0xa66d7c: r0 = AllocateClosure()
    //     0xa66d7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66d80: r1 = Function '<anonymous closure>':.
    //     0xa66d80: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2f0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa66d84: ldr             x1, [x1, #0x2f0]
    // 0xa66d88: r2 = Null
    //     0xa66d88: mov             x2, NULL
    // 0xa66d8c: stur            x0, [fp, #-0x18]
    // 0xa66d90: r0 = AllocateClosure()
    //     0xa66d90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66d94: stur            x0, [fp, #-0x30]
    // 0xa66d98: r0 = CachedNetworkImage()
    //     0xa66d98: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa66d9c: stur            x0, [fp, #-0x38]
    // 0xa66da0: r16 = Instance_BoxFit
    //     0xa66da0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa66da4: ldr             x16, [x16, #0x118]
    // 0xa66da8: ldur            lr, [fp, #-0x18]
    // 0xa66dac: stp             lr, x16, [SP, #8]
    // 0xa66db0: ldur            x16, [fp, #-0x30]
    // 0xa66db4: str             x16, [SP]
    // 0xa66db8: mov             x1, x0
    // 0xa66dbc: ldur            x2, [fp, #-8]
    // 0xa66dc0: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa66dc0: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa66dc4: ldr             x4, [x4, #0x638]
    // 0xa66dc8: r0 = CachedNetworkImage()
    //     0xa66dc8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa66dcc: r0 = ClipRRect()
    //     0xa66dcc: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xa66dd0: mov             x1, x0
    // 0xa66dd4: ldur            x0, [fp, #-0x28]
    // 0xa66dd8: stur            x1, [fp, #-8]
    // 0xa66ddc: StoreField: r1->field_f = r0
    //     0xa66ddc: stur            w0, [x1, #0xf]
    // 0xa66de0: r0 = Instance_Clip
    //     0xa66de0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xa66de4: ldr             x0, [x0, #0x138]
    // 0xa66de8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa66de8: stur            w0, [x1, #0x17]
    // 0xa66dec: ldur            x0, [fp, #-0x38]
    // 0xa66df0: StoreField: r1->field_b = r0
    //     0xa66df0: stur            w0, [x1, #0xb]
    // 0xa66df4: r0 = Container()
    //     0xa66df4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa66df8: stur            x0, [fp, #-0x18]
    // 0xa66dfc: ldur            x16, [fp, #-0x20]
    // 0xa66e00: r30 = Instance_EdgeInsets
    //     0xa66e00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa66e04: ldr             lr, [lr, #0x980]
    // 0xa66e08: stp             lr, x16, [SP, #8]
    // 0xa66e0c: ldur            x16, [fp, #-8]
    // 0xa66e10: str             x16, [SP]
    // 0xa66e14: mov             x1, x0
    // 0xa66e18: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, margin, 0x2, null]
    //     0xa66e18: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "margin", 0x2, Null]
    //     0xa66e1c: ldr             x4, [x4, #0x4e8]
    // 0xa66e20: r0 = Container()
    //     0xa66e20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa66e24: r0 = GestureDetector()
    //     0xa66e24: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa66e28: ldur            x2, [fp, #-0x10]
    // 0xa66e2c: r1 = Function '<anonymous closure>':.
    //     0xa66e2c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a2f8] AnonymousClosure: (0xa66e74), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xa66748)
    //     0xa66e30: ldr             x1, [x1, #0x2f8]
    // 0xa66e34: stur            x0, [fp, #-8]
    // 0xa66e38: r0 = AllocateClosure()
    //     0xa66e38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66e3c: ldur            x16, [fp, #-0x18]
    // 0xa66e40: stp             x16, x0, [SP]
    // 0xa66e44: ldur            x1, [fp, #-8]
    // 0xa66e48: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa66e48: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa66e4c: ldr             x4, [x4, #0xaf0]
    // 0xa66e50: r0 = GestureDetector()
    //     0xa66e50: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa66e54: ldur            x0, [fp, #-8]
    // 0xa66e58: LeaveFrame
    //     0xa66e58: mov             SP, fp
    //     0xa66e5c: ldp             fp, lr, [SP], #0x10
    // 0xa66e60: ret
    //     0xa66e60: ret             
    // 0xa66e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66e64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66e68: b               #0xa66b8c
    // 0xa66e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa66e6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa66e70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa66e70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66e74, size: 0xd4
    // 0xa66e74: EnterFrame
    //     0xa66e74: stp             fp, lr, [SP, #-0x10]!
    //     0xa66e78: mov             fp, SP
    // 0xa66e7c: AllocStack(0x10)
    //     0xa66e7c: sub             SP, SP, #0x10
    // 0xa66e80: SetupParameters()
    //     0xa66e80: ldr             x0, [fp, #0x10]
    //     0xa66e84: ldur            w4, [x0, #0x17]
    //     0xa66e88: add             x4, x4, HEAP, lsl #32
    //     0xa66e8c: stur            x4, [fp, #-0x10]
    // 0xa66e90: CheckStackOverflow
    //     0xa66e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66e94: cmp             SP, x16
    //     0xa66e98: b.ls            #0xa66f34
    // 0xa66e9c: LoadField: r0 = r4->field_b
    //     0xa66e9c: ldur            w0, [x4, #0xb]
    // 0xa66ea0: DecompressPointer r0
    //     0xa66ea0: add             x0, x0, HEAP, lsl #32
    // 0xa66ea4: stur            x0, [fp, #-8]
    // 0xa66ea8: LoadField: r1 = r0->field_f
    //     0xa66ea8: ldur            w1, [x0, #0xf]
    // 0xa66eac: DecompressPointer r1
    //     0xa66eac: add             x1, x1, HEAP, lsl #32
    // 0xa66eb0: LoadField: r2 = r4->field_f
    //     0xa66eb0: ldur            w2, [x4, #0xf]
    // 0xa66eb4: DecompressPointer r2
    //     0xa66eb4: add             x2, x2, HEAP, lsl #32
    // 0xa66eb8: r3 = LoadInt32Instr(r2)
    //     0xa66eb8: sbfx            x3, x2, #1, #0x1f
    //     0xa66ebc: tbz             w2, #0, #0xa66ec4
    //     0xa66ec0: ldur            x3, [x2, #7]
    // 0xa66ec4: StoreField: r1->field_13 = r3
    //     0xa66ec4: stur            x3, [x1, #0x13]
    // 0xa66ec8: LoadField: r2 = r1->field_1b
    //     0xa66ec8: ldur            w2, [x1, #0x1b]
    // 0xa66ecc: DecompressPointer r2
    //     0xa66ecc: add             x2, x2, HEAP, lsl #32
    // 0xa66ed0: r16 = Sentinel
    //     0xa66ed0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa66ed4: cmp             w2, w16
    // 0xa66ed8: b.eq            #0xa66f3c
    // 0xa66edc: mov             x1, x2
    // 0xa66ee0: mov             x2, x3
    // 0xa66ee4: r3 = Instance_Cubic
    //     0xa66ee4: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0xa66ee8: ldr             x3, [x3, #0x2b0]
    // 0xa66eec: r5 = Instance_Duration
    //     0xa66eec: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0xa66ef0: ldr             x5, [x5, #0xf00]
    // 0xa66ef4: r0 = animateToPage()
    //     0xa66ef4: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0xa66ef8: ldur            x0, [fp, #-8]
    // 0xa66efc: LoadField: r1 = r0->field_f
    //     0xa66efc: ldur            w1, [x0, #0xf]
    // 0xa66f00: DecompressPointer r1
    //     0xa66f00: add             x1, x1, HEAP, lsl #32
    // 0xa66f04: ldur            x0, [fp, #-0x10]
    // 0xa66f08: LoadField: r2 = r0->field_f
    //     0xa66f08: ldur            w2, [x0, #0xf]
    // 0xa66f0c: DecompressPointer r2
    //     0xa66f0c: add             x2, x2, HEAP, lsl #32
    // 0xa66f10: r0 = LoadInt32Instr(r2)
    //     0xa66f10: sbfx            x0, x2, #1, #0x1f
    //     0xa66f14: tbz             w2, #0, #0xa66f1c
    //     0xa66f18: ldur            x0, [x2, #7]
    // 0xa66f1c: mov             x2, x0
    // 0xa66f20: r0 = _onImageTapped()
    //     0xa66f20: bl              #0xa66f48  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xa66f24: r0 = Null
    //     0xa66f24: mov             x0, NULL
    // 0xa66f28: LeaveFrame
    //     0xa66f28: mov             SP, fp
    //     0xa66f2c: ldp             fp, lr, [SP], #0x10
    // 0xa66f30: ret
    //     0xa66f30: ret             
    // 0xa66f34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66f34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66f38: b               #0xa66e9c
    // 0xa66f3c: r9 = _pageController
    //     0xa66f3c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5a2e0] Field <_TestimonialMoreImagesWidgetState@1341381811._pageController@1341381811>: late (offset: 0x1c)
    //     0xa66f40: ldr             x9, [x9, #0x2e0]
    // 0xa66f44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa66f44: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _onImageTapped(/* No info */) {
    // ** addr: 0xa66f48, size: 0x80
    // 0xa66f48: EnterFrame
    //     0xa66f48: stp             fp, lr, [SP, #-0x10]!
    //     0xa66f4c: mov             fp, SP
    // 0xa66f50: AllocStack(0x10)
    //     0xa66f50: sub             SP, SP, #0x10
    // 0xa66f54: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa66f54: stur            x1, [fp, #-8]
    //     0xa66f58: stur            x2, [fp, #-0x10]
    // 0xa66f5c: CheckStackOverflow
    //     0xa66f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66f60: cmp             SP, x16
    //     0xa66f64: b.ls            #0xa66fc0
    // 0xa66f68: r1 = 2
    //     0xa66f68: movz            x1, #0x2
    // 0xa66f6c: r0 = AllocateContext()
    //     0xa66f6c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa66f70: mov             x2, x0
    // 0xa66f74: ldur            x3, [fp, #-8]
    // 0xa66f78: StoreField: r2->field_f = r3
    //     0xa66f78: stur            w3, [x2, #0xf]
    // 0xa66f7c: ldur            x4, [fp, #-0x10]
    // 0xa66f80: r0 = BoxInt64Instr(r4)
    //     0xa66f80: sbfiz           x0, x4, #1, #0x1f
    //     0xa66f84: cmp             x4, x0, asr #1
    //     0xa66f88: b.eq            #0xa66f94
    //     0xa66f8c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa66f90: stur            x4, [x0, #7]
    // 0xa66f94: StoreField: r2->field_13 = r0
    //     0xa66f94: stur            w0, [x2, #0x13]
    // 0xa66f98: r1 = Function '<anonymous closure>':.
    //     0xa66f98: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a300] AnonymousClosure: (0xa66fc8), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped (0xa66ffc)
    //     0xa66f9c: ldr             x1, [x1, #0x300]
    // 0xa66fa0: r0 = AllocateClosure()
    //     0xa66fa0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66fa4: ldur            x1, [fp, #-8]
    // 0xa66fa8: mov             x2, x0
    // 0xa66fac: r0 = setState()
    //     0xa66fac: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa66fb0: r0 = Null
    //     0xa66fb0: mov             x0, NULL
    // 0xa66fb4: LeaveFrame
    //     0xa66fb4: mov             SP, fp
    //     0xa66fb8: ldp             fp, lr, [SP], #0x10
    // 0xa66fbc: ret
    //     0xa66fbc: ret             
    // 0xa66fc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66fc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66fc4: b               #0xa66f68
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa6707c, size: 0x1c8
    // 0xa6707c: EnterFrame
    //     0xa6707c: stp             fp, lr, [SP, #-0x10]!
    //     0xa67080: mov             fp, SP
    // 0xa67084: AllocStack(0x38)
    //     0xa67084: sub             SP, SP, #0x38
    // 0xa67088: SetupParameters()
    //     0xa67088: ldr             x0, [fp, #0x20]
    //     0xa6708c: ldur            w1, [x0, #0x17]
    //     0xa67090: add             x1, x1, HEAP, lsl #32
    // 0xa67094: CheckStackOverflow
    //     0xa67094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa67098: cmp             SP, x16
    //     0xa6709c: b.ls            #0xa67234
    // 0xa670a0: LoadField: r0 = r1->field_f
    //     0xa670a0: ldur            w0, [x1, #0xf]
    // 0xa670a4: DecompressPointer r0
    //     0xa670a4: add             x0, x0, HEAP, lsl #32
    // 0xa670a8: LoadField: r2 = r0->field_b
    //     0xa670a8: ldur            w2, [x0, #0xb]
    // 0xa670ac: DecompressPointer r2
    //     0xa670ac: add             x2, x2, HEAP, lsl #32
    // 0xa670b0: cmp             w2, NULL
    // 0xa670b4: b.eq            #0xa6723c
    // 0xa670b8: LoadField: r3 = r2->field_b
    //     0xa670b8: ldur            w3, [x2, #0xb]
    // 0xa670bc: DecompressPointer r3
    //     0xa670bc: add             x3, x3, HEAP, lsl #32
    // 0xa670c0: cmp             w3, NULL
    // 0xa670c4: b.ne            #0xa670d4
    // 0xa670c8: ldr             x4, [fp, #0x10]
    // 0xa670cc: r0 = Null
    //     0xa670cc: mov             x0, NULL
    // 0xa670d0: b               #0xa67134
    // 0xa670d4: ldr             x4, [fp, #0x10]
    // 0xa670d8: LoadField: r0 = r3->field_b
    //     0xa670d8: ldur            w0, [x3, #0xb]
    // 0xa670dc: r5 = LoadInt32Instr(r4)
    //     0xa670dc: sbfx            x5, x4, #1, #0x1f
    //     0xa670e0: tbz             w4, #0, #0xa670e8
    //     0xa670e4: ldur            x5, [x4, #7]
    // 0xa670e8: r1 = LoadInt32Instr(r0)
    //     0xa670e8: sbfx            x1, x0, #1, #0x1f
    // 0xa670ec: mov             x0, x1
    // 0xa670f0: mov             x1, x5
    // 0xa670f4: cmp             x1, x0
    // 0xa670f8: b.hs            #0xa67240
    // 0xa670fc: LoadField: r0 = r3->field_f
    //     0xa670fc: ldur            w0, [x3, #0xf]
    // 0xa67100: DecompressPointer r0
    //     0xa67100: add             x0, x0, HEAP, lsl #32
    // 0xa67104: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa67104: add             x16, x0, x5, lsl #2
    //     0xa67108: ldur            w1, [x16, #0xf]
    // 0xa6710c: DecompressPointer r1
    //     0xa6710c: add             x1, x1, HEAP, lsl #32
    // 0xa67110: LoadField: r0 = r1->field_7
    //     0xa67110: ldur            w0, [x1, #7]
    // 0xa67114: DecompressPointer r0
    //     0xa67114: add             x0, x0, HEAP, lsl #32
    // 0xa67118: cmp             w0, NULL
    // 0xa6711c: b.ne            #0xa67128
    // 0xa67120: r0 = Null
    //     0xa67120: mov             x0, NULL
    // 0xa67124: b               #0xa67134
    // 0xa67128: LoadField: r1 = r0->field_b
    //     0xa67128: ldur            w1, [x0, #0xb]
    // 0xa6712c: DecompressPointer r1
    //     0xa6712c: add             x1, x1, HEAP, lsl #32
    // 0xa67130: mov             x0, x1
    // 0xa67134: cmp             w0, NULL
    // 0xa67138: b.ne            #0xa67190
    // 0xa6713c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa6713c: ldur            w0, [x2, #0x17]
    // 0xa67140: DecompressPointer r0
    //     0xa67140: add             x0, x0, HEAP, lsl #32
    // 0xa67144: cmp             w0, NULL
    // 0xa67148: b.ne            #0xa67154
    // 0xa6714c: r0 = Null
    //     0xa6714c: mov             x0, NULL
    // 0xa67150: b               #0xa67190
    // 0xa67154: r1 = LoadClassIdInstr(r0)
    //     0xa67154: ldur            x1, [x0, #-1]
    //     0xa67158: ubfx            x1, x1, #0xc, #0x14
    // 0xa6715c: stp             x4, x0, [SP]
    // 0xa67160: mov             x0, x1
    // 0xa67164: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa67164: sub             lr, x0, #0xb7
    //     0xa67168: ldr             lr, [x21, lr, lsl #3]
    //     0xa6716c: blr             lr
    // 0xa67170: LoadField: r1 = r0->field_2b
    //     0xa67170: ldur            w1, [x0, #0x2b]
    // 0xa67174: DecompressPointer r1
    //     0xa67174: add             x1, x1, HEAP, lsl #32
    // 0xa67178: cmp             w1, NULL
    // 0xa6717c: b.ne            #0xa67188
    // 0xa67180: r0 = Null
    //     0xa67180: mov             x0, NULL
    // 0xa67184: b               #0xa67190
    // 0xa67188: LoadField: r0 = r1->field_b
    //     0xa67188: ldur            w0, [x1, #0xb]
    // 0xa6718c: DecompressPointer r0
    //     0xa6718c: add             x0, x0, HEAP, lsl #32
    // 0xa67190: cmp             w0, NULL
    // 0xa67194: b.ne            #0xa6719c
    // 0xa67198: r0 = ""
    //     0xa67198: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa6719c: stur            x0, [fp, #-8]
    // 0xa671a0: r1 = Function '<anonymous closure>':.
    //     0xa671a0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a308] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa671a4: ldr             x1, [x1, #0x308]
    // 0xa671a8: r2 = Null
    //     0xa671a8: mov             x2, NULL
    // 0xa671ac: r0 = AllocateClosure()
    //     0xa671ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa671b0: r1 = Function '<anonymous closure>':.
    //     0xa671b0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a310] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa671b4: ldr             x1, [x1, #0x310]
    // 0xa671b8: r2 = Null
    //     0xa671b8: mov             x2, NULL
    // 0xa671bc: stur            x0, [fp, #-0x10]
    // 0xa671c0: r0 = AllocateClosure()
    //     0xa671c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa671c4: stur            x0, [fp, #-0x18]
    // 0xa671c8: r0 = CachedNetworkImage()
    //     0xa671c8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa671cc: stur            x0, [fp, #-0x20]
    // 0xa671d0: r16 = inf
    //     0xa671d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa671d4: ldr             x16, [x16, #0x9f8]
    // 0xa671d8: ldur            lr, [fp, #-0x10]
    // 0xa671dc: stp             lr, x16, [SP, #8]
    // 0xa671e0: ldur            x16, [fp, #-0x18]
    // 0xa671e4: str             x16, [SP]
    // 0xa671e8: mov             x1, x0
    // 0xa671ec: ldur            x2, [fp, #-8]
    // 0xa671f0: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, progressIndicatorBuilder, 0x3, width, 0x2, null]
    //     0xa671f0: add             x4, PP, #0x55, lsl #12  ; [pp+0x55b20] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "progressIndicatorBuilder", 0x3, "width", 0x2, Null]
    //     0xa671f4: ldr             x4, [x4, #0xb20]
    // 0xa671f8: r0 = CachedNetworkImage()
    //     0xa671f8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa671fc: r0 = Container()
    //     0xa671fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa67200: stur            x0, [fp, #-8]
    // 0xa67204: r16 = Instance_EdgeInsets
    //     0xa67204: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xa67208: ldr             x16, [x16, #0x980]
    // 0xa6720c: ldur            lr, [fp, #-0x20]
    // 0xa67210: stp             lr, x16, [SP]
    // 0xa67214: mov             x1, x0
    // 0xa67218: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xa67218: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xa6721c: ldr             x4, [x4, #0x30]
    // 0xa67220: r0 = Container()
    //     0xa67220: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa67224: ldur            x0, [fp, #-8]
    // 0xa67228: LeaveFrame
    //     0xa67228: mov             SP, fp
    //     0xa6722c: ldp             fp, lr, [SP], #0x10
    // 0xa67230: ret
    //     0xa67230: ret             
    // 0xa67234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa67234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa67238: b               #0xa670a0
    // 0xa6723c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6723c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa67240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa67240: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa67244, size: 0x5c
    // 0xa67244: EnterFrame
    //     0xa67244: stp             fp, lr, [SP, #-0x10]!
    //     0xa67248: mov             fp, SP
    // 0xa6724c: ldr             x0, [fp, #0x18]
    // 0xa67250: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa67250: ldur            w1, [x0, #0x17]
    // 0xa67254: DecompressPointer r1
    //     0xa67254: add             x1, x1, HEAP, lsl #32
    // 0xa67258: CheckStackOverflow
    //     0xa67258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6725c: cmp             SP, x16
    //     0xa67260: b.ls            #0xa67298
    // 0xa67264: LoadField: r0 = r1->field_f
    //     0xa67264: ldur            w0, [x1, #0xf]
    // 0xa67268: DecompressPointer r0
    //     0xa67268: add             x0, x0, HEAP, lsl #32
    // 0xa6726c: ldr             x1, [fp, #0x10]
    // 0xa67270: r2 = LoadInt32Instr(r1)
    //     0xa67270: sbfx            x2, x1, #1, #0x1f
    //     0xa67274: tbz             w1, #0, #0xa6727c
    //     0xa67278: ldur            x2, [x1, #7]
    // 0xa6727c: StoreField: r0->field_13 = r2
    //     0xa6727c: stur            x2, [x0, #0x13]
    // 0xa67280: mov             x1, x0
    // 0xa67284: r0 = _onImageTapped()
    //     0xa67284: bl              #0xa66f48  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xa67288: r0 = Null
    //     0xa67288: mov             x0, NULL
    // 0xa6728c: LeaveFrame
    //     0xa6728c: mov             SP, fp
    //     0xa67290: ldp             fp, lr, [SP], #0x10
    // 0xa67294: ret
    //     0xa67294: ret             
    // 0xa67298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa67298: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6729c: b               #0xa67264
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc86df4, size: 0x54
    // 0xc86df4: EnterFrame
    //     0xc86df4: stp             fp, lr, [SP, #-0x10]!
    //     0xc86df8: mov             fp, SP
    // 0xc86dfc: CheckStackOverflow
    //     0xc86dfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc86e00: cmp             SP, x16
    //     0xc86e04: b.ls            #0xc86e34
    // 0xc86e08: LoadField: r0 = r1->field_1b
    //     0xc86e08: ldur            w0, [x1, #0x1b]
    // 0xc86e0c: DecompressPointer r0
    //     0xc86e0c: add             x0, x0, HEAP, lsl #32
    // 0xc86e10: r16 = Sentinel
    //     0xc86e10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc86e14: cmp             w0, w16
    // 0xc86e18: b.eq            #0xc86e3c
    // 0xc86e1c: mov             x1, x0
    // 0xc86e20: r0 = dispose()
    //     0xc86e20: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc86e24: r0 = Null
    //     0xc86e24: mov             x0, NULL
    // 0xc86e28: LeaveFrame
    //     0xc86e28: mov             SP, fp
    //     0xc86e2c: ldp             fp, lr, [SP], #0x10
    // 0xc86e30: ret
    //     0xc86e30: ret             
    // 0xc86e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc86e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc86e38: b               #0xc86e08
    // 0xc86e3c: r9 = _pageController
    //     0xc86e3c: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5a2e0] Field <_TestimonialMoreImagesWidgetState@1341381811._pageController@1341381811>: late (offset: 0x1c)
    //     0xc86e40: ldr             x9, [x9, #0x2e0]
    // 0xc86e44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc86e44: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4239, size: 0x1c, field offset: 0xc
//   const constructor, 
class TestimonialMoreImagesWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7bddc, size: 0x30
    // 0xc7bddc: EnterFrame
    //     0xc7bddc: stp             fp, lr, [SP, #-0x10]!
    //     0xc7bde0: mov             fp, SP
    // 0xc7bde4: mov             x0, x1
    // 0xc7bde8: r1 = <TestimonialMoreImagesWidget>
    //     0xc7bde8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48f90] TypeArguments: <TestimonialMoreImagesWidget>
    //     0xc7bdec: ldr             x1, [x1, #0xf90]
    // 0xc7bdf0: r0 = _TestimonialMoreImagesWidgetState()
    //     0xc7bdf0: bl              #0xc7be0c  ; Allocate_TestimonialMoreImagesWidgetStateStub -> _TestimonialMoreImagesWidgetState (size=0x20)
    // 0xc7bdf4: StoreField: r0->field_13 = rZR
    //     0xc7bdf4: stur            xzr, [x0, #0x13]
    // 0xc7bdf8: r1 = Sentinel
    //     0xc7bdf8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7bdfc: StoreField: r0->field_1b = r1
    //     0xc7bdfc: stur            w1, [x0, #0x1b]
    // 0xc7be00: LeaveFrame
    //     0xc7be00: mov             SP, fp
    //     0xc7be04: ldp             fp, lr, [SP], #0x10
    // 0xc7be08: ret
    //     0xc7be08: ret             
  }
}
