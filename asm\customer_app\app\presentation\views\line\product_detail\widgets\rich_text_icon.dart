// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/rich_text_icon.dart

// class id: 1049569, size: 0x8
class :: {
}

// class id: 4481, size: 0x14, field offset: 0xc
//   const constructor, 
class RichTextIcon extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x12992bc, size: 0x154
    // 0x12992bc: EnterFrame
    //     0x12992bc: stp             fp, lr, [SP, #-0x10]!
    //     0x12992c0: mov             fp, SP
    // 0x12992c4: AllocStack(0x28)
    //     0x12992c4: sub             SP, SP, #0x28
    // 0x12992c8: SetupParameters(RichTextIcon this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x12992c8: mov             x0, x1
    //     0x12992cc: mov             x1, x2
    //     0x12992d0: stur            x2, [fp, #-0x10]
    // 0x12992d4: CheckStackOverflow
    //     0x12992d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12992d8: cmp             SP, x16
    //     0x12992dc: b.ls            #0x1299408
    // 0x12992e0: LoadField: r2 = r0->field_b
    //     0x12992e0: ldur            w2, [x0, #0xb]
    // 0x12992e4: DecompressPointer r2
    //     0x12992e4: add             x2, x2, HEAP, lsl #32
    // 0x12992e8: stur            x2, [fp, #-8]
    // 0x12992ec: r0 = WidgetSpan()
    //     0x12992ec: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0x12992f0: mov             x2, x0
    // 0x12992f4: ldur            x0, [fp, #-8]
    // 0x12992f8: stur            x2, [fp, #-0x18]
    // 0x12992fc: StoreField: r2->field_13 = r0
    //     0x12992fc: stur            w0, [x2, #0x13]
    // 0x1299300: r0 = Instance_PlaceholderAlignment
    //     0x1299300: add             x0, PP, #0x46, lsl #12  ; [pp+0x46930] Obj!PlaceholderAlignment@d76421
    //     0x1299304: ldr             x0, [x0, #0x930]
    // 0x1299308: StoreField: r2->field_b = r0
    //     0x1299308: stur            w0, [x2, #0xb]
    // 0x129930c: ldur            x1, [fp, #-0x10]
    // 0x1299310: r0 = of()
    //     0x1299310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1299314: LoadField: r1 = r0->field_87
    //     0x1299314: ldur            w1, [x0, #0x87]
    // 0x1299318: DecompressPointer r1
    //     0x1299318: add             x1, x1, HEAP, lsl #32
    // 0x129931c: LoadField: r0 = r1->field_7
    //     0x129931c: ldur            w0, [x1, #7]
    // 0x1299320: DecompressPointer r0
    //     0x1299320: add             x0, x0, HEAP, lsl #32
    // 0x1299324: stur            x0, [fp, #-8]
    // 0x1299328: r1 = Instance_Color
    //     0x1299328: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x129932c: d0 = 0.700000
    //     0x129932c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1299330: ldr             d0, [x17, #0xf48]
    // 0x1299334: r0 = withOpacity()
    //     0x1299334: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1299338: r16 = 14.000000
    //     0x1299338: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x129933c: ldr             x16, [x16, #0x1d8]
    // 0x1299340: stp             x0, x16, [SP]
    // 0x1299344: ldur            x1, [fp, #-8]
    // 0x1299348: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1299348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x129934c: ldr             x4, [x4, #0xaa0]
    // 0x1299350: r0 = copyWith()
    //     0x1299350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1299354: stur            x0, [fp, #-8]
    // 0x1299358: r0 = TextSpan()
    //     0x1299358: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x129935c: mov             x3, x0
    // 0x1299360: r0 = ""
    //     0x1299360: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1299364: stur            x3, [fp, #-0x10]
    // 0x1299368: StoreField: r3->field_b = r0
    //     0x1299368: stur            w0, [x3, #0xb]
    // 0x129936c: r0 = Instance__DeferringMouseCursor
    //     0x129936c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1299370: ArrayStore: r3[0] = r0  ; List_4
    //     0x1299370: stur            w0, [x3, #0x17]
    // 0x1299374: ldur            x1, [fp, #-8]
    // 0x1299378: StoreField: r3->field_7 = r1
    //     0x1299378: stur            w1, [x3, #7]
    // 0x129937c: r1 = Null
    //     0x129937c: mov             x1, NULL
    // 0x1299380: r2 = 4
    //     0x1299380: movz            x2, #0x4
    // 0x1299384: r0 = AllocateArray()
    //     0x1299384: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1299388: mov             x2, x0
    // 0x129938c: ldur            x0, [fp, #-0x18]
    // 0x1299390: stur            x2, [fp, #-8]
    // 0x1299394: StoreField: r2->field_f = r0
    //     0x1299394: stur            w0, [x2, #0xf]
    // 0x1299398: ldur            x0, [fp, #-0x10]
    // 0x129939c: StoreField: r2->field_13 = r0
    //     0x129939c: stur            w0, [x2, #0x13]
    // 0x12993a0: r1 = <InlineSpan>
    //     0x12993a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x12993a4: ldr             x1, [x1, #0xe40]
    // 0x12993a8: r0 = AllocateGrowableArray()
    //     0x12993a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12993ac: mov             x1, x0
    // 0x12993b0: ldur            x0, [fp, #-8]
    // 0x12993b4: stur            x1, [fp, #-0x10]
    // 0x12993b8: StoreField: r1->field_f = r0
    //     0x12993b8: stur            w0, [x1, #0xf]
    // 0x12993bc: r0 = 4
    //     0x12993bc: movz            x0, #0x4
    // 0x12993c0: StoreField: r1->field_b = r0
    //     0x12993c0: stur            w0, [x1, #0xb]
    // 0x12993c4: r0 = TextSpan()
    //     0x12993c4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x12993c8: mov             x1, x0
    // 0x12993cc: ldur            x0, [fp, #-0x10]
    // 0x12993d0: stur            x1, [fp, #-8]
    // 0x12993d4: StoreField: r1->field_f = r0
    //     0x12993d4: stur            w0, [x1, #0xf]
    // 0x12993d8: r0 = Instance__DeferringMouseCursor
    //     0x12993d8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x12993dc: ArrayStore: r1[0] = r0  ; List_4
    //     0x12993dc: stur            w0, [x1, #0x17]
    // 0x12993e0: r0 = RichText()
    //     0x12993e0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x12993e4: mov             x1, x0
    // 0x12993e8: ldur            x2, [fp, #-8]
    // 0x12993ec: stur            x0, [fp, #-8]
    // 0x12993f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12993f0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12993f4: r0 = RichText()
    //     0x12993f4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x12993f8: ldur            x0, [fp, #-8]
    // 0x12993fc: LeaveFrame
    //     0x12993fc: mov             SP, fp
    //     0x1299400: ldp             fp, lr, [SP], #0x10
    // 0x1299404: ret
    //     0x1299404: ret             
    // 0x1299408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1299408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129940c: b               #0x12992e0
  }
}
