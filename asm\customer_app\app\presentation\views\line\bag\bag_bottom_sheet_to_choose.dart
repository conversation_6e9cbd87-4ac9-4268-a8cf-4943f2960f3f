// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_bottom_sheet_to_choose.dart

// class id: 1049461, size: 0x8
class :: {
}

// class id: 3296, size: 0x18, field offset: 0x14
class _BagBottomSheetToChooseState extends State<dynamic> {

  late BagImage? image; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x94503c, size: 0xe8
    // 0x94503c: EnterFrame
    //     0x94503c: stp             fp, lr, [SP, #-0x10]!
    //     0x945040: mov             fp, SP
    // 0x945044: AllocStack(0x20)
    //     0x945044: sub             SP, SP, #0x20
    // 0x945048: SetupParameters(_BagBottomSheetToChooseState this /* r1 => r0, fp-0x10 */)
    //     0x945048: mov             x0, x1
    //     0x94504c: stur            x1, [fp, #-0x10]
    // 0x945050: CheckStackOverflow
    //     0x945050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945054: cmp             SP, x16
    //     0x945058: b.ls            #0x945118
    // 0x94505c: LoadField: r1 = r0->field_b
    //     0x94505c: ldur            w1, [x0, #0xb]
    // 0x945060: DecompressPointer r1
    //     0x945060: add             x1, x1, HEAP, lsl #32
    // 0x945064: cmp             w1, NULL
    // 0x945068: b.eq            #0x945120
    // 0x94506c: LoadField: r2 = r1->field_13
    //     0x94506c: ldur            w2, [x1, #0x13]
    // 0x945070: DecompressPointer r2
    //     0x945070: add             x2, x2, HEAP, lsl #32
    // 0x945074: cmp             w2, NULL
    // 0x945078: b.ne            #0x945088
    // 0x94507c: mov             x1, x0
    // 0x945080: r0 = Null
    //     0x945080: mov             x0, NULL
    // 0x945084: b               #0x9450ec
    // 0x945088: LoadField: r3 = r2->field_1b
    //     0x945088: ldur            w3, [x2, #0x1b]
    // 0x94508c: DecompressPointer r3
    //     0x94508c: add             x3, x3, HEAP, lsl #32
    // 0x945090: stur            x3, [fp, #-8]
    // 0x945094: cmp             w3, NULL
    // 0x945098: b.ne            #0x9450a4
    // 0x94509c: r1 = Null
    //     0x94509c: mov             x1, NULL
    // 0x9450a0: b               #0x9450e4
    // 0x9450a4: r1 = Function '<anonymous closure>':.
    //     0x9450a4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d88] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x9450a8: ldr             x1, [x1, #0xd88]
    // 0x9450ac: r2 = Null
    //     0x9450ac: mov             x2, NULL
    // 0x9450b0: r0 = AllocateClosure()
    //     0x9450b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9450b4: r1 = Function '<anonymous closure>':.
    //     0x9450b4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d90] AnonymousClosure: (0x8fb1b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem (0x8fb1d0)
    //     0x9450b8: ldr             x1, [x1, #0xd90]
    // 0x9450bc: r2 = Null
    //     0x9450bc: mov             x2, NULL
    // 0x9450c0: stur            x0, [fp, #-0x18]
    // 0x9450c4: r0 = AllocateClosure()
    //     0x9450c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9450c8: str             x0, [SP]
    // 0x9450cc: ldur            x1, [fp, #-8]
    // 0x9450d0: ldur            x2, [fp, #-0x18]
    // 0x9450d4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x9450d4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x9450d8: ldr             x4, [x4, #0xb48]
    // 0x9450dc: r0 = firstWhere()
    //     0x9450dc: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x9450e0: mov             x1, x0
    // 0x9450e4: mov             x0, x1
    // 0x9450e8: ldur            x1, [fp, #-0x10]
    // 0x9450ec: StoreField: r1->field_13 = r0
    //     0x9450ec: stur            w0, [x1, #0x13]
    //     0x9450f0: ldurb           w16, [x1, #-1]
    //     0x9450f4: ldurb           w17, [x0, #-1]
    //     0x9450f8: and             x16, x17, x16, lsr #2
    //     0x9450fc: tst             x16, HEAP, lsr #32
    //     0x945100: b.eq            #0x945108
    //     0x945104: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x945108: r0 = Null
    //     0x945108: mov             x0, NULL
    // 0x94510c: LeaveFrame
    //     0x94510c: mov             SP, fp
    //     0x945110: ldp             fp, lr, [SP], #0x10
    // 0x945114: ret
    //     0x945114: ret             
    // 0x945118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945118: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94511c: b               #0x94505c
    // 0x945120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945120: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb9cd30, size: 0xc70
    // 0xb9cd30: EnterFrame
    //     0xb9cd30: stp             fp, lr, [SP, #-0x10]!
    //     0xb9cd34: mov             fp, SP
    // 0xb9cd38: AllocStack(0x80)
    //     0xb9cd38: sub             SP, SP, #0x80
    // 0xb9cd3c: SetupParameters(_BagBottomSheetToChooseState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb9cd3c: mov             x0, x1
    //     0xb9cd40: stur            x1, [fp, #-8]
    //     0xb9cd44: mov             x1, x2
    //     0xb9cd48: stur            x2, [fp, #-0x10]
    // 0xb9cd4c: CheckStackOverflow
    //     0xb9cd4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9cd50: cmp             SP, x16
    //     0xb9cd54: b.ls            #0xb9d978
    // 0xb9cd58: r1 = 1
    //     0xb9cd58: movz            x1, #0x1
    // 0xb9cd5c: r0 = AllocateContext()
    //     0xb9cd5c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9cd60: mov             x1, x0
    // 0xb9cd64: ldur            x0, [fp, #-8]
    // 0xb9cd68: stur            x1, [fp, #-0x18]
    // 0xb9cd6c: StoreField: r1->field_f = r0
    //     0xb9cd6c: stur            w0, [x1, #0xf]
    // 0xb9cd70: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb9cd70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9cd74: ldr             x0, [x0, #0x1c80]
    //     0xb9cd78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9cd7c: cmp             w0, w16
    //     0xb9cd80: b.ne            #0xb9cd8c
    //     0xb9cd84: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb9cd88: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb9cd8c: r0 = GetNavigation.size()
    //     0xb9cd8c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb9cd90: LoadField: d0 = r0->field_7
    //     0xb9cd90: ldur            d0, [x0, #7]
    // 0xb9cd94: d1 = 0.880000
    //     0xb9cd94: add             x17, PP, #0x52, lsl #12  ; [pp+0x528e0] IMM: double(0.88) from 0x3fec28f5c28f5c29
    //     0xb9cd98: ldr             d1, [x17, #0x8e0]
    // 0xb9cd9c: fmul            d2, d0, d1
    // 0xb9cda0: stur            d2, [fp, #-0x50]
    // 0xb9cda4: r0 = BoxConstraints()
    //     0xb9cda4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb9cda8: stur            x0, [fp, #-0x20]
    // 0xb9cdac: StoreField: r0->field_7 = rZR
    //     0xb9cdac: stur            xzr, [x0, #7]
    // 0xb9cdb0: ldur            d0, [fp, #-0x50]
    // 0xb9cdb4: StoreField: r0->field_f = d0
    //     0xb9cdb4: stur            d0, [x0, #0xf]
    // 0xb9cdb8: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb9cdb8: stur            xzr, [x0, #0x17]
    // 0xb9cdbc: d0 = inf
    //     0xb9cdbc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb9cdc0: StoreField: r0->field_1f = d0
    //     0xb9cdc0: stur            d0, [x0, #0x1f]
    // 0xb9cdc4: ldur            x1, [fp, #-0x10]
    // 0xb9cdc8: r0 = of()
    //     0xb9cdc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9cdcc: LoadField: r1 = r0->field_87
    //     0xb9cdcc: ldur            w1, [x0, #0x87]
    // 0xb9cdd0: DecompressPointer r1
    //     0xb9cdd0: add             x1, x1, HEAP, lsl #32
    // 0xb9cdd4: LoadField: r0 = r1->field_7
    //     0xb9cdd4: ldur            w0, [x1, #7]
    // 0xb9cdd8: DecompressPointer r0
    //     0xb9cdd8: add             x0, x0, HEAP, lsl #32
    // 0xb9cddc: r16 = 14.000000
    //     0xb9cddc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9cde0: ldr             x16, [x16, #0x1d8]
    // 0xb9cde4: r30 = Instance_Color
    //     0xb9cde4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9cde8: stp             lr, x16, [SP]
    // 0xb9cdec: mov             x1, x0
    // 0xb9cdf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9cdf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9cdf4: ldr             x4, [x4, #0xaa0]
    // 0xb9cdf8: r0 = copyWith()
    //     0xb9cdf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9cdfc: stur            x0, [fp, #-0x28]
    // 0xb9ce00: r0 = Text()
    //     0xb9ce00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9ce04: mov             x1, x0
    // 0xb9ce08: r0 = "Would you like to personalise your product\?"
    //     0xb9ce08: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d18] "Would you like to personalise your product\?"
    //     0xb9ce0c: ldr             x0, [x0, #0xd18]
    // 0xb9ce10: stur            x1, [fp, #-0x30]
    // 0xb9ce14: StoreField: r1->field_b = r0
    //     0xb9ce14: stur            w0, [x1, #0xb]
    // 0xb9ce18: ldur            x0, [fp, #-0x28]
    // 0xb9ce1c: StoreField: r1->field_13 = r0
    //     0xb9ce1c: stur            w0, [x1, #0x13]
    // 0xb9ce20: r0 = ConstrainedBox()
    //     0xb9ce20: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb9ce24: mov             x1, x0
    // 0xb9ce28: ldur            x0, [fp, #-0x20]
    // 0xb9ce2c: stur            x1, [fp, #-0x28]
    // 0xb9ce30: StoreField: r1->field_f = r0
    //     0xb9ce30: stur            w0, [x1, #0xf]
    // 0xb9ce34: ldur            x0, [fp, #-0x30]
    // 0xb9ce38: StoreField: r1->field_b = r0
    //     0xb9ce38: stur            w0, [x1, #0xb]
    // 0xb9ce3c: r0 = InkWell()
    //     0xb9ce3c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb9ce40: mov             x3, x0
    // 0xb9ce44: r0 = Instance_Icon
    //     0xb9ce44: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb9ce48: ldr             x0, [x0, #0x2b8]
    // 0xb9ce4c: stur            x3, [fp, #-0x20]
    // 0xb9ce50: StoreField: r3->field_b = r0
    //     0xb9ce50: stur            w0, [x3, #0xb]
    // 0xb9ce54: r1 = Function '<anonymous closure>':.
    //     0xb9ce54: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d20] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9ce58: ldr             x1, [x1, #0xd20]
    // 0xb9ce5c: r2 = Null
    //     0xb9ce5c: mov             x2, NULL
    // 0xb9ce60: r0 = AllocateClosure()
    //     0xb9ce60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9ce64: mov             x1, x0
    // 0xb9ce68: ldur            x0, [fp, #-0x20]
    // 0xb9ce6c: StoreField: r0->field_f = r1
    //     0xb9ce6c: stur            w1, [x0, #0xf]
    // 0xb9ce70: r3 = true
    //     0xb9ce70: add             x3, NULL, #0x20  ; true
    // 0xb9ce74: StoreField: r0->field_43 = r3
    //     0xb9ce74: stur            w3, [x0, #0x43]
    // 0xb9ce78: r1 = Instance_BoxShape
    //     0xb9ce78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb9ce7c: ldr             x1, [x1, #0x80]
    // 0xb9ce80: StoreField: r0->field_47 = r1
    //     0xb9ce80: stur            w1, [x0, #0x47]
    // 0xb9ce84: StoreField: r0->field_6f = r3
    //     0xb9ce84: stur            w3, [x0, #0x6f]
    // 0xb9ce88: r4 = false
    //     0xb9ce88: add             x4, NULL, #0x30  ; false
    // 0xb9ce8c: StoreField: r0->field_73 = r4
    //     0xb9ce8c: stur            w4, [x0, #0x73]
    // 0xb9ce90: StoreField: r0->field_83 = r3
    //     0xb9ce90: stur            w3, [x0, #0x83]
    // 0xb9ce94: StoreField: r0->field_7b = r4
    //     0xb9ce94: stur            w4, [x0, #0x7b]
    // 0xb9ce98: r1 = Null
    //     0xb9ce98: mov             x1, NULL
    // 0xb9ce9c: r2 = 6
    //     0xb9ce9c: movz            x2, #0x6
    // 0xb9cea0: r0 = AllocateArray()
    //     0xb9cea0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9cea4: mov             x2, x0
    // 0xb9cea8: ldur            x0, [fp, #-0x28]
    // 0xb9ceac: stur            x2, [fp, #-0x30]
    // 0xb9ceb0: StoreField: r2->field_f = r0
    //     0xb9ceb0: stur            w0, [x2, #0xf]
    // 0xb9ceb4: r16 = Instance_Spacer
    //     0xb9ceb4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb9ceb8: ldr             x16, [x16, #0xf0]
    // 0xb9cebc: StoreField: r2->field_13 = r16
    //     0xb9cebc: stur            w16, [x2, #0x13]
    // 0xb9cec0: ldur            x0, [fp, #-0x20]
    // 0xb9cec4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9cec4: stur            w0, [x2, #0x17]
    // 0xb9cec8: r1 = <Widget>
    //     0xb9cec8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9cecc: r0 = AllocateGrowableArray()
    //     0xb9cecc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9ced0: mov             x1, x0
    // 0xb9ced4: ldur            x0, [fp, #-0x30]
    // 0xb9ced8: stur            x1, [fp, #-0x20]
    // 0xb9cedc: StoreField: r1->field_f = r0
    //     0xb9cedc: stur            w0, [x1, #0xf]
    // 0xb9cee0: r2 = 6
    //     0xb9cee0: movz            x2, #0x6
    // 0xb9cee4: StoreField: r1->field_b = r2
    //     0xb9cee4: stur            w2, [x1, #0xb]
    // 0xb9cee8: r0 = Row()
    //     0xb9cee8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9ceec: mov             x1, x0
    // 0xb9cef0: r0 = Instance_Axis
    //     0xb9cef0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9cef4: stur            x1, [fp, #-0x28]
    // 0xb9cef8: StoreField: r1->field_f = r0
    //     0xb9cef8: stur            w0, [x1, #0xf]
    // 0xb9cefc: r2 = Instance_MainAxisAlignment
    //     0xb9cefc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9cf00: ldr             x2, [x2, #0xa08]
    // 0xb9cf04: StoreField: r1->field_13 = r2
    //     0xb9cf04: stur            w2, [x1, #0x13]
    // 0xb9cf08: r3 = Instance_MainAxisSize
    //     0xb9cf08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9cf0c: ldr             x3, [x3, #0xa10]
    // 0xb9cf10: ArrayStore: r1[0] = r3  ; List_4
    //     0xb9cf10: stur            w3, [x1, #0x17]
    // 0xb9cf14: r4 = Instance_CrossAxisAlignment
    //     0xb9cf14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9cf18: ldr             x4, [x4, #0xa18]
    // 0xb9cf1c: StoreField: r1->field_1b = r4
    //     0xb9cf1c: stur            w4, [x1, #0x1b]
    // 0xb9cf20: r5 = Instance_VerticalDirection
    //     0xb9cf20: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9cf24: ldr             x5, [x5, #0xa20]
    // 0xb9cf28: StoreField: r1->field_23 = r5
    //     0xb9cf28: stur            w5, [x1, #0x23]
    // 0xb9cf2c: r6 = Instance_Clip
    //     0xb9cf2c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9cf30: ldr             x6, [x6, #0x38]
    // 0xb9cf34: StoreField: r1->field_2b = r6
    //     0xb9cf34: stur            w6, [x1, #0x2b]
    // 0xb9cf38: StoreField: r1->field_2f = rZR
    //     0xb9cf38: stur            xzr, [x1, #0x2f]
    // 0xb9cf3c: ldur            x7, [fp, #-0x20]
    // 0xb9cf40: StoreField: r1->field_b = r7
    //     0xb9cf40: stur            w7, [x1, #0xb]
    // 0xb9cf44: ldur            x7, [fp, #-8]
    // 0xb9cf48: LoadField: r8 = r7->field_13
    //     0xb9cf48: ldur            w8, [x7, #0x13]
    // 0xb9cf4c: DecompressPointer r8
    //     0xb9cf4c: add             x8, x8, HEAP, lsl #32
    // 0xb9cf50: r16 = Sentinel
    //     0xb9cf50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb9cf54: cmp             w8, w16
    // 0xb9cf58: b.eq            #0xb9d980
    // 0xb9cf5c: cmp             w8, NULL
    // 0xb9cf60: b.ne            #0xb9cf6c
    // 0xb9cf64: r8 = Null
    //     0xb9cf64: mov             x8, NULL
    // 0xb9cf68: b               #0xb9cf78
    // 0xb9cf6c: LoadField: r9 = r8->field_b
    //     0xb9cf6c: ldur            w9, [x8, #0xb]
    // 0xb9cf70: DecompressPointer r9
    //     0xb9cf70: add             x9, x9, HEAP, lsl #32
    // 0xb9cf74: mov             x8, x9
    // 0xb9cf78: cmp             w8, NULL
    // 0xb9cf7c: b.ne            #0xb9cf84
    // 0xb9cf80: r8 = ""
    //     0xb9cf80: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9cf84: stur            x8, [fp, #-0x20]
    // 0xb9cf88: r0 = ImageHeaders.forImages()
    //     0xb9cf88: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xb9cf8c: r1 = Function '<anonymous closure>':.
    //     0xb9cf8c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d28] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9cf90: ldr             x1, [x1, #0xd28]
    // 0xb9cf94: r2 = Null
    //     0xb9cf94: mov             x2, NULL
    // 0xb9cf98: stur            x0, [fp, #-0x30]
    // 0xb9cf9c: r0 = AllocateClosure()
    //     0xb9cf9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9cfa0: r1 = Function '<anonymous closure>':.
    //     0xb9cfa0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d30] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9cfa4: ldr             x1, [x1, #0xd30]
    // 0xb9cfa8: r2 = Null
    //     0xb9cfa8: mov             x2, NULL
    // 0xb9cfac: stur            x0, [fp, #-0x38]
    // 0xb9cfb0: r0 = AllocateClosure()
    //     0xb9cfb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9cfb4: stur            x0, [fp, #-0x40]
    // 0xb9cfb8: r0 = CachedNetworkImage()
    //     0xb9cfb8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb9cfbc: stur            x0, [fp, #-0x48]
    // 0xb9cfc0: r16 = 56.000000
    //     0xb9cfc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb9cfc4: ldr             x16, [x16, #0xb78]
    // 0xb9cfc8: r30 = 56.000000
    //     0xb9cfc8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb9cfcc: ldr             lr, [lr, #0xb78]
    // 0xb9cfd0: stp             lr, x16, [SP, #0x20]
    // 0xb9cfd4: ldur            x16, [fp, #-0x30]
    // 0xb9cfd8: r30 = Instance_BoxFit
    //     0xb9cfd8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb9cfdc: ldr             lr, [lr, #0x118]
    // 0xb9cfe0: stp             lr, x16, [SP, #0x10]
    // 0xb9cfe4: ldur            x16, [fp, #-0x38]
    // 0xb9cfe8: ldur            lr, [fp, #-0x40]
    // 0xb9cfec: stp             lr, x16, [SP]
    // 0xb9cff0: mov             x1, x0
    // 0xb9cff4: ldur            x2, [fp, #-0x20]
    // 0xb9cff8: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xb9cff8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xb9cffc: ldr             x4, [x4, #0xbc8]
    // 0xb9d000: r0 = CachedNetworkImage()
    //     0xb9d000: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb9d004: ldur            x0, [fp, #-8]
    // 0xb9d008: LoadField: r1 = r0->field_b
    //     0xb9d008: ldur            w1, [x0, #0xb]
    // 0xb9d00c: DecompressPointer r1
    //     0xb9d00c: add             x1, x1, HEAP, lsl #32
    // 0xb9d010: cmp             w1, NULL
    // 0xb9d014: b.eq            #0xb9d98c
    // 0xb9d018: LoadField: r2 = r1->field_13
    //     0xb9d018: ldur            w2, [x1, #0x13]
    // 0xb9d01c: DecompressPointer r2
    //     0xb9d01c: add             x2, x2, HEAP, lsl #32
    // 0xb9d020: cmp             w2, NULL
    // 0xb9d024: b.ne            #0xb9d030
    // 0xb9d028: r1 = Null
    //     0xb9d028: mov             x1, NULL
    // 0xb9d02c: b               #0xb9d054
    // 0xb9d030: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb9d030: ldur            w1, [x2, #0x17]
    // 0xb9d034: DecompressPointer r1
    //     0xb9d034: add             x1, x1, HEAP, lsl #32
    // 0xb9d038: cmp             w1, NULL
    // 0xb9d03c: b.ne            #0xb9d048
    // 0xb9d040: r1 = Null
    //     0xb9d040: mov             x1, NULL
    // 0xb9d044: b               #0xb9d054
    // 0xb9d048: LoadField: r2 = r1->field_7
    //     0xb9d048: ldur            w2, [x1, #7]
    // 0xb9d04c: DecompressPointer r2
    //     0xb9d04c: add             x2, x2, HEAP, lsl #32
    // 0xb9d050: mov             x1, x2
    // 0xb9d054: cmp             w1, NULL
    // 0xb9d058: b.ne            #0xb9d064
    // 0xb9d05c: r2 = ""
    //     0xb9d05c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9d060: b               #0xb9d068
    // 0xb9d064: mov             x2, x1
    // 0xb9d068: ldur            x1, [fp, #-0x10]
    // 0xb9d06c: stur            x2, [fp, #-0x20]
    // 0xb9d070: r0 = of()
    //     0xb9d070: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d074: LoadField: r1 = r0->field_87
    //     0xb9d074: ldur            w1, [x0, #0x87]
    // 0xb9d078: DecompressPointer r1
    //     0xb9d078: add             x1, x1, HEAP, lsl #32
    // 0xb9d07c: LoadField: r0 = r1->field_7
    //     0xb9d07c: ldur            w0, [x1, #7]
    // 0xb9d080: DecompressPointer r0
    //     0xb9d080: add             x0, x0, HEAP, lsl #32
    // 0xb9d084: r16 = Instance_Color
    //     0xb9d084: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9d088: r30 = 14.000000
    //     0xb9d088: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9d08c: ldr             lr, [lr, #0x1d8]
    // 0xb9d090: stp             lr, x16, [SP]
    // 0xb9d094: mov             x1, x0
    // 0xb9d098: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb9d098: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb9d09c: ldr             x4, [x4, #0x9b8]
    // 0xb9d0a0: r0 = copyWith()
    //     0xb9d0a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9d0a4: stur            x0, [fp, #-0x30]
    // 0xb9d0a8: r0 = Text()
    //     0xb9d0a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9d0ac: mov             x1, x0
    // 0xb9d0b0: ldur            x0, [fp, #-0x20]
    // 0xb9d0b4: stur            x1, [fp, #-0x38]
    // 0xb9d0b8: StoreField: r1->field_b = r0
    //     0xb9d0b8: stur            w0, [x1, #0xb]
    // 0xb9d0bc: ldur            x0, [fp, #-0x30]
    // 0xb9d0c0: StoreField: r1->field_13 = r0
    //     0xb9d0c0: stur            w0, [x1, #0x13]
    // 0xb9d0c4: r2 = 4
    //     0xb9d0c4: movz            x2, #0x4
    // 0xb9d0c8: StoreField: r1->field_37 = r2
    //     0xb9d0c8: stur            w2, [x1, #0x37]
    // 0xb9d0cc: r0 = SizedBox()
    //     0xb9d0cc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb9d0d0: mov             x1, x0
    // 0xb9d0d4: r0 = 200.000000
    //     0xb9d0d4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0xb9d0d8: ldr             x0, [x0, #0x570]
    // 0xb9d0dc: stur            x1, [fp, #-0x20]
    // 0xb9d0e0: StoreField: r1->field_f = r0
    //     0xb9d0e0: stur            w0, [x1, #0xf]
    // 0xb9d0e4: ldur            x0, [fp, #-0x38]
    // 0xb9d0e8: StoreField: r1->field_b = r0
    //     0xb9d0e8: stur            w0, [x1, #0xb]
    // 0xb9d0ec: r0 = Padding()
    //     0xb9d0ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9d0f0: mov             x2, x0
    // 0xb9d0f4: r1 = Instance_EdgeInsets
    //     0xb9d0f4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb9d0f8: ldr             x1, [x1, #0xd38]
    // 0xb9d0fc: stur            x2, [fp, #-0x30]
    // 0xb9d100: StoreField: r2->field_f = r1
    //     0xb9d100: stur            w1, [x2, #0xf]
    // 0xb9d104: ldur            x0, [fp, #-0x20]
    // 0xb9d108: StoreField: r2->field_b = r0
    //     0xb9d108: stur            w0, [x2, #0xb]
    // 0xb9d10c: ldur            x3, [fp, #-8]
    // 0xb9d110: LoadField: r0 = r3->field_b
    //     0xb9d110: ldur            w0, [x3, #0xb]
    // 0xb9d114: DecompressPointer r0
    //     0xb9d114: add             x0, x0, HEAP, lsl #32
    // 0xb9d118: cmp             w0, NULL
    // 0xb9d11c: b.eq            #0xb9d990
    // 0xb9d120: LoadField: r4 = r0->field_13
    //     0xb9d120: ldur            w4, [x0, #0x13]
    // 0xb9d124: DecompressPointer r4
    //     0xb9d124: add             x4, x4, HEAP, lsl #32
    // 0xb9d128: cmp             w4, NULL
    // 0xb9d12c: b.ne            #0xb9d138
    // 0xb9d130: r0 = Null
    //     0xb9d130: mov             x0, NULL
    // 0xb9d134: b               #0xb9d140
    // 0xb9d138: LoadField: r0 = r4->field_87
    //     0xb9d138: ldur            w0, [x4, #0x87]
    // 0xb9d13c: DecompressPointer r0
    //     0xb9d13c: add             x0, x0, HEAP, lsl #32
    // 0xb9d140: r4 = LoadClassIdInstr(r0)
    //     0xb9d140: ldur            x4, [x0, #-1]
    //     0xb9d144: ubfx            x4, x4, #0xc, #0x14
    // 0xb9d148: r16 = "size"
    //     0xb9d148: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb9d14c: ldr             x16, [x16, #0x9c0]
    // 0xb9d150: stp             x16, x0, [SP]
    // 0xb9d154: mov             x0, x4
    // 0xb9d158: mov             lr, x0
    // 0xb9d15c: ldr             lr, [x21, lr, lsl #3]
    // 0xb9d160: blr             lr
    // 0xb9d164: tbnz            w0, #4, #0xb9d1cc
    // 0xb9d168: ldur            x0, [fp, #-8]
    // 0xb9d16c: r1 = Null
    //     0xb9d16c: mov             x1, NULL
    // 0xb9d170: r2 = 4
    //     0xb9d170: movz            x2, #0x4
    // 0xb9d174: r0 = AllocateArray()
    //     0xb9d174: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d178: r16 = "Size: "
    //     0xb9d178: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xb9d17c: ldr             x16, [x16, #0xf00]
    // 0xb9d180: StoreField: r0->field_f = r16
    //     0xb9d180: stur            w16, [x0, #0xf]
    // 0xb9d184: ldur            x1, [fp, #-8]
    // 0xb9d188: LoadField: r2 = r1->field_b
    //     0xb9d188: ldur            w2, [x1, #0xb]
    // 0xb9d18c: DecompressPointer r2
    //     0xb9d18c: add             x2, x2, HEAP, lsl #32
    // 0xb9d190: cmp             w2, NULL
    // 0xb9d194: b.eq            #0xb9d994
    // 0xb9d198: LoadField: r3 = r2->field_13
    //     0xb9d198: ldur            w3, [x2, #0x13]
    // 0xb9d19c: DecompressPointer r3
    //     0xb9d19c: add             x3, x3, HEAP, lsl #32
    // 0xb9d1a0: cmp             w3, NULL
    // 0xb9d1a4: b.ne            #0xb9d1b0
    // 0xb9d1a8: r2 = Null
    //     0xb9d1a8: mov             x2, NULL
    // 0xb9d1ac: b               #0xb9d1b8
    // 0xb9d1b0: LoadField: r2 = r3->field_53
    //     0xb9d1b0: ldur            w2, [x3, #0x53]
    // 0xb9d1b4: DecompressPointer r2
    //     0xb9d1b4: add             x2, x2, HEAP, lsl #32
    // 0xb9d1b8: StoreField: r0->field_13 = r2
    //     0xb9d1b8: stur            w2, [x0, #0x13]
    // 0xb9d1bc: str             x0, [SP]
    // 0xb9d1c0: r0 = _interpolate()
    //     0xb9d1c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb9d1c4: mov             x2, x0
    // 0xb9d1c8: b               #0xb9d22c
    // 0xb9d1cc: ldur            x0, [fp, #-8]
    // 0xb9d1d0: r1 = Null
    //     0xb9d1d0: mov             x1, NULL
    // 0xb9d1d4: r2 = 4
    //     0xb9d1d4: movz            x2, #0x4
    // 0xb9d1d8: r0 = AllocateArray()
    //     0xb9d1d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d1dc: r16 = "Variant: "
    //     0xb9d1dc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xb9d1e0: ldr             x16, [x16, #0xf08]
    // 0xb9d1e4: StoreField: r0->field_f = r16
    //     0xb9d1e4: stur            w16, [x0, #0xf]
    // 0xb9d1e8: ldur            x1, [fp, #-8]
    // 0xb9d1ec: LoadField: r2 = r1->field_b
    //     0xb9d1ec: ldur            w2, [x1, #0xb]
    // 0xb9d1f0: DecompressPointer r2
    //     0xb9d1f0: add             x2, x2, HEAP, lsl #32
    // 0xb9d1f4: cmp             w2, NULL
    // 0xb9d1f8: b.eq            #0xb9d998
    // 0xb9d1fc: LoadField: r3 = r2->field_13
    //     0xb9d1fc: ldur            w3, [x2, #0x13]
    // 0xb9d200: DecompressPointer r3
    //     0xb9d200: add             x3, x3, HEAP, lsl #32
    // 0xb9d204: cmp             w3, NULL
    // 0xb9d208: b.ne            #0xb9d214
    // 0xb9d20c: r2 = Null
    //     0xb9d20c: mov             x2, NULL
    // 0xb9d210: b               #0xb9d21c
    // 0xb9d214: LoadField: r2 = r3->field_53
    //     0xb9d214: ldur            w2, [x3, #0x53]
    // 0xb9d218: DecompressPointer r2
    //     0xb9d218: add             x2, x2, HEAP, lsl #32
    // 0xb9d21c: StoreField: r0->field_13 = r2
    //     0xb9d21c: stur            w2, [x0, #0x13]
    // 0xb9d220: str             x0, [SP]
    // 0xb9d224: r0 = _interpolate()
    //     0xb9d224: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb9d228: mov             x2, x0
    // 0xb9d22c: ldur            x0, [fp, #-8]
    // 0xb9d230: ldur            x1, [fp, #-0x10]
    // 0xb9d234: stur            x2, [fp, #-0x20]
    // 0xb9d238: r0 = of()
    //     0xb9d238: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d23c: LoadField: r1 = r0->field_87
    //     0xb9d23c: ldur            w1, [x0, #0x87]
    // 0xb9d240: DecompressPointer r1
    //     0xb9d240: add             x1, x1, HEAP, lsl #32
    // 0xb9d244: LoadField: r0 = r1->field_2b
    //     0xb9d244: ldur            w0, [x1, #0x2b]
    // 0xb9d248: DecompressPointer r0
    //     0xb9d248: add             x0, x0, HEAP, lsl #32
    // 0xb9d24c: r16 = 14.000000
    //     0xb9d24c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9d250: ldr             x16, [x16, #0x1d8]
    // 0xb9d254: r30 = Instance_Color
    //     0xb9d254: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9d258: stp             lr, x16, [SP]
    // 0xb9d25c: mov             x1, x0
    // 0xb9d260: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9d260: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9d264: ldr             x4, [x4, #0xaa0]
    // 0xb9d268: r0 = copyWith()
    //     0xb9d268: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9d26c: stur            x0, [fp, #-0x38]
    // 0xb9d270: r0 = Text()
    //     0xb9d270: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9d274: mov             x1, x0
    // 0xb9d278: ldur            x0, [fp, #-0x20]
    // 0xb9d27c: stur            x1, [fp, #-0x40]
    // 0xb9d280: StoreField: r1->field_b = r0
    //     0xb9d280: stur            w0, [x1, #0xb]
    // 0xb9d284: ldur            x0, [fp, #-0x38]
    // 0xb9d288: StoreField: r1->field_13 = r0
    //     0xb9d288: stur            w0, [x1, #0x13]
    // 0xb9d28c: r2 = 4
    //     0xb9d28c: movz            x2, #0x4
    // 0xb9d290: StoreField: r1->field_37 = r2
    //     0xb9d290: stur            w2, [x1, #0x37]
    // 0xb9d294: r0 = Padding()
    //     0xb9d294: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9d298: mov             x1, x0
    // 0xb9d29c: r0 = Instance_EdgeInsets
    //     0xb9d29c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb9d2a0: ldr             x0, [x0, #0xd38]
    // 0xb9d2a4: stur            x1, [fp, #-0x20]
    // 0xb9d2a8: StoreField: r1->field_f = r0
    //     0xb9d2a8: stur            w0, [x1, #0xf]
    // 0xb9d2ac: ldur            x2, [fp, #-0x40]
    // 0xb9d2b0: StoreField: r1->field_b = r2
    //     0xb9d2b0: stur            w2, [x1, #0xb]
    // 0xb9d2b4: ldur            x2, [fp, #-8]
    // 0xb9d2b8: LoadField: r3 = r2->field_b
    //     0xb9d2b8: ldur            w3, [x2, #0xb]
    // 0xb9d2bc: DecompressPointer r3
    //     0xb9d2bc: add             x3, x3, HEAP, lsl #32
    // 0xb9d2c0: cmp             w3, NULL
    // 0xb9d2c4: b.eq            #0xb9d99c
    // 0xb9d2c8: LoadField: r2 = r3->field_13
    //     0xb9d2c8: ldur            w2, [x3, #0x13]
    // 0xb9d2cc: DecompressPointer r2
    //     0xb9d2cc: add             x2, x2, HEAP, lsl #32
    // 0xb9d2d0: cmp             w2, NULL
    // 0xb9d2d4: b.ne            #0xb9d2e0
    // 0xb9d2d8: r5 = Null
    //     0xb9d2d8: mov             x5, NULL
    // 0xb9d2dc: b               #0xb9d2ec
    // 0xb9d2e0: LoadField: r3 = r2->field_3f
    //     0xb9d2e0: ldur            w3, [x2, #0x3f]
    // 0xb9d2e4: DecompressPointer r3
    //     0xb9d2e4: add             x3, x3, HEAP, lsl #32
    // 0xb9d2e8: mov             x5, x3
    // 0xb9d2ec: ldur            x4, [fp, #-0x28]
    // 0xb9d2f0: ldur            x3, [fp, #-0x48]
    // 0xb9d2f4: ldur            x2, [fp, #-0x30]
    // 0xb9d2f8: str             x5, [SP]
    // 0xb9d2fc: r0 = _interpolateSingle()
    //     0xb9d2fc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb9d300: ldur            x1, [fp, #-0x10]
    // 0xb9d304: stur            x0, [fp, #-8]
    // 0xb9d308: r0 = of()
    //     0xb9d308: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d30c: LoadField: r1 = r0->field_87
    //     0xb9d30c: ldur            w1, [x0, #0x87]
    // 0xb9d310: DecompressPointer r1
    //     0xb9d310: add             x1, x1, HEAP, lsl #32
    // 0xb9d314: LoadField: r0 = r1->field_2b
    //     0xb9d314: ldur            w0, [x1, #0x2b]
    // 0xb9d318: DecompressPointer r0
    //     0xb9d318: add             x0, x0, HEAP, lsl #32
    // 0xb9d31c: r16 = 14.000000
    //     0xb9d31c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9d320: ldr             x16, [x16, #0x1d8]
    // 0xb9d324: r30 = Instance_Color
    //     0xb9d324: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9d328: stp             lr, x16, [SP]
    // 0xb9d32c: mov             x1, x0
    // 0xb9d330: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9d330: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9d334: ldr             x4, [x4, #0xaa0]
    // 0xb9d338: r0 = copyWith()
    //     0xb9d338: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9d33c: stur            x0, [fp, #-0x38]
    // 0xb9d340: r0 = TextSpan()
    //     0xb9d340: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb9d344: mov             x3, x0
    // 0xb9d348: ldur            x0, [fp, #-8]
    // 0xb9d34c: stur            x3, [fp, #-0x40]
    // 0xb9d350: StoreField: r3->field_b = r0
    //     0xb9d350: stur            w0, [x3, #0xb]
    // 0xb9d354: r0 = Instance__DeferringMouseCursor
    //     0xb9d354: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb9d358: ArrayStore: r3[0] = r0  ; List_4
    //     0xb9d358: stur            w0, [x3, #0x17]
    // 0xb9d35c: ldur            x1, [fp, #-0x38]
    // 0xb9d360: StoreField: r3->field_7 = r1
    //     0xb9d360: stur            w1, [x3, #7]
    // 0xb9d364: r1 = Null
    //     0xb9d364: mov             x1, NULL
    // 0xb9d368: r2 = 2
    //     0xb9d368: movz            x2, #0x2
    // 0xb9d36c: r0 = AllocateArray()
    //     0xb9d36c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d370: mov             x2, x0
    // 0xb9d374: ldur            x0, [fp, #-0x40]
    // 0xb9d378: stur            x2, [fp, #-8]
    // 0xb9d37c: StoreField: r2->field_f = r0
    //     0xb9d37c: stur            w0, [x2, #0xf]
    // 0xb9d380: r1 = <TextSpan>
    //     0xb9d380: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xb9d384: ldr             x1, [x1, #0x940]
    // 0xb9d388: r0 = AllocateGrowableArray()
    //     0xb9d388: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9d38c: mov             x1, x0
    // 0xb9d390: ldur            x0, [fp, #-8]
    // 0xb9d394: stur            x1, [fp, #-0x38]
    // 0xb9d398: StoreField: r1->field_f = r0
    //     0xb9d398: stur            w0, [x1, #0xf]
    // 0xb9d39c: r0 = 2
    //     0xb9d39c: movz            x0, #0x2
    // 0xb9d3a0: StoreField: r1->field_b = r0
    //     0xb9d3a0: stur            w0, [x1, #0xb]
    // 0xb9d3a4: r0 = TextSpan()
    //     0xb9d3a4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb9d3a8: mov             x1, x0
    // 0xb9d3ac: ldur            x0, [fp, #-0x38]
    // 0xb9d3b0: stur            x1, [fp, #-8]
    // 0xb9d3b4: StoreField: r1->field_f = r0
    //     0xb9d3b4: stur            w0, [x1, #0xf]
    // 0xb9d3b8: r0 = Instance__DeferringMouseCursor
    //     0xb9d3b8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb9d3bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9d3bc: stur            w0, [x1, #0x17]
    // 0xb9d3c0: r0 = RichText()
    //     0xb9d3c0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb9d3c4: mov             x1, x0
    // 0xb9d3c8: ldur            x2, [fp, #-8]
    // 0xb9d3cc: stur            x0, [fp, #-8]
    // 0xb9d3d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb9d3d0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb9d3d4: r0 = RichText()
    //     0xb9d3d4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb9d3d8: r0 = Padding()
    //     0xb9d3d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9d3dc: mov             x3, x0
    // 0xb9d3e0: r0 = Instance_EdgeInsets
    //     0xb9d3e0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d38] Obj!EdgeInsets@d57a11
    //     0xb9d3e4: ldr             x0, [x0, #0xd38]
    // 0xb9d3e8: stur            x3, [fp, #-0x38]
    // 0xb9d3ec: StoreField: r3->field_f = r0
    //     0xb9d3ec: stur            w0, [x3, #0xf]
    // 0xb9d3f0: ldur            x0, [fp, #-8]
    // 0xb9d3f4: StoreField: r3->field_b = r0
    //     0xb9d3f4: stur            w0, [x3, #0xb]
    // 0xb9d3f8: r1 = Null
    //     0xb9d3f8: mov             x1, NULL
    // 0xb9d3fc: r2 = 6
    //     0xb9d3fc: movz            x2, #0x6
    // 0xb9d400: r0 = AllocateArray()
    //     0xb9d400: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d404: mov             x2, x0
    // 0xb9d408: ldur            x0, [fp, #-0x30]
    // 0xb9d40c: stur            x2, [fp, #-8]
    // 0xb9d410: StoreField: r2->field_f = r0
    //     0xb9d410: stur            w0, [x2, #0xf]
    // 0xb9d414: ldur            x0, [fp, #-0x20]
    // 0xb9d418: StoreField: r2->field_13 = r0
    //     0xb9d418: stur            w0, [x2, #0x13]
    // 0xb9d41c: ldur            x0, [fp, #-0x38]
    // 0xb9d420: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9d420: stur            w0, [x2, #0x17]
    // 0xb9d424: r1 = <Widget>
    //     0xb9d424: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9d428: r0 = AllocateGrowableArray()
    //     0xb9d428: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9d42c: mov             x1, x0
    // 0xb9d430: ldur            x0, [fp, #-8]
    // 0xb9d434: stur            x1, [fp, #-0x20]
    // 0xb9d438: StoreField: r1->field_f = r0
    //     0xb9d438: stur            w0, [x1, #0xf]
    // 0xb9d43c: r2 = 6
    //     0xb9d43c: movz            x2, #0x6
    // 0xb9d440: StoreField: r1->field_b = r2
    //     0xb9d440: stur            w2, [x1, #0xb]
    // 0xb9d444: r0 = Column()
    //     0xb9d444: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9d448: mov             x3, x0
    // 0xb9d44c: r0 = Instance_Axis
    //     0xb9d44c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9d450: stur            x3, [fp, #-8]
    // 0xb9d454: StoreField: r3->field_f = r0
    //     0xb9d454: stur            w0, [x3, #0xf]
    // 0xb9d458: r4 = Instance_MainAxisAlignment
    //     0xb9d458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9d45c: ldr             x4, [x4, #0xa08]
    // 0xb9d460: StoreField: r3->field_13 = r4
    //     0xb9d460: stur            w4, [x3, #0x13]
    // 0xb9d464: r5 = Instance_MainAxisSize
    //     0xb9d464: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9d468: ldr             x5, [x5, #0xa10]
    // 0xb9d46c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb9d46c: stur            w5, [x3, #0x17]
    // 0xb9d470: r1 = Instance_CrossAxisAlignment
    //     0xb9d470: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9d474: ldr             x1, [x1, #0x890]
    // 0xb9d478: StoreField: r3->field_1b = r1
    //     0xb9d478: stur            w1, [x3, #0x1b]
    // 0xb9d47c: r6 = Instance_VerticalDirection
    //     0xb9d47c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9d480: ldr             x6, [x6, #0xa20]
    // 0xb9d484: StoreField: r3->field_23 = r6
    //     0xb9d484: stur            w6, [x3, #0x23]
    // 0xb9d488: r7 = Instance_Clip
    //     0xb9d488: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9d48c: ldr             x7, [x7, #0x38]
    // 0xb9d490: StoreField: r3->field_2b = r7
    //     0xb9d490: stur            w7, [x3, #0x2b]
    // 0xb9d494: StoreField: r3->field_2f = rZR
    //     0xb9d494: stur            xzr, [x3, #0x2f]
    // 0xb9d498: ldur            x1, [fp, #-0x20]
    // 0xb9d49c: StoreField: r3->field_b = r1
    //     0xb9d49c: stur            w1, [x3, #0xb]
    // 0xb9d4a0: r1 = Null
    //     0xb9d4a0: mov             x1, NULL
    // 0xb9d4a4: r2 = 4
    //     0xb9d4a4: movz            x2, #0x4
    // 0xb9d4a8: r0 = AllocateArray()
    //     0xb9d4a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d4ac: mov             x2, x0
    // 0xb9d4b0: ldur            x0, [fp, #-0x48]
    // 0xb9d4b4: stur            x2, [fp, #-0x20]
    // 0xb9d4b8: StoreField: r2->field_f = r0
    //     0xb9d4b8: stur            w0, [x2, #0xf]
    // 0xb9d4bc: ldur            x0, [fp, #-8]
    // 0xb9d4c0: StoreField: r2->field_13 = r0
    //     0xb9d4c0: stur            w0, [x2, #0x13]
    // 0xb9d4c4: r1 = <Widget>
    //     0xb9d4c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9d4c8: r0 = AllocateGrowableArray()
    //     0xb9d4c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9d4cc: mov             x1, x0
    // 0xb9d4d0: ldur            x0, [fp, #-0x20]
    // 0xb9d4d4: stur            x1, [fp, #-8]
    // 0xb9d4d8: StoreField: r1->field_f = r0
    //     0xb9d4d8: stur            w0, [x1, #0xf]
    // 0xb9d4dc: r0 = 4
    //     0xb9d4dc: movz            x0, #0x4
    // 0xb9d4e0: StoreField: r1->field_b = r0
    //     0xb9d4e0: stur            w0, [x1, #0xb]
    // 0xb9d4e4: r0 = Row()
    //     0xb9d4e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9d4e8: mov             x1, x0
    // 0xb9d4ec: r0 = Instance_Axis
    //     0xb9d4ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9d4f0: stur            x1, [fp, #-0x20]
    // 0xb9d4f4: StoreField: r1->field_f = r0
    //     0xb9d4f4: stur            w0, [x1, #0xf]
    // 0xb9d4f8: r2 = Instance_MainAxisAlignment
    //     0xb9d4f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9d4fc: ldr             x2, [x2, #0xa08]
    // 0xb9d500: StoreField: r1->field_13 = r2
    //     0xb9d500: stur            w2, [x1, #0x13]
    // 0xb9d504: r3 = Instance_MainAxisSize
    //     0xb9d504: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9d508: ldr             x3, [x3, #0xa10]
    // 0xb9d50c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb9d50c: stur            w3, [x1, #0x17]
    // 0xb9d510: r4 = Instance_CrossAxisAlignment
    //     0xb9d510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9d514: ldr             x4, [x4, #0xa18]
    // 0xb9d518: StoreField: r1->field_1b = r4
    //     0xb9d518: stur            w4, [x1, #0x1b]
    // 0xb9d51c: r5 = Instance_VerticalDirection
    //     0xb9d51c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9d520: ldr             x5, [x5, #0xa20]
    // 0xb9d524: StoreField: r1->field_23 = r5
    //     0xb9d524: stur            w5, [x1, #0x23]
    // 0xb9d528: r6 = Instance_Clip
    //     0xb9d528: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9d52c: ldr             x6, [x6, #0x38]
    // 0xb9d530: StoreField: r1->field_2b = r6
    //     0xb9d530: stur            w6, [x1, #0x2b]
    // 0xb9d534: StoreField: r1->field_2f = rZR
    //     0xb9d534: stur            xzr, [x1, #0x2f]
    // 0xb9d538: ldur            x7, [fp, #-8]
    // 0xb9d53c: StoreField: r1->field_b = r7
    //     0xb9d53c: stur            w7, [x1, #0xb]
    // 0xb9d540: r0 = Padding()
    //     0xb9d540: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9d544: mov             x2, x0
    // 0xb9d548: r0 = Instance_EdgeInsets
    //     0xb9d548: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xb9d54c: ldr             x0, [x0, #0x868]
    // 0xb9d550: stur            x2, [fp, #-8]
    // 0xb9d554: StoreField: r2->field_f = r0
    //     0xb9d554: stur            w0, [x2, #0xf]
    // 0xb9d558: ldur            x0, [fp, #-0x20]
    // 0xb9d55c: StoreField: r2->field_b = r0
    //     0xb9d55c: stur            w0, [x2, #0xb]
    // 0xb9d560: ldur            x1, [fp, #-0x10]
    // 0xb9d564: r0 = of()
    //     0xb9d564: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d568: LoadField: r1 = r0->field_5b
    //     0xb9d568: ldur            w1, [x0, #0x5b]
    // 0xb9d56c: DecompressPointer r1
    //     0xb9d56c: add             x1, x1, HEAP, lsl #32
    // 0xb9d570: r0 = LoadClassIdInstr(r1)
    //     0xb9d570: ldur            x0, [x1, #-1]
    //     0xb9d574: ubfx            x0, x0, #0xc, #0x14
    // 0xb9d578: d0 = 0.300000
    //     0xb9d578: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb9d57c: ldr             d0, [x17, #0x658]
    // 0xb9d580: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb9d580: sub             lr, x0, #0xffa
    //     0xb9d584: ldr             lr, [x21, lr, lsl #3]
    //     0xb9d588: blr             lr
    // 0xb9d58c: stur            x0, [fp, #-0x20]
    // 0xb9d590: r0 = BorderSide()
    //     0xb9d590: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb9d594: mov             x1, x0
    // 0xb9d598: ldur            x0, [fp, #-0x20]
    // 0xb9d59c: stur            x1, [fp, #-0x30]
    // 0xb9d5a0: StoreField: r1->field_7 = r0
    //     0xb9d5a0: stur            w0, [x1, #7]
    // 0xb9d5a4: d0 = 1.000000
    //     0xb9d5a4: fmov            d0, #1.00000000
    // 0xb9d5a8: StoreField: r1->field_b = d0
    //     0xb9d5a8: stur            d0, [x1, #0xb]
    // 0xb9d5ac: r0 = Instance_BorderStyle
    //     0xb9d5ac: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb9d5b0: ldr             x0, [x0, #0xf68]
    // 0xb9d5b4: StoreField: r1->field_13 = r0
    //     0xb9d5b4: stur            w0, [x1, #0x13]
    // 0xb9d5b8: d0 = -1.000000
    //     0xb9d5b8: fmov            d0, #-1.00000000
    // 0xb9d5bc: ArrayStore: r1[0] = d0  ; List_8
    //     0xb9d5bc: stur            d0, [x1, #0x17]
    // 0xb9d5c0: r0 = RoundedRectangleBorder()
    //     0xb9d5c0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb9d5c4: mov             x1, x0
    // 0xb9d5c8: r0 = Instance_BorderRadius
    //     0xb9d5c8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xb9d5cc: ldr             x0, [x0, #0xf70]
    // 0xb9d5d0: StoreField: r1->field_b = r0
    //     0xb9d5d0: stur            w0, [x1, #0xb]
    // 0xb9d5d4: ldur            x0, [fp, #-0x30]
    // 0xb9d5d8: StoreField: r1->field_7 = r0
    //     0xb9d5d8: stur            w0, [x1, #7]
    // 0xb9d5dc: r16 = <RoundedRectangleBorder>
    //     0xb9d5dc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb9d5e0: ldr             x16, [x16, #0xf78]
    // 0xb9d5e4: stp             x1, x16, [SP]
    // 0xb9d5e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb9d5e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb9d5ec: r0 = all()
    //     0xb9d5ec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb9d5f0: stur            x0, [fp, #-0x20]
    // 0xb9d5f4: r0 = ButtonStyle()
    //     0xb9d5f4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb9d5f8: mov             x1, x0
    // 0xb9d5fc: ldur            x0, [fp, #-0x20]
    // 0xb9d600: stur            x1, [fp, #-0x30]
    // 0xb9d604: StoreField: r1->field_43 = r0
    //     0xb9d604: stur            w0, [x1, #0x43]
    // 0xb9d608: r0 = TextButtonThemeData()
    //     0xb9d608: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb9d60c: mov             x2, x0
    // 0xb9d610: ldur            x0, [fp, #-0x30]
    // 0xb9d614: stur            x2, [fp, #-0x20]
    // 0xb9d618: StoreField: r2->field_7 = r0
    //     0xb9d618: stur            w0, [x2, #7]
    // 0xb9d61c: ldur            x1, [fp, #-0x10]
    // 0xb9d620: r0 = of()
    //     0xb9d620: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d624: LoadField: r1 = r0->field_87
    //     0xb9d624: ldur            w1, [x0, #0x87]
    // 0xb9d628: DecompressPointer r1
    //     0xb9d628: add             x1, x1, HEAP, lsl #32
    // 0xb9d62c: LoadField: r0 = r1->field_7
    //     0xb9d62c: ldur            w0, [x1, #7]
    // 0xb9d630: DecompressPointer r0
    //     0xb9d630: add             x0, x0, HEAP, lsl #32
    // 0xb9d634: r16 = 14.000000
    //     0xb9d634: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9d638: ldr             x16, [x16, #0x1d8]
    // 0xb9d63c: str             x16, [SP]
    // 0xb9d640: mov             x1, x0
    // 0xb9d644: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb9d644: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb9d648: ldr             x4, [x4, #0x798]
    // 0xb9d64c: r0 = copyWith()
    //     0xb9d64c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9d650: stur            x0, [fp, #-0x30]
    // 0xb9d654: r0 = Text()
    //     0xb9d654: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9d658: mov             x3, x0
    // 0xb9d65c: r0 = "I\'LL CHOOSE"
    //     0xb9d65c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d40] "I\'LL CHOOSE"
    //     0xb9d660: ldr             x0, [x0, #0xd40]
    // 0xb9d664: stur            x3, [fp, #-0x38]
    // 0xb9d668: StoreField: r3->field_b = r0
    //     0xb9d668: stur            w0, [x3, #0xb]
    // 0xb9d66c: ldur            x0, [fp, #-0x30]
    // 0xb9d670: StoreField: r3->field_13 = r0
    //     0xb9d670: stur            w0, [x3, #0x13]
    // 0xb9d674: ldur            x2, [fp, #-0x18]
    // 0xb9d678: r1 = Function '<anonymous closure>':.
    //     0xb9d678: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d48] AnonymousClosure: (0xb9db40), in [package:customer_app/app/presentation/views/line/bag/bag_bottom_sheet_to_choose.dart] _BagBottomSheetToChooseState::build (0xb9cd30)
    //     0xb9d67c: ldr             x1, [x1, #0xd48]
    // 0xb9d680: r0 = AllocateClosure()
    //     0xb9d680: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9d684: stur            x0, [fp, #-0x30]
    // 0xb9d688: r0 = TextButton()
    //     0xb9d688: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb9d68c: mov             x1, x0
    // 0xb9d690: ldur            x0, [fp, #-0x30]
    // 0xb9d694: stur            x1, [fp, #-0x40]
    // 0xb9d698: StoreField: r1->field_b = r0
    //     0xb9d698: stur            w0, [x1, #0xb]
    // 0xb9d69c: r0 = false
    //     0xb9d69c: add             x0, NULL, #0x30  ; false
    // 0xb9d6a0: StoreField: r1->field_27 = r0
    //     0xb9d6a0: stur            w0, [x1, #0x27]
    // 0xb9d6a4: r2 = true
    //     0xb9d6a4: add             x2, NULL, #0x20  ; true
    // 0xb9d6a8: StoreField: r1->field_2f = r2
    //     0xb9d6a8: stur            w2, [x1, #0x2f]
    // 0xb9d6ac: ldur            x3, [fp, #-0x38]
    // 0xb9d6b0: StoreField: r1->field_37 = r3
    //     0xb9d6b0: stur            w3, [x1, #0x37]
    // 0xb9d6b4: r0 = TextButtonTheme()
    //     0xb9d6b4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb9d6b8: mov             x2, x0
    // 0xb9d6bc: ldur            x0, [fp, #-0x20]
    // 0xb9d6c0: stur            x2, [fp, #-0x30]
    // 0xb9d6c4: StoreField: r2->field_f = r0
    //     0xb9d6c4: stur            w0, [x2, #0xf]
    // 0xb9d6c8: ldur            x0, [fp, #-0x40]
    // 0xb9d6cc: StoreField: r2->field_b = r0
    //     0xb9d6cc: stur            w0, [x2, #0xb]
    // 0xb9d6d0: r1 = <FlexParentData>
    //     0xb9d6d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb9d6d4: ldr             x1, [x1, #0xe00]
    // 0xb9d6d8: r0 = Flexible()
    //     0xb9d6d8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb9d6dc: mov             x2, x0
    // 0xb9d6e0: r0 = 1
    //     0xb9d6e0: movz            x0, #0x1
    // 0xb9d6e4: stur            x2, [fp, #-0x20]
    // 0xb9d6e8: StoreField: r2->field_13 = r0
    //     0xb9d6e8: stur            x0, [x2, #0x13]
    // 0xb9d6ec: r3 = Instance_FlexFit
    //     0xb9d6ec: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb9d6f0: ldr             x3, [x3, #0xe08]
    // 0xb9d6f4: StoreField: r2->field_1b = r3
    //     0xb9d6f4: stur            w3, [x2, #0x1b]
    // 0xb9d6f8: ldur            x1, [fp, #-0x30]
    // 0xb9d6fc: StoreField: r2->field_b = r1
    //     0xb9d6fc: stur            w1, [x2, #0xb]
    // 0xb9d700: ldur            x1, [fp, #-0x10]
    // 0xb9d704: r0 = of()
    //     0xb9d704: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d708: r17 = 307
    //     0xb9d708: movz            x17, #0x133
    // 0xb9d70c: ldr             w2, [x0, x17]
    // 0xb9d710: DecompressPointer r2
    //     0xb9d710: add             x2, x2, HEAP, lsl #32
    // 0xb9d714: ldur            x1, [fp, #-0x10]
    // 0xb9d718: stur            x2, [fp, #-0x30]
    // 0xb9d71c: r0 = of()
    //     0xb9d71c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9d720: LoadField: r1 = r0->field_87
    //     0xb9d720: ldur            w1, [x0, #0x87]
    // 0xb9d724: DecompressPointer r1
    //     0xb9d724: add             x1, x1, HEAP, lsl #32
    // 0xb9d728: LoadField: r0 = r1->field_7
    //     0xb9d728: ldur            w0, [x1, #7]
    // 0xb9d72c: DecompressPointer r0
    //     0xb9d72c: add             x0, x0, HEAP, lsl #32
    // 0xb9d730: r16 = 14.000000
    //     0xb9d730: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9d734: ldr             x16, [x16, #0x1d8]
    // 0xb9d738: r30 = Instance_Color
    //     0xb9d738: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb9d73c: stp             lr, x16, [SP]
    // 0xb9d740: mov             x1, x0
    // 0xb9d744: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9d744: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9d748: ldr             x4, [x4, #0xaa0]
    // 0xb9d74c: r0 = copyWith()
    //     0xb9d74c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9d750: stur            x0, [fp, #-0x10]
    // 0xb9d754: r0 = Text()
    //     0xb9d754: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9d758: mov             x3, x0
    // 0xb9d75c: r0 = "REPEAT LAST"
    //     0xb9d75c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54d50] "REPEAT LAST"
    //     0xb9d760: ldr             x0, [x0, #0xd50]
    // 0xb9d764: stur            x3, [fp, #-0x38]
    // 0xb9d768: StoreField: r3->field_b = r0
    //     0xb9d768: stur            w0, [x3, #0xb]
    // 0xb9d76c: ldur            x0, [fp, #-0x10]
    // 0xb9d770: StoreField: r3->field_13 = r0
    //     0xb9d770: stur            w0, [x3, #0x13]
    // 0xb9d774: ldur            x2, [fp, #-0x18]
    // 0xb9d778: r1 = Function '<anonymous closure>':.
    //     0xb9d778: add             x1, PP, #0x54, lsl #12  ; [pp+0x54d58] AnonymousClosure: (0xb9d9a0), in [package:customer_app/app/presentation/views/line/bag/bag_bottom_sheet_to_choose.dart] _BagBottomSheetToChooseState::build (0xb9cd30)
    //     0xb9d77c: ldr             x1, [x1, #0xd58]
    // 0xb9d780: r0 = AllocateClosure()
    //     0xb9d780: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9d784: stur            x0, [fp, #-0x10]
    // 0xb9d788: r0 = TextButton()
    //     0xb9d788: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb9d78c: mov             x1, x0
    // 0xb9d790: ldur            x0, [fp, #-0x10]
    // 0xb9d794: stur            x1, [fp, #-0x18]
    // 0xb9d798: StoreField: r1->field_b = r0
    //     0xb9d798: stur            w0, [x1, #0xb]
    // 0xb9d79c: r0 = false
    //     0xb9d79c: add             x0, NULL, #0x30  ; false
    // 0xb9d7a0: StoreField: r1->field_27 = r0
    //     0xb9d7a0: stur            w0, [x1, #0x27]
    // 0xb9d7a4: r0 = true
    //     0xb9d7a4: add             x0, NULL, #0x20  ; true
    // 0xb9d7a8: StoreField: r1->field_2f = r0
    //     0xb9d7a8: stur            w0, [x1, #0x2f]
    // 0xb9d7ac: ldur            x0, [fp, #-0x38]
    // 0xb9d7b0: StoreField: r1->field_37 = r0
    //     0xb9d7b0: stur            w0, [x1, #0x37]
    // 0xb9d7b4: r0 = TextButtonTheme()
    //     0xb9d7b4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb9d7b8: mov             x2, x0
    // 0xb9d7bc: ldur            x0, [fp, #-0x30]
    // 0xb9d7c0: stur            x2, [fp, #-0x10]
    // 0xb9d7c4: StoreField: r2->field_f = r0
    //     0xb9d7c4: stur            w0, [x2, #0xf]
    // 0xb9d7c8: ldur            x0, [fp, #-0x18]
    // 0xb9d7cc: StoreField: r2->field_b = r0
    //     0xb9d7cc: stur            w0, [x2, #0xb]
    // 0xb9d7d0: r1 = <FlexParentData>
    //     0xb9d7d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb9d7d4: ldr             x1, [x1, #0xe00]
    // 0xb9d7d8: r0 = Flexible()
    //     0xb9d7d8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb9d7dc: mov             x3, x0
    // 0xb9d7e0: r0 = 1
    //     0xb9d7e0: movz            x0, #0x1
    // 0xb9d7e4: stur            x3, [fp, #-0x18]
    // 0xb9d7e8: StoreField: r3->field_13 = r0
    //     0xb9d7e8: stur            x0, [x3, #0x13]
    // 0xb9d7ec: r0 = Instance_FlexFit
    //     0xb9d7ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb9d7f0: ldr             x0, [x0, #0xe08]
    // 0xb9d7f4: StoreField: r3->field_1b = r0
    //     0xb9d7f4: stur            w0, [x3, #0x1b]
    // 0xb9d7f8: ldur            x0, [fp, #-0x10]
    // 0xb9d7fc: StoreField: r3->field_b = r0
    //     0xb9d7fc: stur            w0, [x3, #0xb]
    // 0xb9d800: r1 = Null
    //     0xb9d800: mov             x1, NULL
    // 0xb9d804: r2 = 6
    //     0xb9d804: movz            x2, #0x6
    // 0xb9d808: r0 = AllocateArray()
    //     0xb9d808: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d80c: mov             x2, x0
    // 0xb9d810: ldur            x0, [fp, #-0x20]
    // 0xb9d814: stur            x2, [fp, #-0x10]
    // 0xb9d818: StoreField: r2->field_f = r0
    //     0xb9d818: stur            w0, [x2, #0xf]
    // 0xb9d81c: r16 = Instance_SizedBox
    //     0xb9d81c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb9d820: ldr             x16, [x16, #0xb20]
    // 0xb9d824: StoreField: r2->field_13 = r16
    //     0xb9d824: stur            w16, [x2, #0x13]
    // 0xb9d828: ldur            x0, [fp, #-0x18]
    // 0xb9d82c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9d82c: stur            w0, [x2, #0x17]
    // 0xb9d830: r1 = <Widget>
    //     0xb9d830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9d834: r0 = AllocateGrowableArray()
    //     0xb9d834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9d838: mov             x1, x0
    // 0xb9d83c: ldur            x0, [fp, #-0x10]
    // 0xb9d840: stur            x1, [fp, #-0x18]
    // 0xb9d844: StoreField: r1->field_f = r0
    //     0xb9d844: stur            w0, [x1, #0xf]
    // 0xb9d848: r2 = 6
    //     0xb9d848: movz            x2, #0x6
    // 0xb9d84c: StoreField: r1->field_b = r2
    //     0xb9d84c: stur            w2, [x1, #0xb]
    // 0xb9d850: r0 = Row()
    //     0xb9d850: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9d854: mov             x3, x0
    // 0xb9d858: r0 = Instance_Axis
    //     0xb9d858: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9d85c: stur            x3, [fp, #-0x10]
    // 0xb9d860: StoreField: r3->field_f = r0
    //     0xb9d860: stur            w0, [x3, #0xf]
    // 0xb9d864: r0 = Instance_MainAxisAlignment
    //     0xb9d864: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb9d868: ldr             x0, [x0, #0xa8]
    // 0xb9d86c: StoreField: r3->field_13 = r0
    //     0xb9d86c: stur            w0, [x3, #0x13]
    // 0xb9d870: r0 = Instance_MainAxisSize
    //     0xb9d870: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9d874: ldr             x0, [x0, #0xa10]
    // 0xb9d878: ArrayStore: r3[0] = r0  ; List_4
    //     0xb9d878: stur            w0, [x3, #0x17]
    // 0xb9d87c: r0 = Instance_CrossAxisAlignment
    //     0xb9d87c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9d880: ldr             x0, [x0, #0xa18]
    // 0xb9d884: StoreField: r3->field_1b = r0
    //     0xb9d884: stur            w0, [x3, #0x1b]
    // 0xb9d888: r4 = Instance_VerticalDirection
    //     0xb9d888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9d88c: ldr             x4, [x4, #0xa20]
    // 0xb9d890: StoreField: r3->field_23 = r4
    //     0xb9d890: stur            w4, [x3, #0x23]
    // 0xb9d894: r5 = Instance_Clip
    //     0xb9d894: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9d898: ldr             x5, [x5, #0x38]
    // 0xb9d89c: StoreField: r3->field_2b = r5
    //     0xb9d89c: stur            w5, [x3, #0x2b]
    // 0xb9d8a0: StoreField: r3->field_2f = rZR
    //     0xb9d8a0: stur            xzr, [x3, #0x2f]
    // 0xb9d8a4: ldur            x1, [fp, #-0x18]
    // 0xb9d8a8: StoreField: r3->field_b = r1
    //     0xb9d8a8: stur            w1, [x3, #0xb]
    // 0xb9d8ac: r1 = Null
    //     0xb9d8ac: mov             x1, NULL
    // 0xb9d8b0: r2 = 6
    //     0xb9d8b0: movz            x2, #0x6
    // 0xb9d8b4: r0 = AllocateArray()
    //     0xb9d8b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9d8b8: mov             x2, x0
    // 0xb9d8bc: ldur            x0, [fp, #-0x28]
    // 0xb9d8c0: stur            x2, [fp, #-0x18]
    // 0xb9d8c4: StoreField: r2->field_f = r0
    //     0xb9d8c4: stur            w0, [x2, #0xf]
    // 0xb9d8c8: ldur            x0, [fp, #-8]
    // 0xb9d8cc: StoreField: r2->field_13 = r0
    //     0xb9d8cc: stur            w0, [x2, #0x13]
    // 0xb9d8d0: ldur            x0, [fp, #-0x10]
    // 0xb9d8d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9d8d4: stur            w0, [x2, #0x17]
    // 0xb9d8d8: r1 = <Widget>
    //     0xb9d8d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9d8dc: r0 = AllocateGrowableArray()
    //     0xb9d8dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9d8e0: mov             x1, x0
    // 0xb9d8e4: ldur            x0, [fp, #-0x18]
    // 0xb9d8e8: stur            x1, [fp, #-8]
    // 0xb9d8ec: StoreField: r1->field_f = r0
    //     0xb9d8ec: stur            w0, [x1, #0xf]
    // 0xb9d8f0: r0 = 6
    //     0xb9d8f0: movz            x0, #0x6
    // 0xb9d8f4: StoreField: r1->field_b = r0
    //     0xb9d8f4: stur            w0, [x1, #0xb]
    // 0xb9d8f8: r0 = Column()
    //     0xb9d8f8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9d8fc: mov             x1, x0
    // 0xb9d900: r0 = Instance_Axis
    //     0xb9d900: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9d904: stur            x1, [fp, #-0x10]
    // 0xb9d908: StoreField: r1->field_f = r0
    //     0xb9d908: stur            w0, [x1, #0xf]
    // 0xb9d90c: r0 = Instance_MainAxisAlignment
    //     0xb9d90c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9d910: ldr             x0, [x0, #0xa08]
    // 0xb9d914: StoreField: r1->field_13 = r0
    //     0xb9d914: stur            w0, [x1, #0x13]
    // 0xb9d918: r0 = Instance_MainAxisSize
    //     0xb9d918: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb9d91c: ldr             x0, [x0, #0xdd0]
    // 0xb9d920: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9d920: stur            w0, [x1, #0x17]
    // 0xb9d924: r0 = Instance_CrossAxisAlignment
    //     0xb9d924: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9d928: ldr             x0, [x0, #0xa18]
    // 0xb9d92c: StoreField: r1->field_1b = r0
    //     0xb9d92c: stur            w0, [x1, #0x1b]
    // 0xb9d930: r0 = Instance_VerticalDirection
    //     0xb9d930: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9d934: ldr             x0, [x0, #0xa20]
    // 0xb9d938: StoreField: r1->field_23 = r0
    //     0xb9d938: stur            w0, [x1, #0x23]
    // 0xb9d93c: r0 = Instance_Clip
    //     0xb9d93c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9d940: ldr             x0, [x0, #0x38]
    // 0xb9d944: StoreField: r1->field_2b = r0
    //     0xb9d944: stur            w0, [x1, #0x2b]
    // 0xb9d948: StoreField: r1->field_2f = rZR
    //     0xb9d948: stur            xzr, [x1, #0x2f]
    // 0xb9d94c: ldur            x0, [fp, #-8]
    // 0xb9d950: StoreField: r1->field_b = r0
    //     0xb9d950: stur            w0, [x1, #0xb]
    // 0xb9d954: r0 = Padding()
    //     0xb9d954: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9d958: r1 = Instance_EdgeInsets
    //     0xb9d958: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb9d95c: ldr             x1, [x1, #0xd0]
    // 0xb9d960: StoreField: r0->field_f = r1
    //     0xb9d960: stur            w1, [x0, #0xf]
    // 0xb9d964: ldur            x1, [fp, #-0x10]
    // 0xb9d968: StoreField: r0->field_b = r1
    //     0xb9d968: stur            w1, [x0, #0xb]
    // 0xb9d96c: LeaveFrame
    //     0xb9d96c: mov             SP, fp
    //     0xb9d970: ldp             fp, lr, [SP], #0x10
    // 0xb9d974: ret
    //     0xb9d974: ret             
    // 0xb9d978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9d978: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9d97c: b               #0xb9cd58
    // 0xb9d980: r9 = image
    //     0xb9d980: add             x9, PP, #0x54, lsl #12  ; [pp+0x54d60] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb9d984: ldr             x9, [x9, #0xd60]
    // 0xb9d988: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb9d988: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb9d98c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9d98c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9d990: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9d990: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9d994: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9d994: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9d998: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9d998: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9d99c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9d99c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9d9a0, size: 0x1a0
    // 0xb9d9a0: EnterFrame
    //     0xb9d9a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb9d9a4: mov             fp, SP
    // 0xb9d9a8: AllocStack(0x38)
    //     0xb9d9a8: sub             SP, SP, #0x38
    // 0xb9d9ac: SetupParameters()
    //     0xb9d9ac: ldr             x0, [fp, #0x10]
    //     0xb9d9b0: ldur            w1, [x0, #0x17]
    //     0xb9d9b4: add             x1, x1, HEAP, lsl #32
    //     0xb9d9b8: stur            x1, [fp, #-8]
    // 0xb9d9bc: CheckStackOverflow
    //     0xb9d9bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9d9c0: cmp             SP, x16
    //     0xb9d9c4: b.ls            #0xb9db2c
    // 0xb9d9c8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb9d9c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb9d9cc: ldr             x0, [x0, #0x1c80]
    //     0xb9d9d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb9d9d4: cmp             w0, w16
    //     0xb9d9d8: b.ne            #0xb9d9e4
    //     0xb9d9dc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb9d9e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb9d9e4: str             NULL, [SP]
    // 0xb9d9e8: r4 = const [0x1, 0, 0, 0, null]
    //     0xb9d9e8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb9d9ec: r0 = GetNavigation.back()
    //     0xb9d9ec: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb9d9f0: ldur            x0, [fp, #-8]
    // 0xb9d9f4: LoadField: r1 = r0->field_f
    //     0xb9d9f4: ldur            w1, [x0, #0xf]
    // 0xb9d9f8: DecompressPointer r1
    //     0xb9d9f8: add             x1, x1, HEAP, lsl #32
    // 0xb9d9fc: LoadField: r0 = r1->field_b
    //     0xb9d9fc: ldur            w0, [x1, #0xb]
    // 0xb9da00: DecompressPointer r0
    //     0xb9da00: add             x0, x0, HEAP, lsl #32
    // 0xb9da04: stur            x0, [fp, #-0x28]
    // 0xb9da08: cmp             w0, NULL
    // 0xb9da0c: b.eq            #0xb9db34
    // 0xb9da10: LoadField: r1 = r0->field_b
    //     0xb9da10: ldur            w1, [x0, #0xb]
    // 0xb9da14: DecompressPointer r1
    //     0xb9da14: add             x1, x1, HEAP, lsl #32
    // 0xb9da18: cmp             w1, NULL
    // 0xb9da1c: b.eq            #0xb9db38
    // 0xb9da20: LoadField: r2 = r0->field_f
    //     0xb9da20: ldur            w2, [x0, #0xf]
    // 0xb9da24: DecompressPointer r2
    //     0xb9da24: add             x2, x2, HEAP, lsl #32
    // 0xb9da28: cmp             w2, NULL
    // 0xb9da2c: b.eq            #0xb9db3c
    // 0xb9da30: r3 = LoadInt32Instr(r1)
    //     0xb9da30: sbfx            x3, x1, #1, #0x1f
    //     0xb9da34: tbz             w1, #0, #0xb9da3c
    //     0xb9da38: ldur            x3, [x1, #7]
    // 0xb9da3c: r1 = LoadInt32Instr(r2)
    //     0xb9da3c: sbfx            x1, x2, #1, #0x1f
    //     0xb9da40: tbz             w2, #0, #0xb9da48
    //     0xb9da44: ldur            x1, [x2, #7]
    // 0xb9da48: sub             x2, x3, x1
    // 0xb9da4c: stur            x2, [fp, #-0x20]
    // 0xb9da50: LoadField: r1 = r0->field_13
    //     0xb9da50: ldur            w1, [x0, #0x13]
    // 0xb9da54: DecompressPointer r1
    //     0xb9da54: add             x1, x1, HEAP, lsl #32
    // 0xb9da58: cmp             w1, NULL
    // 0xb9da5c: b.ne            #0xb9da68
    // 0xb9da60: r3 = Null
    //     0xb9da60: mov             x3, NULL
    // 0xb9da64: b               #0xb9da70
    // 0xb9da68: LoadField: r3 = r1->field_b
    //     0xb9da68: ldur            w3, [x1, #0xb]
    // 0xb9da6c: DecompressPointer r3
    //     0xb9da6c: add             x3, x3, HEAP, lsl #32
    // 0xb9da70: stur            x3, [fp, #-0x18]
    // 0xb9da74: cmp             w1, NULL
    // 0xb9da78: b.ne            #0xb9da84
    // 0xb9da7c: r4 = Null
    //     0xb9da7c: mov             x4, NULL
    // 0xb9da80: b               #0xb9daa8
    // 0xb9da84: LoadField: r4 = r1->field_1f
    //     0xb9da84: ldur            w4, [x1, #0x1f]
    // 0xb9da88: DecompressPointer r4
    //     0xb9da88: add             x4, x4, HEAP, lsl #32
    // 0xb9da8c: cmp             w4, NULL
    // 0xb9da90: b.ne            #0xb9da9c
    // 0xb9da94: r4 = Null
    //     0xb9da94: mov             x4, NULL
    // 0xb9da98: b               #0xb9daa8
    // 0xb9da9c: LoadField: r5 = r4->field_b
    //     0xb9da9c: ldur            w5, [x4, #0xb]
    // 0xb9daa0: DecompressPointer r5
    //     0xb9daa0: add             x5, x5, HEAP, lsl #32
    // 0xb9daa4: mov             x4, x5
    // 0xb9daa8: stur            x4, [fp, #-0x10]
    // 0xb9daac: cmp             w1, NULL
    // 0xb9dab0: b.ne            #0xb9dabc
    // 0xb9dab4: r1 = Null
    //     0xb9dab4: mov             x1, NULL
    // 0xb9dab8: b               #0xb9dac8
    // 0xb9dabc: LoadField: r5 = r1->field_83
    //     0xb9dabc: ldur            w5, [x1, #0x83]
    // 0xb9dac0: DecompressPointer r5
    //     0xb9dac0: add             x5, x5, HEAP, lsl #32
    // 0xb9dac4: mov             x1, x5
    // 0xb9dac8: stur            x1, [fp, #-8]
    // 0xb9dacc: r0 = AddToBagRequest()
    //     0xb9dacc: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xb9dad0: mov             x1, x0
    // 0xb9dad4: ldur            x0, [fp, #-0x18]
    // 0xb9dad8: StoreField: r1->field_7 = r0
    //     0xb9dad8: stur            w0, [x1, #7]
    // 0xb9dadc: ldur            x0, [fp, #-0x10]
    // 0xb9dae0: StoreField: r1->field_b = r0
    //     0xb9dae0: stur            w0, [x1, #0xb]
    // 0xb9dae4: ldur            x0, [fp, #-0x20]
    // 0xb9dae8: StoreField: r1->field_f = r0
    //     0xb9dae8: stur            x0, [x1, #0xf]
    // 0xb9daec: ldur            x0, [fp, #-8]
    // 0xb9daf0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb9daf0: stur            w0, [x1, #0x17]
    // 0xb9daf4: ldur            x0, [fp, #-0x28]
    // 0xb9daf8: LoadField: r2 = r0->field_1b
    //     0xb9daf8: ldur            w2, [x0, #0x1b]
    // 0xb9dafc: DecompressPointer r2
    //     0xb9dafc: add             x2, x2, HEAP, lsl #32
    // 0xb9db00: stp             x1, x2, [SP]
    // 0xb9db04: r4 = 0
    //     0xb9db04: movz            x4, #0
    // 0xb9db08: ldr             x0, [SP, #8]
    // 0xb9db0c: r16 = UnlinkedCall_0x613b5c
    //     0xb9db0c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54d68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9db10: add             x16, x16, #0xd68
    // 0xb9db14: ldp             x5, lr, [x16]
    // 0xb9db18: blr             lr
    // 0xb9db1c: r0 = Null
    //     0xb9db1c: mov             x0, NULL
    // 0xb9db20: LeaveFrame
    //     0xb9db20: mov             SP, fp
    //     0xb9db24: ldp             fp, lr, [SP], #0x10
    // 0xb9db28: ret
    //     0xb9db28: ret             
    // 0xb9db2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9db2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9db30: b               #0xb9d9c8
    // 0xb9db34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9db34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9db38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9db38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9db3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9db3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0xb9db40, size: 0x144
    // 0xb9db40: EnterFrame
    //     0xb9db40: stp             fp, lr, [SP, #-0x10]!
    //     0xb9db44: mov             fp, SP
    // 0xb9db48: AllocStack(0x40)
    //     0xb9db48: sub             SP, SP, #0x40
    // 0xb9db4c: SetupParameters(_BagBottomSheetToChooseState this /* r1 */)
    //     0xb9db4c: stur            NULL, [fp, #-8]
    //     0xb9db50: movz            x0, #0
    //     0xb9db54: add             x1, fp, w0, sxtw #2
    //     0xb9db58: ldr             x1, [x1, #0x10]
    //     0xb9db5c: ldur            w2, [x1, #0x17]
    //     0xb9db60: add             x2, x2, HEAP, lsl #32
    //     0xb9db64: stur            x2, [fp, #-0x10]
    // 0xb9db68: CheckStackOverflow
    //     0xb9db68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9db6c: cmp             SP, x16
    //     0xb9db70: b.ls            #0xb9dc78
    // 0xb9db74: InitAsync() -> Future<void?>
    //     0xb9db74: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0xb9db78: bl              #0x6326e0  ; InitAsyncStub
    // 0xb9db7c: ldur            x0, [fp, #-0x10]
    // 0xb9db80: LoadField: r1 = r0->field_f
    //     0xb9db80: ldur            w1, [x0, #0xf]
    // 0xb9db84: DecompressPointer r1
    //     0xb9db84: add             x1, x1, HEAP, lsl #32
    // 0xb9db88: LoadField: r0 = r1->field_b
    //     0xb9db88: ldur            w0, [x1, #0xb]
    // 0xb9db8c: DecompressPointer r0
    //     0xb9db8c: add             x0, x0, HEAP, lsl #32
    // 0xb9db90: cmp             w0, NULL
    // 0xb9db94: b.eq            #0xb9dc80
    // 0xb9db98: LoadField: r1 = r0->field_13
    //     0xb9db98: ldur            w1, [x0, #0x13]
    // 0xb9db9c: DecompressPointer r1
    //     0xb9db9c: add             x1, x1, HEAP, lsl #32
    // 0xb9dba0: cmp             w1, NULL
    // 0xb9dba4: b.ne            #0xb9dbb0
    // 0xb9dba8: r2 = Null
    //     0xb9dba8: mov             x2, NULL
    // 0xb9dbac: b               #0xb9dbb8
    // 0xb9dbb0: LoadField: r2 = r1->field_7f
    //     0xb9dbb0: ldur            w2, [x1, #0x7f]
    // 0xb9dbb4: DecompressPointer r2
    //     0xb9dbb4: add             x2, x2, HEAP, lsl #32
    // 0xb9dbb8: cmp             w2, NULL
    // 0xb9dbbc: b.ne            #0xb9dbc4
    // 0xb9dbc0: r2 = ""
    //     0xb9dbc0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9dbc4: cmp             w1, NULL
    // 0xb9dbc8: b.ne            #0xb9dbd4
    // 0xb9dbcc: r3 = Null
    //     0xb9dbcc: mov             x3, NULL
    // 0xb9dbd0: b               #0xb9dbdc
    // 0xb9dbd4: LoadField: r3 = r1->field_b
    //     0xb9dbd4: ldur            w3, [x1, #0xb]
    // 0xb9dbd8: DecompressPointer r3
    //     0xb9dbd8: add             x3, x3, HEAP, lsl #32
    // 0xb9dbdc: cmp             w1, NULL
    // 0xb9dbe0: b.ne            #0xb9dbec
    // 0xb9dbe4: r4 = Null
    //     0xb9dbe4: mov             x4, NULL
    // 0xb9dbe8: b               #0xb9dc10
    // 0xb9dbec: LoadField: r4 = r1->field_1f
    //     0xb9dbec: ldur            w4, [x1, #0x1f]
    // 0xb9dbf0: DecompressPointer r4
    //     0xb9dbf0: add             x4, x4, HEAP, lsl #32
    // 0xb9dbf4: cmp             w4, NULL
    // 0xb9dbf8: b.ne            #0xb9dc04
    // 0xb9dbfc: r4 = Null
    //     0xb9dbfc: mov             x4, NULL
    // 0xb9dc00: b               #0xb9dc10
    // 0xb9dc04: LoadField: r5 = r4->field_b
    //     0xb9dc04: ldur            w5, [x4, #0xb]
    // 0xb9dc08: DecompressPointer r5
    //     0xb9dc08: add             x5, x5, HEAP, lsl #32
    // 0xb9dc0c: mov             x4, x5
    // 0xb9dc10: cmp             w1, NULL
    // 0xb9dc14: b.ne            #0xb9dc20
    // 0xb9dc18: r5 = Null
    //     0xb9dc18: mov             x5, NULL
    // 0xb9dc1c: b               #0xb9dc28
    // 0xb9dc20: LoadField: r5 = r1->field_83
    //     0xb9dc20: ldur            w5, [x1, #0x83]
    // 0xb9dc24: DecompressPointer r5
    //     0xb9dc24: add             x5, x5, HEAP, lsl #32
    // 0xb9dc28: cmp             w1, NULL
    // 0xb9dc2c: b.ne            #0xb9dc38
    // 0xb9dc30: r1 = Null
    //     0xb9dc30: mov             x1, NULL
    // 0xb9dc34: b               #0xb9dc44
    // 0xb9dc38: LoadField: r6 = r1->field_43
    //     0xb9dc38: ldur            w6, [x1, #0x43]
    // 0xb9dc3c: DecompressPointer r6
    //     0xb9dc3c: add             x6, x6, HEAP, lsl #32
    // 0xb9dc40: mov             x1, x6
    // 0xb9dc44: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xb9dc44: ldur            w6, [x0, #0x17]
    // 0xb9dc48: DecompressPointer r6
    //     0xb9dc48: add             x6, x6, HEAP, lsl #32
    // 0xb9dc4c: stp             x2, x6, [SP, #0x20]
    // 0xb9dc50: stp             x4, x3, [SP, #0x10]
    // 0xb9dc54: stp             x1, x5, [SP]
    // 0xb9dc58: r4 = 0
    //     0xb9dc58: movz            x4, #0
    // 0xb9dc5c: ldr             x0, [SP, #0x28]
    // 0xb9dc60: r16 = UnlinkedCall_0x613b5c
    //     0xb9dc60: add             x16, PP, #0x54, lsl #12  ; [pp+0x54d78] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb9dc64: add             x16, x16, #0xd78
    // 0xb9dc68: ldp             x5, lr, [x16]
    // 0xb9dc6c: blr             lr
    // 0xb9dc70: r0 = Null
    //     0xb9dc70: mov             x0, NULL
    // 0xb9dc74: r0 = ReturnAsyncNotFuture()
    //     0xb9dc74: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xb9dc78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9dc78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9dc7c: b               #0xb9db74
    // 0xb9dc80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9dc80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4039, size: 0x20, field offset: 0xc
//   const constructor, 
class BagBottomSheetToChoose extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fd78, size: 0x2c
    // 0xc7fd78: EnterFrame
    //     0xc7fd78: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fd7c: mov             fp, SP
    // 0xc7fd80: mov             x0, x1
    // 0xc7fd84: r1 = <BagBottomSheetToChoose>
    //     0xc7fd84: add             x1, PP, #0x48, lsl #12  ; [pp+0x486f8] TypeArguments: <BagBottomSheetToChoose>
    //     0xc7fd88: ldr             x1, [x1, #0x6f8]
    // 0xc7fd8c: r0 = _BagBottomSheetToChooseState()
    //     0xc7fd8c: bl              #0xc7fda4  ; Allocate_BagBottomSheetToChooseStateStub -> _BagBottomSheetToChooseState (size=0x18)
    // 0xc7fd90: r1 = Sentinel
    //     0xc7fd90: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7fd94: StoreField: r0->field_13 = r1
    //     0xc7fd94: stur            w1, [x0, #0x13]
    // 0xc7fd98: LeaveFrame
    //     0xc7fd98: mov             SP, fp
    //     0xc7fd9c: ldp             fp, lr, [SP], #0x10
    // 0xc7fda0: ret
    //     0xc7fda0: ret             
  }
}
