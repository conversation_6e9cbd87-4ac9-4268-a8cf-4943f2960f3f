// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart

// class id: 1049402, size: 0x8
class :: {
}

// class id: 3337, size: 0x14, field offset: 0x14
class _CustomizedBottomSheetState extends State<dynamic> {

  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xa47358, size: 0x7c
    // 0xa47358: EnterFrame
    //     0xa47358: stp             fp, lr, [SP, #-0x10]!
    //     0xa4735c: mov             fp, SP
    // 0xa47360: AllocStack(0x8)
    //     0xa47360: sub             SP, SP, #8
    // 0xa47364: SetupParameters()
    //     0xa47364: ldr             x0, [fp, #0x18]
    //     0xa47368: ldur            w1, [x0, #0x17]
    //     0xa4736c: add             x1, x1, HEAP, lsl #32
    // 0xa47370: CheckStackOverflow
    //     0xa47370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa47374: cmp             SP, x16
    //     0xa47378: b.ls            #0xa473c8
    // 0xa4737c: ldr             x0, [fp, #0x10]
    // 0xa47380: cmp             w0, NULL
    // 0xa47384: b.eq            #0xa473b8
    // 0xa47388: LoadField: r0 = r1->field_f
    //     0xa47388: ldur            w0, [x1, #0xf]
    // 0xa4738c: DecompressPointer r0
    //     0xa4738c: add             x0, x0, HEAP, lsl #32
    // 0xa47390: LoadField: r1 = r0->field_b
    //     0xa47390: ldur            w1, [x0, #0xb]
    // 0xa47394: DecompressPointer r1
    //     0xa47394: add             x1, x1, HEAP, lsl #32
    // 0xa47398: cmp             w1, NULL
    // 0xa4739c: b.eq            #0xa473d0
    // 0xa473a0: LoadField: r0 = r1->field_1f
    //     0xa473a0: ldur            w0, [x1, #0x1f]
    // 0xa473a4: DecompressPointer r0
    //     0xa473a4: add             x0, x0, HEAP, lsl #32
    // 0xa473a8: str             x0, [SP]
    // 0xa473ac: ClosureCall
    //     0xa473ac: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa473b0: ldur            x2, [x0, #0x1f]
    //     0xa473b4: blr             x2
    // 0xa473b8: r0 = Null
    //     0xa473b8: mov             x0, NULL
    // 0xa473bc: LeaveFrame
    //     0xa473bc: mov             SP, fp
    //     0xa473c0: ldp             fp, lr, [SP], #0x10
    // 0xa473c4: ret
    //     0xa473c4: ret             
    // 0xa473c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa473c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa473cc: b               #0xa4737c
    // 0xa473d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa473d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa473d4, size: 0x314
    // 0xa473d4: EnterFrame
    //     0xa473d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa473d8: mov             fp, SP
    // 0xa473dc: AllocStack(0x28)
    //     0xa473dc: sub             SP, SP, #0x28
    // 0xa473e0: SetupParameters()
    //     0xa473e0: ldr             x0, [fp, #0x10]
    //     0xa473e4: ldur            w2, [x0, #0x17]
    //     0xa473e8: add             x2, x2, HEAP, lsl #32
    //     0xa473ec: stur            x2, [fp, #-8]
    // 0xa473f0: CheckStackOverflow
    //     0xa473f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa473f4: cmp             SP, x16
    //     0xa473f8: b.ls            #0xa476dc
    // 0xa473fc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa473fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa47400: ldr             x0, [x0, #0x1c80]
    //     0xa47404: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa47408: cmp             w0, w16
    //     0xa4740c: b.ne            #0xa47418
    //     0xa47410: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa47414: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa47418: str             NULL, [SP]
    // 0xa4741c: r4 = const [0x1, 0, 0, 0, null]
    //     0xa4741c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa47420: r0 = GetNavigation.back()
    //     0xa47420: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa47424: r1 = Null
    //     0xa47424: mov             x1, NULL
    // 0xa47428: r2 = 44
    //     0xa47428: movz            x2, #0x2c
    // 0xa4742c: r0 = AllocateArray()
    //     0xa4742c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa47430: mov             x2, x0
    // 0xa47434: r16 = "customizedResponse"
    //     0xa47434: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] "customizedResponse"
    //     0xa47438: ldr             x16, [x16, #0x38]
    // 0xa4743c: StoreField: r2->field_f = r16
    //     0xa4743c: stur            w16, [x2, #0xf]
    // 0xa47440: ldur            x3, [fp, #-8]
    // 0xa47444: LoadField: r0 = r3->field_f
    //     0xa47444: ldur            w0, [x3, #0xf]
    // 0xa47448: DecompressPointer r0
    //     0xa47448: add             x0, x0, HEAP, lsl #32
    // 0xa4744c: LoadField: r4 = r0->field_b
    //     0xa4744c: ldur            w4, [x0, #0xb]
    // 0xa47450: DecompressPointer r4
    //     0xa47450: add             x4, x4, HEAP, lsl #32
    // 0xa47454: cmp             w4, NULL
    // 0xa47458: b.eq            #0xa476e4
    // 0xa4745c: LoadField: r0 = r4->field_b
    //     0xa4745c: ldur            w0, [x4, #0xb]
    // 0xa47460: DecompressPointer r0
    //     0xa47460: add             x0, x0, HEAP, lsl #32
    // 0xa47464: StoreField: r2->field_13 = r0
    //     0xa47464: stur            w0, [x2, #0x13]
    // 0xa47468: r16 = "productId"
    //     0xa47468: ldr             x16, [PP, #0x3970]  ; [pp+0x3970] "productId"
    // 0xa4746c: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4746c: stur            w16, [x2, #0x17]
    // 0xa47470: LoadField: r0 = r4->field_f
    //     0xa47470: ldur            w0, [x4, #0xf]
    // 0xa47474: DecompressPointer r0
    //     0xa47474: add             x0, x0, HEAP, lsl #32
    // 0xa47478: StoreField: r2->field_1b = r0
    //     0xa47478: stur            w0, [x2, #0x1b]
    // 0xa4747c: r16 = "skuId"
    //     0xa4747c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] "skuId"
    //     0xa47480: ldr             x16, [x16, #0x40]
    // 0xa47484: StoreField: r2->field_1f = r16
    //     0xa47484: stur            w16, [x2, #0x1f]
    // 0xa47488: LoadField: r0 = r4->field_13
    //     0xa47488: ldur            w0, [x4, #0x13]
    // 0xa4748c: DecompressPointer r0
    //     0xa4748c: add             x0, x0, HEAP, lsl #32
    // 0xa47490: StoreField: r2->field_23 = r0
    //     0xa47490: stur            w0, [x2, #0x23]
    // 0xa47494: r16 = "customisedId"
    //     0xa47494: add             x16, PP, #0x30, lsl #12  ; [pp+0x30048] "customisedId"
    //     0xa47498: ldr             x16, [x16, #0x48]
    // 0xa4749c: StoreField: r2->field_27 = r16
    //     0xa4749c: stur            w16, [x2, #0x27]
    // 0xa474a0: r16 = ""
    //     0xa474a0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa474a4: StoreField: r2->field_2b = r16
    //     0xa474a4: stur            w16, [x2, #0x2b]
    // 0xa474a8: r16 = "addTypeValue"
    //     0xa474a8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30050] "addTypeValue"
    //     0xa474ac: ldr             x16, [x16, #0x50]
    // 0xa474b0: StoreField: r2->field_2f = r16
    //     0xa474b0: stur            w16, [x2, #0x2f]
    // 0xa474b4: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xa474b4: ldur            x5, [x4, #0x17]
    // 0xa474b8: r0 = BoxInt64Instr(r5)
    //     0xa474b8: sbfiz           x0, x5, #1, #0x1f
    //     0xa474bc: cmp             x5, x0, asr #1
    //     0xa474c0: b.eq            #0xa474cc
    //     0xa474c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa474c8: stur            x5, [x0, #7]
    // 0xa474cc: mov             x1, x2
    // 0xa474d0: ArrayStore: r1[9] = r0  ; List_4
    //     0xa474d0: add             x25, x1, #0x33
    //     0xa474d4: str             w0, [x25]
    //     0xa474d8: tbz             w0, #0, #0xa474f4
    //     0xa474dc: ldurb           w16, [x1, #-1]
    //     0xa474e0: ldurb           w17, [x0, #-1]
    //     0xa474e4: and             x16, x17, x16, lsr #2
    //     0xa474e8: tst             x16, HEAP, lsr #32
    //     0xa474ec: b.eq            #0xa474f4
    //     0xa474f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa474f4: r16 = "sellingPrice"
    //     0xa474f4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30058] "sellingPrice"
    //     0xa474f8: ldr             x16, [x16, #0x58]
    // 0xa474fc: StoreField: r2->field_37 = r16
    //     0xa474fc: stur            w16, [x2, #0x37]
    // 0xa47500: LoadField: r5 = r4->field_27
    //     0xa47500: ldur            x5, [x4, #0x27]
    // 0xa47504: r0 = BoxInt64Instr(r5)
    //     0xa47504: sbfiz           x0, x5, #1, #0x1f
    //     0xa47508: cmp             x5, x0, asr #1
    //     0xa4750c: b.eq            #0xa47518
    //     0xa47510: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa47514: stur            x5, [x0, #7]
    // 0xa47518: mov             x1, x2
    // 0xa4751c: ArrayStore: r1[11] = r0  ; List_4
    //     0xa4751c: add             x25, x1, #0x3b
    //     0xa47520: str             w0, [x25]
    //     0xa47524: tbz             w0, #0, #0xa47540
    //     0xa47528: ldurb           w16, [x1, #-1]
    //     0xa4752c: ldurb           w17, [x0, #-1]
    //     0xa47530: and             x16, x17, x16, lsr #2
    //     0xa47534: tst             x16, HEAP, lsr #32
    //     0xa47538: b.eq            #0xa47540
    //     0xa4753c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa47540: r16 = "comingFrom"
    //     0xa47540: add             x16, PP, #0x30, lsl #12  ; [pp+0x30060] "comingFrom"
    //     0xa47544: ldr             x16, [x16, #0x60]
    // 0xa47548: StoreField: r2->field_3f = r16
    //     0xa47548: stur            w16, [x2, #0x3f]
    // 0xa4754c: LoadField: r0 = r4->field_2f
    //     0xa4754c: ldur            w0, [x4, #0x2f]
    // 0xa47550: DecompressPointer r0
    //     0xa47550: add             x0, x0, HEAP, lsl #32
    // 0xa47554: mov             x1, x2
    // 0xa47558: ArrayStore: r1[13] = r0  ; List_4
    //     0xa47558: add             x25, x1, #0x43
    //     0xa4755c: str             w0, [x25]
    //     0xa47560: tbz             w0, #0, #0xa4757c
    //     0xa47564: ldurb           w16, [x1, #-1]
    //     0xa47568: ldurb           w17, [x0, #-1]
    //     0xa4756c: and             x16, x17, x16, lsr #2
    //     0xa47570: tst             x16, HEAP, lsr #32
    //     0xa47574: b.eq            #0xa4757c
    //     0xa47578: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4757c: r16 = "skuData"
    //     0xa4757c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] "skuData"
    //     0xa47580: ldr             x16, [x16, #0x68]
    // 0xa47584: StoreField: r2->field_47 = r16
    //     0xa47584: stur            w16, [x2, #0x47]
    // 0xa47588: LoadField: r0 = r4->field_37
    //     0xa47588: ldur            w0, [x4, #0x37]
    // 0xa4758c: DecompressPointer r0
    //     0xa4758c: add             x0, x0, HEAP, lsl #32
    // 0xa47590: mov             x1, x2
    // 0xa47594: ArrayStore: r1[15] = r0  ; List_4
    //     0xa47594: add             x25, x1, #0x4b
    //     0xa47598: str             w0, [x25]
    //     0xa4759c: tbz             w0, #0, #0xa475b8
    //     0xa475a0: ldurb           w16, [x1, #-1]
    //     0xa475a4: ldurb           w17, [x0, #-1]
    //     0xa475a8: and             x16, x17, x16, lsr #2
    //     0xa475ac: tst             x16, HEAP, lsr #32
    //     0xa475b0: b.eq            #0xa475b8
    //     0xa475b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa475b8: r16 = "productTitle"
    //     0xa475b8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] "productTitle"
    //     0xa475bc: ldr             x16, [x16, #0x70]
    // 0xa475c0: StoreField: r2->field_4f = r16
    //     0xa475c0: stur            w16, [x2, #0x4f]
    // 0xa475c4: LoadField: r0 = r4->field_3b
    //     0xa475c4: ldur            w0, [x4, #0x3b]
    // 0xa475c8: DecompressPointer r0
    //     0xa475c8: add             x0, x0, HEAP, lsl #32
    // 0xa475cc: mov             x1, x2
    // 0xa475d0: ArrayStore: r1[17] = r0  ; List_4
    //     0xa475d0: add             x25, x1, #0x53
    //     0xa475d4: str             w0, [x25]
    //     0xa475d8: tbz             w0, #0, #0xa475f4
    //     0xa475dc: ldurb           w16, [x1, #-1]
    //     0xa475e0: ldurb           w17, [x0, #-1]
    //     0xa475e4: and             x16, x17, x16, lsr #2
    //     0xa475e8: tst             x16, HEAP, lsr #32
    //     0xa475ec: b.eq            #0xa475f4
    //     0xa475f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa475f4: r16 = "productImageUrl"
    //     0xa475f4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30078] "productImageUrl"
    //     0xa475f8: ldr             x16, [x16, #0x78]
    // 0xa475fc: StoreField: r2->field_57 = r16
    //     0xa475fc: stur            w16, [x2, #0x57]
    // 0xa47600: LoadField: r0 = r4->field_3f
    //     0xa47600: ldur            w0, [x4, #0x3f]
    // 0xa47604: DecompressPointer r0
    //     0xa47604: add             x0, x0, HEAP, lsl #32
    // 0xa47608: mov             x1, x2
    // 0xa4760c: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4760c: add             x25, x1, #0x5b
    //     0xa47610: str             w0, [x25]
    //     0xa47614: tbz             w0, #0, #0xa47630
    //     0xa47618: ldurb           w16, [x1, #-1]
    //     0xa4761c: ldurb           w17, [x0, #-1]
    //     0xa47620: and             x16, x17, x16, lsr #2
    //     0xa47624: tst             x16, HEAP, lsr #32
    //     0xa47628: b.eq            #0xa47630
    //     0xa4762c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa47630: r16 = "checkout_variant_response"
    //     0xa47630: add             x16, PP, #0x30, lsl #12  ; [pp+0x30080] "checkout_variant_response"
    //     0xa47634: ldr             x16, [x16, #0x80]
    // 0xa47638: StoreField: r2->field_5f = r16
    //     0xa47638: stur            w16, [x2, #0x5f]
    // 0xa4763c: LoadField: r0 = r4->field_4b
    //     0xa4763c: ldur            w0, [x4, #0x4b]
    // 0xa47640: DecompressPointer r0
    //     0xa47640: add             x0, x0, HEAP, lsl #32
    // 0xa47644: mov             x1, x2
    // 0xa47648: ArrayStore: r1[21] = r0  ; List_4
    //     0xa47648: add             x25, x1, #0x63
    //     0xa4764c: str             w0, [x25]
    //     0xa47650: tbz             w0, #0, #0xa4766c
    //     0xa47654: ldurb           w16, [x1, #-1]
    //     0xa47658: ldurb           w17, [x0, #-1]
    //     0xa4765c: and             x16, x17, x16, lsr #2
    //     0xa47660: tst             x16, HEAP, lsr #32
    //     0xa47664: b.eq            #0xa4766c
    //     0xa47668: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4766c: r16 = <String, Object?>
    //     0xa4766c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa47670: ldr             x16, [x16, #0xc28]
    // 0xa47674: stp             x2, x16, [SP]
    // 0xa47678: r0 = Map._fromLiteral()
    //     0xa47678: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4767c: r16 = "/customization"
    //     0xa4767c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0xa47680: ldr             x16, [x16, #0x8a8]
    // 0xa47684: stp             x16, NULL, [SP, #8]
    // 0xa47688: str             x0, [SP]
    // 0xa4768c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4768c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa47690: ldr             x4, [x4, #0x438]
    // 0xa47694: r0 = GetNavigation.toNamed()
    //     0xa47694: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa47698: stur            x0, [fp, #-0x10]
    // 0xa4769c: cmp             w0, NULL
    // 0xa476a0: b.eq            #0xa476cc
    // 0xa476a4: ldur            x2, [fp, #-8]
    // 0xa476a8: r1 = Function '<anonymous closure>':.
    //     0xa476a8: add             x1, PP, #0x56, lsl #12  ; [pp+0x563b8] AnonymousClosure: (0xa47358), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa476ac: ldr             x1, [x1, #0x3b8]
    // 0xa476b0: r0 = AllocateClosure()
    //     0xa476b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa476b4: r16 = <Null?>
    //     0xa476b4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa476b8: ldur            lr, [fp, #-0x10]
    // 0xa476bc: stp             lr, x16, [SP, #8]
    // 0xa476c0: str             x0, [SP]
    // 0xa476c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa476c4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa476c8: r0 = then()
    //     0xa476c8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa476cc: r0 = Null
    //     0xa476cc: mov             x0, NULL
    // 0xa476d0: LeaveFrame
    //     0xa476d0: mov             SP, fp
    //     0xa476d4: ldp             fp, lr, [SP], #0x10
    // 0xa476d8: ret
    //     0xa476d8: ret             
    // 0xa476dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa476dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa476e0: b               #0xa473fc
    // 0xa476e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa476e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xa48ad4, size: 0x4e0
    // 0xa48ad4: EnterFrame
    //     0xa48ad4: stp             fp, lr, [SP, #-0x10]!
    //     0xa48ad8: mov             fp, SP
    // 0xa48adc: AllocStack(0x50)
    //     0xa48adc: sub             SP, SP, #0x50
    // 0xa48ae0: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xa48ae0: stur            NULL, [fp, #-8]
    //     0xa48ae4: movz            x0, #0
    //     0xa48ae8: add             x1, fp, w0, sxtw #2
    //     0xa48aec: ldr             x1, [x1, #0x18]
    //     0xa48af0: add             x2, fp, w0, sxtw #2
    //     0xa48af4: ldr             x2, [x2, #0x10]
    //     0xa48af8: stur            x2, [fp, #-0x18]
    //     0xa48afc: ldur            w3, [x1, #0x17]
    //     0xa48b00: add             x3, x3, HEAP, lsl #32
    //     0xa48b04: stur            x3, [fp, #-0x10]
    // 0xa48b08: CheckStackOverflow
    //     0xa48b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48b0c: cmp             SP, x16
    //     0xa48b10: b.ls            #0xa48f9c
    // 0xa48b14: InitAsync() -> Future<Null?>
    //     0xa48b14: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xa48b18: bl              #0x6326e0  ; InitAsyncStub
    // 0xa48b1c: ldur            x0, [fp, #-0x18]
    // 0xa48b20: r1 = LoadClassIdInstr(r0)
    //     0xa48b20: ldur            x1, [x0, #-1]
    //     0xa48b24: ubfx            x1, x1, #0xc, #0x14
    // 0xa48b28: r16 = ""
    //     0xa48b28: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa48b2c: stp             x16, x0, [SP]
    // 0xa48b30: mov             x0, x1
    // 0xa48b34: mov             lr, x0
    // 0xa48b38: ldr             lr, [x21, lr, lsl #3]
    // 0xa48b3c: blr             lr
    // 0xa48b40: tbz             w0, #4, #0xa48d84
    // 0xa48b44: ldur            x2, [fp, #-0x10]
    // 0xa48b48: LoadField: r3 = r2->field_b
    //     0xa48b48: ldur            w3, [x2, #0xb]
    // 0xa48b4c: DecompressPointer r3
    //     0xa48b4c: add             x3, x3, HEAP, lsl #32
    // 0xa48b50: stur            x3, [fp, #-0x28]
    // 0xa48b54: LoadField: r0 = r3->field_f
    //     0xa48b54: ldur            w0, [x3, #0xf]
    // 0xa48b58: DecompressPointer r0
    //     0xa48b58: add             x0, x0, HEAP, lsl #32
    // 0xa48b5c: LoadField: r1 = r0->field_b
    //     0xa48b5c: ldur            w1, [x0, #0xb]
    // 0xa48b60: DecompressPointer r1
    //     0xa48b60: add             x1, x1, HEAP, lsl #32
    // 0xa48b64: cmp             w1, NULL
    // 0xa48b68: b.eq            #0xa48fa4
    // 0xa48b6c: LoadField: r2 = r1->field_f
    //     0xa48b6c: ldur            w2, [x1, #0xf]
    // 0xa48b70: DecompressPointer r2
    //     0xa48b70: add             x2, x2, HEAP, lsl #32
    // 0xa48b74: stur            x2, [fp, #-0x18]
    // 0xa48b78: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xa48b78: ldur            x4, [x1, #0x17]
    // 0xa48b7c: stur            x4, [fp, #-0x20]
    // 0xa48b80: LoadField: r0 = r1->field_27
    //     0xa48b80: ldur            x0, [x1, #0x27]
    // 0xa48b84: mul             x5, x0, x4
    // 0xa48b88: r0 = BoxInt64Instr(r5)
    //     0xa48b88: sbfiz           x0, x5, #1, #0x1f
    //     0xa48b8c: cmp             x5, x0, asr #1
    //     0xa48b90: b.eq            #0xa48b9c
    //     0xa48b94: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48b98: stur            x5, [x0, #7]
    // 0xa48b9c: stp             x0, NULL, [SP]
    // 0xa48ba0: r0 = _Double.fromInteger()
    //     0xa48ba0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa48ba4: stur            x0, [fp, #-0x30]
    // 0xa48ba8: r0 = CheckoutEventData()
    //     0xa48ba8: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa48bac: mov             x2, x0
    // 0xa48bb0: ldur            x0, [fp, #-0x30]
    // 0xa48bb4: stur            x2, [fp, #-0x38]
    // 0xa48bb8: StoreField: r2->field_7 = r0
    //     0xa48bb8: stur            w0, [x2, #7]
    // 0xa48bbc: ldur            x3, [fp, #-0x20]
    // 0xa48bc0: r0 = BoxInt64Instr(r3)
    //     0xa48bc0: sbfiz           x0, x3, #1, #0x1f
    //     0xa48bc4: cmp             x3, x0, asr #1
    //     0xa48bc8: b.eq            #0xa48bd4
    //     0xa48bcc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48bd0: stur            x3, [x0, #7]
    // 0xa48bd4: StoreField: r2->field_b = r0
    //     0xa48bd4: stur            w0, [x2, #0xb]
    // 0xa48bd8: ldur            x0, [fp, #-0x18]
    // 0xa48bdc: StoreField: r2->field_f = r0
    //     0xa48bdc: stur            w0, [x2, #0xf]
    // 0xa48be0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48be0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48be4: ldr             x0, [x0, #0x1c80]
    //     0xa48be8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48bec: cmp             w0, w16
    //     0xa48bf0: b.ne            #0xa48bfc
    //     0xa48bf4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa48bf8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa48bfc: r1 = Null
    //     0xa48bfc: mov             x1, NULL
    // 0xa48c00: r2 = 28
    //     0xa48c00: movz            x2, #0x1c
    // 0xa48c04: r0 = AllocateArray()
    //     0xa48c04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa48c08: mov             x2, x0
    // 0xa48c0c: stur            x2, [fp, #-0x18]
    // 0xa48c10: r16 = "coming_from"
    //     0xa48c10: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa48c14: ldr             x16, [x16, #0x328]
    // 0xa48c18: StoreField: r2->field_f = r16
    //     0xa48c18: stur            w16, [x2, #0xf]
    // 0xa48c1c: r16 = "buy_now"
    //     0xa48c1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "buy_now"
    //     0xa48c20: ldr             x16, [x16, #0xe78]
    // 0xa48c24: StoreField: r2->field_13 = r16
    //     0xa48c24: stur            w16, [x2, #0x13]
    // 0xa48c28: r16 = "couponCode"
    //     0xa48c28: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa48c2c: ldr             x16, [x16, #0x310]
    // 0xa48c30: ArrayStore: r2[0] = r16  ; List_4
    //     0xa48c30: stur            w16, [x2, #0x17]
    // 0xa48c34: r16 = ""
    //     0xa48c34: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa48c38: StoreField: r2->field_1b = r16
    //     0xa48c38: stur            w16, [x2, #0x1b]
    // 0xa48c3c: r16 = "product_id"
    //     0xa48c3c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa48c40: ldr             x16, [x16, #0x9b8]
    // 0xa48c44: StoreField: r2->field_1f = r16
    //     0xa48c44: stur            w16, [x2, #0x1f]
    // 0xa48c48: ldur            x0, [fp, #-0x28]
    // 0xa48c4c: LoadField: r1 = r0->field_f
    //     0xa48c4c: ldur            w1, [x0, #0xf]
    // 0xa48c50: DecompressPointer r1
    //     0xa48c50: add             x1, x1, HEAP, lsl #32
    // 0xa48c54: LoadField: r0 = r1->field_b
    //     0xa48c54: ldur            w0, [x1, #0xb]
    // 0xa48c58: DecompressPointer r0
    //     0xa48c58: add             x0, x0, HEAP, lsl #32
    // 0xa48c5c: cmp             w0, NULL
    // 0xa48c60: b.eq            #0xa48fa8
    // 0xa48c64: LoadField: r1 = r0->field_f
    //     0xa48c64: ldur            w1, [x0, #0xf]
    // 0xa48c68: DecompressPointer r1
    //     0xa48c68: add             x1, x1, HEAP, lsl #32
    // 0xa48c6c: StoreField: r2->field_23 = r1
    //     0xa48c6c: stur            w1, [x2, #0x23]
    // 0xa48c70: r16 = "sku_id"
    //     0xa48c70: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa48c74: ldr             x16, [x16, #0x498]
    // 0xa48c78: StoreField: r2->field_27 = r16
    //     0xa48c78: stur            w16, [x2, #0x27]
    // 0xa48c7c: LoadField: r1 = r0->field_13
    //     0xa48c7c: ldur            w1, [x0, #0x13]
    // 0xa48c80: DecompressPointer r1
    //     0xa48c80: add             x1, x1, HEAP, lsl #32
    // 0xa48c84: StoreField: r2->field_2b = r1
    //     0xa48c84: stur            w1, [x2, #0x2b]
    // 0xa48c88: r16 = "quantity"
    //     0xa48c88: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa48c8c: ldr             x16, [x16, #0x428]
    // 0xa48c90: StoreField: r2->field_2f = r16
    //     0xa48c90: stur            w16, [x2, #0x2f]
    // 0xa48c94: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xa48c94: ldur            x3, [x0, #0x17]
    // 0xa48c98: r0 = BoxInt64Instr(r3)
    //     0xa48c98: sbfiz           x0, x3, #1, #0x1f
    //     0xa48c9c: cmp             x3, x0, asr #1
    //     0xa48ca0: b.eq            #0xa48cac
    //     0xa48ca4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48ca8: stur            x3, [x0, #7]
    // 0xa48cac: r1 = 60
    //     0xa48cac: movz            x1, #0x3c
    // 0xa48cb0: branchIfSmi(r0, 0xa48cbc)
    //     0xa48cb0: tbz             w0, #0, #0xa48cbc
    // 0xa48cb4: r1 = LoadClassIdInstr(r0)
    //     0xa48cb4: ldur            x1, [x0, #-1]
    //     0xa48cb8: ubfx            x1, x1, #0xc, #0x14
    // 0xa48cbc: str             x0, [SP]
    // 0xa48cc0: mov             x0, x1
    // 0xa48cc4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa48cc4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa48cc8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa48cc8: movz            x17, #0x2700
    //     0xa48ccc: add             lr, x0, x17
    //     0xa48cd0: ldr             lr, [x21, lr, lsl #3]
    //     0xa48cd4: blr             lr
    // 0xa48cd8: ldur            x1, [fp, #-0x18]
    // 0xa48cdc: ArrayStore: r1[9] = r0  ; List_4
    //     0xa48cdc: add             x25, x1, #0x33
    //     0xa48ce0: str             w0, [x25]
    //     0xa48ce4: tbz             w0, #0, #0xa48d00
    //     0xa48ce8: ldurb           w16, [x1, #-1]
    //     0xa48cec: ldurb           w17, [x0, #-1]
    //     0xa48cf0: and             x16, x17, x16, lsr #2
    //     0xa48cf4: tst             x16, HEAP, lsr #32
    //     0xa48cf8: b.eq            #0xa48d00
    //     0xa48cfc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48d00: ldur            x2, [fp, #-0x18]
    // 0xa48d04: r16 = "previousScreenSource"
    //     0xa48d04: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa48d08: ldr             x16, [x16, #0x448]
    // 0xa48d0c: StoreField: r2->field_37 = r16
    //     0xa48d0c: stur            w16, [x2, #0x37]
    // 0xa48d10: r16 = "product_page"
    //     0xa48d10: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa48d14: ldr             x16, [x16, #0x480]
    // 0xa48d18: StoreField: r2->field_3b = r16
    //     0xa48d18: stur            w16, [x2, #0x3b]
    // 0xa48d1c: r16 = "checkout_event_data"
    //     0xa48d1c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa48d20: ldr             x16, [x16, #0xd50]
    // 0xa48d24: StoreField: r2->field_3f = r16
    //     0xa48d24: stur            w16, [x2, #0x3f]
    // 0xa48d28: mov             x1, x2
    // 0xa48d2c: ldur            x0, [fp, #-0x38]
    // 0xa48d30: ArrayStore: r1[13] = r0  ; List_4
    //     0xa48d30: add             x25, x1, #0x43
    //     0xa48d34: str             w0, [x25]
    //     0xa48d38: tbz             w0, #0, #0xa48d54
    //     0xa48d3c: ldurb           w16, [x1, #-1]
    //     0xa48d40: ldurb           w17, [x0, #-1]
    //     0xa48d44: and             x16, x17, x16, lsr #2
    //     0xa48d48: tst             x16, HEAP, lsr #32
    //     0xa48d4c: b.eq            #0xa48d54
    //     0xa48d50: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48d54: r16 = <String, Object?>
    //     0xa48d54: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa48d58: ldr             x16, [x16, #0xc28]
    // 0xa48d5c: stp             x2, x16, [SP]
    // 0xa48d60: r0 = Map._fromLiteral()
    //     0xa48d60: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa48d64: r16 = "/exchange-checkout"
    //     0xa48d64: add             x16, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0xa48d68: ldr             x16, [x16, #0x988]
    // 0xa48d6c: stp             x16, NULL, [SP, #8]
    // 0xa48d70: str             x0, [SP]
    // 0xa48d74: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa48d74: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa48d78: ldr             x4, [x4, #0x438]
    // 0xa48d7c: r0 = GetNavigation.toNamed()
    //     0xa48d7c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa48d80: b               #0xa48f94
    // 0xa48d84: ldur            x2, [fp, #-0x10]
    // 0xa48d88: LoadField: r3 = r2->field_b
    //     0xa48d88: ldur            w3, [x2, #0xb]
    // 0xa48d8c: DecompressPointer r3
    //     0xa48d8c: add             x3, x3, HEAP, lsl #32
    // 0xa48d90: stur            x3, [fp, #-0x28]
    // 0xa48d94: LoadField: r0 = r3->field_f
    //     0xa48d94: ldur            w0, [x3, #0xf]
    // 0xa48d98: DecompressPointer r0
    //     0xa48d98: add             x0, x0, HEAP, lsl #32
    // 0xa48d9c: LoadField: r1 = r0->field_b
    //     0xa48d9c: ldur            w1, [x0, #0xb]
    // 0xa48da0: DecompressPointer r1
    //     0xa48da0: add             x1, x1, HEAP, lsl #32
    // 0xa48da4: cmp             w1, NULL
    // 0xa48da8: b.eq            #0xa48fac
    // 0xa48dac: LoadField: r4 = r1->field_f
    //     0xa48dac: ldur            w4, [x1, #0xf]
    // 0xa48db0: DecompressPointer r4
    //     0xa48db0: add             x4, x4, HEAP, lsl #32
    // 0xa48db4: stur            x4, [fp, #-0x18]
    // 0xa48db8: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xa48db8: ldur            x5, [x1, #0x17]
    // 0xa48dbc: stur            x5, [fp, #-0x20]
    // 0xa48dc0: LoadField: r0 = r1->field_27
    //     0xa48dc0: ldur            x0, [x1, #0x27]
    // 0xa48dc4: mul             x6, x0, x5
    // 0xa48dc8: r0 = BoxInt64Instr(r6)
    //     0xa48dc8: sbfiz           x0, x6, #1, #0x1f
    //     0xa48dcc: cmp             x6, x0, asr #1
    //     0xa48dd0: b.eq            #0xa48ddc
    //     0xa48dd4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48dd8: stur            x6, [x0, #7]
    // 0xa48ddc: stp             x0, NULL, [SP]
    // 0xa48de0: r0 = _Double.fromInteger()
    //     0xa48de0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa48de4: stur            x0, [fp, #-0x30]
    // 0xa48de8: r0 = CheckoutEventData()
    //     0xa48de8: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa48dec: mov             x2, x0
    // 0xa48df0: ldur            x0, [fp, #-0x30]
    // 0xa48df4: stur            x2, [fp, #-0x38]
    // 0xa48df8: StoreField: r2->field_7 = r0
    //     0xa48df8: stur            w0, [x2, #7]
    // 0xa48dfc: ldur            x3, [fp, #-0x20]
    // 0xa48e00: r0 = BoxInt64Instr(r3)
    //     0xa48e00: sbfiz           x0, x3, #1, #0x1f
    //     0xa48e04: cmp             x3, x0, asr #1
    //     0xa48e08: b.eq            #0xa48e14
    //     0xa48e0c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48e10: stur            x3, [x0, #7]
    // 0xa48e14: StoreField: r2->field_b = r0
    //     0xa48e14: stur            w0, [x2, #0xb]
    // 0xa48e18: ldur            x0, [fp, #-0x18]
    // 0xa48e1c: StoreField: r2->field_f = r0
    //     0xa48e1c: stur            w0, [x2, #0xf]
    // 0xa48e20: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48e20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48e24: ldr             x0, [x0, #0x1c80]
    //     0xa48e28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48e2c: cmp             w0, w16
    //     0xa48e30: b.ne            #0xa48e3c
    //     0xa48e34: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa48e38: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa48e3c: r1 = Null
    //     0xa48e3c: mov             x1, NULL
    // 0xa48e40: r2 = 24
    //     0xa48e40: movz            x2, #0x18
    // 0xa48e44: r0 = AllocateArray()
    //     0xa48e44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa48e48: mov             x2, x0
    // 0xa48e4c: stur            x2, [fp, #-0x18]
    // 0xa48e50: r16 = "previousScreenSource"
    //     0xa48e50: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa48e54: ldr             x16, [x16, #0x448]
    // 0xa48e58: StoreField: r2->field_f = r16
    //     0xa48e58: stur            w16, [x2, #0xf]
    // 0xa48e5c: r16 = "product_page"
    //     0xa48e5c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa48e60: ldr             x16, [x16, #0x480]
    // 0xa48e64: StoreField: r2->field_13 = r16
    //     0xa48e64: stur            w16, [x2, #0x13]
    // 0xa48e68: r16 = "checkout_event_data"
    //     0xa48e68: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa48e6c: ldr             x16, [x16, #0xd50]
    // 0xa48e70: ArrayStore: r2[0] = r16  ; List_4
    //     0xa48e70: stur            w16, [x2, #0x17]
    // 0xa48e74: ldur            x0, [fp, #-0x38]
    // 0xa48e78: StoreField: r2->field_1b = r0
    //     0xa48e78: stur            w0, [x2, #0x1b]
    // 0xa48e7c: r16 = "product_id"
    //     0xa48e7c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa48e80: ldr             x16, [x16, #0x9b8]
    // 0xa48e84: StoreField: r2->field_1f = r16
    //     0xa48e84: stur            w16, [x2, #0x1f]
    // 0xa48e88: ldur            x0, [fp, #-0x28]
    // 0xa48e8c: LoadField: r1 = r0->field_f
    //     0xa48e8c: ldur            w1, [x0, #0xf]
    // 0xa48e90: DecompressPointer r1
    //     0xa48e90: add             x1, x1, HEAP, lsl #32
    // 0xa48e94: LoadField: r0 = r1->field_b
    //     0xa48e94: ldur            w0, [x1, #0xb]
    // 0xa48e98: DecompressPointer r0
    //     0xa48e98: add             x0, x0, HEAP, lsl #32
    // 0xa48e9c: cmp             w0, NULL
    // 0xa48ea0: b.eq            #0xa48fb0
    // 0xa48ea4: LoadField: r1 = r0->field_f
    //     0xa48ea4: ldur            w1, [x0, #0xf]
    // 0xa48ea8: DecompressPointer r1
    //     0xa48ea8: add             x1, x1, HEAP, lsl #32
    // 0xa48eac: StoreField: r2->field_23 = r1
    //     0xa48eac: stur            w1, [x2, #0x23]
    // 0xa48eb0: r16 = "sku_id"
    //     0xa48eb0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa48eb4: ldr             x16, [x16, #0x498]
    // 0xa48eb8: StoreField: r2->field_27 = r16
    //     0xa48eb8: stur            w16, [x2, #0x27]
    // 0xa48ebc: LoadField: r1 = r0->field_13
    //     0xa48ebc: ldur            w1, [x0, #0x13]
    // 0xa48ec0: DecompressPointer r1
    //     0xa48ec0: add             x1, x1, HEAP, lsl #32
    // 0xa48ec4: StoreField: r2->field_2b = r1
    //     0xa48ec4: stur            w1, [x2, #0x2b]
    // 0xa48ec8: r16 = "quantity"
    //     0xa48ec8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa48ecc: ldr             x16, [x16, #0x428]
    // 0xa48ed0: StoreField: r2->field_2f = r16
    //     0xa48ed0: stur            w16, [x2, #0x2f]
    // 0xa48ed4: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xa48ed4: ldur            x3, [x0, #0x17]
    // 0xa48ed8: r0 = BoxInt64Instr(r3)
    //     0xa48ed8: sbfiz           x0, x3, #1, #0x1f
    //     0xa48edc: cmp             x3, x0, asr #1
    //     0xa48ee0: b.eq            #0xa48eec
    //     0xa48ee4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa48ee8: stur            x3, [x0, #7]
    // 0xa48eec: r1 = 60
    //     0xa48eec: movz            x1, #0x3c
    // 0xa48ef0: branchIfSmi(r0, 0xa48efc)
    //     0xa48ef0: tbz             w0, #0, #0xa48efc
    // 0xa48ef4: r1 = LoadClassIdInstr(r0)
    //     0xa48ef4: ldur            x1, [x0, #-1]
    //     0xa48ef8: ubfx            x1, x1, #0xc, #0x14
    // 0xa48efc: str             x0, [SP]
    // 0xa48f00: mov             x0, x1
    // 0xa48f04: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa48f04: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa48f08: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa48f08: movz            x17, #0x2700
    //     0xa48f0c: add             lr, x0, x17
    //     0xa48f10: ldr             lr, [x21, lr, lsl #3]
    //     0xa48f14: blr             lr
    // 0xa48f18: ldur            x1, [fp, #-0x18]
    // 0xa48f1c: ArrayStore: r1[9] = r0  ; List_4
    //     0xa48f1c: add             x25, x1, #0x33
    //     0xa48f20: str             w0, [x25]
    //     0xa48f24: tbz             w0, #0, #0xa48f40
    //     0xa48f28: ldurb           w16, [x1, #-1]
    //     0xa48f2c: ldurb           w17, [x0, #-1]
    //     0xa48f30: and             x16, x17, x16, lsr #2
    //     0xa48f34: tst             x16, HEAP, lsr #32
    //     0xa48f38: b.eq            #0xa48f40
    //     0xa48f3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa48f40: ldur            x0, [fp, #-0x18]
    // 0xa48f44: r16 = "coming_from"
    //     0xa48f44: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa48f48: ldr             x16, [x16, #0x328]
    // 0xa48f4c: StoreField: r0->field_37 = r16
    //     0xa48f4c: stur            w16, [x0, #0x37]
    // 0xa48f50: r16 = "bag"
    //     0xa48f50: add             x16, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0xa48f54: ldr             x16, [x16, #0x460]
    // 0xa48f58: StoreField: r0->field_3b = r16
    //     0xa48f58: stur            w16, [x0, #0x3b]
    // 0xa48f5c: r16 = <String, Object?>
    //     0xa48f5c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa48f60: ldr             x16, [x16, #0xc28]
    // 0xa48f64: stp             x0, x16, [SP]
    // 0xa48f68: r0 = Map._fromLiteral()
    //     0xa48f68: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa48f6c: r16 = "/login"
    //     0xa48f6c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0xa48f70: ldr             x16, [x16, #0x880]
    // 0xa48f74: stp             x16, NULL, [SP, #8]
    // 0xa48f78: str             x0, [SP]
    // 0xa48f7c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa48f7c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa48f80: ldr             x4, [x4, #0x438]
    // 0xa48f84: r0 = GetNavigation.toNamed()
    //     0xa48f84: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa48f88: mov             x1, x0
    // 0xa48f8c: stur            x1, [fp, #-0x18]
    // 0xa48f90: r0 = Await()
    //     0xa48f90: bl              #0x63248c  ; AwaitStub
    // 0xa48f94: r0 = Null
    //     0xa48f94: mov             x0, NULL
    // 0xa48f98: r0 = ReturnAsyncNotFuture()
    //     0xa48f98: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xa48f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48f9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48fa0: b               #0xa48b14
    // 0xa48fa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48fa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48fa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48fa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48fac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48fac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa48fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa48fb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xa48fb4, size: 0xcc
    // 0xa48fb4: EnterFrame
    //     0xa48fb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa48fb8: mov             fp, SP
    // 0xa48fbc: AllocStack(0x20)
    //     0xa48fbc: sub             SP, SP, #0x20
    // 0xa48fc0: SetupParameters()
    //     0xa48fc0: ldr             x0, [fp, #0x10]
    //     0xa48fc4: ldur            w2, [x0, #0x17]
    //     0xa48fc8: add             x2, x2, HEAP, lsl #32
    //     0xa48fcc: stur            x2, [fp, #-8]
    // 0xa48fd0: CheckStackOverflow
    //     0xa48fd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48fd4: cmp             SP, x16
    //     0xa48fd8: b.ls            #0xa49078
    // 0xa48fdc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa48fdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa48fe0: ldr             x0, [x0, #0x1c80]
    //     0xa48fe4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa48fe8: cmp             w0, w16
    //     0xa48fec: b.ne            #0xa48ff8
    //     0xa48ff0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa48ff4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa48ff8: str             NULL, [SP]
    // 0xa48ffc: r4 = const [0x1, 0, 0, 0, null]
    //     0xa48ffc: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49000: r0 = GetNavigation.back()
    //     0xa49000: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa49004: r16 = PreferenceManager
    //     0xa49004: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa49008: ldr             x16, [x16, #0x878]
    // 0xa4900c: str             x16, [SP]
    // 0xa49010: r0 = toString()
    //     0xa49010: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa49014: r16 = <PreferenceManager>
    //     0xa49014: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa49018: ldr             x16, [x16, #0x880]
    // 0xa4901c: stp             x0, x16, [SP]
    // 0xa49020: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa49020: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa49024: r0 = Inst.find()
    //     0xa49024: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa49028: mov             x1, x0
    // 0xa4902c: r2 = "token"
    //     0xa4902c: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa49030: ldr             x2, [x2, #0x958]
    // 0xa49034: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa49034: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa49038: r0 = getString()
    //     0xa49038: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa4903c: ldur            x2, [fp, #-8]
    // 0xa49040: r1 = Function '<anonymous closure>':.
    //     0xa49040: add             x1, PP, #0x56, lsl #12  ; [pp+0x56498] AnonymousClosure: (0xa48ad4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa49044: ldr             x1, [x1, #0x498]
    // 0xa49048: stur            x0, [fp, #-8]
    // 0xa4904c: r0 = AllocateClosure()
    //     0xa4904c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa49050: r16 = <Null?>
    //     0xa49050: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa49054: ldur            lr, [fp, #-8]
    // 0xa49058: stp             lr, x16, [SP, #8]
    // 0xa4905c: str             x0, [SP]
    // 0xa49060: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa49060: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa49064: r0 = then()
    //     0xa49064: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa49068: r0 = Null
    //     0xa49068: mov             x0, NULL
    // 0xa4906c: LeaveFrame
    //     0xa4906c: mov             SP, fp
    //     0xa49070: ldp             fp, lr, [SP], #0x10
    // 0xa49074: ret
    //     0xa49074: ret             
    // 0xa49078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4907c: b               #0xa48fdc
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa49080, size: 0x1b8
    // 0xa49080: EnterFrame
    //     0xa49080: stp             fp, lr, [SP, #-0x10]!
    //     0xa49084: mov             fp, SP
    // 0xa49088: AllocStack(0x48)
    //     0xa49088: sub             SP, SP, #0x48
    // 0xa4908c: SetupParameters()
    //     0xa4908c: ldr             x0, [fp, #0x18]
    //     0xa49090: ldur            w2, [x0, #0x17]
    //     0xa49094: add             x2, x2, HEAP, lsl #32
    //     0xa49098: stur            x2, [fp, #-0x28]
    // 0xa4909c: CheckStackOverflow
    //     0xa4909c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa490a0: cmp             SP, x16
    //     0xa490a4: b.ls            #0xa4922c
    // 0xa490a8: LoadField: r0 = r2->field_b
    //     0xa490a8: ldur            w0, [x2, #0xb]
    // 0xa490ac: DecompressPointer r0
    //     0xa490ac: add             x0, x0, HEAP, lsl #32
    // 0xa490b0: LoadField: r1 = r0->field_f
    //     0xa490b0: ldur            w1, [x0, #0xf]
    // 0xa490b4: DecompressPointer r1
    //     0xa490b4: add             x1, x1, HEAP, lsl #32
    // 0xa490b8: LoadField: r0 = r1->field_b
    //     0xa490b8: ldur            w0, [x1, #0xb]
    // 0xa490bc: DecompressPointer r0
    //     0xa490bc: add             x0, x0, HEAP, lsl #32
    // 0xa490c0: cmp             w0, NULL
    // 0xa490c4: b.eq            #0xa49234
    // 0xa490c8: LoadField: r3 = r0->field_47
    //     0xa490c8: ldur            w3, [x0, #0x47]
    // 0xa490cc: DecompressPointer r3
    //     0xa490cc: add             x3, x3, HEAP, lsl #32
    // 0xa490d0: stur            x3, [fp, #-0x20]
    // 0xa490d4: LoadField: r4 = r0->field_37
    //     0xa490d4: ldur            w4, [x0, #0x37]
    // 0xa490d8: DecompressPointer r4
    //     0xa490d8: add             x4, x4, HEAP, lsl #32
    // 0xa490dc: stur            x4, [fp, #-0x18]
    // 0xa490e0: LoadField: r5 = r0->field_3b
    //     0xa490e0: ldur            w5, [x0, #0x3b]
    // 0xa490e4: DecompressPointer r5
    //     0xa490e4: add             x5, x5, HEAP, lsl #32
    // 0xa490e8: stur            x5, [fp, #-0x10]
    // 0xa490ec: LoadField: r6 = r0->field_3f
    //     0xa490ec: ldur            w6, [x0, #0x3f]
    // 0xa490f0: DecompressPointer r6
    //     0xa490f0: add             x6, x6, HEAP, lsl #32
    // 0xa490f4: stur            x6, [fp, #-8]
    // 0xa490f8: ArrayLoad: r7 = r0[0]  ; List_8
    //     0xa490f8: ldur            x7, [x0, #0x17]
    // 0xa490fc: r0 = BoxInt64Instr(r7)
    //     0xa490fc: sbfiz           x0, x7, #1, #0x1f
    //     0xa49100: cmp             x7, x0, asr #1
    //     0xa49104: b.eq            #0xa49110
    //     0xa49108: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4910c: stur            x7, [x0, #7]
    // 0xa49110: r1 = 60
    //     0xa49110: movz            x1, #0x3c
    // 0xa49114: branchIfSmi(r0, 0xa49120)
    //     0xa49114: tbz             w0, #0, #0xa49120
    // 0xa49118: r1 = LoadClassIdInstr(r0)
    //     0xa49118: ldur            x1, [x0, #-1]
    //     0xa4911c: ubfx            x1, x1, #0xc, #0x14
    // 0xa49120: str             x0, [SP]
    // 0xa49124: mov             x0, x1
    // 0xa49128: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa49128: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa4912c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa4912c: movz            x17, #0x2700
    //     0xa49130: add             lr, x0, x17
    //     0xa49134: ldr             lr, [x21, lr, lsl #3]
    //     0xa49138: blr             lr
    // 0xa4913c: ldur            x2, [fp, #-0x28]
    // 0xa49140: stur            x0, [fp, #-0x30]
    // 0xa49144: LoadField: r1 = r2->field_f
    //     0xa49144: ldur            w1, [x2, #0xf]
    // 0xa49148: DecompressPointer r1
    //     0xa49148: add             x1, x1, HEAP, lsl #32
    // 0xa4914c: r16 = Sentinel
    //     0xa4914c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa49150: cmp             w1, w16
    // 0xa49154: b.eq            #0xa4921c
    // 0xa49158: ldur            x3, [fp, #-0x20]
    // 0xa4915c: ldur            x4, [fp, #-0x18]
    // 0xa49160: ldur            x5, [fp, #-0x10]
    // 0xa49164: ldur            x6, [fp, #-8]
    // 0xa49168: str             x1, [SP]
    // 0xa4916c: r4 = 0
    //     0xa4916c: movz            x4, #0
    // 0xa49170: ldr             x0, [SP]
    // 0xa49174: r16 = UnlinkedCall_0x613b5c
    //     0xa49174: add             x16, PP, #0x56, lsl #12  ; [pp+0x56470] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49178: add             x16, x16, #0x470
    // 0xa4917c: ldp             x5, lr, [x16]
    // 0xa49180: blr             lr
    // 0xa49184: stur            x0, [fp, #-0x38]
    // 0xa49188: r0 = SingleExchangeProductBottomSheet()
    //     0xa49188: bl              #0xa49238  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0xa4918c: mov             x3, x0
    // 0xa49190: ldur            x0, [fp, #-0x20]
    // 0xa49194: stur            x3, [fp, #-0x40]
    // 0xa49198: StoreField: r3->field_b = r0
    //     0xa49198: stur            w0, [x3, #0xb]
    // 0xa4919c: ldur            x0, [fp, #-0x18]
    // 0xa491a0: StoreField: r3->field_f = r0
    //     0xa491a0: stur            w0, [x3, #0xf]
    // 0xa491a4: ldur            x0, [fp, #-0x10]
    // 0xa491a8: ArrayStore: r3[0] = r0  ; List_4
    //     0xa491a8: stur            w0, [x3, #0x17]
    // 0xa491ac: ldur            x0, [fp, #-8]
    // 0xa491b0: StoreField: r3->field_13 = r0
    //     0xa491b0: stur            w0, [x3, #0x13]
    // 0xa491b4: ldur            x0, [fp, #-0x30]
    // 0xa491b8: StoreField: r3->field_1b = r0
    //     0xa491b8: stur            w0, [x3, #0x1b]
    // 0xa491bc: ldur            x2, [fp, #-0x28]
    // 0xa491c0: r1 = Function '<anonymous closure>':.
    //     0xa491c0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56480] AnonymousClosure: (0xa49264), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa491c4: ldr             x1, [x1, #0x480]
    // 0xa491c8: r0 = AllocateClosure()
    //     0xa491c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa491cc: mov             x1, x0
    // 0xa491d0: ldur            x0, [fp, #-0x40]
    // 0xa491d4: StoreField: r0->field_1f = r1
    //     0xa491d4: stur            w1, [x0, #0x1f]
    // 0xa491d8: ldur            x2, [fp, #-0x28]
    // 0xa491dc: r1 = Function '<anonymous closure>':.
    //     0xa491dc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56488] AnonymousClosure: (0xa48fb4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa491e0: ldr             x1, [x1, #0x488]
    // 0xa491e4: r0 = AllocateClosure()
    //     0xa491e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa491e8: mov             x1, x0
    // 0xa491ec: ldur            x0, [fp, #-0x40]
    // 0xa491f0: StoreField: r0->field_23 = r1
    //     0xa491f0: stur            w1, [x0, #0x23]
    // 0xa491f4: r1 = const []
    //     0xa491f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56490] List<ProductCustomisation>(0)
    //     0xa491f8: ldr             x1, [x1, #0x490]
    // 0xa491fc: StoreField: r0->field_2b = r1
    //     0xa491fc: stur            w1, [x0, #0x2b]
    // 0xa49200: r1 = ""
    //     0xa49200: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa49204: StoreField: r0->field_27 = r1
    //     0xa49204: stur            w1, [x0, #0x27]
    // 0xa49208: ldur            x1, [fp, #-0x38]
    // 0xa4920c: StoreField: r0->field_2f = r1
    //     0xa4920c: stur            w1, [x0, #0x2f]
    // 0xa49210: LeaveFrame
    //     0xa49210: mov             SP, fp
    //     0xa49214: ldp             fp, lr, [SP], #0x10
    // 0xa49218: ret
    //     0xa49218: ret             
    // 0xa4921c: r16 = "controller"
    //     0xa4921c: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49220: str             x16, [SP]
    // 0xa49224: r0 = _throwLocalNotInitialized()
    //     0xa49224: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49228: brk             #0
    // 0xa4922c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4922c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49230: b               #0xa490a8
    // 0xa49234: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49234: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xa49264, size: 0xcc
    // 0xa49264: EnterFrame
    //     0xa49264: stp             fp, lr, [SP, #-0x10]!
    //     0xa49268: mov             fp, SP
    // 0xa4926c: AllocStack(0x20)
    //     0xa4926c: sub             SP, SP, #0x20
    // 0xa49270: SetupParameters()
    //     0xa49270: ldr             x0, [fp, #0x10]
    //     0xa49274: ldur            w2, [x0, #0x17]
    //     0xa49278: add             x2, x2, HEAP, lsl #32
    //     0xa4927c: stur            x2, [fp, #-8]
    // 0xa49280: CheckStackOverflow
    //     0xa49280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49284: cmp             SP, x16
    //     0xa49288: b.ls            #0xa49328
    // 0xa4928c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4928c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49290: ldr             x0, [x0, #0x1c80]
    //     0xa49294: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49298: cmp             w0, w16
    //     0xa4929c: b.ne            #0xa492a8
    //     0xa492a0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa492a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa492a8: str             NULL, [SP]
    // 0xa492ac: r4 = const [0x1, 0, 0, 0, null]
    //     0xa492ac: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa492b0: r0 = GetNavigation.back()
    //     0xa492b0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa492b4: r16 = PreferenceManager
    //     0xa492b4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa492b8: ldr             x16, [x16, #0x878]
    // 0xa492bc: str             x16, [SP]
    // 0xa492c0: r0 = toString()
    //     0xa492c0: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa492c4: r16 = <PreferenceManager>
    //     0xa492c4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa492c8: ldr             x16, [x16, #0x880]
    // 0xa492cc: stp             x0, x16, [SP]
    // 0xa492d0: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa492d0: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa492d4: r0 = Inst.find()
    //     0xa492d4: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa492d8: mov             x1, x0
    // 0xa492dc: r2 = "token"
    //     0xa492dc: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa492e0: ldr             x2, [x2, #0x958]
    // 0xa492e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa492e4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa492e8: r0 = getString()
    //     0xa492e8: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa492ec: ldur            x2, [fp, #-8]
    // 0xa492f0: r1 = Function '<anonymous closure>':.
    //     0xa492f0: add             x1, PP, #0x56, lsl #12  ; [pp+0x564a0] AnonymousClosure: (0xa49330), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa492f4: ldr             x1, [x1, #0x4a0]
    // 0xa492f8: stur            x0, [fp, #-8]
    // 0xa492fc: r0 = AllocateClosure()
    //     0xa492fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa49300: r16 = <Null?>
    //     0xa49300: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa49304: ldur            lr, [fp, #-8]
    // 0xa49308: stp             lr, x16, [SP, #8]
    // 0xa4930c: str             x0, [SP]
    // 0xa49310: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa49310: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa49314: r0 = then()
    //     0xa49314: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa49318: r0 = Null
    //     0xa49318: mov             x0, NULL
    // 0xa4931c: LeaveFrame
    //     0xa4931c: mov             SP, fp
    //     0xa49320: ldp             fp, lr, [SP], #0x10
    // 0xa49324: ret
    //     0xa49324: ret             
    // 0xa49328: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49328: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4932c: b               #0xa4928c
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xa49330, size: 0x9f8
    // 0xa49330: EnterFrame
    //     0xa49330: stp             fp, lr, [SP, #-0x10]!
    //     0xa49334: mov             fp, SP
    // 0xa49338: AllocStack(0x50)
    //     0xa49338: sub             SP, SP, #0x50
    // 0xa4933c: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xa4933c: stur            NULL, [fp, #-8]
    //     0xa49340: movz            x0, #0
    //     0xa49344: add             x1, fp, w0, sxtw #2
    //     0xa49348: ldr             x1, [x1, #0x18]
    //     0xa4934c: add             x2, fp, w0, sxtw #2
    //     0xa49350: ldr             x2, [x2, #0x10]
    //     0xa49354: stur            x2, [fp, #-0x18]
    //     0xa49358: ldur            w3, [x1, #0x17]
    //     0xa4935c: add             x3, x3, HEAP, lsl #32
    //     0xa49360: stur            x3, [fp, #-0x10]
    // 0xa49364: CheckStackOverflow
    //     0xa49364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49368: cmp             SP, x16
    //     0xa4936c: b.ls            #0xa49d0c
    // 0xa49370: InitAsync() -> Future<Null?>
    //     0xa49370: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xa49374: bl              #0x6326e0  ; InitAsyncStub
    // 0xa49378: ldur            x0, [fp, #-0x18]
    // 0xa4937c: r1 = LoadClassIdInstr(r0)
    //     0xa4937c: ldur            x1, [x0, #-1]
    //     0xa49380: ubfx            x1, x1, #0xc, #0x14
    // 0xa49384: r16 = ""
    //     0xa49384: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa49388: stp             x16, x0, [SP]
    // 0xa4938c: mov             x0, x1
    // 0xa49390: mov             lr, x0
    // 0xa49394: ldr             lr, [x21, lr, lsl #3]
    // 0xa49398: blr             lr
    // 0xa4939c: tbz             w0, #4, #0xa49a64
    // 0xa493a0: ldur            x2, [fp, #-0x10]
    // 0xa493a4: LoadField: r3 = r2->field_b
    //     0xa493a4: ldur            w3, [x2, #0xb]
    // 0xa493a8: DecompressPointer r3
    //     0xa493a8: add             x3, x3, HEAP, lsl #32
    // 0xa493ac: stur            x3, [fp, #-0x28]
    // 0xa493b0: LoadField: r0 = r3->field_f
    //     0xa493b0: ldur            w0, [x3, #0xf]
    // 0xa493b4: DecompressPointer r0
    //     0xa493b4: add             x0, x0, HEAP, lsl #32
    // 0xa493b8: LoadField: r1 = r0->field_b
    //     0xa493b8: ldur            w1, [x0, #0xb]
    // 0xa493bc: DecompressPointer r1
    //     0xa493bc: add             x1, x1, HEAP, lsl #32
    // 0xa493c0: cmp             w1, NULL
    // 0xa493c4: b.eq            #0xa49d14
    // 0xa493c8: LoadField: r4 = r1->field_f
    //     0xa493c8: ldur            w4, [x1, #0xf]
    // 0xa493cc: DecompressPointer r4
    //     0xa493cc: add             x4, x4, HEAP, lsl #32
    // 0xa493d0: stur            x4, [fp, #-0x18]
    // 0xa493d4: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xa493d4: ldur            x5, [x1, #0x17]
    // 0xa493d8: stur            x5, [fp, #-0x20]
    // 0xa493dc: LoadField: r0 = r1->field_27
    //     0xa493dc: ldur            x0, [x1, #0x27]
    // 0xa493e0: mul             x6, x0, x5
    // 0xa493e4: r0 = BoxInt64Instr(r6)
    //     0xa493e4: sbfiz           x0, x6, #1, #0x1f
    //     0xa493e8: cmp             x6, x0, asr #1
    //     0xa493ec: b.eq            #0xa493f8
    //     0xa493f0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa493f4: stur            x6, [x0, #7]
    // 0xa493f8: stp             x0, NULL, [SP]
    // 0xa493fc: r0 = _Double.fromInteger()
    //     0xa493fc: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa49400: stur            x0, [fp, #-0x30]
    // 0xa49404: r0 = CheckoutEventData()
    //     0xa49404: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa49408: mov             x2, x0
    // 0xa4940c: ldur            x0, [fp, #-0x30]
    // 0xa49410: stur            x2, [fp, #-0x38]
    // 0xa49414: StoreField: r2->field_7 = r0
    //     0xa49414: stur            w0, [x2, #7]
    // 0xa49418: ldur            x3, [fp, #-0x20]
    // 0xa4941c: r0 = BoxInt64Instr(r3)
    //     0xa4941c: sbfiz           x0, x3, #1, #0x1f
    //     0xa49420: cmp             x3, x0, asr #1
    //     0xa49424: b.eq            #0xa49430
    //     0xa49428: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4942c: stur            x3, [x0, #7]
    // 0xa49430: StoreField: r2->field_b = r0
    //     0xa49430: stur            w0, [x2, #0xb]
    // 0xa49434: ldur            x0, [fp, #-0x18]
    // 0xa49438: StoreField: r2->field_f = r0
    //     0xa49438: stur            w0, [x2, #0xf]
    // 0xa4943c: ldur            x0, [fp, #-0x10]
    // 0xa49440: LoadField: r1 = r0->field_f
    //     0xa49440: ldur            w1, [x0, #0xf]
    // 0xa49444: DecompressPointer r1
    //     0xa49444: add             x1, x1, HEAP, lsl #32
    // 0xa49448: r16 = Sentinel
    //     0xa49448: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4944c: cmp             w1, w16
    // 0xa49450: b.eq            #0xa49cac
    // 0xa49454: str             x1, [SP]
    // 0xa49458: r4 = 0
    //     0xa49458: movz            x4, #0
    // 0xa4945c: ldr             x0, [SP]
    // 0xa49460: r16 = UnlinkedCall_0x613b5c
    //     0xa49460: add             x16, PP, #0x56, lsl #12  ; [pp+0x564a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49464: add             x16, x16, #0x4a8
    // 0xa49468: ldp             x5, lr, [x16]
    // 0xa4946c: blr             lr
    // 0xa49470: LoadField: r1 = r0->field_1b
    //     0xa49470: ldur            w1, [x0, #0x1b]
    // 0xa49474: DecompressPointer r1
    //     0xa49474: add             x1, x1, HEAP, lsl #32
    // 0xa49478: cmp             w1, NULL
    // 0xa4947c: b.ne            #0xa49488
    // 0xa49480: r0 = Null
    //     0xa49480: mov             x0, NULL
    // 0xa49484: b               #0xa494a0
    // 0xa49488: LoadField: r0 = r1->field_b
    //     0xa49488: ldur            w0, [x1, #0xb]
    // 0xa4948c: cbz             w0, #0xa49498
    // 0xa49490: r1 = false
    //     0xa49490: add             x1, NULL, #0x30  ; false
    // 0xa49494: b               #0xa4949c
    // 0xa49498: r1 = true
    //     0xa49498: add             x1, NULL, #0x20  ; true
    // 0xa4949c: mov             x0, x1
    // 0xa494a0: cmp             w0, NULL
    // 0xa494a4: b.eq            #0xa494ac
    // 0xa494a8: tbz             w0, #4, #0xa49554
    // 0xa494ac: ldur            x0, [fp, #-0x10]
    // 0xa494b0: LoadField: r1 = r0->field_f
    //     0xa494b0: ldur            w1, [x0, #0xf]
    // 0xa494b4: DecompressPointer r1
    //     0xa494b4: add             x1, x1, HEAP, lsl #32
    // 0xa494b8: r16 = Sentinel
    //     0xa494b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa494bc: cmp             w1, w16
    // 0xa494c0: b.eq            #0xa49cbc
    // 0xa494c4: str             x1, [SP]
    // 0xa494c8: r4 = 0
    //     0xa494c8: movz            x4, #0
    // 0xa494cc: ldr             x0, [SP]
    // 0xa494d0: r16 = UnlinkedCall_0x613b5c
    //     0xa494d0: add             x16, PP, #0x56, lsl #12  ; [pp+0x564b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa494d4: add             x16, x16, #0x4b8
    // 0xa494d8: ldp             x5, lr, [x16]
    // 0xa494dc: blr             lr
    // 0xa494e0: LoadField: r1 = r0->field_1b
    //     0xa494e0: ldur            w1, [x0, #0x1b]
    // 0xa494e4: DecompressPointer r1
    //     0xa494e4: add             x1, x1, HEAP, lsl #32
    // 0xa494e8: cmp             w1, NULL
    // 0xa494ec: b.ne            #0xa494f8
    // 0xa494f0: r0 = Null
    //     0xa494f0: mov             x0, NULL
    // 0xa494f4: b               #0xa4953c
    // 0xa494f8: r0 = first()
    //     0xa494f8: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xa494fc: cmp             w0, NULL
    // 0xa49500: b.ne            #0xa4950c
    // 0xa49504: r0 = Null
    //     0xa49504: mov             x0, NULL
    // 0xa49508: b               #0xa4953c
    // 0xa4950c: LoadField: r1 = r0->field_13
    //     0xa4950c: ldur            w1, [x0, #0x13]
    // 0xa49510: DecompressPointer r1
    //     0xa49510: add             x1, x1, HEAP, lsl #32
    // 0xa49514: cmp             w1, NULL
    // 0xa49518: b.ne            #0xa49524
    // 0xa4951c: r0 = Null
    //     0xa4951c: mov             x0, NULL
    // 0xa49520: b               #0xa4953c
    // 0xa49524: LoadField: r0 = r1->field_7
    //     0xa49524: ldur            w0, [x1, #7]
    // 0xa49528: cbz             w0, #0xa49534
    // 0xa4952c: r1 = false
    //     0xa4952c: add             x1, NULL, #0x30  ; false
    // 0xa49530: b               #0xa49538
    // 0xa49534: r1 = true
    //     0xa49534: add             x1, NULL, #0x20  ; true
    // 0xa49538: mov             x0, x1
    // 0xa4953c: cmp             w0, NULL
    // 0xa49540: b.ne            #0xa49550
    // 0xa49544: ldur            x2, [fp, #-0x10]
    // 0xa49548: ldur            x0, [fp, #-0x28]
    // 0xa4954c: b               #0xa497e4
    // 0xa49550: tbnz            w0, #4, #0xa497dc
    // 0xa49554: ldur            x0, [fp, #-0x10]
    // 0xa49558: ldur            x1, [fp, #-0x28]
    // 0xa4955c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4955c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49560: ldr             x0, [x0, #0x1c80]
    //     0xa49564: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49568: cmp             w0, w16
    //     0xa4956c: b.ne            #0xa49578
    //     0xa49570: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa49574: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa49578: r1 = Null
    //     0xa49578: mov             x1, NULL
    // 0xa4957c: r2 = 40
    //     0xa4957c: movz            x2, #0x28
    // 0xa49580: r0 = AllocateArray()
    //     0xa49580: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa49584: mov             x2, x0
    // 0xa49588: stur            x2, [fp, #-0x18]
    // 0xa4958c: r16 = "couponCode"
    //     0xa4958c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa49590: ldr             x16, [x16, #0x310]
    // 0xa49594: StoreField: r2->field_f = r16
    //     0xa49594: stur            w16, [x2, #0xf]
    // 0xa49598: r16 = ""
    //     0xa49598: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4959c: StoreField: r2->field_13 = r16
    //     0xa4959c: stur            w16, [x2, #0x13]
    // 0xa495a0: r16 = "product_id"
    //     0xa495a0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa495a4: ldr             x16, [x16, #0x9b8]
    // 0xa495a8: ArrayStore: r2[0] = r16  ; List_4
    //     0xa495a8: stur            w16, [x2, #0x17]
    // 0xa495ac: ldur            x0, [fp, #-0x28]
    // 0xa495b0: LoadField: r1 = r0->field_f
    //     0xa495b0: ldur            w1, [x0, #0xf]
    // 0xa495b4: DecompressPointer r1
    //     0xa495b4: add             x1, x1, HEAP, lsl #32
    // 0xa495b8: LoadField: r3 = r1->field_b
    //     0xa495b8: ldur            w3, [x1, #0xb]
    // 0xa495bc: DecompressPointer r3
    //     0xa495bc: add             x3, x3, HEAP, lsl #32
    // 0xa495c0: cmp             w3, NULL
    // 0xa495c4: b.eq            #0xa49d18
    // 0xa495c8: LoadField: r0 = r3->field_f
    //     0xa495c8: ldur            w0, [x3, #0xf]
    // 0xa495cc: DecompressPointer r0
    //     0xa495cc: add             x0, x0, HEAP, lsl #32
    // 0xa495d0: StoreField: r2->field_1b = r0
    //     0xa495d0: stur            w0, [x2, #0x1b]
    // 0xa495d4: r16 = "sku_id"
    //     0xa495d4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa495d8: ldr             x16, [x16, #0x498]
    // 0xa495dc: StoreField: r2->field_1f = r16
    //     0xa495dc: stur            w16, [x2, #0x1f]
    // 0xa495e0: LoadField: r0 = r3->field_13
    //     0xa495e0: ldur            w0, [x3, #0x13]
    // 0xa495e4: DecompressPointer r0
    //     0xa495e4: add             x0, x0, HEAP, lsl #32
    // 0xa495e8: StoreField: r2->field_23 = r0
    //     0xa495e8: stur            w0, [x2, #0x23]
    // 0xa495ec: r16 = "quantity"
    //     0xa495ec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa495f0: ldr             x16, [x16, #0x428]
    // 0xa495f4: StoreField: r2->field_27 = r16
    //     0xa495f4: stur            w16, [x2, #0x27]
    // 0xa495f8: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa495f8: ldur            x4, [x3, #0x17]
    // 0xa495fc: r0 = BoxInt64Instr(r4)
    //     0xa495fc: sbfiz           x0, x4, #1, #0x1f
    //     0xa49600: cmp             x4, x0, asr #1
    //     0xa49604: b.eq            #0xa49610
    //     0xa49608: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4960c: stur            x4, [x0, #7]
    // 0xa49610: mov             x1, x2
    // 0xa49614: ArrayStore: r1[7] = r0  ; List_4
    //     0xa49614: add             x25, x1, #0x2b
    //     0xa49618: str             w0, [x25]
    //     0xa4961c: tbz             w0, #0, #0xa49638
    //     0xa49620: ldurb           w16, [x1, #-1]
    //     0xa49624: ldurb           w17, [x0, #-1]
    //     0xa49628: and             x16, x17, x16, lsr #2
    //     0xa4962c: tst             x16, HEAP, lsr #32
    //     0xa49630: b.eq            #0xa49638
    //     0xa49634: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49638: r16 = "checkout_event_data"
    //     0xa49638: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4963c: ldr             x16, [x16, #0xd50]
    // 0xa49640: StoreField: r2->field_2f = r16
    //     0xa49640: stur            w16, [x2, #0x2f]
    // 0xa49644: mov             x1, x2
    // 0xa49648: ldur            x0, [fp, #-0x38]
    // 0xa4964c: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4964c: add             x25, x1, #0x33
    //     0xa49650: str             w0, [x25]
    //     0xa49654: tbz             w0, #0, #0xa49670
    //     0xa49658: ldurb           w16, [x1, #-1]
    //     0xa4965c: ldurb           w17, [x0, #-1]
    //     0xa49660: and             x16, x17, x16, lsr #2
    //     0xa49664: tst             x16, HEAP, lsr #32
    //     0xa49668: b.eq            #0xa49670
    //     0xa4966c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49670: r16 = "previousScreenSource"
    //     0xa49670: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa49674: ldr             x16, [x16, #0x448]
    // 0xa49678: StoreField: r2->field_37 = r16
    //     0xa49678: stur            w16, [x2, #0x37]
    // 0xa4967c: r16 = "product_page"
    //     0xa4967c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa49680: ldr             x16, [x16, #0x480]
    // 0xa49684: StoreField: r2->field_3b = r16
    //     0xa49684: stur            w16, [x2, #0x3b]
    // 0xa49688: r16 = "is_skipped_address"
    //     0xa49688: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4968c: ldr             x16, [x16, #0xb80]
    // 0xa49690: StoreField: r2->field_3f = r16
    //     0xa49690: stur            w16, [x2, #0x3f]
    // 0xa49694: r16 = true
    //     0xa49694: add             x16, NULL, #0x20  ; true
    // 0xa49698: StoreField: r2->field_43 = r16
    //     0xa49698: stur            w16, [x2, #0x43]
    // 0xa4969c: r16 = "coming_from"
    //     0xa4969c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa496a0: ldr             x16, [x16, #0x328]
    // 0xa496a4: StoreField: r2->field_47 = r16
    //     0xa496a4: stur            w16, [x2, #0x47]
    // 0xa496a8: LoadField: r0 = r3->field_2f
    //     0xa496a8: ldur            w0, [x3, #0x2f]
    // 0xa496ac: DecompressPointer r0
    //     0xa496ac: add             x0, x0, HEAP, lsl #32
    // 0xa496b0: mov             x1, x2
    // 0xa496b4: ArrayStore: r1[15] = r0  ; List_4
    //     0xa496b4: add             x25, x1, #0x4b
    //     0xa496b8: str             w0, [x25]
    //     0xa496bc: tbz             w0, #0, #0xa496d8
    //     0xa496c0: ldurb           w16, [x1, #-1]
    //     0xa496c4: ldurb           w17, [x0, #-1]
    //     0xa496c8: and             x16, x17, x16, lsr #2
    //     0xa496cc: tst             x16, HEAP, lsr #32
    //     0xa496d0: b.eq            #0xa496d8
    //     0xa496d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa496d8: r16 = "checkout_id"
    //     0xa496d8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa496dc: ldr             x16, [x16, #0xb88]
    // 0xa496e0: StoreField: r2->field_4f = r16
    //     0xa496e0: stur            w16, [x2, #0x4f]
    // 0xa496e4: ldur            x0, [fp, #-0x10]
    // 0xa496e8: LoadField: r1 = r0->field_f
    //     0xa496e8: ldur            w1, [x0, #0xf]
    // 0xa496ec: DecompressPointer r1
    //     0xa496ec: add             x1, x1, HEAP, lsl #32
    // 0xa496f0: r16 = Sentinel
    //     0xa496f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa496f4: cmp             w1, w16
    // 0xa496f8: b.eq            #0xa49ccc
    // 0xa496fc: str             x1, [SP]
    // 0xa49700: r4 = 0
    //     0xa49700: movz            x4, #0
    // 0xa49704: ldr             x0, [SP]
    // 0xa49708: r16 = UnlinkedCall_0x613b5c
    //     0xa49708: add             x16, PP, #0x56, lsl #12  ; [pp+0x564c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4970c: add             x16, x16, #0x4c8
    // 0xa49710: ldp             x5, lr, [x16]
    // 0xa49714: blr             lr
    // 0xa49718: ldur            x1, [fp, #-0x18]
    // 0xa4971c: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4971c: add             x25, x1, #0x53
    //     0xa49720: str             w0, [x25]
    //     0xa49724: tbz             w0, #0, #0xa49740
    //     0xa49728: ldurb           w16, [x1, #-1]
    //     0xa4972c: ldurb           w17, [x0, #-1]
    //     0xa49730: and             x16, x17, x16, lsr #2
    //     0xa49734: tst             x16, HEAP, lsr #32
    //     0xa49738: b.eq            #0xa49740
    //     0xa4973c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49740: ldur            x1, [fp, #-0x18]
    // 0xa49744: r16 = "user_data"
    //     0xa49744: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa49748: ldr             x16, [x16, #0x58]
    // 0xa4974c: StoreField: r1->field_57 = r16
    //     0xa4974c: stur            w16, [x1, #0x57]
    // 0xa49750: ldur            x2, [fp, #-0x10]
    // 0xa49754: LoadField: r0 = r2->field_f
    //     0xa49754: ldur            w0, [x2, #0xf]
    // 0xa49758: DecompressPointer r0
    //     0xa49758: add             x0, x0, HEAP, lsl #32
    // 0xa4975c: r16 = Sentinel
    //     0xa4975c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa49760: cmp             w0, w16
    // 0xa49764: b.eq            #0xa49cdc
    // 0xa49768: str             x0, [SP]
    // 0xa4976c: r4 = 0
    //     0xa4976c: movz            x4, #0
    // 0xa49770: ldr             x0, [SP]
    // 0xa49774: r16 = UnlinkedCall_0x613b5c
    //     0xa49774: add             x16, PP, #0x56, lsl #12  ; [pp+0x564d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49778: add             x16, x16, #0x4d8
    // 0xa4977c: ldp             x5, lr, [x16]
    // 0xa49780: blr             lr
    // 0xa49784: ldur            x1, [fp, #-0x18]
    // 0xa49788: ArrayStore: r1[19] = r0  ; List_4
    //     0xa49788: add             x25, x1, #0x5b
    //     0xa4978c: str             w0, [x25]
    //     0xa49790: tbz             w0, #0, #0xa497ac
    //     0xa49794: ldurb           w16, [x1, #-1]
    //     0xa49798: ldurb           w17, [x0, #-1]
    //     0xa4979c: and             x16, x17, x16, lsr #2
    //     0xa497a0: tst             x16, HEAP, lsr #32
    //     0xa497a4: b.eq            #0xa497ac
    //     0xa497a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa497ac: r16 = <String, dynamic>
    //     0xa497ac: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa497b0: ldur            lr, [fp, #-0x18]
    // 0xa497b4: stp             lr, x16, [SP]
    // 0xa497b8: r0 = Map._fromLiteral()
    //     0xa497b8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa497bc: r16 = "/checkout_request_address_page"
    //     0xa497bc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xa497c0: ldr             x16, [x16, #0x9e8]
    // 0xa497c4: stp             x16, NULL, [SP, #8]
    // 0xa497c8: str             x0, [SP]
    // 0xa497cc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa497cc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa497d0: ldr             x4, [x4, #0x438]
    // 0xa497d4: r0 = GetNavigation.toNamed()
    //     0xa497d4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa497d8: b               #0xa49ca4
    // 0xa497dc: ldur            x2, [fp, #-0x10]
    // 0xa497e0: ldur            x0, [fp, #-0x28]
    // 0xa497e4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa497e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa497e8: ldr             x0, [x0, #0x1c80]
    //     0xa497ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa497f0: cmp             w0, w16
    //     0xa497f4: b.ne            #0xa49800
    //     0xa497f8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa497fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa49800: r1 = Null
    //     0xa49800: mov             x1, NULL
    // 0xa49804: r2 = 40
    //     0xa49804: movz            x2, #0x28
    // 0xa49808: r0 = AllocateArray()
    //     0xa49808: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4980c: mov             x2, x0
    // 0xa49810: stur            x2, [fp, #-0x18]
    // 0xa49814: r16 = "couponCode"
    //     0xa49814: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa49818: ldr             x16, [x16, #0x310]
    // 0xa4981c: StoreField: r2->field_f = r16
    //     0xa4981c: stur            w16, [x2, #0xf]
    // 0xa49820: r16 = ""
    //     0xa49820: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa49824: StoreField: r2->field_13 = r16
    //     0xa49824: stur            w16, [x2, #0x13]
    // 0xa49828: r16 = "product_id"
    //     0xa49828: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4982c: ldr             x16, [x16, #0x9b8]
    // 0xa49830: ArrayStore: r2[0] = r16  ; List_4
    //     0xa49830: stur            w16, [x2, #0x17]
    // 0xa49834: ldur            x0, [fp, #-0x28]
    // 0xa49838: LoadField: r1 = r0->field_f
    //     0xa49838: ldur            w1, [x0, #0xf]
    // 0xa4983c: DecompressPointer r1
    //     0xa4983c: add             x1, x1, HEAP, lsl #32
    // 0xa49840: LoadField: r3 = r1->field_b
    //     0xa49840: ldur            w3, [x1, #0xb]
    // 0xa49844: DecompressPointer r3
    //     0xa49844: add             x3, x3, HEAP, lsl #32
    // 0xa49848: cmp             w3, NULL
    // 0xa4984c: b.eq            #0xa49d1c
    // 0xa49850: LoadField: r0 = r3->field_f
    //     0xa49850: ldur            w0, [x3, #0xf]
    // 0xa49854: DecompressPointer r0
    //     0xa49854: add             x0, x0, HEAP, lsl #32
    // 0xa49858: StoreField: r2->field_1b = r0
    //     0xa49858: stur            w0, [x2, #0x1b]
    // 0xa4985c: r16 = "sku_id"
    //     0xa4985c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa49860: ldr             x16, [x16, #0x498]
    // 0xa49864: StoreField: r2->field_1f = r16
    //     0xa49864: stur            w16, [x2, #0x1f]
    // 0xa49868: LoadField: r0 = r3->field_13
    //     0xa49868: ldur            w0, [x3, #0x13]
    // 0xa4986c: DecompressPointer r0
    //     0xa4986c: add             x0, x0, HEAP, lsl #32
    // 0xa49870: StoreField: r2->field_23 = r0
    //     0xa49870: stur            w0, [x2, #0x23]
    // 0xa49874: r16 = "quantity"
    //     0xa49874: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa49878: ldr             x16, [x16, #0x428]
    // 0xa4987c: StoreField: r2->field_27 = r16
    //     0xa4987c: stur            w16, [x2, #0x27]
    // 0xa49880: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa49880: ldur            x4, [x3, #0x17]
    // 0xa49884: r0 = BoxInt64Instr(r4)
    //     0xa49884: sbfiz           x0, x4, #1, #0x1f
    //     0xa49888: cmp             x4, x0, asr #1
    //     0xa4988c: b.eq            #0xa49898
    //     0xa49890: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49894: stur            x4, [x0, #7]
    // 0xa49898: mov             x1, x2
    // 0xa4989c: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4989c: add             x25, x1, #0x2b
    //     0xa498a0: str             w0, [x25]
    //     0xa498a4: tbz             w0, #0, #0xa498c0
    //     0xa498a8: ldurb           w16, [x1, #-1]
    //     0xa498ac: ldurb           w17, [x0, #-1]
    //     0xa498b0: and             x16, x17, x16, lsr #2
    //     0xa498b4: tst             x16, HEAP, lsr #32
    //     0xa498b8: b.eq            #0xa498c0
    //     0xa498bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa498c0: r16 = "checkout_event_data"
    //     0xa498c0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa498c4: ldr             x16, [x16, #0xd50]
    // 0xa498c8: StoreField: r2->field_2f = r16
    //     0xa498c8: stur            w16, [x2, #0x2f]
    // 0xa498cc: mov             x1, x2
    // 0xa498d0: ldur            x0, [fp, #-0x38]
    // 0xa498d4: ArrayStore: r1[9] = r0  ; List_4
    //     0xa498d4: add             x25, x1, #0x33
    //     0xa498d8: str             w0, [x25]
    //     0xa498dc: tbz             w0, #0, #0xa498f8
    //     0xa498e0: ldurb           w16, [x1, #-1]
    //     0xa498e4: ldurb           w17, [x0, #-1]
    //     0xa498e8: and             x16, x17, x16, lsr #2
    //     0xa498ec: tst             x16, HEAP, lsr #32
    //     0xa498f0: b.eq            #0xa498f8
    //     0xa498f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa498f8: r16 = "previousScreenSource"
    //     0xa498f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa498fc: ldr             x16, [x16, #0x448]
    // 0xa49900: StoreField: r2->field_37 = r16
    //     0xa49900: stur            w16, [x2, #0x37]
    // 0xa49904: r16 = "product_page"
    //     0xa49904: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa49908: ldr             x16, [x16, #0x480]
    // 0xa4990c: StoreField: r2->field_3b = r16
    //     0xa4990c: stur            w16, [x2, #0x3b]
    // 0xa49910: r16 = "is_skipped_address"
    //     0xa49910: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa49914: ldr             x16, [x16, #0xb80]
    // 0xa49918: StoreField: r2->field_3f = r16
    //     0xa49918: stur            w16, [x2, #0x3f]
    // 0xa4991c: r16 = true
    //     0xa4991c: add             x16, NULL, #0x20  ; true
    // 0xa49920: StoreField: r2->field_43 = r16
    //     0xa49920: stur            w16, [x2, #0x43]
    // 0xa49924: r16 = "coming_from"
    //     0xa49924: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa49928: ldr             x16, [x16, #0x328]
    // 0xa4992c: StoreField: r2->field_47 = r16
    //     0xa4992c: stur            w16, [x2, #0x47]
    // 0xa49930: LoadField: r0 = r3->field_2f
    //     0xa49930: ldur            w0, [x3, #0x2f]
    // 0xa49934: DecompressPointer r0
    //     0xa49934: add             x0, x0, HEAP, lsl #32
    // 0xa49938: mov             x1, x2
    // 0xa4993c: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4993c: add             x25, x1, #0x4b
    //     0xa49940: str             w0, [x25]
    //     0xa49944: tbz             w0, #0, #0xa49960
    //     0xa49948: ldurb           w16, [x1, #-1]
    //     0xa4994c: ldurb           w17, [x0, #-1]
    //     0xa49950: and             x16, x17, x16, lsr #2
    //     0xa49954: tst             x16, HEAP, lsr #32
    //     0xa49958: b.eq            #0xa49960
    //     0xa4995c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49960: r16 = "checkout_id"
    //     0xa49960: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa49964: ldr             x16, [x16, #0xb88]
    // 0xa49968: StoreField: r2->field_4f = r16
    //     0xa49968: stur            w16, [x2, #0x4f]
    // 0xa4996c: ldur            x0, [fp, #-0x10]
    // 0xa49970: LoadField: r1 = r0->field_f
    //     0xa49970: ldur            w1, [x0, #0xf]
    // 0xa49974: DecompressPointer r1
    //     0xa49974: add             x1, x1, HEAP, lsl #32
    // 0xa49978: r16 = Sentinel
    //     0xa49978: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4997c: cmp             w1, w16
    // 0xa49980: b.eq            #0xa49cec
    // 0xa49984: str             x1, [SP]
    // 0xa49988: r4 = 0
    //     0xa49988: movz            x4, #0
    // 0xa4998c: ldr             x0, [SP]
    // 0xa49990: r16 = UnlinkedCall_0x613b5c
    //     0xa49990: add             x16, PP, #0x56, lsl #12  ; [pp+0x564e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49994: add             x16, x16, #0x4e8
    // 0xa49998: ldp             x5, lr, [x16]
    // 0xa4999c: blr             lr
    // 0xa499a0: ldur            x1, [fp, #-0x18]
    // 0xa499a4: ArrayStore: r1[17] = r0  ; List_4
    //     0xa499a4: add             x25, x1, #0x53
    //     0xa499a8: str             w0, [x25]
    //     0xa499ac: tbz             w0, #0, #0xa499c8
    //     0xa499b0: ldurb           w16, [x1, #-1]
    //     0xa499b4: ldurb           w17, [x0, #-1]
    //     0xa499b8: and             x16, x17, x16, lsr #2
    //     0xa499bc: tst             x16, HEAP, lsr #32
    //     0xa499c0: b.eq            #0xa499c8
    //     0xa499c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa499c8: ldur            x1, [fp, #-0x18]
    // 0xa499cc: r16 = "user_data"
    //     0xa499cc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa499d0: ldr             x16, [x16, #0x58]
    // 0xa499d4: StoreField: r1->field_57 = r16
    //     0xa499d4: stur            w16, [x1, #0x57]
    // 0xa499d8: ldur            x0, [fp, #-0x10]
    // 0xa499dc: LoadField: r2 = r0->field_f
    //     0xa499dc: ldur            w2, [x0, #0xf]
    // 0xa499e0: DecompressPointer r2
    //     0xa499e0: add             x2, x2, HEAP, lsl #32
    // 0xa499e4: r16 = Sentinel
    //     0xa499e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa499e8: cmp             w2, w16
    // 0xa499ec: b.eq            #0xa49cfc
    // 0xa499f0: str             x2, [SP]
    // 0xa499f4: r4 = 0
    //     0xa499f4: movz            x4, #0
    // 0xa499f8: ldr             x0, [SP]
    // 0xa499fc: r16 = UnlinkedCall_0x613b5c
    //     0xa499fc: add             x16, PP, #0x56, lsl #12  ; [pp+0x564f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49a00: add             x16, x16, #0x4f8
    // 0xa49a04: ldp             x5, lr, [x16]
    // 0xa49a08: blr             lr
    // 0xa49a0c: ldur            x1, [fp, #-0x18]
    // 0xa49a10: ArrayStore: r1[19] = r0  ; List_4
    //     0xa49a10: add             x25, x1, #0x5b
    //     0xa49a14: str             w0, [x25]
    //     0xa49a18: tbz             w0, #0, #0xa49a34
    //     0xa49a1c: ldurb           w16, [x1, #-1]
    //     0xa49a20: ldurb           w17, [x0, #-1]
    //     0xa49a24: and             x16, x17, x16, lsr #2
    //     0xa49a28: tst             x16, HEAP, lsr #32
    //     0xa49a2c: b.eq            #0xa49a34
    //     0xa49a30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49a34: r16 = <String, dynamic>
    //     0xa49a34: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa49a38: ldur            lr, [fp, #-0x18]
    // 0xa49a3c: stp             lr, x16, [SP]
    // 0xa49a40: r0 = Map._fromLiteral()
    //     0xa49a40: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa49a44: r16 = "/checkout_order_summary_page"
    //     0xa49a44: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xa49a48: ldr             x16, [x16, #0x9d8]
    // 0xa49a4c: stp             x16, NULL, [SP, #8]
    // 0xa49a50: str             x0, [SP]
    // 0xa49a54: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa49a54: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa49a58: ldr             x4, [x4, #0x438]
    // 0xa49a5c: r0 = GetNavigation.toNamed()
    //     0xa49a5c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa49a60: b               #0xa49ca4
    // 0xa49a64: ldur            x0, [fp, #-0x10]
    // 0xa49a68: LoadField: r2 = r0->field_b
    //     0xa49a68: ldur            w2, [x0, #0xb]
    // 0xa49a6c: DecompressPointer r2
    //     0xa49a6c: add             x2, x2, HEAP, lsl #32
    // 0xa49a70: stur            x2, [fp, #-0x18]
    // 0xa49a74: LoadField: r0 = r2->field_f
    //     0xa49a74: ldur            w0, [x2, #0xf]
    // 0xa49a78: DecompressPointer r0
    //     0xa49a78: add             x0, x0, HEAP, lsl #32
    // 0xa49a7c: LoadField: r1 = r0->field_b
    //     0xa49a7c: ldur            w1, [x0, #0xb]
    // 0xa49a80: DecompressPointer r1
    //     0xa49a80: add             x1, x1, HEAP, lsl #32
    // 0xa49a84: cmp             w1, NULL
    // 0xa49a88: b.eq            #0xa49d20
    // 0xa49a8c: LoadField: r3 = r1->field_f
    //     0xa49a8c: ldur            w3, [x1, #0xf]
    // 0xa49a90: DecompressPointer r3
    //     0xa49a90: add             x3, x3, HEAP, lsl #32
    // 0xa49a94: stur            x3, [fp, #-0x10]
    // 0xa49a98: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xa49a98: ldur            x4, [x1, #0x17]
    // 0xa49a9c: stur            x4, [fp, #-0x20]
    // 0xa49aa0: LoadField: r0 = r1->field_27
    //     0xa49aa0: ldur            x0, [x1, #0x27]
    // 0xa49aa4: mul             x5, x0, x4
    // 0xa49aa8: r0 = BoxInt64Instr(r5)
    //     0xa49aa8: sbfiz           x0, x5, #1, #0x1f
    //     0xa49aac: cmp             x5, x0, asr #1
    //     0xa49ab0: b.eq            #0xa49abc
    //     0xa49ab4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49ab8: stur            x5, [x0, #7]
    // 0xa49abc: stp             x0, NULL, [SP]
    // 0xa49ac0: r0 = _Double.fromInteger()
    //     0xa49ac0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa49ac4: stur            x0, [fp, #-0x28]
    // 0xa49ac8: r0 = CheckoutEventData()
    //     0xa49ac8: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa49acc: mov             x2, x0
    // 0xa49ad0: ldur            x0, [fp, #-0x28]
    // 0xa49ad4: stur            x2, [fp, #-0x30]
    // 0xa49ad8: StoreField: r2->field_7 = r0
    //     0xa49ad8: stur            w0, [x2, #7]
    // 0xa49adc: ldur            x3, [fp, #-0x20]
    // 0xa49ae0: r0 = BoxInt64Instr(r3)
    //     0xa49ae0: sbfiz           x0, x3, #1, #0x1f
    //     0xa49ae4: cmp             x3, x0, asr #1
    //     0xa49ae8: b.eq            #0xa49af4
    //     0xa49aec: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49af0: stur            x3, [x0, #7]
    // 0xa49af4: StoreField: r2->field_b = r0
    //     0xa49af4: stur            w0, [x2, #0xb]
    // 0xa49af8: ldur            x0, [fp, #-0x10]
    // 0xa49afc: StoreField: r2->field_f = r0
    //     0xa49afc: stur            w0, [x2, #0xf]
    // 0xa49b00: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa49b00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49b04: ldr             x0, [x0, #0x1c80]
    //     0xa49b08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49b0c: cmp             w0, w16
    //     0xa49b10: b.ne            #0xa49b1c
    //     0xa49b14: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa49b18: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa49b1c: r1 = Null
    //     0xa49b1c: mov             x1, NULL
    // 0xa49b20: r2 = 32
    //     0xa49b20: movz            x2, #0x20
    // 0xa49b24: r0 = AllocateArray()
    //     0xa49b24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa49b28: mov             x2, x0
    // 0xa49b2c: r16 = "couponCode"
    //     0xa49b2c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa49b30: ldr             x16, [x16, #0x310]
    // 0xa49b34: StoreField: r2->field_f = r16
    //     0xa49b34: stur            w16, [x2, #0xf]
    // 0xa49b38: r16 = ""
    //     0xa49b38: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa49b3c: StoreField: r2->field_13 = r16
    //     0xa49b3c: stur            w16, [x2, #0x13]
    // 0xa49b40: r16 = "product_id"
    //     0xa49b40: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa49b44: ldr             x16, [x16, #0x9b8]
    // 0xa49b48: ArrayStore: r2[0] = r16  ; List_4
    //     0xa49b48: stur            w16, [x2, #0x17]
    // 0xa49b4c: ldur            x0, [fp, #-0x18]
    // 0xa49b50: LoadField: r1 = r0->field_f
    //     0xa49b50: ldur            w1, [x0, #0xf]
    // 0xa49b54: DecompressPointer r1
    //     0xa49b54: add             x1, x1, HEAP, lsl #32
    // 0xa49b58: LoadField: r3 = r1->field_b
    //     0xa49b58: ldur            w3, [x1, #0xb]
    // 0xa49b5c: DecompressPointer r3
    //     0xa49b5c: add             x3, x3, HEAP, lsl #32
    // 0xa49b60: cmp             w3, NULL
    // 0xa49b64: b.eq            #0xa49d24
    // 0xa49b68: LoadField: r0 = r3->field_f
    //     0xa49b68: ldur            w0, [x3, #0xf]
    // 0xa49b6c: DecompressPointer r0
    //     0xa49b6c: add             x0, x0, HEAP, lsl #32
    // 0xa49b70: StoreField: r2->field_1b = r0
    //     0xa49b70: stur            w0, [x2, #0x1b]
    // 0xa49b74: r16 = "sku_id"
    //     0xa49b74: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa49b78: ldr             x16, [x16, #0x498]
    // 0xa49b7c: StoreField: r2->field_1f = r16
    //     0xa49b7c: stur            w16, [x2, #0x1f]
    // 0xa49b80: LoadField: r0 = r3->field_13
    //     0xa49b80: ldur            w0, [x3, #0x13]
    // 0xa49b84: DecompressPointer r0
    //     0xa49b84: add             x0, x0, HEAP, lsl #32
    // 0xa49b88: StoreField: r2->field_23 = r0
    //     0xa49b88: stur            w0, [x2, #0x23]
    // 0xa49b8c: r16 = "quantity"
    //     0xa49b8c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa49b90: ldr             x16, [x16, #0x428]
    // 0xa49b94: StoreField: r2->field_27 = r16
    //     0xa49b94: stur            w16, [x2, #0x27]
    // 0xa49b98: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa49b98: ldur            x4, [x3, #0x17]
    // 0xa49b9c: r0 = BoxInt64Instr(r4)
    //     0xa49b9c: sbfiz           x0, x4, #1, #0x1f
    //     0xa49ba0: cmp             x4, x0, asr #1
    //     0xa49ba4: b.eq            #0xa49bb0
    //     0xa49ba8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49bac: stur            x4, [x0, #7]
    // 0xa49bb0: mov             x1, x2
    // 0xa49bb4: ArrayStore: r1[7] = r0  ; List_4
    //     0xa49bb4: add             x25, x1, #0x2b
    //     0xa49bb8: str             w0, [x25]
    //     0xa49bbc: tbz             w0, #0, #0xa49bd8
    //     0xa49bc0: ldurb           w16, [x1, #-1]
    //     0xa49bc4: ldurb           w17, [x0, #-1]
    //     0xa49bc8: and             x16, x17, x16, lsr #2
    //     0xa49bcc: tst             x16, HEAP, lsr #32
    //     0xa49bd0: b.eq            #0xa49bd8
    //     0xa49bd4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49bd8: r16 = "checkout_event_data"
    //     0xa49bd8: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa49bdc: ldr             x16, [x16, #0xd50]
    // 0xa49be0: StoreField: r2->field_2f = r16
    //     0xa49be0: stur            w16, [x2, #0x2f]
    // 0xa49be4: mov             x1, x2
    // 0xa49be8: ldur            x0, [fp, #-0x30]
    // 0xa49bec: ArrayStore: r1[9] = r0  ; List_4
    //     0xa49bec: add             x25, x1, #0x33
    //     0xa49bf0: str             w0, [x25]
    //     0xa49bf4: tbz             w0, #0, #0xa49c10
    //     0xa49bf8: ldurb           w16, [x1, #-1]
    //     0xa49bfc: ldurb           w17, [x0, #-1]
    //     0xa49c00: and             x16, x17, x16, lsr #2
    //     0xa49c04: tst             x16, HEAP, lsr #32
    //     0xa49c08: b.eq            #0xa49c10
    //     0xa49c0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49c10: r16 = "previousScreenSource"
    //     0xa49c10: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa49c14: ldr             x16, [x16, #0x448]
    // 0xa49c18: StoreField: r2->field_37 = r16
    //     0xa49c18: stur            w16, [x2, #0x37]
    // 0xa49c1c: r16 = "product_page"
    //     0xa49c1c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa49c20: ldr             x16, [x16, #0x480]
    // 0xa49c24: StoreField: r2->field_3b = r16
    //     0xa49c24: stur            w16, [x2, #0x3b]
    // 0xa49c28: r16 = "coming_from"
    //     0xa49c28: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa49c2c: ldr             x16, [x16, #0x328]
    // 0xa49c30: StoreField: r2->field_3f = r16
    //     0xa49c30: stur            w16, [x2, #0x3f]
    // 0xa49c34: LoadField: r0 = r3->field_2f
    //     0xa49c34: ldur            w0, [x3, #0x2f]
    // 0xa49c38: DecompressPointer r0
    //     0xa49c38: add             x0, x0, HEAP, lsl #32
    // 0xa49c3c: mov             x1, x2
    // 0xa49c40: ArrayStore: r1[13] = r0  ; List_4
    //     0xa49c40: add             x25, x1, #0x43
    //     0xa49c44: str             w0, [x25]
    //     0xa49c48: tbz             w0, #0, #0xa49c64
    //     0xa49c4c: ldurb           w16, [x1, #-1]
    //     0xa49c50: ldurb           w17, [x0, #-1]
    //     0xa49c54: and             x16, x17, x16, lsr #2
    //     0xa49c58: tst             x16, HEAP, lsr #32
    //     0xa49c5c: b.eq            #0xa49c64
    //     0xa49c60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa49c64: r16 = "is_skipped_address"
    //     0xa49c64: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa49c68: ldr             x16, [x16, #0xb80]
    // 0xa49c6c: StoreField: r2->field_47 = r16
    //     0xa49c6c: stur            w16, [x2, #0x47]
    // 0xa49c70: r16 = true
    //     0xa49c70: add             x16, NULL, #0x20  ; true
    // 0xa49c74: StoreField: r2->field_4b = r16
    //     0xa49c74: stur            w16, [x2, #0x4b]
    // 0xa49c78: r16 = <String, Object?>
    //     0xa49c78: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa49c7c: ldr             x16, [x16, #0xc28]
    // 0xa49c80: stp             x2, x16, [SP]
    // 0xa49c84: r0 = Map._fromLiteral()
    //     0xa49c84: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa49c88: r16 = "/checkout_request_number_page"
    //     0xa49c88: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xa49c8c: ldr             x16, [x16, #0x9f8]
    // 0xa49c90: stp             x16, NULL, [SP, #8]
    // 0xa49c94: str             x0, [SP]
    // 0xa49c98: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa49c98: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa49c9c: ldr             x4, [x4, #0x438]
    // 0xa49ca0: r0 = GetNavigation.toNamed()
    //     0xa49ca0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa49ca4: r0 = Null
    //     0xa49ca4: mov             x0, NULL
    // 0xa49ca8: r0 = ReturnAsyncNotFuture()
    //     0xa49ca8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xa49cac: r16 = "controller"
    //     0xa49cac: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49cb0: str             x16, [SP]
    // 0xa49cb4: r0 = _throwLocalNotInitialized()
    //     0xa49cb4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49cb8: brk             #0
    // 0xa49cbc: r16 = "controller"
    //     0xa49cbc: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49cc0: str             x16, [SP]
    // 0xa49cc4: r0 = _throwLocalNotInitialized()
    //     0xa49cc4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49cc8: brk             #0
    // 0xa49ccc: r16 = "controller"
    //     0xa49ccc: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49cd0: str             x16, [SP]
    // 0xa49cd4: r0 = _throwLocalNotInitialized()
    //     0xa49cd4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49cd8: brk             #0
    // 0xa49cdc: r16 = "controller"
    //     0xa49cdc: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49ce0: str             x16, [SP]
    // 0xa49ce4: r0 = _throwLocalNotInitialized()
    //     0xa49ce4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49ce8: brk             #0
    // 0xa49cec: r16 = "controller"
    //     0xa49cec: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49cf0: str             x16, [SP]
    // 0xa49cf4: r0 = _throwLocalNotInitialized()
    //     0xa49cf4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49cf8: brk             #0
    // 0xa49cfc: r16 = "controller"
    //     0xa49cfc: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa49d00: str             x16, [SP]
    // 0xa49d04: r0 = _throwLocalNotInitialized()
    //     0xa49d04: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa49d08: brk             #0
    // 0xa49d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49d0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49d10: b               #0xa49370
    // 0xa49d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49d14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa49d18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49d18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa49d1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49d1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa49d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49d20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa49d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa49d24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa49d28, size: 0x758
    // 0xa49d28: EnterFrame
    //     0xa49d28: stp             fp, lr, [SP, #-0x10]!
    //     0xa49d2c: mov             fp, SP
    // 0xa49d30: AllocStack(0x78)
    //     0xa49d30: sub             SP, SP, #0x78
    // 0xa49d34: SetupParameters()
    //     0xa49d34: ldr             x0, [fp, #0x10]
    //     0xa49d38: ldur            w1, [x0, #0x17]
    //     0xa49d3c: add             x1, x1, HEAP, lsl #32
    //     0xa49d40: stur            x1, [fp, #-8]
    // 0xa49d44: CheckStackOverflow
    //     0xa49d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa49d48: cmp             SP, x16
    //     0xa49d4c: b.ls            #0xa4a454
    // 0xa49d50: r1 = 1
    //     0xa49d50: movz            x1, #0x1
    // 0xa49d54: r0 = AllocateContext()
    //     0xa49d54: bl              #0x16f6108  ; AllocateContextStub
    // 0xa49d58: mov             x1, x0
    // 0xa49d5c: ldur            x0, [fp, #-8]
    // 0xa49d60: stur            x1, [fp, #-0x10]
    // 0xa49d64: StoreField: r1->field_b = r0
    //     0xa49d64: stur            w0, [x1, #0xb]
    // 0xa49d68: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa49d68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa49d6c: ldr             x0, [x0, #0x1c80]
    //     0xa49d70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa49d74: cmp             w0, w16
    //     0xa49d78: b.ne            #0xa49d84
    //     0xa49d7c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa49d80: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa49d84: str             NULL, [SP]
    // 0xa49d88: r4 = const [0x1, 0, 0, 0, null]
    //     0xa49d88: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49d8c: r0 = GetNavigation.back()
    //     0xa49d8c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xa49d90: ldur            x2, [fp, #-0x10]
    // 0xa49d94: r0 = Sentinel
    //     0xa49d94: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa49d98: StoreField: r2->field_f = r0
    //     0xa49d98: stur            w0, [x2, #0xf]
    // 0xa49d9c: ldur            x0, [fp, #-8]
    // 0xa49da0: LoadField: r1 = r0->field_f
    //     0xa49da0: ldur            w1, [x0, #0xf]
    // 0xa49da4: DecompressPointer r1
    //     0xa49da4: add             x1, x1, HEAP, lsl #32
    // 0xa49da8: LoadField: r3 = r1->field_b
    //     0xa49da8: ldur            w3, [x1, #0xb]
    // 0xa49dac: DecompressPointer r3
    //     0xa49dac: add             x3, x3, HEAP, lsl #32
    // 0xa49db0: cmp             w3, NULL
    // 0xa49db4: b.eq            #0xa4a45c
    // 0xa49db8: LoadField: r1 = r3->field_23
    //     0xa49db8: ldur            w1, [x3, #0x23]
    // 0xa49dbc: DecompressPointer r1
    //     0xa49dbc: add             x1, x1, HEAP, lsl #32
    // 0xa49dc0: r16 = "home_page"
    //     0xa49dc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xa49dc4: ldr             x16, [x16, #0xe60]
    // 0xa49dc8: stp             x16, x1, [SP]
    // 0xa49dcc: r0 = ==()
    //     0xa49dcc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa49dd0: tbnz            w0, #4, #0xa49e1c
    // 0xa49dd4: ldur            x2, [fp, #-0x10]
    // 0xa49dd8: r16 = <HomeController>
    //     0xa49dd8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xa49ddc: ldr             x16, [x16, #0xf98]
    // 0xa49de0: str             x16, [SP]
    // 0xa49de4: r4 = const [0x1, 0, 0, 0, null]
    //     0xa49de4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49de8: r0 = Inst.find()
    //     0xa49de8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa49dec: mov             x1, x0
    // 0xa49df0: ldur            x2, [fp, #-0x10]
    // 0xa49df4: StoreField: r2->field_f = r0
    //     0xa49df4: stur            w0, [x2, #0xf]
    //     0xa49df8: ldurb           w16, [x2, #-1]
    //     0xa49dfc: ldurb           w17, [x0, #-1]
    //     0xa49e00: and             x16, x17, x16, lsr #2
    //     0xa49e04: tst             x16, HEAP, lsr #32
    //     0xa49e08: b.eq            #0xa49e10
    //     0xa49e0c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa49e10: r3 = "product_card"
    //     0xa49e10: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa49e14: ldr             x3, [x3, #0xc28]
    // 0xa49e18: b               #0xa49f7c
    // 0xa49e1c: ldur            x0, [fp, #-8]
    // 0xa49e20: ldur            x2, [fp, #-0x10]
    // 0xa49e24: LoadField: r1 = r0->field_f
    //     0xa49e24: ldur            w1, [x0, #0xf]
    // 0xa49e28: DecompressPointer r1
    //     0xa49e28: add             x1, x1, HEAP, lsl #32
    // 0xa49e2c: LoadField: r3 = r1->field_b
    //     0xa49e2c: ldur            w3, [x1, #0xb]
    // 0xa49e30: DecompressPointer r3
    //     0xa49e30: add             x3, x3, HEAP, lsl #32
    // 0xa49e34: cmp             w3, NULL
    // 0xa49e38: b.eq            #0xa4a460
    // 0xa49e3c: LoadField: r1 = r3->field_23
    //     0xa49e3c: ldur            w1, [x3, #0x23]
    // 0xa49e40: DecompressPointer r1
    //     0xa49e40: add             x1, x1, HEAP, lsl #32
    // 0xa49e44: r16 = "collection_page"
    //     0xa49e44: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xa49e48: ldr             x16, [x16, #0x118]
    // 0xa49e4c: stp             x16, x1, [SP]
    // 0xa49e50: r0 = ==()
    //     0xa49e50: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa49e54: tbnz            w0, #4, #0xa49ea4
    // 0xa49e58: ldur            x2, [fp, #-0x10]
    // 0xa49e5c: r16 = <CollectionsController>
    //     0xa49e5c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xa49e60: ldr             x16, [x16, #0xb00]
    // 0xa49e64: str             x16, [SP]
    // 0xa49e68: r4 = const [0x1, 0, 0, 0, null]
    //     0xa49e68: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49e6c: r0 = Inst.find()
    //     0xa49e6c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa49e70: mov             x1, x0
    // 0xa49e74: ldur            x2, [fp, #-0x10]
    // 0xa49e78: StoreField: r2->field_f = r0
    //     0xa49e78: stur            w0, [x2, #0xf]
    //     0xa49e7c: ldurb           w16, [x2, #-1]
    //     0xa49e80: ldurb           w17, [x0, #-1]
    //     0xa49e84: and             x16, x17, x16, lsr #2
    //     0xa49e88: tst             x16, HEAP, lsr #32
    //     0xa49e8c: b.eq            #0xa49e94
    //     0xa49e90: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa49e94: mov             x0, x1
    // 0xa49e98: r1 = "product_card"
    //     0xa49e98: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa49e9c: ldr             x1, [x1, #0xc28]
    // 0xa49ea0: b               #0xa49f74
    // 0xa49ea4: ldur            x0, [fp, #-8]
    // 0xa49ea8: ldur            x2, [fp, #-0x10]
    // 0xa49eac: LoadField: r1 = r0->field_f
    //     0xa49eac: ldur            w1, [x0, #0xf]
    // 0xa49eb0: DecompressPointer r1
    //     0xa49eb0: add             x1, x1, HEAP, lsl #32
    // 0xa49eb4: LoadField: r3 = r1->field_b
    //     0xa49eb4: ldur            w3, [x1, #0xb]
    // 0xa49eb8: DecompressPointer r3
    //     0xa49eb8: add             x3, x3, HEAP, lsl #32
    // 0xa49ebc: cmp             w3, NULL
    // 0xa49ec0: b.eq            #0xa4a464
    // 0xa49ec4: LoadField: r1 = r3->field_23
    //     0xa49ec4: ldur            w1, [x3, #0x23]
    // 0xa49ec8: DecompressPointer r1
    //     0xa49ec8: add             x1, x1, HEAP, lsl #32
    // 0xa49ecc: r16 = "product_page"
    //     0xa49ecc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa49ed0: ldr             x16, [x16, #0x480]
    // 0xa49ed4: stp             x16, x1, [SP]
    // 0xa49ed8: r0 = ==()
    //     0xa49ed8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xa49edc: tbnz            w0, #4, #0xa49f2c
    // 0xa49ee0: ldur            x2, [fp, #-0x10]
    // 0xa49ee4: r16 = <ProductDetailController>
    //     0xa49ee4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xa49ee8: ldr             x16, [x16, #0xde0]
    // 0xa49eec: str             x16, [SP]
    // 0xa49ef0: r4 = const [0x1, 0, 0, 0, null]
    //     0xa49ef0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49ef4: r0 = Inst.find()
    //     0xa49ef4: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa49ef8: mov             x1, x0
    // 0xa49efc: ldur            x2, [fp, #-0x10]
    // 0xa49f00: StoreField: r2->field_f = r0
    //     0xa49f00: stur            w0, [x2, #0xf]
    //     0xa49f04: ldurb           w16, [x2, #-1]
    //     0xa49f08: ldurb           w17, [x0, #-1]
    //     0xa49f0c: and             x16, x17, x16, lsr #2
    //     0xa49f10: tst             x16, HEAP, lsr #32
    //     0xa49f14: b.eq            #0xa49f1c
    //     0xa49f18: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa49f1c: mov             x0, x1
    // 0xa49f20: r1 = "product_page"
    //     0xa49f20: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa49f24: ldr             x1, [x1, #0x480]
    // 0xa49f28: b               #0xa49f74
    // 0xa49f2c: ldur            x2, [fp, #-0x10]
    // 0xa49f30: r16 = <HomeController>
    //     0xa49f30: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xa49f34: ldr             x16, [x16, #0xf98]
    // 0xa49f38: str             x16, [SP]
    // 0xa49f3c: r4 = const [0x1, 0, 0, 0, null]
    //     0xa49f3c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xa49f40: r0 = Inst.find()
    //     0xa49f40: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa49f44: mov             x1, x0
    // 0xa49f48: ldur            x2, [fp, #-0x10]
    // 0xa49f4c: StoreField: r2->field_f = r0
    //     0xa49f4c: stur            w0, [x2, #0xf]
    //     0xa49f50: ldurb           w16, [x2, #-1]
    //     0xa49f54: ldurb           w17, [x0, #-1]
    //     0xa49f58: and             x16, x17, x16, lsr #2
    //     0xa49f5c: tst             x16, HEAP, lsr #32
    //     0xa49f60: b.eq            #0xa49f68
    //     0xa49f64: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa49f68: mov             x0, x1
    // 0xa49f6c: r1 = "product_card"
    //     0xa49f6c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xa49f70: ldr             x1, [x1, #0xc28]
    // 0xa49f74: mov             x3, x1
    // 0xa49f78: mov             x1, x0
    // 0xa49f7c: ldur            x0, [fp, #-8]
    // 0xa49f80: stur            x3, [fp, #-0x18]
    // 0xa49f84: str             x1, [SP]
    // 0xa49f88: r4 = 0
    //     0xa49f88: movz            x4, #0
    // 0xa49f8c: ldr             x0, [SP]
    // 0xa49f90: r16 = UnlinkedCall_0x613b5c
    //     0xa49f90: add             x16, PP, #0x56, lsl #12  ; [pp+0x563c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa49f94: add             x16, x16, #0x3c0
    // 0xa49f98: ldp             x5, lr, [x16]
    // 0xa49f9c: blr             lr
    // 0xa49fa0: mov             x3, x0
    // 0xa49fa4: ldur            x2, [fp, #-8]
    // 0xa49fa8: stur            x3, [fp, #-0x28]
    // 0xa49fac: LoadField: r0 = r2->field_f
    //     0xa49fac: ldur            w0, [x2, #0xf]
    // 0xa49fb0: DecompressPointer r0
    //     0xa49fb0: add             x0, x0, HEAP, lsl #32
    // 0xa49fb4: LoadField: r1 = r0->field_b
    //     0xa49fb4: ldur            w1, [x0, #0xb]
    // 0xa49fb8: DecompressPointer r1
    //     0xa49fb8: add             x1, x1, HEAP, lsl #32
    // 0xa49fbc: cmp             w1, NULL
    // 0xa49fc0: b.eq            #0xa4a468
    // 0xa49fc4: LoadField: r4 = r1->field_f
    //     0xa49fc4: ldur            w4, [x1, #0xf]
    // 0xa49fc8: DecompressPointer r4
    //     0xa49fc8: add             x4, x4, HEAP, lsl #32
    // 0xa49fcc: stur            x4, [fp, #-0x20]
    // 0xa49fd0: LoadField: r5 = r1->field_27
    //     0xa49fd0: ldur            x5, [x1, #0x27]
    // 0xa49fd4: r0 = BoxInt64Instr(r5)
    //     0xa49fd4: sbfiz           x0, x5, #1, #0x1f
    //     0xa49fd8: cmp             x5, x0, asr #1
    //     0xa49fdc: b.eq            #0xa49fe8
    //     0xa49fe0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa49fe4: stur            x5, [x0, #7]
    // 0xa49fe8: stp             x0, NULL, [SP]
    // 0xa49fec: r0 = _Double.fromInteger()
    //     0xa49fec: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa49ff0: mov             x3, x0
    // 0xa49ff4: ldur            x0, [fp, #-0x20]
    // 0xa49ff8: r2 = Null
    //     0xa49ff8: mov             x2, NULL
    // 0xa49ffc: r1 = Null
    //     0xa49ffc: mov             x1, NULL
    // 0xa4a000: stur            x3, [fp, #-0x30]
    // 0xa4a004: r4 = LoadClassIdInstr(r0)
    //     0xa4a004: ldur            x4, [x0, #-1]
    //     0xa4a008: ubfx            x4, x4, #0xc, #0x14
    // 0xa4a00c: sub             x4, x4, #0x5e
    // 0xa4a010: cmp             x4, #1
    // 0xa4a014: b.ls            #0xa4a028
    // 0xa4a018: r8 = String
    //     0xa4a018: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa4a01c: r3 = Null
    //     0xa4a01c: add             x3, PP, #0x56, lsl #12  ; [pp+0x563d0] Null
    //     0xa4a020: ldr             x3, [x3, #0x3d0]
    // 0xa4a024: r0 = String()
    //     0xa4a024: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa4a028: ldur            x16, [fp, #-0x20]
    // 0xa4a02c: stp             x16, NULL, [SP, #0x18]
    // 0xa4a030: r16 = "Product"
    //     0xa4a030: add             x16, PP, #0x56, lsl #12  ; [pp+0x563e0] "Product"
    //     0xa4a034: ldr             x16, [x16, #0x3e0]
    // 0xa4a038: r30 = "INR"
    //     0xa4a038: add             lr, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0xa4a03c: ldr             lr, [lr, #0x4c0]
    // 0xa4a040: stp             lr, x16, [SP, #8]
    // 0xa4a044: ldur            x16, [fp, #-0x30]
    // 0xa4a048: str             x16, [SP]
    // 0xa4a04c: ldur            x1, [fp, #-0x28]
    // 0xa4a050: r4 = const [0, 0x6, 0x5, 0x1, content, 0x1, currency, 0x4, id, 0x2, price, 0x5, type, 0x3, null]
    //     0xa4a050: add             x4, PP, #0x56, lsl #12  ; [pp+0x563e8] List(15) [0, 0x6, 0x5, 0x1, "content", 0x1, "currency", 0x4, "id", 0x2, "price", 0x5, "type", 0x3, Null]
    //     0xa4a054: ldr             x4, [x4, #0x3e8]
    // 0xa4a058: r0 = logAddToCart()
    //     0xa4a058: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0xa4a05c: ldur            x1, [fp, #-8]
    // 0xa4a060: LoadField: r0 = r1->field_f
    //     0xa4a060: ldur            w0, [x1, #0xf]
    // 0xa4a064: DecompressPointer r0
    //     0xa4a064: add             x0, x0, HEAP, lsl #32
    // 0xa4a068: LoadField: r2 = r0->field_b
    //     0xa4a068: ldur            w2, [x0, #0xb]
    // 0xa4a06c: DecompressPointer r2
    //     0xa4a06c: add             x2, x2, HEAP, lsl #32
    // 0xa4a070: cmp             w2, NULL
    // 0xa4a074: b.eq            #0xa4a46c
    // 0xa4a078: LoadField: r0 = r2->field_2f
    //     0xa4a078: ldur            w0, [x2, #0x2f]
    // 0xa4a07c: DecompressPointer r0
    //     0xa4a07c: add             x0, x0, HEAP, lsl #32
    // 0xa4a080: r2 = LoadClassIdInstr(r0)
    //     0xa4a080: ldur            x2, [x0, #-1]
    //     0xa4a084: ubfx            x2, x2, #0xc, #0x14
    // 0xa4a088: r16 = "add_to_bag"
    //     0xa4a088: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xa4a08c: ldr             x16, [x16, #0xa38]
    // 0xa4a090: stp             x16, x0, [SP]
    // 0xa4a094: mov             x0, x2
    // 0xa4a098: mov             lr, x0
    // 0xa4a09c: ldr             lr, [x21, lr, lsl #3]
    // 0xa4a0a0: blr             lr
    // 0xa4a0a4: tbnz            w0, #4, #0xa4a230
    // 0xa4a0a8: ldur            x2, [fp, #-0x10]
    // 0xa4a0ac: LoadField: r3 = r2->field_f
    //     0xa4a0ac: ldur            w3, [x2, #0xf]
    // 0xa4a0b0: DecompressPointer r3
    //     0xa4a0b0: add             x3, x3, HEAP, lsl #32
    // 0xa4a0b4: stur            x3, [fp, #-0x38]
    // 0xa4a0b8: r16 = Sentinel
    //     0xa4a0b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a0bc: cmp             w3, w16
    // 0xa4a0c0: b.eq            #0xa4a424
    // 0xa4a0c4: ldur            x4, [fp, #-8]
    // 0xa4a0c8: ldur            x5, [fp, #-0x18]
    // 0xa4a0cc: LoadField: r0 = r4->field_f
    //     0xa4a0cc: ldur            w0, [x4, #0xf]
    // 0xa4a0d0: DecompressPointer r0
    //     0xa4a0d0: add             x0, x0, HEAP, lsl #32
    // 0xa4a0d4: LoadField: r1 = r0->field_b
    //     0xa4a0d4: ldur            w1, [x0, #0xb]
    // 0xa4a0d8: DecompressPointer r1
    //     0xa4a0d8: add             x1, x1, HEAP, lsl #32
    // 0xa4a0dc: cmp             w1, NULL
    // 0xa4a0e0: b.eq            #0xa4a470
    // 0xa4a0e4: LoadField: r6 = r1->field_23
    //     0xa4a0e4: ldur            w6, [x1, #0x23]
    // 0xa4a0e8: DecompressPointer r6
    //     0xa4a0e8: add             x6, x6, HEAP, lsl #32
    // 0xa4a0ec: stur            x6, [fp, #-0x30]
    // 0xa4a0f0: LoadField: r7 = r1->field_f
    //     0xa4a0f0: ldur            w7, [x1, #0xf]
    // 0xa4a0f4: DecompressPointer r7
    //     0xa4a0f4: add             x7, x7, HEAP, lsl #32
    // 0xa4a0f8: stur            x7, [fp, #-0x28]
    // 0xa4a0fc: LoadField: r8 = r1->field_13
    //     0xa4a0fc: ldur            w8, [x1, #0x13]
    // 0xa4a100: DecompressPointer r8
    //     0xa4a100: add             x8, x8, HEAP, lsl #32
    // 0xa4a104: stur            x8, [fp, #-0x20]
    // 0xa4a108: LoadField: r9 = r1->field_27
    //     0xa4a108: ldur            x9, [x1, #0x27]
    // 0xa4a10c: r0 = BoxInt64Instr(r9)
    //     0xa4a10c: sbfiz           x0, x9, #1, #0x1f
    //     0xa4a110: cmp             x9, x0, asr #1
    //     0xa4a114: b.eq            #0xa4a120
    //     0xa4a118: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a11c: stur            x9, [x0, #7]
    // 0xa4a120: stp             x0, NULL, [SP]
    // 0xa4a124: r0 = _Double.fromInteger()
    //     0xa4a124: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4a128: stur            x0, [fp, #-0x40]
    // 0xa4a12c: r0 = EventData()
    //     0xa4a12c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa4a130: mov             x1, x0
    // 0xa4a134: ldur            x0, [fp, #-0x30]
    // 0xa4a138: stur            x1, [fp, #-0x48]
    // 0xa4a13c: StoreField: r1->field_13 = r0
    //     0xa4a13c: stur            w0, [x1, #0x13]
    // 0xa4a140: ldur            x0, [fp, #-0x40]
    // 0xa4a144: StoreField: r1->field_2f = r0
    //     0xa4a144: stur            w0, [x1, #0x2f]
    // 0xa4a148: ldur            x0, [fp, #-0x28]
    // 0xa4a14c: StoreField: r1->field_33 = r0
    //     0xa4a14c: stur            w0, [x1, #0x33]
    // 0xa4a150: ldur            x2, [fp, #-0x18]
    // 0xa4a154: StoreField: r1->field_3b = r2
    //     0xa4a154: stur            w2, [x1, #0x3b]
    // 0xa4a158: StoreField: r1->field_87 = r2
    //     0xa4a158: stur            w2, [x1, #0x87]
    // 0xa4a15c: ldur            x0, [fp, #-0x20]
    // 0xa4a160: StoreField: r1->field_8f = r0
    //     0xa4a160: stur            w0, [x1, #0x8f]
    // 0xa4a164: r0 = EventsRequest()
    //     0xa4a164: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa4a168: mov             x1, x0
    // 0xa4a16c: r0 = "add_to_bag_clicked"
    //     0xa4a16c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xa4a170: ldr             x0, [x0, #0xec8]
    // 0xa4a174: StoreField: r1->field_7 = r0
    //     0xa4a174: stur            w0, [x1, #7]
    // 0xa4a178: ldur            x0, [fp, #-0x48]
    // 0xa4a17c: StoreField: r1->field_b = r0
    //     0xa4a17c: stur            w0, [x1, #0xb]
    // 0xa4a180: ldur            x16, [fp, #-0x38]
    // 0xa4a184: stp             x1, x16, [SP]
    // 0xa4a188: r0 = postEvents()
    //     0xa4a188: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xa4a18c: ldur            x3, [fp, #-0x10]
    // 0xa4a190: LoadField: r0 = r3->field_f
    //     0xa4a190: ldur            w0, [x3, #0xf]
    // 0xa4a194: DecompressPointer r0
    //     0xa4a194: add             x0, x0, HEAP, lsl #32
    // 0xa4a198: stur            x0, [fp, #-0x30]
    // 0xa4a19c: r16 = Sentinel
    //     0xa4a19c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a1a0: cmp             w0, w16
    // 0xa4a1a4: b.eq            #0xa4a434
    // 0xa4a1a8: ldur            x4, [fp, #-8]
    // 0xa4a1ac: LoadField: r1 = r4->field_f
    //     0xa4a1ac: ldur            w1, [x4, #0xf]
    // 0xa4a1b0: DecompressPointer r1
    //     0xa4a1b0: add             x1, x1, HEAP, lsl #32
    // 0xa4a1b4: LoadField: r2 = r1->field_b
    //     0xa4a1b4: ldur            w2, [x1, #0xb]
    // 0xa4a1b8: DecompressPointer r2
    //     0xa4a1b8: add             x2, x2, HEAP, lsl #32
    // 0xa4a1bc: cmp             w2, NULL
    // 0xa4a1c0: b.eq            #0xa4a474
    // 0xa4a1c4: LoadField: r1 = r2->field_f
    //     0xa4a1c4: ldur            w1, [x2, #0xf]
    // 0xa4a1c8: DecompressPointer r1
    //     0xa4a1c8: add             x1, x1, HEAP, lsl #32
    // 0xa4a1cc: stur            x1, [fp, #-0x28]
    // 0xa4a1d0: LoadField: r3 = r2->field_13
    //     0xa4a1d0: ldur            w3, [x2, #0x13]
    // 0xa4a1d4: DecompressPointer r3
    //     0xa4a1d4: add             x3, x3, HEAP, lsl #32
    // 0xa4a1d8: stur            x3, [fp, #-0x20]
    // 0xa4a1dc: ArrayLoad: r4 = r2[0]  ; List_8
    //     0xa4a1dc: ldur            x4, [x2, #0x17]
    // 0xa4a1e0: stur            x4, [fp, #-0x50]
    // 0xa4a1e4: r0 = AddToBagRequest()
    //     0xa4a1e4: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xa4a1e8: mov             x1, x0
    // 0xa4a1ec: ldur            x0, [fp, #-0x28]
    // 0xa4a1f0: StoreField: r1->field_7 = r0
    //     0xa4a1f0: stur            w0, [x1, #7]
    // 0xa4a1f4: ldur            x0, [fp, #-0x20]
    // 0xa4a1f8: StoreField: r1->field_b = r0
    //     0xa4a1f8: stur            w0, [x1, #0xb]
    // 0xa4a1fc: ldur            x0, [fp, #-0x50]
    // 0xa4a200: StoreField: r1->field_f = r0
    //     0xa4a200: stur            x0, [x1, #0xf]
    // 0xa4a204: ldur            x16, [fp, #-0x30]
    // 0xa4a208: stp             x1, x16, [SP, #8]
    // 0xa4a20c: r16 = false
    //     0xa4a20c: add             x16, NULL, #0x30  ; false
    // 0xa4a210: str             x16, [SP]
    // 0xa4a214: r4 = 0
    //     0xa4a214: movz            x4, #0
    // 0xa4a218: ldr             x0, [SP, #0x10]
    // 0xa4a21c: r16 = UnlinkedCall_0x613b5c
    //     0xa4a21c: add             x16, PP, #0x56, lsl #12  ; [pp+0x563f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4a220: add             x16, x16, #0x3f0
    // 0xa4a224: ldp             x5, lr, [x16]
    // 0xa4a228: blr             lr
    // 0xa4a22c: b               #0xa4a414
    // 0xa4a230: ldur            x4, [fp, #-8]
    // 0xa4a234: ldur            x3, [fp, #-0x10]
    // 0xa4a238: ldur            x2, [fp, #-0x18]
    // 0xa4a23c: LoadField: r5 = r3->field_f
    //     0xa4a23c: ldur            w5, [x3, #0xf]
    // 0xa4a240: DecompressPointer r5
    //     0xa4a240: add             x5, x5, HEAP, lsl #32
    // 0xa4a244: stur            x5, [fp, #-0x38]
    // 0xa4a248: r16 = Sentinel
    //     0xa4a248: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a24c: cmp             w5, w16
    // 0xa4a250: b.eq            #0xa4a444
    // 0xa4a254: LoadField: r0 = r4->field_f
    //     0xa4a254: ldur            w0, [x4, #0xf]
    // 0xa4a258: DecompressPointer r0
    //     0xa4a258: add             x0, x0, HEAP, lsl #32
    // 0xa4a25c: LoadField: r1 = r0->field_b
    //     0xa4a25c: ldur            w1, [x0, #0xb]
    // 0xa4a260: DecompressPointer r1
    //     0xa4a260: add             x1, x1, HEAP, lsl #32
    // 0xa4a264: cmp             w1, NULL
    // 0xa4a268: b.eq            #0xa4a478
    // 0xa4a26c: LoadField: r6 = r1->field_23
    //     0xa4a26c: ldur            w6, [x1, #0x23]
    // 0xa4a270: DecompressPointer r6
    //     0xa4a270: add             x6, x6, HEAP, lsl #32
    // 0xa4a274: stur            x6, [fp, #-0x30]
    // 0xa4a278: LoadField: r7 = r1->field_f
    //     0xa4a278: ldur            w7, [x1, #0xf]
    // 0xa4a27c: DecompressPointer r7
    //     0xa4a27c: add             x7, x7, HEAP, lsl #32
    // 0xa4a280: stur            x7, [fp, #-0x28]
    // 0xa4a284: LoadField: r8 = r1->field_13
    //     0xa4a284: ldur            w8, [x1, #0x13]
    // 0xa4a288: DecompressPointer r8
    //     0xa4a288: add             x8, x8, HEAP, lsl #32
    // 0xa4a28c: stur            x8, [fp, #-0x20]
    // 0xa4a290: LoadField: r9 = r1->field_27
    //     0xa4a290: ldur            x9, [x1, #0x27]
    // 0xa4a294: r0 = BoxInt64Instr(r9)
    //     0xa4a294: sbfiz           x0, x9, #1, #0x1f
    //     0xa4a298: cmp             x9, x0, asr #1
    //     0xa4a29c: b.eq            #0xa4a2a8
    //     0xa4a2a0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a2a4: stur            x9, [x0, #7]
    // 0xa4a2a8: stp             x0, NULL, [SP]
    // 0xa4a2ac: r0 = _Double.fromInteger()
    //     0xa4a2ac: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4a2b0: stur            x0, [fp, #-0x40]
    // 0xa4a2b4: r0 = EventData()
    //     0xa4a2b4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xa4a2b8: mov             x1, x0
    // 0xa4a2bc: ldur            x0, [fp, #-0x30]
    // 0xa4a2c0: stur            x1, [fp, #-0x48]
    // 0xa4a2c4: StoreField: r1->field_13 = r0
    //     0xa4a2c4: stur            w0, [x1, #0x13]
    // 0xa4a2c8: ldur            x0, [fp, #-0x40]
    // 0xa4a2cc: StoreField: r1->field_2f = r0
    //     0xa4a2cc: stur            w0, [x1, #0x2f]
    // 0xa4a2d0: ldur            x0, [fp, #-0x28]
    // 0xa4a2d4: StoreField: r1->field_33 = r0
    //     0xa4a2d4: stur            w0, [x1, #0x33]
    // 0xa4a2d8: ldur            x0, [fp, #-0x18]
    // 0xa4a2dc: StoreField: r1->field_3b = r0
    //     0xa4a2dc: stur            w0, [x1, #0x3b]
    // 0xa4a2e0: StoreField: r1->field_87 = r0
    //     0xa4a2e0: stur            w0, [x1, #0x87]
    // 0xa4a2e4: ldur            x0, [fp, #-0x20]
    // 0xa4a2e8: StoreField: r1->field_8f = r0
    //     0xa4a2e8: stur            w0, [x1, #0x8f]
    // 0xa4a2ec: r0 = EventsRequest()
    //     0xa4a2ec: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xa4a2f0: mov             x1, x0
    // 0xa4a2f4: r0 = "buy_now_clicked"
    //     0xa4a2f4: add             x0, PP, #0x31, lsl #12  ; [pp+0x31be0] "buy_now_clicked"
    //     0xa4a2f8: ldr             x0, [x0, #0xbe0]
    // 0xa4a2fc: StoreField: r1->field_7 = r0
    //     0xa4a2fc: stur            w0, [x1, #7]
    // 0xa4a300: ldur            x0, [fp, #-0x48]
    // 0xa4a304: StoreField: r1->field_b = r0
    //     0xa4a304: stur            w0, [x1, #0xb]
    // 0xa4a308: ldur            x16, [fp, #-0x38]
    // 0xa4a30c: stp             x1, x16, [SP]
    // 0xa4a310: r0 = postEvents()
    //     0xa4a310: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xa4a314: ldur            x0, [fp, #-8]
    // 0xa4a318: LoadField: r1 = r0->field_f
    //     0xa4a318: ldur            w1, [x0, #0xf]
    // 0xa4a31c: DecompressPointer r1
    //     0xa4a31c: add             x1, x1, HEAP, lsl #32
    // 0xa4a320: LoadField: r2 = r1->field_b
    //     0xa4a320: ldur            w2, [x1, #0xb]
    // 0xa4a324: DecompressPointer r2
    //     0xa4a324: add             x2, x2, HEAP, lsl #32
    // 0xa4a328: cmp             w2, NULL
    // 0xa4a32c: b.eq            #0xa4a47c
    // 0xa4a330: LoadField: r1 = r2->field_43
    //     0xa4a330: ldur            w1, [x2, #0x43]
    // 0xa4a334: DecompressPointer r1
    //     0xa4a334: add             x1, x1, HEAP, lsl #32
    // 0xa4a338: cmp             w1, NULL
    // 0xa4a33c: b.ne            #0xa4a348
    // 0xa4a340: r1 = Null
    //     0xa4a340: mov             x1, NULL
    // 0xa4a344: b               #0xa4a35c
    // 0xa4a348: LoadField: r2 = r1->field_7
    //     0xa4a348: ldur            w2, [x1, #7]
    // 0xa4a34c: cbnz            w2, #0xa4a358
    // 0xa4a350: r1 = false
    //     0xa4a350: add             x1, NULL, #0x30  ; false
    // 0xa4a354: b               #0xa4a35c
    // 0xa4a358: r1 = true
    //     0xa4a358: add             x1, NULL, #0x20  ; true
    // 0xa4a35c: cmp             w1, NULL
    // 0xa4a360: b.eq            #0xa4a3b0
    // 0xa4a364: tbnz            w1, #4, #0xa4a3b0
    // 0xa4a368: LoadField: r3 = r0->field_13
    //     0xa4a368: ldur            w3, [x0, #0x13]
    // 0xa4a36c: DecompressPointer r3
    //     0xa4a36c: add             x3, x3, HEAP, lsl #32
    // 0xa4a370: ldur            x2, [fp, #-0x10]
    // 0xa4a374: stur            x3, [fp, #-0x18]
    // 0xa4a378: r1 = Function '<anonymous closure>':.
    //     0xa4a378: add             x1, PP, #0x56, lsl #12  ; [pp+0x56400] AnonymousClosure: (0xa49080), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa4a37c: ldr             x1, [x1, #0x400]
    // 0xa4a380: r0 = AllocateClosure()
    //     0xa4a380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4a384: stp             x0, NULL, [SP, #0x18]
    // 0xa4a388: ldur            x16, [fp, #-0x18]
    // 0xa4a38c: r30 = true
    //     0xa4a38c: add             lr, NULL, #0x20  ; true
    // 0xa4a390: stp             lr, x16, [SP, #8]
    // 0xa4a394: r16 = Instance_RoundedRectangleBorder
    //     0xa4a394: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xa4a398: ldr             x16, [x16, #0xc78]
    // 0xa4a39c: str             x16, [SP]
    // 0xa4a3a0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xa4a3a0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xa4a3a4: ldr             x4, [x4, #0xb20]
    // 0xa4a3a8: r0 = showModalBottomSheet()
    //     0xa4a3a8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xa4a3ac: b               #0xa4a414
    // 0xa4a3b0: r16 = PreferenceManager
    //     0xa4a3b0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xa4a3b4: ldr             x16, [x16, #0x878]
    // 0xa4a3b8: str             x16, [SP]
    // 0xa4a3bc: r0 = toString()
    //     0xa4a3bc: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xa4a3c0: r16 = <PreferenceManager>
    //     0xa4a3c0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xa4a3c4: ldr             x16, [x16, #0x880]
    // 0xa4a3c8: stp             x0, x16, [SP]
    // 0xa4a3cc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xa4a3cc: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xa4a3d0: r0 = Inst.find()
    //     0xa4a3d0: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xa4a3d4: mov             x1, x0
    // 0xa4a3d8: r2 = "token"
    //     0xa4a3d8: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xa4a3dc: ldr             x2, [x2, #0x958]
    // 0xa4a3e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa4a3e0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa4a3e4: r0 = getString()
    //     0xa4a3e4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xa4a3e8: ldur            x2, [fp, #-0x10]
    // 0xa4a3ec: r1 = Function '<anonymous closure>':.
    //     0xa4a3ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56408] AnonymousClosure: (0xa4a480), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xa4a3f0: ldr             x1, [x1, #0x408]
    // 0xa4a3f4: stur            x0, [fp, #-8]
    // 0xa4a3f8: r0 = AllocateClosure()
    //     0xa4a3f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa4a3fc: r16 = <Null?>
    //     0xa4a3fc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xa4a400: ldur            lr, [fp, #-8]
    // 0xa4a404: stp             lr, x16, [SP, #8]
    // 0xa4a408: str             x0, [SP]
    // 0xa4a40c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa4a40c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa4a410: r0 = then()
    //     0xa4a410: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xa4a414: r0 = Null
    //     0xa4a414: mov             x0, NULL
    // 0xa4a418: LeaveFrame
    //     0xa4a418: mov             SP, fp
    //     0xa4a41c: ldp             fp, lr, [SP], #0x10
    // 0xa4a420: ret
    //     0xa4a420: ret             
    // 0xa4a424: r16 = "controller"
    //     0xa4a424: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4a428: str             x16, [SP]
    // 0xa4a42c: r0 = _throwLocalNotInitialized()
    //     0xa4a42c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4a430: brk             #0
    // 0xa4a434: r16 = "controller"
    //     0xa4a434: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4a438: str             x16, [SP]
    // 0xa4a43c: r0 = _throwLocalNotInitialized()
    //     0xa4a43c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4a440: brk             #0
    // 0xa4a444: r16 = "controller"
    //     0xa4a444: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4a448: str             x16, [SP]
    // 0xa4a44c: r0 = _throwLocalNotInitialized()
    //     0xa4a44c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4a450: brk             #0
    // 0xa4a454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4a454: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4a458: b               #0xa49d50
    // 0xa4a45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a45c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a460: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a468: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a46c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a46c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a470: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a478: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4a47c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4a47c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xa4a480, size: 0x9c0
    // 0xa4a480: EnterFrame
    //     0xa4a480: stp             fp, lr, [SP, #-0x10]!
    //     0xa4a484: mov             fp, SP
    // 0xa4a488: AllocStack(0x50)
    //     0xa4a488: sub             SP, SP, #0x50
    // 0xa4a48c: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xa4a48c: stur            NULL, [fp, #-8]
    //     0xa4a490: movz            x0, #0
    //     0xa4a494: add             x1, fp, w0, sxtw #2
    //     0xa4a498: ldr             x1, [x1, #0x18]
    //     0xa4a49c: add             x2, fp, w0, sxtw #2
    //     0xa4a4a0: ldr             x2, [x2, #0x10]
    //     0xa4a4a4: stur            x2, [fp, #-0x18]
    //     0xa4a4a8: ldur            w3, [x1, #0x17]
    //     0xa4a4ac: add             x3, x3, HEAP, lsl #32
    //     0xa4a4b0: stur            x3, [fp, #-0x10]
    // 0xa4a4b4: CheckStackOverflow
    //     0xa4a4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4a4b8: cmp             SP, x16
    //     0xa4a4bc: b.ls            #0xa4ae24
    // 0xa4a4c0: InitAsync() -> Future<Null?>
    //     0xa4a4c0: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xa4a4c4: bl              #0x6326e0  ; InitAsyncStub
    // 0xa4a4c8: ldur            x0, [fp, #-0x18]
    // 0xa4a4cc: r1 = LoadClassIdInstr(r0)
    //     0xa4a4cc: ldur            x1, [x0, #-1]
    //     0xa4a4d0: ubfx            x1, x1, #0xc, #0x14
    // 0xa4a4d4: r16 = ""
    //     0xa4a4d4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4a4d8: stp             x16, x0, [SP]
    // 0xa4a4dc: mov             x0, x1
    // 0xa4a4e0: mov             lr, x0
    // 0xa4a4e4: ldr             lr, [x21, lr, lsl #3]
    // 0xa4a4e8: blr             lr
    // 0xa4a4ec: tbz             w0, #4, #0xa4abb4
    // 0xa4a4f0: ldur            x2, [fp, #-0x10]
    // 0xa4a4f4: LoadField: r3 = r2->field_b
    //     0xa4a4f4: ldur            w3, [x2, #0xb]
    // 0xa4a4f8: DecompressPointer r3
    //     0xa4a4f8: add             x3, x3, HEAP, lsl #32
    // 0xa4a4fc: stur            x3, [fp, #-0x28]
    // 0xa4a500: LoadField: r0 = r3->field_f
    //     0xa4a500: ldur            w0, [x3, #0xf]
    // 0xa4a504: DecompressPointer r0
    //     0xa4a504: add             x0, x0, HEAP, lsl #32
    // 0xa4a508: LoadField: r1 = r0->field_b
    //     0xa4a508: ldur            w1, [x0, #0xb]
    // 0xa4a50c: DecompressPointer r1
    //     0xa4a50c: add             x1, x1, HEAP, lsl #32
    // 0xa4a510: cmp             w1, NULL
    // 0xa4a514: b.eq            #0xa4ae2c
    // 0xa4a518: LoadField: r4 = r1->field_f
    //     0xa4a518: ldur            w4, [x1, #0xf]
    // 0xa4a51c: DecompressPointer r4
    //     0xa4a51c: add             x4, x4, HEAP, lsl #32
    // 0xa4a520: stur            x4, [fp, #-0x18]
    // 0xa4a524: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xa4a524: ldur            x5, [x1, #0x17]
    // 0xa4a528: stur            x5, [fp, #-0x20]
    // 0xa4a52c: LoadField: r0 = r1->field_27
    //     0xa4a52c: ldur            x0, [x1, #0x27]
    // 0xa4a530: mul             x6, x0, x5
    // 0xa4a534: r0 = BoxInt64Instr(r6)
    //     0xa4a534: sbfiz           x0, x6, #1, #0x1f
    //     0xa4a538: cmp             x6, x0, asr #1
    //     0xa4a53c: b.eq            #0xa4a548
    //     0xa4a540: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a544: stur            x6, [x0, #7]
    // 0xa4a548: stp             x0, NULL, [SP]
    // 0xa4a54c: r0 = _Double.fromInteger()
    //     0xa4a54c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4a550: stur            x0, [fp, #-0x30]
    // 0xa4a554: r0 = CheckoutEventData()
    //     0xa4a554: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa4a558: mov             x2, x0
    // 0xa4a55c: ldur            x0, [fp, #-0x30]
    // 0xa4a560: stur            x2, [fp, #-0x38]
    // 0xa4a564: StoreField: r2->field_7 = r0
    //     0xa4a564: stur            w0, [x2, #7]
    // 0xa4a568: ldur            x3, [fp, #-0x20]
    // 0xa4a56c: r0 = BoxInt64Instr(r3)
    //     0xa4a56c: sbfiz           x0, x3, #1, #0x1f
    //     0xa4a570: cmp             x3, x0, asr #1
    //     0xa4a574: b.eq            #0xa4a580
    //     0xa4a578: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a57c: stur            x3, [x0, #7]
    // 0xa4a580: StoreField: r2->field_b = r0
    //     0xa4a580: stur            w0, [x2, #0xb]
    // 0xa4a584: ldur            x0, [fp, #-0x18]
    // 0xa4a588: StoreField: r2->field_f = r0
    //     0xa4a588: stur            w0, [x2, #0xf]
    // 0xa4a58c: ldur            x0, [fp, #-0x10]
    // 0xa4a590: LoadField: r1 = r0->field_f
    //     0xa4a590: ldur            w1, [x0, #0xf]
    // 0xa4a594: DecompressPointer r1
    //     0xa4a594: add             x1, x1, HEAP, lsl #32
    // 0xa4a598: r16 = Sentinel
    //     0xa4a598: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a59c: cmp             w1, w16
    // 0xa4a5a0: b.eq            #0xa4adc4
    // 0xa4a5a4: str             x1, [SP]
    // 0xa4a5a8: r4 = 0
    //     0xa4a5a8: movz            x4, #0
    // 0xa4a5ac: ldr             x0, [SP]
    // 0xa4a5b0: r16 = UnlinkedCall_0x613b5c
    //     0xa4a5b0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56410] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4a5b4: add             x16, x16, #0x410
    // 0xa4a5b8: ldp             x5, lr, [x16]
    // 0xa4a5bc: blr             lr
    // 0xa4a5c0: LoadField: r1 = r0->field_1b
    //     0xa4a5c0: ldur            w1, [x0, #0x1b]
    // 0xa4a5c4: DecompressPointer r1
    //     0xa4a5c4: add             x1, x1, HEAP, lsl #32
    // 0xa4a5c8: cmp             w1, NULL
    // 0xa4a5cc: b.ne            #0xa4a5d8
    // 0xa4a5d0: r0 = Null
    //     0xa4a5d0: mov             x0, NULL
    // 0xa4a5d4: b               #0xa4a5f0
    // 0xa4a5d8: LoadField: r0 = r1->field_b
    //     0xa4a5d8: ldur            w0, [x1, #0xb]
    // 0xa4a5dc: cbz             w0, #0xa4a5e8
    // 0xa4a5e0: r1 = false
    //     0xa4a5e0: add             x1, NULL, #0x30  ; false
    // 0xa4a5e4: b               #0xa4a5ec
    // 0xa4a5e8: r1 = true
    //     0xa4a5e8: add             x1, NULL, #0x20  ; true
    // 0xa4a5ec: mov             x0, x1
    // 0xa4a5f0: cmp             w0, NULL
    // 0xa4a5f4: b.eq            #0xa4a5fc
    // 0xa4a5f8: tbz             w0, #4, #0xa4a6a4
    // 0xa4a5fc: ldur            x0, [fp, #-0x10]
    // 0xa4a600: LoadField: r1 = r0->field_f
    //     0xa4a600: ldur            w1, [x0, #0xf]
    // 0xa4a604: DecompressPointer r1
    //     0xa4a604: add             x1, x1, HEAP, lsl #32
    // 0xa4a608: r16 = Sentinel
    //     0xa4a608: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a60c: cmp             w1, w16
    // 0xa4a610: b.eq            #0xa4add4
    // 0xa4a614: str             x1, [SP]
    // 0xa4a618: r4 = 0
    //     0xa4a618: movz            x4, #0
    // 0xa4a61c: ldr             x0, [SP]
    // 0xa4a620: r16 = UnlinkedCall_0x613b5c
    //     0xa4a620: add             x16, PP, #0x56, lsl #12  ; [pp+0x56420] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4a624: add             x16, x16, #0x420
    // 0xa4a628: ldp             x5, lr, [x16]
    // 0xa4a62c: blr             lr
    // 0xa4a630: LoadField: r1 = r0->field_1b
    //     0xa4a630: ldur            w1, [x0, #0x1b]
    // 0xa4a634: DecompressPointer r1
    //     0xa4a634: add             x1, x1, HEAP, lsl #32
    // 0xa4a638: cmp             w1, NULL
    // 0xa4a63c: b.ne            #0xa4a648
    // 0xa4a640: r0 = Null
    //     0xa4a640: mov             x0, NULL
    // 0xa4a644: b               #0xa4a68c
    // 0xa4a648: r0 = first()
    //     0xa4a648: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xa4a64c: cmp             w0, NULL
    // 0xa4a650: b.ne            #0xa4a65c
    // 0xa4a654: r0 = Null
    //     0xa4a654: mov             x0, NULL
    // 0xa4a658: b               #0xa4a68c
    // 0xa4a65c: LoadField: r1 = r0->field_13
    //     0xa4a65c: ldur            w1, [x0, #0x13]
    // 0xa4a660: DecompressPointer r1
    //     0xa4a660: add             x1, x1, HEAP, lsl #32
    // 0xa4a664: cmp             w1, NULL
    // 0xa4a668: b.ne            #0xa4a674
    // 0xa4a66c: r0 = Null
    //     0xa4a66c: mov             x0, NULL
    // 0xa4a670: b               #0xa4a68c
    // 0xa4a674: LoadField: r0 = r1->field_7
    //     0xa4a674: ldur            w0, [x1, #7]
    // 0xa4a678: cbz             w0, #0xa4a684
    // 0xa4a67c: r1 = false
    //     0xa4a67c: add             x1, NULL, #0x30  ; false
    // 0xa4a680: b               #0xa4a688
    // 0xa4a684: r1 = true
    //     0xa4a684: add             x1, NULL, #0x20  ; true
    // 0xa4a688: mov             x0, x1
    // 0xa4a68c: cmp             w0, NULL
    // 0xa4a690: b.ne            #0xa4a6a0
    // 0xa4a694: ldur            x2, [fp, #-0x10]
    // 0xa4a698: ldur            x0, [fp, #-0x28]
    // 0xa4a69c: b               #0xa4a934
    // 0xa4a6a0: tbnz            w0, #4, #0xa4a92c
    // 0xa4a6a4: ldur            x0, [fp, #-0x10]
    // 0xa4a6a8: ldur            x1, [fp, #-0x28]
    // 0xa4a6ac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4a6ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4a6b0: ldr             x0, [x0, #0x1c80]
    //     0xa4a6b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4a6b8: cmp             w0, w16
    //     0xa4a6bc: b.ne            #0xa4a6c8
    //     0xa4a6c0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4a6c4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4a6c8: r1 = Null
    //     0xa4a6c8: mov             x1, NULL
    // 0xa4a6cc: r2 = 40
    //     0xa4a6cc: movz            x2, #0x28
    // 0xa4a6d0: r0 = AllocateArray()
    //     0xa4a6d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4a6d4: mov             x2, x0
    // 0xa4a6d8: stur            x2, [fp, #-0x18]
    // 0xa4a6dc: r16 = "couponCode"
    //     0xa4a6dc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa4a6e0: ldr             x16, [x16, #0x310]
    // 0xa4a6e4: StoreField: r2->field_f = r16
    //     0xa4a6e4: stur            w16, [x2, #0xf]
    // 0xa4a6e8: r16 = ""
    //     0xa4a6e8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4a6ec: StoreField: r2->field_13 = r16
    //     0xa4a6ec: stur            w16, [x2, #0x13]
    // 0xa4a6f0: r16 = "product_id"
    //     0xa4a6f0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4a6f4: ldr             x16, [x16, #0x9b8]
    // 0xa4a6f8: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4a6f8: stur            w16, [x2, #0x17]
    // 0xa4a6fc: ldur            x0, [fp, #-0x28]
    // 0xa4a700: LoadField: r1 = r0->field_f
    //     0xa4a700: ldur            w1, [x0, #0xf]
    // 0xa4a704: DecompressPointer r1
    //     0xa4a704: add             x1, x1, HEAP, lsl #32
    // 0xa4a708: LoadField: r3 = r1->field_b
    //     0xa4a708: ldur            w3, [x1, #0xb]
    // 0xa4a70c: DecompressPointer r3
    //     0xa4a70c: add             x3, x3, HEAP, lsl #32
    // 0xa4a710: cmp             w3, NULL
    // 0xa4a714: b.eq            #0xa4ae30
    // 0xa4a718: LoadField: r0 = r3->field_f
    //     0xa4a718: ldur            w0, [x3, #0xf]
    // 0xa4a71c: DecompressPointer r0
    //     0xa4a71c: add             x0, x0, HEAP, lsl #32
    // 0xa4a720: StoreField: r2->field_1b = r0
    //     0xa4a720: stur            w0, [x2, #0x1b]
    // 0xa4a724: r16 = "sku_id"
    //     0xa4a724: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4a728: ldr             x16, [x16, #0x498]
    // 0xa4a72c: StoreField: r2->field_1f = r16
    //     0xa4a72c: stur            w16, [x2, #0x1f]
    // 0xa4a730: LoadField: r0 = r3->field_13
    //     0xa4a730: ldur            w0, [x3, #0x13]
    // 0xa4a734: DecompressPointer r0
    //     0xa4a734: add             x0, x0, HEAP, lsl #32
    // 0xa4a738: StoreField: r2->field_23 = r0
    //     0xa4a738: stur            w0, [x2, #0x23]
    // 0xa4a73c: r16 = "quantity"
    //     0xa4a73c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4a740: ldr             x16, [x16, #0x428]
    // 0xa4a744: StoreField: r2->field_27 = r16
    //     0xa4a744: stur            w16, [x2, #0x27]
    // 0xa4a748: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4a748: ldur            x4, [x3, #0x17]
    // 0xa4a74c: r0 = BoxInt64Instr(r4)
    //     0xa4a74c: sbfiz           x0, x4, #1, #0x1f
    //     0xa4a750: cmp             x4, x0, asr #1
    //     0xa4a754: b.eq            #0xa4a760
    //     0xa4a758: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a75c: stur            x4, [x0, #7]
    // 0xa4a760: mov             x1, x2
    // 0xa4a764: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4a764: add             x25, x1, #0x2b
    //     0xa4a768: str             w0, [x25]
    //     0xa4a76c: tbz             w0, #0, #0xa4a788
    //     0xa4a770: ldurb           w16, [x1, #-1]
    //     0xa4a774: ldurb           w17, [x0, #-1]
    //     0xa4a778: and             x16, x17, x16, lsr #2
    //     0xa4a77c: tst             x16, HEAP, lsr #32
    //     0xa4a780: b.eq            #0xa4a788
    //     0xa4a784: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4a788: r16 = "checkout_event_data"
    //     0xa4a788: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4a78c: ldr             x16, [x16, #0xd50]
    // 0xa4a790: StoreField: r2->field_2f = r16
    //     0xa4a790: stur            w16, [x2, #0x2f]
    // 0xa4a794: mov             x1, x2
    // 0xa4a798: ldur            x0, [fp, #-0x38]
    // 0xa4a79c: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4a79c: add             x25, x1, #0x33
    //     0xa4a7a0: str             w0, [x25]
    //     0xa4a7a4: tbz             w0, #0, #0xa4a7c0
    //     0xa4a7a8: ldurb           w16, [x1, #-1]
    //     0xa4a7ac: ldurb           w17, [x0, #-1]
    //     0xa4a7b0: and             x16, x17, x16, lsr #2
    //     0xa4a7b4: tst             x16, HEAP, lsr #32
    //     0xa4a7b8: b.eq            #0xa4a7c0
    //     0xa4a7bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4a7c0: r16 = "previousScreenSource"
    //     0xa4a7c0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4a7c4: ldr             x16, [x16, #0x448]
    // 0xa4a7c8: StoreField: r2->field_37 = r16
    //     0xa4a7c8: stur            w16, [x2, #0x37]
    // 0xa4a7cc: r16 = "product_page"
    //     0xa4a7cc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4a7d0: ldr             x16, [x16, #0x480]
    // 0xa4a7d4: StoreField: r2->field_3b = r16
    //     0xa4a7d4: stur            w16, [x2, #0x3b]
    // 0xa4a7d8: r16 = "is_skipped_address"
    //     0xa4a7d8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4a7dc: ldr             x16, [x16, #0xb80]
    // 0xa4a7e0: StoreField: r2->field_3f = r16
    //     0xa4a7e0: stur            w16, [x2, #0x3f]
    // 0xa4a7e4: r16 = true
    //     0xa4a7e4: add             x16, NULL, #0x20  ; true
    // 0xa4a7e8: StoreField: r2->field_43 = r16
    //     0xa4a7e8: stur            w16, [x2, #0x43]
    // 0xa4a7ec: r16 = "coming_from"
    //     0xa4a7ec: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4a7f0: ldr             x16, [x16, #0x328]
    // 0xa4a7f4: StoreField: r2->field_47 = r16
    //     0xa4a7f4: stur            w16, [x2, #0x47]
    // 0xa4a7f8: LoadField: r0 = r3->field_2f
    //     0xa4a7f8: ldur            w0, [x3, #0x2f]
    // 0xa4a7fc: DecompressPointer r0
    //     0xa4a7fc: add             x0, x0, HEAP, lsl #32
    // 0xa4a800: mov             x1, x2
    // 0xa4a804: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4a804: add             x25, x1, #0x4b
    //     0xa4a808: str             w0, [x25]
    //     0xa4a80c: tbz             w0, #0, #0xa4a828
    //     0xa4a810: ldurb           w16, [x1, #-1]
    //     0xa4a814: ldurb           w17, [x0, #-1]
    //     0xa4a818: and             x16, x17, x16, lsr #2
    //     0xa4a81c: tst             x16, HEAP, lsr #32
    //     0xa4a820: b.eq            #0xa4a828
    //     0xa4a824: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4a828: r16 = "checkout_id"
    //     0xa4a828: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa4a82c: ldr             x16, [x16, #0xb88]
    // 0xa4a830: StoreField: r2->field_4f = r16
    //     0xa4a830: stur            w16, [x2, #0x4f]
    // 0xa4a834: ldur            x0, [fp, #-0x10]
    // 0xa4a838: LoadField: r1 = r0->field_f
    //     0xa4a838: ldur            w1, [x0, #0xf]
    // 0xa4a83c: DecompressPointer r1
    //     0xa4a83c: add             x1, x1, HEAP, lsl #32
    // 0xa4a840: r16 = Sentinel
    //     0xa4a840: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a844: cmp             w1, w16
    // 0xa4a848: b.eq            #0xa4ade4
    // 0xa4a84c: str             x1, [SP]
    // 0xa4a850: r4 = 0
    //     0xa4a850: movz            x4, #0
    // 0xa4a854: ldr             x0, [SP]
    // 0xa4a858: r16 = UnlinkedCall_0x613b5c
    //     0xa4a858: add             x16, PP, #0x56, lsl #12  ; [pp+0x56430] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4a85c: add             x16, x16, #0x430
    // 0xa4a860: ldp             x5, lr, [x16]
    // 0xa4a864: blr             lr
    // 0xa4a868: ldur            x1, [fp, #-0x18]
    // 0xa4a86c: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4a86c: add             x25, x1, #0x53
    //     0xa4a870: str             w0, [x25]
    //     0xa4a874: tbz             w0, #0, #0xa4a890
    //     0xa4a878: ldurb           w16, [x1, #-1]
    //     0xa4a87c: ldurb           w17, [x0, #-1]
    //     0xa4a880: and             x16, x17, x16, lsr #2
    //     0xa4a884: tst             x16, HEAP, lsr #32
    //     0xa4a888: b.eq            #0xa4a890
    //     0xa4a88c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4a890: ldur            x1, [fp, #-0x18]
    // 0xa4a894: r16 = "user_data"
    //     0xa4a894: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa4a898: ldr             x16, [x16, #0x58]
    // 0xa4a89c: StoreField: r1->field_57 = r16
    //     0xa4a89c: stur            w16, [x1, #0x57]
    // 0xa4a8a0: ldur            x2, [fp, #-0x10]
    // 0xa4a8a4: LoadField: r0 = r2->field_f
    //     0xa4a8a4: ldur            w0, [x2, #0xf]
    // 0xa4a8a8: DecompressPointer r0
    //     0xa4a8a8: add             x0, x0, HEAP, lsl #32
    // 0xa4a8ac: r16 = Sentinel
    //     0xa4a8ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4a8b0: cmp             w0, w16
    // 0xa4a8b4: b.eq            #0xa4adf4
    // 0xa4a8b8: str             x0, [SP]
    // 0xa4a8bc: r4 = 0
    //     0xa4a8bc: movz            x4, #0
    // 0xa4a8c0: ldr             x0, [SP]
    // 0xa4a8c4: r16 = UnlinkedCall_0x613b5c
    //     0xa4a8c4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56440] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4a8c8: add             x16, x16, #0x440
    // 0xa4a8cc: ldp             x5, lr, [x16]
    // 0xa4a8d0: blr             lr
    // 0xa4a8d4: ldur            x1, [fp, #-0x18]
    // 0xa4a8d8: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4a8d8: add             x25, x1, #0x5b
    //     0xa4a8dc: str             w0, [x25]
    //     0xa4a8e0: tbz             w0, #0, #0xa4a8fc
    //     0xa4a8e4: ldurb           w16, [x1, #-1]
    //     0xa4a8e8: ldurb           w17, [x0, #-1]
    //     0xa4a8ec: and             x16, x17, x16, lsr #2
    //     0xa4a8f0: tst             x16, HEAP, lsr #32
    //     0xa4a8f4: b.eq            #0xa4a8fc
    //     0xa4a8f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4a8fc: r16 = <String, dynamic>
    //     0xa4a8fc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa4a900: ldur            lr, [fp, #-0x18]
    // 0xa4a904: stp             lr, x16, [SP]
    // 0xa4a908: r0 = Map._fromLiteral()
    //     0xa4a908: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4a90c: r16 = "/checkout_request_address_page"
    //     0xa4a90c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xa4a910: ldr             x16, [x16, #0x9e8]
    // 0xa4a914: stp             x16, NULL, [SP, #8]
    // 0xa4a918: str             x0, [SP]
    // 0xa4a91c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4a91c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4a920: ldr             x4, [x4, #0x438]
    // 0xa4a924: r0 = GetNavigation.toNamed()
    //     0xa4a924: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4a928: b               #0xa4adbc
    // 0xa4a92c: ldur            x2, [fp, #-0x10]
    // 0xa4a930: ldur            x0, [fp, #-0x28]
    // 0xa4a934: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4a934: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4a938: ldr             x0, [x0, #0x1c80]
    //     0xa4a93c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4a940: cmp             w0, w16
    //     0xa4a944: b.ne            #0xa4a950
    //     0xa4a948: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4a94c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4a950: r1 = Null
    //     0xa4a950: mov             x1, NULL
    // 0xa4a954: r2 = 40
    //     0xa4a954: movz            x2, #0x28
    // 0xa4a958: r0 = AllocateArray()
    //     0xa4a958: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4a95c: mov             x2, x0
    // 0xa4a960: stur            x2, [fp, #-0x18]
    // 0xa4a964: r16 = "couponCode"
    //     0xa4a964: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xa4a968: ldr             x16, [x16, #0x310]
    // 0xa4a96c: StoreField: r2->field_f = r16
    //     0xa4a96c: stur            w16, [x2, #0xf]
    // 0xa4a970: r16 = ""
    //     0xa4a970: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa4a974: StoreField: r2->field_13 = r16
    //     0xa4a974: stur            w16, [x2, #0x13]
    // 0xa4a978: r16 = "product_id"
    //     0xa4a978: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4a97c: ldr             x16, [x16, #0x9b8]
    // 0xa4a980: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4a980: stur            w16, [x2, #0x17]
    // 0xa4a984: ldur            x0, [fp, #-0x28]
    // 0xa4a988: LoadField: r1 = r0->field_f
    //     0xa4a988: ldur            w1, [x0, #0xf]
    // 0xa4a98c: DecompressPointer r1
    //     0xa4a98c: add             x1, x1, HEAP, lsl #32
    // 0xa4a990: LoadField: r3 = r1->field_b
    //     0xa4a990: ldur            w3, [x1, #0xb]
    // 0xa4a994: DecompressPointer r3
    //     0xa4a994: add             x3, x3, HEAP, lsl #32
    // 0xa4a998: cmp             w3, NULL
    // 0xa4a99c: b.eq            #0xa4ae34
    // 0xa4a9a0: LoadField: r0 = r3->field_f
    //     0xa4a9a0: ldur            w0, [x3, #0xf]
    // 0xa4a9a4: DecompressPointer r0
    //     0xa4a9a4: add             x0, x0, HEAP, lsl #32
    // 0xa4a9a8: StoreField: r2->field_1b = r0
    //     0xa4a9a8: stur            w0, [x2, #0x1b]
    // 0xa4a9ac: r16 = "sku_id"
    //     0xa4a9ac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4a9b0: ldr             x16, [x16, #0x498]
    // 0xa4a9b4: StoreField: r2->field_1f = r16
    //     0xa4a9b4: stur            w16, [x2, #0x1f]
    // 0xa4a9b8: LoadField: r0 = r3->field_13
    //     0xa4a9b8: ldur            w0, [x3, #0x13]
    // 0xa4a9bc: DecompressPointer r0
    //     0xa4a9bc: add             x0, x0, HEAP, lsl #32
    // 0xa4a9c0: StoreField: r2->field_23 = r0
    //     0xa4a9c0: stur            w0, [x2, #0x23]
    // 0xa4a9c4: r16 = "quantity"
    //     0xa4a9c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4a9c8: ldr             x16, [x16, #0x428]
    // 0xa4a9cc: StoreField: r2->field_27 = r16
    //     0xa4a9cc: stur            w16, [x2, #0x27]
    // 0xa4a9d0: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4a9d0: ldur            x4, [x3, #0x17]
    // 0xa4a9d4: r0 = BoxInt64Instr(r4)
    //     0xa4a9d4: sbfiz           x0, x4, #1, #0x1f
    //     0xa4a9d8: cmp             x4, x0, asr #1
    //     0xa4a9dc: b.eq            #0xa4a9e8
    //     0xa4a9e0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4a9e4: stur            x4, [x0, #7]
    // 0xa4a9e8: mov             x1, x2
    // 0xa4a9ec: ArrayStore: r1[7] = r0  ; List_4
    //     0xa4a9ec: add             x25, x1, #0x2b
    //     0xa4a9f0: str             w0, [x25]
    //     0xa4a9f4: tbz             w0, #0, #0xa4aa10
    //     0xa4a9f8: ldurb           w16, [x1, #-1]
    //     0xa4a9fc: ldurb           w17, [x0, #-1]
    //     0xa4aa00: and             x16, x17, x16, lsr #2
    //     0xa4aa04: tst             x16, HEAP, lsr #32
    //     0xa4aa08: b.eq            #0xa4aa10
    //     0xa4aa0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4aa10: r16 = "checkout_event_data"
    //     0xa4aa10: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4aa14: ldr             x16, [x16, #0xd50]
    // 0xa4aa18: StoreField: r2->field_2f = r16
    //     0xa4aa18: stur            w16, [x2, #0x2f]
    // 0xa4aa1c: mov             x1, x2
    // 0xa4aa20: ldur            x0, [fp, #-0x38]
    // 0xa4aa24: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4aa24: add             x25, x1, #0x33
    //     0xa4aa28: str             w0, [x25]
    //     0xa4aa2c: tbz             w0, #0, #0xa4aa48
    //     0xa4aa30: ldurb           w16, [x1, #-1]
    //     0xa4aa34: ldurb           w17, [x0, #-1]
    //     0xa4aa38: and             x16, x17, x16, lsr #2
    //     0xa4aa3c: tst             x16, HEAP, lsr #32
    //     0xa4aa40: b.eq            #0xa4aa48
    //     0xa4aa44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4aa48: r16 = "previousScreenSource"
    //     0xa4aa48: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4aa4c: ldr             x16, [x16, #0x448]
    // 0xa4aa50: StoreField: r2->field_37 = r16
    //     0xa4aa50: stur            w16, [x2, #0x37]
    // 0xa4aa54: r16 = "product_page"
    //     0xa4aa54: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4aa58: ldr             x16, [x16, #0x480]
    // 0xa4aa5c: StoreField: r2->field_3b = r16
    //     0xa4aa5c: stur            w16, [x2, #0x3b]
    // 0xa4aa60: r16 = "is_skipped_address"
    //     0xa4aa60: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4aa64: ldr             x16, [x16, #0xb80]
    // 0xa4aa68: StoreField: r2->field_3f = r16
    //     0xa4aa68: stur            w16, [x2, #0x3f]
    // 0xa4aa6c: r16 = true
    //     0xa4aa6c: add             x16, NULL, #0x20  ; true
    // 0xa4aa70: StoreField: r2->field_43 = r16
    //     0xa4aa70: stur            w16, [x2, #0x43]
    // 0xa4aa74: r16 = "coming_from"
    //     0xa4aa74: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4aa78: ldr             x16, [x16, #0x328]
    // 0xa4aa7c: StoreField: r2->field_47 = r16
    //     0xa4aa7c: stur            w16, [x2, #0x47]
    // 0xa4aa80: LoadField: r0 = r3->field_2f
    //     0xa4aa80: ldur            w0, [x3, #0x2f]
    // 0xa4aa84: DecompressPointer r0
    //     0xa4aa84: add             x0, x0, HEAP, lsl #32
    // 0xa4aa88: mov             x1, x2
    // 0xa4aa8c: ArrayStore: r1[15] = r0  ; List_4
    //     0xa4aa8c: add             x25, x1, #0x4b
    //     0xa4aa90: str             w0, [x25]
    //     0xa4aa94: tbz             w0, #0, #0xa4aab0
    //     0xa4aa98: ldurb           w16, [x1, #-1]
    //     0xa4aa9c: ldurb           w17, [x0, #-1]
    //     0xa4aaa0: and             x16, x17, x16, lsr #2
    //     0xa4aaa4: tst             x16, HEAP, lsr #32
    //     0xa4aaa8: b.eq            #0xa4aab0
    //     0xa4aaac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4aab0: r16 = "checkout_id"
    //     0xa4aab0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xa4aab4: ldr             x16, [x16, #0xb88]
    // 0xa4aab8: StoreField: r2->field_4f = r16
    //     0xa4aab8: stur            w16, [x2, #0x4f]
    // 0xa4aabc: ldur            x0, [fp, #-0x10]
    // 0xa4aac0: LoadField: r1 = r0->field_f
    //     0xa4aac0: ldur            w1, [x0, #0xf]
    // 0xa4aac4: DecompressPointer r1
    //     0xa4aac4: add             x1, x1, HEAP, lsl #32
    // 0xa4aac8: r16 = Sentinel
    //     0xa4aac8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4aacc: cmp             w1, w16
    // 0xa4aad0: b.eq            #0xa4ae04
    // 0xa4aad4: str             x1, [SP]
    // 0xa4aad8: r4 = 0
    //     0xa4aad8: movz            x4, #0
    // 0xa4aadc: ldr             x0, [SP]
    // 0xa4aae0: r16 = UnlinkedCall_0x613b5c
    //     0xa4aae0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56450] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4aae4: add             x16, x16, #0x450
    // 0xa4aae8: ldp             x5, lr, [x16]
    // 0xa4aaec: blr             lr
    // 0xa4aaf0: ldur            x1, [fp, #-0x18]
    // 0xa4aaf4: ArrayStore: r1[17] = r0  ; List_4
    //     0xa4aaf4: add             x25, x1, #0x53
    //     0xa4aaf8: str             w0, [x25]
    //     0xa4aafc: tbz             w0, #0, #0xa4ab18
    //     0xa4ab00: ldurb           w16, [x1, #-1]
    //     0xa4ab04: ldurb           w17, [x0, #-1]
    //     0xa4ab08: and             x16, x17, x16, lsr #2
    //     0xa4ab0c: tst             x16, HEAP, lsr #32
    //     0xa4ab10: b.eq            #0xa4ab18
    //     0xa4ab14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ab18: ldur            x1, [fp, #-0x18]
    // 0xa4ab1c: r16 = "user_data"
    //     0xa4ab1c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xa4ab20: ldr             x16, [x16, #0x58]
    // 0xa4ab24: StoreField: r1->field_57 = r16
    //     0xa4ab24: stur            w16, [x1, #0x57]
    // 0xa4ab28: ldur            x0, [fp, #-0x10]
    // 0xa4ab2c: LoadField: r2 = r0->field_f
    //     0xa4ab2c: ldur            w2, [x0, #0xf]
    // 0xa4ab30: DecompressPointer r2
    //     0xa4ab30: add             x2, x2, HEAP, lsl #32
    // 0xa4ab34: r16 = Sentinel
    //     0xa4ab34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa4ab38: cmp             w2, w16
    // 0xa4ab3c: b.eq            #0xa4ae14
    // 0xa4ab40: str             x2, [SP]
    // 0xa4ab44: r4 = 0
    //     0xa4ab44: movz            x4, #0
    // 0xa4ab48: ldr             x0, [SP]
    // 0xa4ab4c: r16 = UnlinkedCall_0x613b5c
    //     0xa4ab4c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56460] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa4ab50: add             x16, x16, #0x460
    // 0xa4ab54: ldp             x5, lr, [x16]
    // 0xa4ab58: blr             lr
    // 0xa4ab5c: ldur            x1, [fp, #-0x18]
    // 0xa4ab60: ArrayStore: r1[19] = r0  ; List_4
    //     0xa4ab60: add             x25, x1, #0x5b
    //     0xa4ab64: str             w0, [x25]
    //     0xa4ab68: tbz             w0, #0, #0xa4ab84
    //     0xa4ab6c: ldurb           w16, [x1, #-1]
    //     0xa4ab70: ldurb           w17, [x0, #-1]
    //     0xa4ab74: and             x16, x17, x16, lsr #2
    //     0xa4ab78: tst             x16, HEAP, lsr #32
    //     0xa4ab7c: b.eq            #0xa4ab84
    //     0xa4ab80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ab84: r16 = <String, dynamic>
    //     0xa4ab84: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xa4ab88: ldur            lr, [fp, #-0x18]
    // 0xa4ab8c: stp             lr, x16, [SP]
    // 0xa4ab90: r0 = Map._fromLiteral()
    //     0xa4ab90: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4ab94: r16 = "/checkout_order_summary_page"
    //     0xa4ab94: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xa4ab98: ldr             x16, [x16, #0x9d8]
    // 0xa4ab9c: stp             x16, NULL, [SP, #8]
    // 0xa4aba0: str             x0, [SP]
    // 0xa4aba4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4aba4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4aba8: ldr             x4, [x4, #0x438]
    // 0xa4abac: r0 = GetNavigation.toNamed()
    //     0xa4abac: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4abb0: b               #0xa4adbc
    // 0xa4abb4: ldur            x0, [fp, #-0x10]
    // 0xa4abb8: LoadField: r2 = r0->field_b
    //     0xa4abb8: ldur            w2, [x0, #0xb]
    // 0xa4abbc: DecompressPointer r2
    //     0xa4abbc: add             x2, x2, HEAP, lsl #32
    // 0xa4abc0: stur            x2, [fp, #-0x18]
    // 0xa4abc4: LoadField: r0 = r2->field_f
    //     0xa4abc4: ldur            w0, [x2, #0xf]
    // 0xa4abc8: DecompressPointer r0
    //     0xa4abc8: add             x0, x0, HEAP, lsl #32
    // 0xa4abcc: LoadField: r1 = r0->field_b
    //     0xa4abcc: ldur            w1, [x0, #0xb]
    // 0xa4abd0: DecompressPointer r1
    //     0xa4abd0: add             x1, x1, HEAP, lsl #32
    // 0xa4abd4: cmp             w1, NULL
    // 0xa4abd8: b.eq            #0xa4ae38
    // 0xa4abdc: LoadField: r3 = r1->field_f
    //     0xa4abdc: ldur            w3, [x1, #0xf]
    // 0xa4abe0: DecompressPointer r3
    //     0xa4abe0: add             x3, x3, HEAP, lsl #32
    // 0xa4abe4: stur            x3, [fp, #-0x10]
    // 0xa4abe8: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xa4abe8: ldur            x4, [x1, #0x17]
    // 0xa4abec: stur            x4, [fp, #-0x20]
    // 0xa4abf0: LoadField: r0 = r1->field_27
    //     0xa4abf0: ldur            x0, [x1, #0x27]
    // 0xa4abf4: mul             x5, x0, x4
    // 0xa4abf8: r0 = BoxInt64Instr(r5)
    //     0xa4abf8: sbfiz           x0, x5, #1, #0x1f
    //     0xa4abfc: cmp             x5, x0, asr #1
    //     0xa4ac00: b.eq            #0xa4ac0c
    //     0xa4ac04: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4ac08: stur            x5, [x0, #7]
    // 0xa4ac0c: stp             x0, NULL, [SP]
    // 0xa4ac10: r0 = _Double.fromInteger()
    //     0xa4ac10: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xa4ac14: stur            x0, [fp, #-0x28]
    // 0xa4ac18: r0 = CheckoutEventData()
    //     0xa4ac18: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xa4ac1c: mov             x2, x0
    // 0xa4ac20: ldur            x0, [fp, #-0x28]
    // 0xa4ac24: stur            x2, [fp, #-0x30]
    // 0xa4ac28: StoreField: r2->field_7 = r0
    //     0xa4ac28: stur            w0, [x2, #7]
    // 0xa4ac2c: ldur            x3, [fp, #-0x20]
    // 0xa4ac30: r0 = BoxInt64Instr(r3)
    //     0xa4ac30: sbfiz           x0, x3, #1, #0x1f
    //     0xa4ac34: cmp             x3, x0, asr #1
    //     0xa4ac38: b.eq            #0xa4ac44
    //     0xa4ac3c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4ac40: stur            x3, [x0, #7]
    // 0xa4ac44: StoreField: r2->field_b = r0
    //     0xa4ac44: stur            w0, [x2, #0xb]
    // 0xa4ac48: ldur            x0, [fp, #-0x10]
    // 0xa4ac4c: StoreField: r2->field_f = r0
    //     0xa4ac4c: stur            w0, [x2, #0xf]
    // 0xa4ac50: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa4ac50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa4ac54: ldr             x0, [x0, #0x1c80]
    //     0xa4ac58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa4ac5c: cmp             w0, w16
    //     0xa4ac60: b.ne            #0xa4ac6c
    //     0xa4ac64: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa4ac68: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa4ac6c: r1 = Null
    //     0xa4ac6c: mov             x1, NULL
    // 0xa4ac70: r2 = 28
    //     0xa4ac70: movz            x2, #0x1c
    // 0xa4ac74: r0 = AllocateArray()
    //     0xa4ac74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa4ac78: mov             x2, x0
    // 0xa4ac7c: r16 = "previousScreenSource"
    //     0xa4ac7c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa4ac80: ldr             x16, [x16, #0x448]
    // 0xa4ac84: StoreField: r2->field_f = r16
    //     0xa4ac84: stur            w16, [x2, #0xf]
    // 0xa4ac88: r16 = "product_page"
    //     0xa4ac88: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xa4ac8c: ldr             x16, [x16, #0x480]
    // 0xa4ac90: StoreField: r2->field_13 = r16
    //     0xa4ac90: stur            w16, [x2, #0x13]
    // 0xa4ac94: r16 = "checkout_event_data"
    //     0xa4ac94: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xa4ac98: ldr             x16, [x16, #0xd50]
    // 0xa4ac9c: ArrayStore: r2[0] = r16  ; List_4
    //     0xa4ac9c: stur            w16, [x2, #0x17]
    // 0xa4aca0: ldur            x0, [fp, #-0x30]
    // 0xa4aca4: StoreField: r2->field_1b = r0
    //     0xa4aca4: stur            w0, [x2, #0x1b]
    // 0xa4aca8: r16 = "product_id"
    //     0xa4aca8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xa4acac: ldr             x16, [x16, #0x9b8]
    // 0xa4acb0: StoreField: r2->field_1f = r16
    //     0xa4acb0: stur            w16, [x2, #0x1f]
    // 0xa4acb4: ldur            x0, [fp, #-0x18]
    // 0xa4acb8: LoadField: r1 = r0->field_f
    //     0xa4acb8: ldur            w1, [x0, #0xf]
    // 0xa4acbc: DecompressPointer r1
    //     0xa4acbc: add             x1, x1, HEAP, lsl #32
    // 0xa4acc0: LoadField: r3 = r1->field_b
    //     0xa4acc0: ldur            w3, [x1, #0xb]
    // 0xa4acc4: DecompressPointer r3
    //     0xa4acc4: add             x3, x3, HEAP, lsl #32
    // 0xa4acc8: cmp             w3, NULL
    // 0xa4accc: b.eq            #0xa4ae3c
    // 0xa4acd0: LoadField: r0 = r3->field_f
    //     0xa4acd0: ldur            w0, [x3, #0xf]
    // 0xa4acd4: DecompressPointer r0
    //     0xa4acd4: add             x0, x0, HEAP, lsl #32
    // 0xa4acd8: StoreField: r2->field_23 = r0
    //     0xa4acd8: stur            w0, [x2, #0x23]
    // 0xa4acdc: r16 = "sku_id"
    //     0xa4acdc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xa4ace0: ldr             x16, [x16, #0x498]
    // 0xa4ace4: StoreField: r2->field_27 = r16
    //     0xa4ace4: stur            w16, [x2, #0x27]
    // 0xa4ace8: LoadField: r0 = r3->field_13
    //     0xa4ace8: ldur            w0, [x3, #0x13]
    // 0xa4acec: DecompressPointer r0
    //     0xa4acec: add             x0, x0, HEAP, lsl #32
    // 0xa4acf0: StoreField: r2->field_2b = r0
    //     0xa4acf0: stur            w0, [x2, #0x2b]
    // 0xa4acf4: r16 = "quantity"
    //     0xa4acf4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xa4acf8: ldr             x16, [x16, #0x428]
    // 0xa4acfc: StoreField: r2->field_2f = r16
    //     0xa4acfc: stur            w16, [x2, #0x2f]
    // 0xa4ad00: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xa4ad00: ldur            x4, [x3, #0x17]
    // 0xa4ad04: r0 = BoxInt64Instr(r4)
    //     0xa4ad04: sbfiz           x0, x4, #1, #0x1f
    //     0xa4ad08: cmp             x4, x0, asr #1
    //     0xa4ad0c: b.eq            #0xa4ad18
    //     0xa4ad10: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa4ad14: stur            x4, [x0, #7]
    // 0xa4ad18: mov             x1, x2
    // 0xa4ad1c: ArrayStore: r1[9] = r0  ; List_4
    //     0xa4ad1c: add             x25, x1, #0x33
    //     0xa4ad20: str             w0, [x25]
    //     0xa4ad24: tbz             w0, #0, #0xa4ad40
    //     0xa4ad28: ldurb           w16, [x1, #-1]
    //     0xa4ad2c: ldurb           w17, [x0, #-1]
    //     0xa4ad30: and             x16, x17, x16, lsr #2
    //     0xa4ad34: tst             x16, HEAP, lsr #32
    //     0xa4ad38: b.eq            #0xa4ad40
    //     0xa4ad3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ad40: r16 = "coming_from"
    //     0xa4ad40: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xa4ad44: ldr             x16, [x16, #0x328]
    // 0xa4ad48: StoreField: r2->field_37 = r16
    //     0xa4ad48: stur            w16, [x2, #0x37]
    // 0xa4ad4c: LoadField: r0 = r3->field_2f
    //     0xa4ad4c: ldur            w0, [x3, #0x2f]
    // 0xa4ad50: DecompressPointer r0
    //     0xa4ad50: add             x0, x0, HEAP, lsl #32
    // 0xa4ad54: mov             x1, x2
    // 0xa4ad58: ArrayStore: r1[11] = r0  ; List_4
    //     0xa4ad58: add             x25, x1, #0x3b
    //     0xa4ad5c: str             w0, [x25]
    //     0xa4ad60: tbz             w0, #0, #0xa4ad7c
    //     0xa4ad64: ldurb           w16, [x1, #-1]
    //     0xa4ad68: ldurb           w17, [x0, #-1]
    //     0xa4ad6c: and             x16, x17, x16, lsr #2
    //     0xa4ad70: tst             x16, HEAP, lsr #32
    //     0xa4ad74: b.eq            #0xa4ad7c
    //     0xa4ad78: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa4ad7c: r16 = "is_skipped_address"
    //     0xa4ad7c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xa4ad80: ldr             x16, [x16, #0xb80]
    // 0xa4ad84: StoreField: r2->field_3f = r16
    //     0xa4ad84: stur            w16, [x2, #0x3f]
    // 0xa4ad88: r16 = true
    //     0xa4ad88: add             x16, NULL, #0x20  ; true
    // 0xa4ad8c: StoreField: r2->field_43 = r16
    //     0xa4ad8c: stur            w16, [x2, #0x43]
    // 0xa4ad90: r16 = <String, Object?>
    //     0xa4ad90: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa4ad94: ldr             x16, [x16, #0xc28]
    // 0xa4ad98: stp             x2, x16, [SP]
    // 0xa4ad9c: r0 = Map._fromLiteral()
    //     0xa4ad9c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa4ada0: r16 = "/checkout_request_number_page"
    //     0xa4ada0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xa4ada4: ldr             x16, [x16, #0x9f8]
    // 0xa4ada8: stp             x16, NULL, [SP, #8]
    // 0xa4adac: str             x0, [SP]
    // 0xa4adb0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa4adb0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa4adb4: ldr             x4, [x4, #0x438]
    // 0xa4adb8: r0 = GetNavigation.toNamed()
    //     0xa4adb8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa4adbc: r0 = Null
    //     0xa4adbc: mov             x0, NULL
    // 0xa4adc0: r0 = ReturnAsyncNotFuture()
    //     0xa4adc0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xa4adc4: r16 = "controller"
    //     0xa4adc4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4adc8: str             x16, [SP]
    // 0xa4adcc: r0 = _throwLocalNotInitialized()
    //     0xa4adcc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4add0: brk             #0
    // 0xa4add4: r16 = "controller"
    //     0xa4add4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4add8: str             x16, [SP]
    // 0xa4addc: r0 = _throwLocalNotInitialized()
    //     0xa4addc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4ade0: brk             #0
    // 0xa4ade4: r16 = "controller"
    //     0xa4ade4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4ade8: str             x16, [SP]
    // 0xa4adec: r0 = _throwLocalNotInitialized()
    //     0xa4adec: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4adf0: brk             #0
    // 0xa4adf4: r16 = "controller"
    //     0xa4adf4: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4adf8: str             x16, [SP]
    // 0xa4adfc: r0 = _throwLocalNotInitialized()
    //     0xa4adfc: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4ae00: brk             #0
    // 0xa4ae04: r16 = "controller"
    //     0xa4ae04: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4ae08: str             x16, [SP]
    // 0xa4ae0c: r0 = _throwLocalNotInitialized()
    //     0xa4ae0c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4ae10: brk             #0
    // 0xa4ae14: r16 = "controller"
    //     0xa4ae14: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xa4ae18: str             x16, [SP]
    // 0xa4ae1c: r0 = _throwLocalNotInitialized()
    //     0xa4ae1c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xa4ae20: brk             #0
    // 0xa4ae24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ae24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ae28: b               #0xa4a4c0
    // 0xa4ae2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ae30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ae34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ae38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa4ae3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa4ae3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb61650, size: 0xbb8
    // 0xb61650: EnterFrame
    //     0xb61650: stp             fp, lr, [SP, #-0x10]!
    //     0xb61654: mov             fp, SP
    // 0xb61658: AllocStack(0x78)
    //     0xb61658: sub             SP, SP, #0x78
    // 0xb6165c: SetupParameters(_CustomizedBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb6165c: stur            x1, [fp, #-8]
    //     0xb61660: stur            x2, [fp, #-0x10]
    // 0xb61664: CheckStackOverflow
    //     0xb61664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb61668: cmp             SP, x16
    //     0xb6166c: b.ls            #0xb621f0
    // 0xb61670: r1 = 2
    //     0xb61670: movz            x1, #0x2
    // 0xb61674: r0 = AllocateContext()
    //     0xb61674: bl              #0x16f6108  ; AllocateContextStub
    // 0xb61678: mov             x1, x0
    // 0xb6167c: ldur            x0, [fp, #-8]
    // 0xb61680: stur            x1, [fp, #-0x18]
    // 0xb61684: StoreField: r1->field_f = r0
    //     0xb61684: stur            w0, [x1, #0xf]
    // 0xb61688: ldur            x2, [fp, #-0x10]
    // 0xb6168c: StoreField: r1->field_13 = r2
    //     0xb6168c: stur            w2, [x1, #0x13]
    // 0xb61690: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xb61690: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb61694: ldr             x0, [x0, #0x1ab0]
    //     0xb61698: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6169c: cmp             w0, w16
    //     0xb616a0: b.ne            #0xb616b0
    //     0xb616a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xb616a8: ldr             x2, [x2, #0x60]
    //     0xb616ac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb616b0: LoadField: r2 = r0->field_87
    //     0xb616b0: ldur            w2, [x0, #0x87]
    // 0xb616b4: DecompressPointer r2
    //     0xb616b4: add             x2, x2, HEAP, lsl #32
    // 0xb616b8: stur            x2, [fp, #-0x20]
    // 0xb616bc: LoadField: r0 = r2->field_7
    //     0xb616bc: ldur            w0, [x2, #7]
    // 0xb616c0: DecompressPointer r0
    //     0xb616c0: add             x0, x0, HEAP, lsl #32
    // 0xb616c4: stur            x0, [fp, #-0x10]
    // 0xb616c8: r16 = 20.000000
    //     0xb616c8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb616cc: ldr             x16, [x16, #0xac8]
    // 0xb616d0: r30 = Instance_Color
    //     0xb616d0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb616d4: stp             lr, x16, [SP]
    // 0xb616d8: mov             x1, x0
    // 0xb616dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb616dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb616e0: ldr             x4, [x4, #0xaa0]
    // 0xb616e4: r0 = copyWith()
    //     0xb616e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb616e8: stur            x0, [fp, #-0x28]
    // 0xb616ec: r0 = Text()
    //     0xb616ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb616f0: mov             x1, x0
    // 0xb616f4: r0 = "Customisation"
    //     0xb616f4: add             x0, PP, #0x56, lsl #12  ; [pp+0x56388] "Customisation"
    //     0xb616f8: ldr             x0, [x0, #0x388]
    // 0xb616fc: stur            x1, [fp, #-0x30]
    // 0xb61700: StoreField: r1->field_b = r0
    //     0xb61700: stur            w0, [x1, #0xb]
    // 0xb61704: ldur            x0, [fp, #-0x28]
    // 0xb61708: StoreField: r1->field_13 = r0
    //     0xb61708: stur            w0, [x1, #0x13]
    // 0xb6170c: r0 = InkWell()
    //     0xb6170c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb61710: mov             x3, x0
    // 0xb61714: r0 = Instance_Icon
    //     0xb61714: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb61718: ldr             x0, [x0, #0x2b8]
    // 0xb6171c: stur            x3, [fp, #-0x28]
    // 0xb61720: StoreField: r3->field_b = r0
    //     0xb61720: stur            w0, [x3, #0xb]
    // 0xb61724: r1 = Function '<anonymous closure>':.
    //     0xb61724: add             x1, PP, #0x56, lsl #12  ; [pp+0x56390] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb61728: ldr             x1, [x1, #0x390]
    // 0xb6172c: r2 = Null
    //     0xb6172c: mov             x2, NULL
    // 0xb61730: r0 = AllocateClosure()
    //     0xb61730: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb61734: mov             x1, x0
    // 0xb61738: ldur            x0, [fp, #-0x28]
    // 0xb6173c: StoreField: r0->field_f = r1
    //     0xb6173c: stur            w1, [x0, #0xf]
    // 0xb61740: r3 = true
    //     0xb61740: add             x3, NULL, #0x20  ; true
    // 0xb61744: StoreField: r0->field_43 = r3
    //     0xb61744: stur            w3, [x0, #0x43]
    // 0xb61748: r1 = Instance_BoxShape
    //     0xb61748: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6174c: ldr             x1, [x1, #0x80]
    // 0xb61750: StoreField: r0->field_47 = r1
    //     0xb61750: stur            w1, [x0, #0x47]
    // 0xb61754: StoreField: r0->field_6f = r3
    //     0xb61754: stur            w3, [x0, #0x6f]
    // 0xb61758: r4 = false
    //     0xb61758: add             x4, NULL, #0x30  ; false
    // 0xb6175c: StoreField: r0->field_73 = r4
    //     0xb6175c: stur            w4, [x0, #0x73]
    // 0xb61760: StoreField: r0->field_83 = r3
    //     0xb61760: stur            w3, [x0, #0x83]
    // 0xb61764: StoreField: r0->field_7b = r4
    //     0xb61764: stur            w4, [x0, #0x7b]
    // 0xb61768: r1 = Null
    //     0xb61768: mov             x1, NULL
    // 0xb6176c: r2 = 6
    //     0xb6176c: movz            x2, #0x6
    // 0xb61770: r0 = AllocateArray()
    //     0xb61770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb61774: mov             x2, x0
    // 0xb61778: ldur            x0, [fp, #-0x30]
    // 0xb6177c: stur            x2, [fp, #-0x38]
    // 0xb61780: StoreField: r2->field_f = r0
    //     0xb61780: stur            w0, [x2, #0xf]
    // 0xb61784: r16 = Instance_Spacer
    //     0xb61784: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb61788: ldr             x16, [x16, #0xf0]
    // 0xb6178c: StoreField: r2->field_13 = r16
    //     0xb6178c: stur            w16, [x2, #0x13]
    // 0xb61790: ldur            x0, [fp, #-0x28]
    // 0xb61794: ArrayStore: r2[0] = r0  ; List_4
    //     0xb61794: stur            w0, [x2, #0x17]
    // 0xb61798: r1 = <Widget>
    //     0xb61798: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6179c: r0 = AllocateGrowableArray()
    //     0xb6179c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb617a0: mov             x1, x0
    // 0xb617a4: ldur            x0, [fp, #-0x38]
    // 0xb617a8: stur            x1, [fp, #-0x28]
    // 0xb617ac: StoreField: r1->field_f = r0
    //     0xb617ac: stur            w0, [x1, #0xf]
    // 0xb617b0: r2 = 6
    //     0xb617b0: movz            x2, #0x6
    // 0xb617b4: StoreField: r1->field_b = r2
    //     0xb617b4: stur            w2, [x1, #0xb]
    // 0xb617b8: r0 = Row()
    //     0xb617b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb617bc: mov             x2, x0
    // 0xb617c0: r0 = Instance_Axis
    //     0xb617c0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb617c4: stur            x2, [fp, #-0x30]
    // 0xb617c8: StoreField: r2->field_f = r0
    //     0xb617c8: stur            w0, [x2, #0xf]
    // 0xb617cc: r3 = Instance_MainAxisAlignment
    //     0xb617cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb617d0: ldr             x3, [x3, #0xa08]
    // 0xb617d4: StoreField: r2->field_13 = r3
    //     0xb617d4: stur            w3, [x2, #0x13]
    // 0xb617d8: r4 = Instance_MainAxisSize
    //     0xb617d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb617dc: ldr             x4, [x4, #0xa10]
    // 0xb617e0: ArrayStore: r2[0] = r4  ; List_4
    //     0xb617e0: stur            w4, [x2, #0x17]
    // 0xb617e4: r5 = Instance_CrossAxisAlignment
    //     0xb617e4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb617e8: ldr             x5, [x5, #0xa18]
    // 0xb617ec: StoreField: r2->field_1b = r5
    //     0xb617ec: stur            w5, [x2, #0x1b]
    // 0xb617f0: r6 = Instance_VerticalDirection
    //     0xb617f0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb617f4: ldr             x6, [x6, #0xa20]
    // 0xb617f8: StoreField: r2->field_23 = r6
    //     0xb617f8: stur            w6, [x2, #0x23]
    // 0xb617fc: r7 = Instance_Clip
    //     0xb617fc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb61800: ldr             x7, [x7, #0x38]
    // 0xb61804: StoreField: r2->field_2b = r7
    //     0xb61804: stur            w7, [x2, #0x2b]
    // 0xb61808: StoreField: r2->field_2f = rZR
    //     0xb61808: stur            xzr, [x2, #0x2f]
    // 0xb6180c: ldur            x1, [fp, #-0x28]
    // 0xb61810: StoreField: r2->field_b = r1
    //     0xb61810: stur            w1, [x2, #0xb]
    // 0xb61814: ldur            x8, [fp, #-8]
    // 0xb61818: LoadField: r1 = r8->field_b
    //     0xb61818: ldur            w1, [x8, #0xb]
    // 0xb6181c: DecompressPointer r1
    //     0xb6181c: add             x1, x1, HEAP, lsl #32
    // 0xb61820: cmp             w1, NULL
    // 0xb61824: b.eq            #0xb621f8
    // 0xb61828: LoadField: r9 = r1->field_b
    //     0xb61828: ldur            w9, [x1, #0xb]
    // 0xb6182c: DecompressPointer r9
    //     0xb6182c: add             x9, x9, HEAP, lsl #32
    // 0xb61830: LoadField: r1 = r9->field_b
    //     0xb61830: ldur            w1, [x9, #0xb]
    // 0xb61834: DecompressPointer r1
    //     0xb61834: add             x1, x1, HEAP, lsl #32
    // 0xb61838: cmp             w1, NULL
    // 0xb6183c: b.ne            #0xb61848
    // 0xb61840: r1 = Null
    //     0xb61840: mov             x1, NULL
    // 0xb61844: b               #0xb61854
    // 0xb61848: LoadField: r9 = r1->field_23
    //     0xb61848: ldur            w9, [x1, #0x23]
    // 0xb6184c: DecompressPointer r9
    //     0xb6184c: add             x9, x9, HEAP, lsl #32
    // 0xb61850: mov             x1, x9
    // 0xb61854: cmp             w1, NULL
    // 0xb61858: b.ne            #0xb61864
    // 0xb6185c: r10 = ""
    //     0xb6185c: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb61860: b               #0xb61868
    // 0xb61864: mov             x10, x1
    // 0xb61868: ldur            x9, [fp, #-0x20]
    // 0xb6186c: stur            x10, [fp, #-0x28]
    // 0xb61870: LoadField: r1 = r9->field_27
    //     0xb61870: ldur            w1, [x9, #0x27]
    // 0xb61874: DecompressPointer r1
    //     0xb61874: add             x1, x1, HEAP, lsl #32
    // 0xb61878: r16 = 16.000000
    //     0xb61878: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6187c: ldr             x16, [x16, #0x188]
    // 0xb61880: r30 = Instance_Color
    //     0xb61880: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb61884: stp             lr, x16, [SP]
    // 0xb61888: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb61888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6188c: ldr             x4, [x4, #0xaa0]
    // 0xb61890: r0 = copyWith()
    //     0xb61890: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb61894: stur            x0, [fp, #-0x38]
    // 0xb61898: r0 = Text()
    //     0xb61898: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6189c: mov             x1, x0
    // 0xb618a0: ldur            x0, [fp, #-0x28]
    // 0xb618a4: stur            x1, [fp, #-0x40]
    // 0xb618a8: StoreField: r1->field_b = r0
    //     0xb618a8: stur            w0, [x1, #0xb]
    // 0xb618ac: ldur            x0, [fp, #-0x38]
    // 0xb618b0: StoreField: r1->field_13 = r0
    //     0xb618b0: stur            w0, [x1, #0x13]
    // 0xb618b4: r0 = 4
    //     0xb618b4: movz            x0, #0x4
    // 0xb618b8: StoreField: r1->field_37 = r0
    //     0xb618b8: stur            w0, [x1, #0x37]
    // 0xb618bc: r0 = Padding()
    //     0xb618bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb618c0: mov             x2, x0
    // 0xb618c4: r0 = Instance_EdgeInsets
    //     0xb618c4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb618c8: ldr             x0, [x0, #0x668]
    // 0xb618cc: stur            x2, [fp, #-0x48]
    // 0xb618d0: StoreField: r2->field_f = r0
    //     0xb618d0: stur            w0, [x2, #0xf]
    // 0xb618d4: ldur            x1, [fp, #-0x40]
    // 0xb618d8: StoreField: r2->field_b = r1
    //     0xb618d8: stur            w1, [x2, #0xb]
    // 0xb618dc: ldur            x3, [fp, #-8]
    // 0xb618e0: LoadField: r1 = r3->field_b
    //     0xb618e0: ldur            w1, [x3, #0xb]
    // 0xb618e4: DecompressPointer r1
    //     0xb618e4: add             x1, x1, HEAP, lsl #32
    // 0xb618e8: cmp             w1, NULL
    // 0xb618ec: b.eq            #0xb621fc
    // 0xb618f0: LoadField: r4 = r1->field_b
    //     0xb618f0: ldur            w4, [x1, #0xb]
    // 0xb618f4: DecompressPointer r4
    //     0xb618f4: add             x4, x4, HEAP, lsl #32
    // 0xb618f8: LoadField: r1 = r4->field_b
    //     0xb618f8: ldur            w1, [x4, #0xb]
    // 0xb618fc: DecompressPointer r1
    //     0xb618fc: add             x1, x1, HEAP, lsl #32
    // 0xb61900: cmp             w1, NULL
    // 0xb61904: b.ne            #0xb61910
    // 0xb61908: r1 = Null
    //     0xb61908: mov             x1, NULL
    // 0xb6190c: b               #0xb6191c
    // 0xb61910: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xb61910: ldur            w4, [x1, #0x17]
    // 0xb61914: DecompressPointer r4
    //     0xb61914: add             x4, x4, HEAP, lsl #32
    // 0xb61918: mov             x1, x4
    // 0xb6191c: cmp             w1, NULL
    // 0xb61920: b.ne            #0xb6192c
    // 0xb61924: r4 = ""
    //     0xb61924: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb61928: b               #0xb61930
    // 0xb6192c: mov             x4, x1
    // 0xb61930: ldur            x1, [fp, #-0x20]
    // 0xb61934: stur            x4, [fp, #-0x38]
    // 0xb61938: LoadField: r5 = r1->field_2b
    //     0xb61938: ldur            w5, [x1, #0x2b]
    // 0xb6193c: DecompressPointer r5
    //     0xb6193c: add             x5, x5, HEAP, lsl #32
    // 0xb61940: stur            x5, [fp, #-0x28]
    // 0xb61944: r1 = Instance_Color
    //     0xb61944: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb61948: d0 = 0.700000
    //     0xb61948: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6194c: ldr             d0, [x17, #0xf48]
    // 0xb61950: r0 = withOpacity()
    //     0xb61950: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb61954: r16 = 12.000000
    //     0xb61954: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb61958: ldr             x16, [x16, #0x9e8]
    // 0xb6195c: stp             x0, x16, [SP]
    // 0xb61960: ldur            x1, [fp, #-0x28]
    // 0xb61964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb61964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb61968: ldr             x4, [x4, #0xaa0]
    // 0xb6196c: r0 = copyWith()
    //     0xb6196c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb61970: stur            x0, [fp, #-0x20]
    // 0xb61974: r0 = Text()
    //     0xb61974: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb61978: mov             x1, x0
    // 0xb6197c: ldur            x0, [fp, #-0x38]
    // 0xb61980: stur            x1, [fp, #-0x28]
    // 0xb61984: StoreField: r1->field_b = r0
    //     0xb61984: stur            w0, [x1, #0xb]
    // 0xb61988: ldur            x0, [fp, #-0x20]
    // 0xb6198c: StoreField: r1->field_13 = r0
    //     0xb6198c: stur            w0, [x1, #0x13]
    // 0xb61990: r0 = 4
    //     0xb61990: movz            x0, #0x4
    // 0xb61994: StoreField: r1->field_37 = r0
    //     0xb61994: stur            w0, [x1, #0x37]
    // 0xb61998: r0 = Padding()
    //     0xb61998: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6199c: mov             x1, x0
    // 0xb619a0: r0 = Instance_EdgeInsets
    //     0xb619a0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb619a4: ldr             x0, [x0, #0x668]
    // 0xb619a8: stur            x1, [fp, #-0x20]
    // 0xb619ac: StoreField: r1->field_f = r0
    //     0xb619ac: stur            w0, [x1, #0xf]
    // 0xb619b0: ldur            x0, [fp, #-0x28]
    // 0xb619b4: StoreField: r1->field_b = r0
    //     0xb619b4: stur            w0, [x1, #0xb]
    // 0xb619b8: r16 = <EdgeInsets>
    //     0xb619b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb619bc: ldr             x16, [x16, #0xda0]
    // 0xb619c0: r30 = Instance_EdgeInsets
    //     0xb619c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb619c4: ldr             lr, [lr, #0x1f0]
    // 0xb619c8: stp             lr, x16, [SP]
    // 0xb619cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb619cc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb619d0: r0 = all()
    //     0xb619d0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb619d4: mov             x1, x0
    // 0xb619d8: ldur            x0, [fp, #-8]
    // 0xb619dc: stur            x1, [fp, #-0x28]
    // 0xb619e0: LoadField: r2 = r0->field_b
    //     0xb619e0: ldur            w2, [x0, #0xb]
    // 0xb619e4: DecompressPointer r2
    //     0xb619e4: add             x2, x2, HEAP, lsl #32
    // 0xb619e8: cmp             w2, NULL
    // 0xb619ec: b.eq            #0xb62200
    // 0xb619f0: LoadField: r3 = r2->field_33
    //     0xb619f0: ldur            w3, [x2, #0x33]
    // 0xb619f4: DecompressPointer r3
    //     0xb619f4: add             x3, x3, HEAP, lsl #32
    // 0xb619f8: LoadField: r2 = r3->field_3f
    //     0xb619f8: ldur            w2, [x3, #0x3f]
    // 0xb619fc: DecompressPointer r2
    //     0xb619fc: add             x2, x2, HEAP, lsl #32
    // 0xb61a00: cmp             w2, NULL
    // 0xb61a04: b.ne            #0xb61a10
    // 0xb61a08: r3 = Null
    //     0xb61a08: mov             x3, NULL
    // 0xb61a0c: b               #0xb61a34
    // 0xb61a10: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb61a10: ldur            w3, [x2, #0x17]
    // 0xb61a14: DecompressPointer r3
    //     0xb61a14: add             x3, x3, HEAP, lsl #32
    // 0xb61a18: cmp             w3, NULL
    // 0xb61a1c: b.ne            #0xb61a28
    // 0xb61a20: r3 = Null
    //     0xb61a20: mov             x3, NULL
    // 0xb61a24: b               #0xb61a34
    // 0xb61a28: LoadField: r4 = r3->field_7
    //     0xb61a28: ldur            w4, [x3, #7]
    // 0xb61a2c: DecompressPointer r4
    //     0xb61a2c: add             x4, x4, HEAP, lsl #32
    // 0xb61a30: mov             x3, x4
    // 0xb61a34: cmp             w3, NULL
    // 0xb61a38: b.ne            #0xb61a44
    // 0xb61a3c: r3 = 0
    //     0xb61a3c: movz            x3, #0
    // 0xb61a40: b               #0xb61a54
    // 0xb61a44: r4 = LoadInt32Instr(r3)
    //     0xb61a44: sbfx            x4, x3, #1, #0x1f
    //     0xb61a48: tbz             w3, #0, #0xb61a50
    //     0xb61a4c: ldur            x4, [x3, #7]
    // 0xb61a50: mov             x3, x4
    // 0xb61a54: stur            x3, [fp, #-0x60]
    // 0xb61a58: cmp             w2, NULL
    // 0xb61a5c: b.ne            #0xb61a68
    // 0xb61a60: r4 = Null
    //     0xb61a60: mov             x4, NULL
    // 0xb61a64: b               #0xb61a8c
    // 0xb61a68: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb61a68: ldur            w4, [x2, #0x17]
    // 0xb61a6c: DecompressPointer r4
    //     0xb61a6c: add             x4, x4, HEAP, lsl #32
    // 0xb61a70: cmp             w4, NULL
    // 0xb61a74: b.ne            #0xb61a80
    // 0xb61a78: r4 = Null
    //     0xb61a78: mov             x4, NULL
    // 0xb61a7c: b               #0xb61a8c
    // 0xb61a80: LoadField: r5 = r4->field_b
    //     0xb61a80: ldur            w5, [x4, #0xb]
    // 0xb61a84: DecompressPointer r5
    //     0xb61a84: add             x5, x5, HEAP, lsl #32
    // 0xb61a88: mov             x4, x5
    // 0xb61a8c: cmp             w4, NULL
    // 0xb61a90: b.ne            #0xb61a9c
    // 0xb61a94: r4 = 0
    //     0xb61a94: movz            x4, #0
    // 0xb61a98: b               #0xb61aac
    // 0xb61a9c: r5 = LoadInt32Instr(r4)
    //     0xb61a9c: sbfx            x5, x4, #1, #0x1f
    //     0xb61aa0: tbz             w4, #0, #0xb61aa8
    //     0xb61aa4: ldur            x5, [x4, #7]
    // 0xb61aa8: mov             x4, x5
    // 0xb61aac: stur            x4, [fp, #-0x58]
    // 0xb61ab0: cmp             w2, NULL
    // 0xb61ab4: b.ne            #0xb61ac0
    // 0xb61ab8: r2 = Null
    //     0xb61ab8: mov             x2, NULL
    // 0xb61abc: b               #0xb61ae0
    // 0xb61ac0: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb61ac0: ldur            w5, [x2, #0x17]
    // 0xb61ac4: DecompressPointer r5
    //     0xb61ac4: add             x5, x5, HEAP, lsl #32
    // 0xb61ac8: cmp             w5, NULL
    // 0xb61acc: b.ne            #0xb61ad8
    // 0xb61ad0: r2 = Null
    //     0xb61ad0: mov             x2, NULL
    // 0xb61ad4: b               #0xb61ae0
    // 0xb61ad8: LoadField: r2 = r5->field_f
    //     0xb61ad8: ldur            w2, [x5, #0xf]
    // 0xb61adc: DecompressPointer r2
    //     0xb61adc: add             x2, x2, HEAP, lsl #32
    // 0xb61ae0: cmp             w2, NULL
    // 0xb61ae4: b.ne            #0xb61af0
    // 0xb61ae8: r2 = 0
    //     0xb61ae8: movz            x2, #0
    // 0xb61aec: b               #0xb61b00
    // 0xb61af0: r5 = LoadInt32Instr(r2)
    //     0xb61af0: sbfx            x5, x2, #1, #0x1f
    //     0xb61af4: tbz             w2, #0, #0xb61afc
    //     0xb61af8: ldur            x5, [x2, #7]
    // 0xb61afc: mov             x2, x5
    // 0xb61b00: stur            x2, [fp, #-0x50]
    // 0xb61b04: r0 = Color()
    //     0xb61b04: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb61b08: mov             x1, x0
    // 0xb61b0c: r0 = Instance_ColorSpace
    //     0xb61b0c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb61b10: stur            x1, [fp, #-0x38]
    // 0xb61b14: StoreField: r1->field_27 = r0
    //     0xb61b14: stur            w0, [x1, #0x27]
    // 0xb61b18: d0 = 1.000000
    //     0xb61b18: fmov            d0, #1.00000000
    // 0xb61b1c: StoreField: r1->field_7 = d0
    //     0xb61b1c: stur            d0, [x1, #7]
    // 0xb61b20: ldur            x2, [fp, #-0x60]
    // 0xb61b24: ubfx            x2, x2, #0, #0x20
    // 0xb61b28: and             w3, w2, #0xff
    // 0xb61b2c: ubfx            x3, x3, #0, #0x20
    // 0xb61b30: scvtf           d1, x3
    // 0xb61b34: d2 = 255.000000
    //     0xb61b34: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb61b38: fdiv            d3, d1, d2
    // 0xb61b3c: StoreField: r1->field_f = d3
    //     0xb61b3c: stur            d3, [x1, #0xf]
    // 0xb61b40: ldur            x2, [fp, #-0x58]
    // 0xb61b44: ubfx            x2, x2, #0, #0x20
    // 0xb61b48: and             w3, w2, #0xff
    // 0xb61b4c: ubfx            x3, x3, #0, #0x20
    // 0xb61b50: scvtf           d1, x3
    // 0xb61b54: fdiv            d3, d1, d2
    // 0xb61b58: ArrayStore: r1[0] = d3  ; List_8
    //     0xb61b58: stur            d3, [x1, #0x17]
    // 0xb61b5c: ldur            x2, [fp, #-0x50]
    // 0xb61b60: ubfx            x2, x2, #0, #0x20
    // 0xb61b64: and             w3, w2, #0xff
    // 0xb61b68: ubfx            x3, x3, #0, #0x20
    // 0xb61b6c: scvtf           d1, x3
    // 0xb61b70: fdiv            d3, d1, d2
    // 0xb61b74: StoreField: r1->field_1f = d3
    //     0xb61b74: stur            d3, [x1, #0x1f]
    // 0xb61b78: r0 = BorderSide()
    //     0xb61b78: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb61b7c: mov             x1, x0
    // 0xb61b80: ldur            x0, [fp, #-0x38]
    // 0xb61b84: stur            x1, [fp, #-0x40]
    // 0xb61b88: StoreField: r1->field_7 = r0
    //     0xb61b88: stur            w0, [x1, #7]
    // 0xb61b8c: d0 = 1.000000
    //     0xb61b8c: fmov            d0, #1.00000000
    // 0xb61b90: StoreField: r1->field_b = d0
    //     0xb61b90: stur            d0, [x1, #0xb]
    // 0xb61b94: r0 = Instance_BorderStyle
    //     0xb61b94: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb61b98: ldr             x0, [x0, #0xf68]
    // 0xb61b9c: StoreField: r1->field_13 = r0
    //     0xb61b9c: stur            w0, [x1, #0x13]
    // 0xb61ba0: d1 = -1.000000
    //     0xb61ba0: fmov            d1, #-1.00000000
    // 0xb61ba4: ArrayStore: r1[0] = d1  ; List_8
    //     0xb61ba4: stur            d1, [x1, #0x17]
    // 0xb61ba8: r0 = Radius()
    //     0xb61ba8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb61bac: d0 = 35.000000
    //     0xb61bac: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3eec8] IMM: double(35) from 0x4041800000000000
    //     0xb61bb0: ldr             d0, [x17, #0xec8]
    // 0xb61bb4: stur            x0, [fp, #-0x38]
    // 0xb61bb8: StoreField: r0->field_7 = d0
    //     0xb61bb8: stur            d0, [x0, #7]
    // 0xb61bbc: StoreField: r0->field_f = d0
    //     0xb61bbc: stur            d0, [x0, #0xf]
    // 0xb61bc0: r0 = BorderRadius()
    //     0xb61bc0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb61bc4: mov             x1, x0
    // 0xb61bc8: ldur            x0, [fp, #-0x38]
    // 0xb61bcc: stur            x1, [fp, #-0x68]
    // 0xb61bd0: StoreField: r1->field_7 = r0
    //     0xb61bd0: stur            w0, [x1, #7]
    // 0xb61bd4: StoreField: r1->field_b = r0
    //     0xb61bd4: stur            w0, [x1, #0xb]
    // 0xb61bd8: StoreField: r1->field_f = r0
    //     0xb61bd8: stur            w0, [x1, #0xf]
    // 0xb61bdc: StoreField: r1->field_13 = r0
    //     0xb61bdc: stur            w0, [x1, #0x13]
    // 0xb61be0: r0 = RoundedRectangleBorder()
    //     0xb61be0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb61be4: mov             x1, x0
    // 0xb61be8: ldur            x0, [fp, #-0x68]
    // 0xb61bec: StoreField: r1->field_b = r0
    //     0xb61bec: stur            w0, [x1, #0xb]
    // 0xb61bf0: ldur            x0, [fp, #-0x40]
    // 0xb61bf4: StoreField: r1->field_7 = r0
    //     0xb61bf4: stur            w0, [x1, #7]
    // 0xb61bf8: r16 = <RoundedRectangleBorder>
    //     0xb61bf8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb61bfc: ldr             x16, [x16, #0xf78]
    // 0xb61c00: stp             x1, x16, [SP]
    // 0xb61c04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb61c04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61c08: r0 = all()
    //     0xb61c08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb61c0c: stur            x0, [fp, #-0x38]
    // 0xb61c10: r0 = ButtonStyle()
    //     0xb61c10: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb61c14: mov             x1, x0
    // 0xb61c18: ldur            x0, [fp, #-0x28]
    // 0xb61c1c: stur            x1, [fp, #-0x40]
    // 0xb61c20: StoreField: r1->field_23 = r0
    //     0xb61c20: stur            w0, [x1, #0x23]
    // 0xb61c24: ldur            x0, [fp, #-0x38]
    // 0xb61c28: StoreField: r1->field_43 = r0
    //     0xb61c28: stur            w0, [x1, #0x43]
    // 0xb61c2c: r0 = TextButtonThemeData()
    //     0xb61c2c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb61c30: mov             x2, x0
    // 0xb61c34: ldur            x0, [fp, #-0x40]
    // 0xb61c38: stur            x2, [fp, #-0x28]
    // 0xb61c3c: StoreField: r2->field_7 = r0
    //     0xb61c3c: stur            w0, [x2, #7]
    // 0xb61c40: r16 = 16.000000
    //     0xb61c40: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb61c44: ldr             x16, [x16, #0x188]
    // 0xb61c48: str             x16, [SP]
    // 0xb61c4c: ldur            x1, [fp, #-0x10]
    // 0xb61c50: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb61c50: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb61c54: ldr             x4, [x4, #0x798]
    // 0xb61c58: r0 = copyWith()
    //     0xb61c58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb61c5c: stur            x0, [fp, #-0x38]
    // 0xb61c60: r0 = Text()
    //     0xb61c60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb61c64: mov             x3, x0
    // 0xb61c68: r0 = "No"
    //     0xb61c68: add             x0, PP, #0x56, lsl #12  ; [pp+0x56398] "No"
    //     0xb61c6c: ldr             x0, [x0, #0x398]
    // 0xb61c70: stur            x3, [fp, #-0x40]
    // 0xb61c74: StoreField: r3->field_b = r0
    //     0xb61c74: stur            w0, [x3, #0xb]
    // 0xb61c78: ldur            x0, [fp, #-0x38]
    // 0xb61c7c: StoreField: r3->field_13 = r0
    //     0xb61c7c: stur            w0, [x3, #0x13]
    // 0xb61c80: ldur            x2, [fp, #-0x18]
    // 0xb61c84: r1 = Function '<anonymous closure>':.
    //     0xb61c84: add             x1, PP, #0x56, lsl #12  ; [pp+0x563a0] AnonymousClosure: (0xa49d28), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xb61c88: ldr             x1, [x1, #0x3a0]
    // 0xb61c8c: r0 = AllocateClosure()
    //     0xb61c8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb61c90: stur            x0, [fp, #-0x38]
    // 0xb61c94: r0 = TextButton()
    //     0xb61c94: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb61c98: mov             x1, x0
    // 0xb61c9c: ldur            x0, [fp, #-0x38]
    // 0xb61ca0: stur            x1, [fp, #-0x68]
    // 0xb61ca4: StoreField: r1->field_b = r0
    //     0xb61ca4: stur            w0, [x1, #0xb]
    // 0xb61ca8: r0 = false
    //     0xb61ca8: add             x0, NULL, #0x30  ; false
    // 0xb61cac: StoreField: r1->field_27 = r0
    //     0xb61cac: stur            w0, [x1, #0x27]
    // 0xb61cb0: r2 = true
    //     0xb61cb0: add             x2, NULL, #0x20  ; true
    // 0xb61cb4: StoreField: r1->field_2f = r2
    //     0xb61cb4: stur            w2, [x1, #0x2f]
    // 0xb61cb8: ldur            x3, [fp, #-0x40]
    // 0xb61cbc: StoreField: r1->field_37 = r3
    //     0xb61cbc: stur            w3, [x1, #0x37]
    // 0xb61cc0: r0 = TextButtonTheme()
    //     0xb61cc0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb61cc4: mov             x2, x0
    // 0xb61cc8: ldur            x0, [fp, #-0x28]
    // 0xb61ccc: stur            x2, [fp, #-0x38]
    // 0xb61cd0: StoreField: r2->field_f = r0
    //     0xb61cd0: stur            w0, [x2, #0xf]
    // 0xb61cd4: ldur            x0, [fp, #-0x68]
    // 0xb61cd8: StoreField: r2->field_b = r0
    //     0xb61cd8: stur            w0, [x2, #0xb]
    // 0xb61cdc: r1 = <FlexParentData>
    //     0xb61cdc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb61ce0: ldr             x1, [x1, #0xe00]
    // 0xb61ce4: r0 = Expanded()
    //     0xb61ce4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb61ce8: mov             x1, x0
    // 0xb61cec: r0 = 1
    //     0xb61cec: movz            x0, #0x1
    // 0xb61cf0: stur            x1, [fp, #-0x28]
    // 0xb61cf4: StoreField: r1->field_13 = r0
    //     0xb61cf4: stur            x0, [x1, #0x13]
    // 0xb61cf8: r2 = Instance_FlexFit
    //     0xb61cf8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb61cfc: ldr             x2, [x2, #0xe08]
    // 0xb61d00: StoreField: r1->field_1b = r2
    //     0xb61d00: stur            w2, [x1, #0x1b]
    // 0xb61d04: ldur            x3, [fp, #-0x38]
    // 0xb61d08: StoreField: r1->field_b = r3
    //     0xb61d08: stur            w3, [x1, #0xb]
    // 0xb61d0c: r16 = <EdgeInsets>
    //     0xb61d0c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb61d10: ldr             x16, [x16, #0xda0]
    // 0xb61d14: r30 = Instance_EdgeInsets
    //     0xb61d14: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb61d18: ldr             lr, [lr, #0x1f0]
    // 0xb61d1c: stp             lr, x16, [SP]
    // 0xb61d20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb61d20: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61d24: r0 = all()
    //     0xb61d24: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb61d28: mov             x1, x0
    // 0xb61d2c: ldur            x0, [fp, #-8]
    // 0xb61d30: stur            x1, [fp, #-0x38]
    // 0xb61d34: LoadField: r2 = r0->field_b
    //     0xb61d34: ldur            w2, [x0, #0xb]
    // 0xb61d38: DecompressPointer r2
    //     0xb61d38: add             x2, x2, HEAP, lsl #32
    // 0xb61d3c: cmp             w2, NULL
    // 0xb61d40: b.eq            #0xb62204
    // 0xb61d44: LoadField: r0 = r2->field_33
    //     0xb61d44: ldur            w0, [x2, #0x33]
    // 0xb61d48: DecompressPointer r0
    //     0xb61d48: add             x0, x0, HEAP, lsl #32
    // 0xb61d4c: LoadField: r2 = r0->field_3f
    //     0xb61d4c: ldur            w2, [x0, #0x3f]
    // 0xb61d50: DecompressPointer r2
    //     0xb61d50: add             x2, x2, HEAP, lsl #32
    // 0xb61d54: cmp             w2, NULL
    // 0xb61d58: b.ne            #0xb61d64
    // 0xb61d5c: r0 = Null
    //     0xb61d5c: mov             x0, NULL
    // 0xb61d60: b               #0xb61d88
    // 0xb61d64: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb61d64: ldur            w0, [x2, #0x17]
    // 0xb61d68: DecompressPointer r0
    //     0xb61d68: add             x0, x0, HEAP, lsl #32
    // 0xb61d6c: cmp             w0, NULL
    // 0xb61d70: b.ne            #0xb61d7c
    // 0xb61d74: r0 = Null
    //     0xb61d74: mov             x0, NULL
    // 0xb61d78: b               #0xb61d88
    // 0xb61d7c: LoadField: r3 = r0->field_7
    //     0xb61d7c: ldur            w3, [x0, #7]
    // 0xb61d80: DecompressPointer r3
    //     0xb61d80: add             x3, x3, HEAP, lsl #32
    // 0xb61d84: mov             x0, x3
    // 0xb61d88: cmp             w0, NULL
    // 0xb61d8c: b.ne            #0xb61d98
    // 0xb61d90: r0 = 0
    //     0xb61d90: movz            x0, #0
    // 0xb61d94: b               #0xb61da8
    // 0xb61d98: r3 = LoadInt32Instr(r0)
    //     0xb61d98: sbfx            x3, x0, #1, #0x1f
    //     0xb61d9c: tbz             w0, #0, #0xb61da4
    //     0xb61da0: ldur            x3, [x0, #7]
    // 0xb61da4: mov             x0, x3
    // 0xb61da8: stur            x0, [fp, #-0x60]
    // 0xb61dac: cmp             w2, NULL
    // 0xb61db0: b.ne            #0xb61dbc
    // 0xb61db4: r3 = Null
    //     0xb61db4: mov             x3, NULL
    // 0xb61db8: b               #0xb61de0
    // 0xb61dbc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb61dbc: ldur            w3, [x2, #0x17]
    // 0xb61dc0: DecompressPointer r3
    //     0xb61dc0: add             x3, x3, HEAP, lsl #32
    // 0xb61dc4: cmp             w3, NULL
    // 0xb61dc8: b.ne            #0xb61dd4
    // 0xb61dcc: r3 = Null
    //     0xb61dcc: mov             x3, NULL
    // 0xb61dd0: b               #0xb61de0
    // 0xb61dd4: LoadField: r4 = r3->field_b
    //     0xb61dd4: ldur            w4, [x3, #0xb]
    // 0xb61dd8: DecompressPointer r4
    //     0xb61dd8: add             x4, x4, HEAP, lsl #32
    // 0xb61ddc: mov             x3, x4
    // 0xb61de0: cmp             w3, NULL
    // 0xb61de4: b.ne            #0xb61df0
    // 0xb61de8: r3 = 0
    //     0xb61de8: movz            x3, #0
    // 0xb61dec: b               #0xb61e00
    // 0xb61df0: r4 = LoadInt32Instr(r3)
    //     0xb61df0: sbfx            x4, x3, #1, #0x1f
    //     0xb61df4: tbz             w3, #0, #0xb61dfc
    //     0xb61df8: ldur            x4, [x3, #7]
    // 0xb61dfc: mov             x3, x4
    // 0xb61e00: stur            x3, [fp, #-0x58]
    // 0xb61e04: cmp             w2, NULL
    // 0xb61e08: b.ne            #0xb61e14
    // 0xb61e0c: r2 = Null
    //     0xb61e0c: mov             x2, NULL
    // 0xb61e10: b               #0xb61e34
    // 0xb61e14: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb61e14: ldur            w4, [x2, #0x17]
    // 0xb61e18: DecompressPointer r4
    //     0xb61e18: add             x4, x4, HEAP, lsl #32
    // 0xb61e1c: cmp             w4, NULL
    // 0xb61e20: b.ne            #0xb61e2c
    // 0xb61e24: r2 = Null
    //     0xb61e24: mov             x2, NULL
    // 0xb61e28: b               #0xb61e34
    // 0xb61e2c: LoadField: r2 = r4->field_f
    //     0xb61e2c: ldur            w2, [x4, #0xf]
    // 0xb61e30: DecompressPointer r2
    //     0xb61e30: add             x2, x2, HEAP, lsl #32
    // 0xb61e34: cmp             w2, NULL
    // 0xb61e38: b.ne            #0xb61e44
    // 0xb61e3c: r7 = 0
    //     0xb61e3c: movz            x7, #0
    // 0xb61e40: b               #0xb61e54
    // 0xb61e44: r4 = LoadInt32Instr(r2)
    //     0xb61e44: sbfx            x4, x2, #1, #0x1f
    //     0xb61e48: tbz             w2, #0, #0xb61e50
    //     0xb61e4c: ldur            x4, [x2, #7]
    // 0xb61e50: mov             x7, x4
    // 0xb61e54: ldur            x6, [fp, #-0x30]
    // 0xb61e58: ldur            x5, [fp, #-0x48]
    // 0xb61e5c: ldur            x4, [fp, #-0x20]
    // 0xb61e60: ldur            x2, [fp, #-0x28]
    // 0xb61e64: stur            x7, [fp, #-0x50]
    // 0xb61e68: r0 = Color()
    //     0xb61e68: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb61e6c: mov             x1, x0
    // 0xb61e70: r0 = Instance_ColorSpace
    //     0xb61e70: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb61e74: StoreField: r1->field_27 = r0
    //     0xb61e74: stur            w0, [x1, #0x27]
    // 0xb61e78: d0 = 1.000000
    //     0xb61e78: fmov            d0, #1.00000000
    // 0xb61e7c: StoreField: r1->field_7 = d0
    //     0xb61e7c: stur            d0, [x1, #7]
    // 0xb61e80: ldur            x0, [fp, #-0x60]
    // 0xb61e84: ubfx            x0, x0, #0, #0x20
    // 0xb61e88: and             w2, w0, #0xff
    // 0xb61e8c: ubfx            x2, x2, #0, #0x20
    // 0xb61e90: scvtf           d0, x2
    // 0xb61e94: d1 = 255.000000
    //     0xb61e94: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb61e98: fdiv            d2, d0, d1
    // 0xb61e9c: StoreField: r1->field_f = d2
    //     0xb61e9c: stur            d2, [x1, #0xf]
    // 0xb61ea0: ldur            x0, [fp, #-0x58]
    // 0xb61ea4: ubfx            x0, x0, #0, #0x20
    // 0xb61ea8: and             w2, w0, #0xff
    // 0xb61eac: ubfx            x2, x2, #0, #0x20
    // 0xb61eb0: scvtf           d0, x2
    // 0xb61eb4: fdiv            d2, d0, d1
    // 0xb61eb8: ArrayStore: r1[0] = d2  ; List_8
    //     0xb61eb8: stur            d2, [x1, #0x17]
    // 0xb61ebc: ldur            x0, [fp, #-0x50]
    // 0xb61ec0: ubfx            x0, x0, #0, #0x20
    // 0xb61ec4: and             w2, w0, #0xff
    // 0xb61ec8: ubfx            x2, x2, #0, #0x20
    // 0xb61ecc: scvtf           d0, x2
    // 0xb61ed0: fdiv            d2, d0, d1
    // 0xb61ed4: StoreField: r1->field_1f = d2
    //     0xb61ed4: stur            d2, [x1, #0x1f]
    // 0xb61ed8: r16 = <Color>
    //     0xb61ed8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb61edc: ldr             x16, [x16, #0xf80]
    // 0xb61ee0: stp             x1, x16, [SP]
    // 0xb61ee4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb61ee4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61ee8: r0 = all()
    //     0xb61ee8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb61eec: stur            x0, [fp, #-8]
    // 0xb61ef0: r0 = Radius()
    //     0xb61ef0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb61ef4: d0 = 35.000000
    //     0xb61ef4: add             x17, PP, #0x3e, lsl #12  ; [pp+0x3eec8] IMM: double(35) from 0x4041800000000000
    //     0xb61ef8: ldr             d0, [x17, #0xec8]
    // 0xb61efc: stur            x0, [fp, #-0x40]
    // 0xb61f00: StoreField: r0->field_7 = d0
    //     0xb61f00: stur            d0, [x0, #7]
    // 0xb61f04: StoreField: r0->field_f = d0
    //     0xb61f04: stur            d0, [x0, #0xf]
    // 0xb61f08: r0 = BorderRadius()
    //     0xb61f08: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb61f0c: mov             x1, x0
    // 0xb61f10: ldur            x0, [fp, #-0x40]
    // 0xb61f14: stur            x1, [fp, #-0x68]
    // 0xb61f18: StoreField: r1->field_7 = r0
    //     0xb61f18: stur            w0, [x1, #7]
    // 0xb61f1c: StoreField: r1->field_b = r0
    //     0xb61f1c: stur            w0, [x1, #0xb]
    // 0xb61f20: StoreField: r1->field_f = r0
    //     0xb61f20: stur            w0, [x1, #0xf]
    // 0xb61f24: StoreField: r1->field_13 = r0
    //     0xb61f24: stur            w0, [x1, #0x13]
    // 0xb61f28: r0 = RoundedRectangleBorder()
    //     0xb61f28: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb61f2c: mov             x1, x0
    // 0xb61f30: ldur            x0, [fp, #-0x68]
    // 0xb61f34: StoreField: r1->field_b = r0
    //     0xb61f34: stur            w0, [x1, #0xb]
    // 0xb61f38: r0 = Instance_BorderSide
    //     0xb61f38: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb61f3c: ldr             x0, [x0, #0xe20]
    // 0xb61f40: StoreField: r1->field_7 = r0
    //     0xb61f40: stur            w0, [x1, #7]
    // 0xb61f44: r16 = <RoundedRectangleBorder>
    //     0xb61f44: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb61f48: ldr             x16, [x16, #0xf78]
    // 0xb61f4c: stp             x1, x16, [SP]
    // 0xb61f50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb61f50: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb61f54: r0 = all()
    //     0xb61f54: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb61f58: stur            x0, [fp, #-0x40]
    // 0xb61f5c: r0 = ButtonStyle()
    //     0xb61f5c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb61f60: mov             x1, x0
    // 0xb61f64: ldur            x0, [fp, #-8]
    // 0xb61f68: stur            x1, [fp, #-0x68]
    // 0xb61f6c: StoreField: r1->field_b = r0
    //     0xb61f6c: stur            w0, [x1, #0xb]
    // 0xb61f70: ldur            x0, [fp, #-0x38]
    // 0xb61f74: StoreField: r1->field_23 = r0
    //     0xb61f74: stur            w0, [x1, #0x23]
    // 0xb61f78: ldur            x0, [fp, #-0x40]
    // 0xb61f7c: StoreField: r1->field_43 = r0
    //     0xb61f7c: stur            w0, [x1, #0x43]
    // 0xb61f80: r0 = TextButtonThemeData()
    //     0xb61f80: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb61f84: mov             x2, x0
    // 0xb61f88: ldur            x0, [fp, #-0x68]
    // 0xb61f8c: stur            x2, [fp, #-8]
    // 0xb61f90: StoreField: r2->field_7 = r0
    //     0xb61f90: stur            w0, [x2, #7]
    // 0xb61f94: r16 = 16.000000
    //     0xb61f94: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb61f98: ldr             x16, [x16, #0x188]
    // 0xb61f9c: r30 = Instance_Color
    //     0xb61f9c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb61fa0: stp             lr, x16, [SP]
    // 0xb61fa4: ldur            x1, [fp, #-0x10]
    // 0xb61fa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb61fa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb61fac: ldr             x4, [x4, #0xaa0]
    // 0xb61fb0: r0 = copyWith()
    //     0xb61fb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb61fb4: stur            x0, [fp, #-0x10]
    // 0xb61fb8: r0 = Text()
    //     0xb61fb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb61fbc: mov             x3, x0
    // 0xb61fc0: r0 = "Yes, Continue"
    //     0xb61fc0: add             x0, PP, #0x56, lsl #12  ; [pp+0x563a8] "Yes, Continue"
    //     0xb61fc4: ldr             x0, [x0, #0x3a8]
    // 0xb61fc8: stur            x3, [fp, #-0x38]
    // 0xb61fcc: StoreField: r3->field_b = r0
    //     0xb61fcc: stur            w0, [x3, #0xb]
    // 0xb61fd0: ldur            x0, [fp, #-0x10]
    // 0xb61fd4: StoreField: r3->field_13 = r0
    //     0xb61fd4: stur            w0, [x3, #0x13]
    // 0xb61fd8: ldur            x2, [fp, #-0x18]
    // 0xb61fdc: r1 = Function '<anonymous closure>':.
    //     0xb61fdc: add             x1, PP, #0x56, lsl #12  ; [pp+0x563b0] AnonymousClosure: (0xa473d4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xb61fe0: ldr             x1, [x1, #0x3b0]
    // 0xb61fe4: r0 = AllocateClosure()
    //     0xb61fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb61fe8: stur            x0, [fp, #-0x10]
    // 0xb61fec: r0 = TextButton()
    //     0xb61fec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb61ff0: mov             x1, x0
    // 0xb61ff4: ldur            x0, [fp, #-0x10]
    // 0xb61ff8: stur            x1, [fp, #-0x18]
    // 0xb61ffc: StoreField: r1->field_b = r0
    //     0xb61ffc: stur            w0, [x1, #0xb]
    // 0xb62000: r0 = false
    //     0xb62000: add             x0, NULL, #0x30  ; false
    // 0xb62004: StoreField: r1->field_27 = r0
    //     0xb62004: stur            w0, [x1, #0x27]
    // 0xb62008: r0 = true
    //     0xb62008: add             x0, NULL, #0x20  ; true
    // 0xb6200c: StoreField: r1->field_2f = r0
    //     0xb6200c: stur            w0, [x1, #0x2f]
    // 0xb62010: ldur            x0, [fp, #-0x38]
    // 0xb62014: StoreField: r1->field_37 = r0
    //     0xb62014: stur            w0, [x1, #0x37]
    // 0xb62018: r0 = TextButtonTheme()
    //     0xb62018: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb6201c: mov             x2, x0
    // 0xb62020: ldur            x0, [fp, #-8]
    // 0xb62024: stur            x2, [fp, #-0x10]
    // 0xb62028: StoreField: r2->field_f = r0
    //     0xb62028: stur            w0, [x2, #0xf]
    // 0xb6202c: ldur            x0, [fp, #-0x18]
    // 0xb62030: StoreField: r2->field_b = r0
    //     0xb62030: stur            w0, [x2, #0xb]
    // 0xb62034: r1 = <FlexParentData>
    //     0xb62034: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb62038: ldr             x1, [x1, #0xe00]
    // 0xb6203c: r0 = Expanded()
    //     0xb6203c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb62040: mov             x3, x0
    // 0xb62044: r0 = 1
    //     0xb62044: movz            x0, #0x1
    // 0xb62048: stur            x3, [fp, #-8]
    // 0xb6204c: StoreField: r3->field_13 = r0
    //     0xb6204c: stur            x0, [x3, #0x13]
    // 0xb62050: r0 = Instance_FlexFit
    //     0xb62050: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb62054: ldr             x0, [x0, #0xe08]
    // 0xb62058: StoreField: r3->field_1b = r0
    //     0xb62058: stur            w0, [x3, #0x1b]
    // 0xb6205c: ldur            x0, [fp, #-0x10]
    // 0xb62060: StoreField: r3->field_b = r0
    //     0xb62060: stur            w0, [x3, #0xb]
    // 0xb62064: r1 = Null
    //     0xb62064: mov             x1, NULL
    // 0xb62068: r2 = 6
    //     0xb62068: movz            x2, #0x6
    // 0xb6206c: r0 = AllocateArray()
    //     0xb6206c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb62070: mov             x2, x0
    // 0xb62074: ldur            x0, [fp, #-0x28]
    // 0xb62078: stur            x2, [fp, #-0x10]
    // 0xb6207c: StoreField: r2->field_f = r0
    //     0xb6207c: stur            w0, [x2, #0xf]
    // 0xb62080: r16 = Instance_SizedBox
    //     0xb62080: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb62084: ldr             x16, [x16, #0x998]
    // 0xb62088: StoreField: r2->field_13 = r16
    //     0xb62088: stur            w16, [x2, #0x13]
    // 0xb6208c: ldur            x0, [fp, #-8]
    // 0xb62090: ArrayStore: r2[0] = r0  ; List_4
    //     0xb62090: stur            w0, [x2, #0x17]
    // 0xb62094: r1 = <Widget>
    //     0xb62094: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb62098: r0 = AllocateGrowableArray()
    //     0xb62098: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6209c: mov             x1, x0
    // 0xb620a0: ldur            x0, [fp, #-0x10]
    // 0xb620a4: stur            x1, [fp, #-8]
    // 0xb620a8: StoreField: r1->field_f = r0
    //     0xb620a8: stur            w0, [x1, #0xf]
    // 0xb620ac: r0 = 6
    //     0xb620ac: movz            x0, #0x6
    // 0xb620b0: StoreField: r1->field_b = r0
    //     0xb620b0: stur            w0, [x1, #0xb]
    // 0xb620b4: r0 = Row()
    //     0xb620b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb620b8: mov             x3, x0
    // 0xb620bc: r0 = Instance_Axis
    //     0xb620bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb620c0: stur            x3, [fp, #-0x10]
    // 0xb620c4: StoreField: r3->field_f = r0
    //     0xb620c4: stur            w0, [x3, #0xf]
    // 0xb620c8: r0 = Instance_MainAxisAlignment
    //     0xb620c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb620cc: ldr             x0, [x0, #0xd10]
    // 0xb620d0: StoreField: r3->field_13 = r0
    //     0xb620d0: stur            w0, [x3, #0x13]
    // 0xb620d4: r0 = Instance_MainAxisSize
    //     0xb620d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb620d8: ldr             x0, [x0, #0xa10]
    // 0xb620dc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb620dc: stur            w0, [x3, #0x17]
    // 0xb620e0: r0 = Instance_CrossAxisAlignment
    //     0xb620e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb620e4: ldr             x0, [x0, #0xa18]
    // 0xb620e8: StoreField: r3->field_1b = r0
    //     0xb620e8: stur            w0, [x3, #0x1b]
    // 0xb620ec: r0 = Instance_VerticalDirection
    //     0xb620ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb620f0: ldr             x0, [x0, #0xa20]
    // 0xb620f4: StoreField: r3->field_23 = r0
    //     0xb620f4: stur            w0, [x3, #0x23]
    // 0xb620f8: r4 = Instance_Clip
    //     0xb620f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb620fc: ldr             x4, [x4, #0x38]
    // 0xb62100: StoreField: r3->field_2b = r4
    //     0xb62100: stur            w4, [x3, #0x2b]
    // 0xb62104: StoreField: r3->field_2f = rZR
    //     0xb62104: stur            xzr, [x3, #0x2f]
    // 0xb62108: ldur            x1, [fp, #-8]
    // 0xb6210c: StoreField: r3->field_b = r1
    //     0xb6210c: stur            w1, [x3, #0xb]
    // 0xb62110: r1 = Null
    //     0xb62110: mov             x1, NULL
    // 0xb62114: r2 = 10
    //     0xb62114: movz            x2, #0xa
    // 0xb62118: r0 = AllocateArray()
    //     0xb62118: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6211c: mov             x2, x0
    // 0xb62120: ldur            x0, [fp, #-0x30]
    // 0xb62124: stur            x2, [fp, #-8]
    // 0xb62128: StoreField: r2->field_f = r0
    //     0xb62128: stur            w0, [x2, #0xf]
    // 0xb6212c: ldur            x0, [fp, #-0x48]
    // 0xb62130: StoreField: r2->field_13 = r0
    //     0xb62130: stur            w0, [x2, #0x13]
    // 0xb62134: ldur            x0, [fp, #-0x20]
    // 0xb62138: ArrayStore: r2[0] = r0  ; List_4
    //     0xb62138: stur            w0, [x2, #0x17]
    // 0xb6213c: r16 = Instance_SizedBox
    //     0xb6213c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb62140: ldr             x16, [x16, #0x328]
    // 0xb62144: StoreField: r2->field_1b = r16
    //     0xb62144: stur            w16, [x2, #0x1b]
    // 0xb62148: ldur            x0, [fp, #-0x10]
    // 0xb6214c: StoreField: r2->field_1f = r0
    //     0xb6214c: stur            w0, [x2, #0x1f]
    // 0xb62150: r1 = <Widget>
    //     0xb62150: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb62154: r0 = AllocateGrowableArray()
    //     0xb62154: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb62158: mov             x1, x0
    // 0xb6215c: ldur            x0, [fp, #-8]
    // 0xb62160: stur            x1, [fp, #-0x10]
    // 0xb62164: StoreField: r1->field_f = r0
    //     0xb62164: stur            w0, [x1, #0xf]
    // 0xb62168: r0 = 10
    //     0xb62168: movz            x0, #0xa
    // 0xb6216c: StoreField: r1->field_b = r0
    //     0xb6216c: stur            w0, [x1, #0xb]
    // 0xb62170: r0 = Column()
    //     0xb62170: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb62174: mov             x1, x0
    // 0xb62178: r0 = Instance_Axis
    //     0xb62178: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6217c: stur            x1, [fp, #-8]
    // 0xb62180: StoreField: r1->field_f = r0
    //     0xb62180: stur            w0, [x1, #0xf]
    // 0xb62184: r0 = Instance_MainAxisAlignment
    //     0xb62184: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb62188: ldr             x0, [x0, #0xa08]
    // 0xb6218c: StoreField: r1->field_13 = r0
    //     0xb6218c: stur            w0, [x1, #0x13]
    // 0xb62190: r0 = Instance_MainAxisSize
    //     0xb62190: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb62194: ldr             x0, [x0, #0xdd0]
    // 0xb62198: ArrayStore: r1[0] = r0  ; List_4
    //     0xb62198: stur            w0, [x1, #0x17]
    // 0xb6219c: r0 = Instance_CrossAxisAlignment
    //     0xb6219c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb621a0: ldr             x0, [x0, #0x890]
    // 0xb621a4: StoreField: r1->field_1b = r0
    //     0xb621a4: stur            w0, [x1, #0x1b]
    // 0xb621a8: r0 = Instance_VerticalDirection
    //     0xb621a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb621ac: ldr             x0, [x0, #0xa20]
    // 0xb621b0: StoreField: r1->field_23 = r0
    //     0xb621b0: stur            w0, [x1, #0x23]
    // 0xb621b4: r0 = Instance_Clip
    //     0xb621b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb621b8: ldr             x0, [x0, #0x38]
    // 0xb621bc: StoreField: r1->field_2b = r0
    //     0xb621bc: stur            w0, [x1, #0x2b]
    // 0xb621c0: StoreField: r1->field_2f = rZR
    //     0xb621c0: stur            xzr, [x1, #0x2f]
    // 0xb621c4: ldur            x0, [fp, #-0x10]
    // 0xb621c8: StoreField: r1->field_b = r0
    //     0xb621c8: stur            w0, [x1, #0xb]
    // 0xb621cc: r0 = Padding()
    //     0xb621cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb621d0: r1 = Instance_EdgeInsets
    //     0xb621d0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb621d4: ldr             x1, [x1, #0x1f0]
    // 0xb621d8: StoreField: r0->field_f = r1
    //     0xb621d8: stur            w1, [x0, #0xf]
    // 0xb621dc: ldur            x1, [fp, #-8]
    // 0xb621e0: StoreField: r0->field_b = r1
    //     0xb621e0: stur            w1, [x0, #0xb]
    // 0xb621e4: LeaveFrame
    //     0xb621e4: mov             SP, fp
    //     0xb621e8: ldp             fp, lr, [SP], #0x10
    // 0xb621ec: ret
    //     0xb621ec: ret             
    // 0xb621f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb621f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb621f4: b               #0xb61670
    // 0xb621f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb621f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb621fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb621fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb62200: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb62200: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb62204: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb62204: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4077, size: 0x50, field offset: 0xc
//   const constructor, 
class CustomizedBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f2f8, size: 0x24
    // 0xc7f2f8: EnterFrame
    //     0xc7f2f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f2fc: mov             fp, SP
    // 0xc7f300: mov             x0, x1
    // 0xc7f304: r1 = <CustomizedBottomSheet>
    //     0xc7f304: add             x1, PP, #0x48, lsl #12  ; [pp+0x48880] TypeArguments: <CustomizedBottomSheet>
    //     0xc7f308: ldr             x1, [x1, #0x880]
    // 0xc7f30c: r0 = _CustomizedBottomSheetState()
    //     0xc7f30c: bl              #0xc7f31c  ; Allocate_CustomizedBottomSheetStateStub -> _CustomizedBottomSheetState (size=0x14)
    // 0xc7f310: LeaveFrame
    //     0xc7f310: mov             SP, fp
    //     0xc7f314: ldp             fp, lr, [SP], #0x10
    // 0xc7f318: ret
    //     0xc7f318: ret             
  }
}
