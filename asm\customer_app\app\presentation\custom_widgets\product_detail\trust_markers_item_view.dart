// lib: , url: package:customer_app/app/presentation/custom_widgets/product_detail/trust_markers_item_view.dart

// class id: 1049087, size: 0x8
class :: {
}

// class id: 3572, size: 0x14, field offset: 0x14
class _TrustMarkersItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9af87c, size: 0x16c
    // 0x9af87c: EnterFrame
    //     0x9af87c: stp             fp, lr, [SP, #-0x10]!
    //     0x9af880: mov             fp, SP
    // 0x9af884: AllocStack(0x30)
    //     0x9af884: sub             SP, SP, #0x30
    // 0x9af888: SetupParameters(_TrustMarkersItemViewState this /* r1 => r1, fp-0x8 */)
    //     0x9af888: stur            x1, [fp, #-8]
    // 0x9af88c: CheckStackOverflow
    //     0x9af88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9af890: cmp             SP, x16
    //     0x9af894: b.ls            #0x9af9dc
    // 0x9af898: r1 = 1
    //     0x9af898: movz            x1, #0x1
    // 0x9af89c: r0 = AllocateContext()
    //     0x9af89c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9af8a0: mov             x1, x0
    // 0x9af8a4: ldur            x0, [fp, #-8]
    // 0x9af8a8: stur            x1, [fp, #-0x18]
    // 0x9af8ac: StoreField: r1->field_f = r0
    //     0x9af8ac: stur            w0, [x1, #0xf]
    // 0x9af8b0: LoadField: r2 = r0->field_b
    //     0x9af8b0: ldur            w2, [x0, #0xb]
    // 0x9af8b4: DecompressPointer r2
    //     0x9af8b4: add             x2, x2, HEAP, lsl #32
    // 0x9af8b8: stur            x2, [fp, #-0x10]
    // 0x9af8bc: cmp             w2, NULL
    // 0x9af8c0: b.eq            #0x9af9e4
    // 0x9af8c4: r0 = RoundedRectangleBorder()
    //     0x9af8c4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x9af8c8: mov             x3, x0
    // 0x9af8cc: r0 = Instance_BorderRadius
    //     0x9af8cc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x9af8d0: ldr             x0, [x0, #0xf70]
    // 0x9af8d4: stur            x3, [fp, #-0x20]
    // 0x9af8d8: StoreField: r3->field_b = r0
    //     0x9af8d8: stur            w0, [x3, #0xb]
    // 0x9af8dc: r0 = Instance_BorderSide
    //     0x9af8dc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x9af8e0: ldr             x0, [x0, #0xe20]
    // 0x9af8e4: StoreField: r3->field_7 = r0
    //     0x9af8e4: stur            w0, [x3, #7]
    // 0x9af8e8: ldur            x0, [fp, #-0x10]
    // 0x9af8ec: LoadField: r1 = r0->field_b
    //     0x9af8ec: ldur            w1, [x0, #0xb]
    // 0x9af8f0: DecompressPointer r1
    //     0x9af8f0: add             x1, x1, HEAP, lsl #32
    // 0x9af8f4: LoadField: r0 = r1->field_1b
    //     0x9af8f4: ldur            w0, [x1, #0x1b]
    // 0x9af8f8: DecompressPointer r0
    //     0x9af8f8: add             x0, x0, HEAP, lsl #32
    // 0x9af8fc: cmp             w0, NULL
    // 0x9af900: b.ne            #0x9af90c
    // 0x9af904: r0 = Null
    //     0x9af904: mov             x0, NULL
    // 0x9af908: b               #0x9af914
    // 0x9af90c: LoadField: r1 = r0->field_b
    //     0x9af90c: ldur            w1, [x0, #0xb]
    // 0x9af910: mov             x0, x1
    // 0x9af914: ldur            x2, [fp, #-0x18]
    // 0x9af918: stur            x0, [fp, #-8]
    // 0x9af91c: r1 = Function '<anonymous closure>':.
    //     0x9af91c: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bac0] AnonymousClosure: (0x9afa18), in [package:customer_app/app/presentation/custom_widgets/product_detail/trust_markers_item_view.dart] _TrustMarkersItemViewState::build (0x9af87c)
    //     0x9af920: ldr             x1, [x1, #0xac0]
    // 0x9af924: r0 = AllocateClosure()
    //     0x9af924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9af928: stur            x0, [fp, #-0x10]
    // 0x9af92c: r0 = ListView()
    //     0x9af92c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x9af930: stur            x0, [fp, #-0x18]
    // 0x9af934: r16 = Instance_NeverScrollableScrollPhysics
    //     0x9af934: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x9af938: ldr             x16, [x16, #0x1c8]
    // 0x9af93c: r30 = true
    //     0x9af93c: add             lr, NULL, #0x20  ; true
    // 0x9af940: stp             lr, x16, [SP]
    // 0x9af944: mov             x1, x0
    // 0x9af948: ldur            x2, [fp, #-0x10]
    // 0x9af94c: ldur            x3, [fp, #-8]
    // 0x9af950: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0x9af950: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0x9af954: ldr             x4, [x4, #0xd18]
    // 0x9af958: r0 = ListView.builder()
    //     0x9af958: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x9af95c: r0 = Card()
    //     0x9af95c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x9af960: mov             x1, x0
    // 0x9af964: r0 = Instance_Color
    //     0x9af964: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x9af968: ldr             x0, [x0, #0xf88]
    // 0x9af96c: stur            x1, [fp, #-8]
    // 0x9af970: StoreField: r1->field_b = r0
    //     0x9af970: stur            w0, [x1, #0xb]
    // 0x9af974: r0 = 0.000000
    //     0x9af974: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x9af978: ArrayStore: r1[0] = r0  ; List_4
    //     0x9af978: stur            w0, [x1, #0x17]
    // 0x9af97c: ldur            x0, [fp, #-0x20]
    // 0x9af980: StoreField: r1->field_1b = r0
    //     0x9af980: stur            w0, [x1, #0x1b]
    // 0x9af984: r0 = true
    //     0x9af984: add             x0, NULL, #0x20  ; true
    // 0x9af988: StoreField: r1->field_1f = r0
    //     0x9af988: stur            w0, [x1, #0x1f]
    // 0x9af98c: r2 = Instance_EdgeInsets
    //     0x9af98c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x9af990: StoreField: r1->field_27 = r2
    //     0x9af990: stur            w2, [x1, #0x27]
    // 0x9af994: r2 = Instance_Clip
    //     0x9af994: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0x9af998: ldr             x2, [x2, #0xb50]
    // 0x9af99c: StoreField: r1->field_23 = r2
    //     0x9af99c: stur            w2, [x1, #0x23]
    // 0x9af9a0: ldur            x2, [fp, #-0x18]
    // 0x9af9a4: StoreField: r1->field_2f = r2
    //     0x9af9a4: stur            w2, [x1, #0x2f]
    // 0x9af9a8: StoreField: r1->field_2b = r0
    //     0x9af9a8: stur            w0, [x1, #0x2b]
    // 0x9af9ac: r0 = Instance__CardVariant
    //     0x9af9ac: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x9af9b0: ldr             x0, [x0, #0xa68]
    // 0x9af9b4: StoreField: r1->field_33 = r0
    //     0x9af9b4: stur            w0, [x1, #0x33]
    // 0x9af9b8: r0 = Padding()
    //     0x9af9b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9af9bc: r1 = Instance_EdgeInsets
    //     0x9af9bc: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x9af9c0: ldr             x1, [x1, #0x240]
    // 0x9af9c4: StoreField: r0->field_f = r1
    //     0x9af9c4: stur            w1, [x0, #0xf]
    // 0x9af9c8: ldur            x1, [fp, #-8]
    // 0x9af9cc: StoreField: r0->field_b = r1
    //     0x9af9cc: stur            w1, [x0, #0xb]
    // 0x9af9d0: LeaveFrame
    //     0x9af9d0: mov             SP, fp
    //     0x9af9d4: ldp             fp, lr, [SP], #0x10
    // 0x9af9d8: ret
    //     0x9af9d8: ret             
    // 0x9af9dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9af9dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9af9e0: b               #0x9af898
    // 0x9af9e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af9e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9afa18, size: 0x810
    // 0x9afa18: EnterFrame
    //     0x9afa18: stp             fp, lr, [SP, #-0x10]!
    //     0x9afa1c: mov             fp, SP
    // 0x9afa20: AllocStack(0x50)
    //     0x9afa20: sub             SP, SP, #0x50
    // 0x9afa24: SetupParameters()
    //     0x9afa24: ldr             x0, [fp, #0x20]
    //     0x9afa28: ldur            w2, [x0, #0x17]
    //     0x9afa2c: add             x2, x2, HEAP, lsl #32
    //     0x9afa30: stur            x2, [fp, #-8]
    // 0x9afa34: CheckStackOverflow
    //     0x9afa34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9afa38: cmp             SP, x16
    //     0x9afa3c: b.ls            #0x9b01f4
    // 0x9afa40: r0 = Container()
    //     0x9afa40: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9afa44: mov             x1, x0
    // 0x9afa48: stur            x0, [fp, #-0x10]
    // 0x9afa4c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9afa4c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9afa50: r0 = Container()
    //     0x9afa50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9afa54: ldur            x2, [fp, #-8]
    // 0x9afa58: LoadField: r0 = r2->field_f
    //     0x9afa58: ldur            w0, [x2, #0xf]
    // 0x9afa5c: DecompressPointer r0
    //     0x9afa5c: add             x0, x0, HEAP, lsl #32
    // 0x9afa60: LoadField: r1 = r0->field_b
    //     0x9afa60: ldur            w1, [x0, #0xb]
    // 0x9afa64: DecompressPointer r1
    //     0x9afa64: add             x1, x1, HEAP, lsl #32
    // 0x9afa68: cmp             w1, NULL
    // 0x9afa6c: b.eq            #0x9b01fc
    // 0x9afa70: LoadField: r0 = r1->field_b
    //     0x9afa70: ldur            w0, [x1, #0xb]
    // 0x9afa74: DecompressPointer r0
    //     0x9afa74: add             x0, x0, HEAP, lsl #32
    // 0x9afa78: LoadField: r3 = r0->field_1b
    //     0x9afa78: ldur            w3, [x0, #0x1b]
    // 0x9afa7c: DecompressPointer r3
    //     0x9afa7c: add             x3, x3, HEAP, lsl #32
    // 0x9afa80: cmp             w3, NULL
    // 0x9afa84: b.ne            #0x9afa94
    // 0x9afa88: ldr             x4, [fp, #0x10]
    // 0x9afa8c: r0 = Null
    //     0x9afa8c: mov             x0, NULL
    // 0x9afa90: b               #0x9afad8
    // 0x9afa94: ldr             x4, [fp, #0x10]
    // 0x9afa98: LoadField: r0 = r3->field_b
    //     0x9afa98: ldur            w0, [x3, #0xb]
    // 0x9afa9c: r5 = LoadInt32Instr(r4)
    //     0x9afa9c: sbfx            x5, x4, #1, #0x1f
    //     0x9afaa0: tbz             w4, #0, #0x9afaa8
    //     0x9afaa4: ldur            x5, [x4, #7]
    // 0x9afaa8: r1 = LoadInt32Instr(r0)
    //     0x9afaa8: sbfx            x1, x0, #1, #0x1f
    // 0x9afaac: mov             x0, x1
    // 0x9afab0: mov             x1, x5
    // 0x9afab4: cmp             x1, x0
    // 0x9afab8: b.hs            #0x9b0200
    // 0x9afabc: LoadField: r0 = r3->field_f
    //     0x9afabc: ldur            w0, [x3, #0xf]
    // 0x9afac0: DecompressPointer r0
    //     0x9afac0: add             x0, x0, HEAP, lsl #32
    // 0x9afac4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9afac4: add             x16, x0, x5, lsl #2
    //     0x9afac8: ldur            w1, [x16, #0xf]
    // 0x9afacc: DecompressPointer r1
    //     0x9afacc: add             x1, x1, HEAP, lsl #32
    // 0x9afad0: LoadField: r0 = r1->field_1f
    //     0x9afad0: ldur            w0, [x1, #0x1f]
    // 0x9afad4: DecompressPointer r0
    //     0x9afad4: add             x0, x0, HEAP, lsl #32
    // 0x9afad8: r1 = LoadClassIdInstr(r0)
    //     0x9afad8: ldur            x1, [x0, #-1]
    //     0x9afadc: ubfx            x1, x1, #0xc, #0x14
    // 0x9afae0: r16 = "delivery"
    //     0x9afae0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a78] "delivery"
    //     0x9afae4: ldr             x16, [x16, #0xa78]
    // 0x9afae8: stp             x16, x0, [SP]
    // 0x9afaec: mov             x0, x1
    // 0x9afaf0: mov             lr, x0
    // 0x9afaf4: ldr             lr, [x21, lr, lsl #3]
    // 0x9afaf8: blr             lr
    // 0x9afafc: tbnz            w0, #4, #0x9afbc0
    // 0x9afb00: ldr             x1, [fp, #0x18]
    // 0x9afb04: r0 = of()
    //     0x9afb04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9afb08: LoadField: r1 = r0->field_5b
    //     0x9afb08: ldur            w1, [x0, #0x5b]
    // 0x9afb0c: DecompressPointer r1
    //     0x9afb0c: add             x1, x1, HEAP, lsl #32
    // 0x9afb10: stur            x1, [fp, #-0x18]
    // 0x9afb14: r0 = BoxDecoration()
    //     0x9afb14: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9afb18: mov             x1, x0
    // 0x9afb1c: ldur            x0, [fp, #-0x18]
    // 0x9afb20: stur            x1, [fp, #-0x20]
    // 0x9afb24: StoreField: r1->field_7 = r0
    //     0x9afb24: stur            w0, [x1, #7]
    // 0x9afb28: r2 = Instance_BoxShape
    //     0x9afb28: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9afb2c: ldr             x2, [x2, #0x970]
    // 0x9afb30: StoreField: r1->field_23 = r2
    //     0x9afb30: stur            w2, [x1, #0x23]
    // 0x9afb34: r0 = SvgPicture()
    //     0x9afb34: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9afb38: stur            x0, [fp, #-0x18]
    // 0x9afb3c: r16 = "Acme Logo"
    //     0x9afb3c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bac8] "Acme Logo"
    //     0x9afb40: ldr             x16, [x16, #0xac8]
    // 0x9afb44: r30 = Instance_BoxFit
    //     0x9afb44: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x9afb48: ldr             lr, [lr, #0xb18]
    // 0x9afb4c: stp             lr, x16, [SP]
    // 0x9afb50: mov             x1, x0
    // 0x9afb54: r2 = "assets/images/truck.svg"
    //     0x9afb54: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5bad0] "assets/images/truck.svg"
    //     0x9afb58: ldr             x2, [x2, #0xad0]
    // 0x9afb5c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x9afb5c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x9afb60: ldr             x4, [x4, #0xb28]
    // 0x9afb64: r0 = SvgPicture.asset()
    //     0x9afb64: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9afb68: r0 = Container()
    //     0x9afb68: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9afb6c: stur            x0, [fp, #-0x28]
    // 0x9afb70: r16 = 50.000000
    //     0x9afb70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afb74: ldr             x16, [x16, #0xa90]
    // 0x9afb78: r30 = 50.000000
    //     0x9afb78: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afb7c: ldr             lr, [lr, #0xa90]
    // 0x9afb80: stp             lr, x16, [SP, #0x10]
    // 0x9afb84: ldur            x16, [fp, #-0x20]
    // 0x9afb88: ldur            lr, [fp, #-0x18]
    // 0x9afb8c: stp             lr, x16, [SP]
    // 0x9afb90: mov             x1, x0
    // 0x9afb94: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x9afb94: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x9afb98: ldr             x4, [x4, #0x870]
    // 0x9afb9c: r0 = Container()
    //     0x9afb9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9afba0: r0 = Padding()
    //     0x9afba0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9afba4: r3 = Instance_EdgeInsets
    //     0x9afba4: add             x3, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x9afba8: ldr             x3, [x3, #0x868]
    // 0x9afbac: StoreField: r0->field_f = r3
    //     0x9afbac: stur            w3, [x0, #0xf]
    // 0x9afbb0: ldur            x1, [fp, #-0x28]
    // 0x9afbb4: StoreField: r0->field_b = r1
    //     0x9afbb4: stur            w1, [x0, #0xb]
    // 0x9afbb8: mov             x3, x0
    // 0x9afbbc: b               #0x9afeb8
    // 0x9afbc0: ldur            x4, [fp, #-8]
    // 0x9afbc4: r2 = Instance_BoxShape
    //     0x9afbc4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9afbc8: ldr             x2, [x2, #0x970]
    // 0x9afbcc: r3 = Instance_EdgeInsets
    //     0x9afbcc: add             x3, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x9afbd0: ldr             x3, [x3, #0x868]
    // 0x9afbd4: LoadField: r0 = r4->field_f
    //     0x9afbd4: ldur            w0, [x4, #0xf]
    // 0x9afbd8: DecompressPointer r0
    //     0x9afbd8: add             x0, x0, HEAP, lsl #32
    // 0x9afbdc: LoadField: r1 = r0->field_b
    //     0x9afbdc: ldur            w1, [x0, #0xb]
    // 0x9afbe0: DecompressPointer r1
    //     0x9afbe0: add             x1, x1, HEAP, lsl #32
    // 0x9afbe4: cmp             w1, NULL
    // 0x9afbe8: b.eq            #0x9b0204
    // 0x9afbec: LoadField: r0 = r1->field_b
    //     0x9afbec: ldur            w0, [x1, #0xb]
    // 0x9afbf0: DecompressPointer r0
    //     0x9afbf0: add             x0, x0, HEAP, lsl #32
    // 0x9afbf4: LoadField: r5 = r0->field_1b
    //     0x9afbf4: ldur            w5, [x0, #0x1b]
    // 0x9afbf8: DecompressPointer r5
    //     0x9afbf8: add             x5, x5, HEAP, lsl #32
    // 0x9afbfc: cmp             w5, NULL
    // 0x9afc00: b.ne            #0x9afc10
    // 0x9afc04: ldr             x6, [fp, #0x10]
    // 0x9afc08: r0 = Null
    //     0x9afc08: mov             x0, NULL
    // 0x9afc0c: b               #0x9afc54
    // 0x9afc10: ldr             x6, [fp, #0x10]
    // 0x9afc14: LoadField: r0 = r5->field_b
    //     0x9afc14: ldur            w0, [x5, #0xb]
    // 0x9afc18: r7 = LoadInt32Instr(r6)
    //     0x9afc18: sbfx            x7, x6, #1, #0x1f
    //     0x9afc1c: tbz             w6, #0, #0x9afc24
    //     0x9afc20: ldur            x7, [x6, #7]
    // 0x9afc24: r1 = LoadInt32Instr(r0)
    //     0x9afc24: sbfx            x1, x0, #1, #0x1f
    // 0x9afc28: mov             x0, x1
    // 0x9afc2c: mov             x1, x7
    // 0x9afc30: cmp             x1, x0
    // 0x9afc34: b.hs            #0x9b0208
    // 0x9afc38: LoadField: r0 = r5->field_f
    //     0x9afc38: ldur            w0, [x5, #0xf]
    // 0x9afc3c: DecompressPointer r0
    //     0x9afc3c: add             x0, x0, HEAP, lsl #32
    // 0x9afc40: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x9afc40: add             x16, x0, x7, lsl #2
    //     0x9afc44: ldur            w1, [x16, #0xf]
    // 0x9afc48: DecompressPointer r1
    //     0x9afc48: add             x1, x1, HEAP, lsl #32
    // 0x9afc4c: LoadField: r0 = r1->field_1f
    //     0x9afc4c: ldur            w0, [x1, #0x1f]
    // 0x9afc50: DecompressPointer r0
    //     0x9afc50: add             x0, x0, HEAP, lsl #32
    // 0x9afc54: r1 = LoadClassIdInstr(r0)
    //     0x9afc54: ldur            x1, [x0, #-1]
    //     0x9afc58: ubfx            x1, x1, #0xc, #0x14
    // 0x9afc5c: r16 = "payment"
    //     0x9afc5c: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5bad8] "payment"
    //     0x9afc60: ldr             x16, [x16, #0xad8]
    // 0x9afc64: stp             x16, x0, [SP]
    // 0x9afc68: mov             x0, x1
    // 0x9afc6c: mov             lr, x0
    // 0x9afc70: ldr             lr, [x21, lr, lsl #3]
    // 0x9afc74: blr             lr
    // 0x9afc78: tbnz            w0, #4, #0x9afd30
    // 0x9afc7c: ldr             x1, [fp, #0x18]
    // 0x9afc80: r0 = of()
    //     0x9afc80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9afc84: LoadField: r1 = r0->field_5b
    //     0x9afc84: ldur            w1, [x0, #0x5b]
    // 0x9afc88: DecompressPointer r1
    //     0x9afc88: add             x1, x1, HEAP, lsl #32
    // 0x9afc8c: stur            x1, [fp, #-0x18]
    // 0x9afc90: r0 = BoxDecoration()
    //     0x9afc90: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9afc94: mov             x1, x0
    // 0x9afc98: ldur            x0, [fp, #-0x18]
    // 0x9afc9c: stur            x1, [fp, #-0x20]
    // 0x9afca0: StoreField: r1->field_7 = r0
    //     0x9afca0: stur            w0, [x1, #7]
    // 0x9afca4: r2 = Instance_BoxShape
    //     0x9afca4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9afca8: ldr             x2, [x2, #0x970]
    // 0x9afcac: StoreField: r1->field_23 = r2
    //     0x9afcac: stur            w2, [x1, #0x23]
    // 0x9afcb0: r0 = SvgPicture()
    //     0x9afcb0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9afcb4: stur            x0, [fp, #-0x18]
    // 0x9afcb8: r16 = Instance_BoxFit
    //     0x9afcb8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x9afcbc: ldr             x16, [x16, #0xb18]
    // 0x9afcc0: str             x16, [SP]
    // 0x9afcc4: mov             x1, x0
    // 0x9afcc8: r2 = "assets/images/outline_creditcard.svg"
    //     0x9afcc8: add             x2, PP, #0x5b, lsl #12  ; [pp+0x5bae0] "assets/images/outline_creditcard.svg"
    //     0x9afccc: ldr             x2, [x2, #0xae0]
    // 0x9afcd0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x9afcd0: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x9afcd4: ldr             x4, [x4, #0xb0]
    // 0x9afcd8: r0 = SvgPicture.asset()
    //     0x9afcd8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9afcdc: r0 = Container()
    //     0x9afcdc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9afce0: stur            x0, [fp, #-0x28]
    // 0x9afce4: r16 = 50.000000
    //     0x9afce4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afce8: ldr             x16, [x16, #0xa90]
    // 0x9afcec: r30 = 50.000000
    //     0x9afcec: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afcf0: ldr             lr, [lr, #0xa90]
    // 0x9afcf4: stp             lr, x16, [SP, #0x10]
    // 0x9afcf8: ldur            x16, [fp, #-0x20]
    // 0x9afcfc: ldur            lr, [fp, #-0x18]
    // 0x9afd00: stp             lr, x16, [SP]
    // 0x9afd04: mov             x1, x0
    // 0x9afd08: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x9afd08: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x9afd0c: ldr             x4, [x4, #0x870]
    // 0x9afd10: r0 = Container()
    //     0x9afd10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9afd14: r0 = Padding()
    //     0x9afd14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9afd18: r3 = Instance_EdgeInsets
    //     0x9afd18: add             x3, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x9afd1c: ldr             x3, [x3, #0x868]
    // 0x9afd20: StoreField: r0->field_f = r3
    //     0x9afd20: stur            w3, [x0, #0xf]
    // 0x9afd24: ldur            x1, [fp, #-0x28]
    // 0x9afd28: StoreField: r0->field_b = r1
    //     0x9afd28: stur            w1, [x0, #0xb]
    // 0x9afd2c: b               #0x9afeb4
    // 0x9afd30: ldur            x4, [fp, #-8]
    // 0x9afd34: r2 = Instance_BoxShape
    //     0x9afd34: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9afd38: ldr             x2, [x2, #0x970]
    // 0x9afd3c: r3 = Instance_EdgeInsets
    //     0x9afd3c: add             x3, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x9afd40: ldr             x3, [x3, #0x868]
    // 0x9afd44: LoadField: r0 = r4->field_f
    //     0x9afd44: ldur            w0, [x4, #0xf]
    // 0x9afd48: DecompressPointer r0
    //     0x9afd48: add             x0, x0, HEAP, lsl #32
    // 0x9afd4c: LoadField: r1 = r0->field_b
    //     0x9afd4c: ldur            w1, [x0, #0xb]
    // 0x9afd50: DecompressPointer r1
    //     0x9afd50: add             x1, x1, HEAP, lsl #32
    // 0x9afd54: cmp             w1, NULL
    // 0x9afd58: b.eq            #0x9b020c
    // 0x9afd5c: LoadField: r0 = r1->field_b
    //     0x9afd5c: ldur            w0, [x1, #0xb]
    // 0x9afd60: DecompressPointer r0
    //     0x9afd60: add             x0, x0, HEAP, lsl #32
    // 0x9afd64: LoadField: r5 = r0->field_1b
    //     0x9afd64: ldur            w5, [x0, #0x1b]
    // 0x9afd68: DecompressPointer r5
    //     0x9afd68: add             x5, x5, HEAP, lsl #32
    // 0x9afd6c: cmp             w5, NULL
    // 0x9afd70: b.ne            #0x9afd80
    // 0x9afd74: ldr             x6, [fp, #0x10]
    // 0x9afd78: r0 = Null
    //     0x9afd78: mov             x0, NULL
    // 0x9afd7c: b               #0x9afdc4
    // 0x9afd80: ldr             x6, [fp, #0x10]
    // 0x9afd84: LoadField: r0 = r5->field_b
    //     0x9afd84: ldur            w0, [x5, #0xb]
    // 0x9afd88: r7 = LoadInt32Instr(r6)
    //     0x9afd88: sbfx            x7, x6, #1, #0x1f
    //     0x9afd8c: tbz             w6, #0, #0x9afd94
    //     0x9afd90: ldur            x7, [x6, #7]
    // 0x9afd94: r1 = LoadInt32Instr(r0)
    //     0x9afd94: sbfx            x1, x0, #1, #0x1f
    // 0x9afd98: mov             x0, x1
    // 0x9afd9c: mov             x1, x7
    // 0x9afda0: cmp             x1, x0
    // 0x9afda4: b.hs            #0x9b0210
    // 0x9afda8: LoadField: r0 = r5->field_f
    //     0x9afda8: ldur            w0, [x5, #0xf]
    // 0x9afdac: DecompressPointer r0
    //     0x9afdac: add             x0, x0, HEAP, lsl #32
    // 0x9afdb0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x9afdb0: add             x16, x0, x7, lsl #2
    //     0x9afdb4: ldur            w1, [x16, #0xf]
    // 0x9afdb8: DecompressPointer r1
    //     0x9afdb8: add             x1, x1, HEAP, lsl #32
    // 0x9afdbc: LoadField: r0 = r1->field_1f
    //     0x9afdbc: ldur            w0, [x1, #0x1f]
    // 0x9afdc0: DecompressPointer r0
    //     0x9afdc0: add             x0, x0, HEAP, lsl #32
    // 0x9afdc4: r1 = LoadClassIdInstr(r0)
    //     0x9afdc4: ldur            x1, [x0, #-1]
    //     0x9afdc8: ubfx            x1, x1, #0xc, #0x14
    // 0x9afdcc: r16 = "return"
    //     0x9afdcc: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x9afdd0: ldr             x16, [x16, #0x9b8]
    // 0x9afdd4: stp             x16, x0, [SP]
    // 0x9afdd8: mov             x0, x1
    // 0x9afddc: mov             lr, x0
    // 0x9afde0: ldr             lr, [x21, lr, lsl #3]
    // 0x9afde4: blr             lr
    // 0x9afde8: tbnz            w0, #4, #0x9afeb0
    // 0x9afdec: ldr             x1, [fp, #0x18]
    // 0x9afdf0: r0 = of()
    //     0x9afdf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9afdf4: LoadField: r1 = r0->field_5b
    //     0x9afdf4: ldur            w1, [x0, #0x5b]
    // 0x9afdf8: DecompressPointer r1
    //     0x9afdf8: add             x1, x1, HEAP, lsl #32
    // 0x9afdfc: stur            x1, [fp, #-0x18]
    // 0x9afe00: r0 = BoxDecoration()
    //     0x9afe00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9afe04: mov             x1, x0
    // 0x9afe08: ldur            x0, [fp, #-0x18]
    // 0x9afe0c: stur            x1, [fp, #-0x20]
    // 0x9afe10: StoreField: r1->field_7 = r0
    //     0x9afe10: stur            w0, [x1, #7]
    // 0x9afe14: r0 = Instance_BoxShape
    //     0x9afe14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x9afe18: ldr             x0, [x0, #0x970]
    // 0x9afe1c: StoreField: r1->field_23 = r0
    //     0x9afe1c: stur            w0, [x1, #0x23]
    // 0x9afe20: r0 = SvgPicture()
    //     0x9afe20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x9afe24: stur            x0, [fp, #-0x18]
    // 0x9afe28: r16 = "return order"
    //     0x9afe28: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x9afe2c: ldr             x16, [x16, #0xc78]
    // 0x9afe30: r30 = Instance_BoxFit
    //     0x9afe30: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x9afe34: ldr             lr, [lr, #0xb18]
    // 0x9afe38: stp             lr, x16, [SP]
    // 0x9afe3c: mov             x1, x0
    // 0x9afe40: r2 = "assets/images/return_order.svg"
    //     0x9afe40: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c80] "assets/images/return_order.svg"
    //     0x9afe44: ldr             x2, [x2, #0xc80]
    // 0x9afe48: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x9afe48: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x9afe4c: ldr             x4, [x4, #0xb28]
    // 0x9afe50: r0 = SvgPicture.asset()
    //     0x9afe50: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x9afe54: r0 = Container()
    //     0x9afe54: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9afe58: stur            x0, [fp, #-0x28]
    // 0x9afe5c: r16 = 50.000000
    //     0x9afe5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afe60: ldr             x16, [x16, #0xa90]
    // 0x9afe64: r30 = 50.000000
    //     0x9afe64: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9afe68: ldr             lr, [lr, #0xa90]
    // 0x9afe6c: stp             lr, x16, [SP, #0x10]
    // 0x9afe70: ldur            x16, [fp, #-0x20]
    // 0x9afe74: ldur            lr, [fp, #-0x18]
    // 0x9afe78: stp             lr, x16, [SP]
    // 0x9afe7c: mov             x1, x0
    // 0x9afe80: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x9afe80: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x9afe84: ldr             x4, [x4, #0x870]
    // 0x9afe88: r0 = Container()
    //     0x9afe88: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9afe8c: r0 = Padding()
    //     0x9afe8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9afe90: mov             x1, x0
    // 0x9afe94: r0 = Instance_EdgeInsets
    //     0x9afe94: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x9afe98: ldr             x0, [x0, #0x868]
    // 0x9afe9c: StoreField: r1->field_f = r0
    //     0x9afe9c: stur            w0, [x1, #0xf]
    // 0x9afea0: ldur            x0, [fp, #-0x28]
    // 0x9afea4: StoreField: r1->field_b = r0
    //     0x9afea4: stur            w0, [x1, #0xb]
    // 0x9afea8: mov             x0, x1
    // 0x9afeac: b               #0x9afeb4
    // 0x9afeb0: ldur            x0, [fp, #-0x10]
    // 0x9afeb4: mov             x3, x0
    // 0x9afeb8: ldur            x0, [fp, #-8]
    // 0x9afebc: stur            x3, [fp, #-0x10]
    // 0x9afec0: LoadField: r1 = r0->field_f
    //     0x9afec0: ldur            w1, [x0, #0xf]
    // 0x9afec4: DecompressPointer r1
    //     0x9afec4: add             x1, x1, HEAP, lsl #32
    // 0x9afec8: LoadField: r2 = r1->field_b
    //     0x9afec8: ldur            w2, [x1, #0xb]
    // 0x9afecc: DecompressPointer r2
    //     0x9afecc: add             x2, x2, HEAP, lsl #32
    // 0x9afed0: cmp             w2, NULL
    // 0x9afed4: b.eq            #0x9b0214
    // 0x9afed8: r16 = 1.000000
    //     0x9afed8: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x9afedc: str             x16, [SP]
    // 0x9afee0: r1 = Null
    //     0x9afee0: mov             x1, NULL
    // 0x9afee4: r2 = Instance_Color
    //     0x9afee4: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x9afee8: ldr             x2, [x2, #0xf88]
    // 0x9afeec: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x9afeec: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x9afef0: ldr             x4, [x4, #0x108]
    // 0x9afef4: r0 = Border.all()
    //     0x9afef4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x9afef8: stur            x0, [fp, #-0x18]
    // 0x9afefc: r0 = BoxDecoration()
    //     0x9afefc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9aff00: mov             x2, x0
    // 0x9aff04: ldur            x0, [fp, #-0x18]
    // 0x9aff08: stur            x2, [fp, #-0x20]
    // 0x9aff0c: StoreField: r2->field_f = r0
    //     0x9aff0c: stur            w0, [x2, #0xf]
    // 0x9aff10: r3 = Instance_BoxShape
    //     0x9aff10: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9aff14: ldr             x3, [x3, #0x80]
    // 0x9aff18: StoreField: r2->field_23 = r3
    //     0x9aff18: stur            w3, [x2, #0x23]
    // 0x9aff1c: ldur            x4, [fp, #-8]
    // 0x9aff20: LoadField: r0 = r4->field_f
    //     0x9aff20: ldur            w0, [x4, #0xf]
    // 0x9aff24: DecompressPointer r0
    //     0x9aff24: add             x0, x0, HEAP, lsl #32
    // 0x9aff28: LoadField: r1 = r0->field_b
    //     0x9aff28: ldur            w1, [x0, #0xb]
    // 0x9aff2c: DecompressPointer r1
    //     0x9aff2c: add             x1, x1, HEAP, lsl #32
    // 0x9aff30: cmp             w1, NULL
    // 0x9aff34: b.eq            #0x9b0218
    // 0x9aff38: LoadField: r0 = r1->field_b
    //     0x9aff38: ldur            w0, [x1, #0xb]
    // 0x9aff3c: DecompressPointer r0
    //     0x9aff3c: add             x0, x0, HEAP, lsl #32
    // 0x9aff40: LoadField: r5 = r0->field_1b
    //     0x9aff40: ldur            w5, [x0, #0x1b]
    // 0x9aff44: DecompressPointer r5
    //     0x9aff44: add             x5, x5, HEAP, lsl #32
    // 0x9aff48: cmp             w5, NULL
    // 0x9aff4c: b.ne            #0x9aff5c
    // 0x9aff50: ldr             x6, [fp, #0x10]
    // 0x9aff54: r0 = Null
    //     0x9aff54: mov             x0, NULL
    // 0x9aff58: b               #0x9affa0
    // 0x9aff5c: ldr             x6, [fp, #0x10]
    // 0x9aff60: LoadField: r0 = r5->field_b
    //     0x9aff60: ldur            w0, [x5, #0xb]
    // 0x9aff64: r7 = LoadInt32Instr(r6)
    //     0x9aff64: sbfx            x7, x6, #1, #0x1f
    //     0x9aff68: tbz             w6, #0, #0x9aff70
    //     0x9aff6c: ldur            x7, [x6, #7]
    // 0x9aff70: r1 = LoadInt32Instr(r0)
    //     0x9aff70: sbfx            x1, x0, #1, #0x1f
    // 0x9aff74: mov             x0, x1
    // 0x9aff78: mov             x1, x7
    // 0x9aff7c: cmp             x1, x0
    // 0x9aff80: b.hs            #0x9b021c
    // 0x9aff84: LoadField: r0 = r5->field_f
    //     0x9aff84: ldur            w0, [x5, #0xf]
    // 0x9aff88: DecompressPointer r0
    //     0x9aff88: add             x0, x0, HEAP, lsl #32
    // 0x9aff8c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x9aff8c: add             x16, x0, x7, lsl #2
    //     0x9aff90: ldur            w1, [x16, #0xf]
    // 0x9aff94: DecompressPointer r1
    //     0x9aff94: add             x1, x1, HEAP, lsl #32
    // 0x9aff98: LoadField: r0 = r1->field_43
    //     0x9aff98: ldur            w0, [x1, #0x43]
    // 0x9aff9c: DecompressPointer r0
    //     0x9aff9c: add             x0, x0, HEAP, lsl #32
    // 0x9affa0: cmp             w0, NULL
    // 0x9affa4: b.ne            #0x9affac
    // 0x9affa8: r0 = ""
    //     0x9affa8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9affac: ldr             x1, [fp, #0x18]
    // 0x9affb0: stur            x0, [fp, #-0x18]
    // 0x9affb4: r0 = of()
    //     0x9affb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9affb8: LoadField: r1 = r0->field_87
    //     0x9affb8: ldur            w1, [x0, #0x87]
    // 0x9affbc: DecompressPointer r1
    //     0x9affbc: add             x1, x1, HEAP, lsl #32
    // 0x9affc0: LoadField: r0 = r1->field_2b
    //     0x9affc0: ldur            w0, [x1, #0x2b]
    // 0x9affc4: DecompressPointer r0
    //     0x9affc4: add             x0, x0, HEAP, lsl #32
    // 0x9affc8: r16 = 12.000000
    //     0x9affc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9affcc: ldr             x16, [x16, #0x9e8]
    // 0x9affd0: r30 = Instance_Color
    //     0x9affd0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9affd4: stp             lr, x16, [SP]
    // 0x9affd8: mov             x1, x0
    // 0x9affdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9affdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9affe0: ldr             x4, [x4, #0xaa0]
    // 0x9affe4: r0 = copyWith()
    //     0x9affe4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9affe8: stur            x0, [fp, #-0x28]
    // 0x9affec: r0 = Text()
    //     0x9affec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9afff0: mov             x2, x0
    // 0x9afff4: ldur            x0, [fp, #-0x18]
    // 0x9afff8: stur            x2, [fp, #-0x30]
    // 0x9afffc: StoreField: r2->field_b = r0
    //     0x9afffc: stur            w0, [x2, #0xb]
    // 0x9b0000: ldur            x0, [fp, #-0x28]
    // 0x9b0004: StoreField: r2->field_13 = r0
    //     0x9b0004: stur            w0, [x2, #0x13]
    // 0x9b0008: ldur            x3, [fp, #-8]
    // 0x9b000c: LoadField: r0 = r3->field_f
    //     0x9b000c: ldur            w0, [x3, #0xf]
    // 0x9b0010: DecompressPointer r0
    //     0x9b0010: add             x0, x0, HEAP, lsl #32
    // 0x9b0014: LoadField: r1 = r0->field_b
    //     0x9b0014: ldur            w1, [x0, #0xb]
    // 0x9b0018: DecompressPointer r1
    //     0x9b0018: add             x1, x1, HEAP, lsl #32
    // 0x9b001c: cmp             w1, NULL
    // 0x9b0020: b.eq            #0x9b0220
    // 0x9b0024: LoadField: r0 = r1->field_b
    //     0x9b0024: ldur            w0, [x1, #0xb]
    // 0x9b0028: DecompressPointer r0
    //     0x9b0028: add             x0, x0, HEAP, lsl #32
    // 0x9b002c: LoadField: r4 = r0->field_1b
    //     0x9b002c: ldur            w4, [x0, #0x1b]
    // 0x9b0030: DecompressPointer r4
    //     0x9b0030: add             x4, x4, HEAP, lsl #32
    // 0x9b0034: cmp             w4, NULL
    // 0x9b0038: b.ne            #0x9b0044
    // 0x9b003c: r0 = Null
    //     0x9b003c: mov             x0, NULL
    // 0x9b0040: b               #0x9b0084
    // 0x9b0044: ldr             x0, [fp, #0x10]
    // 0x9b0048: LoadField: r1 = r4->field_b
    //     0x9b0048: ldur            w1, [x4, #0xb]
    // 0x9b004c: r5 = LoadInt32Instr(r0)
    //     0x9b004c: sbfx            x5, x0, #1, #0x1f
    //     0x9b0050: tbz             w0, #0, #0x9b0058
    //     0x9b0054: ldur            x5, [x0, #7]
    // 0x9b0058: r0 = LoadInt32Instr(r1)
    //     0x9b0058: sbfx            x0, x1, #1, #0x1f
    // 0x9b005c: mov             x1, x5
    // 0x9b0060: cmp             x1, x0
    // 0x9b0064: b.hs            #0x9b0224
    // 0x9b0068: LoadField: r0 = r4->field_f
    //     0x9b0068: ldur            w0, [x4, #0xf]
    // 0x9b006c: DecompressPointer r0
    //     0x9b006c: add             x0, x0, HEAP, lsl #32
    // 0x9b0070: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9b0070: add             x16, x0, x5, lsl #2
    //     0x9b0074: ldur            w1, [x16, #0xf]
    // 0x9b0078: DecompressPointer r1
    //     0x9b0078: add             x1, x1, HEAP, lsl #32
    // 0x9b007c: LoadField: r0 = r1->field_1f
    //     0x9b007c: ldur            w0, [x1, #0x1f]
    // 0x9b0080: DecompressPointer r0
    //     0x9b0080: add             x0, x0, HEAP, lsl #32
    // 0x9b0084: r1 = LoadClassIdInstr(r0)
    //     0x9b0084: ldur            x1, [x0, #-1]
    //     0x9b0088: ubfx            x1, x1, #0xc, #0x14
    // 0x9b008c: r16 = "return"
    //     0x9b008c: add             x16, PP, #0x32, lsl #12  ; [pp+0x329b8] "return"
    //     0x9b0090: ldr             x16, [x16, #0x9b8]
    // 0x9b0094: stp             x16, x0, [SP]
    // 0x9b0098: mov             x0, x1
    // 0x9b009c: mov             lr, x0
    // 0x9b00a0: ldr             lr, [x21, lr, lsl #3]
    // 0x9b00a4: blr             lr
    // 0x9b00a8: tbnz            w0, #4, #0x9b0118
    // 0x9b00ac: ldr             x1, [fp, #0x18]
    // 0x9b00b0: r0 = of()
    //     0x9b00b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9b00b4: LoadField: r1 = r0->field_87
    //     0x9b00b4: ldur            w1, [x0, #0x87]
    // 0x9b00b8: DecompressPointer r1
    //     0x9b00b8: add             x1, x1, HEAP, lsl #32
    // 0x9b00bc: LoadField: r0 = r1->field_7
    //     0x9b00bc: ldur            w0, [x1, #7]
    // 0x9b00c0: DecompressPointer r0
    //     0x9b00c0: add             x0, x0, HEAP, lsl #32
    // 0x9b00c4: r16 = 12.000000
    //     0x9b00c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9b00c8: ldr             x16, [x16, #0x9e8]
    // 0x9b00cc: r30 = Instance_Color
    //     0x9b00cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9b00d0: stp             lr, x16, [SP, #8]
    // 0x9b00d4: r16 = Instance_TextDecoration
    //     0x9b00d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0x9b00d8: ldr             x16, [x16, #0x10]
    // 0x9b00dc: str             x16, [SP]
    // 0x9b00e0: mov             x1, x0
    // 0x9b00e4: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0x9b00e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0x9b00e8: ldr             x4, [x4, #0xe38]
    // 0x9b00ec: r0 = copyWith()
    //     0x9b00ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9b00f0: stur            x0, [fp, #-0x18]
    // 0x9b00f4: r0 = Text()
    //     0x9b00f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9b00f8: mov             x1, x0
    // 0x9b00fc: r0 = "Know more"
    //     0x9b00fc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0x9b0100: ldr             x0, [x0, #0x20]
    // 0x9b0104: StoreField: r1->field_b = r0
    //     0x9b0104: stur            w0, [x1, #0xb]
    // 0x9b0108: ldur            x0, [fp, #-0x18]
    // 0x9b010c: StoreField: r1->field_13 = r0
    //     0x9b010c: stur            w0, [x1, #0x13]
    // 0x9b0110: mov             x2, x1
    // 0x9b0114: b               #0x9b0120
    // 0x9b0118: r2 = Instance_Text
    //     0x9b0118: add             x2, PP, #0x56, lsl #12  ; [pp+0x56ea8] Obj!Text@d659d1
    //     0x9b011c: ldr             x2, [x2, #0xea8]
    // 0x9b0120: ldur            x1, [fp, #-0x10]
    // 0x9b0124: ldur            x0, [fp, #-0x30]
    // 0x9b0128: stur            x2, [fp, #-0x18]
    // 0x9b012c: r0 = InkWell()
    //     0x9b012c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x9b0130: mov             x3, x0
    // 0x9b0134: ldur            x0, [fp, #-0x18]
    // 0x9b0138: stur            x3, [fp, #-0x28]
    // 0x9b013c: StoreField: r3->field_b = r0
    //     0x9b013c: stur            w0, [x3, #0xb]
    // 0x9b0140: ldur            x2, [fp, #-8]
    // 0x9b0144: r1 = Function '<anonymous closure>':.
    //     0x9b0144: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bae8] AnonymousClosure: (0x9b0228), in [package:customer_app/app/presentation/custom_widgets/product_detail/trust_markers_item_view.dart] _TrustMarkersItemViewState::build (0x9af87c)
    //     0x9b0148: ldr             x1, [x1, #0xae8]
    // 0x9b014c: r0 = AllocateClosure()
    //     0x9b014c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9b0150: mov             x1, x0
    // 0x9b0154: ldur            x0, [fp, #-0x28]
    // 0x9b0158: StoreField: r0->field_f = r1
    //     0x9b0158: stur            w1, [x0, #0xf]
    // 0x9b015c: r1 = true
    //     0x9b015c: add             x1, NULL, #0x20  ; true
    // 0x9b0160: StoreField: r0->field_43 = r1
    //     0x9b0160: stur            w1, [x0, #0x43]
    // 0x9b0164: r2 = Instance_BoxShape
    //     0x9b0164: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9b0168: ldr             x2, [x2, #0x80]
    // 0x9b016c: StoreField: r0->field_47 = r2
    //     0x9b016c: stur            w2, [x0, #0x47]
    // 0x9b0170: StoreField: r0->field_6f = r1
    //     0x9b0170: stur            w1, [x0, #0x6f]
    // 0x9b0174: r2 = false
    //     0x9b0174: add             x2, NULL, #0x30  ; false
    // 0x9b0178: StoreField: r0->field_73 = r2
    //     0x9b0178: stur            w2, [x0, #0x73]
    // 0x9b017c: StoreField: r0->field_83 = r1
    //     0x9b017c: stur            w1, [x0, #0x83]
    // 0x9b0180: StoreField: r0->field_7b = r2
    //     0x9b0180: stur            w2, [x0, #0x7b]
    // 0x9b0184: r0 = ListTile()
    //     0x9b0184: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0x9b0188: mov             x1, x0
    // 0x9b018c: ldur            x0, [fp, #-0x10]
    // 0x9b0190: stur            x1, [fp, #-8]
    // 0x9b0194: StoreField: r1->field_b = r0
    //     0x9b0194: stur            w0, [x1, #0xb]
    // 0x9b0198: ldur            x0, [fp, #-0x30]
    // 0x9b019c: StoreField: r1->field_f = r0
    //     0x9b019c: stur            w0, [x1, #0xf]
    // 0x9b01a0: ldur            x0, [fp, #-0x28]
    // 0x9b01a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x9b01a4: stur            w0, [x1, #0x17]
    // 0x9b01a8: r0 = true
    //     0x9b01a8: add             x0, NULL, #0x20  ; true
    // 0x9b01ac: StoreField: r1->field_4b = r0
    //     0x9b01ac: stur            w0, [x1, #0x4b]
    // 0x9b01b0: r2 = false
    //     0x9b01b0: add             x2, NULL, #0x30  ; false
    // 0x9b01b4: StoreField: r1->field_5f = r2
    //     0x9b01b4: stur            w2, [x1, #0x5f]
    // 0x9b01b8: StoreField: r1->field_73 = r2
    //     0x9b01b8: stur            w2, [x1, #0x73]
    // 0x9b01bc: StoreField: r1->field_97 = r0
    //     0x9b01bc: stur            w0, [x1, #0x97]
    // 0x9b01c0: r0 = Container()
    //     0x9b01c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9b01c4: stur            x0, [fp, #-0x10]
    // 0x9b01c8: ldur            x16, [fp, #-0x20]
    // 0x9b01cc: ldur            lr, [fp, #-8]
    // 0x9b01d0: stp             lr, x16, [SP]
    // 0x9b01d4: mov             x1, x0
    // 0x9b01d8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x9b01d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x9b01dc: ldr             x4, [x4, #0x88]
    // 0x9b01e0: r0 = Container()
    //     0x9b01e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9b01e4: ldur            x0, [fp, #-0x10]
    // 0x9b01e8: LeaveFrame
    //     0x9b01e8: mov             SP, fp
    //     0x9b01ec: ldp             fp, lr, [SP], #0x10
    // 0x9b01f0: ret
    //     0x9b01f0: ret             
    // 0x9b01f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b01f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b01f8: b               #0x9afa40
    // 0x9b01fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b01fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b0200: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b0200: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b0204: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b0204: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b0208: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b0208: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b020c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b020c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b0210: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b0210: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b0214: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b0214: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b0218: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b0218: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b021c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b021c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9b0220: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b0220: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9b0224: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9b0224: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9b0228, size: 0x7c
    // 0x9b0228: EnterFrame
    //     0x9b0228: stp             fp, lr, [SP, #-0x10]!
    //     0x9b022c: mov             fp, SP
    // 0x9b0230: AllocStack(0x8)
    //     0x9b0230: sub             SP, SP, #8
    // 0x9b0234: SetupParameters()
    //     0x9b0234: ldr             x0, [fp, #0x10]
    //     0x9b0238: ldur            w1, [x0, #0x17]
    //     0x9b023c: add             x1, x1, HEAP, lsl #32
    // 0x9b0240: CheckStackOverflow
    //     0x9b0240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9b0244: cmp             SP, x16
    //     0x9b0248: b.ls            #0x9b0298
    // 0x9b024c: LoadField: r0 = r1->field_f
    //     0x9b024c: ldur            w0, [x1, #0xf]
    // 0x9b0250: DecompressPointer r0
    //     0x9b0250: add             x0, x0, HEAP, lsl #32
    // 0x9b0254: LoadField: r1 = r0->field_b
    //     0x9b0254: ldur            w1, [x0, #0xb]
    // 0x9b0258: DecompressPointer r1
    //     0x9b0258: add             x1, x1, HEAP, lsl #32
    // 0x9b025c: cmp             w1, NULL
    // 0x9b0260: b.eq            #0x9b02a0
    // 0x9b0264: LoadField: r0 = r1->field_1f
    //     0x9b0264: ldur            w0, [x1, #0x1f]
    // 0x9b0268: DecompressPointer r0
    //     0x9b0268: add             x0, x0, HEAP, lsl #32
    // 0x9b026c: str             x0, [SP]
    // 0x9b0270: r4 = 0
    //     0x9b0270: movz            x4, #0
    // 0x9b0274: ldr             x0, [SP]
    // 0x9b0278: r16 = UnlinkedCall_0x613b5c
    //     0x9b0278: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5baf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9b027c: add             x16, x16, #0xaf0
    // 0x9b0280: ldp             x5, lr, [x16]
    // 0x9b0284: blr             lr
    // 0x9b0288: r0 = Null
    //     0x9b0288: mov             x0, NULL
    // 0x9b028c: LeaveFrame
    //     0x9b028c: mov             SP, fp
    //     0x9b0290: ldp             fp, lr, [SP], #0x10
    // 0x9b0294: ret
    //     0x9b0294: ret             
    // 0x9b0298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9b0298: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9b029c: b               #0x9b024c
    // 0x9b02a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9b02a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4299, size: 0x24, field offset: 0xc
//   const constructor, 
class TrustMarkersItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79eb8, size: 0x24
    // 0xc79eb8: EnterFrame
    //     0xc79eb8: stp             fp, lr, [SP, #-0x10]!
    //     0xc79ebc: mov             fp, SP
    // 0xc79ec0: mov             x0, x1
    // 0xc79ec4: r1 = <TrustMarkersItemView>
    //     0xc79ec4: add             x1, PP, #0x49, lsl #12  ; [pp+0x492a0] TypeArguments: <TrustMarkersItemView>
    //     0xc79ec8: ldr             x1, [x1, #0x2a0]
    // 0xc79ecc: r0 = _TrustMarkersItemViewState()
    //     0xc79ecc: bl              #0xc79edc  ; Allocate_TrustMarkersItemViewStateStub -> _TrustMarkersItemViewState (size=0x14)
    // 0xc79ed0: LeaveFrame
    //     0xc79ed0: mov             SP, fp
    //     0xc79ed4: ldp             fp, lr, [SP], #0x10
    // 0xc79ed8: ret
    //     0xc79ed8: ret             
  }
}
