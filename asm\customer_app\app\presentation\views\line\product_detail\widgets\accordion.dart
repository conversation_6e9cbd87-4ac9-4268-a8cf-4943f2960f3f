// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/accordion.dart

// class id: 1049552, size: 0x8
class :: {
}

// class id: 3231, size: 0x18, field offset: 0x14
class _AccordionState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x94a860, size: 0x2c
    // 0x94a860: r2 = false
    //     0x94a860: add             x2, NULL, #0x30  ; false
    // 0x94a864: LoadField: r3 = r1->field_b
    //     0x94a864: ldur            w3, [x1, #0xb]
    // 0x94a868: DecompressPointer r3
    //     0x94a868: add             x3, x3, HEAP, lsl #32
    // 0x94a86c: cmp             w3, NULL
    // 0x94a870: b.eq            #0x94a880
    // 0x94a874: StoreField: r1->field_13 = r2
    //     0x94a874: stur            w2, [x1, #0x13]
    // 0x94a878: r0 = Null
    //     0x94a878: mov             x0, NULL
    // 0x94a87c: ret
    //     0x94a87c: ret             
    // 0x94a880: EnterFrame
    //     0x94a880: stp             fp, lr, [SP, #-0x10]!
    //     0x94a884: mov             fp, SP
    // 0x94a888: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94a888: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbfd658, size: 0x3e0
    // 0xbfd658: EnterFrame
    //     0xbfd658: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd65c: mov             fp, SP
    // 0xbfd660: AllocStack(0x50)
    //     0xbfd660: sub             SP, SP, #0x50
    // 0xbfd664: SetupParameters(_AccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbfd664: mov             x0, x1
    //     0xbfd668: stur            x1, [fp, #-8]
    //     0xbfd66c: mov             x1, x2
    //     0xbfd670: stur            x2, [fp, #-0x10]
    // 0xbfd674: CheckStackOverflow
    //     0xbfd674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd678: cmp             SP, x16
    //     0xbfd67c: b.ls            #0xbfda10
    // 0xbfd680: r1 = 1
    //     0xbfd680: movz            x1, #0x1
    // 0xbfd684: r0 = AllocateContext()
    //     0xbfd684: bl              #0x16f6108  ; AllocateContextStub
    // 0xbfd688: mov             x2, x0
    // 0xbfd68c: ldur            x0, [fp, #-8]
    // 0xbfd690: stur            x2, [fp, #-0x28]
    // 0xbfd694: StoreField: r2->field_f = r0
    //     0xbfd694: stur            w0, [x2, #0xf]
    // 0xbfd698: LoadField: r1 = r0->field_b
    //     0xbfd698: ldur            w1, [x0, #0xb]
    // 0xbfd69c: DecompressPointer r1
    //     0xbfd69c: add             x1, x1, HEAP, lsl #32
    // 0xbfd6a0: cmp             w1, NULL
    // 0xbfd6a4: b.eq            #0xbfda18
    // 0xbfd6a8: LoadField: r3 = r1->field_b
    //     0xbfd6a8: ldur            w3, [x1, #0xb]
    // 0xbfd6ac: DecompressPointer r3
    //     0xbfd6ac: add             x3, x3, HEAP, lsl #32
    // 0xbfd6b0: stur            x3, [fp, #-0x20]
    // 0xbfd6b4: LoadField: r4 = r0->field_13
    //     0xbfd6b4: ldur            w4, [x0, #0x13]
    // 0xbfd6b8: DecompressPointer r4
    //     0xbfd6b8: add             x4, x4, HEAP, lsl #32
    // 0xbfd6bc: tbnz            w4, #4, #0xbfd6cc
    // 0xbfd6c0: r5 = Instance_IconData
    //     0xbfd6c0: add             x5, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xbfd6c4: ldr             x5, [x5, #0x440]
    // 0xbfd6c8: b               #0xbfd6d4
    // 0xbfd6cc: r5 = Instance_IconData
    //     0xbfd6cc: add             x5, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xbfd6d0: ldr             x5, [x5, #0x448]
    // 0xbfd6d4: stur            x5, [fp, #-0x18]
    // 0xbfd6d8: LoadField: r6 = r1->field_1b
    //     0xbfd6d8: ldur            w6, [x1, #0x1b]
    // 0xbfd6dc: DecompressPointer r6
    //     0xbfd6dc: add             x6, x6, HEAP, lsl #32
    // 0xbfd6e0: cmp             w6, NULL
    // 0xbfd6e4: b.ne            #0xbfd6f0
    // 0xbfd6e8: d0 = 16.000000
    //     0xbfd6e8: fmov            d0, #16.00000000
    // 0xbfd6ec: b               #0xbfd6f4
    // 0xbfd6f0: LoadField: d0 = r6->field_7
    //     0xbfd6f0: ldur            d0, [x6, #7]
    // 0xbfd6f4: stur            d0, [fp, #-0x40]
    // 0xbfd6f8: tbnz            w4, #4, #0xbfd714
    // 0xbfd6fc: ldur            x1, [fp, #-0x10]
    // 0xbfd700: r0 = of()
    //     0xbfd700: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfd704: LoadField: r1 = r0->field_5b
    //     0xbfd704: ldur            w1, [x0, #0x5b]
    // 0xbfd708: DecompressPointer r1
    //     0xbfd708: add             x1, x1, HEAP, lsl #32
    // 0xbfd70c: mov             x3, x1
    // 0xbfd710: b               #0xbfd728
    // 0xbfd714: ldur            x1, [fp, #-0x10]
    // 0xbfd718: r0 = of()
    //     0xbfd718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfd71c: LoadField: r1 = r0->field_5b
    //     0xbfd71c: ldur            w1, [x0, #0x5b]
    // 0xbfd720: DecompressPointer r1
    //     0xbfd720: add             x1, x1, HEAP, lsl #32
    // 0xbfd724: mov             x3, x1
    // 0xbfd728: ldur            x0, [fp, #-8]
    // 0xbfd72c: ldur            x1, [fp, #-0x20]
    // 0xbfd730: ldur            x2, [fp, #-0x18]
    // 0xbfd734: ldur            d0, [fp, #-0x40]
    // 0xbfd738: stur            x3, [fp, #-0x10]
    // 0xbfd73c: r0 = Icon()
    //     0xbfd73c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbfd740: mov             x1, x0
    // 0xbfd744: ldur            x0, [fp, #-0x18]
    // 0xbfd748: stur            x1, [fp, #-0x30]
    // 0xbfd74c: StoreField: r1->field_b = r0
    //     0xbfd74c: stur            w0, [x1, #0xb]
    // 0xbfd750: ldur            d0, [fp, #-0x40]
    // 0xbfd754: r0 = inline_Allocate_Double()
    //     0xbfd754: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbfd758: add             x0, x0, #0x10
    //     0xbfd75c: cmp             x2, x0
    //     0xbfd760: b.ls            #0xbfda1c
    //     0xbfd764: str             x0, [THR, #0x50]  ; THR::top
    //     0xbfd768: sub             x0, x0, #0xf
    //     0xbfd76c: movz            x2, #0xe15c
    //     0xbfd770: movk            x2, #0x3, lsl #16
    //     0xbfd774: stur            x2, [x0, #-1]
    // 0xbfd778: StoreField: r0->field_7 = d0
    //     0xbfd778: stur            d0, [x0, #7]
    // 0xbfd77c: StoreField: r1->field_f = r0
    //     0xbfd77c: stur            w0, [x1, #0xf]
    // 0xbfd780: ldur            x0, [fp, #-0x10]
    // 0xbfd784: StoreField: r1->field_23 = r0
    //     0xbfd784: stur            w0, [x1, #0x23]
    // 0xbfd788: ldur            x0, [fp, #-8]
    // 0xbfd78c: LoadField: r2 = r0->field_b
    //     0xbfd78c: ldur            w2, [x0, #0xb]
    // 0xbfd790: DecompressPointer r2
    //     0xbfd790: add             x2, x2, HEAP, lsl #32
    // 0xbfd794: stur            x2, [fp, #-0x10]
    // 0xbfd798: cmp             w2, NULL
    // 0xbfd79c: b.eq            #0xbfda34
    // 0xbfd7a0: r0 = RichTextIcon()
    //     0xbfd7a0: bl              #0xbf9e74  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xbfd7a4: mov             x1, x0
    // 0xbfd7a8: ldur            x0, [fp, #-0x30]
    // 0xbfd7ac: stur            x1, [fp, #-0x18]
    // 0xbfd7b0: StoreField: r1->field_b = r0
    //     0xbfd7b0: stur            w0, [x1, #0xb]
    // 0xbfd7b4: r0 = ""
    //     0xbfd7b4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfd7b8: StoreField: r1->field_f = r0
    //     0xbfd7b8: stur            w0, [x1, #0xf]
    // 0xbfd7bc: r0 = ListTile()
    //     0xbfd7bc: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xbfd7c0: mov             x3, x0
    // 0xbfd7c4: ldur            x0, [fp, #-0x20]
    // 0xbfd7c8: stur            x3, [fp, #-0x30]
    // 0xbfd7cc: StoreField: r3->field_f = r0
    //     0xbfd7cc: stur            w0, [x3, #0xf]
    // 0xbfd7d0: ldur            x0, [fp, #-0x18]
    // 0xbfd7d4: ArrayStore: r3[0] = r0  ; List_4
    //     0xbfd7d4: stur            w0, [x3, #0x17]
    // 0xbfd7d8: r0 = Instance_EdgeInsets
    //     0xbfd7d8: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbfd7dc: StoreField: r3->field_47 = r0
    //     0xbfd7dc: stur            w0, [x3, #0x47]
    // 0xbfd7e0: r0 = true
    //     0xbfd7e0: add             x0, NULL, #0x20  ; true
    // 0xbfd7e4: StoreField: r3->field_4b = r0
    //     0xbfd7e4: stur            w0, [x3, #0x4b]
    // 0xbfd7e8: ldur            x2, [fp, #-0x28]
    // 0xbfd7ec: r1 = Function '<anonymous closure>':.
    //     0xbfd7ec: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b48] AnonymousClosure: (0xbfda38), in [package:customer_app/app/presentation/views/line/product_detail/widgets/accordion.dart] _AccordionState::build (0xbfd658)
    //     0xbfd7f0: ldr             x1, [x1, #0xb48]
    // 0xbfd7f4: r0 = AllocateClosure()
    //     0xbfd7f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfd7f8: mov             x1, x0
    // 0xbfd7fc: ldur            x0, [fp, #-0x30]
    // 0xbfd800: StoreField: r0->field_4f = r1
    //     0xbfd800: stur            w1, [x0, #0x4f]
    // 0xbfd804: r1 = false
    //     0xbfd804: add             x1, NULL, #0x30  ; false
    // 0xbfd808: StoreField: r0->field_5f = r1
    //     0xbfd808: stur            w1, [x0, #0x5f]
    // 0xbfd80c: StoreField: r0->field_73 = r1
    //     0xbfd80c: stur            w1, [x0, #0x73]
    // 0xbfd810: r1 = 0.000000
    //     0xbfd810: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbfd814: StoreField: r0->field_8b = r1
    //     0xbfd814: stur            w1, [x0, #0x8b]
    // 0xbfd818: r1 = true
    //     0xbfd818: add             x1, NULL, #0x20  ; true
    // 0xbfd81c: StoreField: r0->field_97 = r1
    //     0xbfd81c: stur            w1, [x0, #0x97]
    // 0xbfd820: r1 = Null
    //     0xbfd820: mov             x1, NULL
    // 0xbfd824: r2 = 2
    //     0xbfd824: movz            x2, #0x2
    // 0xbfd828: r0 = AllocateArray()
    //     0xbfd828: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfd82c: mov             x2, x0
    // 0xbfd830: ldur            x0, [fp, #-0x30]
    // 0xbfd834: stur            x2, [fp, #-0x18]
    // 0xbfd838: StoreField: r2->field_f = r0
    //     0xbfd838: stur            w0, [x2, #0xf]
    // 0xbfd83c: r1 = <Widget>
    //     0xbfd83c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfd840: r0 = AllocateGrowableArray()
    //     0xbfd840: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfd844: mov             x1, x0
    // 0xbfd848: ldur            x0, [fp, #-0x18]
    // 0xbfd84c: stur            x1, [fp, #-0x20]
    // 0xbfd850: StoreField: r1->field_f = r0
    //     0xbfd850: stur            w0, [x1, #0xf]
    // 0xbfd854: r0 = 2
    //     0xbfd854: movz            x0, #0x2
    // 0xbfd858: StoreField: r1->field_b = r0
    //     0xbfd858: stur            w0, [x1, #0xb]
    // 0xbfd85c: ldur            x0, [fp, #-8]
    // 0xbfd860: LoadField: r2 = r0->field_13
    //     0xbfd860: ldur            w2, [x0, #0x13]
    // 0xbfd864: DecompressPointer r2
    //     0xbfd864: add             x2, x2, HEAP, lsl #32
    // 0xbfd868: tbnz            w2, #4, #0xbfd920
    // 0xbfd86c: ldur            x0, [fp, #-0x10]
    // 0xbfd870: LoadField: r2 = r0->field_13
    //     0xbfd870: ldur            w2, [x0, #0x13]
    // 0xbfd874: DecompressPointer r2
    //     0xbfd874: add             x2, x2, HEAP, lsl #32
    // 0xbfd878: stur            x2, [fp, #-8]
    // 0xbfd87c: r0 = Container()
    //     0xbfd87c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfd880: stur            x0, [fp, #-0x10]
    // 0xbfd884: r16 = Instance_EdgeInsets
    //     0xbfd884: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbfd888: ldr             x16, [x16, #0xf30]
    // 0xbfd88c: ldur            lr, [fp, #-8]
    // 0xbfd890: stp             lr, x16, [SP]
    // 0xbfd894: mov             x1, x0
    // 0xbfd898: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xbfd898: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xbfd89c: ldr             x4, [x4, #0x30]
    // 0xbfd8a0: r0 = Container()
    //     0xbfd8a0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfd8a4: ldur            x0, [fp, #-0x20]
    // 0xbfd8a8: LoadField: r1 = r0->field_b
    //     0xbfd8a8: ldur            w1, [x0, #0xb]
    // 0xbfd8ac: LoadField: r2 = r0->field_f
    //     0xbfd8ac: ldur            w2, [x0, #0xf]
    // 0xbfd8b0: DecompressPointer r2
    //     0xbfd8b0: add             x2, x2, HEAP, lsl #32
    // 0xbfd8b4: LoadField: r3 = r2->field_b
    //     0xbfd8b4: ldur            w3, [x2, #0xb]
    // 0xbfd8b8: r2 = LoadInt32Instr(r1)
    //     0xbfd8b8: sbfx            x2, x1, #1, #0x1f
    // 0xbfd8bc: stur            x2, [fp, #-0x38]
    // 0xbfd8c0: r1 = LoadInt32Instr(r3)
    //     0xbfd8c0: sbfx            x1, x3, #1, #0x1f
    // 0xbfd8c4: cmp             x2, x1
    // 0xbfd8c8: b.ne            #0xbfd8d4
    // 0xbfd8cc: mov             x1, x0
    // 0xbfd8d0: r0 = _growToNextCapacity()
    //     0xbfd8d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfd8d4: ldur            x2, [fp, #-0x20]
    // 0xbfd8d8: ldur            x3, [fp, #-0x38]
    // 0xbfd8dc: add             x0, x3, #1
    // 0xbfd8e0: lsl             x1, x0, #1
    // 0xbfd8e4: StoreField: r2->field_b = r1
    //     0xbfd8e4: stur            w1, [x2, #0xb]
    // 0xbfd8e8: LoadField: r1 = r2->field_f
    //     0xbfd8e8: ldur            w1, [x2, #0xf]
    // 0xbfd8ec: DecompressPointer r1
    //     0xbfd8ec: add             x1, x1, HEAP, lsl #32
    // 0xbfd8f0: ldur            x0, [fp, #-0x10]
    // 0xbfd8f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfd8f4: add             x25, x1, x3, lsl #2
    //     0xbfd8f8: add             x25, x25, #0xf
    //     0xbfd8fc: str             w0, [x25]
    //     0xbfd900: tbz             w0, #0, #0xbfd91c
    //     0xbfd904: ldurb           w16, [x1, #-1]
    //     0xbfd908: ldurb           w17, [x0, #-1]
    //     0xbfd90c: and             x16, x17, x16, lsr #2
    //     0xbfd910: tst             x16, HEAP, lsr #32
    //     0xbfd914: b.eq            #0xbfd91c
    //     0xbfd918: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfd91c: b               #0xbfd9b0
    // 0xbfd920: mov             x2, x1
    // 0xbfd924: r0 = Container()
    //     0xbfd924: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfd928: mov             x1, x0
    // 0xbfd92c: stur            x0, [fp, #-8]
    // 0xbfd930: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbfd930: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbfd934: r0 = Container()
    //     0xbfd934: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfd938: ldur            x0, [fp, #-0x20]
    // 0xbfd93c: LoadField: r1 = r0->field_b
    //     0xbfd93c: ldur            w1, [x0, #0xb]
    // 0xbfd940: LoadField: r2 = r0->field_f
    //     0xbfd940: ldur            w2, [x0, #0xf]
    // 0xbfd944: DecompressPointer r2
    //     0xbfd944: add             x2, x2, HEAP, lsl #32
    // 0xbfd948: LoadField: r3 = r2->field_b
    //     0xbfd948: ldur            w3, [x2, #0xb]
    // 0xbfd94c: r2 = LoadInt32Instr(r1)
    //     0xbfd94c: sbfx            x2, x1, #1, #0x1f
    // 0xbfd950: stur            x2, [fp, #-0x38]
    // 0xbfd954: r1 = LoadInt32Instr(r3)
    //     0xbfd954: sbfx            x1, x3, #1, #0x1f
    // 0xbfd958: cmp             x2, x1
    // 0xbfd95c: b.ne            #0xbfd968
    // 0xbfd960: mov             x1, x0
    // 0xbfd964: r0 = _growToNextCapacity()
    //     0xbfd964: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbfd968: ldur            x2, [fp, #-0x20]
    // 0xbfd96c: ldur            x3, [fp, #-0x38]
    // 0xbfd970: add             x0, x3, #1
    // 0xbfd974: lsl             x1, x0, #1
    // 0xbfd978: StoreField: r2->field_b = r1
    //     0xbfd978: stur            w1, [x2, #0xb]
    // 0xbfd97c: LoadField: r1 = r2->field_f
    //     0xbfd97c: ldur            w1, [x2, #0xf]
    // 0xbfd980: DecompressPointer r1
    //     0xbfd980: add             x1, x1, HEAP, lsl #32
    // 0xbfd984: ldur            x0, [fp, #-8]
    // 0xbfd988: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbfd988: add             x25, x1, x3, lsl #2
    //     0xbfd98c: add             x25, x25, #0xf
    //     0xbfd990: str             w0, [x25]
    //     0xbfd994: tbz             w0, #0, #0xbfd9b0
    //     0xbfd998: ldurb           w16, [x1, #-1]
    //     0xbfd99c: ldurb           w17, [x0, #-1]
    //     0xbfd9a0: and             x16, x17, x16, lsr #2
    //     0xbfd9a4: tst             x16, HEAP, lsr #32
    //     0xbfd9a8: b.eq            #0xbfd9b0
    //     0xbfd9ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbfd9b0: r0 = Column()
    //     0xbfd9b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfd9b4: r1 = Instance_Axis
    //     0xbfd9b4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfd9b8: StoreField: r0->field_f = r1
    //     0xbfd9b8: stur            w1, [x0, #0xf]
    // 0xbfd9bc: r1 = Instance_MainAxisAlignment
    //     0xbfd9bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfd9c0: ldr             x1, [x1, #0xa08]
    // 0xbfd9c4: StoreField: r0->field_13 = r1
    //     0xbfd9c4: stur            w1, [x0, #0x13]
    // 0xbfd9c8: r1 = Instance_MainAxisSize
    //     0xbfd9c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfd9cc: ldr             x1, [x1, #0xa10]
    // 0xbfd9d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbfd9d0: stur            w1, [x0, #0x17]
    // 0xbfd9d4: r1 = Instance_CrossAxisAlignment
    //     0xbfd9d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfd9d8: ldr             x1, [x1, #0xa18]
    // 0xbfd9dc: StoreField: r0->field_1b = r1
    //     0xbfd9dc: stur            w1, [x0, #0x1b]
    // 0xbfd9e0: r1 = Instance_VerticalDirection
    //     0xbfd9e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfd9e4: ldr             x1, [x1, #0xa20]
    // 0xbfd9e8: StoreField: r0->field_23 = r1
    //     0xbfd9e8: stur            w1, [x0, #0x23]
    // 0xbfd9ec: r1 = Instance_Clip
    //     0xbfd9ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfd9f0: ldr             x1, [x1, #0x38]
    // 0xbfd9f4: StoreField: r0->field_2b = r1
    //     0xbfd9f4: stur            w1, [x0, #0x2b]
    // 0xbfd9f8: StoreField: r0->field_2f = rZR
    //     0xbfd9f8: stur            xzr, [x0, #0x2f]
    // 0xbfd9fc: ldur            x1, [fp, #-0x20]
    // 0xbfda00: StoreField: r0->field_b = r1
    //     0xbfda00: stur            w1, [x0, #0xb]
    // 0xbfda04: LeaveFrame
    //     0xbfda04: mov             SP, fp
    //     0xbfda08: ldp             fp, lr, [SP], #0x10
    // 0xbfda0c: ret
    //     0xbfda0c: ret             
    // 0xbfda10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfda10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfda14: b               #0xbfd680
    // 0xbfda18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfda18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfda1c: SaveReg d0
    //     0xbfda1c: str             q0, [SP, #-0x10]!
    // 0xbfda20: SaveReg r1
    //     0xbfda20: str             x1, [SP, #-8]!
    // 0xbfda24: r0 = AllocateDouble()
    //     0xbfda24: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbfda28: RestoreReg r1
    //     0xbfda28: ldr             x1, [SP], #8
    // 0xbfda2c: RestoreReg d0
    //     0xbfda2c: ldr             q0, [SP], #0x10
    // 0xbfda30: b               #0xbfd778
    // 0xbfda34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfda34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbfda38, size: 0x88
    // 0xbfda38: EnterFrame
    //     0xbfda38: stp             fp, lr, [SP, #-0x10]!
    //     0xbfda3c: mov             fp, SP
    // 0xbfda40: AllocStack(0x10)
    //     0xbfda40: sub             SP, SP, #0x10
    // 0xbfda44: SetupParameters()
    //     0xbfda44: ldr             x0, [fp, #0x10]
    //     0xbfda48: ldur            w3, [x0, #0x17]
    //     0xbfda4c: add             x3, x3, HEAP, lsl #32
    //     0xbfda50: stur            x3, [fp, #-0x10]
    // 0xbfda54: CheckStackOverflow
    //     0xbfda54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfda58: cmp             SP, x16
    //     0xbfda5c: b.ls            #0xbfdab4
    // 0xbfda60: LoadField: r0 = r3->field_f
    //     0xbfda60: ldur            w0, [x3, #0xf]
    // 0xbfda64: DecompressPointer r0
    //     0xbfda64: add             x0, x0, HEAP, lsl #32
    // 0xbfda68: mov             x2, x3
    // 0xbfda6c: stur            x0, [fp, #-8]
    // 0xbfda70: r1 = Function '<anonymous closure>':.
    //     0xbfda70: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b50] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xbfda74: ldr             x1, [x1, #0xb50]
    // 0xbfda78: r0 = AllocateClosure()
    //     0xbfda78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfda7c: ldur            x1, [fp, #-8]
    // 0xbfda80: mov             x2, x0
    // 0xbfda84: r0 = setState()
    //     0xbfda84: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbfda88: ldur            x1, [fp, #-0x10]
    // 0xbfda8c: LoadField: r2 = r1->field_f
    //     0xbfda8c: ldur            w2, [x1, #0xf]
    // 0xbfda90: DecompressPointer r2
    //     0xbfda90: add             x2, x2, HEAP, lsl #32
    // 0xbfda94: LoadField: r1 = r2->field_b
    //     0xbfda94: ldur            w1, [x2, #0xb]
    // 0xbfda98: DecompressPointer r1
    //     0xbfda98: add             x1, x1, HEAP, lsl #32
    // 0xbfda9c: cmp             w1, NULL
    // 0xbfdaa0: b.eq            #0xbfdabc
    // 0xbfdaa4: r0 = Null
    //     0xbfdaa4: mov             x0, NULL
    // 0xbfdaa8: LeaveFrame
    //     0xbfdaa8: mov             SP, fp
    //     0xbfdaac: ldp             fp, lr, [SP], #0x10
    // 0xbfdab0: ret
    //     0xbfdab0: ret             
    // 0xbfdab4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfdab4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfdab8: b               #0xbfda60
    // 0xbfdabc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfdabc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3978, size: 0x3c, field offset: 0xc
//   const constructor, 
class Accordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80e78, size: 0x2c
    // 0xc80e78: EnterFrame
    //     0xc80e78: stp             fp, lr, [SP, #-0x10]!
    //     0xc80e7c: mov             fp, SP
    // 0xc80e80: mov             x0, x1
    // 0xc80e84: r1 = <Accordion>
    //     0xc80e84: add             x1, PP, #0x48, lsl #12  ; [pp+0x486d0] TypeArguments: <Accordion>
    //     0xc80e88: ldr             x1, [x1, #0x6d0]
    // 0xc80e8c: r0 = _AccordionState()
    //     0xc80e8c: bl              #0xc80ea4  ; Allocate_AccordionStateStub -> _AccordionState (size=0x18)
    // 0xc80e90: r1 = false
    //     0xc80e90: add             x1, NULL, #0x30  ; false
    // 0xc80e94: StoreField: r0->field_13 = r1
    //     0xc80e94: stur            w1, [x0, #0x13]
    // 0xc80e98: LeaveFrame
    //     0xc80e98: mov             SP, fp
    //     0xc80e9c: ldp             fp, lr, [SP], #0x10
    // 0xc80ea0: ret
    //     0xc80ea0: ret             
  }
}
