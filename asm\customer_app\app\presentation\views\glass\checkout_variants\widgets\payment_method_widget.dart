// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart

// class id: 1049377, size: 0x8
class :: {
}

// class id: 3353, size: 0x1c, field offset: 0x14
class _PaymentMethodWidgetState extends State<dynamic> {

  late String _currentSelectedPaymentMode; // offset: 0x14

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x804c04, size: 0x184
    // 0x804c04: EnterFrame
    //     0x804c04: stp             fp, lr, [SP, #-0x10]!
    //     0x804c08: mov             fp, SP
    // 0x804c0c: AllocStack(0x28)
    //     0x804c0c: sub             SP, SP, #0x28
    // 0x804c10: SetupParameters(_PaymentMethodWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x804c10: mov             x0, x2
    //     0x804c14: stur            x1, [fp, #-8]
    //     0x804c18: stur            x2, [fp, #-0x10]
    // 0x804c1c: CheckStackOverflow
    //     0x804c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804c20: cmp             SP, x16
    //     0x804c24: b.ls            #0x804d78
    // 0x804c28: r1 = 1
    //     0x804c28: movz            x1, #0x1
    // 0x804c2c: r0 = AllocateContext()
    //     0x804c2c: bl              #0x16f6108  ; AllocateContextStub
    // 0x804c30: mov             x4, x0
    // 0x804c34: ldur            x3, [fp, #-8]
    // 0x804c38: stur            x4, [fp, #-0x18]
    // 0x804c3c: StoreField: r4->field_f = r3
    //     0x804c3c: stur            w3, [x4, #0xf]
    // 0x804c40: ldur            x0, [fp, #-0x10]
    // 0x804c44: r2 = Null
    //     0x804c44: mov             x2, NULL
    // 0x804c48: r1 = Null
    //     0x804c48: mov             x1, NULL
    // 0x804c4c: r4 = 60
    //     0x804c4c: movz            x4, #0x3c
    // 0x804c50: branchIfSmi(r0, 0x804c5c)
    //     0x804c50: tbz             w0, #0, #0x804c5c
    // 0x804c54: r4 = LoadClassIdInstr(r0)
    //     0x804c54: ldur            x4, [x0, #-1]
    //     0x804c58: ubfx            x4, x4, #0xc, #0x14
    // 0x804c5c: cmp             x4, #0xffd
    // 0x804c60: b.eq            #0x804c78
    // 0x804c64: r8 = PaymentMethodWidget
    //     0x804c64: add             x8, PP, #0x56, lsl #12  ; [pp+0x56a38] Type: PaymentMethodWidget
    //     0x804c68: ldr             x8, [x8, #0xa38]
    // 0x804c6c: r3 = Null
    //     0x804c6c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56a40] Null
    //     0x804c70: ldr             x3, [x3, #0xa40]
    // 0x804c74: r0 = PaymentMethodWidget()
    //     0x804c74: bl              #0x804e28  ; IsType_PaymentMethodWidget_Stub
    // 0x804c78: ldur            x3, [fp, #-8]
    // 0x804c7c: LoadField: r2 = r3->field_7
    //     0x804c7c: ldur            w2, [x3, #7]
    // 0x804c80: DecompressPointer r2
    //     0x804c80: add             x2, x2, HEAP, lsl #32
    // 0x804c84: ldur            x0, [fp, #-0x10]
    // 0x804c88: r1 = Null
    //     0x804c88: mov             x1, NULL
    // 0x804c8c: cmp             w2, NULL
    // 0x804c90: b.eq            #0x804cb4
    // 0x804c94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x804c94: ldur            w4, [x2, #0x17]
    // 0x804c98: DecompressPointer r4
    //     0x804c98: add             x4, x4, HEAP, lsl #32
    // 0x804c9c: r8 = X0 bound StatefulWidget
    //     0x804c9c: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x804ca0: ldr             x8, [x8, #0x7a0]
    // 0x804ca4: LoadField: r9 = r4->field_7
    //     0x804ca4: ldur            x9, [x4, #7]
    // 0x804ca8: r3 = Null
    //     0x804ca8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56a50] Null
    //     0x804cac: ldr             x3, [x3, #0xa50]
    // 0x804cb0: blr             x9
    // 0x804cb4: ldur            x0, [fp, #-0x10]
    // 0x804cb8: LoadField: r1 = r0->field_1b
    //     0x804cb8: ldur            w1, [x0, #0x1b]
    // 0x804cbc: DecompressPointer r1
    //     0x804cbc: add             x1, x1, HEAP, lsl #32
    // 0x804cc0: ldur            x2, [fp, #-8]
    // 0x804cc4: LoadField: r0 = r2->field_b
    //     0x804cc4: ldur            w0, [x2, #0xb]
    // 0x804cc8: DecompressPointer r0
    //     0x804cc8: add             x0, x0, HEAP, lsl #32
    // 0x804ccc: cmp             w0, NULL
    // 0x804cd0: b.eq            #0x804d80
    // 0x804cd4: LoadField: r3 = r0->field_1b
    //     0x804cd4: ldur            w3, [x0, #0x1b]
    // 0x804cd8: DecompressPointer r3
    //     0x804cd8: add             x3, x3, HEAP, lsl #32
    // 0x804cdc: r0 = LoadClassIdInstr(r1)
    //     0x804cdc: ldur            x0, [x1, #-1]
    //     0x804ce0: ubfx            x0, x0, #0xc, #0x14
    // 0x804ce4: stp             x3, x1, [SP]
    // 0x804ce8: mov             lr, x0
    // 0x804cec: ldr             lr, [x21, lr, lsl #3]
    // 0x804cf0: blr             lr
    // 0x804cf4: tbz             w0, #4, #0x804d68
    // 0x804cf8: ldur            x0, [fp, #-8]
    // 0x804cfc: ldur            x2, [fp, #-0x18]
    // 0x804d00: r1 = Function '<anonymous closure>':.
    //     0x804d00: add             x1, PP, #0x56, lsl #12  ; [pp+0x56a60] AnonymousClosure: (0x801620), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::didUpdateWidget (0x807038)
    //     0x804d04: ldr             x1, [x1, #0xa60]
    // 0x804d08: r0 = AllocateClosure()
    //     0x804d08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x804d0c: ldur            x1, [fp, #-8]
    // 0x804d10: mov             x2, x0
    // 0x804d14: r0 = setState()
    //     0x804d14: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x804d18: ldur            x0, [fp, #-8]
    // 0x804d1c: LoadField: r1 = r0->field_b
    //     0x804d1c: ldur            w1, [x0, #0xb]
    // 0x804d20: DecompressPointer r1
    //     0x804d20: add             x1, x1, HEAP, lsl #32
    // 0x804d24: cmp             w1, NULL
    // 0x804d28: b.eq            #0x804d84
    // 0x804d2c: LoadField: r2 = r1->field_1b
    //     0x804d2c: ldur            w2, [x1, #0x1b]
    // 0x804d30: DecompressPointer r2
    //     0x804d30: add             x2, x2, HEAP, lsl #32
    // 0x804d34: LoadField: r1 = r2->field_7
    //     0x804d34: ldur            w1, [x2, #7]
    // 0x804d38: cbz             w1, #0x804d68
    // 0x804d3c: LoadField: r1 = r0->field_f
    //     0x804d3c: ldur            w1, [x0, #0xf]
    // 0x804d40: DecompressPointer r1
    //     0x804d40: add             x1, x1, HEAP, lsl #32
    // 0x804d44: cmp             w1, NULL
    // 0x804d48: b.eq            #0x804d68
    // 0x804d4c: ldur            x2, [fp, #-0x18]
    // 0x804d50: r1 = Function '<anonymous closure>':.
    //     0x804d50: add             x1, PP, #0x56, lsl #12  ; [pp+0x56a68] AnonymousClosure: (0x804d88), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::didUpdateWidget (0x804c04)
    //     0x804d54: ldr             x1, [x1, #0xa68]
    // 0x804d58: r0 = AllocateClosure()
    //     0x804d58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x804d5c: mov             x2, x0
    // 0x804d60: r1 = <Null?>
    //     0x804d60: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x804d64: r0 = Future.microtask()
    //     0x804d64: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x804d68: r0 = Null
    //     0x804d68: mov             x0, NULL
    // 0x804d6c: LeaveFrame
    //     0x804d6c: mov             SP, fp
    //     0x804d70: ldp             fp, lr, [SP], #0x10
    // 0x804d74: ret
    //     0x804d74: ret             
    // 0x804d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804d78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804d7c: b               #0x804c28
    // 0x804d80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804d80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x804d84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804d84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x804d88, size: 0xa0
    // 0x804d88: EnterFrame
    //     0x804d88: stp             fp, lr, [SP, #-0x10]!
    //     0x804d8c: mov             fp, SP
    // 0x804d90: AllocStack(0x20)
    //     0x804d90: sub             SP, SP, #0x20
    // 0x804d94: SetupParameters()
    //     0x804d94: ldr             x0, [fp, #0x10]
    //     0x804d98: ldur            w1, [x0, #0x17]
    //     0x804d9c: add             x1, x1, HEAP, lsl #32
    // 0x804da0: CheckStackOverflow
    //     0x804da0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x804da4: cmp             SP, x16
    //     0x804da8: b.ls            #0x804e1c
    // 0x804dac: LoadField: r0 = r1->field_f
    //     0x804dac: ldur            w0, [x1, #0xf]
    // 0x804db0: DecompressPointer r0
    //     0x804db0: add             x0, x0, HEAP, lsl #32
    // 0x804db4: LoadField: r1 = r0->field_f
    //     0x804db4: ldur            w1, [x0, #0xf]
    // 0x804db8: DecompressPointer r1
    //     0x804db8: add             x1, x1, HEAP, lsl #32
    // 0x804dbc: cmp             w1, NULL
    // 0x804dc0: b.eq            #0x804e0c
    // 0x804dc4: LoadField: r1 = r0->field_b
    //     0x804dc4: ldur            w1, [x0, #0xb]
    // 0x804dc8: DecompressPointer r1
    //     0x804dc8: add             x1, x1, HEAP, lsl #32
    // 0x804dcc: cmp             w1, NULL
    // 0x804dd0: b.eq            #0x804e24
    // 0x804dd4: LoadField: r0 = r1->field_1b
    //     0x804dd4: ldur            w0, [x1, #0x1b]
    // 0x804dd8: DecompressPointer r0
    //     0x804dd8: add             x0, x0, HEAP, lsl #32
    // 0x804ddc: LoadField: r2 = r1->field_f
    //     0x804ddc: ldur            w2, [x1, #0xf]
    // 0x804de0: DecompressPointer r2
    //     0x804de0: add             x2, x2, HEAP, lsl #32
    // 0x804de4: stp             x0, x2, [SP, #0x10]
    // 0x804de8: r16 = true
    //     0x804de8: add             x16, NULL, #0x20  ; true
    // 0x804dec: r30 = false
    //     0x804dec: add             lr, NULL, #0x30  ; false
    // 0x804df0: stp             lr, x16, [SP]
    // 0x804df4: r4 = 0
    //     0x804df4: movz            x4, #0
    // 0x804df8: ldr             x0, [SP, #0x18]
    // 0x804dfc: r16 = UnlinkedCall_0x613b5c
    //     0x804dfc: add             x16, PP, #0x56, lsl #12  ; [pp+0x56a70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x804e00: add             x16, x16, #0xa70
    // 0x804e04: ldp             x5, lr, [x16]
    // 0x804e08: blr             lr
    // 0x804e0c: r0 = Null
    //     0x804e0c: mov             x0, NULL
    // 0x804e10: LeaveFrame
    //     0x804e10: mov             SP, fp
    //     0x804e14: ldp             fp, lr, [SP], #0x10
    // 0x804e18: ret
    //     0x804e18: ret             
    // 0x804e1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x804e1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x804e20: b               #0x804dac
    // 0x804e24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x804e24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x9413dc, size: 0x170
    // 0x9413dc: EnterFrame
    //     0x9413dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9413e0: mov             fp, SP
    // 0x9413e4: AllocStack(0x18)
    //     0x9413e4: sub             SP, SP, #0x18
    // 0x9413e8: SetupParameters(_PaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x9413e8: stur            x1, [fp, #-8]
    // 0x9413ec: CheckStackOverflow
    //     0x9413ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9413f0: cmp             SP, x16
    //     0x9413f4: b.ls            #0x94153c
    // 0x9413f8: r1 = 1
    //     0x9413f8: movz            x1, #0x1
    // 0x9413fc: r0 = AllocateContext()
    //     0x9413fc: bl              #0x16f6108  ; AllocateContextStub
    // 0x941400: mov             x2, x0
    // 0x941404: ldur            x1, [fp, #-8]
    // 0x941408: StoreField: r2->field_f = r1
    //     0x941408: stur            w1, [x2, #0xf]
    // 0x94140c: LoadField: r0 = r1->field_b
    //     0x94140c: ldur            w0, [x1, #0xb]
    // 0x941410: DecompressPointer r0
    //     0x941410: add             x0, x0, HEAP, lsl #32
    // 0x941414: cmp             w0, NULL
    // 0x941418: b.eq            #0x941544
    // 0x94141c: LoadField: r3 = r0->field_1b
    //     0x94141c: ldur            w3, [x0, #0x1b]
    // 0x941420: DecompressPointer r3
    //     0x941420: add             x3, x3, HEAP, lsl #32
    // 0x941424: mov             x0, x3
    // 0x941428: StoreField: r1->field_13 = r0
    //     0x941428: stur            w0, [x1, #0x13]
    //     0x94142c: ldurb           w16, [x1, #-1]
    //     0x941430: ldurb           w17, [x0, #-1]
    //     0x941434: and             x16, x17, x16, lsr #2
    //     0x941438: tst             x16, HEAP, lsr #32
    //     0x94143c: b.eq            #0x941444
    //     0x941440: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x941444: LoadField: r0 = r3->field_7
    //     0x941444: ldur            w0, [x3, #7]
    // 0x941448: cbz             w0, #0x94152c
    // 0x94144c: r0 = LoadStaticField(0x878)
    //     0x94144c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x941450: ldr             x0, [x0, #0x10f0]
    // 0x941454: cmp             w0, NULL
    // 0x941458: b.eq            #0x941548
    // 0x94145c: LoadField: r3 = r0->field_53
    //     0x94145c: ldur            w3, [x0, #0x53]
    // 0x941460: DecompressPointer r3
    //     0x941460: add             x3, x3, HEAP, lsl #32
    // 0x941464: stur            x3, [fp, #-0x10]
    // 0x941468: LoadField: r0 = r3->field_7
    //     0x941468: ldur            w0, [x3, #7]
    // 0x94146c: DecompressPointer r0
    //     0x94146c: add             x0, x0, HEAP, lsl #32
    // 0x941470: stur            x0, [fp, #-8]
    // 0x941474: r1 = Function '<anonymous closure>':.
    //     0x941474: add             x1, PP, #0x56, lsl #12  ; [pp+0x56a80] AnonymousClosure: (0x94154c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::initState (0x9413dc)
    //     0x941478: ldr             x1, [x1, #0xa80]
    // 0x94147c: r0 = AllocateClosure()
    //     0x94147c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x941480: ldur            x2, [fp, #-8]
    // 0x941484: mov             x3, x0
    // 0x941488: r1 = Null
    //     0x941488: mov             x1, NULL
    // 0x94148c: stur            x3, [fp, #-8]
    // 0x941490: cmp             w2, NULL
    // 0x941494: b.eq            #0x9414b4
    // 0x941498: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x941498: ldur            w4, [x2, #0x17]
    // 0x94149c: DecompressPointer r4
    //     0x94149c: add             x4, x4, HEAP, lsl #32
    // 0x9414a0: r8 = X0
    //     0x9414a0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x9414a4: LoadField: r9 = r4->field_7
    //     0x9414a4: ldur            x9, [x4, #7]
    // 0x9414a8: r3 = Null
    //     0x9414a8: add             x3, PP, #0x56, lsl #12  ; [pp+0x56a88] Null
    //     0x9414ac: ldr             x3, [x3, #0xa88]
    // 0x9414b0: blr             x9
    // 0x9414b4: ldur            x0, [fp, #-0x10]
    // 0x9414b8: LoadField: r1 = r0->field_b
    //     0x9414b8: ldur            w1, [x0, #0xb]
    // 0x9414bc: LoadField: r2 = r0->field_f
    //     0x9414bc: ldur            w2, [x0, #0xf]
    // 0x9414c0: DecompressPointer r2
    //     0x9414c0: add             x2, x2, HEAP, lsl #32
    // 0x9414c4: LoadField: r3 = r2->field_b
    //     0x9414c4: ldur            w3, [x2, #0xb]
    // 0x9414c8: r2 = LoadInt32Instr(r1)
    //     0x9414c8: sbfx            x2, x1, #1, #0x1f
    // 0x9414cc: stur            x2, [fp, #-0x18]
    // 0x9414d0: r1 = LoadInt32Instr(r3)
    //     0x9414d0: sbfx            x1, x3, #1, #0x1f
    // 0x9414d4: cmp             x2, x1
    // 0x9414d8: b.ne            #0x9414e4
    // 0x9414dc: mov             x1, x0
    // 0x9414e0: r0 = _growToNextCapacity()
    //     0x9414e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9414e4: ldur            x2, [fp, #-0x10]
    // 0x9414e8: ldur            x3, [fp, #-0x18]
    // 0x9414ec: add             x4, x3, #1
    // 0x9414f0: lsl             x5, x4, #1
    // 0x9414f4: StoreField: r2->field_b = r5
    //     0x9414f4: stur            w5, [x2, #0xb]
    // 0x9414f8: LoadField: r1 = r2->field_f
    //     0x9414f8: ldur            w1, [x2, #0xf]
    // 0x9414fc: DecompressPointer r1
    //     0x9414fc: add             x1, x1, HEAP, lsl #32
    // 0x941500: ldur            x0, [fp, #-8]
    // 0x941504: ArrayStore: r1[r3] = r0  ; List_4
    //     0x941504: add             x25, x1, x3, lsl #2
    //     0x941508: add             x25, x25, #0xf
    //     0x94150c: str             w0, [x25]
    //     0x941510: tbz             w0, #0, #0x94152c
    //     0x941514: ldurb           w16, [x1, #-1]
    //     0x941518: ldurb           w17, [x0, #-1]
    //     0x94151c: and             x16, x17, x16, lsr #2
    //     0x941520: tst             x16, HEAP, lsr #32
    //     0x941524: b.eq            #0x94152c
    //     0x941528: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94152c: r0 = Null
    //     0x94152c: mov             x0, NULL
    // 0x941530: LeaveFrame
    //     0x941530: mov             SP, fp
    //     0x941534: ldp             fp, lr, [SP], #0x10
    // 0x941538: ret
    //     0x941538: ret             
    // 0x94153c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94153c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941540: b               #0x9413f8
    // 0x941544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x94154c, size: 0xb4
    // 0x94154c: EnterFrame
    //     0x94154c: stp             fp, lr, [SP, #-0x10]!
    //     0x941550: mov             fp, SP
    // 0x941554: AllocStack(0x20)
    //     0x941554: sub             SP, SP, #0x20
    // 0x941558: SetupParameters()
    //     0x941558: ldr             x0, [fp, #0x18]
    //     0x94155c: ldur            w1, [x0, #0x17]
    //     0x941560: add             x1, x1, HEAP, lsl #32
    // 0x941564: CheckStackOverflow
    //     0x941564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941568: cmp             SP, x16
    //     0x94156c: b.ls            #0x9415f4
    // 0x941570: LoadField: r0 = r1->field_f
    //     0x941570: ldur            w0, [x1, #0xf]
    // 0x941574: DecompressPointer r0
    //     0x941574: add             x0, x0, HEAP, lsl #32
    // 0x941578: LoadField: r1 = r0->field_f
    //     0x941578: ldur            w1, [x0, #0xf]
    // 0x94157c: DecompressPointer r1
    //     0x94157c: add             x1, x1, HEAP, lsl #32
    // 0x941580: cmp             w1, NULL
    // 0x941584: b.eq            #0x9415e4
    // 0x941588: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x941588: ldur            w1, [x0, #0x17]
    // 0x94158c: DecompressPointer r1
    //     0x94158c: add             x1, x1, HEAP, lsl #32
    // 0x941590: tbz             w1, #4, #0x9415e4
    // 0x941594: r1 = true
    //     0x941594: add             x1, NULL, #0x20  ; true
    // 0x941598: ArrayStore: r0[0] = r1  ; List_4
    //     0x941598: stur            w1, [x0, #0x17]
    // 0x94159c: LoadField: r1 = r0->field_b
    //     0x94159c: ldur            w1, [x0, #0xb]
    // 0x9415a0: DecompressPointer r1
    //     0x9415a0: add             x1, x1, HEAP, lsl #32
    // 0x9415a4: cmp             w1, NULL
    // 0x9415a8: b.eq            #0x9415fc
    // 0x9415ac: LoadField: r0 = r1->field_1b
    //     0x9415ac: ldur            w0, [x1, #0x1b]
    // 0x9415b0: DecompressPointer r0
    //     0x9415b0: add             x0, x0, HEAP, lsl #32
    // 0x9415b4: LoadField: r2 = r1->field_f
    //     0x9415b4: ldur            w2, [x1, #0xf]
    // 0x9415b8: DecompressPointer r2
    //     0x9415b8: add             x2, x2, HEAP, lsl #32
    // 0x9415bc: stp             x0, x2, [SP, #0x10]
    // 0x9415c0: r16 = true
    //     0x9415c0: add             x16, NULL, #0x20  ; true
    // 0x9415c4: r30 = false
    //     0x9415c4: add             lr, NULL, #0x30  ; false
    // 0x9415c8: stp             lr, x16, [SP]
    // 0x9415cc: r4 = 0
    //     0x9415cc: movz            x4, #0
    // 0x9415d0: ldr             x0, [SP, #0x18]
    // 0x9415d4: r16 = UnlinkedCall_0x613b5c
    //     0x9415d4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56a98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9415d8: add             x16, x16, #0xa98
    // 0x9415dc: ldp             x5, lr, [x16]
    // 0x9415e0: blr             lr
    // 0x9415e4: r0 = Null
    //     0x9415e4: mov             x0, NULL
    // 0x9415e8: LeaveFrame
    //     0x9415e8: mov             SP, fp
    //     0x9415ec: ldp             fp, lr, [SP], #0x10
    // 0x9415f0: ret
    //     0x9415f0: ret             
    // 0x9415f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9415f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9415f8: b               #0x941570
    // 0x9415fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9415fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb4eabc, size: 0x3fc
    // 0xb4eabc: EnterFrame
    //     0xb4eabc: stp             fp, lr, [SP, #-0x10]!
    //     0xb4eac0: mov             fp, SP
    // 0xb4eac4: AllocStack(0x48)
    //     0xb4eac4: sub             SP, SP, #0x48
    // 0xb4eac8: SetupParameters(_PaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb4eac8: mov             x0, x1
    //     0xb4eacc: stur            x1, [fp, #-8]
    //     0xb4ead0: mov             x1, x2
    //     0xb4ead4: stur            x2, [fp, #-0x10]
    // 0xb4ead8: CheckStackOverflow
    //     0xb4ead8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4eadc: cmp             SP, x16
    //     0xb4eae0: b.ls            #0xb4eeac
    // 0xb4eae4: r1 = 1
    //     0xb4eae4: movz            x1, #0x1
    // 0xb4eae8: r0 = AllocateContext()
    //     0xb4eae8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4eaec: mov             x2, x0
    // 0xb4eaf0: ldur            x0, [fp, #-8]
    // 0xb4eaf4: stur            x2, [fp, #-0x18]
    // 0xb4eaf8: StoreField: r2->field_f = r0
    //     0xb4eaf8: stur            w0, [x2, #0xf]
    // 0xb4eafc: ldur            x1, [fp, #-0x10]
    // 0xb4eb00: r0 = of()
    //     0xb4eb00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4eb04: LoadField: r1 = r0->field_87
    //     0xb4eb04: ldur            w1, [x0, #0x87]
    // 0xb4eb08: DecompressPointer r1
    //     0xb4eb08: add             x1, x1, HEAP, lsl #32
    // 0xb4eb0c: LoadField: r0 = r1->field_7
    //     0xb4eb0c: ldur            w0, [x1, #7]
    // 0xb4eb10: DecompressPointer r0
    //     0xb4eb10: add             x0, x0, HEAP, lsl #32
    // 0xb4eb14: r16 = 16.000000
    //     0xb4eb14: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4eb18: ldr             x16, [x16, #0x188]
    // 0xb4eb1c: r30 = Instance_Color
    //     0xb4eb1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4eb20: stp             lr, x16, [SP]
    // 0xb4eb24: mov             x1, x0
    // 0xb4eb28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4eb28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4eb2c: ldr             x4, [x4, #0xaa0]
    // 0xb4eb30: r0 = copyWith()
    //     0xb4eb30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4eb34: stur            x0, [fp, #-0x20]
    // 0xb4eb38: r0 = Text()
    //     0xb4eb38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4eb3c: mov             x1, x0
    // 0xb4eb40: r0 = "Payment Method"
    //     0xb4eb40: add             x0, PP, #0x34, lsl #12  ; [pp+0x34110] "Payment Method"
    //     0xb4eb44: ldr             x0, [x0, #0x110]
    // 0xb4eb48: stur            x1, [fp, #-0x28]
    // 0xb4eb4c: StoreField: r1->field_b = r0
    //     0xb4eb4c: stur            w0, [x1, #0xb]
    // 0xb4eb50: ldur            x0, [fp, #-0x20]
    // 0xb4eb54: StoreField: r1->field_13 = r0
    //     0xb4eb54: stur            w0, [x1, #0x13]
    // 0xb4eb58: r0 = SvgPicture()
    //     0xb4eb58: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb4eb5c: stur            x0, [fp, #-0x20]
    // 0xb4eb60: r16 = "return order"
    //     0xb4eb60: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0xb4eb64: ldr             x16, [x16, #0xc78]
    // 0xb4eb68: r30 = Instance_BoxFit
    //     0xb4eb68: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb4eb6c: ldr             lr, [lr, #0xb18]
    // 0xb4eb70: stp             lr, x16, [SP]
    // 0xb4eb74: mov             x1, x0
    // 0xb4eb78: r2 = "assets/images/secure_icon.svg"
    //     0xb4eb78: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0xb4eb7c: ldr             x2, [x2, #0xc80]
    // 0xb4eb80: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xb4eb80: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xb4eb84: ldr             x4, [x4, #0xb28]
    // 0xb4eb88: r0 = SvgPicture.asset()
    //     0xb4eb88: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb4eb8c: r1 = Null
    //     0xb4eb8c: mov             x1, NULL
    // 0xb4eb90: r2 = 4
    //     0xb4eb90: movz            x2, #0x4
    // 0xb4eb94: r0 = AllocateArray()
    //     0xb4eb94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4eb98: mov             x2, x0
    // 0xb4eb9c: ldur            x0, [fp, #-0x28]
    // 0xb4eba0: stur            x2, [fp, #-0x30]
    // 0xb4eba4: StoreField: r2->field_f = r0
    //     0xb4eba4: stur            w0, [x2, #0xf]
    // 0xb4eba8: ldur            x0, [fp, #-0x20]
    // 0xb4ebac: StoreField: r2->field_13 = r0
    //     0xb4ebac: stur            w0, [x2, #0x13]
    // 0xb4ebb0: r1 = <Widget>
    //     0xb4ebb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4ebb4: r0 = AllocateGrowableArray()
    //     0xb4ebb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4ebb8: mov             x1, x0
    // 0xb4ebbc: ldur            x0, [fp, #-0x30]
    // 0xb4ebc0: stur            x1, [fp, #-0x20]
    // 0xb4ebc4: StoreField: r1->field_f = r0
    //     0xb4ebc4: stur            w0, [x1, #0xf]
    // 0xb4ebc8: r2 = 4
    //     0xb4ebc8: movz            x2, #0x4
    // 0xb4ebcc: StoreField: r1->field_b = r2
    //     0xb4ebcc: stur            w2, [x1, #0xb]
    // 0xb4ebd0: r0 = Row()
    //     0xb4ebd0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4ebd4: mov             x1, x0
    // 0xb4ebd8: r0 = Instance_Axis
    //     0xb4ebd8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4ebdc: stur            x1, [fp, #-0x28]
    // 0xb4ebe0: StoreField: r1->field_f = r0
    //     0xb4ebe0: stur            w0, [x1, #0xf]
    // 0xb4ebe4: r0 = Instance_MainAxisAlignment
    //     0xb4ebe4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4ebe8: ldr             x0, [x0, #0xa8]
    // 0xb4ebec: StoreField: r1->field_13 = r0
    //     0xb4ebec: stur            w0, [x1, #0x13]
    // 0xb4ebf0: r0 = Instance_MainAxisSize
    //     0xb4ebf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4ebf4: ldr             x0, [x0, #0xa10]
    // 0xb4ebf8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4ebf8: stur            w0, [x1, #0x17]
    // 0xb4ebfc: r2 = Instance_CrossAxisAlignment
    //     0xb4ebfc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4ec00: ldr             x2, [x2, #0xa18]
    // 0xb4ec04: StoreField: r1->field_1b = r2
    //     0xb4ec04: stur            w2, [x1, #0x1b]
    // 0xb4ec08: r3 = Instance_VerticalDirection
    //     0xb4ec08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ec0c: ldr             x3, [x3, #0xa20]
    // 0xb4ec10: StoreField: r1->field_23 = r3
    //     0xb4ec10: stur            w3, [x1, #0x23]
    // 0xb4ec14: r4 = Instance_Clip
    //     0xb4ec14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4ec18: ldr             x4, [x4, #0x38]
    // 0xb4ec1c: StoreField: r1->field_2b = r4
    //     0xb4ec1c: stur            w4, [x1, #0x2b]
    // 0xb4ec20: StoreField: r1->field_2f = rZR
    //     0xb4ec20: stur            xzr, [x1, #0x2f]
    // 0xb4ec24: ldur            x5, [fp, #-0x20]
    // 0xb4ec28: StoreField: r1->field_b = r5
    //     0xb4ec28: stur            w5, [x1, #0xb]
    // 0xb4ec2c: r0 = Padding()
    //     0xb4ec2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4ec30: mov             x2, x0
    // 0xb4ec34: r0 = Instance_EdgeInsets
    //     0xb4ec34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb4ec38: ldr             x0, [x0, #0xd0]
    // 0xb4ec3c: stur            x2, [fp, #-0x20]
    // 0xb4ec40: StoreField: r2->field_f = r0
    //     0xb4ec40: stur            w0, [x2, #0xf]
    // 0xb4ec44: ldur            x0, [fp, #-0x28]
    // 0xb4ec48: StoreField: r2->field_b = r0
    //     0xb4ec48: stur            w0, [x2, #0xb]
    // 0xb4ec4c: ldur            x1, [fp, #-0x10]
    // 0xb4ec50: r0 = of()
    //     0xb4ec50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4ec54: LoadField: r1 = r0->field_5b
    //     0xb4ec54: ldur            w1, [x0, #0x5b]
    // 0xb4ec58: DecompressPointer r1
    //     0xb4ec58: add             x1, x1, HEAP, lsl #32
    // 0xb4ec5c: r0 = LoadClassIdInstr(r1)
    //     0xb4ec5c: ldur            x0, [x1, #-1]
    //     0xb4ec60: ubfx            x0, x0, #0xc, #0x14
    // 0xb4ec64: d0 = 0.100000
    //     0xb4ec64: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4ec68: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4ec68: sub             lr, x0, #0xffa
    //     0xb4ec6c: ldr             lr, [x21, lr, lsl #3]
    //     0xb4ec70: blr             lr
    // 0xb4ec74: mov             x2, x0
    // 0xb4ec78: r1 = Null
    //     0xb4ec78: mov             x1, NULL
    // 0xb4ec7c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb4ec7c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb4ec80: r0 = Border.all()
    //     0xb4ec80: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb4ec84: stur            x0, [fp, #-0x10]
    // 0xb4ec88: r0 = Radius()
    //     0xb4ec88: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4ec8c: d0 = 20.000000
    //     0xb4ec8c: fmov            d0, #20.00000000
    // 0xb4ec90: stur            x0, [fp, #-0x28]
    // 0xb4ec94: StoreField: r0->field_7 = d0
    //     0xb4ec94: stur            d0, [x0, #7]
    // 0xb4ec98: StoreField: r0->field_f = d0
    //     0xb4ec98: stur            d0, [x0, #0xf]
    // 0xb4ec9c: r0 = BorderRadius()
    //     0xb4ec9c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4eca0: mov             x1, x0
    // 0xb4eca4: ldur            x0, [fp, #-0x28]
    // 0xb4eca8: stur            x1, [fp, #-0x30]
    // 0xb4ecac: StoreField: r1->field_7 = r0
    //     0xb4ecac: stur            w0, [x1, #7]
    // 0xb4ecb0: StoreField: r1->field_b = r0
    //     0xb4ecb0: stur            w0, [x1, #0xb]
    // 0xb4ecb4: StoreField: r1->field_f = r0
    //     0xb4ecb4: stur            w0, [x1, #0xf]
    // 0xb4ecb8: StoreField: r1->field_13 = r0
    //     0xb4ecb8: stur            w0, [x1, #0x13]
    // 0xb4ecbc: r0 = BoxDecoration()
    //     0xb4ecbc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4ecc0: mov             x3, x0
    // 0xb4ecc4: ldur            x0, [fp, #-0x10]
    // 0xb4ecc8: stur            x3, [fp, #-0x28]
    // 0xb4eccc: StoreField: r3->field_f = r0
    //     0xb4eccc: stur            w0, [x3, #0xf]
    // 0xb4ecd0: ldur            x0, [fp, #-0x30]
    // 0xb4ecd4: StoreField: r3->field_13 = r0
    //     0xb4ecd4: stur            w0, [x3, #0x13]
    // 0xb4ecd8: r0 = Instance_BoxShape
    //     0xb4ecd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4ecdc: ldr             x0, [x0, #0x80]
    // 0xb4ece0: StoreField: r3->field_23 = r0
    //     0xb4ece0: stur            w0, [x3, #0x23]
    // 0xb4ece4: ldur            x0, [fp, #-8]
    // 0xb4ece8: LoadField: r1 = r0->field_b
    //     0xb4ece8: ldur            w1, [x0, #0xb]
    // 0xb4ecec: DecompressPointer r1
    //     0xb4ecec: add             x1, x1, HEAP, lsl #32
    // 0xb4ecf0: cmp             w1, NULL
    // 0xb4ecf4: b.eq            #0xb4eeb4
    // 0xb4ecf8: LoadField: r0 = r1->field_b
    //     0xb4ecf8: ldur            w0, [x1, #0xb]
    // 0xb4ecfc: DecompressPointer r0
    //     0xb4ecfc: add             x0, x0, HEAP, lsl #32
    // 0xb4ed00: LoadField: r1 = r0->field_b
    //     0xb4ed00: ldur            w1, [x0, #0xb]
    // 0xb4ed04: DecompressPointer r1
    //     0xb4ed04: add             x1, x1, HEAP, lsl #32
    // 0xb4ed08: cmp             w1, NULL
    // 0xb4ed0c: b.ne            #0xb4ed18
    // 0xb4ed10: r0 = Null
    //     0xb4ed10: mov             x0, NULL
    // 0xb4ed14: b               #0xb4ed4c
    // 0xb4ed18: LoadField: r0 = r1->field_1f
    //     0xb4ed18: ldur            w0, [x1, #0x1f]
    // 0xb4ed1c: DecompressPointer r0
    //     0xb4ed1c: add             x0, x0, HEAP, lsl #32
    // 0xb4ed20: cmp             w0, NULL
    // 0xb4ed24: b.ne            #0xb4ed30
    // 0xb4ed28: r0 = Null
    //     0xb4ed28: mov             x0, NULL
    // 0xb4ed2c: b               #0xb4ed4c
    // 0xb4ed30: LoadField: r1 = r0->field_7
    //     0xb4ed30: ldur            w1, [x0, #7]
    // 0xb4ed34: DecompressPointer r1
    //     0xb4ed34: add             x1, x1, HEAP, lsl #32
    // 0xb4ed38: cmp             w1, NULL
    // 0xb4ed3c: b.ne            #0xb4ed48
    // 0xb4ed40: r0 = Null
    //     0xb4ed40: mov             x0, NULL
    // 0xb4ed44: b               #0xb4ed4c
    // 0xb4ed48: LoadField: r0 = r1->field_b
    //     0xb4ed48: ldur            w0, [x1, #0xb]
    // 0xb4ed4c: cmp             w0, NULL
    // 0xb4ed50: b.ne            #0xb4ed5c
    // 0xb4ed54: r4 = 0
    //     0xb4ed54: movz            x4, #0
    // 0xb4ed58: b               #0xb4ed64
    // 0xb4ed5c: r1 = LoadInt32Instr(r0)
    //     0xb4ed5c: sbfx            x1, x0, #1, #0x1f
    // 0xb4ed60: mov             x4, x1
    // 0xb4ed64: ldur            x0, [fp, #-0x20]
    // 0xb4ed68: ldur            x2, [fp, #-0x18]
    // 0xb4ed6c: stur            x4, [fp, #-0x38]
    // 0xb4ed70: r1 = Function '<anonymous closure>':.
    //     0xb4ed70: add             x1, PP, #0x56, lsl #12  ; [pp+0x569d8] AnonymousClosure: (0xb51828), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::build (0xb4eabc)
    //     0xb4ed74: ldr             x1, [x1, #0x9d8]
    // 0xb4ed78: r0 = AllocateClosure()
    //     0xb4ed78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4ed7c: r1 = Function '<anonymous closure>':.
    //     0xb4ed7c: add             x1, PP, #0x56, lsl #12  ; [pp+0x569e0] AnonymousClosure: (0xb4eeb8), in [package:customer_app/app/presentation/views/glass/post_order/return_order/return_order_view.dart] ReturnOrderView::body (0x14f840c)
    //     0xb4ed80: ldr             x1, [x1, #0x9e0]
    // 0xb4ed84: r2 = Null
    //     0xb4ed84: mov             x2, NULL
    // 0xb4ed88: stur            x0, [fp, #-8]
    // 0xb4ed8c: r0 = AllocateClosure()
    //     0xb4ed8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4ed90: stur            x0, [fp, #-0x10]
    // 0xb4ed94: r0 = ListView()
    //     0xb4ed94: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb4ed98: stur            x0, [fp, #-0x18]
    // 0xb4ed9c: r16 = true
    //     0xb4ed9c: add             x16, NULL, #0x20  ; true
    // 0xb4eda0: r30 = false
    //     0xb4eda0: add             lr, NULL, #0x30  ; false
    // 0xb4eda4: stp             lr, x16, [SP]
    // 0xb4eda8: mov             x1, x0
    // 0xb4edac: ldur            x2, [fp, #-8]
    // 0xb4edb0: ldur            x3, [fp, #-0x38]
    // 0xb4edb4: ldur            x5, [fp, #-0x10]
    // 0xb4edb8: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xb4edb8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb4edbc: ldr             x4, [x4, #0xc98]
    // 0xb4edc0: r0 = ListView.separated()
    //     0xb4edc0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb4edc4: r0 = Container()
    //     0xb4edc4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4edc8: stur            x0, [fp, #-8]
    // 0xb4edcc: ldur            x16, [fp, #-0x28]
    // 0xb4edd0: ldur            lr, [fp, #-0x18]
    // 0xb4edd4: stp             lr, x16, [SP]
    // 0xb4edd8: mov             x1, x0
    // 0xb4eddc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb4eddc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4ede0: ldr             x4, [x4, #0x88]
    // 0xb4ede4: r0 = Container()
    //     0xb4ede4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4ede8: r0 = Padding()
    //     0xb4ede8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4edec: mov             x3, x0
    // 0xb4edf0: r0 = Instance_EdgeInsets
    //     0xb4edf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4edf4: ldr             x0, [x0, #0x1f0]
    // 0xb4edf8: stur            x3, [fp, #-0x10]
    // 0xb4edfc: StoreField: r3->field_f = r0
    //     0xb4edfc: stur            w0, [x3, #0xf]
    // 0xb4ee00: ldur            x0, [fp, #-8]
    // 0xb4ee04: StoreField: r3->field_b = r0
    //     0xb4ee04: stur            w0, [x3, #0xb]
    // 0xb4ee08: r1 = Null
    //     0xb4ee08: mov             x1, NULL
    // 0xb4ee0c: r2 = 4
    //     0xb4ee0c: movz            x2, #0x4
    // 0xb4ee10: r0 = AllocateArray()
    //     0xb4ee10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4ee14: mov             x2, x0
    // 0xb4ee18: ldur            x0, [fp, #-0x20]
    // 0xb4ee1c: stur            x2, [fp, #-8]
    // 0xb4ee20: StoreField: r2->field_f = r0
    //     0xb4ee20: stur            w0, [x2, #0xf]
    // 0xb4ee24: ldur            x0, [fp, #-0x10]
    // 0xb4ee28: StoreField: r2->field_13 = r0
    //     0xb4ee28: stur            w0, [x2, #0x13]
    // 0xb4ee2c: r1 = <Widget>
    //     0xb4ee2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4ee30: r0 = AllocateGrowableArray()
    //     0xb4ee30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4ee34: mov             x1, x0
    // 0xb4ee38: ldur            x0, [fp, #-8]
    // 0xb4ee3c: stur            x1, [fp, #-0x10]
    // 0xb4ee40: StoreField: r1->field_f = r0
    //     0xb4ee40: stur            w0, [x1, #0xf]
    // 0xb4ee44: r0 = 4
    //     0xb4ee44: movz            x0, #0x4
    // 0xb4ee48: StoreField: r1->field_b = r0
    //     0xb4ee48: stur            w0, [x1, #0xb]
    // 0xb4ee4c: r0 = Column()
    //     0xb4ee4c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4ee50: r1 = Instance_Axis
    //     0xb4ee50: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4ee54: StoreField: r0->field_f = r1
    //     0xb4ee54: stur            w1, [x0, #0xf]
    // 0xb4ee58: r1 = Instance_MainAxisAlignment
    //     0xb4ee58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4ee5c: ldr             x1, [x1, #0xa08]
    // 0xb4ee60: StoreField: r0->field_13 = r1
    //     0xb4ee60: stur            w1, [x0, #0x13]
    // 0xb4ee64: r1 = Instance_MainAxisSize
    //     0xb4ee64: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4ee68: ldr             x1, [x1, #0xa10]
    // 0xb4ee6c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4ee6c: stur            w1, [x0, #0x17]
    // 0xb4ee70: r1 = Instance_CrossAxisAlignment
    //     0xb4ee70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4ee74: ldr             x1, [x1, #0xa18]
    // 0xb4ee78: StoreField: r0->field_1b = r1
    //     0xb4ee78: stur            w1, [x0, #0x1b]
    // 0xb4ee7c: r1 = Instance_VerticalDirection
    //     0xb4ee7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ee80: ldr             x1, [x1, #0xa20]
    // 0xb4ee84: StoreField: r0->field_23 = r1
    //     0xb4ee84: stur            w1, [x0, #0x23]
    // 0xb4ee88: r1 = Instance_Clip
    //     0xb4ee88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4ee8c: ldr             x1, [x1, #0x38]
    // 0xb4ee90: StoreField: r0->field_2b = r1
    //     0xb4ee90: stur            w1, [x0, #0x2b]
    // 0xb4ee94: StoreField: r0->field_2f = rZR
    //     0xb4ee94: stur            xzr, [x0, #0x2f]
    // 0xb4ee98: ldur            x1, [fp, #-0x10]
    // 0xb4ee9c: StoreField: r0->field_b = r1
    //     0xb4ee9c: stur            w1, [x0, #0xb]
    // 0xb4eea0: LeaveFrame
    //     0xb4eea0: mov             SP, fp
    //     0xb4eea4: ldp             fp, lr, [SP], #0x10
    // 0xb4eea8: ret
    //     0xb4eea8: ret             
    // 0xb4eeac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4eeac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4eeb0: b               #0xb4eae4
    // 0xb4eeb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4eeb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget? <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb51828, size: 0xf0
    // 0xb51828: EnterFrame
    //     0xb51828: stp             fp, lr, [SP, #-0x10]!
    //     0xb5182c: mov             fp, SP
    // 0xb51830: ldr             x0, [fp, #0x20]
    // 0xb51834: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb51834: ldur            w1, [x0, #0x17]
    // 0xb51838: DecompressPointer r1
    //     0xb51838: add             x1, x1, HEAP, lsl #32
    // 0xb5183c: CheckStackOverflow
    //     0xb5183c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb51840: cmp             SP, x16
    //     0xb51844: b.ls            #0xb51908
    // 0xb51848: LoadField: r2 = r1->field_f
    //     0xb51848: ldur            w2, [x1, #0xf]
    // 0xb5184c: DecompressPointer r2
    //     0xb5184c: add             x2, x2, HEAP, lsl #32
    // 0xb51850: LoadField: r0 = r2->field_b
    //     0xb51850: ldur            w0, [x2, #0xb]
    // 0xb51854: DecompressPointer r0
    //     0xb51854: add             x0, x0, HEAP, lsl #32
    // 0xb51858: cmp             w0, NULL
    // 0xb5185c: b.eq            #0xb51910
    // 0xb51860: LoadField: r1 = r0->field_b
    //     0xb51860: ldur            w1, [x0, #0xb]
    // 0xb51864: DecompressPointer r1
    //     0xb51864: add             x1, x1, HEAP, lsl #32
    // 0xb51868: LoadField: r0 = r1->field_b
    //     0xb51868: ldur            w0, [x1, #0xb]
    // 0xb5186c: DecompressPointer r0
    //     0xb5186c: add             x0, x0, HEAP, lsl #32
    // 0xb51870: cmp             w0, NULL
    // 0xb51874: b.ne            #0xb51880
    // 0xb51878: r0 = Null
    //     0xb51878: mov             x0, NULL
    // 0xb5187c: b               #0xb518ec
    // 0xb51880: LoadField: r1 = r0->field_1f
    //     0xb51880: ldur            w1, [x0, #0x1f]
    // 0xb51884: DecompressPointer r1
    //     0xb51884: add             x1, x1, HEAP, lsl #32
    // 0xb51888: cmp             w1, NULL
    // 0xb5188c: b.ne            #0xb51898
    // 0xb51890: r0 = Null
    //     0xb51890: mov             x0, NULL
    // 0xb51894: b               #0xb518ec
    // 0xb51898: LoadField: r3 = r1->field_7
    //     0xb51898: ldur            w3, [x1, #7]
    // 0xb5189c: DecompressPointer r3
    //     0xb5189c: add             x3, x3, HEAP, lsl #32
    // 0xb518a0: cmp             w3, NULL
    // 0xb518a4: b.ne            #0xb518b0
    // 0xb518a8: r0 = Null
    //     0xb518a8: mov             x0, NULL
    // 0xb518ac: b               #0xb518ec
    // 0xb518b0: ldr             x0, [fp, #0x10]
    // 0xb518b4: LoadField: r1 = r3->field_b
    //     0xb518b4: ldur            w1, [x3, #0xb]
    // 0xb518b8: r4 = LoadInt32Instr(r0)
    //     0xb518b8: sbfx            x4, x0, #1, #0x1f
    //     0xb518bc: tbz             w0, #0, #0xb518c4
    //     0xb518c0: ldur            x4, [x0, #7]
    // 0xb518c4: r0 = LoadInt32Instr(r1)
    //     0xb518c4: sbfx            x0, x1, #1, #0x1f
    // 0xb518c8: mov             x1, x4
    // 0xb518cc: cmp             x1, x0
    // 0xb518d0: b.hs            #0xb51914
    // 0xb518d4: LoadField: r0 = r3->field_f
    //     0xb518d4: ldur            w0, [x3, #0xf]
    // 0xb518d8: DecompressPointer r0
    //     0xb518d8: add             x0, x0, HEAP, lsl #32
    // 0xb518dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb518dc: add             x16, x0, x4, lsl #2
    //     0xb518e0: ldur            w1, [x16, #0xf]
    // 0xb518e4: DecompressPointer r1
    //     0xb518e4: add             x1, x1, HEAP, lsl #32
    // 0xb518e8: mov             x0, x1
    // 0xb518ec: mov             x1, x2
    // 0xb518f0: mov             x2, x0
    // 0xb518f4: ldr             x3, [fp, #0x18]
    // 0xb518f8: r0 = paymentMethodCardGlassTheme()
    //     0xb518f8: bl              #0xb51918  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardGlassTheme
    // 0xb518fc: LeaveFrame
    //     0xb518fc: mov             SP, fp
    //     0xb51900: ldp             fp, lr, [SP], #0x10
    // 0xb51904: ret
    //     0xb51904: ret             
    // 0xb51908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb51908: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5190c: b               #0xb51848
    // 0xb51910: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb51910: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb51914: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb51914: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ paymentMethodCardGlassTheme(/* No info */) {
    // ** addr: 0xb51918, size: 0xb80
    // 0xb51918: EnterFrame
    //     0xb51918: stp             fp, lr, [SP, #-0x10]!
    //     0xb5191c: mov             fp, SP
    // 0xb51920: AllocStack(0x60)
    //     0xb51920: sub             SP, SP, #0x60
    // 0xb51924: SetupParameters(_PaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb51924: mov             x0, x1
    //     0xb51928: stur            x1, [fp, #-8]
    //     0xb5192c: mov             x1, x3
    //     0xb51930: stur            x2, [fp, #-0x10]
    //     0xb51934: stur            x3, [fp, #-0x18]
    // 0xb51938: CheckStackOverflow
    //     0xb51938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5193c: cmp             SP, x16
    //     0xb51940: b.ls            #0xb52484
    // 0xb51944: r1 = 2
    //     0xb51944: movz            x1, #0x2
    // 0xb51948: r0 = AllocateContext()
    //     0xb51948: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5194c: mov             x1, x0
    // 0xb51950: ldur            x0, [fp, #-8]
    // 0xb51954: stur            x1, [fp, #-0x20]
    // 0xb51958: StoreField: r1->field_f = r0
    //     0xb51958: stur            w0, [x1, #0xf]
    // 0xb5195c: ldur            x2, [fp, #-0x10]
    // 0xb51960: StoreField: r1->field_13 = r2
    //     0xb51960: stur            w2, [x1, #0x13]
    // 0xb51964: LoadField: r3 = r0->field_13
    //     0xb51964: ldur            w3, [x0, #0x13]
    // 0xb51968: DecompressPointer r3
    //     0xb51968: add             x3, x3, HEAP, lsl #32
    // 0xb5196c: r16 = Sentinel
    //     0xb5196c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb51970: cmp             w3, w16
    // 0xb51974: b.eq            #0xb5248c
    // 0xb51978: cmp             w2, NULL
    // 0xb5197c: b.ne            #0xb51988
    // 0xb51980: r0 = Null
    //     0xb51980: mov             x0, NULL
    // 0xb51984: b               #0xb51990
    // 0xb51988: LoadField: r0 = r2->field_7
    //     0xb51988: ldur            w0, [x2, #7]
    // 0xb5198c: DecompressPointer r0
    //     0xb5198c: add             x0, x0, HEAP, lsl #32
    // 0xb51990: r2 = LoadClassIdInstr(r3)
    //     0xb51990: ldur            x2, [x3, #-1]
    //     0xb51994: ubfx            x2, x2, #0xc, #0x14
    // 0xb51998: stp             x0, x3, [SP]
    // 0xb5199c: mov             x0, x2
    // 0xb519a0: mov             lr, x0
    // 0xb519a4: ldr             lr, [x21, lr, lsl #3]
    // 0xb519a8: blr             lr
    // 0xb519ac: tbnz            w0, #4, #0xb519bc
    // 0xb519b0: r0 = Instance_IconData
    //     0xb519b0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xb519b4: ldr             x0, [x0, #0x30]
    // 0xb519b8: b               #0xb519c4
    // 0xb519bc: r0 = Instance_IconData
    //     0xb519bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xb519c0: ldr             x0, [x0, #0x38]
    // 0xb519c4: ldur            x2, [fp, #-0x20]
    // 0xb519c8: ldur            x1, [fp, #-0x18]
    // 0xb519cc: stur            x0, [fp, #-8]
    // 0xb519d0: r0 = of()
    //     0xb519d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb519d4: LoadField: r1 = r0->field_5b
    //     0xb519d4: ldur            w1, [x0, #0x5b]
    // 0xb519d8: DecompressPointer r1
    //     0xb519d8: add             x1, x1, HEAP, lsl #32
    // 0xb519dc: stur            x1, [fp, #-0x10]
    // 0xb519e0: r0 = Icon()
    //     0xb519e0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb519e4: mov             x2, x0
    // 0xb519e8: ldur            x0, [fp, #-8]
    // 0xb519ec: stur            x2, [fp, #-0x28]
    // 0xb519f0: StoreField: r2->field_b = r0
    //     0xb519f0: stur            w0, [x2, #0xb]
    // 0xb519f4: ldur            x0, [fp, #-0x10]
    // 0xb519f8: StoreField: r2->field_23 = r0
    //     0xb519f8: stur            w0, [x2, #0x23]
    // 0xb519fc: ldur            x0, [fp, #-0x20]
    // 0xb51a00: LoadField: r1 = r0->field_13
    //     0xb51a00: ldur            w1, [x0, #0x13]
    // 0xb51a04: DecompressPointer r1
    //     0xb51a04: add             x1, x1, HEAP, lsl #32
    // 0xb51a08: cmp             w1, NULL
    // 0xb51a0c: b.ne            #0xb51a18
    // 0xb51a10: r1 = Null
    //     0xb51a10: mov             x1, NULL
    // 0xb51a14: b               #0xb51a24
    // 0xb51a18: LoadField: r3 = r1->field_b
    //     0xb51a18: ldur            w3, [x1, #0xb]
    // 0xb51a1c: DecompressPointer r3
    //     0xb51a1c: add             x3, x3, HEAP, lsl #32
    // 0xb51a20: mov             x1, x3
    // 0xb51a24: cmp             w1, NULL
    // 0xb51a28: b.ne            #0xb51a34
    // 0xb51a2c: r3 = ""
    //     0xb51a2c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb51a30: b               #0xb51a38
    // 0xb51a34: mov             x3, x1
    // 0xb51a38: ldur            x1, [fp, #-0x18]
    // 0xb51a3c: stur            x3, [fp, #-8]
    // 0xb51a40: r0 = of()
    //     0xb51a40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb51a44: LoadField: r1 = r0->field_87
    //     0xb51a44: ldur            w1, [x0, #0x87]
    // 0xb51a48: DecompressPointer r1
    //     0xb51a48: add             x1, x1, HEAP, lsl #32
    // 0xb51a4c: LoadField: r0 = r1->field_2b
    //     0xb51a4c: ldur            w0, [x1, #0x2b]
    // 0xb51a50: DecompressPointer r0
    //     0xb51a50: add             x0, x0, HEAP, lsl #32
    // 0xb51a54: r16 = 14.000000
    //     0xb51a54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb51a58: ldr             x16, [x16, #0x1d8]
    // 0xb51a5c: r30 = Instance_Color
    //     0xb51a5c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb51a60: stp             lr, x16, [SP]
    // 0xb51a64: mov             x1, x0
    // 0xb51a68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb51a68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb51a6c: ldr             x4, [x4, #0xaa0]
    // 0xb51a70: r0 = copyWith()
    //     0xb51a70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb51a74: stur            x0, [fp, #-0x10]
    // 0xb51a78: r0 = Text()
    //     0xb51a78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb51a7c: mov             x1, x0
    // 0xb51a80: ldur            x0, [fp, #-8]
    // 0xb51a84: stur            x1, [fp, #-0x30]
    // 0xb51a88: StoreField: r1->field_b = r0
    //     0xb51a88: stur            w0, [x1, #0xb]
    // 0xb51a8c: ldur            x0, [fp, #-0x10]
    // 0xb51a90: StoreField: r1->field_13 = r0
    //     0xb51a90: stur            w0, [x1, #0x13]
    // 0xb51a94: ldur            x2, [fp, #-0x20]
    // 0xb51a98: LoadField: r0 = r2->field_13
    //     0xb51a98: ldur            w0, [x2, #0x13]
    // 0xb51a9c: DecompressPointer r0
    //     0xb51a9c: add             x0, x0, HEAP, lsl #32
    // 0xb51aa0: cmp             w0, NULL
    // 0xb51aa4: b.ne            #0xb51ab0
    // 0xb51aa8: r0 = Null
    //     0xb51aa8: mov             x0, NULL
    // 0xb51aac: b               #0xb51abc
    // 0xb51ab0: LoadField: r3 = r0->field_7
    //     0xb51ab0: ldur            w3, [x0, #7]
    // 0xb51ab4: DecompressPointer r3
    //     0xb51ab4: add             x3, x3, HEAP, lsl #32
    // 0xb51ab8: mov             x0, x3
    // 0xb51abc: r3 = LoadClassIdInstr(r0)
    //     0xb51abc: ldur            x3, [x0, #-1]
    //     0xb51ac0: ubfx            x3, x3, #0xc, #0x14
    // 0xb51ac4: r16 = "partial-cod"
    //     0xb51ac4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xb51ac8: ldr             x16, [x16, #0x830]
    // 0xb51acc: stp             x16, x0, [SP]
    // 0xb51ad0: mov             x0, x3
    // 0xb51ad4: mov             lr, x0
    // 0xb51ad8: ldr             lr, [x21, lr, lsl #3]
    // 0xb51adc: blr             lr
    // 0xb51ae0: r1 = Instance_Color
    //     0xb51ae0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xb51ae4: ldr             x1, [x1, #0xb18]
    // 0xb51ae8: d0 = 0.080000
    //     0xb51ae8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xb51aec: ldr             d0, [x17, #0x798]
    // 0xb51af0: stur            x0, [fp, #-8]
    // 0xb51af4: r0 = withOpacity()
    //     0xb51af4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb51af8: stur            x0, [fp, #-0x10]
    // 0xb51afc: r0 = BoxDecoration()
    //     0xb51afc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb51b00: mov             x2, x0
    // 0xb51b04: ldur            x0, [fp, #-0x10]
    // 0xb51b08: stur            x2, [fp, #-0x38]
    // 0xb51b0c: StoreField: r2->field_7 = r0
    //     0xb51b0c: stur            w0, [x2, #7]
    // 0xb51b10: r0 = Instance_BorderRadius
    //     0xb51b10: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xb51b14: ldr             x0, [x0, #0xbe8]
    // 0xb51b18: StoreField: r2->field_13 = r0
    //     0xb51b18: stur            w0, [x2, #0x13]
    // 0xb51b1c: r0 = Instance_BoxShape
    //     0xb51b1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb51b20: ldr             x0, [x0, #0x80]
    // 0xb51b24: StoreField: r2->field_23 = r0
    //     0xb51b24: stur            w0, [x2, #0x23]
    // 0xb51b28: ldur            x1, [fp, #-0x18]
    // 0xb51b2c: r0 = of()
    //     0xb51b2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb51b30: LoadField: r1 = r0->field_87
    //     0xb51b30: ldur            w1, [x0, #0x87]
    // 0xb51b34: DecompressPointer r1
    //     0xb51b34: add             x1, x1, HEAP, lsl #32
    // 0xb51b38: LoadField: r0 = r1->field_7
    //     0xb51b38: ldur            w0, [x1, #7]
    // 0xb51b3c: DecompressPointer r0
    //     0xb51b3c: add             x0, x0, HEAP, lsl #32
    // 0xb51b40: r16 = 12.000000
    //     0xb51b40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb51b44: ldr             x16, [x16, #0x9e8]
    // 0xb51b48: r30 = Instance_Color
    //     0xb51b48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb51b4c: ldr             lr, [lr, #0x858]
    // 0xb51b50: stp             lr, x16, [SP]
    // 0xb51b54: mov             x1, x0
    // 0xb51b58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb51b58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb51b5c: ldr             x4, [x4, #0xaa0]
    // 0xb51b60: r0 = copyWith()
    //     0xb51b60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb51b64: stur            x0, [fp, #-0x10]
    // 0xb51b68: r0 = Text()
    //     0xb51b68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb51b6c: mov             x1, x0
    // 0xb51b70: r0 = "New"
    //     0xb51b70: add             x0, PP, #0x54, lsl #12  ; [pp+0x544d0] "New"
    //     0xb51b74: ldr             x0, [x0, #0x4d0]
    // 0xb51b78: stur            x1, [fp, #-0x40]
    // 0xb51b7c: StoreField: r1->field_b = r0
    //     0xb51b7c: stur            w0, [x1, #0xb]
    // 0xb51b80: ldur            x0, [fp, #-0x10]
    // 0xb51b84: StoreField: r1->field_13 = r0
    //     0xb51b84: stur            w0, [x1, #0x13]
    // 0xb51b88: r0 = Padding()
    //     0xb51b88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb51b8c: mov             x1, x0
    // 0xb51b90: r0 = Instance_EdgeInsets
    //     0xb51b90: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xb51b94: ldr             x0, [x0, #0xdb0]
    // 0xb51b98: stur            x1, [fp, #-0x10]
    // 0xb51b9c: StoreField: r1->field_f = r0
    //     0xb51b9c: stur            w0, [x1, #0xf]
    // 0xb51ba0: ldur            x0, [fp, #-0x40]
    // 0xb51ba4: StoreField: r1->field_b = r0
    //     0xb51ba4: stur            w0, [x1, #0xb]
    // 0xb51ba8: r0 = Container()
    //     0xb51ba8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb51bac: stur            x0, [fp, #-0x40]
    // 0xb51bb0: ldur            x16, [fp, #-0x38]
    // 0xb51bb4: ldur            lr, [fp, #-0x10]
    // 0xb51bb8: stp             lr, x16, [SP]
    // 0xb51bbc: mov             x1, x0
    // 0xb51bc0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb51bc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb51bc4: ldr             x4, [x4, #0x88]
    // 0xb51bc8: r0 = Container()
    //     0xb51bc8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb51bcc: r0 = Padding()
    //     0xb51bcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb51bd0: mov             x1, x0
    // 0xb51bd4: r0 = Instance_EdgeInsets
    //     0xb51bd4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb51bd8: ldr             x0, [x0, #0xc40]
    // 0xb51bdc: stur            x1, [fp, #-0x10]
    // 0xb51be0: StoreField: r1->field_f = r0
    //     0xb51be0: stur            w0, [x1, #0xf]
    // 0xb51be4: ldur            x0, [fp, #-0x40]
    // 0xb51be8: StoreField: r1->field_b = r0
    //     0xb51be8: stur            w0, [x1, #0xb]
    // 0xb51bec: r0 = Visibility()
    //     0xb51bec: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb51bf0: mov             x3, x0
    // 0xb51bf4: ldur            x0, [fp, #-0x10]
    // 0xb51bf8: stur            x3, [fp, #-0x38]
    // 0xb51bfc: StoreField: r3->field_b = r0
    //     0xb51bfc: stur            w0, [x3, #0xb]
    // 0xb51c00: r0 = Instance_SizedBox
    //     0xb51c00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb51c04: StoreField: r3->field_f = r0
    //     0xb51c04: stur            w0, [x3, #0xf]
    // 0xb51c08: ldur            x1, [fp, #-8]
    // 0xb51c0c: StoreField: r3->field_13 = r1
    //     0xb51c0c: stur            w1, [x3, #0x13]
    // 0xb51c10: r4 = false
    //     0xb51c10: add             x4, NULL, #0x30  ; false
    // 0xb51c14: ArrayStore: r3[0] = r4  ; List_4
    //     0xb51c14: stur            w4, [x3, #0x17]
    // 0xb51c18: StoreField: r3->field_1b = r4
    //     0xb51c18: stur            w4, [x3, #0x1b]
    // 0xb51c1c: StoreField: r3->field_1f = r4
    //     0xb51c1c: stur            w4, [x3, #0x1f]
    // 0xb51c20: StoreField: r3->field_23 = r4
    //     0xb51c20: stur            w4, [x3, #0x23]
    // 0xb51c24: StoreField: r3->field_27 = r4
    //     0xb51c24: stur            w4, [x3, #0x27]
    // 0xb51c28: StoreField: r3->field_2b = r4
    //     0xb51c28: stur            w4, [x3, #0x2b]
    // 0xb51c2c: r1 = Null
    //     0xb51c2c: mov             x1, NULL
    // 0xb51c30: r2 = 4
    //     0xb51c30: movz            x2, #0x4
    // 0xb51c34: r0 = AllocateArray()
    //     0xb51c34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb51c38: mov             x2, x0
    // 0xb51c3c: ldur            x0, [fp, #-0x30]
    // 0xb51c40: stur            x2, [fp, #-8]
    // 0xb51c44: StoreField: r2->field_f = r0
    //     0xb51c44: stur            w0, [x2, #0xf]
    // 0xb51c48: ldur            x0, [fp, #-0x38]
    // 0xb51c4c: StoreField: r2->field_13 = r0
    //     0xb51c4c: stur            w0, [x2, #0x13]
    // 0xb51c50: r1 = <Widget>
    //     0xb51c50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb51c54: r0 = AllocateGrowableArray()
    //     0xb51c54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb51c58: mov             x1, x0
    // 0xb51c5c: ldur            x0, [fp, #-8]
    // 0xb51c60: stur            x1, [fp, #-0x10]
    // 0xb51c64: StoreField: r1->field_f = r0
    //     0xb51c64: stur            w0, [x1, #0xf]
    // 0xb51c68: r2 = 4
    //     0xb51c68: movz            x2, #0x4
    // 0xb51c6c: StoreField: r1->field_b = r2
    //     0xb51c6c: stur            w2, [x1, #0xb]
    // 0xb51c70: r0 = Row()
    //     0xb51c70: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb51c74: mov             x2, x0
    // 0xb51c78: r0 = Instance_Axis
    //     0xb51c78: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb51c7c: stur            x2, [fp, #-0x30]
    // 0xb51c80: StoreField: r2->field_f = r0
    //     0xb51c80: stur            w0, [x2, #0xf]
    // 0xb51c84: r3 = Instance_MainAxisAlignment
    //     0xb51c84: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb51c88: ldr             x3, [x3, #0xa08]
    // 0xb51c8c: StoreField: r2->field_13 = r3
    //     0xb51c8c: stur            w3, [x2, #0x13]
    // 0xb51c90: r4 = Instance_MainAxisSize
    //     0xb51c90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb51c94: ldr             x4, [x4, #0xa10]
    // 0xb51c98: ArrayStore: r2[0] = r4  ; List_4
    //     0xb51c98: stur            w4, [x2, #0x17]
    // 0xb51c9c: r5 = Instance_CrossAxisAlignment
    //     0xb51c9c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb51ca0: ldr             x5, [x5, #0xa18]
    // 0xb51ca4: StoreField: r2->field_1b = r5
    //     0xb51ca4: stur            w5, [x2, #0x1b]
    // 0xb51ca8: r6 = Instance_VerticalDirection
    //     0xb51ca8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb51cac: ldr             x6, [x6, #0xa20]
    // 0xb51cb0: StoreField: r2->field_23 = r6
    //     0xb51cb0: stur            w6, [x2, #0x23]
    // 0xb51cb4: r7 = Instance_Clip
    //     0xb51cb4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb51cb8: ldr             x7, [x7, #0x38]
    // 0xb51cbc: StoreField: r2->field_2b = r7
    //     0xb51cbc: stur            w7, [x2, #0x2b]
    // 0xb51cc0: StoreField: r2->field_2f = rZR
    //     0xb51cc0: stur            xzr, [x2, #0x2f]
    // 0xb51cc4: ldur            x1, [fp, #-0x10]
    // 0xb51cc8: StoreField: r2->field_b = r1
    //     0xb51cc8: stur            w1, [x2, #0xb]
    // 0xb51ccc: ldur            x8, [fp, #-0x20]
    // 0xb51cd0: LoadField: r1 = r8->field_13
    //     0xb51cd0: ldur            w1, [x8, #0x13]
    // 0xb51cd4: DecompressPointer r1
    //     0xb51cd4: add             x1, x1, HEAP, lsl #32
    // 0xb51cd8: cmp             w1, NULL
    // 0xb51cdc: b.ne            #0xb51ce8
    // 0xb51ce0: r9 = Null
    //     0xb51ce0: mov             x9, NULL
    // 0xb51ce4: b               #0xb51d14
    // 0xb51ce8: LoadField: r9 = r1->field_f
    //     0xb51ce8: ldur            w9, [x1, #0xf]
    // 0xb51cec: DecompressPointer r9
    //     0xb51cec: add             x9, x9, HEAP, lsl #32
    // 0xb51cf0: cmp             w9, NULL
    // 0xb51cf4: b.ne            #0xb51d00
    // 0xb51cf8: r9 = Null
    //     0xb51cf8: mov             x9, NULL
    // 0xb51cfc: b               #0xb51d14
    // 0xb51d00: LoadField: r10 = r9->field_7
    //     0xb51d00: ldur            w10, [x9, #7]
    // 0xb51d04: cbnz            w10, #0xb51d10
    // 0xb51d08: r9 = false
    //     0xb51d08: add             x9, NULL, #0x30  ; false
    // 0xb51d0c: b               #0xb51d14
    // 0xb51d10: r9 = true
    //     0xb51d10: add             x9, NULL, #0x20  ; true
    // 0xb51d14: cmp             w9, NULL
    // 0xb51d18: b.ne            #0xb51d20
    // 0xb51d1c: r9 = false
    //     0xb51d1c: add             x9, NULL, #0x30  ; false
    // 0xb51d20: stur            x9, [fp, #-0x10]
    // 0xb51d24: cmp             w1, NULL
    // 0xb51d28: b.ne            #0xb51d34
    // 0xb51d2c: r1 = Null
    //     0xb51d2c: mov             x1, NULL
    // 0xb51d30: b               #0xb51d40
    // 0xb51d34: LoadField: r10 = r1->field_f
    //     0xb51d34: ldur            w10, [x1, #0xf]
    // 0xb51d38: DecompressPointer r10
    //     0xb51d38: add             x10, x10, HEAP, lsl #32
    // 0xb51d3c: mov             x1, x10
    // 0xb51d40: cmp             w1, NULL
    // 0xb51d44: b.ne            #0xb51d50
    // 0xb51d48: r10 = ""
    //     0xb51d48: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb51d4c: b               #0xb51d54
    // 0xb51d50: mov             x10, x1
    // 0xb51d54: ldur            x1, [fp, #-0x18]
    // 0xb51d58: stur            x10, [fp, #-8]
    // 0xb51d5c: r0 = of()
    //     0xb51d5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb51d60: LoadField: r1 = r0->field_87
    //     0xb51d60: ldur            w1, [x0, #0x87]
    // 0xb51d64: DecompressPointer r1
    //     0xb51d64: add             x1, x1, HEAP, lsl #32
    // 0xb51d68: LoadField: r2 = r1->field_7
    //     0xb51d68: ldur            w2, [x1, #7]
    // 0xb51d6c: DecompressPointer r2
    //     0xb51d6c: add             x2, x2, HEAP, lsl #32
    // 0xb51d70: ldur            x1, [fp, #-0x20]
    // 0xb51d74: stur            x2, [fp, #-0x38]
    // 0xb51d78: LoadField: r0 = r1->field_13
    //     0xb51d78: ldur            w0, [x1, #0x13]
    // 0xb51d7c: DecompressPointer r0
    //     0xb51d7c: add             x0, x0, HEAP, lsl #32
    // 0xb51d80: cmp             w0, NULL
    // 0xb51d84: b.ne            #0xb51d90
    // 0xb51d88: r0 = Null
    //     0xb51d88: mov             x0, NULL
    // 0xb51d8c: b               #0xb51d9c
    // 0xb51d90: LoadField: r3 = r0->field_7
    //     0xb51d90: ldur            w3, [x0, #7]
    // 0xb51d94: DecompressPointer r3
    //     0xb51d94: add             x3, x3, HEAP, lsl #32
    // 0xb51d98: mov             x0, x3
    // 0xb51d9c: r3 = LoadClassIdInstr(r0)
    //     0xb51d9c: ldur            x3, [x0, #-1]
    //     0xb51da0: ubfx            x3, x3, #0xc, #0x14
    // 0xb51da4: r16 = "cod"
    //     0xb51da4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xb51da8: ldr             x16, [x16, #0xa28]
    // 0xb51dac: stp             x16, x0, [SP]
    // 0xb51db0: mov             x0, x3
    // 0xb51db4: mov             lr, x0
    // 0xb51db8: ldr             lr, [x21, lr, lsl #3]
    // 0xb51dbc: blr             lr
    // 0xb51dc0: tbnz            w0, #4, #0xb51dd0
    // 0xb51dc4: r1 = Instance_Color
    //     0xb51dc4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb51dc8: ldr             x1, [x1, #0x50]
    // 0xb51dcc: b               #0xb51dd8
    // 0xb51dd0: r1 = Instance_Color
    //     0xb51dd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb51dd4: ldr             x1, [x1, #0x858]
    // 0xb51dd8: ldur            x2, [fp, #-0x20]
    // 0xb51ddc: ldur            x0, [fp, #-0x30]
    // 0xb51de0: ldur            x3, [fp, #-0x10]
    // 0xb51de4: ldur            x4, [fp, #-8]
    // 0xb51de8: r16 = 12.000000
    //     0xb51de8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb51dec: ldr             x16, [x16, #0x9e8]
    // 0xb51df0: stp             x1, x16, [SP]
    // 0xb51df4: ldur            x1, [fp, #-0x38]
    // 0xb51df8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb51df8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb51dfc: ldr             x4, [x4, #0xaa0]
    // 0xb51e00: r0 = copyWith()
    //     0xb51e00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb51e04: stur            x0, [fp, #-0x38]
    // 0xb51e08: r0 = Text()
    //     0xb51e08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb51e0c: mov             x1, x0
    // 0xb51e10: ldur            x0, [fp, #-8]
    // 0xb51e14: stur            x1, [fp, #-0x40]
    // 0xb51e18: StoreField: r1->field_b = r0
    //     0xb51e18: stur            w0, [x1, #0xb]
    // 0xb51e1c: ldur            x0, [fp, #-0x38]
    // 0xb51e20: StoreField: r1->field_13 = r0
    //     0xb51e20: stur            w0, [x1, #0x13]
    // 0xb51e24: r0 = Visibility()
    //     0xb51e24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb51e28: mov             x3, x0
    // 0xb51e2c: ldur            x0, [fp, #-0x40]
    // 0xb51e30: stur            x3, [fp, #-8]
    // 0xb51e34: StoreField: r3->field_b = r0
    //     0xb51e34: stur            w0, [x3, #0xb]
    // 0xb51e38: r0 = Instance_SizedBox
    //     0xb51e38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb51e3c: StoreField: r3->field_f = r0
    //     0xb51e3c: stur            w0, [x3, #0xf]
    // 0xb51e40: ldur            x1, [fp, #-0x10]
    // 0xb51e44: StoreField: r3->field_13 = r1
    //     0xb51e44: stur            w1, [x3, #0x13]
    // 0xb51e48: r4 = false
    //     0xb51e48: add             x4, NULL, #0x30  ; false
    // 0xb51e4c: ArrayStore: r3[0] = r4  ; List_4
    //     0xb51e4c: stur            w4, [x3, #0x17]
    // 0xb51e50: StoreField: r3->field_1b = r4
    //     0xb51e50: stur            w4, [x3, #0x1b]
    // 0xb51e54: StoreField: r3->field_1f = r4
    //     0xb51e54: stur            w4, [x3, #0x1f]
    // 0xb51e58: StoreField: r3->field_23 = r4
    //     0xb51e58: stur            w4, [x3, #0x23]
    // 0xb51e5c: StoreField: r3->field_27 = r4
    //     0xb51e5c: stur            w4, [x3, #0x27]
    // 0xb51e60: StoreField: r3->field_2b = r4
    //     0xb51e60: stur            w4, [x3, #0x2b]
    // 0xb51e64: r1 = Null
    //     0xb51e64: mov             x1, NULL
    // 0xb51e68: r2 = 4
    //     0xb51e68: movz            x2, #0x4
    // 0xb51e6c: r0 = AllocateArray()
    //     0xb51e6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb51e70: mov             x2, x0
    // 0xb51e74: ldur            x0, [fp, #-0x30]
    // 0xb51e78: stur            x2, [fp, #-0x10]
    // 0xb51e7c: StoreField: r2->field_f = r0
    //     0xb51e7c: stur            w0, [x2, #0xf]
    // 0xb51e80: ldur            x0, [fp, #-8]
    // 0xb51e84: StoreField: r2->field_13 = r0
    //     0xb51e84: stur            w0, [x2, #0x13]
    // 0xb51e88: r1 = <Widget>
    //     0xb51e88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb51e8c: r0 = AllocateGrowableArray()
    //     0xb51e8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb51e90: mov             x1, x0
    // 0xb51e94: ldur            x0, [fp, #-0x10]
    // 0xb51e98: stur            x1, [fp, #-8]
    // 0xb51e9c: StoreField: r1->field_f = r0
    //     0xb51e9c: stur            w0, [x1, #0xf]
    // 0xb51ea0: r0 = 4
    //     0xb51ea0: movz            x0, #0x4
    // 0xb51ea4: StoreField: r1->field_b = r0
    //     0xb51ea4: stur            w0, [x1, #0xb]
    // 0xb51ea8: r0 = Row()
    //     0xb51ea8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb51eac: mov             x2, x0
    // 0xb51eb0: r0 = Instance_Axis
    //     0xb51eb0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb51eb4: stur            x2, [fp, #-0x30]
    // 0xb51eb8: StoreField: r2->field_f = r0
    //     0xb51eb8: stur            w0, [x2, #0xf]
    // 0xb51ebc: r0 = Instance_MainAxisAlignment
    //     0xb51ebc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb51ec0: ldr             x0, [x0, #0xa8]
    // 0xb51ec4: StoreField: r2->field_13 = r0
    //     0xb51ec4: stur            w0, [x2, #0x13]
    // 0xb51ec8: r0 = Instance_MainAxisSize
    //     0xb51ec8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb51ecc: ldr             x0, [x0, #0xa10]
    // 0xb51ed0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb51ed0: stur            w0, [x2, #0x17]
    // 0xb51ed4: r1 = Instance_CrossAxisAlignment
    //     0xb51ed4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb51ed8: ldr             x1, [x1, #0xa18]
    // 0xb51edc: StoreField: r2->field_1b = r1
    //     0xb51edc: stur            w1, [x2, #0x1b]
    // 0xb51ee0: r3 = Instance_VerticalDirection
    //     0xb51ee0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb51ee4: ldr             x3, [x3, #0xa20]
    // 0xb51ee8: StoreField: r2->field_23 = r3
    //     0xb51ee8: stur            w3, [x2, #0x23]
    // 0xb51eec: r4 = Instance_Clip
    //     0xb51eec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb51ef0: ldr             x4, [x4, #0x38]
    // 0xb51ef4: StoreField: r2->field_2b = r4
    //     0xb51ef4: stur            w4, [x2, #0x2b]
    // 0xb51ef8: StoreField: r2->field_2f = rZR
    //     0xb51ef8: stur            xzr, [x2, #0x2f]
    // 0xb51efc: ldur            x1, [fp, #-8]
    // 0xb51f00: StoreField: r2->field_b = r1
    //     0xb51f00: stur            w1, [x2, #0xb]
    // 0xb51f04: ldur            x5, [fp, #-0x20]
    // 0xb51f08: LoadField: r1 = r5->field_13
    //     0xb51f08: ldur            w1, [x5, #0x13]
    // 0xb51f0c: DecompressPointer r1
    //     0xb51f0c: add             x1, x1, HEAP, lsl #32
    // 0xb51f10: cmp             w1, NULL
    // 0xb51f14: b.ne            #0xb51f20
    // 0xb51f18: r6 = Null
    //     0xb51f18: mov             x6, NULL
    // 0xb51f1c: b               #0xb51f4c
    // 0xb51f20: LoadField: r6 = r1->field_13
    //     0xb51f20: ldur            w6, [x1, #0x13]
    // 0xb51f24: DecompressPointer r6
    //     0xb51f24: add             x6, x6, HEAP, lsl #32
    // 0xb51f28: cmp             w6, NULL
    // 0xb51f2c: b.ne            #0xb51f38
    // 0xb51f30: r6 = Null
    //     0xb51f30: mov             x6, NULL
    // 0xb51f34: b               #0xb51f4c
    // 0xb51f38: LoadField: r7 = r6->field_7
    //     0xb51f38: ldur            w7, [x6, #7]
    // 0xb51f3c: cbnz            w7, #0xb51f48
    // 0xb51f40: r6 = false
    //     0xb51f40: add             x6, NULL, #0x30  ; false
    // 0xb51f44: b               #0xb51f4c
    // 0xb51f48: r6 = true
    //     0xb51f48: add             x6, NULL, #0x20  ; true
    // 0xb51f4c: cmp             w6, NULL
    // 0xb51f50: b.ne            #0xb51f58
    // 0xb51f54: r6 = false
    //     0xb51f54: add             x6, NULL, #0x30  ; false
    // 0xb51f58: stur            x6, [fp, #-0x10]
    // 0xb51f5c: cmp             w1, NULL
    // 0xb51f60: b.ne            #0xb51f6c
    // 0xb51f64: r1 = Null
    //     0xb51f64: mov             x1, NULL
    // 0xb51f68: b               #0xb51f78
    // 0xb51f6c: LoadField: r7 = r1->field_13
    //     0xb51f6c: ldur            w7, [x1, #0x13]
    // 0xb51f70: DecompressPointer r7
    //     0xb51f70: add             x7, x7, HEAP, lsl #32
    // 0xb51f74: mov             x1, x7
    // 0xb51f78: cmp             w1, NULL
    // 0xb51f7c: b.ne            #0xb51f88
    // 0xb51f80: r7 = ""
    //     0xb51f80: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb51f84: b               #0xb51f8c
    // 0xb51f88: mov             x7, x1
    // 0xb51f8c: ldur            x1, [fp, #-0x18]
    // 0xb51f90: stur            x7, [fp, #-8]
    // 0xb51f94: r0 = of()
    //     0xb51f94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb51f98: LoadField: r1 = r0->field_87
    //     0xb51f98: ldur            w1, [x0, #0x87]
    // 0xb51f9c: DecompressPointer r1
    //     0xb51f9c: add             x1, x1, HEAP, lsl #32
    // 0xb51fa0: LoadField: r0 = r1->field_2b
    //     0xb51fa0: ldur            w0, [x1, #0x2b]
    // 0xb51fa4: DecompressPointer r0
    //     0xb51fa4: add             x0, x0, HEAP, lsl #32
    // 0xb51fa8: stur            x0, [fp, #-0x38]
    // 0xb51fac: r1 = Instance_Color
    //     0xb51fac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb51fb0: d0 = 0.400000
    //     0xb51fb0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb51fb4: r0 = withOpacity()
    //     0xb51fb4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb51fb8: r16 = 12.000000
    //     0xb51fb8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb51fbc: ldr             x16, [x16, #0x9e8]
    // 0xb51fc0: stp             x16, x0, [SP]
    // 0xb51fc4: ldur            x1, [fp, #-0x38]
    // 0xb51fc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb51fc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb51fcc: ldr             x4, [x4, #0x9b8]
    // 0xb51fd0: r0 = copyWith()
    //     0xb51fd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb51fd4: stur            x0, [fp, #-0x38]
    // 0xb51fd8: r0 = TextSpan()
    //     0xb51fd8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb51fdc: mov             x1, x0
    // 0xb51fe0: ldur            x0, [fp, #-8]
    // 0xb51fe4: stur            x1, [fp, #-0x40]
    // 0xb51fe8: StoreField: r1->field_b = r0
    //     0xb51fe8: stur            w0, [x1, #0xb]
    // 0xb51fec: r0 = Instance__DeferringMouseCursor
    //     0xb51fec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb51ff0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb51ff0: stur            w0, [x1, #0x17]
    // 0xb51ff4: ldur            x2, [fp, #-0x38]
    // 0xb51ff8: StoreField: r1->field_7 = r2
    //     0xb51ff8: stur            w2, [x1, #7]
    // 0xb51ffc: r0 = TapGestureRecognizer()
    //     0xb51ffc: bl              #0x7ce314  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x88)
    // 0xb52000: stur            x0, [fp, #-8]
    // 0xb52004: r16 = -1.000000
    //     0xb52004: ldr             x16, [PP, #0x5bc0]  ; [pp+0x5bc0] -1
    // 0xb52008: stp             x16, NULL, [SP]
    // 0xb5200c: mov             x1, x0
    // 0xb52010: r4 = const [0, 0x3, 0x2, 0x1, postAcceptSlopTolerance, 0x2, supportedDevices, 0x1, null]
    //     0xb52010: add             x4, PP, #0x47, lsl #12  ; [pp+0x47c80] List(9) [0, 0x3, 0x2, 0x1, "postAcceptSlopTolerance", 0x2, "supportedDevices", 0x1, Null]
    //     0xb52014: ldr             x4, [x4, #0xc80]
    // 0xb52018: r0 = BaseTapGestureRecognizer()
    //     0xb52018: bl              #0x7ce238  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::BaseTapGestureRecognizer
    // 0xb5201c: ldur            x2, [fp, #-0x20]
    // 0xb52020: r1 = Function '<anonymous closure>':.
    //     0xb52020: add             x1, PP, #0x56, lsl #12  ; [pp+0x569e8] AnonymousClosure: (0xb52694), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardGlassTheme (0xb51918)
    //     0xb52024: ldr             x1, [x1, #0x9e8]
    // 0xb52028: r0 = AllocateClosure()
    //     0xb52028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5202c: ldur            x1, [fp, #-8]
    // 0xb52030: StoreField: r1->field_5f = r0
    //     0xb52030: stur            w0, [x1, #0x5f]
    //     0xb52034: ldurb           w16, [x1, #-1]
    //     0xb52038: ldurb           w17, [x0, #-1]
    //     0xb5203c: and             x16, x17, x16, lsr #2
    //     0xb52040: tst             x16, HEAP, lsr #32
    //     0xb52044: b.eq            #0xb5204c
    //     0xb52048: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb5204c: ldur            x2, [fp, #-0x20]
    // 0xb52050: LoadField: r0 = r2->field_13
    //     0xb52050: ldur            w0, [x2, #0x13]
    // 0xb52054: DecompressPointer r0
    //     0xb52054: add             x0, x0, HEAP, lsl #32
    // 0xb52058: cmp             w0, NULL
    // 0xb5205c: b.ne            #0xb52068
    // 0xb52060: r0 = Null
    //     0xb52060: mov             x0, NULL
    // 0xb52064: b               #0xb52074
    // 0xb52068: LoadField: r3 = r0->field_7
    //     0xb52068: ldur            w3, [x0, #7]
    // 0xb5206c: DecompressPointer r3
    //     0xb5206c: add             x3, x3, HEAP, lsl #32
    // 0xb52070: mov             x0, x3
    // 0xb52074: r3 = LoadClassIdInstr(r0)
    //     0xb52074: ldur            x3, [x0, #-1]
    //     0xb52078: ubfx            x3, x3, #0xc, #0x14
    // 0xb5207c: r16 = "partial-cod"
    //     0xb5207c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xb52080: ldr             x16, [x16, #0x830]
    // 0xb52084: stp             x16, x0, [SP]
    // 0xb52088: mov             x0, x3
    // 0xb5208c: mov             lr, x0
    // 0xb52090: ldr             lr, [x21, lr, lsl #3]
    // 0xb52094: blr             lr
    // 0xb52098: tbnz            w0, #4, #0xb520a8
    // 0xb5209c: r5 = "Know More"
    //     0xb5209c: add             x5, PP, #0x42, lsl #12  ; [pp+0x42f00] "Know More"
    //     0xb520a0: ldr             x5, [x5, #0xf00]
    // 0xb520a4: b               #0xb520ac
    // 0xb520a8: r5 = ""
    //     0xb520a8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb520ac: ldur            x2, [fp, #-0x20]
    // 0xb520b0: ldur            x4, [fp, #-0x10]
    // 0xb520b4: ldur            x3, [fp, #-0x40]
    // 0xb520b8: ldur            x0, [fp, #-8]
    // 0xb520bc: ldur            x1, [fp, #-0x18]
    // 0xb520c0: stur            x5, [fp, #-0x38]
    // 0xb520c4: r0 = of()
    //     0xb520c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb520c8: LoadField: r1 = r0->field_87
    //     0xb520c8: ldur            w1, [x0, #0x87]
    // 0xb520cc: DecompressPointer r1
    //     0xb520cc: add             x1, x1, HEAP, lsl #32
    // 0xb520d0: LoadField: r0 = r1->field_7
    //     0xb520d0: ldur            w0, [x1, #7]
    // 0xb520d4: DecompressPointer r0
    //     0xb520d4: add             x0, x0, HEAP, lsl #32
    // 0xb520d8: ldur            x1, [fp, #-0x18]
    // 0xb520dc: stur            x0, [fp, #-0x48]
    // 0xb520e0: r0 = of()
    //     0xb520e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb520e4: LoadField: r1 = r0->field_5b
    //     0xb520e4: ldur            w1, [x0, #0x5b]
    // 0xb520e8: DecompressPointer r1
    //     0xb520e8: add             x1, x1, HEAP, lsl #32
    // 0xb520ec: r16 = 12.000000
    //     0xb520ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb520f0: ldr             x16, [x16, #0x9e8]
    // 0xb520f4: stp             x1, x16, [SP, #8]
    // 0xb520f8: r16 = Instance_TextDecoration
    //     0xb520f8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb520fc: ldr             x16, [x16, #0x10]
    // 0xb52100: str             x16, [SP]
    // 0xb52104: ldur            x1, [fp, #-0x48]
    // 0xb52108: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb52108: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb5210c: ldr             x4, [x4, #0xe38]
    // 0xb52110: r0 = copyWith()
    //     0xb52110: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb52114: stur            x0, [fp, #-0x18]
    // 0xb52118: r0 = TextSpan()
    //     0xb52118: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb5211c: mov             x3, x0
    // 0xb52120: ldur            x0, [fp, #-0x38]
    // 0xb52124: stur            x3, [fp, #-0x48]
    // 0xb52128: StoreField: r3->field_b = r0
    //     0xb52128: stur            w0, [x3, #0xb]
    // 0xb5212c: ldur            x0, [fp, #-8]
    // 0xb52130: StoreField: r3->field_13 = r0
    //     0xb52130: stur            w0, [x3, #0x13]
    // 0xb52134: r0 = Instance_SystemMouseCursor
    //     0xb52134: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bfe0] Obj!SystemMouseCursor@d645c1
    //     0xb52138: ldr             x0, [x0, #0xfe0]
    // 0xb5213c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5213c: stur            w0, [x3, #0x17]
    // 0xb52140: ldur            x0, [fp, #-0x18]
    // 0xb52144: StoreField: r3->field_7 = r0
    //     0xb52144: stur            w0, [x3, #7]
    // 0xb52148: r1 = Null
    //     0xb52148: mov             x1, NULL
    // 0xb5214c: r2 = 6
    //     0xb5214c: movz            x2, #0x6
    // 0xb52150: r0 = AllocateArray()
    //     0xb52150: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb52154: mov             x2, x0
    // 0xb52158: ldur            x0, [fp, #-0x40]
    // 0xb5215c: stur            x2, [fp, #-8]
    // 0xb52160: StoreField: r2->field_f = r0
    //     0xb52160: stur            w0, [x2, #0xf]
    // 0xb52164: r16 = Instance_TextSpan
    //     0xb52164: add             x16, PP, #0x54, lsl #12  ; [pp+0x54358] Obj!TextSpan@d65581
    //     0xb52168: ldr             x16, [x16, #0x358]
    // 0xb5216c: StoreField: r2->field_13 = r16
    //     0xb5216c: stur            w16, [x2, #0x13]
    // 0xb52170: ldur            x0, [fp, #-0x48]
    // 0xb52174: ArrayStore: r2[0] = r0  ; List_4
    //     0xb52174: stur            w0, [x2, #0x17]
    // 0xb52178: r1 = <InlineSpan>
    //     0xb52178: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb5217c: ldr             x1, [x1, #0xe40]
    // 0xb52180: r0 = AllocateGrowableArray()
    //     0xb52180: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb52184: mov             x1, x0
    // 0xb52188: ldur            x0, [fp, #-8]
    // 0xb5218c: stur            x1, [fp, #-0x18]
    // 0xb52190: StoreField: r1->field_f = r0
    //     0xb52190: stur            w0, [x1, #0xf]
    // 0xb52194: r2 = 6
    //     0xb52194: movz            x2, #0x6
    // 0xb52198: StoreField: r1->field_b = r2
    //     0xb52198: stur            w2, [x1, #0xb]
    // 0xb5219c: r0 = TextSpan()
    //     0xb5219c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb521a0: mov             x1, x0
    // 0xb521a4: ldur            x0, [fp, #-0x18]
    // 0xb521a8: stur            x1, [fp, #-8]
    // 0xb521ac: StoreField: r1->field_f = r0
    //     0xb521ac: stur            w0, [x1, #0xf]
    // 0xb521b0: r0 = Instance__DeferringMouseCursor
    //     0xb521b0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb521b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb521b4: stur            w0, [x1, #0x17]
    // 0xb521b8: r0 = RichText()
    //     0xb521b8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb521bc: mov             x1, x0
    // 0xb521c0: ldur            x2, [fp, #-8]
    // 0xb521c4: stur            x0, [fp, #-8]
    // 0xb521c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb521c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb521cc: r0 = RichText()
    //     0xb521cc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb521d0: r0 = Padding()
    //     0xb521d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb521d4: mov             x1, x0
    // 0xb521d8: r0 = Instance_EdgeInsets
    //     0xb521d8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb521dc: ldr             x0, [x0, #0x770]
    // 0xb521e0: stur            x1, [fp, #-0x18]
    // 0xb521e4: StoreField: r1->field_f = r0
    //     0xb521e4: stur            w0, [x1, #0xf]
    // 0xb521e8: ldur            x0, [fp, #-8]
    // 0xb521ec: StoreField: r1->field_b = r0
    //     0xb521ec: stur            w0, [x1, #0xb]
    // 0xb521f0: r0 = Visibility()
    //     0xb521f0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb521f4: mov             x1, x0
    // 0xb521f8: ldur            x0, [fp, #-0x18]
    // 0xb521fc: stur            x1, [fp, #-8]
    // 0xb52200: StoreField: r1->field_b = r0
    //     0xb52200: stur            w0, [x1, #0xb]
    // 0xb52204: r2 = Instance_SizedBox
    //     0xb52204: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb52208: StoreField: r1->field_f = r2
    //     0xb52208: stur            w2, [x1, #0xf]
    // 0xb5220c: ldur            x0, [fp, #-0x10]
    // 0xb52210: StoreField: r1->field_13 = r0
    //     0xb52210: stur            w0, [x1, #0x13]
    // 0xb52214: r3 = false
    //     0xb52214: add             x3, NULL, #0x30  ; false
    // 0xb52218: ArrayStore: r1[0] = r3  ; List_4
    //     0xb52218: stur            w3, [x1, #0x17]
    // 0xb5221c: StoreField: r1->field_1b = r3
    //     0xb5221c: stur            w3, [x1, #0x1b]
    // 0xb52220: StoreField: r1->field_1f = r3
    //     0xb52220: stur            w3, [x1, #0x1f]
    // 0xb52224: StoreField: r1->field_23 = r3
    //     0xb52224: stur            w3, [x1, #0x23]
    // 0xb52228: StoreField: r1->field_27 = r3
    //     0xb52228: stur            w3, [x1, #0x27]
    // 0xb5222c: StoreField: r1->field_2b = r3
    //     0xb5222c: stur            w3, [x1, #0x2b]
    // 0xb52230: ldur            x4, [fp, #-0x20]
    // 0xb52234: LoadField: r0 = r4->field_13
    //     0xb52234: ldur            w0, [x4, #0x13]
    // 0xb52238: DecompressPointer r0
    //     0xb52238: add             x0, x0, HEAP, lsl #32
    // 0xb5223c: cmp             w0, NULL
    // 0xb52240: b.ne            #0xb5224c
    // 0xb52244: r0 = Null
    //     0xb52244: mov             x0, NULL
    // 0xb52248: b               #0xb52258
    // 0xb5224c: LoadField: r5 = r0->field_7
    //     0xb5224c: ldur            w5, [x0, #7]
    // 0xb52250: DecompressPointer r5
    //     0xb52250: add             x5, x5, HEAP, lsl #32
    // 0xb52254: mov             x0, x5
    // 0xb52258: ldur            x6, [fp, #-0x28]
    // 0xb5225c: ldur            x5, [fp, #-0x30]
    // 0xb52260: r7 = LoadClassIdInstr(r0)
    //     0xb52260: ldur            x7, [x0, #-1]
    //     0xb52264: ubfx            x7, x7, #0xc, #0x14
    // 0xb52268: r16 = "online"
    //     0xb52268: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xb5226c: ldr             x16, [x16, #0xa50]
    // 0xb52270: stp             x16, x0, [SP]
    // 0xb52274: mov             x0, x7
    // 0xb52278: mov             lr, x0
    // 0xb5227c: ldr             lr, [x21, lr, lsl #3]
    // 0xb52280: blr             lr
    // 0xb52284: stur            x0, [fp, #-0x10]
    // 0xb52288: r0 = Image()
    //     0xb52288: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xb5228c: stur            x0, [fp, #-0x18]
    // 0xb52290: r16 = 20.000000
    //     0xb52290: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb52294: ldr             x16, [x16, #0xac8]
    // 0xb52298: r30 = 20.000000
    //     0xb52298: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb5229c: ldr             lr, [lr, #0xac8]
    // 0xb522a0: stp             lr, x16, [SP]
    // 0xb522a4: mov             x1, x0
    // 0xb522a8: r2 = "assets/images/payment_gif.gif"
    //     0xb522a8: add             x2, PP, #0x54, lsl #12  ; [pp+0x54360] "assets/images/payment_gif.gif"
    //     0xb522ac: ldr             x2, [x2, #0x360]
    // 0xb522b0: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xb522b0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xb522b4: ldr             x4, [x4, #0x900]
    // 0xb522b8: r0 = Image.asset()
    //     0xb522b8: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xb522bc: r0 = Padding()
    //     0xb522bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb522c0: mov             x1, x0
    // 0xb522c4: r0 = Instance_EdgeInsets
    //     0xb522c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb522c8: ldr             x0, [x0, #0x990]
    // 0xb522cc: stur            x1, [fp, #-0x38]
    // 0xb522d0: StoreField: r1->field_f = r0
    //     0xb522d0: stur            w0, [x1, #0xf]
    // 0xb522d4: ldur            x0, [fp, #-0x18]
    // 0xb522d8: StoreField: r1->field_b = r0
    //     0xb522d8: stur            w0, [x1, #0xb]
    // 0xb522dc: r0 = Visibility()
    //     0xb522dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb522e0: mov             x3, x0
    // 0xb522e4: ldur            x0, [fp, #-0x38]
    // 0xb522e8: stur            x3, [fp, #-0x18]
    // 0xb522ec: StoreField: r3->field_b = r0
    //     0xb522ec: stur            w0, [x3, #0xb]
    // 0xb522f0: r0 = Instance_SizedBox
    //     0xb522f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb522f4: StoreField: r3->field_f = r0
    //     0xb522f4: stur            w0, [x3, #0xf]
    // 0xb522f8: ldur            x0, [fp, #-0x10]
    // 0xb522fc: StoreField: r3->field_13 = r0
    //     0xb522fc: stur            w0, [x3, #0x13]
    // 0xb52300: r0 = false
    //     0xb52300: add             x0, NULL, #0x30  ; false
    // 0xb52304: ArrayStore: r3[0] = r0  ; List_4
    //     0xb52304: stur            w0, [x3, #0x17]
    // 0xb52308: StoreField: r3->field_1b = r0
    //     0xb52308: stur            w0, [x3, #0x1b]
    // 0xb5230c: StoreField: r3->field_1f = r0
    //     0xb5230c: stur            w0, [x3, #0x1f]
    // 0xb52310: StoreField: r3->field_23 = r0
    //     0xb52310: stur            w0, [x3, #0x23]
    // 0xb52314: StoreField: r3->field_27 = r0
    //     0xb52314: stur            w0, [x3, #0x27]
    // 0xb52318: StoreField: r3->field_2b = r0
    //     0xb52318: stur            w0, [x3, #0x2b]
    // 0xb5231c: r1 = Null
    //     0xb5231c: mov             x1, NULL
    // 0xb52320: r2 = 6
    //     0xb52320: movz            x2, #0x6
    // 0xb52324: r0 = AllocateArray()
    //     0xb52324: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb52328: mov             x2, x0
    // 0xb5232c: ldur            x0, [fp, #-0x30]
    // 0xb52330: stur            x2, [fp, #-0x10]
    // 0xb52334: StoreField: r2->field_f = r0
    //     0xb52334: stur            w0, [x2, #0xf]
    // 0xb52338: ldur            x0, [fp, #-8]
    // 0xb5233c: StoreField: r2->field_13 = r0
    //     0xb5233c: stur            w0, [x2, #0x13]
    // 0xb52340: ldur            x0, [fp, #-0x18]
    // 0xb52344: ArrayStore: r2[0] = r0  ; List_4
    //     0xb52344: stur            w0, [x2, #0x17]
    // 0xb52348: r1 = <Widget>
    //     0xb52348: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5234c: r0 = AllocateGrowableArray()
    //     0xb5234c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb52350: mov             x1, x0
    // 0xb52354: ldur            x0, [fp, #-0x10]
    // 0xb52358: stur            x1, [fp, #-8]
    // 0xb5235c: StoreField: r1->field_f = r0
    //     0xb5235c: stur            w0, [x1, #0xf]
    // 0xb52360: r0 = 6
    //     0xb52360: movz            x0, #0x6
    // 0xb52364: StoreField: r1->field_b = r0
    //     0xb52364: stur            w0, [x1, #0xb]
    // 0xb52368: r0 = Column()
    //     0xb52368: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5236c: mov             x1, x0
    // 0xb52370: r0 = Instance_Axis
    //     0xb52370: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb52374: stur            x1, [fp, #-0x10]
    // 0xb52378: StoreField: r1->field_f = r0
    //     0xb52378: stur            w0, [x1, #0xf]
    // 0xb5237c: r0 = Instance_MainAxisAlignment
    //     0xb5237c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb52380: ldr             x0, [x0, #0xa08]
    // 0xb52384: StoreField: r1->field_13 = r0
    //     0xb52384: stur            w0, [x1, #0x13]
    // 0xb52388: r0 = Instance_MainAxisSize
    //     0xb52388: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5238c: ldr             x0, [x0, #0xa10]
    // 0xb52390: ArrayStore: r1[0] = r0  ; List_4
    //     0xb52390: stur            w0, [x1, #0x17]
    // 0xb52394: r0 = Instance_CrossAxisAlignment
    //     0xb52394: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb52398: ldr             x0, [x0, #0x890]
    // 0xb5239c: StoreField: r1->field_1b = r0
    //     0xb5239c: stur            w0, [x1, #0x1b]
    // 0xb523a0: r0 = Instance_VerticalDirection
    //     0xb523a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb523a4: ldr             x0, [x0, #0xa20]
    // 0xb523a8: StoreField: r1->field_23 = r0
    //     0xb523a8: stur            w0, [x1, #0x23]
    // 0xb523ac: r0 = Instance_Clip
    //     0xb523ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb523b0: ldr             x0, [x0, #0x38]
    // 0xb523b4: StoreField: r1->field_2b = r0
    //     0xb523b4: stur            w0, [x1, #0x2b]
    // 0xb523b8: StoreField: r1->field_2f = rZR
    //     0xb523b8: stur            xzr, [x1, #0x2f]
    // 0xb523bc: ldur            x0, [fp, #-8]
    // 0xb523c0: StoreField: r1->field_b = r0
    //     0xb523c0: stur            w0, [x1, #0xb]
    // 0xb523c4: r0 = ListTile()
    //     0xb523c4: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xb523c8: mov             x1, x0
    // 0xb523cc: ldur            x0, [fp, #-0x28]
    // 0xb523d0: stur            x1, [fp, #-8]
    // 0xb523d4: StoreField: r1->field_b = r0
    //     0xb523d4: stur            w0, [x1, #0xb]
    // 0xb523d8: ldur            x0, [fp, #-0x10]
    // 0xb523dc: StoreField: r1->field_f = r0
    //     0xb523dc: stur            w0, [x1, #0xf]
    // 0xb523e0: r0 = true
    //     0xb523e0: add             x0, NULL, #0x20  ; true
    // 0xb523e4: StoreField: r1->field_1f = r0
    //     0xb523e4: stur            w0, [x1, #0x1f]
    // 0xb523e8: r2 = Instance_EdgeInsets
    //     0xb523e8: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb523ec: ldr             x2, [x2, #0xf30]
    // 0xb523f0: StoreField: r1->field_47 = r2
    //     0xb523f0: stur            w2, [x1, #0x47]
    // 0xb523f4: StoreField: r1->field_4b = r0
    //     0xb523f4: stur            w0, [x1, #0x4b]
    // 0xb523f8: r2 = false
    //     0xb523f8: add             x2, NULL, #0x30  ; false
    // 0xb523fc: StoreField: r1->field_5f = r2
    //     0xb523fc: stur            w2, [x1, #0x5f]
    // 0xb52400: StoreField: r1->field_73 = r2
    //     0xb52400: stur            w2, [x1, #0x73]
    // 0xb52404: r3 = 12.000000
    //     0xb52404: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb52408: ldr             x3, [x3, #0x9e8]
    // 0xb5240c: StoreField: r1->field_83 = r3
    //     0xb5240c: stur            w3, [x1, #0x83]
    // 0xb52410: r3 = Instance_ListTileTitleAlignment
    //     0xb52410: add             x3, PP, #0x54, lsl #12  ; [pp+0x54368] Obj!ListTileTitleAlignment@d74361
    //     0xb52414: ldr             x3, [x3, #0x368]
    // 0xb52418: StoreField: r1->field_93 = r3
    //     0xb52418: stur            w3, [x1, #0x93]
    // 0xb5241c: StoreField: r1->field_97 = r0
    //     0xb5241c: stur            w0, [x1, #0x97]
    // 0xb52420: r0 = InkWell()
    //     0xb52420: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb52424: mov             x3, x0
    // 0xb52428: ldur            x0, [fp, #-8]
    // 0xb5242c: stur            x3, [fp, #-0x10]
    // 0xb52430: StoreField: r3->field_b = r0
    //     0xb52430: stur            w0, [x3, #0xb]
    // 0xb52434: ldur            x2, [fp, #-0x20]
    // 0xb52438: r1 = Function '<anonymous closure>':.
    //     0xb52438: add             x1, PP, #0x56, lsl #12  ; [pp+0x569f0] AnonymousClosure: (0xb52498), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardGlassTheme (0xb51918)
    //     0xb5243c: ldr             x1, [x1, #0x9f0]
    // 0xb52440: r0 = AllocateClosure()
    //     0xb52440: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb52444: mov             x1, x0
    // 0xb52448: ldur            x0, [fp, #-0x10]
    // 0xb5244c: StoreField: r0->field_f = r1
    //     0xb5244c: stur            w1, [x0, #0xf]
    // 0xb52450: r1 = true
    //     0xb52450: add             x1, NULL, #0x20  ; true
    // 0xb52454: StoreField: r0->field_43 = r1
    //     0xb52454: stur            w1, [x0, #0x43]
    // 0xb52458: r2 = Instance_BoxShape
    //     0xb52458: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5245c: ldr             x2, [x2, #0x80]
    // 0xb52460: StoreField: r0->field_47 = r2
    //     0xb52460: stur            w2, [x0, #0x47]
    // 0xb52464: StoreField: r0->field_6f = r1
    //     0xb52464: stur            w1, [x0, #0x6f]
    // 0xb52468: r2 = false
    //     0xb52468: add             x2, NULL, #0x30  ; false
    // 0xb5246c: StoreField: r0->field_73 = r2
    //     0xb5246c: stur            w2, [x0, #0x73]
    // 0xb52470: StoreField: r0->field_83 = r1
    //     0xb52470: stur            w1, [x0, #0x83]
    // 0xb52474: StoreField: r0->field_7b = r2
    //     0xb52474: stur            w2, [x0, #0x7b]
    // 0xb52478: LeaveFrame
    //     0xb52478: mov             SP, fp
    //     0xb5247c: ldp             fp, lr, [SP], #0x10
    // 0xb52480: ret
    //     0xb52480: ret             
    // 0xb52484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb52484: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb52488: b               #0xb51944
    // 0xb5248c: r9 = _currentSelectedPaymentMode
    //     0xb5248c: add             x9, PP, #0x56, lsl #12  ; [pp+0x569f8] Field <_PaymentMethodWidgetState@1545101574._currentSelectedPaymentMode@1545101574>: late (offset: 0x14)
    //     0xb52490: ldr             x9, [x9, #0x9f8]
    // 0xb52494: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb52494: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb52498, size: 0x60
    // 0xb52498: EnterFrame
    //     0xb52498: stp             fp, lr, [SP, #-0x10]!
    //     0xb5249c: mov             fp, SP
    // 0xb524a0: AllocStack(0x8)
    //     0xb524a0: sub             SP, SP, #8
    // 0xb524a4: SetupParameters()
    //     0xb524a4: ldr             x0, [fp, #0x10]
    //     0xb524a8: ldur            w2, [x0, #0x17]
    //     0xb524ac: add             x2, x2, HEAP, lsl #32
    // 0xb524b0: CheckStackOverflow
    //     0xb524b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb524b4: cmp             SP, x16
    //     0xb524b8: b.ls            #0xb524f0
    // 0xb524bc: LoadField: r0 = r2->field_f
    //     0xb524bc: ldur            w0, [x2, #0xf]
    // 0xb524c0: DecompressPointer r0
    //     0xb524c0: add             x0, x0, HEAP, lsl #32
    // 0xb524c4: stur            x0, [fp, #-8]
    // 0xb524c8: r1 = Function '<anonymous closure>':.
    //     0xb524c8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56a00] AnonymousClosure: (0xb524f8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/payment_method_widget.dart] _PaymentMethodWidgetState::paymentMethodCardGlassTheme (0xb51918)
    //     0xb524cc: ldr             x1, [x1, #0xa00]
    // 0xb524d0: r0 = AllocateClosure()
    //     0xb524d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb524d4: ldur            x1, [fp, #-8]
    // 0xb524d8: mov             x2, x0
    // 0xb524dc: r0 = setState()
    //     0xb524dc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb524e0: r0 = Null
    //     0xb524e0: mov             x0, NULL
    // 0xb524e4: LeaveFrame
    //     0xb524e4: mov             SP, fp
    //     0xb524e8: ldp             fp, lr, [SP], #0x10
    // 0xb524ec: ret
    //     0xb524ec: ret             
    // 0xb524f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb524f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb524f4: b               #0xb524bc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb524f8, size: 0x19c
    // 0xb524f8: EnterFrame
    //     0xb524f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb524fc: mov             fp, SP
    // 0xb52500: AllocStack(0x28)
    //     0xb52500: sub             SP, SP, #0x28
    // 0xb52504: SetupParameters()
    //     0xb52504: ldr             x0, [fp, #0x10]
    //     0xb52508: ldur            w1, [x0, #0x17]
    //     0xb5250c: add             x1, x1, HEAP, lsl #32
    //     0xb52510: stur            x1, [fp, #-8]
    // 0xb52514: CheckStackOverflow
    //     0xb52514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb52518: cmp             SP, x16
    //     0xb5251c: b.ls            #0xb52684
    // 0xb52520: LoadField: r2 = r1->field_f
    //     0xb52520: ldur            w2, [x1, #0xf]
    // 0xb52524: DecompressPointer r2
    //     0xb52524: add             x2, x2, HEAP, lsl #32
    // 0xb52528: LoadField: r3 = r2->field_b
    //     0xb52528: ldur            w3, [x2, #0xb]
    // 0xb5252c: DecompressPointer r3
    //     0xb5252c: add             x3, x3, HEAP, lsl #32
    // 0xb52530: cmp             w3, NULL
    // 0xb52534: b.eq            #0xb5268c
    // 0xb52538: LoadField: r4 = r1->field_13
    //     0xb52538: ldur            w4, [x1, #0x13]
    // 0xb5253c: DecompressPointer r4
    //     0xb5253c: add             x4, x4, HEAP, lsl #32
    // 0xb52540: cmp             w4, NULL
    // 0xb52544: b.ne            #0xb52550
    // 0xb52548: r0 = Null
    //     0xb52548: mov             x0, NULL
    // 0xb5254c: b               #0xb52558
    // 0xb52550: LoadField: r0 = r4->field_7
    //     0xb52550: ldur            w0, [x4, #7]
    // 0xb52554: DecompressPointer r0
    //     0xb52554: add             x0, x0, HEAP, lsl #32
    // 0xb52558: cmp             w0, NULL
    // 0xb5255c: b.ne            #0xb52568
    // 0xb52560: r5 = ""
    //     0xb52560: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb52564: b               #0xb5256c
    // 0xb52568: mov             x5, x0
    // 0xb5256c: mov             x0, x5
    // 0xb52570: StoreField: r3->field_1b = r0
    //     0xb52570: stur            w0, [x3, #0x1b]
    //     0xb52574: ldurb           w16, [x3, #-1]
    //     0xb52578: ldurb           w17, [x0, #-1]
    //     0xb5257c: and             x16, x17, x16, lsr #2
    //     0xb52580: tst             x16, HEAP, lsr #32
    //     0xb52584: b.eq            #0xb5258c
    //     0xb52588: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb5258c: mov             x0, x5
    // 0xb52590: StoreField: r2->field_13 = r0
    //     0xb52590: stur            w0, [x2, #0x13]
    //     0xb52594: ldurb           w16, [x2, #-1]
    //     0xb52598: ldurb           w17, [x0, #-1]
    //     0xb5259c: and             x16, x17, x16, lsr #2
    //     0xb525a0: tst             x16, HEAP, lsr #32
    //     0xb525a4: b.eq            #0xb525ac
    //     0xb525a8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb525ac: cmp             w4, NULL
    // 0xb525b0: b.ne            #0xb525bc
    // 0xb525b4: r0 = Null
    //     0xb525b4: mov             x0, NULL
    // 0xb525b8: b               #0xb525c4
    // 0xb525bc: LoadField: r0 = r4->field_7
    //     0xb525bc: ldur            w0, [x4, #7]
    // 0xb525c0: DecompressPointer r0
    //     0xb525c0: add             x0, x0, HEAP, lsl #32
    // 0xb525c4: LoadField: r2 = r3->field_f
    //     0xb525c4: ldur            w2, [x3, #0xf]
    // 0xb525c8: DecompressPointer r2
    //     0xb525c8: add             x2, x2, HEAP, lsl #32
    // 0xb525cc: stp             x0, x2, [SP, #0x10]
    // 0xb525d0: r16 = true
    //     0xb525d0: add             x16, NULL, #0x20  ; true
    // 0xb525d4: r30 = true
    //     0xb525d4: add             lr, NULL, #0x20  ; true
    // 0xb525d8: stp             lr, x16, [SP]
    // 0xb525dc: r4 = 0
    //     0xb525dc: movz            x4, #0
    // 0xb525e0: ldr             x0, [SP, #0x18]
    // 0xb525e4: r16 = UnlinkedCall_0x613b5c
    //     0xb525e4: add             x16, PP, #0x56, lsl #12  ; [pp+0x56a08] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb525e8: add             x16, x16, #0xa08
    // 0xb525ec: ldp             x5, lr, [x16]
    // 0xb525f0: blr             lr
    // 0xb525f4: ldur            x0, [fp, #-8]
    // 0xb525f8: LoadField: r1 = r0->field_f
    //     0xb525f8: ldur            w1, [x0, #0xf]
    // 0xb525fc: DecompressPointer r1
    //     0xb525fc: add             x1, x1, HEAP, lsl #32
    // 0xb52600: LoadField: r2 = r1->field_b
    //     0xb52600: ldur            w2, [x1, #0xb]
    // 0xb52604: DecompressPointer r2
    //     0xb52604: add             x2, x2, HEAP, lsl #32
    // 0xb52608: cmp             w2, NULL
    // 0xb5260c: b.eq            #0xb52690
    // 0xb52610: LoadField: r1 = r0->field_13
    //     0xb52610: ldur            w1, [x0, #0x13]
    // 0xb52614: DecompressPointer r1
    //     0xb52614: add             x1, x1, HEAP, lsl #32
    // 0xb52618: cmp             w1, NULL
    // 0xb5261c: b.ne            #0xb52628
    // 0xb52620: r0 = Null
    //     0xb52620: mov             x0, NULL
    // 0xb52624: b               #0xb52630
    // 0xb52628: LoadField: r0 = r1->field_7
    //     0xb52628: ldur            w0, [x1, #7]
    // 0xb5262c: DecompressPointer r0
    //     0xb5262c: add             x0, x0, HEAP, lsl #32
    // 0xb52630: cmp             w1, NULL
    // 0xb52634: b.ne            #0xb52640
    // 0xb52638: r1 = Null
    //     0xb52638: mov             x1, NULL
    // 0xb5263c: b               #0xb5264c
    // 0xb52640: LoadField: r3 = r1->field_13
    //     0xb52640: ldur            w3, [x1, #0x13]
    // 0xb52644: DecompressPointer r3
    //     0xb52644: add             x3, x3, HEAP, lsl #32
    // 0xb52648: mov             x1, x3
    // 0xb5264c: LoadField: r3 = r2->field_13
    //     0xb5264c: ldur            w3, [x2, #0x13]
    // 0xb52650: DecompressPointer r3
    //     0xb52650: add             x3, x3, HEAP, lsl #32
    // 0xb52654: stp             x0, x3, [SP, #8]
    // 0xb52658: str             x1, [SP]
    // 0xb5265c: r4 = 0
    //     0xb5265c: movz            x4, #0
    // 0xb52660: ldr             x0, [SP, #0x10]
    // 0xb52664: r16 = UnlinkedCall_0x613b5c
    //     0xb52664: add             x16, PP, #0x56, lsl #12  ; [pp+0x56a18] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb52668: add             x16, x16, #0xa18
    // 0xb5266c: ldp             x5, lr, [x16]
    // 0xb52670: blr             lr
    // 0xb52674: r0 = Null
    //     0xb52674: mov             x0, NULL
    // 0xb52678: LeaveFrame
    //     0xb52678: mov             SP, fp
    //     0xb5267c: ldp             fp, lr, [SP], #0x10
    // 0xb52680: ret
    //     0xb52680: ret             
    // 0xb52684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb52684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb52688: b               #0xb52520
    // 0xb5268c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5268c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb52690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb52690: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb52694, size: 0xd0
    // 0xb52694: EnterFrame
    //     0xb52694: stp             fp, lr, [SP, #-0x10]!
    //     0xb52698: mov             fp, SP
    // 0xb5269c: AllocStack(0x18)
    //     0xb5269c: sub             SP, SP, #0x18
    // 0xb526a0: SetupParameters()
    //     0xb526a0: ldr             x0, [fp, #0x10]
    //     0xb526a4: ldur            w1, [x0, #0x17]
    //     0xb526a8: add             x1, x1, HEAP, lsl #32
    //     0xb526ac: stur            x1, [fp, #-8]
    // 0xb526b0: CheckStackOverflow
    //     0xb526b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb526b4: cmp             SP, x16
    //     0xb526b8: b.ls            #0xb52758
    // 0xb526bc: LoadField: r0 = r1->field_13
    //     0xb526bc: ldur            w0, [x1, #0x13]
    // 0xb526c0: DecompressPointer r0
    //     0xb526c0: add             x0, x0, HEAP, lsl #32
    // 0xb526c4: cmp             w0, NULL
    // 0xb526c8: b.ne            #0xb526d4
    // 0xb526cc: r0 = Null
    //     0xb526cc: mov             x0, NULL
    // 0xb526d0: b               #0xb526e0
    // 0xb526d4: LoadField: r2 = r0->field_7
    //     0xb526d4: ldur            w2, [x0, #7]
    // 0xb526d8: DecompressPointer r2
    //     0xb526d8: add             x2, x2, HEAP, lsl #32
    // 0xb526dc: mov             x0, x2
    // 0xb526e0: r2 = LoadClassIdInstr(r0)
    //     0xb526e0: ldur            x2, [x0, #-1]
    //     0xb526e4: ubfx            x2, x2, #0xc, #0x14
    // 0xb526e8: r16 = "partial-cod"
    //     0xb526e8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xb526ec: ldr             x16, [x16, #0x830]
    // 0xb526f0: stp             x16, x0, [SP]
    // 0xb526f4: mov             x0, x2
    // 0xb526f8: mov             lr, x0
    // 0xb526fc: ldr             lr, [x21, lr, lsl #3]
    // 0xb52700: blr             lr
    // 0xb52704: tbnz            w0, #4, #0xb52748
    // 0xb52708: ldur            x0, [fp, #-8]
    // 0xb5270c: LoadField: r1 = r0->field_f
    //     0xb5270c: ldur            w1, [x0, #0xf]
    // 0xb52710: DecompressPointer r1
    //     0xb52710: add             x1, x1, HEAP, lsl #32
    // 0xb52714: LoadField: r0 = r1->field_b
    //     0xb52714: ldur            w0, [x1, #0xb]
    // 0xb52718: DecompressPointer r0
    //     0xb52718: add             x0, x0, HEAP, lsl #32
    // 0xb5271c: cmp             w0, NULL
    // 0xb52720: b.eq            #0xb52760
    // 0xb52724: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb52724: ldur            w1, [x0, #0x17]
    // 0xb52728: DecompressPointer r1
    //     0xb52728: add             x1, x1, HEAP, lsl #32
    // 0xb5272c: str             x1, [SP]
    // 0xb52730: r4 = 0
    //     0xb52730: movz            x4, #0
    // 0xb52734: ldr             x0, [SP]
    // 0xb52738: r16 = UnlinkedCall_0x613b5c
    //     0xb52738: add             x16, PP, #0x56, lsl #12  ; [pp+0x56a28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5273c: add             x16, x16, #0xa28
    // 0xb52740: ldp             x5, lr, [x16]
    // 0xb52744: blr             lr
    // 0xb52748: r0 = Null
    //     0xb52748: mov             x0, NULL
    // 0xb5274c: LeaveFrame
    //     0xb5274c: mov             SP, fp
    //     0xb52750: ldp             fp, lr, [SP], #0x10
    // 0xb52754: ret
    //     0xb52754: ret             
    // 0xb52758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb52758: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5275c: b               #0xb526bc
    // 0xb52760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb52760: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4093, size: 0x20, field offset: 0xc
class PaymentMethodWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ef28, size: 0x34
    // 0xc7ef28: EnterFrame
    //     0xc7ef28: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ef2c: mov             fp, SP
    // 0xc7ef30: mov             x0, x1
    // 0xc7ef34: r1 = <PaymentMethodWidget>
    //     0xc7ef34: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a28] TypeArguments: <PaymentMethodWidget>
    //     0xc7ef38: ldr             x1, [x1, #0xa28]
    // 0xc7ef3c: r0 = _PaymentMethodWidgetState()
    //     0xc7ef3c: bl              #0xc7ef5c  ; Allocate_PaymentMethodWidgetStateStub -> _PaymentMethodWidgetState (size=0x1c)
    // 0xc7ef40: r1 = Sentinel
    //     0xc7ef40: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7ef44: StoreField: r0->field_13 = r1
    //     0xc7ef44: stur            w1, [x0, #0x13]
    // 0xc7ef48: r1 = false
    //     0xc7ef48: add             x1, NULL, #0x30  ; false
    // 0xc7ef4c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7ef4c: stur            w1, [x0, #0x17]
    // 0xc7ef50: LeaveFrame
    //     0xc7ef50: mov             SP, fp
    //     0xc7ef54: ldp             fp, lr, [SP], #0x10
    // 0xc7ef58: ret
    //     0xc7ef58: ret             
  }
}
