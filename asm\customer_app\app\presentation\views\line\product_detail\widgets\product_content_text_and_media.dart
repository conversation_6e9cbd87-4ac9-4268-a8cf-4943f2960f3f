// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_content_text_and_media.dart

// class id: 1049558, size: 0x8
class :: {
}

// class id: 3226, size: 0x14, field offset: 0x14
class _ProductContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc00c88, size: 0x15c
    // 0xc00c88: EnterFrame
    //     0xc00c88: stp             fp, lr, [SP, #-0x10]!
    //     0xc00c8c: mov             fp, SP
    // 0xc00c90: AllocStack(0x28)
    //     0xc00c90: sub             SP, SP, #0x28
    // 0xc00c94: SetupParameters(_ProductContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xc00c94: stur            x1, [fp, #-8]
    // 0xc00c98: CheckStackOverflow
    //     0xc00c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00c9c: cmp             SP, x16
    //     0xc00ca0: b.ls            #0xc00dd8
    // 0xc00ca4: r1 = 1
    //     0xc00ca4: movz            x1, #0x1
    // 0xc00ca8: r0 = AllocateContext()
    //     0xc00ca8: bl              #0x16f6108  ; AllocateContextStub
    // 0xc00cac: mov             x1, x0
    // 0xc00cb0: ldur            x0, [fp, #-8]
    // 0xc00cb4: StoreField: r1->field_f = r0
    //     0xc00cb4: stur            w0, [x1, #0xf]
    // 0xc00cb8: LoadField: r2 = r0->field_b
    //     0xc00cb8: ldur            w2, [x0, #0xb]
    // 0xc00cbc: DecompressPointer r2
    //     0xc00cbc: add             x2, x2, HEAP, lsl #32
    // 0xc00cc0: cmp             w2, NULL
    // 0xc00cc4: b.eq            #0xc00de0
    // 0xc00cc8: LoadField: r0 = r2->field_b
    //     0xc00cc8: ldur            w0, [x2, #0xb]
    // 0xc00ccc: DecompressPointer r0
    //     0xc00ccc: add             x0, x0, HEAP, lsl #32
    // 0xc00cd0: LoadField: r3 = r0->field_b
    //     0xc00cd0: ldur            w3, [x0, #0xb]
    // 0xc00cd4: mov             x2, x1
    // 0xc00cd8: stur            x3, [fp, #-8]
    // 0xc00cdc: r1 = Function '<anonymous closure>':.
    //     0xc00cdc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b18] AnonymousClosure: (0xc00e04), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_content_text_and_media.dart] _ProductContentTextAndMediaState::build (0xc00c88)
    //     0xc00ce0: ldr             x1, [x1, #0xb18]
    // 0xc00ce4: r0 = AllocateClosure()
    //     0xc00ce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00ce8: stur            x0, [fp, #-0x10]
    // 0xc00cec: r0 = ListView()
    //     0xc00cec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc00cf0: stur            x0, [fp, #-0x18]
    // 0xc00cf4: r16 = true
    //     0xc00cf4: add             x16, NULL, #0x20  ; true
    // 0xc00cf8: r30 = Instance_NeverScrollableScrollPhysics
    //     0xc00cf8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc00cfc: ldr             lr, [lr, #0x1c8]
    // 0xc00d00: stp             lr, x16, [SP]
    // 0xc00d04: mov             x1, x0
    // 0xc00d08: ldur            x2, [fp, #-0x10]
    // 0xc00d0c: ldur            x3, [fp, #-8]
    // 0xc00d10: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xc00d10: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xc00d14: ldr             x4, [x4, #8]
    // 0xc00d18: r0 = ListView.builder()
    //     0xc00d18: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xc00d1c: r1 = Null
    //     0xc00d1c: mov             x1, NULL
    // 0xc00d20: r2 = 2
    //     0xc00d20: movz            x2, #0x2
    // 0xc00d24: r0 = AllocateArray()
    //     0xc00d24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc00d28: mov             x2, x0
    // 0xc00d2c: ldur            x0, [fp, #-0x18]
    // 0xc00d30: stur            x2, [fp, #-8]
    // 0xc00d34: StoreField: r2->field_f = r0
    //     0xc00d34: stur            w0, [x2, #0xf]
    // 0xc00d38: r1 = <Widget>
    //     0xc00d38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc00d3c: r0 = AllocateGrowableArray()
    //     0xc00d3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc00d40: mov             x1, x0
    // 0xc00d44: ldur            x0, [fp, #-8]
    // 0xc00d48: stur            x1, [fp, #-0x10]
    // 0xc00d4c: StoreField: r1->field_f = r0
    //     0xc00d4c: stur            w0, [x1, #0xf]
    // 0xc00d50: r0 = 2
    //     0xc00d50: movz            x0, #0x2
    // 0xc00d54: StoreField: r1->field_b = r0
    //     0xc00d54: stur            w0, [x1, #0xb]
    // 0xc00d58: r0 = Column()
    //     0xc00d58: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc00d5c: mov             x1, x0
    // 0xc00d60: r0 = Instance_Axis
    //     0xc00d60: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc00d64: stur            x1, [fp, #-8]
    // 0xc00d68: StoreField: r1->field_f = r0
    //     0xc00d68: stur            w0, [x1, #0xf]
    // 0xc00d6c: r0 = Instance_MainAxisAlignment
    //     0xc00d6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc00d70: ldr             x0, [x0, #0xa08]
    // 0xc00d74: StoreField: r1->field_13 = r0
    //     0xc00d74: stur            w0, [x1, #0x13]
    // 0xc00d78: r0 = Instance_MainAxisSize
    //     0xc00d78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc00d7c: ldr             x0, [x0, #0xa10]
    // 0xc00d80: ArrayStore: r1[0] = r0  ; List_4
    //     0xc00d80: stur            w0, [x1, #0x17]
    // 0xc00d84: r0 = Instance_CrossAxisAlignment
    //     0xc00d84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc00d88: ldr             x0, [x0, #0x890]
    // 0xc00d8c: StoreField: r1->field_1b = r0
    //     0xc00d8c: stur            w0, [x1, #0x1b]
    // 0xc00d90: r0 = Instance_VerticalDirection
    //     0xc00d90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc00d94: ldr             x0, [x0, #0xa20]
    // 0xc00d98: StoreField: r1->field_23 = r0
    //     0xc00d98: stur            w0, [x1, #0x23]
    // 0xc00d9c: r0 = Instance_Clip
    //     0xc00d9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc00da0: ldr             x0, [x0, #0x38]
    // 0xc00da4: StoreField: r1->field_2b = r0
    //     0xc00da4: stur            w0, [x1, #0x2b]
    // 0xc00da8: StoreField: r1->field_2f = rZR
    //     0xc00da8: stur            xzr, [x1, #0x2f]
    // 0xc00dac: ldur            x0, [fp, #-0x10]
    // 0xc00db0: StoreField: r1->field_b = r0
    //     0xc00db0: stur            w0, [x1, #0xb]
    // 0xc00db4: r0 = Padding()
    //     0xc00db4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc00db8: r1 = Instance_EdgeInsets
    //     0xc00db8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xc00dbc: ldr             x1, [x1, #0x110]
    // 0xc00dc0: StoreField: r0->field_f = r1
    //     0xc00dc0: stur            w1, [x0, #0xf]
    // 0xc00dc4: ldur            x1, [fp, #-8]
    // 0xc00dc8: StoreField: r0->field_b = r1
    //     0xc00dc8: stur            w1, [x0, #0xb]
    // 0xc00dcc: LeaveFrame
    //     0xc00dcc: mov             SP, fp
    //     0xc00dd0: ldp             fp, lr, [SP], #0x10
    // 0xc00dd4: ret
    //     0xc00dd4: ret             
    // 0xc00dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc00dd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00ddc: b               #0xc00ca4
    // 0xc00de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00de0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc00e04, size: 0xf60
    // 0xc00e04: EnterFrame
    //     0xc00e04: stp             fp, lr, [SP, #-0x10]!
    //     0xc00e08: mov             fp, SP
    // 0xc00e0c: AllocStack(0x80)
    //     0xc00e0c: sub             SP, SP, #0x80
    // 0xc00e10: SetupParameters()
    //     0xc00e10: ldr             x0, [fp, #0x20]
    //     0xc00e14: ldur            w2, [x0, #0x17]
    //     0xc00e18: add             x2, x2, HEAP, lsl #32
    //     0xc00e1c: stur            x2, [fp, #-0x10]
    // 0xc00e20: CheckStackOverflow
    //     0xc00e20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00e24: cmp             SP, x16
    //     0xc00e28: b.ls            #0xc01d04
    // 0xc00e2c: LoadField: r0 = r2->field_f
    //     0xc00e2c: ldur            w0, [x2, #0xf]
    // 0xc00e30: DecompressPointer r0
    //     0xc00e30: add             x0, x0, HEAP, lsl #32
    // 0xc00e34: LoadField: r1 = r0->field_b
    //     0xc00e34: ldur            w1, [x0, #0xb]
    // 0xc00e38: DecompressPointer r1
    //     0xc00e38: add             x1, x1, HEAP, lsl #32
    // 0xc00e3c: cmp             w1, NULL
    // 0xc00e40: b.eq            #0xc01d0c
    // 0xc00e44: LoadField: r3 = r1->field_b
    //     0xc00e44: ldur            w3, [x1, #0xb]
    // 0xc00e48: DecompressPointer r3
    //     0xc00e48: add             x3, x3, HEAP, lsl #32
    // 0xc00e4c: LoadField: r0 = r3->field_b
    //     0xc00e4c: ldur            w0, [x3, #0xb]
    // 0xc00e50: ldr             x1, [fp, #0x10]
    // 0xc00e54: r4 = LoadInt32Instr(r1)
    //     0xc00e54: sbfx            x4, x1, #1, #0x1f
    //     0xc00e58: tbz             w1, #0, #0xc00e60
    //     0xc00e5c: ldur            x4, [x1, #7]
    // 0xc00e60: stur            x4, [fp, #-8]
    // 0xc00e64: r1 = LoadInt32Instr(r0)
    //     0xc00e64: sbfx            x1, x0, #1, #0x1f
    // 0xc00e68: mov             x0, x1
    // 0xc00e6c: mov             x1, x4
    // 0xc00e70: cmp             x1, x0
    // 0xc00e74: b.hs            #0xc01d10
    // 0xc00e78: LoadField: r0 = r3->field_f
    //     0xc00e78: ldur            w0, [x3, #0xf]
    // 0xc00e7c: DecompressPointer r0
    //     0xc00e7c: add             x0, x0, HEAP, lsl #32
    // 0xc00e80: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc00e80: add             x16, x0, x4, lsl #2
    //     0xc00e84: ldur            w1, [x16, #0xf]
    // 0xc00e88: DecompressPointer r1
    //     0xc00e88: add             x1, x1, HEAP, lsl #32
    // 0xc00e8c: LoadField: r0 = r1->field_27
    //     0xc00e8c: ldur            w0, [x1, #0x27]
    // 0xc00e90: DecompressPointer r0
    //     0xc00e90: add             x0, x0, HEAP, lsl #32
    // 0xc00e94: cmp             w0, NULL
    // 0xc00e98: b.ne            #0xc00ea4
    // 0xc00e9c: r0 = Null
    //     0xc00e9c: mov             x0, NULL
    // 0xc00ea0: b               #0xc00eb0
    // 0xc00ea4: LoadField: r1 = r0->field_f
    //     0xc00ea4: ldur            w1, [x0, #0xf]
    // 0xc00ea8: DecompressPointer r1
    //     0xc00ea8: add             x1, x1, HEAP, lsl #32
    // 0xc00eac: mov             x0, x1
    // 0xc00eb0: r1 = LoadClassIdInstr(r0)
    //     0xc00eb0: ldur            x1, [x0, #-1]
    //     0xc00eb4: ubfx            x1, x1, #0xc, #0x14
    // 0xc00eb8: r16 = "left"
    //     0xc00eb8: ldr             x16, [PP, #0x6df8]  ; [pp+0x6df8] "left"
    // 0xc00ebc: stp             x16, x0, [SP]
    // 0xc00ec0: mov             x0, x1
    // 0xc00ec4: mov             lr, x0
    // 0xc00ec8: ldr             lr, [x21, lr, lsl #3]
    // 0xc00ecc: blr             lr
    // 0xc00ed0: tbnz            w0, #4, #0xc0157c
    // 0xc00ed4: ldur            x0, [fp, #-0x10]
    // 0xc00ed8: ldur            x1, [fp, #-8]
    // 0xc00edc: r0 = ImageHeaders.forImages()
    //     0xc00edc: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc00ee0: mov             x4, x0
    // 0xc00ee4: ldur            x3, [fp, #-0x10]
    // 0xc00ee8: stur            x4, [fp, #-0x20]
    // 0xc00eec: LoadField: r0 = r3->field_f
    //     0xc00eec: ldur            w0, [x3, #0xf]
    // 0xc00ef0: DecompressPointer r0
    //     0xc00ef0: add             x0, x0, HEAP, lsl #32
    // 0xc00ef4: LoadField: r1 = r0->field_b
    //     0xc00ef4: ldur            w1, [x0, #0xb]
    // 0xc00ef8: DecompressPointer r1
    //     0xc00ef8: add             x1, x1, HEAP, lsl #32
    // 0xc00efc: cmp             w1, NULL
    // 0xc00f00: b.eq            #0xc01d14
    // 0xc00f04: LoadField: r2 = r1->field_b
    //     0xc00f04: ldur            w2, [x1, #0xb]
    // 0xc00f08: DecompressPointer r2
    //     0xc00f08: add             x2, x2, HEAP, lsl #32
    // 0xc00f0c: LoadField: r0 = r2->field_b
    //     0xc00f0c: ldur            w0, [x2, #0xb]
    // 0xc00f10: r1 = LoadInt32Instr(r0)
    //     0xc00f10: sbfx            x1, x0, #1, #0x1f
    // 0xc00f14: mov             x0, x1
    // 0xc00f18: ldur            x1, [fp, #-8]
    // 0xc00f1c: cmp             x1, x0
    // 0xc00f20: b.hs            #0xc01d18
    // 0xc00f24: LoadField: r0 = r2->field_f
    //     0xc00f24: ldur            w0, [x2, #0xf]
    // 0xc00f28: DecompressPointer r0
    //     0xc00f28: add             x0, x0, HEAP, lsl #32
    // 0xc00f2c: ldur            x5, [fp, #-8]
    // 0xc00f30: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc00f30: add             x16, x0, x5, lsl #2
    //     0xc00f34: ldur            w1, [x16, #0xf]
    // 0xc00f38: DecompressPointer r1
    //     0xc00f38: add             x1, x1, HEAP, lsl #32
    // 0xc00f3c: LoadField: r0 = r1->field_13
    //     0xc00f3c: ldur            w0, [x1, #0x13]
    // 0xc00f40: DecompressPointer r0
    //     0xc00f40: add             x0, x0, HEAP, lsl #32
    // 0xc00f44: cmp             w0, NULL
    // 0xc00f48: b.ne            #0xc00f50
    // 0xc00f4c: r0 = ""
    //     0xc00f4c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc00f50: stur            x0, [fp, #-0x18]
    // 0xc00f54: r1 = Function '<anonymous closure>':.
    //     0xc00f54: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b20] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc00f58: ldr             x1, [x1, #0xb20]
    // 0xc00f5c: r2 = Null
    //     0xc00f5c: mov             x2, NULL
    // 0xc00f60: r0 = AllocateClosure()
    //     0xc00f60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00f64: r1 = Function '<anonymous closure>':.
    //     0xc00f64: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b28] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc00f68: ldr             x1, [x1, #0xb28]
    // 0xc00f6c: r2 = Null
    //     0xc00f6c: mov             x2, NULL
    // 0xc00f70: stur            x0, [fp, #-0x28]
    // 0xc00f74: r0 = AllocateClosure()
    //     0xc00f74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00f78: stur            x0, [fp, #-0x30]
    // 0xc00f7c: r0 = CachedNetworkImage()
    //     0xc00f7c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc00f80: stur            x0, [fp, #-0x38]
    // 0xc00f84: ldur            x16, [fp, #-0x20]
    // 0xc00f88: ldur            lr, [fp, #-0x28]
    // 0xc00f8c: stp             lr, x16, [SP, #0x10]
    // 0xc00f90: ldur            x16, [fp, #-0x30]
    // 0xc00f94: r30 = Instance_BoxFit
    //     0xc00f94: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc00f98: ldr             lr, [lr, #0xb18]
    // 0xc00f9c: stp             lr, x16, [SP]
    // 0xc00fa0: mov             x1, x0
    // 0xc00fa4: ldur            x2, [fp, #-0x18]
    // 0xc00fa8: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xc00fa8: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xc00fac: ldr             x4, [x4, #0x2b0]
    // 0xc00fb0: r0 = CachedNetworkImage()
    //     0xc00fb0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc00fb4: ldur            x2, [fp, #-0x10]
    // 0xc00fb8: LoadField: r0 = r2->field_f
    //     0xc00fb8: ldur            w0, [x2, #0xf]
    // 0xc00fbc: DecompressPointer r0
    //     0xc00fbc: add             x0, x0, HEAP, lsl #32
    // 0xc00fc0: LoadField: r1 = r0->field_b
    //     0xc00fc0: ldur            w1, [x0, #0xb]
    // 0xc00fc4: DecompressPointer r1
    //     0xc00fc4: add             x1, x1, HEAP, lsl #32
    // 0xc00fc8: cmp             w1, NULL
    // 0xc00fcc: b.eq            #0xc01d1c
    // 0xc00fd0: LoadField: r3 = r1->field_b
    //     0xc00fd0: ldur            w3, [x1, #0xb]
    // 0xc00fd4: DecompressPointer r3
    //     0xc00fd4: add             x3, x3, HEAP, lsl #32
    // 0xc00fd8: LoadField: r0 = r3->field_b
    //     0xc00fd8: ldur            w0, [x3, #0xb]
    // 0xc00fdc: r1 = LoadInt32Instr(r0)
    //     0xc00fdc: sbfx            x1, x0, #1, #0x1f
    // 0xc00fe0: mov             x0, x1
    // 0xc00fe4: ldur            x1, [fp, #-8]
    // 0xc00fe8: cmp             x1, x0
    // 0xc00fec: b.hs            #0xc01d20
    // 0xc00ff0: LoadField: r0 = r3->field_f
    //     0xc00ff0: ldur            w0, [x3, #0xf]
    // 0xc00ff4: DecompressPointer r0
    //     0xc00ff4: add             x0, x0, HEAP, lsl #32
    // 0xc00ff8: ldur            x3, [fp, #-8]
    // 0xc00ffc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc00ffc: add             x16, x0, x3, lsl #2
    //     0xc01000: ldur            w1, [x16, #0xf]
    // 0xc01004: DecompressPointer r1
    //     0xc01004: add             x1, x1, HEAP, lsl #32
    // 0xc01008: LoadField: r0 = r1->field_7
    //     0xc01008: ldur            w0, [x1, #7]
    // 0xc0100c: DecompressPointer r0
    //     0xc0100c: add             x0, x0, HEAP, lsl #32
    // 0xc01010: cmp             w0, NULL
    // 0xc01014: b.ne            #0xc0101c
    // 0xc01018: r0 = ""
    //     0xc01018: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0101c: ldr             x1, [fp, #0x18]
    // 0xc01020: stur            x0, [fp, #-0x18]
    // 0xc01024: r0 = of()
    //     0xc01024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc01028: LoadField: r1 = r0->field_87
    //     0xc01028: ldur            w1, [x0, #0x87]
    // 0xc0102c: DecompressPointer r1
    //     0xc0102c: add             x1, x1, HEAP, lsl #32
    // 0xc01030: LoadField: r0 = r1->field_7
    //     0xc01030: ldur            w0, [x1, #7]
    // 0xc01034: DecompressPointer r0
    //     0xc01034: add             x0, x0, HEAP, lsl #32
    // 0xc01038: stur            x0, [fp, #-0x20]
    // 0xc0103c: r1 = Instance_Color
    //     0xc0103c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc01040: d0 = 0.700000
    //     0xc01040: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc01044: ldr             d0, [x17, #0xf48]
    // 0xc01048: r0 = withOpacity()
    //     0xc01048: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0104c: r16 = 16.000000
    //     0xc0104c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc01050: ldr             x16, [x16, #0x188]
    // 0xc01054: stp             x0, x16, [SP]
    // 0xc01058: ldur            x1, [fp, #-0x20]
    // 0xc0105c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0105c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc01060: ldr             x4, [x4, #0xaa0]
    // 0xc01064: r0 = copyWith()
    //     0xc01064: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc01068: stur            x0, [fp, #-0x20]
    // 0xc0106c: r0 = Text()
    //     0xc0106c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc01070: mov             x1, x0
    // 0xc01074: ldur            x0, [fp, #-0x18]
    // 0xc01078: stur            x1, [fp, #-0x28]
    // 0xc0107c: StoreField: r1->field_b = r0
    //     0xc0107c: stur            w0, [x1, #0xb]
    // 0xc01080: ldur            x0, [fp, #-0x20]
    // 0xc01084: StoreField: r1->field_13 = r0
    //     0xc01084: stur            w0, [x1, #0x13]
    // 0xc01088: r0 = Padding()
    //     0xc01088: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0108c: mov             x2, x0
    // 0xc01090: r0 = Instance_EdgeInsets
    //     0xc01090: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xc01094: ldr             x0, [x0, #0xb30]
    // 0xc01098: stur            x2, [fp, #-0x18]
    // 0xc0109c: StoreField: r2->field_f = r0
    //     0xc0109c: stur            w0, [x2, #0xf]
    // 0xc010a0: ldur            x1, [fp, #-0x28]
    // 0xc010a4: StoreField: r2->field_b = r1
    //     0xc010a4: stur            w1, [x2, #0xb]
    // 0xc010a8: ldr             x1, [fp, #0x18]
    // 0xc010ac: r0 = of()
    //     0xc010ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc010b0: LoadField: r1 = r0->field_87
    //     0xc010b0: ldur            w1, [x0, #0x87]
    // 0xc010b4: DecompressPointer r1
    //     0xc010b4: add             x1, x1, HEAP, lsl #32
    // 0xc010b8: LoadField: r0 = r1->field_7
    //     0xc010b8: ldur            w0, [x1, #7]
    // 0xc010bc: DecompressPointer r0
    //     0xc010bc: add             x0, x0, HEAP, lsl #32
    // 0xc010c0: r16 = Instance_TextDecoration
    //     0xc010c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xc010c4: ldr             x16, [x16, #0xe30]
    // 0xc010c8: r30 = 6.000000
    //     0xc010c8: add             lr, PP, #0x27, lsl #12  ; [pp+0x27668] 6
    //     0xc010cc: ldr             lr, [lr, #0x668]
    // 0xc010d0: stp             lr, x16, [SP, #0x18]
    // 0xc010d4: r16 = Instance_Color
    //     0xc010d4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc010d8: r30 = 16.000000
    //     0xc010d8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc010dc: ldr             lr, [lr, #0x188]
    // 0xc010e0: stp             lr, x16, [SP, #8]
    // 0xc010e4: r16 = Instance_Color
    //     0xc010e4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc010e8: str             x16, [SP]
    // 0xc010ec: mov             x1, x0
    // 0xc010f0: r4 = const [0, 0x6, 0x5, 0x1, color, 0x5, decoration, 0x1, decorationColor, 0x3, decorationThickness, 0x2, fontSize, 0x4, null]
    //     0xc010f0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52b38] List(15) [0, 0x6, 0x5, 0x1, "color", 0x5, "decoration", 0x1, "decorationColor", 0x3, "decorationThickness", 0x2, "fontSize", 0x4, Null]
    //     0xc010f4: ldr             x4, [x4, #0xb38]
    // 0xc010f8: r0 = copyWith()
    //     0xc010f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc010fc: stur            x0, [fp, #-0x20]
    // 0xc01100: r0 = Text()
    //     0xc01100: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc01104: mov             x3, x0
    // 0xc01108: r2 = "------"
    //     0xc01108: add             x2, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xc0110c: ldr             x2, [x2, #0xb40]
    // 0xc01110: stur            x3, [fp, #-0x28]
    // 0xc01114: StoreField: r3->field_b = r2
    //     0xc01114: stur            w2, [x3, #0xb]
    // 0xc01118: ldur            x0, [fp, #-0x20]
    // 0xc0111c: StoreField: r3->field_13 = r0
    //     0xc0111c: stur            w0, [x3, #0x13]
    // 0xc01120: ldur            x2, [fp, #-0x10]
    // 0xc01124: LoadField: r0 = r2->field_f
    //     0xc01124: ldur            w0, [x2, #0xf]
    // 0xc01128: DecompressPointer r0
    //     0xc01128: add             x0, x0, HEAP, lsl #32
    // 0xc0112c: LoadField: r1 = r0->field_b
    //     0xc0112c: ldur            w1, [x0, #0xb]
    // 0xc01130: DecompressPointer r1
    //     0xc01130: add             x1, x1, HEAP, lsl #32
    // 0xc01134: cmp             w1, NULL
    // 0xc01138: b.eq            #0xc01d24
    // 0xc0113c: LoadField: r4 = r1->field_b
    //     0xc0113c: ldur            w4, [x1, #0xb]
    // 0xc01140: DecompressPointer r4
    //     0xc01140: add             x4, x4, HEAP, lsl #32
    // 0xc01144: LoadField: r0 = r4->field_b
    //     0xc01144: ldur            w0, [x4, #0xb]
    // 0xc01148: r1 = LoadInt32Instr(r0)
    //     0xc01148: sbfx            x1, x0, #1, #0x1f
    // 0xc0114c: mov             x0, x1
    // 0xc01150: ldur            x1, [fp, #-8]
    // 0xc01154: cmp             x1, x0
    // 0xc01158: b.hs            #0xc01d28
    // 0xc0115c: LoadField: r0 = r4->field_f
    //     0xc0115c: ldur            w0, [x4, #0xf]
    // 0xc01160: DecompressPointer r0
    //     0xc01160: add             x0, x0, HEAP, lsl #32
    // 0xc01164: ldur            x4, [fp, #-8]
    // 0xc01168: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc01168: add             x16, x0, x4, lsl #2
    //     0xc0116c: ldur            w1, [x16, #0xf]
    // 0xc01170: DecompressPointer r1
    //     0xc01170: add             x1, x1, HEAP, lsl #32
    // 0xc01174: LoadField: r0 = r1->field_b
    //     0xc01174: ldur            w0, [x1, #0xb]
    // 0xc01178: DecompressPointer r0
    //     0xc01178: add             x0, x0, HEAP, lsl #32
    // 0xc0117c: cmp             w0, NULL
    // 0xc01180: b.ne            #0xc01188
    // 0xc01184: r0 = ""
    //     0xc01184: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc01188: ldr             x1, [fp, #0x18]
    // 0xc0118c: stur            x0, [fp, #-0x20]
    // 0xc01190: r0 = of()
    //     0xc01190: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc01194: LoadField: r1 = r0->field_87
    //     0xc01194: ldur            w1, [x0, #0x87]
    // 0xc01198: DecompressPointer r1
    //     0xc01198: add             x1, x1, HEAP, lsl #32
    // 0xc0119c: LoadField: r0 = r1->field_2b
    //     0xc0119c: ldur            w0, [x1, #0x2b]
    // 0xc011a0: DecompressPointer r0
    //     0xc011a0: add             x0, x0, HEAP, lsl #32
    // 0xc011a4: r16 = 14.000000
    //     0xc011a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc011a8: ldr             x16, [x16, #0x1d8]
    // 0xc011ac: r30 = Instance_Color
    //     0xc011ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc011b0: stp             lr, x16, [SP]
    // 0xc011b4: mov             x1, x0
    // 0xc011b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc011b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc011bc: ldr             x4, [x4, #0xaa0]
    // 0xc011c0: r0 = copyWith()
    //     0xc011c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc011c4: stur            x0, [fp, #-0x30]
    // 0xc011c8: r0 = HtmlWidget()
    //     0xc011c8: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xc011cc: mov             x1, x0
    // 0xc011d0: ldur            x0, [fp, #-0x20]
    // 0xc011d4: stur            x1, [fp, #-0x40]
    // 0xc011d8: StoreField: r1->field_1f = r0
    //     0xc011d8: stur            w0, [x1, #0x1f]
    // 0xc011dc: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xc011dc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xc011e0: ldr             x3, [x3, #0x1e0]
    // 0xc011e4: StoreField: r1->field_23 = r3
    //     0xc011e4: stur            w3, [x1, #0x23]
    // 0xc011e8: r4 = Instance_ColumnMode
    //     0xc011e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xc011ec: ldr             x4, [x4, #0x1e8]
    // 0xc011f0: StoreField: r1->field_3b = r4
    //     0xc011f0: stur            w4, [x1, #0x3b]
    // 0xc011f4: ldur            x0, [fp, #-0x30]
    // 0xc011f8: StoreField: r1->field_3f = r0
    //     0xc011f8: stur            w0, [x1, #0x3f]
    // 0xc011fc: r0 = Padding()
    //     0xc011fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc01200: mov             x2, x0
    // 0xc01204: r5 = Instance_EdgeInsets
    //     0xc01204: add             x5, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xc01208: ldr             x5, [x5, #0xb30]
    // 0xc0120c: stur            x2, [fp, #-0x30]
    // 0xc01210: StoreField: r2->field_f = r5
    //     0xc01210: stur            w5, [x2, #0xf]
    // 0xc01214: ldur            x0, [fp, #-0x40]
    // 0xc01218: StoreField: r2->field_b = r0
    //     0xc01218: stur            w0, [x2, #0xb]
    // 0xc0121c: ldur            x3, [fp, #-0x10]
    // 0xc01220: LoadField: r0 = r3->field_f
    //     0xc01220: ldur            w0, [x3, #0xf]
    // 0xc01224: DecompressPointer r0
    //     0xc01224: add             x0, x0, HEAP, lsl #32
    // 0xc01228: LoadField: r1 = r0->field_b
    //     0xc01228: ldur            w1, [x0, #0xb]
    // 0xc0122c: DecompressPointer r1
    //     0xc0122c: add             x1, x1, HEAP, lsl #32
    // 0xc01230: cmp             w1, NULL
    // 0xc01234: b.eq            #0xc01d2c
    // 0xc01238: LoadField: r4 = r1->field_b
    //     0xc01238: ldur            w4, [x1, #0xb]
    // 0xc0123c: DecompressPointer r4
    //     0xc0123c: add             x4, x4, HEAP, lsl #32
    // 0xc01240: LoadField: r0 = r4->field_b
    //     0xc01240: ldur            w0, [x4, #0xb]
    // 0xc01244: r1 = LoadInt32Instr(r0)
    //     0xc01244: sbfx            x1, x0, #1, #0x1f
    // 0xc01248: mov             x0, x1
    // 0xc0124c: ldur            x1, [fp, #-8]
    // 0xc01250: cmp             x1, x0
    // 0xc01254: b.hs            #0xc01d30
    // 0xc01258: LoadField: r0 = r4->field_f
    //     0xc01258: ldur            w0, [x4, #0xf]
    // 0xc0125c: DecompressPointer r0
    //     0xc0125c: add             x0, x0, HEAP, lsl #32
    // 0xc01260: ldur            x1, [fp, #-8]
    // 0xc01264: ArrayLoad: r4 = r0[r1]  ; Unknown_4
    //     0xc01264: add             x16, x0, x1, lsl #2
    //     0xc01268: ldur            w4, [x16, #0xf]
    // 0xc0126c: DecompressPointer r4
    //     0xc0126c: add             x4, x4, HEAP, lsl #32
    // 0xc01270: LoadField: r0 = r4->field_f
    //     0xc01270: ldur            w0, [x4, #0xf]
    // 0xc01274: DecompressPointer r0
    //     0xc01274: add             x0, x0, HEAP, lsl #32
    // 0xc01278: cmp             w0, NULL
    // 0xc0127c: b.ne            #0xc01288
    // 0xc01280: r0 = Null
    //     0xc01280: mov             x0, NULL
    // 0xc01284: b               #0xc0129c
    // 0xc01288: LoadField: r4 = r0->field_7
    //     0xc01288: ldur            w4, [x0, #7]
    // 0xc0128c: cbnz            w4, #0xc01298
    // 0xc01290: r0 = false
    //     0xc01290: add             x0, NULL, #0x30  ; false
    // 0xc01294: b               #0xc0129c
    // 0xc01298: r0 = true
    //     0xc01298: add             x0, NULL, #0x20  ; true
    // 0xc0129c: cmp             w0, NULL
    // 0xc012a0: b.ne            #0xc012a8
    // 0xc012a4: r0 = false
    //     0xc012a4: add             x0, NULL, #0x30  ; false
    // 0xc012a8: stur            x0, [fp, #-0x20]
    // 0xc012ac: r16 = <EdgeInsets>
    //     0xc012ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc012b0: ldr             x16, [x16, #0xda0]
    // 0xc012b4: r30 = Instance_EdgeInsets
    //     0xc012b4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc012b8: ldr             lr, [lr, #0x1f0]
    // 0xc012bc: stp             lr, x16, [SP]
    // 0xc012c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc012c0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc012c4: r0 = all()
    //     0xc012c4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc012c8: stur            x0, [fp, #-0x40]
    // 0xc012cc: r16 = <RoundedRectangleBorder>
    //     0xc012cc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc012d0: ldr             x16, [x16, #0xf78]
    // 0xc012d4: r30 = Instance_RoundedRectangleBorder
    //     0xc012d4: add             lr, PP, #0x52, lsl #12  ; [pp+0x52b48] Obj!RoundedRectangleBorder@d5ac51
    //     0xc012d8: ldr             lr, [lr, #0xb48]
    // 0xc012dc: stp             lr, x16, [SP]
    // 0xc012e0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc012e0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc012e4: r0 = all()
    //     0xc012e4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc012e8: stur            x0, [fp, #-0x48]
    // 0xc012ec: r0 = ButtonStyle()
    //     0xc012ec: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc012f0: mov             x1, x0
    // 0xc012f4: ldur            x0, [fp, #-0x40]
    // 0xc012f8: stur            x1, [fp, #-0x50]
    // 0xc012fc: StoreField: r1->field_23 = r0
    //     0xc012fc: stur            w0, [x1, #0x23]
    // 0xc01300: ldur            x0, [fp, #-0x48]
    // 0xc01304: StoreField: r1->field_43 = r0
    //     0xc01304: stur            w0, [x1, #0x43]
    // 0xc01308: r0 = TextButtonThemeData()
    //     0xc01308: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc0130c: mov             x2, x0
    // 0xc01310: ldur            x0, [fp, #-0x50]
    // 0xc01314: stur            x2, [fp, #-0x40]
    // 0xc01318: StoreField: r2->field_7 = r0
    //     0xc01318: stur            w0, [x2, #7]
    // 0xc0131c: ldur            x6, [fp, #-0x10]
    // 0xc01320: LoadField: r0 = r6->field_f
    //     0xc01320: ldur            w0, [x6, #0xf]
    // 0xc01324: DecompressPointer r0
    //     0xc01324: add             x0, x0, HEAP, lsl #32
    // 0xc01328: LoadField: r1 = r0->field_b
    //     0xc01328: ldur            w1, [x0, #0xb]
    // 0xc0132c: DecompressPointer r1
    //     0xc0132c: add             x1, x1, HEAP, lsl #32
    // 0xc01330: cmp             w1, NULL
    // 0xc01334: b.eq            #0xc01d34
    // 0xc01338: LoadField: r3 = r1->field_b
    //     0xc01338: ldur            w3, [x1, #0xb]
    // 0xc0133c: DecompressPointer r3
    //     0xc0133c: add             x3, x3, HEAP, lsl #32
    // 0xc01340: LoadField: r0 = r3->field_b
    //     0xc01340: ldur            w0, [x3, #0xb]
    // 0xc01344: r1 = LoadInt32Instr(r0)
    //     0xc01344: sbfx            x1, x0, #1, #0x1f
    // 0xc01348: mov             x0, x1
    // 0xc0134c: ldur            x1, [fp, #-8]
    // 0xc01350: cmp             x1, x0
    // 0xc01354: b.hs            #0xc01d38
    // 0xc01358: LoadField: r0 = r3->field_f
    //     0xc01358: ldur            w0, [x3, #0xf]
    // 0xc0135c: DecompressPointer r0
    //     0xc0135c: add             x0, x0, HEAP, lsl #32
    // 0xc01360: ldur            x7, [fp, #-8]
    // 0xc01364: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xc01364: add             x16, x0, x7, lsl #2
    //     0xc01368: ldur            w1, [x16, #0xf]
    // 0xc0136c: DecompressPointer r1
    //     0xc0136c: add             x1, x1, HEAP, lsl #32
    // 0xc01370: LoadField: r0 = r1->field_f
    //     0xc01370: ldur            w0, [x1, #0xf]
    // 0xc01374: DecompressPointer r0
    //     0xc01374: add             x0, x0, HEAP, lsl #32
    // 0xc01378: cmp             w0, NULL
    // 0xc0137c: b.ne            #0xc01384
    // 0xc01380: r0 = ""
    //     0xc01380: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc01384: ldur            x6, [fp, #-0x38]
    // 0xc01388: ldur            x5, [fp, #-0x18]
    // 0xc0138c: ldur            x4, [fp, #-0x28]
    // 0xc01390: ldur            x1, [fp, #-0x30]
    // 0xc01394: ldur            x3, [fp, #-0x20]
    // 0xc01398: r7 = LoadClassIdInstr(r0)
    //     0xc01398: ldur            x7, [x0, #-1]
    //     0xc0139c: ubfx            x7, x7, #0xc, #0x14
    // 0xc013a0: str             x0, [SP]
    // 0xc013a4: mov             x0, x7
    // 0xc013a8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc013a8: sub             lr, x0, #1, lsl #12
    //     0xc013ac: ldr             lr, [x21, lr, lsl #3]
    //     0xc013b0: blr             lr
    // 0xc013b4: ldr             x1, [fp, #0x18]
    // 0xc013b8: stur            x0, [fp, #-0x48]
    // 0xc013bc: r0 = of()
    //     0xc013bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc013c0: LoadField: r1 = r0->field_87
    //     0xc013c0: ldur            w1, [x0, #0x87]
    // 0xc013c4: DecompressPointer r1
    //     0xc013c4: add             x1, x1, HEAP, lsl #32
    // 0xc013c8: LoadField: r0 = r1->field_7
    //     0xc013c8: ldur            w0, [x1, #7]
    // 0xc013cc: DecompressPointer r0
    //     0xc013cc: add             x0, x0, HEAP, lsl #32
    // 0xc013d0: r16 = Instance_Color
    //     0xc013d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc013d4: r30 = 12.000000
    //     0xc013d4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc013d8: ldr             lr, [lr, #0x9e8]
    // 0xc013dc: stp             lr, x16, [SP]
    // 0xc013e0: mov             x1, x0
    // 0xc013e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc013e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc013e8: ldr             x4, [x4, #0x9b8]
    // 0xc013ec: r0 = copyWith()
    //     0xc013ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc013f0: stur            x0, [fp, #-0x50]
    // 0xc013f4: r0 = Text()
    //     0xc013f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc013f8: mov             x3, x0
    // 0xc013fc: ldur            x0, [fp, #-0x48]
    // 0xc01400: stur            x3, [fp, #-0x58]
    // 0xc01404: StoreField: r3->field_b = r0
    //     0xc01404: stur            w0, [x3, #0xb]
    // 0xc01408: ldur            x0, [fp, #-0x50]
    // 0xc0140c: StoreField: r3->field_13 = r0
    //     0xc0140c: stur            w0, [x3, #0x13]
    // 0xc01410: r1 = Function '<anonymous closure>':.
    //     0xc01410: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b50] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc01414: ldr             x1, [x1, #0xb50]
    // 0xc01418: r2 = Null
    //     0xc01418: mov             x2, NULL
    // 0xc0141c: r0 = AllocateClosure()
    //     0xc0141c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01420: stur            x0, [fp, #-0x48]
    // 0xc01424: r0 = TextButton()
    //     0xc01424: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc01428: mov             x1, x0
    // 0xc0142c: ldur            x0, [fp, #-0x48]
    // 0xc01430: stur            x1, [fp, #-0x50]
    // 0xc01434: StoreField: r1->field_b = r0
    //     0xc01434: stur            w0, [x1, #0xb]
    // 0xc01438: r0 = false
    //     0xc01438: add             x0, NULL, #0x30  ; false
    // 0xc0143c: StoreField: r1->field_27 = r0
    //     0xc0143c: stur            w0, [x1, #0x27]
    // 0xc01440: r8 = true
    //     0xc01440: add             x8, NULL, #0x20  ; true
    // 0xc01444: StoreField: r1->field_2f = r8
    //     0xc01444: stur            w8, [x1, #0x2f]
    // 0xc01448: ldur            x2, [fp, #-0x58]
    // 0xc0144c: StoreField: r1->field_37 = r2
    //     0xc0144c: stur            w2, [x1, #0x37]
    // 0xc01450: r0 = TextButtonTheme()
    //     0xc01450: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc01454: mov             x1, x0
    // 0xc01458: ldur            x0, [fp, #-0x40]
    // 0xc0145c: stur            x1, [fp, #-0x48]
    // 0xc01460: StoreField: r1->field_f = r0
    //     0xc01460: stur            w0, [x1, #0xf]
    // 0xc01464: ldur            x0, [fp, #-0x50]
    // 0xc01468: StoreField: r1->field_b = r0
    //     0xc01468: stur            w0, [x1, #0xb]
    // 0xc0146c: r0 = Padding()
    //     0xc0146c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc01470: r9 = Instance_EdgeInsets
    //     0xc01470: add             x9, PP, #0x52, lsl #12  ; [pp+0x52b58] Obj!EdgeInsets@d588b1
    //     0xc01474: ldr             x9, [x9, #0xb58]
    // 0xc01478: stur            x0, [fp, #-0x40]
    // 0xc0147c: StoreField: r0->field_f = r9
    //     0xc0147c: stur            w9, [x0, #0xf]
    // 0xc01480: ldur            x1, [fp, #-0x48]
    // 0xc01484: StoreField: r0->field_b = r1
    //     0xc01484: stur            w1, [x0, #0xb]
    // 0xc01488: r0 = Visibility()
    //     0xc01488: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0148c: mov             x3, x0
    // 0xc01490: ldur            x0, [fp, #-0x40]
    // 0xc01494: stur            x3, [fp, #-0x48]
    // 0xc01498: StoreField: r3->field_b = r0
    //     0xc01498: stur            w0, [x3, #0xb]
    // 0xc0149c: r10 = Instance_SizedBox
    //     0xc0149c: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc014a0: StoreField: r3->field_f = r10
    //     0xc014a0: stur            w10, [x3, #0xf]
    // 0xc014a4: ldur            x0, [fp, #-0x20]
    // 0xc014a8: StoreField: r3->field_13 = r0
    //     0xc014a8: stur            w0, [x3, #0x13]
    // 0xc014ac: r11 = false
    //     0xc014ac: add             x11, NULL, #0x30  ; false
    // 0xc014b0: ArrayStore: r3[0] = r11  ; List_4
    //     0xc014b0: stur            w11, [x3, #0x17]
    // 0xc014b4: StoreField: r3->field_1b = r11
    //     0xc014b4: stur            w11, [x3, #0x1b]
    // 0xc014b8: StoreField: r3->field_1f = r11
    //     0xc014b8: stur            w11, [x3, #0x1f]
    // 0xc014bc: StoreField: r3->field_23 = r11
    //     0xc014bc: stur            w11, [x3, #0x23]
    // 0xc014c0: StoreField: r3->field_27 = r11
    //     0xc014c0: stur            w11, [x3, #0x27]
    // 0xc014c4: StoreField: r3->field_2b = r11
    //     0xc014c4: stur            w11, [x3, #0x2b]
    // 0xc014c8: r1 = Null
    //     0xc014c8: mov             x1, NULL
    // 0xc014cc: r2 = 10
    //     0xc014cc: movz            x2, #0xa
    // 0xc014d0: r0 = AllocateArray()
    //     0xc014d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc014d4: mov             x2, x0
    // 0xc014d8: ldur            x0, [fp, #-0x38]
    // 0xc014dc: stur            x2, [fp, #-0x20]
    // 0xc014e0: StoreField: r2->field_f = r0
    //     0xc014e0: stur            w0, [x2, #0xf]
    // 0xc014e4: ldur            x0, [fp, #-0x18]
    // 0xc014e8: StoreField: r2->field_13 = r0
    //     0xc014e8: stur            w0, [x2, #0x13]
    // 0xc014ec: ldur            x0, [fp, #-0x28]
    // 0xc014f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc014f0: stur            w0, [x2, #0x17]
    // 0xc014f4: ldur            x0, [fp, #-0x30]
    // 0xc014f8: StoreField: r2->field_1b = r0
    //     0xc014f8: stur            w0, [x2, #0x1b]
    // 0xc014fc: ldur            x0, [fp, #-0x48]
    // 0xc01500: StoreField: r2->field_1f = r0
    //     0xc01500: stur            w0, [x2, #0x1f]
    // 0xc01504: r1 = <Widget>
    //     0xc01504: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc01508: r0 = AllocateGrowableArray()
    //     0xc01508: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0150c: mov             x1, x0
    // 0xc01510: ldur            x0, [fp, #-0x20]
    // 0xc01514: stur            x1, [fp, #-0x18]
    // 0xc01518: StoreField: r1->field_f = r0
    //     0xc01518: stur            w0, [x1, #0xf]
    // 0xc0151c: r12 = 10
    //     0xc0151c: movz            x12, #0xa
    // 0xc01520: StoreField: r1->field_b = r12
    //     0xc01520: stur            w12, [x1, #0xb]
    // 0xc01524: r0 = Column()
    //     0xc01524: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc01528: r13 = Instance_Axis
    //     0xc01528: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0152c: StoreField: r0->field_f = r13
    //     0xc0152c: stur            w13, [x0, #0xf]
    // 0xc01530: r14 = Instance_MainAxisAlignment
    //     0xc01530: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc01534: ldr             x14, [x14, #0xa08]
    // 0xc01538: StoreField: r0->field_13 = r14
    //     0xc01538: stur            w14, [x0, #0x13]
    // 0xc0153c: r19 = Instance_MainAxisSize
    //     0xc0153c: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc01540: ldr             x19, [x19, #0xa10]
    // 0xc01544: ArrayStore: r0[0] = r19  ; List_4
    //     0xc01544: stur            w19, [x0, #0x17]
    // 0xc01548: r20 = Instance_CrossAxisAlignment
    //     0xc01548: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0154c: ldr             x20, [x20, #0x890]
    // 0xc01550: StoreField: r0->field_1b = r20
    //     0xc01550: stur            w20, [x0, #0x1b]
    // 0xc01554: r23 = Instance_VerticalDirection
    //     0xc01554: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc01558: ldr             x23, [x23, #0xa20]
    // 0xc0155c: StoreField: r0->field_23 = r23
    //     0xc0155c: stur            w23, [x0, #0x23]
    // 0xc01560: r24 = Instance_Clip
    //     0xc01560: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc01564: ldr             x24, [x24, #0x38]
    // 0xc01568: StoreField: r0->field_2b = r24
    //     0xc01568: stur            w24, [x0, #0x2b]
    // 0xc0156c: StoreField: r0->field_2f = rZR
    //     0xc0156c: stur            xzr, [x0, #0x2f]
    // 0xc01570: ldur            x1, [fp, #-0x18]
    // 0xc01574: StoreField: r0->field_b = r1
    //     0xc01574: stur            w1, [x0, #0xb]
    // 0xc01578: b               #0xc01cf8
    // 0xc0157c: ldur            x6, [fp, #-0x10]
    // 0xc01580: ldur            x7, [fp, #-8]
    // 0xc01584: r8 = true
    //     0xc01584: add             x8, NULL, #0x20  ; true
    // 0xc01588: r5 = Instance_EdgeInsets
    //     0xc01588: add             x5, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xc0158c: ldr             x5, [x5, #0xb30]
    // 0xc01590: r2 = "------"
    //     0xc01590: add             x2, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xc01594: ldr             x2, [x2, #0xb40]
    // 0xc01598: r9 = Instance_EdgeInsets
    //     0xc01598: add             x9, PP, #0x52, lsl #12  ; [pp+0x52b58] Obj!EdgeInsets@d588b1
    //     0xc0159c: ldr             x9, [x9, #0xb58]
    // 0xc015a0: r20 = Instance_CrossAxisAlignment
    //     0xc015a0: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc015a4: ldr             x20, [x20, #0x890]
    // 0xc015a8: r11 = false
    //     0xc015a8: add             x11, NULL, #0x30  ; false
    // 0xc015ac: r12 = 10
    //     0xc015ac: movz            x12, #0xa
    // 0xc015b0: r14 = Instance_MainAxisAlignment
    //     0xc015b0: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc015b4: ldr             x14, [x14, #0xa08]
    // 0xc015b8: r19 = Instance_MainAxisSize
    //     0xc015b8: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc015bc: ldr             x19, [x19, #0xa10]
    // 0xc015c0: r23 = Instance_VerticalDirection
    //     0xc015c0: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc015c4: ldr             x23, [x23, #0xa20]
    // 0xc015c8: r13 = Instance_Axis
    //     0xc015c8: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc015cc: r10 = Instance_SizedBox
    //     0xc015cc: ldr             x10, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc015d0: r24 = Instance_Clip
    //     0xc015d0: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc015d4: ldr             x24, [x24, #0x38]
    // 0xc015d8: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xc015d8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xc015dc: ldr             x3, [x3, #0x1e0]
    // 0xc015e0: r4 = Instance_ColumnMode
    //     0xc015e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xc015e4: ldr             x4, [x4, #0x1e8]
    // 0xc015e8: LoadField: r0 = r6->field_f
    //     0xc015e8: ldur            w0, [x6, #0xf]
    // 0xc015ec: DecompressPointer r0
    //     0xc015ec: add             x0, x0, HEAP, lsl #32
    // 0xc015f0: LoadField: r1 = r0->field_b
    //     0xc015f0: ldur            w1, [x0, #0xb]
    // 0xc015f4: DecompressPointer r1
    //     0xc015f4: add             x1, x1, HEAP, lsl #32
    // 0xc015f8: cmp             w1, NULL
    // 0xc015fc: b.eq            #0xc01d3c
    // 0xc01600: LoadField: r25 = r1->field_b
    //     0xc01600: ldur            w25, [x1, #0xb]
    // 0xc01604: DecompressPointer r25
    //     0xc01604: add             x25, x25, HEAP, lsl #32
    // 0xc01608: LoadField: r0 = r25->field_b
    //     0xc01608: ldur            w0, [x25, #0xb]
    // 0xc0160c: r1 = LoadInt32Instr(r0)
    //     0xc0160c: sbfx            x1, x0, #1, #0x1f
    // 0xc01610: mov             x0, x1
    // 0xc01614: mov             x1, x7
    // 0xc01618: cmp             x1, x0
    // 0xc0161c: b.hs            #0xc01d40
    // 0xc01620: LoadField: r0 = r25->field_f
    //     0xc01620: ldur            w0, [x25, #0xf]
    // 0xc01624: DecompressPointer r0
    //     0xc01624: add             x0, x0, HEAP, lsl #32
    // 0xc01628: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xc01628: add             x16, x0, x7, lsl #2
    //     0xc0162c: ldur            w1, [x16, #0xf]
    // 0xc01630: DecompressPointer r1
    //     0xc01630: add             x1, x1, HEAP, lsl #32
    // 0xc01634: LoadField: r0 = r1->field_7
    //     0xc01634: ldur            w0, [x1, #7]
    // 0xc01638: DecompressPointer r0
    //     0xc01638: add             x0, x0, HEAP, lsl #32
    // 0xc0163c: cmp             w0, NULL
    // 0xc01640: b.ne            #0xc01648
    // 0xc01644: r0 = ""
    //     0xc01644: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc01648: ldr             x1, [fp, #0x18]
    // 0xc0164c: stur            x0, [fp, #-0x18]
    // 0xc01650: r0 = of()
    //     0xc01650: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc01654: LoadField: r1 = r0->field_87
    //     0xc01654: ldur            w1, [x0, #0x87]
    // 0xc01658: DecompressPointer r1
    //     0xc01658: add             x1, x1, HEAP, lsl #32
    // 0xc0165c: LoadField: r0 = r1->field_7
    //     0xc0165c: ldur            w0, [x1, #7]
    // 0xc01660: DecompressPointer r0
    //     0xc01660: add             x0, x0, HEAP, lsl #32
    // 0xc01664: stur            x0, [fp, #-0x20]
    // 0xc01668: r1 = Instance_Color
    //     0xc01668: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0166c: d0 = 0.700000
    //     0xc0166c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc01670: ldr             d0, [x17, #0xf48]
    // 0xc01674: r0 = withOpacity()
    //     0xc01674: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc01678: r16 = 16.000000
    //     0xc01678: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc0167c: ldr             x16, [x16, #0x188]
    // 0xc01680: stp             x0, x16, [SP]
    // 0xc01684: ldur            x1, [fp, #-0x20]
    // 0xc01688: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc01688: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0168c: ldr             x4, [x4, #0xaa0]
    // 0xc01690: r0 = copyWith()
    //     0xc01690: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc01694: stur            x0, [fp, #-0x20]
    // 0xc01698: r0 = Text()
    //     0xc01698: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0169c: mov             x1, x0
    // 0xc016a0: ldur            x0, [fp, #-0x18]
    // 0xc016a4: stur            x1, [fp, #-0x28]
    // 0xc016a8: StoreField: r1->field_b = r0
    //     0xc016a8: stur            w0, [x1, #0xb]
    // 0xc016ac: ldur            x0, [fp, #-0x20]
    // 0xc016b0: StoreField: r1->field_13 = r0
    //     0xc016b0: stur            w0, [x1, #0x13]
    // 0xc016b4: r0 = Padding()
    //     0xc016b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc016b8: mov             x2, x0
    // 0xc016bc: r0 = Instance_EdgeInsets
    //     0xc016bc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xc016c0: ldr             x0, [x0, #0xb30]
    // 0xc016c4: stur            x2, [fp, #-0x18]
    // 0xc016c8: StoreField: r2->field_f = r0
    //     0xc016c8: stur            w0, [x2, #0xf]
    // 0xc016cc: ldur            x1, [fp, #-0x28]
    // 0xc016d0: StoreField: r2->field_b = r1
    //     0xc016d0: stur            w1, [x2, #0xb]
    // 0xc016d4: ldr             x1, [fp, #0x18]
    // 0xc016d8: r0 = of()
    //     0xc016d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc016dc: LoadField: r1 = r0->field_87
    //     0xc016dc: ldur            w1, [x0, #0x87]
    // 0xc016e0: DecompressPointer r1
    //     0xc016e0: add             x1, x1, HEAP, lsl #32
    // 0xc016e4: LoadField: r0 = r1->field_7
    //     0xc016e4: ldur            w0, [x1, #7]
    // 0xc016e8: DecompressPointer r0
    //     0xc016e8: add             x0, x0, HEAP, lsl #32
    // 0xc016ec: r16 = Instance_TextDecoration
    //     0xc016ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xc016f0: ldr             x16, [x16, #0xe30]
    // 0xc016f4: r30 = 6.000000
    //     0xc016f4: add             lr, PP, #0x27, lsl #12  ; [pp+0x27668] 6
    //     0xc016f8: ldr             lr, [lr, #0x668]
    // 0xc016fc: stp             lr, x16, [SP, #0x18]
    // 0xc01700: r16 = Instance_Color
    //     0xc01700: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc01704: r30 = 16.000000
    //     0xc01704: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc01708: ldr             lr, [lr, #0x188]
    // 0xc0170c: stp             lr, x16, [SP, #8]
    // 0xc01710: r16 = Instance_Color
    //     0xc01710: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc01714: str             x16, [SP]
    // 0xc01718: mov             x1, x0
    // 0xc0171c: r4 = const [0, 0x6, 0x5, 0x1, color, 0x5, decoration, 0x1, decorationColor, 0x3, decorationThickness, 0x2, fontSize, 0x4, null]
    //     0xc0171c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52b38] List(15) [0, 0x6, 0x5, 0x1, "color", 0x5, "decoration", 0x1, "decorationColor", 0x3, "decorationThickness", 0x2, "fontSize", 0x4, Null]
    //     0xc01720: ldr             x4, [x4, #0xb38]
    // 0xc01724: r0 = copyWith()
    //     0xc01724: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc01728: stur            x0, [fp, #-0x20]
    // 0xc0172c: r0 = Text()
    //     0xc0172c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc01730: mov             x2, x0
    // 0xc01734: r0 = "------"
    //     0xc01734: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b40] "------"
    //     0xc01738: ldr             x0, [x0, #0xb40]
    // 0xc0173c: stur            x2, [fp, #-0x28]
    // 0xc01740: StoreField: r2->field_b = r0
    //     0xc01740: stur            w0, [x2, #0xb]
    // 0xc01744: ldur            x0, [fp, #-0x20]
    // 0xc01748: StoreField: r2->field_13 = r0
    //     0xc01748: stur            w0, [x2, #0x13]
    // 0xc0174c: ldur            x3, [fp, #-0x10]
    // 0xc01750: LoadField: r0 = r3->field_f
    //     0xc01750: ldur            w0, [x3, #0xf]
    // 0xc01754: DecompressPointer r0
    //     0xc01754: add             x0, x0, HEAP, lsl #32
    // 0xc01758: LoadField: r1 = r0->field_b
    //     0xc01758: ldur            w1, [x0, #0xb]
    // 0xc0175c: DecompressPointer r1
    //     0xc0175c: add             x1, x1, HEAP, lsl #32
    // 0xc01760: cmp             w1, NULL
    // 0xc01764: b.eq            #0xc01d44
    // 0xc01768: LoadField: r4 = r1->field_b
    //     0xc01768: ldur            w4, [x1, #0xb]
    // 0xc0176c: DecompressPointer r4
    //     0xc0176c: add             x4, x4, HEAP, lsl #32
    // 0xc01770: LoadField: r0 = r4->field_b
    //     0xc01770: ldur            w0, [x4, #0xb]
    // 0xc01774: r1 = LoadInt32Instr(r0)
    //     0xc01774: sbfx            x1, x0, #1, #0x1f
    // 0xc01778: mov             x0, x1
    // 0xc0177c: ldur            x1, [fp, #-8]
    // 0xc01780: cmp             x1, x0
    // 0xc01784: b.hs            #0xc01d48
    // 0xc01788: LoadField: r0 = r4->field_f
    //     0xc01788: ldur            w0, [x4, #0xf]
    // 0xc0178c: DecompressPointer r0
    //     0xc0178c: add             x0, x0, HEAP, lsl #32
    // 0xc01790: ldur            x4, [fp, #-8]
    // 0xc01794: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc01794: add             x16, x0, x4, lsl #2
    //     0xc01798: ldur            w1, [x16, #0xf]
    // 0xc0179c: DecompressPointer r1
    //     0xc0179c: add             x1, x1, HEAP, lsl #32
    // 0xc017a0: LoadField: r0 = r1->field_b
    //     0xc017a0: ldur            w0, [x1, #0xb]
    // 0xc017a4: DecompressPointer r0
    //     0xc017a4: add             x0, x0, HEAP, lsl #32
    // 0xc017a8: cmp             w0, NULL
    // 0xc017ac: b.ne            #0xc017b4
    // 0xc017b0: r0 = ""
    //     0xc017b0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc017b4: ldr             x1, [fp, #0x18]
    // 0xc017b8: stur            x0, [fp, #-0x20]
    // 0xc017bc: r0 = of()
    //     0xc017bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc017c0: LoadField: r1 = r0->field_87
    //     0xc017c0: ldur            w1, [x0, #0x87]
    // 0xc017c4: DecompressPointer r1
    //     0xc017c4: add             x1, x1, HEAP, lsl #32
    // 0xc017c8: LoadField: r0 = r1->field_2b
    //     0xc017c8: ldur            w0, [x1, #0x2b]
    // 0xc017cc: DecompressPointer r0
    //     0xc017cc: add             x0, x0, HEAP, lsl #32
    // 0xc017d0: r16 = 14.000000
    //     0xc017d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc017d4: ldr             x16, [x16, #0x1d8]
    // 0xc017d8: r30 = Instance_Color
    //     0xc017d8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc017dc: stp             lr, x16, [SP]
    // 0xc017e0: mov             x1, x0
    // 0xc017e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc017e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc017e8: ldr             x4, [x4, #0xaa0]
    // 0xc017ec: r0 = copyWith()
    //     0xc017ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc017f0: stur            x0, [fp, #-0x30]
    // 0xc017f4: r0 = HtmlWidget()
    //     0xc017f4: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xc017f8: mov             x1, x0
    // 0xc017fc: ldur            x0, [fp, #-0x20]
    // 0xc01800: stur            x1, [fp, #-0x38]
    // 0xc01804: StoreField: r1->field_1f = r0
    //     0xc01804: stur            w0, [x1, #0x1f]
    // 0xc01808: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xc01808: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xc0180c: ldr             x0, [x0, #0x1e0]
    // 0xc01810: StoreField: r1->field_23 = r0
    //     0xc01810: stur            w0, [x1, #0x23]
    // 0xc01814: r0 = Instance_ColumnMode
    //     0xc01814: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xc01818: ldr             x0, [x0, #0x1e8]
    // 0xc0181c: StoreField: r1->field_3b = r0
    //     0xc0181c: stur            w0, [x1, #0x3b]
    // 0xc01820: ldur            x0, [fp, #-0x30]
    // 0xc01824: StoreField: r1->field_3f = r0
    //     0xc01824: stur            w0, [x1, #0x3f]
    // 0xc01828: r0 = Padding()
    //     0xc01828: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0182c: mov             x2, x0
    // 0xc01830: r0 = Instance_EdgeInsets
    //     0xc01830: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b30] Obj!EdgeInsets@d588e1
    //     0xc01834: ldr             x0, [x0, #0xb30]
    // 0xc01838: stur            x2, [fp, #-0x30]
    // 0xc0183c: StoreField: r2->field_f = r0
    //     0xc0183c: stur            w0, [x2, #0xf]
    // 0xc01840: ldur            x0, [fp, #-0x38]
    // 0xc01844: StoreField: r2->field_b = r0
    //     0xc01844: stur            w0, [x2, #0xb]
    // 0xc01848: ldur            x3, [fp, #-0x10]
    // 0xc0184c: LoadField: r0 = r3->field_f
    //     0xc0184c: ldur            w0, [x3, #0xf]
    // 0xc01850: DecompressPointer r0
    //     0xc01850: add             x0, x0, HEAP, lsl #32
    // 0xc01854: LoadField: r1 = r0->field_b
    //     0xc01854: ldur            w1, [x0, #0xb]
    // 0xc01858: DecompressPointer r1
    //     0xc01858: add             x1, x1, HEAP, lsl #32
    // 0xc0185c: cmp             w1, NULL
    // 0xc01860: b.eq            #0xc01d4c
    // 0xc01864: LoadField: r4 = r1->field_b
    //     0xc01864: ldur            w4, [x1, #0xb]
    // 0xc01868: DecompressPointer r4
    //     0xc01868: add             x4, x4, HEAP, lsl #32
    // 0xc0186c: LoadField: r0 = r4->field_b
    //     0xc0186c: ldur            w0, [x4, #0xb]
    // 0xc01870: r1 = LoadInt32Instr(r0)
    //     0xc01870: sbfx            x1, x0, #1, #0x1f
    // 0xc01874: mov             x0, x1
    // 0xc01878: ldur            x1, [fp, #-8]
    // 0xc0187c: cmp             x1, x0
    // 0xc01880: b.hs            #0xc01d50
    // 0xc01884: LoadField: r0 = r4->field_f
    //     0xc01884: ldur            w0, [x4, #0xf]
    // 0xc01888: DecompressPointer r0
    //     0xc01888: add             x0, x0, HEAP, lsl #32
    // 0xc0188c: ldur            x1, [fp, #-8]
    // 0xc01890: ArrayLoad: r4 = r0[r1]  ; Unknown_4
    //     0xc01890: add             x16, x0, x1, lsl #2
    //     0xc01894: ldur            w4, [x16, #0xf]
    // 0xc01898: DecompressPointer r4
    //     0xc01898: add             x4, x4, HEAP, lsl #32
    // 0xc0189c: LoadField: r0 = r4->field_f
    //     0xc0189c: ldur            w0, [x4, #0xf]
    // 0xc018a0: DecompressPointer r0
    //     0xc018a0: add             x0, x0, HEAP, lsl #32
    // 0xc018a4: cmp             w0, NULL
    // 0xc018a8: b.ne            #0xc018b4
    // 0xc018ac: r0 = Null
    //     0xc018ac: mov             x0, NULL
    // 0xc018b0: b               #0xc018c8
    // 0xc018b4: LoadField: r4 = r0->field_7
    //     0xc018b4: ldur            w4, [x0, #7]
    // 0xc018b8: cbnz            w4, #0xc018c4
    // 0xc018bc: r0 = false
    //     0xc018bc: add             x0, NULL, #0x30  ; false
    // 0xc018c0: b               #0xc018c8
    // 0xc018c4: r0 = true
    //     0xc018c4: add             x0, NULL, #0x20  ; true
    // 0xc018c8: cmp             w0, NULL
    // 0xc018cc: b.ne            #0xc018d4
    // 0xc018d0: r0 = false
    //     0xc018d0: add             x0, NULL, #0x30  ; false
    // 0xc018d4: stur            x0, [fp, #-0x20]
    // 0xc018d8: r16 = <EdgeInsets>
    //     0xc018d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc018dc: ldr             x16, [x16, #0xda0]
    // 0xc018e0: r30 = Instance_EdgeInsets
    //     0xc018e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc018e4: ldr             lr, [lr, #0x1f0]
    // 0xc018e8: stp             lr, x16, [SP]
    // 0xc018ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc018ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc018f0: r0 = all()
    //     0xc018f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc018f4: ldr             x1, [fp, #0x18]
    // 0xc018f8: stur            x0, [fp, #-0x38]
    // 0xc018fc: r0 = of()
    //     0xc018fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc01900: LoadField: r1 = r0->field_5b
    //     0xc01900: ldur            w1, [x0, #0x5b]
    // 0xc01904: DecompressPointer r1
    //     0xc01904: add             x1, x1, HEAP, lsl #32
    // 0xc01908: r0 = LoadClassIdInstr(r1)
    //     0xc01908: ldur            x0, [x1, #-1]
    //     0xc0190c: ubfx            x0, x0, #0xc, #0x14
    // 0xc01910: d0 = 0.400000
    //     0xc01910: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc01914: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc01914: sub             lr, x0, #0xffa
    //     0xc01918: ldr             lr, [x21, lr, lsl #3]
    //     0xc0191c: blr             lr
    // 0xc01920: stur            x0, [fp, #-0x40]
    // 0xc01924: r0 = BorderSide()
    //     0xc01924: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xc01928: mov             x1, x0
    // 0xc0192c: ldur            x0, [fp, #-0x40]
    // 0xc01930: stur            x1, [fp, #-0x48]
    // 0xc01934: StoreField: r1->field_7 = r0
    //     0xc01934: stur            w0, [x1, #7]
    // 0xc01938: d0 = 1.000000
    //     0xc01938: fmov            d0, #1.00000000
    // 0xc0193c: StoreField: r1->field_b = d0
    //     0xc0193c: stur            d0, [x1, #0xb]
    // 0xc01940: r0 = Instance_BorderStyle
    //     0xc01940: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xc01944: ldr             x0, [x0, #0xf68]
    // 0xc01948: StoreField: r1->field_13 = r0
    //     0xc01948: stur            w0, [x1, #0x13]
    // 0xc0194c: d0 = -1.000000
    //     0xc0194c: fmov            d0, #-1.00000000
    // 0xc01950: ArrayStore: r1[0] = d0  ; List_8
    //     0xc01950: stur            d0, [x1, #0x17]
    // 0xc01954: r0 = RoundedRectangleBorder()
    //     0xc01954: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xc01958: mov             x1, x0
    // 0xc0195c: r0 = Instance_BorderRadius
    //     0xc0195c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc01960: ldr             x0, [x0, #0xf70]
    // 0xc01964: StoreField: r1->field_b = r0
    //     0xc01964: stur            w0, [x1, #0xb]
    // 0xc01968: ldur            x0, [fp, #-0x48]
    // 0xc0196c: StoreField: r1->field_7 = r0
    //     0xc0196c: stur            w0, [x1, #7]
    // 0xc01970: r16 = <RoundedRectangleBorder>
    //     0xc01970: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc01974: ldr             x16, [x16, #0xf78]
    // 0xc01978: stp             x1, x16, [SP]
    // 0xc0197c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0197c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc01980: r0 = all()
    //     0xc01980: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc01984: stur            x0, [fp, #-0x40]
    // 0xc01988: r0 = ButtonStyle()
    //     0xc01988: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc0198c: mov             x1, x0
    // 0xc01990: ldur            x0, [fp, #-0x38]
    // 0xc01994: stur            x1, [fp, #-0x48]
    // 0xc01998: StoreField: r1->field_23 = r0
    //     0xc01998: stur            w0, [x1, #0x23]
    // 0xc0199c: ldur            x0, [fp, #-0x40]
    // 0xc019a0: StoreField: r1->field_43 = r0
    //     0xc019a0: stur            w0, [x1, #0x43]
    // 0xc019a4: r0 = TextButtonThemeData()
    //     0xc019a4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc019a8: mov             x2, x0
    // 0xc019ac: ldur            x0, [fp, #-0x48]
    // 0xc019b0: stur            x2, [fp, #-0x38]
    // 0xc019b4: StoreField: r2->field_7 = r0
    //     0xc019b4: stur            w0, [x2, #7]
    // 0xc019b8: ldur            x3, [fp, #-0x10]
    // 0xc019bc: LoadField: r0 = r3->field_f
    //     0xc019bc: ldur            w0, [x3, #0xf]
    // 0xc019c0: DecompressPointer r0
    //     0xc019c0: add             x0, x0, HEAP, lsl #32
    // 0xc019c4: LoadField: r1 = r0->field_b
    //     0xc019c4: ldur            w1, [x0, #0xb]
    // 0xc019c8: DecompressPointer r1
    //     0xc019c8: add             x1, x1, HEAP, lsl #32
    // 0xc019cc: cmp             w1, NULL
    // 0xc019d0: b.eq            #0xc01d54
    // 0xc019d4: LoadField: r4 = r1->field_b
    //     0xc019d4: ldur            w4, [x1, #0xb]
    // 0xc019d8: DecompressPointer r4
    //     0xc019d8: add             x4, x4, HEAP, lsl #32
    // 0xc019dc: LoadField: r0 = r4->field_b
    //     0xc019dc: ldur            w0, [x4, #0xb]
    // 0xc019e0: r1 = LoadInt32Instr(r0)
    //     0xc019e0: sbfx            x1, x0, #1, #0x1f
    // 0xc019e4: mov             x0, x1
    // 0xc019e8: ldur            x1, [fp, #-8]
    // 0xc019ec: cmp             x1, x0
    // 0xc019f0: b.hs            #0xc01d58
    // 0xc019f4: LoadField: r0 = r4->field_f
    //     0xc019f4: ldur            w0, [x4, #0xf]
    // 0xc019f8: DecompressPointer r0
    //     0xc019f8: add             x0, x0, HEAP, lsl #32
    // 0xc019fc: ldur            x1, [fp, #-8]
    // 0xc01a00: ArrayLoad: r4 = r0[r1]  ; Unknown_4
    //     0xc01a00: add             x16, x0, x1, lsl #2
    //     0xc01a04: ldur            w4, [x16, #0xf]
    // 0xc01a08: DecompressPointer r4
    //     0xc01a08: add             x4, x4, HEAP, lsl #32
    // 0xc01a0c: LoadField: r0 = r4->field_f
    //     0xc01a0c: ldur            w0, [x4, #0xf]
    // 0xc01a10: DecompressPointer r0
    //     0xc01a10: add             x0, x0, HEAP, lsl #32
    // 0xc01a14: cmp             w0, NULL
    // 0xc01a18: b.ne            #0xc01a20
    // 0xc01a1c: r0 = ""
    //     0xc01a1c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc01a20: ldur            x4, [fp, #-0x20]
    // 0xc01a24: r5 = LoadClassIdInstr(r0)
    //     0xc01a24: ldur            x5, [x0, #-1]
    //     0xc01a28: ubfx            x5, x5, #0xc, #0x14
    // 0xc01a2c: str             x0, [SP]
    // 0xc01a30: mov             x0, x5
    // 0xc01a34: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc01a34: sub             lr, x0, #1, lsl #12
    //     0xc01a38: ldr             lr, [x21, lr, lsl #3]
    //     0xc01a3c: blr             lr
    // 0xc01a40: ldr             x1, [fp, #0x18]
    // 0xc01a44: stur            x0, [fp, #-0x40]
    // 0xc01a48: r0 = of()
    //     0xc01a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc01a4c: LoadField: r1 = r0->field_87
    //     0xc01a4c: ldur            w1, [x0, #0x87]
    // 0xc01a50: DecompressPointer r1
    //     0xc01a50: add             x1, x1, HEAP, lsl #32
    // 0xc01a54: LoadField: r0 = r1->field_7
    //     0xc01a54: ldur            w0, [x1, #7]
    // 0xc01a58: DecompressPointer r0
    //     0xc01a58: add             x0, x0, HEAP, lsl #32
    // 0xc01a5c: r16 = Instance_Color
    //     0xc01a5c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc01a60: r30 = 12.000000
    //     0xc01a60: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc01a64: ldr             lr, [lr, #0x9e8]
    // 0xc01a68: stp             lr, x16, [SP]
    // 0xc01a6c: mov             x1, x0
    // 0xc01a70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc01a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc01a74: ldr             x4, [x4, #0x9b8]
    // 0xc01a78: r0 = copyWith()
    //     0xc01a78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc01a7c: stur            x0, [fp, #-0x48]
    // 0xc01a80: r0 = Text()
    //     0xc01a80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc01a84: mov             x3, x0
    // 0xc01a88: ldur            x0, [fp, #-0x40]
    // 0xc01a8c: stur            x3, [fp, #-0x50]
    // 0xc01a90: StoreField: r3->field_b = r0
    //     0xc01a90: stur            w0, [x3, #0xb]
    // 0xc01a94: ldur            x0, [fp, #-0x48]
    // 0xc01a98: StoreField: r3->field_13 = r0
    //     0xc01a98: stur            w0, [x3, #0x13]
    // 0xc01a9c: r1 = Function '<anonymous closure>':.
    //     0xc01a9c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b60] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc01aa0: ldr             x1, [x1, #0xb60]
    // 0xc01aa4: r2 = Null
    //     0xc01aa4: mov             x2, NULL
    // 0xc01aa8: r0 = AllocateClosure()
    //     0xc01aa8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01aac: stur            x0, [fp, #-0x40]
    // 0xc01ab0: r0 = TextButton()
    //     0xc01ab0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc01ab4: mov             x1, x0
    // 0xc01ab8: ldur            x0, [fp, #-0x40]
    // 0xc01abc: stur            x1, [fp, #-0x48]
    // 0xc01ac0: StoreField: r1->field_b = r0
    //     0xc01ac0: stur            w0, [x1, #0xb]
    // 0xc01ac4: r0 = false
    //     0xc01ac4: add             x0, NULL, #0x30  ; false
    // 0xc01ac8: StoreField: r1->field_27 = r0
    //     0xc01ac8: stur            w0, [x1, #0x27]
    // 0xc01acc: r2 = true
    //     0xc01acc: add             x2, NULL, #0x20  ; true
    // 0xc01ad0: StoreField: r1->field_2f = r2
    //     0xc01ad0: stur            w2, [x1, #0x2f]
    // 0xc01ad4: ldur            x2, [fp, #-0x50]
    // 0xc01ad8: StoreField: r1->field_37 = r2
    //     0xc01ad8: stur            w2, [x1, #0x37]
    // 0xc01adc: r0 = TextButtonTheme()
    //     0xc01adc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc01ae0: mov             x1, x0
    // 0xc01ae4: ldur            x0, [fp, #-0x38]
    // 0xc01ae8: stur            x1, [fp, #-0x40]
    // 0xc01aec: StoreField: r1->field_f = r0
    //     0xc01aec: stur            w0, [x1, #0xf]
    // 0xc01af0: ldur            x0, [fp, #-0x48]
    // 0xc01af4: StoreField: r1->field_b = r0
    //     0xc01af4: stur            w0, [x1, #0xb]
    // 0xc01af8: r0 = Padding()
    //     0xc01af8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc01afc: mov             x1, x0
    // 0xc01b00: r0 = Instance_EdgeInsets
    //     0xc01b00: add             x0, PP, #0x52, lsl #12  ; [pp+0x52b58] Obj!EdgeInsets@d588b1
    //     0xc01b04: ldr             x0, [x0, #0xb58]
    // 0xc01b08: stur            x1, [fp, #-0x38]
    // 0xc01b0c: StoreField: r1->field_f = r0
    //     0xc01b0c: stur            w0, [x1, #0xf]
    // 0xc01b10: ldur            x0, [fp, #-0x40]
    // 0xc01b14: StoreField: r1->field_b = r0
    //     0xc01b14: stur            w0, [x1, #0xb]
    // 0xc01b18: r0 = Visibility()
    //     0xc01b18: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc01b1c: mov             x1, x0
    // 0xc01b20: ldur            x0, [fp, #-0x38]
    // 0xc01b24: stur            x1, [fp, #-0x40]
    // 0xc01b28: StoreField: r1->field_b = r0
    //     0xc01b28: stur            w0, [x1, #0xb]
    // 0xc01b2c: r0 = Instance_SizedBox
    //     0xc01b2c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc01b30: StoreField: r1->field_f = r0
    //     0xc01b30: stur            w0, [x1, #0xf]
    // 0xc01b34: ldur            x0, [fp, #-0x20]
    // 0xc01b38: StoreField: r1->field_13 = r0
    //     0xc01b38: stur            w0, [x1, #0x13]
    // 0xc01b3c: r0 = false
    //     0xc01b3c: add             x0, NULL, #0x30  ; false
    // 0xc01b40: ArrayStore: r1[0] = r0  ; List_4
    //     0xc01b40: stur            w0, [x1, #0x17]
    // 0xc01b44: StoreField: r1->field_1b = r0
    //     0xc01b44: stur            w0, [x1, #0x1b]
    // 0xc01b48: StoreField: r1->field_1f = r0
    //     0xc01b48: stur            w0, [x1, #0x1f]
    // 0xc01b4c: StoreField: r1->field_23 = r0
    //     0xc01b4c: stur            w0, [x1, #0x23]
    // 0xc01b50: StoreField: r1->field_27 = r0
    //     0xc01b50: stur            w0, [x1, #0x27]
    // 0xc01b54: StoreField: r1->field_2b = r0
    //     0xc01b54: stur            w0, [x1, #0x2b]
    // 0xc01b58: r0 = ImageHeaders.forImages()
    //     0xc01b58: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc01b5c: mov             x3, x0
    // 0xc01b60: ldur            x0, [fp, #-0x10]
    // 0xc01b64: stur            x3, [fp, #-0x20]
    // 0xc01b68: LoadField: r1 = r0->field_f
    //     0xc01b68: ldur            w1, [x0, #0xf]
    // 0xc01b6c: DecompressPointer r1
    //     0xc01b6c: add             x1, x1, HEAP, lsl #32
    // 0xc01b70: LoadField: r0 = r1->field_b
    //     0xc01b70: ldur            w0, [x1, #0xb]
    // 0xc01b74: DecompressPointer r0
    //     0xc01b74: add             x0, x0, HEAP, lsl #32
    // 0xc01b78: cmp             w0, NULL
    // 0xc01b7c: b.eq            #0xc01d5c
    // 0xc01b80: LoadField: r2 = r0->field_b
    //     0xc01b80: ldur            w2, [x0, #0xb]
    // 0xc01b84: DecompressPointer r2
    //     0xc01b84: add             x2, x2, HEAP, lsl #32
    // 0xc01b88: LoadField: r0 = r2->field_b
    //     0xc01b88: ldur            w0, [x2, #0xb]
    // 0xc01b8c: r1 = LoadInt32Instr(r0)
    //     0xc01b8c: sbfx            x1, x0, #1, #0x1f
    // 0xc01b90: mov             x0, x1
    // 0xc01b94: ldur            x1, [fp, #-8]
    // 0xc01b98: cmp             x1, x0
    // 0xc01b9c: b.hs            #0xc01d60
    // 0xc01ba0: LoadField: r0 = r2->field_f
    //     0xc01ba0: ldur            w0, [x2, #0xf]
    // 0xc01ba4: DecompressPointer r0
    //     0xc01ba4: add             x0, x0, HEAP, lsl #32
    // 0xc01ba8: ldur            x1, [fp, #-8]
    // 0xc01bac: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xc01bac: add             x16, x0, x1, lsl #2
    //     0xc01bb0: ldur            w2, [x16, #0xf]
    // 0xc01bb4: DecompressPointer r2
    //     0xc01bb4: add             x2, x2, HEAP, lsl #32
    // 0xc01bb8: LoadField: r0 = r2->field_13
    //     0xc01bb8: ldur            w0, [x2, #0x13]
    // 0xc01bbc: DecompressPointer r0
    //     0xc01bbc: add             x0, x0, HEAP, lsl #32
    // 0xc01bc0: cmp             w0, NULL
    // 0xc01bc4: b.ne            #0xc01bd0
    // 0xc01bc8: r7 = ""
    //     0xc01bc8: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc01bcc: b               #0xc01bd4
    // 0xc01bd0: mov             x7, x0
    // 0xc01bd4: ldur            x6, [fp, #-0x18]
    // 0xc01bd8: ldur            x5, [fp, #-0x28]
    // 0xc01bdc: ldur            x4, [fp, #-0x30]
    // 0xc01be0: ldur            x0, [fp, #-0x40]
    // 0xc01be4: stur            x7, [fp, #-0x10]
    // 0xc01be8: r1 = Function '<anonymous closure>':.
    //     0xc01be8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b68] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc01bec: ldr             x1, [x1, #0xb68]
    // 0xc01bf0: r2 = Null
    //     0xc01bf0: mov             x2, NULL
    // 0xc01bf4: r0 = AllocateClosure()
    //     0xc01bf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01bf8: r1 = Function '<anonymous closure>':.
    //     0xc01bf8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b70] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc01bfc: ldr             x1, [x1, #0xb70]
    // 0xc01c00: r2 = Null
    //     0xc01c00: mov             x2, NULL
    // 0xc01c04: stur            x0, [fp, #-0x38]
    // 0xc01c08: r0 = AllocateClosure()
    //     0xc01c08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc01c0c: stur            x0, [fp, #-0x48]
    // 0xc01c10: r0 = CachedNetworkImage()
    //     0xc01c10: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc01c14: stur            x0, [fp, #-0x50]
    // 0xc01c18: ldur            x16, [fp, #-0x20]
    // 0xc01c1c: ldur            lr, [fp, #-0x38]
    // 0xc01c20: stp             lr, x16, [SP, #0x10]
    // 0xc01c24: ldur            x16, [fp, #-0x48]
    // 0xc01c28: r30 = Instance_BoxFit
    //     0xc01c28: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xc01c2c: ldr             lr, [lr, #0xb18]
    // 0xc01c30: stp             lr, x16, [SP]
    // 0xc01c34: mov             x1, x0
    // 0xc01c38: ldur            x2, [fp, #-0x10]
    // 0xc01c3c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xc01c3c: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xc01c40: ldr             x4, [x4, #0x2b0]
    // 0xc01c44: r0 = CachedNetworkImage()
    //     0xc01c44: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc01c48: r1 = Null
    //     0xc01c48: mov             x1, NULL
    // 0xc01c4c: r2 = 10
    //     0xc01c4c: movz            x2, #0xa
    // 0xc01c50: r0 = AllocateArray()
    //     0xc01c50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc01c54: mov             x2, x0
    // 0xc01c58: ldur            x0, [fp, #-0x18]
    // 0xc01c5c: stur            x2, [fp, #-0x10]
    // 0xc01c60: StoreField: r2->field_f = r0
    //     0xc01c60: stur            w0, [x2, #0xf]
    // 0xc01c64: ldur            x0, [fp, #-0x28]
    // 0xc01c68: StoreField: r2->field_13 = r0
    //     0xc01c68: stur            w0, [x2, #0x13]
    // 0xc01c6c: ldur            x0, [fp, #-0x30]
    // 0xc01c70: ArrayStore: r2[0] = r0  ; List_4
    //     0xc01c70: stur            w0, [x2, #0x17]
    // 0xc01c74: ldur            x0, [fp, #-0x40]
    // 0xc01c78: StoreField: r2->field_1b = r0
    //     0xc01c78: stur            w0, [x2, #0x1b]
    // 0xc01c7c: ldur            x0, [fp, #-0x50]
    // 0xc01c80: StoreField: r2->field_1f = r0
    //     0xc01c80: stur            w0, [x2, #0x1f]
    // 0xc01c84: r1 = <Widget>
    //     0xc01c84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc01c88: r0 = AllocateGrowableArray()
    //     0xc01c88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc01c8c: mov             x1, x0
    // 0xc01c90: ldur            x0, [fp, #-0x10]
    // 0xc01c94: stur            x1, [fp, #-0x18]
    // 0xc01c98: StoreField: r1->field_f = r0
    //     0xc01c98: stur            w0, [x1, #0xf]
    // 0xc01c9c: r0 = 10
    //     0xc01c9c: movz            x0, #0xa
    // 0xc01ca0: StoreField: r1->field_b = r0
    //     0xc01ca0: stur            w0, [x1, #0xb]
    // 0xc01ca4: r0 = Column()
    //     0xc01ca4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc01ca8: r1 = Instance_Axis
    //     0xc01ca8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc01cac: StoreField: r0->field_f = r1
    //     0xc01cac: stur            w1, [x0, #0xf]
    // 0xc01cb0: r1 = Instance_MainAxisAlignment
    //     0xc01cb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc01cb4: ldr             x1, [x1, #0xa08]
    // 0xc01cb8: StoreField: r0->field_13 = r1
    //     0xc01cb8: stur            w1, [x0, #0x13]
    // 0xc01cbc: r1 = Instance_MainAxisSize
    //     0xc01cbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc01cc0: ldr             x1, [x1, #0xa10]
    // 0xc01cc4: ArrayStore: r0[0] = r1  ; List_4
    //     0xc01cc4: stur            w1, [x0, #0x17]
    // 0xc01cc8: r1 = Instance_CrossAxisAlignment
    //     0xc01cc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc01ccc: ldr             x1, [x1, #0x890]
    // 0xc01cd0: StoreField: r0->field_1b = r1
    //     0xc01cd0: stur            w1, [x0, #0x1b]
    // 0xc01cd4: r1 = Instance_VerticalDirection
    //     0xc01cd4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc01cd8: ldr             x1, [x1, #0xa20]
    // 0xc01cdc: StoreField: r0->field_23 = r1
    //     0xc01cdc: stur            w1, [x0, #0x23]
    // 0xc01ce0: r1 = Instance_Clip
    //     0xc01ce0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc01ce4: ldr             x1, [x1, #0x38]
    // 0xc01ce8: StoreField: r0->field_2b = r1
    //     0xc01ce8: stur            w1, [x0, #0x2b]
    // 0xc01cec: StoreField: r0->field_2f = rZR
    //     0xc01cec: stur            xzr, [x0, #0x2f]
    // 0xc01cf0: ldur            x1, [fp, #-0x18]
    // 0xc01cf4: StoreField: r0->field_b = r1
    //     0xc01cf4: stur            w1, [x0, #0xb]
    // 0xc01cf8: LeaveFrame
    //     0xc01cf8: mov             SP, fp
    //     0xc01cfc: ldp             fp, lr, [SP], #0x10
    // 0xc01d00: ret
    //     0xc01d00: ret             
    // 0xc01d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc01d04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc01d08: b               #0xc00e2c
    // 0xc01d0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc01d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc01d5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc01d60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc01d60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 3973, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80f8c, size: 0x24
    // 0xc80f8c: EnterFrame
    //     0xc80f8c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80f90: mov             fp, SP
    // 0xc80f94: mov             x0, x1
    // 0xc80f98: r1 = <ProductContentTextAndMedia>
    //     0xc80f98: add             x1, PP, #0x48, lsl #12  ; [pp+0x48368] TypeArguments: <ProductContentTextAndMedia>
    //     0xc80f9c: ldr             x1, [x1, #0x368]
    // 0xc80fa0: r0 = _ProductContentTextAndMediaState()
    //     0xc80fa0: bl              #0xc80fb0  ; Allocate_ProductContentTextAndMediaStateStub -> _ProductContentTextAndMediaState (size=0x14)
    // 0xc80fa4: LeaveFrame
    //     0xc80fa4: mov             SP, fp
    //     0xc80fa8: ldp             fp, lr, [SP], #0x10
    // 0xc80fac: ret
    //     0xc80fac: ret             
  }
}
