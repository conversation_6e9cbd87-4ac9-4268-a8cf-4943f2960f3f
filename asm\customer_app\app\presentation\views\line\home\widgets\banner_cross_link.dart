// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/banner_cross_link.dart

// class id: 1049520, size: 0x8
class :: {
}

// class id: 3251, size: 0x14, field offset: 0x14
class _CarouselItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbdca84, size: 0x1b0
    // 0xbdca84: EnterFrame
    //     0xbdca84: stp             fp, lr, [SP, #-0x10]!
    //     0xbdca88: mov             fp, SP
    // 0xbdca8c: AllocStack(0x50)
    //     0xbdca8c: sub             SP, SP, #0x50
    // 0xbdca90: SetupParameters(_CarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbdca90: mov             x0, x1
    //     0xbdca94: stur            x1, [fp, #-8]
    //     0xbdca98: mov             x1, x2
    //     0xbdca9c: stur            x2, [fp, #-0x10]
    // 0xbdcaa0: CheckStackOverflow
    //     0xbdcaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdcaa4: cmp             SP, x16
    //     0xbdcaa8: b.ls            #0xbdcc24
    // 0xbdcaac: r1 = 1
    //     0xbdcaac: movz            x1, #0x1
    // 0xbdcab0: r0 = AllocateContext()
    //     0xbdcab0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdcab4: mov             x3, x0
    // 0xbdcab8: ldur            x0, [fp, #-8]
    // 0xbdcabc: stur            x3, [fp, #-0x18]
    // 0xbdcac0: StoreField: r3->field_f = r0
    //     0xbdcac0: stur            w0, [x3, #0xf]
    // 0xbdcac4: LoadField: r1 = r0->field_b
    //     0xbdcac4: ldur            w1, [x0, #0xb]
    // 0xbdcac8: DecompressPointer r1
    //     0xbdcac8: add             x1, x1, HEAP, lsl #32
    // 0xbdcacc: cmp             w1, NULL
    // 0xbdcad0: b.eq            #0xbdcc2c
    // 0xbdcad4: LoadField: r2 = r1->field_b
    //     0xbdcad4: ldur            w2, [x1, #0xb]
    // 0xbdcad8: DecompressPointer r2
    //     0xbdcad8: add             x2, x2, HEAP, lsl #32
    // 0xbdcadc: cmp             w2, NULL
    // 0xbdcae0: b.ne            #0xbdcaf8
    // 0xbdcae4: r1 = <Entity>
    //     0xbdcae4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xbdcae8: ldr             x1, [x1, #0xb68]
    // 0xbdcaec: r2 = 0
    //     0xbdcaec: movz            x2, #0
    // 0xbdcaf0: r0 = _GrowableList()
    //     0xbdcaf0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbdcaf4: mov             x2, x0
    // 0xbdcaf8: ldur            x0, [fp, #-8]
    // 0xbdcafc: stur            x2, [fp, #-0x48]
    // 0xbdcb00: LoadField: r1 = r0->field_b
    //     0xbdcb00: ldur            w1, [x0, #0xb]
    // 0xbdcb04: DecompressPointer r1
    //     0xbdcb04: add             x1, x1, HEAP, lsl #32
    // 0xbdcb08: cmp             w1, NULL
    // 0xbdcb0c: b.eq            #0xbdcc30
    // 0xbdcb10: LoadField: r0 = r1->field_1b
    //     0xbdcb10: ldur            w0, [x1, #0x1b]
    // 0xbdcb14: DecompressPointer r0
    //     0xbdcb14: add             x0, x0, HEAP, lsl #32
    // 0xbdcb18: stur            x0, [fp, #-0x40]
    // 0xbdcb1c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbdcb1c: ldur            w3, [x1, #0x17]
    // 0xbdcb20: DecompressPointer r3
    //     0xbdcb20: add             x3, x3, HEAP, lsl #32
    // 0xbdcb24: stur            x3, [fp, #-0x38]
    // 0xbdcb28: LoadField: r4 = r1->field_13
    //     0xbdcb28: ldur            w4, [x1, #0x13]
    // 0xbdcb2c: DecompressPointer r4
    //     0xbdcb2c: add             x4, x4, HEAP, lsl #32
    // 0xbdcb30: stur            x4, [fp, #-0x30]
    // 0xbdcb34: LoadField: r5 = r1->field_f
    //     0xbdcb34: ldur            w5, [x1, #0xf]
    // 0xbdcb38: DecompressPointer r5
    //     0xbdcb38: add             x5, x5, HEAP, lsl #32
    // 0xbdcb3c: stur            x5, [fp, #-0x28]
    // 0xbdcb40: LoadField: r6 = r1->field_27
    //     0xbdcb40: ldur            w6, [x1, #0x27]
    // 0xbdcb44: DecompressPointer r6
    //     0xbdcb44: add             x6, x6, HEAP, lsl #32
    // 0xbdcb48: stur            x6, [fp, #-0x20]
    // 0xbdcb4c: LoadField: r7 = r1->field_2b
    //     0xbdcb4c: ldur            w7, [x1, #0x2b]
    // 0xbdcb50: DecompressPointer r7
    //     0xbdcb50: add             x7, x7, HEAP, lsl #32
    // 0xbdcb54: ldur            x1, [fp, #-0x10]
    // 0xbdcb58: stur            x7, [fp, #-8]
    // 0xbdcb5c: r0 = of()
    //     0xbdcb5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdcb60: LoadField: r1 = r0->field_5b
    //     0xbdcb60: ldur            w1, [x0, #0x5b]
    // 0xbdcb64: DecompressPointer r1
    //     0xbdcb64: add             x1, x1, HEAP, lsl #32
    // 0xbdcb68: stur            x1, [fp, #-0x10]
    // 0xbdcb6c: r0 = BannerCrossWidget()
    //     0xbdcb6c: bl              #0xa419a0  ; AllocateBannerCrossWidgetStub -> BannerCrossWidget (size=0x4c)
    // 0xbdcb70: d0 = 200.000000
    //     0xbdcb70: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xbdcb74: ldr             d0, [x17, #0x360]
    // 0xbdcb78: stur            x0, [fp, #-0x50]
    // 0xbdcb7c: StoreField: r0->field_b = d0
    //     0xbdcb7c: stur            d0, [x0, #0xb]
    // 0xbdcb80: r1 = true
    //     0xbdcb80: add             x1, NULL, #0x20  ; true
    // 0xbdcb84: StoreField: r0->field_13 = r1
    //     0xbdcb84: stur            w1, [x0, #0x13]
    // 0xbdcb88: r1 = Instance_Duration
    //     0xbdcb88: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xbdcb8c: ldr             x1, [x1, #0xbd8]
    // 0xbdcb90: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdcb90: stur            w1, [x0, #0x17]
    // 0xbdcb94: ldur            x1, [fp, #-0x10]
    // 0xbdcb98: StoreField: r0->field_1b = r1
    //     0xbdcb98: stur            w1, [x0, #0x1b]
    // 0xbdcb9c: r1 = Instance_Color
    //     0xbdcb9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbdcba0: ldr             x1, [x1, #0x90]
    // 0xbdcba4: StoreField: r0->field_1f = r1
    //     0xbdcba4: stur            w1, [x0, #0x1f]
    // 0xbdcba8: ldur            x1, [fp, #-0x48]
    // 0xbdcbac: StoreField: r0->field_27 = r1
    //     0xbdcbac: stur            w1, [x0, #0x27]
    // 0xbdcbb0: ldur            x2, [fp, #-0x18]
    // 0xbdcbb4: r1 = Function '<anonymous closure>':.
    //     0xbdcbb4: add             x1, PP, #0x53, lsl #12  ; [pp+0x538d0] AnonymousClosure: (0xbde44c), in [package:customer_app/app/presentation/views/line/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xbdca84)
    //     0xbdcbb8: ldr             x1, [x1, #0x8d0]
    // 0xbdcbbc: r0 = AllocateClosure()
    //     0xbdcbbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdcbc0: mov             x1, x0
    // 0xbdcbc4: ldur            x0, [fp, #-0x50]
    // 0xbdcbc8: StoreField: r0->field_2b = r1
    //     0xbdcbc8: stur            w1, [x0, #0x2b]
    // 0xbdcbcc: ldur            x1, [fp, #-0x40]
    // 0xbdcbd0: StoreField: r0->field_2f = r1
    //     0xbdcbd0: stur            w1, [x0, #0x2f]
    // 0xbdcbd4: ldur            x1, [fp, #-0x38]
    // 0xbdcbd8: StoreField: r0->field_33 = r1
    //     0xbdcbd8: stur            w1, [x0, #0x33]
    // 0xbdcbdc: ldur            x1, [fp, #-8]
    // 0xbdcbe0: StoreField: r0->field_37 = r1
    //     0xbdcbe0: stur            w1, [x0, #0x37]
    // 0xbdcbe4: ldur            x1, [fp, #-0x30]
    // 0xbdcbe8: StoreField: r0->field_3b = r1
    //     0xbdcbe8: stur            w1, [x0, #0x3b]
    // 0xbdcbec: ldur            x1, [fp, #-0x28]
    // 0xbdcbf0: StoreField: r0->field_3f = r1
    //     0xbdcbf0: stur            w1, [x0, #0x3f]
    // 0xbdcbf4: ldur            x1, [fp, #-0x20]
    // 0xbdcbf8: StoreField: r0->field_43 = r1
    //     0xbdcbf8: stur            w1, [x0, #0x43]
    // 0xbdcbfc: ldur            x2, [fp, #-0x18]
    // 0xbdcc00: r1 = Function '<anonymous closure>':.
    //     0xbdcc00: add             x1, PP, #0x53, lsl #12  ; [pp+0x538d8] AnonymousClosure: (0xbdcc34), in [package:customer_app/app/presentation/views/line/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xbdca84)
    //     0xbdcc04: ldr             x1, [x1, #0x8d8]
    // 0xbdcc08: r0 = AllocateClosure()
    //     0xbdcc08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdcc0c: mov             x1, x0
    // 0xbdcc10: ldur            x0, [fp, #-0x50]
    // 0xbdcc14: StoreField: r0->field_47 = r1
    //     0xbdcc14: stur            w1, [x0, #0x47]
    // 0xbdcc18: LeaveFrame
    //     0xbdcc18: mov             SP, fp
    //     0xbdcc1c: ldp             fp, lr, [SP], #0x10
    // 0xbdcc20: ret
    //     0xbdcc20: ret             
    // 0xbdcc24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdcc24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdcc28: b               #0xbdcaac
    // 0xbdcc2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdcc2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdcc30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdcc30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xbdcc34, size: 0x80
    // 0xbdcc34: EnterFrame
    //     0xbdcc34: stp             fp, lr, [SP, #-0x10]!
    //     0xbdcc38: mov             fp, SP
    // 0xbdcc3c: AllocStack(0x8)
    //     0xbdcc3c: sub             SP, SP, #8
    // 0xbdcc40: SetupParameters()
    //     0xbdcc40: ldr             x0, [fp, #0x20]
    //     0xbdcc44: ldur            w1, [x0, #0x17]
    //     0xbdcc48: add             x1, x1, HEAP, lsl #32
    // 0xbdcc4c: CheckStackOverflow
    //     0xbdcc4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdcc50: cmp             SP, x16
    //     0xbdcc54: b.ls            #0xbdccac
    // 0xbdcc58: LoadField: r3 = r1->field_f
    //     0xbdcc58: ldur            w3, [x1, #0xf]
    // 0xbdcc5c: DecompressPointer r3
    //     0xbdcc5c: add             x3, x3, HEAP, lsl #32
    // 0xbdcc60: ldr             x0, [fp, #0x18]
    // 0xbdcc64: stur            x3, [fp, #-8]
    // 0xbdcc68: r2 = Null
    //     0xbdcc68: mov             x2, NULL
    // 0xbdcc6c: r1 = Null
    //     0xbdcc6c: mov             x1, NULL
    // 0xbdcc70: r8 = List<Entity>
    //     0xbdcc70: add             x8, PP, #0x53, lsl #12  ; [pp+0x538e0] Type: List<Entity>
    //     0xbdcc74: ldr             x8, [x8, #0x8e0]
    // 0xbdcc78: r3 = Null
    //     0xbdcc78: add             x3, PP, #0x53, lsl #12  ; [pp+0x538e8] Null
    //     0xbdcc7c: ldr             x3, [x3, #0x8e8]
    // 0xbdcc80: r0 = List<Entity>()
    //     0xbdcc80: bl              #0xa43098  ; IsType_List<Entity>_Stub
    // 0xbdcc84: ldr             x0, [fp, #0x10]
    // 0xbdcc88: r3 = LoadInt32Instr(r0)
    //     0xbdcc88: sbfx            x3, x0, #1, #0x1f
    //     0xbdcc8c: tbz             w0, #0, #0xbdcc94
    //     0xbdcc90: ldur            x3, [x0, #7]
    // 0xbdcc94: ldur            x1, [fp, #-8]
    // 0xbdcc98: ldr             x2, [fp, #0x18]
    // 0xbdcc9c: r0 = bannerSlider()
    //     0xbdcc9c: bl              #0xbdccb4  ; [package:customer_app/app/presentation/views/line/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider
    // 0xbdcca0: LeaveFrame
    //     0xbdcca0: mov             SP, fp
    //     0xbdcca4: ldp             fp, lr, [SP], #0x10
    // 0xbdcca8: ret
    //     0xbdcca8: ret             
    // 0xbdccac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdccac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdccb0: b               #0xbdcc58
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xbdccb4, size: 0x8c4
    // 0xbdccb4: EnterFrame
    //     0xbdccb4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdccb8: mov             fp, SP
    // 0xbdccbc: AllocStack(0x90)
    //     0xbdccbc: sub             SP, SP, #0x90
    // 0xbdccc0: SetupParameters(_CarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbdccc0: stur            x1, [fp, #-8]
    //     0xbdccc4: stur            x2, [fp, #-0x10]
    //     0xbdccc8: stur            x3, [fp, #-0x18]
    // 0xbdcccc: CheckStackOverflow
    //     0xbdcccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdccd0: cmp             SP, x16
    //     0xbdccd4: b.ls            #0xbdd528
    // 0xbdccd8: r1 = 2
    //     0xbdccd8: movz            x1, #0x2
    // 0xbdccdc: r0 = AllocateContext()
    //     0xbdccdc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdcce0: mov             x3, x0
    // 0xbdcce4: ldur            x2, [fp, #-8]
    // 0xbdcce8: stur            x3, [fp, #-0x28]
    // 0xbdccec: StoreField: r3->field_f = r2
    //     0xbdccec: stur            w2, [x3, #0xf]
    // 0xbdccf0: ldur            x4, [fp, #-0x18]
    // 0xbdccf4: r0 = BoxInt64Instr(r4)
    //     0xbdccf4: sbfiz           x0, x4, #1, #0x1f
    //     0xbdccf8: cmp             x4, x0, asr #1
    //     0xbdccfc: b.eq            #0xbdcd08
    //     0xbdcd00: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdcd04: stur            x4, [x0, #7]
    // 0xbdcd08: StoreField: r3->field_13 = r0
    //     0xbdcd08: stur            w0, [x3, #0x13]
    // 0xbdcd0c: LoadField: r0 = r2->field_b
    //     0xbdcd0c: ldur            w0, [x2, #0xb]
    // 0xbdcd10: DecompressPointer r0
    //     0xbdcd10: add             x0, x0, HEAP, lsl #32
    // 0xbdcd14: cmp             w0, NULL
    // 0xbdcd18: b.eq            #0xbdd530
    // 0xbdcd1c: LoadField: r4 = r0->field_2f
    //     0xbdcd1c: ldur            w4, [x0, #0x2f]
    // 0xbdcd20: DecompressPointer r4
    //     0xbdcd20: add             x4, x4, HEAP, lsl #32
    // 0xbdcd24: stur            x4, [fp, #-0x20]
    // 0xbdcd28: LoadField: r1 = r2->field_f
    //     0xbdcd28: ldur            w1, [x2, #0xf]
    // 0xbdcd2c: DecompressPointer r1
    //     0xbdcd2c: add             x1, x1, HEAP, lsl #32
    // 0xbdcd30: cmp             w1, NULL
    // 0xbdcd34: b.eq            #0xbdd534
    // 0xbdcd38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdcd38: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdcd3c: r0 = _of()
    //     0xbdcd3c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbdcd40: LoadField: r1 = r0->field_7
    //     0xbdcd40: ldur            w1, [x0, #7]
    // 0xbdcd44: DecompressPointer r1
    //     0xbdcd44: add             x1, x1, HEAP, lsl #32
    // 0xbdcd48: LoadField: d0 = r1->field_7
    //     0xbdcd48: ldur            d0, [x1, #7]
    // 0xbdcd4c: stur            d0, [fp, #-0x68]
    // 0xbdcd50: r0 = ImageHeaders.forImages()
    //     0xbdcd50: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdcd54: mov             x1, x0
    // 0xbdcd58: ldur            x2, [fp, #-0x28]
    // 0xbdcd5c: stur            x1, [fp, #-0x30]
    // 0xbdcd60: LoadField: r0 = r2->field_13
    //     0xbdcd60: ldur            w0, [x2, #0x13]
    // 0xbdcd64: DecompressPointer r0
    //     0xbdcd64: add             x0, x0, HEAP, lsl #32
    // 0xbdcd68: ldur            x3, [fp, #-0x10]
    // 0xbdcd6c: r4 = LoadClassIdInstr(r3)
    //     0xbdcd6c: ldur            x4, [x3, #-1]
    //     0xbdcd70: ubfx            x4, x4, #0xc, #0x14
    // 0xbdcd74: stp             x0, x3, [SP]
    // 0xbdcd78: mov             x0, x4
    // 0xbdcd7c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdcd7c: sub             lr, x0, #0xb7
    //     0xbdcd80: ldr             lr, [x21, lr, lsl #3]
    //     0xbdcd84: blr             lr
    // 0xbdcd88: LoadField: r1 = r0->field_13
    //     0xbdcd88: ldur            w1, [x0, #0x13]
    // 0xbdcd8c: DecompressPointer r1
    //     0xbdcd8c: add             x1, x1, HEAP, lsl #32
    // 0xbdcd90: cmp             w1, NULL
    // 0xbdcd94: b.ne            #0xbdcda0
    // 0xbdcd98: r4 = ""
    //     0xbdcd98: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdcd9c: b               #0xbdcda4
    // 0xbdcda0: mov             x4, x1
    // 0xbdcda4: ldur            x3, [fp, #-0x10]
    // 0xbdcda8: ldur            x0, [fp, #-0x28]
    // 0xbdcdac: ldur            d0, [fp, #-0x68]
    // 0xbdcdb0: stur            x4, [fp, #-0x38]
    // 0xbdcdb4: r1 = Function '<anonymous closure>':.
    //     0xbdcdb4: add             x1, PP, #0x53, lsl #12  ; [pp+0x538f8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdcdb8: ldr             x1, [x1, #0x8f8]
    // 0xbdcdbc: r2 = Null
    //     0xbdcdbc: mov             x2, NULL
    // 0xbdcdc0: r0 = AllocateClosure()
    //     0xbdcdc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdcdc4: r1 = Function '<anonymous closure>':.
    //     0xbdcdc4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53900] AnonymousClosure: (0xbdd668), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xbdd6c8)
    //     0xbdcdc8: ldr             x1, [x1, #0x900]
    // 0xbdcdcc: r2 = Null
    //     0xbdcdcc: mov             x2, NULL
    // 0xbdcdd0: stur            x0, [fp, #-0x40]
    // 0xbdcdd4: r0 = AllocateClosure()
    //     0xbdcdd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdcdd8: stur            x0, [fp, #-0x48]
    // 0xbdcddc: r0 = CachedNetworkImage()
    //     0xbdcddc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbdcde0: stur            x0, [fp, #-0x50]
    // 0xbdcde4: ldur            x16, [fp, #-0x30]
    // 0xbdcde8: r30 = Instance_BoxFit
    //     0xbdcde8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbdcdec: ldr             lr, [lr, #0x118]
    // 0xbdcdf0: stp             lr, x16, [SP, #0x10]
    // 0xbdcdf4: ldur            x16, [fp, #-0x40]
    // 0xbdcdf8: ldur            lr, [fp, #-0x48]
    // 0xbdcdfc: stp             lr, x16, [SP]
    // 0xbdce00: mov             x1, x0
    // 0xbdce04: ldur            x2, [fp, #-0x38]
    // 0xbdce08: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xbdce08: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xbdce0c: ldr             x4, [x4, #0x828]
    // 0xbdce10: r0 = CachedNetworkImage()
    //     0xbdce10: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbdce14: ldur            d0, [fp, #-0x68]
    // 0xbdce18: r0 = inline_Allocate_Double()
    //     0xbdce18: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbdce1c: add             x0, x0, #0x10
    //     0xbdce20: cmp             x1, x0
    //     0xbdce24: b.ls            #0xbdd538
    //     0xbdce28: str             x0, [THR, #0x50]  ; THR::top
    //     0xbdce2c: sub             x0, x0, #0xf
    //     0xbdce30: movz            x1, #0xe15c
    //     0xbdce34: movk            x1, #0x3, lsl #16
    //     0xbdce38: stur            x1, [x0, #-1]
    // 0xbdce3c: StoreField: r0->field_7 = d0
    //     0xbdce3c: stur            d0, [x0, #7]
    // 0xbdce40: stur            x0, [fp, #-0x30]
    // 0xbdce44: r0 = Container()
    //     0xbdce44: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdce48: stur            x0, [fp, #-0x38]
    // 0xbdce4c: ldur            x16, [fp, #-0x20]
    // 0xbdce50: ldur            lr, [fp, #-0x30]
    // 0xbdce54: stp             lr, x16, [SP, #8]
    // 0xbdce58: ldur            x16, [fp, #-0x50]
    // 0xbdce5c: str             x16, [SP]
    // 0xbdce60: mov             x1, x0
    // 0xbdce64: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xbdce64: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xbdce68: ldr             x4, [x4, #0x1b8]
    // 0xbdce6c: r0 = Container()
    //     0xbdce6c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdce70: ldur            x2, [fp, #-0x28]
    // 0xbdce74: LoadField: r0 = r2->field_13
    //     0xbdce74: ldur            w0, [x2, #0x13]
    // 0xbdce78: DecompressPointer r0
    //     0xbdce78: add             x0, x0, HEAP, lsl #32
    // 0xbdce7c: ldur            x1, [fp, #-0x10]
    // 0xbdce80: r3 = LoadClassIdInstr(r1)
    //     0xbdce80: ldur            x3, [x1, #-1]
    //     0xbdce84: ubfx            x3, x3, #0xc, #0x14
    // 0xbdce88: stp             x0, x1, [SP]
    // 0xbdce8c: mov             x0, x3
    // 0xbdce90: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdce90: sub             lr, x0, #0xb7
    //     0xbdce94: ldr             lr, [x21, lr, lsl #3]
    //     0xbdce98: blr             lr
    // 0xbdce9c: LoadField: r1 = r0->field_7
    //     0xbdce9c: ldur            w1, [x0, #7]
    // 0xbdcea0: DecompressPointer r1
    //     0xbdcea0: add             x1, x1, HEAP, lsl #32
    // 0xbdcea4: cmp             w1, NULL
    // 0xbdcea8: b.ne            #0xbdceb4
    // 0xbdceac: r0 = Null
    //     0xbdceac: mov             x0, NULL
    // 0xbdceb0: b               #0xbdcecc
    // 0xbdceb4: LoadField: r0 = r1->field_7
    //     0xbdceb4: ldur            w0, [x1, #7]
    // 0xbdceb8: cbnz            w0, #0xbdcec4
    // 0xbdcebc: r1 = false
    //     0xbdcebc: add             x1, NULL, #0x30  ; false
    // 0xbdcec0: b               #0xbdcec8
    // 0xbdcec4: r1 = true
    //     0xbdcec4: add             x1, NULL, #0x20  ; true
    // 0xbdcec8: mov             x0, x1
    // 0xbdcecc: cmp             w0, NULL
    // 0xbdced0: b.ne            #0xbdcedc
    // 0xbdced4: r1 = false
    //     0xbdced4: add             x1, NULL, #0x30  ; false
    // 0xbdced8: b               #0xbdcee0
    // 0xbdcedc: mov             x1, x0
    // 0xbdcee0: ldur            x0, [fp, #-0x10]
    // 0xbdcee4: ldur            x2, [fp, #-0x28]
    // 0xbdcee8: stur            x1, [fp, #-0x20]
    // 0xbdceec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbdceec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbdcef0: ldr             x0, [x0, #0x1c80]
    //     0xbdcef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbdcef8: cmp             w0, w16
    //     0xbdcefc: b.ne            #0xbdcf08
    //     0xbdcf00: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbdcf04: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbdcf08: r0 = GetNavigation.size()
    //     0xbdcf08: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbdcf0c: LoadField: d0 = r0->field_7
    //     0xbdcf0c: ldur            d0, [x0, #7]
    // 0xbdcf10: d1 = 0.700000
    //     0xbdcf10: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbdcf14: ldr             d1, [x17, #0xf48]
    // 0xbdcf18: fmul            d2, d0, d1
    // 0xbdcf1c: ldur            x2, [fp, #-0x28]
    // 0xbdcf20: stur            d2, [fp, #-0x68]
    // 0xbdcf24: LoadField: r0 = r2->field_13
    //     0xbdcf24: ldur            w0, [x2, #0x13]
    // 0xbdcf28: DecompressPointer r0
    //     0xbdcf28: add             x0, x0, HEAP, lsl #32
    // 0xbdcf2c: ldur            x1, [fp, #-0x10]
    // 0xbdcf30: r3 = LoadClassIdInstr(r1)
    //     0xbdcf30: ldur            x3, [x1, #-1]
    //     0xbdcf34: ubfx            x3, x3, #0xc, #0x14
    // 0xbdcf38: stp             x0, x1, [SP]
    // 0xbdcf3c: mov             x0, x3
    // 0xbdcf40: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdcf40: sub             lr, x0, #0xb7
    //     0xbdcf44: ldr             lr, [x21, lr, lsl #3]
    //     0xbdcf48: blr             lr
    // 0xbdcf4c: LoadField: r1 = r0->field_7
    //     0xbdcf4c: ldur            w1, [x0, #7]
    // 0xbdcf50: DecompressPointer r1
    //     0xbdcf50: add             x1, x1, HEAP, lsl #32
    // 0xbdcf54: cmp             w1, NULL
    // 0xbdcf58: b.ne            #0xbdcf64
    // 0xbdcf5c: r4 = ""
    //     0xbdcf5c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdcf60: b               #0xbdcf68
    // 0xbdcf64: mov             x4, x1
    // 0xbdcf68: ldur            x3, [fp, #-8]
    // 0xbdcf6c: ldur            x0, [fp, #-0x10]
    // 0xbdcf70: ldur            x2, [fp, #-0x28]
    // 0xbdcf74: stur            x4, [fp, #-0x30]
    // 0xbdcf78: LoadField: r1 = r3->field_f
    //     0xbdcf78: ldur            w1, [x3, #0xf]
    // 0xbdcf7c: DecompressPointer r1
    //     0xbdcf7c: add             x1, x1, HEAP, lsl #32
    // 0xbdcf80: cmp             w1, NULL
    // 0xbdcf84: b.eq            #0xbdd548
    // 0xbdcf88: r0 = of()
    //     0xbdcf88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdcf8c: LoadField: r1 = r0->field_87
    //     0xbdcf8c: ldur            w1, [x0, #0x87]
    // 0xbdcf90: DecompressPointer r1
    //     0xbdcf90: add             x1, x1, HEAP, lsl #32
    // 0xbdcf94: LoadField: r0 = r1->field_27
    //     0xbdcf94: ldur            w0, [x1, #0x27]
    // 0xbdcf98: DecompressPointer r0
    //     0xbdcf98: add             x0, x0, HEAP, lsl #32
    // 0xbdcf9c: r16 = 21.000000
    //     0xbdcf9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbdcfa0: ldr             x16, [x16, #0x9b0]
    // 0xbdcfa4: r30 = Instance_Color
    //     0xbdcfa4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdcfa8: stp             lr, x16, [SP]
    // 0xbdcfac: mov             x1, x0
    // 0xbdcfb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdcfb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdcfb4: ldr             x4, [x4, #0xaa0]
    // 0xbdcfb8: r0 = copyWith()
    //     0xbdcfb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdcfbc: stur            x0, [fp, #-0x40]
    // 0xbdcfc0: r0 = Text()
    //     0xbdcfc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdcfc4: mov             x1, x0
    // 0xbdcfc8: ldur            x0, [fp, #-0x30]
    // 0xbdcfcc: stur            x1, [fp, #-0x48]
    // 0xbdcfd0: StoreField: r1->field_b = r0
    //     0xbdcfd0: stur            w0, [x1, #0xb]
    // 0xbdcfd4: ldur            x0, [fp, #-0x40]
    // 0xbdcfd8: StoreField: r1->field_13 = r0
    //     0xbdcfd8: stur            w0, [x1, #0x13]
    // 0xbdcfdc: r2 = 4
    //     0xbdcfdc: movz            x2, #0x4
    // 0xbdcfe0: StoreField: r1->field_37 = r2
    //     0xbdcfe0: stur            w2, [x1, #0x37]
    // 0xbdcfe4: r0 = Padding()
    //     0xbdcfe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdcfe8: mov             x1, x0
    // 0xbdcfec: r0 = Instance_EdgeInsets
    //     0xbdcfec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbdcff0: ldr             x0, [x0, #0x668]
    // 0xbdcff4: stur            x1, [fp, #-0x30]
    // 0xbdcff8: StoreField: r1->field_f = r0
    //     0xbdcff8: stur            w0, [x1, #0xf]
    // 0xbdcffc: ldur            x0, [fp, #-0x48]
    // 0xbdd000: StoreField: r1->field_b = r0
    //     0xbdd000: stur            w0, [x1, #0xb]
    // 0xbdd004: ldur            x2, [fp, #-0x28]
    // 0xbdd008: LoadField: r0 = r2->field_13
    //     0xbdd008: ldur            w0, [x2, #0x13]
    // 0xbdd00c: DecompressPointer r0
    //     0xbdd00c: add             x0, x0, HEAP, lsl #32
    // 0xbdd010: ldur            x3, [fp, #-0x10]
    // 0xbdd014: r4 = LoadClassIdInstr(r3)
    //     0xbdd014: ldur            x4, [x3, #-1]
    //     0xbdd018: ubfx            x4, x4, #0xc, #0x14
    // 0xbdd01c: stp             x0, x3, [SP]
    // 0xbdd020: mov             x0, x4
    // 0xbdd024: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd024: sub             lr, x0, #0xb7
    //     0xbdd028: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd02c: blr             lr
    // 0xbdd030: LoadField: r1 = r0->field_f
    //     0xbdd030: ldur            w1, [x0, #0xf]
    // 0xbdd034: DecompressPointer r1
    //     0xbdd034: add             x1, x1, HEAP, lsl #32
    // 0xbdd038: cmp             w1, NULL
    // 0xbdd03c: b.ne            #0xbdd048
    // 0xbdd040: r0 = Null
    //     0xbdd040: mov             x0, NULL
    // 0xbdd044: b               #0xbdd060
    // 0xbdd048: LoadField: r0 = r1->field_7
    //     0xbdd048: ldur            w0, [x1, #7]
    // 0xbdd04c: cbnz            w0, #0xbdd058
    // 0xbdd050: r1 = false
    //     0xbdd050: add             x1, NULL, #0x30  ; false
    // 0xbdd054: b               #0xbdd05c
    // 0xbdd058: r1 = true
    //     0xbdd058: add             x1, NULL, #0x20  ; true
    // 0xbdd05c: mov             x0, x1
    // 0xbdd060: cmp             w0, NULL
    // 0xbdd064: b.ne            #0xbdd070
    // 0xbdd068: r3 = false
    //     0xbdd068: add             x3, NULL, #0x30  ; false
    // 0xbdd06c: b               #0xbdd074
    // 0xbdd070: mov             x3, x0
    // 0xbdd074: ldur            x1, [fp, #-8]
    // 0xbdd078: ldur            x0, [fp, #-0x10]
    // 0xbdd07c: ldur            x2, [fp, #-0x28]
    // 0xbdd080: stur            x3, [fp, #-0x40]
    // 0xbdd084: r16 = <EdgeInsets>
    //     0xbdd084: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdd088: ldr             x16, [x16, #0xda0]
    // 0xbdd08c: r30 = Instance_EdgeInsets
    //     0xbdd08c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdd090: ldr             lr, [lr, #0x1f0]
    // 0xbdd094: stp             lr, x16, [SP]
    // 0xbdd098: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdd098: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdd09c: r0 = all()
    //     0xbdd09c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdd0a0: mov             x2, x0
    // 0xbdd0a4: ldur            x0, [fp, #-8]
    // 0xbdd0a8: stur            x2, [fp, #-0x48]
    // 0xbdd0ac: LoadField: r1 = r0->field_f
    //     0xbdd0ac: ldur            w1, [x0, #0xf]
    // 0xbdd0b0: DecompressPointer r1
    //     0xbdd0b0: add             x1, x1, HEAP, lsl #32
    // 0xbdd0b4: cmp             w1, NULL
    // 0xbdd0b8: b.eq            #0xbdd54c
    // 0xbdd0bc: r0 = of()
    //     0xbdd0bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdd0c0: LoadField: r1 = r0->field_5b
    //     0xbdd0c0: ldur            w1, [x0, #0x5b]
    // 0xbdd0c4: DecompressPointer r1
    //     0xbdd0c4: add             x1, x1, HEAP, lsl #32
    // 0xbdd0c8: r16 = <Color>
    //     0xbdd0c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbdd0cc: ldr             x16, [x16, #0xf80]
    // 0xbdd0d0: stp             x1, x16, [SP]
    // 0xbdd0d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdd0d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdd0d8: r0 = all()
    //     0xbdd0d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdd0dc: stur            x0, [fp, #-0x50]
    // 0xbdd0e0: r16 = <RoundedRectangleBorder>
    //     0xbdd0e0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdd0e4: ldr             x16, [x16, #0xf78]
    // 0xbdd0e8: r30 = Instance_RoundedRectangleBorder
    //     0xbdd0e8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbdd0ec: ldr             lr, [lr, #0xd68]
    // 0xbdd0f0: stp             lr, x16, [SP]
    // 0xbdd0f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdd0f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdd0f8: r0 = all()
    //     0xbdd0f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdd0fc: stur            x0, [fp, #-0x58]
    // 0xbdd100: r0 = ButtonStyle()
    //     0xbdd100: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbdd104: mov             x1, x0
    // 0xbdd108: ldur            x0, [fp, #-0x50]
    // 0xbdd10c: stur            x1, [fp, #-0x60]
    // 0xbdd110: StoreField: r1->field_b = r0
    //     0xbdd110: stur            w0, [x1, #0xb]
    // 0xbdd114: ldur            x0, [fp, #-0x48]
    // 0xbdd118: StoreField: r1->field_23 = r0
    //     0xbdd118: stur            w0, [x1, #0x23]
    // 0xbdd11c: ldur            x0, [fp, #-0x58]
    // 0xbdd120: StoreField: r1->field_43 = r0
    //     0xbdd120: stur            w0, [x1, #0x43]
    // 0xbdd124: r0 = TextButtonThemeData()
    //     0xbdd124: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdd128: mov             x1, x0
    // 0xbdd12c: ldur            x0, [fp, #-0x60]
    // 0xbdd130: stur            x1, [fp, #-0x48]
    // 0xbdd134: StoreField: r1->field_7 = r0
    //     0xbdd134: stur            w0, [x1, #7]
    // 0xbdd138: ldur            x2, [fp, #-0x28]
    // 0xbdd13c: LoadField: r0 = r2->field_13
    //     0xbdd13c: ldur            w0, [x2, #0x13]
    // 0xbdd140: DecompressPointer r0
    //     0xbdd140: add             x0, x0, HEAP, lsl #32
    // 0xbdd144: ldur            x3, [fp, #-0x10]
    // 0xbdd148: r4 = LoadClassIdInstr(r3)
    //     0xbdd148: ldur            x4, [x3, #-1]
    //     0xbdd14c: ubfx            x4, x4, #0xc, #0x14
    // 0xbdd150: stp             x0, x3, [SP]
    // 0xbdd154: mov             x0, x4
    // 0xbdd158: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbdd158: sub             lr, x0, #0xb7
    //     0xbdd15c: ldr             lr, [x21, lr, lsl #3]
    //     0xbdd160: blr             lr
    // 0xbdd164: LoadField: r1 = r0->field_f
    //     0xbdd164: ldur            w1, [x0, #0xf]
    // 0xbdd168: DecompressPointer r1
    //     0xbdd168: add             x1, x1, HEAP, lsl #32
    // 0xbdd16c: cmp             w1, NULL
    // 0xbdd170: b.ne            #0xbdd17c
    // 0xbdd174: r6 = ""
    //     0xbdd174: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdd178: b               #0xbdd180
    // 0xbdd17c: mov             x6, x1
    // 0xbdd180: ldur            x1, [fp, #-8]
    // 0xbdd184: ldur            x5, [fp, #-0x38]
    // 0xbdd188: ldur            x4, [fp, #-0x20]
    // 0xbdd18c: ldur            d0, [fp, #-0x68]
    // 0xbdd190: ldur            x3, [fp, #-0x30]
    // 0xbdd194: ldur            x2, [fp, #-0x40]
    // 0xbdd198: ldur            x0, [fp, #-0x48]
    // 0xbdd19c: stur            x6, [fp, #-0x10]
    // 0xbdd1a0: LoadField: r7 = r1->field_f
    //     0xbdd1a0: ldur            w7, [x1, #0xf]
    // 0xbdd1a4: DecompressPointer r7
    //     0xbdd1a4: add             x7, x7, HEAP, lsl #32
    // 0xbdd1a8: cmp             w7, NULL
    // 0xbdd1ac: b.eq            #0xbdd550
    // 0xbdd1b0: mov             x1, x7
    // 0xbdd1b4: r0 = of()
    //     0xbdd1b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdd1b8: LoadField: r1 = r0->field_87
    //     0xbdd1b8: ldur            w1, [x0, #0x87]
    // 0xbdd1bc: DecompressPointer r1
    //     0xbdd1bc: add             x1, x1, HEAP, lsl #32
    // 0xbdd1c0: LoadField: r0 = r1->field_7
    //     0xbdd1c0: ldur            w0, [x1, #7]
    // 0xbdd1c4: DecompressPointer r0
    //     0xbdd1c4: add             x0, x0, HEAP, lsl #32
    // 0xbdd1c8: r16 = Instance_Color
    //     0xbdd1c8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbdd1cc: r30 = 14.000000
    //     0xbdd1cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbdd1d0: ldr             lr, [lr, #0x1d8]
    // 0xbdd1d4: stp             lr, x16, [SP]
    // 0xbdd1d8: mov             x1, x0
    // 0xbdd1dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbdd1dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbdd1e0: ldr             x4, [x4, #0x9b8]
    // 0xbdd1e4: r0 = copyWith()
    //     0xbdd1e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdd1e8: stur            x0, [fp, #-8]
    // 0xbdd1ec: r0 = Text()
    //     0xbdd1ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdd1f0: mov             x3, x0
    // 0xbdd1f4: ldur            x0, [fp, #-0x10]
    // 0xbdd1f8: stur            x3, [fp, #-0x50]
    // 0xbdd1fc: StoreField: r3->field_b = r0
    //     0xbdd1fc: stur            w0, [x3, #0xb]
    // 0xbdd200: ldur            x0, [fp, #-8]
    // 0xbdd204: StoreField: r3->field_13 = r0
    //     0xbdd204: stur            w0, [x3, #0x13]
    // 0xbdd208: ldur            x2, [fp, #-0x28]
    // 0xbdd20c: r1 = Function '<anonymous closure>':.
    //     0xbdd20c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53908] AnonymousClosure: (0xbdd578), in [package:customer_app/app/presentation/views/line/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider (0xbdccb4)
    //     0xbdd210: ldr             x1, [x1, #0x908]
    // 0xbdd214: r0 = AllocateClosure()
    //     0xbdd214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdd218: stur            x0, [fp, #-8]
    // 0xbdd21c: r0 = TextButton()
    //     0xbdd21c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbdd220: mov             x1, x0
    // 0xbdd224: ldur            x0, [fp, #-8]
    // 0xbdd228: stur            x1, [fp, #-0x10]
    // 0xbdd22c: StoreField: r1->field_b = r0
    //     0xbdd22c: stur            w0, [x1, #0xb]
    // 0xbdd230: r0 = false
    //     0xbdd230: add             x0, NULL, #0x30  ; false
    // 0xbdd234: StoreField: r1->field_27 = r0
    //     0xbdd234: stur            w0, [x1, #0x27]
    // 0xbdd238: r2 = true
    //     0xbdd238: add             x2, NULL, #0x20  ; true
    // 0xbdd23c: StoreField: r1->field_2f = r2
    //     0xbdd23c: stur            w2, [x1, #0x2f]
    // 0xbdd240: ldur            x2, [fp, #-0x50]
    // 0xbdd244: StoreField: r1->field_37 = r2
    //     0xbdd244: stur            w2, [x1, #0x37]
    // 0xbdd248: r0 = TextButtonTheme()
    //     0xbdd248: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbdd24c: mov             x1, x0
    // 0xbdd250: ldur            x0, [fp, #-0x48]
    // 0xbdd254: stur            x1, [fp, #-8]
    // 0xbdd258: StoreField: r1->field_f = r0
    //     0xbdd258: stur            w0, [x1, #0xf]
    // 0xbdd25c: ldur            x0, [fp, #-0x10]
    // 0xbdd260: StoreField: r1->field_b = r0
    //     0xbdd260: stur            w0, [x1, #0xb]
    // 0xbdd264: r0 = Visibility()
    //     0xbdd264: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbdd268: mov             x1, x0
    // 0xbdd26c: ldur            x0, [fp, #-8]
    // 0xbdd270: stur            x1, [fp, #-0x10]
    // 0xbdd274: StoreField: r1->field_b = r0
    //     0xbdd274: stur            w0, [x1, #0xb]
    // 0xbdd278: r0 = Instance_SizedBox
    //     0xbdd278: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbdd27c: StoreField: r1->field_f = r0
    //     0xbdd27c: stur            w0, [x1, #0xf]
    // 0xbdd280: ldur            x2, [fp, #-0x40]
    // 0xbdd284: StoreField: r1->field_13 = r2
    //     0xbdd284: stur            w2, [x1, #0x13]
    // 0xbdd288: r2 = false
    //     0xbdd288: add             x2, NULL, #0x30  ; false
    // 0xbdd28c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbdd28c: stur            w2, [x1, #0x17]
    // 0xbdd290: StoreField: r1->field_1b = r2
    //     0xbdd290: stur            w2, [x1, #0x1b]
    // 0xbdd294: StoreField: r1->field_1f = r2
    //     0xbdd294: stur            w2, [x1, #0x1f]
    // 0xbdd298: StoreField: r1->field_23 = r2
    //     0xbdd298: stur            w2, [x1, #0x23]
    // 0xbdd29c: StoreField: r1->field_27 = r2
    //     0xbdd29c: stur            w2, [x1, #0x27]
    // 0xbdd2a0: StoreField: r1->field_2b = r2
    //     0xbdd2a0: stur            w2, [x1, #0x2b]
    // 0xbdd2a4: r0 = Padding()
    //     0xbdd2a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdd2a8: mov             x3, x0
    // 0xbdd2ac: r0 = Instance_EdgeInsets
    //     0xbdd2ac: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbdd2b0: ldr             x0, [x0, #0x858]
    // 0xbdd2b4: stur            x3, [fp, #-8]
    // 0xbdd2b8: StoreField: r3->field_f = r0
    //     0xbdd2b8: stur            w0, [x3, #0xf]
    // 0xbdd2bc: ldur            x0, [fp, #-0x10]
    // 0xbdd2c0: StoreField: r3->field_b = r0
    //     0xbdd2c0: stur            w0, [x3, #0xb]
    // 0xbdd2c4: r1 = Null
    //     0xbdd2c4: mov             x1, NULL
    // 0xbdd2c8: r2 = 4
    //     0xbdd2c8: movz            x2, #0x4
    // 0xbdd2cc: r0 = AllocateArray()
    //     0xbdd2cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdd2d0: mov             x2, x0
    // 0xbdd2d4: ldur            x0, [fp, #-0x30]
    // 0xbdd2d8: stur            x2, [fp, #-0x10]
    // 0xbdd2dc: StoreField: r2->field_f = r0
    //     0xbdd2dc: stur            w0, [x2, #0xf]
    // 0xbdd2e0: ldur            x0, [fp, #-8]
    // 0xbdd2e4: StoreField: r2->field_13 = r0
    //     0xbdd2e4: stur            w0, [x2, #0x13]
    // 0xbdd2e8: r1 = <Widget>
    //     0xbdd2e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdd2ec: r0 = AllocateGrowableArray()
    //     0xbdd2ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdd2f0: mov             x1, x0
    // 0xbdd2f4: ldur            x0, [fp, #-0x10]
    // 0xbdd2f8: stur            x1, [fp, #-8]
    // 0xbdd2fc: StoreField: r1->field_f = r0
    //     0xbdd2fc: stur            w0, [x1, #0xf]
    // 0xbdd300: r2 = 4
    //     0xbdd300: movz            x2, #0x4
    // 0xbdd304: StoreField: r1->field_b = r2
    //     0xbdd304: stur            w2, [x1, #0xb]
    // 0xbdd308: r0 = Column()
    //     0xbdd308: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdd30c: mov             x3, x0
    // 0xbdd310: r0 = Instance_Axis
    //     0xbdd310: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdd314: stur            x3, [fp, #-0x10]
    // 0xbdd318: StoreField: r3->field_f = r0
    //     0xbdd318: stur            w0, [x3, #0xf]
    // 0xbdd31c: r0 = Instance_MainAxisAlignment
    //     0xbdd31c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbdd320: ldr             x0, [x0, #0xab0]
    // 0xbdd324: StoreField: r3->field_13 = r0
    //     0xbdd324: stur            w0, [x3, #0x13]
    // 0xbdd328: r0 = Instance_MainAxisSize
    //     0xbdd328: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdd32c: ldr             x0, [x0, #0xa10]
    // 0xbdd330: ArrayStore: r3[0] = r0  ; List_4
    //     0xbdd330: stur            w0, [x3, #0x17]
    // 0xbdd334: r0 = Instance_CrossAxisAlignment
    //     0xbdd334: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdd338: ldr             x0, [x0, #0xa18]
    // 0xbdd33c: StoreField: r3->field_1b = r0
    //     0xbdd33c: stur            w0, [x3, #0x1b]
    // 0xbdd340: r0 = Instance_VerticalDirection
    //     0xbdd340: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdd344: ldr             x0, [x0, #0xa20]
    // 0xbdd348: StoreField: r3->field_23 = r0
    //     0xbdd348: stur            w0, [x3, #0x23]
    // 0xbdd34c: r0 = Instance_Clip
    //     0xbdd34c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdd350: ldr             x0, [x0, #0x38]
    // 0xbdd354: StoreField: r3->field_2b = r0
    //     0xbdd354: stur            w0, [x3, #0x2b]
    // 0xbdd358: StoreField: r3->field_2f = rZR
    //     0xbdd358: stur            xzr, [x3, #0x2f]
    // 0xbdd35c: ldur            x0, [fp, #-8]
    // 0xbdd360: StoreField: r3->field_b = r0
    //     0xbdd360: stur            w0, [x3, #0xb]
    // 0xbdd364: r1 = Null
    //     0xbdd364: mov             x1, NULL
    // 0xbdd368: r2 = 2
    //     0xbdd368: movz            x2, #0x2
    // 0xbdd36c: r0 = AllocateArray()
    //     0xbdd36c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdd370: mov             x2, x0
    // 0xbdd374: ldur            x0, [fp, #-0x10]
    // 0xbdd378: stur            x2, [fp, #-8]
    // 0xbdd37c: StoreField: r2->field_f = r0
    //     0xbdd37c: stur            w0, [x2, #0xf]
    // 0xbdd380: r1 = <Widget>
    //     0xbdd380: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdd384: r0 = AllocateGrowableArray()
    //     0xbdd384: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdd388: mov             x1, x0
    // 0xbdd38c: ldur            x0, [fp, #-8]
    // 0xbdd390: stur            x1, [fp, #-0x10]
    // 0xbdd394: StoreField: r1->field_f = r0
    //     0xbdd394: stur            w0, [x1, #0xf]
    // 0xbdd398: r0 = 2
    //     0xbdd398: movz            x0, #0x2
    // 0xbdd39c: StoreField: r1->field_b = r0
    //     0xbdd39c: stur            w0, [x1, #0xb]
    // 0xbdd3a0: r0 = Stack()
    //     0xbdd3a0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbdd3a4: mov             x1, x0
    // 0xbdd3a8: r0 = Instance_Alignment
    //     0xbdd3a8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbdd3ac: ldr             x0, [x0, #0xb10]
    // 0xbdd3b0: stur            x1, [fp, #-0x28]
    // 0xbdd3b4: StoreField: r1->field_f = r0
    //     0xbdd3b4: stur            w0, [x1, #0xf]
    // 0xbdd3b8: r0 = Instance_StackFit
    //     0xbdd3b8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbdd3bc: ldr             x0, [x0, #0xfa8]
    // 0xbdd3c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdd3c0: stur            w0, [x1, #0x17]
    // 0xbdd3c4: r2 = Instance_Clip
    //     0xbdd3c4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdd3c8: ldr             x2, [x2, #0x7e0]
    // 0xbdd3cc: StoreField: r1->field_1b = r2
    //     0xbdd3cc: stur            w2, [x1, #0x1b]
    // 0xbdd3d0: ldur            x3, [fp, #-0x10]
    // 0xbdd3d4: StoreField: r1->field_b = r3
    //     0xbdd3d4: stur            w3, [x1, #0xb]
    // 0xbdd3d8: ldur            d0, [fp, #-0x68]
    // 0xbdd3dc: r3 = inline_Allocate_Double()
    //     0xbdd3dc: ldp             x3, x4, [THR, #0x50]  ; THR::top
    //     0xbdd3e0: add             x3, x3, #0x10
    //     0xbdd3e4: cmp             x4, x3
    //     0xbdd3e8: b.ls            #0xbdd554
    //     0xbdd3ec: str             x3, [THR, #0x50]  ; THR::top
    //     0xbdd3f0: sub             x3, x3, #0xf
    //     0xbdd3f4: movz            x4, #0xe15c
    //     0xbdd3f8: movk            x4, #0x3, lsl #16
    //     0xbdd3fc: stur            x4, [x3, #-1]
    // 0xbdd400: StoreField: r3->field_7 = d0
    //     0xbdd400: stur            d0, [x3, #7]
    // 0xbdd404: stur            x3, [fp, #-8]
    // 0xbdd408: r0 = Container()
    //     0xbdd408: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdd40c: stur            x0, [fp, #-0x10]
    // 0xbdd410: r16 = Instance_Alignment
    //     0xbdd410: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xbdd414: ldr             x16, [x16, #0xcb0]
    // 0xbdd418: ldur            lr, [fp, #-8]
    // 0xbdd41c: stp             lr, x16, [SP, #0x18]
    // 0xbdd420: r16 = 132.000000
    //     0xbdd420: add             x16, PP, #0x53, lsl #12  ; [pp+0x53910] 132
    //     0xbdd424: ldr             x16, [x16, #0x910]
    // 0xbdd428: r30 = Instance_Color
    //     0xbdd428: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbdd42c: stp             lr, x16, [SP, #8]
    // 0xbdd430: ldur            x16, [fp, #-0x28]
    // 0xbdd434: str             x16, [SP]
    // 0xbdd438: mov             x1, x0
    // 0xbdd43c: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x1, child, 0x5, color, 0x4, height, 0x3, width, 0x2, null]
    //     0xbdd43c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53918] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x1, "child", 0x5, "color", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xbdd440: ldr             x4, [x4, #0x918]
    // 0xbdd444: r0 = Container()
    //     0xbdd444: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdd448: r0 = Padding()
    //     0xbdd448: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdd44c: mov             x1, x0
    // 0xbdd450: r0 = Instance_EdgeInsets
    //     0xbdd450: add             x0, PP, #0x52, lsl #12  ; [pp+0x52c40] Obj!EdgeInsets@d596f1
    //     0xbdd454: ldr             x0, [x0, #0xc40]
    // 0xbdd458: stur            x1, [fp, #-8]
    // 0xbdd45c: StoreField: r1->field_f = r0
    //     0xbdd45c: stur            w0, [x1, #0xf]
    // 0xbdd460: ldur            x0, [fp, #-0x10]
    // 0xbdd464: StoreField: r1->field_b = r0
    //     0xbdd464: stur            w0, [x1, #0xb]
    // 0xbdd468: r0 = Visibility()
    //     0xbdd468: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbdd46c: mov             x3, x0
    // 0xbdd470: ldur            x0, [fp, #-8]
    // 0xbdd474: stur            x3, [fp, #-0x10]
    // 0xbdd478: StoreField: r3->field_b = r0
    //     0xbdd478: stur            w0, [x3, #0xb]
    // 0xbdd47c: r0 = Instance_SizedBox
    //     0xbdd47c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbdd480: StoreField: r3->field_f = r0
    //     0xbdd480: stur            w0, [x3, #0xf]
    // 0xbdd484: ldur            x0, [fp, #-0x20]
    // 0xbdd488: StoreField: r3->field_13 = r0
    //     0xbdd488: stur            w0, [x3, #0x13]
    // 0xbdd48c: r0 = false
    //     0xbdd48c: add             x0, NULL, #0x30  ; false
    // 0xbdd490: ArrayStore: r3[0] = r0  ; List_4
    //     0xbdd490: stur            w0, [x3, #0x17]
    // 0xbdd494: StoreField: r3->field_1b = r0
    //     0xbdd494: stur            w0, [x3, #0x1b]
    // 0xbdd498: StoreField: r3->field_1f = r0
    //     0xbdd498: stur            w0, [x3, #0x1f]
    // 0xbdd49c: StoreField: r3->field_23 = r0
    //     0xbdd49c: stur            w0, [x3, #0x23]
    // 0xbdd4a0: StoreField: r3->field_27 = r0
    //     0xbdd4a0: stur            w0, [x3, #0x27]
    // 0xbdd4a4: StoreField: r3->field_2b = r0
    //     0xbdd4a4: stur            w0, [x3, #0x2b]
    // 0xbdd4a8: r1 = Null
    //     0xbdd4a8: mov             x1, NULL
    // 0xbdd4ac: r2 = 4
    //     0xbdd4ac: movz            x2, #0x4
    // 0xbdd4b0: r0 = AllocateArray()
    //     0xbdd4b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdd4b4: mov             x2, x0
    // 0xbdd4b8: ldur            x0, [fp, #-0x38]
    // 0xbdd4bc: stur            x2, [fp, #-8]
    // 0xbdd4c0: StoreField: r2->field_f = r0
    //     0xbdd4c0: stur            w0, [x2, #0xf]
    // 0xbdd4c4: ldur            x0, [fp, #-0x10]
    // 0xbdd4c8: StoreField: r2->field_13 = r0
    //     0xbdd4c8: stur            w0, [x2, #0x13]
    // 0xbdd4cc: r1 = <Widget>
    //     0xbdd4cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdd4d0: r0 = AllocateGrowableArray()
    //     0xbdd4d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdd4d4: mov             x1, x0
    // 0xbdd4d8: ldur            x0, [fp, #-8]
    // 0xbdd4dc: stur            x1, [fp, #-0x10]
    // 0xbdd4e0: StoreField: r1->field_f = r0
    //     0xbdd4e0: stur            w0, [x1, #0xf]
    // 0xbdd4e4: r0 = 4
    //     0xbdd4e4: movz            x0, #0x4
    // 0xbdd4e8: StoreField: r1->field_b = r0
    //     0xbdd4e8: stur            w0, [x1, #0xb]
    // 0xbdd4ec: r0 = Stack()
    //     0xbdd4ec: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbdd4f0: r1 = Instance_Alignment
    //     0xbdd4f0: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xbdd4f4: ldr             x1, [x1, #0xcb0]
    // 0xbdd4f8: StoreField: r0->field_f = r1
    //     0xbdd4f8: stur            w1, [x0, #0xf]
    // 0xbdd4fc: r1 = Instance_StackFit
    //     0xbdd4fc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbdd500: ldr             x1, [x1, #0xfa8]
    // 0xbdd504: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdd504: stur            w1, [x0, #0x17]
    // 0xbdd508: r1 = Instance_Clip
    //     0xbdd508: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdd50c: ldr             x1, [x1, #0x7e0]
    // 0xbdd510: StoreField: r0->field_1b = r1
    //     0xbdd510: stur            w1, [x0, #0x1b]
    // 0xbdd514: ldur            x1, [fp, #-0x10]
    // 0xbdd518: StoreField: r0->field_b = r1
    //     0xbdd518: stur            w1, [x0, #0xb]
    // 0xbdd51c: LeaveFrame
    //     0xbdd51c: mov             SP, fp
    //     0xbdd520: ldp             fp, lr, [SP], #0x10
    // 0xbdd524: ret
    //     0xbdd524: ret             
    // 0xbdd528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdd528: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdd52c: b               #0xbdccd8
    // 0xbdd530: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdd530: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdd534: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdd534: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdd538: SaveReg d0
    //     0xbdd538: str             q0, [SP, #-0x10]!
    // 0xbdd53c: r0 = AllocateDouble()
    //     0xbdd53c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbdd540: RestoreReg d0
    //     0xbdd540: ldr             q0, [SP], #0x10
    // 0xbdd544: b               #0xbdce3c
    // 0xbdd548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdd548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdd54c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdd54c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdd550: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbdd550: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbdd554: SaveReg d0
    //     0xbdd554: str             q0, [SP, #-0x10]!
    // 0xbdd558: stp             x1, x2, [SP, #-0x10]!
    // 0xbdd55c: SaveReg r0
    //     0xbdd55c: str             x0, [SP, #-8]!
    // 0xbdd560: r0 = AllocateDouble()
    //     0xbdd560: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbdd564: mov             x3, x0
    // 0xbdd568: RestoreReg r0
    //     0xbdd568: ldr             x0, [SP], #8
    // 0xbdd56c: ldp             x1, x2, [SP], #0x10
    // 0xbdd570: RestoreReg d0
    //     0xbdd570: ldr             q0, [SP], #0x10
    // 0xbdd574: b               #0xbdd400
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbdd578, size: 0xf0
    // 0xbdd578: EnterFrame
    //     0xbdd578: stp             fp, lr, [SP, #-0x10]!
    //     0xbdd57c: mov             fp, SP
    // 0xbdd580: AllocStack(0x20)
    //     0xbdd580: sub             SP, SP, #0x20
    // 0xbdd584: SetupParameters()
    //     0xbdd584: ldr             x0, [fp, #0x10]
    //     0xbdd588: ldur            w1, [x0, #0x17]
    //     0xbdd58c: add             x1, x1, HEAP, lsl #32
    // 0xbdd590: CheckStackOverflow
    //     0xbdd590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdd594: cmp             SP, x16
    //     0xbdd598: b.ls            #0xbdd658
    // 0xbdd59c: LoadField: r0 = r1->field_f
    //     0xbdd59c: ldur            w0, [x1, #0xf]
    // 0xbdd5a0: DecompressPointer r0
    //     0xbdd5a0: add             x0, x0, HEAP, lsl #32
    // 0xbdd5a4: LoadField: r2 = r0->field_b
    //     0xbdd5a4: ldur            w2, [x0, #0xb]
    // 0xbdd5a8: DecompressPointer r2
    //     0xbdd5a8: add             x2, x2, HEAP, lsl #32
    // 0xbdd5ac: cmp             w2, NULL
    // 0xbdd5b0: b.eq            #0xbdd660
    // 0xbdd5b4: LoadField: r3 = r2->field_b
    //     0xbdd5b4: ldur            w3, [x2, #0xb]
    // 0xbdd5b8: DecompressPointer r3
    //     0xbdd5b8: add             x3, x3, HEAP, lsl #32
    // 0xbdd5bc: cmp             w3, NULL
    // 0xbdd5c0: b.ne            #0xbdd5cc
    // 0xbdd5c4: r0 = Null
    //     0xbdd5c4: mov             x0, NULL
    // 0xbdd5c8: b               #0xbdd610
    // 0xbdd5cc: LoadField: r0 = r1->field_13
    //     0xbdd5cc: ldur            w0, [x1, #0x13]
    // 0xbdd5d0: DecompressPointer r0
    //     0xbdd5d0: add             x0, x0, HEAP, lsl #32
    // 0xbdd5d4: LoadField: r1 = r3->field_b
    //     0xbdd5d4: ldur            w1, [x3, #0xb]
    // 0xbdd5d8: r4 = LoadInt32Instr(r0)
    //     0xbdd5d8: sbfx            x4, x0, #1, #0x1f
    //     0xbdd5dc: tbz             w0, #0, #0xbdd5e4
    //     0xbdd5e0: ldur            x4, [x0, #7]
    // 0xbdd5e4: r0 = LoadInt32Instr(r1)
    //     0xbdd5e4: sbfx            x0, x1, #1, #0x1f
    // 0xbdd5e8: mov             x1, x4
    // 0xbdd5ec: cmp             x1, x0
    // 0xbdd5f0: b.hs            #0xbdd664
    // 0xbdd5f4: LoadField: r0 = r3->field_f
    //     0xbdd5f4: ldur            w0, [x3, #0xf]
    // 0xbdd5f8: DecompressPointer r0
    //     0xbdd5f8: add             x0, x0, HEAP, lsl #32
    // 0xbdd5fc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbdd5fc: add             x16, x0, x4, lsl #2
    //     0xbdd600: ldur            w1, [x16, #0xf]
    // 0xbdd604: DecompressPointer r1
    //     0xbdd604: add             x1, x1, HEAP, lsl #32
    // 0xbdd608: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbdd608: ldur            w0, [x1, #0x17]
    // 0xbdd60c: DecompressPointer r0
    //     0xbdd60c: add             x0, x0, HEAP, lsl #32
    // 0xbdd610: LoadField: r1 = r2->field_1b
    //     0xbdd610: ldur            w1, [x2, #0x1b]
    // 0xbdd614: DecompressPointer r1
    //     0xbdd614: add             x1, x1, HEAP, lsl #32
    // 0xbdd618: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbdd618: ldur            w3, [x2, #0x17]
    // 0xbdd61c: DecompressPointer r3
    //     0xbdd61c: add             x3, x3, HEAP, lsl #32
    // 0xbdd620: LoadField: r4 = r2->field_23
    //     0xbdd620: ldur            w4, [x2, #0x23]
    // 0xbdd624: DecompressPointer r4
    //     0xbdd624: add             x4, x4, HEAP, lsl #32
    // 0xbdd628: stp             x0, x4, [SP, #0x10]
    // 0xbdd62c: stp             x3, x1, [SP]
    // 0xbdd630: r4 = 0
    //     0xbdd630: movz            x4, #0
    // 0xbdd634: ldr             x0, [SP, #0x18]
    // 0xbdd638: r16 = UnlinkedCall_0x613b5c
    //     0xbdd638: add             x16, PP, #0x53, lsl #12  ; [pp+0x53920] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdd63c: add             x16, x16, #0x920
    // 0xbdd640: ldp             x5, lr, [x16]
    // 0xbdd644: blr             lr
    // 0xbdd648: r0 = Null
    //     0xbdd648: mov             x0, NULL
    // 0xbdd64c: LeaveFrame
    //     0xbdd64c: mov             SP, fp
    //     0xbdd650: ldp             fp, lr, [SP], #0x10
    // 0xbdd654: ret
    //     0xbdd654: ret             
    // 0xbdd658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdd658: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdd65c: b               #0xbdd59c
    // 0xbdd660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdd660: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdd664: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdd664: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xbde44c, size: 0xa4
    // 0xbde44c: EnterFrame
    //     0xbde44c: stp             fp, lr, [SP, #-0x10]!
    //     0xbde450: mov             fp, SP
    // 0xbde454: AllocStack(0x40)
    //     0xbde454: sub             SP, SP, #0x40
    // 0xbde458: SetupParameters()
    //     0xbde458: ldr             x0, [fp, #0x48]
    //     0xbde45c: ldur            w1, [x0, #0x17]
    //     0xbde460: add             x1, x1, HEAP, lsl #32
    // 0xbde464: CheckStackOverflow
    //     0xbde464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbde468: cmp             SP, x16
    //     0xbde46c: b.ls            #0xbde4e4
    // 0xbde470: LoadField: r0 = r1->field_f
    //     0xbde470: ldur            w0, [x1, #0xf]
    // 0xbde474: DecompressPointer r0
    //     0xbde474: add             x0, x0, HEAP, lsl #32
    // 0xbde478: LoadField: r1 = r0->field_b
    //     0xbde478: ldur            w1, [x0, #0xb]
    // 0xbde47c: DecompressPointer r1
    //     0xbde47c: add             x1, x1, HEAP, lsl #32
    // 0xbde480: cmp             w1, NULL
    // 0xbde484: b.eq            #0xbde4ec
    // 0xbde488: LoadField: r0 = r1->field_1f
    //     0xbde488: ldur            w0, [x1, #0x1f]
    // 0xbde48c: DecompressPointer r0
    //     0xbde48c: add             x0, x0, HEAP, lsl #32
    // 0xbde490: ldr             x16, [fp, #0x40]
    // 0xbde494: stp             x16, x0, [SP, #0x30]
    // 0xbde498: ldr             x16, [fp, #0x38]
    // 0xbde49c: ldr             lr, [fp, #0x30]
    // 0xbde4a0: stp             lr, x16, [SP, #0x20]
    // 0xbde4a4: ldr             x16, [fp, #0x28]
    // 0xbde4a8: ldr             lr, [fp, #0x20]
    // 0xbde4ac: stp             lr, x16, [SP, #0x10]
    // 0xbde4b0: ldr             x16, [fp, #0x18]
    // 0xbde4b4: ldr             lr, [fp, #0x10]
    // 0xbde4b8: stp             lr, x16, [SP]
    // 0xbde4bc: r4 = 0
    //     0xbde4bc: movz            x4, #0
    // 0xbde4c0: ldr             x0, [SP, #0x38]
    // 0xbde4c4: r16 = UnlinkedCall_0x613b5c
    //     0xbde4c4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53930] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbde4c8: add             x16, x16, #0x930
    // 0xbde4cc: ldp             x5, lr, [x16]
    // 0xbde4d0: blr             lr
    // 0xbde4d4: r0 = Null
    //     0xbde4d4: mov             x0, NULL
    // 0xbde4d8: LeaveFrame
    //     0xbde4d8: mov             SP, fp
    //     0xbde4dc: ldp             fp, lr, [SP], #0x10
    // 0xbde4e0: ret
    //     0xbde4e0: ret             
    // 0xbde4e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbde4e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbde4e8: b               #0xbde470
    // 0xbde4ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde4ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3997, size: 0x34, field offset: 0xc
//   const constructor, 
class BannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8098c, size: 0x24
    // 0xc8098c: EnterFrame
    //     0xc8098c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80990: mov             fp, SP
    // 0xc80994: mov             x0, x1
    // 0xc80998: r1 = <BannerCrossLink>
    //     0xc80998: add             x1, PP, #0x48, lsl #12  ; [pp+0x48430] TypeArguments: <BannerCrossLink>
    //     0xc8099c: ldr             x1, [x1, #0x430]
    // 0xc809a0: r0 = _CarouselItemViewState()
    //     0xc809a0: bl              #0xc809b0  ; Allocate_CarouselItemViewStateStub -> _CarouselItemViewState (size=0x14)
    // 0xc809a4: LeaveFrame
    //     0xc809a4: mov             SP, fp
    //     0xc809a8: ldp             fp, lr, [SP], #0x10
    // 0xc809ac: ret
    //     0xc809ac: ret             
  }
}
