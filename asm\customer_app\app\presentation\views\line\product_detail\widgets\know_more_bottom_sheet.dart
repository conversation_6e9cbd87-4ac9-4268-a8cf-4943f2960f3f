// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart

// class id: 1049555, size: 0x8
class :: {
}

// class id: 3229, size: 0x14, field offset: 0x14
class _KnowMoreBottomSheetState extends State<dynamic> {

  [closure] Widget <anonymous closure>(dynamic, BuildContext, int, ConnectorType) {
    // ** addr: 0xa796c4, size: 0x2c
    // 0xa796c4: ldr             x1, [SP, #8]
    // 0xa796c8: r2 = LoadInt32Instr(r1)
    //     0xa796c8: sbfx            x2, x1, #1, #0x1f
    //     0xa796cc: tbz             w1, #0, #0xa796d4
    //     0xa796d0: ldur            x2, [x1, #7]
    // 0xa796d4: cmp             x2, #0
    // 0xa796d8: b.le            #0xa796e8
    // 0xa796dc: r0 = Instance_DecoratedLineConnector
    //     0xa796dc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52cd8] Obj!DecoratedLineConnector@d65991
    //     0xa796e0: ldr             x0, [x0, #0xcd8]
    // 0xa796e4: ret
    //     0xa796e4: ret             
    // 0xa796e8: r0 = Instance_SizedBox
    //     0xa796e8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa796ec: ret
    //     0xa796ec: ret             
  }
  [closure] Material <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa79710, size: 0xc
    // 0xa79710: r0 = Instance_Material
    //     0xa79710: add             x0, PP, #0x52, lsl #12  ; [pp+0x52ce0] Obj!Material@d657e1
    //     0xa79714: ldr             x0, [x0, #0xce0]
    // 0xa79718: ret
    //     0xa79718: ret             
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa7971c, size: 0x314
    // 0xa7971c: EnterFrame
    //     0xa7971c: stp             fp, lr, [SP, #-0x10]!
    //     0xa79720: mov             fp, SP
    // 0xa79724: AllocStack(0x30)
    //     0xa79724: sub             SP, SP, #0x30
    // 0xa79728: SetupParameters()
    //     0xa79728: ldr             x0, [fp, #0x20]
    //     0xa7972c: ldur            w2, [x0, #0x17]
    //     0xa79730: add             x2, x2, HEAP, lsl #32
    //     0xa79734: stur            x2, [fp, #-8]
    // 0xa79738: CheckStackOverflow
    //     0xa79738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7973c: cmp             SP, x16
    //     0xa79740: b.ls            #0xa79a18
    // 0xa79744: LoadField: r0 = r2->field_f
    //     0xa79744: ldur            w0, [x2, #0xf]
    // 0xa79748: DecompressPointer r0
    //     0xa79748: add             x0, x0, HEAP, lsl #32
    // 0xa7974c: LoadField: r1 = r0->field_b
    //     0xa7974c: ldur            w1, [x0, #0xb]
    // 0xa79750: DecompressPointer r1
    //     0xa79750: add             x1, x1, HEAP, lsl #32
    // 0xa79754: cmp             w1, NULL
    // 0xa79758: b.eq            #0xa79a20
    // 0xa7975c: LoadField: r0 = r1->field_b
    //     0xa7975c: ldur            w0, [x1, #0xb]
    // 0xa79760: DecompressPointer r0
    //     0xa79760: add             x0, x0, HEAP, lsl #32
    // 0xa79764: LoadField: r1 = r0->field_b
    //     0xa79764: ldur            w1, [x0, #0xb]
    // 0xa79768: DecompressPointer r1
    //     0xa79768: add             x1, x1, HEAP, lsl #32
    // 0xa7976c: cmp             w1, NULL
    // 0xa79770: b.ne            #0xa79780
    // 0xa79774: ldr             x3, [fp, #0x10]
    // 0xa79778: r0 = Null
    //     0xa79778: mov             x0, NULL
    // 0xa7977c: b               #0xa797d4
    // 0xa79780: ldr             x3, [fp, #0x10]
    // 0xa79784: LoadField: r0 = r1->field_f
    //     0xa79784: ldur            w0, [x1, #0xf]
    // 0xa79788: DecompressPointer r0
    //     0xa79788: add             x0, x0, HEAP, lsl #32
    // 0xa7978c: LoadField: r4 = r0->field_b
    //     0xa7978c: ldur            w4, [x0, #0xb]
    // 0xa79790: DecompressPointer r4
    //     0xa79790: add             x4, x4, HEAP, lsl #32
    // 0xa79794: LoadField: r0 = r4->field_b
    //     0xa79794: ldur            w0, [x4, #0xb]
    // 0xa79798: r5 = LoadInt32Instr(r3)
    //     0xa79798: sbfx            x5, x3, #1, #0x1f
    //     0xa7979c: tbz             w3, #0, #0xa797a4
    //     0xa797a0: ldur            x5, [x3, #7]
    // 0xa797a4: r1 = LoadInt32Instr(r0)
    //     0xa797a4: sbfx            x1, x0, #1, #0x1f
    // 0xa797a8: mov             x0, x1
    // 0xa797ac: mov             x1, x5
    // 0xa797b0: cmp             x1, x0
    // 0xa797b4: b.hs            #0xa79a24
    // 0xa797b8: LoadField: r0 = r4->field_f
    //     0xa797b8: ldur            w0, [x4, #0xf]
    // 0xa797bc: DecompressPointer r0
    //     0xa797bc: add             x0, x0, HEAP, lsl #32
    // 0xa797c0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa797c0: add             x16, x0, x5, lsl #2
    //     0xa797c4: ldur            w1, [x16, #0xf]
    // 0xa797c8: DecompressPointer r1
    //     0xa797c8: add             x1, x1, HEAP, lsl #32
    // 0xa797cc: LoadField: r0 = r1->field_7
    //     0xa797cc: ldur            w0, [x1, #7]
    // 0xa797d0: DecompressPointer r0
    //     0xa797d0: add             x0, x0, HEAP, lsl #32
    // 0xa797d4: str             x0, [SP]
    // 0xa797d8: r0 = _interpolateSingle()
    //     0xa797d8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa797dc: ldr             x1, [fp, #0x18]
    // 0xa797e0: stur            x0, [fp, #-0x10]
    // 0xa797e4: r0 = of()
    //     0xa797e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa797e8: LoadField: r1 = r0->field_87
    //     0xa797e8: ldur            w1, [x0, #0x87]
    // 0xa797ec: DecompressPointer r1
    //     0xa797ec: add             x1, x1, HEAP, lsl #32
    // 0xa797f0: LoadField: r0 = r1->field_2b
    //     0xa797f0: ldur            w0, [x1, #0x2b]
    // 0xa797f4: DecompressPointer r0
    //     0xa797f4: add             x0, x0, HEAP, lsl #32
    // 0xa797f8: r16 = 12.000000
    //     0xa797f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa797fc: ldr             x16, [x16, #0x9e8]
    // 0xa79800: r30 = Instance_Color
    //     0xa79800: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa79804: stp             lr, x16, [SP]
    // 0xa79808: mov             x1, x0
    // 0xa7980c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa7980c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa79810: ldr             x4, [x4, #0xaa0]
    // 0xa79814: r0 = copyWith()
    //     0xa79814: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa79818: stur            x0, [fp, #-0x18]
    // 0xa7981c: r0 = Text()
    //     0xa7981c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa79820: mov             x2, x0
    // 0xa79824: ldur            x0, [fp, #-0x10]
    // 0xa79828: stur            x2, [fp, #-0x20]
    // 0xa7982c: StoreField: r2->field_b = r0
    //     0xa7982c: stur            w0, [x2, #0xb]
    // 0xa79830: ldur            x0, [fp, #-0x18]
    // 0xa79834: StoreField: r2->field_13 = r0
    //     0xa79834: stur            w0, [x2, #0x13]
    // 0xa79838: ldur            x0, [fp, #-8]
    // 0xa7983c: LoadField: r1 = r0->field_f
    //     0xa7983c: ldur            w1, [x0, #0xf]
    // 0xa79840: DecompressPointer r1
    //     0xa79840: add             x1, x1, HEAP, lsl #32
    // 0xa79844: LoadField: r0 = r1->field_b
    //     0xa79844: ldur            w0, [x1, #0xb]
    // 0xa79848: DecompressPointer r0
    //     0xa79848: add             x0, x0, HEAP, lsl #32
    // 0xa7984c: cmp             w0, NULL
    // 0xa79850: b.eq            #0xa79a28
    // 0xa79854: LoadField: r1 = r0->field_b
    //     0xa79854: ldur            w1, [x0, #0xb]
    // 0xa79858: DecompressPointer r1
    //     0xa79858: add             x1, x1, HEAP, lsl #32
    // 0xa7985c: LoadField: r0 = r1->field_b
    //     0xa7985c: ldur            w0, [x1, #0xb]
    // 0xa79860: DecompressPointer r0
    //     0xa79860: add             x0, x0, HEAP, lsl #32
    // 0xa79864: cmp             w0, NULL
    // 0xa79868: b.ne            #0xa79874
    // 0xa7986c: r0 = Null
    //     0xa7986c: mov             x0, NULL
    // 0xa79870: b               #0xa798c8
    // 0xa79874: ldr             x1, [fp, #0x10]
    // 0xa79878: LoadField: r3 = r0->field_f
    //     0xa79878: ldur            w3, [x0, #0xf]
    // 0xa7987c: DecompressPointer r3
    //     0xa7987c: add             x3, x3, HEAP, lsl #32
    // 0xa79880: LoadField: r4 = r3->field_b
    //     0xa79880: ldur            w4, [x3, #0xb]
    // 0xa79884: DecompressPointer r4
    //     0xa79884: add             x4, x4, HEAP, lsl #32
    // 0xa79888: LoadField: r0 = r4->field_b
    //     0xa79888: ldur            w0, [x4, #0xb]
    // 0xa7988c: r3 = LoadInt32Instr(r1)
    //     0xa7988c: sbfx            x3, x1, #1, #0x1f
    //     0xa79890: tbz             w1, #0, #0xa79898
    //     0xa79894: ldur            x3, [x1, #7]
    // 0xa79898: r1 = LoadInt32Instr(r0)
    //     0xa79898: sbfx            x1, x0, #1, #0x1f
    // 0xa7989c: mov             x0, x1
    // 0xa798a0: mov             x1, x3
    // 0xa798a4: cmp             x1, x0
    // 0xa798a8: b.hs            #0xa79a2c
    // 0xa798ac: LoadField: r0 = r4->field_f
    //     0xa798ac: ldur            w0, [x4, #0xf]
    // 0xa798b0: DecompressPointer r0
    //     0xa798b0: add             x0, x0, HEAP, lsl #32
    // 0xa798b4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa798b4: add             x16, x0, x3, lsl #2
    //     0xa798b8: ldur            w1, [x16, #0xf]
    // 0xa798bc: DecompressPointer r1
    //     0xa798bc: add             x1, x1, HEAP, lsl #32
    // 0xa798c0: LoadField: r0 = r1->field_b
    //     0xa798c0: ldur            w0, [x1, #0xb]
    // 0xa798c4: DecompressPointer r0
    //     0xa798c4: add             x0, x0, HEAP, lsl #32
    // 0xa798c8: cmp             w0, NULL
    // 0xa798cc: b.ne            #0xa798d4
    // 0xa798d0: r0 = ""
    //     0xa798d0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa798d4: ldr             x1, [fp, #0x18]
    // 0xa798d8: stur            x0, [fp, #-8]
    // 0xa798dc: r0 = of()
    //     0xa798dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa798e0: LoadField: r1 = r0->field_87
    //     0xa798e0: ldur            w1, [x0, #0x87]
    // 0xa798e4: DecompressPointer r1
    //     0xa798e4: add             x1, x1, HEAP, lsl #32
    // 0xa798e8: LoadField: r0 = r1->field_2b
    //     0xa798e8: ldur            w0, [x1, #0x2b]
    // 0xa798ec: DecompressPointer r0
    //     0xa798ec: add             x0, x0, HEAP, lsl #32
    // 0xa798f0: r16 = 12.000000
    //     0xa798f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa798f4: ldr             x16, [x16, #0x9e8]
    // 0xa798f8: r30 = Instance_Color
    //     0xa798f8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa798fc: stp             lr, x16, [SP]
    // 0xa79900: mov             x1, x0
    // 0xa79904: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa79904: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa79908: ldr             x4, [x4, #0xaa0]
    // 0xa7990c: r0 = copyWith()
    //     0xa7990c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa79910: stur            x0, [fp, #-0x10]
    // 0xa79914: r0 = HtmlWidget()
    //     0xa79914: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xa79918: mov             x3, x0
    // 0xa7991c: ldur            x0, [fp, #-8]
    // 0xa79920: stur            x3, [fp, #-0x18]
    // 0xa79924: StoreField: r3->field_1f = r0
    //     0xa79924: stur            w0, [x3, #0x1f]
    // 0xa79928: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xa79928: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xa7992c: ldr             x0, [x0, #0x1e0]
    // 0xa79930: StoreField: r3->field_23 = r0
    //     0xa79930: stur            w0, [x3, #0x23]
    // 0xa79934: r0 = Instance_ColumnMode
    //     0xa79934: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xa79938: ldr             x0, [x0, #0x1e8]
    // 0xa7993c: StoreField: r3->field_3b = r0
    //     0xa7993c: stur            w0, [x3, #0x3b]
    // 0xa79940: ldur            x0, [fp, #-0x10]
    // 0xa79944: StoreField: r3->field_3f = r0
    //     0xa79944: stur            w0, [x3, #0x3f]
    // 0xa79948: r1 = Null
    //     0xa79948: mov             x1, NULL
    // 0xa7994c: r2 = 6
    //     0xa7994c: movz            x2, #0x6
    // 0xa79950: r0 = AllocateArray()
    //     0xa79950: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa79954: mov             x2, x0
    // 0xa79958: ldur            x0, [fp, #-0x20]
    // 0xa7995c: stur            x2, [fp, #-8]
    // 0xa79960: StoreField: r2->field_f = r0
    //     0xa79960: stur            w0, [x2, #0xf]
    // 0xa79964: r16 = Instance_SizedBox
    //     0xa79964: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xa79968: ldr             x16, [x16, #0xc70]
    // 0xa7996c: StoreField: r2->field_13 = r16
    //     0xa7996c: stur            w16, [x2, #0x13]
    // 0xa79970: ldur            x0, [fp, #-0x18]
    // 0xa79974: ArrayStore: r2[0] = r0  ; List_4
    //     0xa79974: stur            w0, [x2, #0x17]
    // 0xa79978: r1 = <Widget>
    //     0xa79978: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa7997c: r0 = AllocateGrowableArray()
    //     0xa7997c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa79980: mov             x1, x0
    // 0xa79984: ldur            x0, [fp, #-8]
    // 0xa79988: stur            x1, [fp, #-0x10]
    // 0xa7998c: StoreField: r1->field_f = r0
    //     0xa7998c: stur            w0, [x1, #0xf]
    // 0xa79990: r0 = 6
    //     0xa79990: movz            x0, #0x6
    // 0xa79994: StoreField: r1->field_b = r0
    //     0xa79994: stur            w0, [x1, #0xb]
    // 0xa79998: r0 = Column()
    //     0xa79998: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa7999c: mov             x1, x0
    // 0xa799a0: r0 = Instance_Axis
    //     0xa799a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa799a4: stur            x1, [fp, #-8]
    // 0xa799a8: StoreField: r1->field_f = r0
    //     0xa799a8: stur            w0, [x1, #0xf]
    // 0xa799ac: r0 = Instance_MainAxisAlignment
    //     0xa799ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa799b0: ldr             x0, [x0, #0xa08]
    // 0xa799b4: StoreField: r1->field_13 = r0
    //     0xa799b4: stur            w0, [x1, #0x13]
    // 0xa799b8: r0 = Instance_MainAxisSize
    //     0xa799b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa799bc: ldr             x0, [x0, #0xa10]
    // 0xa799c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa799c0: stur            w0, [x1, #0x17]
    // 0xa799c4: r0 = Instance_CrossAxisAlignment
    //     0xa799c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa799c8: ldr             x0, [x0, #0x890]
    // 0xa799cc: StoreField: r1->field_1b = r0
    //     0xa799cc: stur            w0, [x1, #0x1b]
    // 0xa799d0: r0 = Instance_VerticalDirection
    //     0xa799d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa799d4: ldr             x0, [x0, #0xa20]
    // 0xa799d8: StoreField: r1->field_23 = r0
    //     0xa799d8: stur            w0, [x1, #0x23]
    // 0xa799dc: r0 = Instance_Clip
    //     0xa799dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa799e0: ldr             x0, [x0, #0x38]
    // 0xa799e4: StoreField: r1->field_2b = r0
    //     0xa799e4: stur            w0, [x1, #0x2b]
    // 0xa799e8: StoreField: r1->field_2f = rZR
    //     0xa799e8: stur            xzr, [x1, #0x2f]
    // 0xa799ec: ldur            x0, [fp, #-0x10]
    // 0xa799f0: StoreField: r1->field_b = r0
    //     0xa799f0: stur            w0, [x1, #0xb]
    // 0xa799f4: r0 = Padding()
    //     0xa799f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa799f8: r1 = Instance_EdgeInsets
    //     0xa799f8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0xa799fc: ldr             x1, [x1, #0xf98]
    // 0xa79a00: StoreField: r0->field_f = r1
    //     0xa79a00: stur            w1, [x0, #0xf]
    // 0xa79a04: ldur            x1, [fp, #-8]
    // 0xa79a08: StoreField: r0->field_b = r1
    //     0xa79a08: stur            w1, [x0, #0xb]
    // 0xa79a0c: LeaveFrame
    //     0xa79a0c: mov             SP, fp
    //     0xa79a10: ldp             fp, lr, [SP], #0x10
    // 0xa79a14: ret
    //     0xa79a14: ret             
    // 0xa79a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa79a18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa79a1c: b               #0xa79744
    // 0xa79a20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa79a20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa79a24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa79a24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa79a28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa79a28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa79a2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa79a2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] ListTile <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa79a30, size: 0x200
    // 0xa79a30: EnterFrame
    //     0xa79a30: stp             fp, lr, [SP, #-0x10]!
    //     0xa79a34: mov             fp, SP
    // 0xa79a38: AllocStack(0x28)
    //     0xa79a38: sub             SP, SP, #0x28
    // 0xa79a3c: SetupParameters()
    //     0xa79a3c: ldr             x0, [fp, #0x20]
    //     0xa79a40: ldur            w1, [x0, #0x17]
    //     0xa79a44: add             x1, x1, HEAP, lsl #32
    // 0xa79a48: CheckStackOverflow
    //     0xa79a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa79a4c: cmp             SP, x16
    //     0xa79a50: b.ls            #0xa79c1c
    // 0xa79a54: LoadField: r0 = r1->field_f
    //     0xa79a54: ldur            w0, [x1, #0xf]
    // 0xa79a58: DecompressPointer r0
    //     0xa79a58: add             x0, x0, HEAP, lsl #32
    // 0xa79a5c: LoadField: r1 = r0->field_b
    //     0xa79a5c: ldur            w1, [x0, #0xb]
    // 0xa79a60: DecompressPointer r1
    //     0xa79a60: add             x1, x1, HEAP, lsl #32
    // 0xa79a64: cmp             w1, NULL
    // 0xa79a68: b.eq            #0xa79c24
    // 0xa79a6c: LoadField: r0 = r1->field_b
    //     0xa79a6c: ldur            w0, [x1, #0xb]
    // 0xa79a70: DecompressPointer r0
    //     0xa79a70: add             x0, x0, HEAP, lsl #32
    // 0xa79a74: LoadField: r2 = r0->field_b
    //     0xa79a74: ldur            w2, [x0, #0xb]
    // 0xa79a78: DecompressPointer r2
    //     0xa79a78: add             x2, x2, HEAP, lsl #32
    // 0xa79a7c: cmp             w2, NULL
    // 0xa79a80: b.ne            #0xa79a90
    // 0xa79a84: ldr             x3, [fp, #0x10]
    // 0xa79a88: r0 = Null
    //     0xa79a88: mov             x0, NULL
    // 0xa79a8c: b               #0xa79ae4
    // 0xa79a90: ldr             x3, [fp, #0x10]
    // 0xa79a94: LoadField: r0 = r2->field_b
    //     0xa79a94: ldur            w0, [x2, #0xb]
    // 0xa79a98: DecompressPointer r0
    //     0xa79a98: add             x0, x0, HEAP, lsl #32
    // 0xa79a9c: LoadField: r4 = r0->field_7
    //     0xa79a9c: ldur            w4, [x0, #7]
    // 0xa79aa0: DecompressPointer r4
    //     0xa79aa0: add             x4, x4, HEAP, lsl #32
    // 0xa79aa4: LoadField: r0 = r4->field_b
    //     0xa79aa4: ldur            w0, [x4, #0xb]
    // 0xa79aa8: r5 = LoadInt32Instr(r3)
    //     0xa79aa8: sbfx            x5, x3, #1, #0x1f
    //     0xa79aac: tbz             w3, #0, #0xa79ab4
    //     0xa79ab0: ldur            x5, [x3, #7]
    // 0xa79ab4: r1 = LoadInt32Instr(r0)
    //     0xa79ab4: sbfx            x1, x0, #1, #0x1f
    // 0xa79ab8: mov             x0, x1
    // 0xa79abc: mov             x1, x5
    // 0xa79ac0: cmp             x1, x0
    // 0xa79ac4: b.hs            #0xa79c28
    // 0xa79ac8: LoadField: r0 = r4->field_f
    //     0xa79ac8: ldur            w0, [x4, #0xf]
    // 0xa79acc: DecompressPointer r0
    //     0xa79acc: add             x0, x0, HEAP, lsl #32
    // 0xa79ad0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa79ad0: add             x16, x0, x5, lsl #2
    //     0xa79ad4: ldur            w1, [x16, #0xf]
    // 0xa79ad8: DecompressPointer r1
    //     0xa79ad8: add             x1, x1, HEAP, lsl #32
    // 0xa79adc: LoadField: r0 = r1->field_b
    //     0xa79adc: ldur            w0, [x1, #0xb]
    // 0xa79ae0: DecompressPointer r0
    //     0xa79ae0: add             x0, x0, HEAP, lsl #32
    // 0xa79ae4: cmp             w0, NULL
    // 0xa79ae8: b.eq            #0xa79afc
    // 0xa79aec: tbnz            w0, #4, #0xa79afc
    // 0xa79af0: r4 = Instance_Icon
    //     0xa79af0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52ce8] Obj!Icon@d664f1
    //     0xa79af4: ldr             x4, [x4, #0xce8]
    // 0xa79af8: b               #0xa79b04
    // 0xa79afc: r4 = Instance_Icon
    //     0xa79afc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52cf0] Obj!Icon@d664b1
    //     0xa79b00: ldr             x4, [x4, #0xcf0]
    // 0xa79b04: stur            x4, [fp, #-8]
    // 0xa79b08: cmp             w2, NULL
    // 0xa79b0c: b.ne            #0xa79b18
    // 0xa79b10: r0 = Null
    //     0xa79b10: mov             x0, NULL
    // 0xa79b14: b               #0xa79b68
    // 0xa79b18: LoadField: r0 = r2->field_b
    //     0xa79b18: ldur            w0, [x2, #0xb]
    // 0xa79b1c: DecompressPointer r0
    //     0xa79b1c: add             x0, x0, HEAP, lsl #32
    // 0xa79b20: LoadField: r2 = r0->field_7
    //     0xa79b20: ldur            w2, [x0, #7]
    // 0xa79b24: DecompressPointer r2
    //     0xa79b24: add             x2, x2, HEAP, lsl #32
    // 0xa79b28: LoadField: r0 = r2->field_b
    //     0xa79b28: ldur            w0, [x2, #0xb]
    // 0xa79b2c: r5 = LoadInt32Instr(r3)
    //     0xa79b2c: sbfx            x5, x3, #1, #0x1f
    //     0xa79b30: tbz             w3, #0, #0xa79b38
    //     0xa79b34: ldur            x5, [x3, #7]
    // 0xa79b38: r1 = LoadInt32Instr(r0)
    //     0xa79b38: sbfx            x1, x0, #1, #0x1f
    // 0xa79b3c: mov             x0, x1
    // 0xa79b40: mov             x1, x5
    // 0xa79b44: cmp             x1, x0
    // 0xa79b48: b.hs            #0xa79c2c
    // 0xa79b4c: LoadField: r0 = r2->field_f
    //     0xa79b4c: ldur            w0, [x2, #0xf]
    // 0xa79b50: DecompressPointer r0
    //     0xa79b50: add             x0, x0, HEAP, lsl #32
    // 0xa79b54: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa79b54: add             x16, x0, x5, lsl #2
    //     0xa79b58: ldur            w1, [x16, #0xf]
    // 0xa79b5c: DecompressPointer r1
    //     0xa79b5c: add             x1, x1, HEAP, lsl #32
    // 0xa79b60: LoadField: r0 = r1->field_7
    //     0xa79b60: ldur            w0, [x1, #7]
    // 0xa79b64: DecompressPointer r0
    //     0xa79b64: add             x0, x0, HEAP, lsl #32
    // 0xa79b68: str             x0, [SP]
    // 0xa79b6c: r0 = _interpolateSingle()
    //     0xa79b6c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa79b70: ldr             x1, [fp, #0x18]
    // 0xa79b74: stur            x0, [fp, #-0x10]
    // 0xa79b78: r0 = of()
    //     0xa79b78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa79b7c: LoadField: r1 = r0->field_87
    //     0xa79b7c: ldur            w1, [x0, #0x87]
    // 0xa79b80: DecompressPointer r1
    //     0xa79b80: add             x1, x1, HEAP, lsl #32
    // 0xa79b84: LoadField: r0 = r1->field_2b
    //     0xa79b84: ldur            w0, [x1, #0x2b]
    // 0xa79b88: DecompressPointer r0
    //     0xa79b88: add             x0, x0, HEAP, lsl #32
    // 0xa79b8c: r16 = 12.000000
    //     0xa79b8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa79b90: ldr             x16, [x16, #0x9e8]
    // 0xa79b94: str             x16, [SP]
    // 0xa79b98: mov             x1, x0
    // 0xa79b9c: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xa79b9c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xa79ba0: ldr             x4, [x4, #0x798]
    // 0xa79ba4: r0 = copyWith()
    //     0xa79ba4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa79ba8: stur            x0, [fp, #-0x18]
    // 0xa79bac: r0 = Text()
    //     0xa79bac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa79bb0: mov             x1, x0
    // 0xa79bb4: ldur            x0, [fp, #-0x10]
    // 0xa79bb8: stur            x1, [fp, #-0x20]
    // 0xa79bbc: StoreField: r1->field_b = r0
    //     0xa79bbc: stur            w0, [x1, #0xb]
    // 0xa79bc0: ldur            x0, [fp, #-0x18]
    // 0xa79bc4: StoreField: r1->field_13 = r0
    //     0xa79bc4: stur            w0, [x1, #0x13]
    // 0xa79bc8: r0 = ListTile()
    //     0xa79bc8: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xa79bcc: ldur            x1, [fp, #-8]
    // 0xa79bd0: StoreField: r0->field_b = r1
    //     0xa79bd0: stur            w1, [x0, #0xb]
    // 0xa79bd4: ldur            x1, [fp, #-0x20]
    // 0xa79bd8: StoreField: r0->field_f = r1
    //     0xa79bd8: stur            w1, [x0, #0xf]
    // 0xa79bdc: r1 = true
    //     0xa79bdc: add             x1, NULL, #0x20  ; true
    // 0xa79be0: StoreField: r0->field_1f = r1
    //     0xa79be0: stur            w1, [x0, #0x1f]
    // 0xa79be4: StoreField: r0->field_4b = r1
    //     0xa79be4: stur            w1, [x0, #0x4b]
    // 0xa79be8: r2 = false
    //     0xa79be8: add             x2, NULL, #0x30  ; false
    // 0xa79bec: StoreField: r0->field_5f = r2
    //     0xa79bec: stur            w2, [x0, #0x5f]
    // 0xa79bf0: StoreField: r0->field_73 = r2
    //     0xa79bf0: stur            w2, [x0, #0x73]
    // 0xa79bf4: r2 = 4.000000
    //     0xa79bf4: add             x2, PP, #0x27, lsl #12  ; [pp+0x27838] 4
    //     0xa79bf8: ldr             x2, [x2, #0x838]
    // 0xa79bfc: StoreField: r0->field_87 = r2
    //     0xa79bfc: stur            w2, [x0, #0x87]
    // 0xa79c00: r2 = 8.000000
    //     0xa79c00: add             x2, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xa79c04: ldr             x2, [x2, #0x608]
    // 0xa79c08: StoreField: r0->field_8b = r2
    //     0xa79c08: stur            w2, [x0, #0x8b]
    // 0xa79c0c: StoreField: r0->field_97 = r1
    //     0xa79c0c: stur            w1, [x0, #0x97]
    // 0xa79c10: LeaveFrame
    //     0xa79c10: mov             SP, fp
    //     0xa79c14: ldp             fp, lr, [SP], #0x10
    // 0xa79c18: ret
    //     0xa79c18: ret             
    // 0xa79c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa79c1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa79c20: b               #0xa79a54
    // 0xa79c24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa79c24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa79c28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa79c28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa79c2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa79c2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbff404, size: 0xf08
    // 0xbff404: EnterFrame
    //     0xbff404: stp             fp, lr, [SP, #-0x10]!
    //     0xbff408: mov             fp, SP
    // 0xbff40c: AllocStack(0x70)
    //     0xbff40c: sub             SP, SP, #0x70
    // 0xbff410: SetupParameters(_KnowMoreBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbff410: mov             x0, x1
    //     0xbff414: stur            x1, [fp, #-8]
    //     0xbff418: mov             x1, x2
    //     0xbff41c: stur            x2, [fp, #-0x10]
    // 0xbff420: CheckStackOverflow
    //     0xbff420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbff424: cmp             SP, x16
    //     0xbff428: b.ls            #0xc002dc
    // 0xbff42c: r1 = 1
    //     0xbff42c: movz            x1, #0x1
    // 0xbff430: r0 = AllocateContext()
    //     0xbff430: bl              #0x16f6108  ; AllocateContextStub
    // 0xbff434: mov             x2, x0
    // 0xbff438: ldur            x0, [fp, #-8]
    // 0xbff43c: stur            x2, [fp, #-0x18]
    // 0xbff440: StoreField: r2->field_f = r0
    //     0xbff440: stur            w0, [x2, #0xf]
    // 0xbff444: ldur            x1, [fp, #-0x10]
    // 0xbff448: r0 = of()
    //     0xbff448: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbff44c: LoadField: r1 = r0->field_5b
    //     0xbff44c: ldur            w1, [x0, #0x5b]
    // 0xbff450: DecompressPointer r1
    //     0xbff450: add             x1, x1, HEAP, lsl #32
    // 0xbff454: stur            x1, [fp, #-0x20]
    // 0xbff458: r0 = BoxDecoration()
    //     0xbff458: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbff45c: mov             x1, x0
    // 0xbff460: ldur            x0, [fp, #-0x20]
    // 0xbff464: stur            x1, [fp, #-0x28]
    // 0xbff468: StoreField: r1->field_7 = r0
    //     0xbff468: stur            w0, [x1, #7]
    // 0xbff46c: r0 = Instance_BoxShape
    //     0xbff46c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbff470: ldr             x0, [x0, #0x970]
    // 0xbff474: StoreField: r1->field_23 = r0
    //     0xbff474: stur            w0, [x1, #0x23]
    // 0xbff478: r0 = SvgPicture()
    //     0xbff478: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff47c: stur            x0, [fp, #-0x20]
    // 0xbff480: r16 = Instance_BoxFit
    //     0xbff480: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbff484: ldr             x16, [x16, #0xb18]
    // 0xbff488: str             x16, [SP]
    // 0xbff48c: mov             x1, x0
    // 0xbff490: r2 = "assets/images/return_order.svg"
    //     0xbff490: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c80] "assets/images/return_order.svg"
    //     0xbff494: ldr             x2, [x2, #0xc80]
    // 0xbff498: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbff498: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbff49c: ldr             x4, [x4, #0xb0]
    // 0xbff4a0: r0 = SvgPicture.asset()
    //     0xbff4a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff4a4: r0 = Container()
    //     0xbff4a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbff4a8: stur            x0, [fp, #-0x30]
    // 0xbff4ac: r16 = 45.000000
    //     0xbff4ac: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbff4b0: ldr             x16, [x16, #0xc88]
    // 0xbff4b4: r30 = 45.000000
    //     0xbff4b4: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbff4b8: ldr             lr, [lr, #0xc88]
    // 0xbff4bc: stp             lr, x16, [SP, #0x10]
    // 0xbff4c0: ldur            x16, [fp, #-0x28]
    // 0xbff4c4: ldur            lr, [fp, #-0x20]
    // 0xbff4c8: stp             lr, x16, [SP]
    // 0xbff4cc: mov             x1, x0
    // 0xbff4d0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbff4d0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbff4d4: ldr             x4, [x4, #0x870]
    // 0xbff4d8: r0 = Container()
    //     0xbff4d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbff4dc: ldur            x0, [fp, #-8]
    // 0xbff4e0: LoadField: r1 = r0->field_b
    //     0xbff4e0: ldur            w1, [x0, #0xb]
    // 0xbff4e4: DecompressPointer r1
    //     0xbff4e4: add             x1, x1, HEAP, lsl #32
    // 0xbff4e8: cmp             w1, NULL
    // 0xbff4ec: b.eq            #0xc002e4
    // 0xbff4f0: LoadField: r2 = r1->field_b
    //     0xbff4f0: ldur            w2, [x1, #0xb]
    // 0xbff4f4: DecompressPointer r2
    //     0xbff4f4: add             x2, x2, HEAP, lsl #32
    // 0xbff4f8: LoadField: r1 = r2->field_b
    //     0xbff4f8: ldur            w1, [x2, #0xb]
    // 0xbff4fc: DecompressPointer r1
    //     0xbff4fc: add             x1, x1, HEAP, lsl #32
    // 0xbff500: cmp             w1, NULL
    // 0xbff504: b.ne            #0xbff510
    // 0xbff508: r1 = Null
    //     0xbff508: mov             x1, NULL
    // 0xbff50c: b               #0xbff52c
    // 0xbff510: LoadField: r2 = r1->field_7
    //     0xbff510: ldur            w2, [x1, #7]
    // 0xbff514: DecompressPointer r2
    //     0xbff514: add             x2, x2, HEAP, lsl #32
    // 0xbff518: LoadField: r1 = r2->field_7
    //     0xbff518: ldur            w1, [x2, #7]
    // 0xbff51c: DecompressPointer r1
    //     0xbff51c: add             x1, x1, HEAP, lsl #32
    // 0xbff520: LoadField: r2 = r1->field_7
    //     0xbff520: ldur            w2, [x1, #7]
    // 0xbff524: DecompressPointer r2
    //     0xbff524: add             x2, x2, HEAP, lsl #32
    // 0xbff528: mov             x1, x2
    // 0xbff52c: cmp             w1, NULL
    // 0xbff530: b.ne            #0xbff538
    // 0xbff534: r1 = false
    //     0xbff534: add             x1, NULL, #0x30  ; false
    // 0xbff538: stur            x1, [fp, #-0x20]
    // 0xbff53c: r0 = SvgPicture()
    //     0xbff53c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff540: stur            x0, [fp, #-0x28]
    // 0xbff544: r16 = Instance_BoxFit
    //     0xbff544: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbff548: ldr             x16, [x16, #0xb18]
    // 0xbff54c: str             x16, [SP]
    // 0xbff550: mov             x1, x0
    // 0xbff554: r2 = "assets/images/replacement_check.svg"
    //     0xbff554: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xbff558: ldr             x2, [x2, #0xc90]
    // 0xbff55c: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbff55c: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbff560: ldr             x4, [x4, #0xb0]
    // 0xbff564: r0 = SvgPicture.asset()
    //     0xbff564: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff568: r0 = Visibility()
    //     0xbff568: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbff56c: mov             x1, x0
    // 0xbff570: ldur            x0, [fp, #-0x28]
    // 0xbff574: stur            x1, [fp, #-0x38]
    // 0xbff578: StoreField: r1->field_b = r0
    //     0xbff578: stur            w0, [x1, #0xb]
    // 0xbff57c: r0 = Instance_SizedBox
    //     0xbff57c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbff580: StoreField: r1->field_f = r0
    //     0xbff580: stur            w0, [x1, #0xf]
    // 0xbff584: ldur            x2, [fp, #-0x20]
    // 0xbff588: StoreField: r1->field_13 = r2
    //     0xbff588: stur            w2, [x1, #0x13]
    // 0xbff58c: r2 = false
    //     0xbff58c: add             x2, NULL, #0x30  ; false
    // 0xbff590: ArrayStore: r1[0] = r2  ; List_4
    //     0xbff590: stur            w2, [x1, #0x17]
    // 0xbff594: StoreField: r1->field_1b = r2
    //     0xbff594: stur            w2, [x1, #0x1b]
    // 0xbff598: StoreField: r1->field_1f = r2
    //     0xbff598: stur            w2, [x1, #0x1f]
    // 0xbff59c: StoreField: r1->field_23 = r2
    //     0xbff59c: stur            w2, [x1, #0x23]
    // 0xbff5a0: StoreField: r1->field_27 = r2
    //     0xbff5a0: stur            w2, [x1, #0x27]
    // 0xbff5a4: StoreField: r1->field_2b = r2
    //     0xbff5a4: stur            w2, [x1, #0x2b]
    // 0xbff5a8: ldur            x3, [fp, #-8]
    // 0xbff5ac: LoadField: r4 = r3->field_b
    //     0xbff5ac: ldur            w4, [x3, #0xb]
    // 0xbff5b0: DecompressPointer r4
    //     0xbff5b0: add             x4, x4, HEAP, lsl #32
    // 0xbff5b4: cmp             w4, NULL
    // 0xbff5b8: b.eq            #0xc002e8
    // 0xbff5bc: LoadField: r5 = r4->field_b
    //     0xbff5bc: ldur            w5, [x4, #0xb]
    // 0xbff5c0: DecompressPointer r5
    //     0xbff5c0: add             x5, x5, HEAP, lsl #32
    // 0xbff5c4: LoadField: r4 = r5->field_b
    //     0xbff5c4: ldur            w4, [x5, #0xb]
    // 0xbff5c8: DecompressPointer r4
    //     0xbff5c8: add             x4, x4, HEAP, lsl #32
    // 0xbff5cc: cmp             w4, NULL
    // 0xbff5d0: b.ne            #0xbff5dc
    // 0xbff5d4: r4 = Null
    //     0xbff5d4: mov             x4, NULL
    // 0xbff5d8: b               #0xbff5f8
    // 0xbff5dc: LoadField: r5 = r4->field_7
    //     0xbff5dc: ldur            w5, [x4, #7]
    // 0xbff5e0: DecompressPointer r5
    //     0xbff5e0: add             x5, x5, HEAP, lsl #32
    // 0xbff5e4: LoadField: r4 = r5->field_7
    //     0xbff5e4: ldur            w4, [x5, #7]
    // 0xbff5e8: DecompressPointer r4
    //     0xbff5e8: add             x4, x4, HEAP, lsl #32
    // 0xbff5ec: LoadField: r5 = r4->field_7
    //     0xbff5ec: ldur            w5, [x4, #7]
    // 0xbff5f0: DecompressPointer r5
    //     0xbff5f0: add             x5, x5, HEAP, lsl #32
    // 0xbff5f4: mov             x4, x5
    // 0xbff5f8: cmp             w4, NULL
    // 0xbff5fc: b.ne            #0xbff608
    // 0xbff600: r5 = false
    //     0xbff600: add             x5, NULL, #0x30  ; false
    // 0xbff604: b               #0xbff60c
    // 0xbff608: mov             x5, x4
    // 0xbff60c: ldur            x4, [fp, #-0x30]
    // 0xbff610: eor             x6, x5, #0x10
    // 0xbff614: stur            x6, [fp, #-0x20]
    // 0xbff618: r0 = SvgPicture()
    //     0xbff618: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff61c: stur            x0, [fp, #-0x28]
    // 0xbff620: r16 = Instance_BoxFit
    //     0xbff620: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbff624: ldr             x16, [x16, #0xb18]
    // 0xbff628: str             x16, [SP]
    // 0xbff62c: mov             x1, x0
    // 0xbff630: r2 = "assets/images/exchange_check.svg"
    //     0xbff630: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xbff634: ldr             x2, [x2, #0xc98]
    // 0xbff638: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbff638: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbff63c: ldr             x4, [x4, #0xb0]
    // 0xbff640: r0 = SvgPicture.asset()
    //     0xbff640: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff644: r0 = Visibility()
    //     0xbff644: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbff648: mov             x3, x0
    // 0xbff64c: ldur            x0, [fp, #-0x28]
    // 0xbff650: stur            x3, [fp, #-0x40]
    // 0xbff654: StoreField: r3->field_b = r0
    //     0xbff654: stur            w0, [x3, #0xb]
    // 0xbff658: r0 = Instance_SizedBox
    //     0xbff658: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbff65c: StoreField: r3->field_f = r0
    //     0xbff65c: stur            w0, [x3, #0xf]
    // 0xbff660: ldur            x1, [fp, #-0x20]
    // 0xbff664: StoreField: r3->field_13 = r1
    //     0xbff664: stur            w1, [x3, #0x13]
    // 0xbff668: r4 = false
    //     0xbff668: add             x4, NULL, #0x30  ; false
    // 0xbff66c: ArrayStore: r3[0] = r4  ; List_4
    //     0xbff66c: stur            w4, [x3, #0x17]
    // 0xbff670: StoreField: r3->field_1b = r4
    //     0xbff670: stur            w4, [x3, #0x1b]
    // 0xbff674: StoreField: r3->field_1f = r4
    //     0xbff674: stur            w4, [x3, #0x1f]
    // 0xbff678: StoreField: r3->field_23 = r4
    //     0xbff678: stur            w4, [x3, #0x23]
    // 0xbff67c: StoreField: r3->field_27 = r4
    //     0xbff67c: stur            w4, [x3, #0x27]
    // 0xbff680: StoreField: r3->field_2b = r4
    //     0xbff680: stur            w4, [x3, #0x2b]
    // 0xbff684: r1 = Null
    //     0xbff684: mov             x1, NULL
    // 0xbff688: r2 = 6
    //     0xbff688: movz            x2, #0x6
    // 0xbff68c: r0 = AllocateArray()
    //     0xbff68c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbff690: mov             x2, x0
    // 0xbff694: ldur            x0, [fp, #-0x30]
    // 0xbff698: stur            x2, [fp, #-0x20]
    // 0xbff69c: StoreField: r2->field_f = r0
    //     0xbff69c: stur            w0, [x2, #0xf]
    // 0xbff6a0: ldur            x0, [fp, #-0x38]
    // 0xbff6a4: StoreField: r2->field_13 = r0
    //     0xbff6a4: stur            w0, [x2, #0x13]
    // 0xbff6a8: ldur            x0, [fp, #-0x40]
    // 0xbff6ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xbff6ac: stur            w0, [x2, #0x17]
    // 0xbff6b0: r1 = <Widget>
    //     0xbff6b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbff6b4: r0 = AllocateGrowableArray()
    //     0xbff6b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbff6b8: mov             x1, x0
    // 0xbff6bc: ldur            x0, [fp, #-0x20]
    // 0xbff6c0: stur            x1, [fp, #-0x28]
    // 0xbff6c4: StoreField: r1->field_f = r0
    //     0xbff6c4: stur            w0, [x1, #0xf]
    // 0xbff6c8: r2 = 6
    //     0xbff6c8: movz            x2, #0x6
    // 0xbff6cc: StoreField: r1->field_b = r2
    //     0xbff6cc: stur            w2, [x1, #0xb]
    // 0xbff6d0: r0 = Stack()
    //     0xbff6d0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbff6d4: mov             x1, x0
    // 0xbff6d8: r0 = Instance_Alignment
    //     0xbff6d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbff6dc: ldr             x0, [x0, #0x950]
    // 0xbff6e0: stur            x1, [fp, #-0x20]
    // 0xbff6e4: StoreField: r1->field_f = r0
    //     0xbff6e4: stur            w0, [x1, #0xf]
    // 0xbff6e8: r2 = Instance_StackFit
    //     0xbff6e8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbff6ec: ldr             x2, [x2, #0xfa8]
    // 0xbff6f0: ArrayStore: r1[0] = r2  ; List_4
    //     0xbff6f0: stur            w2, [x1, #0x17]
    // 0xbff6f4: r3 = Instance_Clip
    //     0xbff6f4: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbff6f8: ldr             x3, [x3, #0x7e0]
    // 0xbff6fc: StoreField: r1->field_1b = r3
    //     0xbff6fc: stur            w3, [x1, #0x1b]
    // 0xbff700: ldur            x4, [fp, #-0x28]
    // 0xbff704: StoreField: r1->field_b = r4
    //     0xbff704: stur            w4, [x1, #0xb]
    // 0xbff708: ldur            x4, [fp, #-8]
    // 0xbff70c: LoadField: r5 = r4->field_b
    //     0xbff70c: ldur            w5, [x4, #0xb]
    // 0xbff710: DecompressPointer r5
    //     0xbff710: add             x5, x5, HEAP, lsl #32
    // 0xbff714: cmp             w5, NULL
    // 0xbff718: b.eq            #0xc002ec
    // 0xbff71c: LoadField: r6 = r5->field_b
    //     0xbff71c: ldur            w6, [x5, #0xb]
    // 0xbff720: DecompressPointer r6
    //     0xbff720: add             x6, x6, HEAP, lsl #32
    // 0xbff724: LoadField: r5 = r6->field_b
    //     0xbff724: ldur            w5, [x6, #0xb]
    // 0xbff728: DecompressPointer r5
    //     0xbff728: add             x5, x5, HEAP, lsl #32
    // 0xbff72c: cmp             w5, NULL
    // 0xbff730: b.ne            #0xbff73c
    // 0xbff734: r5 = Null
    //     0xbff734: mov             x5, NULL
    // 0xbff738: b               #0xbff758
    // 0xbff73c: LoadField: r6 = r5->field_7
    //     0xbff73c: ldur            w6, [x5, #7]
    // 0xbff740: DecompressPointer r6
    //     0xbff740: add             x6, x6, HEAP, lsl #32
    // 0xbff744: LoadField: r5 = r6->field_7
    //     0xbff744: ldur            w5, [x6, #7]
    // 0xbff748: DecompressPointer r5
    //     0xbff748: add             x5, x5, HEAP, lsl #32
    // 0xbff74c: LoadField: r6 = r5->field_b
    //     0xbff74c: ldur            w6, [x5, #0xb]
    // 0xbff750: DecompressPointer r6
    //     0xbff750: add             x6, x6, HEAP, lsl #32
    // 0xbff754: mov             x5, x6
    // 0xbff758: str             x5, [SP]
    // 0xbff75c: r0 = _interpolateSingle()
    //     0xbff75c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbff760: ldur            x1, [fp, #-0x10]
    // 0xbff764: stur            x0, [fp, #-0x28]
    // 0xbff768: r0 = of()
    //     0xbff768: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbff76c: LoadField: r1 = r0->field_87
    //     0xbff76c: ldur            w1, [x0, #0x87]
    // 0xbff770: DecompressPointer r1
    //     0xbff770: add             x1, x1, HEAP, lsl #32
    // 0xbff774: LoadField: r0 = r1->field_7
    //     0xbff774: ldur            w0, [x1, #7]
    // 0xbff778: DecompressPointer r0
    //     0xbff778: add             x0, x0, HEAP, lsl #32
    // 0xbff77c: r16 = Instance_Color
    //     0xbff77c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbff780: r30 = 14.000000
    //     0xbff780: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbff784: ldr             lr, [lr, #0x1d8]
    // 0xbff788: stp             lr, x16, [SP]
    // 0xbff78c: mov             x1, x0
    // 0xbff790: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbff790: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbff794: ldr             x4, [x4, #0x9b8]
    // 0xbff798: r0 = copyWith()
    //     0xbff798: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbff79c: stur            x0, [fp, #-0x30]
    // 0xbff7a0: r0 = Text()
    //     0xbff7a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbff7a4: mov             x3, x0
    // 0xbff7a8: ldur            x0, [fp, #-0x28]
    // 0xbff7ac: stur            x3, [fp, #-0x38]
    // 0xbff7b0: StoreField: r3->field_b = r0
    //     0xbff7b0: stur            w0, [x3, #0xb]
    // 0xbff7b4: ldur            x0, [fp, #-0x30]
    // 0xbff7b8: StoreField: r3->field_13 = r0
    //     0xbff7b8: stur            w0, [x3, #0x13]
    // 0xbff7bc: r1 = Null
    //     0xbff7bc: mov             x1, NULL
    // 0xbff7c0: r2 = 4
    //     0xbff7c0: movz            x2, #0x4
    // 0xbff7c4: r0 = AllocateArray()
    //     0xbff7c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbff7c8: mov             x2, x0
    // 0xbff7cc: ldur            x0, [fp, #-0x20]
    // 0xbff7d0: stur            x2, [fp, #-0x28]
    // 0xbff7d4: StoreField: r2->field_f = r0
    //     0xbff7d4: stur            w0, [x2, #0xf]
    // 0xbff7d8: ldur            x0, [fp, #-0x38]
    // 0xbff7dc: StoreField: r2->field_13 = r0
    //     0xbff7dc: stur            w0, [x2, #0x13]
    // 0xbff7e0: r1 = <Widget>
    //     0xbff7e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbff7e4: r0 = AllocateGrowableArray()
    //     0xbff7e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbff7e8: mov             x1, x0
    // 0xbff7ec: ldur            x0, [fp, #-0x28]
    // 0xbff7f0: stur            x1, [fp, #-0x20]
    // 0xbff7f4: StoreField: r1->field_f = r0
    //     0xbff7f4: stur            w0, [x1, #0xf]
    // 0xbff7f8: r2 = 4
    //     0xbff7f8: movz            x2, #0x4
    // 0xbff7fc: StoreField: r1->field_b = r2
    //     0xbff7fc: stur            w2, [x1, #0xb]
    // 0xbff800: r0 = Column()
    //     0xbff800: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbff804: mov             x2, x0
    // 0xbff808: r0 = Instance_Axis
    //     0xbff808: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbff80c: stur            x2, [fp, #-0x28]
    // 0xbff810: StoreField: r2->field_f = r0
    //     0xbff810: stur            w0, [x2, #0xf]
    // 0xbff814: r3 = Instance_MainAxisAlignment
    //     0xbff814: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbff818: ldr             x3, [x3, #0xa08]
    // 0xbff81c: StoreField: r2->field_13 = r3
    //     0xbff81c: stur            w3, [x2, #0x13]
    // 0xbff820: r4 = Instance_MainAxisSize
    //     0xbff820: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbff824: ldr             x4, [x4, #0xa10]
    // 0xbff828: ArrayStore: r2[0] = r4  ; List_4
    //     0xbff828: stur            w4, [x2, #0x17]
    // 0xbff82c: r5 = Instance_CrossAxisAlignment
    //     0xbff82c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbff830: ldr             x5, [x5, #0xa18]
    // 0xbff834: StoreField: r2->field_1b = r5
    //     0xbff834: stur            w5, [x2, #0x1b]
    // 0xbff838: r6 = Instance_VerticalDirection
    //     0xbff838: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbff83c: ldr             x6, [x6, #0xa20]
    // 0xbff840: StoreField: r2->field_23 = r6
    //     0xbff840: stur            w6, [x2, #0x23]
    // 0xbff844: r7 = Instance_Clip
    //     0xbff844: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbff848: ldr             x7, [x7, #0x38]
    // 0xbff84c: StoreField: r2->field_2b = r7
    //     0xbff84c: stur            w7, [x2, #0x2b]
    // 0xbff850: StoreField: r2->field_2f = rZR
    //     0xbff850: stur            xzr, [x2, #0x2f]
    // 0xbff854: ldur            x1, [fp, #-0x20]
    // 0xbff858: StoreField: r2->field_b = r1
    //     0xbff858: stur            w1, [x2, #0xb]
    // 0xbff85c: ldur            x1, [fp, #-0x10]
    // 0xbff860: r0 = of()
    //     0xbff860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbff864: LoadField: r1 = r0->field_5b
    //     0xbff864: ldur            w1, [x0, #0x5b]
    // 0xbff868: DecompressPointer r1
    //     0xbff868: add             x1, x1, HEAP, lsl #32
    // 0xbff86c: stur            x1, [fp, #-0x20]
    // 0xbff870: r0 = BoxDecoration()
    //     0xbff870: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbff874: mov             x1, x0
    // 0xbff878: ldur            x0, [fp, #-0x20]
    // 0xbff87c: stur            x1, [fp, #-0x30]
    // 0xbff880: StoreField: r1->field_7 = r0
    //     0xbff880: stur            w0, [x1, #7]
    // 0xbff884: r0 = Instance_BoxShape
    //     0xbff884: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xbff888: ldr             x0, [x0, #0x970]
    // 0xbff88c: StoreField: r1->field_23 = r0
    //     0xbff88c: stur            w0, [x1, #0x23]
    // 0xbff890: r0 = SvgPicture()
    //     0xbff890: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff894: stur            x0, [fp, #-0x20]
    // 0xbff898: r16 = Instance_BoxFit
    //     0xbff898: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbff89c: ldr             x16, [x16, #0xb18]
    // 0xbff8a0: str             x16, [SP]
    // 0xbff8a4: mov             x1, x0
    // 0xbff8a8: r2 = "assets/images/exchange.svg"
    //     0xbff8a8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xbff8ac: ldr             x2, [x2, #0xca0]
    // 0xbff8b0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbff8b0: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbff8b4: ldr             x4, [x4, #0xb0]
    // 0xbff8b8: r0 = SvgPicture.asset()
    //     0xbff8b8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff8bc: r0 = Container()
    //     0xbff8bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbff8c0: stur            x0, [fp, #-0x38]
    // 0xbff8c4: r16 = 45.000000
    //     0xbff8c4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbff8c8: ldr             x16, [x16, #0xc88]
    // 0xbff8cc: r30 = 45.000000
    //     0xbff8cc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbff8d0: ldr             lr, [lr, #0xc88]
    // 0xbff8d4: stp             lr, x16, [SP, #0x10]
    // 0xbff8d8: ldur            x16, [fp, #-0x30]
    // 0xbff8dc: ldur            lr, [fp, #-0x20]
    // 0xbff8e0: stp             lr, x16, [SP]
    // 0xbff8e4: mov             x1, x0
    // 0xbff8e8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbff8e8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbff8ec: ldr             x4, [x4, #0x870]
    // 0xbff8f0: r0 = Container()
    //     0xbff8f0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbff8f4: ldur            x0, [fp, #-8]
    // 0xbff8f8: LoadField: r1 = r0->field_b
    //     0xbff8f8: ldur            w1, [x0, #0xb]
    // 0xbff8fc: DecompressPointer r1
    //     0xbff8fc: add             x1, x1, HEAP, lsl #32
    // 0xbff900: cmp             w1, NULL
    // 0xbff904: b.eq            #0xc002f0
    // 0xbff908: LoadField: r2 = r1->field_b
    //     0xbff908: ldur            w2, [x1, #0xb]
    // 0xbff90c: DecompressPointer r2
    //     0xbff90c: add             x2, x2, HEAP, lsl #32
    // 0xbff910: LoadField: r1 = r2->field_b
    //     0xbff910: ldur            w1, [x2, #0xb]
    // 0xbff914: DecompressPointer r1
    //     0xbff914: add             x1, x1, HEAP, lsl #32
    // 0xbff918: cmp             w1, NULL
    // 0xbff91c: b.ne            #0xbff928
    // 0xbff920: r1 = Null
    //     0xbff920: mov             x1, NULL
    // 0xbff924: b               #0xbff944
    // 0xbff928: LoadField: r2 = r1->field_7
    //     0xbff928: ldur            w2, [x1, #7]
    // 0xbff92c: DecompressPointer r2
    //     0xbff92c: add             x2, x2, HEAP, lsl #32
    // 0xbff930: LoadField: r1 = r2->field_7
    //     0xbff930: ldur            w1, [x2, #7]
    // 0xbff934: DecompressPointer r1
    //     0xbff934: add             x1, x1, HEAP, lsl #32
    // 0xbff938: LoadField: r2 = r1->field_7
    //     0xbff938: ldur            w2, [x1, #7]
    // 0xbff93c: DecompressPointer r2
    //     0xbff93c: add             x2, x2, HEAP, lsl #32
    // 0xbff940: mov             x1, x2
    // 0xbff944: cmp             w1, NULL
    // 0xbff948: b.ne            #0xbff950
    // 0xbff94c: r1 = false
    //     0xbff94c: add             x1, NULL, #0x30  ; false
    // 0xbff950: stur            x1, [fp, #-0x20]
    // 0xbff954: r0 = SvgPicture()
    //     0xbff954: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbff958: stur            x0, [fp, #-0x30]
    // 0xbff95c: r16 = Instance_BoxFit
    //     0xbff95c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbff960: ldr             x16, [x16, #0xb18]
    // 0xbff964: str             x16, [SP]
    // 0xbff968: mov             x1, x0
    // 0xbff96c: r2 = "assets/images/exchange_check.svg"
    //     0xbff96c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xbff970: ldr             x2, [x2, #0xc98]
    // 0xbff974: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbff974: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbff978: ldr             x4, [x4, #0xb0]
    // 0xbff97c: r0 = SvgPicture.asset()
    //     0xbff97c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbff980: r0 = Visibility()
    //     0xbff980: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbff984: mov             x1, x0
    // 0xbff988: ldur            x0, [fp, #-0x30]
    // 0xbff98c: stur            x1, [fp, #-0x40]
    // 0xbff990: StoreField: r1->field_b = r0
    //     0xbff990: stur            w0, [x1, #0xb]
    // 0xbff994: r0 = Instance_SizedBox
    //     0xbff994: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbff998: StoreField: r1->field_f = r0
    //     0xbff998: stur            w0, [x1, #0xf]
    // 0xbff99c: ldur            x2, [fp, #-0x20]
    // 0xbff9a0: StoreField: r1->field_13 = r2
    //     0xbff9a0: stur            w2, [x1, #0x13]
    // 0xbff9a4: r2 = false
    //     0xbff9a4: add             x2, NULL, #0x30  ; false
    // 0xbff9a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbff9a8: stur            w2, [x1, #0x17]
    // 0xbff9ac: StoreField: r1->field_1b = r2
    //     0xbff9ac: stur            w2, [x1, #0x1b]
    // 0xbff9b0: StoreField: r1->field_1f = r2
    //     0xbff9b0: stur            w2, [x1, #0x1f]
    // 0xbff9b4: StoreField: r1->field_23 = r2
    //     0xbff9b4: stur            w2, [x1, #0x23]
    // 0xbff9b8: StoreField: r1->field_27 = r2
    //     0xbff9b8: stur            w2, [x1, #0x27]
    // 0xbff9bc: StoreField: r1->field_2b = r2
    //     0xbff9bc: stur            w2, [x1, #0x2b]
    // 0xbff9c0: ldur            x3, [fp, #-8]
    // 0xbff9c4: LoadField: r4 = r3->field_b
    //     0xbff9c4: ldur            w4, [x3, #0xb]
    // 0xbff9c8: DecompressPointer r4
    //     0xbff9c8: add             x4, x4, HEAP, lsl #32
    // 0xbff9cc: cmp             w4, NULL
    // 0xbff9d0: b.eq            #0xc002f4
    // 0xbff9d4: LoadField: r5 = r4->field_b
    //     0xbff9d4: ldur            w5, [x4, #0xb]
    // 0xbff9d8: DecompressPointer r5
    //     0xbff9d8: add             x5, x5, HEAP, lsl #32
    // 0xbff9dc: LoadField: r4 = r5->field_b
    //     0xbff9dc: ldur            w4, [x5, #0xb]
    // 0xbff9e0: DecompressPointer r4
    //     0xbff9e0: add             x4, x4, HEAP, lsl #32
    // 0xbff9e4: cmp             w4, NULL
    // 0xbff9e8: b.ne            #0xbff9f4
    // 0xbff9ec: r4 = Null
    //     0xbff9ec: mov             x4, NULL
    // 0xbff9f0: b               #0xbffa10
    // 0xbff9f4: LoadField: r5 = r4->field_7
    //     0xbff9f4: ldur            w5, [x4, #7]
    // 0xbff9f8: DecompressPointer r5
    //     0xbff9f8: add             x5, x5, HEAP, lsl #32
    // 0xbff9fc: LoadField: r4 = r5->field_7
    //     0xbff9fc: ldur            w4, [x5, #7]
    // 0xbffa00: DecompressPointer r4
    //     0xbffa00: add             x4, x4, HEAP, lsl #32
    // 0xbffa04: LoadField: r5 = r4->field_7
    //     0xbffa04: ldur            w5, [x4, #7]
    // 0xbffa08: DecompressPointer r5
    //     0xbffa08: add             x5, x5, HEAP, lsl #32
    // 0xbffa0c: mov             x4, x5
    // 0xbffa10: cmp             w4, NULL
    // 0xbffa14: b.ne            #0xbffa20
    // 0xbffa18: r5 = false
    //     0xbffa18: add             x5, NULL, #0x30  ; false
    // 0xbffa1c: b               #0xbffa24
    // 0xbffa20: mov             x5, x4
    // 0xbffa24: ldur            x4, [fp, #-0x38]
    // 0xbffa28: eor             x6, x5, #0x10
    // 0xbffa2c: stur            x6, [fp, #-0x20]
    // 0xbffa30: r0 = SvgPicture()
    //     0xbffa30: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbffa34: stur            x0, [fp, #-0x30]
    // 0xbffa38: r16 = Instance_BoxFit
    //     0xbffa38: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbffa3c: ldr             x16, [x16, #0xb18]
    // 0xbffa40: str             x16, [SP]
    // 0xbffa44: mov             x1, x0
    // 0xbffa48: r2 = "assets/images/replacement_check.svg"
    //     0xbffa48: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xbffa4c: ldr             x2, [x2, #0xc90]
    // 0xbffa50: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbffa50: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbffa54: ldr             x4, [x4, #0xb0]
    // 0xbffa58: r0 = SvgPicture.asset()
    //     0xbffa58: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbffa5c: r0 = Visibility()
    //     0xbffa5c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbffa60: mov             x3, x0
    // 0xbffa64: ldur            x0, [fp, #-0x30]
    // 0xbffa68: stur            x3, [fp, #-0x48]
    // 0xbffa6c: StoreField: r3->field_b = r0
    //     0xbffa6c: stur            w0, [x3, #0xb]
    // 0xbffa70: r0 = Instance_SizedBox
    //     0xbffa70: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbffa74: StoreField: r3->field_f = r0
    //     0xbffa74: stur            w0, [x3, #0xf]
    // 0xbffa78: ldur            x0, [fp, #-0x20]
    // 0xbffa7c: StoreField: r3->field_13 = r0
    //     0xbffa7c: stur            w0, [x3, #0x13]
    // 0xbffa80: r0 = false
    //     0xbffa80: add             x0, NULL, #0x30  ; false
    // 0xbffa84: ArrayStore: r3[0] = r0  ; List_4
    //     0xbffa84: stur            w0, [x3, #0x17]
    // 0xbffa88: StoreField: r3->field_1b = r0
    //     0xbffa88: stur            w0, [x3, #0x1b]
    // 0xbffa8c: StoreField: r3->field_1f = r0
    //     0xbffa8c: stur            w0, [x3, #0x1f]
    // 0xbffa90: StoreField: r3->field_23 = r0
    //     0xbffa90: stur            w0, [x3, #0x23]
    // 0xbffa94: StoreField: r3->field_27 = r0
    //     0xbffa94: stur            w0, [x3, #0x27]
    // 0xbffa98: StoreField: r3->field_2b = r0
    //     0xbffa98: stur            w0, [x3, #0x2b]
    // 0xbffa9c: r1 = Null
    //     0xbffa9c: mov             x1, NULL
    // 0xbffaa0: r2 = 6
    //     0xbffaa0: movz            x2, #0x6
    // 0xbffaa4: r0 = AllocateArray()
    //     0xbffaa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbffaa8: mov             x2, x0
    // 0xbffaac: ldur            x0, [fp, #-0x38]
    // 0xbffab0: stur            x2, [fp, #-0x20]
    // 0xbffab4: StoreField: r2->field_f = r0
    //     0xbffab4: stur            w0, [x2, #0xf]
    // 0xbffab8: ldur            x0, [fp, #-0x40]
    // 0xbffabc: StoreField: r2->field_13 = r0
    //     0xbffabc: stur            w0, [x2, #0x13]
    // 0xbffac0: ldur            x0, [fp, #-0x48]
    // 0xbffac4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbffac4: stur            w0, [x2, #0x17]
    // 0xbffac8: r1 = <Widget>
    //     0xbffac8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbffacc: r0 = AllocateGrowableArray()
    //     0xbffacc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbffad0: mov             x1, x0
    // 0xbffad4: ldur            x0, [fp, #-0x20]
    // 0xbffad8: stur            x1, [fp, #-0x30]
    // 0xbffadc: StoreField: r1->field_f = r0
    //     0xbffadc: stur            w0, [x1, #0xf]
    // 0xbffae0: r0 = 6
    //     0xbffae0: movz            x0, #0x6
    // 0xbffae4: StoreField: r1->field_b = r0
    //     0xbffae4: stur            w0, [x1, #0xb]
    // 0xbffae8: r0 = Stack()
    //     0xbffae8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbffaec: mov             x1, x0
    // 0xbffaf0: r0 = Instance_Alignment
    //     0xbffaf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbffaf4: ldr             x0, [x0, #0x950]
    // 0xbffaf8: stur            x1, [fp, #-0x20]
    // 0xbffafc: StoreField: r1->field_f = r0
    //     0xbffafc: stur            w0, [x1, #0xf]
    // 0xbffb00: r0 = Instance_StackFit
    //     0xbffb00: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbffb04: ldr             x0, [x0, #0xfa8]
    // 0xbffb08: ArrayStore: r1[0] = r0  ; List_4
    //     0xbffb08: stur            w0, [x1, #0x17]
    // 0xbffb0c: r0 = Instance_Clip
    //     0xbffb0c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbffb10: ldr             x0, [x0, #0x7e0]
    // 0xbffb14: StoreField: r1->field_1b = r0
    //     0xbffb14: stur            w0, [x1, #0x1b]
    // 0xbffb18: ldur            x2, [fp, #-0x30]
    // 0xbffb1c: StoreField: r1->field_b = r2
    //     0xbffb1c: stur            w2, [x1, #0xb]
    // 0xbffb20: ldur            x2, [fp, #-8]
    // 0xbffb24: LoadField: r3 = r2->field_b
    //     0xbffb24: ldur            w3, [x2, #0xb]
    // 0xbffb28: DecompressPointer r3
    //     0xbffb28: add             x3, x3, HEAP, lsl #32
    // 0xbffb2c: cmp             w3, NULL
    // 0xbffb30: b.eq            #0xc002f8
    // 0xbffb34: LoadField: r4 = r3->field_b
    //     0xbffb34: ldur            w4, [x3, #0xb]
    // 0xbffb38: DecompressPointer r4
    //     0xbffb38: add             x4, x4, HEAP, lsl #32
    // 0xbffb3c: LoadField: r3 = r4->field_b
    //     0xbffb3c: ldur            w3, [x4, #0xb]
    // 0xbffb40: DecompressPointer r3
    //     0xbffb40: add             x3, x3, HEAP, lsl #32
    // 0xbffb44: cmp             w3, NULL
    // 0xbffb48: b.ne            #0xbffb54
    // 0xbffb4c: r4 = Null
    //     0xbffb4c: mov             x4, NULL
    // 0xbffb50: b               #0xbffb6c
    // 0xbffb54: LoadField: r4 = r3->field_7
    //     0xbffb54: ldur            w4, [x3, #7]
    // 0xbffb58: DecompressPointer r4
    //     0xbffb58: add             x4, x4, HEAP, lsl #32
    // 0xbffb5c: LoadField: r3 = r4->field_b
    //     0xbffb5c: ldur            w3, [x4, #0xb]
    // 0xbffb60: DecompressPointer r3
    //     0xbffb60: add             x3, x3, HEAP, lsl #32
    // 0xbffb64: LoadField: r4 = r3->field_b
    //     0xbffb64: ldur            w4, [x3, #0xb]
    // 0xbffb68: DecompressPointer r4
    //     0xbffb68: add             x4, x4, HEAP, lsl #32
    // 0xbffb6c: ldur            x3, [fp, #-0x28]
    // 0xbffb70: str             x4, [SP]
    // 0xbffb74: r0 = _interpolateSingle()
    //     0xbffb74: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbffb78: ldur            x1, [fp, #-0x10]
    // 0xbffb7c: stur            x0, [fp, #-0x30]
    // 0xbffb80: r0 = of()
    //     0xbffb80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbffb84: LoadField: r1 = r0->field_87
    //     0xbffb84: ldur            w1, [x0, #0x87]
    // 0xbffb88: DecompressPointer r1
    //     0xbffb88: add             x1, x1, HEAP, lsl #32
    // 0xbffb8c: LoadField: r0 = r1->field_7
    //     0xbffb8c: ldur            w0, [x1, #7]
    // 0xbffb90: DecompressPointer r0
    //     0xbffb90: add             x0, x0, HEAP, lsl #32
    // 0xbffb94: r16 = Instance_Color
    //     0xbffb94: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbffb98: r30 = 14.000000
    //     0xbffb98: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbffb9c: ldr             lr, [lr, #0x1d8]
    // 0xbffba0: stp             lr, x16, [SP]
    // 0xbffba4: mov             x1, x0
    // 0xbffba8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbffba8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbffbac: ldr             x4, [x4, #0x9b8]
    // 0xbffbb0: r0 = copyWith()
    //     0xbffbb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbffbb4: stur            x0, [fp, #-0x38]
    // 0xbffbb8: r0 = Text()
    //     0xbffbb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbffbbc: mov             x3, x0
    // 0xbffbc0: ldur            x0, [fp, #-0x30]
    // 0xbffbc4: stur            x3, [fp, #-0x40]
    // 0xbffbc8: StoreField: r3->field_b = r0
    //     0xbffbc8: stur            w0, [x3, #0xb]
    // 0xbffbcc: ldur            x0, [fp, #-0x38]
    // 0xbffbd0: StoreField: r3->field_13 = r0
    //     0xbffbd0: stur            w0, [x3, #0x13]
    // 0xbffbd4: r1 = Null
    //     0xbffbd4: mov             x1, NULL
    // 0xbffbd8: r2 = 4
    //     0xbffbd8: movz            x2, #0x4
    // 0xbffbdc: r0 = AllocateArray()
    //     0xbffbdc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbffbe0: mov             x2, x0
    // 0xbffbe4: ldur            x0, [fp, #-0x20]
    // 0xbffbe8: stur            x2, [fp, #-0x30]
    // 0xbffbec: StoreField: r2->field_f = r0
    //     0xbffbec: stur            w0, [x2, #0xf]
    // 0xbffbf0: ldur            x0, [fp, #-0x40]
    // 0xbffbf4: StoreField: r2->field_13 = r0
    //     0xbffbf4: stur            w0, [x2, #0x13]
    // 0xbffbf8: r1 = <Widget>
    //     0xbffbf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbffbfc: r0 = AllocateGrowableArray()
    //     0xbffbfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbffc00: mov             x1, x0
    // 0xbffc04: ldur            x0, [fp, #-0x30]
    // 0xbffc08: stur            x1, [fp, #-0x20]
    // 0xbffc0c: StoreField: r1->field_f = r0
    //     0xbffc0c: stur            w0, [x1, #0xf]
    // 0xbffc10: r2 = 4
    //     0xbffc10: movz            x2, #0x4
    // 0xbffc14: StoreField: r1->field_b = r2
    //     0xbffc14: stur            w2, [x1, #0xb]
    // 0xbffc18: r0 = Column()
    //     0xbffc18: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbffc1c: mov             x3, x0
    // 0xbffc20: r0 = Instance_Axis
    //     0xbffc20: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbffc24: stur            x3, [fp, #-0x30]
    // 0xbffc28: StoreField: r3->field_f = r0
    //     0xbffc28: stur            w0, [x3, #0xf]
    // 0xbffc2c: r4 = Instance_MainAxisAlignment
    //     0xbffc2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbffc30: ldr             x4, [x4, #0xa08]
    // 0xbffc34: StoreField: r3->field_13 = r4
    //     0xbffc34: stur            w4, [x3, #0x13]
    // 0xbffc38: r5 = Instance_MainAxisSize
    //     0xbffc38: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbffc3c: ldr             x5, [x5, #0xa10]
    // 0xbffc40: ArrayStore: r3[0] = r5  ; List_4
    //     0xbffc40: stur            w5, [x3, #0x17]
    // 0xbffc44: r6 = Instance_CrossAxisAlignment
    //     0xbffc44: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbffc48: ldr             x6, [x6, #0xa18]
    // 0xbffc4c: StoreField: r3->field_1b = r6
    //     0xbffc4c: stur            w6, [x3, #0x1b]
    // 0xbffc50: r7 = Instance_VerticalDirection
    //     0xbffc50: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbffc54: ldr             x7, [x7, #0xa20]
    // 0xbffc58: StoreField: r3->field_23 = r7
    //     0xbffc58: stur            w7, [x3, #0x23]
    // 0xbffc5c: r8 = Instance_Clip
    //     0xbffc5c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbffc60: ldr             x8, [x8, #0x38]
    // 0xbffc64: StoreField: r3->field_2b = r8
    //     0xbffc64: stur            w8, [x3, #0x2b]
    // 0xbffc68: StoreField: r3->field_2f = rZR
    //     0xbffc68: stur            xzr, [x3, #0x2f]
    // 0xbffc6c: ldur            x1, [fp, #-0x20]
    // 0xbffc70: StoreField: r3->field_b = r1
    //     0xbffc70: stur            w1, [x3, #0xb]
    // 0xbffc74: r1 = Null
    //     0xbffc74: mov             x1, NULL
    // 0xbffc78: r2 = 4
    //     0xbffc78: movz            x2, #0x4
    // 0xbffc7c: r0 = AllocateArray()
    //     0xbffc7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbffc80: mov             x2, x0
    // 0xbffc84: ldur            x0, [fp, #-0x28]
    // 0xbffc88: stur            x2, [fp, #-0x20]
    // 0xbffc8c: StoreField: r2->field_f = r0
    //     0xbffc8c: stur            w0, [x2, #0xf]
    // 0xbffc90: ldur            x0, [fp, #-0x30]
    // 0xbffc94: StoreField: r2->field_13 = r0
    //     0xbffc94: stur            w0, [x2, #0x13]
    // 0xbffc98: r1 = <Widget>
    //     0xbffc98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbffc9c: r0 = AllocateGrowableArray()
    //     0xbffc9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbffca0: mov             x1, x0
    // 0xbffca4: ldur            x0, [fp, #-0x20]
    // 0xbffca8: stur            x1, [fp, #-0x28]
    // 0xbffcac: StoreField: r1->field_f = r0
    //     0xbffcac: stur            w0, [x1, #0xf]
    // 0xbffcb0: r0 = 4
    //     0xbffcb0: movz            x0, #0x4
    // 0xbffcb4: StoreField: r1->field_b = r0
    //     0xbffcb4: stur            w0, [x1, #0xb]
    // 0xbffcb8: r0 = Row()
    //     0xbffcb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbffcbc: mov             x1, x0
    // 0xbffcc0: r0 = Instance_Axis
    //     0xbffcc0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbffcc4: stur            x1, [fp, #-0x20]
    // 0xbffcc8: StoreField: r1->field_f = r0
    //     0xbffcc8: stur            w0, [x1, #0xf]
    // 0xbffccc: r0 = Instance_MainAxisAlignment
    //     0xbffccc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbffcd0: ldr             x0, [x0, #0xd10]
    // 0xbffcd4: StoreField: r1->field_13 = r0
    //     0xbffcd4: stur            w0, [x1, #0x13]
    // 0xbffcd8: r0 = Instance_MainAxisSize
    //     0xbffcd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbffcdc: ldr             x0, [x0, #0xa10]
    // 0xbffce0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbffce0: stur            w0, [x1, #0x17]
    // 0xbffce4: r0 = Instance_CrossAxisAlignment
    //     0xbffce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbffce8: ldr             x0, [x0, #0xa18]
    // 0xbffcec: StoreField: r1->field_1b = r0
    //     0xbffcec: stur            w0, [x1, #0x1b]
    // 0xbffcf0: r0 = Instance_VerticalDirection
    //     0xbffcf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbffcf4: ldr             x0, [x0, #0xa20]
    // 0xbffcf8: StoreField: r1->field_23 = r0
    //     0xbffcf8: stur            w0, [x1, #0x23]
    // 0xbffcfc: r2 = Instance_Clip
    //     0xbffcfc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbffd00: ldr             x2, [x2, #0x38]
    // 0xbffd04: StoreField: r1->field_2b = r2
    //     0xbffd04: stur            w2, [x1, #0x2b]
    // 0xbffd08: StoreField: r1->field_2f = rZR
    //     0xbffd08: stur            xzr, [x1, #0x2f]
    // 0xbffd0c: ldur            x3, [fp, #-0x28]
    // 0xbffd10: StoreField: r1->field_b = r3
    //     0xbffd10: stur            w3, [x1, #0xb]
    // 0xbffd14: r0 = Padding()
    //     0xbffd14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbffd18: mov             x1, x0
    // 0xbffd1c: r0 = Instance_EdgeInsets
    //     0xbffd1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbffd20: ldr             x0, [x0, #0x980]
    // 0xbffd24: stur            x1, [fp, #-0x28]
    // 0xbffd28: StoreField: r1->field_f = r0
    //     0xbffd28: stur            w0, [x1, #0xf]
    // 0xbffd2c: ldur            x0, [fp, #-0x20]
    // 0xbffd30: StoreField: r1->field_b = r0
    //     0xbffd30: stur            w0, [x1, #0xb]
    // 0xbffd34: ldur            x0, [fp, #-8]
    // 0xbffd38: LoadField: r2 = r0->field_b
    //     0xbffd38: ldur            w2, [x0, #0xb]
    // 0xbffd3c: DecompressPointer r2
    //     0xbffd3c: add             x2, x2, HEAP, lsl #32
    // 0xbffd40: cmp             w2, NULL
    // 0xbffd44: b.eq            #0xc002fc
    // 0xbffd48: LoadField: r3 = r2->field_b
    //     0xbffd48: ldur            w3, [x2, #0xb]
    // 0xbffd4c: DecompressPointer r3
    //     0xbffd4c: add             x3, x3, HEAP, lsl #32
    // 0xbffd50: LoadField: r2 = r3->field_b
    //     0xbffd50: ldur            w2, [x3, #0xb]
    // 0xbffd54: DecompressPointer r2
    //     0xbffd54: add             x2, x2, HEAP, lsl #32
    // 0xbffd58: cmp             w2, NULL
    // 0xbffd5c: b.ne            #0xbffd68
    // 0xbffd60: r2 = Null
    //     0xbffd60: mov             x2, NULL
    // 0xbffd64: b               #0xbffd78
    // 0xbffd68: LoadField: r3 = r2->field_b
    //     0xbffd68: ldur            w3, [x2, #0xb]
    // 0xbffd6c: DecompressPointer r3
    //     0xbffd6c: add             x3, x3, HEAP, lsl #32
    // 0xbffd70: LoadField: r2 = r3->field_b
    //     0xbffd70: ldur            w2, [x3, #0xb]
    // 0xbffd74: DecompressPointer r2
    //     0xbffd74: add             x2, x2, HEAP, lsl #32
    // 0xbffd78: str             x2, [SP]
    // 0xbffd7c: r0 = _interpolateSingle()
    //     0xbffd7c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbffd80: ldur            x1, [fp, #-0x10]
    // 0xbffd84: stur            x0, [fp, #-0x20]
    // 0xbffd88: r0 = of()
    //     0xbffd88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbffd8c: LoadField: r1 = r0->field_87
    //     0xbffd8c: ldur            w1, [x0, #0x87]
    // 0xbffd90: DecompressPointer r1
    //     0xbffd90: add             x1, x1, HEAP, lsl #32
    // 0xbffd94: LoadField: r0 = r1->field_7
    //     0xbffd94: ldur            w0, [x1, #7]
    // 0xbffd98: DecompressPointer r0
    //     0xbffd98: add             x0, x0, HEAP, lsl #32
    // 0xbffd9c: r16 = Instance_Color
    //     0xbffd9c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbffda0: r30 = 16.000000
    //     0xbffda0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbffda4: ldr             lr, [lr, #0x188]
    // 0xbffda8: stp             lr, x16, [SP]
    // 0xbffdac: mov             x1, x0
    // 0xbffdb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbffdb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbffdb4: ldr             x4, [x4, #0x9b8]
    // 0xbffdb8: r0 = copyWith()
    //     0xbffdb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbffdbc: stur            x0, [fp, #-0x30]
    // 0xbffdc0: r0 = Text()
    //     0xbffdc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbffdc4: mov             x1, x0
    // 0xbffdc8: ldur            x0, [fp, #-0x20]
    // 0xbffdcc: stur            x1, [fp, #-0x38]
    // 0xbffdd0: StoreField: r1->field_b = r0
    //     0xbffdd0: stur            w0, [x1, #0xb]
    // 0xbffdd4: ldur            x0, [fp, #-0x30]
    // 0xbffdd8: StoreField: r1->field_13 = r0
    //     0xbffdd8: stur            w0, [x1, #0x13]
    // 0xbffddc: r0 = Padding()
    //     0xbffddc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbffde0: mov             x3, x0
    // 0xbffde4: r0 = Instance_EdgeInsets
    //     0xbffde4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbffde8: ldr             x0, [x0, #0xf30]
    // 0xbffdec: stur            x3, [fp, #-0x30]
    // 0xbffdf0: StoreField: r3->field_f = r0
    //     0xbffdf0: stur            w0, [x3, #0xf]
    // 0xbffdf4: ldur            x1, [fp, #-0x38]
    // 0xbffdf8: StoreField: r3->field_b = r1
    //     0xbffdf8: stur            w1, [x3, #0xb]
    // 0xbffdfc: ldur            x4, [fp, #-8]
    // 0xbffe00: LoadField: r1 = r4->field_b
    //     0xbffe00: ldur            w1, [x4, #0xb]
    // 0xbffe04: DecompressPointer r1
    //     0xbffe04: add             x1, x1, HEAP, lsl #32
    // 0xbffe08: cmp             w1, NULL
    // 0xbffe0c: b.eq            #0xc00300
    // 0xbffe10: LoadField: r2 = r1->field_b
    //     0xbffe10: ldur            w2, [x1, #0xb]
    // 0xbffe14: DecompressPointer r2
    //     0xbffe14: add             x2, x2, HEAP, lsl #32
    // 0xbffe18: LoadField: r1 = r2->field_b
    //     0xbffe18: ldur            w1, [x2, #0xb]
    // 0xbffe1c: DecompressPointer r1
    //     0xbffe1c: add             x1, x1, HEAP, lsl #32
    // 0xbffe20: cmp             w1, NULL
    // 0xbffe24: b.ne            #0xbffe30
    // 0xbffe28: r5 = Null
    //     0xbffe28: mov             x5, NULL
    // 0xbffe2c: b               #0xbffe48
    // 0xbffe30: LoadField: r2 = r1->field_b
    //     0xbffe30: ldur            w2, [x1, #0xb]
    // 0xbffe34: DecompressPointer r2
    //     0xbffe34: add             x2, x2, HEAP, lsl #32
    // 0xbffe38: LoadField: r1 = r2->field_7
    //     0xbffe38: ldur            w1, [x2, #7]
    // 0xbffe3c: DecompressPointer r1
    //     0xbffe3c: add             x1, x1, HEAP, lsl #32
    // 0xbffe40: LoadField: r2 = r1->field_b
    //     0xbffe40: ldur            w2, [x1, #0xb]
    // 0xbffe44: mov             x5, x2
    // 0xbffe48: ldur            x2, [fp, #-0x18]
    // 0xbffe4c: stur            x5, [fp, #-0x20]
    // 0xbffe50: r1 = Function '<anonymous closure>':.
    //     0xbffe50: add             x1, PP, #0x52, lsl #12  ; [pp+0x52ca8] AnonymousClosure: (0xa79a30), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xbffe54: ldr             x1, [x1, #0xca8]
    // 0xbffe58: r0 = AllocateClosure()
    //     0xbffe58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbffe5c: stur            x0, [fp, #-0x38]
    // 0xbffe60: r0 = ListView()
    //     0xbffe60: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbffe64: stur            x0, [fp, #-0x40]
    // 0xbffe68: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbffe68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbffe6c: ldr             x16, [x16, #0x1c8]
    // 0xbffe70: r30 = true
    //     0xbffe70: add             lr, NULL, #0x20  ; true
    // 0xbffe74: stp             lr, x16, [SP, #8]
    // 0xbffe78: r16 = false
    //     0xbffe78: add             x16, NULL, #0x30  ; false
    // 0xbffe7c: str             x16, [SP]
    // 0xbffe80: mov             x1, x0
    // 0xbffe84: ldur            x2, [fp, #-0x38]
    // 0xbffe88: ldur            x3, [fp, #-0x20]
    // 0xbffe8c: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x3, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xbffe8c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32058] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x3, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbffe90: ldr             x4, [x4, #0x58]
    // 0xbffe94: r0 = ListView.builder()
    //     0xbffe94: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbffe98: r0 = Padding()
    //     0xbffe98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbffe9c: mov             x1, x0
    // 0xbffea0: r0 = Instance_EdgeInsets
    //     0xbffea0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbffea4: ldr             x0, [x0, #0xf30]
    // 0xbffea8: stur            x1, [fp, #-0x20]
    // 0xbffeac: StoreField: r1->field_f = r0
    //     0xbffeac: stur            w0, [x1, #0xf]
    // 0xbffeb0: ldur            x2, [fp, #-0x40]
    // 0xbffeb4: StoreField: r1->field_b = r2
    //     0xbffeb4: stur            w2, [x1, #0xb]
    // 0xbffeb8: ldur            x2, [fp, #-8]
    // 0xbffebc: LoadField: r3 = r2->field_b
    //     0xbffebc: ldur            w3, [x2, #0xb]
    // 0xbffec0: DecompressPointer r3
    //     0xbffec0: add             x3, x3, HEAP, lsl #32
    // 0xbffec4: cmp             w3, NULL
    // 0xbffec8: b.eq            #0xc00304
    // 0xbffecc: LoadField: r4 = r3->field_b
    //     0xbffecc: ldur            w4, [x3, #0xb]
    // 0xbffed0: DecompressPointer r4
    //     0xbffed0: add             x4, x4, HEAP, lsl #32
    // 0xbffed4: LoadField: r3 = r4->field_b
    //     0xbffed4: ldur            w3, [x4, #0xb]
    // 0xbffed8: DecompressPointer r3
    //     0xbffed8: add             x3, x3, HEAP, lsl #32
    // 0xbffedc: cmp             w3, NULL
    // 0xbffee0: b.ne            #0xbffeec
    // 0xbffee4: r3 = Null
    //     0xbffee4: mov             x3, NULL
    // 0xbffee8: b               #0xbffefc
    // 0xbffeec: LoadField: r4 = r3->field_f
    //     0xbffeec: ldur            w4, [x3, #0xf]
    // 0xbffef0: DecompressPointer r4
    //     0xbffef0: add             x4, x4, HEAP, lsl #32
    // 0xbffef4: LoadField: r3 = r4->field_7
    //     0xbffef4: ldur            w3, [x4, #7]
    // 0xbffef8: DecompressPointer r3
    //     0xbffef8: add             x3, x3, HEAP, lsl #32
    // 0xbffefc: str             x3, [SP]
    // 0xbfff00: r0 = _interpolateSingle()
    //     0xbfff00: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbfff04: ldur            x1, [fp, #-0x10]
    // 0xbfff08: stur            x0, [fp, #-0x38]
    // 0xbfff0c: r0 = of()
    //     0xbfff0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfff10: LoadField: r1 = r0->field_87
    //     0xbfff10: ldur            w1, [x0, #0x87]
    // 0xbfff14: DecompressPointer r1
    //     0xbfff14: add             x1, x1, HEAP, lsl #32
    // 0xbfff18: LoadField: r0 = r1->field_7
    //     0xbfff18: ldur            w0, [x1, #7]
    // 0xbfff1c: DecompressPointer r0
    //     0xbfff1c: add             x0, x0, HEAP, lsl #32
    // 0xbfff20: r16 = Instance_Color
    //     0xbfff20: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfff24: r30 = 16.000000
    //     0xbfff24: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbfff28: ldr             lr, [lr, #0x188]
    // 0xbfff2c: stp             lr, x16, [SP]
    // 0xbfff30: mov             x1, x0
    // 0xbfff34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbfff34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbfff38: ldr             x4, [x4, #0x9b8]
    // 0xbfff3c: r0 = copyWith()
    //     0xbfff3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfff40: stur            x0, [fp, #-0x40]
    // 0xbfff44: r0 = Text()
    //     0xbfff44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfff48: mov             x1, x0
    // 0xbfff4c: ldur            x0, [fp, #-0x38]
    // 0xbfff50: stur            x1, [fp, #-0x48]
    // 0xbfff54: StoreField: r1->field_b = r0
    //     0xbfff54: stur            w0, [x1, #0xb]
    // 0xbfff58: ldur            x0, [fp, #-0x40]
    // 0xbfff5c: StoreField: r1->field_13 = r0
    //     0xbfff5c: stur            w0, [x1, #0x13]
    // 0xbfff60: r0 = Padding()
    //     0xbfff60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfff64: mov             x2, x0
    // 0xbfff68: r0 = Instance_EdgeInsets
    //     0xbfff68: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xbfff6c: ldr             x0, [x0, #0xf30]
    // 0xbfff70: stur            x2, [fp, #-0x38]
    // 0xbfff74: StoreField: r2->field_f = r0
    //     0xbfff74: stur            w0, [x2, #0xf]
    // 0xbfff78: ldur            x0, [fp, #-0x48]
    // 0xbfff7c: StoreField: r2->field_b = r0
    //     0xbfff7c: stur            w0, [x2, #0xb]
    // 0xbfff80: r16 = 0.000000
    //     0xbfff80: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbfff84: r30 = Instance_ConnectorThemeData
    //     0xbfff84: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3fe88] Obj!ConnectorThemeData@d5bb71
    //     0xbfff88: ldr             lr, [lr, #0xe88]
    // 0xbfff8c: stp             lr, x16, [SP]
    // 0xbfff90: r1 = Null
    //     0xbfff90: mov             x1, NULL
    // 0xbfff94: r4 = const [0, 0x3, 0x2, 0x1, connectorTheme, 0x2, nodePosition, 0x1, null]
    //     0xbfff94: add             x4, PP, #0x36, lsl #12  ; [pp+0x365b8] List(9) [0, 0x3, 0x2, 0x1, "connectorTheme", 0x2, "nodePosition", 0x1, Null]
    //     0xbfff98: ldr             x4, [x4, #0x5b8]
    // 0xbfff9c: r0 = TimelineThemeData()
    //     0xbfff9c: bl              #0x9dffe8  ; [package:timelines_plus/src/timeline_theme.dart] TimelineThemeData::TimelineThemeData
    // 0xbfffa0: mov             x3, x0
    // 0xbfffa4: ldur            x0, [fp, #-8]
    // 0xbfffa8: stur            x3, [fp, #-0x40]
    // 0xbfffac: LoadField: r1 = r0->field_b
    //     0xbfffac: ldur            w1, [x0, #0xb]
    // 0xbfffb0: DecompressPointer r1
    //     0xbfffb0: add             x1, x1, HEAP, lsl #32
    // 0xbfffb4: cmp             w1, NULL
    // 0xbfffb8: b.eq            #0xc00308
    // 0xbfffbc: LoadField: r0 = r1->field_b
    //     0xbfffbc: ldur            w0, [x1, #0xb]
    // 0xbfffc0: DecompressPointer r0
    //     0xbfffc0: add             x0, x0, HEAP, lsl #32
    // 0xbfffc4: LoadField: r1 = r0->field_b
    //     0xbfffc4: ldur            w1, [x0, #0xb]
    // 0xbfffc8: DecompressPointer r1
    //     0xbfffc8: add             x1, x1, HEAP, lsl #32
    // 0xbfffcc: cmp             w1, NULL
    // 0xbfffd0: b.ne            #0xbfffdc
    // 0xbfffd4: r0 = Null
    //     0xbfffd4: mov             x0, NULL
    // 0xbfffd8: b               #0xbffff0
    // 0xbfffdc: LoadField: r0 = r1->field_f
    //     0xbfffdc: ldur            w0, [x1, #0xf]
    // 0xbfffe0: DecompressPointer r0
    //     0xbfffe0: add             x0, x0, HEAP, lsl #32
    // 0xbfffe4: LoadField: r1 = r0->field_b
    //     0xbfffe4: ldur            w1, [x0, #0xb]
    // 0xbfffe8: DecompressPointer r1
    //     0xbfffe8: add             x1, x1, HEAP, lsl #32
    // 0xbfffec: LoadField: r0 = r1->field_b
    //     0xbfffec: ldur            w0, [x1, #0xb]
    // 0xbffff0: cmp             w0, NULL
    // 0xbffff4: b.ne            #0xc00000
    // 0xbffff8: r7 = 0
    //     0xbffff8: movz            x7, #0
    // 0xbffffc: b               #0xc00008
    // 0xc00000: r1 = LoadInt32Instr(r0)
    //     0xc00000: sbfx            x1, x0, #1, #0x1f
    // 0xc00004: mov             x7, x1
    // 0xc00008: ldur            x6, [fp, #-0x28]
    // 0xc0000c: ldur            x5, [fp, #-0x30]
    // 0xc00010: ldur            x4, [fp, #-0x20]
    // 0xc00014: ldur            x0, [fp, #-0x38]
    // 0xc00018: ldur            x2, [fp, #-0x18]
    // 0xc0001c: stur            x7, [fp, #-0x50]
    // 0xc00020: r1 = Function '<anonymous closure>':.
    //     0xc00020: add             x1, PP, #0x52, lsl #12  ; [pp+0x52cb0] AnonymousClosure: (0xa7971c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xc00024: ldr             x1, [x1, #0xcb0]
    // 0xc00028: r0 = AllocateClosure()
    //     0xc00028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0002c: r1 = Function '<anonymous closure>':.
    //     0xc0002c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52cb8] AnonymousClosure: (0xa79710), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xc00030: ldr             x1, [x1, #0xcb8]
    // 0xc00034: r2 = Null
    //     0xc00034: mov             x2, NULL
    // 0xc00038: stur            x0, [fp, #-8]
    // 0xc0003c: r0 = AllocateClosure()
    //     0xc0003c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00040: r1 = Function '<anonymous closure>':.
    //     0xc00040: add             x1, PP, #0x52, lsl #12  ; [pp+0x52cc0] AnonymousClosure: (0xa796c4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xc00044: ldr             x1, [x1, #0xcc0]
    // 0xc00048: r2 = Null
    //     0xc00048: mov             x2, NULL
    // 0xc0004c: stur            x0, [fp, #-0x18]
    // 0xc00050: r0 = AllocateClosure()
    //     0xc00050: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00054: mov             x2, x0
    // 0xc00058: ldur            x3, [fp, #-8]
    // 0xc0005c: ldur            x5, [fp, #-0x18]
    // 0xc00060: ldur            x6, [fp, #-0x50]
    // 0xc00064: r1 = Null
    //     0xc00064: mov             x1, NULL
    // 0xc00068: r0 = TimelineTileBuilder.connected()
    //     0xc00068: bl              #0x9dfab0  ; [package:timelines_plus/src/timeline_tile_builder.dart] TimelineTileBuilder::TimelineTileBuilder.connected
    // 0xc0006c: mov             x2, x0
    // 0xc00070: ldur            x3, [fp, #-0x40]
    // 0xc00074: r1 = Null
    //     0xc00074: mov             x1, NULL
    // 0xc00078: r0 = Timeline.tileBuilder()
    //     0xc00078: bl              #0x9df894  ; [package:timelines_plus/src/timelines.dart] Timeline::Timeline.tileBuilder
    // 0xc0007c: stur            x0, [fp, #-8]
    // 0xc00080: r0 = Padding()
    //     0xc00080: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc00084: mov             x2, x0
    // 0xc00088: r0 = Instance_EdgeInsets
    //     0xc00088: add             x0, PP, #0x43, lsl #12  ; [pp+0x437a8] Obj!EdgeInsets@d58881
    //     0xc0008c: ldr             x0, [x0, #0x7a8]
    // 0xc00090: stur            x2, [fp, #-0x18]
    // 0xc00094: StoreField: r2->field_f = r0
    //     0xc00094: stur            w0, [x2, #0xf]
    // 0xc00098: ldur            x0, [fp, #-8]
    // 0xc0009c: StoreField: r2->field_b = r0
    //     0xc0009c: stur            w0, [x2, #0xb]
    // 0xc000a0: ldur            x1, [fp, #-0x10]
    // 0xc000a4: r0 = of()
    //     0xc000a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc000a8: r17 = 307
    //     0xc000a8: movz            x17, #0x133
    // 0xc000ac: ldr             w2, [x0, x17]
    // 0xc000b0: DecompressPointer r2
    //     0xc000b0: add             x2, x2, HEAP, lsl #32
    // 0xc000b4: ldur            x1, [fp, #-0x10]
    // 0xc000b8: stur            x2, [fp, #-8]
    // 0xc000bc: r0 = of()
    //     0xc000bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc000c0: LoadField: r1 = r0->field_87
    //     0xc000c0: ldur            w1, [x0, #0x87]
    // 0xc000c4: DecompressPointer r1
    //     0xc000c4: add             x1, x1, HEAP, lsl #32
    // 0xc000c8: LoadField: r0 = r1->field_7
    //     0xc000c8: ldur            w0, [x1, #7]
    // 0xc000cc: DecompressPointer r0
    //     0xc000cc: add             x0, x0, HEAP, lsl #32
    // 0xc000d0: r16 = 16.000000
    //     0xc000d0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc000d4: ldr             x16, [x16, #0x188]
    // 0xc000d8: r30 = Instance_Color
    //     0xc000d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc000dc: stp             lr, x16, [SP]
    // 0xc000e0: mov             x1, x0
    // 0xc000e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc000e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc000e8: ldr             x4, [x4, #0xaa0]
    // 0xc000ec: r0 = copyWith()
    //     0xc000ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc000f0: stur            x0, [fp, #-0x10]
    // 0xc000f4: r0 = Text()
    //     0xc000f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc000f8: mov             x3, x0
    // 0xc000fc: r0 = "OK,GO BACK"
    //     0xc000fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52cc8] "OK,GO BACK"
    //     0xc00100: ldr             x0, [x0, #0xcc8]
    // 0xc00104: stur            x3, [fp, #-0x40]
    // 0xc00108: StoreField: r3->field_b = r0
    //     0xc00108: stur            w0, [x3, #0xb]
    // 0xc0010c: ldur            x0, [fp, #-0x10]
    // 0xc00110: StoreField: r3->field_13 = r0
    //     0xc00110: stur            w0, [x3, #0x13]
    // 0xc00114: r1 = Function '<anonymous closure>':.
    //     0xc00114: add             x1, PP, #0x52, lsl #12  ; [pp+0x52cd0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc00118: ldr             x1, [x1, #0xcd0]
    // 0xc0011c: r2 = Null
    //     0xc0011c: mov             x2, NULL
    // 0xc00120: r0 = AllocateClosure()
    //     0xc00120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00124: stur            x0, [fp, #-0x10]
    // 0xc00128: r0 = TextButton()
    //     0xc00128: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc0012c: mov             x1, x0
    // 0xc00130: ldur            x0, [fp, #-0x10]
    // 0xc00134: stur            x1, [fp, #-0x48]
    // 0xc00138: StoreField: r1->field_b = r0
    //     0xc00138: stur            w0, [x1, #0xb]
    // 0xc0013c: r0 = false
    //     0xc0013c: add             x0, NULL, #0x30  ; false
    // 0xc00140: StoreField: r1->field_27 = r0
    //     0xc00140: stur            w0, [x1, #0x27]
    // 0xc00144: r2 = true
    //     0xc00144: add             x2, NULL, #0x20  ; true
    // 0xc00148: StoreField: r1->field_2f = r2
    //     0xc00148: stur            w2, [x1, #0x2f]
    // 0xc0014c: ldur            x2, [fp, #-0x40]
    // 0xc00150: StoreField: r1->field_37 = r2
    //     0xc00150: stur            w2, [x1, #0x37]
    // 0xc00154: r0 = TextButtonTheme()
    //     0xc00154: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc00158: mov             x1, x0
    // 0xc0015c: ldur            x0, [fp, #-8]
    // 0xc00160: stur            x1, [fp, #-0x10]
    // 0xc00164: StoreField: r1->field_f = r0
    //     0xc00164: stur            w0, [x1, #0xf]
    // 0xc00168: ldur            x0, [fp, #-0x48]
    // 0xc0016c: StoreField: r1->field_b = r0
    //     0xc0016c: stur            w0, [x1, #0xb]
    // 0xc00170: r0 = SizedBox()
    //     0xc00170: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc00174: mov             x1, x0
    // 0xc00178: r0 = inf
    //     0xc00178: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc0017c: ldr             x0, [x0, #0x9f8]
    // 0xc00180: stur            x1, [fp, #-8]
    // 0xc00184: StoreField: r1->field_f = r0
    //     0xc00184: stur            w0, [x1, #0xf]
    // 0xc00188: r0 = 44.000000
    //     0xc00188: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xc0018c: ldr             x0, [x0, #0xad8]
    // 0xc00190: StoreField: r1->field_13 = r0
    //     0xc00190: stur            w0, [x1, #0x13]
    // 0xc00194: ldur            x0, [fp, #-0x10]
    // 0xc00198: StoreField: r1->field_b = r0
    //     0xc00198: stur            w0, [x1, #0xb]
    // 0xc0019c: r0 = Padding()
    //     0xc0019c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc001a0: mov             x3, x0
    // 0xc001a4: r0 = Instance_EdgeInsets
    //     0xc001a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc001a8: ldr             x0, [x0, #0x1f0]
    // 0xc001ac: stur            x3, [fp, #-0x10]
    // 0xc001b0: StoreField: r3->field_f = r0
    //     0xc001b0: stur            w0, [x3, #0xf]
    // 0xc001b4: ldur            x0, [fp, #-8]
    // 0xc001b8: StoreField: r3->field_b = r0
    //     0xc001b8: stur            w0, [x3, #0xb]
    // 0xc001bc: r1 = Null
    //     0xc001bc: mov             x1, NULL
    // 0xc001c0: r2 = 16
    //     0xc001c0: movz            x2, #0x10
    // 0xc001c4: r0 = AllocateArray()
    //     0xc001c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc001c8: mov             x2, x0
    // 0xc001cc: ldur            x0, [fp, #-0x28]
    // 0xc001d0: stur            x2, [fp, #-8]
    // 0xc001d4: StoreField: r2->field_f = r0
    //     0xc001d4: stur            w0, [x2, #0xf]
    // 0xc001d8: r16 = Instance_SizedBox
    //     0xc001d8: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xc001dc: ldr             x16, [x16, #0x328]
    // 0xc001e0: StoreField: r2->field_13 = r16
    //     0xc001e0: stur            w16, [x2, #0x13]
    // 0xc001e4: ldur            x0, [fp, #-0x30]
    // 0xc001e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xc001e8: stur            w0, [x2, #0x17]
    // 0xc001ec: ldur            x0, [fp, #-0x20]
    // 0xc001f0: StoreField: r2->field_1b = r0
    //     0xc001f0: stur            w0, [x2, #0x1b]
    // 0xc001f4: r16 = Instance_SizedBox
    //     0xc001f4: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xc001f8: ldr             x16, [x16, #0x328]
    // 0xc001fc: StoreField: r2->field_1f = r16
    //     0xc001fc: stur            w16, [x2, #0x1f]
    // 0xc00200: ldur            x0, [fp, #-0x38]
    // 0xc00204: StoreField: r2->field_23 = r0
    //     0xc00204: stur            w0, [x2, #0x23]
    // 0xc00208: ldur            x0, [fp, #-0x18]
    // 0xc0020c: StoreField: r2->field_27 = r0
    //     0xc0020c: stur            w0, [x2, #0x27]
    // 0xc00210: ldur            x0, [fp, #-0x10]
    // 0xc00214: StoreField: r2->field_2b = r0
    //     0xc00214: stur            w0, [x2, #0x2b]
    // 0xc00218: r1 = <Widget>
    //     0xc00218: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0021c: r0 = AllocateGrowableArray()
    //     0xc0021c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc00220: mov             x1, x0
    // 0xc00224: ldur            x0, [fp, #-8]
    // 0xc00228: stur            x1, [fp, #-0x10]
    // 0xc0022c: StoreField: r1->field_f = r0
    //     0xc0022c: stur            w0, [x1, #0xf]
    // 0xc00230: r0 = 16
    //     0xc00230: movz            x0, #0x10
    // 0xc00234: StoreField: r1->field_b = r0
    //     0xc00234: stur            w0, [x1, #0xb]
    // 0xc00238: r0 = Column()
    //     0xc00238: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0023c: mov             x1, x0
    // 0xc00240: r0 = Instance_Axis
    //     0xc00240: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc00244: stur            x1, [fp, #-8]
    // 0xc00248: StoreField: r1->field_f = r0
    //     0xc00248: stur            w0, [x1, #0xf]
    // 0xc0024c: r2 = Instance_MainAxisAlignment
    //     0xc0024c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc00250: ldr             x2, [x2, #0xa08]
    // 0xc00254: StoreField: r1->field_13 = r2
    //     0xc00254: stur            w2, [x1, #0x13]
    // 0xc00258: r2 = Instance_MainAxisSize
    //     0xc00258: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc0025c: ldr             x2, [x2, #0xdd0]
    // 0xc00260: ArrayStore: r1[0] = r2  ; List_4
    //     0xc00260: stur            w2, [x1, #0x17]
    // 0xc00264: r2 = Instance_CrossAxisAlignment
    //     0xc00264: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc00268: ldr             x2, [x2, #0x890]
    // 0xc0026c: StoreField: r1->field_1b = r2
    //     0xc0026c: stur            w2, [x1, #0x1b]
    // 0xc00270: r2 = Instance_VerticalDirection
    //     0xc00270: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc00274: ldr             x2, [x2, #0xa20]
    // 0xc00278: StoreField: r1->field_23 = r2
    //     0xc00278: stur            w2, [x1, #0x23]
    // 0xc0027c: r2 = Instance_Clip
    //     0xc0027c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc00280: ldr             x2, [x2, #0x38]
    // 0xc00284: StoreField: r1->field_2b = r2
    //     0xc00284: stur            w2, [x1, #0x2b]
    // 0xc00288: StoreField: r1->field_2f = rZR
    //     0xc00288: stur            xzr, [x1, #0x2f]
    // 0xc0028c: ldur            x2, [fp, #-0x10]
    // 0xc00290: StoreField: r1->field_b = r2
    //     0xc00290: stur            w2, [x1, #0xb]
    // 0xc00294: r0 = SingleChildScrollView()
    //     0xc00294: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xc00298: r1 = Instance_Axis
    //     0xc00298: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0029c: StoreField: r0->field_b = r1
    //     0xc0029c: stur            w1, [x0, #0xb]
    // 0xc002a0: r1 = false
    //     0xc002a0: add             x1, NULL, #0x30  ; false
    // 0xc002a4: StoreField: r0->field_f = r1
    //     0xc002a4: stur            w1, [x0, #0xf]
    // 0xc002a8: ldur            x1, [fp, #-8]
    // 0xc002ac: StoreField: r0->field_23 = r1
    //     0xc002ac: stur            w1, [x0, #0x23]
    // 0xc002b0: r1 = Instance_DragStartBehavior
    //     0xc002b0: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xc002b4: StoreField: r0->field_27 = r1
    //     0xc002b4: stur            w1, [x0, #0x27]
    // 0xc002b8: r1 = Instance_Clip
    //     0xc002b8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc002bc: ldr             x1, [x1, #0x7e0]
    // 0xc002c0: StoreField: r0->field_2b = r1
    //     0xc002c0: stur            w1, [x0, #0x2b]
    // 0xc002c4: r1 = Instance_HitTestBehavior
    //     0xc002c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xc002c8: ldr             x1, [x1, #0x288]
    // 0xc002cc: StoreField: r0->field_2f = r1
    //     0xc002cc: stur            w1, [x0, #0x2f]
    // 0xc002d0: LeaveFrame
    //     0xc002d0: mov             SP, fp
    //     0xc002d4: ldp             fp, lr, [SP], #0x10
    // 0xc002d8: ret
    //     0xc002d8: ret             
    // 0xc002dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc002dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc002e0: b               #0xbff42c
    // 0xc002e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc002fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc002fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3976, size: 0x10, field offset: 0xc
//   const constructor, 
class KnowMoreBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80ef0, size: 0x24
    // 0xc80ef0: EnterFrame
    //     0xc80ef0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80ef4: mov             fp, SP
    // 0xc80ef8: mov             x0, x1
    // 0xc80efc: r1 = <KnowMoreBottomSheet>
    //     0xc80efc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48380] TypeArguments: <KnowMoreBottomSheet>
    //     0xc80f00: ldr             x1, [x1, #0x380]
    // 0xc80f04: r0 = _KnowMoreBottomSheetState()
    //     0xc80f04: bl              #0xc80f14  ; Allocate_KnowMoreBottomSheetStateStub -> _KnowMoreBottomSheetState (size=0x14)
    // 0xc80f08: LeaveFrame
    //     0xc80f08: mov             SP, fp
    //     0xc80f0c: ldp             fp, lr, [SP], #0x10
    // 0xc80f10: ret
    //     0xc80f10: ret             
  }
}
