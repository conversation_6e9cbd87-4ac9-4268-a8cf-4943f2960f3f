// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/delivery_info_widget.dart

// class id: 1049369, size: 0x8
class :: {
}

// class id: 3362, size: 0x14, field offset: 0x14
class _LineShowDeliveryInfoWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb48128, size: 0x584
    // 0xb48128: EnterFrame
    //     0xb48128: stp             fp, lr, [SP, #-0x10]!
    //     0xb4812c: mov             fp, SP
    // 0xb48130: AllocStack(0x60)
    //     0xb48130: sub             SP, SP, #0x60
    // 0xb48134: SetupParameters(_LineShowDeliveryInfoWidgetState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xb48134: mov             x3, x1
    //     0xb48138: mov             x0, x2
    //     0xb4813c: stur            x1, [fp, #-0x10]
    //     0xb48140: stur            x2, [fp, #-0x18]
    // 0xb48144: CheckStackOverflow
    //     0xb48144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb48148: cmp             SP, x16
    //     0xb4814c: b.ls            #0xb48664
    // 0xb48150: LoadField: r1 = r3->field_b
    //     0xb48150: ldur            w1, [x3, #0xb]
    // 0xb48154: DecompressPointer r1
    //     0xb48154: add             x1, x1, HEAP, lsl #32
    // 0xb48158: cmp             w1, NULL
    // 0xb4815c: b.eq            #0xb4866c
    // 0xb48160: LoadField: r2 = r1->field_b
    //     0xb48160: ldur            w2, [x1, #0xb]
    // 0xb48164: DecompressPointer r2
    //     0xb48164: add             x2, x2, HEAP, lsl #32
    // 0xb48168: LoadField: r1 = r2->field_b
    //     0xb48168: ldur            w1, [x2, #0xb]
    // 0xb4816c: DecompressPointer r1
    //     0xb4816c: add             x1, x1, HEAP, lsl #32
    // 0xb48170: cmp             w1, NULL
    // 0xb48174: b.ne            #0xb48180
    // 0xb48178: r0 = Null
    //     0xb48178: mov             x0, NULL
    // 0xb4817c: b               #0xb481f0
    // 0xb48180: LoadField: r2 = r1->field_1b
    //     0xb48180: ldur            w2, [x1, #0x1b]
    // 0xb48184: DecompressPointer r2
    //     0xb48184: add             x2, x2, HEAP, lsl #32
    // 0xb48188: cmp             w2, NULL
    // 0xb4818c: b.ne            #0xb48198
    // 0xb48190: r0 = Null
    //     0xb48190: mov             x0, NULL
    // 0xb48194: b               #0xb481f0
    // 0xb48198: LoadField: r4 = r2->field_f
    //     0xb48198: ldur            w4, [x2, #0xf]
    // 0xb4819c: DecompressPointer r4
    //     0xb4819c: add             x4, x4, HEAP, lsl #32
    // 0xb481a0: stur            x4, [fp, #-8]
    // 0xb481a4: cmp             w4, NULL
    // 0xb481a8: b.ne            #0xb481b4
    // 0xb481ac: r0 = Null
    //     0xb481ac: mov             x0, NULL
    // 0xb481b0: b               #0xb481f0
    // 0xb481b4: r1 = Function '<anonymous closure>':.
    //     0xb481b4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aab8] AnonymousClosure: (0xa0bf40), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_info_widget.dart] _LineShowDeliveryInfoWidgetState::build (0xbb7f38)
    //     0xb481b8: ldr             x1, [x1, #0xab8]
    // 0xb481bc: r2 = Null
    //     0xb481bc: mov             x2, NULL
    // 0xb481c0: r0 = AllocateClosure()
    //     0xb481c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb481c4: r1 = Function '<anonymous closure>':.
    //     0xb481c4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aac0] AnonymousClosure: (0xa0befc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/delivery_info_widget.dart] _LineShowDeliveryInfoWidgetState::build (0xbb7f38)
    //     0xb481c8: ldr             x1, [x1, #0xac0]
    // 0xb481cc: r2 = Null
    //     0xb481cc: mov             x2, NULL
    // 0xb481d0: stur            x0, [fp, #-0x20]
    // 0xb481d4: r0 = AllocateClosure()
    //     0xb481d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb481d8: str             x0, [SP]
    // 0xb481dc: ldur            x1, [fp, #-8]
    // 0xb481e0: ldur            x2, [fp, #-0x20]
    // 0xb481e4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb481e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb481e8: ldr             x4, [x4, #0xb48]
    // 0xb481ec: r0 = firstWhere()
    //     0xb481ec: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb481f0: stur            x0, [fp, #-0x20]
    // 0xb481f4: cmp             w0, NULL
    // 0xb481f8: b.ne            #0xb48204
    // 0xb481fc: r1 = Null
    //     0xb481fc: mov             x1, NULL
    // 0xb48200: b               #0xb48230
    // 0xb48204: LoadField: r1 = r0->field_f
    //     0xb48204: ldur            w1, [x0, #0xf]
    // 0xb48208: DecompressPointer r1
    //     0xb48208: add             x1, x1, HEAP, lsl #32
    // 0xb4820c: cmp             w1, NULL
    // 0xb48210: b.ne            #0xb4821c
    // 0xb48214: r1 = Null
    //     0xb48214: mov             x1, NULL
    // 0xb48218: b               #0xb48230
    // 0xb4821c: LoadField: r2 = r1->field_7
    //     0xb4821c: ldur            w2, [x1, #7]
    // 0xb48220: cbnz            w2, #0xb4822c
    // 0xb48224: r1 = false
    //     0xb48224: add             x1, NULL, #0x30  ; false
    // 0xb48228: b               #0xb48230
    // 0xb4822c: r1 = true
    //     0xb4822c: add             x1, NULL, #0x20  ; true
    // 0xb48230: cmp             w1, NULL
    // 0xb48234: b.eq            #0xb48248
    // 0xb48238: tbnz            w1, #4, #0xb48248
    // 0xb4823c: ldur            x1, [fp, #-0x10]
    // 0xb48240: r2 = true
    //     0xb48240: add             x2, NULL, #0x20  ; true
    // 0xb48244: b               #0xb482cc
    // 0xb48248: ldur            x1, [fp, #-0x10]
    // 0xb4824c: LoadField: r2 = r1->field_b
    //     0xb4824c: ldur            w2, [x1, #0xb]
    // 0xb48250: DecompressPointer r2
    //     0xb48250: add             x2, x2, HEAP, lsl #32
    // 0xb48254: cmp             w2, NULL
    // 0xb48258: b.eq            #0xb48670
    // 0xb4825c: LoadField: r3 = r2->field_b
    //     0xb4825c: ldur            w3, [x2, #0xb]
    // 0xb48260: DecompressPointer r3
    //     0xb48260: add             x3, x3, HEAP, lsl #32
    // 0xb48264: LoadField: r2 = r3->field_b
    //     0xb48264: ldur            w2, [x3, #0xb]
    // 0xb48268: DecompressPointer r2
    //     0xb48268: add             x2, x2, HEAP, lsl #32
    // 0xb4826c: cmp             w2, NULL
    // 0xb48270: b.ne            #0xb4827c
    // 0xb48274: r2 = Null
    //     0xb48274: mov             x2, NULL
    // 0xb48278: b               #0xb482c0
    // 0xb4827c: LoadField: r3 = r2->field_1b
    //     0xb4827c: ldur            w3, [x2, #0x1b]
    // 0xb48280: DecompressPointer r3
    //     0xb48280: add             x3, x3, HEAP, lsl #32
    // 0xb48284: cmp             w3, NULL
    // 0xb48288: b.ne            #0xb48294
    // 0xb4828c: r2 = Null
    //     0xb4828c: mov             x2, NULL
    // 0xb48290: b               #0xb482c0
    // 0xb48294: LoadField: r2 = r3->field_b
    //     0xb48294: ldur            w2, [x3, #0xb]
    // 0xb48298: DecompressPointer r2
    //     0xb48298: add             x2, x2, HEAP, lsl #32
    // 0xb4829c: cmp             w2, NULL
    // 0xb482a0: b.ne            #0xb482ac
    // 0xb482a4: r2 = Null
    //     0xb482a4: mov             x2, NULL
    // 0xb482a8: b               #0xb482c0
    // 0xb482ac: LoadField: r3 = r2->field_7
    //     0xb482ac: ldur            w3, [x2, #7]
    // 0xb482b0: cbnz            w3, #0xb482bc
    // 0xb482b4: r2 = false
    //     0xb482b4: add             x2, NULL, #0x30  ; false
    // 0xb482b8: b               #0xb482c0
    // 0xb482bc: r2 = true
    //     0xb482bc: add             x2, NULL, #0x20  ; true
    // 0xb482c0: cmp             w2, NULL
    // 0xb482c4: b.ne            #0xb482cc
    // 0xb482c8: r2 = false
    //     0xb482c8: add             x2, NULL, #0x30  ; false
    // 0xb482cc: stur            x2, [fp, #-8]
    // 0xb482d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb482d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb482d4: ldr             x0, [x0, #0x1c80]
    //     0xb482d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb482dc: cmp             w0, w16
    //     0xb482e0: b.ne            #0xb482ec
    //     0xb482e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb482e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb482ec: r0 = GetNavigation.size()
    //     0xb482ec: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb482f0: LoadField: d0 = r0->field_f
    //     0xb482f0: ldur            d0, [x0, #0xf]
    // 0xb482f4: d1 = 0.050000
    //     0xb482f4: ldr             d1, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb482f8: fmul            d2, d0, d1
    // 0xb482fc: stur            d2, [fp, #-0x38]
    // 0xb48300: r0 = GetNavigation.size()
    //     0xb48300: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb48304: LoadField: d0 = r0->field_7
    //     0xb48304: ldur            d0, [x0, #7]
    // 0xb48308: stur            d0, [fp, #-0x40]
    // 0xb4830c: r0 = Radius()
    //     0xb4830c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb48310: d0 = 30.000000
    //     0xb48310: fmov            d0, #30.00000000
    // 0xb48314: stur            x0, [fp, #-0x28]
    // 0xb48318: StoreField: r0->field_7 = d0
    //     0xb48318: stur            d0, [x0, #7]
    // 0xb4831c: StoreField: r0->field_f = d0
    //     0xb4831c: stur            d0, [x0, #0xf]
    // 0xb48320: r0 = BorderRadius()
    //     0xb48320: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb48324: mov             x1, x0
    // 0xb48328: ldur            x0, [fp, #-0x28]
    // 0xb4832c: stur            x1, [fp, #-0x30]
    // 0xb48330: StoreField: r1->field_7 = r0
    //     0xb48330: stur            w0, [x1, #7]
    // 0xb48334: StoreField: r1->field_b = r0
    //     0xb48334: stur            w0, [x1, #0xb]
    // 0xb48338: StoreField: r1->field_f = r0
    //     0xb48338: stur            w0, [x1, #0xf]
    // 0xb4833c: StoreField: r1->field_13 = r0
    //     0xb4833c: stur            w0, [x1, #0x13]
    // 0xb48340: r0 = BoxDecoration()
    //     0xb48340: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb48344: mov             x1, x0
    // 0xb48348: r0 = Instance_Color
    //     0xb48348: add             x0, PP, #0x40, lsl #12  ; [pp+0x40860] Obj!Color@d6b0a1
    //     0xb4834c: ldr             x0, [x0, #0x860]
    // 0xb48350: stur            x1, [fp, #-0x28]
    // 0xb48354: StoreField: r1->field_7 = r0
    //     0xb48354: stur            w0, [x1, #7]
    // 0xb48358: ldur            x0, [fp, #-0x30]
    // 0xb4835c: StoreField: r1->field_13 = r0
    //     0xb4835c: stur            w0, [x1, #0x13]
    // 0xb48360: r0 = Instance_BoxShape
    //     0xb48360: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb48364: ldr             x0, [x0, #0x80]
    // 0xb48368: StoreField: r1->field_23 = r0
    //     0xb48368: stur            w0, [x1, #0x23]
    // 0xb4836c: r0 = SvgPicture()
    //     0xb4836c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb48370: stur            x0, [fp, #-0x30]
    // 0xb48374: r16 = Instance_BoxFit
    //     0xb48374: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb48378: ldr             x16, [x16, #0xb18]
    // 0xb4837c: str             x16, [SP]
    // 0xb48380: mov             x1, x0
    // 0xb48384: r2 = "assets/images/free_delivery.svg"
    //     0xb48384: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6aac8] "assets/images/free_delivery.svg"
    //     0xb48388: ldr             x2, [x2, #0xac8]
    // 0xb4838c: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb4838c: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb48390: ldr             x4, [x4, #0xb0]
    // 0xb48394: r0 = SvgPicture.asset()
    //     0xb48394: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb48398: ldur            x0, [fp, #-0x20]
    // 0xb4839c: cmp             w0, NULL
    // 0xb483a0: b.ne            #0xb483ac
    // 0xb483a4: r1 = Null
    //     0xb483a4: mov             x1, NULL
    // 0xb483a8: b               #0xb483d8
    // 0xb483ac: LoadField: r1 = r0->field_f
    //     0xb483ac: ldur            w1, [x0, #0xf]
    // 0xb483b0: DecompressPointer r1
    //     0xb483b0: add             x1, x1, HEAP, lsl #32
    // 0xb483b4: cmp             w1, NULL
    // 0xb483b8: b.ne            #0xb483c4
    // 0xb483bc: r1 = Null
    //     0xb483bc: mov             x1, NULL
    // 0xb483c0: b               #0xb483d8
    // 0xb483c4: LoadField: r2 = r1->field_7
    //     0xb483c4: ldur            w2, [x1, #7]
    // 0xb483c8: cbnz            w2, #0xb483d4
    // 0xb483cc: r1 = false
    //     0xb483cc: add             x1, NULL, #0x30  ; false
    // 0xb483d0: b               #0xb483d8
    // 0xb483d4: r1 = true
    //     0xb483d4: add             x1, NULL, #0x20  ; true
    // 0xb483d8: cmp             w1, NULL
    // 0xb483dc: b.eq            #0xb48410
    // 0xb483e0: tbnz            w1, #4, #0xb48410
    // 0xb483e4: cmp             w0, NULL
    // 0xb483e8: b.ne            #0xb483f4
    // 0xb483ec: r0 = Null
    //     0xb483ec: mov             x0, NULL
    // 0xb483f0: b               #0xb48400
    // 0xb483f4: LoadField: r1 = r0->field_f
    //     0xb483f4: ldur            w1, [x0, #0xf]
    // 0xb483f8: DecompressPointer r1
    //     0xb483f8: add             x1, x1, HEAP, lsl #32
    // 0xb483fc: mov             x0, x1
    // 0xb48400: str             x0, [SP]
    // 0xb48404: r0 = _interpolateSingle()
    //     0xb48404: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb48408: mov             x3, x0
    // 0xb4840c: b               #0xb48474
    // 0xb48410: ldur            x0, [fp, #-0x10]
    // 0xb48414: LoadField: r1 = r0->field_b
    //     0xb48414: ldur            w1, [x0, #0xb]
    // 0xb48418: DecompressPointer r1
    //     0xb48418: add             x1, x1, HEAP, lsl #32
    // 0xb4841c: cmp             w1, NULL
    // 0xb48420: b.eq            #0xb48674
    // 0xb48424: LoadField: r0 = r1->field_b
    //     0xb48424: ldur            w0, [x1, #0xb]
    // 0xb48428: DecompressPointer r0
    //     0xb48428: add             x0, x0, HEAP, lsl #32
    // 0xb4842c: LoadField: r1 = r0->field_b
    //     0xb4842c: ldur            w1, [x0, #0xb]
    // 0xb48430: DecompressPointer r1
    //     0xb48430: add             x1, x1, HEAP, lsl #32
    // 0xb48434: cmp             w1, NULL
    // 0xb48438: b.ne            #0xb48444
    // 0xb4843c: r0 = Null
    //     0xb4843c: mov             x0, NULL
    // 0xb48440: b               #0xb48468
    // 0xb48444: LoadField: r0 = r1->field_1b
    //     0xb48444: ldur            w0, [x1, #0x1b]
    // 0xb48448: DecompressPointer r0
    //     0xb48448: add             x0, x0, HEAP, lsl #32
    // 0xb4844c: cmp             w0, NULL
    // 0xb48450: b.ne            #0xb4845c
    // 0xb48454: r0 = Null
    //     0xb48454: mov             x0, NULL
    // 0xb48458: b               #0xb48468
    // 0xb4845c: LoadField: r1 = r0->field_b
    //     0xb4845c: ldur            w1, [x0, #0xb]
    // 0xb48460: DecompressPointer r1
    //     0xb48460: add             x1, x1, HEAP, lsl #32
    // 0xb48464: mov             x0, x1
    // 0xb48468: str             x0, [SP]
    // 0xb4846c: r0 = _interpolateSingle()
    //     0xb4846c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb48470: mov             x3, x0
    // 0xb48474: ldur            x2, [fp, #-8]
    // 0xb48478: ldur            d1, [fp, #-0x38]
    // 0xb4847c: ldur            x0, [fp, #-0x30]
    // 0xb48480: ldur            d0, [fp, #-0x40]
    // 0xb48484: ldur            x1, [fp, #-0x18]
    // 0xb48488: stur            x3, [fp, #-0x10]
    // 0xb4848c: r0 = of()
    //     0xb4848c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb48490: LoadField: r1 = r0->field_87
    //     0xb48490: ldur            w1, [x0, #0x87]
    // 0xb48494: DecompressPointer r1
    //     0xb48494: add             x1, x1, HEAP, lsl #32
    // 0xb48498: LoadField: r0 = r1->field_2b
    //     0xb48498: ldur            w0, [x1, #0x2b]
    // 0xb4849c: DecompressPointer r0
    //     0xb4849c: add             x0, x0, HEAP, lsl #32
    // 0xb484a0: r16 = Instance_Color
    //     0xb484a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb484a4: ldr             x16, [x16, #0x858]
    // 0xb484a8: r30 = 14.000000
    //     0xb484a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb484ac: ldr             lr, [lr, #0x1d8]
    // 0xb484b0: stp             lr, x16, [SP]
    // 0xb484b4: mov             x1, x0
    // 0xb484b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb484b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb484bc: ldr             x4, [x4, #0x9b8]
    // 0xb484c0: r0 = copyWith()
    //     0xb484c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb484c4: stur            x0, [fp, #-0x18]
    // 0xb484c8: r0 = Text()
    //     0xb484c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb484cc: mov             x3, x0
    // 0xb484d0: ldur            x0, [fp, #-0x10]
    // 0xb484d4: stur            x3, [fp, #-0x20]
    // 0xb484d8: StoreField: r3->field_b = r0
    //     0xb484d8: stur            w0, [x3, #0xb]
    // 0xb484dc: ldur            x0, [fp, #-0x18]
    // 0xb484e0: StoreField: r3->field_13 = r0
    //     0xb484e0: stur            w0, [x3, #0x13]
    // 0xb484e4: r1 = Null
    //     0xb484e4: mov             x1, NULL
    // 0xb484e8: r2 = 6
    //     0xb484e8: movz            x2, #0x6
    // 0xb484ec: r0 = AllocateArray()
    //     0xb484ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb484f0: mov             x2, x0
    // 0xb484f4: ldur            x0, [fp, #-0x30]
    // 0xb484f8: stur            x2, [fp, #-0x10]
    // 0xb484fc: StoreField: r2->field_f = r0
    //     0xb484fc: stur            w0, [x2, #0xf]
    // 0xb48500: r16 = Instance_SizedBox
    //     0xb48500: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0xb48504: ldr             x16, [x16, #0xaa8]
    // 0xb48508: StoreField: r2->field_13 = r16
    //     0xb48508: stur            w16, [x2, #0x13]
    // 0xb4850c: ldur            x0, [fp, #-0x20]
    // 0xb48510: ArrayStore: r2[0] = r0  ; List_4
    //     0xb48510: stur            w0, [x2, #0x17]
    // 0xb48514: r1 = <Widget>
    //     0xb48514: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb48518: r0 = AllocateGrowableArray()
    //     0xb48518: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4851c: mov             x1, x0
    // 0xb48520: ldur            x0, [fp, #-0x10]
    // 0xb48524: stur            x1, [fp, #-0x18]
    // 0xb48528: StoreField: r1->field_f = r0
    //     0xb48528: stur            w0, [x1, #0xf]
    // 0xb4852c: r0 = 6
    //     0xb4852c: movz            x0, #0x6
    // 0xb48530: StoreField: r1->field_b = r0
    //     0xb48530: stur            w0, [x1, #0xb]
    // 0xb48534: r0 = Row()
    //     0xb48534: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb48538: mov             x1, x0
    // 0xb4853c: r0 = Instance_Axis
    //     0xb4853c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb48540: stur            x1, [fp, #-0x20]
    // 0xb48544: StoreField: r1->field_f = r0
    //     0xb48544: stur            w0, [x1, #0xf]
    // 0xb48548: r0 = Instance_MainAxisAlignment
    //     0xb48548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb4854c: ldr             x0, [x0, #0xab0]
    // 0xb48550: StoreField: r1->field_13 = r0
    //     0xb48550: stur            w0, [x1, #0x13]
    // 0xb48554: r0 = Instance_MainAxisSize
    //     0xb48554: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb48558: ldr             x0, [x0, #0xa10]
    // 0xb4855c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4855c: stur            w0, [x1, #0x17]
    // 0xb48560: r0 = Instance_CrossAxisAlignment
    //     0xb48560: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb48564: ldr             x0, [x0, #0xa18]
    // 0xb48568: StoreField: r1->field_1b = r0
    //     0xb48568: stur            w0, [x1, #0x1b]
    // 0xb4856c: r0 = Instance_VerticalDirection
    //     0xb4856c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb48570: ldr             x0, [x0, #0xa20]
    // 0xb48574: StoreField: r1->field_23 = r0
    //     0xb48574: stur            w0, [x1, #0x23]
    // 0xb48578: r0 = Instance_Clip
    //     0xb48578: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4857c: ldr             x0, [x0, #0x38]
    // 0xb48580: StoreField: r1->field_2b = r0
    //     0xb48580: stur            w0, [x1, #0x2b]
    // 0xb48584: StoreField: r1->field_2f = rZR
    //     0xb48584: stur            xzr, [x1, #0x2f]
    // 0xb48588: ldur            x0, [fp, #-0x18]
    // 0xb4858c: StoreField: r1->field_b = r0
    //     0xb4858c: stur            w0, [x1, #0xb]
    // 0xb48590: ldur            d0, [fp, #-0x38]
    // 0xb48594: r0 = inline_Allocate_Double()
    //     0xb48594: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb48598: add             x0, x0, #0x10
    //     0xb4859c: cmp             x2, x0
    //     0xb485a0: b.ls            #0xb48678
    //     0xb485a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb485a8: sub             x0, x0, #0xf
    //     0xb485ac: movz            x2, #0xe15c
    //     0xb485b0: movk            x2, #0x3, lsl #16
    //     0xb485b4: stur            x2, [x0, #-1]
    // 0xb485b8: StoreField: r0->field_7 = d0
    //     0xb485b8: stur            d0, [x0, #7]
    // 0xb485bc: ldur            d0, [fp, #-0x40]
    // 0xb485c0: stur            x0, [fp, #-0x18]
    // 0xb485c4: r2 = inline_Allocate_Double()
    //     0xb485c4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb485c8: add             x2, x2, #0x10
    //     0xb485cc: cmp             x3, x2
    //     0xb485d0: b.ls            #0xb48690
    //     0xb485d4: str             x2, [THR, #0x50]  ; THR::top
    //     0xb485d8: sub             x2, x2, #0xf
    //     0xb485dc: movz            x3, #0xe15c
    //     0xb485e0: movk            x3, #0x3, lsl #16
    //     0xb485e4: stur            x3, [x2, #-1]
    // 0xb485e8: StoreField: r2->field_7 = d0
    //     0xb485e8: stur            d0, [x2, #7]
    // 0xb485ec: stur            x2, [fp, #-0x10]
    // 0xb485f0: r0 = Container()
    //     0xb485f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb485f4: stur            x0, [fp, #-0x30]
    // 0xb485f8: ldur            x16, [fp, #-0x18]
    // 0xb485fc: ldur            lr, [fp, #-0x10]
    // 0xb48600: stp             lr, x16, [SP, #0x10]
    // 0xb48604: ldur            x16, [fp, #-0x28]
    // 0xb48608: ldur            lr, [fp, #-0x20]
    // 0xb4860c: stp             lr, x16, [SP]
    // 0xb48610: mov             x1, x0
    // 0xb48614: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb48614: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb48618: ldr             x4, [x4, #0x8c0]
    // 0xb4861c: r0 = Container()
    //     0xb4861c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb48620: r0 = Visibility()
    //     0xb48620: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb48624: ldur            x1, [fp, #-0x30]
    // 0xb48628: StoreField: r0->field_b = r1
    //     0xb48628: stur            w1, [x0, #0xb]
    // 0xb4862c: r1 = Instance_SizedBox
    //     0xb4862c: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb48630: StoreField: r0->field_f = r1
    //     0xb48630: stur            w1, [x0, #0xf]
    // 0xb48634: ldur            x1, [fp, #-8]
    // 0xb48638: StoreField: r0->field_13 = r1
    //     0xb48638: stur            w1, [x0, #0x13]
    // 0xb4863c: r1 = false
    //     0xb4863c: add             x1, NULL, #0x30  ; false
    // 0xb48640: ArrayStore: r0[0] = r1  ; List_4
    //     0xb48640: stur            w1, [x0, #0x17]
    // 0xb48644: StoreField: r0->field_1b = r1
    //     0xb48644: stur            w1, [x0, #0x1b]
    // 0xb48648: StoreField: r0->field_1f = r1
    //     0xb48648: stur            w1, [x0, #0x1f]
    // 0xb4864c: StoreField: r0->field_23 = r1
    //     0xb4864c: stur            w1, [x0, #0x23]
    // 0xb48650: StoreField: r0->field_27 = r1
    //     0xb48650: stur            w1, [x0, #0x27]
    // 0xb48654: StoreField: r0->field_2b = r1
    //     0xb48654: stur            w1, [x0, #0x2b]
    // 0xb48658: LeaveFrame
    //     0xb48658: mov             SP, fp
    //     0xb4865c: ldp             fp, lr, [SP], #0x10
    // 0xb48660: ret
    //     0xb48660: ret             
    // 0xb48664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb48664: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb48668: b               #0xb48150
    // 0xb4866c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4866c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48670: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb48674: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb48678: SaveReg d0
    //     0xb48678: str             q0, [SP, #-0x10]!
    // 0xb4867c: SaveReg r1
    //     0xb4867c: str             x1, [SP, #-8]!
    // 0xb48680: r0 = AllocateDouble()
    //     0xb48680: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb48684: RestoreReg r1
    //     0xb48684: ldr             x1, [SP], #8
    // 0xb48688: RestoreReg d0
    //     0xb48688: ldr             q0, [SP], #0x10
    // 0xb4868c: b               #0xb485b8
    // 0xb48690: SaveReg d0
    //     0xb48690: str             q0, [SP, #-0x10]!
    // 0xb48694: stp             x0, x1, [SP, #-0x10]!
    // 0xb48698: r0 = AllocateDouble()
    //     0xb48698: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb4869c: mov             x2, x0
    // 0xb486a0: ldp             x0, x1, [SP], #0x10
    // 0xb486a4: RestoreReg d0
    //     0xb486a4: ldr             q0, [SP], #0x10
    // 0xb486a8: b               #0xb485e8
  }
}

// class id: 4101, size: 0x10, field offset: 0xc
class DeliveryInfoWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ec44, size: 0x24
    // 0xc7ec44: EnterFrame
    //     0xc7ec44: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ec48: mov             fp, SP
    // 0xc7ec4c: mov             x0, x1
    // 0xc7ec50: r1 = <DeliveryInfoWidget>
    //     0xc7ec50: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e38] TypeArguments: <DeliveryInfoWidget>
    //     0xc7ec54: ldr             x1, [x1, #0xe38]
    // 0xc7ec58: r0 = _LineShowDeliveryInfoWidgetState()
    //     0xc7ec58: bl              #0xc7ec68  ; Allocate_LineShowDeliveryInfoWidgetStateStub -> _LineShowDeliveryInfoWidgetState (size=0x14)
    // 0xc7ec5c: LeaveFrame
    //     0xc7ec5c: mov             SP, fp
    //     0xc7ec60: ldp             fp, lr, [SP], #0x10
    // 0xc7ec64: ret
    //     0xc7ec64: ret             
  }
}
