// lib: , url: package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart

// class id: 1049060, size: 0x8
class :: {
}

// class id: 1275, size: 0x78, field offset: 0x48
class TestimonialsController extends BaseController {

  late TestimonialRepository repository; // offset: 0x4c

  _ getTestimonials(/* No info */) {
    // ** addr: 0x147a09c, size: 0xf8
    // 0x147a09c: EnterFrame
    //     0x147a09c: stp             fp, lr, [SP, #-0x10]!
    //     0x147a0a0: mov             fp, SP
    // 0x147a0a4: AllocStack(0x40)
    //     0x147a0a4: sub             SP, SP, #0x40
    // 0x147a0a8: SetupParameters(TestimonialsController this /* r1 => r0, fp-0x8 */)
    //     0x147a0a8: mov             x0, x1
    //     0x147a0ac: stur            x1, [fp, #-8]
    // 0x147a0b0: CheckStackOverflow
    //     0x147a0b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147a0b4: cmp             SP, x16
    //     0x147a0b8: b.ls            #0x147a180
    // 0x147a0bc: LoadField: r1 = r0->field_47
    //     0x147a0bc: ldur            w1, [x0, #0x47]
    // 0x147a0c0: DecompressPointer r1
    //     0x147a0c0: add             x1, x1, HEAP, lsl #32
    // 0x147a0c4: LoadField: r2 = r1->field_27
    //     0x147a0c4: ldur            w2, [x1, #0x27]
    // 0x147a0c8: DecompressPointer r2
    //     0x147a0c8: add             x2, x2, HEAP, lsl #32
    // 0x147a0cc: tbz             w2, #4, #0x147a170
    // 0x147a0d0: LoadField: r2 = r1->field_2b
    //     0x147a0d0: ldur            w2, [x1, #0x2b]
    // 0x147a0d4: DecompressPointer r2
    //     0x147a0d4: add             x2, x2, HEAP, lsl #32
    // 0x147a0d8: tbnz            w2, #4, #0x147a170
    // 0x147a0dc: r2 = true
    //     0x147a0dc: add             x2, NULL, #0x20  ; true
    // 0x147a0e0: StoreField: r1->field_27 = r2
    //     0x147a0e0: stur            w2, [x1, #0x27]
    // 0x147a0e4: LoadField: r2 = r0->field_4b
    //     0x147a0e4: ldur            w2, [x0, #0x4b]
    // 0x147a0e8: DecompressPointer r2
    //     0x147a0e8: add             x2, x2, HEAP, lsl #32
    // 0x147a0ec: r16 = Sentinel
    //     0x147a0ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x147a0f0: cmp             w2, w16
    // 0x147a0f4: b.eq            #0x147a188
    // 0x147a0f8: LoadField: r3 = r1->field_f
    //     0x147a0f8: ldur            x3, [x1, #0xf]
    // 0x147a0fc: ArrayLoad: r4 = r1[0]  ; List_8
    //     0x147a0fc: ldur            x4, [x1, #0x17]
    // 0x147a100: mov             x1, x2
    // 0x147a104: mov             x2, x3
    // 0x147a108: mov             x3, x4
    // 0x147a10c: r0 = getTestimonials()
    //     0x147a10c: bl              #0x147a194  ; [package:customer_app/app/data/repositories/testimonial/testimonial_repository_impl.dart] TestimonialRepositoryImpl::getTestimonials
    // 0x147a110: ldur            x2, [fp, #-8]
    // 0x147a114: r1 = Function '_handleResponse@1187233183':.
    //     0x147a114: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e688] AnonymousClosure: (0x147bbcc), in [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::_handleResponse (0x147bc08)
    //     0x147a118: ldr             x1, [x1, #0x688]
    // 0x147a11c: stur            x0, [fp, #-0x10]
    // 0x147a120: r0 = AllocateClosure()
    //     0x147a120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147a124: ldur            x2, [fp, #-8]
    // 0x147a128: r1 = Function '_handleError@1187233183':.
    //     0x147a128: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e690] AnonymousClosure: (0x147bb08), in [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::_handleError (0x147bb44)
    //     0x147a12c: ldr             x1, [x1, #0x690]
    // 0x147a130: stur            x0, [fp, #-0x18]
    // 0x147a134: r0 = AllocateClosure()
    //     0x147a134: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147a138: r16 = <TestimonialsResponse>
    //     0x147a138: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e698] TypeArguments: <TestimonialsResponse>
    //     0x147a13c: ldr             x16, [x16, #0x698]
    // 0x147a140: ldur            lr, [fp, #-8]
    // 0x147a144: stp             lr, x16, [SP, #0x18]
    // 0x147a148: ldur            x16, [fp, #-0x10]
    // 0x147a14c: stp             x0, x16, [SP, #8]
    // 0x147a150: ldur            x16, [fp, #-0x18]
    // 0x147a154: str             x16, [SP]
    // 0x147a158: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x147a158: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x147a15c: r0 = callDataService()
    //     0x147a15c: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x147a160: r0 = Null
    //     0x147a160: mov             x0, NULL
    // 0x147a164: LeaveFrame
    //     0x147a164: mov             SP, fp
    //     0x147a168: ldp             fp, lr, [SP], #0x10
    // 0x147a16c: ret
    //     0x147a16c: ret             
    // 0x147a170: r0 = Null
    //     0x147a170: mov             x0, NULL
    // 0x147a174: LeaveFrame
    //     0x147a174: mov             SP, fp
    //     0x147a178: ldp             fp, lr, [SP], #0x10
    // 0x147a17c: ret
    //     0x147a17c: ret             
    // 0x147a180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147a180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147a184: b               #0x147a0bc
    // 0x147a188: r9 = repository
    //     0x147a188: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e6a0] Field <TestimonialsController.repository>: late (offset: 0x4c)
    //     0x147a18c: ldr             x9, [x9, #0x6a0]
    // 0x147a190: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x147a190: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleError(dynamic, Exception) {
    // ** addr: 0x147bb08, size: 0x3c
    // 0x147bb08: EnterFrame
    //     0x147bb08: stp             fp, lr, [SP, #-0x10]!
    //     0x147bb0c: mov             fp, SP
    // 0x147bb10: ldr             x0, [fp, #0x18]
    // 0x147bb14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x147bb14: ldur            w1, [x0, #0x17]
    // 0x147bb18: DecompressPointer r1
    //     0x147bb18: add             x1, x1, HEAP, lsl #32
    // 0x147bb1c: CheckStackOverflow
    //     0x147bb1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147bb20: cmp             SP, x16
    //     0x147bb24: b.ls            #0x147bb3c
    // 0x147bb28: ldr             x2, [fp, #0x10]
    // 0x147bb2c: r0 = _handleError()
    //     0x147bb2c: bl              #0x147bb44  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::_handleError
    // 0x147bb30: LeaveFrame
    //     0x147bb30: mov             SP, fp
    //     0x147bb34: ldp             fp, lr, [SP], #0x10
    // 0x147bb38: ret
    //     0x147bb38: ret             
    // 0x147bb3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bb3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bb40: b               #0x147bb28
  }
  _ _handleError(/* No info */) {
    // ** addr: 0x147bb44, size: 0x88
    // 0x147bb44: EnterFrame
    //     0x147bb44: stp             fp, lr, [SP, #-0x10]!
    //     0x147bb48: mov             fp, SP
    // 0x147bb4c: AllocStack(0x10)
    //     0x147bb4c: sub             SP, SP, #0x10
    // 0x147bb50: SetupParameters(TestimonialsController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x147bb50: mov             x4, x1
    //     0x147bb54: mov             x3, x2
    //     0x147bb58: stur            x1, [fp, #-8]
    //     0x147bb5c: stur            x2, [fp, #-0x10]
    // 0x147bb60: CheckStackOverflow
    //     0x147bb60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147bb64: cmp             SP, x16
    //     0x147bb68: b.ls            #0x147bbc4
    // 0x147bb6c: mov             x0, x3
    // 0x147bb70: r2 = Null
    //     0x147bb70: mov             x2, NULL
    // 0x147bb74: r1 = Null
    //     0x147bb74: mov             x1, NULL
    // 0x147bb78: r4 = LoadClassIdInstr(r0)
    //     0x147bb78: ldur            x4, [x0, #-1]
    //     0x147bb7c: ubfx            x4, x4, #0xc, #0x14
    // 0x147bb80: r17 = -5018
    //     0x147bb80: movn            x17, #0x1399
    // 0x147bb84: add             x4, x4, x17
    // 0x147bb88: cmp             x4, #8
    // 0x147bb8c: b.ls            #0x147bba0
    // 0x147bb90: r8 = BaseException
    //     0x147bb90: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x147bb94: r3 = Null
    //     0x147bb94: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e6a8] Null
    //     0x147bb98: ldr             x3, [x3, #0x6a8]
    // 0x147bb9c: r0 = DefaultTypeTest()
    //     0x147bb9c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x147bba0: ldur            x0, [fp, #-0x10]
    // 0x147bba4: LoadField: r2 = r0->field_7
    //     0x147bba4: ldur            w2, [x0, #7]
    // 0x147bba8: DecompressPointer r2
    //     0x147bba8: add             x2, x2, HEAP, lsl #32
    // 0x147bbac: ldur            x1, [fp, #-8]
    // 0x147bbb0: r0 = showErrorMessage()
    //     0x147bbb0: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x147bbb4: r0 = Null
    //     0x147bbb4: mov             x0, NULL
    // 0x147bbb8: LeaveFrame
    //     0x147bbb8: mov             SP, fp
    //     0x147bbbc: ldp             fp, lr, [SP], #0x10
    // 0x147bbc0: ret
    //     0x147bbc0: ret             
    // 0x147bbc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bbc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bbc8: b               #0x147bb6c
  }
  [closure] dynamic _handleResponse(dynamic, TestimonialsResponse) {
    // ** addr: 0x147bbcc, size: 0x3c
    // 0x147bbcc: EnterFrame
    //     0x147bbcc: stp             fp, lr, [SP, #-0x10]!
    //     0x147bbd0: mov             fp, SP
    // 0x147bbd4: ldr             x0, [fp, #0x18]
    // 0x147bbd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x147bbd8: ldur            w1, [x0, #0x17]
    // 0x147bbdc: DecompressPointer r1
    //     0x147bbdc: add             x1, x1, HEAP, lsl #32
    // 0x147bbe0: CheckStackOverflow
    //     0x147bbe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147bbe4: cmp             SP, x16
    //     0x147bbe8: b.ls            #0x147bc00
    // 0x147bbec: ldr             x2, [fp, #0x10]
    // 0x147bbf0: r0 = _handleResponse()
    //     0x147bbf0: bl              #0x147bc08  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::_handleResponse
    // 0x147bbf4: LeaveFrame
    //     0x147bbf4: mov             SP, fp
    //     0x147bbf8: ldp             fp, lr, [SP], #0x10
    // 0x147bbfc: ret
    //     0x147bbfc: ret             
    // 0x147bc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bc00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bc04: b               #0x147bbec
  }
  _ _handleResponse(/* No info */) {
    // ** addr: 0x147bc08, size: 0x58
    // 0x147bc08: EnterFrame
    //     0x147bc08: stp             fp, lr, [SP, #-0x10]!
    //     0x147bc0c: mov             fp, SP
    // 0x147bc10: AllocStack(0x8)
    //     0x147bc10: sub             SP, SP, #8
    // 0x147bc14: r0 = false
    //     0x147bc14: add             x0, NULL, #0x30  ; false
    // 0x147bc18: mov             x3, x1
    // 0x147bc1c: stur            x1, [fp, #-8]
    // 0x147bc20: CheckStackOverflow
    //     0x147bc20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147bc24: cmp             SP, x16
    //     0x147bc28: b.ls            #0x147bc58
    // 0x147bc2c: LoadField: r1 = r3->field_47
    //     0x147bc2c: ldur            w1, [x3, #0x47]
    // 0x147bc30: DecompressPointer r1
    //     0x147bc30: add             x1, x1, HEAP, lsl #32
    // 0x147bc34: StoreField: r1->field_27 = r0
    //     0x147bc34: stur            w0, [x1, #0x27]
    // 0x147bc38: mov             x1, x3
    // 0x147bc3c: r0 = headerConfigData=()
    //     0x147bc3c: bl              #0x1311418  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData=
    // 0x147bc40: ldur            x1, [fp, #-8]
    // 0x147bc44: r0 = handleList()
    //     0x147bc44: bl              #0x147bc60  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::handleList
    // 0x147bc48: r0 = Null
    //     0x147bc48: mov             x0, NULL
    // 0x147bc4c: LeaveFrame
    //     0x147bc4c: mov             SP, fp
    //     0x147bc50: ldp             fp, lr, [SP], #0x10
    // 0x147bc54: ret
    //     0x147bc54: ret             
    // 0x147bc58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bc58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bc5c: b               #0x147bc2c
  }
  _ handleList(/* No info */) {
    // ** addr: 0x147bc60, size: 0x19c
    // 0x147bc60: EnterFrame
    //     0x147bc60: stp             fp, lr, [SP, #-0x10]!
    //     0x147bc64: mov             fp, SP
    // 0x147bc68: AllocStack(0x18)
    //     0x147bc68: sub             SP, SP, #0x18
    // 0x147bc6c: SetupParameters(TestimonialsController this /* r1 => r0, fp-0x18 */)
    //     0x147bc6c: mov             x0, x1
    //     0x147bc70: stur            x1, [fp, #-0x18]
    // 0x147bc74: CheckStackOverflow
    //     0x147bc74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147bc78: cmp             SP, x16
    //     0x147bc7c: b.ls            #0x147bdf4
    // 0x147bc80: LoadField: r2 = r0->field_47
    //     0x147bc80: ldur            w2, [x0, #0x47]
    // 0x147bc84: DecompressPointer r2
    //     0x147bc84: add             x2, x2, HEAP, lsl #32
    // 0x147bc88: stur            x2, [fp, #-0x10]
    // 0x147bc8c: LoadField: r3 = r0->field_53
    //     0x147bc8c: ldur            w3, [x0, #0x53]
    // 0x147bc90: DecompressPointer r3
    //     0x147bc90: add             x3, x3, HEAP, lsl #32
    // 0x147bc94: mov             x1, x3
    // 0x147bc98: stur            x3, [fp, #-8]
    // 0x147bc9c: r0 = value()
    //     0x147bc9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147bca0: LoadField: r1 = r0->field_b
    //     0x147bca0: ldur            w1, [x0, #0xb]
    // 0x147bca4: DecompressPointer r1
    //     0x147bca4: add             x1, x1, HEAP, lsl #32
    // 0x147bca8: cmp             w1, NULL
    // 0x147bcac: b.ne            #0x147bcb8
    // 0x147bcb0: r0 = Null
    //     0x147bcb0: mov             x0, NULL
    // 0x147bcb4: b               #0x147bcdc
    // 0x147bcb8: LoadField: r0 = r1->field_b
    //     0x147bcb8: ldur            w0, [x1, #0xb]
    // 0x147bcbc: DecompressPointer r0
    //     0x147bcbc: add             x0, x0, HEAP, lsl #32
    // 0x147bcc0: cmp             w0, NULL
    // 0x147bcc4: b.ne            #0x147bcd0
    // 0x147bcc8: r0 = Null
    //     0x147bcc8: mov             x0, NULL
    // 0x147bccc: b               #0x147bcdc
    // 0x147bcd0: LoadField: r1 = r0->field_7
    //     0x147bcd0: ldur            w1, [x0, #7]
    // 0x147bcd4: DecompressPointer r1
    //     0x147bcd4: add             x1, x1, HEAP, lsl #32
    // 0x147bcd8: mov             x0, x1
    // 0x147bcdc: cmp             w0, NULL
    // 0x147bce0: b.ne            #0x147bcec
    // 0x147bce4: r1 = true
    //     0x147bce4: add             x1, NULL, #0x20  ; true
    // 0x147bce8: b               #0x147bcf0
    // 0x147bcec: mov             x1, x0
    // 0x147bcf0: ldur            x0, [fp, #-0x10]
    // 0x147bcf4: StoreField: r0->field_2b = r1
    //     0x147bcf4: stur            w1, [x0, #0x2b]
    // 0x147bcf8: tbnz            w1, #4, #0x147bd54
    // 0x147bcfc: ldur            x1, [fp, #-8]
    // 0x147bd00: r0 = value()
    //     0x147bd00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147bd04: LoadField: r1 = r0->field_b
    //     0x147bd04: ldur            w1, [x0, #0xb]
    // 0x147bd08: DecompressPointer r1
    //     0x147bd08: add             x1, x1, HEAP, lsl #32
    // 0x147bd0c: cmp             w1, NULL
    // 0x147bd10: b.ne            #0x147bd1c
    // 0x147bd14: r0 = Null
    //     0x147bd14: mov             x0, NULL
    // 0x147bd18: b               #0x147bd24
    // 0x147bd1c: LoadField: r0 = r1->field_7
    //     0x147bd1c: ldur            w0, [x1, #7]
    // 0x147bd20: DecompressPointer r0
    //     0x147bd20: add             x0, x0, HEAP, lsl #32
    // 0x147bd24: cmp             w0, NULL
    // 0x147bd28: b.ne            #0x147bd44
    // 0x147bd2c: r1 = <TestimonialEntity?>
    //     0x147bd2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e6b8] TypeArguments: <TestimonialEntity?>
    //     0x147bd30: ldr             x1, [x1, #0x6b8]
    // 0x147bd34: r2 = 0
    //     0x147bd34: movz            x2, #0
    // 0x147bd38: r0 = AllocateArray()
    //     0x147bd38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x147bd3c: mov             x2, x0
    // 0x147bd40: b               #0x147bd48
    // 0x147bd44: mov             x2, x0
    // 0x147bd48: ldur            x1, [fp, #-0x10]
    // 0x147bd4c: r0 = appendPage()
    //     0x147bd4c: bl              #0x8a9f60  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::appendPage
    // 0x147bd50: b               #0x147bda8
    // 0x147bd54: ldur            x1, [fp, #-8]
    // 0x147bd58: r0 = value()
    //     0x147bd58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147bd5c: LoadField: r1 = r0->field_b
    //     0x147bd5c: ldur            w1, [x0, #0xb]
    // 0x147bd60: DecompressPointer r1
    //     0x147bd60: add             x1, x1, HEAP, lsl #32
    // 0x147bd64: cmp             w1, NULL
    // 0x147bd68: b.ne            #0x147bd74
    // 0x147bd6c: r0 = Null
    //     0x147bd6c: mov             x0, NULL
    // 0x147bd70: b               #0x147bd7c
    // 0x147bd74: LoadField: r0 = r1->field_7
    //     0x147bd74: ldur            w0, [x1, #7]
    // 0x147bd78: DecompressPointer r0
    //     0x147bd78: add             x0, x0, HEAP, lsl #32
    // 0x147bd7c: cmp             w0, NULL
    // 0x147bd80: b.ne            #0x147bd9c
    // 0x147bd84: r1 = <TestimonialEntity?>
    //     0x147bd84: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e6b8] TypeArguments: <TestimonialEntity?>
    //     0x147bd88: ldr             x1, [x1, #0x6b8]
    // 0x147bd8c: r2 = 0
    //     0x147bd8c: movz            x2, #0
    // 0x147bd90: r0 = AllocateArray()
    //     0x147bd90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x147bd94: mov             x2, x0
    // 0x147bd98: b               #0x147bda0
    // 0x147bd9c: mov             x2, x0
    // 0x147bda0: ldur            x1, [fp, #-0x10]
    // 0x147bda4: r0 = appendLastPage()
    //     0x147bda4: bl              #0x8a9f10  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::appendLastPage
    // 0x147bda8: ldur            x3, [fp, #-0x18]
    // 0x147bdac: ldur            x0, [fp, #-0x10]
    // 0x147bdb0: LoadField: r2 = r0->field_b
    //     0x147bdb0: ldur            w2, [x0, #0xb]
    // 0x147bdb4: DecompressPointer r2
    //     0x147bdb4: add             x2, x2, HEAP, lsl #32
    // 0x147bdb8: r1 = <TestimonialEntity?>
    //     0x147bdb8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e6b8] TypeArguments: <TestimonialEntity?>
    //     0x147bdbc: ldr             x1, [x1, #0x6b8]
    // 0x147bdc0: r0 = _GrowableList._ofGrowableList()
    //     0x147bdc0: bl              #0x62add8  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0x147bdc4: mov             x1, x0
    // 0x147bdc8: ldur            x0, [fp, #-0x18]
    // 0x147bdcc: LoadField: r2 = r0->field_57
    //     0x147bdcc: ldur            w2, [x0, #0x57]
    // 0x147bdd0: DecompressPointer r2
    //     0x147bdd0: add             x2, x2, HEAP, lsl #32
    // 0x147bdd4: mov             x16, x1
    // 0x147bdd8: mov             x1, x2
    // 0x147bddc: mov             x2, x16
    // 0x147bde0: r0 = call()
    //     0x147bde0: bl              #0x8a9c54  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::call
    // 0x147bde4: r0 = Null
    //     0x147bde4: mov             x0, NULL
    // 0x147bde8: LeaveFrame
    //     0x147bde8: mov             SP, fp
    //     0x147bdec: ldp             fp, lr, [SP], #0x10
    // 0x147bdf0: ret
    //     0x147bdf0: ret             
    // 0x147bdf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bdf4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bdf8: b               #0x147bc80
  }
  _ onRefreshPage(/* No info */) {
    // ** addr: 0x147be60, size: 0x4c
    // 0x147be60: EnterFrame
    //     0x147be60: stp             fp, lr, [SP, #-0x10]!
    //     0x147be64: mov             fp, SP
    // 0x147be68: AllocStack(0x8)
    //     0x147be68: sub             SP, SP, #8
    // 0x147be6c: SetupParameters(TestimonialsController this /* r1 => r0, fp-0x8 */)
    //     0x147be6c: mov             x0, x1
    //     0x147be70: stur            x1, [fp, #-8]
    // 0x147be74: CheckStackOverflow
    //     0x147be74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147be78: cmp             SP, x16
    //     0x147be7c: b.ls            #0x147bea4
    // 0x147be80: LoadField: r1 = r0->field_47
    //     0x147be80: ldur            w1, [x0, #0x47]
    // 0x147be84: DecompressPointer r1
    //     0x147be84: add             x1, x1, HEAP, lsl #32
    // 0x147be88: r0 = initRefresh()
    //     0x147be88: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x147be8c: ldur            x1, [fp, #-8]
    // 0x147be90: r0 = getTestimonials()
    //     0x147be90: bl              #0x147a09c  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::getTestimonials
    // 0x147be94: r0 = Null
    //     0x147be94: mov             x0, NULL
    // 0x147be98: LeaveFrame
    //     0x147be98: mov             SP, fp
    //     0x147be9c: ldp             fp, lr, [SP], #0x10
    // 0x147bea0: ret
    //     0x147bea0: ret             
    // 0x147bea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147bea4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147bea8: b               #0x147be80
  }
  _ onInit(/* No info */) {
    // ** addr: 0x15c3d38, size: 0x2c4
    // 0x15c3d38: EnterFrame
    //     0x15c3d38: stp             fp, lr, [SP, #-0x10]!
    //     0x15c3d3c: mov             fp, SP
    // 0x15c3d40: AllocStack(0x30)
    //     0x15c3d40: sub             SP, SP, #0x30
    // 0x15c3d44: SetupParameters(TestimonialsController this /* r1 => r1, fp-0x8 */)
    //     0x15c3d44: stur            x1, [fp, #-8]
    // 0x15c3d48: CheckStackOverflow
    //     0x15c3d48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15c3d4c: cmp             SP, x16
    //     0x15c3d50: b.ls            #0x15c3ff4
    // 0x15c3d54: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15c3d54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15c3d58: ldr             x0, [x0, #0x1c80]
    //     0x15c3d5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15c3d60: cmp             w0, w16
    //     0x15c3d64: b.ne            #0x15c3d70
    //     0x15c3d68: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15c3d6c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15c3d70: r0 = GetNavigation.arguments()
    //     0x15c3d70: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x15c3d74: stur            x0, [fp, #-0x10]
    // 0x15c3d78: cmp             w0, NULL
    // 0x15c3d7c: b.eq            #0x15c3f60
    // 0x15c3d80: r16 = "previousScreenSource"
    //     0x15c3d80: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15c3d84: ldr             x16, [x16, #0x448]
    // 0x15c3d88: stp             x16, x0, [SP]
    // 0x15c3d8c: r4 = 0
    //     0x15c3d8c: movz            x4, #0
    // 0x15c3d90: ldr             x0, [SP, #8]
    // 0x15c3d94: r16 = UnlinkedCall_0x613b5c
    //     0x15c3d94: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c498] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15c3d98: add             x16, x16, #0x498
    // 0x15c3d9c: ldp             x5, lr, [x16]
    // 0x15c3da0: blr             lr
    // 0x15c3da4: cmp             w0, NULL
    // 0x15c3da8: b.ne            #0x15c3db4
    // 0x15c3dac: r4 = ""
    //     0x15c3dac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15c3db0: b               #0x15c3db8
    // 0x15c3db4: mov             x4, x0
    // 0x15c3db8: ldur            x3, [fp, #-8]
    // 0x15c3dbc: mov             x0, x4
    // 0x15c3dc0: stur            x4, [fp, #-0x18]
    // 0x15c3dc4: r2 = Null
    //     0x15c3dc4: mov             x2, NULL
    // 0x15c3dc8: r1 = Null
    //     0x15c3dc8: mov             x1, NULL
    // 0x15c3dcc: r4 = 60
    //     0x15c3dcc: movz            x4, #0x3c
    // 0x15c3dd0: branchIfSmi(r0, 0x15c3ddc)
    //     0x15c3dd0: tbz             w0, #0, #0x15c3ddc
    // 0x15c3dd4: r4 = LoadClassIdInstr(r0)
    //     0x15c3dd4: ldur            x4, [x0, #-1]
    //     0x15c3dd8: ubfx            x4, x4, #0xc, #0x14
    // 0x15c3ddc: sub             x4, x4, #0x5e
    // 0x15c3de0: cmp             x4, #1
    // 0x15c3de4: b.ls            #0x15c3df8
    // 0x15c3de8: r8 = String
    //     0x15c3de8: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15c3dec: r3 = Null
    //     0x15c3dec: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c4a8] Null
    //     0x15c3df0: ldr             x3, [x3, #0x4a8]
    // 0x15c3df4: r0 = String()
    //     0x15c3df4: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15c3df8: ldur            x0, [fp, #-0x18]
    // 0x15c3dfc: ldur            x1, [fp, #-8]
    // 0x15c3e00: StoreField: r1->field_5f = r0
    //     0x15c3e00: stur            w0, [x1, #0x5f]
    //     0x15c3e04: ldurb           w16, [x1, #-1]
    //     0x15c3e08: ldurb           w17, [x0, #-1]
    //     0x15c3e0c: and             x16, x17, x16, lsr #2
    //     0x15c3e10: tst             x16, HEAP, lsr #32
    //     0x15c3e14: b.eq            #0x15c3e1c
    //     0x15c3e18: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15c3e1c: ldur            x16, [fp, #-0x10]
    // 0x15c3e20: r30 = "screenSource"
    //     0x15c3e20: add             lr, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x15c3e24: ldr             lr, [lr, #0x450]
    // 0x15c3e28: stp             lr, x16, [SP]
    // 0x15c3e2c: r4 = 0
    //     0x15c3e2c: movz            x4, #0
    // 0x15c3e30: ldr             x0, [SP, #8]
    // 0x15c3e34: r16 = UnlinkedCall_0x613b5c
    //     0x15c3e34: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c4b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15c3e38: add             x16, x16, #0x4b8
    // 0x15c3e3c: ldp             x5, lr, [x16]
    // 0x15c3e40: blr             lr
    // 0x15c3e44: cmp             w0, NULL
    // 0x15c3e48: b.ne            #0x15c3e54
    // 0x15c3e4c: r4 = ""
    //     0x15c3e4c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15c3e50: b               #0x15c3e58
    // 0x15c3e54: mov             x4, x0
    // 0x15c3e58: ldur            x3, [fp, #-8]
    // 0x15c3e5c: mov             x0, x4
    // 0x15c3e60: stur            x4, [fp, #-0x18]
    // 0x15c3e64: r2 = Null
    //     0x15c3e64: mov             x2, NULL
    // 0x15c3e68: r1 = Null
    //     0x15c3e68: mov             x1, NULL
    // 0x15c3e6c: r4 = 60
    //     0x15c3e6c: movz            x4, #0x3c
    // 0x15c3e70: branchIfSmi(r0, 0x15c3e7c)
    //     0x15c3e70: tbz             w0, #0, #0x15c3e7c
    // 0x15c3e74: r4 = LoadClassIdInstr(r0)
    //     0x15c3e74: ldur            x4, [x0, #-1]
    //     0x15c3e78: ubfx            x4, x4, #0xc, #0x14
    // 0x15c3e7c: sub             x4, x4, #0x5e
    // 0x15c3e80: cmp             x4, #1
    // 0x15c3e84: b.ls            #0x15c3e98
    // 0x15c3e88: r8 = String
    //     0x15c3e88: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15c3e8c: r3 = Null
    //     0x15c3e8c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c4c8] Null
    //     0x15c3e90: ldr             x3, [x3, #0x4c8]
    // 0x15c3e94: r0 = String()
    //     0x15c3e94: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15c3e98: ldur            x0, [fp, #-0x18]
    // 0x15c3e9c: ldur            x1, [fp, #-8]
    // 0x15c3ea0: StoreField: r1->field_63 = r0
    //     0x15c3ea0: stur            w0, [x1, #0x63]
    //     0x15c3ea4: ldurb           w16, [x1, #-1]
    //     0x15c3ea8: ldurb           w17, [x0, #-1]
    //     0x15c3eac: and             x16, x17, x16, lsr #2
    //     0x15c3eb0: tst             x16, HEAP, lsr #32
    //     0x15c3eb4: b.eq            #0x15c3ebc
    //     0x15c3eb8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15c3ebc: ldur            x16, [fp, #-0x10]
    // 0x15c3ec0: r30 = "widgetType"
    //     0x15c3ec0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0x15c3ec4: ldr             lr, [lr, #0x338]
    // 0x15c3ec8: stp             lr, x16, [SP]
    // 0x15c3ecc: r4 = 0
    //     0x15c3ecc: movz            x4, #0
    // 0x15c3ed0: ldr             x0, [SP, #8]
    // 0x15c3ed4: r16 = UnlinkedCall_0x613b5c
    //     0x15c3ed4: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c4d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15c3ed8: add             x16, x16, #0x4d8
    // 0x15c3edc: ldp             x5, lr, [x16]
    // 0x15c3ee0: blr             lr
    // 0x15c3ee4: cmp             w0, NULL
    // 0x15c3ee8: b.ne            #0x15c3ef4
    // 0x15c3eec: r4 = ""
    //     0x15c3eec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15c3ef0: b               #0x15c3ef8
    // 0x15c3ef4: mov             x4, x0
    // 0x15c3ef8: ldur            x3, [fp, #-8]
    // 0x15c3efc: mov             x0, x4
    // 0x15c3f00: stur            x4, [fp, #-0x10]
    // 0x15c3f04: r2 = Null
    //     0x15c3f04: mov             x2, NULL
    // 0x15c3f08: r1 = Null
    //     0x15c3f08: mov             x1, NULL
    // 0x15c3f0c: r4 = 60
    //     0x15c3f0c: movz            x4, #0x3c
    // 0x15c3f10: branchIfSmi(r0, 0x15c3f1c)
    //     0x15c3f10: tbz             w0, #0, #0x15c3f1c
    // 0x15c3f14: r4 = LoadClassIdInstr(r0)
    //     0x15c3f14: ldur            x4, [x0, #-1]
    //     0x15c3f18: ubfx            x4, x4, #0xc, #0x14
    // 0x15c3f1c: sub             x4, x4, #0x5e
    // 0x15c3f20: cmp             x4, #1
    // 0x15c3f24: b.ls            #0x15c3f38
    // 0x15c3f28: r8 = String
    //     0x15c3f28: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15c3f2c: r3 = Null
    //     0x15c3f2c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c4e8] Null
    //     0x15c3f30: ldr             x3, [x3, #0x4e8]
    // 0x15c3f34: r0 = String()
    //     0x15c3f34: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15c3f38: ldur            x0, [fp, #-0x10]
    // 0x15c3f3c: ldur            x2, [fp, #-8]
    // 0x15c3f40: StoreField: r2->field_67 = r0
    //     0x15c3f40: stur            w0, [x2, #0x67]
    //     0x15c3f44: ldurb           w16, [x2, #-1]
    //     0x15c3f48: ldurb           w17, [x0, #-1]
    //     0x15c3f4c: and             x16, x17, x16, lsr #2
    //     0x15c3f50: tst             x16, HEAP, lsr #32
    //     0x15c3f54: b.eq            #0x15c3f5c
    //     0x15c3f58: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15c3f5c: b               #0x15c3f64
    // 0x15c3f60: ldur            x2, [fp, #-8]
    // 0x15c3f64: mov             x1, x2
    // 0x15c3f68: r0 = getTestimonials()
    //     0x15c3f68: bl              #0x147a09c  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::getTestimonials
    // 0x15c3f6c: ldur            x1, [fp, #-8]
    // 0x15c3f70: LoadField: r0 = r1->field_5f
    //     0x15c3f70: ldur            w0, [x1, #0x5f]
    // 0x15c3f74: DecompressPointer r0
    //     0x15c3f74: add             x0, x0, HEAP, lsl #32
    // 0x15c3f78: stur            x0, [fp, #-0x18]
    // 0x15c3f7c: LoadField: r2 = r1->field_63
    //     0x15c3f7c: ldur            w2, [x1, #0x63]
    // 0x15c3f80: DecompressPointer r2
    //     0x15c3f80: add             x2, x2, HEAP, lsl #32
    // 0x15c3f84: stur            x2, [fp, #-0x10]
    // 0x15c3f88: r0 = EventData()
    //     0x15c3f88: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x15c3f8c: mov             x1, x0
    // 0x15c3f90: ldur            x0, [fp, #-0x18]
    // 0x15c3f94: stur            x1, [fp, #-0x20]
    // 0x15c3f98: StoreField: r1->field_13 = r0
    //     0x15c3f98: stur            w0, [x1, #0x13]
    // 0x15c3f9c: ldur            x0, [fp, #-0x10]
    // 0x15c3fa0: StoreField: r1->field_87 = r0
    //     0x15c3fa0: stur            w0, [x1, #0x87]
    // 0x15c3fa4: r0 = EventsRequest()
    //     0x15c3fa4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x15c3fa8: mov             x1, x0
    // 0x15c3fac: r0 = "testimonial_page_opened"
    //     0x15c3fac: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c4f8] "testimonial_page_opened"
    //     0x15c3fb0: ldr             x0, [x0, #0x4f8]
    // 0x15c3fb4: StoreField: r1->field_7 = r0
    //     0x15c3fb4: stur            w0, [x1, #7]
    // 0x15c3fb8: ldur            x0, [fp, #-0x20]
    // 0x15c3fbc: StoreField: r1->field_b = r0
    //     0x15c3fbc: stur            w0, [x1, #0xb]
    // 0x15c3fc0: mov             x2, x1
    // 0x15c3fc4: ldur            x1, [fp, #-8]
    // 0x15c3fc8: r0 = postEvents()
    //     0x15c3fc8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x15c3fcc: ldur            x1, [fp, #-8]
    // 0x15c3fd0: r0 = onInit()
    //     0x15c3fd0: bl              #0x158ae60  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::onInit
    // 0x15c3fd4: ldur            x1, [fp, #-8]
    // 0x15c3fd8: r0 = getConfigData()
    //     0x15c3fd8: bl              #0x15c404c  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::getConfigData
    // 0x15c3fdc: ldur            x1, [fp, #-8]
    // 0x15c3fe0: r0 = showSearchOrBag()
    //     0x15c3fe0: bl              #0x15c3ffc  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::showSearchOrBag
    // 0x15c3fe4: r0 = Null
    //     0x15c3fe4: mov             x0, NULL
    // 0x15c3fe8: LeaveFrame
    //     0x15c3fe8: mov             SP, fp
    //     0x15c3fec: ldp             fp, lr, [SP], #0x10
    // 0x15c3ff0: ret
    //     0x15c3ff0: ret             
    // 0x15c3ff4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15c3ff4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15c3ff8: b               #0x15c3d54
  }
  _ showSearchOrBag(/* No info */) {
    // ** addr: 0x15c3ffc, size: 0x50
    // 0x15c3ffc: EnterFrame
    //     0x15c3ffc: stp             fp, lr, [SP, #-0x10]!
    //     0x15c4000: mov             fp, SP
    // 0x15c4004: AllocStack(0x8)
    //     0x15c4004: sub             SP, SP, #8
    // 0x15c4008: SetupParameters(TestimonialsController this /* r1 => r0, fp-0x8 */)
    //     0x15c4008: mov             x0, x1
    //     0x15c400c: stur            x1, [fp, #-8]
    // 0x15c4010: CheckStackOverflow
    //     0x15c4010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15c4014: cmp             SP, x16
    //     0x15c4018: b.ls            #0x15c4044
    // 0x15c401c: mov             x1, x0
    // 0x15c4020: r2 = false
    //     0x15c4020: add             x2, NULL, #0x30  ; false
    // 0x15c4024: r0 = isShowSearch=()
    //     0x15c4024: bl              #0x1591698  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::isShowSearch=
    // 0x15c4028: ldur            x1, [fp, #-8]
    // 0x15c402c: r2 = false
    //     0x15c402c: add             x2, NULL, #0x30  ; false
    // 0x15c4030: r0 = isShowBag=()
    //     0x15c4030: bl              #0x1591658  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::isShowBag=
    // 0x15c4034: r0 = Null
    //     0x15c4034: mov             x0, NULL
    // 0x15c4038: LeaveFrame
    //     0x15c4038: mov             SP, fp
    //     0x15c403c: ldp             fp, lr, [SP], #0x10
    // 0x15c4040: ret
    //     0x15c4040: ret             
    // 0x15c4044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15c4044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15c4048: b               #0x15c401c
  }
  _ getConfigData(/* No info */) async {
    // ** addr: 0x15c404c, size: 0x60
    // 0x15c404c: EnterFrame
    //     0x15c404c: stp             fp, lr, [SP, #-0x10]!
    //     0x15c4050: mov             fp, SP
    // 0x15c4054: AllocStack(0x18)
    //     0x15c4054: sub             SP, SP, #0x18
    // 0x15c4058: SetupParameters(TestimonialsController this /* r1 => r1, fp-0x10 */)
    //     0x15c4058: stur            NULL, [fp, #-8]
    //     0x15c405c: stur            x1, [fp, #-0x10]
    // 0x15c4060: CheckStackOverflow
    //     0x15c4060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15c4064: cmp             SP, x16
    //     0x15c4068: b.ls            #0x15c40a4
    // 0x15c406c: InitAsync() -> Future<void?>
    //     0x15c406c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15c4070: bl              #0x6326e0  ; InitAsyncStub
    // 0x15c4074: ldur            x0, [fp, #-0x10]
    // 0x15c4078: LoadField: r1 = r0->field_4f
    //     0x15c4078: ldur            w1, [x0, #0x4f]
    // 0x15c407c: DecompressPointer r1
    //     0x15c407c: add             x1, x1, HEAP, lsl #32
    // 0x15c4080: r0 = getConfigData()
    //     0x15c4080: bl              #0x8897b0  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getConfigData
    // 0x15c4084: mov             x1, x0
    // 0x15c4088: stur            x1, [fp, #-0x18]
    // 0x15c408c: r0 = Await()
    //     0x15c408c: bl              #0x63248c  ; AwaitStub
    // 0x15c4090: ldur            x1, [fp, #-0x10]
    // 0x15c4094: mov             x2, x0
    // 0x15c4098: r0 = configData=()
    //     0x15c4098: bl              #0x15c40ac  ; [package:customer_app/app/presentation/controllers/testimonials/testimonials_controller.dart] TestimonialsController::configData=
    // 0x15c409c: r0 = Null
    //     0x15c409c: mov             x0, NULL
    // 0x15c40a0: r0 = ReturnAsyncNotFuture()
    //     0x15c40a0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15c40a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15c40a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15c40a8: b               #0x15c406c
  }
  set _ configData=(/* No info */) {
    // ** addr: 0x15c40ac, size: 0x8c
    // 0x15c40ac: EnterFrame
    //     0x15c40ac: stp             fp, lr, [SP, #-0x10]!
    //     0x15c40b0: mov             fp, SP
    // 0x15c40b4: AllocStack(0x10)
    //     0x15c40b4: sub             SP, SP, #0x10
    // 0x15c40b8: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x15c40b8: mov             x3, x2
    //     0x15c40bc: stur            x2, [fp, #-0x10]
    // 0x15c40c0: CheckStackOverflow
    //     0x15c40c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15c40c4: cmp             SP, x16
    //     0x15c40c8: b.ls            #0x15c4130
    // 0x15c40cc: LoadField: r4 = r1->field_5b
    //     0x15c40cc: ldur            w4, [x1, #0x5b]
    // 0x15c40d0: DecompressPointer r4
    //     0x15c40d0: add             x4, x4, HEAP, lsl #32
    // 0x15c40d4: mov             x0, x3
    // 0x15c40d8: stur            x4, [fp, #-8]
    // 0x15c40dc: r2 = Null
    //     0x15c40dc: mov             x2, NULL
    // 0x15c40e0: r1 = Null
    //     0x15c40e0: mov             x1, NULL
    // 0x15c40e4: r4 = 60
    //     0x15c40e4: movz            x4, #0x3c
    // 0x15c40e8: branchIfSmi(r0, 0x15c40f4)
    //     0x15c40e8: tbz             w0, #0, #0x15c40f4
    // 0x15c40ec: r4 = LoadClassIdInstr(r0)
    //     0x15c40ec: ldur            x4, [x0, #-1]
    //     0x15c40f0: ubfx            x4, x4, #0xc, #0x14
    // 0x15c40f4: r17 = 5478
    //     0x15c40f4: movz            x17, #0x1566
    // 0x15c40f8: cmp             x4, x17
    // 0x15c40fc: b.eq            #0x15c4114
    // 0x15c4100: r8 = LocalConfigData
    //     0x15c4100: add             x8, PP, #0x11, lsl #12  ; [pp+0x11e90] Type: LocalConfigData
    //     0x15c4104: ldr             x8, [x8, #0xe90]
    // 0x15c4108: r3 = Null
    //     0x15c4108: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c500] Null
    //     0x15c410c: ldr             x3, [x3, #0x500]
    // 0x15c4110: r0 = LocalConfigData()
    //     0x15c4110: bl              #0x88ff18  ; IsType_LocalConfigData_Stub
    // 0x15c4114: ldur            x1, [fp, #-8]
    // 0x15c4118: ldur            x2, [fp, #-0x10]
    // 0x15c411c: r0 = value=()
    //     0x15c411c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15c4120: ldur            x0, [fp, #-0x10]
    // 0x15c4124: LeaveFrame
    //     0x15c4124: mov             SP, fp
    //     0x15c4128: ldp             fp, lr, [SP], #0x10
    // 0x15c412c: ret
    //     0x15c412c: ret             
    // 0x15c4130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15c4130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15c4134: b               #0x15c40cc
  }
  _ TestimonialsController(/* No info */) {
    // ** addr: 0x1607a38, size: 0x2e4
    // 0x1607a38: EnterFrame
    //     0x1607a38: stp             fp, lr, [SP, #-0x10]!
    //     0x1607a3c: mov             fp, SP
    // 0x1607a40: AllocStack(0x28)
    //     0x1607a40: sub             SP, SP, #0x28
    // 0x1607a44: r2 = Sentinel
    //     0x1607a44: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1607a48: r0 = ""
    //     0x1607a48: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1607a4c: mov             x3, x1
    // 0x1607a50: stur            x1, [fp, #-8]
    // 0x1607a54: CheckStackOverflow
    //     0x1607a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1607a58: cmp             SP, x16
    //     0x1607a5c: b.ls            #0x1607d14
    // 0x1607a60: StoreField: r3->field_4b = r2
    //     0x1607a60: stur            w2, [x3, #0x4b]
    // 0x1607a64: StoreField: r3->field_5f = r0
    //     0x1607a64: stur            w0, [x3, #0x5f]
    // 0x1607a68: StoreField: r3->field_63 = r0
    //     0x1607a68: stur            w0, [x3, #0x63]
    // 0x1607a6c: StoreField: r3->field_67 = r0
    //     0x1607a6c: stur            w0, [x3, #0x67]
    // 0x1607a70: r1 = <TestimonialEntity?>
    //     0x1607a70: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e6b8] TypeArguments: <TestimonialEntity?>
    //     0x1607a74: ldr             x1, [x1, #0x6b8]
    // 0x1607a78: r0 = PagingController()
    //     0x1607a78: bl              #0x12df82c  ; AllocatePagingControllerStub -> PagingController<X0> (size=0x30)
    // 0x1607a7c: mov             x1, x0
    // 0x1607a80: stur            x0, [fp, #-0x10]
    // 0x1607a84: r0 = PagingController()
    //     0x1607a84: bl              #0x12df7a4  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::PagingController
    // 0x1607a88: ldur            x0, [fp, #-0x10]
    // 0x1607a8c: ldur            x1, [fp, #-8]
    // 0x1607a90: StoreField: r1->field_47 = r0
    //     0x1607a90: stur            w0, [x1, #0x47]
    //     0x1607a94: ldurb           w16, [x1, #-1]
    //     0x1607a98: ldurb           w17, [x0, #-1]
    //     0x1607a9c: and             x16, x17, x16, lsr #2
    //     0x1607aa0: tst             x16, HEAP, lsr #32
    //     0x1607aa4: b.eq            #0x1607aac
    //     0x1607aa8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1607aac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1607aac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1607ab0: ldr             x0, [x0, #0x1c80]
    //     0x1607ab4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1607ab8: cmp             w0, w16
    //     0x1607abc: b.ne            #0x1607ac8
    //     0x1607ac0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1607ac4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1607ac8: r0 = GetNavigation.arguments()
    //     0x1607ac8: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x1607acc: r16 = PreferenceManager
    //     0x1607acc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1607ad0: ldr             x16, [x16, #0x878]
    // 0x1607ad4: str             x16, [SP]
    // 0x1607ad8: r0 = toString()
    //     0x1607ad8: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1607adc: r16 = <PreferenceManager>
    //     0x1607adc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1607ae0: ldr             x16, [x16, #0x880]
    // 0x1607ae4: stp             x0, x16, [SP]
    // 0x1607ae8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1607ae8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1607aec: r0 = Inst.find()
    //     0x1607aec: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1607af0: ldur            x1, [fp, #-8]
    // 0x1607af4: StoreField: r1->field_4f = r0
    //     0x1607af4: stur            w0, [x1, #0x4f]
    //     0x1607af8: ldurb           w16, [x1, #-1]
    //     0x1607afc: ldurb           w17, [x0, #-1]
    //     0x1607b00: and             x16, x17, x16, lsr #2
    //     0x1607b04: tst             x16, HEAP, lsr #32
    //     0x1607b08: b.eq            #0x1607b10
    //     0x1607b0c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1607b10: r0 = TestimonialsResponse()
    //     0x1607b10: bl              #0x147bafc  ; AllocateTestimonialsResponseStub -> TestimonialsResponse (size=0x10)
    // 0x1607b14: r16 = <TestimonialsResponse>
    //     0x1607b14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e698] TypeArguments: <TestimonialsResponse>
    //     0x1607b18: ldr             x16, [x16, #0x698]
    // 0x1607b1c: stp             x0, x16, [SP]
    // 0x1607b20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1607b20: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1607b24: r0 = RxT.obs()
    //     0x1607b24: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1607b28: ldur            x2, [fp, #-8]
    // 0x1607b2c: StoreField: r2->field_53 = r0
    //     0x1607b2c: stur            w0, [x2, #0x53]
    //     0x1607b30: ldurb           w16, [x2, #-1]
    //     0x1607b34: ldurb           w17, [x0, #-1]
    //     0x1607b38: and             x16, x17, x16, lsr #2
    //     0x1607b3c: tst             x16, HEAP, lsr #32
    //     0x1607b40: b.eq            #0x1607b48
    //     0x1607b44: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1607b48: r1 = <TestimonialEntity?>
    //     0x1607b48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e6b8] TypeArguments: <TestimonialEntity?>
    //     0x1607b4c: ldr             x1, [x1, #0x6b8]
    // 0x1607b50: r0 = RxList.empty()
    //     0x1607b50: bl              #0x12df608  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList.empty
    // 0x1607b54: ldur            x1, [fp, #-8]
    // 0x1607b58: StoreField: r1->field_57 = r0
    //     0x1607b58: stur            w0, [x1, #0x57]
    //     0x1607b5c: ldurb           w16, [x1, #-1]
    //     0x1607b60: ldurb           w17, [x0, #-1]
    //     0x1607b64: and             x16, x17, x16, lsr #2
    //     0x1607b68: tst             x16, HEAP, lsr #32
    //     0x1607b6c: b.eq            #0x1607b74
    //     0x1607b70: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1607b74: r0 = LocalConfigData()
    //     0x1607b74: bl              #0x8933d8  ; AllocateLocalConfigDataStub -> LocalConfigData (size=0x80)
    // 0x1607b78: r16 = <LocalConfigData>
    //     0x1607b78: add             x16, PP, #0xa, lsl #12  ; [pp+0xab30] TypeArguments: <LocalConfigData>
    //     0x1607b7c: ldr             x16, [x16, #0xb30]
    // 0x1607b80: stp             x0, x16, [SP]
    // 0x1607b84: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1607b84: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1607b88: r0 = RxT.obs()
    //     0x1607b88: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1607b8c: ldur            x1, [fp, #-8]
    // 0x1607b90: StoreField: r1->field_5b = r0
    //     0x1607b90: stur            w0, [x1, #0x5b]
    //     0x1607b94: ldurb           w16, [x1, #-1]
    //     0x1607b98: ldurb           w17, [x0, #-1]
    //     0x1607b9c: and             x16, x17, x16, lsr #2
    //     0x1607ba0: tst             x16, HEAP, lsr #32
    //     0x1607ba4: b.eq            #0x1607bac
    //     0x1607ba8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1607bac: r16 = ConnectionController
    //     0x1607bac: add             x16, PP, #0xa, lsl #12  ; [pp+0xaaf8] Type: ConnectionController
    //     0x1607bb0: ldr             x16, [x16, #0xaf8]
    // 0x1607bb4: str             x16, [SP]
    // 0x1607bb8: r0 = toString()
    //     0x1607bb8: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1607bbc: r16 = <ConnectionController>
    //     0x1607bbc: add             x16, PP, #0xa, lsl #12  ; [pp+0xab00] TypeArguments: <ConnectionController>
    //     0x1607bc0: ldr             x16, [x16, #0xb00]
    // 0x1607bc4: stp             x0, x16, [SP]
    // 0x1607bc8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1607bc8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1607bcc: r0 = Inst.find()
    //     0x1607bcc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1607bd0: r1 = false
    //     0x1607bd0: add             x1, NULL, #0x30  ; false
    // 0x1607bd4: r0 = BoolExtension.obs()
    //     0x1607bd4: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1607bd8: ldur            x2, [fp, #-8]
    // 0x1607bdc: StoreField: r2->field_6b = r0
    //     0x1607bdc: stur            w0, [x2, #0x6b]
    //     0x1607be0: ldurb           w16, [x2, #-1]
    //     0x1607be4: ldurb           w17, [x0, #-1]
    //     0x1607be8: and             x16, x17, x16, lsr #2
    //     0x1607bec: tst             x16, HEAP, lsr #32
    //     0x1607bf0: b.eq            #0x1607bf8
    //     0x1607bf4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1607bf8: r1 = false
    //     0x1607bf8: add             x1, NULL, #0x30  ; false
    // 0x1607bfc: r0 = BoolExtension.obs()
    //     0x1607bfc: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1607c00: ldur            x2, [fp, #-8]
    // 0x1607c04: StoreField: r2->field_6f = r0
    //     0x1607c04: stur            w0, [x2, #0x6f]
    //     0x1607c08: ldurb           w16, [x2, #-1]
    //     0x1607c0c: ldurb           w17, [x0, #-1]
    //     0x1607c10: and             x16, x17, x16, lsr #2
    //     0x1607c14: tst             x16, HEAP, lsr #32
    //     0x1607c18: b.eq            #0x1607c20
    //     0x1607c1c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1607c20: r1 = <int?, int>
    //     0x1607c20: add             x1, PP, #0xa, lsl #12  ; [pp+0xac40] TypeArguments: <int?, int>
    //     0x1607c24: ldr             x1, [x1, #0xc40]
    // 0x1607c28: r0 = Rxn()
    //     0x1607c28: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x1607c2c: mov             x2, x0
    // 0x1607c30: r0 = Sentinel
    //     0x1607c30: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1607c34: stur            x2, [fp, #-0x10]
    // 0x1607c38: StoreField: r2->field_13 = r0
    //     0x1607c38: stur            w0, [x2, #0x13]
    // 0x1607c3c: r0 = true
    //     0x1607c3c: add             x0, NULL, #0x20  ; true
    // 0x1607c40: ArrayStore: r2[0] = r0  ; List_4
    //     0x1607c40: stur            w0, [x2, #0x17]
    // 0x1607c44: mov             x1, x2
    // 0x1607c48: r0 = RxNotifier()
    //     0x1607c48: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1607c4c: ldur            x0, [fp, #-0x10]
    // 0x1607c50: StoreField: r0->field_13 = rNULL
    //     0x1607c50: stur            NULL, [x0, #0x13]
    // 0x1607c54: ldur            x2, [fp, #-8]
    // 0x1607c58: StoreField: r2->field_73 = r0
    //     0x1607c58: stur            w0, [x2, #0x73]
    //     0x1607c5c: ldurb           w16, [x2, #-1]
    //     0x1607c60: ldurb           w17, [x0, #-1]
    //     0x1607c64: and             x16, x17, x16, lsr #2
    //     0x1607c68: tst             x16, HEAP, lsr #32
    //     0x1607c6c: b.eq            #0x1607c74
    //     0x1607c70: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1607c74: mov             x1, x2
    // 0x1607c78: r0 = BaseController()
    //     0x1607c78: bl              #0x12c396c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::BaseController
    // 0x1607c7c: r16 = TestimonialRepository
    //     0x1607c7c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb0f0] Type: TestimonialRepository
    //     0x1607c80: ldr             x16, [x16, #0xf0]
    // 0x1607c84: str             x16, [SP]
    // 0x1607c88: r0 = toString()
    //     0x1607c88: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1607c8c: r1 = Function '<anonymous closure>':.
    //     0x1607c8c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49420] AnonymousClosure: (0x1607d1c), in [package:customer_app/app/bindings/repository_bindings.dart] RepositoryBindings::dependencies (0x1608434)
    //     0x1607c90: ldr             x1, [x1, #0x420]
    // 0x1607c94: r2 = Null
    //     0x1607c94: mov             x2, NULL
    // 0x1607c98: stur            x0, [fp, #-0x10]
    // 0x1607c9c: r0 = AllocateClosure()
    //     0x1607c9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1607ca0: r16 = <TestimonialRepository>
    //     0x1607ca0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb100] TypeArguments: <TestimonialRepository>
    //     0x1607ca4: ldr             x16, [x16, #0x100]
    // 0x1607ca8: stp             x0, x16, [SP, #8]
    // 0x1607cac: ldur            x16, [fp, #-0x10]
    // 0x1607cb0: str             x16, [SP]
    // 0x1607cb4: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x1607cb4: add             x4, PP, #0xa, lsl #12  ; [pp+0xaac8] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x1607cb8: ldr             x4, [x4, #0xac8]
    // 0x1607cbc: r0 = Inst.lazyPut()
    //     0x1607cbc: bl              #0x12c3844  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x1607cc0: r16 = TestimonialRepository
    //     0x1607cc0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb0f0] Type: TestimonialRepository
    //     0x1607cc4: ldr             x16, [x16, #0xf0]
    // 0x1607cc8: str             x16, [SP]
    // 0x1607ccc: r0 = toString()
    //     0x1607ccc: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1607cd0: r16 = <TestimonialRepository>
    //     0x1607cd0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb100] TypeArguments: <TestimonialRepository>
    //     0x1607cd4: ldr             x16, [x16, #0x100]
    // 0x1607cd8: stp             x0, x16, [SP]
    // 0x1607cdc: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1607cdc: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1607ce0: r0 = Inst.find()
    //     0x1607ce0: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1607ce4: ldur            x1, [fp, #-8]
    // 0x1607ce8: StoreField: r1->field_4b = r0
    //     0x1607ce8: stur            w0, [x1, #0x4b]
    //     0x1607cec: ldurb           w16, [x1, #-1]
    //     0x1607cf0: ldurb           w17, [x0, #-1]
    //     0x1607cf4: and             x16, x17, x16, lsr #2
    //     0x1607cf8: tst             x16, HEAP, lsr #32
    //     0x1607cfc: b.eq            #0x1607d04
    //     0x1607d00: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1607d04: r0 = Null
    //     0x1607d04: mov             x0, NULL
    // 0x1607d08: LeaveFrame
    //     0x1607d08: mov             SP, fp
    //     0x1607d0c: ldp             fp, lr, [SP], #0x10
    // 0x1607d10: ret
    //     0x1607d10: ret             
    // 0x1607d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1607d14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1607d18: b               #0x1607a60
  }
}
