// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart

// class id: 1049405, size: 0x8
class :: {
}

// class id: 3333, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb69e28, size: 0x64
    // 0xb69e28: EnterFrame
    //     0xb69e28: stp             fp, lr, [SP, #-0x10]!
    //     0xb69e2c: mov             fp, SP
    // 0xb69e30: AllocStack(0x18)
    //     0xb69e30: sub             SP, SP, #0x18
    // 0xb69e34: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb69e34: stur            x1, [fp, #-8]
    //     0xb69e38: stur            x2, [fp, #-0x10]
    // 0xb69e3c: r1 = 2
    //     0xb69e3c: movz            x1, #0x2
    // 0xb69e40: r0 = AllocateContext()
    //     0xb69e40: bl              #0x16f6108  ; AllocateContextStub
    // 0xb69e44: mov             x1, x0
    // 0xb69e48: ldur            x0, [fp, #-8]
    // 0xb69e4c: stur            x1, [fp, #-0x18]
    // 0xb69e50: StoreField: r1->field_f = r0
    //     0xb69e50: stur            w0, [x1, #0xf]
    // 0xb69e54: ldur            x0, [fp, #-0x10]
    // 0xb69e58: StoreField: r1->field_13 = r0
    //     0xb69e58: stur            w0, [x1, #0x13]
    // 0xb69e5c: r0 = Obx()
    //     0xb69e5c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb69e60: ldur            x2, [fp, #-0x18]
    // 0xb69e64: r1 = Function '<anonymous closure>':.
    //     0xb69e64: add             x1, PP, #0x55, lsl #12  ; [pp+0x55be8] AnonymousClosure: (0xb69eac), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb69e28)
    //     0xb69e68: ldr             x1, [x1, #0xbe8]
    // 0xb69e6c: stur            x0, [fp, #-8]
    // 0xb69e70: r0 = AllocateClosure()
    //     0xb69e70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69e74: mov             x1, x0
    // 0xb69e78: ldur            x0, [fp, #-8]
    // 0xb69e7c: StoreField: r0->field_b = r1
    //     0xb69e7c: stur            w1, [x0, #0xb]
    // 0xb69e80: LeaveFrame
    //     0xb69e80: mov             SP, fp
    //     0xb69e84: ldp             fp, lr, [SP], #0x10
    // 0xb69e88: ret
    //     0xb69e88: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xb69eac, size: 0x868
    // 0xb69eac: EnterFrame
    //     0xb69eac: stp             fp, lr, [SP, #-0x10]!
    //     0xb69eb0: mov             fp, SP
    // 0xb69eb4: AllocStack(0x78)
    //     0xb69eb4: sub             SP, SP, #0x78
    // 0xb69eb8: SetupParameters()
    //     0xb69eb8: ldr             x0, [fp, #0x10]
    //     0xb69ebc: ldur            w3, [x0, #0x17]
    //     0xb69ec0: add             x3, x3, HEAP, lsl #32
    //     0xb69ec4: stur            x3, [fp, #-0x10]
    // 0xb69ec8: CheckStackOverflow
    //     0xb69ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69ecc: cmp             SP, x16
    //     0xb69ed0: b.ls            #0xb6a6d8
    // 0xb69ed4: LoadField: r0 = r3->field_f
    //     0xb69ed4: ldur            w0, [x3, #0xf]
    // 0xb69ed8: DecompressPointer r0
    //     0xb69ed8: add             x0, x0, HEAP, lsl #32
    // 0xb69edc: LoadField: r1 = r0->field_b
    //     0xb69edc: ldur            w1, [x0, #0xb]
    // 0xb69ee0: DecompressPointer r1
    //     0xb69ee0: add             x1, x1, HEAP, lsl #32
    // 0xb69ee4: cmp             w1, NULL
    // 0xb69ee8: b.eq            #0xb6a6e0
    // 0xb69eec: LoadField: r0 = r1->field_13
    //     0xb69eec: ldur            w0, [x1, #0x13]
    // 0xb69ef0: DecompressPointer r0
    //     0xb69ef0: add             x0, x0, HEAP, lsl #32
    // 0xb69ef4: LoadField: r1 = r0->field_7
    //     0xb69ef4: ldur            w1, [x0, #7]
    // 0xb69ef8: DecompressPointer r1
    //     0xb69ef8: add             x1, x1, HEAP, lsl #32
    // 0xb69efc: cmp             w1, NULL
    // 0xb69f00: b.ne            #0xb69f10
    // 0xb69f04: r0 = Instance_TitleAlignment
    //     0xb69f04: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb69f08: ldr             x0, [x0, #0x518]
    // 0xb69f0c: b               #0xb69f14
    // 0xb69f10: mov             x0, x1
    // 0xb69f14: r16 = Instance_TitleAlignment
    //     0xb69f14: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb69f18: ldr             x16, [x16, #0x520]
    // 0xb69f1c: cmp             w0, w16
    // 0xb69f20: b.ne            #0xb69f30
    // 0xb69f24: r0 = Instance_CrossAxisAlignment
    //     0xb69f24: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb69f28: ldr             x0, [x0, #0xc68]
    // 0xb69f2c: b               #0xb69f54
    // 0xb69f30: r16 = Instance_TitleAlignment
    //     0xb69f30: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb69f34: ldr             x16, [x16, #0x518]
    // 0xb69f38: cmp             w0, w16
    // 0xb69f3c: b.ne            #0xb69f4c
    // 0xb69f40: r0 = Instance_CrossAxisAlignment
    //     0xb69f40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb69f44: ldr             x0, [x0, #0x890]
    // 0xb69f48: b               #0xb69f54
    // 0xb69f4c: r0 = Instance_CrossAxisAlignment
    //     0xb69f4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb69f50: ldr             x0, [x0, #0xa18]
    // 0xb69f54: stur            x0, [fp, #-8]
    // 0xb69f58: r1 = <Widget>
    //     0xb69f58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb69f5c: r2 = 0
    //     0xb69f5c: movz            x2, #0
    // 0xb69f60: r0 = _GrowableList()
    //     0xb69f60: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb69f64: mov             x1, x0
    // 0xb69f68: ldur            x2, [fp, #-0x10]
    // 0xb69f6c: stur            x1, [fp, #-0x18]
    // 0xb69f70: LoadField: r0 = r2->field_f
    //     0xb69f70: ldur            w0, [x2, #0xf]
    // 0xb69f74: DecompressPointer r0
    //     0xb69f74: add             x0, x0, HEAP, lsl #32
    // 0xb69f78: LoadField: r3 = r0->field_b
    //     0xb69f78: ldur            w3, [x0, #0xb]
    // 0xb69f7c: DecompressPointer r3
    //     0xb69f7c: add             x3, x3, HEAP, lsl #32
    // 0xb69f80: cmp             w3, NULL
    // 0xb69f84: b.eq            #0xb6a6e4
    // 0xb69f88: LoadField: r0 = r3->field_f
    //     0xb69f88: ldur            w0, [x3, #0xf]
    // 0xb69f8c: DecompressPointer r0
    //     0xb69f8c: add             x0, x0, HEAP, lsl #32
    // 0xb69f90: LoadField: r3 = r0->field_7
    //     0xb69f90: ldur            w3, [x0, #7]
    // 0xb69f94: cbz             w3, #0xb6a138
    // 0xb69f98: r3 = LoadClassIdInstr(r0)
    //     0xb69f98: ldur            x3, [x0, #-1]
    //     0xb69f9c: ubfx            x3, x3, #0xc, #0x14
    // 0xb69fa0: str             x0, [SP]
    // 0xb69fa4: mov             x0, x3
    // 0xb69fa8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb69fa8: sub             lr, x0, #1, lsl #12
    //     0xb69fac: ldr             lr, [x21, lr, lsl #3]
    //     0xb69fb0: blr             lr
    // 0xb69fb4: ldur            x2, [fp, #-0x10]
    // 0xb69fb8: stur            x0, [fp, #-0x28]
    // 0xb69fbc: LoadField: r1 = r2->field_f
    //     0xb69fbc: ldur            w1, [x2, #0xf]
    // 0xb69fc0: DecompressPointer r1
    //     0xb69fc0: add             x1, x1, HEAP, lsl #32
    // 0xb69fc4: LoadField: r3 = r1->field_b
    //     0xb69fc4: ldur            w3, [x1, #0xb]
    // 0xb69fc8: DecompressPointer r3
    //     0xb69fc8: add             x3, x3, HEAP, lsl #32
    // 0xb69fcc: cmp             w3, NULL
    // 0xb69fd0: b.eq            #0xb6a6e8
    // 0xb69fd4: LoadField: r1 = r3->field_13
    //     0xb69fd4: ldur            w1, [x3, #0x13]
    // 0xb69fd8: DecompressPointer r1
    //     0xb69fd8: add             x1, x1, HEAP, lsl #32
    // 0xb69fdc: LoadField: r3 = r1->field_7
    //     0xb69fdc: ldur            w3, [x1, #7]
    // 0xb69fe0: DecompressPointer r3
    //     0xb69fe0: add             x3, x3, HEAP, lsl #32
    // 0xb69fe4: cmp             w3, NULL
    // 0xb69fe8: b.ne            #0xb69ff8
    // 0xb69fec: r1 = Instance_TitleAlignment
    //     0xb69fec: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb69ff0: ldr             x1, [x1, #0x518]
    // 0xb69ff4: b               #0xb69ffc
    // 0xb69ff8: mov             x1, x3
    // 0xb69ffc: r16 = Instance_TitleAlignment
    //     0xb69ffc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb6a000: ldr             x16, [x16, #0x520]
    // 0xb6a004: cmp             w1, w16
    // 0xb6a008: b.ne            #0xb6a014
    // 0xb6a00c: r4 = Instance_TextAlign
    //     0xb6a00c: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb6a010: b               #0xb6a030
    // 0xb6a014: r16 = Instance_TitleAlignment
    //     0xb6a014: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb6a018: ldr             x16, [x16, #0x518]
    // 0xb6a01c: cmp             w1, w16
    // 0xb6a020: b.ne            #0xb6a02c
    // 0xb6a024: r4 = Instance_TextAlign
    //     0xb6a024: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb6a028: b               #0xb6a030
    // 0xb6a02c: r4 = Instance_TextAlign
    //     0xb6a02c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb6a030: ldur            x3, [fp, #-0x18]
    // 0xb6a034: stur            x4, [fp, #-0x20]
    // 0xb6a038: LoadField: r1 = r2->field_13
    //     0xb6a038: ldur            w1, [x2, #0x13]
    // 0xb6a03c: DecompressPointer r1
    //     0xb6a03c: add             x1, x1, HEAP, lsl #32
    // 0xb6a040: r0 = of()
    //     0xb6a040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6a044: LoadField: r1 = r0->field_87
    //     0xb6a044: ldur            w1, [x0, #0x87]
    // 0xb6a048: DecompressPointer r1
    //     0xb6a048: add             x1, x1, HEAP, lsl #32
    // 0xb6a04c: LoadField: r0 = r1->field_7
    //     0xb6a04c: ldur            w0, [x1, #7]
    // 0xb6a050: DecompressPointer r0
    //     0xb6a050: add             x0, x0, HEAP, lsl #32
    // 0xb6a054: r16 = Instance_Color
    //     0xb6a054: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6a058: r30 = 32.000000
    //     0xb6a058: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb6a05c: ldr             lr, [lr, #0x848]
    // 0xb6a060: stp             lr, x16, [SP]
    // 0xb6a064: mov             x1, x0
    // 0xb6a068: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb6a068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb6a06c: ldr             x4, [x4, #0x9b8]
    // 0xb6a070: r0 = copyWith()
    //     0xb6a070: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6a074: stur            x0, [fp, #-0x30]
    // 0xb6a078: r0 = Text()
    //     0xb6a078: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6a07c: mov             x1, x0
    // 0xb6a080: ldur            x0, [fp, #-0x28]
    // 0xb6a084: stur            x1, [fp, #-0x38]
    // 0xb6a088: StoreField: r1->field_b = r0
    //     0xb6a088: stur            w0, [x1, #0xb]
    // 0xb6a08c: ldur            x0, [fp, #-0x30]
    // 0xb6a090: StoreField: r1->field_13 = r0
    //     0xb6a090: stur            w0, [x1, #0x13]
    // 0xb6a094: ldur            x0, [fp, #-0x20]
    // 0xb6a098: StoreField: r1->field_1b = r0
    //     0xb6a098: stur            w0, [x1, #0x1b]
    // 0xb6a09c: r0 = Padding()
    //     0xb6a09c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6a0a0: mov             x2, x0
    // 0xb6a0a4: r0 = Instance_EdgeInsets
    //     0xb6a0a4: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb6a0a8: ldr             x0, [x0, #0x78]
    // 0xb6a0ac: stur            x2, [fp, #-0x20]
    // 0xb6a0b0: StoreField: r2->field_f = r0
    //     0xb6a0b0: stur            w0, [x2, #0xf]
    // 0xb6a0b4: ldur            x0, [fp, #-0x38]
    // 0xb6a0b8: StoreField: r2->field_b = r0
    //     0xb6a0b8: stur            w0, [x2, #0xb]
    // 0xb6a0bc: ldur            x0, [fp, #-0x18]
    // 0xb6a0c0: LoadField: r1 = r0->field_b
    //     0xb6a0c0: ldur            w1, [x0, #0xb]
    // 0xb6a0c4: LoadField: r3 = r0->field_f
    //     0xb6a0c4: ldur            w3, [x0, #0xf]
    // 0xb6a0c8: DecompressPointer r3
    //     0xb6a0c8: add             x3, x3, HEAP, lsl #32
    // 0xb6a0cc: LoadField: r4 = r3->field_b
    //     0xb6a0cc: ldur            w4, [x3, #0xb]
    // 0xb6a0d0: r3 = LoadInt32Instr(r1)
    //     0xb6a0d0: sbfx            x3, x1, #1, #0x1f
    // 0xb6a0d4: stur            x3, [fp, #-0x40]
    // 0xb6a0d8: r1 = LoadInt32Instr(r4)
    //     0xb6a0d8: sbfx            x1, x4, #1, #0x1f
    // 0xb6a0dc: cmp             x3, x1
    // 0xb6a0e0: b.ne            #0xb6a0ec
    // 0xb6a0e4: mov             x1, x0
    // 0xb6a0e8: r0 = _growToNextCapacity()
    //     0xb6a0e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6a0ec: ldur            x3, [fp, #-0x18]
    // 0xb6a0f0: ldur            x2, [fp, #-0x40]
    // 0xb6a0f4: add             x0, x2, #1
    // 0xb6a0f8: lsl             x1, x0, #1
    // 0xb6a0fc: StoreField: r3->field_b = r1
    //     0xb6a0fc: stur            w1, [x3, #0xb]
    // 0xb6a100: LoadField: r1 = r3->field_f
    //     0xb6a100: ldur            w1, [x3, #0xf]
    // 0xb6a104: DecompressPointer r1
    //     0xb6a104: add             x1, x1, HEAP, lsl #32
    // 0xb6a108: ldur            x0, [fp, #-0x20]
    // 0xb6a10c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb6a10c: add             x25, x1, x2, lsl #2
    //     0xb6a110: add             x25, x25, #0xf
    //     0xb6a114: str             w0, [x25]
    //     0xb6a118: tbz             w0, #0, #0xb6a134
    //     0xb6a11c: ldurb           w16, [x1, #-1]
    //     0xb6a120: ldurb           w17, [x0, #-1]
    //     0xb6a124: and             x16, x17, x16, lsr #2
    //     0xb6a128: tst             x16, HEAP, lsr #32
    //     0xb6a12c: b.eq            #0xb6a134
    //     0xb6a130: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6a134: b               #0xb6a13c
    // 0xb6a138: mov             x3, x1
    // 0xb6a13c: ldur            x0, [fp, #-0x10]
    // 0xb6a140: LoadField: r1 = r0->field_f
    //     0xb6a140: ldur            w1, [x0, #0xf]
    // 0xb6a144: DecompressPointer r1
    //     0xb6a144: add             x1, x1, HEAP, lsl #32
    // 0xb6a148: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xb6a148: ldur            x2, [x1, #0x17]
    // 0xb6a14c: r0 = _calculateCardHeight()
    //     0xb6a14c: bl              #0xa5b7d8  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xb6a150: ldur            x0, [fp, #-0x10]
    // 0xb6a154: stur            d0, [fp, #-0x58]
    // 0xb6a158: LoadField: r1 = r0->field_f
    //     0xb6a158: ldur            w1, [x0, #0xf]
    // 0xb6a15c: DecompressPointer r1
    //     0xb6a15c: add             x1, x1, HEAP, lsl #32
    // 0xb6a160: LoadField: r2 = r1->field_b
    //     0xb6a160: ldur            w2, [x1, #0xb]
    // 0xb6a164: DecompressPointer r2
    //     0xb6a164: add             x2, x2, HEAP, lsl #32
    // 0xb6a168: cmp             w2, NULL
    // 0xb6a16c: b.eq            #0xb6a6ec
    // 0xb6a170: LoadField: r3 = r2->field_b
    //     0xb6a170: ldur            w3, [x2, #0xb]
    // 0xb6a174: DecompressPointer r3
    //     0xb6a174: add             x3, x3, HEAP, lsl #32
    // 0xb6a178: cmp             w3, NULL
    // 0xb6a17c: b.ne            #0xb6a188
    // 0xb6a180: r4 = Null
    //     0xb6a180: mov             x4, NULL
    // 0xb6a184: b               #0xb6a190
    // 0xb6a188: LoadField: r2 = r3->field_b
    //     0xb6a188: ldur            w2, [x3, #0xb]
    // 0xb6a18c: mov             x4, x2
    // 0xb6a190: ldur            x3, [fp, #-0x18]
    // 0xb6a194: stur            x4, [fp, #-0x28]
    // 0xb6a198: LoadField: r5 = r1->field_13
    //     0xb6a198: ldur            w5, [x1, #0x13]
    // 0xb6a19c: DecompressPointer r5
    //     0xb6a19c: add             x5, x5, HEAP, lsl #32
    // 0xb6a1a0: r16 = Sentinel
    //     0xb6a1a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6a1a4: cmp             w5, w16
    // 0xb6a1a8: b.eq            #0xb6a6f0
    // 0xb6a1ac: mov             x2, x0
    // 0xb6a1b0: stur            x5, [fp, #-0x20]
    // 0xb6a1b4: r1 = Function '<anonymous closure>':.
    //     0xb6a1b4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bf0] AnonymousClosure: (0xb6b6e4), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb69e28)
    //     0xb6a1b8: ldr             x1, [x1, #0xbf0]
    // 0xb6a1bc: r0 = AllocateClosure()
    //     0xb6a1bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6a1c0: ldur            x2, [fp, #-0x10]
    // 0xb6a1c4: r1 = Function '<anonymous closure>':.
    //     0xb6a1c4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bf8] AnonymousClosure: (0xb6a7b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb69e28)
    //     0xb6a1c8: ldr             x1, [x1, #0xbf8]
    // 0xb6a1cc: stur            x0, [fp, #-0x30]
    // 0xb6a1d0: r0 = AllocateClosure()
    //     0xb6a1d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6a1d4: stur            x0, [fp, #-0x38]
    // 0xb6a1d8: r0 = PageView()
    //     0xb6a1d8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb6a1dc: stur            x0, [fp, #-0x48]
    // 0xb6a1e0: r16 = Instance_BouncingScrollPhysics
    //     0xb6a1e0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb6a1e4: ldr             x16, [x16, #0x890]
    // 0xb6a1e8: ldur            lr, [fp, #-0x20]
    // 0xb6a1ec: stp             lr, x16, [SP]
    // 0xb6a1f0: mov             x1, x0
    // 0xb6a1f4: ldur            x2, [fp, #-0x38]
    // 0xb6a1f8: ldur            x3, [fp, #-0x28]
    // 0xb6a1fc: ldur            x5, [fp, #-0x30]
    // 0xb6a200: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb6a200: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb6a204: ldr             x4, [x4, #0xe40]
    // 0xb6a208: r0 = PageView.builder()
    //     0xb6a208: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb6a20c: ldur            d0, [fp, #-0x58]
    // 0xb6a210: r0 = inline_Allocate_Double()
    //     0xb6a210: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb6a214: add             x0, x0, #0x10
    //     0xb6a218: cmp             x1, x0
    //     0xb6a21c: b.ls            #0xb6a6fc
    //     0xb6a220: str             x0, [THR, #0x50]  ; THR::top
    //     0xb6a224: sub             x0, x0, #0xf
    //     0xb6a228: movz            x1, #0xe15c
    //     0xb6a22c: movk            x1, #0x3, lsl #16
    //     0xb6a230: stur            x1, [x0, #-1]
    // 0xb6a234: StoreField: r0->field_7 = d0
    //     0xb6a234: stur            d0, [x0, #7]
    // 0xb6a238: stur            x0, [fp, #-0x20]
    // 0xb6a23c: r0 = SizedBox()
    //     0xb6a23c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6a240: mov             x2, x0
    // 0xb6a244: ldur            x0, [fp, #-0x20]
    // 0xb6a248: stur            x2, [fp, #-0x28]
    // 0xb6a24c: StoreField: r2->field_13 = r0
    //     0xb6a24c: stur            w0, [x2, #0x13]
    // 0xb6a250: ldur            x0, [fp, #-0x48]
    // 0xb6a254: StoreField: r2->field_b = r0
    //     0xb6a254: stur            w0, [x2, #0xb]
    // 0xb6a258: ldur            x0, [fp, #-0x18]
    // 0xb6a25c: LoadField: r1 = r0->field_b
    //     0xb6a25c: ldur            w1, [x0, #0xb]
    // 0xb6a260: LoadField: r3 = r0->field_f
    //     0xb6a260: ldur            w3, [x0, #0xf]
    // 0xb6a264: DecompressPointer r3
    //     0xb6a264: add             x3, x3, HEAP, lsl #32
    // 0xb6a268: LoadField: r4 = r3->field_b
    //     0xb6a268: ldur            w4, [x3, #0xb]
    // 0xb6a26c: r3 = LoadInt32Instr(r1)
    //     0xb6a26c: sbfx            x3, x1, #1, #0x1f
    // 0xb6a270: stur            x3, [fp, #-0x40]
    // 0xb6a274: r1 = LoadInt32Instr(r4)
    //     0xb6a274: sbfx            x1, x4, #1, #0x1f
    // 0xb6a278: cmp             x3, x1
    // 0xb6a27c: b.ne            #0xb6a288
    // 0xb6a280: mov             x1, x0
    // 0xb6a284: r0 = _growToNextCapacity()
    //     0xb6a284: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6a288: ldur            x4, [fp, #-0x10]
    // 0xb6a28c: ldur            x2, [fp, #-0x18]
    // 0xb6a290: ldur            x3, [fp, #-0x40]
    // 0xb6a294: add             x0, x3, #1
    // 0xb6a298: lsl             x1, x0, #1
    // 0xb6a29c: StoreField: r2->field_b = r1
    //     0xb6a29c: stur            w1, [x2, #0xb]
    // 0xb6a2a0: LoadField: r1 = r2->field_f
    //     0xb6a2a0: ldur            w1, [x2, #0xf]
    // 0xb6a2a4: DecompressPointer r1
    //     0xb6a2a4: add             x1, x1, HEAP, lsl #32
    // 0xb6a2a8: ldur            x0, [fp, #-0x28]
    // 0xb6a2ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6a2ac: add             x25, x1, x3, lsl #2
    //     0xb6a2b0: add             x25, x25, #0xf
    //     0xb6a2b4: str             w0, [x25]
    //     0xb6a2b8: tbz             w0, #0, #0xb6a2d4
    //     0xb6a2bc: ldurb           w16, [x1, #-1]
    //     0xb6a2c0: ldurb           w17, [x0, #-1]
    //     0xb6a2c4: and             x16, x17, x16, lsr #2
    //     0xb6a2c8: tst             x16, HEAP, lsr #32
    //     0xb6a2cc: b.eq            #0xb6a2d4
    //     0xb6a2d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6a2d4: LoadField: r0 = r4->field_f
    //     0xb6a2d4: ldur            w0, [x4, #0xf]
    // 0xb6a2d8: DecompressPointer r0
    //     0xb6a2d8: add             x0, x0, HEAP, lsl #32
    // 0xb6a2dc: LoadField: r1 = r0->field_b
    //     0xb6a2dc: ldur            w1, [x0, #0xb]
    // 0xb6a2e0: DecompressPointer r1
    //     0xb6a2e0: add             x1, x1, HEAP, lsl #32
    // 0xb6a2e4: cmp             w1, NULL
    // 0xb6a2e8: b.eq            #0xb6a70c
    // 0xb6a2ec: LoadField: r3 = r1->field_b
    //     0xb6a2ec: ldur            w3, [x1, #0xb]
    // 0xb6a2f0: DecompressPointer r3
    //     0xb6a2f0: add             x3, x3, HEAP, lsl #32
    // 0xb6a2f4: cmp             w3, NULL
    // 0xb6a2f8: b.ne            #0xb6a304
    // 0xb6a2fc: r1 = Null
    //     0xb6a2fc: mov             x1, NULL
    // 0xb6a300: b               #0xb6a308
    // 0xb6a304: LoadField: r1 = r3->field_b
    //     0xb6a304: ldur            w1, [x3, #0xb]
    // 0xb6a308: cmp             w1, NULL
    // 0xb6a30c: b.ne            #0xb6a318
    // 0xb6a310: r1 = 0
    //     0xb6a310: movz            x1, #0
    // 0xb6a314: b               #0xb6a320
    // 0xb6a318: r5 = LoadInt32Instr(r1)
    //     0xb6a318: sbfx            x5, x1, #1, #0x1f
    // 0xb6a31c: mov             x1, x5
    // 0xb6a320: cmp             x1, #1
    // 0xb6a324: b.le            #0xb6a41c
    // 0xb6a328: cmp             w3, NULL
    // 0xb6a32c: b.ne            #0xb6a338
    // 0xb6a330: r1 = Null
    //     0xb6a330: mov             x1, NULL
    // 0xb6a334: b               #0xb6a33c
    // 0xb6a338: LoadField: r1 = r3->field_b
    //     0xb6a338: ldur            w1, [x3, #0xb]
    // 0xb6a33c: cmp             w1, NULL
    // 0xb6a340: b.ne            #0xb6a34c
    // 0xb6a344: r3 = 0
    //     0xb6a344: movz            x3, #0
    // 0xb6a348: b               #0xb6a350
    // 0xb6a34c: r3 = LoadInt32Instr(r1)
    //     0xb6a34c: sbfx            x3, x1, #1, #0x1f
    // 0xb6a350: stur            x3, [fp, #-0x50]
    // 0xb6a354: ArrayLoad: r5 = r0[0]  ; List_8
    //     0xb6a354: ldur            x5, [x0, #0x17]
    // 0xb6a358: stur            x5, [fp, #-0x40]
    // 0xb6a35c: LoadField: r1 = r4->field_13
    //     0xb6a35c: ldur            w1, [x4, #0x13]
    // 0xb6a360: DecompressPointer r1
    //     0xb6a360: add             x1, x1, HEAP, lsl #32
    // 0xb6a364: r0 = of()
    //     0xb6a364: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6a368: LoadField: r1 = r0->field_5b
    //     0xb6a368: ldur            w1, [x0, #0x5b]
    // 0xb6a36c: DecompressPointer r1
    //     0xb6a36c: add             x1, x1, HEAP, lsl #32
    // 0xb6a370: stur            x1, [fp, #-0x20]
    // 0xb6a374: r0 = CarouselIndicator()
    //     0xb6a374: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb6a378: mov             x2, x0
    // 0xb6a37c: ldur            x0, [fp, #-0x50]
    // 0xb6a380: stur            x2, [fp, #-0x28]
    // 0xb6a384: StoreField: r2->field_b = r0
    //     0xb6a384: stur            x0, [x2, #0xb]
    // 0xb6a388: ldur            x0, [fp, #-0x40]
    // 0xb6a38c: StoreField: r2->field_13 = r0
    //     0xb6a38c: stur            x0, [x2, #0x13]
    // 0xb6a390: ldur            x0, [fp, #-0x20]
    // 0xb6a394: StoreField: r2->field_1b = r0
    //     0xb6a394: stur            w0, [x2, #0x1b]
    // 0xb6a398: r0 = Instance_Color
    //     0xb6a398: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb6a39c: ldr             x0, [x0, #0x90]
    // 0xb6a3a0: StoreField: r2->field_1f = r0
    //     0xb6a3a0: stur            w0, [x2, #0x1f]
    // 0xb6a3a4: ldur            x0, [fp, #-0x18]
    // 0xb6a3a8: LoadField: r1 = r0->field_b
    //     0xb6a3a8: ldur            w1, [x0, #0xb]
    // 0xb6a3ac: LoadField: r3 = r0->field_f
    //     0xb6a3ac: ldur            w3, [x0, #0xf]
    // 0xb6a3b0: DecompressPointer r3
    //     0xb6a3b0: add             x3, x3, HEAP, lsl #32
    // 0xb6a3b4: LoadField: r4 = r3->field_b
    //     0xb6a3b4: ldur            w4, [x3, #0xb]
    // 0xb6a3b8: r3 = LoadInt32Instr(r1)
    //     0xb6a3b8: sbfx            x3, x1, #1, #0x1f
    // 0xb6a3bc: stur            x3, [fp, #-0x40]
    // 0xb6a3c0: r1 = LoadInt32Instr(r4)
    //     0xb6a3c0: sbfx            x1, x4, #1, #0x1f
    // 0xb6a3c4: cmp             x3, x1
    // 0xb6a3c8: b.ne            #0xb6a3d4
    // 0xb6a3cc: mov             x1, x0
    // 0xb6a3d0: r0 = _growToNextCapacity()
    //     0xb6a3d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6a3d4: ldur            x2, [fp, #-0x18]
    // 0xb6a3d8: ldur            x3, [fp, #-0x40]
    // 0xb6a3dc: add             x0, x3, #1
    // 0xb6a3e0: lsl             x1, x0, #1
    // 0xb6a3e4: StoreField: r2->field_b = r1
    //     0xb6a3e4: stur            w1, [x2, #0xb]
    // 0xb6a3e8: LoadField: r1 = r2->field_f
    //     0xb6a3e8: ldur            w1, [x2, #0xf]
    // 0xb6a3ec: DecompressPointer r1
    //     0xb6a3ec: add             x1, x1, HEAP, lsl #32
    // 0xb6a3f0: ldur            x0, [fp, #-0x28]
    // 0xb6a3f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6a3f4: add             x25, x1, x3, lsl #2
    //     0xb6a3f8: add             x25, x25, #0xf
    //     0xb6a3fc: str             w0, [x25]
    //     0xb6a400: tbz             w0, #0, #0xb6a41c
    //     0xb6a404: ldurb           w16, [x1, #-1]
    //     0xb6a408: ldurb           w17, [x0, #-1]
    //     0xb6a40c: and             x16, x17, x16, lsr #2
    //     0xb6a410: tst             x16, HEAP, lsr #32
    //     0xb6a414: b.eq            #0xb6a41c
    //     0xb6a418: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6a41c: ldur            x0, [fp, #-0x10]
    // 0xb6a420: LoadField: r1 = r0->field_13
    //     0xb6a420: ldur            w1, [x0, #0x13]
    // 0xb6a424: DecompressPointer r1
    //     0xb6a424: add             x1, x1, HEAP, lsl #32
    // 0xb6a428: r0 = of()
    //     0xb6a428: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6a42c: LoadField: r1 = r0->field_5b
    //     0xb6a42c: ldur            w1, [x0, #0x5b]
    // 0xb6a430: DecompressPointer r1
    //     0xb6a430: add             x1, x1, HEAP, lsl #32
    // 0xb6a434: stur            x1, [fp, #-0x20]
    // 0xb6a438: r0 = BoxDecoration()
    //     0xb6a438: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6a43c: mov             x1, x0
    // 0xb6a440: ldur            x0, [fp, #-0x20]
    // 0xb6a444: stur            x1, [fp, #-0x28]
    // 0xb6a448: StoreField: r1->field_7 = r0
    //     0xb6a448: stur            w0, [x1, #7]
    // 0xb6a44c: r0 = Instance_BorderRadius
    //     0xb6a44c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb6a450: ldr             x0, [x0, #0x460]
    // 0xb6a454: StoreField: r1->field_13 = r0
    //     0xb6a454: stur            w0, [x1, #0x13]
    // 0xb6a458: r0 = Instance_BoxShape
    //     0xb6a458: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6a45c: ldr             x0, [x0, #0x80]
    // 0xb6a460: StoreField: r1->field_23 = r0
    //     0xb6a460: stur            w0, [x1, #0x23]
    // 0xb6a464: ldur            x2, [fp, #-0x10]
    // 0xb6a468: LoadField: r3 = r2->field_f
    //     0xb6a468: ldur            w3, [x2, #0xf]
    // 0xb6a46c: DecompressPointer r3
    //     0xb6a46c: add             x3, x3, HEAP, lsl #32
    // 0xb6a470: LoadField: r4 = r3->field_b
    //     0xb6a470: ldur            w4, [x3, #0xb]
    // 0xb6a474: DecompressPointer r4
    //     0xb6a474: add             x4, x4, HEAP, lsl #32
    // 0xb6a478: cmp             w4, NULL
    // 0xb6a47c: b.eq            #0xb6a710
    // 0xb6a480: LoadField: r3 = r4->field_2b
    //     0xb6a480: ldur            w3, [x4, #0x2b]
    // 0xb6a484: DecompressPointer r3
    //     0xb6a484: add             x3, x3, HEAP, lsl #32
    // 0xb6a488: cmp             w3, NULL
    // 0xb6a48c: b.ne            #0xb6a498
    // 0xb6a490: r4 = Null
    //     0xb6a490: mov             x4, NULL
    // 0xb6a494: b               #0xb6a4a0
    // 0xb6a498: LoadField: r4 = r3->field_7
    //     0xb6a498: ldur            w4, [x3, #7]
    // 0xb6a49c: DecompressPointer r4
    //     0xb6a49c: add             x4, x4, HEAP, lsl #32
    // 0xb6a4a0: ldur            x3, [fp, #-0x18]
    // 0xb6a4a4: str             x4, [SP]
    // 0xb6a4a8: r0 = _interpolateSingle()
    //     0xb6a4a8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb6a4ac: ldur            x2, [fp, #-0x10]
    // 0xb6a4b0: stur            x0, [fp, #-0x20]
    // 0xb6a4b4: LoadField: r1 = r2->field_13
    //     0xb6a4b4: ldur            w1, [x2, #0x13]
    // 0xb6a4b8: DecompressPointer r1
    //     0xb6a4b8: add             x1, x1, HEAP, lsl #32
    // 0xb6a4bc: r0 = of()
    //     0xb6a4bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6a4c0: LoadField: r1 = r0->field_87
    //     0xb6a4c0: ldur            w1, [x0, #0x87]
    // 0xb6a4c4: DecompressPointer r1
    //     0xb6a4c4: add             x1, x1, HEAP, lsl #32
    // 0xb6a4c8: LoadField: r0 = r1->field_2b
    //     0xb6a4c8: ldur            w0, [x1, #0x2b]
    // 0xb6a4cc: DecompressPointer r0
    //     0xb6a4cc: add             x0, x0, HEAP, lsl #32
    // 0xb6a4d0: r16 = 16.000000
    //     0xb6a4d0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6a4d4: ldr             x16, [x16, #0x188]
    // 0xb6a4d8: r30 = Instance_Color
    //     0xb6a4d8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6a4dc: stp             lr, x16, [SP]
    // 0xb6a4e0: mov             x1, x0
    // 0xb6a4e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6a4e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6a4e8: ldr             x4, [x4, #0xaa0]
    // 0xb6a4ec: r0 = copyWith()
    //     0xb6a4ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6a4f0: stur            x0, [fp, #-0x30]
    // 0xb6a4f4: r0 = Text()
    //     0xb6a4f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6a4f8: mov             x1, x0
    // 0xb6a4fc: ldur            x0, [fp, #-0x20]
    // 0xb6a500: stur            x1, [fp, #-0x38]
    // 0xb6a504: StoreField: r1->field_b = r0
    //     0xb6a504: stur            w0, [x1, #0xb]
    // 0xb6a508: ldur            x0, [fp, #-0x30]
    // 0xb6a50c: StoreField: r1->field_13 = r0
    //     0xb6a50c: stur            w0, [x1, #0x13]
    // 0xb6a510: r0 = Center()
    //     0xb6a510: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb6a514: mov             x1, x0
    // 0xb6a518: r0 = Instance_Alignment
    //     0xb6a518: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb6a51c: ldr             x0, [x0, #0xb10]
    // 0xb6a520: stur            x1, [fp, #-0x20]
    // 0xb6a524: StoreField: r1->field_f = r0
    //     0xb6a524: stur            w0, [x1, #0xf]
    // 0xb6a528: ldur            x0, [fp, #-0x38]
    // 0xb6a52c: StoreField: r1->field_b = r0
    //     0xb6a52c: stur            w0, [x1, #0xb]
    // 0xb6a530: r0 = Container()
    //     0xb6a530: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6a534: stur            x0, [fp, #-0x30]
    // 0xb6a538: r16 = 40.000000
    //     0xb6a538: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb6a53c: ldr             x16, [x16, #8]
    // 0xb6a540: r30 = 110.000000
    //     0xb6a540: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb6a544: ldr             lr, [lr, #0x770]
    // 0xb6a548: stp             lr, x16, [SP, #0x10]
    // 0xb6a54c: ldur            x16, [fp, #-0x28]
    // 0xb6a550: ldur            lr, [fp, #-0x20]
    // 0xb6a554: stp             lr, x16, [SP]
    // 0xb6a558: mov             x1, x0
    // 0xb6a55c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb6a55c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb6a560: ldr             x4, [x4, #0x8c0]
    // 0xb6a564: r0 = Container()
    //     0xb6a564: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6a568: r0 = InkWell()
    //     0xb6a568: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb6a56c: mov             x3, x0
    // 0xb6a570: ldur            x0, [fp, #-0x30]
    // 0xb6a574: stur            x3, [fp, #-0x20]
    // 0xb6a578: StoreField: r3->field_b = r0
    //     0xb6a578: stur            w0, [x3, #0xb]
    // 0xb6a57c: ldur            x2, [fp, #-0x10]
    // 0xb6a580: r1 = Function '<anonymous closure>':.
    //     0xb6a580: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c00] AnonymousClosure: (0xb6a714), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb69e28)
    //     0xb6a584: ldr             x1, [x1, #0xc00]
    // 0xb6a588: r0 = AllocateClosure()
    //     0xb6a588: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6a58c: mov             x1, x0
    // 0xb6a590: ldur            x0, [fp, #-0x20]
    // 0xb6a594: StoreField: r0->field_f = r1
    //     0xb6a594: stur            w1, [x0, #0xf]
    // 0xb6a598: r1 = true
    //     0xb6a598: add             x1, NULL, #0x20  ; true
    // 0xb6a59c: StoreField: r0->field_43 = r1
    //     0xb6a59c: stur            w1, [x0, #0x43]
    // 0xb6a5a0: r2 = Instance_BoxShape
    //     0xb6a5a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6a5a4: ldr             x2, [x2, #0x80]
    // 0xb6a5a8: StoreField: r0->field_47 = r2
    //     0xb6a5a8: stur            w2, [x0, #0x47]
    // 0xb6a5ac: StoreField: r0->field_6f = r1
    //     0xb6a5ac: stur            w1, [x0, #0x6f]
    // 0xb6a5b0: r2 = false
    //     0xb6a5b0: add             x2, NULL, #0x30  ; false
    // 0xb6a5b4: StoreField: r0->field_73 = r2
    //     0xb6a5b4: stur            w2, [x0, #0x73]
    // 0xb6a5b8: StoreField: r0->field_83 = r1
    //     0xb6a5b8: stur            w1, [x0, #0x83]
    // 0xb6a5bc: StoreField: r0->field_7b = r2
    //     0xb6a5bc: stur            w2, [x0, #0x7b]
    // 0xb6a5c0: r0 = Padding()
    //     0xb6a5c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6a5c4: mov             x2, x0
    // 0xb6a5c8: r0 = Instance_EdgeInsets
    //     0xb6a5c8: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0xb6a5cc: ldr             x0, [x0, #0xc30]
    // 0xb6a5d0: stur            x2, [fp, #-0x10]
    // 0xb6a5d4: StoreField: r2->field_f = r0
    //     0xb6a5d4: stur            w0, [x2, #0xf]
    // 0xb6a5d8: ldur            x0, [fp, #-0x20]
    // 0xb6a5dc: StoreField: r2->field_b = r0
    //     0xb6a5dc: stur            w0, [x2, #0xb]
    // 0xb6a5e0: ldur            x0, [fp, #-0x18]
    // 0xb6a5e4: LoadField: r1 = r0->field_b
    //     0xb6a5e4: ldur            w1, [x0, #0xb]
    // 0xb6a5e8: LoadField: r3 = r0->field_f
    //     0xb6a5e8: ldur            w3, [x0, #0xf]
    // 0xb6a5ec: DecompressPointer r3
    //     0xb6a5ec: add             x3, x3, HEAP, lsl #32
    // 0xb6a5f0: LoadField: r4 = r3->field_b
    //     0xb6a5f0: ldur            w4, [x3, #0xb]
    // 0xb6a5f4: r3 = LoadInt32Instr(r1)
    //     0xb6a5f4: sbfx            x3, x1, #1, #0x1f
    // 0xb6a5f8: stur            x3, [fp, #-0x40]
    // 0xb6a5fc: r1 = LoadInt32Instr(r4)
    //     0xb6a5fc: sbfx            x1, x4, #1, #0x1f
    // 0xb6a600: cmp             x3, x1
    // 0xb6a604: b.ne            #0xb6a610
    // 0xb6a608: mov             x1, x0
    // 0xb6a60c: r0 = _growToNextCapacity()
    //     0xb6a60c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6a610: ldur            x2, [fp, #-0x18]
    // 0xb6a614: ldur            x4, [fp, #-8]
    // 0xb6a618: ldur            x3, [fp, #-0x40]
    // 0xb6a61c: add             x0, x3, #1
    // 0xb6a620: lsl             x1, x0, #1
    // 0xb6a624: StoreField: r2->field_b = r1
    //     0xb6a624: stur            w1, [x2, #0xb]
    // 0xb6a628: LoadField: r1 = r2->field_f
    //     0xb6a628: ldur            w1, [x2, #0xf]
    // 0xb6a62c: DecompressPointer r1
    //     0xb6a62c: add             x1, x1, HEAP, lsl #32
    // 0xb6a630: ldur            x0, [fp, #-0x10]
    // 0xb6a634: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6a634: add             x25, x1, x3, lsl #2
    //     0xb6a638: add             x25, x25, #0xf
    //     0xb6a63c: str             w0, [x25]
    //     0xb6a640: tbz             w0, #0, #0xb6a65c
    //     0xb6a644: ldurb           w16, [x1, #-1]
    //     0xb6a648: ldurb           w17, [x0, #-1]
    //     0xb6a64c: and             x16, x17, x16, lsr #2
    //     0xb6a650: tst             x16, HEAP, lsr #32
    //     0xb6a654: b.eq            #0xb6a65c
    //     0xb6a658: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6a65c: r0 = Column()
    //     0xb6a65c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6a660: mov             x1, x0
    // 0xb6a664: r0 = Instance_Axis
    //     0xb6a664: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6a668: stur            x1, [fp, #-0x10]
    // 0xb6a66c: StoreField: r1->field_f = r0
    //     0xb6a66c: stur            w0, [x1, #0xf]
    // 0xb6a670: r0 = Instance_MainAxisAlignment
    //     0xb6a670: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6a674: ldr             x0, [x0, #0xa08]
    // 0xb6a678: StoreField: r1->field_13 = r0
    //     0xb6a678: stur            w0, [x1, #0x13]
    // 0xb6a67c: r0 = Instance_MainAxisSize
    //     0xb6a67c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6a680: ldr             x0, [x0, #0xa10]
    // 0xb6a684: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6a684: stur            w0, [x1, #0x17]
    // 0xb6a688: ldur            x0, [fp, #-8]
    // 0xb6a68c: StoreField: r1->field_1b = r0
    //     0xb6a68c: stur            w0, [x1, #0x1b]
    // 0xb6a690: r0 = Instance_VerticalDirection
    //     0xb6a690: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6a694: ldr             x0, [x0, #0xa20]
    // 0xb6a698: StoreField: r1->field_23 = r0
    //     0xb6a698: stur            w0, [x1, #0x23]
    // 0xb6a69c: r0 = Instance_Clip
    //     0xb6a69c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6a6a0: ldr             x0, [x0, #0x38]
    // 0xb6a6a4: StoreField: r1->field_2b = r0
    //     0xb6a6a4: stur            w0, [x1, #0x2b]
    // 0xb6a6a8: StoreField: r1->field_2f = rZR
    //     0xb6a6a8: stur            xzr, [x1, #0x2f]
    // 0xb6a6ac: ldur            x0, [fp, #-0x18]
    // 0xb6a6b0: StoreField: r1->field_b = r0
    //     0xb6a6b0: stur            w0, [x1, #0xb]
    // 0xb6a6b4: r0 = Padding()
    //     0xb6a6b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6a6b8: r1 = Instance_EdgeInsets
    //     0xb6a6b8: add             x1, PP, #0x54, lsl #12  ; [pp+0x544d8] Obj!EdgeInsets@d580d1
    //     0xb6a6bc: ldr             x1, [x1, #0x4d8]
    // 0xb6a6c0: StoreField: r0->field_f = r1
    //     0xb6a6c0: stur            w1, [x0, #0xf]
    // 0xb6a6c4: ldur            x1, [fp, #-0x10]
    // 0xb6a6c8: StoreField: r0->field_b = r1
    //     0xb6a6c8: stur            w1, [x0, #0xb]
    // 0xb6a6cc: LeaveFrame
    //     0xb6a6cc: mov             SP, fp
    //     0xb6a6d0: ldp             fp, lr, [SP], #0x10
    // 0xb6a6d4: ret
    //     0xb6a6d4: ret             
    // 0xb6a6d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6a6d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6a6dc: b               #0xb69ed4
    // 0xb6a6e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a6e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6a6e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a6e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6a6e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a6e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6a6ec: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb6a6ec: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb6a6f0: r9 = _pageController
    //     0xb6a6f0: add             x9, PP, #0x55, lsl #12  ; [pp+0x55be0] Field <_ProductTestimonialCarouselState@1590439617._pageController@1590439617>: late (offset: 0x14)
    //     0xb6a6f4: ldr             x9, [x9, #0xbe0]
    // 0xb6a6f8: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xb6a6f8: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xb6a6fc: SaveReg d0
    //     0xb6a6fc: str             q0, [SP, #-0x10]!
    // 0xb6a700: r0 = AllocateDouble()
    //     0xb6a700: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6a704: RestoreReg d0
    //     0xb6a704: ldr             q0, [SP], #0x10
    // 0xb6a708: b               #0xb6a234
    // 0xb6a70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6a710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6a714, size: 0xa4
    // 0xb6a714: EnterFrame
    //     0xb6a714: stp             fp, lr, [SP, #-0x10]!
    //     0xb6a718: mov             fp, SP
    // 0xb6a71c: AllocStack(0x28)
    //     0xb6a71c: sub             SP, SP, #0x28
    // 0xb6a720: SetupParameters()
    //     0xb6a720: ldr             x0, [fp, #0x10]
    //     0xb6a724: ldur            w1, [x0, #0x17]
    //     0xb6a728: add             x1, x1, HEAP, lsl #32
    // 0xb6a72c: CheckStackOverflow
    //     0xb6a72c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6a730: cmp             SP, x16
    //     0xb6a734: b.ls            #0xb6a7ac
    // 0xb6a738: LoadField: r0 = r1->field_f
    //     0xb6a738: ldur            w0, [x1, #0xf]
    // 0xb6a73c: DecompressPointer r0
    //     0xb6a73c: add             x0, x0, HEAP, lsl #32
    // 0xb6a740: LoadField: r1 = r0->field_b
    //     0xb6a740: ldur            w1, [x0, #0xb]
    // 0xb6a744: DecompressPointer r1
    //     0xb6a744: add             x1, x1, HEAP, lsl #32
    // 0xb6a748: cmp             w1, NULL
    // 0xb6a74c: b.eq            #0xb6a7b4
    // 0xb6a750: LoadField: r0 = r1->field_1b
    //     0xb6a750: ldur            w0, [x1, #0x1b]
    // 0xb6a754: DecompressPointer r0
    //     0xb6a754: add             x0, x0, HEAP, lsl #32
    // 0xb6a758: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb6a758: ldur            w2, [x1, #0x17]
    // 0xb6a75c: DecompressPointer r2
    //     0xb6a75c: add             x2, x2, HEAP, lsl #32
    // 0xb6a760: LoadField: r3 = r1->field_23
    //     0xb6a760: ldur            w3, [x1, #0x23]
    // 0xb6a764: DecompressPointer r3
    //     0xb6a764: add             x3, x3, HEAP, lsl #32
    // 0xb6a768: LoadField: r4 = r1->field_1f
    //     0xb6a768: ldur            w4, [x1, #0x1f]
    // 0xb6a76c: DecompressPointer r4
    //     0xb6a76c: add             x4, x4, HEAP, lsl #32
    // 0xb6a770: LoadField: r5 = r1->field_27
    //     0xb6a770: ldur            w5, [x1, #0x27]
    // 0xb6a774: DecompressPointer r5
    //     0xb6a774: add             x5, x5, HEAP, lsl #32
    // 0xb6a778: stp             x0, x5, [SP, #0x18]
    // 0xb6a77c: stp             x3, x2, [SP, #8]
    // 0xb6a780: str             x4, [SP]
    // 0xb6a784: r4 = 0
    //     0xb6a784: movz            x4, #0
    // 0xb6a788: ldr             x0, [SP, #0x20]
    // 0xb6a78c: r16 = UnlinkedCall_0x613b5c
    //     0xb6a78c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55c08] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6a790: add             x16, x16, #0xc08
    // 0xb6a794: ldp             x5, lr, [x16]
    // 0xb6a798: blr             lr
    // 0xb6a79c: r0 = Null
    //     0xb6a79c: mov             x0, NULL
    // 0xb6a7a0: LeaveFrame
    //     0xb6a7a0: mov             SP, fp
    //     0xb6a7a4: ldp             fp, lr, [SP], #0x10
    // 0xb6a7a8: ret
    //     0xb6a7a8: ret             
    // 0xb6a7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6a7ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6a7b0: b               #0xb6a738
    // 0xb6a7b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a7b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6a7b8, size: 0x7c
    // 0xb6a7b8: EnterFrame
    //     0xb6a7b8: stp             fp, lr, [SP, #-0x10]!
    //     0xb6a7bc: mov             fp, SP
    // 0xb6a7c0: ldr             x0, [fp, #0x20]
    // 0xb6a7c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb6a7c4: ldur            w1, [x0, #0x17]
    // 0xb6a7c8: DecompressPointer r1
    //     0xb6a7c8: add             x1, x1, HEAP, lsl #32
    // 0xb6a7cc: CheckStackOverflow
    //     0xb6a7cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6a7d0: cmp             SP, x16
    //     0xb6a7d4: b.ls            #0xb6a824
    // 0xb6a7d8: LoadField: r0 = r1->field_f
    //     0xb6a7d8: ldur            w0, [x1, #0xf]
    // 0xb6a7dc: DecompressPointer r0
    //     0xb6a7dc: add             x0, x0, HEAP, lsl #32
    // 0xb6a7e0: LoadField: r1 = r0->field_b
    //     0xb6a7e0: ldur            w1, [x0, #0xb]
    // 0xb6a7e4: DecompressPointer r1
    //     0xb6a7e4: add             x1, x1, HEAP, lsl #32
    // 0xb6a7e8: cmp             w1, NULL
    // 0xb6a7ec: b.eq            #0xb6a82c
    // 0xb6a7f0: LoadField: r2 = r1->field_b
    //     0xb6a7f0: ldur            w2, [x1, #0xb]
    // 0xb6a7f4: DecompressPointer r2
    //     0xb6a7f4: add             x2, x2, HEAP, lsl #32
    // 0xb6a7f8: cmp             w2, NULL
    // 0xb6a7fc: b.eq            #0xb6a830
    // 0xb6a800: ldr             x1, [fp, #0x10]
    // 0xb6a804: r3 = LoadInt32Instr(r1)
    //     0xb6a804: sbfx            x3, x1, #1, #0x1f
    //     0xb6a808: tbz             w1, #0, #0xb6a810
    //     0xb6a80c: ldur            x3, [x1, #7]
    // 0xb6a810: mov             x1, x0
    // 0xb6a814: r0 = _testimonialCard()
    //     0xb6a814: bl              #0xb6a834  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xb6a818: LeaveFrame
    //     0xb6a818: mov             SP, fp
    //     0xb6a81c: ldp             fp, lr, [SP], #0x10
    // 0xb6a820: ret
    //     0xb6a820: ret             
    // 0xb6a824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6a824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6a828: b               #0xb6a7d8
    // 0xb6a82c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a82c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6a830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6a830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xb6a834, size: 0xe24
    // 0xb6a834: EnterFrame
    //     0xb6a834: stp             fp, lr, [SP, #-0x10]!
    //     0xb6a838: mov             fp, SP
    // 0xb6a83c: AllocStack(0xa0)
    //     0xb6a83c: sub             SP, SP, #0xa0
    // 0xb6a840: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb6a840: mov             x0, x1
    //     0xb6a844: stur            x1, [fp, #-8]
    //     0xb6a848: mov             x1, x3
    //     0xb6a84c: stur            x2, [fp, #-0x10]
    //     0xb6a850: stur            x3, [fp, #-0x18]
    // 0xb6a854: CheckStackOverflow
    //     0xb6a854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6a858: cmp             SP, x16
    //     0xb6a85c: b.ls            #0xb6b620
    // 0xb6a860: r1 = 2
    //     0xb6a860: movz            x1, #0x2
    // 0xb6a864: r0 = AllocateContext()
    //     0xb6a864: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6a868: mov             x4, x0
    // 0xb6a86c: ldur            x3, [fp, #-8]
    // 0xb6a870: stur            x4, [fp, #-0x30]
    // 0xb6a874: StoreField: r4->field_f = r3
    //     0xb6a874: stur            w3, [x4, #0xf]
    // 0xb6a878: ldur            x2, [fp, #-0x10]
    // 0xb6a87c: LoadField: r0 = r2->field_b
    //     0xb6a87c: ldur            w0, [x2, #0xb]
    // 0xb6a880: r1 = LoadInt32Instr(r0)
    //     0xb6a880: sbfx            x1, x0, #1, #0x1f
    // 0xb6a884: mov             x0, x1
    // 0xb6a888: ldur            x1, [fp, #-0x18]
    // 0xb6a88c: cmp             x1, x0
    // 0xb6a890: b.hs            #0xb6b628
    // 0xb6a894: LoadField: r5 = r2->field_f
    //     0xb6a894: ldur            w5, [x2, #0xf]
    // 0xb6a898: DecompressPointer r5
    //     0xb6a898: add             x5, x5, HEAP, lsl #32
    // 0xb6a89c: ldur            x2, [fp, #-0x18]
    // 0xb6a8a0: r0 = BoxInt64Instr(r2)
    //     0xb6a8a0: sbfiz           x0, x2, #1, #0x1f
    //     0xb6a8a4: cmp             x2, x0, asr #1
    //     0xb6a8a8: b.eq            #0xb6a8b4
    //     0xb6a8ac: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6a8b0: stur            x2, [x0, #7]
    // 0xb6a8b4: ArrayLoad: r6 = r5[r2]  ; Unknown_4
    //     0xb6a8b4: add             x16, x5, x2, lsl #2
    //     0xb6a8b8: ldur            w6, [x16, #0xf]
    // 0xb6a8bc: DecompressPointer r6
    //     0xb6a8bc: add             x6, x6, HEAP, lsl #32
    // 0xb6a8c0: stur            x6, [fp, #-0x28]
    // 0xb6a8c4: LoadField: r1 = r6->field_ab
    //     0xb6a8c4: ldur            w1, [x6, #0xab]
    // 0xb6a8c8: DecompressPointer r1
    //     0xb6a8c8: add             x1, x1, HEAP, lsl #32
    // 0xb6a8cc: cmp             w1, NULL
    // 0xb6a8d0: b.ne            #0xb6a8dc
    // 0xb6a8d4: r1 = Null
    //     0xb6a8d4: mov             x1, NULL
    // 0xb6a8d8: b               #0xb6a8f0
    // 0xb6a8dc: LoadField: r2 = r1->field_b
    //     0xb6a8dc: ldur            w2, [x1, #0xb]
    // 0xb6a8e0: cbnz            w2, #0xb6a8ec
    // 0xb6a8e4: r1 = false
    //     0xb6a8e4: add             x1, NULL, #0x30  ; false
    // 0xb6a8e8: b               #0xb6a8f0
    // 0xb6a8ec: r1 = true
    //     0xb6a8ec: add             x1, NULL, #0x20  ; true
    // 0xb6a8f0: cmp             w1, NULL
    // 0xb6a8f4: b.ne            #0xb6a900
    // 0xb6a8f8: r5 = false
    //     0xb6a8f8: add             x5, NULL, #0x30  ; false
    // 0xb6a8fc: b               #0xb6a904
    // 0xb6a900: mov             x5, x1
    // 0xb6a904: stur            x5, [fp, #-0x20]
    // 0xb6a908: LoadField: r7 = r3->field_1f
    //     0xb6a908: ldur            w7, [x3, #0x1f]
    // 0xb6a90c: DecompressPointer r7
    //     0xb6a90c: add             x7, x7, HEAP, lsl #32
    // 0xb6a910: mov             x1, x7
    // 0xb6a914: mov             x2, x0
    // 0xb6a918: stur            x7, [fp, #-0x10]
    // 0xb6a91c: r0 = _getValueOrData()
    //     0xb6a91c: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb6a920: mov             x1, x0
    // 0xb6a924: ldur            x0, [fp, #-0x10]
    // 0xb6a928: LoadField: r2 = r0->field_f
    //     0xb6a928: ldur            w2, [x0, #0xf]
    // 0xb6a92c: DecompressPointer r2
    //     0xb6a92c: add             x2, x2, HEAP, lsl #32
    // 0xb6a930: cmp             w2, w1
    // 0xb6a934: b.ne            #0xb6a940
    // 0xb6a938: r0 = Null
    //     0xb6a938: mov             x0, NULL
    // 0xb6a93c: b               #0xb6a944
    // 0xb6a940: mov             x0, x1
    // 0xb6a944: cmp             w0, NULL
    // 0xb6a948: b.ne            #0xb6a988
    // 0xb6a94c: r1 = <bool>
    //     0xb6a94c: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xb6a950: r0 = RxBool()
    //     0xb6a950: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xb6a954: mov             x2, x0
    // 0xb6a958: r0 = Sentinel
    //     0xb6a958: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6a95c: stur            x2, [fp, #-0x10]
    // 0xb6a960: StoreField: r2->field_13 = r0
    //     0xb6a960: stur            w0, [x2, #0x13]
    // 0xb6a964: r0 = true
    //     0xb6a964: add             x0, NULL, #0x20  ; true
    // 0xb6a968: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6a968: stur            w0, [x2, #0x17]
    // 0xb6a96c: mov             x1, x2
    // 0xb6a970: r0 = RxNotifier()
    //     0xb6a970: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xb6a974: ldur            x0, [fp, #-0x10]
    // 0xb6a978: r1 = false
    //     0xb6a978: add             x1, NULL, #0x30  ; false
    // 0xb6a97c: StoreField: r0->field_13 = r1
    //     0xb6a97c: stur            w1, [x0, #0x13]
    // 0xb6a980: mov             x5, x0
    // 0xb6a984: b               #0xb6a990
    // 0xb6a988: r1 = false
    //     0xb6a988: add             x1, NULL, #0x30  ; false
    // 0xb6a98c: mov             x5, x0
    // 0xb6a990: ldur            x2, [fp, #-8]
    // 0xb6a994: ldur            x3, [fp, #-0x30]
    // 0xb6a998: ldur            x4, [fp, #-0x20]
    // 0xb6a99c: mov             x0, x5
    // 0xb6a9a0: stur            x5, [fp, #-0x10]
    // 0xb6a9a4: StoreField: r3->field_13 = r0
    //     0xb6a9a4: stur            w0, [x3, #0x13]
    //     0xb6a9a8: ldurb           w16, [x3, #-1]
    //     0xb6a9ac: ldurb           w17, [x0, #-1]
    //     0xb6a9b0: and             x16, x17, x16, lsr #2
    //     0xb6a9b4: tst             x16, HEAP, lsr #32
    //     0xb6a9b8: b.eq            #0xb6a9c0
    //     0xb6a9bc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb6a9c0: r0 = Radius()
    //     0xb6a9c0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6a9c4: d0 = 12.000000
    //     0xb6a9c4: fmov            d0, #12.00000000
    // 0xb6a9c8: stur            x0, [fp, #-0x38]
    // 0xb6a9cc: StoreField: r0->field_7 = d0
    //     0xb6a9cc: stur            d0, [x0, #7]
    // 0xb6a9d0: StoreField: r0->field_f = d0
    //     0xb6a9d0: stur            d0, [x0, #0xf]
    // 0xb6a9d4: r0 = BorderRadius()
    //     0xb6a9d4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6a9d8: mov             x1, x0
    // 0xb6a9dc: ldur            x0, [fp, #-0x38]
    // 0xb6a9e0: stur            x1, [fp, #-0x40]
    // 0xb6a9e4: StoreField: r1->field_7 = r0
    //     0xb6a9e4: stur            w0, [x1, #7]
    // 0xb6a9e8: StoreField: r1->field_b = r0
    //     0xb6a9e8: stur            w0, [x1, #0xb]
    // 0xb6a9ec: StoreField: r1->field_f = r0
    //     0xb6a9ec: stur            w0, [x1, #0xf]
    // 0xb6a9f0: StoreField: r1->field_13 = r0
    //     0xb6a9f0: stur            w0, [x1, #0x13]
    // 0xb6a9f4: r0 = RoundedRectangleBorder()
    //     0xb6a9f4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb6a9f8: mov             x2, x0
    // 0xb6a9fc: ldur            x0, [fp, #-0x40]
    // 0xb6aa00: stur            x2, [fp, #-0x38]
    // 0xb6aa04: StoreField: r2->field_b = r0
    //     0xb6aa04: stur            w0, [x2, #0xb]
    // 0xb6aa08: r0 = Instance_BorderSide
    //     0xb6aa08: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb6aa0c: ldr             x0, [x0, #0xe20]
    // 0xb6aa10: StoreField: r2->field_7 = r0
    //     0xb6aa10: stur            w0, [x2, #7]
    // 0xb6aa14: ldur            x0, [fp, #-8]
    // 0xb6aa18: LoadField: r1 = r0->field_f
    //     0xb6aa18: ldur            w1, [x0, #0xf]
    // 0xb6aa1c: DecompressPointer r1
    //     0xb6aa1c: add             x1, x1, HEAP, lsl #32
    // 0xb6aa20: cmp             w1, NULL
    // 0xb6aa24: b.eq            #0xb6b62c
    // 0xb6aa28: r0 = of()
    //     0xb6aa28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6aa2c: LoadField: r1 = r0->field_5b
    //     0xb6aa2c: ldur            w1, [x0, #0x5b]
    // 0xb6aa30: DecompressPointer r1
    //     0xb6aa30: add             x1, x1, HEAP, lsl #32
    // 0xb6aa34: r0 = LoadClassIdInstr(r1)
    //     0xb6aa34: ldur            x0, [x1, #-1]
    //     0xb6aa38: ubfx            x0, x0, #0xc, #0x14
    // 0xb6aa3c: d0 = 0.030000
    //     0xb6aa3c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb6aa40: ldr             d0, [x17, #0x238]
    // 0xb6aa44: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb6aa44: sub             lr, x0, #0xffa
    //     0xb6aa48: ldr             lr, [x21, lr, lsl #3]
    //     0xb6aa4c: blr             lr
    // 0xb6aa50: r1 = <Widget>
    //     0xb6aa50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6aa54: r2 = 0
    //     0xb6aa54: movz            x2, #0
    // 0xb6aa58: stur            x0, [fp, #-0x40]
    // 0xb6aa5c: r0 = _GrowableList()
    //     0xb6aa5c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb6aa60: mov             x3, x0
    // 0xb6aa64: ldur            x0, [fp, #-0x20]
    // 0xb6aa68: stur            x3, [fp, #-0x48]
    // 0xb6aa6c: tbnz            w0, #4, #0xb6ac00
    // 0xb6aa70: ldur            x4, [fp, #-0x28]
    // 0xb6aa74: LoadField: r2 = r4->field_ab
    //     0xb6aa74: ldur            w2, [x4, #0xab]
    // 0xb6aa78: DecompressPointer r2
    //     0xb6aa78: add             x2, x2, HEAP, lsl #32
    // 0xb6aa7c: cmp             w2, NULL
    // 0xb6aa80: b.ne            #0xb6aa8c
    // 0xb6aa84: r0 = Null
    //     0xb6aa84: mov             x0, NULL
    // 0xb6aa88: b               #0xb6aad8
    // 0xb6aa8c: LoadField: r0 = r2->field_b
    //     0xb6aa8c: ldur            w0, [x2, #0xb]
    // 0xb6aa90: r1 = LoadInt32Instr(r0)
    //     0xb6aa90: sbfx            x1, x0, #1, #0x1f
    // 0xb6aa94: mov             x0, x1
    // 0xb6aa98: r1 = 0
    //     0xb6aa98: movz            x1, #0
    // 0xb6aa9c: cmp             x1, x0
    // 0xb6aaa0: b.hs            #0xb6b630
    // 0xb6aaa4: LoadField: r0 = r2->field_f
    //     0xb6aaa4: ldur            w0, [x2, #0xf]
    // 0xb6aaa8: DecompressPointer r0
    //     0xb6aaa8: add             x0, x0, HEAP, lsl #32
    // 0xb6aaac: LoadField: r1 = r0->field_f
    //     0xb6aaac: ldur            w1, [x0, #0xf]
    // 0xb6aab0: DecompressPointer r1
    //     0xb6aab0: add             x1, x1, HEAP, lsl #32
    // 0xb6aab4: LoadField: r0 = r1->field_7
    //     0xb6aab4: ldur            w0, [x1, #7]
    // 0xb6aab8: DecompressPointer r0
    //     0xb6aab8: add             x0, x0, HEAP, lsl #32
    // 0xb6aabc: cmp             w0, NULL
    // 0xb6aac0: b.ne            #0xb6aacc
    // 0xb6aac4: r0 = Null
    //     0xb6aac4: mov             x0, NULL
    // 0xb6aac8: b               #0xb6aad8
    // 0xb6aacc: LoadField: r1 = r0->field_b
    //     0xb6aacc: ldur            w1, [x0, #0xb]
    // 0xb6aad0: DecompressPointer r1
    //     0xb6aad0: add             x1, x1, HEAP, lsl #32
    // 0xb6aad4: mov             x0, x1
    // 0xb6aad8: cmp             w0, NULL
    // 0xb6aadc: b.ne            #0xb6aae4
    // 0xb6aae0: r0 = ""
    //     0xb6aae0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6aae4: stur            x0, [fp, #-0x20]
    // 0xb6aae8: r1 = Function '<anonymous closure>':.
    //     0xb6aae8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c18] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6aaec: ldr             x1, [x1, #0xc18]
    // 0xb6aaf0: r2 = Null
    //     0xb6aaf0: mov             x2, NULL
    // 0xb6aaf4: r0 = AllocateClosure()
    //     0xb6aaf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6aaf8: r1 = Function '<anonymous closure>':.
    //     0xb6aaf8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c20] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6aafc: ldr             x1, [x1, #0xc20]
    // 0xb6ab00: r2 = Null
    //     0xb6ab00: mov             x2, NULL
    // 0xb6ab04: stur            x0, [fp, #-0x50]
    // 0xb6ab08: r0 = AllocateClosure()
    //     0xb6ab08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6ab0c: stur            x0, [fp, #-0x58]
    // 0xb6ab10: r0 = CachedNetworkImage()
    //     0xb6ab10: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb6ab14: stur            x0, [fp, #-0x60]
    // 0xb6ab18: ldur            x16, [fp, #-0x50]
    // 0xb6ab1c: ldur            lr, [fp, #-0x58]
    // 0xb6ab20: stp             lr, x16, [SP, #0x18]
    // 0xb6ab24: r16 = inf
    //     0xb6ab24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb6ab28: ldr             x16, [x16, #0x9f8]
    // 0xb6ab2c: r30 = 336.000000
    //     0xb6ab2c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55530] 336
    //     0xb6ab30: ldr             lr, [lr, #0x530]
    // 0xb6ab34: stp             lr, x16, [SP, #8]
    // 0xb6ab38: r16 = Instance_BoxFit
    //     0xb6ab38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb6ab3c: ldr             x16, [x16, #0x118]
    // 0xb6ab40: str             x16, [SP]
    // 0xb6ab44: mov             x1, x0
    // 0xb6ab48: ldur            x2, [fp, #-0x20]
    // 0xb6ab4c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x3, fit, 0x6, height, 0x5, progressIndicatorBuilder, 0x2, width, 0x4, null]
    //     0xb6ab4c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55538] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x3, "fit", 0x6, "height", 0x5, "progressIndicatorBuilder", 0x2, "width", 0x4, Null]
    //     0xb6ab50: ldr             x4, [x4, #0x538]
    // 0xb6ab54: r0 = CachedNetworkImage()
    //     0xb6ab54: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb6ab58: r0 = ClipRRect()
    //     0xb6ab58: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb6ab5c: mov             x2, x0
    // 0xb6ab60: r0 = Instance_BorderRadius
    //     0xb6ab60: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb6ab64: ldr             x0, [x0, #0xe10]
    // 0xb6ab68: stur            x2, [fp, #-0x20]
    // 0xb6ab6c: StoreField: r2->field_f = r0
    //     0xb6ab6c: stur            w0, [x2, #0xf]
    // 0xb6ab70: r0 = Instance_Clip
    //     0xb6ab70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb6ab74: ldr             x0, [x0, #0x138]
    // 0xb6ab78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6ab78: stur            w0, [x2, #0x17]
    // 0xb6ab7c: ldur            x0, [fp, #-0x60]
    // 0xb6ab80: StoreField: r2->field_b = r0
    //     0xb6ab80: stur            w0, [x2, #0xb]
    // 0xb6ab84: ldur            x0, [fp, #-0x48]
    // 0xb6ab88: LoadField: r1 = r0->field_b
    //     0xb6ab88: ldur            w1, [x0, #0xb]
    // 0xb6ab8c: LoadField: r3 = r0->field_f
    //     0xb6ab8c: ldur            w3, [x0, #0xf]
    // 0xb6ab90: DecompressPointer r3
    //     0xb6ab90: add             x3, x3, HEAP, lsl #32
    // 0xb6ab94: LoadField: r4 = r3->field_b
    //     0xb6ab94: ldur            w4, [x3, #0xb]
    // 0xb6ab98: r3 = LoadInt32Instr(r1)
    //     0xb6ab98: sbfx            x3, x1, #1, #0x1f
    // 0xb6ab9c: stur            x3, [fp, #-0x18]
    // 0xb6aba0: r1 = LoadInt32Instr(r4)
    //     0xb6aba0: sbfx            x1, x4, #1, #0x1f
    // 0xb6aba4: cmp             x3, x1
    // 0xb6aba8: b.ne            #0xb6abb4
    // 0xb6abac: mov             x1, x0
    // 0xb6abb0: r0 = _growToNextCapacity()
    //     0xb6abb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6abb4: ldur            x2, [fp, #-0x48]
    // 0xb6abb8: ldur            x3, [fp, #-0x18]
    // 0xb6abbc: add             x0, x3, #1
    // 0xb6abc0: lsl             x1, x0, #1
    // 0xb6abc4: StoreField: r2->field_b = r1
    //     0xb6abc4: stur            w1, [x2, #0xb]
    // 0xb6abc8: LoadField: r1 = r2->field_f
    //     0xb6abc8: ldur            w1, [x2, #0xf]
    // 0xb6abcc: DecompressPointer r1
    //     0xb6abcc: add             x1, x1, HEAP, lsl #32
    // 0xb6abd0: ldur            x0, [fp, #-0x20]
    // 0xb6abd4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6abd4: add             x25, x1, x3, lsl #2
    //     0xb6abd8: add             x25, x25, #0xf
    //     0xb6abdc: str             w0, [x25]
    //     0xb6abe0: tbz             w0, #0, #0xb6abfc
    //     0xb6abe4: ldurb           w16, [x1, #-1]
    //     0xb6abe8: ldurb           w17, [x0, #-1]
    //     0xb6abec: and             x16, x17, x16, lsr #2
    //     0xb6abf0: tst             x16, HEAP, lsr #32
    //     0xb6abf4: b.eq            #0xb6abfc
    //     0xb6abf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6abfc: b               #0xb6ac04
    // 0xb6ac00: mov             x2, x3
    // 0xb6ac04: ldur            x0, [fp, #-0x28]
    // 0xb6ac08: LoadField: r1 = r0->field_97
    //     0xb6ac08: ldur            w1, [x0, #0x97]
    // 0xb6ac0c: DecompressPointer r1
    //     0xb6ac0c: add             x1, x1, HEAP, lsl #32
    // 0xb6ac10: cmp             w1, NULL
    // 0xb6ac14: b.ne            #0xb6ac1c
    // 0xb6ac18: r1 = ""
    //     0xb6ac18: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ac1c: ldur            x3, [fp, #-8]
    // 0xb6ac20: r0 = capitalizeFirstWord()
    //     0xb6ac20: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb6ac24: mov             x2, x0
    // 0xb6ac28: ldur            x0, [fp, #-8]
    // 0xb6ac2c: stur            x2, [fp, #-0x20]
    // 0xb6ac30: LoadField: r1 = r0->field_f
    //     0xb6ac30: ldur            w1, [x0, #0xf]
    // 0xb6ac34: DecompressPointer r1
    //     0xb6ac34: add             x1, x1, HEAP, lsl #32
    // 0xb6ac38: cmp             w1, NULL
    // 0xb6ac3c: b.eq            #0xb6b634
    // 0xb6ac40: r0 = of()
    //     0xb6ac40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ac44: LoadField: r1 = r0->field_87
    //     0xb6ac44: ldur            w1, [x0, #0x87]
    // 0xb6ac48: DecompressPointer r1
    //     0xb6ac48: add             x1, x1, HEAP, lsl #32
    // 0xb6ac4c: LoadField: r0 = r1->field_7
    //     0xb6ac4c: ldur            w0, [x1, #7]
    // 0xb6ac50: DecompressPointer r0
    //     0xb6ac50: add             x0, x0, HEAP, lsl #32
    // 0xb6ac54: r16 = 14.000000
    //     0xb6ac54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb6ac58: ldr             x16, [x16, #0x1d8]
    // 0xb6ac5c: r30 = Instance_Color
    //     0xb6ac5c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6ac60: stp             lr, x16, [SP]
    // 0xb6ac64: mov             x1, x0
    // 0xb6ac68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6ac68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6ac6c: ldr             x4, [x4, #0xaa0]
    // 0xb6ac70: r0 = copyWith()
    //     0xb6ac70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6ac74: stur            x0, [fp, #-0x50]
    // 0xb6ac78: r0 = Text()
    //     0xb6ac78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6ac7c: mov             x1, x0
    // 0xb6ac80: ldur            x0, [fp, #-0x20]
    // 0xb6ac84: stur            x1, [fp, #-0x58]
    // 0xb6ac88: StoreField: r1->field_b = r0
    //     0xb6ac88: stur            w0, [x1, #0xb]
    // 0xb6ac8c: ldur            x0, [fp, #-0x50]
    // 0xb6ac90: StoreField: r1->field_13 = r0
    //     0xb6ac90: stur            w0, [x1, #0x13]
    // 0xb6ac94: r0 = Padding()
    //     0xb6ac94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6ac98: mov             x2, x0
    // 0xb6ac9c: r0 = Instance_EdgeInsets
    //     0xb6ac9c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb6aca0: ldr             x0, [x0, #0xa98]
    // 0xb6aca4: stur            x2, [fp, #-0x20]
    // 0xb6aca8: StoreField: r2->field_f = r0
    //     0xb6aca8: stur            w0, [x2, #0xf]
    // 0xb6acac: ldur            x0, [fp, #-0x58]
    // 0xb6acb0: StoreField: r2->field_b = r0
    //     0xb6acb0: stur            w0, [x2, #0xb]
    // 0xb6acb4: ldur            x0, [fp, #-0x48]
    // 0xb6acb8: LoadField: r1 = r0->field_b
    //     0xb6acb8: ldur            w1, [x0, #0xb]
    // 0xb6acbc: LoadField: r3 = r0->field_f
    //     0xb6acbc: ldur            w3, [x0, #0xf]
    // 0xb6acc0: DecompressPointer r3
    //     0xb6acc0: add             x3, x3, HEAP, lsl #32
    // 0xb6acc4: LoadField: r4 = r3->field_b
    //     0xb6acc4: ldur            w4, [x3, #0xb]
    // 0xb6acc8: r3 = LoadInt32Instr(r1)
    //     0xb6acc8: sbfx            x3, x1, #1, #0x1f
    // 0xb6accc: stur            x3, [fp, #-0x18]
    // 0xb6acd0: r1 = LoadInt32Instr(r4)
    //     0xb6acd0: sbfx            x1, x4, #1, #0x1f
    // 0xb6acd4: cmp             x3, x1
    // 0xb6acd8: b.ne            #0xb6ace4
    // 0xb6acdc: mov             x1, x0
    // 0xb6ace0: r0 = _growToNextCapacity()
    //     0xb6ace0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6ace4: ldur            x2, [fp, #-0x48]
    // 0xb6ace8: ldur            x3, [fp, #-0x18]
    // 0xb6acec: add             x4, x3, #1
    // 0xb6acf0: stur            x4, [fp, #-0x68]
    // 0xb6acf4: lsl             x0, x4, #1
    // 0xb6acf8: StoreField: r2->field_b = r0
    //     0xb6acf8: stur            w0, [x2, #0xb]
    // 0xb6acfc: LoadField: r5 = r2->field_f
    //     0xb6acfc: ldur            w5, [x2, #0xf]
    // 0xb6ad00: DecompressPointer r5
    //     0xb6ad00: add             x5, x5, HEAP, lsl #32
    // 0xb6ad04: mov             x1, x5
    // 0xb6ad08: ldur            x0, [fp, #-0x20]
    // 0xb6ad0c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6ad0c: add             x25, x1, x3, lsl #2
    //     0xb6ad10: add             x25, x25, #0xf
    //     0xb6ad14: str             w0, [x25]
    //     0xb6ad18: tbz             w0, #0, #0xb6ad34
    //     0xb6ad1c: ldurb           w16, [x1, #-1]
    //     0xb6ad20: ldurb           w17, [x0, #-1]
    //     0xb6ad24: and             x16, x17, x16, lsr #2
    //     0xb6ad28: tst             x16, HEAP, lsr #32
    //     0xb6ad2c: b.eq            #0xb6ad34
    //     0xb6ad30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6ad34: LoadField: r0 = r5->field_b
    //     0xb6ad34: ldur            w0, [x5, #0xb]
    // 0xb6ad38: r1 = LoadInt32Instr(r0)
    //     0xb6ad38: sbfx            x1, x0, #1, #0x1f
    // 0xb6ad3c: cmp             x4, x1
    // 0xb6ad40: b.ne            #0xb6ad4c
    // 0xb6ad44: mov             x1, x2
    // 0xb6ad48: r0 = _growToNextCapacity()
    //     0xb6ad48: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6ad4c: ldur            x0, [fp, #-0x48]
    // 0xb6ad50: ldur            x1, [fp, #-0x68]
    // 0xb6ad54: ldur            x2, [fp, #-0x28]
    // 0xb6ad58: add             x3, x1, #1
    // 0xb6ad5c: lsl             x4, x3, #1
    // 0xb6ad60: StoreField: r0->field_b = r4
    //     0xb6ad60: stur            w4, [x0, #0xb]
    // 0xb6ad64: LoadField: r3 = r0->field_f
    //     0xb6ad64: ldur            w3, [x0, #0xf]
    // 0xb6ad68: DecompressPointer r3
    //     0xb6ad68: add             x3, x3, HEAP, lsl #32
    // 0xb6ad6c: add             x4, x3, x1, lsl #2
    // 0xb6ad70: r16 = Instance_SizedBox
    //     0xb6ad70: add             x16, PP, #0x55, lsl #12  ; [pp+0x55540] Obj!SizedBox@d67fe1
    //     0xb6ad74: ldr             x16, [x16, #0x540]
    // 0xb6ad78: StoreField: r4->field_f = r16
    //     0xb6ad78: stur            w16, [x4, #0xf]
    // 0xb6ad7c: LoadField: r1 = r2->field_9b
    //     0xb6ad7c: ldur            w1, [x2, #0x9b]
    // 0xb6ad80: DecompressPointer r1
    //     0xb6ad80: add             x1, x1, HEAP, lsl #32
    // 0xb6ad84: cmp             w1, NULL
    // 0xb6ad88: b.ne            #0xb6ad90
    // 0xb6ad8c: r1 = "0"
    //     0xb6ad8c: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xb6ad90: ldur            x3, [fp, #-8]
    // 0xb6ad94: r0 = parse()
    //     0xb6ad94: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb6ad98: ldur            x0, [fp, #-8]
    // 0xb6ad9c: stur            d0, [fp, #-0x78]
    // 0xb6ada0: LoadField: r1 = r0->field_f
    //     0xb6ada0: ldur            w1, [x0, #0xf]
    // 0xb6ada4: DecompressPointer r1
    //     0xb6ada4: add             x1, x1, HEAP, lsl #32
    // 0xb6ada8: cmp             w1, NULL
    // 0xb6adac: b.eq            #0xb6b638
    // 0xb6adb0: r0 = of()
    //     0xb6adb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6adb4: LoadField: r1 = r0->field_5b
    //     0xb6adb4: ldur            w1, [x0, #0x5b]
    // 0xb6adb8: DecompressPointer r1
    //     0xb6adb8: add             x1, x1, HEAP, lsl #32
    // 0xb6adbc: stur            x1, [fp, #-0x20]
    // 0xb6adc0: r0 = Icon()
    //     0xb6adc0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb6adc4: mov             x2, x0
    // 0xb6adc8: r0 = Instance_IconData
    //     0xb6adc8: add             x0, PP, #0x55, lsl #12  ; [pp+0x55548] Obj!IconData@d55401
    //     0xb6adcc: ldr             x0, [x0, #0x548]
    // 0xb6add0: stur            x2, [fp, #-0x50]
    // 0xb6add4: StoreField: r2->field_b = r0
    //     0xb6add4: stur            w0, [x2, #0xb]
    // 0xb6add8: r0 = 8.000000
    //     0xb6add8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb6addc: ldr             x0, [x0, #0x608]
    // 0xb6ade0: StoreField: r2->field_f = r0
    //     0xb6ade0: stur            w0, [x2, #0xf]
    // 0xb6ade4: ldur            x1, [fp, #-0x20]
    // 0xb6ade8: StoreField: r2->field_23 = r1
    //     0xb6ade8: stur            w1, [x2, #0x23]
    // 0xb6adec: ldur            x3, [fp, #-8]
    // 0xb6adf0: LoadField: r1 = r3->field_f
    //     0xb6adf0: ldur            w1, [x3, #0xf]
    // 0xb6adf4: DecompressPointer r1
    //     0xb6adf4: add             x1, x1, HEAP, lsl #32
    // 0xb6adf8: cmp             w1, NULL
    // 0xb6adfc: b.eq            #0xb6b63c
    // 0xb6ae00: r0 = of()
    //     0xb6ae00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ae04: LoadField: r1 = r0->field_5b
    //     0xb6ae04: ldur            w1, [x0, #0x5b]
    // 0xb6ae08: DecompressPointer r1
    //     0xb6ae08: add             x1, x1, HEAP, lsl #32
    // 0xb6ae0c: stur            x1, [fp, #-0x20]
    // 0xb6ae10: r0 = Icon()
    //     0xb6ae10: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb6ae14: mov             x2, x0
    // 0xb6ae18: r0 = Instance_IconData
    //     0xb6ae18: add             x0, PP, #0x55, lsl #12  ; [pp+0x55550] Obj!IconData@d553e1
    //     0xb6ae1c: ldr             x0, [x0, #0x550]
    // 0xb6ae20: stur            x2, [fp, #-0x58]
    // 0xb6ae24: StoreField: r2->field_b = r0
    //     0xb6ae24: stur            w0, [x2, #0xb]
    // 0xb6ae28: r0 = 8.000000
    //     0xb6ae28: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb6ae2c: ldr             x0, [x0, #0x608]
    // 0xb6ae30: StoreField: r2->field_f = r0
    //     0xb6ae30: stur            w0, [x2, #0xf]
    // 0xb6ae34: ldur            x1, [fp, #-0x20]
    // 0xb6ae38: StoreField: r2->field_23 = r1
    //     0xb6ae38: stur            w1, [x2, #0x23]
    // 0xb6ae3c: ldur            x3, [fp, #-8]
    // 0xb6ae40: LoadField: r1 = r3->field_f
    //     0xb6ae40: ldur            w1, [x3, #0xf]
    // 0xb6ae44: DecompressPointer r1
    //     0xb6ae44: add             x1, x1, HEAP, lsl #32
    // 0xb6ae48: cmp             w1, NULL
    // 0xb6ae4c: b.eq            #0xb6b640
    // 0xb6ae50: r0 = of()
    //     0xb6ae50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ae54: LoadField: r1 = r0->field_5b
    //     0xb6ae54: ldur            w1, [x0, #0x5b]
    // 0xb6ae58: DecompressPointer r1
    //     0xb6ae58: add             x1, x1, HEAP, lsl #32
    // 0xb6ae5c: stur            x1, [fp, #-0x20]
    // 0xb6ae60: r0 = Icon()
    //     0xb6ae60: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb6ae64: mov             x1, x0
    // 0xb6ae68: r0 = Instance_IconData
    //     0xb6ae68: add             x0, PP, #0x55, lsl #12  ; [pp+0x55558] Obj!IconData@d553c1
    //     0xb6ae6c: ldr             x0, [x0, #0x558]
    // 0xb6ae70: stur            x1, [fp, #-0x60]
    // 0xb6ae74: StoreField: r1->field_b = r0
    //     0xb6ae74: stur            w0, [x1, #0xb]
    // 0xb6ae78: r0 = 8.000000
    //     0xb6ae78: add             x0, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0xb6ae7c: ldr             x0, [x0, #0x608]
    // 0xb6ae80: StoreField: r1->field_f = r0
    //     0xb6ae80: stur            w0, [x1, #0xf]
    // 0xb6ae84: ldur            x0, [fp, #-0x20]
    // 0xb6ae88: StoreField: r1->field_23 = r0
    //     0xb6ae88: stur            w0, [x1, #0x23]
    // 0xb6ae8c: r0 = RatingWidget()
    //     0xb6ae8c: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb6ae90: mov             x3, x0
    // 0xb6ae94: ldur            x0, [fp, #-0x50]
    // 0xb6ae98: stur            x3, [fp, #-0x20]
    // 0xb6ae9c: StoreField: r3->field_7 = r0
    //     0xb6ae9c: stur            w0, [x3, #7]
    // 0xb6aea0: ldur            x0, [fp, #-0x58]
    // 0xb6aea4: StoreField: r3->field_b = r0
    //     0xb6aea4: stur            w0, [x3, #0xb]
    // 0xb6aea8: ldur            x0, [fp, #-0x60]
    // 0xb6aeac: StoreField: r3->field_f = r0
    //     0xb6aeac: stur            w0, [x3, #0xf]
    // 0xb6aeb0: r1 = Function '<anonymous closure>':.
    //     0xb6aeb0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c28] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb6aeb4: ldr             x1, [x1, #0xc28]
    // 0xb6aeb8: r2 = Null
    //     0xb6aeb8: mov             x2, NULL
    // 0xb6aebc: r0 = AllocateClosure()
    //     0xb6aebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6aec0: stur            x0, [fp, #-0x50]
    // 0xb6aec4: r0 = RatingBar()
    //     0xb6aec4: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb6aec8: mov             x2, x0
    // 0xb6aecc: ldur            x0, [fp, #-0x50]
    // 0xb6aed0: stur            x2, [fp, #-0x58]
    // 0xb6aed4: StoreField: r2->field_b = r0
    //     0xb6aed4: stur            w0, [x2, #0xb]
    // 0xb6aed8: r0 = true
    //     0xb6aed8: add             x0, NULL, #0x20  ; true
    // 0xb6aedc: StoreField: r2->field_1f = r0
    //     0xb6aedc: stur            w0, [x2, #0x1f]
    // 0xb6aee0: r3 = Instance_Axis
    //     0xb6aee0: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6aee4: StoreField: r2->field_23 = r3
    //     0xb6aee4: stur            w3, [x2, #0x23]
    // 0xb6aee8: StoreField: r2->field_27 = r0
    //     0xb6aee8: stur            w0, [x2, #0x27]
    // 0xb6aeec: d0 = 2.000000
    //     0xb6aeec: fmov            d0, #2.00000000
    // 0xb6aef0: StoreField: r2->field_2b = d0
    //     0xb6aef0: stur            d0, [x2, #0x2b]
    // 0xb6aef4: StoreField: r2->field_33 = r0
    //     0xb6aef4: stur            w0, [x2, #0x33]
    // 0xb6aef8: ldur            d0, [fp, #-0x78]
    // 0xb6aefc: StoreField: r2->field_37 = d0
    //     0xb6aefc: stur            d0, [x2, #0x37]
    // 0xb6af00: r1 = 5
    //     0xb6af00: movz            x1, #0x5
    // 0xb6af04: StoreField: r2->field_3f = r1
    //     0xb6af04: stur            x1, [x2, #0x3f]
    // 0xb6af08: r1 = Instance_EdgeInsets
    //     0xb6af08: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb6af0c: StoreField: r2->field_47 = r1
    //     0xb6af0c: stur            w1, [x2, #0x47]
    // 0xb6af10: d0 = 18.000000
    //     0xb6af10: fmov            d0, #18.00000000
    // 0xb6af14: StoreField: r2->field_4b = d0
    //     0xb6af14: stur            d0, [x2, #0x4b]
    // 0xb6af18: StoreField: r2->field_53 = rZR
    //     0xb6af18: stur            xzr, [x2, #0x53]
    // 0xb6af1c: r1 = false
    //     0xb6af1c: add             x1, NULL, #0x30  ; false
    // 0xb6af20: StoreField: r2->field_5b = r1
    //     0xb6af20: stur            w1, [x2, #0x5b]
    // 0xb6af24: StoreField: r2->field_5f = r1
    //     0xb6af24: stur            w1, [x2, #0x5f]
    // 0xb6af28: ldur            x1, [fp, #-0x20]
    // 0xb6af2c: StoreField: r2->field_67 = r1
    //     0xb6af2c: stur            w1, [x2, #0x67]
    // 0xb6af30: ldur            x4, [fp, #-0x28]
    // 0xb6af34: LoadField: r1 = r4->field_a7
    //     0xb6af34: ldur            w1, [x4, #0xa7]
    // 0xb6af38: DecompressPointer r1
    //     0xb6af38: add             x1, x1, HEAP, lsl #32
    // 0xb6af3c: cmp             w1, NULL
    // 0xb6af40: b.ne            #0xb6af4c
    // 0xb6af44: r7 = ""
    //     0xb6af44: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6af48: b               #0xb6af50
    // 0xb6af4c: mov             x7, x1
    // 0xb6af50: ldur            x5, [fp, #-8]
    // 0xb6af54: ldur            x6, [fp, #-0x48]
    // 0xb6af58: stur            x7, [fp, #-0x20]
    // 0xb6af5c: LoadField: r1 = r5->field_f
    //     0xb6af5c: ldur            w1, [x5, #0xf]
    // 0xb6af60: DecompressPointer r1
    //     0xb6af60: add             x1, x1, HEAP, lsl #32
    // 0xb6af64: cmp             w1, NULL
    // 0xb6af68: b.eq            #0xb6b644
    // 0xb6af6c: r0 = of()
    //     0xb6af6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6af70: LoadField: r1 = r0->field_87
    //     0xb6af70: ldur            w1, [x0, #0x87]
    // 0xb6af74: DecompressPointer r1
    //     0xb6af74: add             x1, x1, HEAP, lsl #32
    // 0xb6af78: LoadField: r0 = r1->field_2b
    //     0xb6af78: ldur            w0, [x1, #0x2b]
    // 0xb6af7c: DecompressPointer r0
    //     0xb6af7c: add             x0, x0, HEAP, lsl #32
    // 0xb6af80: stur            x0, [fp, #-0x50]
    // 0xb6af84: r1 = Instance_Color
    //     0xb6af84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6af88: d0 = 0.700000
    //     0xb6af88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6af8c: ldr             d0, [x17, #0xf48]
    // 0xb6af90: r0 = withOpacity()
    //     0xb6af90: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6af94: r16 = 14.000000
    //     0xb6af94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb6af98: ldr             x16, [x16, #0x1d8]
    // 0xb6af9c: stp             x0, x16, [SP]
    // 0xb6afa0: ldur            x1, [fp, #-0x50]
    // 0xb6afa4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6afa4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6afa8: ldr             x4, [x4, #0xaa0]
    // 0xb6afac: r0 = copyWith()
    //     0xb6afac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6afb0: stur            x0, [fp, #-0x50]
    // 0xb6afb4: r0 = Text()
    //     0xb6afb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6afb8: mov             x3, x0
    // 0xb6afbc: ldur            x0, [fp, #-0x20]
    // 0xb6afc0: stur            x3, [fp, #-0x60]
    // 0xb6afc4: StoreField: r3->field_b = r0
    //     0xb6afc4: stur            w0, [x3, #0xb]
    // 0xb6afc8: ldur            x0, [fp, #-0x50]
    // 0xb6afcc: StoreField: r3->field_13 = r0
    //     0xb6afcc: stur            w0, [x3, #0x13]
    // 0xb6afd0: r1 = Null
    //     0xb6afd0: mov             x1, NULL
    // 0xb6afd4: r2 = 6
    //     0xb6afd4: movz            x2, #0x6
    // 0xb6afd8: r0 = AllocateArray()
    //     0xb6afd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6afdc: mov             x2, x0
    // 0xb6afe0: ldur            x0, [fp, #-0x58]
    // 0xb6afe4: stur            x2, [fp, #-0x20]
    // 0xb6afe8: StoreField: r2->field_f = r0
    //     0xb6afe8: stur            w0, [x2, #0xf]
    // 0xb6afec: r16 = Instance_SizedBox
    //     0xb6afec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xb6aff0: ldr             x16, [x16, #0x940]
    // 0xb6aff4: StoreField: r2->field_13 = r16
    //     0xb6aff4: stur            w16, [x2, #0x13]
    // 0xb6aff8: ldur            x0, [fp, #-0x60]
    // 0xb6affc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6affc: stur            w0, [x2, #0x17]
    // 0xb6b000: r1 = <Widget>
    //     0xb6b000: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6b004: r0 = AllocateGrowableArray()
    //     0xb6b004: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6b008: mov             x1, x0
    // 0xb6b00c: ldur            x0, [fp, #-0x20]
    // 0xb6b010: stur            x1, [fp, #-0x50]
    // 0xb6b014: StoreField: r1->field_f = r0
    //     0xb6b014: stur            w0, [x1, #0xf]
    // 0xb6b018: r0 = 6
    //     0xb6b018: movz            x0, #0x6
    // 0xb6b01c: StoreField: r1->field_b = r0
    //     0xb6b01c: stur            w0, [x1, #0xb]
    // 0xb6b020: r0 = Row()
    //     0xb6b020: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6b024: mov             x1, x0
    // 0xb6b028: r0 = Instance_Axis
    //     0xb6b028: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6b02c: stur            x1, [fp, #-0x20]
    // 0xb6b030: StoreField: r1->field_f = r0
    //     0xb6b030: stur            w0, [x1, #0xf]
    // 0xb6b034: r0 = Instance_MainAxisAlignment
    //     0xb6b034: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6b038: ldr             x0, [x0, #0xa08]
    // 0xb6b03c: StoreField: r1->field_13 = r0
    //     0xb6b03c: stur            w0, [x1, #0x13]
    // 0xb6b040: r2 = Instance_MainAxisSize
    //     0xb6b040: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6b044: ldr             x2, [x2, #0xa10]
    // 0xb6b048: ArrayStore: r1[0] = r2  ; List_4
    //     0xb6b048: stur            w2, [x1, #0x17]
    // 0xb6b04c: r3 = Instance_CrossAxisAlignment
    //     0xb6b04c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6b050: ldr             x3, [x3, #0xa18]
    // 0xb6b054: StoreField: r1->field_1b = r3
    //     0xb6b054: stur            w3, [x1, #0x1b]
    // 0xb6b058: r3 = Instance_VerticalDirection
    //     0xb6b058: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6b05c: ldr             x3, [x3, #0xa20]
    // 0xb6b060: StoreField: r1->field_23 = r3
    //     0xb6b060: stur            w3, [x1, #0x23]
    // 0xb6b064: r4 = Instance_Clip
    //     0xb6b064: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6b068: ldr             x4, [x4, #0x38]
    // 0xb6b06c: StoreField: r1->field_2b = r4
    //     0xb6b06c: stur            w4, [x1, #0x2b]
    // 0xb6b070: StoreField: r1->field_2f = rZR
    //     0xb6b070: stur            xzr, [x1, #0x2f]
    // 0xb6b074: ldur            x5, [fp, #-0x50]
    // 0xb6b078: StoreField: r1->field_b = r5
    //     0xb6b078: stur            w5, [x1, #0xb]
    // 0xb6b07c: r0 = Padding()
    //     0xb6b07c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6b080: mov             x2, x0
    // 0xb6b084: r0 = Instance_EdgeInsets
    //     0xb6b084: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb6b088: ldr             x0, [x0, #0x668]
    // 0xb6b08c: stur            x2, [fp, #-0x50]
    // 0xb6b090: StoreField: r2->field_f = r0
    //     0xb6b090: stur            w0, [x2, #0xf]
    // 0xb6b094: ldur            x0, [fp, #-0x20]
    // 0xb6b098: StoreField: r2->field_b = r0
    //     0xb6b098: stur            w0, [x2, #0xb]
    // 0xb6b09c: ldur            x0, [fp, #-0x48]
    // 0xb6b0a0: LoadField: r1 = r0->field_b
    //     0xb6b0a0: ldur            w1, [x0, #0xb]
    // 0xb6b0a4: LoadField: r3 = r0->field_f
    //     0xb6b0a4: ldur            w3, [x0, #0xf]
    // 0xb6b0a8: DecompressPointer r3
    //     0xb6b0a8: add             x3, x3, HEAP, lsl #32
    // 0xb6b0ac: LoadField: r4 = r3->field_b
    //     0xb6b0ac: ldur            w4, [x3, #0xb]
    // 0xb6b0b0: r3 = LoadInt32Instr(r1)
    //     0xb6b0b0: sbfx            x3, x1, #1, #0x1f
    // 0xb6b0b4: stur            x3, [fp, #-0x18]
    // 0xb6b0b8: r1 = LoadInt32Instr(r4)
    //     0xb6b0b8: sbfx            x1, x4, #1, #0x1f
    // 0xb6b0bc: cmp             x3, x1
    // 0xb6b0c0: b.ne            #0xb6b0cc
    // 0xb6b0c4: mov             x1, x0
    // 0xb6b0c8: r0 = _growToNextCapacity()
    //     0xb6b0c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6b0cc: ldur            x2, [fp, #-0x48]
    // 0xb6b0d0: ldur            x3, [fp, #-0x18]
    // 0xb6b0d4: ldur            x4, [fp, #-0x28]
    // 0xb6b0d8: add             x0, x3, #1
    // 0xb6b0dc: lsl             x1, x0, #1
    // 0xb6b0e0: StoreField: r2->field_b = r1
    //     0xb6b0e0: stur            w1, [x2, #0xb]
    // 0xb6b0e4: LoadField: r1 = r2->field_f
    //     0xb6b0e4: ldur            w1, [x2, #0xf]
    // 0xb6b0e8: DecompressPointer r1
    //     0xb6b0e8: add             x1, x1, HEAP, lsl #32
    // 0xb6b0ec: ldur            x0, [fp, #-0x50]
    // 0xb6b0f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6b0f0: add             x25, x1, x3, lsl #2
    //     0xb6b0f4: add             x25, x25, #0xf
    //     0xb6b0f8: str             w0, [x25]
    //     0xb6b0fc: tbz             w0, #0, #0xb6b118
    //     0xb6b100: ldurb           w16, [x1, #-1]
    //     0xb6b104: ldurb           w17, [x0, #-1]
    //     0xb6b108: and             x16, x17, x16, lsr #2
    //     0xb6b10c: tst             x16, HEAP, lsr #32
    //     0xb6b110: b.eq            #0xb6b118
    //     0xb6b114: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6b118: LoadField: r1 = r4->field_9f
    //     0xb6b118: ldur            w1, [x4, #0x9f]
    // 0xb6b11c: DecompressPointer r1
    //     0xb6b11c: add             x1, x1, HEAP, lsl #32
    // 0xb6b120: cmp             w1, NULL
    // 0xb6b124: b.ne            #0xb6b130
    // 0xb6b128: r0 = Null
    //     0xb6b128: mov             x0, NULL
    // 0xb6b12c: b               #0xb6b134
    // 0xb6b130: r0 = trim()
    //     0xb6b130: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb6b134: cmp             w0, NULL
    // 0xb6b138: b.ne            #0xb6b140
    // 0xb6b13c: r0 = ""
    //     0xb6b13c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6b140: ldur            x1, [fp, #-0x10]
    // 0xb6b144: stur            x0, [fp, #-0x20]
    // 0xb6b148: r0 = value()
    //     0xb6b148: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6b14c: tbnz            w0, #4, #0xb6b158
    // 0xb6b150: r0 = Null
    //     0xb6b150: mov             x0, NULL
    // 0xb6b154: b               #0xb6b15c
    // 0xb6b158: r0 = 4
    //     0xb6b158: movz            x0, #0x4
    // 0xb6b15c: ldur            x1, [fp, #-0x10]
    // 0xb6b160: stur            x0, [fp, #-0x50]
    // 0xb6b164: r0 = value()
    //     0xb6b164: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6b168: tbnz            w0, #4, #0xb6b178
    // 0xb6b16c: r5 = Instance_TextOverflow
    //     0xb6b16c: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xb6b170: ldr             x5, [x5, #0x3a8]
    // 0xb6b174: b               #0xb6b180
    // 0xb6b178: r5 = Instance_TextOverflow
    //     0xb6b178: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb6b17c: ldr             x5, [x5, #0xe10]
    // 0xb6b180: ldur            x4, [fp, #-8]
    // 0xb6b184: ldur            x2, [fp, #-0x20]
    // 0xb6b188: ldur            x0, [fp, #-0x50]
    // 0xb6b18c: ldur            x3, [fp, #-0x28]
    // 0xb6b190: stur            x5, [fp, #-0x58]
    // 0xb6b194: LoadField: r1 = r4->field_f
    //     0xb6b194: ldur            w1, [x4, #0xf]
    // 0xb6b198: DecompressPointer r1
    //     0xb6b198: add             x1, x1, HEAP, lsl #32
    // 0xb6b19c: cmp             w1, NULL
    // 0xb6b1a0: b.eq            #0xb6b648
    // 0xb6b1a4: r0 = of()
    //     0xb6b1a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6b1a8: LoadField: r1 = r0->field_87
    //     0xb6b1a8: ldur            w1, [x0, #0x87]
    // 0xb6b1ac: DecompressPointer r1
    //     0xb6b1ac: add             x1, x1, HEAP, lsl #32
    // 0xb6b1b0: LoadField: r0 = r1->field_2b
    //     0xb6b1b0: ldur            w0, [x1, #0x2b]
    // 0xb6b1b4: DecompressPointer r0
    //     0xb6b1b4: add             x0, x0, HEAP, lsl #32
    // 0xb6b1b8: LoadField: r1 = r0->field_13
    //     0xb6b1b8: ldur            w1, [x0, #0x13]
    // 0xb6b1bc: DecompressPointer r1
    //     0xb6b1bc: add             x1, x1, HEAP, lsl #32
    // 0xb6b1c0: stur            x1, [fp, #-0x60]
    // 0xb6b1c4: r0 = TextStyle()
    //     0xb6b1c4: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb6b1c8: mov             x1, x0
    // 0xb6b1cc: r0 = true
    //     0xb6b1cc: add             x0, NULL, #0x20  ; true
    // 0xb6b1d0: stur            x1, [fp, #-0x70]
    // 0xb6b1d4: StoreField: r1->field_7 = r0
    //     0xb6b1d4: stur            w0, [x1, #7]
    // 0xb6b1d8: r2 = Instance_Color
    //     0xb6b1d8: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6b1dc: StoreField: r1->field_b = r2
    //     0xb6b1dc: stur            w2, [x1, #0xb]
    // 0xb6b1e0: r2 = 12.000000
    //     0xb6b1e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb6b1e4: ldr             x2, [x2, #0x9e8]
    // 0xb6b1e8: StoreField: r1->field_1f = r2
    //     0xb6b1e8: stur            w2, [x1, #0x1f]
    // 0xb6b1ec: ldur            x2, [fp, #-0x60]
    // 0xb6b1f0: StoreField: r1->field_13 = r2
    //     0xb6b1f0: stur            w2, [x1, #0x13]
    // 0xb6b1f4: r0 = Text()
    //     0xb6b1f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6b1f8: mov             x3, x0
    // 0xb6b1fc: ldur            x0, [fp, #-0x20]
    // 0xb6b200: stur            x3, [fp, #-0x60]
    // 0xb6b204: StoreField: r3->field_b = r0
    //     0xb6b204: stur            w0, [x3, #0xb]
    // 0xb6b208: ldur            x0, [fp, #-0x70]
    // 0xb6b20c: StoreField: r3->field_13 = r0
    //     0xb6b20c: stur            w0, [x3, #0x13]
    // 0xb6b210: r0 = Instance_TextAlign
    //     0xb6b210: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb6b214: StoreField: r3->field_1b = r0
    //     0xb6b214: stur            w0, [x3, #0x1b]
    // 0xb6b218: ldur            x0, [fp, #-0x58]
    // 0xb6b21c: StoreField: r3->field_2b = r0
    //     0xb6b21c: stur            w0, [x3, #0x2b]
    // 0xb6b220: ldur            x0, [fp, #-0x50]
    // 0xb6b224: StoreField: r3->field_37 = r0
    //     0xb6b224: stur            w0, [x3, #0x37]
    // 0xb6b228: r1 = Null
    //     0xb6b228: mov             x1, NULL
    // 0xb6b22c: r2 = 2
    //     0xb6b22c: movz            x2, #0x2
    // 0xb6b230: r0 = AllocateArray()
    //     0xb6b230: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6b234: mov             x2, x0
    // 0xb6b238: ldur            x0, [fp, #-0x60]
    // 0xb6b23c: stur            x2, [fp, #-0x20]
    // 0xb6b240: StoreField: r2->field_f = r0
    //     0xb6b240: stur            w0, [x2, #0xf]
    // 0xb6b244: r1 = <Widget>
    //     0xb6b244: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6b248: r0 = AllocateGrowableArray()
    //     0xb6b248: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6b24c: mov             x2, x0
    // 0xb6b250: ldur            x0, [fp, #-0x20]
    // 0xb6b254: stur            x2, [fp, #-0x50]
    // 0xb6b258: StoreField: r2->field_f = r0
    //     0xb6b258: stur            w0, [x2, #0xf]
    // 0xb6b25c: r0 = 2
    //     0xb6b25c: movz            x0, #0x2
    // 0xb6b260: StoreField: r2->field_b = r0
    //     0xb6b260: stur            w0, [x2, #0xb]
    // 0xb6b264: ldur            x0, [fp, #-0x28]
    // 0xb6b268: LoadField: r1 = r0->field_9f
    //     0xb6b268: ldur            w1, [x0, #0x9f]
    // 0xb6b26c: DecompressPointer r1
    //     0xb6b26c: add             x1, x1, HEAP, lsl #32
    // 0xb6b270: cmp             w1, NULL
    // 0xb6b274: b.ne            #0xb6b280
    // 0xb6b278: r0 = Null
    //     0xb6b278: mov             x0, NULL
    // 0xb6b27c: b               #0xb6b284
    // 0xb6b280: r0 = trim()
    //     0xb6b280: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb6b284: cmp             w0, NULL
    // 0xb6b288: b.ne            #0xb6b294
    // 0xb6b28c: r1 = ""
    //     0xb6b28c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6b290: b               #0xb6b298
    // 0xb6b294: mov             x1, x0
    // 0xb6b298: ldur            x0, [fp, #-8]
    // 0xb6b29c: LoadField: r2 = r0->field_f
    //     0xb6b29c: ldur            w2, [x0, #0xf]
    // 0xb6b2a0: DecompressPointer r2
    //     0xb6b2a0: add             x2, x2, HEAP, lsl #32
    // 0xb6b2a4: cmp             w2, NULL
    // 0xb6b2a8: b.eq            #0xb6b64c
    // 0xb6b2ac: r0 = TextExceeds.textExceedsLines()
    //     0xb6b2ac: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xb6b2b0: tbnz            w0, #4, #0xb6b440
    // 0xb6b2b4: ldur            x1, [fp, #-0x10]
    // 0xb6b2b8: r0 = value()
    //     0xb6b2b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6b2bc: tbnz            w0, #4, #0xb6b2cc
    // 0xb6b2c0: r3 = "Know Less"
    //     0xb6b2c0: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xb6b2c4: ldr             x3, [x3, #0x1d0]
    // 0xb6b2c8: b               #0xb6b2d4
    // 0xb6b2cc: r3 = "Know more"
    //     0xb6b2cc: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xb6b2d0: ldr             x3, [x3, #0x20]
    // 0xb6b2d4: ldur            x0, [fp, #-8]
    // 0xb6b2d8: ldur            x2, [fp, #-0x50]
    // 0xb6b2dc: stur            x3, [fp, #-0x10]
    // 0xb6b2e0: LoadField: r1 = r0->field_f
    //     0xb6b2e0: ldur            w1, [x0, #0xf]
    // 0xb6b2e4: DecompressPointer r1
    //     0xb6b2e4: add             x1, x1, HEAP, lsl #32
    // 0xb6b2e8: cmp             w1, NULL
    // 0xb6b2ec: b.eq            #0xb6b650
    // 0xb6b2f0: r0 = of()
    //     0xb6b2f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6b2f4: LoadField: r1 = r0->field_87
    //     0xb6b2f4: ldur            w1, [x0, #0x87]
    // 0xb6b2f8: DecompressPointer r1
    //     0xb6b2f8: add             x1, x1, HEAP, lsl #32
    // 0xb6b2fc: LoadField: r0 = r1->field_7
    //     0xb6b2fc: ldur            w0, [x1, #7]
    // 0xb6b300: DecompressPointer r0
    //     0xb6b300: add             x0, x0, HEAP, lsl #32
    // 0xb6b304: ldur            x1, [fp, #-8]
    // 0xb6b308: stur            x0, [fp, #-0x20]
    // 0xb6b30c: LoadField: r2 = r1->field_f
    //     0xb6b30c: ldur            w2, [x1, #0xf]
    // 0xb6b310: DecompressPointer r2
    //     0xb6b310: add             x2, x2, HEAP, lsl #32
    // 0xb6b314: cmp             w2, NULL
    // 0xb6b318: b.eq            #0xb6b654
    // 0xb6b31c: mov             x1, x2
    // 0xb6b320: r0 = of()
    //     0xb6b320: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6b324: LoadField: r1 = r0->field_5b
    //     0xb6b324: ldur            w1, [x0, #0x5b]
    // 0xb6b328: DecompressPointer r1
    //     0xb6b328: add             x1, x1, HEAP, lsl #32
    // 0xb6b32c: r16 = 12.000000
    //     0xb6b32c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb6b330: ldr             x16, [x16, #0x9e8]
    // 0xb6b334: stp             x1, x16, [SP, #8]
    // 0xb6b338: r16 = Instance_TextDecoration
    //     0xb6b338: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb6b33c: ldr             x16, [x16, #0x10]
    // 0xb6b340: str             x16, [SP]
    // 0xb6b344: ldur            x1, [fp, #-0x20]
    // 0xb6b348: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb6b348: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb6b34c: ldr             x4, [x4, #0xe38]
    // 0xb6b350: r0 = copyWith()
    //     0xb6b350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6b354: stur            x0, [fp, #-8]
    // 0xb6b358: r0 = Text()
    //     0xb6b358: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6b35c: mov             x1, x0
    // 0xb6b360: ldur            x0, [fp, #-0x10]
    // 0xb6b364: stur            x1, [fp, #-0x20]
    // 0xb6b368: StoreField: r1->field_b = r0
    //     0xb6b368: stur            w0, [x1, #0xb]
    // 0xb6b36c: ldur            x0, [fp, #-8]
    // 0xb6b370: StoreField: r1->field_13 = r0
    //     0xb6b370: stur            w0, [x1, #0x13]
    // 0xb6b374: r0 = Padding()
    //     0xb6b374: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6b378: mov             x1, x0
    // 0xb6b37c: r0 = Instance_EdgeInsets
    //     0xb6b37c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb6b380: ldr             x0, [x0, #0x668]
    // 0xb6b384: stur            x1, [fp, #-8]
    // 0xb6b388: StoreField: r1->field_f = r0
    //     0xb6b388: stur            w0, [x1, #0xf]
    // 0xb6b38c: ldur            x0, [fp, #-0x20]
    // 0xb6b390: StoreField: r1->field_b = r0
    //     0xb6b390: stur            w0, [x1, #0xb]
    // 0xb6b394: r0 = GestureDetector()
    //     0xb6b394: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb6b398: ldur            x2, [fp, #-0x30]
    // 0xb6b39c: r1 = Function '<anonymous closure>':.
    //     0xb6b39c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c30] AnonymousClosure: (0xb6b658), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xb6a834)
    //     0xb6b3a0: ldr             x1, [x1, #0xc30]
    // 0xb6b3a4: stur            x0, [fp, #-0x10]
    // 0xb6b3a8: r0 = AllocateClosure()
    //     0xb6b3a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6b3ac: ldur            x16, [fp, #-8]
    // 0xb6b3b0: stp             x16, x0, [SP]
    // 0xb6b3b4: ldur            x1, [fp, #-0x10]
    // 0xb6b3b8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb6b3b8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb6b3bc: ldr             x4, [x4, #0xaf0]
    // 0xb6b3c0: r0 = GestureDetector()
    //     0xb6b3c0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb6b3c4: ldur            x0, [fp, #-0x50]
    // 0xb6b3c8: LoadField: r1 = r0->field_b
    //     0xb6b3c8: ldur            w1, [x0, #0xb]
    // 0xb6b3cc: LoadField: r2 = r0->field_f
    //     0xb6b3cc: ldur            w2, [x0, #0xf]
    // 0xb6b3d0: DecompressPointer r2
    //     0xb6b3d0: add             x2, x2, HEAP, lsl #32
    // 0xb6b3d4: LoadField: r3 = r2->field_b
    //     0xb6b3d4: ldur            w3, [x2, #0xb]
    // 0xb6b3d8: r2 = LoadInt32Instr(r1)
    //     0xb6b3d8: sbfx            x2, x1, #1, #0x1f
    // 0xb6b3dc: stur            x2, [fp, #-0x18]
    // 0xb6b3e0: r1 = LoadInt32Instr(r3)
    //     0xb6b3e0: sbfx            x1, x3, #1, #0x1f
    // 0xb6b3e4: cmp             x2, x1
    // 0xb6b3e8: b.ne            #0xb6b3f4
    // 0xb6b3ec: mov             x1, x0
    // 0xb6b3f0: r0 = _growToNextCapacity()
    //     0xb6b3f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6b3f4: ldur            x2, [fp, #-0x50]
    // 0xb6b3f8: ldur            x3, [fp, #-0x18]
    // 0xb6b3fc: add             x0, x3, #1
    // 0xb6b400: lsl             x1, x0, #1
    // 0xb6b404: StoreField: r2->field_b = r1
    //     0xb6b404: stur            w1, [x2, #0xb]
    // 0xb6b408: LoadField: r1 = r2->field_f
    //     0xb6b408: ldur            w1, [x2, #0xf]
    // 0xb6b40c: DecompressPointer r1
    //     0xb6b40c: add             x1, x1, HEAP, lsl #32
    // 0xb6b410: ldur            x0, [fp, #-0x10]
    // 0xb6b414: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6b414: add             x25, x1, x3, lsl #2
    //     0xb6b418: add             x25, x25, #0xf
    //     0xb6b41c: str             w0, [x25]
    //     0xb6b420: tbz             w0, #0, #0xb6b43c
    //     0xb6b424: ldurb           w16, [x1, #-1]
    //     0xb6b428: ldurb           w17, [x0, #-1]
    //     0xb6b42c: and             x16, x17, x16, lsr #2
    //     0xb6b430: tst             x16, HEAP, lsr #32
    //     0xb6b434: b.eq            #0xb6b43c
    //     0xb6b438: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6b43c: b               #0xb6b444
    // 0xb6b440: ldur            x2, [fp, #-0x50]
    // 0xb6b444: ldur            x1, [fp, #-0x48]
    // 0xb6b448: r0 = Column()
    //     0xb6b448: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6b44c: mov             x1, x0
    // 0xb6b450: r0 = Instance_Axis
    //     0xb6b450: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6b454: stur            x1, [fp, #-8]
    // 0xb6b458: StoreField: r1->field_f = r0
    //     0xb6b458: stur            w0, [x1, #0xf]
    // 0xb6b45c: r0 = Instance_MainAxisAlignment
    //     0xb6b45c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6b460: ldr             x0, [x0, #0xa08]
    // 0xb6b464: StoreField: r1->field_13 = r0
    //     0xb6b464: stur            w0, [x1, #0x13]
    // 0xb6b468: r0 = Instance_MainAxisSize
    //     0xb6b468: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6b46c: ldr             x0, [x0, #0xa10]
    // 0xb6b470: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6b470: stur            w0, [x1, #0x17]
    // 0xb6b474: r0 = Instance_CrossAxisAlignment
    //     0xb6b474: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb6b478: ldr             x0, [x0, #0x890]
    // 0xb6b47c: StoreField: r1->field_1b = r0
    //     0xb6b47c: stur            w0, [x1, #0x1b]
    // 0xb6b480: r0 = Instance_VerticalDirection
    //     0xb6b480: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6b484: ldr             x0, [x0, #0xa20]
    // 0xb6b488: StoreField: r1->field_23 = r0
    //     0xb6b488: stur            w0, [x1, #0x23]
    // 0xb6b48c: r0 = Instance_Clip
    //     0xb6b48c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6b490: ldr             x0, [x0, #0x38]
    // 0xb6b494: StoreField: r1->field_2b = r0
    //     0xb6b494: stur            w0, [x1, #0x2b]
    // 0xb6b498: StoreField: r1->field_2f = rZR
    //     0xb6b498: stur            xzr, [x1, #0x2f]
    // 0xb6b49c: ldur            x0, [fp, #-0x50]
    // 0xb6b4a0: StoreField: r1->field_b = r0
    //     0xb6b4a0: stur            w0, [x1, #0xb]
    // 0xb6b4a4: r0 = Padding()
    //     0xb6b4a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6b4a8: mov             x2, x0
    // 0xb6b4ac: r0 = Instance_EdgeInsets
    //     0xb6b4ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb6b4b0: ldr             x0, [x0, #0xd0]
    // 0xb6b4b4: stur            x2, [fp, #-0x10]
    // 0xb6b4b8: StoreField: r2->field_f = r0
    //     0xb6b4b8: stur            w0, [x2, #0xf]
    // 0xb6b4bc: ldur            x0, [fp, #-8]
    // 0xb6b4c0: StoreField: r2->field_b = r0
    //     0xb6b4c0: stur            w0, [x2, #0xb]
    // 0xb6b4c4: ldur            x0, [fp, #-0x48]
    // 0xb6b4c8: LoadField: r1 = r0->field_b
    //     0xb6b4c8: ldur            w1, [x0, #0xb]
    // 0xb6b4cc: LoadField: r3 = r0->field_f
    //     0xb6b4cc: ldur            w3, [x0, #0xf]
    // 0xb6b4d0: DecompressPointer r3
    //     0xb6b4d0: add             x3, x3, HEAP, lsl #32
    // 0xb6b4d4: LoadField: r4 = r3->field_b
    //     0xb6b4d4: ldur            w4, [x3, #0xb]
    // 0xb6b4d8: r3 = LoadInt32Instr(r1)
    //     0xb6b4d8: sbfx            x3, x1, #1, #0x1f
    // 0xb6b4dc: stur            x3, [fp, #-0x18]
    // 0xb6b4e0: r1 = LoadInt32Instr(r4)
    //     0xb6b4e0: sbfx            x1, x4, #1, #0x1f
    // 0xb6b4e4: cmp             x3, x1
    // 0xb6b4e8: b.ne            #0xb6b4f4
    // 0xb6b4ec: mov             x1, x0
    // 0xb6b4f0: r0 = _growToNextCapacity()
    //     0xb6b4f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6b4f4: ldur            x5, [fp, #-0x38]
    // 0xb6b4f8: ldur            x4, [fp, #-0x40]
    // 0xb6b4fc: ldur            x2, [fp, #-0x48]
    // 0xb6b500: ldur            x3, [fp, #-0x18]
    // 0xb6b504: add             x0, x3, #1
    // 0xb6b508: lsl             x1, x0, #1
    // 0xb6b50c: StoreField: r2->field_b = r1
    //     0xb6b50c: stur            w1, [x2, #0xb]
    // 0xb6b510: LoadField: r1 = r2->field_f
    //     0xb6b510: ldur            w1, [x2, #0xf]
    // 0xb6b514: DecompressPointer r1
    //     0xb6b514: add             x1, x1, HEAP, lsl #32
    // 0xb6b518: ldur            x0, [fp, #-0x10]
    // 0xb6b51c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6b51c: add             x25, x1, x3, lsl #2
    //     0xb6b520: add             x25, x25, #0xf
    //     0xb6b524: str             w0, [x25]
    //     0xb6b528: tbz             w0, #0, #0xb6b544
    //     0xb6b52c: ldurb           w16, [x1, #-1]
    //     0xb6b530: ldurb           w17, [x0, #-1]
    //     0xb6b534: and             x16, x17, x16, lsr #2
    //     0xb6b538: tst             x16, HEAP, lsr #32
    //     0xb6b53c: b.eq            #0xb6b544
    //     0xb6b540: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6b544: r0 = ListView()
    //     0xb6b544: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb6b548: stur            x0, [fp, #-8]
    // 0xb6b54c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb6b54c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb6b550: ldr             x16, [x16, #0x1c8]
    // 0xb6b554: r30 = true
    //     0xb6b554: add             lr, NULL, #0x20  ; true
    // 0xb6b558: stp             lr, x16, [SP, #8]
    // 0xb6b55c: r16 = Instance_EdgeInsets
    //     0xb6b55c: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb6b560: str             x16, [SP]
    // 0xb6b564: mov             x1, x0
    // 0xb6b568: ldur            x2, [fp, #-0x48]
    // 0xb6b56c: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xb6b56c: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xb6b570: ldr             x4, [x4, #0x6b8]
    // 0xb6b574: r0 = ListView()
    //     0xb6b574: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb6b578: r0 = Card()
    //     0xb6b578: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb6b57c: mov             x1, x0
    // 0xb6b580: ldur            x0, [fp, #-0x40]
    // 0xb6b584: stur            x1, [fp, #-0x10]
    // 0xb6b588: StoreField: r1->field_b = r0
    //     0xb6b588: stur            w0, [x1, #0xb]
    // 0xb6b58c: r0 = 0.000000
    //     0xb6b58c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb6b590: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6b590: stur            w0, [x1, #0x17]
    // 0xb6b594: ldur            x0, [fp, #-0x38]
    // 0xb6b598: StoreField: r1->field_1b = r0
    //     0xb6b598: stur            w0, [x1, #0x1b]
    // 0xb6b59c: r0 = true
    //     0xb6b59c: add             x0, NULL, #0x20  ; true
    // 0xb6b5a0: StoreField: r1->field_1f = r0
    //     0xb6b5a0: stur            w0, [x1, #0x1f]
    // 0xb6b5a4: ldur            x2, [fp, #-8]
    // 0xb6b5a8: StoreField: r1->field_2f = r2
    //     0xb6b5a8: stur            w2, [x1, #0x2f]
    // 0xb6b5ac: StoreField: r1->field_2b = r0
    //     0xb6b5ac: stur            w0, [x1, #0x2b]
    // 0xb6b5b0: r0 = Instance__CardVariant
    //     0xb6b5b0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb6b5b4: ldr             x0, [x0, #0xa68]
    // 0xb6b5b8: StoreField: r1->field_33 = r0
    //     0xb6b5b8: stur            w0, [x1, #0x33]
    // 0xb6b5bc: r0 = Container()
    //     0xb6b5bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6b5c0: stur            x0, [fp, #-8]
    // 0xb6b5c4: r16 = Instance_EdgeInsets
    //     0xb6b5c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb6b5c8: ldr             x16, [x16, #0x980]
    // 0xb6b5cc: ldur            lr, [fp, #-0x10]
    // 0xb6b5d0: stp             lr, x16, [SP]
    // 0xb6b5d4: mov             x1, x0
    // 0xb6b5d8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb6b5d8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb6b5dc: ldr             x4, [x4, #0x30]
    // 0xb6b5e0: r0 = Container()
    //     0xb6b5e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6b5e4: r0 = AnimatedContainer()
    //     0xb6b5e4: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb6b5e8: stur            x0, [fp, #-0x10]
    // 0xb6b5ec: r16 = Instance_Cubic
    //     0xb6b5ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb6b5f0: ldr             x16, [x16, #0xaf8]
    // 0xb6b5f4: str             x16, [SP]
    // 0xb6b5f8: mov             x1, x0
    // 0xb6b5fc: ldur            x2, [fp, #-8]
    // 0xb6b600: r3 = Instance_Duration
    //     0xb6b600: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb6b604: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb6b604: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb6b608: ldr             x4, [x4, #0xbc8]
    // 0xb6b60c: r0 = AnimatedContainer()
    //     0xb6b60c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb6b610: ldur            x0, [fp, #-0x10]
    // 0xb6b614: LeaveFrame
    //     0xb6b614: mov             SP, fp
    //     0xb6b618: ldp             fp, lr, [SP], #0x10
    // 0xb6b61c: ret
    //     0xb6b61c: ret             
    // 0xb6b620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6b620: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6b624: b               #0xb6a860
    // 0xb6b628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6b628: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6b62c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b62c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6b630: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6b634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b634: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b638: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb6b638: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb6b63c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b63c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b640: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b644: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b644: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b648: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b64c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b64c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b650: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6b654: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6b654: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6b658, size: 0x8c
    // 0xb6b658: EnterFrame
    //     0xb6b658: stp             fp, lr, [SP, #-0x10]!
    //     0xb6b65c: mov             fp, SP
    // 0xb6b660: AllocStack(0x10)
    //     0xb6b660: sub             SP, SP, #0x10
    // 0xb6b664: SetupParameters()
    //     0xb6b664: ldr             x0, [fp, #0x10]
    //     0xb6b668: ldur            w2, [x0, #0x17]
    //     0xb6b66c: add             x2, x2, HEAP, lsl #32
    //     0xb6b670: stur            x2, [fp, #-0x10]
    // 0xb6b674: CheckStackOverflow
    //     0xb6b674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6b678: cmp             SP, x16
    //     0xb6b67c: b.ls            #0xb6b6dc
    // 0xb6b680: LoadField: r0 = r2->field_13
    //     0xb6b680: ldur            w0, [x2, #0x13]
    // 0xb6b684: DecompressPointer r0
    //     0xb6b684: add             x0, x0, HEAP, lsl #32
    // 0xb6b688: mov             x1, x0
    // 0xb6b68c: stur            x0, [fp, #-8]
    // 0xb6b690: r0 = value()
    //     0xb6b690: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb6b694: eor             x2, x0, #0x10
    // 0xb6b698: ldur            x1, [fp, #-8]
    // 0xb6b69c: r0 = value=()
    //     0xb6b69c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb6b6a0: ldur            x0, [fp, #-0x10]
    // 0xb6b6a4: LoadField: r3 = r0->field_f
    //     0xb6b6a4: ldur            w3, [x0, #0xf]
    // 0xb6b6a8: DecompressPointer r3
    //     0xb6b6a8: add             x3, x3, HEAP, lsl #32
    // 0xb6b6ac: stur            x3, [fp, #-8]
    // 0xb6b6b0: r1 = Function '<anonymous closure>':.
    //     0xb6b6b0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c38] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb6b6b4: ldr             x1, [x1, #0xc38]
    // 0xb6b6b8: r2 = Null
    //     0xb6b6b8: mov             x2, NULL
    // 0xb6b6bc: r0 = AllocateClosure()
    //     0xb6b6bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6b6c0: ldur            x1, [fp, #-8]
    // 0xb6b6c4: mov             x2, x0
    // 0xb6b6c8: r0 = setState()
    //     0xb6b6c8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb6b6cc: r0 = Null
    //     0xb6b6cc: mov             x0, NULL
    // 0xb6b6d0: LeaveFrame
    //     0xb6b6d0: mov             SP, fp
    //     0xb6b6d4: ldp             fp, lr, [SP], #0x10
    // 0xb6b6d8: ret
    //     0xb6b6d8: ret             
    // 0xb6b6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6b6dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6b6e0: b               #0xb6b680
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb6b6e4, size: 0x84
    // 0xb6b6e4: EnterFrame
    //     0xb6b6e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb6b6e8: mov             fp, SP
    // 0xb6b6ec: AllocStack(0x10)
    //     0xb6b6ec: sub             SP, SP, #0x10
    // 0xb6b6f0: SetupParameters()
    //     0xb6b6f0: ldr             x0, [fp, #0x18]
    //     0xb6b6f4: ldur            w1, [x0, #0x17]
    //     0xb6b6f8: add             x1, x1, HEAP, lsl #32
    //     0xb6b6fc: stur            x1, [fp, #-8]
    // 0xb6b700: CheckStackOverflow
    //     0xb6b700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6b704: cmp             SP, x16
    //     0xb6b708: b.ls            #0xb6b760
    // 0xb6b70c: r1 = 1
    //     0xb6b70c: movz            x1, #0x1
    // 0xb6b710: r0 = AllocateContext()
    //     0xb6b710: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6b714: mov             x1, x0
    // 0xb6b718: ldur            x0, [fp, #-8]
    // 0xb6b71c: StoreField: r1->field_b = r0
    //     0xb6b71c: stur            w0, [x1, #0xb]
    // 0xb6b720: ldr             x2, [fp, #0x10]
    // 0xb6b724: StoreField: r1->field_f = r2
    //     0xb6b724: stur            w2, [x1, #0xf]
    // 0xb6b728: LoadField: r3 = r0->field_f
    //     0xb6b728: ldur            w3, [x0, #0xf]
    // 0xb6b72c: DecompressPointer r3
    //     0xb6b72c: add             x3, x3, HEAP, lsl #32
    // 0xb6b730: mov             x2, x1
    // 0xb6b734: stur            x3, [fp, #-0x10]
    // 0xb6b738: r1 = Function '<anonymous closure>':.
    //     0xb6b738: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c40] AnonymousClosure: (0xb6b768), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb69e28)
    //     0xb6b73c: ldr             x1, [x1, #0xc40]
    // 0xb6b740: r0 = AllocateClosure()
    //     0xb6b740: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6b744: ldur            x1, [fp, #-0x10]
    // 0xb6b748: mov             x2, x0
    // 0xb6b74c: r0 = setState()
    //     0xb6b74c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb6b750: r0 = Null
    //     0xb6b750: mov             x0, NULL
    // 0xb6b754: LeaveFrame
    //     0xb6b754: mov             SP, fp
    //     0xb6b758: ldp             fp, lr, [SP], #0x10
    // 0xb6b75c: ret
    //     0xb6b75c: ret             
    // 0xb6b760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6b760: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6b764: b               #0xb6b70c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6b768, size: 0x8c
    // 0xb6b768: EnterFrame
    //     0xb6b768: stp             fp, lr, [SP, #-0x10]!
    //     0xb6b76c: mov             fp, SP
    // 0xb6b770: AllocStack(0x8)
    //     0xb6b770: sub             SP, SP, #8
    // 0xb6b774: SetupParameters()
    //     0xb6b774: ldr             x0, [fp, #0x10]
    //     0xb6b778: ldur            w1, [x0, #0x17]
    //     0xb6b77c: add             x1, x1, HEAP, lsl #32
    // 0xb6b780: CheckStackOverflow
    //     0xb6b780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6b784: cmp             SP, x16
    //     0xb6b788: b.ls            #0xb6b7ec
    // 0xb6b78c: LoadField: r0 = r1->field_b
    //     0xb6b78c: ldur            w0, [x1, #0xb]
    // 0xb6b790: DecompressPointer r0
    //     0xb6b790: add             x0, x0, HEAP, lsl #32
    // 0xb6b794: LoadField: r2 = r0->field_f
    //     0xb6b794: ldur            w2, [x0, #0xf]
    // 0xb6b798: DecompressPointer r2
    //     0xb6b798: add             x2, x2, HEAP, lsl #32
    // 0xb6b79c: LoadField: r0 = r1->field_f
    //     0xb6b79c: ldur            w0, [x1, #0xf]
    // 0xb6b7a0: DecompressPointer r0
    //     0xb6b7a0: add             x0, x0, HEAP, lsl #32
    // 0xb6b7a4: r1 = LoadInt32Instr(r0)
    //     0xb6b7a4: sbfx            x1, x0, #1, #0x1f
    //     0xb6b7a8: tbz             w0, #0, #0xb6b7b0
    //     0xb6b7ac: ldur            x1, [x0, #7]
    // 0xb6b7b0: ArrayStore: r2[0] = r1  ; List_8
    //     0xb6b7b0: stur            x1, [x2, #0x17]
    // 0xb6b7b4: LoadField: r0 = r2->field_1f
    //     0xb6b7b4: ldur            w0, [x2, #0x1f]
    // 0xb6b7b8: DecompressPointer r0
    //     0xb6b7b8: add             x0, x0, HEAP, lsl #32
    // 0xb6b7bc: stur            x0, [fp, #-8]
    // 0xb6b7c0: r1 = Function '<anonymous closure>':.
    //     0xb6b7c0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c48] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xb6b7c4: ldr             x1, [x1, #0xc48]
    // 0xb6b7c8: r2 = Null
    //     0xb6b7c8: mov             x2, NULL
    // 0xb6b7cc: r0 = AllocateClosure()
    //     0xb6b7cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6b7d0: ldur            x1, [fp, #-8]
    // 0xb6b7d4: mov             x2, x0
    // 0xb6b7d8: r0 = forEach()
    //     0xb6b7d8: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xb6b7dc: r0 = Null
    //     0xb6b7dc: mov             x0, NULL
    // 0xb6b7e0: LeaveFrame
    //     0xb6b7e0: mov             SP, fp
    //     0xb6b7e4: ldp             fp, lr, [SP], #0x10
    // 0xb6b7e8: ret
    //     0xb6b7e8: ret             
    // 0xb6b7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6b7ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6b7f0: b               #0xb6b78c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87b48, size: 0x54
    // 0xc87b48: EnterFrame
    //     0xc87b48: stp             fp, lr, [SP, #-0x10]!
    //     0xc87b4c: mov             fp, SP
    // 0xc87b50: CheckStackOverflow
    //     0xc87b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87b54: cmp             SP, x16
    //     0xc87b58: b.ls            #0xc87b88
    // 0xc87b5c: LoadField: r0 = r1->field_13
    //     0xc87b5c: ldur            w0, [x1, #0x13]
    // 0xc87b60: DecompressPointer r0
    //     0xc87b60: add             x0, x0, HEAP, lsl #32
    // 0xc87b64: r16 = Sentinel
    //     0xc87b64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87b68: cmp             w0, w16
    // 0xc87b6c: b.eq            #0xc87b90
    // 0xc87b70: mov             x1, x0
    // 0xc87b74: r0 = dispose()
    //     0xc87b74: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87b78: r0 = Null
    //     0xc87b78: mov             x0, NULL
    // 0xc87b7c: LeaveFrame
    //     0xc87b7c: mov             SP, fp
    //     0xc87b80: ldp             fp, lr, [SP], #0x10
    // 0xc87b84: ret
    //     0xc87b84: ret             
    // 0xc87b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87b88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87b8c: b               #0xc87b5c
    // 0xc87b90: r9 = _pageController
    //     0xc87b90: add             x9, PP, #0x55, lsl #12  ; [pp+0x55be0] Field <_ProductTestimonialCarouselState@1590439617._pageController@1590439617>: late (offset: 0x14)
    //     0xc87b94: ldr             x9, [x9, #0xbe0]
    // 0xc87b98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87b98: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4074, size: 0x30, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f3a4, size: 0x84
    // 0xc7f3a4: EnterFrame
    //     0xc7f3a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f3a8: mov             fp, SP
    // 0xc7f3ac: AllocStack(0x18)
    //     0xc7f3ac: sub             SP, SP, #0x18
    // 0xc7f3b0: CheckStackOverflow
    //     0xc7f3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f3b4: cmp             SP, x16
    //     0xc7f3b8: b.ls            #0xc7f420
    // 0xc7f3bc: r1 = <ProductTestimonialCarousel>
    //     0xc7f3bc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48818] TypeArguments: <ProductTestimonialCarousel>
    //     0xc7f3c0: ldr             x1, [x1, #0x818]
    // 0xc7f3c4: r0 = _ProductTestimonialCarouselState()
    //     0xc7f3c4: bl              #0xc7f428  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc7f3c8: mov             x1, x0
    // 0xc7f3cc: r0 = Sentinel
    //     0xc7f3cc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f3d0: stur            x1, [fp, #-8]
    // 0xc7f3d4: StoreField: r1->field_13 = r0
    //     0xc7f3d4: stur            w0, [x1, #0x13]
    // 0xc7f3d8: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7f3d8: stur            xzr, [x1, #0x17]
    // 0xc7f3dc: r16 = <int, RxBool>
    //     0xc7f3dc: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7f3e0: ldr             x16, [x16, #0x298]
    // 0xc7f3e4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7f3e8: stp             lr, x16, [SP]
    // 0xc7f3ec: r0 = Map._fromLiteral()
    //     0xc7f3ec: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7f3f0: ldur            x1, [fp, #-8]
    // 0xc7f3f4: StoreField: r1->field_1f = r0
    //     0xc7f3f4: stur            w0, [x1, #0x1f]
    //     0xc7f3f8: ldurb           w16, [x1, #-1]
    //     0xc7f3fc: ldurb           w17, [x0, #-1]
    //     0xc7f400: and             x16, x17, x16, lsr #2
    //     0xc7f404: tst             x16, HEAP, lsr #32
    //     0xc7f408: b.eq            #0xc7f410
    //     0xc7f40c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7f410: mov             x0, x1
    // 0xc7f414: LeaveFrame
    //     0xc7f414: mov             SP, fp
    //     0xc7f418: ldp             fp, lr, [SP], #0x10
    // 0xc7f41c: ret
    //     0xc7f41c: ret             
    // 0xc7f420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f420: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f424: b               #0xc7f3bc
  }
}
