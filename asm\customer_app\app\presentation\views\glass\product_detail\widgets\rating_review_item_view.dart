// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart

// class id: 1049442, size: 0x8
class :: {
}

// class id: 3308, size: 0x14, field offset: 0x14
class _RatingReviewItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb8aa84, size: 0x280c
    // 0xb8aa84: EnterFrame
    //     0xb8aa84: stp             fp, lr, [SP, #-0x10]!
    //     0xb8aa88: mov             fp, SP
    // 0xb8aa8c: AllocStack(0x80)
    //     0xb8aa8c: sub             SP, SP, #0x80
    // 0xb8aa90: SetupParameters(_RatingReviewItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb8aa90: mov             x0, x1
    //     0xb8aa94: stur            x1, [fp, #-8]
    //     0xb8aa98: mov             x1, x2
    //     0xb8aa9c: stur            x2, [fp, #-0x10]
    // 0xb8aaa0: CheckStackOverflow
    //     0xb8aaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8aaa4: cmp             SP, x16
    //     0xb8aaa8: b.ls            #0xb8d248
    // 0xb8aaac: r1 = 2
    //     0xb8aaac: movz            x1, #0x2
    // 0xb8aab0: r0 = AllocateContext()
    //     0xb8aab0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8aab4: mov             x2, x0
    // 0xb8aab8: ldur            x0, [fp, #-8]
    // 0xb8aabc: stur            x2, [fp, #-0x20]
    // 0xb8aac0: StoreField: r2->field_f = r0
    //     0xb8aac0: stur            w0, [x2, #0xf]
    // 0xb8aac4: ldur            x1, [fp, #-0x10]
    // 0xb8aac8: StoreField: r2->field_13 = r1
    //     0xb8aac8: stur            w1, [x2, #0x13]
    // 0xb8aacc: LoadField: r3 = r0->field_b
    //     0xb8aacc: ldur            w3, [x0, #0xb]
    // 0xb8aad0: DecompressPointer r3
    //     0xb8aad0: add             x3, x3, HEAP, lsl #32
    // 0xb8aad4: cmp             w3, NULL
    // 0xb8aad8: b.eq            #0xb8d250
    // 0xb8aadc: LoadField: r4 = r3->field_f
    //     0xb8aadc: ldur            w4, [x3, #0xf]
    // 0xb8aae0: DecompressPointer r4
    //     0xb8aae0: add             x4, x4, HEAP, lsl #32
    // 0xb8aae4: LoadField: r5 = r4->field_b
    //     0xb8aae4: ldur            w5, [x4, #0xb]
    // 0xb8aae8: DecompressPointer r5
    //     0xb8aae8: add             x5, x5, HEAP, lsl #32
    // 0xb8aaec: cmp             w5, NULL
    // 0xb8aaf0: b.eq            #0xb8d234
    // 0xb8aaf4: LoadField: r4 = r3->field_13
    //     0xb8aaf4: ldur            w4, [x3, #0x13]
    // 0xb8aaf8: DecompressPointer r4
    //     0xb8aaf8: add             x4, x4, HEAP, lsl #32
    // 0xb8aafc: cmp             w4, NULL
    // 0xb8ab00: b.ne            #0xb8ab0c
    // 0xb8ab04: r3 = ""
    //     0xb8ab04: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8ab08: b               #0xb8ab10
    // 0xb8ab0c: mov             x3, x4
    // 0xb8ab10: stur            x3, [fp, #-0x18]
    // 0xb8ab14: r0 = of()
    //     0xb8ab14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ab18: LoadField: r1 = r0->field_87
    //     0xb8ab18: ldur            w1, [x0, #0x87]
    // 0xb8ab1c: DecompressPointer r1
    //     0xb8ab1c: add             x1, x1, HEAP, lsl #32
    // 0xb8ab20: LoadField: r0 = r1->field_7
    //     0xb8ab20: ldur            w0, [x1, #7]
    // 0xb8ab24: DecompressPointer r0
    //     0xb8ab24: add             x0, x0, HEAP, lsl #32
    // 0xb8ab28: r16 = 21.000000
    //     0xb8ab28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb8ab2c: ldr             x16, [x16, #0x9b0]
    // 0xb8ab30: r30 = Instance_Color
    //     0xb8ab30: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ab34: stp             lr, x16, [SP]
    // 0xb8ab38: mov             x1, x0
    // 0xb8ab3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8ab3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8ab40: ldr             x4, [x4, #0xaa0]
    // 0xb8ab44: r0 = copyWith()
    //     0xb8ab44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8ab48: stur            x0, [fp, #-0x10]
    // 0xb8ab4c: r0 = Text()
    //     0xb8ab4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8ab50: mov             x1, x0
    // 0xb8ab54: ldur            x0, [fp, #-0x18]
    // 0xb8ab58: stur            x1, [fp, #-0x28]
    // 0xb8ab5c: StoreField: r1->field_b = r0
    //     0xb8ab5c: stur            w0, [x1, #0xb]
    // 0xb8ab60: ldur            x0, [fp, #-0x10]
    // 0xb8ab64: StoreField: r1->field_13 = r0
    //     0xb8ab64: stur            w0, [x1, #0x13]
    // 0xb8ab68: r0 = Center()
    //     0xb8ab68: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8ab6c: mov             x2, x0
    // 0xb8ab70: r0 = Instance_Alignment
    //     0xb8ab70: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8ab74: ldr             x0, [x0, #0xb10]
    // 0xb8ab78: stur            x2, [fp, #-0x10]
    // 0xb8ab7c: StoreField: r2->field_f = r0
    //     0xb8ab7c: stur            w0, [x2, #0xf]
    // 0xb8ab80: ldur            x1, [fp, #-0x28]
    // 0xb8ab84: StoreField: r2->field_b = r1
    //     0xb8ab84: stur            w1, [x2, #0xb]
    // 0xb8ab88: ldur            x3, [fp, #-0x20]
    // 0xb8ab8c: LoadField: r1 = r3->field_13
    //     0xb8ab8c: ldur            w1, [x3, #0x13]
    // 0xb8ab90: DecompressPointer r1
    //     0xb8ab90: add             x1, x1, HEAP, lsl #32
    // 0xb8ab94: r0 = of()
    //     0xb8ab94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ab98: LoadField: r1 = r0->field_5b
    //     0xb8ab98: ldur            w1, [x0, #0x5b]
    // 0xb8ab9c: DecompressPointer r1
    //     0xb8ab9c: add             x1, x1, HEAP, lsl #32
    // 0xb8aba0: r0 = LoadClassIdInstr(r1)
    //     0xb8aba0: ldur            x0, [x1, #-1]
    //     0xb8aba4: ubfx            x0, x0, #0xc, #0x14
    // 0xb8aba8: d0 = 0.200000
    //     0xb8aba8: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb8abac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb8abac: sub             lr, x0, #0xffa
    //     0xb8abb0: ldr             lr, [x21, lr, lsl #3]
    //     0xb8abb4: blr             lr
    // 0xb8abb8: mov             x2, x0
    // 0xb8abbc: r1 = Null
    //     0xb8abbc: mov             x1, NULL
    // 0xb8abc0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb8abc0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb8abc4: r0 = Border.all()
    //     0xb8abc4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb8abc8: stur            x0, [fp, #-0x18]
    // 0xb8abcc: r0 = Radius()
    //     0xb8abcc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8abd0: d0 = 12.000000
    //     0xb8abd0: fmov            d0, #12.00000000
    // 0xb8abd4: stur            x0, [fp, #-0x28]
    // 0xb8abd8: StoreField: r0->field_7 = d0
    //     0xb8abd8: stur            d0, [x0, #7]
    // 0xb8abdc: StoreField: r0->field_f = d0
    //     0xb8abdc: stur            d0, [x0, #0xf]
    // 0xb8abe0: r0 = BorderRadius()
    //     0xb8abe0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8abe4: mov             x1, x0
    // 0xb8abe8: ldur            x0, [fp, #-0x28]
    // 0xb8abec: stur            x1, [fp, #-0x30]
    // 0xb8abf0: StoreField: r1->field_7 = r0
    //     0xb8abf0: stur            w0, [x1, #7]
    // 0xb8abf4: StoreField: r1->field_b = r0
    //     0xb8abf4: stur            w0, [x1, #0xb]
    // 0xb8abf8: StoreField: r1->field_f = r0
    //     0xb8abf8: stur            w0, [x1, #0xf]
    // 0xb8abfc: StoreField: r1->field_13 = r0
    //     0xb8abfc: stur            w0, [x1, #0x13]
    // 0xb8ac00: r0 = BoxDecoration()
    //     0xb8ac00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8ac04: mov             x1, x0
    // 0xb8ac08: ldur            x0, [fp, #-0x18]
    // 0xb8ac0c: stur            x1, [fp, #-0x28]
    // 0xb8ac10: StoreField: r1->field_f = r0
    //     0xb8ac10: stur            w0, [x1, #0xf]
    // 0xb8ac14: ldur            x0, [fp, #-0x30]
    // 0xb8ac18: StoreField: r1->field_13 = r0
    //     0xb8ac18: stur            w0, [x1, #0x13]
    // 0xb8ac1c: r2 = Instance_BoxShape
    //     0xb8ac1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8ac20: ldr             x2, [x2, #0x80]
    // 0xb8ac24: StoreField: r1->field_23 = r2
    //     0xb8ac24: stur            w2, [x1, #0x23]
    // 0xb8ac28: ldur            x3, [fp, #-8]
    // 0xb8ac2c: LoadField: r0 = r3->field_b
    //     0xb8ac2c: ldur            w0, [x3, #0xb]
    // 0xb8ac30: DecompressPointer r0
    //     0xb8ac30: add             x0, x0, HEAP, lsl #32
    // 0xb8ac34: cmp             w0, NULL
    // 0xb8ac38: b.eq            #0xb8d254
    // 0xb8ac3c: LoadField: r4 = r0->field_f
    //     0xb8ac3c: ldur            w4, [x0, #0xf]
    // 0xb8ac40: DecompressPointer r4
    //     0xb8ac40: add             x4, x4, HEAP, lsl #32
    // 0xb8ac44: LoadField: r0 = r4->field_b
    //     0xb8ac44: ldur            w0, [x4, #0xb]
    // 0xb8ac48: DecompressPointer r0
    //     0xb8ac48: add             x0, x0, HEAP, lsl #32
    // 0xb8ac4c: cmp             w0, NULL
    // 0xb8ac50: b.ne            #0xb8ac5c
    // 0xb8ac54: r0 = Null
    //     0xb8ac54: mov             x0, NULL
    // 0xb8ac58: b               #0xb8aca0
    // 0xb8ac5c: LoadField: r4 = r0->field_f
    //     0xb8ac5c: ldur            w4, [x0, #0xf]
    // 0xb8ac60: DecompressPointer r4
    //     0xb8ac60: add             x4, x4, HEAP, lsl #32
    // 0xb8ac64: cmp             w4, NULL
    // 0xb8ac68: b.ne            #0xb8ac74
    // 0xb8ac6c: r0 = Null
    //     0xb8ac6c: mov             x0, NULL
    // 0xb8ac70: b               #0xb8aca0
    // 0xb8ac74: LoadField: r0 = r4->field_f
    //     0xb8ac74: ldur            w0, [x4, #0xf]
    // 0xb8ac78: DecompressPointer r0
    //     0xb8ac78: add             x0, x0, HEAP, lsl #32
    // 0xb8ac7c: r4 = LoadClassIdInstr(r0)
    //     0xb8ac7c: ldur            x4, [x0, #-1]
    //     0xb8ac80: ubfx            x4, x4, #0xc, #0x14
    // 0xb8ac84: str             x0, [SP]
    // 0xb8ac88: mov             x0, x4
    // 0xb8ac8c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb8ac8c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb8ac90: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb8ac90: movz            x17, #0x2700
    //     0xb8ac94: add             lr, x0, x17
    //     0xb8ac98: ldr             lr, [x21, lr, lsl #3]
    //     0xb8ac9c: blr             lr
    // 0xb8aca0: cmp             w0, NULL
    // 0xb8aca4: b.ne            #0xb8acb0
    // 0xb8aca8: r3 = ""
    //     0xb8aca8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8acac: b               #0xb8acb4
    // 0xb8acb0: mov             x3, x0
    // 0xb8acb4: ldur            x0, [fp, #-8]
    // 0xb8acb8: ldur            x2, [fp, #-0x20]
    // 0xb8acbc: stur            x3, [fp, #-0x18]
    // 0xb8acc0: LoadField: r1 = r2->field_13
    //     0xb8acc0: ldur            w1, [x2, #0x13]
    // 0xb8acc4: DecompressPointer r1
    //     0xb8acc4: add             x1, x1, HEAP, lsl #32
    // 0xb8acc8: r0 = of()
    //     0xb8acc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8accc: LoadField: r1 = r0->field_87
    //     0xb8accc: ldur            w1, [x0, #0x87]
    // 0xb8acd0: DecompressPointer r1
    //     0xb8acd0: add             x1, x1, HEAP, lsl #32
    // 0xb8acd4: LoadField: r0 = r1->field_23
    //     0xb8acd4: ldur            w0, [x1, #0x23]
    // 0xb8acd8: DecompressPointer r0
    //     0xb8acd8: add             x0, x0, HEAP, lsl #32
    // 0xb8acdc: r16 = Instance_Color
    //     0xb8acdc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8ace0: r30 = 32.000000
    //     0xb8ace0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb8ace4: ldr             lr, [lr, #0x848]
    // 0xb8ace8: stp             lr, x16, [SP]
    // 0xb8acec: mov             x1, x0
    // 0xb8acf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb8acf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb8acf4: ldr             x4, [x4, #0x9b8]
    // 0xb8acf8: r0 = copyWith()
    //     0xb8acf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8acfc: stur            x0, [fp, #-0x30]
    // 0xb8ad00: r0 = Text()
    //     0xb8ad00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8ad04: mov             x1, x0
    // 0xb8ad08: ldur            x0, [fp, #-0x18]
    // 0xb8ad0c: stur            x1, [fp, #-0x38]
    // 0xb8ad10: StoreField: r1->field_b = r0
    //     0xb8ad10: stur            w0, [x1, #0xb]
    // 0xb8ad14: ldur            x0, [fp, #-0x30]
    // 0xb8ad18: StoreField: r1->field_13 = r0
    //     0xb8ad18: stur            w0, [x1, #0x13]
    // 0xb8ad1c: r0 = Padding()
    //     0xb8ad1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8ad20: mov             x2, x0
    // 0xb8ad24: r0 = Instance_EdgeInsets
    //     0xb8ad24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0xb8ad28: ldr             x0, [x0, #0x850]
    // 0xb8ad2c: stur            x2, [fp, #-0x18]
    // 0xb8ad30: StoreField: r2->field_f = r0
    //     0xb8ad30: stur            w0, [x2, #0xf]
    // 0xb8ad34: ldur            x0, [fp, #-0x38]
    // 0xb8ad38: StoreField: r2->field_b = r0
    //     0xb8ad38: stur            w0, [x2, #0xb]
    // 0xb8ad3c: ldur            x0, [fp, #-8]
    // 0xb8ad40: LoadField: r1 = r0->field_b
    //     0xb8ad40: ldur            w1, [x0, #0xb]
    // 0xb8ad44: DecompressPointer r1
    //     0xb8ad44: add             x1, x1, HEAP, lsl #32
    // 0xb8ad48: cmp             w1, NULL
    // 0xb8ad4c: b.eq            #0xb8d258
    // 0xb8ad50: LoadField: r3 = r1->field_f
    //     0xb8ad50: ldur            w3, [x1, #0xf]
    // 0xb8ad54: DecompressPointer r3
    //     0xb8ad54: add             x3, x3, HEAP, lsl #32
    // 0xb8ad58: LoadField: r1 = r3->field_b
    //     0xb8ad58: ldur            w1, [x3, #0xb]
    // 0xb8ad5c: DecompressPointer r1
    //     0xb8ad5c: add             x1, x1, HEAP, lsl #32
    // 0xb8ad60: cmp             w1, NULL
    // 0xb8ad64: b.ne            #0xb8ad70
    // 0xb8ad68: r3 = Null
    //     0xb8ad68: mov             x3, NULL
    // 0xb8ad6c: b               #0xb8ad94
    // 0xb8ad70: LoadField: r3 = r1->field_f
    //     0xb8ad70: ldur            w3, [x1, #0xf]
    // 0xb8ad74: DecompressPointer r3
    //     0xb8ad74: add             x3, x3, HEAP, lsl #32
    // 0xb8ad78: cmp             w3, NULL
    // 0xb8ad7c: b.ne            #0xb8ad88
    // 0xb8ad80: r3 = Null
    //     0xb8ad80: mov             x3, NULL
    // 0xb8ad84: b               #0xb8ad94
    // 0xb8ad88: LoadField: r4 = r3->field_f
    //     0xb8ad88: ldur            w4, [x3, #0xf]
    // 0xb8ad8c: DecompressPointer r4
    //     0xb8ad8c: add             x4, x4, HEAP, lsl #32
    // 0xb8ad90: mov             x3, x4
    // 0xb8ad94: cmp             w3, NULL
    // 0xb8ad98: b.ne            #0xb8ada4
    // 0xb8ad9c: d1 = 0.000000
    //     0xb8ad9c: eor             v1.16b, v1.16b, v1.16b
    // 0xb8ada0: b               #0xb8adac
    // 0xb8ada4: LoadField: d0 = r3->field_7
    //     0xb8ada4: ldur            d0, [x3, #7]
    // 0xb8ada8: mov             v1.16b, v0.16b
    // 0xb8adac: d0 = 4.000000
    //     0xb8adac: fmov            d0, #4.00000000
    // 0xb8adb0: fcmp            d1, d0
    // 0xb8adb4: b.lt            #0xb8adcc
    // 0xb8adb8: mov             x1, x0
    // 0xb8adbc: mov             x0, x2
    // 0xb8adc0: r2 = Instance_Color
    //     0xb8adc0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb8adc4: ldr             x2, [x2, #0x858]
    // 0xb8adc8: b               #0xb8aeb4
    // 0xb8adcc: cmp             w1, NULL
    // 0xb8add0: b.ne            #0xb8addc
    // 0xb8add4: r3 = Null
    //     0xb8add4: mov             x3, NULL
    // 0xb8add8: b               #0xb8ae00
    // 0xb8addc: LoadField: r3 = r1->field_f
    //     0xb8addc: ldur            w3, [x1, #0xf]
    // 0xb8ade0: DecompressPointer r3
    //     0xb8ade0: add             x3, x3, HEAP, lsl #32
    // 0xb8ade4: cmp             w3, NULL
    // 0xb8ade8: b.ne            #0xb8adf4
    // 0xb8adec: r3 = Null
    //     0xb8adec: mov             x3, NULL
    // 0xb8adf0: b               #0xb8ae00
    // 0xb8adf4: LoadField: r4 = r3->field_f
    //     0xb8adf4: ldur            w4, [x3, #0xf]
    // 0xb8adf8: DecompressPointer r4
    //     0xb8adf8: add             x4, x4, HEAP, lsl #32
    // 0xb8adfc: mov             x3, x4
    // 0xb8ae00: cmp             w3, NULL
    // 0xb8ae04: b.ne            #0xb8ae10
    // 0xb8ae08: d1 = 0.000000
    //     0xb8ae08: eor             v1.16b, v1.16b, v1.16b
    // 0xb8ae0c: b               #0xb8ae18
    // 0xb8ae10: LoadField: d0 = r3->field_7
    //     0xb8ae10: ldur            d0, [x3, #7]
    // 0xb8ae14: mov             v1.16b, v0.16b
    // 0xb8ae18: d0 = 3.500000
    //     0xb8ae18: fmov            d0, #3.50000000
    // 0xb8ae1c: fcmp            d1, d0
    // 0xb8ae20: b.lt            #0xb8ae3c
    // 0xb8ae24: r1 = Instance_Color
    //     0xb8ae24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb8ae28: ldr             x1, [x1, #0x858]
    // 0xb8ae2c: d0 = 0.700000
    //     0xb8ae2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb8ae30: ldr             d0, [x17, #0xf48]
    // 0xb8ae34: r0 = withOpacity()
    //     0xb8ae34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8ae38: b               #0xb8aea8
    // 0xb8ae3c: cmp             w1, NULL
    // 0xb8ae40: b.ne            #0xb8ae4c
    // 0xb8ae44: r0 = Null
    //     0xb8ae44: mov             x0, NULL
    // 0xb8ae48: b               #0xb8ae70
    // 0xb8ae4c: LoadField: r0 = r1->field_f
    //     0xb8ae4c: ldur            w0, [x1, #0xf]
    // 0xb8ae50: DecompressPointer r0
    //     0xb8ae50: add             x0, x0, HEAP, lsl #32
    // 0xb8ae54: cmp             w0, NULL
    // 0xb8ae58: b.ne            #0xb8ae64
    // 0xb8ae5c: r0 = Null
    //     0xb8ae5c: mov             x0, NULL
    // 0xb8ae60: b               #0xb8ae70
    // 0xb8ae64: LoadField: r1 = r0->field_f
    //     0xb8ae64: ldur            w1, [x0, #0xf]
    // 0xb8ae68: DecompressPointer r1
    //     0xb8ae68: add             x1, x1, HEAP, lsl #32
    // 0xb8ae6c: mov             x0, x1
    // 0xb8ae70: cmp             w0, NULL
    // 0xb8ae74: b.ne            #0xb8ae80
    // 0xb8ae78: d1 = 0.000000
    //     0xb8ae78: eor             v1.16b, v1.16b, v1.16b
    // 0xb8ae7c: b               #0xb8ae88
    // 0xb8ae80: LoadField: d0 = r0->field_7
    //     0xb8ae80: ldur            d0, [x0, #7]
    // 0xb8ae84: mov             v1.16b, v0.16b
    // 0xb8ae88: d0 = 2.000000
    //     0xb8ae88: fmov            d0, #2.00000000
    // 0xb8ae8c: fcmp            d1, d0
    // 0xb8ae90: b.lt            #0xb8aea0
    // 0xb8ae94: r0 = Instance_Color
    //     0xb8ae94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb8ae98: ldr             x0, [x0, #0x860]
    // 0xb8ae9c: b               #0xb8aea8
    // 0xb8aea0: r0 = Instance_Color
    //     0xb8aea0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb8aea4: ldr             x0, [x0, #0x50]
    // 0xb8aea8: mov             x2, x0
    // 0xb8aeac: ldur            x1, [fp, #-8]
    // 0xb8aeb0: ldur            x0, [fp, #-0x18]
    // 0xb8aeb4: stur            x2, [fp, #-0x30]
    // 0xb8aeb8: r0 = ColorFilter()
    //     0xb8aeb8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb8aebc: mov             x1, x0
    // 0xb8aec0: ldur            x0, [fp, #-0x30]
    // 0xb8aec4: stur            x1, [fp, #-0x38]
    // 0xb8aec8: StoreField: r1->field_7 = r0
    //     0xb8aec8: stur            w0, [x1, #7]
    // 0xb8aecc: r0 = Instance_BlendMode
    //     0xb8aecc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb8aed0: ldr             x0, [x0, #0xb30]
    // 0xb8aed4: StoreField: r1->field_b = r0
    //     0xb8aed4: stur            w0, [x1, #0xb]
    // 0xb8aed8: r0 = 1
    //     0xb8aed8: movz            x0, #0x1
    // 0xb8aedc: StoreField: r1->field_13 = r0
    //     0xb8aedc: stur            x0, [x1, #0x13]
    // 0xb8aee0: r0 = SvgPicture()
    //     0xb8aee0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8aee4: stur            x0, [fp, #-0x30]
    // 0xb8aee8: ldur            x16, [fp, #-0x38]
    // 0xb8aeec: str             x16, [SP]
    // 0xb8aef0: mov             x1, x0
    // 0xb8aef4: r2 = "assets/images/big_green_star.svg"
    //     0xb8aef4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0xb8aef8: ldr             x2, [x2, #0x868]
    // 0xb8aefc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb8aefc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb8af00: ldr             x4, [x4, #0xa38]
    // 0xb8af04: r0 = SvgPicture.asset()
    //     0xb8af04: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8af08: r1 = Null
    //     0xb8af08: mov             x1, NULL
    // 0xb8af0c: r2 = 4
    //     0xb8af0c: movz            x2, #0x4
    // 0xb8af10: r0 = AllocateArray()
    //     0xb8af10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8af14: mov             x2, x0
    // 0xb8af18: ldur            x0, [fp, #-0x18]
    // 0xb8af1c: stur            x2, [fp, #-0x38]
    // 0xb8af20: StoreField: r2->field_f = r0
    //     0xb8af20: stur            w0, [x2, #0xf]
    // 0xb8af24: ldur            x0, [fp, #-0x30]
    // 0xb8af28: StoreField: r2->field_13 = r0
    //     0xb8af28: stur            w0, [x2, #0x13]
    // 0xb8af2c: r1 = <Widget>
    //     0xb8af2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8af30: r0 = AllocateGrowableArray()
    //     0xb8af30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8af34: mov             x1, x0
    // 0xb8af38: ldur            x0, [fp, #-0x38]
    // 0xb8af3c: stur            x1, [fp, #-0x18]
    // 0xb8af40: StoreField: r1->field_f = r0
    //     0xb8af40: stur            w0, [x1, #0xf]
    // 0xb8af44: r2 = 4
    //     0xb8af44: movz            x2, #0x4
    // 0xb8af48: StoreField: r1->field_b = r2
    //     0xb8af48: stur            w2, [x1, #0xb]
    // 0xb8af4c: r0 = Row()
    //     0xb8af4c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8af50: mov             x3, x0
    // 0xb8af54: r0 = Instance_Axis
    //     0xb8af54: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8af58: stur            x3, [fp, #-0x30]
    // 0xb8af5c: StoreField: r3->field_f = r0
    //     0xb8af5c: stur            w0, [x3, #0xf]
    // 0xb8af60: r4 = Instance_MainAxisAlignment
    //     0xb8af60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8af64: ldr             x4, [x4, #0xa08]
    // 0xb8af68: StoreField: r3->field_13 = r4
    //     0xb8af68: stur            w4, [x3, #0x13]
    // 0xb8af6c: r5 = Instance_MainAxisSize
    //     0xb8af6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8af70: ldr             x5, [x5, #0xa10]
    // 0xb8af74: ArrayStore: r3[0] = r5  ; List_4
    //     0xb8af74: stur            w5, [x3, #0x17]
    // 0xb8af78: r6 = Instance_CrossAxisAlignment
    //     0xb8af78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8af7c: ldr             x6, [x6, #0xa18]
    // 0xb8af80: StoreField: r3->field_1b = r6
    //     0xb8af80: stur            w6, [x3, #0x1b]
    // 0xb8af84: r7 = Instance_VerticalDirection
    //     0xb8af84: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8af88: ldr             x7, [x7, #0xa20]
    // 0xb8af8c: StoreField: r3->field_23 = r7
    //     0xb8af8c: stur            w7, [x3, #0x23]
    // 0xb8af90: r8 = Instance_Clip
    //     0xb8af90: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8af94: ldr             x8, [x8, #0x38]
    // 0xb8af98: StoreField: r3->field_2b = r8
    //     0xb8af98: stur            w8, [x3, #0x2b]
    // 0xb8af9c: StoreField: r3->field_2f = rZR
    //     0xb8af9c: stur            xzr, [x3, #0x2f]
    // 0xb8afa0: ldur            x1, [fp, #-0x18]
    // 0xb8afa4: StoreField: r3->field_b = r1
    //     0xb8afa4: stur            w1, [x3, #0xb]
    // 0xb8afa8: r1 = Null
    //     0xb8afa8: mov             x1, NULL
    // 0xb8afac: r2 = 2
    //     0xb8afac: movz            x2, #0x2
    // 0xb8afb0: r0 = AllocateArray()
    //     0xb8afb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8afb4: mov             x2, x0
    // 0xb8afb8: ldur            x0, [fp, #-0x30]
    // 0xb8afbc: stur            x2, [fp, #-0x18]
    // 0xb8afc0: StoreField: r2->field_f = r0
    //     0xb8afc0: stur            w0, [x2, #0xf]
    // 0xb8afc4: r1 = <Widget>
    //     0xb8afc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8afc8: r0 = AllocateGrowableArray()
    //     0xb8afc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8afcc: mov             x3, x0
    // 0xb8afd0: ldur            x0, [fp, #-0x18]
    // 0xb8afd4: stur            x3, [fp, #-0x30]
    // 0xb8afd8: StoreField: r3->field_f = r0
    //     0xb8afd8: stur            w0, [x3, #0xf]
    // 0xb8afdc: r0 = 2
    //     0xb8afdc: movz            x0, #0x2
    // 0xb8afe0: StoreField: r3->field_b = r0
    //     0xb8afe0: stur            w0, [x3, #0xb]
    // 0xb8afe4: ldur            x0, [fp, #-8]
    // 0xb8afe8: LoadField: r1 = r0->field_b
    //     0xb8afe8: ldur            w1, [x0, #0xb]
    // 0xb8afec: DecompressPointer r1
    //     0xb8afec: add             x1, x1, HEAP, lsl #32
    // 0xb8aff0: cmp             w1, NULL
    // 0xb8aff4: b.eq            #0xb8d25c
    // 0xb8aff8: LoadField: r2 = r1->field_f
    //     0xb8aff8: ldur            w2, [x1, #0xf]
    // 0xb8affc: DecompressPointer r2
    //     0xb8affc: add             x2, x2, HEAP, lsl #32
    // 0xb8b000: LoadField: r1 = r2->field_b
    //     0xb8b000: ldur            w1, [x2, #0xb]
    // 0xb8b004: DecompressPointer r1
    //     0xb8b004: add             x1, x1, HEAP, lsl #32
    // 0xb8b008: cmp             w1, NULL
    // 0xb8b00c: b.eq            #0xb8b02c
    // 0xb8b010: LoadField: r2 = r1->field_f
    //     0xb8b010: ldur            w2, [x1, #0xf]
    // 0xb8b014: DecompressPointer r2
    //     0xb8b014: add             x2, x2, HEAP, lsl #32
    // 0xb8b018: cmp             w2, NULL
    // 0xb8b01c: b.eq            #0xb8b02c
    // 0xb8b020: LoadField: r4 = r2->field_13
    //     0xb8b020: ldur            w4, [x2, #0x13]
    // 0xb8b024: DecompressPointer r4
    //     0xb8b024: add             x4, x4, HEAP, lsl #32
    // 0xb8b028: cbz             w4, #0xb8b18c
    // 0xb8b02c: cmp             w1, NULL
    // 0xb8b030: b.ne            #0xb8b03c
    // 0xb8b034: r1 = Null
    //     0xb8b034: mov             x1, NULL
    // 0xb8b038: b               #0xb8b05c
    // 0xb8b03c: LoadField: r2 = r1->field_f
    //     0xb8b03c: ldur            w2, [x1, #0xf]
    // 0xb8b040: DecompressPointer r2
    //     0xb8b040: add             x2, x2, HEAP, lsl #32
    // 0xb8b044: cmp             w2, NULL
    // 0xb8b048: b.ne            #0xb8b054
    // 0xb8b04c: r1 = Null
    //     0xb8b04c: mov             x1, NULL
    // 0xb8b050: b               #0xb8b05c
    // 0xb8b054: LoadField: r1 = r2->field_13
    //     0xb8b054: ldur            w1, [x2, #0x13]
    // 0xb8b058: DecompressPointer r1
    //     0xb8b058: add             x1, x1, HEAP, lsl #32
    // 0xb8b05c: cmp             w1, NULL
    // 0xb8b060: b.ne            #0xb8b06c
    // 0xb8b064: r5 = ""
    //     0xb8b064: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8b068: b               #0xb8b070
    // 0xb8b06c: mov             x5, x1
    // 0xb8b070: ldur            x4, [fp, #-0x20]
    // 0xb8b074: stur            x5, [fp, #-0x18]
    // 0xb8b078: r1 = Null
    //     0xb8b078: mov             x1, NULL
    // 0xb8b07c: r2 = 4
    //     0xb8b07c: movz            x2, #0x4
    // 0xb8b080: r0 = AllocateArray()
    //     0xb8b080: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8b084: mov             x1, x0
    // 0xb8b088: ldur            x0, [fp, #-0x18]
    // 0xb8b08c: StoreField: r1->field_f = r0
    //     0xb8b08c: stur            w0, [x1, #0xf]
    // 0xb8b090: r16 = " Ratings"
    //     0xb8b090: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0xb8b094: ldr             x16, [x16, #0x870]
    // 0xb8b098: StoreField: r1->field_13 = r16
    //     0xb8b098: stur            w16, [x1, #0x13]
    // 0xb8b09c: str             x1, [SP]
    // 0xb8b0a0: r0 = _interpolate()
    //     0xb8b0a0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb8b0a4: ldur            x2, [fp, #-0x20]
    // 0xb8b0a8: stur            x0, [fp, #-0x18]
    // 0xb8b0ac: LoadField: r1 = r2->field_13
    //     0xb8b0ac: ldur            w1, [x2, #0x13]
    // 0xb8b0b0: DecompressPointer r1
    //     0xb8b0b0: add             x1, x1, HEAP, lsl #32
    // 0xb8b0b4: r0 = of()
    //     0xb8b0b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8b0b8: LoadField: r1 = r0->field_87
    //     0xb8b0b8: ldur            w1, [x0, #0x87]
    // 0xb8b0bc: DecompressPointer r1
    //     0xb8b0bc: add             x1, x1, HEAP, lsl #32
    // 0xb8b0c0: LoadField: r0 = r1->field_2b
    //     0xb8b0c0: ldur            w0, [x1, #0x2b]
    // 0xb8b0c4: DecompressPointer r0
    //     0xb8b0c4: add             x0, x0, HEAP, lsl #32
    // 0xb8b0c8: stur            x0, [fp, #-0x38]
    // 0xb8b0cc: r1 = Instance_Color
    //     0xb8b0cc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8b0d0: d0 = 0.850000
    //     0xb8b0d0: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xb8b0d4: ldr             d0, [x17, #0x878]
    // 0xb8b0d8: r0 = withOpacity()
    //     0xb8b0d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8b0dc: r16 = 10.000000
    //     0xb8b0dc: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb8b0e0: stp             x0, x16, [SP]
    // 0xb8b0e4: ldur            x1, [fp, #-0x38]
    // 0xb8b0e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8b0e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8b0ec: ldr             x4, [x4, #0xaa0]
    // 0xb8b0f0: r0 = copyWith()
    //     0xb8b0f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8b0f4: stur            x0, [fp, #-0x38]
    // 0xb8b0f8: r0 = Text()
    //     0xb8b0f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8b0fc: mov             x2, x0
    // 0xb8b100: ldur            x0, [fp, #-0x18]
    // 0xb8b104: stur            x2, [fp, #-0x48]
    // 0xb8b108: StoreField: r2->field_b = r0
    //     0xb8b108: stur            w0, [x2, #0xb]
    // 0xb8b10c: ldur            x0, [fp, #-0x38]
    // 0xb8b110: StoreField: r2->field_13 = r0
    //     0xb8b110: stur            w0, [x2, #0x13]
    // 0xb8b114: ldur            x0, [fp, #-0x30]
    // 0xb8b118: LoadField: r1 = r0->field_b
    //     0xb8b118: ldur            w1, [x0, #0xb]
    // 0xb8b11c: LoadField: r3 = r0->field_f
    //     0xb8b11c: ldur            w3, [x0, #0xf]
    // 0xb8b120: DecompressPointer r3
    //     0xb8b120: add             x3, x3, HEAP, lsl #32
    // 0xb8b124: LoadField: r4 = r3->field_b
    //     0xb8b124: ldur            w4, [x3, #0xb]
    // 0xb8b128: r3 = LoadInt32Instr(r1)
    //     0xb8b128: sbfx            x3, x1, #1, #0x1f
    // 0xb8b12c: stur            x3, [fp, #-0x40]
    // 0xb8b130: r1 = LoadInt32Instr(r4)
    //     0xb8b130: sbfx            x1, x4, #1, #0x1f
    // 0xb8b134: cmp             x3, x1
    // 0xb8b138: b.ne            #0xb8b144
    // 0xb8b13c: mov             x1, x0
    // 0xb8b140: r0 = _growToNextCapacity()
    //     0xb8b140: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8b144: ldur            x3, [fp, #-0x30]
    // 0xb8b148: ldur            x2, [fp, #-0x40]
    // 0xb8b14c: add             x0, x2, #1
    // 0xb8b150: lsl             x1, x0, #1
    // 0xb8b154: StoreField: r3->field_b = r1
    //     0xb8b154: stur            w1, [x3, #0xb]
    // 0xb8b158: LoadField: r1 = r3->field_f
    //     0xb8b158: ldur            w1, [x3, #0xf]
    // 0xb8b15c: DecompressPointer r1
    //     0xb8b15c: add             x1, x1, HEAP, lsl #32
    // 0xb8b160: ldur            x0, [fp, #-0x48]
    // 0xb8b164: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8b164: add             x25, x1, x2, lsl #2
    //     0xb8b168: add             x25, x25, #0xf
    //     0xb8b16c: str             w0, [x25]
    //     0xb8b170: tbz             w0, #0, #0xb8b18c
    //     0xb8b174: ldurb           w16, [x1, #-1]
    //     0xb8b178: ldurb           w17, [x0, #-1]
    //     0xb8b17c: and             x16, x17, x16, lsr #2
    //     0xb8b180: tst             x16, HEAP, lsr #32
    //     0xb8b184: b.eq            #0xb8b18c
    //     0xb8b188: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8b18c: ldur            x0, [fp, #-8]
    // 0xb8b190: LoadField: r1 = r0->field_b
    //     0xb8b190: ldur            w1, [x0, #0xb]
    // 0xb8b194: DecompressPointer r1
    //     0xb8b194: add             x1, x1, HEAP, lsl #32
    // 0xb8b198: cmp             w1, NULL
    // 0xb8b19c: b.eq            #0xb8d260
    // 0xb8b1a0: LoadField: r2 = r1->field_f
    //     0xb8b1a0: ldur            w2, [x1, #0xf]
    // 0xb8b1a4: DecompressPointer r2
    //     0xb8b1a4: add             x2, x2, HEAP, lsl #32
    // 0xb8b1a8: LoadField: r4 = r2->field_b
    //     0xb8b1a8: ldur            w4, [x2, #0xb]
    // 0xb8b1ac: DecompressPointer r4
    //     0xb8b1ac: add             x4, x4, HEAP, lsl #32
    // 0xb8b1b0: stur            x4, [fp, #-0x18]
    // 0xb8b1b4: cmp             w4, NULL
    // 0xb8b1b8: b.eq            #0xb8b1d8
    // 0xb8b1bc: LoadField: r1 = r4->field_f
    //     0xb8b1bc: ldur            w1, [x4, #0xf]
    // 0xb8b1c0: DecompressPointer r1
    //     0xb8b1c0: add             x1, x1, HEAP, lsl #32
    // 0xb8b1c4: cmp             w1, NULL
    // 0xb8b1c8: b.eq            #0xb8b1d8
    // 0xb8b1cc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb8b1cc: ldur            w2, [x1, #0x17]
    // 0xb8b1d0: DecompressPointer r2
    //     0xb8b1d0: add             x2, x2, HEAP, lsl #32
    // 0xb8b1d4: cbz             w2, #0xb8b344
    // 0xb8b1d8: r1 = Null
    //     0xb8b1d8: mov             x1, NULL
    // 0xb8b1dc: r2 = 6
    //     0xb8b1dc: movz            x2, #0x6
    // 0xb8b1e0: r0 = AllocateArray()
    //     0xb8b1e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8b1e4: r16 = "& "
    //     0xb8b1e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0xb8b1e8: ldr             x16, [x16, #0x880]
    // 0xb8b1ec: StoreField: r0->field_f = r16
    //     0xb8b1ec: stur            w16, [x0, #0xf]
    // 0xb8b1f0: ldur            x1, [fp, #-0x18]
    // 0xb8b1f4: cmp             w1, NULL
    // 0xb8b1f8: b.ne            #0xb8b204
    // 0xb8b1fc: r1 = Null
    //     0xb8b1fc: mov             x1, NULL
    // 0xb8b200: b               #0xb8b224
    // 0xb8b204: LoadField: r2 = r1->field_f
    //     0xb8b204: ldur            w2, [x1, #0xf]
    // 0xb8b208: DecompressPointer r2
    //     0xb8b208: add             x2, x2, HEAP, lsl #32
    // 0xb8b20c: cmp             w2, NULL
    // 0xb8b210: b.ne            #0xb8b21c
    // 0xb8b214: r1 = Null
    //     0xb8b214: mov             x1, NULL
    // 0xb8b218: b               #0xb8b224
    // 0xb8b21c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb8b21c: ldur            w1, [x2, #0x17]
    // 0xb8b220: DecompressPointer r1
    //     0xb8b220: add             x1, x1, HEAP, lsl #32
    // 0xb8b224: cmp             w1, NULL
    // 0xb8b228: b.ne            #0xb8b234
    // 0xb8b22c: r3 = ""
    //     0xb8b22c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8b230: b               #0xb8b238
    // 0xb8b234: mov             x3, x1
    // 0xb8b238: ldur            x2, [fp, #-0x20]
    // 0xb8b23c: ldur            x1, [fp, #-0x30]
    // 0xb8b240: StoreField: r0->field_13 = r3
    //     0xb8b240: stur            w3, [x0, #0x13]
    // 0xb8b244: r16 = " Reviews"
    //     0xb8b244: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0xb8b248: ldr             x16, [x16, #0x888]
    // 0xb8b24c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8b24c: stur            w16, [x0, #0x17]
    // 0xb8b250: str             x0, [SP]
    // 0xb8b254: r0 = _interpolate()
    //     0xb8b254: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb8b258: ldur            x2, [fp, #-0x20]
    // 0xb8b25c: stur            x0, [fp, #-0x18]
    // 0xb8b260: LoadField: r1 = r2->field_13
    //     0xb8b260: ldur            w1, [x2, #0x13]
    // 0xb8b264: DecompressPointer r1
    //     0xb8b264: add             x1, x1, HEAP, lsl #32
    // 0xb8b268: r0 = of()
    //     0xb8b268: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8b26c: LoadField: r1 = r0->field_87
    //     0xb8b26c: ldur            w1, [x0, #0x87]
    // 0xb8b270: DecompressPointer r1
    //     0xb8b270: add             x1, x1, HEAP, lsl #32
    // 0xb8b274: LoadField: r0 = r1->field_2b
    //     0xb8b274: ldur            w0, [x1, #0x2b]
    // 0xb8b278: DecompressPointer r0
    //     0xb8b278: add             x0, x0, HEAP, lsl #32
    // 0xb8b27c: stur            x0, [fp, #-0x38]
    // 0xb8b280: r1 = Instance_Color
    //     0xb8b280: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8b284: d0 = 0.850000
    //     0xb8b284: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xb8b288: ldr             d0, [x17, #0x878]
    // 0xb8b28c: r0 = withOpacity()
    //     0xb8b28c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8b290: r16 = 10.000000
    //     0xb8b290: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb8b294: stp             x0, x16, [SP]
    // 0xb8b298: ldur            x1, [fp, #-0x38]
    // 0xb8b29c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8b29c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8b2a0: ldr             x4, [x4, #0xaa0]
    // 0xb8b2a4: r0 = copyWith()
    //     0xb8b2a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8b2a8: stur            x0, [fp, #-0x38]
    // 0xb8b2ac: r0 = Text()
    //     0xb8b2ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8b2b0: mov             x2, x0
    // 0xb8b2b4: ldur            x0, [fp, #-0x18]
    // 0xb8b2b8: stur            x2, [fp, #-0x48]
    // 0xb8b2bc: StoreField: r2->field_b = r0
    //     0xb8b2bc: stur            w0, [x2, #0xb]
    // 0xb8b2c0: ldur            x0, [fp, #-0x38]
    // 0xb8b2c4: StoreField: r2->field_13 = r0
    //     0xb8b2c4: stur            w0, [x2, #0x13]
    // 0xb8b2c8: ldur            x0, [fp, #-0x30]
    // 0xb8b2cc: LoadField: r1 = r0->field_b
    //     0xb8b2cc: ldur            w1, [x0, #0xb]
    // 0xb8b2d0: LoadField: r3 = r0->field_f
    //     0xb8b2d0: ldur            w3, [x0, #0xf]
    // 0xb8b2d4: DecompressPointer r3
    //     0xb8b2d4: add             x3, x3, HEAP, lsl #32
    // 0xb8b2d8: LoadField: r4 = r3->field_b
    //     0xb8b2d8: ldur            w4, [x3, #0xb]
    // 0xb8b2dc: r3 = LoadInt32Instr(r1)
    //     0xb8b2dc: sbfx            x3, x1, #1, #0x1f
    // 0xb8b2e0: stur            x3, [fp, #-0x40]
    // 0xb8b2e4: r1 = LoadInt32Instr(r4)
    //     0xb8b2e4: sbfx            x1, x4, #1, #0x1f
    // 0xb8b2e8: cmp             x3, x1
    // 0xb8b2ec: b.ne            #0xb8b2f8
    // 0xb8b2f0: mov             x1, x0
    // 0xb8b2f4: r0 = _growToNextCapacity()
    //     0xb8b2f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8b2f8: ldur            x2, [fp, #-0x30]
    // 0xb8b2fc: ldur            x3, [fp, #-0x40]
    // 0xb8b300: add             x0, x3, #1
    // 0xb8b304: lsl             x1, x0, #1
    // 0xb8b308: StoreField: r2->field_b = r1
    //     0xb8b308: stur            w1, [x2, #0xb]
    // 0xb8b30c: LoadField: r1 = r2->field_f
    //     0xb8b30c: ldur            w1, [x2, #0xf]
    // 0xb8b310: DecompressPointer r1
    //     0xb8b310: add             x1, x1, HEAP, lsl #32
    // 0xb8b314: ldur            x0, [fp, #-0x48]
    // 0xb8b318: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8b318: add             x25, x1, x3, lsl #2
    //     0xb8b31c: add             x25, x25, #0xf
    //     0xb8b320: str             w0, [x25]
    //     0xb8b324: tbz             w0, #0, #0xb8b340
    //     0xb8b328: ldurb           w16, [x1, #-1]
    //     0xb8b32c: ldurb           w17, [x0, #-1]
    //     0xb8b330: and             x16, x17, x16, lsr #2
    //     0xb8b334: tst             x16, HEAP, lsr #32
    //     0xb8b338: b.eq            #0xb8b340
    //     0xb8b33c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8b340: b               #0xb8b348
    // 0xb8b344: mov             x2, x3
    // 0xb8b348: ldur            x1, [fp, #-8]
    // 0xb8b34c: LoadField: r0 = r1->field_b
    //     0xb8b34c: ldur            w0, [x1, #0xb]
    // 0xb8b350: DecompressPointer r0
    //     0xb8b350: add             x0, x0, HEAP, lsl #32
    // 0xb8b354: cmp             w0, NULL
    // 0xb8b358: b.eq            #0xb8d264
    // 0xb8b35c: LoadField: r3 = r0->field_f
    //     0xb8b35c: ldur            w3, [x0, #0xf]
    // 0xb8b360: DecompressPointer r3
    //     0xb8b360: add             x3, x3, HEAP, lsl #32
    // 0xb8b364: LoadField: r0 = r3->field_b
    //     0xb8b364: ldur            w0, [x3, #0xb]
    // 0xb8b368: DecompressPointer r0
    //     0xb8b368: add             x0, x0, HEAP, lsl #32
    // 0xb8b36c: cmp             w0, NULL
    // 0xb8b370: b.ne            #0xb8b37c
    // 0xb8b374: r0 = Null
    //     0xb8b374: mov             x0, NULL
    // 0xb8b378: b               #0xb8b39c
    // 0xb8b37c: LoadField: r3 = r0->field_f
    //     0xb8b37c: ldur            w3, [x0, #0xf]
    // 0xb8b380: DecompressPointer r3
    //     0xb8b380: add             x3, x3, HEAP, lsl #32
    // 0xb8b384: cmp             w3, NULL
    // 0xb8b388: b.ne            #0xb8b394
    // 0xb8b38c: r0 = Null
    //     0xb8b38c: mov             x0, NULL
    // 0xb8b390: b               #0xb8b39c
    // 0xb8b394: LoadField: r0 = r3->field_b
    //     0xb8b394: ldur            w0, [x3, #0xb]
    // 0xb8b398: DecompressPointer r0
    //     0xb8b398: add             x0, x0, HEAP, lsl #32
    // 0xb8b39c: cmp             w0, NULL
    // 0xb8b3a0: b.ne            #0xb8b3a8
    // 0xb8b3a4: r0 = false
    //     0xb8b3a4: add             x0, NULL, #0x30  ; false
    // 0xb8b3a8: stur            x0, [fp, #-0x18]
    // 0xb8b3ac: r0 = SvgPicture()
    //     0xb8b3ac: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb8b3b0: mov             x1, x0
    // 0xb8b3b4: r2 = "assets/images/shopdeck-tag.svg"
    //     0xb8b3b4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0xb8b3b8: ldr             x2, [x2, #0xd38]
    // 0xb8b3bc: stur            x0, [fp, #-0x38]
    // 0xb8b3c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb8b3c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb8b3c4: r0 = SvgPicture.asset()
    //     0xb8b3c4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8b3c8: r0 = InkWell()
    //     0xb8b3c8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8b3cc: mov             x3, x0
    // 0xb8b3d0: ldur            x0, [fp, #-0x38]
    // 0xb8b3d4: stur            x3, [fp, #-0x48]
    // 0xb8b3d8: StoreField: r3->field_b = r0
    //     0xb8b3d8: stur            w0, [x3, #0xb]
    // 0xb8b3dc: ldur            x2, [fp, #-0x20]
    // 0xb8b3e0: r1 = Function '<anonymous closure>':.
    //     0xb8b3e0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55300] AnonymousClosure: (0xb8e0ec), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8b3e4: ldr             x1, [x1, #0x300]
    // 0xb8b3e8: r0 = AllocateClosure()
    //     0xb8b3e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8b3ec: mov             x1, x0
    // 0xb8b3f0: ldur            x0, [fp, #-0x48]
    // 0xb8b3f4: StoreField: r0->field_f = r1
    //     0xb8b3f4: stur            w1, [x0, #0xf]
    // 0xb8b3f8: r1 = true
    //     0xb8b3f8: add             x1, NULL, #0x20  ; true
    // 0xb8b3fc: StoreField: r0->field_43 = r1
    //     0xb8b3fc: stur            w1, [x0, #0x43]
    // 0xb8b400: r2 = Instance_BoxShape
    //     0xb8b400: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8b404: ldr             x2, [x2, #0x80]
    // 0xb8b408: StoreField: r0->field_47 = r2
    //     0xb8b408: stur            w2, [x0, #0x47]
    // 0xb8b40c: StoreField: r0->field_6f = r1
    //     0xb8b40c: stur            w1, [x0, #0x6f]
    // 0xb8b410: r3 = false
    //     0xb8b410: add             x3, NULL, #0x30  ; false
    // 0xb8b414: StoreField: r0->field_73 = r3
    //     0xb8b414: stur            w3, [x0, #0x73]
    // 0xb8b418: StoreField: r0->field_83 = r1
    //     0xb8b418: stur            w1, [x0, #0x83]
    // 0xb8b41c: StoreField: r0->field_7b = r3
    //     0xb8b41c: stur            w3, [x0, #0x7b]
    // 0xb8b420: r0 = Padding()
    //     0xb8b420: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8b424: mov             x1, x0
    // 0xb8b428: r0 = Instance_EdgeInsets
    //     0xb8b428: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb8b42c: ldr             x0, [x0, #0x990]
    // 0xb8b430: stur            x1, [fp, #-0x38]
    // 0xb8b434: StoreField: r1->field_f = r0
    //     0xb8b434: stur            w0, [x1, #0xf]
    // 0xb8b438: ldur            x0, [fp, #-0x48]
    // 0xb8b43c: StoreField: r1->field_b = r0
    //     0xb8b43c: stur            w0, [x1, #0xb]
    // 0xb8b440: r0 = Visibility()
    //     0xb8b440: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8b444: mov             x2, x0
    // 0xb8b448: ldur            x0, [fp, #-0x38]
    // 0xb8b44c: stur            x2, [fp, #-0x48]
    // 0xb8b450: StoreField: r2->field_b = r0
    //     0xb8b450: stur            w0, [x2, #0xb]
    // 0xb8b454: r0 = Instance_SizedBox
    //     0xb8b454: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8b458: StoreField: r2->field_f = r0
    //     0xb8b458: stur            w0, [x2, #0xf]
    // 0xb8b45c: ldur            x1, [fp, #-0x18]
    // 0xb8b460: StoreField: r2->field_13 = r1
    //     0xb8b460: stur            w1, [x2, #0x13]
    // 0xb8b464: r3 = false
    //     0xb8b464: add             x3, NULL, #0x30  ; false
    // 0xb8b468: ArrayStore: r2[0] = r3  ; List_4
    //     0xb8b468: stur            w3, [x2, #0x17]
    // 0xb8b46c: StoreField: r2->field_1b = r3
    //     0xb8b46c: stur            w3, [x2, #0x1b]
    // 0xb8b470: StoreField: r2->field_1f = r3
    //     0xb8b470: stur            w3, [x2, #0x1f]
    // 0xb8b474: StoreField: r2->field_23 = r3
    //     0xb8b474: stur            w3, [x2, #0x23]
    // 0xb8b478: StoreField: r2->field_27 = r3
    //     0xb8b478: stur            w3, [x2, #0x27]
    // 0xb8b47c: StoreField: r2->field_2b = r3
    //     0xb8b47c: stur            w3, [x2, #0x2b]
    // 0xb8b480: ldur            x4, [fp, #-0x30]
    // 0xb8b484: LoadField: r1 = r4->field_b
    //     0xb8b484: ldur            w1, [x4, #0xb]
    // 0xb8b488: LoadField: r5 = r4->field_f
    //     0xb8b488: ldur            w5, [x4, #0xf]
    // 0xb8b48c: DecompressPointer r5
    //     0xb8b48c: add             x5, x5, HEAP, lsl #32
    // 0xb8b490: LoadField: r6 = r5->field_b
    //     0xb8b490: ldur            w6, [x5, #0xb]
    // 0xb8b494: r5 = LoadInt32Instr(r1)
    //     0xb8b494: sbfx            x5, x1, #1, #0x1f
    // 0xb8b498: stur            x5, [fp, #-0x40]
    // 0xb8b49c: r1 = LoadInt32Instr(r6)
    //     0xb8b49c: sbfx            x1, x6, #1, #0x1f
    // 0xb8b4a0: cmp             x5, x1
    // 0xb8b4a4: b.ne            #0xb8b4b0
    // 0xb8b4a8: mov             x1, x4
    // 0xb8b4ac: r0 = _growToNextCapacity()
    //     0xb8b4ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8b4b0: ldur            x4, [fp, #-8]
    // 0xb8b4b4: ldur            x5, [fp, #-0x20]
    // 0xb8b4b8: ldur            x2, [fp, #-0x30]
    // 0xb8b4bc: ldur            x3, [fp, #-0x40]
    // 0xb8b4c0: add             x0, x3, #1
    // 0xb8b4c4: lsl             x1, x0, #1
    // 0xb8b4c8: StoreField: r2->field_b = r1
    //     0xb8b4c8: stur            w1, [x2, #0xb]
    // 0xb8b4cc: LoadField: r1 = r2->field_f
    //     0xb8b4cc: ldur            w1, [x2, #0xf]
    // 0xb8b4d0: DecompressPointer r1
    //     0xb8b4d0: add             x1, x1, HEAP, lsl #32
    // 0xb8b4d4: ldur            x0, [fp, #-0x48]
    // 0xb8b4d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8b4d8: add             x25, x1, x3, lsl #2
    //     0xb8b4dc: add             x25, x25, #0xf
    //     0xb8b4e0: str             w0, [x25]
    //     0xb8b4e4: tbz             w0, #0, #0xb8b500
    //     0xb8b4e8: ldurb           w16, [x1, #-1]
    //     0xb8b4ec: ldurb           w17, [x0, #-1]
    //     0xb8b4f0: and             x16, x17, x16, lsr #2
    //     0xb8b4f4: tst             x16, HEAP, lsr #32
    //     0xb8b4f8: b.eq            #0xb8b500
    //     0xb8b4fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8b500: r0 = Column()
    //     0xb8b500: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8b504: mov             x4, x0
    // 0xb8b508: r0 = Instance_Axis
    //     0xb8b508: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8b50c: stur            x4, [fp, #-0x18]
    // 0xb8b510: StoreField: r4->field_f = r0
    //     0xb8b510: stur            w0, [x4, #0xf]
    // 0xb8b514: r6 = Instance_MainAxisAlignment
    //     0xb8b514: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8b518: ldr             x6, [x6, #0xa08]
    // 0xb8b51c: StoreField: r4->field_13 = r6
    //     0xb8b51c: stur            w6, [x4, #0x13]
    // 0xb8b520: r7 = Instance_MainAxisSize
    //     0xb8b520: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8b524: ldr             x7, [x7, #0xa10]
    // 0xb8b528: ArrayStore: r4[0] = r7  ; List_4
    //     0xb8b528: stur            w7, [x4, #0x17]
    // 0xb8b52c: r8 = Instance_CrossAxisAlignment
    //     0xb8b52c: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8b530: ldr             x8, [x8, #0x890]
    // 0xb8b534: StoreField: r4->field_1b = r8
    //     0xb8b534: stur            w8, [x4, #0x1b]
    // 0xb8b538: r9 = Instance_VerticalDirection
    //     0xb8b538: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8b53c: ldr             x9, [x9, #0xa20]
    // 0xb8b540: StoreField: r4->field_23 = r9
    //     0xb8b540: stur            w9, [x4, #0x23]
    // 0xb8b544: r10 = Instance_Clip
    //     0xb8b544: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8b548: ldr             x10, [x10, #0x38]
    // 0xb8b54c: StoreField: r4->field_2b = r10
    //     0xb8b54c: stur            w10, [x4, #0x2b]
    // 0xb8b550: StoreField: r4->field_2f = rZR
    //     0xb8b550: stur            xzr, [x4, #0x2f]
    // 0xb8b554: ldur            x1, [fp, #-0x30]
    // 0xb8b558: StoreField: r4->field_b = r1
    //     0xb8b558: stur            w1, [x4, #0xb]
    // 0xb8b55c: ldur            x11, [fp, #-0x20]
    // 0xb8b560: LoadField: r2 = r11->field_13
    //     0xb8b560: ldur            w2, [x11, #0x13]
    // 0xb8b564: DecompressPointer r2
    //     0xb8b564: add             x2, x2, HEAP, lsl #32
    // 0xb8b568: ldur            x12, [fp, #-8]
    // 0xb8b56c: LoadField: r1 = r12->field_b
    //     0xb8b56c: ldur            w1, [x12, #0xb]
    // 0xb8b570: DecompressPointer r1
    //     0xb8b570: add             x1, x1, HEAP, lsl #32
    // 0xb8b574: cmp             w1, NULL
    // 0xb8b578: b.eq            #0xb8d268
    // 0xb8b57c: LoadField: r3 = r1->field_f
    //     0xb8b57c: ldur            w3, [x1, #0xf]
    // 0xb8b580: DecompressPointer r3
    //     0xb8b580: add             x3, x3, HEAP, lsl #32
    // 0xb8b584: LoadField: r1 = r3->field_b
    //     0xb8b584: ldur            w1, [x3, #0xb]
    // 0xb8b588: DecompressPointer r1
    //     0xb8b588: add             x1, x1, HEAP, lsl #32
    // 0xb8b58c: cmp             w1, NULL
    // 0xb8b590: b.ne            #0xb8b59c
    // 0xb8b594: r3 = Null
    //     0xb8b594: mov             x3, NULL
    // 0xb8b598: b               #0xb8b5d4
    // 0xb8b59c: LoadField: r3 = r1->field_f
    //     0xb8b59c: ldur            w3, [x1, #0xf]
    // 0xb8b5a0: DecompressPointer r3
    //     0xb8b5a0: add             x3, x3, HEAP, lsl #32
    // 0xb8b5a4: cmp             w3, NULL
    // 0xb8b5a8: b.ne            #0xb8b5b4
    // 0xb8b5ac: r3 = Null
    //     0xb8b5ac: mov             x3, NULL
    // 0xb8b5b0: b               #0xb8b5d4
    // 0xb8b5b4: LoadField: r5 = r3->field_1b
    //     0xb8b5b4: ldur            w5, [x3, #0x1b]
    // 0xb8b5b8: DecompressPointer r5
    //     0xb8b5b8: add             x5, x5, HEAP, lsl #32
    // 0xb8b5bc: cmp             w5, NULL
    // 0xb8b5c0: b.ne            #0xb8b5cc
    // 0xb8b5c4: r3 = Null
    //     0xb8b5c4: mov             x3, NULL
    // 0xb8b5c8: b               #0xb8b5d4
    // 0xb8b5cc: LoadField: r3 = r5->field_7
    //     0xb8b5cc: ldur            w3, [x5, #7]
    // 0xb8b5d0: DecompressPointer r3
    //     0xb8b5d0: add             x3, x3, HEAP, lsl #32
    // 0xb8b5d4: cmp             w3, NULL
    // 0xb8b5d8: b.ne            #0xb8b5e4
    // 0xb8b5dc: r3 = 0
    //     0xb8b5dc: movz            x3, #0
    // 0xb8b5e0: b               #0xb8b5f4
    // 0xb8b5e4: r5 = LoadInt32Instr(r3)
    //     0xb8b5e4: sbfx            x5, x3, #1, #0x1f
    //     0xb8b5e8: tbz             w3, #0, #0xb8b5f0
    //     0xb8b5ec: ldur            x5, [x3, #7]
    // 0xb8b5f0: mov             x3, x5
    // 0xb8b5f4: cmp             w1, NULL
    // 0xb8b5f8: b.ne            #0xb8b604
    // 0xb8b5fc: r5 = Null
    //     0xb8b5fc: mov             x5, NULL
    // 0xb8b600: b               #0xb8b63c
    // 0xb8b604: LoadField: r5 = r1->field_f
    //     0xb8b604: ldur            w5, [x1, #0xf]
    // 0xb8b608: DecompressPointer r5
    //     0xb8b608: add             x5, x5, HEAP, lsl #32
    // 0xb8b60c: cmp             w5, NULL
    // 0xb8b610: b.ne            #0xb8b61c
    // 0xb8b614: r5 = Null
    //     0xb8b614: mov             x5, NULL
    // 0xb8b618: b               #0xb8b63c
    // 0xb8b61c: LoadField: r13 = r5->field_1b
    //     0xb8b61c: ldur            w13, [x5, #0x1b]
    // 0xb8b620: DecompressPointer r13
    //     0xb8b620: add             x13, x13, HEAP, lsl #32
    // 0xb8b624: cmp             w13, NULL
    // 0xb8b628: b.ne            #0xb8b634
    // 0xb8b62c: r5 = Null
    //     0xb8b62c: mov             x5, NULL
    // 0xb8b630: b               #0xb8b63c
    // 0xb8b634: LoadField: r5 = r13->field_7
    //     0xb8b634: ldur            w5, [x13, #7]
    // 0xb8b638: DecompressPointer r5
    //     0xb8b638: add             x5, x5, HEAP, lsl #32
    // 0xb8b63c: cmp             w5, NULL
    // 0xb8b640: b.ne            #0xb8b64c
    // 0xb8b644: r5 = 0
    //     0xb8b644: movz            x5, #0
    // 0xb8b648: b               #0xb8b65c
    // 0xb8b64c: r13 = LoadInt32Instr(r5)
    //     0xb8b64c: sbfx            x13, x5, #1, #0x1f
    //     0xb8b650: tbz             w5, #0, #0xb8b658
    //     0xb8b654: ldur            x13, [x5, #7]
    // 0xb8b658: mov             x5, x13
    // 0xb8b65c: cmp             w1, NULL
    // 0xb8b660: b.ne            #0xb8b66c
    // 0xb8b664: r13 = Null
    //     0xb8b664: mov             x13, NULL
    // 0xb8b668: b               #0xb8b6a4
    // 0xb8b66c: LoadField: r13 = r1->field_f
    //     0xb8b66c: ldur            w13, [x1, #0xf]
    // 0xb8b670: DecompressPointer r13
    //     0xb8b670: add             x13, x13, HEAP, lsl #32
    // 0xb8b674: cmp             w13, NULL
    // 0xb8b678: b.ne            #0xb8b684
    // 0xb8b67c: r13 = Null
    //     0xb8b67c: mov             x13, NULL
    // 0xb8b680: b               #0xb8b6a4
    // 0xb8b684: LoadField: r14 = r13->field_1b
    //     0xb8b684: ldur            w14, [x13, #0x1b]
    // 0xb8b688: DecompressPointer r14
    //     0xb8b688: add             x14, x14, HEAP, lsl #32
    // 0xb8b68c: cmp             w14, NULL
    // 0xb8b690: b.ne            #0xb8b69c
    // 0xb8b694: r13 = Null
    //     0xb8b694: mov             x13, NULL
    // 0xb8b698: b               #0xb8b6a4
    // 0xb8b69c: LoadField: r13 = r14->field_b
    //     0xb8b69c: ldur            w13, [x14, #0xb]
    // 0xb8b6a0: DecompressPointer r13
    //     0xb8b6a0: add             x13, x13, HEAP, lsl #32
    // 0xb8b6a4: cmp             w13, NULL
    // 0xb8b6a8: b.ne            #0xb8b6b4
    // 0xb8b6ac: r13 = 0
    //     0xb8b6ac: movz            x13, #0
    // 0xb8b6b0: b               #0xb8b6c4
    // 0xb8b6b4: r14 = LoadInt32Instr(r13)
    //     0xb8b6b4: sbfx            x14, x13, #1, #0x1f
    //     0xb8b6b8: tbz             w13, #0, #0xb8b6c0
    //     0xb8b6bc: ldur            x14, [x13, #7]
    // 0xb8b6c0: mov             x13, x14
    // 0xb8b6c4: add             x14, x5, x13
    // 0xb8b6c8: cmp             w1, NULL
    // 0xb8b6cc: b.ne            #0xb8b6d8
    // 0xb8b6d0: r5 = Null
    //     0xb8b6d0: mov             x5, NULL
    // 0xb8b6d4: b               #0xb8b710
    // 0xb8b6d8: LoadField: r5 = r1->field_f
    //     0xb8b6d8: ldur            w5, [x1, #0xf]
    // 0xb8b6dc: DecompressPointer r5
    //     0xb8b6dc: add             x5, x5, HEAP, lsl #32
    // 0xb8b6e0: cmp             w5, NULL
    // 0xb8b6e4: b.ne            #0xb8b6f0
    // 0xb8b6e8: r5 = Null
    //     0xb8b6e8: mov             x5, NULL
    // 0xb8b6ec: b               #0xb8b710
    // 0xb8b6f0: LoadField: r13 = r5->field_1b
    //     0xb8b6f0: ldur            w13, [x5, #0x1b]
    // 0xb8b6f4: DecompressPointer r13
    //     0xb8b6f4: add             x13, x13, HEAP, lsl #32
    // 0xb8b6f8: cmp             w13, NULL
    // 0xb8b6fc: b.ne            #0xb8b708
    // 0xb8b700: r5 = Null
    //     0xb8b700: mov             x5, NULL
    // 0xb8b704: b               #0xb8b710
    // 0xb8b708: LoadField: r5 = r13->field_f
    //     0xb8b708: ldur            w5, [x13, #0xf]
    // 0xb8b70c: DecompressPointer r5
    //     0xb8b70c: add             x5, x5, HEAP, lsl #32
    // 0xb8b710: cmp             w5, NULL
    // 0xb8b714: b.ne            #0xb8b720
    // 0xb8b718: r5 = 0
    //     0xb8b718: movz            x5, #0
    // 0xb8b71c: b               #0xb8b730
    // 0xb8b720: r13 = LoadInt32Instr(r5)
    //     0xb8b720: sbfx            x13, x5, #1, #0x1f
    //     0xb8b724: tbz             w5, #0, #0xb8b72c
    //     0xb8b728: ldur            x13, [x5, #7]
    // 0xb8b72c: mov             x5, x13
    // 0xb8b730: add             x13, x14, x5
    // 0xb8b734: cmp             w1, NULL
    // 0xb8b738: b.ne            #0xb8b744
    // 0xb8b73c: r5 = Null
    //     0xb8b73c: mov             x5, NULL
    // 0xb8b740: b               #0xb8b77c
    // 0xb8b744: LoadField: r5 = r1->field_f
    //     0xb8b744: ldur            w5, [x1, #0xf]
    // 0xb8b748: DecompressPointer r5
    //     0xb8b748: add             x5, x5, HEAP, lsl #32
    // 0xb8b74c: cmp             w5, NULL
    // 0xb8b750: b.ne            #0xb8b75c
    // 0xb8b754: r5 = Null
    //     0xb8b754: mov             x5, NULL
    // 0xb8b758: b               #0xb8b77c
    // 0xb8b75c: LoadField: r14 = r5->field_1b
    //     0xb8b75c: ldur            w14, [x5, #0x1b]
    // 0xb8b760: DecompressPointer r14
    //     0xb8b760: add             x14, x14, HEAP, lsl #32
    // 0xb8b764: cmp             w14, NULL
    // 0xb8b768: b.ne            #0xb8b774
    // 0xb8b76c: r5 = Null
    //     0xb8b76c: mov             x5, NULL
    // 0xb8b770: b               #0xb8b77c
    // 0xb8b774: LoadField: r5 = r14->field_13
    //     0xb8b774: ldur            w5, [x14, #0x13]
    // 0xb8b778: DecompressPointer r5
    //     0xb8b778: add             x5, x5, HEAP, lsl #32
    // 0xb8b77c: cmp             w5, NULL
    // 0xb8b780: b.ne            #0xb8b78c
    // 0xb8b784: r5 = 0
    //     0xb8b784: movz            x5, #0
    // 0xb8b788: b               #0xb8b79c
    // 0xb8b78c: r14 = LoadInt32Instr(r5)
    //     0xb8b78c: sbfx            x14, x5, #1, #0x1f
    //     0xb8b790: tbz             w5, #0, #0xb8b798
    //     0xb8b794: ldur            x14, [x5, #7]
    // 0xb8b798: mov             x5, x14
    // 0xb8b79c: add             x14, x13, x5
    // 0xb8b7a0: cmp             w1, NULL
    // 0xb8b7a4: b.ne            #0xb8b7b0
    // 0xb8b7a8: r5 = Null
    //     0xb8b7a8: mov             x5, NULL
    // 0xb8b7ac: b               #0xb8b7e8
    // 0xb8b7b0: LoadField: r5 = r1->field_f
    //     0xb8b7b0: ldur            w5, [x1, #0xf]
    // 0xb8b7b4: DecompressPointer r5
    //     0xb8b7b4: add             x5, x5, HEAP, lsl #32
    // 0xb8b7b8: cmp             w5, NULL
    // 0xb8b7bc: b.ne            #0xb8b7c8
    // 0xb8b7c0: r5 = Null
    //     0xb8b7c0: mov             x5, NULL
    // 0xb8b7c4: b               #0xb8b7e8
    // 0xb8b7c8: LoadField: r13 = r5->field_1b
    //     0xb8b7c8: ldur            w13, [x5, #0x1b]
    // 0xb8b7cc: DecompressPointer r13
    //     0xb8b7cc: add             x13, x13, HEAP, lsl #32
    // 0xb8b7d0: cmp             w13, NULL
    // 0xb8b7d4: b.ne            #0xb8b7e0
    // 0xb8b7d8: r5 = Null
    //     0xb8b7d8: mov             x5, NULL
    // 0xb8b7dc: b               #0xb8b7e8
    // 0xb8b7e0: ArrayLoad: r5 = r13[0]  ; List_4
    //     0xb8b7e0: ldur            w5, [x13, #0x17]
    // 0xb8b7e4: DecompressPointer r5
    //     0xb8b7e4: add             x5, x5, HEAP, lsl #32
    // 0xb8b7e8: cmp             w5, NULL
    // 0xb8b7ec: b.ne            #0xb8b7f8
    // 0xb8b7f0: r5 = 0
    //     0xb8b7f0: movz            x5, #0
    // 0xb8b7f4: b               #0xb8b808
    // 0xb8b7f8: r13 = LoadInt32Instr(r5)
    //     0xb8b7f8: sbfx            x13, x5, #1, #0x1f
    //     0xb8b7fc: tbz             w5, #0, #0xb8b804
    //     0xb8b800: ldur            x13, [x5, #7]
    // 0xb8b804: mov             x5, x13
    // 0xb8b808: add             x13, x14, x5
    // 0xb8b80c: scvtf           d0, x3
    // 0xb8b810: scvtf           d1, x13
    // 0xb8b814: fdiv            d2, d0, d1
    // 0xb8b818: cmp             w1, NULL
    // 0xb8b81c: b.ne            #0xb8b828
    // 0xb8b820: r1 = Null
    //     0xb8b820: mov             x1, NULL
    // 0xb8b824: b               #0xb8b864
    // 0xb8b828: LoadField: r3 = r1->field_f
    //     0xb8b828: ldur            w3, [x1, #0xf]
    // 0xb8b82c: DecompressPointer r3
    //     0xb8b82c: add             x3, x3, HEAP, lsl #32
    // 0xb8b830: cmp             w3, NULL
    // 0xb8b834: b.ne            #0xb8b840
    // 0xb8b838: r1 = Null
    //     0xb8b838: mov             x1, NULL
    // 0xb8b83c: b               #0xb8b864
    // 0xb8b840: LoadField: r1 = r3->field_1b
    //     0xb8b840: ldur            w1, [x3, #0x1b]
    // 0xb8b844: DecompressPointer r1
    //     0xb8b844: add             x1, x1, HEAP, lsl #32
    // 0xb8b848: cmp             w1, NULL
    // 0xb8b84c: b.ne            #0xb8b858
    // 0xb8b850: r1 = Null
    //     0xb8b850: mov             x1, NULL
    // 0xb8b854: b               #0xb8b864
    // 0xb8b858: LoadField: r3 = r1->field_7
    //     0xb8b858: ldur            w3, [x1, #7]
    // 0xb8b85c: DecompressPointer r3
    //     0xb8b85c: add             x3, x3, HEAP, lsl #32
    // 0xb8b860: mov             x1, x3
    // 0xb8b864: cmp             w1, NULL
    // 0xb8b868: b.ne            #0xb8b874
    // 0xb8b86c: r5 = 0
    //     0xb8b86c: movz            x5, #0
    // 0xb8b870: b               #0xb8b884
    // 0xb8b874: r3 = LoadInt32Instr(r1)
    //     0xb8b874: sbfx            x3, x1, #1, #0x1f
    //     0xb8b878: tbz             w1, #0, #0xb8b880
    //     0xb8b87c: ldur            x3, [x1, #7]
    // 0xb8b880: mov             x5, x3
    // 0xb8b884: mov             x1, x12
    // 0xb8b888: mov             v0.16b, v2.16b
    // 0xb8b88c: r3 = "5"
    //     0xb8b88c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0xb8b890: ldr             x3, [x3, #0x898]
    // 0xb8b894: r0 = chartRow()
    //     0xb8b894: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb8b898: mov             x4, x0
    // 0xb8b89c: ldur            x0, [fp, #-0x20]
    // 0xb8b8a0: stur            x4, [fp, #-0x30]
    // 0xb8b8a4: LoadField: r2 = r0->field_13
    //     0xb8b8a4: ldur            w2, [x0, #0x13]
    // 0xb8b8a8: DecompressPointer r2
    //     0xb8b8a8: add             x2, x2, HEAP, lsl #32
    // 0xb8b8ac: ldur            x6, [fp, #-8]
    // 0xb8b8b0: LoadField: r1 = r6->field_b
    //     0xb8b8b0: ldur            w1, [x6, #0xb]
    // 0xb8b8b4: DecompressPointer r1
    //     0xb8b8b4: add             x1, x1, HEAP, lsl #32
    // 0xb8b8b8: cmp             w1, NULL
    // 0xb8b8bc: b.eq            #0xb8d26c
    // 0xb8b8c0: LoadField: r3 = r1->field_f
    //     0xb8b8c0: ldur            w3, [x1, #0xf]
    // 0xb8b8c4: DecompressPointer r3
    //     0xb8b8c4: add             x3, x3, HEAP, lsl #32
    // 0xb8b8c8: LoadField: r1 = r3->field_b
    //     0xb8b8c8: ldur            w1, [x3, #0xb]
    // 0xb8b8cc: DecompressPointer r1
    //     0xb8b8cc: add             x1, x1, HEAP, lsl #32
    // 0xb8b8d0: cmp             w1, NULL
    // 0xb8b8d4: b.ne            #0xb8b8e0
    // 0xb8b8d8: r3 = Null
    //     0xb8b8d8: mov             x3, NULL
    // 0xb8b8dc: b               #0xb8b918
    // 0xb8b8e0: LoadField: r3 = r1->field_f
    //     0xb8b8e0: ldur            w3, [x1, #0xf]
    // 0xb8b8e4: DecompressPointer r3
    //     0xb8b8e4: add             x3, x3, HEAP, lsl #32
    // 0xb8b8e8: cmp             w3, NULL
    // 0xb8b8ec: b.ne            #0xb8b8f8
    // 0xb8b8f0: r3 = Null
    //     0xb8b8f0: mov             x3, NULL
    // 0xb8b8f4: b               #0xb8b918
    // 0xb8b8f8: LoadField: r5 = r3->field_1b
    //     0xb8b8f8: ldur            w5, [x3, #0x1b]
    // 0xb8b8fc: DecompressPointer r5
    //     0xb8b8fc: add             x5, x5, HEAP, lsl #32
    // 0xb8b900: cmp             w5, NULL
    // 0xb8b904: b.ne            #0xb8b910
    // 0xb8b908: r3 = Null
    //     0xb8b908: mov             x3, NULL
    // 0xb8b90c: b               #0xb8b918
    // 0xb8b910: LoadField: r3 = r5->field_b
    //     0xb8b910: ldur            w3, [x5, #0xb]
    // 0xb8b914: DecompressPointer r3
    //     0xb8b914: add             x3, x3, HEAP, lsl #32
    // 0xb8b918: cmp             w3, NULL
    // 0xb8b91c: b.ne            #0xb8b928
    // 0xb8b920: r3 = 0
    //     0xb8b920: movz            x3, #0
    // 0xb8b924: b               #0xb8b938
    // 0xb8b928: r5 = LoadInt32Instr(r3)
    //     0xb8b928: sbfx            x5, x3, #1, #0x1f
    //     0xb8b92c: tbz             w3, #0, #0xb8b934
    //     0xb8b930: ldur            x5, [x3, #7]
    // 0xb8b934: mov             x3, x5
    // 0xb8b938: cmp             w1, NULL
    // 0xb8b93c: b.ne            #0xb8b948
    // 0xb8b940: r5 = Null
    //     0xb8b940: mov             x5, NULL
    // 0xb8b944: b               #0xb8b980
    // 0xb8b948: LoadField: r5 = r1->field_f
    //     0xb8b948: ldur            w5, [x1, #0xf]
    // 0xb8b94c: DecompressPointer r5
    //     0xb8b94c: add             x5, x5, HEAP, lsl #32
    // 0xb8b950: cmp             w5, NULL
    // 0xb8b954: b.ne            #0xb8b960
    // 0xb8b958: r5 = Null
    //     0xb8b958: mov             x5, NULL
    // 0xb8b95c: b               #0xb8b980
    // 0xb8b960: LoadField: r7 = r5->field_1b
    //     0xb8b960: ldur            w7, [x5, #0x1b]
    // 0xb8b964: DecompressPointer r7
    //     0xb8b964: add             x7, x7, HEAP, lsl #32
    // 0xb8b968: cmp             w7, NULL
    // 0xb8b96c: b.ne            #0xb8b978
    // 0xb8b970: r5 = Null
    //     0xb8b970: mov             x5, NULL
    // 0xb8b974: b               #0xb8b980
    // 0xb8b978: LoadField: r5 = r7->field_7
    //     0xb8b978: ldur            w5, [x7, #7]
    // 0xb8b97c: DecompressPointer r5
    //     0xb8b97c: add             x5, x5, HEAP, lsl #32
    // 0xb8b980: cmp             w5, NULL
    // 0xb8b984: b.ne            #0xb8b990
    // 0xb8b988: r5 = 0
    //     0xb8b988: movz            x5, #0
    // 0xb8b98c: b               #0xb8b9a0
    // 0xb8b990: r7 = LoadInt32Instr(r5)
    //     0xb8b990: sbfx            x7, x5, #1, #0x1f
    //     0xb8b994: tbz             w5, #0, #0xb8b99c
    //     0xb8b998: ldur            x7, [x5, #7]
    // 0xb8b99c: mov             x5, x7
    // 0xb8b9a0: cmp             w1, NULL
    // 0xb8b9a4: b.ne            #0xb8b9b0
    // 0xb8b9a8: r7 = Null
    //     0xb8b9a8: mov             x7, NULL
    // 0xb8b9ac: b               #0xb8b9e8
    // 0xb8b9b0: LoadField: r7 = r1->field_f
    //     0xb8b9b0: ldur            w7, [x1, #0xf]
    // 0xb8b9b4: DecompressPointer r7
    //     0xb8b9b4: add             x7, x7, HEAP, lsl #32
    // 0xb8b9b8: cmp             w7, NULL
    // 0xb8b9bc: b.ne            #0xb8b9c8
    // 0xb8b9c0: r7 = Null
    //     0xb8b9c0: mov             x7, NULL
    // 0xb8b9c4: b               #0xb8b9e8
    // 0xb8b9c8: LoadField: r8 = r7->field_1b
    //     0xb8b9c8: ldur            w8, [x7, #0x1b]
    // 0xb8b9cc: DecompressPointer r8
    //     0xb8b9cc: add             x8, x8, HEAP, lsl #32
    // 0xb8b9d0: cmp             w8, NULL
    // 0xb8b9d4: b.ne            #0xb8b9e0
    // 0xb8b9d8: r7 = Null
    //     0xb8b9d8: mov             x7, NULL
    // 0xb8b9dc: b               #0xb8b9e8
    // 0xb8b9e0: LoadField: r7 = r8->field_b
    //     0xb8b9e0: ldur            w7, [x8, #0xb]
    // 0xb8b9e4: DecompressPointer r7
    //     0xb8b9e4: add             x7, x7, HEAP, lsl #32
    // 0xb8b9e8: cmp             w7, NULL
    // 0xb8b9ec: b.ne            #0xb8b9f8
    // 0xb8b9f0: r7 = 0
    //     0xb8b9f0: movz            x7, #0
    // 0xb8b9f4: b               #0xb8ba08
    // 0xb8b9f8: r8 = LoadInt32Instr(r7)
    //     0xb8b9f8: sbfx            x8, x7, #1, #0x1f
    //     0xb8b9fc: tbz             w7, #0, #0xb8ba04
    //     0xb8ba00: ldur            x8, [x7, #7]
    // 0xb8ba04: mov             x7, x8
    // 0xb8ba08: add             x8, x5, x7
    // 0xb8ba0c: cmp             w1, NULL
    // 0xb8ba10: b.ne            #0xb8ba1c
    // 0xb8ba14: r5 = Null
    //     0xb8ba14: mov             x5, NULL
    // 0xb8ba18: b               #0xb8ba54
    // 0xb8ba1c: LoadField: r5 = r1->field_f
    //     0xb8ba1c: ldur            w5, [x1, #0xf]
    // 0xb8ba20: DecompressPointer r5
    //     0xb8ba20: add             x5, x5, HEAP, lsl #32
    // 0xb8ba24: cmp             w5, NULL
    // 0xb8ba28: b.ne            #0xb8ba34
    // 0xb8ba2c: r5 = Null
    //     0xb8ba2c: mov             x5, NULL
    // 0xb8ba30: b               #0xb8ba54
    // 0xb8ba34: LoadField: r7 = r5->field_1b
    //     0xb8ba34: ldur            w7, [x5, #0x1b]
    // 0xb8ba38: DecompressPointer r7
    //     0xb8ba38: add             x7, x7, HEAP, lsl #32
    // 0xb8ba3c: cmp             w7, NULL
    // 0xb8ba40: b.ne            #0xb8ba4c
    // 0xb8ba44: r5 = Null
    //     0xb8ba44: mov             x5, NULL
    // 0xb8ba48: b               #0xb8ba54
    // 0xb8ba4c: LoadField: r5 = r7->field_f
    //     0xb8ba4c: ldur            w5, [x7, #0xf]
    // 0xb8ba50: DecompressPointer r5
    //     0xb8ba50: add             x5, x5, HEAP, lsl #32
    // 0xb8ba54: cmp             w5, NULL
    // 0xb8ba58: b.ne            #0xb8ba64
    // 0xb8ba5c: r5 = 0
    //     0xb8ba5c: movz            x5, #0
    // 0xb8ba60: b               #0xb8ba74
    // 0xb8ba64: r7 = LoadInt32Instr(r5)
    //     0xb8ba64: sbfx            x7, x5, #1, #0x1f
    //     0xb8ba68: tbz             w5, #0, #0xb8ba70
    //     0xb8ba6c: ldur            x7, [x5, #7]
    // 0xb8ba70: mov             x5, x7
    // 0xb8ba74: add             x7, x8, x5
    // 0xb8ba78: cmp             w1, NULL
    // 0xb8ba7c: b.ne            #0xb8ba88
    // 0xb8ba80: r5 = Null
    //     0xb8ba80: mov             x5, NULL
    // 0xb8ba84: b               #0xb8bac0
    // 0xb8ba88: LoadField: r5 = r1->field_f
    //     0xb8ba88: ldur            w5, [x1, #0xf]
    // 0xb8ba8c: DecompressPointer r5
    //     0xb8ba8c: add             x5, x5, HEAP, lsl #32
    // 0xb8ba90: cmp             w5, NULL
    // 0xb8ba94: b.ne            #0xb8baa0
    // 0xb8ba98: r5 = Null
    //     0xb8ba98: mov             x5, NULL
    // 0xb8ba9c: b               #0xb8bac0
    // 0xb8baa0: LoadField: r8 = r5->field_1b
    //     0xb8baa0: ldur            w8, [x5, #0x1b]
    // 0xb8baa4: DecompressPointer r8
    //     0xb8baa4: add             x8, x8, HEAP, lsl #32
    // 0xb8baa8: cmp             w8, NULL
    // 0xb8baac: b.ne            #0xb8bab8
    // 0xb8bab0: r5 = Null
    //     0xb8bab0: mov             x5, NULL
    // 0xb8bab4: b               #0xb8bac0
    // 0xb8bab8: LoadField: r5 = r8->field_13
    //     0xb8bab8: ldur            w5, [x8, #0x13]
    // 0xb8babc: DecompressPointer r5
    //     0xb8babc: add             x5, x5, HEAP, lsl #32
    // 0xb8bac0: cmp             w5, NULL
    // 0xb8bac4: b.ne            #0xb8bad0
    // 0xb8bac8: r5 = 0
    //     0xb8bac8: movz            x5, #0
    // 0xb8bacc: b               #0xb8bae0
    // 0xb8bad0: r8 = LoadInt32Instr(r5)
    //     0xb8bad0: sbfx            x8, x5, #1, #0x1f
    //     0xb8bad4: tbz             w5, #0, #0xb8badc
    //     0xb8bad8: ldur            x8, [x5, #7]
    // 0xb8badc: mov             x5, x8
    // 0xb8bae0: add             x8, x7, x5
    // 0xb8bae4: cmp             w1, NULL
    // 0xb8bae8: b.ne            #0xb8baf4
    // 0xb8baec: r5 = Null
    //     0xb8baec: mov             x5, NULL
    // 0xb8baf0: b               #0xb8bb2c
    // 0xb8baf4: LoadField: r5 = r1->field_f
    //     0xb8baf4: ldur            w5, [x1, #0xf]
    // 0xb8baf8: DecompressPointer r5
    //     0xb8baf8: add             x5, x5, HEAP, lsl #32
    // 0xb8bafc: cmp             w5, NULL
    // 0xb8bb00: b.ne            #0xb8bb0c
    // 0xb8bb04: r5 = Null
    //     0xb8bb04: mov             x5, NULL
    // 0xb8bb08: b               #0xb8bb2c
    // 0xb8bb0c: LoadField: r7 = r5->field_1b
    //     0xb8bb0c: ldur            w7, [x5, #0x1b]
    // 0xb8bb10: DecompressPointer r7
    //     0xb8bb10: add             x7, x7, HEAP, lsl #32
    // 0xb8bb14: cmp             w7, NULL
    // 0xb8bb18: b.ne            #0xb8bb24
    // 0xb8bb1c: r5 = Null
    //     0xb8bb1c: mov             x5, NULL
    // 0xb8bb20: b               #0xb8bb2c
    // 0xb8bb24: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb8bb24: ldur            w5, [x7, #0x17]
    // 0xb8bb28: DecompressPointer r5
    //     0xb8bb28: add             x5, x5, HEAP, lsl #32
    // 0xb8bb2c: cmp             w5, NULL
    // 0xb8bb30: b.ne            #0xb8bb3c
    // 0xb8bb34: r5 = 0
    //     0xb8bb34: movz            x5, #0
    // 0xb8bb38: b               #0xb8bb4c
    // 0xb8bb3c: r7 = LoadInt32Instr(r5)
    //     0xb8bb3c: sbfx            x7, x5, #1, #0x1f
    //     0xb8bb40: tbz             w5, #0, #0xb8bb48
    //     0xb8bb44: ldur            x7, [x5, #7]
    // 0xb8bb48: mov             x5, x7
    // 0xb8bb4c: add             x7, x8, x5
    // 0xb8bb50: scvtf           d0, x3
    // 0xb8bb54: scvtf           d1, x7
    // 0xb8bb58: fdiv            d2, d0, d1
    // 0xb8bb5c: cmp             w1, NULL
    // 0xb8bb60: b.ne            #0xb8bb6c
    // 0xb8bb64: r1 = Null
    //     0xb8bb64: mov             x1, NULL
    // 0xb8bb68: b               #0xb8bba8
    // 0xb8bb6c: LoadField: r3 = r1->field_f
    //     0xb8bb6c: ldur            w3, [x1, #0xf]
    // 0xb8bb70: DecompressPointer r3
    //     0xb8bb70: add             x3, x3, HEAP, lsl #32
    // 0xb8bb74: cmp             w3, NULL
    // 0xb8bb78: b.ne            #0xb8bb84
    // 0xb8bb7c: r1 = Null
    //     0xb8bb7c: mov             x1, NULL
    // 0xb8bb80: b               #0xb8bba8
    // 0xb8bb84: LoadField: r1 = r3->field_1b
    //     0xb8bb84: ldur            w1, [x3, #0x1b]
    // 0xb8bb88: DecompressPointer r1
    //     0xb8bb88: add             x1, x1, HEAP, lsl #32
    // 0xb8bb8c: cmp             w1, NULL
    // 0xb8bb90: b.ne            #0xb8bb9c
    // 0xb8bb94: r1 = Null
    //     0xb8bb94: mov             x1, NULL
    // 0xb8bb98: b               #0xb8bba8
    // 0xb8bb9c: LoadField: r3 = r1->field_b
    //     0xb8bb9c: ldur            w3, [x1, #0xb]
    // 0xb8bba0: DecompressPointer r3
    //     0xb8bba0: add             x3, x3, HEAP, lsl #32
    // 0xb8bba4: mov             x1, x3
    // 0xb8bba8: cmp             w1, NULL
    // 0xb8bbac: b.ne            #0xb8bbb8
    // 0xb8bbb0: r5 = 0
    //     0xb8bbb0: movz            x5, #0
    // 0xb8bbb4: b               #0xb8bbc8
    // 0xb8bbb8: r3 = LoadInt32Instr(r1)
    //     0xb8bbb8: sbfx            x3, x1, #1, #0x1f
    //     0xb8bbbc: tbz             w1, #0, #0xb8bbc4
    //     0xb8bbc0: ldur            x3, [x1, #7]
    // 0xb8bbc4: mov             x5, x3
    // 0xb8bbc8: mov             x1, x6
    // 0xb8bbcc: mov             v0.16b, v2.16b
    // 0xb8bbd0: r3 = "4"
    //     0xb8bbd0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0xb8bbd4: ldr             x3, [x3, #0x8a0]
    // 0xb8bbd8: r0 = chartRow()
    //     0xb8bbd8: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb8bbdc: mov             x4, x0
    // 0xb8bbe0: ldur            x0, [fp, #-0x20]
    // 0xb8bbe4: stur            x4, [fp, #-0x38]
    // 0xb8bbe8: LoadField: r2 = r0->field_13
    //     0xb8bbe8: ldur            w2, [x0, #0x13]
    // 0xb8bbec: DecompressPointer r2
    //     0xb8bbec: add             x2, x2, HEAP, lsl #32
    // 0xb8bbf0: ldur            x6, [fp, #-8]
    // 0xb8bbf4: LoadField: r1 = r6->field_b
    //     0xb8bbf4: ldur            w1, [x6, #0xb]
    // 0xb8bbf8: DecompressPointer r1
    //     0xb8bbf8: add             x1, x1, HEAP, lsl #32
    // 0xb8bbfc: cmp             w1, NULL
    // 0xb8bc00: b.eq            #0xb8d270
    // 0xb8bc04: LoadField: r3 = r1->field_f
    //     0xb8bc04: ldur            w3, [x1, #0xf]
    // 0xb8bc08: DecompressPointer r3
    //     0xb8bc08: add             x3, x3, HEAP, lsl #32
    // 0xb8bc0c: LoadField: r1 = r3->field_b
    //     0xb8bc0c: ldur            w1, [x3, #0xb]
    // 0xb8bc10: DecompressPointer r1
    //     0xb8bc10: add             x1, x1, HEAP, lsl #32
    // 0xb8bc14: cmp             w1, NULL
    // 0xb8bc18: b.ne            #0xb8bc24
    // 0xb8bc1c: r3 = Null
    //     0xb8bc1c: mov             x3, NULL
    // 0xb8bc20: b               #0xb8bc5c
    // 0xb8bc24: LoadField: r3 = r1->field_f
    //     0xb8bc24: ldur            w3, [x1, #0xf]
    // 0xb8bc28: DecompressPointer r3
    //     0xb8bc28: add             x3, x3, HEAP, lsl #32
    // 0xb8bc2c: cmp             w3, NULL
    // 0xb8bc30: b.ne            #0xb8bc3c
    // 0xb8bc34: r3 = Null
    //     0xb8bc34: mov             x3, NULL
    // 0xb8bc38: b               #0xb8bc5c
    // 0xb8bc3c: LoadField: r5 = r3->field_1b
    //     0xb8bc3c: ldur            w5, [x3, #0x1b]
    // 0xb8bc40: DecompressPointer r5
    //     0xb8bc40: add             x5, x5, HEAP, lsl #32
    // 0xb8bc44: cmp             w5, NULL
    // 0xb8bc48: b.ne            #0xb8bc54
    // 0xb8bc4c: r3 = Null
    //     0xb8bc4c: mov             x3, NULL
    // 0xb8bc50: b               #0xb8bc5c
    // 0xb8bc54: LoadField: r3 = r5->field_f
    //     0xb8bc54: ldur            w3, [x5, #0xf]
    // 0xb8bc58: DecompressPointer r3
    //     0xb8bc58: add             x3, x3, HEAP, lsl #32
    // 0xb8bc5c: cmp             w3, NULL
    // 0xb8bc60: b.ne            #0xb8bc6c
    // 0xb8bc64: r3 = 0
    //     0xb8bc64: movz            x3, #0
    // 0xb8bc68: b               #0xb8bc7c
    // 0xb8bc6c: r5 = LoadInt32Instr(r3)
    //     0xb8bc6c: sbfx            x5, x3, #1, #0x1f
    //     0xb8bc70: tbz             w3, #0, #0xb8bc78
    //     0xb8bc74: ldur            x5, [x3, #7]
    // 0xb8bc78: mov             x3, x5
    // 0xb8bc7c: cmp             w1, NULL
    // 0xb8bc80: b.ne            #0xb8bc8c
    // 0xb8bc84: r5 = Null
    //     0xb8bc84: mov             x5, NULL
    // 0xb8bc88: b               #0xb8bcc4
    // 0xb8bc8c: LoadField: r5 = r1->field_f
    //     0xb8bc8c: ldur            w5, [x1, #0xf]
    // 0xb8bc90: DecompressPointer r5
    //     0xb8bc90: add             x5, x5, HEAP, lsl #32
    // 0xb8bc94: cmp             w5, NULL
    // 0xb8bc98: b.ne            #0xb8bca4
    // 0xb8bc9c: r5 = Null
    //     0xb8bc9c: mov             x5, NULL
    // 0xb8bca0: b               #0xb8bcc4
    // 0xb8bca4: LoadField: r7 = r5->field_1b
    //     0xb8bca4: ldur            w7, [x5, #0x1b]
    // 0xb8bca8: DecompressPointer r7
    //     0xb8bca8: add             x7, x7, HEAP, lsl #32
    // 0xb8bcac: cmp             w7, NULL
    // 0xb8bcb0: b.ne            #0xb8bcbc
    // 0xb8bcb4: r5 = Null
    //     0xb8bcb4: mov             x5, NULL
    // 0xb8bcb8: b               #0xb8bcc4
    // 0xb8bcbc: LoadField: r5 = r7->field_7
    //     0xb8bcbc: ldur            w5, [x7, #7]
    // 0xb8bcc0: DecompressPointer r5
    //     0xb8bcc0: add             x5, x5, HEAP, lsl #32
    // 0xb8bcc4: cmp             w5, NULL
    // 0xb8bcc8: b.ne            #0xb8bcd4
    // 0xb8bccc: r5 = 0
    //     0xb8bccc: movz            x5, #0
    // 0xb8bcd0: b               #0xb8bce4
    // 0xb8bcd4: r7 = LoadInt32Instr(r5)
    //     0xb8bcd4: sbfx            x7, x5, #1, #0x1f
    //     0xb8bcd8: tbz             w5, #0, #0xb8bce0
    //     0xb8bcdc: ldur            x7, [x5, #7]
    // 0xb8bce0: mov             x5, x7
    // 0xb8bce4: cmp             w1, NULL
    // 0xb8bce8: b.ne            #0xb8bcf4
    // 0xb8bcec: r7 = Null
    //     0xb8bcec: mov             x7, NULL
    // 0xb8bcf0: b               #0xb8bd2c
    // 0xb8bcf4: LoadField: r7 = r1->field_f
    //     0xb8bcf4: ldur            w7, [x1, #0xf]
    // 0xb8bcf8: DecompressPointer r7
    //     0xb8bcf8: add             x7, x7, HEAP, lsl #32
    // 0xb8bcfc: cmp             w7, NULL
    // 0xb8bd00: b.ne            #0xb8bd0c
    // 0xb8bd04: r7 = Null
    //     0xb8bd04: mov             x7, NULL
    // 0xb8bd08: b               #0xb8bd2c
    // 0xb8bd0c: LoadField: r8 = r7->field_1b
    //     0xb8bd0c: ldur            w8, [x7, #0x1b]
    // 0xb8bd10: DecompressPointer r8
    //     0xb8bd10: add             x8, x8, HEAP, lsl #32
    // 0xb8bd14: cmp             w8, NULL
    // 0xb8bd18: b.ne            #0xb8bd24
    // 0xb8bd1c: r7 = Null
    //     0xb8bd1c: mov             x7, NULL
    // 0xb8bd20: b               #0xb8bd2c
    // 0xb8bd24: LoadField: r7 = r8->field_b
    //     0xb8bd24: ldur            w7, [x8, #0xb]
    // 0xb8bd28: DecompressPointer r7
    //     0xb8bd28: add             x7, x7, HEAP, lsl #32
    // 0xb8bd2c: cmp             w7, NULL
    // 0xb8bd30: b.ne            #0xb8bd3c
    // 0xb8bd34: r7 = 0
    //     0xb8bd34: movz            x7, #0
    // 0xb8bd38: b               #0xb8bd4c
    // 0xb8bd3c: r8 = LoadInt32Instr(r7)
    //     0xb8bd3c: sbfx            x8, x7, #1, #0x1f
    //     0xb8bd40: tbz             w7, #0, #0xb8bd48
    //     0xb8bd44: ldur            x8, [x7, #7]
    // 0xb8bd48: mov             x7, x8
    // 0xb8bd4c: add             x8, x5, x7
    // 0xb8bd50: cmp             w1, NULL
    // 0xb8bd54: b.ne            #0xb8bd60
    // 0xb8bd58: r5 = Null
    //     0xb8bd58: mov             x5, NULL
    // 0xb8bd5c: b               #0xb8bd98
    // 0xb8bd60: LoadField: r5 = r1->field_f
    //     0xb8bd60: ldur            w5, [x1, #0xf]
    // 0xb8bd64: DecompressPointer r5
    //     0xb8bd64: add             x5, x5, HEAP, lsl #32
    // 0xb8bd68: cmp             w5, NULL
    // 0xb8bd6c: b.ne            #0xb8bd78
    // 0xb8bd70: r5 = Null
    //     0xb8bd70: mov             x5, NULL
    // 0xb8bd74: b               #0xb8bd98
    // 0xb8bd78: LoadField: r7 = r5->field_1b
    //     0xb8bd78: ldur            w7, [x5, #0x1b]
    // 0xb8bd7c: DecompressPointer r7
    //     0xb8bd7c: add             x7, x7, HEAP, lsl #32
    // 0xb8bd80: cmp             w7, NULL
    // 0xb8bd84: b.ne            #0xb8bd90
    // 0xb8bd88: r5 = Null
    //     0xb8bd88: mov             x5, NULL
    // 0xb8bd8c: b               #0xb8bd98
    // 0xb8bd90: LoadField: r5 = r7->field_f
    //     0xb8bd90: ldur            w5, [x7, #0xf]
    // 0xb8bd94: DecompressPointer r5
    //     0xb8bd94: add             x5, x5, HEAP, lsl #32
    // 0xb8bd98: cmp             w5, NULL
    // 0xb8bd9c: b.ne            #0xb8bda8
    // 0xb8bda0: r5 = 0
    //     0xb8bda0: movz            x5, #0
    // 0xb8bda4: b               #0xb8bdb8
    // 0xb8bda8: r7 = LoadInt32Instr(r5)
    //     0xb8bda8: sbfx            x7, x5, #1, #0x1f
    //     0xb8bdac: tbz             w5, #0, #0xb8bdb4
    //     0xb8bdb0: ldur            x7, [x5, #7]
    // 0xb8bdb4: mov             x5, x7
    // 0xb8bdb8: add             x7, x8, x5
    // 0xb8bdbc: cmp             w1, NULL
    // 0xb8bdc0: b.ne            #0xb8bdcc
    // 0xb8bdc4: r5 = Null
    //     0xb8bdc4: mov             x5, NULL
    // 0xb8bdc8: b               #0xb8be04
    // 0xb8bdcc: LoadField: r5 = r1->field_f
    //     0xb8bdcc: ldur            w5, [x1, #0xf]
    // 0xb8bdd0: DecompressPointer r5
    //     0xb8bdd0: add             x5, x5, HEAP, lsl #32
    // 0xb8bdd4: cmp             w5, NULL
    // 0xb8bdd8: b.ne            #0xb8bde4
    // 0xb8bddc: r5 = Null
    //     0xb8bddc: mov             x5, NULL
    // 0xb8bde0: b               #0xb8be04
    // 0xb8bde4: LoadField: r8 = r5->field_1b
    //     0xb8bde4: ldur            w8, [x5, #0x1b]
    // 0xb8bde8: DecompressPointer r8
    //     0xb8bde8: add             x8, x8, HEAP, lsl #32
    // 0xb8bdec: cmp             w8, NULL
    // 0xb8bdf0: b.ne            #0xb8bdfc
    // 0xb8bdf4: r5 = Null
    //     0xb8bdf4: mov             x5, NULL
    // 0xb8bdf8: b               #0xb8be04
    // 0xb8bdfc: LoadField: r5 = r8->field_13
    //     0xb8bdfc: ldur            w5, [x8, #0x13]
    // 0xb8be00: DecompressPointer r5
    //     0xb8be00: add             x5, x5, HEAP, lsl #32
    // 0xb8be04: cmp             w5, NULL
    // 0xb8be08: b.ne            #0xb8be14
    // 0xb8be0c: r5 = 0
    //     0xb8be0c: movz            x5, #0
    // 0xb8be10: b               #0xb8be24
    // 0xb8be14: r8 = LoadInt32Instr(r5)
    //     0xb8be14: sbfx            x8, x5, #1, #0x1f
    //     0xb8be18: tbz             w5, #0, #0xb8be20
    //     0xb8be1c: ldur            x8, [x5, #7]
    // 0xb8be20: mov             x5, x8
    // 0xb8be24: add             x8, x7, x5
    // 0xb8be28: cmp             w1, NULL
    // 0xb8be2c: b.ne            #0xb8be38
    // 0xb8be30: r5 = Null
    //     0xb8be30: mov             x5, NULL
    // 0xb8be34: b               #0xb8be70
    // 0xb8be38: LoadField: r5 = r1->field_f
    //     0xb8be38: ldur            w5, [x1, #0xf]
    // 0xb8be3c: DecompressPointer r5
    //     0xb8be3c: add             x5, x5, HEAP, lsl #32
    // 0xb8be40: cmp             w5, NULL
    // 0xb8be44: b.ne            #0xb8be50
    // 0xb8be48: r5 = Null
    //     0xb8be48: mov             x5, NULL
    // 0xb8be4c: b               #0xb8be70
    // 0xb8be50: LoadField: r7 = r5->field_1b
    //     0xb8be50: ldur            w7, [x5, #0x1b]
    // 0xb8be54: DecompressPointer r7
    //     0xb8be54: add             x7, x7, HEAP, lsl #32
    // 0xb8be58: cmp             w7, NULL
    // 0xb8be5c: b.ne            #0xb8be68
    // 0xb8be60: r5 = Null
    //     0xb8be60: mov             x5, NULL
    // 0xb8be64: b               #0xb8be70
    // 0xb8be68: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb8be68: ldur            w5, [x7, #0x17]
    // 0xb8be6c: DecompressPointer r5
    //     0xb8be6c: add             x5, x5, HEAP, lsl #32
    // 0xb8be70: cmp             w5, NULL
    // 0xb8be74: b.ne            #0xb8be80
    // 0xb8be78: r5 = 0
    //     0xb8be78: movz            x5, #0
    // 0xb8be7c: b               #0xb8be90
    // 0xb8be80: r7 = LoadInt32Instr(r5)
    //     0xb8be80: sbfx            x7, x5, #1, #0x1f
    //     0xb8be84: tbz             w5, #0, #0xb8be8c
    //     0xb8be88: ldur            x7, [x5, #7]
    // 0xb8be8c: mov             x5, x7
    // 0xb8be90: add             x7, x8, x5
    // 0xb8be94: scvtf           d0, x3
    // 0xb8be98: scvtf           d1, x7
    // 0xb8be9c: fdiv            d2, d0, d1
    // 0xb8bea0: cmp             w1, NULL
    // 0xb8bea4: b.ne            #0xb8beb0
    // 0xb8bea8: r1 = Null
    //     0xb8bea8: mov             x1, NULL
    // 0xb8beac: b               #0xb8beec
    // 0xb8beb0: LoadField: r3 = r1->field_f
    //     0xb8beb0: ldur            w3, [x1, #0xf]
    // 0xb8beb4: DecompressPointer r3
    //     0xb8beb4: add             x3, x3, HEAP, lsl #32
    // 0xb8beb8: cmp             w3, NULL
    // 0xb8bebc: b.ne            #0xb8bec8
    // 0xb8bec0: r1 = Null
    //     0xb8bec0: mov             x1, NULL
    // 0xb8bec4: b               #0xb8beec
    // 0xb8bec8: LoadField: r1 = r3->field_1b
    //     0xb8bec8: ldur            w1, [x3, #0x1b]
    // 0xb8becc: DecompressPointer r1
    //     0xb8becc: add             x1, x1, HEAP, lsl #32
    // 0xb8bed0: cmp             w1, NULL
    // 0xb8bed4: b.ne            #0xb8bee0
    // 0xb8bed8: r1 = Null
    //     0xb8bed8: mov             x1, NULL
    // 0xb8bedc: b               #0xb8beec
    // 0xb8bee0: LoadField: r3 = r1->field_f
    //     0xb8bee0: ldur            w3, [x1, #0xf]
    // 0xb8bee4: DecompressPointer r3
    //     0xb8bee4: add             x3, x3, HEAP, lsl #32
    // 0xb8bee8: mov             x1, x3
    // 0xb8beec: cmp             w1, NULL
    // 0xb8bef0: b.ne            #0xb8befc
    // 0xb8bef4: r5 = 0
    //     0xb8bef4: movz            x5, #0
    // 0xb8bef8: b               #0xb8bf0c
    // 0xb8befc: r3 = LoadInt32Instr(r1)
    //     0xb8befc: sbfx            x3, x1, #1, #0x1f
    //     0xb8bf00: tbz             w1, #0, #0xb8bf08
    //     0xb8bf04: ldur            x3, [x1, #7]
    // 0xb8bf08: mov             x5, x3
    // 0xb8bf0c: mov             x1, x6
    // 0xb8bf10: mov             v0.16b, v2.16b
    // 0xb8bf14: r3 = "3"
    //     0xb8bf14: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0xb8bf18: ldr             x3, [x3, #0x8a8]
    // 0xb8bf1c: r0 = chartRow()
    //     0xb8bf1c: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb8bf20: mov             x4, x0
    // 0xb8bf24: ldur            x0, [fp, #-0x20]
    // 0xb8bf28: stur            x4, [fp, #-0x48]
    // 0xb8bf2c: LoadField: r2 = r0->field_13
    //     0xb8bf2c: ldur            w2, [x0, #0x13]
    // 0xb8bf30: DecompressPointer r2
    //     0xb8bf30: add             x2, x2, HEAP, lsl #32
    // 0xb8bf34: ldur            x6, [fp, #-8]
    // 0xb8bf38: LoadField: r1 = r6->field_b
    //     0xb8bf38: ldur            w1, [x6, #0xb]
    // 0xb8bf3c: DecompressPointer r1
    //     0xb8bf3c: add             x1, x1, HEAP, lsl #32
    // 0xb8bf40: cmp             w1, NULL
    // 0xb8bf44: b.eq            #0xb8d274
    // 0xb8bf48: LoadField: r3 = r1->field_f
    //     0xb8bf48: ldur            w3, [x1, #0xf]
    // 0xb8bf4c: DecompressPointer r3
    //     0xb8bf4c: add             x3, x3, HEAP, lsl #32
    // 0xb8bf50: LoadField: r1 = r3->field_b
    //     0xb8bf50: ldur            w1, [x3, #0xb]
    // 0xb8bf54: DecompressPointer r1
    //     0xb8bf54: add             x1, x1, HEAP, lsl #32
    // 0xb8bf58: cmp             w1, NULL
    // 0xb8bf5c: b.ne            #0xb8bf68
    // 0xb8bf60: r3 = Null
    //     0xb8bf60: mov             x3, NULL
    // 0xb8bf64: b               #0xb8bfa0
    // 0xb8bf68: LoadField: r3 = r1->field_f
    //     0xb8bf68: ldur            w3, [x1, #0xf]
    // 0xb8bf6c: DecompressPointer r3
    //     0xb8bf6c: add             x3, x3, HEAP, lsl #32
    // 0xb8bf70: cmp             w3, NULL
    // 0xb8bf74: b.ne            #0xb8bf80
    // 0xb8bf78: r3 = Null
    //     0xb8bf78: mov             x3, NULL
    // 0xb8bf7c: b               #0xb8bfa0
    // 0xb8bf80: LoadField: r5 = r3->field_1b
    //     0xb8bf80: ldur            w5, [x3, #0x1b]
    // 0xb8bf84: DecompressPointer r5
    //     0xb8bf84: add             x5, x5, HEAP, lsl #32
    // 0xb8bf88: cmp             w5, NULL
    // 0xb8bf8c: b.ne            #0xb8bf98
    // 0xb8bf90: r3 = Null
    //     0xb8bf90: mov             x3, NULL
    // 0xb8bf94: b               #0xb8bfa0
    // 0xb8bf98: LoadField: r3 = r5->field_13
    //     0xb8bf98: ldur            w3, [x5, #0x13]
    // 0xb8bf9c: DecompressPointer r3
    //     0xb8bf9c: add             x3, x3, HEAP, lsl #32
    // 0xb8bfa0: cmp             w3, NULL
    // 0xb8bfa4: b.ne            #0xb8bfb0
    // 0xb8bfa8: r3 = 0
    //     0xb8bfa8: movz            x3, #0
    // 0xb8bfac: b               #0xb8bfc0
    // 0xb8bfb0: r5 = LoadInt32Instr(r3)
    //     0xb8bfb0: sbfx            x5, x3, #1, #0x1f
    //     0xb8bfb4: tbz             w3, #0, #0xb8bfbc
    //     0xb8bfb8: ldur            x5, [x3, #7]
    // 0xb8bfbc: mov             x3, x5
    // 0xb8bfc0: cmp             w1, NULL
    // 0xb8bfc4: b.ne            #0xb8bfd0
    // 0xb8bfc8: r5 = Null
    //     0xb8bfc8: mov             x5, NULL
    // 0xb8bfcc: b               #0xb8c008
    // 0xb8bfd0: LoadField: r5 = r1->field_f
    //     0xb8bfd0: ldur            w5, [x1, #0xf]
    // 0xb8bfd4: DecompressPointer r5
    //     0xb8bfd4: add             x5, x5, HEAP, lsl #32
    // 0xb8bfd8: cmp             w5, NULL
    // 0xb8bfdc: b.ne            #0xb8bfe8
    // 0xb8bfe0: r5 = Null
    //     0xb8bfe0: mov             x5, NULL
    // 0xb8bfe4: b               #0xb8c008
    // 0xb8bfe8: LoadField: r7 = r5->field_1b
    //     0xb8bfe8: ldur            w7, [x5, #0x1b]
    // 0xb8bfec: DecompressPointer r7
    //     0xb8bfec: add             x7, x7, HEAP, lsl #32
    // 0xb8bff0: cmp             w7, NULL
    // 0xb8bff4: b.ne            #0xb8c000
    // 0xb8bff8: r5 = Null
    //     0xb8bff8: mov             x5, NULL
    // 0xb8bffc: b               #0xb8c008
    // 0xb8c000: LoadField: r5 = r7->field_7
    //     0xb8c000: ldur            w5, [x7, #7]
    // 0xb8c004: DecompressPointer r5
    //     0xb8c004: add             x5, x5, HEAP, lsl #32
    // 0xb8c008: cmp             w5, NULL
    // 0xb8c00c: b.ne            #0xb8c018
    // 0xb8c010: r5 = 0
    //     0xb8c010: movz            x5, #0
    // 0xb8c014: b               #0xb8c028
    // 0xb8c018: r7 = LoadInt32Instr(r5)
    //     0xb8c018: sbfx            x7, x5, #1, #0x1f
    //     0xb8c01c: tbz             w5, #0, #0xb8c024
    //     0xb8c020: ldur            x7, [x5, #7]
    // 0xb8c024: mov             x5, x7
    // 0xb8c028: cmp             w1, NULL
    // 0xb8c02c: b.ne            #0xb8c038
    // 0xb8c030: r7 = Null
    //     0xb8c030: mov             x7, NULL
    // 0xb8c034: b               #0xb8c070
    // 0xb8c038: LoadField: r7 = r1->field_f
    //     0xb8c038: ldur            w7, [x1, #0xf]
    // 0xb8c03c: DecompressPointer r7
    //     0xb8c03c: add             x7, x7, HEAP, lsl #32
    // 0xb8c040: cmp             w7, NULL
    // 0xb8c044: b.ne            #0xb8c050
    // 0xb8c048: r7 = Null
    //     0xb8c048: mov             x7, NULL
    // 0xb8c04c: b               #0xb8c070
    // 0xb8c050: LoadField: r8 = r7->field_1b
    //     0xb8c050: ldur            w8, [x7, #0x1b]
    // 0xb8c054: DecompressPointer r8
    //     0xb8c054: add             x8, x8, HEAP, lsl #32
    // 0xb8c058: cmp             w8, NULL
    // 0xb8c05c: b.ne            #0xb8c068
    // 0xb8c060: r7 = Null
    //     0xb8c060: mov             x7, NULL
    // 0xb8c064: b               #0xb8c070
    // 0xb8c068: LoadField: r7 = r8->field_b
    //     0xb8c068: ldur            w7, [x8, #0xb]
    // 0xb8c06c: DecompressPointer r7
    //     0xb8c06c: add             x7, x7, HEAP, lsl #32
    // 0xb8c070: cmp             w7, NULL
    // 0xb8c074: b.ne            #0xb8c080
    // 0xb8c078: r7 = 0
    //     0xb8c078: movz            x7, #0
    // 0xb8c07c: b               #0xb8c090
    // 0xb8c080: r8 = LoadInt32Instr(r7)
    //     0xb8c080: sbfx            x8, x7, #1, #0x1f
    //     0xb8c084: tbz             w7, #0, #0xb8c08c
    //     0xb8c088: ldur            x8, [x7, #7]
    // 0xb8c08c: mov             x7, x8
    // 0xb8c090: add             x8, x5, x7
    // 0xb8c094: cmp             w1, NULL
    // 0xb8c098: b.ne            #0xb8c0a4
    // 0xb8c09c: r5 = Null
    //     0xb8c09c: mov             x5, NULL
    // 0xb8c0a0: b               #0xb8c0dc
    // 0xb8c0a4: LoadField: r5 = r1->field_f
    //     0xb8c0a4: ldur            w5, [x1, #0xf]
    // 0xb8c0a8: DecompressPointer r5
    //     0xb8c0a8: add             x5, x5, HEAP, lsl #32
    // 0xb8c0ac: cmp             w5, NULL
    // 0xb8c0b0: b.ne            #0xb8c0bc
    // 0xb8c0b4: r5 = Null
    //     0xb8c0b4: mov             x5, NULL
    // 0xb8c0b8: b               #0xb8c0dc
    // 0xb8c0bc: LoadField: r7 = r5->field_1b
    //     0xb8c0bc: ldur            w7, [x5, #0x1b]
    // 0xb8c0c0: DecompressPointer r7
    //     0xb8c0c0: add             x7, x7, HEAP, lsl #32
    // 0xb8c0c4: cmp             w7, NULL
    // 0xb8c0c8: b.ne            #0xb8c0d4
    // 0xb8c0cc: r5 = Null
    //     0xb8c0cc: mov             x5, NULL
    // 0xb8c0d0: b               #0xb8c0dc
    // 0xb8c0d4: LoadField: r5 = r7->field_f
    //     0xb8c0d4: ldur            w5, [x7, #0xf]
    // 0xb8c0d8: DecompressPointer r5
    //     0xb8c0d8: add             x5, x5, HEAP, lsl #32
    // 0xb8c0dc: cmp             w5, NULL
    // 0xb8c0e0: b.ne            #0xb8c0ec
    // 0xb8c0e4: r5 = 0
    //     0xb8c0e4: movz            x5, #0
    // 0xb8c0e8: b               #0xb8c0fc
    // 0xb8c0ec: r7 = LoadInt32Instr(r5)
    //     0xb8c0ec: sbfx            x7, x5, #1, #0x1f
    //     0xb8c0f0: tbz             w5, #0, #0xb8c0f8
    //     0xb8c0f4: ldur            x7, [x5, #7]
    // 0xb8c0f8: mov             x5, x7
    // 0xb8c0fc: add             x7, x8, x5
    // 0xb8c100: cmp             w1, NULL
    // 0xb8c104: b.ne            #0xb8c110
    // 0xb8c108: r5 = Null
    //     0xb8c108: mov             x5, NULL
    // 0xb8c10c: b               #0xb8c148
    // 0xb8c110: LoadField: r5 = r1->field_f
    //     0xb8c110: ldur            w5, [x1, #0xf]
    // 0xb8c114: DecompressPointer r5
    //     0xb8c114: add             x5, x5, HEAP, lsl #32
    // 0xb8c118: cmp             w5, NULL
    // 0xb8c11c: b.ne            #0xb8c128
    // 0xb8c120: r5 = Null
    //     0xb8c120: mov             x5, NULL
    // 0xb8c124: b               #0xb8c148
    // 0xb8c128: LoadField: r8 = r5->field_1b
    //     0xb8c128: ldur            w8, [x5, #0x1b]
    // 0xb8c12c: DecompressPointer r8
    //     0xb8c12c: add             x8, x8, HEAP, lsl #32
    // 0xb8c130: cmp             w8, NULL
    // 0xb8c134: b.ne            #0xb8c140
    // 0xb8c138: r5 = Null
    //     0xb8c138: mov             x5, NULL
    // 0xb8c13c: b               #0xb8c148
    // 0xb8c140: LoadField: r5 = r8->field_13
    //     0xb8c140: ldur            w5, [x8, #0x13]
    // 0xb8c144: DecompressPointer r5
    //     0xb8c144: add             x5, x5, HEAP, lsl #32
    // 0xb8c148: cmp             w5, NULL
    // 0xb8c14c: b.ne            #0xb8c158
    // 0xb8c150: r5 = 0
    //     0xb8c150: movz            x5, #0
    // 0xb8c154: b               #0xb8c168
    // 0xb8c158: r8 = LoadInt32Instr(r5)
    //     0xb8c158: sbfx            x8, x5, #1, #0x1f
    //     0xb8c15c: tbz             w5, #0, #0xb8c164
    //     0xb8c160: ldur            x8, [x5, #7]
    // 0xb8c164: mov             x5, x8
    // 0xb8c168: add             x8, x7, x5
    // 0xb8c16c: cmp             w1, NULL
    // 0xb8c170: b.ne            #0xb8c17c
    // 0xb8c174: r5 = Null
    //     0xb8c174: mov             x5, NULL
    // 0xb8c178: b               #0xb8c1b4
    // 0xb8c17c: LoadField: r5 = r1->field_f
    //     0xb8c17c: ldur            w5, [x1, #0xf]
    // 0xb8c180: DecompressPointer r5
    //     0xb8c180: add             x5, x5, HEAP, lsl #32
    // 0xb8c184: cmp             w5, NULL
    // 0xb8c188: b.ne            #0xb8c194
    // 0xb8c18c: r5 = Null
    //     0xb8c18c: mov             x5, NULL
    // 0xb8c190: b               #0xb8c1b4
    // 0xb8c194: LoadField: r7 = r5->field_1b
    //     0xb8c194: ldur            w7, [x5, #0x1b]
    // 0xb8c198: DecompressPointer r7
    //     0xb8c198: add             x7, x7, HEAP, lsl #32
    // 0xb8c19c: cmp             w7, NULL
    // 0xb8c1a0: b.ne            #0xb8c1ac
    // 0xb8c1a4: r5 = Null
    //     0xb8c1a4: mov             x5, NULL
    // 0xb8c1a8: b               #0xb8c1b4
    // 0xb8c1ac: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb8c1ac: ldur            w5, [x7, #0x17]
    // 0xb8c1b0: DecompressPointer r5
    //     0xb8c1b0: add             x5, x5, HEAP, lsl #32
    // 0xb8c1b4: cmp             w5, NULL
    // 0xb8c1b8: b.ne            #0xb8c1c4
    // 0xb8c1bc: r5 = 0
    //     0xb8c1bc: movz            x5, #0
    // 0xb8c1c0: b               #0xb8c1d4
    // 0xb8c1c4: r7 = LoadInt32Instr(r5)
    //     0xb8c1c4: sbfx            x7, x5, #1, #0x1f
    //     0xb8c1c8: tbz             w5, #0, #0xb8c1d0
    //     0xb8c1cc: ldur            x7, [x5, #7]
    // 0xb8c1d0: mov             x5, x7
    // 0xb8c1d4: add             x7, x8, x5
    // 0xb8c1d8: scvtf           d0, x3
    // 0xb8c1dc: scvtf           d1, x7
    // 0xb8c1e0: fdiv            d2, d0, d1
    // 0xb8c1e4: cmp             w1, NULL
    // 0xb8c1e8: b.ne            #0xb8c1f4
    // 0xb8c1ec: r1 = Null
    //     0xb8c1ec: mov             x1, NULL
    // 0xb8c1f0: b               #0xb8c230
    // 0xb8c1f4: LoadField: r3 = r1->field_f
    //     0xb8c1f4: ldur            w3, [x1, #0xf]
    // 0xb8c1f8: DecompressPointer r3
    //     0xb8c1f8: add             x3, x3, HEAP, lsl #32
    // 0xb8c1fc: cmp             w3, NULL
    // 0xb8c200: b.ne            #0xb8c20c
    // 0xb8c204: r1 = Null
    //     0xb8c204: mov             x1, NULL
    // 0xb8c208: b               #0xb8c230
    // 0xb8c20c: LoadField: r1 = r3->field_1b
    //     0xb8c20c: ldur            w1, [x3, #0x1b]
    // 0xb8c210: DecompressPointer r1
    //     0xb8c210: add             x1, x1, HEAP, lsl #32
    // 0xb8c214: cmp             w1, NULL
    // 0xb8c218: b.ne            #0xb8c224
    // 0xb8c21c: r1 = Null
    //     0xb8c21c: mov             x1, NULL
    // 0xb8c220: b               #0xb8c230
    // 0xb8c224: LoadField: r3 = r1->field_13
    //     0xb8c224: ldur            w3, [x1, #0x13]
    // 0xb8c228: DecompressPointer r3
    //     0xb8c228: add             x3, x3, HEAP, lsl #32
    // 0xb8c22c: mov             x1, x3
    // 0xb8c230: cmp             w1, NULL
    // 0xb8c234: b.ne            #0xb8c240
    // 0xb8c238: r5 = 0
    //     0xb8c238: movz            x5, #0
    // 0xb8c23c: b               #0xb8c250
    // 0xb8c240: r3 = LoadInt32Instr(r1)
    //     0xb8c240: sbfx            x3, x1, #1, #0x1f
    //     0xb8c244: tbz             w1, #0, #0xb8c24c
    //     0xb8c248: ldur            x3, [x1, #7]
    // 0xb8c24c: mov             x5, x3
    // 0xb8c250: mov             x1, x6
    // 0xb8c254: mov             v0.16b, v2.16b
    // 0xb8c258: r3 = "2"
    //     0xb8c258: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0xb8c25c: ldr             x3, [x3, #0x8b0]
    // 0xb8c260: r0 = chartRow()
    //     0xb8c260: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb8c264: mov             x4, x0
    // 0xb8c268: ldur            x0, [fp, #-0x20]
    // 0xb8c26c: stur            x4, [fp, #-0x50]
    // 0xb8c270: LoadField: r2 = r0->field_13
    //     0xb8c270: ldur            w2, [x0, #0x13]
    // 0xb8c274: DecompressPointer r2
    //     0xb8c274: add             x2, x2, HEAP, lsl #32
    // 0xb8c278: ldur            x6, [fp, #-8]
    // 0xb8c27c: LoadField: r1 = r6->field_b
    //     0xb8c27c: ldur            w1, [x6, #0xb]
    // 0xb8c280: DecompressPointer r1
    //     0xb8c280: add             x1, x1, HEAP, lsl #32
    // 0xb8c284: cmp             w1, NULL
    // 0xb8c288: b.eq            #0xb8d278
    // 0xb8c28c: LoadField: r3 = r1->field_f
    //     0xb8c28c: ldur            w3, [x1, #0xf]
    // 0xb8c290: DecompressPointer r3
    //     0xb8c290: add             x3, x3, HEAP, lsl #32
    // 0xb8c294: LoadField: r1 = r3->field_b
    //     0xb8c294: ldur            w1, [x3, #0xb]
    // 0xb8c298: DecompressPointer r1
    //     0xb8c298: add             x1, x1, HEAP, lsl #32
    // 0xb8c29c: cmp             w1, NULL
    // 0xb8c2a0: b.ne            #0xb8c2ac
    // 0xb8c2a4: r3 = Null
    //     0xb8c2a4: mov             x3, NULL
    // 0xb8c2a8: b               #0xb8c2e4
    // 0xb8c2ac: LoadField: r3 = r1->field_f
    //     0xb8c2ac: ldur            w3, [x1, #0xf]
    // 0xb8c2b0: DecompressPointer r3
    //     0xb8c2b0: add             x3, x3, HEAP, lsl #32
    // 0xb8c2b4: cmp             w3, NULL
    // 0xb8c2b8: b.ne            #0xb8c2c4
    // 0xb8c2bc: r3 = Null
    //     0xb8c2bc: mov             x3, NULL
    // 0xb8c2c0: b               #0xb8c2e4
    // 0xb8c2c4: LoadField: r5 = r3->field_1b
    //     0xb8c2c4: ldur            w5, [x3, #0x1b]
    // 0xb8c2c8: DecompressPointer r5
    //     0xb8c2c8: add             x5, x5, HEAP, lsl #32
    // 0xb8c2cc: cmp             w5, NULL
    // 0xb8c2d0: b.ne            #0xb8c2dc
    // 0xb8c2d4: r3 = Null
    //     0xb8c2d4: mov             x3, NULL
    // 0xb8c2d8: b               #0xb8c2e4
    // 0xb8c2dc: ArrayLoad: r3 = r5[0]  ; List_4
    //     0xb8c2dc: ldur            w3, [x5, #0x17]
    // 0xb8c2e0: DecompressPointer r3
    //     0xb8c2e0: add             x3, x3, HEAP, lsl #32
    // 0xb8c2e4: cmp             w3, NULL
    // 0xb8c2e8: b.ne            #0xb8c2f4
    // 0xb8c2ec: r3 = 0
    //     0xb8c2ec: movz            x3, #0
    // 0xb8c2f0: b               #0xb8c304
    // 0xb8c2f4: r5 = LoadInt32Instr(r3)
    //     0xb8c2f4: sbfx            x5, x3, #1, #0x1f
    //     0xb8c2f8: tbz             w3, #0, #0xb8c300
    //     0xb8c2fc: ldur            x5, [x3, #7]
    // 0xb8c300: mov             x3, x5
    // 0xb8c304: cmp             w1, NULL
    // 0xb8c308: b.ne            #0xb8c314
    // 0xb8c30c: r5 = Null
    //     0xb8c30c: mov             x5, NULL
    // 0xb8c310: b               #0xb8c34c
    // 0xb8c314: LoadField: r5 = r1->field_f
    //     0xb8c314: ldur            w5, [x1, #0xf]
    // 0xb8c318: DecompressPointer r5
    //     0xb8c318: add             x5, x5, HEAP, lsl #32
    // 0xb8c31c: cmp             w5, NULL
    // 0xb8c320: b.ne            #0xb8c32c
    // 0xb8c324: r5 = Null
    //     0xb8c324: mov             x5, NULL
    // 0xb8c328: b               #0xb8c34c
    // 0xb8c32c: LoadField: r7 = r5->field_1b
    //     0xb8c32c: ldur            w7, [x5, #0x1b]
    // 0xb8c330: DecompressPointer r7
    //     0xb8c330: add             x7, x7, HEAP, lsl #32
    // 0xb8c334: cmp             w7, NULL
    // 0xb8c338: b.ne            #0xb8c344
    // 0xb8c33c: r5 = Null
    //     0xb8c33c: mov             x5, NULL
    // 0xb8c340: b               #0xb8c34c
    // 0xb8c344: LoadField: r5 = r7->field_7
    //     0xb8c344: ldur            w5, [x7, #7]
    // 0xb8c348: DecompressPointer r5
    //     0xb8c348: add             x5, x5, HEAP, lsl #32
    // 0xb8c34c: cmp             w5, NULL
    // 0xb8c350: b.ne            #0xb8c35c
    // 0xb8c354: r5 = 0
    //     0xb8c354: movz            x5, #0
    // 0xb8c358: b               #0xb8c36c
    // 0xb8c35c: r7 = LoadInt32Instr(r5)
    //     0xb8c35c: sbfx            x7, x5, #1, #0x1f
    //     0xb8c360: tbz             w5, #0, #0xb8c368
    //     0xb8c364: ldur            x7, [x5, #7]
    // 0xb8c368: mov             x5, x7
    // 0xb8c36c: cmp             w1, NULL
    // 0xb8c370: b.ne            #0xb8c37c
    // 0xb8c374: r7 = Null
    //     0xb8c374: mov             x7, NULL
    // 0xb8c378: b               #0xb8c3b4
    // 0xb8c37c: LoadField: r7 = r1->field_f
    //     0xb8c37c: ldur            w7, [x1, #0xf]
    // 0xb8c380: DecompressPointer r7
    //     0xb8c380: add             x7, x7, HEAP, lsl #32
    // 0xb8c384: cmp             w7, NULL
    // 0xb8c388: b.ne            #0xb8c394
    // 0xb8c38c: r7 = Null
    //     0xb8c38c: mov             x7, NULL
    // 0xb8c390: b               #0xb8c3b4
    // 0xb8c394: LoadField: r8 = r7->field_1b
    //     0xb8c394: ldur            w8, [x7, #0x1b]
    // 0xb8c398: DecompressPointer r8
    //     0xb8c398: add             x8, x8, HEAP, lsl #32
    // 0xb8c39c: cmp             w8, NULL
    // 0xb8c3a0: b.ne            #0xb8c3ac
    // 0xb8c3a4: r7 = Null
    //     0xb8c3a4: mov             x7, NULL
    // 0xb8c3a8: b               #0xb8c3b4
    // 0xb8c3ac: LoadField: r7 = r8->field_b
    //     0xb8c3ac: ldur            w7, [x8, #0xb]
    // 0xb8c3b0: DecompressPointer r7
    //     0xb8c3b0: add             x7, x7, HEAP, lsl #32
    // 0xb8c3b4: cmp             w7, NULL
    // 0xb8c3b8: b.ne            #0xb8c3c4
    // 0xb8c3bc: r7 = 0
    //     0xb8c3bc: movz            x7, #0
    // 0xb8c3c0: b               #0xb8c3d4
    // 0xb8c3c4: r8 = LoadInt32Instr(r7)
    //     0xb8c3c4: sbfx            x8, x7, #1, #0x1f
    //     0xb8c3c8: tbz             w7, #0, #0xb8c3d0
    //     0xb8c3cc: ldur            x8, [x7, #7]
    // 0xb8c3d0: mov             x7, x8
    // 0xb8c3d4: add             x8, x5, x7
    // 0xb8c3d8: cmp             w1, NULL
    // 0xb8c3dc: b.ne            #0xb8c3e8
    // 0xb8c3e0: r5 = Null
    //     0xb8c3e0: mov             x5, NULL
    // 0xb8c3e4: b               #0xb8c420
    // 0xb8c3e8: LoadField: r5 = r1->field_f
    //     0xb8c3e8: ldur            w5, [x1, #0xf]
    // 0xb8c3ec: DecompressPointer r5
    //     0xb8c3ec: add             x5, x5, HEAP, lsl #32
    // 0xb8c3f0: cmp             w5, NULL
    // 0xb8c3f4: b.ne            #0xb8c400
    // 0xb8c3f8: r5 = Null
    //     0xb8c3f8: mov             x5, NULL
    // 0xb8c3fc: b               #0xb8c420
    // 0xb8c400: LoadField: r7 = r5->field_1b
    //     0xb8c400: ldur            w7, [x5, #0x1b]
    // 0xb8c404: DecompressPointer r7
    //     0xb8c404: add             x7, x7, HEAP, lsl #32
    // 0xb8c408: cmp             w7, NULL
    // 0xb8c40c: b.ne            #0xb8c418
    // 0xb8c410: r5 = Null
    //     0xb8c410: mov             x5, NULL
    // 0xb8c414: b               #0xb8c420
    // 0xb8c418: LoadField: r5 = r7->field_f
    //     0xb8c418: ldur            w5, [x7, #0xf]
    // 0xb8c41c: DecompressPointer r5
    //     0xb8c41c: add             x5, x5, HEAP, lsl #32
    // 0xb8c420: cmp             w5, NULL
    // 0xb8c424: b.ne            #0xb8c430
    // 0xb8c428: r5 = 0
    //     0xb8c428: movz            x5, #0
    // 0xb8c42c: b               #0xb8c440
    // 0xb8c430: r7 = LoadInt32Instr(r5)
    //     0xb8c430: sbfx            x7, x5, #1, #0x1f
    //     0xb8c434: tbz             w5, #0, #0xb8c43c
    //     0xb8c438: ldur            x7, [x5, #7]
    // 0xb8c43c: mov             x5, x7
    // 0xb8c440: add             x7, x8, x5
    // 0xb8c444: cmp             w1, NULL
    // 0xb8c448: b.ne            #0xb8c454
    // 0xb8c44c: r5 = Null
    //     0xb8c44c: mov             x5, NULL
    // 0xb8c450: b               #0xb8c48c
    // 0xb8c454: LoadField: r5 = r1->field_f
    //     0xb8c454: ldur            w5, [x1, #0xf]
    // 0xb8c458: DecompressPointer r5
    //     0xb8c458: add             x5, x5, HEAP, lsl #32
    // 0xb8c45c: cmp             w5, NULL
    // 0xb8c460: b.ne            #0xb8c46c
    // 0xb8c464: r5 = Null
    //     0xb8c464: mov             x5, NULL
    // 0xb8c468: b               #0xb8c48c
    // 0xb8c46c: LoadField: r8 = r5->field_1b
    //     0xb8c46c: ldur            w8, [x5, #0x1b]
    // 0xb8c470: DecompressPointer r8
    //     0xb8c470: add             x8, x8, HEAP, lsl #32
    // 0xb8c474: cmp             w8, NULL
    // 0xb8c478: b.ne            #0xb8c484
    // 0xb8c47c: r5 = Null
    //     0xb8c47c: mov             x5, NULL
    // 0xb8c480: b               #0xb8c48c
    // 0xb8c484: LoadField: r5 = r8->field_13
    //     0xb8c484: ldur            w5, [x8, #0x13]
    // 0xb8c488: DecompressPointer r5
    //     0xb8c488: add             x5, x5, HEAP, lsl #32
    // 0xb8c48c: cmp             w5, NULL
    // 0xb8c490: b.ne            #0xb8c49c
    // 0xb8c494: r5 = 0
    //     0xb8c494: movz            x5, #0
    // 0xb8c498: b               #0xb8c4ac
    // 0xb8c49c: r8 = LoadInt32Instr(r5)
    //     0xb8c49c: sbfx            x8, x5, #1, #0x1f
    //     0xb8c4a0: tbz             w5, #0, #0xb8c4a8
    //     0xb8c4a4: ldur            x8, [x5, #7]
    // 0xb8c4a8: mov             x5, x8
    // 0xb8c4ac: add             x8, x7, x5
    // 0xb8c4b0: cmp             w1, NULL
    // 0xb8c4b4: b.ne            #0xb8c4c0
    // 0xb8c4b8: r5 = Null
    //     0xb8c4b8: mov             x5, NULL
    // 0xb8c4bc: b               #0xb8c4f8
    // 0xb8c4c0: LoadField: r5 = r1->field_f
    //     0xb8c4c0: ldur            w5, [x1, #0xf]
    // 0xb8c4c4: DecompressPointer r5
    //     0xb8c4c4: add             x5, x5, HEAP, lsl #32
    // 0xb8c4c8: cmp             w5, NULL
    // 0xb8c4cc: b.ne            #0xb8c4d8
    // 0xb8c4d0: r5 = Null
    //     0xb8c4d0: mov             x5, NULL
    // 0xb8c4d4: b               #0xb8c4f8
    // 0xb8c4d8: LoadField: r7 = r5->field_1b
    //     0xb8c4d8: ldur            w7, [x5, #0x1b]
    // 0xb8c4dc: DecompressPointer r7
    //     0xb8c4dc: add             x7, x7, HEAP, lsl #32
    // 0xb8c4e0: cmp             w7, NULL
    // 0xb8c4e4: b.ne            #0xb8c4f0
    // 0xb8c4e8: r5 = Null
    //     0xb8c4e8: mov             x5, NULL
    // 0xb8c4ec: b               #0xb8c4f8
    // 0xb8c4f0: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xb8c4f0: ldur            w5, [x7, #0x17]
    // 0xb8c4f4: DecompressPointer r5
    //     0xb8c4f4: add             x5, x5, HEAP, lsl #32
    // 0xb8c4f8: cmp             w5, NULL
    // 0xb8c4fc: b.ne            #0xb8c508
    // 0xb8c500: r5 = 0
    //     0xb8c500: movz            x5, #0
    // 0xb8c504: b               #0xb8c518
    // 0xb8c508: r7 = LoadInt32Instr(r5)
    //     0xb8c508: sbfx            x7, x5, #1, #0x1f
    //     0xb8c50c: tbz             w5, #0, #0xb8c514
    //     0xb8c510: ldur            x7, [x5, #7]
    // 0xb8c514: mov             x5, x7
    // 0xb8c518: add             x7, x8, x5
    // 0xb8c51c: scvtf           d0, x3
    // 0xb8c520: scvtf           d1, x7
    // 0xb8c524: fdiv            d2, d0, d1
    // 0xb8c528: cmp             w1, NULL
    // 0xb8c52c: b.ne            #0xb8c538
    // 0xb8c530: r1 = Null
    //     0xb8c530: mov             x1, NULL
    // 0xb8c534: b               #0xb8c574
    // 0xb8c538: LoadField: r3 = r1->field_f
    //     0xb8c538: ldur            w3, [x1, #0xf]
    // 0xb8c53c: DecompressPointer r3
    //     0xb8c53c: add             x3, x3, HEAP, lsl #32
    // 0xb8c540: cmp             w3, NULL
    // 0xb8c544: b.ne            #0xb8c550
    // 0xb8c548: r1 = Null
    //     0xb8c548: mov             x1, NULL
    // 0xb8c54c: b               #0xb8c574
    // 0xb8c550: LoadField: r1 = r3->field_1b
    //     0xb8c550: ldur            w1, [x3, #0x1b]
    // 0xb8c554: DecompressPointer r1
    //     0xb8c554: add             x1, x1, HEAP, lsl #32
    // 0xb8c558: cmp             w1, NULL
    // 0xb8c55c: b.ne            #0xb8c568
    // 0xb8c560: r1 = Null
    //     0xb8c560: mov             x1, NULL
    // 0xb8c564: b               #0xb8c574
    // 0xb8c568: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb8c568: ldur            w3, [x1, #0x17]
    // 0xb8c56c: DecompressPointer r3
    //     0xb8c56c: add             x3, x3, HEAP, lsl #32
    // 0xb8c570: mov             x1, x3
    // 0xb8c574: cmp             w1, NULL
    // 0xb8c578: b.ne            #0xb8c584
    // 0xb8c57c: r5 = 0
    //     0xb8c57c: movz            x5, #0
    // 0xb8c580: b               #0xb8c594
    // 0xb8c584: r3 = LoadInt32Instr(r1)
    //     0xb8c584: sbfx            x3, x1, #1, #0x1f
    //     0xb8c588: tbz             w1, #0, #0xb8c590
    //     0xb8c58c: ldur            x3, [x1, #7]
    // 0xb8c590: mov             x5, x3
    // 0xb8c594: ldur            x11, [fp, #-0x10]
    // 0xb8c598: ldur            x10, [fp, #-0x18]
    // 0xb8c59c: ldur            x9, [fp, #-0x30]
    // 0xb8c5a0: ldur            x8, [fp, #-0x38]
    // 0xb8c5a4: ldur            x7, [fp, #-0x48]
    // 0xb8c5a8: mov             x1, x6
    // 0xb8c5ac: mov             v0.16b, v2.16b
    // 0xb8c5b0: r3 = "1"
    //     0xb8c5b0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xb8c5b4: ldr             x3, [x3, #0xba8]
    // 0xb8c5b8: r0 = chartRow()
    //     0xb8c5b8: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xb8c5bc: r1 = Null
    //     0xb8c5bc: mov             x1, NULL
    // 0xb8c5c0: r2 = 14
    //     0xb8c5c0: movz            x2, #0xe
    // 0xb8c5c4: stur            x0, [fp, #-0x58]
    // 0xb8c5c8: r0 = AllocateArray()
    //     0xb8c5c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8c5cc: stur            x0, [fp, #-0x60]
    // 0xb8c5d0: r16 = Instance_SizedBox
    //     0xb8c5d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb8c5d4: ldr             x16, [x16, #0x8b8]
    // 0xb8c5d8: StoreField: r0->field_f = r16
    //     0xb8c5d8: stur            w16, [x0, #0xf]
    // 0xb8c5dc: ldur            x1, [fp, #-0x30]
    // 0xb8c5e0: StoreField: r0->field_13 = r1
    //     0xb8c5e0: stur            w1, [x0, #0x13]
    // 0xb8c5e4: ldur            x1, [fp, #-0x38]
    // 0xb8c5e8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8c5e8: stur            w1, [x0, #0x17]
    // 0xb8c5ec: ldur            x1, [fp, #-0x48]
    // 0xb8c5f0: StoreField: r0->field_1b = r1
    //     0xb8c5f0: stur            w1, [x0, #0x1b]
    // 0xb8c5f4: ldur            x1, [fp, #-0x50]
    // 0xb8c5f8: StoreField: r0->field_1f = r1
    //     0xb8c5f8: stur            w1, [x0, #0x1f]
    // 0xb8c5fc: ldur            x1, [fp, #-0x58]
    // 0xb8c600: StoreField: r0->field_23 = r1
    //     0xb8c600: stur            w1, [x0, #0x23]
    // 0xb8c604: r16 = Instance_SizedBox
    //     0xb8c604: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb8c608: ldr             x16, [x16, #0x8b8]
    // 0xb8c60c: StoreField: r0->field_27 = r16
    //     0xb8c60c: stur            w16, [x0, #0x27]
    // 0xb8c610: r1 = <Widget>
    //     0xb8c610: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8c614: r0 = AllocateGrowableArray()
    //     0xb8c614: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8c618: mov             x1, x0
    // 0xb8c61c: ldur            x0, [fp, #-0x60]
    // 0xb8c620: stur            x1, [fp, #-0x30]
    // 0xb8c624: StoreField: r1->field_f = r0
    //     0xb8c624: stur            w0, [x1, #0xf]
    // 0xb8c628: r0 = 14
    //     0xb8c628: movz            x0, #0xe
    // 0xb8c62c: StoreField: r1->field_b = r0
    //     0xb8c62c: stur            w0, [x1, #0xb]
    // 0xb8c630: r0 = Column()
    //     0xb8c630: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8c634: mov             x1, x0
    // 0xb8c638: r0 = Instance_Axis
    //     0xb8c638: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8c63c: stur            x1, [fp, #-0x38]
    // 0xb8c640: StoreField: r1->field_f = r0
    //     0xb8c640: stur            w0, [x1, #0xf]
    // 0xb8c644: r2 = Instance_MainAxisAlignment
    //     0xb8c644: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8c648: ldr             x2, [x2, #0xa08]
    // 0xb8c64c: StoreField: r1->field_13 = r2
    //     0xb8c64c: stur            w2, [x1, #0x13]
    // 0xb8c650: r3 = Instance_MainAxisSize
    //     0xb8c650: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8c654: ldr             x3, [x3, #0xa10]
    // 0xb8c658: ArrayStore: r1[0] = r3  ; List_4
    //     0xb8c658: stur            w3, [x1, #0x17]
    // 0xb8c65c: r4 = Instance_CrossAxisAlignment
    //     0xb8c65c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8c660: ldr             x4, [x4, #0x890]
    // 0xb8c664: StoreField: r1->field_1b = r4
    //     0xb8c664: stur            w4, [x1, #0x1b]
    // 0xb8c668: r5 = Instance_VerticalDirection
    //     0xb8c668: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8c66c: ldr             x5, [x5, #0xa20]
    // 0xb8c670: StoreField: r1->field_23 = r5
    //     0xb8c670: stur            w5, [x1, #0x23]
    // 0xb8c674: r6 = Instance_Clip
    //     0xb8c674: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8c678: ldr             x6, [x6, #0x38]
    // 0xb8c67c: StoreField: r1->field_2b = r6
    //     0xb8c67c: stur            w6, [x1, #0x2b]
    // 0xb8c680: StoreField: r1->field_2f = rZR
    //     0xb8c680: stur            xzr, [x1, #0x2f]
    // 0xb8c684: ldur            x7, [fp, #-0x30]
    // 0xb8c688: StoreField: r1->field_b = r7
    //     0xb8c688: stur            w7, [x1, #0xb]
    // 0xb8c68c: r0 = InkWell()
    //     0xb8c68c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8c690: mov             x3, x0
    // 0xb8c694: ldur            x0, [fp, #-0x38]
    // 0xb8c698: stur            x3, [fp, #-0x30]
    // 0xb8c69c: StoreField: r3->field_b = r0
    //     0xb8c69c: stur            w0, [x3, #0xb]
    // 0xb8c6a0: ldur            x2, [fp, #-0x20]
    // 0xb8c6a4: r1 = Function '<anonymous closure>':.
    //     0xb8c6a4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55308] AnonymousClosure: (0xb8e00c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8c6a8: ldr             x1, [x1, #0x308]
    // 0xb8c6ac: r0 = AllocateClosure()
    //     0xb8c6ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8c6b0: mov             x1, x0
    // 0xb8c6b4: ldur            x0, [fp, #-0x30]
    // 0xb8c6b8: StoreField: r0->field_f = r1
    //     0xb8c6b8: stur            w1, [x0, #0xf]
    // 0xb8c6bc: r3 = true
    //     0xb8c6bc: add             x3, NULL, #0x20  ; true
    // 0xb8c6c0: StoreField: r0->field_43 = r3
    //     0xb8c6c0: stur            w3, [x0, #0x43]
    // 0xb8c6c4: r4 = Instance_BoxShape
    //     0xb8c6c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8c6c8: ldr             x4, [x4, #0x80]
    // 0xb8c6cc: StoreField: r0->field_47 = r4
    //     0xb8c6cc: stur            w4, [x0, #0x47]
    // 0xb8c6d0: StoreField: r0->field_6f = r3
    //     0xb8c6d0: stur            w3, [x0, #0x6f]
    // 0xb8c6d4: r5 = false
    //     0xb8c6d4: add             x5, NULL, #0x30  ; false
    // 0xb8c6d8: StoreField: r0->field_73 = r5
    //     0xb8c6d8: stur            w5, [x0, #0x73]
    // 0xb8c6dc: StoreField: r0->field_83 = r3
    //     0xb8c6dc: stur            w3, [x0, #0x83]
    // 0xb8c6e0: StoreField: r0->field_7b = r5
    //     0xb8c6e0: stur            w5, [x0, #0x7b]
    // 0xb8c6e4: r1 = Null
    //     0xb8c6e4: mov             x1, NULL
    // 0xb8c6e8: r2 = 4
    //     0xb8c6e8: movz            x2, #0x4
    // 0xb8c6ec: r0 = AllocateArray()
    //     0xb8c6ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8c6f0: mov             x2, x0
    // 0xb8c6f4: ldur            x0, [fp, #-0x18]
    // 0xb8c6f8: stur            x2, [fp, #-0x38]
    // 0xb8c6fc: StoreField: r2->field_f = r0
    //     0xb8c6fc: stur            w0, [x2, #0xf]
    // 0xb8c700: ldur            x0, [fp, #-0x30]
    // 0xb8c704: StoreField: r2->field_13 = r0
    //     0xb8c704: stur            w0, [x2, #0x13]
    // 0xb8c708: r1 = <Widget>
    //     0xb8c708: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8c70c: r0 = AllocateGrowableArray()
    //     0xb8c70c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8c710: mov             x1, x0
    // 0xb8c714: ldur            x0, [fp, #-0x38]
    // 0xb8c718: stur            x1, [fp, #-0x18]
    // 0xb8c71c: StoreField: r1->field_f = r0
    //     0xb8c71c: stur            w0, [x1, #0xf]
    // 0xb8c720: r2 = 4
    //     0xb8c720: movz            x2, #0x4
    // 0xb8c724: StoreField: r1->field_b = r2
    //     0xb8c724: stur            w2, [x1, #0xb]
    // 0xb8c728: r0 = Row()
    //     0xb8c728: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8c72c: mov             x1, x0
    // 0xb8c730: r0 = Instance_Axis
    //     0xb8c730: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8c734: stur            x1, [fp, #-0x30]
    // 0xb8c738: StoreField: r1->field_f = r0
    //     0xb8c738: stur            w0, [x1, #0xf]
    // 0xb8c73c: r0 = Instance_MainAxisAlignment
    //     0xb8c73c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb8c740: ldr             x0, [x0, #0xa8]
    // 0xb8c744: StoreField: r1->field_13 = r0
    //     0xb8c744: stur            w0, [x1, #0x13]
    // 0xb8c748: r0 = Instance_MainAxisSize
    //     0xb8c748: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8c74c: ldr             x0, [x0, #0xa10]
    // 0xb8c750: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8c750: stur            w0, [x1, #0x17]
    // 0xb8c754: r2 = Instance_CrossAxisAlignment
    //     0xb8c754: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8c758: ldr             x2, [x2, #0xa18]
    // 0xb8c75c: StoreField: r1->field_1b = r2
    //     0xb8c75c: stur            w2, [x1, #0x1b]
    // 0xb8c760: r3 = Instance_VerticalDirection
    //     0xb8c760: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8c764: ldr             x3, [x3, #0xa20]
    // 0xb8c768: StoreField: r1->field_23 = r3
    //     0xb8c768: stur            w3, [x1, #0x23]
    // 0xb8c76c: r4 = Instance_Clip
    //     0xb8c76c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8c770: ldr             x4, [x4, #0x38]
    // 0xb8c774: StoreField: r1->field_2b = r4
    //     0xb8c774: stur            w4, [x1, #0x2b]
    // 0xb8c778: StoreField: r1->field_2f = rZR
    //     0xb8c778: stur            xzr, [x1, #0x2f]
    // 0xb8c77c: ldur            x5, [fp, #-0x18]
    // 0xb8c780: StoreField: r1->field_b = r5
    //     0xb8c780: stur            w5, [x1, #0xb]
    // 0xb8c784: r0 = Padding()
    //     0xb8c784: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8c788: mov             x1, x0
    // 0xb8c78c: r0 = Instance_EdgeInsets
    //     0xb8c78c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb8c790: ldr             x0, [x0, #0x1f0]
    // 0xb8c794: stur            x1, [fp, #-0x18]
    // 0xb8c798: StoreField: r1->field_f = r0
    //     0xb8c798: stur            w0, [x1, #0xf]
    // 0xb8c79c: ldur            x0, [fp, #-0x30]
    // 0xb8c7a0: StoreField: r1->field_b = r0
    //     0xb8c7a0: stur            w0, [x1, #0xb]
    // 0xb8c7a4: r0 = Container()
    //     0xb8c7a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8c7a8: stur            x0, [fp, #-0x30]
    // 0xb8c7ac: ldur            x16, [fp, #-0x28]
    // 0xb8c7b0: ldur            lr, [fp, #-0x18]
    // 0xb8c7b4: stp             lr, x16, [SP]
    // 0xb8c7b8: mov             x1, x0
    // 0xb8c7bc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb8c7bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb8c7c0: ldr             x4, [x4, #0x88]
    // 0xb8c7c4: r0 = Container()
    //     0xb8c7c4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8c7c8: r1 = Null
    //     0xb8c7c8: mov             x1, NULL
    // 0xb8c7cc: r2 = 6
    //     0xb8c7cc: movz            x2, #0x6
    // 0xb8c7d0: r0 = AllocateArray()
    //     0xb8c7d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8c7d4: mov             x2, x0
    // 0xb8c7d8: ldur            x0, [fp, #-0x10]
    // 0xb8c7dc: stur            x2, [fp, #-0x18]
    // 0xb8c7e0: StoreField: r2->field_f = r0
    //     0xb8c7e0: stur            w0, [x2, #0xf]
    // 0xb8c7e4: r16 = Instance_SizedBox
    //     0xb8c7e4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb8c7e8: ldr             x16, [x16, #0x578]
    // 0xb8c7ec: StoreField: r2->field_13 = r16
    //     0xb8c7ec: stur            w16, [x2, #0x13]
    // 0xb8c7f0: ldur            x0, [fp, #-0x30]
    // 0xb8c7f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8c7f4: stur            w0, [x2, #0x17]
    // 0xb8c7f8: r1 = <Widget>
    //     0xb8c7f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8c7fc: r0 = AllocateGrowableArray()
    //     0xb8c7fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8c800: mov             x1, x0
    // 0xb8c804: ldur            x0, [fp, #-0x18]
    // 0xb8c808: stur            x1, [fp, #-0x10]
    // 0xb8c80c: StoreField: r1->field_f = r0
    //     0xb8c80c: stur            w0, [x1, #0xf]
    // 0xb8c810: r2 = 6
    //     0xb8c810: movz            x2, #0x6
    // 0xb8c814: StoreField: r1->field_b = r2
    //     0xb8c814: stur            w2, [x1, #0xb]
    // 0xb8c818: r0 = Column()
    //     0xb8c818: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8c81c: mov             x2, x0
    // 0xb8c820: r0 = Instance_Axis
    //     0xb8c820: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8c824: stur            x2, [fp, #-0x18]
    // 0xb8c828: StoreField: r2->field_f = r0
    //     0xb8c828: stur            w0, [x2, #0xf]
    // 0xb8c82c: r3 = Instance_MainAxisAlignment
    //     0xb8c82c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8c830: ldr             x3, [x3, #0xa08]
    // 0xb8c834: StoreField: r2->field_13 = r3
    //     0xb8c834: stur            w3, [x2, #0x13]
    // 0xb8c838: r4 = Instance_MainAxisSize
    //     0xb8c838: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8c83c: ldr             x4, [x4, #0xa10]
    // 0xb8c840: ArrayStore: r2[0] = r4  ; List_4
    //     0xb8c840: stur            w4, [x2, #0x17]
    // 0xb8c844: r1 = Instance_CrossAxisAlignment
    //     0xb8c844: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb8c848: ldr             x1, [x1, #0x890]
    // 0xb8c84c: StoreField: r2->field_1b = r1
    //     0xb8c84c: stur            w1, [x2, #0x1b]
    // 0xb8c850: r5 = Instance_VerticalDirection
    //     0xb8c850: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8c854: ldr             x5, [x5, #0xa20]
    // 0xb8c858: StoreField: r2->field_23 = r5
    //     0xb8c858: stur            w5, [x2, #0x23]
    // 0xb8c85c: r6 = Instance_Clip
    //     0xb8c85c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8c860: ldr             x6, [x6, #0x38]
    // 0xb8c864: StoreField: r2->field_2b = r6
    //     0xb8c864: stur            w6, [x2, #0x2b]
    // 0xb8c868: StoreField: r2->field_2f = rZR
    //     0xb8c868: stur            xzr, [x2, #0x2f]
    // 0xb8c86c: ldur            x1, [fp, #-0x10]
    // 0xb8c870: StoreField: r2->field_b = r1
    //     0xb8c870: stur            w1, [x2, #0xb]
    // 0xb8c874: ldur            x7, [fp, #-8]
    // 0xb8c878: LoadField: r1 = r7->field_b
    //     0xb8c878: ldur            w1, [x7, #0xb]
    // 0xb8c87c: DecompressPointer r1
    //     0xb8c87c: add             x1, x1, HEAP, lsl #32
    // 0xb8c880: cmp             w1, NULL
    // 0xb8c884: b.eq            #0xb8d27c
    // 0xb8c888: LoadField: r8 = r1->field_f
    //     0xb8c888: ldur            w8, [x1, #0xf]
    // 0xb8c88c: DecompressPointer r8
    //     0xb8c88c: add             x8, x8, HEAP, lsl #32
    // 0xb8c890: LoadField: r1 = r8->field_b
    //     0xb8c890: ldur            w1, [x8, #0xb]
    // 0xb8c894: DecompressPointer r1
    //     0xb8c894: add             x1, x1, HEAP, lsl #32
    // 0xb8c898: cmp             w1, NULL
    // 0xb8c89c: b.ne            #0xb8c8a8
    // 0xb8c8a0: r1 = Null
    //     0xb8c8a0: mov             x1, NULL
    // 0xb8c8a4: b               #0xb8c8ec
    // 0xb8c8a8: ArrayLoad: r8 = r1[0]  ; List_4
    //     0xb8c8a8: ldur            w8, [x1, #0x17]
    // 0xb8c8ac: DecompressPointer r8
    //     0xb8c8ac: add             x8, x8, HEAP, lsl #32
    // 0xb8c8b0: cmp             w8, NULL
    // 0xb8c8b4: b.ne            #0xb8c8c0
    // 0xb8c8b8: r1 = Null
    //     0xb8c8b8: mov             x1, NULL
    // 0xb8c8bc: b               #0xb8c8ec
    // 0xb8c8c0: LoadField: r1 = r8->field_f
    //     0xb8c8c0: ldur            w1, [x8, #0xf]
    // 0xb8c8c4: DecompressPointer r1
    //     0xb8c8c4: add             x1, x1, HEAP, lsl #32
    // 0xb8c8c8: cmp             w1, NULL
    // 0xb8c8cc: b.ne            #0xb8c8d8
    // 0xb8c8d0: r1 = Null
    //     0xb8c8d0: mov             x1, NULL
    // 0xb8c8d4: b               #0xb8c8ec
    // 0xb8c8d8: LoadField: r8 = r1->field_b
    //     0xb8c8d8: ldur            w8, [x1, #0xb]
    // 0xb8c8dc: cbnz            w8, #0xb8c8e8
    // 0xb8c8e0: r1 = false
    //     0xb8c8e0: add             x1, NULL, #0x30  ; false
    // 0xb8c8e4: b               #0xb8c8ec
    // 0xb8c8e8: r1 = true
    //     0xb8c8e8: add             x1, NULL, #0x20  ; true
    // 0xb8c8ec: cmp             w1, NULL
    // 0xb8c8f0: b.ne            #0xb8c8fc
    // 0xb8c8f4: r9 = false
    //     0xb8c8f4: add             x9, NULL, #0x30  ; false
    // 0xb8c8f8: b               #0xb8c900
    // 0xb8c8fc: mov             x9, x1
    // 0xb8c900: ldur            x8, [fp, #-0x20]
    // 0xb8c904: stur            x9, [fp, #-0x10]
    // 0xb8c908: LoadField: r1 = r8->field_13
    //     0xb8c908: ldur            w1, [x8, #0x13]
    // 0xb8c90c: DecompressPointer r1
    //     0xb8c90c: add             x1, x1, HEAP, lsl #32
    // 0xb8c910: r0 = of()
    //     0xb8c910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8c914: LoadField: r1 = r0->field_87
    //     0xb8c914: ldur            w1, [x0, #0x87]
    // 0xb8c918: DecompressPointer r1
    //     0xb8c918: add             x1, x1, HEAP, lsl #32
    // 0xb8c91c: LoadField: r0 = r1->field_2f
    //     0xb8c91c: ldur            w0, [x1, #0x2f]
    // 0xb8c920: DecompressPointer r0
    //     0xb8c920: add             x0, x0, HEAP, lsl #32
    // 0xb8c924: cmp             w0, NULL
    // 0xb8c928: b.ne            #0xb8c934
    // 0xb8c92c: r1 = Null
    //     0xb8c92c: mov             x1, NULL
    // 0xb8c930: b               #0xb8c958
    // 0xb8c934: r16 = Instance_Color
    //     0xb8c934: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8c938: r30 = 14.000000
    //     0xb8c938: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb8c93c: ldr             lr, [lr, #0x1d8]
    // 0xb8c940: stp             lr, x16, [SP]
    // 0xb8c944: mov             x1, x0
    // 0xb8c948: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb8c948: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb8c94c: ldr             x4, [x4, #0x9b8]
    // 0xb8c950: r0 = copyWith()
    //     0xb8c950: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8c954: mov             x1, x0
    // 0xb8c958: ldur            x0, [fp, #-8]
    // 0xb8c95c: stur            x1, [fp, #-0x28]
    // 0xb8c960: r0 = Text()
    //     0xb8c960: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8c964: mov             x3, x0
    // 0xb8c968: r0 = "Real images from customers"
    //     0xb8c968: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0xb8c96c: ldr             x0, [x0, #0x88]
    // 0xb8c970: stur            x3, [fp, #-0x30]
    // 0xb8c974: StoreField: r3->field_b = r0
    //     0xb8c974: stur            w0, [x3, #0xb]
    // 0xb8c978: ldur            x0, [fp, #-0x28]
    // 0xb8c97c: StoreField: r3->field_13 = r0
    //     0xb8c97c: stur            w0, [x3, #0x13]
    // 0xb8c980: ldur            x0, [fp, #-8]
    // 0xb8c984: LoadField: r1 = r0->field_b
    //     0xb8c984: ldur            w1, [x0, #0xb]
    // 0xb8c988: DecompressPointer r1
    //     0xb8c988: add             x1, x1, HEAP, lsl #32
    // 0xb8c98c: cmp             w1, NULL
    // 0xb8c990: b.eq            #0xb8d280
    // 0xb8c994: LoadField: r2 = r1->field_f
    //     0xb8c994: ldur            w2, [x1, #0xf]
    // 0xb8c998: DecompressPointer r2
    //     0xb8c998: add             x2, x2, HEAP, lsl #32
    // 0xb8c99c: LoadField: r1 = r2->field_b
    //     0xb8c99c: ldur            w1, [x2, #0xb]
    // 0xb8c9a0: DecompressPointer r1
    //     0xb8c9a0: add             x1, x1, HEAP, lsl #32
    // 0xb8c9a4: cmp             w1, NULL
    // 0xb8c9a8: b.ne            #0xb8c9b4
    // 0xb8c9ac: r0 = Null
    //     0xb8c9ac: mov             x0, NULL
    // 0xb8c9b0: b               #0xb8ca14
    // 0xb8c9b4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb8c9b4: ldur            w2, [x1, #0x17]
    // 0xb8c9b8: DecompressPointer r2
    //     0xb8c9b8: add             x2, x2, HEAP, lsl #32
    // 0xb8c9bc: cmp             w2, NULL
    // 0xb8c9c0: b.ne            #0xb8c9cc
    // 0xb8c9c4: r0 = Null
    //     0xb8c9c4: mov             x0, NULL
    // 0xb8c9c8: b               #0xb8ca14
    // 0xb8c9cc: LoadField: r4 = r2->field_f
    //     0xb8c9cc: ldur            w4, [x2, #0xf]
    // 0xb8c9d0: DecompressPointer r4
    //     0xb8c9d0: add             x4, x4, HEAP, lsl #32
    // 0xb8c9d4: stur            x4, [fp, #-0x28]
    // 0xb8c9d8: cmp             w4, NULL
    // 0xb8c9dc: b.ne            #0xb8c9e8
    // 0xb8c9e0: r0 = Null
    //     0xb8c9e0: mov             x0, NULL
    // 0xb8c9e4: b               #0xb8ca14
    // 0xb8c9e8: r1 = Function '<anonymous closure>':.
    //     0xb8c9e8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55310] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb8c9ec: ldr             x1, [x1, #0x310]
    // 0xb8c9f0: r2 = Null
    //     0xb8c9f0: mov             x2, NULL
    // 0xb8c9f4: r0 = AllocateClosure()
    //     0xb8c9f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8c9f8: ldur            x16, [fp, #-0x28]
    // 0xb8c9fc: stp             x16, NULL, [SP, #8]
    // 0xb8ca00: str             x0, [SP]
    // 0xb8ca04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8ca04: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8ca08: r0 = expand()
    //     0xb8ca08: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb8ca0c: str             x0, [SP]
    // 0xb8ca10: r0 = length()
    //     0xb8ca10: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xb8ca14: cmp             w0, NULL
    // 0xb8ca18: b.ne            #0xb8ca24
    // 0xb8ca1c: r0 = 0
    //     0xb8ca1c: movz            x0, #0
    // 0xb8ca20: b               #0xb8ca34
    // 0xb8ca24: r1 = LoadInt32Instr(r0)
    //     0xb8ca24: sbfx            x1, x0, #1, #0x1f
    //     0xb8ca28: tbz             w0, #0, #0xb8ca30
    //     0xb8ca2c: ldur            x1, [x0, #7]
    // 0xb8ca30: mov             x0, x1
    // 0xb8ca34: cmp             x0, #5
    // 0xb8ca38: b.le            #0xb8ca44
    // 0xb8ca3c: r5 = 5
    //     0xb8ca3c: movz            x5, #0x5
    // 0xb8ca40: b               #0xb8cafc
    // 0xb8ca44: ldur            x0, [fp, #-8]
    // 0xb8ca48: LoadField: r1 = r0->field_b
    //     0xb8ca48: ldur            w1, [x0, #0xb]
    // 0xb8ca4c: DecompressPointer r1
    //     0xb8ca4c: add             x1, x1, HEAP, lsl #32
    // 0xb8ca50: cmp             w1, NULL
    // 0xb8ca54: b.eq            #0xb8d284
    // 0xb8ca58: LoadField: r2 = r1->field_f
    //     0xb8ca58: ldur            w2, [x1, #0xf]
    // 0xb8ca5c: DecompressPointer r2
    //     0xb8ca5c: add             x2, x2, HEAP, lsl #32
    // 0xb8ca60: LoadField: r1 = r2->field_b
    //     0xb8ca60: ldur            w1, [x2, #0xb]
    // 0xb8ca64: DecompressPointer r1
    //     0xb8ca64: add             x1, x1, HEAP, lsl #32
    // 0xb8ca68: cmp             w1, NULL
    // 0xb8ca6c: b.ne            #0xb8ca78
    // 0xb8ca70: r0 = Null
    //     0xb8ca70: mov             x0, NULL
    // 0xb8ca74: b               #0xb8cad8
    // 0xb8ca78: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb8ca78: ldur            w2, [x1, #0x17]
    // 0xb8ca7c: DecompressPointer r2
    //     0xb8ca7c: add             x2, x2, HEAP, lsl #32
    // 0xb8ca80: cmp             w2, NULL
    // 0xb8ca84: b.ne            #0xb8ca90
    // 0xb8ca88: r0 = Null
    //     0xb8ca88: mov             x0, NULL
    // 0xb8ca8c: b               #0xb8cad8
    // 0xb8ca90: LoadField: r3 = r2->field_f
    //     0xb8ca90: ldur            w3, [x2, #0xf]
    // 0xb8ca94: DecompressPointer r3
    //     0xb8ca94: add             x3, x3, HEAP, lsl #32
    // 0xb8ca98: stur            x3, [fp, #-0x28]
    // 0xb8ca9c: cmp             w3, NULL
    // 0xb8caa0: b.ne            #0xb8caac
    // 0xb8caa4: r0 = Null
    //     0xb8caa4: mov             x0, NULL
    // 0xb8caa8: b               #0xb8cad8
    // 0xb8caac: r1 = Function '<anonymous closure>':.
    //     0xb8caac: add             x1, PP, #0x55, lsl #12  ; [pp+0x55318] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb8cab0: ldr             x1, [x1, #0x318]
    // 0xb8cab4: r2 = Null
    //     0xb8cab4: mov             x2, NULL
    // 0xb8cab8: r0 = AllocateClosure()
    //     0xb8cab8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cabc: ldur            x16, [fp, #-0x28]
    // 0xb8cac0: stp             x16, NULL, [SP, #8]
    // 0xb8cac4: str             x0, [SP]
    // 0xb8cac8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8cac8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8cacc: r0 = expand()
    //     0xb8cacc: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb8cad0: str             x0, [SP]
    // 0xb8cad4: r0 = length()
    //     0xb8cad4: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xb8cad8: cmp             w0, NULL
    // 0xb8cadc: b.ne            #0xb8cae8
    // 0xb8cae0: r0 = 0
    //     0xb8cae0: movz            x0, #0
    // 0xb8cae4: b               #0xb8caf8
    // 0xb8cae8: r1 = LoadInt32Instr(r0)
    //     0xb8cae8: sbfx            x1, x0, #1, #0x1f
    //     0xb8caec: tbz             w0, #0, #0xb8caf4
    //     0xb8caf0: ldur            x1, [x0, #7]
    // 0xb8caf4: mov             x0, x1
    // 0xb8caf8: mov             x5, x0
    // 0xb8cafc: ldur            x0, [fp, #-8]
    // 0xb8cb00: ldur            x4, [fp, #-0x10]
    // 0xb8cb04: ldur            x3, [fp, #-0x30]
    // 0xb8cb08: ldur            x2, [fp, #-0x20]
    // 0xb8cb0c: stur            x5, [fp, #-0x40]
    // 0xb8cb10: r1 = Function '<anonymous closure>':.
    //     0xb8cb10: add             x1, PP, #0x55, lsl #12  ; [pp+0x55320] AnonymousClosure: (0xb8d3f8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8cb14: ldr             x1, [x1, #0x320]
    // 0xb8cb18: r0 = AllocateClosure()
    //     0xb8cb18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cb1c: r1 = Function '<anonymous closure>':.
    //     0xb8cb1c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55328] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb8cb20: ldr             x1, [x1, #0x328]
    // 0xb8cb24: r2 = Null
    //     0xb8cb24: mov             x2, NULL
    // 0xb8cb28: stur            x0, [fp, #-0x28]
    // 0xb8cb2c: r0 = AllocateClosure()
    //     0xb8cb2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cb30: stur            x0, [fp, #-0x38]
    // 0xb8cb34: r0 = ListView()
    //     0xb8cb34: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb8cb38: stur            x0, [fp, #-0x48]
    // 0xb8cb3c: r16 = true
    //     0xb8cb3c: add             x16, NULL, #0x20  ; true
    // 0xb8cb40: r30 = Instance_Axis
    //     0xb8cb40: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8cb44: stp             lr, x16, [SP]
    // 0xb8cb48: mov             x1, x0
    // 0xb8cb4c: ldur            x2, [fp, #-0x28]
    // 0xb8cb50: ldur            x3, [fp, #-0x40]
    // 0xb8cb54: ldur            x5, [fp, #-0x38]
    // 0xb8cb58: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xb8cb58: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb8cb5c: ldr             x4, [x4, #0x8e8]
    // 0xb8cb60: r0 = ListView.separated()
    //     0xb8cb60: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb8cb64: r0 = SizedBox()
    //     0xb8cb64: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb8cb68: mov             x3, x0
    // 0xb8cb6c: r0 = 60.000000
    //     0xb8cb6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8cb70: ldr             x0, [x0, #0x110]
    // 0xb8cb74: stur            x3, [fp, #-0x28]
    // 0xb8cb78: StoreField: r3->field_13 = r0
    //     0xb8cb78: stur            w0, [x3, #0x13]
    // 0xb8cb7c: ldur            x0, [fp, #-0x48]
    // 0xb8cb80: StoreField: r3->field_b = r0
    //     0xb8cb80: stur            w0, [x3, #0xb]
    // 0xb8cb84: r1 = Null
    //     0xb8cb84: mov             x1, NULL
    // 0xb8cb88: r2 = 8
    //     0xb8cb88: movz            x2, #0x8
    // 0xb8cb8c: r0 = AllocateArray()
    //     0xb8cb8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8cb90: mov             x2, x0
    // 0xb8cb94: ldur            x0, [fp, #-0x30]
    // 0xb8cb98: stur            x2, [fp, #-0x38]
    // 0xb8cb9c: StoreField: r2->field_f = r0
    //     0xb8cb9c: stur            w0, [x2, #0xf]
    // 0xb8cba0: r16 = Instance_SizedBox
    //     0xb8cba0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb8cba4: ldr             x16, [x16, #0x8f0]
    // 0xb8cba8: StoreField: r2->field_13 = r16
    //     0xb8cba8: stur            w16, [x2, #0x13]
    // 0xb8cbac: ldur            x0, [fp, #-0x28]
    // 0xb8cbb0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8cbb0: stur            w0, [x2, #0x17]
    // 0xb8cbb4: r16 = Instance_SizedBox
    //     0xb8cbb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb8cbb8: ldr             x16, [x16, #0x8f0]
    // 0xb8cbbc: StoreField: r2->field_1b = r16
    //     0xb8cbbc: stur            w16, [x2, #0x1b]
    // 0xb8cbc0: r1 = <Widget>
    //     0xb8cbc0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8cbc4: r0 = AllocateGrowableArray()
    //     0xb8cbc4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8cbc8: mov             x1, x0
    // 0xb8cbcc: ldur            x0, [fp, #-0x38]
    // 0xb8cbd0: stur            x1, [fp, #-0x28]
    // 0xb8cbd4: StoreField: r1->field_f = r0
    //     0xb8cbd4: stur            w0, [x1, #0xf]
    // 0xb8cbd8: r2 = 8
    //     0xb8cbd8: movz            x2, #0x8
    // 0xb8cbdc: StoreField: r1->field_b = r2
    //     0xb8cbdc: stur            w2, [x1, #0xb]
    // 0xb8cbe0: r0 = Column()
    //     0xb8cbe0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8cbe4: mov             x1, x0
    // 0xb8cbe8: r0 = Instance_Axis
    //     0xb8cbe8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8cbec: stur            x1, [fp, #-0x30]
    // 0xb8cbf0: StoreField: r1->field_f = r0
    //     0xb8cbf0: stur            w0, [x1, #0xf]
    // 0xb8cbf4: r2 = Instance_MainAxisAlignment
    //     0xb8cbf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8cbf8: ldr             x2, [x2, #0xa08]
    // 0xb8cbfc: StoreField: r1->field_13 = r2
    //     0xb8cbfc: stur            w2, [x1, #0x13]
    // 0xb8cc00: r3 = Instance_MainAxisSize
    //     0xb8cc00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8cc04: ldr             x3, [x3, #0xa10]
    // 0xb8cc08: ArrayStore: r1[0] = r3  ; List_4
    //     0xb8cc08: stur            w3, [x1, #0x17]
    // 0xb8cc0c: r4 = Instance_CrossAxisAlignment
    //     0xb8cc0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8cc10: ldr             x4, [x4, #0xa18]
    // 0xb8cc14: StoreField: r1->field_1b = r4
    //     0xb8cc14: stur            w4, [x1, #0x1b]
    // 0xb8cc18: r5 = Instance_VerticalDirection
    //     0xb8cc18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8cc1c: ldr             x5, [x5, #0xa20]
    // 0xb8cc20: StoreField: r1->field_23 = r5
    //     0xb8cc20: stur            w5, [x1, #0x23]
    // 0xb8cc24: r6 = Instance_Clip
    //     0xb8cc24: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8cc28: ldr             x6, [x6, #0x38]
    // 0xb8cc2c: StoreField: r1->field_2b = r6
    //     0xb8cc2c: stur            w6, [x1, #0x2b]
    // 0xb8cc30: StoreField: r1->field_2f = rZR
    //     0xb8cc30: stur            xzr, [x1, #0x2f]
    // 0xb8cc34: ldur            x7, [fp, #-0x28]
    // 0xb8cc38: StoreField: r1->field_b = r7
    //     0xb8cc38: stur            w7, [x1, #0xb]
    // 0xb8cc3c: r0 = Padding()
    //     0xb8cc3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8cc40: mov             x1, x0
    // 0xb8cc44: r0 = Instance_EdgeInsets
    //     0xb8cc44: add             x0, PP, #0x52, lsl #12  ; [pp+0x52468] Obj!EdgeInsets@d58a01
    //     0xb8cc48: ldr             x0, [x0, #0x468]
    // 0xb8cc4c: stur            x1, [fp, #-0x28]
    // 0xb8cc50: StoreField: r1->field_f = r0
    //     0xb8cc50: stur            w0, [x1, #0xf]
    // 0xb8cc54: ldur            x0, [fp, #-0x30]
    // 0xb8cc58: StoreField: r1->field_b = r0
    //     0xb8cc58: stur            w0, [x1, #0xb]
    // 0xb8cc5c: r0 = Visibility()
    //     0xb8cc5c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8cc60: mov             x3, x0
    // 0xb8cc64: ldur            x0, [fp, #-0x28]
    // 0xb8cc68: stur            x3, [fp, #-0x48]
    // 0xb8cc6c: StoreField: r3->field_b = r0
    //     0xb8cc6c: stur            w0, [x3, #0xb]
    // 0xb8cc70: r0 = Instance_SizedBox
    //     0xb8cc70: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8cc74: StoreField: r3->field_f = r0
    //     0xb8cc74: stur            w0, [x3, #0xf]
    // 0xb8cc78: ldur            x1, [fp, #-0x10]
    // 0xb8cc7c: StoreField: r3->field_13 = r1
    //     0xb8cc7c: stur            w1, [x3, #0x13]
    // 0xb8cc80: r4 = false
    //     0xb8cc80: add             x4, NULL, #0x30  ; false
    // 0xb8cc84: ArrayStore: r3[0] = r4  ; List_4
    //     0xb8cc84: stur            w4, [x3, #0x17]
    // 0xb8cc88: StoreField: r3->field_1b = r4
    //     0xb8cc88: stur            w4, [x3, #0x1b]
    // 0xb8cc8c: StoreField: r3->field_1f = r4
    //     0xb8cc8c: stur            w4, [x3, #0x1f]
    // 0xb8cc90: StoreField: r3->field_23 = r4
    //     0xb8cc90: stur            w4, [x3, #0x23]
    // 0xb8cc94: StoreField: r3->field_27 = r4
    //     0xb8cc94: stur            w4, [x3, #0x27]
    // 0xb8cc98: StoreField: r3->field_2b = r4
    //     0xb8cc98: stur            w4, [x3, #0x2b]
    // 0xb8cc9c: ldur            x5, [fp, #-8]
    // 0xb8cca0: LoadField: r1 = r5->field_b
    //     0xb8cca0: ldur            w1, [x5, #0xb]
    // 0xb8cca4: DecompressPointer r1
    //     0xb8cca4: add             x1, x1, HEAP, lsl #32
    // 0xb8cca8: cmp             w1, NULL
    // 0xb8ccac: b.eq            #0xb8d288
    // 0xb8ccb0: LoadField: r6 = r1->field_f
    //     0xb8ccb0: ldur            w6, [x1, #0xf]
    // 0xb8ccb4: DecompressPointer r6
    //     0xb8ccb4: add             x6, x6, HEAP, lsl #32
    // 0xb8ccb8: stur            x6, [fp, #-0x38]
    // 0xb8ccbc: LoadField: r7 = r6->field_b
    //     0xb8ccbc: ldur            w7, [x6, #0xb]
    // 0xb8ccc0: DecompressPointer r7
    //     0xb8ccc0: add             x7, x7, HEAP, lsl #32
    // 0xb8ccc4: stur            x7, [fp, #-0x30]
    // 0xb8ccc8: cmp             w7, NULL
    // 0xb8cccc: b.ne            #0xb8ccd8
    // 0xb8ccd0: r2 = Null
    //     0xb8ccd0: mov             x2, NULL
    // 0xb8ccd4: b               #0xb8ccf4
    // 0xb8ccd8: LoadField: r2 = r7->field_13
    //     0xb8ccd8: ldur            w2, [x7, #0x13]
    // 0xb8ccdc: DecompressPointer r2
    //     0xb8ccdc: add             x2, x2, HEAP, lsl #32
    // 0xb8cce0: LoadField: r8 = r2->field_b
    //     0xb8cce0: ldur            w8, [x2, #0xb]
    // 0xb8cce4: cbnz            w8, #0xb8ccf0
    // 0xb8cce8: r2 = false
    //     0xb8cce8: add             x2, NULL, #0x30  ; false
    // 0xb8ccec: b               #0xb8ccf4
    // 0xb8ccf0: r2 = true
    //     0xb8ccf0: add             x2, NULL, #0x20  ; true
    // 0xb8ccf4: cmp             w2, NULL
    // 0xb8ccf8: b.ne            #0xb8cd04
    // 0xb8ccfc: r8 = false
    //     0xb8ccfc: add             x8, NULL, #0x30  ; false
    // 0xb8cd00: b               #0xb8cd08
    // 0xb8cd04: mov             x8, x2
    // 0xb8cd08: stur            x8, [fp, #-0x28]
    // 0xb8cd0c: LoadField: r9 = r1->field_1f
    //     0xb8cd0c: ldur            w9, [x1, #0x1f]
    // 0xb8cd10: DecompressPointer r9
    //     0xb8cd10: add             x9, x9, HEAP, lsl #32
    // 0xb8cd14: ldur            x2, [fp, #-0x20]
    // 0xb8cd18: stur            x9, [fp, #-0x10]
    // 0xb8cd1c: r1 = Function '<anonymous closure>':.
    //     0xb8cd1c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55330] AnonymousClosure: (0xa8c4e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xb8cd20: ldr             x1, [x1, #0x330]
    // 0xb8cd24: r0 = AllocateClosure()
    //     0xb8cd24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cd28: stur            x0, [fp, #-0x50]
    // 0xb8cd2c: r0 = ReviewWidget()
    //     0xb8cd2c: bl              #0xb8d2b0  ; AllocateReviewWidgetStub -> ReviewWidget (size=0x1c)
    // 0xb8cd30: mov             x3, x0
    // 0xb8cd34: ldur            x0, [fp, #-0x50]
    // 0xb8cd38: stur            x3, [fp, #-0x58]
    // 0xb8cd3c: StoreField: r3->field_f = r0
    //     0xb8cd3c: stur            w0, [x3, #0xf]
    // 0xb8cd40: ldur            x0, [fp, #-0x30]
    // 0xb8cd44: StoreField: r3->field_b = r0
    //     0xb8cd44: stur            w0, [x3, #0xb]
    // 0xb8cd48: ldur            x0, [fp, #-0x10]
    // 0xb8cd4c: StoreField: r3->field_13 = r0
    //     0xb8cd4c: stur            w0, [x3, #0x13]
    // 0xb8cd50: ldur            x2, [fp, #-0x20]
    // 0xb8cd54: r1 = Function '<anonymous closure>':.
    //     0xb8cd54: add             x1, PP, #0x55, lsl #12  ; [pp+0x55338] AnonymousClosure: (0xb8d36c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8cd58: ldr             x1, [x1, #0x338]
    // 0xb8cd5c: r0 = AllocateClosure()
    //     0xb8cd5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cd60: mov             x1, x0
    // 0xb8cd64: ldur            x0, [fp, #-0x58]
    // 0xb8cd68: ArrayStore: r0[0] = r1  ; List_4
    //     0xb8cd68: stur            w1, [x0, #0x17]
    // 0xb8cd6c: r1 = Null
    //     0xb8cd6c: mov             x1, NULL
    // 0xb8cd70: r2 = 4
    //     0xb8cd70: movz            x2, #0x4
    // 0xb8cd74: r0 = AllocateArray()
    //     0xb8cd74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8cd78: mov             x2, x0
    // 0xb8cd7c: ldur            x0, [fp, #-0x58]
    // 0xb8cd80: stur            x2, [fp, #-0x10]
    // 0xb8cd84: StoreField: r2->field_f = r0
    //     0xb8cd84: stur            w0, [x2, #0xf]
    // 0xb8cd88: r16 = Instance_SizedBox
    //     0xb8cd88: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb8cd8c: ldr             x16, [x16, #0x578]
    // 0xb8cd90: StoreField: r2->field_13 = r16
    //     0xb8cd90: stur            w16, [x2, #0x13]
    // 0xb8cd94: r1 = <Widget>
    //     0xb8cd94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8cd98: r0 = AllocateGrowableArray()
    //     0xb8cd98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8cd9c: mov             x2, x0
    // 0xb8cda0: ldur            x0, [fp, #-0x10]
    // 0xb8cda4: stur            x2, [fp, #-0x30]
    // 0xb8cda8: StoreField: r2->field_f = r0
    //     0xb8cda8: stur            w0, [x2, #0xf]
    // 0xb8cdac: r0 = 4
    //     0xb8cdac: movz            x0, #0x4
    // 0xb8cdb0: StoreField: r2->field_b = r0
    //     0xb8cdb0: stur            w0, [x2, #0xb]
    // 0xb8cdb4: ldur            x0, [fp, #-0x38]
    // 0xb8cdb8: LoadField: r1 = r0->field_f
    //     0xb8cdb8: ldur            w1, [x0, #0xf]
    // 0xb8cdbc: DecompressPointer r1
    //     0xb8cdbc: add             x1, x1, HEAP, lsl #32
    // 0xb8cdc0: cmp             w1, NULL
    // 0xb8cdc4: b.ne            #0xb8cdd0
    // 0xb8cdc8: r0 = Null
    //     0xb8cdc8: mov             x0, NULL
    // 0xb8cdcc: b               #0xb8cdd8
    // 0xb8cdd0: LoadField: r0 = r1->field_f
    //     0xb8cdd0: ldur            w0, [x1, #0xf]
    // 0xb8cdd4: DecompressPointer r0
    //     0xb8cdd4: add             x0, x0, HEAP, lsl #32
    // 0xb8cdd8: cmp             w0, NULL
    // 0xb8cddc: b.ne            #0xb8cde8
    // 0xb8cde0: r0 = 0
    //     0xb8cde0: movz            x0, #0
    // 0xb8cde4: b               #0xb8cdf8
    // 0xb8cde8: r1 = LoadInt32Instr(r0)
    //     0xb8cde8: sbfx            x1, x0, #1, #0x1f
    //     0xb8cdec: tbz             w0, #0, #0xb8cdf4
    //     0xb8cdf0: ldur            x1, [x0, #7]
    // 0xb8cdf4: mov             x0, x1
    // 0xb8cdf8: cmp             x0, #3
    // 0xb8cdfc: b.lt            #0xb8d0bc
    // 0xb8ce00: ldur            x0, [fp, #-8]
    // 0xb8ce04: ldur            x3, [fp, #-0x20]
    // 0xb8ce08: LoadField: r1 = r3->field_13
    //     0xb8ce08: ldur            w1, [x3, #0x13]
    // 0xb8ce0c: DecompressPointer r1
    //     0xb8ce0c: add             x1, x1, HEAP, lsl #32
    // 0xb8ce10: r0 = of()
    //     0xb8ce10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8ce14: LoadField: r1 = r0->field_5b
    //     0xb8ce14: ldur            w1, [x0, #0x5b]
    // 0xb8ce18: DecompressPointer r1
    //     0xb8ce18: add             x1, x1, HEAP, lsl #32
    // 0xb8ce1c: stur            x1, [fp, #-0x10]
    // 0xb8ce20: r0 = Radius()
    //     0xb8ce20: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8ce24: d0 = 30.000000
    //     0xb8ce24: fmov            d0, #30.00000000
    // 0xb8ce28: stur            x0, [fp, #-0x38]
    // 0xb8ce2c: StoreField: r0->field_7 = d0
    //     0xb8ce2c: stur            d0, [x0, #7]
    // 0xb8ce30: StoreField: r0->field_f = d0
    //     0xb8ce30: stur            d0, [x0, #0xf]
    // 0xb8ce34: r0 = BorderRadius()
    //     0xb8ce34: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8ce38: mov             x1, x0
    // 0xb8ce3c: ldur            x0, [fp, #-0x38]
    // 0xb8ce40: stur            x1, [fp, #-0x50]
    // 0xb8ce44: StoreField: r1->field_7 = r0
    //     0xb8ce44: stur            w0, [x1, #7]
    // 0xb8ce48: StoreField: r1->field_b = r0
    //     0xb8ce48: stur            w0, [x1, #0xb]
    // 0xb8ce4c: StoreField: r1->field_f = r0
    //     0xb8ce4c: stur            w0, [x1, #0xf]
    // 0xb8ce50: StoreField: r1->field_13 = r0
    //     0xb8ce50: stur            w0, [x1, #0x13]
    // 0xb8ce54: r0 = BoxDecoration()
    //     0xb8ce54: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8ce58: mov             x3, x0
    // 0xb8ce5c: ldur            x0, [fp, #-0x10]
    // 0xb8ce60: stur            x3, [fp, #-0x38]
    // 0xb8ce64: StoreField: r3->field_7 = r0
    //     0xb8ce64: stur            w0, [x3, #7]
    // 0xb8ce68: ldur            x0, [fp, #-0x50]
    // 0xb8ce6c: StoreField: r3->field_13 = r0
    //     0xb8ce6c: stur            w0, [x3, #0x13]
    // 0xb8ce70: r0 = Instance_BoxShape
    //     0xb8ce70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8ce74: ldr             x0, [x0, #0x80]
    // 0xb8ce78: StoreField: r3->field_23 = r0
    //     0xb8ce78: stur            w0, [x3, #0x23]
    // 0xb8ce7c: r1 = Null
    //     0xb8ce7c: mov             x1, NULL
    // 0xb8ce80: r2 = 6
    //     0xb8ce80: movz            x2, #0x6
    // 0xb8ce84: r0 = AllocateArray()
    //     0xb8ce84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8ce88: r16 = "View All Reviews ("
    //     0xb8ce88: add             x16, PP, #0x52, lsl #12  ; [pp+0x52480] "View All Reviews ("
    //     0xb8ce8c: ldr             x16, [x16, #0x480]
    // 0xb8ce90: StoreField: r0->field_f = r16
    //     0xb8ce90: stur            w16, [x0, #0xf]
    // 0xb8ce94: ldur            x1, [fp, #-8]
    // 0xb8ce98: LoadField: r2 = r1->field_b
    //     0xb8ce98: ldur            w2, [x1, #0xb]
    // 0xb8ce9c: DecompressPointer r2
    //     0xb8ce9c: add             x2, x2, HEAP, lsl #32
    // 0xb8cea0: cmp             w2, NULL
    // 0xb8cea4: b.eq            #0xb8d28c
    // 0xb8cea8: LoadField: r1 = r2->field_f
    //     0xb8cea8: ldur            w1, [x2, #0xf]
    // 0xb8ceac: DecompressPointer r1
    //     0xb8ceac: add             x1, x1, HEAP, lsl #32
    // 0xb8ceb0: LoadField: r2 = r1->field_f
    //     0xb8ceb0: ldur            w2, [x1, #0xf]
    // 0xb8ceb4: DecompressPointer r2
    //     0xb8ceb4: add             x2, x2, HEAP, lsl #32
    // 0xb8ceb8: cmp             w2, NULL
    // 0xb8cebc: b.ne            #0xb8cec8
    // 0xb8cec0: r1 = Null
    //     0xb8cec0: mov             x1, NULL
    // 0xb8cec4: b               #0xb8ced0
    // 0xb8cec8: LoadField: r1 = r2->field_f
    //     0xb8cec8: ldur            w1, [x2, #0xf]
    // 0xb8cecc: DecompressPointer r1
    //     0xb8cecc: add             x1, x1, HEAP, lsl #32
    // 0xb8ced0: cmp             w1, NULL
    // 0xb8ced4: b.ne            #0xb8cedc
    // 0xb8ced8: r1 = ""
    //     0xb8ced8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8cedc: ldur            x2, [fp, #-0x20]
    // 0xb8cee0: StoreField: r0->field_13 = r1
    //     0xb8cee0: stur            w1, [x0, #0x13]
    // 0xb8cee4: r16 = ")"
    //     0xb8cee4: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb8cee8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8cee8: stur            w16, [x0, #0x17]
    // 0xb8ceec: str             x0, [SP]
    // 0xb8cef0: r0 = _interpolate()
    //     0xb8cef0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb8cef4: ldur            x2, [fp, #-0x20]
    // 0xb8cef8: stur            x0, [fp, #-8]
    // 0xb8cefc: LoadField: r1 = r2->field_13
    //     0xb8cefc: ldur            w1, [x2, #0x13]
    // 0xb8cf00: DecompressPointer r1
    //     0xb8cf00: add             x1, x1, HEAP, lsl #32
    // 0xb8cf04: r0 = of()
    //     0xb8cf04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8cf08: LoadField: r1 = r0->field_87
    //     0xb8cf08: ldur            w1, [x0, #0x87]
    // 0xb8cf0c: DecompressPointer r1
    //     0xb8cf0c: add             x1, x1, HEAP, lsl #32
    // 0xb8cf10: LoadField: r0 = r1->field_b
    //     0xb8cf10: ldur            w0, [x1, #0xb]
    // 0xb8cf14: DecompressPointer r0
    //     0xb8cf14: add             x0, x0, HEAP, lsl #32
    // 0xb8cf18: cmp             w0, NULL
    // 0xb8cf1c: b.ne            #0xb8cf28
    // 0xb8cf20: r2 = Null
    //     0xb8cf20: mov             x2, NULL
    // 0xb8cf24: b               #0xb8cf4c
    // 0xb8cf28: r16 = Instance_Color
    //     0xb8cf28: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb8cf2c: r30 = 14.000000
    //     0xb8cf2c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb8cf30: ldr             lr, [lr, #0x1d8]
    // 0xb8cf34: stp             lr, x16, [SP]
    // 0xb8cf38: mov             x1, x0
    // 0xb8cf3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb8cf3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb8cf40: ldr             x4, [x4, #0x9b8]
    // 0xb8cf44: r0 = copyWith()
    //     0xb8cf44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8cf48: mov             x2, x0
    // 0xb8cf4c: ldur            x0, [fp, #-8]
    // 0xb8cf50: ldur            x1, [fp, #-0x30]
    // 0xb8cf54: stur            x2, [fp, #-0x10]
    // 0xb8cf58: r0 = Text()
    //     0xb8cf58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8cf5c: mov             x1, x0
    // 0xb8cf60: ldur            x0, [fp, #-8]
    // 0xb8cf64: stur            x1, [fp, #-0x50]
    // 0xb8cf68: StoreField: r1->field_b = r0
    //     0xb8cf68: stur            w0, [x1, #0xb]
    // 0xb8cf6c: ldur            x0, [fp, #-0x10]
    // 0xb8cf70: StoreField: r1->field_13 = r0
    //     0xb8cf70: stur            w0, [x1, #0x13]
    // 0xb8cf74: r0 = Center()
    //     0xb8cf74: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8cf78: mov             x1, x0
    // 0xb8cf7c: r0 = Instance_Alignment
    //     0xb8cf7c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8cf80: ldr             x0, [x0, #0xb10]
    // 0xb8cf84: stur            x1, [fp, #-8]
    // 0xb8cf88: StoreField: r1->field_f = r0
    //     0xb8cf88: stur            w0, [x1, #0xf]
    // 0xb8cf8c: ldur            x0, [fp, #-0x50]
    // 0xb8cf90: StoreField: r1->field_b = r0
    //     0xb8cf90: stur            w0, [x1, #0xb]
    // 0xb8cf94: r0 = Container()
    //     0xb8cf94: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8cf98: stur            x0, [fp, #-0x10]
    // 0xb8cf9c: ldur            x16, [fp, #-0x38]
    // 0xb8cfa0: r30 = inf
    //     0xb8cfa0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb8cfa4: ldr             lr, [lr, #0x9f8]
    // 0xb8cfa8: stp             lr, x16, [SP, #0x10]
    // 0xb8cfac: r16 = 40.000000
    //     0xb8cfac: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb8cfb0: ldr             x16, [x16, #8]
    // 0xb8cfb4: ldur            lr, [fp, #-8]
    // 0xb8cfb8: stp             lr, x16, [SP]
    // 0xb8cfbc: mov             x1, x0
    // 0xb8cfc0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0xb8cfc0: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0xb8cfc4: ldr             x4, [x4, #0x5d0]
    // 0xb8cfc8: r0 = Container()
    //     0xb8cfc8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8cfcc: r0 = InkWell()
    //     0xb8cfcc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8cfd0: mov             x3, x0
    // 0xb8cfd4: ldur            x0, [fp, #-0x10]
    // 0xb8cfd8: stur            x3, [fp, #-8]
    // 0xb8cfdc: StoreField: r3->field_b = r0
    //     0xb8cfdc: stur            w0, [x3, #0xb]
    // 0xb8cfe0: ldur            x2, [fp, #-0x20]
    // 0xb8cfe4: r1 = Function '<anonymous closure>':.
    //     0xb8cfe4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55340] AnonymousClosure: (0xb8d2dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8cfe8: ldr             x1, [x1, #0x340]
    // 0xb8cfec: r0 = AllocateClosure()
    //     0xb8cfec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8cff0: mov             x1, x0
    // 0xb8cff4: ldur            x0, [fp, #-8]
    // 0xb8cff8: StoreField: r0->field_f = r1
    //     0xb8cff8: stur            w1, [x0, #0xf]
    // 0xb8cffc: r1 = true
    //     0xb8cffc: add             x1, NULL, #0x20  ; true
    // 0xb8d000: StoreField: r0->field_43 = r1
    //     0xb8d000: stur            w1, [x0, #0x43]
    // 0xb8d004: r2 = Instance_BoxShape
    //     0xb8d004: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8d008: ldr             x2, [x2, #0x80]
    // 0xb8d00c: StoreField: r0->field_47 = r2
    //     0xb8d00c: stur            w2, [x0, #0x47]
    // 0xb8d010: StoreField: r0->field_6f = r1
    //     0xb8d010: stur            w1, [x0, #0x6f]
    // 0xb8d014: r2 = false
    //     0xb8d014: add             x2, NULL, #0x30  ; false
    // 0xb8d018: StoreField: r0->field_73 = r2
    //     0xb8d018: stur            w2, [x0, #0x73]
    // 0xb8d01c: StoreField: r0->field_83 = r1
    //     0xb8d01c: stur            w1, [x0, #0x83]
    // 0xb8d020: StoreField: r0->field_7b = r2
    //     0xb8d020: stur            w2, [x0, #0x7b]
    // 0xb8d024: r0 = Padding()
    //     0xb8d024: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8d028: mov             x2, x0
    // 0xb8d02c: r0 = Instance_EdgeInsets
    //     0xb8d02c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb8d030: ldr             x0, [x0, #0x240]
    // 0xb8d034: stur            x2, [fp, #-0x10]
    // 0xb8d038: StoreField: r2->field_f = r0
    //     0xb8d038: stur            w0, [x2, #0xf]
    // 0xb8d03c: ldur            x0, [fp, #-8]
    // 0xb8d040: StoreField: r2->field_b = r0
    //     0xb8d040: stur            w0, [x2, #0xb]
    // 0xb8d044: ldur            x0, [fp, #-0x30]
    // 0xb8d048: LoadField: r1 = r0->field_b
    //     0xb8d048: ldur            w1, [x0, #0xb]
    // 0xb8d04c: LoadField: r3 = r0->field_f
    //     0xb8d04c: ldur            w3, [x0, #0xf]
    // 0xb8d050: DecompressPointer r3
    //     0xb8d050: add             x3, x3, HEAP, lsl #32
    // 0xb8d054: LoadField: r4 = r3->field_b
    //     0xb8d054: ldur            w4, [x3, #0xb]
    // 0xb8d058: r3 = LoadInt32Instr(r1)
    //     0xb8d058: sbfx            x3, x1, #1, #0x1f
    // 0xb8d05c: stur            x3, [fp, #-0x40]
    // 0xb8d060: r1 = LoadInt32Instr(r4)
    //     0xb8d060: sbfx            x1, x4, #1, #0x1f
    // 0xb8d064: cmp             x3, x1
    // 0xb8d068: b.ne            #0xb8d074
    // 0xb8d06c: mov             x1, x0
    // 0xb8d070: r0 = _growToNextCapacity()
    //     0xb8d070: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8d074: ldur            x2, [fp, #-0x30]
    // 0xb8d078: ldur            x3, [fp, #-0x40]
    // 0xb8d07c: add             x0, x3, #1
    // 0xb8d080: lsl             x1, x0, #1
    // 0xb8d084: StoreField: r2->field_b = r1
    //     0xb8d084: stur            w1, [x2, #0xb]
    // 0xb8d088: LoadField: r1 = r2->field_f
    //     0xb8d088: ldur            w1, [x2, #0xf]
    // 0xb8d08c: DecompressPointer r1
    //     0xb8d08c: add             x1, x1, HEAP, lsl #32
    // 0xb8d090: ldur            x0, [fp, #-0x10]
    // 0xb8d094: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb8d094: add             x25, x1, x3, lsl #2
    //     0xb8d098: add             x25, x25, #0xf
    //     0xb8d09c: str             w0, [x25]
    //     0xb8d0a0: tbz             w0, #0, #0xb8d0bc
    //     0xb8d0a4: ldurb           w16, [x1, #-1]
    //     0xb8d0a8: ldurb           w17, [x0, #-1]
    //     0xb8d0ac: and             x16, x17, x16, lsr #2
    //     0xb8d0b0: tst             x16, HEAP, lsr #32
    //     0xb8d0b4: b.eq            #0xb8d0bc
    //     0xb8d0b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb8d0bc: ldur            x3, [fp, #-0x18]
    // 0xb8d0c0: ldur            x0, [fp, #-0x48]
    // 0xb8d0c4: ldur            x1, [fp, #-0x28]
    // 0xb8d0c8: r0 = Column()
    //     0xb8d0c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8d0cc: mov             x1, x0
    // 0xb8d0d0: r0 = Instance_Axis
    //     0xb8d0d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8d0d4: stur            x1, [fp, #-8]
    // 0xb8d0d8: StoreField: r1->field_f = r0
    //     0xb8d0d8: stur            w0, [x1, #0xf]
    // 0xb8d0dc: r2 = Instance_MainAxisAlignment
    //     0xb8d0dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8d0e0: ldr             x2, [x2, #0xa08]
    // 0xb8d0e4: StoreField: r1->field_13 = r2
    //     0xb8d0e4: stur            w2, [x1, #0x13]
    // 0xb8d0e8: r3 = Instance_MainAxisSize
    //     0xb8d0e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8d0ec: ldr             x3, [x3, #0xa10]
    // 0xb8d0f0: ArrayStore: r1[0] = r3  ; List_4
    //     0xb8d0f0: stur            w3, [x1, #0x17]
    // 0xb8d0f4: r4 = Instance_CrossAxisAlignment
    //     0xb8d0f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8d0f8: ldr             x4, [x4, #0xa18]
    // 0xb8d0fc: StoreField: r1->field_1b = r4
    //     0xb8d0fc: stur            w4, [x1, #0x1b]
    // 0xb8d100: r5 = Instance_VerticalDirection
    //     0xb8d100: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8d104: ldr             x5, [x5, #0xa20]
    // 0xb8d108: StoreField: r1->field_23 = r5
    //     0xb8d108: stur            w5, [x1, #0x23]
    // 0xb8d10c: r6 = Instance_Clip
    //     0xb8d10c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8d110: ldr             x6, [x6, #0x38]
    // 0xb8d114: StoreField: r1->field_2b = r6
    //     0xb8d114: stur            w6, [x1, #0x2b]
    // 0xb8d118: StoreField: r1->field_2f = rZR
    //     0xb8d118: stur            xzr, [x1, #0x2f]
    // 0xb8d11c: ldur            x7, [fp, #-0x30]
    // 0xb8d120: StoreField: r1->field_b = r7
    //     0xb8d120: stur            w7, [x1, #0xb]
    // 0xb8d124: r0 = Visibility()
    //     0xb8d124: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb8d128: mov             x3, x0
    // 0xb8d12c: ldur            x0, [fp, #-8]
    // 0xb8d130: stur            x3, [fp, #-0x10]
    // 0xb8d134: StoreField: r3->field_b = r0
    //     0xb8d134: stur            w0, [x3, #0xb]
    // 0xb8d138: r0 = Instance_SizedBox
    //     0xb8d138: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb8d13c: StoreField: r3->field_f = r0
    //     0xb8d13c: stur            w0, [x3, #0xf]
    // 0xb8d140: ldur            x0, [fp, #-0x28]
    // 0xb8d144: StoreField: r3->field_13 = r0
    //     0xb8d144: stur            w0, [x3, #0x13]
    // 0xb8d148: r0 = false
    //     0xb8d148: add             x0, NULL, #0x30  ; false
    // 0xb8d14c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb8d14c: stur            w0, [x3, #0x17]
    // 0xb8d150: StoreField: r3->field_1b = r0
    //     0xb8d150: stur            w0, [x3, #0x1b]
    // 0xb8d154: StoreField: r3->field_1f = r0
    //     0xb8d154: stur            w0, [x3, #0x1f]
    // 0xb8d158: StoreField: r3->field_23 = r0
    //     0xb8d158: stur            w0, [x3, #0x23]
    // 0xb8d15c: StoreField: r3->field_27 = r0
    //     0xb8d15c: stur            w0, [x3, #0x27]
    // 0xb8d160: StoreField: r3->field_2b = r0
    //     0xb8d160: stur            w0, [x3, #0x2b]
    // 0xb8d164: r1 = Null
    //     0xb8d164: mov             x1, NULL
    // 0xb8d168: r2 = 8
    //     0xb8d168: movz            x2, #0x8
    // 0xb8d16c: r0 = AllocateArray()
    //     0xb8d16c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8d170: mov             x2, x0
    // 0xb8d174: ldur            x0, [fp, #-0x18]
    // 0xb8d178: stur            x2, [fp, #-8]
    // 0xb8d17c: StoreField: r2->field_f = r0
    //     0xb8d17c: stur            w0, [x2, #0xf]
    // 0xb8d180: r16 = Instance_SizedBox
    //     0xb8d180: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xb8d184: ldr             x16, [x16, #0xd68]
    // 0xb8d188: StoreField: r2->field_13 = r16
    //     0xb8d188: stur            w16, [x2, #0x13]
    // 0xb8d18c: ldur            x0, [fp, #-0x48]
    // 0xb8d190: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8d190: stur            w0, [x2, #0x17]
    // 0xb8d194: ldur            x0, [fp, #-0x10]
    // 0xb8d198: StoreField: r2->field_1b = r0
    //     0xb8d198: stur            w0, [x2, #0x1b]
    // 0xb8d19c: r1 = <Widget>
    //     0xb8d19c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8d1a0: r0 = AllocateGrowableArray()
    //     0xb8d1a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8d1a4: mov             x1, x0
    // 0xb8d1a8: ldur            x0, [fp, #-8]
    // 0xb8d1ac: stur            x1, [fp, #-0x10]
    // 0xb8d1b0: StoreField: r1->field_f = r0
    //     0xb8d1b0: stur            w0, [x1, #0xf]
    // 0xb8d1b4: r0 = 8
    //     0xb8d1b4: movz            x0, #0x8
    // 0xb8d1b8: StoreField: r1->field_b = r0
    //     0xb8d1b8: stur            w0, [x1, #0xb]
    // 0xb8d1bc: r0 = Column()
    //     0xb8d1bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8d1c0: mov             x1, x0
    // 0xb8d1c4: r0 = Instance_Axis
    //     0xb8d1c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8d1c8: stur            x1, [fp, #-8]
    // 0xb8d1cc: StoreField: r1->field_f = r0
    //     0xb8d1cc: stur            w0, [x1, #0xf]
    // 0xb8d1d0: r0 = Instance_MainAxisAlignment
    //     0xb8d1d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8d1d4: ldr             x0, [x0, #0xa08]
    // 0xb8d1d8: StoreField: r1->field_13 = r0
    //     0xb8d1d8: stur            w0, [x1, #0x13]
    // 0xb8d1dc: r0 = Instance_MainAxisSize
    //     0xb8d1dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8d1e0: ldr             x0, [x0, #0xa10]
    // 0xb8d1e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8d1e4: stur            w0, [x1, #0x17]
    // 0xb8d1e8: r0 = Instance_CrossAxisAlignment
    //     0xb8d1e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8d1ec: ldr             x0, [x0, #0xa18]
    // 0xb8d1f0: StoreField: r1->field_1b = r0
    //     0xb8d1f0: stur            w0, [x1, #0x1b]
    // 0xb8d1f4: r0 = Instance_VerticalDirection
    //     0xb8d1f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8d1f8: ldr             x0, [x0, #0xa20]
    // 0xb8d1fc: StoreField: r1->field_23 = r0
    //     0xb8d1fc: stur            w0, [x1, #0x23]
    // 0xb8d200: r0 = Instance_Clip
    //     0xb8d200: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8d204: ldr             x0, [x0, #0x38]
    // 0xb8d208: StoreField: r1->field_2b = r0
    //     0xb8d208: stur            w0, [x1, #0x2b]
    // 0xb8d20c: StoreField: r1->field_2f = rZR
    //     0xb8d20c: stur            xzr, [x1, #0x2f]
    // 0xb8d210: ldur            x0, [fp, #-0x10]
    // 0xb8d214: StoreField: r1->field_b = r0
    //     0xb8d214: stur            w0, [x1, #0xb]
    // 0xb8d218: r0 = Padding()
    //     0xb8d218: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8d21c: r1 = Instance_EdgeInsets
    //     0xb8d21c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb8d220: ldr             x1, [x1, #0x668]
    // 0xb8d224: StoreField: r0->field_f = r1
    //     0xb8d224: stur            w1, [x0, #0xf]
    // 0xb8d228: ldur            x1, [fp, #-8]
    // 0xb8d22c: StoreField: r0->field_b = r1
    //     0xb8d22c: stur            w1, [x0, #0xb]
    // 0xb8d230: b               #0xb8d23c
    // 0xb8d234: r0 = Instance_Center
    //     0xb8d234: add             x0, PP, #0x32, lsl #12  ; [pp+0x326f0] Obj!Center@d68321
    //     0xb8d238: ldr             x0, [x0, #0x6f0]
    // 0xb8d23c: LeaveFrame
    //     0xb8d23c: mov             SP, fp
    //     0xb8d240: ldp             fp, lr, [SP], #0x10
    // 0xb8d244: ret
    //     0xb8d244: ret             
    // 0xb8d248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d248: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d24c: b               #0xb8aaac
    // 0xb8d250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d250: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d268: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d268: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d26c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d26c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d270: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d274: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d278: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d27c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d27c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d280: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d284: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8d28c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d28c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8d2dc, size: 0x90
    // 0xb8d2dc: EnterFrame
    //     0xb8d2dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d2e0: mov             fp, SP
    // 0xb8d2e4: AllocStack(0x20)
    //     0xb8d2e4: sub             SP, SP, #0x20
    // 0xb8d2e8: SetupParameters()
    //     0xb8d2e8: ldr             x0, [fp, #0x10]
    //     0xb8d2ec: ldur            w1, [x0, #0x17]
    //     0xb8d2f0: add             x1, x1, HEAP, lsl #32
    // 0xb8d2f4: CheckStackOverflow
    //     0xb8d2f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d2f8: cmp             SP, x16
    //     0xb8d2fc: b.ls            #0xb8d360
    // 0xb8d300: LoadField: r0 = r1->field_f
    //     0xb8d300: ldur            w0, [x1, #0xf]
    // 0xb8d304: DecompressPointer r0
    //     0xb8d304: add             x0, x0, HEAP, lsl #32
    // 0xb8d308: LoadField: r1 = r0->field_b
    //     0xb8d308: ldur            w1, [x0, #0xb]
    // 0xb8d30c: DecompressPointer r1
    //     0xb8d30c: add             x1, x1, HEAP, lsl #32
    // 0xb8d310: cmp             w1, NULL
    // 0xb8d314: b.eq            #0xb8d368
    // 0xb8d318: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8d318: ldur            w0, [x1, #0x17]
    // 0xb8d31c: DecompressPointer r0
    //     0xb8d31c: add             x0, x0, HEAP, lsl #32
    // 0xb8d320: r16 = "view_all"
    //     0xb8d320: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb8d324: ldr             x16, [x16, #0xba0]
    // 0xb8d328: stp             x16, x0, [SP, #0x10]
    // 0xb8d32c: r16 = ""
    //     0xb8d32c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8d330: r30 = true
    //     0xb8d330: add             lr, NULL, #0x20  ; true
    // 0xb8d334: stp             lr, x16, [SP]
    // 0xb8d338: r4 = 0
    //     0xb8d338: movz            x4, #0
    // 0xb8d33c: ldr             x0, [SP, #0x18]
    // 0xb8d340: r16 = UnlinkedCall_0x613b5c
    //     0xb8d340: add             x16, PP, #0x55, lsl #12  ; [pp+0x55348] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d344: add             x16, x16, #0x348
    // 0xb8d348: ldp             x5, lr, [x16]
    // 0xb8d34c: blr             lr
    // 0xb8d350: r0 = Null
    //     0xb8d350: mov             x0, NULL
    // 0xb8d354: LeaveFrame
    //     0xb8d354: mov             SP, fp
    //     0xb8d358: ldp             fp, lr, [SP], #0x10
    // 0xb8d35c: ret
    //     0xb8d35c: ret             
    // 0xb8d360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d364: b               #0xb8d300
    // 0xb8d368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0xb8d36c, size: 0x8c
    // 0xb8d36c: EnterFrame
    //     0xb8d36c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d370: mov             fp, SP
    // 0xb8d374: AllocStack(0x20)
    //     0xb8d374: sub             SP, SP, #0x20
    // 0xb8d378: SetupParameters()
    //     0xb8d378: ldr             x0, [fp, #0x20]
    //     0xb8d37c: ldur            w1, [x0, #0x17]
    //     0xb8d380: add             x1, x1, HEAP, lsl #32
    // 0xb8d384: CheckStackOverflow
    //     0xb8d384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d388: cmp             SP, x16
    //     0xb8d38c: b.ls            #0xb8d3ec
    // 0xb8d390: LoadField: r0 = r1->field_f
    //     0xb8d390: ldur            w0, [x1, #0xf]
    // 0xb8d394: DecompressPointer r0
    //     0xb8d394: add             x0, x0, HEAP, lsl #32
    // 0xb8d398: LoadField: r1 = r0->field_b
    //     0xb8d398: ldur            w1, [x0, #0xb]
    // 0xb8d39c: DecompressPointer r1
    //     0xb8d39c: add             x1, x1, HEAP, lsl #32
    // 0xb8d3a0: cmp             w1, NULL
    // 0xb8d3a4: b.eq            #0xb8d3f4
    // 0xb8d3a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8d3a8: ldur            w0, [x1, #0x17]
    // 0xb8d3ac: DecompressPointer r0
    //     0xb8d3ac: add             x0, x0, HEAP, lsl #32
    // 0xb8d3b0: ldr             x16, [fp, #0x18]
    // 0xb8d3b4: stp             x16, x0, [SP, #0x10]
    // 0xb8d3b8: ldr             x16, [fp, #0x10]
    // 0xb8d3bc: r30 = false
    //     0xb8d3bc: add             lr, NULL, #0x30  ; false
    // 0xb8d3c0: stp             lr, x16, [SP]
    // 0xb8d3c4: r4 = 0
    //     0xb8d3c4: movz            x4, #0
    // 0xb8d3c8: ldr             x0, [SP, #0x18]
    // 0xb8d3cc: r16 = UnlinkedCall_0x613b5c
    //     0xb8d3cc: add             x16, PP, #0x55, lsl #12  ; [pp+0x55358] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d3d0: add             x16, x16, #0x358
    // 0xb8d3d4: ldp             x5, lr, [x16]
    // 0xb8d3d8: blr             lr
    // 0xb8d3dc: r0 = Null
    //     0xb8d3dc: mov             x0, NULL
    // 0xb8d3e0: LeaveFrame
    //     0xb8d3e0: mov             SP, fp
    //     0xb8d3e4: ldp             fp, lr, [SP], #0x10
    // 0xb8d3e8: ret
    //     0xb8d3e8: ret             
    // 0xb8d3ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8d3ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8d3f0: b               #0xb8d390
    // 0xb8d3f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8d3f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb8d3f8, size: 0x7d0
    // 0xb8d3f8: EnterFrame
    //     0xb8d3f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8d3fc: mov             fp, SP
    // 0xb8d400: AllocStack(0x68)
    //     0xb8d400: sub             SP, SP, #0x68
    // 0xb8d404: SetupParameters()
    //     0xb8d404: ldr             x0, [fp, #0x20]
    //     0xb8d408: ldur            w1, [x0, #0x17]
    //     0xb8d40c: add             x1, x1, HEAP, lsl #32
    //     0xb8d410: stur            x1, [fp, #-8]
    // 0xb8d414: CheckStackOverflow
    //     0xb8d414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8d418: cmp             SP, x16
    //     0xb8d41c: b.ls            #0xb8dbb4
    // 0xb8d420: r1 = 3
    //     0xb8d420: movz            x1, #0x3
    // 0xb8d424: r0 = AllocateContext()
    //     0xb8d424: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8d428: mov             x3, x0
    // 0xb8d42c: ldur            x0, [fp, #-8]
    // 0xb8d430: stur            x3, [fp, #-0x18]
    // 0xb8d434: StoreField: r3->field_b = r0
    //     0xb8d434: stur            w0, [x3, #0xb]
    // 0xb8d438: ldr             x4, [fp, #0x18]
    // 0xb8d43c: StoreField: r3->field_f = r4
    //     0xb8d43c: stur            w4, [x3, #0xf]
    // 0xb8d440: ldr             x5, [fp, #0x10]
    // 0xb8d444: StoreField: r3->field_13 = r5
    //     0xb8d444: stur            w5, [x3, #0x13]
    // 0xb8d448: LoadField: r1 = r0->field_f
    //     0xb8d448: ldur            w1, [x0, #0xf]
    // 0xb8d44c: DecompressPointer r1
    //     0xb8d44c: add             x1, x1, HEAP, lsl #32
    // 0xb8d450: LoadField: r2 = r1->field_b
    //     0xb8d450: ldur            w2, [x1, #0xb]
    // 0xb8d454: DecompressPointer r2
    //     0xb8d454: add             x2, x2, HEAP, lsl #32
    // 0xb8d458: cmp             w2, NULL
    // 0xb8d45c: b.eq            #0xb8dbbc
    // 0xb8d460: LoadField: r1 = r2->field_f
    //     0xb8d460: ldur            w1, [x2, #0xf]
    // 0xb8d464: DecompressPointer r1
    //     0xb8d464: add             x1, x1, HEAP, lsl #32
    // 0xb8d468: LoadField: r2 = r1->field_b
    //     0xb8d468: ldur            w2, [x1, #0xb]
    // 0xb8d46c: DecompressPointer r2
    //     0xb8d46c: add             x2, x2, HEAP, lsl #32
    // 0xb8d470: cmp             w2, NULL
    // 0xb8d474: b.ne            #0xb8d480
    // 0xb8d478: r2 = Null
    //     0xb8d478: mov             x2, NULL
    // 0xb8d47c: b               #0xb8d4e8
    // 0xb8d480: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb8d480: ldur            w1, [x2, #0x17]
    // 0xb8d484: DecompressPointer r1
    //     0xb8d484: add             x1, x1, HEAP, lsl #32
    // 0xb8d488: cmp             w1, NULL
    // 0xb8d48c: b.ne            #0xb8d498
    // 0xb8d490: r0 = Null
    //     0xb8d490: mov             x0, NULL
    // 0xb8d494: b               #0xb8d4e4
    // 0xb8d498: LoadField: r6 = r1->field_f
    //     0xb8d498: ldur            w6, [x1, #0xf]
    // 0xb8d49c: DecompressPointer r6
    //     0xb8d49c: add             x6, x6, HEAP, lsl #32
    // 0xb8d4a0: stur            x6, [fp, #-0x10]
    // 0xb8d4a4: cmp             w6, NULL
    // 0xb8d4a8: b.ne            #0xb8d4b4
    // 0xb8d4ac: r0 = Null
    //     0xb8d4ac: mov             x0, NULL
    // 0xb8d4b0: b               #0xb8d4e4
    // 0xb8d4b4: r1 = Function '<anonymous closure>':.
    //     0xb8d4b4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55368] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xb8d4b8: ldr             x1, [x1, #0x368]
    // 0xb8d4bc: r2 = Null
    //     0xb8d4bc: mov             x2, NULL
    // 0xb8d4c0: r0 = AllocateClosure()
    //     0xb8d4c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d4c4: ldur            x16, [fp, #-0x10]
    // 0xb8d4c8: stp             x16, NULL, [SP, #8]
    // 0xb8d4cc: str             x0, [SP]
    // 0xb8d4d0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8d4d0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8d4d4: r0 = expand()
    //     0xb8d4d4: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xb8d4d8: mov             x1, x0
    // 0xb8d4dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8d4dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8d4e0: r0 = toList()
    //     0xb8d4e0: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0xb8d4e4: mov             x2, x0
    // 0xb8d4e8: cmp             w2, NULL
    // 0xb8d4ec: b.ne            #0xb8d4fc
    // 0xb8d4f0: ldr             x3, [fp, #0x10]
    // 0xb8d4f4: r1 = Null
    //     0xb8d4f4: mov             x1, NULL
    // 0xb8d4f8: b               #0xb8d538
    // 0xb8d4fc: ldr             x3, [fp, #0x10]
    // 0xb8d500: LoadField: r0 = r2->field_b
    //     0xb8d500: ldur            w0, [x2, #0xb]
    // 0xb8d504: r4 = LoadInt32Instr(r3)
    //     0xb8d504: sbfx            x4, x3, #1, #0x1f
    //     0xb8d508: tbz             w3, #0, #0xb8d510
    //     0xb8d50c: ldur            x4, [x3, #7]
    // 0xb8d510: r1 = LoadInt32Instr(r0)
    //     0xb8d510: sbfx            x1, x0, #1, #0x1f
    // 0xb8d514: mov             x0, x1
    // 0xb8d518: mov             x1, x4
    // 0xb8d51c: cmp             x1, x0
    // 0xb8d520: b.hs            #0xb8dbc0
    // 0xb8d524: LoadField: r0 = r2->field_f
    //     0xb8d524: ldur            w0, [x2, #0xf]
    // 0xb8d528: DecompressPointer r0
    //     0xb8d528: add             x0, x0, HEAP, lsl #32
    // 0xb8d52c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb8d52c: add             x16, x0, x4, lsl #2
    //     0xb8d530: ldur            w1, [x16, #0xf]
    // 0xb8d534: DecompressPointer r1
    //     0xb8d534: add             x1, x1, HEAP, lsl #32
    // 0xb8d538: ldur            x2, [fp, #-0x18]
    // 0xb8d53c: mov             x0, x1
    // 0xb8d540: stur            x1, [fp, #-0x10]
    // 0xb8d544: ArrayStore: r2[0] = r0  ; List_4
    //     0xb8d544: stur            w0, [x2, #0x17]
    //     0xb8d548: tbz             w0, #0, #0xb8d564
    //     0xb8d54c: ldurb           w16, [x2, #-1]
    //     0xb8d550: ldurb           w17, [x0, #-1]
    //     0xb8d554: and             x16, x17, x16, lsr #2
    //     0xb8d558: tst             x16, HEAP, lsr #32
    //     0xb8d55c: b.eq            #0xb8d564
    //     0xb8d560: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb8d564: r0 = LoadInt32Instr(r3)
    //     0xb8d564: sbfx            x0, x3, #1, #0x1f
    //     0xb8d568: tbz             w3, #0, #0xb8d570
    //     0xb8d56c: ldur            x0, [x3, #7]
    // 0xb8d570: cmp             x0, #4
    // 0xb8d574: b.ge            #0xb8d7c4
    // 0xb8d578: str             x1, [SP]
    // 0xb8d57c: r4 = 0
    //     0xb8d57c: movz            x4, #0
    // 0xb8d580: ldr             x0, [SP]
    // 0xb8d584: r16 = UnlinkedCall_0x613b5c
    //     0xb8d584: add             x16, PP, #0x55, lsl #12  ; [pp+0x55370] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d588: add             x16, x16, #0x370
    // 0xb8d58c: ldp             x5, lr, [x16]
    // 0xb8d590: blr             lr
    // 0xb8d594: r1 = 60
    //     0xb8d594: movz            x1, #0x3c
    // 0xb8d598: branchIfSmi(r0, 0xb8d5a4)
    //     0xb8d598: tbz             w0, #0, #0xb8d5a4
    // 0xb8d59c: r1 = LoadClassIdInstr(r0)
    //     0xb8d59c: ldur            x1, [x0, #-1]
    //     0xb8d5a0: ubfx            x1, x1, #0xc, #0x14
    // 0xb8d5a4: r16 = "image"
    //     0xb8d5a4: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb8d5a8: stp             x16, x0, [SP]
    // 0xb8d5ac: mov             x0, x1
    // 0xb8d5b0: mov             lr, x0
    // 0xb8d5b4: ldr             lr, [x21, lr, lsl #3]
    // 0xb8d5b8: blr             lr
    // 0xb8d5bc: tbnz            w0, #4, #0xb8d6ec
    // 0xb8d5c0: r0 = Radius()
    //     0xb8d5c0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8d5c4: d0 = 12.000000
    //     0xb8d5c4: fmov            d0, #12.00000000
    // 0xb8d5c8: stur            x0, [fp, #-0x20]
    // 0xb8d5cc: StoreField: r0->field_7 = d0
    //     0xb8d5cc: stur            d0, [x0, #7]
    // 0xb8d5d0: StoreField: r0->field_f = d0
    //     0xb8d5d0: stur            d0, [x0, #0xf]
    // 0xb8d5d4: r0 = BorderRadius()
    //     0xb8d5d4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8d5d8: mov             x1, x0
    // 0xb8d5dc: ldur            x0, [fp, #-0x20]
    // 0xb8d5e0: stur            x1, [fp, #-0x28]
    // 0xb8d5e4: StoreField: r1->field_7 = r0
    //     0xb8d5e4: stur            w0, [x1, #7]
    // 0xb8d5e8: StoreField: r1->field_b = r0
    //     0xb8d5e8: stur            w0, [x1, #0xb]
    // 0xb8d5ec: StoreField: r1->field_f = r0
    //     0xb8d5ec: stur            w0, [x1, #0xf]
    // 0xb8d5f0: StoreField: r1->field_13 = r0
    //     0xb8d5f0: stur            w0, [x1, #0x13]
    // 0xb8d5f4: ldur            x16, [fp, #-0x10]
    // 0xb8d5f8: str             x16, [SP]
    // 0xb8d5fc: r4 = 0
    //     0xb8d5fc: movz            x4, #0
    // 0xb8d600: ldr             x0, [SP]
    // 0xb8d604: r16 = UnlinkedCall_0x613b5c
    //     0xb8d604: add             x16, PP, #0x55, lsl #12  ; [pp+0x55380] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d608: add             x16, x16, #0x380
    // 0xb8d60c: ldp             x5, lr, [x16]
    // 0xb8d610: blr             lr
    // 0xb8d614: mov             x3, x0
    // 0xb8d618: r2 = Null
    //     0xb8d618: mov             x2, NULL
    // 0xb8d61c: r1 = Null
    //     0xb8d61c: mov             x1, NULL
    // 0xb8d620: stur            x3, [fp, #-0x20]
    // 0xb8d624: r4 = 60
    //     0xb8d624: movz            x4, #0x3c
    // 0xb8d628: branchIfSmi(r0, 0xb8d634)
    //     0xb8d628: tbz             w0, #0, #0xb8d634
    // 0xb8d62c: r4 = LoadClassIdInstr(r0)
    //     0xb8d62c: ldur            x4, [x0, #-1]
    //     0xb8d630: ubfx            x4, x4, #0xc, #0x14
    // 0xb8d634: sub             x4, x4, #0x5e
    // 0xb8d638: cmp             x4, #1
    // 0xb8d63c: b.ls            #0xb8d650
    // 0xb8d640: r8 = String
    //     0xb8d640: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb8d644: r3 = Null
    //     0xb8d644: add             x3, PP, #0x55, lsl #12  ; [pp+0x55390] Null
    //     0xb8d648: ldr             x3, [x3, #0x390]
    // 0xb8d64c: r0 = String()
    //     0xb8d64c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb8d650: r1 = Function '<anonymous closure>':.
    //     0xb8d650: add             x1, PP, #0x55, lsl #12  ; [pp+0x553a0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb8d654: ldr             x1, [x1, #0x3a0]
    // 0xb8d658: r2 = Null
    //     0xb8d658: mov             x2, NULL
    // 0xb8d65c: r0 = AllocateClosure()
    //     0xb8d65c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d660: r1 = Function '<anonymous closure>':.
    //     0xb8d660: add             x1, PP, #0x55, lsl #12  ; [pp+0x553a8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb8d664: ldr             x1, [x1, #0x3a8]
    // 0xb8d668: r2 = Null
    //     0xb8d668: mov             x2, NULL
    // 0xb8d66c: stur            x0, [fp, #-0x30]
    // 0xb8d670: r0 = AllocateClosure()
    //     0xb8d670: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d674: stur            x0, [fp, #-0x38]
    // 0xb8d678: r0 = CachedNetworkImage()
    //     0xb8d678: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb8d67c: stur            x0, [fp, #-0x40]
    // 0xb8d680: r16 = 60.000000
    //     0xb8d680: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8d684: ldr             x16, [x16, #0x110]
    // 0xb8d688: r30 = 60.000000
    //     0xb8d688: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8d68c: ldr             lr, [lr, #0x110]
    // 0xb8d690: stp             lr, x16, [SP, #0x18]
    // 0xb8d694: r16 = Instance_BoxFit
    //     0xb8d694: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb8d698: ldr             x16, [x16, #0x118]
    // 0xb8d69c: ldur            lr, [fp, #-0x30]
    // 0xb8d6a0: stp             lr, x16, [SP, #8]
    // 0xb8d6a4: ldur            x16, [fp, #-0x38]
    // 0xb8d6a8: str             x16, [SP]
    // 0xb8d6ac: mov             x1, x0
    // 0xb8d6b0: ldur            x2, [fp, #-0x20]
    // 0xb8d6b4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb8d6b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb8d6b8: ldr             x4, [x4, #0xc28]
    // 0xb8d6bc: r0 = CachedNetworkImage()
    //     0xb8d6bc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb8d6c0: r0 = ClipRRect()
    //     0xb8d6c0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb8d6c4: mov             x1, x0
    // 0xb8d6c8: ldur            x0, [fp, #-0x28]
    // 0xb8d6cc: StoreField: r1->field_f = r0
    //     0xb8d6cc: stur            w0, [x1, #0xf]
    // 0xb8d6d0: r0 = Instance_Clip
    //     0xb8d6d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb8d6d4: ldr             x0, [x0, #0x138]
    // 0xb8d6d8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8d6d8: stur            w0, [x1, #0x17]
    // 0xb8d6dc: ldur            x0, [fp, #-0x40]
    // 0xb8d6e0: StoreField: r1->field_b = r0
    //     0xb8d6e0: stur            w0, [x1, #0xb]
    // 0xb8d6e4: mov             x0, x1
    // 0xb8d6e8: b               #0xb8d75c
    // 0xb8d6ec: ldur            x16, [fp, #-0x10]
    // 0xb8d6f0: str             x16, [SP]
    // 0xb8d6f4: r4 = 0
    //     0xb8d6f4: movz            x4, #0
    // 0xb8d6f8: ldr             x0, [SP]
    // 0xb8d6fc: r16 = UnlinkedCall_0x613b5c
    //     0xb8d6fc: add             x16, PP, #0x55, lsl #12  ; [pp+0x553b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d700: add             x16, x16, #0x3b0
    // 0xb8d704: ldp             x5, lr, [x16]
    // 0xb8d708: blr             lr
    // 0xb8d70c: mov             x3, x0
    // 0xb8d710: r2 = Null
    //     0xb8d710: mov             x2, NULL
    // 0xb8d714: r1 = Null
    //     0xb8d714: mov             x1, NULL
    // 0xb8d718: stur            x3, [fp, #-0x20]
    // 0xb8d71c: r4 = 60
    //     0xb8d71c: movz            x4, #0x3c
    // 0xb8d720: branchIfSmi(r0, 0xb8d72c)
    //     0xb8d720: tbz             w0, #0, #0xb8d72c
    // 0xb8d724: r4 = LoadClassIdInstr(r0)
    //     0xb8d724: ldur            x4, [x0, #-1]
    //     0xb8d728: ubfx            x4, x4, #0xc, #0x14
    // 0xb8d72c: sub             x4, x4, #0x5e
    // 0xb8d730: cmp             x4, #1
    // 0xb8d734: b.ls            #0xb8d748
    // 0xb8d738: r8 = String
    //     0xb8d738: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb8d73c: r3 = Null
    //     0xb8d73c: add             x3, PP, #0x55, lsl #12  ; [pp+0x553c0] Null
    //     0xb8d740: ldr             x3, [x3, #0x3c0]
    // 0xb8d744: r0 = String()
    //     0xb8d744: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb8d748: r0 = VideoPlayerWidget()
    //     0xb8d748: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb8d74c: mov             x1, x0
    // 0xb8d750: ldur            x0, [fp, #-0x20]
    // 0xb8d754: StoreField: r1->field_b = r0
    //     0xb8d754: stur            w0, [x1, #0xb]
    // 0xb8d758: mov             x0, x1
    // 0xb8d75c: stur            x0, [fp, #-0x20]
    // 0xb8d760: r0 = InkWell()
    //     0xb8d760: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8d764: mov             x3, x0
    // 0xb8d768: ldur            x0, [fp, #-0x20]
    // 0xb8d76c: stur            x3, [fp, #-0x28]
    // 0xb8d770: StoreField: r3->field_b = r0
    //     0xb8d770: stur            w0, [x3, #0xb]
    // 0xb8d774: ldur            x2, [fp, #-0x18]
    // 0xb8d778: r1 = Function '<anonymous closure>':.
    //     0xb8d778: add             x1, PP, #0x55, lsl #12  ; [pp+0x553d0] AnonymousClosure: (0xb8dd04), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8d77c: ldr             x1, [x1, #0x3d0]
    // 0xb8d780: r0 = AllocateClosure()
    //     0xb8d780: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d784: mov             x1, x0
    // 0xb8d788: ldur            x0, [fp, #-0x28]
    // 0xb8d78c: StoreField: r0->field_f = r1
    //     0xb8d78c: stur            w1, [x0, #0xf]
    // 0xb8d790: r1 = true
    //     0xb8d790: add             x1, NULL, #0x20  ; true
    // 0xb8d794: StoreField: r0->field_43 = r1
    //     0xb8d794: stur            w1, [x0, #0x43]
    // 0xb8d798: r2 = Instance_BoxShape
    //     0xb8d798: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8d79c: ldr             x2, [x2, #0x80]
    // 0xb8d7a0: StoreField: r0->field_47 = r2
    //     0xb8d7a0: stur            w2, [x0, #0x47]
    // 0xb8d7a4: StoreField: r0->field_6f = r1
    //     0xb8d7a4: stur            w1, [x0, #0x6f]
    // 0xb8d7a8: r3 = false
    //     0xb8d7a8: add             x3, NULL, #0x30  ; false
    // 0xb8d7ac: StoreField: r0->field_73 = r3
    //     0xb8d7ac: stur            w3, [x0, #0x73]
    // 0xb8d7b0: StoreField: r0->field_83 = r1
    //     0xb8d7b0: stur            w1, [x0, #0x83]
    // 0xb8d7b4: StoreField: r0->field_7b = r3
    //     0xb8d7b4: stur            w3, [x0, #0x7b]
    // 0xb8d7b8: LeaveFrame
    //     0xb8d7b8: mov             SP, fp
    //     0xb8d7bc: ldp             fp, lr, [SP], #0x10
    // 0xb8d7c0: ret
    //     0xb8d7c0: ret             
    // 0xb8d7c4: ldur            x4, [fp, #-8]
    // 0xb8d7c8: r1 = true
    //     0xb8d7c8: add             x1, NULL, #0x20  ; true
    // 0xb8d7cc: r3 = false
    //     0xb8d7cc: add             x3, NULL, #0x30  ; false
    // 0xb8d7d0: r2 = Instance_BoxShape
    //     0xb8d7d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8d7d4: ldr             x2, [x2, #0x80]
    // 0xb8d7d8: r0 = Instance_Clip
    //     0xb8d7d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb8d7dc: ldr             x0, [x0, #0x138]
    // 0xb8d7e0: d0 = 12.000000
    //     0xb8d7e0: fmov            d0, #12.00000000
    // 0xb8d7e4: r0 = Radius()
    //     0xb8d7e4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8d7e8: d0 = 12.000000
    //     0xb8d7e8: fmov            d0, #12.00000000
    // 0xb8d7ec: stur            x0, [fp, #-0x20]
    // 0xb8d7f0: StoreField: r0->field_7 = d0
    //     0xb8d7f0: stur            d0, [x0, #7]
    // 0xb8d7f4: StoreField: r0->field_f = d0
    //     0xb8d7f4: stur            d0, [x0, #0xf]
    // 0xb8d7f8: r0 = BorderRadius()
    //     0xb8d7f8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8d7fc: mov             x1, x0
    // 0xb8d800: ldur            x0, [fp, #-0x20]
    // 0xb8d804: stur            x1, [fp, #-0x28]
    // 0xb8d808: StoreField: r1->field_7 = r0
    //     0xb8d808: stur            w0, [x1, #7]
    // 0xb8d80c: StoreField: r1->field_b = r0
    //     0xb8d80c: stur            w0, [x1, #0xb]
    // 0xb8d810: StoreField: r1->field_f = r0
    //     0xb8d810: stur            w0, [x1, #0xf]
    // 0xb8d814: StoreField: r1->field_13 = r0
    //     0xb8d814: stur            w0, [x1, #0x13]
    // 0xb8d818: ldur            x16, [fp, #-0x10]
    // 0xb8d81c: str             x16, [SP]
    // 0xb8d820: r4 = 0
    //     0xb8d820: movz            x4, #0
    // 0xb8d824: ldr             x0, [SP]
    // 0xb8d828: r16 = UnlinkedCall_0x613b5c
    //     0xb8d828: add             x16, PP, #0x55, lsl #12  ; [pp+0x553d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8d82c: add             x16, x16, #0x3d8
    // 0xb8d830: ldp             x5, lr, [x16]
    // 0xb8d834: blr             lr
    // 0xb8d838: mov             x3, x0
    // 0xb8d83c: r2 = Null
    //     0xb8d83c: mov             x2, NULL
    // 0xb8d840: r1 = Null
    //     0xb8d840: mov             x1, NULL
    // 0xb8d844: stur            x3, [fp, #-0x10]
    // 0xb8d848: r4 = 60
    //     0xb8d848: movz            x4, #0x3c
    // 0xb8d84c: branchIfSmi(r0, 0xb8d858)
    //     0xb8d84c: tbz             w0, #0, #0xb8d858
    // 0xb8d850: r4 = LoadClassIdInstr(r0)
    //     0xb8d850: ldur            x4, [x0, #-1]
    //     0xb8d854: ubfx            x4, x4, #0xc, #0x14
    // 0xb8d858: sub             x4, x4, #0x5e
    // 0xb8d85c: cmp             x4, #1
    // 0xb8d860: b.ls            #0xb8d874
    // 0xb8d864: r8 = String
    //     0xb8d864: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb8d868: r3 = Null
    //     0xb8d868: add             x3, PP, #0x55, lsl #12  ; [pp+0x553e8] Null
    //     0xb8d86c: ldr             x3, [x3, #0x3e8]
    // 0xb8d870: r0 = String()
    //     0xb8d870: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb8d874: r1 = Function '<anonymous closure>':.
    //     0xb8d874: add             x1, PP, #0x55, lsl #12  ; [pp+0x553f8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb8d878: ldr             x1, [x1, #0x3f8]
    // 0xb8d87c: r2 = Null
    //     0xb8d87c: mov             x2, NULL
    // 0xb8d880: r0 = AllocateClosure()
    //     0xb8d880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d884: r1 = Function '<anonymous closure>':.
    //     0xb8d884: add             x1, PP, #0x55, lsl #12  ; [pp+0x55400] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb8d888: ldr             x1, [x1, #0x400]
    // 0xb8d88c: r2 = Null
    //     0xb8d88c: mov             x2, NULL
    // 0xb8d890: stur            x0, [fp, #-0x20]
    // 0xb8d894: r0 = AllocateClosure()
    //     0xb8d894: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8d898: stur            x0, [fp, #-0x30]
    // 0xb8d89c: r0 = CachedNetworkImage()
    //     0xb8d89c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb8d8a0: stur            x0, [fp, #-0x38]
    // 0xb8d8a4: r16 = 60.000000
    //     0xb8d8a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8d8a8: ldr             x16, [x16, #0x110]
    // 0xb8d8ac: r30 = 60.000000
    //     0xb8d8ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8d8b0: ldr             lr, [lr, #0x110]
    // 0xb8d8b4: stp             lr, x16, [SP, #0x18]
    // 0xb8d8b8: r16 = Instance_BoxFit
    //     0xb8d8b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb8d8bc: ldr             x16, [x16, #0x118]
    // 0xb8d8c0: ldur            lr, [fp, #-0x20]
    // 0xb8d8c4: stp             lr, x16, [SP, #8]
    // 0xb8d8c8: ldur            x16, [fp, #-0x30]
    // 0xb8d8cc: str             x16, [SP]
    // 0xb8d8d0: mov             x1, x0
    // 0xb8d8d4: ldur            x2, [fp, #-0x10]
    // 0xb8d8d8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb8d8d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb8d8dc: ldr             x4, [x4, #0xc28]
    // 0xb8d8e0: r0 = CachedNetworkImage()
    //     0xb8d8e0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb8d8e4: r0 = ClipRRect()
    //     0xb8d8e4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb8d8e8: mov             x1, x0
    // 0xb8d8ec: ldur            x0, [fp, #-0x28]
    // 0xb8d8f0: stur            x1, [fp, #-0x10]
    // 0xb8d8f4: StoreField: r1->field_f = r0
    //     0xb8d8f4: stur            w0, [x1, #0xf]
    // 0xb8d8f8: r0 = Instance_Clip
    //     0xb8d8f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb8d8fc: ldr             x0, [x0, #0x138]
    // 0xb8d900: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8d900: stur            w0, [x1, #0x17]
    // 0xb8d904: ldur            x0, [fp, #-0x38]
    // 0xb8d908: StoreField: r1->field_b = r0
    //     0xb8d908: stur            w0, [x1, #0xb]
    // 0xb8d90c: r0 = Radius()
    //     0xb8d90c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb8d910: d0 = 12.000000
    //     0xb8d910: fmov            d0, #12.00000000
    // 0xb8d914: stur            x0, [fp, #-0x20]
    // 0xb8d918: StoreField: r0->field_7 = d0
    //     0xb8d918: stur            d0, [x0, #7]
    // 0xb8d91c: StoreField: r0->field_f = d0
    //     0xb8d91c: stur            d0, [x0, #0xf]
    // 0xb8d920: r0 = BorderRadius()
    //     0xb8d920: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8d924: mov             x2, x0
    // 0xb8d928: ldur            x0, [fp, #-0x20]
    // 0xb8d92c: stur            x2, [fp, #-0x28]
    // 0xb8d930: StoreField: r2->field_7 = r0
    //     0xb8d930: stur            w0, [x2, #7]
    // 0xb8d934: StoreField: r2->field_b = r0
    //     0xb8d934: stur            w0, [x2, #0xb]
    // 0xb8d938: StoreField: r2->field_f = r0
    //     0xb8d938: stur            w0, [x2, #0xf]
    // 0xb8d93c: StoreField: r2->field_13 = r0
    //     0xb8d93c: stur            w0, [x2, #0x13]
    // 0xb8d940: r1 = Instance_Color
    //     0xb8d940: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8d944: d0 = 0.600000
    //     0xb8d944: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xb8d948: r0 = withOpacity()
    //     0xb8d948: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb8d94c: stur            x0, [fp, #-0x20]
    // 0xb8d950: r0 = BoxDecoration()
    //     0xb8d950: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb8d954: mov             x3, x0
    // 0xb8d958: ldur            x0, [fp, #-0x20]
    // 0xb8d95c: stur            x3, [fp, #-0x30]
    // 0xb8d960: StoreField: r3->field_7 = r0
    //     0xb8d960: stur            w0, [x3, #7]
    // 0xb8d964: ldur            x0, [fp, #-0x28]
    // 0xb8d968: StoreField: r3->field_13 = r0
    //     0xb8d968: stur            w0, [x3, #0x13]
    // 0xb8d96c: r0 = Instance_BoxShape
    //     0xb8d96c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8d970: ldr             x0, [x0, #0x80]
    // 0xb8d974: StoreField: r3->field_23 = r0
    //     0xb8d974: stur            w0, [x3, #0x23]
    // 0xb8d978: r1 = Null
    //     0xb8d978: mov             x1, NULL
    // 0xb8d97c: r2 = 4
    //     0xb8d97c: movz            x2, #0x4
    // 0xb8d980: r0 = AllocateArray()
    //     0xb8d980: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8d984: mov             x2, x0
    // 0xb8d988: r16 = "+"
    //     0xb8d988: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xb8d98c: StoreField: r2->field_f = r16
    //     0xb8d98c: stur            w16, [x2, #0xf]
    // 0xb8d990: ldur            x0, [fp, #-8]
    // 0xb8d994: LoadField: r1 = r0->field_f
    //     0xb8d994: ldur            w1, [x0, #0xf]
    // 0xb8d998: DecompressPointer r1
    //     0xb8d998: add             x1, x1, HEAP, lsl #32
    // 0xb8d99c: LoadField: r0 = r1->field_b
    //     0xb8d99c: ldur            w0, [x1, #0xb]
    // 0xb8d9a0: DecompressPointer r0
    //     0xb8d9a0: add             x0, x0, HEAP, lsl #32
    // 0xb8d9a4: cmp             w0, NULL
    // 0xb8d9a8: b.eq            #0xb8dbc4
    // 0xb8d9ac: LoadField: r1 = r0->field_f
    //     0xb8d9ac: ldur            w1, [x0, #0xf]
    // 0xb8d9b0: DecompressPointer r1
    //     0xb8d9b0: add             x1, x1, HEAP, lsl #32
    // 0xb8d9b4: LoadField: r0 = r1->field_b
    //     0xb8d9b4: ldur            w0, [x1, #0xb]
    // 0xb8d9b8: DecompressPointer r0
    //     0xb8d9b8: add             x0, x0, HEAP, lsl #32
    // 0xb8d9bc: cmp             w0, NULL
    // 0xb8d9c0: b.ne            #0xb8d9cc
    // 0xb8d9c4: r0 = Null
    //     0xb8d9c4: mov             x0, NULL
    // 0xb8d9c8: b               #0xb8d9ec
    // 0xb8d9cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb8d9cc: ldur            w1, [x0, #0x17]
    // 0xb8d9d0: DecompressPointer r1
    //     0xb8d9d0: add             x1, x1, HEAP, lsl #32
    // 0xb8d9d4: cmp             w1, NULL
    // 0xb8d9d8: b.ne            #0xb8d9e4
    // 0xb8d9dc: r0 = Null
    //     0xb8d9dc: mov             x0, NULL
    // 0xb8d9e0: b               #0xb8d9ec
    // 0xb8d9e4: LoadField: r0 = r1->field_b
    //     0xb8d9e4: ldur            w0, [x1, #0xb]
    // 0xb8d9e8: DecompressPointer r0
    //     0xb8d9e8: add             x0, x0, HEAP, lsl #32
    // 0xb8d9ec: cmp             w0, NULL
    // 0xb8d9f0: b.ne            #0xb8d9fc
    // 0xb8d9f4: r0 = 0
    //     0xb8d9f4: movz            x0, #0
    // 0xb8d9f8: b               #0xb8da0c
    // 0xb8d9fc: r1 = LoadInt32Instr(r0)
    //     0xb8d9fc: sbfx            x1, x0, #1, #0x1f
    //     0xb8da00: tbz             w0, #0, #0xb8da08
    //     0xb8da04: ldur            x1, [x0, #7]
    // 0xb8da08: mov             x0, x1
    // 0xb8da0c: ldur            x3, [fp, #-0x10]
    // 0xb8da10: sub             x4, x0, #4
    // 0xb8da14: r0 = BoxInt64Instr(r4)
    //     0xb8da14: sbfiz           x0, x4, #1, #0x1f
    //     0xb8da18: cmp             x4, x0, asr #1
    //     0xb8da1c: b.eq            #0xb8da28
    //     0xb8da20: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb8da24: stur            x4, [x0, #7]
    // 0xb8da28: StoreField: r2->field_13 = r0
    //     0xb8da28: stur            w0, [x2, #0x13]
    // 0xb8da2c: str             x2, [SP]
    // 0xb8da30: r0 = _interpolate()
    //     0xb8da30: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb8da34: ldr             x1, [fp, #0x18]
    // 0xb8da38: stur            x0, [fp, #-8]
    // 0xb8da3c: r0 = of()
    //     0xb8da3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8da40: LoadField: r1 = r0->field_87
    //     0xb8da40: ldur            w1, [x0, #0x87]
    // 0xb8da44: DecompressPointer r1
    //     0xb8da44: add             x1, x1, HEAP, lsl #32
    // 0xb8da48: LoadField: r0 = r1->field_7
    //     0xb8da48: ldur            w0, [x1, #7]
    // 0xb8da4c: DecompressPointer r0
    //     0xb8da4c: add             x0, x0, HEAP, lsl #32
    // 0xb8da50: r16 = 12.000000
    //     0xb8da50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb8da54: ldr             x16, [x16, #0x9e8]
    // 0xb8da58: r30 = Instance_Color
    //     0xb8da58: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb8da5c: stp             lr, x16, [SP]
    // 0xb8da60: mov             x1, x0
    // 0xb8da64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb8da64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb8da68: ldr             x4, [x4, #0xaa0]
    // 0xb8da6c: r0 = copyWith()
    //     0xb8da6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8da70: stur            x0, [fp, #-0x20]
    // 0xb8da74: r0 = Text()
    //     0xb8da74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb8da78: mov             x1, x0
    // 0xb8da7c: ldur            x0, [fp, #-8]
    // 0xb8da80: stur            x1, [fp, #-0x28]
    // 0xb8da84: StoreField: r1->field_b = r0
    //     0xb8da84: stur            w0, [x1, #0xb]
    // 0xb8da88: ldur            x0, [fp, #-0x20]
    // 0xb8da8c: StoreField: r1->field_13 = r0
    //     0xb8da8c: stur            w0, [x1, #0x13]
    // 0xb8da90: r0 = Container()
    //     0xb8da90: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8da94: stur            x0, [fp, #-8]
    // 0xb8da98: r16 = 60.000000
    //     0xb8da98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8da9c: ldr             x16, [x16, #0x110]
    // 0xb8daa0: r30 = 60.000000
    //     0xb8daa0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb8daa4: ldr             lr, [lr, #0x110]
    // 0xb8daa8: stp             lr, x16, [SP, #0x18]
    // 0xb8daac: ldur            x16, [fp, #-0x30]
    // 0xb8dab0: r30 = Instance_Alignment
    //     0xb8dab0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8dab4: ldr             lr, [lr, #0xb10]
    // 0xb8dab8: stp             lr, x16, [SP, #8]
    // 0xb8dabc: ldur            x16, [fp, #-0x28]
    // 0xb8dac0: str             x16, [SP]
    // 0xb8dac4: mov             x1, x0
    // 0xb8dac8: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb8dac8: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3eb90] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb8dacc: ldr             x4, [x4, #0xb90]
    // 0xb8dad0: r0 = Container()
    //     0xb8dad0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8dad4: r1 = Null
    //     0xb8dad4: mov             x1, NULL
    // 0xb8dad8: r2 = 4
    //     0xb8dad8: movz            x2, #0x4
    // 0xb8dadc: r0 = AllocateArray()
    //     0xb8dadc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8dae0: mov             x2, x0
    // 0xb8dae4: ldur            x0, [fp, #-0x10]
    // 0xb8dae8: stur            x2, [fp, #-0x20]
    // 0xb8daec: StoreField: r2->field_f = r0
    //     0xb8daec: stur            w0, [x2, #0xf]
    // 0xb8daf0: ldur            x0, [fp, #-8]
    // 0xb8daf4: StoreField: r2->field_13 = r0
    //     0xb8daf4: stur            w0, [x2, #0x13]
    // 0xb8daf8: r1 = <Widget>
    //     0xb8daf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8dafc: r0 = AllocateGrowableArray()
    //     0xb8dafc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb8db00: mov             x1, x0
    // 0xb8db04: ldur            x0, [fp, #-0x20]
    // 0xb8db08: stur            x1, [fp, #-8]
    // 0xb8db0c: StoreField: r1->field_f = r0
    //     0xb8db0c: stur            w0, [x1, #0xf]
    // 0xb8db10: r0 = 4
    //     0xb8db10: movz            x0, #0x4
    // 0xb8db14: StoreField: r1->field_b = r0
    //     0xb8db14: stur            w0, [x1, #0xb]
    // 0xb8db18: r0 = Stack()
    //     0xb8db18: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb8db1c: mov             x1, x0
    // 0xb8db20: r0 = Instance_Alignment
    //     0xb8db20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8db24: ldr             x0, [x0, #0xb10]
    // 0xb8db28: stur            x1, [fp, #-0x10]
    // 0xb8db2c: StoreField: r1->field_f = r0
    //     0xb8db2c: stur            w0, [x1, #0xf]
    // 0xb8db30: r0 = Instance_StackFit
    //     0xb8db30: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb8db34: ldr             x0, [x0, #0xfa8]
    // 0xb8db38: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8db38: stur            w0, [x1, #0x17]
    // 0xb8db3c: r0 = Instance_Clip
    //     0xb8db3c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb8db40: ldr             x0, [x0, #0x7e0]
    // 0xb8db44: StoreField: r1->field_1b = r0
    //     0xb8db44: stur            w0, [x1, #0x1b]
    // 0xb8db48: ldur            x0, [fp, #-8]
    // 0xb8db4c: StoreField: r1->field_b = r0
    //     0xb8db4c: stur            w0, [x1, #0xb]
    // 0xb8db50: r0 = InkWell()
    //     0xb8db50: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb8db54: mov             x3, x0
    // 0xb8db58: ldur            x0, [fp, #-0x10]
    // 0xb8db5c: stur            x3, [fp, #-8]
    // 0xb8db60: StoreField: r3->field_b = r0
    //     0xb8db60: stur            w0, [x3, #0xb]
    // 0xb8db64: ldur            x2, [fp, #-0x18]
    // 0xb8db68: r1 = Function '<anonymous closure>':.
    //     0xb8db68: add             x1, PP, #0x55, lsl #12  ; [pp+0x55408] AnonymousClosure: (0xb8dbc8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8db6c: ldr             x1, [x1, #0x408]
    // 0xb8db70: r0 = AllocateClosure()
    //     0xb8db70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8db74: mov             x1, x0
    // 0xb8db78: ldur            x0, [fp, #-8]
    // 0xb8db7c: StoreField: r0->field_f = r1
    //     0xb8db7c: stur            w1, [x0, #0xf]
    // 0xb8db80: r1 = true
    //     0xb8db80: add             x1, NULL, #0x20  ; true
    // 0xb8db84: StoreField: r0->field_43 = r1
    //     0xb8db84: stur            w1, [x0, #0x43]
    // 0xb8db88: r2 = Instance_BoxShape
    //     0xb8db88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb8db8c: ldr             x2, [x2, #0x80]
    // 0xb8db90: StoreField: r0->field_47 = r2
    //     0xb8db90: stur            w2, [x0, #0x47]
    // 0xb8db94: StoreField: r0->field_6f = r1
    //     0xb8db94: stur            w1, [x0, #0x6f]
    // 0xb8db98: r2 = false
    //     0xb8db98: add             x2, NULL, #0x30  ; false
    // 0xb8db9c: StoreField: r0->field_73 = r2
    //     0xb8db9c: stur            w2, [x0, #0x73]
    // 0xb8dba0: StoreField: r0->field_83 = r1
    //     0xb8dba0: stur            w1, [x0, #0x83]
    // 0xb8dba4: StoreField: r0->field_7b = r2
    //     0xb8dba4: stur            w2, [x0, #0x7b]
    // 0xb8dba8: LeaveFrame
    //     0xb8dba8: mov             SP, fp
    //     0xb8dbac: ldp             fp, lr, [SP], #0x10
    // 0xb8dbb0: ret
    //     0xb8dbb0: ret             
    // 0xb8dbb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8dbb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8dbb8: b               #0xb8d420
    // 0xb8dbbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8dbbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8dbc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb8dbc0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8dbc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8dbc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8dbc8, size: 0x13c
    // 0xb8dbc8: EnterFrame
    //     0xb8dbc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb8dbcc: mov             fp, SP
    // 0xb8dbd0: AllocStack(0x28)
    //     0xb8dbd0: sub             SP, SP, #0x28
    // 0xb8dbd4: SetupParameters()
    //     0xb8dbd4: ldr             x0, [fp, #0x10]
    //     0xb8dbd8: ldur            w1, [x0, #0x17]
    //     0xb8dbdc: add             x1, x1, HEAP, lsl #32
    // 0xb8dbe0: CheckStackOverflow
    //     0xb8dbe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8dbe4: cmp             SP, x16
    //     0xb8dbe8: b.ls            #0xb8dcf4
    // 0xb8dbec: LoadField: r0 = r1->field_b
    //     0xb8dbec: ldur            w0, [x1, #0xb]
    // 0xb8dbf0: DecompressPointer r0
    //     0xb8dbf0: add             x0, x0, HEAP, lsl #32
    // 0xb8dbf4: stur            x0, [fp, #-8]
    // 0xb8dbf8: LoadField: r1 = r0->field_f
    //     0xb8dbf8: ldur            w1, [x0, #0xf]
    // 0xb8dbfc: DecompressPointer r1
    //     0xb8dbfc: add             x1, x1, HEAP, lsl #32
    // 0xb8dc00: LoadField: r2 = r1->field_b
    //     0xb8dc00: ldur            w2, [x1, #0xb]
    // 0xb8dc04: DecompressPointer r2
    //     0xb8dc04: add             x2, x2, HEAP, lsl #32
    // 0xb8dc08: cmp             w2, NULL
    // 0xb8dc0c: b.eq            #0xb8dcfc
    // 0xb8dc10: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb8dc10: ldur            w1, [x2, #0x17]
    // 0xb8dc14: DecompressPointer r1
    //     0xb8dc14: add             x1, x1, HEAP, lsl #32
    // 0xb8dc18: r16 = "more_media"
    //     0xb8dc18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc48] "more_media"
    //     0xb8dc1c: ldr             x16, [x16, #0xc48]
    // 0xb8dc20: stp             x16, x1, [SP, #0x10]
    // 0xb8dc24: r16 = ""
    //     0xb8dc24: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8dc28: r30 = false
    //     0xb8dc28: add             lr, NULL, #0x30  ; false
    // 0xb8dc2c: stp             lr, x16, [SP]
    // 0xb8dc30: r4 = 0
    //     0xb8dc30: movz            x4, #0
    // 0xb8dc34: ldr             x0, [SP, #0x18]
    // 0xb8dc38: r16 = UnlinkedCall_0x613b5c
    //     0xb8dc38: add             x16, PP, #0x55, lsl #12  ; [pp+0x55410] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8dc3c: add             x16, x16, #0x410
    // 0xb8dc40: ldp             x5, lr, [x16]
    // 0xb8dc44: blr             lr
    // 0xb8dc48: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb8dc48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb8dc4c: ldr             x0, [x0, #0x1c80]
    //     0xb8dc50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb8dc54: cmp             w0, w16
    //     0xb8dc58: b.ne            #0xb8dc64
    //     0xb8dc5c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb8dc60: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb8dc64: r1 = Null
    //     0xb8dc64: mov             x1, NULL
    // 0xb8dc68: r2 = 8
    //     0xb8dc68: movz            x2, #0x8
    // 0xb8dc6c: r0 = AllocateArray()
    //     0xb8dc6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8dc70: r16 = "product_short_id"
    //     0xb8dc70: add             x16, PP, #0xb, lsl #12  ; [pp+0xb490] "product_short_id"
    //     0xb8dc74: ldr             x16, [x16, #0x490]
    // 0xb8dc78: StoreField: r0->field_f = r16
    //     0xb8dc78: stur            w16, [x0, #0xf]
    // 0xb8dc7c: ldur            x1, [fp, #-8]
    // 0xb8dc80: LoadField: r2 = r1->field_f
    //     0xb8dc80: ldur            w2, [x1, #0xf]
    // 0xb8dc84: DecompressPointer r2
    //     0xb8dc84: add             x2, x2, HEAP, lsl #32
    // 0xb8dc88: LoadField: r1 = r2->field_b
    //     0xb8dc88: ldur            w1, [x2, #0xb]
    // 0xb8dc8c: DecompressPointer r1
    //     0xb8dc8c: add             x1, x1, HEAP, lsl #32
    // 0xb8dc90: cmp             w1, NULL
    // 0xb8dc94: b.eq            #0xb8dd00
    // 0xb8dc98: LoadField: r2 = r1->field_b
    //     0xb8dc98: ldur            w2, [x1, #0xb]
    // 0xb8dc9c: DecompressPointer r2
    //     0xb8dc9c: add             x2, x2, HEAP, lsl #32
    // 0xb8dca0: StoreField: r0->field_13 = r2
    //     0xb8dca0: stur            w2, [x0, #0x13]
    // 0xb8dca4: r16 = "is_product_page"
    //     0xb8dca4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f368] "is_product_page"
    //     0xb8dca8: ldr             x16, [x16, #0x368]
    // 0xb8dcac: ArrayStore: r0[0] = r16  ; List_4
    //     0xb8dcac: stur            w16, [x0, #0x17]
    // 0xb8dcb0: r16 = true
    //     0xb8dcb0: add             x16, NULL, #0x20  ; true
    // 0xb8dcb4: StoreField: r0->field_1b = r16
    //     0xb8dcb4: stur            w16, [x0, #0x1b]
    // 0xb8dcb8: r16 = <String, Object?>
    //     0xb8dcb8: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xb8dcbc: ldr             x16, [x16, #0xc28]
    // 0xb8dcc0: stp             x0, x16, [SP]
    // 0xb8dcc4: r0 = Map._fromLiteral()
    //     0xb8dcc4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb8dcc8: r16 = "/rating_review_media_screen"
    //     0xb8dcc8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0xb8dccc: ldr             x16, [x16, #0x9c8]
    // 0xb8dcd0: stp             x16, NULL, [SP, #8]
    // 0xb8dcd4: str             x0, [SP]
    // 0xb8dcd8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb8dcd8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb8dcdc: ldr             x4, [x4, #0x438]
    // 0xb8dce0: r0 = GetNavigation.toNamed()
    //     0xb8dce0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb8dce4: r0 = Null
    //     0xb8dce4: mov             x0, NULL
    // 0xb8dce8: LeaveFrame
    //     0xb8dce8: mov             SP, fp
    //     0xb8dcec: ldp             fp, lr, [SP], #0x10
    // 0xb8dcf0: ret
    //     0xb8dcf0: ret             
    // 0xb8dcf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8dcf4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8dcf8: b               #0xb8dbec
    // 0xb8dcfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8dcfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8dd00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8dd00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8dd04, size: 0x128
    // 0xb8dd04: EnterFrame
    //     0xb8dd04: stp             fp, lr, [SP, #-0x10]!
    //     0xb8dd08: mov             fp, SP
    // 0xb8dd0c: AllocStack(0x30)
    //     0xb8dd0c: sub             SP, SP, #0x30
    // 0xb8dd10: SetupParameters()
    //     0xb8dd10: ldr             x0, [fp, #0x10]
    //     0xb8dd14: ldur            w2, [x0, #0x17]
    //     0xb8dd18: add             x2, x2, HEAP, lsl #32
    //     0xb8dd1c: stur            x2, [fp, #-0x10]
    // 0xb8dd20: CheckStackOverflow
    //     0xb8dd20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8dd24: cmp             SP, x16
    //     0xb8dd28: b.ls            #0xb8de20
    // 0xb8dd2c: LoadField: r0 = r2->field_b
    //     0xb8dd2c: ldur            w0, [x2, #0xb]
    // 0xb8dd30: DecompressPointer r0
    //     0xb8dd30: add             x0, x0, HEAP, lsl #32
    // 0xb8dd34: LoadField: r1 = r0->field_f
    //     0xb8dd34: ldur            w1, [x0, #0xf]
    // 0xb8dd38: DecompressPointer r1
    //     0xb8dd38: add             x1, x1, HEAP, lsl #32
    // 0xb8dd3c: LoadField: r0 = r1->field_b
    //     0xb8dd3c: ldur            w0, [x1, #0xb]
    // 0xb8dd40: DecompressPointer r0
    //     0xb8dd40: add             x0, x0, HEAP, lsl #32
    // 0xb8dd44: stur            x0, [fp, #-8]
    // 0xb8dd48: cmp             w0, NULL
    // 0xb8dd4c: b.eq            #0xb8de28
    // 0xb8dd50: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb8dd50: ldur            w1, [x2, #0x17]
    // 0xb8dd54: DecompressPointer r1
    //     0xb8dd54: add             x1, x1, HEAP, lsl #32
    // 0xb8dd58: str             x1, [SP]
    // 0xb8dd5c: r4 = 0
    //     0xb8dd5c: movz            x4, #0
    // 0xb8dd60: ldr             x0, [SP]
    // 0xb8dd64: r16 = UnlinkedCall_0x613b5c
    //     0xb8dd64: add             x16, PP, #0x55, lsl #12  ; [pp+0x55420] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8dd68: add             x16, x16, #0x420
    // 0xb8dd6c: ldp             x5, lr, [x16]
    // 0xb8dd70: blr             lr
    // 0xb8dd74: mov             x1, x0
    // 0xb8dd78: ldur            x0, [fp, #-8]
    // 0xb8dd7c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb8dd7c: ldur            w2, [x0, #0x17]
    // 0xb8dd80: DecompressPointer r2
    //     0xb8dd80: add             x2, x2, HEAP, lsl #32
    // 0xb8dd84: r16 = "single_media"
    //     0xb8dd84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xb8dd88: ldr             x16, [x16, #0xab0]
    // 0xb8dd8c: stp             x16, x2, [SP, #0x10]
    // 0xb8dd90: r16 = false
    //     0xb8dd90: add             x16, NULL, #0x30  ; false
    // 0xb8dd94: stp             x16, x1, [SP]
    // 0xb8dd98: r4 = 0
    //     0xb8dd98: movz            x4, #0
    // 0xb8dd9c: ldr             x0, [SP, #0x18]
    // 0xb8dda0: r16 = UnlinkedCall_0x613b5c
    //     0xb8dda0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55430] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8dda4: add             x16, x16, #0x430
    // 0xb8dda8: ldp             x5, lr, [x16]
    // 0xb8ddac: blr             lr
    // 0xb8ddb0: ldur            x2, [fp, #-0x10]
    // 0xb8ddb4: LoadField: r1 = r2->field_f
    //     0xb8ddb4: ldur            w1, [x2, #0xf]
    // 0xb8ddb8: DecompressPointer r1
    //     0xb8ddb8: add             x1, x1, HEAP, lsl #32
    // 0xb8ddbc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb8ddbc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb8ddc0: r0 = of()
    //     0xb8ddc0: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb8ddc4: ldur            x2, [fp, #-0x10]
    // 0xb8ddc8: r1 = Function '<anonymous closure>':.
    //     0xb8ddc8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55440] AnonymousClosure: (0xb8de2c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xb8aa84)
    //     0xb8ddcc: ldr             x1, [x1, #0x440]
    // 0xb8ddd0: stur            x0, [fp, #-8]
    // 0xb8ddd4: r0 = AllocateClosure()
    //     0xb8ddd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8ddd8: r1 = Null
    //     0xb8ddd8: mov             x1, NULL
    // 0xb8dddc: stur            x0, [fp, #-0x10]
    // 0xb8dde0: r0 = MaterialPageRoute()
    //     0xb8dde0: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb8dde4: mov             x1, x0
    // 0xb8dde8: ldur            x2, [fp, #-0x10]
    // 0xb8ddec: stur            x0, [fp, #-0x10]
    // 0xb8ddf0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb8ddf0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb8ddf4: r0 = MaterialPageRoute()
    //     0xb8ddf4: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb8ddf8: ldur            x16, [fp, #-8]
    // 0xb8ddfc: stp             x16, NULL, [SP, #8]
    // 0xb8de00: ldur            x16, [fp, #-0x10]
    // 0xb8de04: str             x16, [SP]
    // 0xb8de08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb8de08: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb8de0c: r0 = push()
    //     0xb8de0c: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb8de10: r0 = Null
    //     0xb8de10: mov             x0, NULL
    // 0xb8de14: LeaveFrame
    //     0xb8de14: mov             SP, fp
    //     0xb8de18: ldp             fp, lr, [SP], #0x10
    // 0xb8de1c: ret
    //     0xb8de1c: ret             
    // 0xb8de20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8de20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8de24: b               #0xb8dd2c
    // 0xb8de28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8de28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb8de2c, size: 0x1e0
    // 0xb8de2c: EnterFrame
    //     0xb8de2c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8de30: mov             fp, SP
    // 0xb8de34: AllocStack(0x38)
    //     0xb8de34: sub             SP, SP, #0x38
    // 0xb8de38: SetupParameters()
    //     0xb8de38: ldr             x0, [fp, #0x18]
    //     0xb8de3c: ldur            w3, [x0, #0x17]
    //     0xb8de40: add             x3, x3, HEAP, lsl #32
    //     0xb8de44: stur            x3, [fp, #-0x10]
    // 0xb8de48: CheckStackOverflow
    //     0xb8de48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8de4c: cmp             SP, x16
    //     0xb8de50: b.ls            #0xb8dffc
    // 0xb8de54: LoadField: r0 = r3->field_b
    //     0xb8de54: ldur            w0, [x3, #0xb]
    // 0xb8de58: DecompressPointer r0
    //     0xb8de58: add             x0, x0, HEAP, lsl #32
    // 0xb8de5c: stur            x0, [fp, #-8]
    // 0xb8de60: LoadField: r1 = r0->field_f
    //     0xb8de60: ldur            w1, [x0, #0xf]
    // 0xb8de64: DecompressPointer r1
    //     0xb8de64: add             x1, x1, HEAP, lsl #32
    // 0xb8de68: LoadField: r2 = r1->field_b
    //     0xb8de68: ldur            w2, [x1, #0xb]
    // 0xb8de6c: DecompressPointer r2
    //     0xb8de6c: add             x2, x2, HEAP, lsl #32
    // 0xb8de70: cmp             w2, NULL
    // 0xb8de74: b.eq            #0xb8e004
    // 0xb8de78: LoadField: r1 = r2->field_f
    //     0xb8de78: ldur            w1, [x2, #0xf]
    // 0xb8de7c: DecompressPointer r1
    //     0xb8de7c: add             x1, x1, HEAP, lsl #32
    // 0xb8de80: LoadField: r2 = r1->field_b
    //     0xb8de80: ldur            w2, [x1, #0xb]
    // 0xb8de84: DecompressPointer r2
    //     0xb8de84: add             x2, x2, HEAP, lsl #32
    // 0xb8de88: cmp             w2, NULL
    // 0xb8de8c: b.ne            #0xb8de98
    // 0xb8de90: r1 = Null
    //     0xb8de90: mov             x1, NULL
    // 0xb8de94: b               #0xb8debc
    // 0xb8de98: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb8de98: ldur            w1, [x2, #0x17]
    // 0xb8de9c: DecompressPointer r1
    //     0xb8de9c: add             x1, x1, HEAP, lsl #32
    // 0xb8dea0: cmp             w1, NULL
    // 0xb8dea4: b.ne            #0xb8deb0
    // 0xb8dea8: r1 = Null
    //     0xb8dea8: mov             x1, NULL
    // 0xb8deac: b               #0xb8debc
    // 0xb8deb0: LoadField: r2 = r1->field_f
    //     0xb8deb0: ldur            w2, [x1, #0xf]
    // 0xb8deb4: DecompressPointer r2
    //     0xb8deb4: add             x2, x2, HEAP, lsl #32
    // 0xb8deb8: mov             x1, x2
    // 0xb8debc: cmp             w1, NULL
    // 0xb8dec0: b.ne            #0xb8ded8
    // 0xb8dec4: r1 = <ReviewRatingEntity>
    //     0xb8dec4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0xb8dec8: ldr             x1, [x1, #0x150]
    // 0xb8decc: r2 = 0
    //     0xb8decc: movz            x2, #0
    // 0xb8ded0: r0 = _GrowableList()
    //     0xb8ded0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb8ded4: mov             x1, x0
    // 0xb8ded8: ldur            x2, [fp, #-0x10]
    // 0xb8dedc: ldur            x0, [fp, #-8]
    // 0xb8dee0: stur            x1, [fp, #-0x20]
    // 0xb8dee4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb8dee4: ldur            w3, [x2, #0x17]
    // 0xb8dee8: DecompressPointer r3
    //     0xb8dee8: add             x3, x3, HEAP, lsl #32
    // 0xb8deec: stur            x3, [fp, #-0x18]
    // 0xb8def0: str             x3, [SP]
    // 0xb8def4: r4 = 0
    //     0xb8def4: movz            x4, #0
    // 0xb8def8: ldr             x0, [SP]
    // 0xb8defc: r16 = UnlinkedCall_0x613b5c
    //     0xb8defc: add             x16, PP, #0x55, lsl #12  ; [pp+0x55448] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8df00: add             x16, x16, #0x448
    // 0xb8df04: ldp             x5, lr, [x16]
    // 0xb8df08: blr             lr
    // 0xb8df0c: stur            x0, [fp, #-0x28]
    // 0xb8df10: ldur            x16, [fp, #-0x18]
    // 0xb8df14: str             x16, [SP]
    // 0xb8df18: r4 = 0
    //     0xb8df18: movz            x4, #0
    // 0xb8df1c: ldr             x0, [SP]
    // 0xb8df20: r16 = UnlinkedCall_0x613b5c
    //     0xb8df20: add             x16, PP, #0x55, lsl #12  ; [pp+0x55458] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8df24: add             x16, x16, #0x458
    // 0xb8df28: ldp             x5, lr, [x16]
    // 0xb8df2c: blr             lr
    // 0xb8df30: mov             x3, x0
    // 0xb8df34: r2 = Null
    //     0xb8df34: mov             x2, NULL
    // 0xb8df38: r1 = Null
    //     0xb8df38: mov             x1, NULL
    // 0xb8df3c: stur            x3, [fp, #-0x18]
    // 0xb8df40: branchIfSmi(r0, 0xb8df68)
    //     0xb8df40: tbz             w0, #0, #0xb8df68
    // 0xb8df44: r4 = LoadClassIdInstr(r0)
    //     0xb8df44: ldur            x4, [x0, #-1]
    //     0xb8df48: ubfx            x4, x4, #0xc, #0x14
    // 0xb8df4c: sub             x4, x4, #0x3c
    // 0xb8df50: cmp             x4, #1
    // 0xb8df54: b.ls            #0xb8df68
    // 0xb8df58: r8 = int?
    //     0xb8df58: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0xb8df5c: r3 = Null
    //     0xb8df5c: add             x3, PP, #0x55, lsl #12  ; [pp+0x55468] Null
    //     0xb8df60: ldr             x3, [x3, #0x468]
    // 0xb8df64: r0 = int?()
    //     0xb8df64: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0xb8df68: ldur            x0, [fp, #-8]
    // 0xb8df6c: LoadField: r1 = r0->field_f
    //     0xb8df6c: ldur            w1, [x0, #0xf]
    // 0xb8df70: DecompressPointer r1
    //     0xb8df70: add             x1, x1, HEAP, lsl #32
    // 0xb8df74: LoadField: r0 = r1->field_b
    //     0xb8df74: ldur            w0, [x1, #0xb]
    // 0xb8df78: DecompressPointer r0
    //     0xb8df78: add             x0, x0, HEAP, lsl #32
    // 0xb8df7c: cmp             w0, NULL
    // 0xb8df80: b.eq            #0xb8e008
    // 0xb8df84: LoadField: r1 = r0->field_1f
    //     0xb8df84: ldur            w1, [x0, #0x1f]
    // 0xb8df88: DecompressPointer r1
    //     0xb8df88: add             x1, x1, HEAP, lsl #32
    // 0xb8df8c: stur            x1, [fp, #-8]
    // 0xb8df90: r0 = RatingReviewAllMediaOnTapImage()
    //     0xb8df90: bl              #0xa9808c  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0xb8df94: mov             x3, x0
    // 0xb8df98: ldur            x0, [fp, #-0x20]
    // 0xb8df9c: stur            x3, [fp, #-0x30]
    // 0xb8dfa0: StoreField: r3->field_f = r0
    //     0xb8dfa0: stur            w0, [x3, #0xf]
    // 0xb8dfa4: ldur            x0, [fp, #-0x28]
    // 0xb8dfa8: r1 = LoadInt32Instr(r0)
    //     0xb8dfa8: sbfx            x1, x0, #1, #0x1f
    //     0xb8dfac: tbz             w0, #0, #0xb8dfb4
    //     0xb8dfb0: ldur            x1, [x0, #7]
    // 0xb8dfb4: StoreField: r3->field_13 = r1
    //     0xb8dfb4: stur            x1, [x3, #0x13]
    // 0xb8dfb8: ldur            x0, [fp, #-0x18]
    // 0xb8dfbc: StoreField: r3->field_1b = r0
    //     0xb8dfbc: stur            w0, [x3, #0x1b]
    // 0xb8dfc0: r0 = "direct_image"
    //     0xb8dfc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xb8dfc4: ldr             x0, [x0, #0xc98]
    // 0xb8dfc8: StoreField: r3->field_b = r0
    //     0xb8dfc8: stur            w0, [x3, #0xb]
    // 0xb8dfcc: ldur            x2, [fp, #-0x10]
    // 0xb8dfd0: r1 = Function '<anonymous closure>':.
    //     0xb8dfd0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55478] AnonymousClosure: (0xa8d1a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xb8dfd4: ldr             x1, [x1, #0x478]
    // 0xb8dfd8: r0 = AllocateClosure()
    //     0xb8dfd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8dfdc: mov             x1, x0
    // 0xb8dfe0: ldur            x0, [fp, #-0x30]
    // 0xb8dfe4: StoreField: r0->field_1f = r1
    //     0xb8dfe4: stur            w1, [x0, #0x1f]
    // 0xb8dfe8: ldur            x1, [fp, #-8]
    // 0xb8dfec: StoreField: r0->field_23 = r1
    //     0xb8dfec: stur            w1, [x0, #0x23]
    // 0xb8dff0: LeaveFrame
    //     0xb8dff0: mov             SP, fp
    //     0xb8dff4: ldp             fp, lr, [SP], #0x10
    // 0xb8dff8: ret
    //     0xb8dff8: ret             
    // 0xb8dffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8dffc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e000: b               #0xb8de54
    // 0xb8e004: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e004: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8e008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8e00c, size: 0xe0
    // 0xb8e00c: EnterFrame
    //     0xb8e00c: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e010: mov             fp, SP
    // 0xb8e014: AllocStack(0x20)
    //     0xb8e014: sub             SP, SP, #0x20
    // 0xb8e018: SetupParameters()
    //     0xb8e018: ldr             x0, [fp, #0x10]
    //     0xb8e01c: ldur            w1, [x0, #0x17]
    //     0xb8e020: add             x1, x1, HEAP, lsl #32
    // 0xb8e024: CheckStackOverflow
    //     0xb8e024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e028: cmp             SP, x16
    //     0xb8e02c: b.ls            #0xb8e0e0
    // 0xb8e030: LoadField: r0 = r1->field_f
    //     0xb8e030: ldur            w0, [x1, #0xf]
    // 0xb8e034: DecompressPointer r0
    //     0xb8e034: add             x0, x0, HEAP, lsl #32
    // 0xb8e038: LoadField: r1 = r0->field_b
    //     0xb8e038: ldur            w1, [x0, #0xb]
    // 0xb8e03c: DecompressPointer r1
    //     0xb8e03c: add             x1, x1, HEAP, lsl #32
    // 0xb8e040: cmp             w1, NULL
    // 0xb8e044: b.eq            #0xb8e0e8
    // 0xb8e048: LoadField: r0 = r1->field_f
    //     0xb8e048: ldur            w0, [x1, #0xf]
    // 0xb8e04c: DecompressPointer r0
    //     0xb8e04c: add             x0, x0, HEAP, lsl #32
    // 0xb8e050: LoadField: r2 = r0->field_f
    //     0xb8e050: ldur            w2, [x0, #0xf]
    // 0xb8e054: DecompressPointer r2
    //     0xb8e054: add             x2, x2, HEAP, lsl #32
    // 0xb8e058: cmp             w2, NULL
    // 0xb8e05c: b.ne            #0xb8e068
    // 0xb8e060: r0 = Null
    //     0xb8e060: mov             x0, NULL
    // 0xb8e064: b               #0xb8e070
    // 0xb8e068: LoadField: r0 = r2->field_f
    //     0xb8e068: ldur            w0, [x2, #0xf]
    // 0xb8e06c: DecompressPointer r0
    //     0xb8e06c: add             x0, x0, HEAP, lsl #32
    // 0xb8e070: cmp             w0, NULL
    // 0xb8e074: b.ne            #0xb8e080
    // 0xb8e078: r0 = 0
    //     0xb8e078: movz            x0, #0
    // 0xb8e07c: b               #0xb8e090
    // 0xb8e080: r2 = LoadInt32Instr(r0)
    //     0xb8e080: sbfx            x2, x0, #1, #0x1f
    //     0xb8e084: tbz             w0, #0, #0xb8e08c
    //     0xb8e088: ldur            x2, [x0, #7]
    // 0xb8e08c: mov             x0, x2
    // 0xb8e090: cmp             x0, #3
    // 0xb8e094: b.lt            #0xb8e0d0
    // 0xb8e098: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8e098: ldur            w0, [x1, #0x17]
    // 0xb8e09c: DecompressPointer r0
    //     0xb8e09c: add             x0, x0, HEAP, lsl #32
    // 0xb8e0a0: r16 = "view_all"
    //     0xb8e0a0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xb8e0a4: ldr             x16, [x16, #0xba0]
    // 0xb8e0a8: stp             x16, x0, [SP, #0x10]
    // 0xb8e0ac: r16 = ""
    //     0xb8e0ac: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8e0b0: r30 = true
    //     0xb8e0b0: add             lr, NULL, #0x20  ; true
    // 0xb8e0b4: stp             lr, x16, [SP]
    // 0xb8e0b8: r4 = 0
    //     0xb8e0b8: movz            x4, #0
    // 0xb8e0bc: ldr             x0, [SP, #0x18]
    // 0xb8e0c0: r16 = UnlinkedCall_0x613b5c
    //     0xb8e0c0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55480] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8e0c4: add             x16, x16, #0x480
    // 0xb8e0c8: ldp             x5, lr, [x16]
    // 0xb8e0cc: blr             lr
    // 0xb8e0d0: r0 = Null
    //     0xb8e0d0: mov             x0, NULL
    // 0xb8e0d4: LeaveFrame
    //     0xb8e0d4: mov             SP, fp
    //     0xb8e0d8: ldp             fp, lr, [SP], #0x10
    // 0xb8e0dc: ret
    //     0xb8e0dc: ret             
    // 0xb8e0e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e0e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e0e4: b               #0xb8e030
    // 0xb8e0e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e0e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb8e0ec, size: 0xdc
    // 0xb8e0ec: EnterFrame
    //     0xb8e0ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb8e0f0: mov             fp, SP
    // 0xb8e0f4: AllocStack(0x38)
    //     0xb8e0f4: sub             SP, SP, #0x38
    // 0xb8e0f8: SetupParameters()
    //     0xb8e0f8: ldr             x0, [fp, #0x10]
    //     0xb8e0fc: ldur            w1, [x0, #0x17]
    //     0xb8e100: add             x1, x1, HEAP, lsl #32
    //     0xb8e104: stur            x1, [fp, #-8]
    // 0xb8e108: CheckStackOverflow
    //     0xb8e108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8e10c: cmp             SP, x16
    //     0xb8e110: b.ls            #0xb8e1bc
    // 0xb8e114: LoadField: r0 = r1->field_f
    //     0xb8e114: ldur            w0, [x1, #0xf]
    // 0xb8e118: DecompressPointer r0
    //     0xb8e118: add             x0, x0, HEAP, lsl #32
    // 0xb8e11c: LoadField: r2 = r0->field_b
    //     0xb8e11c: ldur            w2, [x0, #0xb]
    // 0xb8e120: DecompressPointer r2
    //     0xb8e120: add             x2, x2, HEAP, lsl #32
    // 0xb8e124: cmp             w2, NULL
    // 0xb8e128: b.eq            #0xb8e1c4
    // 0xb8e12c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb8e12c: ldur            w0, [x2, #0x17]
    // 0xb8e130: DecompressPointer r0
    //     0xb8e130: add             x0, x0, HEAP, lsl #32
    // 0xb8e134: r16 = "trusted_badge"
    //     0xb8e134: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0xb8e138: ldr             x16, [x16, #0xd58]
    // 0xb8e13c: stp             x16, x0, [SP, #0x10]
    // 0xb8e140: r16 = ""
    //     0xb8e140: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8e144: r30 = false
    //     0xb8e144: add             lr, NULL, #0x30  ; false
    // 0xb8e148: stp             lr, x16, [SP]
    // 0xb8e14c: r4 = 0
    //     0xb8e14c: movz            x4, #0
    // 0xb8e150: ldr             x0, [SP, #0x18]
    // 0xb8e154: r16 = UnlinkedCall_0x613b5c
    //     0xb8e154: add             x16, PP, #0x55, lsl #12  ; [pp+0x55490] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb8e158: add             x16, x16, #0x490
    // 0xb8e15c: ldp             x5, lr, [x16]
    // 0xb8e160: blr             lr
    // 0xb8e164: ldur            x0, [fp, #-8]
    // 0xb8e168: LoadField: r3 = r0->field_13
    //     0xb8e168: ldur            w3, [x0, #0x13]
    // 0xb8e16c: DecompressPointer r3
    //     0xb8e16c: add             x3, x3, HEAP, lsl #32
    // 0xb8e170: stur            x3, [fp, #-0x10]
    // 0xb8e174: r1 = Function '<anonymous closure>':.
    //     0xb8e174: add             x1, PP, #0x55, lsl #12  ; [pp+0x554a0] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xb8e178: ldr             x1, [x1, #0x4a0]
    // 0xb8e17c: r2 = Null
    //     0xb8e17c: mov             x2, NULL
    // 0xb8e180: r0 = AllocateClosure()
    //     0xb8e180: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8e184: stp             x0, NULL, [SP, #0x18]
    // 0xb8e188: ldur            x16, [fp, #-0x10]
    // 0xb8e18c: r30 = Instance_RoundedRectangleBorder
    //     0xb8e18c: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec08] Obj!RoundedRectangleBorder@d5abe1
    //     0xb8e190: ldr             lr, [lr, #0xc08]
    // 0xb8e194: stp             lr, x16, [SP, #8]
    // 0xb8e198: r16 = true
    //     0xb8e198: add             x16, NULL, #0x20  ; true
    // 0xb8e19c: str             x16, [SP]
    // 0xb8e1a0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0xb8e1a0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0xb8e1a4: ldr             x4, [x4, #0xd70]
    // 0xb8e1a8: r0 = showModalBottomSheet()
    //     0xb8e1a8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb8e1ac: r0 = Null
    //     0xb8e1ac: mov             x0, NULL
    // 0xb8e1b0: LeaveFrame
    //     0xb8e1b0: mov             SP, fp
    //     0xb8e1b4: ldp             fp, lr, [SP], #0x10
    // 0xb8e1b8: ret
    //     0xb8e1b8: ret             
    // 0xb8e1bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8e1bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8e1c0: b               #0xb8e114
    // 0xb8e1c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8e1c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4051, size: 0x24, field offset: 0xc
//   const constructor, 
class RatingReviewItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f9f8, size: 0x24
    // 0xc7f9f8: EnterFrame
    //     0xc7f9f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f9fc: mov             fp, SP
    // 0xc7fa00: mov             x0, x1
    // 0xc7fa04: r1 = <RatingReviewItemView>
    //     0xc7fa04: add             x1, PP, #0x48, lsl #12  ; [pp+0x48738] TypeArguments: <RatingReviewItemView>
    //     0xc7fa08: ldr             x1, [x1, #0x738]
    // 0xc7fa0c: r0 = _RatingReviewItemViewState()
    //     0xc7fa0c: bl              #0xc7fa1c  ; Allocate_RatingReviewItemViewStateStub -> _RatingReviewItemViewState (size=0x14)
    // 0xc7fa10: LeaveFrame
    //     0xc7fa10: mov             SP, fp
    //     0xc7fa14: ldp             fp, lr, [SP], #0x10
    // 0xc7fa18: ret
    //     0xc7fa18: ret             
  }
}
