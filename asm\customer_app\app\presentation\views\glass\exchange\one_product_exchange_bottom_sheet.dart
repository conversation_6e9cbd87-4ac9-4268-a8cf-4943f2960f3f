// lib: , url: package:customer_app/app/presentation/views/glass/exchange/one_product_exchange_bottom_sheet.dart

// class id: 1049395, size: 0x8
class :: {
}

// class id: 3342, size: 0x14, field offset: 0x14
class _OneProductExchangeBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb5cd48, size: 0x564
    // 0xb5cd48: EnterFrame
    //     0xb5cd48: stp             fp, lr, [SP, #-0x10]!
    //     0xb5cd4c: mov             fp, SP
    // 0xb5cd50: AllocStack(0x50)
    //     0xb5cd50: sub             SP, SP, #0x50
    // 0xb5cd54: SetupParameters(_OneProductExchangeBottomSheetState this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0xb5cd54: mov             x0, x1
    //     0xb5cd58: mov             x1, x2
    //     0xb5cd5c: stur            x2, [fp, #-8]
    // 0xb5cd60: CheckStackOverflow
    //     0xb5cd60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5cd64: cmp             SP, x16
    //     0xb5cd68: b.ls            #0xb5d2a4
    // 0xb5cd6c: r1 = 1
    //     0xb5cd6c: movz            x1, #0x1
    // 0xb5cd70: r0 = AllocateContext()
    //     0xb5cd70: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5cd74: ldur            x1, [fp, #-8]
    // 0xb5cd78: stur            x0, [fp, #-0x10]
    // 0xb5cd7c: StoreField: r0->field_f = r1
    //     0xb5cd7c: stur            w1, [x0, #0xf]
    // 0xb5cd80: r0 = of()
    //     0xb5cd80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5cd84: LoadField: r1 = r0->field_87
    //     0xb5cd84: ldur            w1, [x0, #0x87]
    // 0xb5cd88: DecompressPointer r1
    //     0xb5cd88: add             x1, x1, HEAP, lsl #32
    // 0xb5cd8c: LoadField: r0 = r1->field_7
    //     0xb5cd8c: ldur            w0, [x1, #7]
    // 0xb5cd90: DecompressPointer r0
    //     0xb5cd90: add             x0, x0, HEAP, lsl #32
    // 0xb5cd94: r16 = 16.000000
    //     0xb5cd94: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5cd98: ldr             x16, [x16, #0x188]
    // 0xb5cd9c: r30 = Instance_Color
    //     0xb5cd9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5cda0: stp             lr, x16, [SP]
    // 0xb5cda4: mov             x1, x0
    // 0xb5cda8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5cda8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5cdac: ldr             x4, [x4, #0xaa0]
    // 0xb5cdb0: r0 = copyWith()
    //     0xb5cdb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5cdb4: stur            x0, [fp, #-8]
    // 0xb5cdb8: r0 = Text()
    //     0xb5cdb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5cdbc: mov             x2, x0
    // 0xb5cdc0: r0 = "Only one product can be exchanged at a time!"
    //     0xb5cdc0: add             x0, PP, #0x53, lsl #12  ; [pp+0x53978] "Only one product can be exchanged at a time!"
    //     0xb5cdc4: ldr             x0, [x0, #0x978]
    // 0xb5cdc8: stur            x2, [fp, #-0x18]
    // 0xb5cdcc: StoreField: r2->field_b = r0
    //     0xb5cdcc: stur            w0, [x2, #0xb]
    // 0xb5cdd0: ldur            x0, [fp, #-8]
    // 0xb5cdd4: StoreField: r2->field_13 = r0
    //     0xb5cdd4: stur            w0, [x2, #0x13]
    // 0xb5cdd8: r1 = <FlexParentData>
    //     0xb5cdd8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5cddc: ldr             x1, [x1, #0xe00]
    // 0xb5cde0: r0 = Expanded()
    //     0xb5cde0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5cde4: mov             x1, x0
    // 0xb5cde8: r0 = 1
    //     0xb5cde8: movz            x0, #0x1
    // 0xb5cdec: stur            x1, [fp, #-8]
    // 0xb5cdf0: StoreField: r1->field_13 = r0
    //     0xb5cdf0: stur            x0, [x1, #0x13]
    // 0xb5cdf4: r0 = Instance_FlexFit
    //     0xb5cdf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb5cdf8: ldr             x0, [x0, #0xe08]
    // 0xb5cdfc: StoreField: r1->field_1b = r0
    //     0xb5cdfc: stur            w0, [x1, #0x1b]
    // 0xb5ce00: ldur            x0, [fp, #-0x18]
    // 0xb5ce04: StoreField: r1->field_b = r0
    //     0xb5ce04: stur            w0, [x1, #0xb]
    // 0xb5ce08: r0 = SvgPicture()
    //     0xb5ce08: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb5ce0c: mov             x1, x0
    // 0xb5ce10: r2 = "assets/images/x.svg"
    //     0xb5ce10: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb5ce14: ldr             x2, [x2, #0x5e8]
    // 0xb5ce18: stur            x0, [fp, #-0x18]
    // 0xb5ce1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb5ce1c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb5ce20: r0 = SvgPicture.asset()
    //     0xb5ce20: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb5ce24: r0 = InkWell()
    //     0xb5ce24: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5ce28: mov             x3, x0
    // 0xb5ce2c: ldur            x0, [fp, #-0x18]
    // 0xb5ce30: stur            x3, [fp, #-0x20]
    // 0xb5ce34: StoreField: r3->field_b = r0
    //     0xb5ce34: stur            w0, [x3, #0xb]
    // 0xb5ce38: ldur            x2, [fp, #-0x10]
    // 0xb5ce3c: r1 = Function '<anonymous closure>':.
    //     0xb5ce3c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fb0] AnonymousClosure: (0x99db20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_showFreeGiftDialog (0x99e89c)
    //     0xb5ce40: ldr             x1, [x1, #0xfb0]
    // 0xb5ce44: r0 = AllocateClosure()
    //     0xb5ce44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ce48: mov             x1, x0
    // 0xb5ce4c: ldur            x0, [fp, #-0x20]
    // 0xb5ce50: StoreField: r0->field_f = r1
    //     0xb5ce50: stur            w1, [x0, #0xf]
    // 0xb5ce54: r3 = true
    //     0xb5ce54: add             x3, NULL, #0x20  ; true
    // 0xb5ce58: StoreField: r0->field_43 = r3
    //     0xb5ce58: stur            w3, [x0, #0x43]
    // 0xb5ce5c: r1 = Instance_BoxShape
    //     0xb5ce5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5ce60: ldr             x1, [x1, #0x80]
    // 0xb5ce64: StoreField: r0->field_47 = r1
    //     0xb5ce64: stur            w1, [x0, #0x47]
    // 0xb5ce68: StoreField: r0->field_6f = r3
    //     0xb5ce68: stur            w3, [x0, #0x6f]
    // 0xb5ce6c: r4 = false
    //     0xb5ce6c: add             x4, NULL, #0x30  ; false
    // 0xb5ce70: StoreField: r0->field_73 = r4
    //     0xb5ce70: stur            w4, [x0, #0x73]
    // 0xb5ce74: StoreField: r0->field_83 = r3
    //     0xb5ce74: stur            w3, [x0, #0x83]
    // 0xb5ce78: StoreField: r0->field_7b = r4
    //     0xb5ce78: stur            w4, [x0, #0x7b]
    // 0xb5ce7c: r1 = Null
    //     0xb5ce7c: mov             x1, NULL
    // 0xb5ce80: r2 = 4
    //     0xb5ce80: movz            x2, #0x4
    // 0xb5ce84: r0 = AllocateArray()
    //     0xb5ce84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5ce88: mov             x2, x0
    // 0xb5ce8c: ldur            x0, [fp, #-8]
    // 0xb5ce90: stur            x2, [fp, #-0x18]
    // 0xb5ce94: StoreField: r2->field_f = r0
    //     0xb5ce94: stur            w0, [x2, #0xf]
    // 0xb5ce98: ldur            x0, [fp, #-0x20]
    // 0xb5ce9c: StoreField: r2->field_13 = r0
    //     0xb5ce9c: stur            w0, [x2, #0x13]
    // 0xb5cea0: r1 = <Widget>
    //     0xb5cea0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5cea4: r0 = AllocateGrowableArray()
    //     0xb5cea4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5cea8: mov             x1, x0
    // 0xb5ceac: ldur            x0, [fp, #-0x18]
    // 0xb5ceb0: stur            x1, [fp, #-8]
    // 0xb5ceb4: StoreField: r1->field_f = r0
    //     0xb5ceb4: stur            w0, [x1, #0xf]
    // 0xb5ceb8: r0 = 4
    //     0xb5ceb8: movz            x0, #0x4
    // 0xb5cebc: StoreField: r1->field_b = r0
    //     0xb5cebc: stur            w0, [x1, #0xb]
    // 0xb5cec0: r0 = Row()
    //     0xb5cec0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5cec4: mov             x2, x0
    // 0xb5cec8: r0 = Instance_Axis
    //     0xb5cec8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5cecc: stur            x2, [fp, #-0x18]
    // 0xb5ced0: StoreField: r2->field_f = r0
    //     0xb5ced0: stur            w0, [x2, #0xf]
    // 0xb5ced4: r0 = Instance_MainAxisAlignment
    //     0xb5ced4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5ced8: ldr             x0, [x0, #0xa08]
    // 0xb5cedc: StoreField: r2->field_13 = r0
    //     0xb5cedc: stur            w0, [x2, #0x13]
    // 0xb5cee0: r3 = Instance_MainAxisSize
    //     0xb5cee0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5cee4: ldr             x3, [x3, #0xa10]
    // 0xb5cee8: ArrayStore: r2[0] = r3  ; List_4
    //     0xb5cee8: stur            w3, [x2, #0x17]
    // 0xb5ceec: r1 = Instance_CrossAxisAlignment
    //     0xb5ceec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5cef0: ldr             x1, [x1, #0xa18]
    // 0xb5cef4: StoreField: r2->field_1b = r1
    //     0xb5cef4: stur            w1, [x2, #0x1b]
    // 0xb5cef8: r4 = Instance_VerticalDirection
    //     0xb5cef8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5cefc: ldr             x4, [x4, #0xa20]
    // 0xb5cf00: StoreField: r2->field_23 = r4
    //     0xb5cf00: stur            w4, [x2, #0x23]
    // 0xb5cf04: r5 = Instance_Clip
    //     0xb5cf04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5cf08: ldr             x5, [x5, #0x38]
    // 0xb5cf0c: StoreField: r2->field_2b = r5
    //     0xb5cf0c: stur            w5, [x2, #0x2b]
    // 0xb5cf10: StoreField: r2->field_2f = rZR
    //     0xb5cf10: stur            xzr, [x2, #0x2f]
    // 0xb5cf14: ldur            x1, [fp, #-8]
    // 0xb5cf18: StoreField: r2->field_b = r1
    //     0xb5cf18: stur            w1, [x2, #0xb]
    // 0xb5cf1c: ldur            x6, [fp, #-0x10]
    // 0xb5cf20: LoadField: r1 = r6->field_f
    //     0xb5cf20: ldur            w1, [x6, #0xf]
    // 0xb5cf24: DecompressPointer r1
    //     0xb5cf24: add             x1, x1, HEAP, lsl #32
    // 0xb5cf28: r0 = of()
    //     0xb5cf28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5cf2c: LoadField: r1 = r0->field_87
    //     0xb5cf2c: ldur            w1, [x0, #0x87]
    // 0xb5cf30: DecompressPointer r1
    //     0xb5cf30: add             x1, x1, HEAP, lsl #32
    // 0xb5cf34: LoadField: r0 = r1->field_2b
    //     0xb5cf34: ldur            w0, [x1, #0x2b]
    // 0xb5cf38: DecompressPointer r0
    //     0xb5cf38: add             x0, x0, HEAP, lsl #32
    // 0xb5cf3c: stur            x0, [fp, #-8]
    // 0xb5cf40: r1 = Instance_Color
    //     0xb5cf40: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5cf44: d0 = 0.700000
    //     0xb5cf44: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5cf48: ldr             d0, [x17, #0xf48]
    // 0xb5cf4c: r0 = withOpacity()
    //     0xb5cf4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb5cf50: r16 = 14.000000
    //     0xb5cf50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5cf54: ldr             x16, [x16, #0x1d8]
    // 0xb5cf58: stp             x0, x16, [SP]
    // 0xb5cf5c: ldur            x1, [fp, #-8]
    // 0xb5cf60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5cf60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5cf64: ldr             x4, [x4, #0xaa0]
    // 0xb5cf68: r0 = copyWith()
    //     0xb5cf68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5cf6c: stur            x0, [fp, #-8]
    // 0xb5cf70: r0 = Text()
    //     0xb5cf70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5cf74: mov             x1, x0
    // 0xb5cf78: r0 = "One product is already up for exchange. To exchange this product, please exchange the other item first"
    //     0xb5cf78: add             x0, PP, #0x53, lsl #12  ; [pp+0x53988] "One product is already up for exchange. To exchange this product, please exchange the other item first"
    //     0xb5cf7c: ldr             x0, [x0, #0x988]
    // 0xb5cf80: stur            x1, [fp, #-0x20]
    // 0xb5cf84: StoreField: r1->field_b = r0
    //     0xb5cf84: stur            w0, [x1, #0xb]
    // 0xb5cf88: ldur            x0, [fp, #-8]
    // 0xb5cf8c: StoreField: r1->field_13 = r0
    //     0xb5cf8c: stur            w0, [x1, #0x13]
    // 0xb5cf90: r16 = <EdgeInsets>
    //     0xb5cf90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb5cf94: ldr             x16, [x16, #0xda0]
    // 0xb5cf98: r30 = Instance_EdgeInsets
    //     0xb5cf98: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5cf9c: ldr             lr, [lr, #0x1f0]
    // 0xb5cfa0: stp             lr, x16, [SP]
    // 0xb5cfa4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5cfa4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5cfa8: r0 = all()
    //     0xb5cfa8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5cfac: mov             x2, x0
    // 0xb5cfb0: ldur            x0, [fp, #-0x10]
    // 0xb5cfb4: stur            x2, [fp, #-8]
    // 0xb5cfb8: LoadField: r1 = r0->field_f
    //     0xb5cfb8: ldur            w1, [x0, #0xf]
    // 0xb5cfbc: DecompressPointer r1
    //     0xb5cfbc: add             x1, x1, HEAP, lsl #32
    // 0xb5cfc0: r0 = of()
    //     0xb5cfc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5cfc4: LoadField: r1 = r0->field_5b
    //     0xb5cfc4: ldur            w1, [x0, #0x5b]
    // 0xb5cfc8: DecompressPointer r1
    //     0xb5cfc8: add             x1, x1, HEAP, lsl #32
    // 0xb5cfcc: r16 = <Color>
    //     0xb5cfcc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb5cfd0: ldr             x16, [x16, #0xf80]
    // 0xb5cfd4: stp             x1, x16, [SP]
    // 0xb5cfd8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5cfd8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5cfdc: r0 = all()
    //     0xb5cfdc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5cfe0: stur            x0, [fp, #-0x28]
    // 0xb5cfe4: r0 = Radius()
    //     0xb5cfe4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5cfe8: d0 = 20.000000
    //     0xb5cfe8: fmov            d0, #20.00000000
    // 0xb5cfec: stur            x0, [fp, #-0x30]
    // 0xb5cff0: StoreField: r0->field_7 = d0
    //     0xb5cff0: stur            d0, [x0, #7]
    // 0xb5cff4: StoreField: r0->field_f = d0
    //     0xb5cff4: stur            d0, [x0, #0xf]
    // 0xb5cff8: r0 = BorderRadius()
    //     0xb5cff8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb5cffc: mov             x1, x0
    // 0xb5d000: ldur            x0, [fp, #-0x30]
    // 0xb5d004: stur            x1, [fp, #-0x38]
    // 0xb5d008: StoreField: r1->field_7 = r0
    //     0xb5d008: stur            w0, [x1, #7]
    // 0xb5d00c: StoreField: r1->field_b = r0
    //     0xb5d00c: stur            w0, [x1, #0xb]
    // 0xb5d010: StoreField: r1->field_f = r0
    //     0xb5d010: stur            w0, [x1, #0xf]
    // 0xb5d014: StoreField: r1->field_13 = r0
    //     0xb5d014: stur            w0, [x1, #0x13]
    // 0xb5d018: r0 = RoundedRectangleBorder()
    //     0xb5d018: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb5d01c: mov             x1, x0
    // 0xb5d020: ldur            x0, [fp, #-0x38]
    // 0xb5d024: StoreField: r1->field_b = r0
    //     0xb5d024: stur            w0, [x1, #0xb]
    // 0xb5d028: r0 = Instance_BorderSide
    //     0xb5d028: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb5d02c: ldr             x0, [x0, #0xe20]
    // 0xb5d030: StoreField: r1->field_7 = r0
    //     0xb5d030: stur            w0, [x1, #7]
    // 0xb5d034: r16 = <RoundedRectangleBorder>
    //     0xb5d034: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb5d038: ldr             x16, [x16, #0xf78]
    // 0xb5d03c: stp             x1, x16, [SP]
    // 0xb5d040: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5d040: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5d044: r0 = all()
    //     0xb5d044: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5d048: stur            x0, [fp, #-0x30]
    // 0xb5d04c: r0 = ButtonStyle()
    //     0xb5d04c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb5d050: mov             x1, x0
    // 0xb5d054: ldur            x0, [fp, #-0x28]
    // 0xb5d058: stur            x1, [fp, #-0x38]
    // 0xb5d05c: StoreField: r1->field_b = r0
    //     0xb5d05c: stur            w0, [x1, #0xb]
    // 0xb5d060: ldur            x0, [fp, #-8]
    // 0xb5d064: StoreField: r1->field_23 = r0
    //     0xb5d064: stur            w0, [x1, #0x23]
    // 0xb5d068: ldur            x0, [fp, #-0x30]
    // 0xb5d06c: StoreField: r1->field_43 = r0
    //     0xb5d06c: stur            w0, [x1, #0x43]
    // 0xb5d070: r0 = TextButtonThemeData()
    //     0xb5d070: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb5d074: mov             x2, x0
    // 0xb5d078: ldur            x0, [fp, #-0x38]
    // 0xb5d07c: stur            x2, [fp, #-8]
    // 0xb5d080: StoreField: r2->field_7 = r0
    //     0xb5d080: stur            w0, [x2, #7]
    // 0xb5d084: r1 = "Got it!"
    //     0xb5d084: add             x1, PP, #0x53, lsl #12  ; [pp+0x53990] "Got it!"
    //     0xb5d088: ldr             x1, [x1, #0x990]
    // 0xb5d08c: r0 = capitalizeFirstWord()
    //     0xb5d08c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb5d090: mov             x2, x0
    // 0xb5d094: ldur            x0, [fp, #-0x10]
    // 0xb5d098: stur            x2, [fp, #-0x28]
    // 0xb5d09c: LoadField: r1 = r0->field_f
    //     0xb5d09c: ldur            w1, [x0, #0xf]
    // 0xb5d0a0: DecompressPointer r1
    //     0xb5d0a0: add             x1, x1, HEAP, lsl #32
    // 0xb5d0a4: r0 = of()
    //     0xb5d0a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5d0a8: LoadField: r1 = r0->field_87
    //     0xb5d0a8: ldur            w1, [x0, #0x87]
    // 0xb5d0ac: DecompressPointer r1
    //     0xb5d0ac: add             x1, x1, HEAP, lsl #32
    // 0xb5d0b0: LoadField: r0 = r1->field_7
    //     0xb5d0b0: ldur            w0, [x1, #7]
    // 0xb5d0b4: DecompressPointer r0
    //     0xb5d0b4: add             x0, x0, HEAP, lsl #32
    // 0xb5d0b8: r16 = 16.000000
    //     0xb5d0b8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5d0bc: ldr             x16, [x16, #0x188]
    // 0xb5d0c0: r30 = Instance_Color
    //     0xb5d0c0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5d0c4: stp             lr, x16, [SP]
    // 0xb5d0c8: mov             x1, x0
    // 0xb5d0cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5d0cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5d0d0: ldr             x4, [x4, #0xaa0]
    // 0xb5d0d4: r0 = copyWith()
    //     0xb5d0d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5d0d8: stur            x0, [fp, #-0x10]
    // 0xb5d0dc: r0 = Text()
    //     0xb5d0dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5d0e0: mov             x3, x0
    // 0xb5d0e4: ldur            x0, [fp, #-0x28]
    // 0xb5d0e8: stur            x3, [fp, #-0x30]
    // 0xb5d0ec: StoreField: r3->field_b = r0
    //     0xb5d0ec: stur            w0, [x3, #0xb]
    // 0xb5d0f0: ldur            x0, [fp, #-0x10]
    // 0xb5d0f4: StoreField: r3->field_13 = r0
    //     0xb5d0f4: stur            w0, [x3, #0x13]
    // 0xb5d0f8: r1 = Function '<anonymous closure>':.
    //     0xb5d0f8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fb8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5d0fc: ldr             x1, [x1, #0xfb8]
    // 0xb5d100: r2 = Null
    //     0xb5d100: mov             x2, NULL
    // 0xb5d104: r0 = AllocateClosure()
    //     0xb5d104: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5d108: stur            x0, [fp, #-0x10]
    // 0xb5d10c: r0 = TextButton()
    //     0xb5d10c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb5d110: mov             x1, x0
    // 0xb5d114: ldur            x0, [fp, #-0x10]
    // 0xb5d118: stur            x1, [fp, #-0x28]
    // 0xb5d11c: StoreField: r1->field_b = r0
    //     0xb5d11c: stur            w0, [x1, #0xb]
    // 0xb5d120: r0 = false
    //     0xb5d120: add             x0, NULL, #0x30  ; false
    // 0xb5d124: StoreField: r1->field_27 = r0
    //     0xb5d124: stur            w0, [x1, #0x27]
    // 0xb5d128: r2 = true
    //     0xb5d128: add             x2, NULL, #0x20  ; true
    // 0xb5d12c: StoreField: r1->field_2f = r2
    //     0xb5d12c: stur            w2, [x1, #0x2f]
    // 0xb5d130: ldur            x2, [fp, #-0x30]
    // 0xb5d134: StoreField: r1->field_37 = r2
    //     0xb5d134: stur            w2, [x1, #0x37]
    // 0xb5d138: r0 = TextButtonTheme()
    //     0xb5d138: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb5d13c: mov             x1, x0
    // 0xb5d140: ldur            x0, [fp, #-8]
    // 0xb5d144: stur            x1, [fp, #-0x10]
    // 0xb5d148: StoreField: r1->field_f = r0
    //     0xb5d148: stur            w0, [x1, #0xf]
    // 0xb5d14c: ldur            x0, [fp, #-0x28]
    // 0xb5d150: StoreField: r1->field_b = r0
    //     0xb5d150: stur            w0, [x1, #0xb]
    // 0xb5d154: r0 = SizedBox()
    //     0xb5d154: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb5d158: mov             x3, x0
    // 0xb5d15c: r0 = inf
    //     0xb5d15c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb5d160: ldr             x0, [x0, #0x9f8]
    // 0xb5d164: stur            x3, [fp, #-8]
    // 0xb5d168: StoreField: r3->field_f = r0
    //     0xb5d168: stur            w0, [x3, #0xf]
    // 0xb5d16c: ldur            x0, [fp, #-0x10]
    // 0xb5d170: StoreField: r3->field_b = r0
    //     0xb5d170: stur            w0, [x3, #0xb]
    // 0xb5d174: r1 = Null
    //     0xb5d174: mov             x1, NULL
    // 0xb5d178: r2 = 8
    //     0xb5d178: movz            x2, #0x8
    // 0xb5d17c: r0 = AllocateArray()
    //     0xb5d17c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5d180: mov             x2, x0
    // 0xb5d184: ldur            x0, [fp, #-0x18]
    // 0xb5d188: stur            x2, [fp, #-0x10]
    // 0xb5d18c: StoreField: r2->field_f = r0
    //     0xb5d18c: stur            w0, [x2, #0xf]
    // 0xb5d190: r16 = Instance_SizedBox
    //     0xb5d190: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb5d194: ldr             x16, [x16, #0x578]
    // 0xb5d198: StoreField: r2->field_13 = r16
    //     0xb5d198: stur            w16, [x2, #0x13]
    // 0xb5d19c: ldur            x0, [fp, #-0x20]
    // 0xb5d1a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5d1a0: stur            w0, [x2, #0x17]
    // 0xb5d1a4: ldur            x0, [fp, #-8]
    // 0xb5d1a8: StoreField: r2->field_1b = r0
    //     0xb5d1a8: stur            w0, [x2, #0x1b]
    // 0xb5d1ac: r1 = <Widget>
    //     0xb5d1ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5d1b0: r0 = AllocateGrowableArray()
    //     0xb5d1b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5d1b4: mov             x1, x0
    // 0xb5d1b8: ldur            x0, [fp, #-0x10]
    // 0xb5d1bc: stur            x1, [fp, #-8]
    // 0xb5d1c0: StoreField: r1->field_f = r0
    //     0xb5d1c0: stur            w0, [x1, #0xf]
    // 0xb5d1c4: r0 = 8
    //     0xb5d1c4: movz            x0, #0x8
    // 0xb5d1c8: StoreField: r1->field_b = r0
    //     0xb5d1c8: stur            w0, [x1, #0xb]
    // 0xb5d1cc: r0 = Column()
    //     0xb5d1cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5d1d0: mov             x1, x0
    // 0xb5d1d4: r0 = Instance_Axis
    //     0xb5d1d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5d1d8: stur            x1, [fp, #-0x10]
    // 0xb5d1dc: StoreField: r1->field_f = r0
    //     0xb5d1dc: stur            w0, [x1, #0xf]
    // 0xb5d1e0: r2 = Instance_MainAxisAlignment
    //     0xb5d1e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5d1e4: ldr             x2, [x2, #0xa08]
    // 0xb5d1e8: StoreField: r1->field_13 = r2
    //     0xb5d1e8: stur            w2, [x1, #0x13]
    // 0xb5d1ec: r2 = Instance_MainAxisSize
    //     0xb5d1ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5d1f0: ldr             x2, [x2, #0xa10]
    // 0xb5d1f4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb5d1f4: stur            w2, [x1, #0x17]
    // 0xb5d1f8: r2 = Instance_CrossAxisAlignment
    //     0xb5d1f8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5d1fc: ldr             x2, [x2, #0x890]
    // 0xb5d200: StoreField: r1->field_1b = r2
    //     0xb5d200: stur            w2, [x1, #0x1b]
    // 0xb5d204: r2 = Instance_VerticalDirection
    //     0xb5d204: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5d208: ldr             x2, [x2, #0xa20]
    // 0xb5d20c: StoreField: r1->field_23 = r2
    //     0xb5d20c: stur            w2, [x1, #0x23]
    // 0xb5d210: r2 = Instance_Clip
    //     0xb5d210: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5d214: ldr             x2, [x2, #0x38]
    // 0xb5d218: StoreField: r1->field_2b = r2
    //     0xb5d218: stur            w2, [x1, #0x2b]
    // 0xb5d21c: StoreField: r1->field_2f = rZR
    //     0xb5d21c: stur            xzr, [x1, #0x2f]
    // 0xb5d220: ldur            x2, [fp, #-8]
    // 0xb5d224: StoreField: r1->field_b = r2
    //     0xb5d224: stur            w2, [x1, #0xb]
    // 0xb5d228: r0 = Container()
    //     0xb5d228: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5d22c: stur            x0, [fp, #-8]
    // 0xb5d230: r16 = Instance_EdgeInsets
    //     0xb5d230: add             x16, PP, #0x55, lsl #12  ; [pp+0x55fc0] Obj!EdgeInsets@d58d91
    //     0xb5d234: ldr             x16, [x16, #0xfc0]
    // 0xb5d238: r30 = Instance_BoxDecoration
    //     0xb5d238: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec50] Obj!BoxDecoration@d64921
    //     0xb5d23c: ldr             lr, [lr, #0xc50]
    // 0xb5d240: stp             lr, x16, [SP, #8]
    // 0xb5d244: ldur            x16, [fp, #-0x10]
    // 0xb5d248: str             x16, [SP]
    // 0xb5d24c: mov             x1, x0
    // 0xb5d250: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb5d250: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb5d254: ldr             x4, [x4, #0x610]
    // 0xb5d258: r0 = Container()
    //     0xb5d258: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5d25c: r0 = SingleChildScrollView()
    //     0xb5d25c: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb5d260: r1 = Instance_Axis
    //     0xb5d260: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5d264: StoreField: r0->field_b = r1
    //     0xb5d264: stur            w1, [x0, #0xb]
    // 0xb5d268: r1 = false
    //     0xb5d268: add             x1, NULL, #0x30  ; false
    // 0xb5d26c: StoreField: r0->field_f = r1
    //     0xb5d26c: stur            w1, [x0, #0xf]
    // 0xb5d270: ldur            x1, [fp, #-8]
    // 0xb5d274: StoreField: r0->field_23 = r1
    //     0xb5d274: stur            w1, [x0, #0x23]
    // 0xb5d278: r1 = Instance_DragStartBehavior
    //     0xb5d278: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb5d27c: StoreField: r0->field_27 = r1
    //     0xb5d27c: stur            w1, [x0, #0x27]
    // 0xb5d280: r1 = Instance_Clip
    //     0xb5d280: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb5d284: ldr             x1, [x1, #0x7e0]
    // 0xb5d288: StoreField: r0->field_2b = r1
    //     0xb5d288: stur            w1, [x0, #0x2b]
    // 0xb5d28c: r1 = Instance_HitTestBehavior
    //     0xb5d28c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb5d290: ldr             x1, [x1, #0x288]
    // 0xb5d294: StoreField: r0->field_2f = r1
    //     0xb5d294: stur            w1, [x0, #0x2f]
    // 0xb5d298: LeaveFrame
    //     0xb5d298: mov             SP, fp
    //     0xb5d29c: ldp             fp, lr, [SP], #0x10
    // 0xb5d2a0: ret
    //     0xb5d2a0: ret             
    // 0xb5d2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5d2a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5d2a8: b               #0xb5cd6c
  }
}

// class id: 4082, size: 0xc, field offset: 0xc
//   const constructor, 
class OneProductExchangeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f1e4, size: 0x24
    // 0xc7f1e4: EnterFrame
    //     0xc7f1e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f1e8: mov             fp, SP
    // 0xc7f1ec: mov             x0, x1
    // 0xc7f1f0: r1 = <OneProductExchangeBottomSheet>
    //     0xc7f1f0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48850] TypeArguments: <OneProductExchangeBottomSheet>
    //     0xc7f1f4: ldr             x1, [x1, #0x850]
    // 0xc7f1f8: r0 = _OneProductExchangeBottomSheetState()
    //     0xc7f1f8: bl              #0xc7f208  ; Allocate_OneProductExchangeBottomSheetStateStub -> _OneProductExchangeBottomSheetState (size=0x14)
    // 0xc7f1fc: LeaveFrame
    //     0xc7f1fc: mov             SP, fp
    //     0xc7f200: ldp             fp, lr, [SP], #0x10
    // 0xc7f204: ret
    //     0xc7f204: ret             
  }
}
