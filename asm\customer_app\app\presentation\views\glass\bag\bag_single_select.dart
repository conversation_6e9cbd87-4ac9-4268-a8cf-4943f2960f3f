// lib: , url: package:customer_app/app/presentation/views/glass/bag/bag_single_select.dart

// class id: 1049343, size: 0x8
class :: {
}

// class id: 3381, size: 0x1c, field offset: 0x14
class _BagSingleSelectState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customisedValue; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xb2f720, size: 0x8ac
    // 0xb2f720: EnterFrame
    //     0xb2f720: stp             fp, lr, [SP, #-0x10]!
    //     0xb2f724: mov             fp, SP
    // 0xb2f728: AllocStack(0x50)
    //     0xb2f728: sub             SP, SP, #0x50
    // 0xb2f72c: SetupParameters(_BagSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb2f72c: mov             x0, x1
    //     0xb2f730: stur            x1, [fp, #-8]
    //     0xb2f734: mov             x1, x2
    //     0xb2f738: stur            x2, [fp, #-0x10]
    // 0xb2f73c: CheckStackOverflow
    //     0xb2f73c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2f740: cmp             SP, x16
    //     0xb2f744: b.ls            #0xb2ff98
    // 0xb2f748: LoadField: r2 = r0->field_b
    //     0xb2f748: ldur            w2, [x0, #0xb]
    // 0xb2f74c: DecompressPointer r2
    //     0xb2f74c: add             x2, x2, HEAP, lsl #32
    // 0xb2f750: cmp             w2, NULL
    // 0xb2f754: b.eq            #0xb2ffa0
    // 0xb2f758: LoadField: r3 = r2->field_b
    //     0xb2f758: ldur            w3, [x2, #0xb]
    // 0xb2f75c: DecompressPointer r3
    //     0xb2f75c: add             x3, x3, HEAP, lsl #32
    // 0xb2f760: cmp             w3, NULL
    // 0xb2f764: b.ne            #0xb2f770
    // 0xb2f768: r2 = Null
    //     0xb2f768: mov             x2, NULL
    // 0xb2f76c: b               #0xb2f79c
    // 0xb2f770: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb2f770: ldur            w2, [x3, #0x17]
    // 0xb2f774: DecompressPointer r2
    //     0xb2f774: add             x2, x2, HEAP, lsl #32
    // 0xb2f778: cmp             w2, NULL
    // 0xb2f77c: b.ne            #0xb2f788
    // 0xb2f780: r2 = Null
    //     0xb2f780: mov             x2, NULL
    // 0xb2f784: b               #0xb2f79c
    // 0xb2f788: LoadField: r3 = r2->field_7
    //     0xb2f788: ldur            w3, [x2, #7]
    // 0xb2f78c: cbnz            w3, #0xb2f798
    // 0xb2f790: r2 = false
    //     0xb2f790: add             x2, NULL, #0x30  ; false
    // 0xb2f794: b               #0xb2f79c
    // 0xb2f798: r2 = true
    //     0xb2f798: add             x2, NULL, #0x20  ; true
    // 0xb2f79c: cmp             w2, NULL
    // 0xb2f7a0: b.ne            #0xb2f7ec
    // 0xb2f7a4: mov             x3, x0
    // 0xb2f7a8: r4 = 6
    //     0xb2f7a8: movz            x4, #0x6
    // 0xb2f7ac: r2 = 4
    //     0xb2f7ac: movz            x2, #0x4
    // 0xb2f7b0: r7 = Instance_CrossAxisAlignment
    //     0xb2f7b0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2f7b4: ldr             x7, [x7, #0xa18]
    // 0xb2f7b8: r5 = Instance_MainAxisAlignment
    //     0xb2f7b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2f7bc: ldr             x5, [x5, #0xa08]
    // 0xb2f7c0: r6 = Instance_MainAxisSize
    //     0xb2f7c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2f7c4: ldr             x6, [x6, #0xa10]
    // 0xb2f7c8: r1 = Instance_Axis
    //     0xb2f7c8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2f7cc: r8 = Instance_VerticalDirection
    //     0xb2f7cc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2f7d0: ldr             x8, [x8, #0xa20]
    // 0xb2f7d4: r0 = Instance__DeferringMouseCursor
    //     0xb2f7d4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f7d8: r9 = Instance_Clip
    //     0xb2f7d8: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2f7dc: ldr             x9, [x9, #0x38]
    // 0xb2f7e0: d1 = 0.500000
    //     0xb2f7e0: fmov            d1, #0.50000000
    // 0xb2f7e4: d0 = inf
    //     0xb2f7e4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2f7e8: b               #0xb2fbe4
    // 0xb2f7ec: tbnz            w2, #4, #0xb2fba0
    // 0xb2f7f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2f7f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2f7f4: ldr             x0, [x0, #0x1c80]
    //     0xb2f7f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2f7fc: cmp             w0, w16
    //     0xb2f800: b.ne            #0xb2f80c
    //     0xb2f804: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2f808: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2f80c: r0 = GetNavigation.size()
    //     0xb2f80c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2f810: LoadField: d0 = r0->field_7
    //     0xb2f810: ldur            d0, [x0, #7]
    // 0xb2f814: d1 = 0.500000
    //     0xb2f814: fmov            d1, #0.50000000
    // 0xb2f818: fmul            d2, d0, d1
    // 0xb2f81c: stur            d2, [fp, #-0x40]
    // 0xb2f820: r0 = BoxConstraints()
    //     0xb2f820: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb2f824: stur            x0, [fp, #-0x20]
    // 0xb2f828: StoreField: r0->field_7 = rZR
    //     0xb2f828: stur            xzr, [x0, #7]
    // 0xb2f82c: ldur            d0, [fp, #-0x40]
    // 0xb2f830: StoreField: r0->field_f = d0
    //     0xb2f830: stur            d0, [x0, #0xf]
    // 0xb2f834: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb2f834: stur            xzr, [x0, #0x17]
    // 0xb2f838: d0 = inf
    //     0xb2f838: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2f83c: StoreField: r0->field_1f = d0
    //     0xb2f83c: stur            d0, [x0, #0x1f]
    // 0xb2f840: ldur            x2, [fp, #-8]
    // 0xb2f844: LoadField: r1 = r2->field_b
    //     0xb2f844: ldur            w1, [x2, #0xb]
    // 0xb2f848: DecompressPointer r1
    //     0xb2f848: add             x1, x1, HEAP, lsl #32
    // 0xb2f84c: cmp             w1, NULL
    // 0xb2f850: b.eq            #0xb2ffa4
    // 0xb2f854: LoadField: r3 = r1->field_b
    //     0xb2f854: ldur            w3, [x1, #0xb]
    // 0xb2f858: DecompressPointer r3
    //     0xb2f858: add             x3, x3, HEAP, lsl #32
    // 0xb2f85c: cmp             w3, NULL
    // 0xb2f860: b.ne            #0xb2f86c
    // 0xb2f864: r1 = Null
    //     0xb2f864: mov             x1, NULL
    // 0xb2f868: b               #0xb2f874
    // 0xb2f86c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb2f86c: ldur            w1, [x3, #0x17]
    // 0xb2f870: DecompressPointer r1
    //     0xb2f870: add             x1, x1, HEAP, lsl #32
    // 0xb2f874: cmp             w1, NULL
    // 0xb2f878: b.ne            #0xb2f884
    // 0xb2f87c: r3 = ""
    //     0xb2f87c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2f880: b               #0xb2f888
    // 0xb2f884: mov             x3, x1
    // 0xb2f888: ldur            x1, [fp, #-0x10]
    // 0xb2f88c: stur            x3, [fp, #-0x18]
    // 0xb2f890: r0 = of()
    //     0xb2f890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f894: LoadField: r1 = r0->field_87
    //     0xb2f894: ldur            w1, [x0, #0x87]
    // 0xb2f898: DecompressPointer r1
    //     0xb2f898: add             x1, x1, HEAP, lsl #32
    // 0xb2f89c: LoadField: r0 = r1->field_7
    //     0xb2f89c: ldur            w0, [x1, #7]
    // 0xb2f8a0: DecompressPointer r0
    //     0xb2f8a0: add             x0, x0, HEAP, lsl #32
    // 0xb2f8a4: r16 = 16.000000
    //     0xb2f8a4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2f8a8: ldr             x16, [x16, #0x188]
    // 0xb2f8ac: r30 = Instance_Color
    //     0xb2f8ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f8b0: stp             lr, x16, [SP]
    // 0xb2f8b4: mov             x1, x0
    // 0xb2f8b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f8b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f8bc: ldr             x4, [x4, #0xaa0]
    // 0xb2f8c0: r0 = copyWith()
    //     0xb2f8c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f8c4: stur            x0, [fp, #-0x28]
    // 0xb2f8c8: r0 = TextSpan()
    //     0xb2f8c8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f8cc: mov             x3, x0
    // 0xb2f8d0: ldur            x0, [fp, #-0x18]
    // 0xb2f8d4: stur            x3, [fp, #-0x30]
    // 0xb2f8d8: StoreField: r3->field_b = r0
    //     0xb2f8d8: stur            w0, [x3, #0xb]
    // 0xb2f8dc: r0 = Instance__DeferringMouseCursor
    //     0xb2f8dc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f8e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2f8e0: stur            w0, [x3, #0x17]
    // 0xb2f8e4: ldur            x1, [fp, #-0x28]
    // 0xb2f8e8: StoreField: r3->field_7 = r1
    //     0xb2f8e8: stur            w1, [x3, #7]
    // 0xb2f8ec: r1 = Null
    //     0xb2f8ec: mov             x1, NULL
    // 0xb2f8f0: r2 = 6
    //     0xb2f8f0: movz            x2, #0x6
    // 0xb2f8f4: r0 = AllocateArray()
    //     0xb2f8f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f8f8: r16 = " : "
    //     0xb2f8f8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb2f8fc: ldr             x16, [x16, #0x680]
    // 0xb2f900: StoreField: r0->field_f = r16
    //     0xb2f900: stur            w16, [x0, #0xf]
    // 0xb2f904: ldur            x1, [fp, #-8]
    // 0xb2f908: LoadField: r2 = r1->field_13
    //     0xb2f908: ldur            w2, [x1, #0x13]
    // 0xb2f90c: DecompressPointer r2
    //     0xb2f90c: add             x2, x2, HEAP, lsl #32
    // 0xb2f910: r16 = Sentinel
    //     0xb2f910: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb2f914: cmp             w2, w16
    // 0xb2f918: b.eq            #0xb2ffa8
    // 0xb2f91c: StoreField: r0->field_13 = r2
    //     0xb2f91c: stur            w2, [x0, #0x13]
    // 0xb2f920: r16 = " "
    //     0xb2f920: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2f924: ArrayStore: r0[0] = r16  ; List_4
    //     0xb2f924: stur            w16, [x0, #0x17]
    // 0xb2f928: str             x0, [SP]
    // 0xb2f92c: r0 = _interpolate()
    //     0xb2f92c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2f930: ldur            x1, [fp, #-0x10]
    // 0xb2f934: stur            x0, [fp, #-0x18]
    // 0xb2f938: r0 = of()
    //     0xb2f938: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2f93c: LoadField: r1 = r0->field_87
    //     0xb2f93c: ldur            w1, [x0, #0x87]
    // 0xb2f940: DecompressPointer r1
    //     0xb2f940: add             x1, x1, HEAP, lsl #32
    // 0xb2f944: LoadField: r0 = r1->field_2b
    //     0xb2f944: ldur            w0, [x1, #0x2b]
    // 0xb2f948: DecompressPointer r0
    //     0xb2f948: add             x0, x0, HEAP, lsl #32
    // 0xb2f94c: r16 = 14.000000
    //     0xb2f94c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2f950: ldr             x16, [x16, #0x1d8]
    // 0xb2f954: r30 = Instance_Color
    //     0xb2f954: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2f958: stp             lr, x16, [SP]
    // 0xb2f95c: mov             x1, x0
    // 0xb2f960: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2f960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2f964: ldr             x4, [x4, #0xaa0]
    // 0xb2f968: r0 = copyWith()
    //     0xb2f968: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2f96c: stur            x0, [fp, #-0x28]
    // 0xb2f970: r0 = TextSpan()
    //     0xb2f970: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f974: mov             x3, x0
    // 0xb2f978: ldur            x0, [fp, #-0x18]
    // 0xb2f97c: stur            x3, [fp, #-0x38]
    // 0xb2f980: StoreField: r3->field_b = r0
    //     0xb2f980: stur            w0, [x3, #0xb]
    // 0xb2f984: r0 = Instance__DeferringMouseCursor
    //     0xb2f984: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f988: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2f988: stur            w0, [x3, #0x17]
    // 0xb2f98c: ldur            x1, [fp, #-0x28]
    // 0xb2f990: StoreField: r3->field_7 = r1
    //     0xb2f990: stur            w1, [x3, #7]
    // 0xb2f994: r1 = Null
    //     0xb2f994: mov             x1, NULL
    // 0xb2f998: r2 = 4
    //     0xb2f998: movz            x2, #0x4
    // 0xb2f99c: r0 = AllocateArray()
    //     0xb2f99c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2f9a0: mov             x2, x0
    // 0xb2f9a4: ldur            x0, [fp, #-0x30]
    // 0xb2f9a8: stur            x2, [fp, #-0x18]
    // 0xb2f9ac: StoreField: r2->field_f = r0
    //     0xb2f9ac: stur            w0, [x2, #0xf]
    // 0xb2f9b0: ldur            x0, [fp, #-0x38]
    // 0xb2f9b4: StoreField: r2->field_13 = r0
    //     0xb2f9b4: stur            w0, [x2, #0x13]
    // 0xb2f9b8: r1 = <InlineSpan>
    //     0xb2f9b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2f9bc: ldr             x1, [x1, #0xe40]
    // 0xb2f9c0: r0 = AllocateGrowableArray()
    //     0xb2f9c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2f9c4: mov             x1, x0
    // 0xb2f9c8: ldur            x0, [fp, #-0x18]
    // 0xb2f9cc: stur            x1, [fp, #-0x28]
    // 0xb2f9d0: StoreField: r1->field_f = r0
    //     0xb2f9d0: stur            w0, [x1, #0xf]
    // 0xb2f9d4: r2 = 4
    //     0xb2f9d4: movz            x2, #0x4
    // 0xb2f9d8: StoreField: r1->field_b = r2
    //     0xb2f9d8: stur            w2, [x1, #0xb]
    // 0xb2f9dc: r0 = TextSpan()
    //     0xb2f9dc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2f9e0: mov             x1, x0
    // 0xb2f9e4: ldur            x0, [fp, #-0x28]
    // 0xb2f9e8: stur            x1, [fp, #-0x18]
    // 0xb2f9ec: StoreField: r1->field_f = r0
    //     0xb2f9ec: stur            w0, [x1, #0xf]
    // 0xb2f9f0: r0 = Instance__DeferringMouseCursor
    //     0xb2f9f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2f9f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2f9f4: stur            w0, [x1, #0x17]
    // 0xb2f9f8: r0 = RichText()
    //     0xb2f9f8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2f9fc: mov             x1, x0
    // 0xb2fa00: ldur            x2, [fp, #-0x18]
    // 0xb2fa04: stur            x0, [fp, #-0x18]
    // 0xb2fa08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2fa08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2fa0c: r0 = RichText()
    //     0xb2fa0c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2fa10: r0 = ConstrainedBox()
    //     0xb2fa10: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb2fa14: mov             x1, x0
    // 0xb2fa18: ldur            x0, [fp, #-0x20]
    // 0xb2fa1c: stur            x1, [fp, #-0x28]
    // 0xb2fa20: StoreField: r1->field_f = r0
    //     0xb2fa20: stur            w0, [x1, #0xf]
    // 0xb2fa24: ldur            x0, [fp, #-0x18]
    // 0xb2fa28: StoreField: r1->field_b = r0
    //     0xb2fa28: stur            w0, [x1, #0xb]
    // 0xb2fa2c: ldur            x3, [fp, #-8]
    // 0xb2fa30: LoadField: r0 = r3->field_b
    //     0xb2fa30: ldur            w0, [x3, #0xb]
    // 0xb2fa34: DecompressPointer r0
    //     0xb2fa34: add             x0, x0, HEAP, lsl #32
    // 0xb2fa38: cmp             w0, NULL
    // 0xb2fa3c: b.eq            #0xb2ffb4
    // 0xb2fa40: LoadField: r2 = r0->field_b
    //     0xb2fa40: ldur            w2, [x0, #0xb]
    // 0xb2fa44: DecompressPointer r2
    //     0xb2fa44: add             x2, x2, HEAP, lsl #32
    // 0xb2fa48: cmp             w2, NULL
    // 0xb2fa4c: b.ne            #0xb2fa58
    // 0xb2fa50: r0 = Null
    //     0xb2fa50: mov             x0, NULL
    // 0xb2fa54: b               #0xb2fa84
    // 0xb2fa58: LoadField: r0 = r2->field_2b
    //     0xb2fa58: ldur            w0, [x2, #0x2b]
    // 0xb2fa5c: DecompressPointer r0
    //     0xb2fa5c: add             x0, x0, HEAP, lsl #32
    // 0xb2fa60: r2 = LoadClassIdInstr(r0)
    //     0xb2fa60: ldur            x2, [x0, #-1]
    //     0xb2fa64: ubfx            x2, x2, #0xc, #0x14
    // 0xb2fa68: str             x0, [SP]
    // 0xb2fa6c: mov             x0, x2
    // 0xb2fa70: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2fa70: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2fa74: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2fa74: movz            x17, #0x2700
    //     0xb2fa78: add             lr, x0, x17
    //     0xb2fa7c: ldr             lr, [x21, lr, lsl #3]
    //     0xb2fa80: blr             lr
    // 0xb2fa84: cmp             w0, NULL
    // 0xb2fa88: b.ne            #0xb2fa94
    // 0xb2fa8c: r2 = " "
    //     0xb2fa8c: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2fa90: b               #0xb2fa98
    // 0xb2fa94: mov             x2, x0
    // 0xb2fa98: ldur            x0, [fp, #-0x28]
    // 0xb2fa9c: ldur            x1, [fp, #-0x10]
    // 0xb2faa0: stur            x2, [fp, #-0x18]
    // 0xb2faa4: r0 = of()
    //     0xb2faa4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2faa8: LoadField: r1 = r0->field_87
    //     0xb2faa8: ldur            w1, [x0, #0x87]
    // 0xb2faac: DecompressPointer r1
    //     0xb2faac: add             x1, x1, HEAP, lsl #32
    // 0xb2fab0: LoadField: r0 = r1->field_2b
    //     0xb2fab0: ldur            w0, [x1, #0x2b]
    // 0xb2fab4: DecompressPointer r0
    //     0xb2fab4: add             x0, x0, HEAP, lsl #32
    // 0xb2fab8: r16 = 14.000000
    //     0xb2fab8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2fabc: ldr             x16, [x16, #0x1d8]
    // 0xb2fac0: r30 = Instance_Color
    //     0xb2fac0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2fac4: stp             lr, x16, [SP]
    // 0xb2fac8: mov             x1, x0
    // 0xb2facc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2facc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2fad0: ldr             x4, [x4, #0xaa0]
    // 0xb2fad4: r0 = copyWith()
    //     0xb2fad4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2fad8: stur            x0, [fp, #-0x20]
    // 0xb2fadc: r0 = Text()
    //     0xb2fadc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2fae0: mov             x3, x0
    // 0xb2fae4: ldur            x0, [fp, #-0x18]
    // 0xb2fae8: stur            x3, [fp, #-0x30]
    // 0xb2faec: StoreField: r3->field_b = r0
    //     0xb2faec: stur            w0, [x3, #0xb]
    // 0xb2faf0: ldur            x0, [fp, #-0x20]
    // 0xb2faf4: StoreField: r3->field_13 = r0
    //     0xb2faf4: stur            w0, [x3, #0x13]
    // 0xb2faf8: r1 = Null
    //     0xb2faf8: mov             x1, NULL
    // 0xb2fafc: r2 = 6
    //     0xb2fafc: movz            x2, #0x6
    // 0xb2fb00: r0 = AllocateArray()
    //     0xb2fb00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2fb04: mov             x2, x0
    // 0xb2fb08: ldur            x0, [fp, #-0x28]
    // 0xb2fb0c: stur            x2, [fp, #-0x18]
    // 0xb2fb10: StoreField: r2->field_f = r0
    //     0xb2fb10: stur            w0, [x2, #0xf]
    // 0xb2fb14: r16 = Instance_Spacer
    //     0xb2fb14: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2fb18: ldr             x16, [x16, #0xf0]
    // 0xb2fb1c: StoreField: r2->field_13 = r16
    //     0xb2fb1c: stur            w16, [x2, #0x13]
    // 0xb2fb20: ldur            x0, [fp, #-0x30]
    // 0xb2fb24: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2fb24: stur            w0, [x2, #0x17]
    // 0xb2fb28: r1 = <Widget>
    //     0xb2fb28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2fb2c: r0 = AllocateGrowableArray()
    //     0xb2fb2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2fb30: mov             x1, x0
    // 0xb2fb34: ldur            x0, [fp, #-0x18]
    // 0xb2fb38: stur            x1, [fp, #-0x20]
    // 0xb2fb3c: StoreField: r1->field_f = r0
    //     0xb2fb3c: stur            w0, [x1, #0xf]
    // 0xb2fb40: r4 = 6
    //     0xb2fb40: movz            x4, #0x6
    // 0xb2fb44: StoreField: r1->field_b = r4
    //     0xb2fb44: stur            w4, [x1, #0xb]
    // 0xb2fb48: r0 = Row()
    //     0xb2fb48: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2fb4c: r1 = Instance_Axis
    //     0xb2fb4c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2fb50: StoreField: r0->field_f = r1
    //     0xb2fb50: stur            w1, [x0, #0xf]
    // 0xb2fb54: r5 = Instance_MainAxisAlignment
    //     0xb2fb54: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2fb58: ldr             x5, [x5, #0xa08]
    // 0xb2fb5c: StoreField: r0->field_13 = r5
    //     0xb2fb5c: stur            w5, [x0, #0x13]
    // 0xb2fb60: r6 = Instance_MainAxisSize
    //     0xb2fb60: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2fb64: ldr             x6, [x6, #0xa10]
    // 0xb2fb68: ArrayStore: r0[0] = r6  ; List_4
    //     0xb2fb68: stur            w6, [x0, #0x17]
    // 0xb2fb6c: r7 = Instance_CrossAxisAlignment
    //     0xb2fb6c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2fb70: ldr             x7, [x7, #0xa18]
    // 0xb2fb74: StoreField: r0->field_1b = r7
    //     0xb2fb74: stur            w7, [x0, #0x1b]
    // 0xb2fb78: r8 = Instance_VerticalDirection
    //     0xb2fb78: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2fb7c: ldr             x8, [x8, #0xa20]
    // 0xb2fb80: StoreField: r0->field_23 = r8
    //     0xb2fb80: stur            w8, [x0, #0x23]
    // 0xb2fb84: r9 = Instance_Clip
    //     0xb2fb84: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2fb88: ldr             x9, [x9, #0x38]
    // 0xb2fb8c: StoreField: r0->field_2b = r9
    //     0xb2fb8c: stur            w9, [x0, #0x2b]
    // 0xb2fb90: StoreField: r0->field_2f = rZR
    //     0xb2fb90: stur            xzr, [x0, #0x2f]
    // 0xb2fb94: ldur            x1, [fp, #-0x20]
    // 0xb2fb98: StoreField: r0->field_b = r1
    //     0xb2fb98: stur            w1, [x0, #0xb]
    // 0xb2fb9c: b               #0xb2ff8c
    // 0xb2fba0: mov             x3, x0
    // 0xb2fba4: r4 = 6
    //     0xb2fba4: movz            x4, #0x6
    // 0xb2fba8: r2 = 4
    //     0xb2fba8: movz            x2, #0x4
    // 0xb2fbac: r7 = Instance_CrossAxisAlignment
    //     0xb2fbac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2fbb0: ldr             x7, [x7, #0xa18]
    // 0xb2fbb4: r5 = Instance_MainAxisAlignment
    //     0xb2fbb4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2fbb8: ldr             x5, [x5, #0xa08]
    // 0xb2fbbc: r6 = Instance_MainAxisSize
    //     0xb2fbbc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2fbc0: ldr             x6, [x6, #0xa10]
    // 0xb2fbc4: r1 = Instance_Axis
    //     0xb2fbc4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2fbc8: r8 = Instance_VerticalDirection
    //     0xb2fbc8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2fbcc: ldr             x8, [x8, #0xa20]
    // 0xb2fbd0: r0 = Instance__DeferringMouseCursor
    //     0xb2fbd0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2fbd4: r9 = Instance_Clip
    //     0xb2fbd4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2fbd8: ldr             x9, [x9, #0x38]
    // 0xb2fbdc: d1 = 0.500000
    //     0xb2fbdc: fmov            d1, #0.50000000
    // 0xb2fbe0: d0 = inf
    //     0xb2fbe0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2fbe4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb2fbe4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb2fbe8: ldr             x0, [x0, #0x1c80]
    //     0xb2fbec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb2fbf0: cmp             w0, w16
    //     0xb2fbf4: b.ne            #0xb2fc00
    //     0xb2fbf8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb2fbfc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb2fc00: r0 = GetNavigation.size()
    //     0xb2fc00: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb2fc04: LoadField: d0 = r0->field_7
    //     0xb2fc04: ldur            d0, [x0, #7]
    // 0xb2fc08: d1 = 0.500000
    //     0xb2fc08: fmov            d1, #0.50000000
    // 0xb2fc0c: fmul            d2, d0, d1
    // 0xb2fc10: stur            d2, [fp, #-0x40]
    // 0xb2fc14: r0 = BoxConstraints()
    //     0xb2fc14: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb2fc18: stur            x0, [fp, #-0x20]
    // 0xb2fc1c: StoreField: r0->field_7 = rZR
    //     0xb2fc1c: stur            xzr, [x0, #7]
    // 0xb2fc20: ldur            d0, [fp, #-0x40]
    // 0xb2fc24: StoreField: r0->field_f = d0
    //     0xb2fc24: stur            d0, [x0, #0xf]
    // 0xb2fc28: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb2fc28: stur            xzr, [x0, #0x17]
    // 0xb2fc2c: d0 = inf
    //     0xb2fc2c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2fc30: StoreField: r0->field_1f = d0
    //     0xb2fc30: stur            d0, [x0, #0x1f]
    // 0xb2fc34: ldur            x2, [fp, #-8]
    // 0xb2fc38: LoadField: r1 = r2->field_b
    //     0xb2fc38: ldur            w1, [x2, #0xb]
    // 0xb2fc3c: DecompressPointer r1
    //     0xb2fc3c: add             x1, x1, HEAP, lsl #32
    // 0xb2fc40: cmp             w1, NULL
    // 0xb2fc44: b.eq            #0xb2ffb8
    // 0xb2fc48: LoadField: r3 = r1->field_f
    //     0xb2fc48: ldur            w3, [x1, #0xf]
    // 0xb2fc4c: DecompressPointer r3
    //     0xb2fc4c: add             x3, x3, HEAP, lsl #32
    // 0xb2fc50: cmp             w3, NULL
    // 0xb2fc54: b.ne            #0xb2fc60
    // 0xb2fc58: r1 = Null
    //     0xb2fc58: mov             x1, NULL
    // 0xb2fc5c: b               #0xb2fc68
    // 0xb2fc60: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb2fc60: ldur            w1, [x3, #0x17]
    // 0xb2fc64: DecompressPointer r1
    //     0xb2fc64: add             x1, x1, HEAP, lsl #32
    // 0xb2fc68: cmp             w1, NULL
    // 0xb2fc6c: b.ne            #0xb2fc78
    // 0xb2fc70: r3 = ""
    //     0xb2fc70: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2fc74: b               #0xb2fc7c
    // 0xb2fc78: mov             x3, x1
    // 0xb2fc7c: ldur            x1, [fp, #-0x10]
    // 0xb2fc80: stur            x3, [fp, #-0x18]
    // 0xb2fc84: r0 = of()
    //     0xb2fc84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2fc88: LoadField: r1 = r0->field_87
    //     0xb2fc88: ldur            w1, [x0, #0x87]
    // 0xb2fc8c: DecompressPointer r1
    //     0xb2fc8c: add             x1, x1, HEAP, lsl #32
    // 0xb2fc90: LoadField: r0 = r1->field_7
    //     0xb2fc90: ldur            w0, [x1, #7]
    // 0xb2fc94: DecompressPointer r0
    //     0xb2fc94: add             x0, x0, HEAP, lsl #32
    // 0xb2fc98: r16 = 16.000000
    //     0xb2fc98: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2fc9c: ldr             x16, [x16, #0x188]
    // 0xb2fca0: r30 = Instance_Color
    //     0xb2fca0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2fca4: stp             lr, x16, [SP]
    // 0xb2fca8: mov             x1, x0
    // 0xb2fcac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2fcac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2fcb0: ldr             x4, [x4, #0xaa0]
    // 0xb2fcb4: r0 = copyWith()
    //     0xb2fcb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2fcb8: stur            x0, [fp, #-0x28]
    // 0xb2fcbc: r0 = TextSpan()
    //     0xb2fcbc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2fcc0: mov             x3, x0
    // 0xb2fcc4: ldur            x0, [fp, #-0x18]
    // 0xb2fcc8: stur            x3, [fp, #-0x30]
    // 0xb2fccc: StoreField: r3->field_b = r0
    //     0xb2fccc: stur            w0, [x3, #0xb]
    // 0xb2fcd0: r0 = Instance__DeferringMouseCursor
    //     0xb2fcd0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2fcd4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2fcd4: stur            w0, [x3, #0x17]
    // 0xb2fcd8: ldur            x1, [fp, #-0x28]
    // 0xb2fcdc: StoreField: r3->field_7 = r1
    //     0xb2fcdc: stur            w1, [x3, #7]
    // 0xb2fce0: r1 = Null
    //     0xb2fce0: mov             x1, NULL
    // 0xb2fce4: r2 = 6
    //     0xb2fce4: movz            x2, #0x6
    // 0xb2fce8: r0 = AllocateArray()
    //     0xb2fce8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2fcec: r16 = " : "
    //     0xb2fcec: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xb2fcf0: ldr             x16, [x16, #0x680]
    // 0xb2fcf4: StoreField: r0->field_f = r16
    //     0xb2fcf4: stur            w16, [x0, #0xf]
    // 0xb2fcf8: ldur            x1, [fp, #-8]
    // 0xb2fcfc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb2fcfc: ldur            w2, [x1, #0x17]
    // 0xb2fd00: DecompressPointer r2
    //     0xb2fd00: add             x2, x2, HEAP, lsl #32
    // 0xb2fd04: r16 = Sentinel
    //     0xb2fd04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb2fd08: cmp             w2, w16
    // 0xb2fd0c: b.eq            #0xb2ffbc
    // 0xb2fd10: StoreField: r0->field_13 = r2
    //     0xb2fd10: stur            w2, [x0, #0x13]
    // 0xb2fd14: r16 = " "
    //     0xb2fd14: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2fd18: ArrayStore: r0[0] = r16  ; List_4
    //     0xb2fd18: stur            w16, [x0, #0x17]
    // 0xb2fd1c: str             x0, [SP]
    // 0xb2fd20: r0 = _interpolate()
    //     0xb2fd20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb2fd24: ldur            x1, [fp, #-0x10]
    // 0xb2fd28: stur            x0, [fp, #-0x18]
    // 0xb2fd2c: r0 = of()
    //     0xb2fd2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2fd30: LoadField: r1 = r0->field_87
    //     0xb2fd30: ldur            w1, [x0, #0x87]
    // 0xb2fd34: DecompressPointer r1
    //     0xb2fd34: add             x1, x1, HEAP, lsl #32
    // 0xb2fd38: LoadField: r0 = r1->field_2b
    //     0xb2fd38: ldur            w0, [x1, #0x2b]
    // 0xb2fd3c: DecompressPointer r0
    //     0xb2fd3c: add             x0, x0, HEAP, lsl #32
    // 0xb2fd40: r16 = 14.000000
    //     0xb2fd40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2fd44: ldr             x16, [x16, #0x1d8]
    // 0xb2fd48: r30 = Instance_Color
    //     0xb2fd48: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2fd4c: stp             lr, x16, [SP]
    // 0xb2fd50: mov             x1, x0
    // 0xb2fd54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2fd54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2fd58: ldr             x4, [x4, #0xaa0]
    // 0xb2fd5c: r0 = copyWith()
    //     0xb2fd5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2fd60: stur            x0, [fp, #-0x28]
    // 0xb2fd64: r0 = TextSpan()
    //     0xb2fd64: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2fd68: mov             x3, x0
    // 0xb2fd6c: ldur            x0, [fp, #-0x18]
    // 0xb2fd70: stur            x3, [fp, #-0x38]
    // 0xb2fd74: StoreField: r3->field_b = r0
    //     0xb2fd74: stur            w0, [x3, #0xb]
    // 0xb2fd78: r0 = Instance__DeferringMouseCursor
    //     0xb2fd78: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2fd7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb2fd7c: stur            w0, [x3, #0x17]
    // 0xb2fd80: ldur            x1, [fp, #-0x28]
    // 0xb2fd84: StoreField: r3->field_7 = r1
    //     0xb2fd84: stur            w1, [x3, #7]
    // 0xb2fd88: r1 = Null
    //     0xb2fd88: mov             x1, NULL
    // 0xb2fd8c: r2 = 4
    //     0xb2fd8c: movz            x2, #0x4
    // 0xb2fd90: r0 = AllocateArray()
    //     0xb2fd90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2fd94: mov             x2, x0
    // 0xb2fd98: ldur            x0, [fp, #-0x30]
    // 0xb2fd9c: stur            x2, [fp, #-0x18]
    // 0xb2fda0: StoreField: r2->field_f = r0
    //     0xb2fda0: stur            w0, [x2, #0xf]
    // 0xb2fda4: ldur            x0, [fp, #-0x38]
    // 0xb2fda8: StoreField: r2->field_13 = r0
    //     0xb2fda8: stur            w0, [x2, #0x13]
    // 0xb2fdac: r1 = <InlineSpan>
    //     0xb2fdac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb2fdb0: ldr             x1, [x1, #0xe40]
    // 0xb2fdb4: r0 = AllocateGrowableArray()
    //     0xb2fdb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2fdb8: mov             x1, x0
    // 0xb2fdbc: ldur            x0, [fp, #-0x18]
    // 0xb2fdc0: stur            x1, [fp, #-0x28]
    // 0xb2fdc4: StoreField: r1->field_f = r0
    //     0xb2fdc4: stur            w0, [x1, #0xf]
    // 0xb2fdc8: r0 = 4
    //     0xb2fdc8: movz            x0, #0x4
    // 0xb2fdcc: StoreField: r1->field_b = r0
    //     0xb2fdcc: stur            w0, [x1, #0xb]
    // 0xb2fdd0: r0 = TextSpan()
    //     0xb2fdd0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb2fdd4: mov             x1, x0
    // 0xb2fdd8: ldur            x0, [fp, #-0x28]
    // 0xb2fddc: stur            x1, [fp, #-0x18]
    // 0xb2fde0: StoreField: r1->field_f = r0
    //     0xb2fde0: stur            w0, [x1, #0xf]
    // 0xb2fde4: r0 = Instance__DeferringMouseCursor
    //     0xb2fde4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb2fde8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb2fde8: stur            w0, [x1, #0x17]
    // 0xb2fdec: r0 = RichText()
    //     0xb2fdec: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb2fdf0: mov             x1, x0
    // 0xb2fdf4: ldur            x2, [fp, #-0x18]
    // 0xb2fdf8: stur            x0, [fp, #-0x18]
    // 0xb2fdfc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb2fdfc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb2fe00: r0 = RichText()
    //     0xb2fe00: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb2fe04: r0 = ConstrainedBox()
    //     0xb2fe04: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb2fe08: mov             x1, x0
    // 0xb2fe0c: ldur            x0, [fp, #-0x20]
    // 0xb2fe10: stur            x1, [fp, #-0x28]
    // 0xb2fe14: StoreField: r1->field_f = r0
    //     0xb2fe14: stur            w0, [x1, #0xf]
    // 0xb2fe18: ldur            x0, [fp, #-0x18]
    // 0xb2fe1c: StoreField: r1->field_b = r0
    //     0xb2fe1c: stur            w0, [x1, #0xb]
    // 0xb2fe20: ldur            x0, [fp, #-8]
    // 0xb2fe24: LoadField: r2 = r0->field_b
    //     0xb2fe24: ldur            w2, [x0, #0xb]
    // 0xb2fe28: DecompressPointer r2
    //     0xb2fe28: add             x2, x2, HEAP, lsl #32
    // 0xb2fe2c: cmp             w2, NULL
    // 0xb2fe30: b.eq            #0xb2ffc8
    // 0xb2fe34: LoadField: r0 = r2->field_f
    //     0xb2fe34: ldur            w0, [x2, #0xf]
    // 0xb2fe38: DecompressPointer r0
    //     0xb2fe38: add             x0, x0, HEAP, lsl #32
    // 0xb2fe3c: cmp             w0, NULL
    // 0xb2fe40: b.ne            #0xb2fe4c
    // 0xb2fe44: r0 = Null
    //     0xb2fe44: mov             x0, NULL
    // 0xb2fe48: b               #0xb2fe74
    // 0xb2fe4c: LoadField: r2 = r0->field_2b
    //     0xb2fe4c: ldur            w2, [x0, #0x2b]
    // 0xb2fe50: DecompressPointer r2
    //     0xb2fe50: add             x2, x2, HEAP, lsl #32
    // 0xb2fe54: r0 = LoadClassIdInstr(r2)
    //     0xb2fe54: ldur            x0, [x2, #-1]
    //     0xb2fe58: ubfx            x0, x0, #0xc, #0x14
    // 0xb2fe5c: str             x2, [SP]
    // 0xb2fe60: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb2fe60: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2fe64: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2fe64: movz            x17, #0x2700
    //     0xb2fe68: add             lr, x0, x17
    //     0xb2fe6c: ldr             lr, [x21, lr, lsl #3]
    //     0xb2fe70: blr             lr
    // 0xb2fe74: cmp             w0, NULL
    // 0xb2fe78: b.ne            #0xb2fe84
    // 0xb2fe7c: r2 = " "
    //     0xb2fe7c: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb2fe80: b               #0xb2fe88
    // 0xb2fe84: mov             x2, x0
    // 0xb2fe88: ldur            x0, [fp, #-0x28]
    // 0xb2fe8c: ldur            x1, [fp, #-0x10]
    // 0xb2fe90: stur            x2, [fp, #-8]
    // 0xb2fe94: r0 = of()
    //     0xb2fe94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2fe98: LoadField: r1 = r0->field_87
    //     0xb2fe98: ldur            w1, [x0, #0x87]
    // 0xb2fe9c: DecompressPointer r1
    //     0xb2fe9c: add             x1, x1, HEAP, lsl #32
    // 0xb2fea0: LoadField: r0 = r1->field_2b
    //     0xb2fea0: ldur            w0, [x1, #0x2b]
    // 0xb2fea4: DecompressPointer r0
    //     0xb2fea4: add             x0, x0, HEAP, lsl #32
    // 0xb2fea8: r16 = 14.000000
    //     0xb2fea8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2feac: ldr             x16, [x16, #0x1d8]
    // 0xb2feb0: r30 = Instance_Color
    //     0xb2feb0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2feb4: stp             lr, x16, [SP]
    // 0xb2feb8: mov             x1, x0
    // 0xb2febc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2febc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2fec0: ldr             x4, [x4, #0xaa0]
    // 0xb2fec4: r0 = copyWith()
    //     0xb2fec4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2fec8: stur            x0, [fp, #-0x10]
    // 0xb2fecc: r0 = Text()
    //     0xb2fecc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2fed0: mov             x3, x0
    // 0xb2fed4: ldur            x0, [fp, #-8]
    // 0xb2fed8: stur            x3, [fp, #-0x18]
    // 0xb2fedc: StoreField: r3->field_b = r0
    //     0xb2fedc: stur            w0, [x3, #0xb]
    // 0xb2fee0: ldur            x0, [fp, #-0x10]
    // 0xb2fee4: StoreField: r3->field_13 = r0
    //     0xb2fee4: stur            w0, [x3, #0x13]
    // 0xb2fee8: r1 = Null
    //     0xb2fee8: mov             x1, NULL
    // 0xb2feec: r2 = 6
    //     0xb2feec: movz            x2, #0x6
    // 0xb2fef0: r0 = AllocateArray()
    //     0xb2fef0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2fef4: mov             x2, x0
    // 0xb2fef8: ldur            x0, [fp, #-0x28]
    // 0xb2fefc: stur            x2, [fp, #-8]
    // 0xb2ff00: StoreField: r2->field_f = r0
    //     0xb2ff00: stur            w0, [x2, #0xf]
    // 0xb2ff04: r16 = Instance_Spacer
    //     0xb2ff04: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb2ff08: ldr             x16, [x16, #0xf0]
    // 0xb2ff0c: StoreField: r2->field_13 = r16
    //     0xb2ff0c: stur            w16, [x2, #0x13]
    // 0xb2ff10: ldur            x0, [fp, #-0x18]
    // 0xb2ff14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2ff14: stur            w0, [x2, #0x17]
    // 0xb2ff18: r1 = <Widget>
    //     0xb2ff18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2ff1c: r0 = AllocateGrowableArray()
    //     0xb2ff1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2ff20: mov             x1, x0
    // 0xb2ff24: ldur            x0, [fp, #-8]
    // 0xb2ff28: stur            x1, [fp, #-0x10]
    // 0xb2ff2c: StoreField: r1->field_f = r0
    //     0xb2ff2c: stur            w0, [x1, #0xf]
    // 0xb2ff30: r0 = 6
    //     0xb2ff30: movz            x0, #0x6
    // 0xb2ff34: StoreField: r1->field_b = r0
    //     0xb2ff34: stur            w0, [x1, #0xb]
    // 0xb2ff38: r0 = Row()
    //     0xb2ff38: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2ff3c: r1 = Instance_Axis
    //     0xb2ff3c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2ff40: StoreField: r0->field_f = r1
    //     0xb2ff40: stur            w1, [x0, #0xf]
    // 0xb2ff44: r1 = Instance_MainAxisAlignment
    //     0xb2ff44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2ff48: ldr             x1, [x1, #0xa08]
    // 0xb2ff4c: StoreField: r0->field_13 = r1
    //     0xb2ff4c: stur            w1, [x0, #0x13]
    // 0xb2ff50: r1 = Instance_MainAxisSize
    //     0xb2ff50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb2ff54: ldr             x1, [x1, #0xa10]
    // 0xb2ff58: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2ff58: stur            w1, [x0, #0x17]
    // 0xb2ff5c: r1 = Instance_CrossAxisAlignment
    //     0xb2ff5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2ff60: ldr             x1, [x1, #0xa18]
    // 0xb2ff64: StoreField: r0->field_1b = r1
    //     0xb2ff64: stur            w1, [x0, #0x1b]
    // 0xb2ff68: r1 = Instance_VerticalDirection
    //     0xb2ff68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2ff6c: ldr             x1, [x1, #0xa20]
    // 0xb2ff70: StoreField: r0->field_23 = r1
    //     0xb2ff70: stur            w1, [x0, #0x23]
    // 0xb2ff74: r1 = Instance_Clip
    //     0xb2ff74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2ff78: ldr             x1, [x1, #0x38]
    // 0xb2ff7c: StoreField: r0->field_2b = r1
    //     0xb2ff7c: stur            w1, [x0, #0x2b]
    // 0xb2ff80: StoreField: r0->field_2f = rZR
    //     0xb2ff80: stur            xzr, [x0, #0x2f]
    // 0xb2ff84: ldur            x1, [fp, #-0x10]
    // 0xb2ff88: StoreField: r0->field_b = r1
    //     0xb2ff88: stur            w1, [x0, #0xb]
    // 0xb2ff8c: LeaveFrame
    //     0xb2ff8c: mov             SP, fp
    //     0xb2ff90: ldp             fp, lr, [SP], #0x10
    // 0xb2ff94: ret
    //     0xb2ff94: ret             
    // 0xb2ff98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2ff98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2ff9c: b               #0xb2f748
    // 0xb2ffa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ffa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2ffa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ffa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2ffa8: r9 = value
    //     0xb2ffa8: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ab60] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb2ffac: ldr             x9, [x9, #0xb60]
    // 0xb2ffb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb2ffb0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb2ffb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ffb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2ffb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ffb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2ffbc: r9 = customisedValue
    //     0xb2ffbc: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6ab68] Field <<EMAIL>>: late (offset: 0x18)
    //     0xb2ffc0: ldr             x9, [x9, #0xb68]
    // 0xb2ffc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb2ffc4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb2ffc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2ffc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4119, size: 0x14, field offset: 0xc
//   const constructor, 
class BagSingleSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e6a0, size: 0x30
    // 0xc7e6a0: EnterFrame
    //     0xc7e6a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e6a4: mov             fp, SP
    // 0xc7e6a8: mov             x0, x1
    // 0xc7e6ac: r1 = <BagSingleSelect>
    //     0xc7e6ac: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e50] TypeArguments: <BagSingleSelect>
    //     0xc7e6b0: ldr             x1, [x1, #0xe50]
    // 0xc7e6b4: r0 = _BagSingleSelectState()
    //     0xc7e6b4: bl              #0xc7e6d0  ; Allocate_BagSingleSelectStateStub -> _BagSingleSelectState (size=0x1c)
    // 0xc7e6b8: r1 = Sentinel
    //     0xc7e6b8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7e6bc: StoreField: r0->field_13 = r1
    //     0xc7e6bc: stur            w1, [x0, #0x13]
    // 0xc7e6c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7e6c0: stur            w1, [x0, #0x17]
    // 0xc7e6c4: LeaveFrame
    //     0xc7e6c4: mov             SP, fp
    //     0xc7e6c8: ldp             fp, lr, [SP], #0x10
    // 0xc7e6cc: ret
    //     0xc7e6cc: ret             
  }
}
