// lib: , url: package:dio/src/redirect_record.dart

// class id: 1049612, size: 0x8
class :: {
}

// class id: 4974, size: 0x18, field offset: 0x8
//   const constructor, 
class RedirectRecord extends Object {

  _ toString(/* No info */) {
    // ** addr: 0x15686a0, size: 0xac
    // 0x15686a0: EnterFrame
    //     0x15686a0: stp             fp, lr, [SP, #-0x10]!
    //     0x15686a4: mov             fp, SP
    // 0x15686a8: AllocStack(0x8)
    //     0x15686a8: sub             SP, SP, #8
    // 0x15686ac: CheckStackOverflow
    //     0x15686ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15686b0: cmp             SP, x16
    //     0x15686b4: b.ls            #0x1568744
    // 0x15686b8: r1 = Null
    //     0x15686b8: mov             x1, NULL
    // 0x15686bc: r2 = 14
    //     0x15686bc: movz            x2, #0xe
    // 0x15686c0: r0 = AllocateArray()
    //     0x15686c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15686c4: mov             x2, x0
    // 0x15686c8: r16 = "RedirectRecord{statusCode: "
    //     0x15686c8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26238] "RedirectRecord{statusCode: "
    //     0x15686cc: ldr             x16, [x16, #0x238]
    // 0x15686d0: StoreField: r2->field_f = r16
    //     0x15686d0: stur            w16, [x2, #0xf]
    // 0x15686d4: ldr             x3, [fp, #0x10]
    // 0x15686d8: LoadField: r4 = r3->field_7
    //     0x15686d8: ldur            x4, [x3, #7]
    // 0x15686dc: r0 = BoxInt64Instr(r4)
    //     0x15686dc: sbfiz           x0, x4, #1, #0x1f
    //     0x15686e0: cmp             x4, x0, asr #1
    //     0x15686e4: b.eq            #0x15686f0
    //     0x15686e8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x15686ec: stur            x4, [x0, #7]
    // 0x15686f0: StoreField: r2->field_13 = r0
    //     0x15686f0: stur            w0, [x2, #0x13]
    // 0x15686f4: r16 = ", method: "
    //     0x15686f4: add             x16, PP, #0x26, lsl #12  ; [pp+0x26240] ", method: "
    //     0x15686f8: ldr             x16, [x16, #0x240]
    // 0x15686fc: ArrayStore: r2[0] = r16  ; List_4
    //     0x15686fc: stur            w16, [x2, #0x17]
    // 0x1568700: LoadField: r0 = r3->field_f
    //     0x1568700: ldur            w0, [x3, #0xf]
    // 0x1568704: DecompressPointer r0
    //     0x1568704: add             x0, x0, HEAP, lsl #32
    // 0x1568708: StoreField: r2->field_1b = r0
    //     0x1568708: stur            w0, [x2, #0x1b]
    // 0x156870c: r16 = ", location: "
    //     0x156870c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26248] ", location: "
    //     0x1568710: ldr             x16, [x16, #0x248]
    // 0x1568714: StoreField: r2->field_1f = r16
    //     0x1568714: stur            w16, [x2, #0x1f]
    // 0x1568718: LoadField: r0 = r3->field_13
    //     0x1568718: ldur            w0, [x3, #0x13]
    // 0x156871c: DecompressPointer r0
    //     0x156871c: add             x0, x0, HEAP, lsl #32
    // 0x1568720: StoreField: r2->field_23 = r0
    //     0x1568720: stur            w0, [x2, #0x23]
    // 0x1568724: r16 = "}"
    //     0x1568724: add             x16, PP, #0x11, lsl #12  ; [pp+0x11b38] "}"
    //     0x1568728: ldr             x16, [x16, #0xb38]
    // 0x156872c: StoreField: r2->field_27 = r16
    //     0x156872c: stur            w16, [x2, #0x27]
    // 0x1568730: str             x2, [SP]
    // 0x1568734: r0 = _interpolate()
    //     0x1568734: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1568738: LeaveFrame
    //     0x1568738: mov             SP, fp
    //     0x156873c: ldp             fp, lr, [SP], #0x10
    // 0x1568740: ret
    //     0x1568740: ret             
    // 0x1568744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568744: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1568748: b               #0x15686b8
  }
}
