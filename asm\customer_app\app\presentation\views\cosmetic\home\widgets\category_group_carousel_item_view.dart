// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart

// class id: 1049277, size: 0x8
class :: {
}

// class id: 3429, size: 0x2c, field offset: 0x14
class _CategoryGroupCarouselItemViewState extends State<dynamic> {

  late double _viewportHeight; // offset: 0x28
  late PageController _pageLineController; // offset: 0x14
  late TextStyle _titleStyle; // offset: 0x24
  late double _imageSize; // offset: 0x20

  _ build(/* No info */) {
    // ** addr: 0xae405c, size: 0x3b4
    // 0xae405c: EnterFrame
    //     0xae405c: stp             fp, lr, [SP, #-0x10]!
    //     0xae4060: mov             fp, SP
    // 0xae4064: AllocStack(0x60)
    //     0xae4064: sub             SP, SP, #0x60
    // 0xae4068: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae4068: mov             x0, x1
    //     0xae406c: stur            x1, [fp, #-8]
    //     0xae4070: mov             x1, x2
    //     0xae4074: stur            x2, [fp, #-0x10]
    // 0xae4078: CheckStackOverflow
    //     0xae4078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae407c: cmp             SP, x16
    //     0xae4080: b.ls            #0xae43bc
    // 0xae4084: r1 = 1
    //     0xae4084: movz            x1, #0x1
    // 0xae4088: r0 = AllocateContext()
    //     0xae4088: bl              #0x16f6108  ; AllocateContextStub
    // 0xae408c: mov             x3, x0
    // 0xae4090: ldur            x0, [fp, #-8]
    // 0xae4094: stur            x3, [fp, #-0x30]
    // 0xae4098: StoreField: r3->field_f = r0
    //     0xae4098: stur            w0, [x3, #0xf]
    // 0xae409c: LoadField: r1 = r0->field_b
    //     0xae409c: ldur            w1, [x0, #0xb]
    // 0xae40a0: DecompressPointer r1
    //     0xae40a0: add             x1, x1, HEAP, lsl #32
    // 0xae40a4: cmp             w1, NULL
    // 0xae40a8: b.eq            #0xae43c4
    // 0xae40ac: LoadField: r2 = r1->field_b
    //     0xae40ac: ldur            w2, [x1, #0xb]
    // 0xae40b0: DecompressPointer r2
    //     0xae40b0: add             x2, x2, HEAP, lsl #32
    // 0xae40b4: cmp             w2, NULL
    // 0xae40b8: b.ne            #0xae40c4
    // 0xae40bc: r1 = Null
    //     0xae40bc: mov             x1, NULL
    // 0xae40c0: b               #0xae40c8
    // 0xae40c4: LoadField: r1 = r2->field_b
    //     0xae40c4: ldur            w1, [x2, #0xb]
    // 0xae40c8: cmp             w1, NULL
    // 0xae40cc: b.ne            #0xae40d8
    // 0xae40d0: r1 = 0
    //     0xae40d0: movz            x1, #0
    // 0xae40d4: b               #0xae40e0
    // 0xae40d8: r2 = LoadInt32Instr(r1)
    //     0xae40d8: sbfx            x2, x1, #1, #0x1f
    // 0xae40dc: mov             x1, x2
    // 0xae40e0: d0 = 3.000000
    //     0xae40e0: fmov            d0, #3.00000000
    // 0xae40e4: scvtf           d1, x1
    // 0xae40e8: fdiv            d2, d1, d0
    // 0xae40ec: fcmp            d2, d2
    // 0xae40f0: b.vs            #0xae43c8
    // 0xae40f4: fcvtps          x4, d2
    // 0xae40f8: asr             x16, x4, #0x1e
    // 0xae40fc: cmp             x16, x4, asr #63
    // 0xae4100: b.ne            #0xae43c8
    // 0xae4104: lsl             x4, x4, #1
    // 0xae4108: stur            x4, [fp, #-0x28]
    // 0xae410c: LoadField: r5 = r0->field_27
    //     0xae410c: ldur            w5, [x0, #0x27]
    // 0xae4110: DecompressPointer r5
    //     0xae4110: add             x5, x5, HEAP, lsl #32
    // 0xae4114: r16 = Sentinel
    //     0xae4114: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4118: cmp             w5, w16
    // 0xae411c: b.eq            #0xae43f4
    // 0xae4120: stur            x5, [fp, #-0x20]
    // 0xae4124: LoadField: r6 = r0->field_13
    //     0xae4124: ldur            w6, [x0, #0x13]
    // 0xae4128: DecompressPointer r6
    //     0xae4128: add             x6, x6, HEAP, lsl #32
    // 0xae412c: r16 = Sentinel
    //     0xae412c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4130: cmp             w6, w16
    // 0xae4134: b.eq            #0xae4400
    // 0xae4138: mov             x2, x3
    // 0xae413c: stur            x6, [fp, #-0x18]
    // 0xae4140: r1 = Function '<anonymous closure>':.
    //     0xae4140: add             x1, PP, #0x58, lsl #12  ; [pp+0x58358] AnonymousClosure: (0xae4b84), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xae405c)
    //     0xae4144: ldr             x1, [x1, #0x358]
    // 0xae4148: r0 = AllocateClosure()
    //     0xae4148: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae414c: ldur            x2, [fp, #-0x30]
    // 0xae4150: r1 = Function '<anonymous closure>':.
    //     0xae4150: add             x1, PP, #0x58, lsl #12  ; [pp+0x58360] AnonymousClosure: (0xae4434), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xae405c)
    //     0xae4154: ldr             x1, [x1, #0x360]
    // 0xae4158: stur            x0, [fp, #-0x30]
    // 0xae415c: r0 = AllocateClosure()
    //     0xae415c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae4160: stur            x0, [fp, #-0x38]
    // 0xae4164: r0 = PageView()
    //     0xae4164: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xae4168: stur            x0, [fp, #-0x40]
    // 0xae416c: r16 = Instance_BouncingScrollPhysics
    //     0xae416c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xae4170: ldr             x16, [x16, #0x890]
    // 0xae4174: ldur            lr, [fp, #-0x18]
    // 0xae4178: stp             lr, x16, [SP]
    // 0xae417c: mov             x1, x0
    // 0xae4180: ldur            x2, [fp, #-0x38]
    // 0xae4184: ldur            x3, [fp, #-0x28]
    // 0xae4188: ldur            x5, [fp, #-0x30]
    // 0xae418c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xae418c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xae4190: ldr             x4, [x4, #0xe40]
    // 0xae4194: r0 = PageView.builder()
    //     0xae4194: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xae4198: r0 = SizedBox()
    //     0xae4198: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xae419c: mov             x3, x0
    // 0xae41a0: ldur            x0, [fp, #-0x20]
    // 0xae41a4: stur            x3, [fp, #-0x18]
    // 0xae41a8: StoreField: r3->field_13 = r0
    //     0xae41a8: stur            w0, [x3, #0x13]
    // 0xae41ac: ldur            x0, [fp, #-0x40]
    // 0xae41b0: StoreField: r3->field_b = r0
    //     0xae41b0: stur            w0, [x3, #0xb]
    // 0xae41b4: r1 = Null
    //     0xae41b4: mov             x1, NULL
    // 0xae41b8: r2 = 2
    //     0xae41b8: movz            x2, #0x2
    // 0xae41bc: r0 = AllocateArray()
    //     0xae41bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae41c0: mov             x2, x0
    // 0xae41c4: ldur            x0, [fp, #-0x18]
    // 0xae41c8: stur            x2, [fp, #-0x20]
    // 0xae41cc: StoreField: r2->field_f = r0
    //     0xae41cc: stur            w0, [x2, #0xf]
    // 0xae41d0: r1 = <Widget>
    //     0xae41d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae41d4: r0 = AllocateGrowableArray()
    //     0xae41d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae41d8: mov             x2, x0
    // 0xae41dc: ldur            x0, [fp, #-0x20]
    // 0xae41e0: stur            x2, [fp, #-0x18]
    // 0xae41e4: StoreField: r2->field_f = r0
    //     0xae41e4: stur            w0, [x2, #0xf]
    // 0xae41e8: r0 = 2
    //     0xae41e8: movz            x0, #0x2
    // 0xae41ec: StoreField: r2->field_b = r0
    //     0xae41ec: stur            w0, [x2, #0xb]
    // 0xae41f0: ldur            x0, [fp, #-8]
    // 0xae41f4: LoadField: r1 = r0->field_b
    //     0xae41f4: ldur            w1, [x0, #0xb]
    // 0xae41f8: DecompressPointer r1
    //     0xae41f8: add             x1, x1, HEAP, lsl #32
    // 0xae41fc: cmp             w1, NULL
    // 0xae4200: b.eq            #0xae440c
    // 0xae4204: LoadField: r3 = r1->field_b
    //     0xae4204: ldur            w3, [x1, #0xb]
    // 0xae4208: DecompressPointer r3
    //     0xae4208: add             x3, x3, HEAP, lsl #32
    // 0xae420c: cmp             w3, NULL
    // 0xae4210: b.ne            #0xae421c
    // 0xae4214: r1 = Null
    //     0xae4214: mov             x1, NULL
    // 0xae4218: b               #0xae4220
    // 0xae421c: LoadField: r1 = r3->field_b
    //     0xae421c: ldur            w1, [x3, #0xb]
    // 0xae4220: cmp             w1, NULL
    // 0xae4224: b.ne            #0xae4230
    // 0xae4228: r1 = 0
    //     0xae4228: movz            x1, #0
    // 0xae422c: b               #0xae4238
    // 0xae4230: r3 = LoadInt32Instr(r1)
    //     0xae4230: sbfx            x3, x1, #1, #0x1f
    // 0xae4234: mov             x1, x3
    // 0xae4238: cmp             x1, #4
    // 0xae423c: b.le            #0xae433c
    // 0xae4240: ldur            x3, [fp, #-0x28]
    // 0xae4244: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xae4244: ldur            x4, [x0, #0x17]
    // 0xae4248: ldur            x1, [fp, #-0x10]
    // 0xae424c: stur            x4, [fp, #-0x48]
    // 0xae4250: r0 = of()
    //     0xae4250: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae4254: LoadField: r1 = r0->field_5b
    //     0xae4254: ldur            w1, [x0, #0x5b]
    // 0xae4258: DecompressPointer r1
    //     0xae4258: add             x1, x1, HEAP, lsl #32
    // 0xae425c: ldur            x0, [fp, #-0x28]
    // 0xae4260: stur            x1, [fp, #-8]
    // 0xae4264: r2 = LoadInt32Instr(r0)
    //     0xae4264: sbfx            x2, x0, #1, #0x1f
    //     0xae4268: tbz             w0, #0, #0xae4270
    //     0xae426c: ldur            x2, [x0, #7]
    // 0xae4270: stur            x2, [fp, #-0x50]
    // 0xae4274: r0 = CarouselIndicator()
    //     0xae4274: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xae4278: mov             x1, x0
    // 0xae427c: ldur            x0, [fp, #-0x50]
    // 0xae4280: stur            x1, [fp, #-0x10]
    // 0xae4284: StoreField: r1->field_b = r0
    //     0xae4284: stur            x0, [x1, #0xb]
    // 0xae4288: ldur            x0, [fp, #-0x48]
    // 0xae428c: StoreField: r1->field_13 = r0
    //     0xae428c: stur            x0, [x1, #0x13]
    // 0xae4290: ldur            x0, [fp, #-8]
    // 0xae4294: StoreField: r1->field_1b = r0
    //     0xae4294: stur            w0, [x1, #0x1b]
    // 0xae4298: r0 = Instance_Color
    //     0xae4298: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xae429c: ldr             x0, [x0, #0x90]
    // 0xae42a0: StoreField: r1->field_1f = r0
    //     0xae42a0: stur            w0, [x1, #0x1f]
    // 0xae42a4: r0 = Padding()
    //     0xae42a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae42a8: mov             x2, x0
    // 0xae42ac: r0 = Instance_EdgeInsets
    //     0xae42ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xae42b0: ldr             x0, [x0, #0xa00]
    // 0xae42b4: stur            x2, [fp, #-8]
    // 0xae42b8: StoreField: r2->field_f = r0
    //     0xae42b8: stur            w0, [x2, #0xf]
    // 0xae42bc: ldur            x0, [fp, #-0x10]
    // 0xae42c0: StoreField: r2->field_b = r0
    //     0xae42c0: stur            w0, [x2, #0xb]
    // 0xae42c4: ldur            x0, [fp, #-0x18]
    // 0xae42c8: LoadField: r1 = r0->field_b
    //     0xae42c8: ldur            w1, [x0, #0xb]
    // 0xae42cc: LoadField: r3 = r0->field_f
    //     0xae42cc: ldur            w3, [x0, #0xf]
    // 0xae42d0: DecompressPointer r3
    //     0xae42d0: add             x3, x3, HEAP, lsl #32
    // 0xae42d4: LoadField: r4 = r3->field_b
    //     0xae42d4: ldur            w4, [x3, #0xb]
    // 0xae42d8: r3 = LoadInt32Instr(r1)
    //     0xae42d8: sbfx            x3, x1, #1, #0x1f
    // 0xae42dc: stur            x3, [fp, #-0x48]
    // 0xae42e0: r1 = LoadInt32Instr(r4)
    //     0xae42e0: sbfx            x1, x4, #1, #0x1f
    // 0xae42e4: cmp             x3, x1
    // 0xae42e8: b.ne            #0xae42f4
    // 0xae42ec: mov             x1, x0
    // 0xae42f0: r0 = _growToNextCapacity()
    //     0xae42f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae42f4: ldur            x2, [fp, #-0x18]
    // 0xae42f8: ldur            x3, [fp, #-0x48]
    // 0xae42fc: add             x0, x3, #1
    // 0xae4300: lsl             x1, x0, #1
    // 0xae4304: StoreField: r2->field_b = r1
    //     0xae4304: stur            w1, [x2, #0xb]
    // 0xae4308: LoadField: r1 = r2->field_f
    //     0xae4308: ldur            w1, [x2, #0xf]
    // 0xae430c: DecompressPointer r1
    //     0xae430c: add             x1, x1, HEAP, lsl #32
    // 0xae4310: ldur            x0, [fp, #-8]
    // 0xae4314: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae4314: add             x25, x1, x3, lsl #2
    //     0xae4318: add             x25, x25, #0xf
    //     0xae431c: str             w0, [x25]
    //     0xae4320: tbz             w0, #0, #0xae433c
    //     0xae4324: ldurb           w16, [x1, #-1]
    //     0xae4328: ldurb           w17, [x0, #-1]
    //     0xae432c: and             x16, x17, x16, lsr #2
    //     0xae4330: tst             x16, HEAP, lsr #32
    //     0xae4334: b.eq            #0xae433c
    //     0xae4338: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae433c: r0 = Column()
    //     0xae433c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae4340: mov             x1, x0
    // 0xae4344: r0 = Instance_Axis
    //     0xae4344: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae4348: stur            x1, [fp, #-8]
    // 0xae434c: StoreField: r1->field_f = r0
    //     0xae434c: stur            w0, [x1, #0xf]
    // 0xae4350: r0 = Instance_MainAxisAlignment
    //     0xae4350: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae4354: ldr             x0, [x0, #0xa08]
    // 0xae4358: StoreField: r1->field_13 = r0
    //     0xae4358: stur            w0, [x1, #0x13]
    // 0xae435c: r0 = Instance_MainAxisSize
    //     0xae435c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae4360: ldr             x0, [x0, #0xa10]
    // 0xae4364: ArrayStore: r1[0] = r0  ; List_4
    //     0xae4364: stur            w0, [x1, #0x17]
    // 0xae4368: r0 = Instance_CrossAxisAlignment
    //     0xae4368: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae436c: ldr             x0, [x0, #0xa18]
    // 0xae4370: StoreField: r1->field_1b = r0
    //     0xae4370: stur            w0, [x1, #0x1b]
    // 0xae4374: r0 = Instance_VerticalDirection
    //     0xae4374: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae4378: ldr             x0, [x0, #0xa20]
    // 0xae437c: StoreField: r1->field_23 = r0
    //     0xae437c: stur            w0, [x1, #0x23]
    // 0xae4380: r0 = Instance_Clip
    //     0xae4380: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae4384: ldr             x0, [x0, #0x38]
    // 0xae4388: StoreField: r1->field_2b = r0
    //     0xae4388: stur            w0, [x1, #0x2b]
    // 0xae438c: StoreField: r1->field_2f = rZR
    //     0xae438c: stur            xzr, [x1, #0x2f]
    // 0xae4390: ldur            x0, [fp, #-0x18]
    // 0xae4394: StoreField: r1->field_b = r0
    //     0xae4394: stur            w0, [x1, #0xb]
    // 0xae4398: r0 = Padding()
    //     0xae4398: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae439c: r1 = Instance_EdgeInsets
    //     0xae439c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xae43a0: ldr             x1, [x1, #0x110]
    // 0xae43a4: StoreField: r0->field_f = r1
    //     0xae43a4: stur            w1, [x0, #0xf]
    // 0xae43a8: ldur            x1, [fp, #-8]
    // 0xae43ac: StoreField: r0->field_b = r1
    //     0xae43ac: stur            w1, [x0, #0xb]
    // 0xae43b0: LeaveFrame
    //     0xae43b0: mov             SP, fp
    //     0xae43b4: ldp             fp, lr, [SP], #0x10
    // 0xae43b8: ret
    //     0xae43b8: ret             
    // 0xae43bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae43bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae43c0: b               #0xae4084
    // 0xae43c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae43c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae43c8: SaveReg d2
    //     0xae43c8: str             q2, [SP, #-0x10]!
    // 0xae43cc: stp             x0, x3, [SP, #-0x10]!
    // 0xae43d0: d0 = 0.000000
    //     0xae43d0: fmov            d0, d2
    // 0xae43d4: r0 = 64
    //     0xae43d4: movz            x0, #0x40
    // 0xae43d8: r30 = DoubleToIntegerStub
    //     0xae43d8: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xae43dc: LoadField: r30 = r30->field_7
    //     0xae43dc: ldur            lr, [lr, #7]
    // 0xae43e0: blr             lr
    // 0xae43e4: mov             x4, x0
    // 0xae43e8: ldp             x0, x3, [SP], #0x10
    // 0xae43ec: RestoreReg d2
    //     0xae43ec: ldr             q2, [SP], #0x10
    // 0xae43f0: b               #0xae4108
    // 0xae43f4: r9 = _viewportHeight
    //     0xae43f4: add             x9, PP, #0x58, lsl #12  ; [pp+0x58368] Field <_CategoryGroupCarouselItemViewState@1464505729._viewportHeight@1464505729>: late (offset: 0x28)
    //     0xae43f8: ldr             x9, [x9, #0x368]
    // 0xae43fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae43fc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae4400: r9 = _pageLineController
    //     0xae4400: add             x9, PP, #0x58, lsl #12  ; [pp+0x58370] Field <_CategoryGroupCarouselItemViewState@1464505729._pageLineController@1464505729>: late (offset: 0x14)
    //     0xae4404: ldr             x9, [x9, #0x370]
    // 0xae4408: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae4408: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae440c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae440c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RepaintBoundary <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xae4434, size: 0x68
    // 0xae4434: EnterFrame
    //     0xae4434: stp             fp, lr, [SP, #-0x10]!
    //     0xae4438: mov             fp, SP
    // 0xae443c: AllocStack(0x8)
    //     0xae443c: sub             SP, SP, #8
    // 0xae4440: SetupParameters()
    //     0xae4440: ldr             x0, [fp, #0x20]
    //     0xae4444: ldur            w1, [x0, #0x17]
    //     0xae4448: add             x1, x1, HEAP, lsl #32
    // 0xae444c: CheckStackOverflow
    //     0xae444c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4450: cmp             SP, x16
    //     0xae4454: b.ls            #0xae4494
    // 0xae4458: LoadField: r0 = r1->field_f
    //     0xae4458: ldur            w0, [x1, #0xf]
    // 0xae445c: DecompressPointer r0
    //     0xae445c: add             x0, x0, HEAP, lsl #32
    // 0xae4460: ldr             x1, [fp, #0x10]
    // 0xae4464: r2 = LoadInt32Instr(r1)
    //     0xae4464: sbfx            x2, x1, #1, #0x1f
    //     0xae4468: tbz             w1, #0, #0xae4470
    //     0xae446c: ldur            x2, [x1, #7]
    // 0xae4470: mov             x1, x0
    // 0xae4474: r0 = _buildCarouselPage()
    //     0xae4474: bl              #0xae449c  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage
    // 0xae4478: stur            x0, [fp, #-8]
    // 0xae447c: r0 = RepaintBoundary()
    //     0xae447c: bl              #0xa4360c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xae4480: ldur            x1, [fp, #-8]
    // 0xae4484: StoreField: r0->field_b = r1
    //     0xae4484: stur            w1, [x0, #0xb]
    // 0xae4488: LeaveFrame
    //     0xae4488: mov             SP, fp
    //     0xae448c: ldp             fp, lr, [SP], #0x10
    // 0xae4490: ret
    //     0xae4490: ret             
    // 0xae4494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4494: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4498: b               #0xae4458
  }
  _ _buildCarouselPage(/* No info */) {
    // ** addr: 0xae449c, size: 0x1c4
    // 0xae449c: EnterFrame
    //     0xae449c: stp             fp, lr, [SP, #-0x10]!
    //     0xae44a0: mov             fp, SP
    // 0xae44a4: AllocStack(0x38)
    //     0xae44a4: sub             SP, SP, #0x38
    // 0xae44a8: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae44a8: stur            x1, [fp, #-8]
    //     0xae44ac: stur            x2, [fp, #-0x10]
    // 0xae44b0: CheckStackOverflow
    //     0xae44b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae44b4: cmp             SP, x16
    //     0xae44b8: b.ls            #0xae4650
    // 0xae44bc: r1 = 1
    //     0xae44bc: movz            x1, #0x1
    // 0xae44c0: r0 = AllocateContext()
    //     0xae44c0: bl              #0x16f6108  ; AllocateContextStub
    // 0xae44c4: mov             x5, x0
    // 0xae44c8: ldur            x4, [fp, #-8]
    // 0xae44cc: stur            x5, [fp, #-0x20]
    // 0xae44d0: StoreField: r5->field_f = r4
    //     0xae44d0: stur            w4, [x5, #0xf]
    // 0xae44d4: ldur            x0, [fp, #-0x10]
    // 0xae44d8: r16 = 3
    //     0xae44d8: movz            x16, #0x3
    // 0xae44dc: mul             x6, x0, x16
    // 0xae44e0: stur            x6, [fp, #-0x18]
    // 0xae44e4: add             x2, x6, #3
    // 0xae44e8: LoadField: r0 = r4->field_b
    //     0xae44e8: ldur            w0, [x4, #0xb]
    // 0xae44ec: DecompressPointer r0
    //     0xae44ec: add             x0, x0, HEAP, lsl #32
    // 0xae44f0: cmp             w0, NULL
    // 0xae44f4: b.eq            #0xae4658
    // 0xae44f8: LoadField: r1 = r0->field_b
    //     0xae44f8: ldur            w1, [x0, #0xb]
    // 0xae44fc: DecompressPointer r1
    //     0xae44fc: add             x1, x1, HEAP, lsl #32
    // 0xae4500: cmp             w1, NULL
    // 0xae4504: b.ne            #0xae4510
    // 0xae4508: r0 = Null
    //     0xae4508: mov             x0, NULL
    // 0xae450c: b               #0xae4514
    // 0xae4510: LoadField: r0 = r1->field_b
    //     0xae4510: ldur            w0, [x1, #0xb]
    // 0xae4514: cmp             w0, NULL
    // 0xae4518: b.ne            #0xae4524
    // 0xae451c: r3 = 0
    //     0xae451c: movz            x3, #0
    // 0xae4520: b               #0xae452c
    // 0xae4524: r1 = LoadInt32Instr(r0)
    //     0xae4524: sbfx            x1, x0, #1, #0x1f
    // 0xae4528: mov             x3, x1
    // 0xae452c: r0 = BoxInt64Instr(r2)
    //     0xae452c: sbfiz           x0, x2, #1, #0x1f
    //     0xae4530: cmp             x2, x0, asr #1
    //     0xae4534: b.eq            #0xae4540
    //     0xae4538: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae453c: stur            x2, [x0, #7]
    // 0xae4540: lsl             x1, x3, #1
    // 0xae4544: mov             x3, x1
    // 0xae4548: mov             x1, x0
    // 0xae454c: r2 = 0
    //     0xae454c: movz            x2, #0
    // 0xae4550: r0 = clamp()
    //     0xae4550: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xae4554: mov             x1, x0
    // 0xae4558: ldur            x0, [fp, #-8]
    // 0xae455c: LoadField: r2 = r0->field_b
    //     0xae455c: ldur            w2, [x0, #0xb]
    // 0xae4560: DecompressPointer r2
    //     0xae4560: add             x2, x2, HEAP, lsl #32
    // 0xae4564: cmp             w2, NULL
    // 0xae4568: b.eq            #0xae465c
    // 0xae456c: LoadField: r0 = r2->field_b
    //     0xae456c: ldur            w0, [x2, #0xb]
    // 0xae4570: DecompressPointer r0
    //     0xae4570: add             x0, x0, HEAP, lsl #32
    // 0xae4574: cmp             w0, NULL
    // 0xae4578: b.ne            #0xae4584
    // 0xae457c: r0 = Null
    //     0xae457c: mov             x0, NULL
    // 0xae4580: b               #0xae4598
    // 0xae4584: str             x1, [SP]
    // 0xae4588: mov             x1, x0
    // 0xae458c: ldur            x2, [fp, #-0x18]
    // 0xae4590: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xae4590: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xae4594: r0 = sublist()
    //     0xae4594: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xae4598: cmp             w0, NULL
    // 0xae459c: b.ne            #0xae45b0
    // 0xae45a0: r1 = <Entity>
    //     0xae45a0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xae45a4: ldr             x1, [x1, #0xb68]
    // 0xae45a8: r2 = 0
    //     0xae45a8: movz            x2, #0
    // 0xae45ac: r0 = _GrowableList()
    //     0xae45ac: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xae45b0: ldur            x2, [fp, #-0x20]
    // 0xae45b4: stur            x0, [fp, #-8]
    // 0xae45b8: r1 = Function '<anonymous closure>':.
    //     0xae45b8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58378] AnonymousClosure: (0xae4660), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xae449c)
    //     0xae45bc: ldr             x1, [x1, #0x378]
    // 0xae45c0: r0 = AllocateClosure()
    //     0xae45c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae45c4: r16 = <GestureDetector>
    //     0xae45c4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53890] TypeArguments: <GestureDetector>
    //     0xae45c8: ldr             x16, [x16, #0x890]
    // 0xae45cc: ldur            lr, [fp, #-8]
    // 0xae45d0: stp             lr, x16, [SP, #8]
    // 0xae45d4: str             x0, [SP]
    // 0xae45d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae45d8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae45dc: r0 = map()
    //     0xae45dc: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xae45e0: mov             x1, x0
    // 0xae45e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae45e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae45e8: r0 = toList()
    //     0xae45e8: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xae45ec: stur            x0, [fp, #-8]
    // 0xae45f0: r0 = Row()
    //     0xae45f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xae45f4: r1 = Instance_Axis
    //     0xae45f4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xae45f8: StoreField: r0->field_f = r1
    //     0xae45f8: stur            w1, [x0, #0xf]
    // 0xae45fc: r1 = Instance_MainAxisAlignment
    //     0xae45fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae4600: ldr             x1, [x1, #0xa08]
    // 0xae4604: StoreField: r0->field_13 = r1
    //     0xae4604: stur            w1, [x0, #0x13]
    // 0xae4608: r1 = Instance_MainAxisSize
    //     0xae4608: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae460c: ldr             x1, [x1, #0xa10]
    // 0xae4610: ArrayStore: r0[0] = r1  ; List_4
    //     0xae4610: stur            w1, [x0, #0x17]
    // 0xae4614: r1 = Instance_CrossAxisAlignment
    //     0xae4614: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae4618: ldr             x1, [x1, #0xa18]
    // 0xae461c: StoreField: r0->field_1b = r1
    //     0xae461c: stur            w1, [x0, #0x1b]
    // 0xae4620: r1 = Instance_VerticalDirection
    //     0xae4620: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae4624: ldr             x1, [x1, #0xa20]
    // 0xae4628: StoreField: r0->field_23 = r1
    //     0xae4628: stur            w1, [x0, #0x23]
    // 0xae462c: r1 = Instance_Clip
    //     0xae462c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae4630: ldr             x1, [x1, #0x38]
    // 0xae4634: StoreField: r0->field_2b = r1
    //     0xae4634: stur            w1, [x0, #0x2b]
    // 0xae4638: StoreField: r0->field_2f = rZR
    //     0xae4638: stur            xzr, [x0, #0x2f]
    // 0xae463c: ldur            x1, [fp, #-8]
    // 0xae4640: StoreField: r0->field_b = r1
    //     0xae4640: stur            w1, [x0, #0xb]
    // 0xae4644: LeaveFrame
    //     0xae4644: mov             SP, fp
    //     0xae4648: ldp             fp, lr, [SP], #0x10
    // 0xae464c: ret
    //     0xae464c: ret             
    // 0xae4650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4650: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4654: b               #0xae44bc
    // 0xae4658: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4658: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae465c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae465c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, Entity) {
    // ** addr: 0xae4660, size: 0x320
    // 0xae4660: EnterFrame
    //     0xae4660: stp             fp, lr, [SP, #-0x10]!
    //     0xae4664: mov             fp, SP
    // 0xae4668: AllocStack(0x48)
    //     0xae4668: sub             SP, SP, #0x48
    // 0xae466c: SetupParameters()
    //     0xae466c: ldr             x0, [fp, #0x18]
    //     0xae4670: ldur            w1, [x0, #0x17]
    //     0xae4674: add             x1, x1, HEAP, lsl #32
    //     0xae4678: stur            x1, [fp, #-8]
    // 0xae467c: CheckStackOverflow
    //     0xae467c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4680: cmp             SP, x16
    //     0xae4684: b.ls            #0xae4950
    // 0xae4688: r1 = 1
    //     0xae4688: movz            x1, #0x1
    // 0xae468c: r0 = AllocateContext()
    //     0xae468c: bl              #0x16f6108  ; AllocateContextStub
    // 0xae4690: mov             x2, x0
    // 0xae4694: ldur            x0, [fp, #-8]
    // 0xae4698: stur            x2, [fp, #-0x10]
    // 0xae469c: StoreField: r2->field_b = r0
    //     0xae469c: stur            w0, [x2, #0xb]
    // 0xae46a0: ldr             x1, [fp, #0x10]
    // 0xae46a4: StoreField: r2->field_f = r1
    //     0xae46a4: stur            w1, [x2, #0xf]
    // 0xae46a8: LoadField: r1 = r0->field_f
    //     0xae46a8: ldur            w1, [x0, #0xf]
    // 0xae46ac: DecompressPointer r1
    //     0xae46ac: add             x1, x1, HEAP, lsl #32
    // 0xae46b0: LoadField: r3 = r1->field_f
    //     0xae46b0: ldur            w3, [x1, #0xf]
    // 0xae46b4: DecompressPointer r3
    //     0xae46b4: add             x3, x3, HEAP, lsl #32
    // 0xae46b8: cmp             w3, NULL
    // 0xae46bc: b.eq            #0xae4958
    // 0xae46c0: mov             x1, x3
    // 0xae46c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae46c4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae46c8: r0 = _of()
    //     0xae46c8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae46cc: LoadField: r1 = r0->field_7
    //     0xae46cc: ldur            w1, [x0, #7]
    // 0xae46d0: DecompressPointer r1
    //     0xae46d0: add             x1, x1, HEAP, lsl #32
    // 0xae46d4: LoadField: d0 = r1->field_7
    //     0xae46d4: ldur            d0, [x1, #7]
    // 0xae46d8: d1 = 3.000000
    //     0xae46d8: fmov            d1, #3.00000000
    // 0xae46dc: fdiv            d2, d0, d1
    // 0xae46e0: d0 = 16.000000
    //     0xae46e0: fmov            d0, #16.00000000
    // 0xae46e4: fsub            d1, d2, d0
    // 0xae46e8: ldur            x0, [fp, #-8]
    // 0xae46ec: stur            d1, [fp, #-0x30]
    // 0xae46f0: LoadField: r1 = r0->field_f
    //     0xae46f0: ldur            w1, [x0, #0xf]
    // 0xae46f4: DecompressPointer r1
    //     0xae46f4: add             x1, x1, HEAP, lsl #32
    // 0xae46f8: ldur            x3, [fp, #-0x10]
    // 0xae46fc: LoadField: r2 = r3->field_f
    //     0xae46fc: ldur            w2, [x3, #0xf]
    // 0xae4700: DecompressPointer r2
    //     0xae4700: add             x2, x2, HEAP, lsl #32
    // 0xae4704: LoadField: r4 = r2->field_13
    //     0xae4704: ldur            w4, [x2, #0x13]
    // 0xae4708: DecompressPointer r4
    //     0xae4708: add             x4, x4, HEAP, lsl #32
    // 0xae470c: mov             x2, x4
    // 0xae4710: r0 = _buildImageContainer()
    //     0xae4710: bl              #0xae4980  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildImageContainer
    // 0xae4714: stur            x0, [fp, #-0x18]
    // 0xae4718: r0 = Padding()
    //     0xae4718: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae471c: mov             x1, x0
    // 0xae4720: r0 = Instance_EdgeInsets
    //     0xae4720: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xae4724: ldr             x0, [x0, #0x100]
    // 0xae4728: stur            x1, [fp, #-0x20]
    // 0xae472c: StoreField: r1->field_f = r0
    //     0xae472c: stur            w0, [x1, #0xf]
    // 0xae4730: ldur            x0, [fp, #-0x18]
    // 0xae4734: StoreField: r1->field_b = r0
    //     0xae4734: stur            w0, [x1, #0xb]
    // 0xae4738: ldur            x2, [fp, #-0x10]
    // 0xae473c: LoadField: r0 = r2->field_f
    //     0xae473c: ldur            w0, [x2, #0xf]
    // 0xae4740: DecompressPointer r0
    //     0xae4740: add             x0, x0, HEAP, lsl #32
    // 0xae4744: LoadField: r3 = r0->field_7
    //     0xae4744: ldur            w3, [x0, #7]
    // 0xae4748: DecompressPointer r3
    //     0xae4748: add             x3, x3, HEAP, lsl #32
    // 0xae474c: cmp             w3, NULL
    // 0xae4750: b.ne            #0xae475c
    // 0xae4754: r0 = Null
    //     0xae4754: mov             x0, NULL
    // 0xae4758: b               #0xae4774
    // 0xae475c: r0 = LoadClassIdInstr(r3)
    //     0xae475c: ldur            x0, [x3, #-1]
    //     0xae4760: ubfx            x0, x0, #0xc, #0x14
    // 0xae4764: str             x3, [SP]
    // 0xae4768: r0 = GDT[cid_x0 + -0x1000]()
    //     0xae4768: sub             lr, x0, #1, lsl #12
    //     0xae476c: ldr             lr, [x21, lr, lsl #3]
    //     0xae4770: blr             lr
    // 0xae4774: cmp             w0, NULL
    // 0xae4778: b.ne            #0xae4784
    // 0xae477c: r2 = ""
    //     0xae477c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae4780: b               #0xae4788
    // 0xae4784: mov             x2, x0
    // 0xae4788: ldur            x1, [fp, #-8]
    // 0xae478c: ldur            d0, [fp, #-0x30]
    // 0xae4790: ldur            x0, [fp, #-0x20]
    // 0xae4794: stur            x2, [fp, #-0x18]
    // 0xae4798: LoadField: r3 = r1->field_f
    //     0xae4798: ldur            w3, [x1, #0xf]
    // 0xae479c: DecompressPointer r3
    //     0xae479c: add             x3, x3, HEAP, lsl #32
    // 0xae47a0: LoadField: r1 = r3->field_23
    //     0xae47a0: ldur            w1, [x3, #0x23]
    // 0xae47a4: DecompressPointer r1
    //     0xae47a4: add             x1, x1, HEAP, lsl #32
    // 0xae47a8: r16 = Sentinel
    //     0xae47a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae47ac: cmp             w1, w16
    // 0xae47b0: b.eq            #0xae495c
    // 0xae47b4: stur            x1, [fp, #-8]
    // 0xae47b8: r0 = Text()
    //     0xae47b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae47bc: mov             x1, x0
    // 0xae47c0: ldur            x0, [fp, #-0x18]
    // 0xae47c4: stur            x1, [fp, #-0x28]
    // 0xae47c8: StoreField: r1->field_b = r0
    //     0xae47c8: stur            w0, [x1, #0xb]
    // 0xae47cc: ldur            x0, [fp, #-8]
    // 0xae47d0: StoreField: r1->field_13 = r0
    //     0xae47d0: stur            w0, [x1, #0x13]
    // 0xae47d4: r0 = Instance_TextAlign
    //     0xae47d4: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xae47d8: StoreField: r1->field_1b = r0
    //     0xae47d8: stur            w0, [x1, #0x1b]
    // 0xae47dc: r0 = Instance_TextOverflow
    //     0xae47dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xae47e0: ldr             x0, [x0, #0xe10]
    // 0xae47e4: StoreField: r1->field_2b = r0
    //     0xae47e4: stur            w0, [x1, #0x2b]
    // 0xae47e8: r2 = 4
    //     0xae47e8: movz            x2, #0x4
    // 0xae47ec: StoreField: r1->field_37 = r2
    //     0xae47ec: stur            w2, [x1, #0x37]
    // 0xae47f0: r0 = Padding()
    //     0xae47f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae47f4: mov             x3, x0
    // 0xae47f8: r0 = Instance_EdgeInsets
    //     0xae47f8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xae47fc: ldr             x0, [x0, #0x770]
    // 0xae4800: stur            x3, [fp, #-8]
    // 0xae4804: StoreField: r3->field_f = r0
    //     0xae4804: stur            w0, [x3, #0xf]
    // 0xae4808: ldur            x0, [fp, #-0x28]
    // 0xae480c: StoreField: r3->field_b = r0
    //     0xae480c: stur            w0, [x3, #0xb]
    // 0xae4810: r1 = Null
    //     0xae4810: mov             x1, NULL
    // 0xae4814: r2 = 4
    //     0xae4814: movz            x2, #0x4
    // 0xae4818: r0 = AllocateArray()
    //     0xae4818: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae481c: mov             x2, x0
    // 0xae4820: ldur            x0, [fp, #-0x20]
    // 0xae4824: stur            x2, [fp, #-0x18]
    // 0xae4828: StoreField: r2->field_f = r0
    //     0xae4828: stur            w0, [x2, #0xf]
    // 0xae482c: ldur            x0, [fp, #-8]
    // 0xae4830: StoreField: r2->field_13 = r0
    //     0xae4830: stur            w0, [x2, #0x13]
    // 0xae4834: r1 = <Widget>
    //     0xae4834: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae4838: r0 = AllocateGrowableArray()
    //     0xae4838: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae483c: mov             x1, x0
    // 0xae4840: ldur            x0, [fp, #-0x18]
    // 0xae4844: stur            x1, [fp, #-8]
    // 0xae4848: StoreField: r1->field_f = r0
    //     0xae4848: stur            w0, [x1, #0xf]
    // 0xae484c: r0 = 4
    //     0xae484c: movz            x0, #0x4
    // 0xae4850: StoreField: r1->field_b = r0
    //     0xae4850: stur            w0, [x1, #0xb]
    // 0xae4854: r0 = Column()
    //     0xae4854: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae4858: mov             x1, x0
    // 0xae485c: r0 = Instance_Axis
    //     0xae485c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae4860: stur            x1, [fp, #-0x18]
    // 0xae4864: StoreField: r1->field_f = r0
    //     0xae4864: stur            w0, [x1, #0xf]
    // 0xae4868: r0 = Instance_MainAxisAlignment
    //     0xae4868: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae486c: ldr             x0, [x0, #0xa08]
    // 0xae4870: StoreField: r1->field_13 = r0
    //     0xae4870: stur            w0, [x1, #0x13]
    // 0xae4874: r0 = Instance_MainAxisSize
    //     0xae4874: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae4878: ldr             x0, [x0, #0xa10]
    // 0xae487c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae487c: stur            w0, [x1, #0x17]
    // 0xae4880: r0 = Instance_CrossAxisAlignment
    //     0xae4880: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae4884: ldr             x0, [x0, #0xa18]
    // 0xae4888: StoreField: r1->field_1b = r0
    //     0xae4888: stur            w0, [x1, #0x1b]
    // 0xae488c: r0 = Instance_VerticalDirection
    //     0xae488c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae4890: ldr             x0, [x0, #0xa20]
    // 0xae4894: StoreField: r1->field_23 = r0
    //     0xae4894: stur            w0, [x1, #0x23]
    // 0xae4898: r0 = Instance_Clip
    //     0xae4898: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae489c: ldr             x0, [x0, #0x38]
    // 0xae48a0: StoreField: r1->field_2b = r0
    //     0xae48a0: stur            w0, [x1, #0x2b]
    // 0xae48a4: StoreField: r1->field_2f = rZR
    //     0xae48a4: stur            xzr, [x1, #0x2f]
    // 0xae48a8: ldur            x0, [fp, #-8]
    // 0xae48ac: StoreField: r1->field_b = r0
    //     0xae48ac: stur            w0, [x1, #0xb]
    // 0xae48b0: ldur            d0, [fp, #-0x30]
    // 0xae48b4: r0 = inline_Allocate_Double()
    //     0xae48b4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae48b8: add             x0, x0, #0x10
    //     0xae48bc: cmp             x2, x0
    //     0xae48c0: b.ls            #0xae4968
    //     0xae48c4: str             x0, [THR, #0x50]  ; THR::top
    //     0xae48c8: sub             x0, x0, #0xf
    //     0xae48cc: movz            x2, #0xe15c
    //     0xae48d0: movk            x2, #0x3, lsl #16
    //     0xae48d4: stur            x2, [x0, #-1]
    // 0xae48d8: StoreField: r0->field_7 = d0
    //     0xae48d8: stur            d0, [x0, #7]
    // 0xae48dc: stur            x0, [fp, #-8]
    // 0xae48e0: r0 = Container()
    //     0xae48e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae48e4: stur            x0, [fp, #-0x20]
    // 0xae48e8: ldur            x16, [fp, #-8]
    // 0xae48ec: r30 = Instance_EdgeInsets
    //     0xae48ec: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xae48f0: ldr             lr, [lr, #0xa68]
    // 0xae48f4: stp             lr, x16, [SP, #8]
    // 0xae48f8: ldur            x16, [fp, #-0x18]
    // 0xae48fc: str             x16, [SP]
    // 0xae4900: mov             x1, x0
    // 0xae4904: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xae4904: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xae4908: ldr             x4, [x4, #0x628]
    // 0xae490c: r0 = Container()
    //     0xae490c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae4910: r0 = GestureDetector()
    //     0xae4910: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xae4914: ldur            x2, [fp, #-0x10]
    // 0xae4918: r1 = Function '<anonymous closure>':.
    //     0xae4918: add             x1, PP, #0x58, lsl #12  ; [pp+0x58380] AnonymousClosure: (0xae4ab4), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xae449c)
    //     0xae491c: ldr             x1, [x1, #0x380]
    // 0xae4920: stur            x0, [fp, #-8]
    // 0xae4924: r0 = AllocateClosure()
    //     0xae4924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae4928: ldur            x16, [fp, #-0x20]
    // 0xae492c: stp             x16, x0, [SP]
    // 0xae4930: ldur            x1, [fp, #-8]
    // 0xae4934: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xae4934: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xae4938: ldr             x4, [x4, #0xaf0]
    // 0xae493c: r0 = GestureDetector()
    //     0xae493c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xae4940: ldur            x0, [fp, #-8]
    // 0xae4944: LeaveFrame
    //     0xae4944: mov             SP, fp
    //     0xae4948: ldp             fp, lr, [SP], #0x10
    // 0xae494c: ret
    //     0xae494c: ret             
    // 0xae4950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4950: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4954: b               #0xae4688
    // 0xae4958: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4958: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae495c: r9 = _titleStyle
    //     0xae495c: add             x9, PP, #0x58, lsl #12  ; [pp+0x58388] Field <_CategoryGroupCarouselItemViewState@1464505729._titleStyle@1464505729>: late (offset: 0x24)
    //     0xae4960: ldr             x9, [x9, #0x388]
    // 0xae4964: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xae4964: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xae4968: SaveReg d0
    //     0xae4968: str             q0, [SP, #-0x10]!
    // 0xae496c: SaveReg r1
    //     0xae496c: str             x1, [SP, #-8]!
    // 0xae4970: r0 = AllocateDouble()
    //     0xae4970: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xae4974: RestoreReg r1
    //     0xae4974: ldr             x1, [SP], #8
    // 0xae4978: RestoreReg d0
    //     0xae4978: ldr             q0, [SP], #0x10
    // 0xae497c: b               #0xae48d8
  }
  _ _buildImageContainer(/* No info */) {
    // ** addr: 0xae4980, size: 0x134
    // 0xae4980: EnterFrame
    //     0xae4980: stp             fp, lr, [SP, #-0x10]!
    //     0xae4984: mov             fp, SP
    // 0xae4988: AllocStack(0x30)
    //     0xae4988: sub             SP, SP, #0x30
    // 0xae498c: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae498c: stur            x2, [fp, #-0x10]
    // 0xae4990: CheckStackOverflow
    //     0xae4990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4994: cmp             SP, x16
    //     0xae4998: b.ls            #0xae4a94
    // 0xae499c: cmp             w2, NULL
    // 0xae49a0: b.eq            #0xae49ac
    // 0xae49a4: LoadField: r0 = r2->field_7
    //     0xae49a4: ldur            w0, [x2, #7]
    // 0xae49a8: cbnz            w0, #0xae4a08
    // 0xae49ac: LoadField: r0 = r1->field_1f
    //     0xae49ac: ldur            w0, [x1, #0x1f]
    // 0xae49b0: DecompressPointer r0
    //     0xae49b0: add             x0, x0, HEAP, lsl #32
    // 0xae49b4: r16 = Sentinel
    //     0xae49b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae49b8: cmp             w0, w16
    // 0xae49bc: b.eq            #0xae4a9c
    // 0xae49c0: r0 = Container()
    //     0xae49c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae49c4: stur            x0, [fp, #-8]
    // 0xae49c8: r16 = 84.000000
    //     0xae49c8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xae49cc: ldr             x16, [x16, #0xf90]
    // 0xae49d0: r30 = 84.000000
    //     0xae49d0: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xae49d4: ldr             lr, [lr, #0xf90]
    // 0xae49d8: stp             lr, x16, [SP, #8]
    // 0xae49dc: r16 = Instance_BoxDecoration
    //     0xae49dc: add             x16, PP, #0x53, lsl #12  ; [pp+0x538b8] Obj!BoxDecoration@d649b1
    //     0xae49e0: ldr             x16, [x16, #0x8b8]
    // 0xae49e4: str             x16, [SP]
    // 0xae49e8: mov             x1, x0
    // 0xae49ec: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xae49ec: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xae49f0: ldr             x4, [x4, #0x468]
    // 0xae49f4: r0 = Container()
    //     0xae49f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae49f8: ldur            x0, [fp, #-8]
    // 0xae49fc: LeaveFrame
    //     0xae49fc: mov             SP, fp
    //     0xae4a00: ldp             fp, lr, [SP], #0x10
    // 0xae4a04: ret
    //     0xae4a04: ret             
    // 0xae4a08: LoadField: r0 = r1->field_1f
    //     0xae4a08: ldur            w0, [x1, #0x1f]
    // 0xae4a0c: DecompressPointer r0
    //     0xae4a0c: add             x0, x0, HEAP, lsl #32
    // 0xae4a10: r16 = Sentinel
    //     0xae4a10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4a14: cmp             w0, w16
    // 0xae4a18: b.eq            #0xae4aa8
    // 0xae4a1c: r0 = ImageHeaders.forImages()
    //     0xae4a1c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xae4a20: r1 = <CachedNetworkImageProvider>
    //     0xae4a20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb10] TypeArguments: <CachedNetworkImageProvider>
    //     0xae4a24: ldr             x1, [x1, #0xb10]
    // 0xae4a28: stur            x0, [fp, #-8]
    // 0xae4a2c: r0 = CachedNetworkImageProvider()
    //     0xae4a2c: bl              #0x859df8  ; AllocateCachedNetworkImageProviderStub -> CachedNetworkImageProvider (size=0x34)
    // 0xae4a30: mov             x1, x0
    // 0xae4a34: ldur            x0, [fp, #-0x10]
    // 0xae4a38: stur            x1, [fp, #-0x18]
    // 0xae4a3c: StoreField: r1->field_f = r0
    //     0xae4a3c: stur            w0, [x1, #0xf]
    // 0xae4a40: r0 = 336
    //     0xae4a40: movz            x0, #0x150
    // 0xae4a44: StoreField: r1->field_27 = r0
    //     0xae4a44: stur            w0, [x1, #0x27]
    // 0xae4a48: StoreField: r1->field_2b = r0
    //     0xae4a48: stur            w0, [x1, #0x2b]
    // 0xae4a4c: d0 = 1.000000
    //     0xae4a4c: fmov            d0, #1.00000000
    // 0xae4a50: ArrayStore: r1[0] = d0  ; List_8
    //     0xae4a50: stur            d0, [x1, #0x17]
    // 0xae4a54: ldur            x0, [fp, #-8]
    // 0xae4a58: StoreField: r1->field_23 = r0
    //     0xae4a58: stur            w0, [x1, #0x23]
    // 0xae4a5c: r0 = Instance_ImageRenderMethodForWeb
    //     0xae4a5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb18] Obj!ImageRenderMethodForWeb@d75981
    //     0xae4a60: ldr             x0, [x0, #0xb18]
    // 0xae4a64: StoreField: r1->field_2f = r0
    //     0xae4a64: stur            w0, [x1, #0x2f]
    // 0xae4a68: r0 = CircleAvatar()
    //     0xae4a68: bl              #0xa43c2c  ; AllocateCircleAvatarStub -> CircleAvatar (size=0x38)
    // 0xae4a6c: r1 = Instance_Color
    //     0xae4a6c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xae4a70: ldr             x1, [x1, #0xf88]
    // 0xae4a74: StoreField: r0->field_f = r1
    //     0xae4a74: stur            w1, [x0, #0xf]
    // 0xae4a78: ldur            x1, [fp, #-0x18]
    // 0xae4a7c: ArrayStore: r0[0] = r1  ; List_4
    //     0xae4a7c: stur            w1, [x0, #0x17]
    // 0xae4a80: d0 = 42.000000
    //     0xae4a80: ldr             d0, [PP, #0x5ae0]  ; [pp+0x5ae0] IMM: double(42) from 0x4045000000000000
    // 0xae4a84: StoreField: r0->field_27 = d0
    //     0xae4a84: stur            d0, [x0, #0x27]
    // 0xae4a88: LeaveFrame
    //     0xae4a88: mov             SP, fp
    //     0xae4a8c: ldp             fp, lr, [SP], #0x10
    // 0xae4a90: ret
    //     0xae4a90: ret             
    // 0xae4a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4a94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4a98: b               #0xae499c
    // 0xae4a9c: r9 = _imageSize
    //     0xae4a9c: add             x9, PP, #0x58, lsl #12  ; [pp+0x583a0] Field <_CategoryGroupCarouselItemViewState@1464505729._imageSize@1464505729>: late (offset: 0x20)
    //     0xae4aa0: ldr             x9, [x9, #0x3a0]
    // 0xae4aa4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae4aa4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae4aa8: r9 = _imageSize
    //     0xae4aa8: add             x9, PP, #0x58, lsl #12  ; [pp+0x583a0] Field <_CategoryGroupCarouselItemViewState@1464505729._imageSize@1464505729>: late (offset: 0x20)
    //     0xae4aac: ldr             x9, [x9, #0x3a0]
    // 0xae4ab0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae4ab0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae4ab4, size: 0xd0
    // 0xae4ab4: EnterFrame
    //     0xae4ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xae4ab8: mov             fp, SP
    // 0xae4abc: AllocStack(0x40)
    //     0xae4abc: sub             SP, SP, #0x40
    // 0xae4ac0: SetupParameters()
    //     0xae4ac0: ldr             x0, [fp, #0x10]
    //     0xae4ac4: ldur            w1, [x0, #0x17]
    //     0xae4ac8: add             x1, x1, HEAP, lsl #32
    // 0xae4acc: CheckStackOverflow
    //     0xae4acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4ad0: cmp             SP, x16
    //     0xae4ad4: b.ls            #0xae4b78
    // 0xae4ad8: LoadField: r0 = r1->field_b
    //     0xae4ad8: ldur            w0, [x1, #0xb]
    // 0xae4adc: DecompressPointer r0
    //     0xae4adc: add             x0, x0, HEAP, lsl #32
    // 0xae4ae0: LoadField: r2 = r0->field_f
    //     0xae4ae0: ldur            w2, [x0, #0xf]
    // 0xae4ae4: DecompressPointer r2
    //     0xae4ae4: add             x2, x2, HEAP, lsl #32
    // 0xae4ae8: LoadField: r0 = r2->field_b
    //     0xae4ae8: ldur            w0, [x2, #0xb]
    // 0xae4aec: DecompressPointer r0
    //     0xae4aec: add             x0, x0, HEAP, lsl #32
    // 0xae4af0: cmp             w0, NULL
    // 0xae4af4: b.eq            #0xae4b80
    // 0xae4af8: LoadField: r2 = r0->field_13
    //     0xae4af8: ldur            w2, [x0, #0x13]
    // 0xae4afc: DecompressPointer r2
    //     0xae4afc: add             x2, x2, HEAP, lsl #32
    // 0xae4b00: LoadField: r3 = r0->field_f
    //     0xae4b00: ldur            w3, [x0, #0xf]
    // 0xae4b04: DecompressPointer r3
    //     0xae4b04: add             x3, x3, HEAP, lsl #32
    // 0xae4b08: LoadField: r4 = r0->field_23
    //     0xae4b08: ldur            w4, [x0, #0x23]
    // 0xae4b0c: DecompressPointer r4
    //     0xae4b0c: add             x4, x4, HEAP, lsl #32
    // 0xae4b10: LoadField: r5 = r0->field_1b
    //     0xae4b10: ldur            w5, [x0, #0x1b]
    // 0xae4b14: DecompressPointer r5
    //     0xae4b14: add             x5, x5, HEAP, lsl #32
    // 0xae4b18: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xae4b18: ldur            w6, [x0, #0x17]
    // 0xae4b1c: DecompressPointer r6
    //     0xae4b1c: add             x6, x6, HEAP, lsl #32
    // 0xae4b20: LoadField: r7 = r0->field_27
    //     0xae4b20: ldur            w7, [x0, #0x27]
    // 0xae4b24: DecompressPointer r7
    //     0xae4b24: add             x7, x7, HEAP, lsl #32
    // 0xae4b28: LoadField: r8 = r1->field_f
    //     0xae4b28: ldur            w8, [x1, #0xf]
    // 0xae4b2c: DecompressPointer r8
    //     0xae4b2c: add             x8, x8, HEAP, lsl #32
    // 0xae4b30: ArrayLoad: r1 = r8[0]  ; List_4
    //     0xae4b30: ldur            w1, [x8, #0x17]
    // 0xae4b34: DecompressPointer r1
    //     0xae4b34: add             x1, x1, HEAP, lsl #32
    // 0xae4b38: LoadField: r8 = r0->field_1f
    //     0xae4b38: ldur            w8, [x0, #0x1f]
    // 0xae4b3c: DecompressPointer r8
    //     0xae4b3c: add             x8, x8, HEAP, lsl #32
    // 0xae4b40: stp             x2, x8, [SP, #0x30]
    // 0xae4b44: stp             x4, x3, [SP, #0x20]
    // 0xae4b48: stp             x6, x5, [SP, #0x10]
    // 0xae4b4c: stp             x1, x7, [SP]
    // 0xae4b50: r4 = 0
    //     0xae4b50: movz            x4, #0
    // 0xae4b54: ldr             x0, [SP, #0x38]
    // 0xae4b58: r16 = UnlinkedCall_0x613b5c
    //     0xae4b58: add             x16, PP, #0x58, lsl #12  ; [pp+0x58390] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae4b5c: add             x16, x16, #0x390
    // 0xae4b60: ldp             x5, lr, [x16]
    // 0xae4b64: blr             lr
    // 0xae4b68: r0 = Null
    //     0xae4b68: mov             x0, NULL
    // 0xae4b6c: LeaveFrame
    //     0xae4b6c: mov             SP, fp
    //     0xae4b70: ldp             fp, lr, [SP], #0x10
    // 0xae4b74: ret
    //     0xae4b74: ret             
    // 0xae4b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4b78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4b7c: b               #0xae4ad8
    // 0xae4b80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4b80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xae4b84, size: 0x84
    // 0xae4b84: EnterFrame
    //     0xae4b84: stp             fp, lr, [SP, #-0x10]!
    //     0xae4b88: mov             fp, SP
    // 0xae4b8c: AllocStack(0x10)
    //     0xae4b8c: sub             SP, SP, #0x10
    // 0xae4b90: SetupParameters()
    //     0xae4b90: ldr             x0, [fp, #0x18]
    //     0xae4b94: ldur            w1, [x0, #0x17]
    //     0xae4b98: add             x1, x1, HEAP, lsl #32
    //     0xae4b9c: stur            x1, [fp, #-8]
    // 0xae4ba0: CheckStackOverflow
    //     0xae4ba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4ba4: cmp             SP, x16
    //     0xae4ba8: b.ls            #0xae4c00
    // 0xae4bac: r1 = 1
    //     0xae4bac: movz            x1, #0x1
    // 0xae4bb0: r0 = AllocateContext()
    //     0xae4bb0: bl              #0x16f6108  ; AllocateContextStub
    // 0xae4bb4: mov             x1, x0
    // 0xae4bb8: ldur            x0, [fp, #-8]
    // 0xae4bbc: StoreField: r1->field_b = r0
    //     0xae4bbc: stur            w0, [x1, #0xb]
    // 0xae4bc0: ldr             x2, [fp, #0x10]
    // 0xae4bc4: StoreField: r1->field_f = r2
    //     0xae4bc4: stur            w2, [x1, #0xf]
    // 0xae4bc8: LoadField: r3 = r0->field_f
    //     0xae4bc8: ldur            w3, [x0, #0xf]
    // 0xae4bcc: DecompressPointer r3
    //     0xae4bcc: add             x3, x3, HEAP, lsl #32
    // 0xae4bd0: mov             x2, x1
    // 0xae4bd4: stur            x3, [fp, #-0x10]
    // 0xae4bd8: r1 = Function '<anonymous closure>':.
    //     0xae4bd8: add             x1, PP, #0x58, lsl #12  ; [pp+0x583a8] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xae4bdc: ldr             x1, [x1, #0x3a8]
    // 0xae4be0: r0 = AllocateClosure()
    //     0xae4be0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae4be4: ldur            x1, [fp, #-0x10]
    // 0xae4be8: mov             x2, x0
    // 0xae4bec: r0 = setState()
    //     0xae4bec: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xae4bf0: r0 = Null
    //     0xae4bf0: mov             x0, NULL
    // 0xae4bf4: LeaveFrame
    //     0xae4bf4: mov             SP, fp
    //     0xae4bf8: ldp             fp, lr, [SP], #0x10
    // 0xae4bfc: ret
    //     0xae4bfc: ret             
    // 0xae4c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4c00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4c04: b               #0xae4bac
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0xc70f64, size: 0x134
    // 0xc70f64: EnterFrame
    //     0xc70f64: stp             fp, lr, [SP, #-0x10]!
    //     0xc70f68: mov             fp, SP
    // 0xc70f6c: AllocStack(0x18)
    //     0xc70f6c: sub             SP, SP, #0x18
    // 0xc70f70: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc70f70: mov             x0, x1
    //     0xc70f74: stur            x1, [fp, #-8]
    // 0xc70f78: CheckStackOverflow
    //     0xc70f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc70f7c: cmp             SP, x16
    //     0xc70f80: b.ls            #0xc71078
    // 0xc70f84: LoadField: r1 = r0->field_f
    //     0xc70f84: ldur            w1, [x0, #0xf]
    // 0xc70f88: DecompressPointer r1
    //     0xc70f88: add             x1, x1, HEAP, lsl #32
    // 0xc70f8c: cmp             w1, NULL
    // 0xc70f90: b.eq            #0xc71080
    // 0xc70f94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc70f94: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc70f98: r0 = _of()
    //     0xc70f98: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc70f9c: LoadField: r1 = r0->field_7
    //     0xc70f9c: ldur            w1, [x0, #7]
    // 0xc70fa0: DecompressPointer r1
    //     0xc70fa0: add             x1, x1, HEAP, lsl #32
    // 0xc70fa4: LoadField: d0 = r1->field_f
    //     0xc70fa4: ldur            d0, [x1, #0xf]
    // 0xc70fa8: d1 = 0.200000
    //     0xc70fa8: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xc70fac: fmul            d2, d0, d1
    // 0xc70fb0: r0 = inline_Allocate_Double()
    //     0xc70fb0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc70fb4: add             x0, x0, #0x10
    //     0xc70fb8: cmp             x1, x0
    //     0xc70fbc: b.ls            #0xc71084
    //     0xc70fc0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc70fc4: sub             x0, x0, #0xf
    //     0xc70fc8: movz            x1, #0xe15c
    //     0xc70fcc: movk            x1, #0x3, lsl #16
    //     0xc70fd0: stur            x1, [x0, #-1]
    // 0xc70fd4: StoreField: r0->field_7 = d2
    //     0xc70fd4: stur            d2, [x0, #7]
    // 0xc70fd8: ldur            x2, [fp, #-8]
    // 0xc70fdc: StoreField: r2->field_27 = r0
    //     0xc70fdc: stur            w0, [x2, #0x27]
    //     0xc70fe0: ldurb           w16, [x2, #-1]
    //     0xc70fe4: ldurb           w17, [x0, #-1]
    //     0xc70fe8: and             x16, x17, x16, lsr #2
    //     0xc70fec: tst             x16, HEAP, lsr #32
    //     0xc70ff0: b.eq            #0xc70ff8
    //     0xc70ff4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc70ff8: r0 = 84.000000
    //     0xc70ff8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xc70ffc: ldr             x0, [x0, #0xf90]
    // 0xc71000: StoreField: r2->field_1f = r0
    //     0xc71000: stur            w0, [x2, #0x1f]
    // 0xc71004: LoadField: r1 = r2->field_f
    //     0xc71004: ldur            w1, [x2, #0xf]
    // 0xc71008: DecompressPointer r1
    //     0xc71008: add             x1, x1, HEAP, lsl #32
    // 0xc7100c: cmp             w1, NULL
    // 0xc71010: b.eq            #0xc71094
    // 0xc71014: r0 = of()
    //     0xc71014: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc71018: LoadField: r1 = r0->field_87
    //     0xc71018: ldur            w1, [x0, #0x87]
    // 0xc7101c: DecompressPointer r1
    //     0xc7101c: add             x1, x1, HEAP, lsl #32
    // 0xc71020: LoadField: r0 = r1->field_7
    //     0xc71020: ldur            w0, [x1, #7]
    // 0xc71024: DecompressPointer r0
    //     0xc71024: add             x0, x0, HEAP, lsl #32
    // 0xc71028: r16 = 12.000000
    //     0xc71028: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc7102c: ldr             x16, [x16, #0x9e8]
    // 0xc71030: r30 = Instance_Color
    //     0xc71030: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc71034: stp             lr, x16, [SP]
    // 0xc71038: mov             x1, x0
    // 0xc7103c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc7103c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc71040: ldr             x4, [x4, #0xaa0]
    // 0xc71044: r0 = copyWith()
    //     0xc71044: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc71048: ldur            x1, [fp, #-8]
    // 0xc7104c: StoreField: r1->field_23 = r0
    //     0xc7104c: stur            w0, [x1, #0x23]
    //     0xc71050: ldurb           w16, [x1, #-1]
    //     0xc71054: ldurb           w17, [x0, #-1]
    //     0xc71058: and             x16, x17, x16, lsr #2
    //     0xc7105c: tst             x16, HEAP, lsr #32
    //     0xc71060: b.eq            #0xc71068
    //     0xc71064: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc71068: r0 = Null
    //     0xc71068: mov             x0, NULL
    // 0xc7106c: LeaveFrame
    //     0xc7106c: mov             SP, fp
    //     0xc71070: ldp             fp, lr, [SP], #0x10
    // 0xc71074: ret
    //     0xc71074: ret             
    // 0xc71078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc71078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7107c: b               #0xc70f84
    // 0xc71080: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc71080: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc71084: SaveReg d2
    //     0xc71084: str             q2, [SP, #-0x10]!
    // 0xc71088: r0 = AllocateDouble()
    //     0xc71088: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc7108c: RestoreReg d2
    //     0xc7108c: ldr             q2, [SP], #0x10
    // 0xc71090: b               #0xc70fd4
    // 0xc71094: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc71094: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc872a4, size: 0x54
    // 0xc872a4: EnterFrame
    //     0xc872a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc872a8: mov             fp, SP
    // 0xc872ac: CheckStackOverflow
    //     0xc872ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc872b0: cmp             SP, x16
    //     0xc872b4: b.ls            #0xc872e4
    // 0xc872b8: LoadField: r0 = r1->field_13
    //     0xc872b8: ldur            w0, [x1, #0x13]
    // 0xc872bc: DecompressPointer r0
    //     0xc872bc: add             x0, x0, HEAP, lsl #32
    // 0xc872c0: r16 = Sentinel
    //     0xc872c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc872c4: cmp             w0, w16
    // 0xc872c8: b.eq            #0xc872ec
    // 0xc872cc: mov             x1, x0
    // 0xc872d0: r0 = dispose()
    //     0xc872d0: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc872d4: r0 = Null
    //     0xc872d4: mov             x0, NULL
    // 0xc872d8: LeaveFrame
    //     0xc872d8: mov             SP, fp
    //     0xc872dc: ldp             fp, lr, [SP], #0x10
    // 0xc872e0: ret
    //     0xc872e0: ret             
    // 0xc872e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc872e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc872e8: b               #0xc872b8
    // 0xc872ec: r9 = _pageLineController
    //     0xc872ec: add             x9, PP, #0x58, lsl #12  ; [pp+0x58370] Field <_CategoryGroupCarouselItemViewState@1464505729._pageLineController@1464505729>: late (offset: 0x14)
    //     0xc872f0: ldr             x9, [x9, #0x370]
    // 0xc872f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc872f4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4164, size: 0x2c, field offset: 0xc
//   const constructor, 
class CategoryGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7d9d4, size: 0x3c
    // 0xc7d9d4: EnterFrame
    //     0xc7d9d4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7d9d8: mov             fp, SP
    // 0xc7d9dc: mov             x0, x1
    // 0xc7d9e0: r1 = <CategoryGroupCarouselItemView>
    //     0xc7d9e0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48be8] TypeArguments: <CategoryGroupCarouselItemView>
    //     0xc7d9e4: ldr             x1, [x1, #0xbe8]
    // 0xc7d9e8: r0 = _CategoryGroupCarouselItemViewState()
    //     0xc7d9e8: bl              #0xc7da10  ; Allocate_CategoryGroupCarouselItemViewStateStub -> _CategoryGroupCarouselItemViewState (size=0x2c)
    // 0xc7d9ec: r1 = Sentinel
    //     0xc7d9ec: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7d9f0: StoreField: r0->field_13 = r1
    //     0xc7d9f0: stur            w1, [x0, #0x13]
    // 0xc7d9f4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7d9f4: stur            xzr, [x0, #0x17]
    // 0xc7d9f8: StoreField: r0->field_1f = r1
    //     0xc7d9f8: stur            w1, [x0, #0x1f]
    // 0xc7d9fc: StoreField: r0->field_23 = r1
    //     0xc7d9fc: stur            w1, [x0, #0x23]
    // 0xc7da00: StoreField: r0->field_27 = r1
    //     0xc7da00: stur            w1, [x0, #0x27]
    // 0xc7da04: LeaveFrame
    //     0xc7da04: mov             SP, fp
    //     0xc7da08: ldp             fp, lr, [SP], #0x10
    // 0xc7da0c: ret
    //     0xc7da0c: ret             
  }
}
