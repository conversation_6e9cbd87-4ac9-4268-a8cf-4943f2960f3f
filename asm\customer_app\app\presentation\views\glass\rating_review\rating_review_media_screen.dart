// lib: , url: package:customer_app/app/presentation/views/glass/rating_review/rating_review_media_screen.dart

// class id: 1049455, size: 0x8
class :: {
}

// class id: 4551, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewMediaScreen extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14fc444, size: 0x64
    // 0x14fc444: EnterFrame
    //     0x14fc444: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc448: mov             fp, SP
    // 0x14fc44c: AllocStack(0x18)
    //     0x14fc44c: sub             SP, SP, #0x18
    // 0x14fc450: SetupParameters(RatingReviewMediaScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fc450: stur            x1, [fp, #-8]
    //     0x14fc454: stur            x2, [fp, #-0x10]
    // 0x14fc458: r1 = 2
    //     0x14fc458: movz            x1, #0x2
    // 0x14fc45c: r0 = AllocateContext()
    //     0x14fc45c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fc460: mov             x1, x0
    // 0x14fc464: ldur            x0, [fp, #-8]
    // 0x14fc468: stur            x1, [fp, #-0x18]
    // 0x14fc46c: StoreField: r1->field_f = r0
    //     0x14fc46c: stur            w0, [x1, #0xf]
    // 0x14fc470: ldur            x0, [fp, #-0x10]
    // 0x14fc474: StoreField: r1->field_13 = r0
    //     0x14fc474: stur            w0, [x1, #0x13]
    // 0x14fc478: r0 = Obx()
    //     0x14fc478: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fc47c: ldur            x2, [fp, #-0x18]
    // 0x14fc480: r1 = Function '<anonymous closure>':.
    //     0x14fc480: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e950] AnonymousClosure: (0x14fc4a8), in [package:customer_app/app/presentation/views/glass/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14fc444)
    //     0x14fc484: ldr             x1, [x1, #0x950]
    // 0x14fc488: stur            x0, [fp, #-8]
    // 0x14fc48c: r0 = AllocateClosure()
    //     0x14fc48c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc490: mov             x1, x0
    // 0x14fc494: ldur            x0, [fp, #-8]
    // 0x14fc498: StoreField: r0->field_b = r1
    //     0x14fc498: stur            w1, [x0, #0xb]
    // 0x14fc49c: LeaveFrame
    //     0x14fc49c: mov             SP, fp
    //     0x14fc4a0: ldp             fp, lr, [SP], #0x10
    // 0x14fc4a4: ret
    //     0x14fc4a4: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14fc4a8, size: 0x454
    // 0x14fc4a8: EnterFrame
    //     0x14fc4a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc4ac: mov             fp, SP
    // 0x14fc4b0: AllocStack(0x38)
    //     0x14fc4b0: sub             SP, SP, #0x38
    // 0x14fc4b4: SetupParameters()
    //     0x14fc4b4: ldr             x0, [fp, #0x10]
    //     0x14fc4b8: ldur            w2, [x0, #0x17]
    //     0x14fc4bc: add             x2, x2, HEAP, lsl #32
    //     0x14fc4c0: stur            x2, [fp, #-8]
    // 0x14fc4c4: CheckStackOverflow
    //     0x14fc4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fc4c8: cmp             SP, x16
    //     0x14fc4cc: b.ls            #0x14fc8f4
    // 0x14fc4d0: LoadField: r1 = r2->field_f
    //     0x14fc4d0: ldur            w1, [x2, #0xf]
    // 0x14fc4d4: DecompressPointer r1
    //     0x14fc4d4: add             x1, x1, HEAP, lsl #32
    // 0x14fc4d8: r0 = controller()
    //     0x14fc4d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc4dc: LoadField: r1 = r0->field_4f
    //     0x14fc4dc: ldur            w1, [x0, #0x4f]
    // 0x14fc4e0: DecompressPointer r1
    //     0x14fc4e0: add             x1, x1, HEAP, lsl #32
    // 0x14fc4e4: r0 = value()
    //     0x14fc4e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fc4e8: LoadField: r1 = r0->field_b
    //     0x14fc4e8: ldur            w1, [x0, #0xb]
    // 0x14fc4ec: DecompressPointer r1
    //     0x14fc4ec: add             x1, x1, HEAP, lsl #32
    // 0x14fc4f0: cmp             w1, NULL
    // 0x14fc4f4: b.ne            #0x14fc514
    // 0x14fc4f8: r0 = Container()
    //     0x14fc4f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14fc4fc: mov             x1, x0
    // 0x14fc500: stur            x0, [fp, #-0x10]
    // 0x14fc504: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fc504: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fc508: r0 = Container()
    //     0x14fc508: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14fc50c: ldur            x0, [fp, #-0x10]
    // 0x14fc510: b               #0x14fc8e8
    // 0x14fc514: ldur            x2, [fp, #-8]
    // 0x14fc518: LoadField: r1 = r2->field_13
    //     0x14fc518: ldur            w1, [x2, #0x13]
    // 0x14fc51c: DecompressPointer r1
    //     0x14fc51c: add             x1, x1, HEAP, lsl #32
    // 0x14fc520: r0 = of()
    //     0x14fc520: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fc524: LoadField: r1 = r0->field_87
    //     0x14fc524: ldur            w1, [x0, #0x87]
    // 0x14fc528: DecompressPointer r1
    //     0x14fc528: add             x1, x1, HEAP, lsl #32
    // 0x14fc52c: LoadField: r0 = r1->field_7
    //     0x14fc52c: ldur            w0, [x1, #7]
    // 0x14fc530: DecompressPointer r0
    //     0x14fc530: add             x0, x0, HEAP, lsl #32
    // 0x14fc534: r16 = 16.000000
    //     0x14fc534: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14fc538: ldr             x16, [x16, #0x188]
    // 0x14fc53c: r30 = Instance_Color
    //     0x14fc53c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fc540: stp             lr, x16, [SP]
    // 0x14fc544: mov             x1, x0
    // 0x14fc548: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14fc548: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14fc54c: ldr             x4, [x4, #0xaa0]
    // 0x14fc550: r0 = copyWith()
    //     0x14fc550: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fc554: stur            x0, [fp, #-0x10]
    // 0x14fc558: r0 = Text()
    //     0x14fc558: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14fc55c: mov             x2, x0
    // 0x14fc560: r0 = "Real images from customers"
    //     0x14fc560: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0x14fc564: ldr             x0, [x0, #0x88]
    // 0x14fc568: stur            x2, [fp, #-0x18]
    // 0x14fc56c: StoreField: r2->field_b = r0
    //     0x14fc56c: stur            w0, [x2, #0xb]
    // 0x14fc570: ldur            x0, [fp, #-0x10]
    // 0x14fc574: StoreField: r2->field_13 = r0
    //     0x14fc574: stur            w0, [x2, #0x13]
    // 0x14fc578: ldur            x0, [fp, #-8]
    // 0x14fc57c: LoadField: r1 = r0->field_13
    //     0x14fc57c: ldur            w1, [x0, #0x13]
    // 0x14fc580: DecompressPointer r1
    //     0x14fc580: add             x1, x1, HEAP, lsl #32
    // 0x14fc584: r0 = of()
    //     0x14fc584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fc588: LoadField: r1 = r0->field_5b
    //     0x14fc588: ldur            w1, [x0, #0x5b]
    // 0x14fc58c: DecompressPointer r1
    //     0x14fc58c: add             x1, x1, HEAP, lsl #32
    // 0x14fc590: stur            x1, [fp, #-0x10]
    // 0x14fc594: r0 = Icon()
    //     0x14fc594: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14fc598: mov             x1, x0
    // 0x14fc59c: r0 = Instance_IconData
    //     0x14fc59c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f090] Obj!IconData@d55181
    //     0x14fc5a0: ldr             x0, [x0, #0x90]
    // 0x14fc5a4: stur            x1, [fp, #-0x20]
    // 0x14fc5a8: StoreField: r1->field_b = r0
    //     0x14fc5a8: stur            w0, [x1, #0xb]
    // 0x14fc5ac: r0 = 25.000000
    //     0x14fc5ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x14fc5b0: ldr             x0, [x0, #0x98]
    // 0x14fc5b4: StoreField: r1->field_f = r0
    //     0x14fc5b4: stur            w0, [x1, #0xf]
    // 0x14fc5b8: ldur            x0, [fp, #-0x10]
    // 0x14fc5bc: StoreField: r1->field_23 = r0
    //     0x14fc5bc: stur            w0, [x1, #0x23]
    // 0x14fc5c0: r0 = InkWell()
    //     0x14fc5c0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14fc5c4: mov             x3, x0
    // 0x14fc5c8: ldur            x0, [fp, #-0x20]
    // 0x14fc5cc: stur            x3, [fp, #-0x10]
    // 0x14fc5d0: StoreField: r3->field_b = r0
    //     0x14fc5d0: stur            w0, [x3, #0xb]
    // 0x14fc5d4: r1 = Function '<anonymous closure>':.
    //     0x14fc5d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e958] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14fc5d8: ldr             x1, [x1, #0x958]
    // 0x14fc5dc: r2 = Null
    //     0x14fc5dc: mov             x2, NULL
    // 0x14fc5e0: r0 = AllocateClosure()
    //     0x14fc5e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc5e4: mov             x1, x0
    // 0x14fc5e8: ldur            x0, [fp, #-0x10]
    // 0x14fc5ec: StoreField: r0->field_f = r1
    //     0x14fc5ec: stur            w1, [x0, #0xf]
    // 0x14fc5f0: r3 = true
    //     0x14fc5f0: add             x3, NULL, #0x20  ; true
    // 0x14fc5f4: StoreField: r0->field_43 = r3
    //     0x14fc5f4: stur            w3, [x0, #0x43]
    // 0x14fc5f8: r1 = Instance_BoxShape
    //     0x14fc5f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fc5fc: ldr             x1, [x1, #0x80]
    // 0x14fc600: StoreField: r0->field_47 = r1
    //     0x14fc600: stur            w1, [x0, #0x47]
    // 0x14fc604: StoreField: r0->field_6f = r3
    //     0x14fc604: stur            w3, [x0, #0x6f]
    // 0x14fc608: r4 = false
    //     0x14fc608: add             x4, NULL, #0x30  ; false
    // 0x14fc60c: StoreField: r0->field_73 = r4
    //     0x14fc60c: stur            w4, [x0, #0x73]
    // 0x14fc610: StoreField: r0->field_83 = r3
    //     0x14fc610: stur            w3, [x0, #0x83]
    // 0x14fc614: StoreField: r0->field_7b = r4
    //     0x14fc614: stur            w4, [x0, #0x7b]
    // 0x14fc618: r1 = Null
    //     0x14fc618: mov             x1, NULL
    // 0x14fc61c: r2 = 4
    //     0x14fc61c: movz            x2, #0x4
    // 0x14fc620: r0 = AllocateArray()
    //     0x14fc620: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fc624: mov             x2, x0
    // 0x14fc628: ldur            x0, [fp, #-0x18]
    // 0x14fc62c: stur            x2, [fp, #-0x20]
    // 0x14fc630: StoreField: r2->field_f = r0
    //     0x14fc630: stur            w0, [x2, #0xf]
    // 0x14fc634: ldur            x0, [fp, #-0x10]
    // 0x14fc638: StoreField: r2->field_13 = r0
    //     0x14fc638: stur            w0, [x2, #0x13]
    // 0x14fc63c: r1 = <Widget>
    //     0x14fc63c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fc640: r0 = AllocateGrowableArray()
    //     0x14fc640: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fc644: mov             x1, x0
    // 0x14fc648: ldur            x0, [fp, #-0x20]
    // 0x14fc64c: stur            x1, [fp, #-0x10]
    // 0x14fc650: StoreField: r1->field_f = r0
    //     0x14fc650: stur            w0, [x1, #0xf]
    // 0x14fc654: r2 = 4
    //     0x14fc654: movz            x2, #0x4
    // 0x14fc658: StoreField: r1->field_b = r2
    //     0x14fc658: stur            w2, [x1, #0xb]
    // 0x14fc65c: r0 = Row()
    //     0x14fc65c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14fc660: mov             x1, x0
    // 0x14fc664: r0 = Instance_Axis
    //     0x14fc664: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14fc668: stur            x1, [fp, #-0x18]
    // 0x14fc66c: StoreField: r1->field_f = r0
    //     0x14fc66c: stur            w0, [x1, #0xf]
    // 0x14fc670: r0 = Instance_MainAxisAlignment
    //     0x14fc670: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14fc674: ldr             x0, [x0, #0xa8]
    // 0x14fc678: StoreField: r1->field_13 = r0
    //     0x14fc678: stur            w0, [x1, #0x13]
    // 0x14fc67c: r0 = Instance_MainAxisSize
    //     0x14fc67c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fc680: ldr             x0, [x0, #0xa10]
    // 0x14fc684: ArrayStore: r1[0] = r0  ; List_4
    //     0x14fc684: stur            w0, [x1, #0x17]
    // 0x14fc688: r2 = Instance_CrossAxisAlignment
    //     0x14fc688: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14fc68c: ldr             x2, [x2, #0xa18]
    // 0x14fc690: StoreField: r1->field_1b = r2
    //     0x14fc690: stur            w2, [x1, #0x1b]
    // 0x14fc694: r3 = Instance_VerticalDirection
    //     0x14fc694: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fc698: ldr             x3, [x3, #0xa20]
    // 0x14fc69c: StoreField: r1->field_23 = r3
    //     0x14fc69c: stur            w3, [x1, #0x23]
    // 0x14fc6a0: r4 = Instance_Clip
    //     0x14fc6a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fc6a4: ldr             x4, [x4, #0x38]
    // 0x14fc6a8: StoreField: r1->field_2b = r4
    //     0x14fc6a8: stur            w4, [x1, #0x2b]
    // 0x14fc6ac: StoreField: r1->field_2f = rZR
    //     0x14fc6ac: stur            xzr, [x1, #0x2f]
    // 0x14fc6b0: ldur            x5, [fp, #-0x10]
    // 0x14fc6b4: StoreField: r1->field_b = r5
    //     0x14fc6b4: stur            w5, [x1, #0xb]
    // 0x14fc6b8: r0 = Padding()
    //     0x14fc6b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fc6bc: mov             x2, x0
    // 0x14fc6c0: r0 = Instance_EdgeInsets
    //     0x14fc6c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x14fc6c4: ldr             x0, [x0, #0xb0]
    // 0x14fc6c8: stur            x2, [fp, #-0x10]
    // 0x14fc6cc: StoreField: r2->field_f = r0
    //     0x14fc6cc: stur            w0, [x2, #0xf]
    // 0x14fc6d0: ldur            x0, [fp, #-0x18]
    // 0x14fc6d4: StoreField: r2->field_b = r0
    //     0x14fc6d4: stur            w0, [x2, #0xb]
    // 0x14fc6d8: ldur            x0, [fp, #-8]
    // 0x14fc6dc: LoadField: r1 = r0->field_f
    //     0x14fc6dc: ldur            w1, [x0, #0xf]
    // 0x14fc6e0: DecompressPointer r1
    //     0x14fc6e0: add             x1, x1, HEAP, lsl #32
    // 0x14fc6e4: r0 = controller()
    //     0x14fc6e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc6e8: LoadField: r1 = r0->field_4f
    //     0x14fc6e8: ldur            w1, [x0, #0x4f]
    // 0x14fc6ec: DecompressPointer r1
    //     0x14fc6ec: add             x1, x1, HEAP, lsl #32
    // 0x14fc6f0: r0 = value()
    //     0x14fc6f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fc6f4: LoadField: r1 = r0->field_b
    //     0x14fc6f4: ldur            w1, [x0, #0xb]
    // 0x14fc6f8: DecompressPointer r1
    //     0x14fc6f8: add             x1, x1, HEAP, lsl #32
    // 0x14fc6fc: cmp             w1, NULL
    // 0x14fc700: b.ne            #0x14fc70c
    // 0x14fc704: r5 = Null
    //     0x14fc704: mov             x5, NULL
    // 0x14fc708: b               #0x14fc748
    // 0x14fc70c: LoadField: r0 = r1->field_b
    //     0x14fc70c: ldur            w0, [x1, #0xb]
    // 0x14fc710: DecompressPointer r0
    //     0x14fc710: add             x0, x0, HEAP, lsl #32
    // 0x14fc714: stur            x0, [fp, #-0x18]
    // 0x14fc718: r1 = Function '<anonymous closure>':.
    //     0x14fc718: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e960] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14fc71c: ldr             x1, [x1, #0x960]
    // 0x14fc720: r2 = Null
    //     0x14fc720: mov             x2, NULL
    // 0x14fc724: r0 = AllocateClosure()
    //     0x14fc724: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc728: ldur            x16, [fp, #-0x18]
    // 0x14fc72c: stp             x16, NULL, [SP, #8]
    // 0x14fc730: str             x0, [SP]
    // 0x14fc734: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14fc734: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14fc738: r0 = expand()
    //     0x14fc738: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14fc73c: str             x0, [SP]
    // 0x14fc740: r0 = length()
    //     0x14fc740: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x14fc744: mov             x5, x0
    // 0x14fc748: ldur            x0, [fp, #-0x10]
    // 0x14fc74c: ldur            x2, [fp, #-8]
    // 0x14fc750: stur            x5, [fp, #-0x18]
    // 0x14fc754: r1 = Function '<anonymous closure>':.
    //     0x14fc754: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e968] AnonymousClosure: (0x14fc8fc), in [package:customer_app/app/presentation/views/glass/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14fc444)
    //     0x14fc758: ldr             x1, [x1, #0x968]
    // 0x14fc75c: r0 = AllocateClosure()
    //     0x14fc75c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc760: stur            x0, [fp, #-8]
    // 0x14fc764: r0 = GridView()
    //     0x14fc764: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x14fc768: mov             x1, x0
    // 0x14fc76c: ldur            x3, [fp, #-8]
    // 0x14fc770: ldur            x5, [fp, #-0x18]
    // 0x14fc774: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x14fc774: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0c8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d56481
    //     0x14fc778: ldr             x2, [x2, #0xc8]
    // 0x14fc77c: stur            x0, [fp, #-8]
    // 0x14fc780: r0 = GridView.builder()
    //     0x14fc780: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x14fc784: r1 = Null
    //     0x14fc784: mov             x1, NULL
    // 0x14fc788: r2 = 4
    //     0x14fc788: movz            x2, #0x4
    // 0x14fc78c: r0 = AllocateArray()
    //     0x14fc78c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fc790: mov             x2, x0
    // 0x14fc794: ldur            x0, [fp, #-0x10]
    // 0x14fc798: stur            x2, [fp, #-0x18]
    // 0x14fc79c: StoreField: r2->field_f = r0
    //     0x14fc79c: stur            w0, [x2, #0xf]
    // 0x14fc7a0: ldur            x0, [fp, #-8]
    // 0x14fc7a4: StoreField: r2->field_13 = r0
    //     0x14fc7a4: stur            w0, [x2, #0x13]
    // 0x14fc7a8: r1 = <Widget>
    //     0x14fc7a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fc7ac: r0 = AllocateGrowableArray()
    //     0x14fc7ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fc7b0: mov             x1, x0
    // 0x14fc7b4: ldur            x0, [fp, #-0x18]
    // 0x14fc7b8: stur            x1, [fp, #-8]
    // 0x14fc7bc: StoreField: r1->field_f = r0
    //     0x14fc7bc: stur            w0, [x1, #0xf]
    // 0x14fc7c0: r0 = 4
    //     0x14fc7c0: movz            x0, #0x4
    // 0x14fc7c4: StoreField: r1->field_b = r0
    //     0x14fc7c4: stur            w0, [x1, #0xb]
    // 0x14fc7c8: r0 = Column()
    //     0x14fc7c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fc7cc: mov             x1, x0
    // 0x14fc7d0: r0 = Instance_Axis
    //     0x14fc7d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fc7d4: stur            x1, [fp, #-0x10]
    // 0x14fc7d8: StoreField: r1->field_f = r0
    //     0x14fc7d8: stur            w0, [x1, #0xf]
    // 0x14fc7dc: r2 = Instance_MainAxisAlignment
    //     0x14fc7dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fc7e0: ldr             x2, [x2, #0xa08]
    // 0x14fc7e4: StoreField: r1->field_13 = r2
    //     0x14fc7e4: stur            w2, [x1, #0x13]
    // 0x14fc7e8: r2 = Instance_MainAxisSize
    //     0x14fc7e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fc7ec: ldr             x2, [x2, #0xa10]
    // 0x14fc7f0: ArrayStore: r1[0] = r2  ; List_4
    //     0x14fc7f0: stur            w2, [x1, #0x17]
    // 0x14fc7f4: r2 = Instance_CrossAxisAlignment
    //     0x14fc7f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14fc7f8: ldr             x2, [x2, #0xa18]
    // 0x14fc7fc: StoreField: r1->field_1b = r2
    //     0x14fc7fc: stur            w2, [x1, #0x1b]
    // 0x14fc800: r2 = Instance_VerticalDirection
    //     0x14fc800: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fc804: ldr             x2, [x2, #0xa20]
    // 0x14fc808: StoreField: r1->field_23 = r2
    //     0x14fc808: stur            w2, [x1, #0x23]
    // 0x14fc80c: r2 = Instance_Clip
    //     0x14fc80c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fc810: ldr             x2, [x2, #0x38]
    // 0x14fc814: StoreField: r1->field_2b = r2
    //     0x14fc814: stur            w2, [x1, #0x2b]
    // 0x14fc818: StoreField: r1->field_2f = rZR
    //     0x14fc818: stur            xzr, [x1, #0x2f]
    // 0x14fc81c: ldur            x2, [fp, #-8]
    // 0x14fc820: StoreField: r1->field_b = r2
    //     0x14fc820: stur            w2, [x1, #0xb]
    // 0x14fc824: r0 = Padding()
    //     0x14fc824: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fc828: mov             x1, x0
    // 0x14fc82c: r0 = Instance_EdgeInsets
    //     0x14fc82c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x14fc830: ldr             x0, [x0, #0xd0]
    // 0x14fc834: stur            x1, [fp, #-8]
    // 0x14fc838: StoreField: r1->field_f = r0
    //     0x14fc838: stur            w0, [x1, #0xf]
    // 0x14fc83c: ldur            x0, [fp, #-0x10]
    // 0x14fc840: StoreField: r1->field_b = r0
    //     0x14fc840: stur            w0, [x1, #0xb]
    // 0x14fc844: r0 = SingleChildScrollView()
    //     0x14fc844: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14fc848: mov             x1, x0
    // 0x14fc84c: r0 = Instance_Axis
    //     0x14fc84c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fc850: stur            x1, [fp, #-0x10]
    // 0x14fc854: StoreField: r1->field_b = r0
    //     0x14fc854: stur            w0, [x1, #0xb]
    // 0x14fc858: r0 = false
    //     0x14fc858: add             x0, NULL, #0x30  ; false
    // 0x14fc85c: StoreField: r1->field_f = r0
    //     0x14fc85c: stur            w0, [x1, #0xf]
    // 0x14fc860: ldur            x2, [fp, #-8]
    // 0x14fc864: StoreField: r1->field_23 = r2
    //     0x14fc864: stur            w2, [x1, #0x23]
    // 0x14fc868: r2 = Instance_DragStartBehavior
    //     0x14fc868: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14fc86c: StoreField: r1->field_27 = r2
    //     0x14fc86c: stur            w2, [x1, #0x27]
    // 0x14fc870: r2 = Instance_Clip
    //     0x14fc870: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14fc874: ldr             x2, [x2, #0x7e0]
    // 0x14fc878: StoreField: r1->field_2b = r2
    //     0x14fc878: stur            w2, [x1, #0x2b]
    // 0x14fc87c: r2 = Instance_HitTestBehavior
    //     0x14fc87c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14fc880: ldr             x2, [x2, #0x288]
    // 0x14fc884: StoreField: r1->field_2f = r2
    //     0x14fc884: stur            w2, [x1, #0x2f]
    // 0x14fc888: r0 = SafeArea()
    //     0x14fc888: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14fc88c: mov             x1, x0
    // 0x14fc890: r0 = true
    //     0x14fc890: add             x0, NULL, #0x20  ; true
    // 0x14fc894: stur            x1, [fp, #-8]
    // 0x14fc898: StoreField: r1->field_b = r0
    //     0x14fc898: stur            w0, [x1, #0xb]
    // 0x14fc89c: StoreField: r1->field_f = r0
    //     0x14fc89c: stur            w0, [x1, #0xf]
    // 0x14fc8a0: StoreField: r1->field_13 = r0
    //     0x14fc8a0: stur            w0, [x1, #0x13]
    // 0x14fc8a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x14fc8a4: stur            w0, [x1, #0x17]
    // 0x14fc8a8: r2 = Instance_EdgeInsets
    //     0x14fc8a8: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14fc8ac: StoreField: r1->field_1b = r2
    //     0x14fc8ac: stur            w2, [x1, #0x1b]
    // 0x14fc8b0: r2 = false
    //     0x14fc8b0: add             x2, NULL, #0x30  ; false
    // 0x14fc8b4: StoreField: r1->field_1f = r2
    //     0x14fc8b4: stur            w2, [x1, #0x1f]
    // 0x14fc8b8: ldur            x3, [fp, #-0x10]
    // 0x14fc8bc: StoreField: r1->field_23 = r3
    //     0x14fc8bc: stur            w3, [x1, #0x23]
    // 0x14fc8c0: r0 = Scaffold()
    //     0x14fc8c0: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0x14fc8c4: ldur            x1, [fp, #-8]
    // 0x14fc8c8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fc8c8: stur            w1, [x0, #0x17]
    // 0x14fc8cc: r1 = Instance_Color
    //     0x14fc8cc: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14fc8d0: StoreField: r0->field_33 = r1
    //     0x14fc8d0: stur            w1, [x0, #0x33]
    // 0x14fc8d4: r1 = true
    //     0x14fc8d4: add             x1, NULL, #0x20  ; true
    // 0x14fc8d8: StoreField: r0->field_43 = r1
    //     0x14fc8d8: stur            w1, [x0, #0x43]
    // 0x14fc8dc: r1 = false
    //     0x14fc8dc: add             x1, NULL, #0x30  ; false
    // 0x14fc8e0: StoreField: r0->field_b = r1
    //     0x14fc8e0: stur            w1, [x0, #0xb]
    // 0x14fc8e4: StoreField: r0->field_f = r1
    //     0x14fc8e4: stur            w1, [x0, #0xf]
    // 0x14fc8e8: LeaveFrame
    //     0x14fc8e8: mov             SP, fp
    //     0x14fc8ec: ldp             fp, lr, [SP], #0x10
    // 0x14fc8f0: ret
    //     0x14fc8f0: ret             
    // 0x14fc8f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fc8f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fc8f8: b               #0x14fc4d0
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14fc8fc, size: 0x33c
    // 0x14fc8fc: EnterFrame
    //     0x14fc8fc: stp             fp, lr, [SP, #-0x10]!
    //     0x14fc900: mov             fp, SP
    // 0x14fc904: AllocStack(0x60)
    //     0x14fc904: sub             SP, SP, #0x60
    // 0x14fc908: SetupParameters()
    //     0x14fc908: ldr             x0, [fp, #0x20]
    //     0x14fc90c: ldur            w1, [x0, #0x17]
    //     0x14fc910: add             x1, x1, HEAP, lsl #32
    //     0x14fc914: stur            x1, [fp, #-8]
    // 0x14fc918: CheckStackOverflow
    //     0x14fc918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fc91c: cmp             SP, x16
    //     0x14fc920: b.ls            #0x14fcc2c
    // 0x14fc924: r1 = 3
    //     0x14fc924: movz            x1, #0x3
    // 0x14fc928: r0 = AllocateContext()
    //     0x14fc928: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fc92c: mov             x2, x0
    // 0x14fc930: ldur            x0, [fp, #-8]
    // 0x14fc934: stur            x2, [fp, #-0x10]
    // 0x14fc938: StoreField: r2->field_b = r0
    //     0x14fc938: stur            w0, [x2, #0xb]
    // 0x14fc93c: ldr             x1, [fp, #0x18]
    // 0x14fc940: StoreField: r2->field_f = r1
    //     0x14fc940: stur            w1, [x2, #0xf]
    // 0x14fc944: ldr             x3, [fp, #0x10]
    // 0x14fc948: StoreField: r2->field_13 = r3
    //     0x14fc948: stur            w3, [x2, #0x13]
    // 0x14fc94c: LoadField: r1 = r0->field_f
    //     0x14fc94c: ldur            w1, [x0, #0xf]
    // 0x14fc950: DecompressPointer r1
    //     0x14fc950: add             x1, x1, HEAP, lsl #32
    // 0x14fc954: r0 = controller()
    //     0x14fc954: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fc958: LoadField: r1 = r0->field_4f
    //     0x14fc958: ldur            w1, [x0, #0x4f]
    // 0x14fc95c: DecompressPointer r1
    //     0x14fc95c: add             x1, x1, HEAP, lsl #32
    // 0x14fc960: r0 = value()
    //     0x14fc960: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fc964: LoadField: r1 = r0->field_b
    //     0x14fc964: ldur            w1, [x0, #0xb]
    // 0x14fc968: DecompressPointer r1
    //     0x14fc968: add             x1, x1, HEAP, lsl #32
    // 0x14fc96c: cmp             w1, NULL
    // 0x14fc970: b.ne            #0x14fc97c
    // 0x14fc974: r2 = Null
    //     0x14fc974: mov             x2, NULL
    // 0x14fc978: b               #0x14fc9bc
    // 0x14fc97c: LoadField: r0 = r1->field_b
    //     0x14fc97c: ldur            w0, [x1, #0xb]
    // 0x14fc980: DecompressPointer r0
    //     0x14fc980: add             x0, x0, HEAP, lsl #32
    // 0x14fc984: stur            x0, [fp, #-8]
    // 0x14fc988: r1 = Function '<anonymous closure>':.
    //     0x14fc988: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e970] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0x14fc98c: ldr             x1, [x1, #0x970]
    // 0x14fc990: r2 = Null
    //     0x14fc990: mov             x2, NULL
    // 0x14fc994: r0 = AllocateClosure()
    //     0x14fc994: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fc998: ldur            x16, [fp, #-8]
    // 0x14fc99c: stp             x16, NULL, [SP, #8]
    // 0x14fc9a0: str             x0, [SP]
    // 0x14fc9a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14fc9a4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14fc9a8: r0 = expand()
    //     0x14fc9a8: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0x14fc9ac: mov             x1, x0
    // 0x14fc9b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fc9b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fc9b4: r0 = toList()
    //     0x14fc9b4: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0x14fc9b8: mov             x2, x0
    // 0x14fc9bc: cmp             w2, NULL
    // 0x14fc9c0: b.ne            #0x14fc9cc
    // 0x14fc9c4: r1 = Null
    //     0x14fc9c4: mov             x1, NULL
    // 0x14fc9c8: b               #0x14fca04
    // 0x14fc9cc: ldr             x0, [fp, #0x10]
    // 0x14fc9d0: LoadField: r1 = r2->field_b
    //     0x14fc9d0: ldur            w1, [x2, #0xb]
    // 0x14fc9d4: r3 = LoadInt32Instr(r0)
    //     0x14fc9d4: sbfx            x3, x0, #1, #0x1f
    //     0x14fc9d8: tbz             w0, #0, #0x14fc9e0
    //     0x14fc9dc: ldur            x3, [x0, #7]
    // 0x14fc9e0: r0 = LoadInt32Instr(r1)
    //     0x14fc9e0: sbfx            x0, x1, #1, #0x1f
    // 0x14fc9e4: mov             x1, x3
    // 0x14fc9e8: cmp             x1, x0
    // 0x14fc9ec: b.hs            #0x14fcc34
    // 0x14fc9f0: LoadField: r0 = r2->field_f
    //     0x14fc9f0: ldur            w0, [x2, #0xf]
    // 0x14fc9f4: DecompressPointer r0
    //     0x14fc9f4: add             x0, x0, HEAP, lsl #32
    // 0x14fc9f8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14fc9f8: add             x16, x0, x3, lsl #2
    //     0x14fc9fc: ldur            w1, [x16, #0xf]
    // 0x14fca00: DecompressPointer r1
    //     0x14fca00: add             x1, x1, HEAP, lsl #32
    // 0x14fca04: ldur            x2, [fp, #-0x10]
    // 0x14fca08: mov             x0, x1
    // 0x14fca0c: stur            x1, [fp, #-8]
    // 0x14fca10: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fca10: stur            w0, [x2, #0x17]
    //     0x14fca14: tbz             w0, #0, #0x14fca30
    //     0x14fca18: ldurb           w16, [x2, #-1]
    //     0x14fca1c: ldurb           w17, [x0, #-1]
    //     0x14fca20: and             x16, x17, x16, lsr #2
    //     0x14fca24: tst             x16, HEAP, lsr #32
    //     0x14fca28: b.eq            #0x14fca30
    //     0x14fca2c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x14fca30: r0 = Radius()
    //     0x14fca30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fca34: d0 = 12.000000
    //     0x14fca34: fmov            d0, #12.00000000
    // 0x14fca38: stur            x0, [fp, #-0x18]
    // 0x14fca3c: StoreField: r0->field_7 = d0
    //     0x14fca3c: stur            d0, [x0, #7]
    // 0x14fca40: StoreField: r0->field_f = d0
    //     0x14fca40: stur            d0, [x0, #0xf]
    // 0x14fca44: r0 = BorderRadius()
    //     0x14fca44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fca48: mov             x1, x0
    // 0x14fca4c: ldur            x0, [fp, #-0x18]
    // 0x14fca50: stur            x1, [fp, #-0x20]
    // 0x14fca54: StoreField: r1->field_7 = r0
    //     0x14fca54: stur            w0, [x1, #7]
    // 0x14fca58: StoreField: r1->field_b = r0
    //     0x14fca58: stur            w0, [x1, #0xb]
    // 0x14fca5c: StoreField: r1->field_f = r0
    //     0x14fca5c: stur            w0, [x1, #0xf]
    // 0x14fca60: StoreField: r1->field_13 = r0
    //     0x14fca60: stur            w0, [x1, #0x13]
    // 0x14fca64: ldur            x16, [fp, #-8]
    // 0x14fca68: str             x16, [SP]
    // 0x14fca6c: r4 = 0
    //     0x14fca6c: movz            x4, #0
    // 0x14fca70: ldr             x0, [SP]
    // 0x14fca74: r16 = UnlinkedCall_0x613b5c
    //     0x14fca74: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e978] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14fca78: add             x16, x16, #0x978
    // 0x14fca7c: ldp             x5, lr, [x16]
    // 0x14fca80: blr             lr
    // 0x14fca84: r1 = 60
    //     0x14fca84: movz            x1, #0x3c
    // 0x14fca88: branchIfSmi(r0, 0x14fca94)
    //     0x14fca88: tbz             w0, #0, #0x14fca94
    // 0x14fca8c: r1 = LoadClassIdInstr(r0)
    //     0x14fca8c: ldur            x1, [x0, #-1]
    //     0x14fca90: ubfx            x1, x1, #0xc, #0x14
    // 0x14fca94: r16 = "image"
    //     0x14fca94: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x14fca98: stp             x16, x0, [SP]
    // 0x14fca9c: mov             x0, x1
    // 0x14fcaa0: mov             lr, x0
    // 0x14fcaa4: ldr             lr, [x21, lr, lsl #3]
    // 0x14fcaa8: blr             lr
    // 0x14fcaac: tbnz            w0, #4, #0x14fcb58
    // 0x14fcab0: ldur            x16, [fp, #-8]
    // 0x14fcab4: str             x16, [SP]
    // 0x14fcab8: r4 = 0
    //     0x14fcab8: movz            x4, #0
    // 0x14fcabc: ldr             x0, [SP]
    // 0x14fcac0: r16 = UnlinkedCall_0x613b5c
    //     0x14fcac0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e988] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14fcac4: add             x16, x16, #0x988
    // 0x14fcac8: ldp             x5, lr, [x16]
    // 0x14fcacc: blr             lr
    // 0x14fcad0: cmp             w0, NULL
    // 0x14fcad4: b.ne            #0x14fcadc
    // 0x14fcad8: r0 = ""
    //     0x14fcad8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14fcadc: stur            x0, [fp, #-0x18]
    // 0x14fcae0: r1 = Function '<anonymous closure>':.
    //     0x14fcae0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e998] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14fcae4: ldr             x1, [x1, #0x998]
    // 0x14fcae8: r2 = Null
    //     0x14fcae8: mov             x2, NULL
    // 0x14fcaec: r0 = AllocateClosure()
    //     0x14fcaec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcaf0: r1 = Function '<anonymous closure>':.
    //     0x14fcaf0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14fcaf4: ldr             x1, [x1, #0x9a0]
    // 0x14fcaf8: r2 = Null
    //     0x14fcaf8: mov             x2, NULL
    // 0x14fcafc: stur            x0, [fp, #-0x28]
    // 0x14fcb00: r0 = AllocateClosure()
    //     0x14fcb00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcb04: stur            x0, [fp, #-0x30]
    // 0x14fcb08: r0 = CachedNetworkImage()
    //     0x14fcb08: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14fcb0c: stur            x0, [fp, #-0x38]
    // 0x14fcb10: r16 = 60.000000
    //     0x14fcb10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fcb14: ldr             x16, [x16, #0x110]
    // 0x14fcb18: r30 = 60.000000
    //     0x14fcb18: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14fcb1c: ldr             lr, [lr, #0x110]
    // 0x14fcb20: stp             lr, x16, [SP, #0x18]
    // 0x14fcb24: r16 = Instance_BoxFit
    //     0x14fcb24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14fcb28: ldr             x16, [x16, #0x118]
    // 0x14fcb2c: ldur            lr, [fp, #-0x28]
    // 0x14fcb30: stp             lr, x16, [SP, #8]
    // 0x14fcb34: ldur            x16, [fp, #-0x30]
    // 0x14fcb38: str             x16, [SP]
    // 0x14fcb3c: mov             x1, x0
    // 0x14fcb40: ldur            x2, [fp, #-0x18]
    // 0x14fcb44: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14fcb44: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14fcb48: ldr             x4, [x4, #0xc28]
    // 0x14fcb4c: r0 = CachedNetworkImage()
    //     0x14fcb4c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14fcb50: ldur            x1, [fp, #-0x38]
    // 0x14fcb54: b               #0x14fcb98
    // 0x14fcb58: ldur            x16, [fp, #-8]
    // 0x14fcb5c: str             x16, [SP]
    // 0x14fcb60: r4 = 0
    //     0x14fcb60: movz            x4, #0
    // 0x14fcb64: ldr             x0, [SP]
    // 0x14fcb68: r16 = UnlinkedCall_0x613b5c
    //     0x14fcb68: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14fcb6c: add             x16, x16, #0x9a8
    // 0x14fcb70: ldp             x5, lr, [x16]
    // 0x14fcb74: blr             lr
    // 0x14fcb78: cmp             w0, NULL
    // 0x14fcb7c: b.ne            #0x14fcb84
    // 0x14fcb80: r0 = ""
    //     0x14fcb80: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14fcb84: stur            x0, [fp, #-8]
    // 0x14fcb88: r0 = VideoPlayerWidget()
    //     0x14fcb88: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x14fcb8c: mov             x1, x0
    // 0x14fcb90: ldur            x0, [fp, #-8]
    // 0x14fcb94: StoreField: r1->field_b = r0
    //     0x14fcb94: stur            w0, [x1, #0xb]
    // 0x14fcb98: ldur            x0, [fp, #-0x20]
    // 0x14fcb9c: stur            x1, [fp, #-8]
    // 0x14fcba0: r0 = ClipRRect()
    //     0x14fcba0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14fcba4: mov             x1, x0
    // 0x14fcba8: ldur            x0, [fp, #-0x20]
    // 0x14fcbac: stur            x1, [fp, #-0x18]
    // 0x14fcbb0: StoreField: r1->field_f = r0
    //     0x14fcbb0: stur            w0, [x1, #0xf]
    // 0x14fcbb4: r0 = Instance_Clip
    //     0x14fcbb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14fcbb8: ldr             x0, [x0, #0x138]
    // 0x14fcbbc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14fcbbc: stur            w0, [x1, #0x17]
    // 0x14fcbc0: ldur            x0, [fp, #-8]
    // 0x14fcbc4: StoreField: r1->field_b = r0
    //     0x14fcbc4: stur            w0, [x1, #0xb]
    // 0x14fcbc8: r0 = InkWell()
    //     0x14fcbc8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14fcbcc: mov             x3, x0
    // 0x14fcbd0: ldur            x0, [fp, #-0x18]
    // 0x14fcbd4: stur            x3, [fp, #-8]
    // 0x14fcbd8: StoreField: r3->field_b = r0
    //     0x14fcbd8: stur            w0, [x3, #0xb]
    // 0x14fcbdc: ldur            x2, [fp, #-0x10]
    // 0x14fcbe0: r1 = Function '<anonymous closure>':.
    //     0x14fcbe0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9b8] AnonymousClosure: (0x14fcc38), in [package:customer_app/app/presentation/views/glass/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14fc444)
    //     0x14fcbe4: ldr             x1, [x1, #0x9b8]
    // 0x14fcbe8: r0 = AllocateClosure()
    //     0x14fcbe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcbec: mov             x1, x0
    // 0x14fcbf0: ldur            x0, [fp, #-8]
    // 0x14fcbf4: StoreField: r0->field_f = r1
    //     0x14fcbf4: stur            w1, [x0, #0xf]
    // 0x14fcbf8: r1 = true
    //     0x14fcbf8: add             x1, NULL, #0x20  ; true
    // 0x14fcbfc: StoreField: r0->field_43 = r1
    //     0x14fcbfc: stur            w1, [x0, #0x43]
    // 0x14fcc00: r2 = Instance_BoxShape
    //     0x14fcc00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14fcc04: ldr             x2, [x2, #0x80]
    // 0x14fcc08: StoreField: r0->field_47 = r2
    //     0x14fcc08: stur            w2, [x0, #0x47]
    // 0x14fcc0c: StoreField: r0->field_6f = r1
    //     0x14fcc0c: stur            w1, [x0, #0x6f]
    // 0x14fcc10: r2 = false
    //     0x14fcc10: add             x2, NULL, #0x30  ; false
    // 0x14fcc14: StoreField: r0->field_73 = r2
    //     0x14fcc14: stur            w2, [x0, #0x73]
    // 0x14fcc18: StoreField: r0->field_83 = r1
    //     0x14fcc18: stur            w1, [x0, #0x83]
    // 0x14fcc1c: StoreField: r0->field_7b = r2
    //     0x14fcc1c: stur            w2, [x0, #0x7b]
    // 0x14fcc20: LeaveFrame
    //     0x14fcc20: mov             SP, fp
    //     0x14fcc24: ldp             fp, lr, [SP], #0x10
    // 0x14fcc28: ret
    //     0x14fcc28: ret             
    // 0x14fcc2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fcc2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fcc30: b               #0x14fc924
    // 0x14fcc34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14fcc34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14fcc38, size: 0x9c
    // 0x14fcc38: EnterFrame
    //     0x14fcc38: stp             fp, lr, [SP, #-0x10]!
    //     0x14fcc3c: mov             fp, SP
    // 0x14fcc40: AllocStack(0x28)
    //     0x14fcc40: sub             SP, SP, #0x28
    // 0x14fcc44: SetupParameters()
    //     0x14fcc44: ldr             x0, [fp, #0x10]
    //     0x14fcc48: ldur            w2, [x0, #0x17]
    //     0x14fcc4c: add             x2, x2, HEAP, lsl #32
    //     0x14fcc50: stur            x2, [fp, #-8]
    // 0x14fcc54: CheckStackOverflow
    //     0x14fcc54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fcc58: cmp             SP, x16
    //     0x14fcc5c: b.ls            #0x14fcccc
    // 0x14fcc60: LoadField: r1 = r2->field_f
    //     0x14fcc60: ldur            w1, [x2, #0xf]
    // 0x14fcc64: DecompressPointer r1
    //     0x14fcc64: add             x1, x1, HEAP, lsl #32
    // 0x14fcc68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fcc68: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fcc6c: r0 = of()
    //     0x14fcc6c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x14fcc70: ldur            x2, [fp, #-8]
    // 0x14fcc74: r1 = Function '<anonymous closure>':.
    //     0x14fcc74: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9c0] AnonymousClosure: (0x14fccd4), in [package:customer_app/app/presentation/views/glass/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x14fc444)
    //     0x14fcc78: ldr             x1, [x1, #0x9c0]
    // 0x14fcc7c: stur            x0, [fp, #-8]
    // 0x14fcc80: r0 = AllocateClosure()
    //     0x14fcc80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fcc84: r1 = Null
    //     0x14fcc84: mov             x1, NULL
    // 0x14fcc88: stur            x0, [fp, #-0x10]
    // 0x14fcc8c: r0 = MaterialPageRoute()
    //     0x14fcc8c: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0x14fcc90: mov             x1, x0
    // 0x14fcc94: ldur            x2, [fp, #-0x10]
    // 0x14fcc98: stur            x0, [fp, #-0x10]
    // 0x14fcc9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14fcc9c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14fcca0: r0 = MaterialPageRoute()
    //     0x14fcca0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0x14fcca4: ldur            x16, [fp, #-8]
    // 0x14fcca8: stp             x16, NULL, [SP, #8]
    // 0x14fccac: ldur            x16, [fp, #-0x10]
    // 0x14fccb0: str             x16, [SP]
    // 0x14fccb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14fccb4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14fccb8: r0 = push()
    //     0x14fccb8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x14fccbc: r0 = Null
    //     0x14fccbc: mov             x0, NULL
    // 0x14fccc0: LeaveFrame
    //     0x14fccc0: mov             SP, fp
    //     0x14fccc4: ldp             fp, lr, [SP], #0x10
    // 0x14fccc8: ret
    //     0x14fccc8: ret             
    // 0x14fcccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fcccc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fccd0: b               #0x14fcc60
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14fccd4, size: 0x1a4
    // 0x14fccd4: EnterFrame
    //     0x14fccd4: stp             fp, lr, [SP, #-0x10]!
    //     0x14fccd8: mov             fp, SP
    // 0x14fccdc: AllocStack(0x38)
    //     0x14fccdc: sub             SP, SP, #0x38
    // 0x14fcce0: SetupParameters()
    //     0x14fcce0: ldr             x0, [fp, #0x18]
    //     0x14fcce4: ldur            w2, [x0, #0x17]
    //     0x14fcce8: add             x2, x2, HEAP, lsl #32
    //     0x14fccec: stur            x2, [fp, #-0x10]
    // 0x14fccf0: CheckStackOverflow
    //     0x14fccf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fccf4: cmp             SP, x16
    //     0x14fccf8: b.ls            #0x14fce70
    // 0x14fccfc: LoadField: r0 = r2->field_b
    //     0x14fccfc: ldur            w0, [x2, #0xb]
    // 0x14fcd00: DecompressPointer r0
    //     0x14fcd00: add             x0, x0, HEAP, lsl #32
    // 0x14fcd04: stur            x0, [fp, #-8]
    // 0x14fcd08: LoadField: r1 = r0->field_f
    //     0x14fcd08: ldur            w1, [x0, #0xf]
    // 0x14fcd0c: DecompressPointer r1
    //     0x14fcd0c: add             x1, x1, HEAP, lsl #32
    // 0x14fcd10: r0 = controller()
    //     0x14fcd10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fcd14: LoadField: r1 = r0->field_4f
    //     0x14fcd14: ldur            w1, [x0, #0x4f]
    // 0x14fcd18: DecompressPointer r1
    //     0x14fcd18: add             x1, x1, HEAP, lsl #32
    // 0x14fcd1c: r0 = value()
    //     0x14fcd1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fcd20: LoadField: r1 = r0->field_b
    //     0x14fcd20: ldur            w1, [x0, #0xb]
    // 0x14fcd24: DecompressPointer r1
    //     0x14fcd24: add             x1, x1, HEAP, lsl #32
    // 0x14fcd28: cmp             w1, NULL
    // 0x14fcd2c: b.ne            #0x14fcd38
    // 0x14fcd30: r0 = Null
    //     0x14fcd30: mov             x0, NULL
    // 0x14fcd34: b               #0x14fcd40
    // 0x14fcd38: LoadField: r0 = r1->field_b
    //     0x14fcd38: ldur            w0, [x1, #0xb]
    // 0x14fcd3c: DecompressPointer r0
    //     0x14fcd3c: add             x0, x0, HEAP, lsl #32
    // 0x14fcd40: cmp             w0, NULL
    // 0x14fcd44: b.ne            #0x14fcd60
    // 0x14fcd48: r1 = <ReviewRatingEntity>
    //     0x14fcd48: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0x14fcd4c: ldr             x1, [x1, #0x150]
    // 0x14fcd50: r2 = 0
    //     0x14fcd50: movz            x2, #0
    // 0x14fcd54: r0 = _GrowableList()
    //     0x14fcd54: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x14fcd58: mov             x1, x0
    // 0x14fcd5c: b               #0x14fcd64
    // 0x14fcd60: mov             x1, x0
    // 0x14fcd64: ldur            x2, [fp, #-0x10]
    // 0x14fcd68: ldur            x0, [fp, #-8]
    // 0x14fcd6c: stur            x1, [fp, #-0x20]
    // 0x14fcd70: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14fcd70: ldur            w3, [x2, #0x17]
    // 0x14fcd74: DecompressPointer r3
    //     0x14fcd74: add             x3, x3, HEAP, lsl #32
    // 0x14fcd78: stur            x3, [fp, #-0x18]
    // 0x14fcd7c: str             x3, [SP]
    // 0x14fcd80: r4 = 0
    //     0x14fcd80: movz            x4, #0
    // 0x14fcd84: ldr             x0, [SP]
    // 0x14fcd88: r16 = UnlinkedCall_0x613b5c
    //     0x14fcd88: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14fcd8c: add             x16, x16, #0x9c8
    // 0x14fcd90: ldp             x5, lr, [x16]
    // 0x14fcd94: blr             lr
    // 0x14fcd98: stur            x0, [fp, #-0x28]
    // 0x14fcd9c: ldur            x16, [fp, #-0x18]
    // 0x14fcda0: str             x16, [SP]
    // 0x14fcda4: r4 = 0
    //     0x14fcda4: movz            x4, #0
    // 0x14fcda8: ldr             x0, [SP]
    // 0x14fcdac: r16 = UnlinkedCall_0x613b5c
    //     0x14fcdac: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e9d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x14fcdb0: add             x16, x16, #0x9d8
    // 0x14fcdb4: ldp             x5, lr, [x16]
    // 0x14fcdb8: blr             lr
    // 0x14fcdbc: mov             x3, x0
    // 0x14fcdc0: r2 = Null
    //     0x14fcdc0: mov             x2, NULL
    // 0x14fcdc4: r1 = Null
    //     0x14fcdc4: mov             x1, NULL
    // 0x14fcdc8: stur            x3, [fp, #-0x18]
    // 0x14fcdcc: branchIfSmi(r0, 0x14fcdf4)
    //     0x14fcdcc: tbz             w0, #0, #0x14fcdf4
    // 0x14fcdd0: r4 = LoadClassIdInstr(r0)
    //     0x14fcdd0: ldur            x4, [x0, #-1]
    //     0x14fcdd4: ubfx            x4, x4, #0xc, #0x14
    // 0x14fcdd8: sub             x4, x4, #0x3c
    // 0x14fcddc: cmp             x4, #1
    // 0x14fcde0: b.ls            #0x14fcdf4
    // 0x14fcde4: r8 = int?
    //     0x14fcde4: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0x14fcde8: r3 = Null
    //     0x14fcde8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e9e8] Null
    //     0x14fcdec: ldr             x3, [x3, #0x9e8]
    // 0x14fcdf0: r0 = int?()
    //     0x14fcdf0: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0x14fcdf4: ldur            x0, [fp, #-8]
    // 0x14fcdf8: LoadField: r1 = r0->field_f
    //     0x14fcdf8: ldur            w1, [x0, #0xf]
    // 0x14fcdfc: DecompressPointer r1
    //     0x14fcdfc: add             x1, x1, HEAP, lsl #32
    // 0x14fce00: r0 = controller()
    //     0x14fce00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fce04: LoadField: r1 = r0->field_63
    //     0x14fce04: ldur            w1, [x0, #0x63]
    // 0x14fce08: DecompressPointer r1
    //     0x14fce08: add             x1, x1, HEAP, lsl #32
    // 0x14fce0c: stur            x1, [fp, #-8]
    // 0x14fce10: r0 = RatingReviewAllMediaOnTapImage()
    //     0x14fce10: bl              #0xa9808c  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0x14fce14: mov             x3, x0
    // 0x14fce18: ldur            x0, [fp, #-0x20]
    // 0x14fce1c: stur            x3, [fp, #-0x30]
    // 0x14fce20: StoreField: r3->field_f = r0
    //     0x14fce20: stur            w0, [x3, #0xf]
    // 0x14fce24: ldur            x0, [fp, #-0x28]
    // 0x14fce28: r1 = LoadInt32Instr(r0)
    //     0x14fce28: sbfx            x1, x0, #1, #0x1f
    //     0x14fce2c: tbz             w0, #0, #0x14fce34
    //     0x14fce30: ldur            x1, [x0, #7]
    // 0x14fce34: StoreField: r3->field_13 = r1
    //     0x14fce34: stur            x1, [x3, #0x13]
    // 0x14fce38: ldur            x0, [fp, #-0x18]
    // 0x14fce3c: StoreField: r3->field_1b = r0
    //     0x14fce3c: stur            w0, [x3, #0x1b]
    // 0x14fce40: ldur            x2, [fp, #-0x10]
    // 0x14fce44: r1 = Function '<anonymous closure>':.
    //     0x14fce44: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9f8] AnonymousClosure: (0x8ff1ac), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14fce48: ldr             x1, [x1, #0x9f8]
    // 0x14fce4c: r0 = AllocateClosure()
    //     0x14fce4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fce50: mov             x1, x0
    // 0x14fce54: ldur            x0, [fp, #-0x30]
    // 0x14fce58: StoreField: r0->field_1f = r1
    //     0x14fce58: stur            w1, [x0, #0x1f]
    // 0x14fce5c: ldur            x1, [fp, #-8]
    // 0x14fce60: StoreField: r0->field_23 = r1
    //     0x14fce60: stur            w1, [x0, #0x23]
    // 0x14fce64: LeaveFrame
    //     0x14fce64: mov             SP, fp
    //     0x14fce68: ldp             fp, lr, [SP], #0x10
    // 0x14fce6c: ret
    //     0x14fce6c: ret             
    // 0x14fce70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fce70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fce74: b               #0x14fccfc
  }
}
