// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart

// class id: 1049317, size: 0x8
class :: {
}

// class id: 3400, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb0fe14, size: 0x64
    // 0xb0fe14: EnterFrame
    //     0xb0fe14: stp             fp, lr, [SP, #-0x10]!
    //     0xb0fe18: mov             fp, SP
    // 0xb0fe1c: AllocStack(0x18)
    //     0xb0fe1c: sub             SP, SP, #0x18
    // 0xb0fe20: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb0fe20: stur            x1, [fp, #-8]
    //     0xb0fe24: stur            x2, [fp, #-0x10]
    // 0xb0fe28: r1 = 2
    //     0xb0fe28: movz            x1, #0x2
    // 0xb0fe2c: r0 = AllocateContext()
    //     0xb0fe2c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0fe30: mov             x1, x0
    // 0xb0fe34: ldur            x0, [fp, #-8]
    // 0xb0fe38: stur            x1, [fp, #-0x18]
    // 0xb0fe3c: StoreField: r1->field_f = r0
    //     0xb0fe3c: stur            w0, [x1, #0xf]
    // 0xb0fe40: ldur            x0, [fp, #-0x10]
    // 0xb0fe44: StoreField: r1->field_13 = r0
    //     0xb0fe44: stur            w0, [x1, #0x13]
    // 0xb0fe48: r0 = Obx()
    //     0xb0fe48: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xb0fe4c: ldur            x2, [fp, #-0x18]
    // 0xb0fe50: r1 = Function '<anonymous closure>':.
    //     0xb0fe50: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b38] AnonymousClosure: (0xb0fe9c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb0fe14)
    //     0xb0fe54: ldr             x1, [x1, #0xb38]
    // 0xb0fe58: stur            x0, [fp, #-8]
    // 0xb0fe5c: r0 = AllocateClosure()
    //     0xb0fe5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0fe60: mov             x1, x0
    // 0xb0fe64: ldur            x0, [fp, #-8]
    // 0xb0fe68: StoreField: r0->field_b = r1
    //     0xb0fe68: stur            w1, [x0, #0xb]
    // 0xb0fe6c: LeaveFrame
    //     0xb0fe6c: mov             SP, fp
    //     0xb0fe70: ldp             fp, lr, [SP], #0x10
    // 0xb0fe74: ret
    //     0xb0fe74: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0xb0fe9c, size: 0x974
    // 0xb0fe9c: EnterFrame
    //     0xb0fe9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb0fea0: mov             fp, SP
    // 0xb0fea4: AllocStack(0x78)
    //     0xb0fea4: sub             SP, SP, #0x78
    // 0xb0fea8: SetupParameters()
    //     0xb0fea8: ldr             x0, [fp, #0x10]
    //     0xb0feac: ldur            w3, [x0, #0x17]
    //     0xb0feb0: add             x3, x3, HEAP, lsl #32
    //     0xb0feb4: stur            x3, [fp, #-0x10]
    // 0xb0feb8: CheckStackOverflow
    //     0xb0feb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0febc: cmp             SP, x16
    //     0xb0fec0: b.ls            #0xb107d4
    // 0xb0fec4: LoadField: r0 = r3->field_f
    //     0xb0fec4: ldur            w0, [x3, #0xf]
    // 0xb0fec8: DecompressPointer r0
    //     0xb0fec8: add             x0, x0, HEAP, lsl #32
    // 0xb0fecc: LoadField: r1 = r0->field_b
    //     0xb0fecc: ldur            w1, [x0, #0xb]
    // 0xb0fed0: DecompressPointer r1
    //     0xb0fed0: add             x1, x1, HEAP, lsl #32
    // 0xb0fed4: cmp             w1, NULL
    // 0xb0fed8: b.eq            #0xb107dc
    // 0xb0fedc: LoadField: r0 = r1->field_13
    //     0xb0fedc: ldur            w0, [x1, #0x13]
    // 0xb0fee0: DecompressPointer r0
    //     0xb0fee0: add             x0, x0, HEAP, lsl #32
    // 0xb0fee4: LoadField: r1 = r0->field_7
    //     0xb0fee4: ldur            w1, [x0, #7]
    // 0xb0fee8: DecompressPointer r1
    //     0xb0fee8: add             x1, x1, HEAP, lsl #32
    // 0xb0feec: cmp             w1, NULL
    // 0xb0fef0: b.ne            #0xb0ff00
    // 0xb0fef4: r0 = Instance_TitleAlignment
    //     0xb0fef4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb0fef8: ldr             x0, [x0, #0x518]
    // 0xb0fefc: b               #0xb0ff04
    // 0xb0ff00: mov             x0, x1
    // 0xb0ff04: r16 = Instance_TitleAlignment
    //     0xb0ff04: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb0ff08: ldr             x16, [x16, #0x520]
    // 0xb0ff0c: cmp             w0, w16
    // 0xb0ff10: b.ne            #0xb0ff20
    // 0xb0ff14: r0 = Instance_CrossAxisAlignment
    //     0xb0ff14: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb0ff18: ldr             x0, [x0, #0xc68]
    // 0xb0ff1c: b               #0xb0ff44
    // 0xb0ff20: r16 = Instance_TitleAlignment
    //     0xb0ff20: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb0ff24: ldr             x16, [x16, #0x518]
    // 0xb0ff28: cmp             w0, w16
    // 0xb0ff2c: b.ne            #0xb0ff3c
    // 0xb0ff30: r0 = Instance_CrossAxisAlignment
    //     0xb0ff30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb0ff34: ldr             x0, [x0, #0x890]
    // 0xb0ff38: b               #0xb0ff44
    // 0xb0ff3c: r0 = Instance_CrossAxisAlignment
    //     0xb0ff3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0ff40: ldr             x0, [x0, #0xa18]
    // 0xb0ff44: stur            x0, [fp, #-8]
    // 0xb0ff48: r1 = <Widget>
    //     0xb0ff48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0ff4c: r2 = 0
    //     0xb0ff4c: movz            x2, #0
    // 0xb0ff50: r0 = _GrowableList()
    //     0xb0ff50: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb0ff54: mov             x1, x0
    // 0xb0ff58: ldur            x2, [fp, #-0x10]
    // 0xb0ff5c: stur            x1, [fp, #-0x18]
    // 0xb0ff60: LoadField: r0 = r2->field_f
    //     0xb0ff60: ldur            w0, [x2, #0xf]
    // 0xb0ff64: DecompressPointer r0
    //     0xb0ff64: add             x0, x0, HEAP, lsl #32
    // 0xb0ff68: LoadField: r3 = r0->field_b
    //     0xb0ff68: ldur            w3, [x0, #0xb]
    // 0xb0ff6c: DecompressPointer r3
    //     0xb0ff6c: add             x3, x3, HEAP, lsl #32
    // 0xb0ff70: cmp             w3, NULL
    // 0xb0ff74: b.eq            #0xb107e0
    // 0xb0ff78: LoadField: r0 = r3->field_f
    //     0xb0ff78: ldur            w0, [x3, #0xf]
    // 0xb0ff7c: DecompressPointer r0
    //     0xb0ff7c: add             x0, x0, HEAP, lsl #32
    // 0xb0ff80: LoadField: r3 = r0->field_7
    //     0xb0ff80: ldur            w3, [x0, #7]
    // 0xb0ff84: cbz             w3, #0xb10128
    // 0xb0ff88: r3 = LoadClassIdInstr(r0)
    //     0xb0ff88: ldur            x3, [x0, #-1]
    //     0xb0ff8c: ubfx            x3, x3, #0xc, #0x14
    // 0xb0ff90: str             x0, [SP]
    // 0xb0ff94: mov             x0, x3
    // 0xb0ff98: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb0ff98: sub             lr, x0, #1, lsl #12
    //     0xb0ff9c: ldr             lr, [x21, lr, lsl #3]
    //     0xb0ffa0: blr             lr
    // 0xb0ffa4: ldur            x2, [fp, #-0x10]
    // 0xb0ffa8: stur            x0, [fp, #-0x28]
    // 0xb0ffac: LoadField: r1 = r2->field_f
    //     0xb0ffac: ldur            w1, [x2, #0xf]
    // 0xb0ffb0: DecompressPointer r1
    //     0xb0ffb0: add             x1, x1, HEAP, lsl #32
    // 0xb0ffb4: LoadField: r3 = r1->field_b
    //     0xb0ffb4: ldur            w3, [x1, #0xb]
    // 0xb0ffb8: DecompressPointer r3
    //     0xb0ffb8: add             x3, x3, HEAP, lsl #32
    // 0xb0ffbc: cmp             w3, NULL
    // 0xb0ffc0: b.eq            #0xb107e4
    // 0xb0ffc4: LoadField: r1 = r3->field_13
    //     0xb0ffc4: ldur            w1, [x3, #0x13]
    // 0xb0ffc8: DecompressPointer r1
    //     0xb0ffc8: add             x1, x1, HEAP, lsl #32
    // 0xb0ffcc: LoadField: r3 = r1->field_7
    //     0xb0ffcc: ldur            w3, [x1, #7]
    // 0xb0ffd0: DecompressPointer r3
    //     0xb0ffd0: add             x3, x3, HEAP, lsl #32
    // 0xb0ffd4: cmp             w3, NULL
    // 0xb0ffd8: b.ne            #0xb0ffe8
    // 0xb0ffdc: r1 = Instance_TitleAlignment
    //     0xb0ffdc: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb0ffe0: ldr             x1, [x1, #0x518]
    // 0xb0ffe4: b               #0xb0ffec
    // 0xb0ffe8: mov             x1, x3
    // 0xb0ffec: r16 = Instance_TitleAlignment
    //     0xb0ffec: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb0fff0: ldr             x16, [x16, #0x520]
    // 0xb0fff4: cmp             w1, w16
    // 0xb0fff8: b.ne            #0xb10004
    // 0xb0fffc: r4 = Instance_TextAlign
    //     0xb0fffc: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb10000: b               #0xb10020
    // 0xb10004: r16 = Instance_TitleAlignment
    //     0xb10004: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb10008: ldr             x16, [x16, #0x518]
    // 0xb1000c: cmp             w1, w16
    // 0xb10010: b.ne            #0xb1001c
    // 0xb10014: r4 = Instance_TextAlign
    //     0xb10014: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb10018: b               #0xb10020
    // 0xb1001c: r4 = Instance_TextAlign
    //     0xb1001c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb10020: ldur            x3, [fp, #-0x18]
    // 0xb10024: stur            x4, [fp, #-0x20]
    // 0xb10028: LoadField: r1 = r2->field_13
    //     0xb10028: ldur            w1, [x2, #0x13]
    // 0xb1002c: DecompressPointer r1
    //     0xb1002c: add             x1, x1, HEAP, lsl #32
    // 0xb10030: r0 = of()
    //     0xb10030: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb10034: LoadField: r1 = r0->field_87
    //     0xb10034: ldur            w1, [x0, #0x87]
    // 0xb10038: DecompressPointer r1
    //     0xb10038: add             x1, x1, HEAP, lsl #32
    // 0xb1003c: LoadField: r0 = r1->field_7
    //     0xb1003c: ldur            w0, [x1, #7]
    // 0xb10040: DecompressPointer r0
    //     0xb10040: add             x0, x0, HEAP, lsl #32
    // 0xb10044: r16 = Instance_Color
    //     0xb10044: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb10048: r30 = 32.000000
    //     0xb10048: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb1004c: ldr             lr, [lr, #0x848]
    // 0xb10050: stp             lr, x16, [SP]
    // 0xb10054: mov             x1, x0
    // 0xb10058: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb10058: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb1005c: ldr             x4, [x4, #0x9b8]
    // 0xb10060: r0 = copyWith()
    //     0xb10060: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb10064: stur            x0, [fp, #-0x30]
    // 0xb10068: r0 = Text()
    //     0xb10068: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb1006c: mov             x1, x0
    // 0xb10070: ldur            x0, [fp, #-0x28]
    // 0xb10074: stur            x1, [fp, #-0x38]
    // 0xb10078: StoreField: r1->field_b = r0
    //     0xb10078: stur            w0, [x1, #0xb]
    // 0xb1007c: ldur            x0, [fp, #-0x30]
    // 0xb10080: StoreField: r1->field_13 = r0
    //     0xb10080: stur            w0, [x1, #0x13]
    // 0xb10084: ldur            x0, [fp, #-0x20]
    // 0xb10088: StoreField: r1->field_1b = r0
    //     0xb10088: stur            w0, [x1, #0x1b]
    // 0xb1008c: r0 = Padding()
    //     0xb1008c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb10090: mov             x2, x0
    // 0xb10094: r0 = Instance_EdgeInsets
    //     0xb10094: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xb10098: ldr             x0, [x0, #0x78]
    // 0xb1009c: stur            x2, [fp, #-0x20]
    // 0xb100a0: StoreField: r2->field_f = r0
    //     0xb100a0: stur            w0, [x2, #0xf]
    // 0xb100a4: ldur            x0, [fp, #-0x38]
    // 0xb100a8: StoreField: r2->field_b = r0
    //     0xb100a8: stur            w0, [x2, #0xb]
    // 0xb100ac: ldur            x0, [fp, #-0x18]
    // 0xb100b0: LoadField: r1 = r0->field_b
    //     0xb100b0: ldur            w1, [x0, #0xb]
    // 0xb100b4: LoadField: r3 = r0->field_f
    //     0xb100b4: ldur            w3, [x0, #0xf]
    // 0xb100b8: DecompressPointer r3
    //     0xb100b8: add             x3, x3, HEAP, lsl #32
    // 0xb100bc: LoadField: r4 = r3->field_b
    //     0xb100bc: ldur            w4, [x3, #0xb]
    // 0xb100c0: r3 = LoadInt32Instr(r1)
    //     0xb100c0: sbfx            x3, x1, #1, #0x1f
    // 0xb100c4: stur            x3, [fp, #-0x40]
    // 0xb100c8: r1 = LoadInt32Instr(r4)
    //     0xb100c8: sbfx            x1, x4, #1, #0x1f
    // 0xb100cc: cmp             x3, x1
    // 0xb100d0: b.ne            #0xb100dc
    // 0xb100d4: mov             x1, x0
    // 0xb100d8: r0 = _growToNextCapacity()
    //     0xb100d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb100dc: ldur            x2, [fp, #-0x18]
    // 0xb100e0: ldur            x3, [fp, #-0x40]
    // 0xb100e4: add             x0, x3, #1
    // 0xb100e8: lsl             x1, x0, #1
    // 0xb100ec: StoreField: r2->field_b = r1
    //     0xb100ec: stur            w1, [x2, #0xb]
    // 0xb100f0: LoadField: r1 = r2->field_f
    //     0xb100f0: ldur            w1, [x2, #0xf]
    // 0xb100f4: DecompressPointer r1
    //     0xb100f4: add             x1, x1, HEAP, lsl #32
    // 0xb100f8: ldur            x0, [fp, #-0x20]
    // 0xb100fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb100fc: add             x25, x1, x3, lsl #2
    //     0xb10100: add             x25, x25, #0xf
    //     0xb10104: str             w0, [x25]
    //     0xb10108: tbz             w0, #0, #0xb10124
    //     0xb1010c: ldurb           w16, [x1, #-1]
    //     0xb10110: ldurb           w17, [x0, #-1]
    //     0xb10114: and             x16, x17, x16, lsr #2
    //     0xb10118: tst             x16, HEAP, lsr #32
    //     0xb1011c: b.eq            #0xb10124
    //     0xb10120: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10124: b               #0xb1012c
    // 0xb10128: mov             x2, x1
    // 0xb1012c: ldur            x0, [fp, #-0x10]
    // 0xb10130: LoadField: r1 = r0->field_13
    //     0xb10130: ldur            w1, [x0, #0x13]
    // 0xb10134: DecompressPointer r1
    //     0xb10134: add             x1, x1, HEAP, lsl #32
    // 0xb10138: r0 = of()
    //     0xb10138: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1013c: LoadField: r1 = r0->field_5b
    //     0xb1013c: ldur            w1, [x0, #0x5b]
    // 0xb10140: DecompressPointer r1
    //     0xb10140: add             x1, x1, HEAP, lsl #32
    // 0xb10144: stur            x1, [fp, #-0x20]
    // 0xb10148: r0 = BoxDecoration()
    //     0xb10148: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb1014c: mov             x2, x0
    // 0xb10150: ldur            x0, [fp, #-0x20]
    // 0xb10154: stur            x2, [fp, #-0x28]
    // 0xb10158: StoreField: r2->field_7 = r0
    //     0xb10158: stur            w0, [x2, #7]
    // 0xb1015c: r0 = Instance_BorderRadius
    //     0xb1015c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb10160: ldr             x0, [x0, #0x460]
    // 0xb10164: StoreField: r2->field_13 = r0
    //     0xb10164: stur            w0, [x2, #0x13]
    // 0xb10168: r0 = Instance_BoxShape
    //     0xb10168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1016c: ldr             x0, [x0, #0x80]
    // 0xb10170: StoreField: r2->field_23 = r0
    //     0xb10170: stur            w0, [x2, #0x23]
    // 0xb10174: ldur            x3, [fp, #-0x10]
    // 0xb10178: LoadField: r1 = r3->field_13
    //     0xb10178: ldur            w1, [x3, #0x13]
    // 0xb1017c: DecompressPointer r1
    //     0xb1017c: add             x1, x1, HEAP, lsl #32
    // 0xb10180: r0 = of()
    //     0xb10180: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb10184: LoadField: r1 = r0->field_87
    //     0xb10184: ldur            w1, [x0, #0x87]
    // 0xb10188: DecompressPointer r1
    //     0xb10188: add             x1, x1, HEAP, lsl #32
    // 0xb1018c: LoadField: r0 = r1->field_2b
    //     0xb1018c: ldur            w0, [x1, #0x2b]
    // 0xb10190: DecompressPointer r0
    //     0xb10190: add             x0, x0, HEAP, lsl #32
    // 0xb10194: r16 = 16.000000
    //     0xb10194: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb10198: ldr             x16, [x16, #0x188]
    // 0xb1019c: r30 = Instance_Color
    //     0xb1019c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb101a0: stp             lr, x16, [SP]
    // 0xb101a4: mov             x1, x0
    // 0xb101a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb101a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb101ac: ldr             x4, [x4, #0xaa0]
    // 0xb101b0: r0 = copyWith()
    //     0xb101b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb101b4: stur            x0, [fp, #-0x20]
    // 0xb101b8: r0 = Text()
    //     0xb101b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb101bc: mov             x1, x0
    // 0xb101c0: r0 = "View All"
    //     0xb101c0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55098] "View All"
    //     0xb101c4: ldr             x0, [x0, #0x98]
    // 0xb101c8: stur            x1, [fp, #-0x30]
    // 0xb101cc: StoreField: r1->field_b = r0
    //     0xb101cc: stur            w0, [x1, #0xb]
    // 0xb101d0: ldur            x0, [fp, #-0x20]
    // 0xb101d4: StoreField: r1->field_13 = r0
    //     0xb101d4: stur            w0, [x1, #0x13]
    // 0xb101d8: r0 = Center()
    //     0xb101d8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb101dc: mov             x1, x0
    // 0xb101e0: r0 = Instance_Alignment
    //     0xb101e0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb101e4: ldr             x0, [x0, #0xb10]
    // 0xb101e8: stur            x1, [fp, #-0x20]
    // 0xb101ec: StoreField: r1->field_f = r0
    //     0xb101ec: stur            w0, [x1, #0xf]
    // 0xb101f0: ldur            x0, [fp, #-0x30]
    // 0xb101f4: StoreField: r1->field_b = r0
    //     0xb101f4: stur            w0, [x1, #0xb]
    // 0xb101f8: r0 = Container()
    //     0xb101f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb101fc: stur            x0, [fp, #-0x30]
    // 0xb10200: r16 = 40.000000
    //     0xb10200: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb10204: ldr             x16, [x16, #8]
    // 0xb10208: r30 = 110.000000
    //     0xb10208: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb1020c: ldr             lr, [lr, #0x770]
    // 0xb10210: stp             lr, x16, [SP, #0x10]
    // 0xb10214: ldur            x16, [fp, #-0x28]
    // 0xb10218: ldur            lr, [fp, #-0x20]
    // 0xb1021c: stp             lr, x16, [SP]
    // 0xb10220: mov             x1, x0
    // 0xb10224: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb10224: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb10228: ldr             x4, [x4, #0x8c0]
    // 0xb1022c: r0 = Container()
    //     0xb1022c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb10230: r0 = InkWell()
    //     0xb10230: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb10234: mov             x3, x0
    // 0xb10238: ldur            x0, [fp, #-0x30]
    // 0xb1023c: stur            x3, [fp, #-0x20]
    // 0xb10240: StoreField: r3->field_b = r0
    //     0xb10240: stur            w0, [x3, #0xb]
    // 0xb10244: ldur            x2, [fp, #-0x10]
    // 0xb10248: r1 = Function '<anonymous closure>':.
    //     0xb10248: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b40] AnonymousClosure: (0xb118ec), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb0fe14)
    //     0xb1024c: ldr             x1, [x1, #0xb40]
    // 0xb10250: r0 = AllocateClosure()
    //     0xb10250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb10254: mov             x1, x0
    // 0xb10258: ldur            x0, [fp, #-0x20]
    // 0xb1025c: StoreField: r0->field_f = r1
    //     0xb1025c: stur            w1, [x0, #0xf]
    // 0xb10260: r1 = true
    //     0xb10260: add             x1, NULL, #0x20  ; true
    // 0xb10264: StoreField: r0->field_43 = r1
    //     0xb10264: stur            w1, [x0, #0x43]
    // 0xb10268: r2 = Instance_BoxShape
    //     0xb10268: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1026c: ldr             x2, [x2, #0x80]
    // 0xb10270: StoreField: r0->field_47 = r2
    //     0xb10270: stur            w2, [x0, #0x47]
    // 0xb10274: StoreField: r0->field_6f = r1
    //     0xb10274: stur            w1, [x0, #0x6f]
    // 0xb10278: r2 = false
    //     0xb10278: add             x2, NULL, #0x30  ; false
    // 0xb1027c: StoreField: r0->field_73 = r2
    //     0xb1027c: stur            w2, [x0, #0x73]
    // 0xb10280: StoreField: r0->field_83 = r1
    //     0xb10280: stur            w1, [x0, #0x83]
    // 0xb10284: StoreField: r0->field_7b = r2
    //     0xb10284: stur            w2, [x0, #0x7b]
    // 0xb10288: r0 = Padding()
    //     0xb10288: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb1028c: mov             x2, x0
    // 0xb10290: r0 = Instance_EdgeInsets
    //     0xb10290: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xb10294: ldr             x0, [x0, #0xf30]
    // 0xb10298: stur            x2, [fp, #-0x28]
    // 0xb1029c: StoreField: r2->field_f = r0
    //     0xb1029c: stur            w0, [x2, #0xf]
    // 0xb102a0: ldur            x0, [fp, #-0x20]
    // 0xb102a4: StoreField: r2->field_b = r0
    //     0xb102a4: stur            w0, [x2, #0xb]
    // 0xb102a8: ldur            x0, [fp, #-0x18]
    // 0xb102ac: LoadField: r1 = r0->field_b
    //     0xb102ac: ldur            w1, [x0, #0xb]
    // 0xb102b0: LoadField: r3 = r0->field_f
    //     0xb102b0: ldur            w3, [x0, #0xf]
    // 0xb102b4: DecompressPointer r3
    //     0xb102b4: add             x3, x3, HEAP, lsl #32
    // 0xb102b8: LoadField: r4 = r3->field_b
    //     0xb102b8: ldur            w4, [x3, #0xb]
    // 0xb102bc: r3 = LoadInt32Instr(r1)
    //     0xb102bc: sbfx            x3, x1, #1, #0x1f
    // 0xb102c0: stur            x3, [fp, #-0x40]
    // 0xb102c4: r1 = LoadInt32Instr(r4)
    //     0xb102c4: sbfx            x1, x4, #1, #0x1f
    // 0xb102c8: cmp             x3, x1
    // 0xb102cc: b.ne            #0xb102d8
    // 0xb102d0: mov             x1, x0
    // 0xb102d4: r0 = _growToNextCapacity()
    //     0xb102d4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb102d8: ldur            x4, [fp, #-0x10]
    // 0xb102dc: ldur            x3, [fp, #-0x18]
    // 0xb102e0: ldur            x2, [fp, #-0x40]
    // 0xb102e4: add             x0, x2, #1
    // 0xb102e8: lsl             x1, x0, #1
    // 0xb102ec: StoreField: r3->field_b = r1
    //     0xb102ec: stur            w1, [x3, #0xb]
    // 0xb102f0: LoadField: r1 = r3->field_f
    //     0xb102f0: ldur            w1, [x3, #0xf]
    // 0xb102f4: DecompressPointer r1
    //     0xb102f4: add             x1, x1, HEAP, lsl #32
    // 0xb102f8: ldur            x0, [fp, #-0x28]
    // 0xb102fc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb102fc: add             x25, x1, x2, lsl #2
    //     0xb10300: add             x25, x25, #0xf
    //     0xb10304: str             w0, [x25]
    //     0xb10308: tbz             w0, #0, #0xb10324
    //     0xb1030c: ldurb           w16, [x1, #-1]
    //     0xb10310: ldurb           w17, [x0, #-1]
    //     0xb10314: and             x16, x17, x16, lsr #2
    //     0xb10318: tst             x16, HEAP, lsr #32
    //     0xb1031c: b.eq            #0xb10324
    //     0xb10320: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10324: LoadField: r1 = r4->field_f
    //     0xb10324: ldur            w1, [x4, #0xf]
    // 0xb10328: DecompressPointer r1
    //     0xb10328: add             x1, x1, HEAP, lsl #32
    // 0xb1032c: ArrayLoad: r2 = r1[0]  ; List_8
    //     0xb1032c: ldur            x2, [x1, #0x17]
    // 0xb10330: r0 = _calculateCardHeight()
    //     0xb10330: bl              #0xa87490  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xb10334: ldur            x2, [fp, #-0x10]
    // 0xb10338: stur            d0, [fp, #-0x58]
    // 0xb1033c: LoadField: r0 = r2->field_f
    //     0xb1033c: ldur            w0, [x2, #0xf]
    // 0xb10340: DecompressPointer r0
    //     0xb10340: add             x0, x0, HEAP, lsl #32
    // 0xb10344: LoadField: r1 = r0->field_b
    //     0xb10344: ldur            w1, [x0, #0xb]
    // 0xb10348: DecompressPointer r1
    //     0xb10348: add             x1, x1, HEAP, lsl #32
    // 0xb1034c: cmp             w1, NULL
    // 0xb10350: b.eq            #0xb107e8
    // 0xb10354: LoadField: r0 = r1->field_b
    //     0xb10354: ldur            w0, [x1, #0xb]
    // 0xb10358: DecompressPointer r0
    //     0xb10358: add             x0, x0, HEAP, lsl #32
    // 0xb1035c: r1 = LoadClassIdInstr(r0)
    //     0xb1035c: ldur            x1, [x0, #-1]
    //     0xb10360: ubfx            x1, x1, #0xc, #0x14
    // 0xb10364: str             x0, [SP]
    // 0xb10368: mov             x0, x1
    // 0xb1036c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb1036c: movz            x17, #0xc898
    //     0xb10370: add             lr, x0, x17
    //     0xb10374: ldr             lr, [x21, lr, lsl #3]
    //     0xb10378: blr             lr
    // 0xb1037c: mov             x3, x0
    // 0xb10380: ldur            x0, [fp, #-0x10]
    // 0xb10384: stur            x3, [fp, #-0x28]
    // 0xb10388: LoadField: r1 = r0->field_f
    //     0xb10388: ldur            w1, [x0, #0xf]
    // 0xb1038c: DecompressPointer r1
    //     0xb1038c: add             x1, x1, HEAP, lsl #32
    // 0xb10390: LoadField: r4 = r1->field_13
    //     0xb10390: ldur            w4, [x1, #0x13]
    // 0xb10394: DecompressPointer r4
    //     0xb10394: add             x4, x4, HEAP, lsl #32
    // 0xb10398: r16 = Sentinel
    //     0xb10398: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb1039c: cmp             w4, w16
    // 0xb103a0: b.eq            #0xb107ec
    // 0xb103a4: mov             x2, x0
    // 0xb103a8: stur            x4, [fp, #-0x20]
    // 0xb103ac: r1 = Function '<anonymous closure>':.
    //     0xb103ac: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b48] AnonymousClosure: (0xb117dc), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb0fe14)
    //     0xb103b0: ldr             x1, [x1, #0xb48]
    // 0xb103b4: r0 = AllocateClosure()
    //     0xb103b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb103b8: ldur            x2, [fp, #-0x10]
    // 0xb103bc: r1 = Function '<anonymous closure>':.
    //     0xb103bc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b50] AnonymousClosure: (0xb10810), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb0fe14)
    //     0xb103c0: ldr             x1, [x1, #0xb50]
    // 0xb103c4: stur            x0, [fp, #-0x30]
    // 0xb103c8: r0 = AllocateClosure()
    //     0xb103c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb103cc: stur            x0, [fp, #-0x38]
    // 0xb103d0: r0 = PageView()
    //     0xb103d0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb103d4: stur            x0, [fp, #-0x48]
    // 0xb103d8: r16 = Instance_BouncingScrollPhysics
    //     0xb103d8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb103dc: ldr             x16, [x16, #0x890]
    // 0xb103e0: ldur            lr, [fp, #-0x20]
    // 0xb103e4: stp             lr, x16, [SP]
    // 0xb103e8: mov             x1, x0
    // 0xb103ec: ldur            x2, [fp, #-0x38]
    // 0xb103f0: ldur            x3, [fp, #-0x28]
    // 0xb103f4: ldur            x5, [fp, #-0x30]
    // 0xb103f8: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb103f8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb103fc: ldr             x4, [x4, #0xe40]
    // 0xb10400: r0 = PageView.builder()
    //     0xb10400: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb10404: ldur            d0, [fp, #-0x58]
    // 0xb10408: r0 = inline_Allocate_Double()
    //     0xb10408: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb1040c: add             x0, x0, #0x10
    //     0xb10410: cmp             x1, x0
    //     0xb10414: b.ls            #0xb107f8
    //     0xb10418: str             x0, [THR, #0x50]  ; THR::top
    //     0xb1041c: sub             x0, x0, #0xf
    //     0xb10420: movz            x1, #0xe15c
    //     0xb10424: movk            x1, #0x3, lsl #16
    //     0xb10428: stur            x1, [x0, #-1]
    // 0xb1042c: StoreField: r0->field_7 = d0
    //     0xb1042c: stur            d0, [x0, #7]
    // 0xb10430: stur            x0, [fp, #-0x20]
    // 0xb10434: r0 = SizedBox()
    //     0xb10434: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb10438: mov             x2, x0
    // 0xb1043c: ldur            x0, [fp, #-0x20]
    // 0xb10440: stur            x2, [fp, #-0x28]
    // 0xb10444: StoreField: r2->field_13 = r0
    //     0xb10444: stur            w0, [x2, #0x13]
    // 0xb10448: ldur            x0, [fp, #-0x48]
    // 0xb1044c: StoreField: r2->field_b = r0
    //     0xb1044c: stur            w0, [x2, #0xb]
    // 0xb10450: ldur            x0, [fp, #-0x18]
    // 0xb10454: LoadField: r1 = r0->field_b
    //     0xb10454: ldur            w1, [x0, #0xb]
    // 0xb10458: LoadField: r3 = r0->field_f
    //     0xb10458: ldur            w3, [x0, #0xf]
    // 0xb1045c: DecompressPointer r3
    //     0xb1045c: add             x3, x3, HEAP, lsl #32
    // 0xb10460: LoadField: r4 = r3->field_b
    //     0xb10460: ldur            w4, [x3, #0xb]
    // 0xb10464: r3 = LoadInt32Instr(r1)
    //     0xb10464: sbfx            x3, x1, #1, #0x1f
    // 0xb10468: stur            x3, [fp, #-0x40]
    // 0xb1046c: r1 = LoadInt32Instr(r4)
    //     0xb1046c: sbfx            x1, x4, #1, #0x1f
    // 0xb10470: cmp             x3, x1
    // 0xb10474: b.ne            #0xb10480
    // 0xb10478: mov             x1, x0
    // 0xb1047c: r0 = _growToNextCapacity()
    //     0xb1047c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10480: ldur            x4, [fp, #-0x10]
    // 0xb10484: ldur            x2, [fp, #-0x18]
    // 0xb10488: ldur            x3, [fp, #-0x40]
    // 0xb1048c: add             x0, x3, #1
    // 0xb10490: lsl             x1, x0, #1
    // 0xb10494: StoreField: r2->field_b = r1
    //     0xb10494: stur            w1, [x2, #0xb]
    // 0xb10498: LoadField: r1 = r2->field_f
    //     0xb10498: ldur            w1, [x2, #0xf]
    // 0xb1049c: DecompressPointer r1
    //     0xb1049c: add             x1, x1, HEAP, lsl #32
    // 0xb104a0: ldur            x0, [fp, #-0x28]
    // 0xb104a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb104a4: add             x25, x1, x3, lsl #2
    //     0xb104a8: add             x25, x25, #0xf
    //     0xb104ac: str             w0, [x25]
    //     0xb104b0: tbz             w0, #0, #0xb104cc
    //     0xb104b4: ldurb           w16, [x1, #-1]
    //     0xb104b8: ldurb           w17, [x0, #-1]
    //     0xb104bc: and             x16, x17, x16, lsr #2
    //     0xb104c0: tst             x16, HEAP, lsr #32
    //     0xb104c4: b.eq            #0xb104cc
    //     0xb104c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb104cc: LoadField: r0 = r4->field_f
    //     0xb104cc: ldur            w0, [x4, #0xf]
    // 0xb104d0: DecompressPointer r0
    //     0xb104d0: add             x0, x0, HEAP, lsl #32
    // 0xb104d4: LoadField: r1 = r0->field_b
    //     0xb104d4: ldur            w1, [x0, #0xb]
    // 0xb104d8: DecompressPointer r1
    //     0xb104d8: add             x1, x1, HEAP, lsl #32
    // 0xb104dc: cmp             w1, NULL
    // 0xb104e0: b.eq            #0xb10808
    // 0xb104e4: LoadField: r0 = r1->field_b
    //     0xb104e4: ldur            w0, [x1, #0xb]
    // 0xb104e8: DecompressPointer r0
    //     0xb104e8: add             x0, x0, HEAP, lsl #32
    // 0xb104ec: r1 = LoadClassIdInstr(r0)
    //     0xb104ec: ldur            x1, [x0, #-1]
    //     0xb104f0: ubfx            x1, x1, #0xc, #0x14
    // 0xb104f4: str             x0, [SP]
    // 0xb104f8: mov             x0, x1
    // 0xb104fc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb104fc: movz            x17, #0xc898
    //     0xb10500: add             lr, x0, x17
    //     0xb10504: ldr             lr, [x21, lr, lsl #3]
    //     0xb10508: blr             lr
    // 0xb1050c: r1 = LoadInt32Instr(r0)
    //     0xb1050c: sbfx            x1, x0, #1, #0x1f
    // 0xb10510: cmp             x1, #1
    // 0xb10514: b.le            #0xb106e4
    // 0xb10518: ldur            x2, [fp, #-0x10]
    // 0xb1051c: ldur            x1, [fp, #-0x18]
    // 0xb10520: LoadField: r0 = r2->field_f
    //     0xb10520: ldur            w0, [x2, #0xf]
    // 0xb10524: DecompressPointer r0
    //     0xb10524: add             x0, x0, HEAP, lsl #32
    // 0xb10528: LoadField: r3 = r0->field_b
    //     0xb10528: ldur            w3, [x0, #0xb]
    // 0xb1052c: DecompressPointer r3
    //     0xb1052c: add             x3, x3, HEAP, lsl #32
    // 0xb10530: cmp             w3, NULL
    // 0xb10534: b.eq            #0xb1080c
    // 0xb10538: LoadField: r0 = r3->field_b
    //     0xb10538: ldur            w0, [x3, #0xb]
    // 0xb1053c: DecompressPointer r0
    //     0xb1053c: add             x0, x0, HEAP, lsl #32
    // 0xb10540: r3 = LoadClassIdInstr(r0)
    //     0xb10540: ldur            x3, [x0, #-1]
    //     0xb10544: ubfx            x3, x3, #0xc, #0x14
    // 0xb10548: str             x0, [SP]
    // 0xb1054c: mov             x0, x3
    // 0xb10550: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb10550: movz            x17, #0xc898
    //     0xb10554: add             lr, x0, x17
    //     0xb10558: ldr             lr, [x21, lr, lsl #3]
    //     0xb1055c: blr             lr
    // 0xb10560: mov             x2, x0
    // 0xb10564: ldur            x0, [fp, #-0x10]
    // 0xb10568: stur            x2, [fp, #-0x20]
    // 0xb1056c: LoadField: r1 = r0->field_f
    //     0xb1056c: ldur            w1, [x0, #0xf]
    // 0xb10570: DecompressPointer r1
    //     0xb10570: add             x1, x1, HEAP, lsl #32
    // 0xb10574: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xb10574: ldur            x3, [x1, #0x17]
    // 0xb10578: stur            x3, [fp, #-0x40]
    // 0xb1057c: LoadField: r1 = r0->field_13
    //     0xb1057c: ldur            w1, [x0, #0x13]
    // 0xb10580: DecompressPointer r1
    //     0xb10580: add             x1, x1, HEAP, lsl #32
    // 0xb10584: r0 = of()
    //     0xb10584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb10588: LoadField: r1 = r0->field_5b
    //     0xb10588: ldur            w1, [x0, #0x5b]
    // 0xb1058c: DecompressPointer r1
    //     0xb1058c: add             x1, x1, HEAP, lsl #32
    // 0xb10590: ldur            x0, [fp, #-0x20]
    // 0xb10594: stur            x1, [fp, #-0x10]
    // 0xb10598: r2 = LoadInt32Instr(r0)
    //     0xb10598: sbfx            x2, x0, #1, #0x1f
    // 0xb1059c: stur            x2, [fp, #-0x50]
    // 0xb105a0: r0 = CarouselIndicator()
    //     0xb105a0: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb105a4: mov             x3, x0
    // 0xb105a8: ldur            x0, [fp, #-0x50]
    // 0xb105ac: stur            x3, [fp, #-0x20]
    // 0xb105b0: StoreField: r3->field_b = r0
    //     0xb105b0: stur            x0, [x3, #0xb]
    // 0xb105b4: ldur            x0, [fp, #-0x40]
    // 0xb105b8: StoreField: r3->field_13 = r0
    //     0xb105b8: stur            x0, [x3, #0x13]
    // 0xb105bc: ldur            x0, [fp, #-0x10]
    // 0xb105c0: StoreField: r3->field_1b = r0
    //     0xb105c0: stur            w0, [x3, #0x1b]
    // 0xb105c4: r0 = Instance_Color
    //     0xb105c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb105c8: ldr             x0, [x0, #0x90]
    // 0xb105cc: StoreField: r3->field_1f = r0
    //     0xb105cc: stur            w0, [x3, #0x1f]
    // 0xb105d0: r1 = Null
    //     0xb105d0: mov             x1, NULL
    // 0xb105d4: r2 = 2
    //     0xb105d4: movz            x2, #0x2
    // 0xb105d8: r0 = AllocateArray()
    //     0xb105d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb105dc: mov             x2, x0
    // 0xb105e0: ldur            x0, [fp, #-0x20]
    // 0xb105e4: stur            x2, [fp, #-0x10]
    // 0xb105e8: StoreField: r2->field_f = r0
    //     0xb105e8: stur            w0, [x2, #0xf]
    // 0xb105ec: r1 = <Widget>
    //     0xb105ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb105f0: r0 = AllocateGrowableArray()
    //     0xb105f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb105f4: mov             x1, x0
    // 0xb105f8: ldur            x0, [fp, #-0x10]
    // 0xb105fc: stur            x1, [fp, #-0x20]
    // 0xb10600: StoreField: r1->field_f = r0
    //     0xb10600: stur            w0, [x1, #0xf]
    // 0xb10604: r0 = 2
    //     0xb10604: movz            x0, #0x2
    // 0xb10608: StoreField: r1->field_b = r0
    //     0xb10608: stur            w0, [x1, #0xb]
    // 0xb1060c: r0 = Row()
    //     0xb1060c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb10610: mov             x2, x0
    // 0xb10614: r0 = Instance_Axis
    //     0xb10614: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb10618: stur            x2, [fp, #-0x10]
    // 0xb1061c: StoreField: r2->field_f = r0
    //     0xb1061c: stur            w0, [x2, #0xf]
    // 0xb10620: r0 = Instance_MainAxisAlignment
    //     0xb10620: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb10624: ldr             x0, [x0, #0xab0]
    // 0xb10628: StoreField: r2->field_13 = r0
    //     0xb10628: stur            w0, [x2, #0x13]
    // 0xb1062c: r0 = Instance_MainAxisSize
    //     0xb1062c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb10630: ldr             x0, [x0, #0xa10]
    // 0xb10634: ArrayStore: r2[0] = r0  ; List_4
    //     0xb10634: stur            w0, [x2, #0x17]
    // 0xb10638: r1 = Instance_CrossAxisAlignment
    //     0xb10638: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb1063c: ldr             x1, [x1, #0xa18]
    // 0xb10640: StoreField: r2->field_1b = r1
    //     0xb10640: stur            w1, [x2, #0x1b]
    // 0xb10644: r3 = Instance_VerticalDirection
    //     0xb10644: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb10648: ldr             x3, [x3, #0xa20]
    // 0xb1064c: StoreField: r2->field_23 = r3
    //     0xb1064c: stur            w3, [x2, #0x23]
    // 0xb10650: r4 = Instance_Clip
    //     0xb10650: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb10654: ldr             x4, [x4, #0x38]
    // 0xb10658: StoreField: r2->field_2b = r4
    //     0xb10658: stur            w4, [x2, #0x2b]
    // 0xb1065c: StoreField: r2->field_2f = rZR
    //     0xb1065c: stur            xzr, [x2, #0x2f]
    // 0xb10660: ldur            x1, [fp, #-0x20]
    // 0xb10664: StoreField: r2->field_b = r1
    //     0xb10664: stur            w1, [x2, #0xb]
    // 0xb10668: ldur            x5, [fp, #-0x18]
    // 0xb1066c: LoadField: r1 = r5->field_b
    //     0xb1066c: ldur            w1, [x5, #0xb]
    // 0xb10670: LoadField: r6 = r5->field_f
    //     0xb10670: ldur            w6, [x5, #0xf]
    // 0xb10674: DecompressPointer r6
    //     0xb10674: add             x6, x6, HEAP, lsl #32
    // 0xb10678: LoadField: r7 = r6->field_b
    //     0xb10678: ldur            w7, [x6, #0xb]
    // 0xb1067c: r6 = LoadInt32Instr(r1)
    //     0xb1067c: sbfx            x6, x1, #1, #0x1f
    // 0xb10680: stur            x6, [fp, #-0x40]
    // 0xb10684: r1 = LoadInt32Instr(r7)
    //     0xb10684: sbfx            x1, x7, #1, #0x1f
    // 0xb10688: cmp             x6, x1
    // 0xb1068c: b.ne            #0xb10698
    // 0xb10690: mov             x1, x5
    // 0xb10694: r0 = _growToNextCapacity()
    //     0xb10694: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10698: ldur            x2, [fp, #-0x18]
    // 0xb1069c: ldur            x3, [fp, #-0x40]
    // 0xb106a0: add             x0, x3, #1
    // 0xb106a4: lsl             x1, x0, #1
    // 0xb106a8: StoreField: r2->field_b = r1
    //     0xb106a8: stur            w1, [x2, #0xb]
    // 0xb106ac: LoadField: r1 = r2->field_f
    //     0xb106ac: ldur            w1, [x2, #0xf]
    // 0xb106b0: DecompressPointer r1
    //     0xb106b0: add             x1, x1, HEAP, lsl #32
    // 0xb106b4: ldur            x0, [fp, #-0x10]
    // 0xb106b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb106b8: add             x25, x1, x3, lsl #2
    //     0xb106bc: add             x25, x25, #0xf
    //     0xb106c0: str             w0, [x25]
    //     0xb106c4: tbz             w0, #0, #0xb106e0
    //     0xb106c8: ldurb           w16, [x1, #-1]
    //     0xb106cc: ldurb           w17, [x0, #-1]
    //     0xb106d0: and             x16, x17, x16, lsr #2
    //     0xb106d4: tst             x16, HEAP, lsr #32
    //     0xb106d8: b.eq            #0xb106e0
    //     0xb106dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb106e0: b               #0xb10774
    // 0xb106e4: ldur            x2, [fp, #-0x18]
    // 0xb106e8: r0 = Container()
    //     0xb106e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb106ec: mov             x1, x0
    // 0xb106f0: stur            x0, [fp, #-0x10]
    // 0xb106f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb106f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb106f8: r0 = Container()
    //     0xb106f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb106fc: ldur            x0, [fp, #-0x18]
    // 0xb10700: LoadField: r1 = r0->field_b
    //     0xb10700: ldur            w1, [x0, #0xb]
    // 0xb10704: LoadField: r2 = r0->field_f
    //     0xb10704: ldur            w2, [x0, #0xf]
    // 0xb10708: DecompressPointer r2
    //     0xb10708: add             x2, x2, HEAP, lsl #32
    // 0xb1070c: LoadField: r3 = r2->field_b
    //     0xb1070c: ldur            w3, [x2, #0xb]
    // 0xb10710: r2 = LoadInt32Instr(r1)
    //     0xb10710: sbfx            x2, x1, #1, #0x1f
    // 0xb10714: stur            x2, [fp, #-0x40]
    // 0xb10718: r1 = LoadInt32Instr(r3)
    //     0xb10718: sbfx            x1, x3, #1, #0x1f
    // 0xb1071c: cmp             x2, x1
    // 0xb10720: b.ne            #0xb1072c
    // 0xb10724: mov             x1, x0
    // 0xb10728: r0 = _growToNextCapacity()
    //     0xb10728: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1072c: ldur            x2, [fp, #-0x18]
    // 0xb10730: ldur            x3, [fp, #-0x40]
    // 0xb10734: add             x0, x3, #1
    // 0xb10738: lsl             x1, x0, #1
    // 0xb1073c: StoreField: r2->field_b = r1
    //     0xb1073c: stur            w1, [x2, #0xb]
    // 0xb10740: LoadField: r1 = r2->field_f
    //     0xb10740: ldur            w1, [x2, #0xf]
    // 0xb10744: DecompressPointer r1
    //     0xb10744: add             x1, x1, HEAP, lsl #32
    // 0xb10748: ldur            x0, [fp, #-0x10]
    // 0xb1074c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1074c: add             x25, x1, x3, lsl #2
    //     0xb10750: add             x25, x25, #0xf
    //     0xb10754: str             w0, [x25]
    //     0xb10758: tbz             w0, #0, #0xb10774
    //     0xb1075c: ldurb           w16, [x1, #-1]
    //     0xb10760: ldurb           w17, [x0, #-1]
    //     0xb10764: and             x16, x17, x16, lsr #2
    //     0xb10768: tst             x16, HEAP, lsr #32
    //     0xb1076c: b.eq            #0xb10774
    //     0xb10770: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10774: ldur            x0, [fp, #-8]
    // 0xb10778: r0 = Column()
    //     0xb10778: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb1077c: r1 = Instance_Axis
    //     0xb1077c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb10780: StoreField: r0->field_f = r1
    //     0xb10780: stur            w1, [x0, #0xf]
    // 0xb10784: r1 = Instance_MainAxisAlignment
    //     0xb10784: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb10788: ldr             x1, [x1, #0xa08]
    // 0xb1078c: StoreField: r0->field_13 = r1
    //     0xb1078c: stur            w1, [x0, #0x13]
    // 0xb10790: r1 = Instance_MainAxisSize
    //     0xb10790: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb10794: ldr             x1, [x1, #0xa10]
    // 0xb10798: ArrayStore: r0[0] = r1  ; List_4
    //     0xb10798: stur            w1, [x0, #0x17]
    // 0xb1079c: ldur            x1, [fp, #-8]
    // 0xb107a0: StoreField: r0->field_1b = r1
    //     0xb107a0: stur            w1, [x0, #0x1b]
    // 0xb107a4: r1 = Instance_VerticalDirection
    //     0xb107a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb107a8: ldr             x1, [x1, #0xa20]
    // 0xb107ac: StoreField: r0->field_23 = r1
    //     0xb107ac: stur            w1, [x0, #0x23]
    // 0xb107b0: r1 = Instance_Clip
    //     0xb107b0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb107b4: ldr             x1, [x1, #0x38]
    // 0xb107b8: StoreField: r0->field_2b = r1
    //     0xb107b8: stur            w1, [x0, #0x2b]
    // 0xb107bc: StoreField: r0->field_2f = rZR
    //     0xb107bc: stur            xzr, [x0, #0x2f]
    // 0xb107c0: ldur            x1, [fp, #-0x18]
    // 0xb107c4: StoreField: r0->field_b = r1
    //     0xb107c4: stur            w1, [x0, #0xb]
    // 0xb107c8: LeaveFrame
    //     0xb107c8: mov             SP, fp
    //     0xb107cc: ldp             fp, lr, [SP], #0x10
    // 0xb107d0: ret
    //     0xb107d0: ret             
    // 0xb107d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb107d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb107d8: b               #0xb0fec4
    // 0xb107dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb107dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb107e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb107e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb107e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb107e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb107e8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb107e8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb107ec: r9 = _pageController
    //     0xb107ec: add             x9, PP, #0x57, lsl #12  ; [pp+0x57b30] Field <_ProductTestimonialCarouselState@1502196050._pageController@1502196050>: late (offset: 0x14)
    //     0xb107f0: ldr             x9, [x9, #0xb30]
    // 0xb107f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb107f4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb107f8: SaveReg d0
    //     0xb107f8: str             q0, [SP, #-0x10]!
    // 0xb107fc: r0 = AllocateDouble()
    //     0xb107fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb10800: RestoreReg d0
    //     0xb10800: ldr             q0, [SP], #0x10
    // 0xb10804: b               #0xb1042c
    // 0xb10808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb10808: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1080c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1080c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb10810, size: 0x70
    // 0xb10810: EnterFrame
    //     0xb10810: stp             fp, lr, [SP, #-0x10]!
    //     0xb10814: mov             fp, SP
    // 0xb10818: ldr             x0, [fp, #0x20]
    // 0xb1081c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb1081c: ldur            w1, [x0, #0x17]
    // 0xb10820: DecompressPointer r1
    //     0xb10820: add             x1, x1, HEAP, lsl #32
    // 0xb10824: CheckStackOverflow
    //     0xb10824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb10828: cmp             SP, x16
    //     0xb1082c: b.ls            #0xb10874
    // 0xb10830: LoadField: r0 = r1->field_f
    //     0xb10830: ldur            w0, [x1, #0xf]
    // 0xb10834: DecompressPointer r0
    //     0xb10834: add             x0, x0, HEAP, lsl #32
    // 0xb10838: LoadField: r1 = r0->field_b
    //     0xb10838: ldur            w1, [x0, #0xb]
    // 0xb1083c: DecompressPointer r1
    //     0xb1083c: add             x1, x1, HEAP, lsl #32
    // 0xb10840: cmp             w1, NULL
    // 0xb10844: b.eq            #0xb1087c
    // 0xb10848: LoadField: r2 = r1->field_b
    //     0xb10848: ldur            w2, [x1, #0xb]
    // 0xb1084c: DecompressPointer r2
    //     0xb1084c: add             x2, x2, HEAP, lsl #32
    // 0xb10850: ldr             x1, [fp, #0x10]
    // 0xb10854: r3 = LoadInt32Instr(r1)
    //     0xb10854: sbfx            x3, x1, #1, #0x1f
    //     0xb10858: tbz             w1, #0, #0xb10860
    //     0xb1085c: ldur            x3, [x1, #7]
    // 0xb10860: mov             x1, x0
    // 0xb10864: r0 = _testimonialCard()
    //     0xb10864: bl              #0xb10880  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xb10868: LeaveFrame
    //     0xb10868: mov             SP, fp
    //     0xb1086c: ldp             fp, lr, [SP], #0x10
    // 0xb10870: ret
    //     0xb10870: ret             
    // 0xb10874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb10874: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb10878: b               #0xb10830
    // 0xb1087c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1087c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xb10880, size: 0xed0
    // 0xb10880: EnterFrame
    //     0xb10880: stp             fp, lr, [SP, #-0x10]!
    //     0xb10884: mov             fp, SP
    // 0xb10888: AllocStack(0xa0)
    //     0xb10888: sub             SP, SP, #0xa0
    // 0xb1088c: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb1088c: stur            x1, [fp, #-8]
    //     0xb10890: stur            x2, [fp, #-0x10]
    //     0xb10894: stur            x3, [fp, #-0x18]
    // 0xb10898: CheckStackOverflow
    //     0xb10898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1089c: cmp             SP, x16
    //     0xb108a0: b.ls            #0xb1172c
    // 0xb108a4: r1 = 2
    //     0xb108a4: movz            x1, #0x2
    // 0xb108a8: r0 = AllocateContext()
    //     0xb108a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb108ac: mov             x3, x0
    // 0xb108b0: ldur            x2, [fp, #-8]
    // 0xb108b4: stur            x3, [fp, #-0x28]
    // 0xb108b8: StoreField: r3->field_f = r2
    //     0xb108b8: stur            w2, [x3, #0xf]
    // 0xb108bc: ldur            x4, [fp, #-0x18]
    // 0xb108c0: r0 = BoxInt64Instr(r4)
    //     0xb108c0: sbfiz           x0, x4, #1, #0x1f
    //     0xb108c4: cmp             x4, x0, asr #1
    //     0xb108c8: b.eq            #0xb108d4
    //     0xb108cc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb108d0: stur            x4, [x0, #7]
    // 0xb108d4: mov             x4, x0
    // 0xb108d8: ldur            x1, [fp, #-0x10]
    // 0xb108dc: stur            x4, [fp, #-0x20]
    // 0xb108e0: r0 = LoadClassIdInstr(r1)
    //     0xb108e0: ldur            x0, [x1, #-1]
    //     0xb108e4: ubfx            x0, x0, #0xc, #0x14
    // 0xb108e8: stp             x4, x1, [SP]
    // 0xb108ec: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb108ec: sub             lr, x0, #0xb7
    //     0xb108f0: ldr             lr, [x21, lr, lsl #3]
    //     0xb108f4: blr             lr
    // 0xb108f8: stur            x0, [fp, #-0x40]
    // 0xb108fc: cmp             w0, NULL
    // 0xb10900: b.ne            #0xb1090c
    // 0xb10904: r1 = Null
    //     0xb10904: mov             x1, NULL
    // 0xb10908: b               #0xb10938
    // 0xb1090c: LoadField: r1 = r0->field_b7
    //     0xb1090c: ldur            w1, [x0, #0xb7]
    // 0xb10910: DecompressPointer r1
    //     0xb10910: add             x1, x1, HEAP, lsl #32
    // 0xb10914: cmp             w1, NULL
    // 0xb10918: b.ne            #0xb10924
    // 0xb1091c: r1 = Null
    //     0xb1091c: mov             x1, NULL
    // 0xb10920: b               #0xb10938
    // 0xb10924: LoadField: r2 = r1->field_b
    //     0xb10924: ldur            w2, [x1, #0xb]
    // 0xb10928: cbnz            w2, #0xb10934
    // 0xb1092c: r1 = false
    //     0xb1092c: add             x1, NULL, #0x30  ; false
    // 0xb10930: b               #0xb10938
    // 0xb10934: r1 = true
    //     0xb10934: add             x1, NULL, #0x20  ; true
    // 0xb10938: cmp             w1, NULL
    // 0xb1093c: b.ne            #0xb10948
    // 0xb10940: r4 = false
    //     0xb10940: add             x4, NULL, #0x30  ; false
    // 0xb10944: b               #0xb1094c
    // 0xb10948: mov             x4, x1
    // 0xb1094c: ldur            x3, [fp, #-8]
    // 0xb10950: stur            x4, [fp, #-0x38]
    // 0xb10954: LoadField: r5 = r3->field_1f
    //     0xb10954: ldur            w5, [x3, #0x1f]
    // 0xb10958: DecompressPointer r5
    //     0xb10958: add             x5, x5, HEAP, lsl #32
    // 0xb1095c: mov             x1, x5
    // 0xb10960: ldur            x2, [fp, #-0x20]
    // 0xb10964: stur            x5, [fp, #-0x30]
    // 0xb10968: r0 = _getValueOrData()
    //     0xb10968: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xb1096c: mov             x1, x0
    // 0xb10970: ldur            x0, [fp, #-0x30]
    // 0xb10974: LoadField: r2 = r0->field_f
    //     0xb10974: ldur            w2, [x0, #0xf]
    // 0xb10978: DecompressPointer r2
    //     0xb10978: add             x2, x2, HEAP, lsl #32
    // 0xb1097c: cmp             w2, w1
    // 0xb10980: b.ne            #0xb1098c
    // 0xb10984: r0 = Null
    //     0xb10984: mov             x0, NULL
    // 0xb10988: b               #0xb10990
    // 0xb1098c: mov             x0, x1
    // 0xb10990: cmp             w0, NULL
    // 0xb10994: b.ne            #0xb109d4
    // 0xb10998: r1 = <bool>
    //     0xb10998: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xb1099c: r0 = RxBool()
    //     0xb1099c: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xb109a0: mov             x2, x0
    // 0xb109a4: r0 = Sentinel
    //     0xb109a4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb109a8: stur            x2, [fp, #-0x30]
    // 0xb109ac: StoreField: r2->field_13 = r0
    //     0xb109ac: stur            w0, [x2, #0x13]
    // 0xb109b0: r0 = true
    //     0xb109b0: add             x0, NULL, #0x20  ; true
    // 0xb109b4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb109b4: stur            w0, [x2, #0x17]
    // 0xb109b8: mov             x1, x2
    // 0xb109bc: r0 = RxNotifier()
    //     0xb109bc: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xb109c0: ldur            x0, [fp, #-0x30]
    // 0xb109c4: r1 = false
    //     0xb109c4: add             x1, NULL, #0x30  ; false
    // 0xb109c8: StoreField: r0->field_13 = r1
    //     0xb109c8: stur            w1, [x0, #0x13]
    // 0xb109cc: mov             x4, x0
    // 0xb109d0: b               #0xb109dc
    // 0xb109d4: r1 = false
    //     0xb109d4: add             x1, NULL, #0x30  ; false
    // 0xb109d8: mov             x4, x0
    // 0xb109dc: ldur            x3, [fp, #-0x28]
    // 0xb109e0: ldur            x2, [fp, #-0x38]
    // 0xb109e4: mov             x0, x4
    // 0xb109e8: stur            x4, [fp, #-0x30]
    // 0xb109ec: StoreField: r3->field_13 = r0
    //     0xb109ec: stur            w0, [x3, #0x13]
    //     0xb109f0: ldurb           w16, [x3, #-1]
    //     0xb109f4: ldurb           w17, [x0, #-1]
    //     0xb109f8: and             x16, x17, x16, lsr #2
    //     0xb109fc: tst             x16, HEAP, lsr #32
    //     0xb10a00: b.eq            #0xb10a08
    //     0xb10a04: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb10a08: r0 = Radius()
    //     0xb10a08: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb10a0c: d0 = 12.000000
    //     0xb10a0c: fmov            d0, #12.00000000
    // 0xb10a10: stur            x0, [fp, #-0x48]
    // 0xb10a14: StoreField: r0->field_7 = d0
    //     0xb10a14: stur            d0, [x0, #7]
    // 0xb10a18: StoreField: r0->field_f = d0
    //     0xb10a18: stur            d0, [x0, #0xf]
    // 0xb10a1c: r0 = BorderRadius()
    //     0xb10a1c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb10a20: mov             x1, x0
    // 0xb10a24: ldur            x0, [fp, #-0x48]
    // 0xb10a28: stur            x1, [fp, #-0x50]
    // 0xb10a2c: StoreField: r1->field_7 = r0
    //     0xb10a2c: stur            w0, [x1, #7]
    // 0xb10a30: StoreField: r1->field_b = r0
    //     0xb10a30: stur            w0, [x1, #0xb]
    // 0xb10a34: StoreField: r1->field_f = r0
    //     0xb10a34: stur            w0, [x1, #0xf]
    // 0xb10a38: StoreField: r1->field_13 = r0
    //     0xb10a38: stur            w0, [x1, #0x13]
    // 0xb10a3c: r0 = RoundedRectangleBorder()
    //     0xb10a3c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb10a40: mov             x3, x0
    // 0xb10a44: ldur            x0, [fp, #-0x50]
    // 0xb10a48: stur            x3, [fp, #-0x48]
    // 0xb10a4c: StoreField: r3->field_b = r0
    //     0xb10a4c: stur            w0, [x3, #0xb]
    // 0xb10a50: r0 = Instance_BorderSide
    //     0xb10a50: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb10a54: ldr             x0, [x0, #0xe20]
    // 0xb10a58: StoreField: r3->field_7 = r0
    //     0xb10a58: stur            w0, [x3, #7]
    // 0xb10a5c: r1 = <Widget>
    //     0xb10a5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb10a60: r2 = 0
    //     0xb10a60: movz            x2, #0
    // 0xb10a64: r0 = _GrowableList()
    //     0xb10a64: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb10a68: mov             x1, x0
    // 0xb10a6c: ldur            x0, [fp, #-0x38]
    // 0xb10a70: stur            x1, [fp, #-0x50]
    // 0xb10a74: tbnz            w0, #4, #0xb10c40
    // 0xb10a78: ldur            x2, [fp, #-0x10]
    // 0xb10a7c: r0 = LoadClassIdInstr(r2)
    //     0xb10a7c: ldur            x0, [x2, #-1]
    //     0xb10a80: ubfx            x0, x0, #0xc, #0x14
    // 0xb10a84: ldur            x16, [fp, #-0x20]
    // 0xb10a88: stp             x16, x2, [SP]
    // 0xb10a8c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb10a8c: sub             lr, x0, #0xb7
    //     0xb10a90: ldr             lr, [x21, lr, lsl #3]
    //     0xb10a94: blr             lr
    // 0xb10a98: cmp             w0, NULL
    // 0xb10a9c: b.ne            #0xb10aa8
    // 0xb10aa0: r0 = Null
    //     0xb10aa0: mov             x0, NULL
    // 0xb10aa4: b               #0xb10b0c
    // 0xb10aa8: LoadField: r2 = r0->field_b7
    //     0xb10aa8: ldur            w2, [x0, #0xb7]
    // 0xb10aac: DecompressPointer r2
    //     0xb10aac: add             x2, x2, HEAP, lsl #32
    // 0xb10ab0: cmp             w2, NULL
    // 0xb10ab4: b.ne            #0xb10ac0
    // 0xb10ab8: r0 = Null
    //     0xb10ab8: mov             x0, NULL
    // 0xb10abc: b               #0xb10b0c
    // 0xb10ac0: LoadField: r0 = r2->field_b
    //     0xb10ac0: ldur            w0, [x2, #0xb]
    // 0xb10ac4: r1 = LoadInt32Instr(r0)
    //     0xb10ac4: sbfx            x1, x0, #1, #0x1f
    // 0xb10ac8: mov             x0, x1
    // 0xb10acc: r1 = 0
    //     0xb10acc: movz            x1, #0
    // 0xb10ad0: cmp             x1, x0
    // 0xb10ad4: b.hs            #0xb11734
    // 0xb10ad8: LoadField: r0 = r2->field_f
    //     0xb10ad8: ldur            w0, [x2, #0xf]
    // 0xb10adc: DecompressPointer r0
    //     0xb10adc: add             x0, x0, HEAP, lsl #32
    // 0xb10ae0: LoadField: r1 = r0->field_f
    //     0xb10ae0: ldur            w1, [x0, #0xf]
    // 0xb10ae4: DecompressPointer r1
    //     0xb10ae4: add             x1, x1, HEAP, lsl #32
    // 0xb10ae8: LoadField: r0 = r1->field_7
    //     0xb10ae8: ldur            w0, [x1, #7]
    // 0xb10aec: DecompressPointer r0
    //     0xb10aec: add             x0, x0, HEAP, lsl #32
    // 0xb10af0: cmp             w0, NULL
    // 0xb10af4: b.ne            #0xb10b00
    // 0xb10af8: r0 = Null
    //     0xb10af8: mov             x0, NULL
    // 0xb10afc: b               #0xb10b0c
    // 0xb10b00: LoadField: r1 = r0->field_b
    //     0xb10b00: ldur            w1, [x0, #0xb]
    // 0xb10b04: DecompressPointer r1
    //     0xb10b04: add             x1, x1, HEAP, lsl #32
    // 0xb10b08: mov             x0, x1
    // 0xb10b0c: cmp             w0, NULL
    // 0xb10b10: b.ne            #0xb10b1c
    // 0xb10b14: r3 = ""
    //     0xb10b14: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb10b18: b               #0xb10b20
    // 0xb10b1c: mov             x3, x0
    // 0xb10b20: ldur            x0, [fp, #-0x50]
    // 0xb10b24: stur            x3, [fp, #-0x38]
    // 0xb10b28: r1 = Function '<anonymous closure>':.
    //     0xb10b28: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b58] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb10b2c: ldr             x1, [x1, #0xb58]
    // 0xb10b30: r2 = Null
    //     0xb10b30: mov             x2, NULL
    // 0xb10b34: r0 = AllocateClosure()
    //     0xb10b34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb10b38: r1 = Function '<anonymous closure>':.
    //     0xb10b38: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b60] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb10b3c: ldr             x1, [x1, #0xb60]
    // 0xb10b40: r2 = Null
    //     0xb10b40: mov             x2, NULL
    // 0xb10b44: stur            x0, [fp, #-0x58]
    // 0xb10b48: r0 = AllocateClosure()
    //     0xb10b48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb10b4c: stur            x0, [fp, #-0x60]
    // 0xb10b50: r0 = CachedNetworkImage()
    //     0xb10b50: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb10b54: stur            x0, [fp, #-0x68]
    // 0xb10b58: ldur            x16, [fp, #-0x58]
    // 0xb10b5c: ldur            lr, [fp, #-0x60]
    // 0xb10b60: stp             lr, x16, [SP, #0x18]
    // 0xb10b64: r16 = inf
    //     0xb10b64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb10b68: ldr             x16, [x16, #0x9f8]
    // 0xb10b6c: r30 = 336.000000
    //     0xb10b6c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55530] 336
    //     0xb10b70: ldr             lr, [lr, #0x530]
    // 0xb10b74: stp             lr, x16, [SP, #8]
    // 0xb10b78: r16 = Instance_BoxFit
    //     0xb10b78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb10b7c: ldr             x16, [x16, #0x118]
    // 0xb10b80: str             x16, [SP]
    // 0xb10b84: mov             x1, x0
    // 0xb10b88: ldur            x2, [fp, #-0x38]
    // 0xb10b8c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x3, fit, 0x6, height, 0x5, progressIndicatorBuilder, 0x2, width, 0x4, null]
    //     0xb10b8c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55538] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x3, "fit", 0x6, "height", 0x5, "progressIndicatorBuilder", 0x2, "width", 0x4, Null]
    //     0xb10b90: ldr             x4, [x4, #0x538]
    // 0xb10b94: r0 = CachedNetworkImage()
    //     0xb10b94: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb10b98: r0 = ClipRRect()
    //     0xb10b98: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb10b9c: mov             x2, x0
    // 0xb10ba0: r0 = Instance_BorderRadius
    //     0xb10ba0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb10ba4: ldr             x0, [x0, #0xe10]
    // 0xb10ba8: stur            x2, [fp, #-0x38]
    // 0xb10bac: StoreField: r2->field_f = r0
    //     0xb10bac: stur            w0, [x2, #0xf]
    // 0xb10bb0: r0 = Instance_Clip
    //     0xb10bb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb10bb4: ldr             x0, [x0, #0x138]
    // 0xb10bb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb10bb8: stur            w0, [x2, #0x17]
    // 0xb10bbc: ldur            x0, [fp, #-0x68]
    // 0xb10bc0: StoreField: r2->field_b = r0
    //     0xb10bc0: stur            w0, [x2, #0xb]
    // 0xb10bc4: ldur            x0, [fp, #-0x50]
    // 0xb10bc8: LoadField: r1 = r0->field_b
    //     0xb10bc8: ldur            w1, [x0, #0xb]
    // 0xb10bcc: LoadField: r3 = r0->field_f
    //     0xb10bcc: ldur            w3, [x0, #0xf]
    // 0xb10bd0: DecompressPointer r3
    //     0xb10bd0: add             x3, x3, HEAP, lsl #32
    // 0xb10bd4: LoadField: r4 = r3->field_b
    //     0xb10bd4: ldur            w4, [x3, #0xb]
    // 0xb10bd8: r3 = LoadInt32Instr(r1)
    //     0xb10bd8: sbfx            x3, x1, #1, #0x1f
    // 0xb10bdc: stur            x3, [fp, #-0x18]
    // 0xb10be0: r1 = LoadInt32Instr(r4)
    //     0xb10be0: sbfx            x1, x4, #1, #0x1f
    // 0xb10be4: cmp             x3, x1
    // 0xb10be8: b.ne            #0xb10bf4
    // 0xb10bec: mov             x1, x0
    // 0xb10bf0: r0 = _growToNextCapacity()
    //     0xb10bf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10bf4: ldur            x2, [fp, #-0x50]
    // 0xb10bf8: ldur            x3, [fp, #-0x18]
    // 0xb10bfc: add             x0, x3, #1
    // 0xb10c00: lsl             x1, x0, #1
    // 0xb10c04: StoreField: r2->field_b = r1
    //     0xb10c04: stur            w1, [x2, #0xb]
    // 0xb10c08: LoadField: r1 = r2->field_f
    //     0xb10c08: ldur            w1, [x2, #0xf]
    // 0xb10c0c: DecompressPointer r1
    //     0xb10c0c: add             x1, x1, HEAP, lsl #32
    // 0xb10c10: ldur            x0, [fp, #-0x38]
    // 0xb10c14: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb10c14: add             x25, x1, x3, lsl #2
    //     0xb10c18: add             x25, x25, #0xf
    //     0xb10c1c: str             w0, [x25]
    //     0xb10c20: tbz             w0, #0, #0xb10c3c
    //     0xb10c24: ldurb           w16, [x1, #-1]
    //     0xb10c28: ldurb           w17, [x0, #-1]
    //     0xb10c2c: and             x16, x17, x16, lsr #2
    //     0xb10c30: tst             x16, HEAP, lsr #32
    //     0xb10c34: b.eq            #0xb10c3c
    //     0xb10c38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10c3c: b               #0xb10cd0
    // 0xb10c40: mov             x2, x1
    // 0xb10c44: r0 = Container()
    //     0xb10c44: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb10c48: mov             x1, x0
    // 0xb10c4c: stur            x0, [fp, #-0x38]
    // 0xb10c50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb10c50: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb10c54: r0 = Container()
    //     0xb10c54: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb10c58: ldur            x0, [fp, #-0x50]
    // 0xb10c5c: LoadField: r1 = r0->field_b
    //     0xb10c5c: ldur            w1, [x0, #0xb]
    // 0xb10c60: LoadField: r2 = r0->field_f
    //     0xb10c60: ldur            w2, [x0, #0xf]
    // 0xb10c64: DecompressPointer r2
    //     0xb10c64: add             x2, x2, HEAP, lsl #32
    // 0xb10c68: LoadField: r3 = r2->field_b
    //     0xb10c68: ldur            w3, [x2, #0xb]
    // 0xb10c6c: r2 = LoadInt32Instr(r1)
    //     0xb10c6c: sbfx            x2, x1, #1, #0x1f
    // 0xb10c70: stur            x2, [fp, #-0x18]
    // 0xb10c74: r1 = LoadInt32Instr(r3)
    //     0xb10c74: sbfx            x1, x3, #1, #0x1f
    // 0xb10c78: cmp             x2, x1
    // 0xb10c7c: b.ne            #0xb10c88
    // 0xb10c80: mov             x1, x0
    // 0xb10c84: r0 = _growToNextCapacity()
    //     0xb10c84: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10c88: ldur            x2, [fp, #-0x50]
    // 0xb10c8c: ldur            x3, [fp, #-0x18]
    // 0xb10c90: add             x0, x3, #1
    // 0xb10c94: lsl             x1, x0, #1
    // 0xb10c98: StoreField: r2->field_b = r1
    //     0xb10c98: stur            w1, [x2, #0xb]
    // 0xb10c9c: LoadField: r1 = r2->field_f
    //     0xb10c9c: ldur            w1, [x2, #0xf]
    // 0xb10ca0: DecompressPointer r1
    //     0xb10ca0: add             x1, x1, HEAP, lsl #32
    // 0xb10ca4: ldur            x0, [fp, #-0x38]
    // 0xb10ca8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb10ca8: add             x25, x1, x3, lsl #2
    //     0xb10cac: add             x25, x25, #0xf
    //     0xb10cb0: str             w0, [x25]
    //     0xb10cb4: tbz             w0, #0, #0xb10cd0
    //     0xb10cb8: ldurb           w16, [x1, #-1]
    //     0xb10cbc: ldurb           w17, [x0, #-1]
    //     0xb10cc0: and             x16, x17, x16, lsr #2
    //     0xb10cc4: tst             x16, HEAP, lsr #32
    //     0xb10cc8: b.eq            #0xb10cd0
    //     0xb10ccc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10cd0: ldur            x1, [fp, #-0x10]
    // 0xb10cd4: r0 = LoadClassIdInstr(r1)
    //     0xb10cd4: ldur            x0, [x1, #-1]
    //     0xb10cd8: ubfx            x0, x0, #0xc, #0x14
    // 0xb10cdc: ldur            x16, [fp, #-0x20]
    // 0xb10ce0: stp             x16, x1, [SP]
    // 0xb10ce4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb10ce4: sub             lr, x0, #0xb7
    //     0xb10ce8: ldr             lr, [x21, lr, lsl #3]
    //     0xb10cec: blr             lr
    // 0xb10cf0: cmp             w0, NULL
    // 0xb10cf4: b.ne            #0xb10d00
    // 0xb10cf8: r0 = Null
    //     0xb10cf8: mov             x0, NULL
    // 0xb10cfc: b               #0xb10d0c
    // 0xb10d00: LoadField: r1 = r0->field_cb
    //     0xb10d00: ldur            w1, [x0, #0xcb]
    // 0xb10d04: DecompressPointer r1
    //     0xb10d04: add             x1, x1, HEAP, lsl #32
    // 0xb10d08: mov             x0, x1
    // 0xb10d0c: cmp             w0, NULL
    // 0xb10d10: b.ne            #0xb10d1c
    // 0xb10d14: r1 = ""
    //     0xb10d14: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb10d18: b               #0xb10d20
    // 0xb10d1c: mov             x1, x0
    // 0xb10d20: ldur            x2, [fp, #-8]
    // 0xb10d24: ldur            x0, [fp, #-0x50]
    // 0xb10d28: r0 = capitalizeFirstWord()
    //     0xb10d28: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb10d2c: mov             x2, x0
    // 0xb10d30: ldur            x0, [fp, #-8]
    // 0xb10d34: stur            x2, [fp, #-0x38]
    // 0xb10d38: LoadField: r1 = r0->field_f
    //     0xb10d38: ldur            w1, [x0, #0xf]
    // 0xb10d3c: DecompressPointer r1
    //     0xb10d3c: add             x1, x1, HEAP, lsl #32
    // 0xb10d40: cmp             w1, NULL
    // 0xb10d44: b.eq            #0xb11738
    // 0xb10d48: r0 = of()
    //     0xb10d48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb10d4c: LoadField: r1 = r0->field_87
    //     0xb10d4c: ldur            w1, [x0, #0x87]
    // 0xb10d50: DecompressPointer r1
    //     0xb10d50: add             x1, x1, HEAP, lsl #32
    // 0xb10d54: LoadField: r0 = r1->field_7
    //     0xb10d54: ldur            w0, [x1, #7]
    // 0xb10d58: DecompressPointer r0
    //     0xb10d58: add             x0, x0, HEAP, lsl #32
    // 0xb10d5c: r16 = 14.000000
    //     0xb10d5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb10d60: ldr             x16, [x16, #0x1d8]
    // 0xb10d64: r30 = Instance_Color
    //     0xb10d64: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb10d68: stp             lr, x16, [SP]
    // 0xb10d6c: mov             x1, x0
    // 0xb10d70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb10d70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb10d74: ldr             x4, [x4, #0xaa0]
    // 0xb10d78: r0 = copyWith()
    //     0xb10d78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb10d7c: stur            x0, [fp, #-0x58]
    // 0xb10d80: r0 = Text()
    //     0xb10d80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb10d84: mov             x1, x0
    // 0xb10d88: ldur            x0, [fp, #-0x38]
    // 0xb10d8c: stur            x1, [fp, #-0x60]
    // 0xb10d90: StoreField: r1->field_b = r0
    //     0xb10d90: stur            w0, [x1, #0xb]
    // 0xb10d94: ldur            x0, [fp, #-0x58]
    // 0xb10d98: StoreField: r1->field_13 = r0
    //     0xb10d98: stur            w0, [x1, #0x13]
    // 0xb10d9c: r0 = Padding()
    //     0xb10d9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb10da0: mov             x2, x0
    // 0xb10da4: r0 = Instance_EdgeInsets
    //     0xb10da4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xb10da8: ldr             x0, [x0, #0xa98]
    // 0xb10dac: stur            x2, [fp, #-0x38]
    // 0xb10db0: StoreField: r2->field_f = r0
    //     0xb10db0: stur            w0, [x2, #0xf]
    // 0xb10db4: ldur            x0, [fp, #-0x60]
    // 0xb10db8: StoreField: r2->field_b = r0
    //     0xb10db8: stur            w0, [x2, #0xb]
    // 0xb10dbc: ldur            x0, [fp, #-0x50]
    // 0xb10dc0: LoadField: r1 = r0->field_b
    //     0xb10dc0: ldur            w1, [x0, #0xb]
    // 0xb10dc4: LoadField: r3 = r0->field_f
    //     0xb10dc4: ldur            w3, [x0, #0xf]
    // 0xb10dc8: DecompressPointer r3
    //     0xb10dc8: add             x3, x3, HEAP, lsl #32
    // 0xb10dcc: LoadField: r4 = r3->field_b
    //     0xb10dcc: ldur            w4, [x3, #0xb]
    // 0xb10dd0: r3 = LoadInt32Instr(r1)
    //     0xb10dd0: sbfx            x3, x1, #1, #0x1f
    // 0xb10dd4: stur            x3, [fp, #-0x18]
    // 0xb10dd8: r1 = LoadInt32Instr(r4)
    //     0xb10dd8: sbfx            x1, x4, #1, #0x1f
    // 0xb10ddc: cmp             x3, x1
    // 0xb10de0: b.ne            #0xb10dec
    // 0xb10de4: mov             x1, x0
    // 0xb10de8: r0 = _growToNextCapacity()
    //     0xb10de8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10dec: ldur            x2, [fp, #-0x50]
    // 0xb10df0: ldur            x3, [fp, #-0x18]
    // 0xb10df4: add             x4, x3, #1
    // 0xb10df8: stur            x4, [fp, #-0x70]
    // 0xb10dfc: lsl             x0, x4, #1
    // 0xb10e00: StoreField: r2->field_b = r0
    //     0xb10e00: stur            w0, [x2, #0xb]
    // 0xb10e04: LoadField: r5 = r2->field_f
    //     0xb10e04: ldur            w5, [x2, #0xf]
    // 0xb10e08: DecompressPointer r5
    //     0xb10e08: add             x5, x5, HEAP, lsl #32
    // 0xb10e0c: mov             x1, x5
    // 0xb10e10: ldur            x0, [fp, #-0x38]
    // 0xb10e14: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb10e14: add             x25, x1, x3, lsl #2
    //     0xb10e18: add             x25, x25, #0xf
    //     0xb10e1c: str             w0, [x25]
    //     0xb10e20: tbz             w0, #0, #0xb10e3c
    //     0xb10e24: ldurb           w16, [x1, #-1]
    //     0xb10e28: ldurb           w17, [x0, #-1]
    //     0xb10e2c: and             x16, x17, x16, lsr #2
    //     0xb10e30: tst             x16, HEAP, lsr #32
    //     0xb10e34: b.eq            #0xb10e3c
    //     0xb10e38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb10e3c: LoadField: r0 = r5->field_b
    //     0xb10e3c: ldur            w0, [x5, #0xb]
    // 0xb10e40: r1 = LoadInt32Instr(r0)
    //     0xb10e40: sbfx            x1, x0, #1, #0x1f
    // 0xb10e44: cmp             x4, x1
    // 0xb10e48: b.ne            #0xb10e54
    // 0xb10e4c: mov             x1, x2
    // 0xb10e50: r0 = _growToNextCapacity()
    //     0xb10e50: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb10e54: ldur            x2, [fp, #-0x10]
    // 0xb10e58: ldur            x1, [fp, #-0x50]
    // 0xb10e5c: ldur            x0, [fp, #-0x70]
    // 0xb10e60: add             x3, x0, #1
    // 0xb10e64: lsl             x4, x3, #1
    // 0xb10e68: StoreField: r1->field_b = r4
    //     0xb10e68: stur            w4, [x1, #0xb]
    // 0xb10e6c: LoadField: r3 = r1->field_f
    //     0xb10e6c: ldur            w3, [x1, #0xf]
    // 0xb10e70: DecompressPointer r3
    //     0xb10e70: add             x3, x3, HEAP, lsl #32
    // 0xb10e74: add             x4, x3, x0, lsl #2
    // 0xb10e78: r16 = Instance_SizedBox
    //     0xb10e78: add             x16, PP, #0x55, lsl #12  ; [pp+0x55540] Obj!SizedBox@d67fe1
    //     0xb10e7c: ldr             x16, [x16, #0x540]
    // 0xb10e80: StoreField: r4->field_f = r16
    //     0xb10e80: stur            w16, [x4, #0xf]
    // 0xb10e84: r0 = LoadClassIdInstr(r2)
    //     0xb10e84: ldur            x0, [x2, #-1]
    //     0xb10e88: ubfx            x0, x0, #0xc, #0x14
    // 0xb10e8c: ldur            x16, [fp, #-0x20]
    // 0xb10e90: stp             x16, x2, [SP]
    // 0xb10e94: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb10e94: sub             lr, x0, #0xb7
    //     0xb10e98: ldr             lr, [x21, lr, lsl #3]
    //     0xb10e9c: blr             lr
    // 0xb10ea0: cmp             w0, NULL
    // 0xb10ea4: b.ne            #0xb10eb0
    // 0xb10ea8: r0 = Null
    //     0xb10ea8: mov             x0, NULL
    // 0xb10eac: b               #0xb10ebc
    // 0xb10eb0: LoadField: r1 = r0->field_bb
    //     0xb10eb0: ldur            w1, [x0, #0xbb]
    // 0xb10eb4: DecompressPointer r1
    //     0xb10eb4: add             x1, x1, HEAP, lsl #32
    // 0xb10eb8: mov             x0, x1
    // 0xb10ebc: cmp             w0, NULL
    // 0xb10ec0: b.ne            #0xb10ecc
    // 0xb10ec4: r1 = ""
    //     0xb10ec4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb10ec8: b               #0xb10ed0
    // 0xb10ecc: mov             x1, x0
    // 0xb10ed0: ldur            x0, [fp, #-0x10]
    // 0xb10ed4: r0 = parse()
    //     0xb10ed4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb10ed8: ldur            x1, [fp, #-0x10]
    // 0xb10edc: stur            d0, [fp, #-0x78]
    // 0xb10ee0: r0 = LoadClassIdInstr(r1)
    //     0xb10ee0: ldur            x0, [x1, #-1]
    //     0xb10ee4: ubfx            x0, x0, #0xc, #0x14
    // 0xb10ee8: ldur            x16, [fp, #-0x20]
    // 0xb10eec: stp             x16, x1, [SP]
    // 0xb10ef0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb10ef0: sub             lr, x0, #0xb7
    //     0xb10ef4: ldr             lr, [x21, lr, lsl #3]
    //     0xb10ef8: blr             lr
    // 0xb10efc: cmp             w0, NULL
    // 0xb10f00: b.ne            #0xb10f0c
    // 0xb10f04: r0 = Null
    //     0xb10f04: mov             x0, NULL
    // 0xb10f08: b               #0xb10f18
    // 0xb10f0c: LoadField: r1 = r0->field_bb
    //     0xb10f0c: ldur            w1, [x0, #0xbb]
    // 0xb10f10: DecompressPointer r1
    //     0xb10f10: add             x1, x1, HEAP, lsl #32
    // 0xb10f14: mov             x0, x1
    // 0xb10f18: cmp             w0, NULL
    // 0xb10f1c: b.ne            #0xb10f28
    // 0xb10f20: r1 = ""
    //     0xb10f20: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb10f24: b               #0xb10f2c
    // 0xb10f28: mov             x1, x0
    // 0xb10f2c: ldur            x0, [fp, #-0x10]
    // 0xb10f30: ldur            d0, [fp, #-0x78]
    // 0xb10f34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb10f34: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb10f38: r0 = parse()
    //     0xb10f38: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb10f3c: stur            x0, [fp, #-0x18]
    // 0xb10f40: r0 = RatingWidget()
    //     0xb10f40: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xb10f44: mov             x3, x0
    // 0xb10f48: r0 = Instance_Icon
    //     0xb10f48: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xb10f4c: ldr             x0, [x0, #0x190]
    // 0xb10f50: stur            x3, [fp, #-0x38]
    // 0xb10f54: StoreField: r3->field_7 = r0
    //     0xb10f54: stur            w0, [x3, #7]
    // 0xb10f58: r0 = Instance_Icon
    //     0xb10f58: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xb10f5c: ldr             x0, [x0, #0x198]
    // 0xb10f60: StoreField: r3->field_b = r0
    //     0xb10f60: stur            w0, [x3, #0xb]
    // 0xb10f64: r0 = Instance_Icon
    //     0xb10f64: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xb10f68: ldr             x0, [x0, #0x1a0]
    // 0xb10f6c: StoreField: r3->field_f = r0
    //     0xb10f6c: stur            w0, [x3, #0xf]
    // 0xb10f70: r1 = Function '<anonymous closure>':.
    //     0xb10f70: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b68] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb10f74: ldr             x1, [x1, #0xb68]
    // 0xb10f78: r2 = Null
    //     0xb10f78: mov             x2, NULL
    // 0xb10f7c: r0 = AllocateClosure()
    //     0xb10f7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb10f80: stur            x0, [fp, #-0x58]
    // 0xb10f84: r0 = RatingBar()
    //     0xb10f84: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xb10f88: mov             x1, x0
    // 0xb10f8c: ldur            x0, [fp, #-0x58]
    // 0xb10f90: stur            x1, [fp, #-0x60]
    // 0xb10f94: StoreField: r1->field_b = r0
    //     0xb10f94: stur            w0, [x1, #0xb]
    // 0xb10f98: r2 = true
    //     0xb10f98: add             x2, NULL, #0x20  ; true
    // 0xb10f9c: StoreField: r1->field_1f = r2
    //     0xb10f9c: stur            w2, [x1, #0x1f]
    // 0xb10fa0: r3 = Instance_Axis
    //     0xb10fa0: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb10fa4: StoreField: r1->field_23 = r3
    //     0xb10fa4: stur            w3, [x1, #0x23]
    // 0xb10fa8: StoreField: r1->field_27 = r2
    //     0xb10fa8: stur            w2, [x1, #0x27]
    // 0xb10fac: d0 = 2.000000
    //     0xb10fac: fmov            d0, #2.00000000
    // 0xb10fb0: StoreField: r1->field_2b = d0
    //     0xb10fb0: stur            d0, [x1, #0x2b]
    // 0xb10fb4: StoreField: r1->field_33 = r2
    //     0xb10fb4: stur            w2, [x1, #0x33]
    // 0xb10fb8: ldur            d0, [fp, #-0x78]
    // 0xb10fbc: StoreField: r1->field_37 = d0
    //     0xb10fbc: stur            d0, [x1, #0x37]
    // 0xb10fc0: ldur            x0, [fp, #-0x18]
    // 0xb10fc4: StoreField: r1->field_3f = r0
    //     0xb10fc4: stur            x0, [x1, #0x3f]
    // 0xb10fc8: r0 = Instance_EdgeInsets
    //     0xb10fc8: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb10fcc: StoreField: r1->field_47 = r0
    //     0xb10fcc: stur            w0, [x1, #0x47]
    // 0xb10fd0: d0 = 18.000000
    //     0xb10fd0: fmov            d0, #18.00000000
    // 0xb10fd4: StoreField: r1->field_4b = d0
    //     0xb10fd4: stur            d0, [x1, #0x4b]
    // 0xb10fd8: StoreField: r1->field_53 = rZR
    //     0xb10fd8: stur            xzr, [x1, #0x53]
    // 0xb10fdc: r0 = false
    //     0xb10fdc: add             x0, NULL, #0x30  ; false
    // 0xb10fe0: StoreField: r1->field_5b = r0
    //     0xb10fe0: stur            w0, [x1, #0x5b]
    // 0xb10fe4: StoreField: r1->field_5f = r0
    //     0xb10fe4: stur            w0, [x1, #0x5f]
    // 0xb10fe8: ldur            x0, [fp, #-0x38]
    // 0xb10fec: StoreField: r1->field_67 = r0
    //     0xb10fec: stur            w0, [x1, #0x67]
    // 0xb10ff0: ldur            x0, [fp, #-0x10]
    // 0xb10ff4: r4 = LoadClassIdInstr(r0)
    //     0xb10ff4: ldur            x4, [x0, #-1]
    //     0xb10ff8: ubfx            x4, x4, #0xc, #0x14
    // 0xb10ffc: ldur            x16, [fp, #-0x20]
    // 0xb11000: stp             x16, x0, [SP]
    // 0xb11004: mov             x0, x4
    // 0xb11008: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb11008: sub             lr, x0, #0xb7
    //     0xb1100c: ldr             lr, [x21, lr, lsl #3]
    //     0xb11010: blr             lr
    // 0xb11014: cmp             w0, NULL
    // 0xb11018: b.ne            #0xb11024
    // 0xb1101c: r0 = Null
    //     0xb1101c: mov             x0, NULL
    // 0xb11020: b               #0xb11030
    // 0xb11024: LoadField: r1 = r0->field_c3
    //     0xb11024: ldur            w1, [x0, #0xc3]
    // 0xb11028: DecompressPointer r1
    //     0xb11028: add             x1, x1, HEAP, lsl #32
    // 0xb1102c: mov             x0, x1
    // 0xb11030: cmp             w0, NULL
    // 0xb11034: b.ne            #0xb11040
    // 0xb11038: r4 = ""
    //     0xb11038: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1103c: b               #0xb11044
    // 0xb11040: mov             x4, x0
    // 0xb11044: ldur            x3, [fp, #-8]
    // 0xb11048: ldur            x2, [fp, #-0x50]
    // 0xb1104c: ldur            x0, [fp, #-0x60]
    // 0xb11050: stur            x4, [fp, #-0x10]
    // 0xb11054: LoadField: r1 = r3->field_f
    //     0xb11054: ldur            w1, [x3, #0xf]
    // 0xb11058: DecompressPointer r1
    //     0xb11058: add             x1, x1, HEAP, lsl #32
    // 0xb1105c: cmp             w1, NULL
    // 0xb11060: b.eq            #0xb1173c
    // 0xb11064: r0 = of()
    //     0xb11064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb11068: LoadField: r1 = r0->field_87
    //     0xb11068: ldur            w1, [x0, #0x87]
    // 0xb1106c: DecompressPointer r1
    //     0xb1106c: add             x1, x1, HEAP, lsl #32
    // 0xb11070: LoadField: r0 = r1->field_2b
    //     0xb11070: ldur            w0, [x1, #0x2b]
    // 0xb11074: DecompressPointer r0
    //     0xb11074: add             x0, x0, HEAP, lsl #32
    // 0xb11078: stur            x0, [fp, #-0x20]
    // 0xb1107c: r1 = Instance_Color
    //     0xb1107c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb11080: d0 = 0.700000
    //     0xb11080: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb11084: ldr             d0, [x17, #0xf48]
    // 0xb11088: r0 = withOpacity()
    //     0xb11088: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb1108c: r16 = 14.000000
    //     0xb1108c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb11090: ldr             x16, [x16, #0x1d8]
    // 0xb11094: stp             x0, x16, [SP]
    // 0xb11098: ldur            x1, [fp, #-0x20]
    // 0xb1109c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb1109c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb110a0: ldr             x4, [x4, #0xaa0]
    // 0xb110a4: r0 = copyWith()
    //     0xb110a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb110a8: stur            x0, [fp, #-0x20]
    // 0xb110ac: r0 = Text()
    //     0xb110ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb110b0: mov             x3, x0
    // 0xb110b4: ldur            x0, [fp, #-0x10]
    // 0xb110b8: stur            x3, [fp, #-0x38]
    // 0xb110bc: StoreField: r3->field_b = r0
    //     0xb110bc: stur            w0, [x3, #0xb]
    // 0xb110c0: ldur            x0, [fp, #-0x20]
    // 0xb110c4: StoreField: r3->field_13 = r0
    //     0xb110c4: stur            w0, [x3, #0x13]
    // 0xb110c8: r1 = Null
    //     0xb110c8: mov             x1, NULL
    // 0xb110cc: r2 = 6
    //     0xb110cc: movz            x2, #0x6
    // 0xb110d0: r0 = AllocateArray()
    //     0xb110d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb110d4: mov             x2, x0
    // 0xb110d8: ldur            x0, [fp, #-0x60]
    // 0xb110dc: stur            x2, [fp, #-0x10]
    // 0xb110e0: StoreField: r2->field_f = r0
    //     0xb110e0: stur            w0, [x2, #0xf]
    // 0xb110e4: r16 = Instance_SizedBox
    //     0xb110e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xb110e8: ldr             x16, [x16, #0x940]
    // 0xb110ec: StoreField: r2->field_13 = r16
    //     0xb110ec: stur            w16, [x2, #0x13]
    // 0xb110f0: ldur            x0, [fp, #-0x38]
    // 0xb110f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb110f4: stur            w0, [x2, #0x17]
    // 0xb110f8: r1 = <Widget>
    //     0xb110f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb110fc: r0 = AllocateGrowableArray()
    //     0xb110fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb11100: mov             x1, x0
    // 0xb11104: ldur            x0, [fp, #-0x10]
    // 0xb11108: stur            x1, [fp, #-0x20]
    // 0xb1110c: StoreField: r1->field_f = r0
    //     0xb1110c: stur            w0, [x1, #0xf]
    // 0xb11110: r0 = 6
    //     0xb11110: movz            x0, #0x6
    // 0xb11114: StoreField: r1->field_b = r0
    //     0xb11114: stur            w0, [x1, #0xb]
    // 0xb11118: r0 = Row()
    //     0xb11118: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb1111c: mov             x1, x0
    // 0xb11120: r0 = Instance_Axis
    //     0xb11120: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb11124: stur            x1, [fp, #-0x10]
    // 0xb11128: StoreField: r1->field_f = r0
    //     0xb11128: stur            w0, [x1, #0xf]
    // 0xb1112c: r0 = Instance_MainAxisAlignment
    //     0xb1112c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb11130: ldr             x0, [x0, #0xa08]
    // 0xb11134: StoreField: r1->field_13 = r0
    //     0xb11134: stur            w0, [x1, #0x13]
    // 0xb11138: r2 = Instance_MainAxisSize
    //     0xb11138: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb1113c: ldr             x2, [x2, #0xa10]
    // 0xb11140: ArrayStore: r1[0] = r2  ; List_4
    //     0xb11140: stur            w2, [x1, #0x17]
    // 0xb11144: r3 = Instance_CrossAxisAlignment
    //     0xb11144: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb11148: ldr             x3, [x3, #0xa18]
    // 0xb1114c: StoreField: r1->field_1b = r3
    //     0xb1114c: stur            w3, [x1, #0x1b]
    // 0xb11150: r3 = Instance_VerticalDirection
    //     0xb11150: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb11154: ldr             x3, [x3, #0xa20]
    // 0xb11158: StoreField: r1->field_23 = r3
    //     0xb11158: stur            w3, [x1, #0x23]
    // 0xb1115c: r4 = Instance_Clip
    //     0xb1115c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb11160: ldr             x4, [x4, #0x38]
    // 0xb11164: StoreField: r1->field_2b = r4
    //     0xb11164: stur            w4, [x1, #0x2b]
    // 0xb11168: StoreField: r1->field_2f = rZR
    //     0xb11168: stur            xzr, [x1, #0x2f]
    // 0xb1116c: ldur            x5, [fp, #-0x20]
    // 0xb11170: StoreField: r1->field_b = r5
    //     0xb11170: stur            w5, [x1, #0xb]
    // 0xb11174: r0 = Padding()
    //     0xb11174: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb11178: mov             x2, x0
    // 0xb1117c: r0 = Instance_EdgeInsets
    //     0xb1117c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb11180: ldr             x0, [x0, #0x668]
    // 0xb11184: stur            x2, [fp, #-0x20]
    // 0xb11188: StoreField: r2->field_f = r0
    //     0xb11188: stur            w0, [x2, #0xf]
    // 0xb1118c: ldur            x0, [fp, #-0x10]
    // 0xb11190: StoreField: r2->field_b = r0
    //     0xb11190: stur            w0, [x2, #0xb]
    // 0xb11194: ldur            x0, [fp, #-0x50]
    // 0xb11198: LoadField: r1 = r0->field_b
    //     0xb11198: ldur            w1, [x0, #0xb]
    // 0xb1119c: LoadField: r3 = r0->field_f
    //     0xb1119c: ldur            w3, [x0, #0xf]
    // 0xb111a0: DecompressPointer r3
    //     0xb111a0: add             x3, x3, HEAP, lsl #32
    // 0xb111a4: LoadField: r4 = r3->field_b
    //     0xb111a4: ldur            w4, [x3, #0xb]
    // 0xb111a8: r3 = LoadInt32Instr(r1)
    //     0xb111a8: sbfx            x3, x1, #1, #0x1f
    // 0xb111ac: stur            x3, [fp, #-0x18]
    // 0xb111b0: r1 = LoadInt32Instr(r4)
    //     0xb111b0: sbfx            x1, x4, #1, #0x1f
    // 0xb111b4: cmp             x3, x1
    // 0xb111b8: b.ne            #0xb111c4
    // 0xb111bc: mov             x1, x0
    // 0xb111c0: r0 = _growToNextCapacity()
    //     0xb111c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb111c4: ldur            x4, [fp, #-0x40]
    // 0xb111c8: ldur            x2, [fp, #-0x50]
    // 0xb111cc: ldur            x3, [fp, #-0x18]
    // 0xb111d0: add             x0, x3, #1
    // 0xb111d4: lsl             x1, x0, #1
    // 0xb111d8: StoreField: r2->field_b = r1
    //     0xb111d8: stur            w1, [x2, #0xb]
    // 0xb111dc: LoadField: r1 = r2->field_f
    //     0xb111dc: ldur            w1, [x2, #0xf]
    // 0xb111e0: DecompressPointer r1
    //     0xb111e0: add             x1, x1, HEAP, lsl #32
    // 0xb111e4: ldur            x0, [fp, #-0x20]
    // 0xb111e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb111e8: add             x25, x1, x3, lsl #2
    //     0xb111ec: add             x25, x25, #0xf
    //     0xb111f0: str             w0, [x25]
    //     0xb111f4: tbz             w0, #0, #0xb11210
    //     0xb111f8: ldurb           w16, [x1, #-1]
    //     0xb111fc: ldurb           w17, [x0, #-1]
    //     0xb11200: and             x16, x17, x16, lsr #2
    //     0xb11204: tst             x16, HEAP, lsr #32
    //     0xb11208: b.eq            #0xb11210
    //     0xb1120c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb11210: cmp             w4, NULL
    // 0xb11214: b.ne            #0xb11220
    // 0xb11218: r0 = Null
    //     0xb11218: mov             x0, NULL
    // 0xb1121c: b               #0xb1123c
    // 0xb11220: LoadField: r1 = r4->field_bf
    //     0xb11220: ldur            w1, [x4, #0xbf]
    // 0xb11224: DecompressPointer r1
    //     0xb11224: add             x1, x1, HEAP, lsl #32
    // 0xb11228: cmp             w1, NULL
    // 0xb1122c: b.ne            #0xb11238
    // 0xb11230: r0 = Null
    //     0xb11230: mov             x0, NULL
    // 0xb11234: b               #0xb1123c
    // 0xb11238: r0 = trim()
    //     0xb11238: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb1123c: cmp             w0, NULL
    // 0xb11240: b.ne            #0xb11248
    // 0xb11244: r0 = ""
    //     0xb11244: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb11248: ldur            x1, [fp, #-0x30]
    // 0xb1124c: stur            x0, [fp, #-0x10]
    // 0xb11250: r0 = value()
    //     0xb11250: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb11254: tbnz            w0, #4, #0xb11260
    // 0xb11258: r0 = Null
    //     0xb11258: mov             x0, NULL
    // 0xb1125c: b               #0xb11264
    // 0xb11260: r0 = 4
    //     0xb11260: movz            x0, #0x4
    // 0xb11264: ldur            x1, [fp, #-0x30]
    // 0xb11268: stur            x0, [fp, #-0x20]
    // 0xb1126c: r0 = value()
    //     0xb1126c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb11270: tbnz            w0, #4, #0xb11280
    // 0xb11274: r5 = Instance_TextOverflow
    //     0xb11274: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xb11278: ldr             x5, [x5, #0x3a8]
    // 0xb1127c: b               #0xb11288
    // 0xb11280: r5 = Instance_TextOverflow
    //     0xb11280: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb11284: ldr             x5, [x5, #0xe10]
    // 0xb11288: ldur            x4, [fp, #-8]
    // 0xb1128c: ldur            x3, [fp, #-0x40]
    // 0xb11290: ldur            x2, [fp, #-0x10]
    // 0xb11294: ldur            x0, [fp, #-0x20]
    // 0xb11298: stur            x5, [fp, #-0x38]
    // 0xb1129c: LoadField: r1 = r4->field_f
    //     0xb1129c: ldur            w1, [x4, #0xf]
    // 0xb112a0: DecompressPointer r1
    //     0xb112a0: add             x1, x1, HEAP, lsl #32
    // 0xb112a4: cmp             w1, NULL
    // 0xb112a8: b.eq            #0xb11740
    // 0xb112ac: r0 = of()
    //     0xb112ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb112b0: LoadField: r1 = r0->field_87
    //     0xb112b0: ldur            w1, [x0, #0x87]
    // 0xb112b4: DecompressPointer r1
    //     0xb112b4: add             x1, x1, HEAP, lsl #32
    // 0xb112b8: LoadField: r0 = r1->field_2b
    //     0xb112b8: ldur            w0, [x1, #0x2b]
    // 0xb112bc: DecompressPointer r0
    //     0xb112bc: add             x0, x0, HEAP, lsl #32
    // 0xb112c0: LoadField: r1 = r0->field_13
    //     0xb112c0: ldur            w1, [x0, #0x13]
    // 0xb112c4: DecompressPointer r1
    //     0xb112c4: add             x1, x1, HEAP, lsl #32
    // 0xb112c8: stur            x1, [fp, #-0x58]
    // 0xb112cc: r0 = TextStyle()
    //     0xb112cc: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xb112d0: mov             x1, x0
    // 0xb112d4: r0 = true
    //     0xb112d4: add             x0, NULL, #0x20  ; true
    // 0xb112d8: stur            x1, [fp, #-0x60]
    // 0xb112dc: StoreField: r1->field_7 = r0
    //     0xb112dc: stur            w0, [x1, #7]
    // 0xb112e0: r2 = Instance_Color
    //     0xb112e0: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb112e4: StoreField: r1->field_b = r2
    //     0xb112e4: stur            w2, [x1, #0xb]
    // 0xb112e8: r2 = 12.000000
    //     0xb112e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb112ec: ldr             x2, [x2, #0x9e8]
    // 0xb112f0: StoreField: r1->field_1f = r2
    //     0xb112f0: stur            w2, [x1, #0x1f]
    // 0xb112f4: ldur            x2, [fp, #-0x58]
    // 0xb112f8: StoreField: r1->field_13 = r2
    //     0xb112f8: stur            w2, [x1, #0x13]
    // 0xb112fc: r0 = Text()
    //     0xb112fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb11300: mov             x3, x0
    // 0xb11304: ldur            x0, [fp, #-0x10]
    // 0xb11308: stur            x3, [fp, #-0x58]
    // 0xb1130c: StoreField: r3->field_b = r0
    //     0xb1130c: stur            w0, [x3, #0xb]
    // 0xb11310: ldur            x0, [fp, #-0x60]
    // 0xb11314: StoreField: r3->field_13 = r0
    //     0xb11314: stur            w0, [x3, #0x13]
    // 0xb11318: r0 = Instance_TextAlign
    //     0xb11318: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb1131c: StoreField: r3->field_1b = r0
    //     0xb1131c: stur            w0, [x3, #0x1b]
    // 0xb11320: ldur            x0, [fp, #-0x38]
    // 0xb11324: StoreField: r3->field_2b = r0
    //     0xb11324: stur            w0, [x3, #0x2b]
    // 0xb11328: ldur            x0, [fp, #-0x20]
    // 0xb1132c: StoreField: r3->field_37 = r0
    //     0xb1132c: stur            w0, [x3, #0x37]
    // 0xb11330: r1 = Null
    //     0xb11330: mov             x1, NULL
    // 0xb11334: r2 = 2
    //     0xb11334: movz            x2, #0x2
    // 0xb11338: r0 = AllocateArray()
    //     0xb11338: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb1133c: mov             x2, x0
    // 0xb11340: ldur            x0, [fp, #-0x58]
    // 0xb11344: stur            x2, [fp, #-0x10]
    // 0xb11348: StoreField: r2->field_f = r0
    //     0xb11348: stur            w0, [x2, #0xf]
    // 0xb1134c: r1 = <Widget>
    //     0xb1134c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb11350: r0 = AllocateGrowableArray()
    //     0xb11350: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb11354: mov             x2, x0
    // 0xb11358: ldur            x0, [fp, #-0x10]
    // 0xb1135c: stur            x2, [fp, #-0x20]
    // 0xb11360: StoreField: r2->field_f = r0
    //     0xb11360: stur            w0, [x2, #0xf]
    // 0xb11364: r0 = 2
    //     0xb11364: movz            x0, #0x2
    // 0xb11368: StoreField: r2->field_b = r0
    //     0xb11368: stur            w0, [x2, #0xb]
    // 0xb1136c: ldur            x0, [fp, #-0x40]
    // 0xb11370: cmp             w0, NULL
    // 0xb11374: b.ne            #0xb11380
    // 0xb11378: r0 = Null
    //     0xb11378: mov             x0, NULL
    // 0xb1137c: b               #0xb1139c
    // 0xb11380: LoadField: r1 = r0->field_bf
    //     0xb11380: ldur            w1, [x0, #0xbf]
    // 0xb11384: DecompressPointer r1
    //     0xb11384: add             x1, x1, HEAP, lsl #32
    // 0xb11388: cmp             w1, NULL
    // 0xb1138c: b.ne            #0xb11398
    // 0xb11390: r0 = Null
    //     0xb11390: mov             x0, NULL
    // 0xb11394: b               #0xb1139c
    // 0xb11398: r0 = trim()
    //     0xb11398: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb1139c: cmp             w0, NULL
    // 0xb113a0: b.ne            #0xb113ac
    // 0xb113a4: r1 = ""
    //     0xb113a4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb113a8: b               #0xb113b0
    // 0xb113ac: mov             x1, x0
    // 0xb113b0: ldur            x0, [fp, #-8]
    // 0xb113b4: LoadField: r2 = r0->field_f
    //     0xb113b4: ldur            w2, [x0, #0xf]
    // 0xb113b8: DecompressPointer r2
    //     0xb113b8: add             x2, x2, HEAP, lsl #32
    // 0xb113bc: cmp             w2, NULL
    // 0xb113c0: b.eq            #0xb11744
    // 0xb113c4: r0 = TextExceeds.textExceedsLines()
    //     0xb113c4: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xb113c8: tbnz            w0, #4, #0xb11558
    // 0xb113cc: ldur            x1, [fp, #-0x30]
    // 0xb113d0: r0 = value()
    //     0xb113d0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb113d4: tbnz            w0, #4, #0xb113e4
    // 0xb113d8: r3 = "Know Less"
    //     0xb113d8: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xb113dc: ldr             x3, [x3, #0x1d0]
    // 0xb113e0: b               #0xb113ec
    // 0xb113e4: r3 = "Know more"
    //     0xb113e4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xb113e8: ldr             x3, [x3, #0x20]
    // 0xb113ec: ldur            x0, [fp, #-8]
    // 0xb113f0: ldur            x2, [fp, #-0x20]
    // 0xb113f4: stur            x3, [fp, #-0x10]
    // 0xb113f8: LoadField: r1 = r0->field_f
    //     0xb113f8: ldur            w1, [x0, #0xf]
    // 0xb113fc: DecompressPointer r1
    //     0xb113fc: add             x1, x1, HEAP, lsl #32
    // 0xb11400: cmp             w1, NULL
    // 0xb11404: b.eq            #0xb11748
    // 0xb11408: r0 = of()
    //     0xb11408: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1140c: LoadField: r1 = r0->field_87
    //     0xb1140c: ldur            w1, [x0, #0x87]
    // 0xb11410: DecompressPointer r1
    //     0xb11410: add             x1, x1, HEAP, lsl #32
    // 0xb11414: LoadField: r0 = r1->field_7
    //     0xb11414: ldur            w0, [x1, #7]
    // 0xb11418: DecompressPointer r0
    //     0xb11418: add             x0, x0, HEAP, lsl #32
    // 0xb1141c: ldur            x1, [fp, #-8]
    // 0xb11420: stur            x0, [fp, #-0x30]
    // 0xb11424: LoadField: r2 = r1->field_f
    //     0xb11424: ldur            w2, [x1, #0xf]
    // 0xb11428: DecompressPointer r2
    //     0xb11428: add             x2, x2, HEAP, lsl #32
    // 0xb1142c: cmp             w2, NULL
    // 0xb11430: b.eq            #0xb1174c
    // 0xb11434: mov             x1, x2
    // 0xb11438: r0 = of()
    //     0xb11438: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1143c: LoadField: r1 = r0->field_5b
    //     0xb1143c: ldur            w1, [x0, #0x5b]
    // 0xb11440: DecompressPointer r1
    //     0xb11440: add             x1, x1, HEAP, lsl #32
    // 0xb11444: r16 = 12.000000
    //     0xb11444: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb11448: ldr             x16, [x16, #0x9e8]
    // 0xb1144c: stp             x1, x16, [SP, #8]
    // 0xb11450: r16 = Instance_TextDecoration
    //     0xb11450: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb11454: ldr             x16, [x16, #0x10]
    // 0xb11458: str             x16, [SP]
    // 0xb1145c: ldur            x1, [fp, #-0x30]
    // 0xb11460: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xb11460: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xb11464: ldr             x4, [x4, #0xe38]
    // 0xb11468: r0 = copyWith()
    //     0xb11468: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1146c: stur            x0, [fp, #-8]
    // 0xb11470: r0 = Text()
    //     0xb11470: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb11474: mov             x1, x0
    // 0xb11478: ldur            x0, [fp, #-0x10]
    // 0xb1147c: stur            x1, [fp, #-0x30]
    // 0xb11480: StoreField: r1->field_b = r0
    //     0xb11480: stur            w0, [x1, #0xb]
    // 0xb11484: ldur            x0, [fp, #-8]
    // 0xb11488: StoreField: r1->field_13 = r0
    //     0xb11488: stur            w0, [x1, #0x13]
    // 0xb1148c: r0 = Padding()
    //     0xb1148c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb11490: mov             x1, x0
    // 0xb11494: r0 = Instance_EdgeInsets
    //     0xb11494: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb11498: ldr             x0, [x0, #0x668]
    // 0xb1149c: stur            x1, [fp, #-8]
    // 0xb114a0: StoreField: r1->field_f = r0
    //     0xb114a0: stur            w0, [x1, #0xf]
    // 0xb114a4: ldur            x0, [fp, #-0x30]
    // 0xb114a8: StoreField: r1->field_b = r0
    //     0xb114a8: stur            w0, [x1, #0xb]
    // 0xb114ac: r0 = GestureDetector()
    //     0xb114ac: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb114b0: ldur            x2, [fp, #-0x28]
    // 0xb114b4: r1 = Function '<anonymous closure>':.
    //     0xb114b4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b70] AnonymousClosure: (0xb11750), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xb10880)
    //     0xb114b8: ldr             x1, [x1, #0xb70]
    // 0xb114bc: stur            x0, [fp, #-0x10]
    // 0xb114c0: r0 = AllocateClosure()
    //     0xb114c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb114c4: ldur            x16, [fp, #-8]
    // 0xb114c8: stp             x16, x0, [SP]
    // 0xb114cc: ldur            x1, [fp, #-0x10]
    // 0xb114d0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb114d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb114d4: ldr             x4, [x4, #0xaf0]
    // 0xb114d8: r0 = GestureDetector()
    //     0xb114d8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb114dc: ldur            x0, [fp, #-0x20]
    // 0xb114e0: LoadField: r1 = r0->field_b
    //     0xb114e0: ldur            w1, [x0, #0xb]
    // 0xb114e4: LoadField: r2 = r0->field_f
    //     0xb114e4: ldur            w2, [x0, #0xf]
    // 0xb114e8: DecompressPointer r2
    //     0xb114e8: add             x2, x2, HEAP, lsl #32
    // 0xb114ec: LoadField: r3 = r2->field_b
    //     0xb114ec: ldur            w3, [x2, #0xb]
    // 0xb114f0: r2 = LoadInt32Instr(r1)
    //     0xb114f0: sbfx            x2, x1, #1, #0x1f
    // 0xb114f4: stur            x2, [fp, #-0x18]
    // 0xb114f8: r1 = LoadInt32Instr(r3)
    //     0xb114f8: sbfx            x1, x3, #1, #0x1f
    // 0xb114fc: cmp             x2, x1
    // 0xb11500: b.ne            #0xb1150c
    // 0xb11504: mov             x1, x0
    // 0xb11508: r0 = _growToNextCapacity()
    //     0xb11508: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1150c: ldur            x2, [fp, #-0x20]
    // 0xb11510: ldur            x3, [fp, #-0x18]
    // 0xb11514: add             x0, x3, #1
    // 0xb11518: lsl             x1, x0, #1
    // 0xb1151c: StoreField: r2->field_b = r1
    //     0xb1151c: stur            w1, [x2, #0xb]
    // 0xb11520: LoadField: r1 = r2->field_f
    //     0xb11520: ldur            w1, [x2, #0xf]
    // 0xb11524: DecompressPointer r1
    //     0xb11524: add             x1, x1, HEAP, lsl #32
    // 0xb11528: ldur            x0, [fp, #-0x10]
    // 0xb1152c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb1152c: add             x25, x1, x3, lsl #2
    //     0xb11530: add             x25, x25, #0xf
    //     0xb11534: str             w0, [x25]
    //     0xb11538: tbz             w0, #0, #0xb11554
    //     0xb1153c: ldurb           w16, [x1, #-1]
    //     0xb11540: ldurb           w17, [x0, #-1]
    //     0xb11544: and             x16, x17, x16, lsr #2
    //     0xb11548: tst             x16, HEAP, lsr #32
    //     0xb1154c: b.eq            #0xb11554
    //     0xb11550: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb11554: b               #0xb1155c
    // 0xb11558: ldur            x2, [fp, #-0x20]
    // 0xb1155c: ldur            x1, [fp, #-0x50]
    // 0xb11560: r0 = Column()
    //     0xb11560: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb11564: mov             x1, x0
    // 0xb11568: r0 = Instance_Axis
    //     0xb11568: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1156c: stur            x1, [fp, #-8]
    // 0xb11570: StoreField: r1->field_f = r0
    //     0xb11570: stur            w0, [x1, #0xf]
    // 0xb11574: r0 = Instance_MainAxisAlignment
    //     0xb11574: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb11578: ldr             x0, [x0, #0xa08]
    // 0xb1157c: StoreField: r1->field_13 = r0
    //     0xb1157c: stur            w0, [x1, #0x13]
    // 0xb11580: r0 = Instance_MainAxisSize
    //     0xb11580: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb11584: ldr             x0, [x0, #0xa10]
    // 0xb11588: ArrayStore: r1[0] = r0  ; List_4
    //     0xb11588: stur            w0, [x1, #0x17]
    // 0xb1158c: r0 = Instance_CrossAxisAlignment
    //     0xb1158c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb11590: ldr             x0, [x0, #0x890]
    // 0xb11594: StoreField: r1->field_1b = r0
    //     0xb11594: stur            w0, [x1, #0x1b]
    // 0xb11598: r0 = Instance_VerticalDirection
    //     0xb11598: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1159c: ldr             x0, [x0, #0xa20]
    // 0xb115a0: StoreField: r1->field_23 = r0
    //     0xb115a0: stur            w0, [x1, #0x23]
    // 0xb115a4: r0 = Instance_Clip
    //     0xb115a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb115a8: ldr             x0, [x0, #0x38]
    // 0xb115ac: StoreField: r1->field_2b = r0
    //     0xb115ac: stur            w0, [x1, #0x2b]
    // 0xb115b0: StoreField: r1->field_2f = rZR
    //     0xb115b0: stur            xzr, [x1, #0x2f]
    // 0xb115b4: ldur            x0, [fp, #-0x20]
    // 0xb115b8: StoreField: r1->field_b = r0
    //     0xb115b8: stur            w0, [x1, #0xb]
    // 0xb115bc: r0 = Padding()
    //     0xb115bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb115c0: mov             x2, x0
    // 0xb115c4: r0 = Instance_EdgeInsets
    //     0xb115c4: add             x0, PP, #0x57, lsl #12  ; [pp+0x57b78] Obj!EdgeInsets@d58f71
    //     0xb115c8: ldr             x0, [x0, #0xb78]
    // 0xb115cc: stur            x2, [fp, #-0x10]
    // 0xb115d0: StoreField: r2->field_f = r0
    //     0xb115d0: stur            w0, [x2, #0xf]
    // 0xb115d4: ldur            x0, [fp, #-8]
    // 0xb115d8: StoreField: r2->field_b = r0
    //     0xb115d8: stur            w0, [x2, #0xb]
    // 0xb115dc: ldur            x0, [fp, #-0x50]
    // 0xb115e0: LoadField: r1 = r0->field_b
    //     0xb115e0: ldur            w1, [x0, #0xb]
    // 0xb115e4: LoadField: r3 = r0->field_f
    //     0xb115e4: ldur            w3, [x0, #0xf]
    // 0xb115e8: DecompressPointer r3
    //     0xb115e8: add             x3, x3, HEAP, lsl #32
    // 0xb115ec: LoadField: r4 = r3->field_b
    //     0xb115ec: ldur            w4, [x3, #0xb]
    // 0xb115f0: r3 = LoadInt32Instr(r1)
    //     0xb115f0: sbfx            x3, x1, #1, #0x1f
    // 0xb115f4: stur            x3, [fp, #-0x18]
    // 0xb115f8: r1 = LoadInt32Instr(r4)
    //     0xb115f8: sbfx            x1, x4, #1, #0x1f
    // 0xb115fc: cmp             x3, x1
    // 0xb11600: b.ne            #0xb1160c
    // 0xb11604: mov             x1, x0
    // 0xb11608: r0 = _growToNextCapacity()
    //     0xb11608: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1160c: ldur            x4, [fp, #-0x48]
    // 0xb11610: ldur            x2, [fp, #-0x50]
    // 0xb11614: ldur            x3, [fp, #-0x18]
    // 0xb11618: add             x0, x3, #1
    // 0xb1161c: lsl             x1, x0, #1
    // 0xb11620: StoreField: r2->field_b = r1
    //     0xb11620: stur            w1, [x2, #0xb]
    // 0xb11624: LoadField: r1 = r2->field_f
    //     0xb11624: ldur            w1, [x2, #0xf]
    // 0xb11628: DecompressPointer r1
    //     0xb11628: add             x1, x1, HEAP, lsl #32
    // 0xb1162c: ldur            x0, [fp, #-0x10]
    // 0xb11630: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb11630: add             x25, x1, x3, lsl #2
    //     0xb11634: add             x25, x25, #0xf
    //     0xb11638: str             w0, [x25]
    //     0xb1163c: tbz             w0, #0, #0xb11658
    //     0xb11640: ldurb           w16, [x1, #-1]
    //     0xb11644: ldurb           w17, [x0, #-1]
    //     0xb11648: and             x16, x17, x16, lsr #2
    //     0xb1164c: tst             x16, HEAP, lsr #32
    //     0xb11650: b.eq            #0xb11658
    //     0xb11654: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb11658: r0 = ListView()
    //     0xb11658: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1165c: stur            x0, [fp, #-8]
    // 0xb11660: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb11660: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb11664: ldr             x16, [x16, #0x1c8]
    // 0xb11668: r30 = true
    //     0xb11668: add             lr, NULL, #0x20  ; true
    // 0xb1166c: stp             lr, x16, [SP, #8]
    // 0xb11670: r16 = Instance_EdgeInsets
    //     0xb11670: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb11674: str             x16, [SP]
    // 0xb11678: mov             x1, x0
    // 0xb1167c: ldur            x2, [fp, #-0x50]
    // 0xb11680: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xb11680: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xb11684: ldr             x4, [x4, #0x6b8]
    // 0xb11688: r0 = ListView()
    //     0xb11688: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb1168c: r0 = Card()
    //     0xb1168c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb11690: mov             x1, x0
    // 0xb11694: r0 = 0.000000
    //     0xb11694: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb11698: stur            x1, [fp, #-0x10]
    // 0xb1169c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1169c: stur            w0, [x1, #0x17]
    // 0xb116a0: ldur            x0, [fp, #-0x48]
    // 0xb116a4: StoreField: r1->field_1b = r0
    //     0xb116a4: stur            w0, [x1, #0x1b]
    // 0xb116a8: r0 = true
    //     0xb116a8: add             x0, NULL, #0x20  ; true
    // 0xb116ac: StoreField: r1->field_1f = r0
    //     0xb116ac: stur            w0, [x1, #0x1f]
    // 0xb116b0: ldur            x2, [fp, #-8]
    // 0xb116b4: StoreField: r1->field_2f = r2
    //     0xb116b4: stur            w2, [x1, #0x2f]
    // 0xb116b8: StoreField: r1->field_2b = r0
    //     0xb116b8: stur            w0, [x1, #0x2b]
    // 0xb116bc: r0 = Instance__CardVariant
    //     0xb116bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb116c0: ldr             x0, [x0, #0xa68]
    // 0xb116c4: StoreField: r1->field_33 = r0
    //     0xb116c4: stur            w0, [x1, #0x33]
    // 0xb116c8: r0 = Container()
    //     0xb116c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb116cc: stur            x0, [fp, #-8]
    // 0xb116d0: r16 = Instance_EdgeInsets
    //     0xb116d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb116d4: ldr             x16, [x16, #0x980]
    // 0xb116d8: ldur            lr, [fp, #-0x10]
    // 0xb116dc: stp             lr, x16, [SP]
    // 0xb116e0: mov             x1, x0
    // 0xb116e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb116e4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb116e8: ldr             x4, [x4, #0x30]
    // 0xb116ec: r0 = Container()
    //     0xb116ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb116f0: r0 = AnimatedContainer()
    //     0xb116f0: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb116f4: stur            x0, [fp, #-0x10]
    // 0xb116f8: r16 = Instance_Cubic
    //     0xb116f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb116fc: ldr             x16, [x16, #0xaf8]
    // 0xb11700: str             x16, [SP]
    // 0xb11704: mov             x1, x0
    // 0xb11708: ldur            x2, [fp, #-8]
    // 0xb1170c: r3 = Instance_Duration
    //     0xb1170c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb11710: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb11710: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb11714: ldr             x4, [x4, #0xbc8]
    // 0xb11718: r0 = AnimatedContainer()
    //     0xb11718: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb1171c: ldur            x0, [fp, #-0x10]
    // 0xb11720: LeaveFrame
    //     0xb11720: mov             SP, fp
    //     0xb11724: ldp             fp, lr, [SP], #0x10
    // 0xb11728: ret
    //     0xb11728: ret             
    // 0xb1172c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1172c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11730: b               #0xb108a4
    // 0xb11734: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb11734: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb11738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11738: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1173c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1173c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb11740: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11740: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb11744: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11744: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb11748: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11748: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1174c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1174c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb11750, size: 0x8c
    // 0xb11750: EnterFrame
    //     0xb11750: stp             fp, lr, [SP, #-0x10]!
    //     0xb11754: mov             fp, SP
    // 0xb11758: AllocStack(0x10)
    //     0xb11758: sub             SP, SP, #0x10
    // 0xb1175c: SetupParameters()
    //     0xb1175c: ldr             x0, [fp, #0x10]
    //     0xb11760: ldur            w2, [x0, #0x17]
    //     0xb11764: add             x2, x2, HEAP, lsl #32
    //     0xb11768: stur            x2, [fp, #-0x10]
    // 0xb1176c: CheckStackOverflow
    //     0xb1176c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb11770: cmp             SP, x16
    //     0xb11774: b.ls            #0xb117d4
    // 0xb11778: LoadField: r0 = r2->field_13
    //     0xb11778: ldur            w0, [x2, #0x13]
    // 0xb1177c: DecompressPointer r0
    //     0xb1177c: add             x0, x0, HEAP, lsl #32
    // 0xb11780: mov             x1, x0
    // 0xb11784: stur            x0, [fp, #-8]
    // 0xb11788: r0 = value()
    //     0xb11788: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xb1178c: eor             x2, x0, #0x10
    // 0xb11790: ldur            x1, [fp, #-8]
    // 0xb11794: r0 = value=()
    //     0xb11794: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb11798: ldur            x0, [fp, #-0x10]
    // 0xb1179c: LoadField: r3 = r0->field_f
    //     0xb1179c: ldur            w3, [x0, #0xf]
    // 0xb117a0: DecompressPointer r3
    //     0xb117a0: add             x3, x3, HEAP, lsl #32
    // 0xb117a4: stur            x3, [fp, #-8]
    // 0xb117a8: r1 = Function '<anonymous closure>':.
    //     0xb117a8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b80] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb117ac: ldr             x1, [x1, #0xb80]
    // 0xb117b0: r2 = Null
    //     0xb117b0: mov             x2, NULL
    // 0xb117b4: r0 = AllocateClosure()
    //     0xb117b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb117b8: ldur            x1, [fp, #-8]
    // 0xb117bc: mov             x2, x0
    // 0xb117c0: r0 = setState()
    //     0xb117c0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb117c4: r0 = Null
    //     0xb117c4: mov             x0, NULL
    // 0xb117c8: LeaveFrame
    //     0xb117c8: mov             SP, fp
    //     0xb117cc: ldp             fp, lr, [SP], #0x10
    // 0xb117d0: ret
    //     0xb117d0: ret             
    // 0xb117d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb117d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb117d8: b               #0xb11778
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb117dc, size: 0x84
    // 0xb117dc: EnterFrame
    //     0xb117dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb117e0: mov             fp, SP
    // 0xb117e4: AllocStack(0x10)
    //     0xb117e4: sub             SP, SP, #0x10
    // 0xb117e8: SetupParameters()
    //     0xb117e8: ldr             x0, [fp, #0x18]
    //     0xb117ec: ldur            w1, [x0, #0x17]
    //     0xb117f0: add             x1, x1, HEAP, lsl #32
    //     0xb117f4: stur            x1, [fp, #-8]
    // 0xb117f8: CheckStackOverflow
    //     0xb117f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb117fc: cmp             SP, x16
    //     0xb11800: b.ls            #0xb11858
    // 0xb11804: r1 = 1
    //     0xb11804: movz            x1, #0x1
    // 0xb11808: r0 = AllocateContext()
    //     0xb11808: bl              #0x16f6108  ; AllocateContextStub
    // 0xb1180c: mov             x1, x0
    // 0xb11810: ldur            x0, [fp, #-8]
    // 0xb11814: StoreField: r1->field_b = r0
    //     0xb11814: stur            w0, [x1, #0xb]
    // 0xb11818: ldr             x2, [fp, #0x10]
    // 0xb1181c: StoreField: r1->field_f = r2
    //     0xb1181c: stur            w2, [x1, #0xf]
    // 0xb11820: LoadField: r3 = r0->field_f
    //     0xb11820: ldur            w3, [x0, #0xf]
    // 0xb11824: DecompressPointer r3
    //     0xb11824: add             x3, x3, HEAP, lsl #32
    // 0xb11828: mov             x2, x1
    // 0xb1182c: stur            x3, [fp, #-0x10]
    // 0xb11830: r1 = Function '<anonymous closure>':.
    //     0xb11830: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b88] AnonymousClosure: (0xb11860), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xb0fe14)
    //     0xb11834: ldr             x1, [x1, #0xb88]
    // 0xb11838: r0 = AllocateClosure()
    //     0xb11838: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1183c: ldur            x1, [fp, #-0x10]
    // 0xb11840: mov             x2, x0
    // 0xb11844: r0 = setState()
    //     0xb11844: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb11848: r0 = Null
    //     0xb11848: mov             x0, NULL
    // 0xb1184c: LeaveFrame
    //     0xb1184c: mov             SP, fp
    //     0xb11850: ldp             fp, lr, [SP], #0x10
    // 0xb11854: ret
    //     0xb11854: ret             
    // 0xb11858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1185c: b               #0xb11804
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb11860, size: 0x8c
    // 0xb11860: EnterFrame
    //     0xb11860: stp             fp, lr, [SP, #-0x10]!
    //     0xb11864: mov             fp, SP
    // 0xb11868: AllocStack(0x8)
    //     0xb11868: sub             SP, SP, #8
    // 0xb1186c: SetupParameters()
    //     0xb1186c: ldr             x0, [fp, #0x10]
    //     0xb11870: ldur            w1, [x0, #0x17]
    //     0xb11874: add             x1, x1, HEAP, lsl #32
    // 0xb11878: CheckStackOverflow
    //     0xb11878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1187c: cmp             SP, x16
    //     0xb11880: b.ls            #0xb118e4
    // 0xb11884: LoadField: r0 = r1->field_b
    //     0xb11884: ldur            w0, [x1, #0xb]
    // 0xb11888: DecompressPointer r0
    //     0xb11888: add             x0, x0, HEAP, lsl #32
    // 0xb1188c: LoadField: r2 = r0->field_f
    //     0xb1188c: ldur            w2, [x0, #0xf]
    // 0xb11890: DecompressPointer r2
    //     0xb11890: add             x2, x2, HEAP, lsl #32
    // 0xb11894: LoadField: r0 = r1->field_f
    //     0xb11894: ldur            w0, [x1, #0xf]
    // 0xb11898: DecompressPointer r0
    //     0xb11898: add             x0, x0, HEAP, lsl #32
    // 0xb1189c: r1 = LoadInt32Instr(r0)
    //     0xb1189c: sbfx            x1, x0, #1, #0x1f
    //     0xb118a0: tbz             w0, #0, #0xb118a8
    //     0xb118a4: ldur            x1, [x0, #7]
    // 0xb118a8: ArrayStore: r2[0] = r1  ; List_8
    //     0xb118a8: stur            x1, [x2, #0x17]
    // 0xb118ac: LoadField: r0 = r2->field_1f
    //     0xb118ac: ldur            w0, [x2, #0x1f]
    // 0xb118b0: DecompressPointer r0
    //     0xb118b0: add             x0, x0, HEAP, lsl #32
    // 0xb118b4: stur            x0, [fp, #-8]
    // 0xb118b8: r1 = Function '<anonymous closure>':.
    //     0xb118b8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57b90] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xb118bc: ldr             x1, [x1, #0xb90]
    // 0xb118c0: r2 = Null
    //     0xb118c0: mov             x2, NULL
    // 0xb118c4: r0 = AllocateClosure()
    //     0xb118c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb118c8: ldur            x1, [fp, #-8]
    // 0xb118cc: mov             x2, x0
    // 0xb118d0: r0 = forEach()
    //     0xb118d0: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xb118d4: r0 = Null
    //     0xb118d4: mov             x0, NULL
    // 0xb118d8: LeaveFrame
    //     0xb118d8: mov             SP, fp
    //     0xb118dc: ldp             fp, lr, [SP], #0x10
    // 0xb118e0: ret
    //     0xb118e0: ret             
    // 0xb118e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb118e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb118e8: b               #0xb11884
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb118ec, size: 0x1b8
    // 0xb118ec: EnterFrame
    //     0xb118ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb118f0: mov             fp, SP
    // 0xb118f4: AllocStack(0x48)
    //     0xb118f4: sub             SP, SP, #0x48
    // 0xb118f8: SetupParameters()
    //     0xb118f8: ldr             x0, [fp, #0x10]
    //     0xb118fc: ldur            w1, [x0, #0x17]
    //     0xb11900: add             x1, x1, HEAP, lsl #32
    //     0xb11904: stur            x1, [fp, #-0x28]
    // 0xb11908: CheckStackOverflow
    //     0xb11908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb1190c: cmp             SP, x16
    //     0xb11910: b.ls            #0xb11a94
    // 0xb11914: LoadField: r0 = r1->field_f
    //     0xb11914: ldur            w0, [x1, #0xf]
    // 0xb11918: DecompressPointer r0
    //     0xb11918: add             x0, x0, HEAP, lsl #32
    // 0xb1191c: LoadField: r2 = r0->field_b
    //     0xb1191c: ldur            w2, [x0, #0xb]
    // 0xb11920: DecompressPointer r2
    //     0xb11920: add             x2, x2, HEAP, lsl #32
    // 0xb11924: stur            x2, [fp, #-0x20]
    // 0xb11928: cmp             w2, NULL
    // 0xb1192c: b.eq            #0xb11a9c
    // 0xb11930: LoadField: r0 = r2->field_1b
    //     0xb11930: ldur            w0, [x2, #0x1b]
    // 0xb11934: DecompressPointer r0
    //     0xb11934: add             x0, x0, HEAP, lsl #32
    // 0xb11938: stur            x0, [fp, #-0x18]
    // 0xb1193c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb1193c: ldur            w3, [x2, #0x17]
    // 0xb11940: DecompressPointer r3
    //     0xb11940: add             x3, x3, HEAP, lsl #32
    // 0xb11944: stur            x3, [fp, #-0x10]
    // 0xb11948: LoadField: r4 = r2->field_f
    //     0xb11948: ldur            w4, [x2, #0xf]
    // 0xb1194c: DecompressPointer r4
    //     0xb1194c: add             x4, x4, HEAP, lsl #32
    // 0xb11950: stur            x4, [fp, #-8]
    // 0xb11954: r0 = EventData()
    //     0xb11954: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb11958: mov             x1, x0
    // 0xb1195c: r0 = "product_page"
    //     0xb1195c: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb11960: ldr             x0, [x0, #0x480]
    // 0xb11964: stur            x1, [fp, #-0x30]
    // 0xb11968: StoreField: r1->field_13 = r0
    //     0xb11968: stur            w0, [x1, #0x13]
    // 0xb1196c: ldur            x2, [fp, #-0x10]
    // 0xb11970: StoreField: r1->field_53 = r2
    //     0xb11970: stur            w2, [x1, #0x53]
    // 0xb11974: ldur            x2, [fp, #-0x18]
    // 0xb11978: StoreField: r1->field_57 = r2
    //     0xb11978: stur            w2, [x1, #0x57]
    // 0xb1197c: ldur            x2, [fp, #-8]
    // 0xb11980: StoreField: r1->field_ef = r2
    //     0xb11980: stur            w2, [x1, #0xef]
    // 0xb11984: StoreField: r1->field_f3 = r0
    //     0xb11984: stur            w0, [x1, #0xf3]
    // 0xb11988: r0 = EventsRequest()
    //     0xb11988: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb1198c: mov             x1, x0
    // 0xb11990: r0 = "widget_clicked"
    //     0xb11990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edd8] "widget_clicked"
    //     0xb11994: ldr             x0, [x0, #0xdd8]
    // 0xb11998: StoreField: r1->field_7 = r0
    //     0xb11998: stur            w0, [x1, #7]
    // 0xb1199c: ldur            x0, [fp, #-0x30]
    // 0xb119a0: StoreField: r1->field_b = r0
    //     0xb119a0: stur            w0, [x1, #0xb]
    // 0xb119a4: ldur            x0, [fp, #-0x20]
    // 0xb119a8: LoadField: r2 = r0->field_27
    //     0xb119a8: ldur            w2, [x0, #0x27]
    // 0xb119ac: DecompressPointer r2
    //     0xb119ac: add             x2, x2, HEAP, lsl #32
    // 0xb119b0: stp             x1, x2, [SP]
    // 0xb119b4: r4 = 0
    //     0xb119b4: movz            x4, #0
    // 0xb119b8: ldr             x0, [SP, #8]
    // 0xb119bc: r16 = UnlinkedCall_0x613b5c
    //     0xb119bc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57b98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb119c0: add             x16, x16, #0xb98
    // 0xb119c4: ldp             x5, lr, [x16]
    // 0xb119c8: blr             lr
    // 0xb119cc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb119cc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb119d0: ldr             x0, [x0, #0x1c80]
    //     0xb119d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb119d8: cmp             w0, w16
    //     0xb119dc: b.ne            #0xb119e8
    //     0xb119e0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb119e4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb119e8: r1 = Null
    //     0xb119e8: mov             x1, NULL
    // 0xb119ec: r2 = 12
    //     0xb119ec: movz            x2, #0xc
    // 0xb119f0: r0 = AllocateArray()
    //     0xb119f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb119f4: r16 = "previousScreenSource"
    //     0xb119f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xb119f8: ldr             x16, [x16, #0x448]
    // 0xb119fc: StoreField: r0->field_f = r16
    //     0xb119fc: stur            w16, [x0, #0xf]
    // 0xb11a00: ldur            x1, [fp, #-0x28]
    // 0xb11a04: LoadField: r2 = r1->field_f
    //     0xb11a04: ldur            w2, [x1, #0xf]
    // 0xb11a08: DecompressPointer r2
    //     0xb11a08: add             x2, x2, HEAP, lsl #32
    // 0xb11a0c: LoadField: r1 = r2->field_b
    //     0xb11a0c: ldur            w1, [x2, #0xb]
    // 0xb11a10: DecompressPointer r1
    //     0xb11a10: add             x1, x1, HEAP, lsl #32
    // 0xb11a14: cmp             w1, NULL
    // 0xb11a18: b.eq            #0xb11aa0
    // 0xb11a1c: LoadField: r2 = r1->field_23
    //     0xb11a1c: ldur            w2, [x1, #0x23]
    // 0xb11a20: DecompressPointer r2
    //     0xb11a20: add             x2, x2, HEAP, lsl #32
    // 0xb11a24: StoreField: r0->field_13 = r2
    //     0xb11a24: stur            w2, [x0, #0x13]
    // 0xb11a28: r16 = "screenSource"
    //     0xb11a28: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xb11a2c: ldr             x16, [x16, #0x450]
    // 0xb11a30: ArrayStore: r0[0] = r16  ; List_4
    //     0xb11a30: stur            w16, [x0, #0x17]
    // 0xb11a34: LoadField: r2 = r1->field_1f
    //     0xb11a34: ldur            w2, [x1, #0x1f]
    // 0xb11a38: DecompressPointer r2
    //     0xb11a38: add             x2, x2, HEAP, lsl #32
    // 0xb11a3c: StoreField: r0->field_1b = r2
    //     0xb11a3c: stur            w2, [x0, #0x1b]
    // 0xb11a40: r16 = "widgetType"
    //     0xb11a40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xb11a44: ldr             x16, [x16, #0x338]
    // 0xb11a48: StoreField: r0->field_1f = r16
    //     0xb11a48: stur            w16, [x0, #0x1f]
    // 0xb11a4c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb11a4c: ldur            w2, [x1, #0x17]
    // 0xb11a50: DecompressPointer r2
    //     0xb11a50: add             x2, x2, HEAP, lsl #32
    // 0xb11a54: StoreField: r0->field_23 = r2
    //     0xb11a54: stur            w2, [x0, #0x23]
    // 0xb11a58: r16 = <String, String?>
    //     0xb11a58: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xb11a5c: ldr             x16, [x16, #0x3c8]
    // 0xb11a60: stp             x0, x16, [SP]
    // 0xb11a64: r0 = Map._fromLiteral()
    //     0xb11a64: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb11a68: r16 = "/testimonials"
    //     0xb11a68: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xb11a6c: ldr             x16, [x16, #0x898]
    // 0xb11a70: stp             x16, NULL, [SP, #8]
    // 0xb11a74: str             x0, [SP]
    // 0xb11a78: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xb11a78: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xb11a7c: ldr             x4, [x4, #0x438]
    // 0xb11a80: r0 = GetNavigation.toNamed()
    //     0xb11a80: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xb11a84: r0 = Null
    //     0xb11a84: mov             x0, NULL
    // 0xb11a88: LeaveFrame
    //     0xb11a88: mov             SP, fp
    //     0xb11a8c: ldp             fp, lr, [SP], #0x10
    // 0xb11a90: ret
    //     0xb11a90: ret             
    // 0xb11a94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb11a94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb11a98: b               #0xb11914
    // 0xb11a9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11a9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb11aa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb11aa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87710, size: 0x54
    // 0xc87710: EnterFrame
    //     0xc87710: stp             fp, lr, [SP, #-0x10]!
    //     0xc87714: mov             fp, SP
    // 0xc87718: CheckStackOverflow
    //     0xc87718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8771c: cmp             SP, x16
    //     0xc87720: b.ls            #0xc87750
    // 0xc87724: LoadField: r0 = r1->field_13
    //     0xc87724: ldur            w0, [x1, #0x13]
    // 0xc87728: DecompressPointer r0
    //     0xc87728: add             x0, x0, HEAP, lsl #32
    // 0xc8772c: r16 = Sentinel
    //     0xc8772c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87730: cmp             w0, w16
    // 0xc87734: b.eq            #0xc87758
    // 0xc87738: mov             x1, x0
    // 0xc8773c: r0 = dispose()
    //     0xc8773c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87740: r0 = Null
    //     0xc87740: mov             x0, NULL
    // 0xc87744: LeaveFrame
    //     0xc87744: mov             SP, fp
    //     0xc87748: ldp             fp, lr, [SP], #0x10
    // 0xc8774c: ret
    //     0xc8774c: ret             
    // 0xc87750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87750: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87754: b               #0xc87724
    // 0xc87758: r9 = _pageController
    //     0xc87758: add             x9, PP, #0x57, lsl #12  ; [pp+0x57b30] Field <_ProductTestimonialCarouselState@1502196050._pageController@1502196050>: late (offset: 0x14)
    //     0xc8775c: ldr             x9, [x9, #0xb30]
    // 0xc87760: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87760: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4138, size: 0x2c, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e088, size: 0x84
    // 0xc7e088: EnterFrame
    //     0xc7e088: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e08c: mov             fp, SP
    // 0xc7e090: AllocStack(0x18)
    //     0xc7e090: sub             SP, SP, #0x18
    // 0xc7e094: CheckStackOverflow
    //     0xc7e094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e098: cmp             SP, x16
    //     0xc7e09c: b.ls            #0xc7e104
    // 0xc7e0a0: r1 = <ProductTestimonialCarousel>
    //     0xc7e0a0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b00] TypeArguments: <ProductTestimonialCarousel>
    //     0xc7e0a4: ldr             x1, [x1, #0xb00]
    // 0xc7e0a8: r0 = _ProductTestimonialCarouselState()
    //     0xc7e0a8: bl              #0xc7e10c  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc7e0ac: mov             x1, x0
    // 0xc7e0b0: r0 = Sentinel
    //     0xc7e0b0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7e0b4: stur            x1, [fp, #-8]
    // 0xc7e0b8: StoreField: r1->field_13 = r0
    //     0xc7e0b8: stur            w0, [x1, #0x13]
    // 0xc7e0bc: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7e0bc: stur            xzr, [x1, #0x17]
    // 0xc7e0c0: r16 = <int, RxBool>
    //     0xc7e0c0: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7e0c4: ldr             x16, [x16, #0x298]
    // 0xc7e0c8: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7e0cc: stp             lr, x16, [SP]
    // 0xc7e0d0: r0 = Map._fromLiteral()
    //     0xc7e0d0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7e0d4: ldur            x1, [fp, #-8]
    // 0xc7e0d8: StoreField: r1->field_1f = r0
    //     0xc7e0d8: stur            w0, [x1, #0x1f]
    //     0xc7e0dc: ldurb           w16, [x1, #-1]
    //     0xc7e0e0: ldurb           w17, [x0, #-1]
    //     0xc7e0e4: and             x16, x17, x16, lsr #2
    //     0xc7e0e8: tst             x16, HEAP, lsr #32
    //     0xc7e0ec: b.eq            #0xc7e0f4
    //     0xc7e0f0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7e0f4: mov             x0, x1
    // 0xc7e0f8: LeaveFrame
    //     0xc7e0f8: mov             SP, fp
    //     0xc7e0fc: ldp             fp, lr, [SP], #0x10
    // 0xc7e100: ret
    //     0xc7e100: ret             
    // 0xc7e104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e104: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e108: b               #0xc7e0a0
  }
}
