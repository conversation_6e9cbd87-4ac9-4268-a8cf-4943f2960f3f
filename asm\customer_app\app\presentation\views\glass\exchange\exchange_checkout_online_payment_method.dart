// lib: , url: package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart

// class id: 1049391, size: 0x8
class :: {
}

// class id: 4570, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeCheckoutOnlinePaymentMethod extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x130fd80, size: 0x100
    // 0x130fd80: EnterFrame
    //     0x130fd80: stp             fp, lr, [SP, #-0x10]!
    //     0x130fd84: mov             fp, SP
    // 0x130fd88: AllocStack(0x20)
    //     0x130fd88: sub             SP, SP, #0x20
    // 0x130fd8c: SetupParameters()
    //     0x130fd8c: ldr             x0, [fp, #0x10]
    //     0x130fd90: ldur            w2, [x0, #0x17]
    //     0x130fd94: add             x2, x2, HEAP, lsl #32
    //     0x130fd98: stur            x2, [fp, #-8]
    // 0x130fd9c: CheckStackOverflow
    //     0x130fd9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130fda0: cmp             SP, x16
    //     0x130fda4: b.ls            #0x130fe78
    // 0x130fda8: LoadField: r1 = r2->field_f
    //     0x130fda8: ldur            w1, [x2, #0xf]
    // 0x130fdac: DecompressPointer r1
    //     0x130fdac: add             x1, x1, HEAP, lsl #32
    // 0x130fdb0: r0 = controller()
    //     0x130fdb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130fdb4: mov             x1, x0
    // 0x130fdb8: r2 = "return_exchange_checkout_clicked_online_page"
    //     0x130fdb8: add             x2, PP, #0x39, lsl #12  ; [pp+0x39870] "return_exchange_checkout_clicked_online_page"
    //     0x130fdbc: ldr             x2, [x2, #0x870]
    // 0x130fdc0: r0 = ctaExchangeCheckoutPostEvent()
    //     0x130fdc0: bl              #0x131384c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::ctaExchangeCheckoutPostEvent
    // 0x130fdc4: ldur            x0, [fp, #-8]
    // 0x130fdc8: LoadField: r1 = r0->field_f
    //     0x130fdc8: ldur            w1, [x0, #0xf]
    // 0x130fdcc: DecompressPointer r1
    //     0x130fdcc: add             x1, x1, HEAP, lsl #32
    // 0x130fdd0: r0 = controller()
    //     0x130fdd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130fdd4: LoadField: r1 = r0->field_77
    //     0x130fdd4: ldur            w1, [x0, #0x77]
    // 0x130fdd8: DecompressPointer r1
    //     0x130fdd8: add             x1, x1, HEAP, lsl #32
    // 0x130fddc: r0 = value()
    //     0x130fddc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130fde0: r1 = 60
    //     0x130fde0: movz            x1, #0x3c
    // 0x130fde4: branchIfSmi(r0, 0x130fdf0)
    //     0x130fde4: tbz             w0, #0, #0x130fdf0
    // 0x130fde8: r1 = LoadClassIdInstr(r0)
    //     0x130fde8: ldur            x1, [x0, #-1]
    //     0x130fdec: ubfx            x1, x1, #0xc, #0x14
    // 0x130fdf0: r16 = ""
    //     0x130fdf0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130fdf4: stp             x16, x0, [SP]
    // 0x130fdf8: mov             x0, x1
    // 0x130fdfc: mov             lr, x0
    // 0x130fe00: ldr             lr, [x21, lr, lsl #3]
    // 0x130fe04: blr             lr
    // 0x130fe08: tbz             w0, #4, #0x130fe50
    // 0x130fe0c: ldur            x0, [fp, #-8]
    // 0x130fe10: LoadField: r1 = r0->field_f
    //     0x130fe10: ldur            w1, [x0, #0xf]
    // 0x130fe14: DecompressPointer r1
    //     0x130fe14: add             x1, x1, HEAP, lsl #32
    // 0x130fe18: r0 = controller()
    //     0x130fe18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130fe1c: mov             x2, x0
    // 0x130fe20: ldur            x0, [fp, #-8]
    // 0x130fe24: stur            x2, [fp, #-0x10]
    // 0x130fe28: LoadField: r1 = r0->field_f
    //     0x130fe28: ldur            w1, [x0, #0xf]
    // 0x130fe2c: DecompressPointer r1
    //     0x130fe2c: add             x1, x1, HEAP, lsl #32
    // 0x130fe30: r0 = controller()
    //     0x130fe30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130fe34: LoadField: r1 = r0->field_87
    //     0x130fe34: ldur            w1, [x0, #0x87]
    // 0x130fe38: DecompressPointer r1
    //     0x130fe38: add             x1, x1, HEAP, lsl #32
    // 0x130fe3c: r0 = value()
    //     0x130fe3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130fe40: ldur            x1, [fp, #-0x10]
    // 0x130fe44: mov             x2, x0
    // 0x130fe48: r0 = initPaymentDetails()
    //     0x130fe48: bl              #0x1310458  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::initPaymentDetails
    // 0x130fe4c: b               #0x130fe68
    // 0x130fe50: ldur            x0, [fp, #-8]
    // 0x130fe54: LoadField: r1 = r0->field_f
    //     0x130fe54: ldur            w1, [x0, #0xf]
    // 0x130fe58: DecompressPointer r1
    //     0x130fe58: add             x1, x1, HEAP, lsl #32
    // 0x130fe5c: r2 = "Please select any payment mode!"
    //     0x130fe5c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38198] "Please select any payment mode!"
    //     0x130fe60: ldr             x2, [x2, #0x198]
    // 0x130fe64: r0 = showErrorSnackBar()
    //     0x130fe64: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x130fe68: r0 = Null
    //     0x130fe68: mov             x0, NULL
    // 0x130fe6c: LeaveFrame
    //     0x130fe6c: mov             SP, fp
    //     0x130fe70: ldp             fp, lr, [SP], #0x10
    // 0x130fe74: ret
    //     0x130fe74: ret             
    // 0x130fe78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130fe78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130fe7c: b               #0x130fda8
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x130fe80, size: 0x5d8
    // 0x130fe80: EnterFrame
    //     0x130fe80: stp             fp, lr, [SP, #-0x10]!
    //     0x130fe84: mov             fp, SP
    // 0x130fe88: AllocStack(0x50)
    //     0x130fe88: sub             SP, SP, #0x50
    // 0x130fe8c: SetupParameters()
    //     0x130fe8c: ldr             x0, [fp, #0x10]
    //     0x130fe90: ldur            w2, [x0, #0x17]
    //     0x130fe94: add             x2, x2, HEAP, lsl #32
    //     0x130fe98: stur            x2, [fp, #-8]
    // 0x130fe9c: CheckStackOverflow
    //     0x130fe9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130fea0: cmp             SP, x16
    //     0x130fea4: b.ls            #0x1310440
    // 0x130fea8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130fea8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130feac: ldr             x0, [x0, #0x1c80]
    //     0x130feb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130feb4: cmp             w0, w16
    //     0x130feb8: b.ne            #0x130fec4
    //     0x130febc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130fec0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130fec4: r0 = GetNavigation.size()
    //     0x130fec4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x130fec8: LoadField: d0 = r0->field_f
    //     0x130fec8: ldur            d0, [x0, #0xf]
    // 0x130fecc: d1 = 0.120000
    //     0x130fecc: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x130fed0: fmul            d2, d0, d1
    // 0x130fed4: stur            d2, [fp, #-0x40]
    // 0x130fed8: r1 = _ConstMap len:11
    //     0x130fed8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x130fedc: ldr             x1, [x1, #0xc28]
    // 0x130fee0: r2 = 8
    //     0x130fee0: movz            x2, #0x8
    // 0x130fee4: r0 = []()
    //     0x130fee4: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x130fee8: stur            x0, [fp, #-0x10]
    // 0x130feec: r0 = BoxDecoration()
    //     0x130feec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x130fef0: mov             x2, x0
    // 0x130fef4: r0 = Instance_Color
    //     0x130fef4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130fef8: stur            x2, [fp, #-0x18]
    // 0x130fefc: StoreField: r2->field_7 = r0
    //     0x130fefc: stur            w0, [x2, #7]
    // 0x130ff00: ldur            x0, [fp, #-0x10]
    // 0x130ff04: ArrayStore: r2[0] = r0  ; List_4
    //     0x130ff04: stur            w0, [x2, #0x17]
    // 0x130ff08: r0 = Instance_BoxShape
    //     0x130ff08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x130ff0c: ldr             x0, [x0, #0x80]
    // 0x130ff10: StoreField: r2->field_23 = r0
    //     0x130ff10: stur            w0, [x2, #0x23]
    // 0x130ff14: ldur            x0, [fp, #-8]
    // 0x130ff18: LoadField: r1 = r0->field_f
    //     0x130ff18: ldur            w1, [x0, #0xf]
    // 0x130ff1c: DecompressPointer r1
    //     0x130ff1c: add             x1, x1, HEAP, lsl #32
    // 0x130ff20: r0 = controller()
    //     0x130ff20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ff24: LoadField: r1 = r0->field_53
    //     0x130ff24: ldur            w1, [x0, #0x53]
    // 0x130ff28: DecompressPointer r1
    //     0x130ff28: add             x1, x1, HEAP, lsl #32
    // 0x130ff2c: r0 = value()
    //     0x130ff2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ff30: LoadField: r1 = r0->field_f
    //     0x130ff30: ldur            w1, [x0, #0xf]
    // 0x130ff34: DecompressPointer r1
    //     0x130ff34: add             x1, x1, HEAP, lsl #32
    // 0x130ff38: cmp             w1, NULL
    // 0x130ff3c: b.ne            #0x130ff48
    // 0x130ff40: r0 = ""
    //     0x130ff40: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130ff44: b               #0x130ff4c
    // 0x130ff48: mov             x0, x1
    // 0x130ff4c: ldur            x2, [fp, #-8]
    // 0x130ff50: stur            x0, [fp, #-0x10]
    // 0x130ff54: LoadField: r1 = r2->field_13
    //     0x130ff54: ldur            w1, [x2, #0x13]
    // 0x130ff58: DecompressPointer r1
    //     0x130ff58: add             x1, x1, HEAP, lsl #32
    // 0x130ff5c: r0 = of()
    //     0x130ff5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130ff60: LoadField: r1 = r0->field_87
    //     0x130ff60: ldur            w1, [x0, #0x87]
    // 0x130ff64: DecompressPointer r1
    //     0x130ff64: add             x1, x1, HEAP, lsl #32
    // 0x130ff68: LoadField: r0 = r1->field_2b
    //     0x130ff68: ldur            w0, [x1, #0x2b]
    // 0x130ff6c: DecompressPointer r0
    //     0x130ff6c: add             x0, x0, HEAP, lsl #32
    // 0x130ff70: ldur            x2, [fp, #-8]
    // 0x130ff74: stur            x0, [fp, #-0x20]
    // 0x130ff78: LoadField: r1 = r2->field_13
    //     0x130ff78: ldur            w1, [x2, #0x13]
    // 0x130ff7c: DecompressPointer r1
    //     0x130ff7c: add             x1, x1, HEAP, lsl #32
    // 0x130ff80: r0 = of()
    //     0x130ff80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130ff84: LoadField: r1 = r0->field_5b
    //     0x130ff84: ldur            w1, [x0, #0x5b]
    // 0x130ff88: DecompressPointer r1
    //     0x130ff88: add             x1, x1, HEAP, lsl #32
    // 0x130ff8c: r0 = LoadClassIdInstr(r1)
    //     0x130ff8c: ldur            x0, [x1, #-1]
    //     0x130ff90: ubfx            x0, x0, #0xc, #0x14
    // 0x130ff94: d0 = 0.700000
    //     0x130ff94: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x130ff98: ldr             d0, [x17, #0xf48]
    // 0x130ff9c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x130ff9c: sub             lr, x0, #0xffa
    //     0x130ffa0: ldr             lr, [x21, lr, lsl #3]
    //     0x130ffa4: blr             lr
    // 0x130ffa8: r16 = 12.000000
    //     0x130ffa8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x130ffac: ldr             x16, [x16, #0x9e8]
    // 0x130ffb0: stp             x0, x16, [SP]
    // 0x130ffb4: ldur            x1, [fp, #-0x20]
    // 0x130ffb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130ffb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130ffbc: ldr             x4, [x4, #0xaa0]
    // 0x130ffc0: r0 = copyWith()
    //     0x130ffc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130ffc4: stur            x0, [fp, #-0x20]
    // 0x130ffc8: r0 = Text()
    //     0x130ffc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130ffcc: mov             x2, x0
    // 0x130ffd0: ldur            x0, [fp, #-0x10]
    // 0x130ffd4: stur            x2, [fp, #-0x28]
    // 0x130ffd8: StoreField: r2->field_b = r0
    //     0x130ffd8: stur            w0, [x2, #0xb]
    // 0x130ffdc: ldur            x0, [fp, #-0x20]
    // 0x130ffe0: StoreField: r2->field_13 = r0
    //     0x130ffe0: stur            w0, [x2, #0x13]
    // 0x130ffe4: ldur            x0, [fp, #-8]
    // 0x130ffe8: LoadField: r1 = r0->field_f
    //     0x130ffe8: ldur            w1, [x0, #0xf]
    // 0x130ffec: DecompressPointer r1
    //     0x130ffec: add             x1, x1, HEAP, lsl #32
    // 0x130fff0: r0 = controller()
    //     0x130fff0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130fff4: LoadField: r1 = r0->field_53
    //     0x130fff4: ldur            w1, [x0, #0x53]
    // 0x130fff8: DecompressPointer r1
    //     0x130fff8: add             x1, x1, HEAP, lsl #32
    // 0x130fffc: r0 = value()
    //     0x130fffc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1310000: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1310000: ldur            w1, [x0, #0x17]
    // 0x1310004: DecompressPointer r1
    //     0x1310004: add             x1, x1, HEAP, lsl #32
    // 0x1310008: cmp             w1, NULL
    // 0x131000c: b.ne            #0x1310018
    // 0x1310010: r3 = ""
    //     0x1310010: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1310014: b               #0x131001c
    // 0x1310018: mov             x3, x1
    // 0x131001c: ldur            x2, [fp, #-8]
    // 0x1310020: ldur            d0, [fp, #-0x40]
    // 0x1310024: ldur            x0, [fp, #-0x28]
    // 0x1310028: stur            x3, [fp, #-0x10]
    // 0x131002c: LoadField: r1 = r2->field_13
    //     0x131002c: ldur            w1, [x2, #0x13]
    // 0x1310030: DecompressPointer r1
    //     0x1310030: add             x1, x1, HEAP, lsl #32
    // 0x1310034: r0 = of()
    //     0x1310034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1310038: LoadField: r1 = r0->field_87
    //     0x1310038: ldur            w1, [x0, #0x87]
    // 0x131003c: DecompressPointer r1
    //     0x131003c: add             x1, x1, HEAP, lsl #32
    // 0x1310040: LoadField: r0 = r1->field_7
    //     0x1310040: ldur            w0, [x1, #7]
    // 0x1310044: DecompressPointer r0
    //     0x1310044: add             x0, x0, HEAP, lsl #32
    // 0x1310048: ldur            x2, [fp, #-8]
    // 0x131004c: stur            x0, [fp, #-0x20]
    // 0x1310050: LoadField: r1 = r2->field_13
    //     0x1310050: ldur            w1, [x2, #0x13]
    // 0x1310054: DecompressPointer r1
    //     0x1310054: add             x1, x1, HEAP, lsl #32
    // 0x1310058: r0 = of()
    //     0x1310058: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x131005c: LoadField: r1 = r0->field_5b
    //     0x131005c: ldur            w1, [x0, #0x5b]
    // 0x1310060: DecompressPointer r1
    //     0x1310060: add             x1, x1, HEAP, lsl #32
    // 0x1310064: r16 = 16.000000
    //     0x1310064: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1310068: ldr             x16, [x16, #0x188]
    // 0x131006c: stp             x1, x16, [SP]
    // 0x1310070: ldur            x1, [fp, #-0x20]
    // 0x1310074: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1310074: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1310078: ldr             x4, [x4, #0xaa0]
    // 0x131007c: r0 = copyWith()
    //     0x131007c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1310080: stur            x0, [fp, #-0x20]
    // 0x1310084: r0 = Text()
    //     0x1310084: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1310088: mov             x1, x0
    // 0x131008c: ldur            x0, [fp, #-0x10]
    // 0x1310090: stur            x1, [fp, #-0x30]
    // 0x1310094: StoreField: r1->field_b = r0
    //     0x1310094: stur            w0, [x1, #0xb]
    // 0x1310098: ldur            x0, [fp, #-0x20]
    // 0x131009c: StoreField: r1->field_13 = r0
    //     0x131009c: stur            w0, [x1, #0x13]
    // 0x13100a0: r0 = Padding()
    //     0x13100a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13100a4: mov             x3, x0
    // 0x13100a8: r0 = Instance_EdgeInsets
    //     0x13100a8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x13100ac: ldr             x0, [x0, #0x668]
    // 0x13100b0: stur            x3, [fp, #-0x10]
    // 0x13100b4: StoreField: r3->field_f = r0
    //     0x13100b4: stur            w0, [x3, #0xf]
    // 0x13100b8: ldur            x0, [fp, #-0x30]
    // 0x13100bc: StoreField: r3->field_b = r0
    //     0x13100bc: stur            w0, [x3, #0xb]
    // 0x13100c0: r1 = Null
    //     0x13100c0: mov             x1, NULL
    // 0x13100c4: r2 = 4
    //     0x13100c4: movz            x2, #0x4
    // 0x13100c8: r0 = AllocateArray()
    //     0x13100c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13100cc: mov             x2, x0
    // 0x13100d0: ldur            x0, [fp, #-0x28]
    // 0x13100d4: stur            x2, [fp, #-0x20]
    // 0x13100d8: StoreField: r2->field_f = r0
    //     0x13100d8: stur            w0, [x2, #0xf]
    // 0x13100dc: ldur            x0, [fp, #-0x10]
    // 0x13100e0: StoreField: r2->field_13 = r0
    //     0x13100e0: stur            w0, [x2, #0x13]
    // 0x13100e4: r1 = <Widget>
    //     0x13100e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13100e8: r0 = AllocateGrowableArray()
    //     0x13100e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13100ec: mov             x1, x0
    // 0x13100f0: ldur            x0, [fp, #-0x20]
    // 0x13100f4: stur            x1, [fp, #-0x10]
    // 0x13100f8: StoreField: r1->field_f = r0
    //     0x13100f8: stur            w0, [x1, #0xf]
    // 0x13100fc: r0 = 4
    //     0x13100fc: movz            x0, #0x4
    // 0x1310100: StoreField: r1->field_b = r0
    //     0x1310100: stur            w0, [x1, #0xb]
    // 0x1310104: r0 = Column()
    //     0x1310104: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1310108: mov             x1, x0
    // 0x131010c: r0 = Instance_Axis
    //     0x131010c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1310110: stur            x1, [fp, #-0x20]
    // 0x1310114: StoreField: r1->field_f = r0
    //     0x1310114: stur            w0, [x1, #0xf]
    // 0x1310118: r0 = Instance_MainAxisAlignment
    //     0x1310118: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x131011c: ldr             x0, [x0, #0xa08]
    // 0x1310120: StoreField: r1->field_13 = r0
    //     0x1310120: stur            w0, [x1, #0x13]
    // 0x1310124: r0 = Instance_MainAxisSize
    //     0x1310124: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1310128: ldr             x0, [x0, #0xa10]
    // 0x131012c: ArrayStore: r1[0] = r0  ; List_4
    //     0x131012c: stur            w0, [x1, #0x17]
    // 0x1310130: r2 = Instance_CrossAxisAlignment
    //     0x1310130: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1310134: ldr             x2, [x2, #0x890]
    // 0x1310138: StoreField: r1->field_1b = r2
    //     0x1310138: stur            w2, [x1, #0x1b]
    // 0x131013c: r2 = Instance_VerticalDirection
    //     0x131013c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1310140: ldr             x2, [x2, #0xa20]
    // 0x1310144: StoreField: r1->field_23 = r2
    //     0x1310144: stur            w2, [x1, #0x23]
    // 0x1310148: r3 = Instance_Clip
    //     0x1310148: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x131014c: ldr             x3, [x3, #0x38]
    // 0x1310150: StoreField: r1->field_2b = r3
    //     0x1310150: stur            w3, [x1, #0x2b]
    // 0x1310154: StoreField: r1->field_2f = rZR
    //     0x1310154: stur            xzr, [x1, #0x2f]
    // 0x1310158: ldur            x4, [fp, #-0x10]
    // 0x131015c: StoreField: r1->field_b = r4
    //     0x131015c: stur            w4, [x1, #0xb]
    // 0x1310160: r0 = Padding()
    //     0x1310160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1310164: mov             x1, x0
    // 0x1310168: r0 = Instance_EdgeInsets
    //     0x1310168: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x131016c: ldr             x0, [x0, #0xa00]
    // 0x1310170: stur            x1, [fp, #-0x10]
    // 0x1310174: StoreField: r1->field_f = r0
    //     0x1310174: stur            w0, [x1, #0xf]
    // 0x1310178: ldur            x0, [fp, #-0x20]
    // 0x131017c: StoreField: r1->field_b = r0
    //     0x131017c: stur            w0, [x1, #0xb]
    // 0x1310180: r16 = <EdgeInsets>
    //     0x1310180: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1310184: ldr             x16, [x16, #0xda0]
    // 0x1310188: r30 = Instance_EdgeInsets
    //     0x1310188: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x131018c: ldr             lr, [lr, #0x28]
    // 0x1310190: stp             lr, x16, [SP]
    // 0x1310194: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1310194: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1310198: r0 = all()
    //     0x1310198: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x131019c: ldur            x2, [fp, #-8]
    // 0x13101a0: stur            x0, [fp, #-0x20]
    // 0x13101a4: LoadField: r1 = r2->field_13
    //     0x13101a4: ldur            w1, [x2, #0x13]
    // 0x13101a8: DecompressPointer r1
    //     0x13101a8: add             x1, x1, HEAP, lsl #32
    // 0x13101ac: r0 = of()
    //     0x13101ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13101b0: LoadField: r1 = r0->field_5b
    //     0x13101b0: ldur            w1, [x0, #0x5b]
    // 0x13101b4: DecompressPointer r1
    //     0x13101b4: add             x1, x1, HEAP, lsl #32
    // 0x13101b8: r16 = <Color>
    //     0x13101b8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13101bc: ldr             x16, [x16, #0xf80]
    // 0x13101c0: stp             x1, x16, [SP]
    // 0x13101c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13101c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13101c8: r0 = all()
    //     0x13101c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13101cc: stur            x0, [fp, #-0x28]
    // 0x13101d0: r16 = <RoundedRectangleBorder>
    //     0x13101d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13101d4: ldr             x16, [x16, #0xf78]
    // 0x13101d8: r30 = Instance_RoundedRectangleBorder
    //     0x13101d8: add             lr, PP, #0x40, lsl #12  ; [pp+0x409a8] Obj!RoundedRectangleBorder@d5acb1
    //     0x13101dc: ldr             lr, [lr, #0x9a8]
    // 0x13101e0: stp             lr, x16, [SP]
    // 0x13101e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13101e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13101e8: r0 = all()
    //     0x13101e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13101ec: stur            x0, [fp, #-0x30]
    // 0x13101f0: r0 = ButtonStyle()
    //     0x13101f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13101f4: mov             x1, x0
    // 0x13101f8: ldur            x0, [fp, #-0x28]
    // 0x13101fc: stur            x1, [fp, #-0x38]
    // 0x1310200: StoreField: r1->field_b = r0
    //     0x1310200: stur            w0, [x1, #0xb]
    // 0x1310204: ldur            x0, [fp, #-0x20]
    // 0x1310208: StoreField: r1->field_23 = r0
    //     0x1310208: stur            w0, [x1, #0x23]
    // 0x131020c: ldur            x0, [fp, #-0x30]
    // 0x1310210: StoreField: r1->field_43 = r0
    //     0x1310210: stur            w0, [x1, #0x43]
    // 0x1310214: r0 = TextButtonThemeData()
    //     0x1310214: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1310218: mov             x2, x0
    // 0x131021c: ldur            x0, [fp, #-0x38]
    // 0x1310220: stur            x2, [fp, #-0x20]
    // 0x1310224: StoreField: r2->field_7 = r0
    //     0x1310224: stur            w0, [x2, #7]
    // 0x1310228: r1 = "checkout"
    //     0x1310228: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c4e8] "checkout"
    //     0x131022c: ldr             x1, [x1, #0x4e8]
    // 0x1310230: r0 = capitalizeFirstWord()
    //     0x1310230: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x1310234: ldur            x2, [fp, #-8]
    // 0x1310238: stur            x0, [fp, #-0x28]
    // 0x131023c: LoadField: r1 = r2->field_13
    //     0x131023c: ldur            w1, [x2, #0x13]
    // 0x1310240: DecompressPointer r1
    //     0x1310240: add             x1, x1, HEAP, lsl #32
    // 0x1310244: r0 = of()
    //     0x1310244: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1310248: LoadField: r1 = r0->field_87
    //     0x1310248: ldur            w1, [x0, #0x87]
    // 0x131024c: DecompressPointer r1
    //     0x131024c: add             x1, x1, HEAP, lsl #32
    // 0x1310250: LoadField: r0 = r1->field_7
    //     0x1310250: ldur            w0, [x1, #7]
    // 0x1310254: DecompressPointer r0
    //     0x1310254: add             x0, x0, HEAP, lsl #32
    // 0x1310258: r16 = Instance_Color
    //     0x1310258: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x131025c: r30 = 16.000000
    //     0x131025c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1310260: ldr             lr, [lr, #0x188]
    // 0x1310264: stp             lr, x16, [SP]
    // 0x1310268: mov             x1, x0
    // 0x131026c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x131026c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1310270: ldr             x4, [x4, #0x9b8]
    // 0x1310274: r0 = copyWith()
    //     0x1310274: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1310278: stur            x0, [fp, #-0x30]
    // 0x131027c: r0 = Text()
    //     0x131027c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1310280: mov             x3, x0
    // 0x1310284: ldur            x0, [fp, #-0x28]
    // 0x1310288: stur            x3, [fp, #-0x38]
    // 0x131028c: StoreField: r3->field_b = r0
    //     0x131028c: stur            w0, [x3, #0xb]
    // 0x1310290: ldur            x0, [fp, #-0x30]
    // 0x1310294: StoreField: r3->field_13 = r0
    //     0x1310294: stur            w0, [x3, #0x13]
    // 0x1310298: ldur            x2, [fp, #-8]
    // 0x131029c: r1 = Function '<anonymous closure>':.
    //     0x131029c: add             x1, PP, #0x40, lsl #12  ; [pp+0x409b0] AnonymousClosure: (0x130fd80), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x1360280)
    //     0x13102a0: ldr             x1, [x1, #0x9b0]
    // 0x13102a4: r0 = AllocateClosure()
    //     0x13102a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13102a8: stur            x0, [fp, #-8]
    // 0x13102ac: r0 = TextButton()
    //     0x13102ac: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13102b0: mov             x1, x0
    // 0x13102b4: ldur            x0, [fp, #-8]
    // 0x13102b8: stur            x1, [fp, #-0x28]
    // 0x13102bc: StoreField: r1->field_b = r0
    //     0x13102bc: stur            w0, [x1, #0xb]
    // 0x13102c0: r0 = false
    //     0x13102c0: add             x0, NULL, #0x30  ; false
    // 0x13102c4: StoreField: r1->field_27 = r0
    //     0x13102c4: stur            w0, [x1, #0x27]
    // 0x13102c8: r0 = true
    //     0x13102c8: add             x0, NULL, #0x20  ; true
    // 0x13102cc: StoreField: r1->field_2f = r0
    //     0x13102cc: stur            w0, [x1, #0x2f]
    // 0x13102d0: ldur            x0, [fp, #-0x38]
    // 0x13102d4: StoreField: r1->field_37 = r0
    //     0x13102d4: stur            w0, [x1, #0x37]
    // 0x13102d8: r0 = Instance_ValueKey
    //     0x13102d8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34680] Obj!ValueKey<String>@d5b361
    //     0x13102dc: ldr             x0, [x0, #0x680]
    // 0x13102e0: StoreField: r1->field_7 = r0
    //     0x13102e0: stur            w0, [x1, #7]
    // 0x13102e4: r0 = TextButtonTheme()
    //     0x13102e4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13102e8: mov             x3, x0
    // 0x13102ec: ldur            x0, [fp, #-0x20]
    // 0x13102f0: stur            x3, [fp, #-8]
    // 0x13102f4: StoreField: r3->field_f = r0
    //     0x13102f4: stur            w0, [x3, #0xf]
    // 0x13102f8: ldur            x0, [fp, #-0x28]
    // 0x13102fc: StoreField: r3->field_b = r0
    //     0x13102fc: stur            w0, [x3, #0xb]
    // 0x1310300: r1 = Null
    //     0x1310300: mov             x1, NULL
    // 0x1310304: r2 = 6
    //     0x1310304: movz            x2, #0x6
    // 0x1310308: r0 = AllocateArray()
    //     0x1310308: bl              #0x16f7198  ; AllocateArrayStub
    // 0x131030c: mov             x2, x0
    // 0x1310310: ldur            x0, [fp, #-0x10]
    // 0x1310314: stur            x2, [fp, #-0x20]
    // 0x1310318: StoreField: r2->field_f = r0
    //     0x1310318: stur            w0, [x2, #0xf]
    // 0x131031c: r16 = Instance_Spacer
    //     0x131031c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1310320: ldr             x16, [x16, #0xf0]
    // 0x1310324: StoreField: r2->field_13 = r16
    //     0x1310324: stur            w16, [x2, #0x13]
    // 0x1310328: ldur            x0, [fp, #-8]
    // 0x131032c: ArrayStore: r2[0] = r0  ; List_4
    //     0x131032c: stur            w0, [x2, #0x17]
    // 0x1310330: r1 = <Widget>
    //     0x1310330: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1310334: r0 = AllocateGrowableArray()
    //     0x1310334: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1310338: mov             x1, x0
    // 0x131033c: ldur            x0, [fp, #-0x20]
    // 0x1310340: stur            x1, [fp, #-8]
    // 0x1310344: StoreField: r1->field_f = r0
    //     0x1310344: stur            w0, [x1, #0xf]
    // 0x1310348: r0 = 6
    //     0x1310348: movz            x0, #0x6
    // 0x131034c: StoreField: r1->field_b = r0
    //     0x131034c: stur            w0, [x1, #0xb]
    // 0x1310350: r0 = Row()
    //     0x1310350: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1310354: mov             x1, x0
    // 0x1310358: r0 = Instance_Axis
    //     0x1310358: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x131035c: stur            x1, [fp, #-0x10]
    // 0x1310360: StoreField: r1->field_f = r0
    //     0x1310360: stur            w0, [x1, #0xf]
    // 0x1310364: r0 = Instance_MainAxisAlignment
    //     0x1310364: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x1310368: ldr             x0, [x0, #0xab0]
    // 0x131036c: StoreField: r1->field_13 = r0
    //     0x131036c: stur            w0, [x1, #0x13]
    // 0x1310370: r0 = Instance_MainAxisSize
    //     0x1310370: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1310374: ldr             x0, [x0, #0xa10]
    // 0x1310378: ArrayStore: r1[0] = r0  ; List_4
    //     0x1310378: stur            w0, [x1, #0x17]
    // 0x131037c: r0 = Instance_CrossAxisAlignment
    //     0x131037c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1310380: ldr             x0, [x0, #0xa18]
    // 0x1310384: StoreField: r1->field_1b = r0
    //     0x1310384: stur            w0, [x1, #0x1b]
    // 0x1310388: r0 = Instance_VerticalDirection
    //     0x1310388: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x131038c: ldr             x0, [x0, #0xa20]
    // 0x1310390: StoreField: r1->field_23 = r0
    //     0x1310390: stur            w0, [x1, #0x23]
    // 0x1310394: r0 = Instance_Clip
    //     0x1310394: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1310398: ldr             x0, [x0, #0x38]
    // 0x131039c: StoreField: r1->field_2b = r0
    //     0x131039c: stur            w0, [x1, #0x2b]
    // 0x13103a0: StoreField: r1->field_2f = rZR
    //     0x13103a0: stur            xzr, [x1, #0x2f]
    // 0x13103a4: ldur            x0, [fp, #-8]
    // 0x13103a8: StoreField: r1->field_b = r0
    //     0x13103a8: stur            w0, [x1, #0xb]
    // 0x13103ac: r0 = Padding()
    //     0x13103ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13103b0: mov             x1, x0
    // 0x13103b4: r0 = Instance_EdgeInsets
    //     0x13103b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13103b8: ldr             x0, [x0, #0x1f0]
    // 0x13103bc: stur            x1, [fp, #-8]
    // 0x13103c0: StoreField: r1->field_f = r0
    //     0x13103c0: stur            w0, [x1, #0xf]
    // 0x13103c4: ldur            x0, [fp, #-0x10]
    // 0x13103c8: StoreField: r1->field_b = r0
    //     0x13103c8: stur            w0, [x1, #0xb]
    // 0x13103cc: r0 = Container()
    //     0x13103cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13103d0: stur            x0, [fp, #-0x10]
    // 0x13103d4: ldur            x16, [fp, #-0x18]
    // 0x13103d8: ldur            lr, [fp, #-8]
    // 0x13103dc: stp             lr, x16, [SP]
    // 0x13103e0: mov             x1, x0
    // 0x13103e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13103e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13103e8: ldr             x4, [x4, #0x88]
    // 0x13103ec: r0 = Container()
    //     0x13103ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13103f0: ldur            d0, [fp, #-0x40]
    // 0x13103f4: r0 = inline_Allocate_Double()
    //     0x13103f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13103f8: add             x0, x0, #0x10
    //     0x13103fc: cmp             x1, x0
    //     0x1310400: b.ls            #0x1310448
    //     0x1310404: str             x0, [THR, #0x50]  ; THR::top
    //     0x1310408: sub             x0, x0, #0xf
    //     0x131040c: movz            x1, #0xe15c
    //     0x1310410: movk            x1, #0x3, lsl #16
    //     0x1310414: stur            x1, [x0, #-1]
    // 0x1310418: StoreField: r0->field_7 = d0
    //     0x1310418: stur            d0, [x0, #7]
    // 0x131041c: stur            x0, [fp, #-8]
    // 0x1310420: r0 = SizedBox()
    //     0x1310420: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1310424: ldur            x1, [fp, #-8]
    // 0x1310428: StoreField: r0->field_13 = r1
    //     0x1310428: stur            w1, [x0, #0x13]
    // 0x131042c: ldur            x1, [fp, #-0x10]
    // 0x1310430: StoreField: r0->field_b = r1
    //     0x1310430: stur            w1, [x0, #0xb]
    // 0x1310434: LeaveFrame
    //     0x1310434: mov             SP, fp
    //     0x1310438: ldp             fp, lr, [SP], #0x10
    // 0x131043c: ret
    //     0x131043c: ret             
    // 0x1310440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1310440: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1310444: b               #0x130fea8
    // 0x1310448: SaveReg d0
    //     0x1310448: str             q0, [SP, #-0x10]!
    // 0x131044c: r0 = AllocateDouble()
    //     0x131044c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1310450: RestoreReg d0
    //     0x1310450: ldr             q0, [SP], #0x10
    // 0x1310454: b               #0x1310418
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1360280, size: 0x64
    // 0x1360280: EnterFrame
    //     0x1360280: stp             fp, lr, [SP, #-0x10]!
    //     0x1360284: mov             fp, SP
    // 0x1360288: AllocStack(0x18)
    //     0x1360288: sub             SP, SP, #0x18
    // 0x136028c: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x136028c: stur            x1, [fp, #-8]
    //     0x1360290: stur            x2, [fp, #-0x10]
    // 0x1360294: r1 = 2
    //     0x1360294: movz            x1, #0x2
    // 0x1360298: r0 = AllocateContext()
    //     0x1360298: bl              #0x16f6108  ; AllocateContextStub
    // 0x136029c: mov             x1, x0
    // 0x13602a0: ldur            x0, [fp, #-8]
    // 0x13602a4: stur            x1, [fp, #-0x18]
    // 0x13602a8: StoreField: r1->field_f = r0
    //     0x13602a8: stur            w0, [x1, #0xf]
    // 0x13602ac: ldur            x0, [fp, #-0x10]
    // 0x13602b0: StoreField: r1->field_13 = r0
    //     0x13602b0: stur            w0, [x1, #0x13]
    // 0x13602b4: r0 = Obx()
    //     0x13602b4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13602b8: ldur            x2, [fp, #-0x18]
    // 0x13602bc: r1 = Function '<anonymous closure>':.
    //     0x13602bc: add             x1, PP, #0x40, lsl #12  ; [pp+0x409a0] AnonymousClosure: (0x130fe80), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x1360280)
    //     0x13602c0: ldr             x1, [x1, #0x9a0]
    // 0x13602c4: stur            x0, [fp, #-8]
    // 0x13602c8: r0 = AllocateClosure()
    //     0x13602c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13602cc: mov             x1, x0
    // 0x13602d0: ldur            x0, [fp, #-8]
    // 0x13602d4: StoreField: r0->field_b = r1
    //     0x13602d4: stur            w1, [x0, #0xb]
    // 0x13602d8: LeaveFrame
    //     0x13602d8: mov             SP, fp
    //     0x13602dc: ldp             fp, lr, [SP], #0x10
    // 0x13602e0: ret
    //     0x13602e0: ret             
  }
  _ body(/* No info */) {
    // ** addr: 0x14dbce0, size: 0x64
    // 0x14dbce0: EnterFrame
    //     0x14dbce0: stp             fp, lr, [SP, #-0x10]!
    //     0x14dbce4: mov             fp, SP
    // 0x14dbce8: AllocStack(0x18)
    //     0x14dbce8: sub             SP, SP, #0x18
    // 0x14dbcec: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14dbcec: stur            x1, [fp, #-8]
    //     0x14dbcf0: stur            x2, [fp, #-0x10]
    // 0x14dbcf4: r1 = 2
    //     0x14dbcf4: movz            x1, #0x2
    // 0x14dbcf8: r0 = AllocateContext()
    //     0x14dbcf8: bl              #0x16f6108  ; AllocateContextStub
    // 0x14dbcfc: mov             x1, x0
    // 0x14dbd00: ldur            x0, [fp, #-8]
    // 0x14dbd04: stur            x1, [fp, #-0x18]
    // 0x14dbd08: StoreField: r1->field_f = r0
    //     0x14dbd08: stur            w0, [x1, #0xf]
    // 0x14dbd0c: ldur            x0, [fp, #-0x10]
    // 0x14dbd10: StoreField: r1->field_13 = r0
    //     0x14dbd10: stur            w0, [x1, #0x13]
    // 0x14dbd14: r0 = Obx()
    //     0x14dbd14: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14dbd18: ldur            x2, [fp, #-0x18]
    // 0x14dbd1c: r1 = Function '<anonymous closure>':.
    //     0x14dbd1c: add             x1, PP, #0x40, lsl #12  ; [pp+0x409b8] AnonymousClosure: (0x14dbd44), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x14dbce0)
    //     0x14dbd20: ldr             x1, [x1, #0x9b8]
    // 0x14dbd24: stur            x0, [fp, #-8]
    // 0x14dbd28: r0 = AllocateClosure()
    //     0x14dbd28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dbd2c: mov             x1, x0
    // 0x14dbd30: ldur            x0, [fp, #-8]
    // 0x14dbd34: StoreField: r0->field_b = r1
    //     0x14dbd34: stur            w1, [x0, #0xb]
    // 0x14dbd38: LeaveFrame
    //     0x14dbd38: mov             SP, fp
    //     0x14dbd3c: ldp             fp, lr, [SP], #0x10
    // 0x14dbd40: ret
    //     0x14dbd40: ret             
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0x14dbd44, size: 0x1e44
    // 0x14dbd44: EnterFrame
    //     0x14dbd44: stp             fp, lr, [SP, #-0x10]!
    //     0x14dbd48: mov             fp, SP
    // 0x14dbd4c: AllocStack(0x90)
    //     0x14dbd4c: sub             SP, SP, #0x90
    // 0x14dbd50: SetupParameters()
    //     0x14dbd50: ldr             x0, [fp, #0x10]
    //     0x14dbd54: ldur            w2, [x0, #0x17]
    //     0x14dbd58: add             x2, x2, HEAP, lsl #32
    //     0x14dbd5c: stur            x2, [fp, #-8]
    // 0x14dbd60: CheckStackOverflow
    //     0x14dbd60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14dbd64: cmp             SP, x16
    //     0x14dbd68: b.ls            #0x14ddb40
    // 0x14dbd6c: r1 = Instance_Color
    //     0x14dbd6c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dbd70: d0 = 0.030000
    //     0x14dbd70: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14dbd74: ldr             d0, [x17, #0x238]
    // 0x14dbd78: r0 = withOpacity()
    //     0x14dbd78: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14dbd7c: stur            x0, [fp, #-0x10]
    // 0x14dbd80: r0 = Radius()
    //     0x14dbd80: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14dbd84: d0 = 12.000000
    //     0x14dbd84: fmov            d0, #12.00000000
    // 0x14dbd88: stur            x0, [fp, #-0x18]
    // 0x14dbd8c: StoreField: r0->field_7 = d0
    //     0x14dbd8c: stur            d0, [x0, #7]
    // 0x14dbd90: StoreField: r0->field_f = d0
    //     0x14dbd90: stur            d0, [x0, #0xf]
    // 0x14dbd94: r0 = BorderRadius()
    //     0x14dbd94: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14dbd98: mov             x1, x0
    // 0x14dbd9c: ldur            x0, [fp, #-0x18]
    // 0x14dbda0: stur            x1, [fp, #-0x20]
    // 0x14dbda4: StoreField: r1->field_7 = r0
    //     0x14dbda4: stur            w0, [x1, #7]
    // 0x14dbda8: StoreField: r1->field_b = r0
    //     0x14dbda8: stur            w0, [x1, #0xb]
    // 0x14dbdac: StoreField: r1->field_f = r0
    //     0x14dbdac: stur            w0, [x1, #0xf]
    // 0x14dbdb0: StoreField: r1->field_13 = r0
    //     0x14dbdb0: stur            w0, [x1, #0x13]
    // 0x14dbdb4: r0 = BoxDecoration()
    //     0x14dbdb4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14dbdb8: mov             x2, x0
    // 0x14dbdbc: ldur            x0, [fp, #-0x10]
    // 0x14dbdc0: stur            x2, [fp, #-0x18]
    // 0x14dbdc4: StoreField: r2->field_7 = r0
    //     0x14dbdc4: stur            w0, [x2, #7]
    // 0x14dbdc8: ldur            x0, [fp, #-0x20]
    // 0x14dbdcc: StoreField: r2->field_13 = r0
    //     0x14dbdcc: stur            w0, [x2, #0x13]
    // 0x14dbdd0: r0 = Instance_BoxShape
    //     0x14dbdd0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14dbdd4: ldr             x0, [x0, #0x80]
    // 0x14dbdd8: StoreField: r2->field_23 = r0
    //     0x14dbdd8: stur            w0, [x2, #0x23]
    // 0x14dbddc: ldur            x3, [fp, #-8]
    // 0x14dbde0: LoadField: r1 = r3->field_13
    //     0x14dbde0: ldur            w1, [x3, #0x13]
    // 0x14dbde4: DecompressPointer r1
    //     0x14dbde4: add             x1, x1, HEAP, lsl #32
    // 0x14dbde8: r0 = of()
    //     0x14dbde8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dbdec: LoadField: r1 = r0->field_87
    //     0x14dbdec: ldur            w1, [x0, #0x87]
    // 0x14dbdf0: DecompressPointer r1
    //     0x14dbdf0: add             x1, x1, HEAP, lsl #32
    // 0x14dbdf4: LoadField: r0 = r1->field_7
    //     0x14dbdf4: ldur            w0, [x1, #7]
    // 0x14dbdf8: DecompressPointer r0
    //     0x14dbdf8: add             x0, x0, HEAP, lsl #32
    // 0x14dbdfc: r16 = Instance_Color
    //     0x14dbdfc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dbe00: r30 = 14.000000
    //     0x14dbe00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14dbe04: ldr             lr, [lr, #0x1d8]
    // 0x14dbe08: stp             lr, x16, [SP]
    // 0x14dbe0c: mov             x1, x0
    // 0x14dbe10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dbe10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dbe14: ldr             x4, [x4, #0x9b8]
    // 0x14dbe18: r0 = copyWith()
    //     0x14dbe18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dbe1c: stur            x0, [fp, #-0x10]
    // 0x14dbe20: r0 = Text()
    //     0x14dbe20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dbe24: mov             x1, x0
    // 0x14dbe28: r0 = "Bag"
    //     0x14dbe28: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0x14dbe2c: ldr             x0, [x0, #0xd60]
    // 0x14dbe30: stur            x1, [fp, #-0x20]
    // 0x14dbe34: StoreField: r1->field_b = r0
    //     0x14dbe34: stur            w0, [x1, #0xb]
    // 0x14dbe38: ldur            x0, [fp, #-0x10]
    // 0x14dbe3c: StoreField: r1->field_13 = r0
    //     0x14dbe3c: stur            w0, [x1, #0x13]
    // 0x14dbe40: r0 = Padding()
    //     0x14dbe40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dbe44: mov             x1, x0
    // 0x14dbe48: r0 = Instance_EdgeInsets
    //     0x14dbe48: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x14dbe4c: ldr             x0, [x0, #0xc40]
    // 0x14dbe50: stur            x1, [fp, #-0x10]
    // 0x14dbe54: StoreField: r1->field_f = r0
    //     0x14dbe54: stur            w0, [x1, #0xf]
    // 0x14dbe58: ldur            x0, [fp, #-0x20]
    // 0x14dbe5c: StoreField: r1->field_b = r0
    //     0x14dbe5c: stur            w0, [x1, #0xb]
    // 0x14dbe60: r0 = Radius()
    //     0x14dbe60: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14dbe64: d0 = 12.000000
    //     0x14dbe64: fmov            d0, #12.00000000
    // 0x14dbe68: stur            x0, [fp, #-0x20]
    // 0x14dbe6c: StoreField: r0->field_7 = d0
    //     0x14dbe6c: stur            d0, [x0, #7]
    // 0x14dbe70: StoreField: r0->field_f = d0
    //     0x14dbe70: stur            d0, [x0, #0xf]
    // 0x14dbe74: r0 = BorderRadius()
    //     0x14dbe74: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14dbe78: mov             x2, x0
    // 0x14dbe7c: ldur            x0, [fp, #-0x20]
    // 0x14dbe80: stur            x2, [fp, #-0x28]
    // 0x14dbe84: StoreField: r2->field_7 = r0
    //     0x14dbe84: stur            w0, [x2, #7]
    // 0x14dbe88: StoreField: r2->field_b = r0
    //     0x14dbe88: stur            w0, [x2, #0xb]
    // 0x14dbe8c: StoreField: r2->field_f = r0
    //     0x14dbe8c: stur            w0, [x2, #0xf]
    // 0x14dbe90: StoreField: r2->field_13 = r0
    //     0x14dbe90: stur            w0, [x2, #0x13]
    // 0x14dbe94: ldur            x0, [fp, #-8]
    // 0x14dbe98: LoadField: r1 = r0->field_f
    //     0x14dbe98: ldur            w1, [x0, #0xf]
    // 0x14dbe9c: DecompressPointer r1
    //     0x14dbe9c: add             x1, x1, HEAP, lsl #32
    // 0x14dbea0: r0 = controller()
    //     0x14dbea0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dbea4: LoadField: r1 = r0->field_53
    //     0x14dbea4: ldur            w1, [x0, #0x53]
    // 0x14dbea8: DecompressPointer r1
    //     0x14dbea8: add             x1, x1, HEAP, lsl #32
    // 0x14dbeac: r0 = value()
    //     0x14dbeac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dbeb0: LoadField: r2 = r0->field_5b
    //     0x14dbeb0: ldur            w2, [x0, #0x5b]
    // 0x14dbeb4: DecompressPointer r2
    //     0x14dbeb4: add             x2, x2, HEAP, lsl #32
    // 0x14dbeb8: cmp             w2, NULL
    // 0x14dbebc: b.ne            #0x14dbec8
    // 0x14dbec0: r0 = Null
    //     0x14dbec0: mov             x0, NULL
    // 0x14dbec4: b               #0x14dbef8
    // 0x14dbec8: LoadField: r0 = r2->field_b
    //     0x14dbec8: ldur            w0, [x2, #0xb]
    // 0x14dbecc: r1 = LoadInt32Instr(r0)
    //     0x14dbecc: sbfx            x1, x0, #1, #0x1f
    // 0x14dbed0: mov             x0, x1
    // 0x14dbed4: r1 = 0
    //     0x14dbed4: movz            x1, #0
    // 0x14dbed8: cmp             x1, x0
    // 0x14dbedc: b.hs            #0x14ddb48
    // 0x14dbee0: LoadField: r0 = r2->field_f
    //     0x14dbee0: ldur            w0, [x2, #0xf]
    // 0x14dbee4: DecompressPointer r0
    //     0x14dbee4: add             x0, x0, HEAP, lsl #32
    // 0x14dbee8: LoadField: r1 = r0->field_f
    //     0x14dbee8: ldur            w1, [x0, #0xf]
    // 0x14dbeec: DecompressPointer r1
    //     0x14dbeec: add             x1, x1, HEAP, lsl #32
    // 0x14dbef0: LoadField: r0 = r1->field_f
    //     0x14dbef0: ldur            w0, [x1, #0xf]
    // 0x14dbef4: DecompressPointer r0
    //     0x14dbef4: add             x0, x0, HEAP, lsl #32
    // 0x14dbef8: cmp             w0, NULL
    // 0x14dbefc: b.ne            #0x14dbf08
    // 0x14dbf00: r4 = ""
    //     0x14dbf00: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dbf04: b               #0x14dbf0c
    // 0x14dbf08: mov             x4, x0
    // 0x14dbf0c: ldur            x3, [fp, #-8]
    // 0x14dbf10: ldur            x0, [fp, #-0x28]
    // 0x14dbf14: stur            x4, [fp, #-0x20]
    // 0x14dbf18: r1 = Function '<anonymous closure>':.
    //     0x14dbf18: add             x1, PP, #0x40, lsl #12  ; [pp+0x409c0] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x14dbf1c: ldr             x1, [x1, #0x9c0]
    // 0x14dbf20: r2 = Null
    //     0x14dbf20: mov             x2, NULL
    // 0x14dbf24: r0 = AllocateClosure()
    //     0x14dbf24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dbf28: stur            x0, [fp, #-0x30]
    // 0x14dbf2c: r0 = CachedNetworkImage()
    //     0x14dbf2c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14dbf30: stur            x0, [fp, #-0x38]
    // 0x14dbf34: r16 = Instance_BoxFit
    //     0x14dbf34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14dbf38: ldr             x16, [x16, #0x118]
    // 0x14dbf3c: r30 = 80.000000
    //     0x14dbf3c: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14dbf40: ldr             lr, [lr, #0x2f8]
    // 0x14dbf44: stp             lr, x16, [SP, #0x10]
    // 0x14dbf48: r16 = 80.000000
    //     0x14dbf48: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14dbf4c: ldr             x16, [x16, #0x2f8]
    // 0x14dbf50: ldur            lr, [fp, #-0x30]
    // 0x14dbf54: stp             lr, x16, [SP]
    // 0x14dbf58: mov             x1, x0
    // 0x14dbf5c: ldur            x2, [fp, #-0x20]
    // 0x14dbf60: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x14dbf60: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x14dbf64: ldr             x4, [x4, #0xbf8]
    // 0x14dbf68: r0 = CachedNetworkImage()
    //     0x14dbf68: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14dbf6c: r0 = ClipRRect()
    //     0x14dbf6c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14dbf70: mov             x2, x0
    // 0x14dbf74: ldur            x0, [fp, #-0x28]
    // 0x14dbf78: stur            x2, [fp, #-0x20]
    // 0x14dbf7c: StoreField: r2->field_f = r0
    //     0x14dbf7c: stur            w0, [x2, #0xf]
    // 0x14dbf80: r0 = Instance_Clip
    //     0x14dbf80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14dbf84: ldr             x0, [x0, #0x138]
    // 0x14dbf88: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dbf88: stur            w0, [x2, #0x17]
    // 0x14dbf8c: ldur            x1, [fp, #-0x38]
    // 0x14dbf90: StoreField: r2->field_b = r1
    //     0x14dbf90: stur            w1, [x2, #0xb]
    // 0x14dbf94: ldur            x3, [fp, #-8]
    // 0x14dbf98: LoadField: r1 = r3->field_f
    //     0x14dbf98: ldur            w1, [x3, #0xf]
    // 0x14dbf9c: DecompressPointer r1
    //     0x14dbf9c: add             x1, x1, HEAP, lsl #32
    // 0x14dbfa0: r0 = controller()
    //     0x14dbfa0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dbfa4: LoadField: r1 = r0->field_53
    //     0x14dbfa4: ldur            w1, [x0, #0x53]
    // 0x14dbfa8: DecompressPointer r1
    //     0x14dbfa8: add             x1, x1, HEAP, lsl #32
    // 0x14dbfac: r0 = value()
    //     0x14dbfac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dbfb0: LoadField: r2 = r0->field_5b
    //     0x14dbfb0: ldur            w2, [x0, #0x5b]
    // 0x14dbfb4: DecompressPointer r2
    //     0x14dbfb4: add             x2, x2, HEAP, lsl #32
    // 0x14dbfb8: cmp             w2, NULL
    // 0x14dbfbc: b.ne            #0x14dbfc8
    // 0x14dbfc0: r0 = Null
    //     0x14dbfc0: mov             x0, NULL
    // 0x14dbfc4: b               #0x14dbff8
    // 0x14dbfc8: LoadField: r0 = r2->field_b
    //     0x14dbfc8: ldur            w0, [x2, #0xb]
    // 0x14dbfcc: r1 = LoadInt32Instr(r0)
    //     0x14dbfcc: sbfx            x1, x0, #1, #0x1f
    // 0x14dbfd0: mov             x0, x1
    // 0x14dbfd4: r1 = 0
    //     0x14dbfd4: movz            x1, #0
    // 0x14dbfd8: cmp             x1, x0
    // 0x14dbfdc: b.hs            #0x14ddb4c
    // 0x14dbfe0: LoadField: r0 = r2->field_f
    //     0x14dbfe0: ldur            w0, [x2, #0xf]
    // 0x14dbfe4: DecompressPointer r0
    //     0x14dbfe4: add             x0, x0, HEAP, lsl #32
    // 0x14dbfe8: LoadField: r1 = r0->field_f
    //     0x14dbfe8: ldur            w1, [x0, #0xf]
    // 0x14dbfec: DecompressPointer r1
    //     0x14dbfec: add             x1, x1, HEAP, lsl #32
    // 0x14dbff0: LoadField: r0 = r1->field_13
    //     0x14dbff0: ldur            w0, [x1, #0x13]
    // 0x14dbff4: DecompressPointer r0
    //     0x14dbff4: add             x0, x0, HEAP, lsl #32
    // 0x14dbff8: cmp             w0, NULL
    // 0x14dbffc: b.ne            #0x14dc004
    // 0x14dc000: r0 = ""
    //     0x14dc000: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc004: ldur            x2, [fp, #-8]
    // 0x14dc008: stur            x0, [fp, #-0x28]
    // 0x14dc00c: LoadField: r1 = r2->field_13
    //     0x14dc00c: ldur            w1, [x2, #0x13]
    // 0x14dc010: DecompressPointer r1
    //     0x14dc010: add             x1, x1, HEAP, lsl #32
    // 0x14dc014: r0 = of()
    //     0x14dc014: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dc018: LoadField: r1 = r0->field_87
    //     0x14dc018: ldur            w1, [x0, #0x87]
    // 0x14dc01c: DecompressPointer r1
    //     0x14dc01c: add             x1, x1, HEAP, lsl #32
    // 0x14dc020: LoadField: r0 = r1->field_7
    //     0x14dc020: ldur            w0, [x1, #7]
    // 0x14dc024: DecompressPointer r0
    //     0x14dc024: add             x0, x0, HEAP, lsl #32
    // 0x14dc028: r16 = Instance_Color
    //     0x14dc028: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dc02c: r30 = 12.000000
    //     0x14dc02c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dc030: ldr             lr, [lr, #0x9e8]
    // 0x14dc034: stp             lr, x16, [SP]
    // 0x14dc038: mov             x1, x0
    // 0x14dc03c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dc03c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dc040: ldr             x4, [x4, #0x9b8]
    // 0x14dc044: r0 = copyWith()
    //     0x14dc044: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dc048: stur            x0, [fp, #-0x30]
    // 0x14dc04c: r0 = Text()
    //     0x14dc04c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dc050: mov             x2, x0
    // 0x14dc054: ldur            x0, [fp, #-0x28]
    // 0x14dc058: stur            x2, [fp, #-0x38]
    // 0x14dc05c: StoreField: r2->field_b = r0
    //     0x14dc05c: stur            w0, [x2, #0xb]
    // 0x14dc060: ldur            x0, [fp, #-0x30]
    // 0x14dc064: StoreField: r2->field_13 = r0
    //     0x14dc064: stur            w0, [x2, #0x13]
    // 0x14dc068: r0 = 2
    //     0x14dc068: movz            x0, #0x2
    // 0x14dc06c: StoreField: r2->field_37 = r0
    //     0x14dc06c: stur            w0, [x2, #0x37]
    // 0x14dc070: ldur            x3, [fp, #-8]
    // 0x14dc074: LoadField: r1 = r3->field_f
    //     0x14dc074: ldur            w1, [x3, #0xf]
    // 0x14dc078: DecompressPointer r1
    //     0x14dc078: add             x1, x1, HEAP, lsl #32
    // 0x14dc07c: r0 = controller()
    //     0x14dc07c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc080: LoadField: r1 = r0->field_6f
    //     0x14dc080: ldur            w1, [x0, #0x6f]
    // 0x14dc084: DecompressPointer r1
    //     0x14dc084: add             x1, x1, HEAP, lsl #32
    // 0x14dc088: r0 = value()
    //     0x14dc088: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc08c: LoadField: r1 = r0->field_b
    //     0x14dc08c: ldur            w1, [x0, #0xb]
    // 0x14dc090: DecompressPointer r1
    //     0x14dc090: add             x1, x1, HEAP, lsl #32
    // 0x14dc094: cmp             w1, NULL
    // 0x14dc098: b.ne            #0x14dc0a4
    // 0x14dc09c: r0 = Null
    //     0x14dc09c: mov             x0, NULL
    // 0x14dc0a0: b               #0x14dc0c8
    // 0x14dc0a4: LoadField: r0 = r1->field_2b
    //     0x14dc0a4: ldur            w0, [x1, #0x2b]
    // 0x14dc0a8: DecompressPointer r0
    //     0x14dc0a8: add             x0, x0, HEAP, lsl #32
    // 0x14dc0ac: cmp             w0, NULL
    // 0x14dc0b0: b.ne            #0x14dc0bc
    // 0x14dc0b4: r0 = Null
    //     0x14dc0b4: mov             x0, NULL
    // 0x14dc0b8: b               #0x14dc0c8
    // 0x14dc0bc: LoadField: r1 = r0->field_33
    //     0x14dc0bc: ldur            w1, [x0, #0x33]
    // 0x14dc0c0: DecompressPointer r1
    //     0x14dc0c0: add             x1, x1, HEAP, lsl #32
    // 0x14dc0c4: mov             x0, x1
    // 0x14dc0c8: r1 = LoadClassIdInstr(r0)
    //     0x14dc0c8: ldur            x1, [x0, #-1]
    //     0x14dc0cc: ubfx            x1, x1, #0xc, #0x14
    // 0x14dc0d0: r16 = "size"
    //     0x14dc0d0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14dc0d4: ldr             x16, [x16, #0x9c0]
    // 0x14dc0d8: stp             x16, x0, [SP]
    // 0x14dc0dc: mov             x0, x1
    // 0x14dc0e0: mov             lr, x0
    // 0x14dc0e4: ldr             lr, [x21, lr, lsl #3]
    // 0x14dc0e8: blr             lr
    // 0x14dc0ec: tbz             w0, #4, #0x14dc184
    // 0x14dc0f0: ldur            x2, [fp, #-8]
    // 0x14dc0f4: LoadField: r1 = r2->field_f
    //     0x14dc0f4: ldur            w1, [x2, #0xf]
    // 0x14dc0f8: DecompressPointer r1
    //     0x14dc0f8: add             x1, x1, HEAP, lsl #32
    // 0x14dc0fc: r0 = controller()
    //     0x14dc0fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc100: LoadField: r1 = r0->field_6f
    //     0x14dc100: ldur            w1, [x0, #0x6f]
    // 0x14dc104: DecompressPointer r1
    //     0x14dc104: add             x1, x1, HEAP, lsl #32
    // 0x14dc108: r0 = value()
    //     0x14dc108: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc10c: LoadField: r1 = r0->field_b
    //     0x14dc10c: ldur            w1, [x0, #0xb]
    // 0x14dc110: DecompressPointer r1
    //     0x14dc110: add             x1, x1, HEAP, lsl #32
    // 0x14dc114: cmp             w1, NULL
    // 0x14dc118: b.ne            #0x14dc124
    // 0x14dc11c: r0 = Null
    //     0x14dc11c: mov             x0, NULL
    // 0x14dc120: b               #0x14dc15c
    // 0x14dc124: LoadField: r0 = r1->field_33
    //     0x14dc124: ldur            w0, [x1, #0x33]
    // 0x14dc128: DecompressPointer r0
    //     0x14dc128: add             x0, x0, HEAP, lsl #32
    // 0x14dc12c: cmp             w0, NULL
    // 0x14dc130: b.ne            #0x14dc13c
    // 0x14dc134: r0 = Null
    //     0x14dc134: mov             x0, NULL
    // 0x14dc138: b               #0x14dc15c
    // 0x14dc13c: LoadField: r1 = r0->field_7
    //     0x14dc13c: ldur            w1, [x0, #7]
    // 0x14dc140: DecompressPointer r1
    //     0x14dc140: add             x1, x1, HEAP, lsl #32
    // 0x14dc144: cmp             w1, NULL
    // 0x14dc148: b.ne            #0x14dc154
    // 0x14dc14c: r0 = Null
    //     0x14dc14c: mov             x0, NULL
    // 0x14dc150: b               #0x14dc15c
    // 0x14dc154: LoadField: r0 = r1->field_13
    //     0x14dc154: ldur            w0, [x1, #0x13]
    // 0x14dc158: DecompressPointer r0
    //     0x14dc158: add             x0, x0, HEAP, lsl #32
    // 0x14dc15c: r1 = LoadClassIdInstr(r0)
    //     0x14dc15c: ldur            x1, [x0, #-1]
    //     0x14dc160: ubfx            x1, x1, #0xc, #0x14
    // 0x14dc164: r16 = "size"
    //     0x14dc164: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14dc168: ldr             x16, [x16, #0x9c0]
    // 0x14dc16c: stp             x16, x0, [SP]
    // 0x14dc170: mov             x0, x1
    // 0x14dc174: mov             lr, x0
    // 0x14dc178: ldr             lr, [x21, lr, lsl #3]
    // 0x14dc17c: blr             lr
    // 0x14dc180: tbnz            w0, #4, #0x14dc35c
    // 0x14dc184: ldur            x0, [fp, #-8]
    // 0x14dc188: r1 = Null
    //     0x14dc188: mov             x1, NULL
    // 0x14dc18c: r2 = 8
    //     0x14dc18c: movz            x2, #0x8
    // 0x14dc190: r0 = AllocateArray()
    //     0x14dc190: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dc194: stur            x0, [fp, #-0x28]
    // 0x14dc198: r16 = "Size: "
    //     0x14dc198: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x14dc19c: ldr             x16, [x16, #0xf00]
    // 0x14dc1a0: StoreField: r0->field_f = r16
    //     0x14dc1a0: stur            w16, [x0, #0xf]
    // 0x14dc1a4: ldur            x2, [fp, #-8]
    // 0x14dc1a8: LoadField: r1 = r2->field_f
    //     0x14dc1a8: ldur            w1, [x2, #0xf]
    // 0x14dc1ac: DecompressPointer r1
    //     0x14dc1ac: add             x1, x1, HEAP, lsl #32
    // 0x14dc1b0: r0 = controller()
    //     0x14dc1b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc1b4: LoadField: r1 = r0->field_53
    //     0x14dc1b4: ldur            w1, [x0, #0x53]
    // 0x14dc1b8: DecompressPointer r1
    //     0x14dc1b8: add             x1, x1, HEAP, lsl #32
    // 0x14dc1bc: r0 = value()
    //     0x14dc1bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc1c0: LoadField: r2 = r0->field_5b
    //     0x14dc1c0: ldur            w2, [x0, #0x5b]
    // 0x14dc1c4: DecompressPointer r2
    //     0x14dc1c4: add             x2, x2, HEAP, lsl #32
    // 0x14dc1c8: cmp             w2, NULL
    // 0x14dc1cc: b.ne            #0x14dc1d8
    // 0x14dc1d0: r0 = Null
    //     0x14dc1d0: mov             x0, NULL
    // 0x14dc1d4: b               #0x14dc208
    // 0x14dc1d8: LoadField: r0 = r2->field_b
    //     0x14dc1d8: ldur            w0, [x2, #0xb]
    // 0x14dc1dc: r1 = LoadInt32Instr(r0)
    //     0x14dc1dc: sbfx            x1, x0, #1, #0x1f
    // 0x14dc1e0: mov             x0, x1
    // 0x14dc1e4: r1 = 0
    //     0x14dc1e4: movz            x1, #0
    // 0x14dc1e8: cmp             x1, x0
    // 0x14dc1ec: b.hs            #0x14ddb50
    // 0x14dc1f0: LoadField: r0 = r2->field_f
    //     0x14dc1f0: ldur            w0, [x2, #0xf]
    // 0x14dc1f4: DecompressPointer r0
    //     0x14dc1f4: add             x0, x0, HEAP, lsl #32
    // 0x14dc1f8: LoadField: r1 = r0->field_f
    //     0x14dc1f8: ldur            w1, [x0, #0xf]
    // 0x14dc1fc: DecompressPointer r1
    //     0x14dc1fc: add             x1, x1, HEAP, lsl #32
    // 0x14dc200: LoadField: r0 = r1->field_33
    //     0x14dc200: ldur            w0, [x1, #0x33]
    // 0x14dc204: DecompressPointer r0
    //     0x14dc204: add             x0, x0, HEAP, lsl #32
    // 0x14dc208: cmp             w0, NULL
    // 0x14dc20c: b.ne            #0x14dc214
    // 0x14dc210: r0 = ""
    //     0x14dc210: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc214: ldur            x3, [fp, #-8]
    // 0x14dc218: ldur            x2, [fp, #-0x28]
    // 0x14dc21c: mov             x1, x2
    // 0x14dc220: ArrayStore: r1[1] = r0  ; List_4
    //     0x14dc220: add             x25, x1, #0x13
    //     0x14dc224: str             w0, [x25]
    //     0x14dc228: tbz             w0, #0, #0x14dc244
    //     0x14dc22c: ldurb           w16, [x1, #-1]
    //     0x14dc230: ldurb           w17, [x0, #-1]
    //     0x14dc234: and             x16, x17, x16, lsr #2
    //     0x14dc238: tst             x16, HEAP, lsr #32
    //     0x14dc23c: b.eq            #0x14dc244
    //     0x14dc240: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dc244: r16 = " / Qty: "
    //     0x14dc244: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14dc248: ldr             x16, [x16, #0x760]
    // 0x14dc24c: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dc24c: stur            w16, [x2, #0x17]
    // 0x14dc250: LoadField: r1 = r3->field_f
    //     0x14dc250: ldur            w1, [x3, #0xf]
    // 0x14dc254: DecompressPointer r1
    //     0x14dc254: add             x1, x1, HEAP, lsl #32
    // 0x14dc258: r0 = controller()
    //     0x14dc258: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc25c: LoadField: r1 = r0->field_53
    //     0x14dc25c: ldur            w1, [x0, #0x53]
    // 0x14dc260: DecompressPointer r1
    //     0x14dc260: add             x1, x1, HEAP, lsl #32
    // 0x14dc264: r0 = value()
    //     0x14dc264: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc268: LoadField: r2 = r0->field_5b
    //     0x14dc268: ldur            w2, [x0, #0x5b]
    // 0x14dc26c: DecompressPointer r2
    //     0x14dc26c: add             x2, x2, HEAP, lsl #32
    // 0x14dc270: cmp             w2, NULL
    // 0x14dc274: b.ne            #0x14dc280
    // 0x14dc278: r0 = Null
    //     0x14dc278: mov             x0, NULL
    // 0x14dc27c: b               #0x14dc2b0
    // 0x14dc280: LoadField: r0 = r2->field_b
    //     0x14dc280: ldur            w0, [x2, #0xb]
    // 0x14dc284: r1 = LoadInt32Instr(r0)
    //     0x14dc284: sbfx            x1, x0, #1, #0x1f
    // 0x14dc288: mov             x0, x1
    // 0x14dc28c: r1 = 0
    //     0x14dc28c: movz            x1, #0
    // 0x14dc290: cmp             x1, x0
    // 0x14dc294: b.hs            #0x14ddb54
    // 0x14dc298: LoadField: r0 = r2->field_f
    //     0x14dc298: ldur            w0, [x2, #0xf]
    // 0x14dc29c: DecompressPointer r0
    //     0x14dc29c: add             x0, x0, HEAP, lsl #32
    // 0x14dc2a0: LoadField: r1 = r0->field_f
    //     0x14dc2a0: ldur            w1, [x0, #0xf]
    // 0x14dc2a4: DecompressPointer r1
    //     0x14dc2a4: add             x1, x1, HEAP, lsl #32
    // 0x14dc2a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14dc2a8: ldur            w0, [x1, #0x17]
    // 0x14dc2ac: DecompressPointer r0
    //     0x14dc2ac: add             x0, x0, HEAP, lsl #32
    // 0x14dc2b0: cmp             w0, NULL
    // 0x14dc2b4: b.ne            #0x14dc2bc
    // 0x14dc2b8: r0 = ""
    //     0x14dc2b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc2bc: ldur            x2, [fp, #-8]
    // 0x14dc2c0: ldur            x1, [fp, #-0x28]
    // 0x14dc2c4: ArrayStore: r1[3] = r0  ; List_4
    //     0x14dc2c4: add             x25, x1, #0x1b
    //     0x14dc2c8: str             w0, [x25]
    //     0x14dc2cc: tbz             w0, #0, #0x14dc2e8
    //     0x14dc2d0: ldurb           w16, [x1, #-1]
    //     0x14dc2d4: ldurb           w17, [x0, #-1]
    //     0x14dc2d8: and             x16, x17, x16, lsr #2
    //     0x14dc2dc: tst             x16, HEAP, lsr #32
    //     0x14dc2e0: b.eq            #0x14dc2e8
    //     0x14dc2e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dc2e8: ldur            x16, [fp, #-0x28]
    // 0x14dc2ec: str             x16, [SP]
    // 0x14dc2f0: r0 = _interpolate()
    //     0x14dc2f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14dc2f4: ldur            x2, [fp, #-8]
    // 0x14dc2f8: stur            x0, [fp, #-0x28]
    // 0x14dc2fc: LoadField: r1 = r2->field_13
    //     0x14dc2fc: ldur            w1, [x2, #0x13]
    // 0x14dc300: DecompressPointer r1
    //     0x14dc300: add             x1, x1, HEAP, lsl #32
    // 0x14dc304: r0 = of()
    //     0x14dc304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dc308: LoadField: r1 = r0->field_87
    //     0x14dc308: ldur            w1, [x0, #0x87]
    // 0x14dc30c: DecompressPointer r1
    //     0x14dc30c: add             x1, x1, HEAP, lsl #32
    // 0x14dc310: LoadField: r0 = r1->field_2b
    //     0x14dc310: ldur            w0, [x1, #0x2b]
    // 0x14dc314: DecompressPointer r0
    //     0x14dc314: add             x0, x0, HEAP, lsl #32
    // 0x14dc318: r16 = 12.000000
    //     0x14dc318: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dc31c: ldr             x16, [x16, #0x9e8]
    // 0x14dc320: r30 = Instance_Color
    //     0x14dc320: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dc324: stp             lr, x16, [SP]
    // 0x14dc328: mov             x1, x0
    // 0x14dc32c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14dc32c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14dc330: ldr             x4, [x4, #0xaa0]
    // 0x14dc334: r0 = copyWith()
    //     0x14dc334: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dc338: stur            x0, [fp, #-0x30]
    // 0x14dc33c: r0 = Text()
    //     0x14dc33c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dc340: mov             x1, x0
    // 0x14dc344: ldur            x0, [fp, #-0x28]
    // 0x14dc348: StoreField: r1->field_b = r0
    //     0x14dc348: stur            w0, [x1, #0xb]
    // 0x14dc34c: ldur            x0, [fp, #-0x30]
    // 0x14dc350: StoreField: r1->field_13 = r0
    //     0x14dc350: stur            w0, [x1, #0x13]
    // 0x14dc354: mov             x0, x1
    // 0x14dc358: b               #0x14dc530
    // 0x14dc35c: ldur            x0, [fp, #-8]
    // 0x14dc360: r1 = Null
    //     0x14dc360: mov             x1, NULL
    // 0x14dc364: r2 = 8
    //     0x14dc364: movz            x2, #0x8
    // 0x14dc368: r0 = AllocateArray()
    //     0x14dc368: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dc36c: stur            x0, [fp, #-0x28]
    // 0x14dc370: r16 = "Variant: "
    //     0x14dc370: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x14dc374: ldr             x16, [x16, #0xf08]
    // 0x14dc378: StoreField: r0->field_f = r16
    //     0x14dc378: stur            w16, [x0, #0xf]
    // 0x14dc37c: ldur            x2, [fp, #-8]
    // 0x14dc380: LoadField: r1 = r2->field_f
    //     0x14dc380: ldur            w1, [x2, #0xf]
    // 0x14dc384: DecompressPointer r1
    //     0x14dc384: add             x1, x1, HEAP, lsl #32
    // 0x14dc388: r0 = controller()
    //     0x14dc388: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc38c: LoadField: r1 = r0->field_53
    //     0x14dc38c: ldur            w1, [x0, #0x53]
    // 0x14dc390: DecompressPointer r1
    //     0x14dc390: add             x1, x1, HEAP, lsl #32
    // 0x14dc394: r0 = value()
    //     0x14dc394: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc398: LoadField: r2 = r0->field_5b
    //     0x14dc398: ldur            w2, [x0, #0x5b]
    // 0x14dc39c: DecompressPointer r2
    //     0x14dc39c: add             x2, x2, HEAP, lsl #32
    // 0x14dc3a0: cmp             w2, NULL
    // 0x14dc3a4: b.ne            #0x14dc3b0
    // 0x14dc3a8: r0 = Null
    //     0x14dc3a8: mov             x0, NULL
    // 0x14dc3ac: b               #0x14dc3e0
    // 0x14dc3b0: LoadField: r0 = r2->field_b
    //     0x14dc3b0: ldur            w0, [x2, #0xb]
    // 0x14dc3b4: r1 = LoadInt32Instr(r0)
    //     0x14dc3b4: sbfx            x1, x0, #1, #0x1f
    // 0x14dc3b8: mov             x0, x1
    // 0x14dc3bc: r1 = 0
    //     0x14dc3bc: movz            x1, #0
    // 0x14dc3c0: cmp             x1, x0
    // 0x14dc3c4: b.hs            #0x14ddb58
    // 0x14dc3c8: LoadField: r0 = r2->field_f
    //     0x14dc3c8: ldur            w0, [x2, #0xf]
    // 0x14dc3cc: DecompressPointer r0
    //     0x14dc3cc: add             x0, x0, HEAP, lsl #32
    // 0x14dc3d0: LoadField: r1 = r0->field_f
    //     0x14dc3d0: ldur            w1, [x0, #0xf]
    // 0x14dc3d4: DecompressPointer r1
    //     0x14dc3d4: add             x1, x1, HEAP, lsl #32
    // 0x14dc3d8: LoadField: r0 = r1->field_33
    //     0x14dc3d8: ldur            w0, [x1, #0x33]
    // 0x14dc3dc: DecompressPointer r0
    //     0x14dc3dc: add             x0, x0, HEAP, lsl #32
    // 0x14dc3e0: cmp             w0, NULL
    // 0x14dc3e4: b.ne            #0x14dc3ec
    // 0x14dc3e8: r0 = ""
    //     0x14dc3e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc3ec: ldur            x3, [fp, #-8]
    // 0x14dc3f0: ldur            x2, [fp, #-0x28]
    // 0x14dc3f4: mov             x1, x2
    // 0x14dc3f8: ArrayStore: r1[1] = r0  ; List_4
    //     0x14dc3f8: add             x25, x1, #0x13
    //     0x14dc3fc: str             w0, [x25]
    //     0x14dc400: tbz             w0, #0, #0x14dc41c
    //     0x14dc404: ldurb           w16, [x1, #-1]
    //     0x14dc408: ldurb           w17, [x0, #-1]
    //     0x14dc40c: and             x16, x17, x16, lsr #2
    //     0x14dc410: tst             x16, HEAP, lsr #32
    //     0x14dc414: b.eq            #0x14dc41c
    //     0x14dc418: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dc41c: r16 = " / Qty: "
    //     0x14dc41c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14dc420: ldr             x16, [x16, #0x760]
    // 0x14dc424: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dc424: stur            w16, [x2, #0x17]
    // 0x14dc428: LoadField: r1 = r3->field_f
    //     0x14dc428: ldur            w1, [x3, #0xf]
    // 0x14dc42c: DecompressPointer r1
    //     0x14dc42c: add             x1, x1, HEAP, lsl #32
    // 0x14dc430: r0 = controller()
    //     0x14dc430: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc434: LoadField: r1 = r0->field_53
    //     0x14dc434: ldur            w1, [x0, #0x53]
    // 0x14dc438: DecompressPointer r1
    //     0x14dc438: add             x1, x1, HEAP, lsl #32
    // 0x14dc43c: r0 = value()
    //     0x14dc43c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc440: LoadField: r2 = r0->field_5b
    //     0x14dc440: ldur            w2, [x0, #0x5b]
    // 0x14dc444: DecompressPointer r2
    //     0x14dc444: add             x2, x2, HEAP, lsl #32
    // 0x14dc448: cmp             w2, NULL
    // 0x14dc44c: b.ne            #0x14dc458
    // 0x14dc450: r0 = Null
    //     0x14dc450: mov             x0, NULL
    // 0x14dc454: b               #0x14dc488
    // 0x14dc458: LoadField: r0 = r2->field_b
    //     0x14dc458: ldur            w0, [x2, #0xb]
    // 0x14dc45c: r1 = LoadInt32Instr(r0)
    //     0x14dc45c: sbfx            x1, x0, #1, #0x1f
    // 0x14dc460: mov             x0, x1
    // 0x14dc464: r1 = 0
    //     0x14dc464: movz            x1, #0
    // 0x14dc468: cmp             x1, x0
    // 0x14dc46c: b.hs            #0x14ddb5c
    // 0x14dc470: LoadField: r0 = r2->field_f
    //     0x14dc470: ldur            w0, [x2, #0xf]
    // 0x14dc474: DecompressPointer r0
    //     0x14dc474: add             x0, x0, HEAP, lsl #32
    // 0x14dc478: LoadField: r1 = r0->field_f
    //     0x14dc478: ldur            w1, [x0, #0xf]
    // 0x14dc47c: DecompressPointer r1
    //     0x14dc47c: add             x1, x1, HEAP, lsl #32
    // 0x14dc480: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14dc480: ldur            w0, [x1, #0x17]
    // 0x14dc484: DecompressPointer r0
    //     0x14dc484: add             x0, x0, HEAP, lsl #32
    // 0x14dc488: cmp             w0, NULL
    // 0x14dc48c: b.ne            #0x14dc494
    // 0x14dc490: r0 = ""
    //     0x14dc490: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc494: ldur            x2, [fp, #-8]
    // 0x14dc498: ldur            x1, [fp, #-0x28]
    // 0x14dc49c: ArrayStore: r1[3] = r0  ; List_4
    //     0x14dc49c: add             x25, x1, #0x1b
    //     0x14dc4a0: str             w0, [x25]
    //     0x14dc4a4: tbz             w0, #0, #0x14dc4c0
    //     0x14dc4a8: ldurb           w16, [x1, #-1]
    //     0x14dc4ac: ldurb           w17, [x0, #-1]
    //     0x14dc4b0: and             x16, x17, x16, lsr #2
    //     0x14dc4b4: tst             x16, HEAP, lsr #32
    //     0x14dc4b8: b.eq            #0x14dc4c0
    //     0x14dc4bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dc4c0: ldur            x16, [fp, #-0x28]
    // 0x14dc4c4: str             x16, [SP]
    // 0x14dc4c8: r0 = _interpolate()
    //     0x14dc4c8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14dc4cc: ldur            x2, [fp, #-8]
    // 0x14dc4d0: stur            x0, [fp, #-0x28]
    // 0x14dc4d4: LoadField: r1 = r2->field_13
    //     0x14dc4d4: ldur            w1, [x2, #0x13]
    // 0x14dc4d8: DecompressPointer r1
    //     0x14dc4d8: add             x1, x1, HEAP, lsl #32
    // 0x14dc4dc: r0 = of()
    //     0x14dc4dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dc4e0: LoadField: r1 = r0->field_87
    //     0x14dc4e0: ldur            w1, [x0, #0x87]
    // 0x14dc4e4: DecompressPointer r1
    //     0x14dc4e4: add             x1, x1, HEAP, lsl #32
    // 0x14dc4e8: LoadField: r0 = r1->field_2b
    //     0x14dc4e8: ldur            w0, [x1, #0x2b]
    // 0x14dc4ec: DecompressPointer r0
    //     0x14dc4ec: add             x0, x0, HEAP, lsl #32
    // 0x14dc4f0: r16 = 12.000000
    //     0x14dc4f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dc4f4: ldr             x16, [x16, #0x9e8]
    // 0x14dc4f8: r30 = Instance_Color
    //     0x14dc4f8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dc4fc: stp             lr, x16, [SP]
    // 0x14dc500: mov             x1, x0
    // 0x14dc504: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14dc504: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14dc508: ldr             x4, [x4, #0xaa0]
    // 0x14dc50c: r0 = copyWith()
    //     0x14dc50c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dc510: stur            x0, [fp, #-0x30]
    // 0x14dc514: r0 = Text()
    //     0x14dc514: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dc518: mov             x1, x0
    // 0x14dc51c: ldur            x0, [fp, #-0x28]
    // 0x14dc520: StoreField: r1->field_b = r0
    //     0x14dc520: stur            w0, [x1, #0xb]
    // 0x14dc524: ldur            x0, [fp, #-0x30]
    // 0x14dc528: StoreField: r1->field_13 = r0
    //     0x14dc528: stur            w0, [x1, #0x13]
    // 0x14dc52c: mov             x0, x1
    // 0x14dc530: ldur            x2, [fp, #-8]
    // 0x14dc534: stur            x0, [fp, #-0x28]
    // 0x14dc538: r0 = Padding()
    //     0x14dc538: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dc53c: mov             x2, x0
    // 0x14dc540: r0 = Instance_EdgeInsets
    //     0x14dc540: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14dc544: ldr             x0, [x0, #0x770]
    // 0x14dc548: stur            x2, [fp, #-0x30]
    // 0x14dc54c: StoreField: r2->field_f = r0
    //     0x14dc54c: stur            w0, [x2, #0xf]
    // 0x14dc550: ldur            x1, [fp, #-0x28]
    // 0x14dc554: StoreField: r2->field_b = r1
    //     0x14dc554: stur            w1, [x2, #0xb]
    // 0x14dc558: ldur            x3, [fp, #-8]
    // 0x14dc55c: LoadField: r1 = r3->field_f
    //     0x14dc55c: ldur            w1, [x3, #0xf]
    // 0x14dc560: DecompressPointer r1
    //     0x14dc560: add             x1, x1, HEAP, lsl #32
    // 0x14dc564: r0 = controller()
    //     0x14dc564: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc568: LoadField: r1 = r0->field_53
    //     0x14dc568: ldur            w1, [x0, #0x53]
    // 0x14dc56c: DecompressPointer r1
    //     0x14dc56c: add             x1, x1, HEAP, lsl #32
    // 0x14dc570: r0 = value()
    //     0x14dc570: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc574: LoadField: r2 = r0->field_5b
    //     0x14dc574: ldur            w2, [x0, #0x5b]
    // 0x14dc578: DecompressPointer r2
    //     0x14dc578: add             x2, x2, HEAP, lsl #32
    // 0x14dc57c: cmp             w2, NULL
    // 0x14dc580: b.ne            #0x14dc58c
    // 0x14dc584: r0 = Null
    //     0x14dc584: mov             x0, NULL
    // 0x14dc588: b               #0x14dc5bc
    // 0x14dc58c: LoadField: r0 = r2->field_b
    //     0x14dc58c: ldur            w0, [x2, #0xb]
    // 0x14dc590: r1 = LoadInt32Instr(r0)
    //     0x14dc590: sbfx            x1, x0, #1, #0x1f
    // 0x14dc594: mov             x0, x1
    // 0x14dc598: r1 = 0
    //     0x14dc598: movz            x1, #0
    // 0x14dc59c: cmp             x1, x0
    // 0x14dc5a0: b.hs            #0x14ddb60
    // 0x14dc5a4: LoadField: r0 = r2->field_f
    //     0x14dc5a4: ldur            w0, [x2, #0xf]
    // 0x14dc5a8: DecompressPointer r0
    //     0x14dc5a8: add             x0, x0, HEAP, lsl #32
    // 0x14dc5ac: LoadField: r1 = r0->field_f
    //     0x14dc5ac: ldur            w1, [x0, #0xf]
    // 0x14dc5b0: DecompressPointer r1
    //     0x14dc5b0: add             x1, x1, HEAP, lsl #32
    // 0x14dc5b4: LoadField: r0 = r1->field_2f
    //     0x14dc5b4: ldur            w0, [x1, #0x2f]
    // 0x14dc5b8: DecompressPointer r0
    //     0x14dc5b8: add             x0, x0, HEAP, lsl #32
    // 0x14dc5bc: cmp             w0, NULL
    // 0x14dc5c0: b.ne            #0x14dc5cc
    // 0x14dc5c4: r5 = ""
    //     0x14dc5c4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc5c8: b               #0x14dc5d0
    // 0x14dc5cc: mov             x5, x0
    // 0x14dc5d0: ldur            x2, [fp, #-8]
    // 0x14dc5d4: ldur            x4, [fp, #-0x20]
    // 0x14dc5d8: ldur            x3, [fp, #-0x38]
    // 0x14dc5dc: ldur            x0, [fp, #-0x30]
    // 0x14dc5e0: stur            x5, [fp, #-0x28]
    // 0x14dc5e4: LoadField: r1 = r2->field_13
    //     0x14dc5e4: ldur            w1, [x2, #0x13]
    // 0x14dc5e8: DecompressPointer r1
    //     0x14dc5e8: add             x1, x1, HEAP, lsl #32
    // 0x14dc5ec: r0 = of()
    //     0x14dc5ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dc5f0: LoadField: r1 = r0->field_87
    //     0x14dc5f0: ldur            w1, [x0, #0x87]
    // 0x14dc5f4: DecompressPointer r1
    //     0x14dc5f4: add             x1, x1, HEAP, lsl #32
    // 0x14dc5f8: LoadField: r0 = r1->field_7
    //     0x14dc5f8: ldur            w0, [x1, #7]
    // 0x14dc5fc: DecompressPointer r0
    //     0x14dc5fc: add             x0, x0, HEAP, lsl #32
    // 0x14dc600: r16 = Instance_Color
    //     0x14dc600: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dc604: r30 = 12.000000
    //     0x14dc604: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dc608: ldr             lr, [lr, #0x9e8]
    // 0x14dc60c: stp             lr, x16, [SP]
    // 0x14dc610: mov             x1, x0
    // 0x14dc614: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dc614: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dc618: ldr             x4, [x4, #0x9b8]
    // 0x14dc61c: r0 = copyWith()
    //     0x14dc61c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dc620: stur            x0, [fp, #-0x40]
    // 0x14dc624: r0 = Text()
    //     0x14dc624: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dc628: mov             x3, x0
    // 0x14dc62c: ldur            x0, [fp, #-0x28]
    // 0x14dc630: stur            x3, [fp, #-0x48]
    // 0x14dc634: StoreField: r3->field_b = r0
    //     0x14dc634: stur            w0, [x3, #0xb]
    // 0x14dc638: ldur            x0, [fp, #-0x40]
    // 0x14dc63c: StoreField: r3->field_13 = r0
    //     0x14dc63c: stur            w0, [x3, #0x13]
    // 0x14dc640: r1 = Null
    //     0x14dc640: mov             x1, NULL
    // 0x14dc644: r2 = 8
    //     0x14dc644: movz            x2, #0x8
    // 0x14dc648: r0 = AllocateArray()
    //     0x14dc648: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dc64c: mov             x2, x0
    // 0x14dc650: ldur            x0, [fp, #-0x38]
    // 0x14dc654: stur            x2, [fp, #-0x28]
    // 0x14dc658: StoreField: r2->field_f = r0
    //     0x14dc658: stur            w0, [x2, #0xf]
    // 0x14dc65c: ldur            x0, [fp, #-0x30]
    // 0x14dc660: StoreField: r2->field_13 = r0
    //     0x14dc660: stur            w0, [x2, #0x13]
    // 0x14dc664: r16 = Instance_SizedBox
    //     0x14dc664: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14dc668: ldr             x16, [x16, #0xc70]
    // 0x14dc66c: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dc66c: stur            w16, [x2, #0x17]
    // 0x14dc670: ldur            x0, [fp, #-0x48]
    // 0x14dc674: StoreField: r2->field_1b = r0
    //     0x14dc674: stur            w0, [x2, #0x1b]
    // 0x14dc678: r1 = <Widget>
    //     0x14dc678: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dc67c: r0 = AllocateGrowableArray()
    //     0x14dc67c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dc680: mov             x1, x0
    // 0x14dc684: ldur            x0, [fp, #-0x28]
    // 0x14dc688: stur            x1, [fp, #-0x30]
    // 0x14dc68c: StoreField: r1->field_f = r0
    //     0x14dc68c: stur            w0, [x1, #0xf]
    // 0x14dc690: r2 = 8
    //     0x14dc690: movz            x2, #0x8
    // 0x14dc694: StoreField: r1->field_b = r2
    //     0x14dc694: stur            w2, [x1, #0xb]
    // 0x14dc698: r0 = Column()
    //     0x14dc698: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14dc69c: mov             x2, x0
    // 0x14dc6a0: r0 = Instance_Axis
    //     0x14dc6a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14dc6a4: stur            x2, [fp, #-0x28]
    // 0x14dc6a8: StoreField: r2->field_f = r0
    //     0x14dc6a8: stur            w0, [x2, #0xf]
    // 0x14dc6ac: r3 = Instance_MainAxisAlignment
    //     0x14dc6ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dc6b0: ldr             x3, [x3, #0xa08]
    // 0x14dc6b4: StoreField: r2->field_13 = r3
    //     0x14dc6b4: stur            w3, [x2, #0x13]
    // 0x14dc6b8: r4 = Instance_MainAxisSize
    //     0x14dc6b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dc6bc: ldr             x4, [x4, #0xa10]
    // 0x14dc6c0: ArrayStore: r2[0] = r4  ; List_4
    //     0x14dc6c0: stur            w4, [x2, #0x17]
    // 0x14dc6c4: r5 = Instance_CrossAxisAlignment
    //     0x14dc6c4: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14dc6c8: ldr             x5, [x5, #0x890]
    // 0x14dc6cc: StoreField: r2->field_1b = r5
    //     0x14dc6cc: stur            w5, [x2, #0x1b]
    // 0x14dc6d0: r6 = Instance_VerticalDirection
    //     0x14dc6d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dc6d4: ldr             x6, [x6, #0xa20]
    // 0x14dc6d8: StoreField: r2->field_23 = r6
    //     0x14dc6d8: stur            w6, [x2, #0x23]
    // 0x14dc6dc: r7 = Instance_Clip
    //     0x14dc6dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dc6e0: ldr             x7, [x7, #0x38]
    // 0x14dc6e4: StoreField: r2->field_2b = r7
    //     0x14dc6e4: stur            w7, [x2, #0x2b]
    // 0x14dc6e8: StoreField: r2->field_2f = rZR
    //     0x14dc6e8: stur            xzr, [x2, #0x2f]
    // 0x14dc6ec: ldur            x1, [fp, #-0x30]
    // 0x14dc6f0: StoreField: r2->field_b = r1
    //     0x14dc6f0: stur            w1, [x2, #0xb]
    // 0x14dc6f4: r1 = <FlexParentData>
    //     0x14dc6f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14dc6f8: ldr             x1, [x1, #0xe00]
    // 0x14dc6fc: r0 = Expanded()
    //     0x14dc6fc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14dc700: mov             x3, x0
    // 0x14dc704: r0 = 1
    //     0x14dc704: movz            x0, #0x1
    // 0x14dc708: stur            x3, [fp, #-0x30]
    // 0x14dc70c: StoreField: r3->field_13 = r0
    //     0x14dc70c: stur            x0, [x3, #0x13]
    // 0x14dc710: r4 = Instance_FlexFit
    //     0x14dc710: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14dc714: ldr             x4, [x4, #0xe08]
    // 0x14dc718: StoreField: r3->field_1b = r4
    //     0x14dc718: stur            w4, [x3, #0x1b]
    // 0x14dc71c: ldur            x1, [fp, #-0x28]
    // 0x14dc720: StoreField: r3->field_b = r1
    //     0x14dc720: stur            w1, [x3, #0xb]
    // 0x14dc724: r1 = Null
    //     0x14dc724: mov             x1, NULL
    // 0x14dc728: r2 = 6
    //     0x14dc728: movz            x2, #0x6
    // 0x14dc72c: r0 = AllocateArray()
    //     0x14dc72c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dc730: mov             x2, x0
    // 0x14dc734: ldur            x0, [fp, #-0x20]
    // 0x14dc738: stur            x2, [fp, #-0x28]
    // 0x14dc73c: StoreField: r2->field_f = r0
    //     0x14dc73c: stur            w0, [x2, #0xf]
    // 0x14dc740: r16 = Instance_SizedBox
    //     0x14dc740: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14dc744: ldr             x16, [x16, #0xb20]
    // 0x14dc748: StoreField: r2->field_13 = r16
    //     0x14dc748: stur            w16, [x2, #0x13]
    // 0x14dc74c: ldur            x0, [fp, #-0x30]
    // 0x14dc750: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dc750: stur            w0, [x2, #0x17]
    // 0x14dc754: r1 = <Widget>
    //     0x14dc754: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dc758: r0 = AllocateGrowableArray()
    //     0x14dc758: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dc75c: mov             x1, x0
    // 0x14dc760: ldur            x0, [fp, #-0x28]
    // 0x14dc764: stur            x1, [fp, #-0x20]
    // 0x14dc768: StoreField: r1->field_f = r0
    //     0x14dc768: stur            w0, [x1, #0xf]
    // 0x14dc76c: r2 = 6
    //     0x14dc76c: movz            x2, #0x6
    // 0x14dc770: StoreField: r1->field_b = r2
    //     0x14dc770: stur            w2, [x1, #0xb]
    // 0x14dc774: r0 = Row()
    //     0x14dc774: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14dc778: mov             x1, x0
    // 0x14dc77c: r0 = Instance_Axis
    //     0x14dc77c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14dc780: stur            x1, [fp, #-0x28]
    // 0x14dc784: StoreField: r1->field_f = r0
    //     0x14dc784: stur            w0, [x1, #0xf]
    // 0x14dc788: r2 = Instance_MainAxisAlignment
    //     0x14dc788: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dc78c: ldr             x2, [x2, #0xa08]
    // 0x14dc790: StoreField: r1->field_13 = r2
    //     0x14dc790: stur            w2, [x1, #0x13]
    // 0x14dc794: r3 = Instance_MainAxisSize
    //     0x14dc794: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dc798: ldr             x3, [x3, #0xa10]
    // 0x14dc79c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14dc79c: stur            w3, [x1, #0x17]
    // 0x14dc7a0: r4 = Instance_CrossAxisAlignment
    //     0x14dc7a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14dc7a4: ldr             x4, [x4, #0xa18]
    // 0x14dc7a8: StoreField: r1->field_1b = r4
    //     0x14dc7a8: stur            w4, [x1, #0x1b]
    // 0x14dc7ac: r5 = Instance_VerticalDirection
    //     0x14dc7ac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dc7b0: ldr             x5, [x5, #0xa20]
    // 0x14dc7b4: StoreField: r1->field_23 = r5
    //     0x14dc7b4: stur            w5, [x1, #0x23]
    // 0x14dc7b8: r6 = Instance_Clip
    //     0x14dc7b8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dc7bc: ldr             x6, [x6, #0x38]
    // 0x14dc7c0: StoreField: r1->field_2b = r6
    //     0x14dc7c0: stur            w6, [x1, #0x2b]
    // 0x14dc7c4: StoreField: r1->field_2f = rZR
    //     0x14dc7c4: stur            xzr, [x1, #0x2f]
    // 0x14dc7c8: ldur            x7, [fp, #-0x20]
    // 0x14dc7cc: StoreField: r1->field_b = r7
    //     0x14dc7cc: stur            w7, [x1, #0xb]
    // 0x14dc7d0: r0 = Padding()
    //     0x14dc7d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dc7d4: mov             x1, x0
    // 0x14dc7d8: r0 = Instance_EdgeInsets
    //     0x14dc7d8: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0x14dc7dc: ldr             x0, [x0, #0xb8]
    // 0x14dc7e0: stur            x1, [fp, #-0x20]
    // 0x14dc7e4: StoreField: r1->field_f = r0
    //     0x14dc7e4: stur            w0, [x1, #0xf]
    // 0x14dc7e8: ldur            x0, [fp, #-0x28]
    // 0x14dc7ec: StoreField: r1->field_b = r0
    //     0x14dc7ec: stur            w0, [x1, #0xb]
    // 0x14dc7f0: r0 = Container()
    //     0x14dc7f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dc7f4: stur            x0, [fp, #-0x28]
    // 0x14dc7f8: r16 = Instance_BoxDecoration
    //     0x14dc7f8: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fc18] Obj!BoxDecoration@d64da1
    //     0x14dc7fc: ldr             x16, [x16, #0xc18]
    // 0x14dc800: ldur            lr, [fp, #-0x20]
    // 0x14dc804: stp             lr, x16, [SP]
    // 0x14dc808: mov             x1, x0
    // 0x14dc80c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14dc80c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14dc810: ldr             x4, [x4, #0x88]
    // 0x14dc814: r0 = Container()
    //     0x14dc814: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dc818: r1 = Instance_Color
    //     0x14dc818: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dc81c: d0 = 0.100000
    //     0x14dc81c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14dc820: r0 = withOpacity()
    //     0x14dc820: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14dc824: mov             x2, x0
    // 0x14dc828: r1 = Null
    //     0x14dc828: mov             x1, NULL
    // 0x14dc82c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14dc82c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14dc830: r0 = Border.all()
    //     0x14dc830: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14dc834: stur            x0, [fp, #-0x20]
    // 0x14dc838: r0 = BoxDecoration()
    //     0x14dc838: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14dc83c: mov             x1, x0
    // 0x14dc840: r0 = Instance_Color
    //     0x14dc840: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dc844: stur            x1, [fp, #-0x30]
    // 0x14dc848: StoreField: r1->field_7 = r0
    //     0x14dc848: stur            w0, [x1, #7]
    // 0x14dc84c: ldur            x0, [fp, #-0x20]
    // 0x14dc850: StoreField: r1->field_f = r0
    //     0x14dc850: stur            w0, [x1, #0xf]
    // 0x14dc854: r0 = Instance_BorderRadius
    //     0x14dc854: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fbe0] Obj!BorderRadius@d5a561
    //     0x14dc858: ldr             x0, [x0, #0xbe0]
    // 0x14dc85c: StoreField: r1->field_13 = r0
    //     0x14dc85c: stur            w0, [x1, #0x13]
    // 0x14dc860: r0 = Instance_BoxShape
    //     0x14dc860: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14dc864: ldr             x0, [x0, #0x80]
    // 0x14dc868: StoreField: r1->field_23 = r0
    //     0x14dc868: stur            w0, [x1, #0x23]
    // 0x14dc86c: r0 = Radius()
    //     0x14dc86c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14dc870: d0 = 12.000000
    //     0x14dc870: fmov            d0, #12.00000000
    // 0x14dc874: stur            x0, [fp, #-0x20]
    // 0x14dc878: StoreField: r0->field_7 = d0
    //     0x14dc878: stur            d0, [x0, #7]
    // 0x14dc87c: StoreField: r0->field_f = d0
    //     0x14dc87c: stur            d0, [x0, #0xf]
    // 0x14dc880: r0 = BorderRadius()
    //     0x14dc880: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14dc884: mov             x2, x0
    // 0x14dc888: ldur            x0, [fp, #-0x20]
    // 0x14dc88c: stur            x2, [fp, #-0x38]
    // 0x14dc890: StoreField: r2->field_7 = r0
    //     0x14dc890: stur            w0, [x2, #7]
    // 0x14dc894: StoreField: r2->field_b = r0
    //     0x14dc894: stur            w0, [x2, #0xb]
    // 0x14dc898: StoreField: r2->field_f = r0
    //     0x14dc898: stur            w0, [x2, #0xf]
    // 0x14dc89c: StoreField: r2->field_13 = r0
    //     0x14dc89c: stur            w0, [x2, #0x13]
    // 0x14dc8a0: ldur            x0, [fp, #-8]
    // 0x14dc8a4: LoadField: r1 = r0->field_f
    //     0x14dc8a4: ldur            w1, [x0, #0xf]
    // 0x14dc8a8: DecompressPointer r1
    //     0x14dc8a8: add             x1, x1, HEAP, lsl #32
    // 0x14dc8ac: r0 = controller()
    //     0x14dc8ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc8b0: LoadField: r1 = r0->field_53
    //     0x14dc8b0: ldur            w1, [x0, #0x53]
    // 0x14dc8b4: DecompressPointer r1
    //     0x14dc8b4: add             x1, x1, HEAP, lsl #32
    // 0x14dc8b8: r0 = value()
    //     0x14dc8b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc8bc: LoadField: r2 = r0->field_1b
    //     0x14dc8bc: ldur            w2, [x0, #0x1b]
    // 0x14dc8c0: DecompressPointer r2
    //     0x14dc8c0: add             x2, x2, HEAP, lsl #32
    // 0x14dc8c4: cmp             w2, NULL
    // 0x14dc8c8: b.ne            #0x14dc8d4
    // 0x14dc8cc: r0 = Null
    //     0x14dc8cc: mov             x0, NULL
    // 0x14dc8d0: b               #0x14dc904
    // 0x14dc8d4: LoadField: r0 = r2->field_b
    //     0x14dc8d4: ldur            w0, [x2, #0xb]
    // 0x14dc8d8: r1 = LoadInt32Instr(r0)
    //     0x14dc8d8: sbfx            x1, x0, #1, #0x1f
    // 0x14dc8dc: mov             x0, x1
    // 0x14dc8e0: r1 = 0
    //     0x14dc8e0: movz            x1, #0
    // 0x14dc8e4: cmp             x1, x0
    // 0x14dc8e8: b.hs            #0x14ddb64
    // 0x14dc8ec: LoadField: r0 = r2->field_f
    //     0x14dc8ec: ldur            w0, [x2, #0xf]
    // 0x14dc8f0: DecompressPointer r0
    //     0x14dc8f0: add             x0, x0, HEAP, lsl #32
    // 0x14dc8f4: LoadField: r1 = r0->field_f
    //     0x14dc8f4: ldur            w1, [x0, #0xf]
    // 0x14dc8f8: DecompressPointer r1
    //     0x14dc8f8: add             x1, x1, HEAP, lsl #32
    // 0x14dc8fc: LoadField: r0 = r1->field_13
    //     0x14dc8fc: ldur            w0, [x1, #0x13]
    // 0x14dc900: DecompressPointer r0
    //     0x14dc900: add             x0, x0, HEAP, lsl #32
    // 0x14dc904: cmp             w0, NULL
    // 0x14dc908: b.ne            #0x14dc914
    // 0x14dc90c: r4 = ""
    //     0x14dc90c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dc910: b               #0x14dc918
    // 0x14dc914: mov             x4, x0
    // 0x14dc918: ldur            x3, [fp, #-8]
    // 0x14dc91c: ldur            x0, [fp, #-0x38]
    // 0x14dc920: stur            x4, [fp, #-0x20]
    // 0x14dc924: r1 = Function '<anonymous closure>':.
    //     0x14dc924: add             x1, PP, #0x40, lsl #12  ; [pp+0x409c8] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x14dc928: ldr             x1, [x1, #0x9c8]
    // 0x14dc92c: r2 = Null
    //     0x14dc92c: mov             x2, NULL
    // 0x14dc930: r0 = AllocateClosure()
    //     0x14dc930: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dc934: stur            x0, [fp, #-0x40]
    // 0x14dc938: r0 = CachedNetworkImage()
    //     0x14dc938: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14dc93c: stur            x0, [fp, #-0x48]
    // 0x14dc940: r16 = Instance_BoxFit
    //     0x14dc940: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14dc944: ldr             x16, [x16, #0x118]
    // 0x14dc948: r30 = 80.000000
    //     0x14dc948: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14dc94c: ldr             lr, [lr, #0x2f8]
    // 0x14dc950: stp             lr, x16, [SP, #0x10]
    // 0x14dc954: r16 = 80.000000
    //     0x14dc954: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14dc958: ldr             x16, [x16, #0x2f8]
    // 0x14dc95c: ldur            lr, [fp, #-0x40]
    // 0x14dc960: stp             lr, x16, [SP]
    // 0x14dc964: mov             x1, x0
    // 0x14dc968: ldur            x2, [fp, #-0x20]
    // 0x14dc96c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x14dc96c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fbf8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x14dc970: ldr             x4, [x4, #0xbf8]
    // 0x14dc974: r0 = CachedNetworkImage()
    //     0x14dc974: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14dc978: r0 = ClipRRect()
    //     0x14dc978: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14dc97c: mov             x2, x0
    // 0x14dc980: ldur            x0, [fp, #-0x38]
    // 0x14dc984: stur            x2, [fp, #-0x20]
    // 0x14dc988: StoreField: r2->field_f = r0
    //     0x14dc988: stur            w0, [x2, #0xf]
    // 0x14dc98c: r0 = Instance_Clip
    //     0x14dc98c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14dc990: ldr             x0, [x0, #0x138]
    // 0x14dc994: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dc994: stur            w0, [x2, #0x17]
    // 0x14dc998: ldur            x0, [fp, #-0x48]
    // 0x14dc99c: StoreField: r2->field_b = r0
    //     0x14dc99c: stur            w0, [x2, #0xb]
    // 0x14dc9a0: ldur            x0, [fp, #-8]
    // 0x14dc9a4: LoadField: r1 = r0->field_f
    //     0x14dc9a4: ldur            w1, [x0, #0xf]
    // 0x14dc9a8: DecompressPointer r1
    //     0x14dc9a8: add             x1, x1, HEAP, lsl #32
    // 0x14dc9ac: r0 = controller()
    //     0x14dc9ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dc9b0: LoadField: r1 = r0->field_53
    //     0x14dc9b0: ldur            w1, [x0, #0x53]
    // 0x14dc9b4: DecompressPointer r1
    //     0x14dc9b4: add             x1, x1, HEAP, lsl #32
    // 0x14dc9b8: r0 = value()
    //     0x14dc9b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dc9bc: LoadField: r2 = r0->field_1b
    //     0x14dc9bc: ldur            w2, [x0, #0x1b]
    // 0x14dc9c0: DecompressPointer r2
    //     0x14dc9c0: add             x2, x2, HEAP, lsl #32
    // 0x14dc9c4: cmp             w2, NULL
    // 0x14dc9c8: b.ne            #0x14dc9d4
    // 0x14dc9cc: r0 = Null
    //     0x14dc9cc: mov             x0, NULL
    // 0x14dc9d0: b               #0x14dca04
    // 0x14dc9d4: LoadField: r0 = r2->field_b
    //     0x14dc9d4: ldur            w0, [x2, #0xb]
    // 0x14dc9d8: r1 = LoadInt32Instr(r0)
    //     0x14dc9d8: sbfx            x1, x0, #1, #0x1f
    // 0x14dc9dc: mov             x0, x1
    // 0x14dc9e0: r1 = 0
    //     0x14dc9e0: movz            x1, #0
    // 0x14dc9e4: cmp             x1, x0
    // 0x14dc9e8: b.hs            #0x14ddb68
    // 0x14dc9ec: LoadField: r0 = r2->field_f
    //     0x14dc9ec: ldur            w0, [x2, #0xf]
    // 0x14dc9f0: DecompressPointer r0
    //     0x14dc9f0: add             x0, x0, HEAP, lsl #32
    // 0x14dc9f4: LoadField: r1 = r0->field_f
    //     0x14dc9f4: ldur            w1, [x0, #0xf]
    // 0x14dc9f8: DecompressPointer r1
    //     0x14dc9f8: add             x1, x1, HEAP, lsl #32
    // 0x14dc9fc: LoadField: r0 = r1->field_f
    //     0x14dc9fc: ldur            w0, [x1, #0xf]
    // 0x14dca00: DecompressPointer r0
    //     0x14dca00: add             x0, x0, HEAP, lsl #32
    // 0x14dca04: cmp             w0, NULL
    // 0x14dca08: b.ne            #0x14dca10
    // 0x14dca0c: r0 = ""
    //     0x14dca0c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dca10: ldur            x2, [fp, #-8]
    // 0x14dca14: stur            x0, [fp, #-0x38]
    // 0x14dca18: LoadField: r1 = r2->field_13
    //     0x14dca18: ldur            w1, [x2, #0x13]
    // 0x14dca1c: DecompressPointer r1
    //     0x14dca1c: add             x1, x1, HEAP, lsl #32
    // 0x14dca20: r0 = of()
    //     0x14dca20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dca24: LoadField: r1 = r0->field_87
    //     0x14dca24: ldur            w1, [x0, #0x87]
    // 0x14dca28: DecompressPointer r1
    //     0x14dca28: add             x1, x1, HEAP, lsl #32
    // 0x14dca2c: LoadField: r0 = r1->field_7
    //     0x14dca2c: ldur            w0, [x1, #7]
    // 0x14dca30: DecompressPointer r0
    //     0x14dca30: add             x0, x0, HEAP, lsl #32
    // 0x14dca34: r16 = Instance_Color
    //     0x14dca34: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dca38: r30 = 12.000000
    //     0x14dca38: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dca3c: ldr             lr, [lr, #0x9e8]
    // 0x14dca40: stp             lr, x16, [SP]
    // 0x14dca44: mov             x1, x0
    // 0x14dca48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dca48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dca4c: ldr             x4, [x4, #0x9b8]
    // 0x14dca50: r0 = copyWith()
    //     0x14dca50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dca54: stur            x0, [fp, #-0x40]
    // 0x14dca58: r0 = Text()
    //     0x14dca58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dca5c: mov             x2, x0
    // 0x14dca60: ldur            x0, [fp, #-0x38]
    // 0x14dca64: stur            x2, [fp, #-0x48]
    // 0x14dca68: StoreField: r2->field_b = r0
    //     0x14dca68: stur            w0, [x2, #0xb]
    // 0x14dca6c: ldur            x0, [fp, #-0x40]
    // 0x14dca70: StoreField: r2->field_13 = r0
    //     0x14dca70: stur            w0, [x2, #0x13]
    // 0x14dca74: r0 = 2
    //     0x14dca74: movz            x0, #0x2
    // 0x14dca78: StoreField: r2->field_37 = r0
    //     0x14dca78: stur            w0, [x2, #0x37]
    // 0x14dca7c: ldur            x0, [fp, #-8]
    // 0x14dca80: LoadField: r1 = r0->field_f
    //     0x14dca80: ldur            w1, [x0, #0xf]
    // 0x14dca84: DecompressPointer r1
    //     0x14dca84: add             x1, x1, HEAP, lsl #32
    // 0x14dca88: r0 = controller()
    //     0x14dca88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dca8c: LoadField: r1 = r0->field_53
    //     0x14dca8c: ldur            w1, [x0, #0x53]
    // 0x14dca90: DecompressPointer r1
    //     0x14dca90: add             x1, x1, HEAP, lsl #32
    // 0x14dca94: r0 = value()
    //     0x14dca94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dca98: LoadField: r2 = r0->field_1b
    //     0x14dca98: ldur            w2, [x0, #0x1b]
    // 0x14dca9c: DecompressPointer r2
    //     0x14dca9c: add             x2, x2, HEAP, lsl #32
    // 0x14dcaa0: cmp             w2, NULL
    // 0x14dcaa4: b.ne            #0x14dcab0
    // 0x14dcaa8: r0 = Null
    //     0x14dcaa8: mov             x0, NULL
    // 0x14dcaac: b               #0x14dcae0
    // 0x14dcab0: LoadField: r0 = r2->field_b
    //     0x14dcab0: ldur            w0, [x2, #0xb]
    // 0x14dcab4: r1 = LoadInt32Instr(r0)
    //     0x14dcab4: sbfx            x1, x0, #1, #0x1f
    // 0x14dcab8: mov             x0, x1
    // 0x14dcabc: r1 = 0
    //     0x14dcabc: movz            x1, #0
    // 0x14dcac0: cmp             x1, x0
    // 0x14dcac4: b.hs            #0x14ddb6c
    // 0x14dcac8: LoadField: r0 = r2->field_f
    //     0x14dcac8: ldur            w0, [x2, #0xf]
    // 0x14dcacc: DecompressPointer r0
    //     0x14dcacc: add             x0, x0, HEAP, lsl #32
    // 0x14dcad0: LoadField: r1 = r0->field_f
    //     0x14dcad0: ldur            w1, [x0, #0xf]
    // 0x14dcad4: DecompressPointer r1
    //     0x14dcad4: add             x1, x1, HEAP, lsl #32
    // 0x14dcad8: LoadField: r0 = r1->field_13
    //     0x14dcad8: ldur            w0, [x1, #0x13]
    // 0x14dcadc: DecompressPointer r0
    //     0x14dcadc: add             x0, x0, HEAP, lsl #32
    // 0x14dcae0: r1 = LoadClassIdInstr(r0)
    //     0x14dcae0: ldur            x1, [x0, #-1]
    //     0x14dcae4: ubfx            x1, x1, #0xc, #0x14
    // 0x14dcae8: r16 = "size"
    //     0x14dcae8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14dcaec: ldr             x16, [x16, #0x9c0]
    // 0x14dcaf0: stp             x16, x0, [SP]
    // 0x14dcaf4: mov             x0, x1
    // 0x14dcaf8: mov             lr, x0
    // 0x14dcafc: ldr             lr, [x21, lr, lsl #3]
    // 0x14dcb00: blr             lr
    // 0x14dcb04: tbnz            w0, #4, #0x14dcce0
    // 0x14dcb08: ldur            x0, [fp, #-8]
    // 0x14dcb0c: r1 = Null
    //     0x14dcb0c: mov             x1, NULL
    // 0x14dcb10: r2 = 8
    //     0x14dcb10: movz            x2, #0x8
    // 0x14dcb14: r0 = AllocateArray()
    //     0x14dcb14: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dcb18: stur            x0, [fp, #-0x38]
    // 0x14dcb1c: r16 = "Size: "
    //     0x14dcb1c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x14dcb20: ldr             x16, [x16, #0xf00]
    // 0x14dcb24: StoreField: r0->field_f = r16
    //     0x14dcb24: stur            w16, [x0, #0xf]
    // 0x14dcb28: ldur            x2, [fp, #-8]
    // 0x14dcb2c: LoadField: r1 = r2->field_f
    //     0x14dcb2c: ldur            w1, [x2, #0xf]
    // 0x14dcb30: DecompressPointer r1
    //     0x14dcb30: add             x1, x1, HEAP, lsl #32
    // 0x14dcb34: r0 = controller()
    //     0x14dcb34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dcb38: LoadField: r1 = r0->field_53
    //     0x14dcb38: ldur            w1, [x0, #0x53]
    // 0x14dcb3c: DecompressPointer r1
    //     0x14dcb3c: add             x1, x1, HEAP, lsl #32
    // 0x14dcb40: r0 = value()
    //     0x14dcb40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcb44: LoadField: r2 = r0->field_1b
    //     0x14dcb44: ldur            w2, [x0, #0x1b]
    // 0x14dcb48: DecompressPointer r2
    //     0x14dcb48: add             x2, x2, HEAP, lsl #32
    // 0x14dcb4c: cmp             w2, NULL
    // 0x14dcb50: b.ne            #0x14dcb5c
    // 0x14dcb54: r0 = Null
    //     0x14dcb54: mov             x0, NULL
    // 0x14dcb58: b               #0x14dcb8c
    // 0x14dcb5c: LoadField: r0 = r2->field_b
    //     0x14dcb5c: ldur            w0, [x2, #0xb]
    // 0x14dcb60: r1 = LoadInt32Instr(r0)
    //     0x14dcb60: sbfx            x1, x0, #1, #0x1f
    // 0x14dcb64: mov             x0, x1
    // 0x14dcb68: r1 = 0
    //     0x14dcb68: movz            x1, #0
    // 0x14dcb6c: cmp             x1, x0
    // 0x14dcb70: b.hs            #0x14ddb70
    // 0x14dcb74: LoadField: r0 = r2->field_f
    //     0x14dcb74: ldur            w0, [x2, #0xf]
    // 0x14dcb78: DecompressPointer r0
    //     0x14dcb78: add             x0, x0, HEAP, lsl #32
    // 0x14dcb7c: LoadField: r1 = r0->field_f
    //     0x14dcb7c: ldur            w1, [x0, #0xf]
    // 0x14dcb80: DecompressPointer r1
    //     0x14dcb80: add             x1, x1, HEAP, lsl #32
    // 0x14dcb84: LoadField: r0 = r1->field_33
    //     0x14dcb84: ldur            w0, [x1, #0x33]
    // 0x14dcb88: DecompressPointer r0
    //     0x14dcb88: add             x0, x0, HEAP, lsl #32
    // 0x14dcb8c: cmp             w0, NULL
    // 0x14dcb90: b.ne            #0x14dcb98
    // 0x14dcb94: r0 = ""
    //     0x14dcb94: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dcb98: ldur            x3, [fp, #-8]
    // 0x14dcb9c: ldur            x2, [fp, #-0x38]
    // 0x14dcba0: mov             x1, x2
    // 0x14dcba4: ArrayStore: r1[1] = r0  ; List_4
    //     0x14dcba4: add             x25, x1, #0x13
    //     0x14dcba8: str             w0, [x25]
    //     0x14dcbac: tbz             w0, #0, #0x14dcbc8
    //     0x14dcbb0: ldurb           w16, [x1, #-1]
    //     0x14dcbb4: ldurb           w17, [x0, #-1]
    //     0x14dcbb8: and             x16, x17, x16, lsr #2
    //     0x14dcbbc: tst             x16, HEAP, lsr #32
    //     0x14dcbc0: b.eq            #0x14dcbc8
    //     0x14dcbc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dcbc8: r16 = " / Qty: "
    //     0x14dcbc8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14dcbcc: ldr             x16, [x16, #0x760]
    // 0x14dcbd0: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dcbd0: stur            w16, [x2, #0x17]
    // 0x14dcbd4: LoadField: r1 = r3->field_f
    //     0x14dcbd4: ldur            w1, [x3, #0xf]
    // 0x14dcbd8: DecompressPointer r1
    //     0x14dcbd8: add             x1, x1, HEAP, lsl #32
    // 0x14dcbdc: r0 = controller()
    //     0x14dcbdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dcbe0: LoadField: r1 = r0->field_53
    //     0x14dcbe0: ldur            w1, [x0, #0x53]
    // 0x14dcbe4: DecompressPointer r1
    //     0x14dcbe4: add             x1, x1, HEAP, lsl #32
    // 0x14dcbe8: r0 = value()
    //     0x14dcbe8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcbec: LoadField: r2 = r0->field_1b
    //     0x14dcbec: ldur            w2, [x0, #0x1b]
    // 0x14dcbf0: DecompressPointer r2
    //     0x14dcbf0: add             x2, x2, HEAP, lsl #32
    // 0x14dcbf4: cmp             w2, NULL
    // 0x14dcbf8: b.ne            #0x14dcc04
    // 0x14dcbfc: r0 = Null
    //     0x14dcbfc: mov             x0, NULL
    // 0x14dcc00: b               #0x14dcc34
    // 0x14dcc04: LoadField: r0 = r2->field_b
    //     0x14dcc04: ldur            w0, [x2, #0xb]
    // 0x14dcc08: r1 = LoadInt32Instr(r0)
    //     0x14dcc08: sbfx            x1, x0, #1, #0x1f
    // 0x14dcc0c: mov             x0, x1
    // 0x14dcc10: r1 = 0
    //     0x14dcc10: movz            x1, #0
    // 0x14dcc14: cmp             x1, x0
    // 0x14dcc18: b.hs            #0x14ddb74
    // 0x14dcc1c: LoadField: r0 = r2->field_f
    //     0x14dcc1c: ldur            w0, [x2, #0xf]
    // 0x14dcc20: DecompressPointer r0
    //     0x14dcc20: add             x0, x0, HEAP, lsl #32
    // 0x14dcc24: LoadField: r1 = r0->field_f
    //     0x14dcc24: ldur            w1, [x0, #0xf]
    // 0x14dcc28: DecompressPointer r1
    //     0x14dcc28: add             x1, x1, HEAP, lsl #32
    // 0x14dcc2c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14dcc2c: ldur            w0, [x1, #0x17]
    // 0x14dcc30: DecompressPointer r0
    //     0x14dcc30: add             x0, x0, HEAP, lsl #32
    // 0x14dcc34: cmp             w0, NULL
    // 0x14dcc38: b.ne            #0x14dcc40
    // 0x14dcc3c: r0 = ""
    //     0x14dcc3c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dcc40: ldur            x2, [fp, #-8]
    // 0x14dcc44: ldur            x1, [fp, #-0x38]
    // 0x14dcc48: ArrayStore: r1[3] = r0  ; List_4
    //     0x14dcc48: add             x25, x1, #0x1b
    //     0x14dcc4c: str             w0, [x25]
    //     0x14dcc50: tbz             w0, #0, #0x14dcc6c
    //     0x14dcc54: ldurb           w16, [x1, #-1]
    //     0x14dcc58: ldurb           w17, [x0, #-1]
    //     0x14dcc5c: and             x16, x17, x16, lsr #2
    //     0x14dcc60: tst             x16, HEAP, lsr #32
    //     0x14dcc64: b.eq            #0x14dcc6c
    //     0x14dcc68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dcc6c: ldur            x16, [fp, #-0x38]
    // 0x14dcc70: str             x16, [SP]
    // 0x14dcc74: r0 = _interpolate()
    //     0x14dcc74: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14dcc78: ldur            x2, [fp, #-8]
    // 0x14dcc7c: stur            x0, [fp, #-0x38]
    // 0x14dcc80: LoadField: r1 = r2->field_13
    //     0x14dcc80: ldur            w1, [x2, #0x13]
    // 0x14dcc84: DecompressPointer r1
    //     0x14dcc84: add             x1, x1, HEAP, lsl #32
    // 0x14dcc88: r0 = of()
    //     0x14dcc88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dcc8c: LoadField: r1 = r0->field_87
    //     0x14dcc8c: ldur            w1, [x0, #0x87]
    // 0x14dcc90: DecompressPointer r1
    //     0x14dcc90: add             x1, x1, HEAP, lsl #32
    // 0x14dcc94: LoadField: r0 = r1->field_2b
    //     0x14dcc94: ldur            w0, [x1, #0x2b]
    // 0x14dcc98: DecompressPointer r0
    //     0x14dcc98: add             x0, x0, HEAP, lsl #32
    // 0x14dcc9c: r16 = 12.000000
    //     0x14dcc9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dcca0: ldr             x16, [x16, #0x9e8]
    // 0x14dcca4: r30 = Instance_Color
    //     0x14dcca4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dcca8: stp             lr, x16, [SP]
    // 0x14dccac: mov             x1, x0
    // 0x14dccb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14dccb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14dccb4: ldr             x4, [x4, #0xaa0]
    // 0x14dccb8: r0 = copyWith()
    //     0x14dccb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dccbc: stur            x0, [fp, #-0x40]
    // 0x14dccc0: r0 = Text()
    //     0x14dccc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dccc4: mov             x1, x0
    // 0x14dccc8: ldur            x0, [fp, #-0x38]
    // 0x14dcccc: StoreField: r1->field_b = r0
    //     0x14dcccc: stur            w0, [x1, #0xb]
    // 0x14dccd0: ldur            x0, [fp, #-0x40]
    // 0x14dccd4: StoreField: r1->field_13 = r0
    //     0x14dccd4: stur            w0, [x1, #0x13]
    // 0x14dccd8: mov             x0, x1
    // 0x14dccdc: b               #0x14dceb4
    // 0x14dcce0: ldur            x0, [fp, #-8]
    // 0x14dcce4: r1 = Null
    //     0x14dcce4: mov             x1, NULL
    // 0x14dcce8: r2 = 8
    //     0x14dcce8: movz            x2, #0x8
    // 0x14dccec: r0 = AllocateArray()
    //     0x14dccec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dccf0: stur            x0, [fp, #-0x38]
    // 0x14dccf4: r16 = "Variant: "
    //     0x14dccf4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x14dccf8: ldr             x16, [x16, #0xf08]
    // 0x14dccfc: StoreField: r0->field_f = r16
    //     0x14dccfc: stur            w16, [x0, #0xf]
    // 0x14dcd00: ldur            x2, [fp, #-8]
    // 0x14dcd04: LoadField: r1 = r2->field_f
    //     0x14dcd04: ldur            w1, [x2, #0xf]
    // 0x14dcd08: DecompressPointer r1
    //     0x14dcd08: add             x1, x1, HEAP, lsl #32
    // 0x14dcd0c: r0 = controller()
    //     0x14dcd0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dcd10: LoadField: r1 = r0->field_53
    //     0x14dcd10: ldur            w1, [x0, #0x53]
    // 0x14dcd14: DecompressPointer r1
    //     0x14dcd14: add             x1, x1, HEAP, lsl #32
    // 0x14dcd18: r0 = value()
    //     0x14dcd18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcd1c: LoadField: r2 = r0->field_1b
    //     0x14dcd1c: ldur            w2, [x0, #0x1b]
    // 0x14dcd20: DecompressPointer r2
    //     0x14dcd20: add             x2, x2, HEAP, lsl #32
    // 0x14dcd24: cmp             w2, NULL
    // 0x14dcd28: b.ne            #0x14dcd34
    // 0x14dcd2c: r0 = Null
    //     0x14dcd2c: mov             x0, NULL
    // 0x14dcd30: b               #0x14dcd64
    // 0x14dcd34: LoadField: r0 = r2->field_b
    //     0x14dcd34: ldur            w0, [x2, #0xb]
    // 0x14dcd38: r1 = LoadInt32Instr(r0)
    //     0x14dcd38: sbfx            x1, x0, #1, #0x1f
    // 0x14dcd3c: mov             x0, x1
    // 0x14dcd40: r1 = 0
    //     0x14dcd40: movz            x1, #0
    // 0x14dcd44: cmp             x1, x0
    // 0x14dcd48: b.hs            #0x14ddb78
    // 0x14dcd4c: LoadField: r0 = r2->field_f
    //     0x14dcd4c: ldur            w0, [x2, #0xf]
    // 0x14dcd50: DecompressPointer r0
    //     0x14dcd50: add             x0, x0, HEAP, lsl #32
    // 0x14dcd54: LoadField: r1 = r0->field_f
    //     0x14dcd54: ldur            w1, [x0, #0xf]
    // 0x14dcd58: DecompressPointer r1
    //     0x14dcd58: add             x1, x1, HEAP, lsl #32
    // 0x14dcd5c: LoadField: r0 = r1->field_33
    //     0x14dcd5c: ldur            w0, [x1, #0x33]
    // 0x14dcd60: DecompressPointer r0
    //     0x14dcd60: add             x0, x0, HEAP, lsl #32
    // 0x14dcd64: cmp             w0, NULL
    // 0x14dcd68: b.ne            #0x14dcd70
    // 0x14dcd6c: r0 = ""
    //     0x14dcd6c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dcd70: ldur            x3, [fp, #-8]
    // 0x14dcd74: ldur            x2, [fp, #-0x38]
    // 0x14dcd78: mov             x1, x2
    // 0x14dcd7c: ArrayStore: r1[1] = r0  ; List_4
    //     0x14dcd7c: add             x25, x1, #0x13
    //     0x14dcd80: str             w0, [x25]
    //     0x14dcd84: tbz             w0, #0, #0x14dcda0
    //     0x14dcd88: ldurb           w16, [x1, #-1]
    //     0x14dcd8c: ldurb           w17, [x0, #-1]
    //     0x14dcd90: and             x16, x17, x16, lsr #2
    //     0x14dcd94: tst             x16, HEAP, lsr #32
    //     0x14dcd98: b.eq            #0x14dcda0
    //     0x14dcd9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dcda0: r16 = " / Qty: "
    //     0x14dcda0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x14dcda4: ldr             x16, [x16, #0x760]
    // 0x14dcda8: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dcda8: stur            w16, [x2, #0x17]
    // 0x14dcdac: LoadField: r1 = r3->field_f
    //     0x14dcdac: ldur            w1, [x3, #0xf]
    // 0x14dcdb0: DecompressPointer r1
    //     0x14dcdb0: add             x1, x1, HEAP, lsl #32
    // 0x14dcdb4: r0 = controller()
    //     0x14dcdb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dcdb8: LoadField: r1 = r0->field_53
    //     0x14dcdb8: ldur            w1, [x0, #0x53]
    // 0x14dcdbc: DecompressPointer r1
    //     0x14dcdbc: add             x1, x1, HEAP, lsl #32
    // 0x14dcdc0: r0 = value()
    //     0x14dcdc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcdc4: LoadField: r2 = r0->field_1b
    //     0x14dcdc4: ldur            w2, [x0, #0x1b]
    // 0x14dcdc8: DecompressPointer r2
    //     0x14dcdc8: add             x2, x2, HEAP, lsl #32
    // 0x14dcdcc: cmp             w2, NULL
    // 0x14dcdd0: b.ne            #0x14dcddc
    // 0x14dcdd4: r0 = Null
    //     0x14dcdd4: mov             x0, NULL
    // 0x14dcdd8: b               #0x14dce0c
    // 0x14dcddc: LoadField: r0 = r2->field_b
    //     0x14dcddc: ldur            w0, [x2, #0xb]
    // 0x14dcde0: r1 = LoadInt32Instr(r0)
    //     0x14dcde0: sbfx            x1, x0, #1, #0x1f
    // 0x14dcde4: mov             x0, x1
    // 0x14dcde8: r1 = 0
    //     0x14dcde8: movz            x1, #0
    // 0x14dcdec: cmp             x1, x0
    // 0x14dcdf0: b.hs            #0x14ddb7c
    // 0x14dcdf4: LoadField: r0 = r2->field_f
    //     0x14dcdf4: ldur            w0, [x2, #0xf]
    // 0x14dcdf8: DecompressPointer r0
    //     0x14dcdf8: add             x0, x0, HEAP, lsl #32
    // 0x14dcdfc: LoadField: r1 = r0->field_f
    //     0x14dcdfc: ldur            w1, [x0, #0xf]
    // 0x14dce00: DecompressPointer r1
    //     0x14dce00: add             x1, x1, HEAP, lsl #32
    // 0x14dce04: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14dce04: ldur            w0, [x1, #0x17]
    // 0x14dce08: DecompressPointer r0
    //     0x14dce08: add             x0, x0, HEAP, lsl #32
    // 0x14dce0c: cmp             w0, NULL
    // 0x14dce10: b.ne            #0x14dce18
    // 0x14dce14: r0 = ""
    //     0x14dce14: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dce18: ldur            x2, [fp, #-8]
    // 0x14dce1c: ldur            x1, [fp, #-0x38]
    // 0x14dce20: ArrayStore: r1[3] = r0  ; List_4
    //     0x14dce20: add             x25, x1, #0x1b
    //     0x14dce24: str             w0, [x25]
    //     0x14dce28: tbz             w0, #0, #0x14dce44
    //     0x14dce2c: ldurb           w16, [x1, #-1]
    //     0x14dce30: ldurb           w17, [x0, #-1]
    //     0x14dce34: and             x16, x17, x16, lsr #2
    //     0x14dce38: tst             x16, HEAP, lsr #32
    //     0x14dce3c: b.eq            #0x14dce44
    //     0x14dce40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dce44: ldur            x16, [fp, #-0x38]
    // 0x14dce48: str             x16, [SP]
    // 0x14dce4c: r0 = _interpolate()
    //     0x14dce4c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14dce50: ldur            x2, [fp, #-8]
    // 0x14dce54: stur            x0, [fp, #-0x38]
    // 0x14dce58: LoadField: r1 = r2->field_13
    //     0x14dce58: ldur            w1, [x2, #0x13]
    // 0x14dce5c: DecompressPointer r1
    //     0x14dce5c: add             x1, x1, HEAP, lsl #32
    // 0x14dce60: r0 = of()
    //     0x14dce60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dce64: LoadField: r1 = r0->field_87
    //     0x14dce64: ldur            w1, [x0, #0x87]
    // 0x14dce68: DecompressPointer r1
    //     0x14dce68: add             x1, x1, HEAP, lsl #32
    // 0x14dce6c: LoadField: r0 = r1->field_2b
    //     0x14dce6c: ldur            w0, [x1, #0x2b]
    // 0x14dce70: DecompressPointer r0
    //     0x14dce70: add             x0, x0, HEAP, lsl #32
    // 0x14dce74: r16 = 12.000000
    //     0x14dce74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dce78: ldr             x16, [x16, #0x9e8]
    // 0x14dce7c: r30 = Instance_Color
    //     0x14dce7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dce80: stp             lr, x16, [SP]
    // 0x14dce84: mov             x1, x0
    // 0x14dce88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14dce88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14dce8c: ldr             x4, [x4, #0xaa0]
    // 0x14dce90: r0 = copyWith()
    //     0x14dce90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dce94: stur            x0, [fp, #-0x40]
    // 0x14dce98: r0 = Text()
    //     0x14dce98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dce9c: mov             x1, x0
    // 0x14dcea0: ldur            x0, [fp, #-0x38]
    // 0x14dcea4: StoreField: r1->field_b = r0
    //     0x14dcea4: stur            w0, [x1, #0xb]
    // 0x14dcea8: ldur            x0, [fp, #-0x40]
    // 0x14dceac: StoreField: r1->field_13 = r0
    //     0x14dceac: stur            w0, [x1, #0x13]
    // 0x14dceb0: mov             x0, x1
    // 0x14dceb4: ldur            x2, [fp, #-8]
    // 0x14dceb8: stur            x0, [fp, #-0x38]
    // 0x14dcebc: r0 = Padding()
    //     0x14dcebc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dcec0: mov             x2, x0
    // 0x14dcec4: r0 = Instance_EdgeInsets
    //     0x14dcec4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14dcec8: ldr             x0, [x0, #0x770]
    // 0x14dcecc: stur            x2, [fp, #-0x40]
    // 0x14dced0: StoreField: r2->field_f = r0
    //     0x14dced0: stur            w0, [x2, #0xf]
    // 0x14dced4: ldur            x0, [fp, #-0x38]
    // 0x14dced8: StoreField: r2->field_b = r0
    //     0x14dced8: stur            w0, [x2, #0xb]
    // 0x14dcedc: ldur            x0, [fp, #-8]
    // 0x14dcee0: LoadField: r1 = r0->field_f
    //     0x14dcee0: ldur            w1, [x0, #0xf]
    // 0x14dcee4: DecompressPointer r1
    //     0x14dcee4: add             x1, x1, HEAP, lsl #32
    // 0x14dcee8: r0 = controller()
    //     0x14dcee8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dceec: LoadField: r1 = r0->field_53
    //     0x14dceec: ldur            w1, [x0, #0x53]
    // 0x14dcef0: DecompressPointer r1
    //     0x14dcef0: add             x1, x1, HEAP, lsl #32
    // 0x14dcef4: r0 = value()
    //     0x14dcef4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcef8: LoadField: r2 = r0->field_1b
    //     0x14dcef8: ldur            w2, [x0, #0x1b]
    // 0x14dcefc: DecompressPointer r2
    //     0x14dcefc: add             x2, x2, HEAP, lsl #32
    // 0x14dcf00: cmp             w2, NULL
    // 0x14dcf04: b.ne            #0x14dcf10
    // 0x14dcf08: r0 = Null
    //     0x14dcf08: mov             x0, NULL
    // 0x14dcf0c: b               #0x14dcf40
    // 0x14dcf10: LoadField: r0 = r2->field_b
    //     0x14dcf10: ldur            w0, [x2, #0xb]
    // 0x14dcf14: r1 = LoadInt32Instr(r0)
    //     0x14dcf14: sbfx            x1, x0, #1, #0x1f
    // 0x14dcf18: mov             x0, x1
    // 0x14dcf1c: r1 = 0
    //     0x14dcf1c: movz            x1, #0
    // 0x14dcf20: cmp             x1, x0
    // 0x14dcf24: b.hs            #0x14ddb80
    // 0x14dcf28: LoadField: r0 = r2->field_f
    //     0x14dcf28: ldur            w0, [x2, #0xf]
    // 0x14dcf2c: DecompressPointer r0
    //     0x14dcf2c: add             x0, x0, HEAP, lsl #32
    // 0x14dcf30: LoadField: r1 = r0->field_f
    //     0x14dcf30: ldur            w1, [x0, #0xf]
    // 0x14dcf34: DecompressPointer r1
    //     0x14dcf34: add             x1, x1, HEAP, lsl #32
    // 0x14dcf38: LoadField: r0 = r1->field_2f
    //     0x14dcf38: ldur            w0, [x1, #0x2f]
    // 0x14dcf3c: DecompressPointer r0
    //     0x14dcf3c: add             x0, x0, HEAP, lsl #32
    // 0x14dcf40: cmp             w0, NULL
    // 0x14dcf44: b.ne            #0x14dcf4c
    // 0x14dcf48: r0 = ""
    //     0x14dcf48: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dcf4c: ldur            x2, [fp, #-8]
    // 0x14dcf50: stur            x0, [fp, #-0x38]
    // 0x14dcf54: LoadField: r1 = r2->field_13
    //     0x14dcf54: ldur            w1, [x2, #0x13]
    // 0x14dcf58: DecompressPointer r1
    //     0x14dcf58: add             x1, x1, HEAP, lsl #32
    // 0x14dcf5c: r0 = of()
    //     0x14dcf5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dcf60: LoadField: r1 = r0->field_87
    //     0x14dcf60: ldur            w1, [x0, #0x87]
    // 0x14dcf64: DecompressPointer r1
    //     0x14dcf64: add             x1, x1, HEAP, lsl #32
    // 0x14dcf68: LoadField: r0 = r1->field_7
    //     0x14dcf68: ldur            w0, [x1, #7]
    // 0x14dcf6c: DecompressPointer r0
    //     0x14dcf6c: add             x0, x0, HEAP, lsl #32
    // 0x14dcf70: r16 = Instance_Color
    //     0x14dcf70: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dcf74: r30 = 12.000000
    //     0x14dcf74: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dcf78: ldr             lr, [lr, #0x9e8]
    // 0x14dcf7c: stp             lr, x16, [SP]
    // 0x14dcf80: mov             x1, x0
    // 0x14dcf84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dcf84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dcf88: ldr             x4, [x4, #0x9b8]
    // 0x14dcf8c: r0 = copyWith()
    //     0x14dcf8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dcf90: stur            x0, [fp, #-0x50]
    // 0x14dcf94: r0 = Text()
    //     0x14dcf94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dcf98: mov             x2, x0
    // 0x14dcf9c: ldur            x0, [fp, #-0x38]
    // 0x14dcfa0: stur            x2, [fp, #-0x58]
    // 0x14dcfa4: StoreField: r2->field_b = r0
    //     0x14dcfa4: stur            w0, [x2, #0xb]
    // 0x14dcfa8: ldur            x0, [fp, #-0x50]
    // 0x14dcfac: StoreField: r2->field_13 = r0
    //     0x14dcfac: stur            w0, [x2, #0x13]
    // 0x14dcfb0: ldur            x0, [fp, #-8]
    // 0x14dcfb4: LoadField: r1 = r0->field_f
    //     0x14dcfb4: ldur            w1, [x0, #0xf]
    // 0x14dcfb8: DecompressPointer r1
    //     0x14dcfb8: add             x1, x1, HEAP, lsl #32
    // 0x14dcfbc: r0 = controller()
    //     0x14dcfbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dcfc0: LoadField: r1 = r0->field_73
    //     0x14dcfc0: ldur            w1, [x0, #0x73]
    // 0x14dcfc4: DecompressPointer r1
    //     0x14dcfc4: add             x1, x1, HEAP, lsl #32
    // 0x14dcfc8: r0 = value()
    //     0x14dcfc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dcfcc: LoadField: r1 = r0->field_13
    //     0x14dcfcc: ldur            w1, [x0, #0x13]
    // 0x14dcfd0: DecompressPointer r1
    //     0x14dcfd0: add             x1, x1, HEAP, lsl #32
    // 0x14dcfd4: cmp             w1, NULL
    // 0x14dcfd8: b.ne            #0x14dcfe4
    // 0x14dcfdc: r0 = Null
    //     0x14dcfdc: mov             x0, NULL
    // 0x14dcfe0: b               #0x14dcffc
    // 0x14dcfe4: LoadField: r0 = r1->field_b
    //     0x14dcfe4: ldur            w0, [x1, #0xb]
    // 0x14dcfe8: cbnz            w0, #0x14dcff4
    // 0x14dcfec: r1 = false
    //     0x14dcfec: add             x1, NULL, #0x30  ; false
    // 0x14dcff0: b               #0x14dcff8
    // 0x14dcff4: r1 = true
    //     0x14dcff4: add             x1, NULL, #0x20  ; true
    // 0x14dcff8: mov             x0, x1
    // 0x14dcffc: cmp             w0, NULL
    // 0x14dd000: b.eq            #0x14dd010
    // 0x14dd004: tbnz            w0, #4, #0x14dd010
    // 0x14dd008: r8 = true
    //     0x14dd008: add             x8, NULL, #0x20  ; true
    // 0x14dd00c: b               #0x14dd0c8
    // 0x14dd010: ldur            x2, [fp, #-8]
    // 0x14dd014: LoadField: r1 = r2->field_f
    //     0x14dd014: ldur            w1, [x2, #0xf]
    // 0x14dd018: DecompressPointer r1
    //     0x14dd018: add             x1, x1, HEAP, lsl #32
    // 0x14dd01c: r0 = controller()
    //     0x14dd01c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dd020: LoadField: r1 = r0->field_6f
    //     0x14dd020: ldur            w1, [x0, #0x6f]
    // 0x14dd024: DecompressPointer r1
    //     0x14dd024: add             x1, x1, HEAP, lsl #32
    // 0x14dd028: r0 = value()
    //     0x14dd028: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dd02c: LoadField: r1 = r0->field_b
    //     0x14dd02c: ldur            w1, [x0, #0xb]
    // 0x14dd030: DecompressPointer r1
    //     0x14dd030: add             x1, x1, HEAP, lsl #32
    // 0x14dd034: cmp             w1, NULL
    // 0x14dd038: b.ne            #0x14dd044
    // 0x14dd03c: r0 = Null
    //     0x14dd03c: mov             x0, NULL
    // 0x14dd040: b               #0x14dd0b8
    // 0x14dd044: LoadField: r0 = r1->field_f
    //     0x14dd044: ldur            w0, [x1, #0xf]
    // 0x14dd048: DecompressPointer r0
    //     0x14dd048: add             x0, x0, HEAP, lsl #32
    // 0x14dd04c: cmp             w0, NULL
    // 0x14dd050: b.ne            #0x14dd05c
    // 0x14dd054: r0 = Null
    //     0x14dd054: mov             x0, NULL
    // 0x14dd058: b               #0x14dd0b8
    // 0x14dd05c: LoadField: r2 = r0->field_b
    //     0x14dd05c: ldur            w2, [x0, #0xb]
    // 0x14dd060: DecompressPointer r2
    //     0x14dd060: add             x2, x2, HEAP, lsl #32
    // 0x14dd064: LoadField: r0 = r2->field_b
    //     0x14dd064: ldur            w0, [x2, #0xb]
    // 0x14dd068: r1 = LoadInt32Instr(r0)
    //     0x14dd068: sbfx            x1, x0, #1, #0x1f
    // 0x14dd06c: mov             x0, x1
    // 0x14dd070: r1 = 0
    //     0x14dd070: movz            x1, #0
    // 0x14dd074: cmp             x1, x0
    // 0x14dd078: b.hs            #0x14ddb84
    // 0x14dd07c: LoadField: r0 = r2->field_f
    //     0x14dd07c: ldur            w0, [x2, #0xf]
    // 0x14dd080: DecompressPointer r0
    //     0x14dd080: add             x0, x0, HEAP, lsl #32
    // 0x14dd084: LoadField: r1 = r0->field_f
    //     0x14dd084: ldur            w1, [x0, #0xf]
    // 0x14dd088: DecompressPointer r1
    //     0x14dd088: add             x1, x1, HEAP, lsl #32
    // 0x14dd08c: LoadField: r0 = r1->field_43
    //     0x14dd08c: ldur            w0, [x1, #0x43]
    // 0x14dd090: DecompressPointer r0
    //     0x14dd090: add             x0, x0, HEAP, lsl #32
    // 0x14dd094: cmp             w0, NULL
    // 0x14dd098: b.ne            #0x14dd0a4
    // 0x14dd09c: r0 = Null
    //     0x14dd09c: mov             x0, NULL
    // 0x14dd0a0: b               #0x14dd0b8
    // 0x14dd0a4: LoadField: r1 = r0->field_b
    //     0x14dd0a4: ldur            w1, [x0, #0xb]
    // 0x14dd0a8: cbnz            w1, #0x14dd0b4
    // 0x14dd0ac: r0 = false
    //     0x14dd0ac: add             x0, NULL, #0x30  ; false
    // 0x14dd0b0: b               #0x14dd0b8
    // 0x14dd0b4: r0 = true
    //     0x14dd0b4: add             x0, NULL, #0x20  ; true
    // 0x14dd0b8: cmp             w0, NULL
    // 0x14dd0bc: b.ne            #0x14dd0c4
    // 0x14dd0c0: r0 = false
    //     0x14dd0c0: add             x0, NULL, #0x30  ; false
    // 0x14dd0c4: mov             x8, x0
    // 0x14dd0c8: ldur            x2, [fp, #-8]
    // 0x14dd0cc: ldur            x7, [fp, #-0x10]
    // 0x14dd0d0: ldur            x6, [fp, #-0x28]
    // 0x14dd0d4: ldur            x5, [fp, #-0x20]
    // 0x14dd0d8: ldur            x4, [fp, #-0x48]
    // 0x14dd0dc: ldur            x3, [fp, #-0x40]
    // 0x14dd0e0: ldur            x0, [fp, #-0x58]
    // 0x14dd0e4: stur            x8, [fp, #-0x38]
    // 0x14dd0e8: LoadField: r1 = r2->field_13
    //     0x14dd0e8: ldur            w1, [x2, #0x13]
    // 0x14dd0ec: DecompressPointer r1
    //     0x14dd0ec: add             x1, x1, HEAP, lsl #32
    // 0x14dd0f0: r0 = of()
    //     0x14dd0f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dd0f4: LoadField: r1 = r0->field_5b
    //     0x14dd0f4: ldur            w1, [x0, #0x5b]
    // 0x14dd0f8: DecompressPointer r1
    //     0x14dd0f8: add             x1, x1, HEAP, lsl #32
    // 0x14dd0fc: r0 = LoadClassIdInstr(r1)
    //     0x14dd0fc: ldur            x0, [x1, #-1]
    //     0x14dd100: ubfx            x0, x0, #0xc, #0x14
    // 0x14dd104: d0 = 0.100000
    //     0x14dd104: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14dd108: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14dd108: sub             lr, x0, #0xffa
    //     0x14dd10c: ldr             lr, [x21, lr, lsl #3]
    //     0x14dd110: blr             lr
    // 0x14dd114: stur            x0, [fp, #-0x50]
    // 0x14dd118: r0 = Radius()
    //     0x14dd118: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14dd11c: d0 = 12.000000
    //     0x14dd11c: fmov            d0, #12.00000000
    // 0x14dd120: stur            x0, [fp, #-0x60]
    // 0x14dd124: StoreField: r0->field_7 = d0
    //     0x14dd124: stur            d0, [x0, #7]
    // 0x14dd128: StoreField: r0->field_f = d0
    //     0x14dd128: stur            d0, [x0, #0xf]
    // 0x14dd12c: r0 = BorderRadius()
    //     0x14dd12c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14dd130: mov             x1, x0
    // 0x14dd134: ldur            x0, [fp, #-0x60]
    // 0x14dd138: stur            x1, [fp, #-0x68]
    // 0x14dd13c: StoreField: r1->field_7 = r0
    //     0x14dd13c: stur            w0, [x1, #7]
    // 0x14dd140: StoreField: r1->field_b = r0
    //     0x14dd140: stur            w0, [x1, #0xb]
    // 0x14dd144: StoreField: r1->field_f = r0
    //     0x14dd144: stur            w0, [x1, #0xf]
    // 0x14dd148: StoreField: r1->field_13 = r0
    //     0x14dd148: stur            w0, [x1, #0x13]
    // 0x14dd14c: r0 = BoxDecoration()
    //     0x14dd14c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14dd150: mov             x2, x0
    // 0x14dd154: ldur            x0, [fp, #-0x50]
    // 0x14dd158: stur            x2, [fp, #-0x60]
    // 0x14dd15c: StoreField: r2->field_7 = r0
    //     0x14dd15c: stur            w0, [x2, #7]
    // 0x14dd160: ldur            x0, [fp, #-0x68]
    // 0x14dd164: StoreField: r2->field_13 = r0
    //     0x14dd164: stur            w0, [x2, #0x13]
    // 0x14dd168: r0 = Instance_BoxShape
    //     0x14dd168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14dd16c: ldr             x0, [x0, #0x80]
    // 0x14dd170: StoreField: r2->field_23 = r0
    //     0x14dd170: stur            w0, [x2, #0x23]
    // 0x14dd174: ldur            x3, [fp, #-8]
    // 0x14dd178: LoadField: r1 = r3->field_13
    //     0x14dd178: ldur            w1, [x3, #0x13]
    // 0x14dd17c: DecompressPointer r1
    //     0x14dd17c: add             x1, x1, HEAP, lsl #32
    // 0x14dd180: r0 = of()
    //     0x14dd180: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dd184: LoadField: r1 = r0->field_87
    //     0x14dd184: ldur            w1, [x0, #0x87]
    // 0x14dd188: DecompressPointer r1
    //     0x14dd188: add             x1, x1, HEAP, lsl #32
    // 0x14dd18c: LoadField: r0 = r1->field_7
    //     0x14dd18c: ldur            w0, [x1, #7]
    // 0x14dd190: DecompressPointer r0
    //     0x14dd190: add             x0, x0, HEAP, lsl #32
    // 0x14dd194: stur            x0, [fp, #-0x50]
    // 0x14dd198: r1 = Instance_Color
    //     0x14dd198: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dd19c: d0 = 0.700000
    //     0x14dd19c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14dd1a0: ldr             d0, [x17, #0xf48]
    // 0x14dd1a4: r0 = withOpacity()
    //     0x14dd1a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14dd1a8: r16 = 12.000000
    //     0x14dd1a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14dd1ac: ldr             x16, [x16, #0x9e8]
    // 0x14dd1b0: stp             x0, x16, [SP]
    // 0x14dd1b4: ldur            x1, [fp, #-0x50]
    // 0x14dd1b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14dd1b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14dd1bc: ldr             x4, [x4, #0xaa0]
    // 0x14dd1c0: r0 = copyWith()
    //     0x14dd1c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dd1c4: stur            x0, [fp, #-0x50]
    // 0x14dd1c8: r0 = Text()
    //     0x14dd1c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dd1cc: mov             x1, x0
    // 0x14dd1d0: r0 = "Customised"
    //     0x14dd1d0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x14dd1d4: ldr             x0, [x0, #0xd88]
    // 0x14dd1d8: stur            x1, [fp, #-0x68]
    // 0x14dd1dc: StoreField: r1->field_b = r0
    //     0x14dd1dc: stur            w0, [x1, #0xb]
    // 0x14dd1e0: ldur            x0, [fp, #-0x50]
    // 0x14dd1e4: StoreField: r1->field_13 = r0
    //     0x14dd1e4: stur            w0, [x1, #0x13]
    // 0x14dd1e8: r0 = Center()
    //     0x14dd1e8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14dd1ec: mov             x1, x0
    // 0x14dd1f0: r0 = Instance_Alignment
    //     0x14dd1f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14dd1f4: ldr             x0, [x0, #0xb10]
    // 0x14dd1f8: stur            x1, [fp, #-0x50]
    // 0x14dd1fc: StoreField: r1->field_f = r0
    //     0x14dd1fc: stur            w0, [x1, #0xf]
    // 0x14dd200: ldur            x2, [fp, #-0x68]
    // 0x14dd204: StoreField: r1->field_b = r2
    //     0x14dd204: stur            w2, [x1, #0xb]
    // 0x14dd208: r0 = Container()
    //     0x14dd208: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dd20c: stur            x0, [fp, #-0x68]
    // 0x14dd210: r16 = Instance_EdgeInsets
    //     0x14dd210: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d90] Obj!EdgeInsets@d59631
    //     0x14dd214: ldr             x16, [x16, #0xd90]
    // 0x14dd218: ldur            lr, [fp, #-0x60]
    // 0x14dd21c: stp             lr, x16, [SP, #8]
    // 0x14dd220: ldur            x16, [fp, #-0x50]
    // 0x14dd224: str             x16, [SP]
    // 0x14dd228: mov             x1, x0
    // 0x14dd22c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x14dd22c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x14dd230: ldr             x4, [x4, #0x610]
    // 0x14dd234: r0 = Container()
    //     0x14dd234: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dd238: r0 = Visibility()
    //     0x14dd238: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14dd23c: mov             x3, x0
    // 0x14dd240: ldur            x0, [fp, #-0x68]
    // 0x14dd244: stur            x3, [fp, #-0x50]
    // 0x14dd248: StoreField: r3->field_b = r0
    //     0x14dd248: stur            w0, [x3, #0xb]
    // 0x14dd24c: r0 = Instance_SizedBox
    //     0x14dd24c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14dd250: StoreField: r3->field_f = r0
    //     0x14dd250: stur            w0, [x3, #0xf]
    // 0x14dd254: ldur            x0, [fp, #-0x38]
    // 0x14dd258: StoreField: r3->field_13 = r0
    //     0x14dd258: stur            w0, [x3, #0x13]
    // 0x14dd25c: r0 = false
    //     0x14dd25c: add             x0, NULL, #0x30  ; false
    // 0x14dd260: ArrayStore: r3[0] = r0  ; List_4
    //     0x14dd260: stur            w0, [x3, #0x17]
    // 0x14dd264: StoreField: r3->field_1b = r0
    //     0x14dd264: stur            w0, [x3, #0x1b]
    // 0x14dd268: StoreField: r3->field_1f = r0
    //     0x14dd268: stur            w0, [x3, #0x1f]
    // 0x14dd26c: StoreField: r3->field_23 = r0
    //     0x14dd26c: stur            w0, [x3, #0x23]
    // 0x14dd270: StoreField: r3->field_27 = r0
    //     0x14dd270: stur            w0, [x3, #0x27]
    // 0x14dd274: StoreField: r3->field_2b = r0
    //     0x14dd274: stur            w0, [x3, #0x2b]
    // 0x14dd278: r1 = Null
    //     0x14dd278: mov             x1, NULL
    // 0x14dd27c: r2 = 6
    //     0x14dd27c: movz            x2, #0x6
    // 0x14dd280: r0 = AllocateArray()
    //     0x14dd280: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd284: mov             x2, x0
    // 0x14dd288: ldur            x0, [fp, #-0x58]
    // 0x14dd28c: stur            x2, [fp, #-0x38]
    // 0x14dd290: StoreField: r2->field_f = r0
    //     0x14dd290: stur            w0, [x2, #0xf]
    // 0x14dd294: r16 = Instance_SizedBox
    //     0x14dd294: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x14dd298: ldr             x16, [x16, #0x998]
    // 0x14dd29c: StoreField: r2->field_13 = r16
    //     0x14dd29c: stur            w16, [x2, #0x13]
    // 0x14dd2a0: ldur            x0, [fp, #-0x50]
    // 0x14dd2a4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dd2a4: stur            w0, [x2, #0x17]
    // 0x14dd2a8: r1 = <Widget>
    //     0x14dd2a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd2ac: r0 = AllocateGrowableArray()
    //     0x14dd2ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd2b0: mov             x1, x0
    // 0x14dd2b4: ldur            x0, [fp, #-0x38]
    // 0x14dd2b8: stur            x1, [fp, #-0x50]
    // 0x14dd2bc: StoreField: r1->field_f = r0
    //     0x14dd2bc: stur            w0, [x1, #0xf]
    // 0x14dd2c0: r2 = 6
    //     0x14dd2c0: movz            x2, #0x6
    // 0x14dd2c4: StoreField: r1->field_b = r2
    //     0x14dd2c4: stur            w2, [x1, #0xb]
    // 0x14dd2c8: r0 = Row()
    //     0x14dd2c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14dd2cc: mov             x3, x0
    // 0x14dd2d0: r0 = Instance_Axis
    //     0x14dd2d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14dd2d4: stur            x3, [fp, #-0x38]
    // 0x14dd2d8: StoreField: r3->field_f = r0
    //     0x14dd2d8: stur            w0, [x3, #0xf]
    // 0x14dd2dc: r4 = Instance_MainAxisAlignment
    //     0x14dd2dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dd2e0: ldr             x4, [x4, #0xa08]
    // 0x14dd2e4: StoreField: r3->field_13 = r4
    //     0x14dd2e4: stur            w4, [x3, #0x13]
    // 0x14dd2e8: r5 = Instance_MainAxisSize
    //     0x14dd2e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dd2ec: ldr             x5, [x5, #0xa10]
    // 0x14dd2f0: ArrayStore: r3[0] = r5  ; List_4
    //     0x14dd2f0: stur            w5, [x3, #0x17]
    // 0x14dd2f4: r6 = Instance_CrossAxisAlignment
    //     0x14dd2f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14dd2f8: ldr             x6, [x6, #0xa18]
    // 0x14dd2fc: StoreField: r3->field_1b = r6
    //     0x14dd2fc: stur            w6, [x3, #0x1b]
    // 0x14dd300: r7 = Instance_VerticalDirection
    //     0x14dd300: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dd304: ldr             x7, [x7, #0xa20]
    // 0x14dd308: StoreField: r3->field_23 = r7
    //     0x14dd308: stur            w7, [x3, #0x23]
    // 0x14dd30c: r8 = Instance_Clip
    //     0x14dd30c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dd310: ldr             x8, [x8, #0x38]
    // 0x14dd314: StoreField: r3->field_2b = r8
    //     0x14dd314: stur            w8, [x3, #0x2b]
    // 0x14dd318: StoreField: r3->field_2f = rZR
    //     0x14dd318: stur            xzr, [x3, #0x2f]
    // 0x14dd31c: ldur            x1, [fp, #-0x50]
    // 0x14dd320: StoreField: r3->field_b = r1
    //     0x14dd320: stur            w1, [x3, #0xb]
    // 0x14dd324: r1 = Null
    //     0x14dd324: mov             x1, NULL
    // 0x14dd328: r2 = 8
    //     0x14dd328: movz            x2, #0x8
    // 0x14dd32c: r0 = AllocateArray()
    //     0x14dd32c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd330: mov             x2, x0
    // 0x14dd334: ldur            x0, [fp, #-0x48]
    // 0x14dd338: stur            x2, [fp, #-0x50]
    // 0x14dd33c: StoreField: r2->field_f = r0
    //     0x14dd33c: stur            w0, [x2, #0xf]
    // 0x14dd340: ldur            x0, [fp, #-0x40]
    // 0x14dd344: StoreField: r2->field_13 = r0
    //     0x14dd344: stur            w0, [x2, #0x13]
    // 0x14dd348: r16 = Instance_SizedBox
    //     0x14dd348: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14dd34c: ldr             x16, [x16, #0xc70]
    // 0x14dd350: ArrayStore: r2[0] = r16  ; List_4
    //     0x14dd350: stur            w16, [x2, #0x17]
    // 0x14dd354: ldur            x0, [fp, #-0x38]
    // 0x14dd358: StoreField: r2->field_1b = r0
    //     0x14dd358: stur            w0, [x2, #0x1b]
    // 0x14dd35c: r1 = <Widget>
    //     0x14dd35c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd360: r0 = AllocateGrowableArray()
    //     0x14dd360: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd364: mov             x1, x0
    // 0x14dd368: ldur            x0, [fp, #-0x50]
    // 0x14dd36c: stur            x1, [fp, #-0x38]
    // 0x14dd370: StoreField: r1->field_f = r0
    //     0x14dd370: stur            w0, [x1, #0xf]
    // 0x14dd374: r2 = 8
    //     0x14dd374: movz            x2, #0x8
    // 0x14dd378: StoreField: r1->field_b = r2
    //     0x14dd378: stur            w2, [x1, #0xb]
    // 0x14dd37c: r0 = Column()
    //     0x14dd37c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14dd380: mov             x2, x0
    // 0x14dd384: r0 = Instance_Axis
    //     0x14dd384: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14dd388: stur            x2, [fp, #-0x40]
    // 0x14dd38c: StoreField: r2->field_f = r0
    //     0x14dd38c: stur            w0, [x2, #0xf]
    // 0x14dd390: r3 = Instance_MainAxisAlignment
    //     0x14dd390: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dd394: ldr             x3, [x3, #0xa08]
    // 0x14dd398: StoreField: r2->field_13 = r3
    //     0x14dd398: stur            w3, [x2, #0x13]
    // 0x14dd39c: r4 = Instance_MainAxisSize
    //     0x14dd39c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dd3a0: ldr             x4, [x4, #0xa10]
    // 0x14dd3a4: ArrayStore: r2[0] = r4  ; List_4
    //     0x14dd3a4: stur            w4, [x2, #0x17]
    // 0x14dd3a8: r1 = Instance_CrossAxisAlignment
    //     0x14dd3a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14dd3ac: ldr             x1, [x1, #0x890]
    // 0x14dd3b0: StoreField: r2->field_1b = r1
    //     0x14dd3b0: stur            w1, [x2, #0x1b]
    // 0x14dd3b4: r5 = Instance_VerticalDirection
    //     0x14dd3b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dd3b8: ldr             x5, [x5, #0xa20]
    // 0x14dd3bc: StoreField: r2->field_23 = r5
    //     0x14dd3bc: stur            w5, [x2, #0x23]
    // 0x14dd3c0: r6 = Instance_Clip
    //     0x14dd3c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dd3c4: ldr             x6, [x6, #0x38]
    // 0x14dd3c8: StoreField: r2->field_2b = r6
    //     0x14dd3c8: stur            w6, [x2, #0x2b]
    // 0x14dd3cc: StoreField: r2->field_2f = rZR
    //     0x14dd3cc: stur            xzr, [x2, #0x2f]
    // 0x14dd3d0: ldur            x1, [fp, #-0x38]
    // 0x14dd3d4: StoreField: r2->field_b = r1
    //     0x14dd3d4: stur            w1, [x2, #0xb]
    // 0x14dd3d8: r1 = <FlexParentData>
    //     0x14dd3d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14dd3dc: ldr             x1, [x1, #0xe00]
    // 0x14dd3e0: r0 = Expanded()
    //     0x14dd3e0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14dd3e4: mov             x3, x0
    // 0x14dd3e8: r0 = 1
    //     0x14dd3e8: movz            x0, #0x1
    // 0x14dd3ec: stur            x3, [fp, #-0x38]
    // 0x14dd3f0: StoreField: r3->field_13 = r0
    //     0x14dd3f0: stur            x0, [x3, #0x13]
    // 0x14dd3f4: r0 = Instance_FlexFit
    //     0x14dd3f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14dd3f8: ldr             x0, [x0, #0xe08]
    // 0x14dd3fc: StoreField: r3->field_1b = r0
    //     0x14dd3fc: stur            w0, [x3, #0x1b]
    // 0x14dd400: ldur            x0, [fp, #-0x40]
    // 0x14dd404: StoreField: r3->field_b = r0
    //     0x14dd404: stur            w0, [x3, #0xb]
    // 0x14dd408: r1 = Null
    //     0x14dd408: mov             x1, NULL
    // 0x14dd40c: r2 = 6
    //     0x14dd40c: movz            x2, #0x6
    // 0x14dd410: r0 = AllocateArray()
    //     0x14dd410: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd414: mov             x2, x0
    // 0x14dd418: ldur            x0, [fp, #-0x20]
    // 0x14dd41c: stur            x2, [fp, #-0x40]
    // 0x14dd420: StoreField: r2->field_f = r0
    //     0x14dd420: stur            w0, [x2, #0xf]
    // 0x14dd424: r16 = Instance_SizedBox
    //     0x14dd424: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14dd428: ldr             x16, [x16, #0xb20]
    // 0x14dd42c: StoreField: r2->field_13 = r16
    //     0x14dd42c: stur            w16, [x2, #0x13]
    // 0x14dd430: ldur            x0, [fp, #-0x38]
    // 0x14dd434: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dd434: stur            w0, [x2, #0x17]
    // 0x14dd438: r1 = <Widget>
    //     0x14dd438: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd43c: r0 = AllocateGrowableArray()
    //     0x14dd43c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd440: mov             x1, x0
    // 0x14dd444: ldur            x0, [fp, #-0x40]
    // 0x14dd448: stur            x1, [fp, #-0x20]
    // 0x14dd44c: StoreField: r1->field_f = r0
    //     0x14dd44c: stur            w0, [x1, #0xf]
    // 0x14dd450: r0 = 6
    //     0x14dd450: movz            x0, #0x6
    // 0x14dd454: StoreField: r1->field_b = r0
    //     0x14dd454: stur            w0, [x1, #0xb]
    // 0x14dd458: r0 = Row()
    //     0x14dd458: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14dd45c: mov             x1, x0
    // 0x14dd460: r0 = Instance_Axis
    //     0x14dd460: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14dd464: stur            x1, [fp, #-0x38]
    // 0x14dd468: StoreField: r1->field_f = r0
    //     0x14dd468: stur            w0, [x1, #0xf]
    // 0x14dd46c: r2 = Instance_MainAxisAlignment
    //     0x14dd46c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dd470: ldr             x2, [x2, #0xa08]
    // 0x14dd474: StoreField: r1->field_13 = r2
    //     0x14dd474: stur            w2, [x1, #0x13]
    // 0x14dd478: r3 = Instance_MainAxisSize
    //     0x14dd478: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dd47c: ldr             x3, [x3, #0xa10]
    // 0x14dd480: ArrayStore: r1[0] = r3  ; List_4
    //     0x14dd480: stur            w3, [x1, #0x17]
    // 0x14dd484: r4 = Instance_CrossAxisAlignment
    //     0x14dd484: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14dd488: ldr             x4, [x4, #0xa18]
    // 0x14dd48c: StoreField: r1->field_1b = r4
    //     0x14dd48c: stur            w4, [x1, #0x1b]
    // 0x14dd490: r5 = Instance_VerticalDirection
    //     0x14dd490: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dd494: ldr             x5, [x5, #0xa20]
    // 0x14dd498: StoreField: r1->field_23 = r5
    //     0x14dd498: stur            w5, [x1, #0x23]
    // 0x14dd49c: r6 = Instance_Clip
    //     0x14dd49c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dd4a0: ldr             x6, [x6, #0x38]
    // 0x14dd4a4: StoreField: r1->field_2b = r6
    //     0x14dd4a4: stur            w6, [x1, #0x2b]
    // 0x14dd4a8: StoreField: r1->field_2f = rZR
    //     0x14dd4a8: stur            xzr, [x1, #0x2f]
    // 0x14dd4ac: ldur            x7, [fp, #-0x20]
    // 0x14dd4b0: StoreField: r1->field_b = r7
    //     0x14dd4b0: stur            w7, [x1, #0xb]
    // 0x14dd4b4: r0 = Padding()
    //     0x14dd4b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dd4b8: mov             x1, x0
    // 0x14dd4bc: r0 = Instance_EdgeInsets
    //     0x14dd4bc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fc30] Obj!EdgeInsets@d57891
    //     0x14dd4c0: ldr             x0, [x0, #0xc30]
    // 0x14dd4c4: stur            x1, [fp, #-0x20]
    // 0x14dd4c8: StoreField: r1->field_f = r0
    //     0x14dd4c8: stur            w0, [x1, #0xf]
    // 0x14dd4cc: ldur            x0, [fp, #-0x38]
    // 0x14dd4d0: StoreField: r1->field_b = r0
    //     0x14dd4d0: stur            w0, [x1, #0xb]
    // 0x14dd4d4: r0 = Container()
    //     0x14dd4d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dd4d8: stur            x0, [fp, #-0x38]
    // 0x14dd4dc: ldur            x16, [fp, #-0x30]
    // 0x14dd4e0: ldur            lr, [fp, #-0x20]
    // 0x14dd4e4: stp             lr, x16, [SP]
    // 0x14dd4e8: mov             x1, x0
    // 0x14dd4ec: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14dd4ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14dd4f0: ldr             x4, [x4, #0x88]
    // 0x14dd4f4: r0 = Container()
    //     0x14dd4f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dd4f8: r1 = Null
    //     0x14dd4f8: mov             x1, NULL
    // 0x14dd4fc: r2 = 4
    //     0x14dd4fc: movz            x2, #0x4
    // 0x14dd500: r0 = AllocateArray()
    //     0x14dd500: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd504: mov             x2, x0
    // 0x14dd508: ldur            x0, [fp, #-0x28]
    // 0x14dd50c: stur            x2, [fp, #-0x20]
    // 0x14dd510: StoreField: r2->field_f = r0
    //     0x14dd510: stur            w0, [x2, #0xf]
    // 0x14dd514: ldur            x0, [fp, #-0x38]
    // 0x14dd518: StoreField: r2->field_13 = r0
    //     0x14dd518: stur            w0, [x2, #0x13]
    // 0x14dd51c: r1 = <Widget>
    //     0x14dd51c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd520: r0 = AllocateGrowableArray()
    //     0x14dd520: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd524: mov             x1, x0
    // 0x14dd528: ldur            x0, [fp, #-0x20]
    // 0x14dd52c: stur            x1, [fp, #-0x28]
    // 0x14dd530: StoreField: r1->field_f = r0
    //     0x14dd530: stur            w0, [x1, #0xf]
    // 0x14dd534: r2 = 4
    //     0x14dd534: movz            x2, #0x4
    // 0x14dd538: StoreField: r1->field_b = r2
    //     0x14dd538: stur            w2, [x1, #0xb]
    // 0x14dd53c: r0 = Column()
    //     0x14dd53c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14dd540: mov             x1, x0
    // 0x14dd544: r0 = Instance_Axis
    //     0x14dd544: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14dd548: stur            x1, [fp, #-0x20]
    // 0x14dd54c: StoreField: r1->field_f = r0
    //     0x14dd54c: stur            w0, [x1, #0xf]
    // 0x14dd550: r0 = Instance_MainAxisAlignment
    //     0x14dd550: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dd554: ldr             x0, [x0, #0xa08]
    // 0x14dd558: StoreField: r1->field_13 = r0
    //     0x14dd558: stur            w0, [x1, #0x13]
    // 0x14dd55c: r0 = Instance_MainAxisSize
    //     0x14dd55c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dd560: ldr             x0, [x0, #0xa10]
    // 0x14dd564: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dd564: stur            w0, [x1, #0x17]
    // 0x14dd568: r2 = Instance_CrossAxisAlignment
    //     0x14dd568: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14dd56c: ldr             x2, [x2, #0xa18]
    // 0x14dd570: StoreField: r1->field_1b = r2
    //     0x14dd570: stur            w2, [x1, #0x1b]
    // 0x14dd574: r3 = Instance_VerticalDirection
    //     0x14dd574: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dd578: ldr             x3, [x3, #0xa20]
    // 0x14dd57c: StoreField: r1->field_23 = r3
    //     0x14dd57c: stur            w3, [x1, #0x23]
    // 0x14dd580: r4 = Instance_Clip
    //     0x14dd580: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dd584: ldr             x4, [x4, #0x38]
    // 0x14dd588: StoreField: r1->field_2b = r4
    //     0x14dd588: stur            w4, [x1, #0x2b]
    // 0x14dd58c: StoreField: r1->field_2f = rZR
    //     0x14dd58c: stur            xzr, [x1, #0x2f]
    // 0x14dd590: ldur            x5, [fp, #-0x28]
    // 0x14dd594: StoreField: r1->field_b = r5
    //     0x14dd594: stur            w5, [x1, #0xb]
    // 0x14dd598: r0 = Padding()
    //     0x14dd598: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dd59c: mov             x1, x0
    // 0x14dd5a0: r0 = Instance_EdgeInsets
    //     0x14dd5a0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x14dd5a4: ldr             x0, [x0, #0x778]
    // 0x14dd5a8: stur            x1, [fp, #-0x28]
    // 0x14dd5ac: StoreField: r1->field_f = r0
    //     0x14dd5ac: stur            w0, [x1, #0xf]
    // 0x14dd5b0: ldur            x2, [fp, #-0x20]
    // 0x14dd5b4: StoreField: r1->field_b = r2
    //     0x14dd5b4: stur            w2, [x1, #0xb]
    // 0x14dd5b8: r0 = SvgPicture()
    //     0x14dd5b8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14dd5bc: stur            x0, [fp, #-0x20]
    // 0x14dd5c0: r16 = Instance_BoxFit
    //     0x14dd5c0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14dd5c4: ldr             x16, [x16, #0xb18]
    // 0x14dd5c8: str             x16, [SP]
    // 0x14dd5cc: mov             x1, x0
    // 0x14dd5d0: r2 = "assets/images/product_between_icon.svg"
    //     0x14dd5d0: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0x14dd5d4: ldr             x2, [x2, #0xf28]
    // 0x14dd5d8: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x14dd5d8: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x14dd5dc: ldr             x4, [x4, #0xb0]
    // 0x14dd5e0: r0 = SvgPicture.asset()
    //     0x14dd5e0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14dd5e4: r1 = <StackParentData>
    //     0x14dd5e4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14dd5e8: ldr             x1, [x1, #0x8e0]
    // 0x14dd5ec: r0 = Positioned()
    //     0x14dd5ec: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14dd5f0: mov             x3, x0
    // 0x14dd5f4: r0 = 0.000000
    //     0x14dd5f4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14dd5f8: stur            x3, [fp, #-0x30]
    // 0x14dd5fc: StoreField: r3->field_13 = r0
    //     0x14dd5fc: stur            w0, [x3, #0x13]
    // 0x14dd600: ArrayStore: r3[0] = r0  ; List_4
    //     0x14dd600: stur            w0, [x3, #0x17]
    // 0x14dd604: StoreField: r3->field_1b = r0
    //     0x14dd604: stur            w0, [x3, #0x1b]
    // 0x14dd608: r0 = 14.000000
    //     0x14dd608: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14dd60c: ldr             x0, [x0, #0x1d8]
    // 0x14dd610: StoreField: r3->field_1f = r0
    //     0x14dd610: stur            w0, [x3, #0x1f]
    // 0x14dd614: ldur            x0, [fp, #-0x20]
    // 0x14dd618: StoreField: r3->field_b = r0
    //     0x14dd618: stur            w0, [x3, #0xb]
    // 0x14dd61c: r1 = Null
    //     0x14dd61c: mov             x1, NULL
    // 0x14dd620: r2 = 4
    //     0x14dd620: movz            x2, #0x4
    // 0x14dd624: r0 = AllocateArray()
    //     0x14dd624: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd628: mov             x2, x0
    // 0x14dd62c: ldur            x0, [fp, #-0x28]
    // 0x14dd630: stur            x2, [fp, #-0x20]
    // 0x14dd634: StoreField: r2->field_f = r0
    //     0x14dd634: stur            w0, [x2, #0xf]
    // 0x14dd638: ldur            x0, [fp, #-0x30]
    // 0x14dd63c: StoreField: r2->field_13 = r0
    //     0x14dd63c: stur            w0, [x2, #0x13]
    // 0x14dd640: r1 = <Widget>
    //     0x14dd640: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd644: r0 = AllocateGrowableArray()
    //     0x14dd644: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd648: mov             x1, x0
    // 0x14dd64c: ldur            x0, [fp, #-0x20]
    // 0x14dd650: stur            x1, [fp, #-0x28]
    // 0x14dd654: StoreField: r1->field_f = r0
    //     0x14dd654: stur            w0, [x1, #0xf]
    // 0x14dd658: r2 = 4
    //     0x14dd658: movz            x2, #0x4
    // 0x14dd65c: StoreField: r1->field_b = r2
    //     0x14dd65c: stur            w2, [x1, #0xb]
    // 0x14dd660: r0 = Stack()
    //     0x14dd660: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14dd664: mov             x1, x0
    // 0x14dd668: r0 = Instance_Alignment
    //     0x14dd668: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14dd66c: ldr             x0, [x0, #0xb10]
    // 0x14dd670: stur            x1, [fp, #-0x20]
    // 0x14dd674: StoreField: r1->field_f = r0
    //     0x14dd674: stur            w0, [x1, #0xf]
    // 0x14dd678: r0 = Instance_StackFit
    //     0x14dd678: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14dd67c: ldr             x0, [x0, #0xfa8]
    // 0x14dd680: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dd680: stur            w0, [x1, #0x17]
    // 0x14dd684: r0 = Instance_Clip
    //     0x14dd684: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14dd688: ldr             x0, [x0, #0x7e0]
    // 0x14dd68c: StoreField: r1->field_1b = r0
    //     0x14dd68c: stur            w0, [x1, #0x1b]
    // 0x14dd690: ldur            x0, [fp, #-0x28]
    // 0x14dd694: StoreField: r1->field_b = r0
    //     0x14dd694: stur            w0, [x1, #0xb]
    // 0x14dd698: r0 = Container()
    //     0x14dd698: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dd69c: mov             x1, x0
    // 0x14dd6a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14dd6a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14dd6a4: r0 = Container()
    //     0x14dd6a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dd6a8: r0 = Accordion()
    //     0x14dd6a8: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0x14dd6ac: mov             x1, x0
    // 0x14dd6b0: ldur            x0, [fp, #-0x10]
    // 0x14dd6b4: stur            x1, [fp, #-0x28]
    // 0x14dd6b8: StoreField: r1->field_b = r0
    //     0x14dd6b8: stur            w0, [x1, #0xb]
    // 0x14dd6bc: ldur            x0, [fp, #-0x20]
    // 0x14dd6c0: StoreField: r1->field_13 = r0
    //     0x14dd6c0: stur            w0, [x1, #0x13]
    // 0x14dd6c4: r0 = false
    //     0x14dd6c4: add             x0, NULL, #0x30  ; false
    // 0x14dd6c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dd6c8: stur            w0, [x1, #0x17]
    // 0x14dd6cc: d0 = 25.000000
    //     0x14dd6cc: fmov            d0, #25.00000000
    // 0x14dd6d0: StoreField: r1->field_1b = d0
    //     0x14dd6d0: stur            d0, [x1, #0x1b]
    // 0x14dd6d4: r0 = true
    //     0x14dd6d4: add             x0, NULL, #0x20  ; true
    // 0x14dd6d8: StoreField: r1->field_23 = r0
    //     0x14dd6d8: stur            w0, [x1, #0x23]
    // 0x14dd6dc: r0 = Container()
    //     0x14dd6dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dd6e0: stur            x0, [fp, #-0x10]
    // 0x14dd6e4: ldur            x16, [fp, #-0x18]
    // 0x14dd6e8: ldur            lr, [fp, #-0x28]
    // 0x14dd6ec: stp             lr, x16, [SP]
    // 0x14dd6f0: mov             x1, x0
    // 0x14dd6f4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14dd6f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14dd6f8: ldr             x4, [x4, #0x88]
    // 0x14dd6fc: r0 = Container()
    //     0x14dd6fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dd700: r0 = Padding()
    //     0x14dd700: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dd704: mov             x2, x0
    // 0x14dd708: r0 = Instance_EdgeInsets
    //     0x14dd708: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x14dd70c: ldr             x0, [x0, #0x778]
    // 0x14dd710: stur            x2, [fp, #-0x18]
    // 0x14dd714: StoreField: r2->field_f = r0
    //     0x14dd714: stur            w0, [x2, #0xf]
    // 0x14dd718: ldur            x0, [fp, #-0x10]
    // 0x14dd71c: StoreField: r2->field_b = r0
    //     0x14dd71c: stur            w0, [x2, #0xb]
    // 0x14dd720: ldur            x0, [fp, #-8]
    // 0x14dd724: LoadField: r1 = r0->field_f
    //     0x14dd724: ldur            w1, [x0, #0xf]
    // 0x14dd728: DecompressPointer r1
    //     0x14dd728: add             x1, x1, HEAP, lsl #32
    // 0x14dd72c: r0 = controller()
    //     0x14dd72c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dd730: LoadField: r1 = r0->field_53
    //     0x14dd730: ldur            w1, [x0, #0x53]
    // 0x14dd734: DecompressPointer r1
    //     0x14dd734: add             x1, x1, HEAP, lsl #32
    // 0x14dd738: r0 = value()
    //     0x14dd738: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dd73c: LoadField: r1 = r0->field_2f
    //     0x14dd73c: ldur            w1, [x0, #0x2f]
    // 0x14dd740: DecompressPointer r1
    //     0x14dd740: add             x1, x1, HEAP, lsl #32
    // 0x14dd744: cmp             w1, NULL
    // 0x14dd748: b.ne            #0x14dd754
    // 0x14dd74c: r0 = ""
    //     0x14dd74c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dd750: b               #0x14dd758
    // 0x14dd754: mov             x0, x1
    // 0x14dd758: ldur            x2, [fp, #-8]
    // 0x14dd75c: stur            x0, [fp, #-0x10]
    // 0x14dd760: LoadField: r1 = r2->field_13
    //     0x14dd760: ldur            w1, [x2, #0x13]
    // 0x14dd764: DecompressPointer r1
    //     0x14dd764: add             x1, x1, HEAP, lsl #32
    // 0x14dd768: r0 = of()
    //     0x14dd768: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dd76c: LoadField: r1 = r0->field_87
    //     0x14dd76c: ldur            w1, [x0, #0x87]
    // 0x14dd770: DecompressPointer r1
    //     0x14dd770: add             x1, x1, HEAP, lsl #32
    // 0x14dd774: LoadField: r0 = r1->field_7
    //     0x14dd774: ldur            w0, [x1, #7]
    // 0x14dd778: DecompressPointer r0
    //     0x14dd778: add             x0, x0, HEAP, lsl #32
    // 0x14dd77c: r16 = Instance_Color
    //     0x14dd77c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dd780: r30 = 16.000000
    //     0x14dd780: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14dd784: ldr             lr, [lr, #0x188]
    // 0x14dd788: stp             lr, x16, [SP]
    // 0x14dd78c: mov             x1, x0
    // 0x14dd790: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dd790: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dd794: ldr             x4, [x4, #0x9b8]
    // 0x14dd798: r0 = copyWith()
    //     0x14dd798: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dd79c: stur            x0, [fp, #-0x20]
    // 0x14dd7a0: r0 = Text()
    //     0x14dd7a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14dd7a4: mov             x1, x0
    // 0x14dd7a8: ldur            x0, [fp, #-0x10]
    // 0x14dd7ac: stur            x1, [fp, #-0x28]
    // 0x14dd7b0: StoreField: r1->field_b = r0
    //     0x14dd7b0: stur            w0, [x1, #0xb]
    // 0x14dd7b4: ldur            x0, [fp, #-0x20]
    // 0x14dd7b8: StoreField: r1->field_13 = r0
    //     0x14dd7b8: stur            w0, [x1, #0x13]
    // 0x14dd7bc: r0 = SvgPicture()
    //     0x14dd7bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14dd7c0: stur            x0, [fp, #-0x10]
    // 0x14dd7c4: r16 = "return order"
    //     0x14dd7c4: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x14dd7c8: ldr             x16, [x16, #0xc78]
    // 0x14dd7cc: r30 = Instance_BoxFit
    //     0x14dd7cc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14dd7d0: ldr             lr, [lr, #0xb18]
    // 0x14dd7d4: stp             lr, x16, [SP]
    // 0x14dd7d8: mov             x1, x0
    // 0x14dd7dc: r2 = "assets/images/secure_icon.svg"
    //     0x14dd7dc: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x14dd7e0: ldr             x2, [x2, #0xc80]
    // 0x14dd7e4: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x14dd7e4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x14dd7e8: ldr             x4, [x4, #0xb28]
    // 0x14dd7ec: r0 = SvgPicture.asset()
    //     0x14dd7ec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14dd7f0: r1 = Null
    //     0x14dd7f0: mov             x1, NULL
    // 0x14dd7f4: r2 = 4
    //     0x14dd7f4: movz            x2, #0x4
    // 0x14dd7f8: r0 = AllocateArray()
    //     0x14dd7f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd7fc: mov             x2, x0
    // 0x14dd800: ldur            x0, [fp, #-0x28]
    // 0x14dd804: stur            x2, [fp, #-0x20]
    // 0x14dd808: StoreField: r2->field_f = r0
    //     0x14dd808: stur            w0, [x2, #0xf]
    // 0x14dd80c: ldur            x0, [fp, #-0x10]
    // 0x14dd810: StoreField: r2->field_13 = r0
    //     0x14dd810: stur            w0, [x2, #0x13]
    // 0x14dd814: r1 = <Widget>
    //     0x14dd814: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dd818: r0 = AllocateGrowableArray()
    //     0x14dd818: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dd81c: mov             x1, x0
    // 0x14dd820: ldur            x0, [fp, #-0x20]
    // 0x14dd824: stur            x1, [fp, #-0x10]
    // 0x14dd828: StoreField: r1->field_f = r0
    //     0x14dd828: stur            w0, [x1, #0xf]
    // 0x14dd82c: r0 = 4
    //     0x14dd82c: movz            x0, #0x4
    // 0x14dd830: StoreField: r1->field_b = r0
    //     0x14dd830: stur            w0, [x1, #0xb]
    // 0x14dd834: r0 = Row()
    //     0x14dd834: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14dd838: mov             x1, x0
    // 0x14dd83c: r0 = Instance_Axis
    //     0x14dd83c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14dd840: stur            x1, [fp, #-0x20]
    // 0x14dd844: StoreField: r1->field_f = r0
    //     0x14dd844: stur            w0, [x1, #0xf]
    // 0x14dd848: r0 = Instance_MainAxisAlignment
    //     0x14dd848: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14dd84c: ldr             x0, [x0, #0xa8]
    // 0x14dd850: StoreField: r1->field_13 = r0
    //     0x14dd850: stur            w0, [x1, #0x13]
    // 0x14dd854: r0 = Instance_MainAxisSize
    //     0x14dd854: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dd858: ldr             x0, [x0, #0xa10]
    // 0x14dd85c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dd85c: stur            w0, [x1, #0x17]
    // 0x14dd860: r0 = Instance_CrossAxisAlignment
    //     0x14dd860: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14dd864: ldr             x0, [x0, #0xa18]
    // 0x14dd868: StoreField: r1->field_1b = r0
    //     0x14dd868: stur            w0, [x1, #0x1b]
    // 0x14dd86c: r0 = Instance_VerticalDirection
    //     0x14dd86c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dd870: ldr             x0, [x0, #0xa20]
    // 0x14dd874: StoreField: r1->field_23 = r0
    //     0x14dd874: stur            w0, [x1, #0x23]
    // 0x14dd878: r0 = Instance_Clip
    //     0x14dd878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dd87c: ldr             x0, [x0, #0x38]
    // 0x14dd880: StoreField: r1->field_2b = r0
    //     0x14dd880: stur            w0, [x1, #0x2b]
    // 0x14dd884: StoreField: r1->field_2f = rZR
    //     0x14dd884: stur            xzr, [x1, #0x2f]
    // 0x14dd888: ldur            x0, [fp, #-0x10]
    // 0x14dd88c: StoreField: r1->field_b = r0
    //     0x14dd88c: stur            w0, [x1, #0xb]
    // 0x14dd890: r0 = Padding()
    //     0x14dd890: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dd894: mov             x1, x0
    // 0x14dd898: r0 = Instance_EdgeInsets
    //     0x14dd898: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x14dd89c: ldr             x0, [x0, #0x100]
    // 0x14dd8a0: stur            x1, [fp, #-0x10]
    // 0x14dd8a4: StoreField: r1->field_f = r0
    //     0x14dd8a4: stur            w0, [x1, #0xf]
    // 0x14dd8a8: ldur            x0, [fp, #-0x20]
    // 0x14dd8ac: StoreField: r1->field_b = r0
    //     0x14dd8ac: stur            w0, [x1, #0xb]
    // 0x14dd8b0: r0 = Radius()
    //     0x14dd8b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14dd8b4: d0 = 12.000000
    //     0x14dd8b4: fmov            d0, #12.00000000
    // 0x14dd8b8: stur            x0, [fp, #-0x20]
    // 0x14dd8bc: StoreField: r0->field_7 = d0
    //     0x14dd8bc: stur            d0, [x0, #7]
    // 0x14dd8c0: StoreField: r0->field_f = d0
    //     0x14dd8c0: stur            d0, [x0, #0xf]
    // 0x14dd8c4: r0 = BorderRadius()
    //     0x14dd8c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14dd8c8: mov             x2, x0
    // 0x14dd8cc: ldur            x0, [fp, #-0x20]
    // 0x14dd8d0: stur            x2, [fp, #-0x28]
    // 0x14dd8d4: StoreField: r2->field_7 = r0
    //     0x14dd8d4: stur            w0, [x2, #7]
    // 0x14dd8d8: StoreField: r2->field_b = r0
    //     0x14dd8d8: stur            w0, [x2, #0xb]
    // 0x14dd8dc: StoreField: r2->field_f = r0
    //     0x14dd8dc: stur            w0, [x2, #0xf]
    // 0x14dd8e0: StoreField: r2->field_13 = r0
    //     0x14dd8e0: stur            w0, [x2, #0x13]
    // 0x14dd8e4: ldur            x0, [fp, #-8]
    // 0x14dd8e8: LoadField: r1 = r0->field_13
    //     0x14dd8e8: ldur            w1, [x0, #0x13]
    // 0x14dd8ec: DecompressPointer r1
    //     0x14dd8ec: add             x1, x1, HEAP, lsl #32
    // 0x14dd8f0: r0 = of()
    //     0x14dd8f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dd8f4: LoadField: r1 = r0->field_5b
    //     0x14dd8f4: ldur            w1, [x0, #0x5b]
    // 0x14dd8f8: DecompressPointer r1
    //     0x14dd8f8: add             x1, x1, HEAP, lsl #32
    // 0x14dd8fc: r0 = LoadClassIdInstr(r1)
    //     0x14dd8fc: ldur            x0, [x1, #-1]
    //     0x14dd900: ubfx            x0, x0, #0xc, #0x14
    // 0x14dd904: d0 = 0.100000
    //     0x14dd904: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14dd908: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14dd908: sub             lr, x0, #0xffa
    //     0x14dd90c: ldr             lr, [x21, lr, lsl #3]
    //     0x14dd910: blr             lr
    // 0x14dd914: mov             x2, x0
    // 0x14dd918: r1 = Null
    //     0x14dd918: mov             x1, NULL
    // 0x14dd91c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14dd91c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14dd920: r0 = Border.all()
    //     0x14dd920: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14dd924: stur            x0, [fp, #-0x20]
    // 0x14dd928: r0 = BoxDecoration()
    //     0x14dd928: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14dd92c: mov             x2, x0
    // 0x14dd930: ldur            x0, [fp, #-0x20]
    // 0x14dd934: stur            x2, [fp, #-0x30]
    // 0x14dd938: StoreField: r2->field_f = r0
    //     0x14dd938: stur            w0, [x2, #0xf]
    // 0x14dd93c: ldur            x0, [fp, #-0x28]
    // 0x14dd940: StoreField: r2->field_13 = r0
    //     0x14dd940: stur            w0, [x2, #0x13]
    // 0x14dd944: r0 = Instance_BoxShape
    //     0x14dd944: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14dd948: ldr             x0, [x0, #0x80]
    // 0x14dd94c: StoreField: r2->field_23 = r0
    //     0x14dd94c: stur            w0, [x2, #0x23]
    // 0x14dd950: ldur            x0, [fp, #-8]
    // 0x14dd954: LoadField: r1 = r0->field_f
    //     0x14dd954: ldur            w1, [x0, #0xf]
    // 0x14dd958: DecompressPointer r1
    //     0x14dd958: add             x1, x1, HEAP, lsl #32
    // 0x14dd95c: r0 = controller()
    //     0x14dd95c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dd960: LoadField: r1 = r0->field_53
    //     0x14dd960: ldur            w1, [x0, #0x53]
    // 0x14dd964: DecompressPointer r1
    //     0x14dd964: add             x1, x1, HEAP, lsl #32
    // 0x14dd968: r0 = value()
    //     0x14dd968: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dd96c: LoadField: r1 = r0->field_1f
    //     0x14dd96c: ldur            w1, [x0, #0x1f]
    // 0x14dd970: DecompressPointer r1
    //     0x14dd970: add             x1, x1, HEAP, lsl #32
    // 0x14dd974: cmp             w1, NULL
    // 0x14dd978: b.ne            #0x14dd990
    // 0x14dd97c: r1 = <PaymentOptions>
    //     0x14dd97c: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x14dd980: ldr             x1, [x1, #0x5a8]
    // 0x14dd984: r2 = 0
    //     0x14dd984: movz            x2, #0
    // 0x14dd988: r0 = AllocateArray()
    //     0x14dd988: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dd98c: b               #0x14dd994
    // 0x14dd990: mov             x0, x1
    // 0x14dd994: ldur            x2, [fp, #-8]
    // 0x14dd998: ldur            x3, [fp, #-0x18]
    // 0x14dd99c: ldur            x1, [fp, #-0x10]
    // 0x14dd9a0: r4 = LoadClassIdInstr(r0)
    //     0x14dd9a0: ldur            x4, [x0, #-1]
    //     0x14dd9a4: ubfx            x4, x4, #0xc, #0x14
    // 0x14dd9a8: str             x0, [SP]
    // 0x14dd9ac: mov             x0, x4
    // 0x14dd9b0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14dd9b0: movz            x17, #0xc898
    //     0x14dd9b4: add             lr, x0, x17
    //     0x14dd9b8: ldr             lr, [x21, lr, lsl #3]
    //     0x14dd9bc: blr             lr
    // 0x14dd9c0: r3 = LoadInt32Instr(r0)
    //     0x14dd9c0: sbfx            x3, x0, #1, #0x1f
    // 0x14dd9c4: ldur            x2, [fp, #-8]
    // 0x14dd9c8: stur            x3, [fp, #-0x70]
    // 0x14dd9cc: r1 = Function '<anonymous closure>':.
    //     0x14dd9cc: add             x1, PP, #0x40, lsl #12  ; [pp+0x409d0] AnonymousClosure: (0x14ddb88), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x14dbce0)
    //     0x14dd9d0: ldr             x1, [x1, #0x9d0]
    // 0x14dd9d4: r0 = AllocateClosure()
    //     0x14dd9d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dd9d8: r1 = Function '<anonymous closure>':.
    //     0x14dd9d8: add             x1, PP, #0x40, lsl #12  ; [pp+0x409d8] AnonymousClosure: (0xbc3e50), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_screen.dart] ExchangeCheckoutScreen::body (0x15059dc)
    //     0x14dd9dc: ldr             x1, [x1, #0x9d8]
    // 0x14dd9e0: r2 = Null
    //     0x14dd9e0: mov             x2, NULL
    // 0x14dd9e4: stur            x0, [fp, #-0x20]
    // 0x14dd9e8: r0 = AllocateClosure()
    //     0x14dd9e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dd9ec: stur            x0, [fp, #-0x28]
    // 0x14dd9f0: r0 = ListView()
    //     0x14dd9f0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14dd9f4: stur            x0, [fp, #-0x38]
    // 0x14dd9f8: r16 = true
    //     0x14dd9f8: add             x16, NULL, #0x20  ; true
    // 0x14dd9fc: r30 = false
    //     0x14dd9fc: add             lr, NULL, #0x30  ; false
    // 0x14dda00: stp             lr, x16, [SP]
    // 0x14dda04: mov             x1, x0
    // 0x14dda08: ldur            x2, [fp, #-0x20]
    // 0x14dda0c: ldur            x3, [fp, #-0x70]
    // 0x14dda10: ldur            x5, [fp, #-0x28]
    // 0x14dda14: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x14dda14: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14dda18: ldr             x4, [x4, #0xc98]
    // 0x14dda1c: r0 = ListView.separated()
    //     0x14dda1c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14dda20: r0 = Container()
    //     0x14dda20: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14dda24: stur            x0, [fp, #-0x20]
    // 0x14dda28: ldur            x16, [fp, #-0x30]
    // 0x14dda2c: ldur            lr, [fp, #-0x38]
    // 0x14dda30: stp             lr, x16, [SP]
    // 0x14dda34: mov             x1, x0
    // 0x14dda38: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14dda38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14dda3c: ldr             x4, [x4, #0x88]
    // 0x14dda40: r0 = Container()
    //     0x14dda40: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14dda44: r0 = Padding()
    //     0x14dda44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dda48: mov             x2, x0
    // 0x14dda4c: r0 = Instance_EdgeInsets
    //     0x14dda4c: add             x0, PP, #0x40, lsl #12  ; [pp+0x409e0] Obj!EdgeInsets@d58bb1
    //     0x14dda50: ldr             x0, [x0, #0x9e0]
    // 0x14dda54: stur            x2, [fp, #-0x28]
    // 0x14dda58: StoreField: r2->field_f = r0
    //     0x14dda58: stur            w0, [x2, #0xf]
    // 0x14dda5c: ldur            x0, [fp, #-0x20]
    // 0x14dda60: StoreField: r2->field_b = r0
    //     0x14dda60: stur            w0, [x2, #0xb]
    // 0x14dda64: ldur            x0, [fp, #-8]
    // 0x14dda68: LoadField: r1 = r0->field_f
    //     0x14dda68: ldur            w1, [x0, #0xf]
    // 0x14dda6c: DecompressPointer r1
    //     0x14dda6c: add             x1, x1, HEAP, lsl #32
    // 0x14dda70: r0 = controller()
    //     0x14dda70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dda74: LoadField: r1 = r0->field_7b
    //     0x14dda74: ldur            w1, [x0, #0x7b]
    // 0x14dda78: DecompressPointer r1
    //     0x14dda78: add             x1, x1, HEAP, lsl #32
    // 0x14dda7c: r0 = value()
    //     0x14dda7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dda80: stur            x0, [fp, #-8]
    // 0x14dda84: r0 = PaymentTrustMarkers()
    //     0x14dda84: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x14dda88: mov             x3, x0
    // 0x14dda8c: r0 = true
    //     0x14dda8c: add             x0, NULL, #0x20  ; true
    // 0x14dda90: stur            x3, [fp, #-0x20]
    // 0x14dda94: StoreField: r3->field_b = r0
    //     0x14dda94: stur            w0, [x3, #0xb]
    // 0x14dda98: ldur            x0, [fp, #-8]
    // 0x14dda9c: StoreField: r3->field_f = r0
    //     0x14dda9c: stur            w0, [x3, #0xf]
    // 0x14ddaa0: r1 = Null
    //     0x14ddaa0: mov             x1, NULL
    // 0x14ddaa4: r2 = 8
    //     0x14ddaa4: movz            x2, #0x8
    // 0x14ddaa8: r0 = AllocateArray()
    //     0x14ddaa8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ddaac: mov             x2, x0
    // 0x14ddab0: ldur            x0, [fp, #-0x18]
    // 0x14ddab4: stur            x2, [fp, #-8]
    // 0x14ddab8: StoreField: r2->field_f = r0
    //     0x14ddab8: stur            w0, [x2, #0xf]
    // 0x14ddabc: ldur            x0, [fp, #-0x10]
    // 0x14ddac0: StoreField: r2->field_13 = r0
    //     0x14ddac0: stur            w0, [x2, #0x13]
    // 0x14ddac4: ldur            x0, [fp, #-0x28]
    // 0x14ddac8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ddac8: stur            w0, [x2, #0x17]
    // 0x14ddacc: ldur            x0, [fp, #-0x20]
    // 0x14ddad0: StoreField: r2->field_1b = r0
    //     0x14ddad0: stur            w0, [x2, #0x1b]
    // 0x14ddad4: r1 = <Widget>
    //     0x14ddad4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ddad8: r0 = AllocateGrowableArray()
    //     0x14ddad8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ddadc: mov             x1, x0
    // 0x14ddae0: ldur            x0, [fp, #-8]
    // 0x14ddae4: stur            x1, [fp, #-0x10]
    // 0x14ddae8: StoreField: r1->field_f = r0
    //     0x14ddae8: stur            w0, [x1, #0xf]
    // 0x14ddaec: r0 = 8
    //     0x14ddaec: movz            x0, #0x8
    // 0x14ddaf0: StoreField: r1->field_b = r0
    //     0x14ddaf0: stur            w0, [x1, #0xb]
    // 0x14ddaf4: r0 = ListView()
    //     0x14ddaf4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14ddaf8: stur            x0, [fp, #-8]
    // 0x14ddafc: r16 = Instance_EdgeInsets
    //     0x14ddafc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0x14ddb00: ldr             x16, [x16, #0xd0]
    // 0x14ddb04: r30 = true
    //     0x14ddb04: add             lr, NULL, #0x20  ; true
    // 0x14ddb08: stp             lr, x16, [SP, #0x10]
    // 0x14ddb0c: r16 = false
    //     0x14ddb0c: add             x16, NULL, #0x30  ; false
    // 0x14ddb10: r30 = Instance_RangeMaintainingScrollPhysics
    //     0x14ddb10: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x14ddb14: ldr             lr, [lr, #0xfa0]
    // 0x14ddb18: stp             lr, x16, [SP]
    // 0x14ddb1c: mov             x1, x0
    // 0x14ddb20: ldur            x2, [fp, #-0x10]
    // 0x14ddb24: r4 = const [0, 0x6, 0x4, 0x2, padding, 0x2, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0x14ddb24: add             x4, PP, #0x40, lsl #12  ; [pp+0x409e8] List(13) [0, 0x6, 0x4, 0x2, "padding", 0x2, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14ddb28: ldr             x4, [x4, #0x9e8]
    // 0x14ddb2c: r0 = ListView()
    //     0x14ddb2c: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x14ddb30: ldur            x0, [fp, #-8]
    // 0x14ddb34: LeaveFrame
    //     0x14ddb34: mov             SP, fp
    //     0x14ddb38: ldp             fp, lr, [SP], #0x10
    // 0x14ddb3c: ret
    //     0x14ddb3c: ret             
    // 0x14ddb40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ddb40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ddb44: b               #0x14dbd6c
    // 0x14ddb48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ddb84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddb84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14ddb88, size: 0xd4
    // 0x14ddb88: EnterFrame
    //     0x14ddb88: stp             fp, lr, [SP, #-0x10]!
    //     0x14ddb8c: mov             fp, SP
    // 0x14ddb90: AllocStack(0x8)
    //     0x14ddb90: sub             SP, SP, #8
    // 0x14ddb94: SetupParameters()
    //     0x14ddb94: ldr             x0, [fp, #0x20]
    //     0x14ddb98: ldur            w1, [x0, #0x17]
    //     0x14ddb9c: add             x1, x1, HEAP, lsl #32
    // 0x14ddba0: CheckStackOverflow
    //     0x14ddba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ddba4: cmp             SP, x16
    //     0x14ddba8: b.ls            #0x14ddc50
    // 0x14ddbac: LoadField: r0 = r1->field_f
    //     0x14ddbac: ldur            w0, [x1, #0xf]
    // 0x14ddbb0: DecompressPointer r0
    //     0x14ddbb0: add             x0, x0, HEAP, lsl #32
    // 0x14ddbb4: mov             x1, x0
    // 0x14ddbb8: stur            x0, [fp, #-8]
    // 0x14ddbbc: r0 = controller()
    //     0x14ddbbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ddbc0: LoadField: r1 = r0->field_53
    //     0x14ddbc0: ldur            w1, [x0, #0x53]
    // 0x14ddbc4: DecompressPointer r1
    //     0x14ddbc4: add             x1, x1, HEAP, lsl #32
    // 0x14ddbc8: r0 = value()
    //     0x14ddbc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ddbcc: LoadField: r2 = r0->field_1f
    //     0x14ddbcc: ldur            w2, [x0, #0x1f]
    // 0x14ddbd0: DecompressPointer r2
    //     0x14ddbd0: add             x2, x2, HEAP, lsl #32
    // 0x14ddbd4: cmp             w2, NULL
    // 0x14ddbd8: b.ne            #0x14ddbe8
    // 0x14ddbdc: ldr             x3, [fp, #0x10]
    // 0x14ddbe0: r2 = Null
    //     0x14ddbe0: mov             x2, NULL
    // 0x14ddbe4: b               #0x14ddc28
    // 0x14ddbe8: ldr             x3, [fp, #0x10]
    // 0x14ddbec: LoadField: r0 = r2->field_b
    //     0x14ddbec: ldur            w0, [x2, #0xb]
    // 0x14ddbf0: r4 = LoadInt32Instr(r3)
    //     0x14ddbf0: sbfx            x4, x3, #1, #0x1f
    //     0x14ddbf4: tbz             w3, #0, #0x14ddbfc
    //     0x14ddbf8: ldur            x4, [x3, #7]
    // 0x14ddbfc: r1 = LoadInt32Instr(r0)
    //     0x14ddbfc: sbfx            x1, x0, #1, #0x1f
    // 0x14ddc00: mov             x0, x1
    // 0x14ddc04: mov             x1, x4
    // 0x14ddc08: cmp             x1, x0
    // 0x14ddc0c: b.hs            #0x14ddc58
    // 0x14ddc10: LoadField: r0 = r2->field_f
    //     0x14ddc10: ldur            w0, [x2, #0xf]
    // 0x14ddc14: DecompressPointer r0
    //     0x14ddc14: add             x0, x0, HEAP, lsl #32
    // 0x14ddc18: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14ddc18: add             x16, x0, x4, lsl #2
    //     0x14ddc1c: ldur            w1, [x16, #0xf]
    // 0x14ddc20: DecompressPointer r1
    //     0x14ddc20: add             x1, x1, HEAP, lsl #32
    // 0x14ddc24: mov             x2, x1
    // 0x14ddc28: r0 = LoadInt32Instr(r3)
    //     0x14ddc28: sbfx            x0, x3, #1, #0x1f
    //     0x14ddc2c: tbz             w3, #0, #0x14ddc34
    //     0x14ddc30: ldur            x0, [x3, #7]
    // 0x14ddc34: ldur            x1, [fp, #-8]
    // 0x14ddc38: mov             x3, x0
    // 0x14ddc3c: ldr             x5, [fp, #0x18]
    // 0x14ddc40: r0 = glassThemePaymentMethodCard()
    //     0x14ddc40: bl              #0x14ddc5c  ; [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::glassThemePaymentMethodCard
    // 0x14ddc44: LeaveFrame
    //     0x14ddc44: mov             SP, fp
    //     0x14ddc48: ldp             fp, lr, [SP], #0x10
    // 0x14ddc4c: ret
    //     0x14ddc4c: ret             
    // 0x14ddc50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ddc50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ddc54: b               #0x14ddbac
    // 0x14ddc58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ddc58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ glassThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x14ddc5c, size: 0x90
    // 0x14ddc5c: EnterFrame
    //     0x14ddc5c: stp             fp, lr, [SP, #-0x10]!
    //     0x14ddc60: mov             fp, SP
    // 0x14ddc64: AllocStack(0x28)
    //     0x14ddc64: sub             SP, SP, #0x28
    // 0x14ddc68: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x14ddc68: stur            x1, [fp, #-8]
    //     0x14ddc6c: stur            x2, [fp, #-0x10]
    //     0x14ddc70: stur            x3, [fp, #-0x18]
    //     0x14ddc74: stur            x5, [fp, #-0x20]
    // 0x14ddc78: r1 = 4
    //     0x14ddc78: movz            x1, #0x4
    // 0x14ddc7c: r0 = AllocateContext()
    //     0x14ddc7c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ddc80: mov             x2, x0
    // 0x14ddc84: ldur            x0, [fp, #-8]
    // 0x14ddc88: stur            x2, [fp, #-0x28]
    // 0x14ddc8c: StoreField: r2->field_f = r0
    //     0x14ddc8c: stur            w0, [x2, #0xf]
    // 0x14ddc90: ldur            x0, [fp, #-0x10]
    // 0x14ddc94: StoreField: r2->field_13 = r0
    //     0x14ddc94: stur            w0, [x2, #0x13]
    // 0x14ddc98: ldur            x3, [fp, #-0x18]
    // 0x14ddc9c: r0 = BoxInt64Instr(r3)
    //     0x14ddc9c: sbfiz           x0, x3, #1, #0x1f
    //     0x14ddca0: cmp             x3, x0, asr #1
    //     0x14ddca4: b.eq            #0x14ddcb0
    //     0x14ddca8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x14ddcac: stur            x3, [x0, #7]
    // 0x14ddcb0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ddcb0: stur            w0, [x2, #0x17]
    // 0x14ddcb4: ldur            x0, [fp, #-0x20]
    // 0x14ddcb8: StoreField: r2->field_1b = r0
    //     0x14ddcb8: stur            w0, [x2, #0x1b]
    // 0x14ddcbc: r0 = Obx()
    //     0x14ddcbc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14ddcc0: ldur            x2, [fp, #-0x28]
    // 0x14ddcc4: r1 = Function '<anonymous closure>':.
    //     0x14ddcc4: add             x1, PP, #0x40, lsl #12  ; [pp+0x409f0] AnonymousClosure: (0x14ddcec), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::glassThemePaymentMethodCard (0x14ddc5c)
    //     0x14ddcc8: ldr             x1, [x1, #0x9f0]
    // 0x14ddccc: stur            x0, [fp, #-8]
    // 0x14ddcd0: r0 = AllocateClosure()
    //     0x14ddcd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ddcd4: mov             x1, x0
    // 0x14ddcd8: ldur            x0, [fp, #-8]
    // 0x14ddcdc: StoreField: r0->field_b = r1
    //     0x14ddcdc: stur            w1, [x0, #0xb]
    // 0x14ddce0: LeaveFrame
    //     0x14ddce0: mov             SP, fp
    //     0x14ddce4: ldp             fp, lr, [SP], #0x10
    // 0x14ddce8: ret
    //     0x14ddce8: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x14ddcec, size: 0x3c4
    // 0x14ddcec: EnterFrame
    //     0x14ddcec: stp             fp, lr, [SP, #-0x10]!
    //     0x14ddcf0: mov             fp, SP
    // 0x14ddcf4: AllocStack(0x40)
    //     0x14ddcf4: sub             SP, SP, #0x40
    // 0x14ddcf8: SetupParameters()
    //     0x14ddcf8: ldr             x0, [fp, #0x10]
    //     0x14ddcfc: ldur            w2, [x0, #0x17]
    //     0x14ddd00: add             x2, x2, HEAP, lsl #32
    //     0x14ddd04: stur            x2, [fp, #-8]
    // 0x14ddd08: CheckStackOverflow
    //     0x14ddd08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ddd0c: cmp             SP, x16
    //     0x14ddd10: b.ls            #0x14de0a8
    // 0x14ddd14: LoadField: r1 = r2->field_f
    //     0x14ddd14: ldur            w1, [x2, #0xf]
    // 0x14ddd18: DecompressPointer r1
    //     0x14ddd18: add             x1, x1, HEAP, lsl #32
    // 0x14ddd1c: r0 = controller()
    //     0x14ddd1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ddd20: LoadField: r1 = r0->field_77
    //     0x14ddd20: ldur            w1, [x0, #0x77]
    // 0x14ddd24: DecompressPointer r1
    //     0x14ddd24: add             x1, x1, HEAP, lsl #32
    // 0x14ddd28: r0 = value()
    //     0x14ddd28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ddd2c: ldur            x2, [fp, #-8]
    // 0x14ddd30: LoadField: r1 = r2->field_13
    //     0x14ddd30: ldur            w1, [x2, #0x13]
    // 0x14ddd34: DecompressPointer r1
    //     0x14ddd34: add             x1, x1, HEAP, lsl #32
    // 0x14ddd38: cmp             w1, NULL
    // 0x14ddd3c: b.ne            #0x14ddd48
    // 0x14ddd40: r1 = Null
    //     0x14ddd40: mov             x1, NULL
    // 0x14ddd44: b               #0x14ddd54
    // 0x14ddd48: LoadField: r3 = r1->field_f
    //     0x14ddd48: ldur            w3, [x1, #0xf]
    // 0x14ddd4c: DecompressPointer r3
    //     0x14ddd4c: add             x3, x3, HEAP, lsl #32
    // 0x14ddd50: mov             x1, x3
    // 0x14ddd54: r3 = 60
    //     0x14ddd54: movz            x3, #0x3c
    // 0x14ddd58: branchIfSmi(r0, 0x14ddd64)
    //     0x14ddd58: tbz             w0, #0, #0x14ddd64
    // 0x14ddd5c: r3 = LoadClassIdInstr(r0)
    //     0x14ddd5c: ldur            x3, [x0, #-1]
    //     0x14ddd60: ubfx            x3, x3, #0xc, #0x14
    // 0x14ddd64: stp             x1, x0, [SP]
    // 0x14ddd68: mov             x0, x3
    // 0x14ddd6c: mov             lr, x0
    // 0x14ddd70: ldr             lr, [x21, lr, lsl #3]
    // 0x14ddd74: blr             lr
    // 0x14ddd78: tbnz            w0, #4, #0x14dddd0
    // 0x14ddd7c: ldur            x2, [fp, #-8]
    // 0x14ddd80: LoadField: r1 = r2->field_f
    //     0x14ddd80: ldur            w1, [x2, #0xf]
    // 0x14ddd84: DecompressPointer r1
    //     0x14ddd84: add             x1, x1, HEAP, lsl #32
    // 0x14ddd88: r0 = controller()
    //     0x14ddd88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ddd8c: LoadField: r1 = r0->field_97
    //     0x14ddd8c: ldur            w1, [x0, #0x97]
    // 0x14ddd90: DecompressPointer r1
    //     0x14ddd90: add             x1, x1, HEAP, lsl #32
    // 0x14ddd94: r0 = value()
    //     0x14ddd94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14ddd98: ldur            x2, [fp, #-8]
    // 0x14ddd9c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14ddd9c: ldur            w1, [x2, #0x17]
    // 0x14ddda0: DecompressPointer r1
    //     0x14ddda0: add             x1, x1, HEAP, lsl #32
    // 0x14ddda4: r3 = LoadInt32Instr(r0)
    //     0x14ddda4: sbfx            x3, x0, #1, #0x1f
    //     0x14ddda8: tbz             w0, #0, #0x14dddb0
    //     0x14dddac: ldur            x3, [x0, #7]
    // 0x14dddb0: r0 = LoadInt32Instr(r1)
    //     0x14dddb0: sbfx            x0, x1, #1, #0x1f
    //     0x14dddb4: tbz             w1, #0, #0x14dddbc
    //     0x14dddb8: ldur            x0, [x1, #7]
    // 0x14dddbc: cmp             x3, x0
    // 0x14dddc0: b.ne            #0x14dddd4
    // 0x14dddc4: r0 = Instance_IconData
    //     0x14dddc4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x14dddc8: ldr             x0, [x0, #0x30]
    // 0x14dddcc: b               #0x14ddddc
    // 0x14dddd0: ldur            x2, [fp, #-8]
    // 0x14dddd4: r0 = Instance_IconData
    //     0x14dddd4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x14dddd8: ldr             x0, [x0, #0x38]
    // 0x14ddddc: stur            x0, [fp, #-0x10]
    // 0x14ddde0: LoadField: r1 = r2->field_1b
    //     0x14ddde0: ldur            w1, [x2, #0x1b]
    // 0x14ddde4: DecompressPointer r1
    //     0x14ddde4: add             x1, x1, HEAP, lsl #32
    // 0x14ddde8: r0 = of()
    //     0x14ddde8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dddec: LoadField: r1 = r0->field_5b
    //     0x14dddec: ldur            w1, [x0, #0x5b]
    // 0x14dddf0: DecompressPointer r1
    //     0x14dddf0: add             x1, x1, HEAP, lsl #32
    // 0x14dddf4: stur            x1, [fp, #-0x18]
    // 0x14dddf8: r0 = Icon()
    //     0x14dddf8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14dddfc: mov             x1, x0
    // 0x14dde00: ldur            x0, [fp, #-0x10]
    // 0x14dde04: stur            x1, [fp, #-0x20]
    // 0x14dde08: StoreField: r1->field_b = r0
    //     0x14dde08: stur            w0, [x1, #0xb]
    // 0x14dde0c: ldur            x0, [fp, #-0x18]
    // 0x14dde10: StoreField: r1->field_23 = r0
    //     0x14dde10: stur            w0, [x1, #0x23]
    // 0x14dde14: ldur            x2, [fp, #-8]
    // 0x14dde18: LoadField: r0 = r2->field_13
    //     0x14dde18: ldur            w0, [x2, #0x13]
    // 0x14dde1c: DecompressPointer r0
    //     0x14dde1c: add             x0, x0, HEAP, lsl #32
    // 0x14dde20: cmp             w0, NULL
    // 0x14dde24: b.ne            #0x14dde30
    // 0x14dde28: r0 = Null
    //     0x14dde28: mov             x0, NULL
    // 0x14dde2c: b               #0x14dde3c
    // 0x14dde30: LoadField: r3 = r0->field_7
    //     0x14dde30: ldur            w3, [x0, #7]
    // 0x14dde34: DecompressPointer r3
    //     0x14dde34: add             x3, x3, HEAP, lsl #32
    // 0x14dde38: mov             x0, x3
    // 0x14dde3c: cmp             w0, NULL
    // 0x14dde40: b.ne            #0x14dde48
    // 0x14dde44: r0 = ""
    //     0x14dde44: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dde48: stur            x0, [fp, #-0x10]
    // 0x14dde4c: r0 = CachedNetworkImage()
    //     0x14dde4c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14dde50: stur            x0, [fp, #-0x18]
    // 0x14dde54: r16 = 48.000000
    //     0x14dde54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14dde58: ldr             x16, [x16, #0xad8]
    // 0x14dde5c: r30 = 48.000000
    //     0x14dde5c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14dde60: ldr             lr, [lr, #0xad8]
    // 0x14dde64: stp             lr, x16, [SP]
    // 0x14dde68: mov             x1, x0
    // 0x14dde6c: ldur            x2, [fp, #-0x10]
    // 0x14dde70: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x14dde70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x14dde74: ldr             x4, [x4, #0x900]
    // 0x14dde78: r0 = CachedNetworkImage()
    //     0x14dde78: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14dde7c: r0 = Padding()
    //     0x14dde7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dde80: mov             x2, x0
    // 0x14dde84: r0 = Instance_EdgeInsets
    //     0x14dde84: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x14dde88: ldr             x0, [x0, #0x9f8]
    // 0x14dde8c: stur            x2, [fp, #-0x28]
    // 0x14dde90: StoreField: r2->field_f = r0
    //     0x14dde90: stur            w0, [x2, #0xf]
    // 0x14dde94: ldur            x1, [fp, #-0x18]
    // 0x14dde98: StoreField: r2->field_b = r1
    //     0x14dde98: stur            w1, [x2, #0xb]
    // 0x14dde9c: ldur            x3, [fp, #-8]
    // 0x14ddea0: LoadField: r1 = r3->field_13
    //     0x14ddea0: ldur            w1, [x3, #0x13]
    // 0x14ddea4: DecompressPointer r1
    //     0x14ddea4: add             x1, x1, HEAP, lsl #32
    // 0x14ddea8: cmp             w1, NULL
    // 0x14ddeac: b.ne            #0x14ddeb8
    // 0x14ddeb0: r1 = Null
    //     0x14ddeb0: mov             x1, NULL
    // 0x14ddeb4: b               #0x14ddec4
    // 0x14ddeb8: LoadField: r4 = r1->field_b
    //     0x14ddeb8: ldur            w4, [x1, #0xb]
    // 0x14ddebc: DecompressPointer r4
    //     0x14ddebc: add             x4, x4, HEAP, lsl #32
    // 0x14ddec0: mov             x1, x4
    // 0x14ddec4: cmp             w1, NULL
    // 0x14ddec8: b.ne            #0x14dded4
    // 0x14ddecc: r5 = ""
    //     0x14ddecc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14dded0: b               #0x14dded8
    // 0x14dded4: mov             x5, x1
    // 0x14dded8: ldur            x4, [fp, #-0x20]
    // 0x14ddedc: stur            x5, [fp, #-0x10]
    // 0x14ddee0: LoadField: r1 = r3->field_1b
    //     0x14ddee0: ldur            w1, [x3, #0x1b]
    // 0x14ddee4: DecompressPointer r1
    //     0x14ddee4: add             x1, x1, HEAP, lsl #32
    // 0x14ddee8: r0 = of()
    //     0x14ddee8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ddeec: LoadField: r1 = r0->field_87
    //     0x14ddeec: ldur            w1, [x0, #0x87]
    // 0x14ddef0: DecompressPointer r1
    //     0x14ddef0: add             x1, x1, HEAP, lsl #32
    // 0x14ddef4: LoadField: r0 = r1->field_2b
    //     0x14ddef4: ldur            w0, [x1, #0x2b]
    // 0x14ddef8: DecompressPointer r0
    //     0x14ddef8: add             x0, x0, HEAP, lsl #32
    // 0x14ddefc: r16 = 14.000000
    //     0x14ddefc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14ddf00: ldr             x16, [x16, #0x1d8]
    // 0x14ddf04: r30 = Instance_Color
    //     0x14ddf04: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ddf08: stp             lr, x16, [SP]
    // 0x14ddf0c: mov             x1, x0
    // 0x14ddf10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14ddf10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14ddf14: ldr             x4, [x4, #0xaa0]
    // 0x14ddf18: r0 = copyWith()
    //     0x14ddf18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14ddf1c: stur            x0, [fp, #-0x18]
    // 0x14ddf20: r0 = Text()
    //     0x14ddf20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14ddf24: mov             x1, x0
    // 0x14ddf28: ldur            x0, [fp, #-0x10]
    // 0x14ddf2c: stur            x1, [fp, #-0x30]
    // 0x14ddf30: StoreField: r1->field_b = r0
    //     0x14ddf30: stur            w0, [x1, #0xb]
    // 0x14ddf34: ldur            x0, [fp, #-0x18]
    // 0x14ddf38: StoreField: r1->field_13 = r0
    //     0x14ddf38: stur            w0, [x1, #0x13]
    // 0x14ddf3c: r0 = Padding()
    //     0x14ddf3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ddf40: mov             x3, x0
    // 0x14ddf44: r0 = Instance_EdgeInsets
    //     0x14ddf44: add             x0, PP, #0x40, lsl #12  ; [pp+0x409f8] Obj!EdgeInsets@d59b41
    //     0x14ddf48: ldr             x0, [x0, #0x9f8]
    // 0x14ddf4c: stur            x3, [fp, #-0x10]
    // 0x14ddf50: StoreField: r3->field_f = r0
    //     0x14ddf50: stur            w0, [x3, #0xf]
    // 0x14ddf54: ldur            x0, [fp, #-0x30]
    // 0x14ddf58: StoreField: r3->field_b = r0
    //     0x14ddf58: stur            w0, [x3, #0xb]
    // 0x14ddf5c: r1 = Null
    //     0x14ddf5c: mov             x1, NULL
    // 0x14ddf60: r2 = 6
    //     0x14ddf60: movz            x2, #0x6
    // 0x14ddf64: r0 = AllocateArray()
    //     0x14ddf64: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ddf68: mov             x2, x0
    // 0x14ddf6c: ldur            x0, [fp, #-0x20]
    // 0x14ddf70: stur            x2, [fp, #-0x18]
    // 0x14ddf74: StoreField: r2->field_f = r0
    //     0x14ddf74: stur            w0, [x2, #0xf]
    // 0x14ddf78: ldur            x0, [fp, #-0x28]
    // 0x14ddf7c: StoreField: r2->field_13 = r0
    //     0x14ddf7c: stur            w0, [x2, #0x13]
    // 0x14ddf80: ldur            x0, [fp, #-0x10]
    // 0x14ddf84: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ddf84: stur            w0, [x2, #0x17]
    // 0x14ddf88: r1 = <Widget>
    //     0x14ddf88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ddf8c: r0 = AllocateGrowableArray()
    //     0x14ddf8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ddf90: mov             x1, x0
    // 0x14ddf94: ldur            x0, [fp, #-0x18]
    // 0x14ddf98: stur            x1, [fp, #-0x10]
    // 0x14ddf9c: StoreField: r1->field_f = r0
    //     0x14ddf9c: stur            w0, [x1, #0xf]
    // 0x14ddfa0: r0 = 6
    //     0x14ddfa0: movz            x0, #0x6
    // 0x14ddfa4: StoreField: r1->field_b = r0
    //     0x14ddfa4: stur            w0, [x1, #0xb]
    // 0x14ddfa8: r0 = Row()
    //     0x14ddfa8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14ddfac: mov             x1, x0
    // 0x14ddfb0: r0 = Instance_Axis
    //     0x14ddfb0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14ddfb4: stur            x1, [fp, #-0x18]
    // 0x14ddfb8: StoreField: r1->field_f = r0
    //     0x14ddfb8: stur            w0, [x1, #0xf]
    // 0x14ddfbc: r0 = Instance_MainAxisAlignment
    //     0x14ddfbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14ddfc0: ldr             x0, [x0, #0xa08]
    // 0x14ddfc4: StoreField: r1->field_13 = r0
    //     0x14ddfc4: stur            w0, [x1, #0x13]
    // 0x14ddfc8: r0 = Instance_MainAxisSize
    //     0x14ddfc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14ddfcc: ldr             x0, [x0, #0xa10]
    // 0x14ddfd0: ArrayStore: r1[0] = r0  ; List_4
    //     0x14ddfd0: stur            w0, [x1, #0x17]
    // 0x14ddfd4: r0 = Instance_CrossAxisAlignment
    //     0x14ddfd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14ddfd8: ldr             x0, [x0, #0xa18]
    // 0x14ddfdc: StoreField: r1->field_1b = r0
    //     0x14ddfdc: stur            w0, [x1, #0x1b]
    // 0x14ddfe0: r0 = Instance_VerticalDirection
    //     0x14ddfe0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14ddfe4: ldr             x0, [x0, #0xa20]
    // 0x14ddfe8: StoreField: r1->field_23 = r0
    //     0x14ddfe8: stur            w0, [x1, #0x23]
    // 0x14ddfec: r0 = Instance_Clip
    //     0x14ddfec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14ddff0: ldr             x0, [x0, #0x38]
    // 0x14ddff4: StoreField: r1->field_2b = r0
    //     0x14ddff4: stur            w0, [x1, #0x2b]
    // 0x14ddff8: StoreField: r1->field_2f = rZR
    //     0x14ddff8: stur            xzr, [x1, #0x2f]
    // 0x14ddffc: ldur            x0, [fp, #-0x10]
    // 0x14de000: StoreField: r1->field_b = r0
    //     0x14de000: stur            w0, [x1, #0xb]
    // 0x14de004: r0 = Center()
    //     0x14de004: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14de008: mov             x1, x0
    // 0x14de00c: r0 = Instance_Alignment
    //     0x14de00c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14de010: ldr             x0, [x0, #0xb10]
    // 0x14de014: stur            x1, [fp, #-0x10]
    // 0x14de018: StoreField: r1->field_f = r0
    //     0x14de018: stur            w0, [x1, #0xf]
    // 0x14de01c: ldur            x0, [fp, #-0x18]
    // 0x14de020: StoreField: r1->field_b = r0
    //     0x14de020: stur            w0, [x1, #0xb]
    // 0x14de024: r0 = Padding()
    //     0x14de024: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14de028: mov             x1, x0
    // 0x14de02c: r0 = Instance_EdgeInsets
    //     0x14de02c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40800] Obj!EdgeInsets@d59b11
    //     0x14de030: ldr             x0, [x0, #0x800]
    // 0x14de034: stur            x1, [fp, #-0x18]
    // 0x14de038: StoreField: r1->field_f = r0
    //     0x14de038: stur            w0, [x1, #0xf]
    // 0x14de03c: ldur            x0, [fp, #-0x10]
    // 0x14de040: StoreField: r1->field_b = r0
    //     0x14de040: stur            w0, [x1, #0xb]
    // 0x14de044: r0 = InkWell()
    //     0x14de044: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14de048: mov             x3, x0
    // 0x14de04c: ldur            x0, [fp, #-0x18]
    // 0x14de050: stur            x3, [fp, #-0x10]
    // 0x14de054: StoreField: r3->field_b = r0
    //     0x14de054: stur            w0, [x3, #0xb]
    // 0x14de058: ldur            x2, [fp, #-8]
    // 0x14de05c: r1 = Function '<anonymous closure>':.
    //     0x14de05c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a00] AnonymousClosure: (0x13c1b6c), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::lineThemePaymentMethodCard (0x13c1fc4)
    //     0x14de060: ldr             x1, [x1, #0xa00]
    // 0x14de064: r0 = AllocateClosure()
    //     0x14de064: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14de068: mov             x1, x0
    // 0x14de06c: ldur            x0, [fp, #-0x10]
    // 0x14de070: StoreField: r0->field_f = r1
    //     0x14de070: stur            w1, [x0, #0xf]
    // 0x14de074: r1 = true
    //     0x14de074: add             x1, NULL, #0x20  ; true
    // 0x14de078: StoreField: r0->field_43 = r1
    //     0x14de078: stur            w1, [x0, #0x43]
    // 0x14de07c: r2 = Instance_BoxShape
    //     0x14de07c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14de080: ldr             x2, [x2, #0x80]
    // 0x14de084: StoreField: r0->field_47 = r2
    //     0x14de084: stur            w2, [x0, #0x47]
    // 0x14de088: StoreField: r0->field_6f = r1
    //     0x14de088: stur            w1, [x0, #0x6f]
    // 0x14de08c: r2 = false
    //     0x14de08c: add             x2, NULL, #0x30  ; false
    // 0x14de090: StoreField: r0->field_73 = r2
    //     0x14de090: stur            w2, [x0, #0x73]
    // 0x14de094: StoreField: r0->field_83 = r1
    //     0x14de094: stur            w1, [x0, #0x83]
    // 0x14de098: StoreField: r0->field_7b = r2
    //     0x14de098: stur            w2, [x0, #0x7b]
    // 0x14de09c: LeaveFrame
    //     0x14de09c: mov             SP, fp
    //     0x14de0a0: ldp             fp, lr, [SP], #0x10
    // 0x14de0a4: ret
    //     0x14de0a4: ret             
    // 0x14de0a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14de0a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14de0ac: b               #0x14ddd14
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e18f8, size: 0x18c
    // 0x15e18f8: EnterFrame
    //     0x15e18f8: stp             fp, lr, [SP, #-0x10]!
    //     0x15e18fc: mov             fp, SP
    // 0x15e1900: AllocStack(0x28)
    //     0x15e1900: sub             SP, SP, #0x28
    // 0x15e1904: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e1904: mov             x0, x1
    //     0x15e1908: stur            x1, [fp, #-8]
    //     0x15e190c: mov             x1, x2
    //     0x15e1910: stur            x2, [fp, #-0x10]
    // 0x15e1914: CheckStackOverflow
    //     0x15e1914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1918: cmp             SP, x16
    //     0x15e191c: b.ls            #0x15e1a7c
    // 0x15e1920: r1 = 2
    //     0x15e1920: movz            x1, #0x2
    // 0x15e1924: r0 = AllocateContext()
    //     0x15e1924: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e1928: mov             x1, x0
    // 0x15e192c: ldur            x0, [fp, #-8]
    // 0x15e1930: stur            x1, [fp, #-0x18]
    // 0x15e1934: StoreField: r1->field_f = r0
    //     0x15e1934: stur            w0, [x1, #0xf]
    // 0x15e1938: ldur            x0, [fp, #-0x10]
    // 0x15e193c: StoreField: r1->field_13 = r0
    //     0x15e193c: stur            w0, [x1, #0x13]
    // 0x15e1940: r0 = Obx()
    //     0x15e1940: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e1944: ldur            x2, [fp, #-0x18]
    // 0x15e1948: r1 = Function '<anonymous closure>':.
    //     0x15e1948: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a08] AnonymousClosure: (0x15cf880), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15e194c: ldr             x1, [x1, #0xa08]
    // 0x15e1950: stur            x0, [fp, #-8]
    // 0x15e1954: r0 = AllocateClosure()
    //     0x15e1954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1958: mov             x1, x0
    // 0x15e195c: ldur            x0, [fp, #-8]
    // 0x15e1960: StoreField: r0->field_b = r1
    //     0x15e1960: stur            w1, [x0, #0xb]
    // 0x15e1964: ldur            x1, [fp, #-0x10]
    // 0x15e1968: r0 = of()
    //     0x15e1968: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e196c: LoadField: r1 = r0->field_5b
    //     0x15e196c: ldur            w1, [x0, #0x5b]
    // 0x15e1970: DecompressPointer r1
    //     0x15e1970: add             x1, x1, HEAP, lsl #32
    // 0x15e1974: stur            x1, [fp, #-0x10]
    // 0x15e1978: r0 = ColorFilter()
    //     0x15e1978: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e197c: mov             x1, x0
    // 0x15e1980: ldur            x0, [fp, #-0x10]
    // 0x15e1984: stur            x1, [fp, #-0x20]
    // 0x15e1988: StoreField: r1->field_7 = r0
    //     0x15e1988: stur            w0, [x1, #7]
    // 0x15e198c: r0 = Instance_BlendMode
    //     0x15e198c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1990: ldr             x0, [x0, #0xb30]
    // 0x15e1994: StoreField: r1->field_b = r0
    //     0x15e1994: stur            w0, [x1, #0xb]
    // 0x15e1998: r0 = 1
    //     0x15e1998: movz            x0, #0x1
    // 0x15e199c: StoreField: r1->field_13 = r0
    //     0x15e199c: stur            x0, [x1, #0x13]
    // 0x15e19a0: r0 = SvgPicture()
    //     0x15e19a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e19a4: stur            x0, [fp, #-0x10]
    // 0x15e19a8: ldur            x16, [fp, #-0x20]
    // 0x15e19ac: str             x16, [SP]
    // 0x15e19b0: mov             x1, x0
    // 0x15e19b4: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e19b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e19b8: ldr             x2, [x2, #0xa40]
    // 0x15e19bc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e19bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e19c0: ldr             x4, [x4, #0xa38]
    // 0x15e19c4: r0 = SvgPicture.asset()
    //     0x15e19c4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e19c8: r0 = Align()
    //     0x15e19c8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e19cc: mov             x1, x0
    // 0x15e19d0: r0 = Instance_Alignment
    //     0x15e19d0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e19d4: ldr             x0, [x0, #0xb10]
    // 0x15e19d8: stur            x1, [fp, #-0x20]
    // 0x15e19dc: StoreField: r1->field_f = r0
    //     0x15e19dc: stur            w0, [x1, #0xf]
    // 0x15e19e0: r0 = 1.000000
    //     0x15e19e0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e19e4: StoreField: r1->field_13 = r0
    //     0x15e19e4: stur            w0, [x1, #0x13]
    // 0x15e19e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e19e8: stur            w0, [x1, #0x17]
    // 0x15e19ec: ldur            x0, [fp, #-0x10]
    // 0x15e19f0: StoreField: r1->field_b = r0
    //     0x15e19f0: stur            w0, [x1, #0xb]
    // 0x15e19f4: r0 = InkWell()
    //     0x15e19f4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e19f8: mov             x3, x0
    // 0x15e19fc: ldur            x0, [fp, #-0x20]
    // 0x15e1a00: stur            x3, [fp, #-0x10]
    // 0x15e1a04: StoreField: r3->field_b = r0
    //     0x15e1a04: stur            w0, [x3, #0xb]
    // 0x15e1a08: ldur            x2, [fp, #-0x18]
    // 0x15e1a0c: r1 = Function '<anonymous closure>':.
    //     0x15e1a0c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a10] AnonymousClosure: (0x15e1a84), in [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::appBar (0x15e18f8)
    //     0x15e1a10: ldr             x1, [x1, #0xa10]
    // 0x15e1a14: r0 = AllocateClosure()
    //     0x15e1a14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1a18: ldur            x2, [fp, #-0x10]
    // 0x15e1a1c: StoreField: r2->field_f = r0
    //     0x15e1a1c: stur            w0, [x2, #0xf]
    // 0x15e1a20: r0 = true
    //     0x15e1a20: add             x0, NULL, #0x20  ; true
    // 0x15e1a24: StoreField: r2->field_43 = r0
    //     0x15e1a24: stur            w0, [x2, #0x43]
    // 0x15e1a28: r1 = Instance_BoxShape
    //     0x15e1a28: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e1a2c: ldr             x1, [x1, #0x80]
    // 0x15e1a30: StoreField: r2->field_47 = r1
    //     0x15e1a30: stur            w1, [x2, #0x47]
    // 0x15e1a34: StoreField: r2->field_6f = r0
    //     0x15e1a34: stur            w0, [x2, #0x6f]
    // 0x15e1a38: r1 = false
    //     0x15e1a38: add             x1, NULL, #0x30  ; false
    // 0x15e1a3c: StoreField: r2->field_73 = r1
    //     0x15e1a3c: stur            w1, [x2, #0x73]
    // 0x15e1a40: StoreField: r2->field_83 = r0
    //     0x15e1a40: stur            w0, [x2, #0x83]
    // 0x15e1a44: StoreField: r2->field_7b = r1
    //     0x15e1a44: stur            w1, [x2, #0x7b]
    // 0x15e1a48: r0 = AppBar()
    //     0x15e1a48: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e1a4c: stur            x0, [fp, #-0x18]
    // 0x15e1a50: ldur            x16, [fp, #-8]
    // 0x15e1a54: str             x16, [SP]
    // 0x15e1a58: mov             x1, x0
    // 0x15e1a5c: ldur            x2, [fp, #-0x10]
    // 0x15e1a60: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e1a60: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e1a64: ldr             x4, [x4, #0xf00]
    // 0x15e1a68: r0 = AppBar()
    //     0x15e1a68: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e1a6c: ldur            x0, [fp, #-0x18]
    // 0x15e1a70: LeaveFrame
    //     0x15e1a70: mov             SP, fp
    //     0x15e1a74: ldp             fp, lr, [SP], #0x10
    // 0x15e1a78: ret
    //     0x15e1a78: ret             
    // 0x15e1a7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1a7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1a80: b               #0x15e1920
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e1a84, size: 0x48
    // 0x15e1a84: EnterFrame
    //     0x15e1a84: stp             fp, lr, [SP, #-0x10]!
    //     0x15e1a88: mov             fp, SP
    // 0x15e1a8c: ldr             x0, [fp, #0x10]
    // 0x15e1a90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15e1a90: ldur            w1, [x0, #0x17]
    // 0x15e1a94: DecompressPointer r1
    //     0x15e1a94: add             x1, x1, HEAP, lsl #32
    // 0x15e1a98: CheckStackOverflow
    //     0x15e1a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1a9c: cmp             SP, x16
    //     0x15e1aa0: b.ls            #0x15e1ac4
    // 0x15e1aa4: LoadField: r0 = r1->field_f
    //     0x15e1aa4: ldur            w0, [x1, #0xf]
    // 0x15e1aa8: DecompressPointer r0
    //     0x15e1aa8: add             x0, x0, HEAP, lsl #32
    // 0x15e1aac: mov             x1, x0
    // 0x15e1ab0: r0 = onBackPress()
    //     0x15e1ab0: bl              #0x15e1acc  ; [package:customer_app/app/presentation/views/glass/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress
    // 0x15e1ab4: r0 = Null
    //     0x15e1ab4: mov             x0, NULL
    // 0x15e1ab8: LeaveFrame
    //     0x15e1ab8: mov             SP, fp
    //     0x15e1abc: ldp             fp, lr, [SP], #0x10
    // 0x15e1ac0: ret
    //     0x15e1ac0: ret             
    // 0x15e1ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1ac4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1ac8: b               #0x15e1aa4
  }
  _ onBackPress(/* No info */) {
    // ** addr: 0x15e1acc, size: 0xbc
    // 0x15e1acc: EnterFrame
    //     0x15e1acc: stp             fp, lr, [SP, #-0x10]!
    //     0x15e1ad0: mov             fp, SP
    // 0x15e1ad4: AllocStack(0x8)
    //     0x15e1ad4: sub             SP, SP, #8
    // 0x15e1ad8: CheckStackOverflow
    //     0x15e1ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1adc: cmp             SP, x16
    //     0x15e1ae0: b.ls            #0x15e1b80
    // 0x15e1ae4: r0 = controller()
    //     0x15e1ae4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e1ae8: LoadField: r1 = r0->field_77
    //     0x15e1ae8: ldur            w1, [x0, #0x77]
    // 0x15e1aec: DecompressPointer r1
    //     0x15e1aec: add             x1, x1, HEAP, lsl #32
    // 0x15e1af0: r2 = ""
    //     0x15e1af0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e1af4: r0 = value=()
    //     0x15e1af4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15e1af8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e1af8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e1afc: ldr             x0, [x0, #0x1c80]
    //     0x15e1b00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e1b04: cmp             w0, w16
    //     0x15e1b08: b.ne            #0x15e1b14
    //     0x15e1b0c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e1b10: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e1b14: r1 = Function '<anonymous closure>':.
    //     0x15e1b14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a18] AnonymousClosure: (0x15cf754), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress (0x15cf7c4)
    //     0x15e1b18: ldr             x1, [x1, #0xa18]
    // 0x15e1b1c: r2 = Null
    //     0x15e1b1c: mov             x2, NULL
    // 0x15e1b20: r0 = AllocateClosure()
    //     0x15e1b20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1b24: mov             x1, x0
    // 0x15e1b28: r0 = GetNavigation.until()
    //     0x15e1b28: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x15e1b2c: r1 = <bool>
    //     0x15e1b2c: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15e1b30: r0 = _Future()
    //     0x15e1b30: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x15e1b34: stur            x0, [fp, #-8]
    // 0x15e1b38: StoreField: r0->field_b = rZR
    //     0x15e1b38: stur            xzr, [x0, #0xb]
    // 0x15e1b3c: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x15e1b3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e1b40: ldr             x0, [x0, #0x778]
    //     0x15e1b44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e1b48: cmp             w0, w16
    //     0x15e1b4c: b.ne            #0x15e1b58
    //     0x15e1b50: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x15e1b54: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x15e1b58: mov             x1, x0
    // 0x15e1b5c: ldur            x0, [fp, #-8]
    // 0x15e1b60: StoreField: r0->field_13 = r1
    //     0x15e1b60: stur            w1, [x0, #0x13]
    // 0x15e1b64: mov             x1, x0
    // 0x15e1b68: r2 = true
    //     0x15e1b68: add             x2, NULL, #0x20  ; true
    // 0x15e1b6c: r0 = _asyncComplete()
    //     0x15e1b6c: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x15e1b70: ldur            x0, [fp, #-8]
    // 0x15e1b74: LeaveFrame
    //     0x15e1b74: mov             SP, fp
    //     0x15e1b78: ldp             fp, lr, [SP], #0x10
    // 0x15e1b7c: ret
    //     0x15e1b7c: ret             
    // 0x15e1b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1b80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1b84: b               #0x15e1ae4
  }
}
