// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart

// class id: 1049322, size: 0x8
class :: {
}

// class id: 3395, size: 0x1c, field offset: 0x14
class _ReviewWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb1658c, size: 0x110
    // 0xb1658c: EnterFrame
    //     0xb1658c: stp             fp, lr, [SP, #-0x10]!
    //     0xb16590: mov             fp, SP
    // 0xb16594: AllocStack(0x30)
    //     0xb16594: sub             SP, SP, #0x30
    // 0xb16598: SetupParameters(_ReviewWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xb16598: stur            x1, [fp, #-8]
    // 0xb1659c: CheckStackOverflow
    //     0xb1659c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb165a0: cmp             SP, x16
    //     0xb165a4: b.ls            #0xb16690
    // 0xb165a8: r1 = 1
    //     0xb165a8: movz            x1, #0x1
    // 0xb165ac: r0 = AllocateContext()
    //     0xb165ac: bl              #0x16f6108  ; AllocateContextStub
    // 0xb165b0: mov             x3, x0
    // 0xb165b4: ldur            x0, [fp, #-8]
    // 0xb165b8: stur            x3, [fp, #-0x18]
    // 0xb165bc: StoreField: r3->field_f = r0
    //     0xb165bc: stur            w0, [x3, #0xf]
    // 0xb165c0: LoadField: r1 = r0->field_b
    //     0xb165c0: ldur            w1, [x0, #0xb]
    // 0xb165c4: DecompressPointer r1
    //     0xb165c4: add             x1, x1, HEAP, lsl #32
    // 0xb165c8: cmp             w1, NULL
    // 0xb165cc: b.eq            #0xb16698
    // 0xb165d0: LoadField: r0 = r1->field_b
    //     0xb165d0: ldur            w0, [x1, #0xb]
    // 0xb165d4: DecompressPointer r0
    //     0xb165d4: add             x0, x0, HEAP, lsl #32
    // 0xb165d8: cmp             w0, NULL
    // 0xb165dc: b.ne            #0xb165e8
    // 0xb165e0: r0 = Null
    //     0xb165e0: mov             x0, NULL
    // 0xb165e4: b               #0xb165f4
    // 0xb165e8: LoadField: r1 = r0->field_13
    //     0xb165e8: ldur            w1, [x0, #0x13]
    // 0xb165ec: DecompressPointer r1
    //     0xb165ec: add             x1, x1, HEAP, lsl #32
    // 0xb165f0: LoadField: r0 = r1->field_b
    //     0xb165f0: ldur            w0, [x1, #0xb]
    // 0xb165f4: cmp             w0, NULL
    // 0xb165f8: b.ne            #0xb16604
    // 0xb165fc: r0 = 0
    //     0xb165fc: movz            x0, #0
    // 0xb16600: b               #0xb1660c
    // 0xb16604: r1 = LoadInt32Instr(r0)
    //     0xb16604: sbfx            x1, x0, #1, #0x1f
    // 0xb16608: mov             x0, x1
    // 0xb1660c: stur            x0, [fp, #-0x10]
    // 0xb16610: r1 = Function '<anonymous closure>':.
    //     0xb16610: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aba8] AnonymousClosure: (0xa928e0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x14fc2bc)
    //     0xb16614: ldr             x1, [x1, #0xba8]
    // 0xb16618: r2 = Null
    //     0xb16618: mov             x2, NULL
    // 0xb1661c: r0 = AllocateClosure()
    //     0xb1661c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb16620: ldur            x2, [fp, #-0x18]
    // 0xb16624: r1 = Function '<anonymous closure>':.
    //     0xb16624: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abb0] AnonymousClosure: (0xb1669c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb1658c)
    //     0xb16628: ldr             x1, [x1, #0xbb0]
    // 0xb1662c: stur            x0, [fp, #-8]
    // 0xb16630: r0 = AllocateClosure()
    //     0xb16630: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb16634: stur            x0, [fp, #-0x18]
    // 0xb16638: r0 = ListView()
    //     0xb16638: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb1663c: stur            x0, [fp, #-0x20]
    // 0xb16640: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb16640: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb16644: ldr             x16, [x16, #0x1c8]
    // 0xb16648: r30 = true
    //     0xb16648: add             lr, NULL, #0x20  ; true
    // 0xb1664c: stp             lr, x16, [SP]
    // 0xb16650: mov             x1, x0
    // 0xb16654: ldur            x2, [fp, #-0x18]
    // 0xb16658: ldur            x3, [fp, #-0x10]
    // 0xb1665c: ldur            x5, [fp, #-8]
    // 0xb16660: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xb16660: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xb16664: ldr             x4, [x4, #0x968]
    // 0xb16668: r0 = ListView.separated()
    //     0xb16668: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb1666c: r0 = Padding()
    //     0xb1666c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb16670: r1 = Instance_EdgeInsets
    //     0xb16670: add             x1, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb16674: ldr             x1, [x1, #0x778]
    // 0xb16678: StoreField: r0->field_f = r1
    //     0xb16678: stur            w1, [x0, #0xf]
    // 0xb1667c: ldur            x1, [fp, #-0x20]
    // 0xb16680: StoreField: r0->field_b = r1
    //     0xb16680: stur            w1, [x0, #0xb]
    // 0xb16684: LeaveFrame
    //     0xb16684: mov             SP, fp
    //     0xb16688: ldp             fp, lr, [SP], #0x10
    // 0xb1668c: ret
    //     0xb1668c: ret             
    // 0xb16690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb16690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb16694: b               #0xb165a8
    // 0xb16698: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb16698: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb1669c, size: 0xec4
    // 0xb1669c: EnterFrame
    //     0xb1669c: stp             fp, lr, [SP, #-0x10]!
    //     0xb166a0: mov             fp, SP
    // 0xb166a4: AllocStack(0x78)
    //     0xb166a4: sub             SP, SP, #0x78
    // 0xb166a8: SetupParameters()
    //     0xb166a8: ldr             x0, [fp, #0x20]
    //     0xb166ac: ldur            w1, [x0, #0x17]
    //     0xb166b0: add             x1, x1, HEAP, lsl #32
    //     0xb166b4: stur            x1, [fp, #-8]
    // 0xb166b8: CheckStackOverflow
    //     0xb166b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb166bc: cmp             SP, x16
    //     0xb166c0: b.ls            #0xb1754c
    // 0xb166c4: r1 = 2
    //     0xb166c4: movz            x1, #0x2
    // 0xb166c8: r0 = AllocateContext()
    //     0xb166c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb166cc: mov             x2, x0
    // 0xb166d0: ldur            x0, [fp, #-8]
    // 0xb166d4: stur            x2, [fp, #-0x10]
    // 0xb166d8: StoreField: r2->field_b = r0
    //     0xb166d8: stur            w0, [x2, #0xb]
    // 0xb166dc: ldr             x1, [fp, #0x18]
    // 0xb166e0: StoreField: r2->field_f = r1
    //     0xb166e0: stur            w1, [x2, #0xf]
    // 0xb166e4: LoadField: r1 = r0->field_f
    //     0xb166e4: ldur            w1, [x0, #0xf]
    // 0xb166e8: DecompressPointer r1
    //     0xb166e8: add             x1, x1, HEAP, lsl #32
    // 0xb166ec: LoadField: r0 = r1->field_b
    //     0xb166ec: ldur            w0, [x1, #0xb]
    // 0xb166f0: DecompressPointer r0
    //     0xb166f0: add             x0, x0, HEAP, lsl #32
    // 0xb166f4: cmp             w0, NULL
    // 0xb166f8: b.eq            #0xb17554
    // 0xb166fc: LoadField: r1 = r0->field_b
    //     0xb166fc: ldur            w1, [x0, #0xb]
    // 0xb16700: DecompressPointer r1
    //     0xb16700: add             x1, x1, HEAP, lsl #32
    // 0xb16704: cmp             w1, NULL
    // 0xb16708: b.ne            #0xb16714
    // 0xb1670c: r0 = Null
    //     0xb1670c: mov             x0, NULL
    // 0xb16710: b               #0xb16758
    // 0xb16714: ldr             x0, [fp, #0x10]
    // 0xb16718: LoadField: r3 = r1->field_13
    //     0xb16718: ldur            w3, [x1, #0x13]
    // 0xb1671c: DecompressPointer r3
    //     0xb1671c: add             x3, x3, HEAP, lsl #32
    // 0xb16720: LoadField: r1 = r3->field_b
    //     0xb16720: ldur            w1, [x3, #0xb]
    // 0xb16724: r4 = LoadInt32Instr(r0)
    //     0xb16724: sbfx            x4, x0, #1, #0x1f
    //     0xb16728: tbz             w0, #0, #0xb16730
    //     0xb1672c: ldur            x4, [x0, #7]
    // 0xb16730: r0 = LoadInt32Instr(r1)
    //     0xb16730: sbfx            x0, x1, #1, #0x1f
    // 0xb16734: mov             x1, x4
    // 0xb16738: cmp             x1, x0
    // 0xb1673c: b.hs            #0xb17558
    // 0xb16740: LoadField: r0 = r3->field_f
    //     0xb16740: ldur            w0, [x3, #0xf]
    // 0xb16744: DecompressPointer r0
    //     0xb16744: add             x0, x0, HEAP, lsl #32
    // 0xb16748: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb16748: add             x16, x0, x4, lsl #2
    //     0xb1674c: ldur            w1, [x16, #0xf]
    // 0xb16750: DecompressPointer r1
    //     0xb16750: add             x1, x1, HEAP, lsl #32
    // 0xb16754: mov             x0, x1
    // 0xb16758: stur            x0, [fp, #-8]
    // 0xb1675c: StoreField: r2->field_13 = r0
    //     0xb1675c: stur            w0, [x2, #0x13]
    // 0xb16760: r0 = Radius()
    //     0xb16760: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb16764: d0 = 15.000000
    //     0xb16764: fmov            d0, #15.00000000
    // 0xb16768: stur            x0, [fp, #-0x18]
    // 0xb1676c: StoreField: r0->field_7 = d0
    //     0xb1676c: stur            d0, [x0, #7]
    // 0xb16770: StoreField: r0->field_f = d0
    //     0xb16770: stur            d0, [x0, #0xf]
    // 0xb16774: r0 = BorderRadius()
    //     0xb16774: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb16778: mov             x1, x0
    // 0xb1677c: ldur            x0, [fp, #-0x18]
    // 0xb16780: stur            x1, [fp, #-0x20]
    // 0xb16784: StoreField: r1->field_7 = r0
    //     0xb16784: stur            w0, [x1, #7]
    // 0xb16788: StoreField: r1->field_b = r0
    //     0xb16788: stur            w0, [x1, #0xb]
    // 0xb1678c: StoreField: r1->field_f = r0
    //     0xb1678c: stur            w0, [x1, #0xf]
    // 0xb16790: StoreField: r1->field_13 = r0
    //     0xb16790: stur            w0, [x1, #0x13]
    // 0xb16794: r0 = BoxDecoration()
    //     0xb16794: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb16798: mov             x2, x0
    // 0xb1679c: r0 = Instance_Color
    //     0xb1679c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb167a0: stur            x2, [fp, #-0x18]
    // 0xb167a4: StoreField: r2->field_7 = r0
    //     0xb167a4: stur            w0, [x2, #7]
    // 0xb167a8: ldur            x0, [fp, #-0x20]
    // 0xb167ac: StoreField: r2->field_13 = r0
    //     0xb167ac: stur            w0, [x2, #0x13]
    // 0xb167b0: r0 = Instance_BoxShape
    //     0xb167b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb167b4: ldr             x0, [x0, #0x80]
    // 0xb167b8: StoreField: r2->field_23 = r0
    //     0xb167b8: stur            w0, [x2, #0x23]
    // 0xb167bc: r1 = Instance_Color
    //     0xb167bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb167c0: d0 = 0.050000
    //     0xb167c0: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb167c4: r0 = withOpacity()
    //     0xb167c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb167c8: stur            x0, [fp, #-0x20]
    // 0xb167cc: r0 = BoxDecoration()
    //     0xb167cc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb167d0: mov             x1, x0
    // 0xb167d4: ldur            x0, [fp, #-0x20]
    // 0xb167d8: stur            x1, [fp, #-0x28]
    // 0xb167dc: StoreField: r1->field_7 = r0
    //     0xb167dc: stur            w0, [x1, #7]
    // 0xb167e0: r0 = Instance_BoxShape
    //     0xb167e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb167e4: ldr             x0, [x0, #0x970]
    // 0xb167e8: StoreField: r1->field_23 = r0
    //     0xb167e8: stur            w0, [x1, #0x23]
    // 0xb167ec: ldur            x0, [fp, #-8]
    // 0xb167f0: cmp             w0, NULL
    // 0xb167f4: b.ne            #0xb16800
    // 0xb167f8: r0 = Null
    //     0xb167f8: mov             x0, NULL
    // 0xb167fc: b               #0xb1683c
    // 0xb16800: LoadField: r2 = r0->field_7
    //     0xb16800: ldur            w2, [x0, #7]
    // 0xb16804: DecompressPointer r2
    //     0xb16804: add             x2, x2, HEAP, lsl #32
    // 0xb16808: cmp             w2, NULL
    // 0xb1680c: b.ne            #0xb16818
    // 0xb16810: r0 = Null
    //     0xb16810: mov             x0, NULL
    // 0xb16814: b               #0xb1683c
    // 0xb16818: stp             xzr, x2, [SP]
    // 0xb1681c: r0 = []()
    //     0xb1681c: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb16820: r1 = LoadClassIdInstr(r0)
    //     0xb16820: ldur            x1, [x0, #-1]
    //     0xb16824: ubfx            x1, x1, #0xc, #0x14
    // 0xb16828: str             x0, [SP]
    // 0xb1682c: mov             x0, x1
    // 0xb16830: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb16830: sub             lr, x0, #1, lsl #12
    //     0xb16834: ldr             lr, [x21, lr, lsl #3]
    //     0xb16838: blr             lr
    // 0xb1683c: cmp             w0, NULL
    // 0xb16840: b.ne            #0xb1684c
    // 0xb16844: r3 = ""
    //     0xb16844: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb16848: b               #0xb16850
    // 0xb1684c: mov             x3, x0
    // 0xb16850: ldur            x2, [fp, #-0x10]
    // 0xb16854: ldur            x0, [fp, #-8]
    // 0xb16858: stur            x3, [fp, #-0x20]
    // 0xb1685c: LoadField: r1 = r2->field_f
    //     0xb1685c: ldur            w1, [x2, #0xf]
    // 0xb16860: DecompressPointer r1
    //     0xb16860: add             x1, x1, HEAP, lsl #32
    // 0xb16864: r0 = of()
    //     0xb16864: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb16868: LoadField: r1 = r0->field_87
    //     0xb16868: ldur            w1, [x0, #0x87]
    // 0xb1686c: DecompressPointer r1
    //     0xb1686c: add             x1, x1, HEAP, lsl #32
    // 0xb16870: LoadField: r0 = r1->field_7
    //     0xb16870: ldur            w0, [x1, #7]
    // 0xb16874: DecompressPointer r0
    //     0xb16874: add             x0, x0, HEAP, lsl #32
    // 0xb16878: stur            x0, [fp, #-0x30]
    // 0xb1687c: r1 = Instance_Color
    //     0xb1687c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb16880: d0 = 0.500000
    //     0xb16880: fmov            d0, #0.50000000
    // 0xb16884: r0 = withOpacity()
    //     0xb16884: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb16888: r16 = 16.000000
    //     0xb16888: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb1688c: ldr             x16, [x16, #0x188]
    // 0xb16890: stp             x0, x16, [SP]
    // 0xb16894: ldur            x1, [fp, #-0x30]
    // 0xb16898: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb16898: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb1689c: ldr             x4, [x4, #0xaa0]
    // 0xb168a0: r0 = copyWith()
    //     0xb168a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb168a4: stur            x0, [fp, #-0x30]
    // 0xb168a8: r0 = Text()
    //     0xb168a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb168ac: mov             x1, x0
    // 0xb168b0: ldur            x0, [fp, #-0x20]
    // 0xb168b4: stur            x1, [fp, #-0x38]
    // 0xb168b8: StoreField: r1->field_b = r0
    //     0xb168b8: stur            w0, [x1, #0xb]
    // 0xb168bc: ldur            x0, [fp, #-0x30]
    // 0xb168c0: StoreField: r1->field_13 = r0
    //     0xb168c0: stur            w0, [x1, #0x13]
    // 0xb168c4: r0 = Center()
    //     0xb168c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb168c8: mov             x1, x0
    // 0xb168cc: r0 = Instance_Alignment
    //     0xb168cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb168d0: ldr             x0, [x0, #0xb10]
    // 0xb168d4: stur            x1, [fp, #-0x20]
    // 0xb168d8: StoreField: r1->field_f = r0
    //     0xb168d8: stur            w0, [x1, #0xf]
    // 0xb168dc: ldur            x0, [fp, #-0x38]
    // 0xb168e0: StoreField: r1->field_b = r0
    //     0xb168e0: stur            w0, [x1, #0xb]
    // 0xb168e4: r0 = Container()
    //     0xb168e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb168e8: stur            x0, [fp, #-0x30]
    // 0xb168ec: r16 = 34.000000
    //     0xb168ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb168f0: ldr             x16, [x16, #0x978]
    // 0xb168f4: r30 = 34.000000
    //     0xb168f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb168f8: ldr             lr, [lr, #0x978]
    // 0xb168fc: stp             lr, x16, [SP, #0x18]
    // 0xb16900: r16 = Instance_EdgeInsets
    //     0xb16900: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb16904: ldr             x16, [x16, #0x980]
    // 0xb16908: ldur            lr, [fp, #-0x28]
    // 0xb1690c: stp             lr, x16, [SP, #8]
    // 0xb16910: ldur            x16, [fp, #-0x20]
    // 0xb16914: str             x16, [SP]
    // 0xb16918: mov             x1, x0
    // 0xb1691c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb1691c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb16920: ldr             x4, [x4, #0x988]
    // 0xb16924: r0 = Container()
    //     0xb16924: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb16928: ldur            x0, [fp, #-8]
    // 0xb1692c: cmp             w0, NULL
    // 0xb16930: b.ne            #0xb1693c
    // 0xb16934: r1 = Null
    //     0xb16934: mov             x1, NULL
    // 0xb16938: b               #0xb16944
    // 0xb1693c: LoadField: r1 = r0->field_7
    //     0xb1693c: ldur            w1, [x0, #7]
    // 0xb16940: DecompressPointer r1
    //     0xb16940: add             x1, x1, HEAP, lsl #32
    // 0xb16944: cmp             w1, NULL
    // 0xb16948: b.ne            #0xb16954
    // 0xb1694c: r3 = ""
    //     0xb1694c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb16950: b               #0xb16958
    // 0xb16954: mov             x3, x1
    // 0xb16958: ldur            x2, [fp, #-0x10]
    // 0xb1695c: stur            x3, [fp, #-0x20]
    // 0xb16960: LoadField: r1 = r2->field_f
    //     0xb16960: ldur            w1, [x2, #0xf]
    // 0xb16964: DecompressPointer r1
    //     0xb16964: add             x1, x1, HEAP, lsl #32
    // 0xb16968: r0 = of()
    //     0xb16968: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1696c: LoadField: r1 = r0->field_87
    //     0xb1696c: ldur            w1, [x0, #0x87]
    // 0xb16970: DecompressPointer r1
    //     0xb16970: add             x1, x1, HEAP, lsl #32
    // 0xb16974: LoadField: r0 = r1->field_7
    //     0xb16974: ldur            w0, [x1, #7]
    // 0xb16978: DecompressPointer r0
    //     0xb16978: add             x0, x0, HEAP, lsl #32
    // 0xb1697c: r16 = 14.000000
    //     0xb1697c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb16980: ldr             x16, [x16, #0x1d8]
    // 0xb16984: r30 = Instance_Color
    //     0xb16984: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb16988: stp             lr, x16, [SP]
    // 0xb1698c: mov             x1, x0
    // 0xb16990: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb16990: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb16994: ldr             x4, [x4, #0xaa0]
    // 0xb16998: r0 = copyWith()
    //     0xb16998: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1699c: stur            x0, [fp, #-0x28]
    // 0xb169a0: r0 = Text()
    //     0xb169a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb169a4: mov             x2, x0
    // 0xb169a8: ldur            x0, [fp, #-0x20]
    // 0xb169ac: stur            x2, [fp, #-0x38]
    // 0xb169b0: StoreField: r2->field_b = r0
    //     0xb169b0: stur            w0, [x2, #0xb]
    // 0xb169b4: ldur            x0, [fp, #-0x28]
    // 0xb169b8: StoreField: r2->field_13 = r0
    //     0xb169b8: stur            w0, [x2, #0x13]
    // 0xb169bc: ldur            x0, [fp, #-8]
    // 0xb169c0: cmp             w0, NULL
    // 0xb169c4: b.ne            #0xb169d0
    // 0xb169c8: r1 = Null
    //     0xb169c8: mov             x1, NULL
    // 0xb169cc: b               #0xb169d8
    // 0xb169d0: LoadField: r1 = r0->field_1f
    //     0xb169d0: ldur            w1, [x0, #0x1f]
    // 0xb169d4: DecompressPointer r1
    //     0xb169d4: add             x1, x1, HEAP, lsl #32
    // 0xb169d8: cmp             w1, NULL
    // 0xb169dc: b.ne            #0xb169e8
    // 0xb169e0: r4 = ""
    //     0xb169e0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb169e4: b               #0xb169ec
    // 0xb169e8: mov             x4, x1
    // 0xb169ec: ldur            x3, [fp, #-0x10]
    // 0xb169f0: stur            x4, [fp, #-0x20]
    // 0xb169f4: LoadField: r1 = r3->field_f
    //     0xb169f4: ldur            w1, [x3, #0xf]
    // 0xb169f8: DecompressPointer r1
    //     0xb169f8: add             x1, x1, HEAP, lsl #32
    // 0xb169fc: r0 = of()
    //     0xb169fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb16a00: LoadField: r1 = r0->field_87
    //     0xb16a00: ldur            w1, [x0, #0x87]
    // 0xb16a04: DecompressPointer r1
    //     0xb16a04: add             x1, x1, HEAP, lsl #32
    // 0xb16a08: LoadField: r0 = r1->field_33
    //     0xb16a08: ldur            w0, [x1, #0x33]
    // 0xb16a0c: DecompressPointer r0
    //     0xb16a0c: add             x0, x0, HEAP, lsl #32
    // 0xb16a10: stur            x0, [fp, #-0x28]
    // 0xb16a14: cmp             w0, NULL
    // 0xb16a18: b.ne            #0xb16a24
    // 0xb16a1c: r4 = Null
    //     0xb16a1c: mov             x4, NULL
    // 0xb16a20: b               #0xb16a4c
    // 0xb16a24: r1 = Instance_Color
    //     0xb16a24: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb16a28: d0 = 0.500000
    //     0xb16a28: fmov            d0, #0.50000000
    // 0xb16a2c: r0 = withOpacity()
    //     0xb16a2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb16a30: r16 = 10.000000
    //     0xb16a30: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb16a34: stp             x0, x16, [SP]
    // 0xb16a38: ldur            x1, [fp, #-0x28]
    // 0xb16a3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb16a3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb16a40: ldr             x4, [x4, #0xaa0]
    // 0xb16a44: r0 = copyWith()
    //     0xb16a44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb16a48: mov             x4, x0
    // 0xb16a4c: ldur            x1, [fp, #-8]
    // 0xb16a50: ldur            x3, [fp, #-0x30]
    // 0xb16a54: ldur            x0, [fp, #-0x38]
    // 0xb16a58: ldur            x2, [fp, #-0x20]
    // 0xb16a5c: stur            x4, [fp, #-0x28]
    // 0xb16a60: r0 = Text()
    //     0xb16a60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb16a64: mov             x1, x0
    // 0xb16a68: ldur            x0, [fp, #-0x20]
    // 0xb16a6c: stur            x1, [fp, #-0x40]
    // 0xb16a70: StoreField: r1->field_b = r0
    //     0xb16a70: stur            w0, [x1, #0xb]
    // 0xb16a74: ldur            x0, [fp, #-0x28]
    // 0xb16a78: StoreField: r1->field_13 = r0
    //     0xb16a78: stur            w0, [x1, #0x13]
    // 0xb16a7c: r0 = Padding()
    //     0xb16a7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb16a80: mov             x3, x0
    // 0xb16a84: r0 = Instance_EdgeInsets
    //     0xb16a84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xb16a88: ldr             x0, [x0, #0x990]
    // 0xb16a8c: stur            x3, [fp, #-0x20]
    // 0xb16a90: StoreField: r3->field_f = r0
    //     0xb16a90: stur            w0, [x3, #0xf]
    // 0xb16a94: ldur            x0, [fp, #-0x40]
    // 0xb16a98: StoreField: r3->field_b = r0
    //     0xb16a98: stur            w0, [x3, #0xb]
    // 0xb16a9c: r1 = Null
    //     0xb16a9c: mov             x1, NULL
    // 0xb16aa0: r2 = 4
    //     0xb16aa0: movz            x2, #0x4
    // 0xb16aa4: r0 = AllocateArray()
    //     0xb16aa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16aa8: mov             x2, x0
    // 0xb16aac: ldur            x0, [fp, #-0x38]
    // 0xb16ab0: stur            x2, [fp, #-0x28]
    // 0xb16ab4: StoreField: r2->field_f = r0
    //     0xb16ab4: stur            w0, [x2, #0xf]
    // 0xb16ab8: ldur            x0, [fp, #-0x20]
    // 0xb16abc: StoreField: r2->field_13 = r0
    //     0xb16abc: stur            w0, [x2, #0x13]
    // 0xb16ac0: r1 = <Widget>
    //     0xb16ac0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16ac4: r0 = AllocateGrowableArray()
    //     0xb16ac4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16ac8: mov             x1, x0
    // 0xb16acc: ldur            x0, [fp, #-0x28]
    // 0xb16ad0: stur            x1, [fp, #-0x20]
    // 0xb16ad4: StoreField: r1->field_f = r0
    //     0xb16ad4: stur            w0, [x1, #0xf]
    // 0xb16ad8: r2 = 4
    //     0xb16ad8: movz            x2, #0x4
    // 0xb16adc: StoreField: r1->field_b = r2
    //     0xb16adc: stur            w2, [x1, #0xb]
    // 0xb16ae0: r0 = Column()
    //     0xb16ae0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb16ae4: mov             x3, x0
    // 0xb16ae8: r0 = Instance_Axis
    //     0xb16ae8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb16aec: stur            x3, [fp, #-0x28]
    // 0xb16af0: StoreField: r3->field_f = r0
    //     0xb16af0: stur            w0, [x3, #0xf]
    // 0xb16af4: r4 = Instance_MainAxisAlignment
    //     0xb16af4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb16af8: ldr             x4, [x4, #0xa08]
    // 0xb16afc: StoreField: r3->field_13 = r4
    //     0xb16afc: stur            w4, [x3, #0x13]
    // 0xb16b00: r5 = Instance_MainAxisSize
    //     0xb16b00: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb16b04: ldr             x5, [x5, #0xa10]
    // 0xb16b08: ArrayStore: r3[0] = r5  ; List_4
    //     0xb16b08: stur            w5, [x3, #0x17]
    // 0xb16b0c: r6 = Instance_CrossAxisAlignment
    //     0xb16b0c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb16b10: ldr             x6, [x6, #0x890]
    // 0xb16b14: StoreField: r3->field_1b = r6
    //     0xb16b14: stur            w6, [x3, #0x1b]
    // 0xb16b18: r7 = Instance_VerticalDirection
    //     0xb16b18: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb16b1c: ldr             x7, [x7, #0xa20]
    // 0xb16b20: StoreField: r3->field_23 = r7
    //     0xb16b20: stur            w7, [x3, #0x23]
    // 0xb16b24: r8 = Instance_Clip
    //     0xb16b24: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb16b28: ldr             x8, [x8, #0x38]
    // 0xb16b2c: StoreField: r3->field_2b = r8
    //     0xb16b2c: stur            w8, [x3, #0x2b]
    // 0xb16b30: StoreField: r3->field_2f = rZR
    //     0xb16b30: stur            xzr, [x3, #0x2f]
    // 0xb16b34: ldur            x1, [fp, #-0x20]
    // 0xb16b38: StoreField: r3->field_b = r1
    //     0xb16b38: stur            w1, [x3, #0xb]
    // 0xb16b3c: r1 = Null
    //     0xb16b3c: mov             x1, NULL
    // 0xb16b40: r2 = 6
    //     0xb16b40: movz            x2, #0x6
    // 0xb16b44: r0 = AllocateArray()
    //     0xb16b44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16b48: mov             x2, x0
    // 0xb16b4c: ldur            x0, [fp, #-0x30]
    // 0xb16b50: stur            x2, [fp, #-0x20]
    // 0xb16b54: StoreField: r2->field_f = r0
    //     0xb16b54: stur            w0, [x2, #0xf]
    // 0xb16b58: r16 = Instance_SizedBox
    //     0xb16b58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb16b5c: ldr             x16, [x16, #0x998]
    // 0xb16b60: StoreField: r2->field_13 = r16
    //     0xb16b60: stur            w16, [x2, #0x13]
    // 0xb16b64: ldur            x0, [fp, #-0x28]
    // 0xb16b68: ArrayStore: r2[0] = r0  ; List_4
    //     0xb16b68: stur            w0, [x2, #0x17]
    // 0xb16b6c: r1 = <Widget>
    //     0xb16b6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16b70: r0 = AllocateGrowableArray()
    //     0xb16b70: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16b74: mov             x1, x0
    // 0xb16b78: ldur            x0, [fp, #-0x20]
    // 0xb16b7c: stur            x1, [fp, #-0x28]
    // 0xb16b80: StoreField: r1->field_f = r0
    //     0xb16b80: stur            w0, [x1, #0xf]
    // 0xb16b84: r2 = 6
    //     0xb16b84: movz            x2, #0x6
    // 0xb16b88: StoreField: r1->field_b = r2
    //     0xb16b88: stur            w2, [x1, #0xb]
    // 0xb16b8c: r0 = Row()
    //     0xb16b8c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb16b90: mov             x2, x0
    // 0xb16b94: r0 = Instance_Axis
    //     0xb16b94: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb16b98: stur            x2, [fp, #-0x20]
    // 0xb16b9c: StoreField: r2->field_f = r0
    //     0xb16b9c: stur            w0, [x2, #0xf]
    // 0xb16ba0: r3 = Instance_MainAxisAlignment
    //     0xb16ba0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb16ba4: ldr             x3, [x3, #0xa08]
    // 0xb16ba8: StoreField: r2->field_13 = r3
    //     0xb16ba8: stur            w3, [x2, #0x13]
    // 0xb16bac: r4 = Instance_MainAxisSize
    //     0xb16bac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb16bb0: ldr             x4, [x4, #0xa10]
    // 0xb16bb4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb16bb4: stur            w4, [x2, #0x17]
    // 0xb16bb8: r5 = Instance_CrossAxisAlignment
    //     0xb16bb8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb16bbc: ldr             x5, [x5, #0xa18]
    // 0xb16bc0: StoreField: r2->field_1b = r5
    //     0xb16bc0: stur            w5, [x2, #0x1b]
    // 0xb16bc4: r6 = Instance_VerticalDirection
    //     0xb16bc4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb16bc8: ldr             x6, [x6, #0xa20]
    // 0xb16bcc: StoreField: r2->field_23 = r6
    //     0xb16bcc: stur            w6, [x2, #0x23]
    // 0xb16bd0: r7 = Instance_Clip
    //     0xb16bd0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb16bd4: ldr             x7, [x7, #0x38]
    // 0xb16bd8: StoreField: r2->field_2b = r7
    //     0xb16bd8: stur            w7, [x2, #0x2b]
    // 0xb16bdc: StoreField: r2->field_2f = rZR
    //     0xb16bdc: stur            xzr, [x2, #0x2f]
    // 0xb16be0: ldur            x1, [fp, #-0x28]
    // 0xb16be4: StoreField: r2->field_b = r1
    //     0xb16be4: stur            w1, [x2, #0xb]
    // 0xb16be8: ldur            x8, [fp, #-8]
    // 0xb16bec: cmp             w8, NULL
    // 0xb16bf0: b.eq            #0xb16c04
    // 0xb16bf4: LoadField: r1 = r8->field_f
    //     0xb16bf4: ldur            w1, [x8, #0xf]
    // 0xb16bf8: DecompressPointer r1
    //     0xb16bf8: add             x1, x1, HEAP, lsl #32
    // 0xb16bfc: cmp             w1, #0xa
    // 0xb16c00: b.eq            #0xb16c1c
    // 0xb16c04: cmp             w8, NULL
    // 0xb16c08: b.eq            #0xb16c2c
    // 0xb16c0c: LoadField: r1 = r8->field_f
    //     0xb16c0c: ldur            w1, [x8, #0xf]
    // 0xb16c10: DecompressPointer r1
    //     0xb16c10: add             x1, x1, HEAP, lsl #32
    // 0xb16c14: cmp             w1, #8
    // 0xb16c18: b.ne            #0xb16c2c
    // 0xb16c1c: mov             x0, x8
    // 0xb16c20: r1 = Instance_Color
    //     0xb16c20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb16c24: ldr             x1, [x1, #0x858]
    // 0xb16c28: b               #0xb16c9c
    // 0xb16c2c: cmp             w8, NULL
    // 0xb16c30: b.ne            #0xb16c3c
    // 0xb16c34: mov             x0, x8
    // 0xb16c38: b               #0xb16c70
    // 0xb16c3c: LoadField: r1 = r8->field_f
    //     0xb16c3c: ldur            w1, [x8, #0xf]
    // 0xb16c40: DecompressPointer r1
    //     0xb16c40: add             x1, x1, HEAP, lsl #32
    // 0xb16c44: cmp             w1, #6
    // 0xb16c48: b.ne            #0xb16c6c
    // 0xb16c4c: r1 = Instance_Color
    //     0xb16c4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb16c50: ldr             x1, [x1, #0x858]
    // 0xb16c54: d0 = 0.700000
    //     0xb16c54: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb16c58: ldr             d0, [x17, #0xf48]
    // 0xb16c5c: r0 = withOpacity()
    //     0xb16c5c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb16c60: mov             x1, x0
    // 0xb16c64: ldur            x0, [fp, #-8]
    // 0xb16c68: b               #0xb16c9c
    // 0xb16c6c: ldur            x0, [fp, #-8]
    // 0xb16c70: cmp             w0, NULL
    // 0xb16c74: b.eq            #0xb16c94
    // 0xb16c78: LoadField: r1 = r0->field_f
    //     0xb16c78: ldur            w1, [x0, #0xf]
    // 0xb16c7c: DecompressPointer r1
    //     0xb16c7c: add             x1, x1, HEAP, lsl #32
    // 0xb16c80: cmp             w1, #4
    // 0xb16c84: b.ne            #0xb16c94
    // 0xb16c88: r1 = Instance_Color
    //     0xb16c88: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb16c8c: ldr             x1, [x1, #0x860]
    // 0xb16c90: b               #0xb16c9c
    // 0xb16c94: r1 = Instance_Color
    //     0xb16c94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb16c98: ldr             x1, [x1, #0x50]
    // 0xb16c9c: stur            x1, [fp, #-0x28]
    // 0xb16ca0: r0 = ColorFilter()
    //     0xb16ca0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb16ca4: mov             x1, x0
    // 0xb16ca8: ldur            x0, [fp, #-0x28]
    // 0xb16cac: stur            x1, [fp, #-0x30]
    // 0xb16cb0: StoreField: r1->field_7 = r0
    //     0xb16cb0: stur            w0, [x1, #7]
    // 0xb16cb4: r0 = Instance_BlendMode
    //     0xb16cb4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb16cb8: ldr             x0, [x0, #0xb30]
    // 0xb16cbc: StoreField: r1->field_b = r0
    //     0xb16cbc: stur            w0, [x1, #0xb]
    // 0xb16cc0: r0 = 1
    //     0xb16cc0: movz            x0, #0x1
    // 0xb16cc4: StoreField: r1->field_13 = r0
    //     0xb16cc4: stur            x0, [x1, #0x13]
    // 0xb16cc8: r0 = SvgPicture()
    //     0xb16cc8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb16ccc: stur            x0, [fp, #-0x28]
    // 0xb16cd0: ldur            x16, [fp, #-0x30]
    // 0xb16cd4: str             x16, [SP]
    // 0xb16cd8: mov             x1, x0
    // 0xb16cdc: r2 = "assets/images/green_star.svg"
    //     0xb16cdc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb16ce0: ldr             x2, [x2, #0x9a0]
    // 0xb16ce4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb16ce4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb16ce8: ldr             x4, [x4, #0xa38]
    // 0xb16cec: r0 = SvgPicture.asset()
    //     0xb16cec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb16cf0: ldur            x1, [fp, #-8]
    // 0xb16cf4: cmp             w1, NULL
    // 0xb16cf8: b.ne            #0xb16d04
    // 0xb16cfc: r0 = Null
    //     0xb16cfc: mov             x0, NULL
    // 0xb16d00: b               #0xb16d38
    // 0xb16d04: LoadField: r0 = r1->field_f
    //     0xb16d04: ldur            w0, [x1, #0xf]
    // 0xb16d08: DecompressPointer r0
    //     0xb16d08: add             x0, x0, HEAP, lsl #32
    // 0xb16d0c: r2 = 60
    //     0xb16d0c: movz            x2, #0x3c
    // 0xb16d10: branchIfSmi(r0, 0xb16d1c)
    //     0xb16d10: tbz             w0, #0, #0xb16d1c
    // 0xb16d14: r2 = LoadClassIdInstr(r0)
    //     0xb16d14: ldur            x2, [x0, #-1]
    //     0xb16d18: ubfx            x2, x2, #0xc, #0x14
    // 0xb16d1c: str             x0, [SP]
    // 0xb16d20: mov             x0, x2
    // 0xb16d24: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb16d24: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb16d28: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb16d28: movz            x17, #0x2700
    //     0xb16d2c: add             lr, x0, x17
    //     0xb16d30: ldr             lr, [x21, lr, lsl #3]
    //     0xb16d34: blr             lr
    // 0xb16d38: cmp             w0, NULL
    // 0xb16d3c: b.ne            #0xb16d48
    // 0xb16d40: r5 = ""
    //     0xb16d40: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb16d44: b               #0xb16d4c
    // 0xb16d48: mov             x5, x0
    // 0xb16d4c: ldur            x4, [fp, #-0x10]
    // 0xb16d50: ldur            x0, [fp, #-8]
    // 0xb16d54: ldur            x3, [fp, #-0x20]
    // 0xb16d58: ldur            x2, [fp, #-0x28]
    // 0xb16d5c: stur            x5, [fp, #-0x30]
    // 0xb16d60: LoadField: r1 = r4->field_f
    //     0xb16d60: ldur            w1, [x4, #0xf]
    // 0xb16d64: DecompressPointer r1
    //     0xb16d64: add             x1, x1, HEAP, lsl #32
    // 0xb16d68: r0 = of()
    //     0xb16d68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb16d6c: LoadField: r1 = r0->field_87
    //     0xb16d6c: ldur            w1, [x0, #0x87]
    // 0xb16d70: DecompressPointer r1
    //     0xb16d70: add             x1, x1, HEAP, lsl #32
    // 0xb16d74: LoadField: r0 = r1->field_7
    //     0xb16d74: ldur            w0, [x1, #7]
    // 0xb16d78: DecompressPointer r0
    //     0xb16d78: add             x0, x0, HEAP, lsl #32
    // 0xb16d7c: r16 = 12.000000
    //     0xb16d7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb16d80: ldr             x16, [x16, #0x9e8]
    // 0xb16d84: r30 = Instance_Color
    //     0xb16d84: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb16d88: stp             lr, x16, [SP]
    // 0xb16d8c: mov             x1, x0
    // 0xb16d90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb16d90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb16d94: ldr             x4, [x4, #0xaa0]
    // 0xb16d98: r0 = copyWith()
    //     0xb16d98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb16d9c: stur            x0, [fp, #-0x38]
    // 0xb16da0: r0 = Text()
    //     0xb16da0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb16da4: mov             x3, x0
    // 0xb16da8: ldur            x0, [fp, #-0x30]
    // 0xb16dac: stur            x3, [fp, #-0x40]
    // 0xb16db0: StoreField: r3->field_b = r0
    //     0xb16db0: stur            w0, [x3, #0xb]
    // 0xb16db4: ldur            x0, [fp, #-0x38]
    // 0xb16db8: StoreField: r3->field_13 = r0
    //     0xb16db8: stur            w0, [x3, #0x13]
    // 0xb16dbc: r1 = Null
    //     0xb16dbc: mov             x1, NULL
    // 0xb16dc0: r2 = 6
    //     0xb16dc0: movz            x2, #0x6
    // 0xb16dc4: r0 = AllocateArray()
    //     0xb16dc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16dc8: mov             x2, x0
    // 0xb16dcc: ldur            x0, [fp, #-0x28]
    // 0xb16dd0: stur            x2, [fp, #-0x30]
    // 0xb16dd4: StoreField: r2->field_f = r0
    //     0xb16dd4: stur            w0, [x2, #0xf]
    // 0xb16dd8: r16 = Instance_SizedBox
    //     0xb16dd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9a8] Obj!SizedBox@d67ea1
    //     0xb16ddc: ldr             x16, [x16, #0x9a8]
    // 0xb16de0: StoreField: r2->field_13 = r16
    //     0xb16de0: stur            w16, [x2, #0x13]
    // 0xb16de4: ldur            x0, [fp, #-0x40]
    // 0xb16de8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb16de8: stur            w0, [x2, #0x17]
    // 0xb16dec: r1 = <Widget>
    //     0xb16dec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16df0: r0 = AllocateGrowableArray()
    //     0xb16df0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16df4: mov             x1, x0
    // 0xb16df8: ldur            x0, [fp, #-0x30]
    // 0xb16dfc: stur            x1, [fp, #-0x28]
    // 0xb16e00: StoreField: r1->field_f = r0
    //     0xb16e00: stur            w0, [x1, #0xf]
    // 0xb16e04: r0 = 6
    //     0xb16e04: movz            x0, #0x6
    // 0xb16e08: StoreField: r1->field_b = r0
    //     0xb16e08: stur            w0, [x1, #0xb]
    // 0xb16e0c: r0 = Row()
    //     0xb16e0c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb16e10: mov             x3, x0
    // 0xb16e14: r0 = Instance_Axis
    //     0xb16e14: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb16e18: stur            x3, [fp, #-0x30]
    // 0xb16e1c: StoreField: r3->field_f = r0
    //     0xb16e1c: stur            w0, [x3, #0xf]
    // 0xb16e20: r4 = Instance_MainAxisAlignment
    //     0xb16e20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb16e24: ldr             x4, [x4, #0xa08]
    // 0xb16e28: StoreField: r3->field_13 = r4
    //     0xb16e28: stur            w4, [x3, #0x13]
    // 0xb16e2c: r5 = Instance_MainAxisSize
    //     0xb16e2c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb16e30: ldr             x5, [x5, #0xa10]
    // 0xb16e34: ArrayStore: r3[0] = r5  ; List_4
    //     0xb16e34: stur            w5, [x3, #0x17]
    // 0xb16e38: r6 = Instance_CrossAxisAlignment
    //     0xb16e38: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb16e3c: ldr             x6, [x6, #0xa18]
    // 0xb16e40: StoreField: r3->field_1b = r6
    //     0xb16e40: stur            w6, [x3, #0x1b]
    // 0xb16e44: r7 = Instance_VerticalDirection
    //     0xb16e44: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb16e48: ldr             x7, [x7, #0xa20]
    // 0xb16e4c: StoreField: r3->field_23 = r7
    //     0xb16e4c: stur            w7, [x3, #0x23]
    // 0xb16e50: r8 = Instance_Clip
    //     0xb16e50: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb16e54: ldr             x8, [x8, #0x38]
    // 0xb16e58: StoreField: r3->field_2b = r8
    //     0xb16e58: stur            w8, [x3, #0x2b]
    // 0xb16e5c: StoreField: r3->field_2f = rZR
    //     0xb16e5c: stur            xzr, [x3, #0x2f]
    // 0xb16e60: ldur            x1, [fp, #-0x28]
    // 0xb16e64: StoreField: r3->field_b = r1
    //     0xb16e64: stur            w1, [x3, #0xb]
    // 0xb16e68: r1 = Null
    //     0xb16e68: mov             x1, NULL
    // 0xb16e6c: r2 = 4
    //     0xb16e6c: movz            x2, #0x4
    // 0xb16e70: r0 = AllocateArray()
    //     0xb16e70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16e74: mov             x2, x0
    // 0xb16e78: ldur            x0, [fp, #-0x20]
    // 0xb16e7c: stur            x2, [fp, #-0x28]
    // 0xb16e80: StoreField: r2->field_f = r0
    //     0xb16e80: stur            w0, [x2, #0xf]
    // 0xb16e84: ldur            x0, [fp, #-0x30]
    // 0xb16e88: StoreField: r2->field_13 = r0
    //     0xb16e88: stur            w0, [x2, #0x13]
    // 0xb16e8c: r1 = <Widget>
    //     0xb16e8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16e90: r0 = AllocateGrowableArray()
    //     0xb16e90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16e94: mov             x1, x0
    // 0xb16e98: ldur            x0, [fp, #-0x28]
    // 0xb16e9c: stur            x1, [fp, #-0x20]
    // 0xb16ea0: StoreField: r1->field_f = r0
    //     0xb16ea0: stur            w0, [x1, #0xf]
    // 0xb16ea4: r2 = 4
    //     0xb16ea4: movz            x2, #0x4
    // 0xb16ea8: StoreField: r1->field_b = r2
    //     0xb16ea8: stur            w2, [x1, #0xb]
    // 0xb16eac: r0 = Row()
    //     0xb16eac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb16eb0: mov             x3, x0
    // 0xb16eb4: r0 = Instance_Axis
    //     0xb16eb4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb16eb8: stur            x3, [fp, #-0x28]
    // 0xb16ebc: StoreField: r3->field_f = r0
    //     0xb16ebc: stur            w0, [x3, #0xf]
    // 0xb16ec0: r0 = Instance_MainAxisAlignment
    //     0xb16ec0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb16ec4: ldr             x0, [x0, #0xa8]
    // 0xb16ec8: StoreField: r3->field_13 = r0
    //     0xb16ec8: stur            w0, [x3, #0x13]
    // 0xb16ecc: r0 = Instance_MainAxisSize
    //     0xb16ecc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb16ed0: ldr             x0, [x0, #0xa10]
    // 0xb16ed4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb16ed4: stur            w0, [x3, #0x17]
    // 0xb16ed8: r1 = Instance_CrossAxisAlignment
    //     0xb16ed8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb16edc: ldr             x1, [x1, #0xa18]
    // 0xb16ee0: StoreField: r3->field_1b = r1
    //     0xb16ee0: stur            w1, [x3, #0x1b]
    // 0xb16ee4: r4 = Instance_VerticalDirection
    //     0xb16ee4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb16ee8: ldr             x4, [x4, #0xa20]
    // 0xb16eec: StoreField: r3->field_23 = r4
    //     0xb16eec: stur            w4, [x3, #0x23]
    // 0xb16ef0: r5 = Instance_Clip
    //     0xb16ef0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb16ef4: ldr             x5, [x5, #0x38]
    // 0xb16ef8: StoreField: r3->field_2b = r5
    //     0xb16ef8: stur            w5, [x3, #0x2b]
    // 0xb16efc: StoreField: r3->field_2f = rZR
    //     0xb16efc: stur            xzr, [x3, #0x2f]
    // 0xb16f00: ldur            x1, [fp, #-0x20]
    // 0xb16f04: StoreField: r3->field_b = r1
    //     0xb16f04: stur            w1, [x3, #0xb]
    // 0xb16f08: r1 = Null
    //     0xb16f08: mov             x1, NULL
    // 0xb16f0c: r2 = 2
    //     0xb16f0c: movz            x2, #0x2
    // 0xb16f10: r0 = AllocateArray()
    //     0xb16f10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16f14: mov             x2, x0
    // 0xb16f18: ldur            x0, [fp, #-0x28]
    // 0xb16f1c: stur            x2, [fp, #-0x20]
    // 0xb16f20: StoreField: r2->field_f = r0
    //     0xb16f20: stur            w0, [x2, #0xf]
    // 0xb16f24: r1 = <Widget>
    //     0xb16f24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16f28: r0 = AllocateGrowableArray()
    //     0xb16f28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16f2c: mov             x2, x0
    // 0xb16f30: ldur            x0, [fp, #-0x20]
    // 0xb16f34: stur            x2, [fp, #-0x28]
    // 0xb16f38: StoreField: r2->field_f = r0
    //     0xb16f38: stur            w0, [x2, #0xf]
    // 0xb16f3c: r0 = 2
    //     0xb16f3c: movz            x0, #0x2
    // 0xb16f40: StoreField: r2->field_b = r0
    //     0xb16f40: stur            w0, [x2, #0xb]
    // 0xb16f44: ldur            x0, [fp, #-8]
    // 0xb16f48: cmp             w0, NULL
    // 0xb16f4c: b.ne            #0xb16f58
    // 0xb16f50: r1 = Null
    //     0xb16f50: mov             x1, NULL
    // 0xb16f54: b               #0xb16f84
    // 0xb16f58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb16f58: ldur            w1, [x0, #0x17]
    // 0xb16f5c: DecompressPointer r1
    //     0xb16f5c: add             x1, x1, HEAP, lsl #32
    // 0xb16f60: cmp             w1, NULL
    // 0xb16f64: b.ne            #0xb16f70
    // 0xb16f68: r1 = Null
    //     0xb16f68: mov             x1, NULL
    // 0xb16f6c: b               #0xb16f84
    // 0xb16f70: LoadField: r3 = r1->field_7
    //     0xb16f70: ldur            w3, [x1, #7]
    // 0xb16f74: cbnz            w3, #0xb16f80
    // 0xb16f78: r1 = false
    //     0xb16f78: add             x1, NULL, #0x30  ; false
    // 0xb16f7c: b               #0xb16f84
    // 0xb16f80: r1 = true
    //     0xb16f80: add             x1, NULL, #0x20  ; true
    // 0xb16f84: cmp             w1, NULL
    // 0xb16f88: b.ne            #0xb16f94
    // 0xb16f8c: mov             x3, x2
    // 0xb16f90: b               #0xb171cc
    // 0xb16f94: tbnz            w1, #4, #0xb171c8
    // 0xb16f98: cmp             w0, NULL
    // 0xb16f9c: b.ne            #0xb16fa8
    // 0xb16fa0: r0 = Null
    //     0xb16fa0: mov             x0, NULL
    // 0xb16fa4: b               #0xb16fc4
    // 0xb16fa8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb16fa8: ldur            w1, [x0, #0x17]
    // 0xb16fac: DecompressPointer r1
    //     0xb16fac: add             x1, x1, HEAP, lsl #32
    // 0xb16fb0: cmp             w1, NULL
    // 0xb16fb4: b.ne            #0xb16fc0
    // 0xb16fb8: r0 = Null
    //     0xb16fb8: mov             x0, NULL
    // 0xb16fbc: b               #0xb16fc4
    // 0xb16fc0: r0 = trim()
    //     0xb16fc0: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb16fc4: cmp             w0, NULL
    // 0xb16fc8: b.ne            #0xb16fd4
    // 0xb16fcc: r3 = ""
    //     0xb16fcc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb16fd0: b               #0xb16fd8
    // 0xb16fd4: mov             x3, x0
    // 0xb16fd8: ldur            x2, [fp, #-0x10]
    // 0xb16fdc: ldur            x0, [fp, #-0x28]
    // 0xb16fe0: stur            x3, [fp, #-0x20]
    // 0xb16fe4: LoadField: r1 = r2->field_f
    //     0xb16fe4: ldur            w1, [x2, #0xf]
    // 0xb16fe8: DecompressPointer r1
    //     0xb16fe8: add             x1, x1, HEAP, lsl #32
    // 0xb16fec: r0 = of()
    //     0xb16fec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb16ff0: LoadField: r1 = r0->field_87
    //     0xb16ff0: ldur            w1, [x0, #0x87]
    // 0xb16ff4: DecompressPointer r1
    //     0xb16ff4: add             x1, x1, HEAP, lsl #32
    // 0xb16ff8: LoadField: r0 = r1->field_2b
    //     0xb16ff8: ldur            w0, [x1, #0x2b]
    // 0xb16ffc: DecompressPointer r0
    //     0xb16ffc: add             x0, x0, HEAP, lsl #32
    // 0xb17000: LoadField: r1 = r0->field_13
    //     0xb17000: ldur            w1, [x0, #0x13]
    // 0xb17004: DecompressPointer r1
    //     0xb17004: add             x1, x1, HEAP, lsl #32
    // 0xb17008: r16 = Instance_Color
    //     0xb17008: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1700c: stp             x16, x1, [SP]
    // 0xb17010: r1 = Instance_TextStyle
    //     0xb17010: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb17014: ldr             x1, [x1, #0x9b0]
    // 0xb17018: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb17018: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb1701c: ldr             x4, [x4, #0x9b8]
    // 0xb17020: r0 = copyWith()
    //     0xb17020: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb17024: ldur            x2, [fp, #-0x10]
    // 0xb17028: stur            x0, [fp, #-0x30]
    // 0xb1702c: LoadField: r1 = r2->field_f
    //     0xb1702c: ldur            w1, [x2, #0xf]
    // 0xb17030: DecompressPointer r1
    //     0xb17030: add             x1, x1, HEAP, lsl #32
    // 0xb17034: r0 = of()
    //     0xb17034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb17038: LoadField: r1 = r0->field_87
    //     0xb17038: ldur            w1, [x0, #0x87]
    // 0xb1703c: DecompressPointer r1
    //     0xb1703c: add             x1, x1, HEAP, lsl #32
    // 0xb17040: LoadField: r0 = r1->field_7
    //     0xb17040: ldur            w0, [x1, #7]
    // 0xb17044: DecompressPointer r0
    //     0xb17044: add             x0, x0, HEAP, lsl #32
    // 0xb17048: r16 = Instance_Color
    //     0xb17048: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb1704c: r30 = 12.000000
    //     0xb1704c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb17050: ldr             lr, [lr, #0x9e8]
    // 0xb17054: stp             lr, x16, [SP]
    // 0xb17058: mov             x1, x0
    // 0xb1705c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb1705c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb17060: ldr             x4, [x4, #0x9b8]
    // 0xb17064: r0 = copyWith()
    //     0xb17064: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb17068: ldur            x2, [fp, #-0x10]
    // 0xb1706c: stur            x0, [fp, #-0x38]
    // 0xb17070: LoadField: r1 = r2->field_f
    //     0xb17070: ldur            w1, [x2, #0xf]
    // 0xb17074: DecompressPointer r1
    //     0xb17074: add             x1, x1, HEAP, lsl #32
    // 0xb17078: r0 = of()
    //     0xb17078: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1707c: LoadField: r1 = r0->field_87
    //     0xb1707c: ldur            w1, [x0, #0x87]
    // 0xb17080: DecompressPointer r1
    //     0xb17080: add             x1, x1, HEAP, lsl #32
    // 0xb17084: LoadField: r0 = r1->field_7
    //     0xb17084: ldur            w0, [x1, #7]
    // 0xb17088: DecompressPointer r0
    //     0xb17088: add             x0, x0, HEAP, lsl #32
    // 0xb1708c: r16 = Instance_Color
    //     0xb1708c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb17090: r30 = 12.000000
    //     0xb17090: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb17094: ldr             lr, [lr, #0x9e8]
    // 0xb17098: stp             lr, x16, [SP]
    // 0xb1709c: mov             x1, x0
    // 0xb170a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb170a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb170a4: ldr             x4, [x4, #0x9b8]
    // 0xb170a8: r0 = copyWith()
    //     0xb170a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb170ac: stur            x0, [fp, #-0x40]
    // 0xb170b0: r0 = ReadMoreText()
    //     0xb170b0: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb170b4: mov             x1, x0
    // 0xb170b8: ldur            x0, [fp, #-0x20]
    // 0xb170bc: stur            x1, [fp, #-0x48]
    // 0xb170c0: StoreField: r1->field_3f = r0
    //     0xb170c0: stur            w0, [x1, #0x3f]
    // 0xb170c4: r0 = " Read Less"
    //     0xb170c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb170c8: ldr             x0, [x0, #0x9c0]
    // 0xb170cc: StoreField: r1->field_43 = r0
    //     0xb170cc: stur            w0, [x1, #0x43]
    // 0xb170d0: r0 = "Read More"
    //     0xb170d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb170d4: ldr             x0, [x0, #0x9c8]
    // 0xb170d8: StoreField: r1->field_47 = r0
    //     0xb170d8: stur            w0, [x1, #0x47]
    // 0xb170dc: r0 = 240
    //     0xb170dc: movz            x0, #0xf0
    // 0xb170e0: StoreField: r1->field_f = r0
    //     0xb170e0: stur            x0, [x1, #0xf]
    // 0xb170e4: r0 = 2
    //     0xb170e4: movz            x0, #0x2
    // 0xb170e8: ArrayStore: r1[0] = r0  ; List_8
    //     0xb170e8: stur            x0, [x1, #0x17]
    // 0xb170ec: r0 = Instance_TrimMode
    //     0xb170ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb170f0: ldr             x0, [x0, #0x9d0]
    // 0xb170f4: StoreField: r1->field_1f = r0
    //     0xb170f4: stur            w0, [x1, #0x1f]
    // 0xb170f8: ldur            x0, [fp, #-0x30]
    // 0xb170fc: StoreField: r1->field_4f = r0
    //     0xb170fc: stur            w0, [x1, #0x4f]
    // 0xb17100: r0 = Instance_TextAlign
    //     0xb17100: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb17104: StoreField: r1->field_53 = r0
    //     0xb17104: stur            w0, [x1, #0x53]
    // 0xb17108: ldur            x0, [fp, #-0x38]
    // 0xb1710c: StoreField: r1->field_23 = r0
    //     0xb1710c: stur            w0, [x1, #0x23]
    // 0xb17110: ldur            x0, [fp, #-0x40]
    // 0xb17114: StoreField: r1->field_27 = r0
    //     0xb17114: stur            w0, [x1, #0x27]
    // 0xb17118: r0 = "… "
    //     0xb17118: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb1711c: ldr             x0, [x0, #0x9d8]
    // 0xb17120: StoreField: r1->field_3b = r0
    //     0xb17120: stur            w0, [x1, #0x3b]
    // 0xb17124: r0 = true
    //     0xb17124: add             x0, NULL, #0x20  ; true
    // 0xb17128: StoreField: r1->field_37 = r0
    //     0xb17128: stur            w0, [x1, #0x37]
    // 0xb1712c: r0 = Padding()
    //     0xb1712c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb17130: mov             x2, x0
    // 0xb17134: r0 = Instance_EdgeInsets
    //     0xb17134: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xb17138: ldr             x0, [x0, #0x9e0]
    // 0xb1713c: stur            x2, [fp, #-0x20]
    // 0xb17140: StoreField: r2->field_f = r0
    //     0xb17140: stur            w0, [x2, #0xf]
    // 0xb17144: ldur            x0, [fp, #-0x48]
    // 0xb17148: StoreField: r2->field_b = r0
    //     0xb17148: stur            w0, [x2, #0xb]
    // 0xb1714c: ldur            x0, [fp, #-0x28]
    // 0xb17150: LoadField: r1 = r0->field_b
    //     0xb17150: ldur            w1, [x0, #0xb]
    // 0xb17154: LoadField: r3 = r0->field_f
    //     0xb17154: ldur            w3, [x0, #0xf]
    // 0xb17158: DecompressPointer r3
    //     0xb17158: add             x3, x3, HEAP, lsl #32
    // 0xb1715c: LoadField: r4 = r3->field_b
    //     0xb1715c: ldur            w4, [x3, #0xb]
    // 0xb17160: r3 = LoadInt32Instr(r1)
    //     0xb17160: sbfx            x3, x1, #1, #0x1f
    // 0xb17164: stur            x3, [fp, #-0x50]
    // 0xb17168: r1 = LoadInt32Instr(r4)
    //     0xb17168: sbfx            x1, x4, #1, #0x1f
    // 0xb1716c: cmp             x3, x1
    // 0xb17170: b.ne            #0xb1717c
    // 0xb17174: mov             x1, x0
    // 0xb17178: r0 = _growToNextCapacity()
    //     0xb17178: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb1717c: ldur            x3, [fp, #-0x28]
    // 0xb17180: ldur            x2, [fp, #-0x50]
    // 0xb17184: add             x0, x2, #1
    // 0xb17188: lsl             x1, x0, #1
    // 0xb1718c: StoreField: r3->field_b = r1
    //     0xb1718c: stur            w1, [x3, #0xb]
    // 0xb17190: LoadField: r1 = r3->field_f
    //     0xb17190: ldur            w1, [x3, #0xf]
    // 0xb17194: DecompressPointer r1
    //     0xb17194: add             x1, x1, HEAP, lsl #32
    // 0xb17198: ldur            x0, [fp, #-0x20]
    // 0xb1719c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb1719c: add             x25, x1, x2, lsl #2
    //     0xb171a0: add             x25, x25, #0xf
    //     0xb171a4: str             w0, [x25]
    //     0xb171a8: tbz             w0, #0, #0xb171c4
    //     0xb171ac: ldurb           w16, [x1, #-1]
    //     0xb171b0: ldurb           w17, [x0, #-1]
    //     0xb171b4: and             x16, x17, x16, lsr #2
    //     0xb171b8: tst             x16, HEAP, lsr #32
    //     0xb171bc: b.eq            #0xb171c4
    //     0xb171c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb171c4: b               #0xb171cc
    // 0xb171c8: mov             x3, x2
    // 0xb171cc: ldur            x0, [fp, #-8]
    // 0xb171d0: cmp             w0, NULL
    // 0xb171d4: b.ne            #0xb171e0
    // 0xb171d8: r1 = Null
    //     0xb171d8: mov             x1, NULL
    // 0xb171dc: b               #0xb171f0
    // 0xb171e0: LoadField: r1 = r0->field_1b
    //     0xb171e0: ldur            w1, [x0, #0x1b]
    // 0xb171e4: DecompressPointer r1
    //     0xb171e4: add             x1, x1, HEAP, lsl #32
    // 0xb171e8: LoadField: r2 = r1->field_b
    //     0xb171e8: ldur            w2, [x1, #0xb]
    // 0xb171ec: mov             x1, x2
    // 0xb171f0: cmp             w1, NULL
    // 0xb171f4: b.eq            #0xb1755c
    // 0xb171f8: r2 = LoadInt32Instr(r1)
    //     0xb171f8: sbfx            x2, x1, #1, #0x1f
    // 0xb171fc: cmp             x2, #1
    // 0xb17200: r16 = true
    //     0xb17200: add             x16, NULL, #0x20  ; true
    // 0xb17204: r17 = false
    //     0xb17204: add             x17, NULL, #0x30  ; false
    // 0xb17208: csel            x4, x16, x17, ge
    // 0xb1720c: stur            x4, [fp, #-0x20]
    // 0xb17210: cmp             w0, NULL
    // 0xb17214: b.ne            #0xb17220
    // 0xb17218: r0 = Null
    //     0xb17218: mov             x0, NULL
    // 0xb1721c: b               #0xb1722c
    // 0xb17220: LoadField: r1 = r0->field_1b
    //     0xb17220: ldur            w1, [x0, #0x1b]
    // 0xb17224: DecompressPointer r1
    //     0xb17224: add             x1, x1, HEAP, lsl #32
    // 0xb17228: LoadField: r0 = r1->field_b
    //     0xb17228: ldur            w0, [x1, #0xb]
    // 0xb1722c: cmp             w0, NULL
    // 0xb17230: b.ne            #0xb1723c
    // 0xb17234: r0 = 0
    //     0xb17234: movz            x0, #0
    // 0xb17238: b               #0xb17244
    // 0xb1723c: r1 = LoadInt32Instr(r0)
    //     0xb1723c: sbfx            x1, x0, #1, #0x1f
    // 0xb17240: mov             x0, x1
    // 0xb17244: stur            x0, [fp, #-0x50]
    // 0xb17248: r1 = Function '<anonymous closure>':.
    //     0xb17248: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abb8] AnonymousClosure: (0x9b3480), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb1724c: ldr             x1, [x1, #0xbb8]
    // 0xb17250: r2 = Null
    //     0xb17250: mov             x2, NULL
    // 0xb17254: r0 = AllocateClosure()
    //     0xb17254: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17258: ldur            x2, [fp, #-0x10]
    // 0xb1725c: r1 = Function '<anonymous closure>':.
    //     0xb1725c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abc0] AnonymousClosure: (0xb18010), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb1658c)
    //     0xb17260: ldr             x1, [x1, #0xbc0]
    // 0xb17264: stur            x0, [fp, #-8]
    // 0xb17268: r0 = AllocateClosure()
    //     0xb17268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb1726c: stur            x0, [fp, #-0x30]
    // 0xb17270: r0 = ListView()
    //     0xb17270: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb17274: stur            x0, [fp, #-0x38]
    // 0xb17278: r16 = true
    //     0xb17278: add             x16, NULL, #0x20  ; true
    // 0xb1727c: r30 = Instance_Axis
    //     0xb1727c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb17280: stp             lr, x16, [SP, #8]
    // 0xb17284: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb17284: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb17288: ldr             x16, [x16, #0x1c8]
    // 0xb1728c: str             x16, [SP]
    // 0xb17290: mov             x1, x0
    // 0xb17294: ldur            x2, [fp, #-0x30]
    // 0xb17298: ldur            x3, [fp, #-0x50]
    // 0xb1729c: ldur            x5, [fp, #-8]
    // 0xb172a0: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xb172a0: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a0f8] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb172a4: ldr             x4, [x4, #0xf8]
    // 0xb172a8: r0 = ListView.separated()
    //     0xb172a8: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb172ac: r0 = SizedBox()
    //     0xb172ac: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb172b0: mov             x1, x0
    // 0xb172b4: r0 = inf
    //     0xb172b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb172b8: ldr             x0, [x0, #0x9f8]
    // 0xb172bc: stur            x1, [fp, #-8]
    // 0xb172c0: StoreField: r1->field_f = r0
    //     0xb172c0: stur            w0, [x1, #0xf]
    // 0xb172c4: r0 = 48.000000
    //     0xb172c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb172c8: ldr             x0, [x0, #0xad8]
    // 0xb172cc: StoreField: r1->field_13 = r0
    //     0xb172cc: stur            w0, [x1, #0x13]
    // 0xb172d0: ldur            x0, [fp, #-0x38]
    // 0xb172d4: StoreField: r1->field_b = r0
    //     0xb172d4: stur            w0, [x1, #0xb]
    // 0xb172d8: r0 = Padding()
    //     0xb172d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb172dc: mov             x1, x0
    // 0xb172e0: r0 = Instance_EdgeInsets
    //     0xb172e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb172e4: ldr             x0, [x0, #0xa00]
    // 0xb172e8: stur            x1, [fp, #-0x30]
    // 0xb172ec: StoreField: r1->field_f = r0
    //     0xb172ec: stur            w0, [x1, #0xf]
    // 0xb172f0: ldur            x0, [fp, #-8]
    // 0xb172f4: StoreField: r1->field_b = r0
    //     0xb172f4: stur            w0, [x1, #0xb]
    // 0xb172f8: r0 = Visibility()
    //     0xb172f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb172fc: mov             x1, x0
    // 0xb17300: ldur            x0, [fp, #-0x30]
    // 0xb17304: stur            x1, [fp, #-8]
    // 0xb17308: StoreField: r1->field_b = r0
    //     0xb17308: stur            w0, [x1, #0xb]
    // 0xb1730c: r0 = Instance_SizedBox
    //     0xb1730c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb17310: StoreField: r1->field_f = r0
    //     0xb17310: stur            w0, [x1, #0xf]
    // 0xb17314: ldur            x0, [fp, #-0x20]
    // 0xb17318: StoreField: r1->field_13 = r0
    //     0xb17318: stur            w0, [x1, #0x13]
    // 0xb1731c: r0 = false
    //     0xb1731c: add             x0, NULL, #0x30  ; false
    // 0xb17320: ArrayStore: r1[0] = r0  ; List_4
    //     0xb17320: stur            w0, [x1, #0x17]
    // 0xb17324: StoreField: r1->field_1b = r0
    //     0xb17324: stur            w0, [x1, #0x1b]
    // 0xb17328: StoreField: r1->field_1f = r0
    //     0xb17328: stur            w0, [x1, #0x1f]
    // 0xb1732c: StoreField: r1->field_23 = r0
    //     0xb1732c: stur            w0, [x1, #0x23]
    // 0xb17330: StoreField: r1->field_27 = r0
    //     0xb17330: stur            w0, [x1, #0x27]
    // 0xb17334: StoreField: r1->field_2b = r0
    //     0xb17334: stur            w0, [x1, #0x2b]
    // 0xb17338: r0 = GestureDetector()
    //     0xb17338: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb1733c: ldur            x2, [fp, #-0x10]
    // 0xb17340: r1 = Function '<anonymous closure>':.
    //     0xb17340: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abc8] AnonymousClosure: (0xa90680), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xb17344: ldr             x1, [x1, #0xbc8]
    // 0xb17348: stur            x0, [fp, #-0x20]
    // 0xb1734c: r0 = AllocateClosure()
    //     0xb1734c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17350: ldur            x2, [fp, #-0x10]
    // 0xb17354: r1 = Function '<anonymous closure>':.
    //     0xb17354: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abd0] AnonymousClosure: (0xb17560), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb1658c)
    //     0xb17358: ldr             x1, [x1, #0xbd0]
    // 0xb1735c: stur            x0, [fp, #-0x10]
    // 0xb17360: r0 = AllocateClosure()
    //     0xb17360: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17364: ldur            x16, [fp, #-0x10]
    // 0xb17368: stp             x0, x16, [SP, #8]
    // 0xb1736c: r16 = Instance_Icon
    //     0xb1736c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa18] Obj!Icon@d65e71
    //     0xb17370: ldr             x16, [x16, #0xa18]
    // 0xb17374: str             x16, [SP]
    // 0xb17378: ldur            x1, [fp, #-0x20]
    // 0xb1737c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb1737c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb17380: ldr             x4, [x4, #0xa20]
    // 0xb17384: r0 = GestureDetector()
    //     0xb17384: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb17388: r0 = Align()
    //     0xb17388: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb1738c: mov             x3, x0
    // 0xb17390: r0 = Instance_Alignment
    //     0xb17390: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xb17394: ldr             x0, [x0, #0xa28]
    // 0xb17398: stur            x3, [fp, #-0x10]
    // 0xb1739c: StoreField: r3->field_f = r0
    //     0xb1739c: stur            w0, [x3, #0xf]
    // 0xb173a0: ldur            x1, [fp, #-0x20]
    // 0xb173a4: StoreField: r3->field_b = r1
    //     0xb173a4: stur            w1, [x3, #0xb]
    // 0xb173a8: r1 = Null
    //     0xb173a8: mov             x1, NULL
    // 0xb173ac: r2 = 4
    //     0xb173ac: movz            x2, #0x4
    // 0xb173b0: r0 = AllocateArray()
    //     0xb173b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb173b4: mov             x2, x0
    // 0xb173b8: ldur            x0, [fp, #-8]
    // 0xb173bc: stur            x2, [fp, #-0x20]
    // 0xb173c0: StoreField: r2->field_f = r0
    //     0xb173c0: stur            w0, [x2, #0xf]
    // 0xb173c4: ldur            x0, [fp, #-0x10]
    // 0xb173c8: StoreField: r2->field_13 = r0
    //     0xb173c8: stur            w0, [x2, #0x13]
    // 0xb173cc: r1 = <Widget>
    //     0xb173cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb173d0: r0 = AllocateGrowableArray()
    //     0xb173d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb173d4: mov             x1, x0
    // 0xb173d8: ldur            x0, [fp, #-0x20]
    // 0xb173dc: stur            x1, [fp, #-8]
    // 0xb173e0: StoreField: r1->field_f = r0
    //     0xb173e0: stur            w0, [x1, #0xf]
    // 0xb173e4: r0 = 4
    //     0xb173e4: movz            x0, #0x4
    // 0xb173e8: StoreField: r1->field_b = r0
    //     0xb173e8: stur            w0, [x1, #0xb]
    // 0xb173ec: r0 = Stack()
    //     0xb173ec: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb173f0: mov             x2, x0
    // 0xb173f4: r0 = Instance_Alignment
    //     0xb173f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xb173f8: ldr             x0, [x0, #0xa28]
    // 0xb173fc: stur            x2, [fp, #-0x10]
    // 0xb17400: StoreField: r2->field_f = r0
    //     0xb17400: stur            w0, [x2, #0xf]
    // 0xb17404: r0 = Instance_StackFit
    //     0xb17404: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb17408: ldr             x0, [x0, #0xfa8]
    // 0xb1740c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1740c: stur            w0, [x2, #0x17]
    // 0xb17410: r0 = Instance_Clip
    //     0xb17410: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb17414: ldr             x0, [x0, #0x7e0]
    // 0xb17418: StoreField: r2->field_1b = r0
    //     0xb17418: stur            w0, [x2, #0x1b]
    // 0xb1741c: ldur            x0, [fp, #-8]
    // 0xb17420: StoreField: r2->field_b = r0
    //     0xb17420: stur            w0, [x2, #0xb]
    // 0xb17424: ldur            x0, [fp, #-0x28]
    // 0xb17428: LoadField: r1 = r0->field_b
    //     0xb17428: ldur            w1, [x0, #0xb]
    // 0xb1742c: LoadField: r3 = r0->field_f
    //     0xb1742c: ldur            w3, [x0, #0xf]
    // 0xb17430: DecompressPointer r3
    //     0xb17430: add             x3, x3, HEAP, lsl #32
    // 0xb17434: LoadField: r4 = r3->field_b
    //     0xb17434: ldur            w4, [x3, #0xb]
    // 0xb17438: r3 = LoadInt32Instr(r1)
    //     0xb17438: sbfx            x3, x1, #1, #0x1f
    // 0xb1743c: stur            x3, [fp, #-0x50]
    // 0xb17440: r1 = LoadInt32Instr(r4)
    //     0xb17440: sbfx            x1, x4, #1, #0x1f
    // 0xb17444: cmp             x3, x1
    // 0xb17448: b.ne            #0xb17454
    // 0xb1744c: mov             x1, x0
    // 0xb17450: r0 = _growToNextCapacity()
    //     0xb17450: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb17454: ldur            x2, [fp, #-0x28]
    // 0xb17458: ldur            x3, [fp, #-0x50]
    // 0xb1745c: add             x0, x3, #1
    // 0xb17460: lsl             x1, x0, #1
    // 0xb17464: StoreField: r2->field_b = r1
    //     0xb17464: stur            w1, [x2, #0xb]
    // 0xb17468: LoadField: r1 = r2->field_f
    //     0xb17468: ldur            w1, [x2, #0xf]
    // 0xb1746c: DecompressPointer r1
    //     0xb1746c: add             x1, x1, HEAP, lsl #32
    // 0xb17470: ldur            x0, [fp, #-0x10]
    // 0xb17474: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb17474: add             x25, x1, x3, lsl #2
    //     0xb17478: add             x25, x25, #0xf
    //     0xb1747c: str             w0, [x25]
    //     0xb17480: tbz             w0, #0, #0xb1749c
    //     0xb17484: ldurb           w16, [x1, #-1]
    //     0xb17488: ldurb           w17, [x0, #-1]
    //     0xb1748c: and             x16, x17, x16, lsr #2
    //     0xb17490: tst             x16, HEAP, lsr #32
    //     0xb17494: b.eq            #0xb1749c
    //     0xb17498: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb1749c: r0 = Column()
    //     0xb1749c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb174a0: mov             x1, x0
    // 0xb174a4: r0 = Instance_Axis
    //     0xb174a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb174a8: stur            x1, [fp, #-8]
    // 0xb174ac: StoreField: r1->field_f = r0
    //     0xb174ac: stur            w0, [x1, #0xf]
    // 0xb174b0: r0 = Instance_MainAxisAlignment
    //     0xb174b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb174b4: ldr             x0, [x0, #0xa08]
    // 0xb174b8: StoreField: r1->field_13 = r0
    //     0xb174b8: stur            w0, [x1, #0x13]
    // 0xb174bc: r0 = Instance_MainAxisSize
    //     0xb174bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb174c0: ldr             x0, [x0, #0xa10]
    // 0xb174c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb174c4: stur            w0, [x1, #0x17]
    // 0xb174c8: r0 = Instance_CrossAxisAlignment
    //     0xb174c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb174cc: ldr             x0, [x0, #0x890]
    // 0xb174d0: StoreField: r1->field_1b = r0
    //     0xb174d0: stur            w0, [x1, #0x1b]
    // 0xb174d4: r0 = Instance_VerticalDirection
    //     0xb174d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb174d8: ldr             x0, [x0, #0xa20]
    // 0xb174dc: StoreField: r1->field_23 = r0
    //     0xb174dc: stur            w0, [x1, #0x23]
    // 0xb174e0: r0 = Instance_Clip
    //     0xb174e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb174e4: ldr             x0, [x0, #0x38]
    // 0xb174e8: StoreField: r1->field_2b = r0
    //     0xb174e8: stur            w0, [x1, #0x2b]
    // 0xb174ec: StoreField: r1->field_2f = rZR
    //     0xb174ec: stur            xzr, [x1, #0x2f]
    // 0xb174f0: ldur            x0, [fp, #-0x28]
    // 0xb174f4: StoreField: r1->field_b = r0
    //     0xb174f4: stur            w0, [x1, #0xb]
    // 0xb174f8: r0 = Padding()
    //     0xb174f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb174fc: mov             x1, x0
    // 0xb17500: r0 = Instance_EdgeInsets
    //     0xb17500: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb17504: ldr             x0, [x0, #0x1f0]
    // 0xb17508: stur            x1, [fp, #-0x10]
    // 0xb1750c: StoreField: r1->field_f = r0
    //     0xb1750c: stur            w0, [x1, #0xf]
    // 0xb17510: ldur            x0, [fp, #-8]
    // 0xb17514: StoreField: r1->field_b = r0
    //     0xb17514: stur            w0, [x1, #0xb]
    // 0xb17518: r0 = Container()
    //     0xb17518: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb1751c: stur            x0, [fp, #-8]
    // 0xb17520: ldur            x16, [fp, #-0x18]
    // 0xb17524: ldur            lr, [fp, #-0x10]
    // 0xb17528: stp             lr, x16, [SP]
    // 0xb1752c: mov             x1, x0
    // 0xb17530: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb17530: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb17534: ldr             x4, [x4, #0x88]
    // 0xb17538: r0 = Container()
    //     0xb17538: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb1753c: ldur            x0, [fp, #-8]
    // 0xb17540: LeaveFrame
    //     0xb17540: mov             SP, fp
    //     0xb17544: ldp             fp, lr, [SP], #0x10
    // 0xb17548: ret
    //     0xb17548: ret             
    // 0xb1754c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb1754c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17550: b               #0xb166c4
    // 0xb17554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb17554: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb17558: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb17558: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb1755c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1755c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb17560, size: 0x104
    // 0xb17560: EnterFrame
    //     0xb17560: stp             fp, lr, [SP, #-0x10]!
    //     0xb17564: mov             fp, SP
    // 0xb17568: AllocStack(0x28)
    //     0xb17568: sub             SP, SP, #0x28
    // 0xb1756c: SetupParameters()
    //     0xb1756c: ldr             x0, [fp, #0x10]
    //     0xb17570: ldur            w1, [x0, #0x17]
    //     0xb17574: add             x1, x1, HEAP, lsl #32
    //     0xb17578: stur            x1, [fp, #-0x10]
    // 0xb1757c: CheckStackOverflow
    //     0xb1757c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17580: cmp             SP, x16
    //     0xb17584: b.ls            #0xb17658
    // 0xb17588: LoadField: r0 = r1->field_b
    //     0xb17588: ldur            w0, [x1, #0xb]
    // 0xb1758c: DecompressPointer r0
    //     0xb1758c: add             x0, x0, HEAP, lsl #32
    // 0xb17590: stur            x0, [fp, #-8]
    // 0xb17594: LoadField: r2 = r0->field_f
    //     0xb17594: ldur            w2, [x0, #0xf]
    // 0xb17598: DecompressPointer r2
    //     0xb17598: add             x2, x2, HEAP, lsl #32
    // 0xb1759c: LoadField: r3 = r2->field_13
    //     0xb1759c: ldur            w3, [x2, #0x13]
    // 0xb175a0: DecompressPointer r3
    //     0xb175a0: add             x3, x3, HEAP, lsl #32
    // 0xb175a4: cmp             w3, NULL
    // 0xb175a8: b.eq            #0xb17648
    // 0xb175ac: LoadField: r3 = r2->field_b
    //     0xb175ac: ldur            w3, [x2, #0xb]
    // 0xb175b0: DecompressPointer r3
    //     0xb175b0: add             x3, x3, HEAP, lsl #32
    // 0xb175b4: cmp             w3, NULL
    // 0xb175b8: b.eq            #0xb17660
    // 0xb175bc: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb175bc: ldur            w2, [x3, #0x17]
    // 0xb175c0: DecompressPointer r2
    //     0xb175c0: add             x2, x2, HEAP, lsl #32
    // 0xb175c4: r16 = "flag_dots"
    //     0xb175c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa30] "flag_dots"
    //     0xb175c8: ldr             x16, [x16, #0xa30]
    // 0xb175cc: stp             x16, x2, [SP, #8]
    // 0xb175d0: r16 = ""
    //     0xb175d0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb175d4: str             x16, [SP]
    // 0xb175d8: r4 = 0
    //     0xb175d8: movz            x4, #0
    // 0xb175dc: ldr             x0, [SP, #0x10]
    // 0xb175e0: r16 = UnlinkedCall_0x613b5c
    //     0xb175e0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6abd8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb175e4: add             x16, x16, #0xbd8
    // 0xb175e8: ldp             x5, lr, [x16]
    // 0xb175ec: blr             lr
    // 0xb175f0: ldur            x0, [fp, #-8]
    // 0xb175f4: LoadField: r1 = r0->field_f
    //     0xb175f4: ldur            w1, [x0, #0xf]
    // 0xb175f8: DecompressPointer r1
    //     0xb175f8: add             x1, x1, HEAP, lsl #32
    // 0xb175fc: ldur            x0, [fp, #-0x10]
    // 0xb17600: LoadField: r2 = r0->field_f
    //     0xb17600: ldur            w2, [x0, #0xf]
    // 0xb17604: DecompressPointer r2
    //     0xb17604: add             x2, x2, HEAP, lsl #32
    // 0xb17608: LoadField: r3 = r1->field_13
    //     0xb17608: ldur            w3, [x1, #0x13]
    // 0xb1760c: DecompressPointer r3
    //     0xb1760c: add             x3, x3, HEAP, lsl #32
    // 0xb17610: LoadField: r4 = r0->field_13
    //     0xb17610: ldur            w4, [x0, #0x13]
    // 0xb17614: DecompressPointer r4
    //     0xb17614: add             x4, x4, HEAP, lsl #32
    // 0xb17618: cmp             w4, NULL
    // 0xb1761c: b.ne            #0xb17628
    // 0xb17620: r0 = Null
    //     0xb17620: mov             x0, NULL
    // 0xb17624: b               #0xb17630
    // 0xb17628: LoadField: r0 = r4->field_b
    //     0xb17628: ldur            w0, [x4, #0xb]
    // 0xb1762c: DecompressPointer r0
    //     0xb1762c: add             x0, x0, HEAP, lsl #32
    // 0xb17630: cmp             w0, NULL
    // 0xb17634: b.ne            #0xb17640
    // 0xb17638: r5 = ""
    //     0xb17638: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb1763c: b               #0xb17644
    // 0xb17640: mov             x5, x0
    // 0xb17644: r0 = showMenuItem()
    //     0xb17644: bl              #0xb17664  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem
    // 0xb17648: r0 = Null
    //     0xb17648: mov             x0, NULL
    // 0xb1764c: LeaveFrame
    //     0xb1764c: mov             SP, fp
    //     0xb17650: ldp             fp, lr, [SP], #0x10
    // 0xb17654: ret
    //     0xb17654: ret             
    // 0xb17658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17658: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1765c: b               #0xb17588
    // 0xb17660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb17660: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb17664, size: 0x6b0
    // 0xb17664: EnterFrame
    //     0xb17664: stp             fp, lr, [SP, #-0x10]!
    //     0xb17668: mov             fp, SP
    // 0xb1766c: AllocStack(0x98)
    //     0xb1766c: sub             SP, SP, #0x98
    // 0xb17670: SetupParameters(_ReviewWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb17670: mov             x0, x1
    //     0xb17674: stur            x1, [fp, #-8]
    //     0xb17678: mov             x1, x2
    //     0xb1767c: stur            x2, [fp, #-0x10]
    //     0xb17680: mov             x2, x5
    //     0xb17684: stur            x3, [fp, #-0x18]
    //     0xb17688: stur            x5, [fp, #-0x20]
    // 0xb1768c: CheckStackOverflow
    //     0xb1768c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17690: cmp             SP, x16
    //     0xb17694: b.ls            #0xb17c8c
    // 0xb17698: r1 = 2
    //     0xb17698: movz            x1, #0x2
    // 0xb1769c: r0 = AllocateContext()
    //     0xb1769c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb176a0: mov             x4, x0
    // 0xb176a4: ldur            x3, [fp, #-8]
    // 0xb176a8: stur            x4, [fp, #-0x28]
    // 0xb176ac: StoreField: r4->field_f = r3
    //     0xb176ac: stur            w3, [x4, #0xf]
    // 0xb176b0: ldur            x2, [fp, #-0x20]
    // 0xb176b4: StoreField: r4->field_13 = r2
    //     0xb176b4: stur            w2, [x4, #0x13]
    // 0xb176b8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb176b8: ldur            w1, [x3, #0x17]
    // 0xb176bc: DecompressPointer r1
    //     0xb176bc: add             x1, x1, HEAP, lsl #32
    // 0xb176c0: r0 = LoadClassIdInstr(r1)
    //     0xb176c0: ldur            x0, [x1, #-1]
    //     0xb176c4: ubfx            x0, x0, #0xc, #0x14
    // 0xb176c8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb176c8: sub             lr, x0, #0xfe
    //     0xb176cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb176d0: blr             lr
    // 0xb176d4: r1 = 60
    //     0xb176d4: movz            x1, #0x3c
    // 0xb176d8: branchIfSmi(r0, 0xb176e4)
    //     0xb176d8: tbz             w0, #0, #0xb176e4
    // 0xb176dc: r1 = LoadClassIdInstr(r0)
    //     0xb176dc: ldur            x1, [x0, #-1]
    //     0xb176e0: ubfx            x1, x1, #0xc, #0x14
    // 0xb176e4: r16 = true
    //     0xb176e4: add             x16, NULL, #0x20  ; true
    // 0xb176e8: stp             x16, x0, [SP]
    // 0xb176ec: mov             x0, x1
    // 0xb176f0: mov             lr, x0
    // 0xb176f4: ldr             lr, [x21, lr, lsl #3]
    // 0xb176f8: blr             lr
    // 0xb176fc: tbz             w0, #4, #0xb1770c
    // 0xb17700: d0 = 120.000000
    //     0xb17700: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb17704: ldr             d0, [x17, #0xa38]
    // 0xb17708: b               #0xb17710
    // 0xb1770c: d0 = 100.000000
    //     0xb1770c: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb17710: ldur            x0, [fp, #-0x18]
    // 0xb17714: stur            d0, [fp, #-0x50]
    // 0xb17718: r0 = BoxConstraints()
    //     0xb17718: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb1771c: stur            x0, [fp, #-0x20]
    // 0xb17720: StoreField: r0->field_7 = rZR
    //     0xb17720: stur            xzr, [x0, #7]
    // 0xb17724: ldur            d0, [fp, #-0x50]
    // 0xb17728: StoreField: r0->field_f = d0
    //     0xb17728: stur            d0, [x0, #0xf]
    // 0xb1772c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb1772c: stur            xzr, [x0, #0x17]
    // 0xb17730: d0 = inf
    //     0xb17730: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb17734: StoreField: r0->field_1f = d0
    //     0xb17734: stur            d0, [x0, #0x1f]
    // 0xb17738: ldur            x1, [fp, #-0x18]
    // 0xb1773c: cmp             w1, NULL
    // 0xb17740: b.ne            #0xb1774c
    // 0xb17744: r2 = Null
    //     0xb17744: mov             x2, NULL
    // 0xb17748: b               #0xb17778
    // 0xb1774c: LoadField: d0 = r1->field_7
    //     0xb1774c: ldur            d0, [x1, #7]
    // 0xb17750: r2 = inline_Allocate_Double()
    //     0xb17750: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb17754: add             x2, x2, #0x10
    //     0xb17758: cmp             x3, x2
    //     0xb1775c: b.ls            #0xb17c94
    //     0xb17760: str             x2, [THR, #0x50]  ; THR::top
    //     0xb17764: sub             x2, x2, #0xf
    //     0xb17768: movz            x3, #0xe15c
    //     0xb1776c: movk            x3, #0x3, lsl #16
    //     0xb17770: stur            x3, [x2, #-1]
    // 0xb17774: StoreField: r2->field_7 = d0
    //     0xb17774: stur            d0, [x2, #7]
    // 0xb17778: cmp             w2, NULL
    // 0xb1777c: b.ne            #0xb17788
    // 0xb17780: d0 = 0.000000
    //     0xb17780: eor             v0.16b, v0.16b, v0.16b
    // 0xb17784: b               #0xb1778c
    // 0xb17788: LoadField: d0 = r2->field_7
    //     0xb17788: ldur            d0, [x2, #7]
    // 0xb1778c: stur            d0, [fp, #-0x68]
    // 0xb17790: cmp             w1, NULL
    // 0xb17794: b.ne            #0xb177a0
    // 0xb17798: r2 = Null
    //     0xb17798: mov             x2, NULL
    // 0xb1779c: b               #0xb177cc
    // 0xb177a0: LoadField: d1 = r1->field_f
    //     0xb177a0: ldur            d1, [x1, #0xf]
    // 0xb177a4: r2 = inline_Allocate_Double()
    //     0xb177a4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb177a8: add             x2, x2, #0x10
    //     0xb177ac: cmp             x3, x2
    //     0xb177b0: b.ls            #0xb17cb0
    //     0xb177b4: str             x2, [THR, #0x50]  ; THR::top
    //     0xb177b8: sub             x2, x2, #0xf
    //     0xb177bc: movz            x3, #0xe15c
    //     0xb177c0: movk            x3, #0x3, lsl #16
    //     0xb177c4: stur            x3, [x2, #-1]
    // 0xb177c8: StoreField: r2->field_7 = d1
    //     0xb177c8: stur            d1, [x2, #7]
    // 0xb177cc: cmp             w2, NULL
    // 0xb177d0: b.ne            #0xb177dc
    // 0xb177d4: d2 = 0.000000
    //     0xb177d4: eor             v2.16b, v2.16b, v2.16b
    // 0xb177d8: b               #0xb177e4
    // 0xb177dc: LoadField: d1 = r2->field_7
    //     0xb177dc: ldur            d1, [x2, #7]
    // 0xb177e0: mov             v2.16b, v1.16b
    // 0xb177e4: d1 = 50.000000
    //     0xb177e4: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb177e8: fsub            d3, d2, d1
    // 0xb177ec: stur            d3, [fp, #-0x60]
    // 0xb177f0: cmp             w1, NULL
    // 0xb177f4: b.ne            #0xb17800
    // 0xb177f8: r2 = Null
    //     0xb177f8: mov             x2, NULL
    // 0xb177fc: b               #0xb1782c
    // 0xb17800: LoadField: d2 = r1->field_7
    //     0xb17800: ldur            d2, [x1, #7]
    // 0xb17804: r2 = inline_Allocate_Double()
    //     0xb17804: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb17808: add             x2, x2, #0x10
    //     0xb1780c: cmp             x3, x2
    //     0xb17810: b.ls            #0xb17ccc
    //     0xb17814: str             x2, [THR, #0x50]  ; THR::top
    //     0xb17818: sub             x2, x2, #0xf
    //     0xb1781c: movz            x3, #0xe15c
    //     0xb17820: movk            x3, #0x3, lsl #16
    //     0xb17824: stur            x3, [x2, #-1]
    // 0xb17828: StoreField: r2->field_7 = d2
    //     0xb17828: stur            d2, [x2, #7]
    // 0xb1782c: cmp             w2, NULL
    // 0xb17830: b.ne            #0xb1783c
    // 0xb17834: d2 = 0.000000
    //     0xb17834: eor             v2.16b, v2.16b, v2.16b
    // 0xb17838: b               #0xb17840
    // 0xb1783c: LoadField: d2 = r2->field_7
    //     0xb1783c: ldur            d2, [x2, #7]
    // 0xb17840: fadd            d4, d2, d1
    // 0xb17844: stur            d4, [fp, #-0x58]
    // 0xb17848: cmp             w1, NULL
    // 0xb1784c: b.ne            #0xb17858
    // 0xb17850: r1 = Null
    //     0xb17850: mov             x1, NULL
    // 0xb17854: b               #0xb17884
    // 0xb17858: LoadField: d1 = r1->field_f
    //     0xb17858: ldur            d1, [x1, #0xf]
    // 0xb1785c: r1 = inline_Allocate_Double()
    //     0xb1785c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb17860: add             x1, x1, #0x10
    //     0xb17864: cmp             x2, x1
    //     0xb17868: b.ls            #0xb17cf0
    //     0xb1786c: str             x1, [THR, #0x50]  ; THR::top
    //     0xb17870: sub             x1, x1, #0xf
    //     0xb17874: movz            x2, #0xe15c
    //     0xb17878: movk            x2, #0x3, lsl #16
    //     0xb1787c: stur            x2, [x1, #-1]
    // 0xb17880: StoreField: r1->field_7 = d1
    //     0xb17880: stur            d1, [x1, #7]
    // 0xb17884: cmp             w1, NULL
    // 0xb17888: b.ne            #0xb17894
    // 0xb1788c: d1 = 0.000000
    //     0xb1788c: eor             v1.16b, v1.16b, v1.16b
    // 0xb17890: b               #0xb17898
    // 0xb17894: LoadField: d1 = r1->field_7
    //     0xb17894: ldur            d1, [x1, #7]
    // 0xb17898: ldur            x1, [fp, #-8]
    // 0xb1789c: ldur            x2, [fp, #-0x28]
    // 0xb178a0: stur            d1, [fp, #-0x50]
    // 0xb178a4: r0 = RelativeRect()
    //     0xb178a4: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb178a8: ldur            d0, [fp, #-0x68]
    // 0xb178ac: stur            x0, [fp, #-0x18]
    // 0xb178b0: StoreField: r0->field_7 = d0
    //     0xb178b0: stur            d0, [x0, #7]
    // 0xb178b4: ldur            d0, [fp, #-0x60]
    // 0xb178b8: StoreField: r0->field_f = d0
    //     0xb178b8: stur            d0, [x0, #0xf]
    // 0xb178bc: ldur            d0, [fp, #-0x58]
    // 0xb178c0: ArrayStore: r0[0] = d0  ; List_8
    //     0xb178c0: stur            d0, [x0, #0x17]
    // 0xb178c4: ldur            d0, [fp, #-0x50]
    // 0xb178c8: StoreField: r0->field_1f = d0
    //     0xb178c8: stur            d0, [x0, #0x1f]
    // 0xb178cc: r1 = <Widget>
    //     0xb178cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb178d0: r2 = 0
    //     0xb178d0: movz            x2, #0
    // 0xb178d4: r0 = _GrowableList()
    //     0xb178d4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb178d8: mov             x4, x0
    // 0xb178dc: ldur            x3, [fp, #-8]
    // 0xb178e0: stur            x4, [fp, #-0x30]
    // 0xb178e4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb178e4: ldur            w1, [x3, #0x17]
    // 0xb178e8: DecompressPointer r1
    //     0xb178e8: add             x1, x1, HEAP, lsl #32
    // 0xb178ec: ldur            x5, [fp, #-0x28]
    // 0xb178f0: LoadField: r2 = r5->field_13
    //     0xb178f0: ldur            w2, [x5, #0x13]
    // 0xb178f4: DecompressPointer r2
    //     0xb178f4: add             x2, x2, HEAP, lsl #32
    // 0xb178f8: r0 = LoadClassIdInstr(r1)
    //     0xb178f8: ldur            x0, [x1, #-1]
    //     0xb178fc: ubfx            x0, x0, #0xc, #0x14
    // 0xb17900: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb17900: sub             lr, x0, #0xfe
    //     0xb17904: ldr             lr, [x21, lr, lsl #3]
    //     0xb17908: blr             lr
    // 0xb1790c: r16 = true
    //     0xb1790c: add             x16, NULL, #0x20  ; true
    // 0xb17910: cmp             w0, w16
    // 0xb17914: b.ne            #0xb17978
    // 0xb17918: ldur            x0, [fp, #-0x30]
    // 0xb1791c: LoadField: r1 = r0->field_b
    //     0xb1791c: ldur            w1, [x0, #0xb]
    // 0xb17920: LoadField: r2 = r0->field_f
    //     0xb17920: ldur            w2, [x0, #0xf]
    // 0xb17924: DecompressPointer r2
    //     0xb17924: add             x2, x2, HEAP, lsl #32
    // 0xb17928: LoadField: r3 = r2->field_b
    //     0xb17928: ldur            w3, [x2, #0xb]
    // 0xb1792c: r2 = LoadInt32Instr(r1)
    //     0xb1792c: sbfx            x2, x1, #1, #0x1f
    // 0xb17930: stur            x2, [fp, #-0x38]
    // 0xb17934: r1 = LoadInt32Instr(r3)
    //     0xb17934: sbfx            x1, x3, #1, #0x1f
    // 0xb17938: cmp             x2, x1
    // 0xb1793c: b.ne            #0xb17948
    // 0xb17940: mov             x1, x0
    // 0xb17944: r0 = _growToNextCapacity()
    //     0xb17944: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb17948: ldur            x0, [fp, #-0x30]
    // 0xb1794c: ldur            x1, [fp, #-0x38]
    // 0xb17950: add             x2, x1, #1
    // 0xb17954: lsl             x3, x2, #1
    // 0xb17958: StoreField: r0->field_b = r3
    //     0xb17958: stur            w3, [x0, #0xb]
    // 0xb1795c: LoadField: r2 = r0->field_f
    //     0xb1795c: ldur            w2, [x0, #0xf]
    // 0xb17960: DecompressPointer r2
    //     0xb17960: add             x2, x2, HEAP, lsl #32
    // 0xb17964: add             x3, x2, x1, lsl #2
    // 0xb17968: r16 = Instance_Icon
    //     0xb17968: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb1796c: ldr             x16, [x16, #0xa48]
    // 0xb17970: StoreField: r3->field_f = r16
    //     0xb17970: stur            w16, [x3, #0xf]
    // 0xb17974: b               #0xb1797c
    // 0xb17978: ldur            x0, [fp, #-0x30]
    // 0xb1797c: LoadField: r1 = r0->field_b
    //     0xb1797c: ldur            w1, [x0, #0xb]
    // 0xb17980: LoadField: r2 = r0->field_f
    //     0xb17980: ldur            w2, [x0, #0xf]
    // 0xb17984: DecompressPointer r2
    //     0xb17984: add             x2, x2, HEAP, lsl #32
    // 0xb17988: LoadField: r3 = r2->field_b
    //     0xb17988: ldur            w3, [x2, #0xb]
    // 0xb1798c: r2 = LoadInt32Instr(r1)
    //     0xb1798c: sbfx            x2, x1, #1, #0x1f
    // 0xb17990: stur            x2, [fp, #-0x38]
    // 0xb17994: r1 = LoadInt32Instr(r3)
    //     0xb17994: sbfx            x1, x3, #1, #0x1f
    // 0xb17998: cmp             x2, x1
    // 0xb1799c: b.ne            #0xb179a8
    // 0xb179a0: mov             x1, x0
    // 0xb179a4: r0 = _growToNextCapacity()
    //     0xb179a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb179a8: ldur            x1, [fp, #-8]
    // 0xb179ac: ldur            x4, [fp, #-0x28]
    // 0xb179b0: ldur            x3, [fp, #-0x30]
    // 0xb179b4: ldur            x0, [fp, #-0x38]
    // 0xb179b8: add             x2, x0, #1
    // 0xb179bc: lsl             x5, x2, #1
    // 0xb179c0: StoreField: r3->field_b = r5
    //     0xb179c0: stur            w5, [x3, #0xb]
    // 0xb179c4: LoadField: r2 = r3->field_f
    //     0xb179c4: ldur            w2, [x3, #0xf]
    // 0xb179c8: DecompressPointer r2
    //     0xb179c8: add             x2, x2, HEAP, lsl #32
    // 0xb179cc: add             x5, x2, x0, lsl #2
    // 0xb179d0: r16 = Instance_SizedBox
    //     0xb179d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb179d4: ldr             x16, [x16, #0xa50]
    // 0xb179d8: StoreField: r5->field_f = r16
    //     0xb179d8: stur            w16, [x5, #0xf]
    // 0xb179dc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb179dc: ldur            w0, [x1, #0x17]
    // 0xb179e0: DecompressPointer r0
    //     0xb179e0: add             x0, x0, HEAP, lsl #32
    // 0xb179e4: LoadField: r2 = r4->field_13
    //     0xb179e4: ldur            w2, [x4, #0x13]
    // 0xb179e8: DecompressPointer r2
    //     0xb179e8: add             x2, x2, HEAP, lsl #32
    // 0xb179ec: r1 = LoadClassIdInstr(r0)
    //     0xb179ec: ldur            x1, [x0, #-1]
    //     0xb179f0: ubfx            x1, x1, #0xc, #0x14
    // 0xb179f4: mov             x16, x0
    // 0xb179f8: mov             x0, x1
    // 0xb179fc: mov             x1, x16
    // 0xb17a00: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb17a00: sub             lr, x0, #0xfe
    //     0xb17a04: ldr             lr, [x21, lr, lsl #3]
    //     0xb17a08: blr             lr
    // 0xb17a0c: r1 = 60
    //     0xb17a0c: movz            x1, #0x3c
    // 0xb17a10: branchIfSmi(r0, 0xb17a1c)
    //     0xb17a10: tbz             w0, #0, #0xb17a1c
    // 0xb17a14: r1 = LoadClassIdInstr(r0)
    //     0xb17a14: ldur            x1, [x0, #-1]
    //     0xb17a18: ubfx            x1, x1, #0xc, #0x14
    // 0xb17a1c: r16 = true
    //     0xb17a1c: add             x16, NULL, #0x20  ; true
    // 0xb17a20: stp             x16, x0, [SP]
    // 0xb17a24: mov             x0, x1
    // 0xb17a28: mov             lr, x0
    // 0xb17a2c: ldr             lr, [x21, lr, lsl #3]
    // 0xb17a30: blr             lr
    // 0xb17a34: tbz             w0, #4, #0xb17a44
    // 0xb17a38: r0 = "Flag as abusive"
    //     0xb17a38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb17a3c: ldr             x0, [x0, #0xa60]
    // 0xb17a40: b               #0xb17a4c
    // 0xb17a44: r0 = "Flagged"
    //     0xb17a44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb17a48: ldr             x0, [x0, #0xa58]
    // 0xb17a4c: ldur            x1, [fp, #-0x10]
    // 0xb17a50: stur            x0, [fp, #-8]
    // 0xb17a54: r0 = of()
    //     0xb17a54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb17a58: LoadField: r1 = r0->field_87
    //     0xb17a58: ldur            w1, [x0, #0x87]
    // 0xb17a5c: DecompressPointer r1
    //     0xb17a5c: add             x1, x1, HEAP, lsl #32
    // 0xb17a60: LoadField: r0 = r1->field_33
    //     0xb17a60: ldur            w0, [x1, #0x33]
    // 0xb17a64: DecompressPointer r0
    //     0xb17a64: add             x0, x0, HEAP, lsl #32
    // 0xb17a68: cmp             w0, NULL
    // 0xb17a6c: b.ne            #0xb17a78
    // 0xb17a70: r2 = Null
    //     0xb17a70: mov             x2, NULL
    // 0xb17a74: b               #0xb17a9c
    // 0xb17a78: r16 = 12.000000
    //     0xb17a78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb17a7c: ldr             x16, [x16, #0x9e8]
    // 0xb17a80: r30 = Instance_Color
    //     0xb17a80: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb17a84: stp             lr, x16, [SP]
    // 0xb17a88: mov             x1, x0
    // 0xb17a8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb17a8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb17a90: ldr             x4, [x4, #0xaa0]
    // 0xb17a94: r0 = copyWith()
    //     0xb17a94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb17a98: mov             x2, x0
    // 0xb17a9c: ldur            x1, [fp, #-0x30]
    // 0xb17aa0: ldur            x0, [fp, #-8]
    // 0xb17aa4: stur            x2, [fp, #-0x40]
    // 0xb17aa8: r0 = Text()
    //     0xb17aa8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb17aac: mov             x2, x0
    // 0xb17ab0: ldur            x0, [fp, #-8]
    // 0xb17ab4: stur            x2, [fp, #-0x48]
    // 0xb17ab8: StoreField: r2->field_b = r0
    //     0xb17ab8: stur            w0, [x2, #0xb]
    // 0xb17abc: ldur            x0, [fp, #-0x40]
    // 0xb17ac0: StoreField: r2->field_13 = r0
    //     0xb17ac0: stur            w0, [x2, #0x13]
    // 0xb17ac4: ldur            x0, [fp, #-0x30]
    // 0xb17ac8: LoadField: r1 = r0->field_b
    //     0xb17ac8: ldur            w1, [x0, #0xb]
    // 0xb17acc: LoadField: r3 = r0->field_f
    //     0xb17acc: ldur            w3, [x0, #0xf]
    // 0xb17ad0: DecompressPointer r3
    //     0xb17ad0: add             x3, x3, HEAP, lsl #32
    // 0xb17ad4: LoadField: r4 = r3->field_b
    //     0xb17ad4: ldur            w4, [x3, #0xb]
    // 0xb17ad8: r3 = LoadInt32Instr(r1)
    //     0xb17ad8: sbfx            x3, x1, #1, #0x1f
    // 0xb17adc: stur            x3, [fp, #-0x38]
    // 0xb17ae0: r1 = LoadInt32Instr(r4)
    //     0xb17ae0: sbfx            x1, x4, #1, #0x1f
    // 0xb17ae4: cmp             x3, x1
    // 0xb17ae8: b.ne            #0xb17af4
    // 0xb17aec: mov             x1, x0
    // 0xb17af0: r0 = _growToNextCapacity()
    //     0xb17af0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb17af4: ldur            x2, [fp, #-0x30]
    // 0xb17af8: ldur            x3, [fp, #-0x38]
    // 0xb17afc: add             x0, x3, #1
    // 0xb17b00: lsl             x1, x0, #1
    // 0xb17b04: StoreField: r2->field_b = r1
    //     0xb17b04: stur            w1, [x2, #0xb]
    // 0xb17b08: LoadField: r1 = r2->field_f
    //     0xb17b08: ldur            w1, [x2, #0xf]
    // 0xb17b0c: DecompressPointer r1
    //     0xb17b0c: add             x1, x1, HEAP, lsl #32
    // 0xb17b10: ldur            x0, [fp, #-0x48]
    // 0xb17b14: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb17b14: add             x25, x1, x3, lsl #2
    //     0xb17b18: add             x25, x25, #0xf
    //     0xb17b1c: str             w0, [x25]
    //     0xb17b20: tbz             w0, #0, #0xb17b3c
    //     0xb17b24: ldurb           w16, [x1, #-1]
    //     0xb17b28: ldurb           w17, [x0, #-1]
    //     0xb17b2c: and             x16, x17, x16, lsr #2
    //     0xb17b30: tst             x16, HEAP, lsr #32
    //     0xb17b34: b.eq            #0xb17b3c
    //     0xb17b38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb17b3c: r0 = Row()
    //     0xb17b3c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb17b40: mov             x2, x0
    // 0xb17b44: r0 = Instance_Axis
    //     0xb17b44: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb17b48: stur            x2, [fp, #-8]
    // 0xb17b4c: StoreField: r2->field_f = r0
    //     0xb17b4c: stur            w0, [x2, #0xf]
    // 0xb17b50: r0 = Instance_MainAxisAlignment
    //     0xb17b50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb17b54: ldr             x0, [x0, #0xa08]
    // 0xb17b58: StoreField: r2->field_13 = r0
    //     0xb17b58: stur            w0, [x2, #0x13]
    // 0xb17b5c: r0 = Instance_MainAxisSize
    //     0xb17b5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb17b60: ldr             x0, [x0, #0xa10]
    // 0xb17b64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb17b64: stur            w0, [x2, #0x17]
    // 0xb17b68: r0 = Instance_CrossAxisAlignment
    //     0xb17b68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb17b6c: ldr             x0, [x0, #0xa18]
    // 0xb17b70: StoreField: r2->field_1b = r0
    //     0xb17b70: stur            w0, [x2, #0x1b]
    // 0xb17b74: r0 = Instance_VerticalDirection
    //     0xb17b74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb17b78: ldr             x0, [x0, #0xa20]
    // 0xb17b7c: StoreField: r2->field_23 = r0
    //     0xb17b7c: stur            w0, [x2, #0x23]
    // 0xb17b80: r0 = Instance_Clip
    //     0xb17b80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb17b84: ldr             x0, [x0, #0x38]
    // 0xb17b88: StoreField: r2->field_2b = r0
    //     0xb17b88: stur            w0, [x2, #0x2b]
    // 0xb17b8c: StoreField: r2->field_2f = rZR
    //     0xb17b8c: stur            xzr, [x2, #0x2f]
    // 0xb17b90: ldur            x0, [fp, #-0x30]
    // 0xb17b94: StoreField: r2->field_b = r0
    //     0xb17b94: stur            w0, [x2, #0xb]
    // 0xb17b98: r1 = <String>
    //     0xb17b98: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb17b9c: r0 = PopupMenuItem()
    //     0xb17b9c: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb17ba0: mov             x3, x0
    // 0xb17ba4: r0 = "flag"
    //     0xb17ba4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb17ba8: ldr             x0, [x0, #0xa68]
    // 0xb17bac: stur            x3, [fp, #-0x30]
    // 0xb17bb0: StoreField: r3->field_f = r0
    //     0xb17bb0: stur            w0, [x3, #0xf]
    // 0xb17bb4: ldur            x2, [fp, #-0x28]
    // 0xb17bb8: r1 = Function '<anonymous closure>':.
    //     0xb17bb8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abe8] AnonymousClosure: (0xb17eac), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb17664)
    //     0xb17bbc: ldr             x1, [x1, #0xbe8]
    // 0xb17bc0: r0 = AllocateClosure()
    //     0xb17bc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17bc4: mov             x1, x0
    // 0xb17bc8: ldur            x0, [fp, #-0x30]
    // 0xb17bcc: StoreField: r0->field_13 = r1
    //     0xb17bcc: stur            w1, [x0, #0x13]
    // 0xb17bd0: r1 = true
    //     0xb17bd0: add             x1, NULL, #0x20  ; true
    // 0xb17bd4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb17bd4: stur            w1, [x0, #0x17]
    // 0xb17bd8: d0 = 25.000000
    //     0xb17bd8: fmov            d0, #25.00000000
    // 0xb17bdc: StoreField: r0->field_1b = d0
    //     0xb17bdc: stur            d0, [x0, #0x1b]
    // 0xb17be0: ldur            x1, [fp, #-8]
    // 0xb17be4: StoreField: r0->field_33 = r1
    //     0xb17be4: stur            w1, [x0, #0x33]
    // 0xb17be8: r1 = Null
    //     0xb17be8: mov             x1, NULL
    // 0xb17bec: r2 = 2
    //     0xb17bec: movz            x2, #0x2
    // 0xb17bf0: r0 = AllocateArray()
    //     0xb17bf0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb17bf4: mov             x2, x0
    // 0xb17bf8: ldur            x0, [fp, #-0x30]
    // 0xb17bfc: stur            x2, [fp, #-8]
    // 0xb17c00: StoreField: r2->field_f = r0
    //     0xb17c00: stur            w0, [x2, #0xf]
    // 0xb17c04: r1 = <PopupMenuEntry<String>>
    //     0xb17c04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb17c08: ldr             x1, [x1, #0xa70]
    // 0xb17c0c: r0 = AllocateGrowableArray()
    //     0xb17c0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb17c10: mov             x1, x0
    // 0xb17c14: ldur            x0, [fp, #-8]
    // 0xb17c18: StoreField: r1->field_f = r0
    //     0xb17c18: stur            w0, [x1, #0xf]
    // 0xb17c1c: r0 = 2
    //     0xb17c1c: movz            x0, #0x2
    // 0xb17c20: StoreField: r1->field_b = r0
    //     0xb17c20: stur            w0, [x1, #0xb]
    // 0xb17c24: r16 = <String>
    //     0xb17c24: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb17c28: ldur            lr, [fp, #-0x10]
    // 0xb17c2c: stp             lr, x16, [SP, #0x20]
    // 0xb17c30: ldur            x16, [fp, #-0x18]
    // 0xb17c34: stp             x16, x1, [SP, #0x10]
    // 0xb17c38: r16 = Instance_Color
    //     0xb17c38: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb17c3c: ldur            lr, [fp, #-0x20]
    // 0xb17c40: stp             lr, x16, [SP]
    // 0xb17c44: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb17c44: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb17c48: ldr             x4, [x4, #0xa78]
    // 0xb17c4c: r0 = showMenu()
    //     0xb17c4c: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb17c50: ldur            x2, [fp, #-0x28]
    // 0xb17c54: r1 = Function '<anonymous closure>':.
    //     0xb17c54: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abf0] AnonymousClosure: (0xb17d14), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb17664)
    //     0xb17c58: ldr             x1, [x1, #0xbf0]
    // 0xb17c5c: stur            x0, [fp, #-8]
    // 0xb17c60: r0 = AllocateClosure()
    //     0xb17c60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17c64: r16 = <Null?>
    //     0xb17c64: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb17c68: ldur            lr, [fp, #-8]
    // 0xb17c6c: stp             lr, x16, [SP, #8]
    // 0xb17c70: str             x0, [SP]
    // 0xb17c74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb17c74: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb17c78: r0 = then()
    //     0xb17c78: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb17c7c: r0 = Null
    //     0xb17c7c: mov             x0, NULL
    // 0xb17c80: LeaveFrame
    //     0xb17c80: mov             SP, fp
    //     0xb17c84: ldp             fp, lr, [SP], #0x10
    // 0xb17c88: ret
    //     0xb17c88: ret             
    // 0xb17c8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17c8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17c90: b               #0xb17698
    // 0xb17c94: SaveReg d0
    //     0xb17c94: str             q0, [SP, #-0x10]!
    // 0xb17c98: stp             x0, x1, [SP, #-0x10]!
    // 0xb17c9c: r0 = AllocateDouble()
    //     0xb17c9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb17ca0: mov             x2, x0
    // 0xb17ca4: ldp             x0, x1, [SP], #0x10
    // 0xb17ca8: RestoreReg d0
    //     0xb17ca8: ldr             q0, [SP], #0x10
    // 0xb17cac: b               #0xb17774
    // 0xb17cb0: stp             q0, q1, [SP, #-0x20]!
    // 0xb17cb4: stp             x0, x1, [SP, #-0x10]!
    // 0xb17cb8: r0 = AllocateDouble()
    //     0xb17cb8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb17cbc: mov             x2, x0
    // 0xb17cc0: ldp             x0, x1, [SP], #0x10
    // 0xb17cc4: ldp             q0, q1, [SP], #0x20
    // 0xb17cc8: b               #0xb177c8
    // 0xb17ccc: stp             q2, q3, [SP, #-0x20]!
    // 0xb17cd0: stp             q0, q1, [SP, #-0x20]!
    // 0xb17cd4: stp             x0, x1, [SP, #-0x10]!
    // 0xb17cd8: r0 = AllocateDouble()
    //     0xb17cd8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb17cdc: mov             x2, x0
    // 0xb17ce0: ldp             x0, x1, [SP], #0x10
    // 0xb17ce4: ldp             q0, q1, [SP], #0x20
    // 0xb17ce8: ldp             q2, q3, [SP], #0x20
    // 0xb17cec: b               #0xb17828
    // 0xb17cf0: stp             q3, q4, [SP, #-0x20]!
    // 0xb17cf4: stp             q0, q1, [SP, #-0x20]!
    // 0xb17cf8: SaveReg r0
    //     0xb17cf8: str             x0, [SP, #-8]!
    // 0xb17cfc: r0 = AllocateDouble()
    //     0xb17cfc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb17d00: mov             x1, x0
    // 0xb17d04: RestoreReg r0
    //     0xb17d04: ldr             x0, [SP], #8
    // 0xb17d08: ldp             q0, q1, [SP], #0x20
    // 0xb17d0c: ldp             q3, q4, [SP], #0x20
    // 0xb17d10: b               #0xb17880
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb17d14, size: 0x94
    // 0xb17d14: EnterFrame
    //     0xb17d14: stp             fp, lr, [SP, #-0x10]!
    //     0xb17d18: mov             fp, SP
    // 0xb17d1c: AllocStack(0x20)
    //     0xb17d1c: sub             SP, SP, #0x20
    // 0xb17d20: SetupParameters()
    //     0xb17d20: ldr             x0, [fp, #0x18]
    //     0xb17d24: ldur            w2, [x0, #0x17]
    //     0xb17d28: add             x2, x2, HEAP, lsl #32
    //     0xb17d2c: stur            x2, [fp, #-8]
    // 0xb17d30: CheckStackOverflow
    //     0xb17d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17d34: cmp             SP, x16
    //     0xb17d38: b.ls            #0xb17da0
    // 0xb17d3c: ldr             x0, [fp, #0x10]
    // 0xb17d40: r1 = LoadClassIdInstr(r0)
    //     0xb17d40: ldur            x1, [x0, #-1]
    //     0xb17d44: ubfx            x1, x1, #0xc, #0x14
    // 0xb17d48: r16 = "flag"
    //     0xb17d48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb17d4c: ldr             x16, [x16, #0xa68]
    // 0xb17d50: stp             x16, x0, [SP]
    // 0xb17d54: mov             x0, x1
    // 0xb17d58: mov             lr, x0
    // 0xb17d5c: ldr             lr, [x21, lr, lsl #3]
    // 0xb17d60: blr             lr
    // 0xb17d64: tbnz            w0, #4, #0xb17d90
    // 0xb17d68: ldur            x2, [fp, #-8]
    // 0xb17d6c: LoadField: r0 = r2->field_f
    //     0xb17d6c: ldur            w0, [x2, #0xf]
    // 0xb17d70: DecompressPointer r0
    //     0xb17d70: add             x0, x0, HEAP, lsl #32
    // 0xb17d74: stur            x0, [fp, #-0x10]
    // 0xb17d78: r1 = Function '<anonymous closure>':.
    //     0xb17d78: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6abf8] AnonymousClosure: (0xb17da8), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb17664)
    //     0xb17d7c: ldr             x1, [x1, #0xbf8]
    // 0xb17d80: r0 = AllocateClosure()
    //     0xb17d80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17d84: ldur            x1, [fp, #-0x10]
    // 0xb17d88: mov             x2, x0
    // 0xb17d8c: r0 = setState()
    //     0xb17d8c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb17d90: r0 = Null
    //     0xb17d90: mov             x0, NULL
    // 0xb17d94: LeaveFrame
    //     0xb17d94: mov             SP, fp
    //     0xb17d98: ldp             fp, lr, [SP], #0x10
    // 0xb17d9c: ret
    //     0xb17d9c: ret             
    // 0xb17da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17da0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17da4: b               #0xb17d3c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb17da8, size: 0x104
    // 0xb17da8: EnterFrame
    //     0xb17da8: stp             fp, lr, [SP, #-0x10]!
    //     0xb17dac: mov             fp, SP
    // 0xb17db0: AllocStack(0x20)
    //     0xb17db0: sub             SP, SP, #0x20
    // 0xb17db4: SetupParameters()
    //     0xb17db4: ldr             x0, [fp, #0x10]
    //     0xb17db8: ldur            w4, [x0, #0x17]
    //     0xb17dbc: add             x4, x4, HEAP, lsl #32
    //     0xb17dc0: stur            x4, [fp, #-8]
    // 0xb17dc4: CheckStackOverflow
    //     0xb17dc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17dc8: cmp             SP, x16
    //     0xb17dcc: b.ls            #0xb17e9c
    // 0xb17dd0: LoadField: r0 = r4->field_f
    //     0xb17dd0: ldur            w0, [x4, #0xf]
    // 0xb17dd4: DecompressPointer r0
    //     0xb17dd4: add             x0, x0, HEAP, lsl #32
    // 0xb17dd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb17dd8: ldur            w1, [x0, #0x17]
    // 0xb17ddc: DecompressPointer r1
    //     0xb17ddc: add             x1, x1, HEAP, lsl #32
    // 0xb17de0: LoadField: r2 = r4->field_13
    //     0xb17de0: ldur            w2, [x4, #0x13]
    // 0xb17de4: DecompressPointer r2
    //     0xb17de4: add             x2, x2, HEAP, lsl #32
    // 0xb17de8: r0 = LoadClassIdInstr(r1)
    //     0xb17de8: ldur            x0, [x1, #-1]
    //     0xb17dec: ubfx            x0, x0, #0xc, #0x14
    // 0xb17df0: r3 = true
    //     0xb17df0: add             x3, NULL, #0x20  ; true
    // 0xb17df4: r0 = GDT[cid_x0 + 0x35a]()
    //     0xb17df4: add             lr, x0, #0x35a
    //     0xb17df8: ldr             lr, [x21, lr, lsl #3]
    //     0xb17dfc: blr             lr
    // 0xb17e00: ldur            x1, [fp, #-8]
    // 0xb17e04: LoadField: r0 = r1->field_f
    //     0xb17e04: ldur            w0, [x1, #0xf]
    // 0xb17e08: DecompressPointer r0
    //     0xb17e08: add             x0, x0, HEAP, lsl #32
    // 0xb17e0c: LoadField: r2 = r0->field_b
    //     0xb17e0c: ldur            w2, [x0, #0xb]
    // 0xb17e10: DecompressPointer r2
    //     0xb17e10: add             x2, x2, HEAP, lsl #32
    // 0xb17e14: cmp             w2, NULL
    // 0xb17e18: b.eq            #0xb17ea4
    // 0xb17e1c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb17e1c: ldur            w3, [x0, #0x17]
    // 0xb17e20: DecompressPointer r3
    //     0xb17e20: add             x3, x3, HEAP, lsl #32
    // 0xb17e24: LoadField: r0 = r2->field_f
    //     0xb17e24: ldur            w0, [x2, #0xf]
    // 0xb17e28: DecompressPointer r0
    //     0xb17e28: add             x0, x0, HEAP, lsl #32
    // 0xb17e2c: stp             x3, x0, [SP]
    // 0xb17e30: ClosureCall
    //     0xb17e30: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb17e34: ldur            x2, [x0, #0x1f]
    //     0xb17e38: blr             x2
    // 0xb17e3c: ldur            x0, [fp, #-8]
    // 0xb17e40: LoadField: r1 = r0->field_f
    //     0xb17e40: ldur            w1, [x0, #0xf]
    // 0xb17e44: DecompressPointer r1
    //     0xb17e44: add             x1, x1, HEAP, lsl #32
    // 0xb17e48: LoadField: r0 = r1->field_b
    //     0xb17e48: ldur            w0, [x1, #0xb]
    // 0xb17e4c: DecompressPointer r0
    //     0xb17e4c: add             x0, x0, HEAP, lsl #32
    // 0xb17e50: cmp             w0, NULL
    // 0xb17e54: b.eq            #0xb17ea8
    // 0xb17e58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb17e58: ldur            w1, [x0, #0x17]
    // 0xb17e5c: DecompressPointer r1
    //     0xb17e5c: add             x1, x1, HEAP, lsl #32
    // 0xb17e60: r16 = "flag_abusive"
    //     0xb17e60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xb17e64: ldr             x16, [x16, #0xa88]
    // 0xb17e68: stp             x16, x1, [SP, #8]
    // 0xb17e6c: r16 = ""
    //     0xb17e6c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb17e70: str             x16, [SP]
    // 0xb17e74: r4 = 0
    //     0xb17e74: movz            x4, #0
    // 0xb17e78: ldr             x0, [SP, #0x10]
    // 0xb17e7c: r16 = UnlinkedCall_0x613b5c
    //     0xb17e7c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ac00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb17e80: add             x16, x16, #0xc00
    // 0xb17e84: ldp             x5, lr, [x16]
    // 0xb17e88: blr             lr
    // 0xb17e8c: r0 = Null
    //     0xb17e8c: mov             x0, NULL
    // 0xb17e90: LeaveFrame
    //     0xb17e90: mov             SP, fp
    //     0xb17e94: ldp             fp, lr, [SP], #0x10
    // 0xb17e98: ret
    //     0xb17e98: ret             
    // 0xb17e9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17e9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17ea0: b               #0xb17dd0
    // 0xb17ea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb17ea4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb17ea8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb17ea8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb17eac, size: 0x60
    // 0xb17eac: EnterFrame
    //     0xb17eac: stp             fp, lr, [SP, #-0x10]!
    //     0xb17eb0: mov             fp, SP
    // 0xb17eb4: AllocStack(0x8)
    //     0xb17eb4: sub             SP, SP, #8
    // 0xb17eb8: SetupParameters()
    //     0xb17eb8: ldr             x0, [fp, #0x10]
    //     0xb17ebc: ldur            w2, [x0, #0x17]
    //     0xb17ec0: add             x2, x2, HEAP, lsl #32
    // 0xb17ec4: CheckStackOverflow
    //     0xb17ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17ec8: cmp             SP, x16
    //     0xb17ecc: b.ls            #0xb17f04
    // 0xb17ed0: LoadField: r0 = r2->field_f
    //     0xb17ed0: ldur            w0, [x2, #0xf]
    // 0xb17ed4: DecompressPointer r0
    //     0xb17ed4: add             x0, x0, HEAP, lsl #32
    // 0xb17ed8: stur            x0, [fp, #-8]
    // 0xb17edc: r1 = Function '<anonymous closure>':.
    //     0xb17edc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac10] AnonymousClosure: (0xb17f0c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::showMenuItem (0xb17664)
    //     0xb17ee0: ldr             x1, [x1, #0xc10]
    // 0xb17ee4: r0 = AllocateClosure()
    //     0xb17ee4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb17ee8: ldur            x1, [fp, #-8]
    // 0xb17eec: mov             x2, x0
    // 0xb17ef0: r0 = setState()
    //     0xb17ef0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb17ef4: r0 = Null
    //     0xb17ef4: mov             x0, NULL
    // 0xb17ef8: LeaveFrame
    //     0xb17ef8: mov             SP, fp
    //     0xb17efc: ldp             fp, lr, [SP], #0x10
    // 0xb17f00: ret
    //     0xb17f00: ret             
    // 0xb17f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb17f04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb17f08: b               #0xb17ed0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb17f0c, size: 0x104
    // 0xb17f0c: EnterFrame
    //     0xb17f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb17f10: mov             fp, SP
    // 0xb17f14: AllocStack(0x20)
    //     0xb17f14: sub             SP, SP, #0x20
    // 0xb17f18: SetupParameters()
    //     0xb17f18: ldr             x0, [fp, #0x10]
    //     0xb17f1c: ldur            w4, [x0, #0x17]
    //     0xb17f20: add             x4, x4, HEAP, lsl #32
    //     0xb17f24: stur            x4, [fp, #-8]
    // 0xb17f28: CheckStackOverflow
    //     0xb17f28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb17f2c: cmp             SP, x16
    //     0xb17f30: b.ls            #0xb18000
    // 0xb17f34: LoadField: r0 = r4->field_f
    //     0xb17f34: ldur            w0, [x4, #0xf]
    // 0xb17f38: DecompressPointer r0
    //     0xb17f38: add             x0, x0, HEAP, lsl #32
    // 0xb17f3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb17f3c: ldur            w1, [x0, #0x17]
    // 0xb17f40: DecompressPointer r1
    //     0xb17f40: add             x1, x1, HEAP, lsl #32
    // 0xb17f44: LoadField: r2 = r4->field_13
    //     0xb17f44: ldur            w2, [x4, #0x13]
    // 0xb17f48: DecompressPointer r2
    //     0xb17f48: add             x2, x2, HEAP, lsl #32
    // 0xb17f4c: r0 = LoadClassIdInstr(r1)
    //     0xb17f4c: ldur            x0, [x1, #-1]
    //     0xb17f50: ubfx            x0, x0, #0xc, #0x14
    // 0xb17f54: r3 = true
    //     0xb17f54: add             x3, NULL, #0x20  ; true
    // 0xb17f58: r0 = GDT[cid_x0 + 0x35a]()
    //     0xb17f58: add             lr, x0, #0x35a
    //     0xb17f5c: ldr             lr, [x21, lr, lsl #3]
    //     0xb17f60: blr             lr
    // 0xb17f64: ldur            x1, [fp, #-8]
    // 0xb17f68: LoadField: r0 = r1->field_f
    //     0xb17f68: ldur            w0, [x1, #0xf]
    // 0xb17f6c: DecompressPointer r0
    //     0xb17f6c: add             x0, x0, HEAP, lsl #32
    // 0xb17f70: LoadField: r2 = r0->field_b
    //     0xb17f70: ldur            w2, [x0, #0xb]
    // 0xb17f74: DecompressPointer r2
    //     0xb17f74: add             x2, x2, HEAP, lsl #32
    // 0xb17f78: cmp             w2, NULL
    // 0xb17f7c: b.eq            #0xb18008
    // 0xb17f80: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb17f80: ldur            w3, [x0, #0x17]
    // 0xb17f84: DecompressPointer r3
    //     0xb17f84: add             x3, x3, HEAP, lsl #32
    // 0xb17f88: LoadField: r0 = r2->field_f
    //     0xb17f88: ldur            w0, [x2, #0xf]
    // 0xb17f8c: DecompressPointer r0
    //     0xb17f8c: add             x0, x0, HEAP, lsl #32
    // 0xb17f90: stp             x3, x0, [SP]
    // 0xb17f94: ClosureCall
    //     0xb17f94: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb17f98: ldur            x2, [x0, #0x1f]
    //     0xb17f9c: blr             x2
    // 0xb17fa0: ldur            x0, [fp, #-8]
    // 0xb17fa4: LoadField: r1 = r0->field_f
    //     0xb17fa4: ldur            w1, [x0, #0xf]
    // 0xb17fa8: DecompressPointer r1
    //     0xb17fa8: add             x1, x1, HEAP, lsl #32
    // 0xb17fac: LoadField: r0 = r1->field_b
    //     0xb17fac: ldur            w0, [x1, #0xb]
    // 0xb17fb0: DecompressPointer r0
    //     0xb17fb0: add             x0, x0, HEAP, lsl #32
    // 0xb17fb4: cmp             w0, NULL
    // 0xb17fb8: b.eq            #0xb1800c
    // 0xb17fbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb17fbc: ldur            w1, [x0, #0x17]
    // 0xb17fc0: DecompressPointer r1
    //     0xb17fc0: add             x1, x1, HEAP, lsl #32
    // 0xb17fc4: r16 = "flag_abusive"
    //     0xb17fc4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa88] "flag_abusive"
    //     0xb17fc8: ldr             x16, [x16, #0xa88]
    // 0xb17fcc: stp             x16, x1, [SP, #8]
    // 0xb17fd0: r16 = ""
    //     0xb17fd0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb17fd4: str             x16, [SP]
    // 0xb17fd8: r4 = 0
    //     0xb17fd8: movz            x4, #0
    // 0xb17fdc: ldr             x0, [SP, #0x10]
    // 0xb17fe0: r16 = UnlinkedCall_0x613b5c
    //     0xb17fe0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ac18] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb17fe4: add             x16, x16, #0xc18
    // 0xb17fe8: ldp             x5, lr, [x16]
    // 0xb17fec: blr             lr
    // 0xb17ff0: r0 = Null
    //     0xb17ff0: mov             x0, NULL
    // 0xb17ff4: LeaveFrame
    //     0xb17ff4: mov             SP, fp
    //     0xb17ff8: ldp             fp, lr, [SP], #0x10
    // 0xb17ffc: ret
    //     0xb17ffc: ret             
    // 0xb18000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb18000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb18004: b               #0xb17f34
    // 0xb18008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb18008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1800c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb1800c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb18010, size: 0x2c0
    // 0xb18010: EnterFrame
    //     0xb18010: stp             fp, lr, [SP, #-0x10]!
    //     0xb18014: mov             fp, SP
    // 0xb18018: AllocStack(0x60)
    //     0xb18018: sub             SP, SP, #0x60
    // 0xb1801c: SetupParameters()
    //     0xb1801c: ldr             x0, [fp, #0x20]
    //     0xb18020: ldur            w1, [x0, #0x17]
    //     0xb18024: add             x1, x1, HEAP, lsl #32
    //     0xb18028: stur            x1, [fp, #-8]
    // 0xb1802c: CheckStackOverflow
    //     0xb1802c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb18030: cmp             SP, x16
    //     0xb18034: b.ls            #0xb182c0
    // 0xb18038: r1 = 3
    //     0xb18038: movz            x1, #0x3
    // 0xb1803c: r0 = AllocateContext()
    //     0xb1803c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb18040: mov             x2, x0
    // 0xb18044: ldur            x0, [fp, #-8]
    // 0xb18048: stur            x2, [fp, #-0x10]
    // 0xb1804c: StoreField: r2->field_b = r0
    //     0xb1804c: stur            w0, [x2, #0xb]
    // 0xb18050: ldr             x1, [fp, #0x18]
    // 0xb18054: StoreField: r2->field_f = r1
    //     0xb18054: stur            w1, [x2, #0xf]
    // 0xb18058: ldr             x3, [fp, #0x10]
    // 0xb1805c: StoreField: r2->field_13 = r3
    //     0xb1805c: stur            w3, [x2, #0x13]
    // 0xb18060: LoadField: r4 = r0->field_13
    //     0xb18060: ldur            w4, [x0, #0x13]
    // 0xb18064: DecompressPointer r4
    //     0xb18064: add             x4, x4, HEAP, lsl #32
    // 0xb18068: cmp             w4, NULL
    // 0xb1806c: b.ne            #0xb18078
    // 0xb18070: r5 = Null
    //     0xb18070: mov             x5, NULL
    // 0xb18074: b               #0xb180c4
    // 0xb18078: LoadField: r5 = r4->field_1b
    //     0xb18078: ldur            w5, [x4, #0x1b]
    // 0xb1807c: DecompressPointer r5
    //     0xb1807c: add             x5, x5, HEAP, lsl #32
    // 0xb18080: LoadField: r0 = r5->field_b
    //     0xb18080: ldur            w0, [x5, #0xb]
    // 0xb18084: r6 = LoadInt32Instr(r3)
    //     0xb18084: sbfx            x6, x3, #1, #0x1f
    //     0xb18088: tbz             w3, #0, #0xb18090
    //     0xb1808c: ldur            x6, [x3, #7]
    // 0xb18090: r1 = LoadInt32Instr(r0)
    //     0xb18090: sbfx            x1, x0, #1, #0x1f
    // 0xb18094: mov             x0, x1
    // 0xb18098: mov             x1, x6
    // 0xb1809c: cmp             x1, x0
    // 0xb180a0: b.hs            #0xb182c8
    // 0xb180a4: LoadField: r0 = r5->field_f
    //     0xb180a4: ldur            w0, [x5, #0xf]
    // 0xb180a8: DecompressPointer r0
    //     0xb180a8: add             x0, x0, HEAP, lsl #32
    // 0xb180ac: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb180ac: add             x16, x0, x6, lsl #2
    //     0xb180b0: ldur            w1, [x16, #0xf]
    // 0xb180b4: DecompressPointer r1
    //     0xb180b4: add             x1, x1, HEAP, lsl #32
    // 0xb180b8: LoadField: r0 = r1->field_13
    //     0xb180b8: ldur            w0, [x1, #0x13]
    // 0xb180bc: DecompressPointer r0
    //     0xb180bc: add             x0, x0, HEAP, lsl #32
    // 0xb180c0: mov             x5, x0
    // 0xb180c4: stur            x5, [fp, #-8]
    // 0xb180c8: ArrayStore: r2[0] = r5  ; List_4
    //     0xb180c8: stur            w5, [x2, #0x17]
    // 0xb180cc: cmp             w4, NULL
    // 0xb180d0: b.ne            #0xb180dc
    // 0xb180d4: r0 = Null
    //     0xb180d4: mov             x0, NULL
    // 0xb180d8: b               #0xb18124
    // 0xb180dc: LoadField: r6 = r4->field_1b
    //     0xb180dc: ldur            w6, [x4, #0x1b]
    // 0xb180e0: DecompressPointer r6
    //     0xb180e0: add             x6, x6, HEAP, lsl #32
    // 0xb180e4: LoadField: r0 = r6->field_b
    //     0xb180e4: ldur            w0, [x6, #0xb]
    // 0xb180e8: r4 = LoadInt32Instr(r3)
    //     0xb180e8: sbfx            x4, x3, #1, #0x1f
    //     0xb180ec: tbz             w3, #0, #0xb180f4
    //     0xb180f0: ldur            x4, [x3, #7]
    // 0xb180f4: r1 = LoadInt32Instr(r0)
    //     0xb180f4: sbfx            x1, x0, #1, #0x1f
    // 0xb180f8: mov             x0, x1
    // 0xb180fc: mov             x1, x4
    // 0xb18100: cmp             x1, x0
    // 0xb18104: b.hs            #0xb182cc
    // 0xb18108: LoadField: r0 = r6->field_f
    //     0xb18108: ldur            w0, [x6, #0xf]
    // 0xb1810c: DecompressPointer r0
    //     0xb1810c: add             x0, x0, HEAP, lsl #32
    // 0xb18110: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb18110: add             x16, x0, x4, lsl #2
    //     0xb18114: ldur            w1, [x16, #0xf]
    // 0xb18118: DecompressPointer r1
    //     0xb18118: add             x1, x1, HEAP, lsl #32
    // 0xb1811c: LoadField: r0 = r1->field_f
    //     0xb1811c: ldur            w0, [x1, #0xf]
    // 0xb18120: DecompressPointer r0
    //     0xb18120: add             x0, x0, HEAP, lsl #32
    // 0xb18124: r1 = LoadClassIdInstr(r0)
    //     0xb18124: ldur            x1, [x0, #-1]
    //     0xb18128: ubfx            x1, x1, #0xc, #0x14
    // 0xb1812c: r16 = "image"
    //     0xb1812c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb18130: stp             x16, x0, [SP]
    // 0xb18134: mov             x0, x1
    // 0xb18138: mov             lr, x0
    // 0xb1813c: ldr             lr, [x21, lr, lsl #3]
    // 0xb18140: blr             lr
    // 0xb18144: tbnz            w0, #4, #0xb18230
    // 0xb18148: ldur            x0, [fp, #-8]
    // 0xb1814c: r0 = Radius()
    //     0xb1814c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb18150: d0 = 12.000000
    //     0xb18150: fmov            d0, #12.00000000
    // 0xb18154: stur            x0, [fp, #-0x18]
    // 0xb18158: StoreField: r0->field_7 = d0
    //     0xb18158: stur            d0, [x0, #7]
    // 0xb1815c: StoreField: r0->field_f = d0
    //     0xb1815c: stur            d0, [x0, #0xf]
    // 0xb18160: r0 = BorderRadius()
    //     0xb18160: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb18164: mov             x3, x0
    // 0xb18168: ldur            x0, [fp, #-0x18]
    // 0xb1816c: stur            x3, [fp, #-0x20]
    // 0xb18170: StoreField: r3->field_7 = r0
    //     0xb18170: stur            w0, [x3, #7]
    // 0xb18174: StoreField: r3->field_b = r0
    //     0xb18174: stur            w0, [x3, #0xb]
    // 0xb18178: StoreField: r3->field_f = r0
    //     0xb18178: stur            w0, [x3, #0xf]
    // 0xb1817c: StoreField: r3->field_13 = r0
    //     0xb1817c: stur            w0, [x3, #0x13]
    // 0xb18180: ldur            x0, [fp, #-8]
    // 0xb18184: cmp             w0, NULL
    // 0xb18188: b.ne            #0xb18190
    // 0xb1818c: r0 = ""
    //     0xb1818c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb18190: stur            x0, [fp, #-0x18]
    // 0xb18194: r1 = Function '<anonymous closure>':.
    //     0xb18194: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac28] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb18198: ldr             x1, [x1, #0xc28]
    // 0xb1819c: r2 = Null
    //     0xb1819c: mov             x2, NULL
    // 0xb181a0: r0 = AllocateClosure()
    //     0xb181a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb181a4: r1 = Function '<anonymous closure>':.
    //     0xb181a4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac30] AnonymousClosure: (0x9b17ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb181a8: ldr             x1, [x1, #0xc30]
    // 0xb181ac: r2 = Null
    //     0xb181ac: mov             x2, NULL
    // 0xb181b0: stur            x0, [fp, #-0x28]
    // 0xb181b4: r0 = AllocateClosure()
    //     0xb181b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb181b8: stur            x0, [fp, #-0x30]
    // 0xb181bc: r0 = CachedNetworkImage()
    //     0xb181bc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb181c0: stur            x0, [fp, #-0x38]
    // 0xb181c4: r16 = Instance_BoxFit
    //     0xb181c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb181c8: ldr             x16, [x16, #0x118]
    // 0xb181cc: r30 = 48.000000
    //     0xb181cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb181d0: ldr             lr, [lr, #0xad8]
    // 0xb181d4: stp             lr, x16, [SP, #0x18]
    // 0xb181d8: r16 = 48.000000
    //     0xb181d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb181dc: ldr             x16, [x16, #0xad8]
    // 0xb181e0: ldur            lr, [fp, #-0x28]
    // 0xb181e4: stp             lr, x16, [SP, #8]
    // 0xb181e8: ldur            x16, [fp, #-0x30]
    // 0xb181ec: str             x16, [SP]
    // 0xb181f0: mov             x1, x0
    // 0xb181f4: ldur            x2, [fp, #-0x18]
    // 0xb181f8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xb181f8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fae0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xb181fc: ldr             x4, [x4, #0xae0]
    // 0xb18200: r0 = CachedNetworkImage()
    //     0xb18200: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb18204: r0 = ClipRRect()
    //     0xb18204: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb18208: mov             x1, x0
    // 0xb1820c: ldur            x0, [fp, #-0x20]
    // 0xb18210: StoreField: r1->field_f = r0
    //     0xb18210: stur            w0, [x1, #0xf]
    // 0xb18214: r0 = Instance_Clip
    //     0xb18214: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb18218: ldr             x0, [x0, #0x138]
    // 0xb1821c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb1821c: stur            w0, [x1, #0x17]
    // 0xb18220: ldur            x0, [fp, #-0x38]
    // 0xb18224: StoreField: r1->field_b = r0
    //     0xb18224: stur            w0, [x1, #0xb]
    // 0xb18228: mov             x0, x1
    // 0xb1822c: b               #0xb1827c
    // 0xb18230: ldur            x0, [fp, #-8]
    // 0xb18234: cmp             w0, NULL
    // 0xb18238: b.ne            #0xb18240
    // 0xb1823c: r0 = ""
    //     0xb1823c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb18240: stur            x0, [fp, #-8]
    // 0xb18244: r0 = VideoPlayerWidget()
    //     0xb18244: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb18248: mov             x1, x0
    // 0xb1824c: ldur            x0, [fp, #-8]
    // 0xb18250: stur            x1, [fp, #-0x18]
    // 0xb18254: StoreField: r1->field_b = r0
    //     0xb18254: stur            w0, [x1, #0xb]
    // 0xb18258: r0 = SizedBox()
    //     0xb18258: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb1825c: mov             x1, x0
    // 0xb18260: r0 = 48.000000
    //     0xb18260: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb18264: ldr             x0, [x0, #0xad8]
    // 0xb18268: StoreField: r1->field_f = r0
    //     0xb18268: stur            w0, [x1, #0xf]
    // 0xb1826c: StoreField: r1->field_13 = r0
    //     0xb1826c: stur            w0, [x1, #0x13]
    // 0xb18270: ldur            x0, [fp, #-0x18]
    // 0xb18274: StoreField: r1->field_b = r0
    //     0xb18274: stur            w0, [x1, #0xb]
    // 0xb18278: mov             x0, x1
    // 0xb1827c: stur            x0, [fp, #-8]
    // 0xb18280: r0 = GestureDetector()
    //     0xb18280: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb18284: ldur            x2, [fp, #-0x10]
    // 0xb18288: r1 = Function '<anonymous closure>':.
    //     0xb18288: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac38] AnonymousClosure: (0xb182d0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb1658c)
    //     0xb1828c: ldr             x1, [x1, #0xc38]
    // 0xb18290: stur            x0, [fp, #-0x10]
    // 0xb18294: r0 = AllocateClosure()
    //     0xb18294: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb18298: ldur            x16, [fp, #-8]
    // 0xb1829c: stp             x16, x0, [SP]
    // 0xb182a0: ldur            x1, [fp, #-0x10]
    // 0xb182a4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb182a4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb182a8: ldr             x4, [x4, #0xaf0]
    // 0xb182ac: r0 = GestureDetector()
    //     0xb182ac: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb182b0: ldur            x0, [fp, #-0x10]
    // 0xb182b4: LeaveFrame
    //     0xb182b4: mov             SP, fp
    //     0xb182b8: ldp             fp, lr, [SP], #0x10
    // 0xb182bc: ret
    //     0xb182bc: ret             
    // 0xb182c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb182c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb182c4: b               #0xb18038
    // 0xb182c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb182c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb182cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb182cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb182d0, size: 0x104
    // 0xb182d0: EnterFrame
    //     0xb182d0: stp             fp, lr, [SP, #-0x10]!
    //     0xb182d4: mov             fp, SP
    // 0xb182d8: AllocStack(0x28)
    //     0xb182d8: sub             SP, SP, #0x28
    // 0xb182dc: SetupParameters()
    //     0xb182dc: ldr             x0, [fp, #0x10]
    //     0xb182e0: ldur            w2, [x0, #0x17]
    //     0xb182e4: add             x2, x2, HEAP, lsl #32
    //     0xb182e8: stur            x2, [fp, #-8]
    // 0xb182ec: CheckStackOverflow
    //     0xb182ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb182f0: cmp             SP, x16
    //     0xb182f4: b.ls            #0xb183c8
    // 0xb182f8: LoadField: r0 = r2->field_b
    //     0xb182f8: ldur            w0, [x2, #0xb]
    // 0xb182fc: DecompressPointer r0
    //     0xb182fc: add             x0, x0, HEAP, lsl #32
    // 0xb18300: LoadField: r1 = r0->field_b
    //     0xb18300: ldur            w1, [x0, #0xb]
    // 0xb18304: DecompressPointer r1
    //     0xb18304: add             x1, x1, HEAP, lsl #32
    // 0xb18308: LoadField: r0 = r1->field_f
    //     0xb18308: ldur            w0, [x1, #0xf]
    // 0xb1830c: DecompressPointer r0
    //     0xb1830c: add             x0, x0, HEAP, lsl #32
    // 0xb18310: LoadField: r1 = r0->field_b
    //     0xb18310: ldur            w1, [x0, #0xb]
    // 0xb18314: DecompressPointer r1
    //     0xb18314: add             x1, x1, HEAP, lsl #32
    // 0xb18318: cmp             w1, NULL
    // 0xb1831c: b.eq            #0xb183d0
    // 0xb18320: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb18320: ldur            w0, [x2, #0x17]
    // 0xb18324: DecompressPointer r0
    //     0xb18324: add             x0, x0, HEAP, lsl #32
    // 0xb18328: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb18328: ldur            w3, [x1, #0x17]
    // 0xb1832c: DecompressPointer r3
    //     0xb1832c: add             x3, x3, HEAP, lsl #32
    // 0xb18330: r16 = "single_media"
    //     0xb18330: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xb18334: ldr             x16, [x16, #0xab0]
    // 0xb18338: stp             x16, x3, [SP, #8]
    // 0xb1833c: str             x0, [SP]
    // 0xb18340: r4 = 0
    //     0xb18340: movz            x4, #0
    // 0xb18344: ldr             x0, [SP, #0x10]
    // 0xb18348: r16 = UnlinkedCall_0x613b5c
    //     0xb18348: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ac40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb1834c: add             x16, x16, #0xc40
    // 0xb18350: ldp             x5, lr, [x16]
    // 0xb18354: blr             lr
    // 0xb18358: ldur            x2, [fp, #-8]
    // 0xb1835c: LoadField: r1 = r2->field_f
    //     0xb1835c: ldur            w1, [x2, #0xf]
    // 0xb18360: DecompressPointer r1
    //     0xb18360: add             x1, x1, HEAP, lsl #32
    // 0xb18364: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb18364: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb18368: r0 = of()
    //     0xb18368: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xb1836c: ldur            x2, [fp, #-8]
    // 0xb18370: r1 = Function '<anonymous closure>':.
    //     0xb18370: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac50] AnonymousClosure: (0xb183d4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xb1658c)
    //     0xb18374: ldr             x1, [x1, #0xc50]
    // 0xb18378: stur            x0, [fp, #-8]
    // 0xb1837c: r0 = AllocateClosure()
    //     0xb1837c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb18380: r1 = Null
    //     0xb18380: mov             x1, NULL
    // 0xb18384: stur            x0, [fp, #-0x10]
    // 0xb18388: r0 = MaterialPageRoute()
    //     0xb18388: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xb1838c: mov             x1, x0
    // 0xb18390: ldur            x2, [fp, #-0x10]
    // 0xb18394: stur            x0, [fp, #-0x10]
    // 0xb18398: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb18398: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb1839c: r0 = MaterialPageRoute()
    //     0xb1839c: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xb183a0: ldur            x16, [fp, #-8]
    // 0xb183a4: stp             x16, NULL, [SP, #8]
    // 0xb183a8: ldur            x16, [fp, #-0x10]
    // 0xb183ac: str             x16, [SP]
    // 0xb183b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb183b0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb183b4: r0 = push()
    //     0xb183b4: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xb183b8: r0 = Null
    //     0xb183b8: mov             x0, NULL
    // 0xb183bc: LeaveFrame
    //     0xb183bc: mov             SP, fp
    //     0xb183c0: ldp             fp, lr, [SP], #0x10
    // 0xb183c4: ret
    //     0xb183c4: ret             
    // 0xb183c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb183c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb183cc: b               #0xb182f8
    // 0xb183d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb183d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb183d4, size: 0xc4
    // 0xb183d4: EnterFrame
    //     0xb183d4: stp             fp, lr, [SP, #-0x10]!
    //     0xb183d8: mov             fp, SP
    // 0xb183dc: AllocStack(0x28)
    //     0xb183dc: sub             SP, SP, #0x28
    // 0xb183e0: SetupParameters()
    //     0xb183e0: ldr             x0, [fp, #0x18]
    //     0xb183e4: ldur            w2, [x0, #0x17]
    //     0xb183e8: add             x2, x2, HEAP, lsl #32
    //     0xb183ec: stur            x2, [fp, #-0x20]
    // 0xb183f0: LoadField: r0 = r2->field_13
    //     0xb183f0: ldur            w0, [x2, #0x13]
    // 0xb183f4: DecompressPointer r0
    //     0xb183f4: add             x0, x0, HEAP, lsl #32
    // 0xb183f8: stur            x0, [fp, #-0x18]
    // 0xb183fc: LoadField: r1 = r2->field_b
    //     0xb183fc: ldur            w1, [x2, #0xb]
    // 0xb18400: DecompressPointer r1
    //     0xb18400: add             x1, x1, HEAP, lsl #32
    // 0xb18404: LoadField: r3 = r1->field_b
    //     0xb18404: ldur            w3, [x1, #0xb]
    // 0xb18408: DecompressPointer r3
    //     0xb18408: add             x3, x3, HEAP, lsl #32
    // 0xb1840c: LoadField: r4 = r3->field_f
    //     0xb1840c: ldur            w4, [x3, #0xf]
    // 0xb18410: DecompressPointer r4
    //     0xb18410: add             x4, x4, HEAP, lsl #32
    // 0xb18414: LoadField: r3 = r4->field_b
    //     0xb18414: ldur            w3, [x4, #0xb]
    // 0xb18418: DecompressPointer r3
    //     0xb18418: add             x3, x3, HEAP, lsl #32
    // 0xb1841c: cmp             w3, NULL
    // 0xb18420: b.eq            #0xb18494
    // 0xb18424: LoadField: r4 = r3->field_13
    //     0xb18424: ldur            w4, [x3, #0x13]
    // 0xb18428: DecompressPointer r4
    //     0xb18428: add             x4, x4, HEAP, lsl #32
    // 0xb1842c: stur            x4, [fp, #-0x10]
    // 0xb18430: LoadField: r3 = r1->field_13
    //     0xb18430: ldur            w3, [x1, #0x13]
    // 0xb18434: DecompressPointer r3
    //     0xb18434: add             x3, x3, HEAP, lsl #32
    // 0xb18438: stur            x3, [fp, #-8]
    // 0xb1843c: r0 = RatingReviewOnTapImage()
    //     0xb1843c: bl              #0xb18498  ; AllocateRatingReviewOnTapImageStub -> RatingReviewOnTapImage (size=0x20)
    // 0xb18440: mov             x3, x0
    // 0xb18444: ldur            x0, [fp, #-8]
    // 0xb18448: stur            x3, [fp, #-0x28]
    // 0xb1844c: StoreField: r3->field_b = r0
    //     0xb1844c: stur            w0, [x3, #0xb]
    // 0xb18450: ldur            x0, [fp, #-0x18]
    // 0xb18454: r1 = LoadInt32Instr(r0)
    //     0xb18454: sbfx            x1, x0, #1, #0x1f
    //     0xb18458: tbz             w0, #0, #0xb18460
    //     0xb1845c: ldur            x1, [x0, #7]
    // 0xb18460: StoreField: r3->field_f = r1
    //     0xb18460: stur            x1, [x3, #0xf]
    // 0xb18464: ldur            x2, [fp, #-0x20]
    // 0xb18468: r1 = Function '<anonymous closure>':.
    //     0xb18468: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac58] AnonymousClosure: (0xa923cc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/review_widget.dart] _ReviewWidgetState::build (0xc0c210)
    //     0xb1846c: ldr             x1, [x1, #0xc58]
    // 0xb18470: r0 = AllocateClosure()
    //     0xb18470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb18474: mov             x1, x0
    // 0xb18478: ldur            x0, [fp, #-0x28]
    // 0xb1847c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb1847c: stur            w1, [x0, #0x17]
    // 0xb18480: ldur            x1, [fp, #-0x10]
    // 0xb18484: StoreField: r0->field_1b = r1
    //     0xb18484: stur            w1, [x0, #0x1b]
    // 0xb18488: LeaveFrame
    //     0xb18488: mov             SP, fp
    //     0xb1848c: ldp             fp, lr, [SP], #0x10
    // 0xb18490: ret
    //     0xb18490: ret             
    // 0xb18494: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb18494: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4133, size: 0x1c, field offset: 0xc
//   const constructor, 
class ReviewWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e1e8, size: 0x54
    // 0xc7e1e8: EnterFrame
    //     0xc7e1e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e1ec: mov             fp, SP
    // 0xc7e1f0: AllocStack(0x18)
    //     0xc7e1f0: sub             SP, SP, #0x18
    // 0xc7e1f4: CheckStackOverflow
    //     0xc7e1f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e1f8: cmp             SP, x16
    //     0xc7e1fc: b.ls            #0xc7e234
    // 0xc7e200: r16 = <String, dynamic>
    //     0xc7e200: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xc7e204: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7e208: stp             lr, x16, [SP]
    // 0xc7e20c: r0 = Map._fromLiteral()
    //     0xc7e20c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7e210: r1 = <ReviewWidget>
    //     0xc7e210: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e78] TypeArguments: <ReviewWidget>
    //     0xc7e214: ldr             x1, [x1, #0xe78]
    // 0xc7e218: stur            x0, [fp, #-8]
    // 0xc7e21c: r0 = _ReviewWidgetState()
    //     0xc7e21c: bl              #0xc7e23c  ; Allocate_ReviewWidgetStateStub -> _ReviewWidgetState (size=0x1c)
    // 0xc7e220: ldur            x1, [fp, #-8]
    // 0xc7e224: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7e224: stur            w1, [x0, #0x17]
    // 0xc7e228: LeaveFrame
    //     0xc7e228: mov             SP, fp
    //     0xc7e22c: ldp             fp, lr, [SP], #0x10
    // 0xc7e230: ret
    //     0xc7e230: ret             
    // 0xc7e234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e238: b               #0xc7e200
  }
}
