// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart

// class id: 1049557, size: 0x8
class :: {
}

// class id: 3227, size: 0x20, field offset: 0x14
class _ProductCollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa7bc98, size: 0x228
    // 0xa7bc98: EnterFrame
    //     0xa7bc98: stp             fp, lr, [SP, #-0x10]!
    //     0xa7bc9c: mov             fp, SP
    // 0xa7bca0: AllocStack(0x28)
    //     0xa7bca0: sub             SP, SP, #0x28
    // 0xa7bca4: SetupParameters()
    //     0xa7bca4: ldr             x0, [fp, #0x10]
    //     0xa7bca8: ldur            w2, [x0, #0x17]
    //     0xa7bcac: add             x2, x2, HEAP, lsl #32
    //     0xa7bcb0: stur            x2, [fp, #-8]
    // 0xa7bcb4: CheckStackOverflow
    //     0xa7bcb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7bcb8: cmp             SP, x16
    //     0xa7bcbc: b.ls            #0xa7bea8
    // 0xa7bcc0: LoadField: r3 = r2->field_13
    //     0xa7bcc0: ldur            w3, [x2, #0x13]
    // 0xa7bcc4: DecompressPointer r3
    //     0xa7bcc4: add             x3, x3, HEAP, lsl #32
    // 0xa7bcc8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa7bcc8: ldur            w0, [x2, #0x17]
    // 0xa7bccc: DecompressPointer r0
    //     0xa7bccc: add             x0, x0, HEAP, lsl #32
    // 0xa7bcd0: LoadField: r1 = r3->field_b
    //     0xa7bcd0: ldur            w1, [x3, #0xb]
    // 0xa7bcd4: r4 = LoadInt32Instr(r0)
    //     0xa7bcd4: sbfx            x4, x0, #1, #0x1f
    //     0xa7bcd8: tbz             w0, #0, #0xa7bce0
    //     0xa7bcdc: ldur            x4, [x0, #7]
    // 0xa7bce0: r0 = LoadInt32Instr(r1)
    //     0xa7bce0: sbfx            x0, x1, #1, #0x1f
    // 0xa7bce4: mov             x1, x4
    // 0xa7bce8: cmp             x1, x0
    // 0xa7bcec: b.hs            #0xa7beb0
    // 0xa7bcf0: LoadField: r0 = r3->field_f
    //     0xa7bcf0: ldur            w0, [x3, #0xf]
    // 0xa7bcf4: DecompressPointer r0
    //     0xa7bcf4: add             x0, x0, HEAP, lsl #32
    // 0xa7bcf8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa7bcf8: add             x16, x0, x4, lsl #2
    //     0xa7bcfc: ldur            w1, [x16, #0xf]
    // 0xa7bd00: DecompressPointer r1
    //     0xa7bd00: add             x1, x1, HEAP, lsl #32
    // 0xa7bd04: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa7bd04: ldur            w0, [x1, #0x17]
    // 0xa7bd08: DecompressPointer r0
    //     0xa7bd08: add             x0, x0, HEAP, lsl #32
    // 0xa7bd0c: cmp             w0, NULL
    // 0xa7bd10: b.eq            #0xa7beb4
    // 0xa7bd14: LoadField: r1 = r0->field_7
    //     0xa7bd14: ldur            w1, [x0, #7]
    // 0xa7bd18: cbz             w1, #0xa7be98
    // 0xa7bd1c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa7bd1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa7bd20: ldr             x0, [x0, #0x1c80]
    //     0xa7bd24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa7bd28: cmp             w0, w16
    //     0xa7bd2c: b.ne            #0xa7bd38
    //     0xa7bd30: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa7bd34: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa7bd38: r1 = Null
    //     0xa7bd38: mov             x1, NULL
    // 0xa7bd3c: r2 = 12
    //     0xa7bd3c: movz            x2, #0xc
    // 0xa7bd40: r0 = AllocateArray()
    //     0xa7bd40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa7bd44: mov             x2, x0
    // 0xa7bd48: stur            x2, [fp, #-0x10]
    // 0xa7bd4c: r16 = "link"
    //     0xa7bd4c: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0xa7bd50: StoreField: r2->field_f = r16
    //     0xa7bd50: stur            w16, [x2, #0xf]
    // 0xa7bd54: ldur            x3, [fp, #-8]
    // 0xa7bd58: LoadField: r4 = r3->field_13
    //     0xa7bd58: ldur            w4, [x3, #0x13]
    // 0xa7bd5c: DecompressPointer r4
    //     0xa7bd5c: add             x4, x4, HEAP, lsl #32
    // 0xa7bd60: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa7bd60: ldur            w0, [x3, #0x17]
    // 0xa7bd64: DecompressPointer r0
    //     0xa7bd64: add             x0, x0, HEAP, lsl #32
    // 0xa7bd68: LoadField: r1 = r4->field_b
    //     0xa7bd68: ldur            w1, [x4, #0xb]
    // 0xa7bd6c: r5 = LoadInt32Instr(r0)
    //     0xa7bd6c: sbfx            x5, x0, #1, #0x1f
    //     0xa7bd70: tbz             w0, #0, #0xa7bd78
    //     0xa7bd74: ldur            x5, [x0, #7]
    // 0xa7bd78: r0 = LoadInt32Instr(r1)
    //     0xa7bd78: sbfx            x0, x1, #1, #0x1f
    // 0xa7bd7c: mov             x1, x5
    // 0xa7bd80: cmp             x1, x0
    // 0xa7bd84: b.hs            #0xa7beb8
    // 0xa7bd88: LoadField: r0 = r4->field_f
    //     0xa7bd88: ldur            w0, [x4, #0xf]
    // 0xa7bd8c: DecompressPointer r0
    //     0xa7bd8c: add             x0, x0, HEAP, lsl #32
    // 0xa7bd90: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa7bd90: add             x16, x0, x5, lsl #2
    //     0xa7bd94: ldur            w1, [x16, #0xf]
    // 0xa7bd98: DecompressPointer r1
    //     0xa7bd98: add             x1, x1, HEAP, lsl #32
    // 0xa7bd9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa7bd9c: ldur            w0, [x1, #0x17]
    // 0xa7bda0: DecompressPointer r0
    //     0xa7bda0: add             x0, x0, HEAP, lsl #32
    // 0xa7bda4: str             x0, [SP]
    // 0xa7bda8: r0 = _interpolateSingle()
    //     0xa7bda8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa7bdac: ldur            x1, [fp, #-0x10]
    // 0xa7bdb0: ArrayStore: r1[1] = r0  ; List_4
    //     0xa7bdb0: add             x25, x1, #0x13
    //     0xa7bdb4: str             w0, [x25]
    //     0xa7bdb8: tbz             w0, #0, #0xa7bdd4
    //     0xa7bdbc: ldurb           w16, [x1, #-1]
    //     0xa7bdc0: ldurb           w17, [x0, #-1]
    //     0xa7bdc4: and             x16, x17, x16, lsr #2
    //     0xa7bdc8: tst             x16, HEAP, lsr #32
    //     0xa7bdcc: b.eq            #0xa7bdd4
    //     0xa7bdd0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa7bdd4: ldur            x2, [fp, #-0x10]
    // 0xa7bdd8: r16 = "previousScreenSource"
    //     0xa7bdd8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xa7bddc: ldr             x16, [x16, #0x448]
    // 0xa7bde0: ArrayStore: r2[0] = r16  ; List_4
    //     0xa7bde0: stur            w16, [x2, #0x17]
    // 0xa7bde4: ldur            x0, [fp, #-8]
    // 0xa7bde8: LoadField: r1 = r0->field_f
    //     0xa7bde8: ldur            w1, [x0, #0xf]
    // 0xa7bdec: DecompressPointer r1
    //     0xa7bdec: add             x1, x1, HEAP, lsl #32
    // 0xa7bdf0: LoadField: r3 = r1->field_b
    //     0xa7bdf0: ldur            w3, [x1, #0xb]
    // 0xa7bdf4: DecompressPointer r3
    //     0xa7bdf4: add             x3, x3, HEAP, lsl #32
    // 0xa7bdf8: cmp             w3, NULL
    // 0xa7bdfc: b.eq            #0xa7bebc
    // 0xa7be00: LoadField: r0 = r3->field_1b
    //     0xa7be00: ldur            w0, [x3, #0x1b]
    // 0xa7be04: DecompressPointer r0
    //     0xa7be04: add             x0, x0, HEAP, lsl #32
    // 0xa7be08: mov             x1, x2
    // 0xa7be0c: ArrayStore: r1[3] = r0  ; List_4
    //     0xa7be0c: add             x25, x1, #0x1b
    //     0xa7be10: str             w0, [x25]
    //     0xa7be14: tbz             w0, #0, #0xa7be30
    //     0xa7be18: ldurb           w16, [x1, #-1]
    //     0xa7be1c: ldurb           w17, [x0, #-1]
    //     0xa7be20: and             x16, x17, x16, lsr #2
    //     0xa7be24: tst             x16, HEAP, lsr #32
    //     0xa7be28: b.eq            #0xa7be30
    //     0xa7be2c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa7be30: r16 = "screenSource"
    //     0xa7be30: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xa7be34: ldr             x16, [x16, #0x450]
    // 0xa7be38: StoreField: r2->field_1f = r16
    //     0xa7be38: stur            w16, [x2, #0x1f]
    // 0xa7be3c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa7be3c: ldur            w0, [x3, #0x17]
    // 0xa7be40: DecompressPointer r0
    //     0xa7be40: add             x0, x0, HEAP, lsl #32
    // 0xa7be44: mov             x1, x2
    // 0xa7be48: ArrayStore: r1[5] = r0  ; List_4
    //     0xa7be48: add             x25, x1, #0x23
    //     0xa7be4c: str             w0, [x25]
    //     0xa7be50: tbz             w0, #0, #0xa7be6c
    //     0xa7be54: ldurb           w16, [x1, #-1]
    //     0xa7be58: ldurb           w17, [x0, #-1]
    //     0xa7be5c: and             x16, x17, x16, lsr #2
    //     0xa7be60: tst             x16, HEAP, lsr #32
    //     0xa7be64: b.eq            #0xa7be6c
    //     0xa7be68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa7be6c: r16 = <String, String?>
    //     0xa7be6c: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xa7be70: ldr             x16, [x16, #0x3c8]
    // 0xa7be74: stp             x2, x16, [SP]
    // 0xa7be78: r0 = Map._fromLiteral()
    //     0xa7be78: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa7be7c: r16 = "/collection"
    //     0xa7be7c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0xa7be80: ldr             x16, [x16, #0x458]
    // 0xa7be84: stp             x16, NULL, [SP, #8]
    // 0xa7be88: str             x0, [SP]
    // 0xa7be8c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa7be8c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa7be90: ldr             x4, [x4, #0x438]
    // 0xa7be94: r0 = GetNavigation.toNamed()
    //     0xa7be94: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa7be98: r0 = Null
    //     0xa7be98: mov             x0, NULL
    // 0xa7be9c: LeaveFrame
    //     0xa7be9c: mov             SP, fp
    //     0xa7bea0: ldp             fp, lr, [SP], #0x10
    // 0xa7bea4: ret
    //     0xa7bea4: ret             
    // 0xa7bea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7bea8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7beac: b               #0xa7bcc0
    // 0xa7beb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7beb0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7beb4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa7beb4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa7beb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7beb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7bebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7bebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xa7bec0, size: 0x5b0
    // 0xa7bec0: EnterFrame
    //     0xa7bec0: stp             fp, lr, [SP, #-0x10]!
    //     0xa7bec4: mov             fp, SP
    // 0xa7bec8: AllocStack(0x60)
    //     0xa7bec8: sub             SP, SP, #0x60
    // 0xa7becc: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa7becc: stur            x1, [fp, #-8]
    //     0xa7bed0: stur            x2, [fp, #-0x10]
    //     0xa7bed4: stur            x3, [fp, #-0x18]
    // 0xa7bed8: CheckStackOverflow
    //     0xa7bed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7bedc: cmp             SP, x16
    //     0xa7bee0: b.ls            #0xa7c44c
    // 0xa7bee4: r1 = 3
    //     0xa7bee4: movz            x1, #0x3
    // 0xa7bee8: r0 = AllocateContext()
    //     0xa7bee8: bl              #0x16f6108  ; AllocateContextStub
    // 0xa7beec: mov             x3, x0
    // 0xa7bef0: ldur            x2, [fp, #-8]
    // 0xa7bef4: stur            x3, [fp, #-0x20]
    // 0xa7bef8: StoreField: r3->field_f = r2
    //     0xa7bef8: stur            w2, [x3, #0xf]
    // 0xa7befc: ldur            x0, [fp, #-0x10]
    // 0xa7bf00: StoreField: r3->field_13 = r0
    //     0xa7bf00: stur            w0, [x3, #0x13]
    // 0xa7bf04: ldur            x4, [fp, #-0x18]
    // 0xa7bf08: r0 = BoxInt64Instr(r4)
    //     0xa7bf08: sbfiz           x0, x4, #1, #0x1f
    //     0xa7bf0c: cmp             x4, x0, asr #1
    //     0xa7bf10: b.eq            #0xa7bf1c
    //     0xa7bf14: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa7bf18: stur            x4, [x0, #7]
    // 0xa7bf1c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa7bf1c: stur            w0, [x3, #0x17]
    // 0xa7bf20: r0 = ImageHeaders.forImages()
    //     0xa7bf20: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa7bf24: mov             x4, x0
    // 0xa7bf28: ldur            x3, [fp, #-0x20]
    // 0xa7bf2c: stur            x4, [fp, #-0x28]
    // 0xa7bf30: LoadField: r2 = r3->field_13
    //     0xa7bf30: ldur            w2, [x3, #0x13]
    // 0xa7bf34: DecompressPointer r2
    //     0xa7bf34: add             x2, x2, HEAP, lsl #32
    // 0xa7bf38: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa7bf38: ldur            w0, [x3, #0x17]
    // 0xa7bf3c: DecompressPointer r0
    //     0xa7bf3c: add             x0, x0, HEAP, lsl #32
    // 0xa7bf40: LoadField: r1 = r2->field_b
    //     0xa7bf40: ldur            w1, [x2, #0xb]
    // 0xa7bf44: r5 = LoadInt32Instr(r0)
    //     0xa7bf44: sbfx            x5, x0, #1, #0x1f
    //     0xa7bf48: tbz             w0, #0, #0xa7bf50
    //     0xa7bf4c: ldur            x5, [x0, #7]
    // 0xa7bf50: r0 = LoadInt32Instr(r1)
    //     0xa7bf50: sbfx            x0, x1, #1, #0x1f
    // 0xa7bf54: mov             x1, x5
    // 0xa7bf58: cmp             x1, x0
    // 0xa7bf5c: b.hs            #0xa7c454
    // 0xa7bf60: LoadField: r0 = r2->field_f
    //     0xa7bf60: ldur            w0, [x2, #0xf]
    // 0xa7bf64: DecompressPointer r0
    //     0xa7bf64: add             x0, x0, HEAP, lsl #32
    // 0xa7bf68: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa7bf68: add             x16, x0, x5, lsl #2
    //     0xa7bf6c: ldur            w1, [x16, #0xf]
    // 0xa7bf70: DecompressPointer r1
    //     0xa7bf70: add             x1, x1, HEAP, lsl #32
    // 0xa7bf74: LoadField: r0 = r1->field_13
    //     0xa7bf74: ldur            w0, [x1, #0x13]
    // 0xa7bf78: DecompressPointer r0
    //     0xa7bf78: add             x0, x0, HEAP, lsl #32
    // 0xa7bf7c: stur            x0, [fp, #-0x10]
    // 0xa7bf80: cmp             w0, NULL
    // 0xa7bf84: b.eq            #0xa7c458
    // 0xa7bf88: r1 = Function '<anonymous closure>':.
    //     0xa7bf88: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b90] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xa7bf8c: ldr             x1, [x1, #0xb90]
    // 0xa7bf90: r2 = Null
    //     0xa7bf90: mov             x2, NULL
    // 0xa7bf94: r0 = AllocateClosure()
    //     0xa7bf94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa7bf98: r1 = Function '<anonymous closure>':.
    //     0xa7bf98: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b98] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa7bf9c: ldr             x1, [x1, #0xb98]
    // 0xa7bfa0: r2 = Null
    //     0xa7bfa0: mov             x2, NULL
    // 0xa7bfa4: stur            x0, [fp, #-0x30]
    // 0xa7bfa8: r0 = AllocateClosure()
    //     0xa7bfa8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa7bfac: stur            x0, [fp, #-0x38]
    // 0xa7bfb0: r0 = CachedNetworkImage()
    //     0xa7bfb0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa7bfb4: stur            x0, [fp, #-0x40]
    // 0xa7bfb8: ldur            x16, [fp, #-0x28]
    // 0xa7bfbc: ldur            lr, [fp, #-0x30]
    // 0xa7bfc0: stp             lr, x16, [SP, #0x10]
    // 0xa7bfc4: ldur            x16, [fp, #-0x38]
    // 0xa7bfc8: r30 = Instance_BoxFit
    //     0xa7bfc8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa7bfcc: ldr             lr, [lr, #0x118]
    // 0xa7bfd0: stp             lr, x16, [SP]
    // 0xa7bfd4: mov             x1, x0
    // 0xa7bfd8: ldur            x2, [fp, #-0x10]
    // 0xa7bfdc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa7bfdc: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa7bfe0: ldr             x4, [x4, #0x2b0]
    // 0xa7bfe4: r0 = CachedNetworkImage()
    //     0xa7bfe4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa7bfe8: r0 = AspectRatio()
    //     0xa7bfe8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa7bfec: d0 = 1.000000
    //     0xa7bfec: fmov            d0, #1.00000000
    // 0xa7bff0: stur            x0, [fp, #-0x10]
    // 0xa7bff4: StoreField: r0->field_f = d0
    //     0xa7bff4: stur            d0, [x0, #0xf]
    // 0xa7bff8: ldur            x1, [fp, #-0x40]
    // 0xa7bffc: StoreField: r0->field_b = r1
    //     0xa7bffc: stur            w1, [x0, #0xb]
    // 0xa7c000: r0 = Align()
    //     0xa7c000: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa7c004: mov             x2, x0
    // 0xa7c008: r0 = Instance_Alignment
    //     0xa7c008: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xa7c00c: ldr             x0, [x0, #0xcb0]
    // 0xa7c010: stur            x2, [fp, #-0x28]
    // 0xa7c014: StoreField: r2->field_f = r0
    //     0xa7c014: stur            w0, [x2, #0xf]
    // 0xa7c018: ldur            x0, [fp, #-0x10]
    // 0xa7c01c: StoreField: r2->field_b = r0
    //     0xa7c01c: stur            w0, [x2, #0xb]
    // 0xa7c020: ldur            x3, [fp, #-0x20]
    // 0xa7c024: LoadField: r4 = r3->field_13
    //     0xa7c024: ldur            w4, [x3, #0x13]
    // 0xa7c028: DecompressPointer r4
    //     0xa7c028: add             x4, x4, HEAP, lsl #32
    // 0xa7c02c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xa7c02c: ldur            w0, [x3, #0x17]
    // 0xa7c030: DecompressPointer r0
    //     0xa7c030: add             x0, x0, HEAP, lsl #32
    // 0xa7c034: LoadField: r1 = r4->field_b
    //     0xa7c034: ldur            w1, [x4, #0xb]
    // 0xa7c038: r5 = LoadInt32Instr(r0)
    //     0xa7c038: sbfx            x5, x0, #1, #0x1f
    //     0xa7c03c: tbz             w0, #0, #0xa7c044
    //     0xa7c040: ldur            x5, [x0, #7]
    // 0xa7c044: r0 = LoadInt32Instr(r1)
    //     0xa7c044: sbfx            x0, x1, #1, #0x1f
    // 0xa7c048: mov             x1, x5
    // 0xa7c04c: cmp             x1, x0
    // 0xa7c050: b.hs            #0xa7c45c
    // 0xa7c054: LoadField: r0 = r4->field_f
    //     0xa7c054: ldur            w0, [x4, #0xf]
    // 0xa7c058: DecompressPointer r0
    //     0xa7c058: add             x0, x0, HEAP, lsl #32
    // 0xa7c05c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa7c05c: add             x16, x0, x5, lsl #2
    //     0xa7c060: ldur            w1, [x16, #0xf]
    // 0xa7c064: DecompressPointer r1
    //     0xa7c064: add             x1, x1, HEAP, lsl #32
    // 0xa7c068: LoadField: r0 = r1->field_f
    //     0xa7c068: ldur            w0, [x1, #0xf]
    // 0xa7c06c: DecompressPointer r0
    //     0xa7c06c: add             x0, x0, HEAP, lsl #32
    // 0xa7c070: cmp             w0, NULL
    // 0xa7c074: b.eq            #0xa7c460
    // 0xa7c078: LoadField: r1 = r0->field_7
    //     0xa7c078: ldur            w1, [x0, #7]
    // 0xa7c07c: cbz             w1, #0xa7c298
    // 0xa7c080: ldur            x0, [fp, #-8]
    // 0xa7c084: LoadField: r1 = r0->field_f
    //     0xa7c084: ldur            w1, [x0, #0xf]
    // 0xa7c088: DecompressPointer r1
    //     0xa7c088: add             x1, x1, HEAP, lsl #32
    // 0xa7c08c: cmp             w1, NULL
    // 0xa7c090: b.eq            #0xa7c464
    // 0xa7c094: r0 = of()
    //     0xa7c094: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7c098: LoadField: r1 = r0->field_5b
    //     0xa7c098: ldur            w1, [x0, #0x5b]
    // 0xa7c09c: DecompressPointer r1
    //     0xa7c09c: add             x1, x1, HEAP, lsl #32
    // 0xa7c0a0: r16 = <Color>
    //     0xa7c0a0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa7c0a4: ldr             x16, [x16, #0xf80]
    // 0xa7c0a8: stp             x1, x16, [SP]
    // 0xa7c0ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa7c0ac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa7c0b0: r0 = all()
    //     0xa7c0b0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa7c0b4: stur            x0, [fp, #-0x10]
    // 0xa7c0b8: r16 = <EdgeInsets>
    //     0xa7c0b8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa7c0bc: ldr             x16, [x16, #0xda0]
    // 0xa7c0c0: r30 = Instance_EdgeInsets
    //     0xa7c0c0: add             lr, PP, #0x52, lsl #12  ; [pp+0x52ba0] Obj!EdgeInsets@d58341
    //     0xa7c0c4: ldr             lr, [lr, #0xba0]
    // 0xa7c0c8: stp             lr, x16, [SP]
    // 0xa7c0cc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa7c0cc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa7c0d0: r0 = all()
    //     0xa7c0d0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa7c0d4: stur            x0, [fp, #-0x30]
    // 0xa7c0d8: r16 = <RoundedRectangleBorder>
    //     0xa7c0d8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa7c0dc: ldr             x16, [x16, #0xf78]
    // 0xa7c0e0: r30 = Instance_RoundedRectangleBorder
    //     0xa7c0e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xa7c0e4: ldr             lr, [lr, #0xd68]
    // 0xa7c0e8: stp             lr, x16, [SP]
    // 0xa7c0ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa7c0ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa7c0f0: r0 = all()
    //     0xa7c0f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa7c0f4: stur            x0, [fp, #-0x38]
    // 0xa7c0f8: r0 = ButtonStyle()
    //     0xa7c0f8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa7c0fc: mov             x1, x0
    // 0xa7c100: ldur            x0, [fp, #-0x10]
    // 0xa7c104: stur            x1, [fp, #-0x40]
    // 0xa7c108: StoreField: r1->field_b = r0
    //     0xa7c108: stur            w0, [x1, #0xb]
    // 0xa7c10c: ldur            x0, [fp, #-0x30]
    // 0xa7c110: StoreField: r1->field_23 = r0
    //     0xa7c110: stur            w0, [x1, #0x23]
    // 0xa7c114: ldur            x0, [fp, #-0x38]
    // 0xa7c118: StoreField: r1->field_43 = r0
    //     0xa7c118: stur            w0, [x1, #0x43]
    // 0xa7c11c: r0 = TextButtonThemeData()
    //     0xa7c11c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa7c120: mov             x3, x0
    // 0xa7c124: ldur            x0, [fp, #-0x40]
    // 0xa7c128: stur            x3, [fp, #-0x30]
    // 0xa7c12c: StoreField: r3->field_7 = r0
    //     0xa7c12c: stur            w0, [x3, #7]
    // 0xa7c130: ldur            x4, [fp, #-0x20]
    // 0xa7c134: LoadField: r2 = r4->field_13
    //     0xa7c134: ldur            w2, [x4, #0x13]
    // 0xa7c138: DecompressPointer r2
    //     0xa7c138: add             x2, x2, HEAP, lsl #32
    // 0xa7c13c: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xa7c13c: ldur            w0, [x4, #0x17]
    // 0xa7c140: DecompressPointer r0
    //     0xa7c140: add             x0, x0, HEAP, lsl #32
    // 0xa7c144: LoadField: r1 = r2->field_b
    //     0xa7c144: ldur            w1, [x2, #0xb]
    // 0xa7c148: r5 = LoadInt32Instr(r0)
    //     0xa7c148: sbfx            x5, x0, #1, #0x1f
    //     0xa7c14c: tbz             w0, #0, #0xa7c154
    //     0xa7c150: ldur            x5, [x0, #7]
    // 0xa7c154: r0 = LoadInt32Instr(r1)
    //     0xa7c154: sbfx            x0, x1, #1, #0x1f
    // 0xa7c158: mov             x1, x5
    // 0xa7c15c: cmp             x1, x0
    // 0xa7c160: b.hs            #0xa7c468
    // 0xa7c164: LoadField: r0 = r2->field_f
    //     0xa7c164: ldur            w0, [x2, #0xf]
    // 0xa7c168: DecompressPointer r0
    //     0xa7c168: add             x0, x0, HEAP, lsl #32
    // 0xa7c16c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa7c16c: add             x16, x0, x5, lsl #2
    //     0xa7c170: ldur            w1, [x16, #0xf]
    // 0xa7c174: DecompressPointer r1
    //     0xa7c174: add             x1, x1, HEAP, lsl #32
    // 0xa7c178: LoadField: r5 = r1->field_f
    //     0xa7c178: ldur            w5, [x1, #0xf]
    // 0xa7c17c: DecompressPointer r5
    //     0xa7c17c: add             x5, x5, HEAP, lsl #32
    // 0xa7c180: mov             x0, x5
    // 0xa7c184: stur            x5, [fp, #-0x10]
    // 0xa7c188: r2 = Null
    //     0xa7c188: mov             x2, NULL
    // 0xa7c18c: r1 = Null
    //     0xa7c18c: mov             x1, NULL
    // 0xa7c190: r4 = LoadClassIdInstr(r0)
    //     0xa7c190: ldur            x4, [x0, #-1]
    //     0xa7c194: ubfx            x4, x4, #0xc, #0x14
    // 0xa7c198: sub             x4, x4, #0x5e
    // 0xa7c19c: cmp             x4, #1
    // 0xa7c1a0: b.ls            #0xa7c1b4
    // 0xa7c1a4: r8 = String
    //     0xa7c1a4: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa7c1a8: r3 = Null
    //     0xa7c1a8: add             x3, PP, #0x52, lsl #12  ; [pp+0x52ba8] Null
    //     0xa7c1ac: ldr             x3, [x3, #0xba8]
    // 0xa7c1b0: r0 = String()
    //     0xa7c1b0: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa7c1b4: ldur            x0, [fp, #-8]
    // 0xa7c1b8: LoadField: r1 = r0->field_f
    //     0xa7c1b8: ldur            w1, [x0, #0xf]
    // 0xa7c1bc: DecompressPointer r1
    //     0xa7c1bc: add             x1, x1, HEAP, lsl #32
    // 0xa7c1c0: cmp             w1, NULL
    // 0xa7c1c4: b.eq            #0xa7c46c
    // 0xa7c1c8: r0 = of()
    //     0xa7c1c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa7c1cc: LoadField: r1 = r0->field_87
    //     0xa7c1cc: ldur            w1, [x0, #0x87]
    // 0xa7c1d0: DecompressPointer r1
    //     0xa7c1d0: add             x1, x1, HEAP, lsl #32
    // 0xa7c1d4: LoadField: r0 = r1->field_7
    //     0xa7c1d4: ldur            w0, [x1, #7]
    // 0xa7c1d8: DecompressPointer r0
    //     0xa7c1d8: add             x0, x0, HEAP, lsl #32
    // 0xa7c1dc: r16 = Instance_Color
    //     0xa7c1dc: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa7c1e0: r30 = 14.000000
    //     0xa7c1e0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa7c1e4: ldr             lr, [lr, #0x1d8]
    // 0xa7c1e8: stp             lr, x16, [SP]
    // 0xa7c1ec: mov             x1, x0
    // 0xa7c1f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa7c1f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa7c1f4: ldr             x4, [x4, #0x9b8]
    // 0xa7c1f8: r0 = copyWith()
    //     0xa7c1f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa7c1fc: stur            x0, [fp, #-8]
    // 0xa7c200: r0 = Text()
    //     0xa7c200: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa7c204: mov             x3, x0
    // 0xa7c208: ldur            x0, [fp, #-0x10]
    // 0xa7c20c: stur            x3, [fp, #-0x38]
    // 0xa7c210: StoreField: r3->field_b = r0
    //     0xa7c210: stur            w0, [x3, #0xb]
    // 0xa7c214: ldur            x0, [fp, #-8]
    // 0xa7c218: StoreField: r3->field_13 = r0
    //     0xa7c218: stur            w0, [x3, #0x13]
    // 0xa7c21c: ldur            x2, [fp, #-0x20]
    // 0xa7c220: r1 = Function '<anonymous closure>':.
    //     0xa7c220: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bb8] AnonymousClosure: (0xa7bc98), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::lineThemeSlider (0xa7bec0)
    //     0xa7c224: ldr             x1, [x1, #0xbb8]
    // 0xa7c228: r0 = AllocateClosure()
    //     0xa7c228: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa7c22c: stur            x0, [fp, #-8]
    // 0xa7c230: r0 = TextButton()
    //     0xa7c230: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa7c234: mov             x1, x0
    // 0xa7c238: ldur            x0, [fp, #-8]
    // 0xa7c23c: stur            x1, [fp, #-0x10]
    // 0xa7c240: StoreField: r1->field_b = r0
    //     0xa7c240: stur            w0, [x1, #0xb]
    // 0xa7c244: r0 = false
    //     0xa7c244: add             x0, NULL, #0x30  ; false
    // 0xa7c248: StoreField: r1->field_27 = r0
    //     0xa7c248: stur            w0, [x1, #0x27]
    // 0xa7c24c: r0 = true
    //     0xa7c24c: add             x0, NULL, #0x20  ; true
    // 0xa7c250: StoreField: r1->field_2f = r0
    //     0xa7c250: stur            w0, [x1, #0x2f]
    // 0xa7c254: ldur            x0, [fp, #-0x38]
    // 0xa7c258: StoreField: r1->field_37 = r0
    //     0xa7c258: stur            w0, [x1, #0x37]
    // 0xa7c25c: r0 = TextButtonTheme()
    //     0xa7c25c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa7c260: mov             x1, x0
    // 0xa7c264: ldur            x0, [fp, #-0x30]
    // 0xa7c268: stur            x1, [fp, #-8]
    // 0xa7c26c: StoreField: r1->field_f = r0
    //     0xa7c26c: stur            w0, [x1, #0xf]
    // 0xa7c270: ldur            x0, [fp, #-0x10]
    // 0xa7c274: StoreField: r1->field_b = r0
    //     0xa7c274: stur            w0, [x1, #0xb]
    // 0xa7c278: r0 = SizedBox()
    //     0xa7c278: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa7c27c: mov             x1, x0
    // 0xa7c280: r0 = 40.000000
    //     0xa7c280: add             x0, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa7c284: ldr             x0, [x0, #8]
    // 0xa7c288: StoreField: r1->field_13 = r0
    //     0xa7c288: stur            w0, [x1, #0x13]
    // 0xa7c28c: ldur            x0, [fp, #-8]
    // 0xa7c290: StoreField: r1->field_b = r0
    //     0xa7c290: stur            w0, [x1, #0xb]
    // 0xa7c294: b               #0xa7c2b0
    // 0xa7c298: r0 = Container()
    //     0xa7c298: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa7c29c: mov             x1, x0
    // 0xa7c2a0: stur            x0, [fp, #-8]
    // 0xa7c2a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa7c2a4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa7c2a8: r0 = Container()
    //     0xa7c2a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa7c2ac: ldur            x1, [fp, #-8]
    // 0xa7c2b0: ldur            x0, [fp, #-0x28]
    // 0xa7c2b4: stur            x1, [fp, #-8]
    // 0xa7c2b8: r0 = Padding()
    //     0xa7c2b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa7c2bc: mov             x3, x0
    // 0xa7c2c0: r0 = Instance_EdgeInsets
    //     0xa7c2c0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52bc0] Obj!EdgeInsets@d574d1
    //     0xa7c2c4: ldr             x0, [x0, #0xbc0]
    // 0xa7c2c8: stur            x3, [fp, #-0x10]
    // 0xa7c2cc: StoreField: r3->field_f = r0
    //     0xa7c2cc: stur            w0, [x3, #0xf]
    // 0xa7c2d0: ldur            x0, [fp, #-8]
    // 0xa7c2d4: StoreField: r3->field_b = r0
    //     0xa7c2d4: stur            w0, [x3, #0xb]
    // 0xa7c2d8: r1 = Null
    //     0xa7c2d8: mov             x1, NULL
    // 0xa7c2dc: r2 = 4
    //     0xa7c2dc: movz            x2, #0x4
    // 0xa7c2e0: r0 = AllocateArray()
    //     0xa7c2e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa7c2e4: mov             x2, x0
    // 0xa7c2e8: ldur            x0, [fp, #-0x28]
    // 0xa7c2ec: stur            x2, [fp, #-8]
    // 0xa7c2f0: StoreField: r2->field_f = r0
    //     0xa7c2f0: stur            w0, [x2, #0xf]
    // 0xa7c2f4: ldur            x0, [fp, #-0x10]
    // 0xa7c2f8: StoreField: r2->field_13 = r0
    //     0xa7c2f8: stur            w0, [x2, #0x13]
    // 0xa7c2fc: r1 = <Widget>
    //     0xa7c2fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa7c300: r0 = AllocateGrowableArray()
    //     0xa7c300: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa7c304: mov             x1, x0
    // 0xa7c308: ldur            x0, [fp, #-8]
    // 0xa7c30c: stur            x1, [fp, #-0x10]
    // 0xa7c310: StoreField: r1->field_f = r0
    //     0xa7c310: stur            w0, [x1, #0xf]
    // 0xa7c314: r0 = 4
    //     0xa7c314: movz            x0, #0x4
    // 0xa7c318: StoreField: r1->field_b = r0
    //     0xa7c318: stur            w0, [x1, #0xb]
    // 0xa7c31c: r0 = Stack()
    //     0xa7c31c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa7c320: mov             x1, x0
    // 0xa7c324: r0 = Instance_Alignment
    //     0xa7c324: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa7c328: ldr             x0, [x0, #0x5b8]
    // 0xa7c32c: stur            x1, [fp, #-8]
    // 0xa7c330: StoreField: r1->field_f = r0
    //     0xa7c330: stur            w0, [x1, #0xf]
    // 0xa7c334: r0 = Instance_StackFit
    //     0xa7c334: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa7c338: ldr             x0, [x0, #0xfa8]
    // 0xa7c33c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa7c33c: stur            w0, [x1, #0x17]
    // 0xa7c340: r0 = Instance_Clip
    //     0xa7c340: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa7c344: ldr             x0, [x0, #0x7e0]
    // 0xa7c348: StoreField: r1->field_1b = r0
    //     0xa7c348: stur            w0, [x1, #0x1b]
    // 0xa7c34c: ldur            x0, [fp, #-0x10]
    // 0xa7c350: StoreField: r1->field_b = r0
    //     0xa7c350: stur            w0, [x1, #0xb]
    // 0xa7c354: r0 = AnimatedContainer()
    //     0xa7c354: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa7c358: stur            x0, [fp, #-0x10]
    // 0xa7c35c: r16 = Instance_Cubic
    //     0xa7c35c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa7c360: ldr             x16, [x16, #0xaf8]
    // 0xa7c364: str             x16, [SP]
    // 0xa7c368: mov             x1, x0
    // 0xa7c36c: ldur            x2, [fp, #-8]
    // 0xa7c370: r3 = Instance_Duration
    //     0xa7c370: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa7c374: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa7c374: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa7c378: ldr             x4, [x4, #0xbc8]
    // 0xa7c37c: r0 = AnimatedContainer()
    //     0xa7c37c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa7c380: r1 = <FlexParentData>
    //     0xa7c380: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa7c384: ldr             x1, [x1, #0xe00]
    // 0xa7c388: r0 = Expanded()
    //     0xa7c388: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa7c38c: mov             x3, x0
    // 0xa7c390: r0 = 1
    //     0xa7c390: movz            x0, #0x1
    // 0xa7c394: stur            x3, [fp, #-8]
    // 0xa7c398: StoreField: r3->field_13 = r0
    //     0xa7c398: stur            x0, [x3, #0x13]
    // 0xa7c39c: r0 = Instance_FlexFit
    //     0xa7c39c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa7c3a0: ldr             x0, [x0, #0xe08]
    // 0xa7c3a4: StoreField: r3->field_1b = r0
    //     0xa7c3a4: stur            w0, [x3, #0x1b]
    // 0xa7c3a8: ldur            x0, [fp, #-0x10]
    // 0xa7c3ac: StoreField: r3->field_b = r0
    //     0xa7c3ac: stur            w0, [x3, #0xb]
    // 0xa7c3b0: r1 = Null
    //     0xa7c3b0: mov             x1, NULL
    // 0xa7c3b4: r2 = 2
    //     0xa7c3b4: movz            x2, #0x2
    // 0xa7c3b8: r0 = AllocateArray()
    //     0xa7c3b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa7c3bc: mov             x2, x0
    // 0xa7c3c0: ldur            x0, [fp, #-8]
    // 0xa7c3c4: stur            x2, [fp, #-0x10]
    // 0xa7c3c8: StoreField: r2->field_f = r0
    //     0xa7c3c8: stur            w0, [x2, #0xf]
    // 0xa7c3cc: r1 = <Widget>
    //     0xa7c3cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa7c3d0: r0 = AllocateGrowableArray()
    //     0xa7c3d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa7c3d4: mov             x1, x0
    // 0xa7c3d8: ldur            x0, [fp, #-0x10]
    // 0xa7c3dc: stur            x1, [fp, #-8]
    // 0xa7c3e0: StoreField: r1->field_f = r0
    //     0xa7c3e0: stur            w0, [x1, #0xf]
    // 0xa7c3e4: r0 = 2
    //     0xa7c3e4: movz            x0, #0x2
    // 0xa7c3e8: StoreField: r1->field_b = r0
    //     0xa7c3e8: stur            w0, [x1, #0xb]
    // 0xa7c3ec: r0 = Row()
    //     0xa7c3ec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa7c3f0: r1 = Instance_Axis
    //     0xa7c3f0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa7c3f4: StoreField: r0->field_f = r1
    //     0xa7c3f4: stur            w1, [x0, #0xf]
    // 0xa7c3f8: r1 = Instance_MainAxisAlignment
    //     0xa7c3f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa7c3fc: ldr             x1, [x1, #0xa08]
    // 0xa7c400: StoreField: r0->field_13 = r1
    //     0xa7c400: stur            w1, [x0, #0x13]
    // 0xa7c404: r1 = Instance_MainAxisSize
    //     0xa7c404: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa7c408: ldr             x1, [x1, #0xa10]
    // 0xa7c40c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa7c40c: stur            w1, [x0, #0x17]
    // 0xa7c410: r1 = Instance_CrossAxisAlignment
    //     0xa7c410: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa7c414: ldr             x1, [x1, #0xa18]
    // 0xa7c418: StoreField: r0->field_1b = r1
    //     0xa7c418: stur            w1, [x0, #0x1b]
    // 0xa7c41c: r1 = Instance_VerticalDirection
    //     0xa7c41c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa7c420: ldr             x1, [x1, #0xa20]
    // 0xa7c424: StoreField: r0->field_23 = r1
    //     0xa7c424: stur            w1, [x0, #0x23]
    // 0xa7c428: r1 = Instance_Clip
    //     0xa7c428: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa7c42c: ldr             x1, [x1, #0x38]
    // 0xa7c430: StoreField: r0->field_2b = r1
    //     0xa7c430: stur            w1, [x0, #0x2b]
    // 0xa7c434: StoreField: r0->field_2f = rZR
    //     0xa7c434: stur            xzr, [x0, #0x2f]
    // 0xa7c438: ldur            x1, [fp, #-8]
    // 0xa7c43c: StoreField: r0->field_b = r1
    //     0xa7c43c: stur            w1, [x0, #0xb]
    // 0xa7c440: LeaveFrame
    //     0xa7c440: mov             SP, fp
    //     0xa7c444: ldp             fp, lr, [SP], #0x10
    // 0xa7c448: ret
    //     0xa7c448: ret             
    // 0xa7c44c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7c44c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7c450: b               #0xa7bee4
    // 0xa7c454: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c454: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7c458: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7c45c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c45c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c460: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa7c460: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa7c464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7c464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa7c468: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c468: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c46c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa7c46c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc0059c, size: 0x5f8
    // 0xc0059c: EnterFrame
    //     0xc0059c: stp             fp, lr, [SP, #-0x10]!
    //     0xc005a0: mov             fp, SP
    // 0xc005a4: AllocStack(0x68)
    //     0xc005a4: sub             SP, SP, #0x68
    // 0xc005a8: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc005a8: mov             x0, x1
    //     0xc005ac: stur            x1, [fp, #-8]
    //     0xc005b0: mov             x1, x2
    //     0xc005b4: stur            x2, [fp, #-0x10]
    // 0xc005b8: CheckStackOverflow
    //     0xc005b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc005bc: cmp             SP, x16
    //     0xc005c0: b.ls            #0xc00b6c
    // 0xc005c4: r1 = 1
    //     0xc005c4: movz            x1, #0x1
    // 0xc005c8: r0 = AllocateContext()
    //     0xc005c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xc005cc: mov             x3, x0
    // 0xc005d0: ldur            x0, [fp, #-8]
    // 0xc005d4: stur            x3, [fp, #-0x20]
    // 0xc005d8: StoreField: r3->field_f = r0
    //     0xc005d8: stur            w0, [x3, #0xf]
    // 0xc005dc: LoadField: r1 = r0->field_b
    //     0xc005dc: ldur            w1, [x0, #0xb]
    // 0xc005e0: DecompressPointer r1
    //     0xc005e0: add             x1, x1, HEAP, lsl #32
    // 0xc005e4: cmp             w1, NULL
    // 0xc005e8: b.eq            #0xc00b74
    // 0xc005ec: LoadField: r2 = r1->field_13
    //     0xc005ec: ldur            w2, [x1, #0x13]
    // 0xc005f0: DecompressPointer r2
    //     0xc005f0: add             x2, x2, HEAP, lsl #32
    // 0xc005f4: LoadField: r1 = r2->field_7
    //     0xc005f4: ldur            w1, [x2, #7]
    // 0xc005f8: DecompressPointer r1
    //     0xc005f8: add             x1, x1, HEAP, lsl #32
    // 0xc005fc: cmp             w1, NULL
    // 0xc00600: b.ne            #0xc0060c
    // 0xc00604: r1 = Instance_TitleAlignment
    //     0xc00604: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc00608: ldr             x1, [x1, #0x518]
    // 0xc0060c: r16 = Instance_TitleAlignment
    //     0xc0060c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc00610: ldr             x16, [x16, #0x520]
    // 0xc00614: cmp             w1, w16
    // 0xc00618: b.ne            #0xc00628
    // 0xc0061c: r4 = Instance_CrossAxisAlignment
    //     0xc0061c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xc00620: ldr             x4, [x4, #0xc68]
    // 0xc00624: b               #0xc0064c
    // 0xc00628: r16 = Instance_TitleAlignment
    //     0xc00628: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc0062c: ldr             x16, [x16, #0x518]
    // 0xc00630: cmp             w1, w16
    // 0xc00634: b.ne            #0xc00644
    // 0xc00638: r4 = Instance_CrossAxisAlignment
    //     0xc00638: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0063c: ldr             x4, [x4, #0x890]
    // 0xc00640: b               #0xc0064c
    // 0xc00644: r4 = Instance_CrossAxisAlignment
    //     0xc00644: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc00648: ldr             x4, [x4, #0xa18]
    // 0xc0064c: stur            x4, [fp, #-0x18]
    // 0xc00650: r1 = <Widget>
    //     0xc00650: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc00654: r2 = 0
    //     0xc00654: movz            x2, #0
    // 0xc00658: r0 = _GrowableList()
    //     0xc00658: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0065c: mov             x2, x0
    // 0xc00660: ldur            x1, [fp, #-8]
    // 0xc00664: stur            x2, [fp, #-0x28]
    // 0xc00668: LoadField: r0 = r1->field_b
    //     0xc00668: ldur            w0, [x1, #0xb]
    // 0xc0066c: DecompressPointer r0
    //     0xc0066c: add             x0, x0, HEAP, lsl #32
    // 0xc00670: cmp             w0, NULL
    // 0xc00674: b.eq            #0xc00b78
    // 0xc00678: LoadField: r3 = r0->field_f
    //     0xc00678: ldur            w3, [x0, #0xf]
    // 0xc0067c: DecompressPointer r3
    //     0xc0067c: add             x3, x3, HEAP, lsl #32
    // 0xc00680: LoadField: r0 = r3->field_7
    //     0xc00680: ldur            w0, [x3, #7]
    // 0xc00684: cbz             w0, #0xc00814
    // 0xc00688: r0 = LoadClassIdInstr(r3)
    //     0xc00688: ldur            x0, [x3, #-1]
    //     0xc0068c: ubfx            x0, x0, #0xc, #0x14
    // 0xc00690: str             x3, [SP]
    // 0xc00694: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc00694: sub             lr, x0, #1, lsl #12
    //     0xc00698: ldr             lr, [x21, lr, lsl #3]
    //     0xc0069c: blr             lr
    // 0xc006a0: mov             x2, x0
    // 0xc006a4: ldur            x0, [fp, #-8]
    // 0xc006a8: stur            x2, [fp, #-0x38]
    // 0xc006ac: LoadField: r1 = r0->field_b
    //     0xc006ac: ldur            w1, [x0, #0xb]
    // 0xc006b0: DecompressPointer r1
    //     0xc006b0: add             x1, x1, HEAP, lsl #32
    // 0xc006b4: cmp             w1, NULL
    // 0xc006b8: b.eq            #0xc00b7c
    // 0xc006bc: LoadField: r3 = r1->field_13
    //     0xc006bc: ldur            w3, [x1, #0x13]
    // 0xc006c0: DecompressPointer r3
    //     0xc006c0: add             x3, x3, HEAP, lsl #32
    // 0xc006c4: LoadField: r1 = r3->field_7
    //     0xc006c4: ldur            w1, [x3, #7]
    // 0xc006c8: DecompressPointer r1
    //     0xc006c8: add             x1, x1, HEAP, lsl #32
    // 0xc006cc: cmp             w1, NULL
    // 0xc006d0: b.ne            #0xc006dc
    // 0xc006d4: r1 = Instance_TitleAlignment
    //     0xc006d4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc006d8: ldr             x1, [x1, #0x518]
    // 0xc006dc: r16 = Instance_TitleAlignment
    //     0xc006dc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc006e0: ldr             x16, [x16, #0x520]
    // 0xc006e4: cmp             w1, w16
    // 0xc006e8: b.ne            #0xc006f4
    // 0xc006ec: r4 = Instance_TextAlign
    //     0xc006ec: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xc006f0: b               #0xc00710
    // 0xc006f4: r16 = Instance_TitleAlignment
    //     0xc006f4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc006f8: ldr             x16, [x16, #0x518]
    // 0xc006fc: cmp             w1, w16
    // 0xc00700: b.ne            #0xc0070c
    // 0xc00704: r4 = Instance_TextAlign
    //     0xc00704: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc00708: b               #0xc00710
    // 0xc0070c: r4 = Instance_TextAlign
    //     0xc0070c: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc00710: ldur            x3, [fp, #-0x28]
    // 0xc00714: ldur            x1, [fp, #-0x10]
    // 0xc00718: stur            x4, [fp, #-0x30]
    // 0xc0071c: r0 = of()
    //     0xc0071c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc00720: LoadField: r1 = r0->field_87
    //     0xc00720: ldur            w1, [x0, #0x87]
    // 0xc00724: DecompressPointer r1
    //     0xc00724: add             x1, x1, HEAP, lsl #32
    // 0xc00728: LoadField: r0 = r1->field_27
    //     0xc00728: ldur            w0, [x1, #0x27]
    // 0xc0072c: DecompressPointer r0
    //     0xc0072c: add             x0, x0, HEAP, lsl #32
    // 0xc00730: r16 = Instance_Color
    //     0xc00730: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc00734: r30 = 21.000000
    //     0xc00734: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xc00738: ldr             lr, [lr, #0x9b0]
    // 0xc0073c: stp             lr, x16, [SP]
    // 0xc00740: mov             x1, x0
    // 0xc00744: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc00744: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc00748: ldr             x4, [x4, #0x9b8]
    // 0xc0074c: r0 = copyWith()
    //     0xc0074c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc00750: stur            x0, [fp, #-0x40]
    // 0xc00754: r0 = Text()
    //     0xc00754: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc00758: mov             x1, x0
    // 0xc0075c: ldur            x0, [fp, #-0x38]
    // 0xc00760: stur            x1, [fp, #-0x48]
    // 0xc00764: StoreField: r1->field_b = r0
    //     0xc00764: stur            w0, [x1, #0xb]
    // 0xc00768: ldur            x0, [fp, #-0x40]
    // 0xc0076c: StoreField: r1->field_13 = r0
    //     0xc0076c: stur            w0, [x1, #0x13]
    // 0xc00770: ldur            x0, [fp, #-0x30]
    // 0xc00774: StoreField: r1->field_1b = r0
    //     0xc00774: stur            w0, [x1, #0x1b]
    // 0xc00778: r0 = Padding()
    //     0xc00778: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0077c: mov             x2, x0
    // 0xc00780: r0 = Instance_EdgeInsets
    //     0xc00780: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xc00784: ldr             x0, [x0, #0x778]
    // 0xc00788: stur            x2, [fp, #-0x30]
    // 0xc0078c: StoreField: r2->field_f = r0
    //     0xc0078c: stur            w0, [x2, #0xf]
    // 0xc00790: ldur            x0, [fp, #-0x48]
    // 0xc00794: StoreField: r2->field_b = r0
    //     0xc00794: stur            w0, [x2, #0xb]
    // 0xc00798: ldur            x0, [fp, #-0x28]
    // 0xc0079c: LoadField: r1 = r0->field_b
    //     0xc0079c: ldur            w1, [x0, #0xb]
    // 0xc007a0: LoadField: r3 = r0->field_f
    //     0xc007a0: ldur            w3, [x0, #0xf]
    // 0xc007a4: DecompressPointer r3
    //     0xc007a4: add             x3, x3, HEAP, lsl #32
    // 0xc007a8: LoadField: r4 = r3->field_b
    //     0xc007a8: ldur            w4, [x3, #0xb]
    // 0xc007ac: r3 = LoadInt32Instr(r1)
    //     0xc007ac: sbfx            x3, x1, #1, #0x1f
    // 0xc007b0: stur            x3, [fp, #-0x50]
    // 0xc007b4: r1 = LoadInt32Instr(r4)
    //     0xc007b4: sbfx            x1, x4, #1, #0x1f
    // 0xc007b8: cmp             x3, x1
    // 0xc007bc: b.ne            #0xc007c8
    // 0xc007c0: mov             x1, x0
    // 0xc007c4: r0 = _growToNextCapacity()
    //     0xc007c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc007c8: ldur            x3, [fp, #-0x28]
    // 0xc007cc: ldur            x2, [fp, #-0x50]
    // 0xc007d0: add             x0, x2, #1
    // 0xc007d4: lsl             x1, x0, #1
    // 0xc007d8: StoreField: r3->field_b = r1
    //     0xc007d8: stur            w1, [x3, #0xb]
    // 0xc007dc: LoadField: r1 = r3->field_f
    //     0xc007dc: ldur            w1, [x3, #0xf]
    // 0xc007e0: DecompressPointer r1
    //     0xc007e0: add             x1, x1, HEAP, lsl #32
    // 0xc007e4: ldur            x0, [fp, #-0x30]
    // 0xc007e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc007e8: add             x25, x1, x2, lsl #2
    //     0xc007ec: add             x25, x25, #0xf
    //     0xc007f0: str             w0, [x25]
    //     0xc007f4: tbz             w0, #0, #0xc00810
    //     0xc007f8: ldurb           w16, [x1, #-1]
    //     0xc007fc: ldurb           w17, [x0, #-1]
    //     0xc00800: and             x16, x17, x16, lsr #2
    //     0xc00804: tst             x16, HEAP, lsr #32
    //     0xc00808: b.eq            #0xc00810
    //     0xc0080c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc00810: b               #0xc00818
    // 0xc00814: mov             x3, x2
    // 0xc00818: ldur            x0, [fp, #-8]
    // 0xc0081c: LoadField: r1 = r0->field_b
    //     0xc0081c: ldur            w1, [x0, #0xb]
    // 0xc00820: DecompressPointer r1
    //     0xc00820: add             x1, x1, HEAP, lsl #32
    // 0xc00824: cmp             w1, NULL
    // 0xc00828: b.eq            #0xc00b80
    // 0xc0082c: LoadField: r2 = r1->field_b
    //     0xc0082c: ldur            w2, [x1, #0xb]
    // 0xc00830: DecompressPointer r2
    //     0xc00830: add             x2, x2, HEAP, lsl #32
    // 0xc00834: LoadField: r4 = r2->field_b
    //     0xc00834: ldur            w4, [x2, #0xb]
    // 0xc00838: stur            x4, [fp, #-0x38]
    // 0xc0083c: LoadField: r5 = r0->field_13
    //     0xc0083c: ldur            w5, [x0, #0x13]
    // 0xc00840: DecompressPointer r5
    //     0xc00840: add             x5, x5, HEAP, lsl #32
    // 0xc00844: r16 = Sentinel
    //     0xc00844: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc00848: cmp             w5, w16
    // 0xc0084c: b.eq            #0xc00b84
    // 0xc00850: ldur            x2, [fp, #-0x20]
    // 0xc00854: stur            x5, [fp, #-0x30]
    // 0xc00858: r1 = Function '<anonymous closure>':.
    //     0xc00858: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b78] AnonymousClosure: (0xc00c04), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xc0059c)
    //     0xc0085c: ldr             x1, [x1, #0xb78]
    // 0xc00860: r0 = AllocateClosure()
    //     0xc00860: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00864: ldur            x2, [fp, #-0x20]
    // 0xc00868: r1 = Function '<anonymous closure>':.
    //     0xc00868: add             x1, PP, #0x52, lsl #12  ; [pp+0x52b80] AnonymousClosure: (0xc00b94), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xc0059c)
    //     0xc0086c: ldr             x1, [x1, #0xb80]
    // 0xc00870: stur            x0, [fp, #-0x20]
    // 0xc00874: r0 = AllocateClosure()
    //     0xc00874: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00878: stur            x0, [fp, #-0x40]
    // 0xc0087c: r0 = PageView()
    //     0xc0087c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc00880: stur            x0, [fp, #-0x48]
    // 0xc00884: r16 = Instance_BouncingScrollPhysics
    //     0xc00884: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xc00888: ldr             x16, [x16, #0x890]
    // 0xc0088c: ldur            lr, [fp, #-0x30]
    // 0xc00890: stp             lr, x16, [SP]
    // 0xc00894: mov             x1, x0
    // 0xc00898: ldur            x2, [fp, #-0x40]
    // 0xc0089c: ldur            x3, [fp, #-0x38]
    // 0xc008a0: ldur            x5, [fp, #-0x20]
    // 0xc008a4: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xc008a4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xc008a8: ldr             x4, [x4, #0xe40]
    // 0xc008ac: r0 = PageView.builder()
    //     0xc008ac: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc008b0: r0 = AspectRatio()
    //     0xc008b0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xc008b4: d0 = 1.000000
    //     0xc008b4: fmov            d0, #1.00000000
    // 0xc008b8: stur            x0, [fp, #-0x20]
    // 0xc008bc: StoreField: r0->field_f = d0
    //     0xc008bc: stur            d0, [x0, #0xf]
    // 0xc008c0: ldur            x1, [fp, #-0x48]
    // 0xc008c4: StoreField: r0->field_b = r1
    //     0xc008c4: stur            w1, [x0, #0xb]
    // 0xc008c8: ldur            x2, [fp, #-0x28]
    // 0xc008cc: LoadField: r1 = r2->field_b
    //     0xc008cc: ldur            w1, [x2, #0xb]
    // 0xc008d0: LoadField: r3 = r2->field_f
    //     0xc008d0: ldur            w3, [x2, #0xf]
    // 0xc008d4: DecompressPointer r3
    //     0xc008d4: add             x3, x3, HEAP, lsl #32
    // 0xc008d8: LoadField: r4 = r3->field_b
    //     0xc008d8: ldur            w4, [x3, #0xb]
    // 0xc008dc: r3 = LoadInt32Instr(r1)
    //     0xc008dc: sbfx            x3, x1, #1, #0x1f
    // 0xc008e0: stur            x3, [fp, #-0x50]
    // 0xc008e4: r1 = LoadInt32Instr(r4)
    //     0xc008e4: sbfx            x1, x4, #1, #0x1f
    // 0xc008e8: cmp             x3, x1
    // 0xc008ec: b.ne            #0xc008f8
    // 0xc008f0: mov             x1, x2
    // 0xc008f4: r0 = _growToNextCapacity()
    //     0xc008f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc008f8: ldur            x4, [fp, #-8]
    // 0xc008fc: ldur            x2, [fp, #-0x28]
    // 0xc00900: ldur            x3, [fp, #-0x50]
    // 0xc00904: add             x0, x3, #1
    // 0xc00908: lsl             x1, x0, #1
    // 0xc0090c: StoreField: r2->field_b = r1
    //     0xc0090c: stur            w1, [x2, #0xb]
    // 0xc00910: LoadField: r1 = r2->field_f
    //     0xc00910: ldur            w1, [x2, #0xf]
    // 0xc00914: DecompressPointer r1
    //     0xc00914: add             x1, x1, HEAP, lsl #32
    // 0xc00918: ldur            x0, [fp, #-0x20]
    // 0xc0091c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0091c: add             x25, x1, x3, lsl #2
    //     0xc00920: add             x25, x25, #0xf
    //     0xc00924: str             w0, [x25]
    //     0xc00928: tbz             w0, #0, #0xc00944
    //     0xc0092c: ldurb           w16, [x1, #-1]
    //     0xc00930: ldurb           w17, [x0, #-1]
    //     0xc00934: and             x16, x17, x16, lsr #2
    //     0xc00938: tst             x16, HEAP, lsr #32
    //     0xc0093c: b.eq            #0xc00944
    //     0xc00940: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc00944: LoadField: r0 = r4->field_b
    //     0xc00944: ldur            w0, [x4, #0xb]
    // 0xc00948: DecompressPointer r0
    //     0xc00948: add             x0, x0, HEAP, lsl #32
    // 0xc0094c: cmp             w0, NULL
    // 0xc00950: b.eq            #0xc00b90
    // 0xc00954: LoadField: r1 = r0->field_b
    //     0xc00954: ldur            w1, [x0, #0xb]
    // 0xc00958: DecompressPointer r1
    //     0xc00958: add             x1, x1, HEAP, lsl #32
    // 0xc0095c: LoadField: r0 = r1->field_b
    //     0xc0095c: ldur            w0, [x1, #0xb]
    // 0xc00960: r3 = LoadInt32Instr(r0)
    //     0xc00960: sbfx            x3, x0, #1, #0x1f
    // 0xc00964: stur            x3, [fp, #-0x58]
    // 0xc00968: cmp             x3, #1
    // 0xc0096c: b.le            #0xc00aec
    // 0xc00970: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xc00970: ldur            x0, [x4, #0x17]
    // 0xc00974: ldur            x1, [fp, #-0x10]
    // 0xc00978: stur            x0, [fp, #-0x50]
    // 0xc0097c: r0 = of()
    //     0xc0097c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc00980: LoadField: r1 = r0->field_5b
    //     0xc00980: ldur            w1, [x0, #0x5b]
    // 0xc00984: DecompressPointer r1
    //     0xc00984: add             x1, x1, HEAP, lsl #32
    // 0xc00988: stur            x1, [fp, #-8]
    // 0xc0098c: r0 = CarouselIndicator()
    //     0xc0098c: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xc00990: mov             x3, x0
    // 0xc00994: ldur            x0, [fp, #-0x58]
    // 0xc00998: stur            x3, [fp, #-0x10]
    // 0xc0099c: StoreField: r3->field_b = r0
    //     0xc0099c: stur            x0, [x3, #0xb]
    // 0xc009a0: ldur            x0, [fp, #-0x50]
    // 0xc009a4: StoreField: r3->field_13 = r0
    //     0xc009a4: stur            x0, [x3, #0x13]
    // 0xc009a8: ldur            x0, [fp, #-8]
    // 0xc009ac: StoreField: r3->field_1b = r0
    //     0xc009ac: stur            w0, [x3, #0x1b]
    // 0xc009b0: r0 = Instance_Color
    //     0xc009b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xc009b4: ldr             x0, [x0, #0x90]
    // 0xc009b8: StoreField: r3->field_1f = r0
    //     0xc009b8: stur            w0, [x3, #0x1f]
    // 0xc009bc: r1 = Null
    //     0xc009bc: mov             x1, NULL
    // 0xc009c0: r2 = 2
    //     0xc009c0: movz            x2, #0x2
    // 0xc009c4: r0 = AllocateArray()
    //     0xc009c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc009c8: mov             x2, x0
    // 0xc009cc: ldur            x0, [fp, #-0x10]
    // 0xc009d0: stur            x2, [fp, #-8]
    // 0xc009d4: StoreField: r2->field_f = r0
    //     0xc009d4: stur            w0, [x2, #0xf]
    // 0xc009d8: r1 = <Widget>
    //     0xc009d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc009dc: r0 = AllocateGrowableArray()
    //     0xc009dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc009e0: mov             x1, x0
    // 0xc009e4: ldur            x0, [fp, #-8]
    // 0xc009e8: stur            x1, [fp, #-0x10]
    // 0xc009ec: StoreField: r1->field_f = r0
    //     0xc009ec: stur            w0, [x1, #0xf]
    // 0xc009f0: r0 = 2
    //     0xc009f0: movz            x0, #0x2
    // 0xc009f4: StoreField: r1->field_b = r0
    //     0xc009f4: stur            w0, [x1, #0xb]
    // 0xc009f8: r0 = Row()
    //     0xc009f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc009fc: mov             x1, x0
    // 0xc00a00: r0 = Instance_Axis
    //     0xc00a00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc00a04: stur            x1, [fp, #-8]
    // 0xc00a08: StoreField: r1->field_f = r0
    //     0xc00a08: stur            w0, [x1, #0xf]
    // 0xc00a0c: r0 = Instance_MainAxisAlignment
    //     0xc00a0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc00a10: ldr             x0, [x0, #0xab0]
    // 0xc00a14: StoreField: r1->field_13 = r0
    //     0xc00a14: stur            w0, [x1, #0x13]
    // 0xc00a18: r0 = Instance_MainAxisSize
    //     0xc00a18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc00a1c: ldr             x0, [x0, #0xa10]
    // 0xc00a20: ArrayStore: r1[0] = r0  ; List_4
    //     0xc00a20: stur            w0, [x1, #0x17]
    // 0xc00a24: r0 = Instance_CrossAxisAlignment
    //     0xc00a24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc00a28: ldr             x0, [x0, #0xa18]
    // 0xc00a2c: StoreField: r1->field_1b = r0
    //     0xc00a2c: stur            w0, [x1, #0x1b]
    // 0xc00a30: r0 = Instance_VerticalDirection
    //     0xc00a30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc00a34: ldr             x0, [x0, #0xa20]
    // 0xc00a38: StoreField: r1->field_23 = r0
    //     0xc00a38: stur            w0, [x1, #0x23]
    // 0xc00a3c: r2 = Instance_Clip
    //     0xc00a3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc00a40: ldr             x2, [x2, #0x38]
    // 0xc00a44: StoreField: r1->field_2b = r2
    //     0xc00a44: stur            w2, [x1, #0x2b]
    // 0xc00a48: StoreField: r1->field_2f = rZR
    //     0xc00a48: stur            xzr, [x1, #0x2f]
    // 0xc00a4c: ldur            x3, [fp, #-0x10]
    // 0xc00a50: StoreField: r1->field_b = r3
    //     0xc00a50: stur            w3, [x1, #0xb]
    // 0xc00a54: r0 = Padding()
    //     0xc00a54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc00a58: mov             x2, x0
    // 0xc00a5c: r0 = Instance_EdgeInsets
    //     0xc00a5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc00a60: ldr             x0, [x0, #0xa00]
    // 0xc00a64: stur            x2, [fp, #-0x10]
    // 0xc00a68: StoreField: r2->field_f = r0
    //     0xc00a68: stur            w0, [x2, #0xf]
    // 0xc00a6c: ldur            x0, [fp, #-8]
    // 0xc00a70: StoreField: r2->field_b = r0
    //     0xc00a70: stur            w0, [x2, #0xb]
    // 0xc00a74: ldur            x0, [fp, #-0x28]
    // 0xc00a78: LoadField: r1 = r0->field_b
    //     0xc00a78: ldur            w1, [x0, #0xb]
    // 0xc00a7c: LoadField: r3 = r0->field_f
    //     0xc00a7c: ldur            w3, [x0, #0xf]
    // 0xc00a80: DecompressPointer r3
    //     0xc00a80: add             x3, x3, HEAP, lsl #32
    // 0xc00a84: LoadField: r4 = r3->field_b
    //     0xc00a84: ldur            w4, [x3, #0xb]
    // 0xc00a88: r3 = LoadInt32Instr(r1)
    //     0xc00a88: sbfx            x3, x1, #1, #0x1f
    // 0xc00a8c: stur            x3, [fp, #-0x50]
    // 0xc00a90: r1 = LoadInt32Instr(r4)
    //     0xc00a90: sbfx            x1, x4, #1, #0x1f
    // 0xc00a94: cmp             x3, x1
    // 0xc00a98: b.ne            #0xc00aa4
    // 0xc00a9c: mov             x1, x0
    // 0xc00aa0: r0 = _growToNextCapacity()
    //     0xc00aa0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc00aa4: ldur            x2, [fp, #-0x28]
    // 0xc00aa8: ldur            x3, [fp, #-0x50]
    // 0xc00aac: add             x0, x3, #1
    // 0xc00ab0: lsl             x1, x0, #1
    // 0xc00ab4: StoreField: r2->field_b = r1
    //     0xc00ab4: stur            w1, [x2, #0xb]
    // 0xc00ab8: LoadField: r1 = r2->field_f
    //     0xc00ab8: ldur            w1, [x2, #0xf]
    // 0xc00abc: DecompressPointer r1
    //     0xc00abc: add             x1, x1, HEAP, lsl #32
    // 0xc00ac0: ldur            x0, [fp, #-0x10]
    // 0xc00ac4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc00ac4: add             x25, x1, x3, lsl #2
    //     0xc00ac8: add             x25, x25, #0xf
    //     0xc00acc: str             w0, [x25]
    //     0xc00ad0: tbz             w0, #0, #0xc00aec
    //     0xc00ad4: ldurb           w16, [x1, #-1]
    //     0xc00ad8: ldurb           w17, [x0, #-1]
    //     0xc00adc: and             x16, x17, x16, lsr #2
    //     0xc00ae0: tst             x16, HEAP, lsr #32
    //     0xc00ae4: b.eq            #0xc00aec
    //     0xc00ae8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc00aec: ldur            x0, [fp, #-0x18]
    // 0xc00af0: r0 = Column()
    //     0xc00af0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc00af4: mov             x1, x0
    // 0xc00af8: r0 = Instance_Axis
    //     0xc00af8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc00afc: stur            x1, [fp, #-8]
    // 0xc00b00: StoreField: r1->field_f = r0
    //     0xc00b00: stur            w0, [x1, #0xf]
    // 0xc00b04: r0 = Instance_MainAxisAlignment
    //     0xc00b04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc00b08: ldr             x0, [x0, #0xa08]
    // 0xc00b0c: StoreField: r1->field_13 = r0
    //     0xc00b0c: stur            w0, [x1, #0x13]
    // 0xc00b10: r0 = Instance_MainAxisSize
    //     0xc00b10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc00b14: ldr             x0, [x0, #0xdd0]
    // 0xc00b18: ArrayStore: r1[0] = r0  ; List_4
    //     0xc00b18: stur            w0, [x1, #0x17]
    // 0xc00b1c: ldur            x0, [fp, #-0x18]
    // 0xc00b20: StoreField: r1->field_1b = r0
    //     0xc00b20: stur            w0, [x1, #0x1b]
    // 0xc00b24: r0 = Instance_VerticalDirection
    //     0xc00b24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc00b28: ldr             x0, [x0, #0xa20]
    // 0xc00b2c: StoreField: r1->field_23 = r0
    //     0xc00b2c: stur            w0, [x1, #0x23]
    // 0xc00b30: r0 = Instance_Clip
    //     0xc00b30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc00b34: ldr             x0, [x0, #0x38]
    // 0xc00b38: StoreField: r1->field_2b = r0
    //     0xc00b38: stur            w0, [x1, #0x2b]
    // 0xc00b3c: StoreField: r1->field_2f = rZR
    //     0xc00b3c: stur            xzr, [x1, #0x2f]
    // 0xc00b40: ldur            x0, [fp, #-0x28]
    // 0xc00b44: StoreField: r1->field_b = r0
    //     0xc00b44: stur            w0, [x1, #0xb]
    // 0xc00b48: r0 = Padding()
    //     0xc00b48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc00b4c: r1 = Instance_EdgeInsets
    //     0xc00b4c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xc00b50: ldr             x1, [x1, #0x110]
    // 0xc00b54: StoreField: r0->field_f = r1
    //     0xc00b54: stur            w1, [x0, #0xf]
    // 0xc00b58: ldur            x1, [fp, #-8]
    // 0xc00b5c: StoreField: r0->field_b = r1
    //     0xc00b5c: stur            w1, [x0, #0xb]
    // 0xc00b60: LeaveFrame
    //     0xc00b60: mov             SP, fp
    //     0xc00b64: ldp             fp, lr, [SP], #0x10
    // 0xc00b68: ret
    //     0xc00b68: ret             
    // 0xc00b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc00b6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00b70: b               #0xc005c4
    // 0xc00b74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00b74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00b78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00b78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00b7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00b7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00b80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00b80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc00b84: r9 = _pageController
    //     0xc00b84: add             x9, PP, #0x52, lsl #12  ; [pp+0x52b88] Field <_ProductCollectionPosterCarouselState@1735483010._pageController@1735483010>: late (offset: 0x14)
    //     0xc00b88: ldr             x9, [x9, #0xb88]
    // 0xc00b8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc00b8c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc00b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00b90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc00b94, size: 0x70
    // 0xc00b94: EnterFrame
    //     0xc00b94: stp             fp, lr, [SP, #-0x10]!
    //     0xc00b98: mov             fp, SP
    // 0xc00b9c: ldr             x0, [fp, #0x20]
    // 0xc00ba0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc00ba0: ldur            w1, [x0, #0x17]
    // 0xc00ba4: DecompressPointer r1
    //     0xc00ba4: add             x1, x1, HEAP, lsl #32
    // 0xc00ba8: CheckStackOverflow
    //     0xc00ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00bac: cmp             SP, x16
    //     0xc00bb0: b.ls            #0xc00bf8
    // 0xc00bb4: LoadField: r0 = r1->field_f
    //     0xc00bb4: ldur            w0, [x1, #0xf]
    // 0xc00bb8: DecompressPointer r0
    //     0xc00bb8: add             x0, x0, HEAP, lsl #32
    // 0xc00bbc: LoadField: r1 = r0->field_b
    //     0xc00bbc: ldur            w1, [x0, #0xb]
    // 0xc00bc0: DecompressPointer r1
    //     0xc00bc0: add             x1, x1, HEAP, lsl #32
    // 0xc00bc4: cmp             w1, NULL
    // 0xc00bc8: b.eq            #0xc00c00
    // 0xc00bcc: LoadField: r2 = r1->field_b
    //     0xc00bcc: ldur            w2, [x1, #0xb]
    // 0xc00bd0: DecompressPointer r2
    //     0xc00bd0: add             x2, x2, HEAP, lsl #32
    // 0xc00bd4: ldr             x1, [fp, #0x10]
    // 0xc00bd8: r3 = LoadInt32Instr(r1)
    //     0xc00bd8: sbfx            x3, x1, #1, #0x1f
    //     0xc00bdc: tbz             w1, #0, #0xc00be4
    //     0xc00be0: ldur            x3, [x1, #7]
    // 0xc00be4: mov             x1, x0
    // 0xc00be8: r0 = lineThemeSlider()
    //     0xc00be8: bl              #0xa7bec0  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::lineThemeSlider
    // 0xc00bec: LeaveFrame
    //     0xc00bec: mov             SP, fp
    //     0xc00bf0: ldp             fp, lr, [SP], #0x10
    // 0xc00bf4: ret
    //     0xc00bf4: ret             
    // 0xc00bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc00bf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00bfc: b               #0xc00bb4
    // 0xc00c00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc00c00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xc00c04, size: 0x84
    // 0xc00c04: EnterFrame
    //     0xc00c04: stp             fp, lr, [SP, #-0x10]!
    //     0xc00c08: mov             fp, SP
    // 0xc00c0c: AllocStack(0x10)
    //     0xc00c0c: sub             SP, SP, #0x10
    // 0xc00c10: SetupParameters()
    //     0xc00c10: ldr             x0, [fp, #0x18]
    //     0xc00c14: ldur            w1, [x0, #0x17]
    //     0xc00c18: add             x1, x1, HEAP, lsl #32
    //     0xc00c1c: stur            x1, [fp, #-8]
    // 0xc00c20: CheckStackOverflow
    //     0xc00c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc00c24: cmp             SP, x16
    //     0xc00c28: b.ls            #0xc00c80
    // 0xc00c2c: r1 = 1
    //     0xc00c2c: movz            x1, #0x1
    // 0xc00c30: r0 = AllocateContext()
    //     0xc00c30: bl              #0x16f6108  ; AllocateContextStub
    // 0xc00c34: mov             x1, x0
    // 0xc00c38: ldur            x0, [fp, #-8]
    // 0xc00c3c: StoreField: r1->field_b = r0
    //     0xc00c3c: stur            w0, [x1, #0xb]
    // 0xc00c40: ldr             x2, [fp, #0x10]
    // 0xc00c44: StoreField: r1->field_f = r2
    //     0xc00c44: stur            w2, [x1, #0xf]
    // 0xc00c48: LoadField: r3 = r0->field_f
    //     0xc00c48: ldur            w3, [x0, #0xf]
    // 0xc00c4c: DecompressPointer r3
    //     0xc00c4c: add             x3, x3, HEAP, lsl #32
    // 0xc00c50: mov             x2, x1
    // 0xc00c54: stur            x3, [fp, #-0x10]
    // 0xc00c58: r1 = Function '<anonymous closure>':.
    //     0xc00c58: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd0] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xc00c5c: ldr             x1, [x1, #0xbd0]
    // 0xc00c60: r0 = AllocateClosure()
    //     0xc00c60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc00c64: ldur            x1, [fp, #-0x10]
    // 0xc00c68: mov             x2, x0
    // 0xc00c6c: r0 = setState()
    //     0xc00c6c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc00c70: r0 = Null
    //     0xc00c70: mov             x0, NULL
    // 0xc00c74: LeaveFrame
    //     0xc00c74: mov             SP, fp
    //     0xc00c78: ldp             fp, lr, [SP], #0x10
    // 0xc00c7c: ret
    //     0xc00c7c: ret             
    // 0xc00c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc00c80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc00c84: b               #0xc00c2c
  }
}

// class id: 3974, size: 0x20, field offset: 0xc
//   const constructor, 
class ProductCollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80f50, size: 0x30
    // 0xc80f50: EnterFrame
    //     0xc80f50: stp             fp, lr, [SP, #-0x10]!
    //     0xc80f54: mov             fp, SP
    // 0xc80f58: mov             x0, x1
    // 0xc80f5c: r1 = <ProductCollectionPosterCarousel>
    //     0xc80f5c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48370] TypeArguments: <ProductCollectionPosterCarousel>
    //     0xc80f60: ldr             x1, [x1, #0x370]
    // 0xc80f64: r0 = _ProductCollectionPosterCarouselState()
    //     0xc80f64: bl              #0xc80f80  ; Allocate_ProductCollectionPosterCarouselStateStub -> _ProductCollectionPosterCarouselState (size=0x20)
    // 0xc80f68: r1 = Sentinel
    //     0xc80f68: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80f6c: StoreField: r0->field_13 = r1
    //     0xc80f6c: stur            w1, [x0, #0x13]
    // 0xc80f70: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc80f70: stur            xzr, [x0, #0x17]
    // 0xc80f74: LeaveFrame
    //     0xc80f74: mov             SP, fp
    //     0xc80f78: ldp             fp, lr, [SP], #0x10
    // 0xc80f7c: ret
    //     0xc80f7c: ret             
  }
}
