// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart

// class id: 1049521, size: 0x8
class :: {
}

// class id: 3250, size: 0x2c, field offset: 0x14
class _CategoryGroupCarouselItemViewState extends State<dynamic> {

  late double _viewportHeight; // offset: 0x28
  late PageController _pageLineController; // offset: 0x14
  late TextStyle _titleStyle; // offset: 0x24
  late double _imageSize; // offset: 0x20

  _ build(/* No info */) {
    // ** addr: 0xbde4f0, size: 0x37c
    // 0xbde4f0: EnterFrame
    //     0xbde4f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbde4f4: mov             fp, SP
    // 0xbde4f8: AllocStack(0x60)
    //     0xbde4f8: sub             SP, SP, #0x60
    // 0xbde4fc: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbde4fc: mov             x0, x1
    //     0xbde500: stur            x1, [fp, #-8]
    //     0xbde504: mov             x1, x2
    //     0xbde508: stur            x2, [fp, #-0x10]
    // 0xbde50c: CheckStackOverflow
    //     0xbde50c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbde510: cmp             SP, x16
    //     0xbde514: b.ls            #0xbde814
    // 0xbde518: r1 = 1
    //     0xbde518: movz            x1, #0x1
    // 0xbde51c: r0 = AllocateContext()
    //     0xbde51c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbde520: mov             x3, x0
    // 0xbde524: ldur            x0, [fp, #-8]
    // 0xbde528: stur            x3, [fp, #-0x38]
    // 0xbde52c: StoreField: r3->field_f = r0
    //     0xbde52c: stur            w0, [x3, #0xf]
    // 0xbde530: LoadField: r1 = r0->field_b
    //     0xbde530: ldur            w1, [x0, #0xb]
    // 0xbde534: DecompressPointer r1
    //     0xbde534: add             x1, x1, HEAP, lsl #32
    // 0xbde538: cmp             w1, NULL
    // 0xbde53c: b.eq            #0xbde81c
    // 0xbde540: LoadField: r2 = r1->field_b
    //     0xbde540: ldur            w2, [x1, #0xb]
    // 0xbde544: DecompressPointer r2
    //     0xbde544: add             x2, x2, HEAP, lsl #32
    // 0xbde548: cmp             w2, NULL
    // 0xbde54c: b.ne            #0xbde558
    // 0xbde550: r1 = Null
    //     0xbde550: mov             x1, NULL
    // 0xbde554: b               #0xbde55c
    // 0xbde558: LoadField: r1 = r2->field_b
    //     0xbde558: ldur            w1, [x2, #0xb]
    // 0xbde55c: cmp             w1, NULL
    // 0xbde560: b.ne            #0xbde56c
    // 0xbde564: r4 = 0
    //     0xbde564: movz            x4, #0
    // 0xbde568: b               #0xbde574
    // 0xbde56c: r2 = LoadInt32Instr(r1)
    //     0xbde56c: sbfx            x2, x1, #1, #0x1f
    // 0xbde570: mov             x4, x2
    // 0xbde574: d0 = 4.000000
    //     0xbde574: fmov            d0, #4.00000000
    // 0xbde578: stur            x4, [fp, #-0x30]
    // 0xbde57c: scvtf           d1, x4
    // 0xbde580: fdiv            d2, d1, d0
    // 0xbde584: fcmp            d2, d2
    // 0xbde588: b.vs            #0xbde820
    // 0xbde58c: fcvtps          x5, d2
    // 0xbde590: asr             x16, x5, #0x1e
    // 0xbde594: cmp             x16, x5, asr #63
    // 0xbde598: b.ne            #0xbde820
    // 0xbde59c: lsl             x5, x5, #1
    // 0xbde5a0: stur            x5, [fp, #-0x28]
    // 0xbde5a4: LoadField: r6 = r0->field_27
    //     0xbde5a4: ldur            w6, [x0, #0x27]
    // 0xbde5a8: DecompressPointer r6
    //     0xbde5a8: add             x6, x6, HEAP, lsl #32
    // 0xbde5ac: r16 = Sentinel
    //     0xbde5ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbde5b0: cmp             w6, w16
    // 0xbde5b4: b.eq            #0xbde854
    // 0xbde5b8: stur            x6, [fp, #-0x20]
    // 0xbde5bc: LoadField: r7 = r0->field_13
    //     0xbde5bc: ldur            w7, [x0, #0x13]
    // 0xbde5c0: DecompressPointer r7
    //     0xbde5c0: add             x7, x7, HEAP, lsl #32
    // 0xbde5c4: r16 = Sentinel
    //     0xbde5c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbde5c8: cmp             w7, w16
    // 0xbde5cc: b.eq            #0xbde860
    // 0xbde5d0: mov             x2, x3
    // 0xbde5d4: stur            x7, [fp, #-0x18]
    // 0xbde5d8: r1 = Function '<anonymous closure>':.
    //     0xbde5d8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53868] AnonymousClosure: (0xbdefb8), in [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xbde4f0)
    //     0xbde5dc: ldr             x1, [x1, #0x868]
    // 0xbde5e0: r0 = AllocateClosure()
    //     0xbde5e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbde5e4: ldur            x2, [fp, #-0x38]
    // 0xbde5e8: r1 = Function '<anonymous closure>':.
    //     0xbde5e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53870] AnonymousClosure: (0xbde86c), in [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::build (0xbde4f0)
    //     0xbde5ec: ldr             x1, [x1, #0x870]
    // 0xbde5f0: stur            x0, [fp, #-0x38]
    // 0xbde5f4: r0 = AllocateClosure()
    //     0xbde5f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbde5f8: stur            x0, [fp, #-0x40]
    // 0xbde5fc: r0 = PageView()
    //     0xbde5fc: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbde600: stur            x0, [fp, #-0x48]
    // 0xbde604: r16 = Instance_BouncingScrollPhysics
    //     0xbde604: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbde608: ldr             x16, [x16, #0x890]
    // 0xbde60c: ldur            lr, [fp, #-0x18]
    // 0xbde610: stp             lr, x16, [SP]
    // 0xbde614: mov             x1, x0
    // 0xbde618: ldur            x2, [fp, #-0x40]
    // 0xbde61c: ldur            x3, [fp, #-0x28]
    // 0xbde620: ldur            x5, [fp, #-0x38]
    // 0xbde624: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xbde624: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xbde628: ldr             x4, [x4, #0xe40]
    // 0xbde62c: r0 = PageView.builder()
    //     0xbde62c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbde630: r0 = SizedBox()
    //     0xbde630: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbde634: mov             x3, x0
    // 0xbde638: ldur            x0, [fp, #-0x20]
    // 0xbde63c: stur            x3, [fp, #-0x18]
    // 0xbde640: StoreField: r3->field_13 = r0
    //     0xbde640: stur            w0, [x3, #0x13]
    // 0xbde644: ldur            x0, [fp, #-0x48]
    // 0xbde648: StoreField: r3->field_b = r0
    //     0xbde648: stur            w0, [x3, #0xb]
    // 0xbde64c: r1 = Null
    //     0xbde64c: mov             x1, NULL
    // 0xbde650: r2 = 2
    //     0xbde650: movz            x2, #0x2
    // 0xbde654: r0 = AllocateArray()
    //     0xbde654: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbde658: mov             x2, x0
    // 0xbde65c: ldur            x0, [fp, #-0x18]
    // 0xbde660: stur            x2, [fp, #-0x20]
    // 0xbde664: StoreField: r2->field_f = r0
    //     0xbde664: stur            w0, [x2, #0xf]
    // 0xbde668: r1 = <Widget>
    //     0xbde668: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbde66c: r0 = AllocateGrowableArray()
    //     0xbde66c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbde670: mov             x2, x0
    // 0xbde674: ldur            x0, [fp, #-0x20]
    // 0xbde678: stur            x2, [fp, #-0x18]
    // 0xbde67c: StoreField: r2->field_f = r0
    //     0xbde67c: stur            w0, [x2, #0xf]
    // 0xbde680: r0 = 2
    //     0xbde680: movz            x0, #0x2
    // 0xbde684: StoreField: r2->field_b = r0
    //     0xbde684: stur            w0, [x2, #0xb]
    // 0xbde688: ldur            x0, [fp, #-0x30]
    // 0xbde68c: cmp             x0, #4
    // 0xbde690: b.le            #0xbde794
    // 0xbde694: ldur            x0, [fp, #-8]
    // 0xbde698: ldur            x3, [fp, #-0x28]
    // 0xbde69c: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xbde69c: ldur            x4, [x0, #0x17]
    // 0xbde6a0: ldur            x1, [fp, #-0x10]
    // 0xbde6a4: stur            x4, [fp, #-0x30]
    // 0xbde6a8: r0 = of()
    //     0xbde6a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbde6ac: LoadField: r1 = r0->field_5b
    //     0xbde6ac: ldur            w1, [x0, #0x5b]
    // 0xbde6b0: DecompressPointer r1
    //     0xbde6b0: add             x1, x1, HEAP, lsl #32
    // 0xbde6b4: ldur            x0, [fp, #-0x28]
    // 0xbde6b8: stur            x1, [fp, #-8]
    // 0xbde6bc: r2 = LoadInt32Instr(r0)
    //     0xbde6bc: sbfx            x2, x0, #1, #0x1f
    //     0xbde6c0: tbz             w0, #0, #0xbde6c8
    //     0xbde6c4: ldur            x2, [x0, #7]
    // 0xbde6c8: stur            x2, [fp, #-0x50]
    // 0xbde6cc: r0 = CarouselIndicator()
    //     0xbde6cc: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbde6d0: mov             x1, x0
    // 0xbde6d4: ldur            x0, [fp, #-0x50]
    // 0xbde6d8: stur            x1, [fp, #-0x10]
    // 0xbde6dc: StoreField: r1->field_b = r0
    //     0xbde6dc: stur            x0, [x1, #0xb]
    // 0xbde6e0: ldur            x0, [fp, #-0x30]
    // 0xbde6e4: StoreField: r1->field_13 = r0
    //     0xbde6e4: stur            x0, [x1, #0x13]
    // 0xbde6e8: ldur            x0, [fp, #-8]
    // 0xbde6ec: StoreField: r1->field_1b = r0
    //     0xbde6ec: stur            w0, [x1, #0x1b]
    // 0xbde6f0: r0 = Instance_Color
    //     0xbde6f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbde6f4: ldr             x0, [x0, #0x90]
    // 0xbde6f8: StoreField: r1->field_1f = r0
    //     0xbde6f8: stur            w0, [x1, #0x1f]
    // 0xbde6fc: r0 = Padding()
    //     0xbde6fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbde700: mov             x2, x0
    // 0xbde704: r0 = Instance_EdgeInsets
    //     0xbde704: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbde708: ldr             x0, [x0, #0x990]
    // 0xbde70c: stur            x2, [fp, #-8]
    // 0xbde710: StoreField: r2->field_f = r0
    //     0xbde710: stur            w0, [x2, #0xf]
    // 0xbde714: ldur            x0, [fp, #-0x10]
    // 0xbde718: StoreField: r2->field_b = r0
    //     0xbde718: stur            w0, [x2, #0xb]
    // 0xbde71c: ldur            x0, [fp, #-0x18]
    // 0xbde720: LoadField: r1 = r0->field_b
    //     0xbde720: ldur            w1, [x0, #0xb]
    // 0xbde724: LoadField: r3 = r0->field_f
    //     0xbde724: ldur            w3, [x0, #0xf]
    // 0xbde728: DecompressPointer r3
    //     0xbde728: add             x3, x3, HEAP, lsl #32
    // 0xbde72c: LoadField: r4 = r3->field_b
    //     0xbde72c: ldur            w4, [x3, #0xb]
    // 0xbde730: r3 = LoadInt32Instr(r1)
    //     0xbde730: sbfx            x3, x1, #1, #0x1f
    // 0xbde734: stur            x3, [fp, #-0x30]
    // 0xbde738: r1 = LoadInt32Instr(r4)
    //     0xbde738: sbfx            x1, x4, #1, #0x1f
    // 0xbde73c: cmp             x3, x1
    // 0xbde740: b.ne            #0xbde74c
    // 0xbde744: mov             x1, x0
    // 0xbde748: r0 = _growToNextCapacity()
    //     0xbde748: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbde74c: ldur            x2, [fp, #-0x18]
    // 0xbde750: ldur            x3, [fp, #-0x30]
    // 0xbde754: add             x0, x3, #1
    // 0xbde758: lsl             x1, x0, #1
    // 0xbde75c: StoreField: r2->field_b = r1
    //     0xbde75c: stur            w1, [x2, #0xb]
    // 0xbde760: LoadField: r1 = r2->field_f
    //     0xbde760: ldur            w1, [x2, #0xf]
    // 0xbde764: DecompressPointer r1
    //     0xbde764: add             x1, x1, HEAP, lsl #32
    // 0xbde768: ldur            x0, [fp, #-8]
    // 0xbde76c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbde76c: add             x25, x1, x3, lsl #2
    //     0xbde770: add             x25, x25, #0xf
    //     0xbde774: str             w0, [x25]
    //     0xbde778: tbz             w0, #0, #0xbde794
    //     0xbde77c: ldurb           w16, [x1, #-1]
    //     0xbde780: ldurb           w17, [x0, #-1]
    //     0xbde784: and             x16, x17, x16, lsr #2
    //     0xbde788: tst             x16, HEAP, lsr #32
    //     0xbde78c: b.eq            #0xbde794
    //     0xbde790: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbde794: r0 = Column()
    //     0xbde794: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbde798: mov             x1, x0
    // 0xbde79c: r0 = Instance_Axis
    //     0xbde79c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbde7a0: stur            x1, [fp, #-8]
    // 0xbde7a4: StoreField: r1->field_f = r0
    //     0xbde7a4: stur            w0, [x1, #0xf]
    // 0xbde7a8: r0 = Instance_MainAxisAlignment
    //     0xbde7a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbde7ac: ldr             x0, [x0, #0xa08]
    // 0xbde7b0: StoreField: r1->field_13 = r0
    //     0xbde7b0: stur            w0, [x1, #0x13]
    // 0xbde7b4: r0 = Instance_MainAxisSize
    //     0xbde7b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbde7b8: ldr             x0, [x0, #0xa10]
    // 0xbde7bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbde7bc: stur            w0, [x1, #0x17]
    // 0xbde7c0: r0 = Instance_CrossAxisAlignment
    //     0xbde7c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbde7c4: ldr             x0, [x0, #0xa18]
    // 0xbde7c8: StoreField: r1->field_1b = r0
    //     0xbde7c8: stur            w0, [x1, #0x1b]
    // 0xbde7cc: r0 = Instance_VerticalDirection
    //     0xbde7cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbde7d0: ldr             x0, [x0, #0xa20]
    // 0xbde7d4: StoreField: r1->field_23 = r0
    //     0xbde7d4: stur            w0, [x1, #0x23]
    // 0xbde7d8: r0 = Instance_Clip
    //     0xbde7d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbde7dc: ldr             x0, [x0, #0x38]
    // 0xbde7e0: StoreField: r1->field_2b = r0
    //     0xbde7e0: stur            w0, [x1, #0x2b]
    // 0xbde7e4: StoreField: r1->field_2f = rZR
    //     0xbde7e4: stur            xzr, [x1, #0x2f]
    // 0xbde7e8: ldur            x0, [fp, #-0x18]
    // 0xbde7ec: StoreField: r1->field_b = r0
    //     0xbde7ec: stur            w0, [x1, #0xb]
    // 0xbde7f0: r0 = Padding()
    //     0xbde7f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbde7f4: r1 = Instance_EdgeInsets
    //     0xbde7f4: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbde7f8: ldr             x1, [x1, #0x110]
    // 0xbde7fc: StoreField: r0->field_f = r1
    //     0xbde7fc: stur            w1, [x0, #0xf]
    // 0xbde800: ldur            x1, [fp, #-8]
    // 0xbde804: StoreField: r0->field_b = r1
    //     0xbde804: stur            w1, [x0, #0xb]
    // 0xbde808: LeaveFrame
    //     0xbde808: mov             SP, fp
    //     0xbde80c: ldp             fp, lr, [SP], #0x10
    // 0xbde810: ret
    //     0xbde810: ret             
    // 0xbde814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbde814: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbde818: b               #0xbde518
    // 0xbde81c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbde81c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbde820: SaveReg d2
    //     0xbde820: str             q2, [SP, #-0x10]!
    // 0xbde824: stp             x3, x4, [SP, #-0x10]!
    // 0xbde828: SaveReg r0
    //     0xbde828: str             x0, [SP, #-8]!
    // 0xbde82c: d0 = 0.000000
    //     0xbde82c: fmov            d0, d2
    // 0xbde830: r0 = 64
    //     0xbde830: movz            x0, #0x40
    // 0xbde834: r30 = DoubleToIntegerStub
    //     0xbde834: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xbde838: LoadField: r30 = r30->field_7
    //     0xbde838: ldur            lr, [lr, #7]
    // 0xbde83c: blr             lr
    // 0xbde840: mov             x5, x0
    // 0xbde844: RestoreReg r0
    //     0xbde844: ldr             x0, [SP], #8
    // 0xbde848: ldp             x3, x4, [SP], #0x10
    // 0xbde84c: RestoreReg d2
    //     0xbde84c: ldr             q2, [SP], #0x10
    // 0xbde850: b               #0xbde5a0
    // 0xbde854: r9 = _viewportHeight
    //     0xbde854: add             x9, PP, #0x53, lsl #12  ; [pp+0x53878] Field <_CategoryGroupCarouselItemViewState@1703444197._viewportHeight@1703444197>: late (offset: 0x28)
    //     0xbde858: ldr             x9, [x9, #0x878]
    // 0xbde85c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbde85c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbde860: r9 = _pageLineController
    //     0xbde860: add             x9, PP, #0x53, lsl #12  ; [pp+0x53880] Field <_CategoryGroupCarouselItemViewState@1703444197._pageLineController@1703444197>: late (offset: 0x14)
    //     0xbde864: ldr             x9, [x9, #0x880]
    // 0xbde868: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbde868: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] RepaintBoundary <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbde86c, size: 0x68
    // 0xbde86c: EnterFrame
    //     0xbde86c: stp             fp, lr, [SP, #-0x10]!
    //     0xbde870: mov             fp, SP
    // 0xbde874: AllocStack(0x8)
    //     0xbde874: sub             SP, SP, #8
    // 0xbde878: SetupParameters()
    //     0xbde878: ldr             x0, [fp, #0x20]
    //     0xbde87c: ldur            w1, [x0, #0x17]
    //     0xbde880: add             x1, x1, HEAP, lsl #32
    // 0xbde884: CheckStackOverflow
    //     0xbde884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbde888: cmp             SP, x16
    //     0xbde88c: b.ls            #0xbde8cc
    // 0xbde890: LoadField: r0 = r1->field_f
    //     0xbde890: ldur            w0, [x1, #0xf]
    // 0xbde894: DecompressPointer r0
    //     0xbde894: add             x0, x0, HEAP, lsl #32
    // 0xbde898: ldr             x1, [fp, #0x10]
    // 0xbde89c: r2 = LoadInt32Instr(r1)
    //     0xbde89c: sbfx            x2, x1, #1, #0x1f
    //     0xbde8a0: tbz             w1, #0, #0xbde8a8
    //     0xbde8a4: ldur            x2, [x1, #7]
    // 0xbde8a8: mov             x1, x0
    // 0xbde8ac: r0 = _buildCarouselPage()
    //     0xbde8ac: bl              #0xbde8d4  ; [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage
    // 0xbde8b0: stur            x0, [fp, #-8]
    // 0xbde8b4: r0 = RepaintBoundary()
    //     0xbde8b4: bl              #0xa4360c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0xbde8b8: ldur            x1, [fp, #-8]
    // 0xbde8bc: StoreField: r0->field_b = r1
    //     0xbde8bc: stur            w1, [x0, #0xb]
    // 0xbde8c0: LeaveFrame
    //     0xbde8c0: mov             SP, fp
    //     0xbde8c4: ldp             fp, lr, [SP], #0x10
    // 0xbde8c8: ret
    //     0xbde8c8: ret             
    // 0xbde8cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbde8cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbde8d0: b               #0xbde890
  }
  _ _buildCarouselPage(/* No info */) {
    // ** addr: 0xbde8d4, size: 0x1c0
    // 0xbde8d4: EnterFrame
    //     0xbde8d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbde8d8: mov             fp, SP
    // 0xbde8dc: AllocStack(0x38)
    //     0xbde8dc: sub             SP, SP, #0x38
    // 0xbde8e0: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbde8e0: stur            x1, [fp, #-8]
    //     0xbde8e4: stur            x2, [fp, #-0x10]
    // 0xbde8e8: CheckStackOverflow
    //     0xbde8e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbde8ec: cmp             SP, x16
    //     0xbde8f0: b.ls            #0xbdea84
    // 0xbde8f4: r1 = 1
    //     0xbde8f4: movz            x1, #0x1
    // 0xbde8f8: r0 = AllocateContext()
    //     0xbde8f8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbde8fc: mov             x5, x0
    // 0xbde900: ldur            x4, [fp, #-8]
    // 0xbde904: stur            x5, [fp, #-0x20]
    // 0xbde908: StoreField: r5->field_f = r4
    //     0xbde908: stur            w4, [x5, #0xf]
    // 0xbde90c: ldur            x0, [fp, #-0x10]
    // 0xbde910: lsl             x6, x0, #2
    // 0xbde914: stur            x6, [fp, #-0x18]
    // 0xbde918: add             x2, x6, #4
    // 0xbde91c: LoadField: r0 = r4->field_b
    //     0xbde91c: ldur            w0, [x4, #0xb]
    // 0xbde920: DecompressPointer r0
    //     0xbde920: add             x0, x0, HEAP, lsl #32
    // 0xbde924: cmp             w0, NULL
    // 0xbde928: b.eq            #0xbdea8c
    // 0xbde92c: LoadField: r1 = r0->field_b
    //     0xbde92c: ldur            w1, [x0, #0xb]
    // 0xbde930: DecompressPointer r1
    //     0xbde930: add             x1, x1, HEAP, lsl #32
    // 0xbde934: cmp             w1, NULL
    // 0xbde938: b.ne            #0xbde944
    // 0xbde93c: r0 = Null
    //     0xbde93c: mov             x0, NULL
    // 0xbde940: b               #0xbde948
    // 0xbde944: LoadField: r0 = r1->field_b
    //     0xbde944: ldur            w0, [x1, #0xb]
    // 0xbde948: cmp             w0, NULL
    // 0xbde94c: b.ne            #0xbde958
    // 0xbde950: r3 = 0
    //     0xbde950: movz            x3, #0
    // 0xbde954: b               #0xbde960
    // 0xbde958: r1 = LoadInt32Instr(r0)
    //     0xbde958: sbfx            x1, x0, #1, #0x1f
    // 0xbde95c: mov             x3, x1
    // 0xbde960: r0 = BoxInt64Instr(r2)
    //     0xbde960: sbfiz           x0, x2, #1, #0x1f
    //     0xbde964: cmp             x2, x0, asr #1
    //     0xbde968: b.eq            #0xbde974
    //     0xbde96c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbde970: stur            x2, [x0, #7]
    // 0xbde974: lsl             x1, x3, #1
    // 0xbde978: mov             x3, x1
    // 0xbde97c: mov             x1, x0
    // 0xbde980: r2 = 0
    //     0xbde980: movz            x2, #0
    // 0xbde984: r0 = clamp()
    //     0xbde984: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xbde988: mov             x1, x0
    // 0xbde98c: ldur            x0, [fp, #-8]
    // 0xbde990: LoadField: r2 = r0->field_b
    //     0xbde990: ldur            w2, [x0, #0xb]
    // 0xbde994: DecompressPointer r2
    //     0xbde994: add             x2, x2, HEAP, lsl #32
    // 0xbde998: cmp             w2, NULL
    // 0xbde99c: b.eq            #0xbdea90
    // 0xbde9a0: LoadField: r0 = r2->field_b
    //     0xbde9a0: ldur            w0, [x2, #0xb]
    // 0xbde9a4: DecompressPointer r0
    //     0xbde9a4: add             x0, x0, HEAP, lsl #32
    // 0xbde9a8: cmp             w0, NULL
    // 0xbde9ac: b.ne            #0xbde9b8
    // 0xbde9b0: r0 = Null
    //     0xbde9b0: mov             x0, NULL
    // 0xbde9b4: b               #0xbde9cc
    // 0xbde9b8: str             x1, [SP]
    // 0xbde9bc: mov             x1, x0
    // 0xbde9c0: ldur            x2, [fp, #-0x18]
    // 0xbde9c4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbde9c4: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbde9c8: r0 = sublist()
    //     0xbde9c8: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xbde9cc: cmp             w0, NULL
    // 0xbde9d0: b.ne            #0xbde9e4
    // 0xbde9d4: r1 = <Entity>
    //     0xbde9d4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xbde9d8: ldr             x1, [x1, #0xb68]
    // 0xbde9dc: r2 = 0
    //     0xbde9dc: movz            x2, #0
    // 0xbde9e0: r0 = _GrowableList()
    //     0xbde9e0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbde9e4: ldur            x2, [fp, #-0x20]
    // 0xbde9e8: stur            x0, [fp, #-8]
    // 0xbde9ec: r1 = Function '<anonymous closure>':.
    //     0xbde9ec: add             x1, PP, #0x53, lsl #12  ; [pp+0x53888] AnonymousClosure: (0xbdea94), in [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xbde8d4)
    //     0xbde9f0: ldr             x1, [x1, #0x888]
    // 0xbde9f4: r0 = AllocateClosure()
    //     0xbde9f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbde9f8: r16 = <GestureDetector>
    //     0xbde9f8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53890] TypeArguments: <GestureDetector>
    //     0xbde9fc: ldr             x16, [x16, #0x890]
    // 0xbdea00: ldur            lr, [fp, #-8]
    // 0xbdea04: stp             lr, x16, [SP, #8]
    // 0xbdea08: str             x0, [SP]
    // 0xbdea0c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbdea0c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbdea10: r0 = map()
    //     0xbdea10: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xbdea14: mov             x1, x0
    // 0xbdea18: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdea18: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdea1c: r0 = toList()
    //     0xbdea1c: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xbdea20: stur            x0, [fp, #-8]
    // 0xbdea24: r0 = Row()
    //     0xbdea24: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdea28: r1 = Instance_Axis
    //     0xbdea28: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdea2c: StoreField: r0->field_f = r1
    //     0xbdea2c: stur            w1, [x0, #0xf]
    // 0xbdea30: r1 = Instance_MainAxisAlignment
    //     0xbdea30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdea34: ldr             x1, [x1, #0xa08]
    // 0xbdea38: StoreField: r0->field_13 = r1
    //     0xbdea38: stur            w1, [x0, #0x13]
    // 0xbdea3c: r1 = Instance_MainAxisSize
    //     0xbdea3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdea40: ldr             x1, [x1, #0xa10]
    // 0xbdea44: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdea44: stur            w1, [x0, #0x17]
    // 0xbdea48: r1 = Instance_CrossAxisAlignment
    //     0xbdea48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdea4c: ldr             x1, [x1, #0xa18]
    // 0xbdea50: StoreField: r0->field_1b = r1
    //     0xbdea50: stur            w1, [x0, #0x1b]
    // 0xbdea54: r1 = Instance_VerticalDirection
    //     0xbdea54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdea58: ldr             x1, [x1, #0xa20]
    // 0xbdea5c: StoreField: r0->field_23 = r1
    //     0xbdea5c: stur            w1, [x0, #0x23]
    // 0xbdea60: r1 = Instance_Clip
    //     0xbdea60: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdea64: ldr             x1, [x1, #0x38]
    // 0xbdea68: StoreField: r0->field_2b = r1
    //     0xbdea68: stur            w1, [x0, #0x2b]
    // 0xbdea6c: StoreField: r0->field_2f = rZR
    //     0xbdea6c: stur            xzr, [x0, #0x2f]
    // 0xbdea70: ldur            x1, [fp, #-8]
    // 0xbdea74: StoreField: r0->field_b = r1
    //     0xbdea74: stur            w1, [x0, #0xb]
    // 0xbdea78: LeaveFrame
    //     0xbdea78: mov             SP, fp
    //     0xbdea7c: ldp             fp, lr, [SP], #0x10
    // 0xbdea80: ret
    //     0xbdea80: ret             
    // 0xbdea84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdea84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdea88: b               #0xbde8f4
    // 0xbdea8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdea8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdea90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdea90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, Entity) {
    // ** addr: 0xbdea94, size: 0x320
    // 0xbdea94: EnterFrame
    //     0xbdea94: stp             fp, lr, [SP, #-0x10]!
    //     0xbdea98: mov             fp, SP
    // 0xbdea9c: AllocStack(0x48)
    //     0xbdea9c: sub             SP, SP, #0x48
    // 0xbdeaa0: SetupParameters()
    //     0xbdeaa0: ldr             x0, [fp, #0x18]
    //     0xbdeaa4: ldur            w1, [x0, #0x17]
    //     0xbdeaa8: add             x1, x1, HEAP, lsl #32
    //     0xbdeaac: stur            x1, [fp, #-8]
    // 0xbdeab0: CheckStackOverflow
    //     0xbdeab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdeab4: cmp             SP, x16
    //     0xbdeab8: b.ls            #0xbded84
    // 0xbdeabc: r1 = 1
    //     0xbdeabc: movz            x1, #0x1
    // 0xbdeac0: r0 = AllocateContext()
    //     0xbdeac0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdeac4: mov             x2, x0
    // 0xbdeac8: ldur            x0, [fp, #-8]
    // 0xbdeacc: stur            x2, [fp, #-0x10]
    // 0xbdead0: StoreField: r2->field_b = r0
    //     0xbdead0: stur            w0, [x2, #0xb]
    // 0xbdead4: ldr             x1, [fp, #0x10]
    // 0xbdead8: StoreField: r2->field_f = r1
    //     0xbdead8: stur            w1, [x2, #0xf]
    // 0xbdeadc: LoadField: r1 = r0->field_f
    //     0xbdeadc: ldur            w1, [x0, #0xf]
    // 0xbdeae0: DecompressPointer r1
    //     0xbdeae0: add             x1, x1, HEAP, lsl #32
    // 0xbdeae4: LoadField: r3 = r1->field_f
    //     0xbdeae4: ldur            w3, [x1, #0xf]
    // 0xbdeae8: DecompressPointer r3
    //     0xbdeae8: add             x3, x3, HEAP, lsl #32
    // 0xbdeaec: cmp             w3, NULL
    // 0xbdeaf0: b.eq            #0xbded8c
    // 0xbdeaf4: mov             x1, x3
    // 0xbdeaf8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdeaf8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdeafc: r0 = _of()
    //     0xbdeafc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbdeb00: LoadField: r1 = r0->field_7
    //     0xbdeb00: ldur            w1, [x0, #7]
    // 0xbdeb04: DecompressPointer r1
    //     0xbdeb04: add             x1, x1, HEAP, lsl #32
    // 0xbdeb08: LoadField: d0 = r1->field_7
    //     0xbdeb08: ldur            d0, [x1, #7]
    // 0xbdeb0c: d1 = 4.000000
    //     0xbdeb0c: fmov            d1, #4.00000000
    // 0xbdeb10: fdiv            d2, d0, d1
    // 0xbdeb14: d0 = 16.000000
    //     0xbdeb14: fmov            d0, #16.00000000
    // 0xbdeb18: fsub            d1, d2, d0
    // 0xbdeb1c: ldur            x0, [fp, #-8]
    // 0xbdeb20: stur            d1, [fp, #-0x30]
    // 0xbdeb24: LoadField: r1 = r0->field_f
    //     0xbdeb24: ldur            w1, [x0, #0xf]
    // 0xbdeb28: DecompressPointer r1
    //     0xbdeb28: add             x1, x1, HEAP, lsl #32
    // 0xbdeb2c: ldur            x3, [fp, #-0x10]
    // 0xbdeb30: LoadField: r2 = r3->field_f
    //     0xbdeb30: ldur            w2, [x3, #0xf]
    // 0xbdeb34: DecompressPointer r2
    //     0xbdeb34: add             x2, x2, HEAP, lsl #32
    // 0xbdeb38: LoadField: r4 = r2->field_13
    //     0xbdeb38: ldur            w4, [x2, #0x13]
    // 0xbdeb3c: DecompressPointer r4
    //     0xbdeb3c: add             x4, x4, HEAP, lsl #32
    // 0xbdeb40: mov             x2, x4
    // 0xbdeb44: r0 = _buildImageContainer()
    //     0xbdeb44: bl              #0xbdedb4  ; [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildImageContainer
    // 0xbdeb48: stur            x0, [fp, #-0x18]
    // 0xbdeb4c: r0 = Padding()
    //     0xbdeb4c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdeb50: mov             x1, x0
    // 0xbdeb54: r0 = Instance_EdgeInsets
    //     0xbdeb54: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xbdeb58: ldr             x0, [x0, #0x100]
    // 0xbdeb5c: stur            x1, [fp, #-0x20]
    // 0xbdeb60: StoreField: r1->field_f = r0
    //     0xbdeb60: stur            w0, [x1, #0xf]
    // 0xbdeb64: ldur            x0, [fp, #-0x18]
    // 0xbdeb68: StoreField: r1->field_b = r0
    //     0xbdeb68: stur            w0, [x1, #0xb]
    // 0xbdeb6c: ldur            x2, [fp, #-0x10]
    // 0xbdeb70: LoadField: r0 = r2->field_f
    //     0xbdeb70: ldur            w0, [x2, #0xf]
    // 0xbdeb74: DecompressPointer r0
    //     0xbdeb74: add             x0, x0, HEAP, lsl #32
    // 0xbdeb78: LoadField: r3 = r0->field_7
    //     0xbdeb78: ldur            w3, [x0, #7]
    // 0xbdeb7c: DecompressPointer r3
    //     0xbdeb7c: add             x3, x3, HEAP, lsl #32
    // 0xbdeb80: cmp             w3, NULL
    // 0xbdeb84: b.ne            #0xbdeb90
    // 0xbdeb88: r0 = Null
    //     0xbdeb88: mov             x0, NULL
    // 0xbdeb8c: b               #0xbdeba8
    // 0xbdeb90: r0 = LoadClassIdInstr(r3)
    //     0xbdeb90: ldur            x0, [x3, #-1]
    //     0xbdeb94: ubfx            x0, x0, #0xc, #0x14
    // 0xbdeb98: str             x3, [SP]
    // 0xbdeb9c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbdeb9c: sub             lr, x0, #1, lsl #12
    //     0xbdeba0: ldr             lr, [x21, lr, lsl #3]
    //     0xbdeba4: blr             lr
    // 0xbdeba8: cmp             w0, NULL
    // 0xbdebac: b.ne            #0xbdebb8
    // 0xbdebb0: r2 = ""
    //     0xbdebb0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdebb4: b               #0xbdebbc
    // 0xbdebb8: mov             x2, x0
    // 0xbdebbc: ldur            x1, [fp, #-8]
    // 0xbdebc0: ldur            d0, [fp, #-0x30]
    // 0xbdebc4: ldur            x0, [fp, #-0x20]
    // 0xbdebc8: stur            x2, [fp, #-0x18]
    // 0xbdebcc: LoadField: r3 = r1->field_f
    //     0xbdebcc: ldur            w3, [x1, #0xf]
    // 0xbdebd0: DecompressPointer r3
    //     0xbdebd0: add             x3, x3, HEAP, lsl #32
    // 0xbdebd4: LoadField: r1 = r3->field_23
    //     0xbdebd4: ldur            w1, [x3, #0x23]
    // 0xbdebd8: DecompressPointer r1
    //     0xbdebd8: add             x1, x1, HEAP, lsl #32
    // 0xbdebdc: r16 = Sentinel
    //     0xbdebdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbdebe0: cmp             w1, w16
    // 0xbdebe4: b.eq            #0xbded90
    // 0xbdebe8: stur            x1, [fp, #-8]
    // 0xbdebec: r0 = Text()
    //     0xbdebec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdebf0: mov             x1, x0
    // 0xbdebf4: ldur            x0, [fp, #-0x18]
    // 0xbdebf8: stur            x1, [fp, #-0x28]
    // 0xbdebfc: StoreField: r1->field_b = r0
    //     0xbdebfc: stur            w0, [x1, #0xb]
    // 0xbdec00: ldur            x0, [fp, #-8]
    // 0xbdec04: StoreField: r1->field_13 = r0
    //     0xbdec04: stur            w0, [x1, #0x13]
    // 0xbdec08: r0 = Instance_TextAlign
    //     0xbdec08: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbdec0c: StoreField: r1->field_1b = r0
    //     0xbdec0c: stur            w0, [x1, #0x1b]
    // 0xbdec10: r0 = Instance_TextOverflow
    //     0xbdec10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbdec14: ldr             x0, [x0, #0xe10]
    // 0xbdec18: StoreField: r1->field_2b = r0
    //     0xbdec18: stur            w0, [x1, #0x2b]
    // 0xbdec1c: r2 = 4
    //     0xbdec1c: movz            x2, #0x4
    // 0xbdec20: StoreField: r1->field_37 = r2
    //     0xbdec20: stur            w2, [x1, #0x37]
    // 0xbdec24: r0 = Padding()
    //     0xbdec24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdec28: mov             x3, x0
    // 0xbdec2c: r0 = Instance_EdgeInsets
    //     0xbdec2c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbdec30: ldr             x0, [x0, #0x770]
    // 0xbdec34: stur            x3, [fp, #-8]
    // 0xbdec38: StoreField: r3->field_f = r0
    //     0xbdec38: stur            w0, [x3, #0xf]
    // 0xbdec3c: ldur            x0, [fp, #-0x28]
    // 0xbdec40: StoreField: r3->field_b = r0
    //     0xbdec40: stur            w0, [x3, #0xb]
    // 0xbdec44: r1 = Null
    //     0xbdec44: mov             x1, NULL
    // 0xbdec48: r2 = 4
    //     0xbdec48: movz            x2, #0x4
    // 0xbdec4c: r0 = AllocateArray()
    //     0xbdec4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdec50: mov             x2, x0
    // 0xbdec54: ldur            x0, [fp, #-0x20]
    // 0xbdec58: stur            x2, [fp, #-0x18]
    // 0xbdec5c: StoreField: r2->field_f = r0
    //     0xbdec5c: stur            w0, [x2, #0xf]
    // 0xbdec60: ldur            x0, [fp, #-8]
    // 0xbdec64: StoreField: r2->field_13 = r0
    //     0xbdec64: stur            w0, [x2, #0x13]
    // 0xbdec68: r1 = <Widget>
    //     0xbdec68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdec6c: r0 = AllocateGrowableArray()
    //     0xbdec6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdec70: mov             x1, x0
    // 0xbdec74: ldur            x0, [fp, #-0x18]
    // 0xbdec78: stur            x1, [fp, #-8]
    // 0xbdec7c: StoreField: r1->field_f = r0
    //     0xbdec7c: stur            w0, [x1, #0xf]
    // 0xbdec80: r0 = 4
    //     0xbdec80: movz            x0, #0x4
    // 0xbdec84: StoreField: r1->field_b = r0
    //     0xbdec84: stur            w0, [x1, #0xb]
    // 0xbdec88: r0 = Column()
    //     0xbdec88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdec8c: mov             x1, x0
    // 0xbdec90: r0 = Instance_Axis
    //     0xbdec90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdec94: stur            x1, [fp, #-0x18]
    // 0xbdec98: StoreField: r1->field_f = r0
    //     0xbdec98: stur            w0, [x1, #0xf]
    // 0xbdec9c: r0 = Instance_MainAxisAlignment
    //     0xbdec9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdeca0: ldr             x0, [x0, #0xa08]
    // 0xbdeca4: StoreField: r1->field_13 = r0
    //     0xbdeca4: stur            w0, [x1, #0x13]
    // 0xbdeca8: r0 = Instance_MainAxisSize
    //     0xbdeca8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdecac: ldr             x0, [x0, #0xa10]
    // 0xbdecb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdecb0: stur            w0, [x1, #0x17]
    // 0xbdecb4: r0 = Instance_CrossAxisAlignment
    //     0xbdecb4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdecb8: ldr             x0, [x0, #0xa18]
    // 0xbdecbc: StoreField: r1->field_1b = r0
    //     0xbdecbc: stur            w0, [x1, #0x1b]
    // 0xbdecc0: r0 = Instance_VerticalDirection
    //     0xbdecc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdecc4: ldr             x0, [x0, #0xa20]
    // 0xbdecc8: StoreField: r1->field_23 = r0
    //     0xbdecc8: stur            w0, [x1, #0x23]
    // 0xbdeccc: r0 = Instance_Clip
    //     0xbdeccc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdecd0: ldr             x0, [x0, #0x38]
    // 0xbdecd4: StoreField: r1->field_2b = r0
    //     0xbdecd4: stur            w0, [x1, #0x2b]
    // 0xbdecd8: StoreField: r1->field_2f = rZR
    //     0xbdecd8: stur            xzr, [x1, #0x2f]
    // 0xbdecdc: ldur            x0, [fp, #-8]
    // 0xbdece0: StoreField: r1->field_b = r0
    //     0xbdece0: stur            w0, [x1, #0xb]
    // 0xbdece4: ldur            d0, [fp, #-0x30]
    // 0xbdece8: r0 = inline_Allocate_Double()
    //     0xbdece8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbdecec: add             x0, x0, #0x10
    //     0xbdecf0: cmp             x2, x0
    //     0xbdecf4: b.ls            #0xbded9c
    //     0xbdecf8: str             x0, [THR, #0x50]  ; THR::top
    //     0xbdecfc: sub             x0, x0, #0xf
    //     0xbded00: movz            x2, #0xe15c
    //     0xbded04: movk            x2, #0x3, lsl #16
    //     0xbded08: stur            x2, [x0, #-1]
    // 0xbded0c: StoreField: r0->field_7 = d0
    //     0xbded0c: stur            d0, [x0, #7]
    // 0xbded10: stur            x0, [fp, #-8]
    // 0xbded14: r0 = Container()
    //     0xbded14: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbded18: stur            x0, [fp, #-0x20]
    // 0xbded1c: ldur            x16, [fp, #-8]
    // 0xbded20: r30 = Instance_EdgeInsets
    //     0xbded20: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xbded24: ldr             lr, [lr, #0xa68]
    // 0xbded28: stp             lr, x16, [SP, #8]
    // 0xbded2c: ldur            x16, [fp, #-0x18]
    // 0xbded30: str             x16, [SP]
    // 0xbded34: mov             x1, x0
    // 0xbded38: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xbded38: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xbded3c: ldr             x4, [x4, #0x628]
    // 0xbded40: r0 = Container()
    //     0xbded40: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbded44: r0 = GestureDetector()
    //     0xbded44: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbded48: ldur            x2, [fp, #-0x10]
    // 0xbded4c: r1 = Function '<anonymous closure>':.
    //     0xbded4c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53898] AnonymousClosure: (0xbdeee8), in [package:customer_app/app/presentation/views/line/home/<USER>/category_group_carousel_item_view.dart] _CategoryGroupCarouselItemViewState::_buildCarouselPage (0xbde8d4)
    //     0xbded50: ldr             x1, [x1, #0x898]
    // 0xbded54: stur            x0, [fp, #-8]
    // 0xbded58: r0 = AllocateClosure()
    //     0xbded58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbded5c: ldur            x16, [fp, #-0x20]
    // 0xbded60: stp             x16, x0, [SP]
    // 0xbded64: ldur            x1, [fp, #-8]
    // 0xbded68: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbded68: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbded6c: ldr             x4, [x4, #0xaf0]
    // 0xbded70: r0 = GestureDetector()
    //     0xbded70: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbded74: ldur            x0, [fp, #-8]
    // 0xbded78: LeaveFrame
    //     0xbded78: mov             SP, fp
    //     0xbded7c: ldp             fp, lr, [SP], #0x10
    // 0xbded80: ret
    //     0xbded80: ret             
    // 0xbded84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbded84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbded88: b               #0xbdeabc
    // 0xbded8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbded8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbded90: r9 = _titleStyle
    //     0xbded90: add             x9, PP, #0x53, lsl #12  ; [pp+0x538a0] Field <_CategoryGroupCarouselItemViewState@1703444197._titleStyle@1703444197>: late (offset: 0x24)
    //     0xbded94: ldr             x9, [x9, #0x8a0]
    // 0xbded98: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xbded98: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xbded9c: SaveReg d0
    //     0xbded9c: str             q0, [SP, #-0x10]!
    // 0xbdeda0: SaveReg r1
    //     0xbdeda0: str             x1, [SP, #-8]!
    // 0xbdeda4: r0 = AllocateDouble()
    //     0xbdeda4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbdeda8: RestoreReg r1
    //     0xbdeda8: ldr             x1, [SP], #8
    // 0xbdedac: RestoreReg d0
    //     0xbdedac: ldr             q0, [SP], #0x10
    // 0xbdedb0: b               #0xbded0c
  }
  _ _buildImageContainer(/* No info */) {
    // ** addr: 0xbdedb4, size: 0x134
    // 0xbdedb4: EnterFrame
    //     0xbdedb4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdedb8: mov             fp, SP
    // 0xbdedbc: AllocStack(0x30)
    //     0xbdedbc: sub             SP, SP, #0x30
    // 0xbdedc0: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbdedc0: stur            x2, [fp, #-0x10]
    // 0xbdedc4: CheckStackOverflow
    //     0xbdedc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdedc8: cmp             SP, x16
    //     0xbdedcc: b.ls            #0xbdeec8
    // 0xbdedd0: cmp             w2, NULL
    // 0xbdedd4: b.eq            #0xbdede0
    // 0xbdedd8: LoadField: r0 = r2->field_7
    //     0xbdedd8: ldur            w0, [x2, #7]
    // 0xbdeddc: cbnz            w0, #0xbdee3c
    // 0xbdede0: LoadField: r0 = r1->field_1f
    //     0xbdede0: ldur            w0, [x1, #0x1f]
    // 0xbdede4: DecompressPointer r0
    //     0xbdede4: add             x0, x0, HEAP, lsl #32
    // 0xbdede8: r16 = Sentinel
    //     0xbdede8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbdedec: cmp             w0, w16
    // 0xbdedf0: b.eq            #0xbdeed0
    // 0xbdedf4: r0 = Container()
    //     0xbdedf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdedf8: stur            x0, [fp, #-8]
    // 0xbdedfc: r16 = 84.000000
    //     0xbdedfc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xbdee00: ldr             x16, [x16, #0xf90]
    // 0xbdee04: r30 = 84.000000
    //     0xbdee04: add             lr, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xbdee08: ldr             lr, [lr, #0xf90]
    // 0xbdee0c: stp             lr, x16, [SP, #8]
    // 0xbdee10: r16 = Instance_BoxDecoration
    //     0xbdee10: add             x16, PP, #0x53, lsl #12  ; [pp+0x538b8] Obj!BoxDecoration@d649b1
    //     0xbdee14: ldr             x16, [x16, #0x8b8]
    // 0xbdee18: str             x16, [SP]
    // 0xbdee1c: mov             x1, x0
    // 0xbdee20: r4 = const [0, 0x4, 0x3, 0x1, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xbdee20: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3f468] List(11) [0, 0x4, 0x3, 0x1, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xbdee24: ldr             x4, [x4, #0x468]
    // 0xbdee28: r0 = Container()
    //     0xbdee28: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdee2c: ldur            x0, [fp, #-8]
    // 0xbdee30: LeaveFrame
    //     0xbdee30: mov             SP, fp
    //     0xbdee34: ldp             fp, lr, [SP], #0x10
    // 0xbdee38: ret
    //     0xbdee38: ret             
    // 0xbdee3c: LoadField: r0 = r1->field_1f
    //     0xbdee3c: ldur            w0, [x1, #0x1f]
    // 0xbdee40: DecompressPointer r0
    //     0xbdee40: add             x0, x0, HEAP, lsl #32
    // 0xbdee44: r16 = Sentinel
    //     0xbdee44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbdee48: cmp             w0, w16
    // 0xbdee4c: b.eq            #0xbdeedc
    // 0xbdee50: r0 = ImageHeaders.forImages()
    //     0xbdee50: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdee54: r1 = <CachedNetworkImageProvider>
    //     0xbdee54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb10] TypeArguments: <CachedNetworkImageProvider>
    //     0xbdee58: ldr             x1, [x1, #0xb10]
    // 0xbdee5c: stur            x0, [fp, #-8]
    // 0xbdee60: r0 = CachedNetworkImageProvider()
    //     0xbdee60: bl              #0x859df8  ; AllocateCachedNetworkImageProviderStub -> CachedNetworkImageProvider (size=0x34)
    // 0xbdee64: mov             x1, x0
    // 0xbdee68: ldur            x0, [fp, #-0x10]
    // 0xbdee6c: stur            x1, [fp, #-0x18]
    // 0xbdee70: StoreField: r1->field_f = r0
    //     0xbdee70: stur            w0, [x1, #0xf]
    // 0xbdee74: r0 = 336
    //     0xbdee74: movz            x0, #0x150
    // 0xbdee78: StoreField: r1->field_27 = r0
    //     0xbdee78: stur            w0, [x1, #0x27]
    // 0xbdee7c: StoreField: r1->field_2b = r0
    //     0xbdee7c: stur            w0, [x1, #0x2b]
    // 0xbdee80: d0 = 1.000000
    //     0xbdee80: fmov            d0, #1.00000000
    // 0xbdee84: ArrayStore: r1[0] = d0  ; List_8
    //     0xbdee84: stur            d0, [x1, #0x17]
    // 0xbdee88: ldur            x0, [fp, #-8]
    // 0xbdee8c: StoreField: r1->field_23 = r0
    //     0xbdee8c: stur            w0, [x1, #0x23]
    // 0xbdee90: r0 = Instance_ImageRenderMethodForWeb
    //     0xbdee90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb18] Obj!ImageRenderMethodForWeb@d75981
    //     0xbdee94: ldr             x0, [x0, #0xb18]
    // 0xbdee98: StoreField: r1->field_2f = r0
    //     0xbdee98: stur            w0, [x1, #0x2f]
    // 0xbdee9c: r0 = CircleAvatar()
    //     0xbdee9c: bl              #0xa43c2c  ; AllocateCircleAvatarStub -> CircleAvatar (size=0x38)
    // 0xbdeea0: r1 = Instance_Color
    //     0xbdeea0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xbdeea4: ldr             x1, [x1, #0xf88]
    // 0xbdeea8: StoreField: r0->field_f = r1
    //     0xbdeea8: stur            w1, [x0, #0xf]
    // 0xbdeeac: ldur            x1, [fp, #-0x18]
    // 0xbdeeb0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdeeb0: stur            w1, [x0, #0x17]
    // 0xbdeeb4: d0 = 42.000000
    //     0xbdeeb4: ldr             d0, [PP, #0x5ae0]  ; [pp+0x5ae0] IMM: double(42) from 0x4045000000000000
    // 0xbdeeb8: StoreField: r0->field_27 = d0
    //     0xbdeeb8: stur            d0, [x0, #0x27]
    // 0xbdeebc: LeaveFrame
    //     0xbdeebc: mov             SP, fp
    //     0xbdeec0: ldp             fp, lr, [SP], #0x10
    // 0xbdeec4: ret
    //     0xbdeec4: ret             
    // 0xbdeec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdeec8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdeecc: b               #0xbdedd0
    // 0xbdeed0: r9 = _imageSize
    //     0xbdeed0: add             x9, PP, #0x53, lsl #12  ; [pp+0x538c0] Field <_CategoryGroupCarouselItemViewState@1703444197._imageSize@1703444197>: late (offset: 0x20)
    //     0xbdeed4: ldr             x9, [x9, #0x8c0]
    // 0xbdeed8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbdeed8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbdeedc: r9 = _imageSize
    //     0xbdeedc: add             x9, PP, #0x53, lsl #12  ; [pp+0x538c0] Field <_CategoryGroupCarouselItemViewState@1703444197._imageSize@1703444197>: late (offset: 0x20)
    //     0xbdeee0: ldr             x9, [x9, #0x8c0]
    // 0xbdeee4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbdeee4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbdeee8, size: 0xd0
    // 0xbdeee8: EnterFrame
    //     0xbdeee8: stp             fp, lr, [SP, #-0x10]!
    //     0xbdeeec: mov             fp, SP
    // 0xbdeef0: AllocStack(0x40)
    //     0xbdeef0: sub             SP, SP, #0x40
    // 0xbdeef4: SetupParameters()
    //     0xbdeef4: ldr             x0, [fp, #0x10]
    //     0xbdeef8: ldur            w1, [x0, #0x17]
    //     0xbdeefc: add             x1, x1, HEAP, lsl #32
    // 0xbdef00: CheckStackOverflow
    //     0xbdef00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdef04: cmp             SP, x16
    //     0xbdef08: b.ls            #0xbdefac
    // 0xbdef0c: LoadField: r0 = r1->field_b
    //     0xbdef0c: ldur            w0, [x1, #0xb]
    // 0xbdef10: DecompressPointer r0
    //     0xbdef10: add             x0, x0, HEAP, lsl #32
    // 0xbdef14: LoadField: r2 = r0->field_f
    //     0xbdef14: ldur            w2, [x0, #0xf]
    // 0xbdef18: DecompressPointer r2
    //     0xbdef18: add             x2, x2, HEAP, lsl #32
    // 0xbdef1c: LoadField: r0 = r2->field_b
    //     0xbdef1c: ldur            w0, [x2, #0xb]
    // 0xbdef20: DecompressPointer r0
    //     0xbdef20: add             x0, x0, HEAP, lsl #32
    // 0xbdef24: cmp             w0, NULL
    // 0xbdef28: b.eq            #0xbdefb4
    // 0xbdef2c: LoadField: r2 = r0->field_13
    //     0xbdef2c: ldur            w2, [x0, #0x13]
    // 0xbdef30: DecompressPointer r2
    //     0xbdef30: add             x2, x2, HEAP, lsl #32
    // 0xbdef34: LoadField: r3 = r0->field_f
    //     0xbdef34: ldur            w3, [x0, #0xf]
    // 0xbdef38: DecompressPointer r3
    //     0xbdef38: add             x3, x3, HEAP, lsl #32
    // 0xbdef3c: LoadField: r4 = r0->field_23
    //     0xbdef3c: ldur            w4, [x0, #0x23]
    // 0xbdef40: DecompressPointer r4
    //     0xbdef40: add             x4, x4, HEAP, lsl #32
    // 0xbdef44: LoadField: r5 = r0->field_1b
    //     0xbdef44: ldur            w5, [x0, #0x1b]
    // 0xbdef48: DecompressPointer r5
    //     0xbdef48: add             x5, x5, HEAP, lsl #32
    // 0xbdef4c: ArrayLoad: r6 = r0[0]  ; List_4
    //     0xbdef4c: ldur            w6, [x0, #0x17]
    // 0xbdef50: DecompressPointer r6
    //     0xbdef50: add             x6, x6, HEAP, lsl #32
    // 0xbdef54: LoadField: r7 = r0->field_27
    //     0xbdef54: ldur            w7, [x0, #0x27]
    // 0xbdef58: DecompressPointer r7
    //     0xbdef58: add             x7, x7, HEAP, lsl #32
    // 0xbdef5c: LoadField: r8 = r1->field_f
    //     0xbdef5c: ldur            w8, [x1, #0xf]
    // 0xbdef60: DecompressPointer r8
    //     0xbdef60: add             x8, x8, HEAP, lsl #32
    // 0xbdef64: ArrayLoad: r1 = r8[0]  ; List_4
    //     0xbdef64: ldur            w1, [x8, #0x17]
    // 0xbdef68: DecompressPointer r1
    //     0xbdef68: add             x1, x1, HEAP, lsl #32
    // 0xbdef6c: LoadField: r8 = r0->field_1f
    //     0xbdef6c: ldur            w8, [x0, #0x1f]
    // 0xbdef70: DecompressPointer r8
    //     0xbdef70: add             x8, x8, HEAP, lsl #32
    // 0xbdef74: stp             x2, x8, [SP, #0x30]
    // 0xbdef78: stp             x4, x3, [SP, #0x20]
    // 0xbdef7c: stp             x6, x5, [SP, #0x10]
    // 0xbdef80: stp             x1, x7, [SP]
    // 0xbdef84: r4 = 0
    //     0xbdef84: movz            x4, #0
    // 0xbdef88: ldr             x0, [SP, #0x38]
    // 0xbdef8c: r16 = UnlinkedCall_0x613b5c
    //     0xbdef8c: add             x16, PP, #0x53, lsl #12  ; [pp+0x538a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdef90: add             x16, x16, #0x8a8
    // 0xbdef94: ldp             x5, lr, [x16]
    // 0xbdef98: blr             lr
    // 0xbdef9c: r0 = Null
    //     0xbdef9c: mov             x0, NULL
    // 0xbdefa0: LeaveFrame
    //     0xbdefa0: mov             SP, fp
    //     0xbdefa4: ldp             fp, lr, [SP], #0x10
    // 0xbdefa8: ret
    //     0xbdefa8: ret             
    // 0xbdefac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdefac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdefb0: b               #0xbdef0c
    // 0xbdefb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdefb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbdefb8, size: 0x84
    // 0xbdefb8: EnterFrame
    //     0xbdefb8: stp             fp, lr, [SP, #-0x10]!
    //     0xbdefbc: mov             fp, SP
    // 0xbdefc0: AllocStack(0x10)
    //     0xbdefc0: sub             SP, SP, #0x10
    // 0xbdefc4: SetupParameters()
    //     0xbdefc4: ldr             x0, [fp, #0x18]
    //     0xbdefc8: ldur            w1, [x0, #0x17]
    //     0xbdefcc: add             x1, x1, HEAP, lsl #32
    //     0xbdefd0: stur            x1, [fp, #-8]
    // 0xbdefd4: CheckStackOverflow
    //     0xbdefd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdefd8: cmp             SP, x16
    //     0xbdefdc: b.ls            #0xbdf034
    // 0xbdefe0: r1 = 1
    //     0xbdefe0: movz            x1, #0x1
    // 0xbdefe4: r0 = AllocateContext()
    //     0xbdefe4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdefe8: mov             x1, x0
    // 0xbdefec: ldur            x0, [fp, #-8]
    // 0xbdeff0: StoreField: r1->field_b = r0
    //     0xbdeff0: stur            w0, [x1, #0xb]
    // 0xbdeff4: ldr             x2, [fp, #0x10]
    // 0xbdeff8: StoreField: r1->field_f = r2
    //     0xbdeff8: stur            w2, [x1, #0xf]
    // 0xbdeffc: LoadField: r3 = r0->field_f
    //     0xbdeffc: ldur            w3, [x0, #0xf]
    // 0xbdf000: DecompressPointer r3
    //     0xbdf000: add             x3, x3, HEAP, lsl #32
    // 0xbdf004: mov             x2, x1
    // 0xbdf008: stur            x3, [fp, #-0x10]
    // 0xbdf00c: r1 = Function '<anonymous closure>':.
    //     0xbdf00c: add             x1, PP, #0x53, lsl #12  ; [pp+0x538c8] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xbdf010: ldr             x1, [x1, #0x8c8]
    // 0xbdf014: r0 = AllocateClosure()
    //     0xbdf014: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdf018: ldur            x1, [fp, #-0x10]
    // 0xbdf01c: mov             x2, x0
    // 0xbdf020: r0 = setState()
    //     0xbdf020: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbdf024: r0 = Null
    //     0xbdf024: mov             x0, NULL
    // 0xbdf028: LeaveFrame
    //     0xbdf028: mov             SP, fp
    //     0xbdf02c: ldp             fp, lr, [SP], #0x10
    // 0xbdf030: ret
    //     0xbdf030: ret             
    // 0xbdf034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdf034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdf038: b               #0xbdefe0
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0xc711f8, size: 0x134
    // 0xc711f8: EnterFrame
    //     0xc711f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc711fc: mov             fp, SP
    // 0xc71200: AllocStack(0x18)
    //     0xc71200: sub             SP, SP, #0x18
    // 0xc71204: SetupParameters(_CategoryGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc71204: mov             x0, x1
    //     0xc71208: stur            x1, [fp, #-8]
    // 0xc7120c: CheckStackOverflow
    //     0xc7120c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc71210: cmp             SP, x16
    //     0xc71214: b.ls            #0xc7130c
    // 0xc71218: LoadField: r1 = r0->field_f
    //     0xc71218: ldur            w1, [x0, #0xf]
    // 0xc7121c: DecompressPointer r1
    //     0xc7121c: add             x1, x1, HEAP, lsl #32
    // 0xc71220: cmp             w1, NULL
    // 0xc71224: b.eq            #0xc71314
    // 0xc71228: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc71228: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc7122c: r0 = _of()
    //     0xc7122c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc71230: LoadField: r1 = r0->field_7
    //     0xc71230: ldur            w1, [x0, #7]
    // 0xc71234: DecompressPointer r1
    //     0xc71234: add             x1, x1, HEAP, lsl #32
    // 0xc71238: LoadField: d0 = r1->field_f
    //     0xc71238: ldur            d0, [x1, #0xf]
    // 0xc7123c: d1 = 0.200000
    //     0xc7123c: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xc71240: fmul            d2, d0, d1
    // 0xc71244: r0 = inline_Allocate_Double()
    //     0xc71244: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc71248: add             x0, x0, #0x10
    //     0xc7124c: cmp             x1, x0
    //     0xc71250: b.ls            #0xc71318
    //     0xc71254: str             x0, [THR, #0x50]  ; THR::top
    //     0xc71258: sub             x0, x0, #0xf
    //     0xc7125c: movz            x1, #0xe15c
    //     0xc71260: movk            x1, #0x3, lsl #16
    //     0xc71264: stur            x1, [x0, #-1]
    // 0xc71268: StoreField: r0->field_7 = d2
    //     0xc71268: stur            d2, [x0, #7]
    // 0xc7126c: ldur            x2, [fp, #-8]
    // 0xc71270: StoreField: r2->field_27 = r0
    //     0xc71270: stur            w0, [x2, #0x27]
    //     0xc71274: ldurb           w16, [x2, #-1]
    //     0xc71278: ldurb           w17, [x0, #-1]
    //     0xc7127c: and             x16, x17, x16, lsr #2
    //     0xc71280: tst             x16, HEAP, lsr #32
    //     0xc71284: b.eq            #0xc7128c
    //     0xc71288: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc7128c: r0 = 84.000000
    //     0xc7128c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0xc71290: ldr             x0, [x0, #0xf90]
    // 0xc71294: StoreField: r2->field_1f = r0
    //     0xc71294: stur            w0, [x2, #0x1f]
    // 0xc71298: LoadField: r1 = r2->field_f
    //     0xc71298: ldur            w1, [x2, #0xf]
    // 0xc7129c: DecompressPointer r1
    //     0xc7129c: add             x1, x1, HEAP, lsl #32
    // 0xc712a0: cmp             w1, NULL
    // 0xc712a4: b.eq            #0xc71328
    // 0xc712a8: r0 = of()
    //     0xc712a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc712ac: LoadField: r1 = r0->field_87
    //     0xc712ac: ldur            w1, [x0, #0x87]
    // 0xc712b0: DecompressPointer r1
    //     0xc712b0: add             x1, x1, HEAP, lsl #32
    // 0xc712b4: LoadField: r0 = r1->field_2b
    //     0xc712b4: ldur            w0, [x1, #0x2b]
    // 0xc712b8: DecompressPointer r0
    //     0xc712b8: add             x0, x0, HEAP, lsl #32
    // 0xc712bc: r16 = 12.000000
    //     0xc712bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc712c0: ldr             x16, [x16, #0x9e8]
    // 0xc712c4: r30 = Instance_Color
    //     0xc712c4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc712c8: stp             lr, x16, [SP]
    // 0xc712cc: mov             x1, x0
    // 0xc712d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc712d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc712d4: ldr             x4, [x4, #0xaa0]
    // 0xc712d8: r0 = copyWith()
    //     0xc712d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc712dc: ldur            x1, [fp, #-8]
    // 0xc712e0: StoreField: r1->field_23 = r0
    //     0xc712e0: stur            w0, [x1, #0x23]
    //     0xc712e4: ldurb           w16, [x1, #-1]
    //     0xc712e8: ldurb           w17, [x0, #-1]
    //     0xc712ec: and             x16, x17, x16, lsr #2
    //     0xc712f0: tst             x16, HEAP, lsr #32
    //     0xc712f4: b.eq            #0xc712fc
    //     0xc712f8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc712fc: r0 = Null
    //     0xc712fc: mov             x0, NULL
    // 0xc71300: LeaveFrame
    //     0xc71300: mov             SP, fp
    //     0xc71304: ldp             fp, lr, [SP], #0x10
    // 0xc71308: ret
    //     0xc71308: ret             
    // 0xc7130c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7130c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc71310: b               #0xc71218
    // 0xc71314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc71314: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc71318: SaveReg d2
    //     0xc71318: str             q2, [SP, #-0x10]!
    // 0xc7131c: r0 = AllocateDouble()
    //     0xc7131c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc71320: RestoreReg d2
    //     0xc71320: ldr             q2, [SP], #0x10
    // 0xc71324: b               #0xc71268
    // 0xc71328: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc71328: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88144, size: 0x54
    // 0xc88144: EnterFrame
    //     0xc88144: stp             fp, lr, [SP, #-0x10]!
    //     0xc88148: mov             fp, SP
    // 0xc8814c: CheckStackOverflow
    //     0xc8814c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88150: cmp             SP, x16
    //     0xc88154: b.ls            #0xc88184
    // 0xc88158: LoadField: r0 = r1->field_13
    //     0xc88158: ldur            w0, [x1, #0x13]
    // 0xc8815c: DecompressPointer r0
    //     0xc8815c: add             x0, x0, HEAP, lsl #32
    // 0xc88160: r16 = Sentinel
    //     0xc88160: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88164: cmp             w0, w16
    // 0xc88168: b.eq            #0xc8818c
    // 0xc8816c: mov             x1, x0
    // 0xc88170: r0 = dispose()
    //     0xc88170: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc88174: r0 = Null
    //     0xc88174: mov             x0, NULL
    // 0xc88178: LeaveFrame
    //     0xc88178: mov             SP, fp
    //     0xc8817c: ldp             fp, lr, [SP], #0x10
    // 0xc88180: ret
    //     0xc88180: ret             
    // 0xc88184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88184: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88188: b               #0xc88158
    // 0xc8818c: r9 = _pageLineController
    //     0xc8818c: add             x9, PP, #0x53, lsl #12  ; [pp+0x53880] Field <_CategoryGroupCarouselItemViewState@1703444197._pageLineController@1703444197>: late (offset: 0x14)
    //     0xc88190: ldr             x9, [x9, #0x880]
    // 0xc88194: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88194: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3996, size: 0x2c, field offset: 0xc
//   const constructor, 
class CategoryGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc809bc, size: 0x3c
    // 0xc809bc: EnterFrame
    //     0xc809bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc809c0: mov             fp, SP
    // 0xc809c4: mov             x0, x1
    // 0xc809c8: r1 = <CategoryGroupCarouselItemView>
    //     0xc809c8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48428] TypeArguments: <CategoryGroupCarouselItemView>
    //     0xc809cc: ldr             x1, [x1, #0x428]
    // 0xc809d0: r0 = _CategoryGroupCarouselItemViewState()
    //     0xc809d0: bl              #0xc809f8  ; Allocate_CategoryGroupCarouselItemViewStateStub -> _CategoryGroupCarouselItemViewState (size=0x2c)
    // 0xc809d4: r1 = Sentinel
    //     0xc809d4: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc809d8: StoreField: r0->field_13 = r1
    //     0xc809d8: stur            w1, [x0, #0x13]
    // 0xc809dc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc809dc: stur            xzr, [x0, #0x17]
    // 0xc809e0: StoreField: r0->field_1f = r1
    //     0xc809e0: stur            w1, [x0, #0x1f]
    // 0xc809e4: StoreField: r0->field_23 = r1
    //     0xc809e4: stur            w1, [x0, #0x23]
    // 0xc809e8: StoreField: r0->field_27 = r1
    //     0xc809e8: stur            w1, [x0, #0x27]
    // 0xc809ec: LeaveFrame
    //     0xc809ec: mov             SP, fp
    //     0xc809f0: ldp             fp, lr, [SP], #0x10
    // 0xc809f4: ret
    //     0xc809f4: ret             
  }
}
